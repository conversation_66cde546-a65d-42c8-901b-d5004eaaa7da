import {ZoneIF} from "./ZoneIF";
import {ZoneContentHandlerIF} from "./api/ZoneContentHandlerIF";
import {ZoneFlowHandlerIF} from "./api/ZoneFlowHandlerIF";
import {ZoneStateHandlerIF} from "./api/ZoneStateHandlerIF";
import {RenderingDataZone, ZoneContent, ZoneContentType} from "../../util/CommonTypes";
import {Page} from "../sections/Page";
import {EditActionIF} from "../../containers/left/actions/EditActionIF";
import {ContentIF} from "../../content-mgmt/content/ContentIF";
import {ZoneComponentIF} from "./api/ZoneComponentIF";
import {ZoneStateIF} from "./zone-state/ZoneStateIF";
import {ZoneRuntimePropertiesManager} from "./ZoneRuntimePropertiesManager";
import {GlobalChangeNotifier} from "../change/GlobalChangeNotifier";
import {ContentFactory} from "../../content-mgmt/content-factories/ContentFactory";
import {ArrayUtil} from "../../../shared/util/ArrayUtil";
import {JQueryUtil} from "../../util/JQueryUtil";
import {ZoneUtils} from "./ZoneUtils";
import {ZoneComponentTypeEnum} from "./base/ZoneComponentTypeEnum";
import {CommonZoneHelper} from "./CommonZoneHelper";
import {CommonZoneIF} from "./CommonZoneIF";
import {UpdateBindingContext} from "./UpdateBindingContext";
import {ObjectUtil} from "../../../shared/util/ObjectUtil";

export abstract class AbstractZone implements ZoneIF {

    private component: ZoneComponentIF;
    private contentHandler: ZoneContentHandlerIF;
    private flowHandler: ZoneFlowHandlerIF;
    private stateHandler: ZoneStateHandlerIF;
    private temporaryZone : boolean = false;
    protected commonZoneHelper: CommonZoneHelper = new CommonZoneHelper(this);

    constructor() {

    }

    public getComponentType(): ZoneComponentTypeEnum {
        return this.component.getComponentType();
    }

    public setComponent(component: ZoneComponentIF): void {
        this.component = component;
    }

    public setContentHandler(contentHandler: ZoneContentHandlerIF): void {
        this.contentHandler = contentHandler;
    }

    public setFlowHandler(flowHandler: ZoneFlowHandlerIF): void {
        this.flowHandler = flowHandler;
    }

    public setStateHandler(stateHandler: ZoneStateHandlerIF): void {
        this.stateHandler = stateHandler;
    }

    afterRender(): void {
        try {
            this.component.afterRender();
        } finally {
            if (!this.isTemporaryZone()) {
                GlobalChangeNotifier.getInstance().addZone(this);
            }
        }
    }

    getCloneZoneData(): RenderingDataZone {
        return this.component.getCloneZoneData();
    }

    getComponentId(): string {
        return this.component.getComponentId();
    }

    getId(): number {
        return this.component.getId();
    }

    getUiTemplate(): string {
        return this.component.getUiTemplate();
    }

    getZoneData(): RenderingDataZone {
        return this.component.getZoneData();
    }

    isConnectedZone(): boolean {
        return this.component.isConnectedZone();
    }

    isPageBodyZone(): boolean {
        return this.component.isPageBodyZone();
    }

    isSmartTextZone(): boolean {
        return this.component.isSmartTextZone();
    }

    getJqueryElement(): JQuery {
        return this.component.getJqueryElement();
    }

    getMinimumDistance(): number {
        return this.component.getMinimumDistance();
    }

    horizontalEnd(): number {
        return this.component.horizontalEnd();
    }

    horizontalStart(): number {
        return this.component.horizontalStart();
    }

    isFooterZone(): boolean {
        return this.component.isFooterZone();
    }

    isHeaderZone(): boolean {
        return this.component.isHeaderZone();
    }

    setMinimumDistance(minimumDistance: number): void {
        this.component.setMinimumDistance(minimumDistance)
    }

    setVerticalStart(verticalStart: number): void {
        this.component.setVerticalStart(verticalStart);
    }

    verticalEnd(): number {
        return this.component.verticalEnd();
    }

    verticalStart(): number {
        return this.component.verticalStart();
    }

    exceedsOriginalHeight(): boolean {
        return this.flowHandler.exceedsOriginalHeight();
    }

    getTotalHeight(): number {
        return this.flowHandler.getTotalHeight();
    }

    fitContent(): void {
        this.component.fitContent();
    }

    getOriginalHeight(): number {
        return this.component.getOriginalHeight();
    }

    height(): number {
        return this.component.height();
    }

    width(): number {
        return this.component.width();
    }

    innerHeight(): number {
        return this.component.innerHeight();
    }

    resetToOriginalHeight(): void {
        this.component.resetToOriginalHeight();
    }

    setMinHeightToOriginalHeight(): void {
        this.component.setMinHeightToOriginalHeight();
    }

    setHeight(newHeight: number): void {
        this.component.setHeight(newHeight);
    }

    setOriginalHeight(height: number): void {
        this.component.setOriginalHeight(height);
    }

    getHeightExceedingEndOfPage(): number {
        return this.component.getHeightExceedingEndOfPage();
    }

    getOverflowData(): RenderingDataZone {
        return this.component.getOverflowData();
    }

    getPage(): Page {
        return this.component.getPage();
    }

    partiallyExceedsEndOfPage(): boolean {
        return this.component.partiallyExceedsEndOfPage();
    }

    removeHeightExceedingEndOfPage(): void {
        this.component.removeHeightExceedingEndOfPage();
    }

    setPage(page: Page): void {
        this.component.setPage(page);
    }

    totallyExceedsEndOfPage(): boolean {
        return this.component.totallyExceedsEndOfPage();
    }

    isHandleContentChangeNotifications(): boolean {
        let zoneComponentId = this.getComponentId();
        if (ObjectUtil.isNullOrUndefined(zoneComponentId) || zoneComponentId.length === 0) {
            return false;
        }
        return ZoneRuntimePropertiesManager.getInstance().isHandleContentChangeNotifications(zoneComponentId);
    }

    setHandleContentChangeNotifications(handleContentChangeNotifications: boolean) {
        let zoneComponentId = this.getComponentId();
        if (ObjectUtil.isNullOrUndefined(zoneComponentId) || zoneComponentId.length === 0) {
            return;
        }

        if (handleContentChangeNotifications) {
            GlobalChangeNotifier.getInstance().reconnectZoneWithComponentId(zoneComponentId);
        } else {
            GlobalChangeNotifier.getInstance().disconnectZoneWithComponentId(zoneComponentId);
        }

        ZoneRuntimePropertiesManager.getInstance().setHandleContentChangeNotifications(zoneComponentId, handleContentChangeNotifications);
    }

    getActions(): EditActionIF[] {
        return this.contentHandler.getActions();
    }

    getContent(): ContentIF {
        return this.contentHandler.getContent();
    }

    clearZoneContent(): void {
        this.contentHandler.clearZoneContent();
    }

    hasChildContent(contentId: string): boolean {
        return this.contentHandler.hasChildContent(contentId);
    }

    hasContent(): boolean {
        return this.contentHandler.hasContent();
    }

    hasOverflowContent(): boolean {
        return this.contentHandler.hasOverflowContent();
    }

    hasPersistedContent(): boolean {
        return this.contentHandler.hasPersistedContent();
    }

    isBindable(): boolean {
        return this.contentHandler.isBindable();
    }

    moveOverflowContentToNextBlock(): void {
        this.contentHandler.moveOverflowContentToNextBlock();
    }

    replaceJqueryContent(element: JQuery): void {
        this.contentHandler.replaceJqueryContent(element);
    }

    setJqueryContent(element: JQuery<HTMLElement | Text | Comment | Document>): void {
        this.contentHandler.setJqueryContent(element);
    }

    setZoneContent(zoneContent: ZoneContent): void {
        this.contentHandler.setZoneContent(zoneContent);
    }

    getFirstEditableContent(): ContentIF {
        return this.contentHandler.getFirstEditableContent();
    }

    addAffectedByZone(zone: ZoneIF): void {
        this.flowHandler.addAffectedByZone(zone);
    }

    addAffectedZone(zone: ZoneIF): void {
        this.flowHandler.addAffectedZone(zone);
    }

    affectsZone(zone: ZoneIF): boolean {
        return this.flowHandler.affectsZone(zone);
    }

    clearAffectedZones(): void {
        this.flowHandler.clearAffectedZones();
    }

    getFlowAffectedByZones(): ZoneIF[] {
        return this.flowHandler.getFlowAffectedByZones();
    }

    getFlowAffectedByZonesOnPreviousPage(): ZoneIF[] {
        return this.flowHandler.getFlowAffectedByZonesOnPreviousPage();
    }

    getFlowAffectedByZonesOnSamePage(): ZoneIF[] {
        return this.flowHandler.getFlowAffectedByZonesOnSamePage();
    }

    getFlowAffectedZones(): ZoneIF[] {
        return this.flowHandler.getFlowAffectedZones();
    }

    getFlowAffectedZonesOnSamePage(): ZoneIF[] {
        return this.flowHandler.getFlowAffectedZonesOnSamePage();
    }

    getFlowAffectedZonesOnNextPage(): ZoneIF[] {
        return this.flowHandler.getFlowAffectedZonesOnNextPage();
    }

    getHeadZoneBlock(): CommonZoneIF {
        return this.flowHandler.getHeadZoneBlock();
    }

    getLastZoneBlock(): CommonZoneIF {
        return this.flowHandler.getLastZoneBlock();
    }

    getNearestTopZone(): ZoneIF {
        return this.flowHandler.getNearestTopZone();
    }

    getNextZoneWithSameId(): CommonZoneIF {
        return this.flowHandler.getNextZoneWithSameId();
    }

    getAllZonesWithSameId() : CommonZoneIF[] {
        return this.flowHandler.getAllZonesWithSameId();
    }

    getPreviousZoneWithSameId(): CommonZoneIF {
        return this.flowHandler.getPreviousZoneWithSameId();
    }

    getFirstZoneWithSameId() : CommonZoneIF {
        return this.flowHandler.getFirstZoneWithSameId();
    }

    getLastZoneWithSameId() : CommonZoneIF {
        return this.flowHandler.getLastZoneWithSameId();
    }

    getNextZoneBlock(): CommonZoneIF {
        return this.flowHandler.getNextZoneBlock();
    }

    getNextZoneSkipHeaderFooter (): CommonZoneIF {
        let nextZone = this.getNextZoneBlock();
        while (nextZone
            && nextZone.isPaged()
            && ((nextZone as ZoneIF).isFooterZone() || (nextZone as ZoneIF).isHeaderZone())
            ) {
            nextZone = nextZone.getNextZoneBlock();
        }
        return nextZone;
    }

    getPreviousZoneBlock(): CommonZoneIF {
        return this.flowHandler.getPreviousZoneBlock();
    }

    getPreviousZoneSkipHeaderFooter (): CommonZoneIF {
        let prevZone = this.getPreviousZoneBlock();
        while (prevZone
            && prevZone.isPaged()
            && ((prevZone as ZoneIF).isFooterZone() || (prevZone as ZoneIF).isHeaderZone())
            ) {
            prevZone = prevZone.getPreviousZoneBlock();
        }
        return prevZone;
    }

    isAffectedByOtherZones(zones: ZoneIF[]): boolean {
        return this.flowHandler.isAffectedByOtherZones(zones);
    }

    moveHeightExceedingEndOfPageToNextBlock(): void {
        this.flowHandler.moveHeightExceedingEndOfPageToNextBlock();
    }

    remove(): void {
        this.flowHandler.remove();
    }

    removeAffectedBy(zone: ZoneIF): void {
        this.flowHandler.removeAffectedBy(zone);
    }

    removeAffectedZone(zone: ZoneIF): void {
        this.flowHandler.removeAffectedZone(zone);
    }

    removeZone(): void {
        try {
            this.flowHandler.removeZone();
        } finally {
            GlobalChangeNotifier.getInstance().removeZone(this);
        }
    }

    retainAffectedZones(zones: ZoneIF[]): void {
        this.flowHandler.retainAffectedZones(zones);
    }

    onAddContent(): void {
        if (this.getComponentType() === ZoneComponentTypeEnum.EMAIL_ZONE_COMPONENT_TYPE) {
            return;
        }

        this.stateHandler.onAddContent();
    }

    onPullOutsideOfPage(): void {
        this.stateHandler.onPullOutsideOfPage();
    }

    onPullUp(): void {
        this.stateHandler.onPullUp();
    }

    onPushedDown(): void {
        this.stateHandler.onPushedDown();
    }

    onRemoveContent(): void {
        if (this.getComponentType() === ZoneComponentTypeEnum.EMAIL_ZONE_COMPONENT_TYPE) {
            return;
        }

        this.stateHandler.onRemoveContent();
    }

    updateZoneState(): void {
        this.stateHandler.updateZoneState();
    }

    afterMovedToNextPage(): void {
        this.stateHandler.afterMovedToNextPage();
    }

    abstract createTemporaryZone(): ZoneIF;
    abstract updateBinding(updateBindingContext: UpdateBindingContext): void;

    setZoneState(zoneState: ZoneStateIF): void {
        this.stateHandler.setZoneState(zoneState);
    }

    public mergeSplitContent(): ContentIF {
        let zonesToProcess = ZoneUtils.computeLinkedZonesForMergeSplitContent(this as ZoneIF);
        if (ArrayUtil.isEmptyArray(zonesToProcess)) {
            return undefined;
        }
        let firstZone = zonesToProcess.shift();
        let remainingZonesWithSameId = zonesToProcess;
        const clone = firstZone.getContent().cloneDetached();
        if (!clone) {
            return undefined;
        }
        for (let crtZone of remainingZonesWithSameId) {
            clone.appendMergeChildren(crtZone.getContent().cloneDetached());
        }

        let cloneJqueryElement = clone.getJqueryElement();
        if (!cloneJqueryElement || cloneJqueryElement.length === 0) {
            return clone;
        }

        JQueryUtil.cleanNodeForMergeSplitContent(cloneJqueryElement[0]);

        return clone;
    }

    public getLastParagraph(): JQuery {
        return this.getContent().getJqueryElement().find("p").last();
    }

    public getFirstListNoIndent(): JQuery {
        return this.getContent().getJqueryElement().find("ol:not(ol ol):not(ul ol),ul:not(ul ul):not(ol ul):not(.wsc-list)").first();
    }

    public getFirstOrderedListNoIndent(): JQuery {
        return this.getContent().getJqueryElement().find("ol:not(ol ol):not(ul ol)").first();
    }

    public getFirstUnorderedListNoIndent(): JQuery {
        return this.getContent().getJqueryElement().find("ul:not(ul ul):not(ol ul):not(.wsc-list)").first();
    }

    public getLastListNoIndent(): JQuery {
        return this.getContent().getJqueryElement().find("ol:not(ol ol):not(ul ol),ul:not(ul ul):not(ol ul):not(.wsc-list)").last();
    }

    public getLastOrderedListNoIndent(): JQuery {
        return this.getContent().getJqueryElement().find("ol:not(ol ol):not(ul ol)").last();
    }

    public getLastUnorderedListNoIndent(): JQuery {
        return this.getContent().getJqueryElement().find("ul:not(ul ul):not(ol ul):not(.wsc-list)").last();
    }

    public isTemporaryZone(): boolean {
        return this.temporaryZone;
    }

    public setTemporaryZone(temporaryZone): void {
        this.temporaryZone = temporaryZone;
    }

    public addDataContentPieceIdIfMissingRecursively() {
        let tmpJqueryElement = this.getJqueryElement();
        if (!tmpJqueryElement || tmpJqueryElement.length === 0) {
            return;
        }
        let tmpNode : Node = tmpJqueryElement[0];

        let handleContentChangeNotifications = this.isHandleContentChangeNotifications();
        if (handleContentChangeNotifications) {
            this.setHandleContentChangeNotifications(false);
        }
        try {
            ContentFactory.addDataContentPieceIdIfMissingRecursively(tmpNode);
        } finally {
            if (handleContentChangeNotifications) {
                this.setHandleContentChangeNotifications(true);
            }
        }
    }

    getZoneState(): ZoneStateIF {
        return this.stateHandler.getZoneState();
    }

    isContinuous(): boolean {
        return false;
    }

    isPaged(): boolean {
        return !this.isContinuous();
    }

    getRootContent(): ContentIF {
        return this.contentHandler.getRootContent();
    }

    setRootContent(rootContent: ContentIF): void {
        this.contentHandler.setRootContent(rootContent);
    }

    getZoneContentType(): ZoneContentType {
        return ZoneContentType.TEXT;
    }
}
