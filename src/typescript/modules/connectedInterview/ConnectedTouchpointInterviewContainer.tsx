import * as React from 'react';
import ConnectedTouchpointInterviewEntryItem from "./ConnectedTouchpointInterviewEntryItem";
import SwitchComponent from "./components/SwitchComponent";
import "./ConnectedTouchpointInterviewContainer.css"
import AsyncConnectedTouchpointInterviewController from "./src/model/AsyncConnectedTouchpointInterviewController"
import ConnectedTouchpointInterviewReorderAction from "./src/model/ConnectedTouchpointInterviewReorderAction"

import ReactDOM from 'react-dom';
import {Type} from "./ConnectedTouchpointInterviewTypeComponent";
import {MessagesLoad} from "./src/model/MessagesLoad";
import ErrorRendering from "./src/model/ErrorRendering";
import {CriteriaItemModel} from "./src/model/CriteriaItemModel";
import ConnectedTouchpointSelectionByIdComponent, {SelectedValue} from "./components/ConnectedTouchpointSelectionByIdComponent";
import AutocompleteSelection from "./components/AutocompleteSelection";
import {
    RepeatingDataType,
} from "../communication/components/utils/enums";

MessagesLoad.getInstance();

interface IProps {

}

interface Item {
    id: number
    name?: string,
    isPrimaryDriverEntry?: boolean,
    isIndicatorEntry?: boolean,
    description?: string,
    parentOrder: number,
    order: number,
    action?: number,
    itemIndex?: number,
    type: Type,
    criteriaItems: CriteriaItemModel[] | null,
    regexValidation: string,
    criteriaOperator: string,
    guid: string,
    repeatingDataTypeId: number;


}

interface Document {
    communicationDataFeedWebServiceUi: {
        url: string,
        username: string,
        password: string
    },
    communicationOrderEntryEnabled: boolean,
    communicationAppliesTouchpointSelection: boolean,
    isEnableForVariation: boolean
}

interface IState {
    metadataFormItems: Array<Item>,
    types: Type[],
    sizeTypes: [],
    validationTypes: [],
    connectedPrivacyTypes: [],
    repeatingDataTypes: [],
    dataElementTypes:[],
    inputFormatString:[],
    inputFormatBoolean: [],
    inputFormatDate: [],
    inputFormatNumeric: [],
    requestRefreshTypes: [],
    referenceDataSources: [],
    addNewItem: boolean,
    order: number,
    dataVariableElements: [SelectedValue],
    document: Document,
    communicationAppliesTouchpointSelection: boolean,
    allApplicableVariants: [],
    uploadedFileTypes: [],
    isItemTypeWeb: boolean,
    index: number,
    childrenMap: Map<number, []>,
    brothersMap: Map<number, any>,
    isUpdateSuccess: boolean,
    isUpdateFailed: boolean,
    errorMessage: string[],
    fixit: boolean,
    isCollapsedAll: boolean,
    saveInProgress:boolean,
    primaryDataVariableId: number,
    referenceDataVariableId: number,
    primaryDataElementVariables:SelectedValue [],
    dataCollectionMissing: boolean
}

declare var context: string;

declare let getParam: (string) => string;

class ConnectedTouchpointInterviewContainer extends React.Component<IProps, IState> {

    constructor(props: IProps) {
        super(props);
        this.state = {
            metadataFormItems: undefined,
            types: [],
            sizeTypes: [],
            validationTypes: [],
            connectedPrivacyTypes: [],
            repeatingDataTypes: [],
            dataElementTypes: [],
            inputFormatBoolean: [],
            inputFormatDate: [],
            inputFormatNumeric: [],
            inputFormatString: [],
            requestRefreshTypes: [],
            referenceDataSources: [],
            addNewItem: false,
            order: 0,
            index: 0,
            dataVariableElements: [],
            document: null,
            communicationAppliesTouchpointSelection: false,
            allApplicableVariants: [],
            uploadedFileTypes: [],
            isItemTypeWeb: false,
            childrenMap: new Map,
            isUpdateFailed: false,
            isUpdateSuccess: false,
            errorMessage: new Array(),
            brothersMap: new Map(),
            fixit: false,
            isCollapsedAll: true,
            saveInProgress: false,
            primaryDataElementVariables: [],
            dataCollectionMissing: false
        }
    }

    UNSAFE_componentWillReceiveProps(nextProps) {
        if (nextProps) {
            this.setState({
                order: 0,
                index: 0
            });
        }
    }

    componentDidMount() {
        AsyncConnectedTouchpointInterviewController.loadDataSourceAssociationInfo(result => {
            if(result.primaryDataVariableId) {
                this.setState({primaryDataVariableId: result.primaryDataVariableId});
            }
            if(result.referenceDataVariableId) {
                this.setState({referenceDataVariableId: result.referenceDataVariableId});
            }
            if(result.primaryDataElementVariables){
                let dataElementVariables: SelectedValue[] = [];
                result.primaryDataElementVariables.forEach(item => {
                    if (item.name && item.id > 0) {
                        let selectedValue = {
                            id: item.id,
                            name: item.name

                        }
                        dataElementVariables.push(selectedValue);
                    }
                })
                this.setState({primaryDataElementVariables: dataElementVariables});
            }
        });
        AsyncConnectedTouchpointInterviewController.loadAllInputFormatDate(result => {
            this.setState({inputFormatDate: result});
        });
        AsyncConnectedTouchpointInterviewController.loadAllInputFormatNumeric(result => {
            this.setState({inputFormatNumeric: result});
        });
        AsyncConnectedTouchpointInterviewController.loadAllInputFormatString(result => {
            this.setState({inputFormatString: result});
        });
        AsyncConnectedTouchpointInterviewController.loadAllInputFormatBoolean(result => {
            this.setState({inputFormatBoolean: result});
        });

        AsyncConnectedTouchpointInterviewController.loadAllMetadataFormItems(result => {
            let items = result.items;
            if (result.warn) {
                let warn = new ErrorRendering(result.warn);
                let fix = false;
                result.warn.forEach ( item => {
                    if (item.codes.includes("error.order.entry.duplicate.order.number")) {
                        fix = true;
                    }
                });
                this.setState({
                    errorMessage: warn.getDisplayableErrorMessage(),
                    isUpdateFailed: true,
                    fixit: (result.fixit) ? fix : false
                });
            }
            if (result.dataCollectionMissing) {
                let dataCollectionMissing = new ErrorRendering(result.dataCollectionMissing);
                this.setState({
                    errorMessage: dataCollectionMissing.getDisplayableErrorMessage(),
                    isUpdateFailed: true,
                    dataCollectionMissing: true})
            }
            let max = 0;
            items.forEach(item => {
                if (item.order > max) {
                    max = item.order;
                }
            });
            this.setState({
                metadataFormItems: items,
                order: items ? max : 0
            }, () => {
                this.computeChildrenMap();
                this.getDisplayWebDataForItems();
            });
        });
        AsyncConnectedTouchpointInterviewController.loadAllTypes(result => {
            this.setState({types: result});
        });
        AsyncConnectedTouchpointInterviewController.loadAllDataElements(result => {
            if(result) {
                let dataElementVariables = [];
                result.forEach(item => {
                    let selectedValue = {
                        id : item.id,
                        name : item.name

                    }
                    dataElementVariables.push(selectedValue);
                })
                this.setState({dataVariableElements: dataElementVariables});
            }
        });
        AsyncConnectedTouchpointInterviewController.loadLazyDocumentInfo(result => {
            this.setState({
                document: result,
                communicationAppliesTouchpointSelection: result.communicationAppliesTouchpointSelection
            });
        });
        AsyncConnectedTouchpointInterviewController.loadAllSizeTypes(result => {
            this.setState({sizeTypes: result});
        });
        AsyncConnectedTouchpointInterviewController.loadAllValidationTypes(result => {
            this.setState({validationTypes: result});
        });
        AsyncConnectedTouchpointInterviewController.loadAllConnectedDataPrivacyTypes(result => {
            this.setState({connectedPrivacyTypes: result});
        });
        AsyncConnectedTouchpointInterviewController.loadAllRepeatingDataTypes(result => {
            this.setState({repeatingDataTypes: result});
        });
        AsyncConnectedTouchpointInterviewController.loadAllDataElementTypes(result => {
            this.setState({dataElementTypes: result});
        });
        AsyncConnectedTouchpointInterviewController.loadWebServiceRefreshTypes(result => {
            this.setState({requestRefreshTypes: result});
        });
        AsyncConnectedTouchpointInterviewController.loadUploadedFileTypes(result => {
            this.setState({uploadedFileTypes: result});
        });
        AsyncConnectedTouchpointInterviewController.loadApplicableVariants(result => {
            this.setState({allApplicableVariants: result});
        });
        AsyncConnectedTouchpointInterviewController.loadFileReferenceDataSources(result => {
            this.setState({referenceDataSources: result});
        });

    }

    private rootOrphanChildren () {
        this.state.metadataFormItems.map (value => {
            if (value.parentOrder && !(this.getMetadataFormItemByOrder(value.parentOrder))) {
                console.warn("Item " + value.order + " " + value.name + " was moved to root level. Please review its definition.");
                value.name = "PLEASE REVIEW: " + value.name;
                value.parentOrder = null;
            }
        })
    }

    private getMetadataFormItemByOrder (order) {
        for (let i = 0; i < this.state.metadataFormItems.length; i ++) {
            if (this.state.metadataFormItems[i].order === order) {
                return this.state.metadataFormItems[i];
            }
        }
        return null;
    }

    /**
     * Creates a map with key the order and the value a list of all children for the order.
     */
    private computeChildrenMap(): void {
        let childrenMap = new Map();
        if (this.state.metadataFormItems) {
            this.state.metadataFormItems.map(item => {
                if (item.action === -9) {
                    return;
                }
                let childrenList = []
                if (!item.parentOrder) {
                    if (childrenMap.get(-1)) {
                        childrenList = childrenMap.get(-1);
                    }
                    childrenList.push(item.order);
                    childrenMap.set(-1, childrenList);
                } else {
                    if (childrenMap.get(item.parentOrder)) {
                        childrenList = childrenMap.get(item.parentOrder);
                    }
                    childrenList.push(item.order);
                    childrenMap.set(item.parentOrder, childrenList);
                }
            });
        }
        let orderedItemsNr = [];
        this.getChildren(orderedItemsNr, -1, childrenMap);

        let orderedMetadataFormItems = [];
        orderedItemsNr.forEach((value) => {
            let item = this.getMetadataFormItemByOrder(value);
            if (item) {
                orderedMetadataFormItems.push(item);
            } else {
                console.log("unable to identify item having order " + value);
            }
        });
        // add duplicates & orphans at the end.
        this.state.metadataFormItems.forEach(item => {
           if (!orderedMetadataFormItems.includes(item)) {
               orderedMetadataFormItems.push(item);
           }
        });

        this.setState({
            metadataFormItems: orderedMetadataFormItems,
            childrenMap: childrenMap
        }, () => {this.computeBrotherMap();});
    }


    private getChildren (orderedList, currentOrder, map) {
        let childrenList = map.get(currentOrder);
        if (!childrenList) {
            return;
        }
        for (let i = 0; i < childrenList.length; i ++) {
            if (orderedList.includes(childrenList[i])) {
                continue;
            }
            orderedList.push(childrenList[i]);
            this.getChildren(orderedList, childrenList[i], map);
        }
    }

    private computeBrotherMap(): void {
        let brothersMap = new Map();
        this.state.metadataFormItems.map(item => {
            if (item.action !== -9)
                brothersMap.set(item.order, {
                    little: this.findLittleBrotherOrder(item),
                    big: this.findBigBrotherOrder(item)
                });
        });
        this.setState({brothersMap})
    }

    private findLittleBrotherOrder(item): number {
        let littleBrotherOrder: number = null;

        if (item.parentOrder) {
            const brothersList = this.state.childrenMap.get(item.parentOrder);
            // look on brother's list
            if (brothersList && brothersList.length > 1) {
                for (let i = 0; i < brothersList.length; i++) {
                    if (brothersList[i] === item.order) {
                        littleBrotherOrder = brothersList[i - 1];
                        break;
                    }
                }
            }
            return littleBrotherOrder;
        }
        this.state.metadataFormItems.forEach(itemIt => {
            if (!itemIt.parentOrder && itemIt.order < item.order) {
                littleBrotherOrder = itemIt.order;
            }
        });
        return littleBrotherOrder;
    }

    private findBigBrotherOrder(item): number {
        let bigBrotherOrder: number = null;

        if (item.parentOrder) {
            const brothersList = this.state.childrenMap.get(item.parentOrder);
            // look on brother's list
            if (brothersList && brothersList.length > 1) {
                for (let i = 0; i < brothersList.length - 1; i++) {
                    if (brothersList[i] === item.order) {
                        bigBrotherOrder = brothersList[i + 1];
                        break;
                    }
                }
            }
            return bigBrotherOrder;
        }

        for (let j = this.state.metadataFormItems.length - 1; j >= 0; j--) {
            if (!this.state.metadataFormItems[j].parentOrder && this.state.metadataFormItems[j].order > item.order) {
                bigBrotherOrder = this.state.metadataFormItems[j].order;
            }
            if (this.state.metadataFormItems[j].order === item.order) {
                break;
            }
        }

        return bigBrotherOrder;
    }

    private onClickSave(): void {
        this.reOrderItemsFull();
        // reset state for success/ failed values
        this.setState({isUpdateFailed: false, isUpdateSuccess: false, fixit: false, saveInProgress: true});
        $.ajax({
            type: "POST",
            url: context + `/communication/connected/asyncConnectedTouchpointInterview.form?type=saveUpdate&tk=${getParam('tk')}&documentId=${getParam('documentId')}`,
            // contentType: "application/json; charset=utf-8",
            data: {
                content: JSON.stringify({
                    metadataFormItemDefinitionVOs: this.state.metadataFormItems,
                    primaryDataVariableId: this.state.primaryDataVariableId,
                    referenceDataVariableId: this.state.referenceDataVariableId,
                    document: {
                        communicationDataFeedWebService: {
                            url: this.state.document ? this.state.document.communicationDataFeedWebServiceUi.url : '',
                            username: this.state.document ? this.state.document.communicationDataFeedWebServiceUi.username : '',
                            updatedPassword: this.state.document ? this.state.document.communicationDataFeedWebServiceUi.password : '',
                        },
                        communicationOrderEntryEnabled: this.state.document ? this.state.document.communicationOrderEntryEnabled : false,
                        communicationAppliesTouchpointSelection: this.state.communicationAppliesTouchpointSelection,
                    }
                })
            },
            dataType: "json",
        }).then(() => {
            this.setState({saveInProgress: false});
        }, (error) => {
            this.setResponseMessage(error.responseText);
            this.setState({saveInProgress: false});
        });
        $(window).scrollTop(0);
    }

    private onClickExpandAll(): void {
        this.setState({isCollapsedAll: !this.state.isCollapsedAll});
    }

    private setResponseMessage(message) {
        let errorMessages: string[] = new Array();
        if (!message) {
            errorMessages.push('Failed to save/update');
        } else if (message.includes('202')) {
            this.setState({isUpdateSuccess: true});
            AsyncConnectedTouchpointInterviewController.loadAllMetadataFormItems(result => {
                let items = result.items;
                let max = 0;
                items.forEach(item => {
                    if (item.order > max) {
                        max = item.order;
                    }
                });
                this.setState({
                    metadataFormItems: items,
                    order: items ? max : 0
                }, () => {
                    this.computeChildrenMap();
                });
            });
        } else if (message.includes('500')) {
            errorMessages.push('Failed to save/update');
        } else if (message.includes('422')) {
            message = message.replace('<422 UNPROCESSABLE_ENTITY Unprocessable Entity,', '');
            message = message.replace('>', '');
            message = message.replace(',[]', '');
            let response = JSON.parse(message);
            let errorRendering = new ErrorRendering(response.errors);
            if (response.fixit) {
                let fix = false;
                response.errors.forEach ( item => {
                    if (item.codes.includes("error.order.entry.duplicate.order.number")) {
                        fix = true;
                    }
                })
                this.setState({
                    fixit: fix
                })
            }
            errorMessages = errorMessages.concat(errorRendering.getDisplayableErrorMessage());
        }
        this.setState({
            isUpdateFailed: true,
            errorMessage: errorMessages
        });
    }

    private onClickAddNewItem() {
        const newIndexValue: number = this.state.index + 1;
        const order: number = this.state.order + 1;
        const newItem: Item = {
            order: order,
            parentOrder: null,
            itemIndex: newIndexValue,
            action: 1,
            type: this.state.types[0],
            id: parseInt(`-${order}`),
            criteriaItems: null,
            repeatingDataTypeId: RepeatingDataType.None
        };
        const newList = this.state.metadataFormItems;
        newList.push(newItem);
        this.setState({
            addNewItem: true,
            order: order,
            index: newIndexValue,
            metadataFormItems: newList,
        }, () => {
            this.computeChildrenMap();
        });
    }

    private renderButtonActions() {
        return (<div className="bottomSectionButtonsContainer">
            <div id="addEntryButtonBottom" className="btn btn-primary highlightedBtn"
                 onClick={() => this.onClickAddNewItem()}>
                <i className="icon fa fa-plus-circle" aria-hidden="true"/>
                <span className="text">
                    {MessagesLoad.getInstance().getValueForLabel('page.label.add.item')}
                </span>
            </div>

            <button id="saveEntryButtonBottom" className="btn btn-primary highlightedBtn"
                 onClick={() => this.onClickSave()} disabled={this.state.saveInProgress}>
                <i className="icon fa fa-save" aria-hidden="true"/>
                <span className="text">
                    { this.state.saveInProgress && <>{MessagesLoad.getInstance().getValueForLabel('page.label.save.in.progress')}<span id="loading-overlay" style={{marginLeft:2}}><i className="fad fa-spinner fa-pulse"></i> </span></> }
                    { !this.state.saveInProgress && MessagesLoad.getInstance().getValueForLabel('page.label.save')}

                </span>
            </button>
        </div>)
    }

    private updateItemCurrentState(itemState) {
        if (itemState.type.id === 4 || itemState.type.id === 10 || itemState.type.id === 11) {
            this.setState({isItemTypeWeb: true});
        }
        const updatedList = this.state.metadataFormItems.map(item => {
                if (item.id !== itemState.id) {
                    return item;
                }
                return itemState;
            }
        );
        this.setState({metadataFormItems: updatedList});
    }

    private reorderMoveUp(item: any, brothers: Map<number, any>): void {

        const action = new ConnectedTouchpointInterviewReorderAction(this.state.childrenMap, this.state.metadataFormItems);
        let items = this.getGroupItems(item)
        let brothersItem = brothers.get(item.order);
        let isInsideGroup = false;
        let littleBrother = null;
        if (brothersItem.little && brothersItem.little > 0) {
            littleBrother = this.getMetadataFormItemByOrder(brothersItem.little);
            if(littleBrother &&  (littleBrother.repeatingDataTypeId == RepeatingDataType.RepeatAndGroupWithAbove || littleBrother.repeatingDataTypeId == RepeatingDataType.Repeat) && item.repeatingDataTypeId == RepeatingDataType.RepeatAndGroupWithAbove){
                isInsideGroup = true;
            }
        }
        let littleBrotherItems  = [];
        if (littleBrother){
            littleBrotherItems = this.getGroupItems(littleBrother);
        }
        if(littleBrother != null && littleBrother.repeatingDataTypeId == RepeatingDataType.Repeat && item.repeatingDataTypeId == RepeatingDataType.RepeatAndGroupWithAbove) {
            item.repeatingDataTypeId = RepeatingDataType.Repeat
        }
        //This logic is used to move groups between each other
        if (!isInsideGroup && items && littleBrotherItems && items.length > 1 && littleBrotherItems.length> 1 && littleBrotherItems.toString() !== items.toString()) {
            let littleBrotherItemsLength = littleBrotherItems.length;
                items.forEach((itemFromGroup, index) => {
                    littleBrotherItems.forEach((itemUp, index) => {
                        const newMetadataList = action.reorderMoveUp(itemFromGroup, brothers.get(itemFromGroup.order), this.state.order);
                        this.setState({metadataFormItems: newMetadataList}, () => {
                            this.computeChildrenMap();
                        });
                });
            })
        } else if(items && items.length > 1 && !isInsideGroup) {
            items.forEach((itemFromGroup, index) => {
                const newMetadataList = action.reorderMoveUp(itemFromGroup, brothers.get(itemFromGroup.order), this.state.order);
                this.setState({metadataFormItems: newMetadataList}, () => {
                    this.computeChildrenMap();
                });
            });
        } else if(!isInsideGroup && littleBrother != null && littleBrother.repeatingDataTypeId == RepeatingDataType.RepeatAndGroupWithAbove || ( littleBrotherItems && littleBrotherItems.length > 1 && littleBrother.repeatingDataTypeId == RepeatingDataType.Repeat)){
            this.reorderMoveDown(littleBrother, brothers);
        } else {
            const newMetadataList = action.reorderMoveUp(item, brothers.get(item.order), this.state.order);
            this.setState({metadataFormItems: newMetadataList}, () => {
                this.computeChildrenMap();
            });
        }
    }


    private reorderMoveDown(item: any, brothers: any): void {
        const action = new ConnectedTouchpointInterviewReorderAction(this.state.childrenMap, this.state.metadataFormItems);
        let items = this.getGroupItems(item)
        items.sort((a, b) => b.order - a.order);
        let brothersItem = brothers.get(item.order);
        let isInsideGroup = false;
        let bigBrother = null;
        if (brothersItem.big && brothersItem.big > 0) {
            bigBrother = this.getMetadataFormItemByOrder(brothersItem.big);
            if(bigBrother && bigBrother.repeatingDataTypeId == RepeatingDataType.RepeatAndGroupWithAbove && (item.repeatingDataTypeId == RepeatingDataType.RepeatAndGroupWithAbove || item.repeatingDataTypeId == RepeatingDataType.Repeat)){
                isInsideGroup = true;
            }
        }
        let bigBrotherItems  = [];
        if (bigBrother){
            bigBrotherItems = this.getGroupItems(bigBrother);
        }

        //This logic is used to move groups between each other
        if (!isInsideGroup && items && bigBrotherItems && items.length > 1 && bigBrotherItems.length> 1 && bigBrotherItems.toString() !== items.toString()) {
              items.forEach((itemFromGroup, index) => {
                  bigBrotherItems.forEach((itemUp, index) => {
                    const newMetadataList = action.reorderMoveDown(itemFromGroup,  this.state.order);
                    this.setState({metadataFormItems: newMetadataList}, () => {
                        this.computeChildrenMap();
                    });
                });
            })
        } else
        // move the all group
        if(items && items.length > 1 && !isInsideGroup) {
            items.forEach((itemFromGroup, index) => {
                const newMetadataList = action.reorderMoveDown(itemFromGroup,  this.state.order);
                this.setState({metadataFormItems: newMetadataList}, () => {
                    this.computeChildrenMap();
                });
            })
        } else if(!isInsideGroup && bigBrother != null && (bigBrother.repeatingDataTypeId == RepeatingDataType.RepeatAndGroupWithAbove || ( bigBrotherItems && bigBrotherItems.length > 1 && bigBrother.repeatingDataTypeId == RepeatingDataType.Repeat))){ //Check if the next item is part of the group and change the position with the entire group.
                let itemsGroup = this.getGroupItems(bigBrother);
                this.reorderMoveUp(bigBrother, brothers);
        } else {
            const newMetadataList = action.reorderMoveDown(item, this.state.order);
            this.setState({metadataFormItems: newMetadataList}, () => {
                this.computeChildrenMap();
            });
        }
    }

    private reorderPromote(item: any): void {
        const action = new ConnectedTouchpointInterviewReorderAction(this.state.childrenMap, this.state.metadataFormItems);
        let items = this.getGroupItems(item)

        if (items && items.length > 1  ) {
            items.forEach((itemFromGroup, index) => {
                if( itemFromGroup.parentOrder <= 0){
                    itemFromGroup.criteriaItems = null;
                }
                let newMetadataList = action.reorderPromote(itemFromGroup);
                this.setState({metadataFormItems: newMetadataList}, () => {
                    this.computeChildrenMap();
                });
            })
        } else {
            let newMetadataList = action.reorderPromote(item);
            if(item.parentOrder <= 0) {
                item.criteriaItems = null;
            }
            this.setState({metadataFormItems: newMetadataList}, () => {
                this.computeChildrenMap();
            });
        }

    }

    private reorderNest(item: any): void {
        const action = new ConnectedTouchpointInterviewReorderAction(this.state.childrenMap, this.state.metadataFormItems);
        let items = this.getGroupItems(item)
        let brothers = this.state.brothersMap.get(item.order);
        if (items && items.length > 1 && brothers ) {
            items.forEach((itemFromGroup, index) => {
                if( !itemFromGroup.criteriaOperator){
                    itemFromGroup.criteriaOperator = 'AND';
                }
                let newMetadataList = action.reorderNest(itemFromGroup, this.state.brothersMap, brothers.little);
                this.setState({metadataFormItems: newMetadataList}, () => {
                    this.computeChildrenMap();
                });
            })
        } else {
            if (!item.criteriaOperator) {
                item.criteriaOperator = 'AND'
            }
            let newMetadataList = action.reorderNest(item, this.state.brothersMap, brothers.little);
            this.setState({metadataFormItems: newMetadataList}, () => {
                this.computeChildrenMap();
            });
        }
    }

    private renderContentItem(item, index, imbricationChildren, name?) {
        let dataVariableElementsDataSource = [];
        this.state.dataVariableElements.forEach(variable => {
            dataVariableElementsDataSource.push(variable);
        })
        if ( item.dataElementVariable) {
            let connectedInterviewDataVariable = dataVariableElementsDataSource.find(variable => variable.id === item.dataElementVariable.id)
            if(!connectedInterviewDataVariable){
                let selectedValue = {
                    id : item.dataElementVariable.id,
                    name : item.dataElementVariable.name

                }
                dataVariableElementsDataSource.push(selectedValue);
            }
        }
        return (
        <div key={item.id}>
            <ConnectedTouchpointInterviewEntryItem
                item={item}
                document={this.state.document}
                types={this.state.types}
                sizeTypes={this.state.sizeTypes}
                validationTypes={this.state.validationTypes}
                privacyTypes={this.state.connectedPrivacyTypes}
                requests={this.state.requestRefreshTypes}
                dataVariableElements = {dataVariableElementsDataSource}
                inputFormatBoolean={this.state.inputFormatBoolean}
                inputFormatString={this.state.inputFormatString}
                inputFormatDate={this.state.inputFormatDate}
                inputFormatNumeric={this.state.inputFormatNumeric}
                dataElementTypes={this.state.dataElementTypes}
                referenceDataSources={this.state.referenceDataSources}
                useAppVariant={this.state.communicationAppliesTouchpointSelection}
                allApplicableVariants={this.state.allApplicableVariants}
                uploadedFileTypes={this.state.uploadedFileTypes}
                itemIndex={index}
                imbricationChild={imbricationChildren}
                brothers={this.state.brothersMap.get(item.order)}
                name={name}
                collapsedAll={this.state.isCollapsedAll}
                updateItemCurrentState={(data) => this.updateItemCurrentState(data)}
                updateDisplayWebData={(typeId, itemId) => this.shouldDisplayDataWebServiceContainer(typeId, itemId)}
                removeItemAndChildren={(order) => this.removeItemAndChildren(order)}
                addNewChildrenToNode={(nodeId) => this.addNewChildrenToNode(nodeId)}
                reorderMoveDown={(order, brother) => this.reorderMoveDown(order, this.state.brothersMap)}
                reorderPromote={(order) => this.reorderPromote(order)}
                reorderNest={(order) => this.reorderNest(order)}
                reorderMoveUp={(order, brothers) => this.reorderMoveUp(order, this.state.brothersMap)}
                showReorderNest={(brothers, order) => this.showReorderNest(brothers, order)}
                parentItems={this.state.metadataFormItems}
                criteriaItems={item.criteriaItems}
                key={`${item.order}-${item.id}-${item.name ? item.name.split(" ").join("_") : ''}`}
                regexValidation = {item.regexValidation}
                dataElementVariableId={item.dataElementVariable ? item.dataElementVariable.id : -1}
                connectorDataVariableElementsDataSource = {this.state.dataVariableElements}
                criteriaOperator={item.criteriaOperator}
                repeatingDataTypes={this.state.repeatingDataTypes}
                guid = {item.guid}
                repeatingDataTypeId={item.repeatingDataTypeId}

            />
        </div>)
    }

    private showReorderNest(brothers, item: Item) :boolean {
        if( item.repeatingDataTypeId == RepeatingDataType.RepeatAndGroupWithAbove || item.repeatingDataTypeId == RepeatingDataType.Repeat){
            return  false;
        }
        if(brothers.little){
           let brotherUp = this.getMetadataFormItemByOrder(brothers.little);
            if( brotherUp.repeatingDataTypeId == RepeatingDataType.RepeatAndGroupWithAbove || brotherUp.repeatingDataTypeId == RepeatingDataType.Repeat){
                return  false;
            }
        }
        return  true;
    }

    private removeItemAndChildren(order) {
        //update action -9
        const metadataList = this.state.metadataFormItems;
        let metadataOrderToChange = metadataList.filter(item => {
                if (item.order === order) {
                    item.action = -9;
                    this.setState({
                        index: this.state.index - 1
                    });
                    return item;
                }
                return item;
            }
        );
        this.setState({metadataFormItems: metadataOrderToChange}, () => {
            const childrenList = this.state.childrenMap.get(order);
            if (childrenList) {
                childrenList.map(childOrder => this.removeItemAndChildren(childOrder));
            }
            this.computeChildrenMap();
        });
    }

    private addNewChildrenToNode(order: number) {
        let newItemsList = [];
        let position = -1;
        this.state.metadataFormItems.forEach((item, index) => {
            if (item.order === order || item.parentOrder === order) {
                    position = index;
            }
        });
        this.state.metadataFormItems.forEach((item, index) => {
            newItemsList.push(item);
            if (index === position) {
                const newChildItem = {
                    parentOrder: order,
                    action: 1,
                    id: parseInt(`-${this.state.order + 1}`),
                    order: this.state.order + 1,
                    type: this.state.types[0],
                    criteriaItems: [],
                    dataElementVariable: null,
                    criteriaOperator: 'AND'
                };
                const criteriaItemModel: CriteriaItemModel = {
                    parentOrder: order,
                    displayCriteria:null
                }
                newChildItem.criteriaItems.push(criteriaItemModel);
                newItemsList.push(newChildItem);
            }
        });

        this.setState({
            metadataFormItems: newItemsList,
            order: this.state.order + 1
        }, () => {
            this.computeChildrenMap();
        });
    }

    private renderContentItems() {
        let childrenMap = new Map();
        let childrenNameMap = new Map();
        const itemsToRender = [];
        let index = 0;
        if (this.state.metadataFormItems
            && this.state.metadataFormItems.length > 0) {
            this.state.metadataFormItems.map(item => {
                if (item.action === -9) {
                    return;
                }
                if (!item.parentOrder) {
                    index++;
                    item.itemIndex = index;
                    itemsToRender.push(this.renderContentItem(item, index, 0));
                    childrenNameMap.set(item.order, `${index}`);
                    return;
                }

                let imbricationChildren = 0;
                if (childrenMap.get(parseInt(item.parentOrder.toString(), 10))) {
                    imbricationChildren = childrenMap.get(item.parentOrder);
                }
                let childrenIndexInParentArray = 0;
                if (this.state.childrenMap.get(item.parentOrder)) {
                    for (let i = 0; i < this.state.childrenMap.get(item.parentOrder).length; i++) {
                        if (this.state.childrenMap.get(item.parentOrder)[i] === item.order) {
                            childrenIndexInParentArray = i;
                        }
                    }
                }
                imbricationChildren++;
                childrenIndexInParentArray++;
                childrenMap.set(item.order, imbricationChildren);
                let childrenName = `${childrenNameMap.get(parseInt(item.parentOrder.toString()))}.${childrenIndexInParentArray}`;
                childrenNameMap.set(item.order, childrenName);
                itemsToRender.push(this.renderContentItem(item, null, imbricationChildren, childrenName));

            });
            return itemsToRender;
        }
        if (this.state.metadataFormItems === null || (this.state.metadataFormItems !== undefined &&
            this.state.metadataFormItems.length === 0)) {
            return (<div id="noEntriesInfo" className="InfoSysContainer_info" style={{margin: "3px 8px"}}>
                <div>
                    {MessagesLoad.getInstance().getValueForLabel('page.text.touchpoint.has.no.order.entry.items')}
                </div>
                <div style={{paddingTop: "6px"}}><i>
                    {MessagesLoad.getInstance().getValueForLabel('page.text.click.button.to.add.items')}
                </i></div>
            </div>)
        }
        return <div/>
    }

    private renderConfiguration() {
        return (<div>
            <div className="tableComponentWrapper">
                <div className="actionsBarContainer">
                    <div className="actionBarHeaderLabel featureLabelText">
                        {MessagesLoad.getInstance().getValueForLabel('page.label.order.entry.setup')}
                    </div>
                    <div className="actionBarButtonsContainer">
                        <input id="addEntryButton" type="button" title="Add item"
                               className="actionBtn_roundAll actionBtn actionBtnText"
                               onClick={() => this.onClickAddNewItem()}
                               value={MessagesLoad.getInstance().getValueForLabel('page.label.add.item')} style={{marginTop: "-2px", paddingBottom: "2px"}}/>
                        <button id="saveBtn" style={{color:"#444", fontSize:"12px"}}
                               title= {MessagesLoad.getInstance().getValueForLabel('page.label.save')}
                               className="actionBtn_roundAll actionBtn actionBtnText"
                               onClick={() => this.onClickSave()} disabled={this.state.saveInProgress}>
                            <span className="text">
                                { this.state.saveInProgress && <>{MessagesLoad.getInstance().getValueForLabel('page.label.save.in.progress')}<span id="loading-overlay" style={{marginLeft:2}}><i className="fad fa-spinner fa-pulse"></i> </span></> }
                                { !this.state.saveInProgress && MessagesLoad.getInstance().getValueForLabel('page.label.save')}

                            </span>
                        </button>

                        {this.state.isCollapsedAll &&
                        <div className="float-left">
                            <input id="expandBtn" type="button"
                                   title={MessagesLoad.getInstance().getValueForLabel('page.label.expand.all')}
                                   value={MessagesLoad.getInstance().getValueForLabel('page.label.expand.all')}
                                   className="actionBtn_roundAll actionBtn actionBtnText"
                                   onClick={() => this.onClickExpandAll()} style={{marginTop: "-2px", paddingBottom: "2px"}}/>
                            <i className="fas fa-expand-arrows-alt" style={{color: '#fff'}}/>
                        </div>}

                        {!this.state.isCollapsedAll &&
                        <div className="float-left">
                            <input id="compressBtn" type="button"
                                   title={MessagesLoad.getInstance().getValueForLabel('page.label.collapse.all')}
                                   value={MessagesLoad.getInstance().getValueForLabel('page.label.collapse.all')}
                                   className="actionBtn_roundAll actionBtn actionBtnText"
                                   onClick={() => this.onClickExpandAll()}/>
                            <i className="fas fa-compress-arrows-alt" style={{color: '#fff'}}/>
                        </div>}
                    </div>
                </div>
            </div>
            {this.state.document && (
                <div className="configureInterviewPanel">
                    {this.renderSwitchApplicableVariants()}
                    {this.renderDataAssociationInformation()}
                    {this.renderDataWebServiceContainer()}
                </div>
            )}
        </div>)
    }

    private shouldDisplayDataWebServiceContainer(typeId: number, itemId: number) {
        if (typeId === 4 || typeId === 10 || typeId === 11) {
            this.setState({isItemTypeWeb: true});
        } else {
            this.getDisplayWebDataForItems(itemId);
        }
    }

    private getDisplayWebDataForItems(itemId?: number) {
        let shouldDisplayWebData = false;
        const webItems = this.state.metadataFormItems.filter(item => item.id !== itemId && (item.type.id === 4 || item.type.id === 10 || item.type.id === 11));
        if (webItems && webItems.length > 0) {
            shouldDisplayWebData = true;
        }
        this.setState({isItemTypeWeb: shouldDisplayWebData});
    }

    private reOrderItems () {
        let parents = [];
        let currentOrder = this.state.order;
        this.state.metadataFormItems.forEach(item => {
            if (parents.includes(item.order)) {
                console.warn ("Item " + item.order + " " + item.name + " was re-ordered to " + currentOrder + " " + item.name + ". Parent: " + item.parentOrder);
                item.order = ++currentOrder;
            }
            parents.push(item.order);
        })

        this.setState({
            order: currentOrder
        });
    }

    private reOrderItemsFull () {
        let orders = new Map();
        let currentOrder = this.state.order;
        for (let i = 0; i < this.state.metadataFormItems.length; i++) {
            if (orders.get(this.state.metadataFormItems[i].order)) {
                this.state.metadataFormItems[i].order = orders.get(this.state.metadataFormItems[i].order);
            } else {
                orders.set(this.state.metadataFormItems[i].order, i + 1);
                this.state.metadataFormItems[i].order = i + 1;
            }
        }
        for (let i = 0; i < this.state.metadataFormItems.length; i++) {
            if (orders.get(this.state.metadataFormItems[i].parentOrder)) {
                this.state.metadataFormItems[i].parentOrder = orders.get(this.state.metadataFormItems[i].parentOrder);
            }
            let metadataFormItem = this.state.metadataFormItems[i];
            if(metadataFormItem.criteriaItems) {
                for (let index = 0; index < metadataFormItem.criteriaItems.length; index++) {
                    metadataFormItem.criteriaItems[index].parentOrder = orders.get(metadataFormItem.criteriaItems[index].parentOrder)
                }
            }
        }
    }

    private renderSwitchApplicableVariants() {
        if (this.state.document.isEnableForVariation) {
            return (<div style={{"width": "30%"}} className="float-left">
                <div>
                    <span>{MessagesLoad.getInstance().getValueForLabel('page.label.variant.selection')}</span>
                    <SwitchComponent
                        checked={this.state.communicationAppliesTouchpointSelection}
                        colorCheck="#6d3075"
                        checkedLabel={MessagesLoad.getInstance().getValueForLabel('page.label.enabled')}
                        uncheckedLabel={MessagesLoad.getInstance().getValueForLabel('page.label.disabled')}
                        onChange={() => this.setState(
                            {
                                communicationAppliesTouchpointSelection: !this.state.communicationAppliesTouchpointSelection
                            })}
                    />
                </div>
            </div>)
        }
    }

    private renderDataAssociationInformation() {
        if(!this.state.dataVariableElements || !this.state.dataVariableElements.length || !this.state.primaryDataElementVariables || !this.state.primaryDataElementVariables.length) {
            return null;
        }

        return (
            <div className="float-left" >
                <AutocompleteSelection selectedValue = {this.state.primaryDataElementVariables.find(item=> item.id === this.state.primaryDataVariableId)}
                                                           values={this.state.primaryDataElementVariables}
                                                           title={MessagesLoad.getInstance().getValueForLabel('page.label.primary.data.variable')}
                                                           onSelectionChange={(data) => {
                                                               this.setState({
                                                                   primaryDataVariableId: data ? data.id : null,
                                                               })

                                                           }
                                                           }
                                                            itemOrder = {"primaryDataVariableId"}
                />

                <AutocompleteSelection  selectedValue={this.state.dataVariableElements.find(item=> item.id === this.state.referenceDataVariableId)}
                                                           values={this.state.dataVariableElements}
                                                           title={MessagesLoad.getInstance().getValueForLabel('page.label.reference.data.variable')}
                                                           onSelectionChange={(data) => {
                                                               this.setState({
                                                                   referenceDataVariableId: data ? data.id : null,
                                                               })

                                                           }
                                                           }
                                                          itemOrder = {"referenceDataVariableId"}
                />
            </div>
        )

    }

    private renderDataWebServiceContainer() {
        if (this.state.isItemTypeWeb) {
            return (<div style={{"width": "30%"}} className="float-left">
                <p> {MessagesLoad.getInstance().getValueForLabel('page.label.data.web.service')}</p>
                <div className="float-left" style={{"width": "100%"}}>
                    <p className="float-left"
                       style={{"width": "30%"}}>{MessagesLoad.getInstance().getValueForLabel('page.label.url')}</p>
                    <input
                        type="text"
                        value={this.state.document.communicationDataFeedWebServiceUi.url ?
                            this.state.document.communicationDataFeedWebServiceUi.url : ''}
                        style={{"width": "60%"}}
                        className="textInputStyle float-left"
                        onChange={(event) => this.setState({
                            document: {
                                communicationDataFeedWebServiceUi: {
                                    url: event.target.value,
                                    username: this.state.document.communicationDataFeedWebServiceUi.username,
                                    password: this.state.document.communicationDataFeedWebServiceUi.password,
                                },
                                communicationAppliesTouchpointSelection: this.state.communicationAppliesTouchpointSelection,
                                communicationOrderEntryEnabled: this.state.document.communicationOrderEntryEnabled,
                                isEnableForVariation: this.state.document.isEnableForVariation
                            }
                        })}
                    >
                    </input>
                </div>
                <div className="float-left" style={{"width": "100%"}}>
                    <p className="float-left"
                       style={{"width": "30%"}}>{MessagesLoad.getInstance().getValueForLabel('page.label.username')}</p>
                    <input
                        type="text"
                        value={this.state.document.communicationDataFeedWebServiceUi.username ?
                            this.state.document.communicationDataFeedWebServiceUi.username : ''}
                        style={{"width": "60%"}}
                        className="textInputStyle float-left"
                        onChange={(event) => this.setState({
                            document: {
                                communicationDataFeedWebServiceUi: {
                                    url: this.state.document.communicationDataFeedWebServiceUi.url,
                                    username: event.target.value,
                                    password: this.state.document.communicationDataFeedWebServiceUi.password
                                },
                                communicationAppliesTouchpointSelection: this.state.communicationAppliesTouchpointSelection,
                                communicationOrderEntryEnabled: this.state.document.communicationOrderEntryEnabled,
                                isEnableForVariation: this.state.document.isEnableForVariation
                            }
                        })}
                    >
                    </input>
                </div>
                <div className="float-left" style={{"width": "100%"}}>
                    <p className="float-left"
                       style={{"width": "30%"}}>{MessagesLoad.getInstance().getValueForLabel('page.label.password')}</p>
                    <input
                        type="password"
                        value={this.state.document.communicationDataFeedWebServiceUi.password ?
                            this.state.document.communicationDataFeedWebServiceUi.password : ''}
                        style={{"width": "60%"}}
                        className="textInputStyle float-left"
                        onChange={(event) => this.setState({
                            document: {
                                communicationDataFeedWebServiceUi: {
                                    url: this.state.document.communicationDataFeedWebServiceUi.url,
                                    username: this.state.document.communicationDataFeedWebServiceUi.username,
                                    password: event.target.value
                                },
                                communicationAppliesTouchpointSelection: this.state.communicationAppliesTouchpointSelection,
                                communicationOrderEntryEnabled: this.state.document.communicationOrderEntryEnabled,
                                isEnableForVariation: this.state.document.isEnableForVariation
                            }
                        })}
                    >
                    </input>
                </div>
            </div>)
        }
    }

    private renderUpdateResultInfoSection() {
        if (this.state.isUpdateSuccess) {
            return (<div>
                <div className="alert alert-success alert-dismissible text-left fade show"
                     style={{display: "list-item"}}>
                    <strong className="fs-md pr4 float-left">
                        <i className="fas fa-check-circle mr-2"/>
                        Success
                    </strong>
                    <p style={{
                        float: 'left',
                        marginLeft: '10px'
                    }}>{MessagesLoad.getInstance().getValueForLabel('page.label.save.complete')}</p>
                    <i className="fas fa-times-circle mt-2 float-right"
                       onClick={() => this.setState({isUpdateSuccess: false})}/>
                </div>
            </div>)
        }

        if (this.state.isUpdateFailed) {
            return (<div className="informationMessageSection alert alert-danger alert-dismissible text-left fade show">
                <strong className="fs-md pr4 float-left" style={{width: '8' +
                        '%'}}>
                    <i className="fas fa-minus-circle mt-2"/>
                    {this.state.dataCollectionMissing ? <span>Warning </span>: <span>Error</span>}
                </strong>
                <div style={{marginLeft: '10px', float: 'left', width: '89%'}}>
                    {this.renderErrorMessages()}
                    {this.renderFixDuplicate ()}
                </div>
                <i className="fas fa-times-circle mt-2 float-right"
                   onClick={() => this.setState({isUpdateFailed: false})}/>
            </div>)
        }
    }

    private renderErrorMessages() {
        if (this.state.errorMessage && this.state.errorMessage.length > 0) {
            return this.state.errorMessage.map(error => {
                return this.renderErrorMessage(error)
            });
        }
    }

    private renderErrorMessage(error) {
        return (<span key={error}>{error}<br/></span>)
    }

    private renderFixDuplicate () {
        if (this.state.fixit) {
            return (<div id="fixDuplicatesButton" className="btn btn-primary highlightedBtn"
                 onClick={() => {
                     this.rootOrphanChildren();
                     this.reOrderItems();
                     this.setState({
                         isUpdateFailed: false,
                         errorMessage: null,
                         fixit: false
                     });
                     this.computeChildrenMap();
                 }}>
                <span className="text">
                       {MessagesLoad.getInstance().getValueForLabel('page.label.fix.duplicates')}
                    </span>
            </div>);
        } else {
            return '';
        }
    }

    private getGroupItems(item: Item): Item[] {
        let items = [...this.state.metadataFormItems];

        if(item.repeatingDataTypeId == RepeatingDataType.None) return [];

        let itemIndex = items.findIndex(currentItem => currentItem.order === item.order);
        if (itemIndex === -1) return [];

        let groupStartIndex = itemIndex;
        let groupEndIndex = itemIndex;


        while (groupStartIndex > 0 && (items[groupStartIndex-1].repeatingDataTypeId == RepeatingDataType.RepeatAndGroupWithAbove && items[groupStartIndex].repeatingDataTypeId  != RepeatingDataType.Repeat)|| (items[groupStartIndex-1].repeatingDataTypeId == RepeatingDataType.Repeat &&  items[groupStartIndex].repeatingDataTypeId == RepeatingDataType.RepeatAndGroupWithAbove)) {
            groupStartIndex--;
        }

        while (groupEndIndex < items.length - 1 && items[groupEndIndex + 1].repeatingDataTypeId == RepeatingDataType.RepeatAndGroupWithAbove)  {
            groupEndIndex++;
        }
        let groupItems = items.slice(groupStartIndex, groupEndIndex + 1);

        /*let parentIds = new Set(groupItems.map(item => item.parentOrder).filter(order => order !== null));
        return groupItems.filter(item => !parentIds.has(item.order));*/

        return groupItems;
    }

    render() {
        return (
            <div>
                {this.renderUpdateResultInfoSection()}
                {this.renderConfiguration()}
                {this.renderContentItems()}
                {this.renderButtonActions()}
            </div>)
    }
}

ReactDOM.render(
    <ConnectedTouchpointInterviewContainer/>,
    document.getElementById('viewInterview')
);
