package com.prinova.migrate.models.V3_7_1;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.prinova.migrate.models.Model;
import com.prinova.migrate.models.V4_0_0.Document;

public class TouchpointSelection extends Model 
{
	private long id;
	public String guid;
	public int archiveTypeId;
	public boolean ownContentReady;
	public Long assigneeId;
	public long documentId;
	public long pgTreeNodeId;
	public long currentWorkflowStepId;
	public Date contentLastUpdated;
	public Long contentLastUpdatedBy;
	public Date latestProductionDate;
	public Date created;
	public Long createdBy;
	public Date updated;
	public Long updatedBy;
	public boolean fullyVisible;

	private static String COLUMNS = "tps.id, tps.guid, tps.archive_type_id, tps.own_content_ready, tps.assignee_id, tps.document_id, tps.pg_tree_node_id, tps.current_wf_step_id, tps.content_last_updated, tps.content_last_updated_by_id, tps.latest_production_date, tps.created, tps.created_by_id, tps.updated, tps.updated_by_id, tps.fully_visible";

	private TouchpointSelection( ResultSet rs ) throws Exception
	{
		id = rs.getLong(1);
		guid = rs.getString(2);
		archiveTypeId = getInt(rs, 3);
		ownContentReady = getBoolean(rs, 4);
		assigneeId = getLong(rs, 5);
		documentId = getLong(rs, 6);
		pgTreeNodeId = getLong(rs, 7);
		currentWorkflowStepId = getLong(rs, 8);
		contentLastUpdated = getDate(rs, 9);
		contentLastUpdatedBy = getLong(rs, 10);
		latestProductionDate = getDate(rs, 11);
		created = getDate(rs, 12);
		createdBy = getLong(rs, 13);
		updated = getDate(rs, 14);
		updatedBy = getLong(rs, 15);
		fullyVisible = getBoolean(rs, 16);
	}

	public long getId()
	{
		return id;
	}

	public static TouchpointSelection findDocumentRootNode( Connection conn, Document tp ) throws Exception
	{
		String query = 
			"SELECT " + COLUMNS + " FROM touchpoint_selection tps " +
			"INNER JOIN pg_tree_node pgtn on pgtn.id = tps.pg_tree_node_id " +
			"WHERE tps.document_id = ? AND pgtn.parent_node_id is null";
		PreparedStatement ps = conn.prepareStatement(query);
		ps.setLong(1, tp.getId());
		ResultSet rs = ps.executeQuery();

		while( rs.next() )
		{
			return new TouchpointSelection(rs);
		}
		return null;
	}

	public String getName( Connection conn ) throws Exception
	{
		String query = "SELECT name FROM pg_tree_node pgtn WHERE pgtn.id = ?";
		PreparedStatement ps = conn.prepareStatement(query);
		ps.setLong(1, pgTreeNodeId );
		ResultSet rs = ps.executeQuery();

		while( rs.next() )
		{
			return rs.getString(1);
		}
		return "";
	}
	
	public static List<TouchpointSelection> findDocumentNodes( Connection conn, Document tp ) throws Exception
	{
		List<TouchpointSelection> result = new ArrayList<>();
		String query = 
			"SELECT " + COLUMNS + " FROM touchpoint_selection tps " +
			"INNER JOIN pg_tree_node pgtn on pgtn.id = tps.pg_tree_node_id " +
			"WHERE tps.document_id = ?";
		PreparedStatement ps = conn.prepareStatement(query);
		ps.setLong(1, tp.getId());
		ResultSet rs = ps.executeQuery();

		while( rs.next() )
		{
			TouchpointSelection tps = new TouchpointSelection(rs);
			result.add(tps);
		}
		return result;
	}

	
	public boolean isActive( Connection conn ) throws Exception
	{
		String sql = "SELECT next_step_id from configurable_workflow_step where id = ?";
		PreparedStatement ps = conn.prepareStatement(sql);
		ps.setLong(1, currentWorkflowStepId);
		ResultSet rs = ps.executeQuery();

		while ( rs.next() )
		{
			Long next = getLong(rs, 1);
			if ( next == null )
				return true;
			return false;
		}
		return false;
	}
}
