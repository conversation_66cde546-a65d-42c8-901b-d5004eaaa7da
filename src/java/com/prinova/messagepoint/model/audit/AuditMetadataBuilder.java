package com.prinova.messagepoint.model.audit;

import com.prinova.licence.webapp.models.Licence;
import com.prinova.messagepoint.controller.admin.DataRecordEditController.Command;
import com.prinova.messagepoint.controller.admin.DocumentEditWrapper;
import com.prinova.messagepoint.controller.admin.LicenceEditWrapper;
import com.prinova.messagepoint.controller.admin.LicenceManagementWrapper;
import com.prinova.messagepoint.controller.admin.ReferenceConnectionVO;
import com.prinova.messagepoint.controller.content.ContentPowerEditWrapper;
import com.prinova.messagepoint.controller.content.ContentTransformWrapper;
import com.prinova.messagepoint.controller.targeting.TargetingWrapper;
import com.prinova.messagepoint.controller.tpadmin.DocumentChannelEditWrapper;
import com.prinova.messagepoint.controller.tpadmin.DocumentExtendedReportingVariablesWrapper;
import com.prinova.messagepoint.controller.tpadmin.TemplateVariantVO;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.clickatell.ClickatellConfiguration;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.contentintelligence.ContentAssistant;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataDefinition;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataElement;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDefinitionType;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataElement;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataTagDefinition;
import com.prinova.messagepoint.model.dialogue.DialogueConfiguration;
import com.prinova.messagepoint.model.eMessaging.EMessagingConfiguration;
import com.prinova.messagepoint.model.email.TemplateModifier;
import com.prinova.messagepoint.model.exacttarget.ExactTargetConfiguration;
import com.prinova.messagepoint.model.font.DecimalValueUtil;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.model.ftp.FtpConfiguration;
import com.prinova.messagepoint.model.gmc.GMCConfiguration;
import com.prinova.messagepoint.model.insert.WeightUtil;
import com.prinova.messagepoint.model.nativecomposition.NativeCompositionConfiguration;
import com.prinova.messagepoint.model.proof.Proof;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.Role;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.sefas.MPHCSConfiguration;
import com.prinova.messagepoint.model.sefas.SefasConfiguration;
import com.prinova.messagepoint.model.sendmail.SendmailConfiguration;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.ConditionSubelement;
import com.prinova.messagepoint.model.util.ConditionSubelementComparator;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowAction;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowStep;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.model.wrapper.TargetGroupWrapper;
import com.prinova.messagepoint.platform.services.admin.NewUserProxy;
import com.prinova.messagepoint.platform.services.admin.UserProxy;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateDataSourceServiceRequest;
import com.prinova.messagepoint.platform.services.dataadmin.jsonlayout.UpdateJSONDataElementServiceRequest;
import com.prinova.messagepoint.platform.services.dataadmin.xmllayout.UpdateXmlDataElementServiceRequest;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateDeproxyUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.PropertyChange;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.util.HtmlUtils;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;


public class AuditMetadataBuilder {
	
	public static final String S_LABEL				= "label";
	public static final String S_CHANGES			= "changes";
	public static final String S_FROM 				= "from";
	public static final String S_TO 				= "to";
	public static final String S_ACTION 			= "action";
	public static final String S_ADDED 				= "added";
	public static final String S_REMOVED 			= "removed";
	public static final String S_KEY 				= "key";
	public static final String S_VALUE 				= "value";

	public static String forSimpleAuditMessage(String label, String from, String to){
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			JSONObject itemObj = new JSONObject();
			if(label != null)
				itemObj.put(S_LABEL, label);
			if(from != null)
				itemObj.put(S_FROM, from);
			if(to != null)
				itemObj.put(S_TO, to);
			changesArr.put(itemObj);
			changesObj.put(S_CHANGES, changesArr);
			return changesObj.toString();
		} catch (JSONException e) {
			return null;
		}
	}
	
	public static String forSimpleStringCompareMessage(String label, String from, String to) {
		try {
			if(label == null)
				label = ApplicationUtil.getMessage("page.label.name");
			JSONObject changesObj = new JSONObject();
			JSONObject itemObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			if( compareString(from, to) != null){
				itemObj = compareString(from, to);
				itemObj.putOpt(S_LABEL, label);
				changesArr.put(itemObj);
			}
			//changesArr.put(itemObj);
			changesObj.put(S_CHANGES, changesArr);
			return changesObj.toString();
		} catch (JSONException e) {
			return null;
		}
	}
	
	public static String forRoleChange(Role from, Role to){
		try {
			JSONObject changesObj = new JSONObject();
			JSONObject itemObj = new JSONObject();
			itemObj.put(S_FROM, from.getName());
			itemObj.put(S_TO, to.getName());
			JSONArray changesArr = new JSONArray();
			changesArr.put(itemObj);
			changesObj.put(S_CHANGES, changesArr);
			return changesObj.toString();
		} catch (JSONException e) {
			return null;
		}
	}
	
	public static String forWorkgroupChange(Workgroup from, Workgroup to){
		try {
			JSONObject changesObj = new JSONObject();
			JSONObject itemObj = new JSONObject();
			itemObj.put(S_FROM, from.getName());
			itemObj.put(S_TO, to.getName());
			JSONArray changesArr = new JSONArray();
			changesArr.put(itemObj);
			changesObj.put(S_CHANGES, changesArr);
			return changesObj.toString();
		} catch (JSONException e) {
			return null;
		}
	}
	
	public static String forPermissionChange(Set<Permission> from, Set<Permission> to){
		try {
			List<Permission> addedList = new ArrayList<>(to);
			addedList.removeAll(from);
			List<Permission> removedList = new ArrayList<>(from);
			removedList.removeAll(to);
			
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			for(Permission perAdded : addedList){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, perAdded.getName());
				itemObj.put(S_ACTION, S_ADDED);
				changesArr.put(itemObj);
			}
			for(Permission perRemoved : removedList){
				// Ignore "ROLE_MESSAGE_VIEW_MY" and "ROLE_USER" permissions in removed list
				if(perRemoved.getId() == Permission.ID_ROLE_MESSAGE_VIEW_MY || perRemoved.getId() == Permission.ID_ROLE_USER)
					continue;
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, perRemoved.getName());
				itemObj.put(S_ACTION, S_REMOVED);
				changesArr.put(itemObj);
			}
			if(changesArr.isEmpty()){
				return null;
			}else{
				changesObj.put(S_CHANGES, changesArr);
				return changesObj.toString();
			}
		} catch (JSONException e) {
			return null;
		}
	}
	
	public static String forPasswordSettingsChanges(SecuritySettings from, SecuritySettings to){
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			if(!from.getMinLength().equals(to.getMinLength())){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "password minimum length");
				itemObj.put(S_FROM, from.getMinLength());
				itemObj.put(S_TO, to.getMinLength());
				changesArr.put(itemObj);
			}
			if(!from.getMaxLength().equals(to.getMaxLength())){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "password maximum length");
				itemObj.put(S_FROM, from.getMaxLength());
				itemObj.put(S_TO, to.getMaxLength());
				changesArr.put(itemObj);
			}
			if(from.isRequiresUppercase() != to.isRequiresUppercase()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "uppercase letter required");
				itemObj.put(S_FROM, from.isRequiresUppercase());
				itemObj.put(S_TO, to.isRequiresUppercase());
				changesArr.put(itemObj);
			}
			if(from.isRequiresLowercase() != to.isRequiresLowercase()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "lowercase letter required");
				itemObj.put(S_FROM, from.isRequiresLowercase());
				itemObj.put(S_TO, to.isRequiresLowercase());
				changesArr.put(itemObj);
			}
			if(from.isRequiresNumeral() != to.isRequiresNumeral()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "numeral required");
				itemObj.put(S_FROM, from.isRequiresNumeral());
				itemObj.put(S_TO, to.isRequiresNumeral());
				changesArr.put(itemObj);
			}
			if(from.isRequiresSymbol() != to.isRequiresSymbol()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "special symbol required");
				itemObj.put(S_FROM, from.isRequiresSymbol());
				itemObj.put(S_TO, to.isRequiresSymbol());
				changesArr.put(itemObj);
			}
			if(from.isNorepeats() != to.isNorepeats()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "reject consecutive repeated characters");
				itemObj.put(S_FROM, from.isNorepeats());
				itemObj.put(S_TO, to.isNorepeats());
				changesArr.put(itemObj);
			}
			if(from.isTrackFlag() != to.isTrackFlag()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "track invalid sign in attempts");
				itemObj.put(S_FROM, from.isTrackFlag());
				itemObj.put(S_TO, to.isTrackFlag());
				changesArr.put(itemObj);
			}
			if(!from.getMaxAttempts().equals(to.getMaxAttempts())){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "maximum attempts");
				itemObj.put(S_FROM, from.getMaxAttempts());
				itemObj.put(S_TO, to.getMaxAttempts());
				changesArr.put(itemObj);
			}
			if(from.isAlphanumericOnly() != to.isAlphanumericOnly()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "alphanumeric only");
				itemObj.put(S_FROM, from.isAlphanumericOnly());
				itemObj.put(S_TO, to.isAlphanumericOnly());
				changesArr.put(itemObj);
			}
			if(!from.getUsernameMinLength().equals(to.getUsernameMinLength())){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "username minumum length");
				itemObj.put(S_FROM, from.getUsernameMinLength());
				itemObj.put(S_TO, to.getUsernameMinLength());
				changesArr.put(itemObj);
			}	
			if(!from.getUsernameMaxLength().equals(to.getUsernameMaxLength())){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "username maximum length");
				itemObj.put(S_FROM, from.getUsernameMaxLength());
				itemObj.put(S_TO, to.getUsernameMaxLength());
				changesArr.put(itemObj);
			}
			if(!from.getPwResetKeepAlive().equals(to.getPwResetKeepAlive())){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "password reset keep-alive");
				itemObj.put(S_FROM, from.getPwResetKeepAlive());
				itemObj.put(S_TO, to.getPwResetKeepAlive());
				changesArr.put(itemObj);
			}
			if(from.isPwExpires() != to.isPwExpires()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "password expires");
				itemObj.put(S_FROM, from.isPwExpires());
				itemObj.put(S_TO, to.isPwExpires());
				changesArr.put(itemObj);
			}
			if(from.getPwExpireDays() != to.getPwExpireDays()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "password expires days");
				itemObj.put(S_FROM, from.getPwExpireDays());
				itemObj.put(S_TO, to.getPwExpireDays());
				changesArr.put(itemObj);
			}
			if(from.isSoftDeactivationEnabled() != to.isSoftDeactivationEnabled()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "soft deactivation");
				itemObj.put(S_FROM, from.isSoftDeactivationEnabled());
				itemObj.put(S_TO, to.isSoftDeactivationEnabled());
				changesArr.put(itemObj);
			}
			if(from.getSoftDeactivationLimitDays() != to.getSoftDeactivationLimitDays()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "soft deactivation limit days");
				itemObj.put(S_FROM, from.getSoftDeactivationLimitDays());
				itemObj.put(S_TO, to.getSoftDeactivationLimitDays());
				changesArr.put(itemObj);
			}
			if(from.isHardDeactivationEnabled() != to.isHardDeactivationEnabled()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "hard deactivation");
				itemObj.put(S_FROM, from.isHardDeactivationEnabled());
				itemObj.put(S_TO, to.isHardDeactivationEnabled());
				changesArr.put(itemObj);
			}
			if(from.getHardDeactivationLimitDays() != to.getHardDeactivationLimitDays()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "hard deactivation limit days");
				itemObj.put(S_FROM, from.getHardDeactivationLimitDays());
				itemObj.put(S_TO, to.getHardDeactivationLimitDays());
				changesArr.put(itemObj);
			}
			if(from.isPreventRepeatedPw() != to.isPreventRepeatedPw()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "prevent repeated password");
				itemObj.put(S_FROM, from.isPreventRepeatedPw());
				itemObj.put(S_TO, to.isPreventRepeatedPw());
				changesArr.put(itemObj);
			}
			if(from.getPwHistoryEntries() != to.getPwHistoryEntries()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "password history entries");
				itemObj.put(S_FROM, from.getPwHistoryEntries());
				itemObj.put(S_TO, to.getPwHistoryEntries());
				changesArr.put(itemObj);
			}
			if(from.isPwLimitReusePeriod() != to.isPwLimitReusePeriod()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "limit password reuse period");
				itemObj.put(S_FROM, from.isPwLimitReusePeriod());
				itemObj.put(S_TO, to.isPwLimitReusePeriod());
				changesArr.put(itemObj);
			}
			if(from.getPwLimitMonths() != to.getPwLimitMonths()){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "password reuse period");
				itemObj.put(S_FROM, from.getPwLimitMonths());
				itemObj.put(S_TO, to.getPwLimitMonths());
				changesArr.put(itemObj);
			}
			if(!from.getSessionExpireMins().equals(to.getSessionExpireMins())){
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "session timeout");
				itemObj.put(S_FROM, from.getSessionExpireMins());
				itemObj.put(S_TO, to.getSessionExpireMins());
				changesArr.put(itemObj);
			}
			changesObj.put(S_CHANGES, changesArr);
			return changesObj.toString();
		} catch (JSONException e) {
			return null;
		}

	}

	public static String forReceivingExpiredTranslation(String requestGuid, String filename){
		return "# " + ApplicationUtil.getMessage("page.label.request.GUID") +
				": " + requestGuid + " > # " + ApplicationUtil.getMessage("page.label.file.name") + ": " + filename + " > ";
	}

	public static String forWSFailureNoPermission(){
		try {
			JSONObject changesObj = new JSONObject();
			JSONObject itemObj = new JSONObject();
			itemObj.put(S_LABEL, "No permission");
			JSONArray changesArr = new JSONArray();
			changesArr.put(itemObj);
			changesObj.put(S_CHANGES, changesArr);
			return changesObj.toString();
		} catch (JSONException e) {
			return null;
		}
	}
	
	public static String forProofRun(Proof proof){
		try {
			JSONObject changesObj = new JSONObject();
			JSONObject itemObj = new JSONObject();
			TouchpointSelection variant = proof.getProofDefinition().getTouchpointSelection();
			itemObj.put(S_LABEL, variant.getName());
			JSONArray changesArr = new JSONArray();
			changesArr.put(itemObj);
			changesObj.put(S_CHANGES, changesArr);
			return changesObj.toString();
		} catch (JSONException e) {
			return null;
		}
	}	
	
	public static String forUMHInstanceLicenseChanges(List<MessagepointLicence> licencesList, List<Boolean> accessibleList, List<ApplicationLocale> appLocalesList, LicenceManagementWrapper to){
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			
			for(int i=0; i< to.getLicencesList().size(); i++){
				if((accessibleList.get(i) == null && to.getAccessibleList().get(i) != null) 
						|| (accessibleList.get(i) != null && to.getAccessibleList().get(i) == null) 
						|| (accessibleList.get(i) != null && to.getAccessibleList().get(i) != null && (!accessibleList.get(i).equals(to.getAccessibleList().get(i))))){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, licencesList.get(i).getKey());
					itemObj.put(S_FROM, accessibleList.get(i) == null?ApplicationUtil.getMessage("page.label.none"):(accessibleList.get(i) == true?"Enabled":"Disabled"));
					itemObj.put(S_TO, to.getAccessibleList().get(i) == null?ApplicationUtil.getMessage("page.label.none"):(to.getAccessibleList().get(i) == true?"Enabled":"Disabled"));
					changesArr.put(itemObj);
				}
			}
			for(int i =0; i < to.getAppLocalesList().size();i++){
				if(appLocalesList.get(i).getAccessible() != to.getAppLocalesList().get(i).getAccessible()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage(appLocalesList.get(i).getLocaleDisplayCode()));
					itemObj.put(S_FROM, appLocalesList.get(i).getAccessible() == true?"On":"Off");
					itemObj.put(S_TO, to.getAppLocalesList().get(i).getAccessible() == true?"On":"Off");
					changesArr.put(itemObj);
				}
			}
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			return null;
		}
		
	}
	public static String forUMHBranchLicenseChanges(List<MessagepointLicence> licencesList, List<Boolean> enabledList, List<ApplicationLocale> appLocalesList, LicenceManagementWrapper to){
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			
			for(int i=0; i< to.getLicencesList().size(); i++){
				String keyValue = licencesList.get(i).getKey();
				if(licencesList.get(i).getKey().equals("NumberOfFullUsers")){
					keyValue = "NumberOfRegularUsers";
				}else if(licencesList.get(i).getKey().equals("NumberOfRestrictedUsers")){
					keyValue = "NumberOfWorkFlowUsers";
				}
				if(!Objects.equals(to.getLicencesList().get(i).getAccessible(), licencesList.get(i).getAccessible())){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, keyValue);
					itemObj.put(S_FROM, licencesList.get(i).getAccessible());
					itemObj.put(S_TO, to.getLicencesList().get(i).getAccessible());
					changesArr.put(itemObj);
				}
				if(to.getLicencesList().get(i).isInputField()){
					if(!Objects.equals(to.getLicencesList().get(i).getIntValue(), licencesList.get(i).getIntValue())){
						JSONObject itemObj = new JSONObject();
						itemObj.put(S_LABEL, keyValue.concat(" - Reserved optional*"));
						itemObj.put(S_FROM, licencesList.get(i).getIntValue());
						itemObj.put(S_TO, to.getLicencesList().get(i).getIntValue());
						changesArr.put(itemObj);
					}
				}
				if((enabledList.get(i) == null && to.getEnabledList().get(i) != null) 
						|| (enabledList.get(i) != null && to.getEnabledList().get(i) == null) 
						|| (enabledList.get(i) != null && to.getEnabledList().get(i) != null && (!enabledList.get(i).equals(to.getEnabledList().get(i))))){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, keyValue);
					itemObj.put(S_FROM, enabledList.get(i) == null?ApplicationUtil.getMessage("page.label.none"):(enabledList.get(i) == true?"Enabled":"Disabled"));
					itemObj.put(S_TO, to.getEnabledList().get(i) == null?ApplicationUtil.getMessage("page.label.none"):(to.getEnabledList().get(i) == true?"Enabled":"Disabled"));
					changesArr.put(itemObj);
				}
			}
			for(int i =0; i < to.getAppLocalesList().size();i++){
				if(appLocalesList.get(i).getEnable() != to.getAppLocalesList().get(i).getEnable()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage(appLocalesList.get(i).getLocaleDisplayCode()));
					itemObj.put(S_FROM, appLocalesList.get(i).getEnable() == true?"Enabled":"Disabled");
					itemObj.put(S_TO, to.getAppLocalesList().get(i).getEnable() == true?"Enabled":"Disabled");
					changesArr.put(itemObj);
				}
			}
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			return null;
		}
	}

	public static String forDELicenseChanges(String currentBranchName, LicenceEditWrapper dbCommand, LicenceEditWrapper command) {
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			if(dbCommand == null || dbCommand.getLicence() == null || dbCommand.getLicence().getId() == 0){
				// list all
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.company"));
				itemObj.put(S_FROM, "");
				itemObj.put(S_TO, command.getCompanyName());
				changesArr.put(itemObj);
				
				JSONObject itemObj0 = new JSONObject();
				itemObj0.put(S_LABEL, ApplicationUtil.getMessage("page.label.contact.email"));
				itemObj0.put(S_FROM, "");
				itemObj0.put(S_TO, command.getContractEmail());
				changesArr.put(itemObj0);
				
				JSONObject itemObj1 = new JSONObject();
				itemObj1.put(S_LABEL, ApplicationUtil.getMessage("page.label.realtime"));
				itemObj1.put(S_FROM, "");
				itemObj1.put(S_TO, command.isTransactionalEnabled()?"Enabled":"Disabled");
				changesArr.put(itemObj1);
				
				JSONObject itemObj2 = new JSONObject();
				itemObj2.put(S_LABEL, ApplicationUtil.getMessage("page.label.data.anonymizer"));
				itemObj2.put(S_FROM, "");
				itemObj2.put(S_TO, command.isDataAnonymizerEnabled()?"Enabled":"Disabled");
				changesArr.put(itemObj2);
				
				JSONObject itemObj3 = new JSONObject();
				itemObj3.put(S_LABEL, ApplicationUtil.getMessage("page.label.license.native"));
				itemObj3.put(S_FROM, "");
				itemObj3.put(S_TO, command.isMessagepointCompositionEnabled()?"Enabled":"Disabled");
				changesArr.put(itemObj3);
				
				JSONObject itemObj4 = new JSONObject();
				itemObj4.put(S_LABEL, ApplicationUtil.getMessage("page.label.connected"));
				itemObj4.put(S_FROM, "");
				itemObj4.put(S_TO, command.isConnectedEnabled()?"Enabled":"Disabled");
				changesArr.put(itemObj4);
				
				JSONObject itemObj5 = new JSONObject();
				itemObj5.put(S_LABEL, ApplicationUtil.getMessage("page.label.start.date"));
				itemObj5.put(S_FROM, "");
				itemObj5.put(S_TO, command.getLicence().getStartDate());
				changesArr.put(itemObj5);
				
				JSONObject itemObj6 = new JSONObject();
				itemObj6.put(S_LABEL, ApplicationUtil.getMessage("page.label.end.date"));
				itemObj6.put(S_FROM, "");
				itemObj6.put(S_TO, command.getLicence().getStopDate());
				changesArr.put(itemObj6);

				JSONObject itemObj7 = new JSONObject();
				itemObj7.put(S_LABEL, ApplicationUtil.getMessage("page.label.manager"));
				itemObj7.put(S_FROM, "");
				itemObj7.put(S_TO, command.getLicence().isLicenseManagerEnabled());
				changesArr.put(itemObj7);

				JSONObject itemObj8 = new JSONObject();
				itemObj8.put(S_LABEL, ApplicationUtil.getMessage("page.label.hardware.key.a"));
				itemObj8.put(S_FROM, "");
				itemObj8.put(S_TO, command.getLicence().getLicenseManagerHardwareKeyA());
				changesArr.put(itemObj8);

				JSONObject itemObj9 = new JSONObject();
				itemObj9.put(S_LABEL, ApplicationUtil.getMessage("page.label.hardware.key.b"));
				itemObj9.put(S_FROM, "");
				itemObj9.put(S_TO, command.getLicence().getLicenseManagerHardwareKeyB());
				changesArr.put(itemObj9);

				JSONObject itemObj10 = new JSONObject();
				itemObj10.put(S_LABEL, ApplicationUtil.getMessage("page.label.hardware.key.c"));
				itemObj10.put(S_FROM, "");
				itemObj10.put(S_TO, command.getLicence().getLicenseManagerHardwareKeyC());
				changesArr.put(itemObj10);

				JSONObject itemObj11 = new JSONObject();
				itemObj11.put(S_LABEL, ApplicationUtil.getMessage("page.label.hardware.key.d"));
				itemObj11.put(S_FROM, "");
				itemObj11.put(S_TO, command.getLicence().getLicenseManagerHardwareKeyD());
				changesArr.put(itemObj11);

				JSONObject itemObj12 = new JSONObject();
				itemObj12.put(S_LABEL, ApplicationUtil.getMessage("page.label.hardware.key.e"));
				itemObj12.put(S_FROM, "");
				itemObj12.put(S_TO, command.getLicence().getLicenseManagerHardwareKeyE());
				changesArr.put(itemObj12);

				JSONObject itemObj13 = new JSONObject();
				itemObj13.put(S_LABEL, ApplicationUtil.getMessage("page.label.hardware.key.f"));
				itemObj13.put(S_FROM, "");
				itemObj13.put(S_TO, command.getLicence().getLicenseManagerHardwareKeyF());
				changesArr.put(itemObj13);

			}else{
				if(!dbCommand.getCompanyName().equals(command.getCompanyName())){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.company"));
					itemObj.put(S_FROM, dbCommand.getCompanyName());
					itemObj.put(S_TO, command.getCompanyName());
					changesArr.put(itemObj);
				}
				if(!dbCommand.getContractEmail().equals(command.getContractEmail())){
					JSONObject itemObj0 = new JSONObject();
					itemObj0.put(S_LABEL, ApplicationUtil.getMessage("page.label.contact.email"));
					itemObj0.put(S_FROM, dbCommand.getContractEmail());
					itemObj0.put(S_TO, command.getContractEmail());
					changesArr.put(itemObj0);
				}
				if(dbCommand.isTransactionalEnabled() != command.isTransactionalEnabled()){
					JSONObject itemObj1 = new JSONObject();
					itemObj1.put(S_LABEL, ApplicationUtil.getMessage("page.label.realtime"));
					itemObj1.put(S_FROM, dbCommand.isTransactionalEnabled()?"Enabled":"Disabled");
					itemObj1.put(S_TO, command.isTransactionalEnabled()?"Enabled":"Disabled");
					changesArr.put(itemObj1);
				}
				if(dbCommand.isDataAnonymizerEnabled()!=command.isDataAnonymizerEnabled()){
					JSONObject itemObj2 = new JSONObject();
					itemObj2.put(S_LABEL, ApplicationUtil.getMessage("page.label.data.anonymizer"));
					itemObj2.put(S_FROM, dbCommand.isDataAnonymizerEnabled()?"Enabled":"Disabled");
					itemObj2.put(S_TO, command.isDataAnonymizerEnabled()?"Enabled":"Disabled");
					changesArr.put(itemObj2);
				}
				if(dbCommand.isMessagepointCompositionEnabled()!=command.isMessagepointCompositionEnabled()){
					JSONObject itemObj3 = new JSONObject();
					itemObj3.put(S_LABEL, ApplicationUtil.getMessage("page.label.license.native"));
					itemObj3.put(S_FROM, dbCommand.isMessagepointCompositionEnabled()?"Enabled":"Disabled");
					itemObj3.put(S_TO, command.isMessagepointCompositionEnabled()?"Enabled":"Disabled");
					changesArr.put(itemObj3);
				}
				if(dbCommand.isConnectedEnabled()!=command.isConnectedEnabled()){
					JSONObject itemObj4 = new JSONObject();
					itemObj4.put(S_LABEL, ApplicationUtil.getMessage("page.label.connected"));
					itemObj4.put(S_FROM, dbCommand.isConnectedEnabled()?"Enabled":"Disabled");
					itemObj4.put(S_TO, command.isConnectedEnabled()?"Enabled":"Disabled");
					changesArr.put(itemObj4);
				}
				if(!dbCommand.getStartDate().toString().equals(command.getLicence().getStartDate().toString())){
					JSONObject itemObj5 = new JSONObject();
					itemObj5.put(S_LABEL, ApplicationUtil.getMessage("page.label.start.date"));
					itemObj5.put(S_FROM, DateUtil.formatDate(dbCommand.getStartDate()));
					itemObj5.put(S_TO, DateUtil.formatDate(command.getLicence().getStartDate()));
					changesArr.put(itemObj5);
				}
				if(!dbCommand.getStopDate().toString().equals(command.getLicence().getStopDate().toString())){
					JSONObject itemObj6 = new JSONObject();
					itemObj6.put(S_LABEL, ApplicationUtil.getMessage("page.label.end.date"));
					itemObj6.put(S_FROM, DateUtil.formatDate(dbCommand.getStopDate()));
					itemObj6.put(S_TO, DateUtil.formatDate(command.getLicence().getStopDate()));
					changesArr.put(itemObj6);
				}

				if(dbCommand.isLicenseManagerEnabled() != command.isLicenseManagerEnabled()) {
					JSONObject itemObj7 = new JSONObject();
					itemObj7.put(S_LABEL, ApplicationUtil.getMessage("page.label.manager"));
					itemObj7.put(S_FROM, dbCommand.isLicenseManagerEnabled());
					itemObj7.put(S_TO, command.getLicence().isLicenseManagerEnabled());
					changesArr.put(itemObj7);
				}

				String dbCompareValue = dbCommand.getLicenseManagerHardwareKeyA() != null ? dbCommand.getLicenseManagerHardwareKeyA() : "";
				String newValue = command.getLicence().getLicenseManagerHardwareKeyA() != null ? command.getLicence().getLicenseManagerHardwareKeyA() : "";
				if(!dbCompareValue.equals(newValue)) {
					JSONObject itemObj8 = new JSONObject();
					itemObj8.put(S_LABEL, ApplicationUtil.getMessage("page.label.hardware.key.a"));
					itemObj8.put(S_FROM, dbCompareValue);
					itemObj8.put(S_TO, newValue);
					changesArr.put(itemObj8);
				}

				dbCompareValue = dbCommand.getLicenseManagerHardwareKeyB() != null ? dbCommand.getLicenseManagerHardwareKeyB() : "";
				newValue = command.getLicence().getLicenseManagerHardwareKeyB() != null ? command.getLicence().getLicenseManagerHardwareKeyB() : "";
				if(!dbCompareValue.equals(newValue)) {
					JSONObject itemObj9 = new JSONObject();
					itemObj9.put(S_LABEL, ApplicationUtil.getMessage("page.label.hardware.key.b"));
					itemObj9.put(S_FROM, dbCompareValue);
					itemObj9.put(S_TO, newValue);
					changesArr.put(itemObj9);
				}

				dbCompareValue = dbCommand.getLicenseManagerHardwareKeyC() != null ? dbCommand.getLicenseManagerHardwareKeyC() : "";
				newValue = command.getLicence().getLicenseManagerHardwareKeyC() != null ? command.getLicence().getLicenseManagerHardwareKeyC() : "";
				if(!dbCompareValue.equals(newValue)) {
					JSONObject itemObj10 = new JSONObject();
					itemObj10.put(S_LABEL, ApplicationUtil.getMessage("page.label.hardware.key.c"));
					itemObj10.put(S_FROM, dbCompareValue);
					itemObj10.put(S_TO, command.getLicence().getLicenseManagerHardwareKeyC());
					changesArr.put(itemObj10);
				}

				dbCompareValue = dbCommand.getLicenseManagerHardwareKeyD() != null ? dbCommand.getLicenseManagerHardwareKeyD() : "";
				newValue = command.getLicence().getLicenseManagerHardwareKeyD() != null ? command.getLicence().getLicenseManagerHardwareKeyD() : "";
				if(!dbCompareValue.equals(newValue)) {
					JSONObject itemObj11 = new JSONObject();
					itemObj11.put(S_LABEL, ApplicationUtil.getMessage("page.label.hardware.key.d"));
					itemObj11.put(S_FROM, dbCompareValue);
					itemObj11.put(S_TO, newValue);
					changesArr.put(itemObj11);
				}

				dbCompareValue = dbCommand.getLicenseManagerHardwareKeyE() != null ? dbCommand.getLicenseManagerHardwareKeyE() : "";
				newValue = command.getLicence().getLicenseManagerHardwareKeyE() != null ? command.getLicence().getLicenseManagerHardwareKeyE() : "";
				if(!dbCompareValue.equals(newValue)) {
					JSONObject itemObj12 = new JSONObject();
					itemObj12.put(S_LABEL, ApplicationUtil.getMessage("page.label.hardware.key.e"));
					itemObj12.put(S_FROM, dbCompareValue);
					itemObj12.put(S_TO, newValue);
					changesArr.put(itemObj12);
				}

				dbCompareValue = dbCommand.getLicenseManagerHardwareKeyF() != null ? dbCommand.getLicenseManagerHardwareKeyF() : "";
				newValue = command.getLicence().getLicenseManagerHardwareKeyF() != null ? command.getLicence().getLicenseManagerHardwareKeyF() : "";
				if(!dbCompareValue.equals(newValue)) {
					JSONObject itemObj13 = new JSONObject();
					itemObj13.put(S_LABEL, ApplicationUtil.getMessage("page.label.hardware.key.f"));
					itemObj13.put(S_FROM, dbCompareValue);
					itemObj13.put(S_TO, newValue);
					changesArr.put(itemObj13);
				}


			}
			
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			return null;
		}
	}

	public static String forDELicenseChanges(String currentBranchName, Licence licence) {
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			JSONObject itemObj = new JSONObject();
			String changeStatus = "License deleted for company: " + licence.getCustomer().getName() + " on " + currentBranchName + " domain";
			itemObj.put(S_LABEL, changeStatus);
			changesArr.put(itemObj);
			changesObj.put(S_CHANGES, changesArr);
			return changesObj.toString();
		} catch (JSONException e) {
			return null;
		}
	}
	public static void forTextStylesChanges(JSONArray changesArr, TextStyle originStyle, TextStyle style) throws NumberFormatException, ParseException {
		try {
			if(style.getName().equals(String.valueOf(Long.MIN_VALUE))){
				// Style deleted
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Text Style deleted: " + originStyle.getName());
				changesArr.put(itemObj);
				
			}else if(originStyle != null){
				String changes = "";
				if(originStyle.getName() != null && !originStyle.getName().equals(style.getName())){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.name") + ": " + originStyle.getName() + " to " + style.getName());
				}
				if(((originStyle.getConnectorName() != null && !originStyle.getConnectorName().trim().isEmpty())
						&& (style.getConnectorName() == null || style.getConnectorName().trim().isEmpty()))
						|| (((originStyle.getConnectorName() == null || originStyle.getConnectorName().trim().isEmpty()))
								&& (style.getConnectorName() != null && !style.getConnectorName().trim().isEmpty()))
						|| (originStyle.getConnectorName() != null && style.getConnectorName() != null 
						&& !style.getConnectorName().trim().isEmpty() && !originStyle.getConnectorName().trim().isEmpty()
						&& !originStyle.getConnectorName().equals(style.getConnectorName()))){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.connector.name") + ": " + originStyle.getConnectorName() + " to " + style.getConnectorName());
				}
				if(((originStyle.getColor() != null && !originStyle.getColor().trim().isEmpty())
						&& (style.getColor() == null || style.getColor().trim().isEmpty()))
						|| ((originStyle.getColor() == null || originStyle.getColor().trim().isEmpty())
								&& (style.getColor() != null && !style.getColor().trim().isEmpty()))
						|| (originStyle.getColor() != null && style.getColor() != null 
						&& !style.getColor().trim().isEmpty() && !originStyle.getColor().trim().isEmpty()
						&& !originStyle.getColor().equals(style.getColor()))){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.color") + ": " + originStyle.getColor() + " to " + style.getColor());
				}
				if(((originStyle.getFontName() != null && !originStyle.getFontName().trim().isEmpty())
						&& (style.getFontName() == null || style.getFontName().trim().isEmpty()))
						|| ((originStyle.getFontName() == null || originStyle.getFontName().trim().isEmpty())
								&& (style.getFontName() != null && !style.getFontName().trim().isEmpty()))
						|| (originStyle.getFontName() != null && style.getFontName() != null 
						&& !style.getFontName().trim().isEmpty() && !originStyle.getFontName().trim().isEmpty()
						&& !originStyle.getFontName().equals(style.getFontName()))){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.font.name") + ": " + originStyle.getFontName() + " to " + style.getFontName());
				}
				if(((originStyle.getWebFontName() != null && !originStyle.getWebFontName().trim().isEmpty())
						&& (style.getWebFontName() == null || style.getWebFontName().trim().isEmpty()))
						|| ((originStyle.getWebFontName() == null || originStyle.getWebFontName().trim().isEmpty())
								&& (style.getWebFontName() != null && !style.getWebFontName().trim().isEmpty()))
						|| (originStyle.getWebFontName() != null && style.getWebFontName() != null 
						&& !style.getWebFontName().trim().isEmpty() && !originStyle.getWebFontName().trim().isEmpty()
						&& !originStyle.getWebFontName().equals(style.getWebFontName()))){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.web.font.name") + ": " + originStyle.getWebFontName() + " to " + style.getWebFontName());
				}
				if(originStyle.getTextStyleFont() != originStyle.getTextStyleFont()){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.apply.text.style.font.toggle") + ": " + originStyle.getTextStyleFont() == null?"":originStyle.getTextStyleFont().getName() + " to " + style.getTextStyleFont() == null?"":style.getTextStyleFont().getName());
				
				}
				if(originStyle.isApplyTextStyleFont() != style.isApplyTextStyleFont()){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.apply.text.style.font.toggle") + ": " + originStyle.isApplyTextStyleFont() + " to " + style.isApplyTextStyleFont());
				}
				if(originStyle.getPointSize() != style.getPointSize()){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					
					changes = changes.concat(ApplicationUtil.getMessage("page.label.point.size") + ": " + DecimalValueUtil.dehydrate(originStyle.getPointSize()) + " to " + DecimalValueUtil.dehydrate(style.getPointSize()));
				}
				if(originStyle.isTogglePointSize() != style.isTogglePointSize()){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.point.size.toggle") + ": " + originStyle.isTogglePointSize() + " to " + style.isTogglePointSize());
				}
				if(originStyle.isBold() != style.isBold()){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.bold") + ": " + originStyle.isBold() + " to " + style.isBold());
				}
				if(originStyle.isToggleBold() != style.isToggleBold()){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.bold.toggle") + ": " + originStyle.isToggleBold() + " to " + style.isToggleBold());
				}
				if(originStyle.isItalic() != style.isItalic()){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.italic") + ": " + originStyle.isItalic() + " to " + style.isItalic());
				}
				if(originStyle.isToggleItalic() != style.isToggleItalic()){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.italic.toggle") + ": " + originStyle.isToggleItalic() + " to " + style.isToggleItalic());
				}
				if(originStyle.isUnderline() != style.isUnderline()){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.underline") + ": " + originStyle.isUnderline() + " to " + style.isUnderline());
				}
				if(originStyle.isToggleUnderline() != style.isToggleUnderline()){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.underline.toggle") + ": " + originStyle.isToggleUnderline() + " to " + style.isToggleUnderline());
				}
				if(originStyle.isToggleColor() != style.isToggleColor()){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.color.toggle") + ": " + originStyle.isToggleColor() + " to " + style.isToggleColor());
				}
				if(((originStyle.getTaggingOverride() != null && !originStyle.getTaggingOverride().trim().isEmpty())
						&& (style.getTaggingOverride() == null || style.getTaggingOverride().trim().isEmpty()))
						|| ((originStyle.getTaggingOverride() == null || originStyle.getTaggingOverride().trim().isEmpty())
								&& (style.getTaggingOverride() != null && !style.getTaggingOverride().trim().isEmpty()))
						|| (originStyle.getTaggingOverride() != null && style.getTaggingOverride() != null 
						&& !style.getTaggingOverride().trim().isEmpty() && !originStyle.getTaggingOverride().trim().isEmpty()
						&& !originStyle.getTaggingOverride().equals(style.getTaggingOverride()))){
					if(!changes.isEmpty())
						changes = changes.concat(",");
					changes = changes.concat(ApplicationUtil.getMessage("page.label.tagging.override") + ": " + originStyle.getTaggingOverride() + " to " + style.getTaggingOverride());
				}
				if(!changes.isEmpty()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, style.getName() + " changed - " + changes);
					changesArr.put(itemObj);
				}
			}
		} catch (JSONException e) {
			//
		}
	}

	public static String forContentChanges(ContentObjectAssociation ca, long fromHcaId, long toHcaId){
		StringBuilder changes = new StringBuilder();
		changes.append("# ").append(ApplicationUtil.getMessage("page.label.content"));
		ContentObject contentObject = ca.getContentObject();
		if(ca.getMessagepointLocale() != null){
			changes.append(" (").append(ca.getMessagepointLocale().getLanguageName());
			if(ca.getPGTreeNode() != null){
				String fullpath = ParameterGroupTreeNode.getFullPath(ca.getPGTreeNode());
				if(org.apache.commons.lang3.StringUtils.isNotEmpty(fullpath)) {
					if (fullpath.startsWith("/")) {
						fullpath = fullpath.substring(1);
					}
					fullpath = fullpath.replace("/", " > ");
				}
				changes.append(", ").append(fullpath);
			}

			if(ca.getZonePart() != null){
				changes.append(", ").append(ca.getZonePart().getName());
			}

			changes.append(")");
		}

		String fromHcaStr = fromHcaId>0?("hcaid@" + fromHcaId):"";
		String toHcaStr = toHcaId>0?("hcaid@" + toHcaId):"";
		changes.append(": ").append(fromHcaStr).append(" > ").append(toHcaStr);

		return changes.toString();
	}

	public static String forJaversDiff(Diff diffs){
		StringBuilder changes = new StringBuilder();
		for(PropertyChange change: diffs.getChangesByType(PropertyChange.class)){
			changes.append("# ").append(change.getPropertyName()).append(": ").append(change.getLeft()).append(" > ").append(change.getRight());
		}

		return changes.toString();
	}

	public static String forDataSourceChanges(DataSourceVO oldDataSource, UpdateDataSourceServiceRequest request) {
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			DataSource updatedDS = request.getDataSource();
			if(oldDataSource == null){
				// new
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "New Data Source added: " + updatedDS.getName());
				changesArr.put(itemObj);
			}else{
				if(!updatedDS.getName().equals(oldDataSource.getName())){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.name"));
					itemObj.put(S_FROM, oldDataSource.getName());
					itemObj.put(S_TO, updatedDS.getName());
					changesArr.put(itemObj);
				}
				if(!updatedDS.getEncodingType().getDisplayName().equals(oldDataSource.getEncodingType().getDisplayName())){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.encoding.type"));
					itemObj.put(S_FROM, oldDataSource.getEncodingType().getDisplayName());
					itemObj.put(S_TO, updatedDS.getEncodingType().getDisplayName());
					changesArr.put(itemObj);
				}
				if(!oldDataSource.getSourceType().getName().equals(updatedDS.getSourceType().getName())){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.source.type"));
					itemObj.put(S_FROM, oldDataSource.getSourceType().getName());
					itemObj.put(S_TO, updatedDS.getSourceType().getName());
					changesArr.put(itemObj);
				}
				String oldID = "";
				String newID = "";
				if(oldDataSource.getExternalId() != null){
					oldID = oldDataSource.getExternalId();
				}
				if(updatedDS.getExternalId() != null){
					newID = updatedDS.getExternalId();
				}
				if(!oldID.equals(newID) ){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.externalID"));
					itemObj.put(S_FROM, oldID);
					itemObj.put(S_TO, newID);
					changesArr.put(itemObj);
				}
				if(!oldDataSource.getLayoutType().getDisplayName().equals(updatedDS.getLayoutType().getDisplayName())){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.layouttype"));
					itemObj.put(S_FROM, oldDataSource.getLayoutType().getDisplayName());
					itemObj.put(S_TO, updatedDS.getLayoutType().getDisplayName());
					changesArr.put(itemObj);
				}
				String oldDelimiter = "";
				String newDelimiter = "";
				if(oldDataSource.getDelimeter() != null){
					oldDelimiter = oldDataSource.getDelimeter();
				}
				if(updatedDS.getDelimeter() != null){
					newDelimiter = updatedDS.getDelimeter();
				}
				if(!oldDelimiter.equals(newDelimiter)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.delimiter"));
					itemObj.put(S_FROM, oldDelimiter);
					itemObj.put(S_TO, newDelimiter);
					changesArr.put(itemObj);
				}
				if(!oldDataSource.getRecordType().getName().equals(updatedDS.getRecordType().getName())){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.record.type"));
					itemObj.put(S_FROM, oldDataSource.getRecordType().getName());
					itemObj.put(S_TO, updatedDS.getRecordType().getName());
					changesArr.put(itemObj);
				}
				if(oldDataSource.getHeaders() != updatedDS.getHeaders()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.header.records"));
					itemObj.put(S_FROM, oldDataSource.getHeaders());
					itemObj.put(S_TO, updatedDS.getHeaders());
					changesArr.put(itemObj);
				}
				if(updatedDS.getLayoutType().isDelimited()){
					if(oldDataSource.getDelimIndicatorList().size() != updatedDS.getDelimitedIndicators().size()){
						JSONObject itemObj = new JSONObject();
						itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.indicators.changed"));
						itemObj.put(S_FROM, oldDataSource.getDelimIndicatorList().size());
						itemObj.put(S_TO, updatedDS.getDelimitedIndicators().size());
						changesArr.put(itemObj);
					}
					int delCompareNumber = oldDataSource.getDelimIndicatorList().size()  <= updatedDS.getDelimitedIndicators().size()?oldDataSource.getDelimIndicatorList().size():updatedDS.getDelimitedIndicators().size();
					for(int i = 0; i < delCompareNumber; i++){
						int oldLocation = 0;
						if(oldDataSource.getDelimIndicatorList().get(i) != null){
							oldLocation = Integer.valueOf(oldDataSource.getDelimIndicatorList().get(i));
						}
						if(oldLocation != updatedDS.getDelimitedIndicators().get(i)){
							JSONObject itemObj = new JSONObject();
							itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.indicator.location"));
							itemObj.put(S_FROM, oldDataSource.getDelimIndicatorList().get(i) == null? ApplicationUtil.getMessage("page.label.none"): oldLocation);
							itemObj.put(S_TO, updatedDS.getDelimitedIndicators().get(i));
							changesArr.put(itemObj);
						}
					}
				}
				if(updatedDS.getLayoutType().isColumnar()){
					if(oldDataSource.getColumnIndicatorList().size() != updatedDS.getColumnarIndicators().size()){
						JSONObject itemObj = new JSONObject();
						itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.indicators.changed"));
						itemObj.put(S_FROM, oldDataSource.getColumnIndicatorList().size());
						itemObj.put(S_TO, updatedDS.getColumnarIndicators().size());
						changesArr.put(itemObj);
					}
					int compareNumber = oldDataSource.getColumnIndicatorList().size()  <= updatedDS.getColumnarIndicators().size()?oldDataSource.getColumnIndicatorList().size():updatedDS.getColumnarIndicators().size();
					for(int i = 0; i < compareNumber; i++){
						int oldLocation = 0;
						if(oldDataSource.getColumnIndicatorList().get(i) != null){
							oldLocation = Integer.valueOf(oldDataSource.getColumnIndicatorList().get(i));
						}
						if(oldLocation != updatedDS.getColumnarIndicators().get(i).getIndicatorStart()){
							JSONObject itemObj = new JSONObject();
							itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.indicator.location"));
							itemObj.put(S_FROM, oldDataSource.getColumnIndicatorList().get(i) == null? ApplicationUtil.getMessage("page.label.none"): oldLocation);
							itemObj.put(S_TO, updatedDS.getColumnarIndicators().get(i).getIndicatorStart());
							changesArr.put(itemObj);
						}
						int oldLength = 0;
						if(oldDataSource.getColumnIndLengthList().get(i) != null){
							oldLength = Integer.valueOf(oldDataSource.getColumnIndLengthList().get(i));
						}
						if( oldLength != updatedDS.getColumnarIndicators().get(i).getIndicatorLength()){
							JSONObject itemObj = new JSONObject();
							itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.length"));
							itemObj.put(S_FROM, oldDataSource.getColumnIndLengthList().get(i) == null? ApplicationUtil.getMessage("page.label.none"): oldLength);
							itemObj.put(S_TO, updatedDS.getColumnarIndicators().get(i).getIndicatorLength());
							changesArr.put(itemObj);
						}
					}
				}
			}
			
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}
		
		return null;
	}

	public static String forDataRecordChanges(DataRecordVO oldDataRecordVO, Command command) {
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			DataRecord updatedDR = command.getDataRecord();
			if(oldDataRecordVO == null || updatedDR.getId() == 0){
				// new
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "New DataRecord added: " + updatedDR.getName());
				changesArr.put(itemObj);
			}else{
				if(oldDataRecordVO.getRecordPosition() != updatedDR.getRecordPosition()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.position"));
					itemObj.put(S_FROM, oldDataRecordVO.getRecordPosition());
					itemObj.put(S_TO, updatedDR.getRecordPosition());
					changesArr.put(itemObj);
				}
				
				String oldIndicator = "";
				String newIndicator = "";
				if(oldDataRecordVO.getRecordIndicator() != null){
					oldIndicator = oldDataRecordVO.getRecordIndicator();
				}
				if(updatedDR.getRecordIndicator() != null){
					newIndicator = updatedDR.getRecordIndicator();
				}
				if(!oldIndicator.equals(newIndicator)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.recordindicator"));
					itemObj.put(S_FROM, oldIndicator);
					itemObj.put(S_TO, newIndicator);
					changesArr.put(itemObj);
				}
				
				String oldBreakIndicator = "";
				String newBreakIndicator = "";
				if(oldDataRecordVO.getBreakIndicator() != null){
					oldBreakIndicator = oldDataRecordVO.getBreakIndicator();
				}
				if(updatedDR.getBreakIndicator() != null){
					newBreakIndicator = updatedDR.getBreakIndicator();
				}
				if(!oldBreakIndicator.equals(newBreakIndicator)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.breakindicator"));
					itemObj.put(S_FROM, oldBreakIndicator);
					itemObj.put(S_TO, newBreakIndicator);
					changesArr.put(itemObj);
				}
				
				if(oldDataRecordVO.isStartDataGoup() != command.isStartDataGroup()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.start.data.group"));
					itemObj.put(S_FROM, oldDataRecordVO.isStartDataGoup());
					itemObj.put(S_TO, command.isStartDataGroup());
					changesArr.put(itemObj);
				}
				if(oldDataRecordVO.getDataGroupId() != command.getDataGroupId()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.data.groups"));
					DataGroup oldDg = DataGroup.findById(oldDataRecordVO.getDataGroupId());
					itemObj.put(S_FROM, oldDg != null? oldDg.getName():ApplicationUtil.getMessage("page.label.none"));
					DataGroup newDg = DataGroup.findById(command.getDataGroupId());
					itemObj.put(S_TO, newDg != null? newDg.getName():ApplicationUtil.getMessage("page.label.none"));
					changesArr.put(itemObj);
				}
				if(oldDataRecordVO.isEnabled() != updatedDR.isEnabled()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.enabled"));
					itemObj.put(S_FROM, oldDataRecordVO.isEnabled());
					itemObj.put(S_TO, updatedDR.isEnabled());
					changesArr.put(itemObj);
				}
				if(oldDataRecordVO.isStartCustomer() != updatedDR.isStartCustomer()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.startcustomer"));
					itemObj.put(S_FROM, oldDataRecordVO.isStartCustomer());
					itemObj.put(S_TO, updatedDR.isStartCustomer());
					changesArr.put(itemObj);
				}
				if(oldDataRecordVO.isRepeating() != updatedDR.isRepeating()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.repeatingrecord"));
					itemObj.put(S_FROM, oldDataRecordVO.isRepeating());
					itemObj.put(S_TO, updatedDR.isRepeating());
					changesArr.put(itemObj);
				}
			}
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}
		
		return null;
	}

	public static String forDataElementChanges(DataElementVO elementVO, DataElement dataElement) {
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			
			if(elementVO == null || dataElement.getId() == 0){
				// new
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "New DataElement added: " + dataElement.getName());
				changesArr.put(itemObj);
			}else{
				
				String oldName = "";
				String newName = "";
				if(elementVO.getName() != null){
					oldName = elementVO.getName();
				}
				if(dataElement.getName() != null){
					newName = dataElement.getName();
				}
				if(!oldName.equals(newName)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.name"));
					itemObj.put(S_FROM, oldName);
					itemObj.put(S_TO, newName);
					changesArr.put(itemObj);
				}
				if(elementVO.getLength() != dataElement.getLength()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.length"));
					itemObj.put(S_FROM, elementVO.getLength());
					itemObj.put(S_TO, dataElement.getLength());
					changesArr.put(itemObj);
				}
				if(elementVO.getStartLocation() != dataElement.getStartLocation()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.location"));
					itemObj.put(S_FROM, elementVO.getStartLocation());
					itemObj.put(S_TO, dataElement.getStartLocation());
					changesArr.put(itemObj);
				}
				String oldExternalFormatText = "";
				String newExternalFormatText = "";
				if(elementVO.getExternalFormatText() != null){
					oldExternalFormatText = elementVO.getExternalFormatText();
				}
				if(dataElement.getExternalFormatText() != null){
					newExternalFormatText = dataElement.getExternalFormatText();
				}
				if(!oldExternalFormatText.equals(newExternalFormatText)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.input.formatting"));
					itemObj.put(S_FROM, oldExternalFormatText);
					itemObj.put(S_TO, newExternalFormatText);
					changesArr.put(itemObj);
				}
				
				if(elementVO.getDecimalPlaces() != dataElement.getDecimalPlaces()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.implied.decimal.places"));
					itemObj.put(S_FROM, elementVO.getDecimalPlaces());
					itemObj.put(S_TO, dataElement.getDecimalPlaces());
					changesArr.put(itemObj);
				}
//				if(elementVO.getDataTypeId() != dataElement.getDataTypeId()){
//					JSONObject itemObj = new JSONObject();
//					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.datatype"));
//					itemObj.put(S_FROM, elementVO.getDataTypeId());
//					itemObj.put(S_TO, dataElement.getDataTypeId());
//					changesArr.put(itemObj);
//				}
				if(elementVO.getDataSubtypeId() != dataElement.getDataSubtypeId()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.datatype"));
					DataSubtype oldType = DataSubtype.findById(elementVO.getDataSubtypeId());
					itemObj.put(S_FROM, oldType != null? ApplicationUtil.getMessage(oldType.getName()):"");
					DataSubtype newType = DataSubtype.findById(dataElement.getDataSubtypeId());
					itemObj.put(S_TO, newType != null? ApplicationUtil.getMessage(newType.getName()):"");
					changesArr.put(itemObj);
				}
				if(elementVO.getAnonymized() != dataElement.getAnonymized()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.anonymized"));
					itemObj.put(S_FROM, elementVO.getAnonymized());
					itemObj.put(S_TO, dataElement.getAnonymized());
					changesArr.put(itemObj);
				}
				if(dataElement.getAnonymized()){
					if(elementVO.getAnonymizationTypeId() != dataElement.getAnonymizationTypeId()){
						JSONObject itemObj = new JSONObject();
						itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.mask"));
						itemObj.put(S_FROM, elementVO.getAnonymizationTypeId());
						itemObj.put(S_TO, dataElement.getAnonymizationTypeId());
						changesArr.put(itemObj);
					}
					if(!Objects.equals(elementVO.getAnonymizationCustomMasks(), dataElement.getAnonymizationCustomMasks())){
						JSONObject itemObj = new JSONObject();
						itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.custom.masks"));
						itemObj.put(S_FROM, elementVO.getAnonymizationCustomMasks());
						itemObj.put(S_TO, dataElement.getAnonymizationCustomMasks());
						changesArr.put(itemObj);
					}
				}
			}
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}
		
		return null;
	}

	public static String forXMLDataTagChanges(DataTagVO tagVO, XmlDataTagDefinition xmlTag) {
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			
			if(tagVO == null || xmlTag.getId() == 0 ){
				// new
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "New Tag added: " + xmlTag.getName());
				changesArr.put(itemObj);
			}else{
				
				String oldName = tagVO.getName() != null? tagVO.getName():"";
				String newName = xmlTag.getName() != null? xmlTag.getName():"";
				if(!oldName.equals(newName)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.name"));
					itemObj.put(S_FROM, oldName);
					itemObj.put(S_TO, newName);
					changesArr.put(itemObj);
				}
				if(tagVO.isStartCustomer() != xmlTag.isStartCustomer()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.startcustomer"));
					itemObj.put(S_FROM, tagVO.isStartCustomer());
					itemObj.put(S_TO, xmlTag.isStartCustomer());
					changesArr.put(itemObj);
				}
				if(tagVO.isStartDataGroup() != xmlTag.isStartDataGroup()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.xml.tag.start.data.group"));
					itemObj.put(S_FROM, tagVO.isStartDataGroup());
					itemObj.put(S_TO, xmlTag.isStartDataGroup());
					changesArr.put(itemObj);
				}
				String oldBreakIndicator = tagVO.getBreakIndicator() != null? tagVO.getBreakIndicator():"";
				String newBreakIndicator = xmlTag.getBreakIndicator() != null? xmlTag.getBreakIndicator():"";
				if(!oldBreakIndicator.equals(newBreakIndicator)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.xml.tag.break.indicator"));
					itemObj.put(S_FROM, oldBreakIndicator);
					itemObj.put(S_TO, newBreakIndicator);
					changesArr.put(itemObj);
				}
				long dgID = xmlTag.getDataGroup() != null? xmlTag.getDataGroup().getId():0;
				if( tagVO.getDataGroupId() != dgID){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.data.group"));
					DataGroup oldDG = DataGroup.findById(tagVO.getDataGroupId());
					itemObj.put(S_FROM, oldDG != null? oldDG.getName():ApplicationUtil.getMessage("page.label.none"));
					DataGroup newDG = DataGroup.findById(dgID);
					itemObj.put(S_TO, newDG != null? newDG.getName():ApplicationUtil.getMessage("page.label.none"));
					changesArr.put(itemObj);
				}
				long newParentId = xmlTag.getParentTag() != null? xmlTag.getParentTag().getId():0;
				if(tagVO.getParentTagId() != newParentId){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.parent"));
					XmlDataTagDefinition orgPTag = tagVO.getParentTagId() == 0? null:XmlDataTagDefinition.findById(tagVO.getParentTagId());
					itemObj.put(S_FROM, orgPTag == null?ApplicationUtil.getMessage("page.label.none"): orgPTag.getName());
					itemObj.put(S_TO, newParentId == 0? ApplicationUtil.getMessage("page.label.none"): xmlTag.getParentTag().getName());
					changesArr.put(itemObj);
				}
				if(tagVO.isRepeating() != xmlTag.isRepeating()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.repeatingrecord"));
					itemObj.put(S_FROM, tagVO.isRepeating());
					itemObj.put(S_TO, xmlTag.isRepeating());
					changesArr.put(itemObj);
				}
				
			}
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}
		
		return null;
	}

	public static String forXMLDataElementChanges(XmlDataElementVO elementVO,
			UpdateXmlDataElementServiceRequest serviceRequest) {
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			XmlDataElement dataElement = serviceRequest.getXmlDataElement();
			if(elementVO == null || (elementVO != null && elementVO.getId() == 0)){
				// new
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "New DataElement added: " + dataElement.getName());
				changesArr.put(itemObj);
			}else{
				
				String oldName = "";
				String newName = "";
				if(elementVO.getName() != null){
					oldName = elementVO.getName();
				}
				if(dataElement.getName() != null){
					newName = dataElement.getName();
				}
				if(!oldName.equals(newName)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.name"));
					itemObj.put(S_FROM, oldName);
					itemObj.put(S_TO, newName);
					changesArr.put(itemObj);
				}
				if(elementVO.isAttribute() != dataElement.getIsAttribute()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.length"));
					itemObj.put(S_FROM, elementVO.isAttribute());
					itemObj.put(S_TO, dataElement.getIsAttribute());
					changesArr.put(itemObj);
				}
				String oldExternalFormatText = "";
				String newExternalFormatText = "";
				if(elementVO.getExternalFormatText() != null){
					oldExternalFormatText = elementVO.getExternalFormatText();
				}
				if(dataElement.getExternalFormatText() != null){
					newExternalFormatText = dataElement.getExternalFormatText();
				}
				if(!oldExternalFormatText.equals(newExternalFormatText)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.input.formatting"));
					itemObj.put(S_FROM, oldExternalFormatText);
					itemObj.put(S_TO, newExternalFormatText);
					changesArr.put(itemObj);
				}
				
				if(elementVO.getDecimalPlaces() != dataElement.getDecimalPlaces()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.implied.decimal.places"));
					itemObj.put(S_FROM, elementVO.getDecimalPlaces());
					itemObj.put(S_TO, dataElement.getDecimalPlaces());
					changesArr.put(itemObj);
				}
				if(elementVO.getDataSubtypeId() != dataElement.getDataSubtypeId()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.datatype"));
					DataSubtype oldType = DataSubtype.findById(elementVO.getDataSubtypeId());
					itemObj.put(S_FROM, oldType != null? ApplicationUtil.getMessage(oldType.getName()):"");
					DataSubtype newType = DataSubtype.findById(dataElement.getDataSubtypeId());
					itemObj.put(S_TO, newType != null? ApplicationUtil.getMessage(newType.getName()):"");
					changesArr.put(itemObj);
				}
				if(elementVO.getAnonymized() != dataElement.getAnonymized()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.anonymized"));
					itemObj.put(S_FROM, elementVO.getAnonymized());
					itemObj.put(S_TO, dataElement.getAnonymized());
					changesArr.put(itemObj);
				}
				if(elementVO.getAnonymizationTypeId() != dataElement.getAnonymizationTypeId()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.mask"));
					itemObj.put(S_FROM, elementVO.getAnonymizationTypeId());
					itemObj.put(S_TO, dataElement.getAnonymizationTypeId());
					changesArr.put(itemObj);
				}
			}
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}
		
		return null;
	}

	public static String forXMLDataTagUploadChanges(String msg) {
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			JSONObject itemObj = new JSONObject();
			itemObj.put(S_LABEL, msg);
			changesArr.put(itemObj);
			changesObj.put(S_CHANGES, changesArr);
			return changesObj.toString();
		
		} catch (JSONException e) {
			//
		}
		
		return null;
	}

	public static String forJSONDataDefinitionChanges(DataDefinitionVO definitionVO, JSONDataDefinition definition) {
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();

			if(definitionVO == null || definition.getId() == 0 ){
				// new
				JSONObject itemObj = new JSONObject();
				if(definition.getDefinitionType() == JSONDefinitionType.ID_KEY){
					itemObj.put(S_LABEL, "New JSON Key added: " + definition.getName());
				}else{
					itemObj.put(S_LABEL, "New JSON Definition added: " + definition.getName());
				}
				changesArr.put(itemObj);
			}else{
				String oldName = definitionVO.getName() != null? definitionVO.getName():"";
				String newName = definition.getName() != null? definition.getName():"";
				if(!oldName.equals(newName)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.name"));
					itemObj.put(S_FROM, oldName);
					itemObj.put(S_TO, newName);
					changesArr.put(itemObj);
				}
				if(definitionVO.isStartCustomer() != definition.isStartCustomer()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.startcustomer"));
					itemObj.put(S_FROM, definitionVO.isStartCustomer());
					itemObj.put(S_TO, definition.isStartCustomer());
					changesArr.put(itemObj);
				}
				if(definitionVO.isStartDataGroup() != definition.isStartDataGroup()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.xml.tag.start.data.group"));
					itemObj.put(S_FROM, definitionVO.isStartDataGroup());
					itemObj.put(S_TO, definition.isStartDataGroup());
					changesArr.put(itemObj);
				}
				String oldBreakIndicator = definitionVO.getBreakIndicator() != null? definitionVO.getBreakIndicator():"";
				String newBreakIndicator = definition.getBreakIndicator() != null? definition.getBreakIndicator():"";
				if(!oldBreakIndicator.equals(newBreakIndicator)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.xml.tag.break.indicator"));
					itemObj.put(S_FROM, oldBreakIndicator);
					itemObj.put(S_TO, newBreakIndicator);
					changesArr.put(itemObj);
				}
				long dgID = definition.getDataGroup() != null? definition.getDataGroup().getId():0;
				if( definitionVO.getDataGroupId() != dgID){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.data.group"));
					DataGroup oldDG = DataGroup.findById(definitionVO.getDataGroupId());
					itemObj.put(S_FROM, oldDG != null? oldDG.getName():ApplicationUtil.getMessage("page.label.none"));
					DataGroup newDG = DataGroup.findById(dgID);
					itemObj.put(S_TO, newDG != null? newDG.getName():ApplicationUtil.getMessage("page.label.none"));
					changesArr.put(itemObj);
				}
				long newParentId = definition.getParentDefinition() != null? definition.getParentDefinition().getId():0;
				if(definitionVO.getParentDefinitionId() != newParentId){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.parent"));
					JSONDataDefinition orgPTag = definitionVO.getParentDefinitionId() == 0? null:JSONDataDefinition.findById(definitionVO.getParentDefinitionId());
					itemObj.put(S_FROM, orgPTag == null?ApplicationUtil.getMessage("page.label.none"): orgPTag.getName());
					itemObj.put(S_TO, newParentId == 0? ApplicationUtil.getMessage("page.label.none"): definition.getParentDefinition().getName());
					changesArr.put(itemObj);
				}
				if(definitionVO.isRepeating() != definition.isRepeating()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.repeatingrecord"));
					itemObj.put(S_FROM, definitionVO.isRepeating());
					itemObj.put(S_TO, definition.isRepeating());
					changesArr.put(itemObj);
				}
			}
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}

		return null;
	}

	public static String forJSONDataElementChanges(JSONDataElementVO elementVO,
												  UpdateJSONDataElementServiceRequest serviceRequest) {
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			JSONDataElement dataElement = serviceRequest.getJsonDataElement();
			if(elementVO == null || (elementVO != null && elementVO.getId() == 0)){
				// new
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "New DataElement added: " + dataElement.getName());
				changesArr.put(itemObj);
			}else{

				String oldName = "";
				String newName = "";
				if(elementVO.getName() != null){
					oldName = elementVO.getName();
				}
				if(dataElement.getName() != null){
					newName = dataElement.getName();
				}
				if(!oldName.equals(newName)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.name"));
					itemObj.put(S_FROM, oldName);
					itemObj.put(S_TO, newName);
					changesArr.put(itemObj);
				}
				String oldExternalFormatText = "";
				String newExternalFormatText = "";
				if(elementVO.getExternalFormatText() != null){
					oldExternalFormatText = elementVO.getExternalFormatText();
				}
				if(dataElement.getExternalFormatText() != null){
					newExternalFormatText = dataElement.getExternalFormatText();
				}
				if(!oldExternalFormatText.equals(newExternalFormatText)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.input.formatting"));
					itemObj.put(S_FROM, oldExternalFormatText);
					itemObj.put(S_TO, newExternalFormatText);
					changesArr.put(itemObj);
				}

				if(elementVO.getDecimalPlaces() != dataElement.getDecimalPlaces()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.implied.decimal.places"));
					itemObj.put(S_FROM, elementVO.getDecimalPlaces());
					itemObj.put(S_TO, dataElement.getDecimalPlaces());
					changesArr.put(itemObj);
				}
				if(elementVO.getDataSubtypeId() != dataElement.getDataSubtypeId()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.datatype"));
					DataSubtype oldType = DataSubtype.findById(elementVO.getDataSubtypeId());
					itemObj.put(S_FROM, oldType != null? ApplicationUtil.getMessage(oldType.getName()):"");
					DataSubtype newType = DataSubtype.findById(dataElement.getDataSubtypeId());
					itemObj.put(S_TO, newType != null? ApplicationUtil.getMessage(newType.getName()):"");
					changesArr.put(itemObj);
				}
				if(elementVO.getAnonymized() != dataElement.getAnonymized()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.anonymized"));
					itemObj.put(S_FROM, elementVO.getAnonymized());
					itemObj.put(S_TO, dataElement.getAnonymized());
					changesArr.put(itemObj);
				}
				if(elementVO.getAnonymizationTypeId() != dataElement.getAnonymizationTypeId()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.mask"));
					itemObj.put(S_FROM, elementVO.getAnonymizationTypeId());
					itemObj.put(S_TO, dataElement.getAnonymizationTypeId());
					changesArr.put(itemObj);
				}
			}
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}

		return null;
	}

	public static String forUserEditChanges(UserProxy userProxy) {
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			if(userProxy instanceof NewUserProxy){
				// new
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "New user added: " + userProxy.getUser().getName());
				changesArr.put(itemObj);
			}else{
				User user = userProxy.getUser();
				User originalUser = userProxy.getOriginalUser();
				if(userProxy.isUserIdChanged()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.username"));
					itemObj.put(S_FROM, originalUser.getUsername());
					itemObj.put(S_TO, user.getUsername());
					changesArr.put(itemObj);
				}
				if(userProxy.isEmailChanged()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.email"));
					itemObj.put(S_FROM, originalUser.getEmail());
					itemObj.put(S_TO, user.getEmail());
					changesArr.put(itemObj);
				}
//				if(userProxy.isEmailNotificationChanged()){
//					JSONObject itemObj = new JSONObject();
//					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.username"));
//					itemObj.put(S_FROM, originalUser.isEmailNotifyRealTime()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
//					itemObj.put(S_TO, user.isEmailNotifyRealTime()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
//					changesArr.put(itemObj);
//				}
				if(userProxy.isIdpTypeChanged()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.is.sso.user"));
					itemObj.put(S_FROM, originalUser.getIdPTypeDisplayName());
					itemObj.put(S_TO, user.getIdPTypeDisplayName());
					changesArr.put(itemObj);
				}
				if( compareString(originalUser.getFirstName(), user.getFirstName(), true) != null){
					JSONObject itemObj = compareString(originalUser.getFirstName(), user.getFirstName(),true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.firstname")); // set label
					changesArr.put(itemObj);
				}
				if( compareString(originalUser.getLastName(), user.getLastName(), true) != null){
					JSONObject itemObj = compareString(originalUser.getLastName(), user.getLastName(),true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.lastname")); // set label
					changesArr.put(itemObj);
				}
				if( compareString(originalUser.getNotes(), user.getNotes(), true) != null){
					JSONObject itemObj = compareString(originalUser.getNotes(), user.getNotes(),true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.notes")); // set label
					changesArr.put(itemObj);
				}

			}
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}

		return null;
	}
	
	public static String forDataCollectionChanges(DataSourceAssociationVO oldDSAassociationVO, Long associationId) {
		try {
			DataSourceAssociation dataSourceAssociation = HibernateUtil.getManager().getObject(DataSourceAssociation.class, associationId);
			if(dataSourceAssociation == null)
				return null;
		
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			if(oldDSAassociationVO == null){
				// new
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "New Data Collection added: " + dataSourceAssociation.getName());
				changesArr.put(itemObj);
				
			}else{
				String oldName = "";
				String newName = "";
				if(oldDSAassociationVO.getName() != null){
					oldName = oldDSAassociationVO.getName();
				}
				if(dataSourceAssociation.getName() != null){
					newName = dataSourceAssociation.getName();
				}
				if(!oldName.equals(newName)){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.name"));
					itemObj.put(S_FROM, oldName);
					itemObj.put(S_TO, newName);
					changesArr.put(itemObj);
				}
				if(oldDSAassociationVO.getPrimaryDataSource() != dataSourceAssociation.getPrimaryDataSource()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.primary.data.source"));
					itemObj.put(S_FROM, oldDSAassociationVO.getPrimaryDataSource().getName());
					itemObj.put(S_TO, dataSourceAssociation.getPrimaryDataSource().getName());
					changesArr.put(itemObj);
				}
				if( compareString(oldDSAassociationVO.getCustomerDataElementName(), dataSourceAssociation.getCustomerDataElementId()!= null?dataSourceAssociation.getCustomerDataElementId().getName():null) != null){
					JSONObject itemObj = compareString(oldDSAassociationVO.getCustomerDataElementName(), dataSourceAssociation.getCustomerDataElementId()!= null?dataSourceAssociation.getCustomerDataElementId().getName():null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.customer.primary.key"));
					changesArr.put(itemObj);
				}

				Collection<ReferenceConnection> listOne = oldDSAassociationVO.getOriginalReferenceConnections();
				Collection<ReferenceConnection> listTwo = dataSourceAssociation.getReferenceConnections();
				Collection<ReferenceConnection> similar = new HashSet<>(listOne);
				Collection<ReferenceConnection> different = new HashSet<>();
				different.addAll( listOne );
		        different.addAll( listTwo );
		        similar.retainAll( listTwo );
		        different.removeAll( similar );
		        if(!similar.isEmpty()){
		        	// Check if Reference Data setting changed
		        	for(ReferenceConnection refConn:similar){
		        		ReferenceConnectionVO one = getVOFrom(refConn, oldDSAassociationVO.getReferenceConnectionVOs());
		        		ReferenceConnection two = getFrom(refConn, dataSourceAssociation.getReferenceConnections());
		        		String oldDsName = "";
						String newDsName = "";
						if(one.getReferenceDataSource().getName() != null){
							oldDsName = one.getReferenceDataSource().getName();
						}
						if(two.getReferenceDataSource().getName() != null){
							newDsName = two.getReferenceDataSource().getName();
						}
						if(!oldDsName.equals(newDsName)){
							JSONObject itemObj = new JSONObject();
							itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.reference.data.source"));
							itemObj.put(S_FROM, oldDsName);
							itemObj.put(S_TO, newDsName);
							changesArr.put(itemObj);
						}
		        		if(two.getReferenceVariable() != null && one.getReferenceVariableId() != two.getReferenceVariable().getId()){
		        			JSONObject itemObj = new JSONObject();
							itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.reference.data.source") + " - " + ApplicationUtil.getMessage("page.label.reference.data.variable"));
							itemObj.put(S_FROM, one.getReferenceVariable().getName());
							itemObj.put(S_TO, two.getReferenceVariable().getName());
							changesArr.put(itemObj);
		        		}
		        		if(two.getPrimaryVariable() != null && one.getPrimaryVariableId() != two.getPrimaryVariable().getId()){
		        			JSONObject itemObj = new JSONObject();
							itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.reference.data.source") + " - " + ApplicationUtil.getMessage("page.label.primary.data.variable"));
							itemObj.put(S_FROM, one.getPrimaryVariable().getName());
							itemObj.put(S_TO, two.getPrimaryVariable().getName());
							changesArr.put(itemObj);
		        		}
		        		if(one.getConnectorParameter() != null && !one.getConnectorParameter().equals(two.getConnectorParameter())){
		        			JSONObject itemObj = new JSONObject();
							itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.reference.data.source") + " - " + ApplicationUtil.getMessage("page.label.connector.parameter"));
							itemObj.put(S_FROM, one.getConnectorParameter());
							itemObj.put(S_TO, two.getConnectorParameter());
							changesArr.put(itemObj);
		        		}
		        	}
		        }
		        if(!different.isEmpty()){
		        	StringBuilder addedName = new StringBuilder();
		        	StringBuilder deletedName = new StringBuilder();
		        	for(ReferenceConnection refConn:different){
		        		if(listOne.contains(refConn)){
		        			// deleted
		        			if(deletedName.length() > 0)
		        				deletedName.append(", ");
		        			deletedName.append(refConn.getReferenceDataSource().getName());
		        		}else if(listTwo.contains(refConn)){
		        			// Added
		        			if(addedName.length() > 0)
		        				addedName.append(", ");
		        			addedName.append(refConn.getReferenceDataSource().getName());
		        		}
		        	}
		        	if((deletedName.length() > 0) || (addedName.length() > 0)){
		        		String message = "";
		        		if(addedName.length() > 0){
		        			message += addedName + " newly added ";
		        		}
		        		if(deletedName.length() > 0){
		        			message += deletedName + " deleted";
		        		}
		        		JSONObject itemObj = new JSONObject();
						itemObj.put(S_LABEL, dataSourceAssociation.getName() + " - " + ApplicationUtil.getMessage("page.label.reference.data.source") + " -  " + message);
						changesArr.put(itemObj);
		        	}
		        }
		    }
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}
		return null;
	}

	private static ReferenceConnectionVO getVOFrom(ReferenceConnection refConn,
			List<ReferenceConnectionVO> referenceConnectionVOs) {
		for(ReferenceConnectionVO oneRef:referenceConnectionVOs)
			if(refConn.getId() == oneRef.getId())
				return oneRef;
		return null;
	}

	private static ReferenceConnection getFrom(ReferenceConnection refConn, Collection<ReferenceConnection> list) {
		for(ReferenceConnection oneRef:list)
			if(refConn.getId() == oneRef.getId())
				return oneRef;
		return null;
	}
	
	public static String forTouchpointSettings(DocumentEditWrapper docWrapper) throws ParseException {
		try {
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			
			Document document = Document.findById(docWrapper.getDocument().getId());
			DocumentEditVO documentVO = docWrapper.getDocumentEditVO();
			
			// Touchpoint Properties
			if(documentVO.isEnabled() != document.isEnabled()){
				// enable/disabled
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, document.isEnabled()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
				return changesObj.toString();
			}
			if( compareString(documentVO.getName(), document.getName()) != null){
				JSONObject itemObj = compareString(documentVO.getName(), document.getName());
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.name"));
				changesArr.put(itemObj);
			}
			// metatags
			if( compareString(documentVO.getMetaTags(), document.getMetatags()) != null){
				JSONObject itemObj = compareString(documentVO.getMetaTags(), document.getMetatags());
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.tags")); // set label
				changesArr.put(itemObj);
			}
			// Removed
			if(documentVO.isRemoved() != document.isRemoved()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.remove"));
				itemObj.put(S_FROM, documentVO.isRemoved()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, document.isRemoved()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// description
			if( compareString(documentVO.getDescription(), document.getDescription()) != null){
				JSONObject itemObj = compareString(documentVO.getDescription(), document.getDescription());
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.description")); // set label
				itemObj.put(S_FROM, StringEscapeUtils.escapeHtml(documentVO.getDescription()));
				itemObj.put(S_TO, StringEscapeUtils.escapeHtml(document.getDescription()));
				changesArr.put(itemObj);
			}
			// Default Text Style
			if( compareString(documentVO.getDefaultTextStyle(), document.getDefaultTextStyle() != null?document.getDefaultTextStyle().getName(): null, true) != null){
				JSONObject itemObj = compareString(documentVO.getDefaultTextStyle(), document.getDefaultTextStyle() != null?document.getDefaultTextStyle().getName(): null,true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.default.text.style")); // set label
				changesArr.put(itemObj);
			}
			// Default Paragraph Style
			if( compareString(documentVO.getDefaultParagraphStyle(), document.getDefaultParagraphStyle() != null?document.getDefaultParagraphStyle().getName(): null, true) != null){
				JSONObject itemObj = compareString(documentVO.getDefaultParagraphStyle(), document.getDefaultParagraphStyle() != null?document.getDefaultParagraphStyle().getName(): null,true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.default.paragraph.style")); // set label
				changesArr.put(itemObj);
			}
			// Default List Style
			if( compareString(documentVO.getDefaultListStyle(), document.getDefaultListStyle() != null?document.getDefaultListStyle().getName(): null, true) != null){
				JSONObject itemObj = compareString(documentVO.getDefaultListStyle(), document.getDefaultListStyle() != null?document.getDefaultListStyle().getName(): null,true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.default.list.style")); // set label
				changesArr.put(itemObj);
			}
			// Fisical Year Start Month
			if( documentVO.getFiscalYearStartMonth() != document.getFiscalYearStartMonth()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.fiscal.year.start.month"));
				itemObj.put(S_FROM, documentVO.getFiscalYearStartMonth());
				itemObj.put(S_TO, document.getFiscalYearStartMonth());
				changesArr.put(itemObj);
			}
			// Data Group
			if( compareString(documentVO.getDataGroupName(), document.getDataGroup() != null?document.getDataGroup().getName(): null, true) != null){
				JSONObject itemObj = compareString(documentVO.getDataGroupName(), document.getDataGroup() != null?document.getDataGroup().getName(): null,true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.data.group")); // set label
				changesArr.put(itemObj);
			}
			
			// Touchpoint Variation Management
			if(documentVO.isEnabledForVariation() != document.isEnabledForVariation()){
				// variation
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.variation.enabled"));
				itemObj.put(S_FROM, documentVO.isEnabledForVariation()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, document.isEnabledForVariation()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
				
			}else if(documentVO.isEnabledForVariation() == document.isEnabledForVariation() && document.isEnabledForVariation()){
				// Full visibility by default
				if(documentVO.isSelectionVisibleByDefault() != document.isSelectionVisibleByDefault()){
					JSONObject itemObj_ = new JSONObject();
					itemObj_.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.variants.fully.visible.by.default"));
					itemObj_.put(S_FROM, documentVO.isSelectionVisibleByDefault()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj_.put(S_TO, document.isSelectionVisibleByDefault()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj_);
				}
				// variant workflow
				if(documentVO.isEnabledForVariantWorkflow() != document.isEnabledForVariantWorkflow()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.variant.workflow"));
					itemObj.put(S_FROM, documentVO.isEnabledForVariantWorkflow()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
					itemObj.put(S_TO, document.isEnabledForVariantWorkflow()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
					changesArr.put(itemObj);
				}
				
				if( compareString(documentVO.getSelectionParameterGroupName(), document.getSelectionParameterGroup() != null?document.getSelectionParameterGroup().getName(): null) != null){
					JSONObject itemObj = compareString(documentVO.getSelectionParameterGroupName(), document.getSelectionParameterGroup() != null?document.getSelectionParameterGroup().getName(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.parameter")); // set label
					changesArr.put(itemObj);
				}
			}
			
			// Insert Management
			if(documentVO.isEnabledForInserts() != document.isEnabledForInserts()){
				// Inserts Enabled
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.inserts.enabled"));
				itemObj.put(S_FROM, documentVO.isEnabledForInserts()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, document.isEnabledForInserts()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
				
			}else if(documentVO.isEnabledForInserts() == document.isEnabledForInserts() && document.isEnabledForInserts()){
				// Inserts Enabled
				
				// Insert Parameter
				if( compareString(documentVO.getInsertParameterGroupName(), document.getInsertParameterGroup() != null?document.getInsertParameterGroup().getName(): null) != null){
					JSONObject itemObj = compareString(documentVO.getInsertParameterGroupName(), document.getInsertParameterGroup() != null?document.getInsertParameterGroup().getName(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.inserts") + " - " + ApplicationUtil.getMessage("page.label.parameter")); // set label
					changesArr.put(itemObj);
				}
				// First page weight
				if(!documentVO.getFirstPageWeight().equals(document.getFirstPageWeight())){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.first.page.weight"));
					itemObj.put(S_FROM, WeightUtil.dehydrateWeight(documentVO.getFirstPageWeight()));
					itemObj.put(S_TO, document.getFirstPageDehydratedWeight());
					changesArr.put(itemObj);
				}
				// Other page weight
				if(documentVO.getOtherPagesWeight() != document.getOtherPagesWeight()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.other.page.weights"));
					itemObj.put(S_FROM, WeightUtil.dehydrateWeight(documentVO.getOtherPagesWeight()));
					itemObj.put(S_TO, document.getOtherPagesDehydratedWeight());
					changesArr.put(itemObj);
				}
				// Default Rate Sheet
				if( compareString(documentVO.getDefaultRateScheduleName(), document.getDefaultRateScheduleCollection() != null?document.getDefaultRateScheduleCollection().getRateScheduleForDate(DateUtil.now()).getName(): null, true) != null){
					JSONObject itemObj = compareString(documentVO.getDefaultRateScheduleName(), document.getDefaultRateScheduleCollection() != null?document.getDefaultRateScheduleCollection().getRateScheduleForDate(DateUtil.now()).getName(): null, true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.default.rate.sheet")); // set label
					changesArr.put(itemObj);
				}
			}
			
			// Language Management
			if( compareString(documentVO.getLanguageParameterGroupName(), document.getLanguageParameterGroup() != null?document.getLanguageParameterGroup().getName(): null, true) != null){
				JSONObject itemObj = compareString(documentVO.getLanguageParameterGroupName(), document.getLanguageParameterGroup() != null?document.getLanguageParameterGroup().getName(): null, true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.language.management")); // set label
				changesArr.put(itemObj);
			}
			
			// Connected Management
			if(documentVO.isConnectedEnabled() != document.isConnectedEnabled()){
				// Enabled
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.connected.enabled"));
				itemObj.put(S_FROM, documentVO.isConnectedEnabled()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, document.isConnectedEnabled()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
				
			}else if(documentVO.isConnectedEnabled() == document.isConnectedEnabled() && document.isConnectedEnabled()){
				// Data Resource
				if( compareString(documentVO.getCommunicationsDataResourceName(), document.getCommunicationsDataResource() != null?document.getCommunicationsDataResource().getName(): null, true) != null){
					JSONObject itemObj = compareString(documentVO.getCommunicationsDataResourceName(), document.getCommunicationsDataResource() != null?document.getCommunicationsDataResource().getName(): null, true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.data.resource")); // set label
					changesArr.put(itemObj);
				}
				
				// Zone MarkerStyle
				if( compareString(documentVO.getCommunicationZoneMarkerStyle(), document.getCommunicationZoneMarkerStyle() != null?document.getCommunicationZoneMarkerStyle().getName(): null, true) != null){
					JSONObject itemObj = compareString(documentVO.getCommunicationZoneMarkerStyle(), document.getCommunicationZoneMarkerStyle() != null?document.getCommunicationZoneMarkerStyle().getName(): null, true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.zone.marker.style")); // set label
					changesArr.put(itemObj);
				}
				// Server Access
				if(documentVO.isUseWebServiceForCommunicationCompositionResults() != document.isCommunicationWebServiceCompositionResultsEnabled()){
					// Enabled
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.server.access"));
					itemObj.put(S_FROM, documentVO.isUseWebServiceForCommunicationCompositionResults()?ApplicationUtil.getMessage("page.label.web.service"):ApplicationUtil.getMessage("page.label.cloud"));
					itemObj.put(S_TO, document.isCommunicationWebServiceCompositionResultsEnabled()?ApplicationUtil.getMessage("page.label.web.service"):ApplicationUtil.getMessage("page.label.cloud"));
					changesArr.put(itemObj);
					
				} 
				if(document.isCommunicationWebServiceCompositionResultsEnabled()){
					//URL
					if( compareString(documentVO.getCommunicationCompositionResultsWebServiceURL(), document.getCommunicationCompositionResultsWebService() != null?document.getCommunicationCompositionResultsWebService().getUrl(): null) != null){
						JSONObject itemObj = compareString(documentVO.getCommunicationCompositionResultsWebServiceURL(), document.getCommunicationCompositionResultsWebService() != null?document.getCommunicationCompositionResultsWebService().getUrl(): null);
						itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.url")); // set label
						changesArr.put(itemObj);
					}
					// User Name
					if( compareString(documentVO.getCommunicationCompositionResultsWebServiceUsername(), document.getCommunicationCompositionResultsWebService() != null?document.getCommunicationCompositionResultsWebService().getUsername(): null) != null){
						JSONObject itemObj = compareString(documentVO.getCommunicationCompositionResultsWebServiceUsername(), document.getCommunicationCompositionResultsWebService() != null?document.getCommunicationCompositionResultsWebService().getUsername(): null);
						itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.username")); // set label
						changesArr.put(itemObj);
					}
					// Password
					if( compareString(documentVO.getCommunicationCompositionResultsWebServicePassword(), document.getCommunicationCompositionResultsWebService() != null?document.getCommunicationCompositionResultsWebService().getPassword(): null) != null){
						JSONObject itemObj = compareString(documentVO.getCommunicationCompositionResultsWebServicePassword(), document.getCommunicationCompositionResultsWebService() != null?document.getCommunicationCompositionResultsWebService().getPassword(): null);
						itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.password")); // set label
						changesArr.put(itemObj);
					}
				}
				
				// Production Status
				if(documentVO.isUseWebServiceForCommunicationProductionStatus() != document.isCommunicationWebServiceProductionStatusEnabled()){
					// Enabled
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.production.status"));
					itemObj.put(S_FROM, documentVO.isUseWebServiceForCommunicationProductionStatus()?ApplicationUtil.getMessage("page.label.web.service"):ApplicationUtil.getMessage("page.label.not.tracked"));
					itemObj.put(S_TO, document.isCommunicationWebServiceProductionStatusEnabled()?ApplicationUtil.getMessage("page.label.web.service"):ApplicationUtil.getMessage("page.label.not.tracked"));
					changesArr.put(itemObj);
					
				} 
				if(document.isCommunicationWebServiceProductionStatusEnabled()){
					//URL
					if( compareString(documentVO.getCommunicationProductionStatusWebServiceURL(), document.getCommunicationProductionStatusWebService() != null?document.getCommunicationProductionStatusWebService().getUrl(): null) != null){
						JSONObject itemObj = compareString(documentVO.getCommunicationProductionStatusWebServiceURL(), document.getCommunicationProductionStatusWebService() != null?document.getCommunicationProductionStatusWebService().getUrl(): null);
						itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.url")); // set label
						changesArr.put(itemObj);
					}
					// User Name
					if( compareString(documentVO.getCommunicationProductionStatusWebServiceUsername(), document.getCommunicationProductionStatusWebService() != null?document.getCommunicationProductionStatusWebService().getUsername(): null) != null){
						JSONObject itemObj = compareString(documentVO.getCommunicationProductionStatusWebServiceUsername(), document.getCommunicationProductionStatusWebService() != null?document.getCommunicationProductionStatusWebService().getUsername(): null);
						itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.username")); // set label
						changesArr.put(itemObj);
					}
					// Password
					if( compareString(documentVO.getCommunicationProductionStatusWebServicePassword(), document.getCommunicationProductionStatusWebService() != null?document.getCommunicationProductionStatusWebService().getPassword(): null) != null){
						JSONObject itemObj = compareString(documentVO.getCommunicationProductionStatusWebServicePassword(), document.getCommunicationProductionStatusWebService() != null?document.getCommunicationProductionStatusWebService().getPassword(): null);
						itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.password")); // set label
						changesArr.put(itemObj);
					}
				}
				
				// Production Type
				if(documentVO.getCommunicationProductionTypeId() != document.getCommunicationProductionTypeId()){
					JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.production.type"));
					itemObj.put(S_FROM, Document.getCommunicationProductionTypeLocalizedString(documentVO.getCommunicationProductionTypeId()));
					itemObj.put(S_TO, Document.getCommunicationProductionTypeLocalizedString(document.getCommunicationProductionTypeId()));
					changesArr.put(itemObj);
				}
				// Multi-recipient Identifier
				if( compareString(documentVO.getCommunicationMultiRecipientIdentifier(), document.getCommunicationMultiRecipientIdentifier() != null?document.getCommunicationMultiRecipientIdentifier().getDisplayName(): null, true) != null){
					JSONObject itemObj = compareString(documentVO.getCommunicationMultiRecipientIdentifier(), document.getCommunicationMultiRecipientIdentifier() != null?document.getCommunicationMultiRecipientIdentifier().getDisplayName(): null,true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.multi.recipient.identifier")); // set label
					changesArr.put(itemObj);
				}
				// Socialize Proofs 
				if(documentVO.isCommunicationExternalValidationEnabled() != document.isCommunicationExternalValidationEnabled()){
					// Enabled
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.socialize.proofs"));
					itemObj.put(S_FROM, documentVO.isCommunicationExternalValidationEnabled()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, document.isCommunicationExternalValidationEnabled()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				// Tag Cloud  
				if(documentVO.isCommunicationAppliesTagCloud() != document.isCommunicationAppliesTagCloud()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.tag.cloud"));
					itemObj.put(S_FROM, documentVO.isCommunicationAppliesTagCloud()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
					itemObj.put(S_TO, document.isCommunicationAppliesTagCloud()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
					changesArr.put(itemObj);
				}
				// Multiple Order Copies
				if(documentVO.isCommunicationAppliesCopiesInput() != document.isCommunicationAppliesCopiesInput()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.copies.input"));
					itemObj.put(S_FROM, documentVO.isCommunicationAppliesCopiesInput()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
					itemObj.put(S_TO, document.isCommunicationAppliesCopiesInput()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
					changesArr.put(itemObj);
				}
				// Thumbnail  
				if(documentVO.isCommunicationDisplayTouchpointThumbnail() != document.isCommunicationDisplayTouchpointThumbnail()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.thumbnail"));
					itemObj.put(S_FROM, documentVO.isCommunicationDisplayTouchpointThumbnail()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
					itemObj.put(S_TO, document.isCommunicationDisplayTouchpointThumbnail()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
					changesArr.put(itemObj);
				}
				// Resolve Variable Values  
				if(documentVO.isCommunicationResolveVariableValues() != document.isCommunicationResolveVariableValues()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.resolve.variable.values"));
					itemObj.put(S_FROM, documentVO.isCommunicationResolveVariableValues()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
					itemObj.put(S_TO, document.isCommunicationResolveVariableValues()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
					changesArr.put(itemObj);
				}
				// Suppress Noneditable Zones
				if(documentVO.isCommunicationSuppressNonEditableZones() != document.isCommunicationSuppressNonEditableZones()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.suppress.noneditable.zones"));
					itemObj.put(S_FROM, documentVO.isCommunicationSuppressNonEditableZones()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
					itemObj.put(S_TO, document.isCommunicationSuppressNonEditableZones()?ApplicationUtil.getMessage("page.label.enabled"):ApplicationUtil.getMessage("page.label.disabled"));
					changesArr.put(itemObj);
				}
			}
			// Segmentation
			// page.label.analysis.enabled
			if(documentVO.isSegmentationAnalysisEnabled() != document.isSegmentationAnalysisEnabled()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.analysis.enabled"));
				itemObj.put(S_FROM, documentVO.isSegmentationAnalysisEnabled()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, document.isSegmentationAnalysisEnabled()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			if(document.isSegmentationAnalysisEnabled()){
				
				if( compareString(documentVO.getSegmentationAnalysisResourceName(), document.getSegmentationAnalysisResource() != null?document.getSegmentationAnalysisResource().getName(): null, true) != null){
					JSONObject itemObj = compareString(documentVO.getSegmentationAnalysisResourceName(), document.getSegmentationAnalysisResource() != null?document.getSegmentationAnalysisResource().getName(): null, true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.segmentation.analysis") + " - " +ApplicationUtil.getMessage("page.label.data.resource")); // set label
					changesArr.put(itemObj);
				}
			}
			
			// TP metadata
			if( compareString(documentVO.getTouchpointMetadataFormDefinitionName(), 
					(document.getTouchpointMetadataFormDefinition() != null?document.getTouchpointMetadataFormDefinition().getName(): null), true) != null){
				JSONObject itemObj = compareString(documentVO.getTouchpointMetadataFormDefinitionName(), 
						(document.getTouchpointMetadataFormDefinition() != null?document.getTouchpointMetadataFormDefinition().getName(): null), true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.touchpoint.metadata")); // set label
				changesArr.put(itemObj);
			}
			
			// Variant metadata
			if( compareString(documentVO.getVariantMetadataFormDefinitionName(), 
					(document.getVariantMetadataFormDefinition() != null?document.getVariantMetadataFormDefinition().getName(): null), true) != null){
				JSONObject itemObj = compareString(documentVO.getVariantMetadataFormDefinitionName(), 
						(document.getVariantMetadataFormDefinition() != null?document.getVariantMetadataFormDefinition().getName(): null), true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.variant.metadata")); // set label
				changesArr.put(itemObj);
			}
			
			// STFP Settings
			if( compareString(documentVO.getSftpIPAddress(), document.getSftpIPAddress()) != null){
				JSONObject itemObj = compareString(documentVO.getSftpIPAddress(), document.getSftpIPAddress());
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.sftp.settings") + " - " + ApplicationUtil.getMessage("page.label.ip.address")); // set label
				changesArr.put(itemObj);
			}
			if( compareString(documentVO.getSftpFolderPath(), document.getSftpFolderPath()) != null){
				JSONObject itemObj = compareString(documentVO.getSftpFolderPath(), document.getSftpFolderPath());
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.sftp.settings") + " - " + ApplicationUtil.getMessage("page.label.folder")); // set label
				changesArr.put(itemObj);
			}
			if( compareString(documentVO.getSftpUsername(), document.getSftpUsername()) != null){
				JSONObject itemObj = compareString(documentVO.getSftpUsername(), document.getSftpUsername());
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.sftp.settings") + " - " + ApplicationUtil.getMessage("page.label.username")); // set label
				changesArr.put(itemObj);
			}
			if( compareString(documentVO.getSftpPassword(), document.getSftpPassword()) != null){
				JSONObject itemObj = compareString(documentVO.getSftpPassword(), document.getSftpPassword());
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.sftp.settings") + " - " + ApplicationUtil.getMessage("page.label.password")); // set label
				changesArr.put(itemObj);
			}
			if( compareString(documentVO.getSftpSSHKey(), document.getSftpSSHKey()) != null){
				JSONObject itemObj = compareString(documentVO.getSftpSSHKey(), document.getSftpSSHKey());
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.sftp.settings") + " - " + ApplicationUtil.getMessage("page.label.ssh.key")); // set label
				changesArr.put(itemObj);
			}
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
			
		}catch (JSONException e) {
			//
		}
		return null;
	}

	public static String forTPTemplateModifiersChanges(Map<String, String> map, List<TemplateModifier> newModifiers) {
		try {
			
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			
			//Collection<Map> listOne = map;
			Map<String, String> newMap = new HashMap<>();
			for(TemplateModifier modifier:newModifiers){
				newMap.put(modifier.getConnectorName(), modifier.getName());
			}
			for(String key:map.keySet()){
        		if( compareString(map.get(key), newMap.get(key)) != null){
    				JSONObject itemObj = compareString(map.get(key), newMap.get(key));
    				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.template.modifiers") + " - " + key); // set label
    				changesArr.put(itemObj);
    			}
        	}
	        
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}
		return null;
	}
	
	public static String forTPChannelChanges(DocumentChannelEditWrapper wrapper){
		try {
			
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			
			Document document = wrapper.getDocument();
			DocumentChannelEditVO editVO = wrapper.getDocumentChannelEditVO();
			ConnectorConfiguration connConfiguration = document.getConnectorConfiguration();
			
			if (connConfiguration == null || connConfiguration.getConnector() == null) {
				return null;
			}
			// Connector change. if so, return with connector name
			if( compareString(editVO.getConnectorName(), ApplicationUtil.getMessage(connConfiguration.getConnector().getPresentationName())) != null){
				JSONObject itemObj = compareString(editVO.getConnectorName(), ApplicationUtil.getMessage(connConfiguration.getConnector().getPresentationName()));
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.connector"));
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
				return changesObj.toString();
			}
			// Channel
			if( compareString(editVO.getChannelName(), connConfiguration.getConnector().getChannel().getName()) != null){
				JSONObject itemObj = compareString(editVO.getChannelName(), connConfiguration.getConnector().getChannel().getName());
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.channel"));
				changesArr.put(itemObj);
			}
//			// Connector
//			if( compareString(editVO.getConnectorName(), connConfiguration.getConnector().getName()) != null){
//				JSONObject itemObj = compareString(editVO.getConnectorName(), connConfiguration.getConnector().getName());
//				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.connector"));
//				changesArr.put(itemObj);
//			}
			// Connector Name
			if( compareString(editVO.getTpConnectorName(), document.getConnectorName()) != null){
				JSONObject itemObj = compareString(editVO.getTpConnectorName(), document.getConnectorName());
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.connector.name"));
				changesArr.put(itemObj);
			}
			// Qualification Output
			if( compareString(ApplicationUtil.getMessage(editVO.getQualificationOutputName()), ApplicationUtil.getMessage(connConfiguration.getQualificationOutput().getName())) != null){
				JSONObject itemObj = compareString(ApplicationUtil.getMessage(editVO.getQualificationOutputName()), ApplicationUtil.getMessage(connConfiguration.getQualificationOutput().getName()));
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.qualification.output"));
				changesArr.put(itemObj);
			}
			// Recipient Driver Input Filename
			if( compareString(editVO.getCustomerDriverInputFilename(), connConfiguration.getCustomerDriverInputFileName()) != null){
				JSONObject itemObj = compareString(editVO.getCustomerDriverInputFilename(), connConfiguration.getCustomerDriverInputFileName());
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.customer.driver.input.filename"));
				changesArr.put(itemObj);
			}

			// Recipient Input Files Encoding
			if(editVO.getInputCharacterEncoding() != connConfiguration.getInputCharacterEncoding()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.customer.input.files.encoding"));
				itemObj.put(S_FROM, ConnectorConfiguration.getMessageCodeForCharactorEncode(editVO.getInputCharacterEncoding()));
				itemObj.put(S_TO, ConnectorConfiguration.getMessageCodeForCharactorEncode(connConfiguration.getInputCharacterEncoding()));
				changesArr.put(itemObj);
			}
			// Recipient Output Files Encoding
			if(editVO.getOutputCharacterEncoding() != connConfiguration.getOutputCharacterEncoding()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.customer.output.files.encoding"));
				itemObj.put(S_FROM, ConnectorConfiguration.getMessageCodeForCharactorEncode(editVO.getOutputCharacterEncoding()));
				itemObj.put(S_TO, ConnectorConfiguration.getMessageCodeForCharactorEncode(connConfiguration.getOutputCharacterEncoding()));
				changesArr.put(itemObj);
			}
			// Execute In Cloud Test
			if(editVO.isExecuteInCloudTest() != connConfiguration.isExecuteInCloudTest()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.execute.incloud.test"));
				itemObj.put(S_FROM, editVO.isExecuteInCloudTest()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.isExecuteInCloudTest()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Execute In Cloud Preview
			if(editVO.isExecuteInCloudPreview() != connConfiguration.isExecuteInCloudPreview()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.execute.incloud.preview"));
				itemObj.put(S_FROM, editVO.isExecuteInCloudPreview()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.isExecuteInCloudPreview()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Execute In Cloud Proof
			if(editVO.isExecuteInCloudProof() != connConfiguration.isExecuteInCloudProof()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.execute.incloud.proof"));
				itemObj.put(S_FROM, editVO.isExecuteInCloudProof()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.isExecuteInCloudProof()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Execute In Cloud Simulation
			if(editVO.isExecuteInCloudSimulation() != connConfiguration.isExecuteInCloudSimulation()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.execute.incloud.simulation"));
				itemObj.put(S_FROM, editVO.isExecuteInCloudSimulation()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.isExecuteInCloudSimulation()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Override Remote Server Settings
			if(editVO.isOverrideRemote() != connConfiguration.isOverrideRemote()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.override.remote.server.settings"));
				itemObj.put(S_FROM, editVO.isOverrideRemote()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.isOverrideRemote()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			if(connConfiguration.isOverrideRemote()){
				if ( compareString(String.valueOf(editVO.getDEServerGuid()), String.valueOf(connConfiguration.getDEServerGuid())) != null ) {
					JSONObject itemObj = compareString(String.valueOf(editVO.getDEServerGuid()), String.valueOf(editVO.getDEServerGuid()));
					if (itemObj != null) {
						itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.bundle.delivery")); // set label
						changesArr.put(itemObj);
					}
				}
			}

			// Preprocessor script
			if( compareString(editVO.getPreQualEngineScript(), connConfiguration.getPreQualEngineScript(), true) != null){
				JSONObject itemObj = compareString(editVO.getPreQualEngineScript(), connConfiguration.getPreQualEngineScript(),true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.preprocessor.script")); // set label
				changesArr.put(itemObj);
			}
			// Post processor script step 1
			if( compareString(editVO.getPostQualEngineScript(), connConfiguration.getPostQualEngineScript(), true) != null){
				JSONObject itemObj = compareString(editVO.getPostQualEngineScript(), connConfiguration.getPostQualEngineScript(),true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.postprocessor.script1")); // set label
				changesArr.put(itemObj);
			}
			// Post processor script step 2
			if( compareString(editVO.getPostConnectorScript(), connConfiguration.getPostConnectorScript(), true) != null){
				JSONObject itemObj = compareString(editVO.getPostConnectorScript(), connConfiguration.getPostConnectorScript(),true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.postprocessor.script2")); // set label
				changesArr.put(itemObj);
			}
			// Use image filename in bundle
			if(editVO.getApplyFilenamesToBundledImages() != connConfiguration.getApplyFilenamesToBundledImages()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.bundle.images.applying.filename"));
				itemObj.put(S_FROM, editVO.getApplyFilenamesToBundledImages()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getApplyFilenamesToBundledImages()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Filename separator for images in bundle
			if(compareString(editVO.getFilenameSeparatorForBundledImages(), connConfiguration.getFilenameSeparatorForBundledImages()) != null){
				JSONObject itemObj = compareString(editVO.getFilenameSeparatorForBundledImages(), connConfiguration.getFilenameSeparatorForBundledImages(),true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.separator.for.bundled.image.filenames")); // set label
				changesArr.put(itemObj);
			}
			// Play messages with empty variables
			if(editVO.getPlayMessageOnEmptyVar() != connConfiguration.getPlayMessageOnEmptyVar()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.play.message.on.empty.variables"));
				itemObj.put(S_FROM, editVO.getPlayMessageOnEmptyVar()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getPlayMessageOnEmptyVar()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Composition Version
			if( compareString(editVO.getAppliedDeVersion(), connConfiguration.getAppliedDeVersion()) != null){
				JSONObject itemObj = compareString(editVO.getAppliedDeVersion(), connConfiguration.getAppliedDeVersion());
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.composition.version")); // set label
				changesArr.put(itemObj);
			}
			// Bundle combined content
			if(editVO.isProcessUsingCombinedContent() != document.isProcessUsingCombinedContent()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.bundle.combined.content"));
				itemObj.put(S_FROM, editVO.isProcessUsingCombinedContent()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, document.isProcessUsingCombinedContent()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Execute In Cloud Simulation
			if(editVO.getValidateProductionBundle() != connConfiguration.getValidateProductionBundle()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.validate.production.bundle"));
				itemObj.put(S_FROM, editVO.getValidateProductionBundle()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getValidateProductionBundle()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Escape Tags In Driver Data
			if(editVO.getEscapeTagsInDriverData() != connConfiguration.getEscapeTagsInDriverData()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.escape.tags.in.driver.data"));
				itemObj.put(S_FROM, editVO.getEscapeTagsInDriverData()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getEscapeTagsInDriverData()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Convert table border px to pt
			if(editVO.getConvertTableBorderPxToPts() != connConfiguration.getConvertTableBorderPxToPts()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.render.table.border.in.pt"));
				itemObj.put(S_FROM, editVO.getConvertTableBorderPxToPts()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getConvertTableBorderPxToPts()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Eval not equal to on missing tag
			if(editVO.getEvalNotEqualOnMissingTag() != connConfiguration.getEvalNotEqualOnMissingTag()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.eval.not.equal.on.missing.tag"));
				itemObj.put(S_FROM, editVO.getEvalNotEqualOnMissingTag()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getEvalNotEqualOnMissingTag()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Play empty agg first last var
			if(editVO.getPlayEmptyAggFirstLastVar() != connConfiguration.getPlayEmptyAggFirstLastVar()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.play.empty.agg.first.last.var"));
				itemObj.put(S_FROM, editVO.getPlayEmptyAggFirstLastVar()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getPlayEmptyAggFirstLastVar()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Remove underscore zero from style connector on pts size
			if(editVO.getRemoveZeroFromStyleConnector() != connConfiguration.getRemoveZeroFromStyleConnector()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.remove.underscore.zero.from.style.connector"));
				itemObj.put(S_FROM, editVO.getRemoveZeroFromStyleConnector()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getRemoveZeroFromStyleConnector()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Apply parent tagging at comp time as necessary (instead of when managing markup)
			if(editVO.getCompTimeParentTagging() != connConfiguration.getCompTimeParentTagging()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.comp.time.parent.tagging"));
				itemObj.put(S_FROM, editVO.getCompTimeParentTagging()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getCompTimeParentTagging()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Process expression variables at data group level
			if(editVO.getDataGroupExpressionVarProc() != connConfiguration.getDataGroupExpressionVarProc()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.data.group.expression.var.proc"));
				itemObj.put(S_FROM, editVO.getDataGroupExpressionVarProc()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getDataGroupExpressionVarProc()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			if(editVO.getScriptVarAppliesUndefined() != connConfiguration.getScriptVarAppliesUndefined()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.script.var.applies.undefined"));
				itemObj.put(S_FROM, editVO.getScriptVarAppliesUndefined()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getScriptVarAppliesUndefined()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			if(editVO.getCorrectParagraphTextStyles() != connConfiguration.getCorrectParagraphTextStyles()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.correct.paragraph.text.styles"));
				itemObj.put(S_FROM, editVO.getCorrectParagraphTextStyles()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getCorrectParagraphTextStyles()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			if(editVO.getFixInlineTargetingStyles() != connConfiguration.getFixInlineTargetingStyles()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.fix.inline.targeting.styles"));
				itemObj.put(S_FROM, editVO.getFixInlineTargetingStyles()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getFixInlineTargetingStyles()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			if(editVO.getPreserveDataWhitespace() != connConfiguration.getPreserveDataWhitespace()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.preserve.data.whitespace"));
				itemObj.put(S_FROM, editVO.getPreserveDataWhitespace()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getPreserveDataWhitespace()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			if(editVO.getNbspComposedAsSpace() != connConfiguration.getNbspComposedAsSpace()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.nbsp.composed.as.space"));
				itemObj.put(S_FROM, editVO.getNbspComposedAsSpace()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getNbspComposedAsSpace()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			if(editVO.getNormalizeImageLibrary() != connConfiguration.getNormalizeImageLibrary()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.normalize.image.library"));
				itemObj.put(S_FROM, editVO.getNormalizeImageLibrary()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getNormalizeImageLibrary()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			if(editVO.getNormalizeEmbeddedContent() != connConfiguration.getNormalizeEmbeddedContent()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.normalize.smart.text"));
				itemObj.put(S_FROM, editVO.getNormalizeEmbeddedContent()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getNormalizeEmbeddedContent()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			// Color Output Format (RGB or CMYK)
			if(editVO.getColorOutputFormatType() != connConfiguration.getColorOutputFormatType()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.color.output"));
				itemObj.put(S_FROM, new ColorOutputFormatType(editVO.getColorOutputFormatType()).getName());
				itemObj.put(S_TO, new ColorOutputFormatType(connConfiguration.getColorOutputFormatType()).getName());
				changesArr.put(itemObj);
			}
			if(editVO.getGmcSpanToTTag() != connConfiguration.getGmcSpanToTTag()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.gmc.span.to.t.tag"));
				itemObj.put(S_FROM, editVO.getGmcSpanToTTag()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getGmcSpanToTTag()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}
			if(editVO.getBlueUnderlineLinks() != connConfiguration.getBlueUnderlineLinks()){
				JSONObject itemObj = new JSONObject();
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.blue.underline.links"));
				itemObj.put(S_FROM, editVO.getBlueUnderlineLinks()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				itemObj.put(S_TO, connConfiguration.getBlueUnderlineLinks()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				changesArr.put(itemObj);
			}


			/*
			 *  Compare channel specific configuration
			 */
			// force hibernate to deproxy this object
			connConfiguration = HibernateDeproxyUtil.deproxy(connConfiguration, ConnectorConfiguration.class);
			if (connConfiguration instanceof GenericConnectorConfiguration) {
				GenericConnectorConfiguration instance = (GenericConnectorConfiguration) connConfiguration;
				// XML File Name
				if( compareString(editVO.getXmlFileName(), instance.getFileName()) != null){
					JSONObject itemObj = compareString(editVO.getXmlFileName(), instance.getFileName());
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.xml.file.name"));
					changesArr.put(itemObj);
				}
				// XML Format Type
				if(editVO.getFormatType() != instance.getFormatType()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.XML.format.type"));
					itemObj.put(S_FROM, editVO.getFormatType() == 1?ApplicationUtil.getMessage("page.label.optimized"):ApplicationUtil.getMessage("page.label.verbose"));
					itemObj.put(S_TO, instance.getFormatType() == 1?ApplicationUtil.getMessage("page.label.optimized"):ApplicationUtil.getMessage("page.label.verbose"));
					changesArr.put(itemObj);
				}
				// PreProcess XSLT File
				if( compareString(editVO.getPreProcessXSLTFile(), instance.getPreProcessXSLTFile()) != null){
					JSONObject itemObj = compareString(editVO.getXmlFileName(), instance.getFileName());
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.PreProcess.XSLT.File"));
					changesArr.put(itemObj);
				}
				// PostProcess XSLT File
				if( compareString(editVO.getPostProcessXSLTFile(), instance.getPostProcessXSLTFile()) != null){
					JSONObject itemObj = compareString(editVO.getPostProcessXSLTFile(), instance.getPostProcessXSLTFile());
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.PostProcess.XSLT.File"));
					changesArr.put(itemObj);
				}
			}
			if (connConfiguration instanceof DialogueConfiguration) {
				DialogueConfiguration instance = (DialogueConfiguration) connConfiguration;
				// Publication File Path
				if( compareString(editVO.getPubFileName(), instance.getPubFile()) != null){
					JSONObject itemObj = compareString(editVO.getPubFileName(), instance.getPubFile());
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.touchpoint.container.publication.file.path"));
					changesArr.put(itemObj);
				}
				// Supports Styles
				if(editVO.isSupportsStyles() != instance.isSupportsStyles()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.supports.styles"));
					itemObj.put(S_FROM, editVO.isSupportsStyles()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, instance.isSupportsStyles()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				// Legacy DXF Mode
				if(editVO.isLegacyDxfMode() != instance.isLegacyDxfMode()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.legacy.dxf.mode"));
					itemObj.put(S_FROM, editVO.isLegacyDxfMode()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, instance.isLegacyDxfMode()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				// Mixed DXF Tagged Text
				if(editVO.isMixedDxfTaggedText() != instance.isMixedDxfTaggedText()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.mixed.dxf.tagged.text.mode"));
					itemObj.put(S_FROM, editVO.isMixedDxfTaggedText()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, instance.isMixedDxfTaggedText()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				if(editVO.isRunTimeDxf() != instance.isRunTimeDxf()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.mixed.dxf.tagged.text.mode"));
					itemObj.put(S_FROM, editVO.isRunTimeDxf()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, instance.isRunTimeDxf()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				// List Style Control Type
				if(editVO.getListStyleControlType() != instance.getListStyleControlType()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.list.styles"));
					itemObj.put(S_FROM, new StyleControlType(editVO.getListStyleControlType()).getName());
					itemObj.put(S_TO, new StyleControlType(instance.getListStyleControlType()).getName());
					changesArr.put(itemObj);
				}

				// Composition Package
				if( compareString(editVO.getCompositionFileSetName(), instance.getCompositionFileSet() != null?instance.getCompositionFileSet().getName(): null, true) != null){
					JSONObject itemObj = compareString(editVO.getCompositionFileSetName(), instance.getCompositionFileSet() != null?instance.getCompositionFileSet().getName(): null,true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.composition.package"));
					changesArr.put(itemObj);
				}
				// Composition Version
				if( compareString(editVO.getCompositionVersion(), instance.getCompositionVersion()) != null){
					JSONObject itemObj = compareString(editVO.getCompositionVersion(), instance.getCompositionVersion());
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.composition.version"));
					changesArr.put(itemObj);
				}
			} else if (connConfiguration instanceof GMCConfiguration) {
				GMCConfiguration instance = (GMCConfiguration) connConfiguration;
				// Workflow File Path
				if( compareString(editVO.getWorkflowFileName(), instance.getWorkflowFile()) != null){
					JSONObject itemObj = compareString(editVO.getWorkflowFileName(), instance.getWorkflowFile());
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.touchpoint.container.workflow.file.path"));
					changesArr.put(itemObj);
				}
				// Composition Package
				if( compareString(editVO.getCompositionFileSetName(), instance.getCompositionFileSet() != null?instance.getCompositionFileSet().getName(): null, true) != null){
					JSONObject itemObj = compareString(editVO.getCompositionFileSetName(), instance.getCompositionFileSet() != null?instance.getCompositionFileSet().getName(): null,true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.composition.package"));
					changesArr.put(itemObj);
				}
				// Composition Version
				if( compareString(editVO.getCompositionVersion(), instance.getCompositionVersion()) != null){
					JSONObject itemObj = compareString(editVO.getCompositionVersion(), instance.getCompositionVersion());
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.composition.version"));
					changesArr.put(itemObj);
				}
				// Style Control
				if(editVO.getControlStyles() != instance.getControlStyles()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.control.style.attributes"));
					itemObj.put(S_FROM, editVO.getControlStyles()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, instance.getControlStyles()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				if(editVO.getTextStyleCompositionType() != instance.getTextStyleCompositionType()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.text.style.composition"));
					itemObj.put(S_FROM, new StyleCompositionType(editVO.getTextStyleCompositionType()).getName());
					itemObj.put(S_TO, new StyleCompositionType(instance.getTextStyleCompositionType()).getName());
					changesArr.put(itemObj);
				}
				if(editVO.getParagraphStyleCompositionType() != instance.getParagraphStyleCompositionType()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.paragraph.style.composition"));
					itemObj.put(S_FROM, new StyleCompositionType(editVO.getParagraphStyleCompositionType()).getName());
					itemObj.put(S_TO, new StyleCompositionType(instance.getParagraphStyleCompositionType()).getName());
					changesArr.put(itemObj);
				}
				if(editVO.getListStyleCompositionType() != instance.getListStyleCompositionType()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.list.style.composition"));
					itemObj.put(S_FROM, new StyleCompositionType(editVO.getListStyleCompositionType()).getName());
					itemObj.put(S_TO, new StyleCompositionType(instance.getListStyleCompositionType()).getName());
					changesArr.put(itemObj);
				}
			} else if (connConfiguration instanceof EMessagingConfiguration) {
				EMessagingConfiguration instance = (EMessagingConfiguration) connConfiguration;
				if (connConfiguration.getConnector().getChannel().isSMS()){
					// Customer Phone Number Variable 
					if( compareString(editVO.getCustomerPhoneNumberVariableName(), instance.getCustomerPhoneNumberVariable() != null?instance.getCustomerPhoneNumberVariable().getName(): null, true) != null){
						JSONObject itemObj = compareString(editVO.getCustomerPhoneNumberVariableName(), instance.getCustomerPhoneNumberVariable() != null?instance.getCustomerPhoneNumberVariable().getName(): null,true);
						itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.customer.phone.number.variable"));
						changesArr.put(itemObj);
					}
				}else{
					// Customer Email Address Variable
					if( compareString(editVO.getCustomerEmailAddressVariableName(), instance.getCustomerEmailAddressVariable() != null?instance.getCustomerEmailAddressVariable().getName(): null, true) != null){
						JSONObject itemObj = compareString(editVO.getCustomerEmailAddressVariableName(), instance.getCustomerEmailAddressVariable() != null?instance.getCustomerEmailAddressVariable().getName(): null,true);
						itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.customer.email.address.variable"));
						changesArr.put(itemObj);
					}
				}
			} else if (connConfiguration instanceof SendmailConfiguration) {
				SendmailConfiguration instance = (SendmailConfiguration) connConfiguration;
				// Customer Email Address Variable
				if( compareString(editVO.getCustomerEmailAddressVariableName(), instance.getCustomerEmailAddressVariable() != null?instance.getCustomerEmailAddressVariable().getName(): null, true) != null){
					JSONObject itemObj = compareString(editVO.getCustomerEmailAddressVariableName(), instance.getCustomerEmailAddressVariable() != null?instance.getCustomerEmailAddressVariable().getName(): null,true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.customer.email.address.variable"));
					changesArr.put(itemObj);
				}
			} else if (connConfiguration instanceof ExactTargetConfiguration) {
				ExactTargetConfiguration instance = (ExactTargetConfiguration) connConfiguration;
				// Customer Email Address Variable
				if( compareString(editVO.getCustomerEmailAddressVariableName(), instance.getCustomerEmailAddressVariable() != null?instance.getCustomerEmailAddressVariable().getName(): null, true) != null){
					JSONObject itemObj = compareString(editVO.getCustomerEmailAddressVariableName(), instance.getCustomerEmailAddressVariable() != null?instance.getCustomerEmailAddressVariable().getName(): null,true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.customer.email.address.variable"));
					changesArr.put(itemObj);
				}
				// Customer ET Key (GUID) Variable
				if( compareString(editVO.getCustomerEmailAddressVariableName(), instance.getCustomerKeyVariable() != null?instance.getCustomerKeyVariable().getName(): null, true) != null){
					JSONObject itemObj = compareString(editVO.getCustomerEmailAddressVariableName(), instance.getCustomerKeyVariable() != null?instance.getCustomerKeyVariable().getName(): null,true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.customer.key.variable"));
					changesArr.put(itemObj);
				}
			} else if (connConfiguration instanceof ClickatellConfiguration) {
				ClickatellConfiguration instance = (ClickatellConfiguration) connConfiguration;
				// Customer Phone Number Variable 
				if( compareString(editVO.getCustomerPhoneNumberVariableName(), instance.getCustomerPhoneNumberVariable() != null?instance.getCustomerPhoneNumberVariable().getName(): null, true) != null){
					JSONObject itemObj = compareString(editVO.getCustomerPhoneNumberVariableName(), instance.getCustomerPhoneNumberVariable() != null?instance.getCustomerPhoneNumberVariable().getName(): null,true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.customer.phone.number.variable"));
					changesArr.put(itemObj);
				}
			} else if (connConfiguration instanceof FtpConfiguration) {
				FtpConfiguration instance = (FtpConfiguration) connConfiguration;
				// FTP Server
				if(editVO.getServerId() != instance.getServerId()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.ftp.server"));
					itemObj.put(S_FROM, getFTPHostName(editVO.getServerId()));
					itemObj.put(S_TO, getFTPHostName(instance.getServerId()));
					changesArr.put(itemObj);
				}
				// FTP Upload Location/Path
				if( compareString(editVO.getRecipientFileLocation(), instance.getRecipientFileLocation()) != null){
					JSONObject itemObj = compareString(editVO.getRecipientFileLocation(), instance.getRecipientFileLocation());
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.ftp.location"));
					changesArr.put(itemObj);
				}
				// Web URL
				if( compareString(editVO.getWebURL(), instance.getWebURL()) != null){
					JSONObject itemObj = compareString(editVO.getWebURL(), instance.getWebURL());
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.web.url"));
					changesArr.put(itemObj);
				}
				ComplexValue fileNameValue= instance.getRecipientFileComplexValue();
				// Recipient Filename
				if( compareString(editVO.getRecipientFileName(), fileNameValue != null?fileNameValue.getEncodedValue(): null) != null){
					JSONObject itemObj = compareString(editVO.getRecipientFileName(), fileNameValue != null?fileNameValue.getEncodedValue(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.customer.filename"));
					itemObj.put(S_FROM, StringEscapeUtils.escapeHtml(editVO.getRecipientFileName()));
					itemObj.put(S_TO, StringEscapeUtils.escapeHtml(fileNameValue.getEncodedValue()));
					changesArr.put(itemObj);
				}
				ComplexValue outputFilenameValue= instance.getOutputFilename();
				// Output Filename
				if( compareString(editVO.getOutputFilename(), outputFilenameValue != null?outputFilenameValue.getEncodedValue(): null) != null){
					JSONObject itemObj = compareString(editVO.getOutputFilename(), outputFilenameValue != null?outputFilenameValue.getEncodedValue(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.output.filename"));
					itemObj.put(S_FROM, StringEscapeUtils.escapeHtml(editVO.getOutputFilename()));
					itemObj.put(S_TO, StringEscapeUtils.escapeHtml(outputFilenameValue.getEncodedValue()));
					changesArr.put(itemObj);
				}
				// As Landing Page, As Embedded
				if(editVO.getIsEmbedded() != instance.getIsEmbedded()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.as.landing.page") + "/" + ApplicationUtil.getMessage("page.label.as.embedded"));
					itemObj.put(S_FROM, editVO.getIsEmbedded()?ApplicationUtil.getMessage("page.label.as.embedded"):ApplicationUtil.getMessage("page.label.as.landing.page"));
					itemObj.put(S_TO, instance.getIsEmbedded()?ApplicationUtil.getMessage("page.label.as.embedded"):ApplicationUtil.getMessage("page.label.as.landing.page"));
					changesArr.put(itemObj);
				}
			}
			else if ( connConfiguration instanceof NativeCompositionConfiguration ) {
				NativeCompositionConfiguration instance = (NativeCompositionConfiguration) connConfiguration;
//				setAdvancedComposition( ((NativeCompositionConfiguration) connConfiguration).isAdvancedComposition() );

				// Fillable Forms
				if(editVO.isApplyFillableForms() != instance.isApplyFillableForms()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.fillable.forms"));
					itemObj.put(S_FROM, editVO.isApplyFillableForms()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, instance.isApplyFillableForms()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				// Output File Type
				OutputFileType type = new OutputFileType(instance.getOutputFileType());
				if( compareString(editVO.getOutputFileTypeName(), type != null?type.getName(): null) != null){
					JSONObject itemObj = compareString(editVO.getOutputFileTypeName(), type != null?type.getName(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.output.file.type"));
					changesArr.put(itemObj);
				}
				// Start Document On Odd Page
				if(editVO.getStartsOnOddPage() != instance.getStartsOnOddPage()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.start.on.odd.page"));
					itemObj.put(S_FROM, editVO.getStartsOnOddPage()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, instance.getStartsOnOddPage()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				// Duplex Output
				if(editVO.getDuplexOutput() != instance.getDuplexOutput()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.duplex.output"));
					itemObj.put(S_FROM, editVO.getDuplexOutput()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, instance.getDuplexOutput()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				ComplexValue outputFilenameValue= instance.getOutputFilename();
				// Output Filename
				if( compareString(editVO.getOutputFilename(), outputFilenameValue != null?outputFilenameValue.getEncodedValue(): null) != null){
					JSONObject itemObj = compareString(editVO.getOutputFilename(), outputFilenameValue != null?outputFilenameValue.getEncodedValue(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.output.filename"));
					itemObj.put(S_FROM, StringEscapeUtils.escapeHtml(editVO.getOutputFilename()));
					itemObj.put(S_TO, StringEscapeUtils.escapeHtml(outputFilenameValue.getEncodedValue()));
					changesArr.put(itemObj);
				}
				ComplexValue outputDocumentTitleValue= instance.getOutputDocumentTitle();
				// Output Filename
				if( compareString(editVO.getOutputDocumentTitle(), outputDocumentTitleValue != null?outputDocumentTitleValue.getEncodedValue(): null) != null){
					JSONObject itemObj = compareString(editVO.getOutputDocumentTitle(), outputDocumentTitleValue != null?outputDocumentTitleValue.getEncodedValue(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.output.document.title"));
					itemObj.put(S_FROM, StringEscapeUtils.escapeHtml(editVO.getOutputDocumentTitle()));
					itemObj.put(S_TO, StringEscapeUtils.escapeHtml(outputDocumentTitleValue.getEncodedValue()));
					changesArr.put(itemObj);
				}
			}
			else if ( connConfiguration instanceof SefasConfiguration) {
				SefasConfiguration instance = (SefasConfiguration) connConfiguration;

				// Composition Package
				if( compareString(editVO.getCompositionFileSetName(), instance.getCompositionFileSet() != null?instance.getCompositionFileSet().getName(): null, true) != null){
					JSONObject itemObj = compareString(editVO.getCompositionFileSetName(), instance.getCompositionFileSet() != null?instance.getCompositionFileSet().getName(): null,true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.composition.package"));
					changesArr.put(itemObj);
				}
				// Composition Version
				if( compareString(editVO.getCompositionVersion(), instance.getCompositionVersion()) != null){
					JSONObject itemObj = compareString(editVO.getCompositionVersion(), instance.getCompositionVersion());
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.composition.version"));
					changesArr.put(itemObj);
				}

				// Output File Type
				OutputFileType type = new OutputFileType(instance.getOutputFileType());
				if( compareString(editVO.getOutputFileTypeName(), type != null?type.getName(): null) != null){
					JSONObject itemObj = compareString(editVO.getOutputFileTypeName(), type != null?type.getName(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.output.file.type"));
					changesArr.put(itemObj);
				}
				// Start Document On Odd Page
				if(editVO.getStartsOnOddPage() != instance.getStartsOnOddPage()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.start.on.odd.page"));
					itemObj.put(S_FROM, editVO.getStartsOnOddPage()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, instance.getStartsOnOddPage()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				// Duplex Output
				if(editVO.getDuplexOutput() != instance.getDuplexOutput()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.duplex.output"));
					itemObj.put(S_FROM, editVO.getDuplexOutput()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, instance.getDuplexOutput()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				ComplexValue outputFilenameValue= instance.getOutputFilename();
				// Output Filename
				if( compareString(editVO.getOutputFilename(), outputFilenameValue != null?outputFilenameValue.getEncodedValue(): null) != null){
					JSONObject itemObj = compareString(editVO.getOutputFilename(), outputFilenameValue != null?outputFilenameValue.getEncodedValue(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.output.filename"));
					itemObj.put(S_FROM, StringEscapeUtils.escapeHtml(editVO.getOutputFilename()));
					itemObj.put(S_TO, StringEscapeUtils.escapeHtml(outputFilenameValue.getEncodedValue()));
					changesArr.put(itemObj);
				}
				ComplexValue outputDocumentTitleValue= instance.getOutputDocumentTitle();
				// Output Filename
				if( compareString(editVO.getOutputDocumentTitle(), outputDocumentTitleValue != null?outputDocumentTitleValue.getEncodedValue(): null) != null){
					JSONObject itemObj = compareString(editVO.getOutputDocumentTitle(), outputDocumentTitleValue != null?outputDocumentTitleValue.getEncodedValue(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.output.document.title"));
					itemObj.put(S_FROM, StringEscapeUtils.escapeHtml(editVO.getOutputDocumentTitle()));
					itemObj.put(S_TO, StringEscapeUtils.escapeHtml(outputDocumentTitleValue.getEncodedValue()));
					changesArr.put(itemObj);
				}
			}
			else if ( connConfiguration instanceof MPHCSConfiguration) {
				MPHCSConfiguration instance = (MPHCSConfiguration) connConfiguration;

				// Composition Package
				if( compareString(editVO.getCompositionFileSetName(), instance.getCompositionFileSet() != null?instance.getCompositionFileSet().getName(): null, true) != null){
					JSONObject itemObj = compareString(editVO.getCompositionFileSetName(), instance.getCompositionFileSet() != null?instance.getCompositionFileSet().getName(): null,true);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.composition.package"));
					changesArr.put(itemObj);
				}
				// Composition Version
				if( compareString(editVO.getCompositionVersion(), instance.getCompositionVersion()) != null){
					JSONObject itemObj = compareString(editVO.getCompositionVersion(), instance.getCompositionVersion());
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.composition.version"));
					changesArr.put(itemObj);
				}

				// Output File Type
				OutputFileType type = new OutputFileType(instance.getOutputFileType());
				if( compareString(editVO.getOutputFileTypeName(), type != null?type.getName(): null) != null){
					JSONObject itemObj = compareString(editVO.getOutputFileTypeName(), type != null?type.getName(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.output.file.type"));
					changesArr.put(itemObj);
				}
				// Start Document On Odd Page
				if(editVO.getStartsOnOddPage() != instance.getStartsOnOddPage()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.start.on.odd.page"));
					itemObj.put(S_FROM, editVO.getStartsOnOddPage()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, instance.getStartsOnOddPage()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				// Duplex Output
				if(editVO.getDuplexOutput() != instance.getDuplexOutput()){
					JSONObject itemObj = new JSONObject();
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.duplex.output"));
					itemObj.put(S_FROM, editVO.getDuplexOutput()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					itemObj.put(S_TO, instance.getDuplexOutput()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
					changesArr.put(itemObj);
				}
				ComplexValue outputFilenameValue= instance.getOutputFilename();
				// Output Filename
				if( compareString(editVO.getOutputFilename(), outputFilenameValue != null?outputFilenameValue.getEncodedValue(): null) != null){
					JSONObject itemObj = compareString(editVO.getOutputFilename(), outputFilenameValue != null?outputFilenameValue.getEncodedValue(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.output.filename"));
					itemObj.put(S_FROM, StringEscapeUtils.escapeHtml(editVO.getOutputFilename()));
					itemObj.put(S_TO, StringEscapeUtils.escapeHtml(outputFilenameValue.getEncodedValue()));
					changesArr.put(itemObj);
				}
				ComplexValue outputDocumentTitleValue= instance.getOutputDocumentTitle();
				// Output Filename
				if( compareString(editVO.getOutputDocumentTitle(), outputDocumentTitleValue != null?outputDocumentTitleValue.getEncodedValue(): null) != null){
					JSONObject itemObj = compareString(editVO.getOutputDocumentTitle(), outputDocumentTitleValue != null?outputDocumentTitleValue.getEncodedValue(): null);
					itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.output.document.title"));
					itemObj.put(S_FROM, StringEscapeUtils.escapeHtml(editVO.getOutputDocumentTitle()));
					itemObj.put(S_TO, StringEscapeUtils.escapeHtml(outputDocumentTitleValue.getEncodedValue()));
					changesArr.put(itemObj);
				}
			}
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}
		return null;
	}

	public static String forTPExtendedReportingVariablesChanges(DocumentExtendedReportingVariablesWrapper command) {
		try {
			
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			
			// Customer Reporting Variable A
			if( compareString(command.getCustomerReportingVariableAName(), command.getCustomerReportingVariableA() != null?command.getCustomerReportingVariableA().getName(): null, true) != null){
				JSONObject itemObj = compareString(command.getCustomerReportingVariableAName(), command.getCustomerReportingVariableA() != null?command.getCustomerReportingVariableA().getName(): null, true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.other.customer.reporting.variable.a")); // set label
				changesArr.put(itemObj);
			}
			// Customer Reporting Variable B
			if( compareString(command.getCustomerReportingVariableBName(), command.getCustomerReportingVariableB() != null?command.getCustomerReportingVariableB().getName(): null, true) != null){
				JSONObject itemObj = compareString(command.getCustomerReportingVariableBName(), command.getCustomerReportingVariableB() != null?command.getCustomerReportingVariableB().getName(): null, true);
				itemObj.putOpt(S_LABEL, ApplicationUtil.getMessage("page.label.other.customer.reporting.variable.b")); // set label
				changesArr.put(itemObj);
			}
			Collection<Long> listOne = new HashSet<>(command.getOrgSelectedExtReportingVariableIds());
			if(command.getOrgSelectedExtReportingVariableIds() != null)
				listOne.addAll(command.getOrgSelectedExtReportingVariableIds());
			Collection<Long> listTwo = new HashSet<>();
			if(command.getSelectedExtReportingVariableIds() != null)
				listTwo.addAll(command.getSelectedExtReportingVariableIds());
			Collection<Long> similar = new HashSet<>(listOne);
			Collection<Long> different = new HashSet<>();
			different.addAll( listOne );
			different.addAll( listTwo );
			similar.retainAll( listTwo );
			different.removeAll( similar );
	        if(!different.isEmpty()){
	        	StringBuilder addedName = new StringBuilder();
	        	StringBuilder deletedName = new StringBuilder();
	        	for(long variableId:different){
	        		if(listOne.contains(variableId) && DataElementVariable.findById(variableId) != null){
	        			// deleted
	        			if(deletedName.length() > 0)
	        				deletedName.append(", ");
	        			deletedName.append(DataElementVariable.findById(variableId).getDisplayName());
	        		}else if(listTwo.contains(variableId) && DataElementVariable.findById(variableId) != null){
	        			// Added
	        			if(addedName.length() > 0)
	        				addedName.append(", ");
	        			addedName.append(DataElementVariable.findById(variableId).getDisplayName());
	        		}
	        	}
	        	if((deletedName.length() > 0) || (addedName.length() > 0)){
	        		String message = "";
	        		if(addedName.length() > 0){
	        			message += addedName + " newly added ";
	        		}
	        		if(deletedName.length() > 0){
	        			message += deletedName + " deleted";
	        		}
	        		JSONObject itemObj = new JSONObject();
					itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.external.reporting.data") + " -  " + ApplicationUtil.getMessage("page.label.variables") + ":" + message);
					changesArr.put(itemObj);
	        	}
	        }
	        
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}
		return null;
	}

	private static JSONObject compareString(String oldString, String newString) throws JSONException {
		return compareString(oldString, newString, false);
	}

	private static JSONObject compareString(String oldString, String newString, boolean replaceNullToNone) throws JSONException{
		String oldName = "";
		String newName = "";
		if(oldString == null || oldString.isEmpty()){
			if(replaceNullToNone)
				oldName = ApplicationUtil.getMessage("page.label.none");
			else
				oldName = "";
		}else{
			oldName = oldString;
		}
		if(newString == null || newString.isEmpty()){
			if(replaceNullToNone)
				newName = ApplicationUtil.getMessage("page.label.none");
			else
				newName = "";
		}else{
			newName = newString;
		}
		if(!oldName.equals(newName)){
			JSONObject itemObj = new JSONObject();
			itemObj.put(S_FROM, oldName);
			itemObj.put(S_TO, newName);
			return itemObj;
		}
		return null;
	}

	private static String getFTPHostName(long serverId) {
		String hostName = "";
		switch((int)serverId){
		case 0: // None
			hostName = ApplicationUtil.getMessage("page.label.none");
			break;
		case 1: // ftp1host
			hostName = "1.(" + SystemPropertyManager.getInstance().getSystemProperty("ftp1.host")+ ")";
			break;
		case 2: // ftp2host
			hostName = "2.(" + SystemPropertyManager.getInstance().getSystemProperty("ftp2.host")+ ")";
			break;
		case 3: // ftp3host
			hostName = "3.(" + SystemPropertyManager.getInstance().getSystemProperty("ftp3.host")+ ")";
			break;
		}
		return hostName;
	}

	public static String forTemplateVariantsChanges(Document document, List<TemplateVariantVO> orgVariants,
			List<TemplateVariantVO> variants) {
		try {
			
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			
			Collection<TemplateVariantVO> listOne = new HashSet<>();
			if(orgVariants != null)
				listOne.addAll(orgVariants);
			Collection<TemplateVariantVO> listTwo = new HashSet<>();
			if(variants != null)
				listTwo.addAll(variants);
			String message = "";
	        if(variants != null && !variants.isEmpty()){
	        	StringBuilder addedName = new StringBuilder();
	        	StringBuilder deletedName = new StringBuilder();
	        	StringBuilder updatedName = new StringBuilder();
	        	
	        	for(TemplateVariantVO variant:variants){
	        		if(variant.getAction() == -1){
	        			// deleted
	        			if(!deletedName.isEmpty())
	        				deletedName.append(", ");
	        			deletedName.append(variant.getName());
	        		}else if(variant.getAction() == 1 && variant.getId() == Long.MAX_VALUE){
	        			// Added
	        			if(!addedName.isEmpty())
	        				addedName.append(", ");
	        			addedName.append(variant.getName());
	        		}else if(variant.getAction() == 2L){
	        			TemplateVariantVO one = null;
						for(TemplateVariantVO vo: listOne) 
							if(vo.getAction() == 2 && variant.getGuid().equals(vo.getGuid())){ 
								one = vo;
								break;
							}
						if(one != null)
		        			if( compareString(one.getName(), variant.getName()) != null || compareString(one.getDataFileSandboxFile(), variant.getDataFileSandboxFile()) != null){
								if(!updatedName.isEmpty())
									updatedName.append(", ");
								updatedName.append(variant.getName());
							}
	        		}
	        	}
	        	if((!deletedName.isEmpty()) || (!addedName.isEmpty()) || (!updatedName.isEmpty())){
	        		
	        		if(!addedName.isEmpty()){
	        			message += addedName + " newly added ";
	        		}
	        		if(!deletedName.isEmpty()){
	        			message += deletedName + " deleted";
	        		}
	        		if(!updatedName.isEmpty()){
	        			message += updatedName + " updated";
	        		}
	        	}
	        }
	        if(!message.isEmpty()){
		        JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, ApplicationUtil.getMessage("page.label.alternate.templates") + " : " + message);
				changesArr.put(itemObj);
	        }
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}
		return null;
	}
	
	public static String forTPTargetingChanges(Map<String, List<TargetGroupWrapper>> orgMap, TargetingWrapper command) throws ClassNotFoundException {
		try {
			
			JSONObject changesObj = new JSONObject();
			JSONArray changesArr = new JSONArray();
			
			for(String key:command.getMapIndex()){
				StringBuilder addedName = new StringBuilder();
	        	StringBuilder deletedName = new StringBuilder();
	        	StringBuilder updatedName = new StringBuilder();
	        	String message = "";
				String listName = ApplicationUtil.getMessage("page.label.touchpoint.targeting") + " - ";
		        listName += (key.equals(TargetingWrapper.INCLUDED_TARGETING_MAP_KEY)? ApplicationUtil.getMessage("page.label.include"):(key.equals(TargetingWrapper.EXCLUDED_TARGETING_MAP_KEY)?ApplicationUtil.getMessage("page.label.exclude"): ApplicationUtil.getMessage("page.label.exclude")));
				
				// relationship compare
				if(command.getTargetGroupRelationship().get(key) != command.getOrgTargetGroupRelationship().get(key)){
					String from = command.getOrgTargetGroupRelationship().get(key)==true?ApplicationUtil.getMessage("page.label.at.least.one"): ApplicationUtil.getMessage("page.label.all");
					String to = command.getTargetGroupRelationship().get(key)==true?ApplicationUtil.getMessage("page.label.at.least.one"): ApplicationUtil.getMessage("page.label.all");
					message += listName + " condition option changed from " + from + " to " + to;
				}
				Collection<TargetGroupWrapper> listOne = orgMap.get(key);
				Collection<TargetGroupWrapper> listTwo = command.getTargetGroupsMap().get(key);
				List<Long> processedTGIds = new ArrayList<>();
				
				if(!listOne.isEmpty()){
					for(TargetGroupWrapper wrapper:listOne){
						TargetGroupWrapper fromList = getFromTargetGroupWrapperList(wrapper, listTwo);
						if( fromList == null){
							// deleted
							if(!deletedName.isEmpty())
		        				deletedName.append(", ");
							deletedName.append(wrapper.getNameText());
						}else{
							// assume this covers all similar wrapper cases
							// compare details
							// add to processedTGIds
							JSONObject itemObj = compareTGInstanceDetails(wrapper, fromList);
							if(itemObj != null){
								if(!updatedName.isEmpty())
									updatedName.append(", ");
								updatedName.append(itemObj.get(S_LABEL));
							}
							processedTGIds.add(wrapper.getTargetGroupId());
						}
					}
				}
				if(!listTwo.isEmpty()){
					for(TargetGroupWrapper wrapper:listTwo){
						TargetGroupWrapper fromList = getFromTargetGroupWrapperList(wrapper, listOne);
						if( fromList == null){
							// added
							if(wrapper.getTargetGroupId() > 0){
								if(!addedName.isEmpty())
			        				addedName.append(", ");
			        			addedName.append(wrapper.getTargetGroup() != null ? wrapper.getTargetGroup().getName() : wrapper.getNameText() != null ? wrapper.getNameText() : "");
							}
						}
					}
				}
				if((!deletedName.isEmpty()) || (!addedName.isEmpty()) || (!updatedName.isEmpty())){
	        		if(!addedName.isEmpty()){
	        			message += addedName + " newly added ";
	        		}
	        		if(!deletedName.isEmpty()){
	        			message += deletedName + " deleted";
	        		}
	        		if(!updatedName.isEmpty()){
	        			message += updatedName + " updated";
	        		}
	        	}
				if(!message.isEmpty()){
			        JSONObject itemObj = new JSONObject();
			        itemObj.put(S_LABEL,  listName + " : " + message);
					changesArr.put(itemObj);
		        }
			}
			if(!changesArr.isEmpty()){
				changesObj.put(S_CHANGES, changesArr);
			}else{
				JSONObject itemObj = new JSONObject();
				itemObj.put(S_LABEL, "Saved but nothing changed");
				changesArr.put(itemObj);
				changesObj.put(S_CHANGES, changesArr);
			}
			return changesObj.toString();
		} catch (JSONException e) {
			//
		}
		return null;
	}

	public static String forAssetTargetingChanges(String fromHtml, String toHtml){

        return "# " + ApplicationUtil.getMessage("page.label.targeting") +
				": " + HtmlUtils.htmlEscape(fromHtml) + " > " + HtmlUtils.htmlEscape(toHtml);
	}

	public static String forContentAssociationTypeChanges(ContentAssociationType fromType,
														  ContentAssociationType toType,
														  ContentObjectAssociation ca){
		StringBuilder changes = new StringBuilder();
		changes.append("# ").append(ApplicationUtil.getMessage("page.label.type"));
		if(ca.getMessagepointLocale() != null){
			changes.append(" (").append(ca.getMessagepointLocale().getLanguageName());
			if(ca.getPGTreeNode() != null){
				String fullpath = ParameterGroupTreeNode.getFullPath(ca.getPGTreeNode());
				if(org.apache.commons.lang3.StringUtils.isNotEmpty(fullpath)) {
					if (fullpath.startsWith("/")) {
						fullpath = fullpath.substring(1);
					}
					fullpath = fullpath.replace("/", " > ");
				}
				changes.append(", ").append(fullpath);
			}

			if(ca.getZonePart() != null){
				changes.append(", ").append(ca.getZonePart().getName());
			}

			changes.append(")");
		}

		changes.append(": ").append(fromType.getName()).append(" > ").append(toType.getName());

		return changes.toString();
	}
	
	private static JSONObject compareTGInstanceDetails(TargetGroupWrapper wrapper, TargetGroupWrapper fromList) throws JSONException {
		Set<Long> allKeys = new HashSet<>();
		Set<Long> wrapperKeys = new HashSet<>();
		Set<Long> fromListKeys = new HashSet<>();
		if(!wrapper.getConditionSubelementValues().isEmpty()){
			wrapperKeys.addAll(wrapper.getConditionSubelementValues().keySet());
		}
		if(!fromList.getConditionSubelementValues().isEmpty()){
			fromListKeys.addAll(fromList.getConditionSubelementValues().keySet());
		}
		allKeys.addAll(wrapperKeys);
		allKeys.addAll(fromListKeys);
		StringBuilder changes = new StringBuilder();
		for(Long key: allKeys){
			String tgName = wrapper.getTargetGroup()!=null?wrapper.getTargetGroup().getName():wrapper.getNameText()!=null?wrapper.getNameText():"";
			if(wrapperKeys.contains(key) && fromListKeys.contains(key)){
				JSONObject itemObj = compareString(wrapper.getConditionSubelementValues().get(key), fromList.getConditionSubelementValues().get(key));
				if( compareString(wrapper.getConditionSubelementValues().get(key), fromList.getConditionSubelementValues().get(key)) != null){
					changes.append(tgName).append(" value from ").append(itemObj.get(S_FROM).toString()).append(" to ").append(itemObj.get(S_TO).toString());
				}
			}
		}
		if(!changes.isEmpty()){
			JSONObject itemObj = new JSONObject();
			itemObj.put(S_LABEL, changes.toString());
			return itemObj;
		}
		return null;
	}

	private static TargetGroupWrapper getFromTargetGroupWrapperList(TargetGroupWrapper wrapper, Collection<TargetGroupWrapper> list) {
		for(TargetGroupWrapper oneRef:list)
			if(wrapper.getTargetGroupId() == oneRef.getTargetGroupId())
				return oneRef;
		return null;
	}

	public static String forTargetRuleChanges(ConditionElement from, ConditionElement to) {
		try {
			StringBuilder message = new StringBuilder();
			if(from == null && to == null){
				message.append(AuditMetadataBuilder.forTargetRules(ApplicationUtil.getMessage("page.label.new.target.rule"), "", ""));
			}
			if( compareString(from.getMetatags(), to.getMetatags()) != null){
				message.append(AuditMetadataBuilder.forTargetRules(to.getName(), from.getMetatags(), to.getMetatags()));
			}
			if(!from.getConditionType().getName().equals(to.getConditionType().getName())) {
				message.append(AuditMetadataBuilder.forTargetRules(to.getName(), ApplicationUtil.getMessage(from.getConditionType().getName()), ApplicationUtil.getMessage(to.getConditionType().getName())));

			}else{
				if( compareString(from.getName(), to.getName()) != null){
					message.append(AuditMetadataBuilder.forTargetRules(ApplicationUtil.getMessage("page.label.name"), from.getName(), to.getName()));
				}
				if( compareString(from.getMetatags(), to.getMetatags()) != null){
					message.append(AuditMetadataBuilder.forTargetRules(ApplicationUtil.getMessage("page.label.metatags"), from.getMetatags(), to.getMetatags()));
				}
				if(!from.getConditionType().getName().equals(to.getConditionType().getName())) {
					message.append(AuditMetadataBuilder.forTargetRules(ApplicationUtil.getMessage("page.label.attribute.condition.type"),
                            ApplicationUtil.getMessage(from.getConditionType().getName()),
                            ApplicationUtil.getMessage(to.getConditionType().getName())));
				}
			}

			if (from != null && to != null) {
				List<ConditionSubelement> fromSubElements = from.getSubElements();
				List<ConditionSubelement> toSubElements = to.getSubElements();

				//Need to check for adding and removing subelements
				List<ConditionSubelement> added = ConditionSubelementComparator.findAddedSubElementsByIds(fromSubElements, toSubElements);
				List<ConditionSubelement> removed = ConditionSubelementComparator.findRemovedSubElementsByIds(fromSubElements, toSubElements);
				List<ConditionSubelement> updated = ConditionSubelementComparator.findCommonSubElementsByIds(fromSubElements, toSubElements);

				if (!updated.isEmpty()) { //Look for common elements to check if it was a change
					message.append(ConditionSubelementComparator.findChangesInConditionSubelementsForChangeList(fromSubElements, toSubElements));
				}

				if(!removed.isEmpty()) {
					for (ConditionSubelement subelement : removed) {
						message.append(AuditMetadataBuilder.forTargetRules(subelement.getName(), "", ApplicationUtil.getMessage("page.label.removed")));
					}
				}

				if (!added.isEmpty()) {
					for (ConditionSubelement subelement : added) {
						message.append(AuditMetadataBuilder.forTargetRules(subelement.getName(), "", ApplicationUtil.getMessage("page.label.added")));
					}
				}
			}
			return message.toString();
			
		} catch (JSONException e) {
			return null;
		}
	}

	public static String forPowerEdit(ContentPowerEditWrapper wrapper, List<Long> persistedIds, List<Long> skippedIds) {
		try {
			JSONObject jsonData = new JSONObject();

			JSONObject data = new JSONObject();

			data.put("find_script", StringEscapeUtils.escapeHtml(wrapper.getFindObjectsScript()));
			data.put("alter_script", StringEscapeUtils.escapeHtml(wrapper.getAlterObjectsScript()));
			data.put("time_stamp", wrapper.getTimeStamp());
			if ( wrapper.getActionValue() == 1 ) {
				data.put("altered_content_id", StringUtils.join(persistedIds, ", "));
			} else if ( wrapper.getActionValue() == 2 ) {
				data.put("reverted_content_id", StringUtils.join(persistedIds, ", "));
			}
			data.put("skipped_content_id", StringUtils.join(skippedIds, ", "));

			jsonData.put("json_data", data);

			return jsonData.toString();
		} catch (JSONException e) {
			return null;
		}
	}

	public static String forTransform(ContentTransformWrapper wrapper, List<Long> persistedIds, List<Long> skippedIds) {
		try {
			JSONObject jsonData = new JSONObject();

			JSONObject data = new JSONObject();

			data.put("time_stamp", wrapper.getTimeStamp());
			data.put("transformed_content_id", StringUtils.join(persistedIds, ", "));
			data.put("skipped_content_id", StringUtils.join(skippedIds, ", "));

			jsonData.put("json_data", data);

			return jsonData.toString();
		} catch (JSONException e) {
			return null;
		}
	}

	public static String forContentAssistant(ContentAssistant contentAssistant, List<Long> persistedIds, List<Long> skippedIds) {
		try {
			JSONObject jsonData = new JSONObject();

			JSONObject data = new JSONObject();


			jsonData.put("json_data", data);

			return jsonData.toString();
		} catch (JSONException e) {
			return null;
		}
	}

	public static String forMetatagAdmin(List<String> removedTags, List<String> renamedTags) {
		try {
			JSONObject jsonData = new JSONObject();

			JSONObject data = new JSONObject();

			data.put("removed_tags", StringEscapeUtils.escapeHtml(StringUtils.join(removedTags,"; ")));
			data.put("renamed_tags", StringEscapeUtils.escapeHtml(StringUtils.join(renamedTags,"; ")));

			jsonData.put("json_data", data);

			return jsonData.toString();
		} catch (JSONException e) {
			return null;
		}
	}

	public static String forAIRewrite(String moduleName, int contentLength) {
		try {
			JSONObject jsonData = new JSONObject();

			JSONObject data = new JSONObject();

			data.put("module", moduleName);
			data.put("suggestedTextLength", contentLength);

			jsonData.put("json_data", data);

			return jsonData.toString();
		} catch (JSONException e) {
			return null;
		}
	}

	public static String forWorkflowChanges(ConfigurableWorkflowAction previousWorkflowAction){
		try {
			JSONObject jsonData = new JSONObject();
			JSONArray changesArr = new JSONArray();

			if(previousWorkflowAction != null){
				ConfigurableWorkflowStep workflowStep = previousWorkflowAction.getConfigurableWorkflowStep();

				JSONObject stepData = new JSONObject();
				stepData.put(S_KEY, "WORKFLOW_STEP_NAME");
				stepData.put(S_VALUE, workflowStep.getState());
				changesArr.put(stepData);

				if(workflowStep.isTranslationStep()){
					JSONObject langData = new JSONObject();
					langData.put(S_KEY, "TRANSLATION_LANGS");
					langData.put(S_VALUE, workflowStep.getLanguages().stream().map(IdentifiableMessagePointModel::getName).collect(Collectors.joining(",")));
					changesArr.put(langData);
				}
			}

			jsonData.put("json_data", changesArr);

			return jsonData.toString();
		} catch (JSONException e) {
			return null;
		}
	}

	public static String forReassign(String fromUserName, String toUserName){
		return "# " + ApplicationUtil.getMessage("page.label.assignee") + ": " + fromUserName + " > " + toUserName;
	}

	public static String forRename(String fromName, String toName){
		return "# " + ApplicationUtil.getMessage("page.label.name") + ": " + fromName + " > " + toName;
	}

	public static String forMessagePriority(String messageName, String prePriority, String currPriority, String preDeliveryType, String currDeliveryType){
		return "# " + ApplicationUtil.getMessage("page.label.priority") + " (" + messageName + ")"
				+ ": " + preDeliveryType + " " + prePriority +
				" > " + currDeliveryType + " " + currPriority;
	}

	public static String forTargetRules (String name, String fromValue, String toValue) {
		return "# " + name + ": " + fromValue + " > " + toValue;
	}
}
