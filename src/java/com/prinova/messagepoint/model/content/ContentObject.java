package com.prinova.messagepoint.model.content;

import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.controller.content.ContentObjectDynamicVariantViewController;
import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.diffs.ContentObjectDetailsDiffMapping;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.communication.LibraryItemUsageType;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.dialogue.DialogueConfiguration;
import com.prinova.messagepoint.model.font.*;
import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.model.message.MessageFlowType;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.tagcloud.TagCloud;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.targeting.TargetGroupInstance;
import com.prinova.messagepoint.model.targeting.Targetable;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.task.TaskType;
import com.prinova.messagepoint.model.tenant.TenantMetaData;
import com.prinova.messagepoint.model.util.ContentStyleUtils;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.model.version.*;
import com.prinova.messagepoint.model.workflow.*;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.model.wrapper.SearchResultVO;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.query.handler.PostQueryHandler;
import com.prinova.messagepoint.query.service.HibernatePaginationService;
import com.prinova.messagepoint.query.service.PaginationServiceResponse;
import com.prinova.messagepoint.reports.model.report.JobMessage;
import com.prinova.messagepoint.tag.layout.TxtFmtTag;
import com.prinova.messagepoint.util.*;
import com.prinova.messagepoint.util.jstree.TouchpointSelectionWrapper;
import com.prinova.messagepoint.wtu.Referencable;
import com.prinova.messagepoint.wtu.ReferencableObject;
import com.prinova.messagepoint.wtu.services.DirectReferencesFetchService;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;
import org.hibernate.Session;
import org.hibernate.annotations.Formula;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import javax.persistence.criteria.JoinType;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigInteger;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.controller.AsyncContentSearchController.SEARCH_TYPE_SMART_CONTENT;

public class ContentObject extends VersionedPrivateModelImpl implements Targetable, SegmentationAnalyzable, Referencable, ApprovableWithWfActions {

	private static final long serialVersionUID = 1220047058459325959L;

	public static final String REQ_PARM_CONTENT_OBJECT_ID 	= "contentObjectId";
	public static final String REQ_PARM_OBJECT_DNA 			= "objectDna";
	public static final String REQ_PARM_DATA_TYPE 			= "dataType";

	public static final int REQUEST_GOT_AN_ERROR			= -1;
	public static final int REQUEST_NOT_APPLICABLE			=  0;
	public static final int REQUEST_SUCCESSFUL     			=  1;

	public static final int OBJECT_TYPE_MESSAGE				=  0;	// 0000
	public static final int OBJECT_TYPE_LOCAL_SMART_TEXT	=  1;	// 0001
	public static final int OBJECT_TYPE_LOCAL_IMAGE			=  2;	// 0010
	public static final int OBJECT_TYPE_GLOBAL_SMART_TEXT	=  4;	// 0100
	public static final int OBJECT_TYPE_GLOBAL_IMAGE		=  8;	// 1000

	public static final int OBJECT_TYPE_ANY_LOCAL			=  3;	// 0011
	public static final int OBJECT_TYPE_ANY_GLOBAL			= 12;	// 1100

	public static final int DATA_TYPE_FOCUS_ON				=  0;	// 0000
	public static final int DATA_TYPE_WORKING				=  1;	// 0001
	public static final int DATA_TYPE_ACTIVE				=  2;	// 0010
	public static final int DATA_TYPE_ARCHIVED				=  4;	// 0100
	public static final int DATA_TYPE_WORKING_AND_ACTIVE	=  3;	// 0011
	public static final int DATA_TYPE_ACTIVE_AND_ARCHIVED	=  6;	// 0110
	public static final int DATA_TYPE_ALL					=  7;	// 0111
	public static final int DATA_TYPE_WORKING_OR_ACTIVE		= 11;	// 1011
	public static final int DATA_TYPE_ACTIVE_OR_ARCHIVED	= 14;	// 1110
	public static final int DATA_TYPE_ANY					= 15;	// 1111

	public static final int DELIVERY_TYPE_INDEPENDENT 		=  0;
	public static final int DELIVERY_TYPE_DEPENDENT 		=  1;	// For messages also known as DELIVERY_TYPE_MANDATORY
	public static final int DELIVERY_TYPE_MANDATORY 		=  1;	// For smart texts and images also known as DELIVERY_TYPE_DEPENDENT
	public static final int DELIVERY_TYPE_OPTIONAL 			=  2;
	public static final int DELIVERY_TYPE_ALL 				=  3;

	public static final int CLONE_CONTENT_NONE 				=  0;
	public static final int CLONE_CONTENT_ONLY_EXISTING_COA	=  1;
	public static final int CLONE_CONTENT_RESOLVE_COW_COA	=  2;

	public static final int USAGE_TYPE_CONNECTED           	=  2;

	public static final String MESSAGE_CODE_DELIVERY_TYPE_INDEPENDENT 	= "page.label.independent";
	public static final String MESSAGE_CODE_DELIVERY_TYPE_DEPENDENT 	= "page.label.dependent";

	private static final Log log = LogUtil.getLog(ContentObject.class);

    private Set<ContentObjectAssociation> 	contentObjectAssociations 	= new LinkedHashSet<>();

	// Following map contains data object of attributes, which can be modified / different for data type
	//
	// dataType   = 1 working data
	//				2 active data
	//				4 archived data
	//
	private Map<Integer, ContentObjectData> contentObjectDataTypeMap = new ConcurrentHashMap<>();

	// This focus determines which data type will be used if there is no explicit parameter
	//
	private int							focusOnDataType 		= ContentObject.DATA_TYPE_WORKING;

	private int						    objectType				= OBJECT_TYPE_MESSAGE;

	private ContentType 				contentType;
	private Integer						graphicTypeId			= 0;
	private Long						channelContextId;

	private boolean 					advanced				= false;
	private int 						usageTypeId 			= LibraryItemUsageType.ID_MESSAGE;

	// Content Object, which is not starting at Master level has owningTouchpointSelection != null
	// ContentObject.isVariantContentObject()
	//
	private TouchpointSelection 		owningTouchpointSelection = null;

	private boolean						removed					 = false;
	private boolean						suppressed				 = false;
	private boolean						onHold					 = false;
	private boolean						structuredContentEnabled = false;
	private boolean						variableContentEnabled	 = true;

	private Long 						lockedFor;

	private Zone 						zone 					= null;
	private Document					document				= null;
	private Set<Document> 				visibleDocuments 		= new HashSet<>();
	private Set<TouchpointCollection> 	touchpointCollections 	= new HashSet<>();

	private ContentObject				globalParentObject;
	private Date						lastGlobalParentObjectSyncDate;

	private boolean                     needRecalculateHash = true;
	private String                      sha256Hash;

	private Set<ObjectWorkflowActionAssociation> workflowActionAssociations = new HashSet<>();
	private boolean 					readyForApproval;
	private WorkflowState 				state;
	private Set<Task> 					tasks;

    @Formula("case document_id when null then dna else document_id || '-' || dna end")
    private String documentDna;


    // Public default constructor
	//
	public ContentObject() {}

	public ContentObject(User user)
	{

		ContentObjectData contentObjectData = new ContentObjectData();
		contentObjectData.setDataType(ContentObject.DATA_TYPE_WORKING);
		contentObjectData.setContentObject(this);
		contentObjectData.setStatus(VersionStatus.findById(VersionStatus.VERSION_WIP));

		contentObjectData.setCreatedBy(user.getId());
		contentObjectData.setCreated(new Date(System.currentTimeMillis()));

		this.setCreatedBy(user.getId());
		this.setCreated(contentObjectData.getCreated());

		contentObjectDataTypeMap.put(ContentObject.DATA_TYPE_WORKING, contentObjectData);

	}

	public ContentObject(String name, User user)
	{
		ContentObjectData contentObjectData = new ContentObjectData();
		contentObjectData.setDataType(ContentObject.DATA_TYPE_WORKING);
		contentObjectData.setContentObject(this);
		contentObjectData.setStatus(VersionStatus.findById(VersionStatus.VERSION_WIP));

		contentObjectData.setCreatedBy(user.getId());
		contentObjectData.setCreated(new Date(System.currentTimeMillis()));

		this.setName(name);
		this.setCreatedBy(user.getId());
		this.setCreated(contentObjectData.getCreated());

		this.save();

		contentObjectDataTypeMap.put(ContentObject.DATA_TYPE_WORKING, contentObjectData);

		contentObjectData.save();

		// Save data to HistoricalContentObjectData for history tracking purpose
		HistoricalContentObjectData histContentObjectData = HistoricalContentObjectData.create(this, contentObjectData.getGuid());
		histContentObjectData.save();
	}

	// Copy constructor (not public) for cloning
	//
	protected ContentObject(ContentObject cloneFrom, Document clonedDocument, int cloneContentType, Map<Long, String> languagesForSync, int cloneDataTypes)
	{
		super(cloneFrom);

//        String newContentObjectName = CloneHelper.getUnusedContentObjectName(cloneFrom, this, clonedDocument,true);
//        this.setName(newContentObjectName);

        this.setName(cloneFrom.getName());

		this.focusOnDataType = cloneFrom.focusOnDataType;

		this.objectType = cloneFrom.objectType;

        this.contentType = CloneHelper.assignConstObject(cloneFrom.getContentType());
        this.graphicTypeId = cloneFrom.graphicTypeId;
		this.channelContextId = cloneFrom.channelContextId;

        this.advanced = cloneFrom.advanced;
		this.usageTypeId = cloneFrom.usageTypeId;

		this.removed = cloneFrom.removed;
		this.suppressed = cloneFrom.suppressed;
		this.onHold = cloneFrom.onHold;
		this.structuredContentEnabled = cloneFrom.structuredContentEnabled;
		this.variableContentEnabled = cloneFrom.variableContentEnabled;

        if(this.objectType <= OBJECT_TYPE_ANY_LOCAL){
            if(clonedDocument != null) {
                this.document = clonedDocument;
            } else {
                this.document = cloneFrom.document;
            }
        }
        else if((this.objectType & OBJECT_TYPE_ANY_GLOBAL) != 0) {

            if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
                if(cloneFrom.visibleDocuments != null && !cloneFrom.visibleDocuments.isEmpty()){
                    this.visibleDocuments.addAll(cloneFrom.visibleDocuments);
                }
            } else {
                if(clonedDocument != null) {
                    CloneHelper.execInSaveSession(()->{
                        this.getVisibleDocuments().add(clonedDocument);
                    });
                }
            }
        }

        this.setCreatedBy(UserUtil.getPrincipalUserId());
        this.setCreated(DateUtil.now());

        if(cloneFrom.getObjectSchemaName().equals(getObjectSchemaName())) {
            boolean needNewDna = false;
            if(cloneFrom.isMessageOrTouchpointLocal() && clonedDocument != null) {
                if(cloneFrom.getDocument().getId() == clonedDocument.getId()) {
                    needNewDna = true;
                }
            }
            else if(cloneFrom.isGlobalContentObject()) {
                needNewDna = true;
            }
            if(needNewDna) {
                this.setDna(RandomGUID.getGUID());
            }
        }

        this.save();

        if(cloneDataTypes != DATA_TYPE_ALL || cloneFrom.hasWorkingData())
        {
            if(cloneDataTypes != 0) {
                long workflowStateId = (cloneFrom.getState() == null || cloneFrom.getState().getId() < WorkflowState.STATE_CONTENT) ?
                    WorkflowState.STATE_OVERVIEW : WorkflowState.STATE_CONTENT;

                User requestor = CloneHelper.getRequestor();

                CloneHelper.execInSaveSession(() -> {
                    this.setWorkflowAction(null);
                    this.setReadyForApproval(false);
                    this.setState(WorkflowState.findById(workflowStateId));
                    if (requestor != null) {
                        this.setLockedFor(requestor.getId());
                    }
                });
            }
        } else if(cloneFrom.hasActiveData()) {
            CloneHelper.execInSaveSession(()-> {
                ConfigurableWorkflowAction activeWorkflowAction = new ConfigurableWorkflowAction();
                activeWorkflowAction.setActive(true);
                activeWorkflowAction.setModel(this);
                activeWorkflowAction.setPreviousAction(null);
                activeWorkflowAction.setPreviousAction(null);
                activeWorkflowAction.setConfigurableWorkflowStep(null);
                activeWorkflowAction.setReleaseForApprovalDate(DateUtil.now());
                activeWorkflowAction.save();

                this.setWorkflowAction(activeWorkflowAction);
                this.setReadyForApproval(false);
                this.setState(WorkflowState.findById(WorkflowState.STATE_PRODUCTION));
            });
        }

		if((cloneFrom.isMessage() || cloneFrom.isLocalContentObject())) {
            TouchpointSelection sourceTouchpointSelection = cloneFrom.getOwningTouchpointSelection();
            if(clonedDocument != null) {
                if (sourceTouchpointSelection != null) {
                    String tsDna = sourceTouchpointSelection.getDna();
                    TouchpointSelection targetTouchpointSelection = CloneHelper.queryInSaveSession(() -> {
                        return clonedDocument.getTouchpointSelections().stream().filter(ts -> ts.getDna().equals(tsDna)).findFirst().orElse(null);
                    });
                    this.setOwningTouchpointSelection(targetTouchpointSelection);
                }
            } else if(cloneFrom.getObjectSchemaName().equals(this.getObjectSchemaName())) {
                this.setOwningTouchpointSelection(sourceTouchpointSelection);
            }
        }

		this.globalParentObject = CloneHelper.assign(cloneFrom.getGlobalParentObject());

		if(cloneContentType != CLONE_CONTENT_NONE) {
			this.sha256Hash = cloneFrom.sha256Hash;
			this.needRecalculateHash = false;
		}

		// Clone ContentObjectData according to requested cloneDataTypes
        List<Integer> dataTypes = new ArrayList<>(cloneFrom.getContentObjectDataTypeMap().keySet());
        Collections.sort(dataTypes, Collections.reverseOrder());
        for(int dataType : dataTypes) {
            if ((cloneDataTypes & dataType) > 0) {
            	// clone this data type only if it's requested
				ContentObjectData sourceData = cloneFrom.getContentObjectDataTypeMap().get(dataType);
				ContentObjectData clonedData = CloneHelper.clone(sourceData, o -> o.clone(this, clonedDocument, false, null, true));
                CloneHelper.execInSaveSession(()-> {
                    clonedData.save();
                    contentObjectDataTypeMap.put(dataType, clonedData);
                });

				// Save data to HistoricalContentObjectData for history tracking purpose
                CloneHelper.execInSaveSession(()->{
                    HistoricalContentObjectData histContentObjectData = HistoricalContentObjectData.create(clonedData.getContentObject(), clonedData.getGuid());
                    histContentObjectData.save();
                    if(dataType == DATA_TYPE_ACTIVE || dataType == DATA_TYPE_ARCHIVED) {
                        if (CloneHelper.getIsSynchronizing()) {
                            histContentObjectData.setVersionActivatedActionTypeId(HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_SYNC);
                        } else if(CloneHelper.getIsCloningGlobal()) {
                            histContentObjectData.setVersionActivatedActionTypeId(HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_CLONE);
                        } else {
                            histContentObjectData.setVersionActivatedActionTypeId(HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_DEFAULT);
                        }
                        histContentObjectData.setDataType(ContentObject.DATA_TYPE_ACTIVE);
                        histContentObjectData.setVersionActivated(DateUtil.now());
                        histContentObjectData.setVersionActivatedBy(UserUtil.getPrincipalUserId());
                        histContentObjectData.save();
                    }
                    if(dataType == DATA_TYPE_ARCHIVED) {
                        if (CloneHelper.getIsSynchronizing()) {
                            histContentObjectData.setVersionArchivedActionTypeId(HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_SYNC);
                        } else if(CloneHelper.getIsCloningGlobal()) {
                            histContentObjectData.setVersionArchivedActionTypeId(HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_CLONE);
                        } else {
                            histContentObjectData.setVersionArchivedActionTypeId(HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_DEFAULT);
                        }
                        histContentObjectData.setDataType(ContentObject.DATA_TYPE_ARCHIVED);
                        histContentObjectData.setVersionArchived(DateUtil.now());
                        histContentObjectData.setVersionArchivedBy(UserUtil.getPrincipalUserId());
                        histContentObjectData.save();
                    }
                });
			}
        }
/*
        if(this.getCreated() != null && cloneFrom.getCreated() != null) {
            this.setCreated(cloneFrom.getCreated());
            this.save();
        }
*/
        String externalId = cloneFrom.getExternalIdOrNull();
        if (externalId != null)
        {
            CloneHelper.execInSaveSession(()->{
                TenantMetaData tmd = new TenantMetaData();
                tmd.setInternalId(this.getId());
                tmd.setExternalId(externalId);
                tmd.setModelSignature(TenantMetaData.MODEL_SIGN_MESSAGE);

                if (CloneHelper.getRequestor() != null)
                {
                    tmd.setCreatedBy(CloneHelper.getRequestor().getId());
                    tmd.setUpdatedBy(CloneHelper.getRequestor().getId());
                }
                else
                {
                    tmd.setCreatedBy(UserUtil.getPrincipalUserId());
                    tmd.setUpdatedBy(UserUtil.getPrincipalUserId());
                }

                tmd.save();
            });
        }

        // Clone zone

		if (cloneFrom.zone != null)
		{
			if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) { // This code block is required only if cloning in same schema
                String zoneDna = cloneFrom.zone.getDna();
				Zone clonedZone = clonedDocument == null ? cloneFrom.zone : Zone.findByDnaAndDocument(zoneDna, clonedDocument);
				if (clonedZone != null) {
					clonedZone.addContentObject(this);
					clonedZone.save();
					this.zone = clonedZone;
					ContentObjectZonePriority.addActiveAndWorkingDataToTail(this, clonedZone);
				}

			} else { // Cross schema cloning
				Zone clonedZone = CloneHelper.assignAlreadyClonedObject(cloneFrom.zone);
				if (clonedZone != null) {
					CloneHelper.execInSaveSession(()->{
						clonedZone.addContentObject(this);
						clonedZone.save();
					});
					this.zone = clonedZone;
					CloneHelper.execInSaveSession(()->{
						ContentObjectZonePriority.addActiveAndWorkingDataToTail(this, clonedZone);
					});
				}
			}
		}

        // Clone dynamic tree
        Map<Long, ParameterGroupTreeNode> sourcePgtnToClonedPgtnMap = new HashMap<>();

        for(int dataType : dataTypes) {
            if ((cloneDataTypes & dataType) > 0) {
                // clone dynamic tree for this data type only if it's requested
                if(isDynamicVariantEnabled(dataType)) {
                    List<ParameterGroupTreeNode> sourceTreeNodes = ParameterGroupTreeNode.findAllNodesForDynamicAsset(cloneFrom, dataType);

                    for(ParameterGroupTreeNode sourceTreeNode : sourceTreeNodes) {
                        ParameterGroupTreeNode clonedTreeNode = CloneHelper.clone(sourceTreeNode);
                        CloneHelper.execInSaveSession(() -> {
                            clonedTreeNode.setContentObject(this);
                            clonedTreeNode.setDataType(dataType);
                            clonedTreeNode.save();
                        });
                        sourcePgtnToClonedPgtnMap.put(sourceTreeNode.getId(), clonedTreeNode);
                    }

                }
            }
        }

        CloneHelper.execInSaveSession(() -> {
            SyncTouchpointUtil.setupDynamicPgtnOriginObjects(sourcePgtnToClonedPgtnMap.values());
        });

		// Clone ContentObjectAssociations

		if(cloneContentType != CLONE_CONTENT_NONE) {
            cloneContentFrom(cloneFrom, clonedDocument, languagesForSync, cloneDataTypes, cloneContentType, sourcePgtnToClonedPgtnMap);
        }
	}

	private void cloneContentFrom(ContentObject cloneFrom, Document clonedDocument, Map<Long, String> languagesForSync, int cloneDataTypes, int cloneContentType, Map<Long, ParameterGroupTreeNode> sourcePgtnToClonedPgtnMap) {
		Long defaultMessagepointLocaleId = CloneHelper.queryInSaveSession(()->MessagepointLocale.getDefaultSystemLanguageLocale().getId());
		String defaultMessagepointLocaleCode = CloneHelper.queryInSaveSession(()->MessagepointLocale.getDefaultSystemLanguageLocale().getCode());
		if(clonedDocument != null && (cloneFrom.isMessage() || cloneFrom.isTouchpointLocal())) {
            defaultMessagepointLocaleId = CloneHelper.queryInSaveSession(()->clonedDocument.getDefaultTouchpointLanguage().getMessagepointLocale().getId());
            defaultMessagepointLocaleCode = CloneHelper.queryInSaveSession(()->clonedDocument.getDefaultTouchpointLanguage().getMessagepointLocale().getCode());
        }
/*
        List<ContentObjectAssociation> sortedContentObjectAssociations = new ArrayList<>(cloneFrom.getContentObjectAssociations());
        Collections.sort(sortedContentObjectAssociations, new ContentObjectAssociationIdComparator());
*/
//		boolean disableStructuredCOWResolution = cloneContentType != CLONE_CONTENT_RESOLVE_COW_COA; // If we are cloning only existing COA, we should not try to get active data type
        List<ParameterGroupTreeNode> touchpointVariants = new ArrayList<>();
        if(cloneFrom.isStructuredContentEnabled()) {
            TouchpointSelection owningVariant = cloneFrom.getOwningTouchpointSelection();
            if(owningVariant == null) {
                owningVariant = cloneFrom.getDocument().getMasterTouchpointSelection();
            }
            if(owningVariant != null) {
                ParameterGroupTreeNode owningVariantPGTN = owningVariant.getParameterGroupTreeNode();
                touchpointVariants.add(owningVariantPGTN);
                List<ParameterGroupTreeNode> descendents = ParameterGroupTreeNode.findAllDescendentNodes(touchpointVariants);
                touchpointVariants.addAll(descendents);
                touchpointVariants.removeIf(v->v.getParentNode() == null || v.getId() == owningVariantPGTN.getId());
            }
        }

        Map<Integer, List<ContentObjectAssociation>> sortedContentObjectAssociations = new LinkedHashMap<>();
        boolean needCOWResolution = true;
        if((DATA_TYPE_ARCHIVED & cloneDataTypes) != 0) {
            if(cloneFrom.hasArchivedData()) {
                cloneFrom.setFocusOnDataType(DATA_TYPE_ARCHIVED);
                List<ContentObjectAssociation> archivedContentObjectAssociations = ContentObjectAssociation.findAllByContentObjectAndParameters(cloneFrom, DATA_TYPE_ARCHIVED, null, null, null, false, true, true, true, false);
                if (archivedContentObjectAssociations != null && !archivedContentObjectAssociations.isEmpty()) {
                    sortedContentObjectAssociations.put(DATA_TYPE_ARCHIVED, archivedContentObjectAssociations);
                    needCOWResolution = false;
                }
            }
        }

        if((DATA_TYPE_ACTIVE & cloneDataTypes) != 0) {
            if(cloneFrom.hasActiveData()) {
                cloneFrom.setFocusOnDataType(DATA_TYPE_ACTIVE);
                if((isStructuredContentEnabled() || isDynamicVariantEnabled(DATA_TYPE_ACTIVE)) && needCOWResolution) { // Full resolve
                    cloneFrom.setFocusOnDataType(DATA_TYPE_ACTIVE);
                    List<ContentObjectAssociation> activeContentObjectAssociations = new ArrayList<>();
                    List<ContentObjectAssociation> activeMasterContentObjectAssociations = ContentObjectAssociation.findAllByContentObjectAndParameters(cloneFrom, DATA_TYPE_ACTIVE, null, null, null, false, false, true, false, false);
                    if(activeMasterContentObjectAssociations != null && !activeMasterContentObjectAssociations.isEmpty()) {
                        activeContentObjectAssociations.addAll(activeMasterContentObjectAssociations);
                    }

                    List<ParameterGroupTreeNode> variants = touchpointVariants;

                    if(cloneFrom.isDynamicVariantEnabled(DATA_TYPE_ACTIVE)) {
                        List<ParameterGroupTreeNode> contentObjectVariants = ParameterGroupTreeNode.findAllNodesForDynamicAsset(cloneFrom, DATA_TYPE_ACTIVE);
                        variants = contentObjectVariants;
                    }

                    for (ParameterGroupTreeNode parameterGroupTreeNode : variants) {
                        cloneFrom.setFocusOnDataType(DATA_TYPE_ACTIVE);
                        List<ContentObjectAssociation> activeVariantContentObjectAssociations = ContentObjectAssociation.findAllByContentObjectAndParameters(cloneFrom, DATA_TYPE_ACTIVE, parameterGroupTreeNode, null, null, false, false, true, false, false);
                        if (activeVariantContentObjectAssociations != null && !activeVariantContentObjectAssociations.isEmpty()) {
                            activeContentObjectAssociations.addAll(activeVariantContentObjectAssociations);
                        }
                    }

                    if (activeContentObjectAssociations != null && !activeContentObjectAssociations.isEmpty()) {
                        sortedContentObjectAssociations.put(DATA_TYPE_ACTIVE, activeContentObjectAssociations);
                        needCOWResolution = false;
                    }
                }
                else { // just take whatever datatype is active
                    List<ContentObjectAssociation> activeContentObjectAssociations = ContentObjectAssociation.findAllByContentObjectAndParameters(cloneFrom, DATA_TYPE_ACTIVE, null, null, null, false, true, true, true, false);
                    if (activeContentObjectAssociations != null && !activeContentObjectAssociations.isEmpty()) {
                        sortedContentObjectAssociations.put(DATA_TYPE_ACTIVE, activeContentObjectAssociations);
                        needCOWResolution = false;
                    }
                }
            }
        }

        if((DATA_TYPE_WORKING & cloneDataTypes) != 0) {
            if(cloneFrom.hasWorkingData()) {
                cloneFrom.setFocusOnDataType(DATA_TYPE_WORKING);
                if((isStructuredContentEnabled() || isDynamicVariantEnabled(DATA_TYPE_WORKING)) && needCOWResolution) { // need full resolve
                    cloneFrom.setFocusOnDataType(DATA_TYPE_WORKING);
                    List<ContentObjectAssociation> workingContentObjectAssociations = new ArrayList<>();
                    List<ContentObjectAssociation> workingMasterContentObjectAssociations = ContentObjectAssociation.findAllByContentObjectAndParameters(cloneFrom, DATA_TYPE_WORKING, null, null, null, false, false, true, false, false);
                    if(workingMasterContentObjectAssociations != null && !workingMasterContentObjectAssociations.isEmpty()) {
                        workingContentObjectAssociations.addAll(workingMasterContentObjectAssociations);
                    }
                    List<ParameterGroupTreeNode> variants = touchpointVariants;
                    if(isDynamicVariantEnabled(DATA_TYPE_WORKING)) {
                        List<ParameterGroupTreeNode> workingParameterGroupTreeNodes = ParameterGroupTreeNode.findAllNodesForDynamicAsset(cloneFrom, DATA_TYPE_WORKING);
                        variants = workingParameterGroupTreeNodes;
                    }

                    for (ParameterGroupTreeNode parameterGroupTreeNode : variants) {
                        cloneFrom.setFocusOnDataType(DATA_TYPE_WORKING);
                        List<ContentObjectAssociation> workingVariantContentObjectAssociations = ContentObjectAssociation.findAllByContentObjectAndParameters(cloneFrom, DATA_TYPE_WORKING, parameterGroupTreeNode, null, null, false, false, true, false, false);
                        if (workingVariantContentObjectAssociations != null && !workingVariantContentObjectAssociations.isEmpty()) {
                            workingContentObjectAssociations.addAll(workingVariantContentObjectAssociations);
                        }
                    }

                    if (workingContentObjectAssociations != null && !workingContentObjectAssociations.isEmpty()) {
                        sortedContentObjectAssociations.put(DATA_TYPE_WORKING, workingContentObjectAssociations);
                    }
                }
                else { // just take whatever datatype is working
                    List<ContentObjectAssociation> workingContentObjectAssociations = ContentObjectAssociation.findAllByContentObjectAndParameters(cloneFrom, DATA_TYPE_WORKING, null, null, null, false, true, true, true, false);
                    if (workingContentObjectAssociations != null && !workingContentObjectAssociations.isEmpty()) {
                        sortedContentObjectAssociations.put(DATA_TYPE_WORKING, workingContentObjectAssociations);
                    }
                }
            }
        }

//        Map<Long, ParameterGroupTreeNode> sourcePgtnToClonedPgtnMap = new HashMap<>();

        Map<String, TouchpointSelection> dnaToTargetTouchpointSelectionMap = new HashMap<>();
        Map<String, ZonePart> dnaToZonePartMap = new HashMap<>();

        if(!this.isGlobalContentObject()) {
            CloneHelper.execInSaveSession(() -> {
                if (clonedDocument != null && clonedDocument.isEnabledForVariation()) {
                    dnaToTargetTouchpointSelectionMap.putAll(
                            clonedDocument.getTouchpointSelections()
                                    .stream()
//                                    .filter(ts->! ts.isDeleted())
//                                    .map(ts -> ts.getParameterGroupTreeNode())
                                    .collect(Collectors.toMap(ts -> ts.getParameterGroupTreeNode().getDna(),
                                            Function.identity(),
                                            (p1, p2)->{
                                            if(! p1.isDeleted()) {
                                                return p1;
                                            }
                                            if(! p2.isDeleted()) {

                                            }
                                            return p1;
                                        })));
                }

                if (clonedDocument != null) {
                    for (Zone zone : clonedDocument.getZones()) {
                        if (zone.isMultipart()) {
                            for (ZonePart zonePart : zone.getParts()) {
                                dnaToZonePartMap.put(zonePart.getDna(), zonePart);
                            }
                        }
                    }
                }
            });
        }

        for(Integer dataType : sortedContentObjectAssociations.keySet()) {
            List<ContentObjectAssociation> sortedContentObjectAssociationsForDataType = sortedContentObjectAssociations.get(dataType);
            // clone this data type only if it's requested
            if ((cloneDataTypes & dataType) > 0 || cloneContentType == CLONE_CONTENT_RESOLVE_COW_COA) {
                for(ContentObjectAssociation sourceContentObjectAssociation : sortedContentObjectAssociationsForDataType) {
                    boolean needCloneContent = false;
                    long coaLocaleId = sourceContentObjectAssociation.getMessagepointLocale() == null ? 0 : sourceContentObjectAssociation.getMessagepointLocale().getId();
                    String coaLocaleCode = sourceContentObjectAssociation.getMessagepointLocale() == null ? "" : sourceContentObjectAssociation.getMessagepointLocale().getCode();
                    if (coaLocaleId == defaultMessagepointLocaleId || coaLocaleCode.equals(defaultMessagepointLocaleCode) || languagesForSync == null) {
                        needCloneContent = true;
                    } else if (languagesForSync != null) {
                        if(languagesForSync.containsKey(coaLocaleId) || languagesForSync.containsValue(coaLocaleCode)) {
                            needCloneContent = true;
                        }
                    }

                    //if (needCloneContent)
                    {
                        int sourceDataType = sourceContentObjectAssociation.getDataType();
                        ParameterGroupTreeNode sourceContentObjectPGTreeNode = sourceContentObjectAssociation.getContentObjectPGTreeNode();
                        ParameterGroupTreeNode sourceReferencingContentObjectPGTreeNode = sourceContentObjectAssociation.getReferencingContentObjectPGTreeNode();
                        ParameterGroupTreeNode sourceContentObjectPGTreeNodeForThisDataType =
                            sourceContentObjectPGTreeNode == null || sourceDataType == dataType ?
                                sourceContentObjectPGTreeNode :
                                ParameterGroupTreeNode.findSpecificVersionOfDynamicVariant(sourceContentObjectPGTreeNode, dataType);
                        ParameterGroupTreeNode sourceReferencingContentObjectPGTreeNodeForThisDataType =
                            sourceReferencingContentObjectPGTreeNode == null || sourceDataType == dataType ?
                                sourceReferencingContentObjectPGTreeNode :
                                ParameterGroupTreeNode.findSpecificVersionOfDynamicVariant(sourceReferencingContentObjectPGTreeNode, dataType);
                        ParameterGroupTreeNode targetContentObjectPGTreeNode = sourceContentObjectPGTreeNodeForThisDataType == null ? null : sourcePgtnToClonedPgtnMap.get(sourceContentObjectPGTreeNodeForThisDataType.getId());
                        ParameterGroupTreeNode targetReferencingContentObjectPGTreeNode = sourceReferencingContentObjectPGTreeNodeForThisDataType == null ? null : sourcePgtnToClonedPgtnMap.get(sourceReferencingContentObjectPGTreeNodeForThisDataType.getId());
                        boolean needCloneContentFinal = needCloneContent;
                        ContentObjectAssociation clonedContentObjectAssociation = CloneHelper.clone(sourceContentObjectAssociation, o -> o.clone(this, needCloneContentFinal));
                        clonedContentObjectAssociation.setDataType(dataType); // sourceContentObjectAssociation might be resolved from other data version, so its data type would be different from expected data type
                        clonedContentObjectAssociation.setContentObjectPGTreeNode(targetContentObjectPGTreeNode);
                        clonedContentObjectAssociation.setReferencingContentObjectPGTreeNode(targetReferencingContentObjectPGTreeNode);

                        if (!this.isGlobalContentObject()) {
                            if (sourceContentObjectAssociation.getTouchpointPGTreeNode() != null) {
                                if (dnaToTargetTouchpointSelectionMap.containsKey(sourceContentObjectAssociation.getTouchpointPGTreeNode().getDna())) { // This contains data only when clonedDocument != null
                                    clonedContentObjectAssociation.setTouchpointPGTreeNode(dnaToTargetTouchpointSelectionMap.get(sourceContentObjectAssociation.getTouchpointPGTreeNode().getDna()).getParameterGroupTreeNode());
                                }
                                else {
                                    clonedContentObjectAssociation.setTouchpointPGTreeNode(sourceContentObjectAssociation.getTouchpointPGTreeNode());
                                }
                            }

                            if (sourceContentObjectAssociation.getReferencingTouchpointPGTreeNode() != null) {
                                if (dnaToTargetTouchpointSelectionMap.containsKey(sourceContentObjectAssociation.getReferencingTouchpointPGTreeNode().getDna())) { // This contains data only when clonedDocument != null
                                    clonedContentObjectAssociation.setReferencingTouchpointPGTreeNode(dnaToTargetTouchpointSelectionMap.get(sourceContentObjectAssociation.getReferencingTouchpointPGTreeNode().getDna()).getParameterGroupTreeNode());
                                }
                                else {
                                    clonedContentObjectAssociation.setReferencingTouchpointPGTreeNode(sourceContentObjectAssociation.getReferencingTouchpointPGTreeNode());
                                }
                            }

                            if (sourceContentObjectAssociation.getZonePart() != null) {
                                if (dnaToZonePartMap.containsKey(sourceContentObjectAssociation.getZonePart().getDna())) { // This contains data only when clonedDocument != null
                                    clonedContentObjectAssociation.setZonePart(dnaToZonePartMap.get(sourceContentObjectAssociation.getZonePart().getDna()));
                                }
                                else {
                                    clonedContentObjectAssociation.setZonePart(sourceContentObjectAssociation.getZonePart());
                                }
                            }
                        }
    /*
                            if (cloneContentType == CLONE_CONTENT_RESOLVE_COW_COA)
                            {
                                if (cloneDataTypes == DATA_TYPE_WORKING || cloneDataTypes == DATA_TYPE_ACTIVE || cloneDataTypes == DATA_TYPE_ARCHIVED)
                                {
                                    clonedContentObjectAssociation.setDataType(cloneDataTypes);
                                }
                            }
    */
                        CloneHelper.execInSaveSession(() -> {
                            clonedContentObjectAssociation.save();
                            this.getContentObjectAssociations().add(clonedContentObjectAssociation);
                        });
                    }
                }
            }
        }
	}

	public ContentObject clone(Document document)
	{
		return clone(document, true, null);
	}

	public ContentObject clone(Document document, boolean cloneContent, Map<Long, String> languagesForSync)
	{
        return clone(document, cloneContent ? ContentObject.CLONE_CONTENT_ONLY_EXISTING_COA : ContentObject.CLONE_CONTENT_NONE, languagesForSync, ContentObject.DATA_TYPE_ALL);
	}

    public ContentObject clone(Document document, int cloneContentType, Map<Long, String> languagesForSync, int dataTypes)
    {
        ContentObject clone = new ContentObject(this, document, cloneContentType, languagesForSync, dataTypes);
        if(document != null && !document.getObjectSchemaName().equals(this.getObjectSchemaName())) {
            if(document != null && (this.document == null || document.getId() != this.document.getId())) {
                clone.cloneDna();
            }
        }
        return clone;
    }


    public ContentObject clone(int dataTypes)
	{
		return new ContentObject(this, this.getDocument(), dataTypes < DATA_TYPE_ARCHIVED ? ContentObject.CLONE_CONTENT_RESOLVE_COW_COA : ContentObject.CLONE_CONTENT_ONLY_EXISTING_COA, null, dataTypes);
	}

	// Common overrided methods
	//
	@Override
	public Object clone()
	{
		return new ContentObject(this, this.getDocument(), ContentObject.CLONE_CONTENT_ONLY_EXISTING_COA, null, ContentObject.DATA_TYPE_ALL);
	}

	@Override
	public int hashCode() {
		return (getObjectSchemaName() + " "  + getClass().getName() + " " + getId() + " " + getGuid()).hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!(obj instanceof ContentObject))
			return false;
		final ContentObject other = (ContentObject) obj;
        if(!getObjectSchemaName().equals(other.getObjectSchemaName()))
            return false;
		return (getGuid().equals(other.getGuid()));
	}

	@Override
	public void preSave(Boolean isNew) {
		super.preSave(isNew);
		if(needRecalculateHash) {
			makeHash(false);
		}
	}

	@Override
	public void save() {
		super.save();
	}

	public ContentObjectDetailsDiffMapping getDetailsDiffMapping(){
		ContentObjectDetailsDiffMapping detailsDiffMapping = new ContentObjectDetailsDiffMapping();
		detailsDiffMapping.initProperties(this);

		return detailsDiffMapping;
	}

	@Override
	public Map<String, Object> getAttributesMap() {
		Map<String, Object> attributesMap = super.getAttributesMap();

		attributesMap.put("page.label.attribute.name", name);

		if(description != null && (! description.isEmpty())) {
			attributesMap.put("page.label.attribute.description", description);
		}

        if(isGlobalSmartCanvas()) {
            if (contentType.isSharedFreeform()) {
                attributesMap.put("page.label.attribute.is.smart.canvas", true);
            }
        }

        if(isLocalSmartCanvas()) {
            if (contentType.isSharedFreeform()) {
                attributesMap.put("page.label.attribute.is.local.smart.canvas", true);
            }
        }

		if(contentType != null) {
			attributesMap.put("page.label.attribute.content.type", contentType.getLocaledString());
		}

		if(graphicTypeId != null && graphicTypeId.intValue() != 0) {
			SubContentType subContentType = SubContentType.findById(graphicTypeId);
			if(subContentType != null) {
				attributesMap.put("page.label.attribute.graphic.type", subContentType.getLocaledString());
			}
		}

		if(channelContextId != null && channelContextId.longValue() > 0) {
			Channel channel = Channel.findById(channelContextId);
			if(channel != null) {
				attributesMap.put("page.label.attribute.channel.context", channel.getName());
			}
		}

		attributesMap.put("page.label.attribute.removed",  						"" + removed);
		attributesMap.put("page.label.attribute.suppressed",  					"" + suppressed);
		attributesMap.put("page.label.attribute.on.hold",      					"" + onHold);

		if(globalParentObject != null) {
			attributesMap.put("page.label.attribute.global.smart.text.name", 	globalParentObject.getName());
			attributesMap.put("page.label.attribute.global.smart.text.dna", 	globalParentObject.getDna());
		}

		attributesMap.put("page.label.attribute.dna", getDna());

        if(! isGlobalContentObject()) {
            attributesMap.put("page.label.attribute.external.id", getExternalId());
        }

//		attributesMap.put("page.label.attribute.status", getStatus().getLocaledString());

		if(zone != null) {
            String zoneName = zone.getFriendlyName();
            if(zoneName == null || zoneName.isEmpty() || zoneName.replaceAll("\\s+", "").isEmpty()) zoneName = zone.getName();
			attributesMap.put("page.label.attribute.zone.name", zoneName);
			attributesMap.put("page.label.attribute.zone.dna", zone.getDna());
		}

        if(isMessage() || isLocalContentObject()) {
            TouchpointSelection owningVariant = owningTouchpointSelection;

            if (owningVariant == null) {
                if (document.isEnabledForVariation()) {
                    owningVariant = document.getMasterTouchpointSelection();

                }
            }

            if (owningVariant != null) {
                attributesMap.put("page.label.attribute.owning.touchpoint.variant.name", owningVariant.getName());
                if (owningTouchpointSelection != null) {
                    attributesMap.put("page.label.attribute.owning.touchpoint.variant.dna", owningVariant.getDna());
                }
            }
        }

        List<String> versions = new ArrayList<>();
        if(hasArchivedData()) {
            versions.add("Archived");
        }
        if(hasActiveData()) {
            versions.add("Active");
        }
        if(hasWorkingData()) {
            versions.add("Working");
        }

        if(!versions.isEmpty()) {
            attributesMap.put("page.label.attribute.versions", versions.stream().sequential().collect(Collectors.joining(",")));
        }

        attributesMap.put("page.label.attribute.model.hash", sha256Hash);

		return attributesMap;
	}


	public void makeDataHash(boolean isAlgorithmChanged) {
        for(ContentObjectData contentObjectData : getContentObjectDataTypeMap().values()) {
            if(contentObjectData.getDataType() != DATA_TYPE_WORKING || contentObjectData.getStatus().getId() != VersionStatus.VERSION_REMOVED) {
                contentObjectData.makeHash(isAlgorithmChanged);
            }
        }
    }
	// Hash calculation TODO
	//
	public void makeHash(boolean isAlgorithmChanged) {
        makeDataHash(isAlgorithmChanged);

		StringBuilder hashDataStringBuilder = new StringBuilder();

		hashDataStringBuilder.append("message");

		if(name != null) hashDataStringBuilder.append(" name:").append(name);
		if(description != null && ! description.isEmpty()) hashDataStringBuilder.append(" description:").append(description);

		if (contentType != null)
			hashDataStringBuilder.append(" contentType:").append(contentType.getId());

		if(graphicTypeId != null) {
			hashDataStringBuilder.append(" graphicTypeId:").append(graphicTypeId);
		}

		if(channelContextId != null) {
			hashDataStringBuilder.append(" channelContextId:").append(channelContextId);
		}

		hashDataStringBuilder.append(" removed:").append(removed);
		hashDataStringBuilder.append(" suppressed:").append(suppressed);
		hashDataStringBuilder.append(" onHold:").append(onHold);

		if (globalParentObject != null) {
			hashDataStringBuilder.append(" globalContentObject:").append(globalParentObject.getDna());
		}

		if(zone != null) {
			hashDataStringBuilder.append(" zoneName:").append(zone.getName());
			hashDataStringBuilder.append(" zoneDna:").append(zone.getDna());
		}

        if(owningTouchpointSelection != null) {
            hashDataStringBuilder.append(" variantName:").append(owningTouchpointSelection.getName());
            hashDataStringBuilder.append(" variantDna:").append(owningTouchpointSelection.getDna());
        }

		/** TODO
		hashDataStringBuilder.append(" status:" + getStatus().getId());
		hashDataStringBuilder.append(" dna:" + getDna());
		hashDataStringBuilder.append(" externalId:" + getExternalId());

		 if(! getVersionMappings().isEmpty()) {
		 if(this.isDynamicVariantEnabled()) {
		 String selectionData = SyncTouchpointUtil.getSelectionData(this);
		 if(selectionData != null) {
		 hashDataStringBuilder.append(" selection.data:" + selectionData);
		 }
		 }
		 }
		 **/

        String objectHashKey = getObjectHashKey();
        sha256Hash = calculateSha256Hash(objectHashKey, isAlgorithmChanged, sha256Hash, hashDataStringBuilder.toString());
	}


	// Implement not used abstract methods
	//

	public Set<ModelVersionMapping> getVersionMappings() {
		return null;
	}
	public ModelVersionMapping newVersionMapping() { return null; }
	public VersionedInstance getWorkingCopy()  {
		return null;
	}
	public ModelVersionMapping getWorkingCopyVersionInfo()  {
		return null;
	}
	public VersionedInstance getProduction()   {
		return null;
	}
	public ModelVersionMapping getProductionVersionInfo() { return null; }
	public ModelVersionMapping getLatestArchivedVersionInfo()   {
		return null;
	}


	// Direct getters and setters
	//

	public Set<ContentObjectAssociation> getContentObjectAssociations() {
		return contentObjectAssociations;
	}
	public void setContentObjectAssociations(Set<ContentObjectAssociation> contentObjectAssociations) {
		this.contentObjectAssociations = contentObjectAssociations;
	}

	public Map<Integer, ContentObjectData> getContentObjectDataTypeMap() {
		return contentObjectDataTypeMap;
	}
	public void setContentObjectDataTypeMap(Map<Integer, ContentObjectData> contentObjectDataTypeMap) {
		this.contentObjectDataTypeMap = contentObjectDataTypeMap;

		this.normalizationRelatedToUsageTypeId();
	}

	public int getFocusOnDataType() {
		return focusOnDataType;
	}
	public void setFocusOnDataType(int focusOnDataType) {
		this.focusOnDataType = focusOnDataType;
	}

	public int setFocusOnDataTypeCheckAndAdjust(int focusOnDataType) {
		this.focusOnDataType = focusOnDataType;
		return checkAndAdjustFocus();
	}

	public String getObjectTypeForDisplay(){
		switch(this.objectType){
			case ContentObject.OBJECT_TYPE_MESSAGE:
				return ApplicationUtil.getMessage("page.label.message");
			case ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT:
				return ApplicationUtil.getMessage("page.label.local.smart.text");
			case ContentObject.OBJECT_TYPE_LOCAL_IMAGE:
				return ApplicationUtil.getMessage("page.label.local.image");
			case ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT:
				return ApplicationUtil.getMessage("page.label.smart.text");
			case ContentObject.OBJECT_TYPE_GLOBAL_IMAGE:
				return ApplicationUtil.getMessage("page.label.image");
			default:
				return "";
		}
	}

	public int getObjectType() {
		return objectType;
	}
	public void setObjectType(int objectType) {
		this.objectType = objectType;
	}

	public TouchpointSelection getOwningTouchpointSelection() {
		return owningTouchpointSelection;
	}
	public void setOwningTouchpointSelection(TouchpointSelection tpSelection) {
		this.owningTouchpointSelection = tpSelection;
	}

	public boolean getIsRemoved() {
		return isRemoved();
	}
	public boolean isRemoved() {
		return this.removed;
	}
	public void setRemoved(boolean removed) {
		this.removed = removed;
	}

	public boolean getIsSuppressed() {
		return isSuppressed();
	}
	public boolean isSuppressed() {
		return this.suppressed;
	}
	public void setSuppressed(boolean suppressed) {
		this.suppressed = suppressed;
	}

	public boolean getIsOnHold() {
		return isOnHold();
	}
	public boolean isOnHold() {
		return this.onHold;
	}
	public void setOnHold(boolean onHold) {
		this.onHold = onHold;
	}

	public boolean isStructuredContentEnabled() {
		return structuredContentEnabled;
	}
	public void setStructuredContentEnabled(boolean structuredContentEnabled) {
		this.structuredContentEnabled = structuredContentEnabled;
	}

	public boolean isVariableContentEnabled() {
		return variableContentEnabled;
	}
	public void setVariableContentEnabled(boolean variableContentEnabled) {
		this.variableContentEnabled = variableContentEnabled;
	}

	public Long getLockedFor() {
		return lockedFor;
	}
	public void setLockedFor(Long lockedFor) {
		this.lockedFor = lockedFor;
	}

	public Zone getZone() {
		return zone;
	}
	public void setZone(Zone zone) {
		this.zone = zone;
	}

	public Document getDocument() {
		return document;
	}
	public void setDocument(Document document) {
		this.document = document;
	}

	public Set<Document> getVisibleDocuments() {
		return this.visibleDocuments;
	}
	public void setVisibleDocuments(Set<Document> visibleDocuments) {
		this.visibleDocuments = visibleDocuments;
	}

	public Set<TouchpointCollection> getTouchpointCollections() {
		return touchpointCollections;
	}
	public void setTouchpointCollections(Set<TouchpointCollection> touchpointCollections) {
		this.touchpointCollections = touchpointCollections;
	}

	public long getContentTypeId() {
		if (contentType != null)
			return contentType.getId();
		return 0;
	}

	public long getContentTypeIdForJobPacker() {
		if ( contentType != null )
		{
			if ( contentType.isText() || contentType.isMarkup() || contentType.isSharedFreeform() )
				return 1;
			else if ( contentType.isGraphic() )
				return 2;
			else if ( contentType.isMultipart() )
				return 4;
			else if ( contentType.isVideo() )
				return 5;
		}
		return 0;
	}

	public ContentType getContentType() {
		return contentType;
	}
	public void setContentType(ContentType contentType) {
		this.contentType = contentType;
	}

	public Integer getGraphicTypeId() { return graphicTypeId; }
	public void setGraphicTypeId(Integer graphicTypeId) {
		this.graphicTypeId = graphicTypeId;
	}

	public Long getChannelContextId() {
		return channelContextId;
	}
	public void setChannelContextId(Long channelContextId) {
		this.channelContextId = channelContextId;
	}

	public boolean isAdvanced() {
		return advanced;
	}
	public void setAdvanced(boolean advanced) {
		this.advanced = advanced;
	}

	public int getUsageTypeId() {
		return usageTypeId;
	}
	public void setUsageTypeId(int usageTypeId) {
		this.usageTypeId = usageTypeId;
		this.normalizationRelatedToUsageTypeId();
	}

	private void normalizationRelatedToUsageTypeId() {
		if (usageTypeId != USAGE_TYPE_CONNECTED) {
			return;
		}

		ContentObjectData tmpContentObjectData = this.getContentObjectData();
		if (tmpContentObjectData == null) {
			return;
		}

		tmpContentObjectData.setSupportsContentMenus(true);
	}

	public LibraryItemUsageType getUsageType() {
		return new LibraryItemUsageType(this.getUsageTypeId());
	}

	public ContentObject getGlobalParentObject() {
		if(globalParentObject != null && !globalParentObject.isRemoved()){
			return globalParentObject;
		}else{
			return null;
		}
	}
	public void setGlobalParentObject(ContentObject globalParentObject) {
		this.globalParentObject = globalParentObject;
	}

	public Date getLastGlobalParentObjectSyncDate() {
		return lastGlobalParentObjectSyncDate;
	}
	public void setLastGlobalParentObjectSyncDate(Date lastGlobalParentObjectSyncDate) {
		this.lastGlobalParentObjectSyncDate = lastGlobalParentObjectSyncDate;
	}

	public boolean getNeedRecalculateHash() {
		return needRecalculateHash;
	}
	public void setNeedRecalculateHash(boolean needRecalculateHash) {
		this.needRecalculateHash = needRecalculateHash;
	}

	@Override
	public void setSha256Hash(String sha256Hash) {
		this.sha256Hash = sha256Hash;
	}

	@Override
	public String getSha256Hash() {
		return sha256Hash;
	}

	public Set<ObjectWorkflowActionAssociation> getWorkflowActionAssociations() {
		return workflowActionAssociations;
	}

	public void setWorkflowActionAssociations(Set<ObjectWorkflowActionAssociation> workflowActionAssociations) {
		this.workflowActionAssociations = workflowActionAssociations;
	}

	public boolean workflowHasSteps() {
		ConfigurableWorkflow workflow = null;
		if (this.isGlobalSmartText()) {
			workflow = ConfigurableWorkflow.getGlobalSmartTextWorkflow();
		} else if (this.isGlobalImage()) {
			workflow = ConfigurableWorkflow.getGlobalImageWorkflow();
		} else {
			workflow = this.getDocument().getMessageWorkflow();
		}

		ConfigurableWorkflowInstance wfInstance = workflow.findActiveInstance();

		return wfInstance != null && wfInstance.hasWorkflowSteps();
	}

	@Override
	public ConfigurableWorkflowAction getWorkflowAction() {
		if(this.getWorkflowActions().isEmpty()) {
			return null;
		}else {
			return this.getWorkflowActions().iterator().next();
		}
	}

	public ConfigurableWorkflowAction getActionByWorkflow(ConfigurableWorkflow workflow) {
		for(ObjectWorkflowActionAssociation association : this.getWorkflowActionAssociations()) {
			ConfigurableWorkflow currentWorkflow = association.getAction().getConfigurableWorkflowStep().getConfigurableWorkflowInstance().getConfigurableWorkflow();
			if(workflow == null || currentWorkflow.getId() == workflow.getId()){
				return association.getAction();
			}
		}
		return null;
	}

	@Override
	public void setWorkflowAction(ConfigurableWorkflowAction workflowAction) {
		this.clearWorkflowActions();
		if(workflowAction != null) {
			Map<ConfigurableWorkflowAction, Set<ConfigurableWorkflowAction>> wfActionsMap = new HashMap<>();
			wfActionsMap.put(null, new HashSet<>(List.of(workflowAction)));
			this.updateWorkflowActions(wfActionsMap);
		}
	}

	@Override
	public List<ConfigurableWorkflowAction> getWorkflowActions() {
		return this.getWorkflowActionAssociations().stream().map(ObjectWorkflowActionAssociation::getAction).sorted(Comparator.comparing(ConfigurableWorkflowAction::getId)).toList();
	}

	@Override
	public void updateWorkflowActions(Map<ConfigurableWorkflowAction, Set<ConfigurableWorkflowAction>> wfActionsmap) {
		for(ConfigurableWorkflowAction parentWfAction : wfActionsmap.keySet()) {
			Set<ObjectWorkflowActionAssociation> associations = wfActionsmap.get(parentWfAction).stream().map(action -> {
					ObjectWorkflowActionAssociation association = new ObjectWorkflowActionAssociation();
					association.setParentAction(parentWfAction);
					association.setAction(action);
					association.setModel(this);
					return association;
				}).collect(Collectors.toSet());
			this.getWorkflowActionAssociations().addAll(associations);
		}
	}

	@Override
	public void replaceWorkflowAction(ConfigurableWorkflowAction oldAction, ConfigurableWorkflowAction newAction) {
		ObjectWorkflowActionAssociation associationToRemove = null;
		for(ObjectWorkflowActionAssociation association : this.getWorkflowActionAssociations()) {
			if(association.getAction().getId() == oldAction.getId()) {
				if(newAction == null) {
					associationToRemove = association;
				}else {
					association.setAction(newAction);
					association.save();
				}
			}
		}

		if(associationToRemove != null) {
			this.getWorkflowActionAssociations().remove(associationToRemove);
			associationToRemove.delete();
		}

		// Update this object's task's workflow action
		List<Task> tasks = this.getTasks().stream().filter(task -> task.getWorkflowAction() != null && task.getWorkflowAction().getId() == oldAction.getId()).toList();
		for(Task task: tasks){
			if(newAction == null) {	// If newAction is null, delete the task
				this.getTasks().remove(task);
				task.delete();
			}else {
				task.setWorkflowAction(newAction);
				task.save();
			}
		}
	}

	public void clearWorkflowActions() {
		for(ObjectWorkflowActionAssociation association : this.getWorkflowActionAssociations()) {
			association.deleteWithPropagationSupports();
		}
		this.getWorkflowActionAssociations().clear();
	}

	// Find the list of workflows which are executable now
	public List<ConfigurableWorkflowAction> getExecutableWorkflowActions() {
		List<ConfigurableWorkflowAction> wfActions = new ArrayList<>();
		ConfigurableWorkflowActionHistory latestHistory = ConfigurableWorkflowActionHistory.findLatestHistoryByModel(this, true);
		for(ObjectWorkflowActionAssociation association : this.getWorkflowActionAssociations()) {
			ConfigurableWorkflowAction wfAction = association.getAction();
			// Check if it is being rejected or aborted most recently
			if (latestHistory != null) {
				if(latestHistory.getAction() == ConfigurableWorkflowActionType.ID_ABORTED) {
					// If the latest history is aborted, then it is not executable
					continue;
				}else if(latestHistory.getAction() == ConfigurableWorkflowActionType.ID_REJECTED) {
					// If the latest history is rejected, and the action is not a subworkflow action, then it is not executable
					if(association.getParentAction()==null) {
						continue;
					}
				}
			}
			if(!wfAction.isActive() && ObjectWorkflowActionAssociation.findAllByParentAction(wfAction).isEmpty()) {
				wfActions.add(wfAction);
			}
		}
		return wfActions.stream().sorted(Comparator.comparing(ConfigurableWorkflowAction::getId)).toList();
	}

	public WorkflowState getState() {
		return state;
	}
	public void setState(WorkflowState state) {
		this.state = state;
	}

	public boolean isReadyForApproval() {
		return readyForApproval;
	}
	public void setReadyForApproval(boolean readyForApproval) {
		this.readyForApproval = readyForApproval;
	}

	public Set<Task> getTasks() {
		return tasks;
	}

	public void setTasks(Set<Task> tasks) {
		this.tasks = tasks;
	}

	// End of Direct Getters and Setters


	// Setters and Getters from Content Object Data
	//

	public ParameterGroup getParameterGroup() {
		return getParameterGroup(focusOnDataType);
	}
	public void setParameterGroup(ParameterGroup parameterGroup) {
		this.setParameterGroup(parameterGroup, focusOnDataType);
	}

	public ParameterGroup getParameterGroup(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getParameterGroup();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getParameterGroup();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getParameterGroup();
		}

		return null;
	}

	public void setParameterGroup(ParameterGroup parameterGroup, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setParameterGroup(parameterGroup);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setParameterGroup(parameterGroup);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setParameterGroup(parameterGroup);
		}

	}

	public String getDeliveryTypeText() {
		return getDeliveryTypeText(focusOnDataType);
	}

	public String getDeliveryTypeText(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getDeliveryTypeText();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getDeliveryTypeText();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getDeliveryTypeText();
		}

		return "";
	}

	public int getDeliveryType() {
		return getDeliveryType(focusOnDataType);
	}
	public void setDeliveryType(int deliveryType) {
		this.setDeliveryType(deliveryType, focusOnDataType);
	}

	public int getDeliveryType(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getDeliveryType();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getDeliveryType();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getDeliveryType();
		}

		return 0;
	}

	public void setDeliveryType(int deliveryType, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setDeliveryType(deliveryType);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setDeliveryType(deliveryType);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setDeliveryType(deliveryType);
		}

	}

	public String getFlowTypeDisplay() {
		return new MessageFlowType(getFlowType()).getName();
	}

	public int getFlowType() {
		return getFlowType(focusOnDataType);
	}
	public void setFlowType(int flowType) {
		this.setFlowType(flowType, focusOnDataType);
	}

	public int getFlowType(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getFlowType();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getFlowType();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getFlowType();
		}

		return 0;
	}

	public void setFlowType(int flowType, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setFlowType(flowType);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setFlowType(flowType);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setFlowType(flowType);
		}

	}

	public ContentObjectComment getNewComment() {
		return getNewComment(focusOnDataType);
	}
	public void setNewComment(ContentObjectComment newComment) {
		this.setNewComment(newComment, focusOnDataType);
	}

	public ContentObjectComment getNewComment(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getNewComment();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getNewComment();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getNewComment();
		}

		return new ContentObjectComment();
	}

	public void setNewComment(ContentObjectComment newComment, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setNewComment(newComment);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setNewComment(newComment);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setNewComment(newComment);
		}

	}

	public boolean isConfirmationNeeded() {
		return isConfirmationNeeded(focusOnDataType);
	}
	public void setConfirmationNeeded(boolean isConfirmationNeeded) {
		this.setConfirmationNeeded(isConfirmationNeeded, focusOnDataType);
	}

	public boolean isConfirmationNeeded(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).isConfirmationNeeded();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).isConfirmationNeeded();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).isConfirmationNeeded();
		}

		return false;
	}

	public void setConfirmationNeeded(boolean isConfirmationNeeded, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setConfirmationNeeded(isConfirmationNeeded);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setConfirmationNeeded(isConfirmationNeeded);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setConfirmationNeeded(isConfirmationNeeded);
		}

	}

	public DataGroup getDataGroup() {
		return getDataGroup(focusOnDataType);
	}
	public void setDataGroup(DataGroup dataGroup) {
		this.setDataGroup(dataGroup, focusOnDataType);
	}

	public DataGroup getDataGroup(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getDataGroup();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getDataGroup();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getDataGroup();
		}

		return null;
	}

	public void setDataGroup(DataGroup dataGroup, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setDataGroup(dataGroup);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setDataGroup(dataGroup);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setDataGroup(dataGroup);
		}

	}

	public Boolean getStartsFrontFacing() {
		return getStartsFrontFacing(focusOnDataType);
	}
    public Boolean getStartsFrontFacingEvery() { return getStartsFrontFacingEvery(focusOnDataType);}
	public void setStartsFrontFacing(Boolean startsFrontFacing) {
		setStartsFrontFacing(startsFrontFacing, focusOnDataType);
	}
    public void setStartsFrontFacingEvery(Boolean startsFrontFacingEvery) {
        setStartsFrontFacingEvery(startsFrontFacingEvery, focusOnDataType);
    }
	public Boolean getStartsFrontFacing(int dataType) {
		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getStartsFrontFacing();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getStartsFrontFacing();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getStartsFrontFacing();
		}

		return null;
	}
public Boolean getStartsFrontFacingEvery(int dataType) {
        if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
        {
            return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getStartsFrontFacingEvery();
        }

        if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
        {
            return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getStartsFrontFacingEvery();
        }

        if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
        {
            return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getStartsFrontFacingEvery();
        }

        return null;
    }
	public void setStartsFrontFacing(Boolean startsFrontFacing, int dataType) {
		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setStartsFrontFacing(startsFrontFacing);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setStartsFrontFacing(startsFrontFacing);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setStartsFrontFacing(startsFrontFacing);
		}
	}
    public void setStartsFrontFacingEvery(Boolean startsFrontFacingEvery, int dataType) {
        if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
        {
            contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setStartsFrontFacingEvery(startsFrontFacingEvery);
        }

        if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
        {
            contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setStartsFrontFacingEvery(startsFrontFacingEvery);
        }

        if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
        {
            contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setStartsFrontFacingEvery(startsFrontFacingEvery);
        }
    }

	public String getMetatags() {
		return getMetatags(focusOnDataType);
	}
	public void setMetatags(String metatags) {
		setMetatags(metatags, focusOnDataType);
	}

	public String getMetatags(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getMetatags();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getMetatags();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getMetatags();
		}

		return null;
	}

	public void setMetatags(String metatags, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setMetatags(metatags);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setMetatags(metatags);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setMetatags(metatags);
		}

	}

	public Date getEndDate() {
		return getEndDate(focusOnDataType);
	}
	public void setEndDate(Date endDate) {
		this.setEndDate(endDate, focusOnDataType);
	}

	public Date getEndDate(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getEndDate();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getEndDate();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getEndDate();
		}

		return null;
	}

	public void setEndDate(Date endDate, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setEndDate(endDate);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setEndDate(endDate);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setEndDate(endDate);
		}

	}

	public Date getStartDate() {
		return getStartDate(focusOnDataType);
	}
	public void setStartDate(Date startDate) {
		this.setStartDate(startDate, focusOnDataType);
	}

	public Date getStartDate(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getStartDate();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getStartDate();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getStartDate();
		}

		return null;
	}

	public void setStartDate(Date startDate, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setStartDate(startDate);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setStartDate(startDate);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setStartDate(startDate);
		}

	}

	public boolean isRepeatDatesAnnually() {
		return isRepeatDatesAnnually(focusOnDataType);
	}
	public void setRepeatDatesAnnually(boolean repeatDatesAnnually) {
		this.setRepeatDatesAnnually(repeatDatesAnnually, focusOnDataType);
	}

	public boolean isRepeatDatesAnnually(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).isRepeatDatesAnnually();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).isRepeatDatesAnnually();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).isRepeatDatesAnnually();
		}

		return false;
	}

	public void setRepeatDatesAnnually(boolean repeatDatesAnnually, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setRepeatDatesAnnually(repeatDatesAnnually);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setRepeatDatesAnnually(repeatDatesAnnually);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setRepeatDatesAnnually(repeatDatesAnnually);
		}

	}

	public int getCompVarFormatType() {
		return getCompVarFormatType(focusOnDataType);
	}
	public void setCompVarFormatType(int compVarFormatType) {
		this.setCompVarFormatType(compVarFormatType, focusOnDataType);
	}

	public int getCompVarFormatType(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getCompVarFormatType();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getCompVarFormatType();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getCompVarFormatType();
		}

		return 0;
	}

	public void setCompVarFormatType(int compVarFormatType, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setCompVarFormatType(compVarFormatType);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setCompVarFormatType(compVarFormatType);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setCompVarFormatType(compVarFormatType);
		}

	}

	public int getDataGroupCompVarFormatType() {
		return getDataGroupCompVarFormatType(focusOnDataType);
	}
	public void setDataGroupCompVarFormatType(int compVarFormatType) {
		this.setDataGroupCompVarFormatType(compVarFormatType, focusOnDataType);
	}

	public int getDataGroupCompVarFormatType(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getDataGroupCompVarFormatType();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getDataGroupCompVarFormatType();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getDataGroupCompVarFormatType();
		}

		return 0;
	}

	public void setDataGroupCompVarFormatType(int dataGropupCompVarFormatType, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setDataGroupCompVarFormatType(dataGropupCompVarFormatType);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setDataGroupCompVarFormatType(dataGropupCompVarFormatType);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setDataGroupCompVarFormatType(dataGropupCompVarFormatType);
		}

	}

	public boolean isInsertAsBlockContent() {
		return isInsertAsBlockContent(focusOnDataType);
	}
	public void setInsertAsBlockContent(boolean insertAsBlockContent) {
		this.setInsertAsBlockContent(insertAsBlockContent, focusOnDataType);
	}

	public boolean isInsertAsBlockContent(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).isInsertAsBlockContent();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).isInsertAsBlockContent();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).isInsertAsBlockContent();
		}

		return false;
	}

	public void setInsertAsBlockContent(boolean insertAsBlockContent, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setInsertAsBlockContent(insertAsBlockContent);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setInsertAsBlockContent(insertAsBlockContent);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setInsertAsBlockContent(insertAsBlockContent);
		}

	}

	public int getContentTrimType() {
		return getContentTrimType(focusOnDataType);
	}
	public void setContentTrimType(int contentTrimType) {
		this.setContentTrimType(contentTrimType, focusOnDataType);
	}

	public int getContentTrimType(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getContentTrimType();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getContentTrimType();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getContentTrimType();
		}

		return 0;
	}

	public void setContentTrimType(int contentTrimType, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setContentTrimType(contentTrimType);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setContentTrimType(contentTrimType);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setContentTrimType(contentTrimType);
		}

	}

	public String getSegmentationData() {
		return getSegmentationData(focusOnDataType);
	}
	public void setSegmentationData(String segmentationData) {
		this.setSegmentationData(segmentationData, focusOnDataType);
	}

	public String getSegmentationData(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getSegmentationData();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getSegmentationData();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getSegmentationData();
		}

		return null;
	}

	public void setSegmentationData(String segmentationData, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setSegmentationData(segmentationData);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setSegmentationData(segmentationData);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setSegmentationData(segmentationData);
		}

	}

	public int getCanvasMaxWidth() {
		return getCanvasMaxWidth(focusOnDataType);
	}
	public void setCanvasMaxWidth(int canvasMaxWidth) {
		this.setCanvasMaxWidth(canvasMaxWidth, focusOnDataType);
	}

	public int getCanvasMaxWidth(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getCanvasMaxWidth();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getCanvasMaxWidth();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getCanvasMaxWidth();
		}

		return 0;
	}

	public void setCanvasMaxWidth(int canvasMaxWidth, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setCanvasMaxWidth(canvasMaxWidth);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setCanvasMaxWidth(canvasMaxWidth);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setCanvasMaxWidth(canvasMaxWidth);
		}

	}

	public int getCanvasMaxHeight() {
		return getCanvasMaxHeight(focusOnDataType);
	}
	public void setCanvasMaxHeight(int canvasMaxHeight) {
		this.setCanvasMaxHeight(canvasMaxHeight, focusOnDataType);
	}

	public int getCanvasMaxHeight(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getCanvasMaxHeight();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getCanvasMaxHeight();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getCanvasMaxHeight();
		}

		return 0;
	}

	public void setCanvasMaxHeight(int canvasMaxHeight, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setCanvasMaxHeight(canvasMaxHeight);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setCanvasMaxHeight(canvasMaxHeight);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setCanvasMaxHeight(canvasMaxHeight);
		}

	}

	public int getCanvasTrimWidth() {
		return getCanvasTrimWidth(focusOnDataType);
	}
	public void setCanvasTrimWidth(int canvasTrimWidth) {
		this.setCanvasTrimWidth(canvasTrimWidth, focusOnDataType);
	}

	public int getCanvasTrimWidth(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getCanvasTrimWidth();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getCanvasTrimWidth();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getCanvasTrimWidth();
		}

		return 0;
	}

	public void setCanvasTrimWidth(int canvasTrimWidth, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setCanvasTrimWidth(canvasTrimWidth);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setCanvasTrimWidth(canvasTrimWidth);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setCanvasTrimWidth(canvasTrimWidth);
		}

	}

	public int getCanvasTrimHeight() {
		return getCanvasTrimHeight(focusOnDataType);
	}
	public void setCanvasTrimHeight(int canvasTrimHeight) {
		this.setCanvasTrimHeight(canvasTrimHeight, focusOnDataType);
	}

	public int getCanvasTrimHeight(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getCanvasTrimHeight();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getCanvasTrimHeight();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getCanvasTrimHeight();
		}

		return 0;
	}

	public void setCanvasTrimHeight(int canvasTrimHeight, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setCanvasTrimHeight(canvasTrimHeight);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setCanvasTrimHeight(canvasTrimHeight);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setCanvasTrimHeight(canvasTrimHeight);
		}

	}

	public boolean isSupportsTables() {
		return isSupportsTables(focusOnDataType);
	}
	public void setSupportsTables(boolean supportsTables) {
		this.setSupportsTables(supportsTables, focusOnDataType);
	}

	public boolean isSupportsTables(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).isSupportsTables();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).isSupportsTables();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).isSupportsTables();
		}

		return false;
	}

	public void setSupportsTables(boolean supportsTables, int... dataTypeParameter) {

		int dataType = dataTypeParameter.length > 0 ? dataTypeParameter[0] : ContentObject.DATA_TYPE_WORKING;

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setSupportsTables(supportsTables);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setSupportsTables(supportsTables);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setSupportsTables(supportsTables);
		}

	}

	public boolean isSupportsForms() {
		return isSupportsForms(focusOnDataType);
	}
	public void setSupportsForms(boolean supportsForms) {
		this.setSupportsForms(supportsForms, focusOnDataType);
	}

	public boolean isSupportsForms(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).isSupportsForms();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).isSupportsForms();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).isSupportsForms();
		}

		return false;
	}

	public void setSupportsForms(boolean supportsForms, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setSupportsForms(supportsForms);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setSupportsForms(supportsForms);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setSupportsForms(supportsForms);
		}

	}

	public boolean isSupportsBarcodes() {
		return isSupportsBarcodes(focusOnDataType);
	}
	public void setSupportsBarcodes(boolean supportsBarcodes) {
		this.setSupportsBarcodes(supportsBarcodes, focusOnDataType);
	}

	public boolean isSupportsBarcodes(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).isSupportsBarcodes();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).isSupportsBarcodes();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).isSupportsBarcodes();
		}

		return false;
	}

	public void setSupportsBarcodes(boolean supportsBarcodes, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setSupportsBarcodes(supportsBarcodes);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setSupportsBarcodes(supportsBarcodes);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setSupportsBarcodes(supportsBarcodes);
		}

	}

	public boolean isSupportsContentMenus() {
		return isSupportsContentMenus(focusOnDataType);
	}
	public void setSupportsContentMenus(boolean supportsContentMenus) {
		this.setSupportsContentMenus(supportsContentMenus, focusOnDataType);
	}

	public boolean isSupportsContentMenus(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).isSupportsContentMenus();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).isSupportsContentMenus();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).isSupportsContentMenus();
		}

		return false;
	}

	public void setSupportsContentMenus(boolean supportsContentMenus, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setSupportsContentMenus(supportsContentMenus);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setSupportsContentMenus(supportsContentMenus);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setSupportsContentMenus(supportsContentMenus);
		}

	}

	public boolean isKeepContentTogether() {
		return isKeepContentTogether(focusOnDataType);
	}
	public void setKeepContentTogether(boolean keepContentTogether) {
		this.setKeepContentTogether(keepContentTogether, focusOnDataType);
	}

	public boolean isKeepContentTogether(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).isKeepContentTogether();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).isKeepContentTogether();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).isKeepContentTogether();
		}

		return false;
	}

	public void setKeepContentTogether(boolean keepContentTogether, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setKeepContentTogether(keepContentTogether);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setKeepContentTogether(keepContentTogether);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setKeepContentTogether(keepContentTogether);
		}

	}

	public boolean isRenderAsTaggedText() {
		return isRenderAsTaggedText(focusOnDataType);
	}
	public void setRenderAsTaggedText(boolean renderAsTaggedText) {
		this.setRenderAsTaggedText(renderAsTaggedText, focusOnDataType);
	}

	public boolean isRenderAsTaggedText(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).isRenderAsTaggedText();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).isRenderAsTaggedText();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).isRenderAsTaggedText();
		}

		return false;
	}

	public void setRenderAsTaggedText(boolean renderAsTaggedText, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setRenderAsTaggedText(renderAsTaggedText);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setRenderAsTaggedText(renderAsTaggedText);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setRenderAsTaggedText(renderAsTaggedText);
		}

	}

	public MetadataForm getMetadataForm() {
		return getMetadataForm(focusOnDataType);
	}
	public void setMetadataForm(MetadataForm metadataForm) {
		this.setMetadataForm(metadataForm, focusOnDataType);
	}

	public MetadataForm getMetadataForm(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getMetadataForm();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getMetadataForm();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getMetadataForm();
		}

		return null;
	}

	public void setMetadataForm(MetadataForm metadataForm, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setMetadataForm(metadataForm);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setMetadataForm(metadataForm);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setMetadataForm(metadataForm);
		}

	}

	public Set<ContentObjectComment> getComments() {
		return getComments(focusOnDataType);
	}
	public void setComments(Set<ContentObjectComment> comments) {
		this.setComments(comments, focusOnDataType);
	}

	public Set<ContentObjectComment> getComments(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getComments();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getComments();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getComments();
		}

		return null;
	}

	public void setComments(Set<ContentObjectComment> comments, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setComments(comments);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setComments(comments);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setComments(comments);
		}

	}

	public List<ContentObjectComment> getCommentsOrdered() {
		return getCommentsOrdered(focusOnDataType);
	}

	public List<ContentObjectComment> getCommentsOrdered(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getCommentsOrdered();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getCommentsOrdered();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getCommentsOrdered();
		}

		return new ArrayList<>();
	}

	public List<TargetGroup> getIncludedTargetGroups() {
		return getIncludedTargetGroups(focusOnDataType);
	}

	public void setIncludedTargetGroups(List<TargetGroup> includedTargetGroups) {
		setIncludedTargetGroups(includedTargetGroups, focusOnDataType);
	}

	public List<TargetGroup> getIncludedTargetGroups(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getIncludedTargetGroups();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getIncludedTargetGroups();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getIncludedTargetGroups();
		}

		return null;
	}

	public void setIncludedTargetGroups(List<TargetGroup> includedTargetGroups, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setIncludedTargetGroups(includedTargetGroups);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setIncludedTargetGroups(includedTargetGroups);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setIncludedTargetGroups(includedTargetGroups);
		}

	}

	public List<TargetGroup> getExtendedTargetGroups() {
		return getExtendedTargetGroups(focusOnDataType);
	}

	public void setExtendedTargetGroups(List<TargetGroup> extendedTargetGroups) {
		setExtendedTargetGroups(extendedTargetGroups, focusOnDataType);
	}

	public Boolean getNoTargetingSet(){
		return getExcludedTargetGroups().isEmpty() && getIncludedTargetGroups().isEmpty();
	}

	public List<TargetGroup> getExtendedTargetGroups(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getExtendedTargetGroups();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getExtendedTargetGroups();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getExtendedTargetGroups();
		}

		return null;
	}

	public void setExtendedTargetGroups(List<TargetGroup> extendedTargetGroups, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setExtendedTargetGroups(extendedTargetGroups);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setExtendedTargetGroups(extendedTargetGroups);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setExtendedTargetGroups(extendedTargetGroups);
		}

	}

	public List<TargetGroup> getExcludedTargetGroups() {
		return getExcludedTargetGroups(focusOnDataType);
	}

	public void setExcludedTargetGroups(List<TargetGroup> excludedTargetGroups) {
		setExcludedTargetGroups(excludedTargetGroups, focusOnDataType);
	}

	public List<TargetGroup> getExcludedTargetGroups(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getExcludedTargetGroups();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getExcludedTargetGroups();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getExcludedTargetGroups();
		}

		return null;
	}

	public void setExcludedTargetGroups(List<TargetGroup> excludedTargetGroups, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setExcludedTargetGroups(excludedTargetGroups);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setExcludedTargetGroups(excludedTargetGroups);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setExcludedTargetGroups(excludedTargetGroups);
		}

	}

	public int getIncludedTargetGroupRelationship() {
		return getIncludedTargetGroupRelationship(focusOnDataType);
	}
	public void setIncludedTargetGroupRelationship(int includeTGR) {
		setIncludedTargetGroupRelationship(includeTGR, focusOnDataType);
	}

	public int getIncludedTargetGroupRelationship(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getIncludedTargetGroupRelationship();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getIncludedTargetGroupRelationship();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getIncludedTargetGroupRelationship();
		}

		return 0;
	}

	public void setIncludedTargetGroupRelationship(int includeTGR, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setIncludedTargetGroupRelationship(includeTGR);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setIncludedTargetGroupRelationship(includeTGR);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setIncludedTargetGroupRelationship(includeTGR);
		}

	}

	public int getExtendedTargetGroupRelationship() {
		return getExtendedTargetGroupRelationship(focusOnDataType);
	}
	public void setExtendedTargetGroupRelationship(int extendedTGR) {
		setExtendedTargetGroupRelationship(extendedTGR, focusOnDataType);
	}

	public int getExtendedTargetGroupRelationship(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getExtendedTargetGroupRelationship();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getExtendedTargetGroupRelationship();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getExtendedTargetGroupRelationship();
		}

		return 0;
	}

	public void setExtendedTargetGroupRelationship(int extendedTGR, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setExtendedTargetGroupRelationship(extendedTGR);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setExtendedTargetGroupRelationship(extendedTGR);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setExtendedTargetGroupRelationship(extendedTGR);
		}

	}

	public int getExcludedTargetGroupRelationship() {
		return getExcludedTargetGroupRelationship(focusOnDataType);
	}
	public void setExcludedTargetGroupRelationship(int excludedTGR) {
		setExcludedTargetGroupRelationship(excludedTGR, focusOnDataType);
	}

	public int getExcludedTargetGroupRelationship(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getExcludedTargetGroupRelationship();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getExcludedTargetGroupRelationship();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getExcludedTargetGroupRelationship();
		}

		return 0;
	}

	public void setExcludedTargetGroupRelationship(int excludedTGR, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setExcludedTargetGroupRelationship(excludedTGR);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setExcludedTargetGroupRelationship(excludedTGR);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setExcludedTargetGroupRelationship(excludedTGR);
		}

	}


	public void addTargetGroupInstance(TargetGroup tg, TargetGroupInstance tgi) {
		addTargetGroupInstance(tg, tgi, focusOnDataType);
	}

	public void addTargetGroupInstance(TargetGroup tg, TargetGroupInstance tgi, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).addTargetGroupInstance(tg, tgi);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).addTargetGroupInstance(tg, tgi);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).addTargetGroupInstance(tg, tgi);
		}

	}


	public TargetGroupInstance getTargetGroupInstance(long targetGroupId) {
		return getTargetGroupInstance(targetGroupId, focusOnDataType);
	}

	public TargetGroupInstance getTargetGroupInstance(long targetGroupId, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getTargetGroupInstance(targetGroupId);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getTargetGroupInstance(targetGroupId);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getTargetGroupInstance(targetGroupId);
		}

		return null;
	}


	public List<String> getIncludedTargetGroupDetailsList() {
		return getIncludedTargetGroupDetailsList(focusOnDataType);
	}

	public List<String> getIncludedTargetGroupDetailsList(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getIncludedTargetGroupDetailsList();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getIncludedTargetGroupDetailsList();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getIncludedTargetGroupDetailsList();
		}

		return new ArrayList<>();
	}


	public List<String> getExtendedTargetGroupDetailsList() {
		return getExtendedTargetGroupDetailsList(focusOnDataType);
	}

	public List<String> getExtendedTargetGroupDetailsList(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getExtendedTargetGroupDetailsList();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getExtendedTargetGroupDetailsList();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getExtendedTargetGroupDetailsList();
		}

		return new ArrayList<>();
	}


	public List<String> getExcludedTargetGroupDetailsList() {
		return getExcludedTargetGroupDetailsList(focusOnDataType);
	}

	public List<String> getExcludedTargetGroupDetailsList(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getExcludedTargetGroupDetailsList();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getExcludedTargetGroupDetailsList();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getExcludedTargetGroupDetailsList();
		}

		return new ArrayList<>();
	}


	public String getStatusDisplay() {

		if (focusOnDataType == ContentObject.DATA_TYPE_WORKING)
			return VersionStatus.findById(VersionStatus.MODEL_NEW).getLocaledString();

		if (focusOnDataType == ContentObject.DATA_TYPE_ACTIVE)
			return VersionStatus.findById(VersionStatus.MODEL_PRODUCTION).getLocaledString();

		if (focusOnDataType == ContentObject.DATA_TYPE_ARCHIVED)
			return VersionStatus.findById(VersionStatus.MODEL_ARCHIVE).getLocaledString();

		return  VersionStatus.findById(VersionStatus.MODEL_REMOVED).getLocaledString();
	}


	public VersionStatus getDataStatus() {
		return getDataStatus(focusOnDataType);
	}
	public void setDataStatus(VersionStatus dataStatus) {
		setDataStatus(dataStatus, focusOnDataType);
	}

	public VersionStatus getDataStatus(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getStatus();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getStatus();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getStatus();
		}

		return null;
	}

	public void setDataStatus(VersionStatus dataStatus, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setStatus(dataStatus);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setStatus(dataStatus);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setStatus(dataStatus);
		}

	}


	public WorkflowState getDataState() {
		return getDataState(focusOnDataType);
	}
	public void setDataState(WorkflowState dataState) {
		setDataState(dataState, focusOnDataType);
	}

	public WorkflowState getDataState(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getState();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getState();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getState();
		}

		return null;
	}

	public void setDataState(WorkflowState dataState, int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).setState(dataState);
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).setState(dataState);
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).setState(dataState);
		}

	}


	@SuppressWarnings("unchecked")
	public List<ReferencableObject> getDirectReferences() {
		ServiceExecutionContext context = DirectReferencesFetchService.createContext(getId(), ContentObject.class);
		Service service = MessagepointServiceFactory.getInstance().lookupService(DirectReferencesFetchService.SERVICE_NAME, DirectReferencesFetchService.class);
		service.execute(context);
		if (!context.getResponse().isSuccessful()) {
			StringBuilder sb = new StringBuilder();
			sb.append(DirectReferencesFetchService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" for object ").append(this.getClass().getName());
			sb.append(" id=").append(this.getId());
			log.error(sb.toString());
			throw new RuntimeException(sb.toString());
		}
		return (List<ReferencableObject>) context.getResponse().getResultValueBean();
	}

	public boolean isReferenced() {
		List<ReferencableObject> refs = this.getDirectReferences();
		if(refs != null && !refs.isEmpty())
			return true;
		return false;
	}

	public Boolean getIsSingleLevelDateParameter() {
		return getIsSingleLevelDateParameter(focusOnDataType);
	}

	public Boolean getIsSingleLevelDateParameter(int dataType) {

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getIsSingleLevelDateParameter();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getIsSingleLevelDateParameter();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getIsSingleLevelDateParameter();
		}

		return false;
	}


	//


	public ContentObjectData getLatestContentObjectDataWorkingCentric() {
		if (this.hasWorkingData()) return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING);
		if (this.hasActiveData()) return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE);
		return null;
	}

	public ContentObjectData getLatestContentObjectDataActiveCentric() {
		if (this.hasActiveData()) return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE);
		if (this.hasWorkingData()) return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING);
		return null;
	}

    public Integer getLatestContentObjectDataTypeWorkingCentric() {
        if (this.hasWorkingData()) return ContentObject.DATA_TYPE_WORKING;
        if (this.hasActiveData()) return ContentObject.DATA_TYPE_ACTIVE;
        return null;
    }

    public Integer getLatestContentObjectDataTypeActiveCentric() {
        if (this.hasActiveData()) return ContentObject.DATA_TYPE_ACTIVE;
        if (this.hasWorkingData()) return ContentObject.DATA_TYPE_WORKING;
        return null;
    }

	public List<ParameterGroupInstanceCollection> getParameterGroupInstanceCollections() {
		List<ParameterGroupInstanceCollection> pgInstanceCollections = new ArrayList<>();
		for (ContentObjectAssociation coa : this.getContentObjectAssociations()) {
			if (coa.getTouchpointPGTreeNode() != null) {
				pgInstanceCollections.add(coa.getTouchpointPGTreeNode().getParameterGroupInstanceCollection());
			}
		}
		return pgInstanceCollections;
	}

	public List<ParameterGroupTreeNode> getParameterGroupTreeNodes() {
		List<ParameterGroupTreeNode> pgTreeNodes = new ArrayList<>();
		for (ContentObjectAssociation coa : this.getContentObjectAssociations()) {
			if (coa.getTouchpointPGTreeNode() != null) {
				pgTreeNodes.add(coa.getTouchpointPGTreeNode());
			}
		}
		return pgTreeNodes;
	}

	public Set<Long> getDynamicVariantWorkingDataParameterGroupTreeNodeIds() {
		Set<Long> pgTreeNodeIds = new HashSet<>();
		List<ParameterGroupTreeNode> pgTreeNodes = ParameterGroupTreeNode.findAllDynamicVariants(this, ContentObject.DATA_TYPE_WORKING);
		for (ParameterGroupTreeNode pgtn : pgTreeNodes) {
			pgTreeNodeIds.add(pgtn.getId());
		}
		return pgTreeNodeIds;
	}

	// Statuses
	//
	public boolean getHasWorkingData(){
		return hasWorkingData();
	}
	public boolean hasWorkingData() {
		return contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING);
	}

	public boolean getHasActiveData(){
		return hasActiveData();
	}
	public boolean hasActiveData() {
		return contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE);
	}

	public boolean getHasArchivedData(){
		return hasArchivedData();
	}
	public boolean hasArchivedData() {
		return contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED);
	}

	public boolean hasDataType(int dataType) {
		return contentObjectDataTypeMap.containsKey(dataType);
	}

	public boolean getHasStructuredContent(){
		return hasStructuredContent();
	}
	public boolean hasStructuredContent() {
		if (isStructuredContentEnabled())
		{
			return ContentObjectAssociation.hasStructuredContent(this, this.getFocusOnDataType(), true);
		}
		return false;
	}

	public boolean hasStructuredContentForJobPacker( int dataType ) {
		if (isStructuredContentEnabled())
		{
			Document doc = getDocument();
			if ( doc.isEnabledForVariantWorkflow() )
				return true;

			return ContentObjectAssociation.hasStructuredContent(this, dataType, false);
		}
		return false;
	}


	public boolean isDynamicVariantEnabled() {
		return this.isDynamicVariantEnabled(focusOnDataType);
	}

	public boolean getDynamicVariantEnabled() {
		return this.isDynamicVariantEnabled(focusOnDataType);
	}

	public boolean getHasDynamicVariantWorkingVersion(){
		return hasDynamicVariantWorkingVersion();
	}

	public boolean hasDynamicVariantWorkingVersion() {
		if (isDynamicVariantEnabled(ContentObject.DATA_TYPE_WORKING))
		{
			return ContentObjectAssociation.hasDynamicVariant(this, ContentObject.DATA_TYPE_WORKING);
		}
		return false;
	}

	public boolean getHasDynamicVariantActiveVersion(){
		return hasDynamicVariantActiveVersion();
	}

	public boolean hasDynamicVariantActiveVersion() {
		if (isDynamicVariantEnabled(ContentObject.DATA_TYPE_ACTIVE))
		{
			return ContentObjectAssociation.hasDynamicVariant(this, ContentObject.DATA_TYPE_ACTIVE);
		}
		return false;
	}

	public boolean getHasDynamicVariantArchivedVersion(){
		return hasDynamicVariantArchivedVersion();
	}

	public boolean hasDynamicVariantArchivedVersion() {
		if (isDynamicVariantEnabled(ContentObject.DATA_TYPE_ARCHIVED))
		{
			return ContentObjectAssociation.hasDynamicVariant(this, ContentObject.DATA_TYPE_ARCHIVED);
		}
		return false;
	}

	public boolean getHasDynamicVariantAnyVersion(){
		return hasDynamicVariantAnyVersion();
	}

	public boolean hasDynamicVariantAnyVersion() {
		if (isDynamicVariantEnabled(ContentObject.DATA_TYPE_ANY))
		{
			return ContentObjectAssociation.hasDynamicVariant(this, ContentObject.DATA_TYPE_ANY);
		}
		return false;
	}

	public boolean isDynamicVariantEnabled(int dataType) {
		boolean result = false;

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			result = contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getParameterGroup() != null;

			if (dataType == ContentObject.DATA_TYPE_WORKING_OR_ACTIVE && result) return result;
			if (dataType == ContentObject.DATA_TYPE_WORKING_AND_ACTIVE && !result) return result;
			if (dataType == ContentObject.DATA_TYPE_ANY && result) return result;
			if (dataType == ContentObject.DATA_TYPE_ALL && !result) return result;
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			result = contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getParameterGroup() != null;

			if (dataType == ContentObject.DATA_TYPE_ACTIVE_OR_ARCHIVED && result) return result;
			if (dataType == ContentObject.DATA_TYPE_ACTIVE_AND_ARCHIVED && !result) return result;
			if (dataType == ContentObject.DATA_TYPE_ANY && result) return result;
			if (dataType == ContentObject.DATA_TYPE_ALL && !result) return result;
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			result = contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getParameterGroup() != null;
		}

		return result;
	}

	public boolean getHasVariableContent(){
		return hasVariableContent();
	}

	public boolean hasVariableContent() {
		if (isVariableContentEnabled())
		{
			String query = "SELECT COUNT(*) " +
					"FROM CONTENT_OBJECT_ASSOCIATION coa " +
					"INNER JOIN CONTENT c ON c.ID = coa.CONTENT_ID " +
					"INNER JOIN CONTENT_VARIABLE cv ON cv.content_id = c.ID " +
					"WHERE coa.content_object_id = " + this.getId();

			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
			List retList = sqlQuery.list();

			int count = 0;
			if (retList != null && !retList.isEmpty()) {
				Object retObj = retList.get(0);
				if (Number.class.isInstance(retObj))
					count = ((Number) retObj).intValue();
			}
			if (count > 0)
				return true;

			List<Long> contentObjectChildrenIds = new ArrayList<>();
			String childrentQuery = "SELECT DISTINCT ccot.content_object_id " +
					"FROM CONTENT_OBJECT_ASSOCIATION coa " +
					"INNER JOIN CONTENT c ON c.ID = coa.CONTENT_ID " +
					"INNER JOIN CONTENT_CONTENT_OBJECT_TYPE ccot ON ccot.content_id = c.ID " +
					"WHERE coa.content_object_id = " + this.getId();

			sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(childrentQuery);
			List ids = sqlQuery.list();
			for (Object idObj : ids) {
				ContentObject contentObject = ContentObject.findById(((BigInteger) idObj).longValue());
				if (contentObject != null && contentObject.hasVariableContent())
					return true;
			}
		}

		return false;
	}

	public boolean isMine() {
		return isMine(UserUtil.getPrincipalUser());
	}

	public boolean isMine(User user) {
		if (!hasWorkingData()) return false;
		if(user != null) {
			if(! getObjectSchemaName().equals(user.getSchemaOfThisUser())) {
				Node thisNode = Node.findBySchema(getObjectSchemaName());
				user = user.getNodeUser(thisNode);
			}
		}
		if (user == null) {
			return false;
		} else {
			if (getLockedForId() == 0L)
				return false;
			else
				return (getLockedForId() == user.getId());
		}
	}

	public Boolean canReassign() {
		if ( isMine() )
			return true;
		else if ( getLastLockTime() == null || (new Date()).getTime() - getLastLockTime().getTime() > 15000 )
			return true;
		return false;
	}

	public Boolean isEditLocked() {
		return getLastLockTime() != null && !((new Date()).getTime() - getLastLockTime().getTime() > 15000);
	}

	public boolean isFocusOnWorkingData() {
		return (focusOnDataType == ContentObject.DATA_TYPE_WORKING);
	}

	public boolean isFocusOnActiveData() {
		return (focusOnDataType == ContentObject.DATA_TYPE_ACTIVE);
	}

	public boolean isFocusOnArchivedData() {
		return (focusOnDataType == ContentObject.DATA_TYPE_ARCHIVED);
	}

	public boolean isVisibleForGUI() {
		if (this.isRemoved()) {
			return false;
		}

		return true;
	}

	public Boolean isInEnabledZone() {
		if ( this.getZone() != null ) {
			if ( getOwningTouchpointSelection() != null ) {
				TouchpointSelection owningSelection = getOwningTouchpointSelection();
				if ( owningSelection.getAlternateLayout() != null ) {
					Zone owningAlternateZone = owningSelection.getAlternateLayout().findZoneByParent( this.getZone() );
					if ( owningAlternateZone != null && !owningAlternateZone.isEnabled() )
						return false;
				}
			} else if ( !this.getZone().isEnabled() ) {
				return false;
			}
		}
		return true;
	}

	public boolean getIsStructuredContentEnabled() {
		return this.isStructuredContentEnabled();
	}

	public boolean getIsVariableContentEnabled() {
		return this.isVariableContentEnabled();
	}

	public boolean isVariantType() {
		return (getOwningTouchpointSelection() != null);
	}

	public boolean isMessage() {
		return (this.objectType == OBJECT_TYPE_MESSAGE);
	}

	public boolean getIsMessage() {
		return (this.objectType == OBJECT_TYPE_MESSAGE);
	}

	public boolean isLocalSmartCanvas() {
		return getIsLocalSmartCanvas();
	}

	public boolean getIsLocalSmartCanvas() {
		return (this.objectType == OBJECT_TYPE_LOCAL_SMART_TEXT && this.isSharedFreeform());
	}

	public boolean isLocalSmartText() {
		return (this.objectType == OBJECT_TYPE_LOCAL_SMART_TEXT);
	}

	public boolean getIsLocalSmartText() {
		return (this.objectType == OBJECT_TYPE_LOCAL_SMART_TEXT);
	}

	public boolean isLocalImage() {
		return (this.objectType == OBJECT_TYPE_LOCAL_IMAGE);
	}

	public boolean getIsLocalImage() {
		return (this.objectType == OBJECT_TYPE_LOCAL_IMAGE);
	}

	public boolean isGlobalSmartCanvas() {
		return getIsGlobalSmartCanvas();
	}

	public boolean getIsGlobalSmartCanvas() {
		return (this.objectType == OBJECT_TYPE_GLOBAL_SMART_TEXT && this.isSharedFreeform());
	}

	public boolean isGlobalSmartText() {
		return (this.objectType == OBJECT_TYPE_GLOBAL_SMART_TEXT);
	}

	public boolean getIsGlobalSmartText() {
		return (this.objectType == OBJECT_TYPE_GLOBAL_SMART_TEXT);
	}

	public boolean isGlobalImage() {
		return (this.objectType == OBJECT_TYPE_GLOBAL_IMAGE);
	}

	public boolean getIsGlobalImage() {
		return (this.objectType == OBJECT_TYPE_GLOBAL_IMAGE);
	}

	public boolean isTouchpointLocal() {
		return (this.objectType & OBJECT_TYPE_ANY_LOCAL) > 0;
	}

	public boolean getIsTouchpointLocal() {
		return (this.objectType & OBJECT_TYPE_ANY_LOCAL) > 0;
	}

	public boolean isLocalContentObject() {
		return (this.objectType & OBJECT_TYPE_ANY_LOCAL) > 0;
	}

    public boolean isMessageOrTouchpointLocal() {
        return (this.objectType == OBJECT_TYPE_MESSAGE) || ((this.objectType & OBJECT_TYPE_ANY_LOCAL) > 0);
    }

    public boolean getIsLocalContentObject() {
		return (this.objectType & OBJECT_TYPE_ANY_LOCAL) > 0;
	}

	public boolean isGlobalContentObject() {
		return (this.objectType & OBJECT_TYPE_ANY_GLOBAL) > 0;
	}

	public boolean getIsGlobalContentObject() {
		return (this.objectType & OBJECT_TYPE_ANY_GLOBAL) > 0;
	}

	public boolean isTextContent() {
		return (contentType != null) ? contentType.isText() : false;
	}

	public boolean getIsText() {
		return isTextContent();
	}

	public boolean isMarkupContent() {
		return (contentType != null) ? contentType.isMarkup() : false;
	}

	public boolean getIsMarkup() {
		return isMarkupContent();
	}

	public boolean isTextOrMarkupContent() {
		if (contentType != null)
			return contentType.isText() || contentType.isMarkup();
		else
			return false;
	}

	public boolean getIsTextOrMarkup() {
		return isTextOrMarkupContent();
	}

	public boolean isSharedFreeform() {
		return (contentType != null) ? contentType.isSharedFreeform() : false;
	}

	public boolean getIsSharedFreeform() { return isSharedFreeform(); }

	public boolean isGraphicContent() {
		return (contentType != null) ? contentType.isGraphic() : false;
	}

	public boolean getIsGraphic() {
		return isGraphicContent();
	}

	public boolean isMultipartContent() {
		return (contentType != null) ? contentType.isMultipart() : false;
	}

	public boolean getIsMultipart() {
		return isMultipartContent();
	}

	public boolean isMultipartType() {
		return (contentType != null) ? contentType.isMultipart() : false;
	}

	public Boolean isDeliveredToPlaceholder() {
		return isTouchpointLocal() && this.getZone() != null;
	}

	public boolean isParameterGroupEditable() {
		if (this.getId() <= 0) {
			return true;
		} else if (this.getContentObjectAssociations() != null && !this.getContentObjectAssociations().isEmpty()) {
			return false;
		} else {
			return true;
		}
	}

	public boolean getIsFreeform() {
		if ( this.getIsSharedFreeform() )
			return true;
		if ( zone != null && ((zone.getSection() != null && zone.getSection().getSectionType() == DocumentSection.SECTION_TYPE_FREEFORM) || zone.isFreeform()) )
			return true;
		return false;
	}

	public boolean isVersionVisible(Workgroup wg) {
		if ( !this.isMessage() && !isDeliveredToPlaceholder() ) {
			return true;
		} else {
			if ( this.zone != null ) {
				if ( wg.getZones().contains(this.zone) )
					return true;
			}
		}
		return false;
	}

	public boolean getPermitsMultiDelivery() {
		return !( isStructuredContentEnabled() || isVariantType() || isDynamicVariantEnabled() );
	}

	public boolean getHasTiming() {
		return this.getStartDate() != null;
	}

	public boolean getHasTarget() {
		return ((this.getExcludedTargetGroups() != null && !this.getExcludedTargetGroups().isEmpty()) ||
				(this.getIncludedTargetGroups() != null && !this.getIncludedTargetGroups().isEmpty()) ||
				(this.getExtendedTargetGroups() != null && !this.getExtendedTargetGroups().isEmpty()));
	}

	public int getTargetGroupCount() {
		int count = 0;
		if (this.getIncludedTargetGroups() != null) {
			count += getIncludedTargetGroups().size();
		}
		if (this.getExcludedTargetGroups() != null) {
			count += getExcludedTargetGroups().size();
		}
		if (this.getExtendedTargetGroups() != null) {
			count += getExtendedTargetGroups().size();
		}
		return count;
	}

	public String getActionRequired() {
		if (getState() != null && getState().getId() < WorkflowState.STATE_CONTENT) {
			return ApplicationUtil.getMessage("page.work.flow.action.addcontent");
		}

		if (getWorkflowAction() == null && !isReadyForApproval()) {
			return ApplicationUtil.getMessage("page.work.flow.action.markworkcomplete");
		}

		if (getWorkflowAction() == null)
			return ApplicationUtil.getMessage("page.label.na");

		if (getState() != null && WorkflowState.STATE_PRODUCTION == getState().getId())
			return ApplicationUtil.getMessage("page.label.na");

		return getNextAction();
	}

	public String getNextAction() {
		ConfigurableWorkflowAction action = getWorkflowAction();
		if (action == null)
			return ApplicationUtil.getMessage("page.label.na");
		if (action.getConfigurableWorkflowStep() == null)
			return ApplicationUtil.getMessage("page.label.na");
		return action.getConfigurableWorkflowStep().getState();
	}

	public User findLatestReleasedForApprovalUser(){
		ConfigurableWorkflowActionHistory actionHistory = ConfigurableWorkflowActionHistory.findLatestReleasedForApprovalByModel(this);
		User latestReleasedForApprovalUser = null;
		if(actionHistory != null){
			latestReleasedForApprovalUser = User.findById(actionHistory.getUserId());
		}
		return latestReleasedForApprovalUser;
	}

	public boolean getIsMixedDataGroups() {
		boolean result = false;
		if (this.getZone() != null) {
			result = this.getZone().getMixedDataGroups() == Boolean.TRUE;
		}
		return result;
	}

	public long getLockedForId() {
		if (lockedFor != null)
			return lockedFor.longValue();
		else
			return 0L;
	}

	public String getLockedForName() {
		if (lockedFor != null)
		{
			User user = User.findById(lockedFor.longValue());
			if (user != null)
				return user.getName();
		}
		return "";
	}

	public User getLockedForUser() {
		if (lockedFor != null)
		{
			return User.findById(lockedFor.longValue());
		}
		return null;
	}

	public String getAssignedToUserName(){
		return getAssignedToUserName(null);
	}

	public String getAssignedToUserName(ConfigurableWorkflowAction wfAction){
		String assignedTo = "";
		if(this.hasWorkingData()) {
			List<ConfigurableWorkflowAction> wfActionList = this.getExecutableWorkflowActions();
			if(!wfActionList.isEmpty() && this.isPendingApproval()){
				List<String> assignedToUserStrList = new ArrayList<>();
				for(ConfigurableWorkflowAction wfAction2 : wfActionList){
					if(wfAction != null && wfAction2.getId() != wfAction.getId()){
						continue;
					}
					ConfigurableWorkflowStep wfStep = wfAction2.getConfigurableWorkflowStep();
					ConfigurableWorkflow wf = wfStep.getConfigurableWorkflowInstance().getConfigurableWorkflow();
					String assignedToUsersStr = "";

					if(wfStep.getTranslatorType()==TranslatorType.ID_USER) {
						if(wfAction2.getActionApprovers().size()>1){
							if(wfStep.isTranslationStep()){
								for (ConfigurableWorkflowApprovalDetail wfDetail : wfAction2.getApprovalDetailsSorted()) {
									if (wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.REASSIGNED) {
										assignedToUsersStr = wfDetail.getUser().getFullName();
									}
								}
							}else {
								assignedToUsersStr = wfAction2.getActionApprovers().size() + " " + ApplicationUtil.getMessage("page.label.approvers").toLowerCase();
							}
						}else if(wfAction2.getActionApprovers().size()==1){
							assignedToUsersStr = wfAction2.getActionApprovers().get(0).getFullName();
						}
					}else{
						assignedToUsersStr = ApplicationUtil.getMessage("page.label.service");
					}
					assignedToUsersStr += (this.isInSubworkflowStep()?" (" + wf.getName() + ")":"");
					assignedToUserStrList.add(assignedToUsersStr);
				}
				assignedTo = StringUtils.join(assignedToUserStrList, ", ");
			}else{	// WC
				assignedTo = this.getLockedForName();
			}
		}
		return assignedTo;
	}

	public List<MessagepointLocale> getContentObjectLanguagesAsLocales() {

		if (!this.isGlobalContentObject() && this.getFirstDocumentDelivery() != null)
			return this.getFirstDocumentDelivery().getTouchpointLanguagesAsLocales();

		// If the content object is global, it requires all locales used in the instance
		return TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();
	}

	public MessagepointLocale getDefaultContentObjectLanguageAsLocale() {

		if (!this.isGlobalContentObject() && this.getFirstDocumentDelivery() != null)
			return this.getFirstDocumentDelivery().getDefaultTouchpointLanguageLocale();

		// If the content object is global, it requires default system locale used in the instance
		return MessagepointLocale.getDefaultSystemLanguageLocale();
	}

	public Document getFirstDocumentDelivery() {
		if ( this.getDocument() != null )
			return this.getDocument();

		if (this.getZone() != null && this.getZone().getDocument() != null)
			return this.getZone().getDocument();

		if (!this.getVisibleDocuments().isEmpty())
			return this.getVisibleDocuments().iterator().next();

		return null;
	}

	public Set<Document> getDocuments() {

		HashSet<Document> documents = new HashSet<>();

		if ( this.getDocument() != null )
			documents.add( this.getDocument() );

		if (!this.getVisibleDocuments().isEmpty())
			documents.addAll(this.getVisibleDocuments());

		return documents;
	}

	public String getContentTypeName() {
		if (getContentType() == null) {
			return "";
		} else {
			return getContentType().getName();
		}
	}

	public boolean isNeedRecalculateHash() {
		return this.getNeedRecalculateHash();
	}

	public boolean isVisible(Workgroup wg) {
		if ( !this.isMessage() && !isDeliveredToPlaceholder() ) {
			return true;
		} else {
			if ( this.getZone() != null && wg.getZones().contains(this.getZone()) )
				return true;
		}
		return false;
	}

	/**
	 * check if content object id is in tenant_metadata table and externalId matched
	 *
     */
	public static boolean isExternalIdMatched(long msgInsId, String externalId) {
		boolean result = false;
		if (externalId == null)
			return false;
		TenantMetaData metaData = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, msgInsId);
		if (metaData != null) {
			if (metaData.getExternalId() != null) {
				String lowerIdStr = metaData.getExternalId().toLowerCase();
				if (lowerIdStr.indexOf(externalId.toLowerCase()) >= 0)
					result = true;
			}
		}
		return result;
	}

	/// Given a set of instance IDs, return the first ID that comes from this content object
	public Long getInstanceIdFromIds( Set<Long> instanceIds )
	{
		Long miId = this.getLatestContentObjectDataWorkingCentric().getId();
		if ( instanceIds.contains( miId ) ) {
			return miId;
		}
		return 0L;
	}

	public boolean hasTarget(int dataType) {
		boolean result = false;

		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			result = contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getHasTarget();
		}

		if ((dataType & ContentObject.DATA_TYPE_ACTIVE) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			result = result || contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE).getHasTarget();
		}

		if ((dataType & ContentObject.DATA_TYPE_ARCHIVED) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			result = result || contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED).getHasTarget();
		}

		return result;
	}

	public boolean isPendingApproval() {
		if (contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING)) {
			// Check if it is being rejected or aborted
			ConfigurableWorkflowActionHistory latestHistory = ConfigurableWorkflowActionHistory.findLatestHistoryByModel(this, true);
			if (latestHistory != null) {
				if(latestHistory.getWorkflowAction() == null){
					// If the latest history is not associated with a workflow action, then it is not pending approval
					return false;
				}
				if(latestHistory.getAction() == ConfigurableWorkflowActionType.ID_ABORTED) {
					// If the latest history is aborted, then it is not pending approval
					return false;
				}else if(latestHistory.getAction() == ConfigurableWorkflowActionType.ID_REJECTED) {
					// If there is an executable workflow action which is in sub-workflow, then it is still pending approval
					return this.getExecutableWorkflowActions().stream().anyMatch(ConfigurableWorkflowAction::isSubworkflowAction);
				}
			}
			return this.getWorkflowAction() != null;
		}
		return false;
	}

	public ConfigurableWorkflowStep getCurrentWorkflowStep() {
		if ( this.isPendingApproval() ) {
			return this.getWorkflowAction().getConfigurableWorkflowStep();
		}
		return null;
	}

	public List<ConfigurableWorkflowStep> getCurrentSubWorkflowSteps() {
		List<ConfigurableWorkflowStep> wfSteps = new ArrayList<>();
		if ( this.isInSubworkflowStep() ) {
			for( ConfigurableWorkflowAction wfAction : this.getExecutableWorkflowActions()) {
				wfSteps.add(wfAction.getConfigurableWorkflowStep());
			}
		}
		return wfSteps;
	}

	public boolean isAccessRestricted() {
		if (getOwningTouchpointSelection() != null) {
			User principalUser = UserUtil.getPrincipalUser();
			return !getOwningTouchpointSelection().isVisible(principalUser);
		}
		return false;
	}

	public boolean getIsEmailContentDelivery() {
		if (zone != null && (zone.isEmailContent() || (zone.isPrintContent() && getIsEmailChannelContext())) )
			return true;
		return false;
	}

	public boolean getIsEmailSubjectDelivery() {
		if (zone != null && zone.isEmailSubjectLine())
			return true;
		return false;
	}

	public boolean getIsSmsDelivery() {
		if (zone != null && zone.isSms())
			return true;
		return false;
	}

	public boolean getIsWebDelivery() {
		if (zone != null && (zone.isWeb() || (zone.isPrintContent() && getIsWebChannelContext())))
			return true;
		return false;
	}

	public boolean getIsMarkupDelivery() {
		if (zone != null && zone.getZoneTypeId() == ZoneType.ID_MARKUP_ZONE)
			return true;
		return false;
	}

	public boolean getIsEmailChannelContext() {
		if ( this.getChannelContextId() == null )
			return false;
		return this.getChannelContextId() == Channel.CHANNEL_EMAIL_ID;
	}
	public boolean getIsWebChannelContext() {
		if ( this.getChannelContextId() == null )
			return false;
		return this.getChannelContextId() == Channel.CHANNEL_WEB_ID;
	}

	protected Channel getChannelContext() {
		if ( this.getChannelContextId() != null )
			return Channel.findById(this.getChannelContextId());
		return null;
	}

	public String getChannelContextDisplay() {
		if ( this.getIsEmailChannelContext() )
			return Channel.findById(Channel.CHANNEL_EMAIL_ID).getName();
		else if ( this.getIsWebChannelContext() )
			return Channel.findById(Channel.CHANNEL_WEB_ID).getName();
		else if ( this.getChannelContextId() != null && this.getChannelContextId() ==  Channel.CHANNEL_COMPOSITION )
			return Channel.findById(Channel.CHANNEL_COMPOSITION).getName();
		else {
			return ApplicationUtil.getMessage("page.label.none");
		}
	}

	public boolean getSupportsUnicode() {
		// FUTURE USE: Unicode chars in editor
		//if ( this.zones.iterator().next().getDocument().getConnectorConfiguration().getOutputCharacterEncoding() == 3 )
		//	return true;
		return false;
	}

	public String getCanvasDimensions() {
		if ( this.isMessage() ) {
			Document document = UserUtil.getCurrentChannelDocumentContext(zone.getDocument());
			if ( document.isChannelAlternate() )
				return document.findZoneByParent(zone).getCanvasDimensions();

			return zone.getCanvasDimensions();
		} else {

			if ( this.isSharedFreeform() ||
					(this.isSupportsTables(focusOnDataType) && this.getCanvasMaxWidth(focusOnDataType) > 0) ) {
				float canvasWidth = 0f;
				float canvasHeight = 0f;

				try {
					canvasWidth = Float.valueOf( DecimalValueUtil.dehydrate(this.getCanvasMaxWidth(focusOnDataType)) );
					canvasHeight = Float.valueOf( DecimalValueUtil.dehydrate(this.getCanvasMaxHeight(focusOnDataType)) );
				} catch (ParseException e) {
					e.printStackTrace();
				}

				return String.valueOf(canvasWidth) + ":" + String.valueOf(canvasHeight);
			} else if ( this.isDeliveredToPlaceholder() ) {
				float canvasWidth = 0f;
				float canvasHeight = 0f;

				try {
					canvasWidth = Float.valueOf( DecimalValueUtil.dehydrate(this.getZone().getDefaultCanvasWidth()) );
					canvasHeight = 4f;
				} catch (ParseException e) {
					e.printStackTrace();
				}

				return String.valueOf(canvasWidth) + ":" + String.valueOf(canvasHeight);
			}

			return null;
		}
	}
	public int getCanvasRotation() {
		return getZoneRotation();
	}

	public int getZoneRotation() {
		if ( this.isMessage() ) {
			return zone.getRotationAngle();
		} else {
			return 0;
		}
	}

	public boolean getAppliesConnectedAuthoring() {
		if ( this.isMessage() ) {
			return this.getDocument() != null && this.getDocument().isConnectedEnabled();
		} else if ( this.isGlobalContentObject() ) {
			for (Document currentDocument: this.getDocuments() )
				if ( currentDocument.isConnectedEnabled() && this.getUsageTypeId() == LibraryItemUsageType.ID_MESSAGE )
					return true;
		}
		return false;
	}

	public boolean getAppliesForms() {

		if ( this.getIsSharedFreeform() || (!this.isMessage() && !isDeliveredToPlaceholder()) )
			return this.isSupportsForms(focusOnDataType);

		Document document = this.getFirstDocumentDelivery();
		if ( document != null && !document.isNativeCompositionTouchpoint() )
			return false;

		if ( this.getZone() != null && this.getZone().getAreFormsEnabled() )
			return true;

		return false;
	}
	public boolean getAppliesBarcodes() {
		if ( this.getIsSharedFreeform() || (!this.isMessage() && !isDeliveredToPlaceholder()) )
			return this.isSupportsBarcodes(focusOnDataType);
		if ( this.getZone() != null && this.getZone().isSupportsBarcodes() )
			return true;
		return false;
	}
	public boolean getAppliesContentMenus() {
		if ( this.getIsSharedFreeform() || (!this.isMessage() && !isDeliveredToPlaceholder()) )
			return this.isSupportsContentMenus(focusOnDataType);
		return false;
	}
	public boolean getAppliesContentMenusForConnected() {
		if ( this.getDocument() != null && this.getDocument().isConnectedEnabled() && this.getDocument().isCommunicationUseBeta() )
			return true;
		return false;
	}
	public boolean getAppliesImages() {
		if ( this.getIsFreeform() )
			return true;

		if ( !this.isMessage() && !isDeliveredToPlaceholder() )
			return this.isSupportsTables(focusOnDataType);

		Document document = this.getFirstDocumentDelivery();
		if ( document != null && this.getZone() != null ) {
			if ( document.isDialogueTouchpoint() && !getZone().isSupportsTables() )
				return false;
		}

		if ( this.getZone() != null && this.getZone().isSupportsTables() )
			return true;

		return false;
	}

	public boolean getAppliesImagelibraryOnly() {
		if ( this.getZone() != null && !this.getZone().isMessageContentEditing() && this.getZone().getContentTypeId() == ContentType.GRAPHIC )
			return true;
		return false;
	}
	public boolean getAppliesTables() {
		if ( this.getIsSharedFreeform() || (!this.isMessage() && !isDeliveredToPlaceholder()) )
			return this.isSupportsTables(focusOnDataType);

		if ( this.getZone() != null )
			return this.getZone().isSupportsBarcodes() || this.getZone().getAreTablesEnabled();

		return false;
	}

	public boolean getIsVideoDelivery(){
		return contentType.getId() == ContentType.VIDEO;
	}

	public String getZoneBackgroundColor() {
		if (this.getZone() == null)
			return Zone.ZONE_BACKGROUND_DEFAULT;
		Zone zone = Zone.findZoneInContextByParent(this.getZone());

		if ( zone.getContentType().getId() == ContentType.GRAPHIC ||
				zone.getBackgroundColor().equalsIgnoreCase(Zone.ZONE_BACKGROUND_TRANSPARENT) )
			return Zone.ZONE_BACKGROUND_DEFAULT;
		return zone.getBackgroundColor();
	}

	public String getZoneStarterTextStyle() {
		Zone zone = this.getZone();

		if ( zone != null && zone.getStarterTextStyle() != null ) {
			return this.getZone().getStarterTextStyle().getName();
		}

		return "";
	}

	public boolean isInTranslationStep() {
		for(ConfigurableWorkflowAction wfAction : this.getExecutableWorkflowActions()){
			if (wfAction.getConfigurableWorkflowStep() != null && wfAction.getConfigurableWorkflowStep().isTranslationStep()) {
				return true;
			}
		}
		return false;
	}

	public boolean isInApprovalStep() {
		for(ConfigurableWorkflowAction wfAction : this.getExecutableWorkflowActions()){
			if (wfAction.getConfigurableWorkflowStep() != null && (wfAction.getConfigurableWorkflowStep().isApprovalStep() || wfAction.getConfigurableWorkflowStep().isTranslationApprovalStep())) {
				return true;
			}
		}
		return false;
	}

	public boolean isInSubworkflowStep() {
		for(ConfigurableWorkflowAction wfAction : this.getExecutableWorkflowActions()){
			if (wfAction.getConfigurableWorkflowStep() != null && wfAction.getConfigurableWorkflowStep().isSubworkflowStep()) {
				return true;
			}
			// Sub-workflow step need to be checked one level up as sub-workflow step cannot be directly sit
			ObjectWorkflowActionAssociation wfActionAsso = ObjectWorkflowActionAssociation.findByAction(wfAction);
			if(wfActionAsso != null && wfActionAsso.getParentAction() != null && wfActionAsso.getParentAction().getConfigurableWorkflowStep() != null &&
					wfActionAsso.getParentAction().getConfigurableWorkflowStep().isSubworkflowStep()){
				return true;
			}
		}
		return false;
	}

	public boolean isUserNotParticipateInFirstSubworkflowStep(User user, boolean isWorkflowOwner) {
		if(this.isInSubworkflowStep()) {
			for(ConfigurableWorkflowAction wfAction : this.getExecutableWorkflowActions()) {
				ConfigurableWorkflowStep wfStep = wfAction.getConfigurableWorkflowStep();
				if(!wfStep.isFirstStep()) {
					Set<User> translatorOrApproverSet = new HashSet<>();
					if (wfStep.isApprovalStep() || wfStep.isTranslationApprovalStep()) {
						translatorOrApproverSet.addAll(wfAction.getActionApprovers());
					} else if (wfStep.isTranslationStep()) {
						wfAction.getApprovalDetailsSorted()
								.stream().filter(d -> d.getApproved() == ConfigurableWorkflowApprovalDetail.REASSIGNED).findFirst().ifPresent(wfDetail -> translatorOrApproverSet.add(wfDetail.getUser()));
					}

					if(translatorOrApproverSet.contains(user) || isWorkflowOwner) {
						return true;
					}
				}
			}
			return false;
		}
		return true;
	}

	public List<User> getCurrentTranslators(){
		List<User> translators = new ArrayList<>();
		for(ConfigurableWorkflowAction wfAction : this.getExecutableWorkflowActions()) {
			if (wfAction.getConfigurableWorkflowStep() != null && wfAction.getConfigurableWorkflowStep().isTranslationStep()) {
                wfAction.getApprovalDetailsSorted()
                        .stream().filter(d -> d.getApproved() == ConfigurableWorkflowApprovalDetail.REASSIGNED).findFirst().ifPresent(wfDetail -> translators.add(wfDetail.getUser()));
            }
		}
		return translators;
	}

	// End of Statuses

	//

	public ContentObjectData getContentObjectDataWorkingCentric() {
		if (this.hasWorkingData())
			return getContentObjectData(ContentObject.DATA_TYPE_WORKING);
		if (this.hasActiveData())
			return getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
		if (this.hasArchivedData())
			return getContentObjectData(ContentObject.DATA_TYPE_ARCHIVED);

		return null;
	}

	public ContentObjectData getContentObjectData() {
		return getContentObjectData(focusOnDataType);
	}

	public ContentObjectData getContentObjectData(int dataType) {
		if (contentObjectDataTypeMap.containsKey(dataType))
		{
			return contentObjectDataTypeMap.get(dataType);
		}

		return null;
	}

	/**
	 * Check whether this content object is delivered to the exact target touchpoint
     */
	public boolean isDeliveredToExactTargetTouchpoint(){
		Set<Document> documents = this.getDocuments();
		for ( Document document : documents ) {
			if ( document.isExactTargetTouchpoint() )
				return true;
		}
		return false;
	}

	public boolean isDeliveredToWebOrEmailTouchpoint(){
		Set<Document> documents = this.getDocuments();
		for ( Document document : documents ) {
			if ( document.isWebTouchpoint() || document.isEmailTouchpoint() )
				return true;
		}
		return false;
	}

	public boolean isDeliveredToSefasTouchpoint(){
		Set<Document> documents = this.getDocuments();
		for ( Document document : documents ) {
			if ( document.getIsSefasCompositionTouchpoint() || document.getIsMPHCSCompositionTouchpoint() )
				return true;
		}
		return false;
	}

	public boolean isGMCTouchpoint(){
		Set<Document> documents = this.getDocuments();
		for ( Document document : documents ) {
			if ( document.getIsGMCTouchpoint() )
				return true;
		}
		return false;
	}

	public String getDeliveryNavTreeHTML() {
		StringBuilder returnHTML = new StringBuilder();

		Document touchpointContext = UserUtil.getCurrentTouchpointContext();

		if (!touchpointContext.getDocumentSections().isEmpty() && this.getZone() != null)
			for (DocumentSection section : touchpointContext.getDocumentSections())
				if (!section.getZones().isEmpty())
					for (Zone currentZone : section.getZones())
						if (currentZone.isEnabled() && this.getZone().getId() == currentZone.getId())
							returnHTML.append("<div id=\"").append(touchpointContext.getId()).append("_").append(section.getId()).append("_").append(currentZone.getId()).append("\" class=\"rounded border border-primary bg-primary-lightest text-primary fs-xs font-weight-bold px-2 m-1\">").append(TxtFmtTag.maxTxtLengh(currentZone.getNameInContext(), 40)).append("</div>");

		return returnHTML.toString();
	}

	@SuppressWarnings("unchecked")
	public Set<DataGroup> getZoneDataGroups() {
		// For each zone determine which level the zone's datagroup
		// is and return the intersection of the zones.
		if (this.getZone() == null)
			return null;
		Set<DataGroup>[] dataGroupSetArray = new Set[1];

		int index = 0;
		DataGroup dataGroup = this.getZone().getDataGroup();
		if ( dataGroup != null ) {
			dataGroupSetArray[index] = dataGroup.getDataGroupFamily();
			index++;
		}

		for (int i = 1; i < index; i++) {
			dataGroupSetArray[0].retainAll(dataGroupSetArray[i]);
		}

		return dataGroupSetArray[0];
	}

	// Find methods
	//

	public static ContentObject findById(long id) {
		return findByIdWorkingDataFocusCentric(id);
	}

	public int checkAndAdjustFocus() {
	    // focusOnDataType is volatile when contentObjectDataTypeMap is initialized
	    int focusOnDataTypeExpected = this.focusOnDataType;

		if (!contentObjectDataTypeMap.containsKey(focusOnDataTypeExpected)) {
			if (this.hasWorkingData())
                focusOnDataTypeExpected = ContentObject.DATA_TYPE_WORKING;
			else if (this.hasActiveData())
                focusOnDataTypeExpected = ContentObject.DATA_TYPE_ACTIVE;
			else
                focusOnDataTypeExpected = ContentObject.DATA_TYPE_ARCHIVED;

            this.setFocusOnDataType(focusOnDataTypeExpected);
		}

        this.focusOnDataType = focusOnDataTypeExpected;

		return this.focusOnDataType;
	}

	public static ContentObject findByHttpServletRequest( HttpServletRequest request ) {
		long contentObjectId = ServletRequestUtils.getLongParameter(request, REQ_PARM_CONTENT_OBJECT_ID, -1L);
		String dna = ServletRequestUtils.getStringParameter(request, REQ_PARM_OBJECT_DNA, "");

		int dataType = ServletRequestUtils.getIntParameter(request, REQ_PARM_DATA_TYPE, -1);
		if (dataType < 1)
			dataType = ContentObjectDynamicVariantViewController.getStatusViewIdParam(request);
		if (dataType < 1)
			dataType = ContentObject.DATA_TYPE_WORKING;

		ContentObject contentObject = null;
		if(contentObjectId > 0) {
			contentObject = ContentObject.findById(contentObjectId);
		}else if(!dna.isEmpty()){
			contentObject = ContentObject.findByDna(dna);
			if(contentObject == null){
				contentObject = ContentObject.findByDnaAndDocument(dna, UserUtil.getCurrentTouchpointContext());
			}
		}
		if (contentObject != null) {
			contentObject.setFocusOnDataType(dataType);
			contentObject.checkAndAdjustFocus();
		}

		return contentObject;
	}

	public static ContentObject findByDnaActiveDataFocusCentric(String dna, Document document) {
		ContentObject contentObject = null;
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("dna", dna));
		if(document != null){
			critList.add(MessagepointRestrictions.eq("document", document));
		}
		List<ContentObject> list = HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
		if (list != null && !list.isEmpty())
		{
			contentObject = list.get(0);
			if (contentObject.hasActiveData())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
			else if (contentObject.hasWorkingData())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
			else
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ARCHIVED);
		}

		return contentObject;
	}

	public static ContentObject findByDnaActiveDataFocusCentric(String dna) {
		return findByDnaActiveDataFocusCentric(dna, null);
	}

	public static ContentObject findByIdActiveDataFocusCentric(long id) {
		ContentObject contentObject = HibernateUtil.getManager().getObject(ContentObject.class, id);
		if (contentObject != null)
		{
			if (contentObject.hasActiveData())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
			else if (contentObject.hasWorkingData())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
			else
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ARCHIVED);
		}

		return contentObject;
	}

	public static ContentObject findByDnaWorkingDataFocusCentric(String dna, Document document) {
		ContentObject contentObject = null;
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("dna", dna));
		if(document != null){
			critList.add(MessagepointRestrictions.eq("document", document));
		}
		List<ContentObject> list = HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
		if (list != null && !list.isEmpty())
		{
			contentObject = list.get(0);
			if (contentObject.hasWorkingData())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
			else if (contentObject.hasActiveData())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
			else
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ARCHIVED);
		}

		return contentObject;
	}

	public static ContentObject findByDnaWorkingDataFocusCentric(String dna) {
		return findByDnaWorkingDataFocusCentric(dna, null);
	}

	public static ContentObject findByIdWorkingDataFocusCentric(long id) {
		ContentObject contentObject = HibernateUtil.getManager().getObject(ContentObject.class, id);
		if (contentObject != null)
		{
			if (contentObject.hasWorkingData())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
			else if (contentObject.hasActiveData())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
			else
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ARCHIVED);
		}

		return contentObject;
	}

	public static ContentObject findByDnaArchivedDataFocusCentric(String dna, Document document) {
		ContentObject contentObject = null;
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("dna", dna));
		if(document != null){
			critList.add(MessagepointRestrictions.eq("document", document));
		}
		List<ContentObject> list = HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
		if (list != null && !list.isEmpty())
		{
			contentObject = list.get(0);
			if (contentObject.hasArchivedData())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ARCHIVED);
			else if (contentObject.hasWorkingData())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
			else
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
		}

		return contentObject;
	}

	public static ContentObject findByDnaArchivedDataFocusCentric(String dna) {
		return findByDnaArchivedDataFocusCentric(dna, null);
	}

	public static ContentObject findByIdArchivedDataFocusCentric(long id) {
		ContentObject contentObject = HibernateUtil.getManager().getObject(ContentObject.class, id);
		if (contentObject != null)
		{
			if (contentObject.hasArchivedData())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ARCHIVED);
			else if (contentObject.hasWorkingData())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
			else
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
		}

		return contentObject;
	}

	public static ContentObject findByInternalId(long internalId) {
		TenantMetaData tmd = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, internalId);
		if (tmd != null) {
			return ContentObject.findById(tmd.getInternalId());
		}
		return null;
	}

	public static List<ContentObject> findAllLocalTextByDocumentAndSelection(Document document, TouchpointSelection touchpointSelection){
		return findAllLocalAssetByAdvancedQuery(document, touchpointSelection, false, ContentType.TEXT, "", -1, false);
	}

	public static List<ContentObject> findAllLocalImageByDocumentAndSelection(Document document, TouchpointSelection touchpointSelection){
		return findAllLocalAssetByAdvancedQuery(document, touchpointSelection, false, ContentType.GRAPHIC, "", -1, false);
	}
	public static List<ContentObject> findAllLocalCanvasByDocumentAndSelection(Document document, TouchpointSelection touchpointSelection, Boolean supportsTables,
																		Boolean supportsForms, int canvasMaxWidth, int canvasMaxHeight){
		return findAllLocalAssetByAdvancedQuery(document, touchpointSelection, false, ContentType.SHARED_FREEFORM, "", -1, supportsTables, supportsForms, canvasMaxWidth, canvasMaxHeight, false);
	}

	public static List<ContentObject> findAllLocalAssetByAdvancedQuery(Document document, TouchpointSelection touchpointSelection, boolean includesArchived, long contentTypeId, String nameSearchStr, int numCap, Boolean includePlaceholderAssets) {
		return findAllLocalAssetByAdvancedQuery(document, touchpointSelection, includesArchived, contentTypeId, nameSearchStr, numCap, null, null, 0, 0, includePlaceholderAssets);
	}

	public static List<ContentObject> findAllLocalAssetByAdvancedQuery(Document document, TouchpointSelection touchpointSelection, boolean includesArchived, long contentTypeId,
																String nameSearchStr, int numCap, Boolean supportsTables, Boolean supportsForms, int canvasMaxWidth,
																int canvasMaxHeight, Boolean includePlaceholderAssets) {

		Map<String, String> firstLevelJoinAlias 		= new LinkedHashMap<>();
		Map<String, String> secondLevelJoinAlias 		= new LinkedHashMap<>();
		List<MessagepointCriterion> firstLevelCriterionList 	= new ArrayList<>();
		List<MessagepointCriterion> secondLevelCriterionList 	= new ArrayList<>();
		Map<String, JoinType> secondLevelJoinType	= new HashMap<>();
		List<MessagepointOrder> orderList 						= new ArrayList<>();

		// VARIANT CONTENT OBJECT (The variant content object can be shared from its ancestor selections)
		if ( touchpointSelection != null && document.isEnabledForVariation() ) {
			Set<Long> tpSelectionIds = new HashSet<>();
			TouchpointSelection parentSelection = touchpointSelection;
			while ( parentSelection != null ) {
				tpSelectionIds.add( parentSelection.getId() );
				parentSelection = parentSelection.getParent();
			}

			if (tpSelectionIds.isEmpty())
				tpSelectionIds.add((long) 0);

			firstLevelCriterionList.add(MessagepointRestrictions.or(MessagepointRestrictions.isNull("owningTouchpointSelection"), MessagepointRestrictions.in("owningTouchpointSelection.id", tpSelectionIds)));
		}

		firstLevelJoinAlias.put("document", "document");
		firstLevelJoinAlias.put("contentType", "contentType");

		// SEARCH by name and/or metatags
		if ( nameSearchStr != null && !nameSearchStr.isEmpty() && !"NULL".equals(nameSearchStr) ) {
			Map<String, List<String>> searchLists = TagCloud.tokenizeSearchString(nameSearchStr);
			for (int i=0; i < searchLists.get("tags").size(); i++) {
				firstLevelCriterionList.add(MessagepointRestrictions.ilike("metatags", "%" + searchLists.get("tags").get(i).toLowerCase() + "%"));
			}
			for (int i=0; i < searchLists.get("names").size(); i++) {
				firstLevelCriterionList.add(MessagepointRestrictions.ilike("name", "%" + searchLists.get("names").get(i).toLowerCase() + "%"));
			}
		}

		// TOUCHPOINT LOCALS
		firstLevelCriterionList.add(MessagepointRestrictions.in("objectType", Arrays.asList(OBJECT_TYPE_LOCAL_SMART_TEXT, OBJECT_TYPE_LOCAL_IMAGE)));

		firstLevelCriterionList.add(MessagepointRestrictions.eq("contentType.id", contentTypeId));
		firstLevelCriterionList.add(MessagepointRestrictions.eq("document.id", document.getId()));

		secondLevelJoinAlias.put("contentObjectDataTypeMap", "coi");

		if ( supportsTables != null && !supportsTables )
			secondLevelCriterionList.add(MessagepointRestrictions.eq("coi.supportsTables", false));
		if ( supportsForms != null && !supportsForms )
			secondLevelCriterionList.add(MessagepointRestrictions.eq("coi.supportsForms", false));

		// SHARED CANVAS
		if ( contentTypeId == ContentType.SHARED_FREEFORM ) {
			if ( canvasMaxWidth > 0 && canvasMaxHeight > 0 ) {
				secondLevelCriterionList.add(MessagepointRestrictions.le("coi.canvasTrimWidth", canvasMaxWidth));
				secondLevelCriterionList.add(MessagepointRestrictions.le("coi.canvasTrimHeight", canvasMaxHeight));
			}
		}

		if ( !includePlaceholderAssets )
			firstLevelCriterionList.add(MessagepointRestrictions.isNull("zone"));

		PostQueryHandler postHandler = null;

		// SORT
		orderList.add(MessagepointOrder.asc(HibernateObjectManager.MODEL_ALIAS_PREFIX + ".name"));

		ServiceExecutionContext context = HibernatePaginationService.createContext(ContentObject.class, null, firstLevelCriterionList, secondLevelCriterionList, firstLevelJoinAlias, secondLevelJoinAlias, secondLevelJoinType, 1, numCap, orderList, null, postHandler);
		Service paginationService = MessagepointServiceFactory.getInstance().lookupService(HibernatePaginationService.SERVICE_NAME, HibernatePaginationService.class);
		paginationService.execute(context);
		PaginationServiceResponse serviceResponse = (PaginationServiceResponse) context.getResponse();
		List<?> list = serviceResponse.getPage().getList();

		List<ContentObject> contentObjects = new ArrayList<>();
		for (Object o : list) {
			if(o instanceof ContentObject) {
				ContentObject contentObj = (ContentObject) o;
				if (contentObj.hasActiveData() || contentObj.hasWorkingData()) {
					contentObjects.add(contentObj);
				} else if (includesArchived && contentObj.hasArchivedData()) {
					contentObjects.add(contentObj);
				}
			}
		}
		return contentObjects;
	}

	public static List<ContentObject> findAllGlobalAssetByAdvancedQuery(Document document, Zone zone, ContentObject selectedGlobalAsset, boolean filterForGlobal, boolean filterForCommunications,
															   long contentTypeId, boolean activeOnly, boolean includesArchived, String nameSearchStr, int numCap) {
		return findAllGlobalAssetByAdvancedQuery(document, zone, selectedGlobalAsset, filterForGlobal, filterForCommunications,
				contentTypeId, activeOnly, includesArchived, nameSearchStr, numCap, null, null, null, null, 0, 0);
	}
	public static List<ContentObject> findAllGlobalAssetByAdvancedQuery(Document document, Zone zone, ContentObject selectedGlobalAsset, boolean filterForGlobal,
															   boolean filterForCommunications, long contentTypeId, boolean activeOnly, boolean includesArchived,
															   String nameSearchStr, int numCap, Boolean supportsTables, Boolean supportsForms, Boolean supportsBarcodes, Boolean supportsContentMenus,
															   int canvasMaxWidth, int canvasMaxHeight) {
		Set<Document> documents = new HashSet<>();
		if ( document != null )
			documents.add(document);
		return findAllGlobalAssetByAdvancedQuery(documents, zone, selectedGlobalAsset, filterForGlobal, filterForCommunications, contentTypeId, activeOnly, includesArchived,
				nameSearchStr, numCap, supportsTables, supportsForms, supportsBarcodes, supportsContentMenus, canvasMaxWidth, canvasMaxHeight, false);
	}

	public static List<ContentObject> findAllGlobalAssetByAdvancedQuery(Set<Document> documents, Zone zone, ContentObject selectedGlobalAsset, boolean filterForGlobal,
															   boolean filterForCommunications, long contentTypeId, boolean activeOnly, boolean includesArchived,
															   String nameSearchStr, int numCap, Boolean supportsTables, Boolean supportsForms, Boolean supportsBarcodes, Boolean supportsContentMenus,
															   int canvasMaxWidth, int canvasMaxHeight, Boolean snippetsOnly) {

		List<Document> appliedDocuments = new ArrayList<>();
		if (!documents.isEmpty()) {
			for ( Document currentDocument: documents )
				appliedDocuments.add( currentDocument.getRootDocument() );
		}

		StringBuilder query = new StringBuilder("	SELECT DISTINCT	co ");

		if ( zone != null && zone.isRestrictSharedAssets() )
			query.append("						FROM 			ContentObject as co, Zone as z ");
		else
			query.append("						FROM 			ContentObject as co ");

		query.append("											, ContentObjectData as cod ");
		query.append("							WHERE			co.id = cod.contentObject.id ");
		if(!includesArchived){
			query.append("							AND			cod.dataType != 4 ");
		}

		// USAGE
		if ( filterForGlobal )
			query.append("    					AND				co.variableContentEnabled = false ");
		if ( selectedGlobalAsset != null ) {
			query.append("						AND 			(co.usageTypeId = ").append(selectedGlobalAsset.getUsageTypeId()).append(") ");
		} else {
			if ( filterForCommunications )
				query.append("    				AND				(co.usageTypeId = " + LibraryItemUsageType.ID_COMMUNICATION + " OR co.usageTypeId = " + LibraryItemUsageType.ID_UNRESTRICTED + ") ");
			else
				query.append("    				AND				(co.usageTypeId = " + LibraryItemUsageType.ID_MESSAGE + " OR co.usageTypeId = " + LibraryItemUsageType.ID_UNRESTRICTED + ") ");
		}

		Map<String, Object> params = new HashMap<>();

		//"AND 		d.id IN (SELECT z.document.id FROM Zone z, Workgroup wg WHERE wg.id = :workgroupid AND z IN elements(wg.zones) AND z.enabled = true ) " +
		// ZONE
		if ( zone != null && zone.isRestrictSharedAssets() ) {
			query.append("						AND 			z.id = :zoneId ");
			query.append("						AND 			co.id IN (SELECT zst.id FROM z.smartTextAssets AS zst) ");
			params.put("zoneId", zone.getId());
		}

		// DOCUMENT
		if (!appliedDocuments.isEmpty()) {
			query.append("						AND				(d.id IN :documentIds OR co.variableContentEnabled = false OR co.id IN (:ids)) ");
			List<Long> documentIds = new ArrayList<>();
			for (Document currentDocument: appliedDocuments)
				documentIds.add( currentDocument.getId() );
			params.put("documentIds", documentIds);
			params.put("ids", findAllIdsByDocumentInCollection(appliedDocuments));
		}

		// IF ACTIVE ONLY
		if ( activeOnly ) {
			query.append("    					AND				cod.dataType = 2 ");
		}

		if ( contentTypeId > -1 ) {
			query.append("    					AND				co.contentType.id = :contentTypeId ");
			params.put("contentTypeId", contentTypeId);
		}

		if ( supportsTables != null && !supportsTables ) {
			query.append("    				AND				cod.supportsTables = :supportsTables ");
			params.put("supportsTables", false);
		}
		if ( supportsForms != null && !supportsForms ) {
			query.append("    				AND				cod.supportsForms = :supportsForms ");
			params.put("supportsForms", false);
		}
		if ( supportsBarcodes != null && !supportsBarcodes ) {
			query.append("    				AND				cod.supportsBarcodes = :supportsBarcodes ");
			params.put("supportsBarcodes", false);
		}
		if ( supportsContentMenus != null && !supportsContentMenus ) {
			query.append("    				AND				cod.supportsContentMenus = :supportsContentMenus ");
			params.put("supportsContentMenus", false);
		}
		if ( contentTypeId == ContentType.SHARED_FREEFORM ) {
			if ( canvasMaxWidth > 0 && canvasMaxHeight > 0 ) {
				query.append("    					AND				cod.canvasTrimWidth <= :canvasMaxWidth ");
				query.append("    					AND				cod.canvasTrimHeight <= :canvasMaxHeight ");
				params.put("canvasMaxWidth", canvasMaxWidth);
				params.put("canvasMaxHeight", canvasMaxHeight);
			}
		}

		if ( snippetsOnly ) {
			query.append("    					AND				cod.insertAsBlockContent != :snippetsOnly ");
			params.put("snippetsOnly", snippetsOnly);
		}

		// SEARCH
		if ( nameSearchStr != null && !nameSearchStr.isEmpty()) {
			query.append("			AND ");

			Map<String, List<String>> searchLists = TagCloud.tokenizeSearchString(nameSearchStr);

			for (int i=0; i < searchLists.get("tags").size(); i++) {
				if ( i != 0 )
					query.append("	AND ");
				query.append("		LOWER(co.metatags) LIKE '%").append(searchLists.get("tags").get(i).toLowerCase().replaceAll("_", "|_").replaceAll("%", "|%")).append("%' ESCAPE '|' ");

			}
			for (int i=0; i < searchLists.get("names").size(); i++) {
				if ( i != 0 || !searchLists.get("tags").isEmpty())
					query.append("	AND ");
				query.append("		LOWER(co.name) LIKE '%").append(searchLists.get("names").get(i).toLowerCase().replaceAll("_", "|_").replaceAll("%", "|%")).append("%' ESCAPE '|' ");

			}
		}


		List<ContentObject> globalAssets = null;
		if ( numCap > 0 )
			globalAssets = (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params, 1, numCap, "");
		else
			globalAssets = (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);

		return globalAssets;
	}


	public static List<ContentObject> findAllContentObjectsByDocument(Document document) {
        StringBuilder query = new StringBuilder("SELECT DISTINCT co ");
        query.append("                          FROM            ContentObject as co ");
        query.append("                          WHERE           co.document.id = ").append(document.getId());
        query.append("                          AND             co.objectType in (" +
                ContentObject.OBJECT_TYPE_MESSAGE + "," +
                ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT + "," +
                ContentObject.OBJECT_TYPE_LOCAL_IMAGE +
                ")");
        query.append("                          AND             co.removed  = false");

        return new ArrayList<>((List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString()));
    }

	public static List<ContentObject> findAllContentObjectsByDocumentIncludeRemoved(Document document) {
		StringBuilder query = new StringBuilder("SELECT DISTINCT co ");
		query.append("                          FROM            ContentObject as co ");
		query.append("                          WHERE           co.document.id = ").append(document.getId());
		query.append("                          AND             co.objectType in (" +
				ContentObject.OBJECT_TYPE_MESSAGE + "," +
				ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT + "," +
				ContentObject.OBJECT_TYPE_LOCAL_IMAGE +
				")");

        return new ArrayList<>((List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString()));
	}

	public static List<ContentObject> findAllLocalAssetsByDocument(Document document) {
        StringBuilder query = new StringBuilder("SELECT DISTINCT co ");
        query.append("                          FROM            ContentObject as co ");
        query.append("                          WHERE           co.document.id = ").append(document.getId());
        query.append("                          AND             co.objectType in (" +
                    ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT + "," +
                    ContentObject.OBJECT_TYPE_LOCAL_IMAGE +
                ")");
        query.append("                          AND             co.removed  = false");

        return new ArrayList<>((List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString()));
    }

	public static List<ContentObject> findAllByGlobalContentObject(ContentObject contentObject){
        StringBuilder query = new StringBuilder("SELECT DISTINCT co ");
        query.append("                          FROM            ContentObject as co ");
        query.append("                          WHERE           co.globalContentObject.id = ").append(contentObject.getId());

        return new ArrayList<>((List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString()));
	}

	public static List<ContentObject> findByGlobalContentObject(ContentObject contentObject, Document document){
        StringBuilder query = new StringBuilder("SELECT DISTINCT co ");
        query.append("                          FROM            ContentObject as co ");
        query.append("                          WHERE           co.document.id = ").append(document.getId());
        query.append("                          AND             co.globalContentObject.id = ").append(contentObject.getId());

        return new ArrayList<>((List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString()));
	}

	public static List<ContentObject> findAllWithObjectType(int... types) {
		List<Integer> typeList = new ArrayList<>(); // required due to int/Integer casting issue
		for (int i : types) {
			typeList.add(i);
		}
		ArrayList<MessagepointCriterion> criList = new ArrayList<>();
		criList.add(MessagepointRestrictions.in("objectType", typeList));
		return HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, criList);
	}

	public static List<ContentObject> findAllGlobalImageLibraryItems(){
		ArrayList<MessagepointCriterion> criList = new ArrayList<>();
		criList.add(MessagepointRestrictions.eq("objectType", OBJECT_TYPE_GLOBAL_IMAGE));
		return HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, criList);
	}

	public String getExternalId() {
		TenantMetaData tmd = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, this.getId());
		if (tmd != null)
			return tmd.getExternalId();
		else
			return ApplicationUtil.getMessage("page.label.none");
	}

	public String getExternalIdOrNull() {
		TenantMetaData tmd = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, this.getId());
		if (tmd != null)
			return tmd.getExternalId();
		else
			return null;
	}

	public static List<ContentObject> findByExternalId(String externalId) {
		List<TenantMetaData> list = TenantMetaData.findByExternalId(TenantMetaData.MODEL_SIGN_MESSAGE, externalId);
		List<ContentObject> messages = new ArrayList<>();
		for (TenantMetaData currentItem: list)
			messages.add(findById(currentItem.getInternalId()));
		return messages;
	}

	private static Set<ContentObjectAssociation> getAssociationsForZonePart(ZonePart zp, List<ContentObjectAssociation> contAssocs)
	{
	    Set<ContentObjectAssociation> result = new HashSet<>();
	    for( ContentObjectAssociation ca : contAssocs )
	    {
	        if ( ca.getZonePart() != null && ca.getZonePart().getId() == zp.getId() )
	        {
	            result.add(ca);
	        }
	    }
	    return result;
	}

	public static class MessagepointLocaleComparatorOnName implements Comparator<MessagepointLocale> {

		public int compare(MessagepointLocale arg0, MessagepointLocale arg1) {
			MessagepointLocale firstML = (MessagepointLocale)arg0;
			MessagepointLocale secondML = (MessagepointLocale)arg1;

			return firstML.getName().compareTo(secondML.getName());
		}
	}

	public static ContentObject findByGuid(String guid) {
		ContentObject result = null;

		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
	    critList.add(MessagepointRestrictions.eq("guid", guid));

	    List<ContentObject> list = HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
	    if (list != null && !list.isEmpty())
	    {
	    	result = list.get(0);
	    	result.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_WORKING);
	    }

	    return result;
	}

	public static ContentObject findGlobalObjectByDna(String dna) {
		ContentObject result = null;
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("dna", dna));
        critList.add(MessagepointRestrictions.isNull("document"));
        critList.add(MessagepointRestrictions.in("objectType",
				Arrays.asList(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT, ContentObject.OBJECT_TYPE_GLOBAL_IMAGE)));
		List<ContentObject> list = HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
		if (list != null && !list.isEmpty())
		{
			result = list.get(0);
			result.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_WORKING);
		}

		return result;
	}

    public static ContentObject findByDna(String dna) {
    	return findGlobalObjectByDna(dna);
    }

    public static ContentObject findByName(String name, int objectType) {
        return findByName(name, objectType, null);
    }

	public static ContentObject findByName(String name, int objectType, Boolean removed) {
		ContentObject result = null;

		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("name", name));
		if (objectType >= 0)
			critList.add(MessagepointRestrictions.eq("objectType", objectType));

        if (removed != null) {
            critList.add(MessagepointRestrictions.eq("removed", removed.booleanValue()));
        }

		List<ContentObject> list = HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
		if (list != null && !list.isEmpty())
		{
			result = list.get(0);
			result.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_WORKING);
		}

		return result;
	}

	public static JSONArray getListAsJSON(List<ContentObject> list) {
    	JSONArray jsonList = new JSONArray();

    	for ( ContentObject currentItem: list ) {
    		JSONObject jsonItem = new JSONObject();

    		try {
				jsonItem.put("name"	, currentItem.getName());
				jsonItem.put("id"	, currentItem.getId());
				jsonItem.put("tags"	, currentItem.getMetatags() != null ? currentItem.getMetatags() : "");

			} catch (JSONException e) {
				log.error("Error generating content object JSON list");
			}

    		jsonList.put(jsonItem);
    	}

    	return jsonList;
    }

	public static ContentObject findByDnaAndDocument(ContentObject fellow, Document document) {
		ContentObject result = null;

        if (document != null && fellow != null){
            String dna = fellow.getDna();
            result = findByDnaAndDocument(dna, document);
        }

        return result;
    }

	@SuppressWarnings("unchecked")
	public static ContentObject findByDnaAndDocument(String dna, Document document) {
		ContentObject result = null;

		if (document != null && dna != null){
			StringBuilder query = new StringBuilder("SELECT DISTINCT co ");
			query.append("                          FROM            ContentObject as co ");
            query.append("                          JOIN            co.document as d ");
			query.append("                          WHERE           d.id = :documentId ");
			query.append("                          AND             co.dna = :dna ");
            query.append("                          AND             co.objectType in (0,1,2) ");

			Map<String, Object> params = new HashMap<>();
			params.put("documentId", document.getId());
			params.put("dna", dna);

            List<ContentObject> orderedList = new ArrayList<>((List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params));
			if (!orderedList.isEmpty())
				result = orderedList.get(0);
		}

		return result;
	}

	// Operations
	//

	public int createWorkingDataEmpty() {
		int result = REQUEST_GOT_AN_ERROR;
		long totalStartTime = System.currentTimeMillis();

		if (contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			// Working copy already exists
			result = REQUEST_NOT_APPLICABLE;
		}
		else {
			ContentObjectData contentObjectData = new ContentObjectData();
			contentObjectData.setDataType(ContentObject.DATA_TYPE_WORKING);
			contentObjectData.setContentObject(this);
			contentObjectData.setStatus(VersionStatus.findById(VersionStatus.VERSION_WIP));

			contentObjectDataTypeMap.put(ContentObject.DATA_TYPE_WORKING, contentObjectData);

            contentObjectData.getLanguageContentHash().save();
			contentObjectData.save();

			this.save();

            if (this.getZone() != null)
                ContentObjectZonePriority.addWorkingDataAfterActive(this, this.getZone());

            // Save data to HistoricalContentObjectData for history tracking purpose
			HistoricalContentObjectData histContentObjectData = HistoricalContentObjectData.create(this, contentObjectData.getGuid());
			histContentObjectData.save();

			result = REQUEST_SUCCESSFUL;
		}

		log.info("createWorkingDataFrom took " + (System.currentTimeMillis() - totalStartTime) + " ms");
		log.info("createWorkingDataFrom took " + ((System.currentTimeMillis() - totalStartTime)/1000/60) + " mins");
		return result;
	}

	public int createWorkingDataFrom(ContentObjectData originalContentObjectData) {
		int result = REQUEST_GOT_AN_ERROR;

		if (contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING) || originalContentObjectData == null)
		{
			// Working copy already exists or active copy doesn't exist
			result = REQUEST_NOT_APPLICABLE;
		}
		else {

			// Process only top level of COA if the content object is local (this.getDocument() != null), structured and the touchpoint is enabled for a variant workflow
			//
			boolean processOnlyTopLevel = (this.getDocument() != null && this.getDocument().isEnabledForVariantWorkflow() && this.isStructuredContentEnabled());

			// If the content object is enabled for dynamic variant, then we need to process only the top level of COA if we are cloning from active version
			//
			if (this.isDynamicVariantEnabled() && originalContentObjectData.isActive())
				processOnlyTopLevel = true;

			// Process only top level of COA if the content object is structured, and we are cloning from active version
			//
			if (!processOnlyTopLevel)
				processOnlyTopLevel = (this.isStructuredContentEnabled() && originalContentObjectData.isActive());

			long startTime = System.currentTimeMillis();

            // Delete working copy of dynamic variants if there are some from previous data corruption
            //
            if (this.isDynamicVariantEnabled())
            {
                StringBuilder deleteQuery = new StringBuilder();
                deleteQuery.append("DELETE from parameter_group_instance where pg_instance_collection_id in (select pgi_collection_id from pg_tree_node WHERE CONTENT_OBJECT_ID = ").append(this.getId());
                deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_WORKING).append("); ");

                deleteQuery.append("DELETE from pg_instance_collection where id in (select pgi_collection_id from pg_tree_node WHERE CONTENT_OBJECT_ID = ").append(this.getId());
                deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_WORKING).append("); ");

                deleteQuery.append("DELETE FROM HIST_CONTENT_OBJ_ASSOCIATION WHERE CO_PG_TN_ID IN (SELECT ID FROM PG_TREE_NODE WHERE CONTENT_OBJECT_ID = ").append(this.getId());
                deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_WORKING).append("); ");

                deleteQuery.append("DELETE FROM HIST_CONTENT_OBJ_ASSOCIATION WHERE REF_CO_PG_TN_ID IN (SELECT ID FROM PG_TREE_NODE WHERE CONTENT_OBJECT_ID = ").append(this.getId());
                deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_WORKING).append("); ");

                deleteQuery.append("DELETE FROM CONTENT_OBJECT_ASSOCIATION WHERE CO_PG_TN_ID IN (SELECT ID FROM PG_TREE_NODE WHERE CONTENT_OBJECT_ID = ").append(this.getId());
                deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_WORKING).append("); ");

                deleteQuery.append("DELETE FROM CONTENT_OBJECT_ASSOCIATION WHERE REF_CO_PG_TN_ID IN (SELECT ID FROM PG_TREE_NODE WHERE CONTENT_OBJECT_ID = ").append(this.getId());
                deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_WORKING).append("); ");

                deleteQuery.append("DELETE FROM CONTENT_OBJECT_ASSOCIATION WHERE CONTENT_OBJECT_ID = ").append(this.getId());
                deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_WORKING).append("; ");

                deleteQuery.append("DELETE FROM PG_TREE_NODE WHERE CONTENT_OBJECT_ID = ").append(this.getId());
                deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_WORKING).append("; ");

                NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(deleteQuery.toString());
                int count = sqlQuery.executeUpdate();
                if (count > 0)
                {
                    log.error("Previous working copy of dynamic variants were found and deleted: " + count + " records");
                }
                log.info("Check and delete corrupted dynamic variants took " + (System.currentTimeMillis() - startTime) + " ms");
            }

			ContentObjectData workingCopyAttributes = (ContentObjectData) originalContentObjectData.clone(ContentObject.findByGuid(this.getGuid()), ContentObject.DATA_TYPE_WORKING, processOnlyTopLevel, false);
			workingCopyAttributes.setDataType(ContentObject.DATA_TYPE_WORKING);
			workingCopyAttributes.setStatus(VersionStatus.findById(VersionStatus.VERSION_WIP));

			contentObjectDataTypeMap.put(ContentObject.DATA_TYPE_WORKING, workingCopyAttributes);
			log.info("originalContentObjectData.clone took " + (System.currentTimeMillis() - startTime) + " ms");

			workingCopyAttributes.save();

			this.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
			this.setLockedFor(UserUtil.getPrincipalUserId());
			this.setWorkflowAction(null);
			this.setReadyForApproval(false);
			this.setState(WorkflowState.findById(WorkflowState.STATE_CONTENT));
			this.save();

			if (this.getZone() != null)
				ContentObjectZonePriority.addWorkingDataAfterActive(this, this.getZone());

			startTime = System.currentTimeMillis();
			// Save data to HistoricalContentObjectData for history tracking purpose
			HistoricalContentObjectData histContentObjectData = HistoricalContentObjectData.create(this, workingCopyAttributes.getGuid());
			histContentObjectData.save();
			log.info("HistoricalContentObjectData.create took " + (System.currentTimeMillis() - startTime) + " ms");

			startTime = System.currentTimeMillis();
            Date now = DateUtil.now();
            for(ContentObjectAssociation coa : getContentObjectAssociations()) {
                if(coa.getDataType() != ContentObject.DATA_TYPE_WORKING) continue;
                coa.setCreated(now); // Cheat history content controller to believe the content is created after version created
                if(coa.getContent() != null) {
                    coa.getContent().setCreated(now);
                }
                boolean needCreateHistory = coa.getNeedCreateHistory();
                coa.setNeedCreateHistory(true);
                coa.postSave(true);
                coa.setNeedCreateHistory(needCreateHistory);
            }
			log.info("ContentObjectAssociation.postSave took " + (System.currentTimeMillis() - startTime) + " ms");

			this.save();
			result = REQUEST_SUCCESSFUL;
		}

		return result;
	}

	public int createWorkingDataFromActive() {
		int result = REQUEST_GOT_AN_ERROR;

		if (contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING) || !contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			// Working copy already exists or active copy doesn't exist
			result = REQUEST_NOT_APPLICABLE;
		}
		else {
			result = createWorkingDataFrom(contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ACTIVE));
		}

		return result;
	}

	public int createWorkingDataFromArchive() {
		int result = REQUEST_GOT_AN_ERROR;

		if (contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING) || !contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			// Working copy already exists or active copy doesn't exist
			result = REQUEST_NOT_APPLICABLE;
		}
		else {
			result = createWorkingDataFrom(contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_ARCHIVED));
		}

		return result;
	}

	public int activateWorkingData() {
		int result = REQUEST_GOT_AN_ERROR;

		if (contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			// Process only top level of COA if the content object is local, structured and the touchpoint is enabled for a variant workflow
			//
			boolean processOnlyTopLevel = (this.getDocument() != null && this.getDocument().isEnabledForVariantWorkflow() && this.isStructuredContentEnabled());

			if (contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
			{
				archiveActiveData(processOnlyTopLevel);
			}

			ContentObjectData atributes = contentObjectDataTypeMap.remove(ContentObject.DATA_TYPE_WORKING);
			if (atributes != null) {
				Session session = HibernateUtil.getManager().getSession();

				StringBuilder updateQuery = new StringBuilder();
				updateQuery.append("UPDATE CONTENT_OBJECT_DATA SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
					updateQuery.append(", STATUS_ID = "); updateQuery.append(VersionStatus.VERSION_PRODUCTION);
					updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
					updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
					updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_TG_INCLUDED_MAP SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
					updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
					updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
					updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_TG_EXCLUDED_MAP SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
					updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
					updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
					updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_TG_EXTENDED_MAP SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
					updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
					updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
					updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_TG_INSTANCE_MAP SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
					updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
					updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
					updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_COMMENT SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
					updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
					updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
					updateQuery.append("; ");

				updateQuery.append("UPDATE PG_TREE_NODE SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
				updateQuery.append("; ");

				if (processOnlyTopLevel) {
					updateQuery.append("UPDATE CONTENT_OBJECT_ASSOCIATION SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
						updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
						updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
						updateQuery.append(" AND TP_PG_TN_ID IS NULL; ");
				} else {
					updateQuery.append("UPDATE CONTENT_OBJECT_ASSOCIATION SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
						updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
						updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
						updateQuery.append("; ");
				}

				NativeQuery sqlQuery = session.createNativeQuery(updateQuery.toString());
				sqlQuery.executeUpdate();

				// We need to disconnect previous content object data, which the composite id was modified above
				session.evict(atributes);
				session.flush();	// Please don't disable this flush; It was disabled in TPM-391 and re-enabled back in TPM-691

				this.contentObjectAssociations.clear();
				session.refresh(this);

                refreshContentObjectDataAndContentObjectAssociations();

				this.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
				this.setLockedFor(null);
				this.setCheckoutTime(null);
				boolean saveNeedRecalcHash = this.isNeedRecalculateHash();
				this.setNeedRecalculateHash(false);
				this.save();
				this.setNeedRecalculateHash(saveNeedRecalcHash);

				// Change data type from working to active in zone priority

				if (this.getZone() != null)
					ContentObjectZonePriority.changeDataTypeOfContentObjectZonePriority(this, ContentObject.DATA_TYPE_WORKING);

				// Activate the historical data
				HistoricalContentObjectData.activate(atributes.getGuid());

				result = REQUEST_SUCCESSFUL;
			}
		}
		else
		{
			// Working copy doesn't exist
			result = REQUEST_NOT_APPLICABLE;
		}

		return result;
	}

	public int discardWorkingData() {
		int result = REQUEST_GOT_AN_ERROR;

		if (contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			ContentObjectData atributes = contentObjectDataTypeMap.remove(ContentObject.DATA_TYPE_WORKING);
			if (atributes != null) {

				// Process only top level of COA if the content object is local, structured and the touchpoint is enabled for a variant workflow
				//
				boolean processOnlyTopLevel = (this.getDocument() != null && this.getDocument().isEnabledForVariantWorkflow() && this.isStructuredContentEnabled());

				// Delete contents
				List<ContentObjectAssociation> coas = ContentObjectAssociation.findAllByContentObjectAndParameters(this, ContentObject.DATA_TYPE_WORKING, null, null, null, false, true, true, true, false);
				for(ContentObjectAssociation coa : coas) {
					if (!processOnlyTopLevel || coa.getTouchpointPGTreeNode() == null) {
						if (coa.getTypeId() == ContentAssociationType.ID_OWNS && coa.getContent() != null && coa.getReferencingImageLibrary() == null) {
							coa.getContent().deleteContentSafe(false);
						}
                        coa.deleteWithPropagationSupports();
						this.contentObjectAssociations.remove(coa);
					}
				}

				// Delete working data from zone priority
				ContentObjectZonePriority.deleteContentObjectZonePriority(this, ContentObject.DATA_TYPE_WORKING, null);

                LanguageContentHash languageContentHash = atributes.getLanguageContentHash();
                if(languageContentHash != null) {
                    languageContentHash.deleteWithPropagationSupports();
                }

				// Remove from history since it's a user initiated
				HistoricalContentObjectData histContentObjectData = HistoricalContentObjectData.findByContentObjectDataGuid(atributes.getGuid());
				if (histContentObjectData != null)
					histContentObjectData.deleteWithPropagationSupports();

				Session session = HibernateUtil.getManager().getSession();

				StringBuilder deleteQuery = new StringBuilder();

				deleteQuery.append("DELETE FROM CONTENT_OBJECT_COMMENT  WHERE CONTENT_OBJECT_ID = ").append(this.getId());
				deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_WORKING).append("; ");

				NativeQuery sqlQuery = session.createNativeQuery(deleteQuery.toString());
				sqlQuery.executeUpdate();

				session.flush();

				this.contentObjectAssociations.clear();
				session.refresh(this);

				// Clear the StringBuilder
				deleteQuery.setLength(0);
				deleteQuery.append("DELETE FROM HIST_CONTENT_OBJ_ASSOCIATION  WHERE CO_PG_TN_ID IN (SELECT ID FROM PG_TREE_NODE WHERE CONTENT_OBJECT_ID = ").append(this.getId());
				deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_WORKING).append("); ");

				deleteQuery.append("DELETE FROM PG_TREE_NODE  WHERE CONTENT_OBJECT_ID = ").append(this.getId());
				deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_WORKING).append("; ");

				sqlQuery = session.createNativeQuery(deleteQuery.toString());
				sqlQuery.executeUpdate();

				TargetGroupInstance.deleteAllNecessaryByContentObjectAndDataType(this, ContentObject.DATA_TYPE_WORKING);

				HistoricalContentObjectAssociation.deleteHistoricalContentObjectAssociationByContentObjectAndDataGuid(this.getId(), atributes.getGuid());

				session.flush();

				result = REQUEST_SUCCESSFUL;
			}
		}
		else
		{
			// Working copy doesn't exist
			result = REQUEST_NOT_APPLICABLE;
		}

		return result;
	}

	public int archiveActiveData(boolean processOnlyTopLevel) {
		int result = REQUEST_GOT_AN_ERROR;

		if (contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ACTIVE))
		{
			Session session = HibernateUtil.getManager().getSession();
			if (contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
			{
				Set<String> existingActivePgtnDNAs = new HashSet<>();
				if (this.isDynamicVariantEnabled()) {
					List<ContentObjectAssociation> existingActiveCOAs = ContentObjectAssociation.findAllDynamicVariants(this, ContentObject.DATA_TYPE_ACTIVE);
					for (ContentObjectAssociation activeCOA : existingActiveCOAs) {
						if (activeCOA.getContentObjectPGTreeNode() != null)
							existingActivePgtnDNAs.add(activeCOA.getContentObjectPGTreeNode().getDna());
					}
				}

				List<Long> existingActiveTPGTreeNodeIds = new ArrayList<>();
				if (this.isStructuredContentEnabled())
				{
					existingActiveTPGTreeNodeIds.addAll(ContentObjectAssociation.findAllExistingStructuredVariantIds(this, ContentObject.DATA_TYPE_ACTIVE));
				}

				List<ContentObjectAssociation> coas = ContentObjectAssociation.findAllByContentObjectAndParameters(this, ContentObject.DATA_TYPE_ARCHIVED, null, null, null, false, true, true, true, false);
				for(ContentObjectAssociation coa : coas) {
					if (coa.getContentObjectPGTreeNode() != null && !existingActivePgtnDNAs.contains(coa.getContentObjectPGTreeNode().getDna())) {
						ParameterGroupTreeNode activePGTN = ParameterGroupTreeNode.findActiveDynamicVariant(coa.getContentObjectPGTreeNode());
						if (activePGTN != null) {
							coa.setContentObjectPGTreeNode(activePGTN);
							if (coa.getReferencingContentObjectPGTreeNode() != null)
								coa.setReferencingContentObjectPGTreeNode(ParameterGroupTreeNode.findActiveDynamicVariant(coa.getReferencingContentObjectPGTreeNode()));
							coa.save();
							continue; // skipped deletion of dynamic variant COA if active COA doesn't exist for this dynamic variant
						}
					}

					if (!processOnlyTopLevel || coa.getTouchpointPGTreeNode() == null) {
						if (this.isStructuredContentEnabled() && coa.getTouchpointPGTreeNode() != null && !existingActiveTPGTreeNodeIds.contains(coa.getTouchpointPGTreeNode().getId())) {
							continue; // skipped deletion of structured variant COA if active COA doesn't exist for this structured variant
						}

						if (coa.getTypeId() == ContentAssociationType.ID_OWNS && coa.getContent() != null && coa.getReferencingImageLibrary() == null) {
							coa.getContent().deleteContentSafe(false);
						}
						this.contentObjectAssociations.remove(coa);
					}
				}

				// Delete the previous version

				ContentObjectData previous = contentObjectDataTypeMap.remove(ContentObject.DATA_TYPE_ARCHIVED);

				StringBuilder deleteQuery = new StringBuilder();

				deleteQuery.append("DELETE FROM CONTENT_OBJECT_COMMENT  WHERE CONTENT_OBJECT_ID = ").append(this.getId());
				deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_ARCHIVED).append("; ");

				NativeQuery sqlQuery = session.createNativeQuery(deleteQuery.toString());
				sqlQuery.executeUpdate();

                session.evict(previous);

				session.flush();

				this.contentObjectAssociations.clear();
				session.refresh(this);

                refreshContentObjectDataAndContentObjectAssociations();

                // Update or delete the historical content object association
                HistoricalContentObjectAssociation.updateOrDeleteHistoriesForArchivedDynamicVariant(this);

				// Clear the StringBuilder
				deleteQuery.setLength(0);
				deleteQuery.append("DELETE FROM PG_TREE_NODE  WHERE CONTENT_OBJECT_ID = ").append(this.getId());
				deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_ARCHIVED).append("; ");

				sqlQuery = session.createNativeQuery(deleteQuery.toString());
				sqlQuery.executeUpdate();

				TargetGroupInstance.deleteAllNecessaryByContentObjectAndDataType(this, ContentObject.DATA_TYPE_ARCHIVED);

				session.flush();
			}

			ContentObjectData atributes = contentObjectDataTypeMap.remove(ContentObject.DATA_TYPE_ACTIVE);
			if (atributes != null) {

				StringBuilder updateQuery = new StringBuilder();
				updateQuery.append("UPDATE CONTENT_OBJECT_DATA SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
				updateQuery.append(", STATUS_ID = "); updateQuery.append(VersionStatus.VERSION_PRODUCTION);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
				updateQuery.append("; ");

                updateQuery.append("DELETE FROM CONTENT_OBJECT_TG_INCLUDED_MAP WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
                updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
                updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_TG_INCLUDED_MAP SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
				updateQuery.append("; ");

                updateQuery.append("DELETE FROM CONTENT_OBJECT_TG_EXCLUDED_MAP WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
                updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
                updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_TG_EXCLUDED_MAP SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
				updateQuery.append("; ");

                updateQuery.append("DELETE FROM CONTENT_OBJECT_TG_EXTENDED_MAP WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
                updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
                updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_TG_EXTENDED_MAP SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
				updateQuery.append("; ");

                updateQuery.append("DELETE FROM CONTENT_OBJECT_TG_INSTANCE_MAP WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
                updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
                updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_TG_INSTANCE_MAP SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
				updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_COMMENT SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
				updateQuery.append("; ");

				updateQuery.append("UPDATE PG_TREE_NODE SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
				updateQuery.append("; ");

				if (processOnlyTopLevel) {
					updateQuery.append("UPDATE CONTENT_OBJECT_ASSOCIATION SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
					updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
					updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
					updateQuery.append(" AND TP_PG_TN_ID IS NULL; ");
				} else {
					updateQuery.append("UPDATE CONTENT_OBJECT_ASSOCIATION SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ARCHIVED);
					updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.getId());
					updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_ACTIVE);
					updateQuery.append("; ");
				}

				NativeQuery sqlQuery = session.createNativeQuery(updateQuery.toString());
				sqlQuery.executeUpdate();

				// We need to disconnect previous content object data, which the composite id was modified above
				session.evict(atributes);

				session.flush();	// Please don't disable this flush; It was disabled in TPM-391 and re-enabled back in TPM-691

				this.contentObjectAssociations.clear();
				session.refresh(this);

                refreshContentObjectDataAndContentObjectAssociations();

				this.setFocusOnDataType(ContentObject.DATA_TYPE_ARCHIVED);
				this.save();

				// Delete active data type from zone priority since it was just archived

				ContentObjectZonePriority.deleteContentObjectZonePriority(this, ContentObject.DATA_TYPE_ACTIVE, null);

				// Archive the historical data
				HistoricalContentObjectData.archive(atributes.getGuid());

				result = REQUEST_SUCCESSFUL;
			}
		}
		else
		{
			// Active copy doesn't exist
			result = REQUEST_NOT_APPLICABLE;
		}

		return result;
	}

	public int deleteArchiveData() {
		int result = REQUEST_GOT_AN_ERROR;

		if (contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_ARCHIVED))
		{
			boolean isDynamicVariantEnabled = this.isDynamicVariantEnabled(ContentObject.DATA_TYPE_ARCHIVED);
			ContentObjectData attributes = contentObjectDataTypeMap.remove(ContentObject.DATA_TYPE_ARCHIVED);

			if (attributes != null) {

				Set<String> existingActivePgtnDNAs = new HashSet<>();
				if (isDynamicVariantEnabled) {
					List<ContentObjectAssociation> existingActiveCOAs = ContentObjectAssociation.findAllDynamicVariants(this, ContentObject.DATA_TYPE_ACTIVE);
					for (ContentObjectAssociation activeCOA : existingActiveCOAs) {
						if (activeCOA.getContentObjectPGTreeNode() != null)
							existingActivePgtnDNAs.add(activeCOA.getContentObjectPGTreeNode().getDna());
					}
				}

				List<Long> existingActiveTPGTreeNodeIds = new ArrayList<>();
				if (this.isStructuredContentEnabled())
				{
					existingActiveTPGTreeNodeIds.addAll(ContentObjectAssociation.findAllExistingStructuredVariantIds(this, ContentObject.DATA_TYPE_ACTIVE));
				}

				List<ContentObjectAssociation> coas = ContentObjectAssociation.findAllByContentObjectAndParameters(this, ContentObject.DATA_TYPE_ARCHIVED, null, null, null, false, true, true, true, false);
				for(ContentObjectAssociation coa : coas) {
					if (coa.getContentObjectPGTreeNode() != null && !existingActivePgtnDNAs.contains(coa.getContentObjectPGTreeNode().getDna())) {
						ParameterGroupTreeNode activePGTN = ParameterGroupTreeNode.findActiveDynamicVariant(coa.getContentObjectPGTreeNode());
						if (activePGTN != null) {
							coa.setContentObjectPGTreeNode(activePGTN);
							if (coa.getReferencingContentObjectPGTreeNode() != null)
								coa.setReferencingContentObjectPGTreeNode(ParameterGroupTreeNode.findActiveDynamicVariant(coa.getReferencingContentObjectPGTreeNode()));
							coa.setDataType(ContentObject.DATA_TYPE_ACTIVE);
							coa.save();
							continue; // skipped deletion of dynamic variant COA if active COA doesn't exist for this dynamic variant
						}
					}

					if (this.isStructuredContentEnabled() && coa.getTouchpointPGTreeNode() != null && !existingActiveTPGTreeNodeIds.contains(coa.getTouchpointPGTreeNode().getId())) {
						coa.setDataType(ContentObject.DATA_TYPE_ACTIVE);
						coa.save();
						continue; // skipped deletion of structured variant COA if active COA doesn't exist for this structured variant
					}

					if (coa.getTypeId() == ContentAssociationType.ID_OWNS) {
						if (coa.getContent() != null && coa.getReferencingImageLibrary() == null) {
							coa.getContent().deleteContentSafe(false);
						}
					}
                    coa.deleteWithPropagationSupports();
					this.contentObjectAssociations.remove(coa);
				}

                LanguageContentHash languageContentHash = attributes.getLanguageContentHash();
                if(languageContentHash != null) {
                    languageContentHash.delete();
                }

				// Remove from history since it's a user initiated
				HistoricalContentObjectData histContentObjectData = HistoricalContentObjectData.findByContentObjectDataGuid(attributes.getGuid());
				if (histContentObjectData != null)
					histContentObjectData.delete();

				Session session = HibernateUtil.getManager().getSession();

				StringBuilder deleteQuery = new StringBuilder();

				deleteQuery.append("DELETE FROM CONTENT_OBJECT_COMMENT  WHERE CONTENT_OBJECT_ID = ").append(this.getId());
				deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_ARCHIVED).append("; ");

				NativeQuery sqlQuery = session.createNativeQuery(deleteQuery.toString());
				sqlQuery.executeUpdate();

                session.flush();

				this.contentObjectAssociations.clear();
				session.refresh(this);

                // Update or delete the historical content object association
                HistoricalContentObjectAssociation.updateOrDeleteHistoriesForArchivedDynamicVariant(this);

				// Clear the StringBuilder
				deleteQuery.setLength(0);
				deleteQuery.append("DELETE FROM PG_TREE_NODE  WHERE CONTENT_OBJECT_ID = ").append(this.getId());
				deleteQuery.append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_ARCHIVED).append("; ");

				sqlQuery = session.createNativeQuery(deleteQuery.toString());
				sqlQuery.executeUpdate();

				TargetGroupInstance.deleteAllNecessaryByContentObjectAndDataType(this, ContentObject.DATA_TYPE_ARCHIVED);

				HistoricalContentObjectAssociation.deleteHistoricalContentObjectAssociationByContentObjectAndDataGuid(this.getId(), attributes.getGuid());


				session.flush();

				result = REQUEST_SUCCESSFUL;
			}
		}
		else
		{
			// Working copy doesn't exist
			result = REQUEST_NOT_APPLICABLE;
		}

		return result;
	}

	public int deleteAllData() {
		int result = REQUEST_GOT_AN_ERROR;

		if (!contentObjectDataTypeMap.isEmpty())
		{
			// Delete all contents
			List<ContentObjectAssociation> coas = ContentObjectAssociation.findAllByContentObjectAndParameters(this, ContentObject.DATA_TYPE_ALL, null, null, null, false, true, true, true, false);
			for(ContentObjectAssociation coa : coas) {
				if (coa.getTypeId() == ContentAssociationType.ID_OWNS) {
					if (coa.getContent() != null && coa.getReferencingImageLibrary() == null) {
						coa.getContent().deleteContentSafe(false);
					}
				}
				this.contentObjectAssociations.remove(coa);
			}

			// Delete all data type from zone priority
			ContentObjectZonePriority.deleteContentObjectZonePriority(this, ContentObject.DATA_TYPE_ALL, null);

			// Remove from history
			List<HistoricalContentObjectData> histContentObjectDatas = HistoricalContentObjectData.findAllByContentObjectId(this.getId());
			for (HistoricalContentObjectData histContentObjectData : histContentObjectDatas) {
				histContentObjectData.deleteWithPropagationSupports();
			}

			Session session = HibernateUtil.getManager().getSession();

			log.info("Deleting content object data...");
			for (ContentObjectData cod : contentObjectDataTypeMap.values()) {
				session.evict(cod);
                LanguageContentHash languageContentHash = cod.getLanguageContentHash();
                if(languageContentHash != null) {
                    languageContentHash.deleteWithPropagationSupports();
                }
				cod.deleteWithPropagationSupports();
			}

			this.contentObjectDataTypeMap.clear();

			StringBuilder deleteQuery = new StringBuilder();

			deleteQuery.append("DELETE FROM CONTENT_OBJECT_COMMENT WHERE  CONTENT_OBJECT_ID = ").append(this.getId()).append("; ");

			NativeQuery sqlQuery = session.createNativeQuery(deleteQuery.toString());
			sqlQuery.executeUpdate();

			session.flush();

			this.contentObjectAssociations.clear();
			session.refresh(this);

			refreshContentObjectDataAndContentObjectAssociations();

			// Clear the StringBuilder
			deleteQuery.setLength(0);
			deleteQuery.append("DELETE FROM HIST_CONTENT_OBJ_ASSOCIATION  WHERE CO_PG_TN_ID IN (SELECT ID FROM PG_TREE_NODE WHERE CONTENT_OBJECT_ID = ").append(this.getId()).append("); ");

			deleteQuery.append("DELETE FROM PG_TREE_NODE  WHERE CONTENT_OBJECT_ID = ").append(this.getId()).append("; ");

			sqlQuery = session.createNativeQuery(deleteQuery.toString());
			sqlQuery.executeUpdate();

			TargetGroupInstance.deleteAllNecessaryByContentObjectAndDataType(this, ContentObject.DATA_TYPE_ALL);

			HistoricalContentObjectAssociation.deleteHistoricalContentObjectAssociationByContentObject(this.getId());

			this.setRemoved(true);

			this.save();

			session.flush();

			result = REQUEST_SUCCESSFUL;
		}
		else
		{
			result = REQUEST_NOT_APPLICABLE;
		}

		return result;
	}

    private void refreshContentObjectDataAndContentObjectAssociations() {
//  reverted FB 26062 case since it causes huge performance issue (FB 28289 case and more) when a content object has a target group
//  the problem seems to be specificaly with refresh Target Groups
//
//        for(ContentObjectData contentObjectData : this.getContentObjectDataTypeMap().values()) {
//            if(contentObjectData != null) {
//                HibernateUtil.getManager().getSession().refresh(contentObjectData);
//                contentObjectData.getComments().size();

//                refreshTargetGroups(contentObjectData.getIncludedTargetGroups());
//                refreshTargetGroups(contentObjectData.getExcludedTargetGroups());
//                refreshTargetGroups(contentObjectData.getExtendedTargetGroups());
//            }
//        }

		Session session = HibernateUtil.getManager().getSession();
        for(ContentObjectAssociation coa : this.contentObjectAssociations) {
            if(coa != null) {
				session.refresh(coa);
            }
        }
    }

    private void refreshTargetGroups(Collection<TargetGroup> targetGroups) {
	    for(TargetGroup targetGroup : targetGroups) {
	        if(targetGroup != null) {
                HibernateUtil.getManager().getSession().refresh(targetGroup);
                if (targetGroup.isParameterized()) {
                    for (Map.Entry<ContentObjectDataMapKey, TargetGroupInstance> entry : targetGroup.getParameterizedMap().entrySet()) {
                        if (entry.getKey().getContentObjectId() == this.getId()) {
                            TargetGroupInstance targetGroupInstance = entry.getValue();
                            if(targetGroupInstance != null) {
                                HibernateUtil.getManager().getSession().refresh(targetGroupInstance);
                            }
                        }
                    }
                }
            }
        }
    }

	// From Content Object Data
	//

	// TEXT STYLES
	public List<TextStyleVO> getStyles() {
		return getStyles(false);
	}
	public List<TextStyleVO> getStyles(boolean mergeAlternates) {

		List<TextStyleVO> stylesList = new ArrayList<>();

		if ( this.isTouchpointLocal() && !isDeliveredToPlaceholder() ) {

			Document document = this.getFirstDocumentDelivery();
			document = UserUtil.getCurrentChannelDocumentContext(document);
			stylesList = TextStyle.findAllVOs( document );

		} else if(this.isGlobalSmartText()) {
			if (getDocuments().isEmpty())
				return new ArrayList<>();

			if (!this.isVariableContentEnabled())
				return new ArrayList<>();

			stylesList = TextStyle.findAllVOs(null);
		} else {
			if (this.getZone() == null)
				return new ArrayList<>();

			HashSet<TextStyle> styles = new HashSet<>();
			boolean aZoneHasStyle = false;
			boolean aZoneHasNoStyle = false;
			Zone zone = Zone.findZoneInContextByParent(this.getZone());

			Set<TextStyle> zoneStyleSet = mergeAlternates ? zone.getStylesMergedForAlternates() : zone.getStyles();

			if (zone.getStarterTextStyle() != null) {
				zoneStyleSet.add(zone.getStarterTextStyle());
			}

			if ((zoneStyleSet != null) && (!zoneStyleSet.isEmpty())) {
				// If we haven't encountered a zone without styles
				// and this is the first zone with styles
				if (!aZoneHasStyle && !aZoneHasNoStyle) {
					// First zone with styles.
					styles.addAll(zoneStyleSet);
				} else {
					// Retain only the styles defined by all zones in the set.
					styles.retainAll(zone.getStylesMergedForAlternates());
				}
				aZoneHasStyle = true;
			} else {
				aZoneHasNoStyle = true;
				styles.clear();
			}

			Document document = this.getFirstDocumentDelivery();
			document = UserUtil.getCurrentChannelDocumentContext(document);
			for ( TextStyle textStyle : styles )
				stylesList.add(new TextStyleVO(textStyle, document));

		}

		Collections.sort(stylesList, new TextStyleVOComparator());
		return stylesList;
	}

	public JSONArray getTextStyleData() {
		return ContentStyleUtils.getTextStyleData(getStyles());
	}

	public String getCSSFilename() {
		return ContentStyleUtils.getTextCSSFilePath(this);
	}

	// PARAGRAPH STYLES
	public List<ParagraphStyleVO> getParagraphStyles() {

		List<ParagraphStyleVO> paragraphStylesList = new ArrayList<>();

		if ( this.isTouchpointLocal() && !isDeliveredToPlaceholder() ) {

			paragraphStylesList = ParagraphStyle.findAllVOs( this.getFirstDocumentDelivery() );

		} else if(this.isGlobalSmartText()){
			if (this.getDocuments().isEmpty())
				return new ArrayList<>();

			if (!this.isVariableContentEnabled())
				return new ArrayList<>();

			paragraphStylesList = ParagraphStyle.findAllVOs(null);
		} else {

			if (this.getZone() == null)
				return new ArrayList<>();

			HashSet<ParagraphStyle> paragraphStyles = new HashSet<>();
			boolean aZoneHasParagraphStyle = false;
			boolean aZoneHasNoParagraphStyle = false;
			Zone zone = Zone.findZoneInContextByParent(this.getZone());

			Set<ParagraphStyle> zoneParagraphStyleSet = zone.getParagraphStyles();
			if ((zoneParagraphStyleSet != null) && (!zoneParagraphStyleSet.isEmpty())) {
				// If we haven't encountered a zone without styles
				// and this is the first zone with styles
				if (!aZoneHasParagraphStyle && !aZoneHasNoParagraphStyle) {
					// First zone with styles.
					paragraphStyles.addAll(zoneParagraphStyleSet);
				} else {
					// Retain only the styles defined by all zones in the set.
					paragraphStyles.retainAll(zone.getParagraphStyles());
				}
				aZoneHasParagraphStyle = true;
			} else {
				aZoneHasNoParagraphStyle = true;
				paragraphStyles.clear();
			}

			for ( ParagraphStyle paraStyle : paragraphStyles ) {
				paragraphStylesList.add(new ParagraphStyleVO(paraStyle, this.getFirstDocumentDelivery()));
			}

		}

		Collections.sort(paragraphStylesList, new ParagraphStyleVOComparator());

		return paragraphStylesList;
	}

	public JSONArray getParagraphStyleData() {
		return ContentStyleUtils.getParagraphStyleData( getParagraphStyles() );
	}

	public String getParagraphCSSFilename() {
		return ContentStyleUtils.getParagraphCSSFilePath(this);
	}

	// LIST STYLES
	public List<ListStyleVO> getListStyles() {

		List<ListStyleVO> listStylesList = new ArrayList<>();

		if ( this.isTouchpointLocal() && !isDeliveredToPlaceholder() ) {

			listStylesList = ListStyle.findAllVOs( this.getFirstDocumentDelivery() );

		} else if(this.isGlobalSmartText()){
			if (getDocuments().isEmpty())
				return new ArrayList<>();

			if (!this.isVariableContentEnabled())
				return new ArrayList<>();

			listStylesList = ListStyle.findAllVOs(null);
		} else {

			if (this.getZone() == null)
				return new ArrayList<>();

			HashSet<ListStyle> listStyles = new HashSet<>();
			boolean aZoneHasListStyle = false;
			boolean aZoneHasNoListStyle = false;
			Zone zone = Zone.findZoneInContextByParent(this.getZone());

			Set<ListStyle> zoneListStyleSet = zone.getListStyles();
			if ((zoneListStyleSet != null) && (!zoneListStyleSet.isEmpty())) {
				// If we haven't encountered a zone without styles
				// and this is the first zone with styles
				if (!aZoneHasListStyle && !aZoneHasNoListStyle) {
					// First zone with styles.
					listStyles.addAll(zoneListStyleSet);
				} else {
					// Retain only the styles defined by all zones in the set.
					listStyles.retainAll(zone.getListStyles());
				}
				aZoneHasListStyle = true;
			} else {
				aZoneHasNoListStyle = true;
				listStyles.clear();
			}

			for ( ListStyle listStyle : listStyles ) {
				listStylesList.add(new ListStyleVO(listStyle, this.getFirstDocumentDelivery()));
			}

		}

		Collections.sort(listStylesList, new ListStyleVOComparator());

		return listStylesList;
	}

	public JSONArray getListStyleData() {
		return ContentStyleUtils.getListStyleData( getListStyles() );
	}

	public String getListCSSFilename() {
		return ContentStyleUtils.getListCSSFilePath(this);
	}

	public String getDefaultEditorCSSFilePath() {
		return ContentStyleUtils.getDefaultCSSFilePath(this, ContentStyleUtils.CONTENT_TYPE_EDITOR);
	}
	public String getDefaultViewCSSFilePath() {
		return ContentStyleUtils.getDefaultCSSFilePath(this, ContentStyleUtils.CONTENT_TYPE_VIEW);
	}

	public JSONArray getSpellcheckLanguages() {
		JSONArray langArray = new JSONArray();

		List<MessagepointLocale> locales = this.getContentObjectLanguagesAsLocales();

		for ( MessagepointLocale locale: locales ) {
			JSONObject langObj = new JSONObject();
			langObj.put("code", locale.getLanguageCode() );
			langObj.put("locale_id", locale.getId());
			langObj.put("locale", locale.getCode().split("_")[locale.getCode().split("_").length - 1].toUpperCase() );
			langObj.put("default", getDocument() != null ?
					getDocument().getDefaultTouchpointLanguageLocale().getId() == locale.getId() :
					MessagepointLocale.getDefaultSystemLanguageLocale().getId() == locale.getId() );
			langArray.put( langObj );
		}

		return langArray;
	}

	public Boolean getIsExstreamHtml() {
		if ( this.getFirstDocumentDelivery().isDialogueTouchpoint() ) {
			if ( this.isMessage() && this.getZone().isHtmlOutput() )
				return true;
			else if (this.getFirstDocumentDelivery().isEnabledForHtmlOutput() )
				return true;
			else if ( !this.isMessage() && this.getFirstDocumentDelivery().hasZoneEnabledForHtmlOutput() )
				return true;
		}
		return false;
	}

	public Boolean getIsExstreamDxf() {
		if ( this.getFirstDocumentDelivery().isDialogueTouchpoint() ) {
			if ( this.isMessage() && this.getZone().isDxfOutput() )
				return true;
			else if (this.getFirstDocumentDelivery().isEnabledForDXFOutput() )
				return true;
			else if ( !this.isMessage() && this.getFirstDocumentDelivery().hasZoneEnabledForDxfOutput() )
				return true;
		}
		return false;
	}

	public Boolean getIsExstreamRunTimeDxf() {
		if (getIsExstreamDxf())
		{
			if (this.getFirstDocumentDelivery().getConnectorConfiguration() instanceof DialogueConfiguration)
				return ((DialogueConfiguration)this.getFirstDocumentDelivery().getConnectorConfiguration()).isRunTimeDxf();
			return true;
		}
		return false;
	}

	public TouchpointSelectionWrapper getTreeNodeSourceWrapper() {
		if ( !isStructuredContentEnabled() || getFirstDocumentDelivery() == null )
			return null;
		return new TouchpointSelectionWrapper(getFirstDocumentDelivery().getMasterTouchpointSelection(), this.getOwningTouchpointSelection(), this.getId(), this.isFocusOnActiveData());
	}

	public Boolean getIsOmniChannel() {
		return getFirstDocumentDelivery() != null ? getFirstDocumentDelivery().getIsOmniChannel() : false;
	}


	//

	/**
	 * Retrieve the all pgtn mapping from origin to the version for the dynamic or structured message
	 * NOTE: this method only function if the messages are dynamic or structured and they are parent and child relationship
     */
	public Map<ParameterGroupTreeNode, Map<ParameterGroupTreeNode, Object>> findPGTNOriginVersionMapping(ContentObject mapToMsg, String compareToSchema, int dataType){
		Map<ParameterGroupTreeNode, Map<ParameterGroupTreeNode, Object>> originVersionMapping = new HashMap<>();
		List<ParameterGroupTreeNode> mapFromPgtns = new ArrayList<>();
		List<ParameterGroupTreeNode> mapToPgtns = new ArrayList<>();

		ContentObject mapFromMi = this;
		ContentObject mapToMi = CloneHelper.queryInSchema(compareToSchema, ()->mapToMsg);
		Long mapFromMasterPgtnId = null, mapToMasterPgtnId = null;
		if(mapFromMi.isDynamicVariantEnabled()){
			mapFromPgtns.addAll(ParameterGroupTreeNode.findAllNodesForDynamicAsset(this, dataType));
			CloneHelper.execInSchema(compareToSchema, ()->{
				mapToPgtns.addAll(ParameterGroupTreeNode.findAllNodesForDynamicAsset(mapToMsg, dataType));
			});
		}else if(mapFromMi.isStructuredContentEnabled()){
			boolean isVariantMessage = mapFromMi.isVariantType();
			TouchpointSelection mapFromTs = isVariantMessage?mapFromMi.getOwningTouchpointSelection():mapFromMi.getDocument().getMasterTouchpointSelection();
			mapFromPgtns.addAll(mapFromTs.getParameterGroupTreeNode().getAllDescendentNodes());
			mapFromMasterPgtnId = mapFromTs.getParameterGroupTreeNode().getId();

			mapToMasterPgtnId =  CloneHelper.queryInSchema(compareToSchema, ()->{
				TouchpointSelection mapToTs = isVariantMessage?mapToMi.getOwningTouchpointSelection():mapToMi.getDocument().getMasterTouchpointSelection();
				mapToPgtns.addAll(mapToTs.getParameterGroupTreeNode().getAllDescendentNodes());
				return mapToTs.getParameterGroupTreeNode().getId();
			});
		}

		originVersionMapping = ParameterGroupTreeNode.findPGTNByDnaVersionMapping(mapFromPgtns, mapToPgtns, mapFromMasterPgtnId, mapToMasterPgtnId, compareToSchema);

		return originVersionMapping;
	}


	/**
	 * Find all ids of most recent message instances for each status (working copy, active), except those status or those models' status are REMOVED or ARCHIVED
     */
	public static List<Long> findUniqueVisibleIdsOfMostRecentCopies(List<Long> zoneIds, int objectType, long documentId, String sSearch, User requestor){
		return findUniqueVisibleIdsOfMostRecentCopies(zoneIds, objectType, List.of(documentId), true, sSearch, requestor);
	}
	public static List<Long> findUniqueVisibleIdsOfMostRecentCopies(List<Long> zoneIds, int objectType, long documentId, String sSearch){
		return findUniqueVisibleIdsOfMostRecentCopies(zoneIds, objectType, List.of(documentId), true, sSearch, null);
	}
	public static List<Long> findUniqueVisibleIdsOfMostRecentCopies(List<Long> zoneIds, int objectType, List<Long> documentIds, String sSearch){
		return findUniqueVisibleIdsOfMostRecentCopies(zoneIds, objectType, documentIds, true, sSearch, null);
	}

	@SuppressWarnings("rawtypes")
	public static List<Long> findUniqueVisibleIdsOfMostRecentCopies(List<Long> zoneIds, int objectType, List<Long> documentIds, boolean applyAccessFiltering, String sSearch, User requestor){
		List<Long> contentObjectDataIds = new ArrayList<>();

		boolean applyZoneFiltering = !(objectType > 0) || (zoneIds != null && !zoneIds.isEmpty());

		// Search embedded content name/tags
		StringBuilder searchQuery = new StringBuilder();
		if ( sSearch != null && !sSearch.isEmpty() && !sSearch.equals("NULL") ) {
			Map<String, List<String>> searchLists = TagCloud.tokenizeSearchString(sSearch);
			for (int i=0; i < searchLists.get("tags").size(); i++)
				searchQuery.append("AND LOWER(cod.metatags) LIKE '%").append(searchLists.get("tags").get(i).toLowerCase()).append("%' ");
			for (int i=0; i < searchLists.get("names").size(); i++)
				searchQuery.append("AND LOWER(co.name) LIKE '%").append(searchLists.get("names").get(i).toLowerCase()).append("%' ");
		}

		String query = 	"SELECT DISTINCT (co.id) " +
						"FROM 	content_object co " +
						"INNER JOIN content_object_data cod ON cod.content_object_id = co.id ";

		if ( applyZoneFiltering) {
			query +=	"INNER JOIN zone z ON co.zone_id = z.id ";
			if ( applyAccessFiltering )
				query +="INNER JOIN workgroup_zone wz ON wz.zone_id = z.id " +
						"INNER JOIN workgroup w ON w.id = wz.workgroup_id ";
		} else if (!documentIds.isEmpty() && objectType > 3 ) {
			query +=	"INNER JOIN content_object_document_map codm ON codm.content_object_id = co.id ";
		}


		query += "WHERE 1=1 ";
		if ( applyZoneFiltering ) {
			if ( applyAccessFiltering ) {
				query += "AND 		w.id = " + (requestor!=null?requestor.getWorkgroupId():UserUtil.getPrincipalUser().getWorkgroupId()) + " " +
						"AND 		z.enabled = true ";
			}
			if ( zoneIds != null && !zoneIds.isEmpty() ) {
				query += "AND 		co.zone_id in (:zoneIds) ";
			}
		}

		// TODO: Legacy APIs are calling with localContext:  0,1; Should declare explicit object type(s)
		query +=	"AND 		co.object_type = " + objectType + " ";
		if (objectType > 0 && objectType <= 2) {
			query +=	"AND 		co.document_id IN (" + StringUtils.join(documentIds, ",") + ") ";
		}else if ( objectType > 2 ) {
			query +=	"AND 		co.object_type = " + objectType + " ";
			if (!documentIds.isEmpty())
				query +="AND 		codm.document_id IN (" + StringUtils.join(documentIds, ",") + ") ";
		}

		query += 		searchQuery.toString();
		query +=		"GROUP BY  	co.id";

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);

		if ( zoneIds != null && !zoneIds.isEmpty() )
			sqlQuery.setParameterList("zoneIds", zoneIds);

		List ids = sqlQuery.list();
		for(Object idObj : ids){
			contentObjectDataIds.add(((BigInteger)idObj).longValue());
		}

		return contentObjectDataIds;
	}


	public List<Long> getAppliedChannels() {
		List<Long> appliedChannels = new ArrayList<>();
		if (this.isGlobalContentObject())
		{
			for (Document currentDocument: this.visibleDocuments ) {
				ConnectorConfiguration connectorConfiguration = currentDocument.getConnectorConfiguration();
				if (connectorConfiguration != null && connectorConfiguration.getChannel() != null) {
					if (!appliedChannels.contains(connectorConfiguration.getChannel().getId())) {
						appliedChannels.add(connectorConfiguration.getChannel().getId());
					}
				}
			}

			if (appliedChannels.isEmpty())
				appliedChannels.add(0L);
		}
		else if ( !this.getIsOmniChannel() && this.getZone() != null ) {
			appliedChannels.add(this.getZone().getChannel());
		} else {
			if ( this.getChannelContextId() != null && this.getChannelContextId() > 0 )
				appliedChannels.add(this.getChannelContextId());
			else {
				appliedChannels.add(this.getFirstDocumentDelivery().getConnectorConfiguration().getChannel().getId());
				for (Document currentDocument: this.getFirstDocumentDelivery().getChannelAlternateDocuments() )
					if ( !appliedChannels.contains(currentDocument.getConnectorConfiguration().getChannel().getId()))
						appliedChannels.add(currentDocument.getConnectorConfiguration().getChannel().getId());
			}
		}
		return appliedChannels;
	}
	public List<Long> getAppliedConnectors() {
		List<Long> appliedConnectors = new ArrayList<>();
		if (this.isGlobalContentObject())
		{
			for (Document currentDocument: this.visibleDocuments ) {
				ConnectorConfiguration connectorConfiguration = currentDocument.getConnectorConfiguration();
				if (connectorConfiguration != null && connectorConfiguration.getConnector() != null) {
					if ( !appliedConnectors.contains(connectorConfiguration.getConnector().getId())) {
						appliedConnectors.add(connectorConfiguration.getConnector().getId());
					}
				}
			}

			if (appliedConnectors.isEmpty())
				appliedConnectors.add(0L);
		}
		else if ( !this.getIsOmniChannel() && this.getZone() != null ) {
			appliedConnectors.add(this.getZone().getConnector());
		} else {
			List<Document> omniDocuments = new ArrayList<>();
			omniDocuments.add(this.getFirstDocumentDelivery());
			omniDocuments.addAll(this.getFirstDocumentDelivery().getChannelAlternateDocuments());

			if ( this.getChannelContextId() != null && this.getChannelContextId() > 0 ){
				for (Document currentDocument: omniDocuments )
					if ( currentDocument.getConnectorConfiguration().getChannel().getId() == this.getChannelContextId() )
						appliedConnectors.add(currentDocument.getConnectorConfiguration().getConnector().getId());
			} else {
				for (Document currentDocument: omniDocuments )
					if ( !appliedConnectors.contains(currentDocument.getConnectorConfiguration().getConnector().getId()))
						appliedConnectors.add(currentDocument.getConnectorConfiguration().getConnector().getId());
			}
		}
		return appliedConnectors;
	}

	public Boolean supportsSuperSubScript() {
		boolean supportsSuperSubScript = false;

		Document touchpointContext 	= this.getFirstDocumentDelivery();

		if (touchpointContext == null)
			return supportsSuperSubScript;

		boolean isZoneDXFEnabled 	= this.getZone() != null && this.getZone().isDxfOutput();
		boolean isZoneHTMLEnabled 	= this.getZone() != null && this.getZone().isHtmlOutput();
		if ( touchpointContext.isNativeCompositionTouchpoint() || touchpointContext.isSefasCompositionTouchpoint() || touchpointContext.isMPHCSCompositionTouchpoint() || touchpointContext.isEmailTouchpoint() || touchpointContext.isWebTouchpoint() )
			supportsSuperSubScript = true;
		else if ( touchpointContext.isEnabledForDXFOutput() || touchpointContext.isEnabledForHtmlOutput() || isZoneDXFEnabled || isZoneHTMLEnabled )
			supportsSuperSubScript = true;
		else if ( touchpointContext.isGMCTouchpoint() )
			supportsSuperSubScript = true;

		return supportsSuperSubScript;
	}

	public long getGraphicSubTypeId() {
		if ( this.getIsGraphic() ) {
			if ( !this.isMessage() && !isDeliveredToPlaceholder() ) {
				return (this.getGraphicTypeId() != null ? this.getGraphicTypeId() : 0);
			} else {
				if (this.getZone() != null) {
					return this.getZone().getSubContentTypeId();
				}
			}
		}
		return 0;
	}

	public boolean isCompoundContentObject() {

		if ( this.getParameterGroup() == null || (this.getParameterGroup() != null && this.getParameterGroup().getParameterGroupItems().size() != 1) )
			return false;

		DataElementVariable variable = null;
		if ( this.getParameterGroup().getFirstParameterGroupItem() != null &&
				this.getParameterGroup().getFirstParameterGroupItem().getParameter().getDataElementVariable() != null )
		{
			variable = this.getParameterGroup().getFirstParameterGroupItem().getParameter().getDataElementVariable();
		}

		if ( variable == null )
			return false;
		AbstractDataElement de = variable.getDefaultDataElement();
		if ( de == null )
			return false;
		if ( variable.getDefaultDataElement().getDataTypeId() != DataType.DATA_TYPE_STRING )
			return false;
		if ( !de.isRepeats() )
			return false;

		int lvl1 = de.getDataGroup().getLevel();
		VariableDataElementMap vdem = variable.getDataElementMap().get( de.getDataSource().getId() );
		if ( vdem == null )
			return false;
		DataRecordLevel lvl2 = vdem.getLevel();
		if ( lvl2 == null )
			return false;

		if ( vdem.getAggOperator() != null && vdem.getAggOperator().getId() != AggregationOperator.AGG_OPERATOR_NA )
			return false;

		if ( lvl1 != lvl2.getValue() )
			return true;

		return false;
	}

	public List<User> getAssignableUsers() {
		List<User> finalUsers = new ArrayList<>();

		if (this.isGlobalContentObject())
		{
			List<User> listUsers = new ArrayList<>();

			if (this.isGlobalSmartText())
				listUsers = User.findEnabledUsersByPermission(Permission.ROLE_EMBEDDED_CONTENT_EDIT);
			else
				listUsers = User.findEnabledUsersByPermission(Permission.ROLE_CONTENT_LIBRARY_EDIT);

			for(Document doc : this.isVariableContentEnabled()? this.getDocuments() : Document.findAllDocumentsAndProjectsVisible()){
				for(User user: listUsers){
					if(doc.isVisible(user) && !finalUsers.contains(user))
						finalUsers.add(user);
				}
			}
			// Put back Super User if this message is assigned to Super User
			User superUser = User.findById(1L);
			if(this.getAssignedToUserName().equals(superUser.getName()) && !finalUsers.contains(superUser))
				finalUsers.add(superUser);
		}
		else {

			WorkflowState wfState = getState();
			if (wfState == null)
				return finalUsers;

			WorkflowPosition thisPosition = Workflow.getWorkflow(this.getClass()).getWorkflowPosition(wfState.getId());

			if (thisPosition == null)
				return finalUsers;

			Set<User> users = UserUtil.getActiveUsers(getStateUsers(thisPosition));
			if (wfState.getId() >= WorkflowState.STATE_CONTENT && !isReadyForApproval()) {
				WorkflowPosition authoringPosition = thisPosition.getPreviousValid();
				users = UserUtil.getActiveUsers(getStateUsers(authoringPosition));
			}

			finalUsers.addAll(users);
		}

		Collections.sort(finalUsers,new UserNameComparator());
		return finalUsers;
	}

	// get a list of users with content update permission
	public List<User> getRejectToUsers(long permission) {
		List<User> listUsers = new ArrayList<>();
		listUsers = User.findEnabledUsersByPermission(permission);
		List<User> finalUsers = new ArrayList<>();
		for(Document doc : this.isVariableContentEnabled() ? this.getDocuments() : Document.findAllDocumentsAndProjectsVisible()){
			for(User user: listUsers){
				if(doc.isVisible(user) && !finalUsers.contains(user))
					finalUsers.add(user);
			}
		}

		Collections.sort(finalUsers,new UserNameComparator());
		return finalUsers;
	}

	public List<User> getRejectToUsers() {
		List<User> finalUsers = new ArrayList<>();

		if (this.isGlobalContentObject()) {
			List<User> listUsers = new ArrayList<>();

			if (this.isGlobalSmartText())
				listUsers = User.findEnabledUsersByPermission(Permission.ROLE_EMBEDDED_CONTENT_EDIT);
			else
				listUsers = User.findEnabledUsersByPermission(Permission.ROLE_CONTENT_LIBRARY_EDIT);

			for(Document doc : this.isVariableContentEnabled()? this.getDocuments() : Document.findAllDocumentsAndProjectsVisible()){
				for (User user : listUsers) {
					if (doc.isVisible(user) && !finalUsers.contains(user))
						finalUsers.add(user);
				}
			}

			Collections.sort(finalUsers, new UserNameComparator());
		}

		return finalUsers;
	}

	public Set<User> getStateUsers(WorkflowPosition position) {
		int dataType = ContentObject.DATA_TYPE_WORKING;
		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getStateUsers(position);
		}

		return new HashSet<>();
	}

	public WorkflowPosition getWorkflowPosition() {
		int dataType = ContentObject.DATA_TYPE_WORKING;
		if ((dataType & ContentObject.DATA_TYPE_WORKING) > 0 && contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			return contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getWorkflowPosition();
		}

		return null;
	}

	/**
	 * Get the actionable task of the content object. The actionable task is the task that is not to do and not complete.
     */
	public Task getTask(ConfigurableWorkflow workflow) {
		if (getTasks() == null || getTasks().isEmpty())
			return null;
		for(Task task : getTasks()){
			if(!task.isToDo() && !task.isComplete()){
				ConfigurableWorkflow taskWorkflow = task.getWorkflowAction()!=null?task.getWorkflowAction().getConfigurableWorkflow():null;
				if(workflow == null || (taskWorkflow != null && workflow.getId() == taskWorkflow.getId())) {
					return task;
				}
			}
		}
		return null;
	}

	public User getTaskUser() {
		long lockedForUserId = this.getLockedForId();
		if (lockedForUserId > 0L)
			return User.findById(lockedForUserId);
		else
			return null;
	}

	public Set<Workgroup> getVisibleWorkgroups() {
		Set<Workgroup> result = new HashSet<>();

		if (this.getZone() != null) {
			// zone exists, then it is only visible to workgroups owns the zones.
			result.addAll(this.getZone().getWorkgroups());
		}

		return result;
	}

	public double getSegmentationPercentage() {
		return SegmentationAnalysis.getRecentPercentageOfRecipients(this.getSegmentationData(), UserUtil.getCurrentTouchpointContext().getId());
	}

	public String getSegmentationPercentageInStr() {
		String segmentation = "--";
		if(this.getSegmentationData() != null && !this.getSegmentationData().isEmpty()){
			segmentation = SegmentationAnalysis.getRecentPercentageOfRecipientsInStr(this.getSegmentationData(), UserUtil.getCurrentTouchpointContext().getId());
		}
		return segmentation;
	}

	public List<ContentObject> getReferencedLocalSmartTexts(){
		List<ContentObject> referenced = new ArrayList<>();
		for(ContentObjectAssociation ca : this.getContentObjectAssociations()){
			Content content = ca.getContent();
			if(content != null && content.hasLocalSmartTexts()){
				referenced.addAll(content.getLocalSmartTexts());
			}
		}
		return referenced;
	}

	public List<ContentObject> getReferencedLocalImages(){
		List<ContentObject> referenced = new ArrayList<>();
		for(ContentObjectAssociation ca : this.getContentObjectAssociations()){
			Content content = ca.getContent();
			if(content != null && content.hasLocalImageLibraryItems()){
				referenced.addAll(content.getLocalImageLibraryItems());
			}
		}
		return referenced;
	}

	// Find all the content objects that are used in this content object
	public List<ContentObject> findAllAssetsUsedIn(){
		String query = " SELECT DISTINCT cec.content_object_id FROM content_object_association ca, content_content_object_type cec "
					+ " WHERE ca.content_id = cec.content_id AND ca.content_object_id = :contentObjectId ";

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		sqlQuery.setParameter("contentObjectId", this.getId());
		List ids = sqlQuery.list();

		List<ContentObject> references = new ArrayList<>();
		for(Object idObj : ids){
			ContentObject contentObject = ContentObject.findById(Long.valueOf(idObj.toString()));
			if (contentObject != null)
				references.add(contentObject);
		}
		return references;
	}

	public List<ContentObject> findReferencedByGlobalSmartTexts(){
		String query = " SELECT DISTINCT ca.content_object_id FROM content_object_association ca, content_object co, content_content_object_type cec "
						+ " WHERE ca.content_object_id = co.id AND ca.content_id = cec.content_id  "
						+ " AND cec.content_object_id = :globalSTId AND co.object_type = 4 ";

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		sqlQuery.setParameter("globalSTId", this.getId());
		List ids = sqlQuery.list();

		List<ContentObject> documents = new ArrayList<>();
		for(Object idObj : ids){
			ContentObject contentObject = ContentObject.findById(Long.valueOf(idObj.toString()));
			if (contentObject != null)
				documents.add(contentObject);
		}
		return documents;
	}

	public List<ContentObject> findReferencedByGlobalImages(){
		String query = " SELECT DISTINCT ca.content_object_id FROM content_object_association ca, content_object co, content_content_object_type cec "
						+ " WHERE ca.content_object_id = co.id AND ca.content_id = cec.content_id  "
						+ " AND cec.content_object_id = :globalImageId AND co.object_type = 8 ";

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		sqlQuery.setParameter("globalImageId", this.getId());
		List ids = sqlQuery.list();

		List<ContentObject> documents = new ArrayList<>();
		for(Object idObj : ids){
			ContentObject contentObject = ContentObject.findById(Long.valueOf(idObj.toString()));
			if (contentObject != null)
				documents.add(contentObject);
		}
		return documents;
	}

	/**
	public String getSegmentationPercentageInStr() {
		String segmentation = "--";
		if (contentObjectDataTypeMap.containsKey(ContentObject.DATA_TYPE_WORKING))
		{
			segmentation = contentObjectDataTypeMap.get(ContentObject.DATA_TYPE_WORKING).getSegmentationPercentageInStr();
		}
		return segmentation;
	}
	 **/

	public ContentVO getDefaultProductionImageLibrary(MessagepointLocale locale) {
		ContentVO contentVO = new ContentVO();
		ContentObjectAssociation defaultPCA = ContentObjectAssociation.getDefaultProductionContentAssociation(this, ContentObject.DATA_TYPE_ACTIVE, locale);
		if(defaultPCA != null && defaultPCA.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT){
			defaultPCA = ContentObjectAssociation.getDefaultProductionContentAssociation(this, ContentObject.DATA_TYPE_ACTIVE, null);
		}

		while (defaultPCA != null && defaultPCA.getReferencingImageLibrary() != null)
		{
			defaultPCA = ContentObjectAssociation.getDefaultProductionContentAssociation(defaultPCA.getReferencingImageLibrary(), ContentObject.DATA_TYPE_ACTIVE, locale);
		}

		if (defaultPCA == null && locale != null) {

			// If there is no PCA for a specific locale, try to find the default language

			defaultPCA = ContentObjectAssociation.getDefaultProductionContentAssociation(this, ContentObject.DATA_TYPE_ACTIVE, null);

			while (defaultPCA != null && defaultPCA.getReferencingImageLibrary() != null)
			{
				defaultPCA = ContentObjectAssociation.getDefaultProductionContentAssociation(defaultPCA.getReferencingImageLibrary(), ContentObject.DATA_TYPE_ACTIVE, locale);
			}

			contentVO.setSameAsDefault(true);
		}

		if (defaultPCA != null) {

			Content content = defaultPCA.getContent();

			if (content != null) {
				contentVO.setContentId(content.getId());
                contentVO.setSha256Hash(content.getSha256Hash());
				contentVO.setContentSupplier(() -> content.getContent());
				contentVO.setImageLocation(content.getImageLocation());
				contentVO.setImageName(content.getImageName());
				contentVO.setAppliedImageFilename(content.getAppliedImageFilename());
				contentVO.setImageUploadedDate(content.getImageUploadedDate());
				contentVO.setImageLink(Content.copyImageLinkFrom(content.getImageLink()));
				contentVO.setImageAltText(Content.copyImageAltTextFrom(content.getImageAltText()));
				contentVO.setImageExtLink(Content.copyImageExtLinkFrom(content.getImageExtLink()));
				contentVO.setImageExtPath(Content.copyImageExtPathFrom(content.getImageExtPath()));
				contentVO.setAssetId(content.getAssetId());
				contentVO.setAssetSite(content.getAssetSite());
				contentVO.setAssetURL(content.getAssetURL());
				contentVO.setAssetLastUpdate(content.getAssetLastUpdate());
				contentVO.setAssetLastSync(content.getAssetLastSync());

			}
			else {
				contentVO.setContentId(0L);
                contentVO.setSha256Hash(null);
				contentVO.setShared(false);
				contentVO.setCustom(true);
				contentVO.setEmpty(true);
			}

			if(defaultPCA.getContentObject() != null) {
				contentVO.setImageLibraryName(defaultPCA.getContentObject().getName());
				contentVO.setImageLibraryId(getId());
				contentVO.setImageLibraryObjectType(getObjectType());
			}
		}

		return contentVO;
	}

	public List<ContentObject> findAllReferencedNonActiveLocalAssets(){
		String query = " SELECT lco.id " +
						" FROM content_object lco, content_object_data lcod, content_content_object_type ccot, content c, content_object_association ca " +
						" WHERE lco.id = lcod.content_object_id AND ccot.content_object_id = lco.id AND ccot.content_id = c.id AND c.id = ca.content_id " +
						" AND ca.content_object_id = :contentObjectId AND lcod.data_type = 1 AND lco.id IN ( " +
						" SELECT content_object_id FROM content_object_data WHERE data_type != 4 GROUP BY content_object_id HAVING COUNT(*) = 1) " +
						" ORDER BY lco.name ASC ";
		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		sqlQuery.setParameter("contentObjectId", this.getId());
		List<?> ids = sqlQuery.list();
		List<ContentObject> objs = new ArrayList<>();
		for(Object idObj : ids){
			objs.add(ContentObject.findById(((BigInteger)idObj).longValue()));
		}
		return objs;
	}

	@SuppressWarnings("unchecked")
	public static ContentObject findByOriginAndDocument(ContentObject origin, Document document) {
		ContentObject result = null;

		if (document != null && origin != null)
		{
			StringBuilder query = new StringBuilder("	SELECT DISTINCT	co ");
			query.append("						   	FROM 			ContentObject as co ");
			query.append("							WHERE			co.document.id = :documentId ");
			query.append("							AND 			co.originObject.id = :originObjectId ");

			Map<String, Object> params = new HashMap<>();
			params.put("documentId", document.getId());
			params.put("originObjectId", origin.getId());

			List<ContentObject> orderedList = (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
			if (orderedList != null && !orderedList.isEmpty())
				result = orderedList.get(0);
		}

		return result;
	}


	@SuppressWarnings("rawtypes")
	public static List<Long> findAllIdsByDocumentInCollection(Document document){
		List<Long> cliIds = new ArrayList<>();
		if (document != null)
		{
			String query = 	"SELECT DISTINCT clt.content_object_id " +
					"FROM content_object_tpc_map clt " +
					"LEFT OUTER JOIN tp_collection_touchpoint tct ON clt.tp_collection_id = tct.collection_id " +
					"WHERE tct.document_id = " + document.getId();

			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
			List ids = sqlQuery.list();
			for(Object idObj : ids)
				cliIds.add(((BigInteger)idObj).longValue());

		}
		if(cliIds.isEmpty())
			cliIds.add((long)0);
		return cliIds;
	}

	public static List<Long> findAllIdsByDocumentInCollection(List<Document> documents){

		List<String> documentIds = new ArrayList<>();
		for ( Document currentDocument: documents )
			documentIds.add( String.valueOf(currentDocument.getId()) );
		if (documentIds.isEmpty())
			documentIds.add( "0" );

		String query = 	"SELECT DISTINCT ect.content_object_id " +
				"FROM content_object_tpc_map ect " +
				"LEFT OUTER JOIN tp_collection_touchpoint tct ON ect.tp_collection_id = tct.collection_id " +
				"WHERE tct.document_id IN (" + StringUtil.join(documentIds,",") + ") ";

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);

		List ids = sqlQuery.list();
		List<Long> eciIds = new ArrayList<>();
		for(Object idObj : ids)
			eciIds.add(((BigInteger)idObj).longValue());

		if(eciIds.isEmpty())
			eciIds.add((long)0);
		return eciIds;
	}

	public static List<ContentObject> findAllGlobalImagesFiltered(Document document, ContentObject selectedCL, ContentObject targetCLI, String nameSearchStr, String extType, int numCap, boolean activeOnly, boolean filterForCommunications){

		List<ContentObject> clList = new ArrayList<>();

		String query = findAllGlobalImagesFilteredAsCountOrList(document, selectedCL, targetCLI, nameSearchStr, numCap, activeOnly, filterForCommunications, true, findAllIdsByDocumentInCollection(document));
		if ( selectedCL != null && selectedCL.isGlobalImage())
			clList.add(selectedCL);

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);

		// Skip the number cap here as the post query image extension type filtering
		//if ( numCap > 0 )
		//	sqlQuery.setMaxResults((selectedCL != null ? numCap - 1 : numCap));

		if ( targetCLI != null )
			sqlQuery.setParameter("targetCL_Id", targetCLI.getId());
		if ( document != null )
			sqlQuery.setParameter("documentId", document.getId());
		if ( activeOnly )
			sqlQuery.setParameter("dataType", ContentObject.DATA_TYPE_ACTIVE);

		if ( selectedCL != null )
			sqlQuery.setParameter("selectedId", selectedCL.getId());

		@SuppressWarnings("unchecked")
		List<Object> ids = sqlQuery.list();
		for(Object idObj : ids)
		{
			if ( idObj != null )
			{
				ContentObject image = ContentObject.findByIdActiveDataFocusCentric(((BigInteger)idObj).longValue());
				if(extType == null || filterImageByExtType(image, extType)) {
					clList.add(image);
				}
			}
		}

		return clList;
	}

	public static Long findAllFilteredCount(Document document, ContentObject selectedCL, ContentObject targetCLI, String nameSearchStr, String extType, int numCap, boolean activeOnly, boolean filterForCommunications){

		long totalItems = 0;

		String query = findAllGlobalImagesFilteredAsCountOrList(document, selectedCL, targetCLI, nameSearchStr, numCap, activeOnly, filterForCommunications, false, findAllIdsByDocumentInCollection(document));
		String countQuery = query.replaceFirst("SELECT DISTINCT cl.ID", "SELECT COUNT(DISTINCT cl.ID) ");

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(countQuery);

		if ( targetCLI != null )
			sqlQuery.setParameter("targetCL_Id", targetCLI.getId());
		if ( document != null )
			sqlQuery.setParameter("documentId", document.getId());
		if ( activeOnly )
			sqlQuery.setParameter("dataType", ContentObject.DATA_TYPE_ACTIVE);

		@SuppressWarnings("unchecked")
		List<Object> ids = sqlQuery.list();
		for(Object idObj : ids)
		{
			if ( idObj != null )
			{
				ContentObject image = ContentObject.findByIdActiveDataFocusCentric(((BigInteger)idObj).longValue());
				if(extType == null || filterImageByExtType(image, extType)) {
					totalItems += (((BigInteger) idObj).longValue());
				}
			}
		}

		return totalItems;
	}

	public static boolean filterImageByExtType(ContentObject image, String extType){
		String imageName = ContentObjectAssociation.findImageNameForImage(image, ContentObject.DATA_TYPE_ACTIVE);

		if ( imageName != null && extType != null && !extType.isEmpty() ) {
			if ( extType.equals("JPEG") ) {
				return imageName.toUpperCase().contains("JPEG")
						|| imageName.toUpperCase().contains("JPG");
			} else if ( extType.equals("TIFF") ) {
				return imageName.toUpperCase().contains("TIFF")
						|| imageName.toUpperCase().contains("TIF");
			} else if ( extType.equals("WEB") ) {
				return imageName.contains("JPEG")
						|| imageName.toUpperCase().contains("JPG")
						|| imageName.toUpperCase().contains("GIF")
						|| imageName.toUpperCase().contains("PNG");
			} else {
				return imageName.toUpperCase().contains(extType);
			}
		}

		return false;
	}

	/*
	 * Find all production version of all ContentLibrarys that are filtered by the params
	 */
	public static String findAllGlobalImagesFilteredAsCountOrList(Document document, ContentObject selectedCL, ContentObject targetContentObject, String nameSearchStr, int numCap, boolean activeOnly, boolean filterForCommunications, boolean isList, List<Long> ids){

		StringBuilder query = new StringBuilder("	SELECT DISTINCT cl.ID FROM CONTENT_OBJECT cl ");
		query.append("							INNER JOIN CONTENT_OBJECT_DATA cli ON cli.CONTENT_OBJECT_ID = cl.ID ");
		query.append("							LEFT OUTER JOIN CONTENT_OBJECT_DOCUMENT_MAP d ON d.CONTENT_OBJECT_ID = cl.ID ");
		query.append("							WHERE cl.OBJECT_TYPE = 8 ");

		// Image Library referencing Image Library
		if ( targetContentObject != null ) {

			// Usage type must be the same
			if(targetContentObject.isGlobalImage()) {
				query.append("						AND (cl.usage_type_id = ").append(targetContentObject.getUsageTypeId()).append(") ");
			}
			// At least one Touchpoint assignment must be match (or Global)
			if ( !targetContentObject.getDocuments().isEmpty() ) {
				List<String> targetCLIdocIdArray = new ArrayList<>();
				for ( Document currentDocument: targetContentObject.getDocuments() )
					targetCLIdocIdArray.add(""+currentDocument.getId());

				String targetCLIdocIds = "(" + StringUtil.join(targetCLIdocIdArray,",") + ")";

				query.append("					AND ( d.document_id IN ").append(targetCLIdocIds);
				query.append("					OR cl.variable_content_enabled = false ) ");

			}

			// Cannot reference self
			query.append("						AND cl.id != :targetCL_Id ");

		} else {
			if (filterForCommunications)
				query.append("					AND (cl.usage_type_id = " + LibraryItemUsageType.ID_COMMUNICATION + " OR cl.usage_type_id = " + LibraryItemUsageType.ID_UNRESTRICTED + ") ");
			else
				query.append("					AND (cl.usage_type_id = " + LibraryItemUsageType.ID_MESSAGE + " OR cl.usage_type_id = " + LibraryItemUsageType.ID_UNRESTRICTED + ") ");
		}

		if ( document != null )
		{
			query.append("						AND (d.document_id = :documentId OR cl.variable_content_enabled = false ");
			if (!ids.isEmpty() && ids.get(0) > 0L)
			{
				query.append("OR cl.id IN ( ");
				boolean next = false;
				for (Long id : ids)
				{
					if (next)
						query.append(", ");
					else
						next = true;

					query.append(id);
				}
				query.append(" )");
			}
			query.append(") ");
		}

		if ( nameSearchStr != null && !nameSearchStr.isEmpty()) {

			query.append("			AND ");

			Map<String, List<String>> searchLists = TagCloud.tokenizeSearchString(nameSearchStr);

			for (int i=0; i < searchLists.get("tags").size(); i++) {
				if ( i != 0 )
					query.append("	AND ");
				query.append("		LOWER(cli.metatags) LIKE '%").append(searchLists.get("tags").get(i).toLowerCase().replaceAll("_", "|_").replaceAll("%", "|%")).append("%' ESCAPE '|' ");

			}
			for (int i=0; i < searchLists.get("names").size(); i++) {
				if ( i != 0 || !searchLists.get("tags").isEmpty())
					query.append("	AND ");
				query.append("		LOWER(cl.name) LIKE '%").append(searchLists.get("names").get(i).toLowerCase().replaceAll("_", "|_").replaceAll("%", "|%")).append("%' ESCAPE '|' ");

			}

		}

		query.append("			AND	cl.removed = false ");
		if ( activeOnly )
			query.append("    		AND	cli.data_type = :dataType ");

		if ( selectedCL != null && isList)
			query.append("    	AND	cl.id != :selectedId ");

		return query.toString();
	}


	public static List<ContentObject> findAllLocalImageLibraryFiltered(Document document, TouchpointSelection touchpointSelection, ContentObject selectedCL, ContentObject targetCLI, String nameSearchStr, String extType, int numCap){

		List<ContentObject> clList = new ArrayList<>();

		String query = getQueryAllLocalImagesFilteredAsCountOrList(document, touchpointSelection, selectedCL, targetCLI, nameSearchStr, extType, numCap, true);
		if ( selectedCL != null  && selectedCL.isLocalImage())
			clList.add(selectedCL);

		try {

			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);

			if ( numCap > 0 )
				sqlQuery.setMaxResults((selectedCL != null ? numCap - 1 : numCap));

			if ( targetCLI != null )
				sqlQuery.setParameter("targetCL_Id", targetCLI.getId());
			if ( document != null )
				sqlQuery.setParameter("documentId", document.getId());

			sqlQuery.setParameter("dataType", ContentObject.DATA_TYPE_ACTIVE);
			if ( selectedCL != null )
				sqlQuery.setParameter("selectedId", selectedCL.getId());

			@SuppressWarnings("unchecked")
			List<Object> ids = sqlQuery.list();
			for(Object idObj : ids)
			{
				if ( idObj != null )
				{
					clList.add(ContentObject.findByIdActiveDataFocusCentric(((BigInteger)idObj).longValue()));
				}
			}

		} catch (Exception e) {
			log.error("Error - Unable to resolve request for local images: "+e.getMessage(),e);
		}

		return clList;
	}

	public static Long findAllLocalImagesFilteredCount(Document document, TouchpointSelection touchpointSelection, ContentObject selectedCL, ContentObject targetCLI, String nameSearchStr, String extType, int numCap){

		long totalItems = 0;

		String query = getQueryAllLocalImagesFilteredAsCountOrList(document, touchpointSelection, selectedCL, targetCLI, nameSearchStr, extType, numCap, false);
		String countQuery = query.replaceFirst("SELECT DISTINCT cl.ID", "SELECT COUNT(DISTINCT cl.ID) ");

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(countQuery);

		if ( targetCLI != null )
			sqlQuery.setParameter("targetCL_Id", targetCLI.getId());
		if ( document != null )
			sqlQuery.setParameter("documentId", document.getId());

		sqlQuery.setParameter("dataType", ContentObject.DATA_TYPE_ACTIVE);

		@SuppressWarnings("unchecked")
		List<Object> ids = sqlQuery.list();
		for(Object idObj : ids)
		{
			if ( idObj != null )
			{
				totalItems += (((BigInteger)idObj).longValue());
			}
		}

		return totalItems;
	}



	// TODO combine findAllByAdvancedQuery

	// From Message
	public static List<ContentObject> findAllByAdvancedQuery(Document document, TouchpointSelection touchpointSelection, boolean includesArchived, long docSectionId, long docZoneId, String nameSearchStr, int numCap){
		Map<String, String> firstLevelJoinAlias 		= new LinkedHashMap<>();
		Map<String, String> secondLevelJoinAlias 		= new LinkedHashMap<>();
		List<MessagepointCriterion> firstLevelCriterionList 	= new ArrayList<>();
		List<MessagepointCriterion> secondLevelCriterionList 	= new ArrayList<>();
		Map<String, JoinType> secondLevelJoinType	= new HashMap<>();
		List<MessagepointOrder> orderList 						= new ArrayList<>();

		/**
		 * First Run: Apply the default filters (Document, Selection and Versions)
		 */
		boolean appliesAlternateTemplate = touchpointSelection != null && touchpointSelection.getAlternateLayout() != null;
		List<Long> zoneIdList = new ArrayList<>();

		zoneIdList = AsyncListTableController.getZoneList(document.getId(), docSectionId, docZoneId, !appliesAlternateTemplate);
		// Alternate layout visibility filter
		if ( appliesAlternateTemplate ) {
			User principal = UserUtil.getPrincipalUser();
			List<Long> nonVisibleZones = new ArrayList<>();
			for ( Long currentZoneId : zoneIdList ) {
				Zone primaryZone = Zone.findById(currentZoneId);
				Zone alternateZone = touchpointSelection.getAlternateLayout().findZoneByParent(primaryZone);
				if ( !alternateZone.getWorkgroups().contains(principal.getWorkgroup()) || !alternateZone.isEnabled() )
					nonVisibleZones.add(currentZoneId);
			}
			zoneIdList.removeAll(nonVisibleZones);
		}

		if (zoneIdList.isEmpty())
			zoneIdList.add((long) 0);

		// Version filter
		List<Long> msgInstIds = new ArrayList<>();

		if(includesArchived){
			msgInstIds = ContentObject.findUniqueVisibleIdsOfMostRecentCopies(zoneIdList, 0, -1, nameSearchStr);
		}else{
			msgInstIds = ContentObject.findUniqueVisibleIdsOfMostRecentCopies(zoneIdList, 0, -1, nameSearchStr);
		}
		if (msgInstIds.isEmpty())
			msgInstIds.add((long) 0);


		// Variant filter (The variant message can be shared from its ancestor selections)
		if ( touchpointSelection != null && document.isEnabledForVariation() ) {
			Set<Long> tpSelectionIds = new HashSet<>();
			TouchpointSelection parentSelection = touchpointSelection;
			while (parentSelection != null) {
				tpSelectionIds.add(parentSelection.getId());
				parentSelection = parentSelection.getParent();
			}

			if (tpSelectionIds.isEmpty())
				tpSelectionIds.add((long) 0);

			firstLevelCriterionList.add(MessagepointRestrictions.or(MessagepointRestrictions.isNull("owningTouchpointSelection"), MessagepointRestrictions.in("owningTouchpointSelection.id", tpSelectionIds)));
		}

		firstLevelCriterionList.add(MessagepointRestrictions.eq("objectType", ContentObject.OBJECT_TYPE_MESSAGE));

		/**
		 * Second Run: Apply the left filters (Status)
		 */

		// SORT
		orderList.add(MessagepointOrder.asc(HibernateObjectManager.MODEL_ALIAS_PREFIX + ".name"));

		PostQueryHandler postHandler = null;

		ServiceExecutionContext context = HibernatePaginationService.createContext(ContentObject.class, msgInstIds, firstLevelCriterionList, secondLevelCriterionList, firstLevelJoinAlias, secondLevelJoinAlias, secondLevelJoinType, 1, numCap, orderList, null, postHandler);
		Service paginationService = MessagepointServiceFactory.getInstance().lookupService(HibernatePaginationService.SERVICE_NAME, HibernatePaginationService.class);
		paginationService.execute(context);
		PaginationServiceResponse serviceResponse = (PaginationServiceResponse) context.getResponse();
		List<?> list = serviceResponse.getPage().getList();

		List<ContentObject> messages = new ArrayList<>();
		for ( Object o : list ){
			if ( o instanceof ContentObject ){
				ContentObject message = (ContentObject) o;
				if(!messages.contains(message)){
					message.checkAndAdjustFocus();
					messages.add(message);
				}
			}
		}

		return messages;
	}

	// From ContentLibrary
	public static List<ContentObject> findAllImagesByAdvancedQuery(Document document, boolean filterForCommunications, boolean activeOnly,
																   String nameSearchStr, int numCap){

		/**
		 * First Run: Apply the default filters (Document, Selection and Versions)
		 */
		List<Long> docIds = new ArrayList<>();
		if (document != null && document.getId() > 0 ) {
			docIds.add(document.getId());
		} else {
			StringBuilder query = new StringBuilder(" SELECT DISTINCT d.id FROM Workgroup wg ");
			query.append("    						INNER JOIN 		wg.zones AS z ");
			query.append("    						INNER JOIN		z.document AS d ");
			query.append("    						WHERE 			d.enabled = true ");
			query.append("    						AND 			wg.id = :workgroupId ");
			query.append("    						AND 			z.enabled = true ");

			Map<String, Object> params = new HashMap<>();
			params.put("workgroupId", UserUtil.getPrincipalUser().getWorkgroupId());
			docIds.addAll((List<Long>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params));
		}

		/**
		 * Second Run: Query for global image library items
		 */
		String query = 	"SELECT DISTINCT (co.id) " +
						"FROM 	content_object co " +
						"INNER JOIN content_object_data cod ON cod.content_object_id = co.id " +
						"INNER JOIN content_object_document_map codm ON codm.content_object_id = co.id ";

		query += 		"WHERE co.object_type = " + ContentObject.OBJECT_TYPE_GLOBAL_IMAGE + " ";
		if (!docIds.isEmpty())
			query +=	"AND codm.document_id IN (" + StringUtils.join(docIds, ",") + ") ";

		// Search embedded content name/tags
		StringBuilder searchQuery = new StringBuilder();
		if ( nameSearchStr != null && !nameSearchStr.isEmpty() && !nameSearchStr.equals("NULL") ) {
			Map<String, List<String>> searchLists = TagCloud.tokenizeSearchString(nameSearchStr);
			for (int i=0; i < searchLists.get("tags").size(); i++)
				searchQuery.append("AND LOWER(cod.metatags) LIKE '%").append(searchLists.get("tags").get(i).toLowerCase()).append("%' ");
			for (int i=0; i < searchLists.get("names").size(); i++)
				searchQuery.append("AND LOWER(co.name) LIKE '%").append(searchLists.get("names").get(i).toLowerCase()).append("%' ");
		}

		if ( filterForCommunications )
			query +=	"AND (co.usage_type_id = " + LibraryItemUsageType.ID_COMMUNICATION + " OR co.usage_type_id = " + LibraryItemUsageType.ID_UNRESTRICTED + ") ";

		if ( activeOnly )
			query +=	"AND cod.data_type = 2 ";

		query += 		searchQuery;
		query +=		"GROUP BY co.id ";

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		List ids = sqlQuery.list();
		List<Long> imgCoIds = new ArrayList<>();
		for(Object idObj : ids){
			imgCoIds.add(((BigInteger)idObj).longValue());
		}

		if (imgCoIds.isEmpty())
			imgCoIds.add((long) 0);

		Map<String, String> firstLevelJoinAlias 		= new LinkedHashMap<>();
		Map<String, String> secondLevelJoinAlias 		= new LinkedHashMap<>();
		List<MessagepointCriterion> firstLevelCriterionList 	= new ArrayList<>();
		List<MessagepointCriterion> secondLevelCriterionList 	= new ArrayList<>();
		Map<String, JoinType> secondLevelJoinType	= new HashMap<>();
		List<MessagepointOrder> orderList 						= new ArrayList<>();

		// SORT
		orderList.add(MessagepointOrder.asc(HibernateObjectManager.MODEL_ALIAS_PREFIX + ".name"));

		PostQueryHandler postHandler = null;

		ServiceExecutionContext context = HibernatePaginationService.createContext(ContentObject.class, imgCoIds, firstLevelCriterionList, secondLevelCriterionList, firstLevelJoinAlias, secondLevelJoinAlias, secondLevelJoinType, 1, numCap, orderList, null, postHandler);
		Service paginationService = MessagepointServiceFactory.getInstance().lookupService(HibernatePaginationService.SERVICE_NAME, HibernatePaginationService.class);
		paginationService.execute(context);
		PaginationServiceResponse serviceResponse = (PaginationServiceResponse) context.getResponse();
		List<?> list = serviceResponse.getPage().getList();

		List<ContentObject> contentLibraries = new ArrayList<>();
		for (Object o : list) {
			if(o instanceof ContentObject) {
				ContentObject cl = (ContentObject)o;
				if(!contentLibraries.contains(cl)){
					contentLibraries.add(cl);
				}
			}
		}
		return contentLibraries;
	}

	/*
	 * Find all local images that are filtered by the params
	 */
	public static String getQueryAllLocalImagesFilteredAsCountOrList(Document document, TouchpointSelection touchpointSelection, ContentObject selectedCL, ContentObject targetCLI, String nameSearchStr, String extType, int numCap, boolean isList){

		StringBuilder query = new StringBuilder("	SELECT DISTINCT cl.ID FROM CONTENT_OBJECT cl ");
		query.append("							INNER JOIN CONTENT_OBJECT_DATA cli ON cli.CONTENT_OBJECT_ID = cl.ID ");

		if ( extType != null && !extType.isEmpty() ) {
			query.append("						LEFT OUTER JOIN CONTENT_OBJECT_ASSOCIATION dca ON dca.CONTENT_OBJECT_ID = cl.ID ");
			query.append("						LEFT JOIN CONTENT dc ON dc.ID = dca.CONTENT_ID ");
		}

		query.append("							WHERE cl.OBJECT_TYPE = 2 ");


		// VARIANT LOCAL IMAGE (The variant image can be shared from its ancestor selections)
		if ( touchpointSelection != null && document != null && document.isEnabledForVariation() ) {
			Set<String> tpSelectionIds = new HashSet<>();
			TouchpointSelection parentSelection = touchpointSelection;
			while ( parentSelection != null ) {
				tpSelectionIds.add( String.valueOf(parentSelection.getId()) );
				parentSelection = parentSelection.getParent();
			}

			if (tpSelectionIds.isEmpty())
				tpSelectionIds.add(String.valueOf(0));

			query.append("						AND ( cl.TP_SELECTION_ID IS NULL OR cl.TP_SELECTION_ID IN (").append(StringUtil.join(tpSelectionIds, ",")).append(") ) ");
		}

		// Image Library referencing Image Library: Cannot reference self
		if ( targetCLI != null )
			query.append("						AND cl.id != :targetCL_Id ");

		if ( document != null )
			query.append("						AND cl.DOCUMENT_ID = :documentId ");

		if ( nameSearchStr != null && !nameSearchStr.isEmpty()) {

			query.append("			AND ");

			Map<String, List<String>> searchLists = TagCloud.tokenizeSearchString(nameSearchStr);

			for (int i=0; i < searchLists.get("tags").size(); i++) {
				if ( i != 0 )
					query.append("	AND ");
				query.append("		LOWER(cli.metatags) LIKE '%").append(searchLists.get("tags").get(i).toLowerCase().replaceAll("_", "|_").replaceAll("%", "|%")).append("%' ESCAPE '|' ");

			}
			for (int i=0; i < searchLists.get("names").size(); i++) {
				if ( i != 0 || !searchLists.get("tags").isEmpty())
					query.append("	AND ");
				query.append("		LOWER(cl.name) LIKE '%").append(searchLists.get("names").get(i).toLowerCase().replaceAll("_", "|_").replaceAll("%", "|%")).append("%' ESCAPE '|' ");

			}

		}

		if ( extType != null && !extType.isEmpty() ) {
			query.append("  AND ( dc.id IS NOT NULL AND ( ");
			if ( extType.equals("JPEG") ) {
				query.append("	(UPPER(dc.image_name) LIKE '%JPEG' ");
				query.append("	OR UPPER(dc.image_name) LIKE '%JPG') ");
			} else if ( extType.equals("TIFF") ) {
				query.append("	(UPPER(dc.image_name) LIKE '%TIFF' ");
				query.append("	OR UPPER(dc.image_name) LIKE '%TIF') ");
			} else if ( extType.equals("WEB") ) {
				query.append("	(UPPER(dc.image_name) LIKE '%JPEG' ");
				query.append("	OR UPPER(dc.image_name) LIKE '%JPG' ");
				query.append("	OR UPPER(dc.image_name) LIKE '%GIF' ");
				query.append("	OR UPPER(dc.image_name) LIKE '%PNG') ");
			} else {
				query.append("	UPPER(dc.image_name) LIKE '%").append(extType).append("' ");
			}
			query.append(" ) ) ");
		}

		query.append("			AND	cl.removed = false ");
		query.append("    		AND	cli.data_type = :dataType ");

		if ( selectedCL != null && isList)
			query.append("    	AND	cl.id != :selectedId ");

		return query.toString();
	}

	public static List<ContentObject> findAllCanvasWithActiveInstanceByDocumentAndZone(Document document, Zone zone, long contentTypeId, Boolean supportsTables, Boolean supportsForms, Boolean supportsBarcodes, Boolean supportsContentMenus, int canvasMaxWidth, int canvasMaxHeight) {
		return findAllGlobalSmartTextByAdvancedQuery(document, zone, null, false, false, contentTypeId, true, false, null, -1, supportsTables, supportsForms, supportsBarcodes, supportsContentMenus, canvasMaxWidth, canvasMaxHeight);
	}

	public static List<ContentObject> findAllGlobalSmartTextByAdvancedQuery(Document document, Zone zone, ContentObject selectedECI, boolean filterForGlobal, boolean filterForCommunications,
																			long contentTypeId, boolean activeOnly, boolean includesArchived, String nameSearchStr, int numCap) {
		return findAllGlobalSmartTextByAdvancedQuery(document, zone, selectedECI, filterForGlobal, filterForCommunications,
				contentTypeId, activeOnly, includesArchived, nameSearchStr, numCap, null, null, null, null, 0, 0);
	}
	public static List<ContentObject> findAllGlobalSmartTextByAdvancedQuery(Document document, Zone zone, ContentObject selectedECI, boolean filterForGlobal,
																			boolean filterForCommunications, long contentTypeId, boolean activeOnly, boolean includesArchived,
																			String nameSearchStr, int numCap, Boolean supportsTables, Boolean supportsForms, Boolean supportsBarcodes, Boolean supportsContentMenus,
																			int canvasMaxWidth, int canvasMaxHeight) {
		Set<Document> documents = new HashSet<>();
		if ( document != null )
			documents.add(document);
		return findAllGlobalSmartTextByAdvancedQuery(documents, zone, selectedECI, filterForGlobal, filterForCommunications, contentTypeId, activeOnly, includesArchived,
				nameSearchStr, numCap, supportsTables, supportsForms, supportsBarcodes, supportsContentMenus, canvasMaxWidth, canvasMaxHeight, false);
	}
	@SuppressWarnings("unchecked")
	public static List<ContentObject> findAllGlobalSmartTextByAdvancedQuery(Set<Document> documents, Zone zone, ContentObject selectedECI, boolean filterForGlobal,
																			boolean filterForCommunications, long contentTypeId, boolean activeOnly, boolean includesArchived,
																			String nameSearchStr, int numCap, Boolean supportsTables, Boolean supportsForms, Boolean supportsBarcodes, Boolean supportsContentMenus,
																			int canvasMaxWidth, int canvasMaxHeight, Boolean snippetsOnly) {

		List<Document> appliedDocuments = new ArrayList<>();
		if (!documents.isEmpty()) {
			for ( Document currentDocument: documents )
				appliedDocuments.add( currentDocument.getRootDocument() );
		}

		StringBuilder query = new StringBuilder("	SELECT DISTINCT	ec ");

		if ( zone != null && zone.isRestrictSharedAssets() )
			query.append("						FROM 			ContentObject as ec, Zone as z ");
		else
			query.append("						FROM 			ContentObject as ec ");

		query.append("							INNER JOIN 		ec.contentObjectDataTypeMap as eci ");
		query.append("							LEFT OUTER JOIN ec.visibleDocuments as d ");
		query.append("							WHERE			ec.removed = false and ec.objectType = " + ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT);
		if(!includesArchived){
			// TODO query.append("							AND				vm.effectEndDate IS NULL ");
		}

		// USAGE
		if ( filterForGlobal )
			query.append("    					AND				ec.variableContentEnabled = false ");
		if ( selectedECI != null ) {
			query.append("						AND 			(ec.usageTypeId = ").append(selectedECI.getUsageTypeId()).append(") ");
		} else {
			if ( filterForCommunications )
				query.append("    				AND				(ec.usageTypeId = " + LibraryItemUsageType.ID_COMMUNICATION + " OR ec.usageTypeId = " + LibraryItemUsageType.ID_UNRESTRICTED + ") ");
			else
				query.append("    				AND				(ec.usageTypeId = " + LibraryItemUsageType.ID_MESSAGE + " OR ec.usageTypeId = " + LibraryItemUsageType.ID_UNRESTRICTED + ") ");
		}

		Map<String, Object> params = new HashMap<>();

		//"AND 		d.id IN (SELECT z.document.id FROM Zone z, Workgroup wg WHERE wg.id = :workgroupid AND z IN elements(wg.zones) AND z.enabled = true ) " +
		// ZONE
		if ( zone != null && zone.isRestrictSharedAssets() ) {
			query.append("						AND 			z.id = :zoneId ");
			query.append("						AND 			ec.id IN (SELECT zec.id FROM z.embeddedContentAssets AS zec) ");
			params.put("zoneId", zone.getId());
		}

		// DOCUMENT
		if (!appliedDocuments.isEmpty()) {
			query.append("						AND				(d.id IN :documentIds OR ec.variableContentEnabled = false OR ec.id IN (:ids)) ");
			List<Long> documentIds = new ArrayList<>();
			for (Document currentDocument: appliedDocuments)
				documentIds.add( currentDocument.getId() );
			params.put("documentIds", documentIds);
			params.put("ids", findAllIdsByDocumentInCollection(appliedDocuments));
		}

		// IF ACTIVE ONLY
		if ( activeOnly ) {
			query.append("    					AND				eci.dataType = :dataType ");
			params.put("dataType", ContentObject.DATA_TYPE_ACTIVE);
		}

		if ( contentTypeId > -1 ) {
			query.append("    					AND				ec.contentType.id = :contentTypeId ");
			params.put("contentTypeId", contentTypeId);
		}

		if ( supportsTables != null && !supportsTables ) {
			query.append("    				AND				eci.supportsTables = :supportsTables ");
			params.put("supportsTables", false);
		}
		if ( supportsForms != null && !supportsForms ) {
			query.append("    				AND				eci.supportsForms = :supportsForms ");
			params.put("supportsForms", false);
		}
		if ( supportsBarcodes != null && !supportsBarcodes ) {
			query.append("    				AND				eci.supportsBarcodes = :supportsBarcodes ");
			params.put("supportsBarcodes", false);
		}
		if ( supportsContentMenus != null && !supportsContentMenus ) {
			query.append("    				AND				eci.supportsContentMenus = :supportsContentMenus ");
			params.put("supportsContentMenus", false);
		}
		if ( contentTypeId == ContentType.SHARED_FREEFORM ) {
			if ( canvasMaxWidth > 0 && canvasMaxHeight > 0 ) {
				query.append("    					AND				eci.canvasTrimWidth <= :canvasMaxWidth ");
				query.append("    					AND				eci.canvasTrimHeight <= :canvasMaxHeight ");
				params.put("canvasMaxWidth", canvasMaxWidth);
				params.put("canvasMaxHeight", canvasMaxHeight);
			}
		}

		if ( snippetsOnly ) {
			query.append("    					AND				eci.insertAsBlockContent != :snippetsOnly ");
			params.put("snippetsOnly", snippetsOnly);
		}

		// SEARCH
		if ( nameSearchStr != null && !nameSearchStr.isEmpty()) {
			query.append("			AND ");

			Map<String, List<String>> searchLists = TagCloud.tokenizeSearchString(nameSearchStr);

			for (int i=0; i < searchLists.get("tags").size(); i++) {
				if ( i != 0 )
					query.append("	AND ");
				query.append("		LOWER(eci.metatags) LIKE '%").append(searchLists.get("tags").get(i).toLowerCase().replaceAll("_", "|_").replaceAll("%", "|%")).append("%' ESCAPE '|' ");

			}
			for (int i=0; i < searchLists.get("names").size(); i++) {
				if ( i != 0 || !searchLists.get("tags").isEmpty())
					query.append("	AND ");
				query.append("		LOWER(ec.name) LIKE '%").append(searchLists.get("names").get(i).toLowerCase().replaceAll("_", "|_").replaceAll("%", "|%")).append("%' ESCAPE '|' ");

			}
		}

		query.append(" ORDER BY ec.name ASC");

		List<ContentObject> embeddedContents = null;
		if ( numCap > 0 )
			embeddedContents = (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params, 1, numCap, "");
		else
			embeddedContents = (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);

		return embeddedContents;
	}


	// Utils
	//

	public String toString() {
		return Long.toString(getId());
	}

	public boolean isStatic()
	{
		return !isDynamicVariantEnabled() && !isStructuredContentEnabled();
	}

	public Long getMessageInstanceIdFromIds( Collection<Long> ids )
	{
		for ( ContentObjectData coid : 	contentObjectDataTypeMap.values() )
		{
			if ( ids.contains( coid.getId() ) )
				return coid.getId();
		}
		return 0L;
	}

	public static List<ContentObject> findAllVisibleMessageTypeFilteredByDocument(long documentId, Workgroup wg)
	{
		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT co ");
		query.append("FROM            ContentObject as co ");
		query.append("INNER JOIN      co.zone as z ");
		query.append("INNER JOIN      z.workgroups as wg ");
		query.append("WHERE           co.objectType = " + ContentObject.OBJECT_TYPE_MESSAGE);
		query.append(" AND            co.removed = false ");
		query.append(" AND            co.document.id = :documentId ");
		query.append(" AND            wg.id = :wgId ");
		query.append(" ORDER BY 	  co.name ");

		Map<String, Object> params = new HashMap<>();
		params.put("documentId", documentId);
		params.put("wgId", wg.getId());

		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	@Deprecated
	public static List<ContentObject> findByDocumentTypeAndStatus(Document d, int type, int status )
	{
		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT co ");
		query.append("FROM 			ContentObjectData as coid ");
		query.append("INNER JOIN	coid.contentObject as co ");
		query.append("WHERE			co.document.id = :documentId ");
		query.append("AND			coid.dataType = :status ");
		query.append("AND			co.objectType = :type ");

		Map<String, Object> params = new HashMap<>();
		params.put("documentId", d.getId());
		params.put("status", status);
		params.put("type", type);

		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	public static List<ContentObject> findLocalSmartTextByDocument(Long docId, boolean activeOnly) {
		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT 	co ");
		query.append("FROM            	ContentObjectData as coid ");
		query.append("INNER JOIN      	coid.contentObject as co ");
		query.append("WHERE           	coid.dataType IN (:dataTypes) ");
		query.append("  AND 			co.document.id = :docId ");
		query.append("  AND 			co.objectType = :objectType ");

		Map<String, Object> params = new HashMap<>();
		params.put("objectType", ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT);
		params.put("docId", docId);

		List<Integer> types = new ArrayList<>();
		types.add(ContentObject.DATA_TYPE_ACTIVE);
		if (!activeOnly)
			types.add(ContentObject.DATA_TYPE_WORKING);
		params.put("dataTypes", types);

		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	public static List<ContentObject> findLocalImagesByDocument(Long docId, boolean activeOnly) {
		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT 	co ");
		query.append("FROM            	ContentObjectData as coid ");
		query.append("INNER JOIN      	coid.contentObject as co ");
		query.append("WHERE           	coid.dataType IN (:dataTypes) ");
		query.append("  AND 			co.document.id = :docId ");
		query.append("  AND 			co.objectType = :objectType ");

		Map<String, Object> params = new HashMap<>();
		params.put("objectType", ContentObject.OBJECT_TYPE_LOCAL_IMAGE);
		params.put("docId", docId);

		List<Integer> types = new ArrayList<>();
		types.add(ContentObject.DATA_TYPE_ACTIVE);
		if (!activeOnly)
			types.add(ContentObject.DATA_TYPE_WORKING);
		params.put("dataTypes", types);

		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	public static List<ContentObject> findGlobalSmartTexts(boolean activeOnly) {
		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT co ");
		query.append("FROM            ContentObjectData as coid ");
		query.append("INNER JOIN      coid.contentObject as co ");
		query.append("WHERE           coid.dataType IN (:dataTypes) ");
		query.append("  AND co.objectType = :objectType ");

		Map<String, Object> params = new HashMap<>();
		params.put("objectType", ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT);

		List<Integer> types = new ArrayList<>();
		types.add(ContentObject.DATA_TYPE_ACTIVE);
		if (!activeOnly)
			types.add(ContentObject.DATA_TYPE_WORKING);
		params.put("dataTypes", types);

		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	public static List<ContentObject> findGlobalSmartTextsVisibleForDocument(Document document, boolean activeOnly) {
		return findGlobalSmartTextsVisibleForDocument( document, activeOnly, false );
	}
	public static List<ContentObject> findGlobalSmartTextsVisibleForDocument( Document document, boolean activeOnly, boolean forConnectedUsage )
	{
		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT co ");
		query.append("FROM            ContentObjectData as coid ");
		query.append("INNER JOIN      coid.contentObject as co ");
		query.append("LEFT JOIN		  co.visibleDocuments as d ");
		query.append("WHERE           coid.dataType IN (:dataTypes) ");
		query.append("  AND co.objectType = :objectType ");
		query.append("  AND (co.variableContentEnabled = false OR d.id = :documentId)");

		Map<String, Object> params = new HashMap<>();
		params.put("objectType", ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT);
		params.put("documentId", document.getId());

		List<Integer> types = new ArrayList<>();
		types.add(ContentObject.DATA_TYPE_ACTIVE);
		if ( !activeOnly )
			types.add(ContentObject.DATA_TYPE_WORKING);
		params.put("dataTypes", types);

		if ( forConnectedUsage )
			query.append(" AND (co.usageTypeId = " + LibraryItemUsageType.ID_COMMUNICATION + " OR co.usageTypeId = " + LibraryItemUsageType.ID_UNRESTRICTED + ") ");

		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	public static List<ContentObject> findGlobalImages( boolean activeOnly )
	{
		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT co ");
		query.append("FROM            ContentObjectData as coid ");
		query.append("INNER JOIN      coid.contentObject as co ");
		query.append("WHERE           coid.dataType IN (:dataTypes) ");
		query.append("  AND co.objectType = :objectType ");

		Map<String, Object> params = new HashMap<>();
		params.put("objectType", ContentObject.OBJECT_TYPE_GLOBAL_IMAGE);

		List<Integer> types = new ArrayList<>();
		types.add(ContentObject.DATA_TYPE_ACTIVE);
		if ( !activeOnly )
			types.add(ContentObject.DATA_TYPE_WORKING);
		params.put("dataTypes", types);

		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	public static List<ContentObject> findGlobalImagesVisibleForDocument( Document document, boolean activeOnly, boolean forConnectedUsage )
	{
		return ContentObject.findGlobalImagesVisibleForDocument(document, activeOnly, forConnectedUsage, forConnectedUsage);
	}

	public static List<ContentObject> findGlobalImagesVisibleForDocument( Document document, boolean activeOnly, boolean forConnectedUsage, boolean restrictChannel )
	{
		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT co ");
		query.append("FROM            ContentObjectData as coid ");
		query.append("INNER JOIN      coid.contentObject as co ");
		query.append("LEFT JOIN		  co.visibleDocuments as d ");
		query.append("WHERE           coid.dataType IN (:dataTypes) ");
		query.append("  AND co.objectType = :objectType ");
		query.append("  AND (co.variableContentEnabled = false OR d.id = :documentId)");

		Map<String, Object> params = new HashMap<>();
		params.put("objectType", ContentObject.OBJECT_TYPE_GLOBAL_IMAGE);
		params.put("documentId", document.getId());

		List<Integer> types = new ArrayList<>();
		types.add(ContentObject.DATA_TYPE_ACTIVE);
		if ( !activeOnly )
			types.add(ContentObject.DATA_TYPE_WORKING);
		params.put("dataTypes", types);

		if (forConnectedUsage ) {
			query.append(" AND (co.usageTypeId = " + LibraryItemUsageType.ID_COMMUNICATION + " OR co.usageTypeId = " + LibraryItemUsageType.ID_UNRESTRICTED + ") ");
			if (restrictChannel) {
				Channel channelContext = UserUtil.getCurrentChannelContext();
				if (channelContext != null) {
					params.put("channelId", channelContext.getId());
					query.append("  AND (co.channelContextId = :channelId or co.channelContextId is null)");
				}
			}
		}
		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	public static List<ContentObject> findMessagesByDocument( Document doc, boolean mustHaveWorking )
	{
		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT co ");
		query.append("FROM            ContentObjectData as coid ");
		query.append("INNER JOIN      coid.contentObject as co ");
		query.append("WHERE           coid.dataType IN (:dataTypes) ");
		query.append("  AND co.document.id = :docId ");
		query.append("  AND co.objectType = :objectType ");
		query.append("  AND co.removed = false ");

		Map<String, Object> params = new HashMap<>();
		params.put("objectType", ContentObject.OBJECT_TYPE_MESSAGE);
		params.put("docId", doc.getId());

		List<Integer> types = new ArrayList<>();
		if ( mustHaveWorking )
			types.add(ContentObject.DATA_TYPE_WORKING);
		else
		{
			types.add(ContentObject.DATA_TYPE_WORKING);
			types.add(ContentObject.DATA_TYPE_ACTIVE);
		}
		params.put("dataTypes", types);

		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	public static List<ContentObject> findAllContentObjectsByDocument( Document doc, boolean activeOnly )
	{
		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT co ");
		query.append("FROM            ContentObjectData as coid ");
		query.append("INNER JOIN      coid.contentObject as co ");
		query.append("WHERE           coid.dataType IN (:dataTypes) ");
		query.append("  AND co.document.id = :docId ");
		query.append("  AND co.objectType < :objectType ");
		query.append("  AND co.removed = false ");

		Map<String, Object> params = new HashMap<>();
		params.put("objectType", ContentObject.OBJECT_TYPE_ANY_LOCAL);
		params.put("docId", doc.getId());

		List<Integer> types = new ArrayList<>();
		types.add(ContentObject.DATA_TYPE_ACTIVE);
		if ( !activeOnly )
			types.add(ContentObject.DATA_TYPE_WORKING);
		params.put("dataTypes", types);

		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	public static List<ContentObject> findByGlobalParentObject(ContentObject parentObject, Document document){
        StringBuilder query = new StringBuilder("	SELECT DISTINCT		co ");
        query.append("						   	FROM 				ContentObject as co ");
        query.append("							WHERE 				co.globalParentObject.id = ").append(parentObject.getId());
        if (document != null) {
            query.append("						AND				    co.document.id = ").append(document.getId());
        }
        List<ContentObject> orderedList = (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString());
        return orderedList;
	}

	public static ContentObject findByGlobalParentObject(ContentObject parentObject, Document document, TouchpointSelection owningTouchpointSelection){
		ContentObject result = null;

		if (document != null){
			StringBuilder query = new StringBuilder("	SELECT DISTINCT		co ");
			query.append("						   	FROM 				ContentObject as co ");
			query.append("							WHERE				co.document.id = :documentId ");
			query.append("							AND 				co.globalParentObject.id = :parentObjectId ");
			if(owningTouchpointSelection == null || owningTouchpointSelection.isMaster()){
				query.append("						AND 				co.owningTouchpointSelection IS NULL ");
			}else{
				query.append("						AND 				co.owningTouchpointSelection.id = :owningTouchpointSelectionId ");
			}

			Map<String, Object> params = new HashMap<>();
			params.put("documentId", document.getId());
			params.put("parentObjectId", parentObject.getId());
			if(owningTouchpointSelection != null && !owningTouchpointSelection.isMaster()){
				params.put("owningTouchpointSelectionId", owningTouchpointSelection.getId());
			}

			List<ContentObject> orderedList = (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
			if (orderedList != null && !orderedList.isEmpty())
				result = orderedList.get(0);
		}

		return result;
	}

	public JobMessage createJobMessage(long jobId)
	{
		JobMessage item = new JobMessage();

		item.setJobId(jobId);
		item.setMessageId(getId());
		item.setName(getName());

		TenantMetaData tmd = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, getId());
		if (tmd != null)
			item.setMessageExternalId(tmd.getExternalId());

		return item;
	}

	public boolean isMandatoryType() {
		// It includes also 0 of local smart texts in a placeholder
		return this.getDeliveryType() <= ContentObject.DELIVERY_TYPE_MANDATORY ? true : false;
	}

	@SuppressWarnings("unchecked")
	public static boolean isTreeNodeNameUnique(ContentObject contentObject, long currentNodeId, long parentNodeId, String name) {

		// Node is master
		if ( parentNodeId == -1 )
			return true;

		String query =
				"SELECT 		m " +
						"FROM 			ContentObject AS m " +
						"INNER JOIN 	m.contentObjectAssociations AS mca " +
						"INNER JOIN 	mca.contentObjectPGTreeNode AS pgtn " +
						"LEFT JOIN 		pgtn.parentNode AS pn " +
						"WHERE 			m.id = :contentObjectId " +
						"AND 			LOWER(pgtn.name) = :name " +
						(parentNodeId == ParameterGroupTreeNode.MASTER_VARIANCE_ID ?
								"AND			pgtn.parentNode IS NULL " :
								"AND			pn.id = :parentTreeNodeId ") +
						( (currentNodeId != -1) ?
								"AND 			pgtn.id != :currentNodeId" : "" );

		Map<String, Object> params = new HashMap<>();
		params.put("name"				, name.toLowerCase());
		params.put("contentObjectId"	, contentObject.getId());
		if ( parentNodeId != ParameterGroupTreeNode.MASTER_VARIANCE_ID )
			params.put("parentTreeNodeId", parentNodeId);
		if ( currentNodeId != -1 )
			params.put("currentNodeId", currentNodeId);

		List<ContentObject> messages = (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query, params);
		if ( messages != null && !messages.isEmpty())
			return false;
		else
			return true;

	}

	public static List<SearchResultVO> searchContent(String searchValueEntered, boolean isAdvancedSearch, long documentId, long msgInstanceId, String searchType) {
		// TODO implement to work also for Global Smart Texts
		// searchType
		// SEARCH_TYPE_MESSAGE				= "message";
		// SEARCH_TYPE_EMBEDDED_TEXT		= "embeddedText";
		// SEARCH_TYPE_SMART_CONTENT		= "smartContent";

		String searchValue = searchValueEntered.toLowerCase();
		List<SearchResultVO> results = new ArrayList<>();

		// Please check Message in previous version

		return results;
	}

	public static List<SearchResultVO> searchContent(String searchValueEntered, boolean isAdvancedSearch, long documentId, String searchType)
	{
		// TODO implement to work also for Global Smart Texts
		// searchType
		// SEARCH_TYPE_MESSAGE				= "message";
		// SEARCH_TYPE_EMBEDDED_TEXT		= "embeddedText";
		// SEARCH_TYPE_SMART_CONTENT		= "smartContent";

		List<SearchResultVO> results = new ArrayList<>();

		if (searchValueEntered == null || searchValueEntered.isEmpty())
			return results;

		boolean smartContentSearch = searchType.equalsIgnoreCase(SEARCH_TYPE_SMART_CONTENT);

		Document document = Document.findById(documentId);
		long masterPGTNId = -1;
		TouchpointSelection masterTouchpointSelection = document.getMasterTouchpointSelection();
		if (masterTouchpointSelection != null)
			masterPGTNId = masterTouchpointSelection.getParameterGroupTreeNode().getId();

		// org.hibernate.dialect.Oracle10gDialect
		// org.hibernate.dialect.SQLServerDialect
		// expect always org.hibernate.dialect.PostgreSQL10Dialect

		String searchValue = searchValueEntered.toLowerCase();
		String searchValueSQL = searchValue.replace("[", "[[]").replace("%", "[%]").replace("_", "[_]").replace("'", "''");

		if (isAdvancedSearch)
			searchValueSQL = searchValueSQL.replace("&lt;", "<").replace("&gt;", ">");

		String query = 	"SELECT mvm.message_id, mvm.message_instance_id, mvm.status_id, ca.messagepoint_locale_id, ca.message_instance_id as reference";
		query +=   		", ca.message_id as structured, ca.pg_tn_id, ts.pg_tree_node_id";

		query +=		" FROM message_version_map mvm " +
				"INNER JOIN message m ON mvm.message_id = m.id AND m.status_id NOT IN (3,4) ";

		query +=		"INNER JOIN message_instance mi ON mvm.message_instance_id = mi.id " +
				"LEFT JOIN touchpoint_selection ts ON mi.tp_selection_id = ts.id ";

		if ( !smartContentSearch) {
			query +=	"INNER JOIN message_zone mz ON mvm.message_instance_id = mz.message_instance_id " +
					"INNER JOIN zone z ON mz.zone_id = z.id AND z.enabled = true ";

			if ( documentId > 0 )
				query +=   	"AND z.document_id = " + documentId + " ";

			query +=		"INNER JOIN workgroup_zone wz ON wz.zone_id = z.id AND wz.workgroup_id = " + UserUtil.getPrincipalUser().getWorkgroupId() + " ";
		}

		query +=		"INNER JOIN content_association ca ON ca.type_id = 2 AND (ca.message_instance_id = mvm.message_instance_id OR ca.message_id = mvm.message_id) AND (ca.pg_tn_id IS NULL OR mvm.status_id <> 2) " +
				"INNER JOIN content c ON c.id = ca.content_id " +
				"WHERE expiry_reason_id IS NULL " +
				"AND mvm.status_id NOT IN (3,4) ";

		if ( smartContentSearch ) {
			query +=	"AND m.is_touchpoint_local = 1 ";
			if ( documentId > 0 )
				query +="AND m.document_id = " + documentId + " ";
		}

		if (isAdvancedSearch)
		{
			query +=   		"AND c.text_content ~* '" + java.util.regex.Matcher.quoteReplacement(searchValueSQL) + "' ";
		}
		else
		{
			query +=   		"AND c.unformatted_text_content ~* '" + java.util.regex.Matcher.quoteReplacement(searchValueSQL) + "' ";
		}

		query +=		"GROUP BY mvm.message_id, mvm.message_instance_id, mvm.status_id, ca.messagepoint_locale_id, ca.message_instance_id";
		query +=		", ca.message_id, ca.pg_tn_id, ts.pg_tree_node_id ";

		/* Add PCA */

		query += 		"UNION (";
		query += 		"SELECT mvm.message_id, mvm.message_instance_id, mvm.status_id, ca.messagepoint_locale_id, ca.message_instance_id as reference";
		query +=   		", ca.message_id as structured, ca.pg_tn_id, ts.pg_tree_node_id ";

		query +=		"FROM message_version_map mvm ";

		query +=		"INNER JOIN message_instance mi ON mvm.message_instance_id = mi.id " +
				"LEFT JOIN touchpoint_selection ts ON mi.tp_selection_id = ts.id ";

		if ( !smartContentSearch) {
			query +=	"INNER JOIN message_zone mz ON mvm.message_instance_id = mz.message_instance_id " +
					"INNER JOIN zone z ON mz.zone_id = z.id AND z.enabled = true ";

			if ( documentId > 0 )
				query +=   	"AND z.document_id = " + documentId + " ";

			query +=		"INNER JOIN workgroup_zone wz ON wz.zone_id = z.id AND wz.workgroup_id = " + UserUtil.getPrincipalUser().getWorkgroupId() + " ";
		}

		query +=		"INNER JOIN prod_content_association ca ON ca.type_id = 2 AND ca.message_id = mvm.message_id AND ca.production_date = (SELECT MAX(production_date) FROM prod_content_association pca WHERE pca.message_id = mvm.message_id AND pca.pg_tn_id = ca.pg_tn_id) " +
				"INNER JOIN content c ON c.id = ca.content_id " +
				"WHERE expiry_reason_id IS NULL " +
				"AND mvm.status_id = 2 ";

		if (isAdvancedSearch)
		{
			query +=   		"AND c.text_content ~* '" + java.util.regex.Matcher.quoteReplacement(searchValueSQL) + "' ";
		}

		else
		{
			query +=   		"AND c.unformatted_text_content ~* '" + java.util.regex.Matcher.quoteReplacement(searchValueSQL) + "' ";
		}

		query +=		"GROUP BY mvm.message_id, mvm.message_instance_id, mvm.status_id, ca.messagepoint_locale_id, ca.message_instance_id";
		query +=		", ca.message_id, ca.pg_tn_id, ts.pg_tree_node_id";

		if (isAdvancedSearch)
		{
			query += 	") UNION (";
			query += 	"SELECT mvm.message_id, mvm.message_instance_id, mvm.status_id, ca.messagepoint_locale_id, ca.message_instance_id as reference";
			query +=   	", ca.message_id as structured, ca.pg_tn_id, ts.pg_tree_node_id";

			query +=		" FROM message_version_map mvm " +
					"INNER JOIN message m ON mvm.message_id = m.id AND m.status_id NOT IN (3,4) ";

			query +=		"INNER JOIN message_instance mi ON mvm.message_instance_id = mi.id " +
					"LEFT JOIN touchpoint_selection ts ON mi.tp_selection_id = ts.id ";

			if ( !smartContentSearch) {
				query +=	"INNER JOIN message_zone mz ON mvm.message_instance_id = mz.message_instance_id " +
						"INNER JOIN zone z ON mz.zone_id = z.id AND z.enabled = true ";

				if ( documentId > 0 )
					query += "AND z.document_id = " + documentId + " ";

				query +=	"INNER JOIN workgroup_zone wz ON wz.zone_id = z.id AND wz.workgroup_id = " + UserUtil.getPrincipalUser().getWorkgroupId() + " ";
			}

			query +=		"INNER JOIN content_association ca ON ca.type_id = 2 AND (ca.message_instance_id = mvm.message_instance_id OR ca.message_id = mvm.message_id) AND (ca.pg_tn_id IS NULL OR mvm.status_id <> 2) " +
					"INNER JOIN content c ON c.id = ca.content_id " +
					", (select upper(identifier) identifier from text_style where upper(name) like :search union select upper(identifier) identifier from paragraph_style where upper(name) like :search) style " +
					"WHERE expiry_reason_id IS NULL " +
					"AND mvm.status_id NOT IN (3,4) ";

			if ( smartContentSearch ) {
				query +=	"AND m.is_touchpoint_local = 1 ";
				if ( documentId > 0 )
					query +="AND m.document_id = " + documentId + " ";
			}

			query += "AND c.text_content LIKE '%' + style.identifier + '%' ";

			query +=	"AND upper(style.identifier) in (select upper(identifier) identifier from text_style where upper(name) like :search union select upper(identifier) identifier from paragraph_style where upper(name) like :search) " +
					"GROUP BY mvm.message_id, mvm.message_instance_id, mvm.status_id, ca.messagepoint_locale_id, ca.message_instance_id";
			query +=	", ca.message_id, ca.pg_tn_id, ts.pg_tree_node_id";

			/* Add PCA */

			query += 		") UNION (";
			query += 		"SELECT mvm.message_id, mvm.message_instance_id, mvm.status_id, ca.messagepoint_locale_id, ca.message_instance_id as reference";
			query +=   		", ca.message_id as structured, ca.pg_tn_id, ts.pg_tree_node_id";

			query +=		" FROM message_version_map mvm ";

			query +=		"INNER JOIN message_instance mi ON mvm.message_instance_id = mi.id " +
					"LEFT JOIN touchpoint_selection ts ON mi.tp_selection_id = ts.id ";

			if ( !smartContentSearch) {
				query +=	"INNER JOIN message_zone mz ON mvm.message_instance_id = mz.message_instance_id " +
						"INNER JOIN zone z ON mz.zone_id = z.id AND z.enabled = true ";

				if ( documentId > 0 )
					query += "AND z.document_id = " + documentId + " ";

				query +=	"INNER JOIN workgroup_zone wz ON wz.zone_id = z.id AND wz.workgroup_id = " + UserUtil.getPrincipalUser().getWorkgroupId() + " ";
			}

			query +=		"INNER JOIN prod_content_association ca ON ca.type_id = 2 AND ca.message_id = mvm.message_id AND ca.production_date = (SELECT MAX(production_date) FROM prod_content_association pca WHERE pca.message_id = mvm.message_id AND pca.pg_tn_id = ca.pg_tn_id) " +
					"INNER JOIN content c ON c.id = ca.content_id " +
					", (select upper(identifier) identifier from text_style where upper(name) like :search union select upper(identifier) identifier from paragraph_style where upper(name) like :search) style " +
					"WHERE expiry_reason_id IS NULL " +
					"AND mvm.status_id = 2 ";

			if ( smartContentSearch ) {
				query +=	"AND m.is_touchpoint_local = 1 ";
				if ( documentId > 0 )
					query +="AND m.document_id = " + documentId + " ";
			}

			query += "AND c.text_content LIKE '%' + style.identifier + '%' ";

			query +=	"AND upper(style.identifier) in (select upper(identifier) from text_style where upper(name) like :search union select upper(identifier) from paragraph_style where upper(name) like :search) " +
					"GROUP BY mvm.message_id, mvm.message_instance_id, mvm.status_id, ca.messagepoint_locale_id, ca.message_instance_id";
			query +=	", ca.message_id, ca.pg_tn_id, ts.pg_tree_node_id";
		}

		query +=		") ORDER BY message_id, message_instance_id";

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);

		if (isAdvancedSearch)
			sqlQuery.setParameter("search", "%" + java.util.regex.Matcher.quoteReplacement(searchValueEntered.toUpperCase()) + "%");

		@SuppressWarnings("unchecked")
		List<Object> objects = sqlQuery.list();

		long currentMessageId = -1;
		long currentMessageInstanceId = -1;
		long currentStatusId = -1;
		SearchResultVO searchResultVO = null;
		Set<MessagepointLocale> locales = new HashSet<>();
		boolean localReference = false;

		for(Object obj : objects){
			Object[] idResult = (Object[])obj;
			long contentObjectId = ((BigInteger)idResult[0]).longValue();
			long msgInstanceId = ((BigInteger)idResult[1]).longValue();
			long statusId = ((BigInteger)idResult[2]).longValue();
			long messagepointLocaleId = ((BigInteger)idResult[3]).longValue();
			boolean reference = false;
			if (((BigInteger)idResult[4]) != null)
				reference = true;

			boolean structured = false;
			long parameterGroupTreeNodeId = 0;
			long owningParameterGroupTreeNodeId = 0;
			if (((BigInteger)idResult[5]) != null && reference)
				structured = true;
			if (((BigInteger)idResult[6]) != null)
				parameterGroupTreeNodeId = ((BigInteger)idResult[6]).longValue();
			if (((BigInteger)idResult[7]) != null)
				owningParameterGroupTreeNodeId = ((BigInteger)idResult[7]).longValue();

			if (currentMessageInstanceId != msgInstanceId)
			{
				if (currentMessageInstanceId > 0)
				{
					// TODO MessageInstance msgInstance = MessageInstance.findById(currentMessageInstanceId);
					if (currentStatusId == VersionStatus.VERSION_PRODUCTION)
					{
						// TODO searchResultVO.setActiveCopyContentObject(msgInstance);
						searchResultVO.setActiveCopyLocalReference(localReference);
						if (localReference)
						{
                            List<MessagepointLocale> mlSortedList = new ArrayList<>(locales);
							Collections.sort(mlSortedList, new MessagepointLocaleComparatorOnName());
							searchResultVO.setActiveCopyLocales(mlSortedList);
						}
					}
					else
					{
						// TODO searchResultVO.setWorkingCopyContentObject(msgInstance);
						searchResultVO.setWorkingCopyLocalReference(localReference);
						if (localReference)
						{
                            List<MessagepointLocale> mlSortedList = new ArrayList<>(locales);
							Collections.sort(mlSortedList, new MessagepointLocaleComparatorOnName());
							searchResultVO.setWorkingCopyLocales(mlSortedList);
						}
					}

					locales.clear();
					localReference = false;
				}

				currentMessageInstanceId = msgInstanceId;
				currentStatusId = statusId;
			}

			if (contentObjectId != currentMessageId)
			{
				searchResultVO = new SearchResultVO(contentObjectId);
				currentMessageId = contentObjectId;
				results.add(searchResultVO);
			}

			locales.add(MessagepointLocale.getLanguageLocaleByLocaleId(messagepointLocaleId));
			if (structured)
			{
				if (parameterGroupTreeNodeId == masterPGTNId || parameterGroupTreeNodeId == owningParameterGroupTreeNodeId)
					localReference = true;
			}
			else
			{
				if (reference)
					localReference = true;
			}
		}

		if (currentMessageInstanceId > 0)
		{
			// TODO MessageInstance msgInstance = MessageInstance.findById(currentMessageInstanceId);
			if (currentStatusId == VersionStatus.VERSION_PRODUCTION)
			{
				// TODO searchResultVO.setActiveCopyContentObject(msgInstance);
				searchResultVO.setActiveCopyLocalReference(localReference);
				if (localReference)
				{
                    List<MessagepointLocale> mlSortedList = new ArrayList<>(locales);
					Collections.sort(mlSortedList, new MessagepointLocaleComparatorOnName());
					searchResultVO.setActiveCopyLocales(mlSortedList);
				}
			}
			else
			{
				// TODO searchResultVO.setWorkingCopyContentObject(msgInstance);
				searchResultVO.setWorkingCopyLocalReference(localReference);
				if (localReference)
				{
                    List<MessagepointLocale> mlSortedList = new ArrayList<>(locales);
					Collections.sort(mlSortedList, new MessagepointLocaleComparatorOnName());
					searchResultVO.setWorkingCopyLocales(mlSortedList);
				}
			}
		}

		return results;
	}

	public boolean hasDirtyContentForDefaultLanguage() {
		MessagepointLocale defaultLocale = MessagepointLocale.getDefaultSystemLanguageLocale();
		if (this.isMessage() || this.isTouchpointLocal()) {
			defaultLocale = this.getDocument().getDefaultTouchpointLanguageLocale();
		}
		return hasDirtyContentForLanguage(defaultLocale.getLanguageId());
	}

    public boolean hasDirtyContentForLanguage(Long languageId) {
        if(languageId == null || languageId <= 0){
            MessagepointLocale defaultLocale = MessagepointLocale.getDefaultSystemLanguageLocale();
            if (this.isMessage() || this.isTouchpointLocal()) {
                defaultLocale = this.getDocument().getDefaultTouchpointLanguageLocale();
            }
            languageId = defaultLocale.getLanguageId();
        }

        Set<ContentObjectAssociation> coas = this.getContentObjectAssociations().stream().filter(coa -> coa.getDataType() == ContentObject.DATA_TYPE_WORKING).collect(Collectors.toSet());
        for (ContentObjectAssociation coa : coas) {
            if (coa.getMessagepointLocale() != null) {
                Content content = coa.getContent();
                if (languageId.equals(coa.getMessagepointLocale().getLanguageId()) && content != null && content.isDirty()) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean skipNextWorkflowStep(ConfigurableWorkflowStep nextStep) {
        boolean skipNextStep = false;
        if (nextStep.isTranslationStep() || nextStep.isTranslationApprovalStep()) {
            List<MessagepointLocale> languages = new ArrayList<>(nextStep.getLanguages());
            // If only default language selected and it is not allow to edit the default language, skip it
            if (!nextStep.isAllowEditDefaultLanguage()) {
                MessagepointLocale defaultLocale = MessagepointLocale.getDefaultSystemLanguageLocale();
                long defaultLocaleId = defaultLocale.getId();

                if (this.isMessage() || this.isTouchpointLocal()) {
                    defaultLocaleId = this.getDocument().getDefaultTouchpointLanguageLocale().getId();
                }

                if (languages.size() == 1 && languages.get(0).getId() == defaultLocaleId) {
                    skipNextStep = true;
                }
            }

            boolean hasNoDirtyContentForDefaultLanguage = skipNextStep || !(this.hasDirtyContentForDefaultLanguage());

            List<Task> tasks = Task.findAllTasks(this, TaskType.ID_TRANSLATE, -1, -1);
            boolean hasOutstandingTaskForStepLanguage = tasks.stream()
                    .anyMatch(task -> !task.isComplete()
                            && task.getMessagepointLocale() != null
                            && !languages.isEmpty()
                            && languages.stream().map(MessagepointLocale::getId).toList().contains(task.getMessagepointLocale().getId()));

            skipNextStep = hasNoDirtyContentForDefaultLanguage && !hasOutstandingTaskForStepLanguage;
        }
        return skipNextStep;
    }

	/**
	 * Find all content object which is in the intermediate approval steps (Workflow action is not null and message instance is not active)
     */
	public static List<ContentObject> findAllInIntermediateApprovals(){
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("readyForApproval", true));
		critList.add(MessagepointRestrictions.ne("state.id", WorkflowState.STATE_PRODUCTION));
		return HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
	}

	public List<MessagepointLocale> getSystemDefaultAndAllTouchpointsLanguagesAsLocales()
	{
		List<TouchpointLanguage> allTouchpointLanguages = new ArrayList<>();

		Set<Document> documents = new HashSet<>();
		if ( !this.isVariableContentEnabled() ) {
			List<Document> docs = Document.findAllDocumentsAndProjects();
            documents.addAll(docs);
		} else {
			documents = this.getDocuments();
		}

		for (Document document : documents )
		{
			allTouchpointLanguages.addAll(document.getTouchpointLanguages());
		}

		MessagepointLocale systemLanguage = MessagepointLocale.getDefaultSystemLanguageLocale();

		Map<String, MessagepointLocale> messagepointLocaleMap = new LinkedHashMap<>();
		messagepointLocaleMap.put(systemLanguage.getLanguageCode(), systemLanguage);

		for (TouchpointLanguage touchpointLanguage : allTouchpointLanguages)
		{
			if(!messagepointLocaleMap.containsKey(touchpointLanguage.getLanguageCode()) && !systemLanguage.getLanguageCode().equalsIgnoreCase(touchpointLanguage.getLanguageCode()))
				messagepointLocaleMap.put(touchpointLanguage.getLanguageCode(), touchpointLanguage.getMessagepointLocale());
		}

        return new ArrayList<>(messagepointLocaleMap.values());
	}

	@SuppressWarnings("unchecked")
	public static Set<ContentObject> findByDocsAndLibraryItemType( Set<Document> docs, int usageType, boolean imageLibrary )
	{
		Set<ContentObject> allItems = new HashSet<>();

		// TODO

		for( Document doc : docs )
		{
			Map<String, Object> vars = new HashMap<>();
			StringBuilder query = new StringBuilder();
			query.append("SELECT cl FROM ContentObjectData cli ");
			query.append("INNER JOIN cli.contentObject as cl ");
			query.append("INNER JOIN cl.visibleDocuments AS docs ");
			query.append("WHERE cl.usageTypeId = :usageType ");
			if ( imageLibrary )
				query.append("AND cl.objectType in ( " + ContentObject.OBJECT_TYPE_LOCAL_IMAGE + "," + ContentObject.OBJECT_TYPE_GLOBAL_IMAGE + ") " );
			else
				query.append("AND cl.objectType in ( " + ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT + "," + ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT + ") ");
			query.append("AND :doc IN docs ");
			query.append("AND cli.parameterGroup is not null");

			vars.put("doc", doc);
			vars.put("usageType",  usageType);

			List<ContentObject> items = (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), vars);
			allItems.addAll(items);
		}
		return allItems;
	}

	public static List<ContentObject> findAllOrphanedGlobalAssets() {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.isEmpty("visibleDocuments"));
		critList.add(MessagepointRestrictions.ne("variableContentEnabled", false));
		critList.add(MessagepointRestrictions.or(MessagepointRestrictions.eq("objectType", ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT), MessagepointRestrictions.eq("objectType", ContentObject.OBJECT_TYPE_GLOBAL_IMAGE)));
		return HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
	}

	public static boolean hasAssetsInTrash(){
		Document trashTouchoint = Document .findTrashTp();
		if(trashTouchoint != null){
			String query = 	"SELECT DISTINCT codm.content_object_id " +
					"FROM content_object_document_map codm " +
					"WHERE codm.document_id = :documentId";

			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
			sqlQuery.setParameter("documentId", trashTouchoint.getId());
			List ids = sqlQuery.list();
			return !ids.isEmpty();
		}else{
			return false;
		}
	}

    @SuppressWarnings("unchecked")
    public static List<ContentObject> findNonExpiredWithMetadataForm(int objectType) {
        String query = " SELECT co from ContentObject co " +
                " INNER JOIN co.contentObjectDataTypeMap cod " +
                " WHERE co.objectType = " + objectType + " AND co.removed = false AND cod.metadataForm IS NOT NULL";
        return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query);
    }

	public static long countUntimedActiveMessageForTouchpoint( Document d )
	{
		String query =
				"SELECT COUNT(distinct(co.id)) " +
						"FROM content_object co " +
						"INNER JOIN zone z on z.id = co.zone_id " +
						"INNER JOIN content_object_data cod on cod.content_object_id = co.id " +
						"WHERE co.removed = false and co.suppressed = false and cod.start_date is null and cod.end_date is null " +
						"and z.enabled = true and cod.data_type = :active and z.zone_type_id != :placeholder and z.document_id = :docId";

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		sqlQuery.setParameter("active", ContentObject.DATA_TYPE_ACTIVE);
		sqlQuery.setParameter("placeholder", ZoneType.ID_PLACEHOLDER_ZONE);
		sqlQuery.setParameter("docId", d.getId());

		List objs = sqlQuery.list();
		for( Object obj : objs )
		{
			long count = ((BigInteger)(obj)).longValue();
			return count;
		}
		return 0;
	}

	public DocumentPreview getLatestPreview() {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("contentObject.id", this.getId()));
		critList.add(MessagepointRestrictions.isNotNull("outputPath"));
		critList.add(MessagepointRestrictions.isNull("parameterGroupTreeNode"));
		List<DocumentPreview> list = HibernateUtil.getManager().getObjectsAdvanced(DocumentPreview.class, critList, MessagepointOrder.desc("requestDate"));
		if (!list.isEmpty()) {
			return list.iterator().next();
		} else {
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	public DocumentPreview getLatestPreview(MessagepointLocale locale) {
		Map<String, Object> params = new HashMap<>();
		params.put("contentObjectId", this.getId());
		params.put("languageCode", locale.getLanguageCode());

		StringBuilder query = new StringBuilder("	SELECT 			dp ");
		query.append("							FROM 			DocumentPreview dp ");
		query.append("							WHERE			dp.contentObject.id = :contentObjectId ");
		query.append("							AND				dp.parameterGroupTreeNode IS NULL ");
		query.append("							AND				dp.messagepointLocale.languageCode = :languageCode ");
		query.append("							ORDER BY requestDate DESC ");

		List<DocumentPreview> list = (List<DocumentPreview>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
		if (!list.isEmpty()) {
			return list.iterator().next();
		} else {
			return null;
		}
	}

	public List<DocumentPreview> getAllPreview() {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("contentObject.id", this.getId()));
		critList.add(MessagepointRestrictions.isNull("parameterGroupTreeNode"));
		return HibernateUtil.getManager().getObjectsAdvanced(DocumentPreview.class, critList, MessagepointOrder.desc("requestDate"));
	}

	@SuppressWarnings("unchecked")
	public List<DocumentPreview> getAllPreview(MessagepointLocale locale) {
		Map<String, Object> params = new HashMap<>();
		params.put("contentObjectId", this.getId());
		params.put("languageCode", locale.getLanguageCode());

		StringBuilder query = new StringBuilder("	SELECT 			dp ");
		query.append("							FROM 			DocumentPreview dp ");
		query.append("							WHERE			dp.contentObject.id = :contentObjectId ");
		query.append("							AND				dp.parameterGroupTreeNode IS NULL ");
		query.append("							AND				dp.messagepointLocale.languageCode = :languageCode ");
		query.append("							ORDER BY requestDate DESC ");

		return (List<DocumentPreview>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	static public List<ContentObject> findAllStructuredMessagesByNode( int dataType, TouchpointSelection tps )
	{
		Map<String, Object> params = new HashMap<>();

		params.put("documentId", tps.getDocument().getId());

		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT co ");
		query.append("FROM ContentObjectData cod ");
		query.append("INNER JOIN cod.contentObject co ");
		query.append("LEFT OUTER JOIN co.owningTouchpointSelection tps ");
		query.append(" WHERE co.document.id = :documentId " );
		if ( tps.getParent() != null )
		{
			query.append(" AND tps.id = :tpsId " );
			params.put("tpsId", tps.getId());
		}
		else
		{
			query.append(" AND tps.id is null " );
		}
		query.append(" AND co.structuredContentEnabled = true AND BITAND(cod.dataType, :dataType) != 0 " );

		params.put("dataType", dataType);

		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	static public List<ContentObject> findAllStructuredContentObjectsByTPVariantIncludingParents( int dataType, TouchpointSelection tps )
	{
		Map<String, Object> params = new HashMap<>();

		params.put("documentId", tps.getDocument().getId());

		Set<Long> tpSelectionIds = new HashSet<>();
		TouchpointSelection parentSelection = tps;
		while (parentSelection != null) {
			tpSelectionIds.add(parentSelection.getId());
			parentSelection = parentSelection.getParent();
		}

		if (tpSelectionIds.isEmpty())
			tpSelectionIds.add((long) 0);

		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT co ");
		query.append("FROM ContentObjectData cod ");
		query.append("INNER JOIN cod.contentObject co ");
		query.append("LEFT OUTER JOIN co.owningTouchpointSelection tps ");
		query.append(" WHERE co.document.id = :documentId " );
		if ( tps.getParent() != null )
		{
			query.append(" AND ( tps.id is null OR tps.id in (:tpsIds) ) " );
			params.put("tpsIds", tpSelectionIds);
		}
		else
		{
			query.append(" AND tps.id is null " );
		}
		query.append(" AND co.structuredContentEnabled = true AND BITAND(cod.dataType, :dataType) != 0 " );

		params.put("dataType", dataType);

		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	static public List<ContentObject> findAllNotStructuredContentObjectsByTPVariantIncludingParents( int dataType, TouchpointSelection tps )
	{
		Map<String, Object> params = new HashMap<>();

		params.put("documentId", tps.getDocument().getId());

		Set<Long> tpSelectionIds = new HashSet<>();
		TouchpointSelection parentSelection = tps;
		while (parentSelection != null) {
			tpSelectionIds.add(parentSelection.getId());
			parentSelection = parentSelection.getParent();
		}

		if (tpSelectionIds.isEmpty())
			tpSelectionIds.add((long) 0);

		StringBuilder query = new StringBuilder();
		query.append("SELECT DISTINCT co ");
		query.append("FROM ContentObjectData cod ");
		query.append("INNER JOIN cod.contentObject co ");
		query.append("LEFT OUTER JOIN co.owningTouchpointSelection tps ");
		query.append(" WHERE co.document.id = :documentId " );
		if ( tps.getParent() != null )
		{
			query.append(" AND ( tps.id is null OR tps.id in (:tpsIds) ) " );
			params.put("tpsIds", tpSelectionIds);
		}
		else
		{
			query.append(" AND tps.id is null " );
		}
		query.append(" AND co.structuredContentEnabled = false AND BITAND(cod.dataType, :dataType) != 0 " );

		params.put("dataType", dataType);

		return (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
	}

	public ContentObjectSlice makeSlice( int dataTypePref )
	{
		if ( getContentObjectData(dataTypePref) != null )
		{
			return new ContentObjectSlice( this, getContentObjectData(dataTypePref) );
		}

		switch ( dataTypePref )
		{
			case ContentObject.DATA_TYPE_WORKING:
			{
				return makeSlice(ContentObject.DATA_TYPE_ACTIVE);
			}
			case ContentObject.DATA_TYPE_ACTIVE:
			{
				return null;
			}
			case ContentObject.DATA_TYPE_WORKING_AND_ACTIVE:
			{
				ContentObjectSlice s = makeSlice(ContentObject.DATA_TYPE_WORKING);
				if ( s == null )
					s = makeSlice(ContentObject.DATA_TYPE_ACTIVE);
				return s;
			}
		}
		return null;

	}

}
