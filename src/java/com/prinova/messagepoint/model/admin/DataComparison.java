package com.prinova.messagepoint.model.admin;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.DescribableMessagePointModel;
import com.prinova.messagepoint.util.HibernateUtil;

public class DataComparison extends DescribableMessagePointModel {
	private static final long serialVersionUID = 4867542528157481439L;
	public static final long COMPARISON_EQUALS = 1;
	public static final long COMPARISON_NOTEQUAL = 2;
	public static final long COMPARISON_CONTAINS = 3;
	public static final long COMPARISON_ISONEOF = 4;
	public static final long COMPARISON_STARTSWITH = 5;
	public static final long COMPARISON_ENDSWITH = 6;
	public static final long COMPARISON_EMPTYORNULL = 7;
	public static final long COMPARISON_GREATERTHAN = 8;
	public static final long COMPARISON_GREATERTHANOREQUALTO = 9;
	public static final long COMPARISON_LESSTHAN = 10;
	public static final long COMPARISON_LESSTHANOREQUALTO = 11;
	public static final long COMPARISON_OCCURSBEFORE = 12;
	public static final long COMPARISON_OCCURSONORBEFORE = 13;
	public static final long COMPARISON_OCCURSAFTER = 14;
	public static final long COMPARISON_OCCURSONORAFTER = 15;
	public static final long COMPARISON_NOTEMPTYORNULL = 16;
	public static final long COMPARISON_ISNOTONEOF = 18;
	
	private String scheme;
	
	public String getScheme() {
		return scheme;
	}
	public void setScheme(String scheme) {
		this.scheme = scheme;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + (int) (id ^ (id >>> 32));
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!(obj instanceof DataComparison)) return false;
		final DataComparison other = (DataComparison)obj;
		if (id != other.getId())
			return false;
		return true;
	}

	public String toString() {
		return getName();
	}
	
	public static List<DataComparison> getNumericComparisonTypes() {
		List<DataComparison> types = new ArrayList<>();
		
		types.add( HibernateUtil.getManager().getObject(DataComparison.class, COMPARISON_GREATERTHAN) );
		types.add( HibernateUtil.getManager().getObject(DataComparison.class, COMPARISON_GREATERTHANOREQUALTO) );
		
		types.add( HibernateUtil.getManager().getObject(DataComparison.class, COMPARISON_LESSTHAN) );
		types.add( HibernateUtil.getManager().getObject(DataComparison.class, COMPARISON_LESSTHANOREQUALTO) );
		
		types.add( HibernateUtil.getManager().getObject(DataComparison.class, COMPARISON_EQUALS) );
		types.add( HibernateUtil.getManager().getObject(DataComparison.class, COMPARISON_NOTEQUAL) );
		
		return types;
	}
}
