package com.prinova.messagepoint.model;

import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.family.FamilyInheritanceModel;
import com.prinova.messagepoint.model.font.*;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.tagcloud.TagCloudType;
import com.prinova.messagepoint.model.util.ContentObjectUtil;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.tagcloud.UpdateTagCloudService;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.tag.view.DocumentTag;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.util.ApplicationUtil.getMessage;

public class Zone extends DocumentObjectModel implements Serializable, FamilyInheritanceModel {

	private static final long serialVersionUID = -7344328336862349893L;

	private static final Log log = LogUtil.getLog(Zone.class);

	public static final String ZONE_BACKGROUND_TRANSPARENT		= "transparent";
	public static final String ZONE_BACKGROUND_DEFAULT			= "FFFFFF";

    private long 					contentTypeId;
    private long 					subContentTypeId;
    private int 					zoneTypeId;
    private boolean 				enabled 			= true;
    private int 					topX;
	private int 					topY;
	private int 					width;
	private int 					height;
	private Integer					sequence;
	private Integer					defaultCanvasWidth;
	private int 					page;
	private int						rotationAngle		= 0;
	private String					backgroundColor		= ZONE_BACKGROUND_DEFAULT;
	private DocumentSection 		section;
	private String 					friendlyName;
	private Boolean					mixedDataGroups		= false;
	private DataGroup 				dataGroup;
    private TextStyle				defaultTextStyle;
    private TextStyle				starterTextStyle;
    private Set<TextStyle> 			styles				= new HashSet<>();
    private ParagraphStyle			defaultParagraphStyle;
    private Set<ParagraphStyle> 	paragraphStyles		= new HashSet<>();
    private ListStyle				defaultListStyle;
    private Set<ListStyle> 			listStyles			= new HashSet<>();
    private boolean					dxfOutput			= false;
    private boolean					htmlOutput			= false;
    private boolean					flowZone			= false;
    private Zone					flowIntoZone;
    private Integer					verticalAlignment	= VerticalAlignment.ID_TOP;
	private boolean					backer				= false;

    private boolean					supportsTables				= true;
    private boolean					supportsForms				= false;
    private boolean					supportsBarcodes			= false;
    private boolean					supportsCustomParagraphs 	= true;
    private boolean					freeform					= false;
	private boolean 				exportToSingleMessage 		= false;

    // Valid attributes include: "qualifiedEqualsPlayed",
    private Map<String, String> 	zoneAttributes 		= new HashMap<>();
    private Set<ZonePart> 			parts 				= new HashSet<>();
    private Set<Workgroup> 			workgroups 			= new HashSet<>();

    // following are native composition attributes
    private boolean					repeats;  // multi-part only
	private int						repeatingZoneTypeId	= 0;

    private boolean					canGrow;  // single part only
    private boolean					canFlow;  // applies to all zones
    private boolean					minimumSize; // is the defined height the minimum size
    private boolean					absolutePositioning	= false; // position independant of other zones
    private int						renderedContainerTypeId = ZoneRenderedContainerType.ID_RENDERED_CONTAINER_TYPE_NONE;
    private int						imageDimensionsTypeId = ZoneImageDimensionsType.ID_IMAGE_DIMENSION_TYPE_RELATIVE_WIDTH;
    private boolean					enforceMinimumHeight = true;
    private boolean					splitTables = false;

    private ContentObject			defaultCommunicationTemplateImage;
    private ContentObject			defaultCommunicationTemplateSmartText;
	
	private DataElementVariable		communicationDataImageVariable;

	public DataElementVariable getCommunicationDataImageVariable() {
		return communicationDataImageVariable;
	}

	public void setCommunicationDataImageVariable(DataElementVariable dataElementVariable) {
		this.communicationDataImageVariable = dataElementVariable;
	}

	private boolean					communicationContentEditing		= true;

    private boolean					restrictSharedAssets			= false;
    private Set<ContentObject>		imageAssets						= new HashSet<>();
    private Set<ContentObject>		smartTextAssets					= new HashSet<>();
    private boolean					messageContentEditing			= true;


    private boolean 				applyAltText;
    private boolean					applyImageLink;
    private boolean 				applyImageExtLink;
	private boolean 				applyImageExtPath;

    private boolean 				overrideName					= false;
    private boolean 				overrideFriendlyName			= false;
    private boolean 				overrideEnabled					= false;
    private boolean 				overridePosition				= false;
    private boolean 				overrideDimensions				= false;
    private boolean 				overrideDocumentSection			= false;
    private boolean 				overrideDefaultTextStyle		= false;
    private boolean 				overrideTextStyles				= false;
    private boolean 				overrideDefaultParagraphStyle	= false;
    private boolean 				overrideParagraphStyles			= false;
    private boolean 				overrideDefaultListStyle		= false;
    private boolean 				overrideListStyles				= false;
    private boolean 				overrideBackgroundColor			= false;
    private boolean 				overrideWorkgroups				= false;
    private boolean 				overrideSharedAssets			= false;
    private boolean 				overrideCommunicationTemplate	= false;

    private Integer 				verticallyRelativeTo        	= VerticallyRelativeTo.ID_NONE;
    private Integer 				relativeDistance        		= 250000;
    private String 				    relativeDistanceInInches        = "0.25";

    private Integer 				minimumKeepTogether        		= null;
    private String 				    minimumKeepTogetherInInches     = "";
	private boolean 				importRequest					= false;
	private long 					importParentId					= 0;

	private Set<ContentObject> 		contentObjects 					= new HashSet<>();

	private boolean                 needRecalculateHash 			= true;
	private String                  sha256Hash;


	// Public default constructor
	//
	public Zone()
	{
	}

	public void copyData(Zone cloneFrom, Timestamp checkoutTimestamp, DocumentSection clonedDocumentSection, Document clonedDocument, Zone primaryLayoutZone, boolean cloningProcess) {
        this.description = cloneFrom.description;
        this.contentTypeId = cloneFrom.contentTypeId;
        this.subContentTypeId = cloneFrom.subContentTypeId;
        this.zoneTypeId = cloneFrom.zoneTypeId;
        this.enabled = cloneFrom.enabled;
        this.topX = cloneFrom.topX;
        this.topY = cloneFrom.topY;
        this.width = cloneFrom.width;
        this.height = cloneFrom.height;
        this.sequence = cloneFrom.sequence;
        this.defaultCanvasWidth = cloneFrom.defaultCanvasWidth;
        this.page = cloneFrom.page;
        this.rotationAngle = cloneFrom.rotationAngle;
        this.backgroundColor = cloneFrom.backgroundColor;

        this.section = clonedDocumentSection;

        this.friendlyName = cloneFrom.friendlyName;

        this.mixedDataGroups = cloneFrom.mixedDataGroups;
        if (cloningProcess)
        	this.dataGroup = CloneHelper.assign(cloneFrom.dataGroup);

        this.checkoutTimestamp = checkoutTimestamp;

        this.defaultTextStyle = CloneHelper.assign(cloneFrom.defaultTextStyle);
        this.starterTextStyle = CloneHelper.assign(cloneFrom.starterTextStyle);
        this.styles.addAll(CloneHelper.assign(cloneFrom.styles));

        this.defaultParagraphStyle = CloneHelper.assign(cloneFrom.defaultParagraphStyle);
        this.paragraphStyles.addAll(CloneHelper.assign(cloneFrom.paragraphStyles));

        this.defaultListStyle = CloneHelper.assign(cloneFrom.defaultListStyle);
        this.listStyles.addAll(CloneHelper.assign(cloneFrom.listStyles));

        this.dxfOutput = cloneFrom.dxfOutput;
        this.htmlOutput = cloneFrom.htmlOutput;
        this.supportsTables = cloneFrom.supportsTables;
        this.supportsForms = cloneFrom.supportsForms;
        this.supportsBarcodes = cloneFrom.supportsBarcodes;
        this.supportsCustomParagraphs = cloneFrom.supportsCustomParagraphs;
        this.freeform = cloneFrom.freeform;
        this.exportToSingleMessage = cloneFrom.exportToSingleMessage;
        this.flowZone = cloneFrom.flowZone;

        if(this.getObjectSchemaName().equals(cloneFrom.getObjectSchemaName()) && this.getDocument().getId() == clonedDocument.getId()) {
            this.flowIntoZone = cloneFrom.flowIntoZone;
        } else {
            // Handled in document.clone
        }

        this.verticalAlignment = cloneFrom.verticalAlignment;
		this.backer = cloneFrom.backer;

        if (cloneFrom.zoneAttributes != null) {
            this.zoneAttributes.putAll(cloneFrom.zoneAttributes);
        }

        if (cloneFrom.parts != null) {
        	Set<String> zonePartDnasInCurrentZone = CloneHelper.queryInSaveSession(()->this.parts.stream().map(ZonePart::getDna).collect(Collectors.toSet()));
        	Set<ZonePart> sourceZonePartsNotInCurrentZone = cloneFrom.parts.stream().filter(zp->! zonePartDnasInCurrentZone.contains(zp.getDna())).collect(Collectors.toSet());
            this.parts.addAll(CloneHelper.clone(sourceZonePartsNotInCurrentZone, zp->{
            	ZonePart clonedZonePart = zp.clone(this, primaryLayoutZone);
            	clonedZonePart.save();
            	return clonedZonePart;
            }));
        }

        if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
            if (cloneFrom.workgroups != null)
            {
//                this.workgroups.addAll(cloneFrom.workgroups);
                cloneFrom.getWorkgroups().forEach(cloneFromWg -> {
                    if(! this.workgroups.stream().anyMatch(thisWg->thisWg.getId() == cloneFromWg.getId())) {
                        this.workgroups.add(cloneFromWg);
                    }
                });

                for (Workgroup workgroup : this.workgroups)
                {
                    if(! workgroup.getZones().stream().anyMatch(z -> z.getId() == this.getId())) {
                        workgroup.getZones().add(this);
                    }
                }
            }
        } else {
            User cloningUser = CloneHelper.getRequestor();

            Workgroup workgroup = CloneHelper.queryInSaveSession(()->{
                Workgroup w = cloningUser.getWorkgroup();
                if(w == null) {
                    w = CloneHelper.queryInSaveSession(()->Workgroup.getDefaultWorkgroupFromDB());
                }
                return w;
            });

            if(workgroup != null) {
            	CloneHelper.execInSaveSession(()->{
            		if(! this.workgroups.stream().anyMatch(wg->wg.getId()==workgroup.getId())) {
            			this.workgroups.add(workgroup);
            		}
            		if(! workgroup.getZones().stream().anyMatch(z->z.getId()==this.getId())) {
            			workgroup.getZones().add(this);
            		}
            	});
            }
        }

        this.repeats = cloneFrom.repeats;
        this.repeatingZoneTypeId = cloneFrom.repeatingZoneTypeId;
        this.canGrow = cloneFrom.canGrow;
        this.canFlow = cloneFrom.canFlow;
        this.minimumSize = cloneFrom.minimumSize;
        this.absolutePositioning = cloneFrom.absolutePositioning;
        this.renderedContainerTypeId = cloneFrom.renderedContainerTypeId;
        this.imageDimensionsTypeId = cloneFrom.imageDimensionsTypeId;
        this.enforceMinimumHeight = cloneFrom.enforceMinimumHeight;
        this.splitTables = cloneFrom.splitTables;

        this.defaultCommunicationTemplateImage = CloneHelper.assignAlreadyClonedObject(cloneFrom.defaultCommunicationTemplateImage);
        this.defaultCommunicationTemplateSmartText = CloneHelper.assignAlreadyClonedObject(cloneFrom.defaultCommunicationTemplateSmartText);
		this.communicationDataImageVariable = CloneHelper.assignAlreadyClonedObject(cloneFrom.communicationDataImageVariable);

        this.communicationContentEditing = cloneFrom.communicationContentEditing;

        this.restrictSharedAssets = cloneFrom.restrictSharedAssets;

        this.imageAssets.addAll(CloneHelper.assignAlreadyClonedObject(cloneFrom.imageAssets));
        this.smartTextAssets.addAll(CloneHelper.assignAlreadyClonedObject(cloneFrom.smartTextAssets));

        this.messageContentEditing = cloneFrom.messageContentEditing;

        this.applyAltText = cloneFrom.applyAltText;
        this.applyImageLink = cloneFrom.applyImageLink;
        this.applyImageExtLink = cloneFrom.applyImageExtLink;
		this.applyImageExtPath = cloneFrom.applyImageExtPath;

        this.overrideName = cloneFrom.overrideName;
        this.overrideFriendlyName = cloneFrom.overrideFriendlyName;
        this.overrideEnabled = cloneFrom.overrideEnabled;
        this.overridePosition = cloneFrom.overridePosition;
        this.overrideDimensions = cloneFrom.overrideDimensions;
        this.overrideDocumentSection = cloneFrom.overrideDocumentSection;
        this.overrideDefaultTextStyle = cloneFrom.overrideDefaultTextStyle;
        this.overrideTextStyles = cloneFrom.overrideTextStyles;
        this.overrideDefaultParagraphStyle = cloneFrom.overrideDefaultParagraphStyle;
        this.overrideParagraphStyles = cloneFrom.overrideParagraphStyles;
        this.overrideDefaultListStyle = cloneFrom.overrideDefaultListStyle;
        this.overrideListStyles = cloneFrom.overrideListStyles;
        this.overrideBackgroundColor = cloneFrom.overrideBackgroundColor;
        this.overrideWorkgroups = cloneFrom.overrideWorkgroups;
        this.overrideSharedAssets = cloneFrom.overrideSharedAssets;
        this.overrideCommunicationTemplate = cloneFrom.overrideCommunicationTemplate;
		this.verticallyRelativeTo = cloneFrom.verticallyRelativeTo;
		this.relativeDistance = cloneFrom.relativeDistance;
        this.relativeDistanceInInches = cloneFrom.relativeDistanceInInches;
		this.minimumKeepTogether = cloneFrom.minimumKeepTogether;
        this.minimumKeepTogetherInInches = cloneFrom.minimumKeepTogetherInInches;
	}

	// Copy constructor (not public) for cloning
	//
	protected Zone(Zone cloneFrom, Timestamp checkoutTimestamp, DocumentSection clonedDocumentSection, Document clonedDocument, Zone primaryLayoutZone)
	{
		super(cloneFrom, clonedDocument);
		this.save();
		copyData(cloneFrom, checkoutTimestamp, clonedDocumentSection, clonedDocument, primaryLayoutZone, true);
	}

	@Override
	public Object clone()
	{
		return new Zone(this, null, CloneHelper.assign(this.getSection()), CloneHelper.assign(this.getSection()).getDocument(), null);
	}

	@Override
	public Zone clone(Document clonedDocument) {
        return new Zone(this, null, CloneHelper.assign(this.getSection()), clonedDocument, null);
	}

	public Zone clone(Timestamp checkoutTimestamp, DocumentSection clonedDocumentSection, Document clonedDocument, Document primaryLayoutDocument)
	{
        Zone sourcePrimaryLayoutZone = this.getParent();
        String primaryLayoutZoneDna = sourcePrimaryLayoutZone == null ? null : sourcePrimaryLayoutZone.getDna();
		Zone targetPrimaryLayoutZone = primaryLayoutZoneDna == null ? null : CloneHelper.queryInSaveSession(()->{
			Zone clonedPrimaryLayoutZone = null;
			if(primaryLayoutDocument != null) {
				clonedPrimaryLayoutZone = primaryLayoutDocument.getZones().stream().filter(z->z.getDna().equals(primaryLayoutZoneDna)).findFirst().orElse(null);
				if(clonedPrimaryLayoutZone == null) {
					clonedPrimaryLayoutZone = findByDnaAndDocument(primaryLayoutZoneDna, primaryLayoutDocument);
				}
			}
			return clonedPrimaryLayoutZone;
		});

		Zone clone = new Zone(this, checkoutTimestamp, clonedDocumentSection, clonedDocument, targetPrimaryLayoutZone);

		clone.setParentObject(targetPrimaryLayoutZone);

		if (clonedDocumentSection == null) {
            clone.save();
        }

		return clone;
	}

	@Override
	public void preSave(Boolean isNew) {
		super.preSave(isNew);
		this.setBreakInheritance(true);

		if(needRecalculateHash || sha256Hash == null) {
            makeHash(false);
        }

		// Update the tag cloud
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTagCloudService.SERVICE_NAME, UpdateTagCloudService.class);
		ServiceExecutionContext context = UpdateTagCloudService.createContext(TagCloudType.ID_ZONE, this.getMetatags(), null, false);
		if ( service != null && context != null )
			service.execute(context);
	}
	@Override
	public void postSave(Boolean isNew) {
		super.postSave(isNew);
		//this.setBreakInheritance(false);
	}
	@Override
	public void preDelete() {
		super.preDelete();
		this.setBreakInheritance(true);
	}

	// *** FIXED ATTRIBUTES ***
	public Set<ZonePart> getParts() {
		return parts;
	}
	public void setParts(Set<ZonePart> parts) {
		this.parts = parts;
	}

	public void replaceParts(Set<ZonePart> parts) {
		if (this.parts != null)
		{
			this.parts.clear();
			this.parts.addAll(parts);
		}
		else
			this.parts = parts;
	}

	public DocumentSection getSection() {
		return section;
	}
	public void setSection(DocumentSection section) {
		this.section = section;
	}

	public Zone getParent() {
		if ( this.isBreakInheritance() || (this.getParentObject() != null && this.getParentObject().isBreakInheritance()) )
			return null;
		Zone parentZone = (Zone) this.getParentObject();
		if ( parentZone != null && parentZone.getParentObject() != null )
			return (Zone) parentZone.getParentObject();
		return (Zone) this.getParentObject();
	}

	public long getRootId() {
		Zone parent = this.getParent();
		return parent != null ? parent.getId() : this.getId();
	}

	public boolean getImportRequest() {
		return importRequest;
	}

	public void setImportRequest(boolean importRequest) {
		this.importRequest = importRequest;
	}

	public long getImportParentId() {
		return importParentId;
	}

	public void setImportParentId(long importParentId) {
		this.importParentId = importParentId;
	}

	// *** OVERRIDE ATTRIBUTES ***
    public String getName() {
    	if ( this.getParent() == null || this.isOverrideName() )
    		return name;
		return this.getParent().getName();
    }
	public void setName(String name) {
		this.name = name != null ? name.trim() : name;
	}

	public String getFriendlyName() {
		if ( this.getParent() == null || this.isOverrideFriendlyName() )
			return friendlyName;
		return this.getParent().getFriendlyName();
	}
	public void setFriendlyName(String friendlyName) {
		this.friendlyName = friendlyName;
	}

	public TextStyle getDefaultTextStyle() {
		if ( this.getParent() == null || this.isOverrideDefaultTextStyle() )
			return defaultTextStyle;
		return this.getParent().getDefaultTextStyle();

	}
	public void setDefaultTextStyle(TextStyle defaultTextStyle) {
		this.defaultTextStyle = defaultTextStyle;
	}

	public TextStyle getStarterTextStyle() {
		if ( this.getParent() == null)
			return starterTextStyle;
		return this.getParent().getStarterTextStyle();

	}
	public void setStarterTextStyle(TextStyle starterTextStyle) {
		this.starterTextStyle = starterTextStyle;
		if (styles == null) {
			styles = new HashSet<>();
			styles.add(this.starterTextStyle);
		} else if (!styles.contains(this.starterTextStyle) && this.starterTextStyle != null) {
			styles.add(this.starterTextStyle);
		}
	}

	public Set<TextStyle> getStyles() {
		if ( this.getParent() != null && !this.isOverrideTextStyles() )
		{
			if (styles == null) styles = new HashSet<>();
			styles.clear();
			styles.addAll( this.getParent().getStyles() );
		}
		return styles;
	}
	public void setStyles(Set<TextStyle> styles) {
		this.styles = styles;
		if (this.starterTextStyle != null && !this.styles.contains(this.starterTextStyle)) {
			this.styles.add(this.starterTextStyle);
		}
	}

	public ParagraphStyle getDefaultParagraphStyle() {
		if ( this.getParent() == null || this.isOverrideDefaultParagraphStyle() )
			return defaultParagraphStyle;
		return this.getParent().getDefaultParagraphStyle();
	}
	public void setDefaultParagraphStyle(ParagraphStyle defaultParagraphStyle) {
		this.defaultParagraphStyle = defaultParagraphStyle;
	}

	public Set<ParagraphStyle> getParagraphStyles() {
		if ( this.getParent() != null && !this.isOverrideParagraphStyles() ) {
			if (paragraphStyles == null) paragraphStyles = new HashSet<>();
			paragraphStyles.clear();
			paragraphStyles.addAll( this.getParent().getParagraphStyles() );
		}
		return paragraphStyles;
	}
	public void setParagraphStyles(Set<ParagraphStyle> paragraphStyles) {
		this.paragraphStyles = paragraphStyles;
	}

	public ListStyle getDefaultListStyle() {
		if ( this.getParent() == null || this.isOverrideDefaultListStyle() )
			return defaultListStyle;
		return this.getParent().getDefaultListStyle();
	}
	public void setDefaultListStyle(ListStyle defaultListStyle) {
		this.defaultListStyle = defaultListStyle;
	}

	public Set<ListStyle> getListStyles() {
		if ( this.getParent() != null && !this.isOverrideListStyles() ) {
			if (listStyles == null) listStyles = new HashSet<>();
			listStyles.clear();
			listStyles.addAll( this.getParent().getListStyles() );
		}
		return listStyles;
	}
	public void setListStyles(Set<ListStyle> listStyles) {
		this.listStyles = listStyles;
	}

    public int getHeight() {
    	if ( this.getParent() == null || this.isOverrideDimensions() || this.isDigitalAlternate() )
    		return height;
		return this.getParent().getHeight();
	}
	public void setHeight(int height) {
		this.height = height;
	}

	public String getHeightInInches() {
		try {
			double sectionHeightInInches = 1;
			if (this.section != null)
				sectionHeightInInches = Double.parseDouble(this.section.getHeightInInches());
			double inInches = Double.parseDouble(DecimalValueUtil.dehydrate(this.getHeight())) * sectionHeightInInches / 100;
			return DecimalValueUtil.dehydrate(DecimalValueUtil.hydrate(inInches));
		} catch (ParseException e) {
			return "error";
		}
	}

	public int getTopX() {
		if ( this.getParent() == null || this.isOverridePosition() || this.isDigitalAlternate() )
			return topX;
		return this.getParent().getTopX();
	}
	public void setTopX(int topX) {
		this.topX = topX;
	}

	public String getTopXInInches() {
		try {
			double sectionWidthInInches = 1;
			if (this.section != null)
				sectionWidthInInches = Double.parseDouble(this.section.getWidthInInches());
			double inInches = Double.parseDouble(DecimalValueUtil.dehydrate(this.getTopX())) * sectionWidthInInches / 100;
			return DecimalValueUtil.dehydrate(DecimalValueUtil.hydrate(inInches));
		} catch (ParseException e) {
			return "error";
		}
	}

	public int getTopY() {
		if ( this.getParent() == null || this.isOverridePosition() || this.isDigitalAlternate() )
			return topY;
		return this.getParent().getTopY();
	}
	public void setTopY(int topY) {
		this.topY = topY;
	}

	public String getTopYInInches() {
		try {
			double sectionHeightInInches = 1;
			if (this.section != null)
				sectionHeightInInches = Double.parseDouble(this.section.getHeightInInches());
			double inInches = Double.parseDouble(DecimalValueUtil.dehydrate(this.getTopY())) * sectionHeightInInches / 100;
			return DecimalValueUtil.dehydrate(DecimalValueUtil.hydrate(inInches));
		} catch (ParseException e) {
			return "error";
		}
	}

	public int getWidth() {
    	if ( this.getParent() == null || this.isOverrideDimensions() || this.isDigitalAlternate() )
    		return width;
		return this.getParent().getWidth();
	}
	public void setWidth(int width) {
		this.width = width;
	}

	public String getWidthInInches() {
		try {
			double sectionWidthInInches = 1;
			if (this.section != null)
				sectionWidthInInches = Double.parseDouble(this.section.getWidthInInches());
			double inInches = Double.parseDouble(DecimalValueUtil.dehydrate(this.getWidth())) * sectionWidthInInches / 100;
			return DecimalValueUtil.dehydrate(DecimalValueUtil.hydrate(inInches));
		} catch (ParseException e) {
			return "error";
		}
	}

	public String getBackgroundColor() {
    	if ( this.getParent() == null || this.isOverrideBackgroundColor() ) {
    		if ( this.backgroundColor == null && (this.getZoneTypeId() == ZoneType.ID_EMAIL_ZONE || this.getZoneTypeId() == ZoneType.ID_WEB_ZONE) )
    			return Zone.ZONE_BACKGROUND_TRANSPARENT;
    		else if ( this.backgroundColor == null )
    			return Zone.ZONE_BACKGROUND_DEFAULT;
    		return backgroundColor;
    	}
		if ( this.getParent().getBackgroundColor() == null && (this.getZoneTypeId() == ZoneType.ID_EMAIL_ZONE || this.getZoneTypeId() == ZoneType.ID_WEB_ZONE) )
			return Zone.ZONE_BACKGROUND_TRANSPARENT;
		else if ( this.getParent().getBackgroundColor() == null )
			return Zone.ZONE_BACKGROUND_DEFAULT;
		return this.getParent().getBackgroundColor();
	}
	public void setBackgroundColor(String backgroundColor) {
		this.backgroundColor = backgroundColor;
	}

	public boolean isEnabled() {
		if ( this.getParent() == null || this.isOverrideEnabled() )
			return enabled;
		return this.getParent().isEnabled();
	}
	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}

	public Set<Workgroup> getWorkgroups() {
		if ( this.getParent() != null && !this.isOverrideWorkgroups() )
		{
			if (workgroups == null) workgroups = new HashSet<>();
			workgroups.clear();
			workgroups.addAll( this.getParent().getWorkgroups() );
		}
		return workgroups;
	}
	public void setWorkgroups(Set<Workgroup> workgroups) {
		this.workgroups = workgroups;
	}

	public boolean isRestrictSharedAssets() {
		if ( this.getParent() != null && !this.isOverrideSharedAssets() )
			return this.getParent().isRestrictSharedAssets();
		return restrictSharedAssets;
	}
	public void setRestrictSharedAssets(boolean restrictSharedAssets) {
		this.restrictSharedAssets = restrictSharedAssets;
	}

	public Set<ContentObject> getImageAssets() {
		if ( this.getParent() != null && !this.isOverrideSharedAssets() )
		{
			if (imageAssets == null) imageAssets = new HashSet<>();
			imageAssets.clear();
			imageAssets.addAll(this.getParent().getImageAssets());
		}
		return imageAssets;
	}
	public void setImageAssets(Set<ContentObject> imageAssets) {
		this.imageAssets = imageAssets;
	}

	public Set<ContentObject> getSmartTextAssets() {
		if ( this.getParent() != null && !this.isOverrideSharedAssets() )
		{
			if (smartTextAssets == null) smartTextAssets = new HashSet<>();
			smartTextAssets.clear();
			smartTextAssets.addAll(this.getParent().getSmartTextAssets());
		}
		return smartTextAssets;
	}
	public void setSmartTextAssets(Set<ContentObject> smartTextAssets) {
		this.smartTextAssets = smartTextAssets;
	}

	public ContentObject getDefaultCommunicationTemplateImage() {
		if ( this.getParent() != null && !this.isOverrideCommunicationTemplate() )
			return this.getParent().getDefaultCommunicationTemplateImage();
		return defaultCommunicationTemplateImage;
	}
	public void setDefaultCommunicationTemplateImage(ContentObject defaultCommunicationTemplateImage) {
		this.defaultCommunicationTemplateImage = defaultCommunicationTemplateImage;
	}

	public ContentObject getDefaultCommunicationTemplateSmartText() {
		if ( this.getParent() != null && !this.isOverrideCommunicationTemplate() )
			return this.getParent().getDefaultCommunicationTemplateSmartText();
		return defaultCommunicationTemplateSmartText;
	}
	public void setDefaultCommunicationTemplateSmartText(ContentObject defaultCommunicationTemplateSmartText) {
		this.defaultCommunicationTemplateSmartText = defaultCommunicationTemplateSmartText;
	}


	// *** NO OVERRIDE ATTRIBUTES ***
	public Integer getSequence() {
		if ( this.getParent() != null )
			return this.getParent().getSequence();
		return sequence;
	}
	public void setSequence(Integer sequence) {
		this.sequence = sequence;
	}

	public int getRenderedContainerTypeId() {
		if ( this.getParent() != null && !this.isDigitalChannelAlternate() )
			return this.getParent().getRenderedContainerTypeId();
		return renderedContainerTypeId;
	}
	public void setRenderedContainerTypeId(int renderedContainerTypeId) {
		this.renderedContainerTypeId = renderedContainerTypeId;
	}

	public int getImageDimensionsTypeId() {
		if ( this.getParent() != null && !this.isDigitalChannelAlternate() )
			return this.getParent().getImageDimensionsTypeId();
		return imageDimensionsTypeId;
	}
	public void setImageDimensionsTypeId(int imageDimensionsTypeId) {
		this.imageDimensionsTypeId = imageDimensionsTypeId;
	}

	public boolean isEnforceMinimumHeight() {
		if ( this.getParent() != null )
			return this.getParent().isEnforceMinimumHeight();
		return enforceMinimumHeight;
	}
	public void setEnforceMinimumHeight(boolean enforceMinimumHeight) {
		this.enforceMinimumHeight = enforceMinimumHeight;
	}

	public boolean isSplitTables() {
		if ( this.getParent() != null )
			return this.getParent().isSplitTables();
		return splitTables;
	}
	public void setSplitTables(boolean splitTables) {
		this.splitTables = splitTables;
	}

	public boolean isMessageContentEditing() {
    	if ( this.getParent() != null )
    		return this.getParent().isMessageContentEditing();
		return messageContentEditing;
	}
	public void setMessageContentEditing(boolean messageContentEditing) {
		this.messageContentEditing = messageContentEditing;
	}

    public boolean isRepeats() {
    	if ( this.getParent() != null )
    		return this.getParent().isRepeats();
    	return repeats;
    }
    public void setRepeats( boolean val ) {
    	repeats = val;
    }

    public int getRepeatingZoneTypeId() {
    	if ( this.getParent() != null )
    		return this.getParent().getRepeatingZoneTypeId();
		return repeatingZoneTypeId;
	}
	public void setRepeatingZoneTypeId(int repeatingZoneTypeId) {
		this.repeatingZoneTypeId = repeatingZoneTypeId;
	}

	public boolean isCanGrow() {
    	if ( this.getParent() != null )
    		return this.getParent().isCanGrow();
    	return canGrow;
    }
    public void setCanGrow( boolean val ) {
    	canGrow = val;
    }

    public boolean isCanFlow() {
    	if ( this.getParent() != null )
    		return this.getParent().isCanFlow();
    	return canFlow;
    }
    public void setCanFlow(boolean val) {
    	canFlow = val;
    }

    public boolean isMinimumSize() {
    	if ( this.getParent() != null )
    		return this.getParent().isMinimumSize();
    	return minimumSize;
    }
    public void setMinimumSize( boolean val ) {
    	minimumSize = val;
    }

	public boolean isAbsolutePositioning() {
		if ( this.getParent() != null )
			return this.getParent().isAbsolutePositioning();
		return absolutePositioning;
	}
	public void setAbsolutePositioning(boolean absolutePositioning) {
		this.absolutePositioning = absolutePositioning;
	}

	public boolean isCommunicationContentEditing() {
		if ( this.getParent() != null )
			return this.getParent().isCommunicationContentEditing();
		return communicationContentEditing;
	}
	public void setCommunicationContentEditing(boolean communicationContentEditing) {
		this.communicationContentEditing = communicationContentEditing;
	}

	public Map<String, String> getZoneAttributes() {
		if ( this.getParent() != null ) {
			if (zoneAttributes == null) zoneAttributes = new HashMap<>();
			zoneAttributes.clear();
			zoneAttributes.putAll(this.getParent().getZoneAttributes());
		}
		return zoneAttributes;
	}
	public void setZoneAttributes(Map<String, String> zoneAttributes) {
		this.zoneAttributes = zoneAttributes;
	}

	public String getDescription() {
		if ( this.getParent() != null )
			return this.getParent().getDescription();
		return description;
	}

	public int getPage() {
		if ( this.getParent() != null )
			return this.getParent().getPage();
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}

    public int getRotationAngle() {
    	if ( this.getParent() != null )
    		return this.getParent().getRotationAngle();
		return rotationAngle;
	}
	public void setRotationAngle(int rotationAngle) {
		this.rotationAngle = rotationAngle;
	}

	public long getContentTypeId() {
    	if ( this.getParent() != null )
			return this.getParent().getContentTypeId();
		return contentTypeId;
	}
	public void setContentTypeId(long contentTypeId) {
		this.contentTypeId = contentTypeId;
	}

	public int getZoneTypeId() {
		if ( this.getParent() != null && (!this.isDigitalChannelAlternate() || this.getParent().isInteractive()))
			return this.getParent().getZoneTypeId();
		return zoneTypeId;
	}
	public void setZoneTypeId(int zoneTypeId) {
		this.zoneTypeId = zoneTypeId;
	}

	public boolean isDxfOutput() {
		if ( this.getParent() != null )
			return this.getParent().isDxfOutput();
		return dxfOutput;
	}
	public void setDxfOutput(boolean dxfOutput) {
		this.dxfOutput = dxfOutput;
	}

	public boolean isHtmlOutput() {
		if ( this.getParent() != null )
			return this.getParent().isHtmlOutput();
		return htmlOutput;
	}
	public void setHtmlOutput(boolean htmlOutput) {
		this.htmlOutput = htmlOutput;
	}

	public boolean isFlowZone() {
		if ( this.getParent() != null )
			return this.getParent().isFlowZone();
		return flowZone;
	}
	public void setFlowZone(boolean flowZone) {
		this.flowZone = flowZone;
	}

	public Zone getFlowIntoZone() {
		if ( this.getParent() != null )
			return this.getParent().getFlowIntoZone();
		return flowIntoZone;
	}
	public void setFlowIntoZone(Zone flowIntoZone) {
		this.flowIntoZone = flowIntoZone;
	}

	public Integer getVerticalAlignment() {
		if ( this.getParent() != null )
			return this.getParent().getVerticalAlignment();
		return verticalAlignment;
	}
	public void setVerticalAlignment(Integer verticalAlignment) {
		this.verticalAlignment = verticalAlignment;
	}

	public boolean isBacker() {
		if ( this.getParent() != null )
			return this.getParent().isBacker();
		return backer;
	}
	public void setBacker(boolean backer) {
		this.backer = backer;
	}

	public boolean isSupportsTables() {
		if ( this.getParent() != null )
			return this.getParent().isSupportsTables();
		return supportsTables;
	}
	public void setSupportsTables(boolean supportsTables) {
		this.supportsTables = supportsTables;
	}

	public boolean isSupportsForms() {
		if ( this.getParent() != null )
			return this.getParent().isSupportsForms();
		return supportsForms;
	}
	public void setSupportsForms(boolean supportsForms) {
		this.supportsForms = supportsForms;
	}

	public boolean isSupportsBarcodes() {
		if ( this.getParent() != null )
			return this.getParent().isSupportsBarcodes();
		return supportsBarcodes;
	}
	public void setSupportsBarcodes(boolean supportsBarcodes) {
		this.supportsBarcodes = supportsBarcodes;
	}

	public boolean isSupportsCustomParagraphs() {
		if ( this.getParent() != null )
			return this.getParent().isSupportsCustomParagraphs();
		return supportsCustomParagraphs;
	}
	public void setSupportsCustomParagraphs(boolean supportsCustomParagraphs) {
		this.supportsCustomParagraphs = supportsCustomParagraphs;
	}

	public boolean isFreeform() {
		if ( this.getParent() != null )
			return this.getParent().isFreeform();
		return freeform;
	}
	public void setFreeform(boolean freeform) {
		this.freeform = freeform;
	}

	public boolean isExportToSingleMessage() {
		return exportToSingleMessage;
	}

	public void setExportToSingleMessage(boolean exportToSingleMessage) {
		this.exportToSingleMessage = exportToSingleMessage;
	}

	public Boolean getMixedDataGroups() {
		if ( this.getParent() != null )
			return this.getParent().getMixedDataGroups();
		return mixedDataGroups;
	}
	public void setMixedDataGroups(Boolean mixedDataGroups) {
		this.mixedDataGroups = mixedDataGroups;
	}

	public DataGroup getDataGroup() {
		if ( this.getParent() != null )
			return this.getParent().getDataGroup();
		return dataGroup;
	}
	public void setDataGroup(DataGroup dataGroup) {
		this.dataGroup = dataGroup;
	}

	public long getSubContentTypeId() {
		if ( this.getParent() != null )
			return this.getParent().getSubContentTypeId();
		return subContentTypeId;
	}
	public void setSubContentTypeId(long subContentTypeId) {
		this.subContentTypeId = subContentTypeId;
	}

	public Set<ContentObject> getContentObjects() {
		if ( this.getParent() != null )
		{
			if (contentObjects == null) contentObjects = new HashSet<>();
			contentObjects.clear();
			contentObjects.addAll(this.getParent().getContentObjects());
		}
		return contentObjects;
	}

	public void setContentObjects(Set<ContentObject> contentObjects) {
		this.contentObjects = contentObjects;
	}

	public void addContentObject(ContentObject contentObject) {
		getContentObjects().add(contentObject);
	}

	public void removeContentObject(ContentObject contentObject) {
		getContentObjects().remove(contentObject);
	}

	@SuppressWarnings("rawtypes")
	public List<ContentObject> findAllMessagesWithActiveOrWorkingCopy() {
		if ( this.getParent() != null )
		{
			return this.getParent().findAllMessagesWithActiveOrWorkingCopy();
		}

		String query =
				"SELECT DISTINCT co.ID FROM content_object co " +
				"INNER JOIN content_object_data cod ON cod.content_object_id = co.id " +
				"WHERE 		cod.data_type = 1 OR cod.data_type = 2 " +
				"AND 		co.zone_id = :zoneId " +
				"GROUP BY 	co.ID";

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		sqlQuery.setParameter("zoneId", this.getId());

        List ids = sqlQuery.list();

		List<ContentObject> msgList = new ArrayList<>();
        for(Object idObj : ids){
        	msgList.add(ContentObject.findById(((BigInteger)idObj).longValue()));
        }

		return msgList;
	}

	public boolean isApplyAltText() {
    	if ( this.getParent() != null )
			return this.getParent().isApplyAltText();
		return applyAltText;
	}
	public void setApplyAltText(boolean applyAltText) {
		this.applyAltText = applyAltText;
	}

	public boolean isApplyImageLink() {
		if ( this.getParent() != null )
			return this.getParent().isApplyImageLink();
		return applyImageLink;
	}
	public void setApplyImageLink(boolean applyImageLink) {
		this.applyImageLink = applyImageLink;
	}

	public boolean isApplyImageExtLink() {
		if ( this.getParent() != null ) {
			if(this.getDocument().getIsOmniChannel() && (this.getDocument().isWebTouchpoint() || this.getDocument().isEmailTouchpoint())){
				return applyImageExtLink;
			}else {
				return this.getParent().isApplyImageExtLink();
			}
		}
		return applyImageExtLink;
	}

	public void setApplyImageExtLink(boolean applyImageExtLink) {
		this.applyImageExtLink = applyImageExtLink;
	}

	public boolean isApplyImageExtPath() {
		if ( this.getParent() != null )
			return this.getParent().isApplyImageExtPath();
		return applyImageExtPath;
	}

	public void setApplyImageExtPath(boolean applyImageExtPath) {
		this.applyImageExtPath = applyImageExtPath;
	}

	public Integer getDefaultCanvasWidth() {
		if ( this.getParent() != null )
			return this.getParent().getDefaultCanvasWidth();
		return defaultCanvasWidth;
	}
	public void setDefaultCanvasWidth(Integer defaultCanvasWidth) {
		this.defaultCanvasWidth = defaultCanvasWidth;
	}

	// *** OVERRIDE TOGGLES ***
	public boolean isOverrideName() {
		return overrideName;
	}
	public void setOverrideName(boolean overrideName) {
		this.overrideName = overrideName;
	}

	public boolean isOverrideFriendlyName() {
		return overrideFriendlyName;
	}
	public void setOverrideFriendlyName(boolean overrideFriendlyName) {
		this.overrideFriendlyName = overrideFriendlyName;
	}

	public boolean isOverrideEnabled() {
		return overrideEnabled;
	}
	public void setOverrideEnabled(boolean overrideEnabled) {
		this.overrideEnabled = overrideEnabled;
	}

	public boolean isOverridePosition() {
		return overridePosition;
	}
	public void setOverridePosition(boolean overridePosition) {
		this.overridePosition = overridePosition;
	}

	public boolean isOverrideDimensions() {
		return overrideDimensions;
	}
	public void setOverrideDimensions(boolean overrideDimensions) {
		this.overrideDimensions = overrideDimensions;
	}

	public boolean isOverrideDocumentSection() {
		return overrideDocumentSection;
	}
	public void setOverrideDocumentSection(boolean overrideDocumentSection) {
		this.overrideDocumentSection = overrideDocumentSection;
	}

	public boolean isOverrideDefaultTextStyle() {
		return overrideDefaultTextStyle;
	}
	public void setOverrideDefaultTextStyle(boolean overrideDefaultTextStyle) {
		this.overrideDefaultTextStyle = overrideDefaultTextStyle;
	}

	public boolean isOverrideTextStyles() {
		return overrideTextStyles;
	}
	public void setOverrideTextStyles(boolean overrideTextStyles) {
		this.overrideTextStyles = overrideTextStyles;
	}

	public boolean isOverrideDefaultParagraphStyle() {
		return overrideDefaultParagraphStyle;
	}
	public void setOverrideDefaultParagraphStyle(boolean overrideDefaultParagraphStyle) {
		this.overrideDefaultParagraphStyle = overrideDefaultParagraphStyle;
	}

	public boolean isOverrideParagraphStyles() {
		return overrideParagraphStyles;
	}
	public void setOverrideParagraphStyles(boolean overrideParagraphStyles) {
		this.overrideParagraphStyles = overrideParagraphStyles;
	}

	public boolean isOverrideDefaultListStyle() {
		return overrideDefaultListStyle;
	}
	public void setOverrideDefaultListStyle(boolean overrideDefaultListStyle) {
		this.overrideDefaultListStyle = overrideDefaultListStyle;
	}

	public boolean isOverrideListStyles() {
		return overrideListStyles;
	}
	public void setOverrideListStyles(boolean overrideListStyles) {
		this.overrideListStyles = overrideListStyles;
	}

	public boolean isOverrideBackgroundColor() {
		return overrideBackgroundColor;
	}
	public void setOverrideBackgroundColor(boolean overrideBackgroundColor) {
		this.overrideBackgroundColor = overrideBackgroundColor;
	}

	public boolean isOverrideWorkgroups() {
		return overrideWorkgroups;
	}
	public void setOverrideWorkgroups(boolean overrideWorkgroups) {
		this.overrideWorkgroups = overrideWorkgroups;
	}

	public boolean isOverrideSharedAssets() {
		return overrideSharedAssets;
	}
	public void setOverrideSharedAssets(boolean overrideSharedAssets) {
		this.overrideSharedAssets = overrideSharedAssets;
	}

	public boolean isOverrideCommunicationTemplate() {
		return overrideCommunicationTemplate;
	}
	public void setOverrideCommunicationTemplate(boolean overrideCommunicationTemplate) {
		this.overrideCommunicationTemplate = overrideCommunicationTemplate;
	}

	public Integer getVerticallyRelativeTo() {
		return this.verticallyRelativeTo;
	}
	public void setVerticallyRelativeTo(Integer verticallyRelativeTo) {
		this.verticallyRelativeTo = verticallyRelativeTo;
	}
	public Integer getRelativeDistance() {
		return this.relativeDistance;
	}

	public void setRelativeDistance(Integer relativeDistance) {
		this.relativeDistance = relativeDistance;
	}

	public String getRelativeDistanceInInches() {
		try {
			return DecimalValueUtil.dehydrate(this.relativeDistance);
		} catch (ParseException e) {
			return "";
		}
	}

	public void setRelativeDistanceInInches(String relativeDistanceInInches) {
		try	{
			this.setRelativeDistance(DecimalValueUtil.hydrate(relativeDistanceInInches));
		} catch (ParseException e) {
			// do nothing
		}
	}

	public Integer getMinimumKeepTogether() {
		return this.minimumKeepTogether;
	}

	public void setMinimumKeepTogether(Integer minimumKeepTogether) {
		this.minimumKeepTogether = minimumKeepTogether;
	}

	public String getMinimumKeepTogetherInInches() {
		if (this.minimumKeepTogether == null)
			return "";

		try {
			return DecimalValueUtil.dehydrate(this.minimumKeepTogether);
		} catch (ParseException e) {
			return "";
		}
	}

	public void setMinimumKeepTogetherInInches(String minimumKeepTogetherInInches) {
		try	{
			Integer keepTogetherValue = !minimumKeepTogetherInInches.isEmpty() ? DecimalValueUtil.hydrate(minimumKeepTogetherInInches) : null;
			this.setMinimumKeepTogether(keepTogetherValue);
		} catch (ParseException e) {
			// do nothing
		}
	}

	/**
	 * Using id's here because there is no logical business key.
	 */
	@Override
	public int hashCode() {
		return MessageFormat.format("{0}-{1}-{2}", getObjectSchemaName(), Zone.class.getSimpleName(), getId()).hashCode();//Long.valueOf(getId()).hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if ( !(obj instanceof Zone) )
			return false;
		final Zone other = (Zone) obj;

        if(! getObjectSchemaName().equals(other.getObjectSchemaName()))
            return false;

		if (id > 0) {
			return id == other.getId();
		}else {
			if (this.getName() == null) {
				if (other.getName() != null)
					return false;
			} else if (!this.getName().equals(other.getName())) {
				return false;
			}
			if (this.getFriendlyName() == null) {
				if (other.getFriendlyName() != null)
					return false;
			} else if (!this.getFriendlyName().equals(other.getFriendlyName()))
				return false;
		}
		return true;
	}

	public String getNameInContext() {
		TouchpointSelection selectionContext = UserUtil.getCurrentSelectionContext(this);
		if ( selectionContext != null && selectionContext.getAlternateLayout() != null && selectionContext.getDocument().getId() == this.getDocument().getId() )
			return selectionContext.getAlternateLayout().findZoneByParent(this).getFriendlyName();
		return this.getFriendlyName();
	}

    public long getHashCode() {
    	return hashCode();
    }

    @Override
	public String toString() {
    	// NOTE - we return the id here so that we can
    	// bind the zone to a hidden checkbox in the message delivery
    	// form. If you need to change this talk to Rob first.
    	return "" + id;
    }

	int getHeightForOrientation() {
		if ( this.isPlaceholder() )
			return 0;
		else if (this.getSection().isPortrait() || this.getSection().getDocument().isEmailTouchpoint() || this.getSection().getDocument().isWebTouchpoint())
			return this.getHeight();
		else
			return (int)(Math.round(this.getHeight()*(8.5/11)));
	}

	public int getWidthForOrientation() {
		if ( this.isPlaceholder() )
			return 0;
		else if (this.getSection().isPortrait() || this.getSection().getDocument().isEmailTouchpoint() || this.getSection().getDocument().isWebTouchpoint())
			return this.getWidth();
		else
			return (int)(Math.round(this.getWidth()*(11/8.5)));
	}

	@SuppressWarnings("rawtypes")
	public boolean hasMessageDeliveries() {
		boolean result = false;

		// It includes also archived content objects

		String query =
				"SELECT COUNT(*) FROM content_object co " +
				"INNER JOIN content_object_data cod ON cod.content_object_id = co.id " +
				"WHERE 		cod.data_type > 0 " +
				"AND 		co.zone_id = :zoneId " +
				"GROUP BY 	co.ID";

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		sqlQuery.setParameter("zoneId", this.getId());
		List retList = sqlQuery.list();

		int count = 0;
		if( retList != null && !retList.isEmpty()){
			Object retObj = retList.get(0);
			if ( Number.class.isInstance(retObj) )
				count = ((Number) retObj).intValue();
		}
		if ( count > 0 )
			result = true;

		return result;
	}
	@SuppressWarnings("rawtypes")
	public List<ContentObject> getMessageDeliveries() {
		List<ContentObject> result = new ArrayList<>();

		// It includes also archived content objects

		String query =
				"SELECT co.ID FROM content_object co " +
				"INNER JOIN content_object_data cod ON cod.content_object_id = co.id " +
				"WHERE 		cod.data_type > 0 " +
				"AND 		co.zone_id = :zoneId " +
				"GROUP BY 	co.ID";

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		sqlQuery.setParameter("zoneId", this.getId());
        List ids = sqlQuery.list();

		for(Object idObj : ids){
			ContentObject currentMessage = ContentObject.findById(((BigInteger)idObj).longValue());
        	if ( currentMessage != null )
        		result.add(currentMessage);
        }

		return result;
	}

	public boolean hasCommunications() {
		return getCommunicationsCount() > 0;
	}

	@SuppressWarnings("rawtypes")
	public int getCommunicationsCount() {
		String query =
				"SELECT COUNT(*) FROM	comm_zone_content_association czca " +
				"WHERE 					czca.zone_id = :zoneId ";

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		sqlQuery.setParameter("zoneId", this.getId());
		List retList = sqlQuery.list();

		int count = 0;
		if( retList != null && !retList.isEmpty()){
			Object retObj = retList.get(0);
			if ( Number.class.isInstance(retObj) )
				count = ((Number) retObj).intValue();
		}
		return count;
	}

	public boolean hasContentObjects()
	{
		String query = "SELECT COUNT(*) " +
				"FROM CONTENT_OBJECT co " +
				"WHERE co.removed = false AND co.zone_id = " + this.getId();

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		List retList = sqlQuery.list();

		int count = 0;
		if (retList != null && !retList.isEmpty()) {
			Object retObj = retList.get(0);
			if (Number.class.isInstance(retObj))
				count = ((Number) retObj).intValue();
		}
		if (count > 0)
			return true;

		return false;
	}

	public boolean hasActiveContentObjects(int objectType)
	{
		String query = "SELECT COUNT(co.id) " +
				"FROM content_object co " +
				"INNER JOIN content_object_data cod ON cod.content_object_id = co.id AND cod.data_type = 2 " +
				"WHERE co.removed = false AND co.zone_id = " + this.getId();

		if (objectType != ContentObject.OBJECT_TYPE_ANY_LOCAL)
			query = query + " AND co.object_type = " + objectType;

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		List retList = sqlQuery.list();

		int count = 0;
		if (retList != null && !retList.isEmpty()) {
			Object retObj = retList.get(0);
			if (Number.class.isInstance(retObj))
				count = ((Number) retObj).intValue();
		}
		if (count > 0)
			return true;

		return false;
	}

	public Set<ContentObject> getMessages() {
		if ( UserUtil.getPrincipalUser() != null )
			return new HashSet<>(getSortedMessages(ContentObject.DATA_TYPE_WORKING_AND_ACTIVE));
		else
			return getSortedMessages(ContentObject.DATA_TYPE_WORKING_AND_ACTIVE);
	}

	// It will return Content Object message type only, which has working, active or/and archived data according to dataType parameter,
	// which is bitfield
	//
	// 0001 - Working Data
	// 0010 - Active Data
	// 0100 - Archived Data
	//
	// and their combinations
	//
	public Set<ContentObject> getSortedMessages(int dataType) {

        StringBuilder query = new StringBuilder("SELECT co " +
				" FROM  ContentObject co, ContentObjectData cod " +
				" WHERE co.id = cod.contentObject.id " +
				" AND 	co.objectType = " + ContentObject.OBJECT_TYPE_MESSAGE +
				" AND 	co.zone.id = :zoneId ");

		if (dataType != ContentObject.DATA_TYPE_ALL) {
			query.append(" AND BITAND(cod.dataType, :dataType) != 0 ");
		}

		query.append(" ORDER BY co.name");

		Map<String, Object> params = new HashMap<>();
		params.put("zoneId", this.getId());
		if (dataType != ContentObject.DATA_TYPE_ALL) {
			params.put("dataType", dataType);
		}

        Set<ContentObject> retSet = new LinkedHashSet<>(HibernateUtil.getManager().getObjectsOfSpecifiedClassByHQL(ContentObject.class, query.toString(), params));

		return Collections.unmodifiableSet(retSet);
	}

	@Override
	public void save() {
		HibernateUtil.getManager().saveObject(this);
	}

	public List<ContentObject> getMessagesFilteredByView(long messageListFilterTypeId, Date versionEffectiveDate) {
		int filterId = Long.valueOf(messageListFilterTypeId).intValue();
		return ContentObjectUtil.filterByView(filterId, new ArrayList<>(getMessages()), versionEffectiveDate);
	}

	public void associateDocument(Document document) {
		setDocument(document);
		if(document != null){
			document.getZones().add(this);
		}
	}

	public List<TextStyle> getStylesSorted() {
		List<TextStyle> sortedStyles = new ArrayList<>(getStyles());
		Collections.sort(sortedStyles, new TextStyleComparator());
		return sortedStyles;
	}
	public List<ParagraphStyle> getParaStylesSorted() {
		List<ParagraphStyle> sortedStyles = new ArrayList<>(getParagraphStyles());
		Collections.sort(sortedStyles, new ParagraphStyleComparator());
		return sortedStyles;
	}
	public List<ListStyle> getListStylesSorted() {
		List<ListStyle> sortedStyles = new ArrayList<>(getListStyles());
		Collections.sort(sortedStyles, new ListStyleComparator());
		return sortedStyles;
	}

	public boolean getAreTablesEnabled() {
		return this.supportsTables;
	}

	public boolean getAreFormsEnabled() {
		return this.supportsForms && this.getDocument().isNativeCompositionTouchpoint();
	}

	public long getConnector() {
		return this.getDocument().getConnectorConfiguration().getConnector().getId();
	}
	public long getChannel() {
		return this.getDocument().getConnectorConfiguration().getConnector().getChannel().getId();
	}

	public boolean isMultipart(){
		return ContentType.MULTIPART == this.getContentTypeId();
	}
	public void setMultipart(boolean value){
		if( value == false )
			this.getParts().clear();
	}

	public boolean getIsHeader() {
		return this.getZoneTypeId() == ZoneType.ID_HEADER_ZONE;
	}

	public boolean getIsFooter() {
		return this.getZoneTypeId() == ZoneType.ID_FOOTER_ZONE;
	}

	public boolean getIsRegionLeft() {
		return this.getZoneTypeId() == ZoneType.ID_REGION_LEFT_ZONE;
	}
	
	public boolean getIsRegionRight() {
		return this.getZoneTypeId() == ZoneType.ID_REGION_RIGHT_ZONE;
	}
	
	/**
	 * Check whether this zone contains at least one text part, including text zone itself.
     */
	public boolean hasAtLeastOneTextPart(){
		if ( this.getContentTypeId() == ContentType.TEXT ) {
			return true;
		} else if ( this.getContentTypeId()==ContentType.MULTIPART ) {
			for ( ZonePart part : this.getParts() ) {
				if ( part.getContentType().getId() == ContentType.TEXT ) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * Check whether this zone contains at least one graphic part, including graphic zone itself.
     */
	public boolean hasAtLeastOneGraphicPart(){
		if ( this.getContentTypeId() == ContentType.GRAPHIC ) {
			return true;
		} else if ( this.getContentTypeId()==ContentType.MULTIPART ) {
			for ( ZonePart part : this.getParts() ) {
				if ( part.getContentType().getId() == ContentType.GRAPHIC ) {
					return true;
				}
			}
		}
		return false;
	}

	public boolean isHasAtLeastOneGraphicPart(){
		return this.hasAtLeastOneGraphicPart();
	}

	public boolean isQualifiedEqualsDelivered(){
		String value = this.getZoneAttributes().get("qualifiedEqualsDelivered");
		return ( value == null ) ? false : "true".equalsIgnoreCase(value);
	}

	public int getNumberOfParts(){
		if ( !isMultipart() ) { return 0; }
		return ( this.getParts() == null ) ? 0 : this.getParts().size();
	}

	public void setEncodedParts(String partsInfo){
		List<ZonePart> newParts = ZonePart.multipartInfo(partsInfo);

		getParts().clear();
		for (ZonePart zonePart : newParts) {
			zonePart.setZone(this);
			getParts().add(zonePart);
		}
	}

	public String getEncodedParts(){
		return ZonePart.parsesToString(getParts());
	}


	/**
	 * The {@link #parts} should be an ordered list.
	 * The GUI and the back end do not support the order.
	 * Therefore for now we will have an implied order
	 * as defined by {@link ZonePartComparator}.
	 *
	 * Changes to the returned list will not be reflected
	 * in the {@link #parts} set.  To add and remove parts
	 * use {@link #getParts()} and {@link #setParts(Set)}
	 * methods instead.
	 *
	 * @return An ordered list of parts.
	 */
	public List<ZonePart> getPartsInOrder() {
		List<ZonePart> list = new ArrayList<>();
		if ( this.getParts() == null || this.getParts().isEmpty() )
			return list;

		list.addAll(this.getParts());
		//Collections.sort(list, new ZonePartComparator());
		list.sort(new ZonePartSequenceComparator());

		return list;
	}

	public List<ZonePart> getPartsInSequenceOrder() {
		List<ZonePart> list = new ArrayList<>();
		if ( this.getParts() == null || this.getParts().isEmpty() )
			return list;

		list.addAll(this.getParts());
		Collections.sort(list, new ZonePartSequenceComparator());

		return list;
	}

	public static Zone findZoneInContextByParent(Zone zone) {
		Zone targetZone = zone;
		if (targetZone != null) {
			TouchpointSelection selectionContext = UserUtil.getCurrentSelectionContext(targetZone);
			if (!targetZone.isAlternate() && selectionContext != null && selectionContext.getAlternateLayout() != null)
				targetZone = selectionContext.getAlternateLayout().findZoneByParent(targetZone);

			if (targetZone == null)
				return zone;
		}
		return targetZone;
	}

	public static Zone findById(long id) {
		return HibernateUtil.getManager().getObject(Zone.class, id);
	}

	public static Zone findByNameAndDocument(String name, Document document) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("name", name));
		critList.add(MessagepointRestrictions.eq("document.id", document.getId()));

		List<Zone> zoneList = HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList);
		if (zoneList != null && !zoneList.isEmpty()) {
			return zoneList.get(0);
		}
		return null;
	}

	public static List<Zone> findByDocumentIdOrderByName(long documentId) {
		return findByDocumentIdOrderByName(documentId, true);
	}
	public static List<Zone> findByDocumentIdOrderByName(long documentId, boolean includePlaceholders) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("document.id", documentId));
		if ( !includePlaceholders )
			critList.add(MessagepointRestrictions.ne("zoneTypeId", ZoneType.ID_PLACEHOLDER_ZONE));
		List<Zone> orderedList = HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList, MessagepointOrder.asc("name"));
		return orderedList;
	}

	public static List<Zone> findPlaceholdersByDocumentId(Document document) {

		document = document.getRootDocument();

		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("document.id", document.getId()));
		critList.add(MessagepointRestrictions.eq("zoneTypeId", ZoneType.ID_PLACEHOLDER_ZONE));
		critList.add(MessagepointRestrictions.eq("enabled", true));

		List<Zone> orderedList = HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList, MessagepointOrder.asc("name"));
		return orderedList;
	}

	public List<Zone> findAllAlternates() {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("parentObject.id", this.getId()));

		return HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList);
	}

	public static List<Zone> findCommunicationZonesByDocument(long documentId) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();

		long targetDocumentId = documentId;
		Document document = Document.findById(documentId);
		if ( document.isAlternate() )
			targetDocumentId = document.getParent().getId();

		critList.add(MessagepointRestrictions.eq("document.id", targetDocumentId));
		critList.add(MessagepointRestrictions.eq("zoneTypeId", ZoneType.ID_INTERACTIVE_ZONE));

		List<Zone> orderedList = HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList, MessagepointOrder.asc("name"));

		List<Zone> returnList = new ArrayList<>();
		if ( document.isAlternate() ) {
			for ( Zone currentZone: orderedList ) {
				if ( document.findZoneByParent(currentZone) != null )
					returnList.add( document.findZoneByParent(currentZone) );
			}
		} else {
			returnList = orderedList;
		}

		return returnList;
	}

	public ZonePart findPartByParent(ZonePart parentPart) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(
			MessagepointRestrictions.and(
				MessagepointRestrictions.isNotNull("parentObject"), MessagepointRestrictions.eq("parentObject.id", parentPart.getId())
			)
		);
		critList.add(MessagepointRestrictions.eq("zone.id", this.getId()));

		List<ZonePart> partList =  HibernateUtil.getManager().getObjectsAdvanced(ZonePart.class, critList);
		if ( partList != null && !partList.isEmpty())
			return partList.get(0);
		return null;
	}

	public static List<Zone> findAll() {

		List<Zone> zoneList= new ArrayList<>();
		List<Document> docList = Document.findAll();
		if ( docList != null ) {
			for (Iterator<Document> iterator = docList.iterator(); iterator.hasNext();) {
				Document doc = iterator.next();
				if ( doc != null )
					zoneList.addAll(doc.getZones());
			}
		}

		return zoneList;
	}

	public static List<Zone> findByDataGroup(long dataGroupId) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("dataGroup.id", dataGroupId));
		List<Zone> orderedList = HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList, MessagepointOrder.asc("name"));
		return orderedList;
	}

	public long getCombinedContentTypeId() {
		long combinedContentTypeId = 0;
		if ( this.getContentTypeId() > 0 ) {
    		if ( this.getSubContentTypeId()  >0 )
    			combinedContentTypeId = this.getContentTypeId() * 100 + this.getSubContentTypeId();
    		else
    			combinedContentTypeId = this.getContentTypeId();
    	}
		return combinedContentTypeId;
	}

	public void setCombinedContentTypeId(long combinedContentTypeId) {
		if ( combinedContentTypeId > 100 ) {
			long contentTypeId = combinedContentTypeId/100;
			if ( contentTypeId > 0 )
				this.setContentTypeId(contentTypeId);
			long subTypeId = combinedContentTypeId%100;
			if ( subTypeId > 0 )
				this.setSubContentTypeId(subTypeId);
		}else{
			this.setContentTypeId(combinedContentTypeId);
			this.setSubContentTypeId(0);
		}
	}

	public Boolean containsContentType(long contentTypeIdToMatch) {
		if ( this.getContentTypeId() != contentTypeIdToMatch )
			if ( ( this.getContentTypeId() == ContentType.TEXT_OR_GRAPHIC && (contentTypeIdToMatch == ContentType.TEXT || contentTypeIdToMatch == ContentType.GRAPHIC)) ||
				 (contentTypeIdToMatch == ContentType.TEXT_OR_GRAPHIC && ( this.getContentTypeId() == ContentType.TEXT || this.getContentTypeId() == ContentType.GRAPHIC))	)
				return true;
			else
				return false;
		else
			return true;
	}

	public ContentType getContentType() {
		return ContentType.findById(this.getContentTypeId());
	}

	public String getContentTypeDisplayName() {

		if (this.isFlowZone() )
			return getMessage("page.label.flow");

		if (this.getZoneTypeId() == ZoneType.ID_MARKUP_ZONE)
			return getMessage("page.label.markup");

		if (this.isFreeformPageZone() || this.isFreeform())
			return getMessage("page.label.freeform");

		if (this.isPDFPageZone() )
			return getMessage("page.label.pdf");

		return getMessage(this.getContentType().getName()) +
				(this.isInteractive() ? " (" + getMessage("page.label.interactive") + ")" : "");
	}
	
	public SubContentType getSubContentType() {
		return SubContentType.findById(this.getSubContentTypeId());
	}

	public ZoneType getZoneType() {
		return new ZoneType(this.getZoneTypeId());
	}

	public String getGraphicType() {
		if ( this.getSubContentTypeId() > 0 )
			return this.getSubContentType().getName();
		return "";
	}

	public String getGraphicTypeRegEx() {
		//"/(\.|\/)(gif|jpe?g|pdf|rtf|tif?f|img)$/i"
		String graphicType = "";
		if (subContentTypeId == SubContentType.GRAPHIC_GIF)
			graphicType = "gif";
		else if (subContentTypeId == SubContentType.GRAPHIC_JPEG)
			graphicType = "jpe?g";
		else if (subContentTypeId == SubContentType.GRAPHIC_RTF)
			graphicType = "rtf";
		else if (subContentTypeId == SubContentType.GRAPHIC_TIFF)
			graphicType = "tif?f";
		else if (subContentTypeId == SubContentType.GRAPHIC_IMG)
			graphicType = "img";
		else if (subContentTypeId == SubContentType.GRAPHIC_PDF)
			graphicType = "pdf";
		else if (subContentTypeId == SubContentType.GRAPHIC_PNG)
			graphicType = "png";
		else if (subContentTypeId == SubContentType.GRAPHIC_WEB)
			graphicType = "WEB";
		else if (subContentTypeId == SubContentType.GRAPHIC_DXF)
			graphicType = "dxf";
		else if (subContentTypeId == SubContentType.GRAPHIC_DLF)
			graphicType = "dlf";
		else if (subContentTypeId == SubContentType.GRAPHIC_DOCX)
			graphicType = "docx";
		else if (subContentTypeId == SubContentType.GRAPHIC_EPS)
			graphicType = "eps";
		else if (subContentTypeId == SubContentType.GRAPHIC_PSEG)
			graphicType = "pseg";

		return graphicType;
	}

	public boolean isVisible(User user) {
		return isVisible(user.getWorkgroup());
	}

	public boolean isVisible(Workgroup workgroup) {
		if ( workgroup != null ) {
			Set<Zone> workgroupZones = workgroup.getZones();
			Zone targetZone = this;
			if ( !this.overrideWorkgroups && this.getParent() != null )
				targetZone = this.getParent();
			if ( workgroupZones != null && workgroupZones.contains(targetZone) )
				return true;
		}
		return false;
	}

	public boolean getIsGraphicUploadOnly() {
		return this.isRestrictSharedAssets() && this.getImageAssets().isEmpty() && this.isCommunicationContentEditing();
	}

	public boolean isPrintContent() {
		return this.getZoneTypeId() == ZoneType.ID_DOCUMENT_ZONE;
	}

	public boolean isEmailContent() {
		return this.getZoneTypeId() == ZoneType.ID_EMAIL_ZONE;
	}

	public boolean isEmailSubjectLine() {
		return this.getZoneTypeId() == ZoneType.ID_EMAIL_SUBJECT_LINE_ZONE;
	}
	public boolean isWebTitleLine() {
		return this.getZoneTypeId() == ZoneType.ID_WEB_TITLE_LINE_ZONE;
	}
	public boolean getIsEmailSubjectLine() {
		return isEmailSubjectLine();
	}
	public boolean getIsWebTitleLine(){
		return isWebTitleLine();
	}

	public boolean isSms() {
		return this.getZoneTypeId() == ZoneType.ID_SMS_ZONE;
	}
	public boolean getIsSms() {
		return isSms();
	}

	public boolean isWeb() {
		return this.getZoneTypeId() == ZoneType.ID_WEB_ZONE;
	}
	public boolean getIsWeb() {
		return isSms();
	}

	public boolean isInteractive() {
		return this.getZoneTypeId() == ZoneType.ID_INTERACTIVE_ZONE;
	}

	public boolean isFreeformPageZone() {
		return this.getZoneTypeId() == ZoneType.ID_FREEFORM_PAGE_ZONE;
	}

	public boolean isPDFPageZone() {
		return this.getZoneTypeId() == ZoneType.ID_PDF_PAGE_ZONE;
	}
	public boolean getIsPDFPageZone() {
		return isPDFPageZone();
	}

	public int getPartRelativeLocation( ZonePart zp )
	{
	    List<ZonePart> parts = getPartsInOrder();
	    for( int index = 0; index != parts.size(); ++index )
	    {
	        ZonePart part = parts.get(index);
	        if ( part.getId() == zp.getId() )
	            return index + 1;
	    }
	    return 0;
	}

	public static Boolean areZoneSimilarSize( Zone zoneA, Zone zoneB )
	{
		BigDecimal widthA = new BigDecimal(zoneA.getWidth());
		int widthARounded = Math.round( widthA.divide(DecimalValueUtil.HYDRATE_FACTOR).floatValue() );
		BigDecimal widthB = new BigDecimal(zoneB.getWidth());
		int widthBRounded = Math.round( widthB.divide(DecimalValueUtil.HYDRATE_FACTOR).floatValue() );

		BigDecimal heightA = new BigDecimal(zoneA.getHeight());
		int heightARounded = Math.round( heightA.divide(DecimalValueUtil.HYDRATE_FACTOR).floatValue() );
		BigDecimal heightB = new BigDecimal(zoneB.getHeight());
		int heightBRounded = Math.round( heightB.divide(DecimalValueUtil.HYDRATE_FACTOR).floatValue() );

		if ( widthARounded == widthBRounded && heightARounded == heightBRounded )
			return true;
	    return false;
	}

	static private class ZonePartSequenceComparator implements Comparator<ZonePart>
    {
    	@Override
		public int compare(ZonePart part1, ZonePart part2)
    	{
    		//The basic checks.
    		if (part1 != null && part2 == null)
    			return 1;
    		else if (part1 == null && part2 != null)
    			return -1;
    		else if (part1 == null && part2 == null)
    			return 0;

    		if ( part1.getSequence() == null && part2.getSequence() == null)
    		    return part1.getCreated().compareTo(part2.getCreated());

    		if ( part1.getSequence() == null && part2.getSequence() != null)
    		    return -1;

            if ( part1.getSequence() != null && part2.getSequence() == null)
                return 1;

    		if ( part1.getSequence() < part2.getSequence() )
    			return -1;

    		else if ( part1.getSequence() > part2.getSequence() )
    			return 1;

    		return 0;
    	}
    }

	public String getCanvasDimensions() {

		if ( this.isPlaceholder() )
			return "0:0";

		// If: Alternate Layout
		TouchpointSelection currentSelection 	= UserUtil.getCurrentSelectionContext(this);
		DocumentSection alternateSection 		= null;
		Zone alternateZone 						= null;
		if ( currentSelection != null && currentSelection.getDocument().getId() == this.getDocument().getId() &&
			 currentSelection.getAlternateLayout() != null ) {
			Document alternateDocument 	= currentSelection.getAlternateLayout();
			alternateSection 			= alternateDocument.findSectionByParent(this.getSection());
			alternateZone 				= alternateDocument.findZoneByParent(this);
		}

		DocumentSection section = alternateSection != null ? alternateSection : this.getSection();
		Zone zone = alternateZone != null ? alternateZone : this;
		int layoutTypeId 		= section.getLayoutType();
		double sectionWidth 	= 8.5;
		double sectionHeight 	= 11;
		double mT = 0, mR = 0, mB = 0, mL = 0, hH = 0, fH = 0, lW = 0, rW = 0;
		
		if ( zone.getZoneTypeId() == ZoneType.ID_EMAIL_SUBJECT_LINE_ZONE || zone.getZoneTypeId() == ZoneType.ID_WEB_TITLE_LINE_ZONE)
			return "7:0.125";

		try {

			mT = Double.valueOf( DecimalValueUtil.dehydrate(section.getMarginTop() ));
			mR = Double.valueOf( DecimalValueUtil.dehydrate(section.getMarginRight() ));
			mB = Double.valueOf( DecimalValueUtil.dehydrate(section.getMarginBottom() ));
			mL = Double.valueOf( DecimalValueUtil.dehydrate(section.getMarginLeft() ));
			hH = Double.valueOf( DecimalValueUtil.dehydrate(section.getHeaderHeight() ));
			fH = Double.valueOf( DecimalValueUtil.dehydrate(section.getFooterHeight() ));
			lW = Double.valueOf( DecimalValueUtil.dehydrate(section.getRegionLeftWidth() ));
			rW = Double.valueOf( DecimalValueUtil.dehydrate(section.getRegionRightWidth() ));

			if ( layoutTypeId == DocumentSectionLayoutType.ID_CUSTOM ) {
				sectionHeight = Double.valueOf( DecimalValueUtil.dehydrate(section.getHeight()) );
				sectionWidth = Double.valueOf( DecimalValueUtil.dehydrate(section.getWidth()) );
			} else if ( layoutTypeId > 0 ) {
				DocumentSectionLayoutType documentSectionLayoutType = new DocumentSectionLayoutType(layoutTypeId);
				sectionWidth = Double.valueOf( documentSectionLayoutType.getWidth() );
				sectionHeight = Double.valueOf( documentSectionLayoutType.getHeight() );
			}

			// MARGINS, HEADER, FOOTER: Canvas size adjustments
			sectionWidth = sectionWidth - mR - rW - mL - lW;
			sectionHeight = sectionHeight - mT - hH - fH - mB;

		} catch (ParseException e) {
			e.printStackTrace();
		}

		if ( section.getSectionType() == DocumentSection.SECTION_TYPE_FREEFORM || section.getSectionType() == DocumentSection.SECTION_TYPE_PDF ) {
			return sectionWidth + ":" + sectionHeight;
		} else {
			return
				DocumentTag.getDimensionFromRelativeValue( Double.parseDouble(String.valueOf(zone.getWidth())), sectionWidth) + ":" +
				DocumentTag.getDimensionFromRelativeValue( Double.parseDouble(String.valueOf(zone.getHeight())), sectionHeight );
		}

	}

	public Set<TextStyle> getStylesMergedForAlternates() {
		Set<TextStyle> styles = new HashSet<>();

		List<Zone> zones = getAlternateZones();
		zones.add( getPrimaryZone() );
		for ( Zone currentZone: zones )
			styles.addAll( currentZone.getStyles() );

		return styles;
	}
	public Zone getPrimaryZone() {
		Zone zone = this;
		while ( zone.getParent() != null )
			zone = zone.getParent();
		return zone;
	}
	public List<Zone> getAlternateZones() {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(
			MessagepointRestrictions.and(
				MessagepointRestrictions.isNotNull("parentObject"),
				MessagepointRestrictions.eq("parentObject.id", getPrimaryZone().getId())
			)
		);
		List<Zone> zones = HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList, MessagepointOrder.asc("name"));

		if ( zones != null )
			return zones;
		else
			return new ArrayList<>();
	}

	public boolean isDigitalAlternate() {
		return (this.getDocument().isEmailTouchpoint() || this.getDocument().isWebTouchpoint()) && (this.getDocument().isChannelAlternate() || this.getDocument().isAlternate());
	}
	public boolean isDigitalChannelAlternate() {
		return (this.getDocument().isEmailTouchpoint() || this.getDocument().isWebTouchpoint()) && this.getDocument().isChannelAlternate();
	}

    public static Zone findByDnaAndDocumentNotUsed(Zone family, Document clonedDocument) {
        String dna = family.getDna();
        return findByDnaAndDocument(dna, clonedDocument);
    }

    public static Zone findByDnaAndDocument(String dna, Document clonedDocument) {
        ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		if(clonedDocument != null) {
			critList.add(MessagepointRestrictions.eq("document.id", clonedDocument.getId()));
		}
        critList.add(MessagepointRestrictions.eq("dna", dna));
        return HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList, MessagepointOrder.asc("name")).stream().findFirst().orElse(null);
    }

    public boolean isPlaceholder() {
    	return this.getZoneTypeId() == ZoneType.ID_PLACEHOLDER_ZONE;
    }

    public static JSONArray getListAsJSON(List<Zone> list) {
    	JSONArray jsonList = new JSONArray();

    	for ( Zone currentItem: list ) {
    		JSONObject jsonItem = new JSONObject();

    		try {
				jsonItem.put("name"	, currentItem.getFriendlyName());
				jsonItem.put("id"	, currentItem.getId());

			} catch (JSONException e) {
				log.error("Error generating zone JSON list");
			}

    		jsonList.put(jsonItem);
    	}

    	return jsonList;
    }

    public boolean isNeedRecalculateHash() {
        return needRecalculateHash;
    }

    public void setNeedRecalculateHash(boolean needRecalculateHash) {
        this.needRecalculateHash = needRecalculateHash;
    }

    @Override
    public String getSha256Hash() {
        return sha256Hash;
    }

    @Override
    public void setSha256Hash(String sha256Hash) {
        this.sha256Hash = sha256Hash;
    }

    public void makeHash(boolean isAlgorithmChanged) {
        StringBuilder hashDataStringBuilder = new StringBuilder();

        hashDataStringBuilder.append("zone");

        if(name != null) {
        	hashDataStringBuilder.append(" name:").append(name);
        }

        if(description != null) {
        	hashDataStringBuilder.append(" description:").append(description);
        }

    	hashDataStringBuilder.append(" contentTypeId:").append(contentTypeId);
    	hashDataStringBuilder.append(" subContentTypeId:").append(subContentTypeId);
    	hashDataStringBuilder.append(" zoneTypeId:").append(zoneTypeId);

    	hashDataStringBuilder.append(" topX:").append(topX);
    	hashDataStringBuilder.append(" topY:").append(topY);
    	hashDataStringBuilder.append(" width:").append(width);
    	hashDataStringBuilder.append(" height:").append(height);

    	if(defaultCanvasWidth != null) {
        	hashDataStringBuilder.append(" defaultCanvasWidth:").append(defaultCanvasWidth);
    	}

    	hashDataStringBuilder.append(" page:").append(page);
    	hashDataStringBuilder.append(" rotationAngle:").append(rotationAngle);
    	hashDataStringBuilder.append(" backgroundColor:").append(backgroundColor);

    	if(section != null) {
    		hashDataStringBuilder.append(" section:").append(section.getDna());
    	}

    	if(friendlyName != null) {
    		hashDataStringBuilder.append(" friendlyName:").append(friendlyName);
    	}

    	if(mixedDataGroups != null && mixedDataGroups.booleanValue()) {
    		hashDataStringBuilder.append(" mixedDataGroups:").append(mixedDataGroups);
    	}

    	if(defaultTextStyle != null) {
    		hashDataStringBuilder.append(" defaultTextStyle:").append(defaultTextStyle.getIdentifier());
    	}

    	if(styles != null && !styles.isEmpty()) {
    		List<String> styleIdentifiersList = styles.stream().map(s->s.getIdentifier()).collect(Collectors.toList());
    		Collections.sort(styleIdentifiersList);
    		String styleIdentifiersString = styleIdentifiersList.stream().sequential().collect(Collectors.joining(","));
    		hashDataStringBuilder.append(" styles:").append(styleIdentifiersString);
    	}

    	if(defaultParagraphStyle != null) {
    		hashDataStringBuilder.append(" defaultParagraphStyle:").append(defaultParagraphStyle.getIdentifier());
    	}

    	if(paragraphStyles != null && !paragraphStyles.isEmpty()) {
    		List<String> styleIdentifiersList = paragraphStyles.stream().map(s->s.getIdentifier()).collect(Collectors.toList());
    		Collections.sort(styleIdentifiersList);
    		String styleIdentifiersString = styleIdentifiersList.stream().sequential().collect(Collectors.joining(","));
    		hashDataStringBuilder.append(" paragraphStyles:").append(styleIdentifiersString);
    	}

    	if(defaultListStyle != null) {
    		hashDataStringBuilder.append(" defaultListStyle:").append(defaultListStyle.getIdentifier());
    	}

    	if(listStyles != null && !listStyles.isEmpty()) {
    		List<String> styleIdentifiersList = listStyles.stream().map(s->s.getIdentifier()).collect(Collectors.toList());
    		Collections.sort(styleIdentifiersList);
    		String styleIdentifiersString = styleIdentifiersList.stream().sequential().collect(Collectors.joining(","));
    		hashDataStringBuilder.append(" listStyles:").append(styleIdentifiersString);
    	}

		hashDataStringBuilder.append(" dxfOutput:").append(dxfOutput);

		hashDataStringBuilder.append(" supportsTables:").append(supportsTables);
		hashDataStringBuilder.append(" supportsForms:").append(supportsForms);
		hashDataStringBuilder.append(" supportsBarcodes:").append(supportsBarcodes);
		hashDataStringBuilder.append(" supportsCustomParagraphs:").append(supportsCustomParagraphs);
		hashDataStringBuilder.append(" freeform:").append(freeform);

		if(zoneAttributes != null && !zoneAttributes.isEmpty()) {
			List<String> zoneAttributeNamesList = new ArrayList<>(zoneAttributes.keySet());
			Collections.sort(zoneAttributeNamesList);
			hashDataStringBuilder.append(" zoneAttributes:{");
			boolean isFirst = true;
			for(String zoneAttributeName : zoneAttributeNamesList) {
				String zoneAttributeValue = zoneAttributes.get(zoneAttributeName);
				if(! isFirst) {
					hashDataStringBuilder.append(", ");
				}
				hashDataStringBuilder.append(zoneAttributeName).append("=").append(zoneAttributeValue);
				isFirst = false;
			}
			hashDataStringBuilder.append("}");
		}

		if(parts != null && ! parts.isEmpty()) {
			List<ZonePart> list = getPartsInSequenceOrder();
			String zonePartDnas = list.stream().sequential().map(ZonePart::getDna).collect(Collectors.joining(","));
			hashDataStringBuilder.append(" zonePartDnas:").append(zonePartDnas);
		}

		if(defaultCommunicationTemplateImage != null) {
			hashDataStringBuilder.append(" defaultCommunicationTemplateImage:").append(defaultCommunicationTemplateImage.getDna());
		}

		if(defaultCommunicationTemplateSmartText != null) {
			hashDataStringBuilder.append(" defaultCommunicationTemplateSmartText:").append(defaultCommunicationTemplateSmartText.getDna());
		}

		if (communicationDataImageVariable != null) {
			hashDataStringBuilder.append(" communicationDataImageVariable: ").append(communicationDataImageVariable.getDna());
		}

		hashDataStringBuilder.append(" communicationContentEditing:").append(communicationContentEditing);

		hashDataStringBuilder.append(" restrictSharedAssets:").append(restrictSharedAssets);

		if(imageAssets != null && !imageAssets.isEmpty()) {
			List<String> imageAssetsDNAList = imageAssets.stream().map(image->image.getDna()).collect(Collectors.toList());
			Collections.sort(imageAssetsDNAList);
			String imageAssetsDnas = imageAssetsDNAList.stream().sequential().collect(Collectors.joining(","));
			hashDataStringBuilder.append(" imageAssetsDnas:").append(imageAssetsDnas);
		}

		if(smartTextAssets != null && !smartTextAssets.isEmpty()) {
			List<String> smartTextAssetsDNAList = smartTextAssets.stream().map(smartText->smartText.getDna()).collect(Collectors.toList());
			Collections.sort(smartTextAssetsDNAList);
			String smartTextAssetsDnas = smartTextAssetsDNAList.stream().sequential().collect(Collectors.joining(","));
			hashDataStringBuilder.append(" smartTextAssetsDnas:").append(smartTextAssetsDnas);
		}

		hashDataStringBuilder.append(" messageContentEditing:").append(messageContentEditing);

		hashDataStringBuilder.append(" applyAltText:").append(applyAltText);
		hashDataStringBuilder.append(" applyImageLink:").append(applyImageLink);
		hashDataStringBuilder.append(" applyImageExtLink:").append(applyImageExtLink);
		hashDataStringBuilder.append(" applyImageExtPath:").append(applyImageExtPath);

		hashDataStringBuilder.append(" overrideName:").append(overrideName);
		hashDataStringBuilder.append(" overrideFriendlyName:").append(overrideFriendlyName);
		hashDataStringBuilder.append(" overridePosition:").append(overridePosition);
		hashDataStringBuilder.append(" overrideDimensions:").append(overrideDimensions);
		hashDataStringBuilder.append(" overrideDocumentSection:").append(overrideDocumentSection);
		hashDataStringBuilder.append(" overrideDefaultTextStyle:").append(overrideDefaultTextStyle);
		hashDataStringBuilder.append(" overrideTextStyles:").append(overrideTextStyles);
		hashDataStringBuilder.append(" overrideDefaultParagraphStyle:").append(overrideDefaultParagraphStyle);
		hashDataStringBuilder.append(" overrideParagraphStyles:").append(overrideParagraphStyles);
		hashDataStringBuilder.append(" overrideDefaultListStyle:").append(overrideDefaultListStyle);
		hashDataStringBuilder.append(" overrideListStyles:").append(overrideListStyles);
		hashDataStringBuilder.append(" overrideBackgroundColor:").append(overrideBackgroundColor);
		hashDataStringBuilder.append(" overrideSharedAssets:").append(overrideSharedAssets);
		hashDataStringBuilder.append(" overrideCommunicationTemplate:").append(overrideCommunicationTemplate);

        String objectHashKey = getObjectHashKey();
    	sha256Hash = calculateSha256Hash(objectHashKey, isAlgorithmChanged, sha256Hash, hashDataStringBuilder.toString());
    }

	public TreeMap<Long, Long> getInitJobPackerPartTreeMap()
	{
		TreeMap<Long, Long> res = new TreeMap<>();
		for( long index = 0; index != parts.size(); ++index )
		{
			res.put(index+1, 0L);
		}
		return res;
	}

	public static List<Zone> getZonesListByTargetedFlowZone(Zone targetedFlowZone) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("flowIntoZone.id", targetedFlowZone.getId()));
		List<Zone> zoneList = HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList);
		if (zoneList != null && !zoneList.isEmpty()) {
			return zoneList;
		}
		return new ArrayList<>();
	}

	public boolean isThisEnabled() {
		return this.enabled;
	}

}