package com.prinova.messagepoint.model.util;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.util.*;
import org.apache.commons.logging.Log;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.hibernate.Hibernate;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.orm.hibernate5.SessionHolder;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.controller.content.StringsDiffUtils;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;

import com.prinova.messagepoint.model.*;

import com.prinova.messagepoint.model.common.ContentSelectionStatusType;
import com.prinova.messagepoint.model.common.ContentSelectionType;
import com.prinova.messagepoint.model.common.SyncObjectType;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.security.User;

import com.prinova.messagepoint.model.dataadmin.LookupTable;
import com.prinova.messagepoint.model.targeting.ConditionElement;


import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.targeting.Targetable;
import com.prinova.messagepoint.model.tenant.TenantMetaData;
import com.prinova.messagepoint.model.version.ModelVersionMapping;
import com.prinova.messagepoint.model.version.VersionedInstance;
import com.prinova.messagepoint.model.version.VersionedModel;
import com.prinova.messagepoint.model.wrapper.ProjectSyncObjectListVO;
import com.prinova.messagepoint.platform.services.backgroundtask.ExportMessagepointObjectBackgroundTask;
import com.prinova.messagepoint.platform.services.export.ExportUtil;
import com.prinova.messagepoint.platform.services.export.ExportXMLUtils;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;


public class CompareReport {
	//private static final Log log = LogUtil.getLog(CompareReport.class);
	private static final Log log = LogUtil.getLog(ExportMessagepointObjectBackgroundTask.class);
	public static final String PATH_SEPARATOR 		= "/";
	
	public static final String EXTENSION_HTML 		= "html";
	public static final String EXTENSION_XML 		= "xml";
	public static final String EXTENSION_ZIP 		= "zip";

	public static final String REPORT_NAME_PREFIX	= "compareReport";
	public static final String DEFAULT_XSL_PATH		= ApplicationUtil.getRootPath() + "/WEB-INF/xslt/compareReportToXHTML.xsl";
	
	private final long projectDocumentId;
	private final long otherDocumentId;
	private final long otherInstanceId;
	private final boolean syncFromOther;
	private final List<String> selectedObjects;
	
	private User						requestor;
	private org.dom4j.Document			document;
	
	private boolean 					syncWithParent;
	private boolean 					syncMultiWay;
	private long 						siblingParentInstanceId;
	private long 						siblingParentId;
	private long 						parentInstanceId;
	private long 						parentId;
	private long 						localContext;
	
	private long						instanceId;
	private String						otherSchema;
	private String 						currentSchema;
	
	private com.prinova.messagepoint.model.Document projectDocument;
	private com.prinova.messagepoint.model.Document otherDocument;
	
	private Map<ContentObject, ProjectSyncObjectListVO> 		aaData = new HashMap<>();
	private int 				contentObjectFilter;
	
	private Map<Object, Long> 	objectMap = new LinkedHashMap<>();
	
	private Multimap<Long, Map<Long, Long>> syncObjectMap = ArrayListMultimap.create();	// objectType, objectId, objectStatus
    private Map<Long, Long> embeddedContentObjectMap = new LinkedHashMap<>();	// objectId, objectStatus
    private Map<Long, Long> contentLibraryObjectMap = new LinkedHashMap<>();  // objectId, objectStatus
	private Map<Long, Long> messageObjectMap = new LinkedHashMap<>();			// objectId, objectStatus
    private Map<Long, Long> dataSourceObjectMap = new LinkedHashMap<>();	// objectId, objectStatus
    private Map<Long, Long> lookupTableObjectMap = new LinkedHashMap<>();	// objectId, objectStatus
    private Map<Long, Long> dataCollectionObjectMap = new LinkedHashMap<>();	// objectId, objectStatus
    private Map<Long, Long> variableObjectMap = new LinkedHashMap<>();	// objectId, objectStatus
    private Map<Long, Long> parameterGroupObjectMap = new LinkedHashMap<>();	// objectId, objectStatus
    private Map<Long, Long> targetRuleObjectMap = new LinkedHashMap<>();	// objectId, objectStatus
    private Map<Long, Long> targetGroupObjectMap = new LinkedHashMap<>();	// objectId, objectStatus

	public CompareReport( long projectDocumentId, boolean syncFromOther, long otherDocumentId, long otherInstanceId, 
			boolean syncMultiWay, long siblingParentInstanceId, long siblingParentId, long parentInstanceId, long parentId,
			List<String> selectedObjects, User requestor )
	{
		this.projectDocumentId = projectDocumentId;
		this.otherDocumentId = otherDocumentId;
		this.otherInstanceId = otherInstanceId;
		this.syncFromOther = syncFromOther;
		this.syncMultiWay = syncMultiWay;
		this.parentInstanceId = parentInstanceId;
		this.parentId = parentId;
		this.siblingParentInstanceId = siblingParentInstanceId;
		this.siblingParentId = siblingParentId;
		this.selectedObjects = selectedObjects;
		this.requestor = requestor;
	}
	
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	
	public void setDocument(org.dom4j.Document document){
		this.document = document;
	}
	
	public org.dom4j.Document getDocument() {
		return this.document;
	}
	
	public static String getReportsRootPath() {
		return ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ReportDir);
	}
	
	public boolean isSyncFromOther() {
		return this.syncFromOther;
	}
	public boolean isSyncWithParent() {
		return syncWithParent;
	}
	public void setSyncWithParent(boolean syncWithParent) {
		this.syncWithParent = syncWithParent;
	}
	public long getLocalContext() {
		return localContext;
	}
	public void setLocalContext(long localContext) {
		this.localContext = localContext;
	}
	
	public com.prinova.messagepoint.model.Document getProjectDocument() {
		return projectDocument;
	}
	public void setProjectDocument(com.prinova.messagepoint.model.Document projectDocument) {
		this.projectDocument = projectDocument;
	}
	public com.prinova.messagepoint.model.Document getOtherTouchpoint() {
		return otherDocument;
	}
	public void setOtherTouchpoint(com.prinova.messagepoint.model.Document otherDocument) {
		this.otherDocument = otherDocument;
	}
	
	public int getContentObjectFilter() {
		return contentObjectFilter;
	}
	public void setContentObjectFilter(int contentObjectFilter) {
		this.contentObjectFilter = contentObjectFilter;
	}
	public long getInstanceId() {
		return instanceId;
	}
	public void setInstanceId(long instanceId) {
		this.instanceId = instanceId;
	}
	public String getCurrentSchema() {
		return currentSchema;
	}

	public void setCurrentSchema(String currentSchema) {
		this.currentSchema = currentSchema;
	}

	/**
	 * Entry point to generate the report xml file
     */
	public void generateReportXML() throws Exception{
		this.document = DocumentHelper.createDocument();
		document.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);

		// Build the root "TouchpointCompare" element
		Element messageExportElement = document.addElement("TouchpointCompare");
		this.document.setRootElement(messageExportElement);
		
		Set<Document> documents = new HashSet<>();
		this.projectDocument = Document.findById(projectDocumentId);
		documents.add(this.projectDocument);
		Node currentNode = Node.getCurrentNode();
	    this.currentSchema = currentNode.getSchemaName();
        Node otherNode = currentNode;
	    this.otherSchema = otherNode.getSchemaName();
	    if(this.otherInstanceId != -1 && this.otherInstanceId != currentNode.getId()) {
	        otherNode = Node.findById(otherInstanceId);
            if(otherNode != null) {
                String otherSchema = otherNode.getSchemaName();
                if(! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(otherSchema)) {
                    this.otherSchema = otherSchema;
                }
            }
        }

        String sourceSchema = syncFromOther ? otherSchema : currentSchema;
        String targetSchema = syncFromOther ? currentSchema : otherSchema;
        
        Node targetNode = syncFromOther ? currentNode : otherNode;
        if(syncFromOther) {
            this.projectDocument = CloneHelper.queryInSchema(targetSchema, ()->Document.findById(projectDocumentId));
            this.otherDocument = CloneHelper.queryInSchema(sourceSchema, ()->Document.findById(otherDocumentId));
        } else {
            this.projectDocument = CloneHelper.queryInSchema(sourceSchema, ()->Document.findById(projectDocumentId));
            this.otherDocument = CloneHelper.queryInSchema(targetSchema, ()->Document.findById(otherDocumentId));
        }
		
        this.syncWithParent = (this.projectDocument.getObjectSchemaName().equals(this.otherSchema) && projectDocument != null && projectDocument.getId() == projectDocument.getId());

		TouchpointSelection touchpointSelection = CloneHelper.queryInSchema(this.projectDocument.getObjectSchemaName(),  ()->this.projectDocument.getMasterTouchpointSelection());
		TouchpointSelection otherTPSelection = CloneHelper.queryInSchema(this.otherDocument.getObjectSchemaName(),  ()->this.otherDocument.getMasterTouchpointSelection());
		
		
		Map<Document, List<ContentObjectData>> tpSelectableMessages = new HashMap<>();
		Set<ParameterGroupInstanceCollection> pgiCollections = new HashSet<>();
		
		messageExportElement.addAttribute("ver", MessagePointStartUp.getBuildRevision());
		messageExportElement.addAttribute("type", "compare");
		createMetaDataTag(touchpointSelection, messageExportElement);
		
		for (String object : selectedObjects)
		{
			String [] parts = object.split("_");
			if (parts.length < 3)
				continue;
			
			long objectType = Long.parseLong(parts[0]);
			long objectId = Long.parseLong(parts[1]);
			Long objectStatus = Long.parseLong(parts[2]);

			Long objectIdx10;
			if(syncFromOther) {
				if((objectStatus & 0x10000) == 0) {
                    objectIdx10 = objectId * 0x10;
				} else {
                    objectIdx10 = objectId * 0x10 + 0x01;
				}
			} else {
                if((objectStatus & 0x10000) == 0) {
                    objectIdx10 = objectId * 0x10 + 0x01;
                } else {
                    objectIdx10 = objectId * 0x10;
                }
                objectStatus ^= 0x10000;
                long prjStatus = (objectStatus & 0xFF00) >> 8;
                long otherStatus = (objectStatus & 0x00FF) << 8;
                objectStatus = (objectStatus & ~0xFFFF) | prjStatus | otherStatus;
			}
			
			if(objectType == SyncObjectType.ID_SMART_TEXT) {
			    embeddedContentObjectMap.put(objectIdx10, objectStatus);
			} 
			else if(objectType == SyncObjectType.ID_CONTENT_LIBRARY) {
                contentLibraryObjectMap.put(objectIdx10, objectStatus);
			}
			else if (objectType == SyncObjectType.ID_MESSAGE || objectType == SyncObjectType.ID_LOCAL_SMART_TEXT || objectType == SyncObjectType.ID_LOCAL_IMAGE)
			{
				messageObjectMap.put(objectIdx10, objectStatus);
			}
			else if (objectType == SyncObjectType.ID_DATASOURCE)
            {
                dataSourceObjectMap.put(objectIdx10, objectStatus);
            }
            else if (objectType == SyncObjectType.ID_LOOKUPTABLE)
            {
                lookupTableObjectMap.put(objectIdx10, objectStatus);
            }
			else if (objectType == SyncObjectType.ID_DATACOLLECTION)
            {
                dataCollectionObjectMap.put(objectIdx10, objectStatus);
            }
            else if (objectType == SyncObjectType.ID_VARIABLE)
            {
                variableObjectMap.put(objectIdx10, objectStatus);
            }
            else if (objectType == SyncObjectType.ID_PARAMETER_GROUP)
            {
                parameterGroupObjectMap.put(objectIdx10, objectStatus);
            }
            else if (objectType == SyncObjectType.ID_TARGET_RULE)
            {
                targetRuleObjectMap.put(objectIdx10, objectStatus);
            }
            else if (objectType == SyncObjectType.ID_TARGET_GROUP)
            {
                targetGroupObjectMap.put(objectIdx10, objectStatus);
            }
			else
			{	
				Map<Long, Long> objectMap = new LinkedHashMap<>();
				objectMap.put(objectIdx10, objectStatus);
				syncObjectMap.put(objectType, objectMap);
			}
		}

		/* Data Source */
        if(! dataSourceObjectMap.isEmpty()) {
            Element dataSourceListElement = messageExportElement.addElement("DataSources");
            createDataSourceTag(dataSourceObjectMap, documents, dataSourceListElement);
        }

        /* Lookup Table */
        if(! lookupTableObjectMap.isEmpty()) {
            Element lookupTableListElement = messageExportElement.addElement("LookupTables");
            createLookupTableTag(lookupTableObjectMap, documents, lookupTableListElement);
        }

        /* Data collection */
        if(! dataCollectionObjectMap.isEmpty()) {
            Element dataCollectionListElement = messageExportElement.addElement("DataCollections");
            createDataCollectionTag(dataCollectionObjectMap, documents, dataCollectionListElement);
        }

        /* Variable */
        if(! variableObjectMap.isEmpty()) {
            Element variableListElement = messageExportElement.addElement("Variables");
            createVariableTag(variableObjectMap, documents, variableListElement);
        }

        /* Parameter Group */
        if(! parameterGroupObjectMap.isEmpty()) {
            Element parameterGroupListElement = messageExportElement.addElement("SelectorGroups");
            createParameterGroupTag(parameterGroupObjectMap, documents, parameterGroupListElement);
        }

        /* Target Rule */
        if(! targetRuleObjectMap.isEmpty()) {
            Element targetRuleListElement = messageExportElement.addElement("TargetRules");
            createTargetRuleTag(targetRuleObjectMap, documents, targetRuleListElement);
        }

        /* Target Group */
        if(! targetGroupObjectMap.isEmpty()) {
            Element targetGroupListElement = messageExportElement.addElement("TargetGroups");
            createTargetGroupTag(targetGroupObjectMap, documents, targetGroupListElement);
        }

		/* Image Library */
		Element imageLibrariesListElement = messageExportElement.addElement("ImageLibraries");
		if(!contentLibraryObjectMap.isEmpty()){
			createImageLibraryTag(contentLibraryObjectMap, documents, imageLibrariesListElement);
		}
		
		/*
		 * Smart Text
		 */
		Element smartTextListElement = messageExportElement.addElement("SmartTexts");
		if(!embeddedContentObjectMap.isEmpty()){
			createSmartTextTag(embeddedContentObjectMap, smartTextListElement, null);
		}
		
		/*
		 * Messages
		 */
		Set<ContentObjectData> messageList = new HashSet<>();
        List<Long> contentlibraryRefIds = new ArrayList<>();
		Element messagesElement = messageExportElement.addElement("Messages");
		
		if (!messageObjectMap.isEmpty()) {
            createMessageTag(messageObjectMap, messagesElement, null);
/*
			for (Long objectIdx10 : messageObjectMap.keySet())
			{
			    Long objectId = objectIdx10 / 0x10;
				long objectStatus = messageObjectMap.get(objectIdx10);
				boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
				objectStatus = objectStatus & 0xFFFF;
				String objectSchema = objectIsFromOther ? sourceSchema : targetSchema;
				ContentObject msg = CloneHelper.queryInSchema(objectSchema, ()->ContentObject.findById(objectId));
				
				boolean objectConflictExchange = (objectStatus & (AsyncProjectSyncObjectListVO.CONFLICT_MODEL_CHANGE | AsyncProjectSyncObjectListVO.CONFLICT_CUST_CHANGE)) != 0;
	            boolean objectFromOther = (objectStatus & 0x10000) != 0;

	            objectStatus = objectStatus & 0xFFFF;
				
				// Name
				vo.setName(CloneHelper.queryInSchema(msg.getObjectSchemaName(), ()->msg.getName()));			

				int projectStatus = (int) ((objectStatus / 0x100) & 0xF);
				int originStatus = (int) (objectStatus & 0xF);
				
				vo.setSyncFromOrigin(this.syncFromOther);
				if (syncCheckBoxRequest < 0)
					vo.setSyncRequest(false);
				else
					vo.setSyncRequest(true);
				vo.setConflictsExchange(objectConflictExchange);
				if(objectConflictExchange) {
	                if (syncCheckBoxRequest == 0)
	                    vo.setSyncRequest(false);
				}

				vo.setProjectStatus(new ContentSelectionStatusType(originStatus));
				vo.setOriginStatus(new ContentSelectionStatusType(projectStatus));
				if(this.syncFromOther){
					
					vo.setOriginNodeId(this.otherInstanceId);
					
					vo.setProjectTP(this.projectDocument);
					vo.setOriginTP(this.otherDocument);
					
					vo.setProjectSchema(this.projectDocument.getObjectSchemaName());
					vo.setOriginSchema(this.otherDocument.getObjectSchemaName());
					
					if (originStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE && (projectStatus == ContentSelectionStatusType.ID_CHANGED || projectStatus == ContentSelectionStatusType.ID_NEW))
					{
						vo.setConflictsWorkingCopy(true);
						if (syncCheckBoxRequest == 0)
							vo.setSyncRequest(false);
					}
					
				}else{
					vo.setOriginNodeId(this.instanceId);
					
					vo.setProjectTP(this.otherDocument);
					vo.setOriginTP(this.projectDocument);
					
					vo.setProjectSchema(this.otherDocument.getObjectSchemaName());
					vo.setOriginSchema(this.projectDocument.getObjectSchemaName());
					
					if (projectStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE && (originStatus == ContentSelectionStatusType.ID_CHANGED || originStatus == ContentSelectionStatusType.ID_NEW))
					{
						vo.setConflictsWorkingCopy(true);
						if (syncCheckBoxRequest == 0)
							vo.setSyncRequest(false);
					}
				}

				ContentObject message = msg;
				ContentObject messageFinal = message;
				ContentObject otherMessage = objectFromOther ? ContentObject.findByDnaAndDocument(message, this.projectDocument) : CloneHelper.queryInSchema(this.otherSchema, ()->ContentObject.findByDnaAndDocument(messageFinal, this.otherDocument));

				ContentObject targetMessage = otherMessage;
                String targetMsgSchema = otherSchema;
                
				if(objectFromOther) {
				    message = otherMessage;
				    otherMessage = msg;
				    targetMsgSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
				}

				ContentObject refMessage = message;
                String refSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
				if(refMessage == null || (!refMessage.hasWorkingData() && !refMessage.hasActiveData())) {
					refMessage = otherMessage;
					refSchema = otherSchema;
				}

				ContentObject refMessageFinal = refMessage;

                CloneHelper.execInSchema(refSchema, ()->{
    				if (refMessageFinal.getIsTouchpointLocal()) {
    					if ( refMessageFinal.getContentType().getId() == ContentType.GRAPHIC )
    						vo.setObjectType(new SyncObjectType(SyncObjectType.ID_LOCAL_IMAGE));
    					else
    						vo.setObjectType(new SyncObjectType(SyncObjectType.ID_LOCAL_SMART_TEXT));
    				} else {
    					vo.setObjectType(new SyncObjectType(SyncObjectType.ID_MESSAGE));
    				}
                    vo.setName(refMessageFinal.getName());
				});
				
                vo.setName(CloneHelper.queryInSchema(refSchema, ()->refMessageFinal.getName()));
                
				int projectActiveStatus = (int) (objectStatus / 0x1000);
				int originActiveStatus = (int) ((objectStatus & 0xFF) / 0x10);
				
				vo.setProjectStatusActiveCopy(new ContentSelectionStatusType(originActiveStatus));
				vo.setOriginStatusActiveCopy(new ContentSelectionStatusType(projectActiveStatus));
				if (this.syncFromOther)
				{
					if (originActiveStatus != ContentSelectionStatusType.ID_UNCHANGED && originActiveStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE && (projectActiveStatus == ContentSelectionStatusType.ID_CHANGED || projectActiveStatus == ContentSelectionStatusType.ID_NEW))
					{
						vo.setConflictsActiveCopy(true);
						if (syncCheckBoxRequest == 0)
							vo.setSyncRequest(false);
					}
				}
				else
				{
					if (projectActiveStatus != ContentSelectionStatusType.ID_UNCHANGED && projectActiveStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE && (originActiveStatus == ContentSelectionStatusType.ID_CHANGED || originActiveStatus == ContentSelectionStatusType.ID_NEW))
					{
						vo.setConflictsActiveCopy(true);
						if (syncCheckBoxRequest == 0)
							vo.setSyncRequest(false);
					}
				}
				
				if(targetMessage != null) {
					ContentObjectData targetWorkingCopy = CloneHelper.queryInSchema(targetMsgSchema, ()->(ContentObjectData) targetMessage.getWorkingCopy());
                    if(targetWorkingCopy != null) {
                        boolean isMine = CloneHelper.queryInSchema(targetMsgSchema, ()->targetWorkingCopy.getContentObject().isMine());
                        vo.setMyTargetWorkingCopy(isMine);
                    }
				}
				
				if (targetMessage != null)
				{
					//Message otherMessageFinal = otherMessage;
					vo.setOriginMessageInstanceActiveCopy((ContentObjectData) CloneHelper.queryInSchema(vo.getOriginSchema(),  ()->messageFinal.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE)));
					vo.setOriginMessageInstanceWorkingCopy((ContentObjectData) CloneHelper.queryInSchema(vo.getOriginSchema(),  ()->messageFinal.getContentObjectData(ContentObject.DATA_TYPE_WORKING)));
					vo.setProjectMessageInstanceActiveCopy((ContentObjectData) CloneHelper.queryInSchema(vo.getProjectSchema(),  ()->targetMessage.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE)));
					vo.setProjectMessageInstanceWorkingCopy((ContentObjectData) CloneHelper.queryInSchema(vo.getProjectSchema(),  ()->targetMessage.getContentObjectData(ContentObject.DATA_TYPE_WORKING)));
				}
				else
				{
					//vo.setProjectSchema(vo.getProjectSchema());
					if (vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE)
						vo.setProjectMessageInstanceWorkingCopy((ContentObjectData) CloneHelper.queryInSchema(refSchema,  ()->refMessageFinal.getContentObjectData(ContentObject.DATA_TYPE_WORKING)));
						
					if (vo.getProjectStatusActiveCopy().getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE)
						vo.setProjectMessageInstanceActiveCopy((ContentObjectData) CloneHelper.queryInSchema(refSchema,  ()->refMessageFinal.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE)));
					
					//vo.setOriginSchema(vo.getProjectSchema());
					if (vo.getOriginStatus().getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE)
						vo.setOriginMessageInstanceWorkingCopy((ContentObjectData) CloneHelper.queryInSchema(refSchema,  ()->refMessageFinal.getContentObjectData(ContentObject.DATA_TYPE_WORKING)));
						
					if (vo.getOriginStatusActiveCopy().getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE)
						vo.setOriginMessageInstanceActiveCopy((ContentObjectData) CloneHelper.queryInSchema(refSchema,  ()->refMessageFinal.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE)));
				}
				
				if(messageFinal != null){
					vo.setLastesOriginCopy(CloneHelper.queryInSchema(messageFinal.getObjectSchemaName(), ()->messageFinal.getLatestContentObjectDataWorkingCentric()));
				}
				
				ContentObjectData latest = vo.getLastesOriginCopy();
				ContentObject contentObject = latest.getContentObject();
				if(targetMessage != null){
					vo.setLatestProjectCopy(CloneHelper.queryInSchema(targetMessage.getObjectSchemaName(), ()->targetMessage.getLatestContentObjectDataWorkingCentric()));
				}
				
				messageList.add(vo.getLastesOriginCopy());
				
				this.aaData.put(msg, vo);
				if ( contentObject.isStructuredContentEnabled() || contentObject.isVariantType() )
				{
					if(!tpSelectableMessages.containsKey(vo.getOriginTP())){
						tpSelectableMessages.put(vo.getOriginTP(), new ArrayList<ContentObjectData>());
					}
					if(!tpSelectableMessages.get(vo.getOriginTP()).contains(latest))
						tpSelectableMessages.get(vo.getOriginTP()).add(latest);
				}
				List<ContentObjectData> dbMessageInstancelist = new ArrayList<ContentObjectData>();

				if(vo.getOriginMessageInstanceActiveCopy() != null){
					dbMessageInstancelist.add(vo.getOriginMessageInstanceActiveCopy());
				}
				if(vo.getOriginMessageInstanceWorkingCopy() != null){
					dbMessageInstancelist.add(vo.getOriginMessageInstanceWorkingCopy());
				}
				if (!dbMessageInstancelist.isEmpty())
				{
					createMessageTag(vo, dbMessageInstancelist, messagesElement,(objectFromOther?otherTPSelection:touchpointSelection), documents);
				}else
					continue;
				
				boolean switchedSession = false;
		        SessionHolder mainSessionHolder = null;
		        if(latest.getObjectSchemaName() != null && ! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(latest.getObjectSchemaName())) {
		            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(latest.getObjectSchemaName());
		            switchedSession = true;
		        }
		        // Check the image library reference
				for(ContentObjectData mi : dbMessageInstancelist){
		            List<Long> miClIds = CloneHelper.queryInSchema(mi.getObjectSchemaName(),  ()->mi.findAllImageReference());
		            miClIds.removeAll(contentlibraryRefIds);
		            contentlibraryRefIds.addAll(miClIds);
				}
				
				Document messageDocument = contentObject.getFirstDocumentDelivery();
				if (!documents.contains(messageDocument))
				{
					documents.add(messageDocument);
					if(!tpSelectableMessages.containsKey(messageDocument))
						tpSelectableMessages.put(messageDocument, new ArrayList<ContentObjectData>());
				}
				if ( contentObject.isStructuredContentEnabled() ||
						contentObject.isVariantType() )
				{
					if(!tpSelectableMessages.containsKey(messageDocument)){
						tpSelectableMessages.put(messageDocument, new ArrayList<ContentObjectData>());
					}
					if(!tpSelectableMessages.get(messageDocument).contains(latest))
						tpSelectableMessages.get(messageDocument).add(latest);
					pgiCollections.addAll(messageDocument.getParameterGroupInstanceCollections());
				}
				else if (contentObject.isDynamicVariantEnabled())
				{
					pgiCollections.addAll(contentObject.getParameterGroupInstanceCollections());
				}

				// Local Content
				if(contentObject.getIsTouchpointLocal()){
					if (!documents.contains(messageDocument)) {
						documents.add(messageDocument);
					}
					if ( contentObject.isStructuredContentEnabled() || contentObject.isVariantType() ) {
						pgiCollections.addAll(messageDocument.getParameterGroupInstanceCollections());
					}else if (contentObject.isDynamicVariantEnabled()){
						pgiCollections.addAll(contentObject.getParameterGroupInstanceCollections());
					}				
				}
				try {
		            if(switchedSession) {
		                Hibernate.initialize(latest);
		            }
		        } finally {
		            if(switchedSession) {
		                HibernateUtil.getManager().restoreSession(mainSessionHolder);
		            }
		        }
			}

 */
		}
		
		
		/*
		 * RerenceData
		 */
		Element referenceDataElement = messageExportElement.addElement("ReferenceData");
		Element imageLibrariesElement = referenceDataElement.addElement("ImageLibraries");
        createImageLibraryTag(contentlibraryRefIds, documents, imageLibrariesElement);

		if (!pgiCollections.isEmpty()) 
		{
			Element dataValuesElement = referenceDataElement.addElement("DataValues");
			for (ParameterGroupInstanceCollection pgiCollection : pgiCollections) 
			{
				ExportUtil.createDataValueTag(pgiCollection, dataValuesElement);
			}
		}

		Element touchpointsElement = referenceDataElement.addElement("Touchpoints");
		if( this.syncFromOther){
			createTouchpointTag(this.otherDocument, touchpointsElement, messageList, otherTPSelection, tpSelectableMessages);	
		}else{
			createTouchpointTag(this.projectDocument, touchpointsElement, messageList, touchpointSelection, tpSelectableMessages);	
		}
	}
	
	private void createMetaDataTag(TouchpointSelection selection, Element messageExportElement) throws Exception
	{
		Element metadataElement = messageExportElement.addElement("Metadata");
		Element userElement = metadataElement.addElement("User");
		if (requestor != null)
			userElement.addText(requestor.getFullName());
		else
			userElement.addText("System");
		Element requestDateElement = metadataElement.addElement("RequestDate");
		requestDateElement.addText(DateUtil.formatDateForXMLOutput(DateUtil.now()));
		Element settingsElement = metadataElement.addElement("Settings");
		settingsElement.addAttribute("content", Boolean.valueOf(false).toString());
		settingsElement.addAttribute("targeting", Boolean.valueOf(false).toString());
		settingsElement.addAttribute("includeAllMessages", Boolean.valueOf(false).toString());
		settingsElement.addAttribute("syncFromOrigin", Boolean.valueOf(this.syncFromOther).toString());
		settingsElement.addAttribute("syncwWithParent", Boolean.valueOf(this.syncWithParent).toString());
		
		Node currentNode = Node.getCurrentNode();
		String currentTPName = "(" +currentNode.getBranch().getName() +  " " + currentNode.getName() + ") " + this.projectDocument.getName();
		settingsElement.addAttribute("currentTPName", currentTPName);
		
		Node node = Node.findBySchema(this.otherDocument.getObjectSchemaName());
		String tpName = "(" +node.getBranch().getName() +  " " + node.getName() + ") " + this.otherDocument.getName();
		settingsElement.addAttribute("otherTPName", tpName);
		
		if(this.syncMultiWay){
			settingsElement.addAttribute("syncMultiWay", this.syncMultiWay?"true":"false");
			Node siblingParentInstance = Node.findById(this.siblingParentInstanceId);
			String siblingParentInstanceSchemaName = siblingParentInstance.getSchemaName();
			Document siblingParentTP = CloneHelper.queryInSchema(siblingParentInstanceSchemaName, ()->Document.findById(this.siblingParentId));
			
			String siblingParentTPName = "(" +CloneHelper.queryInSchema(siblingParentInstanceSchemaName, ()->siblingParentInstance.getBranch().getName()) +  " " + CloneHelper.queryInSchema(siblingParentInstanceSchemaName, ()->siblingParentInstance.getName()) + ") " + CloneHelper.queryInSchema(siblingParentInstanceSchemaName, ()->siblingParentTP.getName());
			settingsElement.addAttribute("siblingParentTPName", siblingParentTPName);
			
			Node parentInstance = Node.findById(this.parentInstanceId);
			String parentInstanceSchemaName = parentInstance.getSchemaName();
			Document parentTP = CloneHelper.queryInSchema(parentInstanceSchemaName, ()->Document.findById(this.parentId));
			String parentTPName = "(" +CloneHelper.queryInSchema(parentInstanceSchemaName, ()->parentInstance.getBranch().getName()) +  " " + CloneHelper.queryInSchema(parentInstanceSchemaName, ()->parentInstance.getName()) + ") " + CloneHelper.queryInSchema(parentInstanceSchemaName, ()->parentTP.getName());
			settingsElement.addAttribute("parentTPName", parentTPName);
			
		}
		int statusId = UserUtil.getCurrentSelectionStatusContext().getId();
		if (selection != null)
		{
			Element seldctionElement = metadataElement.addElement("Selection");
			seldctionElement.addAttribute("refid", Long.valueOf(selection.getId()).toString());
			if (statusId > 0) 
			{
				String selectionStatus = "";
				if (statusId == 2)
				{
					selectionStatus = "Active";
				}
				else
				{
					selectionStatus = "Working Copy";
				}
				seldctionElement.addAttribute("status", selectionStatus);
			}
		}
	}
	
	private void createImageLibraryTag(List<Long> clIds, Set<Document> documents, Element imageLibrariesElement) throws Exception{
		String schema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
		if(!this.syncFromOther)
			schema = this.otherSchema;
		
		boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(!MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(schema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
            switchedSession = true;
        }
		for(Long objectIdx10 : clIds){
			long objectId = objectIdx10 / 0x10;
			ContentObject contentObject = ContentObject.findByIdActiveDataFocusCentric(objectId);
			if(contentObject == null)
				return;
			if (contentObject.isArchived() || contentObject.isRemoved())
				return;

			ContentObjectData contentObjectData = contentObject.getLatestContentObjectDataActiveCentric();
			
		    if (contentObjectData != null)
		    {
				Element cliElement = imageLibrariesElement.addElement("ImageLibrary");
			    cliElement.addAttribute("id", Long.valueOf(contentObject.getId()).toString());
				cliElement.addAttribute("dataType", Integer.valueOf(contentObjectData.getDataType()).toString());
			    cliElement.addAttribute("instanceId", Long.valueOf(contentObjectData.getId()).toString());
			    if(contentObject.isDynamicVariantEnabled()){
			    	cliElement.addAttribute("type", "Dynamic");
			    }else{
			    	cliElement.addAttribute("type", "Regular");
			    }
			    Element cliNameElement = cliElement.addElement("Name");
			    cliNameElement.addText(contentObject.getName());
			    
			    if(contentObject.isDynamicVariantEnabled()){
			    	this.createContentTagForSelectableCL(contentObjectData, documents, cliElement);
			    }else{
			    	this.createContentTagForRegularCL(contentObjectData, documents, cliElement);
			    }
		    }
		}
		try {
            if(switchedSession) {
                Hibernate.initialize(schema);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
	}

    private void createDataSourceTag(Map<Long, Long> dataSourceObjectMap, Set<Document> documents, Element dataSourceListElement) throws Exception{
        String sourceSchema = syncFromOther ? otherSchema : currentSchema;
        String targetSchema = syncFromOther ? currentSchema : otherSchema;

        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(!MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(sourceSchema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
            switchedSession = true;
        }

        try {
            for(Long objectIdx10 : dataSourceObjectMap.keySet()) {
                long objectId = objectIdx10 / 0x10;
                long objectStatus = dataSourceObjectMap.get(objectIdx10);
                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                objectStatus = objectStatus & 0xFFFF;

                int projectStatus = (int) ((objectStatus / 0x100) & 0xF);
                int originStatus = (int) (objectStatus & 0xF);
                String objectSchema = objectIsFromOther ? sourceSchema : targetSchema;

                DataSource dataSource = CloneHelper.queryInSchema(objectSchema, ()->DataSource.findById(objectId));
                if(dataSource == null)
                    return;

                Element dataSourceElement = dataSourceListElement.addElement("DataSource");
                dataSourceElement.addAttribute("id", Long.toString(dataSource.getId()));
                dataSourceElement.addAttribute("sourcetypeid", Long.toString(dataSource.getSourceType().getId()));
                dataSourceElement.addAttribute("layouttypeid", Long.toString(dataSource.getLayoutType().getId()));
                dataSourceElement.addAttribute("recordtypeid", Long.toString(dataSource.getRecordType().getId()));

                dataSourceElement.addElement("Name").addText(dataSource.getName());
                dataSourceElement.addElement("LayoutType").addText(dataSource.getLayoutTypeName());
                dataSourceElement.addElement("SourceType").addText(ApplicationUtil.getMessage(dataSource.getSourceTypeName()));
                dataSourceElement.addElement("RecordType").addText(dataSource.getRecordTypeDisplayString());
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

    private void createLookupTableTag(Map<Long, Long> lookupTableObjectMap, Set<Document> documents, Element lookupTableListElement) throws Exception{
        String sourceSchema = syncFromOther ? otherSchema : currentSchema;
        String targetSchema = syncFromOther ? currentSchema : otherSchema;

        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(!MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(sourceSchema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
            switchedSession = true;
        }

        try {
            for(Long objectIdx10 : lookupTableObjectMap.keySet()) {
                long objectId = objectIdx10 / 0x10;
                long objectStatus = lookupTableObjectMap.get(objectIdx10);
                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                objectStatus = objectStatus & 0xFFFF;

                int projectStatus = (int) ((objectStatus / 0x100) & 0xF);
                int originStatus = (int) (objectStatus & 0xF);
                String objectSchema = objectIsFromOther ? sourceSchema : targetSchema;

                LookupTable lookupTable = CloneHelper.queryInSchema(objectSchema, ()->LookupTable.findById(objectId));
                if(lookupTable == null)
                    return;

                Element lookupTableElement = lookupTableListElement.addElement("LookupTable");
                lookupTableElement.addAttribute("id", Long.toString(lookupTable.getId()));
                lookupTableElement.addElement("Name").addText(lookupTable.getName());

                LookupTableInstance instance = lookupTable.getLatest();
                lookupTableElement.addElement("Delimiter").addText(instance.getDelimiter());
                String inputCharacterEncodingString = instance.getInputCharacterEncodingString();
                if(inputCharacterEncodingString == null) inputCharacterEncodingString = ApplicationUtil.getMessage("page.label.system.default");
                lookupTableElement.addElement("InputCharacterEncoding").addText(inputCharacterEncodingString);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

    private void createDataCollectionTag(Map<Long, Long> dataCollectionObjectMap, Set<Document> documents, Element dataCollectionListElement) throws Exception{
        String sourceSchema = syncFromOther ? otherSchema : currentSchema;
        String targetSchema = syncFromOther ? currentSchema : otherSchema;

        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(!MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(sourceSchema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
            switchedSession = true;
        }

        try {
            for(Long objectIdx10 : dataCollectionObjectMap.keySet()) {
                long objectId = objectIdx10 / 0x10;
                long objectStatus = dataCollectionObjectMap.get(objectIdx10);
                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                objectStatus = objectStatus & 0xFFFF;

                int projectStatus = (int) ((objectStatus / 0x100) & 0xF);
                int originStatus = (int) (objectStatus & 0xF);
                String objectSchema = objectIsFromOther ? sourceSchema : targetSchema;

                DataSourceAssociation dataSourceAssociation = CloneHelper.queryInSchema(objectSchema, ()->DataSourceAssociation.findById(objectId));
                if(dataSourceAssociation == null)
                    return;

                Element dataSourceAssociationElement = dataCollectionListElement.addElement("DataCollection");
                dataSourceAssociationElement.addAttribute("id", Long.toString(dataSourceAssociation.getId()));
                dataSourceAssociationElement.addElement("Name").addText(dataSourceAssociation.getName());
                dataSourceAssociationElement.addElement("PrimaryDataSource").addText(dataSourceAssociation.getPrimaryDataSource().getName());
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

    private void createVariableTag(Map<Long, Long> variableObjectMap, Set<Document> documents, Element variableListElement) throws Exception{
        String sourceSchema = syncFromOther ? otherSchema : currentSchema;
        String targetSchema = syncFromOther ? currentSchema : otherSchema;

        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(!MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(sourceSchema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
            switchedSession = true;
        }

        try {
            for(Long objectIdx10 : variableObjectMap.keySet()) {
                long objectId = objectIdx10 / 0x10;
                long objectStatus = variableObjectMap.get(objectIdx10);
                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                objectStatus = objectStatus & 0xFFFF;

                int projectStatus = (int) ((objectStatus / 0x100) & 0xF);
                int originStatus = (int) (objectStatus & 0xF);
                String objectSchema = objectIsFromOther ? sourceSchema : targetSchema;

                DataElementVariable dataElementVariable = CloneHelper.queryInSchema(objectSchema, ()->DataElementVariable.findById(objectId));
                if(dataElementVariable == null)
                    return;

                Element dataElementVariableElement = variableListElement.addElement("Variable");
                dataElementVariableElement.addAttribute("id", Long.toString(dataElementVariable.getId()));
                dataElementVariableElement.addAttribute("typeid", Long.toString(dataElementVariable.getTypeId()));
                dataElementVariableElement.addAttribute("datatypeid", Long.toString(dataElementVariable.getDataTypeId()));
                dataElementVariableElement.addAttribute("datasubtypeid", Long.toString(dataElementVariable.getDataSubtypeId()));
                dataElementVariableElement.addElement("Name").addText(dataElementVariable.getName());

                int devTypeId = dataElementVariable.getTypeId();
                String devType = devTypeId == 0 ? "" : new DataElementVariableType(devTypeId).getDisplayText();
                dataElementVariableElement.addElement("VariableType").addText(devType);

                int dataTypeId = dataElementVariable.getDataTypeId();
                String dataType = dataTypeId == 0 ? "" : ApplicationUtil.getMessage(DataType.findById(dataElementVariable.getDataTypeId()).getName());
                dataElementVariableElement.addElement("DataType").addText(dataType);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

    private void createParameterGroupTag(Map<Long, Long> parameterGroupObjectMap, Set<Document> documents, Element parameterGroupListElement) throws Exception{
        String sourceSchema = syncFromOther ? otherSchema : currentSchema;
        String targetSchema = syncFromOther ? currentSchema : otherSchema;

        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(!MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(sourceSchema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
            switchedSession = true;
        }

        try {
            for(Long objectIdx10 : parameterGroupObjectMap.keySet()) {
                long objectId = objectIdx10 / 0x10;
                long objectStatus = parameterGroupObjectMap.get(objectIdx10);
                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                objectStatus = objectStatus & 0xFFFF;

                int projectStatus = (int) ((objectStatus / 0x100) & 0xF);
                int originStatus = (int) (objectStatus & 0xF);
                String objectSchema = objectIsFromOther ? sourceSchema : targetSchema;

                ParameterGroup parameterGroup = CloneHelper.queryInSchema(objectSchema, ()->ParameterGroup.findById(objectId));
                if(parameterGroup == null)
                    return;


                Element parameterGroupElement = parameterGroupListElement.addElement("SelectorGroup");
                parameterGroupElement.addAttribute("id", Long.toString(parameterGroup.getId()));
                parameterGroupElement.addElement("Name").addText(parameterGroup.getName());

                Element itemsElement = parameterGroupElement.addElement("Selectors");
                for(ParameterGroupItem pgitem : parameterGroup.getParameterGroupItemsSorted()) {
                    Element itemElement = itemsElement.addElement("Selector");
                    itemElement.addAttribute("id", Long.toString(pgitem.getId()));
                    itemElement.addElement("Name").addText(pgitem.getParameter().getName());
                }
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

    private void createTargetRuleTag(Map<Long, Long> targetRuleObjectMap, Set<Document> documents, Element targetRuleListElement) throws Exception{
        String sourceSchema = syncFromOther ? otherSchema : currentSchema;
        String targetSchema = syncFromOther ? currentSchema : otherSchema;

        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(!MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(sourceSchema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
            switchedSession = true;
        }

        try {
            for(Long objectIdx10 : targetRuleObjectMap.keySet()) {
                long objectId = objectIdx10 / 0x10;
                long objectStatus = targetRuleObjectMap.get(objectIdx10);
                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                objectStatus = objectStatus & 0xFFFF;

                int projectStatus = (int) ((objectStatus / 0x100) & 0xF);
                int originStatus = (int) (objectStatus & 0xF);
                String objectSchema = objectIsFromOther ? sourceSchema : targetSchema;

                ConditionElement targetRule = CloneHelper.queryInSchema(objectSchema, ()->ConditionElement.findById(objectId));
                if(targetRule == null)
                    return;

                Element targetRuleElement = targetRuleListElement.addElement("TargetRule");
                targetRuleElement.addAttribute("id", Long.toString(targetRule.getId()));
                targetRuleElement.addElement("Name").addText(targetRule.getName());

                targetRuleElement.addElement("ConditionType").addText(targetRule.getDisplayType());

                String searchValue = targetRule.buildSearchString4Display(false, null, false);
                targetRuleElement.addElement("SearchValue").addCDATA(searchValue);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

    private void createTargetGroupTag(Map<Long, Long> targetGroupObjectMap, Set<Document> documents, Element targetGroupListElement) throws Exception{
        String sourceSchema = syncFromOther ? otherSchema : currentSchema;
        String targetSchema = syncFromOther ? currentSchema : otherSchema;

        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(!MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(sourceSchema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
            switchedSession = true;
        }

        try {
            for(Long objectIdx10 : targetGroupObjectMap.keySet()) {
                long objectId = objectIdx10 / 0x10;
                long objectStatus = targetGroupObjectMap.get(objectIdx10);
                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                objectStatus = objectStatus & 0xFFFF;

                int projectStatus = (int) ((objectStatus / 0x100) & 0xF);
                int originStatus = (int) (objectStatus & 0xF);
                String objectSchema = objectIsFromOther ? sourceSchema : targetSchema;

                TargetGroup targetGroup = CloneHelper.queryInSchema(objectSchema, ()->TargetGroup.findById(objectId));
                if(targetGroup == null)
                    return;

                Element targetGroupElement = targetGroupListElement.addElement("TargetGroup");
                targetGroupElement.addAttribute("id", Long.toString(targetGroup.getId()));
                targetGroupElement.addElement("Name").addText(targetGroup.getName());

                targetGroupElement.addElement("Details").addCDATA(targetGroup.getDetails());
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

	private void createImageLibraryTag(Map<Long, Long> contentLibraryObjectMap, Set<Document> documents, Element imageLibrariesElement) throws Exception{
	    createContentObjectTag(contentLibraryObjectMap, imageLibrariesElement, null, "ImageLibrary");
/*
		String sourceSchema = syncFromOther ? otherSchema : currentSchema;
        String targetSchema = syncFromOther ? currentSchema : otherSchema;
        
		boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(!MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(sourceSchema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
            switchedSession = true;
        }
		for(Long objectIdx10 : contentLibraryObjectMap.keySet()){
			Long objectId = objectIdx10 / 0x10;
	        long objectStatus = contentLibraryObjectMap.get(objectIdx10);
	        boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
	        objectStatus = objectStatus & 0xFFFF;
	        
	        int projectStatus = (int) ((objectStatus / 0x100) & 0xF);
			int originStatus = (int) (objectStatus & 0xF);
	        String objectSchema = objectIsFromOther ? sourceSchema : targetSchema;
	 
	        ContentObject contentObject = CloneHelper.queryInSchema(objectSchema, ()->ContentObject.findById(objectId));
	        if(contentObject == null)
				return;
			if (contentObject.isArchived() || contentObject.isRemoved())
				return;
			
			ContentObjectData contentObjectData = (ContentObjectData) CloneHelper.queryInSchema(objectSchema, ()->contentObject.getLatestContentObjectDataActiveCentric());
			if (contentObjectData.isWorking())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
			else if (contentObjectData.isActive())
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
			
		    if (contentObjectData != null)
		    {
				Element cliElement = imageLibrariesElement.addElement("ImageLibrary");
			    cliElement.addAttribute("id", Long.valueOf(contentObject.getId()).toString());
			    cliElement.addAttribute("instanceId", Long.valueOf(contentObjectData.getId()).toString());
			    String type = "";
				if (contentObject.isDynamicVariantEnabled())
				{
					type = "Dynamic";
				}
				else if (!contentObject.isVariableContentEnabled())
				{
					type = "Global";
				}
				else
				{
					type = "Regular";
				}

				cliElement.addAttribute("type", type);
				String contentType = "Graphic";
				cliElement.addAttribute("contenttype", contentType);
				cliElement.addAttribute("nextaction", contentObject.getActionRequired());
				if(contentObjectData.isActive()){
					cliElement.addAttribute("assignedto", "N/A");
				}else{
					cliElement.addAttribute("assignedto", contentObject.getAssignedToUserName());
				}

				cliElement.addAttribute("defaultlanguage", MessagepointLocale.getDefaultSystemLanguageCode());
				
			    // Version Tag
			    //createILVersionTag(cli, documents, cliElement);
				Element versionElement = cliElement.addElement("Version");
				versionElement.addAttribute("id", getContentObjectDataID(contentObjectData));
				versionElement.addAttribute("guid", contentObjectData.getGuid());
				versionElement.addAttribute("status", contentObjectData.getStatus().getLocaledString());

				String versionOrigin = contentObjectData.getVersionInfo().getCreationReason().getLocaledString();
				versionElement.addAttribute("origin", versionOrigin);
				ContentSelectionStatusType versionStatusType = new ContentSelectionStatusType((int)projectStatus);
				versionElement.addAttribute("versionStatus", versionStatusType != null?versionStatusType.getDisplayText():"");
				ContentSelectionStatusType sourceStatusType = new ContentSelectionStatusType((int)originStatus);
				versionElement.addAttribute("sourceStatus", sourceStatusType != null? sourceStatusType.getDisplayText():"");
				versionElement.addAttribute("deliverytypeid", Long.toString(contentObject.getDeliveryType()));
				versionElement.addAttribute("usagetypeid", Long.toString(contentObject.getUsageTypeId()));
				
				Element nameElement = versionElement.addElement("Name");
				nameElement.addText(contentObject.getName());
				
			}
		}
		try {
            if(switchedSession) {
                Hibernate.initialize(sourceSchema);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
 */
	}

	private String getContentObjectDataID(ContentObjectData contentObjectData) {
	    ContentObject contentObject = contentObjectData.getContentObject();
	    return "" + contentObject.getId();
/*
	    int dataType = contentObjectData.getDataType();
	    if(dataType == 1) return "WC";
	    if(dataType == 2) return "AC";
	    if(dataType == 4) return "AR";
	    return null;

 */
    }

	private void createContentTagForSelectableCL(ContentObjectData contentObjectData, Set<Document> documents, Element versionElement) throws Exception {
		
		boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        String schema = contentObjectData.getObjectSchemaName();
        if(schema != null && ! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(schema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
            switchedSession = true;
        }
        
		Element selectableContentElement = versionElement.addElement("SelectableContent");
		createSelectorTag(contentObjectData.getParameterGroup(), selectableContentElement);
		Element defaultSelectionElement = selectableContentElement.addElement("Selection");
		Element defaultSelectionNameElement = defaultSelectionElement.addElement("Name");
		defaultSelectionNameElement.addText("Default");
		Element defaultSelectionContentsElement = defaultSelectionElement.addElement("Contents");

		createContentTagForRegularCL(contentObjectData, documents, defaultSelectionContentsElement);

		List<ContentObjectAssociation> topLevelAssociations = ContentObjectAssociation.findTopLevelAssociationsForContentObject(contentObjectData.getContentObject());
		Set<ParameterGroupTreeNode> explored = new HashSet<>();
		for (ContentObjectAssociation clca : topLevelAssociations){
		    ParameterGroupTreeNode pgtn;
		    if (contentObjectData.getContentObject().isDynamicVariantEnabled())
				pgtn = clca.getContentObjectPGTreeNode();
		    else
				pgtn = clca.getTouchpointPGTreeNode();
		    if ( explored.contains(pgtn) ){
		        continue;
		    }
		    explored.add(pgtn);
			createSelectionTagForCL(contentObjectData, topLevelAssociations, clca, documents, defaultSelectionElement);
		}
		
		try {
            if(switchedSession) {
                Hibernate.initialize(contentObjectData);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
	}

    private void createSelectionTagForCL(ContentObjectData cl, List<ContentObjectAssociation> clContentAssociations,
                                         ContentObjectAssociation messageContentAssociation, Set<Document> documents, Element element){
        String schema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
        if(this.syncFromOther)
            schema = this.otherSchema;
        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(schema != null && ! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(schema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
            switchedSession = true;
        }

        Element selectionElement = element.addElement("Selection");
        ParameterGroupTreeNode parameterGroupTreeNode = messageContentAssociation.getPGTreeNode();
        long id = parameterGroupTreeNode.getId();
        TouchpointSelection touchpointSelection = TouchpointSelection.findByPgTreeNodeId(id);

        if (touchpointSelection != null) {
            id = touchpointSelection.getId();
        }

        selectionElement.addAttribute("id", Long.valueOf(id).toString());
        if (parameterGroupTreeNode.getParameterGroupInstanceCollection() != null){
            selectionElement.addAttribute("datavaluerefid", Long.valueOf(parameterGroupTreeNode.getParameterGroupInstanceCollection().getId()).toString());
        }

        Element selectionNameElement = selectionElement.addElement("Name");
        selectionNameElement.addText(parameterGroupTreeNode.getName());
        Element selectionContentsElement = selectionElement.addElement("Contents");
        createContentTagForSelectionForCL(cl, clContentAssociations, parameterGroupTreeNode, documents, selectionContentsElement);

        List<ParameterGroupTreeNode> ChildNodes = parameterGroupTreeNode.getChildren();
        if (ChildNodes != null && !ChildNodes.isEmpty()) {
            for (ParameterGroupTreeNode childNode : ChildNodes) {
                List<ContentObjectAssociation> childContentAssociationList = ContentObjectAssociation.findAllByContentObjectAndParameters(cl.getContentObject(), cl.getDataType(), childNode);
                ContentObjectAssociation childContentAssociation = childContentAssociationList.stream().findFirst().orElse(null);
                createSelectionTagForCL(cl, childContentAssociationList, childContentAssociation, documents, selectionElement);
            }
        }
        try {
            if(switchedSession) {
                Hibernate.initialize(schema);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

    private void createContentTagForSelectionForCL(ContentObjectData cli, List<ContentObjectAssociation> clContentAssociations,
                                                   ParameterGroupTreeNode selection, Set<Document> documents, Element contentsElement) {
        List<MessagepointLocale> locales = null;
        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        String schema = this.syncFromOther?this.otherDocument.getObjectSchemaName():this.projectDocument.getObjectSchemaName();
        if(schema != null && ! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(schema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
            switchedSession = true;
        }
        if (!documents.isEmpty()){
            for (Document doc : documents){
				locales = doc.getTouchpointLanguagesAsLocales();
				if (locales != null)
					break;
            }
        }
        if (locales == null)
			locales = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();

        boolean selectableCL = false;
        if (cli.getContentObject().isGlobalImage() && cli.isDynamicVariantEnabled()) {
            selectableCL = true;
        }

        for (MessagepointLocale locale : locales){
            String languageCode = locale.getLanguageCode();
			String localeCode = locale.getCode();
            boolean missingFlag = true;
            for( ContentObjectAssociation ca : clContentAssociations ){
                if ( ca.getPGTreeNode() != selection ){
                    continue;
                }

                if ( ca.getMessagepointLocale() == null || ca.getMessagepointLocale().getId() != locale.getId() ){
                    continue;
                }

                String languageContent = "";
                boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
                Content content = ca.getContent();
                String langImageName = "";
                String langImageUploaded = "";

                if ( content != null) {
                    languageContent = content.getImageName();
                    if ( content.getAppliedImageFilename() != null )
                        langImageName = content.getAppliedImageFilename();
                    if ( content.getImageUploadedDate() != null )
                        langImageUploaded = DateUtil.formatDateForXMLOutput(content.getImageUploadedDate());
                    else
                        langImageUploaded = "unknown";
                } else{
                    sameAsParent = true;
                }

                missingFlag = false;
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);

                if (sameAsParent && selectableCL) {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                } else {
                    if (!langImageName.isEmpty())
                        contentElement.addAttribute("imagename", langImageName);
                    if (!langImageUploaded.isEmpty())
                        contentElement.addAttribute("uploaded", langImageUploaded);

                    contentElement.addCDATA(languageContent);
                }
            }
            if (missingFlag){
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
                if (selectableCL) {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                }else{
                    contentElement.addCDATA("");
                }
            }
        }
        try {
            if(switchedSession) {
                Hibernate.initialize(document);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

    private void createContentTagForRegularCL(ContentObjectData contentObjectData, Set<Document> documents, Element contentsElement) throws Exception {
        List<MessagepointLocale> locales = null;
        String defaultTouchpointLocaleCode = null;
        String schema = contentObjectData.getObjectSchemaName();
        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(schema != null && ! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(schema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
            switchedSession = true;
        }
        ContentObject contentObject = contentObjectData.getContentObject();
        Document doc = this.syncFromOther?this.otherDocument:this.projectDocument;
		locales = doc.getTouchpointLanguagesAsLocales();
		defaultTouchpointLocaleCode = doc.getDefaultTouchpointLanguageLocaleCode();
        if (locales == null)
			locales = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();
        if (defaultTouchpointLocaleCode == null)
			defaultTouchpointLocaleCode = MessagepointLocale.getDefaultSystemLanguageLocale().getCode();

        for (MessagepointLocale locale : locales){
            String languageCode = locale.getLanguageCode();
			String localeCode = locale.getCode();
            Set<ContentObjectAssociation> clCAs = contentObject
                    .getContentObjectAssociations()
                    .stream()
                    .filter(ca->ca.getDataType() == contentObjectData.getDataType())
                    .collect(Collectors.toSet());
            boolean missingFlag = true;
            for( ContentObjectAssociation ca : clCAs ){
                if ( ca.getPGTreeNode() != null || ca.getMessagepointLocale().getId() != locale.getId() ) {
                    continue;
                }
                Content languageContent = ca.getContent();

                Element contentElement = null;

                missingFlag = false;
                contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
				if (localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
					contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
                if(ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode)){
                    contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
                }else if (languageContent != null){
                    contentElement.addCDATA(languageContent.getImageName());
                    if ( languageContent.getAppliedImageFilename() != null && !languageContent.getAppliedImageFilename().isEmpty())
                        contentElement.addAttribute("imagename", languageContent.getAppliedImageFilename());
                    if ( languageContent.getImageUploadedDate() != null )
                        contentElement.addAttribute("uploaded", DateUtil.formatDateForXMLOutput(languageContent.getImageUploadedDate()) );
                    else
                        contentElement.addAttribute("uploaded", "unknown");
                }
            }
            if (missingFlag){
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
                if (!localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode)){
                    contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
                }else{
                    contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
                }
            }
        }
        try {
            if(switchedSession) {
                Hibernate.initialize(schema);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

    private void createMessageTag(
			ProjectSyncObjectListVO vo,
	        List<ContentObjectData> releventMsgVersions,
	        Element messagesElement,
	        TouchpointSelection tpSelection,
	        Set<Document> documents)
	throws Exception 
	{
		boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(vo.getOriginSchema() != null && ! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(vo.getOriginSchema())) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(vo.getOriginSchema());
            switchedSession = true;
        }
		
		ContentObjectData contentObjectData = vo.getLastesOriginCopy();
        ContentObject contentObject = contentObjectData.getContentObject();
		String tagName = contentObject.getIsTouchpointLocal()?(contentObject.getContentType().getId()==ContentType.TEXT?"LocalSmartText":"LocalImageLibrary"):"Message";
		Element messageElement = messagesElement.addElement(tagName);
        messageElement.addAttribute("id", getContentObjectDataID(contentObjectData));
		//messageElement.addAttribute("localtype", tagName);
		TenantMetaData tmd = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, contentObjectData.getModel().getId());
	
		if (tmd != null) 
		{
			messageElement.addAttribute("externalid", tmd.getExternalId().toString());
		}
	
		ContentSelectionType messageType = new ContentSelectionType(contentObjectData.getContentSelectionTypeId());
		messageElement.addAttribute("type", messageType.getDisplayText());
		
		String contentType = contentObject.getContentType().getLocaledString();
		messageElement.addAttribute("contenttype", contentType);
		createVersionTag(vo, false, messageElement, vo.getOriginTP().getMasterTouchpointSelection(), documents);
		try {
            if(switchedSession) {
                Hibernate.initialize(contentObjectData);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
	}

	private void createVersionTag(
			ProjectSyncObjectListVO versionVO,
	        boolean includeTargeting,
	        Element messageElement,
	        TouchpointSelection tpSelection,
	        Set<Document> documents)
	throws Exception
	{
		Element versionElement = messageElement.addElement("Version");
		
		boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(versionVO.getOriginSchema() != null && ! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(versionVO.getOriginSchema())) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(versionVO.getOriginSchema());
            switchedSession = true;
        }
        ContentObject contentObject = versionVO.getLastesOriginCopy().getContentObject();
	
		versionElement.addAttribute("status",contentObject.getContentObjectData().getStatus()!=null? contentObject.getContentObjectData().getStatus().getLocaledString():"n/a");
		String versionOrigin = contentObject.getContentObjectData().getVersionInfo()!=null?contentObject.getContentObjectData().getVersionInfo().getCreationReason().getLocaledString(): "n/a";
		versionElement.addAttribute("origin", versionOrigin);
		versionElement.addAttribute("versionStatus", versionVO.getProjectStatusText()!=null?versionVO.getProjectStatusText():"");
		versionElement.addAttribute("sourceStatus", versionVO.getOriginStatusText()!=null?versionVO.getOriginStatusText():"");
		// Name
		Element nameElement = versionElement.addElement("Name");
		nameElement.addText(contentObject.getName());
		// Description
		Element descriptionElement = versionElement.addElement("Description");
		if (contentObject.getDescription() != null)
		{
			descriptionElement.addText(contentObject.getDescription());
		}
		
		if (tpSelection != null && (!contentObject.isVariantType() || contentObject.getOwningTouchpointSelection().getId() != tpSelection.getId()))
		{
			// ContentSelector
			Element contentSelectorElement = versionElement.addElement("ContentSelector");
			if (contentObject.getOwningTouchpointSelection() != null)
				contentSelectorElement.addText(contentObject.getOwningTouchpointSelection().getName());
			else
				contentSelectorElement.addText("Master");
		}
		// Zones
		if ( !versionVO.getObject().getIsTouchpointLocal() ) {
			Element zonesElement = versionElement.addElement("Zones");
			if (contentObject.getZone() != null) {
				Element zoneElement = zonesElement.addElement("Zone");
				zoneElement.addText(contentObject.getZone().getFriendlyName());
			}
		}
		contentObject.getOriginObject();
		
		if (contentObject.isFocusOnWorkingData() && contentObject.hasWorkingData())
		{
			Element checkoutElement = versionElement.addElement("Checkout");
			checkoutElement.addText(contentObject.getCreatedByName());
			checkoutElement.addAttribute("date", DateUtil.formatDateForXMLOutput(contentObject.getContentObjectData().getCreated()));
		}
	
		String delivertType = contentObject.getDeliveryTypeText();
		if (contentObject.isDynamicVariantEnabled() || contentObject.isStructuredContentEnabled())
		{
			if (contentObject.getZone() != null)
			{
				Element deliveryElement = versionElement.addElement("Delivery");
				if (true )
				{
					deliveryElement.addAttribute("type", delivertType);	
				}
				deliveryElement.addAttribute("zonerefid", Long.valueOf(contentObject.getZone().getId()).toString());
			}
			if(contentObject.isDynamicVariantEnabled()){
				createContentTagForDynamicMessage(versionVO, documents, versionElement);
			}else if(contentObject.isStructuredContentEnabled()){
				createContentTagForStructuredMessage(versionVO, documents, versionElement);
			}
		}
		else
		{
			if (contentObject.getZone() != null)
			{
				Element deliveryElement = versionElement.addElement("Delivery");
				if (true )
				{
					deliveryElement.addAttribute("type", delivertType);
				}
				deliveryElement.addAttribute("zonerefid", Long.valueOf(contentObject.getZone().getId()).toString());
				if (contentObject.isMultipartType())
				{
					Element contentsElement = deliveryElement.addElement("Contents");
					createContentTagForRegularMessage(versionVO, contentObject.getZone(), documents, contentsElement);
				}
			}
			if (!contentObject.isMultipartType())
			{
				Element contentsElement = versionElement.addElement("Contents");
				createContentTagForRegularMessage(versionVO, null, documents, contentsElement);
			}
		}
	
		Element timingElement = versionElement.addElement("Timing");
		if (contentObject.getStartDate() != null)
		{
			timingElement.addAttribute("startdate", contentObject.getStartDate().toString());
		}
		if (contentObject.getEndDate() != null)
		{
			timingElement.addAttribute("enddate", contentObject.getEndDate().toString());
		}
		if ( contentObject.getStartDate() != null ||
				contentObject.getEndDate() != null)
		{
			timingElement.addAttribute("repeatsannually", Boolean.valueOf(contentObject.isRepeatDatesAnnually()).toString());
		}
	
		if (includeTargeting)
		{
			Element targetCriteriaElement = versionElement.addElement("TargetCriteria");
			if ( contentObject.getExcludedTargetGroups() != null &&
                    !contentObject.getExcludedTargetGroups().isEmpty())
			{
				Element exclusionsElement = targetCriteriaElement.addElement("Exclusions");
				exclusionsElement.addAttribute("relationship", contentObject.getExcludedTargetGroupRelationship()==Targetable.RELATION_ALLOF?"AND":"OR");
				for (TargetGroup targetGroup : contentObject.getExcludedTargetGroups())
				{
					Element targetGroupElement = exclusionsElement.addElement("TargetGroup");
					targetGroupElement.addCDATA(targetGroup.getDetails(contentObject.getContentObjectData()));
				}
			}
			if ( contentObject.getIncludedTargetGroups() != null &&
                    !contentObject.getIncludedTargetGroups().isEmpty())
			{
				Element inclusionsGroupAElement = targetCriteriaElement.addElement("InclusionsGroupA");
				inclusionsGroupAElement.addAttribute("relationship", contentObject.getIncludedTargetGroupRelationship()==Targetable.RELATION_ALLOF?"AND":"OR");
				for (TargetGroup targetGroup : contentObject.getIncludedTargetGroups())
				{
					Element targetGroupElement = inclusionsGroupAElement.addElement("TargetGroup");
					targetGroupElement.addCDATA(targetGroup.getDetails(contentObject.getContentObjectData()));
				}
			}
			if ( contentObject.getExtendedTargetGroups() != null &&
                    !contentObject.getExtendedTargetGroups().isEmpty())
			{
				Element inclusionsGroupBElement = targetCriteriaElement.addElement("InclusionsGroupB");
				inclusionsGroupBElement.addAttribute("relationship", contentObject.getExtendedTargetGroupRelationship()==Targetable.RELATION_ALLOF?"AND":"OR");
				for (TargetGroup targetGroup : contentObject.getExtendedTargetGroups())
				{
					Element targetGroupElement = inclusionsGroupBElement.addElement("TargetGroup");
					targetGroupElement.addCDATA(targetGroup.getDetails(contentObject.getContentObjectData()));
				}
			}
		}
		try {
            if(switchedSession) {
                Hibernate.initialize(contentObject);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
	
	}

	private void createContentTagForRegularMessage(
			ProjectSyncObjectListVO vo,
	        Zone zone,
	        Set<Document> documents,
	        Element contentsElement)
	throws Exception
	{		
		List<MessagepointLocale> locales = null;
		String defaultTouchpointLocaleCode = null;
		
		ContentObject contentObject = vo.getLastesOriginCopy().getContentObject();
		Document doc = vo.getOriginTP();
		boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        String schema = vo.getOriginSchema();
        if(schema != null && ! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(schema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
            switchedSession = true;
        }
		locales = doc.getTouchpointLanguagesAsLocales();
		defaultTouchpointLocaleCode = doc.getDefaultTouchpointLanguageLocaleCode();
		
		if (locales == null)
			locales = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();
		if (defaultTouchpointLocaleCode == null)
			defaultTouchpointLocaleCode = MessagepointLocale.getDefaultSystemLanguageLocale().getCode();
        
		ContentObjectData comparator = null;
		ContentObjectData compareTo = null;
		
	    
		if(this.syncFromOther){	
		    if(vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_DELETED && vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_ACTIVATED) {
		        if((vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_UNCHANGED && vo.getOriginStatus().getId() == ContentSelectionStatusType.ID_UNCHANGED) || vo.getOriginStatus().getId() == ContentSelectionStatusType.ID_CHANGED || vo.getOriginStatus().getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC) {
		        	comparator = vo.getProjectMessageInstanceWorkingCopy();
		        	compareTo = vo.getOriginMessageInstanceWorkingCopy();
		        }
		    }
		    if(vo.getProjectStatusActiveCopy().getId() != ContentSelectionStatusType.ID_DELETED && vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_ARCHIVED) {
		        if((vo.getProjectStatusActiveCopy().getId() != ContentSelectionStatusType.ID_UNCHANGED && vo.getOriginStatusActiveCopy().getId() == ContentSelectionStatusType.ID_UNCHANGED) || vo.getOriginStatusActiveCopy().getId() == ContentSelectionStatusType.ID_CHANGED || vo.getOriginStatusActiveCopy().getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC){
		        	comparator = vo.getProjectMessageInstanceActiveCopy();
		        	compareTo = vo.getOriginMessageInstanceActiveCopy();
		        }
		    }
		} else {	 
			if(vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_DELETED && vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_ACTIVATED) {
		        if((vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_UNCHANGED && vo.getOriginStatus().getId() == ContentSelectionStatusType.ID_UNCHANGED) || vo.getOriginStatus().getId() == ContentSelectionStatusType.ID_CHANGED || vo.getOriginStatus().getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC) {
		        	comparator = vo.getProjectMessageInstanceWorkingCopy();
		        	compareTo = vo.getOriginMessageInstanceWorkingCopy();
		        }
		    }
			if(vo.getProjectStatusActiveCopy().getId() != ContentSelectionStatusType.ID_DELETED && vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_ARCHIVED) {
		        if((vo.getProjectStatusActiveCopy().getId() != ContentSelectionStatusType.ID_UNCHANGED && vo.getOriginStatusActiveCopy().getId() == ContentSelectionStatusType.ID_UNCHANGED) || vo.getOriginStatusActiveCopy().getId() == ContentSelectionStatusType.ID_CHANGED || vo.getOriginStatusActiveCopy().getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC){
		        	comparator = vo.getProjectMessageInstanceActiveCopy();
		        	compareTo = vo.getOriginMessageInstanceActiveCopy();
		        }
		    }
		}
		
		if (contentObject.isMultipartType())
		{
			Integer partNum = 1;
			for (ZonePart zonePart : zone.getPartsInSequenceOrder())
			{
				Element partElement = contentsElement.addElement("Part");
				partElement.addAttribute("num", (partNum++).toString());
				Set<ContentObjectAssociation> contentAssociations = contentObject
                        .getContentObjectAssociations()
                        .stream()
                        .filter(ca->ca.getDataType() == vo.getLastesOriginCopy().getDataType())
                        .collect(Collectors.toSet());
				Set<ContentObjectAssociation> partAssociations = getAssociationsForZonePart(zonePart, contentAssociations);
				
				if ( partAssociations == null || partAssociations.isEmpty() )
				{
					continue;
				}			

				boolean zonePartEmpty = false;
    		    for (MessagepointLocale locale : locales)
				{
					String languageCode = locale.getLanguageCode();
					String localeCode = locale.getCode();

					boolean isActive = false;
					boolean missingFlag = true;					
					for( ContentObjectAssociation partCa : partAssociations )
					{
					    if ( partCa.getPGTreeNode() != null )
					        continue;
					    
						if ( partCa.getMessagepointLocale().getId() != locale.getId() )
						{
							continue;
						}

						if (partCa.getTypeId() == ContentAssociationType.ID_EMPTY)
						{
							zonePartEmpty = true;
							partElement.addAttribute("empty", Boolean.TRUE.toString());
							break;
						}

						boolean conflict = false;
						String contentFinal = "";
						JSONObject comparedJason = null;
						if(comparator != null && compareTo != null){
							comparedJason = getSyncContentCompareJSON(comparator, compareTo, -1, -1, locale.getId() );
						}else{

						}
						try {
							if( comparedJason != null && !comparedJason.get("compared_content").toString().isEmpty()){
								conflict = true;
								contentFinal = comparedJason.getString("compared_content").toString();
							}else if( comparedJason != null
									&& comparedJason.get("compared_content").toString().isEmpty()
									&& comparedJason.has("is_graphic_content")
									&& (!comparedJason.get("graphic_path_1").toString().equals(comparedJason.get("graphic_path_2").toString()))){
								conflict = true;
								contentFinal = comparedJason.getString("tip_text_1").toString();
							}
						} catch (JSONException e) {
							e.printStackTrace();
						}

						String languageContent = "";
						Content langPartContent = partCa.getContent();
						String languageImageName = "";
						String langImageUpload = "";

						if ( langPartContent == null ){
							languageContent = null;
						}
						else if ( zonePart.getContentType().getId() == ContentType.TEXT){
							languageContent = langPartContent.getContent();
						}else{
							languageContent = langPartContent.getImageName();
							languageImageName = langPartContent.getAppliedImageFilename();
							langImageUpload = DateUtil.formatDateForXMLOutput(langPartContent.getImageUploadedDate());
						}

						missingFlag = false;
						Element contentElement = partElement.addElement("Content");
						contentElement.addAttribute("language", languageCode);
						contentElement.addAttribute("locale", localeCode);
						if (localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
							contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
						if(partCa.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode)){
							contentElement.addAttribute("sameasdefault", Boolean.TRUE.toString());
							contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
						}else if (partCa.getReferencingImageLibrary() != null) {
							if (partCa.getReferencingImageLibrary().isGlobalImage())
								contentElement.addAttribute("imagelibraryid", Long.valueOf(partCa.getReferencingImageLibrary().getId()).toString());
							else
								contentElement.addAttribute("localimagelibraryid", Long.valueOf(partCa.getReferencingImageLibrary().getId()).toString());
						}else if (languageContent != null){
							if(!conflict)
								contentElement.addCDATA(languageContent);
							else
								contentElement.addCDATA(contentFinal);

							if ( languageImageName != null && !languageImageName.isEmpty())
								contentElement.addAttribute("imagename", languageImageName);

							if (!langImageUpload.isEmpty())
								contentElement.addAttribute("uploaded", langImageUpload);
						}
					}
					if (missingFlag && !zonePartEmpty)
					{
						Element contentElement = partElement.addElement("Content");
				        contentElement.addAttribute("language", languageCode);
						contentElement.addAttribute("locale", localeCode);
				        if (!localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
				        {
				        	contentElement.addAttribute("sameasdefault", Boolean.TRUE.toString());
	                        contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
				        }
				        else
				        {
				        	contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
				        }
					}					
				}
			}
		}

		else
		{	// Regular Text or Graphic Message (not multipart)
		    for (MessagepointLocale locale : locales)
			{
				String languageCode = locale.getLanguageCode();
				String localeCode = locale.getCode();
				Set<ContentObjectAssociation> msgCAs = contentObject
                        .getContentObjectAssociations()
                        .stream()
                        .filter(ca->ca.getDataType() == vo.getLastesOriginCopy().getDataType())
                        .collect(Collectors.toSet());
				boolean missingFlag = true;
				for( ContentObjectAssociation ca : msgCAs )
				{
				    if ( ca.getPGTreeNode() != null || ca.getMessagepointLocale().getId() != locale.getId() )
				    {
				        continue;
				    }
				    boolean conflict = false;
	                String contentFinal = "";
	                JSONObject comparedJason = null;
	                if(comparator != null && compareTo != null){
	                	comparedJason = getSyncContentCompareJSON(comparator, compareTo, -1, -1, locale.getId() );
	                }else{
	                	
	                }
	                try {
						if( comparedJason != null && !comparedJason.get("compared_content").toString().isEmpty()){
							conflict = true;
							contentFinal = comparedJason.getString("compared_content").toString();
						}else if( comparedJason != null 
								&& comparedJason.get("compared_content").toString().isEmpty()
								&& comparedJason.has("is_graphic_content") 
								&& (!comparedJason.get("graphic_path_1").toString().equals(comparedJason.get("graphic_path_2").toString()))){
							conflict = true; 
							contentFinal = comparedJason.getString("tip_text_1").toString();
						}
					} catch (JSONException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				    Content languageContent = ca.getContent();
				    
				    Element contentElement = null;

				    missingFlag = false;
			        contentElement = contentsElement.addElement("Content");
			        contentElement.addAttribute("language", languageCode);
					contentElement.addAttribute("locale", localeCode);
					if (localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
						contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
					if(ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
			        {
			        	contentElement.addAttribute("sameasdefault", Boolean.TRUE.toString());
                        contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
					}else if (ca.getReferencingImageLibrary() != null) {
						if (ca.getReferencingImageLibrary().isGlobalImage())
							contentElement.addAttribute("imagelibraryid", Long.valueOf(ca.getReferencingImageLibrary().getId()).toString());
						else
							contentElement.addAttribute("localimagelibraryid", Long.valueOf(ca.getReferencingImageLibrary().getId()).toString());
			        }else if (languageContent != null)
			        {
				        if (contentObject.getContentType().isText())
				        {
				            
				            if(!conflict)
				            	contentElement.addCDATA(languageContent.getContent(ca.getDocument()));
			                else
			                	contentElement.addCDATA(contentFinal);
				            
				        }
				        else
				        {
				        	if(!conflict)
				        		contentElement.addCDATA(languageContent.getImageName());
				        	else
				        		contentElement.addCDATA(contentFinal);
                            if ( languageContent.getAppliedImageFilename() != null && !languageContent.getAppliedImageFilename().isEmpty())
                                contentElement.addAttribute("imagename", languageContent.getAppliedImageFilename());
                            if ( languageContent.getImageUploadedDate() != null )
                                contentElement.addAttribute("uploaded", DateUtil.formatDateForXMLOutput(languageContent.getImageUploadedDate()) );
                            else
                                contentElement.addAttribute("uploaded", "unknown");
				        }			        	
			        }
				}
				if (missingFlag)
				{
					Element contentElement = contentsElement.addElement("Content");
			        contentElement.addAttribute("language", languageCode);
					contentElement.addAttribute("locale", localeCode);
			        if (!localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
			        {
			        	contentElement.addAttribute("sameasdefault", Boolean.TRUE.toString());
                        contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
			        }
			        else
			        {
			        	contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
			        }
				}				    
			}
		}
		try {
            if(switchedSession) {
                Hibernate.initialize(contentObject);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
	}

	private void createContentTagForDynamicMessage( // Dynamic
			ProjectSyncObjectListVO vo,
	        Set<Document> documents,
	        Element versionElement)
	throws Exception 
	{
		boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        String schema = vo.getOriginSchema();
        if(schema != null && ! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(schema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
            switchedSession = true;
        }
        ContentObject contentObject = vo.getLastesOriginCopy().getContentObject();
		Element selectableContentElement = versionElement.addElement("SelectableContent");
		createSelectorTag(contentObject.getParameterGroup(), selectableContentElement);
		Element defaultSelectionElement = selectableContentElement.addElement("Selection");
		Element defaultSelectionNameElement = defaultSelectionElement.addElement("Name");
		defaultSelectionNameElement.addText("Master");
		Element defaultSelectionContentsElement = defaultSelectionElement.addElement("Contents");
    	createContentTagForRegularMessage(vo, contentObject.getZone(), documents, defaultSelectionContentsElement);

		List<ContentObjectAssociation> topLevelAssociations = ContentObjectAssociation.findTopLevelAssociationsForContentObject(vo.getObject());
		Set<ParameterGroupTreeNode> explored = new HashSet<>();
		for (ContentObjectAssociation messageContentAssociation : topLevelAssociations) 
		{
		    ParameterGroupTreeNode pgtn = messageContentAssociation.getPGTreeNode();
		    if ( explored.contains(pgtn) )
		        continue;
		    explored.add(pgtn);
		    createSelectionTag(vo, topLevelAssociations, messageContentAssociation, documents, defaultSelectionElement);
		}
		try {
            if(switchedSession) {
                Hibernate.initialize(contentObject);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
	}
	
	private void createContentTagForStructuredMessage( // Structured
			ProjectSyncObjectListVO vo,
	        Set<Document> documents,
	        Element versionElement)
	throws Exception 
	{
		Element selectableContentElement = versionElement.addElement("SelectableContent");
		
		List<ContentObjectAssociation> topLevelAssociations = CloneHelper.queryInSchema(vo.getObject().getObjectSchemaName(),  ()->ContentObjectAssociation.findTopLevelAssociationsForContentObject(vo.getObject()));
		Set<Long> explored = new HashSet<>();
		for (ContentObjectAssociation messageContentAssociation : topLevelAssociations) 
		{
			ParameterGroupTreeNode pgtn = CloneHelper.queryInSchema(messageContentAssociation.getObjectSchemaName(),  ()->messageContentAssociation.getPGTreeNode());
			long pgtnID = 0;
			if(pgtn != null) {
			    pgtnID = pgtn.getId();
            }

		    if ( explored.contains(pgtnID) )
		        continue;
		    explored.add(pgtnID);
		    createSelectionTag(vo, topLevelAssociations, messageContentAssociation, documents, selectableContentElement);
		}
	}
	
	
	private void createContentTagForSelection(
				ProjectSyncObjectListVO vo,
		        List<ContentObjectAssociation> messageContentAssociations,
		        ParameterGroupTreeNode pgtn,
		        Element contentsElement)
		{
			List<MessagepointLocale> locales = null;
			Document doc = vo.getOriginTP();
			
			boolean switchedSession = false;
	        SessionHolder mainSessionHolder = null;
	        String schema = vo.getOriginSchema();
	        if(schema != null && ! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(schema)) {
	            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
	            switchedSession = true;
	        }

			locales = doc.getTouchpointLanguagesAsLocales();
			if (locales == null)
				locales = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();

			ContentObjectData comparator = null;
			ContentObjectData compareTo = null;
			
			if(this.syncFromOther){	
			    if(vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_DELETED && vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_ACTIVATED) {
			        if((vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_UNCHANGED && vo.getOriginStatus().getId() == ContentSelectionStatusType.ID_UNCHANGED) || vo.getOriginStatus().getId() == ContentSelectionStatusType.ID_CHANGED || vo.getOriginStatus().getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC) {
			        	comparator = vo.getProjectMessageInstanceWorkingCopy();
			        	compareTo = vo.getOriginMessageInstanceWorkingCopy();
			        }
			    }
			    if(vo.getProjectStatusActiveCopy().getId() != ContentSelectionStatusType.ID_DELETED && vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_ARCHIVED) {
			        if((vo.getProjectStatusActiveCopy().getId() != ContentSelectionStatusType.ID_UNCHANGED && vo.getOriginStatusActiveCopy().getId() == ContentSelectionStatusType.ID_UNCHANGED) || vo.getOriginStatusActiveCopy().getId() == ContentSelectionStatusType.ID_CHANGED || vo.getOriginStatusActiveCopy().getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC){
			        	comparator = vo.getProjectMessageInstanceActiveCopy();
			        	compareTo = vo.getOriginMessageInstanceActiveCopy();
			        }
			    }
			} else {	 
				if(vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_DELETED && vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_ACTIVATED) {
			        if((vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_UNCHANGED && vo.getOriginStatus().getId() == ContentSelectionStatusType.ID_UNCHANGED) || vo.getOriginStatus().getId() == ContentSelectionStatusType.ID_CHANGED || vo.getOriginStatus().getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC) {
			        	comparator = vo.getProjectMessageInstanceWorkingCopy();
			        	compareTo = vo.getOriginMessageInstanceWorkingCopy();
			        }
			    }
				if(vo.getProjectStatusActiveCopy().getId() != ContentSelectionStatusType.ID_DELETED && vo.getProjectStatus().getId() != ContentSelectionStatusType.ID_ARCHIVED) {
			        if((vo.getProjectStatusActiveCopy().getId() != ContentSelectionStatusType.ID_UNCHANGED && vo.getOriginStatusActiveCopy().getId() == ContentSelectionStatusType.ID_UNCHANGED) || vo.getOriginStatusActiveCopy().getId() == ContentSelectionStatusType.ID_CHANGED || vo.getOriginStatusActiveCopy().getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC){
			        	comparator = vo.getProjectMessageInstanceActiveCopy();
			        	compareTo = vo.getOriginMessageInstanceActiveCopy();
			        }
			    }
			}
			
		    boolean selectableMessage = false;
		    if (vo.getLastesOriginCopy().getContentObject().isDynamicVariantEnabled() || vo.getLastesOriginCopy().getContentObject().isStructuredContentEnabled())
		    {
		        selectableMessage = true;
		    }
	
		    if (vo.getLastesOriginCopy().getContentObject().isMultipartType())
		    {
				Integer partNum = 1;
		        for (ZonePart zonePart : vo.getLastesOriginCopy().getContentObject().getZone().getPartsInSequenceOrder())
		        {
		            Element partElement = contentsElement.addElement("Part");
		            partElement.addAttribute("num", (partNum++).toString());
		            Set<ContentObjectAssociation> partAssociations = getAssociationsForZonePart(zonePart, messageContentAssociations);
		            
	            	boolean isPartEmpty = false;
	    		    for (MessagepointLocale locale : locales)
	                {
	                	if (isPartEmpty)
	                		break;
		            
	                    String languageCode = locale.getLanguageCode();
						String localeCode = locale.getCode();

						boolean missingFlag = true;					
			            for( ContentObjectAssociation ca : partAssociations )
			            {
			                if ( ca.getPGTreeNode() != pgtn )
			                    continue;
			                
			                boolean conflict = false;
			                String contentFinal = "";
			                JSONObject comparedJason = null;
			                if(comparator != null && compareTo != null){
			                	comparedJason = getSyncContentCompareJSON(comparator, compareTo, pgtn.getId(), -1, locale.getId());
			                }
			                try {
								if( comparedJason != null && !comparedJason.get("compared_content").toString().isEmpty()){
									conflict = true;
									contentFinal = comparedJason.getString("compared_content").toString();
								}else if( comparedJason != null 
										&& comparedJason.get("compared_content").toString().isEmpty()
										&& comparedJason.has("is_graphic_content") 
										&& (!comparedJason.get("graphic_path_1").toString().equals(comparedJason.get("graphic_path_2").toString()))){
									conflict = true; 
									contentFinal = comparedJason.getString("tip_text_1").toString();
								}
							} catch (JSONException e) {
								e.printStackTrace();
							}
			                
			                if ( ca.getTypeId() == ContentAssociationType.ID_EMPTY || ( ca.getTypeId() == ContentAssociationType.ID_OWNS && ca.getContent() == null ) )
			                {
			                    partElement.addAttribute("empty", Boolean.TRUE.toString());
			                    isPartEmpty = true;
			                    break;
			                }else if (ca.getTypeId() == ContentAssociationType.ID_SUPPRESSES)
						    {
			                	partElement.addAttribute("suppress", Boolean.TRUE.toString());
						    }
			                else 
			                {
	
		                        if ( ca.getMessagepointLocale().getId() != locale.getId() )
		                            continue;
	
		                        String languageContent = "";
		                        String langImageName = "";
		                        String langImageUploaded = "";
		                        
		                        
		                        boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
		                        Content content = ca.getContent();
	
		                        if (zonePart.getContentType().getId() == ContentType.TEXT) 
		                        {
		                            if ( content != null )
		                            {
		                                if ( content.getEncodedContent() != null )
		                                {
		                                    
		                                    if(!conflict)
		                                    	languageContent = content.getContent();
		    				                else
		    				                	languageContent = contentFinal;
		                                }
		                            } 
		                            else 
		                            {
		                                sameAsParent = true;
		                            }
		                        }
		                        else // graphic type
		                        {
		                            if ( content != null )
		                            {
		                                if ( content.getImageName() != null )
		                                {
		                                    if(!conflict)
		                                    	languageContent = content.getImageName();
		    				                else
		    				                	languageContent = contentFinal;
		                                    if ( content.getAppliedImageFilename() != null )
		                                        langImageName = content.getAppliedImageFilename();
		                                    if ( content.getImageUploadedDate() != null )
		                                        langImageUploaded = DateUtil.formatDateForXMLOutput(content.getImageUploadedDate());
		                                    else
		                                        langImageUploaded = "unknown";
		                                }
		                            } 
		                            else 
		                            {
		                                sameAsParent = true;
		                            }
		                        }
	
								missingFlag = false;
		                        Element contentElement = partElement.addElement("Content");
		                        contentElement.addAttribute("language", languageCode);
								contentElement.addAttribute("locale", localeCode);

		                        if (selectableMessage && sameAsParent){
		                        	if(conflict && !contentFinal.isEmpty()){
					                	contentElement.addCDATA(contentFinal);
				                	}
		                        	if (ca.getTypeId() == ContentAssociationType.ID_SUPPRESSES){
					                	contentElement.addAttribute("suppress", Boolean.TRUE.toString());
								    } else
								    	contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
								}else if (ca.getReferencingImageLibrary() != null) {
									if (ca.getReferencingImageLibrary().isGlobalImage())
										contentElement.addAttribute("imagelibraryid", Long.valueOf(ca.getReferencingImageLibrary().getId()).toString());
									else
										contentElement.addAttribute("localimagelibraryid", Long.valueOf(ca.getReferencingImageLibrary().getId()).toString());
			                	}else{
			                        if (!langImageName.isEmpty())
			                            contentElement.addAttribute("imagename", langImageName);
			                        
			                        if (!langImageUploaded.isEmpty())
			                            contentElement.addAttribute("uploaded", langImageUploaded);
			                        
		                            contentElement.addCDATA(languageContent);
			                    }
		                    }
		                }
						if (missingFlag && !isPartEmpty)
						{
							Element contentElement = partElement.addElement("Content");
					        contentElement.addAttribute("language", locale.getLanguageCode());
							contentElement.addAttribute("locale", localeCode);
			                if (selectableMessage)
			                {
			                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
			                }
			                else
			                {
			                	contentElement.addCDATA("");
			                }
						}					
		            }
		        }
		    }
		    else 
		    {  // not multi-part (text or graphic) 
			    for (MessagepointLocale locale : locales)
		        {
		            String languageCode = locale.getLanguageCode();
					String localeCode = locale.getCode();
					boolean missingFlag = true;
		            for( ContentObjectAssociation ca : messageContentAssociations )
		            {
	                    if ( ca.getPGTreeNode() != pgtn )
	                    {
	                        continue;
	                    }
	
		                if ( ca.getMessagepointLocale() == null || ca.getMessagepointLocale().getId() != locale.getId() )
		                {
		                    continue;
		                }
		                
		                boolean conflict = false;
		                String contentFinal = "";
		                JSONObject comparedJason = null;
		                if(comparator != null && compareTo != null){
		                	comparedJason = getSyncContentCompareJSON(comparator, compareTo, pgtn.getId(), -1, locale.getId());
		                }
		                try {
							if( comparedJason != null && !comparedJason.get("compared_content").toString().isEmpty()){
								conflict = true;
								contentFinal = comparedJason.getString("compared_content").toString();
							}else if( comparedJason != null 
									&& comparedJason.get("compared_content").toString().isEmpty()
									&& comparedJason.has("is_graphic_content") 
									&& (!comparedJason.get("graphic_path_1").toString().equals(comparedJason.get("graphic_path_2").toString()))){
								conflict = true; 
								contentFinal = comparedJason.getString("tip_text_1").toString();
							}
						} catch (JSONException e) {
							e.printStackTrace();
						}
		                
		                String languageContent = "";
		                boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
		                Content content = ca.getContent();
	                    String langImageName = "";
	                    String langImageUploaded = "";
	
		                if (vo.getLastesOriginCopy().getContentObject().getContentType().isText())
		                {
		                    if ( content == null || content.getEncodedContent() == null )
		                    {
		                        sameAsParent = true;
		                    }
		                    else
		                    {
		                    	if(!conflict)
		                    		languageContent = content.getContent(ca.getDocument());
				                else
				                	languageContent = contentFinal;
		                    }
		                }
		                else
		                {
		                    if ( content != null) 
		                    {
		                        if(!conflict)
		                    		languageContent = content.getContent(ca.getDocument());
				                else
				                	languageContent = contentFinal;
		                        if ( content.getAppliedImageFilename() != null )
		                            langImageName = content.getAppliedImageFilename();
		                        if ( content.getImageUploadedDate() != null )
		                            langImageUploaded = DateUtil.formatDateForXMLOutput(content.getImageUploadedDate());
		                        else
		                            langImageUploaded = "unknown";
		                    } 
		                    else
		                    {
		                        sameAsParent = true;
		                    }
		                }
					    
		                missingFlag = false;
		                Element contentElement = contentsElement.addElement("Content");
		                contentElement.addAttribute("language", languageCode);
						contentElement.addAttribute("locale", localeCode);
		                if (vo.getLastesOriginCopy().getContentObject().getContentType().isGraphic() && languageContent != null && !languageContent.isEmpty()){
		                	contentElement.addCDATA(languageContent);
		                }
		                
		                if (sameAsParent && selectableMessage) {
		                	if(conflict && !contentFinal.isEmpty()){
			                	contentElement.addCDATA(contentFinal);
		                	}else if (ca.getTypeId() == ContentAssociationType.ID_SUPPRESSES){
			                	contentElement.addAttribute("suppress", Boolean.TRUE.toString());
						    } else
						    	contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
						}else if (ca.getReferencingImageLibrary() != null) {
							if (ca.getReferencingImageLibrary().isGlobalImage())
								contentElement.addAttribute("imagelibraryid", Long.valueOf(ca.getReferencingImageLibrary().getId()).toString());
							else
								contentElement.addAttribute("localimagelibraryid", Long.valueOf(ca.getReferencingImageLibrary().getId()).toString());
		                } else{
			                if (!langImageName.isEmpty())
			                    contentElement.addAttribute("imagename", langImageName);
			                if (!langImageUploaded.isEmpty())
			                    contentElement.addAttribute("uploaded", langImageUploaded);
			                if(conflict)
					           	contentElement.addCDATA(contentFinal);
					        else
					            contentElement.addCDATA(languageContent);
			            }
		            }
					if (missingFlag)
					{
						Element contentElement = contentsElement.addElement("Content");
				        contentElement.addAttribute("language", languageCode);
						contentElement.addAttribute("locale", localeCode);
		                if (selectableMessage)
		                {
		                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
		                }
		                else
		                {
		                	contentElement.addCDATA("");
		                }
					}				    
		        }
		    }
		
			try {
	            if(switchedSession) {
	                Hibernate.initialize(pgtn);
	            }
	        } finally {
	            if(switchedSession) {
	                HibernateUtil.getManager().restoreSession(mainSessionHolder);
	            }
	        }
		}

	private void createTouchpointTag(
	        Document messageDocument,
	        Element touchpointsElement, 
	        Set<ContentObjectData> dbMessageInstanceVersionMapKeys,
	        TouchpointSelection tpSelection, 
	        Map<Document, List<ContentObjectData>> tpSelectableMessages)
	throws Exception 
	{
		Element touchpointElement = touchpointsElement.addElement("Touchpoint");
		touchpointElement.addAttribute("id", Long.valueOf(messageDocument.getId()).toString());
		Element touchpointNameElement = touchpointElement.addElement("Name");
		String schema = messageDocument.getObjectSchemaName();
		Node node = Node.findBySchema(schema);
		boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(schema != null && ! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(schema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
            switchedSession = true;
        }
		String tpName = "(" +node.getBranch().getName() +  " " + node.getName() + ") " + messageDocument.getName();
		touchpointNameElement.addText(tpName);
		
		ExportXMLUtils.createLanguages(messageDocument, touchpointElement);
		
		List<DocumentSection> documentSections = messageDocument.getDocumentSectionsByOrder();

		if (documentSections != null && !documentSections.isEmpty())
		{
			Element sectionsElement = touchpointElement.addElement("Sections");
			for (DocumentSection documentSection : documentSections)
			{
				Element sectionElement = sectionsElement.addElement("Section");
				Element sectionNameElement = sectionElement.addElement("Name");
				sectionNameElement.addText(documentSection.getName());
				List<Zone> sectionZones = documentSection.getZonesList();

				if ( sectionZones == null || sectionZones.isEmpty() )
				{
				    continue;
				}
				
				Element zonesElement = sectionElement.addElement("Zones");
				for (Zone zone : documentSection.getZonesList()) 
				{
				    if (!zone.isVisible(requestor))
				    {
				        continue;
				    }
				    Element zoneElement = zonesElement.addElement("Zone");
				    zoneElement.addAttribute("id", Long.valueOf(zone.getId()).toString());
				    ContentType zoneContentType = zone.getContentType();
				    zoneElement.addAttribute("type", zoneContentType.getLocaledString());

				    if ( zoneContentType.getId() == ContentType.GRAPHIC ||
 			             zoneContentType.getId() == ContentType.TEXT_OR_GRAPHIC) 
				    {
				        zoneElement.addAttribute("graphictype", zone.getGraphicType());
				    }

				    Element zoneNameElement = zoneElement.addElement("Name");
				    zoneNameElement.addText(zone.getFriendlyName());
				    if ( !zone.isMultipart() || zone.getParts() == null || zone.getParts().isEmpty() )
				    {
				        continue;
				    }

				    Element partsElement = zoneElement.addElement("Parts");
				    partsElement.addAttribute("count", Integer.valueOf(zone.getParts().size()).toString());
					Integer partNum = 1;
				    for (ZonePart zonePart : zone.getPartsInSequenceOrder())
				    {
				        Element partElement = partsElement.addElement("Part");
				        partElement.addAttribute("number", (partNum++).toString());
				        ContentType zonePartContentType = zonePart.getContentType();
				        partElement.addAttribute("type", zonePartContentType.getLocaledString());

				        if ( zonePartContentType.getId() == ContentType.GRAPHIC ||
				             zonePartContentType.getId() == ContentType.TEXT_OR_GRAPHIC) 
				        {
				            partElement.addAttribute("graphictype", zonePart.getGraphicType());
				        }
				        partElement.addText(zonePart.getName());
				    }
				}
			}
		}
		if (tpSelection != null) 
		{
			Element selectionsElement = touchpointElement.addElement("Selections");
			if (messageDocument.isEnabledForVariation()) 
			{
				createSelectorTag(CloneHelper.queryInSchema(schema,  ()->messageDocument.getSelectionParameterGroup()), selectionsElement);

                List<TouchpointSelection> tpSelections = new ArrayList<>(CloneHelper.queryInSchema(schema, () -> tpSelection.getAllChildrenAndGrandchildren()));
				tpSelections.add(tpSelection);

				TouchpointSelection masterTouchpointSelection = CloneHelper.queryInSchema(schema,  ()->messageDocument.getMasterTouchpointSelection());
				createSelectionTagForTP(masterTouchpointSelection, tpSelections, selectionsElement, dbMessageInstanceVersionMapKeys,tpSelectableMessages);
			}
		}
		try {
            if(switchedSession) {
                Hibernate.initialize(messageDocument);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
	}
	
	private void createSelectorTag(
	        ParameterGroup parameterGroup, 
	        Element element)
	{
		Element selectorElement = element.addElement("Selector");
		Element selectorNameElement = selectorElement.addElement("Name");
		selectorNameElement.addText(CloneHelper.queryInSchema(parameterGroup.getObjectSchemaName(), ()->parameterGroup.getName()));
		Element parametersElement = selectorElement.addElement("Parameters");
		
		for (Parameter parameter : CloneHelper.queryInSchema(parameterGroup.getObjectSchemaName(), ()->parameterGroup.getParameters())) 
		{
			Element parameterElement = parametersElement.addElement("Parameter");
			parameterElement.addText(CloneHelper.queryInSchema(parameterGroup.getObjectSchemaName(), ()->parameter.getName()));
		}

	}

	private void createSelectionTag(
			ProjectSyncObjectListVO vo,
	        List<ContentObjectAssociation> msgContentAssociations,
	        ContentObjectAssociation messageContentAssociation,
	        Set<Document> documents,
	        Element element)
	{
		ContentObject model = vo.getObject();
        Document doc = vo.getOriginTP();
        
        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        String schema = vo.getOriginSchema();
        if(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(schema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
            switchedSession = true;
        }

		Element selectionElement = element.addElement("Selection");
		ParameterGroupTreeNode parameterGroupTreeNode = messageContentAssociation.getPGTreeNode();
		if(parameterGroupTreeNode == null) {
		    if(model.getOwningTouchpointSelection() != null) {
                parameterGroupTreeNode = model.getOwningTouchpointSelection().getParameterGroupTreeNode();
            } else {
		        parameterGroupTreeNode = model.getDocument().getMasterTouchpointSelection().getParameterGroupTreeNode();
            }
        }
		long id = parameterGroupTreeNode.getId();
		TouchpointSelection touchpointSelection = TouchpointSelection.findByPgTreeNodeId(parameterGroupTreeNode.getId());

		if (touchpointSelection != null) 
		{
			id = touchpointSelection.getId();
		}
		
		selectionElement.addAttribute("id", Long.valueOf(id).toString());
		if (parameterGroupTreeNode.getParameterGroupInstanceCollection() != null)
		{
			ParameterGroupInstanceCollection collection  = parameterGroupTreeNode.getParameterGroupInstanceCollection();
			selectionElement.addAttribute("datavaluerefid", Long.valueOf(collection.getId()).toString());
		}
		
		Element selectionNameElement = selectionElement.addElement("Name");
		selectionNameElement.addText(parameterGroupTreeNode.getName());
		Element selectionContentsElement = selectionElement.addElement("Contents");
		createContentTagForSelection(vo, msgContentAssociations, parameterGroupTreeNode, selectionContentsElement);
		
		List<ParameterGroupTreeNode> ChildNodes = parameterGroupTreeNode.getChildren();
		
		
		if (ChildNodes != null && !ChildNodes.isEmpty())
		{
			for (ParameterGroupTreeNode childNode : ChildNodes) 
			{
				List<ContentObjectAssociation> childContentAssociationList = ContentObjectAssociation.findAllByContentObjectAndParameters(model, model.getFocusOnDataType(), childNode, null);
                ContentObjectAssociation childContentAssociation = childContentAssociationList.stream().findFirst().orElse(null);
				createSelectionTag(vo, childContentAssociationList, childContentAssociation, documents, selectionElement);
			}
		}
		try {
            if(switchedSession) {
                Hibernate.initialize(schema);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
	}

	private void createSelectionTagForTP(
	        TouchpointSelection tpSelection,
	        List<TouchpointSelection> tpSelections,
	        Element element, 
	        Set<ContentObjectData> dbMessageInstanceVersionMapKeys, 
	        Map<Document, List<ContentObjectData>> tpSelectableMessages) 
	{
		Element selectionElement = element.addElement("Selection");
		Document document = tpSelection.getDocument();
		boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        String schema = this.syncFromOther?this.otherDocument.getObjectSchemaName():this.projectDocument.getObjectSchemaName();
        if(schema != null && ! MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(schema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
            switchedSession = true;
        }
		ParameterGroupTreeNode parameterGroupTreeNode = tpSelection.getParameterGroupTreeNode();

		selectionElement.addAttribute("id", Long.valueOf(tpSelection.getId()).toString());
		if (parameterGroupTreeNode.getParameterGroupInstanceCollection() != null) {
			selectionElement.addAttribute("datavaluerefid", Long.valueOf(parameterGroupTreeNode.getParameterGroupInstanceCollection().getId()).toString());
		}

		Element selectionNameElement = selectionElement.addElement("Name");
		selectionNameElement.addText(parameterGroupTreeNode.getName());
		Element selectionContentsElement = selectionElement.addElement("Contents");

		
		

		if ( tpSelectableMessages.get(document) != null &&
		     !tpSelectableMessages.get(document).isEmpty()) 
		{
			for (ContentObjectData tpSelectableMessage : tpSelectableMessages.get(document))
			{
				if ( !tpSelectableMessage.getContentObject().isStructuredContentEnabled() )
				{
				    continue;
				}

				Element messageElement = selectionContentsElement.addElement("Message");
				messageElement.addAttribute("refid", Long.valueOf(tpSelectableMessage.getId()).toString());
				ContentObjectAssociation messageContentAssociation = ContentObjectAssociation.findByContentObjectAndParameters(tpSelectableMessage.getModel(), tpSelectableMessage.getModel().getFocusOnDataType(), parameterGroupTreeNode, null, null);
				List<ContentObjectAssociation> nodeContent = ContentObjectAssociation.findAllByContentObjectAndParameters(tpSelectableMessage.getModel(), tpSelectableMessage.getModel().getFocusOnDataType(), parameterGroupTreeNode, null);

				if (messageContentAssociation != null)
				{
					if (true)
					{
					    if (messageContentAssociation.getTypeId() == ContentAssociationType.ID_SUPPRESSES)
					    {
					        messageElement.addAttribute("suppress", Boolean.TRUE.toString());
					    }
					    else if (messageContentAssociation.getTypeId() == ContentAssociationType.ID_OWNS)
					    {
					        messageElement.addAttribute("custom", Boolean.TRUE.toString());
					        createContentTagForSelection(this.aaData.get(tpSelectableMessage.getModel()), nodeContent, parameterGroupTreeNode, messageElement);
					    }
					    else if (messageContentAssociation.getTypeId() == ContentAssociationType.ID_REFERENCES)
					    {
					        messageElement.addAttribute("sameasparent", Boolean.TRUE.toString());
					        TouchpointSelection parentSelection = TouchpointSelection.findByPgTreeNodeId(messageContentAssociation.getPGTreeNode().getId());
					        messageElement.addAttribute("parentrefid", Long.valueOf(parentSelection.getId()).toString());
						}
					    else if (messageContentAssociation.getTypeId() == ContentAssociationType.ID_EMPTY)
					    {
						    messageElement.addAttribute("empty", Boolean.TRUE.toString());
					    }
					}
							
					// Set content selection status 		
					ContentSelectionStatusType contentSelectionStatus = new ContentSelectionStatusType(messageContentAssociation.getContentSelectionStatusId());
					messageElement.addAttribute("status", contentSelectionStatus.getDisplayText());
				}
						
			}
		}

		Element messagesElement = selectionElement.addElement("Messages");
		if ( tpSelectableMessages.get(document) != null &&
		     !tpSelectableMessages.get(document).isEmpty()) 
		{
			for (ContentObjectData tpSelectableMessage : tpSelectableMessages.get(document)) 
			{
				if ( !tpSelectableMessage.getContentObject().isVariantType() ) {
				    continue;
				}

				ContentObjectZonePriority contentObjectZonePriority =
						ContentObjectZonePriority.findByContentObjectAndZoneUnique(
				            tpSelectableMessage.getContentObject(), tpSelectableMessage.getContentObject().getZone(), tpSelection );
				Element messageElement = messagesElement.addElement("Message");
				messageElement.addAttribute("id", Long.valueOf(tpSelectableMessage.getId()).toString());
				if (contentObjectZonePriority != null && contentObjectZonePriority.isSuppress())
				{
				    messageElement.addAttribute("suppressed", Boolean.TRUE.toString());
				}
                if (contentObjectZonePriority != null && contentObjectZonePriority.isRepeatWithNext())
                {
                    messageElement.addAttribute("repeatwithnext", Boolean.TRUE.toString());
                }
			}
		}
		
		List<TouchpointSelection> childSelections = tpSelection.getChildrenOrderByName();
		if (childSelections != null && !childSelections.isEmpty())
		{
			for (TouchpointSelection childSelection : childSelections) 
			{
				if ( tpSelections == null || tpSelections.isEmpty() || tpSelections.contains(childSelection)) 
				{
					createSelectionTagForTP(childSelection, tpSelections, selectionElement, dbMessageInstanceVersionMapKeys, tpSelectableMessages);
				}
			}
		}
		try {
            if(switchedSession) {
                Hibernate.initialize(document);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
	}

	private Set<ContentObjectAssociation> getAssociationsForZonePart(
	        ZonePart zp,
	        Set<ContentObjectAssociation> contAssocs )
	{
        Set<ContentObjectAssociation> result = new HashSet<>();
        for( ContentObjectAssociation ca : contAssocs )
        {
            if ( ca.getZonePart() != null && ca.getZonePart().getId() == zp.getId() )
            {
                result.add(ca);
            }
        }
        return result;
	}

	private Set<ContentObjectAssociation> getAssociationsForZonePart(
	        ZonePart zp,
	        List<ContentObjectAssociation> contAssocs)
	{
	    Set<ContentObjectAssociation> result = new HashSet<>();
	    for( ContentObjectAssociation ca : contAssocs )
	    {
	        if ( ca.getZonePart() != null && ca.getZonePart().getId() == zp.getId() )
	        {
	            result.add(ca);
	        }
	    }
	    return result;
	}
	
	private JSONObject getSyncContentCompareJSON(ContentObjectData compareFrom, ContentObjectData compareTo, long variantId, long zonePartId, long langCodeId) {
		boolean variantIsFromOther = variantId < 0;
		if(variantIsFromOther) {
		    variantId = - variantId - ParameterGroupTreeNode.OTHER_PGTNID_OFFSET;
		}
		long variantIdFinal = variantId;
		
		String schema = compareFrom.getObjectSchemaName();// compareFrom.getObjectSchemaName();
		String compareToSchema = (schema.equals(this.projectDocument.getObjectSchemaName())?this.otherDocument.getObjectSchemaName():this.projectDocument.getObjectSchemaName());
		
		JSONObject returnObj = new JSONObject();
		try{
			ContentObjectData compareFromMi = CloneHelper.queryInSchema(schema, ()->ContentObjectData.findByGuid(compareFrom.getGuid()));
			ContentObjectData compareToMi = CloneHelper.queryInSchema(compareToSchema, ()->ContentObjectData.findByGuid(compareTo.getGuid()));
			ContentObject compareFromMsg = CloneHelper.queryInSchema(schema, ()->compareFromMi.getContentObject());
			ContentObject compareToMsg = CloneHelper.queryInSchema(compareToSchema, ()->compareToMi.getContentObject());
			Document compareFromDocument = CloneHelper.queryInSchema(schema, ()->compareFromMsg.getDocument());
			Document compareToDocument = CloneHelper.queryInSchema(compareToSchema, ()->compareToMsg.getDocument());
			
			ParameterGroupTreeNode compareFromPgtn = null, compareToPgtn = null;
			if(compareFromMsg.isStructuredContentEnabled()){
				ParameterGroupTreeNode pgtn = CloneHelper.queryInSchema(compareToSchema, ()->ParameterGroupTreeNode.findById(variantIdFinal));
				if(pgtn != null) {
                    compareFromPgtn = CloneHelper.queryInSchema(schema, ()->ParameterGroupTreeNode.findByDnaAndDocument(pgtn, compareFromDocument));
                    compareToPgtn = CloneHelper.queryInSchema(compareToSchema, ()->ParameterGroupTreeNode.findByDnaAndDocument(pgtn, compareToDocument));
				}
			}else if(compareFromMi.isDynamicVariantEnabled()){
                ParameterGroupTreeNode pgtn = CloneHelper.queryInSchema(compareToSchema, ()->ParameterGroupTreeNode.findById(variantIdFinal));
                if(pgtn != null) {
                    String pgtnDna = pgtn.getDna();
                    compareFromPgtn = CloneHelper.queryInSchema(schema, ()->ParameterGroupTreeNode.findAllNodesForDynamicAsset(compareFromMsg, compareFromMi.getDataType()).stream().filter(p->p.getDna().equals(pgtnDna)).findFirst().orElse(null));
                    compareToPgtn = CloneHelper.queryInSchema(compareToSchema, ()->ParameterGroupTreeNode.findAllNodesForDynamicAsset(compareToMsg, compareToMi.getDataType()).stream().filter(p->p.getDna().equals(pgtnDna)).findFirst().orElse(null));
                }
			}
			
			MessagepointLocale localeFrom = CloneHelper.queryInSchema(schema, ()->MessagepointLocale.findById(langCodeId));
			MessagepointLocale localeTo = CloneHelper.queryInSchema(compareToSchema, ()->MessagepointLocale.findById(langCodeId));
			ContentObjectAssociation ca1 = null;
            ContentObjectAssociation ca2 = null;
			
			// We don't know where the zone part is from, origin or child.
			// So we always find by dna and document
			ZonePart zonePart = CloneHelper.queryInSchema(schema, ()->ZonePart.findById(zonePartId));
			ZonePart compareFromZonePart = zonePart == null ? null : CloneHelper.queryInSchema(schema, ()->ZonePart.findByDnaAndDocument(zonePart, compareFromDocument));
			ZonePart compareToZonePart =  zonePart == null ? null : CloneHelper.queryInSchema(compareToSchema, ()->ZonePart.findByDnaAndDocument(zonePart, compareToDocument));
			ParameterGroupTreeNode compareFromPgtnFinal = compareFromPgtn;
			 if((compareFromZonePart == null && compareToZonePart != null) || (compareFromPgtn == null && compareToPgtn != null))
			     ca1 = CloneHelper.queryInSchema(schema, ()->new ContentObjectAssociation());
			 else
				 ca1 = CloneHelper.queryInSchema(schema, ()->getContentFromMessageInstanceAndAssociation(compareFromMsg, compareFromMi, compareFromZonePart, localeFrom, compareFromPgtnFinal));
			
			ParameterGroupTreeNode compareToPgtnFinal = compareToPgtn;
             if((compareFromZonePart != null && compareToZonePart == null) || (compareFromPgtn != null && compareToPgtn == null))
				 ca2 = CloneHelper.queryInSchema(compareToSchema, ()->new ContentObjectAssociation());
             else
				 ca2 = CloneHelper.queryInSchema(compareToSchema, ()->getContentFromMessageInstanceAndAssociation(compareToMsg, compareToMi, compareToZonePart, localeTo, compareToPgtnFinal));
			
			if(ca1 != null && ca2 != null){
				ContentObjectAssociation ca1Final = ca1;
                ContentObjectAssociation ca2Final = ca2;
				returnObj.put("content_suppressed_1", CloneHelper.queryInSchema(schema, ()->ca1Final.getTypeId() == ContentAssociationType.ID_SUPPRESSES));
				returnObj.put("content_suppressed_2",  CloneHelper.queryInSchema(compareToSchema, ()->ca2Final.getTypeId()) == ContentAssociationType.ID_SUPPRESSES);

                String content1 = ca1Final.getContent()!=null?CloneHelper.queryInSchema(schema, ()->ca1Final.getContent().getContent(compareFromMsg.getDocument())):"";
                String content2 = CloneHelper.queryInSchema(compareToSchema, ()->ca2Final.getContent()!=null?ca2Final.getContent().getContent(compareToMsg.getDocument()):"");
				
				if(content1 == null) {
				    content1 = "";
				}
				
				if(content2 == null) {
				    content2 = "";
				}
//				if(this.syncFromOther)
				compareContent(returnObj, ca1Final, ca2Final, content1, content2, compareTo.getId()==-1);
//				else
//					compareContent(returnObj, ca2, ca1, content1, content2, compareTo.getId()==-1);
			}else{
				returnObj.put("compared_content", "");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Error: Unable to get content: " + e );
		}
		return returnObj;
	}

	private void compareContent(JSONObject returnObj, ContentObjectAssociation ca1, ContentObjectAssociation ca2, String content1, String content2, boolean singleSelect){
		try{
			if(content1.isEmpty() && content2.isEmpty()) {
			    String result = getSuppressedOrEmptyOrBlankMsg(ca1.getTypeId()) + ((ca1.getTypeId() == ca2.getTypeId()) ? "" : getSuppressedOrEmptyOrBlankMsg(ca2.getTypeId()));
	            returnObj.put("compared_content", result);
			} else {
				if(singleSelect){
					returnObj.put("compared_content", content1);
				}else{
				    if(content1 != null && ! content1.isEmpty()) {
				        String content1Final = content1;
				        content1 = CloneHelper.queryInSchema(ca1.getObjectSchemaName(), ()->ContentObjectContentUtil.replaceIdWithDna(content1Final, ca1.getContent()));
				    }
				    
				    if(content2 != null && ! content2.isEmpty()) {
				        String content2Final = content2;
				        content2 = CloneHelper.queryInSchema(ca2.getObjectSchemaName(), ()->ContentObjectContentUtil.replaceIdWithDna(content2Final, ca2.getContent()));
				    }
					String result = StringsDiffUtils.diff(content1, content2);
					returnObj.put("compared_content", result);
				}
			}
			String imagePath1 = "", imagePath2 = "", imageName1 = "", imageName2 = "", appliedImageName1 = "", appliedImageName2 = "";
			if(CloneHelper.queryInSchema(ca1.getObjectSchemaName(), ()->ca1.getContent()!=null && ca1.getContent().getImageLocation() != null)) {
				imagePath1 = CloneHelper.queryInSchema(ca1.getObjectSchemaName(), ()->ca1.getContent().getImageLocation());
				imageName1 = CloneHelper.queryInSchema(ca1.getObjectSchemaName(), ()->ca1.getContent().getImageName());
				appliedImageName1 = CloneHelper.queryInSchema(ca1.getObjectSchemaName(), ()->ca1.getContent().getAppliedImageFilename()!=null?ca1.getContent().getAppliedImageFilename(): ApplicationUtil.getMessage("page.label.none"));
			}
			if(CloneHelper.queryInSchema(ca2.getObjectSchemaName(), ()->ca2.getContent() !=null && ca2.getContent().getImageLocation() != null)) {
				imagePath2 = CloneHelper.queryInSchema(ca2.getObjectSchemaName(), ()->ca2.getContent().getImageLocation());				
				imageName2 = CloneHelper.queryInSchema(ca2.getObjectSchemaName(), ()->ca2.getContent().getImageName());
				appliedImageName2 = CloneHelper.queryInSchema(ca2.getObjectSchemaName(), ()->ca2.getContent().getAppliedImageFilename()!=null?ca2.getContent().getAppliedImageFilename(): ApplicationUtil.getMessage("page.label.none"));
			}
			returnObj.put("graphic_path_1", HttpRequestUtil.getFileResourceToken(imagePath1));
			returnObj.put("graphic_name_1", imageName1);
			String tipText1 = "<table><tr><td style='text-align: left;'>"
											+ ApplicationUtil.getMessage("page.label.content.file") + ": " + imageName1
											+ "</td></tr><tr><td style='text-align: left;'>"
											+ ApplicationUtil.getMessage("client_messages.content_editor.applied_image_name") + ": " + appliedImageName1
											+ "</td></tr></table>";
			returnObj.put("tip_text_1", tipText1);
			returnObj.put("graphic_path_2", HttpRequestUtil.getFileResourceToken(imagePath2));
			returnObj.put("graphic_name_2", imageName2);
			String tipText2 = "<table><tr><td style='text-align: left;'>"
											+ ApplicationUtil.getMessage("page.label.content.file") + ": " + imageName2
											+ "</td></tr><tr><td style='text-align: left;'>"
											+ ApplicationUtil.getMessage("client_messages.content_editor.applied_image_name") + ": " + appliedImageName2
											+ "</td></tr></table>";
			returnObj.put("tip_text_2", tipText2);
			if(!imagePath1.isEmpty() || !imagePath2.isEmpty()){
				returnObj.put("is_graphic_content", true);
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Error: Unable to get content: " + e );
		}
	}

    private String getSuppressedOrEmptyOrBlankMsg(int typeId) {
        String result = "";
        
        if(typeId == ContentAssociationType.ID_SUPPRESSES) {
            result = "<div class=\"contentEditor_SuppressContainer\" >" +
                                "<div class=\"contentEditor_InfoDivContainer\">" +
                                    ApplicationUtil.getMessage("client_messages.text.content_suppressed") +
                                "</div>" + 
                            "</div>";
        } else if(typeId == ContentAssociationType.ID_EMPTY || typeId == ContentAssociationType.ID_NONE) {
            result = "<div class=\"contentEditor_SuppressContainer\" >" +
                    "<div class=\"contentEditor_InfoDivContainer\">" +
                        ApplicationUtil.getMessage("client_messages.text.content_empty") +
                    "</div>" + 
                "</div>";
        }
        
        return result;
    }
    
    private ContentObjectAssociation getContentFromMessageInstanceAndAssociation(ContentObject compareFromMsg, ContentObjectData compareFromMi, ZonePart compareFromZonePart, MessagepointLocale locale, ParameterGroupTreeNode compareFromPgtn) {
        MessagepointLocale systemDefaultLanguage = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->MessagepointLocale.getDefaultSystemLanguageLocale());
        ContentObjectAssociation ca = null;

        if((!compareFromMi.isDynamicVariantEnabled()) && (!compareFromMsg.isStructuredContentEnabled())) {
            ca = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->ContentObjectAssociation.findByContentObjectAndParameters(compareFromMsg, compareFromMi.getDataType(), null, locale, compareFromZonePart));
        }else if(compareFromMsg.isStructuredContentEnabled()){
            if(compareFromPgtn != null) {
                ca = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->ContentObjectAssociation.findByContentObjectAndParameters(compareFromMsg, compareFromMi.getDataType(), compareFromPgtn, locale, compareFromZonePart));
            } else {
                ca = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->ContentObjectAssociation.findByContentObjectAndParameters(compareFromMsg, compareFromMi.getDataType(), compareFromPgtn, locale, compareFromZonePart));
//                ca = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->ContentObjectAssociation.findLanguageContentAssociation(compareFromMi.getId(), compareFromZonePart == null ? -1 : compareFromZonePart.getId(), locale.getLanguageCode(), ContentObjectData.class));
            }
        }else if(compareFromMi.isDynamicVariantEnabled()) {
            if(CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->compareFromMi.isActive())) {
                if(compareFromPgtn != null){
                    ca = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->ContentObjectAssociation.findByContentObjectAndParameters(compareFromMsg, compareFromMi.getDataType(), compareFromPgtn, locale, compareFromZonePart));
                } else {
                    ca = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->ContentObjectAssociation.findByContentObjectAndParameters(compareFromMsg, compareFromMi.getDataType(), null, locale, compareFromZonePart));
//                    ca = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->ContentObjectAssociation.findLanguageContentAssociation(compareFromMi.getId(), compareFromZonePart == null ? -1 : compareFromZonePart.getId(), locale.getLanguageCode(), ContentObjectData.class));
                }
            } else {
                if(compareFromPgtn != null){
                    ca = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->ContentObjectAssociation.findByContentObjectAndParameters(compareFromMsg, compareFromMi.getDataType(), compareFromPgtn, locale, compareFromZonePart));
//                    ca = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->ContentObjectAssociation.findByMessageTreeNodeAndMessagepointLocale(compareFromMsg.getId(), compareFromPgtn.getId(), locale, compareFromZonePart));
                } else {
                    ca = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->ContentObjectAssociation.findByContentObjectAndParameters(compareFromMsg, compareFromMi.getDataType(), null, locale, compareFromZonePart));
//                    ca = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->ContentObjectAssociation.findLanguageContentAssociation(compareFromMi.getId(), compareFromZonePart == null ? -1 : compareFromZonePart.getId(), locale.getLanguageCode(), ContentObjectData.class));
                }
            }
        }
        
        if(ca == null){
            ca =  CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->new ContentObjectAssociation()); 
        } else if(ca.getTypeId () == ContentAssociationType.ID_SAME_AS_DEFAULT) {
            ca = getContentFromMessageInstanceAndAssociation(compareFromMsg, compareFromMi, compareFromZonePart, systemDefaultLanguage, compareFromPgtn);
        } else if(ca.getTypeId () == ContentAssociationType.ID_REFERENCES) {
            if(compareFromPgtn != null) {
                ContentObjectAssociation caF = ca;
                ParameterGroupTreeNode referencingNode = CloneHelper.queryInSchema(compareFromMi.getObjectSchemaName(), ()->caF.getReferencingPGTreeNode());
                ca = getContentFromMessageInstanceAndAssociation(compareFromMsg, compareFromMi, compareFromZonePart, locale, referencingNode);
            }
        }

        return ca;
    }
    
    private <T extends IdentifiableMessagePointModel> String getDnaAndName(T t) {
	    String dna = t.getDna();
	    String name = t.getName();
	    if(t instanceof VersionedModel) {
	        String instanceName = CloneHelper.queryInSchema(t.getObjectSchemaName(), ()->{
    	        VersionedInstance instance = ((VersionedModel) t).getProduction();
    	        if(instance == null) instance = ((VersionedModel) t).getWorkingCopy();
    	        if(instance == null) { 
    	            ModelVersionMapping versionMapping = ((VersionedModel) t).getLatestArchivedVersionInfo();
    	            if(versionMapping != null) {
    	                instance = versionMapping.getModelInstance();
    	            }
    	        }
    	        return (instance != null) ? instance.getName() : null;
	        });
	        if(instanceName != null) name = instanceName;
	    }
	    return dna + "-" + name;
	}
    
    /**
	 * Build the items list based on the input parameters
	 */
	@FunctionalInterface
	interface FindSyncObjects<DOC1, DOC2, DOC2SCHEMA, FROMORIGIN, RESULTMAP> {
	    public RESULTMAP apply(DOC1 d1, DOC2 d2, DOC2SCHEMA s, FROMORIGIN b);
	}
	
	private void createSmartTextTag(Map<Long, Long> embeddedContentObjectMap, Element smartTextsElement, Document doc)
	{
        createContentObjectTag(embeddedContentObjectMap, smartTextsElement, doc, "SmartText");
	}

    private void createMessageTag(Map<Long, Long> messagesMap, Element messagesElement, Document doc)
    {
        createContentObjectTag(messagesMap, messagesElement, doc, "Message");
    }

    private void createContentObjectTag(Map<Long, Long> messagesMap, Element messagesElement, Document doc, String elementTagName) {
        String sourceSchema = syncFromOther ? otherSchema : currentSchema;
        String targetSchema = syncFromOther ? currentSchema : otherSchema;

        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if(!MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(sourceSchema)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
            switchedSession = true;
        }
        for(Long objectIdx10 : messagesMap.keySet()){
            long objectId = objectIdx10 / 0x10;
            long objectStatus = messagesMap.get(objectIdx10);
            boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
            objectStatus = objectStatus & 0xFFFF;
            int projectStatus = (int) ((objectStatus / 0x100) & 0xF);
            int projectActiveCopyStatus = (int) ((objectStatus / 0x1000) & 0xF);
            int originStatus = (int) (objectStatus & 0xF);
            int originActiveCopyStatus = (int) ((objectStatus / 0x10) & 0xF);

            String objectSchema = objectIsFromOther ? sourceSchema : targetSchema;

            CloneHelper.execInSchema(objectSchema, ()->{
                ContentObject contentObject = ContentObject.findById(objectId);
                ContentObjectData contentObjectData = contentObject.getLatestContentObjectDataActiveCentric();
                if(contentObjectData == null) {
                    return;
                }

                if (contentObjectData.isWorking())
                    contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
                else if (contentObjectData.isActive())
                    contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
                if(contentObjectData == null) {
                    return;
                }

                Element messageElement = messagesElement.addElement(elementTagName);

                messageElement.addAttribute("id", Long.toString(contentObject.getId()));

                String type = "";
                if (contentObject.isStructuredContentEnabled()) {
                    type = "Structured";
                }
                else if (contentObjectData.isDynamicVariantEnabled())
                {
                    type = "Dynamic";
                }
                else if (contentObject.isMessage() || contentObject.isLocalContentObject()) {
                    type = "Static";
                }
                else if (! contentObject.isVariableContentEnabled())
                {
                    type = "Global";
                }
                else
                {
                    type = "Regular";
                }

                messageElement.addAttribute("type", type);

                String contentType = "Text";
                if(contentObject.isGlobalImage() || contentObject.isLocalImage()) {
                    contentType = "Image";
                }

                messageElement.addAttribute("contenttype", contentType);

                messageElement.addAttribute("nextaction", contentObject.getActionRequired());
                if(contentObjectData.isActive()){
                    messageElement.addAttribute("assignedto", "N/A");
                } else {
                    messageElement.addAttribute("assignedto", contentObject.getAssignedToUserName());
                }

                messageElement.addAttribute("nextaction", contentObject.getActionRequired());

                if(contentObjectData.isActive()){
                    messageElement.addAttribute("assignedto", "N/A");
                }else{
                    messageElement.addAttribute("assignedto", contentObject.getAssignedToUserName());
                }

                messageElement.addAttribute("defaultlanguage", contentObject.getDefaultContentObjectLanguageAsLocale().getLanguageCode());

                if(contentObject.hasWorkingData()) {
                    ContentObjectData workingCopy = contentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
                    Element versionElement = messageElement.addElement("Version");
                    createVersionTag(contentObject, workingCopy, versionElement, projectStatus, originStatus);
                }

                if(contentObject.hasActiveData()) {
                    ContentObjectData activeCopy = contentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
                    Element versionElement = messageElement.addElement("Version");
                    createVersionTag(contentObject, activeCopy, versionElement, projectActiveCopyStatus, originActiveCopyStatus);
                }
            });

/*
            Element versionElement = messageElement.addElement("Version");

            versionElement.addAttribute("id", getContentObjectDataID(contentObjectData));

            versionElement.addAttribute("guid", contentObjectData.getGuid());
            versionElement.addAttribute("status", contentObjectData.getStatus().getLocaledString());

            String versionOrigin = contentObjectData.getCreationReason() == null ? null : contentObjectData.getCreationReason().getLocaledString();
            if(versionOrigin == null) versionOrigin = "";

            versionElement.addAttribute("origin", versionOrigin);
            ContentSelectionStatusType versionStatusType = new ContentSelectionStatusType((int)projectStatus);
            versionElement.addAttribute("versionStatus", versionStatusType != null?versionStatusType.getDisplayText():"");
            ContentSelectionStatusType sourceStatusType = new ContentSelectionStatusType((int)originStatus);
            versionElement.addAttribute("sourceStatus", sourceStatusType != null? sourceStatusType.getDisplayText():"");
            versionElement.addAttribute("deliverytypeid", Long.toString(contentObject.getDeliveryType()));
            versionElement.addAttribute("usagetypeid", Long.toString(contentObject.getUsageTypeId()));

            Element nameElement = versionElement.addElement("Name");
            nameElement.addText(contentObject.getName());
*/
        }
        try {
            if(switchedSession) {
                Hibernate.initialize(sourceSchema);
            }
        } finally {
            if(switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

    private void createVersionTag(ContentObject contentObject, ContentObjectData contentObjectData, Element versionElement, int projectStatus, int originStatus) {
        versionElement.addAttribute("id", getContentObjectDataID(contentObjectData));

        versionElement.addAttribute("guid", contentObjectData.getGuid());
        versionElement.addAttribute("status", contentObjectData.getStatus().getLocaledString());

        String versionOrigin = contentObjectData.getCreationReason() == null ? null : contentObjectData.getCreationReason().getLocaledString();
        if(versionOrigin == null) versionOrigin = "";

        versionElement.addAttribute("origin", versionOrigin);
        ContentSelectionStatusType versionStatusType = new ContentSelectionStatusType((int)projectStatus);
        versionElement.addAttribute("versionStatus", versionStatusType != null?versionStatusType.getDisplayText():"");
        ContentSelectionStatusType sourceStatusType = new ContentSelectionStatusType((int)originStatus);
        versionElement.addAttribute("sourceStatus", sourceStatusType != null? sourceStatusType.getDisplayText():"");
        versionElement.addAttribute("deliverytypeid", Long.toString(contentObject.getDeliveryType()));
        versionElement.addAttribute("usagetypeid", Long.toString(contentObject.getUsageTypeId()));

        Element nameElement = versionElement.addElement("Name");
        nameElement.addText(contentObject.getName());
    }

	public static class CommentIdComparator implements Comparator<ContentObjectComment> {

		@Override
		public int compare(ContentObjectComment arg0, ContentObjectComment arg1) {
			int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
			return result;
		}
	}

	public List<String> getSelectedObjects() {
		return selectedObjects;
	}

	public long getSiblingParentInstanceId() {
		return siblingParentInstanceId;
	}

	public void setSiblingParentInstanceId(long siblingParentInstanceId) {
		this.siblingParentInstanceId = siblingParentInstanceId;
	}

	public long getParentInstanceId() {
		return parentInstanceId;
	}

	public void setParentInstanceId(long parentInstanceId) {
		this.parentInstanceId = parentInstanceId;
	}

	public long getSiblingParentId() {
		return siblingParentId;
	}

	public void setSiblingParentId(long siblingParentId) {
		this.siblingParentId = siblingParentId;
	}

	public long getParentId() {
		return parentId;
	}

	public void setParentId(long parentId) {
		this.parentId = parentId;
	}
	
	
}
