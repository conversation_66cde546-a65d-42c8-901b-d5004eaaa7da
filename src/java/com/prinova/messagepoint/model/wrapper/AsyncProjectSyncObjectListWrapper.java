package com.prinova.messagepoint.model.wrapper;

import com.prinova.messagepoint.controller.touchpoints.AssignmentFilterType;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.common.ContentSelectionStatusType;
import com.prinova.messagepoint.model.common.SyncObjectType;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.dataadmin.LookupTable;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataElement;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.ConditionItem;
import com.prinova.messagepoint.model.targeting.ConditionSubelement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.wtu.ReferencableObject;
import org.apache.commons.logging.Log;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public class AsyncProjectSyncObjectListWrapper extends AsyncAbstractListWrapper{
    private static final Log log = LogUtil.getLog(AsyncProjectSyncObjectListWrapper.class);
	
	private Map<Object, Long> 	objectMap = new LinkedHashMap<>();
	private long 				documentId;
	private long                otherDocumentId;
	private long                instanceId;
	private boolean				syncFromOrigin;

	private boolean             syncMultiWay;
	private boolean             compareActiveCopyOnly;
	
	private int					totalRecords = 0;
	private int					totalDisplayRecords = 0;
	private int					syncCheckBoxRequest = 0;
	private String              otherSchema;
	private long                parentId;
	private long                parentInstanceId;
	private long                otherParentId;
	private long                otherParentInstanceId;
	private Set<Long>           languagesForSync = new HashSet<>();

	public AsyncProjectSyncObjectListWrapper(String sSearch, Map<String, String> orderByMap, int pageSize, int pageIndex, long documentId, long otherDocumentId, long instanceId, boolean syncFromOrigin, boolean syncMultiWay, boolean compareActiveCopyOnly, long parentId, long parentInstanceId, long otherParentId, long otherParentInstanceId, int syncObjectFilterId, Set<Long> languaguesSelected, String context)
	{
		this.documentId = documentId;
		this.otherDocumentId = otherDocumentId;
		this.instanceId = instanceId;
		this.syncFromOrigin = syncFromOrigin;
		this.syncMultiWay = syncMultiWay;
		this.compareActiveCopyOnly = compareActiveCopyOnly;
		
		this.parentId = parentId;
		this.parentInstanceId = parentInstanceId;
		this.otherParentId = otherParentId;
		this.otherParentInstanceId = otherParentInstanceId;

		this.languagesForSync = languaguesSelected;

		if(instanceId == -1) { 
	        this.otherSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
	        if(this.syncFromOrigin == true && this.syncMultiWay == false){
	        	Document doc = Document.findById(this.documentId);
	        	if(doc != null && doc.getExchangeInstanceGuid() != null && doc.getExchangeTouchpointGuid() != null){
	        		
	        		Node otherInstance = Node.findByGuid(doc.getExchangeInstanceGuid());
	        		this.otherSchema = otherInstance.getSchemaName();
	        		Document otherDoc = CloneHelper.queryInSchema(this.otherSchema, ()->Document.findByGuid(doc.getExchangeTouchpointGuid()));
	        		this.otherDocumentId = otherDoc.getId();
	        	}
	        }
		} else {
		    Node otherInstance = Node.findById(instanceId);
		    if(otherInstance != null) {
	            this.otherSchema = otherInstance.getSchemaName();
		    } else {
		    	
		    }
		}
		
		this.buildItemsList(sSearch, orderByMap, pageSize, pageIndex, syncObjectFilterId, context);
	}
	
	/**
	 * Build the items list based on the input parameters
	 */
	@FunctionalInterface
	interface FindSyncObjects<DOC1, DOC2, DOC2SCHEMA, FROMORIGIN, RESULTMAP> {
	    public RESULTMAP apply(DOC1 d1, DOC2 d2, DOC2SCHEMA s, FROMORIGIN b);
	}
	
	private <T extends IdentifiableMessagePointModel> String getDnaAndName(T t) {
	    String dna = t.getDna();
/*
	    String name = t.getName();
	    if(t instanceof ContentObject) {
        } else if(t instanceof VersionedModel) {
	        String instanceName = CloneHelper.queryInSchema(t.getObjectSchemaName(), ()->{
    	        VersionedInstance instance = ((VersionedModel) t).getProduction();
    	        if(instance == null) instance = ((VersionedModel) t).getWorkingCopy();
    	        if(instance == null) { 
    	            ModelVersionMapping versionMapping = ((VersionedModel) t).getLatestArchivedVersionInfo();
    	            if(versionMapping != null) {
    	                instance = versionMapping.getModelInstance();
    	            }
    	        }
    	        return (instance != null) ? instance.getName() : null;
	        });
	        if(instanceName != null) name = instanceName;
	    }

	    return dna + "-" + name;
 */
	    return dna;
	}
	
	private <T extends IdentifiableMessagePointModel> Map<T, Long> findSyncObjectsWithMultiwayFiltered(Document document, Document exchangeDocument, Document otherDocument, Document otherExchangeDocument, FindSyncObjects<Document, Document, String, Boolean, Map<T, Long>> getObjectForSync, int progresStart, int progressEnd) {
	    // Phase 1. Find client changes from CC2018 to CC2019
	    Map<T, Long> cc18cc19Map = getObjectForSync.apply(document, otherDocument, otherDocument.getObjectSchemaName(), syncFromOrigin); // find changes from cc2018 to cc2019

	    // If we need sync multi way
	    if(!cc18cc19Map.isEmpty() && this.syncMultiWay) {
	    	boolean sameExchangeDocument = (exchangeDocument == null || otherExchangeDocument == null);
	    	
            Document cc2018Document = syncFromOrigin ? otherDocument : document;
            
            Document md2018ExchangeDocument = syncFromOrigin ? 
            	(otherExchangeDocument != null ? otherExchangeDocument : exchangeDocument): 
            	(exchangeDocument != null ? exchangeDocument : otherExchangeDocument);
            	
            Document cc2019Document = syncFromOrigin ? document : otherDocument;
            
            Document md2019ExchangeDocument = syncFromOrigin ? 
            	(exchangeDocument != null ? exchangeDocument : otherExchangeDocument) : 
            	(otherExchangeDocument != null ? otherExchangeDocument : exchangeDocument);

       		AsyncLongOperationResultWrapper.setCurrentOperationPercentage(progresStart);
       		
            Map<T, Long> md18cc18Map = CloneHelper.queryInSchema(cc2018Document.getObjectSchemaName(), ()->getObjectForSync.apply(cc2018Document, md2018ExchangeDocument, md2018ExchangeDocument.getObjectSchemaName(), false)); // find changes from md2018 to cc2018
            Map<String, Long> md18cc18ResultMap = md18cc18Map.entrySet().stream().collect(Collectors.toMap(e->getDnaAndName(e.getKey()), e->e.getValue(), (e1, e2)->e1));
            
       		AsyncLongOperationResultWrapper.setCurrentOperationPercentage(progresStart + (progressEnd - progresStart) / 4);
       		
            Map<T, Long> md18md19Map = sameExchangeDocument ? (new HashMap<>()) : CloneHelper.queryInSchema(md2019ExchangeDocument.getObjectSchemaName(), ()->getObjectForSync.apply(md2019ExchangeDocument, md2018ExchangeDocument, md2018ExchangeDocument.getObjectSchemaName(), false)); // find changes from md2018 to md2019
            Map<String, Long> md18md19ResultMap = md18md19Map.entrySet().stream().collect(Collectors.toMap(e->getDnaAndName(e.getKey()), e->e.getValue(), (e1, e2)->e1));

       		AsyncLongOperationResultWrapper.setCurrentOperationPercentage(progresStart + (progressEnd - progresStart) * 2 / 4);
       		
            Map<T, Long> md19cc19Map = CloneHelper.queryInSchema(cc2019Document.getObjectSchemaName(), ()->getObjectForSync.apply(cc2019Document, md2019ExchangeDocument, md2019ExchangeDocument.getObjectSchemaName(), false)); // find changes from md2018 to cc2019, delta-cc2019-md2018
            Map<String, Long> md19cc19ResultMap = md19cc19Map.entrySet().stream().collect(Collectors.toMap(e->getDnaAndName(e.getKey()), e->e.getValue(), (e1, e2)->e1));
            
       		AsyncLongOperationResultWrapper.setCurrentOperationPercentage(progresStart + (progressEnd - progresStart) * 3 / 4);
       		
            Map<T, Long> md18cc19Map = sameExchangeDocument ? md19cc19Map : CloneHelper.queryInSchema(cc2019Document.getObjectSchemaName(), ()->getObjectForSync.apply(cc2019Document, md2018ExchangeDocument, md2018ExchangeDocument.getObjectSchemaName(), false)); // find changes from md2018 to cc2019, delta-cc2019-md2018
            Map<String, Long> md18cc19ResultMap = sameExchangeDocument ?  md19cc19ResultMap : md18cc19Map.entrySet().stream().collect(Collectors.toMap(e->getDnaAndName(e.getKey()), e->e.getValue(), (e1, e2)->e1));

            List<T> notSyncList = new ArrayList<>();
            Map.Entry<T, Long> entry;
            Iterator<Map.Entry<T, Long>> iterator = cc18cc19Map.entrySet().iterator();
            while (iterator.hasNext())
            {
            	entry = iterator.next();
                Long status = entry.getValue();
                String dnaName = getDnaAndName(entry.getKey());
                
                if (status == null)
                {
                	if (entry.getKey() != null)
                		log.info("Status NULL for key = " + entry.getKey().getId());
                	else
                		log.info("Status NULL for key NULL ");
                	status = 0L;
                }
                
                Long cc18md18Status = md18cc18ResultMap.get(dnaName);
                Long md19md18Status = md18md19ResultMap.get(dnaName);
                Long cc19md19Status = md19cc19ResultMap.get(dnaName);
                Long cc19md18Status = md18cc19ResultMap.get(dnaName);
                
                // Phase 2, Find client changes from MD2018 to CC2018. If not changed from MD2018 to CC2018, remove from sync list.
                if(cc18md18Status == null) {    // Not changed in CC18, so the change may caused in MD19 or CC19. No sync
                    notSyncList.add(entry.getKey()); // Then remove from sync list
                }
                // Phase 3. Find differences between CC2019 from MD2018.
                else if(cc19md18Status != null) { // Changed in CC18, and changed in CC19, CC19 is different from CC18 and different from MD18.
                                                // Mark collision
                    // Phase 4. distinguishing collisions
                    if(cc19md19Status == null) { // No 19 cust change, caused by model change only
                        status |= AsyncProjectSyncObjectListVO.CONFLICT_MODEL_CHANGE;
                    } else if(md19md18Status == null) { // No 19 model change, caused by cust change only
                        status |= AsyncProjectSyncObjectListVO.CONFLICT_CUST_CHANGE;
                    } else { // Changed in CC18, and MD19, and CC19
                        status |= (AsyncProjectSyncObjectListVO.CONFLICT_MODEL_CHANGE + AsyncProjectSyncObjectListVO.CONFLICT_CUST_CHANGE);
                    }
                    entry.setValue(status);
                } 
            }
            
            for(T t : notSyncList) {
                cc18cc19Map.remove(t);
            }
	    }
	    return cc18cc19Map;
	}
	
	private void buildItemsList(String sSearch, Map<String, String> orderByMap, int pageSize, int pageIndex, int syncObjectFilterId, String context)
	{
		
		if ( context.equals("initial") ) {
			super.setiTotalRecords(0); 
			super.setiTotalDisplayRecords(0);
			return;
		}
		
		AsyncLongOperationResultWrapper.setCurrentOperationPercentage(0);
		
		int 	syncContentObjectFilterId = 0; // FILTER ALL
	    String              exchangeSchema = null;
	    String              otherExchangeSchema = null;
		Document document		= Document.findById(documentId);
		Document otherDocument  = otherSchema == null ? Document.findById(otherDocumentId) : CloneHelper.queryInSchema(otherSchema, ()->Document.findById(otherDocumentId));
        Document exchangeDocument = null;
        Document otherExchangeDocument = null;
        long startMillis = System.currentTimeMillis();
		log.info("BuilditemsList starts");
		if(this.syncMultiWay) {
			if(parentId > 0 && parentInstanceId > 0) {
				exchangeSchema = Node.findById(parentInstanceId).getSchemaName();
				exchangeDocument = CloneHelper.queryInSchema(exchangeSchema, ()->Document.findById(parentId));
			}
			if(otherParentId > 0 && otherParentInstanceId > 0) {
				otherExchangeSchema = Node.findById(otherParentInstanceId).getSchemaName();
				otherExchangeDocument = CloneHelper.queryInSchema(otherExchangeSchema, ()->Document.findById(otherParentId));
			}
		}
		
		AsyncLongOperationResultWrapper.setCurrentOperationPercentage(1);
		
        if(orderByMap.containsKey("synccheckboxrequest")){
        	syncCheckBoxRequest = 1;
        	if (orderByMap.get("synccheckboxrequest").equals("asc"))
        		syncCheckBoxRequest = -1;
        }
        
        if (syncObjectFilterId < 0 || syncObjectFilterId == AssignmentFilterType.ID_MESSAGE)
        {
            Map<ContentObject, Long> globalObjectsResult = new LinkedHashMap<>();

            if(syncContentObjectFilterId == 0 || syncContentObjectFilterId == 3) {
                if (!document.getObjectSchemaName().equals(otherDocument.getObjectSchemaName())) {
                    globalObjectsResult =
                            findSyncObjectsWithMultiwayFiltered(document, exchangeDocument, otherDocument, otherExchangeDocument,
                                    (d, o, os, s)->SyncTouchpointUtil.getGlobalContentObjectsForSync(d, o, os, s, false, compareActiveCopyOnly, languagesForSync), 25, 50);
                }
            }


            if(syncContentObjectFilterId == 0 || syncContentObjectFilterId == 3) {
                if(! document.getObjectSchemaName().equals(otherDocument.getObjectSchemaName())) {
                    long startEC = System.currentTimeMillis();
                    log.info("BuilditemsList find smart texts starts");

                    Map<ContentObject, Long> smartTextsResult = globalObjectsResult
                            .entrySet()
                            .stream()
                            .sequential()
                            .filter(entry->entry.getKey().getObjectType() == ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT)
                            .collect(Collectors.toMap(entry->entry.getKey(), entry->entry.getValue()));

                    if(!smartTextsResult.isEmpty()) {
                        this.objectMap.putAll(smartTextsResult);
                        totalRecords += smartTextsResult.size();
                    }

                    long endEC = System.currentTimeMillis();
                    log.info("BuilditemsList find smart texts ends total " + smartTextsResult.size() + " TIME TAKEN <" + (endEC - startEC) + ">");
                }
            }

    		AsyncLongOperationResultWrapper.setCurrentOperationPercentage(25);
    		
            if(syncContentObjectFilterId == 0 || syncContentObjectFilterId == 5) {
                if(! document.getObjectSchemaName().equals(otherDocument.getObjectSchemaName())) {
                    long startCL = System.currentTimeMillis();
                    log.info("BuilditemsList find ContentLibrary starts");

                    Map<ContentObject, Long> imagesResult = globalObjectsResult
                            .entrySet()
                            .stream()
                            .sequential()
                            .filter(entry->entry.getKey().getObjectType() == ContentObject.OBJECT_TYPE_GLOBAL_IMAGE)
                            .collect(Collectors.toMap(entry->entry.getKey(), entry->entry.getValue()));

                    if(!imagesResult.isEmpty()) {
                        this.objectMap.putAll(imagesResult);
                        totalRecords += imagesResult.size();
                    }

                    long endCL = System.currentTimeMillis();
                    log.info("BuilditemsList find ContentLibrary ends total " + imagesResult.size() + " TIME TAKEN <" + (endCL - startCL) + ">");
                }
            }
            
    		AsyncLongOperationResultWrapper.setCurrentOperationPercentage(50);
    		
	    	// Add Messages
	    	//
            long startMsg = System.currentTimeMillis();
            log.info("BuilditemsList find Message starts");
//			Map<Message, Long> mesageResult = SyncTouchpointUtil.getMessagesForSync(document, otherDocument, otherSchema, syncFromOrigin, false, syncContentObjectFilterId);
            Map<ContentObject, Long> mesageResult = findSyncObjectsWithMultiwayFiltered(document, exchangeDocument, otherDocument, otherExchangeDocument,
                    (d, o, os, s)->SyncTouchpointUtil.getMessagesForSync(d, o, os, s, false, false, compareActiveCopyOnly, syncContentObjectFilterId, languagesForSync), 50, 70);
			
			if (!mesageResult.isEmpty())
			{
				this.objectMap.putAll(mesageResult);
				totalRecords += mesageResult.size();
			}
			
            long endMsg = System.currentTimeMillis();
            log.info("BuilditemsList find Message ends total " + mesageResult.size() + " TIME TAKEN <" + (endMsg - startMsg) + ">");
            {
	            long startTargetGroup = System.currentTimeMillis();
	            log.info("BuilditemsList find Target Group starts");
	            
	            Map<TargetGroup, Long> targetGroupsResult = findSyncObjectsWithMultiwayFiltered(document, exchangeDocument, otherDocument, otherExchangeDocument, 
	                    (d, o, os, s)->SyncTouchpointUtil.getTargetGroupsForSync(d, o, os, s, false), 70, 80);
	            
	            if(!targetGroupsResult.isEmpty()) {
	            	this.objectMap.putAll(targetGroupsResult);
					totalRecords += targetGroupsResult.size();
	            }
	            
	            long endTargetGroup = System.currentTimeMillis();
	            log.info("BuilditemsList find Target Group ends total " + targetGroupsResult.size() + " TIME TAKEN <" + (endTargetGroup - startTargetGroup) + ">");
            }
            
            {
	            long startTargetRule = System.currentTimeMillis();
	            log.info("BuilditemsList find Target Rule starts");
	            
	            Map<ConditionElement, Long> targetRulesResult = findSyncObjectsWithMultiwayFiltered(document, exchangeDocument, otherDocument, otherExchangeDocument, 
	                    (d, o, os, s)->SyncTouchpointUtil.getTargetRulesForSync(d, o, os, s, false), 80, 83);

	            if(!targetRulesResult.isEmpty()) {
	            	this.objectMap.putAll(targetRulesResult);
					totalRecords += targetRulesResult.size();
	            }
	            
	            long endTargetRule = System.currentTimeMillis();
	            log.info("BuilditemsList find Target Rule ends total " + targetRulesResult.size() + " TIME TAKEN <" + (endTargetRule - startTargetRule) + ">");
            }

            {
	            long startParameterGroup = System.currentTimeMillis();
	            log.info("BuilditemsList find Parameter Group starts");
	            
	            Map<ParameterGroup, Long> targetParameterGroupResult = findSyncObjectsWithMultiwayFiltered(document, exchangeDocument, otherDocument, otherExchangeDocument, 
	                    (d, o, os, s)->SyncTouchpointUtil.getParameterGroupsForSync(d, o, os, s, false), 83, 88);
	            
	            if(!targetParameterGroupResult.isEmpty()) {
	            	this.objectMap.putAll(targetParameterGroupResult);
					totalRecords += targetParameterGroupResult.size();
	            }
	            
	            long endParameterGroup = System.currentTimeMillis();
	            log.info("BuilditemsList find Parameter Group ends total " + targetParameterGroupResult.size() + " TIME TAKEN <" + (endParameterGroup - startParameterGroup) + ">");
            }

            {
                long startDocumentSettings = System.currentTimeMillis();
                log.info("BuilditemsList find Document Settings starts");

                Map<DocumentSettingsModel, Long> documentSettingsResult = findSyncObjectsWithMultiwayFiltered(document, exchangeDocument, otherDocument, otherExchangeDocument,
                        (d, o, os, s)->SyncTouchpointUtil.getDocumentSettingsForSync(d, o, os, s, false), 88, 89);

                if(!documentSettingsResult.isEmpty()) {
                    this.objectMap.putAll(documentSettingsResult);
                    totalRecords += documentSettingsResult.size();
                }

                long endDocumentSettings = System.currentTimeMillis();
                log.info("BuilditemsList find Document Settings ends total " + documentSettingsResult.size() + " TIME TAKEN <" + (endDocumentSettings - startDocumentSettings) + ">");
            }

            {
                long startLookupTables = System.currentTimeMillis();
                log.info("BuilditemsList find lookup table starts");

                Map<LookupTable, Long> LookupTableResult = findSyncObjectsWithMultiwayFiltered(document, exchangeDocument, otherDocument, otherExchangeDocument,
                        (d, o, os, s)->SyncTouchpointUtil.getLookupTablesForSync(d, o, os, s, false, compareActiveCopyOnly), 89, 90);

                if(!LookupTableResult.isEmpty()) {
                    this.objectMap.putAll(LookupTableResult);
                    totalRecords += LookupTableResult.size();
                }

                long endLookupTables = System.currentTimeMillis();
                log.info("BuilditemsList find lookup table ends total " + LookupTableResult.size() + " TIME TAKEN <" + (endLookupTables - startLookupTables) + ">");
            }

            {
                long startVariables = System.currentTimeMillis();
                log.info("BuilditemsList find variables starts");

                Map<DataElementVariable, Long> variablesResult = findSyncObjectsWithMultiwayFiltered(document, exchangeDocument, otherDocument, otherExchangeDocument,
                        (d, o, os, s)->SyncTouchpointUtil.getVariablesForSync(d, o, os, s, false), 89, 90);

                if(!variablesResult.isEmpty()) {
                    this.objectMap.putAll(variablesResult);
                    totalRecords += variablesResult.size();
                }

                long endVariables = System.currentTimeMillis();
                log.info("BuilditemsList find variables ends total " + variablesResult.size() + " TIME TAKEN <" + (endVariables - startVariables) + ">");
            }

            {
                long startDataSourceAssociations = System.currentTimeMillis();
                log.info("BuilditemsList find data source associations starts");

                Map<DataSourceAssociation, Long> dataSourceAssociationResult = findSyncObjectsWithMultiwayFiltered(document, exchangeDocument, otherDocument, otherExchangeDocument,
                        (d, o, os, s)->SyncTouchpointUtil.getDataSourceAssociationsForSync(d, o, os, s, false), 89, 90);

                if(!dataSourceAssociationResult.isEmpty()) {
                    this.objectMap.putAll(dataSourceAssociationResult);
                    totalRecords += dataSourceAssociationResult.size();
                }

                long endDataSourceAssociations = System.currentTimeMillis();
                log.info("BuilditemsList find data source associations ends total " + dataSourceAssociationResult.size() + " TIME TAKEN <" + (endDataSourceAssociations - startDataSourceAssociations) + ">");
            }

            {
                long startDataSources = System.currentTimeMillis();
                log.info("BuilditemsList find data sources starts");

                Map<DataSource, Long> dataSourceResult = findSyncObjectsWithMultiwayFiltered(document, exchangeDocument, otherDocument, otherExchangeDocument,
                        (d, o, os, s)->SyncTouchpointUtil.getDataSourcesForSync(d, o, os, s, false), 89, 90);

                if(!dataSourceResult.isEmpty()) {
                    this.objectMap.putAll(dataSourceResult);
                    totalRecords += dataSourceResult.size();
                }

                long endDataSources = System.currentTimeMillis();
                log.info("BuilditemsList find data sources ends total " + dataSourceResult.size() + " TIME TAKEN <" + (endDataSources - startDataSources) + ">");
            }
        }
        else
        {
	    	// Add Sections
	    	//
			Map<? extends Object, Long> result = SyncTouchpointUtil.getTouchpointSectionsForSync(document, otherDocument, otherSchema, syncFromOrigin, true, true);
			
			if (!result.isEmpty())
			{
				this.objectMap.putAll(result);
				totalRecords += result.size();
			}
	
	        // Add Zones
	        //
			result = SyncTouchpointUtil.getTouchpointZonesForSync(document, otherDocument, otherSchema, syncFromOrigin, true);
			
			if (!result.isEmpty())
			{
				this.objectMap.putAll(result);
				totalRecords += result.size();
			}
/*
	        // Add Variants
	        //
			result = SyncTouchpointUtil.getTouchpointVariantsForSync(document, otherDocument, otherSchema, syncFromOrigin, true);
			
			if (result.size() > 0)
			{
				this.objectMap.putAll(result);
				totalRecords += result.size();
			}
*/
        }



        totalDisplayRecords = totalRecords;
        long endMillis = System.currentTimeMillis();
        log.info("BuilditemsList ends total records: " + totalDisplayRecords + " TIME TAKEN <" + (endMillis - startMillis) + ">");

		super.setiTotalRecords(totalRecords); 
		super.setiTotalDisplayRecords(totalDisplayRecords);

		AsyncLongOperationResultWrapper.setCurrentOperationPercentage(95);
	}

	private void addAllVariableParameterReference(String schema, Map<Long, List<ReferencableObject>> allParameterGroupReferencesMap, Map<Long, List<ReferencableObject>> allVariableReferencesMap) {
	    CloneHelper.execInSchema(schema, ()-> {
            for (Long parameterGroupId : allParameterGroupReferencesMap.keySet()) {
                ParameterGroup parameterGroup = ParameterGroup.findById(parameterGroupId);
                if (parameterGroup.getParameterGroupItems().size() == 1) {
                    Parameter parameter = parameterGroup.getFirstParameterGroupItem().getParameter();
                    if (parameter != null) {
                        DataElementVariable dataElementVariable = parameter.getDataElementVariable();
                        if (dataElementVariable != null) {
                            Long dataElementVariableID = dataElementVariable.getId();
                            List<ReferencableObject> objectsReferencesParameterGroup = allParameterGroupReferencesMap.get(parameterGroupId);
                            List<ReferencableObject> objectsReferencesVariable = allVariableReferencesMap.get(dataElementVariableID);
                            if (objectsReferencesVariable == null) {
                                objectsReferencesVariable = objectsReferencesParameterGroup;
                                allVariableReferencesMap.put(dataElementVariableID, objectsReferencesVariable);
                            } else {
                                for (ReferencableObject referencableObject : objectsReferencesParameterGroup) {
                                    String targetClassName = referencableObject.getTargetClassName();
                                    long referencableObjectId = referencableObject.getObjectId();
                                    if (!objectsReferencesVariable.stream().anyMatch(ro -> ro.getTargetClassName().equals(targetClassName) && ro.getObjectId() == referencableObjectId)) {
                                        objectsReferencesVariable.add(referencableObject);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    private void addAllLookupTableDataSourceReference(String schema, Map<Long, List<ReferencableObject>> allDataSourceReferencesMap, Map<Long, List<ReferencableObject>> allLookupTableReferencesMap) {
        CloneHelper.execInSchema(schema, ()-> {
            for (Long dataSourceId : allDataSourceReferencesMap.keySet()) {
                DataSource dataSource = DataSource.findById(dataSourceId);
                if (dataSource.isLookupTableDataSource()) {
                    Set<LookupTable> lookupTables = LookupTableInstance.findAll().stream().filter(lti->(lti.isActive() || lti.isWorkingCopy()) && lti.getDataSource().getId() == dataSourceId).map(lti->lti.getModel()).collect(Collectors.toSet());
                    for(LookupTable lookupTable : lookupTables) {
                        Long lookupTableId = lookupTable.getId();
                        List<ReferencableObject> objectsReferencesDataSource = allDataSourceReferencesMap.get(dataSourceId);
                        List<ReferencableObject> objectsReferencesLookupTable = allLookupTableReferencesMap.get(lookupTableId);
                        if (objectsReferencesLookupTable == null) {
                            objectsReferencesLookupTable = objectsReferencesDataSource;
                            allLookupTableReferencesMap.put(lookupTableId, objectsReferencesLookupTable);
                        } else {
                            for (ReferencableObject referencableObject : objectsReferencesDataSource) {
                                String targetClassName = referencableObject.getTargetClassName();
                                long referencableObjectId = referencableObject.getObjectId();
                                if (!objectsReferencesLookupTable.stream().anyMatch(ro -> ro.getTargetClassName().equals(targetClassName) && ro.getObjectId() == referencableObjectId)) {
                                    objectsReferencesLookupTable.add(referencableObject);
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    @Override
	public void init() {
		List<AsyncProjectSyncObjectListVO> aaData = new ArrayList<>();

        log.info("init starts");
        
		Document document       = Document.findById(documentId);
 //       Document otherDocument  = ((otherDocumentId == -1) ? (Document) document.getOriginObject() : Document.findById(otherDocumentId));
		Document otherDocument  = otherSchema == null ? Document.findById(otherDocumentId) : CloneHelper.queryInSchema(otherSchema, ()->Document.findById(otherDocumentId));
        User user = UserUtil.getPrincipalUser();
        
        Node currentNode = Node.getCurrentNode();
        Node otherNode = Node.findBySchema(otherSchema);
        
        Long sourceInstanceId = currentNode.getId();
        if(syncFromOrigin) {
        	sourceInstanceId = otherNode.getId();
        }
        
        Long targetInstanceId = otherNode.getId();
        if(syncFromOrigin) {
        	targetInstanceId = currentNode.getId();
        }
        
        String currentSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
        
        log.info("init query ref map");
        
        long count = 0L;
        long startMillis = System.currentTimeMillis();
        
        String sourceSchema = syncFromOrigin ? otherNode.getSchemaName() : currentNode.getSchemaName();
        String targetSchema = syncFromOrigin ? currentNode.getSchemaName() : otherNode.getSchemaName();

        boolean isSameSchema = sourceSchema.equalsIgnoreCase(targetSchema);

        Document sourceDocument = syncFromOrigin ? otherDocument : document;
        Document targetDocument = syncFromOrigin ? document : otherDocument;
        
        Map<Long, List<String>> allSourceObjectsWithDependenciesNeedExisting = new HashMap<>(); // ObjectId to List of objects which were referenced in the Object which should be created if not exists in target instance.
        Map<Long, List<String>> allSourceObjectsWithDependenciesSyncWhenChange = new HashMap<>(); // ObjectId to List of objects which were referenced in the Object which must be sync'ed when changed.

        Map<Long, List<String>> targetObjectIDsWithReferencingMap = new HashMap<>(); // ObjectId to List of objects which references the object.

        Map<Long, List<ReferencableObject>> allTargetGroupReferencesMapInTargetSchema = new HashMap<>();
        Map<Long, List<ReferencableObject>> allTargetRuleReferencesMapInTargetSchema = new HashMap<>();
        Map<Long, List<ReferencableObject>> allParameterGroupReferencesMapInTargetSchema = new HashMap<>();
        Map<Long, List<ReferencableObject>> allVariableReferencesMapInTargetSchema = new HashMap<>();
        Map<Long, List<ReferencableObject>> allDataSourceReferencesMapInTargetSchema = new HashMap<>();
        Map<Long, List<ReferencableObject>> allLookupTableReferencesMapInTargetSchema = new HashMap<>();
        Map<Long, List<ReferencableObject>> allDataCollectionReferencesMapInTargetSchema = new HashMap<>();

        if(objectMap != null && !objectMap.isEmpty()) {
            log.info("find source content objects ref map");
            Map<Integer, Map<Long, List<String>>> sourceContentObjectIDsWithDependencies = CloneHelper.queryInSchema(sourceSchema, ()->Content.findContentObjectIDsWithDependencies(false));

            log.info("init query source message ref map");
            if(sourceContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_MESSAGE)) {
                allSourceObjectsWithDependenciesNeedExisting.putAll(sourceContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_MESSAGE));
            }
            if(sourceContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT)) {
                allSourceObjectsWithDependenciesNeedExisting.putAll(sourceContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT));
            }
            if(sourceContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_LOCAL_IMAGE)) {
                allSourceObjectsWithDependenciesNeedExisting.putAll(sourceContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_LOCAL_IMAGE));
            }

        	if(! isSameSchema) {
                log.info("init query source smart texts ref map");
                if(sourceContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT)) {
                    allSourceObjectsWithDependenciesNeedExisting.putAll(sourceContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT));
                }
                if(sourceContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_GLOBAL_IMAGE)) {
                    allSourceObjectsWithDependenciesNeedExisting.putAll(sourceContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_GLOBAL_IMAGE));
                }
            }

        	BiConsumer<String, Map<Long, List<String>>> transformToTargetReferencedBy = (objectType, allTargetObjectsWithDependencies) -> {
	        	for(Map.Entry<Long, List<String>> e : allTargetObjectsWithDependencies.entrySet()) {
	        		Long objectAId = e.getKey(); // Object A is target object
	        		List<String> referencedObjectIDs = e.getValue(); // List of objects which were referenced by Object A
	        		for(String objectBTypeId : referencedObjectIDs) { // Object B, who was referenced by object A
	        		    String [] idstring = objectBTypeId.split("-");
	        			Long objectBId = Long.valueOf(idstring[1]);
	        			List<String> referencingIdsList = targetObjectIDsWithReferencingMap.get(objectBId); // List of objects which references object B
	        			if(referencingIdsList == null) {
	        				referencingIdsList = new ArrayList<>();
	        				targetObjectIDsWithReferencingMap.put(objectBId, referencingIdsList);
	        			}
                        referencingIdsList.add(objectType + "-" + objectAId); // Object A references object B
	        		}
	        	}
        	};

            log.info("find target content objects ref map");
            Map<Integer, Map<Long, List<String>>> targetContentObjectIDsWithDependencies = CloneHelper.queryInSchema(targetSchema, ()->Content.findContentObjectIDsWithDependencies(false));

            log.info("init query target message/local smart text/local image ref map");
            if(targetContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_MESSAGE)) {
                transformToTargetReferencedBy.accept("Message", targetContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_MESSAGE));
            }
            if(targetContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT)) {
                transformToTargetReferencedBy.accept("LocalSmartText", targetContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT));
            }
            if(targetContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_LOCAL_IMAGE)) {
                transformToTargetReferencedBy.accept("LocalImage", targetContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_LOCAL_IMAGE));
            }
        	if(! isSameSchema) {
                log.info("init query target smart texts ref map");
                if(targetContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT)) {
                    transformToTargetReferencedBy.accept("SmartText", targetContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT));
                }
                if(targetContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_GLOBAL_IMAGE)) {
                    transformToTargetReferencedBy.accept("Image", targetContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_GLOBAL_IMAGE));
                }
            }

        	BiConsumer<Map<Long, List<ReferencableObject>>, List<? extends IdentifiableMessagePointModel>> transformReferencableToTargetReferencedBy = (allReferencesMap, trList)->{
        		trList.forEach((trr)->{
        			IdentifiableMessagePointModel obj = (IdentifiableMessagePointModel) trr;
                    List<ReferencableObject> referencesList = allReferencesMap.get(obj.getId());
                    if(referencesList != null) {
                        List<IdentifiableMessagePointModel> referencingObjects = SyncTouchpointUtil.getObjectFromReferencableObjectList(referencesList); // List of objects which reference tr
                        referencingObjects.forEach(ro -> {
                            List<String> sourceObjectDependencies = allSourceObjectsWithDependenciesNeedExisting.get(ro.getId());
                            if (sourceObjectDependencies == null) {
                                sourceObjectDependencies = new ArrayList<>();
                                allSourceObjectsWithDependenciesNeedExisting.put(ro.getId(), sourceObjectDependencies);
                            }
                            String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(obj);
                            if (!sourceObjectDependencies.contains(refObjectTypeAndId)) {
                                sourceObjectDependencies.add(refObjectTypeAndId);
                            }
                        });
                    }
        		});
        	};

        	if(! isSameSchema) {
                log.info("init query target target groups ref map");
                allTargetGroupReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->TargetGroup.getAllReferencesMap(false)); // Target Group ID to the list of objects who reference the target group
                log.info("init query target target rules ref map");
                allTargetRuleReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->ConditionElement.getAllReferencesMap()); // Target Rule ID to the list of objects who reference the target rule
                log.info("init query target parameter groups ref map");
                allParameterGroupReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->ParameterGroup.getAllReferencesMap(false)); // Parameter Group ID to the list of objects who reference the parameter group
                log.info("init query target variables ref map");
                allVariableReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->DataElementVariable.getAllReferencesMap()); // Variable ID to the list of objects who reference the variable

                addAllVariableParameterReference(targetSchema, allParameterGroupReferencesMapInTargetSchema, allVariableReferencesMapInTargetSchema);

                log.info("init query target datasource ref map");
                allDataSourceReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->DataSource.getAllReferencesMap()); // Datasource ID to the list of objects who reference the datasource

                log.info("init query target lookup table ref map");
//                allLookupTableReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->LookupTable.getAllReferencesMap()); // LookupTable ID to the list of objects who reference the lookup table

                addAllLookupTableDataSourceReference(targetSchema, allDataSourceReferencesMapInTargetSchema, allLookupTableReferencesMapInTargetSchema);

//                log.info("init query target data collection ref map");
//                allLookupTableReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->Data.getAllReferencesMap()); // LookupTable ID to the list of objects who reference the lookup table


                CloneHelper.execInSchema(sourceSchema, () -> {
                    {
                        log.info("init query source target groups ref map");
                        Map<Long, List<ReferencableObject>> allTargetGroupReferencesMap = TargetGroup.getAllReferencesMap(false); // Target Group ID to the list of objects who reference the target group
                        transformReferencableToTargetReferencedBy.accept(allTargetGroupReferencesMap, TargetGroup.findAllByDocument(sourceDocument, null, false, 0, true));
                    }
                    {
                        log.info("init query source target rules ref map");
                        Map<Long, List<ReferencableObject>> allTargetRuleReferencesMap = ConditionElement.getAllReferencesMap(); // Target Rule ID to the list of objects who reference the target rule
                        transformReferencableToTargetReferencedBy.accept(allTargetRuleReferencesMap, ConditionElement.findAllByDocument(sourceDocument, null, false, 0));
                    }

                    List<DataElementVariable> allDataElementVariables = DataElementVariable.findAllVisibleForDocument(sourceDocument);

                    {
                        log.info("init query source parameter groups ref map");
                        Map<Long, List<ReferencableObject>> allParameterGroupReferencesMap = ParameterGroup.getAllReferencesMap(false); // Parameter Group ID to the list of objects who reference the parameter group

                        transformReferencableToTargetReferencedBy.accept(allParameterGroupReferencesMap, ParameterGroup.findAllIncludingParameters().stream().filter(pg -> pg.isVisibleToDocument(sourceDocument)).collect(Collectors.toList()));

                        log.info("init query source variables ref map");
                        Map<Long, List<ReferencableObject>> allVariableReferencesMap = DataElementVariable.getAllReferencesMap(); // Data Element Variable ID to the list of objects who reference the target group

                        addAllVariableParameterReference(sourceSchema, allParameterGroupReferencesMap, allVariableReferencesMap);
                        transformReferencableToTargetReferencedBy.accept(allVariableReferencesMap, allDataElementVariables);
                    }

                    {
                        log.info("init query source datasource ref map");
                        Map<Long, List<ReferencableObject>> allDataSourceReferencesMap = DataSource.getAllReferencesMap(); // Data Element Variable ID to the list of objects who reference the target group

                        List<DataSource> allDataSourcesVisibleForDocument = DataSource.findAll()
                                .stream()
                                .filter(ds->{
                                    List<Document> dsDocuments = ds.getDocuments();
                                    return dsDocuments != null && dsDocuments.stream().anyMatch(d->d.getId() == sourceDocument.getId());
                                })
                                .collect(Collectors.toList());

                        Set<Long> allVisibleDataSourceIds = allDataSourcesVisibleForDocument.stream().map(DataSource::getId).collect(Collectors.toSet());

                        for(DataElementVariable dev : allDataElementVariables) {
                            List<AbstractDataElement> devDataElements = new ArrayList<>();
                            AbstractDataElement defaultDataElement = dev.getDefaultDataElement();
                            if(defaultDataElement != null) {
                                devDataElements.add(defaultDataElement);
                            }

                            for(Map.Entry<Long, VariableDataElementMap> dsVdemEntry : dev.getDataElementMap().entrySet()) {
                                Long dataSourceId = dsVdemEntry.getKey();
                                VariableDataElementMap vdem = dsVdemEntry.getValue();
                                AbstractDataElement dataElement = vdem.getDataElement();
                                if(dataElement != null) {
                                    devDataElements.add(dataElement);
                                }
                            }
                            for(AbstractDataElement dataElement : devDataElements) {
                                DataSource dataSource = dataElement.getDataSource();
                                Long dataSourceId = dataSource.getId();
                                String dataElementDna = dataElement.getDna();
                                boolean isNewDataElement = false;
                                if(dataSource.isXML()) {
                                    isNewDataElement = CloneHelper.queryInSchema(targetSchema, ()->XmlDataElement.findByDna(dataElementDna) == null);
                                } else {
                                    isNewDataElement = CloneHelper.queryInSchema(targetSchema, ()->DataElement.findByDna(dataElementDna) == null);
                                }
                                if(false && isNewDataElement) {
                                    if(allVisibleDataSourceIds.contains(dataSourceId)) {
                                        List<ReferencableObject> referencableObjects = allDataSourceReferencesMap.get(dataSourceId);
                                        if(referencableObjects == null) {
                                            referencableObjects = new ArrayList<>();
                                            allDataSourceReferencesMap.put(dataSourceId, referencableObjects);
                                        }
                                        if(! referencableObjects.stream().anyMatch(ro->ro.getObjectId() == dev.getId())) {
                                            ReferencableObject referencableObject = new ReferencableObject(dev);
                                            referencableObjects.add(referencableObject);
                                        }
                                        List<String> alwaySyncList = allSourceObjectsWithDependenciesSyncWhenChange.get(dev.getId());
                                        if(alwaySyncList == null) {
                                            alwaySyncList = new ArrayList<>();
                                            allSourceObjectsWithDependenciesSyncWhenChange.put(dev.getId(), alwaySyncList);
                                        }
                                        String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(dataSource);
                                        if(! alwaySyncList.contains(refObjectTypeAndId)) {
                                            alwaySyncList.add(refObjectTypeAndId);
                                        }
                                    }
                                }
                            }
                        }

                        transformReferencableToTargetReferencedBy.accept(allDataSourceReferencesMap, allDataSourcesVisibleForDocument);
                    }

                    {
                        log.info("init query source lookuptable ref map");
                        Map<Long, List<ReferencableObject>> allLookupTableReferencesMap = LookupTable.getAllReferencesMap(); // Data Element Variable ID to the list of objects who reference the target group
                        transformReferencableToTargetReferencedBy.accept(allLookupTableReferencesMap,
                                LookupTableInstance.findAllVisibleIdsOfMostRecentCopies(List.of(sourceDocument.getId()), null)
                                        .stream().map(instid->(LookupTable) LookupTableInstance.findById(instid).getModel()).collect(Collectors.toList()));
                    }
                });
            }
        }

        log.info("init add aaData total = " + this.objectMap.size());
        
		for (Object object : this.objectMap.keySet()) {
			long objectStatus = this.objectMap.get(object);

			AsyncProjectSyncObjectListVO vo = new AsyncProjectSyncObjectListVO();
			
			vo.setObject(object);
			vo.setObjectStatus(objectStatus);
			vo.setOriginNodeId(instanceId);
			
			// remove information about origin
			//
			boolean objectConflictExchange = (objectStatus & (AsyncProjectSyncObjectListVO.CONFLICT_MODEL_CHANGE | AsyncProjectSyncObjectListVO.CONFLICT_CUST_CHANGE)) != 0;
            boolean objectFromOther = (objectStatus & 0x10000) != 0;

            objectStatus = objectStatus & 0xFFFF;
			
			// For drill-down
			vo.setDT_RowId(((IdentifiableMessagePointModel) object).getId());
			vo.setDisplayMode(getDisplayMode());
			
			// Name
			vo.setName(((IdentifiableMessagePointModel) object).getName());			

			int projectStatus = (int) ((objectStatus / 0x100) & 0xF);
			int originStatus = (int) (objectStatus & 0xF);
			
			vo.setProjectStatus(new ContentSelectionStatusType(projectStatus));
			vo.setOriginStatus(new ContentSelectionStatusType(originStatus));
			vo.setSyncFromOrigin(syncFromOrigin);
			
			if (syncCheckBoxRequest < 0)
				vo.setSyncRequest(false);
			else
				vo.setSyncRequest(true);

            vo.setConflictsExchange(objectConflictExchange);
			if(objectConflictExchange) {
                if (syncCheckBoxRequest == 0)
                    vo.setSyncRequest(false);
			}
			
			if (syncFromOrigin)
			{
				if (/*originStatus != ContentSelectionStatusType.ID_UNCHANGED && */originStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE && (projectStatus == ContentSelectionStatusType.ID_CHANGED || projectStatus == ContentSelectionStatusType.ID_NEW || projectStatus == ContentSelectionStatusType.ID_FIRST_TIME_SYNC))
				{
					vo.setConflictsWorkingCopy(true);
					if (syncCheckBoxRequest == 0)
						vo.setSyncRequest(false);
				}
			}
			else
			{
				if (/*projectStatus != ContentSelectionStatusType.ID_UNCHANGED && */projectStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE && (originStatus == ContentSelectionStatusType.ID_CHANGED || originStatus == ContentSelectionStatusType.ID_NEW || originStatus == ContentSelectionStatusType.ID_FIRST_TIME_SYNC))
				{
					vo.setConflictsWorkingCopy(true);
					if (syncCheckBoxRequest == 0)
						vo.setSyncRequest(false);
				}
			}

			if (object instanceof DataElementVariable)
			{
                vo.setObjectType(new SyncObjectType(SyncObjectType.ID_VARIABLE));

                DataElementVariable requestDataElementVariable = (DataElementVariable) object;
                String dna = CloneHelper.queryInSchema(objectFromOther ? otherSchema : currentSchema, () -> requestDataElementVariable.getDna());

                DataElementVariable projectDataElementVariable = objectFromOther ? DataElementVariable.findByDna(dna) : requestDataElementVariable;
                DataElementVariable otherDataElementVariable = objectFromOther ? requestDataElementVariable : CloneHelper.queryInSchema(otherSchema, () -> DataElementVariable.findByDna(dna));

                if (projectDataElementVariable != null) vo.setProjectObjectId(projectDataElementVariable.getId());
                if (otherDataElementVariable != null) vo.setOriginObjectId(otherDataElementVariable.getId());

                DataElementVariable targetDataElementVariable = syncFromOrigin ? projectDataElementVariable : otherDataElementVariable;
                DataElementVariable sourceDataElementVariable = syncFromOrigin ? otherDataElementVariable : projectDataElementVariable;

                vo.setProjectSchema(currentSchema);
                vo.setOriginSchema(otherSchema);

                vo.setSourceInstanceId(sourceInstanceId);
                vo.setTargetInstanceId(targetInstanceId);

                if (sourceDataElementVariable != null) {
                    vo.setSourceObjectId(sourceDataElementVariable.getId());
                    vo.setSourceHasDependencies(allSourceObjectsWithDependenciesNeedExisting.containsKey(sourceDataElementVariable.getId()));
                    vo.setSourceDependenciesNeedCreatingIDs(allSourceObjectsWithDependenciesNeedExisting.get(sourceDataElementVariable.getId()));
                    vo.setSourceAlwaysSyncDependenciesIDs(allSourceObjectsWithDependenciesSyncWhenChange.get(sourceDataElementVariable.getId()));
                }

                if (sourceDataElementVariable == null && targetDataElementVariable != null) {
                    vo.setTargetObjectId(targetDataElementVariable.getId());

                    List<ReferencableObject> referencableObjects = allVariableReferencesMapInTargetSchema.get(targetDataElementVariable.getId()); // CloneHelper.queryInSchema(targetSchema, ()->targetDataElementVariable.getDirectReferences());
                    if (referencableObjects != null && !referencableObjects.isEmpty()) {
                        vo.setTargetIsReferenced(true);
                        List<String> ids = CloneHelper.queryInSchema(targetSchema, () -> SyncTouchpointUtil.getObjectIdsFromReferencableObjectsList(referencableObjects));
                        vo.setTargetDependenciesIDs(ids);
                        vo.setSyncRequest(false);
                    }
                }

                vo.setProjectStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
                vo.setOriginStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
            }
			else if (object instanceof DataSource)
            {
                vo.setObjectType(new SyncObjectType(SyncObjectType.ID_DATASOURCE));

                DataSource requestDataSource = (DataSource) object;
                String dna = CloneHelper.queryInSchema(objectFromOther ? otherSchema : currentSchema, () -> requestDataSource.getDna());

                DataSource projectDataSource = objectFromOther ? DataSource.findByDna(dna) : requestDataSource;
                DataSource otherDataSource = objectFromOther ? requestDataSource : CloneHelper.queryInSchema(otherSchema, () -> DataSource.findByDna(dna));

                if (projectDataSource != null) vo.setProjectObjectId(projectDataSource.getId());
                if (otherDataSource != null) vo.setOriginObjectId(otherDataSource.getId());

                DataSource targetDataSource = syncFromOrigin ? projectDataSource : otherDataSource;
                DataSource sourceDataSource = syncFromOrigin ? otherDataSource : projectDataSource;

                vo.setProjectSchema(currentSchema);
                vo.setOriginSchema(otherSchema);

                vo.setSourceInstanceId(sourceInstanceId);
                vo.setTargetInstanceId(targetInstanceId);

                if (sourceDataSource != null) {
                    vo.setSourceObjectId(sourceDataSource.getId());
                    vo.setSourceHasDependencies(allSourceObjectsWithDependenciesNeedExisting.containsKey(sourceDataSource.getId()));
                    vo.setSourceDependenciesNeedCreatingIDs(allSourceObjectsWithDependenciesNeedExisting.get(sourceDataSource.getId()));
                }

                if (sourceDataSource == null && targetDataSource != null) {
                    vo.setTargetObjectId(targetDataSource.getId());
                    List<ReferencableObject> referencableObjects = allDataSourceReferencesMapInTargetSchema.get(targetDataSource.getId()); // CloneHelper.queryInSchema(targetSchema, ()->targetDataElementVariable.getDirectReferences());
                    if (referencableObjects != null && !referencableObjects.isEmpty()) {
                        vo.setTargetIsReferenced(true);
                        List<String> ids = CloneHelper.queryInSchema(targetSchema, () -> SyncTouchpointUtil.getObjectIdsFromReferencableObjectsList(referencableObjects));
                        vo.setTargetDependenciesIDs(ids);
                        vo.setSyncRequest(false);
                    }
                }

                vo.setProjectStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
                vo.setOriginStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
            }
			else if (object instanceof DataSourceAssociation)
			{
                vo.setObjectType(new SyncObjectType(SyncObjectType.ID_DATACOLLECTION));

                DataSourceAssociation requestDataSourceAssociation = (DataSourceAssociation) object;
                String dna = CloneHelper.queryInSchema(objectFromOther ? otherSchema : currentSchema, () -> requestDataSourceAssociation.getDna());

                DataSourceAssociation projectDataSourceAssociation = objectFromOther ? DataSourceAssociation.findByDna(dna) : requestDataSourceAssociation;
                DataSourceAssociation otherDataSourceAssociation = objectFromOther ? requestDataSourceAssociation : CloneHelper.queryInSchema(otherSchema, () -> DataSourceAssociation.findByDna(dna));

                if (projectDataSourceAssociation != null) vo.setProjectObjectId(projectDataSourceAssociation.getId());
                if (otherDataSourceAssociation != null) vo.setOriginObjectId(otherDataSourceAssociation.getId());

                DataSourceAssociation targetDataSourceAssociation = syncFromOrigin ? projectDataSourceAssociation : otherDataSourceAssociation;
                DataSourceAssociation sourceDataSourceAssociation = syncFromOrigin ? otherDataSourceAssociation : projectDataSourceAssociation;

                vo.setProjectSchema(currentSchema);
                vo.setOriginSchema(otherSchema);

                vo.setSourceInstanceId(sourceInstanceId);
                vo.setTargetInstanceId(targetInstanceId);

                if (sourceDataSourceAssociation != null) {
                    vo.setSourceObjectId(sourceDataSourceAssociation.getId());

                    vo.setSourceHasDependencies(allSourceObjectsWithDependenciesNeedExisting.containsKey(sourceDataSourceAssociation.getId()));
                    vo.setSourceDependenciesNeedCreatingIDs(allSourceObjectsWithDependenciesNeedExisting.get(sourceDataSourceAssociation.getId()));
                }

                if (sourceDataSourceAssociation == null && targetDataSourceAssociation != null) {
                    vo.setTargetObjectId(targetDataSourceAssociation.getId());
                    List<ReferencableObject> referencableObjects = allDataCollectionReferencesMapInTargetSchema.get(targetDataSourceAssociation.getId()); // CloneHelper.queryInSchema(targetSchema, ()->targetDataElementVariable.getDirectReferences());
                    if (referencableObjects != null && !referencableObjects.isEmpty()) {
                        vo.setTargetIsReferenced(true);
                        List<String> ids = CloneHelper.queryInSchema(targetSchema, () -> SyncTouchpointUtil.getObjectIdsFromReferencableObjectsList(referencableObjects));
                        vo.setTargetDependenciesIDs(ids);
                        vo.setSyncRequest(false);
                    }
                }

                vo.setProjectStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
                vo.setOriginStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
            }
			else if (object instanceof DocumentSettingsModel)
			{
                DocumentSettingsModel documentSettingsModel = (DocumentSettingsModel) object;
                vo.setObjectType(new SyncObjectType(SyncObjectType.ID_DOCUMENT_SETTING));

                if(document != null) vo.setProjectObjectId(documentSettingsModel.getId());
                if(otherDocument != null) vo.setOriginObjectId(documentSettingsModel.getId());

                vo.setSourceObjectId(documentSettingsModel.getId());
                vo.setTargetObjectId(documentSettingsModel.getId());

                vo.setProjectSchema(currentSchema);
                vo.setOriginSchema(otherSchema);

                vo.setSourceInstanceId(sourceInstanceId);
                vo.setTargetInstanceId(targetInstanceId);

                vo.setProjectStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
                vo.setOriginStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
            }
			else if (object instanceof DocumentSection)
			{
				vo.setObjectType(new SyncObjectType(SyncObjectType.ID_SECTION));
			}
			else if (object instanceof Zone)
			{
				// Zone is using Friendly Name
				vo.setName(((Zone) object).getFriendlyName());			
				vo.setObjectType(new SyncObjectType(SyncObjectType.ID_ZONE));
			}
			else if (object instanceof TouchpointSelection)
			{
				vo.setObjectType(new SyncObjectType(SyncObjectType.ID_VARIANT));
			}
			else if (object instanceof TargetGroup)  
			{
				vo.setObjectType(new SyncObjectType(SyncObjectType.ID_TARGET_GROUP));
				
				TargetGroup requestTargetGroup = (TargetGroup) object;
                String dna = CloneHelper.queryInSchema(objectFromOther ? otherSchema : currentSchema, ()->requestTargetGroup.getDna());
				
                TargetGroup projectTargetGroup = objectFromOther ? TargetGroup.findByDna(dna) : requestTargetGroup;
				TargetGroup otherTargetGroup = objectFromOther ? requestTargetGroup : CloneHelper.queryInSchema(otherSchema, ()->TargetGroup.findByDna(dna));
				
				if(projectTargetGroup != null) vo.setProjectObjectId(projectTargetGroup.getId());
				if(otherTargetGroup != null) vo.setOriginObjectId(otherTargetGroup.getId());
				
				TargetGroup targetTargetGroup = syncFromOrigin ? projectTargetGroup : otherTargetGroup;
				TargetGroup sourceTargetGroup = syncFromOrigin ? otherTargetGroup : projectTargetGroup;

                vo.setProjectSchema(currentSchema);
                vo.setOriginSchema(otherSchema);

                vo.setSourceInstanceId(sourceInstanceId);
            	vo.setTargetInstanceId(targetInstanceId);
                
                if(sourceTargetGroup != null) {
                	vo.setSourceObjectId(sourceTargetGroup.getId());
	                vo.setSourceHasDependencies(allSourceObjectsWithDependenciesNeedExisting.containsKey(sourceTargetGroup.getId()));
	                vo.setSourceDependenciesNeedCreatingIDs(allSourceObjectsWithDependenciesNeedExisting.get(sourceTargetGroup.getId()));
                }
                
                if(targetTargetGroup != null) {
                	vo.setTargetObjectId(targetTargetGroup.getId());

                	boolean needSetTargetParamer = true; // false;
                	if((!needSetTargetParamer) &&sourceTargetGroup != null && CloneHelper.queryInSchema(sourceSchema, ()->sourceTargetGroup.isParameterized())) {
                		if(! CloneHelper.queryInSchema(targetSchema, ()->targetTargetGroup.isParameterized())) {
                        	needSetTargetParamer = true;
                		} else {
                			Map<ConditionItem, Boolean> sourceConditionParamMap = CloneHelper.queryInSchema(sourceSchema, ()->sourceTargetGroup.getInstance().getConditionParamMap());
                			Map<ConditionItem, Boolean> targetConditionParamMap = CloneHelper.queryInSchema(targetSchema, ()->targetTargetGroup.getInstance().getConditionParamMap());

                			Map<String, ConditionItem> guidToTargetConditionItemMap = CloneHelper.queryInSchema(targetSchema, 
                					()->targetConditionParamMap.entrySet().stream()
                						.filter(e->e.getValue() != null && e.getValue().booleanValue())
                						.collect(Collectors.toMap(e->e.getKey().getConditionElement().getGuid(), e->e.getKey())));
                			
                			for (ConditionItem sourceConditionItem : CloneHelper.queryInSchema(sourceSchema, ()->sourceConditionParamMap.keySet()))
                			{
                				boolean sourceIsParameter = sourceConditionParamMap.containsKey(sourceConditionItem) && sourceConditionParamMap.get(sourceConditionItem);
                				if(sourceIsParameter) {
                					String conditionElementGuid = sourceConditionItem.getConditionElement().getGuid();
                					if(! guidToTargetConditionItemMap.containsKey(conditionElementGuid)) {
                						needSetTargetParamer = true;
                						break;
                					} else {
                						ConditionItem targetConditionItem = guidToTargetConditionItemMap.get(conditionElementGuid);
                						List<ConditionSubelement> sourceSubElements = CloneHelper.queryInSchema(sourceSchema, ()->sourceConditionItem.getConditionElement().getSubElements());
                						List<ConditionSubelement> targetSubElements = CloneHelper.queryInSchema(targetSchema, ()->targetConditionItem.getConditionElement().getSubElements());
                						Map<String, ConditionSubelement> guidToTargetSubElementMap = targetSubElements.stream().collect(Collectors.toMap(se->se.getGuid(), Function.identity()));
                						for (ConditionSubelement sourceSubelement : sourceSubElements) {
                							boolean sourceSubelementIsParameterized = sourceSubelement.isParameterized();
                							if(sourceSubelementIsParameterized) {
                								String subelementGuid = sourceSubelement.getGuid();
                								ConditionSubelement targetConditionSubelement = guidToTargetSubElementMap.get(subelementGuid);
                								if(targetConditionSubelement == null || ! targetConditionSubelement.isParameterized()) {
                            						needSetTargetParamer = true;
                            						break;
                								}
                							}
                						}
                						if(needSetTargetParamer) {
                							break;
                						}
                					}
                				}
                			}
                		}
                	}
                	
                	if(needSetTargetParamer) {
	                	List<ReferencableObject> referencableObjects = allTargetGroupReferencesMapInTargetSchema.get(targetTargetGroup.getId());
	                	if(referencableObjects != null && ! referencableObjects.isEmpty()) {
		                    vo.setTargetIsReferenced(true);
		                    List<String> ids = CloneHelper.queryInSchema(targetSchema, ()->SyncTouchpointUtil.getObjectIdsFromReferencableObjectsList(referencableObjects));
		                    vo.setTargetDependenciesIDs(ids);
		                    vo.setSyncRequest(false);
	                	}
                	}
                }
                vo.setProjectStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
                vo.setOriginStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
			}
			else if (object instanceof ConditionElement)  
			{
				vo.setObjectType(new SyncObjectType(SyncObjectType.ID_TARGET_RULE));
				
				ConditionElement requestConditionElement = (ConditionElement) object;
                String guid = CloneHelper.queryInSchema(objectFromOther ? otherSchema : currentSchema, ()->requestConditionElement.getGuid());
				
                ConditionElement projectConditionElement = objectFromOther ? ConditionElement.findByGuid(guid) : requestConditionElement;
				ConditionElement otherConditionElement = objectFromOther ? requestConditionElement : CloneHelper.queryInSchema(otherSchema, ()->ConditionElement.findByGuid(guid));
				
				if(projectConditionElement != null) vo.setProjectObjectId(projectConditionElement.getId());
				if(otherConditionElement != null) vo.setOriginObjectId(otherConditionElement.getId());
				
				ConditionElement targetConditionElement = syncFromOrigin ? projectConditionElement : otherConditionElement;
				ConditionElement sourceConditionElement = syncFromOrigin ? otherConditionElement : projectConditionElement;

                vo.setProjectSchema(currentSchema);
                vo.setOriginSchema(otherSchema);

                vo.setSourceInstanceId(sourceInstanceId);
            	vo.setTargetInstanceId(targetInstanceId);
                
                if(sourceConditionElement != null) {
                	vo.setSourceObjectId(sourceConditionElement.getId());
	                vo.setSourceHasDependencies(allSourceObjectsWithDependenciesNeedExisting.containsKey(sourceConditionElement.getId()));
	                vo.setSourceDependenciesNeedCreatingIDs(allSourceObjectsWithDependenciesNeedExisting.get(sourceConditionElement.getId()));
                }
                
                if(targetConditionElement != null) {
                	vo.setTargetObjectId(targetConditionElement.getId());
                	
	            	List<ReferencableObject> referencableObjects = allTargetRuleReferencesMapInTargetSchema.get(targetConditionElement.getId()); // CloneHelper.queryInSchema(targetSchema, ()->targetConditionElement.getDirectReferences());
	            	if(referencableObjects != null && ! referencableObjects.isEmpty()) {
	                    vo.setTargetIsReferenced(true);
	                    List<String> ids = CloneHelper.queryInSchema(targetSchema, ()->SyncTouchpointUtil.getObjectIdsFromReferencableObjectsList(referencableObjects));
	                    vo.setTargetDependenciesIDs(ids);
	                    vo.setSyncRequest(false);
	            	}
                }
                
                vo.setProjectStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
                vo.setOriginStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
			}
			else if (object instanceof ParameterGroup)
			{
                vo.setObjectType(new SyncObjectType(SyncObjectType.ID_PARAMETER_GROUP));

                ParameterGroup requestParameterGroup = (ParameterGroup) object;
                String guid = CloneHelper.queryInSchema(objectFromOther ? otherSchema : currentSchema, () -> requestParameterGroup.getGuid());
                boolean isParameter = CloneHelper.queryInSchema(objectFromOther ? otherSchema : currentSchema, () -> requestParameterGroup.isParameter() && requestParameterGroup.getParameters().size() == 1);
                String parameterGuid = isParameter ? CloneHelper.queryInSchema(objectFromOther ? otherSchema : currentSchema, () -> requestParameterGroup.getParameters().get(0).getGuid()) : null;
                String dataElementVariableDna = isParameter ? CloneHelper.queryInSchema(objectFromOther ? otherSchema : currentSchema, () -> requestParameterGroup.getParameters().get(0).getDataElementVariable().getDna()) : null;
                ParameterGroup projectParameterGroup = objectFromOther ? SyncTouchpointUtil.findParameterGroupByGuidOrParameterOrVariable(guid, isParameter, parameterGuid, dataElementVariableDna) : requestParameterGroup;
                ParameterGroup otherParameterGroup = objectFromOther ? requestParameterGroup : CloneHelper.queryInSchema(otherSchema, () -> SyncTouchpointUtil.findParameterGroupByGuidOrParameterOrVariable(guid, isParameter, parameterGuid, dataElementVariableDna));

                if (projectParameterGroup != null) vo.setProjectObjectId(projectParameterGroup.getId());
                if (otherParameterGroup != null) vo.setOriginObjectId(otherParameterGroup.getId());

                ParameterGroup targetParameterGroup = syncFromOrigin ? projectParameterGroup : otherParameterGroup;
                ParameterGroup sourceParameterGroup = syncFromOrigin ? otherParameterGroup : projectParameterGroup;

                vo.setProjectSchema(currentSchema);
                vo.setOriginSchema(otherSchema);

                vo.setSourceInstanceId(sourceInstanceId);
                vo.setTargetInstanceId(targetInstanceId);

                if (sourceParameterGroup != null) {
                    vo.setSourceObjectId(sourceParameterGroup.getId());
                    vo.setSourceHasDependencies(allSourceObjectsWithDependenciesNeedExisting.containsKey(sourceParameterGroup.getId()));
                    vo.setSourceDependenciesNeedCreatingIDs(allSourceObjectsWithDependenciesNeedExisting.get(sourceParameterGroup.getId()));
                }

                if (targetParameterGroup != null) {
                    vo.setTargetObjectId(targetParameterGroup.getId());

                    List<ReferencableObject> referencableObjects = allParameterGroupReferencesMapInTargetSchema.get(targetParameterGroup.getId()); // CloneHelper.queryInSchema(targetSchema, ()->targetParameterGroup.getDirectReferences());
                    if (referencableObjects != null && !referencableObjects.isEmpty()) {
                        vo.setTargetIsReferenced(true);
                        List<String> ids = CloneHelper.queryInSchema(targetSchema, () -> SyncTouchpointUtil.getObjectIdsFromReferencableObjectsList(referencableObjects));
                        vo.setTargetDependenciesIDs(ids);
                        vo.setSyncRequest(false);
                    }
                }

                vo.setProjectStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
                vo.setOriginStatusActiveCopy(new ContentSelectionStatusType(ContentSelectionStatusType.ID_NOT_APPLICABLE));
            }
			else if(object instanceof LookupTable)
			{
                LookupTable requestLookupTable = (LookupTable) object;
                String dna = CloneHelper.queryInSchema(objectFromOther ? otherSchema : currentSchema, ()->requestLookupTable.getDna());
                LookupTable projectLookupTable = objectFromOther ? LookupTable.findByDna(dna) : requestLookupTable;
                LookupTable otherLookupTable = objectFromOther ? requestLookupTable : CloneHelper.queryInSchema(otherSchema, ()->LookupTable.findByDna(dna));

                LookupTable targetLookupTable = syncFromOrigin ? projectLookupTable : otherLookupTable;
                String targetMsgSchema = syncFromOrigin ? currentSchema : otherSchema;

                {
                    LookupTable refLookupTable;
                    String refSchema;

                    if(syncFromOrigin) {
                        refLookupTable = otherLookupTable;
                        refSchema = otherSchema;
                        LookupTable refLookupTableFinal = refLookupTable;
                        if(CloneHelper.queryInSchema(refSchema, ()->refLookupTableFinal == null || (refLookupTableFinal.getWorkingCopy() == null && refLookupTableFinal.getActiveCopy() == null && refLookupTableFinal.getVersionMappings().isEmpty()))) {
                            refLookupTable = projectLookupTable;
                            refSchema = currentSchema;
                        }
                    } else {
                        refLookupTable = projectLookupTable;
                        refSchema = currentSchema;
                        LookupTable refLookupTableFinal = refLookupTable;
                        if(CloneHelper.queryInSchema(refSchema, ()->refLookupTableFinal == null || (refLookupTableFinal.getWorkingCopy() == null && refLookupTableFinal.getActiveCopy() == null && refLookupTableFinal.getVersionMappings().isEmpty()))) {
                            refLookupTable = otherLookupTable;
                            refSchema = otherSchema;
                        }
                    }

                    LookupTable refLookupTableFinal = refLookupTable;

                    CloneHelper.execInSchema(refSchema, ()->{
                        vo.setObjectType(new SyncObjectType(SyncObjectType.ID_LOOKUPTABLE));
                        vo.setName(refLookupTableFinal.getLatestProductionCentric().getName());
                    });
                }
//                vo.setName(CloneHelper.queryInSchema(refSchema, ()->refLookupTableFinal.getLatestProductionCentric().getName()));

                int projectActiveStatus = (int) (objectStatus / 0x1000);
                int originActiveStatus = (int) ((objectStatus & 0xFF) / 0x10);

                vo.setProjectStatusActiveCopy(new ContentSelectionStatusType(projectActiveStatus));
                vo.setOriginStatusActiveCopy(new ContentSelectionStatusType(originActiveStatus));

                if (syncFromOrigin)
                {
                    if (originActiveStatus != ContentSelectionStatusType.ID_UNCHANGED && originActiveStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE && (projectActiveStatus == ContentSelectionStatusType.ID_CHANGED || projectActiveStatus == ContentSelectionStatusType.ID_NEW || projectActiveStatus == ContentSelectionStatusType.ID_FIRST_TIME_SYNC))
                    {
                        vo.setConflictsActiveCopy(true);
                        if (syncCheckBoxRequest == 0)
                            vo.setSyncRequest(false);
                    }
                }
                else
                {
                    if (projectActiveStatus != ContentSelectionStatusType.ID_UNCHANGED && projectActiveStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE && (originActiveStatus == ContentSelectionStatusType.ID_CHANGED || originActiveStatus == ContentSelectionStatusType.ID_NEW || originActiveStatus == ContentSelectionStatusType.ID_FIRST_TIME_SYNC))
                    {
                        vo.setConflictsActiveCopy(true);
                        if (syncCheckBoxRequest == 0)
                            vo.setSyncRequest(false);
                    }
                }

                if(targetLookupTable != null) {
                    LookupTableInstance targetWorkingCopy = CloneHelper.queryInSchema(targetMsgSchema, ()->(LookupTableInstance) targetLookupTable.getWorkingCopy());
                    if(targetWorkingCopy != null) {
                        boolean isMine = CloneHelper.queryInSchema(targetMsgSchema, ()->targetWorkingCopy.isMine());
                        vo.setMyTargetWorkingCopy(isMine);
                    }
                }

                vo.setProjectSchema(currentSchema);
                if (projectLookupTable != null) {
                    vo.setProjectInstanceActiveCopy(CloneHelper.queryInSchema(currentSchema, ()->(LookupTableInstance) projectLookupTable.getActiveCopy()));
                    vo.setProjectInstanceWorkingCopy(CloneHelper.queryInSchema(currentSchema, ()->(LookupTableInstance) projectLookupTable.getWorkingCopy()));
                }

                vo.setOriginSchema(otherSchema);
                if (otherLookupTable != null)
                {
                    vo.setOriginInstanceActiveCopy(CloneHelper.queryInSchema(otherSchema, ()->(LookupTableInstance) otherLookupTable.getActiveCopy()));
                    vo.setOriginInstanceWorkingCopy(CloneHelper.queryInSchema(otherSchema,  ()->(LookupTableInstance) otherLookupTable.getWorkingCopy()));
                }
/*
                else
                {
                    vo.setProjectSchema(currentSchema);
                    if (projectStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE)
                        vo.setProjectInstanceWorkingCopy(CloneHelper.queryInSchema(currentSchema, ()->(LookupTableInstance) refLookupTableFinal.getWorkingCopy()));

                    if (projectActiveStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE)
                        vo.setProjectInstanceActiveCopy(CloneHelper.queryInSchema(currentSchema, ()->(LookupTableInstance) refLookupTableFinal.getActiveCopy()));

                    vo.setOriginSchema(refSchema);
                    if (originStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE)
                        vo.setOriginInstanceWorkingCopy(CloneHelper.queryInSchema(refSchema, ()->(LookupTableInstance) refLookupTableFinal.getWorkingCopy()));

                    if (originActiveStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE)
                        vo.setOriginInstanceActiveCopy(CloneHelper.queryInSchema(refSchema, ()->(LookupTableInstance) refLookupTableFinal.getActiveCopy()));
                }
*/
                LookupTable sourceLookupTable = syncFromOrigin ? otherLookupTable : projectLookupTable;

                if(sourceLookupTable != null) {
                    vo.setSourceObjectId(sourceLookupTable.getId());
                    vo.setSourceInstanceId(sourceInstanceId);

                    vo.setSourceHasDependencies(allSourceObjectsWithDependenciesNeedExisting.containsKey(sourceLookupTable.getId()));
                    vo.setSourceDependenciesNeedCreatingIDs(allSourceObjectsWithDependenciesNeedExisting.get(sourceLookupTable.getId()));
                }

                if(targetLookupTable != null) {
                    vo.setTargetObjectId(targetLookupTable.getId());
                    vo.setTargetInstanceId(targetInstanceId);
                }

                boolean needDeleteActive = CloneHelper.queryInSchema(targetSchema, ()->(targetLookupTable != null && targetLookupTable.getProduction() != null))
                        && CloneHelper.queryInSchema(sourceSchema, ()->(sourceLookupTable == null || sourceLookupTable.isRemoved() || sourceLookupTable.getProduction() == null));

                if((sourceLookupTable == null || sourceLookupTable.isRemoved() || needDeleteActive) && targetLookupTable != null && targetObjectIDsWithReferencingMap.containsKey(targetLookupTable.getId())) {
                    List<ReferencableObject> referencableObjects = allLookupTableReferencesMapInTargetSchema.get(targetLookupTable.getId());
                    if (referencableObjects != null && !referencableObjects.isEmpty()) {
                        vo.setTargetIsReferenced(true);
                        List<String> ids = CloneHelper.queryInSchema(targetSchema, () -> SyncTouchpointUtil.getObjectIdsFromReferencableObjectsList(referencableObjects));
                        vo.setTargetDependenciesIDs(ids);
                        vo.setSyncRequest(false);
                    }
                }
			}
/*
            else if (object instanceof ContentObject)
            {
                ContentObject requestContentObject = (ContentObject) object;

                boolean isGlobalContent = CloneHelper.queryInSchema(objectFromOther ? otherSchema : currentSchema,
                        ()->requestContentObject.isGlobalContentObject());

                String dna = CloneHelper.queryInSchema(objectFromOther ? otherSchema : currentSchema,
                        ()->requestContentObject.getDna());

                ContentObject projectContentObject = objectFromOther ? (isGlobalContent ? ContentObject.findByDna(dna) : ContentObject.findByDnaAndDocument(dna, document)) : requestContentObject;
                ContentObject otherContentObject = objectFromOther ? requestContentObject : CloneHelper.queryInSchema(otherSchema, ()->(isGlobalContent ? ContentObject.findByDna(dna) : ContentObject.findByDnaAndDocument(dna, otherDocument)));


                CloneHelper.execInSchema(objectFromOther ? otherSchema : currentSchema, ()->{
                    if (isGlobalContent) {
                        if ( requestContentObject.isGlobalImage())
                            vo.setObjectType(new SyncObjectType(SyncObjectType.ID_CONTENT_LIBRARY));
                        else if ( requestContentObject.getIsGlobalSmartText())
                            vo.setObjectType(new SyncObjectType(SyncObjectType.ID_SMART_TEXT));
//                        else if ( requestContentObject.isGlobalSmartCanvas())
//                            vo.setObjectType(new SyncObjectType(SyncObjectType.ID_SMART_CANVAS));
                    } else {
                        if ( requestContentObject.isLocalImage() )
                            vo.setObjectType(new SyncObjectType(SyncObjectType.ID_LOCAL_IMAGE));
                        else if ( requestContentObject.isLocalSmartText() )
                            vo.setObjectType(new SyncObjectType(SyncObjectType.ID_LOCAL_SMART_TEXT));
//                        else if ( requestContentObject.isLocalSmartCanvas() )
//                            vo.setObjectType(new SyncObjectType(SyncObjectType.ID_LOCAL_SMART_CANVAS));
                        else
                            vo.setObjectType(new SyncObjectType(SyncObjectType.ID_MESSAGE));
                    }
                    vo.setName(requestContentObject.getName());
                });

                vo.setSyncRequest(true);

                int projectActiveStatus = (int) (objectStatus / 0x1000);
                int originActiveStatus = (int) ((objectStatus & 0xFF) / 0x10);

                vo.setProjectStatusActiveCopy(new ContentSelectionStatusType(projectActiveStatus));
                vo.setOriginStatusActiveCopy(new ContentSelectionStatusType(originActiveStatus));

                if (syncFromOrigin)
                {
                    if (originActiveStatus != ContentSelectionStatusType.ID_UNCHANGED && originActiveStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE && (projectActiveStatus == ContentSelectionStatusType.ID_CHANGED || projectActiveStatus == ContentSelectionStatusType.ID_NEW))
                    {
                        vo.setConflictsActiveCopy(true);
                        if (syncCheckBoxRequest == 0)
                            vo.setSyncRequest(false);
                    }
                }
                else
                {
                    if (projectActiveStatus != ContentSelectionStatusType.ID_UNCHANGED && projectActiveStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE && (originActiveStatus == ContentSelectionStatusType.ID_CHANGED || originActiveStatus == ContentSelectionStatusType.ID_NEW))
                    {
                        vo.setConflictsActiveCopy(true);
                        if (syncCheckBoxRequest == 0)
                            vo.setSyncRequest(false);
                    }
                }

                ContentObject targetContentObject = syncFromOrigin ? projectContentObject : otherContentObject;
                String targetContentObjectSchema = syncFromOrigin ? currentSchema : otherSchema;

                if(targetContentObject != null) {
                    boolean isMine = CloneHelper.queryInSchema(targetContentObjectSchema, () -> targetContentObject.isMine());
                    vo.setMyTargetWorkingCopy(isMine);
                }

                vo.setProjectSchema(currentSchema);
                if (projectContentObject != null) {
                    vo.setProjectInstanceActiveCopy(CloneHelper.queryInSchema(currentSchema, ()->(ContentObjectData) projectContentObject.getContentObjectDataTypeMap().get(ContentObject.DATA_TYPE_ACTIVE)));
                    vo.setProjectInstanceWorkingCopy(CloneHelper.queryInSchema(currentSchema, ()->(ContentObjectData) projectContentObject.getContentObjectDataTypeMap().get(ContentObject.DATA_TYPE_WORKING)));
                }

                vo.setOriginSchema(otherSchema);
                if (otherContentObject != null) {
                    vo.setOriginInstanceActiveCopy(CloneHelper.queryInSchema(otherSchema, ()->(ContentObjectData) otherContentObject.getContentObjectDataTypeMap().get(ContentObject.DATA_TYPE_ACTIVE)));
                    vo.setOriginInstanceWorkingCopy(CloneHelper.queryInSchema(otherSchema,  ()->(ContentObjectData) otherContentObject.getContentObjectDataTypeMap().get(ContentObject.DATA_TYPE_WORKING)));
                }

                ContentObject sourceContentObject = syncFromOrigin ? otherContentObject : projectContentObject;
                if(sourceContentObject != null) {
                    vo.setSourceObjectId(sourceContentObject.getId());
                    vo.setSourceInstanceId(sourceInstanceId);
                    vo.setSourceHasDependencies(allSourceObjectsWithDependenciesNeedExisting.containsKey(sourceContentObject.getId()));
                    vo.setSourceDependenciesNeedCreatingIDs(allSourceObjectsWithDependenciesNeedExisting.get(sourceContentObject.getId()));
                }

                if(targetContentObject != null) {
                    vo.setTargetObjectId(targetContentObject.getId());
                    vo.setTargetInstanceId(targetInstanceId);
                }

                boolean needDeleteActive = CloneHelper.queryInSchema(targetSchema, ()->(targetContentObject != null && targetContentObject.hasActiveData()))
                        && CloneHelper.queryInSchema(sourceSchema, ()->(sourceContentObject == null || sourceContentObject.isRemoved() || !sourceContentObject.hasActiveData()));
                if((sourceContentObject == null || sourceContentObject.isRemoved() || needDeleteActive) && targetContentObject != null && targetObjectIDsWithReferencingMap.containsKey(targetContentObject.getId())) {
                    vo.setTargetIsReferenced(true);
                    vo.setTargetDependenciesIDs(targetObjectIDsWithReferencingMap.get(targetContentObject.getId()));
                    vo.setSyncRequest(false);
                }
            }
 */
			else if (object instanceof ContentObject)
			{
                ContentObject requestContentObject = (ContentObject) object;
                boolean isLocalContentObject = requestContentObject.isMessage() || requestContentObject.isLocalContentObject();
                String contentObjectDna = objectFromOther ? CloneHelper.queryInSchema(otherSchema, ()->requestContentObject.getDna()) : requestContentObject.getDna();
                ContentObject projectContentObject = objectFromOther ? (isLocalContentObject ? ContentObject.findByDnaAndDocument(contentObjectDna, document) : ContentObject.findGlobalObjectByDna(contentObjectDna)) : requestContentObject;
                ContentObject otherContentObject = objectFromOther ? requestContentObject : CloneHelper.queryInSchema(otherSchema, ()->isLocalContentObject ? ContentObject.findByDnaAndDocument(contentObjectDna, otherDocument) : ContentObject.findGlobalObjectByDna(contentObjectDna));

                ContentObject targetContentObject = syncFromOrigin ? projectContentObject : otherContentObject;
                String targetMsgSchema = syncFromOrigin ? currentSchema : otherSchema;

                {
                    ContentObject refContentObject;
                String refSchema;
                
                if(syncFromOrigin) {
                	refContentObject = otherContentObject;
                	refSchema = otherSchema;
                    ContentObject refContentObjectFinal = refContentObject;
	                if(CloneHelper.queryInSchema(refSchema, ()->refContentObjectFinal == null || (!refContentObjectFinal.hasWorkingData() && !refContentObjectFinal.hasActiveData() && !refContentObjectFinal.hasArchivedData()))) {
	                    if(projectContentObject != null) {
                            refContentObject = projectContentObject;
                            refSchema = currentSchema;
                        }
	                }
                } else {
                	refContentObject = projectContentObject;
                	refSchema = currentSchema;
                    ContentObject refContentObjectFinal = refContentObject;
	                if(CloneHelper.queryInSchema(refSchema, ()->refContentObjectFinal == null || (!refContentObjectFinal.hasWorkingData() && !refContentObjectFinal.hasActiveData() && !refContentObjectFinal.hasArchivedData()))) {
	                    if(otherContentObject != null) {
                            refContentObject = otherContentObject;
                            refSchema = otherSchema;
                        }
	                }
                }

                ContentObject refContentObjectFinal = refContentObject;

                CloneHelper.execInSchema(refSchema, ()->{
    				if (refContentObjectFinal.isLocalImage()) {
                        vo.setObjectType(new SyncObjectType(SyncObjectType.ID_LOCAL_IMAGE));
                    } if (refContentObjectFinal.isLocalSmartText()) {
   						vo.setObjectType(new SyncObjectType(SyncObjectType.ID_LOCAL_SMART_TEXT));
    				} else if (refContentObjectFinal.isGlobalImage()) {
                        vo.setObjectType(new SyncObjectType(SyncObjectType.ID_CONTENT_LIBRARY));
                    } else if (refContentObjectFinal.isGlobalSmartText()) {
                        vo.setObjectType(new SyncObjectType(SyncObjectType.ID_SMART_TEXT));
                    } else {
                        vo.setObjectType(new SyncObjectType(SyncObjectType.ID_MESSAGE));
    				}
                    vo.setName(refContentObjectFinal.getName());
				});
                }
				
				int projectActiveStatus = (int) (objectStatus / 0x1000);
				int originActiveStatus = (int) ((objectStatus & 0xFF) / 0x10);
				
				vo.setProjectStatusActiveCopy(new ContentSelectionStatusType(projectActiveStatus));
				vo.setOriginStatusActiveCopy(new ContentSelectionStatusType(originActiveStatus));

				if (syncFromOrigin)
				{
					if (originActiveStatus != ContentSelectionStatusType.ID_UNCHANGED && originActiveStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE && (projectActiveStatus == ContentSelectionStatusType.ID_CHANGED || projectActiveStatus == ContentSelectionStatusType.ID_NEW || projectActiveStatus == ContentSelectionStatusType.ID_FIRST_TIME_SYNC))
					{
						vo.setConflictsActiveCopy(true);
						if (syncCheckBoxRequest == 0)
							vo.setSyncRequest(false);
					}
				}
				else
				{
					if (projectActiveStatus != ContentSelectionStatusType.ID_UNCHANGED && projectActiveStatus != ContentSelectionStatusType.ID_NOT_APPLICABLE && (originActiveStatus == ContentSelectionStatusType.ID_CHANGED || originActiveStatus == ContentSelectionStatusType.ID_NEW || originActiveStatus == ContentSelectionStatusType.ID_FIRST_TIME_SYNC))
					{
						vo.setConflictsActiveCopy(true);
						if (syncCheckBoxRequest == 0)
							vo.setSyncRequest(false);
					}
				}
				
				if(targetContentObject != null) {
                    boolean isMine = CloneHelper.queryInSchema(targetMsgSchema, ()->targetContentObject.getLockedForId() == 0 || targetContentObject.isMine());
                    vo.setMyTargetWorkingCopy(isMine);
				}
				
				vo.setProjectSchema(currentSchema);
				if (projectContentObject != null) {
				    vo.setProjectObjectId(projectContentObject.getId());
					vo.setProjectInstanceActiveCopy(CloneHelper.queryInSchema(currentSchema, ()->(ContentObjectData) projectContentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE)));
					vo.setProjectInstanceWorkingCopy(CloneHelper.queryInSchema(currentSchema, ()->(ContentObjectData) projectContentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING)));
				}
				vo.setOriginSchema(otherSchema);
				if (otherContentObject != null)
				{
				    vo.setOriginObjectId(otherContentObject.getId());
					vo.setOriginInstanceActiveCopy(CloneHelper.queryInSchema(otherSchema, ()->(ContentObjectData) otherContentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE)));
					vo.setOriginInstanceWorkingCopy(CloneHelper.queryInSchema(otherSchema,  ()->(ContentObjectData) otherContentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING)));
				}
				
				ContentObject sourceMessage = syncFromOrigin ? otherContentObject : projectContentObject;
				
				if(sourceMessage != null) {
	                vo.setSourceObjectId(sourceMessage.getId());
	                vo.setSourceInstanceId(sourceInstanceId);
	                vo.setSourceHasDependencies(allSourceObjectsWithDependenciesNeedExisting.containsKey(sourceMessage.getId()));
	                vo.setSourceDependenciesNeedCreatingIDs(allSourceObjectsWithDependenciesNeedExisting.get(sourceMessage.getId()));
				}
				
                if(targetContentObject != null) {
                	vo.setTargetObjectId(targetContentObject.getId());
                	vo.setTargetInstanceId(targetInstanceId);
                }
                
                boolean needDeleteActive = CloneHelper.queryInSchema(targetSchema, ()->(targetContentObject != null && targetContentObject.hasActiveData()))
                		&& CloneHelper.queryInSchema(sourceSchema, ()->(sourceMessage == null || sourceMessage.isRemoved() || (!sourceMessage.hasActiveData())));
                if((sourceMessage == null || sourceMessage.isRemoved() || needDeleteActive) && targetContentObject != null && targetObjectIDsWithReferencingMap.containsKey(targetContentObject.getId())) {
                    vo.setTargetIsReferenced(true);
                    vo.setTargetDependenciesIDs(targetObjectIDsWithReferencingMap.get(targetContentObject.getId()));
					vo.setSyncRequest(false);
                }
			}
			
			aaData.add(vo);
			
            count ++;
            long currentMillis = System.currentTimeMillis();
            if(currentMillis - startMillis > 5000) {
                log.info("init add aaData processed " + count);
                startMillis = currentMillis;
            }
			
		}
		
        log.info("init add aaData processed " + count);
        
        log.info("init add aaData ends");
        
		Collections.sort(aaData, (vo1, vo2)->{
		    String name1 = vo1.getTrueName();
		    String name2 = vo2.getTrueName();
		    name1 = name1.trim();
		    name2 = name2.trim();
		    return name1.compareTo(name2);
		});
		
        log.info("init ends");
        
		super.setAaData(aaData);
	}

}
