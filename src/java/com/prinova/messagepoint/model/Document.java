package com.prinova.messagepoint.model;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.prinova.messagepoint.email.EmailTemplateUtils;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.attachment.Attachment;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.common.ArchiveType;
import com.prinova.messagepoint.model.communication.CommunicationDataPrivacyType;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItemDefinition;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.dialogue.DialogueConfiguration;
import com.prinova.messagepoint.model.email.TemplateModifier;
import com.prinova.messagepoint.model.email.TemplateVariant;
import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.model.font.*;
import com.prinova.messagepoint.model.gmc.GMCConfiguration;
import com.prinova.messagepoint.model.insert.DocumentTenantEmailAddress;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.insert.RateScheduleCollection;
import com.prinova.messagepoint.model.insert.WeightUtil;
import com.prinova.messagepoint.model.metadata.*;
import com.prinova.messagepoint.model.proof.ProofDefinition;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.sefas.MPHCSConfiguration;
import com.prinova.messagepoint.model.sefas.SefasConfiguration;
import com.prinova.messagepoint.model.tagcloud.TagCloudType;
import com.prinova.messagepoint.model.testing.DataFile;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.model.webservice.WebServiceConfiguration;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.model.wrapper.ConnecterConfigurationWrapper;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.tagcloud.UpdateTagCloudService;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.*;
import com.prinova.messagepoint.util.filewriterimpl.DocumentFileWriter;
import com.prinova.messagepoint.wtu.Referencable;
import com.prinova.messagepoint.wtu.ReferencableObject;
import com.prinova.messagepoint.wtu.services.DirectReferencesFetchService;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.hibernate.query.Query;

import java.io.File;
import java.io.Serializable;
import java.math.BigInteger;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.lang.Boolean.FALSE;
import static java.util.Objects.nonNull;

public class Document extends IdentifiableMessagePointModel implements Serializable, Referencable {
	private static final long serialVersionUID = 5485727564100638366L;

    private static final Log log = LogUtil.getLog(Document.class);
	public static final String CLASS_NAME = "com.prinova.messagepoint.model.Document" ;
	
	public static final int COMMUNICATION_PRODUCTION_TYPE_ON_APPROVAL				= 1;
	public static final int COMMUNICATION_PRODUCTION_TYPE_BATCH					= 2;
	public static final int COMMUNICATION_PRODUCTION_TYPE_ON_APPROVAL_MINI_BUNDLE	= 3;
	
	public static final String CHANNEL_ALTERNATE_KEY_PRINT				= "print";
	public static final String CHANNEL_ALTERNATE_KEY_EMAIL				= "email";
	public static final String CHANNEL_ALTERNATE_KEY_WEB				= "web";
	
	private String 						externalId;
	private boolean 					enabled 					= true;
	private boolean 					removed 					= false;
	private Integer						sequence;
    private boolean                     targetOfSync                = true;
    private boolean                     acceptOnlyActiveObjects     = false;
    private boolean                     activeObjectsSyncThroughWorkflow   = false;

	private DataGroup					dataGroup;
	private Set<DataFile> 				dataFiles 					= new HashSet<>();
	private Set<DataResource> 			dataResources 				= new HashSet<>();
	private DataSourceAssociation 		dataSourceAssociation;
	
	private boolean 					tpContentChanged  			= true;
	private boolean 					trashTouchpoint				= false;
	
	// LAYOUT
	private Set<DocumentSection> 		documentSections 			= new HashSet<>();
	private Set<Zone> 					zones 						= new HashSet<>();
	private Zone[] 						sortedZoneArray;
	private Zone[] 						nameSortedZoneArray;
	private Zone[] 						friendlyNameSortedZoneArray;
	
	private Set<Attachment> 	    	attachments 				= new HashSet<>();
	private Set<DataElementVariable>	extReportingDataVariables	= new HashSet<>();
	
    private TextStyle					defaultTextStyle;
    private ParagraphStyle				defaultParagraphStyle;
    private ListStyle					defaultListStyle;
    
    private TouchpointTargeting 		tpTargeting = null;
    private String						connectorName;
    
    private Set<TpCollectionTouchpointAssignment> 	tpCollectionTouchpoints = new HashSet<>();
	
	// INSERT MANAGEMENT
	private ParameterGroup 				insertParameterGroup;
	private int 						firstPageWeight;
	private int 						otherPagesWeight;
	private RateScheduleCollection 		defaultRateScheduleCollection;

	private ParameterGroup 				selectionParameterGroup;
	private Set<TouchpointSelection> 	touchpointSelections 		= new HashSet<>();
	private int 						selectionWorkflowTypeId;
	private boolean						selectionVisibleByDefault	= true;
	
	private Set<ConfigurableWorkflow> 	workflows 					= new HashSet<>();
	
	private ParameterGroup 				languageParameterGroup;
	private Set<LanguageSelection> 		languageSelections 			= new HashSet<>();

    private boolean                     variedByTouchpointVariant   = true;
    private ParameterGroup              formattingParameterGroup;
    private Set<FormattingSelection>    formattingSelections        = new HashSet<>();

	private ConnectorConfiguration 			connectorConfiguration;
	private ConnecterConfigurationWrapper 	connectorConfigWrapper 	= new ConnecterConfigurationWrapper();
	
	private boolean						processUsingCombinedContent = false;
	
	private Set<DocumentTenantEmailAddress> tenantEmailAddresses	= new HashSet<>();

	private Set<TouchpointLanguage> 	touchpointLanguages 		= new HashSet<>();
    private String                      dictionaryIdentifier;
	
	private int 						fiscalYearStartMonth = 1;
	private DataElementVariable 		customerReportingVariableA;
	private DataElementVariable 		customerReportingVariableB;

	private Set<TemplateModifier> 		masterTemplateModifiers 	= new HashSet<>();
	private Set<TemplateVariant>		templateVariants 			= new HashSet<>();
	
	// SFTP
	private String						sftpIPAddress;
	private String						sftpFolderPath;
	private String						sftpUsername;
	private String						sftpPassword;
	private String						sftpSSHKey;
    private String                      privateKey;
	
	// CONNECTED
	private boolean										connectedEnabled							= false;
	private DataResource 								communicationsDataResource;
	private ParagraphStyle								communicationZoneMarkerStyle;
	private Set<CommunicationOrderEntryItemDefinition>	communicationOrderEntryItemDefinitions		= new HashSet<>();
	private boolean										communicationOrderEntryEnabled				= true;
	private WebServiceConfiguration						communicationCompositionResultsWebService;
	private WebServiceConfiguration						communicationProductionStatusWebService;
	private WebServiceConfiguration						communicationDataFeedWebService;
	private WebServiceConfiguration						communicationNotificationWebService;
	private int											communicationProductionTypeId				= 3;
	private DataElementVariable							communicationMultiRecipientIdentifier		= null;
	private boolean										communicationExternalValidationEnabled		= false;
	private boolean										communicationAppliesTagCloud				= true;
	private boolean										communicationAppliesCopiesInput				= true;
	private boolean										communicationDisplayTouchpointThumbnail		= true;
	private boolean										communicationAppliesTouchpointSelection		= false;
	private boolean										communicationZoneContentTransient			= false;
	private String										communicationForcedProofingDriverValue;
	private boolean										communicationResolveVariableValues			= false;
	private boolean										communicationSuppressNonEditableZones		= false;
	private Integer										communicationPDFConversionQuality			= 70;
	private boolean										communicationSkipInteractiveWhenNoZones		= false;
	private boolean										communicationUseBeta						= true;
    private boolean                                     communicationFastOrderIndicatorEdit         = false;

	// SEGMENTATION ANALYSIS
	private boolean										segmentationAnalysisEnabled					= false;
	private DataResource								segmentationAnalysisResource;
	
	// METADATA
	private MetadataFormDefinition						touchpointMetadataFormDefinition;
	private MetadataFormDefinition						variantMetadataFormDefinition;
	private MetadataForm								metadataForm;
	private boolean										maintainVariantHierarchy					= true;

	private int 										hpImportPageCount = 0;
	private long										communicationUniqueIndex					= 0;

	// EXCHANGE
	private String 										exchangeInstanceGuid = null;
	private String 										exchangeTouchpointGuid = null;
	private Timestamp 									exchangePublishedTimestamp = null;
	private Timestamp 									exchangeUpdatedTimestamp = null;
	
	// HIDDEN ADMIN FLAG
	private boolean										useConnectorAsZoneId						= false;
	
	// MESSAGE WORKFLOW
	private ConfigurableWorkflow 		messageWorkflow;
	
	// CONNECTED WORKFLOW
	private ConfigurableWorkflow	connectedWorkflow;

	// BLUE RELAY
	private String blueRelayEndpoint;
	private String blueRelayUsername;
	private String blueRelayToken;
	private String blueRelayTargetFolder;

	// BRAND
	private BrandProfile brandProfile;

	// STRIPO
	private boolean stripoEnabled;

	// History
    private Set<DocumentHistory> historyRecords;

	// Public default constructor
	//
	public Document()
	{
	}

	// Copy constructor (not public) for cloning 
	//
	protected Document(Timestamp timestamp, Document cloneFrom, Document clonedPrimaryLayoutDocument, Document clonedChannelParentDocument, boolean cloneDataSourceAssociation, Set<Long> messagepointLocales)
	{
		super(cloneFrom);

		if(clonedPrimaryLayoutDocument == null) {
		    this.enabled = false;
		} else {
            this.enabled = true;
		}

        this.targetOfSync = true;
        this.acceptOnlyActiveObjects = false;
        this.activeObjectsSyncThroughWorkflow = false;

        // SFTP
        this.sftpIPAddress = cloneFrom.sftpIPAddress;
        this.sftpFolderPath = cloneFrom.sftpFolderPath;
        this.sftpUsername = cloneFrom.sftpUsername;
        this.sftpPassword = cloneFrom.sftpPassword;
        this.sftpSSHKey = cloneFrom.sftpSSHKey;
        this.privateKey = cloneFrom.privateKey;

        // CONNECTED
        this.connectedEnabled = cloneFrom.connectedEnabled;

        this.save();
        
		this.checkoutTimestamp = timestamp;

		boolean breakInheritance = cloneFrom.isBreakInheritance();
		cloneFrom.setBreakInheritance(true);

        boolean isRoot = cloneFrom.isRootDocument();
        boolean isConnected = cloneFrom.isConnectedEnabled();


		this.metatags = cloneFrom.metatags;
		this.externalId = cloneFrom.externalId;
		this.description = cloneFrom.description;
		// Keep the document disabled until cloning process finished.
        if(clonedPrimaryLayoutDocument == null) {
            this.enabled = false; //  cloneFrom.enabled; 
        } else {
            this.enabled = cloneFrom.enabled; 
        }
		this.sequence = cloneFrom.sequence;
		
		if (isRoot && cloneDataSourceAssociation)
		{
            DataSourceAssociation sourceDataSourceAssociation = cloneFrom.getDataSourceAssociation();
            DataSourceAssociation targetDataSourceAssociation = null;
            Function<DataSourceAssociation, DataSourceAssociation> cloneDataSourceAssociationFunction = dsa->{
                DataSourceAssociation clonedDsa = dsa.clone(this);
                for(ReferenceConnection sourceRc : dsa.getReferenceConnections()) {
                    if(sourceRc.getReferenceDataSource() != null && sourceRc.getReferenceDataSource().getSourceType() != null && sourceRc.getReferenceDataSource().getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED) {
                        ReferenceConnection clonedReferenceConnection = CloneHelper.clone(sourceRc, rc -> rc.clone(clonedDsa));
                        CloneHelper.execInSaveSession(()->{
                            clonedReferenceConnection.save();
                            clonedDsa.getReferenceConnections().add(clonedReferenceConnection);
                        });
                    }
                }
                CloneHelper.execInSaveSession(()->{
                    clonedDsa.save();
                });
                return clonedDsa;
            };

            if(isConnected) {
                DataSourceAssociation clonedDataSourceAssociation = CloneHelper.clone(sourceDataSourceAssociation, cloneDataSourceAssociationFunction);
                targetDataSourceAssociation = clonedDataSourceAssociation;
            }
            else {
                targetDataSourceAssociation = CloneHelper.assign(sourceDataSourceAssociation, cloneDataSourceAssociationFunction);
            }
            this.dataSourceAssociation = targetDataSourceAssociation;
	        if(this.dataSourceAssociation != null) {
                DataSource clonedPrimaryDataSource = CloneHelper.queryInSaveSession(()->this.dataSourceAssociation.getPrimaryDataSource());
                if(clonedPrimaryDataSource != null) {
                    this.dataGroup = CloneHelper.assign(cloneFrom.dataGroup, dg -> dg.clone(clonedPrimaryDataSource));
                }
	        }
		}
        
		Map<Long, DataResource> clonedDataResourcesMap = new HashMap<>();

//        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null)
        if(isRoot) {
        	// Clone data files if it's the same instance
        	//
        	
			if (cloneFrom.dataFiles != null) {
				for (DataFile dataFile : cloneFrom.dataFiles) {
                    DataFile clonedDataFile = CloneHelper.assign(dataFile);
                    CloneHelper.execInSaveSession(()->{
                        this.dataFiles.add(clonedDataFile);
                    });
				}
			}
			
			if (cloneFrom.dataResources != null) {
				Set<DataResource> dataResourcesToClone = cloneFrom.getDataResources();
				if(CloneHelper.getIsCrossInstanceClone()){
					dataResourcesToClone = cloneFrom.getDataResources()
							.stream()
							.filter(dr -> DataResource.isAssociatedWithDataSourceAssociation(dr, cloneFrom.getDataSourceAssociation()))
							.collect(Collectors.toSet());
				}

				for (DataResource dataResource : dataResourcesToClone) {
					DataResource clonedDataResource = CloneHelper.clone(dataResource, dr->dr.clone(this));
					if(timestamp != null) {
					    clonedDataResource.cloneDna();
					}
                    CloneHelper.execInSaveSession(()->{
                        clonedDataResource.save();
                        this.dataResources.add(clonedDataResource);
                    });
					clonedDataResource.save();
//			        if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() != null) {
//			            clonedDataResource.save();
//			        }
					
					clonedDataResourcesMap.put(dataResource.getId(), clonedDataResource);
				}
			}
        }
		
		// LAYOUT
        Document channelPrimaryDoc = clonedChannelParentDocument == null ? clonedPrimaryLayoutDocument : clonedChannelParentDocument;
		if (cloneFrom.documentSections != null) {
			for (DocumentSection section : cloneFrom.documentSections) {
			    DocumentSection clonedSection = CloneHelper.clone(section, ds->ds.clone(timestamp, this, channelPrimaryDoc));
                if(timestamp != null) {
                    clonedSection.cloneDna();
                }
                clonedSection.save();
				this.documentSections.add(clonedSection);
			}
		}
		

		Set<Workgroup> workgroups = new HashSet<>();
        Map<String, Zone> dnaToZone = new HashMap<>();

        CloneHelper.execInSaveSession(()->{
            for (DocumentSection section : this.documentSections) {
                for (Zone zone : section.getZones()) {
                    zone.setDocument(this);
                    zone.setCheckoutTimestamp(timestamp);
                    if(timestamp != null) {
                        zone.cloneDna();
                    }
                    zone.save();
                    dnaToZone.put(zone.getDna(), zone);
                    workgroups.addAll(zone.getWorkgroups());
                    this.zones.add(zone);
                    for(ZonePart zonePart : zone.getParts()) {
                        zonePart.setCheckoutTimestamp(timestamp);
                        if(timestamp != null) {
                            zonePart.cloneDna();
                            zonePart.save();
                        }
                    }
                }
            }
        });


        for(DocumentSection sourceSection : cloneFrom.getDocumentSections()) {
            for (Zone sourceZone : sourceSection.getZones()) {
                Zone targetZone = dnaToZone.get(sourceZone.getDna());
                Zone sourceFlowIntoZone = sourceZone.getFlowIntoZone();
                Zone targetFlowIntoZone = sourceFlowIntoZone == null ? null : dnaToZone.get(sourceFlowIntoZone.getDna());
                CloneHelper.execInSaveSession(()->targetZone.setFlowIntoZone(targetFlowIntoZone));
            }
        }
/*
    	for (Zone sourceZone : cloneFrom.getZones()) {
    	    if(sourceZone.getSection() == null) {
    	        Zone clonedZone = CloneHelper.clone(sourceZone, z->z.clone(timestamp, null, this, clonedPrimaryLayoutDocument));
    	        clonedZone.setDocument(this);
    	        clonedZone.setCheckinTimestamp(timestamp);
    	        clonedZone.save();
                workgroups.addAll(clonedZone.getWorkgroups());
                this.zones.add(clonedZone);
                for(ZonePart zonePart : clonedZone.getParts()) {
                    zonePart.setCheckoutTimestamp(timestamp);
                    if(timestamp != null) {
                        zonePart.cloneDna();
                        zonePart.save();
                    }
                }
    	    }
    	}
*/    	
    	workgroups.forEach(Workgroup::save);
    	
		if (isRoot && cloneFrom.attachments != null) {
			for (Attachment attachment : cloneFrom.attachments) {
			    Attachment clonedAttachment = CloneHelper.clone(attachment, at->at.clone(this));
                if(timestamp != null) {
                    clonedAttachment.cloneDna();
//                    clonedAttachment.save();
                    if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() != null) {
                        clonedAttachment.save();
                    }
                }
				this.attachments.add(clonedAttachment);
			}
		}

        if(isRoot) {
            this.extReportingDataVariables.addAll(CloneHelper.assign(cloneFrom.extReportingDataVariables));
        }

        if(isRoot) {
            this.defaultTextStyle = CloneHelper.assign(cloneFrom.defaultTextStyle);
            this.defaultParagraphStyle = CloneHelper.assign(cloneFrom.defaultParagraphStyle);
            this.defaultListStyle = CloneHelper.assign(cloneFrom.defaultListStyle);
        }

        // Brand
        if(isRoot) {
            this.brandProfile = CloneHelper.assign(cloneFrom.brandProfile);
        }

        if(isRoot) {
            if (cloneFrom.tpTargeting != null)
                this.tpTargeting = CloneHelper.clone(cloneFrom.tpTargeting, tp -> tp.clone(this));
            else {
                TouchpointTargeting touchpointTargeting = TouchpointTargeting.findByDocument(cloneFrom);
                if (touchpointTargeting != null)
                    this.tpTargeting = CloneHelper.clone(touchpointTargeting, tp -> tp.clone(this));
            }
        }

	    this.connectorName = cloneFrom.connectorName;

		// INSERT MANAGEMENT
        if(isRoot) {
            this.insertParameterGroup = CloneHelper.assign(cloneFrom.insertParameterGroup);
            this.firstPageWeight = cloneFrom.firstPageWeight;
            this.otherPagesWeight = cloneFrom.otherPagesWeight;
            this.defaultRateScheduleCollection = CloneHelper.assign(cloneFrom.defaultRateScheduleCollection);

            this.selectionParameterGroup = CloneHelper.assign(cloneFrom.selectionParameterGroup);
        }

		// Message Workflows
        ConfigurableWorkflow messageWorkflow = CloneHelper.assign(cloneFrom.getMessageWorkflow(), o->o.clone(this));
        
        if (messageWorkflow != null){
	        CloneHelper.execInSaveSession(()->messageWorkflow.getWorkflowLibraryDocuments().add(this));
	        this.setMessageWorkflow(messageWorkflow);
        }
        
        // Connected Workflows
        ConfigurableWorkflow connectedWorkflow = CloneHelper.assign(cloneFrom.getConnectedWorkflow(), o->o.clone(this));
        
        if (connectedWorkflow != null) {
            CloneHelper.execInSaveSession(()->connectedWorkflow.getWorkflowLibraryDocuments().add(this));
	        this.setConnectedWorkflow(connectedWorkflow);
        }

        Multimap<ParameterGroupTreeNode, MessagepointSelection> parentPGTreeNodeMap = ArrayListMultimap.create();
        if(isRoot) {
            if (cloneFrom.touchpointSelections != null) {
                List<TouchpointSelection> sortedTouchpointSelections = SyncTouchpointUtil.getSortedMessagepointSelectionList(cloneFrom.touchpointSelections);

                for (TouchpointSelection touchpointSelection : sortedTouchpointSelections) {
                    TouchpointSelection clonedTouchpointSelection = CloneHelper.clone(touchpointSelection, ts -> ts.clone(this));

//                clonedTouchpointSelection.save();

                    // Variant Workflows
                    ConfigurableWorkflow variantWorkflow = CloneHelper.assign(touchpointSelection.getWorkflow());
                    ConfigurableWorkflow tsConnectedWorkflow = CloneHelper.assign(touchpointSelection.getConnectedWorkflow());

                    if (variantWorkflow != null) {
                        CloneHelper.execInSaveSession(() -> variantWorkflow.getWorkflowLibraryDocuments().add(this));
                        clonedTouchpointSelection.setWorkflow(variantWorkflow);
                    }

                    if (tsConnectedWorkflow != null) {
                        CloneHelper.execInSaveSession(() -> {
                            if (!tsConnectedWorkflow.getWorkflowLibraryDocuments().contains(this)) {
                                tsConnectedWorkflow.getWorkflowLibraryDocuments().add(this);
                            }
                        });
                        clonedTouchpointSelection.setConnectedWorkflow(tsConnectedWorkflow);
                    }

                    Set<ProofDefinition> clonedProofDefinitions = clonedTouchpointSelection.getProofDefinitions();
                    if (clonedProofDefinitions != null) {
                        for (ProofDefinition proofDefinition : clonedProofDefinitions) {
                            DataResource dataResource = proofDefinition.getDataResource();
                            if (dataResource != null) {
                                if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
                                    if (timestamp != null) {
                                        dataResource.cloneDna();
                                    }
                                    if (clonedDataResourcesMap.containsKey(dataResource.getId())) {
                                        proofDefinition.setDataResource(clonedDataResourcesMap.get(dataResource.getId()));
                                    }
                                }
                            } else {
                                proofDefinition.setDataResource(null);
                            }
                        }
                    }

//                clonedTouchpointSelection.save();

                    this.touchpointSelections.add(clonedTouchpointSelection);
                    if (clonedTouchpointSelection.getParameterGroupTreeNode() != null)
                        parentPGTreeNodeMap.put(clonedTouchpointSelection.getParameterGroupTreeNode().getParentNode(), clonedTouchpointSelection);
                }

                if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
                    MessagepointSelection.cloneParentRelationship(parentPGTreeNodeMap, null, null, timestamp);
                }
            }
        }

        this.selectionWorkflowTypeId = cloneFrom.selectionWorkflowTypeId;
        this.selectionVisibleByDefault = cloneFrom.selectionVisibleByDefault;

        if(isRoot) {
            if (cloneFrom.workflows != null) {
                this.workflows.addAll(CloneHelper.clone(cloneFrom.workflows, o -> o.clone(this)));
            }
        }

        if(isRoot) {
            this.languageParameterGroup = CloneHelper.assign(cloneFrom.languageParameterGroup);
        }

        if(isRoot) {
            parentPGTreeNodeMap.clear();
            if (cloneFrom.languageSelections != null) {
                List<LanguageSelection> sortedLanguageSelections = SyncTouchpointUtil.getSortedMessagepointSelectionList(cloneFrom.languageSelections);
                for (LanguageSelection languageSelection : sortedLanguageSelections) {
                    LanguageSelection clonedLanguageSelection = CloneHelper.clone(languageSelection, ls -> ls.clone(this));
                    if (timestamp != null) {
                        clonedLanguageSelection.cloneDna();
                    }
                    clonedLanguageSelection.save();
                    this.languageSelections.add(clonedLanguageSelection);
                    if (clonedLanguageSelection.getParameterGroupTreeNode() != null)
                        parentPGTreeNodeMap.put(clonedLanguageSelection.getParameterGroupTreeNode().getParentNode(), clonedLanguageSelection);
                }

                if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
                    MessagepointSelection.cloneParentRelationship(parentPGTreeNodeMap, null, null, timestamp);
                }
            }
        }

        if(this.getParent() == null) {
            if (cloneFrom.connectorConfiguration != null) {
                this.connectorConfiguration = CloneHelper.clone(cloneFrom.connectorConfiguration, cc -> cc.clone(this));
                this.connectorConfiguration.save();
            }
        }

        this.connectorConfigWrapper = new ConnecterConfigurationWrapper();

        this.processUsingCombinedContent = cloneFrom.processUsingCombinedContent;

        if(isRoot) {
            if (cloneFrom.tenantEmailAddresses != null) {
                for (DocumentTenantEmailAddress tenantEmailAddresses : cloneFrom.tenantEmailAddresses) {
                    this.tenantEmailAddresses.add(CloneHelper.clone(tenantEmailAddresses, tea -> tea.clone(this)));
                }
            }
        }

        if(isRoot) {
            TouchpointLanguage defaultLanguage = cloneFrom.getDefaultTouchpointLanguage();
            if (cloneFrom.touchpointLanguages != null) {
                for (TouchpointLanguage tpLanguage : cloneFrom.touchpointLanguages) {
                    if (tpLanguage.getId() != defaultLanguage.getId()) {
                        if (messagepointLocales != null) {
                            if (!messagepointLocales.contains(tpLanguage.getMessagepointLocale().getId())) {
                                continue;
                            }
                        }
                    }

                    this.touchpointLanguages.add(CloneHelper.clone(tpLanguage, tpl -> {
                        TouchpointLanguage clone = tpl.clone(this);
//				    clone.save();
                        if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() != null) {
                            clone.save();
                        }
                        return clone;
                    }));
                }
            }
        }

        this.fiscalYearStartMonth = cloneFrom.fiscalYearStartMonth;

        if(isRoot) {
            this.customerReportingVariableA = CloneHelper.assign(cloneFrom.customerReportingVariableA);
            this.customerReportingVariableB = CloneHelper.assign(cloneFrom.customerReportingVariableB);
        }

		if (cloneFrom.masterTemplateModifiers != null) {
			for (TemplateModifier templateModifier : cloneFrom.masterTemplateModifiers) {
                TouchpointSelection touchpointSelection = templateModifier.getTouchpointSelection();
                TouchpointSelection referencingTouchpointSelection = templateModifier.getReferencingTouchpointSelection();
                TouchpointSelection clonedTouchpointSelection = CloneHelper.getAlreadyClonedObject(touchpointSelection);
                TouchpointSelection clonedReferencingTouchpointSelection = CloneHelper.getAlreadyClonedObject(referencingTouchpointSelection);
                TemplateModifier targetTemplateModifier = CloneHelper.clone(templateModifier, tm->{
                    TemplateModifier clone = tm.clone(this, clonedTouchpointSelection, clonedReferencingTouchpointSelection);
                    return clone;
                });
                CloneHelper.execInSaveSession(()->{
                    if(targetTemplateModifier.getComplexValue()!=null){
                        targetTemplateModifier.getComplexValue().save();
                    }
                    targetTemplateModifier.save();
                    this.masterTemplateModifiers.add(targetTemplateModifier);
                });
			}
		}

        if(isRoot) {
            if (cloneFrom.templateVariants != null) {
                for (TemplateVariant templateVariant : cloneFrom.templateVariants) {
                    TemplateVariant clonedTemplateVariant = CloneHelper.clone(templateVariant, tv -> {
                        TemplateVariant clone = tv.clone(this);
                        return clone;
                    });
                    CloneHelper.execInSaveSession(() -> {
                        clonedTemplateVariant.save();
                        this.templateVariants.add(clonedTemplateVariant);
                    });
                }
            }
        }

       if(isRoot) {
            if (cloneFrom.communicationsDataResource != null) {
                if (clonedDataResourcesMap.containsKey(cloneFrom.communicationsDataResource.getId())) {
                    this.communicationsDataResource = clonedDataResourcesMap.get(cloneFrom.communicationsDataResource.getId());
                } else {
                    this.communicationsDataResource = CloneHelper.clone(cloneFrom.communicationsDataResource,
                        cdr -> {
                            DataResource clone = cdr.clone(this);
                            clone.save();
                            return clone;
                        }
                    );
                }
            }
        }

        if(isRoot) {
            this.communicationZoneMarkerStyle = CloneHelper.assign(cloneFrom.communicationZoneMarkerStyle);
        }

        if(isRoot) {
            if (cloneFrom.communicationOrderEntryItemDefinitions != null) {
                for (CommunicationOrderEntryItemDefinition object : cloneFrom.communicationOrderEntryItemDefinitions) {
                    this.communicationOrderEntryItemDefinitions.add(CloneHelper.clone(object, coeid -> {
                        CommunicationOrderEntryItemDefinition clone = coeid.clone(this);
                        clone.save();
                        return clone;
                    }));
                }
            }
        }

        this.communicationOrderEntryEnabled = cloneFrom.communicationOrderEntryEnabled;

        if(isRoot) {
            if (cloneFrom.communicationCompositionResultsWebService != null) {
                this.communicationCompositionResultsWebService = CloneHelper.clone(cloneFrom.communicationCompositionResultsWebService);
                this.communicationCompositionResultsWebService.save();
            }
        }

        if(isRoot) {
            if (cloneFrom.communicationProductionStatusWebService != null) {
                this.communicationProductionStatusWebService = CloneHelper.clone(cloneFrom.communicationProductionStatusWebService);
                this.communicationProductionStatusWebService.save();
            }
        }

        if(isRoot) {
            if (cloneFrom.communicationDataFeedWebService != null) {
                this.communicationDataFeedWebService = CloneHelper.clone(cloneFrom.communicationDataFeedWebService);
                this.communicationDataFeedWebService.save();
            }
        }

        if(isRoot) {
            if (cloneFrom.communicationNotificationWebService != null) {
                this.communicationNotificationWebService = CloneHelper.clone(cloneFrom.communicationNotificationWebService);
                this.communicationNotificationWebService.save();
            }
        }

        this.communicationProductionTypeId = cloneFrom.communicationProductionTypeId;

        if(isRoot) {
            this.communicationMultiRecipientIdentifier = CloneHelper.assign(cloneFrom.communicationMultiRecipientIdentifier,
                o -> {
                    DataElementVariable clone = (DataElementVariable) o.clone();
                    clone.save();
                    return clone;
                });
        }

		this.communicationExternalValidationEnabled     = cloneFrom.communicationExternalValidationEnabled;
		this.communicationAppliesTagCloud 				= cloneFrom.communicationAppliesTagCloud;
		this.communicationAppliesCopiesInput 			= cloneFrom.communicationAppliesCopiesInput;

		this.communicationDisplayTouchpointThumbnail    = cloneFrom.communicationDisplayTouchpointThumbnail;
		this.communicationAppliesTouchpointSelection    = cloneFrom.communicationAppliesTouchpointSelection;
		this.communicationZoneContentTransient          = cloneFrom.communicationZoneContentTransient;

		this.communicationForcedProofingDriverValue		= cloneFrom.communicationForcedProofingDriverValue;
		this.communicationResolveVariableValues			= cloneFrom.communicationResolveVariableValues;
		this.communicationSuppressNonEditableZones		= cloneFrom.communicationSuppressNonEditableZones;
		this.communicationPDFConversionQuality			= cloneFrom.communicationPDFConversionQuality;
		this.communicationSkipInteractiveWhenNoZones	= cloneFrom.communicationSkipInteractiveWhenNoZones;
		this.communicationUseBeta						= cloneFrom.communicationUseBeta;
        this.communicationFastOrderIndicatorEdit        = cloneFrom.communicationFastOrderIndicatorEdit;

		// SEGMENTATION ANALYSIS
		this.segmentationAnalysisEnabled = cloneFrom.segmentationAnalysisEnabled;

        if(isRoot) {
            this.segmentationAnalysisResource = CloneHelper.assign(cloneFrom.segmentationAnalysisResource,
                o -> {
                    DataResource clone = (DataResource) o.clone(this);
                    clone.save();
                    return clone;
                }
            );
        }

		// METADATA
        if(isRoot) {
            this.copyTouchpointMetadata(cloneFrom);
            this.copyVariantMetadataTemplateSetting(cloneFrom);
        }

		this.useConnectorAsZoneId = cloneFrom.useConnectorAsZoneId;

		this.stripoEnabled = cloneFrom.stripoEnabled;
		
    	cloneFrom.setBreakInheritance(breakInheritance);
	}

	public void copyGeneralAttributes(Document cloneFrom) {
        this.metatags = cloneFrom.metatags;
        this.externalId = cloneFrom.externalId;
        this.description = cloneFrom.description;
        this.sequence = cloneFrom.sequence;
/*
        this.connectorName = cloneFrom.connectorName;
        this.insertParameterGroup = CloneHelper.assign(cloneFrom.insertParameterGroup);
        this.firstPageWeight = cloneFrom.firstPageWeight;
        this.otherPagesWeight = cloneFrom.otherPagesWeight;
        this.defaultRateScheduleCollection = CloneHelper.assign(cloneFrom.defaultRateScheduleCollection);

        this.selectionParameterGroup = CloneHelper.assign(cloneFrom.selectionParameterGroup);
        this.selectionWorkflowTypeId = cloneFrom.selectionWorkflowTypeId;
        this.selectionVisibleByDefault = cloneFrom.selectionVisibleByDefault;
        this.processUsingCombinedContent = cloneFrom.processUsingCombinedContent;
*/
    }

    public void copyStyles(Document cloneFrom) {
        copyStyles(cloneFrom, false);
    }

    public void copyStyles(Document cloneFrom, boolean notSyncLayoutChanges) {
        if(! notSyncLayoutChanges) {
            this.defaultTextStyle = CloneHelper.assign(cloneFrom.defaultTextStyle);
            this.defaultParagraphStyle = CloneHelper.assign(cloneFrom.defaultParagraphStyle);
            this.defaultListStyle = CloneHelper.assign(cloneFrom.defaultListStyle);
        }
        else {
            CloneHelper.assign(cloneFrom.defaultTextStyle);
            CloneHelper.assign(cloneFrom.defaultParagraphStyle);
            CloneHelper.assign(cloneFrom.defaultListStyle);
        }

        List<TextStyleCustomization> textStyleCustomizations = TextStyleCustomization.findByDocument(cloneFrom);
        for (TextStyleCustomization sourceTextStyleCustomization : textStyleCustomizations) {
            TextStyle sourceTextStyle = sourceTextStyleCustomization.getMasterTextStyle();
            TextStyle targetTextStyle = CloneHelper.assignAlreadyClonedObject(sourceTextStyle);
            if(targetTextStyle != null) {
                TextStyleCustomization targetTextStyleCustomization = CloneHelper.queryInSaveSession(()->
                    targetTextStyle.getTextStyleCustomizations().entrySet()
                        .stream()
                        .filter(e->e.getKey().getId() == id)
                        .map(e->e.getValue())
                        .findFirst()
                        .orElse(null));

                if(targetTextStyleCustomization != null) {
                    if(! notSyncLayoutChanges) {
                        targetTextStyleCustomization.copyData(sourceTextStyleCustomization);
                        CloneHelper.execInSaveSession(() -> {
                            targetTextStyleCustomization.save();
                        });
                    }
                }
                else {
                    TextStyleCustomization clonedTextStyleCustomization = CloneHelper.clone(sourceTextStyleCustomization, o -> o.clone(targetTextStyle));
                    CloneHelper.execInSaveSession(() -> {
                        targetTextStyle.getTextStyleCustomizations().put(this, clonedTextStyleCustomization);
                        targetTextStyle.save();
//                        clonedTextStyleCustomization.save();
                    });
                }
            }
        }

        List<ListStyleCustomization> listStyleCustomizations = ListStyleCustomization.findByDocument(cloneFrom);
        for (ListStyleCustomization sourceListStyleCustomization : listStyleCustomizations) {
            ListStyle sourceListStyle = sourceListStyleCustomization.getMasterListStyle();
            ListStyle targetListStyle = CloneHelper.assignAlreadyClonedObject(sourceListStyle);
            if(targetListStyle != null) {
                ListStyleCustomization targetListStyleCustomization = CloneHelper.queryInSaveSession(()->
                    targetListStyle.getListStyleCustomizations().entrySet().stream()
                        .filter(e->e.getKey().getId() == id)
                        .map(e->e.getValue())
                        .findFirst()
                        .orElse(null));
                if(targetListStyleCustomization != null) {
                    if(! notSyncLayoutChanges) {
                        targetListStyleCustomization.copyData(sourceListStyleCustomization);
                        CloneHelper.execInSaveSession(() -> {
                            targetListStyleCustomization.save();
                        });
                    }
                }
                else {
                    ListStyleCustomization clonedListStyleCustomization = CloneHelper.clone(sourceListStyleCustomization, o -> o.clone(targetListStyle));
                    CloneHelper.execInSaveSession(() -> {
                        targetListStyle.getListStyleCustomizations().put(this, clonedListStyleCustomization);
                        targetListStyle.save();
//                        clonedListStyleCustomization.save();
                    });
                }
            }
        }

        List<ParagraphStyleCustomization> paragraphStyleCustomizations = ParagraphStyleCustomization.findByDocument(cloneFrom);
        for (ParagraphStyleCustomization sourceParagraphStyleCustomization : paragraphStyleCustomizations) {
            ParagraphStyle sourceParagraphStyle = sourceParagraphStyleCustomization.getMasterParagraphStyle();
            ParagraphStyle targetParagraphStyle = CloneHelper.assignAlreadyClonedObject(sourceParagraphStyle);
            if(targetParagraphStyle != null) {
                ParagraphStyleCustomization targetParagraphStyleCustomization = CloneHelper.queryInSaveSession(()->
                    targetParagraphStyle.getParagraphStyleCustomizations().entrySet().stream()
                        .filter(e->e.getKey().getId() == id)
                        .map(e->e.getValue())
                        .findFirst()
                        .orElse(null)
                );
                if(targetParagraphStyleCustomization != null) {
                    if(! notSyncLayoutChanges) {
                        targetParagraphStyleCustomization.copyData(sourceParagraphStyleCustomization);
                        CloneHelper.execInSaveSession(() -> {
                            targetParagraphStyleCustomization.save();
                        });
                    }
                }
                else {
                    ParagraphStyleCustomization clonedParagraphStyleCustomization = CloneHelper.clone(sourceParagraphStyleCustomization, o -> o.clone(targetParagraphStyle));
                    CloneHelper.execInSaveSession(() -> {
                        targetParagraphStyle.getParagraphStyleCustomizations().put(this, clonedParagraphStyleCustomization);
                        targetParagraphStyle.save();
//                        clonedParagraphStyleCustomization.save();
                    });
                }
            }
        }

    }

    public void copySFTP(Document cloneFrom) {
        // SFTP
        this.sftpIPAddress = cloneFrom.sftpIPAddress;
        this.sftpFolderPath = cloneFrom.sftpFolderPath;
        this.sftpUsername = cloneFrom.sftpUsername;
        this.sftpPassword = cloneFrom.sftpPassword;
        this.sftpSSHKey = cloneFrom.sftpSSHKey;
        this.privateKey = cloneFrom.privateKey;

    }

    public Map<String, Object> getSFTPSettingsMap() {
        Map<String, Object> settingsMap = new HashMap<>();
        return settingsMap;
    }

    public Map<String, Object> getTouchpointMetadataSettingsMap() {
        Map<String, Object> attributesMap = new LinkedHashMap<>();

        if(getTouchpointMetadataFormDefinition() != null) {
            attributesMap.put("page.label.touchpoint.metadata.template.name", getTouchpointMetadataFormDefinition().getName());
            attributesMap.put("page.label.touchpoint.metadata.template.guid", getTouchpointMetadataFormDefinition().getGuid());
        }
        if(getMetadataForm() != null) {
            attributesMap.putAll(getMetadataForm().getAttributesMap());
        }

        return attributesMap;
    }

    public void copyTouchpointMetadata(Document cloneFrom) {
        this.touchpointMetadataFormDefinition = CloneHelper.assign(cloneFrom.touchpointMetadataFormDefinition,
            o -> {
                MetadataFormDefinition clone = (MetadataFormDefinition) o.clone();
                clone.save();
                return clone;
            }
        );

        if (cloneFrom.metadataForm != null) {
            this.metadataForm = CloneHelper.clone(cloneFrom.metadataForm, o -> {
                MetadataForm clone = o.clone(true);
                clone.save();
                return clone;
            });
        }
    }

    public Map<String, Object> getVariantMetadataSettingsMap() {
        Map<String, Object> attributesMap = new LinkedHashMap<>();

        if(getVariantMetadataFormDefinition() != null) {
            attributesMap.put("page.label.variant.metadata.template.name", getVariantMetadataFormDefinition().getName());
            attributesMap.put("page.label.variant.metadata.template.guid", getVariantMetadataFormDefinition().getGuid());
        }

        if(isMaintainVariantHierarchy()) {
            attributesMap.put("page.label.maintain.variant.hierarchy", isMaintainVariantHierarchy());
        }

        return attributesMap;
    }

    public void copyVariantMetadataTemplateSetting(Document cloneFrom) {
        this.maintainVariantHierarchy = cloneFrom.maintainVariantHierarchy;

        this.variantMetadataFormDefinition = CloneHelper.assign(cloneFrom.variantMetadataFormDefinition,
            o -> {
                MetadataFormDefinition clone = (MetadataFormDefinition) o.clone();
                clone.save();
                return clone;
            }
        );
    }


    private String getConnectedInterviewOrderItemFullPath(CommunicationOrderEntryItemDefinition item, Map<Integer,  CommunicationOrderEntryItemDefinition> orderToItemMap, Map<CommunicationOrderEntryItemDefinition, Integer> actualOrderMap) {
	    String parentPath = "";
	    if(item.getParentItemOrder() != null && item.getParentItemOrder() != 0) {
            CommunicationOrderEntryItemDefinition parent = orderToItemMap.get(item.getParentItemOrder());
            parentPath = getConnectedInterviewOrderItemFullPath(parent, orderToItemMap, actualOrderMap) + " / ";
        }
	    return parentPath + ApplicationUtil.getMessage("page.label.communication") + " " + actualOrderMap.get(item);
    }

    public Map<String, Object> getConnectedInterviewSettingsMap() {
        Map<String, Object> attributesMap = new LinkedHashMap<>();

        if(isConnectedEnabled()) {
            if(getDataSourceAssociation() != null) {
                for(ReferenceConnection referenceConnection : getDataSourceAssociation().getReferenceConnections()) {
                    if(referenceConnection.getReferenceDataSource() != null && referenceConnection.getReferenceDataSource().getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED) {
                        DataElementVariable primaryVariable = referenceConnection.getPrimaryVariable();
                        if(primaryVariable != null) {
                            attributesMap.put("page.label.attribute.connected.interview.primary.variable",  					"" + primaryVariable.getName());
                            attributesMap.put("page.label.attribute.connected.interview.primary.variable.dna",  					"" + primaryVariable.getDna());
                        }
                        DataElementVariable referenceVariableVariable = referenceConnection.getReferenceVariable();
                        if(referenceVariableVariable != null) {
                            attributesMap.put("page.label.attribute.connected.interview.reference.variable",  					"" + referenceVariableVariable.getName());
                            attributesMap.put("page.label.attribute.connected.interview.reference.variable.dna",  					"" + referenceVariableVariable.getDna());
                        }
                    }
                }
            }
        }

        attributesMap.put("page.label.attribute.connected.interview.enabled",  					"" + communicationOrderEntryEnabled);
        attributesMap.put("page.label.attribute.connected.interview.variant.selection",  		"" + communicationAppliesTouchpointSelection);

        List<CommunicationOrderEntryItemDefinition> communicationOrderEntryItemDefinitionsInOrder = getCommunicationOrderEntryItemDefinitionsInOrder();

        Map<Integer,  CommunicationOrderEntryItemDefinition> orderToItemMap =
                communicationOrderEntryItemDefinitionsInOrder.stream().collect(Collectors.toMap(CommunicationOrderEntryItemDefinition::getOrder, Function.identity()));

        Map<CommunicationOrderEntryItemDefinition, List<CommunicationOrderEntryItemDefinition>> parentChildMap = new LinkedHashMap<>();
        Map<CommunicationOrderEntryItemDefinition, Integer> actualOrderMap = new HashMap<>();

        int rootOrder = 0;

        for(CommunicationOrderEntryItemDefinition item : communicationOrderEntryItemDefinitionsInOrder) {
            int actualOrder = 0;
            if(item.getParentItemOrder() != null) {
                CommunicationOrderEntryItemDefinition parent = orderToItemMap.get(item.getParentItemOrder());
                if(parent != null) {
                    List<CommunicationOrderEntryItemDefinition> sibList = parentChildMap.get(parent);
                    if(sibList == null) {
                        sibList = new ArrayList<>();
                        parentChildMap.put(parent, sibList);
                    }
                    sibList.add(item);
                    actualOrder = sibList.size();
                }
            }

            if(actualOrder == 0) {
                ++ rootOrder;
                actualOrder = rootOrder;
            }

            actualOrderMap.put(item, actualOrder);
        }

        Map<String, Object> orderTreeData = new LinkedHashMap<>();
        for(CommunicationOrderEntryItemDefinition itemDef: communicationOrderEntryItemDefinitionsInOrder) {
            Map<String, Object> itemAttributesMap = new LinkedHashMap<>();
            itemAttributesMap.put("page.label.attribute.name", itemDef.getName());
            if(itemDef.getDescription() != null) {
                itemAttributesMap.put("page.label.attribute.description",   itemDef.getDescription());
            }

            itemAttributesMap.put("page.label.attribute.is.indicator",      itemDef.getIsIndicatorEntry() ? "T" : "F");
            itemAttributesMap.put("page.label.attribute.is.primary",        itemDef.getIsPrimaryDriverEntry() ? "T" : "F");
            itemAttributesMap.put("page.label.attribute.is.mandatory",      itemDef.getIsManadatory() ? "T" : "F");
            itemAttributesMap.put("page.label.attribute.type",              new MetadataFormItemType(itemDef.getTypeId()).getDisplayText());

            if(itemDef.getDataElementVariable() != null) {
                itemAttributesMap.put("page.label.attribute.data.variable.name", itemDef.getDataElementVariable().getName());
            }

            itemAttributesMap.put("page.label.attribute.connector",         itemDef.getPrimaryConnector());

/*
            if(itemDef.getParentItemOrder() != null) {
                CommunicationOrderEntryItemDefinition parentItem = communicationOrderEntryItemDefinitionsInOrder.stream().filter(o->o.getOrder() == itemDef.getParentItemOrder()).findFirst().orElse(null);
                if(parentItem != null) {
                    itemAttributesMap.put("page.label.attribute.parent.item", parentItem.getName() + " @(" + parentItem.getOrder() + ")");
                }
            }
 */

            if(itemDef.getTypeId() == MetadataFormItemType.ID_SELECT_MENU
            || itemDef.getTypeId() == MetadataFormItemType.ID_WEB_SERVICE_MENU
            || itemDef.getTypeId() == MetadataFormItemType.ID_MULTISELECT_MENU
            || itemDef.getTypeId() == MetadataFormItemType.ID_WEB_SERVICE_MULTISELECT_MENU )
            {
                if(itemDef.getMenuValueItems() != null) {
                    itemAttributesMap.put("page.label.attribute.menu.items",         itemDef.getMenuValueItems());
                }
            }

            if(itemDef.getDisplayTriggerValues() != null) {
                itemAttributesMap.put("page.label.attribute.display.criteria",      itemDef.getDisplayTriggerValues());
            }

            if(itemDef.getDefaultInputValue() != null) {
                itemAttributesMap.put("page.label.attribute.default.value",         itemDef.getDefaultInputValue());
            }

            itemAttributesMap.put("page.label.attribute.data.privacy",              new CommunicationDataPrivacyType(itemDef.getDataPrivacyTypeId()).getDisplayText());
            itemAttributesMap.put("page.label.attribute.field.size",                new MetadataFormFieldSizeType(itemDef.getFieldSizeTypeId()).getDisplayText());
            if(itemDef.getFieldMaxLength() != null) {
                itemAttributesMap.put("page.label.attribute.field.max.length",      "" + itemDef.getFieldMaxLength());
            }
            itemAttributesMap.put("page.label.attribute.unique.value",              itemDef.isUniqueValue() ? "T" : "F");

            if(itemDef.getInputValidationTypeId() != null) {
                itemAttributesMap.put("page.label.attribute.input.validation",      new MetadataFormInputValidationType(itemDef.getInputValidationTypeId()).getDisplayText());
            }

            if(itemDef.getApplicableSelections() != null) {
                Set<TouchpointSelection> applicableSelections = itemDef.getApplicableSelections();
                if(applicableSelections != null) {
                    List<String> applicableSelectionPaths = applicableSelections.stream().map(ts->ts.getParameterGroupTreeNode().getDna() + " - " + ParameterGroupTreeNode.getFullPath(ts.getParameterGroupTreeNode())).collect(Collectors.toList());
                    Collections.sort(applicableSelectionPaths);
                    String applicableSelectionPathsString = applicableSelectionPaths.stream().sequential().collect(Collectors.joining(","));
                    itemAttributesMap.put("page.label.attribute.applicable.variants", applicableSelectionPathsString);
                }
            }

            String fullPath = getConnectedInterviewOrderItemFullPath(itemDef, orderToItemMap, actualOrderMap);
            orderTreeData.put(fullPath, itemAttributesMap);
        }

        if(orderTreeData != null && ! orderTreeData.isEmpty()) {
            attributesMap.put("page.label.attribute.connected.interview.orders", orderTreeData);
        }
        return attributesMap;
    }

	public void copyConnectedInterviewData(Document cloneFrom) {
        CloneHelper.execInSaveSession(()->{
            for (CommunicationOrderEntryItemDefinition object : this.communicationOrderEntryItemDefinitions) {
                object.delete();
            }
            this.communicationOrderEntryItemDefinitions.clear();
        });

        if (cloneFrom.communicationOrderEntryItemDefinitions != null) {
            List<CommunicationOrderEntryItemDefinition> sourceCommunicationOrderEntryItemDefinitions = new ArrayList<>(cloneFrom.getCommunicationOrderEntryItemDefinitions());
            Collections.sort(sourceCommunicationOrderEntryItemDefinitions, (c1, c2) -> c1.getOrder() - c2.getOrder());
            for (CommunicationOrderEntryItemDefinition object : sourceCommunicationOrderEntryItemDefinitions) {
                CommunicationOrderEntryItemDefinition clonedObject = CloneHelper.clone(object, coeid->{
                    CommunicationOrderEntryItemDefinition clone = coeid.clone(this);
                    clone.save();
                    return clone;
                });

                CloneHelper.execInSaveSession(()->{
                    this.communicationOrderEntryItemDefinitions.add(clonedObject);
                });
            }
        }

        this.communicationOrderEntryEnabled 			= cloneFrom.communicationOrderEntryEnabled;

        this.communicationAppliesTouchpointSelection    = cloneFrom.communicationAppliesTouchpointSelection;
    }

    private static void getLayoutTemplateAttributesMap(Document document, Map<String, Object> attributesMap) {
	    if(document.isEmailTouchpoint() || document.isWebTouchpoint()) {
	        String channelName = document.getConnectorConfiguration().getChannel().getPresentationName();
	        Map<String, Object> attrMap = (Map<String, Object>) attributesMap.get(channelName);

	        if(attrMap == null) {
	            attrMap = new HashMap<>();
                attributesMap.put(channelName, attrMap);
            }

            {
                String name = document.getName();
                String templatePath = EmailTemplateUtils.getTemplatePackagePath(document.getId());
                String templateFileHash = SyncTouchpointUtil.calculateFileHash(templatePath);
                if(templateFileHash != null) {
                    Path p = Paths.get(templatePath);
                    String fileName = p.getFileName().toString();
                    Map<String, String> templateFileAttrMaps = new LinkedHashMap<>();
                    templateFileAttrMaps.put("page.label.file.name", fileName);
                    templateFileAttrMaps.put("page.label.attribute.content.hash", templateFileHash);
                    String touchpointOrLayout = (document.getParent() == null && document.getChannelParent() == null) ? "page.label.touchpoint" : "page.label.layout";
                    templateFileAttrMaps.put(touchpointOrLayout, name);
                    attrMap.put(touchpointOrLayout + "|" + document.getDna(), templateFileAttrMaps);
                }
            }

	        List<Document> alternateLayouts = document.getAlternateLayouts();
            for(Document layout : alternateLayouts) {
                String name = layout.getName();
                String templatePath = EmailTemplateUtils.getTemplatePackagePath(layout.getId());
                String templateFileHash = SyncTouchpointUtil.calculateFileHash(templatePath);
                if(templateFileHash != null) {
                    Path p = Paths.get(templatePath);
                    String fileName = p.getFileName().toString();
                    Map<String, String> templateFileAttrMaps = new LinkedHashMap<>();
                    templateFileAttrMaps.put("page.label.file.name", fileName);
                    templateFileAttrMaps.put("page.label.attribute.content.hash", templateFileHash);
                    String touchpointOrLayout = "page.label.layout";
                    templateFileAttrMaps.put(touchpointOrLayout, name);
                    attrMap.put(touchpointOrLayout + "|" + layout.getDna(), templateFileAttrMaps);
                }
            }
        }
    }

    public Map<String, Object> getLayoutTemplateAttributesMap() {
        Map<String, Object> attributesMap = new HashMap<>();
        Document rootDocument = getRootDocument();
        getLayoutTemplateAttributesMap(rootDocument, attributesMap);
        List<Document> channelLayouts = rootDocument.getChannelAlternateDocuments();
        for(Document channelLayout : channelLayouts) {
            getLayoutTemplateAttributesMap(channelLayout, attributesMap);
        }
        return attributesMap;
    }

    private static void getSourceTemplateFiles(Document document, Map<String, String> allSourceTemplateFiles, boolean fetchingPackageFile) {
        if (document.isEmailTouchpoint() || document.isWebTouchpoint()) {
            {
                String dna = document.getDna();
                String templatePath = fetchingPackageFile ? EmailTemplateUtils.getTemplatePackagePath(document.getId()) : EmailTemplateUtils.getFilerootTemplatesBasePath(document.getId());
                allSourceTemplateFiles.put(dna, templatePath);
            }
            List<Document> alternateLayouts = document.getAlternateLayouts();
            for(Document layout : alternateLayouts) {
                String dna = layout.getDna();
                String templatePath = fetchingPackageFile ? EmailTemplateUtils.getTemplatePackagePath(layout.getId()) : EmailTemplateUtils.getFilerootTemplatesBasePath(layout.getId());
                allSourceTemplateFiles.put(dna, templatePath);
            }
        }
    }

    public String calculateLayoutTemplateHash() {
        Map<String, String> allSourceTemplatePaths = new HashMap<>();
        {
            Document sourceRootDocument = this.getRootDocument();
            getSourceTemplateFiles(sourceRootDocument, allSourceTemplatePaths, true);
            List<Document> channelLayouts = sourceRootDocument.getChannelAlternateDocuments();
            for (Document channelLayout : channelLayouts) {
                getSourceTemplateFiles(channelLayout, allSourceTemplatePaths, true);
            }
        }

	    List<String> dnaTemplateFileHashsList = allSourceTemplatePaths.entrySet().stream().map(entry->entry.getKey() + "=" + SyncTouchpointUtil.calculateFileHash(entry.getValue())).collect(Collectors.toList());
	    Collections.sort(dnaTemplateFileHashsList);

        String dnaPaths = dnaTemplateFileHashsList.stream().sequential().collect(Collectors.joining(","));
        return calculateSha256Hash(null, false, null, dnaPaths);
    }

    private static void copyTemplateFolder(Document document, String sourceTemplateFolder) {
        if(document.isWebTouchpoint() || document.isEmailTouchpoint()) {
            String templateFolder = EmailTemplateUtils.getFilerootTemplatesBasePath(document.getId());
            File sourceFolderFile = new File(sourceTemplateFolder);
            if(sourceFolderFile.exists()) {
                try {
                    com.prinova.migrate.util.FileUtil.copyDir(sourceFolderFile, new File(templateFolder));
                } catch (Exception e) {
                }
            }
        }
    }

    private static void copyTemplateFolders(Document document, Map<String, String> allSourceTemplatePaths) {
        if (document.isEmailTouchpoint() || document.isWebTouchpoint()) {
            {
                String dna = document.getDna();
                if(allSourceTemplatePaths.containsKey(dna)) {
                    String templatePath = allSourceTemplatePaths.get(dna);
                    copyTemplateFolder(document, templatePath);
                }
            }

            List<Document> alternateLayouts = document.getAlternateLayouts();
            for(Document layout : alternateLayouts) {
                String dna = layout.getDna();
                if(allSourceTemplatePaths.containsKey(dna)) {
                    String templatePath = allSourceTemplatePaths.get(dna);
                    copyTemplateFolder(layout, templatePath);
                }
            }
        }
    }

    public void copyChannelLayoutTemplates(Document cloneFrom) {
        Map<String, String> allSourceTemplatePaths = new HashMap<>();
        {
            Document sourceRootDocument = cloneFrom.getRootDocument();
            getSourceTemplateFiles(sourceRootDocument, allSourceTemplatePaths, false);
            List<Document> channelLayouts = sourceRootDocument.getChannelAlternateDocuments();
            for (Document channelLayout : channelLayouts) {
                getSourceTemplateFiles(channelLayout, allSourceTemplatePaths, false);
            }
        }

        CloneHelper.execInSaveSession(()->{
            Document rootDocument = getRootDocument();
            copyTemplateFolders(rootDocument, allSourceTemplatePaths);
            List<Document> channelLayouts = rootDocument.getChannelAlternateDocuments();
            for (Document channelLayout : channelLayouts) {
                copyTemplateFolders(channelLayout, allSourceTemplatePaths);
            }
        });
    }

    public String calculateTouchpointMetadataHash() {
        StringBuilder hashDataStringBuilder = new StringBuilder();
        hashDataStringBuilder.append("documentSetting.TouchpointMetadata");
        if(getTouchpointMetadataFormDefinition() != null) {
            hashDataStringBuilder.append(" touchpointMetadataTemplate: ").append(getTouchpointMetadataFormDefinition().getHashSafe());
        }

        if(getMetadataForm() != null) {
            hashDataStringBuilder.append(" metadataForm: ").append(getMetadataForm().getHashSafe());
        }

        return calculateSha256Hash(null, false, null, hashDataStringBuilder.toString());
    }

    public String calculateVariantMetadataSettingsHash() {
        StringBuilder hashDataStringBuilder = new StringBuilder();
        hashDataStringBuilder.append("documentSetting.VariantMetadataTemplate");

        if(getVariantMetadataFormDefinition() != null) {
            hashDataStringBuilder.append(" variantMetadataTemplateName: ").append(getVariantMetadataFormDefinition().getName());
            hashDataStringBuilder.append(" variantMetadataTemplateGuid: ").append(getVariantMetadataFormDefinition().getGuid());
        }

        if(isMaintainVariantHierarchy()) {
            hashDataStringBuilder.append(" maintainVariantHierarchy: ").append(isMaintainVariantHierarchy());
        }

        return calculateSha256Hash(null, false, null, hashDataStringBuilder.toString());
    }

	@Override
	public Object clone()
	{
		return new Document(null, this, null, null, true, null);
	}

	public Document clone(Timestamp timestamp, Document primaryLayoutDocument, Document channelParentDocument, boolean cloneDataSourceAssociation, Set<Long> messagepointLocales)
	{
		Document clone = new Document(timestamp, this, primaryLayoutDocument, channelParentDocument, cloneDataSourceAssociation, messagepointLocales);
		clone.setDna(this.getDna());
        clone.setDictionaryIdentifier(this.getDictionaryIdentifier());
		clone.setCheckoutTimestamp(timestamp);
        if(timestamp == null) {
            clone.setOriginObject(null);
        }
		clone.setParentObject(primaryLayoutDocument);
        clone.setChannelParentObject(channelParentDocument);
		return clone;
	}

    public Document clone1(Timestamp timestamp, Document primaryLayoutDocument, Document channelParent, boolean cloneDataSourceAssociation, Set<Long> messagepointLocales)
    {
        Document clone = new Document(timestamp, this, primaryLayoutDocument, channelParent, cloneDataSourceAssociation, messagepointLocales);
        clone.setDna(this.getDna());
        clone.setCheckoutTimestamp(timestamp);
        if(timestamp == null) {
            clone.setOriginObject(null);
        }
        clone.setParentObject(primaryLayoutDocument);
        clone.setChannelParentObject(channelParent);
        return clone;
    }

    @Override
	public void preSave(Boolean isNew) {
		super.preSave(isNew);
		
		this.setBreakInheritance(true);
		
		for ( DocumentSection section: this.getDocumentSections() )
			section.setBreakInheritance(true);
		for ( Zone zone: this.getZones() )
			zone.setBreakInheritance(true);
		
		if (!getWorkflows().isEmpty()) {
			Set<ConfigurableWorkflow> wfs = getWorkflows();
			for ( ConfigurableWorkflow wf : wfs ) {
				if ( wf.getId() < 1L )
					wf.save();
			}
		}

        if ( this.privateKey == null || this.privateKey.isEmpty() ) {
            try {
                AesUtil aes = new AesUtil(256);
                String privateKey = aes.generateSecretKey();
                this.setPrivateKey(privateKey);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
		
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTagCloudService.SERVICE_NAME, UpdateTagCloudService.class);
		ServiceExecutionContext context = UpdateTagCloudService.createContext(TagCloudType.ID_TOUCHPOINT, this.getMetatags(), null, false);
		if ( service != null && context != null )
			service.execute(context);
	}
	@Override
	public void postSave(Boolean isNew) {
		super.postSave(isNew);
		//this.setBreakInheritance(false);
	}
	@Override
	public void preDelete() {
		super.preDelete();
		this.setBreakInheritance(true);
	}	

	
	// *** FIXED ATTRIBUTES ***
	public Document getParent() {
		if ( this.isBreakInheritance() || (this.getParentObject() != null && this.getParentObject().isBreakInheritance())  )
			return null;
		return (Document) this.getParentObject();
	}
	
	public Document getChannelParent() {
		if ( this.isBreakInheritance() )
			return null;
		return (Document) this.getChannelParentObject();
	}
	
	@Override
	public String getName() {
		if ( this.getChannelParent() == null ) {
			return this.name;
		}
		return this.getChannelParent().getName();
	}

	public Set<TemplateModifier> getMasterTemplateModifiers() {
		return masterTemplateModifiers;
	}
	public void setMasterTemplateModifiers(Set<TemplateModifier> argMasterTemplateModifiers) {
		this.masterTemplateModifiers = argMasterTemplateModifiers;
	}
	
	public String getExchangeInstanceGuid() {
		if ( this.getChannelParent() == null )
			return exchangeInstanceGuid;
		return this.getChannelParent().getExchangeInstanceGuid();
	}
	public void setExchangeInstanceGuid(String guid) {
		this.exchangeInstanceGuid = guid;
	}
	
	public String getExchangeTouchpointGuid() {
		if ( this.getChannelParent() == null )
			return exchangeTouchpointGuid;
		return this.getChannelParent().getExchangeTouchpointGuid();
	}
	public void setExchangeTouchpointGuid(String guid) {
		this.exchangeTouchpointGuid = guid;
	}
	
	public Timestamp getExchangePublishedTimestamp() {
		if ( this.getChannelParent() == null )
			return exchangePublishedTimestamp;
		return this.getChannelParent().getExchangePublishedTimestamp();
	}
	public void setExchangePublishedTimestamp(Timestamp exchangePublishedTimestamp) {
		this.exchangePublishedTimestamp = exchangePublishedTimestamp;
	}
	
	public Timestamp getExchangeUpdatedTimestamp() {
		if ( this.getChannelParent() == null )
			return exchangeUpdatedTimestamp;
		return this.getChannelParent().getExchangeUpdatedTimestamp();
	}
	public void setExchangeUpdatedTimestamp(Timestamp exchangeUpdatedTimestamp) {
		this.exchangeUpdatedTimestamp = exchangeUpdatedTimestamp;
	}

	public boolean isUseConnectorAsZoneId() {
		if ( this.getChannelParent() == null )
			return useConnectorAsZoneId;
		return this.getChannelParent().isUseConnectorAsZoneId();
	}
	public void setUseConnectorAsZoneId(boolean useConnectorAsZoneId) {
		this.useConnectorAsZoneId = useConnectorAsZoneId;
	}

	public boolean isPublished() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().isPublished();
	    	return exchangePublishedTimestamp != null;
		}
		return this.getChannelParent().isPublished();
	}
	public void setPublished(boolean published) {
		if (published)
			this.exchangePublishedTimestamp = new Timestamp(System.currentTimeMillis());
		else
			this.exchangePublishedTimestamp = null;
	}

    public boolean isTargetOfSync() {
        return targetOfSync;
    }

    public void setTargetOfSync(boolean targetOfSync) {
        this.targetOfSync = targetOfSync;
    }

    public boolean isAcceptOnlyActiveObjects() {
        return acceptOnlyActiveObjects;
    }

    public void setAcceptOnlyActiveObjects(boolean acceptOnlyActiveObjects) {
        this.acceptOnlyActiveObjects = acceptOnlyActiveObjects;
    }

    public boolean isActiveObjectsSyncThroughWorkflow() {
        return activeObjectsSyncThroughWorkflow;
    }

    public void setActiveObjectsSyncThroughWorkflow(boolean activeObjectsSyncThroughWorkflow) {
        this.activeObjectsSyncThroughWorkflow = activeObjectsSyncThroughWorkflow;
    }

    public Set<Zone> getZones() {
		return zones;
	}
	public void setZones(Set<Zone> zones) {
		this.zones = zones;
	}
	
	public Set<DocumentSection> getDocumentSections() {
		return documentSections;
	}
	public void setDocumentSections(Set<DocumentSection> documentSections) {
		this.documentSections = documentSections;
	}

	public void replaceDocumentSections(Set<DocumentSection> documentSections) {
		if (this.documentSections != null)
		{
			this.documentSections.clear();
			this.documentSections.addAll(documentSections);
		}
		else
			this.documentSections = documentSections;
	}

	public String getConnectorName() {
    	return connectorName;
	}
	public void setConnectorName(String connectorName) {
		this.connectorName = connectorName;
	}

	// *** NO OVERRIDE ATTRIBUTES ***
	public boolean isConnectedEnabled() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().isConnectedEnabled();
	    	return connectedEnabled;
		}
		return this.getChannelParent().isConnectedEnabled();
	}
	public void setConnectedEnabled(boolean connectedEnabled) {
		this.connectedEnabled = connectedEnabled;
	}

	public DataResource getCommunicationsDataResource() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getCommunicationsDataResource();
	    	return communicationsDataResource;
		}
		return this.getChannelParent().getCommunicationsDataResource();
	}
	public void setCommunicationsDataResource(
			DataResource communicationsDataResource) {
		this.communicationsDataResource = communicationsDataResource;
	}
	
	public ParagraphStyle getCommunicationZoneMarkerStyle() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getCommunicationZoneMarkerStyle();
	    	return communicationZoneMarkerStyle;
		}
		return this.getChannelParent().getCommunicationZoneMarkerStyle();
	}
	public void setCommunicationZoneMarkerStyle(
			ParagraphStyle communicationZoneMarkerStyle) {
		this.communicationZoneMarkerStyle = communicationZoneMarkerStyle;
	}
	
	public Set<CommunicationOrderEntryItemDefinition> getCommunicationOrderEntryItemDefinitions() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
                return  new HashSet<>(this.getParent().getCommunicationOrderEntryItemDefinitions());
			}
		} else {
            return new HashSet<>(this.getChannelParent().getCommunicationOrderEntryItemDefinitions());
		}

		return communicationOrderEntryItemDefinitions;
	}

	public void setCommunicationOrderEntryItemDefinitions(
			Set<CommunicationOrderEntryItemDefinition> communicationOrderEntryItemDefinitions) {
		this.communicationOrderEntryItemDefinitions = communicationOrderEntryItemDefinitions;
	}

	public boolean isCommunicationOrderEntryEnabled() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().isCommunicationOrderEntryEnabled();
	    	return communicationOrderEntryEnabled;
		}
		return this.getChannelParent().isCommunicationOrderEntryEnabled();
	}
	public void setCommunicationOrderEntryEnabled(
			boolean communicationOrderEntryEnabled) {
		this.communicationOrderEntryEnabled = communicationOrderEntryEnabled;
	}

	public WebServiceConfiguration getCommunicationCompositionResultsWebService() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getCommunicationCompositionResultsWebService();
	    	return communicationCompositionResultsWebService;
		}
		return this.getChannelParent().getCommunicationCompositionResultsWebService();
	}
	public void setCommunicationCompositionResultsWebService(
			WebServiceConfiguration communicationCompositionResultsWebService) {
		this.communicationCompositionResultsWebService = communicationCompositionResultsWebService;
	}

	public WebServiceConfiguration getCommunicationProductionStatusWebService() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getCommunicationProductionStatusWebService();
	    	return communicationProductionStatusWebService;
		}
		return this.getChannelParent().getCommunicationProductionStatusWebService();
	}
	public void setCommunicationProductionStatusWebService(
			WebServiceConfiguration communicationProductionStatusWebService) {
		this.communicationProductionStatusWebService = communicationProductionStatusWebService;
	}

	public WebServiceConfiguration getCommunicationNotificationWebService() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().getCommunicationNotificationWebService();
			return communicationNotificationWebService;
		}
		return this.getChannelParent().getCommunicationNotificationWebService();
	}
	public void setCommunicationNotificationWebService(
			WebServiceConfiguration communicationNotificationWebService) {
		this.communicationNotificationWebService = communicationNotificationWebService;
	}

	public WebServiceConfiguration getCommunicationDataFeedWebService() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().getCommunicationDataFeedWebService();
			return communicationDataFeedWebService;
		}
		return this.getChannelParent().getCommunicationDataFeedWebService();
	}
	public void setCommunicationDataFeedWebService(WebServiceConfiguration communicationDataFeedWebService) {
		this.communicationDataFeedWebService = communicationDataFeedWebService;
	}

	public int getCommunicationProductionTypeId() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getCommunicationProductionTypeId();
	    	return communicationProductionTypeId;
		}
		return this.getChannelParent().getCommunicationProductionTypeId();
	}
	public void setCommunicationProductionTypeId(int communicationProductionTypeId) {
		this.communicationProductionTypeId = communicationProductionTypeId;
	}

	public DataElementVariable getCommunicationMultiRecipientIdentifier() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getCommunicationMultiRecipientIdentifier();
	    	return communicationMultiRecipientIdentifier;
		}
		return this.getChannelParent().getCommunicationMultiRecipientIdentifier();
	}
	public void setCommunicationMultiRecipientIdentifier(
			DataElementVariable communicationMultiRecipientIdentifier) {
		this.communicationMultiRecipientIdentifier = communicationMultiRecipientIdentifier;
	}

	public boolean isCommunicationExternalValidationEnabled() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().isCommunicationExternalValidationEnabled();
			return communicationExternalValidationEnabled;
		}
		return this.getChannelParent().isCommunicationExternalValidationEnabled();
	}
	public void setCommunicationExternalValidationEnabled(boolean communicationExternalValidationEnabled) {
		this.communicationExternalValidationEnabled = communicationExternalValidationEnabled;
	}

	public boolean isCommunicationAppliesTagCloud() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().isCommunicationAppliesTagCloud();
			return communicationAppliesTagCloud;
		}
		return this.getChannelParent().isCommunicationAppliesTagCloud();
	}
	public void setCommunicationAppliesTagCloud(boolean communicationAppliesTagCloud) {
		this.communicationAppliesTagCloud = communicationAppliesTagCloud;
	}

	public boolean isCommunicationAppliesCopiesInput() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().isCommunicationAppliesCopiesInput();
			return communicationAppliesCopiesInput;
		}
		return this.getChannelParent().isCommunicationAppliesCopiesInput();
	}
	public void setCommunicationAppliesCopiesInput(boolean communicationAppliesCopiesInput) {
		this.communicationAppliesCopiesInput = communicationAppliesCopiesInput;
	}

	public boolean isCommunicationDisplayTouchpointThumbnail() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().isCommunicationDisplayTouchpointThumbnail();
			return communicationDisplayTouchpointThumbnail;
		}
		return this.getChannelParent().isCommunicationDisplayTouchpointThumbnail();
	}
	public void setCommunicationDisplayTouchpointThumbnail(boolean communicationDisplayTouchpointThumbnail) {
		this.communicationDisplayTouchpointThumbnail = communicationDisplayTouchpointThumbnail;
	}
	
	
	public boolean isCommunicationAppliesTouchpointSelection() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().isCommunicationAppliesTouchpointSelection();
			return communicationAppliesTouchpointSelection;
		}
		return this.getChannelParent().isCommunicationAppliesTouchpointSelection();
	}
	public void setCommunicationAppliesTouchpointSelection(boolean communicationAppliesTouchpointSelection) {
		this.communicationAppliesTouchpointSelection = communicationAppliesTouchpointSelection;
	}

	public String getCommunicationForcedProofingDriverValue() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().getCommunicationForcedProofingDriverValue();
			return communicationForcedProofingDriverValue;
		}
		return this.getChannelParent().getCommunicationForcedProofingDriverValue();
	}
	public void setCommunicationForcedProofingDriverValue(String communicationForcedProofingDriverValue) {
		this.communicationForcedProofingDriverValue = communicationForcedProofingDriverValue;
	}

	public boolean isCommunicationResolveVariableValues() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().isCommunicationResolveVariableValues();
			return communicationResolveVariableValues;
		}
		return this.getChannelParent().isCommunicationResolveVariableValues();
	}
	public void setCommunicationResolveVariableValues(boolean communicationResolveVariableValues) {
		this.communicationResolveVariableValues = communicationResolveVariableValues;
	}

	public boolean isCommunicationSuppressNonEditableZones() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().isCommunicationSuppressNonEditableZones();
			return communicationSuppressNonEditableZones;
		}
		return this.getChannelParent().isCommunicationSuppressNonEditableZones();
	}

	public void setCommunicationSuppressNonEditableZones(boolean communicationSuppressNonEditableZones) {
		this.communicationSuppressNonEditableZones = communicationSuppressNonEditableZones;
	}

	public Integer getCommunicationPDFConversionQuality() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().getCommunicationPDFConversionQuality();
			return communicationPDFConversionQuality;
		}
		return this.getChannelParent().getCommunicationPDFConversionQuality();
	}

	public void setCommunicationPDFConversionQuality(Integer communicationPDFConversionQuality) {
		this.communicationPDFConversionQuality = communicationPDFConversionQuality;
	}

	public boolean isCommunicationSkipInteractiveWhenNoZones() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().isCommunicationSkipInteractiveWhenNoZones();
			return communicationSkipInteractiveWhenNoZones;
		}
		return communicationSkipInteractiveWhenNoZones;
	}
	public void setCommunicationSkipInteractiveWhenNoZones(boolean communicationSkipInteractiveWhenNoZones) {
		this.communicationSkipInteractiveWhenNoZones = communicationSkipInteractiveWhenNoZones;
	}

	public boolean isCommunicationUseBeta() {
		if ( this.getChannelParent() == null ) {
				if ( this.getParent() != null )
						return this.getParent().isCommunicationUseBeta();
				return communicationUseBeta;
			}
		return communicationUseBeta;
	}

    public boolean isCommunicationFastOrderIndicatorEdit() {
        return communicationFastOrderIndicatorEdit;
    }

    public void setCommunicationFastOrderIndicatorEdit (boolean communicationFastOrderIndicatorEdit) {
        this.communicationFastOrderIndicatorEdit = communicationFastOrderIndicatorEdit;
    }

	public void setCommunicationUseBeta(boolean communicationUseBeta) {
		this.communicationUseBeta = communicationUseBeta;
	}

	public DataElementVariable getCustomerReportingVariableA(){
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getCustomerReportingVariableA();
	    	return customerReportingVariableA;
		}
		return this.getChannelParent().getCustomerReportingVariableA();
	}
	public void setCustomerReportingVariableA(DataElementVariable customerReportingVariableA){
		this.customerReportingVariableA = customerReportingVariableA;
	}

	public DataElementVariable getCustomerReportingVariableB(){
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getCustomerReportingVariableB();
	    	return customerReportingVariableB;
		}
		return this.getChannelParent().getCustomerReportingVariableB();
	}
	public void setCustomerReportingVariableB(DataElementVariable customerReportingVariableB){
		this.customerReportingVariableB = customerReportingVariableB;
	}
	
	public int getFiscalYearStartMonth() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getFiscalYearStartMonth();
	    	return fiscalYearStartMonth;
		}
		return this.getChannelParent().getFiscalYearStartMonth();
	}
	public void setFiscalYearStartMonth(int fiscalYearStartMonth) {
		this.fiscalYearStartMonth = fiscalYearStartMonth;
	}
	
	public Set<DocumentTenantEmailAddress> getTenantEmailAddresses(){
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
				tenantEmailAddresses.clear();
				tenantEmailAddresses.addAll(this.getParent().getTenantEmailAddresses());
			}
		} else {
			tenantEmailAddresses.clear();
			tenantEmailAddresses.addAll(this.getChannelParent().getTenantEmailAddresses());
		}
		return tenantEmailAddresses;
	}
	public void setTenantEmailAddresses(Set<DocumentTenantEmailAddress> tenantEmailAddresses){
		this.tenantEmailAddresses = tenantEmailAddresses;
	}
	
	public ConnecterConfigurationWrapper getConnectorConfigWrapper() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getConnectorConfigWrapper();
	    	return connectorConfigWrapper;
		}
		return this.getChannelParent().getConnectorConfigWrapper();
	}
	public void setConnectorConfigWrapper(
			ConnecterConfigurationWrapper connectorConfigWrapper) {
		this.connectorConfigWrapper = connectorConfigWrapper;
	}

	public boolean isProcessUsingCombinedContent() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().isProcessUsingCombinedContent();
			return processUsingCombinedContent;
		}
		return this.getChannelParent().isProcessUsingCombinedContent();
	}
	public void setProcessUsingCombinedContent(boolean processUsingCombinedContent) {
		this.processUsingCombinedContent = processUsingCombinedContent;
	}

	// Channel Local
	public ConnectorConfiguration getConnectorConfiguration() {
		if ( this.getParent() != null )
    		return this.getParent().getConnectorConfiguration();
    	return connectorConfiguration;
	}
	public void setConnectorConfiguration(ConnectorConfiguration connectorConfiguration) {
		this.connectorConfiguration = connectorConfiguration;
	}

	public Set<TemplateVariant> getTemplateVariants() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
				return new HashSet<>(this.getParent().getTemplateVariants());
			}
		} else {
            return new HashSet<>(this.getChannelParent().getTemplateVariants());
		}
		return templateVariants;
	}
	public void setTemplateVariants(Set<TemplateVariant> templateVariants) {
		this.templateVariants = templateVariants;
	}
	
	public String getDescription() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getDescription();
	    	return description;
		}
		return this.getChannelParent().getDescription();
	}

	public String getExternalId() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getExternalId();
	    	return externalId;
		}
		return this.getChannelParent().getExternalId();
	}
	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

	public DataGroup getDataGroup() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getDataGroup();
			return dataGroup;
		}
		return this.getChannelParent().getDataGroup();
	}
	public void setDataGroup(DataGroup dataGroup) {
		this.dataGroup = dataGroup;
	}

	public Set<DataFile> getDataFiles() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
				dataFiles.clear();
				dataFiles.addAll(this.getParent().getDataFiles());
			}
    	} else {
			dataFiles.clear();
			dataFiles.addAll(this.getChannelParent().getDataFiles());
    	}
		return dataFiles;	
	}
	public void setDataFiles(Set<DataFile> dataFiles) {
		this.dataFiles = dataFiles;
	}
	
	public Set<DataResource> getDataResources() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
                return new HashSet<>(this.getParent().getDataResources());
			}
		} else {
			return new HashSet<>(this.getChannelParent().getDataResources());
		}
		return dataResources;
	}
	public void setDataResources(Set<DataResource> dataResources) {
		this.dataResources = dataResources;
	}

	public DataSourceAssociation getDataSourceAssociation() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getDataSourceAssociation();
	    	return dataSourceAssociation;
		}
		return this.getChannelParent().getDataSourceAssociation();
	}
	public void setDataSourceAssociation(DataSourceAssociation dataSourceAssociation){
		this.dataSourceAssociation = dataSourceAssociation;
	}

	public boolean isTpContentChanged() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().isTpContentChanged();
			return tpContentChanged;
		}
		return this.getChannelParent().isTpContentChanged();
	}

	public void setTpContentChanged(boolean tpContentChanged) {
		this.tpContentChanged = tpContentChanged;
	}

	public boolean isEnabled() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().isEnabled();
	    	return enabled;
		}
		return this.getChannelParent().isEnabled();
	}
	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}

	public boolean isRemoved() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().isRemoved();
	    	return removed;
		}
		return this.getChannelParent().isRemoved();
	}
	public void setRemoved(boolean removed) {
		this.removed = removed;
		if (removed)
			this.enabled = false;
	}

	public Integer getSequence() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getSequence();
	    	return sequence;
		}
		return this.getChannelParent().getSequence();
	}
	public void setSequence(Integer sequence) {
		this.sequence = sequence;
	}

	public Set<Attachment> getAttachments() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
				return new HashSet<>(this.getParent().getAttachments());
			}
		} else {
            return new HashSet<>(this.getChannelParent().getAttachments());
		}

		return attachments;
	}
	public void setAttachments(Set<Attachment> attachments) {
		this.attachments = attachments;
	}

	public Set<DataElementVariable> getExtReportingDataVariables() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
				extReportingDataVariables.clear();
				extReportingDataVariables.addAll(this.getParent().getExtReportingDataVariables());
			}
		} else {
			extReportingDataVariables.clear();
			extReportingDataVariables.addAll(this.getChannelParent().getExtReportingDataVariables());
		}
		return extReportingDataVariables;
	}
	public void setExtReportingDataVariables(Set<DataElementVariable> extReportingDataVariables) {
		this.extReportingDataVariables = extReportingDataVariables;
	}
	
	public TextStyle getDefaultTextStyle() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getDefaultTextStyle();
	    	return defaultTextStyle;
		}
		return this.getChannelParent().getDefaultTextStyle();
	}
	public void setDefaultTextStyle(TextStyle defaultTextStyle) {
		this.defaultTextStyle = defaultTextStyle;
	}

	public ParagraphStyle getDefaultParagraphStyle() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getDefaultParagraphStyle();
	    	return defaultParagraphStyle;
		}
		return this.getChannelParent().getDefaultParagraphStyle();
	}
	public void setDefaultParagraphStyle(ParagraphStyle defaultParagraphStyle) {
		this.defaultParagraphStyle = defaultParagraphStyle;
	}
	
	public ListStyle getDefaultListStyle() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getDefaultListStyle();
	    	return defaultListStyle;
		}
		return this.getChannelParent().getDefaultListStyle();
	}
	public void setDefaultListStyle(ListStyle defaultListStyle) {
		this.defaultListStyle = defaultListStyle;
	}

	@Override
	public String getMetatags() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getMetatags();
	    	return metatags;
		}
		return this.getChannelParent().getMetatags();
	}

	public ParameterGroup getInsertParameterGroup() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getInsertParameterGroup();
	    	return insertParameterGroup;
		}
		return this.getChannelParent().getInsertParameterGroup();
	}
	public void setInsertParameterGroup(ParameterGroup insertParameterGroup) {
		this.insertParameterGroup = insertParameterGroup;
	}

	public Integer getFirstPageWeight() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getFirstPageWeight();
	    	return firstPageWeight;
		}
		return this.getChannelParent().getFirstPageWeight();
	}
	public void setFirstPageWeight(int firstPageWeight) {
		this.firstPageWeight = firstPageWeight;
	}
	
	public int getOtherPagesWeight() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getOtherPagesWeight();
	    	return otherPagesWeight;
		}
		return this.getChannelParent().getOtherPagesWeight();
	}
	public void setOtherPagesWeight(int otherPagesWeight) {
		this.otherPagesWeight = otherPagesWeight;
	}
	
	public RateScheduleCollection getDefaultRateScheduleCollection() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getDefaultRateScheduleCollection();
	    	return defaultRateScheduleCollection;
		}
		return this.getChannelParent().getDefaultRateScheduleCollection();
	}
	public void setDefaultRateScheduleCollection(
			RateScheduleCollection defaultRateScheduleCollection) {
		this.defaultRateScheduleCollection = defaultRateScheduleCollection;
	}

	public ParameterGroup getSelectionParameterGroup() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getSelectionParameterGroup();
			return selectionParameterGroup;
		}
    	return this.getChannelParent().getSelectionParameterGroup();
	}
	public void setSelectionParameterGroup(ParameterGroup selectionParameterGroup) {
		this.selectionParameterGroup = selectionParameterGroup;
	}
	
	public Set<TouchpointSelection> getTouchpointSelections() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
				touchpointSelections.clear();
				touchpointSelections.addAll(this.getParent().getTouchpointSelections());
			}
		} else {
			touchpointSelections.clear();
			touchpointSelections.addAll(this.getChannelParent().getTouchpointSelections());
		}
		return touchpointSelections;
	}
	public void setTouchpointSelections(Set<TouchpointSelection> touchpointSelections) {
		this.touchpointSelections = touchpointSelections;
	}

	public int getSelectionWorkflowTypeId() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getSelectionWorkflowTypeId();
	    	return selectionWorkflowTypeId;
		}
		return this.getChannelParent().getSelectionWorkflowTypeId();
	}
	public void setSelectionWorkflowTypeId(int selectionWorkflowTypeId) {
		this.selectionWorkflowTypeId = selectionWorkflowTypeId;
	}

	public boolean isSelectionVisibleByDefault() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().isSelectionVisibleByDefault();
	    	return selectionVisibleByDefault;
		}
		return this.getChannelParent().isSelectionVisibleByDefault();
	}
	public void setSelectionVisibleByDefault(boolean selectionVisibleByDefault) {
		this.selectionVisibleByDefault = selectionVisibleByDefault;
	}

	public TouchpointTargeting getTpTargeting() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getTpTargeting();
			return this.tpTargeting;
		}
		return this.getChannelParent().getTpTargeting();
	}
	public void setTpTargeting(TouchpointTargeting tpTargeting) {
		this.tpTargeting = tpTargeting;
	}
	
	public Set<TpCollectionTouchpointAssignment> getTpCollectionTouchpoints() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
				tpCollectionTouchpoints.clear();
				tpCollectionTouchpoints.addAll(this.getParent().getTpCollectionTouchpoints());
			}
		} else {
			tpCollectionTouchpoints.clear();
			tpCollectionTouchpoints.addAll(this.getChannelParent().getTpCollectionTouchpoints());
		}
		return tpCollectionTouchpoints;
	}
	public void setTpCollectionTouchpoints(Set<TpCollectionTouchpointAssignment> tpCollectionTouchpoints) {
		this.tpCollectionTouchpoints = tpCollectionTouchpoints;
	}
	
	public Set<LanguageSelection> getLanguageSelections() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
				languageSelections.clear();
				languageSelections.addAll(this.getParent().getLanguageSelections());
			}
		} else {
			languageSelections.clear();
			languageSelections.addAll(this.getChannelParent().getLanguageSelections());
		}
		return languageSelections;
	}
	
	public Set<LanguageSelection> getLanguageSelectionsForJob()
	{
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
				return this.getParent().getLanguageSelections();
			}
		} else {
			return this.getChannelParent().getLanguageSelections();
		}
		return languageSelections;		
	}
	
	public void setLanguageSelections(Set<LanguageSelection> languageSelections) {
		this.languageSelections = languageSelections;
	}

	public ParameterGroup getLanguageParameterGroup() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getLanguageParameterGroup();
			return languageParameterGroup;
		}
		return this.getChannelParent().getLanguageParameterGroup();
	}
	public void setLanguageParameterGroup(ParameterGroup languageParameterGroup) {
		this.languageParameterGroup = languageParameterGroup;
	}

    public boolean isVariedByTouchpointVariant() {
        return variedByTouchpointVariant;
    }

    public void setVariedByTouchpointVariant(boolean variedByTouchpointVariant) {
        this.variedByTouchpointVariant = variedByTouchpointVariant;
    }

    public ParameterGroup getFormattingParameterGroup() {
        return formattingParameterGroup;
    }

    public void setFormattingParameterGroup(ParameterGroup formattingParameterGroup) {
        this.formattingParameterGroup = formattingParameterGroup;
    }

    public Set<FormattingSelection> getFormattingSelections() {
        return formattingSelections;
    }

    public void setFormattingSelections(Set<FormattingSelection> formattingSelections) {
        this.formattingSelections = formattingSelections;
    }

    public Set<ConfigurableWorkflow> getWorkflows() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
				workflows.clear();
				workflows.addAll(this.getParent().getWorkflows());
			}
		} else {
			workflows.clear();
			workflows.addAll(this.getChannelParent().getWorkflows());
		}
		return workflows;
	}
	public void setWorkflows(Set<ConfigurableWorkflow> workflows) {
		this.workflows = workflows;
	}	

	public DataResource getSegmentationAnalysisResource() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getSegmentationAnalysisResource();
			return segmentationAnalysisResource;
		}
		return this.getChannelParent().getSegmentationAnalysisResource();
	}
	public void setSegmentationAnalysisResource(DataResource segmentationAnalysisResource) {
		this.segmentationAnalysisResource = segmentationAnalysisResource;
	}

	public boolean isSegmentationAnalysisEnabled() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().isSegmentationAnalysisEnabled();
			return segmentationAnalysisEnabled;
		}
		return this.getChannelParent().isSegmentationAnalysisEnabled();
	}
	public void setSegmentationAnalysisEnabled(boolean segmentationAnalysisEnabled) {
		this.segmentationAnalysisEnabled = segmentationAnalysisEnabled;
	}

	public String getSftpIPAddress() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getSftpIPAddress();
			return sftpIPAddress;
		}
		return this.getChannelParent().getSftpIPAddress();
	}
	public void setSftpIPAddress(String sftpIPAddress) {
		this.sftpIPAddress = sftpIPAddress;
	}

	public String getSftpFolderPath() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getSftpFolderPath();
			return sftpFolderPath;
		}
		return this.getChannelParent().getSftpFolderPath();
	}
	public void setSftpFolderPath(String sftpFolderPath) {
		this.sftpFolderPath = sftpFolderPath;
	}

	public String getSftpUsername() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getSftpUsername();
			return sftpUsername;
		}
		return this.getChannelParent().getSftpUsername();
	}
	public void setSftpUsername(String sftpUsername) {
		this.sftpUsername = sftpUsername;
	}

	public String getSftpPassword() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getSftpPassword();
			return sftpPassword;
		}
		return this.getChannelParent().getSftpPassword();
	}
	public void setSftpPassword(String sftpPassword) {
		this.sftpPassword = sftpPassword;
	}

	public String getSftpSSHKey() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getSftpSSHKey();
			return sftpSSHKey;
		}
		return this.getChannelParent().getSftpSSHKey();
	}
	public void setSftpSSHKey(String sftpSSHKey) {
		this.sftpSSHKey = sftpSSHKey;
	}

    public String getPrivateKey() {
        if ( this.getChannelParent() == null ) {
            if ( this.getParent() != null )
                return this.getParent().getPrivateKey();
            return privateKey;
        }
        return this.getChannelParent().getPrivateKey();
    }
    public void setPrivateKey(String privateKey) { this.privateKey = privateKey; }

	public MetadataFormDefinition getTouchpointMetadataFormDefinition() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getTouchpointMetadataFormDefinition();
			return touchpointMetadataFormDefinition;
		}
		return this.getChannelParent().getTouchpointMetadataFormDefinition();
	}
	public void setTouchpointMetadataFormDefinition(MetadataFormDefinition touchpointMetadataFormDefinition) {
		this.touchpointMetadataFormDefinition = touchpointMetadataFormDefinition;
	}

	public MetadataFormDefinition getVariantMetadataFormDefinition() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getVariantMetadataFormDefinition();
			return variantMetadataFormDefinition;
		}
		return this.getChannelParent().getVariantMetadataFormDefinition();
	}
	public void setVariantMetadataFormDefinition(MetadataFormDefinition variantMetadataFormDefinition) {
		this.variantMetadataFormDefinition = variantMetadataFormDefinition;
	}

	public boolean isMaintainVariantHierarchy() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
				return this.getParent().isMaintainVariantHierarchy();
			return maintainVariantHierarchy;
		}
		return this.getChannelParent().isMaintainVariantHierarchy();
	}
	public void setMaintainVariantHierarchy(boolean maintainVariantHierarchy) {
		this.maintainVariantHierarchy = maintainVariantHierarchy;
	}

    public Set<DocumentHistory> getHistoryRecords() {
        return historyRecords;
    }

    public void setHistoryRecords(Set<DocumentHistory> historyRecords) {
        this.historyRecords = historyRecords;
    }

	public boolean getIsStripoEnabled() { return this.isStripoEnabled(); }

	public boolean isStripoEnabled() {
		if (getChannelParent() != null) {
			return getChannelParent().isStripoEnabled();
		}

		if (getParent() != null) {
			return getParent().isStripoEnabled();
		}
		return stripoEnabled;
	}

	public void setStripoEnabled(boolean stripoEnabled) {
		this.stripoEnabled = stripoEnabled;
	}



	public MetadataForm getMetadataForm() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null )
	    		return this.getParent().getMetadataForm();
			return metadataForm;
		}
		return this.getChannelParent().getMetadataForm();
	}
	public void setMetadataForm(MetadataForm metadataForm) {
		this.metadataForm = metadataForm;
	}

    public boolean  isRootDocument() {
        return (this.getChannelParent() == null && this.getParent() == null);
    }

	public Document getRootDocument() {
		Document rootDocument = this.getChannelParent() != null ? this.getChannelParent() : this;
		if ( rootDocument != null && rootDocument.getParent() != null )
			rootDocument = rootDocument.getParent();
		return rootDocument;
	}
	
	public Boolean getIsOmniChannel() {
		return !this.getChannelAlternateDocuments().isEmpty();
	}
	
	public List<Channel> getAppliedChannels(Boolean applyAlphaSort) {
		List<Channel> appliedChannels = new ArrayList<>();
		if ( this.getConnectorConfiguration() != null ) {
			Channel currentChannel = this.getConnectorConfiguration().getChannel();
			if ( !appliedChannels.contains(currentChannel) )
				appliedChannels.add(currentChannel);
		}
		for ( Document currentDocument: this.getChannelAlternateDocuments() ) {
			if ( currentDocument.getConnectorConfiguration() != null ) {
				Channel currentChannel = currentDocument.getConnectorConfiguration().getChannel();
				if ( !appliedChannels.contains(currentChannel) )
					appliedChannels.add(currentChannel);
			}
		}

		if ( applyAlphaSort )
			Collections.sort(appliedChannels,new IdentifiableMessagepointModelNameComparator());
		
		return appliedChannels;
	}
	
	public List<Document> getChannelAlternateDocuments() {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(
			MessagepointRestrictions.and(
				MessagepointRestrictions.isNotNull("channelParentObject"),
				MessagepointRestrictions.eq("channelParentObject.id", (this.isChannelAlternate() && (this.getChannelParent() != null) ) ? this.getChannelParent().getId() : this.getId())
			) 
		);
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));

		Map<String,String> aliasJoinMap = new HashMap<>();
		aliasJoinMap.put("channelParentObject", "channelParentObject");

		List<Document> documents = HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, aliasJoinMap, MessagepointOrder.asc("id"));
		
		if ( documents != null )
			return documents;
		else
			return new ArrayList<>();
	}
	
	@SuppressWarnings("unchecked")
	public List<Document> getChannelAlternateByType(long channelId) {

	    StringBuilder query = new StringBuilder();
        query.append("SELECT 		   	d ");
        query.append("FROM          	Document d ");
      	query.append("WHERE         	d.channelParentObject.id = :channelParentId ");
      	if ( !this.isAlternate() )
      		query.append("AND			d.connectorConfiguration.connector.channel.id = :channelId ");
      	else
      		query.append("AND			d.parentObject.connectorConfiguration.connector.channel.id = :channelId ");

        Map<String, Object> params = new HashMap<>();
        params.put("channelParentId", this.getId());
        params.put("channelId", channelId);
        
        List<Document> items = (List<Document>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);    
        return items;

	}

	public List<Document> getAlternateLayouts() {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(
			MessagepointRestrictions.and(
				MessagepointRestrictions.and(
					MessagepointRestrictions.isNotNull("parentObject"), 
					MessagepointRestrictions.isNull("channelParentObject") 
				),
				MessagepointRestrictions.eq("parentObject.id", this.getRootDocument().getId())
			) 
		);
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));

		Map<String,String> aliasJoinMap = new HashMap<>();
		aliasJoinMap.put("parentObject", "parentObject");

		List<Document> documents = HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, aliasJoinMap, MessagepointOrder.asc("name"));
		
		if ( documents != null )
			return documents;
		else
			return new ArrayList<>();
	}
	public List<Document> getAlternateLayoutsByName(String name) {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("id", this.getRootDocument().getId()));
		critList.add(MessagepointRestrictions.isNotNull("parentObject"));
		critList.add(MessagepointRestrictions.isNull("channelParentObject"));
		critList.add(MessagepointRestrictions.eq("name",name));
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));
		List<Document> documents = HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("name"));
		
		if ( documents != null )
			return documents;
		else
			return new ArrayList<>();
	}
	
	@SuppressWarnings("unchecked")
	public List<Document> getAppliedAlternateLayouts() {
	    StringBuilder query = new StringBuilder();
        query.append("SELECT DISTINCT   tal ");
        query.append("FROM          	TouchpointSelection t ");
        query.append("INNER JOIN    	t.alternateLayout AS tal ");
        query.append("INNER JOIN    	t.document AS d ");
      	query.append("WHERE         	d.id = :primaryDocId ");

        Map<String, Object> params = new HashMap<>();
        params.put("primaryDocId", this.getRootDocument().getId());
        
        List<Document> items = (List<Document>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);    
        return items;
	}
	
	public List<DataGroup> getApplicableDocumentDataGroups() {
    	// Find all assigned zone data groups
    	Set<DataGroup> zoneDataGroups = new HashSet<>();
    	for ( Zone zone: this.getZones() )
    		if ( !zoneDataGroups.contains(zone.getDataGroup()) && zone.getDataGroup() != null )
    			zoneDataGroups.add(zone.getDataGroup());
    	
    	// Resolve minimum zone data group level
    	int minZoneDataGroupLevel = 10;
    	for ( DataGroup dataGroup: zoneDataGroups )
    		if ( dataGroup.getLevel() < minZoneDataGroupLevel )
    			minZoneDataGroupLevel = dataGroup.getLevel();
    	Set<DataGroup> dataGroupsAtMinLevel = new HashSet<>();
    	
    	// Resolve data groups at lowest data group level
    	for ( DataGroup dataGroup: zoneDataGroups )
    		if ( dataGroup.getLevel() == minZoneDataGroupLevel && !dataGroupsAtMinLevel.contains(dataGroup) )
    			dataGroupsAtMinLevel.add(dataGroup);
    	
    	// If 1 lowest data group include; otherwise, use parent
    	DataGroup refDataGroup = null;
    	if ( dataGroupsAtMinLevel.size() == 1 )
    		refDataGroup = dataGroupsAtMinLevel.iterator().next();
    	else if ( dataGroupsAtMinLevel.size() > 1 ) {
    		Set<DataGroup> parentDataGroups = new HashSet<>();
    		while ( dataGroupsAtMinLevel.size() > 1 ) {
	        	for ( DataGroup dataGroup: dataGroupsAtMinLevel ) {
                    DataGroup currParentDataGroup = dataGroup.findParentDataGroup();
                    if (currParentDataGroup != null && !parentDataGroups.contains(currParentDataGroup))
                        parentDataGroups.add(currParentDataGroup);
                }
	        	dataGroupsAtMinLevel = parentDataGroups;
    		}
    		if (!dataGroupsAtMinLevel.isEmpty())
    			refDataGroup = dataGroupsAtMinLevel.iterator().next();
    	}
    	
    	// Generate list of ancestors
    	List<DataGroup> documentDataGroups = new ArrayList<>();
    	if ( refDataGroup != null )
    		documentDataGroups.add(refDataGroup);
    	while ( refDataGroup != null && refDataGroup.getParentDataGroupId() != null ) {
    		refDataGroup = DataGroup.findById( refDataGroup.getParentDataGroupId() );
    		documentDataGroups.add(refDataGroup);
    	}
    	
    	if (documentDataGroups.isEmpty()) {
    		DataGroup firstLevelDataGroup = DataGroup.getFirstLevelDataGroup(this.getPrimaryDataSource());
    		if ( firstLevelDataGroup != null )		
    			documentDataGroups.add(firstLevelDataGroup);
    	}

        //Filter out non start data groups

    	return documentDataGroups;
	}
	
	
	public boolean isEnabledForVariation() { // It used to be isSelectorDefined()
		return (this.getSelectionParameterGroup() != null);
	}
	
	public boolean isCommunicationWebServiceProductionStatusEnabled() {
		if ( getCommunicationProductionStatusWebService() != null && 
				getCommunicationProductionStatusWebService().getUrl() != null &&
                !getCommunicationProductionStatusWebService().getUrl().isEmpty())
			return true;
		return false;
	}

	public boolean isCommunicationWebServiceNotificationEnabled() {
		if ( getCommunicationNotificationWebService() != null &&
				getCommunicationNotificationWebService().getUrl() != null &&
                !getCommunicationNotificationWebService().getUrl().isEmpty())
			return true;
		return false;
	}
	
	public boolean isCommunicationWebServiceCompositionResultsEnabled() {

		WebServiceConfiguration configuration = getCommunicationCompositionResultsWebService();

		return configuration != null && (!StringUtils.isEmpty(configuration.getUrl()) || !StringUtils.isEmpty(configuration.getDEServerGuid()));
	}
	
	public List<CommunicationOrderEntryItemDefinition> getCommunicationOrderEntryItemDefinitionsInOrder() {
		Set<CommunicationOrderEntryItemDefinition> orderEntryDefSet = getCommunicationOrderEntryItemDefinitions();
        List<CommunicationOrderEntryItemDefinition> orderEntryDefList = new ArrayList<>(orderEntryDefSet);
		orderEntryDefList.sort(new Comparator<>() {
            public int compare(CommunicationOrderEntryItemDefinition o1, CommunicationOrderEntryItemDefinition o2) {
                if (o1.getOrder() > o2.getOrder()) return 1;
                else if (o1.getOrder() < o2.getOrder()) return -1;
                return 0;
            }
        });
		
		return orderEntryDefList;
	}

	public WebServiceConfiguration getCommunicationDriverWebService() {
		return this.getCommunicationDataFeedWebService();
	}
	
	public CommunicationOrderEntryItemDefinition getCommunicationDriverOrderEntryItem() {
		for ( CommunicationOrderEntryItemDefinition currentItem: this.getCommunicationOrderEntryItemDefinitions() )
			if ( currentItem.getIsPrimaryDriverEntry() )
				return currentItem;
		return null;
	}
	
	public CommunicationOrderEntryItemDefinition getCommunicationIndicatorOrderEntryItem() {
		CommunicationOrderEntryItemDefinition primary = null;
		CommunicationOrderEntryItemDefinition indicator = null;
		for ( CommunicationOrderEntryItemDefinition currentItem: this.getCommunicationOrderEntryItemDefinitions() ) {
			if ( currentItem.getIsPrimaryDriverEntry() )
				primary = currentItem;
			else if ( currentItem.getIsIndicatorEntry() )
				indicator = currentItem;
		}
		
		if ( indicator != null )
			return indicator;
		else if ( primary != null )
			return primary;
		
		return null;
	}
	
	public boolean isCommunicationWebServiceDriverEnabled() {
		for ( CommunicationOrderEntryItemDefinition currentItem: this.getCommunicationOrderEntryItemDefinitions() )
			if ( (	currentItem.getTypeId() == MetadataFormItemType.ID_WEB_SERVICE_MENU || 
					currentItem.getTypeId() == MetadataFormItemType.ID_WEB_SERVICE_TEXT || 
					currentItem.getTypeId() == MetadataFormItemType.ID_WEB_SERVICE_MULTISELECT_MENU ) && 
				 currentItem.getIsPrimaryDriverEntry() )
				return true;
		return false;
	}
	public String getCommunicationPrimaryLabel() {
		CommunicationOrderEntryItemDefinition indicatorItem = getCommunicationIndicatorOrderEntryItem();
		if ( indicatorItem != null && this.isCommunicationOrderEntryEnabled() )
			return indicatorItem.getName();
		return ApplicationUtil.getMessage("page.label.recipient");
	}

	public long getCustomerReportingVariableDataElementIdA(){
		if (this.getCustomerReportingVariableA() != null)
		{
			VariableDataElementMap variableDataElementMap = this.getCustomerReportingVariableA().getDataElementMap().get(this.getPrimaryDataSource().getId());	
			
			if(variableDataElementMap != null && variableDataElementMap.getDataElement() != null)
			{
				return variableDataElementMap.getDataElement().getId();
			}
		}
		return -1L;
	}

	public long getCustomerReportingVariableDataElementIdB(){
		if (this.getCustomerReportingVariableB() != null)
		{
			VariableDataElementMap variableDataElementMap = this.getCustomerReportingVariableB().getDataElementMap().get(this.getPrimaryDataSource().getId());	
			
			if(variableDataElementMap != null && variableDataElementMap.getDataElement() != null)
			{
				return variableDataElementMap.getDataElement().getId();
			}
		}
		return -1L;
	}
	
	public String getDisplayNameOfCustomerReportingVariableA(){
		String result = "NULL";
		if (this.getCustomerReportingVariableA() != null)
		{
			result = this.getCustomerReportingVariableA().getFriendlyName();
			if (result == null || result.isEmpty())
			{
				result = this.getCustomerReportingVariableA().getName();
			}
		}
		return result;
	}
	
	public String getDisplayNameOfCustomerReportingVariableB(){
		String result = "NULL";
		if (this.getCustomerReportingVariableB() != null)
		{
			result = this.getCustomerReportingVariableB().getFriendlyName();
			if (result == null || result.isEmpty())
			{
				result = this.getCustomerReportingVariableB().getName();
			}
		}
		return result;
	}
	
	public List<AbstractDataElement> getDataElements() {
    	List<AbstractDataElement> documentDataElements  = new ArrayList<>();
    	List<AbstractDataElement> allDataElements  = HibernateUtil.getManager().getObjects(AbstractDataElement.class, MessagepointOrder.asc("name"));
    	DataSource primaryDS = getPrimaryDataSource();
    	if (primaryDS == null)
    		return allDataElements;
    	for (AbstractDataElement dataElement : allDataElements) {
			DataSource dataSource = dataElement.getDataSource();
			if (dataSource != null) {
				if (dataSource == primaryDS) {
					documentDataElements.add(dataElement);
				}
			}
		}
    	return documentDataElements;
	}

	@SuppressWarnings("unchecked")
	public List<String> getTenantEmailAddressesSorted() {
        return new ArrayList<>((List<String>) HibernateUtil.getManager().getObjectsAdvanced("from DocumentTenantEmailAddress e where e.id = " + this.getId() + " order by tenantEmailAddress"));
	}

	public CompositionFileSet getCompositionFileSet() {
        if ( this.getConnectorConfiguration() == null )
            return null;
		if ( this.getConnectorConfiguration() instanceof DialogueConfiguration )
			return ((DialogueConfiguration)this.getConnectorConfiguration()).getCompositionFileSet();
		else if ( this.getConnectorConfiguration() instanceof GMCConfiguration )
			return ((GMCConfiguration)this.getConnectorConfiguration()).getCompositionFileSet();
		else if ( this.getConnectorConfiguration() instanceof SefasConfiguration )
			return ((SefasConfiguration)this.getConnectorConfiguration()).getCompositionFileSet();
		else if ( this.getConnectorConfiguration() instanceof MPHCSConfiguration )
			return ((MPHCSConfiguration)this.getConnectorConfiguration()).getCompositionFileSet();
		else
			return null;
	}
	
	public void setCompositionFileSet(CompositionFileSet connectorConfiguration) {
        if ( this.getConnectorConfiguration() == null )
            return;

		if ( this.getConnectorConfiguration() instanceof DialogueConfiguration )
			((DialogueConfiguration)this.getConnectorConfiguration()).setCompositionFileSet(connectorConfiguration);
		else if ( this.getConnectorConfiguration() instanceof GMCConfiguration )
			((GMCConfiguration)this.getConnectorConfiguration()).setCompositionFileSet(connectorConfiguration);
		else if ( this.getConnectorConfiguration() instanceof SefasConfiguration)
			((SefasConfiguration)this.getConnectorConfiguration()).setCompositionFileSet(connectorConfiguration);
		else if ( this.getConnectorConfiguration() instanceof MPHCSConfiguration)
			((MPHCSConfiguration)this.getConnectorConfiguration()).setCompositionFileSet(connectorConfiguration);
	}
	
	public boolean getIsEmailTemplateUploaded() {
		return isEmailTemplateUploaded();
	}
	
	public boolean isEmailTemplateUploaded() {
		return !this.getZones().isEmpty();
	}
	
	public boolean getIsEmailTouchpoint() {
		return isEmailTouchpoint();
	}
	public boolean isEmailTouchpoint() {
		if ( this.isChannelAlternate() ) {
			List<Document> emailAlternates = ((Document)this.getChannelParentObject()).getChannelAlternateByType(Channel.CHANNEL_EMAIL_ID);
			if (!emailAlternates.isEmpty()) {
				return emailAlternates.get(0).getId() == this.getId();
			}
			return false;
		}
		if (this.getConnectorConfiguration() == null) {
			return false;
		}
		return this.getConnectorConfiguration().getConnector().getChannel().getId() == Channel.CHANNEL_EMAIL_ID;
	}
	
	public boolean getIsWebTouchpoint(){
		return isWebTouchpoint();
	}
	public boolean isWebTouchpoint() {
		if ( this.isChannelAlternate() ) {
			List<Document> webAlternates = ((Document)this.getChannelParentObject()).getChannelAlternateByType(Channel.CHANNEL_WEB_ID);
			if (!webAlternates.isEmpty())
				return webAlternates.get(0).getId() == this.getId();
			return false;
		}
		if (this.getConnectorConfiguration() == null)
			return false;
		return this.getConnectorConfiguration().getConnector().getChannel().getId() == Channel.CHANNEL_WEB_ID;
	}
	
	public boolean getIsExactTargetTouchpoint(){
		return isExactTargetTouchpoint();
	}
	public boolean isExactTargetTouchpoint(){
		if (this.getConnectorConfiguration() == null)
			return false;
		return this.getConnectorConfiguration().getConnector().getId() == Connector.EXACTTARGET_EMAIL_FILE_ID;
	}	
	
	public boolean getIsSmsTouchpoint() {
		return isSmsTouchpoint();
	}
	public boolean isSmsTouchpoint() {
		if (this.getConnectorConfiguration() == null)
			return false;
		return this.getConnectorConfiguration().getConnector().getChannel().getId() == Channel.CHANNEL_SMS_ID;
	}
	
	public boolean getIsDialogueTouchpoint() {
		return isDialogueTouchpoint();
	}
	public boolean isDialogueTouchpoint() {
		if (this.getConnectorConfiguration() == null)
			return false;
		return this.getConnectorConfiguration().getConnector().getId() == Connector.DIALOGUE_FILE_ID;
	}
	
	public boolean getIsGMCTouchpoint() {
		return isGMCTouchpoint();
	}
	public boolean isGMCTouchpoint() {
		if (this.getConnectorConfiguration() == null)
			return false;
		return this.getConnectorConfiguration().getConnector().getId() == Connector.GMC_FILE_ID;
	}
	
	public boolean getIsNativeCompositionTouchpoint()
	{
		return isNativeCompositionTouchpoint();
	}
	public boolean isNativeCompositionTouchpoint()
	{
		if (this.getConnectorConfiguration() == null)
			return false;
		return this.getConnectorConfiguration().getConnector().getId() == Connector.NATIVE_COMPOSITION_FILE_ID;
	}

	public boolean getIsSefasCompositionTouchpoint()
	{
		return isSefasCompositionTouchpoint();
	}
	public boolean isSefasCompositionTouchpoint()
	{
		if (this.getConnectorConfiguration() == null)
			return false;
		return this.getConnectorConfiguration().getConnector().getId() == Connector.SEFAS_FILE_ID;
	}

	public boolean getIsMPHCSCompositionTouchpoint()
	{
		return isMPHCSCompositionTouchpoint();
	}
	public boolean isMPHCSCompositionTouchpoint()
	{
		if (this.getConnectorConfiguration() == null)
			return false;
		return this.getConnectorConfiguration().getConnector().getId() == Connector.MPHCS_FILE_ID;
	}

	public boolean getIsTemplateControlled()
	{
		return isTemplateControlled();
	}
	public boolean isTemplateControlled() {
		if (this.getConnectorConfiguration() == null)
			return false;
		if (this.getIsSefasCompositionTouchpoint()) {
			return ((SefasConfiguration) (this.getConnectorConfiguration())).getTemplateControl();
		}
		else if (this.getIsMPHCSCompositionTouchpoint()) {
			return ((MPHCSConfiguration) (this.getConnectorConfiguration())).getTemplateControl();
		}
		else
			return false;
	}

	public boolean getAccessibility() {
		if (this.getConnectorConfiguration() == null)
			return false;
		if (this.getIsSefasCompositionTouchpoint()) {
			return ((SefasConfiguration) (this.getConnectorConfiguration())).getAccessibility();
		} else if (this.getIsMPHCSCompositionTouchpoint()) {
			return ((MPHCSConfiguration) (this.getConnectorConfiguration())).getAccessibility();
		} else
			return false;
	}

	public boolean getIsPrintTouchpoint() {
		return isPrintTouchpoint();
	}
	public boolean isPrintTouchpoint() {
		if (this.getConnectorConfiguration() == null)
			return false;
		return 	this.getConnectorConfiguration().getConnector().getId() == Connector.DIALOGUE_FILE_ID ||
				this.getConnectorConfiguration().getConnector().getId() == Connector.GMC_FILE_ID ||
				this.getConnectorConfiguration().getConnector().getId() == Connector.NATIVE_COMPOSITION_FILE_ID ||
				this.getConnectorConfiguration().getConnector().getId() == Connector.SEFAS_FILE_ID ||
				this.getConnectorConfiguration().getConnector().getId() == Connector.MPHCS_FILE_ID;
	}
	
	public int getHpImportPageCount() {
		return hpImportPageCount;
	}

	public void setHpImportPageCount(int hpImportPageCount) {
		this.hpImportPageCount = hpImportPageCount;
	}

	public long getCommunicationUniqueIndex() {
		return this.communicationUniqueIndex;
	}

	public void setCommunicationUniqueIndex(long uniqueIndex) {
		this.communicationUniqueIndex = uniqueIndex;
	}

	public String getBlueRelayEndpoint() {
		if (getChannelParent() != null) {
			return getChannelParent().getBlueRelayEndpoint();
		}

		if (getParent() != null) {
			return getParent().getBlueRelayEndpoint();
		}

		return blueRelayEndpoint;
	}

	public void setBlueRelayEndpoint(String blueRelayEndpoint) {
		this.blueRelayEndpoint = blueRelayEndpoint;
	}

	public String getBlueRelayUsername() {
		if (getChannelParent() != null) {
			return getChannelParent().getBlueRelayUsername();
		}

		if (getParent() != null) {
			return getParent().getBlueRelayUsername();
		}

		return blueRelayUsername;
	}

	public void setBlueRelayUsername(String blueRelayUsername) {
		this.blueRelayUsername = blueRelayUsername;
	}

	public String getBlueRelayToken() {
		if (getChannelParent() != null) {
			return getChannelParent().getBlueRelayToken();
		}

		if (getParent() != null) {
			return getParent().getBlueRelayToken();
		}

		return blueRelayToken;
	}

	public void setBlueRelayToken(String blueRelayToken) {
		this.blueRelayToken = blueRelayToken;
	}

	public String getBlueRelayTargetFolder() {
		if (getChannelParent() != null) {
			return getChannelParent().getBlueRelayTargetFolder();
		}

		if (getParent() != null) {
			return getParent().getBlueRelayTargetFolder();
		}

		return blueRelayTargetFolder;
	}

	public void setBlueRelayTargetFolder(String blueRelayTargetFolder) {
		this.blueRelayTargetFolder = blueRelayTargetFolder;
	}

	public BrandProfile getBrandProfile() {
		if (getChannelParent() != null) {
			return getChannelParent().getBrandProfile();
		}

		if (getParent() != null) {
			return getParent().getBrandProfile();
		}

		return brandProfile;
	}

	public void setBrandProfile(BrandProfile brandProfile) {
		this.brandProfile = brandProfile;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		if (id > 0) {
			result = prime * Long.valueOf(id).hashCode();
		} else {
			result = prime * result + ((this.getGuid() == null) ? 0 : this.getGuid().hashCode());
		}
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (!(obj instanceof Document))
			return false;
		final Document other = (Document) obj;
        if(!getObjectSchemaName().equals(other.getObjectSchemaName()))
            return false;
		if (id > 0) {
			return id == other.getId();
		} else {
			if (this.getGuid() == null) {
				if (other.getGuid() != null)
					return false;
			} else if (!this.getGuid().equals(other.getGuid()))
				return false;
		}
		return true;
	}

	public Set<TemplateModifier> getTemplateManagedActiveMasterTemplateModifiers() {
		Set<TemplateModifier> validList = new HashSet<>();
		for(TemplateModifier tm : getMasterTemplateModifiers()){
			if(tm.getIsActive() && tm.isTemplateManaged())
				validList.add(tm);
		}
		return validList;
	}

	public iFileWriter getFileWriter(long zoneId, long messageId) {
		return new DocumentFileWriter(this, zoneId, messageId);
	}

	public Zone[] getZoneArray() {
		if ((sortedZoneArray == null) || (sortedZoneArray.length != getZones().size())) {
			Zone[] zoneArray = new Zone[0];
			zoneArray = this.getZones().toArray(zoneArray);
			// Sort the zones
			Arrays.sort(zoneArray, new ZoneComparator());
			sortedZoneArray = zoneArray;
		}
		return sortedZoneArray;
	}

	public Zone[] getZoneArraySortedOnFriendlyName() {
		if ((friendlyNameSortedZoneArray == null) || (friendlyNameSortedZoneArray.length != getZones().size())) {
			Zone[] zoneArray = new Zone[0];
			zoneArray = this.getZones().toArray(zoneArray);
			// Sort the zones
			Arrays.sort(zoneArray, new ZoneComparatorOnFrienlyName());
			friendlyNameSortedZoneArray = zoneArray;
		}
		return friendlyNameSortedZoneArray;
	}

	public  List<Zone> getFlowZones() {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("flowZone", true));
		critList.add(MessagepointRestrictions.eq("document.id", this.getRootDocument().getId()));
		List<Zone> flowZones =  HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList);
		if (flowZones != null && !flowZones.isEmpty()) {
			Collections.sort(flowZones, new Comparator<>() {
                public int compare(Zone o1, Zone o2) {
                    return o1.getName().compareToIgnoreCase(o2.getName());
                }
            });
		}
		return flowZones;
	}

	public List<ContentObject> getMessages() {
		List<ContentObject> messages = new ArrayList<>();
		for (Zone zone : this.getZones()) {
			messages.addAll(zone.getMessages());
		}
		return messages;
	}

	public boolean hasActiveMessages()
	{
		for (Zone zone : this.getZones()) {
			if (zone.hasActiveContentObjects(ContentObject.OBJECT_TYPE_MESSAGE))
				return true;
		}

		return false;
	}

	public int getLocalSmartTextCount(){
        String sql ="SELECT COUNT(DISTINCT m.id) FROM content_object m WHERE m.object_type = " + ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT + " AND m.document_id = :touchpointId";
		Query query = HibernateUtil.getManager().getSession().createNativeQuery(sql);
		query.setParameter("touchpointId", this.getId());
		int count = 0;
		try{
			List<?> retList = query.list();
			if(retList!=null && !retList.isEmpty()){
				Object retObj = retList.get(0);

				if (Number.class.isInstance(retObj)) {
					count = ((Number) retObj).intValue();
				}
			}
			return count;
		}catch(Exception e){
			return -1;
		}
	}

	public int getLocalImageLibraryCount(){
//		String sql ="SELECT COUNT(DISTINCT m.id) FROM message m, message_version_map mvm, message_instance mi WHERE m.id = mvm.message_id AND mvm.message_instance_id = mi.id AND m.is_touchpoint_local = 1 AND mi.content_type_id = 2 AND m.status_id != 3 AND m.status_id != 4 AND m.document_id = :touchpointId";
        String sql ="SELECT COUNT(m.id) FROM content_object m WHERE m.object_type = " + ContentObject.OBJECT_TYPE_LOCAL_IMAGE + " AND m.content_type_id = " + ContentType.GRAPHIC + " AND m.document_id = :touchpointId";
		Query query = HibernateUtil.getManager().getSession().createNativeQuery(sql);
		query.setParameter("touchpointId", this.getId());
		int count = 0;
		try{
			List<?> retList = query.list();
			if(retList!=null && !retList.isEmpty()){
				Object retObj = retList.get(0);

				if (Number.class.isInstance(retObj)) {
					count = ((Number) retObj).intValue();
				}
			}
			return count;
		}catch(Exception e){
			return -1;
		}
	}

	public int getLocalCanvasCount(){
//		String sql ="SELECT COUNT(DISTINCT m.id) FROM message m, message_version_map mvm, message_instance mi WHERE m.id = mvm.message_id AND mvm.message_instance_id = mi.id AND m.is_touchpoint_local = 1 AND mi.content_type_id = 1 AND mi.content_specialization_type_id = 2 AND m.status_id != 3 AND m.status_id != 4 AND m.document_id = :touchpointId";
        String sql ="SELECT COUNT(m.id) FROM content_object m WHERE m.object_type = " + ContentObject.OBJECT_TYPE_LOCAL_IMAGE + " AND m.content_type_id = " + ContentType.SHARED_FREEFORM + " AND m.document_id = :touchpointId";
		Query query = HibernateUtil.getManager().getSession().createNativeQuery(sql);
		query.setParameter("touchpointId", this.getId());
		int count = 0;
		try{
			List<?> retList = query.list();
			if(retList!=null && !retList.isEmpty()){
				Object retObj = retList.get(0);

				if (Number.class.isInstance(retObj)) {
					count = ((Number) retObj).intValue();
				}
			}
			return count;
		}catch(Exception e){
			return -1;
		}
	}

	public String toString() {
		// NOTE - we return the id here so that we can
		// bind the document to a checkbox in the message delivery
		// form. If you need to change this talk to Rob first.
		return "" + id;
	}

	public DataGroup getRecipientDataGroup()
	{
		return DataGroup.getFirstLevelDataGroup( getPrimaryDataSource() );
	}

	public DataSource getPrimaryDataSource() {
		if (getDataSourceAssociation() != null)
			return getDataSourceAssociation().getPrimaryDataSource();
		return null;
	}

    public List<DataSource> getReferenceDataSources() {
       return getReferenceDataSources(true);
    }
	
	public List<DataSource> getReferenceDataSources(Boolean viewConnectedReference) {
        List<DataSource> referenceDataSources = new ArrayList<>();
        if (getDataSourceAssociation() != null) {
            Set<ReferenceConnection> referenceConnections = getDataSourceAssociation().getReferenceConnections();
            for (ReferenceConnection referenceConnection : referenceConnections) {
                if (!viewConnectedReference && referenceConnection.getReferenceDataSource().getSourceType().getId() != SourceType.TYPE_REFERENCE_CONNECTED) {
                    referenceDataSources.add(referenceConnection.getReferenceDataSource());
                }
                if (viewConnectedReference) {
                    referenceDataSources.add(referenceConnection.getReferenceDataSource());
                }
            }
        }
        return referenceDataSources;
    }
	
	public List<DataSource> getNonConnectedReferenceDataSources() {
		List<DataSource> referenceDataSources = new ArrayList<>();
		if ( getDataSourceAssociation() != null ) {
			Set<ReferenceConnection> referenceConnections = getDataSourceAssociation().getReferenceConnections();
			for (ReferenceConnection referenceConnection : referenceConnections) {
				
				// Skip Connected reference sources
				if ( referenceConnection.getReferenceDataSource().getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED )
					continue;
                if (referenceConnection.getConnectorParameter() == null || referenceConnection.getConnectorParameter().isEmpty()){
                    continue;
                }
				
				referenceDataSources.add(referenceConnection.getReferenceDataSource());
			}
		}
		return referenceDataSources;
	}

	public List<DataSource> getAllDataSources() {
		List<DataSource> allDataSources = new ArrayList<>();
		if (getPrimaryDataSource() != null)
			allDataSources.add(getPrimaryDataSource());
		allDataSources.addAll(getReferenceDataSources());
		return allDataSources;
	}

    public List<DataSource> getAllDataSourcesBasedOnFeatureFlag(Boolean viewConnectedReferenceDs) {
        List<DataSource> allDataSources = new ArrayList<>();
        if (getPrimaryDataSource() != null)
            allDataSources.add(getPrimaryDataSource());
        allDataSources.addAll(getReferenceDataSources(viewConnectedReferenceDs));
        return allDataSources;
    }

	public static Document findById(long id) {
		return HibernateUtil.getManager().getObject(Document.class, id);
	}
	
	public static Document findTrashTp(){
		return HibernateUtil.getManager().getObjectUnique(Document.class, MessagepointRestrictions.eq("trashTouchpoint", true));
	}
	
	public static boolean hasAssetsInTrashTp(){
		Document trashTp = findTrashTp();
		if(trashTp != null){
			return ContentObject.hasAssetsInTrash();
		}else{
			return false;
		}
	}
	
	public static Document findByName(String name) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("name", name));
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));
		critList.add(MessagepointRestrictions.isNull("parentObject"));
		critList.add(MessagepointRestrictions.isNull("channelParentObject"));
		
		List<Document> documentList =  HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList);
		if (documentList != null && !documentList.isEmpty()) {
			return documentList.get(0);
		}
		return null;
	}

	public static Document findByGuid(String guid) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("guid", guid));
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));
		
		List<Document> documentList =  HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList);
		if (documentList != null && !documentList.isEmpty()) {
			return documentList.get(0);
		}
		return null;
	}

	public static Document findByOriginAndParentDocument(Document origin, Document document) {
		Document result = null;
		
		if (document != null && origin != null)
		{
			if (origin.getId() == document.getId())
				return (Document) origin.getOriginObject(); 
			
			ArrayList<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.eq("parentObject.id", document.getId()));
			critList.add(MessagepointRestrictions.eq("originObject.id", origin.getId()));
			List<Document> orderedList = HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("id"));
			if (orderedList != null && !orderedList.isEmpty())
				result = orderedList.get(0);
		}
		
		return result;
	}
	
	public DocumentSection findSectionByParent(DocumentSection parentSection) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(
			MessagepointRestrictions.and(
				MessagepointRestrictions.isNotNull("parentObject"), MessagepointRestrictions.eq("parentObject.id", parentSection.getId())
			)
		);
		critList.add(MessagepointRestrictions.eq("document.id", this.getId()));
		
		List<DocumentSection> sectionList =  HibernateUtil.getManager().getObjectsAdvanced(DocumentSection.class, critList);
		if ( sectionList != null && !sectionList.isEmpty())
			return sectionList.get(0);
		return null;
	}
	public Zone findZoneByParent(Zone parentZone) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(
			MessagepointRestrictions.and(
				MessagepointRestrictions.isNotNull("parentObject"), MessagepointRestrictions.eq("parentObject.id", parentZone.getId())
			)
		);
		critList.add(MessagepointRestrictions.eq("document.id", this.getId()));
		
		List<Zone> zoneList =  HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList);
		if ( zoneList != null && !zoneList.isEmpty())
			return zoneList.get(0);
		return parentZone;
	}
	public static List<Zone> findZonesByParentId(Long parentZoneId) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(
			MessagepointRestrictions.and(
				MessagepointRestrictions.isNotNull("parentObject"), MessagepointRestrictions.eq("parentObject.id", parentZoneId)
			)
		);
		
		return HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList);
	}
	public Zone findZoneByConnector(String connector) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("name", connector));
		critList.add(MessagepointRestrictions.eq("document.id", this.getId()));
		
		List<Zone> zoneList =  HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList);
		if ( zoneList != null && !zoneList.isEmpty())
			return zoneList.get(0);
		return null;
	}
	
	public List<Zone> getSortedVisibleZonesOfUser(boolean includeCommunicationZones, boolean includePlaceholders){
		List<Zone> zoneList = new ArrayList<>();
		for (Zone currentZone :findZonesFilteredByWorkgroup(UserUtil.getPrincipalUser().getWorkgroupId()) )
			if ( currentZone.isEnabled() ) {
				 if ( !(includeCommunicationZones || currentZone.getZoneTypeId() != ZoneType.ID_INTERACTIVE_ZONE) )
					 continue;
				 if ( !(includePlaceholders || currentZone.getZoneTypeId() != ZoneType.ID_PLACEHOLDER_ZONE) )
					 continue;
				zoneList.add(currentZone);
			}
		Collections.sort(zoneList, new ZoneComparatorOnFrienlyName());
		return zoneList;
	}
	public Set<Zone> getVisibleZonesOfUser(){
		return findZonesFilteredByWorkgroup(UserUtil.getPrincipalUser().getWorkgroupId());
	}
	/**
	 * if workgroupId <0 then , return all Zones
	 * else 
	 *       return all zones within this Touchpoint and are associated with this workgroup
     */
	@SuppressWarnings("rawtypes")
	public Set<Zone> findZonesFilteredByWorkgroup(long workgroupId){
		Set<Zone> retZoneSet = new HashSet<>();
		
		if ( workgroupId > 0 ) {
			
			String query = 	"SELECT z.ID FROM ZONE z " +
					"INNER JOIN WORKGROUP_ZONE wz ON wz.ZONE_ID = z.ID " +
					"WHERE z.DOCUMENT_ID = :documentId AND wz.WORKGROUP_ID = :workgroupId AND (z.PARENT_ZONE_ID IS NULL OR z.OVERRIDE_WORKGROUPS = true) " +
					"UNION " +
					"SELECT z.ID FROM ZONE z " +
					"INNER JOIN WORKGROUP_ZONE wz ON wz.ZONE_ID = z.PARENT_ZONE_ID " +
					"WHERE z.DOCUMENT_ID = :documentId AND wz.WORKGROUP_ID = :workgroupId AND z.PARENT_ZONE_ID IS NOT NULL AND z.OVERRIDE_WORKGROUPS = false " +
					"UNION " +
					"SELECT z.ID FROM ZONE z " +
					"INNER JOIN ZONE pz ON z.PARENT_ZONE_ID = pz.id " +
					"INNER JOIN WORKGROUP_ZONE wz ON wz.ZONE_ID = pz.PARENT_ZONE_ID " +
					"WHERE z.DOCUMENT_ID = :documentId AND pz.PARENT_ZONE_ID IS NOT NULL AND pz.OVERRIDE_WORKGROUPS = false AND wz.WORKGROUP_ID = :workgroupId";
	
			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
			sqlQuery.setParameter("documentId", this.getId());
			sqlQuery.setParameter("workgroupId", workgroupId);
			List ids = sqlQuery.list();
			for(Object idObj : ids)
				retZoneSet.add(Zone.findById(((BigInteger)idObj).longValue()));

		}
		return retZoneSet;			
	}

	public Zone[] getCurrentWorkgroupZones() {
		Set<Zone> unsorted = getCurrentWorkgroupZoneMessageMap().keySet();
		Zone[] sortedArray = new Zone[unsorted.size()];
		// Sort the zones
		Arrays.sort(unsorted.toArray(sortedArray), new ZoneComparatorOnFrienlyName());
		return sortedArray;
	}

	/**
	 * Returns a map having each of the zones of the Workgroup corresponding to workgroupId parameter 
	 * passed to the method as the key and a List of Messages that that Zone is delivered to as the value.
	 *
     */
	public Map<Zone, List<ContentObject>> getWorkgroupZoneMessageMap(long workgroupId, int dataType) {
		Map<Zone, List<ContentObject>> map = new HashMap<>();
		for (Zone zone : findZonesFilteredByWorkgroup(workgroupId) ) {
			if ( zone.isPlaceholder() )
				continue;
			
			List<ContentObject> zoneMessages = new ArrayList<>();
			Set<ContentObject> zoneModelMessages = zone.getSortedMessages(dataType);
			if (zoneModelMessages != null && !zoneModelMessages.isEmpty())
			{
				zoneMessages.addAll(zoneModelMessages);
				map.put(zone, zoneMessages);
			}
		}
		return map;
	}
	
	public List<ContentObject> getWorkgroupZoneMessageList(long workgroupId, int dataType) {
		List<ContentObject> messageList = new ArrayList<>();
		for (Zone zone : findZonesFilteredByWorkgroup(workgroupId) ) {
			if ( zone.isPlaceholder() )
				continue;
			
			Set<ContentObject> zoneModelMessages = zone.getSortedMessages(dataType);
			if (zoneModelMessages != null && !zoneModelMessages.isEmpty()){
				messageList.addAll(zoneModelMessages);
			}
		}
		return messageList;
	}
	
	public Map<Zone, List<ContentObject>> getZoneMessageMap(int dataType) {
		Map<Zone, List<ContentObject>> map = new HashMap<>();
		for (Zone zone : zones ) {
			if ( zone.isPlaceholder() )
				continue;

			List<ContentObject> zoneMessages = new ArrayList<>();
			Set<ContentObject> zoneModelMessages = zone.getSortedMessages(dataType);
			if (zoneModelMessages != null && !zoneModelMessages.isEmpty())
			{
				zoneMessages.addAll(zoneModelMessages);
				map.put(zone, zoneMessages);
			}
		}
		return map;
	}
	
	/**
	 * Returns a map having each of the zones of the Workgroup corresponding to workgroupId parameter 
	 * passed to the method as the key and a List of Messages that that Zone is delivered to as the value.
	 */
	public Map<Zone, List<ContentObject>> getCurrentWorkgroupZoneMessageMap() {
		return getWorkgroupZoneMessageMap(UserUtil.getPrincipalUser().getWorkgroupId(), ContentObject.DATA_TYPE_WORKING_AND_ACTIVE);
	}

	public Map<Zone, List<ContentObject>> getCurrentWorkgroupZoneMessageWithWorkingCopyMap() {
		return getWorkgroupZoneMessageMap(UserUtil.getPrincipalUser().getWorkgroupId(), ContentObject.DATA_TYPE_WORKING);
	}
	
	public List<ContentObject> getCurrentWorkgroupZoneMessageWithWorkingCopyList() {
		return getWorkgroupZoneMessageList(UserUtil.getPrincipalUser().getWorkgroupId(), ContentObject.DATA_TYPE_WORKING);
	}
	
	public boolean isDocumentMultipart(){
		boolean result =false;
		for (Iterator<Zone> iterator = this.getVisibleZonesOfUser().iterator(); iterator.hasNext();) {
			Zone zone = (Zone) iterator.next();
			if(zone.getContentTypeId()==ContentType.MULTIPART){
				result=true;
				break;
			}
		}
		return result;
	}

	public DocumentSection getDocumentSection( int sectionNum )
	{
	    for( DocumentSection ds : this.getDocumentSections() )
	    {
	        if ( ds.getSectionOrder() == sectionNum )
	            return ds;
	    }
	    return null;
	}

	public List<DocumentSection> getDocumentSectionsByOrder() {
		List<DocumentSection> sectionList = new ArrayList<>(this.getDocumentSections());
		Collections.sort(sectionList, new DocumentSectionAscendingOrderComparator());
		return sectionList;
	}
	public DocumentSection getEmailSection() {
		for (DocumentSection documentSection: getDocumentSections())
			return documentSection;
		return null;
	}
	
	public int getNextSectionOrder() {
		return getDocumentSections().size()+1;
	}
	
	public List<DataElementVariable> getExtRepDataVarSorted() {
		List<DataElementVariable> extRepDataVarSorted = new ArrayList<>(this.getExtReportingDataVariables());
		Collections.sort(extRepDataVarSorted, new DataElementVariableComparator());
		return extRepDataVarSorted;
	}	

	public String getFirstPageDehydratedWeight() {
		try {
			return WeightUtil.dehydrateWeight(this.getFirstPageWeight());
		} catch (ParseException e) {
			log.error("Error: ", e);
			return null;
		}
	}

	public String getOtherPagesDehydratedWeight() {
		try {
			return WeightUtil.dehydrateWeight(this.getOtherPagesWeight());
		} catch (ParseException e) {
			log.error("Error: ", e);
			return null;
		}
	}

	public Set<TouchpointSelection> getVisibleTouchpointSelections() {
		Set<TouchpointSelection> visibleTouchpointSelections = new HashSet<>();
		for (TouchpointSelection touchpointSelection : this.getTouchpointSelections())
		{
			if (!touchpointSelection.isDeleted())
			{
				visibleTouchpointSelections.add(touchpointSelection);
			}
		}
		return visibleTouchpointSelections;
	}
	public List<TouchpointSelection> getTouchpointSelectionsSorted() {
		List<TouchpointSelection> tpSelectionsSorted = new ArrayList<>(this.getVisibleTouchpointSelections());
		Collections.sort(tpSelectionsSorted, new TouchpointSelectionComparator());
		return tpSelectionsSorted;
	}
	public boolean getHasTouchpointSelections() {
		return this.getTouchpointSelections().size() > 1;
	}
	
	public Set<ParameterGroupInstanceCollection> getParameterGroupInstanceCollections() {
		Set<ParameterGroupInstanceCollection> pgInstanceCollections = new HashSet<>();
		for (TouchpointSelection touchpointSelection : this.getVisibleTouchpointSelections()) {
			if (touchpointSelection.getParameterGroupTreeNode() != null && touchpointSelection.getParameterGroupTreeNode().getParameterGroupInstanceCollection() != null) {
				pgInstanceCollections.add(touchpointSelection.getParameterGroupTreeNode().getParameterGroupInstanceCollection());
			}
		}
		return pgInstanceCollections;
	}

	public boolean isBelongToExecutableCollection(){
		for(TpCollectionTouchpointAssignment assign : this.getTpCollectionTouchpoints()){
			if(assign.getTouchpointCollection().isExecutable()){
				return true;
			}
		}
		return false;
	}
	
	/**
	 * Return the list of executable collections which contain this touchpoint
	 * e.g., Touchpoint A, we have Package Collection 1: [A, B, C], Package Collection 2: [A, C] (Collection 2 is the subset of Collection 1), 
	 * then this API will return both Package Collections
	 */
	public List<TouchpointCollection> getExecutableCollections(){
		List<TouchpointCollection> executableCollections = new ArrayList<>();
		for(TpCollectionTouchpointAssignment assign : this.getTpCollectionTouchpoints()){
			if(assign.getTouchpointCollection().isExecutable()){
				executableCollections.add(assign.getTouchpointCollection());
			}
		}
		return executableCollections;
	}
	
	/**
	 * Check whether this touchpoint is in one of the executable collections
     */
	public boolean isInExecutableCollection(){
		for(TpCollectionTouchpointAssignment assign : this.getTpCollectionTouchpoints()){
			if(assign.getTouchpointCollection().isExecutable()){
				return true;
			}
		}
		return false;		
	}
	
	/**
	 * Return a list of all documents which are in the same family of this touchpoint
	 * e.g., Touchpoint A, we have Package Collection 1: [A, B, C], Package Collection 2: [A, C] (Collection 2 is the subset of Collection 1), 
	 * then this API will return [A, B, C]
	 */
	public List<Document> getAllPackagedCollectionFamilyDocuments(){
		List<Document> familyDocuments = new ArrayList<>();
		for(TpCollectionTouchpointAssignment assign : this.getTpCollectionTouchpoints()){
			if(assign.getTouchpointCollection().isExecutable()){
				for(Document doc : assign.getTouchpointCollection().getDocuments()){
					if(!familyDocuments.contains(doc)){
						familyDocuments.add(doc);
					}
				}
			}
		}
		return familyDocuments;
	}

	public static boolean existsDocuments() {
		List<MessagepointCriterion> critList = computeCriterionListForFindAll(false);

		return HibernateUtil.getManager().exists(Document.class, critList.toArray(new MessagepointCriterion[0]));
	}

	public static List<Document> findAll() {
		return findAll(false);
	}
	public static List<Document> findAllDocumentsAndProjects() {
		return findAll(true);
	}
	
	public static List<Document> findAll(boolean includeProjects) {
		List<MessagepointCriterion> critList = computeCriterionListForFindAll(includeProjects);
		return HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("name"));
	}

	private static List<MessagepointCriterion> computeCriterionListForFindAll(boolean includeProjects) {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.isNull("parentObject"));
		critList.add(MessagepointRestrictions.isNull("channelParentObject"));
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));
		if ( !includeProjects ) {
			critList.add(MessagepointRestrictions.isNull("checkoutTimestamp"));
		}
		return critList;
	}

	public static List<Document> findAllPublished() {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.isNull("parentObject"));
		critList.add(MessagepointRestrictions.isNull("channelParentObject"));
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));
		critList.add(MessagepointRestrictions.isNull("checkoutTimestamp"));
		critList.add(MessagepointRestrictions.isNotNull("exchangePublishedTimestamp"));
		return HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("name"));
	}
	
	public static List<Document> findAllSubscribed(String exchangeInstanceGuid, String exchangeTouchpointGuid) {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.isNull("parentObject"));
		critList.add(MessagepointRestrictions.isNull("channelParentObject"));
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));
		critList.add(MessagepointRestrictions.eq("exchangeInstanceGuid", exchangeInstanceGuid));
		critList.add(MessagepointRestrictions.eq("exchangeTouchpointGuid", exchangeTouchpointGuid));
		return HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("name"));
	}
	
	public static List<Document> findAllEnabled() {
		return findAllEnabled(false);
	}
	public static List<Document> findAllDocumentsAndProjectsEnabled() {
		return findAllEnabled(true);
	}
	public static List<Document> findAllDocumentsAndProjectsEnabled(boolean sortByHierarchy) {
		if ( !sortByHierarchy)
			return findAllEnabled(true);
		else {
			List<Document> touchpointsList = Document.findAllEnabled(false);
			List<Document> touchpointsAndProjects = Document.findAllDocumentsAndProjectsEnabled();
			
			boolean loopFlag = touchpointsList.size() != touchpointsAndProjects.size();
			while (loopFlag) {
				loopFlag = false;
				for ( int i = touchpointsAndProjects.size() - 1; i >= 0; i--)
				{
					if ( touchpointsList.indexOf(touchpointsAndProjects.get(i)) == -1 && 
						touchpointsAndProjects.get(i).getOriginObject() != null && 
						touchpointsList.indexOf(touchpointsAndProjects.get(i).getOriginObject()) != -1 )
					{
						touchpointsList.add( touchpointsList.indexOf(touchpointsAndProjects.get(i).getOriginObject()) + 1, touchpointsAndProjects.get(i) );
						loopFlag = true;
					}
				}
				if (loopFlag)
					loopFlag = touchpointsList.size() != touchpointsAndProjects.size();
			}
		
			return touchpointsList;
		}
	}
	
	public static List<Document> findAllEnabled(boolean includeProjects) {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("enabled", true));	
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));
		critList.add(MessagepointRestrictions.isNull("parentObject"));
		critList.add(MessagepointRestrictions.isNull("channelParentObject"));
		if ( !includeProjects )
			critList.add(MessagepointRestrictions.isNull("checkoutTimestamp"));
		return HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("name"));
	}

    public static List<Document> findAllRemovedOrTrash() {
        List<MessagepointCriterion> critList = new ArrayList<>();
        critList.add(MessagepointRestrictions.or(
            MessagepointRestrictions.eq("removed", true),
            MessagepointRestrictions.eq("trashTouchpoint", true))
        );
        return HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("name"));
    }

    public static List<Document> findAllRemovedOrTrashOrDisabled() {
        List<MessagepointCriterion> critList = new ArrayList<>();
        critList.add(MessagepointRestrictions.or(
                MessagepointRestrictions.eq("enabled", false),
                MessagepointRestrictions.eq("removed", true),
                MessagepointRestrictions.eq("trashTouchpoint", true))
        );
        return HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("name"));
    }

	public static List<Document> findAllEnabledForInserts() {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("enabled", true));
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));
		critList.add(MessagepointRestrictions.isNotNull("insertParameterGroup"));
		critList.add(MessagepointRestrictions.isNull("parentObject"));
		critList.add(MessagepointRestrictions.isNull("channelParentObject"));
		critList.add(MessagepointRestrictions.isNull("checkoutTimestamp"));
		return HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("name"));
	}
	
	// Note: consider replacement with existing findAllVisible logic
	public static List<Long> findAllVisibleProjectIds() {
        String hql = "SELECT DISTINCT d.id FROM Workgroup wg " +
				"INNER JOIN wg.zones AS z " +
				"INNER JOIN z.document AS d " +
				"WHERE d.enabled = true " +
				"AND wg.id = :workgroupId " +
				"AND z.enabled = true ";

		Map<String, Object> params = new HashMap<>();
		params.put("workgroupId", UserUtil.getPrincipalUser().getWorkgroupId());
        return new ArrayList<>((List<Long>) HibernateUtil.getManager().getObjectsAdvanced(hql, params));
	}
	public static List<Document> findAllVisible() {
		return findAllVisible(false);
	}

	public static boolean existsDocumentsOrProjectsVisible() {
		return !findAllDocumentsAndProjectsVisible().isEmpty();
	}

	public static List<Document> findAllDocumentsAndProjectsVisible() {
		return findAllVisible(true);
	}
	public static List<Document> findAllDocumentsAndProjectsVisible(boolean sortByHierarchy) {
		if ( !sortByHierarchy) {
			return findAllVisible(true);
		} else {
			List<Document> touchpointsList = Document.findAllVisible(false);
			List<Document> touchpointsAndProjects = Document.findAllDocumentsAndProjectsVisible();
			
			boolean loopFlag = touchpointsList.size() != touchpointsAndProjects.size();
			while (loopFlag) {
				loopFlag = false;
				for ( int i = touchpointsAndProjects.size() - 1; i >= 0; i--)
				{
					if ( touchpointsList.indexOf(touchpointsAndProjects.get(i)) == -1 && 
						touchpointsAndProjects.get(i).getOriginObject() != null && 
						touchpointsList.indexOf(touchpointsAndProjects.get(i).getOriginObject()) != -1 )
					{
						touchpointsList.add( touchpointsList.indexOf(touchpointsAndProjects.get(i).getOriginObject()) + 1, touchpointsAndProjects.get(i) );
						loopFlag = true;
					}
				}
				if (loopFlag)
					loopFlag = touchpointsList.size() != touchpointsAndProjects.size();
			}
		
			return touchpointsList;
		}
	}
	
	@SuppressWarnings("unchecked")
	public static List<Document> findAllVisible(boolean includeProjects) {
		String hql = "SELECT DISTINCT d FROM Workgroup wg " +
						"INNER JOIN wg.zones AS z " +
						"INNER JOIN z.document AS d " +
						"WHERE d.enabled = true " +
						"AND z.enabled = true " +
						"AND d.channelParentObject IS NULL " +
						"AND d.parentObject IS NULL ";
		
		if(UserUtil.getPrincipalUser() != null) {
		    hql +=  "AND wg.id = :workgroupId "; // UserUtil.getPrincipalUser();
		}
		if ( !includeProjects )
			hql += 		"AND d.checkoutTimestamp IS NULL ";
		
		hql +=			"ORDER BY d.name ";

		Map<String, Object> params = new HashMap<>();
        if(UserUtil.getPrincipalUser() != null) {
            params.put("workgroupId", UserUtil.getPrincipalUser().getWorkgroupId());
        }
		
		List<Document> docs = (List<Document>)HibernateUtil.getManager().getObjectsAdvanced(hql, params);
		
		// Filter projects if parent is disabled
		if ( includeProjects ) {
			List<Document> projectsHiddenByDisabledParent = new ArrayList<>();
			for ( Document currentDocument : docs ) {
				Document doc = currentDocument;
				while ( doc.getOriginObject() != null && doc.getCheckoutTimestamp() != null ) {
					if ( !((Document)doc.getOriginObject()).isEnabled() ) {
						projectsHiddenByDisabledParent.add(currentDocument);
						break;
					}
					doc = (Document)doc.getOriginObject();
				}
			}
			docs.removeAll(projectsHiddenByDisabledParent);
		}
		
		return docs;
	}
	
	public static List<Document> findAllIncludeAlternates() {
		List<Document> documents = new ArrayList<>();
		List<Document> primaryDocuments = findAll(true);
		for ( Document currentPrimary: primaryDocuments ) {
			documents.add(currentPrimary);
            documents.addAll(currentPrimary.getAlternateLayouts());
		}
		
		return documents;
	}
	
	public static List<Document> findAllVisibleAndEnabledForInserts(){
		List<Document> allVisible = findAllDocumentsAndProjectsVisible();
		List<Document> allVisibleAndEnabledForInserts = new ArrayList<>();
		for ( Document doc : allVisible ) {
			if ( doc.isEnabledForInserts() )
				allVisibleAndEnabledForInserts.add(doc);
		}
		return allVisibleAndEnabledForInserts;
	}	
	
	public boolean isEnabledForInserts() {
		return this.getInsertParameterGroup() != null;
	}

	public boolean isEnabledForDXFOutput() {
        if (this.getConnectorConfiguration() == null)
            return false;

		return getConnectorConfiguration().getQualificationOutput().getId() == QualificationOutput.OUTPUT_TYPE_DXF;
	}

	public boolean isEnabledForHtmlOutput() {
        if (this.getConnectorConfiguration() == null)
            return false;

		return getConnectorConfiguration().getQualificationOutput().getId() == QualificationOutput.OUTPUT_TYPE_HTML;
	}

	public boolean hasZoneEnabledForHtmlOutput() {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("document.id", this.getRootDocument().getId()));
		critList.add(MessagepointRestrictions.eq("htmlOutput", true));
		List<Zone> htmlEnabledZones = HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList);
		return !htmlEnabledZones.isEmpty();
	}

	public boolean hasZoneEnabledForDxfOutput() {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("document.id", this.getRootDocument().getId()));
		critList.add(MessagepointRestrictions.eq("dxfOutput", true));
		List<Zone> dxfEnabledZones = HibernateUtil.getManager().getObjectsAdvanced(Zone.class, critList);
		return !dxfEnabledZones.isEmpty();
	}
	
	public boolean isChannelRoot() {
		return (this.isChannelAlternate() && this.getParent() == null) || this.getRootDocument().getId() == this.getId();
	}

	public static List<Document> findAllEnabledForTouchpointSelectionsByType(int touchpointSelectionTypeId) {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("enabled", true));
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));
		critList.add(MessagepointRestrictions.eq("selectionTypeId", touchpointSelectionTypeId));
		return HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("name"));
	}

	public static List<Document> findByRateScheduleCollection(RateScheduleCollection rateScheduleCollection) {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("defaultRateScheduleCollection", rateScheduleCollection));
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));
		return HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("name"));
	}

	public InsertSchedule getDefaultInsertSchedule() {		
		List<InsertSchedule> defaultInsertSchedules =  HibernateUtil.getManager().getObjectsAdvanced(InsertSchedule.class, MessagepointRestrictions.eq("defaultSchedule", true));
		for (InsertSchedule insertSchedule : defaultInsertSchedules) {
			if (insertSchedule.getScheduleCollection() != null && insertSchedule.getScheduleCollection().getDocument() != null) {
				if (insertSchedule.getScheduleCollection().getDocument().getId() == this.getId()) {
					return insertSchedule;
				}
			}
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	public TouchpointSelection getMasterTouchpointSelection() {
		Map<String, Object> params = new HashMap<>();
		params.put("documentId", this.getRootDocument().getId());		
		params.put("archiveTypeId", ArchiveType.ID_DELETED);		
		
		StringBuilder query = new StringBuilder("	SELECT 			ts ");
		query.append("							FROM 			TouchpointSelection ts ");
		query.append("							WHERE			ts.document.id = :documentId ");
		query.append("							AND				ts.archiveTypeId != :archiveTypeId ");
		query.append("							AND				ts.parameterGroupTreeNode IS NOT NULL ");
		query.append("							AND				ts.parameterGroupTreeNode.parentNode IS NULL ");
		
		List<TouchpointSelection> result = (List<TouchpointSelection>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params, 1, 1, "ORDER BY ts.id DESC");
		
		if(result != null && !result.isEmpty())
			return result.get(0);
		return null;
	}

	public TouchpointSelection getMasterTouchpointSelectionDuringImport() {
		if (this.getVisibleTouchpointSelections() != null) {
			for (TouchpointSelection touchpointSelection : this.getVisibleTouchpointSelections()) {
				if (touchpointSelection != null && !touchpointSelection.isDeleted() && touchpointSelection.getParameterGroupTreeNode() != null && touchpointSelection.getParameterGroupTreeNode().getParentNode() == null) {
					return touchpointSelection;
				}
			}
		}
		return null;
	}

	public boolean isInsertSelectorEditable() {
		if (this.getInsertParameterGroup() == null) {
			return true;
		} else {
			List<InsertSchedule> insertSchedules = InsertSchedule.findByDocumentHavingSelection(this);
			if (insertSchedules != null && !insertSchedules.isEmpty()) {
				return false;
			}
			return true;
		}
	}
	public boolean isSelectorEditable() {
		if (this.getSelectionParameterGroup() == null) {
			return true;
		} else {
			if (this.getVisibleTouchpointSelections().size() > 1) {
				// Make sure the selections are not archived deleted (Remove from the touchpoint_variant_list)
				Set<TouchpointSelection> touchpointSelections = this.getVisibleTouchpointSelections();
				Iterator<TouchpointSelection>  iter = touchpointSelections.iterator();
				boolean allArchivedDeleted = true;
				while(iter.hasNext()){
					TouchpointSelection tpSelection = iter.next();
					if(tpSelection.getArchiveTypeId()!=ArchiveType.ID_DELETED && !tpSelection.isMaster()){
						allArchivedDeleted = false;
						break;
					}
				}				
				if(!allArchivedDeleted){
					return false;
				}
			}
			
			if (TouchpointSelection.ownsNotRemovedContentObjects(this.getVisibleTouchpointSelections())) {
				return false;
			}
			
			return true;
		}
	}

	public List<Long> getTouchpointSelectionTreeNodeIds() {
		List<Long> pgTreeNodeIds = new ArrayList<>();
		for (TouchpointSelection touchpointSelection : this.getVisibleTouchpointSelections()) {
			if (touchpointSelection.getParameterGroupTreeNode() != null) {
				pgTreeNodeIds.add(touchpointSelection.getParameterGroupTreeNode().getId());
			}
		}
		return pgTreeNodeIds;
	}
	
	public List<Long> getLanguageSelectionTreeNodeIds() {
		List<Long> pgTreeNodeIds = new ArrayList<>();
		for (LanguageSelection languageSelection : this.getLanguageSelections()) {
			if (languageSelection.getParameterGroupTreeNode() != null) {
				pgTreeNodeIds.add(languageSelection.getParameterGroupTreeNode().getId());
			}
		}
		return pgTreeNodeIds;
	}
 
	public static List<Document> findByParameterGroup(ParameterGroup parameterGroup) {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions. or(
						MessagepointRestrictions.eq("selectionParameterGroup", parameterGroup),
						(MessagepointRestrictions.or(
								MessagepointRestrictions.eq("insertParameterGroup", parameterGroup),
								MessagepointRestrictions.eq("languageParameterGroup", parameterGroup))
						)
					));
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));
		List<Document> documents = HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("name"));
		if (documents != null) {
			return documents;
		} else {
			return new ArrayList<>();
		}
	}

	public boolean isVisible() {
		User principle = UserUtil.getPrincipalUser();
		User user = User.findById(principle.getId());
		return isVisible(user.getWorkgroup());
	}

	public boolean isVisible(User user) {
		return isVisible(user.getWorkgroup());
	}

	public boolean isVisible(Workgroup workgroup) {
		if (workgroup != null) {
			
			if (!findZonesFilteredByWorkgroup(workgroup.getId()).isEmpty())
			{
				return true;				
			}
			
		}
		return false;
	}
	
	// ******** Start - Language Selection APIs ********
	public LanguageSelection getMasterLanguageSelection() {
		if (getLanguageSelections() != null) {
			for (LanguageSelection languageSelection : getLanguageSelections()) {
				if (languageSelection != null && languageSelection.getParameterGroupTreeNode() != null && languageSelection.getParameterGroupTreeNode().getParentNode() == null) {
					return languageSelection;
				}
			}
		}
		return null;
	}

	public boolean getHasLanguageSelections() {
		return this.getLanguageSelections().size() > 1;
	}
	public boolean isLanguageSelectorEditable() {
		return !getHasLanguageSelections();
	}
	public boolean isEnabledForLanguageSelection() {
		return this.getLanguageParameterGroup() != null;
	}
	// ******** End - Language Selection APIs ********

    // ******** Start - Formatting Selection APIs ********
    public FormattingSelection getMasterFormattingSelection() {
        if (getLanguageSelections() != null) {
            for (FormattingSelection formattingSelection : getFormattingSelections()) {
                if (formattingSelection != null && formattingSelection.getParameterGroupTreeNode() != null && formattingSelection.getParameterGroupTreeNode().getParentNode() == null) {
                    return formattingSelection;
                }
            }
        }
        return null;
    }

    public boolean getHasFormattingSelections() {
        return this.getFormattingSelections().size() > 1;
    }
    public boolean isFormattingSelectorEditable() {
        return !getHasFormattingSelections();
    }
    public boolean isEnabledForFormattingSelection() {
        return this.getLanguageParameterGroup() != null;
    }
    // ******** End - Formatting Selection APIs ********
	
	@SuppressWarnings("unchecked")
	public List<ReferencableObject> getDirectReferences() {
		ServiceExecutionContext context = DirectReferencesFetchService.createContext(getId(), Document.class);
		Service service = MessagepointServiceFactory.getInstance().lookupService(DirectReferencesFetchService.SERVICE_NAME, DirectReferencesFetchService.class);
		service.execute(context);
		if (!context.getResponse().isSuccessful()) {
			StringBuilder sb = new StringBuilder();
			sb.append(DirectReferencesFetchService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" for object ").append(this.getClass().getName());
			sb.append(" id=").append(this.getId());
			throw new RuntimeException(sb.toString());
		}
		return (List<ReferencableObject>) context.getResponse().getResultValueBean();
	}

	public boolean isReferenced() {
		List<ReferencableObject> refs = this.getDirectReferences();
		if(refs != null && !refs.isEmpty())
			return true;
		return false;
	}

	// Touchpoint Languages

	public Set<TouchpointLanguage> getTouchpointLanguages() {
		if ( this.getChannelParent() == null ) {
			if ( this.getParent() != null ) {
				touchpointLanguages.clear();
				touchpointLanguages.addAll(this.getParent().getTouchpointLanguages());
			}
		} else {
			touchpointLanguages.clear();
			touchpointLanguages.addAll(this.getChannelParent().getTouchpointLanguages());
		}
		return touchpointLanguages;
	}
	public void setTouchpointLanguages(Set<TouchpointLanguage> touchpointLanguages) {
		this.touchpointLanguages = touchpointLanguages;
	}

    public String getDictionaryIdentifier() {
        return dictionaryIdentifier;
    }

    public void setDictionaryIdentifier(String dictionaryIdentifier) {
        this.dictionaryIdentifier = dictionaryIdentifier;
    }

    public void replaceTouchpointLanguages(Set<TouchpointLanguage> touchpointLanguages) {
		if (this.touchpointLanguages != null)
		{
			this.touchpointLanguages.clear();
			this.touchpointLanguages.addAll(touchpointLanguages);
		}
		else
			this.touchpointLanguages = touchpointLanguages;
	}

	public TouchpointLanguage getDefaultTouchpointLanguage()
	{
		if (this.getChannelParent() != null)
			return this.getChannelParent().getDefaultTouchpointLanguage();

		if (this.getParent() != null)
			return this.getParent().getDefaultTouchpointLanguage();

		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add( MessagepointRestrictions.eq("isDefaultLanguage", true));
		critList.add( MessagepointRestrictions.eq("document", this));

		return HibernateUtil.getManager().getObjectUnique(TouchpointLanguage.class, critList);
	}

	public MessagepointLocale getDefaultTouchpointLanguageLocale() {
		TouchpointLanguage touchpointLanguage = getDefaultTouchpointLanguage();
		if (touchpointLanguage == null) return null;
		return touchpointLanguage.getMessagepointLocale();
	}

	public String getDefaultTouchpointLanguageCode() {
		TouchpointLanguage touchpointLanguage = getDefaultTouchpointLanguage();
		if (touchpointLanguage == null) return null;
		return touchpointLanguage.getLanguageCode();
	}

	public String getDefaultTouchpointLanguageLocaleCode() {
		TouchpointLanguage touchpointLanguage = getDefaultTouchpointLanguage();
		if (touchpointLanguage == null) return null;
		return touchpointLanguage.getMessagepointLocale().getCode();
	}

	public MessagepointLocale getTouchpointLanguageLocale(String langCode)
	{
		MessagepointLocale languageLocale = null;
		
		for (TouchpointLanguage touchpointLanguage : this.getTouchpointLanguages())
		{
			if (touchpointLanguage.getLanguageCode().equalsIgnoreCase(langCode))
			{
				languageLocale = touchpointLanguage.getMessagepointLocale();
				break;
			}
		}
		
		return languageLocale;
	}

	public void addTouchpointLanguage(MessagepointLocale messagepointLocale) 
	{
		TouchpointLanguage tpLanguage = new TouchpointLanguage();
		
		tpLanguage.setDocument(this);
		tpLanguage.setMessagepointLocale(messagepointLocale);
		tpLanguage.setIsDefaultLanguage(false);
		tpLanguage.setName(messagepointLocale.getName());
		
		this.getTouchpointLanguages().add(tpLanguage);		
	}
	
	public void addDefaultTouchpointLanguage(MessagepointLocale messagepointLocale) 
	{
		TouchpointLanguage tpLanguage = new TouchpointLanguage();
		
		tpLanguage.setDocument(this);
		tpLanguage.setMessagepointLocale(messagepointLocale);
		tpLanguage.setIsDefaultLanguage(true);
		tpLanguage.setName(messagepointLocale.getName());
		
		this.getTouchpointLanguages().add(tpLanguage);		
	}
	
	/*  getTouchpointLanguagesAsLocales() from database

		List<MessagepointCriterion> critList = new ArrayList<MessagepointCriterion>();
		critList.add( MessagepointRestrictions.eq("document.id", docId));

		List<TouchpointLanguage> touchpointLanguages = HibernateUtil.getManager().getObjectsAdvanced(TouchpointLanguage.class, critList, MessagepointOrder.asc("name"));

		List<MessagepointLocale> messagepointLocales = new ArrayList<MessagepointLocale>();
		for (TouchpointLanguage touchpointLanguage : touchpointLanguages)
		{
			messagepointLocales.add(touchpointLanguage.getMessagepointLocale());
		}
	 */

	public List<MessagepointLocale> getTouchpointLanguagesAsLocales() {
		List<MessagepointLocale> messagepointLocales = new ArrayList<>();
        try {
            for (TouchpointLanguage touchpointLanguage : this.getTouchpointLanguages()) {
                if (touchpointLanguage.isDefaultLanguage())
                    messagepointLocales.add(touchpointLanguage.getMessagepointLocale());
            }
            for (TouchpointLanguage touchpointLanguage : getTouchpointLanguagesSortedByLanguageName()) {
                if (!touchpointLanguage.isDefaultLanguage())
                    messagepointLocales.add(touchpointLanguage.getMessagepointLocale());
            }
        } catch (Exception ex){

            log.error("Unable to retrieve touchpoint languages as locale for document " + this.getName());
        }
		return messagepointLocales;
	}

	public List<MessagepointLocale> getSystemDefaultAndTouchpointLanguagesAsLocales() {
		List<MessagepointLocale> messagepointLocales = new ArrayList<>();

		MessagepointLocale systemLanguageLocale = MessagepointLocale.getDefaultSystemLanguageLocale();
		messagepointLocales.add(systemLanguageLocale);

		for (TouchpointLanguage touchpointLanguage : getTouchpointLanguagesSortedByLanguageName())
		{
			if (touchpointLanguage.getMessagepointLocale().getId() != systemLanguageLocale.getId())
				messagepointLocales.add(touchpointLanguage.getMessagepointLocale());
		}
		return messagepointLocales;
	}

	public boolean getIsMultiLanguage() {
		return getTouchpointLanguages().size() > 1;
	}
	
	public Set<TouchpointLanguage> getTouchpointLanguagesSortedByLanguageCode() {

		HashMap<String, TouchpointLanguage> unSortedLanguages = new HashMap<>();
		
		for (TouchpointLanguage language: this.getTouchpointLanguages()) 
			unSortedLanguages.put(language.getLanguageCode(), language);
		
		List<String> mapKeys = new ArrayList<>(unSortedLanguages.keySet());
		List<TouchpointLanguage> mapValues = new ArrayList<>(unSortedLanguages.values());
		
		Set<TouchpointLanguage> sortedLanguages = new LinkedHashSet<>();
	    
	    TreeSet<String> sortedSet = new TreeSet<>(mapKeys);
	    Object[] sortedArray = sortedSet.toArray();
	    int size = sortedArray.length;
	    
	    for (int i = 0; i < size; i++)
	    {
	    	sortedLanguages.add(mapValues.get(mapKeys.indexOf(sortedArray[i])));
	    }
	    
		return sortedLanguages;
	}
	
	public Set<TouchpointLanguage> getTouchpointLanguagesSortedByLanguageName() {
        List<TouchpointLanguage> touchpointLanguagesList = new ArrayList<>(this.getTouchpointLanguages());

        // Sort the List by language name
        Collections.sort(touchpointLanguagesList, (tp1, tp2) -> tp1.getName().compareTo(tp2.getName()));

        // Convert the sorted List back to a LinkedHashSet to maintain the order
        return new LinkedHashSet<>(touchpointLanguagesList);
	}
	
	public static List<Document> getVisibleDocuments() {

		return getVisibleDocuments(UserUtil.getPrincipalUser());
	}

    public static List<Document> getVisibleDocuments(User user) {
        if (user == null)
            return new ArrayList<>();

        String sql = "SELECT DISTINCT d.* FROM WORKGROUP_ZONE wz\n" +
                "LEFT JOIN ZONE z ON wz.zone_id = z.id\n" +
                "LEFT JOIN DOCUMENT d ON z.document_id = d.id\n" +
                "WHERE d.enabled = true AND d.removed = false AND d.is_trash_tp = false AND wz.workgroup_id = :workgroupid";

        NativeQuery<Document> query = HibernateUtil.getManager().getSession().createNativeQuery(sql);
        query.setParameter("workgroupid", user.getWorkgroupId());
        query.addEntity(Document.class);
        return query.list();
    }
	
	public static boolean hasVisibleWebOrEmailDocument(){
		for(Document doc : getVisibleDocuments()){
			if(doc.isWebTouchpoint() || doc.isEmailTouchpoint())
				return true;
		}
		return false;
	}

	public static boolean hasVisibleSefasDocument(){
		for(Document doc : getVisibleDocuments()){
			if(doc.getIsSefasCompositionTouchpoint() || doc.getIsMPHCSCompositionTouchpoint())
				return true;
		}
		return false;
	}
	
	public boolean isZoneDataGroupMissing()
	{
		List<Zone> zoneList = Zone.findByDocumentIdOrderByName(this.getId());
		for ( Zone zone : zoneList ) {
			if ( zone.getDataGroup() == null &&
					FALSE.equals(zone.getMixedDataGroups()) &&
					zone.getZoneTypeId() != ZoneType.ID_PLACEHOLDER_ZONE &&
					zone.getZoneTypeId() != ZoneType.ID_EMAIL_SUBJECT_LINE_ZONE &&
					zone.getZoneTypeId() != ZoneType.ID_WEB_TITLE_LINE_ZONE &&
					zone.getZoneTypeId() != ZoneType.ID_SMS_ZONE &&
					zone.getZoneTypeId() != ZoneType.ID_INTERACTIVE_ZONE )
				return true;
		}
		return false;
	}
	
	public boolean hasInteractiveZone(){
		for(Zone zone : this.getZones()){
			if(zone.getZoneTypeId() == ZoneType.ID_INTERACTIVE_ZONE){
				return true;
			}
		}
		return false;
	}
	
	public boolean isSomeReferencedVariableNotBridged()
	{
		Set<Long> variablesNotBridged = this.getReferencedVariableIdsNotBridged(true);
		
		if (variablesNotBridged != null && !variablesNotBridged.isEmpty())
			return true;
		
		return false;
	}
	
	public Set<DataElementVariable> getReferencedVariablesNotBridged(boolean returnImmediately)
	{
		Set<DataElementVariable> variablesNotBridged = new HashSet<>();

		// Log log = LogUtil.getSystemPerformanceLogger();
		// long startTime = System.currentTimeMillis();
		
		Set<Long> variableIds = getReferencedVariableIdsNotBridged(returnImmediately);
		
		for (long id : variableIds)
		{
			variablesNotBridged.add(DataElementVariable.findById(id));
		}

		// long endTime = System.currentTimeMillis();
		// log.info("getReferencedVariablesNotBridged TIME TAKEN: <" + (endTime - startTime) + ">");
				
		return variablesNotBridged;
	}
	
	public Set<Long> getReferencedVariableIdsNotBridged(boolean returnImmediately)
	{
		Set<Long> variableIdsNotBridged = new HashSet<>();
		
		DataSourceAssociation dsa = this.getDataSourceAssociation();
		
		if (dsa == null)
			return variableIdsNotBridged;
		
		List<DataElementVariable> variables = DataElementVariable.findAllForNotDocument(this);
		
		if (variables == null || variables.size() <= 0)
			return variableIdsNotBridged;
		
		Set<Long> documentVariableIds = new HashSet<>();
		
		if (this.getCustomerReportingVariableA() != null)
			documentVariableIds.add(this.getCustomerReportingVariableA().getId()); // Q65
		
		if (this.getCustomerReportingVariableB() != null)
			documentVariableIds.add(this.getCustomerReportingVariableB().getId()); // Q65
		
		if (!this.getExtReportingDataVariables().isEmpty())
		{
			for (DataElementVariable var : this.getExtReportingDataVariables())
			{
				documentVariableIds.add(var.getId()); // Q46
			}
		}
		
		documentVariableIds.addAll(DataElementVariable.findAllVariableIdsUsedInChannelConfiguration(this)); // Q21, Q54
		documentVariableIds.addAll(DataElementVariable.findAllVariableIdsUsedInAttachment(this)); 			// Q24
		documentVariableIds.addAll(DataElementVariable.findAllVariableIdsUsedInDataCollection(dsa)); 		// Q25
		documentVariableIds.addAll(DataElementVariable.findAllVariableIdsUsedInMessages(this)); 			// Q49
		documentVariableIds.addAll(DataElementVariable.findAllVariableIdsUsedInSmartTexts(this)); 		// Q50

		// Selectors (Parameter)
		documentVariableIds.addAll(DataElementVariable.findAllVariableIdsUsedInSelectors(this)); 

		// Target Groups (ConditionElement and FilterCondition)
		documentVariableIds.addAll(DataElementVariable.findAllVariableIdsUsedInConditionElement(this)); 
		documentVariableIds.addAll(DataElementVariable.findAllVariableIdsUsedInFilterCondition(this)); 
		documentVariableIds.addAll(DataElementVariable.findAllVariableIdsUsedInConditionElementAttributes(this)); 

		if (!documentVariableIds.isEmpty())
		{
			for (DataElementVariable variable : variables)
			{
				if (documentVariableIds.contains(variable.getId()))
				{
					variableIdsNotBridged.add(variable.getId());
					if (returnImmediately)
						return variableIdsNotBridged;
					continue;
				}
			}
		}
			
		return variableIdsNotBridged;
	}

	public void setEnabledForVariantWorkflow(boolean value)
	{
		if (value)
			this.setSelectionWorkflowTypeId(1);
		else
			this.setSelectionWorkflowTypeId(0);
	}
	
	public boolean isEnabledForVariantWorkflow()
	{
		if (this.getSelectionParameterGroup() != null && this.getSelectionWorkflowTypeId() == 1)
			return true;
		
		return false;
	}

	public List<TextStyleVO> getTextStyles() {
		if (getZones().isEmpty())
			return new ArrayList<>();

		HashSet<TextStyle> styles = new HashSet<>();

		for (Zone zone : getZones()) {
			zone = Zone.findZoneInContextByParent(zone);
			
			Set<TextStyle> zoneStyleSet = zone.getStyles();
			if ( zoneStyleSet != null && !zoneStyleSet.isEmpty() ) {
				for ( TextStyle currentStyle: zoneStyleSet )
					if ( !styles.contains(currentStyle) )
						styles.add(currentStyle);
			}
		}
		List<TextStyleVO> stylesList = new ArrayList<>();
		for ( TextStyle textStyle : styles )
			stylesList.add( new TextStyleVO(textStyle, this) );

		Collections.sort(stylesList, new TextStyleVOComparator());
		return stylesList;
	}
	
	public List<ParagraphStyleVO> getParagraphStyles() {
		if (getZones().isEmpty())
			return new ArrayList<>();

		HashSet<ParagraphStyle> styles = new HashSet<>();

		for (Zone zone : getZones()) {
			zone = Zone.findZoneInContextByParent(zone);
			
			Set<ParagraphStyle> zoneStyleSet = zone.getParagraphStyles();
			if ( zoneStyleSet != null && !zoneStyleSet.isEmpty() ) {
				for ( ParagraphStyle currentStyle: zoneStyleSet )
					if ( !styles.contains(currentStyle) )
						styles.add(currentStyle);
			}
		}
		List<ParagraphStyleVO> stylesList = new ArrayList<>();
		for ( ParagraphStyle paragraphStyle : styles )
			stylesList.add( new ParagraphStyleVO(paragraphStyle, this) );

		Collections.sort(stylesList, new ParagraphStyleVOComparator());
		return stylesList;
	}
	
	public int getProjectDepth() {
		int depth = 0;

		Document currentTouchpoint = this;
		while ( currentTouchpoint.getOriginObject() != null && currentTouchpoint.getCheckoutTimestamp() != null ) {
			depth++;
			currentTouchpoint = (Document)currentTouchpoint.getOriginObject();
		}

		return depth;
	}
	
	public boolean getHasEnabledVersion() {
    	String[] queries = {
        		"FROM Document AS d WHERE d.enabled = true AND d.checkoutTimestamp IS NOT NULL AND d.originObject.id = :documentId"
        	};
    	
    	if (this.getCheckoutTimestamp() != null)
    		return true;
    	
    	Map<String, Object> params = new HashMap<>();
    	params.put("documentId", this.getId());
    	List<?> list = null;
    	for ( String query : queries ) {
	    	list = HibernateUtil.getManager().getObjectsAdvanced(query, params);
	    	if ( !list.isEmpty() ) return true;
    	}
    	return false;
	}
	
	public boolean getHasAppliedTouchpointMetadata() {
    	String[] queries = {
        		"FROM Document AS d WHERE d.metadataForm IS NOT NULL AND d.id = :documentId"
        	};
    		
    	Map<String, Object> params = new HashMap<>();
    	params.put("documentId", this.getId());
    	List<?> list = null;
    	for ( String query : queries ) {
	    	list = HibernateUtil.getManager().getObjectsAdvanced(query, params);
	    	if ( !list.isEmpty() ) return true;
    	}
    	return false;
	}
	public boolean getHasAppliedVariantMetadata() {
    	String[] queries = {
        		"FROM TouchpointSelection AS ts WHERE ts.metadataForm IS NOT NULL AND ts.document.id = :documentId"
        	};
    		
    	Map<String, Object> params = new HashMap<>();
    	params.put("documentId", this.getId());
    	List<?> list = null;
    	for ( String query : queries ) {
	    	list = HibernateUtil.getManager().getObjectsAdvanced(query, params);
	    	if ( !list.isEmpty() ) return true;
    	}
    	return false;
	}
	
	public static Document getDefaultDocument() {
		if (!(UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_VIEW) || UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_VIEW)))
			return null;
		List<Document> visibleDocuments = Document.findAllDocumentsAndProjectsVisible();
		for (Document document: visibleDocuments)
			if (document.isEnabledForVariation())
				return document;
		return null;
	}

	public Zone getEmailSubjectLineZone() {
		if ( this.isEmailTouchpoint() || this.getIsOmniChannel() ) {
			for ( Zone currentZone: zones ) {
				if ( currentZone.isEmailSubjectLine() )
					return currentZone;
			}
		}
		return null;
	}
	
	public Zone getWebTitleZone() {
		if ( this.isWebTouchpoint() || this.getIsOmniChannel() ) {
			for ( Zone currentZone: zones ) {
				if ( currentZone.isWebTitleLine() )
					return currentZone;
			}
		}
		return null;
	}

	public boolean getTrashTouchpoint() {
		return trashTouchpoint;
	}

	public void setTrashTouchpoint(boolean trashTouchpoint) {
		this.trashTouchpoint = trashTouchpoint;
	}


    public static Document findByDnaAndParentDocument(String dna, Document document) {
        Document result = null;

        ArrayList<MessagepointCriterion> critList = new ArrayList<>();
        critList.add(MessagepointRestrictions.eq("parentObject.id", document.getId()));
        critList.add(MessagepointRestrictions.eq("dna", dna));
        List<Document> orderedList = HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("id"));
        if (orderedList != null && !orderedList.isEmpty())
            result = orderedList.get(0);

        return result;
    }

    public static Document findByDnaAndParentDocument(Document fellow, Document document) {
        Document result = null;
        if (document != null && fellow != null) {
            String dna = fellow.getDna();
            result = findByDnaAndParentDocument(dna, document);
        }

        return result;
    }
    
    public static Document findByDna(String dna) {
        Document result = null;
        if (dna != null ) {
            ArrayList<MessagepointCriterion> critList = new ArrayList<>();
            critList.add(MessagepointRestrictions.eq("dna", dna));
            List<Document> orderedList = HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("id"));
            if (orderedList != null && !orderedList.isEmpty())
                result = orderedList.get(0);
        }

        return result;
    }
    
    public static List<Document> findFamilyDocuments(Document document) {
        return Document.findAllDocumentsAndProjectsVisible(true).stream().sequential().filter(other->{
            if(document ==  null){
            	return false;
			}

        	if(! other.getDna().equals(document.getDna())) {
                return false;
            }
            
            if(other.getId() == document.getId() && other.getObjectSchemaName().equals(document.getObjectSchemaName())) {
                return false;
            }
            
            return true;
        }).collect(Collectors.toList());    
    }

	public ConfigurableWorkflow getMessageWorkflow() {
		return messageWorkflow;
	}

	public void setMessageWorkflow(ConfigurableWorkflow messageWorkflow) {
		this.messageWorkflow = messageWorkflow;
	}

	public boolean isCommunicationZoneContentTransient() {
		return communicationZoneContentTransient;
	}
	
	public void setCommunicationZoneContentTransient(boolean communicationZoneContentTransient) {
		this.communicationZoneContentTransient = communicationZoneContentTransient;
	}
	
	public List<DataGroup> getDataGroupsForDocument() {
		List<DataGroup> dataGroups = new ArrayList<>();
		
		DataGroup documentDataGroup = getDataGroup();
		if ( documentDataGroup != null ) {
			List<DataGroup> documentDataGroups = new ArrayList<>();
			
			if ( documentDataGroup.getLevel() != DataRecordLevel.FIELD_VALUE_0)
				documentDataGroups.add(documentDataGroup);
			documentDataGroups.addAll( documentDataGroup.getAllDescendents() );
			dataGroups = documentDataGroups;
			
		} else {

			DataSource dataSource = getPrimaryDataSource();
			if ( dataSource != null )
		    	if ( dataSource.isCustomerDriverFile() )
		    		dataGroups = dataSource.getDataGroupsForZones();

		}

        //This section gets level and stores it in a map first before sorting to increase performance
        List<Map.Entry<DataGroup, Integer>> dataGroupMapList = new ArrayList<>();
        for (DataGroup dataGroup : dataGroups) {
            dataGroupMapList.add(new AbstractMap.SimpleEntry<>(dataGroup, dataGroup.getLevel()));
        }

        dataGroupMapList.sort((entry1, entry2) -> {
            int compare = entry1.getValue().compareTo(entry2.getValue());
            if (compare == 0) {
                return entry1.getKey().getName().compareToIgnoreCase(entry2.getKey().getName());
            }
            return compare;
        });

        dataGroups.clear();
        for (Map.Entry<DataGroup, Integer> entry : dataGroupMapList) {
            dataGroups.add(entry.getKey());
        }

		return dataGroups;
	}
	
	public static List<Document> getConnectedEnabledDocuments() {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
        critList.add(MessagepointRestrictions.eq("connectedEnabled", true));
		critList.add(MessagepointRestrictions.eq("enabled", true));	
		critList.add(MessagepointRestrictions.eq("removed", false));
		critList.add(MessagepointRestrictions.eq("trashTouchpoint", false));
		critList.add(MessagepointRestrictions.isNull("parentObject"));
		critList.add(MessagepointRestrictions.isNull("channelParentObject"));

        return HibernateUtil.getManager().getObjectsAdvanced(Document.class, critList, MessagepointOrder.asc("id"));
	}
	
	public boolean isEnabledForAttachments() {
		if ( this.isEmailTouchpoint() )
			return true;
		return false;
	}

	public ConfigurableWorkflow getConnectedWorkflow() {
		return connectedWorkflow;
	}

	public void setConnectedWorkflow(ConfigurableWorkflow connectedWorkflow) {
		this.connectedWorkflow = connectedWorkflow;
	}

	public static String getCommunicationProductionTypeLocalizedString(int type) {
		switch(type) {
			case Document.COMMUNICATION_PRODUCTION_TYPE_ON_APPROVAL:
				return ApplicationUtil.getMessage("page.label.on.approval");
			case Document.COMMUNICATION_PRODUCTION_TYPE_ON_APPROVAL_MINI_BUNDLE:
				return ApplicationUtil.getMessage("page.label.on.approval.mini.bundle");
		}
		return ApplicationUtil.getMessage("page.label.batch");
	}

	public static String getLayoutsDnaDescription(Document d, boolean syncVariantEnabled) {
		StringBuilder layoutsDnaDescription = new StringBuilder();
		layoutsDnaDescription.append(d.getDna());
        if(syncVariantEnabled /*|| "true".equalsIgnoreCase(ApplicationUtil.getProperty(SystemPropertyKeys.Data.KEY_EnableSyncVariants, null))*/) {
            layoutsDnaDescription.append(" enabledForVariantWorkflow:").append(d.isEnabledForVariantWorkflow());
        }
/*
		if(! d.isChannelAlternate()) {
			List<Document> channelAlternateDocuments = d.getChannelAlternateDocuments();
			if(! channelAlternateDocuments.isEmpty()) {
				layoutsDnaDescription.append(" channelAlternates[");
				List<String> channelAlternateDocumentDnas = channelAlternateDocuments.stream().map(cal->cal.getDna()).collect(Collectors.toList());
				Collections.sort(channelAlternateDocumentDnas);
				layoutsDnaDescription.append(channelAlternateDocumentDnas.stream().sequential().collect(Collectors.joining(", ")));
				layoutsDnaDescription.append("]");
			}
		}
		if(! d.isAlternate()) {
			List<Document> alternateLayouts = new ArrayList<>(d.getAlternateLayouts());
			if(! alternateLayouts.isEmpty()) {
				Collections.sort(alternateLayouts, new Comparator<Document>() {
					@Override
					public int compare(Document d1, Document d2) {
						return d1.getDna().compareTo(d2.getDna());
					}
				});
				layoutsDnaDescription.append(" alternateLayouts[");
				layoutsDnaDescription.append(alternateLayouts.stream().sequential().map(al->getLayoutsDnaDescription(al)).collect(Collectors.joining(", ")));
				layoutsDnaDescription.append("]");
			}
		}
 */
		return layoutsDnaDescription.toString();
	}

/*
    public static List<Document> findFamilyDocumentsWithSameVariantWorkflowSetting(Document document) {
        String layoutsDnaDesc = getLayoutsDnaDescription(document);
        return findFamilyDocumentsWithSameVariantWorkflowSetting(document, layoutsDnaDesc);
    }
*/
	public static List<Document> findFamilyDocumentsWithSameVariantWorkflowSetting(Document document, String layoutsDnaDesc, boolean syncVariantEnabled) {
		List<Document> familyDocuments = findFamilyDocuments(document);
		return familyDocuments.stream().filter(d->getLayoutsDnaDescription(d, syncVariantEnabled).equals(layoutsDnaDesc)).collect(Collectors.toList());
	}

	public boolean isBlueRelayConfigured() {
		return nonNull(blueRelayEndpoint) && !blueRelayEndpoint.isEmpty() &&
				nonNull(blueRelayUsername) && !blueRelayUsername.isEmpty() &&
				nonNull(blueRelayToken) && !blueRelayToken.isEmpty() &&
				nonNull(blueRelayTargetFolder) && !blueRelayTargetFolder.isEmpty();
	}

	public static boolean isBlueRelayConfigured(final Long documentId) {
		// when there isn't any TP yet
		if (documentId < 0) return false;
		Document document = Document.findById(documentId);
		return (document != null) && document.isBlueRelayConfigured();
	}

	public static boolean isAnyDocumentBlueRelayConfigured() {
		String query = "select count(*) from document where br_endpoint is not null " +
					"and br_username is not null and br_token is not null and br_target_folder is not null";

		NativeQuery nativeQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		int count = ((Number) nativeQuery.getSingleResult()).intValue();

		return count > 0;
	}

	public List<Zone> getTextZonesList() {
		List<Zone> textZones = new ArrayList<>();
		for ( Zone currentZone: getZones() )
			if ( currentZone.getContentTypeId() == ContentType.TEXT && !currentZone.isPlaceholder() )
				textZones.add( currentZone );
		Collections.sort(textZones, new ZoneComparatorOnFrienlyName());
		return textZones;
	}

	public Map<Long, Integer> getZoneSequenceMap() {
		Map<Long, Integer> sequenceMap = new HashMap<>();

		Map<Long,List<Integer>> explicitSequences = new HashMap<>();
		for  (Zone currentZone: this.getZones() )
			if ( currentZone.getSequence() != null && currentZone.getSequence() > 0 ) {
				if(explicitSequences.containsKey(currentZone.getSection().getId())) {
					List<Integer> sequences = explicitSequences.get(currentZone.getSection().getId());
					sequences.add(currentZone.getSequence());
					explicitSequences.put(currentZone.getSection().getId(), sequences);
				} else {
					List<Integer> sequences = new ArrayList<>();
					sequences.add(currentZone.getSequence());
					explicitSequences.put(currentZone.getSection().getId(), sequences);
				}
			}

		Zone[] orderedZones = this.getZoneArray();
		Arrays.sort(orderedZones, new Comparator<>() {
            public int compare(Zone z1, Zone z2) {
                if (z1.getSection() != null && z2.getSection() != null) {
                    if (z1.getSection().getSectionOrder() > z2.getSection().getSectionOrder()) {
                        return 1;
                    } else if (z1.getSection().getSectionOrder() < z2.getSection().getSectionOrder()) {
                        return -1;
                    } else {
                        if (z1.getTopY() > z2.getTopY()) {
                            return 1;
                        } else if (z1.getTopY() < z2.getTopY()) {
                            return -1;
                        } else {
                            if (z1.getTopX() > z2.getTopX())
                                return 1;
                            else if (z1.getTopX() < z2.getTopX())
                                return -1;
                        }
                    }
                }
                return 0;
            }
        });
		int sequenceIndex = 1;
		for ( int i = 0; i < sortedZoneArray.length; i++ ) {
			if( i>0 && sortedZoneArray[i].getSection() != null &&  sortedZoneArray[i-1].getSection() != null &&  sortedZoneArray[i].getSection().getId() != sortedZoneArray[i-1].getSection().getId()){
				sequenceIndex = 1;
			}
			if ( (sortedZoneArray[i].getZoneTypeId() == ZoneType.ID_DOCUMENT_ZONE || sortedZoneArray[i].getZoneTypeId() == ZoneType.ID_WEB_ZONE ||
					sortedZoneArray[i].getZoneTypeId() == ZoneType.ID_EMAIL_ZONE || sortedZoneArray[i].getZoneTypeId() == ZoneType.ID_INTERACTIVE_ZONE ||
					sortedZoneArray[i].getZoneTypeId() == ZoneType.ID_MARKUP_ZONE)
				&& (sortedZoneArray[i].getSequence() == null || sortedZoneArray[i].getSequence() <= 0) ) {
				List<Integer> sequences = explicitSequences.get(sortedZoneArray[i].getSection().getId());
				while (sequences!=null && sequences.contains( sequenceIndex ) )
					sequenceIndex++;
				sequenceMap.put(sortedZoneArray[i].getId(), sequenceIndex++);
			}
		}

		return sequenceMap;
	}

	public boolean hasDefaultTouchpointLanguage() {
		if (this.getChannelParent() != null)
			return this.getChannelParent().getDefaultTouchpointLanguage() != null;

		if (this.getParent() != null)
			return this.getParent().getDefaultTouchpointLanguage() != null;

		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("isDefaultLanguage", true));
		critList.add(MessagepointRestrictions.eq("document", this));

		TouchpointLanguage tpLanguage =
				HibernateUtil.getManager().getObjectUnique(TouchpointLanguage.class, critList);
		return tpLanguage != null;
	}

    public boolean hasStructuredContent() {
        Map<String, Object> params = new HashMap<>();
        params.put("documentId", this.getId());

        StringBuilder query = new StringBuilder(" FROM ContentObject co ");
        query.append(" INNER JOIN co.document d ");
        query.append(" WHERE d.id = :documentId ");
        query.append(" AND co.structuredContentEnabled = true ");
        query.append(" AND co.removed = false ");

        long count = HibernateUtil.getManager().getPersistedCount(query.toString(), params);
        return count > 0;
    }

}