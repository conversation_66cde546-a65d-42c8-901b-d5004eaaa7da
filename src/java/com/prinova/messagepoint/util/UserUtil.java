package com.prinova.messagepoint.util;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.controller.admin.AccessControlVO;
import com.prinova.messagepoint.controller.admin.UserDeleteObjectType;
import com.prinova.messagepoint.controller.admin.UserSettingsController;
import com.prinova.messagepoint.controller.content.ContentObjectDetailsEditController;
import com.prinova.messagepoint.controller.content.GlobalContentListController;
import com.prinova.messagepoint.controller.insert.InsertScheduleListController;
import com.prinova.messagepoint.controller.touchpoints.SelectionStatusFilterType;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.SystemPropertyKeys.Email;
import com.prinova.messagepoint.model.admin.ApplicationLocale;
import com.prinova.messagepoint.model.admin.Channel;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.SecuritySettings;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.folder.FavoritesFolder;
import com.prinova.messagepoint.model.folder.LastVisitedFolder;
import com.prinova.messagepoint.model.homepage.HomepageWidget;
import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.model.navigation.DropDownMenu;
import com.prinova.messagepoint.model.navigation.NavigationTab;
import com.prinova.messagepoint.model.notification.NotificationActionType;
import com.prinova.messagepoint.model.notification.NotificationEventUtil;
import com.prinova.messagepoint.model.notification.NotificationObjectType;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.security.PasswordRecovery;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.Role;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.tenant.Tenant;
import com.prinova.messagepoint.model.tenant.TenantUtil;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.model.wrapper.AsyncUserDeleteDetailWrapper;
import com.prinova.messagepoint.platform.auth.PINCOpenIdConnectUtil;
import com.prinova.messagepoint.platform.mprest.ApiUser;
import com.prinova.messagepoint.platform.security.PasswordValidator;
import com.prinova.messagepoint.platform.security.SimplePasswordValidationContext;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.admin.UpdateUserService;
import com.prinova.messagepoint.platform.services.common.CreateOrUpdatePasswordRecoveryServiceRequest;
import com.prinova.messagepoint.platform.services.common.ProcessInvalidSigninService;
import com.prinova.messagepoint.platform.services.common.ProcessInvalidSigninServiceResponse;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.tag.layout.BannerTemplateTag;
import com.prinova.messagepoint.validator.MessagepointInputValidationEntry;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.hibernate.Hibernate;
import org.hibernate.query.NativeQuery;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.SaltSource;
import org.springframework.security.authentication.encoding.MessageDigestPasswordEncoder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.validation.Validator;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;

public class UserUtil {

	private static final Log log = LogUtil.getLog(UserUtil.class);

	public static final	String CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS 			= "LIST_CONTEXT_TOUCHPOINTS";
	public static final	String CONTEXT_KEY_TOUCHPOINT_CONTEXT 					= "TOUCHPOINT_CONTEXT";
	public static final	String CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT 		= "TOUCHPOINT_SELECTION_CONTEXT";
	public static final	String CONTEXT_KEY_TOUCHPOINT_SELECTION_STATUS_CONTEXT 	= "TOUCHPOINT_SELECTION_STATUS_CONTEXT";
	public static final	String CONTEXT_KEY_LANGUAGE_CONTEXT 					= "LANGUAGE_CONTEXT";
	public static final	String CONTEXT_KEY_GLOBAL_CONTEXT 						= "GLOBAL_CONTEXT";
	public static final	String CONTEXT_KEY_RETURN_FROM_SETUP_PAGE_CONTEXT		= "RETURN_FROM_SETUP_PAGE_CONTEXT";
	public static final	String CONTEXT_KEY_COLLECTION_CONTEXT 					= "COLLECTION_CONTEXT";
	public static final	String CONTEXT_KEY_RATIONALIZER_APPLICATION_CONTEXT 	= "RATIONALIZER_APPLICATION_CONTEXT";
	public static final String CONTEXT_KEY_WIDGETS_CONTEXT						= "WIDGETS_CONTEXT";
	public static final	String CONTEXT_KEY_CHANNEL_CONTEXT 						= "CHANNEL_CONTEXT";

	public static void addECatalogAuthorities(User user, List<GrantedAuthority> dbAuths) {
		if ( MessagepointLicenceManager.getInstance().isLicencedForECatalog() ) {
			Permission ecatalogViewPermission = Permission.findById(Permission.ROLE_ECATALOG_VIEW);
			Permission ecatalogEditPermission = Permission.findById(Permission.ROLE_ECATALOG_EDIT);
			Permission ecatalogAdminPermission = Permission.findById(Permission.ROLE_ECATALOG_ADMIN);
			Permission ecatalogApprovePermission = Permission.findById(Permission.ROLE_ECATALOG_APPROVE);

			Permission licencedEcatalogViewPermission = Permission.findById(Permission.ROLE_LICENCED_ECATALOG_VIEW);
			Permission licencedEcatalogEditPermission = Permission.findById(Permission.ROLE_LICENCED_ECATALOG_EDIT);
			Permission licencedEcatalogAdminPermission = Permission.findById(Permission.ROLE_LICENCED_ECATALOG_ADMIN);
			Permission licencedEcatalogApprovePermission = Permission.findById(Permission.ROLE_LICENCED_ECATALOG_APPROVE);

			for (GrantedAuthority auth : dbAuths.toArray(new GrantedAuthority[] {})) {
				if (auth.getAuthority().equals(ecatalogViewPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedEcatalogViewPermission.getName()));
				}
				if (auth.getAuthority().equals(ecatalogEditPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedEcatalogEditPermission.getName()));
				}
				if (auth.getAuthority().equals(ecatalogApprovePermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedEcatalogApprovePermission.getName()));
				}
				if (auth.getAuthority().equals(ecatalogAdminPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedEcatalogAdminPermission.getName()));
				}
			}
		}
	}

	public static boolean hasECatalogAuthorities(User user, List<GrantedAuthority> dbAuths) {
		if ( MessagepointLicenceManager.getInstance().isLicencedForECatalog() ) {
			Permission ecatalogViewPermission = Permission.findById(Permission.ROLE_ECATALOG_VIEW);
			Permission ecatalogEditPermission = Permission.findById(Permission.ROLE_ECATALOG_EDIT);
			Permission ecatalogAdminPermission = Permission.findById(Permission.ROLE_ECATALOG_ADMIN);
			Permission ecatalogApprovePermission = Permission.findById(Permission.ROLE_ECATALOG_APPROVE);

			for (GrantedAuthority auth : dbAuths.toArray(new GrantedAuthority[] {})) {
				if (auth.getAuthority().equals(ecatalogViewPermission.getName())
						|| auth.getAuthority().equals(ecatalogEditPermission.getName())
						|| auth.getAuthority().equals(ecatalogAdminPermission.getName())
						|| auth.getAuthority().equals(ecatalogApprovePermission.getName())) {
					return true;
				}
			}
		}
		return false;
	}

	public static void addInsertManagementAuthorities(User user, List<GrantedAuthority> dbAuths) {
		if (MessagepointLicenceManager.getInstance().isLicencedForInsertManagement()) {
			Permission insertViewPermission = Permission.findById(Permission.ROLE_INSERT_VIEW);
			Permission insertEditPermission = Permission.findById(Permission.ROLE_INSERT_EDIT);
			Permission insertApprovePermission = Permission.findById(Permission.ROLE_INSERT_APPROVE);
			Permission insertArchivePermission = Permission.findById(Permission.ROLE_INSERT_ARCHIVE);
			Permission insertReassignPermission = Permission.findById(Permission.ROLE_INSERT_REASSIGN);

			Permission insertScheduleViewPermission = Permission.findById(Permission.ROLE_INSERT_SCHEDULE_VIEW);
			Permission insertScheduleEditPermission = Permission.findById(Permission.ROLE_INSERT_SCHEDULE_EDIT);
			Permission insertScheduleApprovePermission = Permission.findById(Permission.ROLE_INSERT_SCHEDULE_APPROVE);
			Permission insertScheduleSetupPermission = Permission.findById(Permission.ROLE_INSERT_SCHEDULE_SETUP);
			Permission insertScheduleReassignPermission = Permission.findById(Permission.ROLE_INSERT_SCHEDULE_REASSIGN);

			// Permission tenantInsertScheduleViewPermission = Permission.findById(Permission.ROLE_TENANT_INSERT_SCHEDULE_VIEW);
			// Permission tenantInsertScheduleEditPermission = Permission.findById(Permission.ROLE_TENANT_INSERT_SCHEDULE_EDIT);

			Permission rateScheduleViewPermission = Permission.findById(Permission.ROLE_RATE_SCHEDULE_VIEW);
			Permission rateScheduleEditPermission = Permission.findById(Permission.ROLE_RATE_SCHEDULE_EDIT);

			Permission licencedInsertViewPermission = Permission.findById(Permission.ROLE_LICENCED_INSERT_VIEW);
			Permission licencedInsertEditPermission = Permission.findById(Permission.ROLE_LICENCED_INSERT_EDIT);
			Permission licencedInsertApprovePermission = Permission.findById(Permission.ROLE_LICENCED_INSERT_APPROVE);
			Permission licencedInsertArchivePermission = Permission.findById(Permission.ROLE_LICENCED_INSERT_ARCHIVE);
			Permission licencedInsertReassignPermission = Permission.findById(Permission.ROLE_LICENCED_INSERT_REASSIGN);

			Permission licencedInsertScheduleViewPermission = Permission.findById(Permission.ROLE_LICENCED_INSERT_SCHEDULE_VIEW);
			Permission licencedInsertScheduleEditPermission = Permission.findById(Permission.ROLE_LICENCED_INSERT_SCHEDULE_EDIT);
			Permission licencedInsertScheduleApprovePermission = Permission.findById(Permission.ROLE_LICENCED_INSERT_SCHEDULE_APPROVE);
			Permission licencedInsertScheduleScheduleSetupPermission = Permission.findById(Permission.ROLE_LICENCED_INSERT_SCHEDULE_SETUP);
			Permission licencedInsertScheduleReassignPermission = Permission.findById(Permission.ROLE_LICENCED_INSERT_SCHEDULE_REASSIGN);

			Permission licencedRateScheduleViewPermission = Permission.findById(Permission.ROLE_LICENCED_RATE_SCHEDULE_VIEW);
			Permission licencedRateScheduleEditPermission = Permission.findById(Permission.ROLE_LICENCED_RATE_SCHEDULE_EDIT);

			for (GrantedAuthority auth : dbAuths.toArray(new GrantedAuthority[] {})) {
				if (auth.getAuthority().equals(insertViewPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedInsertViewPermission.getName()));
				} else if (auth.getAuthority().equals(insertEditPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedInsertEditPermission.getName()));
				} else if (auth.getAuthority().equals(insertApprovePermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedInsertApprovePermission.getName()));
				} else if (auth.getAuthority().equals(insertArchivePermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedInsertArchivePermission.getName()));
				} else if (auth.getAuthority().equals(insertReassignPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedInsertReassignPermission.getName()));

				}else if (auth.getAuthority().equals(insertScheduleViewPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedInsertScheduleViewPermission.getName()));
				} else if (auth.getAuthority().equals(insertScheduleEditPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedInsertScheduleEditPermission.getName()));
				} else if (auth.getAuthority().equals(insertScheduleApprovePermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedInsertScheduleApprovePermission.getName()));
				} else if (auth.getAuthority().equals(insertScheduleSetupPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedInsertScheduleScheduleSetupPermission.getName()));
				} else if (auth.getAuthority().equals(insertScheduleReassignPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedInsertScheduleReassignPermission.getName()));

				}else if (auth.getAuthority().equals(rateScheduleViewPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedRateScheduleViewPermission.getName()));
				} else if (auth.getAuthority().equals(rateScheduleEditPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedRateScheduleEditPermission.getName()));
				}
			}
		}
	}

	public static void addLookupTableAuthorities(User user, List<GrantedAuthority> dbAuths) {
		if ( MessagepointLicenceManager.getInstance().isLicencedForLookupTable()) {
			Permission lookupTableViewPermission = Permission.findById(Permission.ROLE_LOOKUP_TABLE_VIEW);
			Permission lookupTableEditPermission = Permission.findById(Permission.ROLE_LOOKUP_TABLE_EDIT);
			Permission lookupTableApprovalPermission = Permission.findById(Permission.ROLE_LOOKUP_TABLE_APPROVE);
			Permission lookupTableSetupPermission = Permission.findById(Permission.ROLE_LOOKUP_TABLE_SETUP);

			Permission licencedLookupTableViewPermission = Permission.findById(Permission.ROLE_LICENCED_LOOKUP_TABLE_VIEW);
			Permission licencedLookupTableEditPermission = Permission.findById(Permission.ROLE_LICENCED_LOOKUP_TABLE_EDIT);
			Permission licencedLookupTableApprovalPermission = Permission.findById(Permission.ROLE_LICENCED_LOOKUP_TABLE_APPROVE);
			Permission licencedLookupTableSetupPermission = Permission.findById(Permission.ROLE_LICENCED_LOOKUP_TABLE_SETUP);

			for (GrantedAuthority auth : dbAuths.toArray(new GrantedAuthority[] {})) {
				if (auth.getAuthority().equals(lookupTableViewPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedLookupTableViewPermission.getName()));
				}
				if (auth.getAuthority().equals(lookupTableEditPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedLookupTableEditPermission.getName()));
				}
				if (auth.getAuthority().equals(lookupTableApprovalPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedLookupTableApprovalPermission.getName()));
				}
				if (auth.getAuthority().equals(lookupTableSetupPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedLookupTableSetupPermission.getName()));
				}
			}
		}
	}

	private static void addMasterUserPermissions(User user, List<GrantedAuthority> dbAuths) {
		Permission adminMasterUserPermission = Permission.findById(Permission.ID_ROLE_ADMIN_MASTER_USER);
		Permission adminMasterAdminPermission = Permission.findById(Permission.ID_ROLE_ADMIN_MASTER_ADMIN);
		if (user.getId() == 900L || user.getMasterAdminStatus() >= User.POD_ADMIN_STATUS) {
			dbAuths.add(new SimpleGrantedAuthority(adminMasterAdminPermission.getName()));
			dbAuths.add(new SimpleGrantedAuthority(adminMasterUserPermission.getName()));
		}else if (user.getMasterAdminStatus() == User.DOMAIN_ADMIN_STATUS) {
			dbAuths.add(new SimpleGrantedAuthority(adminMasterUserPermission.getName()));
		}
		if((user.getId() > 899L && user.getId() < 1000L) || user.getMasterAdminStatus() >= User.DOMAIN_ADMIN_STATUS){
			Permission roleUserPermission = Permission.findById(Permission.ID_ROLE_USER);
			if (!dbAuths.contains(new SimpleGrantedAuthority(roleUserPermission.getName()))) {
				dbAuths.add(new SimpleGrantedAuthority(roleUserPermission.getName()));
			}
			Permission roleSystemAdminPermission = Permission.findById(Permission.ID_ROLE_PROVIDER_SYSTEM_ADMIN);
			if (!dbAuths.contains(new SimpleGrantedAuthority(roleSystemAdminPermission.getName()))) {
				dbAuths.add(new SimpleGrantedAuthority(roleSystemAdminPermission.getName()));
			}
		}
	}

	public static void addMessagepointInteractiveAuthorities(User user, List<GrantedAuthority> dbAuths) {
		if ( MessagepointLicenceManager.getInstance().isLicencedForMessagepointInteractive() && user != null && user.isConnectedAllowed() ) {
			Permission communicationViewPermission = Permission.findById(Permission.ROLE_COMMUNICATIONS_VIEW);
			Permission communicationEditPermission = Permission.findById(Permission.ROLE_COMMUNICATIONS_EDIT);
			Permission communicationApprovalPermission = Permission.findById(Permission.ROLE_COMMUNICATIONS_APPROVE);
			Permission communicationReassignPermission = Permission.findById(Permission.ROLE_COMMUNICATIONS_REASSIGN);
			Permission communicationSetupPermission = Permission.findById(Permission.ROLE_COMMUNICATIONS_SETUP);
			Permission communicationAssignedPermission = Permission.findById(Permission.ROLE_COMMUNICATIONS_ASSIGNED);
			Permission communicationApiPermission = Permission.findById(Permission.ROLE_COMMUNICATIONS_API_SETUP);

			Permission licencedCommunicationViewPermission = Permission.findById(Permission.ROLE_LICENCED_COMMUNICATIONS_VIEW);
			Permission licencedCommunicationEditPermission = Permission.findById(Permission.ROLE_LICENCED_COMMUNICATIONS_EDIT);
			Permission licencedCommunicationApprovalPermission = Permission.findById(Permission.ROLE_LICENCED_COMMUNICATIONS_APPROVE);
			Permission licencedCommunicationReassignPermission = Permission.findById(Permission.ROLE_LICENCED_COMMUNICATIONS_REASSIGN);
			Permission licencedCommunicationSetupPermission = Permission.findById(Permission.ROLE_LICENCED_COMMUNICATIONS_SETUP);
			Permission licencedCommunicationAssignedPermission = Permission.findById(Permission.ROLE_LICENCED_COMMUNICATIONS_ASSIGNED);
			Permission licencedCommunicationApiPermission = Permission.findById(Permission.ROLE_LICENCED_COMMUNICATIONS_API_SETUP);

			for (GrantedAuthority auth : dbAuths.toArray(new GrantedAuthority[] {})) {
				if (auth.getAuthority().equals(communicationViewPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedCommunicationViewPermission.getName()));
				}
				if (auth.getAuthority().equals(communicationEditPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedCommunicationEditPermission.getName()));
				}
				if (auth.getAuthority().equals(communicationApprovalPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedCommunicationApprovalPermission.getName()));
				}
				if (auth.getAuthority().equals(communicationReassignPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedCommunicationReassignPermission.getName()));
				}
				if (auth.getAuthority().equals(communicationSetupPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedCommunicationSetupPermission.getName()));
				}
				if (auth.getAuthority().equals(communicationAssignedPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedCommunicationAssignedPermission.getName()));
				}
				if (auth.getAuthority().equals(communicationApiPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedCommunicationApiPermission.getName()));
				}
			}
		}
	}

	public static void addSimulationAuthorities(User user, List<GrantedAuthority> dbAuths) {
		if (MessagepointLicenceManager.getInstance().isSimulationFlag()) {
			Permission simulationViewPermission = Permission.findById(Permission.ID_ROLE_SIMULATION_VIEW);
			Permission simulationEditPermission = Permission.findById(Permission.ID_ROLE_SIMULATION_EDIT);

			Permission licencedSimulationViewPermission = Permission.findById(Permission.ID_LICENCED_SIMULATION_VIEW);
			Permission licencedSimulationEditPermission = Permission.findById(Permission.ID_LICENCED_SIMULATION_EDIT);

			for (GrantedAuthority auth : dbAuths.toArray(new GrantedAuthority[] {})) {
				if (auth.getAuthority().equals(simulationViewPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedSimulationViewPermission.getName()));
				}
				if (auth.getAuthority().equals(simulationEditPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedSimulationEditPermission.getName()));
				}
			}
		}
	}

	public static void addSystemAuthorities(User user, List<GrantedAuthority> dbAuths) {
		// Add Multi Tenant Authority if the System is enabled for Multi-Tenant
		if (TenantUtil.isSystemMultiTenant()) {
			dbAuths.add(new SimpleGrantedAuthority(TenantUtil.getSystemTenantPermission().getName()));

			// Add the Level permissions
			// ROLE_SYSTEM_TENANT_PROVIDER
			// ROLE_SYSTEM_TENANT_SUBTENANT
			// ROLE_SYSTEM_TENANT_GRANDCHILD_OR_MORE
			Permission tenantLevelPermission = TenantUtil.getTenantLevelPermission();
			dbAuths.add(new SimpleGrantedAuthority(tenantLevelPermission.getName()));

			if (tenantLevelPermission.getId() == Long.valueOf(TenantUtil.PERMISSION_TENANT_LEVEL_PROVIDER).longValue()) {
				addSystemSettingsPermissions(dbAuths);
			}
			// Remove any permissions that haven't been assigned to the user's
			// Tenant
			Tenant tenant = Tenant.getPrimary();

			if (tenant != null && tenant.getPermissions() != null) {
				for (GrantedAuthority auth : dbAuths.toArray(new GrantedAuthority[] {})) {
					boolean found = false;
					for (Permission permission : tenant.getPermissions()) {
						if (auth.getAuthority().equals(permission.getName()))
							found = true;
					}
					if (!found) {

						Permission permission = Permission.findByName(auth.getAuthority());

						if (permission != null) {
							if (!permission.getType().equals(Permission.TYPE_SYSTEM_VALUE_STRING)) {
								dbAuths.remove(auth);
							}
						} else {
							throw new IllegalStateException("No permission with " + auth.getAuthority() + " name exists!");
						}
					}
				}
			}

		} else {
			// In a single tenant environment everyone is considered at the
			// PROVIDER TENANT level
			// ROLE_SYSTEM_TENANT_PROVIDER
			Permission providerPermission = Permission.findById(TenantUtil.PERMISSION_TENANT_LEVEL_PROVIDER);
			dbAuths.add(new SimpleGrantedAuthority(providerPermission.getName()));
			addSystemSettingsPermissions(dbAuths);
		}
		addMasterUserPermissions(user, dbAuths);
	}

	private static void addSystemSettingsPermissions(List<GrantedAuthority> dbAuths) {
		Permission systemAdminPermission = Permission.findById(Permission.ID_ROLE_SYSTEM_ADMIN);
		if (dbAuths.contains(new SimpleGrantedAuthority(systemAdminPermission.getName()))) {
			Permission providerSystemAdminPermission = Permission.findById(Permission.ID_ROLE_PROVIDER_SYSTEM_ADMIN);
			dbAuths.add(new SimpleGrantedAuthority(providerSystemAdminPermission.getName()));
		}
		Permission adminSettingsEditPermission = Permission.findById(Permission.ID_ROLE_ADMIN_SETTINGS_EDIT);
		if (dbAuths.contains(new SimpleGrantedAuthority(adminSettingsEditPermission.getName()))) {
			Permission providerSysAdminEditPerm = Permission.findById(Permission.ID_ROLE_PROVIDER_SYSTEM_ADMIN_EDIT);
			dbAuths.add(new SimpleGrantedAuthority(providerSysAdminEditPerm.getName()));
		}
	}

	public static void addVariantManagementAuthorities(User user, List<GrantedAuthority> dbAuths) {
		if ( MessagepointLicenceManager.getInstance().isLicencedForVariantManagement() ) {
			Permission tpSelectionsViewPermission 		= Permission.findById(Permission.ROLE_TOUCHPOINT_SELECTIONS_VIEW);
			Permission tpSelectionsEditPermission 		= Permission.findById(Permission.ROLE_TOUCHPOINT_SELECTIONS_EDIT);
			Permission tpSelectionsApprovePermission 	= Permission.findById(Permission.ROLE_TOUCHPOINT_SELECTIONS_APPROVE);
			Permission tpSelectionsArchivePermission 	= Permission.findById(Permission.ROLE_TOUCHPOINT_SELECTIONS_ARCHIVE);
			Permission tpSelectionsReassignPermission 	= Permission.findById(Permission.ROLE_TOUCHPOINT_SELECTION_REASSIGN);
			Permission tpSelectionsAdminViewPermission 	= Permission.findById(Permission.ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW);
			Permission tpSelectionsAdminEditPermission 	= Permission.findById(Permission.ROLE_TOUCHPOINT_SELECTIONS_ADMIN_EDIT);
			Permission tpSelectionsAdminSetupPermission = Permission.findById(Permission.ROLE_TOUCHPOINT_SELECTIONS_ADMIN_SETUP);

			Permission licencedTpSelectionsViewPermission 		= Permission.findById(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_VIEW);
			Permission licencedTpSelectionsEditPermission 		= Permission.findById(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_EDIT);
			Permission licencedTpSelectionsApprovePermission 	= Permission.findById(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_APPROVE);
			Permission licencedTpSelectionsArchivePermission 	= Permission.findById(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ARCHIVE);
			Permission licencedTpSelectionsReassignPermission 	= Permission.findById(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTION_REASSIGN);
			Permission licencedTpSelectionsAdminViewPermission 	= Permission.findById(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_VIEW);
			Permission licencedTpSelectionsAdminEditPermission 	= Permission.findById(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_EDIT);
			Permission licencedTpSelectionsAdminSetupPermission = Permission.findById(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_SETUP);

			for (GrantedAuthority auth : dbAuths.toArray(new GrantedAuthority[] {})) {
				if (auth.getAuthority().equals(tpSelectionsViewPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedTpSelectionsViewPermission.getName()));
				}
				if (auth.getAuthority().equals(tpSelectionsEditPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedTpSelectionsEditPermission.getName()));
				}
				if (auth.getAuthority().equals(tpSelectionsApprovePermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedTpSelectionsApprovePermission.getName()));
				}
				if (auth.getAuthority().equals(tpSelectionsArchivePermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedTpSelectionsArchivePermission.getName()));
				}
				if (auth.getAuthority().equals(tpSelectionsReassignPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedTpSelectionsReassignPermission.getName()));
				}
				if (auth.getAuthority().equals(tpSelectionsAdminViewPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedTpSelectionsAdminViewPermission.getName()));
				}
				if (auth.getAuthority().equals(tpSelectionsAdminEditPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedTpSelectionsAdminEditPermission.getName()));
				}
				if (auth.getAuthority().equals(tpSelectionsAdminSetupPermission.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(licencedTpSelectionsAdminSetupPermission.getName()));
				}
			}
		}
	}

	public static String canActivateThisUser(User user, Branch branch, User requester){
		String returnMsg = "true";
		// if requestor == null, which means this is validation for soft-deactivate
		if(requester != null){
			boolean hasPermit = (requester.isPermitted(Permission.ID_ROLE_ADMIN_MASTER_USER)
					|| requester.isPermitted(Permission.ID_ROLE_ADMIN_USER_EDIT));

			if(!hasPermit){
				returnMsg = ApplicationUtil.getMessage("code.text.accessdenied");
				return returnMsg;
			}
		}
		User homeDcsUser = user.getHomeDcsUser();
		Branch homeDcsBranch = homeDcsUser.getDomainOfThisUser();
		int homeDcsUserLicenseType = homeDcsUser.getLicensedType();
		if(homeDcsUserLicenseType > 0){
			int possibleLicenseTotal = 0;
			if(homeDcsUserLicenseType == User.LICENSED_TYPE_REGULAR_ACCESS){
				possibleLicenseTotal= MessagepointLicenceManager.getInstance(homeDcsBranch.getDcsSchemaName()).getNumberOfFullUsers();
			}else if(homeDcsUserLicenseType == User.LICENSED_TYPE_CONNECTED_ACCESS){
				possibleLicenseTotal= MessagepointLicenceManager.getInstance(homeDcsBranch.getDcsSchemaName()).getNumberOfConnectedUsers();
			}else if(homeDcsUserLicenseType == User.LICENSED_TYPE_WORKFLOW_ACCESS){
				possibleLicenseTotal= MessagepointLicenceManager.getInstance(homeDcsBranch.getDcsSchemaName()).getNumberOfRestrictedUsers();
			}
			int existingLicenseTotal = BranchUtil.getNumberOfCurrentUsedLicences(homeDcsBranch, homeDcsUserLicenseType, homeDcsUser.getId());
			if(existingLicenseTotal >= possibleLicenseTotal){
				String type = "connected";
				if(homeDcsUserLicenseType == User.LICENSED_TYPE_REGULAR_ACCESS)
					type = "regular";
				if(homeDcsUserLicenseType == User.LICENSED_TYPE_WORKFLOW_ACCESS)
					type = "workflow";
				returnMsg = ApplicationUtil.getMessage("error.message.exceed.maximum.number.of."+ type +".users", new String[]{homeDcsBranch.getName()});
				return returnMsg;
			}
		}
		return returnMsg;
	}

	public static boolean canDeactivate(User user) {

		if ( (UserUtil.isPermissionGranted(Permission.ID_ROLE_ADMIN_MASTER_USER)
				|| UserUtil.isPermissionGranted(Permission.ID_ROLE_ADMIN_USER_EDIT)) &&
				user.isAccountActive() && !user.isPrincipalUser()) {
			return true;
		}
		return false;
	}

	public static boolean canDeleteThisUser(User user, Branch branch, User requester){

		// TODO: need validation and return a boolean
		// this will return false if any object is assigned to this user
		if(!user.isAccountActive() && !user.isPrincipalUser() && requester.isPodAdminUser()) {

			// Check if user is DCS domain user, if not get the DCS Domain user.
			// then go through all domains and its instances 
			// if there's any assigned object to the user, returns false
			// return true at the end
			User homeDcsUser = user.isHomeDcsUser()?user:user.getHomeDcsUser();
			Branch homeDcsBranch = branch == homeDcsUser.getHomeDcsDomain()? branch:Branch.findById(homeDcsUser.getHomeDcsDomain().getId());

			SessionHolder mainSessionHolder = null;
			try {
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(homeDcsBranch.getDcsSchemaName());

				for (Branch loopBranch : BranchUtil.getPossibleEnabledBranchesByRequestor(branch, requester)) {
					//User dcsUser = User.findByGuid(homeDcsUser.getGuid(), branch.getDcsNode());
					// with DCS
					for (Node node : loopBranch.getAllAccessibleNodes(true, requester.canAccessTestingInstances())) {
						SessionHolder localSessionHolder = null;
						try {
							localSessionHolder = HibernateUtil.getManager().openTemporarySession(node.getSchemaName());
							User nodeUser = User.findByGuid(homeDcsUser.getGuid(), node);
							if (nodeUser == null) {
								HibernateUtil.getManager().restoreSession(localSessionHolder);
								continue;
							}
							long documentId = -1L;
							int pageSize = 0;
							int displayStart = 0;
							int pageIndex = pageSize == 0 ? 1 : Math.round(displayStart / pageSize) + 1;
							String sSearch = "sSearch";
							String displayMode = "limited";
							String listDisplayFormat = "name";

							Map<String, String> orderByMap = new HashMap<>();
							orderByMap.put("name", "asc");
							for (UserDeleteObjectType type : UserDeleteObjectType.listAll()) {
								AsyncUserDeleteDetailWrapper wrapper = new AsyncUserDeleteDetailWrapper();
								wrapper.setsEcho("sEcho");
								wrapper.setDisplayMode(displayMode);
								wrapper.setListDisplayFormat(listDisplayFormat);
								try {
									wrapper.buildItemsList(type.getId(), sSearch, orderByMap, pageSize, pageIndex, documentId, false, nodeUser.getId());
								} catch (Exception e) {
									log.error(e);
								}
								wrapper.init();
								if (!wrapper.getAaData().isEmpty()) {
									HibernateUtil.getManager().restoreSession(localSessionHolder);
									HibernateUtil.getManager().restoreSession(mainSessionHolder);
									return false;
								}
							}
						} finally {
							HibernateUtil.getManager().restoreSession(localSessionHolder);
						}
					}

				}
			} finally {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}

			return true;
		}

		return false;
	}

	/*
	 * Replace existing userId with passed userID on given schema(instance).
	 * this will fetch all tables with USER_ID column included then replace
	 */
	@SuppressWarnings("unchecked")
	public static void replaceUserId(String schemaName, long oldUserId, long newUserId) {

		HibernateObjectManager hom = HibernateUtil.getManager();
		boolean switchSchema = true;
		if(newUserId == 0)
			newUserId = 1L;


		String currentSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();

		if ((currentSchema == null && schemaName == null) || (currentSchema != null && currentSchema.equalsIgnoreCase(schemaName)))
		{
			switchSchema = false;
		}

		SessionHolder mainSessionHolder = null;
		try {
			if (switchSchema)
				mainSessionHolder = hom.openTemporarySession(schemaName);


			// all tables has user_id will be called except ones will be deleted for deleting option
			String[] columnNames = {"USER_ID", "ASSIGNEE_ID", "OWNER_ID", "SYNC_BY_ID", "ASSIGNED_TO"};
			List<String> arrayColumnNames = new ArrayList<>(Arrays.asList(columnNames));


			for (String columnName : arrayColumnNames) {
				String query = "SELECT DISTINCT table_name FROM information_schema.columns WHERE table_schema = '" + schemaName + "' AND UPPER(column_name) = UPPER('" + columnName + "') AND (UPPER(table_name) NOT IN ('APPROVAL_USER', 'AUDIT_EVENT', 'AUDIT_REPORT', 'USERS', 'PASSWORD_HISTORY', 'PASSWORD_RECOVERY', 'USER_PERMISSION', 'USER_ROLE', 'TP_SEL_VISIBLE_USER', 'RATIONALIZER_APP_VISIBLE_USER', 'FOLDERS', 'WEB_SERVICE_CONFIG', 'TASK_USER')) ORDER BY table_name";
				NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
				@SuppressWarnings("unchecked")
				List<String> tableNames = sqlQuery.list();

				for (String tableName : tableNames) {
					try {

						String queryString = "UPDATE " + tableName + " SET " + columnName + " = :newUserId WHERE " + columnName + " = :oldUserId";
						sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(queryString);
						sqlQuery.setParameter("newUserId", newUserId);
						sqlQuery.setParameter("oldUserId", oldUserId);
						sqlQuery.executeUpdate();
					} catch (Exception e) {
						log.error(" unexpected exception when invoking replaceuserId execute method on Table:" + tableName + ", Column: " + columnName, e);

						//throw new RuntimeException("Runtime exception in User handleRequest\n table :" + tableName + ", column name: " + columnName);
					}
				}
				HibernateUtil.getManager().getSession().flush();
			}

		/*
		Dealing with approval users
		If WF_STEP_ID with newUserId exist already, delete the row with oldUserId
		If not, replace row oldUserId with newUserId
		 */
			String query = "SELECT WF_STEP_ID FROM APPROVAL_USER WHERE USER_ID=" + oldUserId;
			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
			if (!sqlQuery.list().isEmpty()) {
				List<Object> stepIds = sqlQuery.list();
				for (Object stepIdObject : stepIds) {
					Long stepId = Long.getLong(stepIdObject.toString());
					query = "SELECT USER_ID FROM APPROVAL_USER WHERE WF_STEP_ID=" + stepId;
					sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
					if (sqlQuery.list().isEmpty()) {
						try {
							String queryString = "UPDATE APPROVAL_USER SET USER_ID = :newUserId WHERE USER_ID = :oldUserId";
							sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(queryString);
							sqlQuery.setParameter("newUserId", newUserId);
							sqlQuery.setParameter("oldUserId", oldUserId);
							sqlQuery.executeUpdate();
						} catch (Exception e) {
							log.error(" unexpected exception when invoking replaceuserId execute method on Table:APPROVAL_USER", e);

						}
					} else {
						sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM APPROVAL_USER WHERE USER_ID = " + oldUserId);
						sqlQuery.executeUpdate();
					}
				}
			}

		/*
		Now the rest tables with SYNC_BY_ID, ASSIGNED_TO, CONTENT_LAST_UPDATED_BY_ID, OWNER columns
		 */
			StringBuilder updateQuery = new StringBuilder();
			updateQuery.append("UPDATE SYNC_HISTORY SET SYNC_BY_ID = ").append(newUserId).append(" WHERE SYNC_BY_ID = ").append(oldUserId).append("; ");
			updateQuery.append("UPDATE WORKFLOW_ACTION_HISTORY SET ASSIGNED_TO = ").append(newUserId).append(" WHERE ASSIGNED_TO = ").append(oldUserId).append("; ");
			updateQuery.append("UPDATE TOUCHPOINT_SELECTION SET CONTENT_LAST_UPDATED_BY_ID = ").append(newUserId).append(" WHERE CONTENT_LAST_UPDATED_BY_ID = ").append(oldUserId).append("; ");
			updateQuery.append("UPDATE WORKFLOW_INSTANCE SET OWNER = ").append(newUserId).append(" WHERE OWNER = ").append(oldUserId).append("; ");
			updateQuery.append("UPDATE PG_TREE_NODE SET USER_ID = ").append(newUserId).append(" WHERE USER_ID = ").append(oldUserId).append("; ");
			updateQuery.append("UPDATE PG_TREE_NODE SET LAST_EDITOR_ID = ").append(newUserId).append(" WHERE LAST_EDITOR_ID = ").append(oldUserId).append("; ");

			sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(updateQuery.toString());
			sqlQuery.executeUpdate();

			HibernateUtil.getManager().getSession().flush();
		} finally {
			if (switchSchema) {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}
		}
	}

	/*
	 * Replace existing userId with system default userID, other userId still can be passed.
	 * replace where user was referred as created_by, updated_by from listed tables(tableNames)
	 */
	@SuppressWarnings("unchecked")
	public static void replaceUserIdBeforeDelete(User user, long replaceId, boolean flushRequest) {

		HibernateObjectManager hom = HibernateUtil.getManager();
		boolean switchSchema = true;
		if(replaceId == 0)
			replaceId = 1L;
		// find the current schema name, which is used in the current Thread
		//
		String currentSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
		String userSchema = user.getSchemaOfThisUser();

		if ((currentSchema == null && userSchema == null) || (currentSchema != null && currentSchema.equalsIgnoreCase(userSchema))){

			switchSchema = false;
		}

		SessionHolder mainSessionHolder = null;
		try {
			if (switchSchema) {
				mainSessionHolder = hom.openTemporarySession(userSchema);
			}
			long userId = user.getId();

			// PostgreSQL
			String query = "SELECT DISTINCT table_name FROM information_schema.columns WHERE table_schema = '" + userSchema +
					"' AND (UPPER(column_name) IN ('CREATED_BY_ID', 'UPDATED_BY_ID', 'CREATED_BY', 'UPDATED_BY')) ORDER BY table_name";

			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);

			List<String> tableNames = sqlQuery.list();


			for (String tableName : tableNames) {
				String columnQuery = "select DISTINCT column_name from information_schema.columns where table_schema = '" + userSchema + "' AND table_name = '" + tableName + "'";
				NativeQuery columnSqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(columnQuery);
				List<String> columns = columnSqlQuery.list();
				if (columns.isEmpty())
					continue;
				if (tableName.equalsIgnoreCase("target_group"))
					continue;

				Map<String, String> sqlStrings = new HashMap<>();
				sqlStrings.put("CREATED_BY", "UPDATE " + tableName + " SET CREATED_BY = :replaceId WHERE CREATED_BY = :userId");
				sqlStrings.put("UPDATED_BY", "UPDATE " + tableName + " SET UPDATED_BY = :replaceId WHERE UPDATED_BY = :userId");
				sqlStrings.put("CREATED_BY_ID", "UPDATE " + tableName + " SET CREATED_BY_ID = :replaceId WHERE CREATED_BY_ID = :userId");
				sqlStrings.put("UPDATED_BY_ID", "UPDATE " + tableName + " SET UPDATED_BY_ID = :replaceId WHERE UPDATED_BY_ID = :userId");

				for (String column : sqlStrings.keySet()) {
					if (!columns.contains(column))
						continue;
					String queryString = sqlStrings.get(column);
					sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(queryString);
					sqlQuery.setParameter("replaceId", replaceId);
					sqlQuery.setParameter("userId", userId);
					sqlQuery.executeUpdate();
				}
			}
			if (flushRequest)
				HibernateUtil.getManager().getSession().flush();
		} finally {
			if (switchSchema)
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}
	}

	/*
	This is revised version of deleteUser(User user, boolean flushRequest)
	replacing to-dbe-deleted userId with super admin ID
	 */

	public static void deleteUser(User user, boolean flushRequest, long replaceId) {

		boolean switchSchema = true;
		if(replaceId == 0)
			replaceId = 1L;

		// find the current schema name, which is used in the current Thread
		//
		String currentSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
		String userSchema = user.getSchemaOfThisUser();

		if ((currentSchema == null && userSchema == null) || (currentSchema != null && currentSchema.equalsIgnoreCase(userSchema)))
		{
			switchSchema = false;
		}

		SessionHolder mainSessionHolder = null;
		try {
			if (switchSchema)
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(userSchema);

			long userId = user.getId();


			/*
			 * Replacing userid with superUseId where the user was referred as created_by or updated_by
			 */
			replaceUserIdBeforeDelete(user, 1L, true);

			StringBuilder deleteQuery = new StringBuilder();
			deleteQuery.append("DELETE FROM PASSWORD_HISTORY WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM PASSWORD_RECOVERY WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM AUDIT_EVENT_DOC WHERE AUDIT_EVENT_ID IN (SELECT ID FROM AUDIT_EVENT WHERE OBJECT_ID = ").append(userId).append(" OR USER_ID = ").append(userId).append("); ");
			deleteQuery.append("DELETE FROM AUDIT_EVENT WHERE OBJECT_ID = ").append(userId).append(" OR USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM AUDIT_REPORT WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM USER_ROLE WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM USER_PERMISSION WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM TP_SEL_VISIBLE_USER WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM RATIONALIZER_APP_VISIBLE_USER WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM FOLDERS WHERE OWNER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM TASK_USER WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM USERS WHERE ID = ").append(userId).append("; ");

			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(deleteQuery.toString());
			sqlQuery.executeUpdate();

			replaceUserId(userSchema, userId, replaceId);

			if (flushRequest)
				HibernateUtil.getManager().getSession().flush();
		} finally {
			if (switchSchema)
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}
	}

	public static void deleteUser(User user, boolean flushRequest) {

		boolean switchSchema = true;

		// find the current schema name, which is used in the current Thread
		//
		String currentSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();

		if ((currentSchema == null && user.getSchemaOfThisUser() == null) || (currentSchema != null && currentSchema.equalsIgnoreCase(user.getSchemaOfThisUser())))
		{
			switchSchema = false;
		}

		SessionHolder mainSessionHolder = null;
		try {
			if (switchSchema)
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(user.getSchemaOfThisUser());

			long userId = user.getId();

			StringBuilder deleteQuery = new StringBuilder();
			deleteQuery.append("DELETE FROM PASSWORD_HISTORY WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM PASSWORD_RECOVERY WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM APPROVAL_USER WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM AUDIT_EVENT_DOC WHERE AUDIT_EVENT_ID IN (SELECT ID FROM AUDIT_EVENT WHERE OBJECT_ID = ").append(userId).append(" OR USER_ID = ").append(userId).append("); ");
			deleteQuery.append("DELETE FROM AUDIT_EVENT WHERE OBJECT_ID = ").append(userId).append(" OR USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM AUDIT_REPORT WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM USER_ROLE WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM USER_PERMISSION WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM TP_SEL_VISIBLE_USER WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM RATIONALIZER_APP_VISIBLE_USER WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM FOLDERS WHERE OWNER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM TASK_USER WHERE USER_ID = ").append(userId).append("; ");
			deleteQuery.append("DELETE FROM USERS WHERE ID = ").append(userId).append("; ");

			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(deleteQuery.toString());
			sqlQuery.executeUpdate();

			if (flushRequest)
				HibernateUtil.getManager().getSession().flush();
		} finally {
			if (switchSchema)
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}
	}

	/**
	 * Removes temporary authentication-related data which may have been stored in the session
	 * during the authentication process.
	 */
	protected static final void clearAuthenticationAttributes(HttpServletRequest request) {
		HttpSession session = request.getSession(false);

		if (session == null) {
			return;
		}

		session.removeAttribute(WebAttributes.AUTHENTICATION_EXCEPTION);
	}

	public static List<Permission> convertAuthorityToPermission(Collection<GrantedAuthority> authorityList){
		List<Permission> retList = new ArrayList<>();
		for(GrantedAuthority auth:authorityList){
			if(auth!=null && auth.getAuthority()!=null){
				Permission per = Permission.findByName(auth.getAuthority());
				if(per!=null)
					retList.add(per);
			}
		}
		return retList;
	}

	private static List<GrantedAuthority> convertPermissionsToAuthorities(Set<Permission> permissionsList){
		List<GrantedAuthority> authoritiesList = new ArrayList<>();
		for (Permission permission : permissionsList){
			if (permission != null) {
				authoritiesList.add(new SimpleGrantedAuthority(permission.getName()));
			}
		}
		return authoritiesList;
	}

	/*
	 * Create a new user based on passed User
	 */
	public static User createNewUser(User refUser){
		User newUser = new User();

		newUser.setGuid(refUser.getGuid());
		newUser.setAccountActive(refUser.isAccountActive());
		newUser.setAccountSoftDeactivated(refUser.isAccountSoftDeactivated());
		newUser.setApplicationLocaleId(refUser.getApplicationLocaleId());
		newUser.setEmail(refUser.getEmail());
		newUser.setEmailNotifyDaily(refUser.isEmailNotifyDaily());
		newUser.setEmailNotifyRealTime(refUser.isEmailNotifyRealTime());
		newUser.setFirstName(refUser.getFirstName());
		newUser.setLastName(refUser.getLastName());
		newUser.setName(refUser.getName());
		newUser.setPassword(refUser.getPassword());
		newUser.setPasswordExpired(refUser.isPasswordExpired());
		newUser.setSalt(refUser.getSalt());
		newUser.setUserDeactivateReasonType(refUser.getUserDeactivateReasonType());
		newUser.setUsername(refUser.getUsername());
		newUser.setDefaultNodeId(refUser.getDefaultNodeId());
		newUser.setLicensedType(refUser.getLicensedType());
		newUser.setEmailVerified(refUser.isEmailVerified());

		// set default workgroup 
		newUser.setWorkgroupId(Workgroup.DEFAULT_WORKGROUP);

		return newUser;
	}

	/*
	 * Create a new user with basic user infor then set all other properties to default values
	 */
	public static User createNewUser( String userName, String email, String firstName, String lastName )
	{
		User newUser = new User();

		newUser.setAccountActive(false);
		newUser.setApplicationLocaleId(ApplicationLocale.resolveMissingUserApplicationLocale().getMessagepointLocale().getId());
		newUser.setEmail(email);
		newUser.setEmailNotifyDaily(false);
		newUser.setEmailNotifyRealTime(false);
		newUser.setFirstName(firstName);
		newUser.setLastName(lastName);
		newUser.setPassword(UserUtil.generateNewUserPassword());
		newUser.setSalt( RandomStringUtils.randomAscii(32) );
		newUser.setUserDeactivateReasonType(null);
		newUser.setUsername(userName);
		newUser.setWorkgroupId(Workgroup.DEFAULT_WORKGROUP);
		newUser.setLicensedType(User.LICENSED_TYPE_REGULAR_ACCESS);

		return newUser;
	}

	public static void createUserFolders(User user){
		// create folders for inserts
		//
		LastVisitedFolder lvFolder = new LastVisitedFolder(user);
		lvFolder.setCreatedBy(null);
		lvFolder.setUpdatedBy(null);
		lvFolder.insertWithPropagationRequired();

		FavoritesFolder fFolder = new FavoritesFolder(user);
		fFolder.setCreatedBy(null);
		fFolder.setUpdatedBy(null);
		fFolder.insertWithPropagationRequired();
	}

	@SuppressWarnings("rawtypes")
	private static HashMap<String, Object> createMigratedAttributeMap(HttpSession session) {
		HashMap<String, Object> attributesToMigrate = null;

		attributesToMigrate = new HashMap<>();

		Enumeration enumer = session.getAttributeNames();

		while (enumer.hasMoreElements()) {
			String key = (String) enumer.nextElement();
			attributesToMigrate.put(key, session.getAttribute(key));
		}

		return attributesToMigrate;
	}

	/*
	 * Create a new user based on passed User object on passed Node
	 */
	public static User createNodeUser(User user, Node node, AccessControlVO accessVO, int adminStatus){

		User homeDcsUser = user.getHomeDcsUser();
		Branch homeDcsBranch = homeDcsUser.getDomainOfThisUser();
		Node dcsNode = homeDcsBranch.getDcsNode();

		SessionHolder mainSessionHolder = null;
		User newUser;
		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(node.getSchemaName());

			// create new user, set role to roleId
			newUser = UserUtil.createNewUser(homeDcsUser);

			int idPType = User.IDP_TYPE_SSO_DCS_USER;
			String idPUserGuid = homeDcsUser.getIdPUserGuid();
			String idPIdentifier = homeDcsUser.getIdPIdentifier();
			String idPSubject = null;

			if (!homeDcsUser.isSSOUser()) {
				idPUserGuid = homeDcsUser.getGuid();
				idPIdentifier = dcsNode.getGuid();
				if (node.getBranch().getId() == homeDcsBranch.getId()) {
					// The node is from the current domain
					//
					if (node.isDcsNode()) {
						idPType = User.IDP_TYPE_MP_DCS_USER;
						idPUserGuid = null;
						idPIdentifier = null;
					} else
						idPType = User.IDP_TYPE_MP_INSTANCE_USER;
				} else {
					idPType = User.IDP_TYPE_MP_EXTERNAL_USER;
					if (!node.isDcsNode())
						idPSubject = node.getBranch().getDcsNode().getGuid();
				}
			} else {
				if (node.isDcsNode())
					idPType = User.IDP_TYPE_SSO_DCS_USER;
				else
					idPType = User.IDP_TYPE_SSO_INSTANCE_USER;

				if (node.getBranch().getId() != homeDcsBranch.getId())
					idPSubject = dcsNode.getGuid();
			}

			newUser.setIdPType(idPType);
			newUser.setIdPIdentifier(idPIdentifier);
			newUser.setIdPUserGuid(idPUserGuid);
			newUser.setIdPSubject(idPSubject);
			newUser.setIdPUserGroups(homeDcsUser.getIdPUserGroups());

			if (node.getBranch().getId() != homeDcsBranch.getId())
				newUser.setDefaultNodeId(0);

			Set<Role> roles = new HashSet<>();
			if (accessVO != null) {
				newUser.setAccountEnabled(accessVO.isEnabled());

				for (Role role : accessVO.getRoles()) {
					Role existingRole = Role.findByName(role.getName());
					if (existingRole == null) {
						existingRole = Role.findById(role.getId());
					}
					roles.add(existingRole == null ? role : existingRole);
				}

				if (roles.isEmpty())
					roles.addAll(RolesUtil.createSetForRole(Role.findById(Role.RESTRICTED_ACCESS_ROLE)));

				newUser.setRoles(roles);
				newUser.setLicensedType(newUser.getLicenceTypeByRole());
				newUser.setWorkgroupId(accessVO.getWorkgroupId());
				newUser.setHiddenSupervisor(accessVO.isHiddenUser());
				newUser.setEmailNotifyRealTime(accessVO.isDomainEmailNotification());
			} else {
				newUser.setAccountEnabled(false);

				for (Role role : user.getRoles()) {
					Role existingRole = Role.findByName(role.getName());
					if (existingRole == null) {
						existingRole = Role.findById(role.getId());
					}
					roles.add(existingRole == null ? role : existingRole);
				}

				if (roles.isEmpty())
					roles.addAll(RolesUtil.createSetForRole(Role.findById(Role.RESTRICTED_ACCESS_ROLE)));

				newUser.getRoles().addAll(roles);
				newUser.setLicensedType(user.getLicensedType());
				newUser.setWorkgroupId(Workgroup.DEFAULT_WORKGROUP);
				newUser.setHiddenSupervisor(user.isHiddenSupervisor());
				newUser.setEmailNotifyRealTime(homeDcsUser.isEmailNotifyRealTime());
			}

			newUser.setMasterAdminStatus(adminStatus);
			newUser.save();

			if (!roles.isEmpty()) {
				for (Role role : new ArrayList<>(roles)) {
					role.getUsers().add(newUser);
					role.save();
				}
			}

			// add folders for inserts
			UserUtil.createUserFolders(newUser);
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		return newUser;
	}

	public static String encryptPassword(String rawPassword) {
		String encryptedPassword = "";
		MessageDigestPasswordEncoder mdpe = (MessageDigestPasswordEncoder)ApplicationUtil.getBean("sha1Encoder");
		encryptedPassword = mdpe.encodePassword(rawPassword, null);

		return encryptedPassword;
	}

	/**
	 * Called to extract the existing attributes from the session, prior to invalidating it. If
	 * {@code migrateAttributes} is set to {@code false}, only Spring Security attributes will be retained.
	 * All application attributes will be discarded.
	 * <p>
	 * You can override this method to control exactly what is transferred to the new session.
	 *
	 * @param session the session from which the attributes should be extracted
	 * @return the map of session attributes which should be transferred to the new session
	 */
	protected static Map<String, Object> extractAttributes(HttpSession session) {
		return createMigratedAttributeMap(session);
	}

	public static String generateNewUserPassword() {
		String key = RandomStringUtils.randomAlphabetic(1).toUpperCase();
		String random = RandomGUID.getGUID().substring(1);
		return random.concat(key);
	}

	@SuppressWarnings("unchecked")
	public static List<User> getActiveUsers() {
		List<User> list = (List<User>) HibernateUtil.getManager().getObjectsAdvanced("from User as u where u.accountActive=true");
		return list;
	}

	@SuppressWarnings("unchecked")
	public static List<User> getActiveAndNotSupervisorUsers() {
		List<User> list = (List<User>) HibernateUtil.getManager().getObjectsAdvanced(
				"from User as u where u.accountActive=true and u.hiddenSupervisor=false " + (UserUtil.getPrincipalUserId() == 1L? "": "and u.id != 1 ") + "and u.id NOT BETWEEN 900 AND 999");
		return list;
	}

	// Get Active users
	public static Set<User> getActiveUsers(Set<User> userList) {

		Set<User> activeUserlist = new HashSet<>();

		Iterator<User> userItr = userList.iterator();
		while (userItr.hasNext()) {
			User user = userItr.next();
			if (user.isAccountActive() && !user.isHiddenSupervisor() && !user.isPrinovaUser())
				activeUserlist.add(user);
		}
		return activeUserlist;
	}

	public static boolean getAllAuthorized(Collection<Permission> permissions) {
		if (permissions == null) {
			return false;
		} else if (permissions.isEmpty()) {
			return false;
		} else {
			for (Permission permission : permissions) {
				if (!isPermissionGranted(permission)) {
					return false;
				}
			}
			return true;
		}
	}

	@SuppressWarnings("unchecked")
	public static List<User> getAllUsers() {
		List<User> list = (List<User>) HibernateUtil.getManager().getObjectsAdvanced(
				"from User as u where " + (UserUtil.getPrincipalUserId() == 1L? "": "u.id != 1 and ") + "u.id NOT BETWEEN 900 AND 999");
		return list;
	}

	private static String getAllUsersByUserQuery(User requestor, boolean defaultNode) {
		String query = null;
		if( requestor == null ?
				UserUtil.isPermissionGranted(Permission.ID_ROLE_ADMIN_MASTER_USER) || UserUtil.isPermissionGranted(Permission.ID_ROLE_ADMIN_MASTER_ADMIN) : requestor.isDomainAdminUser()
		) {
			if(defaultNode){
				query = "select distinct u from User as u left join fetch u.roles where " + (UserUtil.getPrincipalUserId() == 1L? "": "u.id != 1 AND ") + "u.id NOT BETWEEN 900 AND 999";
			}else{
				query = "select distinct u from User as u left join fetch u.roles where " + (UserUtil.getPrincipalUserId() == 1L? "": "u.id != 1 AND ") + "u.id NOT BETWEEN 900 AND 999 AND u.accountEnabled = true";
			}
		} else if( requestor == null ?
				UserUtil.isPermissionGranted(Permission.ID_ROLE_ADMIN_USER_EDIT) || UserUtil.isPermissionGranted(Permission.ID_ROLE_ADMIN_USER_VIEW) :
				UserUtil.isPermissionGranted(requestor, Permission.ID_ROLE_ADMIN_USER_EDIT) || UserUtil.isPermissionGranted(requestor, Permission.ID_ROLE_ADMIN_USER_VIEW)
		){
			if(defaultNode){
				query = "select distinct u from User as u left join fetch u.roles where " + (UserUtil.getPrincipalUserId() == 1L? "": "u.id != 1 AND ") + "u.id NOT BETWEEN 900 AND 999 AND (u.hiddenSupervisor = false OR u.idPType = 0)";
			}else{
				query = "select distinct u from User as u left join fetch u.roles where " + (UserUtil.getPrincipalUserId() == 1L? "": "u.id != 1 AND ") + "u.id NOT BETWEEN 900 AND 999 AND u.accountEnabled = true AND (u.hiddenSupervisor = false OR u.idPType = 0)";
			}
		}
		return query;
	}

	@SuppressWarnings("unchecked")
	public static List<User> getAllUsersByUser(User requestor, String currentSchema, boolean defaultNode) {
		if (requestor == null)
			return null;

		List<User> list = new ArrayList<>();
		String dbQuery;

		SessionHolder mainSessionHolder = null;
		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(currentSchema);
			dbQuery = getAllUsersByUserQuery(requestor, defaultNode);
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		if (dbQuery != null) {
			list = (List<User>) HibernateUtil.getManager().getObjectsAdvanced(dbQuery);
		}

		return list;
	}

	@SuppressWarnings("unchecked")

	public static List<User> getUsersForExportByRequestor(User requestor, Branch branch) {
		if(requestor == null)
			return Collections.emptyList();
		List<User> list = new ArrayList<>();
		String dbQuery = getAllUsersByUserQuery(requestor, true);
		if (dbQuery != null) {
			SessionHolder mainSessionHolder = null;
			try {
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(branch.getDcsSchemaName());
				list = (List<User>) HibernateUtil.getManager().getObjectsAdvanced(dbQuery + " order by u.firstName, u.lastName, u.username, u.id");
			} finally {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}
		}
		return list;
	}

	private static List<Permission> getAllViewPermission(List<Permission> unfilterList){
		List<Permission> retList = new ArrayList<>();
		for(Permission per:unfilterList){
			if(per!=null && per.getType()!=null && (Integer.parseInt(per.getType())==Permission.TYPE_VIEW_VALUE||Integer.parseInt(per.getType())==Permission.TYPE_HIDDEN_VALUE)){
				retList.add(per);
			}
		}
		return retList;
	}

	public static boolean getAnyAuthorized(Collection<Permission> permissions) {
		if (permissions == null) {
			return false;
		}
		for (Permission permission : permissions) {
			if (isPermissionGranted(permission)) {
				return true;
			}
		}
		return false;
	}

	public static String getAppLangCodeForPrincipal() {
		User principal = UserUtil.getPrincipalUser();
		if ( principal != null ) {
			long userId = principal.getId();
			User user = User.findById(userId);
			if (user != null)
				return user.getAppLocaleLangCode();
		}

		return ApplicationLocale.resolveMissingUserApplicationLocale().getLanguageCode();
	}

	public static String getUsernameForPrincipal() {
		User principal = UserUtil.getPrincipalUser();
		if ( principal != null ) {
			return principal.getUsername();
		}
		return null;
	}

	/**
	 * Get users with the given permission
     */
	public static List<User> getAuthUsers(long permissionId) {
		Permission permission = Permission.findById(permissionId);
		if (permission == null) {
			return null;
		}
		List<User> users = getActiveAndNotSupervisorUsers();
		List<User> results = new ArrayList<>();
		for (User user : users) {
			if (getUsersAggregatePermissions(user).contains(permission)) {
				results.add(user);
			}
		}
		return results;

	}

	public static Document getCurrentTouchpointContext() {
		User principal = UserUtil.getPrincipalUser();
		return getUserTouchpointContext(principal);
	}

	public static Document getUserTouchpointContext(User user) {
		if (user != null)
		{
			User _usr = User.findById(user.getId());
			if (_usr != null && _usr.getContextAttributes() != null) {
				try {
					JSONObject attrJSON = new JSONObject(_usr.getContextAttributes());
					if (attrJSON.has(UserUtil.CONTEXT_KEY_TOUCHPOINT_CONTEXT))
						return Document.findById((Long.valueOf(attrJSON.get(UserUtil.CONTEXT_KEY_TOUCHPOINT_CONTEXT).toString())));
				} catch (JSONException e) {
					log.error("UserUtil: Unable to read user context attributes: " + e.getMessage(), e);
				}
			}
		}
		return null;
	}

	public static MessagepointLocale getCurrentLanguageLocaleContext() {
		MessagepointLocale langLocale = getCurrentTouchpointContext() != null ? getCurrentTouchpointContext().getRootDocument().getDefaultTouchpointLanguageLocale() : null;
		langLocale = langLocale == null ? MessagepointLocale.getDefaultSystemLanguageLocale() : langLocale;
		User principal = UserUtil.getPrincipalUser();
		if (principal != null)
		{
			User user = User.findById(principal.getId());
			if (user != null && user.getContextAttributes() != null) {
				try {
					JSONObject attrJSON = new JSONObject(user.getContextAttributes());
					if (attrJSON.has(UserUtil.CONTEXT_KEY_LANGUAGE_CONTEXT))
						return MessagepointLocale.getLanguageLocaleByLocaleId((Long.valueOf(attrJSON.get(UserUtil.CONTEXT_KEY_LANGUAGE_CONTEXT).toString())));
				} catch (JSONException e) {
					log.error("UserUtil: Unable to read user context attributes: Applying default language code: " + e.getMessage(), e);
				}
			}
		}
		return langLocale;
	}

	public static SelectionStatusFilterType getCurrentSelectionStatusContext() {
		SelectionStatusFilterType selectionStatus = new SelectionStatusFilterType(SelectionStatusFilterType.ID_WORKING_COPIES);
		User principal = UserUtil.getPrincipalUser();
		if (principal != null)
		{
			User user = User.findById(principal.getId());
			if (user != null && user.getContextAttributes() != null) {
				try {
					JSONObject attrJSON = new JSONObject(user.getContextAttributes());
					if (attrJSON.has(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_STATUS_CONTEXT) && attrJSON.has(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT))
					{
						TouchpointSelection touchpointSelection = TouchpointSelection.findById((Long.valueOf(attrJSON.get(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT).toString())));
						if (touchpointSelection != null && !touchpointSelection.isMaster())
							return new SelectionStatusFilterType((Integer.valueOf(attrJSON.get(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_STATUS_CONTEXT).toString())));
					}
				} catch (JSONException e) {
					log.error("UserUtil: Unable to read user context attributes: Applying selection status: Working copy: " + e.getMessage(), e);
				}
			}
		}
		return selectionStatus;
	}

	public static TouchpointSelection getCurrentSelectionContext() {
		return getCurrentSelectionContext(null);
	}
	public static TouchpointSelection getCurrentSelectionContext(IdentifiableMessagePointModel contextObject) {
		User principal = UserUtil.getPrincipalUser();
		if (principal != null)
		{
			Document contextDocument = null;
			ContentObject contextMessage = null;
			if ( contextObject != null ) {

				if ( contextObject instanceof Zone )
					contextDocument = ((Zone)contextObject).getDocument();
				else if ( contextObject instanceof DocumentSection )
					contextDocument = ((DocumentSection)contextObject).getDocument();
				else if ( contextObject instanceof Document )
					contextDocument = (Document)contextObject;
				else if ( contextObject instanceof ContentObject ) {
					contextDocument = ((ContentObject)contextObject).getDocument();
					contextMessage = (ContentObject)contextObject;
				}

				if ( contextDocument != null ) {
					while (contextDocument.getParentObject() != null )
						contextDocument = contextDocument.getParent();
				}
			}

			User user = User.findById(principal.getId());
			if (user != null && user.getContextAttributes() != null) {
				try {
					JSONObject attrJSON = new JSONObject(user.getContextAttributes());
					if (attrJSON.has(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT)) {

						TouchpointSelection contextSelection = TouchpointSelection.findById((Long.valueOf(attrJSON.get(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT).toString())));

						// CONTEXT MISMATCH: Reset selection context if context doesn't match
						if ( contextDocument != null ) {

							if ( !contextDocument.isEnabledForVariation() ) {
								return null;
							} else if ( contextSelection == null || contextSelection.getDocument().getRootDocument().getId() != contextDocument.getRootDocument().getId()  ) {

								TouchpointSelection rootSelection = null;
								if (contextMessage != null ) {
									if ( contextMessage.getOwningTouchpointSelection() != null )
										rootSelection = contextMessage.getOwningTouchpointSelection();
									else
										rootSelection = contextMessage.getDocuments().iterator().next().getMasterTouchpointSelection();
								} else {
									rootSelection = contextDocument.getMasterTouchpointSelection();
								}

								HashMap<String,String> contextAttr = new HashMap<>();
								if ( rootSelection != null )
									contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT, String.valueOf(rootSelection.getId()));
								UserUtil.updateUserContextAttributes(contextAttr);

								return rootSelection;
							}
						}

						return contextSelection;
					}
				} catch (JSONException e) {
					log.error("UserUtil: Unable to read user context attributes: Applying null selection status: " + e.getMessage(), e);
				}
			}
		}
		return null;
	}

	public static Boolean getCurrentGlobalContext() {
		User principal = UserUtil.getPrincipalUser();
		return getUserGlobalContext(principal);
	}

	public static Boolean getUserGlobalContext(User user) {
		if (user != null)
		{
			User _usr = User.findById(user.getId());
			if (_usr != null && _usr.getContextAttributes() != null) {
				try {
					JSONObject attrJSON = new JSONObject(_usr.getContextAttributes());
					if (attrJSON.has(UserUtil.CONTEXT_KEY_GLOBAL_CONTEXT)) {
						Boolean isContextGlobal = attrJSON.getBoolean(UserUtil.CONTEXT_KEY_GLOBAL_CONTEXT);
						return isContextGlobal;
					}
				} catch (JSONException e) {
					log.error("UserUtil: Unable to read user context attributes: Applying FALSE global context status: " + e.getMessage(), e);
				}
			}
		}
		return false;
	}

	public static TouchpointCollection getCurrentCollectionContext() {
		User principal = UserUtil.getPrincipalUser();
		if (principal != null)
		{
			User user = User.findById(principal.getId());
			if (user != null && user.getContextAttributes() != null) {
				try {
					JSONObject attrJSON = new JSONObject(user.getContextAttributes());
					if (attrJSON.has(UserUtil.CONTEXT_KEY_COLLECTION_CONTEXT))
						return TouchpointCollection.findById((Long.valueOf(attrJSON.get(UserUtil.CONTEXT_KEY_COLLECTION_CONTEXT).toString())));
				} catch (JSONException e) {
					log.error("UserUtil: Unable to read user context attributes: " + e.getMessage(), e);
				}
			}
		}
		return null;
	}

	public static RationalizerApplication getCurrentRationalizerApplicationContext() {
		User principal = UserUtil.getPrincipalUser();
		if (principal != null)
		{
			User user = User.findById(principal.getId());
			if (user != null && user.getContextAttributes() != null) {
				try {
					JSONObject attrJSON = new JSONObject(user.getContextAttributes());
					if (attrJSON.has(UserUtil.CONTEXT_KEY_RATIONALIZER_APPLICATION_CONTEXT))
						return RationalizerApplication.findById((Long.valueOf(attrJSON.get(UserUtil.CONTEXT_KEY_RATIONALIZER_APPLICATION_CONTEXT).toString())));
				} catch (JSONException e) {
					log.error("UserUtil: Unable to read user context attributes: " + e.getMessage(), e);
				}
			}
		}
		return null;
	}

	public static Channel getCurrentChannelContext() {
		User principal = UserUtil.getPrincipalUser();
		if (principal != null)
		{
			User user = User.findById(principal.getId());
			if (user != null && user.getContextAttributes() != null) {
				try {
					JSONObject attrJSON = new JSONObject(user.getContextAttributes());
					if (attrJSON.has(UserUtil.CONTEXT_KEY_CHANNEL_CONTEXT)) {

						long persistedChannelContextId = Long.valueOf( attrJSON.get(UserUtil.CONTEXT_KEY_CHANNEL_CONTEXT).toString() );

						// CONTEXT MISMATCH: Manage current document channel context does not match persisted context
						Document currentDocumentContext = getCurrentTouchpointContext();
						long currentDocumentChannelContextId = currentDocumentContext.getConnectorConfiguration().getChannel().getId();
						if ( currentDocumentContext != null ) {
							if (  (!currentDocumentContext.getIsOmniChannel() && currentDocumentChannelContextId != persistedChannelContextId) ||
									(currentDocumentContext.getIsOmniChannel() &&
                                            currentDocumentContext.getChannelAlternateByType(persistedChannelContextId).isEmpty() && currentDocumentChannelContextId != persistedChannelContextId)
							) {
								HashMap<String,String> contextAttr = new HashMap<>();
								contextAttr.put(UserUtil.CONTEXT_KEY_CHANNEL_CONTEXT, String.valueOf(currentDocumentChannelContextId));
								UserUtil.updateUserContextAttributes(contextAttr);

								return Channel.findById(currentDocumentChannelContextId);
							}
						}

						return Channel.findById( persistedChannelContextId );

					}
				} catch (JSONException e) {
					log.error("UserUtil: Unable to read user context attributes: " + e.getMessage(), e);
				}
			}
		}

		if (getCurrentTouchpointContext() != null && !getCurrentTouchpointContext().getTrashTouchpoint() && getCurrentTouchpointContext().getConnectorConfiguration() != null) {
			return getCurrentTouchpointContext().getConnectorConfiguration().getChannel();
		}

		return null;
	}

	public static Document getCurrentChannelDocumentContext(Document parentDocument) {

		if ( parentDocument == null )
			return getCurrentTouchpointContext();

		Document currentChannelDocument = parentDocument;

		if ( !currentChannelDocument.getRootDocument().getIsOmniChannel() )
			return currentChannelDocument;

		long channelContextId = UserUtil.getCurrentChannelContext().getId();
		if ( channelContextId != currentChannelDocument.getConnectorConfiguration().getChannel().getId() ) {
			if ( channelContextId == Channel.CHANNEL_COMPOSITION )
				currentChannelDocument = parentDocument.getChannelAlternateByType( Channel.CHANNEL_COMPOSITION ).get(0);
			else if ( channelContextId == Channel.CHANNEL_EMAIL_ID )
				currentChannelDocument = parentDocument.getChannelAlternateByType( Channel.CHANNEL_EMAIL_ID ).get(0);
			else if ( channelContextId == Channel.CHANNEL_WEB_ID )
				currentChannelDocument = parentDocument.getChannelAlternateByType( Channel.CHANNEL_WEB_ID ).get(0);
		}

		return currentChannelDocument;
	}

	public static String getEmailToken(long id){
		User user = User.findById(id);
		String emailToken = "";
		if(user != null){
			if(user.getEmailToken() == null || user.getEmailToken().isEmpty()){
				emailToken = user.generateNewEmailToken();
				user.setEmailToken(emailToken);
				user.save();
			}else{
				emailToken = user.getEmailToken();
			}
		}
		return emailToken;
	}

	public static User getNodeUserByEmailAndIdPType(String email, String schemaName, int idPType) {

		Node node = Node.findBySchema(schemaName);
		if (node == null)
			return null;

		SessionHolder mainSessionHolder = null;
		User user = null;
		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
			ArrayList<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.eq("idPType", idPType));
			critList.add(MessagepointRestrictions.eq("email", email).ignoreCase());
			List<User> userList = HibernateUtil.getManager().getObjectsAdvanced(User.class, critList);
			if (userList != null && !userList.isEmpty()) {
				user = userList.get(0);
			}
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		return user;
	}

	public static User getNodeUserByUserName(String userName, String schemaName) {

		Node node = Node.findBySchema(schemaName);
		if (node == null)
			return null;

		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("username",userName).ignoreCase());
		return CloneHelper.queryInSchema(schemaName, ()->HibernateUtil.getManager().getObjectUnique(User.class, critList));
	}

	public static User getNodeUserByEmailToken(String emailToken, String schemaName) {

		Node node = Node.findBySchema(schemaName);
		if (node == null)
			return null;

		SessionHolder mainSessionHolder = null;
		User user;
		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
			ArrayList<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.eq("emailToken", emailToken).ignoreCase());
			user = HibernateUtil.getManager().getObjectUnique(User.class, critList);
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		return user;
	}

	public static User getNodeUserByUserNameAndIdPType(String userName, String schemaName, int idPType) {

		Node node = Node.findBySchema(schemaName);
		if (node == null)
			return null;

		SessionHolder mainSessionHolder = null;
		User user;
		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
			ArrayList<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.eq("idPType", idPType));
			critList.add(MessagepointRestrictions.eq("username", userName).ignoreCase());
			user = HibernateUtil.getManager().getObjectUnique(User.class, critList);
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		return user;
	}

	public static User getNodeUserById(Long id, String schemaName) {

		Node node = Node.findBySchema(schemaName);
		if (node == null)
			return null;

		SessionHolder mainSessionHolder = null;
		User user;
		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
			user = User.findById(id);

			if (user != null) {
				Hibernate.initialize(user.getPermissions());
				for (Role role : user.getRoles()) {
					Hibernate.initialize(role.getPermissions());
				}
				Hibernate.initialize(user.getPasswordHistories());
			}
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		return user;
	}

	public static Set<Role> getNodeUserRolesByUserID(long userId, String schemaName) {
		Node node = Node.findBySchema(schemaName);
		if (node == null)
			return null;

		SessionHolder mainSessionHolder = null;
		Set<Role> userRoles;
		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
			User foundUser = User.findById(userId);
			if (foundUser == null)
				return null;
			userRoles = foundUser.getRoles();
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		return userRoles;
	}

	public static int getNumActiveUsersByRole(int accessType, String branchSsoIdPId, String schema) {
		SessionHolder mainSessionHolder = null;
		int count = 0;
		Map<String, Object> params = new HashMap<>();
		try {
			String query = "select count(*) from User u " +
					"inner join u.roles as role " +
					"where u.accountActive = 1 and u.accountEnabled = true and u.accountSoftDeactivated = 0";
			if (accessType == User.LICENSED_TYPE_REGULAR_ACCESS) {
				query += " and role.id != :limitedRoleId and role.id != :connectedRoleId and role.id != :restrictedRoleId";
				params.put("limitedRoleId", Role.WORKFLOW_ACCESS_ROLE);
				params.put("connectedRoleId", Role.CONNECTED_ACCESS_ROLE);
				params.put("restrictedRoleId", Role.RESTRICTED_ACCESS_ROLE);
			} else if (accessType == User.LICENSED_TYPE_WORKFLOW_ACCESS) {
				query += " and role.id = :limitedRoleId";
				params.put("limitedRoleId", Role.WORKFLOW_ACCESS_ROLE);
			}else if (accessType == User.LICENSED_TYPE_CONNECTED_ACCESS) {
				query += " and role.id = :connectedRoleId";
				params.put("connectedRoleId", Role.CONNECTED_ACCESS_ROLE);
			}
			query += " and (u.idPType = :localIdpType";
			params.put("localIdpType", User.IDP_TYPE_MP_DCS_USER);
			if (branchSsoIdPId != null) { // ssoLocal
				query += " or (u.idPType = :externalPingSsoType and lower(u.idPIdentifier) = :branchSsoIdPId)";
				params.put("externalPingSsoType", User.IDP_TYPE_SSO_DCS_USER);
				params.put("branchSsoIdPId", branchSsoIdPId.toLowerCase());
			}
			query += ") and u.id != 1 and u.id NOT BETWEEN 900 and 999";
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
			@SuppressWarnings("unchecked")
			List<Long> counts = (List<Long>) HibernateUtil.getManager().getObjectsAdvanced(query, params);
			if (counts != null)
				count = counts.get(0).intValue();
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		return count;
	}

	public static User getPrincipalUser() {
		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		User principal = null;
		if (auth != null) {
			Object authorizedUser = auth.getPrincipal();
			if (authorizedUser instanceof User) {
				principal = (User) authorizedUser;
			}
			else if(authorizedUser instanceof ApiUser){
				principal = ((ApiUser)authorizedUser).getWebServiceUser();
			}
		}

		if (principal == null) {
			principal = MessagepointCurrentTenantIdentifierResolver.getUser();
		}

        if (principal == null) {
            if(CloneHelper.getIsCloningActive() || CloneHelper.getIsSynchronizing()) {
                principal = CloneHelper.getRequestor();
            }
        }

        return principal;
	}

	public static long getPrincipalUserId() {
		User user = getPrincipalUser();
		if (user != null)
			return user.getId();
		return 0L;
	}

	public static String getReturnFromSetupPageContextPath() {
		User principal = UserUtil.getPrincipalUser();
		if (principal != null)
		{
			User user = User.findById(principal.getId());
			if (user != null && user.getContextAttributes() != null) {
				try {
					JSONObject attrJSON = new JSONObject(user.getContextAttributes());
					if (attrJSON.has(UserUtil.CONTEXT_KEY_RETURN_FROM_SETUP_PAGE_CONTEXT)) {
						String path = attrJSON.getString(UserUtil.CONTEXT_KEY_RETURN_FROM_SETUP_PAGE_CONTEXT);
						if ( path.indexOf("touchpoint_content_list") != -1 )
							path = path.replace("localContext=0","localContext=1").replace("localContext=-1","localContext=1");
						return path;
					}
				} catch (JSONException e) {
					log.error("UserUtil: Unable to read user context attributes: Applying touchpoint message list return path: " + e.getMessage(), e);
				}
			}
		}
		return "/touchpoints/touchpoint_content_object_list.form";
	}

	/**
	 * Aggregates user permissions by iteration over
	 * {@link User#getPermissions()} and Users {$link
	 * {@link Role#getPermissions()}}
	 */
	public static List<Permission> getUsersAggregatePermissions(User user) {
		if (user == null) {
			return new ArrayList<>();
		}

		List<GrantedAuthority> authoritiesList = getUsersAggregateAuthorities(user);

		List<Permission> permissions = convertAuthorityToPermission(authoritiesList);

		return permissions;
	}

	public static List<GrantedAuthority> getUsersAggregateAuthorities(User user) {
		if (user == null) {
			return new LinkedList<>();
		}

        Set<GrantedAuthority> authoritiesSet = new HashSet<>(convertPermissionsToAuthorities(RolesUtil.getAllPermissions(user)));

		List<GrantedAuthority> authoritiesList = new ArrayList<>(authoritiesSet);
		addSystemAuthorities(user, authoritiesList);
		addSimulationAuthorities(user, authoritiesList);
		addInsertManagementAuthorities(user, authoritiesList);
		addMessagepointInteractiveAuthorities(user, authoritiesList);
		addVariantManagementAuthorities(user, authoritiesList);
		addECatalogAuthorities(user, authoritiesList);
		addLookupTableAuthorities(user, authoritiesList);
		addPINCAuthorities(user, authoritiesList);

		return authoritiesList;
	}


	public static ModelAndView getViewForTouchpointContextMismatch(long documentId, String successView, Map<String, Object> params, boolean applyMessagePerm) {

		Document contextTouchpoint = UserUtil.getCurrentTouchpointContext();
		Document targetDocument = Document.findById(documentId);

		if (targetDocument == null)
			return null;

		// User context mismatch
		if ( targetDocument == null || (contextTouchpoint != null && contextTouchpoint.getId() != documentId) ) {
			HashMap<String,String> contextAttr = new HashMap<>();
			contextAttr = updateContextAttrForDocumentToggle(targetDocument, contextAttr);
			// Update user context properties
			UserUtil.updateUserContextAttributes(contextAttr);
			params.put(AsyncListTableController.PARAM_DOCUMENT_ID, UserUtil.getCurrentTouchpointContext());
			return new ModelAndView(new RedirectView(successView), params);
		}

		// Disabled context mismatch
		if ( applyMessagePerm ) {

			if ( !targetDocument.isVisible() ||
					(!targetDocument.isEnabledForVariation() && !( UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_MY) )) ) {
				params = new HashMap<>();
				List<Document> visibleDocuments = Document.getVisibleDocuments();
				long targetDocId = !visibleDocuments.isEmpty() ? visibleDocuments.iterator().next().getId() : -2;

				// Document is visible but user is not permission to view standard touchpoint
				if ( (!targetDocument.isEnabledForVariation() && !( UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_MY) )) )
					for (Document currentDocument: visibleDocuments)
						if ( currentDocument.isEnabledForVariation() )
							targetDocId = currentDocument.getId();

				params.put(AsyncListTableController.PARAM_DOCUMENT_ID, targetDocId);
				return new ModelAndView(new RedirectView(successView), params);
			}

		} else {

			if ( !targetDocument.isVisible() ) {
				params = new HashMap<>();
				List<Document> visibleDocuments = Document.getVisibleDocuments();
				params.put(AsyncListTableController.PARAM_DOCUMENT_ID, !visibleDocuments.isEmpty() ? visibleDocuments.iterator().next().getId() : -2);
				return new ModelAndView(new RedirectView(successView), params);
			}

		}

		return null;
	}

	public static ModelAndView getViewForContextTouchpoint(String successView, Map<String, Object> params, boolean applyCollections) throws Exception {

		boolean isFreeform = false;
		if ( params.containsKey(GlobalContentListController.REQ_PARM_IS_FREEFORM) )
			isFreeform = (Boolean)params.get(GlobalContentListController.REQ_PARM_IS_FREEFORM);

		boolean isSetup = false;
		if ( params.containsKey(InsertScheduleListController.REQ_PARAM_IS_SETUP) )
			isSetup = (Boolean)params.get(InsertScheduleListController.REQ_PARAM_IS_SETUP);

		int localContentType = -1;
		if ( params.containsKey(ContentObjectDetailsEditController.PARAM_LOCAL_CONTENT_TYPE) )
			localContentType = (Integer)params.get(ContentObjectDetailsEditController.PARAM_LOCAL_CONTENT_TYPE);

		if ( applyCollections && UserUtil.getCurrentCollectionContext() != null ) {
			params = new HashMap<>();

			TouchpointCollection contextCollection = UserUtil.getCurrentCollectionContext();
			if ( !contextCollection.isVisible() || !contextCollection.isExecutable() ) {
				List<TouchpointCollection> visibleCollections = TouchpointCollection.findAllWithAllDocumentsVisible(true);
				contextCollection = !visibleCollections.isEmpty() ? visibleCollections.iterator().next() : null;
			}

			if ( contextCollection != null)
				params.put(AsyncListTableController.PARAM_COLLECTION_ID, contextCollection.getId());
			else {
				List<Document> visibleDocuments = Document.getVisibleDocuments();
				params.put(AsyncListTableController.PARAM_DOCUMENT_ID, !visibleDocuments.isEmpty() ? visibleDocuments.iterator().next().getId() : -2);
			}

			if (isFreeform)
				params.put(GlobalContentListController.REQ_PARM_IS_FREEFORM, true);

			if (isSetup)
				params.put(InsertScheduleListController.REQ_PARAM_IS_SETUP, true);

			if (localContentType > 0)
				params.put(ContentObjectDetailsEditController.PARAM_LOCAL_CONTENT_TYPE, localContentType);

			return new ModelAndView(new RedirectView(successView), params);
		} else if ( UserUtil.getCurrentTouchpointContext() != null ) {
			params = new HashMap<>();

			Document contextTouchpoint = UserUtil.getCurrentTouchpointContext();
			if ( !contextTouchpoint.isVisible() || !contextTouchpoint.isEnabled() ) {
				List<Document> visibleDocuments = Document.findAllDocumentsAndProjectsVisible();
				contextTouchpoint = !visibleDocuments.isEmpty() ? visibleDocuments.iterator().next() : null;
			}

			if (isFreeform)
				params.put(GlobalContentListController.REQ_PARM_IS_FREEFORM, true);

			if (isSetup)
				params.put(InsertScheduleListController.REQ_PARAM_IS_SETUP, true);

			if (localContentType > 0)
				params.put(ContentObjectDetailsEditController.PARAM_LOCAL_CONTENT_TYPE, localContentType);

			params.put(AsyncListTableController.PARAM_DOCUMENT_ID, contextTouchpoint != null ? contextTouchpoint.getId() : -2);
			return new ModelAndView(new RedirectView(successView), params);
		}

		return new ModelAndView(new RedirectView(successView), params);
	}

	public static String hashAndSaltPassword(String rawPassword, UserDetails user) {
		String encryptedPassword = "";
		MessageDigestPasswordEncoder mdpe = (MessageDigestPasswordEncoder)ApplicationUtil.getBean("passwordEncoder");
		Object saltSource = null;
		try {
			saltSource = (Object)ApplicationUtil.getBean("saltSource");
		} catch(NoSuchBeanDefinitionException e) {
			saltSource = null;
		}

		if ( user != null && saltSource != null )
		{
			SaltSource ss = (SaltSource)saltSource;
			encryptedPassword = mdpe.encodePassword(rawPassword, ss.getSalt(user));
		}
		else
		{
			encryptedPassword = mdpe.encodePassword(rawPassword, null);
		}
		return encryptedPassword;
	}

	public static boolean hasGrantedAuthorityOrNot(Collection<? extends GrantedAuthority> fromUserAuthorities, String permissionName){
		if(fromUserAuthorities!=null && permissionName!=null){
			for(GrantedAuthority authInArray:fromUserAuthorities){
				if(authInArray!=null && authInArray.getAuthority().equals(permissionName))
					return true;
			}
		}
		return false;
	}

	public static boolean isAllowed(User user, long permissionId) {
		if (user == null) {
			return false;
		}
		return isAllowed(user.getId(), permissionId);
	}

	public static boolean isAllowed(Long userId, long permissionId) {
		if (userId == null) {
			return false;
		}
		User u = User.findById(userId);

		Permission permission = Permission.findById(permissionId);
		if (permission == null) {
			return false;
		}

		return getUsersAggregatePermissions(u).contains(permission);

	}

	public static boolean isAnonymousUser(Object user) {
		if (user instanceof com.prinova.messagepoint.model.security.User) {
			return false;
		} else {
			return true;
		}
	}

	public static Boolean isMessagepointInteractiveRestrictedUser() {
		// Messagepoint Interactive: Users with only messagepoint interactive permissions should not see categories
		//							 Check for one menu item in Touchpoints category + additional Tasks category
		List<NavigationTab> tabs = HibernateUtil.getManager().getObjects(NavigationTab.class, MessagepointOrder.asc("ordering") );
		boolean isMessagepointInteraciveRestricted = false;
		int authorizedTabsCount = 0;
		int touchpointTabMenuCount = 0;
		for( int i=0; i<tabs.size(); i++ ) {
			if ( i == 0 )
				for ( DropDownMenu currentMenu : tabs.get(i).getMenus() )
					if ( currentMenu.isAuthorized() )
						touchpointTabMenuCount ++;
			if ( tabs.get(i).isAuthorized() )
				authorizedTabsCount++;
		}
		if (touchpointTabMenuCount == 1 && authorizedTabsCount == 2 )
			isMessagepointInteraciveRestricted = true;

		return isMessagepointInteraciveRestricted;
	}

	public static boolean isRationalizerEnabledUser(User user){
		boolean isRationalizeEnabledUser = false;
		Set<GrantedAuthority> authoritiesSet = new HashSet<>();
		for (Role role : user.getRoles()) {
			authoritiesSet.addAll(convertPermissionsToAuthorities(role.getPermissions()));
		}
		List<GrantedAuthority> authoritiesList = new ArrayList<>(authoritiesSet);
		if(hasECatalogAuthorities(user, authoritiesList))
			isRationalizeEnabledUser = true;
		return isRationalizeEnabledUser;
	}

	public static boolean isRationalizerOnlyUser(User user) {
		List<NavigationTab> tabs = HibernateUtil.getManager().getObjects(NavigationTab.class, MessagepointOrder.asc("ordering") );
		boolean isRationalizerOnlyUser = false;
		int authorizedTabsCount = 0;
		for( int i=0; i<tabs.size(); i++ ) {
			if ( tabs.get(i).isAuthorized() )
				authorizedTabsCount++;
		}

		if ( authorizedTabsCount == 1 ){ // only permission to Task tab
			Set<GrantedAuthority> authoritiesSet = new HashSet<>();
			for (Role role : user.getRoles()) {
				authoritiesSet.addAll(convertPermissionsToAuthorities(role.getPermissions()));
			}
			List<GrantedAuthority> authoritiesList = new ArrayList<>(authoritiesSet);
			if(hasECatalogAuthorities(user, authoritiesList))
				isRationalizerOnlyUser = true;
		}
		return isRationalizerOnlyUser;
	}

	public static boolean isPermissionGranted(User user, long permissionId) {
		return isAllowed(user, permissionId);
	}

	public static boolean isPermissionGranted(long permissionId) {
		return isPermissionGranted(getPrincipalUser(), permissionId);
	}

	public static boolean isPermissionGranted(Permission permission) {
		if ((null == permission) || ("".equals(permission.getName()))) {
			return false;
		}

		return isPermissionGranted(getPrincipalUser(), permission.getId());
	}

	public static boolean isPermissionGrantedSchema(long permissionId, String schemaName, long userId) {
		SessionHolder mainSessionHolder = null;
		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
			Permission permission = Permission.findById(permissionId);
			if ((null == permission) || ("".equals(permission.getName()))) {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
				return false;
			}
			User user = User.findById(userId);
			if (user == null) {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
				return false;
			}
			//check the User role -permission
			return (RolesUtil.getAllPermissions(user).contains(permission));
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}
	}

	@SuppressWarnings("unchecked")
	public static boolean isUniqueUsername(User user, String username) {
		if (StringUtils.isBlank(username)) {
			return false;
		}

		User homeDcsUser = user.getHomeDcsUser();

		SessionHolder mainSessionHolder = null;
		try {
			if (homeDcsUser != null) {
				String schema = homeDcsUser.getSchemaOfThisUser();
				/*
				 * Since we always need to find username on domain level, need to get domain schema if the homedcs is not DCS by accident
				 * for new user creation non DCS instance could return SchemaOfThisUser as none DCS
				 */
				Node dcsHomeNode = Node.findBySchema(schema);
				if (!dcsHomeNode.isDcsNode())
					schema = dcsHomeNode.getBranch().getDcsSchemaName();
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
			}
			HashMap<String, Object> params = new HashMap<>();
			params.put("username", username.trim().toLowerCase());

			String query = "from User u where lower(u.username) = :username and idPType = 0 and idPMPDomain is null";
			if (user.isSSOUser()) {
				query = "from User u where lower(u.username) = :username and idPIdentifier = :idPIdentifier and idPType = 3 and idPMPDomain is null";
				params.put("idPIdentifier", user.getIdPIdentifier());
			}

			if (user.getGuid() != null) {
				query = query + " and u.guid <> :currentUserGuid";
				params.put("currentUserGuid", user.getGuid());
			}

			List<User> users = (List<User>) HibernateUtil.getManager().getUnfilteredObjectsAdvanced(query, params, "anscestorFilter");
			return users.isEmpty();
		} finally {
			if (homeDcsUser != null)
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}
	}

	@SuppressWarnings("unchecked")
	public static boolean isUniqueEmail(User user, String email) {
		if (StringUtils.isBlank(email)) {
			return false;
		}

		User homeDcsUser = user.getHomeDcsUser();

		SessionHolder mainSessionHolder = null;
		try {
			if (homeDcsUser != null)
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(homeDcsUser.getSchemaOfThisUser());

			HashMap<String, Object> params = new HashMap<>();
			params.put("email", email.trim().toLowerCase());
			List<User> users = (List<User>) HibernateUtil.getManager().getObjectsAdvanced("from User as u where u.email like :email", params);
			return users.isEmpty();
		} finally {
			if (homeDcsUser != null)
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}
	}

	public static boolean isValidAlphanumericUsername(String username) {
		if (username == null) {
			return false;
		}
		if (username.trim().contains(" ")) {
			return false;
		}
		String usernameRegEx = StringUtil.injectI18Nchars("[A-Za-z0-9]++");
		return Pattern.matches(usernameRegEx, username);
	}

	public static boolean isValidAlphanumericAtDotDashUnserScoreAposUsername(String username) {
		if (username == null) {
			return false;
		}
		if (username.trim().contains(" ")) {
			return false;
		}
		String usernameRegEx = StringUtil.injectI18Nchars("^[A-Za-z0-9_\\-@.'#]*+");
		return Pattern.matches(usernameRegEx, username);
	}

	public static boolean isValidPassword(String password, User user) {
		PasswordValidator passwordValidator = (PasswordValidator) ApplicationUtil.getBean("passwordValidator");
		if (passwordValidator != null) {
			SimplePasswordValidationContext ctx = new SimplePasswordValidationContext(password, user);
			passwordValidator.validate(ctx);
			List<String> passwordErrors = ctx.getValidationErrors();

			if (!passwordErrors.isEmpty()) {
				return false;
			}
		}
		return true;
	}

	public static boolean isValidUsername(User user) {

		if (user == null) {
			return false;
		}

		String username = user.getUsername();

		if (username == null || username.isEmpty() || username.trim().contains(" ")) {
			return false;
		}

		if (user.isSuperuser() || user.isPodAdminUser() || user.isSSOUser()) {
			return true;
		}

		int minLength = 8;
		int maxLength = 32;

		String schemaName = user.getHomeDcsSchema();
		SecuritySettings ss = SecuritySettings.findForSchema(schemaName);

		if (ss != null) {
			maxLength = Integer.parseInt(ss.getUsernameMaxLength());
			minLength = Integer.parseInt(ss.getUsernameMinLength());
		}

		int index = username.indexOf('#');
		if (index != -1) {
			username = username.substring(0, index);
		}

		if (username.length() < minLength || username.length() > maxLength) {
			return false;
		}

		if (ss != null && ss.isAlphanumericOnly()) {
			return UserUtil.isValidAlphanumericUsername(username);
		}

		return true;
	}

	private static void onAuthentication(Authentication authentication, HttpServletRequest request, HttpServletResponse response) {
		boolean hadSessionAlready = request.getSession(false) != null;

		if (!hadSessionAlready) {
			// Session fixation isn't a problem if there's no session

			return;
		}

		// Create new session if necessary
		HttpSession session = request.getSession();

		if (request.isRequestedSessionIdValid()) {
			// We need to migrate to a new session
			String originalSessionId = session.getId();

			Map<String, Object> attributesToMigrate = extractAttributes(session);

			Object oidcAuth = session.getAttribute(PINCOpenIdConnectUtil.REQ_PARAM_OIDC_AUTH);

			session.invalidate();
			session = request.getSession(true); // we now have a new session

			if (oidcAuth != null) {
				session.setAttribute(PINCOpenIdConnectUtil.REQ_PARAM_OIDC_AUTH, oidcAuth);
			}

			if (log.isDebugEnabled()) {
				log.debug("Started new session: " + session.getId());
			}

			if (originalSessionId.equals(session.getId())) {
				log.warn("Your servlet container did not change the session ID when a new session was created. You will" +
						" not be adequately protected against session-fixation attacks");
			}

			transferAttributes(attributesToMigrate, session);

			// onSessionChange(originalSessionId, session, authentication);
		}
	}

	public static void retrieveAndSaveInSecurityContextPrincipalUserPermissions(User user) {
		if (user == null || user.getId() == 0) {
			return;
		}

		if ( (SecurityContextHolder.getContext() == null || SecurityContextHolder.getContext().getAuthentication() == null)
			&& !user.containsInvalidLoginAuthority()
		) {
			List<GrantedAuthority> authoritiesList = getUsersAggregateAuthorities(user);
			user.setAuthorities(authoritiesList);

			final User principalUserFromSessionOrDb = UserUtil.getPrincipalUser();
			if (principalUserFromSessionOrDb == null) {
				return;
			}

			if (principalUserFromSessionOrDb.getId() == user.getId()) {
				principalUserFromSessionOrDb.setAuthorities(authoritiesList);

				return;
			}

			if (!principalUserFromSessionOrDb.containsInvalidLoginAuthority()) {
				principalUserFromSessionOrDb.setAuthorities(getUsersAggregateAuthorities(principalUserFromSessionOrDb));
			}

			return;
		}

		Authentication currentAuthentication = SecurityContextHolder.getContext().getAuthentication();

		Object principal = currentAuthentication.getPrincipal();
		User principalUser = null;
		if (principal != null && principal instanceof User) {
			principalUser = (User) principal;
			if (principalUser.getId() != 0) {
				// Reload roles for principal user.
				User newPrincipalUser = User.findById(principalUser.getId());
				// Keep the previous authorities in order to check for InvalidLoginAuthority.
				User.copyNonDbAttributes(principalUser, newPrincipalUser);

				principalUser = newPrincipalUser;
				principal = newPrincipalUser;
			}
		}

		List<GrantedAuthority> principalUserAuthorities = null;
		if (principalUser != null && !principalUser.containsInvalidLoginAuthority()) {
			principalUserAuthorities = getUsersAggregateAuthorities(principalUser);
			principalUser.setAuthorities(principalUserAuthorities);
		}

		UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(principal,
				currentAuthentication.getCredentials(), principalUserAuthorities);
		result.setDetails(currentAuthentication.getDetails());
		SecurityContextHolder.getContext().setAuthentication(result);

		List<GrantedAuthority> userAuthorities = null;
		if (principalUser != null && principalUserAuthorities != null && user.getId() == principalUser.getId()) {
			userAuthorities = principalUserAuthorities;
		} else if (!user.containsInvalidLoginAuthority()) {
			userAuthorities = getUsersAggregateAuthorities(user);
		}

		if (userAuthorities != null) {
			user.setAuthorities(userAuthorities);
		}

		final User principalUserFromSessionOrDb = UserUtil.getPrincipalUser();
		if (principalUserFromSessionOrDb == null) {
			return;
		}

		if (principalUserFromSessionOrDb.getId() == principalUser.getId()) {
			principalUserFromSessionOrDb.setAuthorities(principalUserAuthorities);

			return;
		}

		if (principalUserFromSessionOrDb.getId() == user.getId()) {
			principalUserFromSessionOrDb.setAuthorities(userAuthorities);

			return;
		}

		if (!principalUserFromSessionOrDb.containsInvalidLoginAuthority()) {
			principalUserFromSessionOrDb.setAuthorities(getUsersAggregateAuthorities(principalUserFromSessionOrDb));
		}

	}

	private static void addPINCAuthorities(User user, List<GrantedAuthority> dbAuths) {
		if ( MessagepointLicenceManager.getInstance().isLicencedForPINC()) {
			Permission pincCompanyAdmin = Permission.findById(Permission.ROLE_PINC_COMPANY_ADMIN);
			Permission pincCompanyRead = Permission.findById(Permission.ROLE_PINC_COMPANY_READ);
			Permission pincCompanyTest = Permission.findById(Permission.ROLE_PINC_COMPANY_TEST);
			Permission pincCompanyProduction = Permission.findById(Permission.ROLE_PINC_COMPANY_PRODUCTION);
			Permission pincCompanyAuthor = Permission.findById(Permission.ROLE_PINC_COMPANY_AUTHOR);

			for (GrantedAuthority auth : dbAuths.toArray(new GrantedAuthority[] {})) {
				if (auth.getAuthority().equals(pincCompanyAdmin.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(pincCompanyAdmin.getName()));
				}

				if (auth.getAuthority().equals(pincCompanyRead.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(pincCompanyRead.getName()));
				}

				if (auth.getAuthority().equals(pincCompanyTest.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(pincCompanyTest.getName()));
				}

				if (auth.getAuthority().equals(pincCompanyProduction.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(pincCompanyProduction.getName()));
				}

				if (auth.getAuthority().equals(pincCompanyAuthor.getName())) {
					dbAuths.add(new SimpleGrantedAuthority(pincCompanyAuthor.getName()));
				}
			}
		}
	}

	/**
	 * Sends out the a password changed email.
	 *
	 * @param user
	 *            An email address
	 * @param newPassword
	 *            The clear text user password.
	 */
	public static void sendPasswordChangedEmail(User user, String newPassword) {
		if(user == null)
			return;
		Locale appLocale = new Locale(user.getAppLocaleLangCode());
		Node node = Node.getCurrentNode();
		String companyName = node.getBranch().getName();
		String formattedContent = MessageFormat.format(ApplicationUtil.getMessage("email.content.password.change.notification", appLocale),
				companyName, user.getUsername(), ApplicationUtil.getMessagepointBrandedLabel(appLocale));
		NotificationEventUtil.sendIndividualNotificationEmail(user, NotificationActionType.ID_PASSWORD_CHANGE, formattedContent, "", NotificationObjectType.ID_USER);
	}

	/**
	 * Sends out the an email depending on PasswordRecovery action for given username 
	 * This assumes PasswordRecovery has been created already for this user.
	 *
	 * @param email
	 *            An email address
	 * @param username
	 *            User account.
	 */
	public static void sendPasswordChangedEmail(String email, String username) {

		User user = User.findByUsername(username);
		if(user == null){
			throw new RuntimeException("User is not found with username [" + username + "]");
		}
		PasswordRecovery passwordRecovery = null;
		passwordRecovery = PasswordRecovery.findLastestUnusedByUser(user.getId());
		if(passwordRecovery == null){
			throw new RuntimeException("PasswordRecovery is not found with username [" + username + "]");
		}
		sendPasswordChangedEmail(passwordRecovery);
	}

	public static void sendSSOUserActivationEmail(User user) {

		Locale appLocale = new Locale(user.getAppLocaleLangCode());
		String previousState = ApplicationUtil.getProperty(Email.KEY_ApplicationSendEmailFlag);
		String url = ApplicationUtil.buildFullyQualifiedServerURLForEmail();


		Node node = Node.getCurrentNode();
		String link = "<a href=\"" + url + "\" >" + node.getBranch().getName() + "</a>";
		//subject: email.subheader.sso.user.activate
		String formattedContent = null;
		formattedContent = MessageFormat.format(ApplicationUtil.getMessage("email.content.sso.user.activate", appLocale),
				link, ApplicationUtil.getMessagepointBrandedLabel(appLocale));

		NotificationEventUtil.sendIndividualNotificationEmail(user, NotificationActionType.ID_ACTIVATION, formattedContent, url, NotificationObjectType.ID_USER);
		if ("false".equalsIgnoreCase(previousState)) {
			SystemPropertyManager.getInstance().addOrUpdateSystemProperty(Email.KEY_ApplicationSendEmailFlag, Boolean.toString(false), false);
		}
	}

	public static void sendPasswordChangedEmail(PasswordRecovery passwordrecovery) {

		String token = passwordrecovery.getResetKey();
		User user = passwordrecovery.getUser();
		Locale appLocale = new Locale(user.getAppLocaleLangCode());
		String previousState = ApplicationUtil.getProperty(Email.KEY_ApplicationSendEmailFlag);
		String url = ApplicationUtil.buildFullyQualifiedServerURLForEmail();

		url = url.concat("password_reset.form?token=");
		url = url.concat(token);
		String nodeGUID = "";
		Node node = Node.getCurrentNode();

		nodeGUID = node.getGuid();
		String companyName = node.getBranch() != null ? node.getBranch().getName() : "prinova";
		url = url.concat("&gd=" + nodeGUID);
		String actionUrl = "";
		if(!url.isEmpty()){
			actionUrl = "<a href=\"" + url + "\" >" + ApplicationUtil.getMessage("page.label.action", appLocale) + "</a>";
		}
		String formattedContent = null;
		int action = NotificationActionType.ID_PASSWORD_RESET;
		if(passwordrecovery.getAction() == CreateOrUpdatePasswordRecoveryServiceRequest.PR_ACTION_ACTIVATION){	// Activation
			action = NotificationActionType.ID_ACTIVATION;
			formattedContent = MessageFormat.format(ApplicationUtil.getMessage("email.content.password.reset.activate", appLocale),
					companyName, user.getUsername(), actionUrl, ApplicationUtil.getMessagepointBrandedLabel(appLocale));
		}else if (passwordrecovery.getAction() == CreateOrUpdatePasswordRecoveryServiceRequest.PR_ACTION_RESET_PASSWORD){
			formattedContent = MessageFormat.format(ApplicationUtil.getMessage("email.content.password.reset", appLocale),
					companyName, actionUrl);
		}else{	// Forget Password
			action = NotificationActionType.ID_FORGOT_PASSWORD;
			formattedContent = MessageFormat.format(ApplicationUtil.getMessage("email.content.password.forget", appLocale),
					companyName, actionUrl);
		}
		NotificationEventUtil.sendIndividualNotificationEmail(user, action, formattedContent, url, NotificationObjectType.ID_USER);

		if ("false".equalsIgnoreCase(previousState)) {
			SystemPropertyManager.getInstance().addOrUpdateSystemProperty(Email.KEY_ApplicationSendEmailFlag, Boolean.toString(false), false);
		}
	}

	public static void sendEmailVerificationNotification(User user) {

		String verificationToken = HttpRequestUtil.buildResourceToken()
				.add("userGuid", user.getGuid())
				.add("userEmail", user.getEmail())
				.add("nodeGuid", user.getNodeOfThisUser().getGuid()).toString();

		Locale appLocale = new Locale(user.getAppLocaleLangCode());
		String previousState = ApplicationUtil.getProperty(Email.KEY_ApplicationSendEmailFlag);
		String url = ApplicationUtil.buildFullyQualifiedServerURLForEmail();

		url = url.concat(MessageFormat.format("verify_email.form?{0}=", UserSettingsController.EmailTokenKeys.PARAM_EMAIL_TOKEN));
		url = url.concat(verificationToken);
		String nodeGUID = "";
		Node node = Node.getCurrentNode();

		nodeGUID = node.getGuid();
		String companyName = node.getBranch() != null ? node.getBranch().getName() : "prinova";
		url = url.concat("&gd=" + nodeGUID);
		String actionUrl = "";
		if(!url.isEmpty()){
			actionUrl = "<a href=\"" + url + "\" >" + ApplicationUtil.getMessage("page.label.action", appLocale) + "</a>";
		}
		String formattedContent = MessageFormat.format(ApplicationUtil.getMessage("email.content.email.verification.notification", appLocale),
				companyName, actionUrl, ApplicationUtil.getMessagepointBrandedLabel(appLocale));

		NotificationEventUtil.sendIndividualNotificationEmail(user, NotificationActionType.ID_VERIFY_NEW_EMAIL, formattedContent, url, NotificationObjectType.ID_USER);

		if ("false".equalsIgnoreCase(previousState)) {
			SystemPropertyManager.getInstance().addOrUpdateSystemProperty(Email.KEY_ApplicationSendEmailFlag, Boolean.toString(false), false);
		}
	}

	public static void sendUsernameChangedEmail(String appLocaleLangCode, String email, String newUsername, String oldUsername) {
		Locale appLocale = new Locale(appLocaleLangCode);
		String text = MessageFormat.format(ApplicationUtil.getMessage("page.text.username.has.been.changed", appLocale), newUsername, oldUsername);
		String subject = ApplicationUtil.getMessage("page.text.username.change.notification", appLocale);

		EmailManager emailManager = EmailUtil.getEmailManagerBean();
		emailManager.sendMail(email, text, subject, "");
	}

	public static void setUsernameValidationRules(Validator validator) {
		if (validator instanceof MessagepointInputValidator) {
			SecuritySettings securitySettings = SecuritySettings.findForCurrentDomain();
			if (securitySettings != null) {
				MessagepointInputValidationEntry usernameValidationEntry = ((MessagepointInputValidator)validator).getValidationEntry("username");
				int minLength = Integer.parseInt(securitySettings.getUsernameMinLength());
				int maxLength = Integer.parseInt(securitySettings.getUsernameMaxLength());
				usernameValidationEntry.setMinLength(minLength);
				usernameValidationEntry.setMaxLength(maxLength);
				if (securitySettings.isAlphanumericOnly()) {
					usernameValidationEntry.setRestrictedCharsRegex(StringUtil.injectI18Nchars("[A-Za-z0-9]++"));
					usernameValidationEntry.setRestrictedCharsList(ApplicationUtil.getMessage("page.text.validator.alphanum"));
				} else {
					usernameValidationEntry.setRestrictedCharsRegex(StringUtil.injectI18Nchars("^[A-Za-z0-9_\\-@.']*+"));
					usernameValidationEntry.setRestrictedCharsList(ApplicationUtil.getMessage("page.text.validator.alphanum.at.dot.dash.underscore.apos"));
				}
			}
		}
	}

	public static void setUsernameValidationRules(Validator validator, String schemaName) {
		if (validator instanceof MessagepointInputValidator) {
			SecuritySettings securitySettings = SecuritySettings.findForSchema(schemaName);
			if (securitySettings != null) {
				MessagepointInputValidationEntry usernameValidationEntry = ((MessagepointInputValidator)validator).getValidationEntry("username");
				int minLength = Integer.parseInt(securitySettings.getUsernameMinLength());
				int maxLength = Integer.parseInt(securitySettings.getUsernameMaxLength());
				usernameValidationEntry.setMinLength(minLength);
				usernameValidationEntry.setMaxLength(maxLength);
				if (securitySettings.isAlphanumericOnly()) {
					usernameValidationEntry.setRestrictedCharsRegex(StringUtil.injectI18Nchars("[A-Za-z0-9]++"));
					usernameValidationEntry.setRestrictedCharsList(ApplicationUtil.getMessage("page.text.validator.alphanum"));
				} else {
					usernameValidationEntry.setRestrictedCharsRegex(StringUtil.injectI18Nchars("^[A-Za-z0-9_\\-@.']*+"));
					usernameValidationEntry.setRestrictedCharsList(ApplicationUtil.getMessage("page.text.validator.alphanum.at.dot.dash.underscore.apos"));
				}
			}
		}
	}

	public static User signIn(String username, String password, String schema, HttpServletRequest request, HttpServletResponse response) {
		return signIn(username, password, schema, request, response, null);
	}

	public static User signIn(String username, String password, String schema, HttpServletRequest request, HttpServletResponse response, AtomicReference<String> errorReturn)
	{
		// String redirectUrl = null;
		User user = null;

		if (username != null)
		{
			if (password == null) {
				password = "";
			}

			username = username.trim();

			SessionHolder mainSessionHolder = null;
			try {

				Object details = null;
				HttpSession httpSession = request.getSession();
				SecurityContext context = (SecurityContext) httpSession.getAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY);
				if(context != null)
				{
					Authentication currentAuth = context.getAuthentication();
					if (currentAuth != null)
					{
						// get original details of sign in (we will keep them for new authentication)
						details = currentAuth.getDetails();
					}
				}

				// get authentication manager
				ProviderManager authenticationManager = ApplicationUtil.getBean("authenticationManager", ProviderManager.class);

				// create a new authentication token
				UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(username, password);

				// set details (IP address and session) the same as the current authentication token
				authRequest.setDetails(details);

				// switch DB session to requested schema
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);

				// try to authenticate
				Authentication authenticationResult = authenticationManager.authenticate( authRequest );

				if (authenticationResult != null && authenticationResult.isAuthenticated())
				{
					onAuthentication(authenticationResult, request, response);

					// the authentication was successful
					Object authorizedUser = authenticationResult.getPrincipal();
					if (authorizedUser instanceof User) {
						user = (User) authorizedUser;
					}

					// set new successful authentication 
					SecurityContextHolder.getContext().setAuthentication( authenticationResult );

					clearAuthenticationAttributes(request);

					HttpSession session = request.getSession(false);

					if (session != null)
					{
						session.setAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY, SecurityContextHolder.getContext());
						Enumeration<String> attributeNames = session.getAttributeNames();
						while (attributeNames.hasMoreElements()) {
							String attributeName = attributeNames.nextElement();
							if (attributeName.startsWith(BannerTemplateTag.SESSION_CACHE_ATTRIBUTE)) {
								session.removeAttribute(attributeName);
							}
						}
					}
					if(user != null && user.isAccountSoftDeactivated()){
						//need to get homeBranch and homeUser if there's enough license to activate this user again.
						Branch currentBranch = Branch.findById(Node.getCurrentBranch().getId());
						if(!UserUtil.canActivateThisUser(user, currentBranch, null).equals("true")){
							//need to pass some message here?
							return null;
						}else{
							// set softDeactivated false
							user.setAccountSoftDeactivated(false);
							BranchUtil.setSoftDeactivation(user, currentBranch, false);
						}
					}
					// Validate the password expiration
					if(user != null && user.checkIsPasswordExpired())
					{
						// redirectUrl = ApplicationUtil.addToken(MessagepointAuthenticationSuccessHandler.passwordChangeRedirect, (String) request.getSession(false).getAttribute(WebAppSecurity.CSRF_SESSION_KEY));
					}
				}

			}
			catch(AuthenticationException exception)
			{
				 String errorMsgKey = null;
				 String errorMessage = exception.getMessage();
				 if (exception instanceof BadCredentialsException) {
				 	 errorMsgKey = processInvalidSigninAttempt(username);
					 errorMessage += ": ";
					 errorMessage += ApplicationUtil.getMessage(errorMsgKey);
					 if (errorMsgKey != null)
					 {
						 if (errorMsgKey.equals(ProcessInvalidSigninService.FAIL_KEY_USER_FAIL_AUTH) || errorMsgKey.equals(ProcessInvalidSigninService.FAIL_KEY_USER_NOT_EXIST))
						 {
							 errorMsgKey = "code.text.authenticationfailed";
						 }
						 else if (errorMsgKey.equals(ProcessInvalidSigninService.FAIL_KEY_USER_LOCK))
						 {
							 errorMsgKey = "code.text.accountlocked";
						 }
					 }
				 }
				 if (exception instanceof LockedException) {
					 errorMsgKey = "code.text.accountlocked";
					 errorMessage += ";";
				 }
				 log.info("Authentication failed; " + errorMessage + " Username: " + username + " Schema: " + schema);

				 // Audit (Authentication failure)
				AuditEventUtil.push(AuditEventType.ID_SIGNIN, AuditObjectType.ID_USER, username, null, AuditActionType.ID_AUTHENTICATION_FAILURE, null);

				if (errorReturn != null) {
					errorReturn.set(errorMsgKey);
				}
				return null;
			}
			finally
			{
				// set DB schema back to the original one the finish this HTTP session 
				// if (mainSessionHolder != null)

				// Auditing
				if(user != null){
					user.setLastLogin(DateUtil.now());
					user.save();
					// Authentication success
					AuditEventUtil.push(AuditEventType.ID_SIGNIN, AuditObjectType.ID_USER, user.getUsername(), user.getId(), AuditActionType.ID_AUTHENTICATION_SUCCESS, null);
				}else{
					// Authentication failure
					AuditEventUtil.push(AuditEventType.ID_SIGNIN, AuditObjectType.ID_USER, username, null, AuditActionType.ID_AUTHENTICATION_FAILURE, null);
				}

				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}
		}

		return user;
	}

	public static void switchUserIdpType(User user, Branch branch, int action) {
		User homeDcsUser = user.getHomeDcsUser();
		long homeDcsUserId = homeDcsUser.getId();
		Branch homeDcsBranch = homeDcsUser.getDomainOfThisUser();

		ServiceExecutionContext context = UpdateUserService.createContextForSoftDeactivation(homeDcsUserId, homeDcsBranch.getDcsNode().getGuid(), action, homeDcsUser.getUsername());
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateUserService.SERVICE_NAME, UpdateUserService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (!serviceResponse.isSuccessful()) {
			StringBuilder sb = new StringBuilder();
			sb.append(UpdateUserService.SERVICE_NAME);
			sb.append(" service call to set accountSoftDeactivated true failed");
			log.error(sb.toString());
		}
	}

	/**
	 * @param attributes the attributes which were extracted from the original session by {@code extractAttributes}
	 * @param newSession the newly created session
	 */
	private static void transferAttributes(Map<String, Object> attributes, HttpSession newSession) {
		if (attributes != null) {
			for (Map.Entry<String, Object> entry : attributes.entrySet()) {
				newSession.setAttribute(entry.getKey(), entry.getValue());
			}
		}
	}

	public static boolean updateUserPasswordAndSendPasswordEmail(String email) {
		if (!EmailUtil.isValidEmailAddress(email)) {
			return false;
		}

		User user = User.findByEmail(email);
		if (user == null) {
			return false;
		}

		String newPassword = generateNewUserPassword();
		String encrytedPassword = hashAndSaltPassword(newPassword, user);
		user.setPassword(encrytedPassword);
		user.setPasswordLastUpdated(DateUtil.now());
		user.addPasswordHistory(encrytedPassword);
		HibernateUtil.getManager().saveObject(user, false);

		sendPasswordChangedEmail(user, newPassword);

		return true;
	}

	public static void updateUserContextAttributes(HashMap<String,String> attrMap) {

		try {

			if ( RedisUtil.getRedisContext().containsKey( "connected_reference_data_" + UserUtil.getPrincipalUserId() ) )
				RedisUtil.getRedisContext().delete("connected_reference_data_" + UserUtil.getPrincipalUserId());
			if ( RedisUtil.getRedisContext().containsKey( "connected_OTG_" + UserUtil.getPrincipalUserId() ) )
				RedisUtil.getRedisContext().delete("connected_OTG_" + UserUtil.getPrincipalUserId());

			User principal = UserUtil.getPrincipalUser();
			User user = User.findById(principal.getId());

			JSONObject attrJSON;
			if (user.getContextAttributes() != null)
				attrJSON = new JSONObject(user.getContextAttributes());
			else
				attrJSON = new JSONObject();

			for (Map.Entry<String, String> currentAttr : attrMap.entrySet()) {
				attrJSON.put(currentAttr.getKey(), currentAttr.getValue());
			}

			ServiceExecutionContext context = UpdateUserService.createContextForContextAttrUpdate(user.getId(), attrJSON.toString());
			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateUserService.SERVICE_NAME, UpdateUserService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(UpdateUserService.SERVICE_NAME);
				sb.append(" service call is not successful in UserUtil:updateUserContextAttributes");
				log.error(sb.toString());
			}

		} catch (Exception e) {
			log.error("Error - Unable to generate JSON when updating user context attributes: "+e.getMessage(),e);
		}

	}

	public static HashMap<String,String> updateContextAttrForDocumentToggle(Document document, HashMap<String,String> contextAttr) {

		Document touchpointContext = document;
		while ( touchpointContext != null && touchpointContext.getParent() != null )
			touchpointContext = (Document) touchpointContext.getParent();

		contextAttr.put(UserUtil.CONTEXT_KEY_COLLECTION_CONTEXT, "-1");
		contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_CONTEXT, String.valueOf(touchpointContext != null ? touchpointContext.getId() : -1));
		contextAttr.put(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS,"touchpoint_content_object_list.form");
		if ( touchpointContext != null && touchpointContext.isEnabledForVariation() && touchpointContext.getMasterTouchpointSelection() != null ) {
			TouchpointSelection firstVisible = TouchpointSelection.findFirstVisible(touchpointContext.getMasterTouchpointSelection(), UserUtil.getPrincipalUser());
			contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT, String.valueOf(firstVisible.getId()));
		} else {
			contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT, String.valueOf(-1));
		}

		return contextAttr;
	}

	public static HashMap<String,String> updateContextAttrForWidgets(List<HomepageWidget> widgetTypes, HashMap<String,String> contextAttr){

		JSONArray widgetArr = new JSONArray();
		for ( HomepageWidget widgetType: widgetTypes ) {
			widgetArr.put(widgetType.getId());
		}
		contextAttr.put(UserUtil.CONTEXT_KEY_WIDGETS_CONTEXT, widgetArr.toString());
		return contextAttr;
	}

	public static List<HomepageWidget> getEnabledWidgetTypesContext() {
		User principal = UserUtil.getPrincipalUser();
		List<HomepageWidget> widgetTypes = new ArrayList<>();
		if (principal != null){
			User user = User.findById(principal.getId());
			if (user != null && user.getContextAttributes() != null) {
				try {
					JSONObject attrJSON = new JSONObject(user.getContextAttributes());
					if (attrJSON.has(UserUtil.CONTEXT_KEY_WIDGETS_CONTEXT)){
						JSONArray widgetArr = new JSONArray(attrJSON.get(UserUtil.CONTEXT_KEY_WIDGETS_CONTEXT).toString());
						for(int i=0; i<widgetArr.length(); i++){
							int widgetType = widgetArr.getInt(i);
							widgetTypes.add(new HomepageWidget(widgetType));
						}
					}
				} catch (JSONException e) {
					log.error("UserUtil: Unable to read user context attributes: Applying null selection status: " + e.getMessage(), e);
				}
			}
		}
		return widgetTypes;
	}

	/*
	 * Create instance user based on passed home DCS user has pod/domain admin on Home DCS
	 */
	public static User CreateInstnaceUserForPodAdmin(User homeDCSUser, Node currentNode) {
		// check home domain user has pod/domain admin privileges, if so, create a new user in current DCS/node
		if (homeDCSUser == null || !homeDCSUser.isPodAdminUser() || !homeDCSUser.isDomainAdminUser()
				|| !Node.isSchemaAccessible(currentNode.getSchemaName())
				|| !Node.isSchemaAccessible(currentNode.getBranch().getDcsSchemaName())){
			return null;
		}

		User nodeUser = null;
		String dcsSchema = currentNode.getBranch().getDcsSchemaName();
		boolean homeDcsUserHidden = CloneHelper.queryInSchema(homeDCSUser.getSchemaOfThisUser(), ()->homeDCSUser.isHiddenSupervisor());
		int homeDcsUserMasterAdminStatus = CloneHelper.queryInSchema(homeDCSUser.getSchemaOfThisUser(), ()->homeDCSUser.getMasterAdminStatus());
		boolean homeDcsUserEmailNotify = CloneHelper.queryInSchema(homeDCSUser.getSchemaOfThisUser(), ()->homeDCSUser.isEmailNotifyRealTime());

		if(homeDCSUser.isPodAdminUser() || homeDCSUser.isDomainAdminUser()){
			String idpIdentifier = homeDCSUser.getIdPIdentifier();
			if(!homeDCSUser.isSSOUser())
				idpIdentifier = Node.findBySchema(homeDCSUser.getSchemaOfThisUser()).getGuid();
			String idpGuid = homeDCSUser.isSSOUser()?homeDCSUser.getIdPUserGuid():homeDCSUser.getGuid();

			SessionHolder mainSessionHolder = null;
			User dcsUser;
			try {
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(dcsSchema);
				dcsUser = User.findByIdPUserGuid(idpGuid, idpIdentifier);
				if (dcsUser == null) {
					Role superAdminRole = Role.findByName("Super Administrator");
					if (superAdminRole == null)
						superAdminRole = Role.findById(Role.DEFAULT_ACCESS_ROLE);
					Set<Role> roles = RolesUtil.createSetForRole(superAdminRole);
					Workgroup defaultWG = Workgroup.findById(Workgroup.DEFAULT_WORKGROUP);

					AccessControlVO dcsVO = new AccessControlVO(0L, currentNode.getBranch().getId(), currentNode, true,
							roles, defaultWG.getId(),
							homeDcsUserHidden, homeDcsUserMasterAdminStatus, homeDcsUserEmailNotify);

					dcsUser = UserUtil.createNodeUser(homeDCSUser, currentNode.getBranch().getDcsNode(), dcsVO, homeDcsUserMasterAdminStatus);
					if (currentNode.getSchemaName().equals(dcsSchema)) {
						nodeUser = dcsUser;
					}
				}
			} finally {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}

			if (!currentNode.getSchemaName().equals(dcsSchema)) {
				Role instanceSuperAdminRole = CloneHelper.queryInSchema(currentNode.getSchemaName(), ()->Role.findByName("Super Administrator"));
				if(instanceSuperAdminRole == null)
					instanceSuperAdminRole = CloneHelper.queryInSchema(currentNode.getSchemaName(), ()->Role.findById(Role.DEFAULT_ACCESS_ROLE));
				Set<Role> roles = RolesUtil.createSetForRole(instanceSuperAdminRole);
				Workgroup defaultWG = CloneHelper.queryInSchema(currentNode.getSchemaName(), ()->Workgroup.findById(Workgroup.DEFAULT_WORKGROUP));

				AccessControlVO nodeVO = new AccessControlVO(0L, currentNode.getBranch().getId(), currentNode, true,
						roles, (defaultWG==null?null:defaultWG.getId()),
						homeDcsUserHidden, homeDcsUserMasterAdminStatus, homeDcsUserEmailNotify);
				nodeUser = UserUtil.createNodeUser(dcsUser, currentNode, nodeVO, homeDcsUserMasterAdminStatus);
			}
		}

		return nodeUser;
	}

	public static User signIn(User user, String schema, HttpServletRequest request, HttpServletResponse response)
	{
		// String redirectUrl = null;

		if (user != null)
		{
			SessionHolder mainSessionHolder = null;
			try {

				Object details = null;
				HttpSession httpSession = request.getSession();
				SecurityContext context = (SecurityContext) httpSession.getAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY);
				if(context != null)
				{
					Authentication currentAuth = context.getAuthentication();
					if (currentAuth != null)
					{
						// get original details of sign in (we will keep them for new authentication)
						details = currentAuth.getDetails();
					}
				}

				// get authentication manager
				ProviderManager authenticationManager = ApplicationUtil.getBean("authenticationManager", ProviderManager.class);

				// create a new authentication token
				UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities());

				// set details (IP address and session) the same as the current authentication token
				authRequest.setDetails(details);

				// switch DB session to requested schema
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);

				// try to authenticate
				User finalUser = user;
				Authentication authenticationResult = new Authentication() {
					@Override
					public Collection<? extends GrantedAuthority> getAuthorities() {
						return finalUser.getAuthorities();
					}

					@Override
					public Object getCredentials() {
						return null;
					}

					@Override
					public Object getDetails() {
						return null;
					}

					@Override
					public Object getPrincipal() {
						return finalUser;
					}

					@Override
					public boolean isAuthenticated() {
						return true;
					}

					@Override
					public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {

					}

					@Override
					public String getName() {
						return finalUser.getUsername();
					}
				};

				if (authenticationResult.isAuthenticated())
				{
					onAuthentication(authenticationResult, request, response);

					// the authentication was successful
					Object authorizedUser = authenticationResult.getPrincipal();
					if (authorizedUser instanceof User) {
						user = (User) authorizedUser;
					}

					// set new successful authentication
					SecurityContextHolder.getContext().setAuthentication( authenticationResult );

					clearAuthenticationAttributes(request);

					HttpSession session = request.getSession(false);

					if (session != null)
					{
						session.setAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY, SecurityContextHolder.getContext());
						Enumeration<String> attributeNames = session.getAttributeNames();
						while (attributeNames.hasMoreElements()) {
							String attributeName = attributeNames.nextElement();
							if (attributeName.startsWith(BannerTemplateTag.SESSION_CACHE_ATTRIBUTE)) {
								session.removeAttribute(attributeName);
							}
						}
					}
					if(user.isAccountSoftDeactivated()){
						//need to get homeBranch and homeUser if there's enough license to activate this user again.
						Branch currentBranch = Branch.findById(Node.getCurrentBranch().getId());
						if(!UserUtil.canActivateThisUser(user, currentBranch, null).equals("true")){
							//need to pass some message here?
							return null;
						}else{
							// set softDeactivated false
							user.setAccountSoftDeactivated(false);
							BranchUtil.setSoftDeactivation(user, currentBranch, false);
						}
					}
					// Validate the password expiration
					if(user != null && user.checkIsPasswordExpired())
					{
						// redirectUrl = ApplicationUtil.addToken(MessagepointAuthenticationSuccessHandler.passwordChangeRedirect, (String) request.getSession(false).getAttribute(WebAppSecurity.CSRF_SESSION_KEY));
					}
				}

			}
			catch(AuthenticationException exception)
			{
				/**
				 String error = exception.getMessage();
				 if (exception instanceof BadCredentialsException) {
				 String errorMsgKey = processInvalidSigninAttempt(request);
				 error += ": ";
				 error += ApplicationUtil.getMessage(errorMsgKey);
				 }
				 if (exception instanceof LockedException) {
				 error += ";";
				 }
				 log.info("Authentication failed; " + error + " Username: " + username + " Schema: " + c.getSchemaName());
				 errors.reject(null, error);
				 return super.showForm(request, response, errors);
				 **/
				return null;
			}
			finally
			{
				// set DB schema back to the original one the finish this HTTP session
				// if (mainSessionHolder != null)

				// Auditing
				if(user != null){
					user.setLastLogin(DateUtil.now());
					user.save();
					// Authentication success
					AuditEventUtil.push(AuditEventType.ID_SIGNIN, AuditObjectType.ID_USER, user.getUsername(), user.getId(), AuditActionType.ID_AUTHENTICATION_SUCCESS, null);
				}else{
					// Authentication failure
					AuditEventUtil.push(AuditEventType.ID_SIGNIN, AuditObjectType.ID_USER, user.getUsername(), null, AuditActionType.ID_AUTHENTICATION_FAILURE, null);
				}

				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}
		}

		return user;
	}

	private static String processInvalidSigninAttempt(String username) {
		ServiceExecutionContext context = ProcessInvalidSigninService.createContext(username);

		Service invalidSigninService = MessagepointServiceFactory.getInstance().lookupService(ProcessInvalidSigninService.SERVICE_NAME, ProcessInvalidSigninService.class);
		invalidSigninService.execute(context);
		if (context.getResponse().isSuccessful()) {
			ProcessInvalidSigninServiceResponse response = (ProcessInvalidSigninServiceResponse) context.getResponse();
			return response.getFailMsgKey();
		} else {
			return null;
		}

	}

	public static boolean isPincCompanyAdmin(User currentUser) {
		return currentUser!= null && (currentUser.isPodAdminUser() || currentUser.getRoles().stream().anyMatch(x -> x.getName() != null && x.getName().equalsIgnoreCase("pinc-company-admin")));
	}

	public static boolean isVerifiedPodAdmin(User currentUser) {
		return currentUser != null && (currentUser.isPodAdminUser() && currentUser.isMessagepointUser() && currentUser.isEmailVerified());
	}
}
