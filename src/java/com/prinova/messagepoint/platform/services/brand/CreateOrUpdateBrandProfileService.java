package com.prinova.messagepoint.platform.services.brand;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.controller.brand.BrandProfileEditWrapper;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.MessagepointDeleteBrandBackgroundTask;
import com.prinova.messagepoint.platform.services.rationalizer.MessagepointRecomputeBrandBackgroundTask;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerDeleteBrandBackgroundTask;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerRecomputeBrandBackgroundTask;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.collections.CollectionUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.function.Function;

public class CreateOrUpdateBrandProfileService extends AbstractService {

    public static final String SERVICE_NAME = "brand.CreateOrUpdateBrandProfileService";
    public static final int MAX_RECOMPUTE_BRAND_TASKS_IN_PARALLEL = 10;

    private static final Log log = LogUtil.getLog(CreateOrUpdateBrandProfileService.class);

    private static final int ACTION_CREATE = 1;
    private static final int ACTION_UPDATE = 2;

    public void execute(ServiceExecutionContext context) {

        CreateOrUpdateBrandProfileServiceRequest request = (CreateOrUpdateBrandProfileServiceRequest) context.getRequest();

        try {
            validate(context);
            if (hasValidationError(context))
                return;

            BrandProfile brandProfile = null;

            switch (request.getAction()) {
                case ACTION_CREATE:
                    brandProfile = new BrandProfile();
                    brandProfile = setBrandProfileProperties(brandProfile, request.getWrapper());
                    brandProfile.setCreated(DateUtil.now());
                    brandProfile.setCreatedBy(request.getRequestor() != null ? request.getRequestor().getId() : null);

                    if (brandProfile.getPrimaryProfile()) {
                        MessagePointRunnableHandler messagePointRunnableHandler = new MessagePointRunnableHandler();
                        triggerRecomputeJobForPrimaryProfile(brandProfile, messagePointRunnableHandler);
                        messagePointRunnableHandler.execute();
                    }

                    break;
                case ACTION_UPDATE:
                    brandProfile = BrandProfile.findById(request.getBrandProfileId());
                    boolean existingProfileWasPrimary = brandProfile.getPrimaryProfile();

                    brandProfile = setBrandProfileProperties(brandProfile, request.getWrapper());

                    MessagePointRunnableHandler messagePointRunnableHandler = new MessagePointRunnableHandler();

                    List<RationalizerApplication> applicationsList = RationalizerApplication.findAllWithLinkedBrandProfile(brandProfile);
                    User requestor = UserUtil.getPrincipalUser();
                    if (CollectionUtils.isNotEmpty(applicationsList)) {
                        for (RationalizerApplication currentApplication : applicationsList) {
                            messagePointRunnableHandler.addRationalizerRecomputeBrandTask(currentApplication, requestor, brandProfile);
                        }
                    }

                    if (brandProfile.getPrimaryProfile()) {
                        triggerRecomputeJobForPrimaryProfile(brandProfile, messagePointRunnableHandler);
                    }

                    if (existingProfileWasPrimary && !brandProfile.getPrimaryProfile()) {
                        triggerDeleteJobForPrimaryProfile(messagePointRunnableHandler);
                    }

                    messagePointRunnableHandler.execute();


                    break;
                default:
                    break;
            }

            brandProfile.setUpdated(DateUtil.now());
            brandProfile.setUpdatedBy(request.getRequestor() != null ? request.getRequestor().getId() : null);

            BrandProfile persistedPrimaryProfile = BrandProfile.findPrimaryProfile();
            if ( persistedPrimaryProfile != null && persistedPrimaryProfile.getId() != brandProfile.getId() && brandProfile.getPrimaryProfile()) {
                persistedPrimaryProfile.setPrimaryProfile(false);
                persistedPrimaryProfile.save();
            }

            brandProfile.save();

            if (brandProfile != null) {
                context.getResponse().setResultValueBean(brandProfile);
            }

        } catch (Exception e) {
            log.error(" unexpected exception when invoking CreateOrUpdateBrandProfileService execute method", e);
            this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + e,
                    context.getLocale());
            throw new RuntimeException(e);
        }

    }

    private static class MessagePointRunnableHandler {
        private List<MessagePointRunnable> messagePointRunnables = Collections.synchronizedList(new LinkedList<>());
        private Semaphore semaphore = new Semaphore(MAX_RECOMPUTE_BRAND_TASKS_IN_PARALLEL, true);
        private Function<MessagePointRunnable, Void> messagePointRunnableCallBack = (messagePointRunnable) -> {
            semaphore.release();

            return null;
        };

        private MessagePointRunnableHandler() {
        }

        public void execute() {
            Executors.newFixedThreadPool(1).submit(() -> {
                try {
                    executeMessagePointRunnables();
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }

                return null;
            });
        }

        private void executeMessagePointRunnables() throws InterruptedException {
            do {
                semaphore.acquire();
                if (messagePointRunnables.isEmpty()) {
                    break;
                }

                MessagePointRunnable messagePointRunnable = messagePointRunnables.remove(0);
                MessagePointRunnableUtil.startThread(messagePointRunnable, Thread.MAX_PRIORITY);
            } while (true);
        }

        public void addRationalizerRecomputeBrandTask(
                RationalizerApplication application,
                User requestor,
                BrandProfile brandProfile
        ) {
            messagePointRunnables.add(new RationalizerRecomputeBrandBackgroundTask(
                    application,
                    requestor,
                    brandProfile,
                    messagePointRunnableCallBack,
                    MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier()
                    )
            );
        }

        public void addRationalizerDeleteBrandTask(
                RationalizerApplication application,
                User requestor
        ) {
            messagePointRunnables.add(new RationalizerDeleteBrandBackgroundTask(
                    application,
                    requestor,
                    messagePointRunnableCallBack,
                            MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier()
                    )
            );
        }

        public void addMessagepointRecomputeBrandTask(
                User requestor,
                BrandProfile brandProfile
        ) {
            messagePointRunnables.add(new MessagepointRecomputeBrandBackgroundTask(
                    requestor,
                    brandProfile,
                    messagePointRunnableCallBack,
                            MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier()
                    )
            );

        }

        public void addMessagepointDeleteBrandTask(
                User requestor
        ) {
            messagePointRunnables.add(new MessagepointDeleteBrandBackgroundTask(
                    requestor,
                    messagePointRunnableCallBack,
                            MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier()
                    )
            );

        }
    }

    private BrandProfile setBrandProfileProperties(BrandProfile brandProfile, BrandProfileEditWrapper wrapper) {

        brandProfile.setName( wrapper.getName() );
        brandProfile.setPrimaryProfile( wrapper.getPrimaryProfile() );

        brandProfile.setApplyRestrictedTerms( wrapper.getApplyRestrictedTerms() );
        JSONArray restrictedTermsArray = new JSONArray();
        if (!wrapper.getRestrictedTerms().isEmpty()) {

            for ( int i = 0; i < wrapper.getRestrictedTerms().size(); i++ ) {

                if ( wrapper.getRestrictedTermDeleted().get(i)  == 1 )
                    continue;

                JSONObject currentRestrictedTerm = new JSONObject();
                currentRestrictedTerm.put("enabled", wrapper.getRestrictedTermEnabled().get(i) == 1 );
                currentRestrictedTerm.put("target", wrapper.getRestrictedTerms().get(i) );
                currentRestrictedTerm.put("replacement", wrapper.getRestrictedTermReplacements().get(i) );
                restrictedTermsArray.put(currentRestrictedTerm);
            }
        }
        brandProfile.setRestrictedTermsDefinition( restrictedTermsArray.toString() );

        brandProfile.setApplyRestrictedContractions( wrapper.getApplyRestrictedContractions() );
        JSONArray restrictedContractionsArray = new JSONArray();
        if (!wrapper.getRestrictedContractions().isEmpty()) {

            for ( int i = 0; i < wrapper.getRestrictedContractions().size(); i++ ) {

                if ( wrapper.getRestrictedContractionDeleted().get(i) == 1 )
                    continue;

                JSONObject currentRestrictedContraction = new JSONObject();
                currentRestrictedContraction.put("enabled", wrapper.getRestrictedContractionEnabled().get(i) == 1 );
                currentRestrictedContraction.put("target", wrapper.getRestrictedContractions().get(i) );
                currentRestrictedContraction.put("replacement", wrapper.getRestrictedContractionReplacements().get(i) );
                restrictedContractionsArray.put(currentRestrictedContraction);
            }
        }
        brandProfile.setRestrictedContractionsDefinition( restrictedContractionsArray.toString() );

        brandProfile.setApplyPreferredContractions( wrapper.getApplyPreferredContractions() );
        JSONArray preferredContractionsArray = new JSONArray();
        if (!wrapper.getPreferredContractions().isEmpty()) {

            for ( int i = 0; i < wrapper.getPreferredContractions().size(); i++ ) {

                if ( wrapper.getPreferredContractionDeleted().get(i)  == 1 )
                    continue;

                JSONObject currentPreferredContraction = new JSONObject();
                currentPreferredContraction.put("enabled", wrapper.getPreferredContractionEnabled().get(i) == 1 );
                currentPreferredContraction.put("target", wrapper.getPreferredContractions().get(i) );
                currentPreferredContraction.put("replacement", wrapper.getPreferredContractionReplacements().get(i) );
                preferredContractionsArray.put(currentPreferredContraction);
            }
        }
        brandProfile.setPreferredContractionsDefinition( preferredContractionsArray.toString() );

        brandProfile.setApplyMaxSentenceLength( wrapper.getApplyMaxSentenceLength() );
        brandProfile.setMaxSentenceLength( wrapper.getMaxSentenceLength() );

        brandProfile.setApplySingleBulletListDetection( wrapper.getApplySingleBulletListDetection() );
        brandProfile.setApplySingleNumberListDetection( wrapper.getApplySingleNumberListDetection() );

        brandProfile.setApplyCompoundSpaceDetection( wrapper.getApplyCompoundSpaceDetection() );
        brandProfile.setApplyLeadCompoundSpaceDetection( wrapper.getApplyLeadCompoundSpaceDetection() );

        brandProfile.setEnforceLegalMarkSuperscripting( wrapper.getEnforceLegalMarkSuperscripting() );

        brandProfile.setApplyURLFormat( wrapper.getApplyURLFormat() );
        brandProfile.setUrlFormat( wrapper.getUrlFormat() );

        brandProfile.setApplyPhoneNumberFormat( wrapper.getApplyPhoneNumberFormat() );
        brandProfile.setPhoneNumberFormat( wrapper.getPhoneNumberFormat() );

        return brandProfile;
    }

    public void validate(ServiceExecutionContext context) {

    }

    public static ServiceExecutionContext createContextForUpdate(long brandProfileId,
                                                                 BrandProfileEditWrapper wrapper,
                                                                 User requestor) {

        ServiceExecutionContext context = createContext(ACTION_UPDATE,
                brandProfileId,
                wrapper,
                requestor);

        return context;
    }

    public static ServiceExecutionContext createContextForNew(BrandProfileEditWrapper wrapper,
                                                              User requestor) {

        ServiceExecutionContext context = createContext(ACTION_CREATE,
                0L,
                wrapper,
                requestor);

        return context;
    }

    public static ServiceExecutionContext createContext(int action,
                                                        long brandProfileId,
                                                        BrandProfileEditWrapper wrapper,
                                                        User requestor) {

        SimpleExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        CreateOrUpdateBrandProfileServiceRequest request = new CreateOrUpdateBrandProfileServiceRequest();
        context.setRequest(request);

        request.setBrandProfileId(brandProfileId);
        request.setWrapper(wrapper);
        request.setRequestor(requestor);
        request.setAction(action);

        SimpleServiceResponse response = new SimpleServiceResponse();
        response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(response);

        return context;
    }

    private void triggerRecomputeJobForPrimaryProfile(
            BrandProfile brandProfile,
            MessagePointRunnableHandler messagePointRunnableHandler
    ) {
        List<RationalizerApplication> applicationsList = RationalizerApplication.findAllWithoutBrandProfileDefined();
        User requestor = UserUtil.getPrincipalUser();
        if (CollectionUtils.isNotEmpty(applicationsList)) {
            for (RationalizerApplication currentApplication : applicationsList) {
                messagePointRunnableHandler.addRationalizerRecomputeBrandTask(currentApplication, requestor, brandProfile);
            }
        }

        messagePointRunnableHandler.addMessagepointRecomputeBrandTask(requestor, brandProfile);
    }

    private void triggerDeleteJobForPrimaryProfile(MessagePointRunnableHandler messagePointRunnableHandler) {
        List<RationalizerApplication> applicationsList = RationalizerApplication.findAllWithoutBrandProfileDefined();
        User requestor = UserUtil.getPrincipalUser();
        if (CollectionUtils.isNotEmpty(applicationsList)) {
            for (RationalizerApplication currentApplication : applicationsList) {
                messagePointRunnableHandler.addRationalizerDeleteBrandTask(currentApplication, requestor);
            }
        }

        messagePointRunnableHandler.addMessagepointDeleteBrandTask(requestor);
    }
}