package com.prinova.messagepoint.platform.services.imports;

import com.prinova.messagepoint.email.EmailTemplateUtils;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.attachment.Attachment;
import com.prinova.messagepoint.model.clickatell.ClickatellConfiguration;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.dialogue.DialogueConfiguration;
import com.prinova.messagepoint.model.eMessaging.EMessagingConfiguration;
import com.prinova.messagepoint.model.exacttarget.ExactTargetConfiguration;
import com.prinova.messagepoint.model.font.ListStyle;
import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.model.ftp.FtpConfiguration;
import com.prinova.messagepoint.model.gmc.GMCConfiguration;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.nativecomposition.NativeCompositionConfiguration;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.sefas.SefasConfiguration;
import com.prinova.messagepoint.model.sefas.MPHCSConfiguration;
import com.prinova.messagepoint.model.sendmail.SendmailConfiguration;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.task.TPImportTask;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.attachment.CreateOrUpdateAttachmentService;
import com.prinova.messagepoint.platform.services.imports.document.*;
import com.prinova.messagepoint.platform.services.insert.ReleaseForApprovalService;
import com.prinova.messagepoint.platform.services.tpadmin.CreateDocumentService;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateDocumentService;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

public class ImportDocumentService extends AbstractService
{
    final public static String SERVICE_NAME = "imports.ImportTouchpointService";
    private static final Log log = LogUtil.getLog(ImportDocumentService.class);
	public Map<String, Long> newSelectionsMap = new HashMap<>();
    TPImportTask tpImportTask;

    public static class ReferenceData {
        Map<Long, TextStyle> textStyles = new HashMap<>();
        Map<Long, ParagraphStyle> pararaphStyles = new HashMap<>();
        Map<Long, ListStyle> listStyles = new HashMap<>();
        Map<Long, DataGroup> dataGroups = new HashMap<>();
        
        TextStyle getStyle( Long id )
        {
            if ( textStyles.containsKey(id) )
                return textStyles.get(id);
            return null;
        }
        ParagraphStyle getParagraphStyle( Long id )
        {
            if ( pararaphStyles.containsKey(id) )
                return pararaphStyles.get(id);
            return null;
        }
        ListStyle getListStyle( Long id )
        {
            if ( listStyles.containsKey(id) )
                return listStyles.get(id);
            return null;
        }
    }
    
    private static class Block
    {
        public int topx = 0;
        public int topy = 0;
        public int width = 0;
        public int height = 0;
        
        public Block( int intopx, int intopy, int inwidth, int inheight )
        {
            final double reportMaxWidth = 8500.0;
            final double appMaxWidth = 100.0;
            final double reportMaxHeight = 11000.0;
            final double appMaxHeight = 100.0;

            final double wScale = (reportMaxWidth / appMaxWidth); //dots per inch;
            final double hScale = (reportMaxHeight / appMaxHeight);

            topx = (int)Math.round( intopx / wScale ) * 1000000;
            topy = (int)Math.round( intopy / hScale ) * 1000000;
            width = (int)Math.round( inwidth / wScale ) * 1000000;
            height = (int)Math.round( inheight / hScale ) * 1000000; 
        }
    }
    
    @Override
	@Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false)
    public void execute(ServiceExecutionContext context)
    {
        try 
        {
            validate(context);
            if (hasValidationError(context)) 
            {
                return;
            }

            ImportDocumentServiceRequest request = (ImportDocumentServiceRequest)(context.getRequest());

            File xmlFile = request.getXmlFile();

            User user = User.findById(request.getImportUserId());
            
            long dataSourceAssociationId = request.getDataSourceAssociationId();
            DataSourceAssociation dataSourceAssociation = null;            
            if(dataSourceAssociationId > 0)
            	dataSourceAssociation = HibernateUtil.getManager().getObject(DataSourceAssociation.class, dataSourceAssociationId);
            
            boolean importOnlyDataCollection = false;
            if (dataSourceAssociationId == -2L)
            {
            	importOnlyDataCollection = true;
            	dataSourceAssociationId = 0;
            }

            tpImportTask = request.getTPImportTask();
            
            SAXParserFactory factory = SAXParserFactory.newInstance();
			factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            SAXParser saxParser = factory.newSAXParser();

            ImportMessageExportHandler importMessageExportHandler = new ImportMessageExportHandler(dataSourceAssociationId, importOnlyDataCollection, request.getUpdateDataSourceAndVariables(), tpImportTask.getDataCollectionVariables());
            importMessageExportHandler.setTpImportTask(tpImportTask);
            saxParser.setProperty("http://xml.org/sax/properties/lexical-handler", importMessageExportHandler);
            saxParser.parse(xmlFile, importMessageExportHandler);

        	ExportImportHandler root = (ExportImportHandler) importMessageExportHandler.getRoot();
            if (dataSourceAssociationId == 0)
            {
            	if (root.getDataCollection() != null)
            	{
                    root.getDataCollection().importOrSetupDataCollection(root.getDataVariables());
                    dataSourceAssociation = root.getDataCollection().getImportedDataSourceAssociation();
                    for (DataSourceImportHandler ds : root.getDataCollection().getDataSources()) {
                        if(ds.getType().equals("ReferenceConnected") && ds.getRecordLayoutType().equals("json")) {
                            if(root.getDataCollection().connectedReferenceDataSourceTypeIsDifferent()){
                                tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
                                throw new Exception("In data base already exits a reference connected data source of delimited type. The imported connected reference data source is of type json. The connected reference data source should have the same type");
                            }
                        }
                    }
            		if (dataSourceAssociation != null)
            		{
            			importMessageExportHandler.setDataSourceAssociationId(dataSourceAssociation.getId());
//            			root.getDataCollection().importDataSourceReferences(root.getDataVariables());
            		}
            	}
            } else {
            	if (root.getDataCollection() != null)
            	{
            		root.getDataCollection().setImportedDataSourceAssociation(dataSourceAssociation);
            		root.getDataCollection().importOrSetupDataCollection(root.getDataVariables());
            	}
            }
            
        	if (dataSourceAssociation != null && dataSourceAssociationId == 0 && root.getDataCollection().getItHasBeenImported())
        	{
        		if (tpImportTask.getFiscalYearStart() > 0)
        			dataSourceAssociation.setFromImportFiscalYearStartMonth(tpImportTask.getFiscalYearStart());
        		
        		if (tpImportTask.getFromImportCRVAId() > 0)
        		{
        			UserVariableImportHandler uvih = root.getDataVariables().findVariableByRefId(tpImportTask.getFromImportCRVAId());
        			if (uvih != null && uvih.getDataElementVariable() != null)
        				dataSourceAssociation.setFromImportCRVAId(uvih.getDataElementVariable().getId());
        		}
        		
        		if (tpImportTask.getFromImportCRVBId() > 0)
        		{
        			UserVariableImportHandler uvih = root.getDataVariables().findVariableByRefId(tpImportTask.getFromImportCRVBId());
        			if (uvih != null && uvih.getDataElementVariable() != null)
        				dataSourceAssociation.setFromImportCRVBId(uvih.getDataElementVariable().getId());
        		}
        		
        		HibernateUtil.getManager().saveObject(dataSourceAssociation);
        	}
        	
            if (importOnlyDataCollection || tpImportTask.isImportOnlySharedObjects())
            {
            	return;
            }
            
            Document doc = ParseImport(importMessageExportHandler, dataSourceAssociation, user);
            doc.setEnabled(false);
            HibernateUtil.getManager().saveObject(doc);
            StatusPollingBackgroundTask task = request.getStatusPollingBackgroundTask();
            if(task != null) {
                task.setTargetObjectId(doc.getId());
                task.save();
            }
            HibernateUtil.getManager().getSession().flush();

            if (dataSourceAssociation != null) {
                if (dataSourceAssociationId > 0) {
                    if (dataSourceAssociation.getFromImportFiscalYearStartMonth() > 0 && doc.getFiscalYearStartMonth() <= 0) {
                        doc.setFiscalYearStartMonth(dataSourceAssociation.getFromImportFiscalYearStartMonth());
                    }
                    if (dataSourceAssociation.getFromImportCRVAId() > 0 && doc.getCustomerReportingVariableA() == null) {
                        doc.setCustomerReportingVariableA(DataElementVariable.findById(dataSourceAssociation.getFromImportCRVAId()));
                    }
                    if (dataSourceAssociation.getFromImportCRVBId() > 0 && doc.getCustomerReportingVariableB() == null) {
                        doc.setCustomerReportingVariableB(DataElementVariable.findById(dataSourceAssociation.getFromImportCRVBId()));
                    }
                }

                doc.setDataSourceAssociation(dataSourceAssociation);
                dataSourceAssociation.getDocuments().add(doc);

                TouchpointImportHandler tpHandler = root.getTouchpoint();
                if(tpHandler.getConnectedManagement() != null) {
                    ConnectedManagementImportHandler cmih = tpHandler.getConnectedManagement();
                    cmih.updateReferenceConnection(doc, root);
                }

                HibernateUtil.getManager().saveObject(dataSourceAssociation);
            }

            tpImportTask.setDoneZoneImport(true);
            tpImportTask.setDoneSectionImport(true);
            
            createTouchpointAndLanguageSelections(doc, importMessageExportHandler, user);

            tpImportTask.setDoneSelectionImport(true);

            createAttachments(doc, importMessageExportHandler, user);

            // Create TP Targeting
            //
        	TouchpointTargeting tpTargeting = doc.getTpTargeting();
        	if (tpTargeting == null)
        	{
        		tpTargeting = TouchpointTargeting.findByDocument(doc);
            	if (tpTargeting == null)
            	{
            		tpTargeting = new TouchpointTargeting();
            		tpTargeting.setDocument(doc);
            		tpTargeting.save(true);
            	}
        	}
            
            TouchpointImportHandler tpih = root.getTouchpoint();
            if (tpih != null && tpih.getTouchpointTargeting() != null)
            {

            	tpTargeting.setExcludedTargetGroups(tpih.getTouchpointTargeting().getExcludedTargetGroups(tpTargeting.getId()));
            	tpTargeting.setIncludedTargetGroups(tpih.getTouchpointTargeting().getIncludedTargetGroups(tpTargeting.getId()));
            	tpTargeting.setExtendedTargetGroups(tpih.getTouchpointTargeting().getExtendedTargetGroups(tpTargeting.getId()));
            	
            	tpTargeting.setExcludedTargetGroupRelationship(tpih.getTouchpointTargeting().getExcludedRelationship());
            	tpTargeting.setIncludedTargetGroupRelationship(tpih.getTouchpointTargeting().getIncludedRelationship());
            	tpTargeting.setExtendedTargetGroupRelationship(tpih.getTouchpointTargeting().getExtendedRelationship());
            	tpTargeting.save();
            }

            if (tpih != null && tpih.getReportingVariables() != null)
            {
            	tpih.getReportingVariables().setReportingVariables(doc);
            }
            
            doc.setMetatags(root.getTouchpoint().getMetatags());
            doc.save(false);

            // Targeting Touchpoint assignment
            
            if (root.getTargetData() != null)
            {
            	Set<TargetGroupImportHandler> targetGroups = root.getTargetData().getTargetGroups();
            	for (TargetGroupImportHandler tgih : targetGroups)
            	{
            		TargetGroup tg = tgih.getTargetGroup();
            		if (tg != null)
            		{
            			tg.getDocuments().add(doc);
            			tg.save(false);
            		}
            	}
            }
            
            // Lookup Table Visibility 
            //
            Set<LookupTableImportHandler> lookupTables = root.getLookupTables();
            if (lookupTables != null && !lookupTables.isEmpty())
            {
            	for (LookupTableImportHandler ltih : lookupTables)
            	{
            		LookupTableInstance lti = ltih.getLookupTableInstance();
            		if (lti != null && !lti.isFullyVisible())
            		{
            			lti.getDocuments().add(doc);
            			lti.save();
            		}
            	}
            }
            
            Set<UserVariableImportHandler> variables = root.getDataVariables().getVariables();
            if (variables != null && !variables.isEmpty())
            {
            	for(UserVariableImportHandler uvih : variables) {
            		DataElementVariable dataElementVariable = uvih.getDataElementVariable ();
            		if(dataElementVariable != null && dataElementVariable.isScriptVariable()) {
            			if(! dataElementVariable.getDocuments().stream().anyMatch(d->d.getId() == doc.getId())) {
            				dataElementVariable.getDocuments().add(doc);
            				dataElementVariable.save();
            			}
            		}
            		if(dataElementVariable != null && dataElementVariable.isExpressionVariable()) {
            		    String sqlExpression = DataElementVariable.calculateSQLExpression(dataElementVariable);
                        dataElementVariable.setSqlExpression(sqlExpression);
                        dataElementVariable.save();
                    }
            	}
            }

            tpImportTask.setDocId(doc.getId());
/*            
            HashMap<String,String> contextAttr = new HashMap<String,String>();
            contextAttr = UserUtil.updateContextAttrForDocumentToggle(doc, contextAttr);
            UserUtil.updateUserContextAttributes(contextAttr);
*/            
            context.getResponse().setResultValueBean(importMessageExportHandler);
        }
        catch ( Exception ex )
        {
            if (ex.getCause() != null)
            {
            	if (ex.getCause().getMessage() != null)
            	{
            		if (ex.getCause().getMessage().equalsIgnoreCase("#UserVariableImportHandler#"))
            		{
                        this.getResponse(context).addErrorMessage(
                        		"service.import.variable.exists",
                        		"service.import.variable.exists",
                        		new String[] {ex.getMessage()}, "", null);
                        this.getResponse(context).setReturnCode(SimpleServiceResponse.ERROR);
                        
                        return;
            		}
            	}
            }
            
            log.error(" unexpected exception when invoking ImportDocumentXMLService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale() );
            throw new RuntimeException(ex.getMessage());
        }
    }
      
    private void createTouchpointAndLanguageSelections(
            Document doc,
            ImportMessageExportHandler handler,
            User user)
    {
        ExportImportHandler root = (ExportImportHandler) handler.getRoot();
        TouchpointImportHandler tpih = root.getTouchpoint();

        SelectionDataImportHandler selectionDataValues = root.getSelectionData();
        
        ParameterGroup pg = null;
        if ( tpih.getSelectorRefid() != 0 )
        {
        	SelectorGroupImportHandler sgih = root.getSelectionData().findSelectorGroupByRefid(tpih.getSelectorRefid());
        	if (sgih != null)
        	{
        		pg = sgih.getParameterGroup();
        	}
        }

        ParameterGroup lpg = null;
        if ( tpih.getLanguageSelectorRefid() != 0 )
        {
        	SelectorGroupImportHandler lsgih = root.getSelectionData().findSelectorGroupByRefid(tpih.getLanguageSelectorRefid());
        	if (lsgih != null)
        	{
        		lpg = lsgih.getParameterGroup();
        	}
        }
        
        doc.setSelectionWorkflowTypeId(tpih.getSelectionWorkflowTypeId());
        doc.save();
        
        ServiceExecutionContext context = UpdateDocumentService.createContextForPropertiesUpdate(doc, pg, lpg, doc.isStripoEnabled(), user.getId());
        Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateDocumentService.SERVICE_NAME, UpdateDocumentService.class);
  
        if ( service != null )
        {
        	service.execute(context);
        
        	TPSelectionImportHandler defaultSelection = tpih.getSelection();
        	if ( defaultSelection != null && pg != null)
        	{
        		defaultSelection.createSelection(root, doc, user, selectionDataValues);
        	}

        	LanguageSelectionImportHandler languageDefaultSelection = tpih.getLanguageSelection();
        	if ( languageDefaultSelection != null && lpg != null)
        	{
        		languageDefaultSelection.createSelection(doc, user, selectionDataValues);
        	}
        }
        
    }

    private void createAttachments(
            Document doc,
            ImportMessageExportHandler handler,
            User user)
    {

        ExportImportHandler root = (ExportImportHandler) handler.getRoot();
        TouchpointImportHandler tpih = root.getTouchpoint();
        if (tpih != null && tpih.getAttachmentManagement() != null)
        {
        	// For now only 1 attachment is allowed / expected
        	Content attachmentName = new Content();
        	Content fileLocation = new Content();
        	ServiceExecutionContext context = CreateOrUpdateAttachmentService.createContextForNew(tpih.getAttachmentManagement().getName(), 
    																							doc,
    																							ContentImportHandler.resolveAndCorrectContent(tpih.getAttachmentManagement().getAttachmentName(),root, attachmentName),
    																							ContentImportHandler.resolveAndCorrectContent(tpih.getAttachmentManagement().getFileLocation(), root, fileLocation),
    																							tpih.getAttachmentManagement().getDeliveryType(),
    																							user);

    		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAttachmentService.SERVICE_NAME, CreateOrUpdateAttachmentService.class);
    		service.execute(context);
    		ServiceResponse serviceResponse = context.getResponse();
    		if (!serviceResponse.isSuccessful()) {
    				StringBuilder sb = new StringBuilder();
    				sb.append(ReleaseForApprovalService.SERVICE_NAME);
    				sb.append(" service call is not successful ");
    				sb.append(" in ").append(this.getClass().getName());
    				sb.append(" requestor = ").append(user.getUsername());
    				sb.append(" Attachment was not saved. ");
    				log.error(sb.toString());
    		}
    		else
    		{
    			Attachment attach = (Attachment)context.getResponse().getResultValueBean();
    			if (attach != null)
    			{
    				attach.setExcludedTargetGroups(tpih.getAttachmentManagement().getExcludedTargetGroups(attach.getId()));
    				attach.setIncludedTargetGroups(tpih.getAttachmentManagement().getIncludedTargetGroups(attach.getId()));
    				attach.setExtendedTargetGroups(tpih.getAttachmentManagement().getExtendedTargetGroups(attach.getId()));
    				
    				attach.setExcludedTargetGroupRelationship(tpih.getAttachmentManagement().getExcludedRelationship());
    				attach.setIncludedTargetGroupRelationship(tpih.getAttachmentManagement().getIncludedRelationship());
    				attach.setExtendedTargetGroupRelationship(tpih.getAttachmentManagement().getExtendedRelationship());
    				
    				if (attach.getRecipientAttachmentName() != null)
    				{
    					attach.getRecipientAttachmentName().setVariables(attachmentName.getVariables());
    				}
    				
    				if (attach.getRecipientAttachmentLocation() != null)
    				{
    					attach.getRecipientAttachmentLocation().setVariables(fileLocation.getVariables());
    				}
    				
    				if(tpih.getAttachmentManagement().getDna() != null) {
    				    attach.setDna(tpih.getAttachmentManagement().getDna());
    				}
    				
    				attach.save();
    			}
    		}
        }
	}
    
    private Document ParseImport(
    		ImportMessageExportHandler xmlDoc, 
            DataSourceAssociation dataSourceAssociation,
            User user) 
    {
        return ParseDefinition(xmlDoc, dataSourceAssociation, xmlDoc, user);
    }
    
    private Document ParseDefinition( 
            MessagepointSAXImportHandler xmlDoc,
            DataSourceAssociation dataSourceAssociation,
            ImportMessageExportHandler importMessageExportHandler,
            User user)
    {
        ExportImportHandler root = (ExportImportHandler)xmlDoc.getRoot();

        ReferenceData refData = ParseReferenceData( root, dataSourceAssociation );

        return ParseTouchpoint( root, refData, importMessageExportHandler, user );
    }

    private ReferenceData ParseReferenceData( ExportImportHandler root, DataSourceAssociation dataSourceAssociation )
    {
        ReferenceData refData = new ReferenceData();
        
        if (dataSourceAssociation != null && dataSourceAssociation.getPrimaryDataSource() != null)
        {
	        DataGroup custDataGroup = DataGroup.getFirstLevelDataGroup(dataSourceAssociation.getPrimaryDataSource());
	        refData.dataGroups.put(0L, custDataGroup );
        }
        
        ParseRefTextStyles( root, refData );
        ParseRefParagraphStyles( root, refData );
        ParseRefListStyles( root, refData );
        ParseRefDataGroups( root.getDataGroups(), refData, dataSourceAssociation );
        
        return refData;
    }

    private Document ParseTouchpoint(
    		ExportImportHandler root,
            ReferenceData refData,
            ImportMessageExportHandler importMessageExportHandler,
            User user)
    {
        TouchpointImportHandler tpHandler = root.getTouchpoint();
    	
        String name = tpHandler.getName();
        String guid = tpHandler.getGuid();
        String dna = tpHandler.getDna();

        long documentToUpdateId = tpHandler.getTpImportTask().getDocToUpdateId();
        boolean isUpdateDocument = documentToUpdateId > 0;
        Set<Long> documentIDsToUpdate = isUpdateDocument ? new HashSet<>(List.of(documentToUpdateId)) : null;
        Set<String> usedNames = ApplicationUtil.getAllNames(Document.class, documentIDsToUpdate);
        String newName = ApplicationUtil.getUnusedName(name, usedNames);

        if(! isUpdateDocument) {
            if (usedNames.contains(name)) {
                log.error("Touchpoint \"" + name + "\" already exists. Imported touchpoint was renamed as \"" + newName + "\"");
            }
        }

        String dictionaryIdent = tpHandler.getDictionaryIdentifier();
        boolean enabled = tpHandler.getEnabled();
        Long channelId = tpHandler.getChannelId();
        Long connectorId = tpHandler.getConnectorTypeId();
        Long qualOutput = tpHandler.getQualificationOutputType();
        Integer inputCharacterEncoding = tpHandler.getInputCharacterEncoding();
        Integer outputCharacterEncoding = tpHandler.getOutputCharacterEncoding();
        Boolean restrictedQuotes = tpHandler.getRestrictedQuotes();
        Boolean playMessagesOnEmptyVar = tpHandler.getPlayMessageOnEmptyVar();
        boolean bundleCombinedContent = tpHandler.isBundleCombinedContent(); 
        boolean usingImageNameInBundle = tpHandler.isUsingImageNameInBundle();
        String separatorForBundledImages = tpHandler.getSeparatorForBundledImages();
        Boolean supportsStyles = tpHandler.getSupportsStyles();
        boolean legacyDxfMode = tpHandler.getLegacyDxfMode();
        boolean mixedDxfTaggedText = tpHandler.getMixedDxfTaggedText();
        boolean runtimedxf = tpHandler.getRunTimeDxf();
        long defListStyleId = tpHandler.getDefaultListStyleId();
        long defParaStyleId = tpHandler.getDefaultParagraphStyleId();
        long defTextStyleId = tpHandler.getDefaultTextStyleId();
        Integer listStyleControlType = tpHandler.getListStyleControlType();
        Integer colorOutputFormatType = tpHandler.getColorOutputFormatType();
        Integer useDefaultImage = tpHandler.getUseDefaultImage();
        boolean connectedEnabled = tpHandler.isConnectedEnabled();
        boolean stripoEnabled = tpHandler.isStripoEnabled();
        boolean segmentationAnalysisEnabled = tpHandler.isSegmentationAnalysisEnabled();
        Long segmentationAnalysisDataResourceRefId = tpHandler.getSegmentationAnalysisDataResourceRefId();

        String compositionVersion       = tpHandler.getCompositionVersion();
        Integer	outputFileType			= tpHandler.getOutputFileType();
        Boolean startsOnOddPage			= tpHandler.getStartsOnOddPage();
        Boolean duplexOutput			= tpHandler.getDuplexOutput();
        Boolean templateControl         = tpHandler.isTemplateControl();
        Boolean accessibility           = tpHandler.getAccessibility();
        Integer linespacePosition       = tpHandler.getLinespacePosition();
        Boolean tablePaddingInPts       = tpHandler.getTablePaddingInPts();

        MessagepointLocale messagepointLocale = null;
        Set<LanguageImportHandler> languages = tpHandler.getLanguages();
        
        if (!languages.isEmpty())
        {
        	for(LanguageImportHandler language : languages)
        	{
        		if (language.isDefault())
        		{
        			messagepointLocale = MessagepointLocale.getLanguageLocaleByLocaleCode(language.getLocaleCode());
        			break;
        		}
        	}
        }
        
		if (messagepointLocale == null)
		{
			messagepointLocale = MessagepointLocale.getDefaultLanguageLocale("en");
		}

		Document targetDocument = null;
		if(isUpdateDocument) {
            targetDocument = Document.findById(documentToUpdateId);
        } else {
            Service service = MessagepointServiceFactory.getInstance().lookupService(CreateDocumentService.SERVICE_NAME, CreateDocumentService.class);
            ServiceExecutionContext context = CreateDocumentService.createContextForNew(connectorId, channelId, newName, guid, dna, messagepointLocale);
            service.execute(context);

            targetDocument = (Document)(context.getResponse().getResultValueBean());
            setCreateAndUpdate(targetDocument, user);
        }

        Document doc = targetDocument;

        tpHandler.setDocument(doc);
        doc.setProcessUsingCombinedContent(bundleCombinedContent);
        doc.setEnabled(enabled);
        doc.setDictionaryIdentifier(dictionaryIdent);

        if(root.getMetadataFormDefinitionsListImportHandler() != null) {
            if(tpHandler.getTouchpointMetadataDefId() > 0) {
        		MetadataFormDefinitionImportHandler defImportHandler = root.getMetadataFormDefinitionsListImportHandler().getChildren().stream().filter(h -> h.getId() == tpHandler.getTouchpointMetadataDefId()).findFirst().orElse(null);
        		if(defImportHandler != null) {
        			MetadataFormDefinition def = defImportHandler.getMetadataFormDefinition();
        			doc.setTouchpointMetadataFormDefinition(def);
        		}
        	}
            if(tpHandler.getVariantMetadataDefId() > 0) {
        		MetadataFormDefinitionImportHandler defImportHandler = root.getMetadataFormDefinitionsListImportHandler().getChildren().stream().filter(h -> h.getId() == tpHandler.getVariantMetadataDefId()).findFirst().orElse(null);
        		if(defImportHandler != null) {
        			MetadataFormDefinition def = defImportHandler.getMetadataFormDefinition();
        			doc.setVariantMetadataFormDefinition(def);
        		}
        	}
        }
		TouchpointMetadataImportHandler tpMetadata = tpHandler.getTpMetadata();
        if(tpMetadata != null) {
        	tpMetadata.createMetadataForm(root.getMetadataFormDefinitionsListImportHandler(), root.getDatabaseFiles());
    		MetadataForm metadataForm = tpMetadata.getMetadataForm();
        	if(metadataForm != null) {
        		doc.setMetadataForm(metadataForm);
        	}
        }
        
        if (root.getDataFiles() != null && !root.getDataFiles().isEmpty()) {
        	root.getDataFiles().forEach(dfih -> {
        		dfih.createDataFile(doc);
        	});
        }
        
        if (root.getDataResources() != null && !root.getDataResources().isEmpty()) {
        	root.getDataResources().forEach(drih -> {
        		drih.createDataResource(doc);
        	});
        }

        if(tpHandler.getConnectedManagement() != null) {
        	ConnectedManagementImportHandler cmih = tpHandler.getConnectedManagement();
        	cmih.createConnectedManagementData(doc, root);
        }
        
        if (tpHandler.getFiscalYearStart() > 0)
        {
        	doc.setFiscalYearStartMonth(tpHandler.getFiscalYearStart());
        }
        
        if (tpHandler.getCrvARefId() > 0)
        {
   			UserVariableImportHandler uvih = root.getDataVariables().findVariableByRefId(tpHandler.getCrvARefId());
    		if (uvih != null)
            	doc.setCustomerReportingVariableA(uvih.getDataElementVariable());
        }
        
        if (tpHandler.getCrvBRefId() > 0)
        {
   			UserVariableImportHandler uvih = root.getDataVariables().findVariableByRefId(tpHandler.getCrvBRefId());
    		if (uvih != null)
            	doc.setCustomerReportingVariableB(uvih.getDataElementVariable());
        }
        
        if ( defListStyleId != 0) 
        {
        	ListStyle ls = refData.listStyles.get(defListStyleId);
        	doc.setDefaultListStyle(ls);
        }
        
        if ( defParaStyleId != 0 )
        {
            ParagraphStyle ps = refData.pararaphStyles.get(defParaStyleId);
            doc.setDefaultParagraphStyle(ps);
        }
        
        if ( defTextStyleId != 0 )
        {
            TextStyle ts = refData.textStyles.get(defTextStyleId);
            doc.setDefaultTextStyle(ts);
        }

        if (tpHandler.getDataGroupRefId() > 0)
        {
           	doc.setDataGroup(refData.dataGroups.get(tpHandler.getDataGroupRefId()));
        }
        
        if (!languages.isEmpty())
        {
        	for(LanguageImportHandler language : languages)
        	{
        		if (!language.isDefault())
        		{
        			messagepointLocale = MessagepointLocale.getLanguageLocaleByLocaleCode(language.getLocaleCode());
        			if (messagepointLocale != null)
        			{
        			    long messagepointLocaleId = messagepointLocale.getId();
        			    if(! doc.getTouchpointLanguages().stream().anyMatch(tl->tl.getMessagepointLocale().getId() == messagepointLocaleId))
                        {
                            doc.addTouchpointLanguage(messagepointLocale);
                        }
        			}
        		}
        	}
        }
        else
        {
        	doc.addTouchpointLanguage(MessagepointLocale.getDefaultLanguageLocale("fr"));
        	doc.addTouchpointLanguage(MessagepointLocale.getDefaultLanguageLocale("es"));
        }
        
        doc.setConnectedEnabled(connectedEnabled);
        doc.setStripoEnabled(stripoEnabled);
        
        doc.setSegmentationAnalysisEnabled(segmentationAnalysisEnabled);
        
        if(segmentationAnalysisEnabled && segmentationAnalysisDataResourceRefId != null && segmentationAnalysisDataResourceRefId.longValue() > 0) {
        	DataResource dr = root.getDataResources().stream().filter(tdr->tdr.getId() == segmentationAnalysisDataResourceRefId.longValue()).map(DataResourceImportHandler::getDataResource).findFirst().orElse(null);
        	doc.setSegmentationAnalysisResource(dr);
        }

        createConnectorConfig( root, tpHandler, doc, qualOutput, user, supportsStyles,
                inputCharacterEncoding, outputCharacterEncoding, playMessagesOnEmptyVar, bundleCombinedContent,
                usingImageNameInBundle, separatorForBundledImages,
                legacyDxfMode, mixedDxfTaggedText, runtimedxf, restrictedQuotes,
                compositionVersion, outputFileType, startsOnOddPage, duplexOutput, templateControl, accessibility, linespacePosition, tablePaddingInPts, listStyleControlType, colorOutputFormatType, useDefaultImage);

//        if ( !channelId.equals(4L) && !channelId.equals(5L) )
        {
            ParseSections(doc, null, tpHandler, user);
        }
        
       	ParseZones(doc, null, null, tpHandler, refData, user);

       	ParseStyleCustomizations(doc, root, tpHandler, refData, user);
       	
     	ParseTemplateFileContent(doc, tpHandler);

     	Map<String, Document> dnaToDocumentMap = new HashMap<>();

        dnaToDocumentMap.put(doc.getDna(), doc);
        doc.getChannelAlternateDocuments().forEach(cd->dnaToDocumentMap.put(cd.getDna(), cd));

     	doc.getAlternateLayouts().forEach(d->{
     	    dnaToDocumentMap.put(d.getDna(), d);
     	    d.getChannelAlternateDocuments().forEach(cd->dnaToDocumentMap.put(cd.getDna(), cd));
     	});

        parseLayouts(root, refData, doc, dnaToDocumentMap, tpHandler, user);

        Map<Long, Zone> refIdToZone = root.getZones().stream().collect(Collectors.toMap(zih->zih.getId(), zih->zih.getZone()));

        for(ZoneImportHandler zih : root.getZones()) {
            Zone zone = zih.getZone();
            if(zih.getFlowIntoZoneRefId() != 0) {
                Zone flowIntoZone = refIdToZone.get(zih.getFlowIntoZoneRefId());
                if(flowIntoZone != null) {
                    zone.setFlowIntoZone(flowIntoZone);
                    zone.save();
                }
            }
        }

        return doc;
    }

    private void createConnectorConfig(
    		ExportImportHandler root,
    		TouchpointLayoutImportHandler tpHandler,
            Document doc,
            Long qualOutput,
            User user,
            Boolean supportsStyles,
            Integer inputCharacterEncoding,
            Integer outputCharacterEncoding,
            Boolean playMessagesOnEmptyVar,
            boolean bundleCombinedContent, 
            boolean usingImageNameInBundle,
			String separatorForBundledImages,
			boolean legacyDxfMode,
			boolean mixedDxfTaggedText,
            boolean runTimeDxf,
            Boolean restrictedQuotes,
            String compositionVersion,
            Integer	outputFileType,
            Boolean startsOnOddPage,
            Boolean duplexOutput,
            Boolean templateControl,
            Boolean accessibility,
            Integer linespacePosition,
            Boolean tablePaddingInPts,
            Integer listStyleControlType,
            Integer colorOutputFormatType,
            Integer useDefaultImage
        )
    {
        DataVariablesImportHandler dviHandler = root.getDataVariables();
        
        String custDataFile = tpHandler.getCustomerDataFile();
        ConnectorConfiguration cc = doc.getConnectorConfiguration();
        
        doc.setProcessUsingCombinedContent(bundleCombinedContent);
        
        doc.setConnectorName(tpHandler.getConnectorName());

        if (cc != null)
        {
            cc.setColorOutputFormatType(colorOutputFormatType);

	        if ( cc instanceof DialogueConfiguration )
	        {
	            DialogueConfiguration dc = (DialogueConfiguration)(cc);
	            
	            String pubFile = tpHandler.getPubFile();
	
	            dc.setSupportsStyles(supportsStyles);
	            dc.setLegacyDxfMode(legacyDxfMode);
	            dc.setMixedDxfTaggedText(mixedDxfTaggedText);
                dc.setRunTimeDxf(runTimeDxf);
	            dc.setPubFile(pubFile);
	            dc.setCustomerDriverInputFileName(custDataFile);
	            if (qualOutput != null)
	            	dc.setQualificationOutput(QualificationOutput.findById(qualOutput));
	        }
	        else if ( cc instanceof GMCConfiguration )
	        {
	            GMCConfiguration gc = (GMCConfiguration)(cc);
	
	            String workflowFile = tpHandler.getWfdFile();
	
	            gc.setControlStyles(supportsStyles);
	            gc.setWorkflowFile(workflowFile);
	            gc.setCustomerDriverInputFileName(custDataFile);
	            gc.setRestrictedQuotes(restrictedQuotes);
	        }
	        else if ( cc instanceof EMessagingConfiguration )
	        {
	            EMessagingConfiguration ec = (EMessagingConfiguration)(cc);
	            
	            ec.setCustomerDriverInputFileName(custDataFile);
	            if ( tpHandler.getCustomerEmailAddressVarRefId() > 0 )
	            {
	            	UserVariableImportHandler customerEmailAddressVariable = dviHandler.findVariableByRefId(tpHandler.getCustomerEmailAddressVarRefId());
	            	if ( customerEmailAddressVariable != null )
	            		ec.setCustomerEmailAddressVariable(customerEmailAddressVariable.getDataElementVariable());
	            }
	            if ( tpHandler.getCustomerPhoneNumberVarRefId() > 0 )
	            {
	            	UserVariableImportHandler customerPhoneNumberVariable = dviHandler.findVariableByRefId(tpHandler.getCustomerPhoneNumberVarRefId());
	            	if ( customerPhoneNumberVariable != null )
	            		ec.setCustomerPhoneNumberVariable(customerPhoneNumberVariable.getDataElementVariable());
	            }
	        }
	        else if ( cc instanceof SendmailConfiguration )
	        {
	        	SendmailConfiguration smc = (SendmailConfiguration)(cc);
	            
	            smc.setCustomerDriverInputFileName(custDataFile);
	            if ( tpHandler.getCustomerEmailAddressVarRefId() > 0 )
	            {
	            	UserVariableImportHandler customerEmailAddressVariable = dviHandler.findVariableByRefId(tpHandler.getCustomerEmailAddressVarRefId());
	            	if ( customerEmailAddressVariable != null )
	            		smc.setCustomerEmailAddressVariable(customerEmailAddressVariable.getDataElementVariable());
	            }
	            
	            smc.setOverrideSMTP(tpHandler.isOverrideSMTP());
	            if(tpHandler.isOverrideSMTP()) {
	            	smc.setSmtpHost(tpHandler.getSMTPHost());
	            	smc.setSmtpPort(tpHandler.getSMTPPort());
	            	smc.setSmtpSecurity(tpHandler.getSMTPSecurity());
	            	smc.setSmtpAccount(tpHandler.getSMTPAccount());
//	            	smc.setSmtpPassword(tpHandler.getSMTPPassword());
	            	smc.setSmtpCustomHeader(tpHandler.getSMTPCustomHeader());
	            }
	        }
	        else if ( cc instanceof ExactTargetConfiguration )
	        {
	        	ExactTargetConfiguration smc = (ExactTargetConfiguration)(cc);

	            smc.setCustomerDriverInputFileName(custDataFile);
	            if ( tpHandler.getCustomerEmailAddressVarRefId() > 0 )
	            {
	            	UserVariableImportHandler customerEmailAddressVariable = dviHandler.findVariableByRefId(tpHandler.getCustomerEmailAddressVarRefId());
	            	if ( customerEmailAddressVariable != null )
	            		smc.setCustomerEmailAddressVariable(customerEmailAddressVariable.getDataElementVariable());
	            }
	            if ( tpHandler.getCustomerKeyVarRefId() > 0 )
	            {
	            	UserVariableImportHandler customerKeyVariable = dviHandler.findVariableByRefId(tpHandler.getCustomerKeyVarRefId());
	            	if ( customerKeyVariable != null )
	            		smc.setCustomerKeyVariable(customerKeyVariable.getDataElementVariable());
	            }
	        }
	        else if ( cc instanceof ClickatellConfiguration )
	        {
	        	ClickatellConfiguration cac = (ClickatellConfiguration)(cc);
	            
	            cac.setCustomerDriverInputFileName(custDataFile);
	            if ( tpHandler.getCustomerPhoneNumberVarRefId() > 0 )
	            {
	            	UserVariableImportHandler customerPhoneNumberVariable = dviHandler.findVariableByRefId(tpHandler.getCustomerPhoneNumberVarRefId());
	            	if ( customerPhoneNumberVariable != null )
	            		cac.setCustomerPhoneNumberVariable(customerPhoneNumberVariable.getDataElementVariable());
	            }
	        }	 
	        else if ( cc instanceof FtpConfiguration )
	        {
	        	FtpConfiguration ftpc = (FtpConfiguration)(cc);
	            
	        	ftpc.setCustomerDriverInputFileName(custDataFile);
	        	ftpc.setIsEmbedded(!tpHandler.isLandingpage());
	        	ComplexValue complexValue = new ComplexValue();
	        	Content contentValue = new Content();
	        	complexValue.setEncodedValue(ContentImportHandler.resolveAndCorrectContent(tpHandler.getHtmlFileNameOutput(), root, contentValue));
	        	long time = System.currentTimeMillis();
	        	complexValue.setUpdated(new Date(time));
	        	complexValue.setUpdatedBy(user.getId());
	        	ftpc.setRecipientFileComplexValue(complexValue);
	        	long ftpServerId = tpHandler.getFTPServerId();
	        	ftpc.setServerId(ftpServerId);
	        	ftpc.setWebURL(tpHandler.getWebURL());
	        	ftpc.setRecipientFileLocation(tpHandler.getFTPUploadLocation());
	        	if (ftpServerId > 0 && ftpServerId < 4)
	        	{
	        		SystemPropertyManager spManager = SystemPropertyManager.getInstance();				
	        		String host = spManager.getSystemProperty("ftp" + ftpServerId + "." + "host");
	        		if (host == null || host.isEmpty())
	        		{
	        			spManager.addOrUpdateSystemProperty("ftp" + ftpServerId + ".host", tpHandler.getFTPHost(), false);
	        			spManager.addOrUpdateSystemProperty("ftp" + ftpServerId + ".user", tpHandler.getFTPUser(), false);
//	        			spManager.addOrUpdateSystemProperty("ftp" + ftpServerId + ".password", tpHandler.getFTPPassword(), false);
	        		}
	        	}
	        }
	        else if ( cc instanceof NativeCompositionConfiguration )
	        {
	        	NativeCompositionConfiguration ncc = (NativeCompositionConfiguration)(cc);
	        	ncc.setCustomerDriverInputFileName(custDataFile);
	        }
			else if ( cc instanceof SefasConfiguration )
			{
				SefasConfiguration sc = (SefasConfiguration)(cc);
				sc.setCustomerDriverInputFileName(custDataFile);
                if(compositionVersion != null) {
                    sc.setCompositionVersion(compositionVersion);
                }
                if(outputFileType != null) {
                    sc.setOutputFileType(outputFileType);
                }
                if(startsOnOddPage != null) {
                    sc.setStartsOnOddPage(startsOnOddPage);
                }
                if(duplexOutput != null) {
                    sc.setDuplexOutput(duplexOutput);
                }
                if(templateControl != null && templateControl.booleanValue())
                    sc.setTemplateControl(true);
                else
                    sc.setTemplateControl(false);
                if(accessibility != null && accessibility.booleanValue())
                    sc.setAccessibility(true);
                else
                    sc.setAccessibility(false);
                if(linespacePosition != null)
                    sc.setLinespacePosition(linespacePosition);
                if(tablePaddingInPts != null && tablePaddingInPts.booleanValue())
                    sc.setTablePaddingInPts(true);
                else
                    sc.setTablePaddingInPts(false);
			}
            else if ( cc instanceof MPHCSConfiguration )
            {
                MPHCSConfiguration sc = (MPHCSConfiguration)(cc);
                sc.setCustomerDriverInputFileName(custDataFile);
                if(compositionVersion != null) {
                    sc.setCompositionVersion(compositionVersion);
                }
                if(outputFileType != null) {
                    sc.setOutputFileType(outputFileType);
                }
                if(startsOnOddPage != null) {
                    sc.setStartsOnOddPage(startsOnOddPage);
                }
                if(duplexOutput != null) {
                    sc.setDuplexOutput(duplexOutput);
                }
                if(templateControl != null && templateControl.booleanValue())
                    sc.setTemplateControl(true);
                else
                    sc.setTemplateControl(false);
                if(accessibility != null && accessibility.booleanValue())
                    sc.setAccessibility(true);
                else
                    sc.setAccessibility(false);
                if(linespacePosition != null)
                    sc.setLinespacePosition(linespacePosition);
                if(tablePaddingInPts != null && tablePaddingInPts.booleanValue())
                    sc.setTablePaddingInPts(true);
                else
                    sc.setTablePaddingInPts(false);
            }
	        
	        cc.setInputCharacterEncoding(inputCharacterEncoding);
	        cc.setOutputCharacterEncoding(outputCharacterEncoding);
	        cc.setPlayMessageOnEmptyVar(playMessagesOnEmptyVar);
	        cc.setApplyFilenamesToBundledImages(usingImageNameInBundle);
	        cc.setFilenameSeparatorForBundledImages(separatorForBundledImages);

	        cc.setExecuteInCloudTest      (tpHandler.isExecuteInCloudTest());
	        cc.setExecuteInCloudPreview   (tpHandler.isExecuteInCloudPreview());
	        cc.setExecuteInCloudProof     (tpHandler.isExecuteInCloudProof());
	        cc.setExecuteInCloudSimulation(tpHandler.isExecuteInCloudSimulation());
	        
	        cc.setPreQualEngineScript(tpHandler.getPreQualEngineScript());
	        cc.setPostQualEngineScript(tpHandler.getPostQualEngineScript());
	        cc.setPostConnectorScript(tpHandler.getPostConnectorScript());
	        
        	cc.setOverrideRemote(tpHandler.isOverrideRemote());
	        if(tpHandler.isOverrideRemote()) {
	        	cc.setRemoteServerIP(tpHandler.getRemoteServerIP());
	        	cc.setRemoteServerPort(tpHandler.getRemoteServerPort());
	        	cc.setRemoteServerUser(tpHandler.getRemoteServerUser());
//	        	cc.setRemoteServerPassword(tpHandler.getRemoteServerPassword());
	        }

            if(tpHandler.isPlayEmptyAggFirstLastVar() != null){
                cc.setPlayEmptyAggFirstLastVar(tpHandler.isPlayEmptyAggFirstLastVar());
            }
            if(tpHandler.isRemoveZeroFromStyleConnector() != null){
                cc.setRemoveZeroFromStyleConnector(tpHandler.isRemoveZeroFromStyleConnector());
            }
            if(tpHandler.isCompTimeParentTagging() != null) {
                cc.setCompTimeParentTagging(tpHandler.isCompTimeParentTagging());
            }
            if(tpHandler.isDataGroupExpressionVarProc() != null) {
                cc.setDataGroupExpressionVarProc(tpHandler.isDataGroupExpressionVarProc());
            }
            if(tpHandler.isScriptVarAppliesUndefined() != null) {
                cc.setScriptVarAppliesUndefined(tpHandler.isScriptVarAppliesUndefined());
            }
            if(tpHandler.isCorrectParagraphTextStyles() != null ) {
                cc.setCorrectParagraphTextStyles(tpHandler.isCorrectParagraphTextStyles());
            }
            if(tpHandler.isFixInlineTargetingStyles() != null) {
                cc.setFixInlineTargetingStyles(tpHandler.isFixInlineTargetingStyles());
            }
            if(tpHandler.isPreserveDataWhitespace() != null) {
                cc.setPreserveDataWhitespace(tpHandler.isPreserveDataWhitespace());
            }
            if(tpHandler.isNbspComposedAsSpace() != null) {
                cc.setNbspComposedAsSpace(tpHandler.isNbspComposedAsSpace());
            }
            if(tpHandler.isNormalizeImageLibrary() != null) {
                cc.setNormalizeImageLibrary(tpHandler.isNormalizeImageLibrary());
            }
            if(tpHandler.isNormalizeEmbeddedContent() != null) {
                cc.setNormalizeEmbeddedContent(tpHandler.isNormalizeEmbeddedContent());
            }
            if(tpHandler.isGmcSpanToTTag() != null) {
                cc.setGmcSpanToTTag(tpHandler.isGmcSpanToTTag());
            }
            if(tpHandler.isBlueUnderlineLinks() != null) {
                cc.setBlueUnderlineLinks(tpHandler.isBlueUnderlineLinks());
            }

            cc.setEvalNotEqualOnMissingTag(tpHandler.isEvalNotEqualOnMissingTag());
            cc.setListStyleControlType(listStyleControlType);
            cc.setUseDefaultImage(useDefaultImage);

	        HibernateUtil.getManager().saveObject(cc);
        }
    }
    
    private void parseLayouts(ExportImportHandler root, ReferenceData refData, Document rootDoc, Map<String, Document> dnaToDocumentMap, TouchpointImportHandler tpHandler, User user) {
    	List<AlternateLayoutImportHandler> alternateLayouts = new ArrayList<>(tpHandler.getAlternateLayouts());
    	Collections.sort(alternateLayouts, (l1, l2) ->  (int) (l1.getId() - l2.getId()));
        TouchpointImportHandler rootDocHandler = tpHandler;
        if(rootDoc.isPrintTouchpoint()) {
            rootDocHandler = tpHandler;
        }
    	for(AlternateLayoutImportHandler alih : alternateLayouts) {
    		Document layout = createAlternateLayout(root, dnaToDocumentMap, refData, rootDocHandler, alih, user);
    		layout.setChannelParentObject(null);
    		layout.setParentObject(rootDoc);
    		layout.save();
    	}
    	
    	List<ChannelLayoutImportHandler> channelLayouts = new ArrayList<>(tpHandler.getChannelLayouts());
    	Collections.sort(channelLayouts, (l1, l2)-> (int) (l1.getId() - l2.getId()));
    	
    	for(ChannelLayoutImportHandler clih : channelLayouts) {
    		Document layout = createChannelLayout(root, dnaToDocumentMap, refData, null, rootDocHandler, null, clih, user);
    		layout.setChannelParentObject(rootDoc);
    		layout.setParentObject(null);
    		layout.save();
    	}
    	
    	for(ChannelLayoutImportHandler rootclih : channelLayouts) {
    		long rootrefid = rootclih.getId();
    		Document rootChannelLayout = rootclih.getDocument();
            TouchpointLayoutImportHandler rootChannelDocHandler = null;
            if(rootChannelLayout.isPrintTouchpoint()) {
                rootChannelDocHandler = rootclih;
            }
        	for(AlternateLayoutImportHandler alih : alternateLayouts) {
        		long alternateLayoutRefId = alih.getId();
        		for(ChannelLayoutImportHandler clih : alih.getChannelLayouts()) {
        			long parentRefId = clih.getParentRefId();
        			long channelParentRefId = clih.getChannelParentRefId();
        			
        			if(parentRefId == rootrefid && channelParentRefId == alternateLayoutRefId) {
        				Document alternateLayout = alih.getDocument();
        				String name = clih.getName();
                		Document channelLayout = createChannelLayout(root, dnaToDocumentMap, refData, rootChannelDocHandler, alih, name, clih, user);
                		channelLayout.setParentObject(rootChannelLayout);
                		channelLayout.setChannelParentObject(alternateLayout);
                		channelLayout.save();
        			}
        		}
        	}
    	}
    }
    
    private Document createAlternateLayout(ExportImportHandler root, Map<String, Document> dnaToDocumentMap, ReferenceData refData, TouchpointLayoutImportHandler rootDocHandler, AlternateLayoutImportHandler tpHandler, User user) {
    	String name = tpHandler.getName();
    	Document layout = createChannelLayout(root, dnaToDocumentMap, refData, rootDocHandler, null, name, tpHandler, user);
    	return layout;
    }
    
    private Document createChannelLayout(ExportImportHandler root, Map<String, Document> dnaToDocumentMap, ReferenceData refData, TouchpointLayoutImportHandler rootDocHandler, TouchpointLayoutImportHandler channelRootDocHandler, String name, TouchpointLayoutImportHandler tpHandler, User user) {
        Long channelId = tpHandler.getChannelId();
        Long connectorId = tpHandler.getConnectorTypeId();
        Long qualOutput = tpHandler.getQualificationOutputType();
        Integer inputCharacterEncoding = tpHandler.getInputCharacterEncoding();
        Integer outputCharacterEncoding = tpHandler.getOutputCharacterEncoding();
        Boolean restrictedQuotes = tpHandler.getRestrictedQuotes();
        Boolean playMessagesOnEmtpyVar = tpHandler.getPlayMessageOnEmptyVar();
        boolean bundleCombinedContent = tpHandler.isBundleCombinedContent();
        boolean usingImageNameInBundle = tpHandler.isUsingImageNameInBundle();
        String separatorForBundledImages = tpHandler.getSeparatorForBundledImages();
        Boolean supportsStyles = tpHandler.getSupportsStyles();
        boolean legacyDxfMode = tpHandler.getLegacyDxfMode();
        boolean mixedDxfTaggedText = tpHandler.getMixedDxfTaggedText();
        boolean runTimeDxf = tpHandler.getRunTimeDxf();
        long defListStyleId = tpHandler.getDefaultListStyleId();
        long defParaStyleId = tpHandler.getDefaultParagraphStyleId();
        long defTextStyleId = tpHandler.getDefaultTextStyleId();
        Integer listStyleControlType = tpHandler.getListStyleControlType();
        Integer colorOutputFormatType = tpHandler.getColorOutputFormatType();
        Integer useDefaultImage = tpHandler.getUseDefaultImage();

        String compositionVersion       = tpHandler.getCompositionVersion();
        Integer	outputFileType			= tpHandler.getOutputFileType();
        Boolean startsOnOddPage			= tpHandler.getStartsOnOddPage();
        Boolean duplexOutput			= tpHandler.getDuplexOutput();
        Boolean templateControl         = tpHandler.isTemplateControl();
        Boolean accessibility           = tpHandler.getAccessibility();
        Integer linespacePosition       = tpHandler.getLinespacePosition();
        Boolean tablePaddingInPts       = tpHandler.getTablePaddingInPts();

        MessagepointLocale messagepointLocale = null;
        Set<LanguageImportHandler> languages = tpHandler.getLanguages();
        String dna = tpHandler.getDna();
        
        if (!languages.isEmpty())
        {
        	for(LanguageImportHandler language : languages)
        	{
        		if (language.isDefault())
        		{
        			messagepointLocale = MessagepointLocale.getLanguageLocaleByLocaleCode(language.getLocaleCode());
        			break;
        		}
        	}
        }
        
		if (messagepointLocale == null)
		{
			messagepointLocale = MessagepointLocale.getDefaultLanguageLocale("en");
		}

		Document targetLayout = (dna == null || dna.isEmpty()) ? null : dnaToDocumentMap.get(dna);

		if(targetLayout == null) {
            Service service = MessagepointServiceFactory.getInstance().lookupService(CreateDocumentService.SERVICE_NAME, CreateDocumentService.class);
            ServiceExecutionContext context = CreateDocumentService.createContextForNew(connectorId, channelId, name, null, dna, messagepointLocale);
            service.execute(context);
            targetLayout = (Document)(context.getResponse().getResultValueBean());
            setCreateAndUpdate(targetLayout, user);
            targetLayout.setEnabled(true);
            if(dna != null && !dna.isEmpty()) {
                dnaToDocumentMap.put(dna, targetLayout);
            }
        }

		Document layout = targetLayout;
        tpHandler.setDocument(layout);
        
        if ( defListStyleId != 0 ) {
        	ListStyle ls = refData.listStyles.get(defListStyleId);
        	layout.setDefaultListStyle(ls);
        }
        
        if ( defParaStyleId != 0 )
        {
            ParagraphStyle ps = refData.pararaphStyles.get(defParaStyleId);
            layout.setDefaultParagraphStyle(ps);
        }
        
        if ( defTextStyleId != 0 )
        {
            TextStyle ts = refData.textStyles.get(defTextStyleId);
            layout.setDefaultTextStyle(ts);
        }
        if (tpHandler.getDataGroupRefId() > 0)
        {
        	layout.setDataGroup(refData.dataGroups.get(tpHandler.getDataGroupRefId()));
        }
        createConnectorConfig( root, tpHandler, layout, qualOutput, user, supportsStyles, inputCharacterEncoding,
                outputCharacterEncoding, playMessagesOnEmtpyVar, bundleCombinedContent, usingImageNameInBundle,
                separatorForBundledImages, legacyDxfMode, mixedDxfTaggedText, runTimeDxf, restrictedQuotes,
                compositionVersion, outputFileType, startsOnOddPage, duplexOutput, templateControl, accessibility, linespacePosition, tablePaddingInPts, listStyleControlType, colorOutputFormatType, useDefaultImage);
//      if ( !channelId.equals(4L) && !channelId.equals(5L) )
        {
        	ParseSections(layout, rootDocHandler == null ? null : rootDocHandler.getDocument(), tpHandler, user);
        }
      
     	ParseZones(layout, rootDocHandler, channelRootDocHandler, tpHandler, refData, user);

     	ParseStyleCustomizations(layout, root, tpHandler, refData, user);
     	
     	ParseTemplateFileContent(layout, tpHandler);
     	
     	return layout;
    }
    
    private void ParseTemplateFileContent(Document layout, TouchpointLayoutImportHandler tpHandler) {
     	
        if (tpHandler.getTemplateFileContent() != null) {
     	    byte [] templateFileContent = tpHandler.getTemplateFileContent();
     	    if(templateFileContent != null && templateFileContent.length > 0) {
     	       String filePath = EmailTemplateUtils.getTemplatePackagePath(layout.getId());
     	       File file = new File(filePath);
     	       File parentPathFile = file.getParentFile();
     	       parentPathFile.mkdirs();
     	       String templatesPath = EmailTemplateUtils.getFilerootTemplatesBasePath(layout.getId()) + EmailTemplateUtils.PATH_SEPARATOR + EmailTemplateUtils.TEMPLATES_DIR;
     	       File templatesPathFile = new File(templatesPath);
     	       templatesPathFile.mkdirs();
     	       FileOutputStream stream;
                try {
                    stream = new FileOutputStream(file);
                    try {
                        stream.write(templateFileContent);
                    } finally {
                        stream.close();
                    }
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
                
                EmailTemplateUtils.extractTemplatePackage(filePath, templatesPath);
     	    }
     	}
    }
    
    private void ParseSections(
            Document doc,
            Document rootDoc,
            TouchpointLayoutImportHandler tpHandler,
            User user )
    {
        for( SectionImportHandler secHandler : tpHandler.getSections() )
        {
            int width = secHandler.getWidth();
            int height = secHandler.getHeight();
            int layoutType = secHandler.getLayoutType();
            int sectionType = secHandler.getSectionType();
            int pageNumberingType = secHandler.getPageNumberingType();
            int marginTop = secHandler.getMarginTop();
            int marginRight = secHandler.getMarginRight();
            int marginBottom = secHandler.getMarginBottom();
            int marginLeft = secHandler.getMarginLeft();
            int headerHeight = secHandler.getHeaderHeight();
            int footerHeight = secHandler.getFooterHeight();
            int regionLeftWidth = secHandler.getRegionLeftWidth();
            int regionRightWidth = secHandler.getRegionRightWidth();
            Integer numberOrder = secHandler.getNumber(); 
            
            String name = secHandler.getName();
            String dna = secHandler.getDna();
            DocumentSection parentSection = null;
            if(rootDoc != null && numberOrder != null && numberOrder.intValue() >= 0) {
            	parentSection = rootDoc.getDocumentSection(numberOrder);
            }
            
            String filename = secHandler.getFilename();
            boolean pathOnly = secHandler.getPathOnly();
            String imageContent = secHandler.getImageContent();

            if(imageContent == null) imageContent = "";

            DocumentSection ds = doc.getDocumentSections()
                    .stream()
                    .filter(docds->dna != null && docds.getDna().equals(dna))
                    .findFirst()
                    .orElse(null);

            if(ds == null) {
                if ( numberOrder.equals(1) )
                {
                    ds = doc.getDocumentSection(1);
                }
                else
                {
                    ds = new DocumentSection();
                    setCreateAndUpdate(ds, user);
                    ds.setDocument(doc);
                    doc.getDocumentSections().add(ds);
                }
            }

            ds.setSectionOrder(numberOrder);
            ds.setName(name);
            if(dna != null) {
                ds.setDna(dna);
            }
            ds.setHeight(height);
            ds.setWidth(width);
            ds.setLayoutType(layoutType);
            ds.setSectionType(sectionType);
            ds.setPageNumberingType(pageNumberingType);
            ds.setMarginTop(marginTop);
            ds.setMarginRight(marginRight);
            ds.setMarginBottom(marginBottom);
            ds.setMarginLeft(marginLeft);
            ds.setHeaderHeight(headerHeight);
            ds.setFooterHeight(footerHeight);
            ds.setRegionLeftWidth(regionLeftWidth);
            ds.setRegionRightWidth(regionRightWidth);
            
            ds.setParentObject(parentSection);

        	ds.setOverrideName(secHandler.isOverrideName());
        	ds.setOverrideSectionOrder(secHandler.isOverrideSectionOrder());
        	ds.setOverrideImage(secHandler.isOverrideImage());
        	ds.setOverrideDimensions(secHandler.isOverrideDimensions());
        	ds.setOverrideMargin(secHandler.isOverrideMargin());
        	ds.setOverrideHeader(secHandler.isOverrideHeader());
        	ds.setOverrideFooter(secHandler.isOverrideFooter());
        	ds.setOverrideRegionLeft(secHandler.isOverrideRegionLeft());
        	ds.setOverrideRegionLeft(secHandler.isOverrideRegionRight());
        	ds.setOverrideGuides(secHandler.isOverrideGuides());

            ds.setStartsFrontFacing(secHandler.isStartsFrontFacing());

            if (filename != null)
            {
            	ds.setImageName(filename);
                
            	String imageDirBase = ApplicationUtil.getProperty( SystemPropertyKeys.Folder.KEY_ImagesDir );
                FileUtil.createDirectoryIfNeeded(imageDirBase);

                if (pathOnly)
            	{
            		ds.setImageLocation(imageDirBase + imageContent);
            	}
            	else
            	{
                    try
                    {
	                    File imageDir = new File( imageDirBase + File.separator + ds.getGuid());
	                    FileUtils.forceMkdir(imageDir);

	                    File imageFile = new File(imageDir + File.separator + filename );

	                    // perform base 64 conversion
	                    FileOutputStream fos = new FileOutputStream(imageFile);
	                    byte[] filecontent = Base64.decodeBase64(imageContent);
	
	                    fos.write(filecontent);
	                    fos.close();
	                    
	                    ds.setImageLocation(ds.getLocalPath());
                    }
                    catch( Exception ex )
                    {
                        LogUtil.getLog(this.getClass()).error("Caught exception: ", ex);
                    }            		
            	}
            }
            
            HibernateUtil.getManager().saveObject(ds);
            tpImportTask.incrementImportedSections();
        }
    }
    
    private void ParseZones(
            Document doc,
            TouchpointLayoutImportHandler rootDocHandler,
            TouchpointLayoutImportHandler channelRootDocHandler,
            TouchpointLayoutImportHandler tpHandler,
            ReferenceData refData,
            User user)
    {
        Workgroup allWg = null;        
        Workgroup userWg = null;  
        if ( user != null )
        {
        	allWg = Workgroup.getDefaultWorkgroupFromDB();
        	userWg = user.getWorkgroup();
        	if (allWg != null && userWg != null)
        	{
        		if (allWg.getId() == userWg.getId())
        		{
        			userWg = null;
        		}
        	}
        }
        else
        {
        	allWg = Workgroup.findById(1);
        }

        ExportImportHandler root = tpHandler.getParent();
        if(root == null) {
        	if(channelRootDocHandler != null) {
        		root = channelRootDocHandler.getParent();
        	}
        	if(root == null) {
        		if(rootDocHandler != null) {
            		root = rootDocHandler.getParent();
        		}
        	}
        }

        for( ZoneImportHandler zoneHandler : tpHandler.getZones() )
        {
            root.getZones().add(zoneHandler);
            
            Zone z = null;

            String friendlyName = zoneHandler.getFriendlyName();
            String name = zoneHandler.getName();
            String dna = zoneHandler.getDna();

            z = doc.getZones().stream().filter(docz->dna != null && docz.getDna().equals(dna)).findFirst().orElse(null);

            if(z == null) {
                if (doc.isSmsTouchpoint()) {
                    z = doc.getZones().iterator().next();
                } else {
                    z = new Zone();
                    z.associateDocument(doc);
                }
                setCreateAndUpdate(z, user);
            }

            int sectionNum = zoneHandler.getSectionNumber();
            int zoneType = zoneHandler.getZoneTypeId();
            boolean enabled = zoneHandler.getEnabled();
            int contentType = zoneHandler.getContentTypeId();
            long subcontentTypeId = zoneHandler.getSubcontentTypeId();
            long defListStyleId = zoneHandler.getDefaultListStyleId();
            long defParaStyleId = zoneHandler.getDefaultParagraphStyleId();
            long defTextStyleId = zoneHandler.getDefaultTextStyleId();
            long starterTextStyleId = zoneHandler.getStarterTextStyleId();
            long defDataGroupId = zoneHandler.getDataGroupId();
            boolean defMixedDataGroups = zoneHandler.getMixedDataGroups();

            int topx = zoneHandler.getTopx();
            int topy = zoneHandler.getTopy();
            int height = zoneHandler.getHeight();
            int width = zoneHandler.getWidth();
            Integer defaultCanvasWidth = zoneHandler.getDefaultCanvasWidth();
            
            int dbtopx = zoneHandler.getDBTopx();
            int dbtopy = zoneHandler.getDBTopy();
            int dbheight = zoneHandler.getDBHeight();
            int dbwidth = zoneHandler.getDBWidth();
            
            boolean flows = zoneHandler.getFlows();
            boolean grows = zoneHandler.getGrows();
            boolean repeats = zoneHandler.getRepeats();
            boolean minimumSize = zoneHandler.getMinimumSize();
            boolean enforceMinHeight = zoneHandler.isEnforceMinHeight();
            boolean splitTables = zoneHandler.isSplitTables();
            int rotationAngle = zoneHandler.getRotationAngle();
            
            String backgroundColor = zoneHandler.getBackgroundColor();
            boolean dxfOutput = zoneHandler.getDxfOutput();
			boolean htmlOutput = zoneHandler.getHtmlOutput();
            boolean supportsTables = zoneHandler.getSupportsTables();
            boolean supportsForms = zoneHandler.getSupportsForms();
            boolean supportsBarcodes = zoneHandler.getSupportsBarcodes();
            boolean exportToSingleMessage = zoneHandler.isExportToSingleMessage();
            boolean supportsCustomParagraphs = zoneHandler.isSupportsCustomParagraphs();
            boolean freeForm = zoneHandler.getFreeForm();
            boolean flowZone = zoneHandler.getFlowZone();
            boolean backer = zoneHandler.getBacker();

            int     verticalAlignment = zoneHandler.getVerticalAlignment();

            int 				verticallyRelativeTo        	= zoneHandler.getVerticallyRelativeTo();
            int 				relativeDistance        		= zoneHandler.getRelativeDistance();

            Integer 			minimumKeepTogether        		= zoneHandler.getMinimumKeepTogether();


            boolean altText = zoneHandler.getAltText();
            boolean extLink = zoneHandler.getExtLink();
            boolean extPath = zoneHandler.getExtPath();
            boolean qualifiedIsDelivered = zoneHandler.getQualifiedIsDelivered();
            
            Block b = new Block(topx, topy, width, height);
            
            DocumentSection ds = doc.getDocumentSection(sectionNum);
            
            z.setFriendlyName(friendlyName);
            z.setName(name);
            if(dna != null) {
                z.setDna(dna);
            }
            z.setSection(ds);
            z.setPage(sectionNum);
            z.setZoneTypeId(zoneType);
            z.setEnabled(enabled);
            z.setContentTypeId(contentType);
            z.setSubContentTypeId(subcontentTypeId);

            if (dbtopx < 0)
            	z.setTopX(b.topx);
            else
            	z.setTopX(dbtopx);

            if (dbtopy < 0)
            	z.setTopY(b.topy);
            else
            	z.setTopY(dbtopy);
            	
            if (dbheight < 0)
            	z.setHeight(b.height);
            else
            	z.setHeight(dbheight);
            	
            if (dbwidth < 0)
            	z.setWidth(b.width);
            else
            	z.setWidth(dbwidth);

            z.setDefaultCanvasWidth(defaultCanvasWidth);
            
            z.setCanFlow(flows);
            z.setCanGrow(grows);
            z.setRepeats(repeats);
            z.setMinimumSize(minimumSize);
            
            z.setRotationAngle(rotationAngle);
            
            if (backgroundColor != null && !backgroundColor.equalsIgnoreCase(""))
            {
            	z.setBackgroundColor(backgroundColor);
            }
            else
            {
            	if (doc.isPrintTouchpoint())
            		z.setBackgroundColor(Zone.ZONE_BACKGROUND_DEFAULT);
            	if (doc.isEmailTouchpoint() || doc.isWebTouchpoint())
            		z.setBackgroundColor(Zone.ZONE_BACKGROUND_TRANSPARENT);
            }
            
            z.setDxfOutput(dxfOutput);
			z.setHtmlOutput(htmlOutput);
            z.setSupportsTables(supportsTables);
            z.setSupportsForms(supportsForms);
            z.setSupportsBarcodes(supportsBarcodes);
            z.setSupportsCustomParagraphs(supportsCustomParagraphs);
            z.setExportToSingleMessage(exportToSingleMessage);
            z.setFreeform(freeForm);
            z.setFlowZone(flowZone);
            z.setBacker(backer);
            z.setVerticalAlignment(verticalAlignment);
            z.setVerticallyRelativeTo(verticallyRelativeTo);
            z.setRelativeDistance(relativeDistance);
            z.setMinimumKeepTogether(minimumKeepTogether);
            z.setApplyAltText(altText);
            z.setApplyImageExtLink(extLink);
            z.setApplyImageExtPath(extPath);
            
            z.setEnforceMinimumHeight(enforceMinHeight);
            z.setSplitTables(splitTables);
            
            z.getZoneAttributes().put("qualifiedEqualsDelivered", qualifiedIsDelivered?"true":"false");
            
            if ( defListStyleId != 0 ) {
            	ListStyle listStyle = refData.listStyles.get(defListStyleId);
            	if(listStyle != null) {
            		listStyle.getZones().add(z);
            		z.setDefaultListStyle(listStyle);
            	}
            }
            
            if ( defParaStyleId != 0 )
            {
                ParagraphStyle ps = refData.pararaphStyles.get(defParaStyleId);
                if (ps != null)
                {
                	ps.getZones().add(z);
                    z.setDefaultParagraphStyle(ps);
                }
            }
            if ( defTextStyleId != 0 )
            {
                TextStyle ts = refData.textStyles.get(defTextStyleId);
                if (ts != null)
                {
                	ts.getZones().add(z);
                	z.setDefaultTextStyle(ts);
                }
            }
            if ( starterTextStyleId != 0 ) {
                TextStyle ts = refData.textStyles.get(starterTextStyleId);
                if (ts != null)
                {
                    ts.getZones().add(z);
                    z.setStarterTextStyle(ts);
                }
            }
            if (defMixedDataGroups) {
                z.setMixedDataGroups(true);
                z.setDataGroup(null);
            }
            else {
                z.setMixedDataGroups(false);
                if ( defDataGroupId != 0 )
                {
                    DataGroup dg = refData.dataGroups.get(defDataGroupId);
                    if (dg != null)
                    {
                        z.setDataGroup(dg);
                    }
                    else
                    {
                        z.setDataGroup(null);
                    }
                }
                else
                {
                    z.setDataGroup(null);
                }
            }
            
            Set<TextStyle> zoneStyles = ParseTextStyles( zoneHandler, refData);
            Set<ParagraphStyle> zoneParaStyles = ParseParagraphStyles( zoneHandler, refData );
            Set<ListStyle> zoneListStyles = ParseListStyles( zoneHandler, refData );
            
            z.setStyles(zoneStyles);
            z.setParagraphStyles(zoneParaStyles);
            z.setListStyles(zoneListStyles);
            
            Zone parentZone = null;
            TouchpointLayoutImportHandler parentZoneDocHandler = rootDocHandler;
            if(channelRootDocHandler != null) {
                parentZoneDocHandler = channelRootDocHandler;
            }
            long parentZoneRefId = 0;
            ZoneImportHandler parentHandler = null;
            if(parentZoneDocHandler != null) {
            	parentZoneRefId = zoneHandler.getParentRefId();
            	if(parentZoneRefId > 0) {
            		parentHandler = parentZoneDocHandler.getZone(parentZoneRefId);
            		if(parentHandler == null) {
            		    
            		}
            		if(parentHandler != null) {
            			parentZone = parentHandler.getZone();
            		}
            	}
                z.setParentObject(parentZone);

                z.setOverrideName(zoneHandler.isOverrideName());
                z.setOverrideFriendlyName(zoneHandler.isOverrideFriendlyName());
                z.setOverrideEnabled(zoneHandler.isOverrideEnabled());
                z.setOverridePosition(zoneHandler.isOverridePosition());
                z.setOverrideDimensions(zoneHandler.isOverrideDimensions());
                z.setOverrideDocumentSection(zoneHandler.isOverrideDocumentSection());
                z.setOverrideDefaultTextStyle(zoneHandler.isOverrideDefaultTextStyle());
                z.setOverrideTextStyles(zoneHandler.isOverrideTextStyles());
                z.setOverrideDefaultParagraphStyle(zoneHandler.isOverrideDefaultParagraphStyle());
                z.setOverrideParagraphStyles(zoneHandler.isOverrideParagraphStyles());
                z.setOverrideDefaultListStyle(zoneHandler.isOverrideDefaultListStyle());
                z.setOverrideListStyles(zoneHandler.isOverrideListStyles());
                z.setOverrideBackgroundColor(zoneHandler.isOverrideBackgroundColor());
                z.setOverrideWorkgroups(zoneHandler.isOverrideWorkgroups());
                z.setOverrideSharedAssets(zoneHandler.isOverrideSharedAssets());
                z.setOverrideCommunicationTemplate(zoneHandler.isOverrideCommunicationTemplate());
            }
            
            if ( contentType == ContentType.MULTIPART )
            {
                ParseMultipartZone( z, zoneHandler, parentHandler, refData, user );
            }

            z.save();

            if ( allWg != null )
            {
//                z.getWorkgroups().add(allWg);
                allWg.getZones().add(z);
            }
            
            if ( userWg != null )
            {
//                z.getWorkgroups().add(userWg);
                userWg.getZones().add(z);
            }
            HibernateUtil.getManager().saveObject(z);
            zoneHandler.setZone(z);
            
            tpImportTask.incrementImportedZones();
        }
    }
    
    private void ParseMultipartZone( 
            Zone z,
            ZoneImportHandler zoneHandler,
            ZoneImportHandler parentZoneHandler,
            ReferenceData refData,
            User user )
    {
        Zone parentZone = (parentZoneHandler == null) ? null : parentZoneHandler.getZone();
        for( ZonePartImportHandler zpHandler : zoneHandler.getZoneParts() )
        {
            String name = zpHandler.getName();
            String dna = zpHandler.getDna();
            Integer seq = zpHandler.getSequence();
            long contentTypeId = zpHandler.getContentTypeId();
            ContentType contentType = ContentType.findById(contentTypeId);
            long subcontentTypeId = zpHandler.getSubcontentTypeId();
            SubContentType subcontentType = SubContentType.findById(subcontentTypeId);
            
            int topx = zpHandler.getTopx();
            int topy = zpHandler.getTopy();
            int height = zpHandler.getHeight();
            int width = zpHandler.getWidth();
            Block b = new Block(topx, topy, width, height);

            ZonePart zp = z.getParts().stream().filter(zzp->dna != null && zzp.getDna().equals(dna)).findFirst().orElse(null);

            if(zp == null) {
                zp = new ZonePart();
                z.getParts().add(zp);
                zp.setZone(z);
                setCreateAndUpdate(zp, user);
            }

            zpHandler.setZonePart(zp);
            
            zp.setName(name);
            if(dna != null) {
                zp.setDna(dna);
            }
            zp.setContentType(contentType);
            zp.setSubContentType(subcontentType);
            zp.setSequence(seq);

            if(parentZoneHandler != null && parentZone != null && parentZone.getParts() != null && ! parentZone.getParts().isEmpty()) {
                ZonePart parentZonePart = parentZone.getParts().stream().filter(pzp-> Objects.equals(pzp.getSequence(), seq)).findFirst().orElse(null);
                zp.setParentObject(parentZonePart);
            }
            
            int dbtopx = zpHandler.getDBTopx();
            int dbtopy = zpHandler.getDBTopy();
            int dbheight = zpHandler.getDBHeight();
            int dbwidth = zpHandler.getDBWidth();
            
            if (dbtopx < 0)
            	zp.setTopX( (int)Math.round( (double)b.topx * 100 / z.getWidth() ) * 1000000 );
            else
            	zp.setTopX(dbtopx);
            
            if (dbtopy < 0)
            	zp.setTopY( (int)Math.round( (double)b.topy * 100 / z.getHeight() ) * 1000000 );
            else
            	zp.setTopY(dbtopy);
            
            if (dbwidth < 0)
            	zp.setWidth( (int)Math.round( (double)b.width * 100 / z.getWidth() ) * 1000000 );
            else
            	zp.setWidth(dbwidth);
            
            if (dbheight < 0)
            	zp.setHeight( (int)Math.round( (double)b.height * 100 / z.getHeight() ) * 1000000 );
            else
            	zp.setHeight(dbheight);
        }
    }
    
    private Set<ParagraphStyle> ParseParagraphStyles( ZoneImportHandler handler, ReferenceData refData )
    {
        Set<ParagraphStyle> toReturn = new HashSet<>();
        
        if ( handler.getParagraphStyleIds() == null || handler.getParagraphStyleIds().isEmpty())
            return toReturn;
        
        for( Long id : handler.getParagraphStyleIds() )
        {
            ParagraphStyle ps = refData.getParagraphStyle(id);
            if ( ps != null )
                toReturn.add(ps);
        }
        
        return toReturn;
    }
    
    private Set<ListStyle> ParseListStyles( ZoneImportHandler handler, ReferenceData refData )
    {
        Set<ListStyle> toReturn = new HashSet<>();
        
        if ( handler.getListStyleIds() == null || handler.getListStyleIds().isEmpty())
            return toReturn;
        
        for( Long id : handler.getListStyleIds() )
        {
        	ListStyle ps = refData.getListStyle(id);
            if ( ps != null )
                toReturn.add(ps);
        }
        
        return toReturn;
    }
    
    private Set<TextStyle> ParseTextStyles( ZoneImportHandler handler, ReferenceData refData)
    {
        Set<TextStyle> toReturn = new HashSet<>();
        
        if ( handler.getTextStyleIds() == null || handler.getTextStyleIds().isEmpty())
            return toReturn;

        for( Long id : handler.getTextStyleIds() )
        {
            TextStyle s = refData.getStyle(id);
            if ( s != null )
                toReturn.add(s);
        }
        
        return toReturn;
    }
    
    // Parse the reference data section and create the text styles
    private void ParseRefTextStyles( ExportImportHandler root, ReferenceData refData )
    {
    	TextStyleListImportHandler refDataNode = root.getTextStyles();
        if ( refDataNode == null )
            return;
        
        for( TextStyleImportHandler handler : refDataNode.getStyles() )
        {
            Long id = handler.getId();
            TextStyle ts = handler.createTextStyle(root);
            if (ts != null)
            	refData.textStyles.put(id, ts);        	
        }
    }
    
    // Parse the reference data section and select out the paragraph styles that are from the app
    private void ParseRefParagraphStyles( ExportImportHandler root, ReferenceData refData )
    {
    	ParagraphStyleListImportHandler listHandler = root.getParastyles();
        if ( listHandler == null )
            return;
        
        for( ParagraphStyleImportHandler handler : listHandler.getParaStyles() )
        {
            Long id = handler.getId();
            ParagraphStyle ps = handler.createParagraphStyle(root);
            if (ps != null)
            	refData.pararaphStyles.put(id, ps);        	
        }
    }
    
    // Parse the reference data section and select out the list styles that are from the app
    private void ParseRefListStyles( ExportImportHandler root, ReferenceData refData )
    {
    	ListStyleListImportHandler listHandler = root.getListStyles();
        if ( listHandler == null )
            return;
        
        for( ListStyleImportHandler handler : listHandler.getListStyles() )
        {
            Long id = handler.getId();
            ListStyle listStyle = handler.createListStyle(root);
            if (listStyle != null)
            	refData.listStyles.put(id, listStyle);        	
        }
    }
    
    private void ParseStyleCustomizations(
            Document doc,
            ExportImportHandler root,
            TouchpointLayoutImportHandler tpHandler,
            ReferenceData refData,
            User user)
    {
        for( TextStyleImportHandler handler : tpHandler.getTextStyleCustomizations() )
        {
            Long refId = handler.getRefId();
    		if (refData.textStyles.containsKey(refId))
    		{
            	handler.createTextStyleCustomization(root, refData.textStyles.get(refId), doc);
    		}
        }
        
        for( ParagraphStyleImportHandler handler : tpHandler.getParagraphStyleCustomizations() )
        {
            Long refId = handler.getRefId();
            if (refData.pararaphStyles.containsKey(refId))
            {
        		long textStyleRefId = 0L;
        		String textstylerefid = handler.getTextStyleRefId();
        		if (textstylerefid != null && !textstylerefid.equalsIgnoreCase(""))
        		{
        			textStyleRefId = Long.parseLong(textstylerefid);
        		}
        		
        		TextStyle textStyle = null;
        		if (refData.textStyles.containsKey(textStyleRefId))
        		{
       				textStyle = refData.textStyles.get(textStyleRefId);
        		}        		
        		
            	handler.createParagraphStyleCustomization(refData.pararaphStyles.get(refId), doc, textStyle);
            }
        }
        
        for( ListStyleImportHandler handler : tpHandler.getListStyleCustomizations() )
        {
            Long refId = handler.getRefId();
            if (refData.listStyles.containsKey(refId))
            {
        		long textStyleRefId = 0L;
        		String textstylerefid = handler.getTextStyleRefId();
        		if (textstylerefid != null && !textstylerefid.equalsIgnoreCase(""))
        		{
        			textStyleRefId = Long.parseLong(textstylerefid);
        		}
        		
        		TextStyle textStyle = null;
        		if (refData.textStyles.containsKey(textStyleRefId))
        		{
       				textStyle = refData.textStyles.get(textStyleRefId);
        		}        		
        		
            	handler.createListStyleCustomization(root, refData.listStyles.get(refId), doc, textStyle);
            }
        }    	
    }
    
    // Parse the reference data section and select out the Data groups that are from the app
    private void ParseRefDataGroups( DataGroupListImportHandler listHandler, ReferenceData refData, DataSourceAssociation dataSourceAssociation )
    {
        // Should we even bother with this, since we don't use them.
        if ( listHandler == null )
            return;
        
        Set<DataGroup> appDataGroups = null;
        
        if (dataSourceAssociation != null && dataSourceAssociation.getPrimaryDataSource() != null)
        	appDataGroups = dataSourceAssociation.getPrimaryDataSource().getDataGroups();
        
        for( DataGroupImportHandler handler : listHandler.getDataGroups() )
        {
            Long id = handler.getId();
            String name = handler.getName();
            int level = handler.getLevelid();
            
            if (appDataGroups != null)
            {
	            for( DataGroup dg : appDataGroups )
	            {
	            	int dgLevel = dg.getLevel();
	                if ( dg.getName().equals(name) && dgLevel == level)
	                {
	                	refData.dataGroups.put(id, dg);
                        listHandler.mapId2DataGroup(id, dg);
	                	break;
	                }
	            }
            }
        }
    }

    
   private void updateZonesForCommunicationSettings(Document doc,
           TouchpointLayoutImportHandler tpHandler,
           ExportImportHandler root
   ) {
       for(ZoneImportHandler zoneHandler : tpHandler.getZones()) {  
           Zone z = zoneHandler.getZone();
           boolean communicationContentEditing = zoneHandler.getCommunicationContentEditing();
           long defaultCommunicationTemplateContentLibraryRefId = zoneHandler.getDefaultCommunicationTemplateContentLibraryRefId();
           long defaultCommunicationTemplateEmbeddedContentRefId = zoneHandler.getDefaultCommunicationTemplateEmbeddedContentRefId();
           z.setCommunicationContentEditing(communicationContentEditing);
           if(defaultCommunicationTemplateContentLibraryRefId > 0) {
               ContentLibraryImportHandler defaultCommunicationTemplateContentLibraryHandler = root.getContentLibrary().stream().filter(clh->clh.getId() == defaultCommunicationTemplateContentLibraryRefId).findFirst().orElse(null);
               if(defaultCommunicationTemplateContentLibraryHandler != null && defaultCommunicationTemplateContentLibraryHandler.getContentObject() != null) {
                   // TODO z.setDefaultCommunicationTemplateContentLibrary(defaultCommunicationTemplateContentLibraryHandler.getContentObject());
               }
           }
           if(defaultCommunicationTemplateEmbeddedContentRefId > 0) {
               EmbeddedContentImportHandler defaultCommunicationTemplateEmbeddedContentHandler = root.getEmbeddedContents().stream().filter(ech->ech.getId() == defaultCommunicationTemplateEmbeddedContentRefId).findFirst().orElse(null);
               if(defaultCommunicationTemplateEmbeddedContentHandler != null && defaultCommunicationTemplateEmbeddedContentHandler.getContentObject() != null) {
                   // TODO z.setDefaultCommunicationTemplateEmbeddedContent(defaultCommunicationTemplateEmbeddedContentHandler.getEmbeddedContentInstance().getModel());
               }
           }
           z.save();
       }
   }
   
    private static void setCreateAndUpdate( 
            UpdatableMessagePointModel umpm,
            User importUser  )
    {
        if ( importUser != null )
        {
            umpm.setCreatedBy(importUser.getId());
            umpm.setUpdatedBy(importUser.getId());
        }
        long time = System.currentTimeMillis();
        Date now = new Date(time);
        umpm.setCreated(now);
        umpm.setUpdated(now);
    }

    @Override
	public void validate(ServiceExecutionContext context)
    {
        ImportDocumentServiceRequest request = (ImportDocumentServiceRequest)(context.getRequest());
        ImportDocumentServiceResponse resp = (ImportDocumentServiceResponse)(context.getResponse());
        
        TPImportTask tpImportTask = request.getTPImportTask();
        
        long dataSourceAssociationId = request.getDataSourceAssociationId();
        if (dataSourceAssociationId == -2L)
        {
        	// import Data Collection Only
        	return;
        }
        
        /*
        try
        {
            File xmlFile = request.getXmlFile();

            SchemaFactory factory = SchemaFactory.newInstance("http://www.w3.org/2001/XMLSchema");
            InputStream xsdStream = ContentObjectContentUtil.class.getClassLoader().getResourceAsStream("com/prinova/messagepoint/model/util/tpi.xsd");
            Source xsdSource = new StreamSource(xsdStream);            
            Schema schema = factory.newSchema(xsdSource);
            Validator validator = schema.newValidator();
            Source source = new StreamSource(xmlFile);
            
            try 
            {
                validator.validate(source);
            }
            catch (SAXException ex) 
            {
                resp.addErrorMessage("service.xml.validation.error", "service.xml.validation.error", new String[] {ex.getMessage()}, "", null);
                return;
            }
        }
        catch( Exception ex )
        {
            resp.addErrorMessage("service.xml.validation.error", "service.xml.validation.error", new String[] {ex.getMessage()}, "", null);
            return;
        }
*/
        try
        {
            File xmlFile = request.getXmlFile();
            SAXParserFactory factory = SAXParserFactory.newInstance();
			factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            SAXParser saxParser = factory.newSAXParser();
            ImportTouchpointValidationHandler handler = new ImportTouchpointValidationHandler();

            saxParser.parse(xmlFile, handler);
            
            String name = handler.getTouchpointName();
            String guid = handler.getTouchpointGuid();
            
            if(! tpImportTask.isRenameTPIfDuplicated()) {
                List<Document> documents = HibernateUtil.getManager().getObjects(Document.class);

                for( Document doc : documents )
                {
                    if ( doc.getName().equals(name) )
                    {
                        resp.addErrorMessage(
                                "service.import.touchpoint.name.exists",
                                "service.import.touchpoint.name.exists",
                                new String[] {name}, "", null);
                        resp.setReturnCode(SimpleServiceResponse.ERROR);
                        break;
                    }
                }
            }
        }
        catch( Exception ex )
        {
            LogUtil.getLog(this.getClass()).error("Caught exception", ex);
            resp.setReturnCode(SimpleServiceResponse.ERROR);
        }
    }
    
    public static ServiceExecutionContext createContext(
            long dataSourceAssocationId,
            File xmlFile,
            long importUserId,
            boolean updateDataSourceAndVariables,
            TPImportTask tpImportTask,
            StatusPollingBackgroundTask statusPollingBackgroundTask)
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ImportDocumentServiceRequest request = new ImportDocumentServiceRequest(dataSourceAssocationId, importUserId, xmlFile, updateDataSourceAndVariables, tpImportTask, statusPollingBackgroundTask);

        context.setRequest(request);

        ImportDocumentServiceResponse serviceResp = new ImportDocumentServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
}
