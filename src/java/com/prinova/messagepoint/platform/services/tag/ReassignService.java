package com.prinova.messagepoint.platform.services.tag;

import java.util.List;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.exceptionhandler.FinderException;
import com.prinova.messagepoint.model.admin.ItemType;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.stateprovider.StateProviderVersionModel;
import com.prinova.messagepoint.model.tag.Tag;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class ReassignService extends AbstractService {
	public static final String SERVICE_NAME = "tag.ReassignService";
	private static final Log log = LogUtil.getLog(ReassignService.class);
	
	public void execute(ServiceExecutionContext context) {
		ReassignServiceRequest request = (ReassignServiceRequest) context.getRequest();
		try {
			List<StateProviderVersionModel> models = request.getModels();
			if (models == null || models.isEmpty()) {
				return;
			}
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			for (StateProviderVersionModel model : models) {
				model.setLockedFor(request.getAssignedToUser().getId());
				model.save();
			}
		} catch (Exception e) {
			log.error(" unexpected exception when invoking ReassignServiceRequest execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
		ReassignServiceRequest request = (ReassignServiceRequest) context.getRequest();
		try {
			for (StateProviderVersionModel model : request.getModels()) {
				if (model instanceof Tag){
					if (!request.getRequestor().isPermitted(Permission.ROLE_TAG_EDIT)) {
						this.getResponse(context).addErrorMessage(model.getName(),
								ApplicationErrorMessages.TAG_NOT_AUTHORIZED_TO_REASSIGN,
								SERVICE_NAME,
								context.getLocale());
					}					
				}
			}
		} catch (FinderException fe) {
			this.getResponse(context).addErrorMessage("",
					ApplicationErrorMessages.TAG_NOT_FOUND,
					SERVICE_NAME,
					context.getLocale());
		}		
	}
	
	public static ServiceExecutionContext createContextForTag(List<Tag> tags, 
															 User assignedToUser,
															 User requestor, 
															 String userNote) {
		
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		ReassignServiceRequest request = new ReassignServiceRequest();
		context.setRequest(request);
		request.setRequestor(requestor);
		request.setUserNote(userNote);
		request.setAssignedToUser(assignedToUser);
		request.setModelType(ItemType.ITEM_TYPE_TAG);
		for (Tag tag : tags) {
			request.getModelIds().add(tag.getId());
		}
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}
}
