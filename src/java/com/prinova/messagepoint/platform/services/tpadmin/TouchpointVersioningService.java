package com.prinova.messagepoint.platform.services.tpadmin;

import com.google.common.collect.Multimap;
import com.prinova.messagepoint.MessagepointException;
import com.prinova.messagepoint.email.EmailTemplateUtils;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.common.ContentSelectionStatusType;
import com.prinova.messagepoint.model.common.SyncObjectType;
import com.prinova.messagepoint.model.content.ContentObjectZonePriority;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.font.*;
import com.prinova.messagepoint.model.proof.ProofDefinition;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.model.workflow.*;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.*;
import com.prinova.migrate.util.FileUtil;
import org.apache.commons.logging.Log;
import org.hibernate.Session;
import org.springframework.orm.hibernate5.SessionHolder;

import javax.xml.ws.Holder;
import java.io.File;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class TouchpointVersioningService extends AbstractService {

    public static final String SERVICE_NAME = "tpadmin.TouchpointVersioningService";

    private static final Log log = LogUtil.getLog(TouchpointVersioningService.class);

    public static final int ACTION_CHECK_OUT_DOCUMENT = 1;
    public static final int ACTION_DUPLICATE_DOCUMENT = 2;
    public static final int ACTION_DELETE_DOCUMENT = 3;
    public static final int ACTION_SYNC_DOCUMENT = 4;

    public static ThreadLocal<Session> saveSessionInThread = new ThreadLocal<>();
    public static ThreadLocal<Session> sourceSessionInThread = new ThreadLocal<>();
    
    public void execute(ServiceExecutionContext context) {
        SessionHolder podMasterSessionHolder = null;
        SessionHolder mainSessionHolder = null;
        SessionHolder saveSessionHolder = null;
        boolean sessionSwitched = false;

        try {
            log.info("TouchpointVersioningService starts");
            validate(context);
            if (hasValidationError(context)) {
                return;
            }

            TouchpointVersioningServiceRequest request = (TouchpointVersioningServiceRequest) context.getRequest();

            boolean syncFromOther = request.isSyncUpdateDocument();
            
            String currentSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
            String requestSchema = request.getSourceSchema();
            
            String sourceSchema = syncFromOther ? requestSchema : currentSchema;
            String targetSchema = syncFromOther ? currentSchema : requestSchema;

            User requestor = request.getRequestor();
            
            if(syncFromOther) {
                if (sourceSchema != null && !MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(sourceSchema)) {
                    Session saveSession = HibernateUtil.getManager().getSession();
                    podMasterSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
                    Session podMasterSession = HibernateUtil.getManager().getSession();
                    mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
                    Session sourceSession = HibernateUtil.getManager().getSession();
                    CloneHelper.startCrossInstanceClone();
                    CloneHelper.addSession(null, podMasterSession);
                    CloneHelper.addSession(targetSchema, saveSession);
                    CloneHelper.addSession(sourceSchema, sourceSession);
                    sessionSwitched = true;
                    saveSessionInThread.set(saveSession);
                    sourceSessionInThread.set(sourceSession);
                    
                }
            }
            else {
                if (targetSchema != null && !MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(targetSchema)) {
                    Node targetNode = Node.findBySchema(targetSchema);
                    podMasterSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
                    Session podMasterSession = HibernateUtil.getManager().getSession();
                    mainSessionHolder = HibernateUtil.getManager().openTemporarySession(targetSchema);
                    Session saveSession = HibernateUtil.getManager().getSession();
                    saveSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
                    Session sourceSession = HibernateUtil.getManager().getSession();
                    CloneHelper.startCrossInstanceClone();
                    CloneHelper.addSession(null, podMasterSession);
                    CloneHelper.addSession(targetSchema, saveSession);
                    CloneHelper.addSession(sourceSchema, sourceSession);
                    sessionSwitched = true;
                    saveSessionInThread.set(saveSession);
                    sourceSessionInThread.set(sourceSession);
                    
                    
                    User targetUser = requestor.getNodeUser(targetNode);
                    if(targetUser != null) {
                        requestor = targetUser; 
                    }
                }
            }

            CloneHelper.setRequestor(requestor);

            if (request.getAction() == ACTION_CHECK_OUT_DOCUMENT || request.getAction() == ACTION_DUPLICATE_DOCUMENT) {
                Document clonedDocument = duplicateDocument(request);

                if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null && !clonedDocument.isAlternate())
                    associateClonedTouchpointToSharedObjects(request, clonedDocument);

                context.getResponse().setResultValueBean(clonedDocument.getId());

            } else if (request.getAction() == ACTION_DELETE_DOCUMENT) {

            } else if (request.getAction() == ACTION_SYNC_DOCUMENT) {
//            		String otherSchema = sourceSchema;
//            		String saveSchema = targetSchema;
            		
           		boolean syncInSameSchema = sourceSchema.equals(targetSchema);
           		Set<Long>messagepointLocales = request.getLanguagesForSync();
                Multimap<Long, Map<Long, Long>> syncObjectMap = request.getSyncObjectMap();
                Document projectDocument = CloneHelper.queryInSaveSession(()->{
                    Document document = Document.findById(request.getDocumentId());
                    return document;
                });
                Document otherDocument = Document.findById(request.getOtherDocumentId());
                
                boolean syncWithOrigin = false;
                if(syncInSameSchema) {
                	syncWithOrigin = ((projectDocument.getOriginObject() != null) && projectDocument.getOriginObject().getId() == otherDocument.getId());
                }
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                Document sourceDocument = projectDocument;
                Document targetDocument = otherDocument;
                if (syncWithOrigin) {
                    if (syncFromOther) {
                        projectDocument.setCheckoutTimestamp(timestamp);
                        sourceDocument = otherDocument;
                        targetDocument = projectDocument;
                    } else {
                        projectDocument.setCheckinTimestamp(timestamp);
                    }
                } else {
                    if (syncFromOther) {
                        sourceDocument = otherDocument;
                        targetDocument = projectDocument;
                    }
                }

                Set<Integer> documentSettingsToSync = request.getDocumentSettingsToSync();
                if(documentSettingsToSync != null) {
                    if(documentSettingsToSync.contains(DocumentSettingsModel.ID_SETTING_CONNECTED_INTERVIEW)) {
                        if(! request.isHideUntilNextChange()) {
                            targetDocument.copyConnectedInterviewData(sourceDocument);
                        }
                    } else if(documentSettingsToSync.contains(DocumentSettingsModel.ID_SETTING_CHANNEL_TEMPLATE)) {
                        if(! request.isHideUntilNextChange()) {
                            targetDocument.copyChannelLayoutTemplates(sourceDocument);
                        }
                    }
                }

                targetDocument.save();

                String sourceNodeGuid = Node.findBySchema(sourceSchema).getGuid();
                String targetNodeGuid = Node.findBySchema(targetSchema).getGuid();

                for(Integer documentSettingsType : documentSettingsToSync) {
                    SyncTouchpointUtil.updateDocumentSettingsModelSyncHistory(SyncHistory.SYNC_TYPE_SYNCHRONIZING, documentSettingsType, sourceDocument, targetDocument, requestor.getId(), sourceNodeGuid, targetNodeGuid, request.isHideUntilNextChange());
                }

                if(! syncInSameSchema) {
                	Document sourceDocumentFinal = sourceDocument;
                	List<DataElementVariable> sourceDataElementVariablesList = CloneHelper.queryInSchema(sourceSchema, ()->DataElementVariable.findAllForDocument(sourceDocumentFinal));
                	List<Long> clonedDataElementVariableIDs = new ArrayList<>();
                	CloneHelper.assign(sourceDataElementVariablesList, o->{
                		DataElementVariable clone = o.clone();
                		clone.save();
                		clonedDataElementVariableIDs.add(clone.getId());
                		return clone;
                	});
                	if(! clonedDataElementVariableIDs.isEmpty()) {
                        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
                            HibernateUtil.getManager().getSession().flush();
                        } else {
                            CloneHelper.getCloneSession().flush();
                        }
                	}
                }
                
                if (syncObjectMap != null && !syncObjectMap.isEmpty()) {
                    Set<String> processedZoneDnas = new HashSet<>();

                    List<DocumentSection> newDocumentSectionOriginList = new ArrayList<>();
                    List<Zone> newZoneOriginList = new ArrayList<>();
                    List<ZonePart> newZonePartOriginList = new ArrayList<>();
                    List<ParameterGroupTreeNode> newParameterGroupTreeNodeOriginList = new ArrayList<>();
                    List<TouchpointSelection> newTouchpointSelectionOriginList = new ArrayList<>();
                    
                    Map<Long, Map<String, DocumentSection>> dnaToOriginDocSectionMap = new HashMap<>();
                    Map<Long, Map<String, Zone>> dnaToOriginDocZoneMap = new HashMap<>();
                    Map<Long, Map<String, ZonePart>> dnaToOriginDocZonePartMap = new HashMap<>();

                    Document targetDocumentFinal = targetDocument;
                    CloneHelper.execInSchema(targetDocument.getObjectSchemaName(), ()->
                    {
                        Document originDocumentOfSyncTo = null;
                        if (targetDocumentFinal.getOriginObject() == null || targetDocumentFinal.getCheckoutTimestamp() == null) {
                            originDocumentOfSyncTo = null;
                        } else {
//                            originDocumentOfSyncTo = (Document) targetDocumentFinal.getOriginObject();
//                            putDocumentSectionZoneAndZonePartToMap(originDocumentOfSyncTo, dnaToOriginDocSectionMap, dnaToOriginDocZoneMap, dnaToOriginDocZonePartMap);
//                            for (Document alternateDocument : originDocumentOfSyncTo.getAlternateLayouts()) {
//                                putDocumentSectionZoneAndZonePartToMap(alternateDocument, dnaToOriginDocSectionMap, dnaToOriginDocZoneMap, dnaToOriginDocZonePartMap);
//                            }
//                            for (Document alternateDocument : originDocumentOfSyncTo.getChannelAlternateDocuments()) {
//                                putDocumentSectionZoneAndZonePartToMap(alternateDocument, dnaToOriginDocSectionMap, dnaToOriginDocZoneMap, dnaToOriginDocZonePartMap);
//                            }
                        }
                    });

                    // Document Settings
                    if (syncObjectMap.containsKey((long) SyncObjectType.ID_DOCUMENT_SETTING)) {
                        List<Map<Long, Long>> objectMaps = (List<Map<Long, Long>>) syncObjectMap.get((long) SyncObjectType.ID_DOCUMENT_SETTING);
                    }

                    // Document Section
                    //
                    if (syncObjectMap.containsKey((long) SyncObjectType.ID_SECTION)) {
                        List<Map<Long, Long>> objectMaps = (List<Map<Long, Long>>) syncObjectMap.get((long) SyncObjectType.ID_SECTION);
                        for (Map<Long, Long> objectMap : objectMaps) {
                            for (Long objectIdx10 : objectMap.keySet()) {
                                long objectId = objectIdx10 / 0x10;
                                long objectStatus = objectMap.get(objectIdx10);
                                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                                objectStatus = objectStatus & 0xFFFF;

                                if (syncFromOther != objectIsFromOther)
                                    continue;

/*                                
                                if ((syncFromOther && ((objectStatus & 0xF) != 0)) || (!syncFromOther && ((objectStatus & 0xF00) != 0)))
                                    continue;
*/
                                String sourceObjectSchema = objectIsFromOther ? sourceSchema : targetSchema;
                                String targetObjectSchema = targetDocumentFinal.getObjectSchemaName();
                                DocumentSection sourceDocumentSection = DocumentSection.findById(objectId); // We will not commit to other schema. 
                                if (sourceDocumentSection != null) {
                                    String sectionDna = sourceDocumentSection.getDna();
                                    DocumentSection targetDocumentSection = CloneHelper.queryInSaveSession(()->DocumentSection.findByDnaAndDocument(sectionDna, targetDocumentFinal));
                                    if(targetDocumentSection == null) {
                                        DocumentSection clonedDocumentSection = CloneHelper.clone(sourceDocumentSection, o->o.clone(timestamp, targetDocumentFinal, null));
                                        clonedDocumentSection.setDna(sourceDocumentSection.getDna());
                                        if (clonedDocumentSection != null) {
                                            if(! syncWithOrigin) {
                                            		if(syncInSameSchema) {
                                            			clonedDocumentSection.setOriginObject(sourceDocumentSection.getOriginObject());
                                            		}
                                            }
                                            CloneHelper.execInSaveSession(()->targetDocumentFinal.getDocumentSections().add(clonedDocumentSection));
                                        }
                                        targetDocumentSection = clonedDocumentSection;

                                    	DocumentSection targetDocumentSectionFinal = targetDocumentSection;
                                        for (Zone clonedZone : CloneHelper.queryInSaveSession(()->targetDocumentSectionFinal.getZones())) {
                                            processedZoneDnas.add(clonedZone.getDna());
                                            clonedZone.setDocument(targetDocument);
                                            clonedZone.setCheckoutTimestamp(timestamp);
                                            clonedZone.save();
                                            CloneHelper.execInSaveSession(()->targetDocumentFinal.getZones().add(clonedZone));
                                            for (ZonePart clonedZonePart : CloneHelper.queryInSaveSession(()->clonedZone.getParts())) {
                                                clonedZonePart.setCheckoutTimestamp(timestamp);
                                                clonedZonePart.save();
                                            }
                                        }

//                                        updateSectionOrigin(syncFromOther, syncWithOrigin, timestamp,
//                                                sourceDocument, targetDocument,
//                                                dnaToOriginDocSectionMap, dnaToOriginDocZoneMap, dnaToOriginDocZonePartMap,
//                                                sourceDocumentSection, targetDocumentSection, newDocumentSectionOriginList);
                                    } else {
                                    	targetDocumentSection.copyData(sourceDocumentSection, timestamp, targetDocumentFinal, null);
                                    	targetDocumentSection.save();
                                    }

                                    if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
                                        HibernateUtil.getManager().getSession().flush();
                                    } else {
                                        CloneHelper.getCloneSession().flush();
                                    }

                                    for (DocumentSection alternateDocumentSection : sourceDocumentSection.findAllAlternates()) {
                                    	Document alternateDocument = alternateDocumentSection.getDocument();
                                        Document clonedAlternateDocument = CloneHelper.queryInSaveSession(()->Document.findByDnaAndParentDocument(alternateDocument, targetDocumentFinal));

                                        if (clonedAlternateDocument == null)
                                            continue;

                                        boolean breakInheritance = alternateDocumentSection.isBreakInheritance();
                                        alternateDocumentSection.setBreakInheritance(true);
                                        String alternateDocumentSectionDna = alternateDocumentSection.getDna();
                                        DocumentSection clonedAlternateDocumentSection = CloneHelper.queryInSaveSession(()->DocumentSection.findByDnaAndDocument(alternateDocumentSectionDna, clonedAlternateDocument));

                                        if(clonedAlternateDocumentSection == null) {
	                                        if (syncWithOrigin) {
	                                            if (syncFromOther)
	                                                clonedAlternateDocumentSection = CloneHelper.clone(alternateDocumentSection, o->o.clone(timestamp, clonedAlternateDocument, targetDocumentFinal));
	                                            else
	                                                clonedAlternateDocumentSection = CloneHelper.clone(alternateDocumentSection, o->o.clone(timestamp, clonedAlternateDocument, null));
	                                        } else {
	                                            clonedAlternateDocumentSection = CloneHelper.clone(alternateDocumentSection, o->o.clone(timestamp, clonedAlternateDocument, targetDocumentFinal));
	                                            clonedAlternateDocumentSection.setOriginObject(alternateDocumentSection.getOriginObject());
	                                        }

                                        	Document clonedAlternateDocumentFinal = clonedAlternateDocument;
                                        	DocumentSection clonedAlternateDocumentSectionFinal = clonedAlternateDocumentSection;
                                            CloneHelper.execInSaveSession(()->{
	                                            clonedAlternateDocumentFinal.getDocumentSections().add(clonedAlternateDocumentSectionFinal);
	                                            for (Zone clonedZone : clonedAlternateDocumentSectionFinal.getZones()) {
	                                                processedZoneDnas.add(clonedZone.getDna());
	                                                clonedZone.setDocument(clonedAlternateDocumentFinal);
	                                                clonedZone.setCheckoutTimestamp(timestamp);
	                                                clonedZone.save();
	                                                clonedAlternateDocumentFinal.getZones().add(clonedZone);
	                                                for (ZonePart clonedZonePart : clonedZone.getParts()) {
	                                                    clonedZonePart.setCheckoutTimestamp(timestamp);
	                                                    clonedZonePart.save();
	                                                }
	                                            }
                                            });
                                            
//                                            updateSectionOrigin(syncFromOther, syncWithOrigin, timestamp,
//                                                    alternateDocumentSection.getDocument(), clonedAlternateDocument,
//                                                    dnaToOriginDocSectionMap,
//                                                    dnaToOriginDocZoneMap, dnaToOriginDocZonePartMap,
//                                                    alternateDocumentSection, clonedAlternateDocumentSection, newDocumentSectionOriginList);
                                        } else {
                                            clonedAlternateDocumentSection.copyData(alternateDocumentSection, timestamp, clonedAlternateDocument, targetDocumentFinal);
                                            clonedAlternateDocumentSection.save();
                                        }

                                        alternateDocumentSection.setBreakInheritance(breakInheritance);
                                    }

                                    if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
                                        HibernateUtil.getManager().getSession().flush();
                                    } else {
                                        CloneHelper.getCloneSession().flush();
                                    }
                                }
                            }
                        }
                    }

                    // Zone
                    //
                    if (syncObjectMap.containsKey((long) SyncObjectType.ID_ZONE)) {
                        List<Map<Long, Long>> objectMaps = (List<Map<Long, Long>>) syncObjectMap.get((long) SyncObjectType.ID_ZONE);
                        for (Map<Long, Long> objectMap : objectMaps) {
                            for (Long objectIdx10 : objectMap.keySet()) {
                                long objectId = objectIdx10 / 0x10;
                                long objectStatus = objectMap.get(objectIdx10);
                                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                                objectStatus = objectStatus & 0xFFFF;

                                if (syncFromOther != objectIsFromOther)
                                    continue;
/*
                                if ((syncFromOther && ((objectStatus & 0xF) != 0)) || (!syncFromOther && ((objectStatus & 0xF00) != 0)))
                                    continue;
*/
                                String sourceObjectSchema = objectIsFromOther ? sourceSchema : targetSchema;
                                String targetObjectSchema = targetDocumentFinal.getObjectSchemaName();
                                
                              Zone sourceZone = Zone.findById(objectId); // We will not commit to other schema.

                                if (sourceZone != null) {
                                    DocumentSection sourceDocumentSection = sourceZone.getSection();
                                    String documentSectionDna = sourceDocumentSection == null ? null : sourceDocumentSection.getDna();
                                    DocumentSection clonedDocumentSection = documentSectionDna == null ? null : CloneHelper.queryInSaveSession(()->DocumentSection.findByDnaAndDocument(documentSectionDna, targetDocumentFinal));
                                    DocumentSection clonedDocumentSectionFinal = clonedDocumentSection;
                                    String zoneDna = sourceZone.getDna();
                                    if (processedZoneDnas.contains(zoneDna)) {
                                        Zone targetZone = CloneHelper.queryInSaveSession(()->Zone.findByDnaAndDocument(zoneDna, targetDocumentFinal));
                                        if(targetZone != null) {
                                            targetZone.copyData(sourceZone, timestamp, clonedDocumentSectionFinal, targetDocumentFinal, null, false);
                                        }
                                        continue;
                                    }
                                    
                                    // We can clone the zone only if document section exists in the target
                                    
                                    if (true || clonedDocumentSection != null) {
                                        Zone targetZone = CloneHelper.queryInSaveSession(()->Zone.findByDnaAndDocument(zoneDna, targetDocumentFinal));
                                        if(targetZone == null) {
                                            Zone clonedZone = CloneHelper.clone(sourceZone, o->o.clone(timestamp, clonedDocumentSectionFinal, targetDocumentFinal, null));
                                            clonedZone.setDna(sourceZone.getDna());
                                            if (clonedZone != null) {
                                                processedZoneDnas.add(sourceZone.getDna());
                                                clonedZone.setDocument(targetDocument);
                                                clonedZone.setCheckoutTimestamp(timestamp);
                                                clonedZone.save();
                                                if(clonedDocumentSection != null) {
                                                    CloneHelper.execInSaveSession(()->clonedDocumentSection.getZones().add(clonedZone));
                                                    clonedDocumentSection.save();
                                                }
                                            }
                                            targetZone = clonedZone;
                                        } else {
                                            targetZone.copyData(sourceZone, timestamp, clonedDocumentSectionFinal, targetDocumentFinal, null, false);
                                        }

                                        if (targetZone != null) {
                                            processedZoneDnas.add(sourceZone.getDna());
                                            targetZone.setDocument(targetDocument);
                                            targetZone.setCheckoutTimestamp(timestamp);
                                            Zone targetZoneFinal = targetZone;
                                            Zone sourcePrimaryZone = sourceZone.getPrimaryZone();
                                            String primaryZoneDna = sourcePrimaryZone.getDna();
                                            Zone targetPrimaryLayoutZone = CloneHelper.queryInSaveSession(()->Zone.findByDnaAndDocument(primaryZoneDna, targetDocumentFinal));
                                            for(ZonePart sourceZonePart : sourceZone.getParts()) {
                                                ZonePart targetZonePart = CloneHelper.queryInSaveSession(()->ZonePart.findByDnaAndZone(sourceZonePart, targetZoneFinal));
                                                if(targetZonePart == null) {
                                                    targetZonePart = CloneHelper.clone(sourceZonePart, o->o.clone(targetZoneFinal, targetPrimaryLayoutZone));
                                                    targetZonePart.setDna(sourceZonePart.getDna());
                                                    ZonePart targetZonePartFinal = targetZonePart;
                                                    CloneHelper.execInSaveSession(()->targetZoneFinal.getParts().add(targetZonePartFinal));
                                                }
                                                if(targetZonePart != null) {
                                                    targetZonePart.setCheckoutTimestamp(timestamp);
                                                    targetZonePart.save();
                                                }
                                            }
                                            targetZone.save();
//                                            updateZoneOrigin(syncFromOther, syncWithOrigin, timestamp,
//                                                    sourceDocument, targetDocument,
//                                                    dnaToOriginDocSectionMap, dnaToOriginDocZoneMap, dnaToOriginDocZonePartMap,
//                                                    sourceZone, clonedDocumentSection, targetZone, newZoneOriginList, newZonePartOriginList);
                                        }

                                        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
                                            HibernateUtil.getManager().getSession().flush();
                                        } else {
                                            CloneHelper.getCloneSession().flush();
                                        }

                                        boolean flushSession = false;
                                        for (Zone alternateZone : sourceZone.findAllAlternates()) {
                                            Document alternateZoneDocument =	alternateZone.getDocument();
                                            Document clonedAlternateDocument = CloneHelper.queryInSaveSession(()->Document.findByDnaAndParentDocument(alternateZoneDocument, targetDocumentFinal));
                                            Zone alternatePrimaryZone = alternateZone.getPrimaryZone();
                                            String alternatePrimaryZoneDna = alternatePrimaryZone.getDna();
                                            Zone targetPrimaryZone = CloneHelper.queryInSaveSession(()->Zone.findByDnaAndDocument(alternatePrimaryZoneDna, targetDocumentFinal));

                                            boolean breakInheritance = alternateZone.isBreakInheritance();
                                            alternateZone.setBreakInheritance(true);
                                            DocumentSection alternateDocumentSection = alternateZone.getSection();
                                            String alternateDocumentSectionDna = alternateDocumentSection == null ? null : alternateDocumentSection.getDna();
                                            DocumentSection clonedAlternateDocumentSection = alternateDocumentSection == null ? null : CloneHelper.queryInSaveSession(()->DocumentSection.findByDnaAndDocument(alternateDocumentSectionDna, clonedAlternateDocument));
                                            if (true /*clonedDocumentSection != null*/) {
                                                String alternateZoneDna = alternateZone.getDna();
                                                Zone targetAlternateZone = CloneHelper.queryInSaveSession(()->Zone.findByDnaAndDocument(alternateZoneDna, clonedAlternateDocument));
                                                if(targetAlternateZone == null) {
                                                    if (syncWithOrigin) {
                                                        if (syncFromOther)
                                                            targetAlternateZone = CloneHelper.clone(alternateZone, o->o.clone(timestamp, clonedAlternateDocumentSection, clonedAlternateDocument, targetDocumentFinal));
                                                        else
                                                            targetAlternateZone = CloneHelper.clone(alternateZone, o->o.clone(timestamp, clonedAlternateDocumentSection, clonedAlternateDocument, null));
                                                    } else {
                                                        targetAlternateZone = CloneHelper.clone(alternateZone, o->o.clone(timestamp, clonedAlternateDocumentSection, clonedAlternateDocument, targetDocumentFinal));
                                                    }
                                                } else {
                                                    targetAlternateZone.copyData(alternateZone, timestamp, clonedAlternateDocumentSection, clonedAlternateDocument, targetPrimaryZone, false);
                                                }
                                                
                                                if (targetAlternateZone != null) {
                                                    processedZoneDnas.add(alternateZone.getDna());

                                                    targetAlternateZone.setDocument(clonedAlternateDocument);
                                                    targetAlternateZone.setCheckoutTimestamp(timestamp);
                                                    for (ZonePart sourceAlternateZonePart : alternateZone.getParts()) {
                                                        String alternateZonePartDna = sourceAlternateZonePart.getDna();
                                                        ZonePart targetAlternateZonePart = CloneHelper.queryInSaveSession(()->ZonePart.findByDnaAndDocument(alternateZonePartDna, clonedAlternateDocument));
                                                        if(targetAlternateZonePart == null) {
                                                        	Zone targetAlternateZoneFinal = targetAlternateZone;
                                                            targetAlternateZonePart = CloneHelper.clone(sourceAlternateZonePart, o->o.clone(targetAlternateZoneFinal, targetPrimaryZone));
                                                            ZonePart targetAlternateZonePartFinal = targetAlternateZonePart;
                                                            CloneHelper.execInSaveSession(()->targetAlternateZoneFinal.getParts().add(targetAlternateZonePartFinal));
                                                        }
                                                        if(targetAlternateZonePart != null) {
                                                            targetAlternateZonePart.setCheckoutTimestamp(timestamp);
                                                            targetAlternateZonePart.save();
                                                        }
                                                    }

                                                    targetAlternateZone.save();

//                                                    updateZoneOrigin(syncFromOther, syncWithOrigin, timestamp, alternateZone.getDocument(), clonedAlternateDocument,
//                                                            dnaToOriginDocSectionMap, dnaToOriginDocZoneMap, dnaToOriginDocZonePartMap,
//                                                            alternateZone, clonedDocumentSection, targetAlternateZone, newZoneOriginList, newZonePartOriginList);
                                                }

                                                flushSession = true;
                                            }

                                            alternateZone.setBreakInheritance(breakInheritance);
                                        }

                                        if (flushSession) {
                                            if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
                                                HibernateUtil.getManager().getSession().flush();
                                            } else {
                                                CloneHelper.getCloneSession().flush();
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Touchpoint Variants
                    //
                    Set<String> processedVariantDnas = new HashSet<>();

                    if (syncObjectMap.containsKey((long) SyncObjectType.ID_VARIANT)) {
                        boolean breakInheritance = targetDocument.isBreakInheritance();
                        targetDocument.setBreakInheritance(true);

                        List<Map<Long, Long>> objectMaps = (List<Map<Long, Long>>) syncObjectMap.get((long) SyncObjectType.ID_VARIANT);
                        {
	                        Map<String, Integer> objectIDStatusToLevelMap = new HashMap<>();
	                        Map<String, String> objectIDStatusToNameMap = new HashMap<>();
	                        Map<String, String> objectIDStatusToDnaMap = new HashMap<>();
	                        
	                        for(Map<Long, Long> objectMap : objectMaps) {
	                        	if(objectMap.size() != 1) {
	                                log.error("TouchpointVersioningService exception when sorting touchpoint variant: Invalid objectMaps.");
	                                throw new MessagepointException("Invalid objectMaps.", new Throwable("#TouchpointVersioningService#"));
	                        	}
	                        	Long objectIdx10 = objectMap.keySet().stream().findFirst().orElse(null);
                                long objectId = objectIdx10 / 0x10;
	                        	Long objectStatus = objectMap.get(objectIdx10);
	                        	String idStatus = objectIdx10.toString() + "-" + objectStatus.toString();
	                        	
	                            boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
	                            String objectSchema = (objectIsFromOther?sourceSchema:targetSchema);
	                            
	                            CloneHelper.execInSchema(objectSchema, ()->{
		                            TouchpointSelection touchpointSelection = TouchpointSelection.findById(objectId);
		                        	
		                            int tsLevel = touchpointSelection.getLevel();
		                            String tsName = touchpointSelection.getName();
		                            String tsDna = touchpointSelection.getDna();
		                            
		                            objectIDStatusToLevelMap.put(idStatus, tsLevel);
		                            objectIDStatusToNameMap.put(idStatus, tsName);
		                            objectIDStatusToDnaMap.put(idStatus, tsDna);
	                            });
	                        }
	                        
	                        Collections.sort(objectMaps, (m1, m2)->{
	                        	Long m1ObjectId = m1.keySet().stream().findFirst().orElse(null);
	                        	Long m1ObjectStatus = m1.get(m1ObjectId);
	                        	String m1IdStatus = m1ObjectId.toString() + "-" + m1ObjectStatus.toString();
	                            int m1Level = objectIDStatusToLevelMap.get(m1IdStatus);
	                            String m1Name = objectIDStatusToNameMap.get(m1IdStatus);
	                            String m1Dna = objectIDStatusToDnaMap.get(m1IdStatus);
	                            
	                        	Long m2ObjectId = m2.keySet().stream().findFirst().orElse(null);
	                        	Long m2ObjectStatus = m2.get(m2ObjectId);
	                        	String m2IdStatus = m2ObjectId.toString() + "-" + m2ObjectStatus.toString();
	                            int m2Level = objectIDStatusToLevelMap.get(m2IdStatus);
	                            String m2Name = objectIDStatusToNameMap.get(m2IdStatus);
	                            String m2Dna = objectIDStatusToDnaMap.get(m2IdStatus);
	                            
	                            if(m1Level != m2Level) {
	                            	return m1Level - m2Level;
	                            }
	                            
	                            int nameCompare = m1Name.compareTo(m2Name);
	                            if(nameCompare != 0) {
	                            	return nameCompare;
	                            }
	                            
	                        	return m1Dna.compareTo(m2Dna);
	                        });
                        }
                        
//                        Map<String, TouchpointSelection> dnaToTargetTouchpointSelectionMap = CloneHelper.queryInSaveSession(()->targetDocumentFinal.getVisibleTouchpointSelections().stream().collect(Collectors.toMap(ts->ts.getDna(), Function.identity())));
                        
                        for (Map<Long, Long> objectMap : objectMaps) {
                            for (Long objectIdx10 : objectMap.keySet()) {
                                long objectStatus = objectMap.get(objectIdx10);
                                long objectId = objectIdx10 / 0x10;
                                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                                objectStatus = objectStatus & 0xFFFF;
                                String objectSchema = (objectIsFromOther?sourceSchema:targetSchema);
                                if (syncFromOther != objectIsFromOther) {
                                    if(syncFromOther) {
                                        if((objectStatus & 0x0F) == ContentSelectionStatusType.ID_DELETED) {
                                            TouchpointSelection touchpointSelection = CloneHelper.queryInSchema(objectSchema, ()->TouchpointSelection.findById(objectId));
//                                            removeTouchpointSelection(touchpointSelection, request.getRequestor());
                                            processedVariantDnas.add(touchpointSelection.getDna());
                                       }
                                    } else {
                                        if((objectStatus & 0xF00) / 0x100 == ContentSelectionStatusType.ID_DELETED) {
                                            TouchpointSelection touchpointSelection = CloneHelper.queryInSchema(objectSchema, ()->TouchpointSelection.findById(objectId));
//                                            removeTouchpointSelection(touchpointSelection, request.getRequestor());
                                            processedVariantDnas.add(touchpointSelection.getDna());
                                        }
                                    }
                                    continue;
                                }
                                
                                TouchpointSelection sourceTouchpointSelection = CloneHelper.queryInSchema(objectSchema, ()->TouchpointSelection.findById(objectId));
                                if (processedVariantDnas.contains(sourceTouchpointSelection.getDna()))
                                    continue;

//                                TouchpointSelection targetTouchpointSelection = dnaToTargetTouchpointSelectionMap.get(sourceTouchpointSelection.getDna()); 
                                Document targetAlternateLayout = sourceTouchpointSelection.getAlternateLayout() == null ? null : 
                                	CloneHelper.queryInSaveSession(()->Document.findByDnaAndParentDocument(sourceTouchpointSelection.getAlternateLayout(), targetDocumentFinal));
                                TouchpointSelection targetTouchpointSelection = 
                                    CloneHelper.queryInSaveSession(()->TouchpointSelection.findByDnaAndDocument(sourceTouchpointSelection, targetDocumentFinal));
                            	if(sourceTouchpointSelection != null && targetTouchpointSelection != null) { 
                            		ParameterGroupTreeNode sourceParameterTreeNode = CloneHelper.queryInSchema(objectSchema, ()->sourceTouchpointSelection.getParameterGroupTreeNode());
                            		ParameterGroupTreeNode targetParameterTreeNode = CloneHelper.queryInSaveSession(()->targetTouchpointSelection.getParameterGroupTreeNode());
                            		
                            		if(sourceParameterTreeNode != null && targetParameterTreeNode != null) {
                            			ParameterGroup sourcePG = CloneHelper.queryInSchema(objectSchema, ()->sourceParameterTreeNode.getParameterGroup());
                            			ParameterGroup targetPG = CloneHelper.queryInSaveSession(()->targetParameterTreeNode.getParameterGroup());
                            			if(sourcePG != null && targetPG == null) {
                            				targetPG = CloneHelper.assign(sourcePG);
                            				targetParameterTreeNode.setParameterGroup(targetPG);
                            				targetParameterTreeNode.save();
                            			}
                            			if(sourcePG != null && targetPG != null) {
	                                        ParameterGroupInstanceCollection sourcePGIC = CloneHelper.queryInSchema(objectSchema, ()->sourceParameterTreeNode.getParameterGroupInstanceCollection());
	                                        if(sourcePGIC != null && 
	                                           CloneHelper.queryInSaveSession(()->targetParameterTreeNode.getParameterGroupInstanceCollection()) == null) {
	                                        	ParameterGroupInstanceCollection pgic = CloneHelper.clone(sourcePGIC);
	                                        	pgic.save();
	                                        	targetParameterTreeNode.setParameterGroupInstanceCollection(pgic);
	                                    		targetParameterTreeNode.save();
	                                        }
	                                        
	                                        ParameterGroupInstanceCollection targetPGIC = CloneHelper.queryInSaveSession(()->targetParameterTreeNode.getParameterGroupInstanceCollection());
	                                        
	                                        Map<String, ParameterGroupInstance> sourcePGIMap = new HashMap<>();
	                                        Map<String, ParameterGroupInstance> targetPGIMap = new HashMap<>();
	                                        
	                                        if (sourcePGIC != null && sourcePGIC.getParameterGroupInstances() != null) {
                                                sourcePGIMap = CloneHelper.queryInSchema(objectSchema, ()->sourcePGIC.getParameterGroupInstances().stream().collect(Collectors.toMap(p->mapParameterGroupInstanceToString(p), Function.identity(), (p1, p2)->{
                                                    return p1.getId() <= p2.getId() ? p1 : p2;
                                                })));
	                                        }

	                                        if (targetPGIC != null && CloneHelper.queryInSaveSession(()->targetPGIC.getParameterGroupInstances()) != null) {
                                                targetPGIMap = CloneHelper.queryInSaveSession(()->targetPGIC.getParameterGroupInstances().stream().collect(Collectors.toMap(p->mapParameterGroupInstanceToString(p), Function.identity(), (p1, p2)->{
                                                    return p1.getId() <= p2.getId() ? p1 : p2;
                                                })));
	                                        }
	                                        
	                                        boolean changed = false;
	
	                                        if(targetPGIC != null && sourcePGIMap != null && ! sourcePGIMap.isEmpty()) {
	                                            for (String pgiKey : targetPGIMap.keySet()) {
	                                                if(! sourcePGIMap.containsKey(pgiKey)) {
	                                                    ParameterGroupInstance pgInstance = targetPGIMap.get(pgiKey);
	                                                    CloneHelper.execInSaveSession(()->{
                                                            Set<ParameterGroupInstance> pgiToDelete =
                                                                    targetPGIC.getParameterGroupInstances().stream()
                                                                            .filter(pgi->pgiKey.equals(mapParameterGroupInstanceToString(pgi)))
                                                                            .collect(Collectors.toSet());
                                                            targetPGIC.getParameterGroupInstances().removeAll(pgiToDelete);
	                                                    });
	                                                    changed = true;
	                                                }
	                                            }
	                                        }
	
	                                        if(targetPGIC != null) {
	                                            for (String pgiKey : sourcePGIMap.keySet()) {
	                                                if(! targetPGIMap.containsKey(pgiKey)) {
	                                                    ParameterGroupInstance pgInstance = sourcePGIMap.get(pgiKey);
	                                                    ParameterGroupInstance clonedPgInstance = CloneHelper.clone(pgInstance, o->o.clone(targetPGIC));
	                                                    targetPGIC.getParameterGroupInstances().add(clonedPgInstance);
	                                                    changed = true;
	                                                }
	                                            }
	                                        }
	                                        if(targetPGIC != null) {
	                                        	if(changed) {
	                                        		targetPGIC.save();
	                                        	}
	                                        }
                            			}
                            		}
                                    continue;
                            	}

                            	if(sourceTouchpointSelection != null && targetTouchpointSelection != null) {
                            	    CloneHelper.execInSaveSession(()->targetTouchpointSelection.setAlternateLayout(targetAlternateLayout));
                                }

                                if (sourceTouchpointSelection != null) {
                                    if(targetTouchpointSelection != null) {
                                        continue;
                                    }
                                    TouchpointSelection clonedTouchpointSelection = CloneHelper.clone(sourceTouchpointSelection, o->o.clone(targetDocumentFinal));
                                    Document clonedAlternateLayoutDocument = null;
                                    if (sourceTouchpointSelection.getAlternateLayout() != null) {
                                    	Document sourceAlternateLayout = sourceTouchpointSelection.getAlternateLayout();
                                    	clonedAlternateLayoutDocument = CloneHelper.queryInSaveSession(()->Document.findByDnaAndParentDocument(sourceAlternateLayout, targetDocumentFinal));
                                        clonedTouchpointSelection.setAlternateLayout(clonedAlternateLayoutDocument);
                                    }

                                    Set<ProofDefinition> clonedProofDefinitions = clonedTouchpointSelection.getProofDefinitions();
                                    if (clonedProofDefinitions != null) {
                                        for (ProofDefinition proofDefinition : clonedProofDefinitions) {
                                            DataResource dataResource = proofDefinition.getDataResource();
                                            if (dataResource != null) {
                                                DataResource clonedDataResource = CloneHelper.queryInSaveSession(()->DataResource.findByDnaAndDocument(dataResource, projectDocument));
                                                if(clonedDataResource == null) {
                                                	    clonedDataResource = CloneHelper.clone(dataResource, o->o.clone(projectDocument));
                                                }
                                                proofDefinition.setDataResource(clonedDataResource);
                                            }
                                        }
                                    }

                                    CloneHelper.execInSaveSession(()->targetDocumentFinal.getTouchpointSelections().add(clonedTouchpointSelection));

                                    processedVariantDnas.add(sourceTouchpointSelection.getDna());
                                    clonedTouchpointSelection.setDocument(targetDocument);
                                    clonedTouchpointSelection.setCheckoutTimestamp(timestamp);
                                    clonedTouchpointSelection.save();

                                    Document originDocumentOfSyncTo = null;
                                    if (targetDocument.getOriginObject() == null || targetDocument.getCheckoutTimestamp() == null) {
                                        originDocumentOfSyncTo = null;
                                    } else {
                                        originDocumentOfSyncTo = (Document) targetDocument.getOriginObject();
                                    }
                                    
                                    Document originDocumentOfSyncToFinal = originDocumentOfSyncTo;
                                    if (syncWithOrigin) {
                                        if (!syncFromOther) {
                                            sourceTouchpointSelection.setOriginObject(clonedTouchpointSelection);
                                            sourceTouchpointSelection.setCheckoutTimestamp(timestamp);
                                            sourceTouchpointSelection.setCheckinTimestamp(timestamp);
                                            sourceTouchpointSelection.save();
                                            newTouchpointSelectionOriginList.add(clonedTouchpointSelection);
                                            if(originDocumentOfSyncTo == null) {
                                                clonedTouchpointSelection.setOriginObject(null);
                                                clonedTouchpointSelection.setCheckoutTimestamp(null);
                                                clonedTouchpointSelection.setCheckinTimestamp(null);
                                                clonedTouchpointSelection.save();
                                            } else {
                                                TouchpointSelection originTouchpointSelection = TouchpointSelection.findByDnaAndDocument(sourceTouchpointSelection, originDocumentOfSyncTo);
                                                clonedTouchpointSelection.setOriginObject(originTouchpointSelection);
                                                clonedTouchpointSelection.setCheckoutTimestamp(timestamp);
                                                clonedTouchpointSelection.setCheckinTimestamp(null);
                                                clonedTouchpointSelection.save();
                                            }
                                        }
                                    } else {
                                    	Document clonedAlternateLayoutDocumentFinal = clonedAlternateLayoutDocument;
                                        TouchpointSelection originTouchpointSelection = CloneHelper.queryInSaveSession(()->TouchpointSelection.findByDnaAndDocument(sourceTouchpointSelection, originDocumentOfSyncToFinal));
                                        clonedTouchpointSelection.setOriginObject(originTouchpointSelection);
                                        clonedTouchpointSelection.setCheckoutTimestamp(timestamp);
                                        clonedTouchpointSelection.setCheckinTimestamp(null);
                                        clonedTouchpointSelection.save();
                                    }

                                    if (sourceTouchpointSelection.getParameterGroupTreeNode() != null) {
                                        ParameterGroupTreeNode sourceParameterGroupTreeNode = sourceTouchpointSelection.getParameterGroupTreeNode();
                                        ParameterGroupTreeNode clonedParameterGroupTreeNode = CloneHelper.clone(sourceParameterGroupTreeNode);

                                        ParameterGroupTreeNode sourceParentParameterGroupTreeNode = sourceParameterGroupTreeNode.getParentNode();
                                        ParameterGroupTreeNode clonedParent = CloneHelper.queryInSaveSession(()->ParameterGroupTreeNode.findByDnaAndDocument(sourceParentParameterGroupTreeNode, targetDocumentFinal));

                                        if (clonedParent == null) {
                                            // Parent has to be cloned before
                                            // its child
                                            log.error("TouchpointVersioningService exception when cloning touchpoint variant (" + sourceParameterGroupTreeNode.getName() + "): Parent has to be cloned before its child.");
                                            throw new MessagepointException("Touchpoint variant (" + sourceParameterGroupTreeNode.getName() + "): Parent has to be cloned before its child.", new Throwable("#TouchpointVersioningService#"));
                                        }

                                        clonedParameterGroupTreeNode.setParentNode(clonedParent);
                                        clonedTouchpointSelection.setParameterGroupTreeNode(clonedParameterGroupTreeNode);
                                        clonedTouchpointSelection.setCheckoutTimestamp(timestamp);
                                        if(syncWithOrigin) {
                                            if (!syncFromOther) {
                                                if(originDocumentOfSyncTo == null) {
                                                    clonedParameterGroupTreeNode.setOriginObject(null);
                                                    clonedTouchpointSelection.setCheckoutTimestamp(null);
                                                    clonedTouchpointSelection.setCheckinTimestamp(null);
                                                } else {
                                                    clonedParameterGroupTreeNode.setOriginObject(ParameterGroupTreeNode.findByDnaAndDocument(sourceParameterGroupTreeNode, originDocumentOfSyncTo));
                                                }
                                                sourceParameterGroupTreeNode.setOriginObject(clonedParameterGroupTreeNode);
                                                sourceParameterGroupTreeNode.setCheckoutTimestamp(timestamp);
                                                sourceParameterGroupTreeNode.setCheckinTimestamp(timestamp);
                                                sourceParameterGroupTreeNode.save();
                                                newParameterGroupTreeNodeOriginList.add(clonedParameterGroupTreeNode);
                                            }
                                        } else {
                                            if(originDocumentOfSyncTo == null) {
                                                clonedParameterGroupTreeNode.setOriginObject(null);
                                                clonedTouchpointSelection.setCheckoutTimestamp(null);
                                                clonedTouchpointSelection.setCheckinTimestamp(null);
                                            } else {
                                            	   ParameterGroupTreeNode clonedOriginNode = CloneHelper.queryInSaveSession(()->ParameterGroupTreeNode.findByDnaAndDocument(sourceParameterGroupTreeNode, originDocumentOfSyncToFinal));
                                                clonedParameterGroupTreeNode.setOriginObject(clonedOriginNode);
                                            }
                                        }

                                        clonedParameterGroupTreeNode.save();

                                        // Structured messages
                                        //
                                        /** TODO
                                        List<Message> messages = CloneHelper.queryInSaveSession(()->Message.findAllTouchpointContentSelectable(targetDocumentFinal.getId()));
                                        if (messages != null && messages.size() > 0) {
                                            for (Message message : messages) {
                                                ContentAssociation parentContentAssociation = CloneHelper.queryInSaveSession(()->ContentAssociation.findByMessageAndTreeNode(message.getId(), clonedParent.getId()));
                                                if (parentContentAssociation != null) {
                                                    try {
                                                        MessageInstance activeOrArchivedCopy = message.hasActiveData() ? (MessageInstance) message.getActiveCopy() : message.isArchived() && message.getLatestArchivedVersionInfo() != null ? message.getLatestArchivedVersionInfo().getModelInstance() : null;
                                                    } catch (Exception ex) {
                                                        ex.printStackTrace();
                                                    }

                                                	MessageInstance activeOrArchivedCopy = message.hasActiveData() ? (MessageInstance) message.getActiveCopy() : message.isArchived() && message.getLatestArchivedVersionInfo() != null ? message.getLatestArchivedVersionInfo().getModelInstance() : null;

                                                    ContentAssociation contentAssociation = CloneHelper.queryInSaveSession(()->new ContentAssociation());
                                                    contentAssociation.setMessage(message);
                                                    contentAssociation.setTypeId(ContentAssociationType.ID_REFERENCES);
                                                    contentAssociation.setParameterGroupTreeNode(clonedParameterGroupTreeNode);
                                                    contentAssociation.setMessageInstance(parentContentAssociation.getMessageInstance());
                                                    contentAssociation.setReferencingTreeNode(parentContentAssociation.getParameterGroupTreeNode());
                                                    contentAssociation.setReferencingContentLibrary(parentContentAssociation.findAncestorContentLibrary());
                                                    contentAssociation.setReferencingLocalContentLibrary(parentContentAssociation.findAncestorLocalContentLibrary());
                                                    contentAssociation.save();
                                                    CloneHelper.execInSaveSession(()->message.getContentAssociations().add(contentAssociation));

                                                    List<ContentAssociation> contents = CloneHelper.queryInSaveSession(()->ContentAssociation.findAllDependentContentAssociations(parentContentAssociation.getMessage().getId(), parentContentAssociation.getParameterGroupTreeNode().getId(), -1));
                                                    if (contents.size() > 0) {
                                                        for (ContentAssociation vo : contents) {
                                                            ContentAssociation ca = CloneHelper.queryInSaveSession(()->new ContentAssociation());
                                                            ca.setContent(vo.getContent());
                                                            ca.setMessage(contentAssociation.getMessage());
                                                            ca.setMessageInstance(contentAssociation.getMessageInstance());
                                                            ca.setTypeId(contentAssociation.getTypeId());
                                                            ca.setMessagepointLocale(vo.getMessagepointLocale());
                                                            ca.setReferencingTreeNode(contentAssociation.getReferencingTreeNode());
                                                            ca.setParameterGroupTreeNode(contentAssociation.getParameterGroupTreeNode());
                                                            ca.setReferencingContentLibrary(vo.findAncestorContentLibrary());
                                                            ca.setReferencingLocalContentLibrary(vo.findAncestorLocalContentLibrary());
                                                            ca.setZonePart(vo.getZonePart());
                                                            ca.save();
                                                            CloneHelper.execInSaveSession(()->message.getContentAssociations().add(ca));
                                                        }
                                                    }

                                                    message.save();
                                                    
                                                    Date productionDate = new Date();

                                                    ProductionContentAssociation productContentAssociation = new ProductionContentAssociation();
                                                    productContentAssociation.setMessage(message);
                                                    productContentAssociation.setMessageInstance(activeOrArchivedCopy);
                                                    productContentAssociation.setParameterGroupTreeNode(clonedParameterGroupTreeNode);
                                                    productContentAssociation.setMessagepointLocale(null);
                                                    productContentAssociation.setZonePart(null);
                                                    productContentAssociation.setContent(null);
                                                    productContentAssociation.setProductionDate(productionDate);
                                                   	productContentAssociation.setTypeId(ContentAssociationType.ID_REFERENCES);
                                                   	productContentAssociation.setReferencingTreeNode(parentContentAssociation.getParameterGroupTreeNode());
                                                    productContentAssociation.setReferencingContentLibrary(parentContentAssociation.findAncestorContentLibrary());
                                                    productContentAssociation.setReferencingLocalContentLibrary(parentContentAssociation.findAncestorLocalContentLibrary());
                                                    productContentAssociation.setSha256Hash(contentAssociation.getSha256Hash());
                                                    productContentAssociation.save();
                                                }
                                            }
                                        }
                                        **/

                                        TouchpointSelection parentSelection = clonedTouchpointSelection.getParent();

                                        // Priority for variant messages
                                        //
                                        if (parentSelection != null) {
                                            List<ContentObjectZonePriority> parentMessageZoneAssocs = CloneHelper.queryInSaveSession(()->ContentObjectZonePriority.findByTouchpointSelectionExcludeMaster(parentSelection));
                                            for (ContentObjectZonePriority parentZoneAssoc : parentMessageZoneAssocs) {
                                                ContentObjectZonePriority zoneAssociation = CloneHelper.queryInSaveSession(()->new ContentObjectZonePriority());
                                                zoneAssociation.setContentObjectPriority(parentZoneAssoc.getContentObjectPriority());
                                                zoneAssociation.setContentObject(parentZoneAssoc.getContentObject());
                                                zoneAssociation.setSuppress(parentZoneAssoc.isSuppress());
                                                zoneAssociation.setZone(parentZoneAssoc.getZone());
                                                zoneAssociation.setTouchpointSelection(clonedTouchpointSelection);
                                                zoneAssociation.save();
                                            }
                                        }
                                    }

                                    clonedTouchpointSelection.save();

                                    // Clone the latest active workflow action
                                    // if exists
                                    ConfigurableWorkflowAction latestActiveAction = ConfigurableWorkflowAction.findLatestActiveActionByModel(sourceTouchpointSelection);
                                    if (latestActiveAction != null) {
                                        ConfigurableWorkflowAction latestActiveActionClone = CloneHelper.clone(latestActiveAction, o->o.clone(clonedTouchpointSelection));
                                        latestActiveActionClone.save();
                                    }

                                    // Create the configurable workflow action
                                    //
                                    CloneHelper.execInSaveSession(()->{
                                    	ConfigurableWorkflowActionHistory.generateAction(null, clonedTouchpointSelection, ConfigurableWorkflowActionType.ID_CREATED, ConfigurableWorkflowActionStateType.ID_NONE, ConfigurableWorkflowActionStateType.ID_WORKING_COPY, ApplicationUtil.getMessage(ConfigurableWorkflowActionStateType.STATE_WORKING_COPY_CODE), request.getRequestor(), null, DateUtil.now(), null, ConfigurableWorkflowActionHistory.ACTION_REGULAR);
                                    });
                                    
                                    if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
                                        HibernateUtil.getManager().getSession().flush();
                                    } else {
                                        CloneHelper.getCloneSession().flush();
                                    }
                                }
                            }
                        }

                        targetDocument.setBreakInheritance(breakInheritance);
                    }

                    targetDocument.save();

                    boolean isConnectedTouchpoint = sourceDocument.isConnectedEnabled();

                    if(true || (! isConnectedTouchpoint)) {
                        List<DataSourceAssociation> allSourceDSAsList = DataSourceAssociation.findAll();
                        for (DataSourceAssociation sourceDSA : allSourceDSAsList) {
                            for (Document sourceDSADocument : sourceDSA.getDocuments()) {
                                if (sourceDSADocument.getId() == sourceDocument.getId()) {
                                    Holder<Boolean> newDSACreated = new Holder<>(false);
                                    DataSourceAssociation targetDSA = CloneHelper.assign(sourceDSA, dsa -> {
                                        DataSourceAssociation newDSA = dsa.clone(targetDocumentFinal);
                                        newDSACreated.value = true;
                                        return newDSA;
                                    });
                                    if (newDSACreated.value != null && newDSACreated.value) {
                                        List<DataSource> allDataSourcesList = new ArrayList<>();
                                        if (sourceDSA.getPrimaryDataSource() != null) {
                                            allDataSourcesList.add(sourceDSA.getPrimaryDataSource());
                                        }
                                        Set<ReferenceConnection> referenceConnectionsInDSA = sourceDSA.getReferenceConnections();
                                        for (ReferenceConnection referenceConnection : referenceConnectionsInDSA) {
                                            DataSource referenceDataSource = referenceConnection.getReferenceDataSource();
                                            if (referenceDataSource != null) {
                                                allDataSourcesList.add(referenceDataSource);
                                            }
                                        }
                                        for (DataSource sourceDataSource : allDataSourcesList) {
                                            if (sourceDataSource.getDocuments() != null) {
                                                for (Document sourceDSDocument : sourceDataSource.getDocuments()) {
                                                    if (sourceDSDocument.getId() == sourceDocument.getId()) {
                                                        List<Long> sourceDEVIDsList = DataElementVariable.findAllIdsByDataSource(sourceDataSource.getId());
                                                        DataSource targetDataSource = CloneHelper.assign(sourceDataSource);
                                                        for (Long sourceDEVId : sourceDEVIDsList) {
                                                            DataElementVariable sourceDEV = DataElementVariable.findById(sourceDEVId);
                                                            DataElementVariable targetDEV = CloneHelper.assign(sourceDEV);
                                                            Map<Long, VariableDataElementMap> sourceVDEMMap = sourceDEV.getDataElementMap();
                                                            VariableDataElementMap sourcdVDEM = sourceVDEMMap.get(sourceDataSource.getId());
                                                            if (sourcdVDEM != null) {
                                                                VariableDataElementMap targetVDEM = CloneHelper.queryInSaveSession(() -> targetDEV.getDataElementMap().get(targetDataSource.getId()));
                                                                if (targetVDEM == null) {
                                                                    VariableDataElementMap clonedVDEM = CloneHelper.clone(sourcdVDEM);
                                                                    CloneHelper.execInSaveSession(() -> {
                                                                        clonedVDEM.save();
                                                                        if (targetDEV.getDataElementMap().containsKey(targetDataSource.getId())) {
                                                                            targetDEV.getDataElementMap().remove(targetDataSource.getId());
                                                                        }
                                                                        targetDEV.getDataElementMap().put(targetDataSource.getId(), targetVDEM);
                                                                        targetDEV.save();
                                                                    });
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
                        HibernateUtil.getManager().getSession().flush();
                    } else {
                        CloneHelper.getCloneSession().flush();
                    }
                    
                    if(syncWithOrigin) {
                        if(! syncFromOther) { // Commit
                            Timestamp checkoutTimestamp = new Timestamp(DateUtil.now().getTime());
                            Document.findAllDocumentsAndProjects().stream().filter(d->d.getOriginObject()!=null && d.getOriginObject().getId() == otherDocument.getId()).forEach(childDoc->{
                                newDocumentSectionOriginList.forEach(originObject->{
                                    String objectDna = originObject.getDna();
                                    IdentifiableMessagePointModel object = DocumentSection.findByDnaAndDocument(objectDna, childDoc);
                                    if(object != null && (object.getOriginObject() == null || object.getOriginObject().getId() != originObject.getId())) {
                                        object.setOriginObject(originObject);
                                        object.setCheckoutTimestamp(checkoutTimestamp);
                                        object.save();
                                    }
                                });
                                
                                newZoneOriginList.forEach(originObject->{
                                    String objectDna = originObject.getDna();
                                    IdentifiableMessagePointModel object = Zone.findByDnaAndDocument(objectDna, childDoc);
                                    if(object != null && (object.getOriginObject() == null || object.getOriginObject().getId() != originObject.getId())) {
                                        object.setOriginObject(originObject);
                                        object.setCheckoutTimestamp(checkoutTimestamp);
                                        object.save();
                                    }
                                });
                                
                                newZonePartOriginList.forEach(originObject->{
                                    IdentifiableMessagePointModel object = ZonePart.findByDnaAndDocument(originObject, childDoc);
                                    if(object != null && (object.getOriginObject() == null || object.getOriginObject().getId() != originObject.getId())) {
                                        object.setOriginObject(originObject);
                                        object.setCheckoutTimestamp(checkoutTimestamp);
                                        object.save();
                                    }
                                });
                                
                                newTouchpointSelectionOriginList.forEach(originObject->{
                                    Document alternateLayout = originObject.getAlternateLayout() != null ? Document.findByDnaAndParentDocument(originObject.getAlternateLayout(), childDoc) : null;
                                    IdentifiableMessagePointModel object = TouchpointSelection.findByDnaAndDocument(originObject, childDoc);
                                    if(object != null && (object.getOriginObject() == null || object.getOriginObject().getId() != originObject.getId())) {
                                        object.setOriginObject(originObject);
                                        object.setCheckoutTimestamp(checkoutTimestamp);
                                        object.save();
                                    }
                                });
                                
                                newParameterGroupTreeNodeOriginList.forEach(originObject->{
                                    IdentifiableMessagePointModel object = ParameterGroupTreeNode.findByDnaAndDocument(originObject, childDoc);
                                    if(object != null && (object.getOriginObject() == null || object.getOriginObject().getId() != originObject.getId())) {
                                        object.setOriginObject(originObject);
                                        object.setCheckoutTimestamp(checkoutTimestamp);
                                        object.save();
                                    }
                                });
                                
                                childDoc.save();
                            });
                            
                        }
                    }

                    context.getResponse().setResultValueBean(targetDocument.getId());
                }

            }

            log.info("TouchpointVersioningService finished");
        } catch (Exception e) {
            log.error("unexpected exception when invoking TouchpointVersioningService execute method", e);
            this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e.toString(), context.getLocale());
            throw new RuntimeException(e.getMessage());
        } finally {
            if (sessionSwitched) {
                CloneHelper.stopCrossInstanceClone();
                
                if(saveSessionHolder != null)
                    HibernateUtil.getManager().restoreSession(saveSessionHolder);
                if(mainSessionHolder != null)
                    HibernateUtil.getManager().restoreSession(mainSessionHolder);
                if(podMasterSessionHolder != null)
                    HibernateUtil.getManager().restoreSession(podMasterSessionHolder);
            }
        }
    }

    private String mapParameterGroupInstanceToString(ParameterGroupInstance pgi) {
        StringBuilder pgiStringBuilder = new StringBuilder();
        pgiStringBuilder.append("Name[").append(pgi.getName() == null ? "" : pgi.getName()).append("]");
        ParameterGroup pg = pgi.getParameterGroup();
        if(pg != null) {
            pgiStringBuilder.append("GROUP[").append(pg.getName() == null ? "" : pg.getName()).append("]");
        }
        pgiStringBuilder.append("Value[").append(pgi.getValueString()).append("]");
        return pgiStringBuilder.toString();
    }
    
    private void updateZoneOrigin(boolean syncFromOther, boolean syncWithOrigin, Timestamp timestamp, 
            Document sourceDocument, Document syncToDocument, 
            Map<Long, Map<String, DocumentSection>> dnaToOriginDocSectionMap, 
            Map<Long, Map<String, Zone>> dnaToOriginDocZoneMap, 
            Map<Long, Map<String, ZonePart>> dnaToOriginDocZonePartMap, 
            Zone sourceZone, DocumentSection clonedDocumentSection, Zone clonedZone, 
            List<Zone> newZoneOriginList, List<ZonePart> newZonePartOriginList
            
            ) {
        Document originDocumentOfSyncTo = null;
        
        if (syncToDocument.getOriginObject() != null && syncToDocument.getCheckoutTimestamp() != null) {
            originDocumentOfSyncTo = (Document) syncToDocument.getOriginObject();
        }
        
        if (syncWithOrigin) {
            clonedZone.setCheckoutTimestamp(timestamp);
            if (!syncFromOther) { // commit a new object. cloned object is
                                  // origin, and zone is child
                sourceZone.setOriginObject(clonedZone);
                sourceZone.setCheckoutTimestamp(timestamp);
                sourceZone.setCheckinTimestamp(timestamp);
                sourceZone.save();
                newZoneOriginList.add(clonedZone);
                newZonePartOriginList.addAll(CloneHelper.queryInSaveSession(clonedZone::getParts));
                
                for (ZonePart clonedZonePart : CloneHelper.queryInSaveSession(clonedZone::getParts)) {
                    ZonePart originZonePart = ZonePart.findByDnaAndDocument(clonedZonePart, sourceDocument); // (ZonePart) clonedZonePart.getOriginObject();
                    if(originZonePart != null) {
                        originZonePart.setOriginObject(clonedZonePart);
                        originZonePart.setCheckoutTimestamp(timestamp);
                        originZonePart.setCheckinTimestamp(timestamp);
                        originZonePart.save();
                    }
                }

                if (originDocumentOfSyncTo == null) { // syncTo is root
                    clonedZone.setOriginObject(null);
                    clonedZone.setCheckoutTimestamp(null);
                    clonedZone.setCheckinTimestamp(null);
                    clonedZone.save();
                    for (ZonePart clonedZonePart : CloneHelper.queryInSaveSession(clonedZone::getParts)) {
                        clonedZonePart.setOriginObject(null);
                        clonedZonePart.setCheckoutTimestamp(null);
                        clonedZonePart.setCheckinTimestamp(null);
                        clonedZonePart.save();
                    }
                } else {
                    Map<String, DocumentSection> dnaToDocSectionMap = dnaToOriginDocSectionMap.get(originDocumentOfSyncTo.getId());
                    Map<String, Zone> dnaToDocZoneMap = dnaToOriginDocZoneMap.get(originDocumentOfSyncTo.getId());
                    Map<String, ZonePart> dnaToDocZonePartMap = dnaToOriginDocZonePartMap.get(originDocumentOfSyncTo.getId());

                    Zone originZone = dnaToDocZoneMap.get(sourceZone.getDna());
                    clonedZone.setOriginObject(originZone);
                    clonedZone.save();
                    for (ZonePart clonedZonePart : CloneHelper.queryInSaveSession(clonedZone::getParts)) {
                        ZonePart originZonePart = dnaToDocZonePartMap.get(clonedZonePart.getDna());
                        clonedZonePart.setOriginObject(originZonePart);
                        clonedZonePart.save();
                    }
                }
            } else {
                clonedZone.save();
            }
        } else {
            if (originDocumentOfSyncTo == null) { // root
                                                  // doc
                clonedZone.setOriginObject(null);
                clonedZone.setCheckoutTimestamp(null);
                clonedZone.setCheckinTimestamp(null);
                clonedZone.save();
                for (ZonePart clonedZonePart : CloneHelper.queryInSaveSession(clonedZone::getParts)) {
                    clonedZonePart.setOriginObject(null);
                    clonedZonePart.setCheckoutTimestamp(null);
                    clonedZonePart.setCheckinTimestamp(null);
                    clonedZonePart.save();
                }
            } else {
                Map<String, DocumentSection> dnaToDocSectionMap = dnaToOriginDocSectionMap.get(originDocumentOfSyncTo.getId());
                Map<String, Zone> dnaToDocZoneMap = dnaToOriginDocZoneMap.get(originDocumentOfSyncTo.getId());
                Map<String, ZonePart> dnaToDocZonePartMap = dnaToOriginDocZonePartMap.get(originDocumentOfSyncTo.getId());
                Zone originZone = dnaToDocZoneMap.get(sourceZone.getDna());
                clonedZone.setOriginObject(originZone);
                clonedZone.save();
                for (ZonePart clonedZonePart : CloneHelper.queryInSaveSession(clonedZone::getParts)) {
                    ZonePart originZonePart = dnaToDocZonePartMap.get(clonedZonePart.getDna());
                    clonedZonePart.setOriginObject(originZonePart);
                    clonedZonePart.save();
                }
            }
        }
/*
        CloneHelper.execInSaveSession(()->{
	        for (Workgroup workgroup : clonedZone.getWorkgroups()) {
	            workgroup.save();
	        }
        });
*/
        if(clonedDocumentSection != null) {
            CloneHelper.execInSaveSession(()->clonedDocumentSection.getZones().add(clonedZone));
        }
        CloneHelper.execInSaveSession(()->syncToDocument.getZones().add(clonedZone));
    }

    private void updateSectionOrigin(boolean syncFromOther, boolean syncWithOrigin, Timestamp timestamp, 
            Document sourceDocument, Document targetDocument, 
            Map<Long, Map<String, DocumentSection>> dnaToOriginDocSectionMap, 
            Map<Long, Map<String, Zone>> dnaToOriginDocZoneMap, 
            Map<Long, Map<String, ZonePart>> dnaToOriginDocZonePartMap, 
            DocumentSection sourceDocumentSection, DocumentSection clonedDocumentSection, List<DocumentSection> newDocumentSectionOriginList) {
        if (syncWithOrigin) { // the other party is parent. Sync in same schema.
            if (! syncFromOther) // Commit. source is in child, and cloned is in
                                // parent.
            {
                Document originDocumentOfSyncTo = null;
                if (targetDocument.getOriginObject() != null && targetDocument.getCheckoutTimestamp() != null) {
                	originDocumentOfSyncTo = (Document) targetDocument.getOriginObject();
                }
                
                for (Zone clonedZone : CloneHelper.queryInSaveSession(clonedDocumentSection::getZones)) {
                    String zoneDna = CloneHelper.queryInSaveSession(clonedZone::getDna);
                    Zone originZone = Zone.findByDnaAndDocument(zoneDna,  sourceDocument); // (Zone) clonedZone.getOriginObject();
                    if (originZone != null) {
                        originZone.setOriginObject(clonedZone);
                        originZone.setCheckoutTimestamp(timestamp);
                        originZone.setCheckinTimestamp(timestamp);
                        originZone.save();

                        for (ZonePart clonedZonePart : CloneHelper.queryInSaveSession(clonedZone::getParts)) {
                            ZonePart originZonePart = ZonePart.findByDnaAndDocument(clonedZonePart, sourceDocument); // (ZonePart)
                                                                                                                     // clonedZonePart.getOriginObject();
                            if (originZonePart != null) {
                                originZonePart.setOriginObject(clonedZonePart);
                                originZonePart.setCheckoutTimestamp(timestamp);
                                originZonePart.setCheckinTimestamp(timestamp);
                                originZonePart.save();
                            }
                        }
                    }
                }

                sourceDocumentSection.setOriginObject(clonedDocumentSection);
                sourceDocumentSection.setCheckoutTimestamp(timestamp);
                sourceDocumentSection.setCheckinTimestamp(timestamp);
                sourceDocumentSection.save();

                newDocumentSectionOriginList.add(clonedDocumentSection);

                if (originDocumentOfSyncTo == null) {
                    for (Zone clonedZone : CloneHelper.queryInSaveSession(clonedDocumentSection::getZones)) {
                        clonedZone.setOriginObject(null);
                        clonedZone.setCheckoutTimestamp(null);
                        clonedZone.setCheckinTimestamp(null);
                        clonedZone.save();

                        for (ZonePart clonedZonePart : CloneHelper.queryInSaveSession(clonedZone::getParts)) {
                            clonedZonePart.setOriginObject(null);
                            clonedZonePart.setCheckoutTimestamp(null);
                            clonedZonePart.setCheckinTimestamp(null);
                            clonedZonePart.save();
                        }
                    }

                    clonedDocumentSection.setOriginObject(null);
                    clonedDocumentSection.setCheckoutTimestamp(null);
                    clonedDocumentSection.setCheckinTimestamp(null);
                    clonedDocumentSection.save();
                } else {
                    Map<String, DocumentSection> dnaToDocSectionMap = dnaToOriginDocSectionMap.get(originDocumentOfSyncTo.getId());
                    Map<String, Zone> dnaToDocZoneMap = dnaToOriginDocZoneMap.get(originDocumentOfSyncTo.getId());
                    Map<String, ZonePart> dnaToDocZonePartMap = dnaToOriginDocZonePartMap.get(originDocumentOfSyncTo.getId());

                    for (Zone clonedZone : CloneHelper.queryInSaveSession(clonedDocumentSection::getZones)) {
                        clonedZone.setOriginObject(dnaToDocZoneMap.get(clonedZone.getDna()));
                        clonedZone.setCheckoutTimestamp(originDocumentOfSyncTo.getCheckoutTimestamp());
                        clonedZone.setCheckinTimestamp(null);
                        clonedZone.save();

                        for (ZonePart clonedZonePart : CloneHelper.queryInSaveSession(clonedZone::getParts)) {
                            clonedZonePart.setOriginObject(dnaToDocZonePartMap.get(clonedZonePart.getDna()));
                            clonedZonePart.setCheckoutTimestamp(originDocumentOfSyncTo.getCheckoutTimestamp());
                            clonedZonePart.setCheckinTimestamp(null);
                            clonedZonePart.save();
                        }
                    }

                    clonedDocumentSection.setOriginObject(dnaToDocSectionMap.get(clonedDocumentSection.getDna()));
                    clonedDocumentSection.setCheckoutTimestamp(originDocumentOfSyncTo.getCheckoutTimestamp());
                    clonedDocumentSection.setCheckinTimestamp(null);
                    clonedDocumentSection.save();
                }
            }
        } else { // the other party is not parent.
        		CloneHelper.execInSaveSession(()->{ // No source objects involved. So we just run in save schema.
	            Document originDocumentOfSyncTo = null;
	            if (targetDocument.getOriginObject() != null && targetDocument.getCheckoutTimestamp() != null) {
	                originDocumentOfSyncTo = (Document) targetDocument.getOriginObject();
	            }
	
	            if (originDocumentOfSyncTo == null) { // The other document is root
	                clonedDocumentSection.setOriginObject(null);
	                clonedDocumentSection.setCheckoutTimestamp(null);
	                clonedDocumentSection.save();
	                for (Zone clonedZone : CloneHelper.queryInSaveSession(clonedDocumentSection::getZones)) {
	                    clonedZone.setOriginObject(null);
	                    clonedZone.setCheckoutTimestamp(null);
	                    clonedZone.save();
	                    for (ZonePart clonedZonePart : CloneHelper.queryInSaveSession(clonedZone::getParts)) {
	                        clonedZonePart.setOriginObject(null);
	                        clonedZonePart.setCheckoutTimestamp(null);
	                        clonedZonePart.save();
	                    }
	                }
	            } else { // the other document is not root, so we will find every
	                     // cloned objects' origin
	                Map<String, DocumentSection> dnaToDocSectionMap = dnaToOriginDocSectionMap.get(originDocumentOfSyncTo.getId());
	                Map<String, Zone> dnaToDocZoneMap = dnaToOriginDocZoneMap.get(originDocumentOfSyncTo.getId());
	                Map<String, ZonePart> dnaToDocZonePartMap = dnaToOriginDocZonePartMap.get(originDocumentOfSyncTo.getId());
	
	                DocumentSection originDocumentSection = dnaToDocSectionMap.get(clonedDocumentSection.getDna());
	                clonedDocumentSection.setOriginObject(originDocumentSection);
	                clonedDocumentSection.save();
	                for (Zone clonedZone : CloneHelper.queryInSaveSession(clonedDocumentSection::getZones)) {
	                    Zone originZone = dnaToDocZoneMap.get(clonedZone.getDna());
	                    clonedZone.setOriginObject(originZone);
	                    clonedZone.save();
	                    for (ZonePart clonedZonePart : CloneHelper.queryInSaveSession(clonedZone::getParts)) {
	                        ZonePart originZonePart = dnaToDocZonePartMap.get(clonedZonePart.getDna());
	                        clonedZonePart.setOriginObject(originZonePart);
	                        clonedZonePart.save();
	                    }
	                }
	            }
        		});
        }
    }

    private void putDocumentSectionZoneAndZonePartToMap(Document originDocumentOfSyncTo, 
            Map<Long, Map<String, DocumentSection>> dnaToOriginDocSectionMap, 
            Map<Long, Map<String, Zone>> dnaToOriginDocZoneMap, 
            Map<Long, Map<String, ZonePart>> dnaToOriginDocZonePartMap) {
        Map<String, DocumentSection> dnaToDocSectionMap = dnaToOriginDocSectionMap.get(originDocumentOfSyncTo.getId());
        Map<String, Zone> dnaToDocZoneMap = dnaToOriginDocZoneMap.get(originDocumentOfSyncTo.getId());
        Map<String, ZonePart> dnaToDocZonePartMap = dnaToOriginDocZonePartMap.get(originDocumentOfSyncTo.getId());
        if (dnaToDocZoneMap == null) {
            dnaToDocZoneMap = new HashMap<>();
            dnaToOriginDocZoneMap.put(originDocumentOfSyncTo.getId(), dnaToDocZoneMap);
        }
        if (dnaToDocZonePartMap == null) {
            dnaToDocZonePartMap = new HashMap<>();
            dnaToOriginDocZonePartMap.put(originDocumentOfSyncTo.getId(), dnaToDocZonePartMap);
        }
        if (dnaToDocSectionMap == null) {
            final Map<String, Zone> finalDnaToDocZoneMap = dnaToDocZoneMap;
            final Map<String, ZonePart> finalDnaToDocZonePartMap = dnaToDocZonePartMap;
            dnaToDocSectionMap = originDocumentOfSyncTo.getDocumentSections().stream().collect(Collectors.toMap(ds -> {
                finalDnaToDocZoneMap.putAll(ds.getZones().stream().collect(Collectors.toMap(z -> {
                    finalDnaToDocZonePartMap.putAll(z.getParts().stream().collect(Collectors.toMap(zp -> zp.getDna(), Function.identity())));
                    return z.getDna();
                }, Function.identity())));
                return ds.getDna();
            }, Function.identity()));
            dnaToOriginDocSectionMap.put(originDocumentOfSyncTo.getId(), dnaToDocSectionMap);
        }
    }

    private Document duplicateDocument(TouchpointVersioningServiceRequest request) {
        Set<Long> messagepointLocales = request.getLanguagesForSync();
        Document primaryDoc = Document.findById(request.getDocumentId());
        Document primaryLayoutDoc = null;
        if (request.getPrimaryLayoutDocumentId() != null) {
        	primaryLayoutDoc = CloneHelper.queryInSaveSession(()->Document.findById(request.getPrimaryLayoutDocumentId()));
        }

        boolean isConnectedTouchpoint = primaryDoc.isConnectedEnabled();

        Document channelDocument = null;
        if (request.getChannelParentDocumentId() != null) {
            channelDocument = CloneHelper.queryInSaveSession(()->Document.findById(request.getChannelParentDocumentId()));
        }
        Document rootDocument = primaryDoc.getRootDocument();
        CloneHelper.setSourceDocument(rootDocument);
        
        User requestor = request.getRequestor();
        String targetNodeGuid = CloneHelper.queryInSaveSession(()->Node.getCurrentNode().getGuid());

        boolean isRoot = primaryDoc.isRootDocument();

//        Document newDocument = (Document) primaryDoc.clone(request.getAction() == ACTION_CHECK_OUT_DOCUMENT ? new Timestamp(System.currentTimeMillis()) : null, primaryLayoutDoc);
        Timestamp checkoutTimestamp = request.getAction() == ACTION_CHECK_OUT_DOCUMENT ? new Timestamp(System.currentTimeMillis()) : null;
        Document clonedPrimaryLayoutDoc = primaryLayoutDoc;
        Document clonedChannelDoc = channelDocument;
        Document newDocument = CloneHelper.clone(primaryDoc, pd -> {
            return pd.clone(checkoutTimestamp, clonedPrimaryLayoutDoc, clonedChannelDoc, request.getCloneDataSourceAssociation(), messagepointLocales);
        });

        if(CloneHelper.getTargetDocument() == null) {
            Document clonedRootDocument = CloneHelper.queryInSaveSession(()->newDocument.getRootDocument());
            if(clonedRootDocument != null) {
                CloneHelper.setTargetDocument(clonedRootDocument);
            }
        }
        
        newDocument.setEnabled(request.isEnableByDefault());
        newDocument.setName(request.getName());

        newDocument.setUpdated(new Date());
        newDocument.setUpdatedBy(requestor.getId());
        newDocument.setCreated(new Date());
        newDocument.setCreatedBy(requestor.getId());

        if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() != null) {
            String sourceNodeGuid = Node.getCurrentNode().getGuid();
            String primaryDocGuid = primaryDoc.getGuid();
            CloneHelper.execInSaveSession(()->{
                newDocument.setExchangeInstanceGuid(sourceNodeGuid);
                newDocument.setExchangeTouchpointGuid(primaryDocGuid);
                newDocument.setExchangeUpdatedTimestamp(newDocument.getCheckoutTimestamp());
                newDocument.setCheckoutTimestamp(null);

                newDocument.setCustomerReportingVariableA(null);
                newDocument.setCustomerReportingVariableB(null);
/*
                if(newDocument.getDataFiles() != null) {
                    newDocument.getDataFiles().clear();
                }
*/
                if(isRoot) {
                    if (newDocument.getExtReportingDataVariables() != null) {
                        newDocument.getExtReportingDataVariables().forEach(o -> o.save());
                    }
                }

                if (newDocument.getAttachments() != null) {
                    newDocument.getAttachments().forEach(o -> o.save());
                }

                if(newDocument.getDefaultTextStyle() != null) {
                    newDocument.getDefaultTextStyle().save();
                }

                if(newDocument.getDefaultParagraphStyle() != null) {
                    newDocument.getDefaultParagraphStyle().save();
                }

                if(newDocument.getDefaultListStyle() != null) {
                    newDocument.getDefaultListStyle().save();
                }

                if(newDocument.getInsertParameterGroup() != null) {
                    newDocument.getInsertParameterGroup().save();
                }

                if(newDocument.getDefaultRateScheduleCollection() != null) {
                    newDocument.getDefaultRateScheduleCollection().save();
                }

                if(newDocument.getSelectionParameterGroup() != null) {
                    newDocument.getSelectionParameterGroup().save();
                }

                if(newDocument.getLanguageParameterGroup() != null) {
                    newDocument.getLanguageParameterGroup().save();
                }

                if(newDocument.getConnectorConfiguration() != null) {
                    newDocument.getConnectorConfiguration().save();
                }

                if(isRoot) {
                    if (newDocument.getTouchpointLanguages() != null) {
                        newDocument.getTouchpointLanguages().forEach(o -> o.save());
                    }
                }

                if(newDocument.getCommunicationZoneMarkerStyle() != null) {
                    newDocument.getCommunicationZoneMarkerStyle().save();
                }

                if(newDocument.getCommunicationMultiRecipientIdentifier() != null) {
                    newDocument.getCommunicationMultiRecipientIdentifier().save();
                }

                if(newDocument.getSegmentationAnalysisResource() != null) {
                    newDocument.getSegmentationAnalysisResource().save();
                }

                if(isRoot) {
                    if (newDocument.getTouchpointMetadataFormDefinition() != null) {
                        newDocument.getTouchpointMetadataFormDefinition().save();
                    }

                    if (newDocument.getVariantMetadataFormDefinition() != null) {
                        newDocument.getVariantMetadataFormDefinition().save();
                    }
                }

                CloneHelper.saveAllClonedObjects();
            });
        }
        String sourceNodeGuid = Node.getCurrentNode().getGuid();

        Map<ConnectorConfiguration, ConnectorConfiguration> clonedChannelConfigurationMap = new HashMap<>();
        clonedChannelConfigurationMap.put(newDocument.getConnectorConfiguration(), primaryDoc.getConnectorConfiguration());

        SyncTouchpointUtil.updateModelSyncHistory(SyncHistory.SYNC_TYPE_CLONING,
                SyncObjectType.ID_CHANNEL_CONFIGURATION,
                clonedChannelConfigurationMap,
                primaryDoc.getId(),
                newDocument.getId(),
                requestor.getId(),
                sourceNodeGuid, targetNodeGuid,
                false);

        CloneHelper.execInSaveSession(()->{
            if (newDocument.getTpTargeting() != null) {
                newDocument.getTpTargeting().save();
            }

            if (newDocument.getCommunicationCompositionResultsWebService() != null)
                newDocument.getCommunicationCompositionResultsWebService().save();
            if (newDocument.getCommunicationDataFeedWebService() != null)
                newDocument.getCommunicationDataFeedWebService().save();
            if (newDocument.getCommunicationDriverWebService() != null)
                newDocument.getCommunicationDriverWebService().save();
        });
/*
        for(DocumentSection section : CloneHelper.queryInSaveSession(()->newDocument.getDocumentSections())) {
            String imageLocation = section.getImageLocation();
            if(imageLocation == null) continue;
            
            File srcFile = new File( imageLocation );
            if(srcFile == null || (!srcFile.exists()) || (!srcFile.isFile())) {
                section.setImageLocation(null);
                section.setImageName(null);
                section.setOriginalImageFile(null);
                section.save();
                continue;
            }
            
            Resource fromFile = new UpdateTouchpointLayoutService().new FileResourceLoader().getResourceByPath(imageLocation);
            String fileName = fromFile.getFilename();
            
            long databaseFileId = -1L;
            
            if ( fileName.indexOf("_DBF") != -1 ) {
                String[] filenameParts = fileName.split("_DBF");
                long sourceDatabaseFileId = Long.valueOf(filenameParts[1].split("[.]")[0]);
                if ( sourceDatabaseFileId > 0 ) {
                    DatabaseFile sourceDatabaseFile = DatabaseFile.findById(sourceDatabaseFileId);
                    if(sourceDatabaseFile != null) {
                        DatabaseFile clonedDatabaseFile = CloneHelper.assign(sourceDatabaseFile);
                        if(clonedDatabaseFile != null) {
                            databaseFileId = clonedDatabaseFile.getId();
                            section.setOriginalImageFile( clonedDatabaseFile );
                        }
                    }
                }
            }
            
            fileName = "selectedImage_" + section.getId() + ( databaseFileId > 0 ? "_DBF" + databaseFileId : "") + "."+ PDFToImage.IMAGE_PNG;
            
            section.setImageName( fileName );
            section.setImageLocation( CloneHelper.queryInSaveSession(()->section.getLocalPath() ) );

            section.save();
            // Copy the new image file to image folder

            File destFile = new File( section.getImageLocation() );
            if ( !destFile.getParentFile().exists() )
                destFile.getParentFile().mkdirs();

            FileUtil.copy(srcFile, destFile);
        }
*/

        if(primaryDoc.isWebTouchpoint() || primaryDoc.isEmailTouchpoint()) {
            String packagePath = EmailTemplateUtils.getFilerootTemplatesBasePath(primaryDoc.getId());
            String clonePackagePath = CloneHelper.queryInSaveSession(()->PathUtil.normalize(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_EmailTemplateDir) + newDocument.getId(), false));
            if (EmailTemplateUtils.getTemplatePackageExists(primaryDoc.getId())
                    || EmailTemplateUtils.getParsedTemplateExists(primaryDoc.getId())) {
                try {
                    FileUtil.copyDir(new File(packagePath), new File(clonePackagePath));
                } catch (Exception e) {
                }
            }
        }

        SyncTouchpointUtil.updateDocumentSettingsModelSyncHistory(SyncHistory.SYNC_TYPE_CLONING, 1, primaryDoc, newDocument, requestor.getId(), sourceNodeGuid, targetNodeGuid, false);

        if (primaryDoc.isAlternate()) {
            CloneHelper.execInSaveSession(()->HibernateUtil.getManager().getSession().flush());
            return newDocument;
        }

        if(primaryDoc.isRootDocument()) {
            List<Zone> zones = Zone.findPlaceholdersByDocumentId(primaryDoc);
            if (zones != null && !zones.isEmpty()) {
                List<Zone> clonedZone = CloneHelper.clone(zones, z -> z.clone(checkoutTimestamp, null, newDocument, null));
                CloneHelper.execInSaveSession(() -> {
                    newDocument.getZones().addAll(clonedZone);
                });
            }
        }
       	
        if (primaryDoc.isEmailTouchpoint() || primaryDoc.isWebTouchpoint()) {
/*
            if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
                Set<TemplateVariant> templateVariants = newDocument.getTemplateVariants();
                if (templateVariants != null && templateVariants.size() > 0) {
                    for (TemplateVariant tv : templateVariants) {
                        if (tv.getOriginObject() != null) {
                            TemplateVariant tvOrigin = (TemplateVariant) tv.getOriginObject();
                            File cloneTVDir = new File(EmailTemplateUtils.getFilerootTemplateVariantsBasePath(newDocument.getId()) + EmailTemplateUtils.PATH_SEPARATOR + tvOrigin.getGuid());
                            if (cloneTVDir.exists()) {
                                cloneTVDir.renameTo(new File(EmailTemplateUtils.getFilerootTemplateVariantsBasePath(newDocument.getId()) + EmailTemplateUtils.PATH_SEPARATOR + tv.getGuid()));
                            } else {
                                try {
                                    FileUtil.copyDir(new File(EmailTemplateUtils.getFilerootTemplateVariantsBasePath(primaryDoc.getId()) + EmailTemplateUtils.PATH_SEPARATOR + tvOrigin.getGuid()), new File(EmailTemplateUtils.getFilerootTemplateVariantsBasePath(newDocument.getId()) + EmailTemplateUtils.PATH_SEPARATOR + tv.getGuid()));
                                } catch (Exception e) {
                                }
                            }
                        }
                    }
                }
            } else {
                Set<TemplateVariant> originTemplateVariants = primaryDoc.getTemplateVariants();
                if (originTemplateVariants != null && originTemplateVariants.size() > 0) {
                    for (TemplateVariant tvOrigin : originTemplateVariants) {
                        TemplateVariant tv = CloneHelper.assignAlreadyClonedObject(tvOrigin);
                        String originTVDirPath = EmailTemplateUtils.getFilerootTemplateVariantsBasePath(primaryDoc.getId()) + EmailTemplateUtils.PATH_SEPARATOR + tvOrigin.getGuid();
                        String cloneTVDirPathWithOriginGuid = CloneHelper.queryInSaveSession(()->EmailTemplateUtils.getFilerootTemplateVariantsBasePath(newDocument.getId()) + EmailTemplateUtils.PATH_SEPARATOR + tvOrigin.getGuid());
                        File cloneTVDir = new File(cloneTVDirPathWithOriginGuid);
                        String cloneTVDirPathWithNewGuid = CloneHelper.queryInSaveSession(()->EmailTemplateUtils.getFilerootTemplateVariantsBasePath(newDocument.getId()) + EmailTemplateUtils.PATH_SEPARATOR + tv.getGuid());
                        if (cloneTVDir.exists()) {
                            cloneTVDir.renameTo(new File(cloneTVDirPathWithNewGuid));
                        } else {
                            try {
                                FileUtil.copyDir(new File(originTVDirPath), new File(cloneTVDirPathWithNewGuid));
                            } catch (Exception e) {
                            }
                        }
                    }
                }
            }
*/            

            CloneHelper.execInSaveSession(()-> {
/*
                Set<TemplateVariant> templateVariants = newDocument.getTemplateVariants();
                if (templateVariants != null && templateVariants.size() > 0) {
                    for (TemplateVariant tv : templateVariants) {
                    	tv.save();
                    }
                }

                Set<TemplateModifier> templateModifiers = newDocument.getMasterTemplateModifiers();
                if (templateModifiers != null && templateModifiers.size() > 0) {
                    for (TemplateModifier tm : templateModifiers) {
                        if(tm.getComplexValue() != null) {
                        	tm.getComplexValue().save();
                        }
                        tm.save();
                    }
                }

 */
            });
        }

       	CloneHelper.execInSaveSession(()->{
               if(newDocument.isRootDocument()) {
                   for (TouchpointSelection touchpointSelection : newDocument.getTouchpointSelections()) {
                       touchpointSelection.save();
                   }
               }
       	});

        CloneHelper.execInSaveSession(()->{
           	newDocument.save();
        });

        // Duplicate text and paragraph style customization
        //
        List<TextStyleCustomization> textStyleCustomizations = TextStyleCustomization.findByDocument(primaryDoc);
        for (TextStyleCustomization textStyleCustomization : textStyleCustomizations) {
            TextStyle masterTextStyle = CloneHelper.assign(textStyleCustomization.getMasterTextStyle());
            TextStyleCustomization clonedTextStyleCustomization = CloneHelper.clone(textStyleCustomization, o->o.clone(masterTextStyle));
            CloneHelper.execInSaveSession(()->{
                masterTextStyle.getTextStyleCustomizations().put(newDocument, clonedTextStyleCustomization);
                masterTextStyle.save();
                clonedTextStyleCustomization.save();
            });
        }

        List<ListStyleCustomization> listStyleCustomizations = ListStyleCustomization.findByDocument(primaryDoc);
        for (ListStyleCustomization listStyleCustomization : listStyleCustomizations) {
            ListStyle masterListStyle = CloneHelper.assign(listStyleCustomization.getMasterListStyle());
            ListStyleCustomization clonedListStyleCustomization = CloneHelper.clone(listStyleCustomization, o->o.clone(masterListStyle));
            CloneHelper.execInSaveSession(()->{
                masterListStyle.getListStyleCustomizations().put(newDocument, clonedListStyleCustomization);
                masterListStyle.save();
                clonedListStyleCustomization.save();
            });
        }

        List<ParagraphStyleCustomization> paragraphStyleCustomizations = ParagraphStyleCustomization.findByDocument(primaryDoc);
        for (ParagraphStyleCustomization paragraphStyleCustomization : paragraphStyleCustomizations) {
            ParagraphStyle masterParagraphStyle = CloneHelper.assign(paragraphStyleCustomization.getMasterParagraphStyle());
            ParagraphStyleCustomization clonedParagraphStyleCustomization = CloneHelper.clone(paragraphStyleCustomization, o->o.clone(masterParagraphStyle));
            CloneHelper.execInSaveSession(()->{
                masterParagraphStyle.getParagraphStyleCustomizations().put(newDocument, clonedParagraphStyleCustomization);
                masterParagraphStyle.save();
                clonedParagraphStyleCustomization.save();
            });
        }

        CloneHelper.execInSaveSession(()->{
            HibernateUtil.getManager().getSession().flush();
        });


        // Duplicate composition file packages
        //
        Long defaultCompositionFileSetId = primaryDoc.getCompositionFileSet() == null ? null : primaryDoc.getCompositionFileSet().getId();
        List<Long> compisitionFileSetIds = new ArrayList<>();
        {
            List<CompositionFileSet> compositionFileSets = CompositionFileSet.findByDocumentId(primaryDoc.getId());
            if (compositionFileSets != null && !compositionFileSets.isEmpty()) {
                for (CompositionFileSet c : compositionFileSets) {
                    compisitionFileSetIds.add(c.getId());
                    if(defaultCompositionFileSetId == null || c.getId() != defaultCompositionFileSetId.longValue()) {
                        HibernateUtil.getManager().getSession().evict(c);
                        for (DatabaseFile df : c.getAdditionalFiles()) {
                            HibernateUtil.getManager().getSession().evict(df);
                        }
                    }
                }
            }
        }

        if (compisitionFileSetIds != null && !compisitionFileSetIds.isEmpty()) {
            for (Long compositionFileSetId : compisitionFileSetIds) {
                CompositionFileSet compositionFileSet = CompositionFileSet.findById(compositionFileSetId);
                CompositionFileSet clonedCompositionFileSet = CloneHelper.clone(compositionFileSet, o->o.clone(newDocument));
                CloneHelper.execInSaveSession(()->{
                    clonedCompositionFileSet.save();
                });

                Map<CompositionFileSet, CompositionFileSet> clonedCompositionFileSetMap = new HashMap<>();
                clonedCompositionFileSetMap.put(clonedCompositionFileSet, compositionFileSet);
                SyncTouchpointUtil.updateModelSyncHistory(SyncHistory.SYNC_TYPE_CLONING,
                    SyncObjectType.ID_COMPOSITION_PACKAGE,
                    clonedCompositionFileSetMap,
                    primaryDoc.getId(),
                    newDocument.getId(),
                    requestor.getId(),
                    sourceNodeGuid, targetNodeGuid,
                    false);
            }
        }

        if(defaultCompositionFileSetId != null) {
            CompositionFileSet clonedDefaultCompositionFileSet = CloneHelper.queryInSaveSession(newDocument::getCompositionFileSet);
            if (clonedDefaultCompositionFileSet != null && defaultCompositionFileSetId != null) {
                CompositionFileSet sourceCompositionFileSet = primaryDoc.getCompositionFileSet();
                Map<CompositionFileSet, CompositionFileSet> clonedCompositionFileSetMap = new HashMap<>();
                clonedCompositionFileSetMap.put(clonedDefaultCompositionFileSet, sourceCompositionFileSet);
                SyncTouchpointUtil.updateModelSyncHistory(SyncHistory.SYNC_TYPE_CLONING,
                    SyncObjectType.ID_COMPOSITION_PACKAGE,
                    clonedCompositionFileSetMap,
                    primaryDoc.getId(),
                    newDocument.getId(),
                    requestor.getId(),
                    sourceNodeGuid, targetNodeGuid,
                    false);
            }
        }

        CloneHelper.execInSaveSession(()->{
            HibernateUtil.getManager().getSession().flush();
        });

        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
            // Add the newDocument touchpoint to other message workflows which
            // has associations with the primaryDoc touchpoint.
            List<ConfigurableWorkflow> messageWorkflows = ConfigurableWorkflow.findByDocumentModelAndUsageTypes(primaryDoc.getId(), ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW, WorkflowUsageType.ID_USAGE_TYPE_MESSAGE);
            for (ConfigurableWorkflow messageWorkflow : messageWorkflows) {
                if (primaryDoc.getMessageWorkflow() == null || messageWorkflow.getId() != primaryDoc.getMessageWorkflow().getId()) {
                    Set<Document> documents = messageWorkflow.getWorkflowLibraryDocuments();
                    if (documents.contains(primaryDoc)) {
                        messageWorkflow.getWorkflowLibraryDocuments().add(newDocument);
                        messageWorkflow.save();
                    }
                }
            }
            
            List<ConfigurableWorkflow> connectedWorkflows = ConfigurableWorkflow.findByDocumentModelAndUsageTypes(primaryDoc.getId(), ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW, WorkflowUsageType.ID_USAGE_TYPE_CONNECTED);
            for (ConfigurableWorkflow connectedWorkflow : connectedWorkflows) {
                if (primaryDoc.getConnectedWorkflow() == null || connectedWorkflow.getId() != primaryDoc.getConnectedWorkflow().getId()) {
                    Set<Document> documents = connectedWorkflow.getWorkflowLibraryDocuments();
                    if (documents.contains(primaryDoc)) {
                        connectedWorkflow.getWorkflowLibraryDocuments().add(newDocument);
                        connectedWorkflow.save();
                    }
                }
            }
        }
        
        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
            Set<TouchpointSelection> touchpointSelections = newDocument.getTouchpointSelections();
            for (TouchpointSelection touchpointSelection : touchpointSelections) {
                // Clone the latest active workflow action if exists
                ConfigurableWorkflowAction latestActiveAction = ConfigurableWorkflowAction.findLatestActiveActionByModel((TouchpointSelection) touchpointSelection.getOriginObject());
                if (latestActiveAction != null) {
                    ConfigurableWorkflowAction latestActiveActionClone = latestActiveAction.clone(touchpointSelection);
                    latestActiveActionClone.save();
                }
            }
        }

        CloneHelper.execInSaveSession(()->{
            if(newDocument.getCommunicationsDataResource() != null) {
                if(newDocument.getCommunicationsDataResource().getId() == 0) {
                    newDocument.getCommunicationsDataResource().save();
                }
            }
        });

        if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() != null && request.isIncludeAllVariables()) 
        {
            Set<DataElementVariable> allExpressionVariables = new HashSet<>();

            Set<DataElementVariable> allVariables = new HashSet<>(DataElementVariable.findAllSystemVariables());
        	
        	for (DataElementVariable variable : DataElementVariable.findAllVisibleForDocument(primaryDoc))
        	{
        		if (variable.isExpressionVariable())
        			allExpressionVariables.add(variable);
        		else
        			allVariables.add(variable);
        	}
        	
	        for (DataElementVariable variable : allVariables)
	        {
                DataElementVariable targetVariable = CloneHelper.assign(variable,
	    		        o->{
	    		            DataElementVariable clone = (DataElementVariable) o.clone();
	                        clone.save();
	    		            return clone;
	    		        }, true);

                SyncTouchpointUtil.SyncVariableUsage(variable, targetVariable);
	        }

	        for (DataElementVariable variable : allExpressionVariables)
	        {
                DataElementVariable targetVariable = CloneHelper.assign(variable,
	    		        o->{
	    		            DataElementVariable clone = (DataElementVariable) o.clone();
	                        clone.save();
	    		            return clone;
	    		        }, true);

                SyncTouchpointUtil.SyncVariableUsage(variable, targetVariable);
	        }
        }

        CloneHelper.execInSaveSession(()->{
            HibernateUtil.getManager().getSession().flush();
        });

        return newDocument;
    }

    private void associateClonedTouchpointToSharedObjects(TouchpointVersioningServiceRequest request, Document newDocument) {
        Document primaryDoc = Document.findById(request.getDocumentId());

        // Associate touchpoint to the target groups
        //
        List<TargetGroup> targetGroups = TargetGroup.findAllByDocument(primaryDoc, null, false, 0, false);
        if (targetGroups != null && !targetGroups.isEmpty()) {
            for (TargetGroup targetGroup : targetGroups) {
            	TargetGroup targetTargetGroup = CloneHelper.assign(targetGroup);
                CloneHelper.execInSaveSession(()->{
                	targetTargetGroup.getDocuments().add(newDocument);
                });
            }
        }

        List<Long> docIds = new ArrayList<>();
        docIds.add(request.getDocumentId());

        // Associate touchpoint to the smart texts
        //
        /** TODO
        List<Long> embeddedInstIds = EmbeddedContentInstance.findAllVisibleIdsOfMostRecentCopies(docIds, null);
        if (embeddedInstIds != null && embeddedInstIds.size() > 0) {
            List<EmbeddedContentInstance> embeddedContentInstances = EmbeddedContentInstance.findByIds(embeddedInstIds);
            if (embeddedContentInstances != null) {
                for (EmbeddedContentInstance eci : embeddedContentInstances) {
                    if (!eci.isGlobal()) {
                    	EmbeddedContentInstance targetEmbeddedContentInstance = CloneHelper.assign(eci);
                        CloneHelper.execInSaveSession(()->{
                        	targetEmbeddedContentInstance.getDocuments().add(newDocument);
                        });
                    }
                }
            }
        }
         **/

        // Associate touchpoint to the image library
        //
        /** TODO
        List<Long> contentLibraryInstIds = ContentLibraryInstance.findAllVisibleIdsOfMostRecentCopies(docIds);
        if (contentLibraryInstIds != null && contentLibraryInstIds.size() > 0) {
            List<ContentLibraryInstance> contentLibraryInstances = ContentLibraryInstance.findByIds(contentLibraryInstIds);
            if (contentLibraryInstances != null) {
                for (ContentLibraryInstance cli : contentLibraryInstances) {
                    if (!cli.isGlobal()) {
                    	ContentLibraryInstance targetContentLibraryInstance = CloneHelper.assign(cli);
                    	CloneHelper.execInSaveSession(()->{
                    		targetContentLibraryInstance.getDocuments().add(newDocument);
                    	});
                    }
                }
            }
        }
         **/

        // Associate touchpoint to the lookup tables
        //
        List<Long> lookupTableInstIds = LookupTableInstance.findAllVisibleIdsOfMostRecentCopies(docIds, null);
        if (lookupTableInstIds != null && !lookupTableInstIds.isEmpty()) {
            List<LookupTableInstance> lookupTableInstances = LookupTableInstance.findByIds(lookupTableInstIds);
            if (lookupTableInstances != null) {
                for (LookupTableInstance lti : lookupTableInstances) {
                    if (!lti.isFullyVisible()) {
                    	LookupTableInstance targetLookupTableInstance = CloneHelper.assign(lti);
                    	CloneHelper.execInSaveSession(()->{
                    		targetLookupTableInstance.getDocuments().add(newDocument);
                    	});
                    }
                }
            }
        }
        
        // Associate touchpoint to variables (scripted use assignment)
        //
        List<DataElementVariable> variables = DataElementVariable.findAllByDocumentAssignment(docIds);
        if (variables != null && !variables.isEmpty()) {
            for (DataElementVariable variable : variables) {
            	DataElementVariable targetDataElementVariable = CloneHelper.assign(variable);
            	CloneHelper.execInSaveSession(()->{
            		targetDataElementVariable.getDocuments().add(newDocument);
            	});
            }
        }
        CloneHelper.execInSaveSession(()->{
            HibernateUtil.getManager().getSession().flush();
        });
    }

    public void validate(ServiceExecutionContext context) {
        // TODO Auto-generated method stub
    }

    public static ServiceExecutionContext createContextForDuplicateDocument(Long documentId, Long primaryLayoutDocumentId, Long channelParentDocumentId, boolean enableByDefault, String name, User requestor, String sourceSchema, boolean includeAllVariables, boolean cloneDataSourceAssociation, Set<Long> languagesForSync) {
        return createContext(ACTION_DUPLICATE_DOCUMENT, documentId, primaryLayoutDocumentId, channelParentDocumentId, null, name, true, enableByDefault, false, null, null, requestor, sourceSchema, includeAllVariables, cloneDataSourceAssociation, languagesForSync);
    }

    public static ServiceExecutionContext createContextForCheckOutDocument(Long documentId, Long primaryLayoutDocumentId, Long channelParentDocumentId, boolean enableByDefault, String name, User requestor, String sourceSchema, boolean includeAllVariables, Set<Long> languagesForSync) {
        return createContext(ACTION_CHECK_OUT_DOCUMENT, documentId, primaryLayoutDocumentId, channelParentDocumentId, null, name, true, enableByDefault, false, null, null, requestor, sourceSchema, includeAllVariables, true, languagesForSync);
    }

    public static ServiceExecutionContext createContextForDeleteDocument(Long documentId, User requestor) {
        return createContext(ACTION_DELETE_DOCUMENT, documentId, null, null, null, null, true, false, false, null, null, requestor, null, false, true, null);
    }

    public static ServiceExecutionContext createContextForSyncDocument(Long documentId, Long otherDocumentId, boolean syncUpdateDocument, boolean hideUntilNextChange, Multimap<Long, Map<Long, Long>> syncObjectMap, Set<Integer> documentSettingsToSync, User requestor, String otherSchema, Set<Long> languagesForSync) {
        return createContext(ACTION_SYNC_DOCUMENT, documentId, null, null, otherDocumentId, null, syncUpdateDocument,true, hideUntilNextChange, syncObjectMap, documentSettingsToSync, requestor, otherSchema, false, true, languagesForSync);
    }

    public static ServiceExecutionContext createContext(int actionId, Long documentId, Long primaryLayoutDocumentId, Long channelParentDocumentId,  Long otherDocumentId, String name, boolean syncUpdateDocument, boolean enableByDefault, boolean hideUntilNextChange, Multimap<Long, Map<Long, Long>> syncObjectMap, Set<Integer> documentSettingsToSync, User requestor, String sourceSchema, boolean includeAllVariables, boolean cloneDataSourceAssociation, Set<Long> languagesForSync) {

        SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
        context.setServiceName(SERVICE_NAME);

        TouchpointVersioningServiceRequest request = new TouchpointVersioningServiceRequest();
        context.setRequest(request);

        request.setAction(actionId);
        request.setDocumentId(documentId);
        request.setPrimaryLayoutDocumentId(primaryLayoutDocumentId);
        request.setChannelParentDocumentId(channelParentDocumentId);
        request.setOtherDocumentId(otherDocumentId);
        request.setName(name);
        request.setSyncUpdateDocument(syncUpdateDocument);
        request.setEnableByDefault(enableByDefault);
        request.setHideUntilNextChange(hideUntilNextChange);
        request.setLanguagesForSync(languagesForSync);
        request.setSyncObjectMap(syncObjectMap);
        request.setRequestor(requestor);
        request.setSourceSchema(sourceSchema);
        request.setIncludeAllVariables(includeAllVariables);
        request.setCloneDataSourceAssociation(cloneDataSourceAssociation);
        request.setDocumentSettingsToSync(documentSettingsToSync);
        SimpleServiceResponse serviceResp = new SimpleServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

}
