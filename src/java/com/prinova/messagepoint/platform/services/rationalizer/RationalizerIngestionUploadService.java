package com.prinova.messagepoint.platform.services.rationalizer;

import ai.mpr.marcie.ingestion.IngestionException;
import ai.mpr.marcie.ingestion.manifest.ManifestSupportedExtensionsEnum;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.dto.RationalizerUploadIngestionDto;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.RationalizerUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static com.prinova.messagepoint.util.ApplicationUtil.getMessage;
import static com.prinova.messagepoint.util.RationalizerUtil.PATH_SEPARATOR;

public class RationalizerIngestionUploadService {
    public static final String SERVICE_NAME = "rationalizer.RationalizerIngestionUploadService";
    private static final String MACOS_SYSTEM_FILE_PREFIX = "._";
    private static final String ZIP_EXTENSION = ".zip";
    public static final String MANIFEST = "manifest";
    private static Log log;
    private static final int ACTION_BULK_UPLOAD = 1;
    private String manifestPath = StringUtils.EMPTY;

    public void execute(ServiceExecutionContext context) {
        RationalizerIngestionUploadServiceRequest request = (RationalizerIngestionUploadServiceRequest) context.getRequest();
        log = LogUtil.getLog(request.getRationalizerApplication().getGuid());
        long applicationId = request.getRationalizerApplication().getId();
        String originalFilename = request.getMultipartFile().getOriginalFilename();
        RationalizerUploadIngestionDto rationalizerUploadIngestionDto = request.getRationalizerUploadIngestionDto();
        Map<String, List<String>> uploadedFilesMap = rationalizerUploadIngestionDto.getUploadedFilesMap();
        Integer uploadedFilesNo = rationalizerUploadIngestionDto.getUploadedIngestionDocsNo();

        List<String> filesToIngestList = new LinkedList<>();
        String zipFileName = originalFilename;
        List<File> extractedFiles =  new ArrayList<>();
        try {
            if (request.getAction() == ACTION_BULK_UPLOAD) {
                if (StringUtils.endsWithIgnoreCase(originalFilename, ZIP_EXTENSION)) {
                    log.info("Start unzip and validate file " + originalFilename);

                    int noFilesInZip = constructIngestList(request, applicationId, rationalizerUploadIngestionDto, filesToIngestList, extractedFiles);
                    uploadedFilesNo = uploadedFilesNo + noFilesInZip;
                    rationalizerUploadIngestionDto.setUploadedIngestionDocsNo(uploadedFilesNo);
                    validateManifest(request.getMultipartFile(), rationalizerUploadIngestionDto.isIngestIfNotFoundInManifest());

                    int i = 1;

                    while (uploadedFilesMap.containsKey(zipFileName)) {
                        //rename
                        String outFileNameWithOutExt = FilenameUtils.removeExtension(originalFilename);
                        zipFileName = new File(outFileNameWithOutExt + "_(" + i + ")." + FilenameUtils.getExtension(originalFilename)).getName();
                        i++;
                    }

                    validateIngestionFilesList(request, zipFileName, uploadedFilesMap, filesToIngestList);

                    uploadedFilesMap.put(zipFileName, filesToIngestList);
                }
                log.info("End unzip and validate file " + originalFilename);
                context.getResponse().setResultValueBean(-1);
            }
        }catch (Exception e) {
            uploadedFilesMap.put(zipFileName, new ArrayList<>());
            rationalizerUploadIngestionDto.setUploadedIngestionDocsNo(uploadedFilesNo);
            log.error(e.getMessage());
            this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, e.getMessage(), context.getLocale());
            if (CollectionUtils.isNotEmpty(extractedFiles)) {
                for (File fileToDelete : extractedFiles) {
                    if(fileToDelete.exists()) {
                        fileToDelete.delete();
                    }
                }
            }
        }
    }

    private int constructIngestList(RationalizerIngestionUploadServiceRequest request, long applicationId, RationalizerUploadIngestionDto rationalizerUploadIngestionDto, List<String> filesToIngestList, List<File> extractedFiles) throws IOException {
        int countFiles = extractZipFromInputStream(extractedFiles, request.getRationalizerApplication(), request.getMultipartFile(),
                RationalizerUtil.getFilerootTemplatesBasePath(applicationId), rationalizerUploadIngestionDto.getUploadFilePolicyValue());

        for (File extractedFile : extractedFiles) {
            String fileName = extractedFile.getName();
            if (!fileName.startsWith(MACOS_SYSTEM_FILE_PREFIX)) {
                filesToIngestList.add(extractedFile.getPath());
                if(extractedFile.getPath().endsWith(".xlsx") || extractedFile.getPath().endsWith("txt")) {
                    countFiles--;
                }
            } else {
                   countFiles--;

            }
        }
        return countFiles;
    }

    private void validateManifest(MultipartFile multiPartFile, boolean ingestIfNotFoundInManifest) throws IOException {
        InputStream zipInputStream = multiPartFile.getInputStream();
        ZipInputStream inputStream = new ZipInputStream(zipInputStream);
        ZipEntry zipEntry = inputStream.getNextEntry();
        boolean foundManifestExtension = false;
        boolean foundCorrectManifestName = false;
        manifestPath = StringUtils.EMPTY;
        int noOfDirectoriesInZip = 0;
        String firstLevelDirectoryName = StringUtils.EMPTY;

        int noOfManifests = 0;
        while (zipEntry != null) {
            String zipEntryName = zipEntry.getName();
            if (isFirstLevelDirectory(zipEntry)) {
                noOfDirectoriesInZip++;
                firstLevelDirectoryName = zipEntryName;
            } else if (isValidZipEntryName(zipEntryName)) {
                if(new File(zipEntryName).getName().contains(MANIFEST)) {
                    manifestPath = zipEntryName;
                }
                String fileExtension = FileUtil.getFileExtension(zipEntryName);
                if (ManifestSupportedExtensionsEnum.EXCEL.getValue().equals(fileExtension) || ManifestSupportedExtensionsEnum.TSV.getValue().equals(fileExtension)) {
                    foundManifestExtension = true;
                    if(new File(zipEntryName).getName().contains(MANIFEST)) {
                        noOfManifests++;
                    }
                }
            }
            zipEntry = inputStream.getNextEntry();
        }
        inputStream.close();

        validateManifest(ingestIfNotFoundInManifest, foundManifestExtension, foundCorrectManifestName, noOfDirectoriesInZip, firstLevelDirectoryName, noOfManifests);
    }

    private void validateManifest(boolean ingestIfNotFoundInManifest, boolean foundManifestExtension, boolean foundCorrectManifestName, int noOfFirstLevelDirectoriesInZip, String firstLevelDirectoryName, int noOfManifests) {
        if(noOfManifests > 1) {
            throw new IngestionException(getMessage("error.message.rationalizer.ingestion.upload.manifest.more.files"));
        }

        if(StringUtils.isEmpty(manifestPath)) {
            if(foundManifestExtension) {
                throw new IngestionException(getMessage("error.message.rationalizer.ingestion.upload.manifest.wrong.name"));
            }
            if (!ingestIfNotFoundInManifest) {
                throw new IngestionException(getMessage("error.message.rationalizer.ingestion.upload.no.manifest"));
            } else {
                log.warn(getMessage("error.message.rationalizer.ingestion.upload.no.manifest.warn"));
            }
        } else {
            String manifestExtension = FileUtil.getFileExtension(manifestPath);
            String manifestNameWithoutExt = FilenameUtils.removeExtension(manifestPath);
            if (!foundManifestExtension) {
                throw new IngestionException(MessageFormat.format(getMessage("error.message.rationalizer.ingestion.upload.manifest.wrong.extension"), manifestExtension));
            } else if (noOfFirstLevelDirectoriesInZip == 1) {
                if (manifestNameWithoutExt.equals(firstLevelDirectoryName + MANIFEST) || manifestNameWithoutExt.equals(MANIFEST)) {
                    foundCorrectManifestName = true;
                } else {
                    throw new IngestionException(getMessage("error.message.rationalizer.ingestion.upload.manifest.wrong.name"));
                }
            } else if (!manifestNameWithoutExt.equals(MANIFEST)) {
                if(noOfFirstLevelDirectoriesInZip > 1) {
                    throw new IngestionException(getMessage("error.message.rationalizer.ingestion.upload.manifest.wrong.location"));
                } else if(noOfFirstLevelDirectoriesInZip == 0) {
                    throw new IngestionException(getMessage("error.message.rationalizer.ingestion.upload.manifest.wrong.name"));
                }
            } else {
                foundCorrectManifestName = true;
            }

            if (!foundCorrectManifestName ) {
                if (!ingestIfNotFoundInManifest) {
                    throw new IngestionException(getMessage("error.message.rationalizer.ingestion.upload.no.manifest"));
                } else {
                    log.warn(getMessage("error.message.rationalizer.ingestion.upload.no.manifest.warn"));
                }
            }
        }
    }

    private void validateIngestionFilesList(RationalizerIngestionUploadServiceRequest request, String originalFilename, Map<String, List<String>> uploadedFilesMap, List<String> filesToIngestList) {
        if (CollectionUtils.isEmpty(filesToIngestList) && request.getRationalizerUploadIngestionDto().getUploadFilePolicyValue() == 1) {
            if (request.getRationalizerUploadIngestionDto().getUploadFilePolicyValue() == 1) {
                uploadedFilesMap.put(originalFilename, new ArrayList<>());
                throw new IngestionException(getMessage("error.message.rationalizer.ingestion.upload.no.files"));
            }
        }
    }
    
    public static SimpleExecutionContext createContextForIngestionUpload(MultipartFile multipartFile, String metatags, RationalizerApplication rationalizerApplication,
                                                                         RationalizerUploadIngestionDto rationalizerUploadIngestionDto) {

        return createContext(ACTION_BULK_UPLOAD, multipartFile, metatags, rationalizerApplication, rationalizerUploadIngestionDto);
    }

    private static SimpleExecutionContext createContext(int action, MultipartFile multipartFile, String metatags,
                                                        RationalizerApplication rationalizerApplication,  RationalizerUploadIngestionDto rationalizerUploadIngestionDto) {

        SimpleExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        RationalizerIngestionUploadServiceRequest request = new RationalizerIngestionUploadServiceRequest();
        context.setRequest(request);

        request.setAction(action);
        request.setMultipartFile(multipartFile);
        request.setMetatags(metatags);
        request.setRationalizerApplication(rationalizerApplication);
        request.setRationalizerUploadIngestionDto(rationalizerUploadIngestionDto);

        SimpleServiceResponse serviceResp = new SimpleServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

    private SimpleServiceResponse getResponse(ServiceExecutionContext context) {
        try {
            if (context.getResponse() == null) {
                context.setResponse(SimpleServiceResponse.class.newInstance());
            }
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
        return (SimpleServiceResponse) context.getResponse();
    }

    private int extractZipFromInputStream(List<File> extractedFiles, RationalizerApplication rationalizerApplication, MultipartFile multiPartFile, String extractDirPath,
                                                 int uploadFilePolicyValue) throws IOException {
        File extractDirFile = new File(extractDirPath);
        extractDirFile.mkdirs();

        InputStream zipInputStream = multiPartFile.getInputStream();

        ZipInputStream inputStream = new ZipInputStream(zipInputStream);
        ZipEntry zipEntry = inputStream.getNextEntry();
        int countFiles = 0;
        while (zipEntry != null) {
            String zipEntryName = zipEntry.getName();
            if (isValidZipEntryName(zipEntryName)) {
                String fileName = FileUtil.getZipEntryFileName(zipEntry, extractDirPath, true);
                if (StringUtils.isNotEmpty(fileName) && !(new File(fileName)).getName().startsWith("~")) {
                    if (manifestPath.equals(fileName)) {
                        extractManifest(multiPartFile, extractDirFile, extractedFiles, inputStream, fileName);
                    } else {
                        File outputFile = new File(extractDirFile, fileName);
                        countFiles++;
                        if (uploadFilePolicyValue == 1) {
                            //ignore if exists
                            if (!outputFile.exists()) {
                                generateOutputFile(extractedFiles, inputStream, outputFile);
                            } else {
                                String filePath = outputFile.getPath().replace(extractDirPath + PATH_SEPARATOR, "");
                                countFiles--;
                                log.warn(MessageFormat.format(getMessage("error.message.rationalizer.ingestion.upload.ignore"), filePath, multiPartFile.getOriginalFilename()));
                            }
                        } else if (uploadFilePolicyValue == 3) {
                            //make unique
                            extractIfMakeUnique(multiPartFile, extractDirPath, extractDirFile, extractedFiles, inputStream, fileName, outputFile);
                        } else if (uploadFilePolicyValue == 2) {
                            //remove and replace
                            extractIfRemoveAndReplace(rationalizerApplication, multiPartFile, extractDirPath, extractedFiles, inputStream, outputFile);
                        }
                    }
                }
            }
            zipEntry = inputStream.getNextEntry();
        }
        inputStream.close();
        return countFiles;
    }

    private void extractIfMakeUnique(MultipartFile multiPartFile, String extractDirPath, File extractDirFile, List<File> zippedFiles, ZipInputStream inputStream, String fileName, File outputFile) throws IOException {
        int i = 1;
        File originalOutputFile = new File(extractDirFile, fileName);
        while (outputFile.exists()) {
            //rename
            String outFileNameWithOutExt = FilenameUtils.removeExtension(fileName);
            outputFile = new File(extractDirFile, outFileNameWithOutExt + "_(" + i + ")." + FilenameUtils.getExtension(fileName));
            i++;
        }
        if (!originalOutputFile.getAbsolutePath().equals(outputFile.getAbsolutePath())) {
            String filePath = originalOutputFile.getPath().replace(extractDirPath + PATH_SEPARATOR, "");
            String newFilePath = outputFile.getPath().replace(extractDirPath + PATH_SEPARATOR, "");
            log.warn(MessageFormat.format(getMessage("error.message.rationalizer.ingestion.upload.unique"), filePath, multiPartFile.getOriginalFilename(), newFilePath));
        }
        generateOutputFile(zippedFiles, inputStream, outputFile);
    }

    private void extractIfRemoveAndReplace(RationalizerApplication rationalizerApplication, MultipartFile multiPartFile, String extractDirPath, List<File> zippedFiles, ZipInputStream inputStream, File outputFile) throws IOException {
        if (outputFile.exists()) {
            String filePath = outputFile.getPath().replace(extractDirPath + PATH_SEPARATOR, "");
            log.warn(MessageFormat.format(getMessage("error.message.rationalizer.ingestion.upload.replace"), filePath, multiPartFile.getOriginalFilename()));

            //delete RationalizerDocument
            RationalizerDocument doc = RationalizerDocument.findByOriginalFilePath(rationalizerApplication, outputFile.getPath());
            if (doc != null) {
                ServiceExecutionContext context = BulkDeleteRationalizerDocumentsService.createContext(Collections.singletonList(doc));
                Service deleteModelService = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteRationalizerDocumentsService.SERVICE_NAME, BulkDeleteRationalizerDocumentsService.class);
                deleteModelService.execute(context);
            }

        }
        generateOutputFile(zippedFiles, inputStream, outputFile);
    }

    private void extractManifest(MultipartFile multiPartFile, File extractDirFile, List<File> zippedFiles, ZipInputStream inputStream, String fileName) throws IOException {
        String manifestName = null;
        String fileExtension = FileUtil.getFileExtension(fileName);
        if (ManifestSupportedExtensionsEnum.EXCEL.getValue().equals(fileExtension) || ManifestSupportedExtensionsEnum.TSV.getValue().equals(fileExtension)) {
            manifestName = FilenameUtils.removeExtension(multiPartFile.getOriginalFilename()) + " manifest." + fileExtension;
        }
        if (StringUtils.isNotEmpty(manifestName)) {
            String firstLevelFolderName = StringUtils.EMPTY;
            if(fileName.contains(PATH_SEPARATOR)) {
                firstLevelFolderName = fileName.substring(0, fileName.indexOf(PATH_SEPARATOR));
            }
            File outputFile = new File(extractDirFile + PATH_SEPARATOR + firstLevelFolderName, manifestName);
            generateOutputFile(zippedFiles, inputStream, outputFile);
        }
    }

    private void generateOutputFile(List<File> zippedFiles, ZipInputStream inputStream, File outputFile) throws IOException {
        try (OutputStream out = new FileOutputStream(outputFile)) {
            byte[] buf = new byte[1024];
            int len;
            while ((len = inputStream.read(buf)) > 0) {
                out.write(buf, 0, len);
            }
        }
        zippedFiles.add(outputFile);
    }
    
    private boolean isFirstLevelDirectory (ZipEntry zipEntry) {
        if(!zipEntry.isDirectory()) {
            return false;
        }
        String zipEntryName = zipEntry.getName();
        Pattern pattern = Pattern.compile(PATH_SEPARATOR);
        Matcher matcher = pattern.matcher(zipEntryName);
        if(matcher.find()){
            boolean endsWith = zipEntryName.endsWith(PATH_SEPARATOR);
            return endsWith && !matcher.find();
        }
        return false;

    }
    
    private boolean isValidZipEntryName(String zipEntryName) {
        if (StringUtils.isEmpty(zipEntryName)) {
            return false;
        }
        if (zipEntryName.contains("__MACOSX") || zipEntryName.contains(".DS_Store")) {
            return false;
        }
        return  !(zipEntryName.startsWith("~"));
    }
}