package com.prinova.messagepoint.platform.services.rationalizer;

import ai.mpr.marcie.ingestion.MetadataJsonProcessor;
import ai.mpr.marcie.ingestion.MetadataUtil;
import ai.mpr.marcie.ingestion.input.VariablesTypeEnum;
import ai.mpr.marcie.ingestion.output.model.metadata.MetadataJson;
import ai.mpr.marcie.ingestion.output.model.metadata.MetadataJsonItem;
import ai.mpr.marcie.ingestion.output.model.metadata.MetadataJsonItemProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.controller.metadata.MetadataFormEditWrapper;
import com.prinova.messagepoint.controller.rationalizer.RationalizerContentStatusFilterType;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.filters.WordCountFilter;
import com.prinova.messagepoint.model.metadata.HistoricalMetadataForm;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.metadata.MetadataFormInputValidationType;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.model.metadata.MetadataFormItemOriginTypeEnum;
import com.prinova.messagepoint.model.rationalizer.HistoricalRationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.HistoricalRationalizerSharedContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerActionTypeEnum;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplicationSyncStatus;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.tagcloud.TagCloudType;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.platform.services.tagcloud.UpdateTagCloudService;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.RationalizerUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.text.DateFormatSymbols;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static ai.mpr.marcie.ingestion.MetadataConstants.BOOLEAN;
import static ai.mpr.marcie.ingestion.MetadataConstants.CONTENT_METADATA_EMAILS;
import static ai.mpr.marcie.ingestion.MetadataConstants.CONTENT_METADATA_FAXES;
import static ai.mpr.marcie.ingestion.MetadataConstants.CONTENT_METADATA_HAS_EMAIL;
import static ai.mpr.marcie.ingestion.MetadataConstants.CONTENT_METADATA_HAS_FAX_NO;
import static ai.mpr.marcie.ingestion.MetadataConstants.CONTENT_METADATA_HAS_PHONE_NO;
import static ai.mpr.marcie.ingestion.MetadataConstants.CONTENT_METADATA_HAS_URL;
import static ai.mpr.marcie.ingestion.MetadataConstants.CONTENT_METADATA_HAS_VARIABLES;
import static ai.mpr.marcie.ingestion.MetadataConstants.CONTENT_METADATA_PHONES;
import static ai.mpr.marcie.ingestion.MetadataConstants.CONTENT_METADATA_STYLE;
import static ai.mpr.marcie.ingestion.MetadataConstants.CONTENT_METADATA_URLS;
import static ai.mpr.marcie.ingestion.MetadataConstants.CONTENT_METADATA_VARIABLES;
import static ai.mpr.marcie.ingestion.MetadataConstants.DOC_METADATA_PHONE_LOCALES;
import static ai.mpr.marcie.ingestion.MetadataConstants.FALSE;
import static ai.mpr.marcie.ingestion.MetadataConstants.LABEL_CONTAINS_VARIABLES;
import static ai.mpr.marcie.ingestion.MetadataConstants.METADATA_VALUES_SEPARATOR;
import static ai.mpr.marcie.ingestion.MetadataConstants.STRING;
import static ai.mpr.marcie.ingestion.MetadataConstants.TRUE;
import static ai.mpr.marcie.ingestion.MetadataUtil.getFormattedTextForElement;
import static com.prinova.messagepoint.mapper.RationalizerDocumentToJsonMapper.getNonNullOrEmptyString;
import static com.prinova.messagepoint.model.rationalizer.RationalizerContent.EMPTY_MARKUP_CONTENT;
import static com.prinova.messagepoint.model.rationalizer.RationalizerContent.buildElasticSearchGuids;
import static com.prinova.messagepoint.model.rationalizer.RationalizerContent.deleteRationalizerContentsFromElasticSearchDelayed;
import static com.prinova.messagepoint.model.rationalizer.RationalizerContent.sendRationalizerContentsToElasticSearchDelayed;
import static com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent.LOWER_CASE_PRIMARY_CONNECTORS_NOT_IN_SHARED_CONTENTS;
import static com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent.syncOrderToElasticSearchDelayed;
import static com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent.updateOrderMetadataForRationalizerContents;
import static com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent.sendSharedContentsToElasticSearchDelayed;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils.createElasticSearchApplicationMetadata;
import static com.prinova.messagepoint.util.RationalizerUtil.computeMarkupWithoutVarTags;
import static com.prinova.messagepoint.util.RationalizerUtil.generateHistoryForRationalizerDocumentContent;
import static com.prinova.messagepoint.util.RationalizerUtil.generateHistoryForRationalizerSharedContent;
import static com.prinova.messagepoint.util.RationalizerUtil.updateMarkupInCaseHyperlinkSplit;
import static com.prinova.messagepoint.util.RationalizerUtil.updateMarkupInCaseVariablesSplit;
import static opennlp.tools.util.StringUtil.isEmpty;

public class CreateOrUpdateRationalizerApplicationService extends AbstractService {

    public static final String SERVICE_NAME = "rationalizer.CreateOrUpdateRationalizerApplicationService";

    private static final Log log = LogUtil.getLog(CreateOrUpdateRationalizerApplicationService.class);

    private static final int ACTION_NEW_APPLICATION = 1;
    private static final int ACTION_UPDATE_APPLICATION = 2;
    private static final int ACTION_NEW_DOCUMENT = 3;
    private static final int ACTION_UPDATE_DOCUMENT = 4;
    private static final int ACTION_ADD_DOCUMENT_CONTENT = 5;
    private static final int ACTION_UPDATE_DOCUMENT_CONTENT_METADATA = 6;
    private static final int ACTION_INIT_DOCUMENT_CONTENT_ORDER = 7;
    private static final int ACTION_UPDATE_DOCUMENT_METADATA = 8;
    private static final int ACTION_SPLIT_DOCUMENT_CONTENT = 9;
    private static final int ACTION_MERGE_DOCUMENT_CONTENTS = 10;
    private static final int ACTION_UPDATE_SHARED_CONTENT_METADATA = 11;

    public static final String TEXT_CONTENT_SPLIT_REGEXP = "\\s+";
    public static final String PHONE_NUMBER_SPLIT_REGEXP = "[\\u00A0\\u000A\\u000D\\u0009,:;'\"\\[\\]\\.\\?!]|\\s+]";
    public static final AfterSplitTokenCleaner EMAIL_AFTER_SPLIT_TOKEN_CLEANER = new AfterSplitTokenCleaner(
            Pattern.compile("(.*?)[\\p{Punct}\\p{Blank}\u00a0]+"),
            1
    );

    public static final String YES_STRING = "Yes";
    public static final String NO_STRING = "No";
    public static final String TRUE_STRING = "true";
    public static final String FALSE_STRING = "false";
    public static final String EMPTY_JSON = "{}";

    public static final String MPR_VARIABLE = "mpr_variable";
    public static final String ORDER = "order";

    public static Set<String> CONTENT_POSITIONAL_PROPS_LOWERCASE = Arrays.asList(
            "Position_(bottom)",
            "Height",
            "Position_(left)",
            "Position_(right)",
            "Position_(top)",
            "Width",
            "Matches",
            "Page_number",
            "Alignment",
            "Indent_(left)",
            "Indent_(right)",
            "Style"
    ).stream()
            .map(String::toLowerCase)
            .collect(Collectors.toCollection(LinkedHashSet::new));

    public static final String CONTAINS_A_URL = constructPrimaryConnector(CONTENT_METADATA_HAS_URL);
    public static final String CONTAINS_AN_EMAIL = constructPrimaryConnector(CONTENT_METADATA_HAS_EMAIL);
    public static final String CONTAINS_A_PHONE_NUMBER = constructPrimaryConnector(CONTENT_METADATA_HAS_PHONE_NO);
    public static final String CONTAINS_A_FAX_NUMBER = constructPrimaryConnector(CONTENT_METADATA_HAS_FAX_NO);
    public static final String CONTAINS_VARIABLES = constructPrimaryConnector(CONTENT_METADATA_HAS_VARIABLES);

    public static final String URLS = constructPrimaryConnector(CONTENT_METADATA_URLS);
    public static final String EMAILS = constructPrimaryConnector(CONTENT_METADATA_EMAILS);
    public static final String PHONE_NUMBERS = constructPrimaryConnector(CONTENT_METADATA_PHONES);
    public static final String FAX_NUMBERS = constructPrimaryConnector(CONTENT_METADATA_FAXES);
    public static final String VARIABLES = constructPrimaryConnector(CONTENT_METADATA_VARIABLES);
    public static final String STYLE = constructPrimaryConnector(CONTENT_METADATA_STYLE);

    public static Map<String, String> CONTENT_AUTO_CLASSIFIED_LOWER_PRIMARY_CONNECTORS_TO_RECOMPUTE = Arrays.asList(
            CONTAINS_A_URL,
            CONTAINS_AN_EMAIL,
            CONTAINS_A_PHONE_NUMBER,
            CONTAINS_VARIABLES,
            CONTAINS_A_FAX_NUMBER,
            URLS,
            EMAILS,
            PHONE_NUMBERS,
            VARIABLES,
            FAX_NUMBERS,
            STYLE

    ).stream().collect(Collectors.toMap(String::toLowerCase, Function.identity()));

    private static Predicate<String> CONTENT_AUTO_CLASSIFIED_PROPERTIES_LOWERCASE_TO_MAINTAIN = (propertyName) ->
            propertyName != null && StringUtils.startsWithIgnoreCase(propertyName, "is_");

    private static String CONTENT_MARKUP_MERGE_SEPARATOR = " ";
    public static String CONTENT_METADATA_MERGE_SEPARATOR = ",";
    public static String CONTENT_METADATA_SPLIT_SEPARATOR = CONTENT_METADATA_MERGE_SEPARATOR;

    @SuppressWarnings("unchecked")
    public void execute(ServiceExecutionContext context) {

        BulkRationalizerActionServiceRequest request = (BulkRationalizerActionServiceRequest) context.getRequest();

        try {
            validate(context);
            if (hasValidationError(context)) {
                return;
            }
            request.computeMapsFromLists();

            if (request.getAction() == ACTION_NEW_APPLICATION) {

                RationalizerApplication newApplication = new RationalizerApplication();
                newApplication.setName(request.getName());
                newApplication.setMetatags(request.getMetatags());
                newApplication.setDescription(request.getDescription());

                // INIT PARSED METADATA: DOCUMENT
                MetadataFormDefinition parsedDocumentFormDefinition = new MetadataFormDefinition();
                parsedDocumentFormDefinition.setType(MetadataFormDefinitionType.ID_RATIONALIZER_PARSED);
                parsedDocumentFormDefinition.setName(newApplication.getName() + "- Document Metadata");
                parsedDocumentFormDefinition.save();

                newApplication.setParsedDocumentFormDefinition(parsedDocumentFormDefinition);

                // INIT PARSED METADATA: CONTENT
                MetadataFormDefinition parsedContentFormDefinition = new MetadataFormDefinition();
                parsedContentFormDefinition.setType(MetadataFormDefinitionType.ID_RATIONALIZER_PARSED);
                parsedContentFormDefinition.setName(newApplication.getName() + "- Content Metadata");
                parsedContentFormDefinition.save();

                newApplication.setParsedContentFormDefinition(parsedContentFormDefinition);
                newApplication.setAppSyncStatus(RationalizerApplicationSyncStatus.SYNCHRONIZED.getStatusValue());

                newApplication.save(true);

                Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTagCloudService.SERVICE_NAME, UpdateTagCloudService.class);
                ServiceExecutionContext tagCloudContext = UpdateTagCloudService.createContextForRationalizer(TagCloudType.ID_RATIONALIZER_APPLICATION, newApplication.getMetatags(), newApplication, false);
                if (service != null && tagCloudContext != null)
                    service.execute(tagCloudContext);

                RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
                RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(newApplication, true);

                rationalizerElasticSearchHandler.createIndex(createElasticSearchApplicationMetadata(newApplication), false);
                context.getResponse().setResultValueBean(newApplication.getId());

                // Create default WordCount Filters
                List<DashboardFilter> dashboardFilters = WordCountFilter.getRationalizerApplicationDefaultWordCountFilters(newApplication.getId());
                if(CollectionUtils.isNotEmpty(dashboardFilters)) {
                    for (DashboardFilter dashboardFilter : dashboardFilters) {
                        dashboardFilter.save(true);
                    }
                }

            } else if (request.getAction() == ACTION_UPDATE_APPLICATION) {

                // Update dashboard filters for Rationalizer application
                List<DashboardFilter> dashboardFilters = request.getDashboardFilters();
                if (CollectionUtils.isNotEmpty(dashboardFilters)) {
                    for (DashboardFilter dashboardFilter : dashboardFilters) {
                        dashboardFilter.save(false);
                    }
                }

                RationalizerApplication application = RationalizerApplication.findById(request.getRationalizerApplicationId());

                application.setName(request.getName());
                application.setMetatags(request.getMetatags());
                application.setDescription(request.getDescription());
                application.setQueryFilterFormItemDefinitions(request.getQueryFilterFormItemDefinitions());

                application.save();

                if (request.getReSync()) {

                    MigrateRationalizerDataToElasticBackgroundTask migrationTask = new MigrateRationalizerDataToElasticBackgroundTask(application, UserUtil.getPrincipalUser());
                    MessagePointRunnableUtil.startThread(migrationTask, Thread.MAX_PRIORITY);
                }

                context.getResponse().setResultValueBean(application.getId());

            } else if (request.getAction() == ACTION_NEW_DOCUMENT) {

                RationalizerApplication application = RationalizerApplication.findById(request.getRationalizerApplicationId());

                // Create document
                RationalizerDocument newDocument = new RationalizerDocument();
                newDocument.setRationalizerApplication(application);
                newDocument.setName(request.getName());
                //add unique filename
                String fileName = generateUniqueFileName(request, application);
                newDocument.setFileName(fileName);
                newDocument.setRationalizerDocumentContents(new HashSet<>());

                if (application.getParsedDocumentFormDefinition() != null) {
                    MetadataFormEditWrapper formEditWrapper = new MetadataFormEditWrapper(application.getParsedDocumentFormDefinition(), true);
                    MetadataForm metadataForm = formEditWrapper.getMetadataForm();
                    metadataForm.save();
                    newDocument.setParsedDocumentForm(metadataForm);
                }

                MetadataForm parsedDocumentForm = newDocument.getParsedDocumentForm();
                addOrUpdateMetadata( application.getParsedDocumentFormDefinition(),  parsedDocumentForm,
                        application.findAutoGeneratedDocumentFormItems(),  "Name",  newDocument.getName(),  false);

                // Add first content object
                RationalizerDocumentContent newContent = new RationalizerDocumentContent();
                newContent.setRationalizerDocument(newDocument);
                newContent.setTextContent(StringUtils.EMPTY);
                newContent.setMarkupContent(StringUtils.EMPTY);

                if (application.getParsedContentFormDefinition() != null) {
                    MetadataFormEditWrapper contentFormEditWrapper = new MetadataFormEditWrapper(application.getParsedContentFormDefinition(), true);
                    MetadataForm contentMetadataForm = contentFormEditWrapper.getMetadataForm();

                    contentMetadataForm.save();

                    newContent.setParsedContentForm(contentMetadataForm);
                }

                newContent.setOrder(1);
                newContent.updateOrderMetadataForRationalizerContent();

                newContent.save();

                newDocument.addContent(newContent);
                newDocument.save();

                RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
                RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(application, true);

                sendRationalizerContentsToElasticSearchDelayed(
                        UserUtil.getPrincipalUser(),
                        rationalizerElasticSearchHandler,
                        Collections.singletonList(newContent.getGuid()),
                        new LinkedHashMap<>() {{
                            put(newContent.getGuid(), null);
                        }},
                        new LinkedHashMap<>() {{
                            put(newContent.getGuid(), newContent.getTextContent());
                        }}
                );
                updateDocumentFieldValueForAllContentsDelayed(
                        rationalizerElasticSearchHandler,
                        newDocument,
                        "document.metadata.Name",
                        Collections.singletonList(newDocument.getName())
                );

                context.getResponse().setResultValueBean(newDocument.getId());

            } else if (request.getAction() == ACTION_UPDATE_DOCUMENT) {
                RationalizerDocument rationalizerDocument = request.getRationalizerDocument();
                RationalizerApplication rationalizerApplication = rationalizerDocument.getRationalizerApplication();
                RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
                RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication, true);

                String initialDocumentName = request.getName();
                String initialDocumentTagsString = request.getMetatags() != null ? request.getMetatags() : "";

                Map<RationalizerDocumentContent, TextAndMarkupForLastAction> changedDocumentContents = new LinkedHashMap<>();
                Map<RationalizerSharedContent, TextAndMarkupForLastAction> changedSharedContents = new LinkedHashMap<>();
                processChangedContents(
                        request,
                        changedDocumentContents,
                        changedSharedContents,
                        RationalizerActionTypeEnum.UPDATE
                );

                saveChangedDocumentContents(
                        rationalizerElasticSearchHandler,
                        changedDocumentContents
                );

                saveChangedSharedContents(
                        rationalizerElasticSearchHandler,
                        changedSharedContents
                );

                processRemovedContents(
                        request,
                        rationalizerDocument,
                        rationalizerElasticSearchHandler
                );

                rationalizerDocument.save();
                HibernateUtil.getManager().getSession().flush();

                recomputeOrderForContents(rationalizerDocument);

                Collection<RationalizerDocumentContent> shallowCopyOfContents = rationalizerDocument.shallowCopyOfContents();

                updateOrderMetadataForRationalizerContents(shallowCopyOfContents);
                syncOrderToElasticSearchDelayed(shallowCopyOfContents, rationalizerElasticSearchHandler);

                processDocumentNameDelayed(
                        rationalizerDocument,
                        initialDocumentName,
                        rationalizerElasticSearchHandler
                );

                processDocumentTagsDelayed(
                        rationalizerDocument,
                        initialDocumentTagsString,
                        rationalizerElasticSearchHandler
                );

                context.getResponse().setResultValueBean(rationalizerDocument.getId());

            } else if (request.getAction() == ACTION_MERGE_DOCUMENT_CONTENTS) {
                RationalizerDocument rationalizerDocument = request.getRationalizerDocument();
                RationalizerApplication rationalizerApplication = rationalizerDocument.getRationalizerApplication();
                RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
                RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication, true);

                String initialDocumentName = request.getName();
                String initialDocumentTagsString = request.getMetatags() != null ? request.getMetatags() : "";

                Map<RationalizerDocumentContent, TextAndMarkupForLastAction> changedDocumentContents = new LinkedHashMap<>();
                Map<RationalizerSharedContent, TextAndMarkupForLastAction> changedSharedContents = new LinkedHashMap<>();
                processChangedContents(
                        request,
                        changedDocumentContents,
                        changedSharedContents,
                        RationalizerActionTypeEnum.MERGE
                );

                saveChangedDocumentContents(
                        rationalizerElasticSearchHandler,
                        changedDocumentContents
                );

                saveChangedSharedContents(
                        rationalizerElasticSearchHandler,
                        changedSharedContents
                );

                processMergedContents(
                        request,
                        rationalizerElasticSearchHandler
                );

                HibernateUtil.getManager().getSession().flush();
                request.recomputeRationalizerContents();

                processRemovedContents(
                        request,
                        rationalizerDocument,
                        rationalizerElasticSearchHandler
                );

                HibernateUtil.getManager().getSession().flush();

                recomputeOrderForContents(rationalizerDocument);

                Collection<RationalizerDocumentContent> shallowCopyOfContents = rationalizerDocument.shallowCopyOfContents();

                updateOrderMetadataForRationalizerContents(shallowCopyOfContents);
                syncOrderToElasticSearchDelayed(shallowCopyOfContents, rationalizerElasticSearchHandler);

                processDocumentNameDelayed(
                        rationalizerDocument,
                        initialDocumentName,
                        rationalizerElasticSearchHandler
                );

                processDocumentTagsDelayed(
                        rationalizerDocument,
                        initialDocumentTagsString,
                        rationalizerElasticSearchHandler
                );

                context.getResponse().setResultValueBean(rationalizerDocument.getId());

            } else if (request.getAction() == ACTION_ADD_DOCUMENT_CONTENT) {
                RationalizerDocument rationalizerDocument = request.getRationalizerDocument();
                RationalizerApplication rationalizerApplication = rationalizerDocument.getRationalizerApplication();
                RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
                RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication, true);

                String initialDocumentName = request.getName();
                String initialDocumentTagsString = request.getMetatags() != null ? request.getMetatags() : "";

                Map<RationalizerDocumentContent, TextAndMarkupForLastAction> changedDocumentContents = new LinkedHashMap<>();
                Map<RationalizerSharedContent, TextAndMarkupForLastAction> changedSharedContents = new LinkedHashMap<>();
                processChangedContents(
                        request,
                        changedDocumentContents,
                        changedSharedContents,
                        RationalizerActionTypeEnum.ADD
                );

                saveChangedDocumentContents(
                        rationalizerElasticSearchHandler,
                        changedDocumentContents
                );

                saveChangedSharedContents(
                        rationalizerElasticSearchHandler,
                        changedSharedContents
                );

                processRemovedContents(
                        request,
                        rationalizerDocument,
                        rationalizerElasticSearchHandler
                );
                HibernateUtil.getManager().getSession().flush();

                processAddContent(
                        request,
                        rationalizerDocument,
                        rationalizerApplication,
                        rationalizerElasticSearchHandler
                );

                HibernateUtil.getManager().getSession().flush();

                recomputeOrderForContents(rationalizerDocument);

                Collection<RationalizerDocumentContent> shallowCopyOfContents = rationalizerDocument.shallowCopyOfContents();

                updateOrderMetadataForRationalizerContents(shallowCopyOfContents);
                syncOrderToElasticSearchDelayed(shallowCopyOfContents, rationalizerElasticSearchHandler);

                processDocumentNameDelayed(
                        rationalizerDocument,
                        initialDocumentName,
                        rationalizerElasticSearchHandler
                );

                processDocumentTagsDelayed(
                        rationalizerDocument,
                        initialDocumentTagsString,
                        rationalizerElasticSearchHandler
                );

                context.getResponse().setResultValueBean(rationalizerDocument.getId());

            } else if (request.getAction() == ACTION_SPLIT_DOCUMENT_CONTENT) {
                RationalizerDocument rationalizerDocument = request.getRationalizerDocument();
                RationalizerApplication rationalizerApplication = rationalizerDocument.getRationalizerApplication();
                RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
                RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication, true);

                String initialDocumentName = request.getName();
                String initialDocumentTagsString = request.getMetatags() != null ? request.getMetatags() : "";

                Map<RationalizerDocumentContent, TextAndMarkupForLastAction> changedDocumentContents = new LinkedHashMap<>();
                Map<RationalizerSharedContent, TextAndMarkupForLastAction> changedSharedContents = new LinkedHashMap<>();

                reconstructInsertMarkupContent(request);

                processChangedContents(
                        request,
                        changedDocumentContents,
                        changedSharedContents,
                        RationalizerActionTypeEnum.SPLIT
                );

                saveChangedDocumentContents(
                        rationalizerElasticSearchHandler,
                        changedDocumentContents
                );

                saveChangedSharedContents(
                        rationalizerElasticSearchHandler,
                        changedSharedContents
                );

                processRemovedContents(
                        request,
                        rationalizerDocument,
                        rationalizerElasticSearchHandler
                );
                HibernateUtil.getManager().getSession().flush();

                processSplitContent(
                        request,
                        rationalizerDocument,
                        rationalizerApplication,
                        rationalizerElasticSearchHandler
                );

                HibernateUtil.getManager().getSession().flush();

                recomputeOrderForContents(rationalizerDocument);

                Collection<RationalizerDocumentContent> shallowCopyOfContents = rationalizerDocument.shallowCopyOfContents();

                updateOrderMetadataForRationalizerContents(shallowCopyOfContents);
                syncOrderToElasticSearchDelayed(shallowCopyOfContents, rationalizerElasticSearchHandler);

                processDocumentNameDelayed(
                        rationalizerDocument,
                        initialDocumentName,
                        rationalizerElasticSearchHandler
                );

                processDocumentTagsDelayed(
                        rationalizerDocument,
                        initialDocumentTagsString,
                        rationalizerElasticSearchHandler
                );

                context.getResponse().setResultValueBean(rationalizerDocument.getId());

            } else if (request.getAction() == ACTION_UPDATE_DOCUMENT_CONTENT_METADATA) {

                updateDocumentContentMetadata(context, request);

            } else if (request.getAction() == ACTION_UPDATE_SHARED_CONTENT_METADATA) {

                updateSharedContentMetadata(context, request);

            } else if (request.getAction() == ACTION_UPDATE_DOCUMENT_METADATA) {

                updateDocumentMetadata(context, request);

            } else if (request.getAction() == ACTION_INIT_DOCUMENT_CONTENT_ORDER) {

                StringBuilder query = new StringBuilder();
                query.append("SELECT DISTINCT	rd ");
                query.append("FROM 				RationalizerDocument AS rd ");
                query.append("LEFT OUTER JOIN 	rd.rationalizerDocumentContents AS rdc ");
                query.append("WHERE 			rdc.order IS NULL ");

                List<RationalizerDocument> documentList = (List<RationalizerDocument>) HibernateUtil.getManager().getObjectsAdvanced(query.toString());

                for (RationalizerDocument currentDocument : documentList) {
                    int i = 1;
                    for (RationalizerDocumentContent currentContent : currentDocument.getRationalizerDocumentContentsInIdOrder()) {
                        currentContent.setOrder(i++);
                        currentContent.save();
                    }
                }
            }
        } catch (Exception e) {
            log.error(" unexpected exception when invoking CreateOrUpdateRationalizerApplicationService execute method", e);
            this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + e,
                    context.getLocale());
            throw new RuntimeException(e.getMessage());
        }
    }

    private void reconstructInsertMarkupContent(BulkRationalizerActionServiceRequest request) {
        final RationalizerDocumentContent contentToSplit = findDocumentContentToSplit(request);
        String insertedMarkupContent = request.getInsertedMarkupContent();
        if(StringUtils.isEmpty(insertedMarkupContent)) {
            return;
        }
        if(contentToSplit != null) {
            //If a user splits in the middle of a variable/hyperlink, variable/hyperlink will not be maintained in either the original or split content
            String varsInMetadataString = contentToSplit.findMetadataValueByItemDefinitionPrimaryConnector(CONTENT_METADATA_VARIABLES);
            insertedMarkupContent = updateMarkupInCaseVariablesSplit(varsInMetadataString, insertedMarkupContent);
            String urlsInMetadataString = contentToSplit.findMetadataValueByItemDefinitionPrimaryConnector(CONTENT_METADATA_URLS);
            insertedMarkupContent = updateMarkupInCaseHyperlinkSplit(urlsInMetadataString, insertedMarkupContent);
        }
        request.setInsertedMarkupContent(insertedMarkupContent);
    }

    private void updateDocumentMetadata(ServiceExecutionContext context, BulkRationalizerActionServiceRequest request) {
        RationalizerDocument rationalizerDocument = request.getRationalizerDocument();

        if (request.getRationalizerDocument().getParsedDocumentForm() != null) {
            rationalizerDocument.setParsedDocumentForm(request.getRationalizerDocument().getParsedDocumentForm());
        }
        rationalizerDocument.save();

        RationalizerApplication rationalizerApplication = rationalizerDocument.getRationalizerApplication();
        RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
        RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication, true);

        rationalizerDocument.updateContentMetadataInElasticSearch(rationalizerElasticSearchHandler);

        context.getResponse().setResultValueBean(request.getRationalizerDocument().getId());
    }

    private void updateDocumentContentMetadata(ServiceExecutionContext context, BulkRationalizerActionServiceRequest request) throws ParseException {
        RationalizerDocumentContent crtRationalizerDocumentContent = (RationalizerDocumentContent) request.getRationalizerContent();
        MetadataForm contentForm = crtRationalizerDocumentContent.getParsedContentForm();
        Map<Long, String> contentMetadataPreValueMap = request.getContentMetadataPreValueMap();

        RationalizerApplication rationalizerApplication = crtRationalizerDocumentContent.computeRationalizerApplication();
        RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
        RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication, true);

        if (contentForm != null) {
            int newOrderValue = 0;
            String newZoneConnector = null;
            String newMessageName = null;

            boolean contentFormChanged = false;
            for (MetadataFormItem currentItem : contentForm.getFormItems()) {
                final String primaryConnector = currentItem.getItemDefinition().getPrimaryConnector();
                if (StringUtils.equalsIgnoreCase(primaryConnector, "order")) {
                    newOrderValue = Integer.valueOf(currentItem.getValue());
                } else if (StringUtils.equalsIgnoreCase(primaryConnector, RationalizerDocumentContent.ZONE_CONNECTOR)) {
                    newZoneConnector = currentItem.getValue();
                } else if (StringUtils.equalsIgnoreCase(primaryConnector, RationalizerDocumentContent.MESSAGE_NAME)) {
                    newMessageName = currentItem.getValue();
                }

                if (!contentFormChanged) {
                    String preValue = contentMetadataPreValueMap.get(currentItem.getId());
                    String currentValue = getCurrentMetadataItemValue(currentItem);

                    if ((preValue == null && currentValue != null) ||
                            (preValue != null && currentValue == null) ||
                            (preValue != null && currentValue != null && !preValue.equals(currentValue))) {
                        contentFormChanged = true;
                    }
                }
            }

            if (contentFormChanged) {
                crtRationalizerDocumentContent.updateHistoryOnEdit(contentMetadataPreValueMap);
            }

            if (newOrderValue > 0) {
                crtRationalizerDocumentContent.updateOrder(newOrderValue);
            }

            if (newZoneConnector != null) {
                crtRationalizerDocumentContent.updateZoneConnector(newZoneConnector);
            }

            if (newMessageName != null) {
                crtRationalizerDocumentContent.updateMessageName(newMessageName);
            }

            contentForm.save();
            crtRationalizerDocumentContent.setParsedContentForm(contentForm);
        }
        crtRationalizerDocumentContent.save();

        Collection<RationalizerDocumentContent> shallowCopyOfContents = crtRationalizerDocumentContent.getRationalizerDocument().shallowCopyOfContents();

        updateOrderMetadataForRationalizerContents(shallowCopyOfContents);
        syncOrderToElasticSearchDelayed(shallowCopyOfContents, rationalizerElasticSearchHandler);

        Map<String, Object> mapContentGuidFieldValue = new HashMap<>();
        JsonObject metadata = RationalizerUtil.mapMetadata(crtRationalizerDocumentContent.getParsedContentForm().getFormItems());
        mapContentGuidFieldValue.put(crtRationalizerDocumentContent.buildElasticSearchGuid(), metadata);
        rationalizerElasticSearchHandler.updateFieldValueForContentsDelayed("metadata", mapContentGuidFieldValue);

        context.getResponse().setResultValueBean(crtRationalizerDocumentContent.getId());
    }

    private void updateSharedContentMetadata(ServiceExecutionContext context, BulkRationalizerActionServiceRequest request) throws ParseException {
        RationalizerSharedContent crtRationalizerSharedContent = (RationalizerSharedContent) request.getRationalizerContent();
        MetadataForm contentForm = crtRationalizerSharedContent.getParsedContentForm();
        Map<Long, String> contentMetadataPreValueMap = request.getContentMetadataPreValueMap();

        RationalizerApplication rationalizerApplication = crtRationalizerSharedContent.computeRationalizerApplication();
        RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
        RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication, true);

        if (contentForm != null) {
            boolean contentFormChanged = false;
            for (MetadataFormItem currentItem : contentForm.getFormItems()) {
                if (contentFormChanged) {
                    break;
                }

                String preValue = contentMetadataPreValueMap.get(currentItem.getId());
                String currentValue = getCurrentMetadataItemValue(currentItem);

                if ((preValue == null && currentValue != null) ||
                        (preValue != null && currentValue == null) ||
                        (preValue != null && currentValue != null && !preValue.equals(currentValue))) {
                    contentFormChanged = true;
                }
            }

            if (contentFormChanged) {
                if (crtRationalizerSharedContent.getHistMetadataContentForms().isEmpty()) {
                    // Save a copy of the previous metadata form if history entry is empty
                    HistoricalMetadataForm histForm = HistoricalMetadataForm.createHistory(contentForm, contentMetadataPreValueMap);
                    histForm.setCreated(contentForm.getCreated());
                    histForm.setCreatedBy(contentForm.getCreatedBy());
                    histForm.setUpdated(contentForm.getUpdated());
                    histForm.setUpdatedBy(contentForm.getUpdatedBy());
                    HibernateUtil.getManager().getSession().saveOrUpdate(histForm);
                    crtRationalizerSharedContent.getHistMetadataContentForms().add(histForm);
                }

                // Save a copy of the current metadata form if form is changed
                HistoricalMetadataForm histForm = HistoricalMetadataForm.createHistory(contentForm, null);
                histForm.save(true);
                crtRationalizerSharedContent.getHistMetadataContentForms().add(histForm);
            }

            contentForm.save();
            crtRationalizerSharedContent.setParsedContentForm(contentForm);
        }
        crtRationalizerSharedContent.save();

        Map<String, Object> mapContentGuidFieldValue = new HashMap<>();
        JsonObject metadata = RationalizerUtil.mapMetadata(crtRationalizerSharedContent.getParsedContentForm().getFormItems());
        mapContentGuidFieldValue.put(crtRationalizerSharedContent.buildElasticSearchGuid(), metadata);
        rationalizerElasticSearchHandler.updateFieldValueForContentsDelayed("metadata", mapContentGuidFieldValue);

        context.getResponse().setResultValueBean(crtRationalizerSharedContent.getId());
    }

    private String generateUniqueFileName(BulkRationalizerActionServiceRequest request, RationalizerApplication application) {
        List<String> existingFileNames = RationalizerDocument.findAllFileNames(application.getId());
        int i = 1;
        String fileName = request.getName();

        while (existingFileNames.contains(fileName)) {
            //rename
            String outFileNameWithOutExt = FilenameUtils.removeExtension(fileName);
            String extension = StringUtils.isEmpty(FilenameUtils.getExtension(fileName)) ? StringUtils.EMPTY : "." + FilenameUtils.getExtension(fileName);
            fileName =  outFileNameWithOutExt + "_(" + i + ")" + extension;
            i++;
        }
        return fileName;
    }

    private void recomputeOrderForContents(RationalizerDocument rationalizerDocument) {
        int order = 0;
        for (RationalizerDocumentContent content : rationalizerDocument.getRationalizerDocumentContentsInOrder()) {
            ++order;
            if (content.getOrder() == order) {
                continue;
            }

            content.setOrder(order);
            content.save();
        }
    }

    private void processAddContent(
            BulkRationalizerActionServiceRequest request,
            RationalizerDocument rationalizerDocument,
            RationalizerApplication rationalizerApplication,
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler
    ) {
        processInsertContentInternal(
                request,
                rationalizerDocument,
                rationalizerApplication,
                rationalizerElasticSearchHandler,
                null,
                RationalizerActionTypeEnum.ADD
        );
    }

    private void processInsertContentInternal(
            BulkRationalizerActionServiceRequest request,
            RationalizerDocument rationalizerDocument,
            RationalizerApplication rationalizerApplication,
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler,
            Function<RationalizerDocumentContent, Void> contentMetadataUpdater,
            RationalizerActionTypeEnum lastActionType
    ) {
        Collection<RationalizerDocumentContent> documentContents = rationalizerDocument.shallowCopyOfContents();

        int addContentCount = request.getAddContentCount();
        if (addContentCount <= 0) {
            return;
        }

        int insertAfterOrder = request.getInsertAfterOrder();
        if (insertAfterOrder > 0) {
            for (RationalizerDocumentContent currentContent : documentContents) {
                if (currentContent.getOrder() > insertAfterOrder) {
                    currentContent.setOrder(currentContent.getOrder() + addContentCount);
                    currentContent.save();
                }
            }
        }

        int orderIndex = insertAfterOrder > 0 ? insertAfterOrder : documentContents.size();
        String insertedMarkupContent = request.getInsertedMarkupContent();

        List<String> contentGuids = new LinkedList<>();
        Map<String, String> contentGuidToPreviousTextMap = new LinkedHashMap<>();
        Map<String, String> contentGuidToNewTextMap = new LinkedHashMap<>();

        for (int i = 0; i < addContentCount; i++) {
            RationalizerDocumentContent newDocumentContent = new RationalizerDocumentContent();
            newDocumentContent.setLastAction(lastActionType);
            if (StringUtils.isNotEmpty(insertedMarkupContent)) {
                if(!insertedMarkupContent.startsWith("<p>")) {
                    insertedMarkupContent = "<p>" +insertedMarkupContent + "</p>";
                }
                newDocumentContent.setMarkupContent(getUnformattedString(insertedMarkupContent), true);
                String textContentForMarcie = ContentObjectContentUtil.getUnformattedTextContentForMarcie(newDocumentContent.getMarkupContent());
                newDocumentContent.setTextContent(textContentForMarcie);

                final HistoricalRationalizerDocumentContent historicalRationalizerDocumentContent = generateHistoryForRationalizerDocumentContent(newDocumentContent, UserUtil.getPrincipalUser());
                newDocumentContent.getHistRationalizerContent().add(historicalRationalizerDocumentContent);
            } else {
                newDocumentContent.setMarkupContent(EMPTY_MARKUP_CONTENT);
                newDocumentContent.setTextContent(StringUtils.EMPTY);
                final HistoricalRationalizerDocumentContent historicalRationalizerDocumentContent = generateHistoryForRationalizerDocumentContent(newDocumentContent, UserUtil.getPrincipalUser());
                newDocumentContent.getHistRationalizerContent().add(historicalRationalizerDocumentContent);
            }

            newDocumentContent.setRationalizerDocument(rationalizerDocument);
            newDocumentContent.setOrder(++orderIndex);

            if (rationalizerApplication.getParsedContentFormDefinition() != null) {
                MetadataFormEditWrapper formEditWrapper = new MetadataFormEditWrapper(
                        rationalizerApplication.getParsedContentFormDefinition(),
                        true
                );
                MetadataForm metadataForm = formEditWrapper.getMetadataForm();

                metadataForm.save();

                newDocumentContent.setParsedContentForm(metadataForm);
            }

            if (contentMetadataUpdater != null) {
                contentMetadataUpdater.apply(newDocumentContent);
            }

            newDocumentContent.save();
            documentContents.add(newDocumentContent);

            final String contentGuid = newDocumentContent.getGuid();
            contentGuids.add(contentGuid);
            contentGuidToPreviousTextMap.put(contentGuid, null);
            contentGuidToNewTextMap.put(contentGuid, newDocumentContent.getTextContent());
        }

        rationalizerDocument.save();

        //send to Elastic
        sendRationalizerContentsToElasticSearchDelayed(
                UserUtil.getPrincipalUser(),
                rationalizerElasticSearchHandler,
                contentGuids,
                contentGuidToPreviousTextMap,
                contentGuidToNewTextMap
        );
    }

    private void processSplitContent(
            BulkRationalizerActionServiceRequest request,
            RationalizerDocument rationalizerDocument,
            RationalizerApplication rationalizerApplication,
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler
    ) {
        final RationalizerDocumentContent contentToSplit = findDocumentContentToSplit(request);
        final RationalizerSharedContent sharedContent = contentToSplit.computeRationalizerSharedContent();
        if (sharedContent != null) {
            log.error(MessageFormat.format("RationalizerDocumentEdit: Split operation for a content that is part of a shared content is not supported." +
                            " RationalizerDocumentContent with id = {0} is part of RationalizerSharedContent with id = {1}",
                    contentToSplit.getId(),
                    sharedContent.getId()
            ));

            return;
        }

        String supportedCountryCodesString = rationalizerDocument.findMetadataValueByItemDefinitionPrimaryConnector(DOC_METADATA_PHONE_LOCALES.replace(" ", "_"));
        if(StringUtils.isEmpty(supportedCountryCodesString)) {
            supportedCountryCodesString = "US";
        }
        List<String> supportedCountryCodesList =  Stream.of(supportedCountryCodesString.split(","))
                .collect(Collectors.toList());
        Function<RationalizerDocumentContent, Void> contentMetadataUpdater = (newContent) -> {
            if (contentToSplit != null) {
                mergeContentsMetadata(newContent, contentToSplit);
            }

            recomputeAutoClassifiedMetadata(
                    newContent.getParsedContentForm(),
                    new AutoClassifiedPropertyParams() {{
                        setTextContent(newContent.getTextContent());
                        setMarkupContent(newContent.computeNormalizedMarkupContent());
                        setMetadataJsonProcessor(new MetadataJsonProcessor());
                    }},
                    newContent.getRationalizerDocument().getParsedDocumentForm().getFormDefinition(),
                    supportedCountryCodesList
            );

            recomputeAutoClassifiedMetadata(
                    contentToSplit.getParsedContentForm(),
                    new AutoClassifiedPropertyParams() {{
                        setTextContent(contentToSplit.getTextContent());
                        setMarkupContent(contentToSplit.computeNormalizedMarkupContent());
                        setMetadataJsonProcessor(new MetadataJsonProcessor());
                    }},
                    contentToSplit.getRationalizerDocument().getParsedDocumentForm().getFormDefinition(),
                    supportedCountryCodesList
            );

            return null;
        };

        processInsertContentInternal(
                request,
                rationalizerDocument,
                rationalizerApplication,
                rationalizerElasticSearchHandler,
                contentMetadataUpdater,
                RationalizerActionTypeEnum.SPLIT
        );
    }

    private RationalizerDocumentContent findDocumentContentToSplit(BulkRationalizerActionServiceRequest request) {
        int insertAfterOrder = request.getInsertAfterOrder();
        Collection<RationalizerDocumentContent> documentContents = request.getRationalizerDocument().shallowCopyOfContents();
        for (RationalizerDocumentContent documentContent : documentContents) {
            if (insertAfterOrder == documentContent.getOrder()) {
                return documentContent;
            }
        }

        return null;
    }

    private void processDocumentTagsDelayed(
            RationalizerDocument updatedRationalizerDocument,
            String initialDocumentTagsString,
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler
    ) {
        //Document tags changed - update all contents in Elastic with the new document tags
        if (updatedRationalizerDocument.getMetatags().equals(initialDocumentTagsString)) {
            return;
        }

        updateDocumentFieldValueForAllContentsDelayed(
                rationalizerElasticSearchHandler,
                updatedRationalizerDocument,
                "document.metadata.tagcloud",
                Arrays.asList(updatedRationalizerDocument.getMetatags().split("\\s"))
        );
    }

    private void processDocumentNameDelayed(
            RationalizerDocument updatedRationalizerDocument,
            String initialDocumentName,
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler
    ) {
        //Document name changed - update all contents in Elastic with the new document name
        if (initialDocumentName.equals(updatedRationalizerDocument.getName())) {
            return;
        }

        //update document.metadata.Name with the new value
        Set<MetadataFormItem> formItems = updatedRationalizerDocument.getParsedDocumentForm().getFormItems();
        for (MetadataFormItem formItem : formItems) {
            if (formItem.getItemDefinition().getPrimaryConnector().toLowerCase().equals("name")) {
                formItem.setValue(updatedRationalizerDocument.getName());
                break;
            }
        }

        updateDocumentFieldValueForAllContentsDelayed(
                rationalizerElasticSearchHandler,
                updatedRationalizerDocument,
                "document.attribute.name",
                Collections.singletonList(updatedRationalizerDocument.getName())
        );

        updateDocumentFieldValueForAllContentsDelayed(
                rationalizerElasticSearchHandler,
                updatedRationalizerDocument,
                "document.metadata.Name",
                Collections.singletonList(updatedRationalizerDocument.getName())
        );
    }

    private void saveChangedDocumentContents(
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler,
            Map<RationalizerDocumentContent, TextAndMarkupForLastAction> documentContentsToTextAndMarkupMap
    ) {
        List<String> documentContentsGuids = new LinkedList<>();
        Map<String, String> documentContentGuidToPreviousTextMap = new LinkedHashMap<>();
        Map<String, String> documentContentGuidToNewTextMap = new LinkedHashMap<>();
        documentContentsToTextAndMarkupMap.forEach(
                (documentContent, textAndMarkupForLastAction) -> {
                    RationalizerActionTypeEnum lastActionType = textAndMarkupForLastAction.getLastActionType();
                    String oldText = documentContent.getTextContent();
                    String newText = textAndMarkupForLastAction.text;

                    String oldMarkup = documentContent.computeNormalizedMarkupContent();
                    String newMarkup = textAndMarkupForLastAction.markup;

                    if (StringUtils.equals(getUnformattedString(oldMarkup), getUnformattedString(newMarkup))) {
                        return;
                    }

                    documentContent.setStatusType(RationalizerContentStatusFilterType.ID_EDITED);
                    documentContent.setLastAction(lastActionType);
                    documentContent.setTextContent(newText);
                    documentContent.setMarkupContent(newMarkup, true);
                    Set<HistoricalRationalizerDocumentContent> histRationalizerContent = documentContent.getHistRationalizerContent();

                    final HistoricalRationalizerDocumentContent historicalRationalizerDocumentContent = generateHistoryForRationalizerDocumentContent(documentContent, UserUtil.getPrincipalUser());
                    histRationalizerContent.add(historicalRationalizerDocumentContent);

                    clearPositionalMetadata(documentContent.getParsedContentForm());
                    recomputeAutoClassifiedMetadata(documentContent);

                    documentContent.save();

                    final String documentContentGuid = documentContent.getGuid();
                    documentContentsGuids.add(documentContentGuid);
                    documentContentGuidToPreviousTextMap.put(documentContentGuid, oldText);
                    documentContentGuidToNewTextMap.put(documentContentGuid, newText);
                }
        );

        //send to Elastic
        sendRationalizerContentsToElasticSearchDelayed(
                UserUtil.getPrincipalUser(),
                rationalizerElasticSearchHandler,
                documentContentsGuids,
                documentContentGuidToPreviousTextMap,
                documentContentGuidToNewTextMap
        );
    }

    private void saveChangedSharedContents(
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler,
            Map<RationalizerSharedContent, TextAndMarkupForLastAction> sharedContentsToTextAndMarkupMap
    ) {
        List<String> sharedContentsGuids = new LinkedList<>();
        Map<String, String> sharedContentGuidToPreviousTextMap = new LinkedHashMap<>();
        Map<String, String> sharedContentGuidToNewTextMap = new LinkedHashMap<>();
        sharedContentsToTextAndMarkupMap.forEach(
                (sharedContent, textAndMarkupForLastAction) -> {
                    String oldText = sharedContent.getTextContent();
                    String newText = textAndMarkupForLastAction.text;

                    String oldMarkup = sharedContent.computeNormalizedMarkupContent();
                    String newMarkup = textAndMarkupForLastAction.markup;

                    if (StringUtils.equals(getUnformattedString(oldMarkup), getUnformattedString(newMarkup))) {
                        return;
                    }

                    sharedContent.setLastAction(RationalizerActionTypeEnum.UPDATE);
                    sharedContent.setTextContent(newText);
                    sharedContent.setMarkupContent(newMarkup, true);

                    final HistoricalRationalizerSharedContent historicalRationalizerSharedContent = generateHistoryForRationalizerSharedContent(sharedContent);
                    sharedContent.getHistRationalizerSharedContents().add(historicalRationalizerSharedContent);

                    clearPositionalMetadata(sharedContent.getParsedContentForm());
                    recomputeAutoClassifiedMetadata(sharedContent);
                    sharedContent.save();

                    final String sharedContentGuid = sharedContent.getGuid();
                    sharedContentsGuids.add(sharedContentGuid);
                    sharedContentGuidToPreviousTextMap.put(sharedContentGuid, oldText);
                    sharedContentGuidToNewTextMap.put(sharedContentGuid, newText);
                }
        );

        //send to Elastic
        sendRationalizerContentsToElasticSearchDelayed(
                UserUtil.getPrincipalUser(),
                rationalizerElasticSearchHandler,
                sharedContentsGuids,
                sharedContentGuidToPreviousTextMap,
                sharedContentGuidToNewTextMap
        );
    }

    private void processRemovedContents(
            BulkRationalizerActionServiceRequest request,
            RationalizerDocument rationalizerDocument,
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler
    ) {
        List<RationalizerContent> rationalizerContents = request.getRationalizerContents();
        Map<String, Integer> elasticSearchGuidToContentsActionsMap = request.getElasticSearchGuidToContentsActionsMap();

        List<RationalizerContent> removedContents = new LinkedList<>();
        Set<RationalizerSharedContent> sharedContentsToUpdate = new LinkedHashSet<>();
        for (RationalizerContent content : rationalizerContents) {
            String elasticSearchGuid = content.buildElasticSearchGuid();
            Integer contentAction = elasticSearchGuidToContentsActionsMap.get(elasticSearchGuid);
            if (contentAction != null && contentAction == -1) {
                removedContents.add(content);

                final RationalizerSharedContent sharedContent = content.computeRationalizerSharedContent();
                if (sharedContent != null) {
                    sharedContentsToUpdate.add(sharedContent);
                }
            }
        }

        if (CollectionUtils.isEmpty(removedContents)) {
            return;
        }

        rationalizerContents.removeAll(removedContents);
        request.adjustMapsRelatedToDocumentContents();

        Collection<String> contentGuidsToDeleteFromElastic = buildElasticSearchGuids(removedContents);
        removedContents.forEach(removedContent -> {
            if (removedContent instanceof RationalizerDocumentContent) {
                final RationalizerDocumentContent rationalizerDocumentContent = (RationalizerDocumentContent) removedContent;
                rationalizerDocumentContent.removeFromRationalizerDocument();
                HistoricalRationalizerDocumentContent.deleteByRationalizerContentId(rationalizerDocumentContent.getId());
            } else if (removedContent instanceof RationalizerSharedContent) {
                final RationalizerSharedContent rationalizerSharedContent = (RationalizerSharedContent) removedContent;
                rationalizerSharedContent.removeFromDependencies();
                rationalizerSharedContent.save();
            }
        });
        rationalizerDocument.save();

        deleteRationalizerContentsFromElasticSearchDelayed(
                UserUtil.getPrincipalUser(),
                rationalizerElasticSearchHandler,
                contentGuidsToDeleteFromElastic
        );

        sendSharedContentsToElasticSearchDelayed(
                rationalizerElasticSearchHandler,
                sharedContentsToUpdate
        );
    }

    private void processMergedContents(
            BulkRationalizerActionServiceRequest request,
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler
    ) {
        List<String> selectedIds = request.getSelectedIds();
        if (selectedIds.size() < 2) {
            return;
        }

        String firstSelectedId = findFirstSelectedId(request);
        if (firstSelectedId == null) {
            return;
        }

        Map<String, RationalizerContent> elasticSearchGuidToContentMap = request.getElasticSearchGuidToContentMap();

        List<RationalizerContent> selectedContents = selectedIds.stream()
                .map(selectedId -> elasticSearchGuidToContentMap.get(selectedId))
                .filter(item -> item != null)
                .collect(Collectors.toCollection(LinkedList::new));
        for (RationalizerContent rationalizerContent : selectedContents) {
            final RationalizerSharedContent sharedContent = rationalizerContent.computeRationalizerSharedContent();
            if (sharedContent != null) {
                log.error(MessageFormat.format("RationalizerDocumentEdit: Merge operation when one of the contents is part of a shared content is not supported." +
                                " RationalizerDocumentContent with id = {0} is part of RationalizerSharedContent with id = {1}",
                        rationalizerContent.getId(),
                        sharedContent.getId()
                ));

                return;
            }
        }

        List<RationalizerDocumentContent> selectedContentsWithoutFirst = selectedIds.stream()
                .filter(elasticSearchGuid -> !StringUtils.equals(elasticSearchGuid, firstSelectedId))
                .map(elasticSearchGuid -> (RationalizerDocumentContent) elasticSearchGuidToContentMap.get(elasticSearchGuid))
                .filter(item -> item != null)
                .collect(Collectors.toList());

        RationalizerDocumentContent targetContent = (RationalizerDocumentContent) elasticSearchGuidToContentMap.get(firstSelectedId);
        if (targetContent == null) {
            return;
        }

        String initialTextContent = targetContent.buildDisplayedTextContent();
        // At this moment Merge operation is disabled when any of the merged contents has a sharedContent.

        clearPositionalMetadata(targetContent.getParsedContentForm());
        mergeContentWithContentsList(targetContent, selectedContentsWithoutFirst);
        recomputeAutoClassifiedMetadata(targetContent);

        // The target content is a brand new content, so history for it is deleted as decided.
        targetContent.getHistRationalizerContent().clear();

        targetContent.setLastAction(RationalizerActionTypeEnum.MERGE);
        final HistoricalRationalizerDocumentContent historicalRationalizerDocumentContent = generateHistoryForRationalizerDocumentContent(targetContent, UserUtil.getPrincipalUser());
        targetContent.getHistRationalizerContent().add(historicalRationalizerDocumentContent);

        targetContent.save();
        targetContent.getRationalizerDocument().save();

        //send to Elastic
        sendRationalizerContentsToElasticSearchDelayed(
                UserUtil.getPrincipalUser(),
                rationalizerElasticSearchHandler,
                Collections.singletonList(targetContent.getGuid()),
                new LinkedHashMap<>() {{
                    put(targetContent.getGuid(), initialTextContent);
                }},
                new LinkedHashMap<>() {{
                    put(targetContent.getGuid(), targetContent.getTextContent());
                }}
        );
    }

    private String findFirstSelectedId(BulkRationalizerActionServiceRequest request) {
        List<String> selectedIds = request.getSelectedIds();
        List<RationalizerContent> rationalizerContents = request.getRationalizerContents();

        if (CollectionUtils.isEmpty(selectedIds) || CollectionUtils.isEmpty(rationalizerContents)) {
            return null;
        }

        String result = rationalizerContents.stream()
                .map(content -> content.buildElasticSearchGuid())
                .filter(elasticSearchGuid -> selectedIds.contains(elasticSearchGuid))
                .findFirst()
                .orElse(null);

        return result;
    }

    private void mergeContentWithContentsList(RationalizerDocumentContent targetContent, List<RationalizerDocumentContent> contentsToAppend) {
        if (CollectionUtils.isEmpty(contentsToAppend)) {
            return;
        }

        contentsToAppend.stream()
                .forEach(contentToAppend -> {
                            mergeContents(targetContent, contentToAppend);
                            contentToAppend.removeFromRationalizerDocument();
                            HistoricalRationalizerDocumentContent.deleteByRationalizerContentId(contentToAppend.getId());
                        }
                );
        String textContentForMarcie = ContentObjectContentUtil.getUnformattedTextContentForMarcie(targetContent.computeNormalizedMarkupContent());
        targetContent.setTextContent(textContentForMarcie);

        RationalizerApplication rationalizerApplication = contentsToAppend.get(0).getRationalizerDocument().getRationalizerApplication();

        RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
        RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication, true);

        deleteRationalizerContentsFromElasticSearchDelayed(
                UserUtil.getPrincipalUser(),
                rationalizerElasticSearchHandler,
                contentsToAppend.stream()
                        .map(content -> content.getGuid())
                        .collect(Collectors.toList())
        );
    }

    private void mergeContents(RationalizerDocumentContent targetContent, RationalizerDocumentContent contentToAppend) {
        mergeContentsMarkupContent(targetContent, contentToAppend);
        mergeContentsMetadata(targetContent, contentToAppend);
    }

    private void mergeContentsMarkupContent(RationalizerDocumentContent targetContent, RationalizerDocumentContent contentToAppend) {
        String markupContentToAppend = contentToAppend.computeNormalizedMarkupContent();
        if (StringUtils.isEmpty(markupContentToAppend)) {
            return;
        }

        String targetMarkupContent = targetContent.computeNormalizedMarkupContent();
        if(!markupContentToAppend.startsWith("<p>")) {
            markupContentToAppend = "<p>" + markupContentToAppend + "</p>";
        }
        targetContent.setMarkupContent(targetMarkupContent + markupContentToAppend, true);
    }

    public static void mergeContentsMetadata(RationalizerContent targetContent, RationalizerContent contentToAppend) {
        Map<String, List<MetadataFormItem>> targetPrimaryConnectorToMetadataFormItems = computePrimaryConnectorLowerCaseToMetadataFormItems(targetContent);
        Map<String, List<MetadataFormItem>> appendPrimaryConnectorToMetadataFormItems = computePrimaryConnectorLowerCaseToMetadataFormItems(contentToAppend);

        final boolean targetIsSharedContent = targetContent instanceof RationalizerSharedContent;

        Set<MetadataFormItem> metadataFormItemsToDelete = new LinkedHashSet<>();

        Set<MetadataFormItem> newTargetMetadataFormItems = new LinkedHashSet<>();
        for (Map.Entry<String, List<MetadataFormItem>> targetEntry : targetPrimaryConnectorToMetadataFormItems.entrySet()) {
            String targetPrimaryConnectorLowerCase = targetEntry.getKey();
            List<MetadataFormItem> targetMetadataFormItems = targetEntry.getValue();

            if (targetIsSharedContent && LOWER_CASE_PRIMARY_CONNECTORS_NOT_IN_SHARED_CONTENTS.contains(targetPrimaryConnectorLowerCase)) {
                metadataFormItemsToDelete.addAll(targetMetadataFormItems);

                continue;
            }

            List<MetadataFormItem> appendMetadataFormItems = appendPrimaryConnectorToMetadataFormItems.remove(targetPrimaryConnectorLowerCase);

            if (targetPrimaryConnectorLowerCase.equalsIgnoreCase(ORDER)
                    || CONTENT_POSITIONAL_PROPS_LOWERCASE.contains(targetPrimaryConnectorLowerCase)
                    || CollectionUtils.isEmpty(appendMetadataFormItems)
            ) {
                newTargetMetadataFormItems.addAll(targetMetadataFormItems);

                continue;
            }

            if (CONTENT_AUTO_CLASSIFIED_PROPERTIES_LOWERCASE_TO_MAINTAIN.test(targetPrimaryConnectorLowerCase)) {
                MetadataFormItem firstTargetValueItem = targetMetadataFormItems.get(0);
                for (MetadataFormItem crtAppendValueItem : appendMetadataFormItems) {
                    mergeBooleanMetadataFormItems(firstTargetValueItem, crtAppendValueItem);
                }

                newTargetMetadataFormItems.addAll(targetMetadataFormItems);

                continue;
            }

            MetadataFormItem firstTargetValueItem = targetMetadataFormItems.get(0);
            for (MetadataFormItem crtAppendValueItem : appendMetadataFormItems) {
                final String primaryConnectorLowerCase = getNonNullOrEmptyString(firstTargetValueItem.getItemDefinition().getPrimaryConnector()).toLowerCase();
                if("zone_connector".equalsIgnoreCase(primaryConnectorLowerCase)) {
                    String zoneValue = StringUtils.isEmpty(firstTargetValueItem.getValue()) ? crtAppendValueItem.getValue() : firstTargetValueItem.getValue();
                    firstTargetValueItem.setValue(zoneValue);
                    if (!targetIsSharedContent){
                        ((RationalizerDocumentContent) targetContent).updateZoneConnector(zoneValue);
                    }
                } else {
                    mergeMetadataFormItemsHavingSamePrimaryConnector(firstTargetValueItem, crtAppendValueItem);
                }
            }

            newTargetMetadataFormItems.addAll(targetMetadataFormItems);
        }

        for (Map.Entry<String, List<MetadataFormItem>> appendEntry : appendPrimaryConnectorToMetadataFormItems.entrySet()) {
            String appendPrimaryConnectorLowerCase = appendEntry.getKey();

            if (targetIsSharedContent && LOWER_CASE_PRIMARY_CONNECTORS_NOT_IN_SHARED_CONTENTS.contains(appendPrimaryConnectorLowerCase)) {
                continue;
            }

            if (
                    CONTENT_AUTO_CLASSIFIED_LOWER_PRIMARY_CONNECTORS_TO_RECOMPUTE.containsKey(appendPrimaryConnectorLowerCase)
                            || CONTENT_POSITIONAL_PROPS_LOWERCASE.contains(appendPrimaryConnectorLowerCase)
            ) {
                continue;
            }

            List<MetadataFormItem> appendMetadataFormItems = appendEntry.getValue();
            for (MetadataFormItem appendItem : appendMetadataFormItems) {
                MetadataForm targetMetadataForm = targetContent.getParsedContentForm();
                MetadataFormItem tmpItem = CloneHelper.clone(appendItem,
                        o -> o.clone(targetMetadataForm, false)
                );

                tmpItem.save(true);

                newTargetMetadataFormItems.add(tmpItem);
            }
        }

        targetContent.getParsedContentForm().setFormItems(newTargetMetadataFormItems);

        for (MetadataFormItem metadataFormItemToDelete : metadataFormItemsToDelete) {
            metadataFormItemToDelete.delete();
        }
    }

    private static void mergeBooleanMetadataFormItems(
            MetadataFormItem targetValueItem,
            MetadataFormItem appendValueItem
    ) {
        String targetValue = targetValueItem.getValue();
        String appendValue = appendValueItem.getValue();

        targetValue = mergeBooleanValues(targetValue, appendValue);

        targetValueItem.setValue(targetValue);
    }

    private static void mergeMetadataFormItemsHavingSamePrimaryConnector(MetadataFormItem targetValueItem, MetadataFormItem appendValueItem) {
        String targetValue = getNonNullOrEmptyString(targetValueItem.getValue());
        String appendValue = getNonNullOrEmptyString(appendValueItem.getValue());

        String newTargetValue;
        final String primaryConnectorLowerCase = getNonNullOrEmptyString(targetValueItem.getItemDefinition().getPrimaryConnector()).toLowerCase();
        if (CONTENT_AUTO_CLASSIFIED_LOWER_PRIMARY_CONNECTORS_TO_RECOMPUTE.containsKey(primaryConnectorLowerCase)) {
            newTargetValue = StringUtils.EMPTY;
            targetValueItem.setValue(newTargetValue);

            return;
        }

        if (isBooleanValue(targetValue) && isBooleanValue(appendValue)) {
            newTargetValue = mergeBooleanValues(targetValue, appendValue);
        } else {
            newTargetValue = mergeCommaSeparatedValues(targetValue, appendValue);
        }

        if (!StringUtils.equals(targetValue, newTargetValue)) {
            targetValueItem.setValue(newTargetValue);
        }
    }

    private static boolean isBooleanValue(String value) {
        return YES_STRING.equalsIgnoreCase(value)
                || NO_STRING.equalsIgnoreCase(value)
                || TRUE_STRING.equalsIgnoreCase(value)
                || FALSE_STRING.equalsIgnoreCase(value);
    }

    private static String mergeBooleanValues(String targetValue, String appendValue) {
        boolean computed = false;
        if (TRUE_STRING.equalsIgnoreCase(targetValue) || TRUE_STRING.equalsIgnoreCase(appendValue)) {
            targetValue = TRUE_STRING;

            computed = true;
        } else if (YES_STRING.equalsIgnoreCase(targetValue) || YES_STRING.equalsIgnoreCase(appendValue)) {
            targetValue = YES_STRING;

            computed = true;
        }

        if (!computed) {
            // If none of the values were neither YES_STRING, neither TRUE_STRING
            // then we need to decide whether we will set targetValue to NO_STRING or FALSE_STRING.
            // If that value is a Yes/No value it will be set to NO_STRING.
            // If that value is a True/False value it will be set to FALSE_STRING.

            if (FALSE_STRING.equalsIgnoreCase(targetValue) || FALSE_STRING.equalsIgnoreCase(appendValue)) {
                targetValue = FALSE_STRING;
            } else if (NO_STRING.equalsIgnoreCase(targetValue) || NO_STRING.equalsIgnoreCase(appendValue)) {
                targetValue = NO_STRING;
            }
        }

        return targetValue;
    }

    private static String mergeCommaSeparatedValues(String targetValue, String appendValue) {
        if (StringUtils.isEmpty(appendValue)) {
            return targetValue;
        }

        Set<String> values = new LinkedHashSet<>();
        values.addAll(Arrays.asList(targetValue.split(CONTENT_METADATA_SPLIT_SEPARATOR)));
        values.addAll(Arrays.asList(appendValue.split(CONTENT_METADATA_SPLIT_SEPARATOR)));

        return values.stream()
                .filter(value -> StringUtils.isNotEmpty(value.trim()))
                .collect(Collectors.joining(CONTENT_METADATA_MERGE_SEPARATOR));
    }

    public static Map<String, List<MetadataFormItem>> computePrimaryConnectorLowerCaseToMetadataFormItems(RationalizerContent targetContent) {
        Map<String, List<MetadataFormItem>> result = new LinkedHashMap<>();

        MetadataForm parsedContentForm = targetContent.getParsedContentForm();
        if (parsedContentForm == null) {
            return result;
        }

        Set<MetadataFormItem> formItems = parsedContentForm.getFormItems();
        if (CollectionUtils.isEmpty(formItems)) {
            return result;
        }

        formItems.forEach(formItem -> {
            MetadataFormItemDefinition itemDefinition = formItem.getItemDefinition();
            if (itemDefinition == null) {
                return;
            }

            String primaryConnector = itemDefinition.getPrimaryConnector();
            if (StringUtils.isEmpty(primaryConnector)) {
                return;
            }

            String primaryConnectorLowerCase = primaryConnector.toLowerCase();

            List<MetadataFormItem> metadataFormItemsList = result.get(primaryConnectorLowerCase);
            if (metadataFormItemsList == null) {
                metadataFormItemsList = new LinkedList<>();
                result.put(primaryConnectorLowerCase, metadataFormItemsList);
            }

            metadataFormItemsList.add(formItem);
        });

        return result;
    }

    public static void recomputeAutoClassifiedMetadata(RationalizerDocumentContent documentContent) {
        RationalizerDocument rationalizerDocument = documentContent.getRationalizerDocument();
        String supportedCountryCodesString = rationalizerDocument.findMetadataValueByItemDefinitionPrimaryConnector(DOC_METADATA_PHONE_LOCALES.replace(" ", "_"));
        if(StringUtils.isEmpty(supportedCountryCodesString)) {
            supportedCountryCodesString = "US";
        }
        List<String> supportedCountryCodesList =  Stream.of(supportedCountryCodesString.split(METADATA_VALUES_SEPARATOR))
                .collect(Collectors.toList());

        if (hasNoTypeForVariablesInMarkup(documentContent.getMarkupContent())) {
            addTypeToVariablesInMarkup(documentContent);
        }
        recomputeAutoClassifiedMetadata(
                documentContent.getParsedContentForm(),
                new AutoClassifiedPropertyParams() {{
                    setTextContent(documentContent.getTextContent());
                    setMarkupContent(documentContent.computeNormalizedMarkupContent());
                    setMetadataJsonProcessor(new MetadataJsonProcessor());
                }},
                rationalizerDocument.getParsedDocumentForm().getFormDefinition(),
                supportedCountryCodesList
        );
    }

    public static void recomputeAutoClassifiedMetadata(RationalizerSharedContent sharedContent) {
        recomputeAutoClassifiedMetadata(
                sharedContent.getParsedContentForm(),
                new AutoClassifiedPropertyParams() {{
                    setTextContent(sharedContent.getTextContent());
                    setMarkupContent(sharedContent.computeNormalizedMarkupContent());
                    setMetadataJsonProcessor(new MetadataJsonProcessor());
                }},
                sharedContent.getRationalizerApplication().getParsedDocumentFormDefinition(),
                RationalizerUtil.getDefaultSupportedCountryCodesList()
        );
    }

    private static void recomputeAutoClassifiedMetadata(
            MetadataForm parsedContentForm,
            AutoClassifiedPropertyParams autoClassifiedPropertyParams,
            MetadataFormDefinition documentFormDefinition,
            List<String> supportedCountryCodesList
    ) {
        if (documentFormDefinition == null || parsedContentForm == null) {
            return;
        }

        Set<MetadataFormItem> formItems = parsedContentForm.getFormItems();
        if (CollectionUtils.isEmpty(formItems)) {
            return;
        }

        Map<String, String> remainingAutoClassifiedPropertiesMap = new LinkedHashMap<>(CONTENT_AUTO_CLASSIFIED_LOWER_PRIMARY_CONNECTORS_TO_RECOMPUTE);

        formItems.forEach(formItem -> {
            MetadataFormItemDefinition itemDefinition = formItem.getItemDefinition();
            if (itemDefinition == null) {
                return;
            }

            String primaryConnector = itemDefinition.getPrimaryConnector();
            if (StringUtils.isEmpty(primaryConnector)) {
                return;
            }

            String primaryConnectorLowerCase = primaryConnector.toLowerCase();
            if (!CONTENT_AUTO_CLASSIFIED_LOWER_PRIMARY_CONNECTORS_TO_RECOMPUTE.containsKey(primaryConnectorLowerCase)) {
                return;
            }

            remainingAutoClassifiedPropertiesMap.remove(primaryConnectorLowerCase);
            recomputeAutoClassifiedProperty(autoClassifiedPropertyParams, formItem, primaryConnector, supportedCountryCodesList);
        });

        Collection<String> remainingAutoClassifiedPropertiesSet = remainingAutoClassifiedPropertiesMap.values();
        for (String autoClassifiedProperty : remainingAutoClassifiedPropertiesSet) {
            addAutoClassifiedProperty(
                    parsedContentForm,
                    autoClassifiedPropertyParams,
                    documentFormDefinition,
                    autoClassifiedProperty,
                    supportedCountryCodesList
            );
        }

        parsedContentForm.save();
    }

    private static void addTypeToVariablesInMarkup(RationalizerDocumentContent documentContent) {
        String markupContent = documentContent.computeNormalizedMarkupContent();
        Document document = Jsoup.parse(markupContent);
        document.outputSettings().prettyPrint(false);
        Elements variableElements = document.select(MPR_VARIABLE);
        String varsInMetadataString = documentContent.findMetadataValueByItemDefinitionPrimaryConnector("Variables");
        if(StringUtils.isEmpty(varsInMetadataString)){
            return;
        }
        List<String> variablesInMetadataList = Arrays.asList(varsInMetadataString.split("(?<!\\\\),"));
        variablesInMetadataList = variablesInMetadataList.stream().filter(e -> StringUtils.isNotEmpty(e)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(variablesInMetadataList)) {
            for(String varMeta : variablesInMetadataList) {
                varMeta = varMeta.replace("\\\\,", ",");
                for(Element varElem : variableElements) {
                    if(StringUtils.isEmpty(varElem.attr("type"))) {
                        String varInMarkup = varElem.text();
                        if(varMeta.contains(varInMarkup)) {
                            String startVarTag = varMeta.substring(0, varMeta.indexOf(varInMarkup));
                            VariablesTypeEnum varType = getVarType(startVarTag);
                            if(varType != null) {
                                varElem.attr("type", varType.toString());
                            } else {
                                varElem.attr("type", VariablesTypeEnum.DOUBLE_ANGLE_BRACKETS.toString());
                            }
                        }
                    }
                }
            }
        }
        for(Element varElem : variableElements) {
            if (StringUtils.isEmpty(varElem.attr("type"))) {
                varElem.attr("type", VariablesTypeEnum.DOUBLE_ANGLE_BRACKETS.toString());
            }
        }
        documentContent.setMarkupContent(document.body().html(), true);
    }

    private static VariablesTypeEnum getVarType(String startTag) {
        for (VariablesTypeEnum s : VariablesTypeEnum.values()) {
            if(s.getStartTag().equals(startTag.trim())) {
                return s;
            }
        }
        return null;
    }


    private static boolean hasNoTypeForVariablesInMarkup(String markup) {
        Elements variableElements = extractVariablesElements(markup);
        for(Element varElem: variableElements) {
            if(StringUtils.isEmpty(varElem.attr("type"))) {
                return true;
            }
        }
        return false;
    }

    private static void addAutoClassifiedProperty(
            MetadataForm parsedContentForm,
            AutoClassifiedPropertyParams autoClassifiedPropertyParams,
            MetadataFormDefinition documentFormDefinition,
            String primaryConnector,
            List<String> supportedCountryCodesList
    ) {
        MetadataFormItemDefinition formItemDefinition = findOrCreateFormItemDefinitionHavingPrimaryConnector(documentFormDefinition, primaryConnector);
        User currentUser = UserUtil.getPrincipalUser();
        Date now = new Date();

        MetadataFormItem metadataFormItem = new MetadataFormItem();
        metadataFormItem.setCreated(now);
        metadataFormItem.setCreatedBy(currentUser.getId());
        metadataFormItem.setAutoGenerateOnImport(false);

        metadataFormItem.setUpdated(now);
        metadataFormItem.setUpdatedBy(currentUser.getId());

        metadataFormItem.setMetadataForm(parsedContentForm);
        metadataFormItem.setItemDefinition(formItemDefinition);
        metadataFormItem.setValue(StringUtils.EMPTY);

        parsedContentForm.getFormItems().add(metadataFormItem);
        metadataFormItem.save(true);

        recomputeAutoClassifiedProperty(
                autoClassifiedPropertyParams,
                metadataFormItem,
                primaryConnector,
                supportedCountryCodesList
        );
    }

    private static MetadataFormItemDefinition findOrCreateFormItemDefinitionHavingPrimaryConnector(
            MetadataFormDefinition documentFormDefinition,
            String primaryConnector
    ) {
        MetadataFormItemDefinition formItemDefinition = findFormItemDefinitionHavingPrimaryConnector(primaryConnector, documentFormDefinition);
        if (formItemDefinition != null) {
            return formItemDefinition;
        }

        return createFormItemDefinitionHavingPrimaryConnector(primaryConnector, documentFormDefinition);
    }

    private static MetadataFormItemDefinition createFormItemDefinitionHavingPrimaryConnector(String primaryConnector, MetadataFormDefinition documentFormDefinition) {
        MetadataFormItemDefinition newFormItemDefinition = new MetadataFormItemDefinition();
        newFormItemDefinition.setPrimaryConnector(primaryConnector);
        newFormItemDefinition.setName(primaryConnector);
        newFormItemDefinition.setOrder(documentFormDefinition.getFormItemDefinitions().size() + 1);
        newFormItemDefinition.setTypeId(MetadataFormItemType.ID_TEXT);
        newFormItemDefinition.setInputValidationTypeId(MetadataFormInputValidationType.ID_NONE);
        newFormItemDefinition.setAutoGenerateOnImport(false);
        newFormItemDefinition.setMetadataFormDefinition(documentFormDefinition);

        documentFormDefinition.getFormItemDefinitions().add(newFormItemDefinition);
        newFormItemDefinition.save();

        return newFormItemDefinition;
    }

    private static MetadataFormItemDefinition findFormItemDefinitionHavingPrimaryConnector(String primaryConnector, MetadataFormDefinition documentFormDefinition) {
        Set<MetadataFormItemDefinition> formItemDefinitions = documentFormDefinition.getFormItemDefinitions();
        for (MetadataFormItemDefinition formItemDefinition : formItemDefinitions) {
            if (StringUtils.equalsIgnoreCase(primaryConnector, formItemDefinition.getPrimaryConnector())) {
                return formItemDefinition;
            }
        }
        return null;
    }

    public static class AutoClassifiedPropertyParams {
        private String textContent;
        private String markupContent;

        private MetadataJsonProcessor metadataJsonProcessor;

        public String getTextContent() {
            return textContent;
        }

        public String getMarkupContent() {
            return markupContent;
        }

        public void setTextContent(String textContent) {
            this.textContent = textContent;
        }

        public void setMarkupContent(String markupContent) {
            this.markupContent = markupContent;
        }

        public MetadataJsonProcessor getMetadataJsonProcessor() {
            return this.metadataJsonProcessor;
        }

        public void setMetadataJsonProcessor(final MetadataJsonProcessor metadataJsonProcessor) {
            this.metadataJsonProcessor = metadataJsonProcessor;
        }
    }

    private static void recomputeAutoClassifiedProperty(
            AutoClassifiedPropertyParams autoClassifiedPropertyParams,
            MetadataFormItem formItem,
            String primaryConnector,
            List<String> supportedCountryCodesList
    ) {
        if (CONTAINS_A_URL.equalsIgnoreCase(primaryConnector)) {
            recomputeContainsAUrlProperty(autoClassifiedPropertyParams, formItem);
        } else if (CONTAINS_AN_EMAIL.equalsIgnoreCase(primaryConnector)) {
            recomputeContainsAnEmailProperty(autoClassifiedPropertyParams, formItem);
        } else if (constructPrimaryConnector(CONTENT_METADATA_EMAILS).equalsIgnoreCase(primaryConnector)) {
            recomputeEmailsProperty(autoClassifiedPropertyParams, formItem);
        } else if (CONTAINS_A_PHONE_NUMBER.equalsIgnoreCase(primaryConnector)) {
            recomputeContainsAPhoneNumberProperty(autoClassifiedPropertyParams, formItem, supportedCountryCodesList);
        } else if (constructPrimaryConnector(CONTENT_METADATA_PHONES).equalsIgnoreCase(primaryConnector)) {
            recomputePhoneNumbersProperty(autoClassifiedPropertyParams, formItem, supportedCountryCodesList);
        } else if (CONTAINS_A_FAX_NUMBER.equalsIgnoreCase(primaryConnector)) {
            recomputeContainsAFaxNumberProperty(autoClassifiedPropertyParams, formItem, supportedCountryCodesList);
        } else if (constructPrimaryConnector(CONTENT_METADATA_FAXES).equalsIgnoreCase(primaryConnector)) {
            recomputeFaxNumbersProperty(autoClassifiedPropertyParams, formItem, supportedCountryCodesList);
        } else if (CONTAINS_VARIABLES.equalsIgnoreCase(primaryConnector)) {
            recomputeContainsVariablesProperty(autoClassifiedPropertyParams, formItem);
        } else if (VARIABLES.equalsIgnoreCase(primaryConnector)) {
            recomputeVariablesProperty(autoClassifiedPropertyParams, formItem);
        } else if (URLS.equalsIgnoreCase(primaryConnector)) {
            recomputeUrlsProperty(autoClassifiedPropertyParams, formItem);
        } else if (STYLE.equalsIgnoreCase(primaryConnector)) {
            recomputeStylesProperty(autoClassifiedPropertyParams, formItem);
        }
    }

    private static void setValueAndPrimaryConnector(MetadataFormItem formItem, String value) {
        formItem.setValue(value);
        formItem.getItemDefinition().setOriginTypeId(MetadataFormItemOriginTypeEnum.CONTENT_PARSED.getId());
        formItem.setPrimaryConnector(formItem.getItemDefinition().getPrimaryConnector());
        formItem.save(false);
    }

    private static void recomputeContainsAUrlProperty(AutoClassifiedPropertyParams autoClassifiedPropertyParams, MetadataFormItem formItem) {
        String markup = autoClassifiedPropertyParams.markupContent;
        if (StringUtils.isEmpty(markup)) {
            return;
        }

        MetadataJson metadataJson = autoClassifiedPropertyParams.metadataJsonProcessor.computeContainsURLsMetadata(markup);
        try {
            final ObjectMapper mapper = new ObjectMapper();
            setValueAndPrimaryConnector(formItem, mapper.writeValueAsString(metadataJson));
        } catch (JsonProcessingException e) {
            log.error("Error while recalculating contains fax metadata " + e);
        }
    }

    private static void recomputeContainsAnEmailProperty(AutoClassifiedPropertyParams autoClassifiedPropertyParams, MetadataFormItem formItem) {
        String markup = autoClassifiedPropertyParams.markupContent;
        if (StringUtils.isEmpty(markup)) {
            return;
        }
        MetadataJson metadataJson = autoClassifiedPropertyParams.metadataJsonProcessor.computeContainsEmailMetadata(markup);
        try {
            final ObjectMapper mapper = new ObjectMapper();
            setValueAndPrimaryConnector(formItem, mapper.writeValueAsString(metadataJson));
        } catch (JsonProcessingException e) {
            log.error("Error while recalculating contains email metadata " + e);
        }
    }

    private static void recomputeEmailsProperty(AutoClassifiedPropertyParams autoClassifiedPropertyParams, MetadataFormItem formItem) {
        String markup = autoClassifiedPropertyParams.markupContent;
        if (StringUtils.isEmpty(markup)) {
            return;
        }
        MetadataJson metadataJson = autoClassifiedPropertyParams.metadataJsonProcessor.computeEmailsMetadata(markup);
        try {
            final ObjectMapper mapper = new ObjectMapper();
            String metaString = mapper.writeValueAsString(metadataJson);
            if (!EMPTY_JSON.equals(metaString)) {
                setValueAndPrimaryConnector(formItem, metaString);
            } else {
                setValueAndPrimaryConnector(formItem, StringUtils.EMPTY);
            }
        } catch (JsonProcessingException e) {
            log.error("Error while recalculating emails metadata " + e);
    }
    }


    private static void recomputeContainsAPhoneNumberProperty(AutoClassifiedPropertyParams autoClassifiedPropertyParams, MetadataFormItem formItem,  List<String> supportedCountryCodesList) {
        String markup = autoClassifiedPropertyParams.markupContent;
        if (StringUtils.isEmpty(markup)) {
            return;
        }
        String markupWithoutVarTags = computeMarkupWithoutVarTags(markup);
        MetadataJson metadataJson = autoClassifiedPropertyParams.metadataJsonProcessor.computeContainsPhoneNumberMetadata(markupWithoutVarTags, supportedCountryCodesList);
        try {
            final ObjectMapper mapper = new ObjectMapper();
           setValueAndPrimaryConnector(formItem, mapper.writeValueAsString(metadataJson));
        } catch (JsonProcessingException e) {
            log.error("Error while recalculating contains phone metadata " + e);
        }
    }


    private static void recomputePhoneNumbersProperty(AutoClassifiedPropertyParams autoClassifiedPropertyParams, MetadataFormItem formItem,  List<String> supportedCountryCodesList) {
        String markup = autoClassifiedPropertyParams.markupContent;
        if (StringUtils.isEmpty(markup)) {
            return;
        }
        String markupWithoutVarTags = computeMarkupWithoutVarTags(markup);
        MetadataJson metadataJson = autoClassifiedPropertyParams.metadataJsonProcessor.computePhoneNumberMetadata(markupWithoutVarTags, supportedCountryCodesList);

        try {
            final ObjectMapper mapper = new ObjectMapper();
            String metaString = mapper.writeValueAsString(metadataJson);
            if (!EMPTY_JSON.equals(metaString)) {
                setValueAndPrimaryConnector(formItem, metaString);
            } else {
                setValueAndPrimaryConnector(formItem, StringUtils.EMPTY);
            }
        } catch (JsonProcessingException e) {
            log.error("Error while recalculating phone numbers metadata " + e);
    }

    }

    private static void recomputeFaxNumbersProperty(AutoClassifiedPropertyParams autoClassifiedPropertyParams, MetadataFormItem formItem,  List<String> supportedCountryCodesList) {
        String markup = autoClassifiedPropertyParams.markupContent;
        if (StringUtils.isEmpty(markup)) {
            return;
        }
        String markupWithoutVarTags = computeMarkupWithoutVarTags(markup);
        MetadataJson metadataJson = autoClassifiedPropertyParams.metadataJsonProcessor.computeFaxNumberMetadata(markupWithoutVarTags, supportedCountryCodesList);
        try {
            final ObjectMapper mapper = new ObjectMapper();
            String metaString = mapper.writeValueAsString(metadataJson);
            if (!EMPTY_JSON.equals(metaString)) {
                setValueAndPrimaryConnector(formItem, metaString);
            } else {
                setValueAndPrimaryConnector(formItem, StringUtils.EMPTY);
            }
        } catch (JsonProcessingException e) {
            log.error("Error while recalculating fax numbers metadata " + e);
        }

    }


    private static void recomputeContainsAFaxNumberProperty(AutoClassifiedPropertyParams autoClassifiedPropertyParams, MetadataFormItem formItem,  List<String> supportedCountryCodesList) {
        String markup = autoClassifiedPropertyParams.markupContent;
        if (StringUtils.isEmpty(markup)) {
            return;
        }
        String markupWithoutVarTags = computeMarkupWithoutVarTags(markup);
        MetadataJson metadataJson = autoClassifiedPropertyParams.metadataJsonProcessor.computeContainsFaxNumberMetadata(markupWithoutVarTags, supportedCountryCodesList);
        try {
            final ObjectMapper mapper = new ObjectMapper();
            setValueAndPrimaryConnector(formItem, mapper.writeValueAsString(metadataJson));
        } catch (JsonProcessingException e) {
            log.error("Error while recalculating contains URL metadata " + e);
        }
    }

    private static void recomputeContainsVariablesProperty(AutoClassifiedPropertyParams autoClassifiedPropertyParams, MetadataFormItem formItem) {
        String markupContent = autoClassifiedPropertyParams.markupContent;
        if (StringUtils.isEmpty(markupContent)) {
            return;
        }
        try {
          Elements variables = extractVariablesElements(markupContent);
          MetadataJson metadataJson = !variables.isEmpty() ?
                  MetadataUtil.constructSingleValueMetadataJson(markupContent, CONTENT_METADATA_HAS_VARIABLES, LABEL_CONTAINS_VARIABLES, TRUE, BOOLEAN) :
                  MetadataUtil.constructSingleValueMetadataJson(markupContent, CONTENT_METADATA_HAS_VARIABLES, LABEL_CONTAINS_VARIABLES, FALSE, BOOLEAN);

            final ObjectMapper mapper = new ObjectMapper();
            setValueAndPrimaryConnector(formItem, mapper.writeValueAsString(metadataJson));
        } catch (Exception ex) {
            log.error(ex);
        }
    }

    private static Elements extractVariablesElements(String markupContent) {
        Document document = Jsoup.parse(markupContent);
        document.outputSettings().prettyPrint(false);
        return document.select(MPR_VARIABLE);
    }

    private static void recomputeVariablesProperty(AutoClassifiedPropertyParams autoClassifiedPropertyParams, MetadataFormItem formItem) {
        String markupContent = autoClassifiedPropertyParams.markupContent;
        if (StringUtils.isEmpty(markupContent)) {
            return;
        }

        try {
            MetadataJson metadataJson;
            List<MetadataJsonItem> items = new ArrayList<>();
            Elements variables = extractVariablesElements(markupContent);
            for (Element currentVariable : variables) {
                String hyperText;
                String hyperElemText;
                if(markupContent.contains("<br />")) {
                    hyperText = getFormattedTextForElement(currentVariable).replace("<br>", "<br />");
                    hyperElemText = currentVariable.toString().replace("<br>", "<br />");
                } else {
                    hyperText = getFormattedTextForElement(currentVariable).replace("<br>", "<br/>");
                    hyperElemText = currentVariable.toString().replace("<br>", "<br/>");
                }

                int startIndex = markupContent.indexOf(hyperElemText) + hyperElemText.indexOf(hyperText);
                int endIndex = startIndex + hyperText.length() - 1;
                String varType =  currentVariable.attr("type");
                if(isEmpty(varType)) {
                    continue;
                }
                VariablesTypeEnum variablesTypeEnum = VariablesTypeEnum.valueOf(varType);
                String variableName = variablesTypeEnum.getStartTag() + currentVariable.text() + variablesTypeEnum.getEndTag();

                MetadataJsonItemProperty itemProperty = MetadataJsonItemProperty.builder().name(CONTENT_METADATA_VARIABLES).label(LABEL_CONTAINS_VARIABLES).value(variableName).type(STRING).build();
                MetadataJsonItem metadataJsonItem = MetadataJsonItem.builder().start(startIndex).end(endIndex).properties(Collections.singletonList(itemProperty)).build();
                items.add(metadataJsonItem);
            }
            if(!items.isEmpty()) {
                 metadataJson = MetadataJson.builder().name(CONTENT_METADATA_VARIABLES).label(LABEL_CONTAINS_VARIABLES).metadataJsonItems(items).build();
                final ObjectMapper mapper = new ObjectMapper();
                setValueAndPrimaryConnector(formItem, mapper.writeValueAsString(metadataJson));
            }

        } catch (Exception ex) {
            log.error("Error while recalculating variables metadata " + ex);
        }
    }

    private static void recomputeUrlsProperty(AutoClassifiedPropertyParams autoClassifiedPropertyParams, MetadataFormItem formItem) {
        String markupContent = autoClassifiedPropertyParams.markupContent;
        if (StringUtils.isEmpty(markupContent)) {
            return;
        }
        MetadataJson metadataJson =  autoClassifiedPropertyParams.metadataJsonProcessor.computeURLsMetadata(markupContent);
        try {
            final ObjectMapper mapper = new ObjectMapper();
            String metaString = mapper.writeValueAsString(metadataJson);
            if (!EMPTY_JSON.equals(metaString)) {
                setValueAndPrimaryConnector(formItem, metaString);
            } else {
                setValueAndPrimaryConnector(formItem, StringUtils.EMPTY);
            }
        } catch (JsonProcessingException e) {
            log.error("Error while recalculating URLs metadata " + e);
        }
    }

    private static void recomputeStylesProperty(AutoClassifiedPropertyParams autoClassifiedPropertyParams, MetadataFormItem formItem) {
        String markup = autoClassifiedPropertyParams.markupContent;
        if (StringUtils.isEmpty(markup)) {
            return;
        }
        MetadataJson metadataJson = autoClassifiedPropertyParams.metadataJsonProcessor.computeStyleMetadataFromMarkup(markup);
        try {
            final ObjectMapper mapper = new ObjectMapper();
            String metaString = mapper.writeValueAsString(metadataJson);
            if (!EMPTY_JSON.equals(metaString)) {
                setValueAndPrimaryConnector(formItem, metaString);
            }
        } catch (JsonProcessingException e) {
            log.error("Error while recalculating contains phone metadata " + e);
        }
    }

    private void clearPositionalMetadata(MetadataForm parsedContentForm) {
        if (parsedContentForm == null) {
            return;
        }

        Set<MetadataFormItem> formItems = parsedContentForm.getFormItems();
        if (CollectionUtils.isEmpty(formItems)) {
            return;
        }

        List<MetadataFormItem> formItemsToBlankOut = new LinkedList<>();

        formItems.forEach(formItem -> {
            MetadataFormItemDefinition itemDefinition = formItem.getItemDefinition();
            if (itemDefinition == null) {
                return;
            }

            String primaryConnector = itemDefinition.getPrimaryConnector();
            if (StringUtils.isEmpty(primaryConnector)) {
                return;
            }

            String primaryConnectorLowerCase = primaryConnector.toLowerCase();
            if (CONTENT_POSITIONAL_PROPS_LOWERCASE.contains(primaryConnectorLowerCase)) {
                formItemsToBlankOut.add(formItem);
            }
        });

        if (CollectionUtils.isNotEmpty(formItemsToBlankOut)) {
            for (MetadataFormItem formItemToBlankOut : formItemsToBlankOut) {
                formItemToBlankOut.setValue(StringUtils.EMPTY);
                formItemToBlankOut.save(false);
            }
        }

        parsedContentForm.save();
    }

    private void processChangedContents(
            BulkRationalizerActionServiceRequest request,
            Map<RationalizerDocumentContent, TextAndMarkupForLastAction> changedDocumentContents,
            Map<RationalizerSharedContent, TextAndMarkupForLastAction> changedSharedContents,
            RationalizerActionTypeEnum actionType
    ) {
        Map<String, Integer> elasticSearchGuidToContentsActionsMap = request.getElasticSearchGuidToContentsActionsMap();
        Map<String, String> elasticSearchGuidToMarkupContentsMap = request.getElasticSearchGuidToMarkupContentsMap();
        List<RationalizerContent> rationalizerContents = request.getRationalizerContents();

        for (RationalizerContent content : rationalizerContents) {
            String elasticSearchGuid = content.buildElasticSearchGuid();
            Integer contentAction = elasticSearchGuidToContentsActionsMap.get(elasticSearchGuid);
            if (contentAction != null && contentAction == -1) {
                // Request for delete content. Will be processed separately.
                continue;
            }

            String updatedMarkupContent = getNonNullOrEmptyString(elasticSearchGuidToMarkupContentsMap.get(elasticSearchGuid));
            if(StringUtils.isEmpty(updatedMarkupContent)) {
                updatedMarkupContent = EMPTY_MARKUP_CONTENT;
            }
            String databaseMarkupContent = getNonNullOrEmptyString(content.buildDisplayedMarkupContent());
            if (StringUtils.equals(getUnformattedString(databaseMarkupContent), getUnformattedString(updatedMarkupContent))) {
                continue;
            }
            if(RationalizerActionTypeEnum.SPLIT.equals(actionType)) {
                updatedMarkupContent = constructMarkupAfterSplit(content, updatedMarkupContent);
            }
            String updatedTextContent = ContentObjectContentUtil.getUnformattedTextContentForMarcie(updatedMarkupContent);

            final RationalizerSharedContent sharedContent = content.computeRationalizerSharedContent();
            RationalizerActionTypeEnum finalActionType = actionType;
            String finalUpdatedMarkupContent = updatedMarkupContent;
            if (sharedContent == null) {
                changedDocumentContents.put( (RationalizerDocumentContent) content, new TextAndMarkupForLastAction() {{
                    setText(updatedTextContent);
                    setMarkup(finalUpdatedMarkupContent);
                    setLastActionType(finalActionType);
                }});

            } else {
                changedSharedContents.put(sharedContent, new TextAndMarkupForLastAction() {{
                    setText(updatedTextContent);
                    setMarkup(finalUpdatedMarkupContent);
                    setLastActionType(finalActionType);
                }});

            }
        }

        RationalizerDocument rationalizerDocument = request.getRationalizerDocument();
        if (rationalizerDocument.getParsedDocumentForm() != null) {
            rationalizerDocument.getParsedDocumentForm().save();
        }
    }

    private String constructMarkupAfterSplit(RationalizerContent content, String updatedMarkupContent) {
        if(updatedMarkupContent.endsWith(EMPTY_MARKUP_CONTENT) || updatedMarkupContent.startsWith(EMPTY_MARKUP_CONTENT)) {
            updatedMarkupContent = updatedMarkupContent.replaceAll("<p></p>$","");
            updatedMarkupContent = updatedMarkupContent.replaceAll("^<p></p>","");
        }
        //If a user splits in the middle of a variable/hyperlink, variable/hyperlink will not be maintained in either the original or split content
        String varsInMetadataString = content.findMetadataValueByItemDefinitionPrimaryConnector(CONTENT_METADATA_VARIABLES);
        updatedMarkupContent = updateMarkupInCaseVariablesSplit(varsInMetadataString, updatedMarkupContent);
        String urlsInMetadataString = content.findMetadataValueByItemDefinitionPrimaryConnector(CONTENT_METADATA_URLS);
        updatedMarkupContent = updateMarkupInCaseHyperlinkSplit(urlsInMetadataString, updatedMarkupContent);
        return updatedMarkupContent;
    }

    private void updateDocumentFieldValueForAllContentsDelayed(RationalizerElasticSearchHandler rationalizerElasticSearchHandler, RationalizerDocument updatedRationalizerDocument,
                                                               String fieldName, List<String> fieldValuesList
    ) {

        final int listBatchSize = 50;
        List<String> contentIdsToDocTagsUpdate = new ArrayList<>();
        updatedRationalizerDocument.shallowCopyOfContents().forEach(c -> contentIdsToDocTagsUpdate.add(c.buildElasticSearchGuid()));
        for (int i = 0; i < contentIdsToDocTagsUpdate.size(); i = i + listBatchSize) {
            int rightMargin = i + listBatchSize > contentIdsToDocTagsUpdate.size() ? contentIdsToDocTagsUpdate.size() : i + listBatchSize;
            List<String> crtGuidBatch = contentIdsToDocTagsUpdate.subList(i, rightMargin);
            rationalizerElasticSearchHandler.updateFieldWithSameValueForAllContentsDelayed(fieldName, fieldValuesList, crtGuidBatch);
        }
    }

    private String getCurrentMetadataItemValue(MetadataFormItem currentItem) throws ParseException {
        String currentValue = currentItem.getValue();
        if (currentItem.getItemDefinition().getTypeId() == MetadataFormItemType.ID_FILE) {
            Long sandboxFileId = currentItem.getSandboxFileId();
            if (sandboxFileId != null) {
                // New upload
                SandboxFile dataSb = SandboxFile.findById(sandboxFileId);
                currentValue = dataSb.getFileName();
            } else {
                if (currentItem.getUploadedFile() != null) {
                    currentValue = currentItem.getUploadedFile().getFileName();
                }
            }
        } else if (currentItem.getItemDefinition().getTypeId() == MetadataFormItemType.ID_DATE_DAY_MONTH_YEAR) {
            if (currentItem.getDateStrInput() != null && !currentItem.getDateStrInput().isEmpty()) {
                Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
                SimpleDateFormat df = new SimpleDateFormat(DateUtil.DATE_FORMAT, DateUtil.formatAbbricationMonths(new DateFormatSymbols(locale)));
                Date date = df.parse(currentItem.getDateStrInput());
                df.applyPattern("yyyy-MM-dd");
                currentValue = df.format(date);
            } else {
                currentValue = null;
            }
        } else if (currentItem.getItemDefinition().getTypeId() == MetadataFormItemType.ID_DATE_MONTH_YEAR) {
            if (currentItem.getDateStrInput() != null && !currentItem.getDateStrInput().isEmpty()) {
                Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
                SimpleDateFormat df = new SimpleDateFormat("MMM yyyy", DateUtil.formatAbbricationMonths(new DateFormatSymbols(locale)));
                Date date = df.parse(currentItem.getDateStrInput());
                df.applyPattern("yyyy-MM");
                currentValue = df.format(date);
            } else {
                currentValue = null;
            }
        }
        return currentValue;
    }

    private void addOrUpdateMetadata(MetadataFormDefinition parsedFormDefinition, MetadataForm parsedForm,
                                     List<MetadataFormItemDefinition> autoGeneratedFormItemDefinitions,
                                     String metadataConnector, String metadataValue, boolean isFromManifest) {

        if (parsedFormDefinition == null || metadataConnector == null) {
            return;
        }
        Set<MetadataFormItem> formItems = parsedForm.getFormItems();
        boolean foundMetadataItem = false;
        for (MetadataFormItem formItem : formItems) {
            if (formItem.getItemDefinition().getPrimaryConnector().equalsIgnoreCase(metadataConnector)) {
                foundMetadataItem = true;
                break;
            }
        }
        MetadataFormItemDefinition formItemDefinition = parsedFormDefinition.findItemDefinitionByConnector(metadataConnector);
        if (!foundMetadataItem) {
            if (formItemDefinition == null) {
                formItemDefinition = new MetadataFormItemDefinition();
                formItemDefinition.setPrimaryConnector(metadataConnector.replace(" ", "_"));
                formItemDefinition.setName(metadataConnector);
                formItemDefinition.setOrder(parsedFormDefinition.getFormItemDefinitions().size() + 1);
                formItemDefinition.setTypeId(MetadataFormItemType.ID_TEXT);
                formItemDefinition.setInputValidationTypeId(MetadataFormInputValidationType.ID_NONE);
                formItemDefinition.setAutoGenerateOnImport(true);
                formItemDefinition.setMetadataFormDefinition(parsedFormDefinition);
                parsedFormDefinition.getFormItemDefinitions().add(formItemDefinition);
            } else {
                if (autoGeneratedFormItemDefinitions.contains(formItemDefinition)) {
                    autoGeneratedFormItemDefinitions.remove(formItemDefinition);
                }
            }

            if (parsedForm.getFormDefinition() == null) {
                parsedForm.setFormDefinition(parsedFormDefinition);
            }

            MetadataFormItem formItem = new MetadataFormItem();
            formItem.setValue(metadataValue);
            formItem.setIsManifestItem(isFromManifest);
            formItem.setItemDefinition(formItemDefinition);
            parsedForm.getFormItems().add(formItem);
        } else {

            for (MetadataFormItem formItem : formItems) {
                if (formItem.getItemDefinition().getPrimaryConnector().equalsIgnoreCase(metadataConnector)) {
                    String existingValue = formItem.getValue();
                    if (!metadataValue.equals(existingValue)) {
                        formItem.setValue(metadataValue);
                    }
                    break;
                }
            }

        }
    }


    private static String constructPrimaryConnector(String contentMetadataHasUrl) {
        return contentMetadataHasUrl.replace(" ", "_");
    }

    private static String getUnformattedString(String text) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        text = text.replaceAll("\\r?\\n", "\n");
        return text;
    }

    public void validate(ServiceExecutionContext context) {
    }

    public static SimpleExecutionContext createContextForNewApplication(String applicationName,
                                                                        String metatags,
                                                                        String description) {
        return createContext(ACTION_NEW_APPLICATION,
                -1,
                applicationName,
                metatags,
                description,
                null,
                -1,
                -1,
                null,
                null,
                null,
                null,
                null,
                null,
                false,
                null,
                null,
                null
        );
    }

    public static SimpleExecutionContext createContextForUpdateApplication(long rationalizerApplicationId,
                                                                           String applicationName,
                                                                           String metatags,
                                                                           String description,
                                                                           Set<MetadataFormItemDefinition> queryFilterFormItemDefinitions,
                                                                           List<DashboardFilter> dashboardFilters,
                                                                           Boolean reSync) {
        return createContext(ACTION_UPDATE_APPLICATION,
                rationalizerApplicationId,
                applicationName,
                metatags,
                description,
                null,
                -1,
                -1,
                null,
                null,
                null,
                null,
                queryFilterFormItemDefinitions,
                dashboardFilters,
                reSync,
                null,
                null,
                null
        );
    }

    public static SimpleExecutionContext createContextForNewDocument(String documentName, long rationalizerApplicationId) {
        return createContext(ACTION_NEW_DOCUMENT,
                rationalizerApplicationId,
                documentName,
                null,
                null,
                null,
                -1,
                -1,
                null,
                null,
                null,
                null,
                null,
                null,
                false,
                null,
                null,
                null
        );
    }

    public static SimpleExecutionContext createContextForUpdateDocument(RationalizerDocument rationalizerDocument,
                                                                        List<RationalizerDocumentContent> documentContentsList,
                                                                        List<Integer> contentActions,
                                                                        List<String> markupContent,
                                                                        String initialName,
                                                                        String initialMetatags
    ) {
        return createContext(ACTION_UPDATE_DOCUMENT,
                -1,
                initialName,
                initialMetatags,
                null,
                rationalizerDocument,
                -1,
                -1,
                null,
                null,
                documentContentsList,
                contentActions,
                null,
                null,
                false,
                markupContent,
                null,
                null
        );
    }

    public static SimpleExecutionContext createContextForMergeDocumentContents(RationalizerDocument rationalizerDocument,
                                                                               List<RationalizerDocumentContent> documentContentsList,
                                                                               List<Integer> contentActions,
                                                                               List<String> markupContent,
                                                                               String initialName,
                                                                               String initialMetatags,
                                                                               List<String> selectedIds
    ) {
        return createContext(ACTION_MERGE_DOCUMENT_CONTENTS,
                -1,
                initialName,
                initialMetatags,
                null,
                rationalizerDocument,
                -1,
                -1,
                null,
                null,
                documentContentsList,
                contentActions,
                null,
                null,
                false,
                markupContent,
                null,
                selectedIds
        );
    }

    public static SimpleExecutionContext createContextForAddDocumentContent(RationalizerDocument rationalizerDocument,
                                                                            int addContentCount,
                                                                            int insertAfterOrder,
                                                                            String insertedMarkupContent,
                                                                            List<RationalizerDocumentContent> documentContentsList,
                                                                            List<Integer> contentActions,
                                                                            List<String> markupContent,
                                                                            String initialName,
                                                                            String initialMetatags) {
        return createContext(ACTION_ADD_DOCUMENT_CONTENT,
                -1,
                initialName,
                initialMetatags,
                null,
                rationalizerDocument,
                addContentCount,
                insertAfterOrder,
                insertedMarkupContent,
                null,
                documentContentsList,
                contentActions,
                null,
                null,
                false,
                markupContent,
                null,
                null
        );
    }

    public static SimpleExecutionContext createContextForSplitDocumentContent(RationalizerDocument rationalizerDocument,
                                                                              int addContentCount,
                                                                              int insertAfterOrder,
                                                                              String insertedMarkupContent,
                                                                              List<RationalizerDocumentContent> documentContentsList,
                                                                              List<Integer> contentActions,
                                                                              List<String> markupContent,
                                                                              String initialName,
                                                                              String initialMetatags
    ) {
        return createContext(ACTION_SPLIT_DOCUMENT_CONTENT,
                -1,
                initialName,
                initialMetatags,
                null,
                rationalizerDocument,
                addContentCount,
                insertAfterOrder,
                insertedMarkupContent,
                null,
                documentContentsList,
                contentActions,
                null,
                null,
                false,
                markupContent,
                null,
                null
        );
    }

    public static SimpleExecutionContext createContextForUpdateDocumentMetadata(RationalizerDocument rationalizerDocument) {
        return createContext(ACTION_UPDATE_DOCUMENT_METADATA,
                -1,
                null,
                null,
                null,
                rationalizerDocument,
                -1,
                -1,
                null,
                null,
                null,
                null,
                null,
                null,
                false,
                null,
                null,
                null
        );
    }

    public static SimpleExecutionContext createContextForUpdateDocumentContentMetadata(
            RationalizerDocumentContent rationalizerDocumentContent,
            Map<Long, String> contentMetadataPreValueMap
    ) {
        return createContext(ACTION_UPDATE_DOCUMENT_CONTENT_METADATA,
                -1,
                null,
                null,
                null,
                null,
                -1,
                -1,
                null,
                rationalizerDocumentContent,
                null,
                null,
                null,
                null,
                false,
                null,
                contentMetadataPreValueMap,
                null
        );
    }

    public static SimpleExecutionContext createContextForUpdateSharedContentMetadata(
            RationalizerSharedContent rationalizerSharedContent,
            Map<Long, String> contentMetadataPreValueMap
    ) {
        return createContext(ACTION_UPDATE_SHARED_CONTENT_METADATA,
                -1,
                null,
                null,
                null,
                null,
                -1,
                -1,
                null,
                rationalizerSharedContent,
                null,
                null,
                null,
                null,
                false,
                null,
                contentMetadataPreValueMap,
                null
        );
    }

    public static SimpleExecutionContext createContextForInitDocumentContentOrder() {
        return createContext(ACTION_INIT_DOCUMENT_CONTENT_ORDER,
                -1,
                null,
                null,
                null,
                null,
                -1,
                -1,
                null,
                null,
                null,
                null,
                null,
                null,
                false,
                null,
                null,
                null
        );
    }

    public static SimpleExecutionContext createContext(int action,
                                                       long rationalizerApplicationId,
                                                       String name,
                                                       String metatags,
                                                       String description,
                                                       RationalizerDocument rationalizerDocument,
                                                       int addContentCount,
                                                       int insertAfterOrder,
                                                       String insertedMarkupContent,
                                                       RationalizerContent rationalizerContent,
                                                       List<RationalizerDocumentContent> documentContentsList,
                                                       List<Integer> contentActions,
                                                       Set<MetadataFormItemDefinition> queryFilterFormItemDefinitions,
                                                       List<DashboardFilter> dashboardFilters,
                                                       Boolean reSync,
                                                       List<String> markupContent,
                                                       Map<Long, String> contentMetadataPreValueMap,
                                                       List<String> selectedIds
    ) {
        SimpleExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        BulkRationalizerActionServiceRequest request = new BulkRationalizerActionServiceRequest();
        context.setRequest(request);

        request.setAction(action);

        request.setRationalizerApplicationId(rationalizerApplicationId);

        request.setName(name);
        request.setMetatags(metatags);
        request.setDescription(description);
        request.setRationalizerDocument(rationalizerDocument);
        request.setAddContentCount(addContentCount);
        request.setInsertAfterOrder(insertAfterOrder);
        request.setInsertedMarkupContent(insertedMarkupContent);
        request.setRationalizerContent(rationalizerContent);
        request.setRationalizerContents(documentContentsList);
        request.setContentActions(contentActions);
        request.setDashboardFilters(dashboardFilters);
        request.setReSync(reSync);
        request.setMarkupContent(markupContent);
        request.setContentMetadataPreValueMap(contentMetadataPreValueMap);

        request.setQueryFilterFormItemDefinitions(queryFilterFormItemDefinitions);

        request.setSelectedIds(selectedIds);

        SimpleServiceResponse serviceResp = new SimpleServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

    public static class AfterSplitTokenCleaner {
        private Pattern pattern;
        private int regexGroup;

        public AfterSplitTokenCleaner(Pattern pattern, int regexGroup) {
            this.pattern = pattern;
            this.regexGroup = regexGroup;
        }

        public String[] cleanupExtractedTokens(String[] tokens) {
            List<String> newTokens = new LinkedList<>();
            for (int i = 0; i < tokens.length; i++) {
                String token = tokens[i];

                Matcher matcher = pattern.matcher(token);
                if (matcher.matches()) {
                    token = matcher.group(regexGroup);
                }

                newTokens.add(token);
            }

            return newTokens.toArray(new String[0]);
        }
    }

    private static class TextAndMarkupForLastAction {
        private String text;
        private String markup;
        private RationalizerActionTypeEnum lastActionType;

        public void setText(String text) {
            this.text = text;
        }

        public void setMarkup(String markup) {
            this.markup = markup;
        }

        public RationalizerActionTypeEnum getLastActionType() {
            return lastActionType;
        }

        public void setLastActionType(RationalizerActionTypeEnum lastActionType) {
            this.lastActionType = lastActionType;
        }

    }
}
