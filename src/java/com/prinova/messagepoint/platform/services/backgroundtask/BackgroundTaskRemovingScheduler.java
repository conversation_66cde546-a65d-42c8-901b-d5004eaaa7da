package com.prinova.messagepoint.platform.services.backgroundtask;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.admin.StatusPollingBackgroundTaskService;
import com.prinova.messagepoint.util.*;
import org.apache.commons.logging.Log;

public class BackgroundTaskRemovingScheduler extends MessagePointRunnable {
    private static final Log log = LogUtil.getLog(BackgroundTaskRemovingScheduler.class);

	@Override
	public void performMainProcessing() {
        log.info("BackgroundTaskRemovingScheuler starts at: " + DateUtil.now());
		try {
            Node node = Node.getCurrentNode();
            String schema = node.getSchemaName();
            if(schema == null)
                log.info("Schema: POD MASTER");
            else
                log.info("Schema: " + schema);

            if(node.isDcsNode() || node.isOffline() || ! node.isEnabled()) {
                return;
            }

            ServiceExecutionContext context = StatusPollingBackgroundTaskService.createContextForRemoveOldTasks();
            Service service = MessagepointServiceFactory.getInstance()
                    .lookupService(StatusPollingBackgroundTaskService.SERVICE_NAME, StatusPollingBackgroundTaskService.class);
            service.execute(context);

		} catch (Exception e) {
			log.error("Error removing background tasks ", e);
		}
        log.info("BackgroundTaskRemovingScheuler finished at: " + DateUtil.now());
	}
	
}
