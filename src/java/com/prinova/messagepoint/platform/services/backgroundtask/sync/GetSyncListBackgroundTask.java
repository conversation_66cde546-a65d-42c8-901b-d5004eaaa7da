package com.prinova.messagepoint.platform.services.backgroundtask.sync;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.controller.AsyncTouchpointSyncListComparator;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.common.ContentSelectionStatusType;
import com.prinova.messagepoint.model.common.SyncObjectType;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItemDefinition;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.dataadmin.LookupTable;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.dataadmin.LookupTableVersionMapping;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataElement;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataElement;
import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.model.font.ListStyle;
import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.model.font.TextStyleFont;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.testing.DataFile;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.*;
import com.prinova.messagepoint.wtu.ReferencableObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Session;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.orm.hibernate5.SessionHolder;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigInteger;
import java.util.*;
import java.util.function.*;
import java.util.stream.Collectors;

public class GetSyncListBackgroundTask extends MessagePointRunnable {
    Long sourceDocumentId;
    Long sourceInstanceId;
    Long targetDocumentId;
    Long targetInstanceId;
    List<Long> languages;
    Boolean activeOnly;
    Boolean syncActiveOnly;
    String listKey = null;
    User user;
    Boolean includeNotVisibleTargetRules;

    Boolean includeRejectedChanges;

    JSONObject statusData;

    public static boolean isSyncVariantFeatureFlagEnabled = false;

    public final static String cacheKey = "syncList";
    public final static String statusKey = ":get_sync_list_status";

    public GetSyncListBackgroundTask(Long sourceDocumentId,
                                     Long sourceInstanceId,
                                     Long targetDocumentId,
                                     Long targetInstanceId,
                                     List<Long> languages,
                                     Boolean activeOnly,
                                     Boolean syncActiveOnly,
                                     User user,
                                     Boolean includeNotVisibleTargetRules,
                                     Boolean includeRejectedChanges,
                                     boolean syncVariantEnabled)
    {
        this.sourceDocumentId = sourceDocumentId;
        this.sourceInstanceId = sourceInstanceId;
        this.targetDocumentId = targetDocumentId;
        this.targetInstanceId = targetInstanceId;
        this.languages = languages;
        this.activeOnly = activeOnly;
        this.syncActiveOnly = syncActiveOnly;
        this.statusData = new JSONObject();

        this.user = user;

        this.listKey = RandomGUID.getGUID();
        this.includeNotVisibleTargetRules = includeNotVisibleTargetRules;
        this.includeRejectedChanges = includeRejectedChanges;

        statusData.put("key", listKey);
        statusData.put("startTimestamp", DateUtil.now());
        statusData.put("stage", "init");
        statusData.put("percentage", 0);

        this.isSyncVariantFeatureFlagEnabled = syncVariantEnabled;
        //"true".equalsIgnoreCase(ApplicationUtil.getProperty(SystemPropertyKeys.Data.KEY_EnableSyncVariants, null));

        saveStatus();
    }

    private void saveStatus() {
        if (StringUtils.isNotEmpty(cacheKey) && StringUtils.isNotEmpty(listKey)) {
            SyncCacheUtil.putValue(cacheKey + listKey + statusKey, statusData.toString());
        }
    }

    public JSONObject getStatus() {
        return statusData;
    }

    public static JSONObject getStatus(String listKey) {
        if(StringUtils.isEmpty(listKey)){
            return null;
        }

        JSONObject status = null;
        if (StringUtils.isNotEmpty(cacheKey) && StringUtils.isNotEmpty(listKey)) {
            String str = SyncCacheUtil.getValue(cacheKey + listKey + statusKey);
            if(StringUtils.isNotEmpty(str)){
                status = new JSONObject(str);
            }
        }
        return status;
    }

    /**
     * Get generated data by list key
     */
    public static JSONArray getGeneratedData(String listKey){
        JSONObject status = getStatus(listKey);
        return getGeneratedData(status);
    }

    /**
     * Get generated data from status object
     */
    public static JSONArray getGeneratedData(JSONObject status){
        JSONArray data = null;

        if (status != null) {
            if (status.get("stage") != null && status.get("stage") instanceof String && status.get("stage").equals("completed")) {
                if (status.get("data") != null && status.get("data") instanceof JSONArray) {
                    data = (JSONArray) status.get("data");
                }
            }
        }

        return data;
    }

    @Override
    public void performMainProcessing() throws Exception {
        Node sourceNode = Node.findById(sourceInstanceId);
        Node targetNode = Node.findById(targetInstanceId);

        User userInSourceSchema = user.getNodeUser(sourceNode);
        User userInTargetSchema = user.getNodeUser(targetNode);

        info("GetSyncListBackgroundTask starts");

        getSyncList(sourceDocumentId, sourceInstanceId, targetDocumentId, targetInstanceId, languages, activeOnly, syncActiveOnly, statusData, status->saveStatus(), userInSourceSchema, userInTargetSchema, includeNotVisibleTargetRules, includeRejectedChanges);
    }

    public static JSONObject getSyncList(Long sourceDocumentId,
                                   Long sourceInstanceId,
                                   Long targetDocumentId,
                                   Long targetInstanceId,
                                   List<Long> languages,
                                   Boolean activeOnly,
                                   Boolean syncActiveOnly,
                                   User user,
                                   Boolean includeNotVisibleTargetRules,
                                   Boolean includeRejectedChanges
    ) {
        Node sourceNode = Node.findById(sourceInstanceId);
        Node targetNode = Node.findById(targetInstanceId);

        User userInSourceSchema = user.getNodeUser(sourceNode);
        User userInTargetSchema = user.getNodeUser(targetNode);

        JSONObject statusData = new JSONObject();
        getSyncList(sourceDocumentId, sourceInstanceId, targetDocumentId, targetInstanceId, languages, activeOnly, syncActiveOnly, statusData, null, userInSourceSchema, userInTargetSchema, includeNotVisibleTargetRules, includeRejectedChanges);
        return statusData;
    }

    private static void getSyncList(Long sourceDocumentId,
                            Long sourceInstanceId,
                            Long targetDocumentId,
                            Long targetInstanceId,
                            List<Long> languages,
                            Boolean activeOnly,
                            Boolean syncActiveOnly,
                            JSONObject statusData,
                            Consumer<JSONObject> saveStatus,
                            User userInSourceSchema,
                            User userInTargetSchema,
                            Boolean includeNotVisibleTargetRules,
                            Boolean includeRejectedChanges
    ) {
        JSONArray data = new JSONArray();

        boolean inSameInstance = (sourceInstanceId == null && targetInstanceId == null) || (sourceInstanceId != null && targetInstanceId != null && sourceInstanceId.equals(targetInstanceId));
        Node sourceNode = inSameInstance ? Node.getCurrentNode() : Node.findById(sourceInstanceId);
        Node targetNode = inSameInstance ? sourceNode : Node.findById(targetInstanceId);
        String sourceSchema = sourceNode.getSchemaName();
        String targetSchema = targetNode.getSchemaName();

        if(isSyncVariantFeatureFlagEnabled) {
            boolean sourceSchemaReady = CloneHelper.queryInSchema(sourceSchema, () -> isSyncReady());
            if (!sourceSchemaReady) {
                info("GetSyncListBackgroundTask source schema is not ready");
            }

            boolean targetSchemaReady = CloneHelper.queryInSchema(targetSchema, () -> isSyncReady());
            if (!targetSchemaReady) {
                info("GetSyncListBackgroundTask target schema is not ready");
            }

            if (!(sourceSchemaReady && targetSchemaReady)) {
                statusData.put("stage", "unavailable");
                statusData.put("exception", "Schema not ready "
                    + (sourceSchemaReady ? ("source schema " + sourceSchema) : "")
                    + ((sourceSchemaReady && targetSchemaReady) ? " " : "")
                    + (targetSchemaReady ? ("target schema " + targetSchema) : "")
                );
                statusData.put("completedTimestamp", DateUtil.now());
                if (saveStatus != null) saveStatus.accept(statusData);
                return;
            }
        }

        Set<Long> languagesForSync = new HashSet<>();
        if (languages != null) languagesForSync.addAll(languages);
//
//        statusData.put("stage", "preparing");
//        statusData.put("percentage", 1);
//
//        if(saveStatus != null) saveStatus.accept(statusData);
//
//        try {
//            SyncTouchpointUtil.makeSureHashCalculated(sourceSchema);
//        } catch (Exception ex) {
//            throw new JSONException(ex);
//        }
//
//        statusData.put("stage", "preparing");
//        statusData.put("percentage", 5);
//        if(saveStatus != null) saveStatus.accept(statusData);
//
//        try {
//            SyncTouchpointUtil.makeSureHashCalculated(targetSchema);
//        } catch (Exception ex) {
//            throw new JSONException(ex);
//        }

        SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
        Session podMasterSession = HibernateUtil.getManager().getSession();
        SessionHolder sourceSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
        Session sourceSession = HibernateUtil.getManager().getSession();
        SessionHolder targetSessionHolder = HibernateUtil.getManager().openTemporarySession(targetSchema);
        Session targetSession = HibernateUtil.getManager().getSession();

        CloneHelper.startCrossInstanceClone();
        CloneHelper.setIsSynchronizing(true);
        CloneHelper.addSession(null, podMasterSession);
        CloneHelper.addSession(targetSchema, targetSession);
        CloneHelper.addSession(sourceSchema, sourceSession);

        statusData.put("stage", "searching");
        statusData.put("percentage", 1);
        if(saveStatus != null) saveStatus.accept(statusData);

        try {
            Document sourceDocument = CloneHelper.queryInSchema(sourceSchema, () -> Document.findById(sourceDocumentId));
            Document targetDocument = CloneHelper.queryInSchema(targetSchema, () -> Document.findById(targetDocumentId));

            Map<Long, List<String>> allSourceObjectsWithDependenciesNeedExisting = new HashMap<>(); // ObjectId to List of objects which were referenced in the Object which should be created if not exists in target instance.
            Map<Long, List<String>> allSourceObjectsWithDependenciesSyncWhenChange = new HashMap<>(); // ObjectId to List of objects which were referenced in the Object which must be sync'ed when changed.

            Map<Long, List<String>> targetObjectIDsWithReferencingMap = new HashMap<>(); // ObjectId to List of objects which references the object.

            Map<Long, List<ReferencableObject>> allTargetGroupReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allTargetRuleReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allParameterGroupReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allVariableReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allDataSourceReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allLookupTableReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allDataCollectionReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allTextStyleReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allTextStyleFontReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allDataFileReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allDataResourceReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allCompositionFileSetReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allChannelConfigReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allListStyleReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allParagraphStyleReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allMetadataFormDefinitionReferencesMapInTargetSchema = new HashMap<>();
            Map<Long, List<ReferencableObject>> allTouchVariantSettingReferencesMapInTargetSchema = new HashMap<>();

            boolean sourceVariantWorkflowEnabled = CloneHelper.queryInSchema(sourceSchema, ()-> sourceDocument.isEnabledForVariantWorkflow());
            boolean targetVariantWorkflowEnabled = CloneHelper.queryInSchema(targetSchema, ()-> targetDocument.isEnabledForVariantWorkflow());

            boolean isConnectedTouchpoint = CloneHelper.queryInSchema(sourceSchema, ()-> sourceDocument.isConnectedEnabled());

            info("find source content objects ref map");
            Map<Integer, Map<Long, List<String>>> sourceContentObjectIDsWithDependencies = CloneHelper.queryInSchema(sourceSchema, ()-> Content.findContentObjectIDsWithDependencies(activeOnly && syncActiveOnly));

            info("init query source message ref map");
            if(sourceContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_MESSAGE)) {
                allSourceObjectsWithDependenciesNeedExisting.putAll(sourceContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_MESSAGE));
            }
            if(sourceContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT)) {
                allSourceObjectsWithDependenciesNeedExisting.putAll(sourceContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT));
            }
            if(sourceContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_LOCAL_IMAGE)) {
                allSourceObjectsWithDependenciesNeedExisting.putAll(sourceContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_LOCAL_IMAGE));
            }

            if(CloneHelper.queryInSchema(sourceSchema, () -> sourceDocument.isEnabledForVariantWorkflow())) {
                Map<Integer, Map<Long, List<String>>> sourceVariantIDsWithDependencies = CloneHelper.queryInSchema(sourceSchema, ()-> Content.findVariantIDsWithDependencies(sourceDocumentId, activeOnly && syncActiveOnly));
                if(sourceVariantIDsWithDependencies.containsKey(SyncObjectType.ID_VARIANT)) {
                    Map<Long, List<String>> variantIDToDepList = sourceVariantIDsWithDependencies.get(SyncObjectType.ID_VARIANT);
                    allSourceObjectsWithDependenciesNeedExisting.putAll(variantIDToDepList);

                    for(Long variantID : variantIDToDepList.keySet()) {
                        TouchpointSelection sourceTouchpointSelection = CloneHelper.queryInSchema(sourceSchema, ()->TouchpointSelection.findById(variantID));
                        String touchpointSelectionDna = CloneHelper.queryInSchema(sourceSchema, ()->sourceTouchpointSelection.getDna());
                        TouchpointSelection targetTouchpointSelection = CloneHelper.queryInSchema(targetSchema, ()->TouchpointSelection.findByDnaAndDocument(touchpointSelectionDna, targetDocument));
                        if(CloneHelper.queryInSchema(sourceSchema, ()->sourceTouchpointSelection.getHasActiveCopy())
                            && CloneHelper.queryInSchema(targetSchema, ()->
                                targetTouchpointSelection == null ||
                                ((!targetTouchpointSelection.getHasActiveCopy()) && targetTouchpointSelection.getHasWorkingCopy()))
                        ) {
                            List<String> needExistsDepList = variantIDToDepList.get(variantID);
                            List<String> needActivation = new ArrayList<>();
                            for(String depItem : needExistsDepList) {
                                if(depItem.startsWith("ContentObject")
                                    || depItem.startsWith("Message")
                                    || depItem.startsWith("LocalSmartText")
                                    || depItem.startsWith("LocalImage")
                                ) {
                                    int hyphenLoc = depItem.indexOf("-");
                                    if(hyphenLoc > 0) {
                                        long contentObjectId = Long.parseLong(depItem.substring(hyphenLoc + 1));
                                        ContentObject sourceContentObject = CloneHelper.queryInSchema(sourceSchema, ()->ContentObject.findById(contentObjectId));
                                        String contentObjectDna = sourceContentObject.getDna();
                                        ContentObject targetContentObject = CloneHelper.queryInSchema(targetSchema, ()->ContentObject.findByDnaAndDocument(contentObjectDna, targetDocument));
                                        if(targetContentObject != null && CloneHelper.queryInSchema(targetSchema, ()->(!targetContentObject.hasActiveData()) && targetContentObject.hasWorkingData())) {
                                            needActivation.add(depItem);
                                        }
                                    }
                                }
                            }

                            if(! needActivation.isEmpty()) {
                                List<String> syncWhenChanged = allSourceObjectsWithDependenciesSyncWhenChange.get(variantID);
                                if(syncWhenChanged == null) {
                                    syncWhenChanged = new ArrayList<>();
                                    allSourceObjectsWithDependenciesSyncWhenChange.put(variantID, syncWhenChanged);
                                }
                                syncWhenChanged.addAll(needActivation);
                            }
                        }
                    }

                    allSourceObjectsWithDependenciesSyncWhenChange.putAll(sourceVariantIDsWithDependencies.get(SyncObjectType.ID_VARIANT));
                }
            }

            if(! inSameInstance) {
                info("init query source smart texts ref map");
                if(sourceContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT)) {
                    allSourceObjectsWithDependenciesNeedExisting.putAll(sourceContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT));
                }
                if(sourceContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_GLOBAL_IMAGE)) {
                    allSourceObjectsWithDependenciesNeedExisting.putAll(sourceContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_GLOBAL_IMAGE));
                }
            }

            BiConsumer<String, Map<Long, List<String>>> transformToTargetReferencedBy = (objectType, allTargetObjectsWithDependencies) -> {
                for(Map.Entry<Long, List<String>> e : allTargetObjectsWithDependencies.entrySet()) {
                    Long objectAId = e.getKey(); // Object A is target object
                    List<String> referencedObjectIDs = e.getValue(); // List of objects which were referenced by Object A
                    for(String objectBTypeId : referencedObjectIDs) { // Object B, who was referenced by object A
                        String [] idstring = objectBTypeId.split("-");
                        Long objectBId = Long.valueOf(idstring[1]);
                        List<String> referencingIdsList = targetObjectIDsWithReferencingMap.get(objectBId); // List of objects which references object B
                        if(referencingIdsList == null) {
                            referencingIdsList = new ArrayList<>();
                            targetObjectIDsWithReferencingMap.put(objectBId, referencingIdsList);
                        }
                        referencingIdsList.add(objectType + "-" + objectAId); // Object A references object B
                    }
                }
            };

            info("find target content objects ref map");
            Map<Integer, Map<Long, List<String>>> targetContentObjectIDsWithDependencies = CloneHelper.queryInSchema(targetSchema, ()->Content.findContentObjectIDsWithDependencies(false)); // In target we always look for all references, including AC and WC

            info("init query target message/local smart text/local image ref map");
            if(targetContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_MESSAGE)) {
                transformToTargetReferencedBy.accept("Message", targetContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_MESSAGE));
            }
            if(targetContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT)) {
                transformToTargetReferencedBy.accept("LocalSmartText", targetContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT));
            }
            if(targetContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_LOCAL_IMAGE)) {
                transformToTargetReferencedBy.accept("LocalImage", targetContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_LOCAL_IMAGE));
            }
            if(! inSameInstance) {
                info("init query target smart texts ref map");
                if(targetContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT)) {
                    transformToTargetReferencedBy.accept("SmartText", targetContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT));
                }
                if(targetContentObjectIDsWithDependencies.containsKey(ContentObject.OBJECT_TYPE_GLOBAL_IMAGE)) {
                    transformToTargetReferencedBy.accept("Image", targetContentObjectIDsWithDependencies.get(ContentObject.OBJECT_TYPE_GLOBAL_IMAGE));
                }
            }

            BiConsumer<Map<Long, List<ReferencableObject>>, List<? extends IdentifiableMessagePointModel>> transformReferencableToNeedExistingReferencedBy = (allReferencesMap, trList)->{
                trList.forEach((trr)->{
                    IdentifiableMessagePointModel obj = (IdentifiableMessagePointModel) trr;
                    List<ReferencableObject> referencesList = allReferencesMap.get(obj.getId());
                    if(referencesList != null) {
                        List<IdentifiableMessagePointModel> referencingObjects = SyncTouchpointUtil.getObjectFromReferencableObjectList(referencesList); // List of objects which reference tr
                        referencingObjects.forEach(ro -> {
                            List<String> sourceObjectDependencies = allSourceObjectsWithDependenciesNeedExisting.get(ro.getId());
                            if (sourceObjectDependencies == null) {
                                sourceObjectDependencies = new ArrayList<>();
                                allSourceObjectsWithDependenciesNeedExisting.put(ro.getId(), sourceObjectDependencies);
                            }
                            String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(obj);
                            if (!sourceObjectDependencies.contains(refObjectTypeAndId)) {
                                sourceObjectDependencies.add(refObjectTypeAndId);
                            }
                        });
                    }
                });
            };

            info("find target composition file set ref map");
            allCompositionFileSetReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->CompositionFileSet.getAllReferencesMap());

            info("find target variant metadata setting ref map");
            allTouchVariantSettingReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->{
                Map<Long, List<ReferencableObject>> documentSettingsToObjectsReferencing = new HashMap<>();
                Document document = Document.findById(targetDocumentId);
                if(document.isEnabledForVariation()) {
                    DocumentSettingsModel variantMetadataSettings = SyncTouchpointUtil.getPsuedoDocumentSettingsObject(DocumentSettingsModel.ID_SETTING_VARIANT_METADATA_TEMPLATE, document, document.getVariantMetadataSettingsMap());
                    Set<TouchpointSelection> touchpointSelections = document.getTouchpointSelections();
                    if(touchpointSelections != null && !touchpointSelections.isEmpty()) {
                        List<ReferencableObject> touchpointSelectionTypeAndIds = touchpointSelections.stream().map(ts->new ReferencableObject(ts)).collect(Collectors.toList());
                        documentSettingsToObjectsReferencing.put(variantMetadataSettings.getId(), touchpointSelectionTypeAndIds);
                    }
                }
                return documentSettingsToObjectsReferencing;
            });

            info("find target channel configurationref map");
            allChannelConfigReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->ConnectorConfiguration.getAllReferencesMap());

            CloneHelper.execInSchema(sourceSchema, () -> {
                {
                    info("init query source composition package ref map");
                    Map<Long, List<ReferencableObject>> allCompositionFileSetReferencesMap = CompositionFileSet.getAllReferencesMap();
//                    for (Long compositionFileSetId : allCompositionFileSetReferencesMap.keySet()) {
//                        CompositionFileSet compositionFileSet = CompositionFileSet.findById(compositionFileSetId);
//                        if (compositionFileSet != null) {
//                            List<ReferencableObject> referencesList = allCompositionFileSetReferencesMap.get(compositionFileSetId);
//                            if (referencesList != null) {
//                                for (ReferencableObject referencableObject : referencesList) {
//                                    Long referencableObjectId = referencableObject.getObjectId();
//                                    if (referencableObjectId != null && referencableObjectId.longValue() != 0) {
//                                        List<String> alwaySyncList = allSourceObjectsWithDependenciesSyncWhenChange.get(referencableObjectId);
//                                        if (alwaySyncList == null) {
//                                            alwaySyncList = new ArrayList<>();
//                                            allSourceObjectsWithDependenciesSyncWhenChange.put(referencableObjectId, alwaySyncList);
//                                        }
//                                        String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(compositionFileSet);
//                                        if (!alwaySyncList.contains(refObjectTypeAndId)) {
//                                            alwaySyncList.add(refObjectTypeAndId);
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }
                    transformReferencableToNeedExistingReferencedBy.accept(allCompositionFileSetReferencesMap, CompositionFileSet.findByDocumentId(sourceDocumentId));
                }

                {
                    info("init query source channel configuration ref map");
                    Map<Long, List<ReferencableObject>> allConnectorConfigurationReferencesMap = ConnectorConfiguration.getAllReferencesMap();
//                    for (Long channelConfigId : allConnectorConfigurationReferencesMap.keySet()) {
//                        ConnectorConfiguration connectorConfiguration = ConnectorConfiguration.findById(channelConfigId);
//                        if (connectorConfiguration != null) {
//                            List<ReferencableObject> referencesList = allConnectorConfigurationReferencesMap.get(channelConfigId);
//                            if (referencesList != null) {
//                                for (ReferencableObject referencableObject : referencesList) {
//                                    Long referencableObjectId = referencableObject.getObjectId();
//                                    if (referencableObjectId != null && referencableObjectId.longValue() != 0) {
//                                        List<String> alwaySyncList = allSourceObjectsWithDependenciesSyncWhenChange.get(referencableObjectId);
//                                        if (alwaySyncList == null) {
//                                            alwaySyncList = new ArrayList<>();
//                                            allSourceObjectsWithDependenciesSyncWhenChange.put(referencableObjectId, alwaySyncList);
//                                        }
//                                        String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(connectorConfiguration);
//                                        if (!alwaySyncList.contains(refObjectTypeAndId)) {
//                                            alwaySyncList.add(refObjectTypeAndId);
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }

                    transformReferencableToNeedExistingReferencedBy.accept(allConnectorConfigurationReferencesMap, ConnectorConfiguration.findAllForDocument(sourceDocument));
                }
            });

            if(! inSameInstance) {
                info("init query target target groups ref map");
                allTargetGroupReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->TargetGroup.getAllReferencesMap(false)); // Target Group ID to the list of objects who reference the target group
                info("init query target target rules ref map");
                allTargetRuleReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->ConditionElement.getAllReferencesMap()); // Target Rule ID to the list of objects who reference the target rule
                info("init query target parameter groups ref map");
                allParameterGroupReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->ParameterGroup.getAllReferencesMap(false)); // Parameter Group ID to the list of objects who reference the parameter group
                info("init query target variables ref map");
                allVariableReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->DataElementVariable.getAllReferencesMap()); // Variable ID to the list of objects who reference the variable
                addAllVariableParameterReference(targetSchema, allParameterGroupReferencesMapInTargetSchema, allVariableReferencesMapInTargetSchema);
                addAllVariableConnectedReference(targetSchema, targetDocumentId, allVariableReferencesMapInTargetSchema); // Variables referenced in connected interview settings

                info("find target text styles ref map");
                allTextStyleReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->TextStyle.getAllReferencesMap(false)); // Variable ID to the list of objects who reference the variable
                info("find target text style fonts ref map");
                allTextStyleFontReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->TextStyleFont.getAllReferencesMap()); // Variable ID to the list of objects who reference the variable

                info("init query target datasource ref map");
                allDataSourceReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->DataSource.getAllReferencesMap()); // Datasource ID to the list of objects who reference the datasource

                info("init query target lookup table ref map");
//                allLookupTableReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->LookupTable.getAllReferencesMap()); // LookupTable ID to the list of objects who reference the lookup table

                addAllLookupTableDataSourceReference(targetSchema, allDataSourceReferencesMapInTargetSchema, allLookupTableReferencesMapInTargetSchema);

//                log.info("init query target data collection ref map");
//                allLookupTableReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->Data.getAllReferencesMap()); // LookupTable ID to the list of objects who reference the lookup table

                info("find target data file ref map");
                allDataFileReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->DataFile.getAllReferencesMap());

                info("find target data collection ref map");
                allDataCollectionReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->DataSourceAssociation.getAllReferencesMap());

                info("find target list style ref map");
                allListStyleReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->ListStyle.getAllReferencesMap());

                info("find target paragraph style ref map");
                allParagraphStyleReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->ParagraphStyle.getAllReferencesMap());

                info("find target metadata templated ref map");
                allMetadataFormDefinitionReferencesMapInTargetSchema = CloneHelper.queryInSchema(targetSchema, ()->MetadataFormDefinition.getAllReferencesMap());
                addTouchhpointAllMetadataFormTemplateReference(targetSchema, targetDocumentId, allMetadataFormDefinitionReferencesMapInTargetSchema);

                CloneHelper.execInSchema(sourceSchema, () -> {
                    {
                        info("init query source target groups ref map");
                        Map<Long, List<ReferencableObject>> allTargetGroupReferencesMap = TargetGroup.getAllReferencesMap(activeOnly && syncActiveOnly); // Target Group ID to the list of objects who reference the target group
                        for(Long targetGroupId : allTargetGroupReferencesMap.keySet()) {
                            TargetGroup targetGroup = TargetGroup.findById(targetGroupId);
                            if(targetGroup != null && targetGroup.isParameterized()) {
                                List<ReferencableObject> referencesList = allTargetGroupReferencesMap.get(targetGroupId);
                                if(referencesList != null) {
                                    for(ReferencableObject referencableObject : referencesList) {
                                        Long referencableObjectId = referencableObject.getObjectId();
                                        if(referencableObjectId != null && referencableObjectId.longValue() != 0) {
                                            List<String> alwaySyncList = allSourceObjectsWithDependenciesSyncWhenChange.get(referencableObjectId);
                                            if(alwaySyncList == null) {
                                                alwaySyncList = new ArrayList<>();
                                                allSourceObjectsWithDependenciesSyncWhenChange.put(referencableObjectId, alwaySyncList);
                                            }
                                            String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(targetGroup);
                                            if(! alwaySyncList.contains(refObjectTypeAndId)) {
                                                alwaySyncList.add(refObjectTypeAndId);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        transformReferencableToNeedExistingReferencedBy.accept(allTargetGroupReferencesMap, TargetGroup.findAllByDocument(sourceDocument, null, false, 0, true));
                    }

                    {
                        info("init query source target rules ref map");
                        Map<Long, List<ReferencableObject>> allTargetRuleReferencesMap = ConditionElement.getAllReferencesMap(); // Target Rule ID to the list of objects who reference the target rule
                        for(Long conditionElementId : allTargetRuleReferencesMap.keySet()) {
                            ConditionElement conditionElement = ConditionElement.findById(conditionElementId);
                            if(conditionElement != null && conditionElement.hasParameterizedSubElements()) {
                                List<ReferencableObject> referencesList = allTargetRuleReferencesMap.get(conditionElementId);
                                if(referencesList != null) {
                                    for(ReferencableObject referencableObject : referencesList) {
                                        Long referencableObjectId = referencableObject.getObjectId();
                                        if(referencableObjectId != null && referencableObjectId.longValue() != 0) {
                                            List<String> alwaySyncList = allSourceObjectsWithDependenciesSyncWhenChange.get(referencableObjectId);
                                            if(alwaySyncList == null) {
                                                alwaySyncList = new ArrayList<>();
                                                allSourceObjectsWithDependenciesSyncWhenChange.put(referencableObjectId, alwaySyncList);
                                            }
                                            String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(conditionElement);
                                            if(! alwaySyncList.contains(refObjectTypeAndId)) {
                                                alwaySyncList.add(refObjectTypeAndId);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        transformReferencableToNeedExistingReferencedBy.accept(allTargetRuleReferencesMap, ConditionElement.findAllByDocument(sourceDocument, null, false, 0));
                    }

                    List<DataElementVariable> allDataElementVariables = DataElementVariable.findAllVisibleForDocument(sourceDocument);

                    {
                        info("init query source parameter groups ref map");
                        Map<Long, List<ReferencableObject>> allParameterGroupReferencesMap = ParameterGroup.getAllReferencesMap(activeOnly && syncActiveOnly); // Parameter Group ID to the list of objects who reference the parameter group

                        transformReferencableToNeedExistingReferencedBy.accept(allParameterGroupReferencesMap, ParameterGroup.findAllIncludingParameters().stream().filter(pg -> pg.isVisibleToDocument(sourceDocument)).collect(Collectors.toList()));

                        info("init query source variables ref map");
                        Map<Long, List<ReferencableObject>> allVariableReferencesMap = DataElementVariable.getAllReferencesMap(); // Data Element Variable ID to the list of objects who reference the target group

                        addAllVariableParameterReference(sourceSchema, allParameterGroupReferencesMap, allVariableReferencesMap);
                        //addAllVariableConnectedReference(sourceSchema, sourceDocumentId, allVariableReferencesMap); // Variables referenced in connected interview settings


                        transformReferencableToNeedExistingReferencedBy.accept(allVariableReferencesMap, allDataElementVariables);
                    }

                    {
                        info("init query source datasource ref map");
                        Map<Long, List<ReferencableObject>> allDataSourceReferencesMap = DataSource.getAllReferencesMap(); // Data Element Variable ID to the list of objects who reference the target group

                        List<DataSource> allDataSourcesVisibleForDocument = DataSource.findAll()
                                .stream()
                                .filter(ds->{
                                    List<Document> dsDocuments = ds.getDocuments();
                                    return dsDocuments != null && dsDocuments.stream().anyMatch(d->d.getId() == sourceDocument.getId());
                                })
                                .collect(Collectors.toList());

                        Set<Long> allVisibleDataSourceIds = allDataSourcesVisibleForDocument.stream().map(DataSource::getId).collect(Collectors.toSet());

                        for(DataElementVariable dev : allDataElementVariables) {
                            List<AbstractDataElement> devDataElements = new ArrayList<>();
                            AbstractDataElement defaultDataElement = dev.getDefaultDataElement();
                            if(defaultDataElement != null) {
                                devDataElements.add(defaultDataElement);
                            }

                            for(Map.Entry<Long, VariableDataElementMap> dsVdemEntry : dev.getDataElementMap().entrySet()) {
                                Long dataSourceId = dsVdemEntry.getKey();
                                VariableDataElementMap vdem = dsVdemEntry.getValue();
                                AbstractDataElement dataElement = vdem.getDataElement();
                                if(dataElement != null) {
                                    devDataElements.add(dataElement);
                                }
                            }

                            for(AbstractDataElement dataElement : devDataElements) {
                                DataSource dataSource = dataElement.getDataSource();
                                Long dataSourceId = dataSource.getId();
                                String dataElementDna = dataElement.getDna();
                                boolean isNewDataElement = false;
                                if(dataSource.isXML()) {
                                    isNewDataElement = CloneHelper.queryInSchema(targetSchema, ()-> XmlDataElement.findByDna(dataElementDna) == null);
                                } else if(dataSource.isJSON()) {
                                    isNewDataElement = CloneHelper.queryInSchema(targetSchema, ()-> JSONDataElement.findByDna(dataElementDna) == null);
                                } else {
                                    isNewDataElement = CloneHelper.queryInSchema(targetSchema, ()-> DataElement.findByDna(dataElementDna) == null);
                                }
                                if(false && isNewDataElement) {
                                    if(allVisibleDataSourceIds.contains(dataSourceId)) {
                                        List<ReferencableObject> referencableObjects = allDataSourceReferencesMap.get(dataSourceId);
                                        if(referencableObjects == null) {
                                            referencableObjects = new ArrayList<>();
                                            allDataSourceReferencesMap.put(dataSourceId, referencableObjects);
                                        }
                                        if(! referencableObjects.stream().anyMatch(ro->ro.getObjectId() == dev.getId())) {
                                            ReferencableObject referencableObject = new ReferencableObject(dev);
                                            referencableObjects.add(referencableObject);
                                        }
                                        List<String> alwaySyncList = allSourceObjectsWithDependenciesSyncWhenChange.get(dev.getId());
                                        if(alwaySyncList == null) {
                                            alwaySyncList = new ArrayList<>();
                                            allSourceObjectsWithDependenciesSyncWhenChange.put(dev.getId(), alwaySyncList);
                                        }
                                        String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(dataSource);
                                        if(! alwaySyncList.contains(refObjectTypeAndId)) {
                                            alwaySyncList.add(refObjectTypeAndId);
                                        }
                                    }
                                }
                            }
                        }

                        transformReferencableToNeedExistingReferencedBy.accept(allDataSourceReferencesMap, allDataSourcesVisibleForDocument);
                    }

                    {
                        info("init query source lookuptable ref map");
                        Map<Long, List<ReferencableObject>> allLookupTableReferencesMap = LookupTable.getAllReferencesMap(); // Data Element Variable ID to the list of objects who reference the target group
                        transformReferencableToNeedExistingReferencedBy.accept(allLookupTableReferencesMap,
                                LookupTableInstance.findAllVisibleIdsOfMostRecentCopies(List.of(sourceDocument.getId()), null)
                                        .stream().map(instid->(LookupTable) LookupTableInstance.findById(instid).getModel()).collect(Collectors.toList()));
                    }

                    if(! isConnectedTouchpoint) {
                        info("init query source data resources ref map");
                        Map<Long, List<ReferencableObject>> allDataResourceReferencesMap = DataResource.getAllReferencesMap(); // Data Element Variable ID to the list of objects who reference the target group
                        transformReferencableToNeedExistingReferencedBy.accept(allDataResourceReferencesMap, DataResource.findAllByDocument(sourceDocument));
                    }

                    if(! isConnectedTouchpoint) {
                        info("init query source data collection ref map");
                        Map<Long, List<ReferencableObject>> allDataCollectionReferencesMap = DataSourceAssociation.getAllReferencesMap(); // Data Collection ID to the list of objects who reference the target group
                        transformReferencableToNeedExistingReferencedBy.accept(allDataCollectionReferencesMap, DataSourceAssociation.findAll());
                    }

                    if(! isConnectedTouchpoint) {
                        info("init query source data file ref map");
                        Map<Long, List<ReferencableObject>> allDataFileReferencesMap = DataFile.getAllReferencesMap();
                        transformReferencableToNeedExistingReferencedBy.accept(allDataFileReferencesMap, DataFile.findAll());
                    }

                    {
                        info("init query source composition file set ref map");
                        Map<Long, List<ReferencableObject>> allCompositionFileSetReferencesMap = CompositionFileSet.getAllReferencesMap(); // Data Element Variable ID to the list of objects who reference the target group
                        transformReferencableToNeedExistingReferencedBy.accept(allCompositionFileSetReferencesMap, CompositionFileSet.findAll());
                    }

                    {
                        info("init query source metadata form template ref map");
                        Map<Long, List<ReferencableObject>> allMetadataFormDefinitionReferencesMap = MetadataFormDefinition.getAllReferencesMap(); // MetadataFormDefinition ID to the list of objects who reference the target group
                        for(Long metadataFormDefinitionId : allMetadataFormDefinitionReferencesMap.keySet()) {
                            MetadataFormDefinition metadataFormDefinition = MetadataFormDefinition.findById(metadataFormDefinitionId);
                            if(metadataFormDefinition != null) {
                                List<ReferencableObject> referencesList = allMetadataFormDefinitionReferencesMap.get(metadataFormDefinitionId);
                                if(referencesList != null) {
                                    for(ReferencableObject referencableObject : referencesList) {
                                        Long referencableObjectId = referencableObject.getObjectId();
                                        if(referencableObjectId != null && referencableObjectId.longValue() != 0) {
                                            List<String> alwaySyncList = allSourceObjectsWithDependenciesSyncWhenChange.get(referencableObjectId);
                                            if(alwaySyncList == null) {
                                                alwaySyncList = new ArrayList<>();
                                                allSourceObjectsWithDependenciesSyncWhenChange.put(referencableObjectId, alwaySyncList);
                                            }
                                            String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(metadataFormDefinition);
                                            if(! alwaySyncList.contains(refObjectTypeAndId)) {
                                                alwaySyncList.add(refObjectTypeAndId);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        transformReferencableToNeedExistingReferencedBy.accept(allMetadataFormDefinitionReferencesMap, MetadataFormDefinition.findAll());
                    }

                    {
                        info("init query source text styles ref map");
                        Map<Long, List<ReferencableObject>> allTextStyleReferencesMap = TextStyle.getAllReferencesMap(activeOnly && syncActiveOnly); // Text Style ID to the list of objects who reference the text style
                        for(Long textStyleId : allTextStyleReferencesMap.keySet()) {
                            TextStyle textStyle = TextStyle.findById(textStyleId);
                            if(textStyle != null) {
                                List<ReferencableObject> referencesList = allTextStyleReferencesMap.get(textStyleId);
                                if(referencesList != null) {
                                    for(ReferencableObject referencableObject : referencesList) {
                                        Long referencableObjectId = referencableObject.getObjectId();
                                        if(referencableObjectId != null && referencableObjectId.longValue() != 0) {
                                            List<String> alwaySyncList = allSourceObjectsWithDependenciesSyncWhenChange.get(referencableObjectId);
                                            if(alwaySyncList == null) {
                                                alwaySyncList = new ArrayList<>();
                                                allSourceObjectsWithDependenciesSyncWhenChange.put(referencableObjectId, alwaySyncList);
                                            }
                                            String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(textStyle);
                                            if(! alwaySyncList.contains(refObjectTypeAndId)) {
                                                alwaySyncList.add(refObjectTypeAndId);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        transformReferencableToNeedExistingReferencedBy.accept(allTextStyleReferencesMap, TextStyle.findAll());
                    }

                    {
                        info("init query source text style fonts ref map");
                        Map<Long, List<ReferencableObject>> allTextStyleFontReferencesMap = TextStyleFont.getAllReferencesMap(); // Text Style Font ID to the list of objects who reference the text style font
                        for(Long textStyleFontId : allTextStyleFontReferencesMap.keySet()) {
                            TextStyleFont textStyleFont = TextStyleFont.findById(textStyleFontId);
                            if(textStyleFont != null) {
                                List<ReferencableObject> referencesList = allTextStyleFontReferencesMap.get(textStyleFontId);
                                if(referencesList != null) {
                                    for(ReferencableObject referencableObject : referencesList) {
                                        Long referencableObjectId = referencableObject.getObjectId();
                                        if(referencableObjectId != null && referencableObjectId.longValue() != 0) {
                                            List<String> alwaySyncList = allSourceObjectsWithDependenciesSyncWhenChange.get(referencableObjectId);
                                            if(alwaySyncList == null) {
                                                alwaySyncList = new ArrayList<>();
                                                allSourceObjectsWithDependenciesSyncWhenChange.put(referencableObjectId, alwaySyncList);
                                            }
                                            String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(textStyleFont);
                                            if(! alwaySyncList.contains(refObjectTypeAndId)) {
                                                alwaySyncList.add(refObjectTypeAndId);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        transformReferencableToNeedExistingReferencedBy.accept(allTextStyleFontReferencesMap, TextStyleFont.findAll());
                    }

                    {
                        info("init query source list styles ref map");
                        Map<Long, List<ReferencableObject>> allListStyleReferencesMap = ListStyle.getAllReferencesMap(); // List Style ID to the list of objects who reference the list style
                        for(Long listStyleId : allListStyleReferencesMap.keySet()) {
                            ListStyle listStyle = ListStyle.findById(listStyleId);
                            if(listStyle != null) {
                                List<ReferencableObject> referencesList = allListStyleReferencesMap.get(listStyleId);
                                if(referencesList != null) {
                                    for(ReferencableObject referencableObject : referencesList) {
                                        Long referencableObjectId = referencableObject.getObjectId();
                                        if(referencableObjectId != null && referencableObjectId.longValue() != 0) {
                                            List<String> alwaySyncList = allSourceObjectsWithDependenciesSyncWhenChange.get(referencableObjectId);
                                            if(alwaySyncList == null) {
                                                alwaySyncList = new ArrayList<>();
                                                allSourceObjectsWithDependenciesSyncWhenChange.put(referencableObjectId, alwaySyncList);
                                            }
                                            String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(listStyle);
                                            if(! alwaySyncList.contains(refObjectTypeAndId)) {
                                                alwaySyncList.add(refObjectTypeAndId);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        transformReferencableToNeedExistingReferencedBy.accept(allListStyleReferencesMap, ListStyle.findAll());
                    }

                    {
                        info("init query source paragraph styles ref map");
                        Map<Long, List<ReferencableObject>> allParagraphStyleReferencesMap = ParagraphStyle.getAllReferencesMap(); // Paragraph Style ID to the list of objects who reference the Paragraph style
                        for(Long paragraphStyleId : allParagraphStyleReferencesMap.keySet()) {
                            ParagraphStyle paragraphStyle = ParagraphStyle.findById(paragraphStyleId);
                            if(paragraphStyle != null) {
                                List<ReferencableObject> referencesList = allParagraphStyleReferencesMap.get(paragraphStyleId);
                                if(referencesList != null) {
                                    for(ReferencableObject referencableObject : referencesList) {
                                        Long referencableObjectId = referencableObject.getObjectId();
                                        if(referencableObjectId != null && referencableObjectId.longValue() != 0) {
                                            List<String> alwaySyncList = allSourceObjectsWithDependenciesSyncWhenChange.get(referencableObjectId);
                                            if(alwaySyncList == null) {
                                                alwaySyncList = new ArrayList<>();
                                                allSourceObjectsWithDependenciesSyncWhenChange.put(referencableObjectId, alwaySyncList);
                                            }
                                            String refObjectTypeAndId = SyncTouchpointUtil.getRefObjectTypeAndId(paragraphStyle);
                                            if(! alwaySyncList.contains(refObjectTypeAndId)) {
                                                alwaySyncList.add(refObjectTypeAndId);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        transformReferencableToNeedExistingReferencedBy.accept(allParagraphStyleReferencesMap, ParagraphStyle.findAll());
                    }
                });
            }

            Map<Long, List<ReferencableObject>> allDataSourceReferencesMapInTargetSchemaFinal = allDataSourceReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allVariableReferencesMapInTargetSchemaFinal = allVariableReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allParameterGroupReferencesMapInTargetSchemaFinal = allParameterGroupReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allTargetRuleReferencesMapInTargetSchemaFinal = allTargetRuleReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allTargetGroupReferencesMapInTargetSchemaFinal = allTargetGroupReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allTextStyleReferencesMapInTargetSchemaFinal = allTextStyleReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allTextStyleFontReferencesMapInTargetSchemaFinal = allTextStyleFontReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allDataFileReferencesMapInTargetSchemaFinal = allDataFileReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allDataResourceReferencesMapInTargetSchemaFinal = allDataResourceReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allDataCollectionReferencesMapInTargetSchemaFinal = allDataCollectionReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allCompositionFileSetReferencesMapInTargetSchemaFinal = allCompositionFileSetReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allChannelConfigReferencesMapInTargetSchemaFinal = allChannelConfigReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allListStyleReferencesMapInTargetSchemaFinal = allListStyleReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allParagraphStyleReferencesMapInTargetSchemaFinal = allParagraphStyleReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allMetadataFormDefinitionReferencesMapInTargetSchemaFinal = allMetadataFormDefinitionReferencesMapInTargetSchema;
            Map<Long, List<ReferencableObject>> allTouchVariantSettingReferencesMapInTargetSchemaFinal = allTouchVariantSettingReferencesMapInTargetSchema;
            List<Long> targetRuleIdsVisibleToSourceDocument = includeNotVisibleTargetRules ? Collections.emptyList() : CloneHelper.queryInSchema(sourceSchema, () -> ConditionElement.findAllIdsWithTargetGroupVisibleToDocument(sourceDocument));

            CloneHelper.execInSchema(targetSchema, () -> {
                if (!inSameInstance) {
                    statusData.put("percentage", 5);
                    if(saveStatus != null) saveStatus.accept(statusData);
                    {
                        Map<Long, DataSource> sourceIdToTargetModelMap = new HashMap<>();
                        Map<DataSource, Long> statusMap = SyncTouchpointUtil.getDataSourcesForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);
                        getSyncObjectsArray(data, SyncObjectType.ID_DATASOURCE,
                                statusMap, sourceIdToTargetModelMap,
                                allSourceObjectsWithDependenciesNeedExisting,
                                allSourceObjectsWithDependenciesSyncWhenChange,
                                allDataSourceReferencesMapInTargetSchemaFinal
                        );
                    }

                    {
                        Map<Long, LookupTable> sourceIdToTargetObjectMap = new HashMap<>();
                        Map<LookupTable, Long> statusMap = SyncTouchpointUtil.getLookupTablesForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, activeOnly, sourceIdToTargetObjectMap);
                        getSyncVersionedObjectsArray(data, SyncObjectType.ID_LOOKUPTABLE,
                                statusMap, sourceIdToTargetObjectMap,
                                null,
                                allSourceObjectsWithDependenciesNeedExisting,
                                allSourceObjectsWithDependenciesSyncWhenChange,
                                allLookupTableReferencesMapInTargetSchema,
                                null,
                                syncActiveOnly,
                                userInSourceSchema,
                                userInTargetSchema);
                    }

                    statusData.put("percentage", 10);
                    if(saveStatus != null) saveStatus.accept(statusData);
                    {
                        Map<Long, DataElementVariable> sourceIdToTargetModelMap = new HashMap<>();
                        Map<DataElementVariable, Long> statusMap = SyncTouchpointUtil.getVariablesForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);
                        getSyncObjectsArray(data, SyncObjectType.ID_VARIABLE,
                                statusMap, sourceIdToTargetModelMap,
                                allSourceObjectsWithDependenciesNeedExisting,
                                allSourceObjectsWithDependenciesSyncWhenChange,
                                allVariableReferencesMapInTargetSchemaFinal
                                );
                    }

                    statusData.put("percentage", 20);
                    if(saveStatus != null) saveStatus.accept(statusData);

                    if(! isConnectedTouchpoint) {
                        Map<Long, DataSourceAssociation> sourceIdToTargetModelMap = new HashMap<>();
                        Map<DataSourceAssociation, Long> statusMap = SyncTouchpointUtil.getDataSourceAssociationsForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);
                        getSyncObjectsArray(data, SyncObjectType.ID_DATACOLLECTION, statusMap, sourceIdToTargetModelMap,
                                allSourceObjectsWithDependenciesNeedExisting,
                                allSourceObjectsWithDependenciesSyncWhenChange,
                                allDataCollectionReferencesMapInTargetSchemaFinal
                                );
                    }

                    statusData.put("percentage", 25);
                    if(saveStatus != null) saveStatus.accept(statusData);
                    {
                        Map<Long, ParameterGroup> sourceIdToTargetModelMap = new HashMap<>();
                        Map<ParameterGroup, Long> statusMap = SyncTouchpointUtil.getParameterGroupsForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);
                        getSyncObjectsArray(data, SyncObjectType.ID_PARAMETER_GROUP, statusMap, sourceIdToTargetModelMap,
                                allSourceObjectsWithDependenciesNeedExisting,
                                allSourceObjectsWithDependenciesSyncWhenChange,
                                allParameterGroupReferencesMapInTargetSchemaFinal);
                    }

                    statusData.put("percentage", 30);
                    if(saveStatus != null) saveStatus.accept(statusData);
                    {
                        Map<Long, ConditionElement> sourceIdToTargetModelMap = new HashMap<>();
                        Map<ConditionElement, Long> statusMap = SyncTouchpointUtil.getTargetRulesForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);
                        if(!includeNotVisibleTargetRules){
                            statusMap = statusMap.entrySet().stream()
                                    .filter(e-> targetRuleIdsVisibleToSourceDocument.contains(BigInteger.valueOf(e.getKey().getId())))
                                    .collect(Collectors.toMap(e-> e.getKey(), e -> e.getValue()));
                        }

                        getSyncObjectsArray(data, SyncObjectType.ID_TARGET_RULE, statusMap, sourceIdToTargetModelMap,
                                allSourceObjectsWithDependenciesNeedExisting,
                                allSourceObjectsWithDependenciesSyncWhenChange,
                                allTargetRuleReferencesMapInTargetSchemaFinal
                        );
                    }

                    statusData.put("percentage", 35);
                    if(saveStatus != null) saveStatus.accept(statusData);
                    {
                        Map<Long, TargetGroup> sourceIdToTargetModelMap = new HashMap<>();
                        Map<TargetGroup, Long> statusMap = SyncTouchpointUtil.getTargetGroupsForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);
                        getSyncObjectsArray(data, SyncObjectType.ID_TARGET_GROUP, statusMap, sourceIdToTargetModelMap,
                                allSourceObjectsWithDependenciesNeedExisting,
                                allSourceObjectsWithDependenciesSyncWhenChange,
                                allTargetGroupReferencesMapInTargetSchemaFinal
                        );

                    }

                    if(! isConnectedTouchpoint) {
                        Map<Long, DataFile> sourceIdToTargetModelMap = new HashMap<>();
                        Map<DataFile, Long> statusMap = SyncTouchpointUtil.getDataFilesForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);
                        getSyncObjectsArray(data, SyncObjectType.ID_DATA_FILE, statusMap, sourceIdToTargetModelMap,
                            allSourceObjectsWithDependenciesNeedExisting,
                            allSourceObjectsWithDependenciesSyncWhenChange,
                            allDataFileReferencesMapInTargetSchemaFinal
                        );

                    }

                    if(! isConnectedTouchpoint) {
                        Map<Long, DataResource> sourceIdToTargetModelMap = new HashMap<>();
                        Map<DataResource, Long> statusMap = SyncTouchpointUtil.getDataResourcesForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);
                        getSyncObjectsArray(data, SyncObjectType.ID_DATA_RESOURCE, statusMap, sourceIdToTargetModelMap,
                                allSourceObjectsWithDependenciesNeedExisting,
                                allSourceObjectsWithDependenciesSyncWhenChange,
                                allDataResourceReferencesMapInTargetSchemaFinal
                        );
                    }

                }

                // Global objects referenced in source

                Set<ContentObject> allGlobalContentObjectsReferencedByDocumentInSourceSchema = SyncTouchpointUtil.findAllGlobalContentObjectsReferencedByDocument(sourceSchema, sourceDocumentId);

                {
                    Map<Long, MetadataFormDefinition> sourceIdToTargetModelMap = new HashMap<>();

                    Map<MetadataFormDefinition, Long> statusMap = SyncTouchpointUtil.getMetadataTemplatesForSync(targetDocument, sourceDocument,
                        sourceSchema, true,
                        allGlobalContentObjectsReferencedByDocumentInSourceSchema,
                        includeRejectedChanges, sourceIdToTargetModelMap);

                    getSyncObjectsArray(data, SyncObjectType.ID_METADATAFORM_DEFINITION, statusMap, sourceIdToTargetModelMap,
                        allSourceObjectsWithDependenciesNeedExisting,
                        allSourceObjectsWithDependenciesSyncWhenChange,
                        allMetadataFormDefinitionReferencesMapInTargetSchemaFinal);
                }

                statusData.put("percentage", 40);
                if(saveStatus != null) saveStatus.accept(statusData);

                {
                    Map<Long, DocumentSettingsModel> sourceIdToTargetModelMap = new HashMap<>();
                    Map<DocumentSettingsModel, Long> statusMap = SyncTouchpointUtil.getDocumentSettingsForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);

                    Map<Long, List<String>> documentSettingsToObjectsNeedExistingReferenced = new HashMap<>(); // DocumentSetting to Metadata form template and Variable map which is needed to exist
                    Map<Long, List<String>> documentSettingsToObjectsSyncWhenChangedReferenced = new HashMap<>(); // DocumentSetting to Metadata form template and Variable map which should sync together

                    {
                        Map<Long, List<ReferencableObject>> metadataTemplateToDocummentSettingDepInSourceSchemaMap = new HashMap<>();
                        addTouchhpointAllMetadataFormTemplateReference(sourceSchema, sourceDocumentId, metadataTemplateToDocummentSettingDepInSourceSchemaMap);
                        metadataTemplateToDocummentSettingDepInSourceSchemaMap.forEach((metadataFormTemplateId, settings) -> {
                            String metadataFormTemplateTypeId = "MetadataFormTemplate" + "-" + metadataFormTemplateId;

                            List<IdentifiableMessagePointModel> referencingObjects = SyncTouchpointUtil.getObjectFromReferencableObjectList(settings); // List of objects which reference tr
                            referencingObjects.forEach(ro -> {
                                {
                                    List<String> sourceObjectDependencies = documentSettingsToObjectsNeedExistingReferenced.get(ro.getId());
                                    if (sourceObjectDependencies == null) {
                                        sourceObjectDependencies = new ArrayList<>();
                                        documentSettingsToObjectsNeedExistingReferenced.put(ro.getId(), sourceObjectDependencies);
                                    }
                                    if (!sourceObjectDependencies.contains(metadataFormTemplateId)) {
                                        sourceObjectDependencies.add(metadataFormTemplateTypeId);
                                    }
                                }
                                if(ro.getId() == DocumentSettingsModel.ID_SETTING_TOUCHPOINT_METADATA) {
                                    List<String> sourceObjectDependencies = documentSettingsToObjectsSyncWhenChangedReferenced.get(ro.getId());
                                    if (sourceObjectDependencies == null) {
                                        sourceObjectDependencies = new ArrayList<>();
                                        documentSettingsToObjectsSyncWhenChangedReferenced.put(ro.getId(), sourceObjectDependencies);
                                    }
                                    if (!sourceObjectDependencies.contains(metadataFormTemplateId)) {
                                        sourceObjectDependencies.add(metadataFormTemplateTypeId);
                                    }
                                }
                            });
                        });
                    }

                    {
                        Map<Long, List<ReferencableObject>> variableToDocummentSettingDepInSourceSchemaMap = new HashMap<>();
                        addAllVariableConnectedReference(sourceSchema, sourceDocumentId, variableToDocummentSettingDepInSourceSchemaMap);

                        variableToDocummentSettingDepInSourceSchemaMap.forEach((variableId, settings) -> {
                            String variableTypeId = "Variable" + "-" + variableId;

                            List<IdentifiableMessagePointModel> referencingObjects = SyncTouchpointUtil.getObjectFromReferencableObjectList(settings); // List of objects which reference tr
                            referencingObjects.forEach(ro -> {
                                List<String> sourceObjectDependencies = documentSettingsToObjectsNeedExistingReferenced.get(ro.getId());
                                if (sourceObjectDependencies == null) {
                                    sourceObjectDependencies = new ArrayList<>();
                                    documentSettingsToObjectsNeedExistingReferenced.put(ro.getId(), sourceObjectDependencies);
                                }
                                if (!sourceObjectDependencies.contains(variableTypeId)) {
                                    sourceObjectDependencies.add(variableTypeId);
                                }
                            });
                        });
                    }

                    getSyncObjectsArray(data, SyncObjectType.ID_DOCUMENT_SETTING, statusMap, sourceIdToTargetModelMap, documentSettingsToObjectsNeedExistingReferenced, documentSettingsToObjectsSyncWhenChangedReferenced, allTouchVariantSettingReferencesMapInTargetSchemaFinal);
                }

                statusData.put("percentage", 45);
                if(saveStatus != null) saveStatus.accept(statusData);

                {
                    Map<Long, CompositionFileSet> sourceIdToTargetModelMap = new HashMap<>();
                    Map<CompositionFileSet, Long> statusMap = SyncTouchpointUtil.getCompositionPackagesForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);
                    getSyncObjectsArray(data, SyncObjectType.ID_COMPOSITION_PACKAGE, statusMap, sourceIdToTargetModelMap,
                            allSourceObjectsWithDependenciesNeedExisting,
                            allSourceObjectsWithDependenciesSyncWhenChange,
                            allCompositionFileSetReferencesMapInTargetSchemaFinal);
                }

                statusData.put("percentage", 50);
                if(saveStatus != null) saveStatus.accept(statusData);

                {
                    Map<Long, TextStyle> sourceIdToTargetModelMap = new HashMap<>();
                    Map<TextStyle, Long> statusMap = SyncTouchpointUtil.getTextStylesForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);
                    getSyncObjectsArray(data, SyncObjectType.ID_TEXT_STYLE, statusMap, sourceIdToTargetModelMap,
                            allSourceObjectsWithDependenciesNeedExisting,
                            allSourceObjectsWithDependenciesSyncWhenChange,
                            allTextStyleReferencesMapInTargetSchemaFinal);
                }

                statusData.put("percentage", 55);
                if(saveStatus != null) saveStatus.accept(statusData);

                {
                    Map<Long, TextStyleFont> sourceIdToTargetModelMap = new HashMap<>();
                    Map<TextStyleFont, Long> statusMap = SyncTouchpointUtil.getTextStyleFontsForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);
                    getSyncObjectsArray(data, SyncObjectType.ID_TEXT_STYLE_FONT, statusMap, sourceIdToTargetModelMap,
                            allSourceObjectsWithDependenciesNeedExisting,
                            allSourceObjectsWithDependenciesSyncWhenChange,
                            allTextStyleFontReferencesMapInTargetSchemaFinal);
                }

                {
                    Map<Long, ListStyle> sourceIdToTargetModelMap = new HashMap<>();
                    Map<ListStyle, Long> statusMap = SyncTouchpointUtil.getListStylesForSync(targetDocument, sourceDocument, sourceSchema, true, allGlobalContentObjectsReferencedByDocumentInSourceSchema, includeRejectedChanges, sourceIdToTargetModelMap);
                    getSyncObjectsArray(data, SyncObjectType.ID_LIST_STYLE, statusMap, sourceIdToTargetModelMap,
                        allSourceObjectsWithDependenciesNeedExisting,
                        allSourceObjectsWithDependenciesSyncWhenChange,
                        allListStyleReferencesMapInTargetSchemaFinal);
                }

                {
                    Map<Long, ParagraphStyle> sourceIdToTargetModelMap = new HashMap<>();
                    Map<ParagraphStyle, Long> statusMap = SyncTouchpointUtil.getParagraphStylesForSync(targetDocument, sourceDocument,
                        sourceSchema, true,
                        allGlobalContentObjectsReferencedByDocumentInSourceSchema,
                        includeRejectedChanges, sourceIdToTargetModelMap);
                    getSyncObjectsArray(data, SyncObjectType.ID_PARAGRAPH_STYLE, statusMap, sourceIdToTargetModelMap,
                        allSourceObjectsWithDependenciesNeedExisting,
                        allSourceObjectsWithDependenciesSyncWhenChange,
                        allParagraphStyleReferencesMapInTargetSchemaFinal);
                }

                {
                    Map<Long, ConnectorConfiguration> sourceIdToTargetModelMap = new HashMap<>();
                    Map<ConnectorConfiguration, Long> statusMap = SyncTouchpointUtil.getChannelConfigurationsForSync(targetDocument, sourceDocument, sourceSchema, true, includeRejectedChanges, sourceIdToTargetModelMap);
                    getSyncObjectsArray(data, SyncObjectType.ID_CHANNEL_CONFIGURATION, statusMap, sourceIdToTargetModelMap,
                            allSourceObjectsWithDependenciesNeedExisting,
                            allSourceObjectsWithDependenciesSyncWhenChange,
                            allChannelConfigReferencesMapInTargetSchemaFinal,
                            (jsonObject, model) -> {
                                ConnectorConfiguration connectorConfiguration = (ConnectorConfiguration) model;
                                String objectTypeName = jsonObject.getString("objectTypeName");
                                String channelTypeLabel = ApplicationUtil.getMessage("page.label.print");
                                if(connectorConfiguration.isWeb()){
                                    channelTypeLabel = ApplicationUtil.getMessage("page.label.web");
                                }
                                else if(connectorConfiguration.isEmail()){
                                    channelTypeLabel = ApplicationUtil.getMessage("page.label.email");
                                }

                                jsonObject.put("objectTypeName", channelTypeLabel + " " + objectTypeName);
                            }
                    );
                }




/* For TPM-288.
    2021-10-13 13;2525 JOHNLIU: Not sure whether or not we need show sections, zones, and variants in sync list yet.
                {
                    Map<? extends Object, Long> statusMap = SyncTouchpointUtil.getTouchpointSectionsForSync(targetDocument, sourceDocument, sourceSchema, true, true);
                    getSyncObjectsArray(data, "section", statusMap);
                }

                {
                    Map<? extends Object, Long> statusMap = SyncTouchpointUtil.getTouchpointZonesForSync(targetDocument, sourceDocument, sourceSchema, true, true);
                    getSyncObjectsArray(data, "zone", statusMap);
                }

                {
                    Map<? extends Object, Long> statusMap = SyncTouchpointUtil.getTouchpointVariantsForSync(targetDocument, sourceDocument, sourceSchema, true, true);
                    getSyncObjectsArray(data, "variant", statusMap);
                }
*/
                statusData.put("percentage", 60);
                if(saveStatus != null) saveStatus.accept(statusData);

                if (!inSameInstance) {
                    Map<Long, ContentObject> sourceIdToTargetObjectMap = new HashMap<>();

                    Map<ContentObject, Long> contentObjectStatusMap = SyncTouchpointUtil.getGlobalContentObjectsForSync(
                            targetDocument,
                            sourceDocument,
                            sourceSchema,
                            true,
                            allGlobalContentObjectsReferencedByDocumentInSourceSchema,
                            includeRejectedChanges,
                            activeOnly,
                            languagesForSync,
                            sourceIdToTargetObjectMap
                    );

                    getSyncVersionedObjectsArray(data, SyncObjectType.ID_CONTENT_LIBRARY, contentObjectStatusMap, sourceIdToTargetObjectMap, ContentObject.OBJECT_TYPE_GLOBAL_IMAGE,
                            allSourceObjectsWithDependenciesNeedExisting,
                            allSourceObjectsWithDependenciesSyncWhenChange,
                            null,
                            targetObjectIDsWithReferencingMap,
                            syncActiveOnly,
                            userInSourceSchema,
                            userInTargetSchema
                    );

                    getSyncVersionedObjectsArray(data, SyncObjectType.ID_SMART_TEXT, contentObjectStatusMap, sourceIdToTargetObjectMap, ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT,
                            allSourceObjectsWithDependenciesNeedExisting,
                            allSourceObjectsWithDependenciesSyncWhenChange,
                            null,
                            targetObjectIDsWithReferencingMap,
                            syncActiveOnly,
                            userInSourceSchema,
                            userInTargetSchema
                    );
                }

                statusData.put("percentage", 65);
                if(saveStatus != null) saveStatus.accept(statusData);

                if ( isSyncVariantFeatureFlagEnabled ) {
                    Map<Long, TouchpointSelection> sourceIdToTargetObjectMap = new HashMap<>();
                    Map<TouchpointSelection, Long> touchpointSelectionStatusMap = SyncTouchpointUtil.getTouchpointVariantsForSync (
                        targetDocument,
                        sourceDocument,
                        sourceSchema,
                        true,
                        includeRejectedChanges,
                        activeOnly,
                        languagesForSync,
                        sourceIdToTargetObjectMap
                    );

                    CloneHelper.execInSchema(sourceSchema, ()->{
                        Document document = Document.findById(sourceDocumentId);
                        Set<TouchpointSelection> touchpointSelections = document.getTouchpointSelections();
                        Map<String, Object> variantMetadataSettingsMap = document.getVariantMetadataSettingsMap();

                        DocumentSettingsModel variantMetadataSettings = SyncTouchpointUtil.getPsuedoDocumentSettingsObject(DocumentSettingsModel.ID_SETTING_VARIANT_METADATA_TEMPLATE, document, document.getVariantMetadataSettingsMap());

                        ReferencableObject referencableObject = new ReferencableObject(variantMetadataSettings);

                        String referencedObjectTypeid = SyncTouchpointUtil.getRefObjectTypeAndId(variantMetadataSettings);
                        for(TouchpointSelection touchpointSelection : touchpointSelections) {
                            List<String> referencedObjectsList = allSourceObjectsWithDependenciesSyncWhenChange.get(touchpointSelection.getId());
                            if(referencedObjectsList == null) {
                                referencedObjectsList = new ArrayList<>();
                                allSourceObjectsWithDependenciesSyncWhenChange.put(touchpointSelection.getId(), referencedObjectsList);
                            }
                            referencedObjectsList.add(referencedObjectTypeid);
                        }
                    });

                    getSyncVersionedObjectsArray(data, SyncObjectType.ID_VARIANT, touchpointSelectionStatusMap, sourceIdToTargetObjectMap, ContentObject.OBJECT_TYPE_MESSAGE,
                        allSourceObjectsWithDependenciesNeedExisting,
                        allSourceObjectsWithDependenciesSyncWhenChange,
                        null,
                        targetObjectIDsWithReferencingMap,
                        syncActiveOnly,
                        userInSourceSchema,
                        userInTargetSchema);
                }

                {
                    Map<Long, ContentObject> sourceIdToTargetObjectMap = new HashMap<>();
                    Map<ContentObject, Long> contentObjectStatusMap = SyncTouchpointUtil.getMessagesForSync(
                            targetDocument,
                            sourceDocument,
                            sourceSchema,
                            true,
                            includeRejectedChanges,
                            false,
                            activeOnly,
                            -1,
                            languagesForSync,
                            sourceIdToTargetObjectMap);

                    if(activeOnly) {
                        appendWorkingCopyReferencedNewObjects(sourceSchema, targetSchema, targetDocument, contentObjectStatusMap, sourceContentObjectIDsWithDependencies, allSourceObjectsWithDependenciesNeedExisting);
                    }

                    getSyncVersionedObjectsArray(data, SyncObjectType.ID_MESSAGE, contentObjectStatusMap, sourceIdToTargetObjectMap, ContentObject.OBJECT_TYPE_MESSAGE,
                            allSourceObjectsWithDependenciesNeedExisting,
                            allSourceObjectsWithDependenciesSyncWhenChange,
                            null,
                            targetObjectIDsWithReferencingMap,
                            syncActiveOnly,
                            userInSourceSchema,
                            userInTargetSchema);
                    getSyncVersionedObjectsArray(data, SyncObjectType.ID_LOCAL_IMAGE, contentObjectStatusMap, sourceIdToTargetObjectMap, ContentObject.OBJECT_TYPE_LOCAL_IMAGE,
                            allSourceObjectsWithDependenciesNeedExisting,
                            allSourceObjectsWithDependenciesSyncWhenChange,
                            null,
                            targetObjectIDsWithReferencingMap,
                            syncActiveOnly,
                            userInSourceSchema,
                            userInTargetSchema);
                    getSyncVersionedObjectsArray(data, SyncObjectType.ID_LOCAL_SMART_TEXT, contentObjectStatusMap, sourceIdToTargetObjectMap, ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT,
                            allSourceObjectsWithDependenciesNeedExisting,
                            allSourceObjectsWithDependenciesSyncWhenChange,
                            null,
                            targetObjectIDsWithReferencingMap,
                            syncActiveOnly,
                            userInSourceSchema,
                            userInTargetSchema);
                }
            });

            // Call getSyncList() it again to get rejected objects that are dependencies for objects in the sync list
            {
                JSONArray syncObjects = data;
                if (!includeRejectedChanges && !syncObjects.isEmpty()) {
                    JSONObject includeRejectedData = new JSONObject();
                    getSyncList(sourceDocumentId, sourceInstanceId, targetDocumentId, targetInstanceId, languages, activeOnly, syncActiveOnly, includeRejectedData, null, userInSourceSchema, userInTargetSchema, includeNotVisibleTargetRules, true);
                    JSONArray rejectedObjects = includeRejectedData.optJSONArray("data");
                    filterRejectedObjects(syncObjects, rejectedObjects);
                }
            }

            {
                // Filter objects that should be in the list only if they are a dependency to any other object in the sync list
                // otherwise remove from sync list
                JSONArray syncObjects = data;
                filterDependencyOnlyObjects(syncObjects);
            }

            //Update dependencies status, set up dependencies interaction
            {
                Map<Long, JSONObject> sourceObjectMap = new HashMap<>();
                Map<Long, JSONObject> targetObjectMap = new HashMap<>();

                for(int n = 0; n < data.length(); n ++) {
                    Object object = data.get(n);
                    if(object instanceof JSONObject) {
                        JSONObject jsonObject = (JSONObject) object;
                        Long id = jsonObject.getLong("id");
                        sourceObjectMap.put(id, jsonObject);
                        Long targetId = jsonObject.optLong("targetObjectId");
                        if(targetId != null && targetId.longValue() != 0) {
                            targetObjectMap.put(targetId, jsonObject);
                        }
                    }
                }

                Consumer<JSONArray> addSourceStatusToDependenciesList = (jarr) -> {
                    for(int i = 0; i < jarr.length(); i ++) {
                        JSONObject dep = (JSONObject) jarr.get(i);
                        Long depId = dep.getLong("id");

                        JSONObject jsonObject = sourceObjectMap.get(depId);
                        if(jsonObject != null) {
                            Integer statusCode = jsonObject.getInt("statusCode");
                            if(statusCode != null) {
                                dep.put("statusCode", statusCode);
                            }
/*
                            JSONObject statusObject = jsonObject.getJSONObject("status");

                            JSONObject sourceStatus = statusObject.getJSONObject("source");
                            if(statusObject != null) {
                                dep.put("status", sourceStatus);
                            }

                            JSONObject sourceActiveCopyStatus = statusObject.optJSONObject("sourceActiveCopy");
                            if(sourceActiveCopyStatus != null) {
                                dep.put("activeCopyStatus", sourceActiveCopyStatus);
                            }
*/
                            dep.put("isConflict", jsonObject.get("isConflict"));
                            dep.put("status", jsonObject.get("status"));
                        } else {
                            dep.put("noAction", true);
                        }
                    }
                };

                for(int n = 0; n < data.length(); n ++) {
                    Object object = data.get(n);
                    if (object instanceof JSONObject) {
                        JSONObject jsonObject = (JSONObject) object;
                        JSONObject sourceDependencies =  jsonObject.optJSONObject("sourceDependencies");
                        if(sourceDependencies != null) {
                            JSONArray needExisting = sourceDependencies.optJSONArray("needExisting");
                            if(needExisting != null) {
                                addSourceStatusToDependenciesList.accept(needExisting);
                            }
                            JSONArray syncWhenChange = sourceDependencies.optJSONArray("syncWhenChange");
                            if(syncWhenChange != null) {
                                addSourceStatusToDependenciesList.accept(syncWhenChange);
                            }
                        }

                        JSONObject targetDependencies = jsonObject.optJSONObject("targetDependencies");
                        if(targetDependencies != null) {
                            JSONArray refObjects = targetDependencies.optJSONArray("objects");
                            if(refObjects != null) {
                                for(int i = 0; i < refObjects.length(); i ++) {
                                    JSONObject dep = (JSONObject) refObjects.get(i);
                                    Long id = dep.getLong("id");
                                    JSONObject targetObject = targetObjectMap.get(id);
                                    if(targetObject != null) {
                                        Integer statusCode = targetObject.getInt("statusCode");
                                        if (statusCode != null) {
                                            dep.put("statusCode", statusCode);
                                        }
/*
                                        JSONObject statusObject = targetObject.getJSONObject("status");

                                        JSONObject targetStatus = statusObject.getJSONObject("target");
                                        if (targetStatus != null) {
                                            dep.put("status", targetStatus);
                                        }

                                        JSONObject targetActiveCopyStatus = statusObject.optJSONObject("targetActiveCopy");
                                        if (targetActiveCopyStatus != null) {
                                            dep.put("activeCopyStatus", targetActiveCopyStatus);
                                        }
*/
                                        dep.put("isConflict", jsonObject.get("isConflict"));
                                        dep.put("status", jsonObject.get("status"));
                                    } else {
                                        dep.put("noAction", true);
                                    }
                                }
                            }
                        }
                    }
                }

                // Process the target group/target rule impact case
                Map<Long, Set<Long>>  needExistingMap = new HashMap<>();
                Map<Long, Set<Long>>  syncWhenChangeMap = new HashMap<>();
                Map<Long, Set<Long>>  targetImpactMap = new HashMap<>();

                findIndirectDeps(sourceObjectMap, needExistingMap, (id) -> {
                    Set<Long> refObjectIds = new HashSet();
                    JSONObject jsonObject = sourceObjectMap.get(id);
                    if(jsonObject != null) {
                        JSONObject sourceDependencies = jsonObject.optJSONObject("sourceDependencies");
                        if (sourceDependencies != null) {
                            JSONArray needExisting = sourceDependencies.optJSONArray("needExisting");
                            if(needExisting != null) {
                                for (int i = 0; i < needExisting.length(); i++) {
                                    JSONObject sourceDependency = needExisting.getJSONObject(i);
                                    if(sourceDependency != null) {
                                        Long sourceObjectId = sourceDependency.getLong("id");
                                        JSONObject sourceObject = sourceObjectMap.get(sourceObjectId);
                                        if (sourceObject != null) {
                                            Long targetObjectId = sourceObject.optLong("targetObjectId");
                                            Boolean fromSource = sourceObject.getBoolean("fromSourceInstance");
                                            if (fromSource != null && fromSource.booleanValue() && targetObjectId == null) { // New dep object
                                                refObjectIds.add(sourceObjectId);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    return refObjectIds;
                });

                findIndirectDeps(sourceObjectMap, syncWhenChangeMap, (id) -> {
                    Set<Long> refObjectIds = new HashSet();
                    JSONObject jsonObject = sourceObjectMap.get(id);
                    if(jsonObject != null) {
                        JSONObject sourceDependencies =  jsonObject.optJSONObject("sourceDependencies");
                        if(sourceDependencies != null) {
                            JSONArray syncWhenChange = sourceDependencies.optJSONArray("syncWhenChange");
                            if(syncWhenChange != null) {
                                for (int i = 0; i < syncWhenChange.length(); i++) {
                                    JSONObject sourceDependency = syncWhenChange.getJSONObject(i);
                                    Long sourceObjectId = sourceDependency.getLong("id");
                                    refObjectIds.add(sourceObjectId);
                                }
                            }
                        }
                    }

                    return refObjectIds;
                });

                Set<Long> notSyncbaleIds = new HashSet<>();

                findIndirectDeps(sourceObjectMap, targetImpactMap, (id) -> {
                    Set<Long> refObjectIds = new HashSet();
                    JSONObject jsonObject = sourceObjectMap.get(id);
                    if(jsonObject != null) {
                        Boolean impactTargetDependencies = jsonObject.optBoolean("impactTargetDependencies");
                        if(impactTargetDependencies != null && impactTargetDependencies.booleanValue()) {
                            JSONObject sourceDependencies = jsonObject.optJSONObject("targetDependencies");
                            if (sourceDependencies != null) {
                                JSONArray refTargetObjectIds = sourceDependencies.optJSONArray("objects");
                                for (int i = 0; i < refTargetObjectIds.length(); i++) {
                                    JSONObject targetDependency = refTargetObjectIds.getJSONObject(i);
                                    Long targetObjectId = targetDependency.getLong("id");
                                    JSONObject syncObject = targetObjectMap.get(targetObjectId);
                                    if (syncObject != null) {
                                        Long sourceObjectId = syncObject.getLong("id");
                                        refObjectIds.add(sourceObjectId);
                                    } else { // the affected object is not in the sync list, thus it is not sync'able
                                        jsonObject.put("notSyncable", Boolean.TRUE); // dep is not syncable
                                        notSyncbaleIds.add(id);
                                        JSONArray affectedObjects = jsonObject.optJSONArray("affectedObjects");
                                        if(affectedObjects == null) {
                                            affectedObjects = new JSONArray();
                                            jsonObject.put("affectedObjects", affectedObjects);
                                        }
                                        affectedObjects.put(targetDependency);
                                    }
                                }
                            }
                        }
                    }

                    return refObjectIds;
                });

                // Sync together set is target group/target rule change caused the objects must be sync together or no sync at all relationship.
                List<Set<Long>> syncTogetherSets = new ArrayList<>(targetImpactMap.entrySet()
                    .stream()
                    .map(entry->{
                        Set<Long> setWithSelf = new HashSet<>(entry.getValue());
                        setWithSelf.add(entry.getKey());
                        return setWithSelf;
                    })
                    .collect(Collectors.toList()));

                mergeSyncTogetherSets(sourceObjectMap.keySet(), syncTogetherSets);

                Map<Long, Set<Long>> syncTogetherSetsMap = new HashMap<>(); // a sync object belongs to at most one syncTogetherSet.

                for(Set<Long> syncTogetherSet : syncTogetherSets) {
                    for(Long objectId : syncTogetherSet) {
                        syncTogetherSetsMap.put(objectId, syncTogetherSet);
                    }
                }

                Map<Long, Set<Long>> autoAcceptsMap = new HashMap<>();
                Map<Long, Set<Long>> autoUnacceptsMap = new HashMap<>();

                for(int thisObjectIndex = 0; thisObjectIndex < data.length(); thisObjectIndex ++) {
                    Object object = data.get(thisObjectIndex);

                    if (object instanceof JSONObject jsonObject) {
                        Long sourceObjectId = jsonObject.getLong("id");
                        Set<Long> syncTogetherSet = syncTogetherSetsMap.get(sourceObjectId);
                        if(syncTogetherSet == null) syncTogetherSet = new HashSet<>();
                        Set<Long> autoAccept = new HashSet<>(syncTogetherSet);
                        Set<Long> autoUnaccept = new HashSet(syncTogetherSet);
                        autoAcceptsMap.put(sourceObjectId, autoAccept);
                        autoUnacceptsMap.put(sourceObjectId, autoUnaccept);
                    }
                }

                for(int thisObjectIndex = 0; thisObjectIndex < data.length(); thisObjectIndex ++) {
                    Object object = data.get(thisObjectIndex);

                    if (object instanceof JSONObject jsonObject) {
                        Long thisObjectId = jsonObject.getLong("id");

                        Set<Long> autoAccept = autoAcceptsMap.get(thisObjectId);
                        if(autoAccept == null) {
                            autoAccept = new HashSet<>();
                            autoAcceptsMap.put(thisObjectId, autoAccept);
                        }

                        Set<Long> autoUnaccept = autoUnacceptsMap.get(thisObjectId);
                        if(autoUnaccept == null) {
                            autoUnaccept = new HashSet<>();
                            autoUnacceptsMap.put(thisObjectId, autoUnaccept);
                        }

                        {
                            Set<Long> needExistingIds = needExistingMap.get(thisObjectId);
                            if (needExistingIds != null) {
                                for (Long depObjectId : needExistingIds) {
                                    JSONObject depJsonObject = sourceObjectMap.get(depObjectId);
                                    if(depJsonObject == null) continue;
                                    Long targetObjectId = depJsonObject.optLong("targetObjectId");
                                    Boolean fromSource = depJsonObject.getBoolean("fromSourceInstance");
                                    if (fromSource != null && fromSource.booleanValue() && targetObjectId == null) { // New dep object
                                        // If this obj is accepted, dep should be accepted
                                        autoAccept.add(depObjectId);

                                        // If dep is unaccepted, this is unaccepted
                                        Set<Long>  depAutoUnaccept = autoUnacceptsMap.get(depObjectId);
                                        if(depAutoUnaccept == null) {
                                            depAutoUnaccept = new HashSet<>();
                                            autoUnacceptsMap.put(depObjectId, depAutoUnaccept);
                                        }

                                        depAutoUnaccept.add(thisObjectId);
                                    }
                                }
                            }
                        }

                        {
                            Set<Long> syncWhenChange = syncWhenChangeMap.get(thisObjectId);
                            if (syncWhenChange != null) {
                                for (Long depObjectId : syncWhenChange) {
                                    JSONObject depJsonObject = sourceObjectMap.get(depObjectId);
                                    if(depJsonObject == null) continue;
                                    Long targetObjectId = depJsonObject.optLong("targetObjectId");
                                    Boolean fromSource = depJsonObject.getBoolean("fromSourceInstance");
                                    if (true) { // Uncondtionally
                                        // If this obj is accepted, dep should be accepted
                                        autoAccept.add(depObjectId);

                                        // If dep is unaccepted, this is unaccepted
                                        Set<Long>  depAutoUnaccept = autoUnacceptsMap.get(depObjectId);
                                        if(depAutoUnaccept == null) {
                                            depAutoUnaccept = new HashSet<>();
                                            autoUnacceptsMap.put(depObjectId, depAutoUnaccept);
                                        }
                                        depAutoUnaccept.add(thisObjectId);
                                    }
                                }
                            }
                        }
                    }
                }

                for(int thisObjectIndex = 0; thisObjectIndex < data.length(); thisObjectIndex ++) {
                    Object object = data.get(thisObjectIndex);

                    if (object instanceof JSONObject jsonObject) {
                        Long thisObjectId = jsonObject.getLong("id");

                        mergeWithDeps(autoAcceptsMap, thisObjectId);
                        mergeWithDeps(autoUnacceptsMap, thisObjectId);

                        Function<Collection<Long>, JSONArray> objectId2SelectIds = (ids) -> {
                            JSONArray autoObjects = new JSONArray();
                            ids.forEach(id->{
                                if(sourceObjectMap.containsKey(id)) {
                                    JSONObject jo = sourceObjectMap.get(id);
                                    autoObjects.put("" +jo.getLong("objectType") + "_" + id + "_" + jo.getLong("statusCode"));
                                }
                            });
                            return autoObjects;
                        };

                        {
                            Set<Long> autoAccept = autoAcceptsMap.get(thisObjectId);
                            if (autoAccept != null && !autoAccept.isEmpty()) {
                                JSONArray autoAcceptObjects = objectId2SelectIds.apply(autoAccept);
                                jsonObject.put("autoAccepts", autoAcceptObjects);
                            }
                        }

                        {
                            Set<Long> autoUnaccept = autoUnacceptsMap.get(thisObjectId);
                            if (autoUnaccept != null && !autoUnaccept.isEmpty()) {
                                JSONArray autoUnacceptObjects = objectId2SelectIds.apply(autoUnaccept);
                                jsonObject.put("autoUnaccepts", autoUnacceptObjects);
                            }
                        }
                    }
                }

                for(Long notSyncableObjectId : notSyncbaleIds) {
                    JSONObject thisJsonObject = sourceObjectMap.get(notSyncableObjectId);
                    if(thisJsonObject != null) {
                        Set<Long> autoUnaccept = autoUnacceptsMap.get(notSyncableObjectId);
                        if(autoUnaccept != null) {
                            for(Long depId : autoUnaccept) {
                                JSONObject depSyncObject = sourceObjectMap.get(depId);
                                if(depSyncObject != null) {
                                    depSyncObject.put("notSyncable", Boolean.TRUE);
                                }
                            }
                        }
                    }
                }
            }


            statusData.put("stage", "completed");
            statusData.put("data", data);
            statusData.put("percentage", 100);
            if(saveStatus != null) saveStatus.accept(statusData);
        } catch (Exception e) {
            e.printStackTrace();
            statusData.put("stage", "error");
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            statusData.put("exception", sw.toString());
            if(saveStatus != null) saveStatus.accept(statusData);
        } finally {
            HibernateUtil.getManager().restoreSession(targetSessionHolder);
            HibernateUtil.getManager().restoreSession(sourceSessionHolder);
            HibernateUtil.getManager().restoreSession(mainSessionHolder);

            CloneHelper.setIsCloning(CloneHelper.ID_CLONING_TYPE_INACTIVE);
            CloneHelper.setIsSynchronizing(false);
            CloneHelper.clearResources();
        }
/*
        // Call getSyncList() it again to get rejected objects that are dependencies for objects in the sync list
        JSONArray syncObjects = statusData.getJSONArray("data");
        if(!includeRejectedChanges && !syncObjects.isEmpty()){
            JSONObject includeRejectedData = new JSONObject();
            getSyncList(sourceDocumentId, sourceInstanceId, targetDocumentId, targetInstanceId, languages, activeOnly, includeRejectedData, null, userInSourceSchema, userInTargetSchema, includeNotVisibleTargetRules, true);
            JSONArray rejectedObjects = includeRejectedData.optJSONArray("data");
            filterRejectedObjects(syncObjects, rejectedObjects);
        }
*/
        statusData.put("completedTimestamp", DateUtil.now());
        if(saveStatus != null) saveStatus.accept(statusData);
    }

    private static void findIndirectDeps(Map<Long, JSONObject> sourceObjectMap, Map<Long, Set<Long>>  needExistingMap, Function<Long, Set<Long>> getDepList) {
        LinkedList<Long> dataIdQueue = new LinkedList<>(sourceObjectMap.keySet());
        LinkedList<Long> backwardQueue = new LinkedList<>();
        while(! dataIdQueue.isEmpty()) {
            Long objectId= dataIdQueue.poll();
            backwardQueue.addFirst(objectId);

            Set<Long> depsIdList = getDepList.apply(objectId);
            for (Long depObjectId : depsIdList) {
                Set<Long> needExistingSet = needExistingMap.get(objectId);

                if (needExistingSet == null) {
                    needExistingSet = new HashSet<>();
                    needExistingMap.put(objectId, needExistingSet);
                }

                needExistingSet.add(depObjectId);

                if(!getDepList.apply(depObjectId).contains(objectId)){
                    dataIdQueue.remove(depObjectId);
                    dataIdQueue.add(depObjectId);
                    backwardQueue.remove(depObjectId);
                }
            }
        }

        while(! backwardQueue.isEmpty()) {
            Long objectId = backwardQueue.poll();
            Set<Long> needExists = needExistingMap.get(objectId);
            if(needExists != null) {
                Set<Long> needExistsToAdd = new HashSet<>();
                for (Long depId : needExists) {
                    Set<Long> depNeedExists = needExistingMap.get(depId);
                    if(depNeedExists != null) {
                        needExistsToAdd.addAll(depNeedExists);
                    }
                }
                needExists.addAll(needExistsToAdd);
            }
        }
    }

    // Merge the sets in syncTogetherSets if two sets contain common object id
    private static void mergeSyncTogetherSets(Set<Long> sourceObjectIDs, List<Set<Long>> syncTogetherSets) {
        for(Long sourceObjectId : sourceObjectIDs) {
            List<Set<Long>> setsToMerge = new ArrayList<>();
            for(Set<Long> togetherSet : syncTogetherSets) {
                if(togetherSet.contains(sourceObjectId)) {
                    setsToMerge.add(togetherSet);
                }
            }
            if(setsToMerge.size() > 1) {
                syncTogetherSets.removeAll(setsToMerge);
                Set<Long> newSet = new HashSet<>();
                for(Set<Long> setToMerge : setsToMerge) {
                    newSet.addAll(setToMerge);
                }
                syncTogetherSets.add(newSet);
            }
        }
    }

    private static void mergeWithDeps(Map<Long, Set<Long>> autoDepsMap, Long thisObjectId) {
        while(true) {
            Set<Long> autoDepsSet = autoDepsMap.get(thisObjectId);
            if (autoDepsSet == null) {
                autoDepsSet = new HashSet<>();
                autoDepsMap.put(thisObjectId, autoDepsSet);
            }

            Set<Long> newDepsSet = new HashSet<>();
            for (Long depId : autoDepsSet) {
                Set<Long> depSet = autoDepsMap.get(depId);
                if(depSet != null) {
                    newDepsSet.addAll(depSet);
                }
            }
            if (autoDepsSet.containsAll(newDepsSet)) {
                return;
            }
            autoDepsSet.addAll(newDepsSet);
        }
    }

    private static void appendWorkingCopyReferencedNewObjects(String sourceSchema, String targetSchema, Document targetDocument, Map<ContentObject, Long> contentObjectStatusMap, Map<Integer, Map<Long, List<String>>> sourceContentObjectIDsWithDependencies, Map<Long, List<String>> allSourceObjectsWithDependenciesNeedExisting) {
        Set<Long> handled = new HashSet<>();
        Queue<ContentObject> contentObjectQueue = new LinkedList<>(contentObjectStatusMap.keySet());
        while(! contentObjectQueue.isEmpty()) {
            ContentObject contentObject = contentObjectQueue.poll();
            if(contentObject == null) break;
            Long status = contentObjectStatusMap.get(contentObject);

            boolean fromSource = ((status & 0x10000) != 0);
/*
            Long sourceWorkingCopyStatus = status & 0xF;
            Long sourceActiveCopyStatus = (status >> 4) & 0xF;
            Long targetWorkingCopyStatus = (status >> 8) & 0xF;
            Long targetActiveCopyStatus = (status >> 12) & 0xF;
*/
            if(! fromSource) continue;

            Long contentObjectId = contentObject.getId();

            if(handled.contains(contentObjectId)) continue;
            handled.add(contentObjectId);

            if(! contentObject.hasWorkingData()) continue;

            Set<String> referencedObjectStringsList = new HashSet<>();
            for(Map<Long, List<String>> maps : sourceContentObjectIDsWithDependencies.values()) {
                List<String> refObjectStrings = maps.get(contentObjectId);
                if(refObjectStrings != null && ! refObjectStrings.isEmpty()) {
                    referencedObjectStringsList.addAll(refObjectStrings);
                }
            }

            if(allSourceObjectsWithDependenciesNeedExisting != null) {
                List<String> needExisting = allSourceObjectsWithDependenciesNeedExisting.get(contentObjectId);
                if(needExisting != null) {
                    referencedObjectStringsList.addAll(needExisting);
                }
            }

            for(String refObjectString : referencedObjectStringsList) {
                String [] refObjectStringComponents = refObjectString.split("-");
                if(refObjectStringComponents != null && refObjectStringComponents.length == 2) {
                    String refContentObjectType =  refObjectStringComponents[0];
                    Long refContentObjectId = Long.valueOf(refObjectStringComponents[1]);
                    if(refContentObjectType != null
                        && (refContentObjectType.equalsIgnoreCase("LocalSmartText") || refContentObjectType.equalsIgnoreCase("LocalImage"))
                        && refContentObjectId != null
                        && ! handled.contains(refContentObjectId)
                    ) {
                        ContentObject refContentObject = CloneHelper.queryInSchema(sourceSchema, () -> ContentObject.findById(refContentObjectId));
                        if(refContentObject != null
                            && refContentObject.isTouchpointLocal()
                            && refContentObject.hasWorkingData()
                            && ! refContentObject.hasActiveData())
                        {
                            String refContentObjectDna = refContentObject.getDna();
                            ContentObject targetContentObject = CloneHelper.queryInSchema(targetSchema, () -> ContentObject.findByDnaAndDocument(refContentObjectDna, targetDocument));
                            if(targetContentObject == null) {
                                Long refContentObjectStatus = (long) (0x010000              // Object is from source
                                    | (ContentSelectionStatusType.ID_NEW)                   // Source Working copy
                                    | (ContentSelectionStatusType.ID_NOT_APPLICABLE << 4)   // Source Active copy
                                    | (ContentSelectionStatusType.ID_NOT_APPLICABLE << 8)   // Target Working copy
                                    | (ContentSelectionStatusType.ID_NOT_APPLICABLE << 12)  // Target Active copy
                                );
                                contentObjectStatusMap.put(refContentObject, refContentObjectStatus);
                                contentObjectQueue.add(refContentObject);
                            }
                        }
                    }
                }
            }
        }
    }

    private static boolean isSyncReady() {
        if(! isSystemPropertyEqualsFalse(SystemPropertyKeys.Data.KEY_FixContentContentTargetingHash))
        {
            return false;
        }
        if(! isSystemPropertyEqualsFalse(SystemPropertyKeys.Data.KEY_HashAlgorithmChanged))
        {
            return false;
        }
        if(! isSystemPropertyEqualsFalse(SystemPropertyKeys.Data.KEY_HashAlgorithmChangedOnObjects))
        {
            return false;
        }
        return true;
    }

    private static boolean isSystemPropertyEqualsFalse(String propertyKey) {
        SystemProperty systemProperty = SystemProperty.findByKey(propertyKey);
        boolean isFalse = systemProperty == null ||  systemProperty.getValue() == null || ! systemProperty.getValue().equalsIgnoreCase("true");
        return isFalse;
    }

    private static void filterRejectedObjects(JSONArray syncObjects, JSONArray rejectedObjects){
        List<Long> syncObjectIds = syncObjects.toList().stream().map(o -> {
            HashMap object = (HashMap) o;
            return (Long) object.get("id");
        }).collect(Collectors.toList());

        // Collect ids of all dependencies from sync list
        List<Long> syncObjectDependencyIds = new ArrayList<>();
        for(int i=0; i < syncObjects.length(); i++){
            JSONObject jsonObject = (JSONObject) syncObjects.get(i);
            JSONObject sourceDependencies = jsonObject.optJSONObject("sourceDependencies");

            if(sourceDependencies != null) {
                if (sourceDependencies.optBoolean("hasDependencies")) {
                    JSONArray needExisting = sourceDependencies.optJSONArray("needExisting");
                    if (needExisting != null) {
                        needExisting.forEach(o -> {
                            JSONObject needExistingObject = (JSONObject) o;
                            syncObjectDependencyIds.add(needExistingObject.getLong("id"));
                        });
                    }

                    JSONArray syncWhenChanged = sourceDependencies.optJSONArray("syncWhenChange");
                    if (syncWhenChanged != null) {
                        syncWhenChanged.forEach(o -> {
                            JSONObject syncWhenChangedObject = (JSONObject) o;
                            syncObjectDependencyIds.add(syncWhenChangedObject.getLong("id"));
                        });
                    }

                }
            }
        }


        for (int i=0; i < rejectedObjects.length(); i++){
            JSONObject rejectedObject = rejectedObjects.getJSONObject(i);
            Long id = rejectedObject.getLong("id");

            // Make sure it is not in the original list to avoid duplicates
            if(syncObjectIds.contains(id)){
                continue;
            }

            // If this is a rejected object and it is a dependency for one of the other objects - add to sync list
            if(syncObjectDependencyIds.contains(id)){
                syncObjects.put(rejectedObject);
            }
        }
    }

    private static void filterDependencyOnlyObjects(JSONArray syncObjects){
        List<Integer> objectIndexesToRemove = new ArrayList<>();
        List<Long> removedObjectIds = new ArrayList<>();

        for (int i=0; i < syncObjects.length(); i++){
            JSONObject object = syncObjects.getJSONObject(i);

            if(removeIfNoDependents(syncObjects, object)){
                objectIndexesToRemove.add(i);
            }
        }

        objectIndexesToRemove.sort(Comparator.reverseOrder());
        for(Integer index: objectIndexesToRemove){
            syncObjects.remove(index);
        }
    }

    private static Boolean removeIfNoDependents(JSONArray syncObjects, JSONObject object){
        Long id = object.getLong("id");

        // The flag set to false, return FALSE
        if(!object.optBoolean("addOnlyIfDependency")) return false;

        Set<JSONObject> dependentObjects = getDependentObjects(syncObjects, object);

        // Flag set to TRUE and there are no dependents - return TRUE
        if(CollectionUtils.isEmpty(dependentObjects)) return true;

        // Check if dependent objects will be removed too
        for(JSONObject dependentObject: dependentObjects){
            if(!removeIfNoDependents(syncObjects, dependentObject)) return false;
        }

        return true;
    }

    private static Set<JSONObject> getDependentObjects(JSONArray allObjects, JSONObject object){
        Set<JSONObject> dependentObjectsList = new HashSet<>();
        Long objectId = object.getLong("id");

        for(int i=0; i < allObjects.length(); i++){
            JSONObject jsonObject = (JSONObject) allObjects.get(i);
            Long dependentObjectId = jsonObject.getLong("id");
            JSONObject sourceDependencies = jsonObject.optJSONObject("sourceDependencies");

            if(sourceDependencies != null) {
                if (sourceDependencies.optBoolean("hasDependencies")) {
                    JSONArray needExisting = sourceDependencies.optJSONArray("needExisting");
                    if (needExisting != null) {
                        needExisting.forEach(o -> {
                            JSONObject needExistingObject = (JSONObject) o;
                            Long dependencyId = needExistingObject.getLong("id");
                            if(dependencyId.equals(objectId)){
                                dependentObjectsList.add(jsonObject);
                            }
                        });
                    }

                    JSONArray syncWhenChanged = sourceDependencies.optJSONArray("syncWhenChange");
                    if (syncWhenChanged != null) {
                        syncWhenChanged.forEach(o -> {
                            JSONObject syncWhenChangedObject = (JSONObject) o;
                            Long dependencyId = syncWhenChangedObject.getLong("id");
                            if(dependencyId.equals(objectId)){
                                dependentObjectsList.add(jsonObject);
                            }
                        });
                    }

                }
            }
        }
        return dependentObjectsList;
    }

    private static void addAllVariableParameterReference(String schema, Map<Long, List<ReferencableObject>> allParameterGroupReferencesMap, Map<Long, List<ReferencableObject>> allVariableReferencesMap) {
        CloneHelper.execInSchema(schema, ()-> {
            for (Long parameterGroupId : allParameterGroupReferencesMap.keySet()) {
                ParameterGroup parameterGroup = ParameterGroup.findById(parameterGroupId);
                if (parameterGroup.getParameterGroupItems().size() == 1) {
                    Parameter parameter = parameterGroup.getFirstParameterGroupItem().getParameter();
                    if (parameter != null) {
                        DataElementVariable dataElementVariable = parameter.getDataElementVariable();
                        if (dataElementVariable != null) {
                            Long dataElementVariableID = dataElementVariable.getId();
                            List<ReferencableObject> objectsReferencesParameterGroup = allParameterGroupReferencesMap.get(parameterGroupId);
                            List<ReferencableObject> objectsReferencesVariable = allVariableReferencesMap.get(dataElementVariableID);
                            if (objectsReferencesVariable == null) {
                                objectsReferencesVariable = objectsReferencesParameterGroup;
                                allVariableReferencesMap.put(dataElementVariableID, objectsReferencesVariable);
                            } else {
                                for (ReferencableObject referencableObject : objectsReferencesParameterGroup) {
                                    String targetClassName = referencableObject.getTargetClassName();
                                    long referencableObjectId = referencableObject.getObjectId();
                                    if (!objectsReferencesVariable.stream().anyMatch(ro -> ro.getTargetClassName().equals(targetClassName) && ro.getObjectId() == referencableObjectId)) {
                                        objectsReferencesVariable.add(referencableObject);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    private static void addAllVariableConnectedReference(String schema, Long documentId, Map<Long, List<ReferencableObject>> allVariableReferencesMap) {
        CloneHelper.execInSchema(schema, ()-> {
            Document document = Document.findById(documentId);

            Set<DataElementVariable> devs = new HashSet<>();

            if(document != null && document.isConnectedEnabled() && document.getDataSourceAssociation() != null) {
                DataSourceAssociation dataSourceAssociation = document.getDataSourceAssociation();
                for(ReferenceConnection referenceConnection : dataSourceAssociation.getReferenceConnections()) {
                    if(referenceConnection.getReferenceDataSource().getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED) {
                        DataElementVariable primaryVariable = referenceConnection.getPrimaryVariable();
                        if(primaryVariable != null) {
                            devs.add(primaryVariable);
                        }

                        DataElementVariable referenceVariable = referenceConnection.getReferenceVariable();
                        if(referenceVariable != null) {
                            devs.add(referenceVariable);
                        }

                        CompoundKey primaryCompoundKey = referenceConnection.getPrimaryCompoundKey();
                        if (primaryCompoundKey != null) {
                            for (CompoundKeyItem compoundKeyItem : primaryCompoundKey.getKeyItems()) {
                                if (compoundKeyItem.getVariable() != null) {
                                    devs.add(compoundKeyItem.getVariable());
                                }
                            }
                        }

                        CompoundKey referenceCompoundKey = referenceConnection.getReferenceCompoundKey();
                        if (referenceCompoundKey != null) {
                            for (CompoundKeyItem compoundKeyItem : referenceCompoundKey.getKeyItems()) {
                                if (compoundKeyItem.getVariable() != null) {
                                    devs.add(compoundKeyItem.getVariable());
                                }
                            }
                        }
                    }
                }
            }

            if(document != null && document.isCommunicationOrderEntryEnabled() && document.getCommunicationOrderEntryItemDefinitions() != null) {
                for(CommunicationOrderEntryItemDefinition itemDef : document.getCommunicationOrderEntryItemDefinitions()) {
                    if(itemDef.getDataElementVariable() != null) {
                        devs.add(itemDef.getDataElementVariable());
                    }
                    if(itemDef.getConnectorVariableMap() != null) {
                        devs.addAll(itemDef.getConnectorVariableMap().values());
                    }
                }
            }

            IdentifiableMessagePointModel referencingModelObject = SyncTouchpointUtil.getPsuedoDocumentSettingsObject(DocumentSettingsModel.ID_SETTING_CONNECTED_INTERVIEW, document, document.getConnectedInterviewSettingsMap());
            ReferencableObject referencableObject = new ReferencableObject(referencingModelObject);

            for(DataElementVariable dataElementVariable : devs) {
                List<ReferencableObject> list = allVariableReferencesMap.get(dataElementVariable.getId());
                if(list == null) {
                    list = new ArrayList<>();
                    allVariableReferencesMap.put(dataElementVariable.getId(), list);
                }
                list.add(referencableObject);
            }
        });
    }

    private static void addTouchhpointAllMetadataFormTemplateReference(String schema, Long documentId, Map<Long, List<ReferencableObject>> allMetadataFormTemplateReferencesMap) {
        CloneHelper.execInSchema(schema, ()-> {
            Document document = Document.findById(documentId);

            if(document != null) {
                MetadataFormDefinition touchpointMetadataFormTemplate = document.getTouchpointMetadataFormDefinition();
                if(touchpointMetadataFormTemplate != null) {
                    IdentifiableMessagePointModel referencingModelObject = SyncTouchpointUtil.getPsuedoDocumentSettingsObject(DocumentSettingsModel.ID_SETTING_TOUCHPOINT_METADATA, document, document.getTouchpointMetadataSettingsMap());
                    ReferencableObject referencableObject = new ReferencableObject(referencingModelObject);
                    List<ReferencableObject> list = allMetadataFormTemplateReferencesMap.get(touchpointMetadataFormTemplate.getId());
                    if(list == null) {
                        list = new ArrayList<>();
                        allMetadataFormTemplateReferencesMap.put(touchpointMetadataFormTemplate.getId(), list);
                    }
                    list.add(referencableObject);
                }

                MetadataFormDefinition variantMetadataFormTemplate = document.getVariantMetadataFormDefinition();
                if(variantMetadataFormTemplate != null) {
                    IdentifiableMessagePointModel referencingModelObject = SyncTouchpointUtil.getPsuedoDocumentSettingsObject(DocumentSettingsModel.ID_SETTING_VARIANT_METADATA_TEMPLATE, document, document.getVariantMetadataSettingsMap());
                    ReferencableObject referencableObject = new ReferencableObject(referencingModelObject);
                    List<ReferencableObject> list = allMetadataFormTemplateReferencesMap.get(variantMetadataFormTemplate.getId());
                    if(list == null) {
                        list = new ArrayList<>();
                        allMetadataFormTemplateReferencesMap.put(variantMetadataFormTemplate.getId(), list);
                    }
                    list.add(referencableObject);
                }
            }
        });
    }

    private static void addAllLookupTableDataSourceReference(String schema, Map<Long, List<ReferencableObject>> allDataSourceReferencesMap, Map<Long, List<ReferencableObject>> allLookupTableReferencesMap) {
        CloneHelper.execInSchema(schema, ()-> {
            for (Long dataSourceId : allDataSourceReferencesMap.keySet()) {
                DataSource dataSource = DataSource.findById(dataSourceId);
                if (dataSource.isLookupTableDataSource()) {
                    Set<LookupTable> lookupTables = LookupTableInstance.findAll().stream().filter(lti->(lti.isActive() || lti.isWorkingCopy()) && lti.getDataSource().getId() == dataSourceId).map(lti->lti.getModel()).collect(Collectors.toSet());
                    for(LookupTable lookupTable : lookupTables) {
                        Long lookupTableId = lookupTable.getId();
                        List<ReferencableObject> objectsReferencesDataSource = allDataSourceReferencesMap.get(dataSourceId);
                        List<ReferencableObject> objectsReferencesLookupTable = allLookupTableReferencesMap.get(lookupTableId);
                        if (objectsReferencesLookupTable == null) {
                            objectsReferencesLookupTable = objectsReferencesDataSource;
                            allLookupTableReferencesMap.put(lookupTableId, objectsReferencesLookupTable);
                        } else {
                            for (ReferencableObject referencableObject : objectsReferencesDataSource) {
                                String targetClassName = referencableObject.getTargetClassName();
                                long referencableObjectId = referencableObject.getObjectId();
                                if (!objectsReferencesLookupTable.stream().anyMatch(ro -> ro.getTargetClassName().equals(targetClassName) && ro.getObjectId() == referencableObjectId)) {
                                    objectsReferencesLookupTable.add(referencableObject);
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    private static <SyncObject extends IdentifiableMessagePointModel> void getSyncObjectsArray
            (JSONArray jsonArray,
             Integer objectType,
             Map<SyncObject, Long> objectStatusMap,
             Map<Long, SyncObject> sourceIdToTargetModelMap,
             Map<Long, List<String>> allSourceObjectsWithDependenciesNeedExisting,
             Map<Long, List<String>> allSourceObjectsWithDependenciesSyncWhenChange,
             Map<Long, List<ReferencableObject>> allTargetObjectReferencesMapInTargetSchema,
             BiConsumer<JSONObject, IdentifiableMessagePointModel> customizeJsonObject) {
        for (SyncObject object : objectStatusMap.keySet()) {
            Integer status = objectStatusMap.get(object).intValue();
            Boolean impactTargetDependencies = ((status & 0x200000L) != 0);
            int sourceWorkingCopyStatus = status & 0xF;
            int targetWorkingCopyStatus = (status >> 8) & 0xF;
            IdentifiableMessagePointModel model = (IdentifiableMessagePointModel) object;
            String objectSchema = model.getObjectSchemaName();
            JSONObject jsonObject = new JSONObject();
            CloneHelper.execInSchema(objectSchema, () -> {
                Boolean addOnlyIfDependency = ((status & 0x100000) != 0);
                Boolean fromSource = ((status & 0x10000) != 0);

                jsonObject.put("id", model.getId());
                jsonObject.put("name", model.getName());
                jsonObject.put("objectType", objectType);
                jsonObject.put("objectTypeName", new SyncObjectType(objectType).getName());
                jsonObject.put("objectTypeCode", SyncTouchpointUtil.getRefObjectType(model));
                jsonObject.put("statusCode", status);
                jsonObject.put("addOnlyIfDependency", addOnlyIfDependency);
                jsonObject.put("fromSourceInstance", fromSource);
                jsonObject.put("impactTargetDependencies", impactTargetDependencies);
                jsonObject.put("sourceDependencies", getSourceDependencies(model.getId(),
                        allSourceObjectsWithDependenciesNeedExisting,
                        allSourceObjectsWithDependenciesSyncWhenChange
                    ));

                {
                    JSONObject statusObject = new JSONObject();
                    statusObject.put("source", getSyncStatusJson(sourceWorkingCopyStatus));
                    statusObject.put("target", getSyncStatusJson(targetWorkingCopyStatus));

                    if(targetWorkingCopyStatus == ContentSelectionStatusType.ID_UNASSOCIATED ||
                        (sourceWorkingCopyStatus == ContentSelectionStatusType.ID_DIFFERENTUSAGES)
                    ) {
                        jsonObject.put("excludeFromRejection", true);
                    }

                    jsonObject.put("status", statusObject);
                }

                jsonObject.put("isConflict", AsyncTouchpointSyncListComparator.isConflicted(jsonObject.toMap()));
            });

            Long sourceModelId = model.getId();
            SyncObject targetObject = sourceIdToTargetModelMap.get(sourceModelId);
            if(targetObject != null) {
                String targetObjectSchema = targetObject.getObjectSchemaName();
                CloneHelper.execInSchema(targetObjectSchema, () -> {
                    jsonObject.put("targetObjectId", targetObject.getId());
                    jsonObject.put("targetObjectName", targetObject.getName());
                    jsonObject.put("targetDependencies", getTargetDependencies(
                            targetObject.getId(), false,
                            (sourceWorkingCopyStatus == ContentSelectionStatusType.ID_DELETED || sourceWorkingCopyStatus == ContentSelectionStatusType.ID_NOT_APPLICABLE),
                        false, false,
                            allTargetObjectReferencesMapInTargetSchema, null
                        ));

                });
            }
            if(customizeJsonObject != null){
                customizeJsonObject.accept(jsonObject, model);
            }

            jsonArray.put(jsonObject);
        }
    }

    private static <SyncObject extends IdentifiableMessagePointModel> void getSyncObjectsArray
            (JSONArray jsonArray,
             Integer objectType,
             Map<SyncObject, Long> objectStatusMap,
             Map<Long, SyncObject> sourceIdToTargetModelMap,
             Map<Long, List<String>> allSourceObjectsWithDependenciesNeedExisting,
             Map<Long, List<String>> allSourceObjectsWithDependenciesSyncWhenChange,
             Map<Long, List<ReferencableObject>> allTargetObjectReferencesMapInTargetSchema
            ) {
        getSyncObjectsArray(
                jsonArray,
                objectType,
                objectStatusMap,
                sourceIdToTargetModelMap,
                allSourceObjectsWithDependenciesNeedExisting,
                allSourceObjectsWithDependenciesSyncWhenChange,
                allTargetObjectReferencesMapInTargetSchema,
                null);
    }

    private static <SyncObject extends IdentifiableMessagePointModel> void getSyncVersionedObjectsArray
            (JSONArray jsonArray,
             Integer objectType,
             Map<SyncObject, Long> contentObjectStatusMap,
             Map<Long, SyncObject> sourceIdToTargetObjectMap,
             Integer contentObjectType,
             Map<Long, List<String>> allSourceObjectsWithDependenciesNeedExisting,
             Map<Long, List<String>> allSourceObjectsWithDependenciesSyncWhenChange,
             Map<Long, List<ReferencableObject>> allTargetObjectReferencesMapInTargetSchema,
             Map<Long, List<String>> allContentObjectReferencesMapInTargetSchema,
             boolean syncActiveOnly,
             User userInSourceSchema,
             User userInTargetSchema) {
        for (SyncObject object : contentObjectStatusMap.keySet()) {
            if (object instanceof ContentObject) {
                ContentObject contentObject = (ContentObject) object;
                if (contentObjectType != null && contentObject != null && contentObject.getObjectType() != contentObjectType.intValue())
                    continue;
            }

            {
                JSONObject jsonObject = new JSONObject();

                Integer status = contentObjectStatusMap.get(object).intValue();
                String objectSchema = object.getObjectSchemaName();

                Boolean fromSource = ((status & 0x10000) != 0);
                int sourceWorkingCopyStatus = status & 0xF;
                int sourceActiveCopyStatus = (status >> 4) & 0xF;
                int targetWorkingCopyStatus = (status >> 8) & 0xF;
                int targetActiveCopyStatus = (status >> 12) & 0xF;


                int sourceDeletionStateCode = CloneHelper.queryInSchema(objectSchema, () -> {
                    int deletionStateCode = 0;
                    boolean multiVersion = true;

                    jsonObject.put("id", object.getId());
                    jsonObject.put("name", object.getName());

                    if (object instanceof ContentObject) {
                        ContentObject contentObject = (ContentObject) object;
                        if (contentObject.isMessage() || contentObject.isLocalSmartText() || contentObject.isLocalImage()) {
                            TouchpointSelection touchpointSelection = contentObject.getOwningTouchpointSelection();

                            if (touchpointSelection == null)
                                touchpointSelection = contentObject.getDocument().getMasterTouchpointSelection();
                            if (touchpointSelection != null) {
                                jsonObject.put("variant", touchpointSelection.getNameWithParentInfo());
                            }

                            Long objectId = object.getId();
                            TouchpointSelection sourceTouchpointSelection = contentObject.getOwningTouchpointSelection();
                            if(sourceTouchpointSelection != null) {
                                String depVariantString = "TouchpointSelection-" + sourceTouchpointSelection.getId();
                                if(contentObject.isStructuredContentEnabled() && contentObject.hasWorkingData() && touchpointSelection.getDocument().isEnabledForVariantWorkflow()) {
                                    String touchpointSelectionDna = sourceTouchpointSelection.getDna();
                                    SyncObject targetObject = sourceIdToTargetObjectMap.get(objectId);
                                    if (targetObject != null && targetObject instanceof ContentObject) {
                                        ContentObject targetContentObject = (ContentObject) targetObject;
                                        if (CloneHelper.queryInSchema(targetObject.getObjectSchemaName(), () -> !targetContentObject.hasWorkingData())) {
                                            TouchpointSelection targetTouchpointSelection = CloneHelper.queryInSchema(targetObject.getObjectSchemaName(), () -> TouchpointSelection.findByDnaAndDocument(touchpointSelectionDna, targetContentObject.getDocument()));
                                            if (targetTouchpointSelection != null && CloneHelper.queryInSchema(targetObject.getObjectSchemaName(), () -> ! targetTouchpointSelection.getHasWorkingCopy())) {
                                                List<String> syncWhenChange = allSourceObjectsWithDependenciesSyncWhenChange.get(objectId);
                                                if(syncWhenChange == null) {
                                                    syncWhenChange = new ArrayList<>();
                                                    allSourceObjectsWithDependenciesSyncWhenChange.put(objectId, syncWhenChange);
                                                }
                                                syncWhenChange.add(depVariantString);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        String objectTypeCode = SyncTouchpointUtil.getRefObjectType(contentObject);
                        jsonObject.put("objectTypeCode", objectTypeCode);
                    } else if(object instanceof LookupTable) {
                        LookupTable lookupTable = (LookupTable) object;
                        String objectTypeCode = SyncTouchpointUtil.getRefObjectType(lookupTable);
                        jsonObject.put("objectTypeCode", objectTypeCode);
                    } else if(object instanceof TouchpointSelection) {
                        TouchpointSelection touchpointSelection = (TouchpointSelection) object;
                        String objectTypeCode = SyncTouchpointUtil.getRefObjectType(touchpointSelection);
                        jsonObject.put("objectTypeCode", objectTypeCode);

                        if (! touchpointSelection.isMaster()) {
                            ParameterGroupTreeNode pgtn = touchpointSelection.getParameterGroupTreeNode();
                            if(pgtn != null) {
                                ParameterGroupTreeNode parentPgtn = pgtn.getParentNode();
                                TouchpointSelection parentTouchpointSelection;

                                if(parentPgtn == null) {
                                    parentTouchpointSelection = touchpointSelection.getDocument().getMasterTouchpointSelection();
                                } else {
                                    parentTouchpointSelection = TouchpointSelection.findByPgTreeNodeId(parentPgtn.getId());
                                }

                                if (parentTouchpointSelection != null) {
                                    jsonObject.put("variant", parentTouchpointSelection.getNameWithParentInfo());
                                }
                            }
                        }

                        List<String> dependenciesList = new ArrayList<>();

                        TouchpointSelection touchpointSelectionParent = touchpointSelection.getParent();
                        if(touchpointSelectionParent != null) {
                            dependenciesList.add("TouchpointSelection" + "-" + touchpointSelectionParent.getId());
                        }

                        if(! dependenciesList.isEmpty()) {
                            List<String> dependenciesNeedExisting = allSourceObjectsWithDependenciesNeedExisting.get(touchpointSelection.getId());
                            if(dependenciesNeedExisting == null) {
                                dependenciesNeedExisting = new ArrayList<>();
                                allSourceObjectsWithDependenciesNeedExisting.put(touchpointSelection.getId(), dependenciesNeedExisting);
                            }

                            dependenciesNeedExisting.addAll(dependenciesList);
                        }

                        if(! touchpointSelection.getDocument().isEnabledForVariantWorkflow()) {
                            multiVersion = false;
                        }
                    }

                    deletionStateCode = getDeletionStateCode(object);

                    boolean workingCopyWillBeDeletedSyncActiveOnly = // Source has active copy and working copy, and target has working copy, may or may not have active copy.
                        multiVersion && syncActiveOnly &&
                        (
                            sourceActiveCopyStatus == ContentSelectionStatusType.ID_NEW
                                || sourceActiveCopyStatus == ContentSelectionStatusType.ID_CHANGED
                                || sourceActiveCopyStatus == ContentSelectionStatusType.ID_UNCHANGED
                                || sourceActiveCopyStatus == ContentSelectionStatusType.ID_ACTIVATED
                                || sourceActiveCopyStatus == ContentSelectionStatusType.ID_FIRST_TIME_SYNC
                                || sourceActiveCopyStatus == ContentSelectionStatusType.ID_UNASSOCIATED
                                || sourceActiveCopyStatus == ContentSelectionStatusType.ID_DIFFERENTLANGUAGES
                                || sourceActiveCopyStatus == ContentSelectionStatusType.ID_DIFFERENTUSAGES
                        )
                        &&
                        (
                            sourceWorkingCopyStatus == ContentSelectionStatusType.ID_NEW
                                || sourceWorkingCopyStatus == ContentSelectionStatusType.ID_CHANGED
                                || sourceWorkingCopyStatus == ContentSelectionStatusType.ID_UNCHANGED
                                || sourceWorkingCopyStatus == ContentSelectionStatusType.ID_FIRST_TIME_SYNC
                                || sourceWorkingCopyStatus == ContentSelectionStatusType.ID_UNASSOCIATED
                                || sourceWorkingCopyStatus == ContentSelectionStatusType.ID_DIFFERENTLANGUAGES
                                || sourceWorkingCopyStatus == ContentSelectionStatusType.ID_DIFFERENTUSAGES
                        )
                        &&
                        (
                            targetWorkingCopyStatus == ContentSelectionStatusType.ID_NEW
                                || targetWorkingCopyStatus == ContentSelectionStatusType.ID_CHANGED
                                || targetWorkingCopyStatus == ContentSelectionStatusType.ID_UNCHANGED
                                || targetWorkingCopyStatus == ContentSelectionStatusType.ID_FIRST_TIME_SYNC
                                || targetWorkingCopyStatus == ContentSelectionStatusType.ID_UNASSOCIATED
                                || targetWorkingCopyStatus == ContentSelectionStatusType.ID_DIFFERENTLANGUAGES
                                || targetWorkingCopyStatus == ContentSelectionStatusType.ID_DIFFERENTUSAGES
                        )
                    ;

                    boolean workingCopyWillBeDeletedSourceHasNoWorkingCopy = // Source has no working copy thus target working copy will be deleted
                        multiVersion
                            && (
                                sourceWorkingCopyStatus == ContentSelectionStatusType.ID_DELETED
                                    || sourceWorkingCopyStatus == ContentSelectionStatusType.ID_NOT_APPLICABLE
                            )
                            && (
                                targetWorkingCopyStatus == ContentSelectionStatusType.ID_NEW
                                    || targetWorkingCopyStatus == ContentSelectionStatusType.ID_CHANGED
                                    || targetWorkingCopyStatus == ContentSelectionStatusType.ID_UNCHANGED
                                    || targetWorkingCopyStatus == ContentSelectionStatusType.ID_FIRST_TIME_SYNC
                                    || targetWorkingCopyStatus == ContentSelectionStatusType.ID_UNASSOCIATED
                                    || targetWorkingCopyStatus == ContentSelectionStatusType.ID_DIFFERENTLANGUAGES
                                    || targetWorkingCopyStatus == ContentSelectionStatusType.ID_DIFFERENTUSAGES
                            );

                    boolean workingCopyWillBeDeleted = workingCopyWillBeDeletedSyncActiveOnly || workingCopyWillBeDeletedSourceHasNoWorkingCopy;

                    jsonObject.put("objectType", objectType);
                    jsonObject.put("objectTypeName", new SyncObjectType(objectType).getName());
                    jsonObject.put("statusCode", status);
                    jsonObject.put("fromSourceInstance", fromSource);
                    jsonObject.put("sourceDependencies", getSourceDependencies(object.getId(),
                            allSourceObjectsWithDependenciesNeedExisting,
                            allSourceObjectsWithDependenciesSyncWhenChange));

                    jsonObject.put("multiVersion", multiVersion);
                    jsonObject.put("syncActiveOnly", syncActiveOnly);

                    if(multiVersion) {
                        if(syncActiveOnly || true) {
                            jsonObject.put("workingCopyWillBeDeleted", workingCopyWillBeDeleted);
                        }
                    }

                    {
                        JSONObject statusObject = new JSONObject();

                        statusObject.put("source", getSyncStatusJson(sourceWorkingCopyStatus));
                        statusObject.put("target", getSyncStatusJson(targetWorkingCopyStatus));

                        if(multiVersion) {
                            statusObject.put("sourceActiveCopy", getSyncStatusJson(sourceActiveCopyStatus));
                            statusObject.put("targetActiveCopy", getSyncStatusJson(targetActiveCopyStatus));
                        }

                        jsonObject.put("status", statusObject);
                    }
                    jsonObject.put("isConflict", AsyncTouchpointSyncListComparator.isConflicted(jsonObject.toMap()));
                    return deletionStateCode;
                });

                Long objectId = object.getId();
                SyncObject targetObject = sourceIdToTargetObjectMap.get(objectId);
                if(targetObject != null) {
                    String targetObjectSchema = targetObject.getObjectSchemaName();
                    CloneHelper.execInSchema(targetObjectSchema, () -> {
                        boolean isGlobalObject = false;
                        if(targetObject instanceof ContentObject) {
                            ContentObject contentObject = (ContentObject) targetObject;
                            isGlobalObject = contentObject.isGlobalContentObject();
                        } else if(targetObject instanceof LookupTable) {
                            isGlobalObject = true;
                        } else if(targetObject instanceof TouchpointSelection) {
                            isGlobalObject = false;
                        }

                        int targetDeletionStateCode = getDeletionStateCode(targetObject);
                        int deletionStateCode = sourceDeletionStateCode; //(sourceDeletionStateCode ^ targetDeletionStateCode) & sourceDeletionStateCode;
                        jsonObject.put("targetObjectId", targetObject.getId());
                        jsonObject.put("targetObjectName", targetObject.getName());
                        jsonObject.put("targetDependencies", getTargetDependencies(targetObject.getId(),
                            isGlobalObject,
                            ((deletionStateCode & 0x10) != 0), // Will delete object
                            ((deletionStateCode & 0x02) != 0), // Will archive active copy
                            ((deletionStateCode & 0x01) != 0), // Will delete working copy
                            allTargetObjectReferencesMapInTargetSchema,
                            allContentObjectReferencesMapInTargetSchema));

                        if(targetObject instanceof ContentObject){
                            ContentObject contentObject = (ContentObject) targetObject;
                            if(contentObject.hasWorkingData()) {
                                jsonObject.put("isAssignedToCurrentUser", contentObject.getLockedForId() == 0 || contentObject.isMine(userInTargetSchema));

                                if (contentObject.getLockedForUser() != null) {
                                    jsonObject.put("assignedUser", contentObject.getLockedForUser().getName());
                                }
                            }
                        }
                        if (targetObject instanceof LookupTable) {
                            LookupTable lookupTable = (LookupTable) targetObject;
                            if(lookupTable.getWorkingCopy() != null){
                                LookupTableInstance lookupTableInstance = lookupTable.getWorkingCopy();
                                jsonObject.put("isAssignedToCurrentUser", lookupTableInstance.isMine(userInTargetSchema));
                                jsonObject.put("assignedUser", lookupTableInstance.getAssignedToUserName());
                            }
                        }
                        if(targetObject instanceof TouchpointSelection) {
                            TouchpointSelection touchpointSelection = (TouchpointSelection) targetObject;
                            if(!touchpointSelection.getDocument().isEnabledForVariantWorkflow()) {
                                jsonObject.put("isAssignedToCurrentUser", true);
                            }
                            else if(touchpointSelection.getHasWorkingCopy()) {
                                jsonObject.put("isAssignedToCurrentUser", touchpointSelection.getAssigneeId() == null || touchpointSelection.getAssigneeId().longValue() == userInTargetSchema.getId());

                                if (touchpointSelection.getAssigneeId() != null) {
                                    User assignee = User.findById(touchpointSelection.getAssigneeId());
                                    if(assignee != null) {
                                        jsonObject.put("assignedUser", assignee.getName());
                                    }
                                }
                            }
                        }

                        if(targetWorkingCopyStatus == ContentSelectionStatusType.ID_UNASSOCIATED ||
                            targetActiveCopyStatus == ContentSelectionStatusType.ID_UNASSOCIATED
                        ) {
                            jsonObject.put("excludeFromRejection", true);
                        }

                    });
                }

                jsonArray.put(jsonObject);
            }
        }
    }

    private static <SyncObject extends IdentifiableMessagePointModel> int getDeletionStateCode(SyncObject syncObject) {
        int stateCode = 0;

        if(syncObject instanceof ContentObject) {
            ContentObject contentObject = (ContentObject) syncObject;
            if (! contentObject.hasWorkingData()) stateCode |= 1;
            if (! contentObject.hasActiveData()) stateCode |= 2;
            if (! contentObject.hasArchivedData()) stateCode |= 4;
            if (contentObject.isRemoved()) stateCode = 0x10;
            if(stateCode == 0x07) stateCode = 0x10;
        } else if(syncObject instanceof LookupTable) {
            LookupTable lookupTable = (LookupTable) syncObject;
            if(lookupTable.getWorkingCopy() == null) stateCode |= 1;
            if(lookupTable.getActiveCopy() == null) stateCode |= 2;
            LookupTableVersionMapping lookupTableVersionMapping = lookupTable.getLatestArchivedVersionInfo();
            if(lookupTableVersionMapping == null || lookupTableVersionMapping.getModelInstance() == null) stateCode |= 4;
            if(lookupTable.isRemoved()) stateCode = 0x10;
            if(stateCode == 0x07) stateCode = 0x10;
        } else if(syncObject instanceof TouchpointSelection) {
            TouchpointSelection touchpointSelection = (TouchpointSelection) syncObject;
            if(! touchpointSelection.getHasWorkingCopy()) stateCode |= 1;
            if(! touchpointSelection.getHasActiveCopy()) stateCode |= 2;
            if(touchpointSelection.isDeleted()) stateCode = 0x10;
            if(stateCode == 0x03) stateCode = 0x10;
        }

        return stateCode;
    }

    private static JSONObject getSyncStatusJson(Integer status) {
        JSONObject jsonObject = new JSONObject();
        ContentSelectionStatusType contentSelectionStatusType = new ContentSelectionStatusType(status);
        jsonObject.put("id", contentSelectionStatusType.getId());
        jsonObject.put("name", contentSelectionStatusType.getName());
        jsonObject.put("displayCode", contentSelectionStatusType.getDisplayMessageCode());
        jsonObject.put("displayText", contentSelectionStatusType.getDisplayText());
        return jsonObject;
    }

    private static JSONObject getSourceDependencies(Long modelId,
                                                    Map<Long, List<String>> allSourceObjectsWithDependenciesNeedExisting,
                                                    Map<Long, List<String>> allSourceObjectsWithDependenciesSyncWhenChange)
    {
        JSONObject dep = new JSONObject();
        boolean hasDependencies = false;
        JSONArray needExisting = new JSONArray();
        JSONArray syncWhenChange = new JSONArray();

        if(allSourceObjectsWithDependenciesNeedExisting != null && ! allSourceObjectsWithDependenciesNeedExisting.isEmpty()) {
            List<String> deps = allSourceObjectsWithDependenciesNeedExisting.get(modelId);
            if(deps != null && ! deps.isEmpty()) {
                for(String typeAndId : deps) {
                    String [] typeIdString = typeAndId.split("-");
                    String refObjectType = typeIdString[0];
                    Long id = Long.valueOf(typeIdString[1]);
                    JSONObject jsonObject = new JSONObject();
                    needExisting.put(jsonObject);
                    jsonObject.put("id", id);
                    getRefObjectInfo(jsonObject, refObjectType, id);
                }

                hasDependencies = true;
            }
        }

        if(allSourceObjectsWithDependenciesSyncWhenChange != null && ! allSourceObjectsWithDependenciesSyncWhenChange.isEmpty()) {
            List<String> deps = allSourceObjectsWithDependenciesSyncWhenChange.get(modelId);
            if(deps != null && ! deps.isEmpty()) {
                for(String typeAndId : deps) {
                    String [] typeIdString = typeAndId.split("-");
                    String refObjectType = typeIdString[0];
                    Long id = Long.valueOf(typeIdString[1]);

                    JSONObject jsonObject = new JSONObject();
                    syncWhenChange.put(jsonObject);
                    needExisting.put(jsonObject);
                    jsonObject.put("id", id);
                    getRefObjectInfo(jsonObject, refObjectType, id);
                }

                hasDependencies = true;
            }
        }

        dep.put("hasDependencies", hasDependencies);

        if(hasDependencies) {
            dep.put("needExisting", needExisting);
            dep.put("syncWhenChange", syncWhenChange);
        }

        return dep;
    }

    private static JSONObject getTargetDependencies(Long modelId,
                                                    boolean isGlobalObject,
                                                    boolean syncToDeleteObject,
                                                    boolean syncToDropActiveCopy,
                                                    boolean syncToDropWorkingCopy,
                                                    Map<Long, List<ReferencableObject>> allTargetObjectReferencesMapInTargetSchema,
                                                    Map<Long, List<String>> allContentObjectReferencesMapInTargetSchema
    ) {
        JSONObject dep = new JSONObject();
        JSONArray refObjects = new JSONArray();
        boolean isReferenced = false;

        if(allTargetObjectReferencesMapInTargetSchema != null) {
            List<ReferencableObject> referencableObjects = allTargetObjectReferencesMapInTargetSchema.get(modelId);
            if(referencableObjects != null && ! referencableObjects.isEmpty()) {
                List<IdentifiableMessagePointModel> refModels = SyncTouchpointUtil.getObjectFromReferencableObjectList(referencableObjects);
                for (IdentifiableMessagePointModel refModel : refModels) {
                    JSONObject jsonObject = new JSONObject();
                    refObjects.put(jsonObject);
                    Long id = refModel.getId();
                    jsonObject.put("id", id);
                    String refObjectType = SyncTouchpointUtil.getRefObjectType(refModel);
                    getRefObjectInfo(jsonObject, refObjectType, id);
                    if(refModel instanceof ContentObject) {
                        ContentObject refContentObject = (ContentObject) refModel;
                        if(syncToDeleteObject) {
                            if(
                                refContentObject.hasWorkingData() || refContentObject.hasActiveData() || refContentObject.hasArchivedData()
                            ) {
                                isReferenced = true;
                            }
                        }
                        else if(isGlobalObject) {
                            if(syncToDropActiveCopy) {
                                if(
                                    isContentObjectDataReferencing(refContentObject, ContentObject.DATA_TYPE_WORKING, modelId, isGlobalObject) ||
                                    isContentObjectDataReferencing(refContentObject, ContentObject.DATA_TYPE_ACTIVE, modelId, isGlobalObject)
                                ) {
                                    isReferenced = true;
                                }
                            }
                        } else {
                            if(syncToDropActiveCopy) {
                                if(
                                        isContentObjectDataReferencing(refContentObject, ContentObject.DATA_TYPE_ACTIVE, modelId, isGlobalObject)
                                ) {
                                    isReferenced = true;
                                }
                            }
                            if(syncToDropActiveCopy && syncToDropWorkingCopy) {
                                if(
                                    isContentObjectDataReferencing(refContentObject, ContentObject.DATA_TYPE_WORKING, modelId, isGlobalObject)
                                ) {
                                    isReferenced = true;
                                }
                            }
                        }
                    } else if(isGlobalObject) {
                        if (syncToDeleteObject || syncToDropActiveCopy) {
                            isReferenced = true;
                        }
                    } else if(syncToDeleteObject) {
                            isReferenced = true;
                    }
                }
            }
        }

        if(allContentObjectReferencesMapInTargetSchema != null) {
            List<String> referencableObjectTypeAndIDs = allContentObjectReferencesMapInTargetSchema.get(modelId);
            if(referencableObjectTypeAndIDs != null && ! referencableObjectTypeAndIDs.isEmpty()) {
                for (String typeAndId : referencableObjectTypeAndIDs) {
                    String[] typeIdString = typeAndId.split("-");
                    String refObjectType = typeIdString[0];
                    Long id = Long.valueOf(typeIdString[1]);

                    JSONObject jsonObject = new JSONObject();
                    refObjects.put(jsonObject);
                    jsonObject.put("id", id);

                    getRefObjectInfo(jsonObject, refObjectType, id);

                    ContentObject refContentObject = ContentObject.findById(id);

                    if(syncToDeleteObject) {
                        if (refContentObject.hasWorkingData() || refContentObject.hasActiveData() || refContentObject.hasArchivedData()) {
                            isReferenced = true;
                        }
                    }
                    else if(isGlobalObject) {
                        if(syncToDropActiveCopy) {
                            if(
                                isContentObjectDataReferencing(refContentObject, ContentObject.DATA_TYPE_WORKING, modelId, isGlobalObject) ||
                                isContentObjectDataReferencing(refContentObject, ContentObject.DATA_TYPE_ACTIVE, modelId, isGlobalObject)
                            ) {
                                isReferenced = true;
                            }
                        }
                    } else {
                        if(syncToDropActiveCopy) {
                            if(
                                isContentObjectDataReferencing(refContentObject, ContentObject.DATA_TYPE_ACTIVE, modelId, isGlobalObject)
                            ) {
                                isReferenced = true;
                            }
                        }
                        if(syncToDropActiveCopy && syncToDropWorkingCopy) {
                            if(
                                isContentObjectDataReferencing(refContentObject, ContentObject.DATA_TYPE_WORKING, modelId, isGlobalObject)
                                || isContentObjectDataReferencing(refContentObject, ContentObject.DATA_TYPE_ACTIVE, modelId, isGlobalObject)
                            ) {
                                isReferenced = true;
                            }
                        }
                    }
                }
            }
        }

        dep.put("objects", refObjects);
        dep.put("isReferenced", isReferenced);

        return dep;
    }

    static private boolean isContentObjectDataReferencing(ContentObject contentObject, int dataType, Long modelId, boolean isGlobalContentObject) {
        if(! contentObject.hasDataType(dataType)) return false;

        ContentObjectData contentObjectData = contentObject.getContentObjectData(dataType);

        List<ContentObjectAssociation> coas = contentObjectData.getContentObjectAssociations(false);
        for(ContentObjectAssociation coa : coas) {
            if(coa.getReferencingImageLibrary() != null && coa.getReferencingImageLibrary().getId() == modelId) {
                return true;
            }

            Content content = coa.getContent();
            if(content == null) continue;
            Map<ContentObject, Integer> contentObjectMap =  content.getContentObjectsTypeMap();
            if(contentObjectMap != null) {
                if(contentObjectMap.keySet().stream().anyMatch(c->c.getId() == modelId)) {
                    return true;
                }
            }

            if(isGlobalContentObject && contentObjectData.getDataType() == ContentObject.DATA_TYPE_ACTIVE) {
                if(isComplexValueReferencing(content.getImageLink(), modelId)) return true;
                if(isComplexValueReferencing(content.getImageAltText(), modelId)) return true;
                if(isComplexValueReferencing(content.getImageExtLink(), modelId)) return true;
                if(isComplexValueReferencing(content.getImageExtPath(), modelId)) return true;
            }
        }

        return false;
    }

    static private boolean isComplexValueReferencing(ComplexValue complexValue, Long modelId) {
        if(complexValue == null) return false;
        Set<ContentObject> globalSmartTexts = complexValue.getGlobalSmartTexts();
        if(globalSmartTexts == null) return false;
        return globalSmartTexts.stream().anyMatch(co->co.getId() == modelId);
    }

    static private void getRefObjectInfo(JSONObject jsonObject, String refObjectType, Long id) {
        Integer objectType = null;
        String objectName = null;
        Long documentId = null;
        String documentName = null;

        switch(refObjectType.toLowerCase(Locale.ROOT)) {
            case "message": {
                objectType = SyncObjectType.ID_MESSAGE;
                ContentObject contentObject = ContentObject.findById(id);
                objectName = contentObject.getName();
                Document document = contentObject.getDocument();
                if(document != null) {
                    documentId = document.getId();
                    documentName = document.getName();
                }
                break;
            }
            case "localsmarttext": {
                objectType = SyncObjectType.ID_LOCAL_SMART_TEXT;
                ContentObject contentObject = ContentObject.findById(id);
                objectName = contentObject.getName();
                Document document = contentObject.getDocument();
                if(document != null) {
                    documentId = document.getId();
                    documentName = document.getName();
                }
                break;
            }
            case "localimage": {
                objectType =  SyncObjectType.ID_LOCAL_IMAGE;
                ContentObject contentObject = ContentObject.findById(id);
                objectName = contentObject.getName();
                Document document = contentObject.getDocument();
                if(document != null) {
                    documentId = document.getId();
                    documentName = document.getName();
                }
                break;
            }
            case "smarttext": {
                objectType = SyncObjectType.ID_SMART_TEXT;
                ContentObject contentObject = ContentObject.findById(id);
                objectName = contentObject.getName();
                break;
            }
            case "image": {
                objectType = SyncObjectType.ID_CONTENT_LIBRARY;
                ContentObject contentObject = ContentObject.findById(id);
                objectName = contentObject.getName();
                break;
            }
            case "targetgroup": {
                objectType = SyncObjectType.ID_TARGET_GROUP;
                TargetGroup targetGroup = TargetGroup.findById(id);
                objectName = targetGroup.getName();
                break;
            }
            case "rule":
            case "targetrule": {
                objectType = SyncObjectType.ID_TARGET_RULE;
                ConditionElement rule = ConditionElement.findById(id);
                objectName = rule.getName();
                break;
            }
            case "document": {
                objectType = SyncObjectType.ID_DOCUMENT;
                Document document = Document.findById(id);
                objectName = document.getName();
                if(document.isAlternate() || document.isChannelAlternate()) {
                    Document rootDocument = document.getRootDocument();
                    if(rootDocument != null) {
                        documentId = rootDocument.getId();
                        documentName = rootDocument.getName();
                    }
                }
                break;
            }

            case "documentsettings":
            case "documentsetting": {
                objectType = SyncObjectType.ID_DOCUMENT_SETTING;
                objectName = SyncTouchpointUtil.getPsuedoDocumentSettingsName(id.intValue());
                break;
            }

            case "parametergroup": {
                objectType = SyncObjectType.ID_PARAMETER_GROUP;
                ParameterGroup parameterGroup = ParameterGroup.findById(id);
                objectName = parameterGroup.getName();
                break;
            }
            case "datasource": {
                objectType = SyncObjectType.ID_DATASOURCE;
                DataSource dataSource = DataSource.findById(id);
                objectName = dataSource.getName();
                break;
            }
            case "lookuptable": {
                objectType = SyncObjectType.ID_LOOKUPTABLE;
                LookupTable lookupTable = LookupTable.findById(id);
                LookupTableInstance lookupTableInstance = lookupTable.getLatestProductionCentric();
                objectName = lookupTableInstance.getName();
                break;
            }
            case "datacollection": {
                objectType = SyncObjectType.ID_DATACOLLECTION;
                DataSourceAssociation dataSourceAssociation = DataSourceAssociation.findById(id);
                objectName = dataSourceAssociation.getName();
                break;
            }
            case "dataelementvariable":
            case "variable": {
                objectType = SyncObjectType.ID_VARIABLE;
                DataElementVariable dataElementVariable = DataElementVariable.findById(id);
                objectName = dataElementVariable.getName();
                break;
            }
            case "textstyle": {
                objectType = SyncObjectType.ID_TEXT_STYLE;
                TextStyle textStyle = TextStyle.findById(id);
                objectName = textStyle.getName();
                break;
            }
            case "textstylefont": {
                objectType = SyncObjectType.ID_TEXT_STYLE_FONT;
                TextStyleFont textStyleFont = TextStyleFont.findById(id);
                objectName = textStyleFont.getName();
                break;
            }
            case "dataresource": {
                objectType = SyncObjectType.ID_DATA_RESOURCE;
                DataResource dataResource = DataResource.findById(id);
                objectName = dataResource.getName();
                break;
            }
            case "datafile": {
                objectType = SyncObjectType.ID_DATA_FILE;
                DataFile dataFile = DataFile.findById(id);
                objectName = dataFile.getName();
                break;
            }
            case "channelconfiguration": {
                objectType = SyncObjectType.ID_CHANNEL_CONFIGURATION;
                ConnectorConfiguration connectorConfig = ConnectorConfiguration.findById(id);
                objectName = connectorConfig.getName();
                break;
            }
            case "compositionpackage": {
                objectType = SyncObjectType.ID_COMPOSITION_PACKAGE;
                CompositionFileSet compositionFileSet = CompositionFileSet.findById(id);
                objectName = compositionFileSet.getName();
                break;
            }
            case "touchpointselection":
            case "touchpointvariant":
            case "variant": {
                objectType = SyncObjectType.ID_VARIANT;
                TouchpointSelection touchpointSelection = TouchpointSelection.findById(id);
                objectName = touchpointSelection.getName();
                break;
            }
            case "liststyle": {
                objectType = SyncObjectType.ID_LIST_STYLE;
                ListStyle listStyle = ListStyle.findById(id);
                objectName = listStyle.getName();
                break;
            }
            case "paragraphstyle": {
                objectType = SyncObjectType.ID_PARAGRAPH_STYLE;
                ParagraphStyle paragraphStyle = ParagraphStyle.findById(id);
                objectName = paragraphStyle.getName();
                break;
            }
            case "metadatatemplate":
            case "metadataformtemplate":
            case "metadataformdefinition": {
                objectType = SyncObjectType.ID_METADATAFORM_DEFINITION;
                MetadataFormDefinition metadataFormDefinition = MetadataFormDefinition.findById(id);
                objectName = metadataFormDefinition.getName();
                break;
            }

        }

        jsonObject.put("objectTypeCode", refObjectType);

        if(objectType != null) {
            jsonObject.put("objectType", objectType);
            jsonObject.put("objectTypeName", new SyncObjectType(objectType).getName());
        }

        if(objectName != null) {
            jsonObject.put("name", objectName);
        }

        if(documentId != null) {
            jsonObject.put("document", documentId);
        }

        if(documentName != null) {
            jsonObject.put("documentName", documentName);
        }
    }

    static private void info(String message){
        LogUtil.getLog(GetSyncListBackgroundTask.class).info("\uD83C\uDF44: " + message);
    }

    static private void error(String message, Exception ex){
        LogUtil.getLog(GetSyncListBackgroundTask.class).error("\uD83C\uDF44: " + message, ex);
    }
}
