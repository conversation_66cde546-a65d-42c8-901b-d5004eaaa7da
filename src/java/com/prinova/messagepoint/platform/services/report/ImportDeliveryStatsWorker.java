package com.prinova.messagepoint.platform.services.report;

import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.prinova.messagepoint.model.content.ContentObject;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;
import org.hibernate.Session;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.batch.SplitReportFile;
import com.prinova.messagepoint.model.admin.SystemState;
import com.prinova.messagepoint.model.deliveryevent.Job;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.reports.MessageReportParser;
import com.prinova.messagepoint.reports.model.report.BatchOperationStatistics;
import com.prinova.messagepoint.reports.model.report.Customer;
import com.prinova.messagepoint.reports.model.report.CustomerField;
import com.prinova.messagepoint.reports.model.report.ExecutedVariable;
import com.prinova.messagepoint.reports.model.report.JobStatisticsVariant;
import com.prinova.messagepoint.reports.model.report.Report;
import com.prinova.messagepoint.reports.model.report.ReportGraphic;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;

public class ImportDeliveryStatsWorker 
{
	private static final Log log = LogUtil.getLog(ImportDeliveryStatsWorker.class);
	public static final String STRING_NO_MESSAGES_ERROR = "NO MESSAGES";

	ImportDeliveryStatsServiceRequest request = null;

	ImportDeliveryStatsWorker( ServiceExecutionContext context )
	{
		request = (ImportDeliveryStatsServiceRequest) context.getRequest();
	}
	
	private static final int BATCH_SIZE = 10000;

	long reportCount = 0;
	long evCount = 0;
	long rgCount = 0;
	
	Map<MessageVariantPair, ReportStatistic> messageMap = new HashMap<>();
	
	public static class MessageVariantPair
	{
		private long messageId;
		private long variantId;
		
		public MessageVariantPair(long mid, long vid )
		{
			messageId = mid;
			variantId = vid;
		}
		
		public long getMessageId()
		{
			return messageId;
		}
		public void setMessageId( long id )
		{
			messageId = id;
		}
		
		public void setVariantId( long id )
		{
			variantId = id;
		}
		public long getVariantId()
		{
			return variantId;
		}

		@Override
		public int hashCode() 
		{
			final int PRIME = 31;
			int result = 1;
			result = PRIME * result + (Long.valueOf(messageId).hashCode());
			result = PRIME * result + (Long.valueOf(variantId).hashCode());
			return result;
		}
		@Override
		public boolean equals( Object obj )
		{
			if (this == obj)
				return true;
			if (!(obj instanceof MessageVariantPair))
				return false;

			final MessageVariantPair other = (MessageVariantPair) obj;
			return other.messageId == messageId && other.variantId == variantId;
		}
	}
	
	private static class ReportStatistic {
		public int count;
		public int qualified;
		public int played;
		public long messageId;

		public ReportStatistic() 
		{
			count = qualified = played = 0;
		}
	}
	
	public void run() throws Exception
	{
		NativeQuery reportPs = null;
		NativeQuery variablePs = null;
		NativeQuery customerPs = null;
		NativeQuery customerVariablesPs = null;
		NativeQuery graphicPs = null;
		
		SplitReportFile reader = request.getCombinedMsgDeliveryFile();
		Job job = HibernateUtil.getManager().getObject(Job.class, request.getJobid());

		String line = reader.GetLine();
		Report report = new MessageReportParser().parse(job, 0, line);
		if ( report.getJobId() != job.getId() )
		{
			throw new Exception("Mismatch between jobid parameter and jobid given in delivery report " + report.getJobId() + ":" + job.getId());
		}
		if ( report.getPartId() != request.getPartid() && report.getPartId() != -1)
		{
			throw new Exception("Mismatch between batchid parameter and batchid given in delivery report" + report.getPartId() + ":" + request.getPartid());
		}
			
		try
		{
			String jobId = String.valueOf(request.getJobid());
			String partId = String.valueOf(request.getPartid());
			String transactionId = "'" + request.getTransactionId() + "'";
			Session session = HibernateUtil.getManager().getSession();
			reportPs = session.createNativeQuery("INSERT INTO report( sq, job_id, part_id, customer_id, zone_id, content_object_id, variant_id, tp_selectable_id, played, transaction_id ) VALUES (:1,"
					+ jobId + "," + partId + ",:2,:3,:4,:5,:6,:7," + transactionId + ") " +
					" on conflict (job_id, part_id, transaction_id, sq) do " +
					" update set customer_id = :2, zone_id = :3, content_object_id = :4, variant_id = :5, tp_selectable_id = :6, played = :7");
			variablePs = session.createNativeQuery("INSERT INTO executed_variable(sq, job_id, part_id, report_sequence, name, f_value, transaction_id) VALUES (:1,"
					+ jobId + "," + partId + ",:2,:3,:4, " + transactionId + ") " +
					" on conflict (job_id, part_id, transaction_id, sq) do " +
					" update set report_sequence = :2, name = :3, f_value = :4");
			customerPs = session.createNativeQuery("INSERT INTO customer(job_id, part_id, customer_id, language_code, email_guid, email_status, document_delivered, email_address, transaction_id) VALUES ("
					+ jobId + "," + partId + ",:1,:2,:3,:4,:5,:6, " + transactionId + ") " +
					" on conflict (job_id, part_id, transaction_id, customer_id) do " +
					" update set language_code = :2, email_guid = :3, email_status = :4, document_delivered = :5, email_address = :6");
			customerVariablesPs = session.createNativeQuery("INSERT INTO customer_field( job_id, part_id, customer_id_value, field, f_value, transaction_id) VALUES ("
					+ jobId + "," + partId + ",:1,:2,:3, " + transactionId + ") " +
					" on conflict (job_id, part_id, transaction_id, customer_id_value, field) do " +
					" update set f_value = :3");
			graphicPs = session.createNativeQuery("INSERT INTO report_graphic( job_id, batch_id, seq, report_sequence, part_number, content_library_id, content_library_selection_id, transaction_id ) VALUES (" 
					+ jobId + "," + partId + ",:1,:2,:3,:4,:5, " + transactionId + ") " +
					" on conflict (job_id, batch_id, transaction_id, seq) do " +
					" update set report_sequence = :2, part_number = :3, content_library_id = :4, content_library_selection_id = :5");
		} catch (Exception e) {
			log.error("Problem with SQL preparation: " + e.getMessage());
			return;
		}
			
		boolean done = false;
		while (!done) 
		{
			log.debug("ReportFileBufferedReader:next() - Processing next batch: " + reportCount);

			done = unitOfWork(reader, job, request.getPartid(),	reportPs, variablePs, customerPs, customerVariablesPs, graphicPs);
		}
		SaveStats(messageMap, job, request.getPartid(), request.getTransactionId());

		if (request.isUpdateJobStatus() == true) {
			job.setStatus(SystemState.STATUS_TYPE_COMPLETED);
		}

		HibernateUtil.getManager().saveObject(job, false);

		log.debug("ReportProcessor:processNext() - Reports saved for Job with id " + request.getJobid());
	}
	
	@Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false)
	public boolean unitOfWork(
			SplitReportFile reader,
			Job job,
			int partid,
			NativeQuery reportPs,
			NativeQuery variablePs,
			NativeQuery customerPs,
			NativeQuery customerVariablesPs,
			NativeQuery graphicPs) throws Exception
	{
		boolean done = false;
		int batchCount = 0;
		
		while ( (batchCount) < BATCH_SIZE ) 
		{
			++reportCount;
			++batchCount;
			try {
				String line = reader.GetLine();
				if (line == null) {
					done = true;
					--reportCount;
					break;
				}

				// Don't process empty lines (but also keep reading)
				if (line.trim().isEmpty() || line.trim().equals(STRING_NO_MESSAGES_ERROR)) {
					continue;
				}

				log.debug("ReportFileBufferedReader:next() - Parsing next line" + line);
				Report report = new MessageReportParser().parse(job, partid, line);
				if ( report.getMessageId() != 0 )
				{
					long msgId = report.getMessageId();
					ContentObject contentObject = ContentObject.findById(msgId);
					long contentObjectId = contentObject.getId();
					report.setMessageId(contentObjectId);
				}
				
				String customerId = report.getCustomerId();

				if (report.getMessageReportId() != 0) 
				{
					long msgId = report.getMessageReportId();
					long varId = report.getVariantReportId();
					MessageVariantPair key = new MessageVariantPair(msgId, varId);
					ReportStatistic mrs = messageMap.get(key);

					if (mrs == null) {
						mrs = new ReportStatistic();
						mrs.messageId = msgId;
						messageMap.put(key, mrs);
					}

					mrs.count = (int) report.getTotalCount();

					if (mrs.played != report.getDeliveryCount())
						log.error("Mismatch on played count for message: " + msgId + " var: " + varId
								+ " Counted: " + mrs.played + " Reported: " + report.getDeliveryCount());
					if (mrs.qualified != report.getQualifyCount())
						log.error("Mismatch on qualification count for message: " + msgId+ " var: " + varId
								+ " Counted: " + mrs.qualified + " Reported: " + report.getQualifyCount());

					--reportCount;
					--batchCount;
					continue;
				}

				if ( report.getCustCount() != 0 )
				{
				    BatchOperationStatistics bos = new BatchOperationStatistics();
				    bos.setJobId(job.getId());
				    bos.setBatchId(partid);
				   
				    bos.setQualEngStart(report.getQeStart());
				    bos.setQualEngFinish(report.getQeFinish());
				    bos.setDeliveryStart(report.getDeliveryStart());
				    bos.setDeliveryFinish(report.getDeliveryFinish());
				    bos.setRecipientCount(report.getCustCount());
				    
				    HibernateUtil.getManager().saveObject(bos);
				    
                    --reportCount;
                    --batchCount;
                    continue;
				}
				
				try {
					reportPs.setParameter("1", reportCount);
					reportPs.setParameter("2", customerId);
					reportPs.setParameter("3", report.getZoneId());
					reportPs.setParameter("4", report.getMessageId());
					reportPs.setParameter("5", report.getVariantId());
					reportPs.setParameter("6", report.getTpSelectableId());
					reportPs.setParameter("7", report.isPlayed());

					reportPs.executeUpdate();
				} catch (Exception se) {
					log.error("Problem with (report) SQL insert: " + se.getMessage());
					return true;
				}

				if (report.getMessageId() != 0) {
					AddStatsToSet(messageMap, report);
				}

				Set<ExecutedVariable> evs = report.getExecutedVariables();
				if (evs != null) {
					log.debug("There are " + evs.size() + " variables");

					for (ExecutedVariable ev : evs) {
						try {
							variablePs.setParameter("1", evCount++);
							variablePs.setParameter("2", reportCount);
							variablePs.setParameter("3", ev.getName());
							String value = limitTo4000(ev.getValue());
							variablePs.setParameter("4", value);

							variablePs.executeUpdate();
						} catch (Exception se) {
							log.error(variablePs.toString());
							log.error("Problem with (variable) SQL insert" + se.getMessage());
							break;
						}
					}
				}

				Set<ReportGraphic> rgs = report.getReportGraphics();
				if ( rgs != null )
				{
					log.debug("There are " + rgs.size() + " graphics");
					
					for( ReportGraphic rg : rgs )
					{
						try
						{
							graphicPs.setParameter("1", rgCount++);
							graphicPs.setParameter("2", reportCount);
							graphicPs.setParameter("3", rg.getPartNumber());
							graphicPs.setParameter("4", rg.getContentLibraryId());
							graphicPs.setParameter("5", rg.getContentLibrarySelectionId());

							graphicPs.executeUpdate();
						} catch (Exception se) {
							log.error(variablePs.toString());
							log.error("Problem with (graphic) SQL insert" + se.getMessage());
							break;
						}
					}
				}
				
				if (report.getCustomerLang() != null && !report.getCustomerLang().isEmpty())
				{
					Customer customer = report.getCustomerObject();
					boolean delivered = (customer.getEmailStatus() == 1 || customer.getEmailAddress() == null || customer.getEmailAddress().isEmpty());

					try {
						customerPs.setParameter("1", customerId);
						customerPs.setParameter("2", customer.getLanguageCode());
						customerPs.setParameter("3", customer.getEmailGuid());
						customerPs.setParameter("4", customer.getEmailStatus());
						customerPs.setParameter("5", delivered);
						customerPs.setParameter("6", customer.getEmailAddress());
						
						customerPs.executeUpdate();
					} catch (Exception se) {
						log.error("1 - `" + customerId);
						log.error("2 - `" + customer.getLanguageCode() + "`");
						log.error("3 - `" + customer.getEmailGuid() + "`");
						log.error("4 - `" + customer.getEmailStatus() + "`");
						log.error("5 - `" + delivered + "`");
						log.error("6 - `" + customer.getEmailAddress() + "`");
						log.error("Customer SQL query is: `" + customerPs.getQueryString() + "`");
						log.error("Problem with (customer) SQL insert", se);
						throw se;
					}

					Set<CustomerField> cfs = customer.getCustomerFields();
					if (cfs != null) {
						for (CustomerField cf : cfs) {
							if (cf.getValue().isEmpty())
								continue;
							try {
								customerVariablesPs.setParameter("1", customerId);
								customerVariablesPs.setParameter("2", cf.getField());
								customerVariablesPs.setParameter("3", cf.getValue());

								customerVariablesPs.executeUpdate();
							} catch (Exception se) {
								log.error("Problem with (customer field) SQL insert" + se.getMessage());
								break;
							}
						}
					}
				}
			} catch (IOException e) {
				log.error("ReportFileBufferedReader:next() - IOException while reading Reports file. Processing exiting early. ", e);
				reportCount--;
				throw e;
			}
		}
		return done;
	}

	private void AddStatsToSet(
			Map<MessageVariantPair, ReportStatistic> dataset, 
			Report report) 
	{
		long id = report.getMessageId();
		long vid = report.getVariantId();
		MessageVariantPair mvp = new MessageVariantPair(id, vid);
		
		if (id == 0)
		{
			log.error("Report has no message id for customer: " + report.getCustomerId() );
			return;
		}

		if (dataset.containsKey(mvp)) {
			ReportStatistic rp = dataset.get(mvp);
			if (report.isQualified())
				rp.qualified++;
			if (report.isPlayed())
				rp.played++;
			rp.count++;
		} else {
			ReportStatistic rp = new ReportStatistic();
			rp.messageId = report.getMessageId();
			
			if (report.isQualified())
				rp.qualified++;
			if (report.isPlayed())
				rp.played++;
			rp.count++;

			dataset.put(mvp, rp);
		}
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false)
	public void SaveStats(
			Map<MessageVariantPair, ReportStatistic> dataset,
			Job job,
			int part,
			String transaction)
	{
		Set<MessageVariantPair> keys = dataset.keySet();
		Iterator<MessageVariantPair> i = keys.iterator();

		while ( i.hasNext() ) {
			try {
				MessageVariantPair key = i.next();
				long msgid = key.getMessageId();
				long varid = key.getVariantId();
				ReportStatistic rs = dataset.get(key);

				StringBuilder query = new StringBuilder("SELECT DISTINCT o FROM JobStatisticsVariant as o WHERE o.jobId = :jobId AND o.partId = :partId AND o.transactionId = :transactionId AND o.messageId = :messageId AND o.variantId = :variantId");
				Map<String, Object> queryParams = new HashMap<>();
				queryParams.put("jobId", job.getId());
				queryParams.put("partId", part);
				queryParams.put("transactionId", transaction);
				queryParams.put("messageId", msgid);
				queryParams.put("variantId", varid);
				List<JobStatisticsVariant> jsList = HibernateUtil.getManager().getObjectsOfSpecifiedClassByHQL(JobStatisticsVariant.class, query.toString(), queryParams);
				JobStatisticsVariant oldJs = null;
				if (!jsList.isEmpty()) {
					oldJs = jsList.get(0);
				}

				if (rs != null) {
					if (oldJs != null) {
						oldJs.setTotal(oldJs.getTotal() + rs.count);
						oldJs.setDelivered(oldJs.getDelivered() + rs.played);
						oldJs.setQualified(oldJs.getQualified() + rs.qualified);
						
						HibernateUtil.getManager().saveObject(oldJs, false);
					} else {
						JobStatisticsVariant js = new JobStatisticsVariant();
						js.setMessageId(msgid);
						js.setVariantId(varid);
						js.setQualified(rs.qualified);
						js.setDelivered(rs.played);
						js.setTotal(rs.count);
						js.setJobId(job.getId());
						js.setPartId(part);
						js.setTransactionId(transaction);
						
						HibernateUtil.getManager().saveObject(js);
						// log.error("Id: " + key + " c: " + rs.count + " q: " + rs.qualified + " p:" + rs.played );
					}
				} else {
					log.error("key not found in report statistics set: " + key);
				}
			} catch (Exception e) {
				log.error("saveStats failed!", e);
			}
		}
	}

	private static String limitTo4000( String value )
	{
		if ( value.length() > 4000 )
			value = value.substring(0, 4000);

		while( value.getBytes().length > 4000 )
		{
			value = value.substring(0, value.length() - 1);
		}
		return value;
	}
}
