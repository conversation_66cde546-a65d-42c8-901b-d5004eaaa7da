package com.prinova.messagepoint.platform.services.message;

import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.ZonePart;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.content.ContentObjectSlice;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleResourceAwareServiceExeContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.job.JobPackerDatabaseFileWrapper;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class GetMessageContentService extends AbstractService {
	
	public static final String SERVICE_NAME = "message.GetMessageContentService";
	
	private static final Log log = LogUtil.getLog(GetMessageContentService.class);

	@Override
	public void execute(ServiceExecutionContext context)
	{
		try
		{
			validate(context);
			if (hasValidationError(context))
				return;

			GetMessageContentServiceRequest request = (GetMessageContentServiceRequest) context.getRequest();
			ContentObjectSlice message = request.getMessage();

			if (message != null)
			{
				if ( message.isMultipartType() )
					populateContentMapForMultipart(context, message);
				else
					populateContentMapForStandard(context, message);
			}
		}
		catch (Exception e) 
		{
			log.error(" unexpected exception when invoking GetMessageContentService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException();
		}
	}

	@Override
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}

	private static void populateContentMapForMultipart(ServiceExecutionContext context,
													   ContentObjectSlice message)
	{
		GetMessageContentServiceRequest request = (GetMessageContentServiceRequest) context.getRequest();
		Set<MessagepointLocale> langs = request.getLanguages();
		ParameterGroupTreeNode pgtn = request.getPgTreeNode();

		Map<ZonePart, Set<ContentObjectAssociation>> zonePartContentMap = new HashMap<>();

		Zone currentZone = message.getZone();
		for ( ZonePart currentPart: currentZone.getParts() )
		{
			Set<ContentObjectAssociation> languageContent = new HashSet<>();

			List<ContentObjectAssociation> caList = ContentObjectAssociation.findAllByContentObjectAndParameters( message.getContentObject(), message.getDataType(), pgtn, null, currentPart, false, false, true, false, false );

			for ( ContentObjectAssociation ca : caList )
			{
				if ( ca.getMessagepointLocale() == null || langs.contains(ca.getMessagepointLocale()) )
					languageContent.add(ca);

                if ( request.getDatabaseFileWrapper() != null )
                    request.getDatabaseFileWrapper().add(ca);
			}

			zonePartContentMap.put(currentPart, languageContent);
		}

		((GetMessageContentServiceResponse)context.getResponse()).setZonePartContentMap(zonePartContentMap);
	}
	
	// For non-multipart messages
	private static void populateContentMapForStandard(ServiceExecutionContext context, ContentObjectSlice message)
	{
		GetMessageContentServiceRequest request = (GetMessageContentServiceRequest) context.getRequest();

		Set<ContentObjectAssociation> content = new HashSet<>();
		Set<MessagepointLocale> langs = request.getLanguages();
		ParameterGroupTreeNode pgtn = request.getPgTreeNode();

		List<ContentObjectAssociation> caList = ContentObjectAssociation.findAllByContentObjectAndParameters( message.getContentObject(), message.getDataType(), pgtn, null, null, false, false, true, false, false );
		//LogUtil.getLog(JobPacker.class).error("Fetched " + caList.size() + " ContentObjectAssociation's for model: " +  message.getId() + ", instance type: " + message.getFocusOnDataType() + " PGTN: " + pgtn.getId() );

		for ( ContentObjectAssociation ca : caList )
		{
			if ( ca.getMessagepointLocale() == null || langs.contains( ca.getMessagepointLocale() ) )
				content.add(ca);
		}

		if ( request.getDatabaseFileWrapper() != null )
		{
			if ( content != null )
				request.getDatabaseFileWrapper().addAll(content);
		}
		
		((GetMessageContentServiceResponse)context.getResponse()).setContent(content);
	}
	
	private static ServiceExecutionContext createContext(
			Set<TouchpointLanguage> langs,
			ContentObjectSlice message,
			ParameterGroupTreeNode pgtn,
			JobPackerDatabaseFileWrapper dbfWrapper)
	{
		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		GetMessageContentServiceRequest request = new GetMessageContentServiceRequest();
		context.setRequest(request);

		request.setMessage(message);
		request.setPgTreeNode(pgtn);
		request.setDatabaseFileWrapper(dbfWrapper);
		request.setLanguages(langs);

		GetMessageContentServiceResponse serviceResp = new GetMessageContentServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}
	
	public static ServiceExecutionContext createContextForResolvedContent(Set<TouchpointLanguage> langs, ContentObjectSlice message, JobPackerDatabaseFileWrapper dbfWrapper)
	{
		return createContext(langs, message, null, dbfWrapper);
	}
	
	public static ServiceExecutionContext createContextForResolvedContent(Set<TouchpointLanguage> langs, ContentObjectSlice message, ParameterGroupTreeNode pgtn, JobPackerDatabaseFileWrapper dbfWrapper)
	{
		return createContext(langs, message, pgtn, dbfWrapper);
	}
}
