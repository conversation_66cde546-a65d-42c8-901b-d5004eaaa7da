
package com.prinova.messagepoint.platform.ws.client.messagepoint.api.definitions;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

import com.prinova.messagepoint.platform.ws.client.messagepoint.api.schemas.*;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "api", targetNamespace = "http://messagepoint.com/api/definitions")
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
@XmlSeeAlso({
    ObjectFactory.class
})
public interface Api {


    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.FileResponseType
     */
    @WebMethod(operationName = "JobServerBundle")
    @WebResult(name = "JobServerBundleResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "JobServerBundleResponse")
    public FileResponseType jobServerBundle(
        @WebParam(name = "JobServerBundleRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "JobServerBundleRequest")
        JobServerBundleRequest jobServerBundleRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.SimpleResponseType
     */
    @WebMethod(operationName = "DataFileUpload")
    @WebResult(name = "DataFileUploadResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DataFileUploadResponse")
    public SimpleResponseType dataFileUpload(
        @WebParam(name = "DataFileUploadRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DataFileUploadRequest")
        DataFileUploadRequest dataFileUploadRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.FileResponseType
     */
    @WebMethod(operationName = "DomainReport")
    @WebResult(name = "DomainReportResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DomainReportResponse")
    public FileResponseType domainReport(
        @WebParam(name = "DomainReportRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DomainReportRequest")
        DomainReportRequest domainReportRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectListResponseType
     */
    @WebMethod(operationName = "TestList")
    @WebResult(name = "TestListResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "TestListResponse")
    public ObjectListResponseType testList(
        @WebParam(name = "TestListRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "TestListRequest")
        TestListRequest testListRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectCreateResponseType
     */
    @WebMethod(operationName = "CompositionPackageUpload")
    @WebResult(name = "CompositionPackageUploadResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "CompositionPackageUploadResponse")
    public ObjectCreateResponseType compositionPackageUpload(
        @WebParam(name = "CompositionPackageUploadRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "CompositionPackageUploadRequest")
        CompositionPackageUploadRequest compositionPackageUploadRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.FileResponseType
     */
    @WebMethod(operationName = "FileChunkDownload")
    @WebResult(name = "FileChunkDownloadResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "FileChunkDownloadResponse")
    public FileResponseType fileChunkDownload(
        @WebParam(name = "FileChunkDownloadRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "FileChunkDownloadRequest")
        FileChunkDownloadRequest fileChunkDownloadRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.FileResponseType
     */
    @WebMethod(operationName = "DataAnonymize")
    @WebResult(name = "DataAnonymizeResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DataAnonymizeResponse")
    public FileResponseType dataAnonymize(
        @WebParam(name = "DataAnonymizeRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DataAnonymizeRequest")
        DataAnonymizeRequest dataAnonymizeRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectListResponseType
     */
    @WebMethod(operationName = "TouchpointList")
    @WebResult(name = "TouchpointListResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "TouchpointListResponse")
    public ObjectListResponseType touchpointList(
        @WebParam(name = "TouchpointListRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "TouchpointListRequest")
        TouchpointListRequest touchpointListRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.SimpleResponseType
     */
    @WebMethod(operationName = "ImportJobStats")
    @WebResult(name = "ImportJobStatsResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "ImportJobStatsResponse")
    public SimpleResponseType importJobStats(
        @WebParam(name = "ImportJobStatsRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "ImportJobStatsRequest")
        ImportJobStatsRequest importJobStatsRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectListResponseType
     */
    @WebMethod(operationName = "NodeList")
    @WebResult(name = "NodeListResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "NodeListResponse")
    public ObjectListResponseType nodeList(
        @WebParam(name = "NodeListRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "NodeListRequest")
        ClientIdentificationType nodeListRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectListResponseType
     */
    @WebMethod(operationName = "DataFileList")
    @WebResult(name = "DataFileListResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DataFileListResponse")
    public ObjectListResponseType dataFileList(
        @WebParam(name = "DataFileListRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DataFileListRequest")
        ClientIdentificationType dataFileListRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.FileResponseType
     */
    @WebMethod(operationName = "AuditReport")
    @WebResult(name = "AuditReportResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "AuditReportResponse")
    public FileResponseType auditReport(
        @WebParam(name = "AuditReportRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "AuditReportRequest")
        AuditReportRequest auditReportRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectListResponseType
     */
    @WebMethod(operationName = "TouchpointCollectionList")
    @WebResult(name = "TouchpointCollectionListResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "TouchpointCollectionListResponse")
    public ObjectListResponseType touchpointCollectionList(
        @WebParam(name = "TouchpointCollectionListRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "TouchpointCollectionListRequest")
        TouchpointCollectionListRequest touchpointCollectionListRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.SimpleResponseType
     */
    @WebMethod(operationName = "DeactivateUser")
    @WebResult(name = "DeactivateUserResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DeactivateUserResponse")
    public SimpleResponseType deactivateUser(
        @WebParam(name = "DeactivateUserRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DeactivateUserRequest")
        DeactivateUserRequest deactivateUserRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectListResponseType
     */
    @WebMethod(operationName = "MessageList")
    @WebResult(name = "MessageListResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "MessageListResponse")
    public ObjectListResponseType messageList(
        @WebParam(name = "MessageListRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "MessageListRequest")
        ClientIdentificationType messageListRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.SimpleResponseType
     */
    @WebMethod(operationName = "NodeToNodeCopy")
    @WebResult(name = "NodeToNodeCopyResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "NodeToNodeCopyResponse")
    public SimpleResponseType nodeToNodeCopy(
        @WebParam(name = "NodeToNodeCopyRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "NodeToNodeCopyRequest")
        NodeToNodeCopyRequest nodeToNodeCopyRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.FileListType
     */
    @WebMethod(operationName = "FileList")
    @WebResult(name = "FileListResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "FileListResponse")
    public FileListType fileList(
        @WebParam(name = "FileListRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "FileListRequest")
        ClientIdentificationType fileListRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectListResponseType
     */
    @WebMethod(operationName = "ContentSearch")
    @WebResult(name = "ContentSearchResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "ContentSearchResponse")
    public ObjectListResponseType contentSearch(
        @WebParam(name = "ContentSearchRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "ContentSearchRequest")
        ContentSearchRequest contentSearchRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.SimpleResponseType
     */
    @WebMethod(operationName = "CompositionPackageUpdate")
    @WebResult(name = "CompositionPackageUpdateResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "CompositionPackageUpdateResponse")
    public SimpleResponseType compositionPackageUpdate(
        @WebParam(name = "CompositionPackageUpdateRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "CompositionPackageUpdateRequest")
        CompositionPackageUpdateRequest compositionPackageUpdateRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.FileResponseType
     */
    @WebMethod(operationName = "JobClientBundle")
    @WebResult(name = "JobClientBundleResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "JobClientBundleResponse")
    public FileResponseType jobClientBundle(
        @WebParam(name = "JobClientBundleRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "JobClientBundleRequest")
        JobClientBundleRequest jobClientBundleRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.FileResponseType
     */
    @WebMethod(operationName = "UnifiedLoginReport")
    @WebResult(name = "UnifiedLoginReportResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "UnifiedLoginReportResponse")
    public FileResponseType unifiedLoginReport(
        @WebParam(name = "UnifiedLoginReportRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "UnifiedLoginReportRequest")
        UnifiedLoginReportRequest unifiedLoginReportRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectListResponseType
     */
    @WebMethod(operationName = "DeliveryReport")
    @WebResult(name = "DeliveryReportResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DeliveryReportResponse")
    public ObjectListResponseType deliveryReport(
        @WebParam(name = "DeliveryReportRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DeliveryReportRequest")
        DeliveryReportRequest deliveryReportRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.FileResponseType
     */
    @WebMethod(operationName = "UsersReport")
    @WebResult(name = "UsersReportResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "UsersReportResponse")
    public FileResponseType usersReport(
        @WebParam(name = "UsersReportRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "UsersReportRequest")
        UsersReportRequest usersReportRequest);

    /**
     *
     * @return
     *     returns com.messagepoint.api.schemas.FileResponseType
     */
    @WebMethod(operationName = "UsersTemplate")
    @WebResult(name = "UsersTemplateResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "UsersTemplateResponse")
    public FileResponseType usersTemplate(
            @WebParam(name = "UsersTemplateRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "UsersTemplateRequest")
                    UsersTemplateRequest usersTemplateRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectCreateResponseType
     */
    @WebMethod(operationName = "TouchpointDeliveryEvent")
    @WebResult(name = "TouchpointDeliveryEventResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "TouchpointDeliveryEventResponse")
    public ObjectCreateResponseType touchpointDeliveryEvent(
        @WebParam(name = "TouchpointDeliveryEventRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "TouchpointDeliveryEventRequest")
        TouchpointDeliveryEventRequest touchpointDeliveryEventRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectListResponseType
     */
    @WebMethod(operationName = "CompositionPackageList")
    @WebResult(name = "CompositionPackageListResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "CompositionPackageListResponse")
    public ObjectListResponseType compositionPackageList(
        @WebParam(name = "CompositionPackageListRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "CompositionPackageListRequest")
        ClientIdentificationType compositionPackageListRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.FileResponseType
     */
    @WebMethod(operationName = "Diagnostic")
    @WebResult(name = "DiagnosticResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DiagnosticResponse")
    public FileResponseType diagnostic(
        @WebParam(name = "DiagnosticRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DiagnosticRequest")
        ClientIdentificationType diagnosticRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectListResponseType
     */
    @WebMethod(operationName = "TouchpointProductionJobList")
    @WebResult(name = "TouchpointProductionJobListResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "TouchpointProductionJobListResponse")
    public ObjectListResponseType touchpointProductionJobList(
        @WebParam(name = "TouchpointProductionJobListRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "TouchpointProductionJobListRequest")
        TouchpointProductionJobListRequest touchpointProductionJobListRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectListResponseType
     */
    @WebMethod(operationName = "MessageContentQuery")
    @WebResult(name = "MessageContentQueryResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "MessageContentQueryResponse")
    public ObjectListResponseType messageContentQuery(
        @WebParam(name = "MessageContentQueryRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "MessageContentQueryRequest")
        MessageContentQueryRequest messageContentQueryRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.SimpleResponseType
     */
    @WebMethod(operationName = "FileChunkUpload")
    @WebResult(name = "FileChunkUploadResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "FileChunkUploadResponse")
    public SimpleResponseType fileChunkUpload(
        @WebParam(name = "FileChunkUploadRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "FileChunkUploadRequest")
        FileChunkUploadRequest fileChunkUploadRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.ObjectCreateResponseType
     */
    @WebMethod(operationName = "RerunTest")
    @WebResult(name = "RerunTestResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "RerunTestResponse")
    public ObjectCreateResponseType rerunTest(
        @WebParam(name = "RerunTestRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "RerunTestRequest")
        RerunTestRequest rerunTestRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.SimpleResponseType
     */
    @WebMethod(operationName = "CreateSandboxInstance")
    @WebResult(name = "CreateSandboxInstanceResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "CreateSandboxInstanceResponse")
    public SimpleResponseType createSandboxInstance(
        @WebParam(name = "CreateSandboxInstanceRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "CreateSandboxInstanceRequest")
        CreateSandboxInstanceRequest createSandboxInstanceRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.SimpleResponseType
     */
    @WebMethod(operationName = "ImportTransactionStats")
    @WebResult(name = "ImportTransactionStatsResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "ImportTransactionStatsResponse")
    public SimpleResponseType importTransactionStats(
        @WebParam(name = "ImportTransactionStatsRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "ImportTransactionStatsRequest")
        ImportTransactionStatsRequest importTransactionStatsRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.BackgroundImageDownloadResponse
     */
    @WebMethod(operationName = "BackgroundImageDownload")
    @WebResult(name = "BackgroundImageDownloadResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "BackgroundImageDownloadResponse")
    public BackgroundImageDownloadResponse backgroundImageDownload(
        @WebParam(name = "BackgroundImageDownloadRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "BackgroundImageDownloadRequest")
        BackgroundImageDownloadRequest backgroundImageDownloadRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.SimpleResponseType
     */
    @WebMethod(operationName = "DeleteFile")
    @WebResult(name = "DeleteFileResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DeleteFileResponse")
    public SimpleResponseType deleteFile(
        @WebParam(name = "DeleteFileRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "DeleteFileRequest")
        DeleteFileRequest deleteFileRequest);

    /**
     * 
     * @return
     *     returns com.messagepoint.api.schemas.FileResponseType
     */
    @WebMethod(operationName = "JobDiagnosticBundle")
    @WebResult(name = "JobDiagnosticBundleResponse", targetNamespace = "http://messagepoint.com/api/schemas", partName = "JobDiagnosticBundleResponse")
    public FileResponseType jobDiagnosticBundle(
        @WebParam(name = "JobDiagnosticBundleRequest", targetNamespace = "http://messagepoint.com/api/schemas", partName = "JobDiagnosticBundleRequest")
        JobDiagnosticBundleRequest jobDiagnosticBundleRequest);

}
