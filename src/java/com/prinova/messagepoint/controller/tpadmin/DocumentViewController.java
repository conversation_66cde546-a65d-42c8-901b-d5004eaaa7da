package com.prinova.messagepoint.controller.tpadmin;

import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.CloneEvents;
import com.prinova.messagepoint.analytics.types.TouchpointAdminEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.email.EmailTemplateUtils;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.attachment.Attachment;
import com.prinova.messagepoint.model.audit.*;
import com.prinova.messagepoint.model.common.DocumentOperationType;
import com.prinova.messagepoint.model.common.SyncObjectType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.email.TemplateModifier;
import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.model.insert.WeightUnit;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.testing.TestScenario;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.attachment.DeleteAttachmentService;
import com.prinova.messagepoint.platform.services.backgroundtask.CloneTouchpointBackgroundTask;
import com.prinova.messagepoint.platform.services.backgroundtask.ExportMessagepointObjectBackgroundTask;
import com.prinova.messagepoint.platform.services.export.GenerateMessagepointObjectExportService;
import com.prinova.messagepoint.platform.services.languageselection.CreateOrUpdateLanguageSelectionService;
import com.prinova.messagepoint.platform.services.tpadmin.*;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.tag.layout.ContextBarTag;
import com.prinova.messagepoint.util.*;
import org.apache.commons.logging.Log;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;


public class DocumentViewController extends MessagepointController {

	private static final Log log = LogUtil.getLog(DocumentViewController.class);
	
	public static final String REQUEST_PARM_ATTACHMENT_ID 			= "attachmentId";
	public static final String REQUEST_PARM_DOCUMENTID 				= "docid";
	public static final String REQUEST_PARM_ACTION 					= "action";
	public static final String REQUEST_PARM_ADD_SECTIONS 			= "addSections";
	public static final String REQUEST_PARM_INSERT_INDEX 			= "insertIndex";
	public static final String REQUEST_PARM_SECTION_ID 				= "sectionId";
	public static final String REQUEST_PARM_TOUCHPOINT_TARGETING_ID = "touchpointTargetingId";
	public static final String REQUEST_PARM_IFRAMETARGET			= "iframetarget";

	public static final int ACTION_ADD_ZONE 			= 1;
	public static final int ACTION_ADD_SECITON 			= 2;
	public static final int ACTION_REMOVE_SECITON 		= 3;
	
	public static final int ACTION_EXPORT_TOUCHPOINT 	= 6;
	public static final int ACTION_DELETE_ATTACHMENT 	= 7;
	public static final int ACTION_ADD_TOUCHPOINT		= 8;
	public static final int ACTION_DUPLICATE_TOUCHPOINT	= 9;
	public static final int ACTION_ADD_CHANNEL			= 10;  // RESERVED
	public static final int ACTION_REMOVE_TOUCHPOINT	= 11;

	public static final int ACTION_NEW_LANGUAGE 			= 12;
	public static final int ACTION_UPDATE_LANGUAGE 			= 13;
	public static final int ACTION_DELETE_LANGUAGE 			= 14;
	public static final int ACTION_MAKE_DEFAULT_LANGUAGE	= 18;
	
	public static final int ACTION_DELETE_COMP_PACKS		= 21;
	
	public static final int ACTION_REFRESH_SEG_ANALY		= 22;
	
	public static final int ACTION_ACCESS_CONTROL			= 23; // RESERVED
	
	public static final int ACTION_CUSTOMIZE_LOCALE			= 24; // RESERVED
	public static final int ACTION_RESTORE_LOCALE			= 25;
	public static final int ACTION_LANGUAGE_REPAIR			= 26;

	private static final int ACTION_MAKE_DEFAULT_COMPOSITION_PACKAGE = 27;

    private static final int ACTION_EXPORT_CONTENT_TO_JSON           = 28;
    private static final int ACTION_EXPORT_TOUCHPOINT_EXPORT_XSD     = 29;

	private static final int ACTION_ATTACHMENT_MANAGEMENT_DELETE     = 31;

	public static final int INSERT_TYPE_END_OF_DOC 					= 1;
	public static final int INSERT_TYPE_AFTER_CURRENT_SECTION 		= 2;
	public static final int INSERT_TYPE_BEFORE_CURRENT_SECTION 		= 3;
	public static final int INSERT_TYPE_START_OF_DOC 				= 4;
	
	public static final int IFRAME_TARGET_EXTENDED_REPORTING_DATA 	= 1;

	private String formViewParentRedirect;
	private String editLanguageView;

	protected Map<String, Object> referenceData(HttpServletRequest request) {
		// Data appears to be coming from handleRequest - Is this redundant?
		Map<String, Object> referenceData = new HashMap<>();
		
		int iFrameTarget = ServletRequestUtils.getIntParameter(request, REQUEST_PARM_IFRAMETARGET, -1);
		
		long documentId = getDocumentId(request);
		Document document = Document.findById(documentId);
		if ( document == null )
			return referenceData;
		referenceData.put("document", document);
		
		// Channel Context
		Document currentChannelDocument =  UserUtil.getCurrentChannelDocumentContext(document);
		referenceData.put("currentChannelDocument", currentChannelDocument);
		referenceData.put("isFilePackage", currentChannelDocument.isTemplateControlled() && (currentChannelDocument.isSefasCompositionTouchpoint() || currentChannelDocument.isMPHCSCompositionTouchpoint()));

		referenceData.put("noTouchpoints", getFirstTouchpoint() == null);
	    	referenceData.put("channels", Channel.getAllChannels());
	    	referenceData.put("connectors", Connector.getAllConnectors());
	    	referenceData.put("locales", MessagepointLocale.getAllLanguagesWithSystemDefaultFirst());
	    	referenceData.put("locales", MessagepointLocale.getAllLocales());
	    	
		referenceData.put("isExchangeNode", Node.getCurrentNode().isExchangeNode());
		
		referenceData.put("currentInstance", Node.getCurrentNode());	
		
		String userGuid = UserUtil.getPrincipalUser().getGuid();
		List<Node> domainInstances = new ArrayList<>();
		for (Node node : Node.getCurrentBranch().getAllAccessibleNodes(false))
		{
			if(node.isOnline() && node.isEnabled()) {
				User user = User.findByGuid(userGuid, node);
				if (user != null && user.isEnabled())
				{
					domainInstances.add(node);
				}
			}
		}
		
		referenceData.put("domainInstances", domainInstances);		

        Map<String, List<Node>> subDomains = new HashMap<>();

        if(Node.getCurrentBranch().getParentBranch() != null) {
            Branch parentBranch = Branch.findById(Node.getCurrentBranch().getParentBranch().getId());
            List<Node> allNodesInBranch = parentBranch.getAllNodes(false)
                .stream()
                .filter(n->n.isEnabled() && n.isOnline() && (n.isProductionNode() || n.isTestingNode() || n.isSandboxNode()))
                .collect(Collectors.toList());
            List<Node> nodesCanCloneTo = new ArrayList<>();
            for(Node node : allNodesInBranch) {
                CloneHelper.execInSchema(node.getSchemaName(), ()->{
                    boolean canSyncUpdateFromChild = MessagepointLicenceManager.getInstance().isLicencedForSyncUpdateFromChild();
                    if(canSyncUpdateFromChild) {
                        nodesCanCloneTo.add(node);
                    }
                });
            }
            if(! nodesCanCloneTo.isEmpty()) {
                String branchName = parentBranch.getName();
                subDomains.put(branchName, nodesCanCloneTo);
            }
        }

        List<Branch> subBranches = Node.getCurrentBranch().getAllEnabledChildren();
        for(Branch subBranch : subBranches) {
            String branchName = subBranch.getName();
            List<Node> nodesCanCloneTo = new ArrayList<>();

            List<Node> allNodesInBranch = subBranch.getAllNodes(false)
                .stream()
                .filter(n->n.isEnabled() && n.isOnline() && (n.isProductionNode() || n.isTestingNode() || n.isSandboxNode()))
                .collect(Collectors.toList());

            for(Node node : allNodesInBranch) {
                SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(node.getSchemaName());
                try {
                    boolean canSyncUpdateFromParent = MessagepointLicenceManager.getInstance().isLicencedForSyncUpdateFromParent();
                    if (canSyncUpdateFromParent) {
                        User user = User.findByGuid(userGuid, node);
                        if (user != null && user.isEnabled())
                        {
                            nodesCanCloneTo.add(node);
                        }
                    }
                } finally {
                    HibernateUtil.getManager().restoreSession(mainSessionHolder);
                }
            }

            if(! nodesCanCloneTo.isEmpty()) {
                subDomains.put(branchName, nodesCanCloneTo);
            }
        }

        referenceData.put("subDomains", subDomains);

		if (currentChannelDocument != null && currentChannelDocument.getDocumentSections() != null)
			referenceData.put("numberOfSections", currentChannelDocument.getDocumentSections().size());
		else
			referenceData.put("numberOfSections", 1);

		boolean hasConnectedDataResource = false;
		if (document != null && document.isConnectedEnabled()) {
			if(document.getCommunicationsDataResource() != null) {
				hasConnectedDataResource = true;
			}
		}
		referenceData.put("hasConnectedDataResource", hasConnectedDataResource);
		
		boolean hasSegmentationAnalysisDataResource = false;
		if (document != null && document.isSegmentationAnalysisEnabled()) {
			if(document.getSegmentationAnalysisResource() != null) {
				hasSegmentationAnalysisDataResource = true;
			}
		}
		referenceData.put("hasSegmentationAnalysisDataResource", hasSegmentationAnalysisDataResource);

		boolean hasDataFilesOrDataResources = false;
		if(document != null) {
			if((document.getDataFiles() != null && !document.getDataFiles().isEmpty()) || (document.getDataResources() != null && ! document.getDataResources().isEmpty())) {
				hasDataFilesOrDataResources = true;
			}
		}
		referenceData.put("hasDataFilesOrDataResources", hasDataFilesOrDataResources);
		
		// TP access control
		boolean tpAccessControlable = (Node.getCurrentNode().getNodeType() == Node.NODE_TYPE_EXCHANGE && (document.getOriginObject() == null 
				|| (document.getOriginObject() != null && document.getCheckoutTimestamp() == null)));
		referenceData.put("tpAccessControlable", tpAccessControlable);
		
		if (iFrameTarget != IFRAME_TARGET_EXTENDED_REPORTING_DATA)
		{
			
			if (document != null) {
				referenceData.put("isVariantTouchpoint", document.isEnabledForVariation()); 
				referenceData.put("activeMasterTemplateModifiers", TemplateModifier.findAllMasterActiveModifiersByTouchpoint(currentChannelDocument));

				if(document.isZoneDataGroupMissing())
					referenceData.put("documentZoneDataGroupMissing", ApplicationUtil.getMessage("error.message.touchpoint.zone.data.group.missing"));
				
				/**
				 * Disabled automatic variable reference check, because of ADS and ESI performance issues 
				 * 
				if(document.isSomeReferencedVariableNotBridged())
				{
					referenceData.put("notBridgedVariableIdList", "RUN_HEALTH_CHECK");

					String dataSourceIds = "";
					for(DataSource dataSource : document.getAllDataSources()){
						if (dataSource != null)
						{
							if(dataSourceIds != ""){
								dataSourceIds += "_";
							}
							dataSourceIds = dataSourceIds.concat(String.valueOf(dataSource.getId()));
						}
					}
					
					referenceData.put("targetDataSources", dataSourceIds);
				}
				**/
				
				boolean hasInsert = false;
				if (document.getInsertParameterGroup() != null) {
					hasInsert = true;
					referenceData.put("insertSelectorName", document.getInsertParameterGroup().getName());
					referenceData.put("firstPageWeight", document.getFirstPageDehydratedWeight());
					referenceData.put("otherPagesWeight", document.getOtherPagesDehydratedWeight());
					if (document.getDefaultRateScheduleCollection() != null)
						referenceData.put("defaultRateScheduleName", document.getDefaultRateScheduleCollection().getRateScheduleForDate(DateUtil.now()).getName());
					if (document.getDefaultInsertSchedule() != null)
						referenceData.put("defaultInsertScheduleName", document.getDefaultInsertSchedule().getName());
					int defaultWeightUnitId = Integer.valueOf(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Insert.KEY_WeightUnits)).intValue();
					WeightUnit defaultWeightUnit = new WeightUnit(defaultWeightUnitId);
					referenceData.put("defaultWeightUnit", defaultWeightUnit);
				}
				referenceData.put("hasInsert", hasInsert);
				
				boolean hasAttachment = currentChannelDocument.isEnabledForAttachments() && !currentChannelDocument.getAttachments().isEmpty();
				referenceData.put("hasAttachment", hasAttachment);
				if (hasAttachment)
					referenceData.put("attachment", currentChannelDocument.getAttachments().iterator().next());
		
				if (document.getSelectionParameterGroup() != null) {
					referenceData.put("contentSelectorName", document.getSelectionParameterGroup().getName());
				}
				referenceData.put("isVariantTouchpoint", document.isEnabledForVariation());
				
				// Language Management
				referenceData.put("isLanguageManagementEnabled", document.isEnabledForLanguageSelection());
				if (document.isEnabledForLanguageSelection()) {
					referenceData.put("languageSelectorName", document.getLanguageParameterGroup().getName());
				}
				
				// Add flag for insert management visibility: Determined by licence
				boolean licencedForInsertManagement = MessagepointLicenceManager.getInstance().isLicencedForInsertManagement();
				referenceData.put("licencedForInsertManagement", licencedForInsertManagement);
		
				if (currentChannelDocument.isEmailTouchpoint() || currentChannelDocument.isWebTouchpoint()) {
					referenceData.put("totalNumberOfTemplateModifiers", TemplateModifier.findAllMasterActiveTemplateManagedByTouchpoint(currentChannelDocument).size());
					boolean templatePackageExists = EmailTemplateUtils.getTemplatePackageExists(currentChannelDocument.getId());
					referenceData.put("templatePackageExists", templatePackageExists);
					if (templatePackageExists)
						referenceData.put("templatePackagePath", EmailTemplateUtils.getTemplatePackagePath(currentChannelDocument.getId()));

					// Stripo template
					boolean parsedTemplateExists = EmailTemplateUtils.getParsedTemplateExists(currentChannelDocument.getId());
					referenceData.put("stripoTemplateExists", parsedTemplateExists);
				}
				
				// TODO: Targeting per channel
				if (currentChannelDocument != null) {
					TouchpointTargeting touchpointTargeting = TouchpointTargeting.findByDocument(currentChannelDocument);
					if (touchpointTargeting != null)
						referenceData.put("touchpointTargeting", touchpointTargeting);
				}
				
				referenceData.put("hasExtReportingData", !document.getExtReportingDataVariables().isEmpty());
				
				// For "Delete Touchpoint" action
				String deleteTpMsgPartOne = "";
				if(document.getParent() != null){
					deleteTpMsgPartOne = ApplicationUtil.getMessage("page.text.delete.touchpoint.part.one.b", new String[]{document.getName(), document.getParent().getName()});
				}else{
					deleteTpMsgPartOne = ApplicationUtil.getMessage("page.text.delete.touchpoint.part.one.a", new String[]{document.getName()});
				}
				referenceData.put("deleteTpMsgPartOne", deleteTpMsgPartOne);
				
				long numMessagesToDelete = ContentObject.findMessagesByDocument(document, false)
																			.stream()
																			.filter(m->!m.getIsTouchpointLocal())
																			.count();

				List<TouchpointSelection> documentList = TouchpointSelection.findAllByDocument(document);
				long numVariantsToDelete = documentList != null ? documentList.size() : 0;

				List<TestScenario>  testScenarioList = TestScenario.findAllByDocument(document);
				long numTestsToDelete = testScenarioList != null ? testScenarioList.size() : 0;
				
				referenceData.put("xMessagesToDeleteStr", ApplicationUtil.getMessage("page.label.confirm.assets.to.be.removed", new String[]{String.valueOf(numMessagesToDelete), ApplicationUtil.getMessage("page.label.messages").toLowerCase()}));
				referenceData.put("xVariantsToDeleteStr", ApplicationUtil.getMessage("page.label.confirm.assets.to.be.removed", new String[]{String.valueOf(numVariantsToDelete), ApplicationUtil.getMessage("page.label.variants").toLowerCase()}));
				referenceData.put("xTestsToDeleteStr", ApplicationUtil.getMessage("page.label.confirm.assets.to.be.removed", new String[]{String.valueOf(numTestsToDelete), ApplicationUtil.getMessage("page.label.tests").toLowerCase()}));
			}
			
	    	referenceData.put("defaultCompositionPackageId", currentChannelDocument != null && currentChannelDocument.getCompositionFileSet() != null ? currentChannelDocument.getCompositionFileSet().getId() : -1);

			// CONNECTED DATA
			boolean licencedForMessagepointInteractive = MessagepointLicenceManager.getInstance().isLicencedForMessagepointInteractive();
			referenceData.put("licencedForMessagepointInteractive", licencedForMessagepointInteractive);
	    	
			boolean licencedForVariantManagement = MessagepointLicenceManager.getInstance().isLicencedForVariantManagement();
			referenceData.put("licencedForVariantManagement", licencedForVariantManagement);
			
			// SEGMENTATION ANALYSIS
			referenceData.put("licencedForSegmentationAnalysis", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ID_LICENCED_SIMULATION_VIEW));
			
			boolean canAddChannel = document != null && document.getChannelAlternateDocuments().size() < 2 && document.isPrintTouchpoint();
			referenceData.put("canAddChannel", canAddChannel);		
		}
		else
		{
			referenceData.put("hasExtReportingData", !document.getExtReportingDataVariables().isEmpty());
		}

        referenceData.put("touchpointLocalesForSync", document.getTouchpointLanguagesAsLocales());

		DocumentHistory documentHistory = document.getHistoryRecords().stream().findFirst().orElse(null);
        referenceData.put("documentHistory", documentHistory);
		referenceData.put("hasDefaultTouchpointLanguage", document.hasDefaultTouchpointLanguage());

        referenceData.put("showContentJSONExport", FeatureFlag.isEnabled(FeatureFlag.Features.ContentJsonExport, request));
        List<MessagepointLocale> locales;
        if (document != null) {
            locales = document.getTouchpointLanguagesAsLocales();
			if(!locales.isEmpty()) {
				locales.remove(0); // The first locale is always the default locale
			}
        }
        else {
            locales = MessagepointLocale.getFavouriteLanguages();
        }
        referenceData.put("exportTargetLocales", locales);

        List<Integer> dontOverrideObjectTypeIds = SyncObjectType.getOverrideTogglableSharedObjectsDuringTpClone();
        referenceData.put("dontOverrideObjectTypeIds", dontOverrideObjectTypeIds);

        String revision = MessagePointStartUp.getBuildRevision();
        revision = revision.length() <= 19 ? revision : revision.substring(0, 19);
        referenceData.put("revision", revision);

        List<SyncObjectType> cloneOptions = SyncObjectType.getCloneOptions();

        referenceData.put("cloneOptions", cloneOptions);

        return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(Node.class, new IdCustomEditor<>(Node.class));
        binder.registerCustomEditor(MessagepointLocale.class, new IdCustomEditor<>(MessagepointLocale.class));
	}

	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long documentId = getDocumentId(request);
		Document document = Document.findById(documentId);
        return new DocumentViewWrapper(document);
	}

	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<TouchpointAdminEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointAdminEvents.DocumentView);

		try {
			DocumentViewWrapper command = (DocumentViewWrapper)commandObj;

			int actionId = ServletRequestUtils.getIntParameter(request, REQUEST_PARM_ACTION, -1);
			long docId = getDocumentId(request);

			switch (actionId) {
				case (ACTION_DELETE_COMP_PACKS) : {
					analyticsEvent.setAction(Actions.CompositionPackage.Delete);
					// For Audit
					StringBuilder deletedNames = new StringBuilder();
					for(CompositionFileSet fileSet:command.getSelectedCompositionFiles()){
						if(!deletedNames.isEmpty())
							deletedNames.append(", ");
						deletedNames.append(fileSet.getName());
					}
					ServiceExecutionContext context = CleanupCompositionPacksService.createContext(command.getSelectedCompositionFiles());
					Service cleanupCompPacksService = MessagepointServiceFactory.getInstance().lookupService(CleanupCompositionPacksService.SERVICE_NAME, CleanupCompositionPacksService.class);

					cleanupCompPacksService.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return super.showForm(request, response, errors);
					} else {
						Document document = Document.findById(docId);
						if(document != null && !document.isTpContentChanged()){
							document.setTpContentChanged(true);
							document.save();
						}
						// Audit (Touchpoint Composition package(s) deleted)
						AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, document.getName(), document.getId(), AuditActionType.ID_CHANGES,
								AuditMetadataBuilder.forSimpleAuditMessage(
										ApplicationUtil.getMessage("page.label.composition.packages.deleted") + ": " + deletedNames, null, null));
						for(CompositionFileSet fileSet:command.getSelectedCompositionFiles()){
							if(!deletedNames.toString().isEmpty()){
								AuditEventUtil.push(AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_COMPOSITION_PKG, fileSet.getName(), fileSet.getId(), AuditActionType.ID_CHANGE_DELETED, null);
							}
						}
						Map<String, Object> parms = new HashMap<>();
						parms.put(REQUEST_PARM_DOCUMENTID, docId);
						return new ModelAndView(new RedirectView(getSuccessView()), parms);
					}
				}
                case (ACTION_EXPORT_TOUCHPOINT) : {
                    analyticsEvent.setAction(Actions.Export);

                    String exportId		= command.getExportId();

                    ServiceExecutionContext context = GenerateMessagepointObjectExportService.createContext(exportId, docId, command.getExportOptions(), Document.class, UserUtil.getPrincipalUser());
                    Service exportTouchpointService = MessagepointServiceFactory.getInstance().lookupService(GenerateMessagepointObjectExportService.SERVICE_NAME, GenerateMessagepointObjectExportService.class);

                    exportTouchpointService.execute(context);
                    ServiceResponse serviceResponse = context.getResponse();
                    if (!serviceResponse.isSuccessful()) {
                        ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                        return super.showForm(request, response, errors);
                    } else {
                        // Audit (Touchpoint exported)
                        Document document = Document.findById(docId);
                        AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, document.getName(), document.getId(), AuditActionType.ID_CHANGES,
                            AuditMetadataBuilder.forSimpleAuditMessage(
                                ApplicationUtil.getMessage("page.label.touchpoint.export.requested"), null, null));
                        Map<String, Object> parms = new HashMap<>();
                        parms.put(REQUEST_PARM_DOCUMENTID, docId);
                        return new ModelAndView(new RedirectView(getSuccessView()), parms);
                    }
                }
                case (ACTION_EXPORT_CONTENT_TO_JSON) : {
                    analyticsEvent.setAction(Actions.Export);

//                    String exportId		= command.getExportId();

//                    ServiceExecutionContext context = GenerateMessagepointObjectExportService.createContext(exportId, docId, command.getExportOptions(), ContentObject.class, UserUtil.getPrincipalUser(), );
                    Document document = Document.findById(docId);
                    String targetLocaleCode = "es_us";
                    if (command.getExportTargetLocale() != null)
                        targetLocaleCode = command.getExportTargetLocale().getCode();

                    boolean exportContentsPerContentObjectToOneJSONFile = true;
                    boolean tryActiveDataIfNoWorkingData = command.isTryActiveDataIfNoWorkingData();

                    ServiceExecutionContext context = GenerateMessagepointObjectExportService.createContext(command.getExportId(),
                        document,
                        exportContentsPerContentObjectToOneJSONFile,
                        tryActiveDataIfNoWorkingData,
                        Document.class,
                        UserUtil.getPrincipalUser(), ExportMessagepointObjectBackgroundTask.EXPORT_JSON_FOR_TRANSLATION_ZIP, targetLocaleCode);
                    Service exportTouchpointService = MessagepointServiceFactory.getInstance().lookupService(GenerateMessagepointObjectExportService.SERVICE_NAME, GenerateMessagepointObjectExportService.class);

                    exportTouchpointService.execute(context);
                    ServiceResponse serviceResponse = context.getResponse();
                    if (!serviceResponse.isSuccessful()) {
                        ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                        return super.showForm(request, response, errors);
                    } else {
                        // Audit (Touchpoint exported)
                        AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, document.getName(), document.getId(), AuditActionType.ID_CHANGES,
                            AuditMetadataBuilder.forSimpleAuditMessage(
                                ApplicationUtil.getMessage("page.label.touchpoint.content.export.to.json.requested"), null, null));
                        Map<String, Object> parms = new HashMap<>();
                        parms.put(REQUEST_PARM_DOCUMENTID, docId);
                        return new ModelAndView(new RedirectView(getSuccessView()), parms);
                    }
                }
                case (ACTION_EXPORT_TOUCHPOINT_EXPORT_XSD) : {
                    analyticsEvent.setAction(Actions.Export);
                    String revision = MessagePointStartUp.getBuildRevision();
                    revision = revision.length() <= 19 ? revision : revision.substring(0, 19);
                    Map<String, Object> parms = new HashMap<>();
                    parms.put(REQUEST_PARM_DOCUMENTID, docId);
                    return new ModelAndView(new RedirectView(getSuccessView()), parms);
                }
				case (ACTION_REMOVE_TOUCHPOINT) : {
					analyticsEvent.setAction(Actions.Remove);

					Document document = Document.findById(docId);
					String name = document.getName();
					DeleteTouchpointBackgroundTask task = new DeleteTouchpointBackgroundTask(docId, UserUtil.getPrincipalUser());
					MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

					// Audit: Touchpoint Deletion
					AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, name, docId, AuditActionType.ID_DELETE,
							AuditMetadataBuilder.forSimpleAuditMessage(name!=null?name:"" + " " +
									ApplicationUtil.getMessage("page.label.touchpoint.deleted"), null, null));

					Map<String, Object> parms = new HashMap<>();
					Document nextTp = getFirstTouchpoint();
					long nextTpId = nextTp != null ? nextTp.getId() : -1;
					parms.put(REQUEST_PARM_DOCUMENTID, nextTpId);

					return new ModelAndView(new RedirectView(getSuccessView()), parms);
				}
				case (ACTION_ATTACHMENT_MANAGEMENT_DELETE) : {
					analyticsEvent.setAction(Actions.DeleteAttachment);
					Document document = Document.findById(docId);
					List<Attachment> selectedAttachments = command.getSelectedAttachments();
					List<Long> attachmentIds = new ArrayList<>();
					if (!selectedAttachments.isEmpty()) {
						for (Attachment attachment: selectedAttachments){
							attachmentIds.add(attachment.getId());
						}
					}

					ServiceExecutionContext context = DeleteAttachmentService.createContext(attachmentIds);
					Service deleteAttachmentService = MessagepointServiceFactory.getInstance().lookupService(DeleteAttachmentService.SERVICE_NAME, DeleteAttachmentService.class);

					deleteAttachmentService.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return super.showForm(request, response, errors);
					} else {
						if(document != null && !document.isTpContentChanged()){
							document.setTpContentChanged(true);
							document.save();
						}
						// Audit (Touchpoint attachment deleted)
						AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, document.getName(), document.getId(), AuditActionType.ID_CHANGES,
								AuditMetadataBuilder.forSimpleAuditMessage(
										ApplicationUtil.getMessage("page.label.attachment.deleted"), null, null));
						Map<String, Object> parms = new HashMap<>();
						parms.put(REQUEST_PARM_DOCUMENTID, docId);
						return new ModelAndView(new RedirectView(getSuccessView()), parms);
					}
				}
				case (ACTION_ADD_TOUCHPOINT): {
					analyticsEvent.setAction(Actions.Add);

					long connectorId = command.getConnectorId();
					long channelId = command.getChannelId();
					long defaultLocaleId = command.getDefaultLocaleId();
					String touchpointName = command.getDocumentName();

					ServiceExecutionContext context = CreateDocumentService.createContextForNew(connectorId, channelId, touchpointName, null, MessagepointLocale.getLanguageLocaleByLocaleId(defaultLocaleId));
					Service createDocumentService = MessagepointServiceFactory.getInstance().lookupService(CreateDocumentService.SERVICE_NAME, CreateDocumentService.class);
					createDocumentService.execute(context);

					ServiceResponse serviceResponse = context.getResponse();

					if (!serviceResponse.isSuccessful()) {
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return super.showForm(request, response, errors);
					} else {
						Document newDocument = (Document)context.getResponse().getResultValueBean();

                        DocumentHistory documentHistory = new DocumentHistory();
                        documentHistory.setOperationTypeId(DocumentOperationType.ID_CREATED);
                        documentHistory.setDocument(newDocument);
                        documentHistory.save();

						// Audit (Touchpoint creation)
						AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, newDocument.getName(), newDocument.getId(), AuditActionType.ID_CREATION, null);

						Map<String, Object> parms = new HashMap<>();
						parms.put(REQUEST_PARM_DOCUMENTID, newDocument.getId());

						return new ModelAndView(new RedirectView(getSuccessView()), parms);
					}
				}
				case (ACTION_DUPLICATE_TOUCHPOINT): {
					analyticsEvent.setAction(Actions.Duplicate);
					Document document = Document.findById(docId);

					AnalyticsEvent<CloneEvents> cloneEvent = AnalyticsUtil.requestFor(CloneEvents.Touchpoint);
					cloneEvent.setAction(Actions.Clone);
					cloneEvent.add(CloneEvents.Properties.ContextName, () -> document.getName());
					cloneEvent.add(CloneEvents.Properties.MessageCount, () -> document.getMessages().size());
					Set<TouchpointSelection> selections = document.getTouchpointSelections();
					cloneEvent.add(CloneEvents.Properties.VariantCount, selections::size);
					List<ContentObject> smartTexts = ContentObject.findGlobalSmartTextsVisibleForDocument(document, false);
					cloneEvent.add(CloneEvents.Properties.SmartTextCount, smartTexts::size);
					List<ContentObject> imageLibraries = ContentObject.findGlobalImagesVisibleForDocument(document, false, false);
					cloneEvent.add(CloneEvents.Properties.ImageLibraryCount, imageLibraries::size);
					cloneEvent.add(CloneEvents.Properties.LocalImageLibraryCount, document::getLocalImageLibraryCount);
					cloneEvent.add(CloneEvents.Properties.localSmartTextCount, document::getLocalSmartTextCount);
					cloneEvent.add(CloneEvents.Properties.LocalCanvas, document::getLocalCanvasCount);

					try {


						String cloneTouchpointName = command.getCloneTouchpointName();
						if (cloneTouchpointName.isEmpty())
							cloneTouchpointName = document.getName();

						long useDataCollectionID = command.getUseDataCollectionId();

						Node sourceNode = Node.getCurrentNode();
						Node targetNode = command.getCloneToInstance();

                        if(SyncTouchpointUtil.hasActiveBackgroundTasks(sourceNode.getId(), targetNode.getId())) {
                            errors.reject("error.message.cannot.run.task");
                            return super.showForm(request, response, errors);
                        }

                        Document doc = Document.findById(docId);
                        Set<Long> touchpointLanguageLocales = doc.getTouchpointLanguagesAsLocales()
                                .stream()
                                .map(MessagepointLocale::getId)
                                .collect(Collectors.toSet());

                        User user = UserUtil.getPrincipalUser();
						String sourceSchema = null;
						SessionHolder mainSessionHolder = null;
						if (targetNode != null && sourceNode != null && targetNode.getId() != sourceNode.getId()) {
							sourceSchema = sourceNode.getSchemaName();
							mainSessionHolder = HibernateUtil.getManager().openTemporarySession(targetNode.getSchemaName());
							user = User.findByGuid(user.getGuid());
						}

						Set<Long> languagesForSync = command.getLanguagesForSync().stream().map(MessagepointLocale::getId).filter(id->touchpointLanguageLocales.contains(id)).collect(Collectors.toSet());

                        Set<Integer> dontOverrideObjectTypeIds = command.getCloneOptions();

                        if(dontOverrideObjectTypeIds == null) {
                            dontOverrideObjectTypeIds = new HashSet<>();
                        }

						CloneTouchpointBackgroundTask task = new CloneTouchpointBackgroundTask(docId, cloneTouchpointName, command.getExportOptions().isIncludeMessages(), command.getExportOptions().isIncludeAllVariables(), false, false, user, sourceSchema, useDataCollectionID, languagesForSync, dontOverrideObjectTypeIds);
						MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

						if (sourceSchema != null)
							HibernateUtil.getManager().restoreSession(mainSessionHolder);

						// Audit (Touchpoint cloned)
						AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, document.getName(), document.getId(), AuditActionType.ID_CHANGES,
								AuditMetadataBuilder.forSimpleAuditMessage(
										ApplicationUtil.getMessage("page.label.touchpoint.cloned") + ": " + cloneTouchpointName, null, null));

						// Audit (Touchpoint created)
						AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, cloneTouchpointName, null, AuditActionType.ID_CREATION,
								AuditMetadataBuilder.forSimpleAuditMessage(
										ApplicationUtil.getMessage("page.label.touchpoint.created"), null, null));
						Map<String, Object> parms = new HashMap<>();
						parms.put(REQUEST_PARM_DOCUMENTID, docId);
						cloneEvent.add(CloneEvents.Properties.Successful, () -> true);

						return new ModelAndView(new RedirectView(getSuccessView()), parms);
					}finally {
						cloneEvent.send();
					}
				}
				case (ACTION_DELETE_LANGUAGE): {
					analyticsEvent.setAction(Actions.DeleteLanguage);

					// For Audit
					StringBuilder deletedNames = new StringBuilder();
					for(TouchpointLanguage language:command.getSelectedLanguages()){
						if(!deletedNames.isEmpty())
							deletedNames.append(", ");
						deletedNames.append(language.getName());
					}
					TouchpointLanguage language = command.getSelectedLanguages().iterator().next();
					ServiceExecutionContext context = TouchpointLanguageEditService.createContext(docId, language.hashCode(), TouchpointLanguageEditService.ACTION_DELETE);
					Service touchpointLanguageEditService = MessagepointServiceFactory.getInstance().lookupService(TouchpointLanguageEditService.SERVICE_NAME, TouchpointLanguageEditService.class);
					touchpointLanguageEditService.execute(context);

					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return super.showForm(request, response, errors);
					} else {
						// TpContentChange set to true
						Document document = Document.findById(docId);
						if(document != null && !document.isTpContentChanged()){
							document.setTpContentChanged(true);
							document.save();
						}
						// Audit (Touchpoint language(s) deleted)
						AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, document.getName(), document.getId(), AuditActionType.ID_CHANGES,
								AuditMetadataBuilder.forSimpleAuditMessage(
										ApplicationUtil.getMessage("page.label.touchpoint.languages.deleted") + ": " + deletedNames, null, null));
						Map<String, Object> parms = new HashMap<>();
						parms.put(REQUEST_PARM_DOCUMENTID, docId);
						return new ModelAndView(new RedirectView(getSuccessView()), parms);
					}
				}
				case (ACTION_MAKE_DEFAULT_LANGUAGE): {
					analyticsEvent.setAction(Actions.MakeDefaultLanguage);

					Document document = Document.findById(docId);
					TouchpointLanguage language = document.getDefaultTouchpointLanguage();
					ServiceExecutionContext context = null;
					Service touchpointLanguageEditService = null;
					ServiceResponse serviceResponse = null;
					String FromLanguageName = "";
					if (language != null)
					{
						FromLanguageName = language.getName();
						language.setIsDefaultLanguage(false);
						context = TouchpointLanguageEditService.createContext(docId, language, TouchpointLanguageEditService.ACTION_EDIT);
						touchpointLanguageEditService = MessagepointServiceFactory.getInstance().lookupService(TouchpointLanguageEditService.SERVICE_NAME, TouchpointLanguageEditService.class);
						touchpointLanguageEditService.execute(context);

						serviceResponse = context.getResponse();
						if (!serviceResponse.isSuccessful()) {
							ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
							return super.showForm(request, response, errors);
						}
					}

					language = command.getSelectedLanguages().iterator().next();
					language.setIsDefaultLanguage(true);
					context = TouchpointLanguageEditService.createContext(docId, language, TouchpointLanguageEditService.ACTION_EDIT);
					touchpointLanguageEditService = MessagepointServiceFactory.getInstance().lookupService(TouchpointLanguageEditService.SERVICE_NAME, TouchpointLanguageEditService.class);
					touchpointLanguageEditService.execute(context);

					serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return super.showForm(request, response, errors);
					}

					// Set Default of Language Selections
					//
					if (document.getMasterLanguageSelection() != null)
					{
						List<LanguageSelection> languageSelections = new ArrayList<>();
						languageSelections.add(document.getMasterLanguageSelection());

						context = CreateOrUpdateLanguageSelectionService.createContextForModifyLanguage(languageSelections, language.getMessagepointLocale(), UserUtil.getPrincipalUser());
						Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateLanguageSelectionService.SERVICE_NAME, CreateOrUpdateLanguageSelectionService.class);
						service.execute(context);

						serviceResponse = context.getResponse();
						if (!serviceResponse.isSuccessful()) {
							ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
							return super.showForm(request, response, errors);
						}
					}
					if(document != null && !document.isTpContentChanged()){
						document.setTpContentChanged(true);
						document.save();
					}
					// Audit (Touchpoint set default language)
					AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, document.getName(), document.getId(), AuditActionType.ID_CHANGES,
							AuditMetadataBuilder.forSimpleAuditMessage(
									ApplicationUtil.getMessage("page.label.touchpoint.default.language.changed") , FromLanguageName, language.getName()));
					Map<String, Object> parms = new HashMap<>();
					parms.put(REQUEST_PARM_DOCUMENTID, docId);
					return new ModelAndView(new RedirectView(getSuccessView()), parms);
				}
				case (ACTION_REFRESH_SEG_ANALY):{
					analyticsEvent.setAction(Actions.RefreshSegmentAnalytics);

					ServiceExecutionContext context = CreateSegmentationAnalysisService.createContextForDocument(docId, UserUtil.getPrincipalUser());
					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateSegmentationAnalysisService.SERVICE_NAME, CreateSegmentationAnalysisService.class);
					service.execute(context);
					if (!context.getResponse().isSuccessful()) {
						log.error(" unexpected exception when invoking CreateSegmentationAnalysisService execute method");
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return showForm(request, response, errors);
					} else {
						// Audit (Refresh segmentation analysis)
						Document document = Document.findById(docId);
						AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, document.getName(), document.getId(), AuditActionType.ID_CHANGES,
								AuditMetadataBuilder.forSimpleAuditMessage(
										ApplicationUtil.getMessage("page.label.refreshed.segmentation.analysis") , null, null));
						Map<String, Object> parms = new HashMap<>();
						parms.put(REQUEST_PARM_DOCUMENTID, docId);
						return new ModelAndView(new RedirectView(getSuccessView()), parms);
					}
				}
				case (ACTION_RESTORE_LOCALE):{

					analyticsEvent.setAction(Actions.RestoreLocale);

					TouchpointLanguage language = command.getSelectedLanguages().iterator().next();
					ServiceExecutionContext context = TouchpointLanguageEditService.createContextForTouchpointLocaleReset(language);
					Service service = MessagepointServiceFactory.getInstance().lookupService(TouchpointLanguageEditService.SERVICE_NAME, TouchpointLanguageEditService.class);
					service.execute(context);

					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return super.showForm(request, response, errors);
					} else {
						// TpContentChange set to true
						Document document = Document.findById(docId);
						if(document != null && !document.isTpContentChanged()){
							document.setTpContentChanged(true);
							document.save();
						}

						Map<String, Object> parms = new HashMap<>();
						parms.put(REQUEST_PARM_DOCUMENTID, docId);
						return new ModelAndView(new RedirectView(getSuccessView()), parms);
					}

				}
				case (ACTION_LANGUAGE_REPAIR):{

					analyticsEvent.setAction(Actions.LanguageRepair);

					ServiceExecutionContext context = TouchpointLanguageEditService.createContextForLanguageRepair(docId);
					Service service = MessagepointServiceFactory.getInstance().lookupService(TouchpointLanguageEditService.SERVICE_NAME, TouchpointLanguageEditService.class);
					service.execute(context);

					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return super.showForm(request, response, errors);
					} else {
						Map<String, Object> parms = new HashMap<>();
						parms.put(REQUEST_PARM_DOCUMENTID, docId);
						return new ModelAndView(new RedirectView(getSuccessView()), parms);
					}

				}
				case ACTION_MAKE_DEFAULT_COMPOSITION_PACKAGE:
					Document document = Document.findById(docId);
					CompositionFileSet compositionFileSet = command.getSelectedCompositionFiles().iterator().next();

					ServiceExecutionContext context = CreateOrUpdateCompositionFileSetService.createContextForMakeDefault(compositionFileSet.getId());
					Service service = MessagepointServiceFactory.getInstance().lookupService(
							CreateOrUpdateCompositionFileSetService.SERVICE_NAME,
							CreateOrUpdateCompositionFileSetService.class
					);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return super.showForm(request, response, errors);
					}
					break;
			}

			Map<String, Object> parms = new HashMap<>();
			parms.put(REQUEST_PARM_DOCUMENTID, docId);
			return new ModelAndView(new RedirectView(getSuccessView()), parms);
		} finally {
			analyticsEvent.send();
		}
	}
	
	@Override
	protected ModelAndView showForm(
			HttpServletRequest request, HttpServletResponse response, BindException errors)
			throws Exception {

		long attachmentId 			= ServletRequestUtils.getLongParameter(request, REQUEST_PARM_ATTACHMENT_ID, -1);
		long touchpointTargetingId 	= ServletRequestUtils.getLongParameter(request, REQUEST_PARM_TOUCHPOINT_TARGETING_ID, -1);
		long docid 					= ServletRequestUtils.getLongParameter(request, REQUEST_PARM_DOCUMENTID, -1);
		long touchpointId			= ServletRequestUtils.getLongParameter(request, ContextBarTag.REQ_PARAM_DOCUMENT_ID_PARAM, -1);				
		
		Map<String, Object> parms = new HashMap<>();
		boolean redirect = false;
		long appliedDocumentId = -1;
		if (touchpointId != -1) {
			appliedDocumentId = touchpointId;
			redirect = true;
		} else if (docid != -1) {
			Document doc = Document.findById(docid);
			if(doc != null || docid == -2){
				appliedDocumentId = docid;
			}else{
				Document defaultTouchpoint = UserUtil.getCurrentTouchpointContext() != null ? UserUtil.getCurrentTouchpointContext() : getFirstTouchpoint();
				appliedDocumentId = (defaultTouchpoint != null ? defaultTouchpoint.getId() : -2);
				redirect = true;
			}
		} else if (docid == -1) {
			Document defaultTouchpoint = UserUtil.getCurrentTouchpointContext() != null ? UserUtil.getCurrentTouchpointContext() : getFirstTouchpoint();
			appliedDocumentId = (defaultTouchpoint != null ? defaultTouchpoint.getId() : -2);
			redirect = true;
		} else if (attachmentId != -1) {
			Attachment attachment = Attachment.findById(attachmentId);
			appliedDocumentId = attachment.getDocuments().iterator().next().getId();
			redirect = true;
		} else if (touchpointTargetingId != -1) {
			TouchpointTargeting touchpointTargeting = TouchpointTargeting.findById(touchpointTargetingId);
			appliedDocumentId = touchpointTargeting.getDocument().getId();
			redirect = true;
		}	
		
		HashMap<String,String> contextAttr = new HashMap<>();
		// Set return from setup page context
		contextAttr.put(UserUtil.CONTEXT_KEY_RETURN_FROM_SETUP_PAGE_CONTEXT, "/tpadmin/document_view.form");
		if ( appliedDocumentId > 0 )
			contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_CONTEXT, String.valueOf(appliedDocumentId) );
		// Update user context properties
		UserUtil.updateUserContextAttributes(contextAttr);
		
		// Touchpoint Targeting: Init if not existing for Touchpoint
		Document appliedDocument = Document.findById(appliedDocumentId);
		if ( appliedDocument != null && TouchpointTargeting.findByDocument(appliedDocument) == null ) {
			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateDocumentService.SERVICE_NAME, UpdateDocumentService.class);
			ServiceExecutionContext context = UpdateDocumentService.createContextForInitializeTargeting();
			service.execute(context);
			if ( context.getResponse().isSuccessful() )
				redirect = true;
		}
		
		if ( redirect ) {
			parms.put(REQUEST_PARM_DOCUMENTID, appliedDocumentId);
			// prevent invalid state in Redirect view when url is not set (null)
			return new ModelAndView(new RedirectView(getSuccessView() != null ? getSuccessView() : "document_view.form"), parms);
		} else		
			return super.showForm(request, response, errors);

	}
	
	private Document getFirstTouchpoint() {
		List<Document> documents = Document.findAll();
		if (!documents.isEmpty())
			return documents.iterator().next();
		return null;
	}

	private List<ZoneVO> convertToVOList(List<Zone> zoneList) {
		List<ZoneVO> voList = new ArrayList<>();
		if (zoneList != null) {
			for (Zone zone : zoneList) {
				if (zone != null) {
					ZoneVO vo = new ZoneVO();
					vo.setId(zone.getId());
					vo.setDocumentId(zone.getDocument().getId());
					vo.setName(zone.getName());
					vo.setFriendlyName(zone.getFriendlyName());
					vo.setIsEmailSubjectLine(zone.getIsEmailSubjectLine());
					if(zone.getDataGroup() != null) {
						vo.setDataGroup(zone.getDataGroup().getName());
					}
					
					String contentTypeStr = ContentType.findById(zone.getContentTypeId()).getName();
					if (zone.getSubContentTypeId() > 0) {
						contentTypeStr = contentTypeStr + "-" + SubContentType.findById(zone.getSubContentTypeId()).getName();
					}
					vo.setContentType(contentTypeStr);
					if (zone.isEnabled())
						vo.setActiveStatus("Yes");
					else
						vo.setActiveStatus("No");
					voList.add(vo);
				}
			}
		}
		return voList;
	}
	
	private Long getDocumentId(HttpServletRequest request) {
		long touchpointTargetingId 	= ServletRequestUtils.getLongParameter(request, REQUEST_PARM_TOUCHPOINT_TARGETING_ID, -1);
		if (touchpointTargetingId != -1)
			return TouchpointTargeting.findById(touchpointTargetingId).getDocument().getId();
		return ServletRequestUtils.getLongParameter(request, REQUEST_PARM_DOCUMENTID, -1);
	}

	public String getFormViewParentRedirect() {
		return formViewParentRedirect;
	}
	public void setFormViewParentRedirect(String formViewParentRedirect) {
		this.formViewParentRedirect = formViewParentRedirect;
	}
	
	public void setEditLanguageView(String editLanguageView) {
		this.editLanguageView = editLanguageView;
	}
	public String getEditLanguageView() {
		return editLanguageView;
	}

}