package com.prinova.messagepoint.controller.communication;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.AuditReportEvents;
import com.prinova.messagepoint.analytics.types.ConnectedEvents;
import com.prinova.messagepoint.connected.data.CacheDataService;
import com.prinova.messagepoint.connected.data.json.support.CommunicationLogType;
import com.prinova.messagepoint.connected.util.Constants;
import com.prinova.messagepoint.connected.util.SupportingDataUtil;
import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.communication.connected.ConnectedReferenceDataSourceUtil;
import com.prinova.messagepoint.controller.touchpoints.AssignmentFilterType;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.DataSourceAssociation;
import com.prinova.messagepoint.model.admin.ReferenceConnection;
import com.prinova.messagepoint.model.admin.SourceType;
import com.prinova.messagepoint.model.audit.*;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationNotificationEventDispatcher;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItem;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItemDefinition;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.communication.CommunicationUnlockModelService;
import com.prinova.messagepoint.platform.services.communication.CreateCommunicationProductionBatchService;
import com.prinova.messagepoint.platform.services.communication.CreateCommunicationProofService;
import com.prinova.messagepoint.platform.services.communication.CreateOrUpdateCommunicationService;
import com.prinova.messagepoint.platform.services.export.CreateOrUpdateAuditReportService;
import com.prinova.messagepoint.platform.services.export.GenerateConnectedAuditReportService;
import com.prinova.messagepoint.platform.services.export.GenerateMessageAuditReportService;
import com.prinova.messagepoint.platform.services.insert.DeactivateService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowApproveService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowRejectService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowReleaseForApprovalService;
import com.prinova.messagepoint.platform.ws.client.CommunicationWSClient;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.*;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class TouchpointCommunicationsListController extends MessagepointController {

	private static final Log log = LogUtil.getLog(TouchpointCommunicationsListController.class);

	public static final String REQ_PARAM_COMMUNICATION_ID 	= "communicationId";
	public static final String REQ_PARAM_NEW_RECIPIENT_ID 	= "recipientId";
	public static final String REQ_PARAM_DOCUMENTID 		= "documentId";
	public static final String REQ_PARAM_VIEWID 			= "viewId";
	public static final String REQ_PARAM_ACTION 			= "action";
	public static final String REQ_PARAM_CONTEXT	 		= "context";

	public static final String COOKIE_DONT_ASK_AGAIN		= "mp-dontaskagain";

	public static final int ACTION_EDIT					= 10;
	public static final int ACTION_EDIT_INDICATOR		= 15;
	public static final int ACTION_DELETE_COMMUNICATION = 2;
	public static final int ACTION_APPROVE 				= 3;
	public static final int ACTION_REJECT 				= 4;
	public static final int ACTION_RELEASE_FOR_APPROVAL = 5;
	public static final int ACTION_ACTIVATE 			= 6;
	public static final int ACTION_REQUEST_PROOF		= 7;
	public static final int ACTION_CLONE				= 8;
	public static final int ACTION_RUN_BATCH			= 9;
	public static final int ACTION_REASSIGN				= 11;
	public static final int ACTION_SOCIALIZE			= 12;
	public static final int ACTION_AUDIT_REPORT			= 13;
	public static final int ACTION_UNSUBMIT 			= 14;
	public static final int ACTION_REASSIGN_EDIT 		= 21;
	public static final int ACTION_REASSIGN_ACTIVATE 	= 22;
	public static final int ACTION_REASSIGN_RELEASE 	= 23;


	private String editView;
	private CacheDataService dataService;

	public void setDataService (CacheDataService dataService) {
		this.dataService = dataService;
	}

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		User principalUser = UserUtil.getPrincipalUser();
		Boolean isTestCenterContext = request.getRequestURL().indexOf("test_center_communications_list") != -1;
		referenceData.put("isTestCenterContext", isTestCenterContext);

		// Filters
		List<AssignmentFilterType> primaryFilterTypes = AssignmentFilterType.listAllForOrderList();
		if ( !UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_COMMUNICATIONS_SETUP) )
			primaryFilterTypes.remove( new AssignmentFilterType(AssignmentFilterType.ID_ALL) );
		if ( UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_COMMUNICATIONS_ASSIGNED) ) {
			primaryFilterTypes.remove(new AssignmentFilterType(AssignmentFilterType.ID_MY));
			primaryFilterTypes.remove(new AssignmentFilterType(AssignmentFilterType.ID_CREATED));
		}
		referenceData.put("primaryFilterTypes", primaryFilterTypes);

		List<CommunicationStatusFilterType> communicationStatusFilterTypes = CommunicationStatusFilterType.listAll();
		referenceData.put("communicationStatusFilterTypes", communicationStatusFilterTypes);

		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		Document document = Document.findById(documentId);
		boolean connectedEnabled = false;
		if (document != null) {

			referenceData.put("document", document);
			connectedEnabled = document.isConnectedEnabled();

			referenceData.put("languages", document.getTouchpointLanguagesAsLocales());

			referenceData.put("orderEntryEnabled", document.isCommunicationOrderEntryEnabled());
			referenceData.put("webServiceDriverEnabled", document.isCommunicationWebServiceDriverEnabled() );

			referenceData.put("primaryDriverLabel", document.getCommunicationPrimaryLabel() );

			referenceData.put("pollProductionStatus", document.isCommunicationWebServiceProductionStatusEnabled() );

			referenceData.put("hasRequestorConfigured", !document.getCommunicationOrderEntryItemDefinitionsInOrder().isEmpty());

			boolean hasVariablesForAllItems = true;
			String invalidDataItemsName = "";
			boolean hasLinkedDataElementForAllItems = true;
			boolean missingDataReferenceConnection = true;
			boolean shareCollectionWithAnOldConnectedDoc= false;
			String variableWithoutDataElement = "";
			if(ConnectedReferenceDataSourceUtil.getConnectedDataSource(document) != null){
				DataSourceAssociation dsa = document.getDataSourceAssociation();
				if (dsa != null) {
					for (ReferenceConnection connection : dsa.getReferenceConnections()) {
						if (connection.getReferenceDataSource().getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED) {
							missingDataReferenceConnection = false;
							break;
						}
					}
					if (document.isCommunicationUseBeta()) {
						List<Document> documents = Document.findAll();
						for (Document doc : documents) {
							if (!doc.isCommunicationUseBeta() && doc.getDataSourceAssociation() != null && doc.getDataSourceAssociation().getId() == dsa.getId()) {
								shareCollectionWithAnOldConnectedDoc = true;
							}
						}
					}
				}
			}
			for (CommunicationOrderEntryItemDefinition item: document.getCommunicationOrderEntryItemDefinitionsInOrder() ) {
				if (item.getTypeId() != MetadataFormItemType.ID_FILE && item.getTypeId() != MetadataFormItemType.ID_HEADER && item.getTypeId() != MetadataFormItemType.ID_DIVIDER && item.getDataElementVariable() == null) {
					hasVariablesForAllItems = false;
					invalidDataItemsName =	invalidDataItemsName.concat(item.getName() + "; ");
				}
				 else if (item.getTypeId() != MetadataFormItemType.ID_FILE && item.getTypeId() != MetadataFormItemType.ID_HEADER && item.getTypeId() != MetadataFormItemType.ID_DIVIDER && item.getDataElementVariable() != null && AbstractDataElement.findDataElementByVariableId(item.getDataElementVariable().getId(), document) == null) {
					hasLinkedDataElementForAllItems = false;
					variableWithoutDataElement = variableWithoutDataElement.concat(item.getDataElementVariable().getName() + "; ");
				}
			}
			referenceData.put("hasVariablesForAllItems",hasVariablesForAllItems);
			referenceData.put("missingDataReferenceConnection",missingDataReferenceConnection);
			referenceData.put("invalidDataItemsName",invalidDataItemsName);
			referenceData.put("hasLinkedDataElementForAllItems",hasLinkedDataElementForAllItems);
			referenceData.put("variableWithoutDataElement",variableWithoutDataElement);
			referenceData.put("shareCollectionWithAnOldConnectedDoc",shareCollectionWithAnOldConnectedDoc);

			referenceData.put("isDigitalProof", document.getIsOmniChannel() || (!document.getIsOmniChannel() && document.isEmailTouchpoint() || document.isWebTouchpoint()) );

			referenceData.put("externalValidationEnabled", document.isCommunicationExternalValidationEnabled() );
			referenceData.put("isNewConnected", document.isCommunicationUseBeta() );
		}

		referenceData.put("connectedEnabled", connectedEnabled );

		AuditReport latestAuditReport = AuditReport.findLatestByUserId(principalUser.getId(), AuditReportType.ID_CONNECTED_REPORT);
		if (latestAuditReport != null) {
			referenceData.put("auditReport", latestAuditReport);
		}
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
		binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		Document document = Document.findById(documentId);
		TouchpointCommunicationsListWrapper wrapper = new TouchpointCommunicationsListWrapper();
		String	exportName = "";
		if(document != null){
			exportName = document.getName();
		}

		if (exportName == null || !exportName.isEmpty())
        	exportName = exportName.replaceAll(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"), "");
        wrapper.setExportName(exportName);
		return wrapper;
    }

	@Override
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		// ******* Start User context persist/recall ************
		long documentId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_DOCUMENT_ID, -1L);

		if ( documentId != -1 && documentId != -2 ) {
			HashMap<String,String> contextAttr = new HashMap<>();
			contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_CONTEXT, String.valueOf(documentId));
			UserUtil.updateUserContextAttributes(contextAttr);
		}
		// ******* End User context persist/recall **************

		boolean idUpdated = false;

		// -2: No documents
		if ( documentId != -2 ) {

			// Prefer URL specified document
			Document doc = Document.findById(documentId);
			if( doc == null ) {

				// No URL specified document: Retrieve user context document
				doc = UserUtil.getCurrentTouchpointContext();
				if (doc == null)
					doc = getDefaultTouchpoint();

				if (doc != null) {
					documentId = doc.getId();
					idUpdated = true;
				} else {
					Map<String, Object> params = new HashMap<>();
					params.put(REQ_PARAM_DOCUMENTID, -2);
					return new ModelAndView(new RedirectView(request.getContextPath() +  "/touchpoints/touchpoint_communications_list.form"), params);
				}
			}
		}

		if ( idUpdated ) {
			return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request, documentId));
		} else {
			return super.handleRequestInternal(request, response);
		}
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
			BindException errors) throws Exception {

		int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, -1);

		AnalyticsEvent<ConnectedEvents> analyticsEvent = AnalyticsUtil.requestFor(ConnectedEvents.List);

		try {
			TouchpointCommunicationsListWrapper command = (TouchpointCommunicationsListWrapper) commandObj;

			this.setCookies(request, response, command);

			User principalUser = UserUtil.getPrincipalUser();
			User requestor = User.findById(principalUser.getId());

			switch (action) {
				case (ACTION_EDIT): {
					// Add Communiciation URL param: communicationId
					Map<String, Object> params = new HashMap<>();
					Long communicationId = command.getSelectedIds().get(0);
					Communication communication = Communication.findById(communicationId);
					params.put(REQ_PARAM_COMMUNICATION_ID, communicationId);
					params.put(REQ_PARAM_DOCUMENTID, communication.getDocument().getId());
					if(communication.isTestOrder())
						params.put(REQ_PARAM_CONTEXT, "test");

					String editURL;
					if ( communication.getZoneContentAssociations() != null && !communication.getZoneContentAssociations().isEmpty() ) {
						if (communication.getDocument().isCommunicationUseBeta()) {
							editURL = ApplicationUtil.getWebRoot() + "connected/connected_touchpoint_rendering_manager.form";
						} else {
							editURL = ApplicationUtil.getWebRoot() + "communication/communication_content_edit.form";
						}
					}
					else
						editURL = ApplicationUtil.getWebRoot() + "communication/communication_order_entry_edit.form";

					analyticsEvent.setAction(Actions.Edit);

					return new ModelAndView(new RedirectView(editURL), getModifiedSuccessViewParams(request,params));
				}
				case (ACTION_EDIT_INDICATOR): {

                    List<Communication> communications = command.getSelectedList();
                    Communication communication = communications.get(0);
                    String newValue = command.getRecipientIdentifier();
                    ServiceExecutionContext context;
                    JSONObject communicationJson;

                    if (communication.getDocument().isCommunicationUseBeta()) {

                        SandboxFile refDataFile = CommunicationWSClient.retrieveRefDataFile(
                                communication.getDocument().getCommunicationCompositionResultsWebService().getUrl(),
                                communication.getDocument().getCommunicationCompositionResultsWebService().getUsername(),
                                communication.getDocument().getCommunicationCompositionResultsWebService().getPassword(),
                                communication.getGuid(), communication.getPmgrOrderUUID()
                        );
                        SupportingDataUtil.logCommunicationAction(communication.getId(), CommunicationLogType.RETRIEVE_INTERVIEW_DATA, new Date(), new Date(), refDataFile == null);
                        if (refDataFile != null) {
                            JSONObject interviewValues = new JSONObject(new String(refDataFile.getFileContent()));
                            String communicationJsonBackend = dataService.getInterviewDefinitionFile(communication.getDocument());
                            communicationJson = new JSONObject(communicationJsonBackend);
                            communication.setMetadataFormItemArray(communicationJson);
                            communication.setInterviewValuesJson(interviewValues);
                            JSONArray interviewItems = (JSONArray) communication.getMetadataFormItemArray().get(Constants.Key_MetadataFormItemDefinitionVOs);
                            long indicatorId = 0;
                            for (int i = 0; i < interviewItems.length(); i++) {
                                JSONObject item = (JSONObject) interviewItems.get(i);
                                if (item.getBoolean(Constants.Key_IsIndicatorEntry)) {
                                    indicatorId = item.getLong("id");
                                    break;
                                }
                            }
                            interviewItems = (JSONArray) communication.getInterviewValues().get(Constants.Key_InterviewValues);
                            for (int i = 0; i < interviewItems.length(); i++) {
                                JSONObject item = (JSONObject) interviewItems.get(i);
                                if (item.getLong("id") == indicatorId) {
                                    item.put(Constants.VALUE, newValue);
                                    break;
                                }
                            }
                        }
                    } else {
						Set<CommunicationOrderEntryItem> orderEntryItems = communication.getOrderEntryItems();
						for (CommunicationOrderEntryItem item : orderEntryItems) {
							if (!item.getIsIndicatorEntry()) {
								continue;
							}
							item.setValue(newValue);
							break;
						}
						communication.setCustomerIdentifier(newValue);
                    }

                    communication.setIndicator(newValue);

                    context = CreateOrUpdateCommunicationService.createContextForUpdateOrderEntry(communication, false, principalUser);
                    Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
                    service.execute(context);
                    ServiceResponse serviceResponse = context.getResponse();

                    analyticsEvent.setAction(Actions.Rename);

                    if (!serviceResponse.isSuccessful()) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(CreateOrUpdateCommunicationService.SERVICE_NAME)
                                .append(" service call is not successful ")
                                .append(" in ").append(this.getClass().getName())
                                .append(" requestor = ").append(requestor.getUsername())
                                .append(" tree node was not renamed. ");
                        log.error(sb.toString());
                        ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
                        return super.showForm(request, response, errors);
                    } else {
                        // Notify about event
                        sendNotification(CommunicationNotificationEventDispatcher.NOTIFICATION_EVENT_UPDATE, getDocument(request), principalUser, communications);
                        return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
                    }
				}
				case (ACTION_DELETE_COMMUNICATION): {
					List<Communication> communications = command.getSelectedList();
					ServiceExecutionContext context = CreateOrUpdateCommunicationService.createContextForDelete(communications, principalUser);

					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();

					analyticsEvent.setAction(Actions.Delete);


					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateCommunicationService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" tree node was not deleted. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						// Notify about event
						sendNotification(CommunicationNotificationEventDispatcher.NOTIFICATION_EVENT_DELETE, getDocument(request), principalUser, communications);
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_ACTIVATE): {
					/**
					 * ACTION_ACTIVATE:
					 *
					 */
					List<Communication> communications = command.getSelectedList();
					ServiceExecutionContext context = WorkflowReleaseForApprovalService.createContext(requestor, command.getUserNote(), communications.toArray(new Communication[]{}));
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();

					analyticsEvent.setAction(Actions.Activate);

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowReleaseForApprovalService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						for (Communication communication : communications) {
							sb.append(" id ").append(communication.getId());
						}
						sb.append(" requestor=").append(requestor.getUsername());
						sb.append("  communications are not activated. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_RELEASE_FOR_APPROVAL): {
					List<Communication> communications = command.getSelectedList();
					ServiceExecutionContext context = WorkflowReleaseForApprovalService.createContext(requestor, command.getUserNote(), communications.toArray(new Communication[]{}));

					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();

					analyticsEvent.setAction(Actions.ReleaseForApproval);

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowReleaseForApprovalService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" release for approval was not completed. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						// Notify about event
						sendNotification(CommunicationNotificationEventDispatcher.NOTIFICATION_EVENT_RELEASE_FOR_APPROVAL, getDocument(request), principalUser, communications);
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				} case (ACTION_APPROVE): {
					List<Communication> communications = command.getSelectedList();
					ServiceExecutionContext context = WorkflowApproveService.createContext(requestor, command.getUserNote(), false,  communications.toArray(new Communication[]{}));

					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();

					analyticsEvent.setAction(Actions.Approve);

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowApproveService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" approvals not applied. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				} case (ACTION_REJECT): {
					List<Communication> communications = command.getSelectedList();
					ServiceExecutionContext context = WorkflowRejectService.createContext(requestor, command.getAssignedToUser(), command.getUserNote(), communications.toArray(new Communication[]{}));

					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowRejectService.SERVICE_NAME, WorkflowRejectService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();

					analyticsEvent.setAction(Actions.Reject);

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowRejectService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" approvals not applied. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_REQUEST_PROOF): {
					List<Communication> communications = command.getSelectedList();
					Communication communication = communications.get(0);

					ServiceExecutionContext context = CreateCommunicationProofService.createContextForProof(communication, command.getProofEmail(), UserUtil.getPrincipalUser(), "Order List: Proof");
					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateCommunicationProofService.SERVICE_NAME, CreateCommunicationProofService.class);
					service.execute(context);

					analyticsEvent.setAction(Actions.RequestProof);

					if (!context.getResponse().isSuccessful()) {
						log.error(" unexpected exception when invoking CreateCommunicationProofService execute method");
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_RUN_BATCH): {
					ServiceExecutionContext context = CreateCommunicationProductionBatchService.createContextForBatch(UserUtil.getCurrentTouchpointContext(), command.getBatchWindowStartDate(), command.getBatchWindowEndDate(), UserUtil.getPrincipalUser());
					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateCommunicationProductionBatchService.SERVICE_NAME, CreateCommunicationProductionBatchService.class);
					service.execute(context);

					analyticsEvent.setAction(Actions.RunBatch);

					if (!context.getResponse().isSuccessful()) {
						log.error(" unexpected exception when invoking CreateCommunicationProductionBatchService execute method");
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_CLONE): {

					List<Communication> communications = command.getSelectedList();
					ServiceExecutionContext context = CreateOrUpdateCommunicationService.createContextForClone(communications.iterator().next(), command.getCloneInterview(), command.getCloneInteractive(), principalUser);

					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();

					analyticsEvent.setAction(Actions.Clone);

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateCommunicationService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" order was not cloned. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {

						Communication clonedOrder = (Communication) serviceResponse.getResultValueBean();

						Map<String, Object> params = new HashMap<>();
						params.put(REQ_PARAM_COMMUNICATION_ID, clonedOrder.getId());

						if (clonedOrder.getDocument().isCommunicationUseBeta() && clonedOrder.GetInteractiveInitialized()) {
							return new ModelAndView(new RedirectView(request.getContextPath() + "/connected/connected_touchpoint_rendering_manager.form"), params);
						}

						return new ModelAndView(new RedirectView("../communication/communication_order_entry_edit.form"), getModifiedSuccessViewParams(request,params));
					}

				}
				case (ACTION_REASSIGN): {
					/**
					 * ACTION_REASSIGN: - reassign to another user
					 *
					 */
					ServiceExecutionContext context = CommunicationUnlockModelService.createReassignContext(command.getSelectedList(),
							requestor.getId(),
							command.getAssignedToUser().getId(),
							command.getUserNote(),false);
					Service service = MessagepointServiceFactory.getInstance().lookupService(CommunicationUnlockModelService.SERVICE_NAME,
							CommunicationUnlockModelService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();

					analyticsEvent.setAction(Actions.Reassign);

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CommunicationUnlockModelService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor=").append(requestor.getUsername());
						sb.append("  Communication is not reassigned. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_SOCIALIZE): {
					List<Communication> communications = command.getSelectedList();
					Communication communication = communications.get(0);

					ServiceExecutionContext context = CreateCommunicationProofService.createContextForExternalValidation(communication, command.getSocializeEmail(), UserUtil.getPrincipalUser());
					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateCommunicationProofService.SERVICE_NAME, CreateCommunicationProofService.class);
					service.execute(context);

					analyticsEvent.setAction(Actions.Socialize);

					if (!context.getResponse().isSuccessful()) {
						log.error(" unexpected exception when invoking CreateCommunicationProofService execute method");
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_AUDIT_REPORT): {
					AnalyticsEvent<AuditReportEvents> auditEvent = AnalyticsUtil.requestFor(AuditReportEvents.Connected);
					auditEvent.setAction(Actions.GenerateReport);
					try {
						List<Long> communications = new ArrayList<>();
						if (command.getSelectedIds() != null)
							communications.addAll(command.getSelectedIds());
						if (communications == null || communications.isEmpty()) {
							for (Communication comm : Communication.findAllByDocument(getDocument(request))) {
								communications.add(comm.getId());
							}
						}
						ServiceExecutionContext context = CreateOrUpdateAuditReportService.createContextForCreate(requestor.getId(), AuditReportType.ID_CONNECTED_REPORT);
						Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
						service.execute(context);
						ServiceResponse serviceResponse = context.getResponse();
						if (!serviceResponse.isSuccessful()) {
							StringBuilder sb = new StringBuilder();
							sb.append(CreateOrUpdateAuditReportService.SERVICE_NAME);
							sb.append(" service call is not successful ");
							sb.append(" in ").append(this.getClass().getName());
							sb.append(" requestor = ").append(requestor.getUsername());
							sb.append(" AuditReport object could not be created. ");
							log.error(sb.toString());
							ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
							return showForm(request, response, errors);
						} else {
							long auditReportId = (Long) serviceResponse.getResultValueBean();
							AuditReportType type = new AuditReportType(AuditReportType.ID_CONNECTED_REPORT);

							context = GenerateConnectedAuditReportService.createContext(auditReportId, communications, requestor);

							service = MessagepointServiceFactory.getInstance().lookupService(GenerateConnectedAuditReportService.SERVICE_NAME, GenerateConnectedAuditReportService.class);
							service.execute(context);
							serviceResponse = context.getResponse();

							if (!serviceResponse.isSuccessful()) {

								context = CreateOrUpdateAuditReportService.createContextForError(auditReportId);
								service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
								service.execute(context);
								// Audit (Audit Report Failed)
								AuditEventUtil.push(AuditEventType.ID_AUDIT_REPORT, AuditObjectType.ID_AUDIT_REPORT, type.getName(), auditReportId, AuditActionType.ID_AUDIT_REPORT_REQUESTED_FAIL, null);


								StringBuilder sb = new StringBuilder();
								sb.append(GenerateMessageAuditReportService.SERVICE_NAME);
								sb.append(" service call is not successful ");
								sb.append(" in ").append(this.getClass().getName());
								sb.append(" requestor = ").append(requestor.getUsername());
								sb.append(" AuditReport object could not be generated. ");
								log.error(sb.toString());
								ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
								return showForm(request, response, errors);
							} else {
								// Audit (Audit Report Success)
								AuditEventUtil.push(AuditEventType.ID_AUDIT_REPORT, AuditObjectType.ID_AUDIT_REPORT, type.getName(), auditReportId, AuditActionType.ID_AUDIT_REPORT_REQUESTED_SUCCEED, null);

								return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
							}
						}
					}finally {
						auditEvent.send();
					}
				}
				case (ACTION_UNSUBMIT): {
					List<Communication> communications = command.getSelectedList();
					ServiceExecutionContext context = CreateOrUpdateCommunicationService.createContextForUnsubmit(communications, principalUser);
					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
					service.execute(context);
					if (!context.getResponse().isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(DeactivateService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor=").append(principalUser);
						sb.append("  Communication was not unsubmitted. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_REASSIGN_EDIT): {
					/**
					 * ACTION_REASSIGN_EDIT: - reassign to current user and go to edit.
					 *
					 */
					ServiceExecutionContext context = CommunicationUnlockModelService.createReassignContext(command.getSelectedList(),
							requestor.getId(),
							requestor.getId(),
							command.getUserNote(),false);
					Service service = MessagepointServiceFactory.getInstance().lookupService(CommunicationUnlockModelService.SERVICE_NAME,
							CommunicationUnlockModelService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();

					analyticsEvent.setAction(Actions.Reassign);

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder().append(CommunicationUnlockModelService.SERVICE_NAME)
								.append(" service call is not successful in ").append(this.getClass().getName())
								.append(" requestor=").append(requestor.getUsername()).append(".  Communication is not reassigned. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					}

					// Add Communiciation URL param: communicationId
					Map<String, Object> params = new HashMap<>();
					Long communicationId = command.getSelectedIds().get(0);
					Communication communication = Communication.findById(communicationId);
					params.put(REQ_PARAM_COMMUNICATION_ID, communicationId);
					params.put(REQ_PARAM_DOCUMENTID, communication.getDocument().getId());
					if(communication.isTestOrder())
						params.put(REQ_PARAM_CONTEXT, "test");

					String editURL;
					if ( communication.getZoneContentAssociations() != null && !communication.getZoneContentAssociations().isEmpty() ) {
						if (communication.getDocument().isCommunicationUseBeta()) {
							editURL = ApplicationUtil.getWebRoot() + "connected/connected_touchpoint_rendering_manager.form";
						} else {
							editURL = ApplicationUtil.getWebRoot() + "communication/communication_content_edit.form";
						}
					}
					else
						editURL = ApplicationUtil.getWebRoot() + "communication/communication_order_entry_edit.form";

					analyticsEvent.setAction(Actions.Edit);

					return new ModelAndView(new RedirectView(editURL), getModifiedSuccessViewParams(request,params));

				}
				case (ACTION_REASSIGN_ACTIVATE): {
					/**
					 * ACTION_REASSIGN_ACTIVATE: - reassign to current user and activate.
					 *
					 */
					ServiceExecutionContext context = CommunicationUnlockModelService.createReassignContext(command.getSelectedList(),
							requestor.getId(),
							requestor.getId(),
							command.getUserNote(),false);
					Service service = MessagepointServiceFactory.getInstance().lookupService(CommunicationUnlockModelService.SERVICE_NAME,
							CommunicationUnlockModelService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();

					analyticsEvent.setAction(Actions.Reassign);

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder().append(CommunicationUnlockModelService.SERVICE_NAME)
								.append(" service call is not successful in ").append(this.getClass().getName())
								.append(" requestor=").append(requestor.getUsername()).append(".  Communication is not reassigned. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					}

					List<Communication> communications = command.getSelectedList();
					context = WorkflowReleaseForApprovalService.createContext(requestor, command.getUserNote(), communications.toArray(new Communication[]{}));
					service = MessagepointServiceFactory.getInstance()
							.lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
					service.execute(context);
					serviceResponse = context.getResponse();

					analyticsEvent.setAction(Actions.Activate);

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowReleaseForApprovalService.SERVICE_NAME).append(" service call is not successful in ").append(this.getClass().getName());
						for (Communication communication : communications) {
							sb.append(" id ").append(communication.getId());
						}
						sb.append(" requestor=").append(requestor.getUsername()).append(". Communications are not activated. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}

				}
				case (ACTION_REASSIGN_RELEASE): {
					/**
					 * ACTION_REASSIGN_RELEASE: - reassign to current user and activate.
					 *
					 */
					ServiceExecutionContext context = CommunicationUnlockModelService.createReassignContext(command.getSelectedList(),
							requestor.getId(),
							requestor.getId(),
							command.getUserNote(),false);
					Service service = MessagepointServiceFactory.getInstance().lookupService(CommunicationUnlockModelService.SERVICE_NAME,
							CommunicationUnlockModelService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();

					analyticsEvent.setAction(Actions.Reassign);

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder().append(CommunicationUnlockModelService.SERVICE_NAME)
								.append(" service call is not successful in ").append(this.getClass().getName())
								.append(" requestor=").append(requestor.getUsername()).append(".  Communication is not reassigned. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					}

					List<Communication> communications = command.getSelectedList();
					context = WorkflowReleaseForApprovalService.createContext(requestor, command.getUserNote(), communications.toArray(new Communication[]{}));

					service = MessagepointServiceFactory.getInstance().lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
					service.execute(context);
					serviceResponse = context.getResponse();

					analyticsEvent.setAction(Actions.ReleaseForApproval);

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder().append(WorkflowReleaseForApprovalService.SERVICE_NAME)
								.append(" service call is not successful in ").append(this.getClass().getName())
								.append(" requestor = ").append(requestor.getUsername()).append(". Release for approval was not completed. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						// Notify about event
						sendNotification(CommunicationNotificationEventDispatcher.NOTIFICATION_EVENT_RELEASE_FOR_APPROVAL, getDocument(request), principalUser, communications);
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}

				}
				default:
					break;
				}
			return null;
		} finally {
			analyticsEvent.send();
		}
	}

	private void setCookies (HttpServletRequest request, HttpServletResponse response, TouchpointCommunicationsListWrapper command) {
		if (command.getDontAskAgain()) {
			Calendar endOfDay = Calendar.getInstance();
			int year = endOfDay.get(Calendar.YEAR);
			int month = endOfDay.get(Calendar.MONTH);
			int day = endOfDay.get(Calendar.DATE);
			endOfDay.set(year, month, day, 23, 59, 59);
			long expires = endOfDay.getTimeInMillis() - new Date().getTime();
			Cookie cookie = new Cookie(COOKIE_DONT_ASK_AGAIN, "true");
			cookie.setMaxAge((int)expires/1000);
			cookie.setPath("/");
			response.addCookie(cookie);
		}
	}

	private void sendNotification(int eventType, Document document, User requestor, List<Communication> communications) {
		if (document.isCommunicationWebServiceNotificationEnabled()) {
			CommunicationNotificationEventDispatcher task = new CommunicationNotificationEventDispatcher(eventType, document, requestor, communications);
			MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
		}
	}

	private Map<String, Object> getSuccessViewParams(HttpServletRequest request){
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		return getSuccessViewParams(request, documentId);
	}

	private Map<String, Object> getSuccessViewParams(HttpServletRequest request, long documentId) {
		Map<String, Object> params = new HashMap<>();
		long viewid = ServletRequestUtils.getLongParameter(request, REQ_PARAM_VIEWID, -1L);

		if(viewid != -1L){
			params.put(REQ_PARAM_VIEWID, viewid);
		}
		if(documentId != -1L){
			params.put(REQ_PARAM_DOCUMENTID, documentId);
		}

		boolean isEmbedded = ServletRequestUtils.getBooleanParameter(request, CommunicationPortalGatewayController.REQ_PARAM_EMBEDDED, false);
		if ( isEmbedded ) {
			params.put(CommunicationPortalGatewayController.REQ_PARAM_EMBEDDED, isEmbedded);
			String returnUrl = CommunicationPortalGatewayController.getWhitelistedReturnUrl(request);
			if ( returnUrl != null )
				params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_URL, returnUrl);
			boolean isReturnToList = ServletRequestUtils.getBooleanParameter(request, CommunicationPortalGatewayController.REQ_PARAM_RETURN_TO_LIST, false);
			if ( isReturnToList )
				params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_TO_LIST, isReturnToList);
		}

		return params;
	}

	private Map<String, Object> getModifiedSuccessViewParams(HttpServletRequest request, Map<String,Object> params) {

		boolean isEmbedded = ServletRequestUtils.getBooleanParameter(request, CommunicationPortalGatewayController.REQ_PARAM_EMBEDDED, false);
		if ( isEmbedded ) {
			params.put(CommunicationPortalGatewayController.REQ_PARAM_EMBEDDED, isEmbedded);
			String returnUrl = CommunicationPortalGatewayController.getWhitelistedReturnUrl(request);
			if ( returnUrl != null )
				params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_URL, returnUrl);
			boolean isReturnToList = ServletRequestUtils.getBooleanParameter(request, CommunicationPortalGatewayController.REQ_PARAM_RETURN_TO_LIST, false);
			if ( isReturnToList )
				params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_TO_LIST, isReturnToList);
		}

		return params;
	}

	private Document getDefaultTouchpoint() {
		if ( !UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_COMMUNICATIONS_VIEW) )
			return null;
		List<Document> visibleDocuments = Document.findAllDocumentsAndProjectsVisible();
		for (Document document: visibleDocuments)
			return document;
		return null;
	}

	protected static Document getDocument(HttpServletRequest request) {
		long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1);
		Document document = HibernateUtil.getManager().getObject(Document.class, id);
		return document;
	}

	public String getEditView() {
		return editView;
	}
	public void setEditView(String editView) {
		this.editView = editView;
	}

	public ModelAndView processFormSubmission(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception{
		ModelAndView mv = super.processFormSubmission(request, response, command, errors);
		return mv;
	}
}