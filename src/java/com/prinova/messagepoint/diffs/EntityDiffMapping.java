package com.prinova.messagepoint.diffs;

import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.util.LogUtil;
import org.javers.core.metamodel.annotation.Id;

import java.lang.reflect.Field;
import java.util.Map;

public abstract class EntityDiffMapping {
    @Id
    protected String id;

    public abstract <E extends IdentifiableMessagePointModel> void initProperties(E entity);

    public void customizeProperties(Map<String, Object> properties){
        if(properties != null) {
            if(properties.containsKey("id")){
                this.id = ((Long)properties.get("id")).toString();
            }

            try {
                Field[] fields = this.getClass().getDeclaredFields();
                for (Field field : fields) {
                    if (properties.containsKey(field.getName())) {
                        Object propertyValue = properties.get(field.getName());
                        field.setAccessible(true);
                        field.set(this, propertyValue != null ? propertyValue : "");
                    }
                }
            } catch (IllegalAccessException iae) {
                LogUtil.getLog(EntityDiffMapping.class).error(iae);
            }
        }
    }
}
