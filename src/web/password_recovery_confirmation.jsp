<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.MessagepointLicenceManager" %>
<%@page import="org.springframework.web.bind.ServletRequestUtils" %>
<%@page import="com.prinova.messagepoint.security.controller.PasswordRecoveryController" %>
<%@page import="com.prinova.messagepoint.MessagePointStartUp" %>
<%@page import="com.prinova.messagepoint.model.SystemPropertyKeys" %>
<%@page import="com.prinova.messagepoint.util.ApplicationUtil" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>
<%@ page import="java.net.URLEncoder" %>
<%@ page import="java.nio.charset.StandardCharsets" %>

<% pageContext.setAttribute("token", MessagepointLicenceManager.getInstance()); %>

<%@ include file="./includes/includes.jsp" %>

<%

    String proxySSOMasterPageURL = ServletRequestUtils.getStringParameter(request, PasswordRecoveryController.REQ_REDIRECTION_RD_URL_PARAM);
    if (proxySSOMasterPageURL != null)
        pageContext.setAttribute("proxySSOMasterPageURL", URLEncoder.encode(proxySSOMasterPageURL, StandardCharsets.UTF_8));

    String signinTheme = ApplicationUtil.getProperty(SystemPropertyKeys.UserInterface.KEY_SigninTheme, null);
    pageContext.setAttribute("signinTheme", signinTheme);

    String revision = MessagePointStartUp.getBuildRevision();
    revision = revision.length() <= 19 ? revision : revision.substring(0, 19);
    pageContext.setAttribute("revision", revision);
%>

<msgpt:Html5>
    <msgpt:GetUser outputName="user" scope="request"/>
    <msgpt:HeaderNew title="page.label.settings" viewType="<%= MessagepointHeader.ViewType.EDIT %>">
    </msgpt:HeaderNew>
    <msgpt:BodyNew type="minimal">
        <div class="d-table-row" style="box-sizing: border-box;">
            <div class="d-table-cell align-middle py-5">
                <div class="px-4 py-2">
                    <div class="mx-auto text-center" style="max-width: 26.25rem;">
                        <c:choose>
                            <c:when test="${signinTheme == 1}">
                                <a class="d-inline-block mb-5 pb-2" href="https://www.opentext.com"
                                   data-toggle="tooltip" data-placement="bottom" data-html="true"
                                   title="&rarr;&nbsp; ${msgpt:getMessage('page.text.visit')} opentext.com"
                                   target="_blank" rel="noopener">
                                    <img src="${contextPath}/_ux/img/brands/opentext-logo-light-theme.svg"
                                         class="d-inline-block align-middle"
                                         alt="Opentext Messagepoint Exstream &trade;" style="height: 3.5rem;"/>
                                </a>
                            </c:when>
                            <c:otherwise>
                                <a class="d-inline-block mb-5 pb-2" href="https://www.messagepoint.com"
                                   data-toggle="tooltip" data-placement="bottom" data-html="true"
                                   title="&rarr;&nbsp; ${msgpt:getMessage('page.text.visit')} messagepoint.com"
                                   target="_blank" rel="noopener">
                                    <img src="${contextPath}/_ux/img/messagepoint-logo.svg" class="d-inline-block align-middle"
                                         alt="Messagepoint &reg;"/>
                                </a>
                            </c:otherwise>
                        </c:choose>
                        <c:if test="${!empty param.msgkey}">
                            <div class="alert alert-danger alert-dismissible py-3 px-4 text-left fade show"
                                 role="alert">
                                <strong class="d-block fs-md pr-4">
                                    <i class="fas fa-minus-circle mr-2" aria-hidden="true"></i>
                                    <fmtSpring:message code="page.label.error"/>
                                </strong>
                                <fmtSpring:message code="${param.msgkey}" htmlEscape=""/>
                                <button type="button" class="close mt-2 mr-2" data-dismiss="alert" aria-label="Close">
                                    <i class="far fa-times" aria-hidden="true"></i>
                                </button>
                            </div>
                        </c:if>
                        <form:form method="post" modelAttribute="command">
                            <form:errors path="*">
                                <msgpt:Information errorMsgs="${messages}" type="error" singleLine="false"/>
                            </form:errors>
                            <div class="position-relative box-shadow-4 bg-white rounded">
                                <div class="bg-ribbon position-absolute rounded-top py-1 w-100"
                                     role="presentation"></div>
                                <div class="p-4">
                                    <h1 class="h4 text-primary px-2 mt-2 mb-3"><fmtSpring:message
                                            code="page.label.passwordrecovery"/></h1>
                                    <div class="px-2">
                                        <p class="fs-md mb-4">
                                            <b><fmtSpring:message
                                                    code="page.label.passwordrecovery.check.your.email"/></b> <br>
                                            <c:choose>
                                                <c:when test="${not empty proxySSOMasterPageURL}">
                                                    <fmtSpring:message code="page.text.passwordreset.confirmation1"/>
                                                </c:when>
                                                <c:otherwise>
                                                    <fmtSpring:message code="page.text.passwordreset.confirmation1"/>
                                                </c:otherwise>
                                            </c:choose>
                                        </p>
                                        <c:choose>
                                            <c:when test="${not empty proxySSOMasterPageURL}">
                                                <button type="button" class="btn btn-lg btn-primary btn-block mb-2"
                                                        onclick="directHref('${proxySSOMasterPageURL}/signin.jsp')"
                                                        autofocus>
                                                <span class="text-uppercase mr-2"><fmtSpring:message
                                                        code="page.label.return.to.login"/></span>
                                                    <i class="far fa-lg fa-arrow-to-left" aria-hidden="true"></i>
                                                </button>
                                            </c:when>
                                            <c:otherwise>
                                                <button type="button" class="btn btn-lg btn-primary btn-block mb-2"
                                                        onclick="directHref('${contextPath}/signin.jsp')" autofocus>
                                                <span class="text-uppercase mr-2"><fmtSpring:message
                                                        code="page.label.return.to.login"/></span>
                                                    <i class="far fa-lg fa-arrow-to-left" aria-hidden="true"></i>
                                                </button>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>
                                <div class="bg-lightest rounded-bottom py-3 px-4">
                                    <p class="px-2 mb-0 text-dark">
                                        <fmtSpring:message code="page.text.comments.and.suggestions"/>
                                    </p>
                                </div>
                            </div>
                        </form:form>
                        <div class="mt-5 pt-2 fs-xs">
                            <div class="text-dark">
                                <fmtSpring:message code="page.text.copyright.signin" arguments="${currentYear}"/>
                                <span class="ml-2">|</span>
                                <span class="ml-2">v${revision}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </msgpt:BodyNew>
</msgpt:Html5>
