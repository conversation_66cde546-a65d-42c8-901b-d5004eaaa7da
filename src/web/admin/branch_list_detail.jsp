<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>

    <msgpt:HeaderNew title="page.label.messages" extendedScripts="true">
        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>

        <style type="text/css">
            .detailsSummaryTable {
                white-space: nowrap;
                margin-left: 10px;
            }

            .detailsSummaryTable td {
                padding: 0px 8px 0px 16px;
                text-align: left;
                font-size: 11px;
            }

            .detailSummaryLeftBorder {
                border-left: 1px solid #f2f2f2;
            }
        </style>

        <msgpt:Script>
            <script>
                function pollForNodeProcessResult(nodeId) {

                    if ($('#nodeStatusPollingContainer_' + nodeId).length == 0)
                        return;

                    var stampDate = new Date();
                    $.ajax({
                        type: "GET",
                        url: context + "/getNodeProcessStatus.form?nodeId=" + nodeId + "&tk=" + getParam('tk') + "&cacheStamp=" + stampDate.getTime(),
                        dataType: "json",
                        success: function (data) {
                            processNodeResult(data);
                        }
                    });

                    window.setTimeout(function () {
                        pollForNodeProcessResult(nodeId)
                    }, 2000);
                }

                function processNodeResult(data) {
                    var nodeStatusContainer = $('#nodeStatusPollingContainer_' + data.node_id).closest('td');
                    if (data.status != "in_process") {
                        location.reload();
                    }
                }

                // Node View/Edit
                function veiwNode(nodeId) {
                    $('#nodeListItem_' + nodeId).iFramePopup({
                        width: 820,
                        src: context + "/admin/node_view.form",
                        displayOnInit: true,
                        appliedParams: {'tk': "${param.tk}", 'nodeId': nodeId},
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    });
                }


                // List actions: Edit
                function editListItem(listItemId) {
                    $('#listItem_' + listItemId).iFramePopup({
                        width: 820,
                        displayOnInit: true,
                        src: "node_edit.form",
                        appliedParams: {branchId: branchId, nodeId: "0", tk: "${param.tk}", action: "1"},
                        closeBtnId: "cancelBtn_button",
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    });
                }

                // ********** Touchpoint collection list checkbox functions ***********
                function toggleChecks() {
                    if ($("#allCheck").attr("checked")) {
                        $("input[id^='listItemCheck']").attr("checked", "checked");
                    } else {
                        $("input[id^='listItemCheck']").removeAttr("checked");
                    }
                    validateActionReq();
                }

                function iFrameAction(actionId) {
                    var nodeId = null;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        nodeId = this.id.replace('listItemCheck_', '');
                    });

                    // Edit Node
                    if (actionId == '2') {
                        $('#nodeListItem_' + nodeId).iFramePopup({
                            width: 820,
                            src: context + "/admin/node_edit.form",
                            displayOnInit: true,
                            appliedParams: {branchId: "${param.branchId}", 'tk': "${param.tk}", 'nodeId': nodeId},
                            closeBtnId: "cancelBtn_button",
                            beforePopupClose: function () {
                                location.reload();
                            },
                        });
                    }

                    // Licence manager
                    if (actionId == '11') {
                        $('#nodeListItem_' + nodeId).iFramePopup($.extend({
                            src: context + "/admin/node_licence_management.form",
                            displayOnInit: true,
                            appliedParams: {'tk': "${param.tk}", 'nodeId': nodeId},
                            beforePopupClose: function () {
                                location.reload();
                            },
                            onSave: function () {
                            }
                        }, iFramePopup_fullFrameAttr));
                    }
                }

                function validateActionReq(nodeId) {


                    var singleSelect = true;
                    var canDisableNode = true;
                    var canEnableNode = true;
                    var canInitializeNode = true;
                    var canMigrateNode = true;
                    var canLicenceNode = true;
                    var canDeleteNode = true;
                    var canCopyIntoNode = true;
                    var canMakeDefaultNode = true;

                    var mainDomain = $('#currentBranchId').val();
                    $("#domainSelect").find('option:selected').removeAttr('selected');
                    $("#domainSelect option").each(function () {
                        if ($(this).val() == mainDomain)
                            $(this).attr('selected', 'selected');
                    });

                    // Resolve selection flags
                    if ($("input[id^='listItemCheck_']:checked").length != 1) {
                        singleSelect = false;
                    }

                    $("input[id^='listItemCheck']:checked").each(
                        function () {
                            nodeId = this.id.replace('listItemCheck_', '');
                            if (!exists('canEnableNode_' + nodeId))
                                canEnableNode = false;
                            if (!exists('canDisableNode_' + nodeId))
                                canDisableNode = false;
                            if (!exists('canInitializeNode_' + nodeId))
                                canInitializeNode = false;
                            if (!exists('canMigrateNode_' + nodeId))
                                canMigrateNode = false;
                            if (!exists('canLicenceNode_' + nodeId))
                                canLicenceNode = false;
                            if (!exists('canDeleteNode_' + nodeId))
                                canDeleteNode = false;
                            if (!exists('canCopyIntoNode_' + nodeId))
                                canCopyIntoNode = false;
                            if (!exists('canMakeDefaultNode_' + nodeId))
                                canMakeDefaultNode = false;
                        }
                    );
                    //Disable all context menu entries
                    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
                    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');

                    if ($("input[id^='listItemCheck_']:checked").length > 0) {
                        if (singleSelect) {
                            $('a#actioniFrame_2').removeClass('disabled');
                            $('a#actionOption_7').show();
                            $('a#actionOption_6').hide();

                            if (canInitializeNode) {
                                $('a#actionOption_3').removeClass('disabled'); // Initialize Node
                            }
                            if (canMigrateNode) {
                                $('a#actionOption_4').removeClass('disabled'); // Migrate Node
                            }
                            if (canEnableNode) {
                                $('a#actionOption_7').removeClass('disabled'); // Enable Node
                            }
                            if (canDisableNode) {
                                $('a#actionOption_7').hide();
                                $('a#actionOption_6').show();
                                $('a#actionOption_6').removeClass('disabled'); // Disable Node
                            }
                            if (canLicenceNode) {
                                $('a#actioniFrame_11').removeClass('disabled');
                            }
                            if (canDeleteNode) {
                                $('a#actionOption_5').removeClass('disabled'); // Delete Node
                            }
                            if (canCopyIntoNode) {
                                $('a#actionOption_12').removeClass('disabled'); // Copy Node
                            }
                            if (canMakeDefaultNode) {
                                $('a#actionOption_14').removeClass('disabled'); // Make default Node
                            }
                        }
                    }
                }


                // Retrieve selected ids seperated by ','
                function getSelectedIds() {
                    var selectedIds = '';
                    var selectedCount = $("input[id^='listItemCheck']:checked").length;
                    var count = 0;
                    $("input[id^='listItemCheck']:checked").each(
                        function () {
                            selectedIds += this.id.replace('listItemCheck_', '');
                            count++;
                            if (count != selectedCount)
                                selectedIds += '_';
                        }
                    );
                    return selectedIds;
                }

                function domainSelection() {
                    var domainId = $('#domainSelect').val();
                    var itemId = $("input[id^='listItemCheck_']:checked:first").attr('id').replace('listItemCheck_', '');
                    var type = 'copyFromInstance';

                    var stampDate = new Date();
                    $.ajax({
                        type: "GET",
                        url: context + "/getDataForSelectMenu.form?contentObjectId=" + itemId + "&type=" + type + "&domainId=" + domainId + "&cacheStamp=" + (stampDate.getTime()),
                        dataType: "xml",
                        success: function (data) {
                            initDataSelect(data);
                        }
                    });
                }

                function toggleCopyFromNodeWarning(ele) {
                    if($(ele).prop('checked') == true) {
                        $("#copyDataWarning").hide();
                        $("#createHistoryWarning").show();
                    } else {
                        $("#copyDataWarning").show();
                        $("#createHistoryWarning").hide();
                    }
                }

                // ************ INIT: START *********************
                $(function () {
                    $("input:button").styleActionElement();

                    // Node processing result polling init
                    $("[id^='nodeStatusPollingContainer_']").each(function () {
                        if ($(this).attr('pollingInit') == undefined || $(this).attr('pollingInit') != "true") {
                            $(this).attr('pollingInit', 'true');
                            pollForNodeProcessResult(parseId(this));
                        }
                    });

                    adjustIFrameHeight(frameElement);

                });

            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <msgpt:BodyNew type="minimal" cssClass="bg-transparent" cssStyle="min-height: 450px;">
        <c:set var="editNodePerm" value="false" scope="request"/>
        <msgpt:IfAuthGranted authority="ROLE_MASTER_USER">
            <c:set var="editNodePerm" value="true"/>
        </msgpt:IfAuthGranted>
        <form:form method="post" modelAttribute="command">

            <form:errors path="*">
                <msgpt:Information errorMsgs="${messages}" type="error"/>
            </form:errors>

            <form:hidden path="actionValue" id="actionElement"/>
            <c:if test="${branch != null}">
                <input id="currentBranchId" type="hidden" value="${branch.id}"/>
            </c:if>

            <c:if test="${branch == null}">
                <input id="currentBranchId" type="hidden" value="0"/>
            </c:if>

            <msgpt:DataTable multiSelect="true" id="nodeList" staticData="true">
                <c:forEach var="currentNode" items="${nodes}" varStatus="forStatus">
                    <msgpt:TableListGroup id="nodeListItem_${currentNode.id}" style="">
                        <!-- Name -->
                        <msgpt:TableElement label="page.label.name" align="left" style="padding-left: 15px;">
                            <a id="nodeListBinding_${currentNode.id}"
                               href="javascript:veiwNode(${currentNode.id});"><c:out value="${currentNode.name}"/></a>
                            <input id="listItemCheck_${currentNode.id}" type="checkbox"
                                   value="${currentNode.id}" style="display : none;" name="selectedIds">
                            <input type="hidden" value="on" name="_selectedIds">
                            <c:if test="${currentNode.status != 1  && currentNode.status != 3 && currentNode.schemaName != null}">
                                <input id="canInitializeNode_${currentNode.id}" type="hidden">
                                <c:if test="${ currentNode.status != 0 }">
                                    <input id="canMigrateNode_${currentNode.id}" type="hidden">
                                </c:if>
                                <input id="canDeleteNode_${currentNode.id}" type="hidden">
                            </c:if>
                            <c:if test="${currentNode.status == 2  || currentNode.status == 3}">
                                <input id="canLicenceNode_${currentNode.id}" type="hidden">
                            </c:if>
                            <c:if test="${currentNode.status == 3  }">
                                <input id="canDisableNode_${currentNode.id}" type="hidden">
                            </c:if>
                            <c:if test="${currentNode.status == 2  }">
                                <input id="canEnableNode_${currentNode.id}" type="hidden">
                            </c:if>
                            <c:if test="${currentNode.status == 2 || currentNode.status == 3}">
                                <input id="canMakeDefaultNode_${currentNode.id}" type="hidden">
                            </c:if>
                            <c:if test="${currentNode.status == 2  }">
                                <input id="canCopyIntoNode_${currentNode.id}" type="hidden">
                            </c:if>
                        </msgpt:TableElement>
                        <msgpt:TableElement label="page.label.friendly.name" align="left"
                                            style="padding-left: 15px;"><c:out value="${currentNode.friendlyName}"/>
                        </msgpt:TableElement>
                        <!-- Enabled -->
                        <msgpt:TableElement align="left" label="page.label.status" style="padding-left: 15px;">
                            <c:choose>
                                <c:when test="${currentNode.status == 0}"><fmtSpring:message
                                        code="page.label.node.status.not.validated"/></c:when>
                                <c:when test="${currentNode.status == 1}">
                                    <div id="nodeStatusPollingContainer_${currentNode.id}"><fmtSpring:message
                                            code="page.label.node.status.initialization.in.progress"/></div>
                                </c:when>
                                <c:when test="${currentNode.status == 2}"><fmtSpring:message
                                        code="page.label.node.status.off.line"/></c:when>
                                <c:when test="${currentNode.status == 3}"><fmtSpring:message
                                        code="page.label.node.status.on.line"/></c:when>
                                <c:when test="${currentNode.status == 4}"><fmtSpring:message
                                        code="page.label.node.status.incorrect.version"/></c:when>
                                <c:when test="${currentNode.status == 5}"><fmtSpring:message
                                        code="page.label.node.status.db.schema.empty"/></c:when>
                                <c:when test="${currentNode.status == 6}"><fmtSpring:message
                                        code="page.label.node.status.cannot.open.db.schema"/></c:when>
                                <c:when test="${currentNode.status == 7}"><fmtSpring:message
                                        code="page.label.node.status.initialization.error"/></c:when>
                                <c:when test="${currentNode.status == 8}"><fmtSpring:message
                                        code="page.label.node.status.unknown.database.error"/></c:when>
                                <c:when test="${currentNode.status == 9}">
                                    <div id="nodeStatusPollingContainer_${currentNode.id}"><fmtSpring:message
                                            code="page.label.node.status.upgrade.in.progress"/></div>
                                </c:when>
                                <c:when test="${currentNode.status == 10}"><fmtSpring:message
                                        code="page.label.node.status.upgrade.error"/></c:when>
                                <c:otherwise><fmtSpring:message code="page.label.node.status.unknown"/></c:otherwise>
                            </c:choose>
                        </msgpt:TableElement>
                        <msgpt:TableElement align="left" label="page.label.type" style="padding-left: 15px;">
                            <c:choose>
                                <c:when test="${currentNode.nodeType == -2}"><fmtSpring:message
                                        code="page.label.exchange"/></c:when>
                                <c:when test="${currentNode.nodeType == -1}"><fmtSpring:message
                                        code="page.label.domain.controller"/></c:when>
                                <c:when test="${currentNode.nodeType == 0}"><fmtSpring:message
                                        code="page.label.production"/></c:when>
                                <c:when test="${currentNode.nodeType == 1}"><fmtSpring:message
                                        code="page.label.sandbox"/></c:when>
                                <c:otherwise><fmtSpring:message code="page.label.testing"/></c:otherwise>
                            </c:choose>
                        </msgpt:TableElement>
                        <msgpt:TableElement label="page.label.default" align="left" style="padding-left: 15px;">
                            <c:if test="${currentNode.isDefaultNode}">
                                <div class="checkmarkIcon dataTable_icon_align"/>
                            </c:if>
                        </msgpt:TableElement>
                    </msgpt:TableListGroup>
                </c:forEach>

            </msgpt:DataTable>

            <!-- SPACER: for menus and popups -->
            <div style="width: 1px; height: 264px;"></div>

            <!-- POPUP DATA -->
            <div id="actionSpecs" style="display: none;">
                <!-- ACTIONS POPUP DATA -->
                <div id="actionSpec_3" submitId="3"> <!-- Initialization -->
                    <div id="actionTitle_3"><fmtSpring:message code="page.label.confirm.validate.and.initialize"/></div>
                    <div id="actionInfo_3"><p><fmtSpring:message
                            code="page.text.validate.selected.node"/></p></div>
                    <div id="actionActivateOptions_3"></div>
                </div>
                <div id="actionSpec_4" submitId="4"> <!-- Upgrade -->
                    <div id="actionTitle_4"><fmtSpring:message code="page.label.confirm.upgrade.instance"/></div>
                    <div id="actionInfo_4"><p><fmtSpring:message
                            code="page.text.upgrade.folder.locations.for.node"/></p></div>
                </div>
                <div id="actionSpec_5" submitId="5"> <!-- Delete Node -->
                    <div id="actionTitle_5"><fmtSpring:message code="page.label.confirm.delete"/></div>
                    <div id="actionInfo_5"><p><fmtSpring:message code="page.text.do.you.want.to.delete.node"/></p></div>
                    <div id="actionDeleteNodeOptions_5"></div>
                </div>
                <div id="actionSpec_6" submitId="6"> <!-- Disable (Off-line) -->
                    <div id="actionTitle_6"><fmtSpring:message code="page.label.confirm.off.line"/></div>
                    <div id="actionInfo_6"><p style="color: #944;"><fmtSpring:message
                            code="page.text.turn.node.off.line"/><br><fmtSpring:message code="page.text.are.you.sure"/>
                    </p>
                    </div>
                </div>
                <div id="actionSpec_7" submitId="7"> <!-- Activate -->
                    <div id="actionTitle_7"><fmtSpring:message code="page.label.confirm.on.line"/></div>
                    <div id="actionInfo_7"><p><fmtSpring:message code="page.text.turn.node.online"/></p></div>
                </div>
                <div id="actionSpec_12" submitId="12"> <!-- Copy data from -->
                    <div id="actionTitle_12"><fmtSpring:message code="page.label.confirm.copy.data"/></div>
                    <div id="actionInfo_12"><p><fmtSpring:message code="page.text.copy.data.from.node"/></p></div>
                    <div id="actionUserSelect_12" required="true" type="copyFromInstance"></div>
                </div>
            </div>
            <!-- END: POPUP DATA -->

            <!-- POPUP INTERFACE -->
            <msgpt:Popup id="actionPopup" theme="minimal">
                <div id="actionPopupInfoFrame">
                    <div id="actionPopupInfo">&nbsp;</div>
                </div>
                <div id="actionPopupActivateOptions">
                    <p><b>
                        <fmtSpring:message code="page.text.erase.db.schema"/>: <form:checkbox
                            id="rebuildDBNodeSchemaCheckbox" path="rebuildDBNodeSchema" cssClass="checkbox"
                            cssStyle="padding-left: 10px;"/></b>
                    </p>
                    <p>
                        <b style="color: #944;"><fmtSpring:message code="page.text.all.node.data.will.be.erased"/></b>
                    </p>
                </div>
                <div id="actionPopupDeleteNodeOptions">
                    <p>
                        <fmtSpring:message code="page.text.delete.db.schema"/>:
                        <form:checkbox id="rebuildDBNodeSchemaCheckbox" path="rebuildDBNodeSchema"
                                       cssClass="checkbox" cssStyle="padding-left: 10px;"/>
                    </p>
                    <p>
                        <b style="color: #944;"><fmtSpring:message code="page.text.all.node.data.will.be.erased"/></b>
                    </p>
                </div>
                <div id="actionPopupUserSelect">
                    <table cellspacing="0" cellpadding="0" border="0">
                        <tr>
                            <td style="color: #444; vertical-align: middle;"><fmtSpring:message
                                    code="page.label.domain"/>: : &nbsp;
                            </td>
                            <td>
                                <select id="domainSelect" onchange="domainSelection()" cssClass="inputL">
                                    <c:forEach var="domain" items="${domains}">
                                        <option value="${domain.id}">${domain.name}</option>
                                    </c:forEach>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td style="color: #444; vertical-align: middle;"><fmtSpring:message
                                    code="page.label.instance"/>: : &nbsp;
                            </td>
                            <td>
                                <form:select id="userSelect" path="copyFromInstance" cssClass="inputL"
                                             onchange="validatePopupReq()" onkeyup="validatePopupReq()">
                                    <option id="0" value="0"><fmtSpring:message code="page.text.loading"/></option>
                                </form:select>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="vertical-align: middle;">
                                <p>
                                    <fmtSpring:message code="page.text.create.sync.history.only"/>:
                                    <form:checkbox id="createSyncHistoryOnlyCheckbox" path="createSyncHistoryOnly"
                                                   cssClass="checkbox" cssStyle="padding-left: 10px;"
                                                   onchange="javascript:toggleCopyFromNodeWarning(this);"
                                    />
                                </p>
                            </td>
                        </tr>
                    </table>
                    <div id="copyDataWarning">
                        <p><fmtSpring:message code="page.text.copy.data.from.node.warning"/></p>
                    </div>
                    <div id="createHistoryWarning" style="display: none">
                        <p><fmtSpring:message code="page.text.create.sync.history.only.warning"/></p>
                    </div>
                </div>
                <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                    <span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#" label="page.label.cancel"
                                                                                      disabled="true"/></span>
                    <span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                              label="page.label.cancel"/></span>
                    <span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                        label="page.label.continue"
                                                                                        disabled="true"/></span>
                    <span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue"
                                                                primary="true"/></span>
                </div>
                <div id="actionPopupCloseButton" class="actionPopupButtonsContainer">
                    <msgpt:Button URL="javascript:actionCancel();" label="page.label.close"/>
                </div>
            </msgpt:Popup>

            <!-- END: POPUP INTERFACE -->

        </form:form>
        <msgpt:ContextMenu name="nodeList">
            <c:if test="${editNodePerm}">
                <msgpt:ContextMenuEntry name="actioniFrame_2" link="#iFrameAction:2"><fmtSpring:message
                        code="page.label.edit"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="actioniFrame_11" link="#iFrameAction:11"><fmtSpring:message
                        code="page.label.feature.activation"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="actionOption_3" link="#actionSelected:3"><fmtSpring:message
                        code="page.label.validate.initialize"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="actionOption_4" link="#actionSelected:4"><fmtSpring:message
                        code="page.label.upgrade.instance"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="actionOption_7" link="#actionSelected:7"><fmtSpring:message
                        code="page.label.enable.online"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="actionOption_6" link="#actionSelected:6"><fmtSpring:message
                        code="page.label.disable.offline"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="actionOption_5" link="#actionSelected:5"><fmtSpring:message
                        code="page.label.delete"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="actionOption_14" link="#actionSelected:14"><fmtSpring:message
                        code="page.label.make.default"/></msgpt:ContextMenuEntry>
            </c:if>
            <c:if test="${copyNodePerm}">
                <msgpt:ContextMenuEntry name="actionOption_12" link="#actionSelected:12"><fmtSpring:message
                        code="page.label.copy.data.from"/></msgpt:ContextMenuEntry>
            </c:if>
        </msgpt:ContextMenu>
    </msgpt:BodyNew>
</msgpt:Html5>