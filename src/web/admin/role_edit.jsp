<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.MessagepointLicenceManager"%>
<%@page import="java.text.MessageFormat"%>
<%@page import="com.prinova.messagepoint.util.ApplicationUtil"%>
<%@page import="com.prinova.messagepoint.controller.admin.RoleEditController.RolePermissionsCommand"%>
<%@page import="com.prinova.messagepoint.model.security.Permission"%>
<%@page import="com.prinova.messagepoint.controller.admin.RoleEditController"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<% pageContext.setAttribute("token", MessagepointLicenceManager.getInstance()); %>

<%@ include file="../includes/includes.jsp" %>

<c:choose>
    <c:when test="${command.mode == '2'}">
        <c:set var="roleName" value="${command.name}" />
        <c:set var="contentDataTitle" value='<%= MessageFormat.format(ApplicationUtil.getMessage("page.label.admin.role.updaterole"),pageContext.getAttribute("roleName")) %>' />
    </c:when>
    <c:otherwise>
        <c:set var="contentDataTitle" value='${msgpt:getMessage("page.label.admin.role.createnewrole")}' />
    </c:otherwise>
</c:choose>

<msgpt:Html5>

<msgpt:HeaderNew title="page.label.admin" viewType="<%= MessagepointHeader.ViewType.EDIT %>">
	<msgpt:Script>
	<script>

		function onCheckboxClick(checkbox) {
			var PERM_VIEW 		= '0';
			var PERM_EDIT 		= '1';
			var PERM_APPROVE 	= '2';
			var PERM_REASSIGN	= '3';
			var PERM_SETUP		= '4';

			var checkAttrArray 	= $(checkbox).attr('id').split('_');
			var permType 		= checkAttrArray[2];
			var permIndex 		= checkAttrArray[1];
			var idPrefix		= checkAttrArray[0];
			var isChecked		= $(checkbox).is(':checked');

			if (permType == PERM_VIEW) {
				if (!isChecked) {
					$('#'+idPrefix+'_'+permIndex+'_'+PERM_EDIT).removeAttr('checked');
					$('#'+idPrefix+'_'+permIndex+'_'+PERM_APPROVE).removeAttr('checked');
					$('#'+idPrefix+'_'+permIndex+'_'+PERM_REASSIGN).removeAttr('checked');
					$('#'+idPrefix+'_'+permIndex+'_'+PERM_SETUP).removeAttr('checked');
				}
			} else {
				if (isChecked)
					$('#'+idPrefix+'_'+permIndex+'_'+PERM_VIEW).attr('checked','checked');
			}
		}


	
		//FogBugz#82/Mantis #6403 - Ability to "Select All" when creating/editing roles 
		function reflectCheckAllsProperly() 
		{				
			var column			= '0';						
			var enableCheckall	= false;
			var checkAttrArray 	= '';
			var permCol 		= '';
			var permIdPrefix	= '';	

                var i = 0;
                for (i = 0; i <= 4; i++)
                {
                    $('input.checkbox').each(function () {
                        checkAttrArray 	= $(this).attr('id').split('_');
                        permCol 		= checkAttrArray[2];
                        permIdPrefix	= checkAttrArray[0];

                        column = i;
                        if (permIdPrefix == 'permCheck' && permCol == column)
                        {
                            if ($(this).is(':checked'))
                            {
                                enableCheckall = true;
                            }
                            else
                            {
                                enableCheckall = false;
                                return false;
                            }
                        }
                    });

                    if (enableCheckall)
                    {
                        if (column == '1')
                            $('input#updateCheckAll').attr('checked', 'checked');
                        else if (column == '0')
                            $('input#viewCheckAll').attr('checked', 'checked');
                        else if (column == '2')
                            $('input#approvalCheckAll').attr('checked', 'checked');
                        else if (column == '3')
                            $('input#reassignCheckAll').attr('checked', 'checked');
                        else if (column == '4')
                            $('input#setupCheckAll').attr('checked', 'checked');
                    }
                    else
                    {
                        if (column == '1')
                            $('input#updateCheckAll').removeAttr('checked');
                        else if (column == '0')
                            $('input#viewCheckAll').removeAttr('checked');
                        else if (column == '2')
                            $('input#approvalCheckAll').removeAttr('checked');
                        else if (column == '3')
                            $('input#reassignCheckAll').removeAttr('checked');
                        else if (column == '4')
                            $('input#setupCheckAll').removeAttr('checked');
                    }
                }
            }

            //FogBugz#82/Mantis #6403 - Ability to "Select All" when creating/editing roles
            $( function() {
                reflectCheckAllsProperly();
            });

            //FogBugz#82/Mantis #6403 - Ability to "Select All" when creating/editing roles
            function checkAllPermsInCol(checkbox)
            {
                var checkAllBoxId = $(checkbox).attr('id');
                var column = '0';

                if (checkAllBoxId.indexOf('update') != -1)
                    column = '1';
                else if (checkAllBoxId.indexOf('view') != -1)
                    column = '0';
                else if (checkAllBoxId.indexOf('approval') != -1)
                    column = '2';
                else if (checkAllBoxId.indexOf('reassign') != -1)
                    column = '3';
                else if (checkAllBoxId.indexOf('setup') != -1)
                    column = '4';

                var isChecked = $(checkbox).is(':checked');
                var checkAttrArray 	= '';
                var permCol 		= '';
                var permRow 		= '';
                var permIdPrefix	= '';
                var viewCheckboxId  = '0';

                $('input.checkbox').each(function () {
                    checkAttrArray 	= $(this).attr('id').split('_');
                    permCol 		= checkAttrArray[2];
                    permRow 		= checkAttrArray[1];
                    permIdPrefix	= checkAttrArray[0];

                    if (permIdPrefix == 'permCheck' && (permCol == column))
                    {
                        if (isChecked)
                        {
                            $(this).attr('checked', 'checked');
                            viewCheckboxId = '_' + permRow + '_0';
                            $('input#permCheck' + viewCheckboxId).attr('checked', 'checked');
                        }
                        else
                        if (!$(this).attr('disabled'))
                            $(this).removeAttr('checked');
                    }
                });

                if (column == '0' && !isChecked)
                {
                    $('input#updateCheckAll').removeAttr('checked');
                    $('input#approvalCheckAll').removeAttr('checked');
                    var updateOrApprovalCheckboxId = '0';

                    $('input.checkbox').each(function () {
                        checkAttrArray 	= $(this).attr('id').split('_');
                        permCol 		= checkAttrArray[2];
                        permRow 		= checkAttrArray[1];
                        permIdPrefix	= checkAttrArray[0];

                        if (permIdPrefix == 'permCheck' && permCol == '0')
                        {
                            if (!$(this).attr('disabled'))
                            {
                                $(this).removeAttr('checked');
                                updateOrApprovalCheckboxId = '_' + permRow;
                                $('input#permCheck' + updateOrApprovalCheckboxId + '_1').removeAttr('checked');
                                $('input#permCheck' + updateOrApprovalCheckboxId + '_2').removeAttr('checked');
                            }
                        }
                    });
                }
            }

        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:BodyNew theme="minimal" type="iframe">


	<div id="popupHeaderTitle" style="display: none;">
		<span class="titleText">
			<c:out value='${contentDataTitle}' />
		</span>
	</div>
	
	<form:form cssClass="formNoMargin" name="editForm">

		<msgpt:iFrameContainer>
			<msgpt:ContentPanel>
				<form:errors path="*">
					<msgpt:Information errorMsgs="${messages}" type="error" />
				</form:errors>

				<!-- Role Overview -->
				<msgpt:DataTable labelPosition="top" multiColumn="true">
					<msgpt:TableHeader label="page.title.role.overview" />
					<!-- Role Name -->
					<msgpt:TableItem label="page.label.rolename">
						<msgpt:InputFilter type="simpleName">
							<form:input path="name" cssClass="inputXL" maxlength="96"/>
						</msgpt:InputFilter>
					</msgpt:TableItem>
					<!-- Role Description -->
					<msgpt:TableItem label="page.label.role.description">
						<msgpt:InputFilter type="description">
							<form:textarea path="description" cssClass="inputXL" rows="3" />
						</msgpt:InputFilter>
					</msgpt:TableItem>
					<!-- Role Visibility -->
					<msgpt:TableItem label="page.label.role.visibility">
						<form:select path="visibility" >
							<option value="1" <c:if test="${command.visibility == 1}">selected='selected'</c:if>><fmtSpring:message code="page.label.role.visibility.shared" /></option>
							<option value="2" <c:if test="${command.visibility == 2}">selected='selected'</c:if>><fmtSpring:message code="page.label.role.visibility.private" /></option>
						</form:select>
					</msgpt:TableItem>
				</msgpt:DataTable>


				<c:set var="permissionCategoriesIndex" value="0" scope="page" />


				<h6 class="my-4 text-uppercase">${msgpt:getMessage('page.label.select.all')}</h6>
				<!-- Select All -->
				<table width="100%" style="border-collapse: collapse; margin-bottom: 16px">
					<thead>
						<tr style="text-align: center; line-height: 38px">
							<td style="border: 1px  solid rgb(218, 218, 218)" width="25%">&nbsp;</td>
							<td style="border: 1px  solid rgb(218, 218, 218)" width="15%"><strong>${msgpt:getMessage(command.permissionTypes[0])}</strong></td>
							<td style="border: 1px  solid rgb(218, 218, 218)" width="15%"><strong>${msgpt:getMessage(command.permissionTypes[1])}</strong></td>
							<td style="border: 1px  solid rgb(218, 218, 218)" width="15%"><strong>${msgpt:getMessage(command.permissionTypes[2])}</strong></td>
							<td style="border: 1px  solid rgb(218, 218, 218)" width="15%"><strong>${msgpt:getMessage(command.permissionTypes[3])}</strong></td>
							<td style="border: 1px  solid rgb(218, 218, 218)" width="15%"><strong>${msgpt:getMessage(command.permissionTypes[4])}</strong></td>
						</tr>
					</thead>
					<tbody>
						<tr style="background-color: rgb(240, 240, 240); text-align: center; line-height: 38px; border: 1px  solid rgb(218, 218, 218)">
							<td style="border: 1px  solid rgb(218, 218, 218)">&nbsp;</td>
							<td style="border: 1px  solid rgb(218, 218, 218)"><input id="viewCheckAll" type="checkbox" class="checkbox" onclick='javascript:checkAllPermsInCol(this)' /></td>
							<td style="border: 1px  solid rgb(218, 218, 218)"><input id="updateCheckAll" type="checkbox" class="checkbox" onclick='javascript:checkAllPermsInCol(this); javascript:reflectCheckAllsProperly(this);' /></td>
							<td style="border: 1px  solid rgb(218, 218, 218)"><input id="approvalCheckAll" type="checkbox" class="checkbox" onclick='javascript:checkAllPermsInCol(this); javascript:reflectCheckAllsProperly(this);' /></td>
							<td style="border: 1px  solid rgb(218, 218, 218)"><input id="reassignCheckAll" type="checkbox" class="checkbox" onclick='javascript:checkAllPermsInCol(this); javascript:reflectCheckAllsProperly(this);' /></td>
							<td style="border: 1px  solid rgb(218, 218, 218)"><input id="setupCheckAll" type="checkbox" class="checkbox" onclick='javascript:checkAllPermsInCol(this); javascript:reflectCheckAllsProperly(this);' /></td>
						</tr>
					</tbody>
				</table>


				<!-- Category Groups -->
				<c:forEach var="categoryGroupRow" items="${command.categoryGroups}" varStatus="categoryGroupStatus">

					<c:set var="categoryGroupHasPermission" value="false" />
					<c:forEach var="permissionCategory" items="${categoryGroupRow.permissionCategories}" varStatus="permissionCategoryStatus">
						<c:forEach var="permissionValue" items="${command.permissionValues[permissionCategoriesIndex + permissionCategoryStatus.index]}" varStatus="columnStatus">
							<c:if test="${permissionValue != 0}">
								<c:set var="categoryGroupHasPermission" value="true" />
							</c:if>
						</c:forEach>
					</c:forEach>

					<c:if test="${categoryGroupHasPermission}">

						<c:set var="displayPermissionRow" value="false" />
						<c:forEach var="permissionValue" items="${command.permissionValues[permissionCategoriesIndex]}" varStatus="columnStatus">
							<c:if test="${permissionValue != 0}">
								<c:set var="displayPermissionRow" value="true" />
							</c:if>
						</c:forEach>

						<c:if test="${displayPermissionRow}">
							<!--  Role Permissions -->
							<h6 class="mt-5 mb-4 text-uppercase">${msgpt:getMessage(categoryGroupRow.name)}</h6>
							<table class="contentTable" style="width: 100%; margin-bottom: 25px; border-collapse: collapse;">
								<c:forEach var="permissionCategoriesRow" items="${categoryGroupRow.permissionCategories}" varStatus="rowStatus">
									<tr style="line-height: 38px; text-align: center; border: 1px  solid rgb(218, 218, 218); background-color: ${rowStatus.index % 2 == 0 ? "white" : "rgb(245, 245, 245)"}">
									<td width="25%" style="text-align: center">
										<fmtSpring:message code="${permissionCategoriesRow.name}"/>
									</td>
									<c:forEach var="permissionValue" items="${command.permissionValues[permissionCategoriesIndex]}" varStatus="columnStatus">
										<td width="15%" style="border: 1px  solid rgb(218, 218, 218)">
											<c:choose>
												<c:when test="${permissionValue != 0}">
													<form:checkbox id="permCheck_${permissionCategoriesIndex}_${columnStatus.index}" cssClass="checkbox" path="selectedPermissions[${permissionCategoriesIndex}][${columnStatus.index}]" onclick="javascript:onCheckboxClick(this); javascript:reflectCheckAllsProperly(this);" />
												</c:when>
												<c:otherwise>
													&nbsp;
												</c:otherwise>
											</c:choose>
										</td>
									</c:forEach>
									</tr>
									<c:set var="permissionCategoriesIndex" value="${permissionCategoriesIndex + 1}" scope="page"/>
								</c:forEach>
							</table>
						</c:if>
					</c:if>
					<c:if test="${categoryGroupHasPermission != true}">
						<c:forEach var="permissionCategoriesRow" items="${categoryGroupRow.permissionCategories}" varStatus="permissionCategoriesStatus">
							<c:set var="permissionCategoriesIndex" value="${permissionCategoriesIndex + 1}" scope="page"/>
						</c:forEach>
					</c:if>


				</c:forEach>

				<msgpt:FlowLayout align="center">
					<msgpt:FlowLayoutItem>
						<msgpt:Button label="page.label.cancel" URL="javascript:closeIframe()" />
					</msgpt:FlowLayoutItem>
					<msgpt:FlowLayoutItem>
						<msgpt:Button label="page.label.save" URL="javascript:doSubmit('submit')" primary="true" flowControl="true" icon="fa-save" />
					</msgpt:FlowLayoutItem>
				</msgpt:FlowLayout>
			</msgpt:ContentPanel>
		</msgpt:iFrameContainer>



	</form:form>
	
</msgpt:BodyNew>
</msgpt:Html5>
