<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.rationalizer" extendedScripts="true">

        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:CalendarIncludes/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/scrollTo/jquery.scrollTo-2.1.2.min.js"/>

        <msgpt:Script src="includes/javascript/handlebars/handlebars-v4.7.8.js"/>
        <msgpt:Stylesheet href="includes/javascript/contentCompare/css/diff.css" />

        <style>
            .similarities-selected {
                color: #7c1a87;
                font-weight: bold;
                cursor: pointer;
            }

            .similarities-not-selected {
                cursor: pointer;
            }

            .duplicates-selected {
                color: #7c1a87;
                font-weight: bold;
                cursor: pointer;
            }

            .duplicates-not-selected {
                cursor: pointer;
            }

            .toolbarContentSection {
                width: 1240px;
                max-height: 300px;
                z-index: 1;
                overflow-wrap: anywhere;
                overflow-y: scroll;
            }

            div.dataTables_wrapper {
                background-color: #fff;
                border-left: 1px solid #eee;
                border-right: 1px solid #eee;
            }

        </style>

        <msgpt:Script>
            <script>
                var iFramePopup_fullFrameAttr_cust = JSON.parse(JSON.stringify(iFramePopup_fullFrameAttr));
                iFramePopup_fullFrameAttr_cust.width = 1240;

                function iFrameView(path, eleId, e) {
                    if (e != undefined) {
                        if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
                            $("#" + eleId).iFramePopup($.extend({
                                src: path,
                                displayOnInit: true,
                                id: "rationalizerDocumentFrame",
                                screenMask: false,
                                appliedParams: {tk: "${param.tk}"},
                                beforePopupClose: function () {
                                    //rebuildListTable();
                                }
                            }, iFramePopup_fullFrameAttr_cust));
                        }
                        e.preventDefault ? e.stopPropagation() : e.returnValue = false;
                    }
                }

                function rebuildListTable(maintainCurrentPage) {
                    $('#rationalizerConsolidate').DataTable().ajax.reload(null, !maintainCurrentPage);
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.columns = [
                        {
                            columnMap: 'name',
                            columnName: client_messages.text.content,
                            sort: false,
                            width: '75%'
                        },
                        {
                            columnMap: 'countDisplayString',
                            sort: false,
                            width: '15%'
                        },
                        {
                            columnMap: 'pinItemHtmlString',
                            sort: false,
                            width: '10%'
                        }
                    ];

                    return obj;
                }

                function getAsyncExtParams() {
                    var stampDate = new Date();
                    var obj = [
                        {"name": "listTableType", "value": "66"},
                        {
                            "name": "rationalizerContentId",
                            "value": getParam('rationalizerContentId') != "" ? getParam('rationalizerContentId') : -1
                        },
                        {
                            "name": "rationalizerConsolidationTabId",
                            "value": getSelectedTabId()
                        },
                        {"name": "displayMode", "value": "full"},
                        {"name": "cacheStamp", "value": stampDate.getTime()}
                    ];
                    return obj;
                }

                function postListDrawCallback(nTable) {
                    $(nTable).find('.dataTableLink,.dataTableSecondaryLink').each(function () {
                        // Content:  Init content popup
                        var instId = parseId($(this));
                    });
                    $(nTable).find('[data-toggle="tooltip"]').tooltip();

                    $('#toggleMarkupBtn').removeClass('fa-toggle-on').addClass('fa-toggle-off');
                    setShowDiffrencesOff();
                }

                function postListRenderFlagInjection(oObj, nTable) {
                    var binding = oObj.aData.binding;
                    var text = oObj.aData.name;

                    // Selection check select
                    text += binding;

                    return text;
                }

                function toggleMarkupBtn() {
                    if ($('#toggleMarkupBtn').is('.fa-toggle-off')) {
                        setShowDiffrencesOn();
                    } else {
                        setShowDiffrencesOff();
                    }
                }

                function setShowDiffrencesOn() {
                    $('#toggleMarkupBtn').removeClass('fa-toggle-off').addClass('fa-toggle-on');

                    $('.raw-content').each(function () {
                        $(this).hide();
                    });
                    $('.raw-content-collapsed').each(function () {
                        $(this).hide();
                    });
                    $('.markup-content').each(function () {
                        $(this).show();
                    });
                }
                function setShowDiffrencesOff() {
                    $('#toggleMarkupBtn').removeClass('fa-toggle-on').addClass('fa-toggle-off');
                    if ($('#collapseAll').is(":checked")) {
                        $('.raw-content-collapsed').each(function () {
                            $(this).show();
                        });
                    } else {
                        $('.raw-content').each(function () {
                            $(this).show();
                        });
                    }
                    $('.markup-content').each(function () {
                        $(this).hide();
                    });
                }

                function getSelectedTabId() {
                    if ($('#similarities-tab').is('.similarities-selected')) {
                        return 0;
                    } else {
                        return 1;
                    }
                }

                function getViewType() {
                    if ($('#similarities-tab').is('.similarities-selected')) {
                        return "similarities";
                    } else {
                        return "duplicates";
                    }
                }

                $('#similarities-tab').click(function () {
                    $('#similarities-tab').removeClass('similarities-not-selected');
                    $('#similarities-tab').addClass('similarities-selected');

                    $('#duplicates-tab').removeClass('duplicates-selected');
                    $('#duplicates-tab').addClass('duplicates-not-selected');

                    $('#toggleMarkupBtn').removeClass('fa-toggle-off').addClass('fa-toggle-on');

                    $('#duplicates-found-count').hide();
                    $('#similarities-found-count').show();

                    rebuildListTable(true);
                });

                $('#duplicates-tab').click(function () {
                    $('#duplicates-tab').removeClass('duplicates-not-selected');
                    $('#duplicates-tab').addClass('duplicates-selected');

                    $('#similarities-tab').removeClass('similarities-selected');
                    $('#similarities-tab').addClass('similarities-not-selected');

                    $('#toggleMarkupBtn').removeClass('fa-toggle-on').addClass('fa-toggle-off');

                    $('#similarities-found-count').hide();
                    $('#duplicates-found-count').show();

                    rebuildListTable(true);
                });

                // Retrieve selected ids seperated by ','
                function getSelectedIds() {
                    var selectedIds = '';
                    var selectedCount = $("input[id^='listItemCheck']:checked").length;
                    var count = 0;
                    $("input[id^='listItemCheck']:checked").each(
                        function () {
                            selectedIds += this.id.replace('listItemCheck_', '');
                            count++;
                            if (count != selectedCount)
                                selectedIds += '_';
                        }
                    );

                    return selectedIds;
                }

                function getSelectAllPages() {
                    if ($('#select_all_rationalizerConsolidate').hasClass('fa-angle-down')) {
                        return false;
                    }

                    $("input[id^='listItemCheck']").each(
                        function () {
                            if ($(this).prop('checked') == false) {
                                return false;
                            }
                        }
                    );

                    return true;
                }

                function getGuidForAllMatchedIds() {
                    if (getSelectedTabId() == 0) {
                        return $('#similaritiesIdsGuid').val();
                    }
                    return $('#duplicatesIdsGuid').val()
                }

                $('#consolidate-button').click(function (e) {
                    var rationalizerContentId = getParam('rationalizerContentId');
                    var selectedIds = getSelectedIds();
                    $.ajax({
                        url: context + "/asyncRationalizerConsolidate.form?tk=" + getParam('tk'),
                        data: {
                            action: "generateGuidForSelectedIds",
                            rationalizerContentId: rationalizerContentId,
                            selectedIds: selectedIds
                        },
                        type: "POST",
                        // The type of data we expect back
                        dataType : "json",
                        success: function(json) {
                            var guidForSelectedIds = json.guidForSelectedIds;
                            if (_.isEmpty(guidForSelectedIds)) {
                                return;
                            }

                            $('#consolidate-button').iFramePopup({
                                width: 800,
                                height: 550,
                                displayOnInit: true,
                                id: "consolidateContentFrame",
                                title: client_messages.consolidate_frame_title,
                                src: context + "/rationalizer/rationalizer_consolidate_edit.form?rationalizerContentId=" + rationalizerContentId,
                                appliedParams: {
                                    tk: "${param.tk}",
                                    action: 1,
                                    guidForAllMatchedIds: getGuidForAllMatchedIds(),
                                    guidForSelectedIds: guidForSelectedIds,
                                    selectAllPages : getSelectAllPages(),
                                    viewType: getViewType()
                                },
                                onSave: function () {
                                    var viewType = getViewType();
                                    window.location = context + "/rationalizer/rationalizer_consolidate.form?rationalizerContentId=" + rationalizerContentId + "&viewType=" + viewType + "&tk=" + getParam('tk') ;
                                }
                            });
                        }
                    });
                });

                function setFixedTopbar($topbar, topPadding) {
                    var $topbarContainer = $topbar.parent(),
                        $topBarContent = $topbar.children();

                    $(window).scroll(function () {
                        if ($(window).scrollTop() > (($topBarContent.offset().top - topPadding) && $topbarContainer.offset().top)) {
                            if($('#pin-square').is(":hidden")) {
                                $topbar.height($topBarContent.outerHeight({margin: true}));
                                $topBarContent.css({
                                    'position': 'fixed',
                                    'top': topPadding + 'px'
                                });
                            }
                        } else {
                            $topbar.height('initial');
                            $topBarContent.css({
                                'position': 'static',
                                'top': '0'
                            });
                        }
                    });
                }

                setFixedTopbar($('#pageToolbar'), 0);

                function pinSelected() {
                    if($('#pin-square').is(":visible")) {
                        $('#pin-square').hide();

                        $('#pageToolbar').height('initial');
                        $('#pageToolbar').children().css({
                            'position': 'static',
                            'top': '0'
                        });
                    } else {
                        $('#pin-square').show()

                        $('#pageToolbar').height('initial');
                        $('#pageToolbar').children().css({
                            'position': 'static',
                            'top': '0'
                        });
                    }

                    setFixedTopbar($('#pageToolbar'), 0)
                }

                function toggleCollapseAll() {
                    if ($('#toggleMarkupBtn').is('.fa-toggle-off')) {
                        if($('#collapseAll').is(":checked")){
                            $('.raw-content').each(function () {
                                $(this).hide();
                            });
                            $('.raw-content-collapsed').each(function () {
                                $(this).show();
                            });
                        } else {
                            $('.raw-content-collapsed').each(function () {
                                $(this).hide();
                            });
                            $('.raw-content').each(function () {
                                $(this).show();
                            });
                        }
                    }
                }

                $(function () {
                    if (${command.viewType == 'similarities'}) {
                        $('#similarities-tab').removeClass('similarities-not-selected');
                        $('#similarities-tab').addClass('similarities-selected');

                        $('#duplicates-tab').removeClass('duplicates-selected');
                        $('#duplicates-tab').addClass('duplicates-not-selected');

                        $('#duplicates-found-count').hide();
                        $('#similarities-found-count').show();
                    } else {
                        $('#duplicates-tab').removeClass('duplicates-not-selected');
                        $('#duplicates-tab').addClass('duplicates-selected');

                        $('#similarities-tab').removeClass('similarities-selected');
                        $('#similarities-tab').addClass('similarities-not-selected');

                        $('#similarities-found-count').hide();
                        $('#duplicates-found-count').show();
                    }

                });

                function goBack() {
                    var storedLocation = localStorage.getItem("consolidate_top_frame_refferer_location");
                    if (typeof storedLocation !== undefined) {
                        location.replace(storedLocation);
                    } else {
                        common.history.goBack();
                    }
                }

                $(document).mouseup(function(e){
                    var container = $("#select_all_drill_down_rationalizerConsolidate");
                    if(!container.is(e.target) && container.has(e.target).length === 0){
                        container.hide();
                    }
                });

            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <msgpt:BodyNew>
        <msgpt:BannerNew edit="false"/>
        <msgpt:NewNavigationTabs edit="false" tab="0"/>
        <%--        <jsp:include page="includes/rationalizer_context_bar.jsp"/>--%>
        <msgpt:LowerContainer fullPanel="true" extendedWidth="true">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="true">
                <form:form method="post" modelAttribute="command">
                    <form:errors path="*">
                        <msgpt:Information errorMsgs="${messages}" type="error"/>
                    </form:errors>
                    <input id="similaritiesIdsGuid" type="hidden" value="${command.similaritiesIdsArrayGuid}">
                    <input id="duplicatesIdsGuid" type="hidden" value="${command.duplicatesIdsArrayGuid}">
                    <div>
                        <div class="px-2 py-1">
                            <div class="mb-4">
                                <div class="d-flex align-items-center pb-1">
                                    <div class="btn-group border-separate mr-auto" role="group">
                                        <div class="btn-group">
                                            <a style="color: #21150c; text-decoration: none;" href="javascript:goBack()">
                                                <i class="fas fa-chevron-left"></i> <fmtSpring:message code="page.label.back"/></a>
                                        </div>
                                    </div>
                                    <div class="form-group position-relative d-inline-block m-0">
                                        <div style="display: inline-block; vertical-align: middle;">
                                            <span id="similarities-tab" class="similarities-selected">
                                                    ${command.similaritiesDisplayString}
                                            </span>
                                        </div>
                                        <span style="padding-left: 5px; padding-right: 5px;">|</span>
                                        <div style="display: inline-block; vertical-align: middle;">
                                            <span id="duplicates-tab" class="duplicates-not-selected">
                                                    ${command.duplicatesDisplayString}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex align-items-center pb-1">
                                    <div class="btn-group border-separate mr-auto" role="group">
                                        <div class="btn-group">
                                           <span style="color: #7c1a87; font-weight: bold; font-size: 16px;">
                                               <fmtSpring:message code="page.label.consolidate.content"/>
                                           </span>
                                        </div>
                                    </div>
                                    <div class="form-group position-relative d-inline-block m-0">
                                        <div id="markupToggleContainer">
                                            <div style="display: inline-block; vertical-align: middle;">
                                                <i id="toggleMarkupBtn" onclick="toggleMarkupBtn()"
                                                   style="font-size: 19px; cursor: pointer; padding-top: 4px;"
                                                   class="far fa-toggle-off"></i>
                                            </div>
                                            <div style="display: inline-block; vertical-align: middle; font-size: 11px; padding-right: 8px; padding-left: 4px;">
                                                <fmtSpring:message code="page.label.show.markup.differences"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="pageToolbar">
                            <div class="toolbarContentSection">
                                <div style="background-color: #fefdd0; padding-left: 30px; padding-top: 30px; border: 3px solid #d8d7a9;">
                                    <table width="100%" cellspacing="0" cellpadding="0" border="0" class="innerContentTable">
                                        <thead>
                                        <tr style="padding-bottom: 20px">
                                            <td width="75%" style="padding: 0px; vertical-align: middle;">
                                                    <span style="font-weight: bold;">
                                                        <fmtSpring:message code="page.label.consolidate.searched.content"/>
                                                    </span>
                                            </td>
                                            <td width="15%" style="padding: 0px; vertical-align: middle; text-align: center;">
                                                    <span style="font-weight: bold;">
                                                        <fmtSpring:message code="page.label.consolidate.items.found"/>
                                                    </span>
                                            </td>
                                            <td width="10%" style="padding: 0px; vertical-align: middle; text-align: center;">

                                                <a href="javascript:pinSelected()">
                                                        <span class="fa-stack">
                                                            <i id="pin-square" style="font-size: 40px; color: #929aa2; display: none;"
                                                               class="far fa-stack-1x fa-square"></i>
                                                            <i style="font-size: 15px; top: 15%; color: #929aa2;"
                                                               class="far fa-stack-1x fa-thumbtack"></i>
                                                        </span>
                                                </a>
                                            </td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td width="75%" style="padding: 0px; vertical-align: middle;">
                                                    ${command.targetContent}
                                            </td>
                                            <td width="15%" style="padding: 0px; vertical-align: middle; text-align: center;">
                                                <span id="similarities-found-count">${command.similaritiesCount}</span>
                                                <span style="display: none;" id="duplicates-found-count">${command.duplicatesCount}</span>
                                            </td>
                                            <td width="10%" style="padding: 0px; vertical-align: middle;"/>
                                        </tr>
                                        <tr>
                                            <td style="padding-top: 20px; padding-bottom: 15px">
                                                <button id="consolidate-button" class="btn btn-primary" type="button">
                                                    <fmtSpring:message code="page.label.consolidate.combine.into.shared.object"/>
                                                </button>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div style="background-color: #a9a9a9; border-left: 3px solid #999; border-right: 3px solid #999; display: none;">
                                    <span style="padding-left: 20px; font-size: 12px;">
                                        <input type="checkbox" id="collapseAll" class="checkbox" onclick="toggleCollapseAll()"/>
                                        <fmtSpring:message code="page.label.collapse.all.results"/>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div style="padding-top: 15px;">
                            <msgpt:DataTable id="rationalizerConsolidate"
                                             async="true"
                                             columnReorder="true"
                                             numUnreorderableCols="0"
                                             permitsShowAll="false"
                                             columnVisibility="true"
                                             multiSelect="true"
                                             selectAllPages="true">
                            </msgpt:DataTable>
                        </div>
                    </div>
                </form:form>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
    </msgpt:BodyNew>
</msgpt:Html5>