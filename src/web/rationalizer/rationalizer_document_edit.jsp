<%@page import="com.prinova.messagepoint.controller.rationalizer.RationalizerDocumentEditController"%>
<%@page import="com.prinova.messagepoint.model.metadata.MetadataFormItemType"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<c:set var="actionSubmitValue" value="<%= RationalizerDocumentEditController.ACTION_SUBMIT %>"/>
<c:set var="actionSubmitAndReloadValue" value="<%= RationalizerDocumentEditController.ACTION_SUBMIT_AND_RELOAD %>"/>

<msgpt:Html5>
	<msgpt:HeaderNew title="page.label.rationalizer" viewType="<%= MessagepointHeader.ViewType.EDIT %>" extendedScripts="true">

		<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />

		<msgpt:Script src="includes/javascript/popupActions.js" />

		<msgpt:CalendarIncludes/>
		<msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js" />
		<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css" />

		<msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js" />
		<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css" />

		<msgpt:Script src="includes/javascript/handlebars/handlebars-v4.7.8.js" />

		<msgpt:Script src="includes/javascript/jQueryPlugins/scrollTo/jquery.scrollTo-2.1.2.min.js"/>

		<msgpt:Stylesheet href="includes/themes/commoncss/tinyMCEEditorContent.css" />
		<msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js" />

		<msgpt:Script src="includes/javascript/jQueryPlugins/hoverIntent/jquery.hoverIntent.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.editorActions.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.varTagAttrManager.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.smartTextTagAttrManager.js" />

		<msgpt:Script src="_ux/js/mp.postTrigger.js"/>
		<msgpt:Script src="_ux/js/mp.complexCombobox.js"/>

		<style type="text/css">
			textarea.markupContentArea  {
				width: 100%;
				height: 30px;
				resize: none;
			}
			textarea.markupContentArea:focus {
				height: 150px;
			}
			.contentSelected .contentViewContainer {
				background-color: #f5f5f5;
				border-radius: 3px;
			}

			.contentOrderIndicatorContainer {
				position: relative;
				background-color: #6d3075;
				color: #fff;
				font-size: 14px;
				text-align: center;
				border-radius: 16px;
				z-index: 1;
				min-width: 32px;
				width: fit-content;
				/* IE and other browsers than Chrome do not support fit-content */
				display: table;

			}
			.contentSelected  .contentOrderIndicatorContainer {
				background-color: green;
			}

			.contentOrderIndicator {
				padding: 9px;
				vertical-align: middle;
				white-space: nowrap;
			}

			.documentContentContainer {
				padding-right: 30px;
			}

			.actionBarIcon {
				font-size: 18px;
				padding: 0px 10px;
			}

			div.sharedContentToggleMenuContainer, div.sharedContentToggleLeftContainer, div.sharedContentToggleLeftContainer:active {
				cursor: pointer;
			}
			div.sharedContentToggleMenuContainer:hover {
				background-color: #eee;
			}
			div.sharedContentToggleMenuContainer:active {
				background-color: #ddd;
			}
			div.sharedContentToggleLeftContainer:hover, div.sharedContentToggleRightContainer:hover {
				color: #666;
			}
			div.sharedContentToggleLeftContainer:active, div.sharedContentToggleRightContainer:active {
				color: #999;
			}

			.sharedContentContainer {
				padding: 3px 7px 3px 3px;
				border: 1px solid #6d3075;
				border-radius: 5px 5px 5px 5px;
				margin-bottom: 6px;
				margin-left: 20px;
				position:  relative;
				width: calc(100% + 10px);
				left: -35px;
			}
			.contentSelected  .sharedContentContainer {
				border-color: green;
			}

			.disabledAction {
				color: #999;
			}

			.contentContainer p {
				margin-bottom: 1rem;
			}

			.actionBarHeaderLabel {
				line-height: 25px;
			}
			p:empty{
				display: none;
			}
			.listPropertiesIcon {
				display: none;
			}
		</style>

		<msgpt:Script>
			<script>

				var iFramePopup_fullFrameAttr_cust = JSON.parse(JSON.stringify(iFramePopup_fullFrameAttr));
				iFramePopup_fullFrameAttr_cust.width = 1240;

				var contentChanged = false;

				function closeIframe_local() {
					$(getTopFrame().document).find('.mce-custompanel,#iFramePopup_brandCheck,#iFramePopup_contentCompare').remove();
					closeIframe();
				}

				function deleteDocumentContent() {
					$('#deleteContentBtn').closest('.documentContentContainer').find('.contentOrderIndicator').popupFactory({
						title				: client_messages.content_editor.confirm,
						width				: 200,
						popupLocation		: "right",
						trigger				: "instant",
						fnSetContent		: function(o) {

							var popupEle = $("<div><div align=\"left\" style=\"font-size: 11px; margin: 8px 16px;\">" +
									"<div align=\"left\" style=\"font-size: 11px;\">" + client_messages.content_editor.confirm_remove_content + "</div>" +
									"</div>" +
									"<div style=\"padding: 8px 16px;\">" +
									"<table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"><tr>" +
									"<td/>" +
									"<td width=\"1%\">" +
									"<input title=\"" + client_messages.text.cancel + "\" type=\"button\" id=\"closeRemoveContentBtn\" onclick=\"javascript:popupFactoryRemove('rationalizerDocumentEdit_removeContentPopup');\" style=\"display: none;\" />" +
									"</td>" +
									"<td width=\"1%\">" +
									"<input title=\"" + client_messages.text.ok + "\" type=\"button\" id=\"confirmRemoveContentBtn\" style=\"display: none;\" />" +
									"</td>" +
									"</tr></table>" +
									"</div></div>");

							$(popupEle).find('#confirmRemoveContentBtn,#closeRemoveContentBtn').styleActionElement();

							$(popupEle).find('#confirmRemoveContentBtn_button').click( function() {
								$(o.targetEle).closest('.contentContainer').hide();

								$('.contentViewContainer').css({'height' : 'auto'});
								$('#editorContainer').css({'display' : 'none'});

								popupFactoryRemove('rationalizerDocumentEdit_removeContentPopup');
								var id = parseId( $(o.targetEle).closest('.documentContentContainer') );
								$("#contentActionBinding_" + id).val( -1 );
								reorderContentItems();
								$('#editContentMetadataBtn').addClass('disabledAction');
							});

							return popupEle;
						}
					});
				}

				function displayLoadingMask() {

					if ( $('#fullPageLoader').length != 0 )
						return;

					var $body = $('body'),
							$fullPageLoader =
									$('<div id="fullPageLoader" class="modal fade d-block">' +
											'	<div class="modal-dialog">' +
											'		<div class="m-5 p-1 text-center">' +
											'			<div class="progress-loader progress-loader-lg mb-3">' +
											'				<i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>' +
											'			</div>' +
											'			<p class="text-white">' +
											client_messages.text.saving +
											'			</p>' +
											'		</div>' +
											'	</div>' +
											'</div>');

					$body
							.addClass('modal-open')
							.append($fullPageLoader)
							.append('<div class="modal-backdrop fade"></div>');

					_.defer(function () {
						$('.modal-backdrop').addClass('show');
						_.delay(function () {
							$('#fullPageLoader').addClass('show');
						}, 150);
					});

				}

				function unselectAllContents() {
					$('.contentSelected').removeClass('contentSelected');
				}

				// Step ordering: Order items up and down
				function orderAction (action, itemId) {
					unselectAllContents();

					var currentStepOrder = $('#orderInput_'+itemId).val();
					if (action == 'up') {

						// First item: Can't order up
						if ( currentStepOrder == "1" )
							return;

						var adjContainer = $('#documentContentContainer_'+itemId).closest('tr').prev();
						$(adjContainer).before( $('#documentContentContainer_'+itemId).closest('tr') );

					} else if (action == 'down') {

						var numberOfVisibleSteps = $("[id^='formDefinitionItemContainer']:visible").length;
						// Last item: Can't order down
						if ( currentStepOrder == numberOfVisibleSteps)
							return;

						var adjContainer = $('#documentContentContainer_'+itemId).closest('tr').next();
						$(adjContainer).after( $('#documentContentContainer_'+itemId).closest('tr') );

					}

					reorderContentItems();
				}

				function reorderContentItems() {
					contentChanged = true;

					var currentOrder = 1;
					$('.contentOrderIndicator:visible').each( function() {
						$(this).html(currentOrder);
						$(this).closest('.documentContentContainer').find("[id^='orderInput_']").val(currentOrder++);
					});
				}

				function updateModifyMetadataValueBinding(ele) {

					var bindingValueArray = new Array();
					if ( $(ele).is('.style_multiselect_binding') ) {
						var metadataValuesArray = $('#metadataSelectValues').val().split(',');
						$('#metadataInput_multiselect').find('.style_multiselect_binding:checked').each( function() {
							bindingValueArray[bindingValueArray.length] = metadataValuesArray[$(this).val()];
						});
					} else if ( $(ele).is('#metadataInput_select') ) {
						var metadataValuesArray = $('#metadataSelectValues').val().split(',');
						bindingValueArray[bindingValueArray.length] = metadataValuesArray[$(ele).val()];
					} else {
						bindingValueArray[bindingValueArray.length] = $(ele).val();
					}

					$('#modifyContentMetadataReplaceValue').val( bindingValueArray.join(',') );
				}

				function requestMetadataValues() {

					function processMetadataValuesResponse(data) {

						$('#metadataInputContainer_loading').hide();

						if ( data.input_type == 3 ) {

							var inputHTML = "";
							for ( var i = 0; i < data.values.length; i++ ) {
								inputHTML += "<option id=\"metadataSelectOption_" + i + "\" value=\"" + i + "\">" +
										data.values[i] +
										"</option>";
							}
							$('#metadataSelectValues').val( data.values.join(',') );
							$('#metadataInput_select').replaceAllOptions(inputHTML).change();
							$('#metadataInputContainer_select').show();

						} else if ( data.input_type == 8 ) {

							var inputHTML = "";
							for ( var i = 0; i < data.values.length; i++ ) {
								inputHTML += "<div id=\"metadataSelectOption_" + i + "\">" +
										"<input value=\"" + i + "\" type=\"checkbox\" " +
										"class=\"style_multiselect_binding\" onclick=\"updateModifyMetadataValueBinding(this);\" />" +
										"<span>" + data.values[i] + "</span>" +
										"</div>";
							}
							$('#metadataSelectValues').val( data.values.join(',') );
							$('#metadataInput_multiselect').replaceAllOptions(inputHTML);
							$('#metadataInputContainer_multiselect').show();

						} else {

							$('#metadataInputContainer_text').change().show();

						}
					}

					$("[id^='metadataInputContainer']").hide();
					$('#metadataInputContainer_loading').show();
					$('#modifyContentMetadataReplaceValue').val('');

					var metadataConnector = $('#modifyContentMetadataConnectorValuesSelect').val();
					$.ajax({
						type: "GET",
						url: context + "/getObjectInfo.form?type=rationalizerQueryValues&sub_type=" + metadataConnector + "&rationalizerApplicationId=${rationalizerApplication.id}&objectId=3&cacheStamp=" + (stampDate.getTime()),
						dataType: "json",
						success: function(data) {
							processMetadataValuesResponse(data);
						}
					});

				}

				function initMetadataValueChangePopup() {
					requestMetadataValues();
					actionSelected('2')
				}

				function toggleMenuActions() {
					if ( $('.contentSelected').length != 0 ) {
						$('#actionMenu').enableOption('actionOption_2');
						$('#actionMenu').enableOption('actionOption_4');
					} else {
						$('#actionMenu').disableOption('actionOption_2');
						$('#actionMenu').disableOption('actionOption_4');
					}
				}

				function toggleMenuAction(ele) {

					var actionId = parseId( $(ele).find('option:selected').attr('id') );

					if ( actionId == '2' ) {
						initMetadataValueChangePopup();
					} else if (actionId == '4') {
						actionSelected('4')
					}
				}

				function editDocumentMetadata() {
					$('#editDocumentMetadataBtn').iFramePopup($.extend({
						src				: context + "/rationalizer/rationalizer_metadata_edit.form",
						displayOnInit	: true,
						id				: "rationalizerDocumentMetadataEditFrame",
						screenMask		: false,
						width           : 800,
						appliedParams	: {
							tk : "${param.tk}",
							rationalizerDocumentId : getParam('rationalizerDocumentId')
						},
						beforePopupClose: function() {

							// Update metadata values
							var stampDate = new Date();
							$.ajax({
								type: "GET",
								url: context+"/getObjectInfo.form" +
										"?type=rationalizerDocument" +
										"&objectId=" + getParam('rationalizerDocumentId') +
										"&tk=" + getParam('tk') +
										"&cacheStamp="+(stampDate.getTime()),
								dataType: "json",
								success: function(data) {
									if ( data.document_metadata != undefined && data.document_metadata.length > 0 )
										for ( var i=0; i < data.document_metadata.length; i++ )
											$('#valueViewContainer_' + data.document_metadata[i].id).html( data.document_metadata[i].value );
								}
							});

						}
					},iFramePopup_fullFrameAttr));
				}


				function updateViewEditDisplay(container) {
					if ( $(container).is('.currentEditorContext') ) {
						$('.documentContentActionBar').find('#editContentBtn').hide();
						$('.documentContentActionBar').find('#viewContentBtn').show();
					} else {
						$('.documentContentActionBar').find('#viewContentBtn').hide();
						$('.documentContentActionBar').find('#editContentBtn').show();
					}
				}

				function positionEditor(offset) {
					var viewContainer = $('.currentEditorContext').find('.contentViewContainer');
					if ( viewContainer.length != 0 ) {
						$('#editorContainer').css({
							'left' 	: $(viewContainer).offset().left + 'px',
							'top'	: ($(viewContainer).offset().top - offset) + 'px',
						});
						$('#editorContainer').show();
						var editorHeight =270;
						var viewContainerHeight = $(viewContainer).outerHeight();
						if(editorHeight > viewContainerHeight) {
							$(viewContainer).css({'height': editorHeight + 'px'});
							 $('#editorContainer').css({'height': editorHeight + 'px'});
						} else {
							$('#editorContainer').css({'height': viewContainerHeight + 'px'});
						}
					}
				}

				function enableActionButton(actionBtn) {
					var disabledCssClass = "fa-mp-disabled";
					var disabledAttr = actionBtn.attr('disabled');
					if (typeof disabledAttr !== typeof undefined && disabledAttr !== false) {
						actionBtn.removeAttr('disabled');
					}

					if (actionBtn.hasClass(disabledCssClass)) {
						actionBtn.removeClass(disabledCssClass);
					}
				}

				function disableActionButton(actionBtn) {
					var disabledCssClass = "fa-mp-disabled";

					var disabledAttr = actionBtn.attr('disabled');
					if (typeof disabledAttr === typeof undefined || disabledAttr === false) {
						actionBtn.attr("disabled", "disabled");
					}

					if (!actionBtn.hasClass(disabledCssClass)) {
						actionBtn.addClass(disabledCssClass);
					}
				}

				function initEditorForContext(documentContainer, offset) {
					$('.currentEditorContext').find('.contentViewContainer').html( $('.currentEditorContext').find('.contentEditInput').val() );

					$('.contentViewContainer').css({'height':'auto'});
					$('.documentContentContainer').removeClass('currentEditorContext');
					$(documentContainer).addClass('currentEditorContext');

					positionEditor(offset);

					var ed = tinymce.get('editor_rationalizerContent');
					ed.setContent( $(documentContainer).find('.contentEditInput').val() );
					var selectedContentId = $(documentContainer).attr('id').split('_')[1];
					ed.settings.content_object_id = selectedContentId;
                    $(window.top.document).find("[id^='iFramePopupWrapper_']").animate({scrollTop: $('#documentContentContainer_' + selectedContentId).offset().top - 50 }, 1000);
					var editorHeight = $('#editorContainer').height() - 100;
					$('#editor_rationalizerContent_ifr').css({'height': editorHeight + 'px'});
					ed.undoManager.clear();

					updateViewEditDisplay(documentContainer)

					ed.on("NodeChange", function() {
						var selection = tinymce.activeEditor.selection.getContent({format: 'text'});

						var shouldEnableSplitButton = true;

						if ($.trim(selection).length === 0) {
							shouldEnableSplitButton = false;
						}

						if (shouldEnableSplitButton
								&& $('.currentEditorContext')
										.find('.contentEditInput')
										.closest('.documentContentContainer')
										.find('.sharedContentContainer').length > 0 )
						{
							shouldEnableSplitButton = false;
						}

						if ( shouldEnableSplitButton ) {
							// Enable split button
							enableActionButton($('#splitContentBtn'));
						} else {
							// Disable split button
							disableActionButton($('#splitContentBtn'));
						}
					});
				}

				function saveAction() {
					saveAction(${submitType});
				}

				function saveAction(actionNr) {
					$('.currentEditorContext').find('.contentEditInput').val( $('#editor_rationalizerContent').val() );
					$(getTopFrame().document).find('.mce-custompanel,#iFramePopup_brandCheck,#iFramePopup_contentCompare').remove();
					var shiftFrame = $(getTopFrame().document).find('.shiftFrameContainer');
					if ( $(shiftFrame).is('.shiftFrameLeft') )
						$(shiftFrame).removeClass('shiftFrameLeft');
					doSubmit(actionNr);
				}

				$('#rationalizerEditSaveBtnGroup01').on('change', function () {
					var submitOption = $(this).val();
					globalSubmit(submitOption);
				});

				$('#rationalizerEditSaveBtnGroup02').on('change', function () {
					var submitOption = $(this).val();
					globalSubmit(submitOption);
				});

				function globalSubmit(submitOption) {
					displayLoadingMask();
					if ('saveandgoback' === submitOption) {
						saveAction(${actionSubmitValue});
					} else {
						saveAction(${actionSubmitAndReloadValue});
					}
				}

				function openEditorAndScrollToSelectedContent() {
					var editorsInitialized = (tinymce.editors && tinymce.editors.length > 0);
					if (!editorsInitialized) {
						setTimeout(openEditorAndScrollToSelectedContent, 500);

						return;
					}

					var selectedContentId = getParam('selectedContentId');
					if (selectedContentId === '') {
						return;
					}

					try {
						$('.currentEditorContext').find('.contentEditInput').val($('#editor_rationalizerContent').val());

						// SCROLL TO SELECTED CONTENT ITEM
						$(window.top.document).find("[id^='iFramePopupWrapper_']").animate({scrollTop: $('#documentContentContainer_' + selectedContentId).offset().top - 50 }, 1000);
						var hasSharedContent = $('#documentContentContainer_' + selectedContentId)
								.find('.sharedContentContainer').length > 0;
						if (hasSharedContent) {
							return;
						}

						initEditorForContext($('#documentContentContainer_' + selectedContentId), $('#contentDiv').offset().top );
					} catch (ex) {
						setTimeout( function(){ openEditorAndScrollToSelectedContent(); }, 300);
					}

				}

				function displaySaveContentChangesPopup() {
					$('#saveBtnContainer').popupFactory({
						title: client_messages.content_editor.save_required,
						width: 200,
						popupLocation: "bottom",
						trigger: "instant",
						fnSetContent: function (o) {

							var popupEle = $("<div><div align=\"left\" style=\"font-size: 11px; margin: 8px 16px;\">" +
									"<div align=\"left\" style=\"font-size: 11px;\">" +
									client_messages.content_editor.save_content_changes +
									"</div>" +
									"</div>");


							return popupEle;
						}
					});
				}

				$(function() {
					var editorData = {
						apply_super_sub: true,
						channel: ${rationalizerDocument.appliedChannelId},
						connector: ${rationalizerDocument.appliedConnectorId},
						can_edit_source: ${sourceEditPerm},
						marcie_flags : ${marcieFlags},
						applies_forms: ${rationalizerDocument.supportsForms},
						applies_barcodes: ${rationalizerDocument.supportsBarcodes},
						applies_tables: ${rationalizerDocument.supportsTables},
						canvas_dimensions: "10:1.5",
						document_ids : [0],
						rationalizer_application_id: ${rationalizerApplication.id}
					};

					tinyMCEinit(1025, 170, editorData);
					$("#editor_rationalizerContent").tinymce( tinymceEditorDef_standard );

					openEditorAndScrollToSelectedContent();

				});

				$(function(){


					$("input:button,.style_select,.style_menu").each( function() {
						var menuItemMaxWidth = $(this).width() / 5;
						$(this).styleActionElement({labelAlign: true,
							maxMenuItemLength	: menuItemMaxWidth
						});
					});

					$('#documentMetatags').tagCloud({
						tagCloudType				: 12,
						rationalizerApplicationId	: '${rationalizerApplication.id}',
						inputType					: 'metatags'
					});

					// Select item: On content click
					$('.contentViewEditContainer')
							.click( function(e) {
								if ( $(e.target).closest('.nonContentSelectEle').length != 0 )
									return;

								if ( $(this).find('.selectedContentIdBinding').is(':checked') ) {
									$(this).find('.selectedContentIdBinding').removeAttr('checked');
									$(this).closest('.documentContentContainer').removeClass('contentSelected');
								} else {
									$(this).find('.selectedContentIdBinding').attr('checked','checked');
									$(this).closest('.documentContentContainer').addClass('contentSelected');
								}

								var shouldEnableMergeButton = true;
								var count = 0;
								$('.contentSelected').each(function () {
									count ++;
									if (!shouldEnableMergeButton) {
										return;
									}

									if ($(this).find('.sharedContentContainer').length > 0) {
										shouldEnableMergeButton = false;
									}

								});
								if (shouldEnableMergeButton && (count < 2) ) {
									shouldEnableMergeButton = false;
								}

								if ( shouldEnableMergeButton ) {
									enableActionButton($('#mergeContentBtn'))
								} else {
									disableActionButton($('#mergeContentBtn'))
								}

								toggleMenuActions();
							});

					// Content action bar: Show on hover of order column
					$('.contentActionContainer')
							.mouseover( function(e) {
								if ( $(this).find('.documentContentActionBar').length == 0 )
									$(this).append( $('.documentContentActionBar') );
								updateViewEditDisplay($(this).closest('.documentContentContainer'));
								$('.documentContentActionBar').show();
							})
							.mouseout( function(e) {
								if ( $(e.target).closest('.documentContentActionBar').length == 0 )
									$('.documentContentActionBar').hide();
								updateViewEditDisplay($(this).closest('.documentContentContainer'));
							});
					$('.documentContentActionBar')
							.mouseout( function(e) {
								$('.documentContentActionBar').hide();
								updateViewEditDisplay($(this).closest('.documentContentContainer'));
							});

					$('.selectedContentIdBinding:checked').each( function(e) {
						$(this).closest('.contentViewEditContainer').closest('.documentContentContainer').addClass('contentSelected');
					});
					toggleMenuActions();

					// Order up: Init order up buttons
					$('#orderUpBtn').click( function() {
						orderAction( 'up', parseId($(this).closest('.documentContentContainer')) );
					});

					// Order down: Init order down buttons
					$('#orderDownBtn').click( function() {
						orderAction( 'down', parseId($(this).closest('.documentContentContainer')) );
					});

					// Add content
					$('#addContentBtn').click( function() {
						unselectAllContents();

						var id = parseId( $(this).closest('.documentContentContainer') );
						var targetOrder = $('#orderInput_' + id).val();
						$('#insertAfterOrder').val( targetOrder );

						$('#addContentBtn').closest('.documentContentContainer').find('.contentOrderIndicator').popupFactory({
							title				: client_messages.title.add_content,
							width				: 300,
							popupLocation		: "right",
							trigger				: "instant",
							fnSetContent		: function(o) {

								var popupEle = $("<div><div align=\"left\" style=\"margin: 8px 16px;\">" +
										"<div align=\"left\" style=\"font-size: 12px;\">" + client_messages.text.how_many_content_to_add + "</div>" +
										"<div align=\"left\" style=\"margin-top: 12px;\">" +
										"<input id=\"popupAddContentCountInput\" class=\"input2digit numeric\" maxlength=\"2\" filterId=\"popupAddContentCountInput\" />" +
										$.alphanumeric.generateFilterTooltip("popupAddContentCountInput") +
										"</div>" +
										"</div>" +
										"<div style=\"padding: 8px 16px;\">" +
										"<table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"><tr>" +
										"<td/>" +
										"<td width=\"1%\" style=\"padding-right: 12px;\">" +
										"<input title=\"" + client_messages.text.cancel + "\" type=\"button\" id=\"closeAddContentBtn\" onclick=\"javascript:popupFactoryRemove('rationalizerDocumentEdit_addContentPopup');\" style=\"display: none;\" />" +
										"</td>" +
										"<td width=\"1%\">" +
										"<input title=\"" + client_messages.text.add + "\" type=\"button\" disabled=\"disabled\" id=\"confirmAddContentBtn\" style=\"display: none;\" />" +
										"</td>" +
										"</tr></table>" +
										"</div></div>");

								$(popupEle).find('.numeric').alphanumeric({numeric: true});

								$(popupEle).find('#confirmAddContentBtn,#closeAddContentBtn').styleActionElement();

								$(popupEle).find('#popupAddContentCountInput').keyup(function() {
									if ( $(this).val().length > 0 && parseInt($(this).val()) > 0 )
										$(popupEle).find('#confirmAddContentBtn').enableElement();
									else
										$(popupEle).find('#confirmAddContentBtn').disableElement();
								});

								$(popupEle).find('#confirmAddContentBtn_button').click( function() {

									popupFactoryRemove('rationalizerDocumentEdit_addContentPopup');
									$('#addContentCountInput').val( $(popupEle).find('#popupAddContentCountInput').val() );
									doSubmit(1);

								});

								return popupEle;
							}
						});

					});

					// Edit content metadata
					$('#editContentMetadataBtn').click( function() {
						unselectAllContents();

						if ( $(this).is('.disabledAction') || contentChanged ) {
							displaySaveContentChangesPopup();

							return;
						}

						var documentContentId = parseId( $(this).closest('.documentContentContainer') );
						var container = $(this).closest('.documentContentContainer');
						var tmpThis = this;

						$.ajax({
							url: context + "/asyncRationalizerUtil.form?tk=" + getParam('tk'),
							data: {
								action: "getSharedContentGuid",
								rationalizerContentId: documentContentId
							},
							type: "POST",
							// The type of data we expect back
							dataType : "json",
							success: function(json) {
								var rationalizerSharedContentGuid = json.rationalizerSharedContentGuid;
								if (_.isEmpty(rationalizerSharedContentGuid)) {
									$(tmpThis).iFramePopup($.extend({
										src				: context + "/rationalizer/rationalizer_metadata_edit.form",
										displayOnInit	: true,
										id				: "rationalizerDocumentContentMetadataEditFrame",
										screenMask		: false,
										width           : 800,
										appliedParams	: {
											tk : "${param.tk}",
											rationalizerDocumentContentId : documentContentId
										},
										afterPopupClose	    : function () {
											window.location = context+'/rationalizer/rationalizer_document_edit.form?rationalizerApplicationId='+${rationalizerApplication.id} +'&rationalizerDocumentId='+ getParam('rationalizerDocumentId')+'&selectedContentId=' + documentContentId + '&tk=' + getParam('tk');
										},
										onSave: function() {
											displayLoadingMask();
										}
									},iFramePopup_fullFrameAttr));
									$(window.top.document).find('#iFramePopupFrame').css('width', '830px');
									$(window.top.document).find('#iFramePopup_rationalizerDocumentContentMetadataEditFrame').css('width', '830px');
								} else {
									$(tmpThis).iFramePopup($.extend({
										src				: context + "/rationalizer/rationalizer_dc_fields.form",
										displayOnInit	: true,
										id				: "rationalizerDCFieldsFrame",
										screenMask		: false,
										width           : 800,
										appliedParams	: {
											tk : "${param.tk}",
											rationalizerDocumentContentId : documentContentId
										},
										afterPopupClose	    : function () {
											window.location = context+'/rationalizer/rationalizer_document_edit.form?rationalizerApplicationId='+${rationalizerApplication.id} +'&rationalizerDocumentId='+ getParam('rationalizerDocumentId')+'&selectedContentId=' + documentContentId + '&tk=' + getParam('tk');
										},
										onSave: function() {
											displayLoadingMask();
										}
									},iFramePopup_fullFrameAttr));
								}
							}
						});
					});

					// Document metadata: Show/hide toggle
					$('#documentMetadataToggle').click( function() {
						if ( $(this).find('.fa-caret-right').length != 0 ) {
							$(this).find('.documentMetadataToggleIndicator').removeClass('fa-caret-right').addClass('fa-caret-down');
							$('#editorContainer').css({'display'	: 'none'});
							$('#documentMetadataContainer').slideDown(function() {
								positionEditor($('#contentDiv').offset().top);
							});
						} else {
							$(this).find('.documentMetadataToggleIndicator').removeClass('fa-caret-down').addClass('fa-caret-right');
							$('#editorContainer').css({'display'	: 'none'});
							$('#documentMetadataContainer').slideUp(function() {
								positionEditor($('#contentDiv').offset().top);
							});
						}
					});

					// Content: Edit
					$('.contentViewContainer').dblclick( function() {
						$('.currentEditorContext').find('.contentEditInput').val( $('#editor_rationalizerContent').val() );
						initEditorForContext( $(this).closest('.documentContentContainer'), $('#contentDiv').offset().top );
					});

					$('#editContentBtn').click( function() {
						var hasSharedContent = $(this)
								.closest('.documentContentContainer')
								.find('.sharedContentContainer').length > 0;
						if (!hasSharedContent) {
							$('.currentEditorContext').find('.contentEditInput').val( $('#editor_rationalizerContent').val() );
							initEditorForContext( $(this).closest('.documentContentContainer'), $('#contentDiv').offset().top );

							return;
						}

						if ( contentChanged ) {
							displaySaveContentChangesPopup();

							return;
						}

						var documentContentId = parseId( $(this).closest('.documentContentContainer') );
						var tmpThis = this;
						$.ajax({
							url: context + "/asyncRationalizerUtil.form?tk=" + getParam('tk'),
							data: {
								action: "getSharedContentGuid",
								rationalizerContentId: documentContentId
							},
							type: "POST",
							// The type of data we expect back
							dataType: "json",
							success: function(json) {
								var rationalizerSharedContentGuid = json.rationalizerSharedContentGuid;
								var emptySharedContentGuid = _.isEmpty(rationalizerSharedContentGuid);

								if (emptySharedContentGuid) {
									return;
								}

								$(tmpThis).iFramePopup({
									src: context + "/rationalizer/rationalizer_shared_content_edit.form?rationalizerSharedContentIds=" + rationalizerSharedContentGuid,
									displayOnInit: true,
									id: "editShareContentFrame",
									width: 1240,
									title: client_messages.title.shared_content_edit,
									appliedParams: {
										tk: "${param.tk}",
										action: 1
									},
									beforePopupClose: function () {
										$('.mce-custompanel,#iFramePopup_brandCheck', getTopFrame().document).remove();

									},
									afterPopupClose	    : function () {
										window.location = context+'/rationalizer/rationalizer_document_edit.form?rationalizerApplicationId='+${rationalizerApplication.id} +'&rationalizerDocumentId='+ getParam('rationalizerDocumentId')+'&selectedContentId='+documentContentId+'&tk=' + getParam('tk');
									},
									onSave: function () {
										displayLoadingMask();
									}
								});
							}
						});
					});

					function areHtmlValuesEqual(firstValue, secondValue) {
						try {
							if (_.isEmpty(firstValue) && _.isEmpty(secondValue)) {
								return true;
							}

							var firstValueNormalized = tinymce.html.Entities.decode(firstValue.replace(/\s+\/>/g,"/>"));
							var secondValueNormalized = tinymce.html.Entities.decode(secondValue.replace(/\s+\/>/g,"/>"));

							return firstValueNormalized === secondValueNormalized;
						} catch (ex) {
							console.log(ex);

							return false;
						}
					}

					// Content: View
					$('#viewContentBtn').click( function() {
						unselectAllContents();

						disableActionButton($('#splitContentBtn'));

						$('.contentViewContainer').css({'height':'auto'});
						$('#editorContainer').css({'display'	: 'none'});

						var previousValue = $(this).closest('.documentContentContainer').find('.contentEditInput').val();
						var newValue = $('#editor_rationalizerContent').val();

						if ( !areHtmlValuesEqual(previousValue, newValue) ) {
							contentChanged = true;
						}

						$(this).closest('.documentContentContainer').find('.contentEditInput').val( newValue );
						$(this).closest('.documentContentContainer').find('.contentViewContainer').html( newValue )
						$(this).closest('.documentContentContainer').removeClass('currentEditorContext');

						updateViewEditDisplay($(this).closest('.documentContentContainer'))
					});

					// Action bar: Float top
					var $topbar = $('#frameToolbar');
					$topbarContainer = $(window.top.document).find("[id^='iFramePopupWrapper_rationalizerDocumentFrame']").find('.iFramePopupFrameContainer'),
							$topBarContent = $topbar.children();
					var scrollContainer = $(window.top.document).find("[id^='iFramePopupWrapper_rationalizerDocumentFrame']");

					$(scrollContainer).scroll(function (e) {
						var containerOffsetTop = $topbarContainer.offset().top;
						if ($(scrollContainer).offset().top >= containerOffsetTop) {
							$topbar.height($topBarContent.outerHeight({margin: true}));
							$topBarContent.css({
								'position': 'fixed',
								top: ($(scrollContainer).offset().top - containerOffsetTop) + 'px'
							});
						} else {
							$topbar.height('initial');
							$topBarContent.css({
								'position': 'static',
								'top': '0'
							});
						}
					});
				});

				// Content metadata: View
				$('#viewContentMetaBtn')
						.click( function() {
							unselectAllContents();

							$('#popupHeightExtension').show();
							adjustIFrameHeight(frameElement);

							var container = $(this).closest('.documentContentContainer');
							var documentContentId = parseId( $(container) );
							$(container).find('.contentOrderIndicator').popupFactory({
								title					: client_messages.title.content_metadata,
								width					: 900,
								popupLocation			: "right",
								referenceWindow			: "local",
								trigger					: "instant",
								asyncDataType			: "json",
								asyncSetContentURL		: context + "/getObjectInfo.form?type=rationalizerContent&objectId=" + documentContentId + "&cacheStamp=" + (stampDate.getTime()),
								asyncSetContentHandler	: function(o, data) {

									var popupEleHTML =  "<div style=\"padding: 1px 8px;max-height: 650px; overflow: auto; display: inline-block;\">";
									var contentParsedMetadata = [];
									var otherMethadata = [];

									if ( data.content_metadata != undefined && data.content_metadata.length > 0 ) {
										for (var i = 0; i < data.content_metadata.length; i++) {
											if (data.content_metadata[i].origin_type == 2) {
												contentParsedMetadata.push(data.content_metadata[i]);
											} else {
												otherMethadata.push(data.content_metadata[i]);
											}
										}
										if(otherMethadata.length >0) {
											popupEleHTML += "<fieldset style=\"color: black; padding-left:0.625rem; border: 1px solid #cfcfcf;\"><legend class=\"form-label\" style=\"color: black; padding-left:0.625rem; border-color: black; width:auto; margin-bottom: 0.1rem\">&nbsp;Template Metadata&nbsp;</legend>";
										}
										for (var i = 0; i < otherMethadata.length; i++) {
											popupEleHTML += "<div style=\"display: inline-block; min-width: 350px; padding-top: 10px; padding-left: 15px;\">" +
													"<div style=\"font-size: 14px; font-weight: bold\">" + otherMethadata[i].label + "</div>" +
													"<div style=\"font-size: 12px;\">" + otherMethadata[i].value + "</div>" +
													"</div>";
										}
										if(otherMethadata.length >0) {
											popupEleHTML += "</fieldset>";
										}
										if(contentParsedMetadata.length >0) {
												popupEleHTML += "<fieldset style=\"color: black; padding-left:0.625rem; padding-top:0.625rem; border: 1px solid #cfcfcf;\"><legend class=\"form-label\" style=\"color: black; padding-left:0.625rem; padding-top:0.625rem; border-color: black; width:auto; margin-bottom: 0.1rem\">&nbsp;Content Metadata&nbsp;</legend>";

										}
										for (var i = 0; i < contentParsedMetadata.length; i++) {
										        popupEleHTML += "<div style=\"display: inline-block; min-width: 350px; padding-top: 10px; padding-left: 15px; \">" +
														"<div style=\"font-size: 14px; font-weight: bold\">" + contentParsedMetadata[i].label + "</div>" +
														"<div style=\"font-size: 12px;white-space: pre-wrap\">" + contentParsedMetadata[i].value + "</div>" +
														"</div>";

										}
										if(contentParsedMetadata.length >0) {
											popupEleHTML += "</fieldset>";
										}
									}

									popupEleHTML += 	"</div>";

									return $(popupEleHTML);
								}
							});
							$('.popupFactory_popupContainer').css({'z-index': 'auto'});
						});

				// Split content
				$('#splitContentBtn').click( function() {
					if ($('#splitContentBtn').attr('disabled') === "disabled") {
						return;
					}

					unselectAllContents();
					var id = parseId($(this).closest('.documentContentContainer'));
					var targetOrder = $('#orderInput_' + id).val();
					$('#insertAfterOrder').val(targetOrder);
					var tinymceActiveEditor = tinymce.activeEditor;
					var contentToSplit = tinyMCE.activeEditor.selection.getContent({format: 'html'});

					if (!contentToSplit || contentToSplit.trim().length == 0) {
						return;
					}
					var count = contentToSplit.split('<span').length - 1;
					if (count == 0) {
						var crtNode = tinyMCE.activeEditor.selection.getNode();
						var parent = tinyMCE.activeEditor.selection.getNode().parentNode;
						if (parent !== undefined && crtNode.innerText === tinyMCE.activeEditor.selection.getContent({format: 'text'})) {
							//selects the whole text in the node
							if (parent.dataset !== undefined && parent.dataset.mceStyle !== undefined) {
								contentToSplit = "<p><span style=\"" + parent.dataset.mceStyle + "\">" + crtNode.outerHTML + "</span></p>";
							} else {
								contentToSplit = "<p>" + crtNode.outerHTML + "</p>";
							}
						} else {
							if (crtNode.localName === 'span' && crtNode.dataset !== undefined && crtNode.dataset.mceStyle !== undefined) {
								// selects text part of a span node
								contentToSplit = "<p><span style=\"" + crtNode.dataset.mceStyle + "\">" + contentToSplit + "</span></p>";
							} else {
								if (parent !== undefined && parent.dataset !== undefined && parent.dataset.mceStyle !== undefined) {
									if (crtNode.localName !== "a" && crtNode.localName !== "mpr_variable") {
										//selects node containing superscript/ subscript
										contentToSplit = "<p><span style=\"" + parent.dataset.mceStyle + "\"><" + crtNode.localName + ">" + contentToSplit + "</" + crtNode.localName + ">" + "</span></p>";
									} else {
										//selects node a href  or mpr_variable
										contentToSplit = "<p><span style=\"" + parent.dataset.mceStyle + "\">" + contentToSplit + "</span></p>";
									}
								} else {
									contentToSplit = "<p>" + contentToSplit + "</p>";
								}
							}
						}
					}
					displayLoadingMask();
					$('#insertedMarkupContentInput').val(contentToSplit);
					tinymceActiveEditor.on("ExecCommand", function (e) {
						if (!(e.command == 'Delete')) {
							return;
						}

						var remainingContent = tinymceActiveEditor.getContent({format: 'html'});

						$('.currentEditorContext').find('.contentEditInput').val(remainingContent);
						$('.currentEditorContext').find('.contentViewContainer').html(remainingContent);

						doSubmit(6);
					});
					tinymceActiveEditor.execCommand('Cut');
				});

				// Merge content
				$('#mergeContentBtn').click( function() {
					if ($('#mergeContentBtn').attr('disabled') === "disabled") {
						return;
					}

					var selectedItems = $(" div.documentContentContainer.contentSelected ").find(" .contentEditInput ");
					if (selectedItems.length == 0) {
						return;
					}
					displayLoadingMask();
					doSubmit(5);
				});

				// Content compare
				$('#contentCompareBtn')
						.click( function() {
							unselectAllContents();

							var documentContentId = parseId( $(this).closest('.documentContentContainer') );
							var src = context + "/rationalizer/rationalizer_query_edit_detail.form?" +
									"rationalizerQueryId=-1&listSubType=duplicatesAndSimilarities&listAccessType=popup";

							$(this).iFramePopup($.extend({
								src					: src,
								title				: client_messages.title.exact_matches_and_similarities,
								displayOnInit		: true,
								applySnapOffToggle	: true,
								id					: "rationalizerDocumentContentCompareFrame",
								screenMask			: false,
								appliedParams		: {
									tk : "${param.tk}",
									rationalizerComparisonContentId : documentContentId
								},
								beforePopupClose	: function(t,o) {
									if ( o != undefined && o.close_action == "frame" )
										location.reload();
								}
							},iFramePopup_fullFrameAttr));
						});

				// Shared content: Toggle documents
				$('.sharedContentToggleMenuContainer')
						.click( function() {
							var sharedContentId = parseId( $(this) );
							$(this).popupFactory({
								title					: client_messages.title.take_me_to,
								width					: 350,
								popupLocation			: "left",
								trigger					: "instant",
								asyncDataType			: "json",
								asyncSetContentURL		: context + "/getObjectInfo.form?type=rationalizerSharedContent&objectId=" + sharedContentId + "&cacheStamp=" + (stampDate.getTime()),
								asyncSetContentHandler	: function(o, data) {

									var popupEleHTML =  "<div style=\"max-height: 300px; overflow: auto;\">";

									if ( data.documents != undefined && data.documents.length > 8 ) {
										popupEleHTML += "<div style=\"padding-bottom: 10px;\">" +
												"<input id=\"popupSearchInput\" type=\"text\" style=\"width: 175px;\" />" +
												"</div>";
									}

									if ( data.documents != undefined && data.documents.length > 0 )
										for ( var i=0; i < data.documents.length; i++ ) {
											popupEleHTML += "<div id=\"sharedContentDocument_" + data.documents[i].id + "\" class=\"sharedContentDocument\" style=\"padding: 5px 10px; cursor: pointer;\">" +
													"<div style=\"font-size: 12px; word-break: break-word; line-height: 1.2;\">" + data.documents[i].name + "</div>" +
													"</div>";
										}

									popupEleHTML += 	"</div>";

									popupEleHTML = $(popupEleHTML);
									$(popupEleHTML).find('.sharedContentDocument')
											.click( function() {
												var documentId = parseId(this);
												window.location = context+'/rationalizer/rationalizer_document_edit.form?rationalizerDocumentId='+documentId+'&tk=' + getParam('tk');
											})
											.mouseover( function() {
												$(this).css({'background-color':'#eee'});
											})
											.mouseout( function() {
												$(this).css({'background-color':'#fff'});
											});

									$(popupEleHTML).find('#popupSearchInput').keyup(function(){
										var searchValue = $(this).val();
										$(popupEleHTML).find('.sharedContentDocument').each( function() {
											if ( $(this).text().indexOf(searchValue) != -1 )
												$(this).show();
											else
												$(this).hide();
										});
									});

									return popupEleHTML;
								}
							});
						});

				$('.sharedContentToggleRightContainer,.sharedContentToggleLeftContainer')
						.click( function() {
							var sharedContentId = parseId( $(this) );
							var stampDate = new Date();
							var direction = $(this).is('.sharedContentToggleRightContainer') ? 'right' : 'left';
							$.ajax({
								type: "GET",
								url: context + "/getObjectInfo.form?type=rationalizerSharedContent&objectId=" + sharedContentId + "&cacheStamp=" + (stampDate.getTime()),
								dataType: "json",
								success: function(data) {

									if ( data.documents != undefined && data.documents.length > 0 ) {
										var currentDocumentId = getParam('rationalizerDocumentId')
										var currentIndex = null;
										for ( var i=0; i < data.documents.length; i++ )
											if ( currentDocumentId == data.documents[i].id )
												currentIndex = i;

										if ( currentIndex != null ) {
											var nextIndex;
											direction == 'right' ? (nextIndex = currentIndex + 1) : (nextIndex = currentIndex - 1);
											nextIndex = nextIndex == data.documents.length ? 0 : nextIndex;
											nextIndex = nextIndex == -1 ? data.documents.length - 1 : nextIndex;

											window.location = context+'/rationalizer/rationalizer_document_edit.form?rationalizerDocumentId='+data.documents[nextIndex].id+'&tk=' + getParam('tk');
										}
									}

								}
							});
						});

				$("#contentHistoryButton").each( function() {
					if ( !$(this).hasClass('actionBtn_disabled') ) {
						$(this).click( function() {
							$('#contentHistoryButton').iFramePopup($.extend({
								src: context + "/rationalizer/rationalizer_content_history.form",
								displayOnInit: true,
								id: "contentHistoryFrame",
								appliedParams: {
									'tk': "${param.tk}",
									'rationalizerContentId': parseId( $(this).closest('.documentContentContainer'))
								},
								beforePopupClose: function () {
									$('#iFramePopup_contentHistoryFrame').remove();
								}
							}, iFramePopup_fullFrameAttr_cust));
						});
					}
				});
			</script>
		</msgpt:Script>
	</msgpt:HeaderNew>

	<c:set var="submitType" value="<%= RationalizerDocumentEditController.ACTION_SUBMIT %>"/>

	<c:set var="itemType_text" 				value="<%= MetadataFormItemType.ID_TEXT %>" />
	<c:set var="itemType_textarea" 			value="<%= MetadataFormItemType.ID_TEXTAREA %>" />
	<c:set var="itemType_menu" 				value="<%= MetadataFormItemType.ID_SELECT_MENU %>" />
	<c:set var="itemType_webServiceMenu" 	value="<%= MetadataFormItemType.ID_WEB_SERVICE_MENU %>" />
	<c:set var="itemType_dateDayMonthYear" 	value="<%= MetadataFormItemType.ID_DATE_DAY_MONTH_YEAR %>" />
	<c:set var="itemType_dateMonthYear" 	value="<%= MetadataFormItemType.ID_DATE_MONTH_YEAR %>" />
	<c:set var="itemType_textEditor" 		value="<%= MetadataFormItemType.ID_TEXT_EDITOR %>" />
	<c:set var="itemType_multiselect" 		value="<%= MetadataFormItemType.ID_MULTISELECT_MENU %>" />

	<msgpt:BodyNew theme="minimal" type="iframe" cssStyle="overflow: hidden;">

		<form:form enctype="multipart/form-data" >
			<c:if test="${empty param.nprSaveSuccess}">

				<form:hidden path="actionValue" id="actionElement"/>
				<form:hidden path="insertAfterOrder" id="insertAfterOrder" />
				<form:hidden path="addContentCount" id="addContentCountInput" />
				<form:hidden path="insertedMarkupContent" id="insertedMarkupContentInput" />

				<div id="popupHeaderTitle" style="display: none;">
				<span class="titleText">
					<fmtSpring:message code="page.label.edit.document"/>
				</span>
				</div>

				<div class="contentTableIframeExtended">
					<div class="contentPanel backgroundTile_10p" style="padding: 0px; min-height: 630px; position: relative;">



						<!-- ACTION BAR: Forms -->
						<div id="frameToolbar" style="position: relative;">
							<div style="width: 1240px; z-index: 2;">
								<table class="actionsBarContainer" width="100%" cellspacing="0" cellpadding="0" border="0" style="border-radius: 0px;"><tr>
									<td align="left">
										<div class="actionBarHeaderLabel" style="padding-left: 5px; vertical-align: middle;">
											<c:if test="${command.rationalizerDocument.originalFilePresent}">
												<i class="table-icon far fa-download ml-2 detailTip" style="padding-right: 8px;" title="<fmtSpring:message code="client_messages.text.click_to_download_pdf" />"
												   aria-hidden="true" onclick="javascript:javascriptHref('${contextPath}/download/pdf.form?resource=${msgpt:getResourceToken().add('file', command.rationalizerDocument.filePath).add('type', 'download_file')}')"></i>
											</c:if>

											<c:out value="${command.rationalizerDocument.trimName}" /> <span style="font-size: 16px; color: #ddd; padding-left:20px;"><c:out value="${command.rationalizerDocument.navigationBreadCrumb}" /></span>
										</div>
									</td>

									<td align="right" style="padding-right: 6px;">
										<!-- ACTION BAR: UPDATE, ACTIONS, SHOW -->
										<table cellspacing="0" cellpadding="0" border="0"><tbody><tr>
											<td id="saveBtnContainer" align="right" style="padding: 0px;">
												<div class="btn-group ml-3">
													<button type="button" class="btn btn-primary btn-placeholder" disabled>
														<i class="fad fa-spinner-third fa-lg fa-spin align-middle mx-3"
														   aria-hidden="true"></i>
													</button>
													<select id="rationalizerEditSaveBtnGroup01"
															class="complex-combobox-select"
															aria-label="${msgpt:getMessage("page.label.actions")}"
															data-toggle="complex-combobox"
															data-combobox-class="btn-primary">

														<option id="btnSaveAndBack01" value="saveandgoback"
																data-icon="far fa-arrow-left"
																data-customitem="true"><fmtSpring:message
																code="page.label.save"/>
														</option>
														<option id="btnSaveAndStay01" value="saveandstay"
																data-icon="fas fa-save"><fmtSpring:message
																code="page.label.save.continue"/>
														</option>
													</select>
													<button type="button"
															class="btn btn-primary dropdown-toggle dropdown-toggle-split"
															data-toggle="dropdown" aria-haspopup="true"
															aria-expanded="false" disabled>
        <span class="sr-only"><fmtSpring:message
				code="page.text.toggle.dropdown"/></span>
													</button>
												</div>
											</td>
											<td td width="1%" align="right" style="padding: 0px;">
												<select title="${msgpt:getMessage('page.label.more')}" id="actionMenu" class="inputM style_menu" onchange="toggleMenuAction(this)" style="display: none;">
													<option id="actionOption_2" disabled="disabled"><fmtSpring:message code="page.label.modify.content.metadata"/></option>
													<option id="actionOption_4" disabled="disabled"><fmtSpring:message code="page.label.delete.content"/></option>
												</select>
											</td>
										</tr></tbody></table>
									</td>
								</tr></table>
							</div>
						</div>

						<!-- ERRORS -->
						<form:errors path="*">
							<msgpt:Information errorMsgs="${messages}" type="error" />
						</form:errors>

						<!-- METADATA CONTAINER -->
						<div class="backgroundTile_50p" style="padding: 10px 30px 10px 20px; border-bottom: 1px solid #ddd; font-size: 20px;">
							<div id="documentMetadataHeader">
								<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>
									<td width="99%" align="left" id="documentMetadataToggle" style="cursor: pointer; font-size: 18px;">
										<fmtSpring:message code="page.label.document.metadata" /> <i class="documentMetadataToggleIndicator fa fa-caret-right" style="padding-left: 12px;"></i>
									</td>
									<td align="right">
										<div id="editDocumentMetadataBtn">
											<msgpt:Button label="page.label.edit.document.metadata" URL="javascript:editDocumentMetadata()" />
										</div>
									</td>
								</tr></table>
							</div>

							<!-- NAME, DOCUMENT TAGS, DESCRIPTION, DOCUMENT METADATA -->
							<div id="documentMetadataContainer" style="display: none; padding: 20px 15px 0px 15px; margin-top: 5px; border-top: 1px solid #ddd;">
								<msgpt:DataTable labelPosition="top" multiColumn="true">
									<!-- Name -->
									<msgpt:TableItem label="page.label.name" mandatory="true">
										<msgpt:InputFilter type="simpleName">
											<form:input id="nameInput" path="rationalizerDocument.name" cssClass="inputXXL"/>
										</msgpt:InputFilter>
									</msgpt:TableItem>
									<!-- Metatags -->
									<msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW" >
										<msgpt:TableItem label="page.label.document.metatags">
											<msgpt:InputFilter type="simpleName">
												<form:textarea id="documentMetatags" path="rationalizerDocument.metatags" cssClass="inputXXL" rows="1" />
											</msgpt:InputFilter>
										</msgpt:TableItem>
									</msgpt:IfAuthGranted>
									<!-- Description -->
									<msgpt:TableItem label="page.label.description">
										<msgpt:InputFilter type="description">
											<form:textarea path="rationalizerDocument.description" cssClass="inputXXL" rows="2" />
										</msgpt:InputFilter>
									</msgpt:TableItem>
								</msgpt:DataTable>

								<!-- DOCUMENT METADATA -->
								<c:if test="${not empty command.formWrapper}">

									<msgpt:DataTable labelPosition="top" theme="minimal" multiColumn="true">

										<c:forEach var="currentItem" items="${command.formWrapper.metadataFormItemsList}" varStatus="itemStat">
											<msgpt:TableItem  cssClass="metadataRowTD_${itemStat.index}" label="${currentItem.itemDefinition.name}" style="position: relative; ${not empty currentItem.itemDefinition.parentItemOrder ? 'display: none;' : ''}" mandatory="${currentItem.itemDefinition.isManadatory}" valign="top">
												<c:set var="bindingPath" value="formWrapper.metadataFormItemsList[${itemStat.index}].value" />
												<c:set var="currentValue" value="${command.formWrapper.metadataFormItemsList[itemStat.index].value}" />

												<c:if test="${not empty currentItem.itemDefinition.description}">
													<div style="position: absolute; left: -28px; top: -18px;" class="detailTip" title="|<div class='detailTipText'>${currentItem.itemDefinition.descriptionDisplay}</div>">
													<span class="fa-stack" style="font-size: 10px; color: #777;">
													  <i class="fa fa-circle fa-stack-2x"></i>
													  <i class="fa fa-info fa-stack-1x fa-inverse"></i>
													</span>
													</div>
												</c:if>

												<div id="valueViewContainer_${currentItem.id}" class="valueViewContainer">
													<c:choose>
														<c:when test="${empty currentValue}">
															<i><fmtSpring:message code="page.label.none"/></i>
														</c:when>
														<c:when test="${currentItem.itemDefinition.typeId == itemType_textEditor}">
															<c:out value="${currentValue}" escapeXml="false" />
														</c:when>
														<c:otherwise>
															<c:out value="${currentValue}" />
														</c:otherwise>
													</c:choose>
												</div>
											</msgpt:TableItem>
										</c:forEach>

									</msgpt:DataTable>

								</c:if>
							</div> <!-- END NAME, DOCUMENT TAGS, DESCRIPTION, DOCUMENT METADATA -->

						</div> <!-- END METADATA CONTAINER -->

						<!-- CONTENT -->
						<div id="contentDiv" style="border-top: 1px solid #ddd; padding: 15px 60px; position: relative">
							<div id="editorContainer" style="width: 1040px; position: absolute; display: none; background-color: #fff; z-index: 1;">
								<textarea class="mceEditor_rationalizerContent" id="editor_rationalizerContent">
								</textarea>
							</div>

							<msgpt:DataTable labelPosition="top">
								<c:choose>
									<c:when test="${empty command.rationalizerDocument.rationalizerDocumentContents}">
										<msgpt:TableItem>
											<div class="InfoSysContainer_info" style="margin-top: 20px;">
												<fmtSpring:message code="page.text.no.rationalizer.document.content.exists"/>
											</div>
										</msgpt:TableItem>
									</c:when>
									<c:otherwise>

										<c:forEach var="currentContent" items="${command.documentContents}" varStatus="contentStat">
											<msgpt:TableItem cssClass="contentContainer">
												<div id="documentContentContainer_<c:out value="${currentContent.buildElasticSearchGuid()}" />" class="documentContentContainer">
													<table width="100%" class="formBorderlessTable" style="${contentStat.index == 0} ? 'margin-top: 10px;' : ''"><tbody><tr>
														<td class="contentActionContainer" width="2%" style="padding: 0px; vertical-align: top;">

															<!-- INDEX INDICATOR -->
															<div class="contentOrderIndicatorContainer">
																<div class="contentOrderIndicator">
																	<c:out value="${currentContent.order}" />
																</div>
															</div>
															<form:hidden path="elasticSearchGuids[${contentStat.index}]" cssClass="input2digit" />
															<form:hidden id="orderInput_${currentContent.buildElasticSearchGuid()}" path="documentContentsOrdersList[${contentStat.index}]" cssClass="input2digit" />

														</td>

														<td width="2%" style="padding: 6px 6px 0px 20px; align-content:center; vertical-align: top;">

															<c:choose>
																<c:when test="${not empty currentContent.zoneConnector}">
																	<i class="fas fa-square-z" style="color:#6d3075; font-size:21px; " aria-hidden="true"
																	   data-toggle="tooltip" data-html="true" data-placement="right"
																	   title="<c:out value="${currentContent.zoneConnector}" />"></i>
																</c:when>
																<c:otherwise>
																	<i class="fas fa-square-z" style="color:#ddd; opacity:1; font-size:21px; " aria-hidden="true"></i>
																</c:otherwise>
															</c:choose>

														</td>


														<td style="padding: 0px; position: relative;">

															<!-- TEXT CONTENT -->
															<div class="contentViewEditContainer" style="margin: 0px 0px 0px 18px; cursor: pointer;">
																<c:if test="${not empty currentContent.rationalizerSharedContent}">
																	<div class="sharedContentContainer">
																		<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>
																			<td align="left">
																				<i class="far fa-repeat-alt fa-mp-btn" style="padding-right: 12px;"></i><c:out value="${currentContent.rationalizerSharedContent.name}" />
																			</td>
																			<td class="nonContentSelectEle" width="1%" align="right" style="white-space: nowrap; padding-left: 8px; padding-right: 8px;">
																				<c:set var="currentSharedDocumentCount" value="${currentContent.rationalizerSharedContent.documentCount}" />
																				<c:if test="${currentSharedDocumentCount > 1}">
																					<div id="sharedContentToggleLeftContainer_<c:out value="${currentContent.rationalizerSharedContent.buildElasticSearchGuid()}" />" class="sharedContentToggleLeftContainer" class="fa-mp-container" style="display: inline-block;">
																						<i class="fas fa-angle-left" style="padding-right: 4px;"></i>
																					</div>
																					<div id="sharedContentToggleMenuContainer_<c:out value="${currentContent.rationalizerSharedContent.buildElasticSearchGuid()}" />" class="sharedContentToggleMenuContainer" style="display: inline-block; padding: 0px 8px;">
																						<fmtSpring:message code="page.text.content.shared.in" />&nbsp;
																							${currentSharedDocumentCount}
																						&nbsp;<fmtSpring:message code="page.text.documents" />
																						<i class="fa fa-caret-down" style="padding-left: 6px;"></i>
																					</div>
																					<div id="sharedContentToggleRightContainer_<c:out value="${currentContent.rationalizerSharedContent.buildElasticSearchGuid()}" />" class="sharedContentToggleRightContainer" class="fa-mp-container" style="display: inline-block;">
																						<i class="fas fa-angle-right fa-mp-btn" style="padding-left: 4px;"></i>
																					</div>
																				</c:if>
																			</td>
																		</tr></table>
																	</div>
																</c:if>
																<div class="contentViewContainer" style="position: relative; padding: 8px 0px; word-break: break-word;">
																	<c:out value="${command.textContent[contentStat.index]}" escapeXml="false" />
																</div>
																<form:textarea path="markupContent[${contentStat.index}]" cssStyle="display: none;" cssClass="input550 markupContentArea contentEditInput" />
																<form:hidden id="contentActionBinding_${currentContent.buildElasticSearchGuid()}" path="contentActions[${contentStat.index}]" cssClass="contentActionBinding"/>
																<form:checkbox class="selectedContentIdBinding" path="selectedIds" value="${currentContent.buildElasticSearchGuid()}" style="display: none;" />
															</div>

														</td>
													</tr></tbody></table>
												</div>

											</msgpt:TableItem>
										</c:forEach>

									</c:otherwise>
								</c:choose>
							</msgpt:DataTable>

						</div>

						<div class="documentContentActionBar nonContentSelectEle" style="display: none; position: absolute; left: 0px; top: 0px; z-index: 2; white-space: nowrap;">
							<div style="position: absolute; left: 49px; top: 8px; width: 0px; height: 0px;"><i class="fa fa-caret-left" style="font-size: 20px; color: #ddd;"></i></div>
							<div style="margin-left: 55px; opacity: 0.95; border: 1px solid #ccc; padding: 6px; padding-left: 12px; background-color: #eee; vertical-align: middle; border-radius: 3px; white-space: nowrap;">
								<div style="display: inline-block; vertical-align: middle; white-space: nowrap;">
								<span id="contentActionIconsContainer" style="white-space: nowrap;">
									<i id="editContentBtn" class="fa fa-pencil fa-mp-button actionBarIcon" showDe title="${msgpt:getMessage('page.tooltip.edit.content')}"></i>
									<i id="viewContentBtn" style="display: none;" class="fa fa-eye fa-mp-button actionBarIcon"  title="${msgpt:getMessage('page.tooltip.view.content')}"></i>
									<i id="orderDownBtn" class="fa fa-caret-down fa-mp-button actionBarIcon"  title="${msgpt:getMessage('page.tooltip.move.down')}"></i>
									<i id="orderUpBtn" class="fa fa-caret-up fa-mp-button actionBarIcon"  title="${msgpt:getMessage('page.tooltip.move.up')}"></i>
									<i id="addContentBtn" class="fa fa-plus fa-mp-button actionBarIcon"  title="${msgpt:getMessage('page.tooltip.add.content')}"></i>
									<i id="splitContentBtn" class="fa fa-page-break fa-mp-disabled fa-mp-button actionBarIcon"  title="${msgpt:getMessage('page.tooltip.split.content')}" disabled="disabled"></i>
									<i id="mergeContentBtn" class="fa fa-code-merge fa-mp-disabled fa-mp-button actionBarIcon"  title="${msgpt:getMessage('page.tooltip.merge.content')}" disabled="disabled"></i>
									<c:if test="${rationalizerApplication.synchronized}">
										<i id="contentCompareBtn" class="far fa-clipboard-list fa-mp-button actionBarIcon"  title="${msgpt:getMessage('page.tooltip.compare.content')}"></i>
									</c:if>
									<i id="contentHistoryButton" class="fa fa-history fa-mp-button actionBarIcon"  title="${msgpt:getMessage('page.tooltip.history.content')} "></i>
								</span>
									<div style="display: inline-block; border-left: 1px solid #bbb; padding: 0px; margin: 0px 8px; height: 20px; position: relative; top: 4px;"></div>
									<c:if test="${appliesContentMetadata}">
										<i id="viewContentMetaBtn" class="fa fa-info fa-mp-button actionBarIcon"  title="${msgpt:getMessage('page.tooltip.view.content.metadata')}"></i>
										<i id="editContentMetadataBtn" class="fa fa-cog fa-mp-button actionBarIcon"  title="${msgpt:getMessage('page.tooltip.edit.content.metadata')}"></i>
									</c:if>
								</div>
								<div style="display: inline-block; margin-left: 40px; vertical-align: middle;">
									<input title="${msgpt:getMessage('page.label.delete')}" type="button" id="deleteContentBtn" onclick="deleteDocumentContent()" style="display: none;" />
								</div>
							</div>
						</div>

						<!-- BOTTOM BUFFER -->
						<div style="height: 200px; width: 1px;"></div>
					</div>
				</div>

				<div class="label">
					<table width="100%" class="formBorderlessTable"><tbody><tr>
						<!-- Buttons -->
						<td></td>
						<td width="1%" align="right" style="padding: 12px 12px; white-space: nowrap;">
							<msgpt:Button label="page.label.cancel" URL="javascript:closeIframe_local()" />
						</td>
						<td width="1%" align="left" style="padding: 12px 12px; white-space: nowrap;">
							<div class="btn-group ml-3">
								<button type="button" class="btn btn-primary btn-placeholder" disabled>
									<i class="fad fa-spinner-third fa-lg fa-spin align-middle mx-3"
									   aria-hidden="true"></i>
								</button>
								<select id="rationalizerEditSaveBtnGroup02"
										class="complex-combobox-select"
										aria-label="${msgpt:getMessage("page.label.actions")}"
										data-toggle="complex-combobox"
										data-combobox-class="btn-primary">

									<option id="btnSaveAndBack02" value="saveandgoback"
											data-icon="far fa-arrow-left"
											data-customitem="true"><fmtSpring:message
											code="page.label.save"/>
									</option>
									<option id="btnSaveAndStay02" value="saveandstay"
											data-icon="fas fa-save"><fmtSpring:message
											code="page.label.save.continue"/>
									</option>
								</select>
								<button type="button"
										class="btn btn-primary dropdown-toggle dropdown-toggle-split"
										data-toggle="dropdown" aria-haspopup="true"
										aria-expanded="false" disabled>
        <span class="sr-only"><fmtSpring:message
				code="page.text.toggle.dropdown"/></span>
								</button>
							</div>
						</td>
						<td></td>
					</tr></tbody></table>
				</div>

				<div id="popupHeightExtension" style="height: 400px; width: 1px; display: none;"></div>


				<!--  POPUP DATA -->
				<div id="actionSpecs" style="display: none;">
					<!-- ACTIONS POPUP DATA -->
					<!-- BULK METADATA UPDATE -->
					<div id="actionSpec_2" submitId="2" contentWidth="500px">
						<div id="actionTitle_2"><fmtSpring:message code="page.label.modify.content.metadata"/></div>
						<div id="actionInfo_2"><fmtSpring:message code="page.text.modify.content.metadata"/></div>
						<div id="actionModifyContent_2"></div>
					</div>
					<!-- BULK CONTENT DELETE -->
					<div id="actionSpec_4" submitId="4" type="simpleConfirm" contentWidth="400px"> <!-- Delete Content -->
						<div id="actionTitle_4"><fmtSpring:message code="page.label.confirm.delete.rationalizer.content"/></div>
						<div id="actionInfo_4"><fmtSpring:message code="page.text.delete.selected.rationalizer.content"/></div>
					</div>
				</div>

				<!-- POPUP INTERFACE -->
				<msgpt:Popup id="actionPopup" theme="minimal">
					<div id="actionPopupInfoFrame">
						<div id="actionPopupInfo">&nbsp;</div>
					</div>
					<div id="actionPopupModifyContent" style="padding: 3px 15px 6px 15px;" align="left">

						<div id="modifyContentMetadataConnectorValuesContainer" style="white-space: nowrap; margin-top: 8px;">
							<div style="display: inline-block; position: relative; vertical-align: middle; min-width: 100px;">
								<fmtSpring:message code="page.label.metadata" />
							</div>
							<div style="display: inline-block; position: relative; vertical-align: middle;">
								<form:select id="modifyContentMetadataConnectorValuesSelect" path="modifyContentMetadataConnectorValue" items="${modifyContentFormItemDefinitions}" itemValue="primaryConnector" itemLabel="name" cssClass="inputXL style_select" onchange="requestMetadataValues()" />
							</div>
						</div>
						<div style="margin-top: 8px; white-space: nowrap; ">
							<div style="display: inline-block; position: relative; vertical-align: middle; min-width: 100px;">
								<fmtSpring:message code="page.label.value" />
							</div>
							<div style="display: inline-block; position: relative; vertical-align: middle;">
								<div id="metadataInputContainer_loading" style="white-space: nowrap;">
									<i class="fa fa-cog fa-spin fa-fw"></i>
									<span class="sr-only">Loading...</span>
								</div>
								<!-- TEXT -->
								<div id="metadataInputContainer_text" style="white-space: nowrap;">
									<input id="metadataInput_text" class="inputXL" onchange="updateModifyMetadataValueBinding(this)" onkeyup="updateModifyMetadataValueBinding(this)" />
								</div>
								<!--  SELECT -->
								<div id="metadataInputContainer_select" style="white-space: nowrap;">
									<select id="metadataInput_select" class="style_select inputXL" onchange="updateModifyMetadataValueBinding(this)">
									</select>
								</div>
								<!-- MULTISELECT -->
								<div id="metadataInputContainer_multiselect" style="white-space: nowrap;">
									<div id="metadataInput_multiselect" class="style_multiselect inputXL">
									</div>
								</div>
							</div>
							<input type="hidden" id="metadataSelectValues" value="" />
							<form:hidden id="modifyContentMetadataReplaceValue" path="modifyContentMetadataReplaceValue" />
						</div>
					</div>
					<div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
						<span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#" label="page.label.cancel" disabled="true" /></span>
						<span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel" /></span>
						<span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#" label="page.label.save" disabled="true" /></span>
						<span id="continueBtnEnabled" type="doSubmit"><msgpt:Button URL="#" label="page.label.save" primary="true" /></span>
					</div>
				</msgpt:Popup>

			</c:if>
		</form:form>
	</msgpt:BodyNew>
</msgpt:Html5>