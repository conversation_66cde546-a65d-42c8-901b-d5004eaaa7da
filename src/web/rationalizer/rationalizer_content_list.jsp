<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.rationalizer" extendedScripts="true">

        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:CalendarIncludes/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

        <msgpt:Script src="includes/javascript/handlebars/handlebars-v4.7.8.js"/>

        <msgpt:Script src="rationalizer/javascript/jquery.rationalizerUtil.js"/>

        <msgpt:Script>
            <script>

                function initMetadataNameDisplay () {
                    if ($('#modifyContentMetadataConnectorValuesSelect') != undefined) {
                        $('#zoneConnectorDisplay').val($('#modifyContentMetadataConnectorValuesSelect').text());
                    }
                }

                function initMetadataValueChangePopup() {
                    requestMetadataValues();
                }


                function updateModifyMetadataValueBinding(ele) {

                    var bindingValueArray = new Array();
                    if ( $(ele).is('.style_multiselect_binding') ) {
                        var metadataValuesArray = $('#metadataSelectValues').val().split(',');
                        $('#metadataInput_multiselect').find('.style_multiselect_binding:checked').each( function() {
                            bindingValueArray[bindingValueArray.length] = metadataValuesArray[$(this).val()];
                        });
                    } else if ( $(ele).is('#metadataInput_select') ) {
                        var metadataValuesArray = $('#metadataSelectValues').val().split(',');
                        bindingValueArray[bindingValueArray.length] = metadataValuesArray[$(ele).val()];
                    } else {
                        bindingValueArray[bindingValueArray.length] = $(ele).val();
                    }

                    $('#modifyContentMetadataReplaceValue').val( bindingValueArray.join(',') );
                }

                function requestMetadataValues() {

                    function processMetadataValuesResponse(data) {

                        $('#metadataInputContainer_loading').hide();

                        if ( data.input_type == 3 ) {

                            var inputHTML = "";
                            for ( var i = 0; i < data.values.length; i++ ) {
                                inputHTML += "<option id=\"metadataSelectOption_" + i + "\" value=\"" + i + "\">" +
                                    data.values[i] +
                                    "</option>";
                            }
                            $('#metadataSelectValues').val( data.values.join(',') );
                            $('#metadataInput_select').replaceAllOptions(inputHTML).change();
                            $('#metadataInputContainer_select').show();

                        } else if ( data.input_type == 8 ) {

                            var inputHTML = "";
                            for ( var i = 0; i < data.values.length; i++ ) {
                                inputHTML += "<div id=\"metadataSelectOption_" + i + "\">" +
                                    "<input value=\"" + i + "\" type=\"checkbox\" " +
                                    "class=\"style_multiselect_binding\" onclick=\"updateModifyMetadataValueBinding(this);\" />" +
                                    "<span>" + data.values[i] + "</span>" +
                                    "</div>";
                            }
                            $('#metadataSelectValues').val( data.values.join(',') );
                            $('#metadataInput_multiselect').replaceAllOptions(inputHTML);
                            $('#metadataInputContainer_multiselect').show();

                        } else {

                            $('#metadataInputContainer_text').change().show();

                        }
                    }

                    $("[id^='metadataInputContainer']").hide();
                    $('#metadataInputContainer_loading').show();
                    $('#modifyContentMetadataReplaceValue').val('');

                    var metadataConnector = $('#modifyContentMetadataConnectorValuesSelect').val();
                    initMetadataNameDisplay();
                    $.ajax({
                        type: "GET",
                        url: context + "/getObjectInfo.form?type=rationalizerQueryValues&sub_type=" + metadataConnector + "&rationalizerApplicationId=" + getParam('rationalizerApplicationId') + "&objectId=3&cacheStamp=" + (stampDate.getTime()),
                        dataType: "json",
                        success: function(data) {
                            processMetadataValuesResponse(data);
                        }
                    });

                }

                var iFramePopup_fullFrameAttr_cust = JSON.parse(JSON.stringify(iFramePopup_fullFrameAttr));
                iFramePopup_fullFrameAttr_cust.width = 1240;
                var iFramePopup_fullFrameAttr_meta_cust = JSON.parse(JSON.stringify(iFramePopup_fullFrameAttr));
                iFramePopup_fullFrameAttr_meta_cust.width = 1024;

                var $updateBtn, $deleteBtn, $exportMenu, $actionMenu, $widgetToggle;

                $(function () {

                    if (${application.treeStructureEmpty}) {
                        localStorage.setItem("msgpt_persistedValue_rationalizerNavTreeSelect_" + getParam('rationalizerApplicationId'), "-1");
                    }

                    $updateBtn = $('#updateBtn');
                    $deleteBtn = $('#deleteBtn');
                    $exportMenu = $('#exportMenu');
                    $actionMenu = $('#actionMenu');
                    $widgetToggle = $('#widgetToggleBtn');

                    $(document).ready(function(){
                        $('[data-toggle="tooltip"]').click(function () {
                            $('[data-toggle="tooltip"]').tooltip("hide");

                        });
                    });

                    $("#rationalizerApplicationToggle").styleActionElement({
                        labelAlign: true,
                        tagCloudFilter: true,
                        tagCloudType: 11,
                        tagCloudPopupPosition: "bottom-left",
                        maxItemsInList: 8
                    });

                    $(".style_select").styleActionElement({labelAlign: true});

                    var stampDate = new Date();

                    $.ajax({
                        type: "GET",
                        url: context + "/getObjectInfo.form" +
                        "?type=rationalizerCrumbPath" +
                        "&objectId=" + getSelectedNavTreeNodeId() +
                        "&rationalizerApplicationId=" + getParam('rationalizerApplicationId') +
                        "&cacheStamp=" + (stampDate.getTime()),
                        dataType: "json",
                        success: function (data) {
                            $('#currentNavValueContainer').html(data.crumb_values && data.crumb_values.length > 0 ?
                                data.crumb_values[data.crumb_values.length - 1] :
                                client_messages.text.master);
                        }
                    });

                    $("[id*='itemInProcess']").actionStatusPolling({
                        itemLabel: client_messages.text.rationalizer_documents_report,
                        type: 'rationalizerdocumentreport',
                        postItemInit: function ($item) {
                            var $option = $('#' + $item.attr('id')),
                                $select = $option.parent(),
                                itemIndex = $select.children().index($option),
                                $dropdownItem = $select.siblings('.dropdown').find('.dropdown-content').children().eq(itemIndex);

                            if ($item.attr('id').indexOf('itemInProcess') < 0)
                                $dropdownItem.find('.progress-loader').remove();
                            else
                                $dropdownItem.prepend('<div class="progress-loader mr-2">' +
                                    '<i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>' +
                                    '</div>');

                        },
                        onInit: function (o) {
                            var $option = $('#' + o.option.attr('id')),
                                $select = $option.parent(),
                                itemIndex = $select.children().index($option),
                                $dropdown = $select.siblings('.dropdown'),
                                $dropdownItem = $dropdown.find('.dropdown-content').children().eq(itemIndex);

                            $dropdownItem.prepend('<div class="progress-loader mr-2">' +
                                '<i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>' +
                                '</div>');


                            // TODO: REVIEW RENDER/PAINT ISSUE WITH MULTIPLE SPINNING (ANIMATED) ELEMENTS
                            /*$dropdown.children('.dropdown-toggle').prepend('<div class="progress-loader mr-2 bg-transparent p-0">' +
                                '<i class="progress-loader-icon far fa-spinner-third text-white" aria-hidden="true"></i>' +
                                '</div>');*/
                        }
                    });
                    $('#backgroundTasksPlaceholder').refresh();

                    $('#newApplicationMetatags,#applicationPropertiesMetatags').tagCloud({
                        tagCloudType: 11,
                        inputType: 'metatags'
                    });

                    $('#addApplicationBtn').click(function () {
                        actionSelected('1')
                    });

                    $('#applicationPropertiesBtn').click(function () {
                        if ($(this).find('.fa-mp-disabled').length != 0)
                            return;
                        $.ajax({
                            type: "GET",
                            url: context + "/getObjectInfo.form" +
                                "?type=rationalizerApplicationProperties" +
                                "&rationalizerApplicationId=" + getParam('rationalizerApplicationId') +
                                "&cacheStamp=" + (stampDate.getTime()),
                            dataType: "json",
                            success: function (data) {
                                $('#currentApplicationName').val(data.currentApplicationName);
                                $('#currentApplicationMetatags').val(data.currentApplicationMetatags);
                            }
                        });
                        //actionSelected('3');
                        var redirectURL = context + "/rationalizer/rationalizer_application_setup.form?rationalizerApplicationId=" + ${param.rationalizerApplicationId} +'&tk=${param.tk}';
                        window.location = redirectURL;
                    });

                    $('#parsedDocumentMetadataTemplateBtn').click(function () {
                        iFrameAction('7');
                    });
                    $('#parsedContentMetadataTemplateBtn').click(function () {
                        iFrameAction('8');
                    });
                    $('#applicationVisibilityBtn').click(function () {
                        iFrameAction('12');
                    });
                    $('#applicationNavigationBtn').click(function () {
                        iFrameAction('13');
                    });
                    $('#workflowBtn').click(function () {
                        iFrameAction('14');
                    });
                    $('#metadataPointsOfInterestTemplateBtn').click(function () {
                        iFrameAction('15');
                    });

                    $("#queryFilterSelect").each(function () {
                        var currentSelect = $(this);
                        $(currentSelect).styleActionElement({
                            isAsync: true,
                            getItemsAsync: {
                                getItemsURL: context + "/getDataForMultiselectMenu.form",
                                fnExtServerParams: function (o) {
                                    return [
                                        {"name": "type", "value": "rationalizer_metadata_definitions"},
                                        {
                                            "name": "rationalizerApplicationId",
                                            "value": getParam('rationalizerApplicationId')
                                        }
                                    ];
                                }
                            }
                        });
                    });

                    $('#documentViewBtn').on('click', function () {
                        window.location.href = context + "/rationalizer/rationalizer_documents_list.form?rationalizerApplicationId=" + getParam('rationalizerApplicationId') + "&tk=" + getParam('tk') + "&reset=true";
                    });

                    $('#contentViewBtn').on('click', function () {
                        window.location.href = context + "/rationalizer/rationalizer_content_list.form?rationalizerApplicationId=" + getParam('rationalizerApplicationId') + "&tk=" + getParam('tk');
                    });

                    $("#flipToggle_documentContentViews").each(function () {
                        var currentBinding = $(this);
                        $(this).iButton({
                            labelOn: $(currentBinding).attr('title').split(';')[0],
                            labelOff: $(currentBinding).attr('title').split(';')[1],
                            resizeHandle: false,
                            resizeContainer: "auto",
                            change: function () {
                                window.location.href = context + "/rationalizer/rationalizer_documents_list.form?rationalizerApplicationId=" + getParam('rationalizerApplicationId') + "&tk=" + getParam('tk');
                            }
                        });
                    });

                    $("#flipToggle_clonedRationalizerApp").each(function () {
                        var currentBinding = $(this);
                        $(this).iButton({
                            labelOn: $(currentBinding).attr('title').split(';')[0],
                            labelOff: $(currentBinding).attr('title').split(';')[1],
                            resizeHandle: false,
                            resizeContainer: "auto",
                            change: function () {
                                targetAppId = ${not empty clonedApplication? clonedApplication.id : (not empty parentApplication? parentApplication.id : -1)};
                                window.location.href = context + "/rationalizer/rationalizer_content_list.form?rationalizerApplicationId=" + targetAppId + "&tk=" + getParam('tk') + "&reset=true";
                            }
                        });
                    });

                    // Hide widget based on persisted state
                    if ($widgetToggle.is('.active')) {

                        $widgetToggle.children('.btn-text').text('Compress');
                        $widgetToggle.attr('aria-pressed', true);

                        updateWidgetDisplay(false);

                        $('i[name="zone_connector_icon"]').attr('hidden', 'true');
                        $('div[name="zone_connector_value"]').removeAttr('hidden');

                    }

                    $widgetToggle.removeClass('focus');
                    updatePersistedClass($widgetToggle);

                    // Init widget toggle
                    $widgetToggle.on('click', function () {

                        _.defer(function () {

                            var $btnText = $widgetToggle.children('.btn-text'),
                                $btnIcon = $widgetToggle.children('.btn-icon');

                            if ($widgetToggle.is('.active')) {

                                $btnText.text(client_messages.text.compress);
                                $btnIcon.removeClass('fa-expand-wide').addClass('fa-compress-wide');

                                updatePersistedClass($btnIcon);
                                updatePersistedClass($widgetToggle);

                                $('i[name="zone_connector_icon"]').attr('hidden', 'true');
                                $('div[name="zone_connector_value"]').removeAttr('hidden');

                            } else {

                                $btnText.text(client_messages.text.expand);
                                $btnIcon.addClass('fa-expand-wide').removeClass('fa-compress-wide');

                                updatePersistedClass($btnIcon);
                                updatePersistedClass($widgetToggle);

                                $('i[name="zone_connector_icon"]').removeAttr('hidden');
                                $('div[name="zone_connector_value"]').attr('hidden', 'true');


                            }

                            updateWidgetDisplay(false);

                        });

                    });

                    var $searchTagCloud = $('#listSearchInput[data-toggle="tagcloud"]');

                    $searchTagCloud.tagCloud({
                        tagCloudType: $searchTagCloud.data('cloud-type'),
                        rationalizerApplicationId: $searchTagCloud.data('rationalizer-application-id'),
                        inputType: 'search',
                        rightOffsetAdj: 16,
                        topOffsetAdj: 12,
                        popupLocation: 'bottom-left',
                        afterInputValueChange: function (o, val) {
                            $searchTagCloud.keyup();
                        }
                    });
                    var formId = ${application.treeStructureEmpty} ? "-9" : getSelectedNavTreeNodeId();
                    var navTreeSrc = "${contextPath}/rationalizer/rationalizer_navigation_widget.form?tk=" + getParam('tk') +
                        "&rationalizerApplicationId=" + getParam('rationalizerApplicationId') + "&formItemId=" + formId + "&isContentView=true";
                    $('#rationalizerNavigationWidget').attr('src', navTreeSrc);

                    // Onload asset popup
                    if (getParam('rationalizerDocumentId') && getParam('rationalizerDocumentId') != "")
                        iFrameView(context + '/rationalizer/rationalizer_document_edit.form'
                            + "?rationalizerApplicationId=" + getParam('rationalizerApplicationId')
                            + '&rationalizerDocumentId=' + getParam('rationalizerDocumentId'),
                            'rationalizerContentList', 'null');

                    initMetadataValueChangePopup();

                });

                function updateWidgetDisplay(reloadWidget) {

                    var $widgetContainer = $('#rationalizerNavigationWidgetContainer');

                    if ($widgetToggle.is('.active')) {

                        $widgetContainer.addClass('d-none');

                    } else {

                        $widgetContainer.removeClass('d-none');

                        if (reloadWidget && window.frames['rationalizerNavigationWidget'])
                            window.frames['rationalizerNavigationWidget'].location = context + "/touchpoints/" + $('#rationalizerNavigationWidget').attr('src');

                    }

                }

                function toggleRationalizerApplication(select) {
                    var targetAppId = $(select).find('option:selected').val();

                    if (targetAppId > 0) {
                        window.location.href = context + "/rationalizer/rationalizer_content_list.form?rationalizerApplicationId=" + targetAppId + "&tk=" + getParam('tk') + "&reset=true";
                    }
                }

                // *********  LIST TABLE FUNCTIONS: START  *********
                function iFrameView(path, eleId, e) {
                    if (e != undefined) {
                        if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
                            $("#" + eleId).iFramePopup($.extend({
                                src: path,
                                displayOnInit: true,
                                id: "rationalizerDocumentFrame",
                                appliedParams: {tk: "${param.tk}"},
                                beforePopupClose: function () {
                                    $('.mce-custompanel,#iFramePopup_brandCheck').remove();
                                    rebuildListTable(true);
                                }
                            }, iFramePopup_fullFrameAttr_cust));
                        }
                        e.preventDefault ? e.stopPropagation() : e.returnValue = false;
                    }
                }

                function cleariFramePopupParams() {
                    var currentURL = window.document.URL;
                    getTopFrame().location.href = currentURL;
                }

                function toggleFilter(select) {
                    rebuildListTable(false);
                }

                function rebuildListTable(maintainCurrentPage) {
                    $('#rationalizerContentList').DataTable().ajax.reload(null, !maintainCurrentPage);
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    if (${hasAssignedWorkflow}) {
                        obj.columns = [
                            {
                                columnMap: 'name',
                                columnName: client_messages.text.document_name,
                                sort: true,
                                width: '20%',
                                colVisToggle: false
                            },
                            {
                                columnMap: 'order',
                                columnName: client_messages.text.order,
                                sort: false,
                                width: '1%',
                                colVisToggle: false
                            },
                            {
                                columnMap: 'state',
                                columnName: client_messages.text.state,
                                sort: false,
                                width: '3%',
                                colVisToggle: false
                            },
                            {
                                columnMap: 'assignee',
                                columnName: client_messages.text.assignee,
                                sort: true,
                                width: '8%',
                                colVisToggle: false
                            },
                            {
                                columnMap: 'contentStatus',
                                columnName: client_messages.text.status,
                                sort: true,
                                width: '8%',
                                colVisToggle: false
                            },
                            {
                                columnMap: 'content',
                                columnName: client_messages.text.content,
                                sort: false,
                                width: '55%',
                                colVisToggle: false
                            },
                            {
                                columnMap: 'zoneConnector',
                                columnName: '<div name=\"zone_connector_value\">' + client_messages.text.zone_connector + '</div>',
                                sort: false,
                                width: '5%',
                                colVisToggle: false
                            }
                        ];
                    } else {
                        obj.columns = [
                            {
                                columnMap: 'name',
                                columnName: client_messages.text.document_name,
                                sort: true,
                                width: '29%',
                                colVisToggle: false
                            },
                            {
                                columnMap: 'order',
                                columnName: client_messages.text.order,
                                sort: false,
                                width: '1%',
                                colVisToggle: false
                            },
                            {
                                columnMap: 'content',
                                columnName: client_messages.text.content,
                                sort: false,
                                width: '68%',
                                colVisToggle: false
                            },
                            {
                                columnMap: 'zoneConnector',
                                columnName: '<div name=\"zone_connector_value\">' + client_messages.text.zone_connector + '</div>',
                                sort: false,
                                width: '2%',
                                colVisToggle: false
                            }
                        ];
                    }

                    return obj;
                }

                function getAsyncExtParams() {
                    var stampDate = new Date();
                    var formId = ${application.treeStructureEmpty} ? "-9" : getSelectedNavTreeNodeId();
                    var obj = [
                        {"name": "listTableType", "value": "41"},
                        {
                            "name": "rationalizerApplicationId",
                            "value": getParam('rationalizerApplicationId') != "" ? getParam('rationalizerApplicationId') : -1
                        },
                        {"name": "rationalizerDocFormItemId", "value": formId},
                        {
                            "name": "rationalizerContentListAssignmentFilterId",
                            "value": $('#contentListAssignmentFilter').val()
                        },
                        {
                            "name": "rationalizerContentListContentStatusFilterId",
                            "value": $('#contentListContentStatusFilter').val()
                        },
                        {"name": "rationalizerContentListStateFilterId", "value": $('#contentListStateFilter').val()},
                        {"name": "displayMode", "value": "full"},
                        {"name": "cacheStamp", "value": stampDate.getTime()}
                    ];
                    return obj;
                }

                function postListDrawCallback(nTable) {
                    $(nTable).find('.detailTip').each(function () {
                        initTip(this);
                    });

                    if ($widgetToggle.is('.active')) {
                        $('i[name="zone_connector_icon"]').attr('hidden', 'true');
                        $('div[name="zone_connector_value"]').removeAttr('hidden');
                    } else {
                        $('i[name="zone_connector_icon"]').removeAttr('hidden').tooltip();
                        $('div[name="zone_connector_value"]').attr('hidden', 'true');
                    }
                }

                function postListRenderFlagInjection(oObj) {
                    var iFrameId = "rationalizer-content-id";
                    var iFrameSrc = context + "/rationalizer/rationalizer_content_list_detail.form?rationalizerContentId=" + oObj.aData.dt_RowId;

                    if ($('#listSearchInput').val() != "")
                        iFrameSrc += "&sSearch=" + $('#listSearchInput').val();

                    var binding = oObj.aData.binding;

                    var text = oObj.aData.name;
                    text += "<input id='iFrameId' value=" + iFrameId + " type='hidden' class='iframe-data' />";
                    text += "<input id='iFrameSrc' value=" + iFrameSrc + " type='hidden' class='iframe-data' />";

                    //Selection check select
                    text += binding;
                    //Selection permission flags
                    if (oObj.aData.flags.canUpdate)
                        text += "<input type='hidden' id='canUpdate_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReassign)
                        text += "<input type='hidden' id='canReassign_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReleaseForApproval)
                        text += "<input type='hidden' id='canReleaseForApproval_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canApprove)
                        text += "<input type='hidden' id='canApprove_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReject)
                        text += "<input type='hidden' id='canReject_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canDiscard)
                        text += "<input type='hidden' id='canDiscard_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canCreateWorkingCopy)
                        text += "<input type='hidden' id='canCreateWorkingCopy_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.hasNoStepForWorkflow)
                        text += "<input type='hidden' id='hasNoStepForWorkflow_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.workflowOwner)
                        text += "<input type='hidden' id='workflowOwner_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canDeactivate)
                        text += "<input type='hidden' id='canDeactivate_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canAddTask)
                        text += "<input type='hidden' id='canAddTask_" + oObj.aData.dt_RowId + "' />";

                    return text;
                }

                // List actions: Edit
                function iFrameAction(actionId) {
                    var rationalizerDocumentId;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        rationalizerDocumentId = $(this).attr('documentId');
                    });

                    if (actionId == '1') {
                        $('#actioniFrame_' + actionId).iFramePopup($.extend({
                            src: context + "/rationalizer/rationalizer_document_edit.form",
                            title: client_messages.title.edit_document,
                            displayOnInit: true,
                            id: "rationalizerDocumentEditFrame",
                            appliedParams: {
                                tk: "${param.tk}",
                                rationalizerDocumentId: rationalizerDocumentId
                            },
                            beforePopupClose: function () {
                                rebuildListTable(true);
                            }
                        }, iFramePopup_fullFrameAttr_cust));
                    } else if (actionId == '7') {
                        $('#parsedDocumentMetadataTemplateBtn').iFramePopup($.extend({
                            src: context + "/metadata/metadata_form_definition_edit.form",
                            title: client_messages.title.document_metadata_template,
                            displayOnInit: true,
                            id: "parsedMetadataDocumentDefinitionFrame",
                            appliedParams: {
                                'tk': "${param.tk}",
                                'formDefinitionId': '${parsedDocumentMetadataFormDefinitionId}'
                            },
                            beforePopupClose: function () {

                            }
                        }, iFramePopup_fullFrameAttr_meta_cust));
                    } else if (actionId == '8') {
                        $('#parsedContentMetadataTemplateBtn').iFramePopup($.extend({
                            src: context + "/metadata/metadata_form_definition_edit.form",
                            title: client_messages.title.content_metadata_template,
                            displayOnInit: true,
                            id: "parsedMetadataContentDefinitionFrame",
                            appliedParams: {
                                'tk': "${param.tk}",
                                'formDefinitionId': '${parsedContentMetadataFormDefinitionId}'
                            },
                            beforePopupClose: function () {

                            }
                        }, iFramePopup_fullFrameAttr_meta_cust));
                    } else if (actionId == '12') {
                        $('#applicationVisibilityBtn').iFramePopup($.extend({
                            src: context + "/rationalizer/rationalizer_application_visibility_edit.form",
                            title: client_messages.title.rationalizer_application_visibility,
                            displayOnInit: true,
                            id: "applicationVisibilityFrame",
                            appliedParams: {
                                'tk': "${param.tk}",
                                'rationalizerApplicationId': getParam('rationalizerApplicationId')
                            },
                            beforePopupClose: function () {

                            }
                        }, iFramePopup_fullFrameAttr_meta_cust));
                    } else if (actionId == '13') {
                        $('#applicationNavigationBtn').iFramePopup({
                            width: 500,
                            title: client_messages.title.rationalizer_change_structure,
                            src: context + "/rationalizer/rationalizer_application_navigation_edit.form",
                            displayOnInit: true,
                            appliedParams: {
                                'tk': "${param.tk}",
                                'rationalizerApplicationId': getParam('rationalizerApplicationId')
                            },
                            closeBtnId: "cancelBtn_button",
                            beforePopupClose: function () {

                            }
                        });
                    } else if (actionId == '14') {
                        $('#workflowBtn').iFramePopup({
                            width: 600,
                            displayOnInit: true,
                            draggable: false,
                            title: client_messages.text.rationalizer_workflow,
                            src: context + "/rationalizer/rationalizer_workflow_assignment_edit.form",
                            appliedParams: {
                                'tk': gup('tk'),
                                'rationalizerApplicationId': getParam('rationalizerApplicationId')
                            },
                            closeBtnId: "cancelBtn_button",
                            applyCloseToggle: false,
                            onSave: function () {
                                getTopFrame().location.reload();
                            }
                        });
                    } else if (actionId == '15') {
                        $('#metadataPointsOfInterestTemplateBtn').iFramePopup({
                            width: 1000,
                            height: 800,
                            displayOnInit: true,
                            draggable: false,
                            title: client_messages.text.metadata_properties,
                            src: context + "/metadata/metadata_points_of_interest_form.form",
                            id: "metadataPointsOfInterestFrame",
                            appliedParams: {
                                'tk': "${param.tk}",
                                'rationalizerApplicationId': getParam('rationalizerApplicationId')
                            },
                            closeBtnId: "cancelBtn_button",
                            applyCloseToggle: false,
                            onSave: function () {
                                getTopFrame().location.reload();
                            }
                        });
                    } else if (actionId == '20') {
                        // assetIds=sas-123_234+ili-245_231
                        var assetIds = 'rac-'+getSelectedIds();
                        openAddTaskModal(assetIds, $('#taskMetadataSelect'), true);
                    } else if (actionId == '30') {
                        requestMetadataValues();
                        actionSelected('30');
                    }

                    actionCancel();
                    initMetadataNameDisplay();
                }

                // Document list validation
                function validateActionReq(contentId) {
                    var singleSelect = true;
                    var canUpdate = true;
                    var canReassign = true;
                    var canReleaseForApproval = true;
                    var canApprove = true;
                    var canReject = true;
                    var canDiscard = true;
                    var canDeactivate = true;
                    var canAddTask = true;
                    var allHasWorkflowStep = true;
                    var allAreWorkflowOwner = true;

                    // Resolve selection flags
                    if ($("input[id^='listItemCheck_']:checked").length != 1) {
                        singleSelect = false;
                    }
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        var contentId = this.id.replace('listItemCheck_', '');
                        if (!exists('canUpdate_' + contentId))
                            canUpdate = false;
                        if (!exists('canReassign_' + contentId))
                            canReassign = false;
                        if (!exists('canClone_' + contentId))
                            canClone = false;
                        if (!exists('canReleaseForApproval_' + contentId))
                            canReleaseForApproval = false;
                        if (!exists('canApprove_' + contentId))
                            canApprove = false;
                        if (!exists('canReject_' + contentId))
                            canReject = false;
                        if (!exists('canDeactivate_' + contentId))
                            canDeactivate = false;
                        if (!exists('canAddTask_' + contentId))
                            canAddTask = false;
                        if (exists('hasNoStepForWorkflow_' + contentId)) {
                            allHasWorkflowStep = false;
                        } else {
                            allHasNoWorkflowStep = false;
                        }
                        if (exists('workflowOwner_' + contentId)) {
                            allAreNotWorkflowOwner = false;
                        } else {
                            allAreWorkflowOwner = false;
                        }
                    });

                    //Disable all context menu entries
                    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
                    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');

                    var $actionMenuDropdown = $actionMenu.data('complexDropdown');

                    if ($actionMenuDropdown)
                        $actionMenuDropdown.disableAllTheOptions();

                    common.disableElement($updateBtn);
                    common.disableElement($deleteBtn);

                    if (singleSelect) {
                        if (canUpdate) {
                            $('a#actioniFrame_1').removeClass('disabled');
                            common.enableElement($updateBtn);
                            $('a#actionOption_30').removeClass('disabled');
                        }
                        if (canReassign) {
                            $('a#actionOption_12').removeClass('disabled');
                            if ($actionMenuDropdown)
                                $actionMenuDropdown.enableOptionById('actionOption_12');	// Reassign
                        }
                    }

                    if ($("input[id^='listItemCheck_']:checked").length > 0) {
                        $('a#actionOption_2').removeClass('disabled');	// Delete
                        common.enableElement($deleteBtn); // Delete
                        $('a#actionOption_30').removeClass('disabled');	// update zone connector

                        if (allHasWorkflowStep) {
                            if (allAreWorkflowOwner) {
                                $('a#actionOption_17').show();	// Approve and override
                                $('a#actionOption_18').show();	// Reject and override
                                $('a#actionOption_14').hide();	// Approve
                                $('a#actionOption_15').hide();	// Reject
                                if ($actionMenuDropdown) {
                                    $actionMenuDropdown.showOptionById('actionOption_17');	// Approve and override
                                    $actionMenuDropdown.showOptionById('actionOption_18');	// Reject and override
                                    $actionMenuDropdown.hideOptionById('actionOption_14');	// Approve
                                    $actionMenuDropdown.hideOptionById('actionOption_15');	// Reject
                                }
                            } else {
                                $('a#actionOption_14').show(); // Approve
                                $('a#actionOption_15').show(); // Reject
                                $('a#actionOption_17').hide();	// Approve and override
                                $('a#actionOption_18').hide();	// Reject and override
                                if ($actionMenuDropdown) {
                                    $actionMenuDropdown.showOptionById('actionOption_14');	// Approve
                                    $actionMenuDropdown.showOptionById('actionOption_15');	// Reject
                                    $actionMenuDropdown.hideOptionById('actionOption_17');	// Approve and override
                                    $actionMenuDropdown.hideOptionById('actionOption_18');	// Reject and override
                                }
                            }

                            $('a#actionOption_13').show();  // Release for approval
                            $('a#actionOption_16').hide();	// Activate
                            if ($actionMenuDropdown) {
                                $actionMenuDropdown.showOptionById('actionOption_13');	// Release for approval
                                $actionMenuDropdown.hideOptionById('actionOption_16');	// Activate
                            }
                        } else {
                            $('a#actionOption_16').show(); // Activate
                            $('a#actionOption_17').hide();	// Approve and override
                            $('a#actionOption_18').hide();	// Reject and override
                            $('a#actionOption_13').hide();	// Release for approval
                            $('a#actionOption_14').hide();	// Approve
                            $('a#actionOption_15').hide();	// Reject
                            if ($actionMenuDropdown) {
                                $actionMenuDropdown.showOptionById('actionOption_16');	// Activate
                                $actionMenuDropdown.hideOptionById('actionOption_17');	// Approve and override
                                $actionMenuDropdown.hideOptionById('actionOption_18');	// Reject and override
                                $actionMenuDropdown.hideOptionById('actionOption_13');	// Release for approval
                                $actionMenuDropdown.hideOptionById('actionOption_14');	// Approve
                                $actionMenuDropdown.hideOptionById('actionOption_15');	// Reject
                            }
                        }

                        if (canReleaseForApproval) {
                            $('a#actionOption_13').removeClass('disabled');	// Release for approval
                            $('a#actionOption_16').removeClass('disabled');	// Activate
                            if ($actionMenuDropdown) {
                                $actionMenuDropdown.enableOptionById('actionOption_13');	// Release for approval
                                $actionMenuDropdown.enableOptionById('actionOption_16');	// Activate
                            }
                        }
                        if (canApprove) {
                            $('a#actionOption_14').removeClass('disabled'); // Approve
                            $('a#actionOption_17').removeClass('disabled'); // Approve and override
                            if ($actionMenuDropdown) {
                                $actionMenuDropdown.enableOptionById('actionOption_14'); // Approve
                                $actionMenuDropdown.enableOptionById('actionOption_17'); // Approve and override
                            }
                        }
                        if (canReject) {
                            $('a#actionOption_15').removeClass('disabled'); // Reject
                            $('a#actionOption_18').removeClass('disabled'); // Reject and override
                            if ($actionMenuDropdown) {
                                $actionMenuDropdown.enableOptionById('actionOption_15'); // Reject
                                $actionMenuDropdown.enableOptionById('actionOption_18'); // Reject and override
                            }
                        }
                        if (canDeactivate) {
                            $('a#actionOption_19').removeClass('disabled'); // Deactivate
                            if ($actionMenuDropdown)
                                $actionMenuDropdown.enableOptionById('actionOption_19'); // Deactivate
                        }

                        if(canAddTask){
                            $('.contextMenu a#actioniFrame_20').removeClass('disabled'); // Add task
                            $actionMenu.data('complexDropdown').enableOptionById('actioniFrame_20'); // Add task
                            $('.contextMenu a#actionOption_21').removeClass('disabled'); // Add task
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_21'); // Add task
                        }
                        if (canUpdate) {
                            $('.contextMenu a#actionOption_30').removeClass('disabled'); // Add task
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_30'); // Add task
                        }
                        initMetadataNameDisplay();
                    }
                }

                // Retrieve selected ids seperated by ','
                function getSelectedIds() {
                    var selectedIds = '';
                    var selectedCount = $("input[id^='listItemCheck']:checked").length;
                    var count = 0;
                    $("input[id^='listItemCheck']:checked").each(
                        function () {
                            selectedIds += this.id.replace('listItemCheck_', '');
                            count++;
                            if (count != selectedCount)
                                selectedIds += '_';
                        }
                    );
                    return selectedIds;
                }

                function getSelectedNavTreeNodeId() {
                    var selectedBranchString;
                    if (typeof(Storage) !== "undefined") {
                        selectedBranchString = localStorage.getItem("msgpt_persistedBranchValue_rationalizerNavTreeSelect_" + getParam('rationalizerApplicationId'));
                    }
                    return selectedBranchString ? selectedBranchString : -9;
                }

                function toggleHistorySelect() {
                    if ($('#actionPopupExtTypeSelect option:selected').val() == 0) {
                        $('#actionHistoryInfo').show();
                        $('#actionHistorySelect').show();
                    } else {
                        $('#actionHistoryInfo').hide();
                        $('#actionHistorySelect').hide();
                    }
                }
            </script>
        </msgpt:Script>
        <style>
            .tooltip-inner {
                max-width: 300px;
            }

            .actionSelectMenuText {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 414px;
                min-width: 100px;
            }

            .actionOptionFiltered {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 600px;
                min-width: 200px;
            }
        </style>
    </msgpt:HeaderNew>

    <!-- FLAGS AND PERMISSIONS -->
    <c:set var="viewMetatagsPermission" value="false"/>
    <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
        <c:set var="viewMetatagsPermission" value="true"/>
    </msgpt:IfAuthGranted>
    <c:set var="editPermission" value="false"/>
    <msgpt:IfAuthGranted authority="ROLE_ECATALOG_EDIT">
        <c:set var="editPermission" value="true"/>
    </msgpt:IfAuthGranted>

    <msgpt:BodyNew>
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_DATA_ADMIN %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_DATA_ADMIN %>"/>
        <jsp:include page="includes/rationalizer_context_bar.jsp"/>
        <msgpt:LowerContainer fullPanel="true" extendedWidth="true">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="true" style="width:1450px">
                <form:form method="post" modelAttribute="command">
                    <form:hidden path="exportId" id="exportId"/>
                    <form:hidden path="exportName" id="exportName"/>
                    <form:errors path="*">
                        <msgpt:Information errorMsgs="${messages}" type="error"/>
                    </form:errors>
                    <c:if test="${param.rationalizerApplicationId != -2 && not application.beingCloned}">
                        <h1 class="d-flex justify-content-start align-items-center h4 pb-2 mb-4">
                            <span class="text-dark"><fmtSpring:message code="page.label.rationalizer"/></span>
                            <div class="ml-3" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 600px; min-width: 400px; margin-right: 10px;"
                                 data-toggle="tooltip" data-placement="top" title="${command.currentApplication.name}">
                                <i class="far fa-angle-right text-dark fa-sm mr-2" aria-hidden="true"></i>
                                <c:out value="${command.currentApplication.name}"/>
                            </div>
                            <div class="d-flex ml-auto" role="group">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" type="button"
                                            onclick="javascriptHref('rationalizer_dashboard.form?rationalizerApplicationId=${not empty param.rationalizerApplicationId ? param.rationalizerApplicationId : -1}');">
                                        <i class="fas fa-chart-line mr-2" aria-hidden="true"></i>
                                        <fmtSpring:message code="page.label.dashboard"/>
                                    </button>
                                    <button class="btn btn-outline-primary active" type="button"
                                            onclick="javascriptHref('rationalizer_documents_list.form?rationalizerApplicationId=${not empty param.rationalizerApplicationId ? param.rationalizerApplicationId : -1}');">
                                        <i class="far fa-file-alt mr-2" aria-hidden="true"></i>
                                        <fmtSpring:message code="page.label.documents"/>
                                        <span class="sr-only">(<fmtSpring:message
                                                code="client_messages.text.current"/>)</span>
                                    </button>
                                    <button class="btn btn-outline-primary" type="button"
                                            onclick="javascriptHref('rationalizer_query_list.form?rationalizerApplicationId=${not empty param.rationalizerApplicationId ? param.rationalizerApplicationId : -1}');">
                                        <i class="far fa-th-list mr-2" aria-hidden="true"></i>
                                        <fmtSpring:message code="page.label.queries"/>
                                    </button>
                                    <button class="btn btn-outline-primary" type="button"
                                            onclick="javascriptHref('rationalizer_shared_content_list.form?rationalizerApplicationId=${not empty param.rationalizerApplicationId ? param.rationalizerApplicationId : -1}');">
                                        <i class="far fa-share-square mr-2" aria-hidden="true"></i>
                                        <fmtSpring:message code="page.label.rationalizer.shared"/>
                                    </button>
                                    <button class="btn btn-outline-primary" type="button"
                                            onclick="javascriptHref('${contextPath}/tasks/task_list.form?rationalizerApplicationId=${not empty param.rationalizerApplicationId ? param.rationalizerApplicationId : -1}');">
                                        <i class="far fa-calendar-check mr-2" aria-hidden="true"></i>
                                        <fmtSpring:message code="page.label.tasks"/>
                                    </button>
                                    <msgpt:IfAuthGranted authority="ROLE_METATAGS_ADMIN">
                                        <msgpt:Script>
                                            <script>
                                                $( function() { if ( !common.isFeatureEnabled('TagSetupEnabled') )
                                                    $('#tagSetupBtn').remove(); else $('#tagSetupBtn').show(); });
                                            </script>
                                        </msgpt:Script>
                                        <button id="tagSetupBtn" class="btn btn-outline-primary" type="button" style="display: :none;"
                                                onclick="javascriptHref('${contextPath}/dataadmin/metatags_edit.form?rationalizerApplicationId=${not empty param.rationalizerApplicationId ? param.rationalizerApplicationId : -1}');">
                                            <i class="far fa-tag mr-2" aria-hidden="true"></i>
                                            <fmtSpring:message code="page.label.tags"/>
                                        </button>
                                    </msgpt:IfAuthGranted>
                                </div>
                            </div>
                        </h1>
                        <div class="row flex-nowrap mt-4">
                            <div id="rationalizerNavigationWidgetContainer" class="col-auto" style="width: 30rem;margin-right: 2rem; margin-left: 1rem;">
                                <div class="box-shadow-4 rounded bg-white" style="width: 30rem;">
                                    <div class="p-4 border-bottom">
                                        <div class="d-flex align-items-top pt-1 px-2">
                                            <div class="fs-xs text-dark align-self-center">
                                                <c:out value="${navTreeBreadCrumbTrail}" escapeXml="false"/>
                                            </div>
                                            <div class="ml-auto pl-3 text-center">
                                                <button id="applicationNavigationBtn" type="button"
                                                        class="btn btn-light"
                                                        data-toggle="tooltip"
                                                        aria-label="${msgpt:getMessage('page.label.application.navigation')}"
                                                        title="${msgpt:getMessage('page.label.application.navigation')}">
                                                    <i class="far fa-sitemap" aria-hidden="true"></i>
                                                </button>
                                                <small class="d-block pt-1"><fmtSpring:message
                                                        code="page.label.structure"/></small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="pb-1 border-top">
                                        <iframe
                                                id="rationalizerNavigationWidget"
                                                name="rationalizerNavigationWidget"
                                                src=""
                                                allowtransparency="true"
                                                frameborder="0"
                                                style="width: 30rem; height: 31.5rem;">
                                        </iframe>
                                    </div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="box-shadow-4 rounded bg-white p-4">
                                    <div class="px-2 pb-1">
                                        <h4 class="d-flex align-items-center m-0">
                                            <span id="currentNavValueContainer" class="font-weight-normal"></span>
                                            <span id="syncIndicator" class="ml-3"></span>
                                            <c:if test="${param.rationalizerApplicationId != -2}">
                                                <div class="d-flex align-items-center ml-3 pl-3 border-left border-light">
                                                    <span id="viewsLabel" class="sr-only">
                                                        <fmtSpring:message code="page.label.view"/>:
                                                    </span>
                                                    <div class="btn-group btn-group-sm" role="group"
                                                         aria-describedby="viewsLabel">
                                                        <button id="documentViewBtn" class="btn btn-outline-light"
                                                                style="color: inherit;"
                                                                type="button" data-toggle="tooltip"
                                                                title="${msgpt:getMessage('page.label.document')}">
                                                            <span class="sr-only">
                                                                    ${msgpt:getMessage('page.label.document')}
                                                            </span>
                                                            <i class="far fa-file" aria-hidden="true"></i>
                                                        </button>
                                                        <button id="contentViewBtn" class="btn btn-outline-light active"
                                                                type="button" data-toggle="tooltip"
                                                                title="${msgpt:getMessage('page.label.content')}">
                                                            <span class="sr-only">
                                                                ${msgpt:getMessage('page.label.content')}
                                                                (<fmtSpring:message
                                                                    code="client_messages.text.current"/>)
                                                            </span>
                                                            <i class="far fa-align-left" aria-hidden="true"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </c:if>
                                            <button id="widgetToggleBtn"
                                                    class="btn btn-link btn-link-inline ml-auto fs-xs font-weight-bold text-uppercase text-dark persistedClass"
                                                    data-toggle="button" aria-pressed="false">
                                                <i id="widgetToggleBtnIcon"
                                                   class="btn-icon fas fa-expand-wide fa-lg mr-2 persistedClass"></i>
                                                <span class="btn-text"><fmtSpring:message
                                                        code="client_messages.text.expand"/></span>
                                            </button>
                                        </h4>
                                        <div class="my-4">
                                            <div class="d-flex align-items-center pb-1">
                                                <form:hidden path="actionValue" id="actionElement"/>
                                                <div class="btn-group border-separate mr-auto" role="group"
                                                     aria-label="${msgpt:getMessage("page.label.actions")}">
                                                    <button id="updateBtn" type="button" class="btn btn-dark"
                                                            onclick="iFrameAction(1);" disabled>
                                                        <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                                        <fmtSpring:message code="action.button.label.update"/>
                                                    </button>
                                                    <button id="deleteBtn" type="button" class="btn btn-dark"
                                                            onclick="actionSelected(2);" disabled>
                                                        <i class="far fa-trash-alt mr-2" aria-hidden="true"></i>
                                                        <fmtSpring:message code="page.label.delete"/>
                                                    </button>
                                                    <c:if test="${documentsExist}">
                                                        <div class="btn-group">
                                                            <select title="${msgpt:getMessage('page.label.export')}"
                                                                    id="exportMenu"
                                                                    class="complex-dropdown-select"
                                                                    aria-label="${msgpt:getMessage('page.label.export')}"
                                                                    data-toggle="complex-dropdown"
                                                                    data-action="menu"
                                                                    data-dropdown-class="btn-dark"
                                                                    onchange="infoItemAction(this,-1)">
                                                                <option id="actionOption_11"><fmtSpring:message
                                                                        code="page.label.generate.documents.report"/></option>
                                                                <c:if test="${not empty report}">
                                                                    <option class="divider" disabled/>
                                                                    <c:choose>
                                                                        <c:when test="${not report.complete && not report.error}"> <!-- Report in process -->
                                                                            <option id="itemInProcess_${report.id}"
                                                                                    type="inProcess" disabled>
                                                                                <fmtSpring:message
                                                                                        code="page.label.processing"/> -
                                                                                <fmtJSTL:formatDate
                                                                                        value="${report.requestDate}"
                                                                                        pattern="${dateTimeFormat}"/>
                                                                            </option>
                                                                        </c:when>
                                                                        <c:when test="${report.error}"> <!-- Report error -->
                                                                            <option id="itemError_${report.id}"
                                                                                    class="text-danger"
                                                                                    deliveryId="-1" itemClass="null">
                                                                                <fmtSpring:message
                                                                                        code="page.text.error"/> -
                                                                                <fmtJSTL:formatDate
                                                                                        value="${report.requestDate}"
                                                                                        pattern="${dateTimeFormat}"/>
                                                                            </option>
                                                                        </c:when>
                                                                        <c:otherwise> <!-- View report -->
                                                                            <option id="reportDownload_${report.id}"
                                                                                    value="${report.resourceToken}" type="${reportType}">
                                                                                <fmtSpring:message
                                                                                        code="page.label.download"/> -
                                                                                <fmtJSTL:formatDate
                                                                                        value="${report.requestDate}"
                                                                                        pattern="${dateTimeFormat}"/>
                                                                            </option>
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                </c:if>
                                                            </select>
                                                        </div>
                                                    </c:if>
                                                    <div class="btn-group">
                                                        <select title="${msgpt:getMessage('page.label.more')}"
                                                                id="actionMenu"
                                                                class="complex-dropdown-select"
                                                                aria-label="${msgpt:getMessage('page.label.more')}"
                                                                data-toggle="complex-dropdown"
                                                                data-action="menu"
                                                                data-dropdown-class="btn-dark"
                                                                onchange="actionSelected(this)">
                                                            <option id="actionOption_12" disabled>
                                                                <fmtSpring:message
                                                                        code="page.label.reassign.to.user"/></option>
                                                            <option id="actionOption_13" disabled>
                                                                <fmtSpring:message
                                                                        code="page.label.release.for.approval"/></option>
                                                            <option id="actionOption_14" disabled>
                                                                <fmtSpring:message
                                                                        code="page.label.approve"/></option>
                                                            <option id="actionOption_15" disabled>
                                                                <fmtSpring:message
                                                                        code="page.label.reject"/></option>
                                                            <option id="actionOption_16" class="d-none" disabled>
                                                                <fmtSpring:message
                                                                        code="page.label.activate"/></option>
                                                            <option id="actionOption_17" class="d-none" disabled>
                                                                <fmtSpring:message
                                                                        code="page.label.approve.and.override"/></option>
                                                            <option id="actionOption_18" class="d-none" disabled>
                                                                <fmtSpring:message
                                                                        code="page.label.reject.and.override"/></option>
                                                            <option id="actionOption_19" disabled>
                                                                <fmtSpring:message
                                                                        code="page.label.deactivate"/></option>
                                                            <c:if test="${not hasTaskMetadataFormDef}">
                                                                <option id="actioniFrame_20" disabled>
                                                                    <fmtSpring:message code="page.label.add.task"/></option>
                                                            </c:if>
                                                            <c:if test="${hasTaskMetadataFormDef}">
                                                                <option id="actionOption_21" disabled>
                                                                    <fmtSpring:message code="page.label.add.task"/></option>
                                                            </c:if>
                                                            <option id="actionOption_30" enabled >
                                                                <fmtSpring:message
                                                                        code="page.label.update.zc"/></option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="form-group position-relative d-inline-block m-0">
                                                    <label for="listSearchInput"
                                                           class="sr-only"><fmtSpring:message
                                                            code="page.label.search"/></label>
                                                    <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                                       style="z-index: 1;" aria-hidden="true"></i>
                                                    <msgpt:InputFilter type="description">
                                                        <input id="listSearchInput" type="text"
                                                               class="form-control bg-light has-control-x border-0"
                                                            ${viewMetatagsPermission ? 'data-toggle="tagcloud"' :''}
                                                               data-cloud-type="13"
                                                               data-rationalizer-application-id="${param.rationalizerApplicationId}"
                                                               placeholder="${msgpt:getMessage('page.label.search')}"
                                                               size="20"/>
                                                    </msgpt:InputFilter>
                                                </div>
                                            </div>
                                            <c:if test="${hasAssignedWorkflow}">
                                                <div class="d-flex align-items-center mt-3">
                                                    <span class="text-dark mr-1" id="filter">
                                                        <fmtSpring:message code="page.label.showing"/>
                                                    </span>
                                                    <div class="mx-2">
                                                        <select id="contentListAssignmentFilter"
                                                                aria-labelledby="filter"
                                                                data-toggle="complex-dropdown"
                                                                data-menu-class="dropdown-custom mt-2"
                                                                class="complex-dropdown-select persistedValue"
                                                                data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                                onchange="toggleFilter(this)">
                                                            <c:forEach items="${contentAssignmentFilterTypes}"
                                                                       var="currentFilter">
                                                                <option id="contentListAssignmentFilterOption_${currentFilter.id}"
                                                                        value="${currentFilter.id}"><fmtSpring:message
                                                                        code="${currentFilter.displayMessageCode}"/></option>
                                                            </c:forEach>
                                                        </select>
                                                    </div>
                                                    <span class="text-dark mx-1" id="content-status">
                                                        <fmtSpring:message code="page.text.content.which.are"/>
                                                    </span>
                                                    <div class="mx-2">
                                                        <select id="contentListContentStatusFilter"
                                                                class="complex-dropdown-select persistedValue"
                                                                aria-labelledby="message-status"
                                                                data-toggle="complex-dropdown"
                                                                data-menu-class="dropdown-custom mt-2"
                                                                data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                                onchange="toggleFilter(this)">
                                                            <option id="contentListContentStatusFilter_0" value="0">
                                                                <fmtSpring:message
                                                                        code="page.label.any.status"/></option>
                                                            <c:forEach items="${contentStatusFilterTypes}"
                                                                       var="currentFilter">
                                                                <option id="contentListContentStatusFilter_${currentFilter.id}"
                                                                        value="${currentFilter.id}"><fmtSpring:message
                                                                        code="${currentFilter.displayMessageCode}"/></option>
                                                            </c:forEach>
                                                        </select>
                                                    </div>
                                                    <span class="text-dark mx-1" id="content-state">
                                                        <fmtSpring:message code="page.label.and"/>
                                                    </span>
                                                    <div class="ml-2">
                                                        <select id="contentListStateFilter"
                                                                class="complex-dropdown-select persistedValue"
                                                                aria-labelledby="content-type"
                                                                data-toggle="complex-dropdown"
                                                                data-menu-class="dropdown-custom mt-2"
                                                                data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                                onchange="toggleFilter(this)">
                                                            <option id="contentListStateFilter_0" value="0">
                                                                <fmtSpring:message
                                                                        code="page.label.any.status"/></option>
                                                            <c:forEach items="${contentStateFilterTypes}"
                                                                       var="currentFilter">
                                                                <option id="contentListStateFilter_${currentFilter.id}"
                                                                        value="${currentFilter.id}"><fmtSpring:message
                                                                        code="${currentFilter.displayMessageCode}"/></option>
                                                            </c:forEach>
                                                        </select>
                                                    </div>
                                                </div>
                                            </c:if>
                                        </div>
                                        <div class="bg-white">
                                            <msgpt:DataTable id="rationalizerContentList"
                                                             listHeader="page.label.rationalizer"
                                                             async="true" columnReorder="true" numUnreorderableCols="1"
                                                             columnVisibility="true" drillDown="true" multiSelect="true"
                                                             searchFilter="true" permitsShowAll="false">
                                            </msgpt:DataTable>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:if>
                    <c:if test="${param.rationalizerApplicationId == -2}">
                        <div class="alert alert-info" role="alert">
                            <strong class="mr-2">
                                <i class="fas fa-info-circle fa-lg mr-2"
                                   aria-hidden="true"></i><fmtSpring:message
                                    code="page.label.info"/>:
                            </strong>
                            <fmtSpring:message code="page.text.no.rationalizer.applications.exist"/>
                        </div>
                    </c:if>
                    <c:if test="${application.beingCloned}">
                        <div class="alert alert-info" role="alert">
                            <strong class="mr-2">
                                <i class="fas fa-info-circle fa-lg mr-2"
                                   aria-hidden="true"></i><fmtSpring:message
                                    code="page.label.info"/>:
                            </strong>
                            <fmtSpring:message code="page.text.rationalizer.application.being.cloned"/>
                        </div>
                    </c:if>

                    <!-- POPUP DATA -->
                    <div id="actionSpecs" style="display: none;">
                        <!-- ACTIONS POPUP DATA -->
                        <div id="actionSpec_1" submitId="3"> <!-- Add Application -->
                            <div id="actionTitle_1"><fmtSpring:message code="page.text.add.application"/></div>
                            <div id="actionAddApplication_1"></div>
                        </div>
                        <div id="actionSpec_2" type="simpleConfirm" submitId="2"> <!-- Delete -->
                            <div id="actionTitle_2"><fmtSpring:message
                                    code="page.label.confirm.delete.rationalizer.content"/></div>
                            <div id="actionInfo_2"><fmtSpring:message
                                    code="page.text.delete.selected.rationalizer.content"/></div>
                        </div>
                        <div id="actionSpec_3" submitId="4" contentWidth="440px"> <!-- Application properties -->
                            <div id="actionTitle_3"><fmtSpring:message code="page.text.application.properties"/></div>
                            <div id="actionApplicationProperties_3"></div>
                            <div id="actionCustomButtons_3"></div>
                        </div>
                        <div id="actionSpec_4" submitId="5"> <!-- Add Document -->
                            <div id="actionTitle_4"><fmtSpring:message code="page.label.add.document"/></div>
                            <div id="actionInfo_4"><fmtSpring:message code="page.text.add.document.note"/></div>
                            <div id="actionName_4"></div>
                        </div>
                        <div id="actionSpec_7" type="simpleConfirm" submitId="7"> <!-- Clone Application -->
                            <div id="actionTitle_7"><fmtSpring:message
                                    code="page.label.confirm.clone.rationalizer.application"/></div>
                            <div id="actionInfo_7"><p><b><fmtSpring:message
                                    code="page.text.clone.selected.rationalizer.application"/></b></p></div>
                            <div id="actionClone_7" required="true"></div>
                        </div>
                        <div id="actionSpec_8" type="simpleConfirm" submitId="8"> <!-- Delete Cloned Application -->
                            <div id="actionTitle_8"><fmtSpring:message
                                    code="page.label.confirm.delete.cloned.rationalizer.application"/></div>
                            <div id="actionInfo_8"><fmtSpring:message
                                    code="page.text.delete.selected.cloned.rationalizer.application"/></div>
                        </div>
                        <div id="actionSpec_9" type="simpleConfirm" submitId="9"> <!-- Delete Application -->
                            <div id="actionTitle_9"><fmtSpring:message
                                    code="page.label.confirm.delete.rationalizer.application"/></div>
                            <div id="actionInfo_9"><fmtSpring:message
                                    code="page.text.delete.selected.rationalizer.application"/></div>
                        </div>
                        <div id="actionSpec_11" submitId="6" contentWidth="330px"> <!-- Export -->
                            <div id="actionTitle_11"><fmtSpring:message
                                    code="page.label.generate.documents.report"/></div>
                            <div id="actionInfo_11"><fmtSpring:message
                                    code="page.text.would.you.like.to.generate.document.report"/></div>
                            <div id="actionExtTypeSelect_11"></div>
                            <div id="actionHistoryInfo_11"></div>
                            <div id="actionHistorySelect_11"></div>
                        </div>
                        <div id="actionSpec_12" submitId="12"> <!-- Reassign to user -->
                            <div id="actionTitle_12"><fmtSpring:message
                                    code="page.label.confirm.reassign.to.user"/></div>
                            <div id="actionInfo_12"><fmtSpring:message code="page.text.reassign.asset.to.user"/></div>
                            <div id="actionNote_12"></div>
                            <div id="actionUserSelect_12" required="true" type="rationalizer_currentStageUsers"></div>
                        </div>
                        <div id="actionSpec_13" submitId="13"> <!-- Release for approval -->
                            <div id="actionTitle_13"><fmtSpring:message
                                    code="page.label.confirm.release.for.approval"/></div>
                            <div id="actionInfo_13"><fmtSpring:message
                                    code="page.text.provide.description.of.action.required"/></div>
                            <div id="actionNote_13"></div>
                        </div>
                        <div id="actionSpec_14" type="simpleConfirm" submitId="14" contentWidth="330px"><!-- APPROVE -->
                            <div id="actionTitle_14"><fmtSpring:message code="page.label.approve"/></div>
                            <div id="actionInfo_14"><fmtSpring:message
                                    code="page.text.would.you.like.to.approve"/></div>
                            <div id="actionApproval_14" approveSubmitId="14"></div>
                        </div>
                        <div id="actionSpec_15" type="simpleConfirm" submitId="15" contentWidth="330px"><!-- REJECT -->
                            <div id="actionTitle_15"><fmtSpring:message code="page.label.reject"/></div>
                            <div id="actionInfo_15"><fmtSpring:message code="page.text.would.you.like.reject"/></div>
                            <div id="actionNote_15" required="true"><fmtSpring:message
                                    code="page.text.note.required.to.reject.brackets"/></div>
                            <div id="actionUserSelect_15" required="true" type="rationalizer_rejectToUsers"></div>
                            <div id="actionReject_15" rejectSubmitId="15"></div>
                        </div>
                        <div id="actionSpec_16" submitId="16">    <!-- Activate -->
                            <div id="actionTitle_16"><fmtSpring:message code="page.label.confirm.activate"/></div>
                            <div id="actionInfo_16"><fmtSpring:message code="page.text.activate.assets"/></div>
                        </div>
                        <div id="actionSpec_17" type="simpleConfirm" submitId="14" contentWidth="330px">
                            <!-- Override approve -->
                            <div id="actionTitle_17"><fmtSpring:message code="page.label.approve.workflow.owner"/></div>
                            <div id="actionInfo_17"><fmtSpring:message
                                    code="page.text.workflow.owners.may.approve.step"/></div>
                            <div id="actionApproval_17" approveSubmitId="14"></div>
                        </div>

                        <div id="actionSpec_18" type="simpleConfirm" submitId="15" contentWidth="330px">
                            <!-- Override reject -->
                            <div id="actionTitle_18"><fmtSpring:message code="page.label.reject.workflow.owner"/></div>
                            <div id="actionInfo_18"><fmtSpring:message
                                    code="page.text.workflow.owners.may.reject.step"/></div>
                            <div id="actionNote_18" required="true"><fmtSpring:message
                                    code="page.text.note.required.to.reject.brackets"/></div>
                            <div id="actionUserSelect_18" required="true" type="rationalizer_rejectToUsers"></div>
                            <div id="actionReject_18" rejectSubmitId="15"></div>
                        </div>

                        <div id="actionSpec_19" type="simpleConfirm" submitId="19" contentWidth="330px">
                            <!-- Deactivate -->
                            <div id="actionTitle_19"><fmtSpring:message code="page.label.confirm.deactivate"/></div>
                            <div id="actionInfo_19"><fmtSpring:message
                                    code="page.text.select.user.to.assign.deactivated.assets.to"/></div>
                            <div id="actionNote_19" required="true"><fmtSpring:message
                                    code="page.text.note.required.brackets"/></div>
                            <div id="actionUserSelect_19" required="false" type="rationalizer_rejectToUsers"></div>
                        </div>
                        <div id="actionSpec_21"> <!-- Add task with metadata -->
                            <div id="actionTitle_21"><fmtSpring:message code="page.label.add.task"/></div>
                            <div id="actionTaskMetadataSelect_21"></div>
                            <div id="actionTaskMetadataButtons_21"></div>
                        </div>
                        <!-- BULK METADATA UPDATE -->
                        <div id="actionSpec_30" submitId="30" contentWidth="500px">
                            <div id="actionTitle_30"><fmtSpring:message code="page.label.modify.content.metadata"/></div>
                            <div id="actionInfo_30"><fmtSpring:message code="page.text.modify.content.metadata"/></div>
                            <div id="actionModifyContent_30"></div>
                        </div>
                    </div>

                    <!-- POPUP INTERFACE -->
                    <msgpt:Popup id="actionPopup" theme="minimal">
                        <div id="actionPopupInfoFrame">
                            <div id="actionPopupInfo">&nbsp;</div>
                        </div>

                        <div id="actionPopupModifyContent" style="padding: 3px 15px 6px 15px;" align="left">

                            <div id="modifyContentMetadataConnectorValuesContainer" style="white-space: nowrap; margin-top: 8px;">
                                <div style="display: inline-block; position: relative; vertical-align: middle; min-width: 100px;">
                                    <fmtSpring:message code="page.label.metadata" />
                                </div>

                                <div style="display:none; position: relative; vertical-align: middle;">
                                    <form:select id="modifyContentMetadataConnectorValuesSelect" path="modifyContentMetadataConnectorValue"
                                                 items="${modifyContentFormItemDefinitions}" itemValue="primaryConnector" itemLabel="name"
                                                 cssClass="inputXL style_select" onchange="requestMetadataValues()" style=" min-width: 100px; display:none" />

                                </div>

                                <div style="display: inline-block; position: relative; vertical-align: middle;">
                                    <input type="text" id="zoneConnectorDisplay" name="zoneConnectorDisplay" readonly>

                                </div>
                            </div>

                            <div style="margin-top: 8px; white-space: nowrap; ">

                                <div style="display: inline-block; position: relative; vertical-align: middle; min-width: 100px;">
                                    <fmtSpring:message code="page.label.value" />
                                </div>

                                <div style="display: inline-block; position: relative; vertical-align: middle;">
                                    <div id="metadataInputContainer_loading" style="white-space: nowrap;">
                                        <i class="fa fa-cog fa-spin fa-fw"></i>
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                    <!-- TEXT -->
                                    <div id="metadataInputContainer_text" style="white-space: nowrap;">
                                        <input id="metadataInput_text" class="inputXL" onchange="updateModifyMetadataValueBinding(this)" onkeyup="updateModifyMetadataValueBinding(this)" />
                                    </div>
                                    <!--  SELECT -->
                                    <div id="metadataInputContainer_select" style="white-space: nowrap;">
                                        <select id="metadataInput_select" class="style_select inputXL" onchange="updateModifyMetadataValueBinding(this)">
                                        </select>
                                    </div>
                                    <!-- MULTISELECT -->
                                    <div id="metadataInputContainer_multiselect" style="white-space: nowrap;">
                                        <div id="metadataInput_multiselect" class="style_multiselect inputXL">
                                        </div>
                                    </div>
                                </div>


                                <input type="hidden" id="metadataSelectValues" value="" />
                                <form:hidden id="modifyContentMetadataReplaceValue" path="modifyContentMetadataReplaceValue" />

                            </div>


                        </div>

                        <!-- POPUP INTERFACE: Add Application -->
                        <div id="actionPopupAddApplication" style="padding: 0px 8px 6px 8px;" align="center">
                            <msgpt:DataTable style="width: 250px;" labelPosition="top">
                                <msgpt:TableItem label="page.label.name">
                                    <msgpt:InputFilter type="simpleName">
                                        <form:input maxlength="255" path="applicationName" cssClass="inputXL"/>
                                    </msgpt:InputFilter>
                                </msgpt:TableItem>
                                <msgpt:TableItem label="page.label.application.metatags">
                                    <msgpt:InputFilter type="simpleName">
                                        <form:input id="newApplicationMetatags" cssClass="inputXL" path="metatags"/>
                                    </msgpt:InputFilter>
                                </msgpt:TableItem>
                            </msgpt:DataTable>
                        </div>
                        <c:if test="${not empty command.currentApplication }">
                            <!-- POPUP INTERFACE: Application Properties -->
                            <div id="actionPopupApplicationProperties" style="padding: 0px 8px 6px 8px;" align="center">
                                <msgpt:DataTable style="width: 350px;" labelPosition="top">
                                    <msgpt:TableItem label="page.label.name">
                                        <msgpt:InputFilter type="simpleName">
                                            <form:input maxlength="255" id="currentApplicationName" path="currentApplication.name" cssClass="inputXL maintainValue"/>
                                        </msgpt:InputFilter>
                                    </msgpt:TableItem>
                                    <msgpt:TableItem label="page.label.application.metatags">
                                        <msgpt:InputFilter type="simpleName">
                                            <form:input id="currentApplicationMetatags"
                                                        cssClass="inputXL maintainValue"
                                                        path="currentApplication.metatags"/>
                                        </msgpt:InputFilter>
                                    </msgpt:TableItem>
                                </msgpt:DataTable>
                                <msgpt:DataTable style="width: 350px;" labelPosition="top" multiColumn="true">
                                    <c:if test="${not empty command.currentApplication.parsedDocumentFormDefinition || not empty command.currentApplication.parsedContentFormDefinition}">
                                        <c:if test="${not empty command.currentApplication.parsedDocumentFormDefinition}">
                                            <msgpt:TableItem label="page.label.document.metadata">
                                                <!-- BUTTON: DOCUMENT METADATA -->
                                                <div id="parsedDocumentMetadataTemplateBtn"
                                                     class="actionBtn_roundAll actionBtn detailTip fa-mp-container"
                                                     style="white-space: nowrap; width: 38px; height: 38px;"
                                                     title="|<div class='detailTipText'>${msgpt:getMessage('page.label.edit.document.metadata.template')}</div>">
                                                    <i class="fa fa-cog fa-mp-btn"></i>
                                                </div>
                                            </msgpt:TableItem>
                                        </c:if>
                                        <c:if test="${not empty command.currentApplication.parsedContentFormDefinition}">
                                            <msgpt:TableItem label="page.label.content.metadata">
                                                <!-- BUTTON: CONTENT METADATA -->
                                                <div id="parsedContentMetadataTemplateBtn"
                                                     class="actionBtn_roundAll actionBtn detailTip fa-mp-container"
                                                     style="white-space: nowrap; width: 38px; height: 38px;"
                                                     title="|<div class='detailTipText'>${msgpt:getMessage('page.label.edit.content.metadata.template')}</div>">
                                                    <i class="fa fa-cog fa-mp-btn"></i>
                                                </div>
                                            </msgpt:TableItem>
                                        </c:if>
                                    </c:if>
                                    <msgpt:TableItem label="page.label.application.visibility">
                                        <!-- BUTTON: APPLICATION VISIBILITY -->
                                        <div id="applicationVisibilityBtn"
                                             class="actionBtn_roundAll actionBtn detailTip fa-mp-container"
                                             style="white-space: nowrap; width: 38px; height: 38px;"
                                             title="|<div class='detailTipText'>${msgpt:getMessage('page.label.application.visibility')}</div>">
                                            <i class="fa fa-info fa-mp-btn"></i>
                                        </div>
                                    </msgpt:TableItem>
                                    <msgpt:TableItem label="page.label.workflow">
                                        <!-- BUTTON: RATIONALIZER WORKFLOW -->
                                        <div id="workflowBtn"
                                             class="actionBtn_roundAll actionBtn detailTip fa-mp-container"
                                             style="white-space: nowrap; width: 38px; height: 38px;"
                                             title="|<div class='detailTipText'>${msgpt:getMessage('page.label.workflow')}</div>">
                                            <i class="fas fa-tasks fa-lg" style="padding: 10px;"></i>
                                        </div>
                                    </msgpt:TableItem>
                                    <msgpt:TableItem label="page.label.metadata">
                                        <!-- BUTTON: METADATA POINT OF INTEREST -->
                                        <div id="metadataPointsOfInterestTemplateBtn"
                                             class="actionBtn_roundAll actionBtn detailTip fa-mp-container"
                                             style="white-space: nowrap; width: 38px; height: 38px;"
                                             title="|<div class='detailTipText'>${msgpt:getMessage('page.label.metadata.properties')}</div>">
                                            <i class="fa fa-check fa-mp-btn"></i>
                                        </div>
                                    </msgpt:TableItem>
                                </msgpt:DataTable>
                                <msgpt:DataTable style="width: 350px;" labelPosition="top">
                                    <msgpt:TableItem label="page.label.query.filters">
                                        <div id="queryFilterSelect" class="style_multiselect inputXL"
                                             style="display: none;">
                                            <!-- CURRENT ITEM SELECTIONS -->
                                            <c:forEach var="currentItem"
                                                       items="${command.currentApplication.queryFilterFormItemDefinitions}">
                                                <div id="itemDefinitionOption_${currentItem.id}">
                                                    <input id="checkbox_itemDefinition_${currentItem.id}"
                                                           name="currentApplication.queryFilterFormItemDefinitions"
                                                           value="${currentItem.id}"
                                                           type="checkbox" checked="checked"
                                                           class="style_multiselect_binding maintainValue"/>
                                                    <input type="hidden"
                                                           name="_currentApplication.queryFilterFormItemDefinitions"/>
                                                    <span><c:out value="${currentItem.name}"/></span>
                                                </div>
                                            </c:forEach>
                                            <!-- BINDING TEMPLATE -->
                                            <script class="optionTemplate" type="text/x-handlebars-template">
                                                <div id="itemDefinitionOption_{{optionId}}">
                                                    <input id="checkbox_itemDefinition_{{optionId}}"
                                                           name="currentApplication.queryFilterFormItemDefinitions"
                                                           value="{{optionId}}"
                                                           type="checkbox"
                                                           class="style_multiselect_binding maintainValue"
                                                           {{selected}}/>
                                                    <input type="hidden"
                                                           name="_currentApplication.queryFilterFormItemDefinitions"/>
                                                    <span>{{optionName}}</span>
                                                </div>
                                            </script>
                                        </div>
                                    </msgpt:TableItem>
                                </msgpt:DataTable>
                                <msgpt:DataTable style="width: 420px;" labelPosition="top">
                                    <msgpt:TableItem label="page.label.brand.profile">
                                        <form:select id="brandProfileSelect" path="currentApplication.brandProfile" cssClass="inputXL style_select">
                                            <form:option value="0"><fmtSpring:message code="page.label.none"/></form:option>
                                            <form:options items="${brandProfiles}" itemValue="id" itemLabel="name" />
                                        </form:select>
                                    </msgpt:TableItem>
                                </msgpt:DataTable>
                                <msgpt:DataTable id="reindexToggleContainer" style="width: 350px;"
                                                 labelPosition="top">
                                    <msgpt:IfAuthGranted authority="ROLE_ECATALOGUE_ADMIN">
                                        <c:if test="${not application.uploadInProgress}">
                                            <msgpt:TableItem label="page.label.resynchronization">
                                                <form:checkbox path="reSync" />
                                            </msgpt:TableItem>
                                        </c:if>
                                    </msgpt:IfAuthGranted>
                                </msgpt:DataTable>
                            </div>
                        </c:if>
                        <div id="actionPopupExtTypeSelect">
                            <div style="padding: 2px 8px 6px 8px;" align="center" onchange="toggleHistorySelect()">
                                <form:select id="extType" path="extType" cssClass="inputL">
                                    <option id="0" value="0"><fmtSpring:message code="page.label.xml"/></option>
                                    <option id="2" value="2"><fmtSpring:message code="page.label.excel"/></option>
                                </form:select>
                            </div>
                            <div id="actionHistoryInfo">
                                <fmtSpring:message code="page.label.document.export.history"/>
                            </div>
                            <div id="actionHistorySelect" style="padding: 2px 8px 6px 8px;" align="center">
                                <form:select id="historyExtType" path="historyExtType" cssClass="inputL">
                                    <option id="historyExtType_0" value="0"><fmtSpring:message code="page.label.document.export.history.none"/></option>
                                    <option id="historyExtType_1" value="1"><fmtSpring:message code="page.label.document.export.history.all"/></option>
                                    <option id="historyExtType_2" value="2"><fmtSpring:message code="page.label.document.export.history.original"/></option>
                                </form:select>
                            </div>
                        </div>
                        <div id="actionPopupName" style="padding: 0px 8px 6px 8px;" align="center">
                            <msgpt:InputFilter type="simpleName">
                                <form:input id="nameInput" path="documentName" cssClass="inputXL"/>
                            </msgpt:InputFilter>
                        </div>
                        <div id="actionPopupClone">
                            <div class="formControl">
                                <label><span class="labelText"><fmtSpring:message
                                        code="page.label.name"/></span></label>
                                <div class="controlWrapper">
                                    <msgpt:InputFilter type="simpleName">
                                        <form:input path="cloneName" onkeyup="validatePopupReq();"
                                                    onchange="validatePopupReq();" id="clone_newInstanceName"
                                                    onfocus="this.select()"/>
                                    </msgpt:InputFilter>
                                </div>
                            </div>
                        </div>
                        <div id="actionPopupNote" state="default"
                             style="padding: 3px 15px 6px 15px; white-space:nowrap;" align="left">
                            <msgpt:InputFilter type="comment">
                                <form:textarea path="userNote" onkeyup="validatePopupReq();"
                                               cssStyle="width: 270px; padding: 4px;" rows="3"
                                               onclick="initTextarea(this)"/>
                            </msgpt:InputFilter>
                        </div>
                        <div id="actionPopupUserSelect" style="padding: 2px 8px 6px 8px;" align="center">
                            <form:select id="userSelect" path="assignedToUser" cssClass="inputL"
                                         onchange="validatePopupReq()" onkeyup="validatePopupReq()">
                                <option id="0" value="0"><fmtSpring:message code="page.text.loading"/></option>
                            </form:select>
                        </div>
                        <div id="actionPopupTaskMetadataSelect">
                            <div class="formControl">
                                <label><span class="labelText"><fmtSpring:message
                                        code="page.label.metadata.optional"/></span></label>
                                <div class="controlWrapper">
                                    <select id="taskMetadataSelect" class="style_select">
                                        <option value="0"><fmtSpring:message code="page.label.none"/></option>
                                        <c:forEach var="taskMetadataFormDefinition"
                                                   items="${taskMetadataFormDefinitions}">
                                            <option id="taskMetadataFormDefinitionOption_${taskMetadataFormDefinition.id}"
                                                    value="${taskMetadataFormDefinition.id}">
                                                <c:out value="${taskMetadataFormDefinition.name}"/>
                                            </option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <!-- Add Task w/ Metadata -->
                        <div id="actionPopupTaskMetadataButtons" class="actionPopupButtonsContainer">
                            <span id="taskCustomCancelBtnEnabled">
                                <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                            </span>
                            <span id="taskCustomContinueBtnEnabled">
                                <msgpt:Button URL="javascript:iFrameAction(20);"
                                              label="page.label.continue" primary="true"/>
                            </span>
                        </div>
                        <div id="actionPopupApprovalButtons" class="actionPopupButtonsContainer">
                            <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                            <span id="approveBtn"><msgpt:Button URL="#" label="page.flow.approve"
                                                                primary="true"/></span>
                            <span id="approveBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                               label="page.flow.approve"
                                                                                               disabled="true"/></span>
                        </div>
                        <div id="actionPopupRejectButtons" class="actionPopupButtonsContainer">
                            <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                            <span id="rejectBtn"><msgpt:Button URL="#" label="page.flow.reject" primary="true"/></span>
                            <span id="rejectBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                              label="page.flow.reject"
                                                                                              disabled="true"/></span>
                        </div>
                        <div id="actionPopupCustomButtons" class="actionPopupButtonsContainer"
                             style="min-width: 450px;">
                            <span id="customCancelBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                    label="page.label.cancel"
                                                                                                    disabled="true"/></span>
                            <span id="customCancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                            label="page.label.cancel"/></span>
                            <c:if test="${empty clonedApplication && empty parentApplication && not application.beingDeleted}">
                                <span id="customCloneBtnEnabled"><msgpt:Button URL="javascript:actionSelected('7');"
                                                                               label="page.label.copy"/></span>
                            </c:if>
                            <c:if test="${not empty parentApplication}">
                                <span id="customDeleteClonedBtnEnabled"><msgpt:Button
                                        URL="javascript:actionSelected('8');" label="page.label.delete.cloned"/></span>
                            </c:if>
                            <c:if test="${empty parentApplication}">
                                <span id="customDeleteClonedBtnEnabled"><msgpt:Button
                                        URL="javascript:actionSelected('9');" label="page.label.delete"/></span>
                            </c:if>
                            <span id="customContinueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                      label="page.label.save"
                                                                                                      disabled="true"/></span>
                            <span id="customContinueBtnEnabled"><msgpt:Button URL="javascript:submitAction('4');"
                                                                              label="page.label.save"
                                                                              primary="true"/></span>
                        </div>
                        <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                            <span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                              label="page.label.cancel"
                                                                                              disabled="true"/></span>
                            <span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                      label="page.label.cancel"/></span>
                            <span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                label="page.label.continue"
                                                                                                disabled="true"/></span>
                            <span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue"
                                                                        primary="true"/></span>
                        </div>
                    </msgpt:Popup>
                </form:form>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
        <msgpt:ContextMenu name="rationalizerContentList">
            <msgpt:ContextMenuEntry name="actioniFrame_1" link="#iFrameAction:1"><fmtSpring:message
                    code="page.label.edit"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_2" link="#actionSelected:2"><fmtSpring:message
                    code="page.label.delete"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_12" link="#actionSelected:12"><fmtSpring:message
                    code="page.label.reassign.to.user"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_13" link="#actionSelected:13"><fmtSpring:message
                    code="page.label.release.for.approval"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_14" link="#actionSelected:14"><fmtSpring:message
                    code="page.label.approve"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_15" link="#actionSelected:15"><fmtSpring:message
                    code="page.label.reject"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_16" link="#actionSelected:16"><fmtSpring:message
                    code="page.label.activate"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_17" link="#actionSelected:17"><fmtSpring:message
                    code="page.label.approve.and.override"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_18" link="#actionSelected:18"><fmtSpring:message
                    code="page.label.reject.and.override"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_19" link="#actionSelected:19"><fmtSpring:message
                    code="page.label.deactivate"/></msgpt:ContextMenuEntry>
            <c:if test="${not hasTaskMetadataFormDef}">
                <msgpt:ContextMenuEntry name="actioniFrame_20" link="#iFrameAction:20"><fmtSpring:message
                        code="page.label.add.task"/>
                </msgpt:ContextMenuEntry>
            </c:if>
            <c:if test="${hasTaskMetadataFormDef}">
                <msgpt:ContextMenuEntry name="actionOption_21" link="#actionSelected:21"><fmtSpring:message
                        code="page.label.add.task"/>
                </msgpt:ContextMenuEntry>
            </c:if>
            <msgpt:ContextMenuEntry name="actionOption_30" link="#actionSelected:30"><fmtSpring:message
                    code="page.label.update.zc"/></msgpt:ContextMenuEntry>
        </msgpt:ContextMenu>
    </msgpt:BodyNew>
</msgpt:Html5>