<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="msgt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="mpt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.rationalizer" extendedScripts="true">

        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:CalendarIncludes/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>
        <msgpt:Stylesheet href="includes/javascript/contentCompare/css/diff.css"/>
        <msgpt:Script>
            <script>

                function actionCancel() {
                    var redirectURL = context + "/rationalizer/rationalizer_documents_list.form?rationalizerApplicationId=" + ${param.rationalizerApplicationId} +'&tk=${param.tk}';
                    window.location = redirectURL;
                }

                function messagesShowMarkupVersion(ele) {
                    $('#messagesMarkupContentButton').addClass('active');
                    $('#messagesRationalizerContentButton').removeClass('active');
                    $('#messagesMessagepointContentButton').removeClass('active');
                    $('.messagesMarkupText').each(function () {
                        $(this).show();
                    });
                    $('.messagesRationalierText').each(function () {
                        $(this).hide();
                    });
                    $('.messagesMessagepointText').each(function () {
                        $(this).hide();
                    });
                }

                function messagesShowRationalizerVersion(ele) {
                    $('#messagesRationalizerContentButton').addClass('active');
                    $('#messagesMarkupContentButton').removeClass('active');
                    $('#messagesMessagepointContentButton').removeClass('active');
                    $('.messagesRationalierText').each(function () {
                        $(this).show();
                    });
                    $('.messagesMarkupText').each(function () {
                        $(this).hide();
                    });
                    $('.messagesMessagepointText').each(function () {
                        $(this).hide();
                    });
                }

                function messagesShowMessagepointVersion(ele) {
                    $('#messagesMessagepointContentButton').addClass('active');
                    $('#messagesMarkupContentButton').removeClass('active');
                    $('#messagesRationalizerContentButton').removeClass('active');
                    $('.messagesMessagepointText').each(function () {
                        $(this).show();
                    });
                    $('.messagesMarkupText').each(function () {
                        $(this).hide();
                    });
                    $('.messagesRationalierText').each(function () {
                        $(this).hide();
                    });
                }

                function smartTextShowMarkupVersion(ele) {
                    $('#smartTextMarkupContentButton').addClass('active');
                    $('#smartTextRationalizerContentButton').removeClass('active');
                    $('#smartTextMessagepointContentButton').removeClass('active');
                    $('.smartTextMarkupText').each(function () {
                        $(this).show();
                    });
                    $('.smartTextRationalierText').each(function () {
                        $(this).hide();
                    });
                    $('.smartTextMessagepointText').each(function () {
                        $(this).hide();
                    });
                }

                function smartTextShowRationalizerVersion(ele) {
                    $('#smartTextRationalizerContentButton').addClass('active');
                    $('#smartTextMarkupContentButton').removeClass('active');
                    $('#smartTextMessagepointContentButton').removeClass('active');
                    $('.smartTextRationalierText').each(function () {
                        $(this).show();
                    });
                    $('.smartTextMarkupText').each(function () {
                        $(this).hide();
                    });
                    $('.smartTextMessagepointText').each(function () {
                        $(this).hide();
                    });
                }

                function smartTextShowMessagepointVersion(ele) {
                    $('#smartTextMessagepointContentButton').addClass('active');
                    $('#smartTextMarkupContentButton').removeClass('active');
                    $('#smartTextRationalizerContentButton').removeClass('active');
                    $('.smartTextMessagepointText').each(function () {
                        $(this).show();
                    });
                    $('.smartTextMarkupText').each(function () {
                        $(this).hide();
                    });
                    $('.smartTextRationalierText').each(function () {
                        $(this).hide();
                    });
                }

                $(function () {
                    var conflictHeaderHtmlString = '<div class="align-content-left" style="vertical-align: middle;">\n' +
                        '  <fmtSpring:message code="page.label.conflict"/>\n' +
                        '  <div class="inline-block-item" style="float: right">\n' +
                        '      <div class="d-flex ml-auto" role="group">\n' +
                        '          <div class="btn-group btn-group-sm">\n' +
                        '              <button id="messagesMarkupContentButton" class="btn btn-outline-primary active" type="button" onclick="messagesShowMarkupVersion(this)">\n' +
                        '                  <fmtSpring:message code="page.label.markup"/>\n' +
                        '              </button>\n' +
                        '              <button id="messagesMessagepointContentButton" class="btn btn-outline-primary" type="button" onclick="messagesShowMessagepointVersion(this)">\n' +
                        '                  <fmtSpring:message code="page.label.messagepoint"/>\n' +
                        '              </button>\n' +
                        '              <button id="messagesRationalizerContentButton" class="btn btn-outline-primary" type="button" onclick="messagesShowRationalizerVersion(this)">\n' +
                        '                  <fmtSpring:message code="page.label.rationalizer"/>\n' +
                        '              </button>\n' +
                        '          </div>\n' +
                        '      </div>\n' +
                        '  </div>\n' +
                        '</div>';
                    var actionHeaderHtmlString = '<div class="align-content-left"><fmtSpring:message code="page.label.action"/></div>';
                    $($("#messagesTable thead tr th")[1]).html(conflictHeaderHtmlString);
                    $($("#messagesTable thead tr th")[2]).html(actionHeaderHtmlString);
                });

                $(function () {
                    var conflictHeaderHtmlString = '<div class="align-content-left" style="vertical-align: middle;">\n' +
                        '    <fmtSpring:message code="page.label.conflict"/>\n' +
                        '    <div class="inline-block-item" style="float: right">\n' +
                        '        <div class="d-flex ml-auto" role="group">\n' +
                        '            <div class="btn-group btn-group-sm">\n' +
                        '                <button id="smartTextMarkupContentButton" class="btn btn-outline-primary active" type="button" onclick="smartTextShowMarkupVersion(this)">\n' +
                        '                    <fmtSpring:message code="page.label.markup"/>\n' +
                        '                </button>\n' +
                        '                <button id="smartTextMessagepointContentButton" class="btn btn-outline-primary" type="button" onclick="smartTextShowMessagepointVersion(this)">\n' +
                        '                    <fmtSpring:message code="page.label.messagepoint"/>\n' +
                        '                </button>\n' +
                        '                <button id="smartTextRationalizerContentButton" class="btn btn-outline-primary" type="button" onclick="smartTextShowRationalizerVersion(this)">\n' +
                        '                    <fmtSpring:message code="page.label.rationalizer"/>\n' +
                        '                </button>\n' +
                        '            </div>\n' +
                        '        </div>\n' +
                        '    </div>\n' +
                        '</div>';
                    var actionHeaderHtmlString = '<div class="align-content-left"><fmtSpring:message code="page.label.action"/></div>';
                    $($("#smartTextsTable thead tr th")[1]).html(conflictHeaderHtmlString);
                    $($("#smartTextsTable thead tr th")[2]).html(actionHeaderHtmlString);
                });

                $(".flipToggle_UpdateTouchpoint").each(function () {
                    var currentBinding = $(this);
                    $(this).iButton({
                        labelOn: $(currentBinding).attr('title').split(';')[0],
                        labelOff: $(currentBinding).attr('title').split(';')[1],
                        resizeHandle: false,
                        resizeContainer: "auto",
                    });
                });

            </script>
        </msgpt:Script>
        <style type="text/css">
              var[type="1"] {
                background: #ffff01;
              }
              var:not([type="1"]) {
                  background: #62f4eb;
              }

              .contentContainer .staticContentItem .staticContentItem.embedded_content_tag, .staticContentItem.embedded_content_tag {
                  background: #edf6fb;
                  white-space: normal;
                  padding: 0;
                  margin-right: 4px;
              }

              .contentContainer .staticContentItem[type="1"], .staticContentItem[type="1"], .contentContainer .innerStaticContentItem[type="1"], .innerStaticContentItem[type="1"], mpr_variable {
                  background: #ffff01;
                  margin-right: 4px;
              }

              .dropdown-toggle::after {
                  display: none;
              }

              .input-migrate-text-styles {
                  display: inline-block;
                  position: absolute;
                  margin-left: 10px;
              }
              .lbl-migrate-text-styles {
                  display: inline-block;
                  max-width: 211px;
                  padding-top: 10px;
                  margin-left: 6px;
                  margin-right: 6px;
                  font-size: 10px;
              }
        </style>
    </msgpt:HeaderNew>

    <msgpt:BodyNew>
        <msgpt:BannerNew edit="false"/>
        <msgpt:NewNavigationTabs edit="false" tab="0"/>
        <msgpt:LowerContainer fullPanel="true" extendedWidth="true">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="true">
                <form:form method="post" modelAttribute="command">
                    <form:errors path="*">
                        <msgpt:Information errorMsgs="${messages}" type="error"/>
                    </form:errors>
                    <div>
                        <h1 class="h4 pb-2 mb-3">
                                ${command.rationalizerApplication.name} >
                            <fmtSpring:message code="page.label.rationalizer.export.configuration"/>
                        </h1>
                        <h2 class="h5 pb-1 mb-1">
                            <fmtSpring:message code="page.label.touchpoint.export.tree.selection.path"
                                               arguments="${command.computeSelectedPath(param.rationalizerDocFormItemId)}" />

                        </h2>
                        <div class="box-shadow-4 rounded bg-white p-4">
                            <div class="px-2 py-1">
                                <c:if test="${param.viewType == 1}">
                                    <c:choose>
                                        <c:when test="${command.isValidTouchpoint}">
                                            <c:if test="${command.isAlreadyUsedTp}">
                                                <div class="align-content-left"
                                                     style="padding-left: 8px; padding-top:2px;">
                                                    <msgpt:Information type="warning">
                                                        <fmtSpring:message
                                                                code="page.label.rationalizer.export.input.tp.reference.used"/>
                                                    </msgpt:Information>
                                                </div>
                                            </c:if>
                                            <c:if test="${command.isOlderTp}">
                                                <div class="align-content-left"
                                                     style="padding-left: 8px; padding-top: -4px;">
                                                    <msgpt:Information type="warning">
                                                        <fmtSpring:message
                                                                code="page.label.rationalizer.export.input.tp.reference.older"/>
                                                    </msgpt:Information>
                                                </div>
                                            </c:if>
                                            <div id="zones-container">
                                                <table>
                                                    <tr>
                                                        <td class="pddng-lv1"><fmtSpring:message
                                                                code="page.label.rationalizer.export.xml.exported.on"/>
                                                        <fmtJSTL:formatDate value="${command.requestDate}" pattern="${dateFormat}" />
                                                        </td>
                                                    </tr>
                                                </table>
                                                <table>
                                                    <tr>
                                                        <td class="pddng-lv1"><fmtSpring:message
                                                                code="page.label.touchpoint.combine.within.document"/></td>
                                                        <td style="text-align: center; vertical-align: middle;"
                                                            class="pddng-lv1"><form:checkbox
                                                                path="combineWithinDocument"
                                                                value="${command.combineWithinDocument}"/></td>
                                                    </tr>
                                                </table>
                                                <br>
                                                <table>
                                                    <tr>
                                                        <td class="pddng-lv1"><fmtSpring:message
                                                                code="page.label.touchpoint.zone"/></td>
                                                        <td class="pddng-lv1"><fmtSpring:message
                                                                code="page.label.touchpoint.export.combine.to.single.message"/></td>
                                                    </tr>
                                                    <c:forEach var="currentZone" items="${command.zonesSet}"
                                                               varStatus="componentStat">
                                                        <tr>
                                                            <td class="pddng-lv1"><c:out
                                                                    value="${currentZone.zoneFriendlyName}"/> &nbsp; / &nbsp;
                                                                <c:out value="${currentZone.zoneName}"/>
                                                            </td>
                                                            <td style="text-align: center; vertical-align: middle;"
                                                                class="pddng-lv1"><form:checkbox
                                                                    path="zonesSet[${componentStat.index}].exportToSingleMessage"
                                                                    value="${currentZone.exportToSingleMessage}"/></td>
                                                        </tr>
                                                    </c:forEach>
                                                </table>
                                            </div>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="align-content-left"
                                                 style="padding-left: 15px; padding-top: 10px;">
                                                <c:forEach var="validationError" items="${command.validationErrorMessageCode}"
                                                           varStatus="validationErrorStatus">
                                                    <msgpt:Information type="error">
                                                        ${validationError}
                                                    </msgpt:Information>
                                                </c:forEach>

                                            </div>
                                        </c:otherwise>
                                    </c:choose>
                                    <div class="label pddng-lv2">
                                        <div class="align-content-left">
                                            <div class="inline-block-item">
                                                <msgpt:Button URL="javascript:actionCancel();"
                                                              label="page.label.cancel"/>
                                            </div>
                                            <c:if test="${command.isValidTouchpoint}">
                                                <div class="inline-block-item">
                                                    <msgpt:Button label="page.label.continue"
                                                                  URL="javascript:doSubmit()" primary="true"/>
                                                </div>
                                            </c:if>
                                        </div>
                                    </div>
                                </c:if>
                                <c:if test="${param.viewType == 2}">
                                    <div id="content-container">
                                        <div>
                                            <div class="label" style="padding-left: 15px">
                                                <div class="inline-block-item">
                                                    <button class="btn btn-outline-primary" type="button"
                                                            onclick="javascriptHref('rationalizer_export_to_messagepoint.form?viewType=1&rationalizerApplicationId=${param.rationalizerApplicationId}&uploadedFileName=${param.uploadedFileName}&rationalizerDocFormItemId=${param.rationalizerDocFormItemId}');">
                                                        <i class="fas fa-chevron-left" aria-hidden="true"></i>
                                                        <fmtSpring:message code="page.label.back"/>
                                                    </button>
                                                    <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                                </div>

                                                <div class="inline-block-item" style="float: right; width: 280px;">
                                                    <div class="inline-block-item" style="float: left; padding-top: 4px;">
                                                            <form:checkbox cssClass="flipToggle_UpdateTouchpoint" path="updateTouchpointOnExport"
                                                                           id="updateTouchpointOnExportCheckbox"
                                                                           title="${msgpt:getMessage('page.label.touchpoint.export.toogle.update')};${msgpt:getMessage('page.label.touchpoint.export.toogle.new')}"/>
                                                    </div>
                                                    <div class="inline-block-item" style="float: right;">
                                                        <div class="btn-group">
                                                            <msgpt:Button label="page.label.export"
                                                                          URL="javascript:doSubmit()" primary="true"/>
                                                            <button type="button" class="btn dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                                                                        style="background-color: #7c1a87; color: white;">
                                                                <i class="fas fa-chevron-double-down"></i>
                                                                <span class="sr-only">
                                                                    <fmtSpring:message code="page.label.touchpoint.export.options"/>
                                                                </span>
                                                            </button>
                                                            <div class="dropdown-menu dropdown-menu-right">
                                                                <%--<input type="checkbox" id="migrate_text_styles" checked="checked"/>--%>
                                                                <form:checkbox path="migrateTextStyle" cssClass="input-migrate-text-styles" id="migrate_text_styles"/>
                                                                <label for="migrate_text_styles" class="lbl-migrate-text-styles dropdown-item">
                                                                    <fmtSpring:message code="page.label.touchpoint.export.options.migrate.styles"/>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <c:if test="${not command.isRationalizerDataSourceReferencePresent}">
                                            <div class="align-content-left"
                                                 style="padding-left: 15px; padding-top: 10px;">
                                                <msgpt:Information type="warning">
                                                    <fmtSpring:message
                                                            code="page.label.touchpoint.export.missing.data.source"/>
                                                </msgpt:Information>
                                            </div>
                                        </c:if>

                                        <c:if test="${command.hasMetadataWithNoValues}">
                                            <div class="align-content-left"
                                                 style="padding-left: 15px; padding-top: 10px;">
                                                <msgpt:Information type="warning">
                                                    <fmtSpring:message
                                                            code="page.label.touchpoint.export.missing.metadata.values"
                                                            arguments="${command.metadataWithNoValues}"/>
                                                </msgpt:Information>
                                            </div>
                                        </c:if>

                                        <div class="align-content-left" style="padding-left: 15px; padding-top: 10px">
                                            <h3><fmtSpring:message code="page.label.messages"/></h3>
                                            <hr>
                                        </div>
                                        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                                            <!-- Unmatched Messagepoint messages -->
                                            <c:set var="unmatchedMessagepointMessagesDto"
                                                   value="${command.unmatchedMessagepointMessages}"/>
                                            <c:choose>
                                                <c:when test="${not empty unmatchedMessagepointMessagesDto.items}">
                                                    <div class="align-content-left" style="padding-left: 15px" role="tab"
                                                         class="card-header" id="headingCard">
                                                        <a style="text-decoration: none !important;"
                                                           data-parent="#accordion" data-toggle="collapse" href="#unmatchedMessagepointMessagesDiv"
                                                           aria-controls="unmatchedMessagepointMessagesDiv"
                                                           aria-expanded="false" role="button">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.export.unmatched.messagepoint.messages"
                                                                    arguments="${unmatchedMessagepointMessagesDto.totalNumberOfItems}"/>
                                                        </a>
                                                        <div class="panel-collapse collapse"
                                                             id="unmatchedMessagepointMessagesDiv"
                                                             role="tabpanel" aria-labelledby="headingCard">
                                                            <div class="panel-body">
                                                                <c:if test="${unmatchedMessagepointMessagesDto.totalNumberOfItems > unmatchedMessagepointMessagesDto.numberOfItemsStored}">
                                                                    <div style="text-align: right; font-size: 10px; font-style: italic; color: darkgray;">
                                                                        <fmtSpring:message code="page.label.touchpoint.export.table.item.number.limitation"
                                                                                           arguments="${unmatchedMessagepointMessagesDto.numberOfItemsStored}" />
                                                                    </div>
                                                                </c:if>
                                                                <msgpt:DataTable id="unmatchedMessagepointMessagesTable" drillDown="false" columnVisibility="false" columnReorder="false" numUnreorderableCols="0" multiSelect="false" paginate="false" tableTools="true" staticData="true">
                                                                    <c:forEach var="crtUnmatchedMessagepointMessage"
                                                                               items="${unmatchedMessagepointMessagesDto.items}"
                                                                               varStatus="crtUnmatchedMessagepointMessagesStat">
                                                                        <msgpt:TableListGroup>
                                                                            <msgpt:TableElement sortable="true" ssortdatatype="dom-text"
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name"
                                                                                    width="20%">
                                                                                <div style="text-align: left; vertical-align: middle; overflow: hidden; text-overflow: ellipsis; max-width: 400px;"
                                                                                     data-toggle="tooltip" data-placement="top" title="${crtUnmatchedMessagepointMessage.versionConfigDto.name}">
                                                                                        ${crtUnmatchedMessagepointMessage.versionConfigDto.name}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.content"
                                                                                    width="80%" style="color: black; font-size: smaller;">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                        ${crtUnmatchedMessagepointMessage.versionConfigDto.getContents(command.defaultLanguage)}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                        </msgpt:TableListGroup>
                                                                    </c:forEach>
                                                                </msgpt:DataTable>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="align-content-left" style="padding-left: 15px">
                                                        <fmtSpring:message
                                                                code="page.label.touchpoint.export.unmatched.messagepoint.messages"
                                                                arguments="${unmatchedMessagepointMessagesDto.totalNumberOfItems}"/>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>
                                            <!-- Unmatched Rationalizer messages -->
                                            <c:set var="unmatchedRationalizerMessagesDto"
                                                   value="${command.unmatchedRationalizerMessages}"/>
                                            <c:choose>
                                                <c:when test="${not empty unmatchedRationalizerMessagesDto.items}">
                                                    <div class="align-content-left" style="padding-left: 15px" role="tab"
                                                         class="panel-heading" id="headingCard">
                                                        <a style="text-decoration: none !important;"
                                                           data-toggle="collapse" href="#unmatchedRationalizerMessagesDiv"
                                                           data-parent="#accordion"
                                                           aria-controls="unmatchedRationalizerMessagesDiv"
                                                           aria-expanded="false" role="button">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.export.unmatched.rationalizer.messages"
                                                                    arguments="${unmatchedRationalizerMessagesDto.totalNumberOfItems}"/>
                                                        </a>

                                                        <div class="panel-collapse collapse"
                                                             id="unmatchedRationalizerMessagesDiv"
                                                             role="tabpanel" aria-labelledby="headingCard">
                                                            <div class="panel-body">
                                                                <c:if test="${unmatchedRationalizerMessagesDto.totalNumberOfItems > unmatchedRationalizerMessagesDto.numberOfItemsStored}">
                                                                    <div style="text-align: right; font-size: 10px; font-style: italic; color: darkgray;">
                                                                        <fmtSpring:message code="page.label.touchpoint.export.table.item.number.limitation"
                                                                                           arguments="${unmatchedRationalizerMessagesDto.numberOfItemsStored}" />
                                                                    </div>
                                                                </c:if>
                                                                <msgpt:DataTable id="unmatchedRationalizerMessagesTable"
                                                                                 multiColumn="true" multiSelect="false">
                                                                    <c:forEach var="crtUnmatchedRationalizerMessage"
                                                                               items="${unmatchedRationalizerMessagesDto.sortedItems}"
                                                                               varStatus="crtUnmatchedRationalizerMessagesStat">
                                                                        <msgpt:TableListGroup>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name"
                                                                                    width="20%">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                    <c:if test="${not empty crtUnmatchedRationalizerMessage.versionConfigDto.deliveryDto.zoneRefId}">
                                                                                        <p style="margin-bottom: 1px;">
                                                                                            <c:out value="${crtUnmatchedRationalizerMessage.versionConfigDto.name}" />
                                                                                        </p>
                                                                                    </c:if>
                                                                                    <c:if test="${empty crtUnmatchedRationalizerMessage.versionConfigDto.deliveryDto.zoneRefId}">
                                                                                        <p style="margin-bottom: 1px; color: red;">
                                                                                            <c:out value="${crtUnmatchedRationalizerMessage.versionConfigDto.name}" />
                                                                                            <i class="fa fa-info-circle fa-lg" style="color: red; padding-left: 8px; font-size: 13px;" data-toggle="tooltip" aria-hidden="true"
                                                                                               title="${msgpt:getMessage("page.label.touchpoint.export.unmatched.rationalizer.zoneconnector.unknown")}"></i>
                                                                                        </p>
                                                                                    </c:if>
                                                                                    <span style="font-size: 10px; font-style: italic; color: darkgray">
                                                                                        <fmtSpring:message code="page.label.touchpoint.export.rationalizer.unmatched.messages.document.name"
                                                                                                           arguments="${crtUnmatchedRationalizerMessage.rationalizerDocumentName}" />
                                                                                    </span><br/>
                                                                                    <span style="font-size: 10px; font-style: italic; color: darkgray">
                                                                                        <fmtSpring:message code="page.label.touchpoint.export.rationalizer.unmatched.messages.document.path"
                                                                                                           arguments="${crtUnmatchedRationalizerMessage.parent.fullPath}" />
                                                                                    </span>
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.content"
                                                                                    width="80%" style="color: black; font-size: smaller;">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                    ${crtUnmatchedRationalizerMessage.versionConfigDto.getContents(command.defaultLanguage)}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                        </msgpt:TableListGroup>
                                                                    </c:forEach>
                                                                </msgpt:DataTable>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="align-content-left" style="padding-left: 15px">
                                                        <fmtSpring:message
                                                                code="page.label.touchpoint.export.unmatched.rationalizer.messages"
                                                                arguments="${unmatchedRationalizerMessagesDto.totalNumberOfItems}"/>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>
                                            <!-- Unmatched Messagepoint variants -->
                                            <c:set var="unmatchedMessagepointVariantsDto"
                                                   value="${command.unmatchedMessagepointVariants}"/>
                                            <c:choose>
                                                <c:when test="${not empty unmatchedMessagepointVariantsDto.items}">
                                                    <div class="align-content-left" style="padding-left: 15px" role="tab"
                                                         class="panel-heading" id="headingCard">
                                                        <a class="collapsed" style="text-decoration: none !important;"
                                                           data-toggle="collapse" data-parent="#accordion" href="#unmatchedMessagepointVariantsDiv"
                                                           aria-controls="unmatchedMessagepointVariantsDiv"
                                                           aria-expanded="false" role="button">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.export.unmatched.messagepoint.variants"
                                                                    arguments="${unmatchedMessagepointVariantsDto.totalNumberOfItems}"/>
                                                        </a>
                                                        <div class="panel-collapse collapse"
                                                             id="unmatchedMessagepointVariantsDiv"
                                                             role="tabpanel" aria-labelledby="headingCard">
                                                            <div class="panel-body">
                                                                <c:if test="${unmatchedMessagepointVariantsDto.totalNumberOfItems > unmatchedMessagepointVariantsDto.numberOfItemsStored}">
                                                                    <div style="text-align: right; font-size: 10px; font-style: italic; color: darkgray;">
                                                                        <fmtSpring:message code="page.label.touchpoint.export.table.item.number.limitation"
                                                                                           arguments="${unmatchedMessagepointVariantsDto.numberOfItemsStored}" />
                                                                    </div>
                                                                </c:if>
                                                                <msgpt:DataTable id="unmatchedMessagepointVariantsTable"
                                                                                 multiColumn="true" multiSelect="false"
                                                                                 numUnreorderableCols="1">
                                                                    <c:forEach var="crtUnmatchedMessagepointVariant"
                                                                               items="${unmatchedMessagepointVariantsDto.items}"
                                                                               varStatus="crtUnmatchedMessagepointVariantsStat">
                                                                        <msgpt:TableListGroup>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name"
                                                                                    width="40%">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                        ${crtUnmatchedMessagepointVariant.name}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.path"
                                                                                    width="60%" style="color: darkgray;font-size: smaller;">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                       ${crtUnmatchedMessagepointVariant.path}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                        </msgpt:TableListGroup>
                                                                    </c:forEach>
                                                                </msgpt:DataTable>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="align-content-left" style="padding-left: 15px">
                                                        <fmtSpring:message
                                                                code="page.label.touchpoint.export.unmatched.messagepoint.variants"
                                                                arguments="${unmatchedMessagepointVariantsDto.totalNumberOfItems}"/>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>
                                            <!-- Unmatched Rationalizer variants -->
                                            <c:set var="unmatchedRationalizerVariantsDto"
                                                   value="${command.unmatchedRationalizerVariants}"/>
                                            <c:choose>
                                                <c:when test="${not empty unmatchedRationalizerVariantsDto.items}">
                                                    <div class="align-content-left" style="padding-left: 15px" role="tab"
                                                         class="card-header" id="headingCard">
                                                        <a class="collapsed" style="text-decoration: none !important;"
                                                           data-toggle="collapse" data-parent="#accordion" href="#unmatchedRationalizerVariantsDiv"
                                                           aria-controls="unmatchedRationalizerVariantsDiv"
                                                           aria-expanded="false" role="button">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.export.unmatched.rationalizer.variants"
                                                                    arguments="${unmatchedRationalizerVariantsDto.totalNumberOfItems}"/>
                                                        </a>
                                                        <div class="panel-collapse collapse" id="unmatchedRationalizerVariantsDiv"
                                                             role="tabpanel" aria-labelledby="headingCard">
                                                            <div class="panel-body">
                                                                <c:if test="${unmatchedRationalizerVariantsDto.totalNumberOfItems > unmatchedRationalizerVariantsDto.numberOfItemsStored}">
                                                                    <div style="text-align: right; font-size: 10px; font-style: italic; color: darkgray;">
                                                                        <fmtSpring:message code="page.label.touchpoint.export.table.item.number.limitation"
                                                                                           arguments="${unmatchedRationalizerVariantsDto.numberOfItemsStored}" />
                                                                    </div>
                                                                </c:if>
                                                                <msgpt:DataTable id="unmatchedRationalizerVariantsTable"
                                                                                 multiColumn="true" multiSelect="false">
                                                                    <c:forEach var="crtUnmatchedRationalizerVariant"
                                                                               items="${unmatchedRationalizerVariantsDto.items}"
                                                                               varStatus="crtUnmatchedRationalizerVariantsStat">
                                                                        <msgpt:TableListGroup>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name"
                                                                                    width="40%">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                        ${crtUnmatchedRationalizerVariant.name}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.path"
                                                                                    width="60%" style="color: darkgray; font-size: smaller;">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                        ${crtUnmatchedRationalizerVariant.path}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                        </msgpt:TableListGroup>
                                                                    </c:forEach>
                                                                </msgpt:DataTable>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="align-content-left" style="padding-left: 15px">
                                                        <fmtSpring:message
                                                                code="page.label.touchpoint.export.unmatched.rationalizer.variants"
                                                                arguments="${unmatchedRationalizerVariantsDto.totalNumberOfItems}"/>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>
                                            <!-- Matched variants -->
                                            <c:set var="matchedVariantsDto"
                                                   value="${command.matchedVariants}"/>
                                            <c:choose>
                                                <c:when test="${not empty matchedVariantsDto.items}">
                                                    <div class="align-content-left" style="padding-left: 15px" role="tab"
                                                         class="card-header" id="headingCard">
                                                        <a class="collapsed" style="text-decoration: none !important;"
                                                           data-toggle="collapse" data-parent="#accordion" href="#matchedVariantsDiv"
                                                           aria-controls="matchedVariantsDiv"
                                                           aria-expanded="false" role="button">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.export.matched.variants"
                                                                    arguments="${matchedVariantsDto.totalNumberOfItems}"/>
                                                        </a>
                                                        <div class="panel-collapse collapse" id="matchedVariantsDiv"
                                                             role="tabpanel" aria-labelledby="headingCard">
                                                            <div class="panel-body">
                                                                <c:if test="${matchedVariantsDto.totalNumberOfItems > matchedVariantsDto.numberOfItemsStored}">
                                                                    <div style="text-align: right; font-size: 10px; font-style: italic; color: darkgray;">
                                                                        <fmtSpring:message code="page.label.touchpoint.export.table.item.number.limitation"
                                                                                           arguments="${matchedVariantsDto.numberOfItemsStored}" />
                                                                    </div>
                                                                </c:if>
                                                                <msgpt:DataTable id="matchedVariantsTable"
                                                                                 multiColumn="true" multiSelect="false"
                                                                                 numUnreorderableCols="1">
                                                                    <c:forEach var="crtMatchedVariant"
                                                                               items="${matchedVariantsDto.items}"
                                                                               varStatus="crtMatchedVariantsStat">
                                                                        <msgpt:TableListGroup>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name"
                                                                                    width="40%">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                        ${crtMatchedVariant.name}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.path"
                                                                                    width="60%" style="color: darkgray; font-size: smaller;">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                        ${crtMatchedVariant.path}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                        </msgpt:TableListGroup>
                                                                    </c:forEach>
                                                                </msgpt:DataTable>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="align-content-left" style="padding-left: 15px">
                                                        <fmtSpring:message
                                                                code="page.label.touchpoint.export.matched.variants"
                                                                arguments="${matchedVariantsDto.totalNumberOfItems}"/>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>
                                            <!-- Matched Messages no collisions -->
                                            <c:set var="matchedMessagesNoCollisionDto"
                                                   value="${command.matchedMessagesNoCollision}"/>
                                            <c:choose>
                                                <c:when test="${not empty matchedMessagesNoCollisionDto.items}">
                                                    <div class="align-content-left" style="padding-left: 15px" role="tab"
                                                         class="card-header" id="headingCard">
                                                        <a class="collapsed" style="text-decoration: none !important;"
                                                           data-toggle="collapse" data-parent="#accordion" href="#matchedMessagesNoCollisionDiv"
                                                           aria-controls="matchedMessagesNoCollisionDiv"
                                                           aria-expanded="false" role="button">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.export.matched.messages.no.collision"
                                                                    arguments="${matchedMessagesNoCollisionDto.totalNumberOfItems}"/>
                                                        </a>
                                                        <div class="panel-collapse collapse" id="matchedMessagesNoCollisionDiv"
                                                             role="tabpanel" aria-labelledby="headingCard">
                                                            <div class="panel-body">
                                                                <c:if test="${matchedMessagesNoCollisionDto.totalNumberOfItems > matchedMessagesNoCollisionDto.numberOfItemsStored}">
                                                                    <div style="text-align: right; font-size: 10px; font-style: italic; color: darkgray;">
                                                                        <fmtSpring:message code="page.label.touchpoint.export.table.item.number.limitation"
                                                                                           arguments="${matchedMessagesNoCollisionDto.numberOfItemsStored}" />
                                                                    </div>
                                                                </c:if>
                                                                <msgpt:DataTable id="matchedMessagesNoCollisionTable"
                                                                                 multiColumn="true" multiSelect="false"
                                                                                 numUnreorderableCols="1">
                                                                    <c:forEach var="crtMatchedMessagesNoCollision"
                                                                               items="${matchedMessagesNoCollisionDto.items}"
                                                                               varStatus="crtMatchedMessagesNoCollisionStat">
                                                                        <msgpt:TableListGroup>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name"
                                                                                    width="100%">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                        ${crtMatchedMessagesNoCollision.versionConfigDto.name}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                        </msgpt:TableListGroup>
                                                                    </c:forEach>
                                                                </msgpt:DataTable>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="align-content-left" style="padding-left: 15px">
                                                        <fmtSpring:message
                                                                code="page.label.touchpoint.export.matched.messages.no.collision"
                                                                arguments="${matchedMessagesNoCollisionDto.totalNumberOfItems}"/>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>
                                            <!-- Matched Messages with collisions -->
                                            <div class="align-content-left" style="padding-left: 15px">
                                                <div>${command.matchedMessagesWithCollisionString}</div>
                                            </div>
                                            <c:if test="${not empty command.touchpointMessageCollisionsList}">
                                                <div class="align-content-left"
                                                     style="padding-left: 15px; padding-top: 10px">

                                                    <msgpt:DataTable style="width: 100%" id="messagesTable"
                                                                     listHeader="page.label.touchpoint.export.messages.collision"
                                                                     multiColumn="true" multiSelect="true"
                                                                     numUnreorderableCols="3">
                                                        <c:forEach var="crtCollision"
                                                                   items="${command.touchpointMessageCollisionsList}"
                                                                   varStatus="collisionStat">
                                                            <msgpt:TableListGroup>
                                                                <msgpt:TableElement width="90%">
                                                                    <div style="text-align: left; vertical-align: middle;">
                                                                        <div style="font-size: 12px; font-weight: bold; padding-bottom: 5px;">${crtCollision.messageCollisionDto.rationalizerMessage.versionConfigDto.name}</div>
                                                                        <div class="messagesMarkupText">${crtCollision.markupContentForUI}</div>
                                                                        <div class="messagesRationalierText"
                                                                             style="display: none">${crtCollision.rationalizerContentForUI}</div>
                                                                        <div class="messagesMessagepointText"
                                                                             style="display: none">${crtCollision.messagepointContent}</div>
                                                                    </div>
                                                                </msgpt:TableElement>
                                                                <msgpt:TableElement width="10%">
                                                                    <div>
                                                                        <form:select
                                                                                path="touchpointMessageCollisionsList[${collisionStat.index}].resolutionId"
                                                                                cssClass="inputL">
                                                                            <option id="0" value="0"><fmtSpring:message
                                                                                    code="page.label.ignore"/></option>
                                                                            <option id="1" value="1" selected="selected">
                                                                                <fmtSpring:message
                                                                                        code="page.label.overwrite"/></option>
                                                                            <option id="2" value="2"><fmtSpring:message
                                                                                    code="page.label.duplicate"/></option>
                                                                        </form:select>
                                                                    </div>
                                                                </msgpt:TableElement>
                                                            </msgpt:TableListGroup>
                                                        </c:forEach>
                                                    </msgpt:DataTable>
                                                </div>
                                            </c:if>

                                            <div class="align-content-left" style="padding-left: 15px; padding-top: 24px">
                                                <h3><fmtSpring:message code="page.label.smart.texts"/></h3>
                                                <hr>
                                            </div>

                                            <!-- unmatchedMessagepointEmbeddedContents -->
                                            <c:set var="unmatchedMessagepointEmbeddedContentsDto"
                                                   value="${command.unmatchedMessagepointEmbeddedContents}"/>
                                            <c:choose>
                                                <c:when test="${not empty unmatchedMessagepointEmbeddedContentsDto.items}">
                                                    <div class="align-content-left" style="padding-left: 15px" role="tab"
                                                         class="card-header" id="headingCard">
                                                        <a class="collapsed" style="text-decoration: none !important;"
                                                           data-toggle="collapse" data-parent="#accordion"
                                                           href="#unmatchedMessagepointEmbeddedContentsDiv"
                                                           aria-controls="unmatchedMessagepointEmbeddedContentsDiv"
                                                           aria-expanded="false" role="button">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.export.unmatched.messagepoint.embedded.contents"
                                                                    arguments="${unmatchedMessagepointEmbeddedContentsDto.totalNumberOfItems}"/>
                                                        </a>
                                                        <div class="panel-collapse collapse" id="unmatchedMessagepointEmbeddedContentsDiv"
                                                             role="tabpanel" aria-labelledby="headingCard">
                                                            <div class="panel-body">
                                                                <c:if test="${unmatchedMessagepointEmbeddedContentsDto.totalNumberOfItems > unmatchedMessagepointEmbeddedContentsDto.numberOfItemsStored}">
                                                                    <div style="text-align: right; font-size: 10px; font-style: italic; color: darkgray;">
                                                                        <fmtSpring:message code="page.label.touchpoint.export.table.item.number.limitation"
                                                                                           arguments="${unmatchedMessagepointEmbeddedContentsDto.numberOfItemsStored}" />
                                                                    </div>
                                                                </c:if>
                                                                <msgpt:DataTable
                                                                        id="unmatchedMessagepointEmbeddedContentsTable"
                                                                         multiSelect="false"
                                                                        numUnreorderableCols="2">
                                                                    <c:forEach var="crtUnmatchedMessagepointEmbeddedContent"
                                                                               items="${unmatchedMessagepointEmbeddedContentsDto.items}"
                                                                               varStatus="crtUnmatchedMessagepointEmbeddedContentsStat">
                                                                        <msgpt:TableListGroup>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name"
                                                                                    width="20%">
                                                                                <div style="text-align: left; vertical-align: middle;">${crtUnmatchedMessagepointEmbeddedContent.versionConfigDto.name}</div>
                                                                            </msgpt:TableElement>
                                                                            <msgpt:TableElement align="left"
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.content"
                                                                                    width="80%" style="color: black; font-size: smaller;">
                                                                                <div style="text-align: left; vertical-align: middle;">${crtUnmatchedMessagepointEmbeddedContent.versionConfigDto.getContents(command.defaultLanguage)}</div>
                                                                            </msgpt:TableElement>
                                                                        </msgpt:TableListGroup>
                                                                    </c:forEach>
                                                                </msgpt:DataTable>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="align-content-left" style="padding-left: 15px">
                                                        <fmtSpring:message
                                                                code="page.label.touchpoint.export.unmatched.messagepoint.embedded.contents"
                                                                arguments="${unmatchedMessagepointEmbeddedContentsDto.totalNumberOfItems}"/>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>

                                            <!-- unmatchedRationalizerEmbeddedContents -->
                                            <c:set var="unmatchedRationalizerEmbeddedContentsDto"
                                                   value="${command.unmatchedRationalizerEmbeddedContents}"/>
                                            <c:choose>
                                                <c:when test="${not empty unmatchedRationalizerEmbeddedContentsDto.items}">
                                                    <div class="align-content-left" style="padding-left: 15px" role="tab"
                                                         class="card-header" id="headingCard">
                                                        <a class="collapsed" style="text-decoration: none !important;"
                                                           data-toggle="collapse" data-parent="#accordion"
                                                           href="#unmatchedRationalizerEmbeddedContentsDiv"
                                                           aria-controls="unmatchedRationalizerEmbeddedContentsDiv"
                                                           aria-expanded="false" role="button">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.export.unmatched.rationalizer.shared.objects"
                                                                    arguments="${unmatchedRationalizerEmbeddedContentsDto.totalNumberOfItems}"/>
                                                        </a>
                                                        <div class="panel-collapse collapse" id="unmatchedRationalizerEmbeddedContentsDiv"
                                                             role="tabpanel" aria-labelledby="headingCard">
                                                            <div class="panel-body">
                                                                <c:if test="${unmatchedRationalizerEmbeddedContentsDto.totalNumberOfItems > unmatchedRationalizerEmbeddedContentsDto.numberOfItemsStored}">
                                                                    <div style="text-align: right; font-size: 10px; font-style: italic; color: darkgray;">
                                                                        <fmtSpring:message code="page.label.touchpoint.export.table.item.number.limitation"
                                                                                           arguments="${unmatchedRationalizerEmbeddedContentsDto.numberOfItemsStored}" />
                                                                    </div>
                                                                </c:if>
                                                                <msgpt:DataTable
                                                                        id="unmatchedRationalizerEmbeddedContentsTable"
                                                                        multiColumn="true" multiSelect="false"
                                                                        numUnreorderableCols="2">
                                                                    <c:forEach var="crtUnmatchedRationalizerEmbeddedContent"
                                                                               items="${unmatchedRationalizerEmbeddedContentsDto.items}"
                                                                               varStatus="crtUnmatchedRationalizerEmbeddedContentsStat">
                                                                        <msgpt:TableListGroup>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name"
                                                                                    width="20%">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                        ${crtUnmatchedRationalizerEmbeddedContent.versionConfigDto.name}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.content"
                                                                                    width="80%" style="color: black; font-size: smaller;">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                        ${crtUnmatchedRationalizerEmbeddedContent.versionConfigDto.getContents(command.defaultLanguage)}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                        </msgpt:TableListGroup>
                                                                    </c:forEach>
                                                                </msgpt:DataTable>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="align-content-left" style="padding-left: 15px">
                                                        <fmtSpring:message
                                                                code="page.label.touchpoint.export.unmatched.rationalizer.shared.objects"
                                                                arguments="${unmatchedRationalizerEmbeddedContentsDto.totalNumberOfItems}"/>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>

                                            <!-- matchedEmbeddedContentsNoCollision -->
                                            <c:set var="matchedEmbeddedContentsNoCollisionDto"
                                                   value="${command.matchedEmbeddedContentsNoCollision}"/>
                                            <c:choose>
                                                <c:when test="${not empty matchedEmbeddedContentsNoCollisionDto.items}">
                                                    <div class="align-content-left" style="padding-left: 15px" role="tab"
                                                         class="card-header" id="headingCard">
                                                        <a class="collapsed" style="text-decoration: none !important;"
                                                           data-toggle="collapse" data-parent="#accordion"
                                                           href="#matchedEmbeddedContentsNoCollisionDiv"
                                                           aria-controls="matchedEmbeddedContentsNoCollisionDiv"
                                                           aria-expanded="false" role="button">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.export.matched.embedded.contents.no.collision"
                                                                    arguments="${matchedEmbeddedContentsNoCollisionDto.totalNumberOfItems}"/>
                                                        </a>
                                                        <div class="panel-collapse collapse" id="matchedEmbeddedContentsNoCollisionDiv"
                                                             role="tabpanel" aria-labelledby="headingCard">
                                                            <div class="panel-body">
                                                                <c:if test="${matchedEmbeddedContentsNoCollisionDto.totalNumberOfItems > matchedEmbeddedContentsNoCollisionDto.numberOfItemsStored}">
                                                                    <div style="text-align: right; font-size: 10px; font-style: italic; color: darkgray;">
                                                                        <fmtSpring:message code="page.label.touchpoint.export.table.item.number.limitation"
                                                                                           arguments="${matchedEmbeddedContentsNoCollisionDto.numberOfItemsStored}" />
                                                                    </div>
                                                                </c:if>
                                                                <msgpt:DataTable
                                                                        id="matchedEmbeddedContentsNoCollisionTable"
                                                                        multiColumn="true" multiSelect="false"
                                                                        numUnreorderableCols="2">
                                                                    <c:forEach var="crtMatchedEmbeddedContentNoCollision"
                                                                               items="${matchedEmbeddedContentsNoCollisionDto.items}"
                                                                               varStatus="crtMatchedEmbeddedContentsNoCollisionStat">
                                                                        <msgpt:TableListGroup>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name"
                                                                                    width="30%">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                        ${crtMatchedEmbeddedContentNoCollision.versionConfigDto.name}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                            <msgpt:TableElement
                                                                                    label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.content"
                                                                                    width="70%" style="color: black; font-size: smaller;">
                                                                                <div style="text-align: left; vertical-align: middle;">
                                                                                        ${crtMatchedEmbeddedContentNoCollision.versionConfigDto.getContents(command.defaultLanguage)}
                                                                                </div>
                                                                            </msgpt:TableElement>
                                                                        </msgpt:TableListGroup>
                                                                    </c:forEach>
                                                                </msgpt:DataTable>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="align-content-left" style="padding-left: 15px">
                                                        <fmtSpring:message
                                                                code="page.label.touchpoint.export.matched.embedded.contents.no.collision"
                                                                arguments="${matchedEmbeddedContentsNoCollisionDto.totalNumberOfItems}"/>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>

                                            <div class="align-content-left" style="padding-left: 15px">
                                                <div>${command.matchedEmbeddedContentsWithCollisionString}</div>
                                            </div>

                                            <c:if test="${not empty command.touchpointEmbeddedContentCollisionsList}">
                                                <div class="align-content-left"
                                                     style="padding-left: 15px; padding-top: 10px">

                                                    <msgpt:DataTable id="smartTextsTable"
                                                                     listHeader="page.label.touchpoint.export.smart.texts.collision"
                                                                     multiColumn="true" multiSelect="true"
                                                                     numUnreorderableCols="3">
                                                        <c:forEach var="crtCollision"
                                                                   items="${command.touchpointEmbeddedContentCollisionsList}"
                                                                   varStatus="collisionStat">
                                                            <msgpt:TableListGroup>
                                                                <msgpt:TableElement width="90%">
                                                                    <div style="text-align: left; vertical-align: middle;">
                                                                        <div class="smartTextMarkupText">${crtCollision.markupContent}</div>
                                                                        <div class="smartTextRationalierText"
                                                                             style="display: none">${crtCollision.rationalizerContent}</div>
                                                                        <div class="smartTextMessagepointText"
                                                                             style="display: none">${crtCollision.messagepointContent}</div>
                                                                    </div>
                                                                </msgpt:TableElement>
                                                                <msgpt:TableElement width="10%">
                                                                    <div>
                                                                        <form:select
                                                                                path="touchpointEmbeddedContentCollisionsList[${collisionStat.index}].resolutionId"
                                                                                cssClass="inputL">
                                                                            <option id="0" value="0"><fmtSpring:message
                                                                                    code="page.label.ignore"/></option>
                                                                            <option id="1" value="1" selected="selected">
                                                                                <fmtSpring:message
                                                                                        code="page.label.overwrite"/></option>
                                                                            <option id="2" value="2"><fmtSpring:message
                                                                                    code="page.label.duplicate"/></option>
                                                                        </form:select>
                                                                    </div>
                                                                </msgpt:TableElement>
                                                            </msgpt:TableListGroup>
                                                        </c:forEach>
                                                    </msgpt:DataTable>
                                                </div>
                                            </c:if>

                                            <c:if test="${command.isRationalizerDataSourceReferencePresent}">
                                                <div class="align-content-left" style="padding-left: 15px; padding-top: 10px">
                                                    <h3><fmtSpring:message code="page.label.touchpoint.export.variables"/></h3>
                                                    <hr>
                                                </div>

                                                <!-- matchedVariables -->
                                                <c:set var="matchedVariablesDto"
                                                       value="${command.matchedVariables}"/>
                                                <c:choose>
                                                    <c:when test="${not empty matchedVariablesDto.items}">
                                                        <div class="align-content-left" style="padding-left: 15px" role="tab"
                                                             class="card-header" id="headingCard">
                                                            <a class="collapsed" style="text-decoration: none !important;"
                                                               data-toggle="collapse" data-parent="#accordion" href="#matchedVariablesDiv"
                                                               aria-controls="matchedVariablesDiv"
                                                               aria-expanded="false" role="button">
                                                                <fmtSpring:message
                                                                        code="page.label.touchpoint.export.matched.variables"
                                                                        arguments="${matchedVariablesDto.totalNumberOfItems}"/>
                                                            </a>
                                                            <div class="panel-collapse collapse" id="matchedVariablesDiv"
                                                                 role="tabpanel" aria-labelledby="headingCard">
                                                                <div class="panel-body">
                                                                    <c:if test="${matchedVariablesDto.totalNumberOfItems > matchedVariablesDto.numberOfItemsStored}">
                                                                        <div style="text-align: right; font-size: 10px; font-style: italic; color: darkgray;">
                                                                            <fmtSpring:message code="page.label.touchpoint.export.table.item.number.limitation"
                                                                                               arguments="${matchedVariablesDto.numberOfItemsStored}" />
                                                                        </div>
                                                                    </c:if>
                                                                    <msgpt:DataTable id="matchedVariablesTable"
                                                                                     multiColumn="true" multiSelect="false"
                                                                                     numUnreorderableCols="1">
                                                                        <c:forEach var="crtMatchedVariable"
                                                                                   items="${matchedVariablesDto.items}"
                                                                                   varStatus="crtMatchedVariablesStat">
                                                                            <msgpt:TableListGroup>
                                                                                <msgpt:TableElement
                                                                                        label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name"
                                                                                        width="100%">
                                                                                    <div style="text-align: left; vertical-align: middle;">
                                                                                            ${crtMatchedVariable.name}
                                                                                    </div>
                                                                                </msgpt:TableElement>
                                                                            </msgpt:TableListGroup>
                                                                        </c:forEach>
                                                                    </msgpt:DataTable>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <div class="align-content-left" style="padding-left: 15px">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.export.matched.variables"
                                                                    arguments="${matchedVariablesDto.totalNumberOfItems}"/>
                                                        </div>
                                                    </c:otherwise>
                                                </c:choose>

                                                <!-- unmatchedMessagepointVariables -->
                                                <c:set var="unmatchedMessagepointVariablesDto"
                                                       value="${command.unmatchedMessagepointVariables}"/>
                                                <c:choose>
                                                    <c:when test="${not empty unmatchedMessagepointVariablesDto.items}">
                                                        <div class="align-content-left" style="padding-left: 15px;" role="tab"
                                                             class="card-header" id="headingCard">
                                                            <a class="collapsed" style="text-decoration: none !important;"
                                                               data-toggle="collapse" data-parent="#accordion" href="#unmatchedMessagepointVariablesDiv"
                                                               aria-controls="unmatchedMessagepointVariablesDiv"
                                                               aria-expanded="false" role="treeitem">
                                                                <fmtSpring:message
                                                                        code="page.label.touchpoint.export.unmatched.messagepoint.variables"
                                                                        arguments="${unmatchedMessagepointVariablesDto.totalNumberOfItems}"/>
                                                            </a>
                                                            <div class="panel-collapse collapse" id="unmatchedMessagepointVariablesDiv"
                                                                 role="tabpanel" aria-labelledby="headingCard">
                                                                <div class="panel-body">
                                                                    <c:if test="${unmatchedMessagepointVariablesDto.totalNumberOfItems > unmatchedMessagepointVariablesDto.numberOfItemsStored}">
                                                                        <div style="text-align: right; font-size: 10px; font-style: italic; color: darkgray;">
                                                                            <fmtSpring:message code="page.label.touchpoint.export.table.item.number.limitation"
                                                                                               arguments="${unmatchedMessagepointVariablesDto.numberOfItemsStored}" />
                                                                        </div>
                                                                    </c:if>
                                                                    <msgpt:DataTable id="unmatchedMessagepointVariablesTable"
                                                                                     multiColumn="true" multiSelect="false"
                                                                                     numUnreorderableCols="1" sort="true">
                                                                        <c:forEach var="crtUnmatchedMessagepointVariable"
                                                                                   items="${unmatchedMessagepointVariablesDto.items}"
                                                                                   varStatus="crtUnmatchedMessagepointVariablesStat">
                                                                            <msgpt:TableListGroup>
                                                                                <msgpt:TableElement
                                                                                        label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name"
                                                                                        width="50%">
                                                                                    <div style="text-align: left; vertical-align: middle;">
                                                                                        ${crtUnmatchedMessagepointVariable.name}
                                                                                    </div>
                                                                                </msgpt:TableElement>
                                                                                <msgpt:TableElement
                                                                                        label="page.label.touchpoint.export.messagespoint.unmatched.variables.data.element"
                                                                                        width="50%">
                                                                                    <div style="text-align: left; vertical-align: middle;">
                                                                                        <span style="font-size: 10px; font-style: italic; color: darkgray">
                                                                                            ${crtUnmatchedMessagepointVariable.dataElementDto.value}
                                                                                        </span>
                                                                                    </div>
                                                                                </msgpt:TableElement>
                                                                            </msgpt:TableListGroup>
                                                                        </c:forEach>
                                                                    </msgpt:DataTable>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <div class="align-content-left" style="padding-left: 15px">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.export.unmatched.messagepoint.variables"
                                                                    arguments="${unmatchedMessagepointVariablesDto.totalNumberOfItems}"/>
                                                        </div>
                                                    </c:otherwise>
                                                </c:choose>

                                                <!-- unmatchedRationalizerVariables -->
                                                <c:set var="unmatchedRationalizerVariablesDto"
                                                       value="${command.unmatchedRationalizerVariables}"/>
                                                <c:choose>
                                                    <c:when test="${not empty unmatchedRationalizerVariablesDto.items}">
                                                        <div class="align-content-left" style="padding-left: 15px" role="tab"
                                                             class="card-header" id="headingCard">
                                                            <a class="collapsed" style="text-decoration: none !important;"
                                                               data-toggle="collapse" data-parent="#accordion" href="#unmatchedRationalizerVariablesDiv"
                                                               aria-controls="unmatchedRationalizerVariablesDiv"
                                                               aria-expanded="false" role="treeitem">
                                                                <fmtSpring:message
                                                                        code="page.label.touchpoint.export.unmatched.rationalizer.variables"
                                                                        arguments="${unmatchedRationalizerVariablesDto.totalNumberOfItems}"/>
                                                            </a>
                                                            <div class="panel-collapse collapse" id="unmatchedRationalizerVariablesDiv"
                                                                 role="tabpanel" aria-labelledby="headingCard">
                                                                <div class="panel-body">
                                                                    <c:if test="${unmatchedRationalizerVariablesDto.totalNumberOfItems > unmatchedRationalizerVariablesDto.numberOfItemsStored}">
                                                                        <div style="text-align: right; font-size: 10px; font-style: italic; color: darkcyan;">
                                                                            <fmtSpring:message code="page.label.touchpoint.export.table.item.number.limitation"
                                                                                               arguments="${unmatchedRationalizerVariablesDto.numberOfItemsStored}" />
                                                                        </div>
                                                                    </c:if>
                                                                    <msgpt:DataTable id="unmatchedRationalizerVariablesTable"
                                                                                     multiColumn="true" multiSelect="false"
                                                                                     numUnreorderableCols="1">
                                                                        <c:forEach var="crtUnmatchedRationalizerVariable"
                                                                                   items="${unmatchedRationalizerVariablesDto.items}"
                                                                                   varStatus="crtUnmatchedRationalizerVariablesStat">
                                                                            <msgpt:TableListGroup>
                                                                                <msgpt:TableElement
                                                                                        label="page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name"
                                                                                        width="100%">
                                                                                    <div style="text-align: left; vertical-align: middle;">
                                                                                            ${crtUnmatchedRationalizerVariable.name}
                                                                                    </div>
                                                                                </msgpt:TableElement>
                                                                            </msgpt:TableListGroup>
                                                                        </c:forEach>
                                                                    </msgpt:DataTable>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <div class="align-content-left" style="padding-left: 15px">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.export.unmatched.rationalizer.variables"
                                                                    arguments="${unmatchedRationalizerVariablesDto.totalNumberOfItems}"/>
                                                        </div>
                                                    </c:otherwise>
                                                </c:choose>

                                            </c:if>
                                        </div>
                                    </div>
                                </c:if>
                            </div>
                        </div>
                    </div>
                </form:form>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
    </msgpt:BodyNew>
</msgpt:Html5>