<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>

    <msgpt:HeaderNew>

        <msgpt:Script src="includes/javascript/popupActions.js"/>

        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>

        <msgpt:Script src="rationalizer/javascript/rationalizerDashboardUtil.js"/>

        <msgpt:CalendarIncludes/>

        <msgpt:Script>
            <script>
                var iFramePopup_fullFrameAttr_cust = JSON.parse(JSON.stringify(iFramePopup_fullFrameAttr));
                iFramePopup_fullFrameAttr_cust.width = 1240;

                function iFrameView(path, eleId, e) {
                    if ( getTopFrame().getParam('windowType') == 'stand_alone' ) {
                        var rationalizerDocumentId = getParamFromURL(path,'rationalizerDocumentId');
                        var rationalizerApplicationId = getParamFromURL(path,'rationalizerApplicationId');
                        var selectedContentId = getParamFromURL(path,'selectedContentId');

                        if (getTopFrame().parent.opener != undefined && getTopFrame().parent.opener != null && rationalizerDocumentId != '' && rationalizerApplicationId != '') {
                            var openerRedirectURL = context + "/rationalizer/rationalizer_documents_list.form?rationalizerApplicationId=" + rationalizerApplicationId +
                                "&rationalizerDocumentId=" + rationalizerDocumentId +
                                "&tk=" + getTopFrame().getParam('tk');
                            if ( selectedContentId != '')
                                openerRedirectURL += '&selectedContentId=' + selectedContentId;
                            getTopFrame().parent.opener.location.href = openerRedirectURL;
                        }
                    } else {
                        if (e != undefined) {
                            if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
                                $("#" + eleId).iFramePopup($.extend({
                                    src: path,
                                    displayOnInit: true,
                                    id: "rationalizerDocumentFrame",
                                    screenMask: false,
                                    appliedParams: {tk: "${param.tk}"},
                                    beforePopupClose: function () {
                                        $(getTopFrame().document).find('.mce-custompanel,#iFramePopup_brandCheck,#iFramePopup_contentCompare').remove();
                                        //rebuildListTable();
                                    }
                                }, iFramePopup_fullFrameAttr_cust));
                            }
                            e.preventDefault ? e.stopPropagation() : e.returnValue = false;
                        }
                    }
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.columns = [
                        {
                            columnMap: 'name',
                            columnName: client_messages.text.name,
                            width: '90%',
                            sort: false
                        }
                    ];

                    return obj;
                }

                function getAsyncExtParams() {
                    var obj = [
                        {"name": "listTableType", "value": "50"},
                        {
                            "name": "rationalizerApplicationId",
                            "value": getParam('rationalizerApplicationId') != "" ? getParam('rationalizerApplicationId') : -1
                        },
                        {
                            "name": "metadataPointOfInterestId",
                            "value": getParam('metadataPointOfInterestId')
                        },
                        {
                            "name": "sourceDashboardCompareSelection", "value": getSourceSelectedNavTreeBranches()
                        }
                    ];
                    return obj;
                }

                function postListDrawCallback(nTable, o) {
                    $(nTable).find('.dataTableLink,.dataTableSecondaryLink').each(function () {

                        var instId = parseId($(this));
                        $('.expandableTxtEle').each(function () {
                            initExpandableTxt($(this));
                        });
                    });

                    $(nTable).find('thead th .dataTableRecordCount').remove();
                    $(nTable).find('thead th:nth-of-type(2) div').append("<span class=\"dataTableRecordCount\" style=\"padding-left: 12px;\">(" + o._iRecordsTotal.toLocaleString() + ")</span>");

                    $(nTable).find('[data-toggle="tooltip"]').tooltip();

                    adjustIFrameHeight(frameElement);
                }

                function adjustIFrameHeight(ele) {
                    if (ele == null || !ele.contentWindow) {
                        return;
                    }
                    var contentHeight = $(ele.contentWindow.document.body).find('#page').height();
                    $(ele).css('height', contentHeight + 'px');
                    window.parent.adjustIFrameHeight(window.frameElement);
                    common.refreshParentIframeHeight();
                }

            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <msgpt:BodyNew type="minimal" cssStyle="background-color: transparent;">
        <div class="dataTables_processing">
            <div class="dataTablesContentWrapper">
                <msgpt:DataTable id="rationalizerMetadataDetails_${timeStamp}"
                                 async="true"
                                 numUnreorderableCols="0"
                                 columnVisibility="true"
                                 searchFilter="true"
                                 permitsShowAll="false"
                                 multiSelect="true">
                </msgpt:DataTable>
            </div>
        </div>
    </msgpt:BodyNew>
</msgpt:Html5>
