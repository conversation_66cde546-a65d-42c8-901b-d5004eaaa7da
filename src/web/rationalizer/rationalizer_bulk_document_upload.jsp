<%@ taglib prefix="input" uri="http://www.springframework.org/tags/form" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>

    <msgpt:HeaderNew extendedScripts="true">

        <!-- The Templates plugin is included to render the upload/download listings -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/tmpl.min.js"/>
        <!-- The Load Image plugin is included for the preview images and image resizing functionality -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/load-image.min.js"/>
        <!-- The Canvas to Blob plugin is included for image resizing functionality -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/canvas-to-blob.min.js"/>
        <!-- jQuery Image Gallery -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.image-gallery.min.js"/>
        <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.iframe-transport.js"/>
        <!-- The basic File Upload plugin -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.fileupload.js"/>
        <!-- The File Upload file processing plugin -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.fileupload-fp.js"/>
        <!-- The File Upload user interface plugin -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.fileupload-ui.js"/>
        <!-- The File Upload jQuery UI plugin -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.fileupload-jui.js"/>

        <!-- CSS to style the file input field as button and adjust the jQuery UI progress bars -->
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/fileUpload/css/jquery.fileupload-ui.css"/>

        <msgpt:Script src="includes/javascript/handlebars/handlebars-v4.7.8.js"/>

        <style type="text/css">

            .btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active, .show > .btn-light.dropdown-toggle {
                color: white;
                background-color: #6d3075;
                /*border: 1px solid #afafaf;*/
                /*border-color: #afafaf;*/
            }
            .btn-outline-primary {
                color: black;
                background-color: white;
                border-color: white;
                padding-left: 3px;
                width: 100px;
                height: 20px;

            }
            .btn-outline-primary:hover {
                color: black;
                background-color: white;
                border-color: white; }

            .btn-outline-primary:focus, .btn-outline-primary.focus {
                box-shadow: 0 0 0 0; }

            .btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus,
            .show > .btn-outline-primary.dropdown-toggle:focus {
                box-shadow: 0 0 0 0; }

            .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
                background: url('../includes/javascript/jQueryPlugins/styledInterfaces/images/fill_grads.gif') repeat-x 0px 0px;
                border: 1px solid #bbb;
            }

            .ui-button .ui-button-text {
                padding: 0em 1.0em 0.1em 2.1em;
                line-height: 1.5;
                text-shadow: 0px 1px 0px #fff;
            }

            .fileinput-button .ui-button-text {
                padding-top: 0.1em;
            }

            .ui-widget {
                font-size: 1em;
            }

            .ui-widget-content {
                border: 1px solid #ddd;
            }

            .fileupload-buttonbar .ui-button {
                margin: 5px 0px;
            }

            .fileupload-buttonbar .fileinput-button {
                margin-right: 5px;
            }

            .fileupload-progress {
                font-size: 11px;
            }

            .fileupload-progress {
                font-size: 10px;
                line-height: 12px;
            }

            .table-striped tbody > tr:nth-child(2n+1) > td, .table-striped tbody > tr:nth-child(2n+1) > th {
                background-color: #F9F9F9;
            }

            .table th, .table td {
                border-top: 1px solid #DDDDDD;
                line-height: 20px;
                padding: 8px;
                text-align: left;
                vertical-align: top;
                white-space: nowrap;
            }

            .table td.delete, .table td.cancel {
                text-align: right;
                padding-right: 20px;
            }

            .table td.preview {
                padding-left: 15px;
            }

            .table {
                border-collapse: collapse;
                border-spacing: 0;
            }

            .progress {
                background-color: #F7F7F7;
                background-image: linear-gradient(to bottom, #F5F5F5, #F9F9F9);
                background-repeat: repeat-x;
                border-radius: 4px 4px 4px 4px;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset;
                height: 20px;
                overflow: hidden;
            }

            .files .progress {
                width: 100px;
            }

            .fileSize {
                font-size: 10px;
            }

            .sandboxFileContainer {
                height: 250px;
                overflow: auto;
                background-color: #fff;
                border-radius: 0px 0px 6px 6px;
                border: 1px solid #bbb;
                border-top: 0px;
            }

            #metatagsContainer {
                background-color: #eee;
                padding: 5px 15px;
            }

            #initialInfo {
                background-color: #fff;
            }

            #completeInfo {
                background-color: #fff;
            }

            canvas {
                border: 1px solid #eee;
                -moz-box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
                -webkit-box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
                box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
            }

            body {
                padding-top: 0px;
                line-height: 18px;
                margin: 10px;
                color: #333333;
            }

            td {
                padding: 5px;
            }

        </style>

        <msgpt:Script>
            <script>

                var totalUploadedNo = 0;
                var totalProcessedNo = 0;
                var totalWithError = 0;
                var totalWithUploadLimitError = 0;
                var showLog = ${hasApplicationLog};
                var requestGuid = 0;
                var first = true;
                var reindexInProgress= ${reindexInProgress};
                var tabIngestionSettings = ['consolidationSettings', 'ingestionVariablesSettings', 'ingestionPhoneSettings', 'ingestionZoneSettings', 'ingestionPdfSettings'];

                function openSettingsTab(evt, settingsName) {
                    if ($('#ingestionApp')[0].checked) {
                        var i, tabcontent, tablinks;
                        tabcontent = document.getElementsByClassName("tabcontent");
                        for (i = 0; i < tabcontent.length; i++) {
                            tabcontent[i].style.display = "none";
                        }
                        for (i = 0; i < tabIngestionSettings.length; i++) {
                            var currentItem = tabIngestionSettings[i];
                            if (currentItem === settingsName) {
                                $("#" + currentItem).show();
                                if(settingsName === 'ingestionZoneSettings') {
                                    setUIZoneValuesFromLocalStorage();
                                }
                            } else {
                                $("#" + currentItem).hide();
                                if(currentItem === 'ingestionZoneSettings') {
                                    saveUIZoneValuesToLocalStorage();
                                }
                            }
                        }
                    }
                    // Get all elements with class="tablinks" and remove the class "active"
                    tablinks = document.getElementsByClassName("tablinks");
                    for (i = 0; i < tablinks.length; i++) {
                        tablinks[i].className = tablinks[i].className.replace(" active", "");
                    }
                    evt.currentTarget.className += " active";
                }

                function disableUpload() {
                    reindexInProgress=true;
                    $('#startUpload').attr('disabled', 'disabled');
                    $('.fileinput-button').attr('disabled', 'disabled');
                    $("input[name='files[]']").attr('disabled', 'disabled');
                }

                function enableUpload() {
                    reindexInProgress=false;
                    $('#startUpload').removeAttr('disabled');
                    $('.fileinput-button').removeAttr('disabled');
                    $("input[name='files[]']").removeAttr('disabled', 'disabled');
                }

                function generateRequestGuidByTimestamp() {
                    requestGuid = new Date().getTime();
                    $('.progress', '.template-upload').show();
                    disableUpload();
                }

                function setUIZoneValuesFromLocalStorage() {
                    document.getElementById("addressZone").value = localStorage.getItem('zone_address');;
                    document.getElementById("headerZone").value = localStorage.getItem('zone_header');
                    document.getElementById("footerZone").value = localStorage.getItem('zone_footer');
                    document.getElementById("salutationZone").value = localStorage.getItem('zone_salutation');
                    document.getElementById("valedictionZone").value = localStorage.getItem('zone_valediction');
                    document.getElementById("paragraphZone").value = localStorage.getItem('zone_paragraph');
                }

                function saveUIZoneValuesToLocalStorage() {
                    localStorage.setItem('zone_address', document.getElementById("addressZone").value);
                    localStorage.setItem('zone_header', document.getElementById("headerZone").value);
                    localStorage.setItem('zone_footer', document.getElementById("footerZone").value);
                    localStorage.setItem('zone_salutation', document.getElementById("salutationZone").value);
                    localStorage.setItem('zone_valediction', document.getElementById("valedictionZone").value);
                    localStorage.setItem('zone_paragraph', document.getElementById("paragraphZone").value);
                }

                $(window).bind("load", function () {
                    $('.actionSelectMenuField').css('width', '470px').css('height', 'auto').css('background-color', '#bbb').css('border', '1px solid #bbb');
                    $('.actionSelectMenuText').css('width', '420px').css('color', 'black').css('font-size', '12px').css('line-height', "20px");
                    $('.actionInputMultiselectValue').css('height', '28px');
                    $('.actionSelectMenuField').hover(function(){
                        $(this).css("background-color", "#bbb");
                    }, function(){
                        $(this).css("background-color", "white");
                    });
                });
                $(window).unload(function() {
                    saveUIZoneValuesToLocalStorage();
                });
                $(window).on('keypress',function(e) {
                    if(e.which == 13) {
                        e.preventDefault();
                    }
                });

                $(function () {
                    toggleReindexInProgressIndicator();
                    setUIZoneValuesFromLocalStorage();
                    var data3 = new Array();
                    <c:forEach items="${variablesNotations}" var="currentItem" varStatus="loop">
                    data3['${currentItem.name}'] = ${currentItem.id};
                    </c:forEach>

                    if ($.browser.msie != true || parseInt($.browser.version) > 10)
                        $('#dragAndDropText').show();
                    if ($('#ingestionApp')[0].checked) {
                        showSettingsTabs();
                    } else{
                        hideSettingsTab();
                    }
                    var fileupload_submit = function (e, data) {
                        var hasValidationError = false;
                        if ($('#consolidateAllCheckbox')[0].checked) {
                            if (!($('#contentCharactersCheckbox')[0].checked) && (new String($('#generatedSharedContentPrefix').val()).trim().length == 0)) {
                                hasValidationError = true;
                                setTimeout(function () {
                                    $("#generatedSharedContentPrefix").popupFactory({
                                        title: client_messages.consolidate.automatically.sharedContent.naming.error.title,
                                        popupLocation: "right",
                                        trigger: "instant",
                                        width: 600,
                                        fnSetContent: function (o) {
                                            var popupEle = $("<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" + client_messages.consolidate.automatically.sharedContent.naming.error.message + "</div>");

                                            return popupEle;
                                        }
                                    });

                                }, 100);
                            }

                            var minWordCountText = $('#generatedSharedContentMinWordCount').val();
                            if (new String(minWordCountText).trim().length > 0) {
                                var minWordCountNumber = parseInt(minWordCountText);
                                if ( isNaN(minWordCountNumber) || minWordCountNumber < 1) {
                                    hasValidationError = true;
                                    setTimeout(function () {
                                        $("#generatedSharedContentMinWordCount").popupFactory({
                                            title: client_messages.consolidate.automatically.sharedContent.naming.error.title,
                                            popupLocation: "bottom",
                                            trigger: "instant",
                                            width: 400,
                                            fnSetContent: function (o) {
                                                var popupEle = $("<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" + client_messages.consolidate.automatically.minWordCount.error + "</div>");

                                                return popupEle;
                                            }
                                        });

                                    }, 100);
                                }
                            }
                        }
                        if (hasValidationError) {
                            enableUpload();
                        }
                        return !hasValidationError;
                    };

                    // Initialize the jQuery File Upload widget:
                    if ($('#ingestionApp')[0].checked) {
                        $('#fileupload').fileupload({
                            url: context + '/uploadFileHandler.form?rationalizerApplicationId=' + getParam('rationalizerApplicationId') + '&type=rationalizerDocuments&action=upload&tk=' + getParam('tk'),
                            acceptFileTypes: /(\.|\/)(zip)$/i,
                            sequentialUploads: true,
                            submit				: fileupload_submit
                        });
                    } else {
                        $('#fileupload').fileupload({
                            url: context + '/uploadFileHandler.form?rationalizerApplicationId=' + getParam('rationalizerApplicationId') + '&type=rationalizerDocuments&action=upload&tk=' + getParam('tk'),
                            acceptFileTypes: /(\.|\/)(xml|zip)$/i,
                            sequentialUploads: true,
                            submit				: fileupload_submit
                        });
                    }

                    $('#fileupload')
                        .bind('fileuploadsubmit', function (e, data) {
                            var inputs = data.context.find(':input');
                            if (inputs.filter('[required][value=""]').first().focus().length) {
                                return false;
                            }
                            data.formData = inputs.serializeArray();
                            data.formData[data.formData.length] = {name: "metatags", value: $('#metatagsInput').val()};

                            if ($('#consolidateAllCheckbox')[0].checked) {
                                data.formData[data.formData.length] = {name: "generateSharedContents", value: true};

                                data.formData[data.formData.length] = {
                                    name: "generatedSharedContentPrefix",
                                    value: $('#generatedSharedContentPrefix').val()
                                };

                                data.formData[data.formData.length] = {
                                    name: "includeContentCharsInGeneratedSharedContent",
                                    value: $('#contentCharactersCheckbox')[0].checked
                                };

                                var generatedSharedContentMinWordCount = $('#generatedSharedContentMinWordCount').val();
                                if (_.isEmpty(generatedSharedContentMinWordCount)) {
                                    generatedSharedContentMinWordCount = 0;
                                }
                                data.formData[data.formData.length] = {
                                    name: "generatedSharedContentMinWordCount",
                                    value: generatedSharedContentMinWordCount
                                };
                            } else {
                                data.formData[data.formData.length] = {name: "generateSharedContents", value: false};

                                data.formData[data.formData.length] = {
                                    name: "generatedSharedContentPrefix",
                                    value: ''
                                };

                                data.formData[data.formData.length] = {
                                    name: "includeContentCharsInGeneratedSharedContent",
                                    value: false
                                };

                                data.formData[data.formData.length] = {
                                    name: "generatedSharedContentMinWordCount",
                                    value: 0
                                };
                            }

                            data.formData[data.formData.length] = {name: "isIngestionUpload", value: $('#ingestionApp')[0].checked};
                            if ($('#ingestionApp')[0].checked) {

                                var radioValue = 1;
                                if ($('#ignore')[0].checked) {
                                    radioValue = 1;
                                }
                                if ($('#replace')[0].checked) {
                                    radioValue = 2;
                                }
                                if ($('#unique')[0].checked) {
                                    radioValue = 3;
                                }
                                data.formData[data.formData.length] = {name: "ingestNotFoundInManifest", value: $('#ingestNotFoundInManifest')[0].checked };
                                data.formData[data.formData.length] = {name: "styleTextIngest", value: $('#styleTextIngest')[0].checked };
                                data.formData[data.formData.length] = {name: "docxFieldPolicy", value: $('#docxFieldPolicyCheckbox')[0].checked };
                                data.formData[data.formData.length] = {name: "uploadFilePolicyValue", value: radioValue };

                                var variablesSelectionIds = 0;
                                var selectedValues = $('.actionInputMultiselectValue');
                                for (var i = 0; i < selectedValues.length; i++) {
                                    var idSel = data3[selectedValues[i].innerText];
                                    if (Number.isInteger(idSel)) {
                                        variablesSelectionIds |= 1 << idSel;
                                    }
                                }
                                var phoneCountriesFormatesSelectionIds ="";
                                var phoneValues =  $("div[id^='menuOption_phoneItemDefinitionOption_']");
                                for (var i = 0; i < phoneValues.length; i++) {
                                    if ($(phoneValues[i]).hasClass('actionOptionSelected')) {
                                        var selectedId = $(phoneValues[i]).attr("id").split("_")[2];
                                        phoneCountriesFormatesSelectionIds = phoneCountriesFormatesSelectionIds + selectedId + "_";
                                    }
                                }
                                phoneCountriesFormatesSelectionIds = phoneCountriesFormatesSelectionIds.substr(0,phoneCountriesFormatesSelectionIds.length - 1);

                                data.formData[data.formData.length] = {name: "variablesSelectionIds", value: variablesSelectionIds};
                                data.formData[data.formData.length] = {name: "allowSpaces", value: $($('#allowSpaces')[0]).is(':checked')};
                                data.formData[data.formData.length] = {name: "allowLowerCase", value: $($('#allowLowerCase')[0]).is(':checked')};
                                data.formData[data.formData.length] = {name: "maxVariableLength", value: $('#maxVariableLength').val()};
                                data.formData[data.formData.length] = {name: "heightScaleLargeFont", value: $('#heightScaleLargeFont').val()};
                                data.formData[data.formData.length] = {name: "heightScaleSmallFont", value: $('#heightScaleSmallFont').val()};
                                data.formData[data.formData.length] = {name: "widthScale", value: $('#widthScale').val()};
                                data.formData[data.formData.length] = {name: "yMul", value: $('#yMul').val()};
                                data.formData[data.formData.length] = {name: "ySpaceMul", value: $('#ySpaceMul').val()};
                                data.formData[data.formData.length] = {name: "useProcessorVersionV2", value: $('#v2Processor').hasClass('active')};

                                data.formData[data.formData.length] = {name: "phoneCountriesFormatesSelectionIds", value: phoneCountriesFormatesSelectionIds};
                                data.formData[data.formData.length] = {name: "addressZone", value: $('#addressZone').val()};
                                data.formData[data.formData.length] = {name: "headerZone", value: $('#headerZone').val()};
                                data.formData[data.formData.length] = {name: "footerZone", value: $('#footerZone').val()};
                                data.formData[data.formData.length] = {name: "salutationZone", value: $('#salutationZone').val()};
                                data.formData[data.formData.length] = {name: "valedictionZone", value: $('#valedictionZone').val()};
                                data.formData[data.formData.length] = {name: "paragraphZone", value: $('#paragraphZone').val()};
                            }
                            data.formData[data.formData.length] = {name: "requestGuid", value: requestGuid};
                            var noOfFilestoBeUploaded = totalUploadedNo - totalProcessedNo;
                            data.formData[data.formData.length] = {name: "noOfFilesToBeUploaded", value: noOfFilestoBeUploaded};
                        })
                        .bind('fileuploadadded', function (e, data) {
                            if (data.context != undefined) {
                                var nameInput = data.context.find("input.simpleName");
                                nameInput.val(nameInput.val().replace(/\.[^/.]+$/, ""));
                                nameInput.alphanumeric({simpleName: true});
                                $.alphanumeric.ref(nameInput).cleanPasteValue();
                                if ($.browser.msie == true && !(parseInt($.browser.version) > 10))
                                    data.context.find(".fileSize").remove();
                            }
                        })
                        .bind('fileuploadfinished', function (e, data) {
                            if ($('.template-upload,.template-download').length > 0) {
                                $('#initialInfo').hide();
                            } else {
                                if(!reindexInProgress) {
                                    displayInitialMessage();
                                }
                            }
                        })
                        .bind('fileuploadadded', function (e, data) {
                            if ($('.template-upload,.template-download').length > 0) {
                                $('#initialInfo').hide();
                                $('#metatagsContainer').showEle('normal');
                            } else {
                                displayInitialMessage();
                            }
                        });

                    // Load existing files:
                    $.ajax({
                        // Uncomment the following to send cross-domain cookies:
                        //xhrFields: {withCredentials: true},
                        url: context + '/uploadFileHandler.form?rationalizerApplicationId=' + getParam('rationalizerApplicationId') + '&type=rationalizerDocuments&action=load&tk=' + getParam('tk'),
                        dataType: 'json',
                        context: $('#fileupload')[0]
                    }).done(function (result) {

                        $(this).fileupload('option', 'done')
                            .call(this, null, {result: result});
                    });

                    // Initialize the Image Gallery widget:
                    $('#fileupload .files').imagegallery();
                    $('#v2Processor').click(function (e) {
                        $("#pdfProcessorCheckbox").prop('checked', true);
                        $('#v2Processor').addClass('active');
                        $('#v1Processor').removeClass('active');
                        $('th:nth-child(3), tr td:nth-child(3)', $('#pdfSettingsTable')).hide();
                        $('th:nth-child(4), tr td:nth-child(4)', $('#pdfSettingsTable')).hide();
                        $('th:nth-child(5), tr td:nth-child(5)', $('#pdfSettingsTable')).show();
                        $('th:nth-child(6), tr td:nth-child(6)', $('#pdfSettingsTable')).show();
                        $("#heightScaleSmallFont").css("display", "none");
                        $("#heightScaleLargeFont").css("display", "none");
                        $("#heightScaleLargeFontLabel").css("display", "none");
                        $("#heightScaleSmallFontLabel").css("display", "none");
                        $("#yMul").css("display", "block");
                        $("#ySpaceMul").css("display", "block");
                        $("#yMulLabel").css("display", "block");
                        $("#ySpaceMulLabel").css("display", "block");
                        e.preventDefault();
                        e.stopPropagation();

                    });
                    $('#v1Processor').click(function (e) {
                        $("#pdfProcessorCheckbox").prop('checked', false);
                        $('#v1Processor').addClass('active');
                        $('#v2Processor').removeClass('active');
                        $('th:nth-child(3), tr td:nth-child(3)', $('#pdfSettingsTable')).show();
                        $('th:nth-child(4), tr td:nth-child(4)', $('#pdfSettingsTable')).show();
                        $('th:nth-child(5), tr td:nth-child(5)', $('#pdfSettingsTable')).hide();
                        $('th:nth-child(6), tr td:nth-child(6)', $('#pdfSettingsTable')).hide();
                        $("#heightScaleSmallFont").css("display", "block");
                        $("#heightScaleLargeFont").css("display", "block");
                        $("#heightScaleLargeFontLabel").css("display", "block");
                        $("#heightScaleSmallFontLabel").css("display", "block");
                        $("#yMul").css("display", "none");
                        $("#ySpaceMul").css("display", "none");
                        $("#yMulLabel").css("display", "none");
                        $("#ySpaceMulLabel").css("display", "none");
                        e.preventDefault();
                        e.stopPropagation();
                     });


                    $('#ingestionApp').click(function() {
                        if( $(this).is(':checked')) {
                            $("#ingestionManifest").show();
                            $("#styleIngestion").show();
                            $("#docxFieldPolicy").show();
                            $("#ingestionPolicy").show();
                            showSettingsTabs();

                            if(first === true) {
                                if(!reindexInProgress) {
                                    showInitialIngestionMessage();
                                } else {
                                    showWaitingMessage();
                                }
                            } else {
                                if(!reindexInProgress) {
                                    if(totalUploadedNo==totalProcessedNo) {
                                        showCompleteMessage();
                                        $('#uploadLimitError').hide();
                                    }
                                } else {
                                    showWaitingMessage();
                                }
                            }
                            showUploadLog();

                        } else {
                            $("#ingestionManifest").hide();
                            $("#styleIngestion").hide();
                            $("#docxFieldPolicy").hide();
                            $("#ingestionPolicy").hide();
                            $('#uploadLimitError').hide();
                            hideSettingsTab();
                            if(first === true) {
                                $('#completeInfo').hide();
                                if(!reindexInProgress) {
                                    showInitialMessage();
                                } else {
                                    showWaitingMessage();
                                }
                            } else {
                                if(!reindexInProgress) {
                                    if(totalUploadedNo>=totalProcessedNo) {
                                        showCompleteMessage();
                                    }
                                } else {
                                    showWaitingMessage();
                                }
                            }
                            showUploadLog();
                        }
                    });

                    $("#variableNotationSelect").each(function () {
                        var currentSelect = $(this);
                        $(currentSelect).styleActionElement({
                            isAsync: true,
                            getItemsAsync: {
                                getItemsURL: context + "/getDataForMultiselectMenu.form",
                                fnExtServerParams: function (o) {
                                    return [
                                        {"name": "type", "value": "variablesNotations"},
                                        {
                                            "name": "rationalizerApplicationId",
                                            "value": getParam('rationalizerApplicationId')
                                        }
                                    ];
                                }
                            }
                        });
                    });

                    $("#phoneCountrySelect").each(function () {
                        var currentSelect = $(this);
                        $(currentSelect).styleActionElement({
                            isAsync: true,
                            getItemsAsync: {
                                getItemsURL: context + "/getDataForMultiselectMenu.form",
                                fnExtServerParams: function (o) {
                                    return [
                                        {"name": "type", "value": "phoneNumberSupportedCountries"},
                                        {
                                            "name": "rationalizerApplicationId",
                                            "value": getParam('rationalizerApplicationId')
                                        }
                                    ];
                                }
                            }
                        });
                    });


                    $('#consolidateAllYesLabel').click(function (e) {
                        $('#consolidateAllYesLabel').is(':visible')
                        {

                        $('#consolidateAllYesLabel').addClass('active');
                        $('#consolidateAllNoLabel').removeClass('active');
                        e.preventDefault();
                        e.stopPropagation();

                        enableConsolidateAllOptions(true);
                    }
                    });
                    $('#consolidateAllNoLabel').click(function (e) {
                        $('#consolidateAllYesLabel').removeClass('active');
                        $('#consolidateAllNoLabel').addClass('active');
                        e.preventDefault();
                        e.stopPropagation();

                        enableConsolidateAllOptions(false);
                    });

                    $('#contentCharactersYesLabel').click(function (e) {
                        $('#contentCharactersYesLabel').addClass('active');
                        $('#contentCharactersNoLabel').removeClass('active');
                        e.preventDefault();
                        e.stopPropagation();

                        $("#contentCharactersCheckbox").prop('checked', true);
                    });
                    $('#contentCharactersNoLabel').click(function (e) {
                        $('#contentCharactersYesLabel').removeClass('active');
                        $('#contentCharactersNoLabel').addClass('active');
                        e.preventDefault();
                        e.stopPropagation();

                        $("#contentCharactersCheckbox").prop('checked', false);
                    });

                });

                function enableConsolidateAllOptions(enable) {
                    $("#consolidateAllCheckbox").prop('checked', enable);
                    if (enable) {
                        $("#consolidationDescriptionTableRowId").css("display", "block");
                        $("#consolidationNotesTableRowId").css("display", "block");
                        $("#contentCharactersTableRowId").css("display", "block");
                        $("#minWordCountTableRowId").css("display", "block");
                        $("#consolidationPrefixTableRowId").css("display", "block");
                    } else {
                        $("#consolidationDescriptionTableRowId").css("display", "none");
                        $("#consolidationNotesTableRowId").css("display", "none");
                        $("#contentCharactersTableRowId").css("display", "none");
                        $("#minWordCountTableRowId").css("display", "none");
                        $("#consolidationPrefixTableRowId").css("display", "none");
                    }
                }
                function showSettingsTabs(){
                    $('#settingsTabs').show();
                    document.getElementById("consolidationTabButton").click();
                    if ( !common.isFeatureEnabled('RationalizerIngestionTuning') ) {
                        $('#pdfTabButton').hide();}
                    else {
                        $('#pdfTabButton').show();
                        $("#pdfProcessorCheckbox").prop('checked', true);
                        $('#v2Processor').addClass('active');
                        $('#v1Processor').removeClass('active');

                        $('th:nth-child(3), tr td:nth-child(3)', $('#pdfSettingsTable')).hide();
                        $('th:nth-child(4), tr td:nth-child(4)', $('#pdfSettingsTable')).hide();
                        $('th:nth-child(5), tr td:nth-child(5)', $('#pdfSettingsTable')).show();
                        $('th:nth-child(6), tr td:nth-child(6)', $('#pdfSettingsTable')).show();
                        $("#heightScaleSmallFont").css("display", "none");
                        $("#heightScaleLargeFont").css("display", "none");
                        $("#heightScaleLargeFontLabel").css("display", "none");
                        $("#heightScaleSmallFontLabel").css("display", "none");
                        $("#yMul").css("display", "block");
                        $("#ySpaceMul").css("display", "block");
                        $("#yMulLabel").css("display", "block");
                        $("#ySpaceMulLabel").css("display", "block");

                    }
                    $('#consolidationTabButton').show();
                    $('#variableTabButton').show();
                    $('#phoneTabButton').show();
                    $('#zoneTabButton').show();
                    document.getElementById("consolidationTabButton").click();

                }
                function hideSettingsTab(){
                    $('#settingsTabs').show();

                    $('#consolidationSettings').show();
                    $('#ingestionVariablesSettings').hide();
                    $('#ingestionPhoneSettings').hide();
                    $('#ingestionZoneSettings').hide();
                    $('#ingestionPdfSettings').hide();

                    $('#consolidationTabButton').show();
                    $('#variableTabButton').hide();
                    $('#phoneTabButton').hide();
                    $('#zoneTabButton').hide();
                    $('#pdfTabButton').hide();
                    document.getElementById("consolidationTabButton").click();
                }

                function showInitialIngestionMessage(){
                    $('#initialMessage').hide();
                    $('#initialIngestionMessage').show();
                    $('#completeInfo').hide();
                    $('#waitingInfo').hide();
                    $('#uploadLimitError').hide();
                }

                function showInitialMessage(){
                    $('#initialMessage').show();
                    $('#initialIngestionMessage').hide();
                    $('#completeInfo').hide();
                    $('#waitingInfo').hide();
                    $('#uploadLimitError').hide();
                }

                function showWaitingMessage(){
                    $('#initialInfo').hide();
                    $('#completeInfo').hide();
                    if(totalWithUploadLimitError>=1){
                        $('#uploadLimitError').show();
                        $('#waitingInfo').hide();
                    } else {
                        $('#waitingInfo').show();
                        $('#uploadLimitError').hide();
                    }
                }
                function showCompleteMessage(){
                    $('#initialInfo').hide();
                    $('#completeInfo').show();
                    $('#waitingInfo').hide();
                    $('#uploadLimitError').hide();
                }

                function showUploadLog(){
                    if(showLog) {
                        $('#applicationLog').show();
                    } else {
                        $('#applicationLog').hide();
                    }
                }
                function displayInitialMessage() {
                    $('#waitingInfo').hide();
                    $('#completeInfo').hide();
                    $('#initialInfo').show();
                    if ($('#ingestionApp')[0].checked) {
                        $('#initialMessage').hide();
                        $('#initialIngestionMessage').show();
                    } else {
                        $('#initialMessage').show();
                        $('#initialIngestionMessage').hide();
                    }
                }


                function toggleReindexInProgressIndicator() {
                    var rationalizerApplicationId = getParam('rationalizerApplicationId') != null && getParam('rationalizerApplicationId') != "" ?
                        getParam('rationalizerApplicationId') : _rationalizerApplicationId;

                    function processReindexStatusResult(data) {
                        if(data.reindexInProgress == true) {
                            disableUpload();
                            showWaitingMessage();
                        } else {
                            if (first === true) {
                                if(totalUploadedNo == 0) {
                                    displayInitialMessage();
                                    enableUpload();
                                }
                            }else {
                                if(totalUploadedNo>=totalProcessedNo) {
                                    showCompleteMessage();
                                    $('#uploadLimitError').hide();
                                    enableUpload();
                                }
                            }
                        }
                        showUploadLog();
                    }

                    function pollForReindexStatus() {
                        var stampDate = new Date();
                        $.ajax({
                            type: "GET",
                            url: context + "/getObjectInfo.form?type=rationalizerReindexingInProgress&rationalizerApplicationId=" + rationalizerApplicationId +
                                "&cacheStamp=" + (stampDate.getTime()),
                            dataType: "json",
                            success: function(data) {
                                try {
                                    processReindexStatusResult(data);
                                } catch (e) {
                                    console.log(e);
                                }
                                window.setTimeout( function(){ pollForReindexStatus(); }, 20000);
                            }
                        });
                    }

                    pollForReindexStatus();
                }
            </script>
        </msgpt:Script>

    </msgpt:HeaderNew>

    <msgpt:BodyNew theme="minimal" type="iframe" cssClass="fileUploadBody">

        <div class="container" style="padding-bottom: 50px;">
            <!-- The file upload form used as target for the file upload widget -->
            <form id="fileupload" name="fileupload" method="POST" enctype="multipart/form-data">

                <!-- The fileupload-buttonbar contains buttons to add/delete files and start/cancel the upload -->
                <div class="fileupload-buttonbar">

                    <!-- ACTION BAR: Section/Zone toggles -->
                    <table class="actionsBarContainer" width="100%" cellspacing="0" cellpadding="0" border="0"
                           style="border-radius: 6px 6px 0 0; background-color: white">
                        <tr>
                            <td colspan="1">
                                <div style="color: black; padding-left:0.625rem;" >
                                    <label  style="display: flex; align-items: center;color: black;">
                                        <input type="checkbox" id="ingestionApp"  name="isIngestionApplication" style="flex:none; margin-right:10px;"/>
                                        <fmtSpring:message code="page.label.ingestion"/>
                                    </label>
                                </div>
                            </td>
                            <td colspan="1">
                                <div id ="applicationLog" style="color: black; padding-left:0.625rem; display:none">
                                    <label class="labelText"><fmtSpring:message code="page.label.ingestion.log"/></label>
                                    <i id="applicationLogIcon" class="far fa-download" style="padding-left:0.625rem; font-size: small" aria-hidden=true  title="${msgpt:getMessage('page.label.application.download.log')}" onclick="javascript:javascriptHref('${applicationLog}')"></i>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" style="vertical-align: middle; padding-left: 10px;">
                                <button id="uploadAddFiles" class="btn btn-light fileinput-button btn-sm" style="margin-top:0px;">
                                    <i class="far fa-plus fa-sm mr-1"></i>
                                    <span><c:out value='${msgpt:getMessage("page.label.add.files")}'/></span>
                                    <input id="uploadAddFilesInput" type="file" name="files[]" multiple>
                                </button>
                                <button id="startUpload" type="submit" style="margin-right:5px" class="btn btn-light btn-sm start"  ${reindexInProgress ? 'disabled="disabled"' : ''}  onclick="generateRequestGuidByTimestamp()">
                                    <i class="far fa-upload fa-sm mr-1"></i>
                                    <span><c:out value='${msgpt:getMessage("page.label.start.upload")}'/></span>
                                </button>
                                <button type="reset" class="btn btn-light btn-sm cancel">
                                    <i class="far fa-ban fa-sm mr-1"></i>
                                    <span><c:out value='${msgpt:getMessage("page.label.cancel.upload")}'/></span>
                                </button>
                            </td>
                            <td>
                                <!-- The global progress information -->
                                <div class="fileupload-progress fade" style="padding-right: 5px;">
                                    <!-- The global progress bar -->
                                    <div class="progress progress-success progress-striped active" role="progressbar"
                                         aria-valuemin="0" aria-valuemax="100">
                                        <div class="bar" style="width:0%;"></div>
                                    </div>
                                    <!-- The extended global progress information -->
                                    <div class="progress-extended text-white">&nbsp;</div>
                                </div>
                            </td>
                        </tr>
                        <tr id="styleIngestion" style="display: none">
                            <td colspan="2">
                                <div style="color: black; padding-left:0.625rem;">
                                    <label  style="display: flex; align-items: center;color: black; height:20px;">
                                        <input type="checkbox" id="styleTextIngest"  name="styleTextIngest" style="flex:none; margin-right:10px;" checked/>
                                        <fmtSpring:message code="page.label.upload.ingest.styleText"/>
                                    </label>
                                </div>
                            </td>

                        </tr>
                        <tr id="ingestionManifest" style="display: none">
                            <td colspan="2">
                                <div style="color: black; padding-left:0.625rem;">
                                    <label  style="display: flex; align-items: center;color: black; height:10px;">
                                        <input type="checkbox" id="ingestNotFoundInManifest"  name="ingestIfNotFoundInManifest" style="flex:none; margin-right:10px;" checked/>
                                        <fmtSpring:message code="page.label.upload.ingest.manifest"/>
                                    </label>
                                </div>
                            </td>

                        </tr>
                        <tr id="docxFieldPolicy" style="display: none">
                            <td colspan="2">
                                <div style="color: black; padding-left:0.625rem;">
                                    <label  style="display: flex; align-items: center;color: black; height:10px;">
                                        <input type="checkbox" id="docxFieldPolicyCheckbox"  name="docxFieldPolicyCheckbox" style="flex:none; margin-right:10px;" checked/>
                                        <fmtSpring:message code="page.label.upload.ingest.docx.field.policy"/>
                                    </label>
                                </div>
                            </td>

                        </tr>
                        <tr id="ingestionPolicy" style="display: none">
                            <td colspan="2">
                                <label class="labelText" style="color: black; padding-left:0.625rem;"
                                       for="ingestNotFoundInManifest">
                                    <fmtSpring:message code="page.label.upload.policy"/>&nbsp;
                                </label>

                                <div class="btn-group btn-group-toggle" data-toggle="buttons">
                                    <label class="btn btn-light btn-sm nav-item active">
                                        <input type="radio" name="options" id="ignore" autocomplete="off" checked>
                                        <fmtSpring:message code="page.label.upload.ignore"/>
                                    </label>
                                    <label class="btn btn-light btn-sm nav-item">
                                        <input type="radio" name="options" id="replace" autocomplete="off">
                                        <fmtSpring:message code="page.label.upload.remove.and.replace"/>
                                    </label>
                                    <label class="btn btn-light btn-sm nav-item">
                                        <input type="radio" name="options" id="unique" autocomplete="off">
                                        <fmtSpring:message code="page.label.upload.make.unique"/>
                                    </label>
                                </div>
                            </td>
                        </tr>
                        <tr id="settingsTabs" >
                            <td colspan="2">
                                <div class="d-flex ml-auto" role="group" style="margin-top: 20px">
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary active tablinks" type="button" onclick="openSettingsTab(event, 'consolidationSettings');" id="consolidationTabButton">
                                            <fmtSpring:message code="page.label.ingestion.upload.consolidation.settings"/>
                                        </button>
                                        <button class="btn btn-outline-primary tablinks" type="button" onclick="openSettingsTab(event, 'ingestionVariablesSettings');" id="variableTabButton" style="margin-left: 3px"; >
                                            <fmtSpring:message code="page.label.ingestion.upload.variable.settings"/>
                                        </button>
                                        <button class="btn btn-outline-primary tablinks" type="button" onclick="openSettingsTab(event, 'ingestionPhoneSettings');" id="phoneTabButton" style="margin-left: 3px";>
                                            <fmtSpring:message code="page.label.ingestion.upload.phone.settings"/>
                                        </button>
                                        <button class="btn btn-outline-primary tablinks" type="button" onclick="openSettingsTab(event, 'ingestionZoneSettings');" id="zoneTabButton" style="margin-left: 3px";>
                                            <fmtSpring:message code="page.label.ingestion.upload.zone.settings"/>
                                        </button>
                                        <button  class="btn btn-outline-primary tablinks" type="button" onclick="openSettingsTab(event, 'ingestionPdfSettings');" id="pdfTabButton" style="margin-left: 3px";>
                                            <fmtSpring:message code="page.label.ingestion.upload.pdf.settings"/>
                                        </button>
                                    </div>
                                </div>
                                <div id="consolidationSettings" style="display: none" class="tabcontent">
                                    <table id="consolidateAutomaticallyTable" style="width: 100%; border: 1px solid #cfcfcf; margin-bottom: 10px;">
                                       <tr>
                                          <td colspan="3">
                                              <div class="btn-group btn-group-toggle" data-toggle="buttons" style="margin-top: 0.3rem;padding-left:0.625rem">
                                                  <input type="checkbox" name="consolidateAllCheckbox" value="yes" id="consolidateAllCheckbox" autocomplete="off" style="display: none" />
                                                        <label id="consolidateAllYesLabel" class="btn btn-light btn-sm nav-item">
                                                            <fmtSpring:message code="page.label.yes"/>
                                                        </label>
                                                        <label id="consolidateAllNoLabel"  class="btn btn-light btn-sm nav-item active">
                                                           <fmtSpring:message code="page.label.no"/>
                                                            </label>
                                              </div>
                                              <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem">
                                                  <fmtSpring:message code="page.label.consolidate.automatically.consolidate.all"/>
                                              </label>
                                          </td>
                                       </tr>
                                       <tr id="minWordCountTableRowId" style="display: none;">
                                          <td colspan="3">
                                             <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem">
                                               <fmtSpring:message code="page.label.consolidate.automatically.minWordCount.prefix"/>&nbsp;
                                             </label>
                                             <msgpt:InputFilter type="numeric">
                                                  <input type="text" id="generatedSharedContentMinWordCount" name="generatedSharedContentMinWordCount" maxlength="2" size="2"/>
                                             </msgpt:InputFilter>
                                             <fmtSpring:message code="page.label.consolidate.automatically.minWordCount.suffix"/>&nbsp;

                                          </td>
                                       </tr>
                                       <tr id="consolidationDescriptionTableRowId" style="display: none;">
                                          <td colspan="3">
                                               <fmtSpring:message code="page.text.consolidate.automatically.description"/>&nbsp;
                                          </td>
                                       </tr>
                                       <tr id="consolidationPrefixTableRowId" style="display: none;">
                                          <td colspan="3">
                                             <msgpt:InputFilter type="rationalizerSharedContentName">
                                                <input type="text" id="generatedSharedContentPrefix" name="generatedSharedContentPrefix" maxlength="8" size="8"/>
                                             </msgpt:InputFilter>

                                             <fmtSpring:message code="page.label.consolidate.automatically.prefix.description"/>&nbsp;
                                          </td>
                                       </tr>
                                       <tr id="contentCharactersTableRowId" style="display: none;">
                                         <td colspan="3">
                                           <div class="btn-group btn-group-toggle" data-toggle="buttons" style="margin-top: 0.3rem;padding-left:0.625rem">
                                              <input type="checkbox" name="contentCharactersCheckbox" value="yes" id="contentCharactersCheckbox" autocomplete="off" style="display: none" checked />
                                                <label id="contentCharactersYesLabel" class="btn btn-light btn-sm nav-item active">
                                                    <fmtSpring:message code="page.label.yes"/>
                                                </label>
                                                <label id="contentCharactersNoLabel" class="btn btn-light btn-sm nav-item">
                                                    <fmtSpring:message code="page.label.no"/>
                                                </label>
                                           </div>
                                           <fmtSpring:message code="page.label.consolidate.automatically.content.characters.description"/>&nbsp;
                                         </td>
                                       </tr>
                                       <tr id="consolidationNotesTableRowId" style="display: none;">
                                          <td colspan="3">
                                              <br/>
                                              <fmtSpring:message code="page.text.consolidate.automatically.notes"/>&nbsp;
                                          </td>
                                       </tr>
                                   </table>
                                </div>
                                <div id="ingestionVariablesSettings" style="display: none" class="tabcontent">
                                    <table id="variablesSettingsTable"  style="width: 100%; border: 1px solid #cfcfcf; margin-bottom: 10px;">
                                        <tr>
                                            <td colspan="3">
                                                <table>
                                                    <tr>
                                                        <td style="width:33%; padding-top: 6px; padding-bottom: 0px;">
                                                            <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem">
                                                                <fmtSpring:message code="page.label.ingestion.upload.allowSpaces"/>
                                                            </label>
                                                        </td>
                                                        <td style="width:33%; padding-top: 6px; padding-bottom: 0px;">
                                                            <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem">
                                                                <fmtSpring:message code="page.label.ingestion.upload.allowLowerCase"/>
                                                            </label>
                                                        </td>
                                                        <td style="width:33%; padding-top: 6px; padding-bottom: 0px;">
                                                            <label class="labelText" style="color: black; margin-bottom: 0.1rem">
                                                                <fmtSpring:message code="page.label.ingestion.upload.maxVariableLength"/>
                                                            </label>
                                                        </td>
                                                    </tr>
                                                    <tr>

                                                        <td style="width: 130px; padding-top: 0px; padding-bottom: 0px;">
                                                            <div>
                                                                <div class="btn-group btn-group-toggle" data-toggle="buttons"
                                                                     style="margin-top: 0.3rem;padding-left:0.625rem;">
                                                                    <label class="btn btn-light btn-sm nav-item active">
                                                                        <input type="radio" name="optionsSpaces" id="allowSpaces" autocomplete="off" checked>
                                                                        <fmtSpring:message code="page.label.yes"/>
                                                                    </label>
                                                                    <label class="btn btn-light btn-sm nav-item">
                                                                        <input type="radio" name="optionsSpaces" id="noSpaces" autocomplete="off">
                                                                        <fmtSpring:message code="page.label.no"/>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td style="width: 130px; padding-top: 0px; padding-bottom: 0px;">
                                                            <div>
                                                                <div class="btn-group btn-group-toggle" data-toggle="buttons"
                                                                     style="margin-top: 0.3rem;padding-left:0.625rem;">
                                                                    <label class="btn btn-light btn-sm nav-item active">
                                                                        <input type="radio" name="optionsLower" id="allowLowerCase" autocomplete="off" checked>
                                                                        <fmtSpring:message code="page.label.yes"/>
                                                                    </label>
                                                                    <label class="btn btn-light btn-sm nav-item">
                                                                        <input type="radio" name="optionsLower" id="noLowerCase" autocomplete="off">
                                                                        <fmtSpring:message code="page.label.no"/>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td style=" padding-top: 0px; padding-bottom: 0px;">
                                                            <div>
                                                                <input style="margin-top: 0.3rem; padding-left:0.3rem; padding-top: 0.3rem; padding-right:0rem; padding-bottom:0.3rem; width: 92px; height:20px"
                                                                       type="number" min="1" max="500" step="1"
                                                                       id="maxVariableLength" value="100" class="form-control">
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="1">
                                                <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.variable.notation"/>
                                                </label>
                                            </td>

                                            <td colspan="2">
                                                <div id="variableNotationSelect" class="style_multiselect inputL"
                                                     style="display: none; padding-left:0.625rem;" width=80%>
                                                    <c:forEach var="currentItem" items="${variablesNotations}">
                                                        <div id="itemDefinitionOption_${currentItem.id}">
                                                            <input id="checkbox_itemDefinition_${currentItem.id}"
                                                                   name="variablesNotations"
                                                                   value="${currentItem.id}"
                                                                   type="checkbox"
                                                                   class="style_multiselect_binding maintainValue"/>
                                                            <input type="hidden" name="_variablesNotations"/>
                                                            <span><c:out value="${currentItem.name}"/></span>
                                                        </div>
                                                    </c:forEach>
                                                    <script class="optionTemplate" type="text/x-handlebars-template">
                                                        <div id="itemDefinitionOption_{{optionId}}">
                                                            <input id="checkbox_itemDefinition_{{optionId}}"
                                                                   name="variablesNotations"
                                                                   value="{{optionId}}"
                                                                   type="checkbox"
                                                                   class="style_multiselect_binding maintainValue"
                                                                   {{selected}}/>
                                                            <input type="hidden" name="_variablesNotations"/>
                                                            <span>{{optionName}}</span>
                                                        </div>
                                                    </script>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div id="ingestionPhoneSettings" style="display: none" class="tabcontent">
                                    <table id="phoneSettingsTable"style="width: 100%; border: 1px solid #cfcfcf; margin-bottom: 10px;">
                                        <tr>
                                            <td style="width:18%; padding-top: 20px; padding-bottom: 20px;">
                                                <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.phone.notation"/>
                                                </label>
                                            </td>
                                            <td style="width:66%; padding-top: 20px; padding-bottom: 10px; padding-left: 13px;">
                                                <div id="phoneCountrySelect" class="style_multiselect inputL"
                                                     style="display: none; padding-left:0.625rem;padding-right:0.625rem; padding-top:20px" width=80%>
                                                    <c:forEach var="currentItem" items="${phoneNumberSupportedCountries}">
                                                        <div id="phoneItemDefinitionOption_${currentItem.id}">
                                                            <input id="checkbox_phoneItemDefinition_${currentItem.id}"
                                                                   name="phoneNumberSupportedCountries"
                                                                   value="${currentItem.id}"
                                                                   type="checkbox"
                                                                   class="style_multiselect_binding maintainValue"/>
                                                            <input type="hidden" name="_phoneNumberSupportedCountries"/>
                                                            <span><fmtSpring:message code="${currentItem.displayName}"/></span>
                                                        </div>
                                                    </c:forEach>
                                                    <script class="optionTemplate" type="text/x-handlebars-template">
                                                        <div id="phoneItemDefinitionOption_{{optionId}}">
                                                            <input id="checkbox_phoneItemDefinition_{{optionId}}"
                                                                   name="phoneNumberSupportedCountries"
                                                                   value="{{optionId}}"
                                                                   type="checkbox"
                                                                   class="style_multiselect_binding maintainValue"
                                                                   {{selected}}/>
                                                            <input type="hidden" name="_phoneNumberSupportedCountries"/>
                                                            <span>{{optionName}}</span>
                                                        </div>
                                                    </script>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div id="ingestionZoneSettings" class="tabcontent" style="width: 100%; border: 1px solid #cfcfcf; margin-bottom: 10px; display:none">
                                    <table id="zoneSettingsTable" style="width: 98%; border: 1px solid #cfcfcf; margin-bottom: 10px; margin-top: 20px; margin-left: 10px;">
                                        <tr>
                                            <th style=" padding-top: 5px; padding-bottom: 5px; background-color: lightgrey; border: 1px solid;">
                                                <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.zone.for"/>
                                                </label>
                                            </th>
                                            <th style="padding-top: 5px; padding-bottom: 5px; background-color: lightgrey;border: 1px solid;">
                                                <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.zone.value"/>
                                                </label>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td style="padding-top: 5px; padding-bottom: 5px; border: 1px solid;">
                                                <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.zone.address"/>
                                                </label>
                                            </td>
                                            <td style="padding-top: 5px; padding-left: 10px; padding-bottom: 5px; border: 1px solid;">
                                                <msgpt:InputFilter type="zoneConnectorValue">
                                                    <input type="text" id="addressZone" name="addressZone" class="style_input input250" maxlength="96" />
                                                </msgpt:InputFilter>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding-top: 5px; padding-bottom: 5px; border: 1px solid;">
                                                <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.zone.header"/>
                                                </label>
                                            </td>
                                            <td style="padding-top: 5px; padding-left: 10px; padding-bottom: 5px; border: 1px solid;">
                                                <msgpt:InputFilter type="zoneConnectorValue">
                                                    <input type="text" id="headerZone" name="headerZone" class="style_input input250" maxlength="96" />
                                                </msgpt:InputFilter>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding-top: 5px; padding-bottom: 5px; border: 1px solid;">
                                                <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.zone.footer"/>
                                                </label>
                                            </td>
                                            <td style="padding-top: 5px; padding-left: 10px; padding-bottom: 5px; border: 1px solid;">
                                                <msgpt:InputFilter type="zoneConnectorValue">
                                                    <input type="text" id="footerZone" name="footerZone" class="style_input input250"  maxlength="96"/>
                                                </msgpt:InputFilter>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding-top: 5px; padding-bottom: 5px; border: 1px solid;">
                                                <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.zone.salutation"/>
                                                </label>
                                            </td>
                                            <td style="padding-top: 5px; padding-left: 10px; padding-bottom: 5px; border: 1px solid;">
                                                <msgpt:InputFilter type="zoneConnectorValue">
                                                    <input type="text" id="salutationZone" name="salutationZone" class="style_input input250" maxlength="96" />
                                                </msgpt:InputFilter>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding-top: 5px; padding-bottom: 5px; border: 1px solid;">
                                                <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.zone.valediction"/>
                                                </label>
                                            </td>
                                            <td style="padding-top: 5px; padding-left: 10px; padding-bottom: 5px; border: 1px solid;">
                                                <msgpt:InputFilter type="zoneConnectorValue">
                                                    <input type="text" id="valedictionZone" name="valedictionZone" class="style_input input250" maxlength="96" />
                                                </msgpt:InputFilter>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding-top: 5px; padding-bottom: 5px; border: 1px solid;">
                                                <label class="labelText" style="color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.zone.paragraph"/>
                                                </label>
                                            </td>
                                            <td style="padding-top: 5px;padding-left: 10px;  padding-bottom: 5px; border: 1px solid;">
                                                <msgpt:InputFilter type="zoneConnectorValue">
                                                    <input type="text" id="paragraphZone" name="paragraphZone" class="style_input input250" maxlength="96"/>
                                                </msgpt:InputFilter>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div id="ingestionPdfSettings" class="tabcontent"  style="width: 100%; border: 1px solid #cfcfcf; margin-bottom: 10px; display:none">
                                    <table id="pdfSettingsTable" style="width: 100%;  margin-bottom: 10px;">
                                        <tr>
                                            <td width:25%; padding-top: 6px; padding-bottom: 0px;>
                                                <label class="labelText" style="color: black; padding-left:0.1rem; margin-bottom: 0.1rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.pdf.settings.processor.version"/>
                                                </label>
                                            </td>
                                            <td style="width:25%; padding-top: 6px; padding-bottom: 0px;">
                                                <label class="labelText" style="color: black; padding-left:0.1rem; margin-bottom: 0.1rem;">
                                                    <fmtSpring:message code="page.label.ingestion.upload.pdf.settings.width.scale"/>
                                                </label>
                                            </td>
                                            <td style="width:25%; padding-top: 6px; padding-bottom: 0px;">
                                                <label id="heightScaleLargeFontLabel" class="labelText" style="color: black; padding-left:0.1rem; margin-bottom: 0.1rem; display: none">
                                                    <fmtSpring:message code="page.label.ingestion.upload.pdf.settings.height.large.font"/>
                                                </label>
                                            </td>
                                            <td style="width:25%; padding-top: 6px; padding-bottom: 0px;">
                                                <label id="heightScaleSmallFontLabel" class="labelText" style="color: black; padding-left:0.1rem; margin-bottom: 0.1rem; display: none">
                                                    <fmtSpring:message code="page.label.ingestion.upload.pdf.settings.height.small.font"/>
                                                </label>
                                            </td>
                                            <td style="width:25%; padding-top: 6px; padding-bottom: 0px;">
                                                <label id="yMulLabel" class="labelText" style="color: black; padding-left:0.1rem; margin-bottom: 0.1rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.pdf.settings.yMul"/>
                                                </label>
                                            </td>
                                            <td style="width:25%; padding-top: 6px; padding-bottom: 0px;">
                                                <label id="ySpaceMulLabel" class="labelText" style="color: black; padding-left:0.1rem; margin-bottom: 0.1rem">
                                                    <fmtSpring:message code="page.label.ingestion.upload.pdf.settings.ySpaceMul"/>
                                                </label>
                                            </td>

                                        </tr>
                                        <tr>
                                            <td style=" padding-top: 0px; padding-bottom: 10px; padding-left:0.625rem;">

                                                <div>
                                                    <div class="btn-group btn-group-toggle" data-toggle="buttons"
                                                         style="margin-top: 0.3rem;padding-left:0.625rem;">
                                                        <input type="checkbox" name="pdfProcessorCheckbox" value="v2"
                                                               id="pdfProcessorCheckbox" autocomplete="off"
                                                               style="display: none"/>
                                                        <label id="v1Processor" class="btn btn-light btn-sm nav-item "
                                                               autocomplete="off">
                                                            <fmtSpring:message code="page.label.v1"/>
                                                        </label>
                                                        <label id="v2Processor"
                                                               class="btn btn-light btn-sm nav-item active"
                                                               autocomplete="off" checked>
                                                            <fmtSpring:message code="page.label.v2"/>
                                                        </label>
                                                    </div>
                                                </div>

                                            </td>
                                            <td style=" padding-top: 0px; padding-bottom: 10px; padding-left:0.625rem;">
                                                <input style="margin-top: 0.3rem; padding-left:0.3rem; padding-top: 0.3rem; padding-right:0rem; padding-bottom:0.3rem; width: 92px; height:20px;"
                                                       type="number" min="0" max="20" step="0.1"
                                                       id="widthScale" value="7" class="form-control">
                                            </td>
                                            <td style=" padding-top: 0px; padding-bottom: 10px; padding-left:0.625rem;">
                                                <input style="margin-top: 0.3rem; padding-left:0.3rem; padding-top: 0.3rem; padding-right:0rem; padding-bottom:0.3rem; width: 92px; height:20px; display: none"
                                                       type="number" min="0" max="10" step="0.01"
                                                       id="heightScaleLargeFont" value="1.01" class="form-control">

                                            </td>

                                            <td style=" padding-top: 0px; padding-bottom: 10px; padding-left:0.625rem;">
                                                <input style="margin-top: 0.3rem; padding-left:0.3rem; padding-top: 0.3rem; padding-right:0rem; padding-bottom:0.3rem; width: 92px; height:20px; display: none"
                                                       type="number" min="0" max="10" step="0.01"
                                                       id="heightScaleSmallFont" value="0.75" class="form-control">

                                            </td>
                                            <td style=" padding-top: 0px; padding-bottom: 10px; padding-left:0.625rem;">
                                                <input style="margin-top: 0.3rem; padding-left:0.3rem; padding-top: 0.3rem; padding-right:0rem; padding-bottom:0.3rem; width: 92px; height:20px"
                                                       type="number" min="0.05" max="10" step="0.01"
                                                       id="yMul" value="1.75" class="form-control">

                                            </td>

                                            <td style=" padding-top: 0px; padding-bottom: 10px; padding-left:0.625rem;">
                                                <input style="margin-top: 0.3rem; padding-left:0.3rem; padding-top: 0.3rem; padding-right:0rem; padding-bottom:0.3rem; width: 92px; height:20px"
                                                       type="number" min="0.05" max="10" step="0.01"
                                                       id="ySpaceMul" value="0.25" class="form-control">

                                            </td>
                                        </tr>
                                    </table>
                                </div>

                            </td></tr>
                    </table>
                </div>
                <!-- The loading indicator is shown during file processing -->
                <div class="fileupload-loading"></div>

                <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
                    <c:set var="metatagsViewPermission" value="true"/>
                </msgpt:IfAuthGranted>
                <table width="100%" cellspacing="0" cellpadding="0" border="0"
                       style="border-left: 1px solid #bbb; border-right: 1px solid #bbb; border-top: 1px solid #bbb;">
                    <tr id="metatagsContainer" style="display: none;">
                        <c:if test="${metatagsViewPermission}">
                            <td width="1%">
                                <div class="fullLineLabel" style="padding: 6px 8px;"><c:out
                                        value='${msgpt:getMessage("page.label.METATAGS")}'/></div>
                            </td>
                            <td align="left">
                                <msgpt:InputFilter type="simpleName">
                                    <input id="metatagsInput" name="metatags" class="inputXXL">
                                </msgpt:InputFilter>
                            </td>
                        </c:if>
                        <c:if test="${not metatagsViewPermission}">
                        <td width="99%">
                            </c:if>
                    </tr>
                    <tr id="initialInfo" style="display: none;">
                        <td colspan="2">
                            <div id="initialMessage" class="InfoSysContainer_info" style="margin: 0px 10px 0px 10px;" align="left; display: none; ">
                                <c:out value='${msgpt:getMessage("page.text.upload.files")}'/><br/><br/>
                                <c:out value='${msgpt:getMessage("page.text.upload.files.click.or.drag")}'/>
                            </div>
                            <div id="initialIngestionMessage" class="InfoSysContainer_info" style="margin: 0px 10px 0px 10px;" align="left; display: none; ">
                                <c:out value='${msgpt:getMessage("page.text.upload.files.ingestion")}'/><br/><br/>
                                <c:out value='${msgpt:getMessage("page.text.upload.files.click.or.drag")}'/>
                            </div>
                        </td>
                    </tr>
                    <tr id="completeInfo" style="display: none;">
                        <td colspan="2">
                            <div class="InfoSysContainer_info" style="margin: 0px 10px 0px 10px;" align="left">
                                <c:out value='${msgpt:getMessage("page.label.upload.complete")}'/>
                            </div>
                        </td>
                    </tr>
                    <tr id="waitingInfo" style="display: none;">
                        <td colspan="2">
                            <div class="InfoSysContainer_info" style="margin: 0px 10px 0px 10px; background-color: #fffccc" align="left">
                                <c:out value='${msgpt:getMessage("page.label.upload.complete.wait.for.indexing")}'/>
                            </div>
                        </td>
                    </tr>
                    <tr id="uploadLimitError" style="display: none;">
                        <td colspan="2">
                            <div class="InfoSysContainer_info" style="margin: 0px 10px 0px 10px; background-color: #fffccc" align="left">
                                                          <span style="color: red; font-weight: bold; ">
                                   <c:out  value='${msgpt:getMessage("client_messages.rationalizer.upload.limit.error.title")}'/>
                                   </span><br/>
                                <span style="color: red; "><c:out  value='${uploadLimitError}'/></span>
                            </div>
                        </td>
                    </tr>
                </table>
                <div class="sandboxFileContainer">

                    <!-- The table listing the files available for upload/download -->
                    <table role="presentation" class="table table-striped" width="100%">
                        <tbody class="files" data-toggle="modal-gallery" data-target="#modal-gallery"></tbody>
                    </table>
                </div>
            </form>
        </div>

        <!-- The template to display files available for upload -->
        <script id="template-upload" type="text/x-tmpl">
                {%  for (var i=0, file; file=o.files[i]; i++) {
                     totalUploadedNo++; %}
                    <tr class="template-upload fade">
                        <td class="preview"><span class="fade"></span></td>
                        <td class="name">
                            <msgpt:InputFilter type="simpleName">
            <input name="documentNames[]" class="input250" value="{%=file.name%}" required>
        </msgpt:InputFilter>
                            <div class="fileSize">{%=o.formatFileSize(file.size)%} | {%=file.type%}</div>
                        </td>
                        {% if (file.error) { %}
                            <td width="1%" class="error" colspan="2"><span class="label label-important"><c:out
                value='${msgpt:getMessage("page.text.error")}'/></span> {%=file.error%}</td>
                        {% } else if (o.files.valid && !i) { %}
                            <td width="1%">
                                <div class="progress progress-success progress-striped active" style="display:none" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0"><div class="bar" style="width:0%;"></div></div>
                            </td>
                            <td width="1%" class="start">{% if (!o.options.autoUpload) { %}
                                <button class="btn btn-success btn-sm" style="display:none">
                                    <i class="far fa-upload fa-sm mr-1"></i>
                                    <span><c:out value='${msgpt:getMessage("page.label.start")}'/></span>
                                </button>
                            {% } %}</td>
                        {% } else { %}
                            <td width="1%" colspan="2"></td>
                        {% } %}
                        <td width="1%" class="cancel">{% if (!i) { %}
                            <button class="btn btn-warning btn-sm" style="display:none">
                                <i class="far fa-ban fa-sm mr-1"></i>
                                <span><c:out value='${msgpt:getMessage("page.label.cancel")}'/></span>
                            </button>
                        {% } %}</td>
                    </tr>
                             $('#metatagsContainer').show();
                {% }         $('#completeInfo').hide();  %}

        </script>

        <!-- The template to display files available for download -->
        <script id="template-download" type="text/x-tmpl">
        {% totalWithUploadLimitError=0;
        for (var i=0, file; file=o.files[i]; i++) {
            if (file.uploadLimitError) {   totalWithUploadLimitError++;
        }} %}
            {% for (var i=0, file; file=o.files[i]; i++) {
                  totalProcessedNo++; %}
                <tr class="template-download fade">
                    {% if (file.error) { totalWithError++;%}
                        <td width="5%"></td>
                        <td colspan="2" class="name">
                            <span>{%=file.name%}</span>
                            <div class="fileSize">{%=o.formatFileSize(file.size)%}</div>
                        </td>
                        <td align="right" class="error" colspan="2"><span class="label label-important"><c:out
                            value='${msgpt:getMessage("page.text.error")}'/></span><br/><span class="label label-important">{%=file.error%}</span></td>
                    {% } else { %}
                        <td width="5%"></td>
                        <td class="name withoutError" colspan="4">
                            <a href="{%=file.url%}" title="{%=file.name%}" data-gallery="{%=file.thumbnail_url&&'gallery'%}" download="{%=file.name%}">{%=file.name%}</a>
                            <div class="fileSize">{%=o.formatFileSize(file.size)%}</div>
                        </td>

                        <td colspan="2" class="name withError"  style="display:none">
                            <span>{%=file.name%}</span>
                            <div class="fileSize">{%=o.formatFileSize(file.size)%}</div>
                        </td>
                        <td align="right" class="error withError" colspan="2" style="display:none" ><span class="label label-important"><c:out
                                 value='${msgpt:getMessage("page.text.error")}'/></span><br/><span class="label label-important">Exceeded Upload Limit</span></td>
                    {% } %}
                </tr>
            {% }
             $('#metatagsContainer').hide();
             $('#initialInfo').hide();
              if(o.files.length > 0 && totalUploadedNo==totalWithError) {
                    $('#completeInfo').show();
                    $('#waitingInfo').hide();
                    enableUpload();
                     showLog=true; first=false;
              }
              if(o.files.length > 0 && totalUploadedNo==totalProcessedNo && totalProcessedNo > totalWithError) {
                     disableUpload();
                    $('#completeInfo').hide();
                    $('#waitingInfo').show();
                     showLog=true;first=false;
              }
               if(totalWithUploadLimitError >=1) {
                     disableUpload();
                    $('#completeInfo').hide();
                    $('#waitingInfo').hide();
                     $('#uploadLimitError').show();
                     $('.withError').show();
                     $('.withoutError').hide();
                     showLog=true;first=false;
              }
            %}
        </script>

    </msgpt:BodyNew>
</msgpt:Html5>
