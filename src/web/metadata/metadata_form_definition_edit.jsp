<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.model.Document" %>
<%@page import="com.prinova.messagepoint.controller.metadata.MetadataFormDefinitionEditController" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">


        <msgpt:CalendarIncludes/>
        <msgpt:Stylesheet href="workflow/workflow.css"/>

        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/monthPicker/jquery.ui.monthpicker.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/monthPicker/ui.monthpicker.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/scrollTo/jquery.scrollTo-2.1.2.min.js"/>

        <style id="pageStyles">

            #ui-monthpicker-div {
                display: none;
            }
            .menuItemValueContainer, .menuItemValueContainer_selected {
                font-size: 12px;
                padding: 4px 8px;
                color: #5c5c5c;
            }

            .menuItemValueContainer_selected {
                background-color: #ccc;
            }

            .menuItemValueContainer:hover {
                background-color: #eee;
            }

            .formDefinitionItemContainer {
                margin-top: 20px;
                margin-bottom: 10px;
            }

            .nestingIndicator {
                display: inline-block;
                color: #555;
                padding: 2px;
                font-size: 12px;
            }

            img.ui-datepicker-trigger {
                margin-top: 5px;
            }

            .extendedPropertiesToggle {
                position: absolute;
                bottom: 10px;
                left: 15px;
                font-size: 16px;
            }

            .insertChildButton .fa-reply {
                -ms-transform: rotate(180deg); /* IE 9 */
                -webkit-transform: rotate(180deg); /* Chrome, Safari, Opera */
                transform: rotate(180deg);
                font-size: 14px;
            }

            .insertChildButton .fa-plus {
                top: -6px;
                left: -1px;
                font-size: 9px;
            }
        </style>

        <msgpt:Script>
            <script>

                // ***************** Step Action Functions ************************

                function generateGuid() {
                    function s4() {
                        return Math.floor((1 + Math.random()) * 0x10000)
                            .toString(16)
                            .substring(1);
                    }

                    return s4() + s4() + s4() + s4() + s4() + s4() + s4() + s4();
                }

                // Add item: Add new item to end of item display
                function addAction() {
                    generateItemInterface();
                    common.refreshParentIframeHeight();
                }

                // Remove item: Remove target item
                function removeAction(index) {
                    var targetContainers = $('#formDefinitionItemContainer_' + index);
                    targetContainers.push(getDescendants($('#formDefinitionItemContainer_' + index)));

                    for (var i = 0; i < targetContainers.length; i++) {
                        $(targetContainers[i]).hide();
                        if ($(targetContainers[i]).find("[id^='itemActionInput']").val() == "1")
                            $(targetContainers[i]).remove();
                        else
                            $(targetContainers[i]).find("[id^='itemActionInput']").val(-9);
                    }

                    if (!$("[id^='formDefinitionItemContainer_']").is(':visible'))
                        $('#noEntriesInfo').showEle('normal');

                    reorderItems();
                }

                // Step ordering: Update item indicators
                function reorderItems() {
                    var order = 1;
                    var primaryOrderDisplay = 1;
                    $("[id^='formDefinitionItemContainer_']:visible").each(function () {

                        var itemId = parseId(this);
                        var currentNestingLevel = parseInt(getNestingLevel(itemId));

                        if (currentNestingLevel == 0) {
                            $('#itemOrderDisplay_' + itemId).html(primaryOrderDisplay++);
                        } else {
                            $('#itemOrderDisplay_' + itemId).html("");
                            var indicatorHtml = "";
                            for (var i = 0; i < currentNestingLevel; i++)
                                indicatorHtml += "<i class=\"fa fa-circle nestingIndicator\"></i>";
                            if (indicatorHtml.length > 0)
                                $('#itemOrderDisplay_' + itemId).append(indicatorHtml);

                            var parentOrder = $(getParentItem(itemId)).find("[id^='itemOrderInput']").val();
                            $('#itemParentInput_' + itemId).val(parentOrder);
                        }

                        $('#itemOrderInput_' + itemId).val(order++);

                        // WEB SERVICE: Request refresh type (toggle on ancestor change)
                        if ($(this).find('.webServiceDetailsContainer').is(':visible')) {
                            if (currentNestingLevel == 0)
                                $('#webServiceRequest_' + itemId).disableOption('webServiceRequest_' + itemId + '_3');
                            else
                                $('#webServiceRequest_' + itemId).enableOption('webServiceRequest_' + itemId + '_3');
                        }

                    });
                }

                // Step ordering: Order items up and down
                function orderAction(action, itemId) {

                    var currentStepOrder = $('#itemOrderInput_' + itemId).val();
                    var itemContainer = $('#formDefinitionItemContainer_' + itemId);
                    var currentParentGUID = $('#itemParentGUID_' + itemId).val();
                    var itemDescendants = getDescendants(itemContainer);

                    $(itemContainer).find('#itemControlTable_' + itemId).hide();

                    if (action == 'up') {

                        var prevSibling = null;
                        $(itemContainer).prevAll("[id^='formDefinitionItemContainer_'][nesting_level=" + getNestingLevel(itemId) + "]:visible").each(function () {
                            if ($(this).find("[id^='itemParentGUID'][value='" + currentParentGUID + "']").length != 0 && prevSibling == null)
                                prevSibling = this;
                        });

                        $(prevSibling).before($(itemContainer));
                        $(prevSibling).before(itemDescendants);

                    } else if (action == 'down') {

                        var nextSibling = null;
                        $(itemContainer).nextAll("[id^='formDefinitionItemContainer_'][nesting_level=" + getNestingLevel(itemId) + "]:visible").each(function () {
                            if ($(this).find("[id^='itemParentGUID'][value='" + currentParentGUID + "']").length != 0 && nextSibling == null)
                                nextSibling = this;
                        });

                        var siblingDescendants = getDescendants(nextSibling);
                        var targetInsertAfter = nextSibling;
                        if (siblingDescendants.length != 0)
                            targetInsertAfter = siblingDescendants[siblingDescendants.length - 1];

                        $(targetInsertAfter).after(itemDescendants);
                        $(targetInsertAfter).after($(itemContainer));
                    }

                    reorderItems();
                }

                function nestAction(itemId) {
                    var itemContainer = $('#formDefinitionItemContainer_' + itemId);
                    var currentNestingLevel = getNestingLevel(itemId);

                    $(itemContainer).attr('nesting_level', currentNestingLevel + 1);
                    $(itemContainer).find('#itemParentGUID_' + itemId)
                        .val($(itemContainer).prevAll("[id^='formDefinitionItemContainer_'][nesting_level=" + currentNestingLevel + "]:visible:first").find("[id^='itemGUID']").val());
                    $(itemContainer).find('#displayCriteriaContainer_' + itemId).show();

                    // Children: Increment nesting level
                    var nextSibling = false;
                    $(itemContainer).nextAll("[id^='formDefinitionItemContainer_']:visible").each(function () {
                        if (parseInt($(this).attr('nesting_level')) > currentNestingLevel && !nextSibling)
                            $(this).attr('nesting_level', parseInt($(this).attr('nesting_level')) + 1);
                        else
                            nextSibling = true;
                    });

                    reorderItems();
                }

                function promoteAction(itemId) {
                    var itemContainer = $('#formDefinitionItemContainer_' + itemId);
                    var currentNestingLevel = getNestingLevel(itemId);
                    var parentItem = getParentItem(itemId);
                    var parentParentItem = getParentItem(parseId(parentItem));

                    $(itemContainer).attr('nesting_level', currentNestingLevel - 1);
                    if (parentParentItem != null) {
                        $(itemContainer).find('#itemParentGUID_' + itemId).val($(parentParentItem).find("[id^='itemGUID']").val());
                    } else {
                        $(itemContainer).find('#itemParentGUID_' + itemId).val('');
                        $(itemContainer).find('#itemParentInput_' + itemId).val('');
                        $(itemContainer).find('#displayCriteriaContainer_' + itemId).hide();
                    }

                    // Children: Decrement nesting level
                    var nextSibling = false;
                    $(itemContainer).nextAll("[id^='formDefinitionItemContainer_']:visible").each(function () {
                        if (parseInt($(this).attr('nesting_level')) > currentNestingLevel && !nextSibling)
                            $(this).attr('nesting_level', parseInt($(this).attr('nesting_level')) - 1);
                        else
                            nextSibling = true;
                    });

                    // Siblings: Change parent to promoted item
                    $(itemContainer).nextAll("[id^='formDefinitionItemContainer_']:visible").each(function () {
                        var currentItemId = parseId(this);
                        var currentParentItem = getParentItem(currentItemId);
                        if (currentParentItem != null && parseId($(currentParentItem)) == parseId($(parentItem)))
                            $(this).find("[id^='itemParentGUID_']").val($(itemContainer).find("[id^='itemGUID']").val());
                    });

                    reorderItems();
                }

                function getParentItem(itemId) {
                    var parentItemGUID = $('#formDefinitionItemContainer_' + itemId + ' #itemParentGUID_' + itemId).val();
                    if (parentItemGUID && parentItemGUID.length != 0) {
                        var parentItemId = parseId($("[id^='itemGUID'][value='" + parentItemGUID + "']"));
                        return $('#formDefinitionItemContainer_' + parentItemId);
                    } else {
                        return null;
                    }
                }

                function getNestingLevel(itemId) {
                    var nestingLevel = 0;
                    var parentItemGUID = $('#formDefinitionItemContainer_' + itemId + ' #itemParentGUID_' + itemId).val();
                    while (parentItemGUID && parentItemGUID.length != 0) {
                        nestingLevel++;
                        parentItemId = parseId($("[id^='itemGUID'][value='" + parentItemGUID + "']"));
                        parentItemGUID = $('#formDefinitionItemContainer_' + parentItemId + ' #itemParentGUID_' + parentItemId).val();
                    }
                    return nestingLevel;
                }

                function getDescendants(itemContainer) {
                    var currentNestingLevel = getNestingLevel(parseId(itemContainer));
                    var nextSibling = false;
                    var descendants = new Array();
                    $(itemContainer).nextAll("[id^='formDefinitionItemContainer_']:visible").each(function () {
                        if (parseInt($(this).attr('nesting_level')) > currentNestingLevel && !nextSibling)
                            descendants[descendants.length] = this;
                        else
                            nextSibling = true;
                    });
                    return descendants;
                }

                // ***************** Display Functions ************************

                function addMenuItemValues(index, popup) {
                    $('#menuItemsBindingsContainer_' + index).find('.noMenuValueItemsMsg').hide();

                    var newMenuItems = $(popup).find('#menuValueItemsInput').val().split(',');

                    for (var i = 0; i < newMenuItems.length; i++) {
                        if (newMenuItems[i].length == 0)
                            continue;

                        var valueExists = false;
                        $('#menuItemsBindingsContainer_' + index).find('.menuValueItemBinding').each(function () {
                            if ($.trim($(this).val()) == $.trim(newMenuItems[i]))
                                valueExists = true;
                        });
                        if (valueExists)
                            continue;

                        var newItemEle =
                            $("<div class=\"menuItemValueContainer\">" +
                                "<input class=\"menuValueItemBinding\" type=\"hidden\" name=\"metadataFormItemDefinitionVOs[" + index + "].menuValueItems\" value=\"\" />" +
                                $.trim(newMenuItems[i]) +
                                "</div>");
                        $(newItemEle).find('.menuValueItemBinding').val($.trim(newMenuItems[i]));
                        initMenuItemValue(newItemEle);

                        $('#menuItemsBindingsContainer_' + index).append(newItemEle);
                    }

                    popupFactoryRemove('metadataFormDefinitionEdit_addMenuItemValues');
                }

                function generateItemInterface(parentItemId) {
                    var index = $("[id^='formDefinitionItemContainer']").length - 1;

                    var itemTemplate = $('#formDefinitionItemContainer_TEMPLATE').clone();
                    var itemTemplateStr = $($('<div/>').append(itemTemplate)).html();
                    itemTemplateStr = itemTemplateStr.replace(/TEMPLATE/g, index);
                    itemTemplateStr = itemTemplateStr.replace(/INDEX_ID/g, index);
                    itemTemplateStr = itemTemplateStr.replace(/BIND_MASK/g, '');

                    if (parentItemId == undefined)
                        $('#itemBindingsContainer').append(itemTemplateStr);
                    else
                        $('#formDefinitionItemContainer_' + parentItemId).after(itemTemplateStr);

                    itemTemplate = $('#formDefinitionItemContainer_' + index);

                    if (parentItemId) {
                        var newItemParentGUID = $('#formDefinitionItemContainer_' + parentItemId + ' #itemGUID_' + parentItemId).val();
                        $(itemTemplate).find("[id^='itemParentGUID']").val(newItemParentGUID);
                        $(itemTemplate).find('#displayCriteriaContainer_' + index).show();
                    }

                    var nextOrder = $("[id^='formDefinitionItemContainer']:visible").length + 1;
                    $(itemTemplate).find("[id^='itemOrderDisplay']").text(nextOrder);

                    // SEC ITEM ACTION: 1 - New item
                    $(itemTemplate).find("[id^='itemActionInput']").val(1);

                    initItemContainer(itemTemplate);

                    $(itemTemplate).find('.simpleName,.extendedName,.targetingValue,.webSite,.numeric').each(function () {
                        initInputFilter(this);
                    });

                    $('#noEntriesInfo').hide();
                    $(itemTemplate).showEle('normal');
                    reorderItems();

                    // SCROLL TO NEW ITEM
                    $('.contentTableIframeExtended').scrollTo($('#formDefinitionItemContainer_' + index), 500, {
                        offset: {
                            top: -50
                        }
                    });
                }

                function toggleItemType(index) {
                    var itemEle = $('#formDefinitionItemContainer_' + index);

                    itemTypeId = $(itemEle).find("[id^='entryTypeSelect']").val();
                    $(itemEle).find(".typeDetailValue").hide();
                    if (itemTypeId == 4 || itemTypeId == 10 || itemTypeId == 11) {
                        $(itemEle).find("[id^='typeDetailsContainer']").show();
                        $(itemEle).find(".webServiceDetailsContainer").show();
                    } else if (itemTypeId == 3 || itemTypeId == 8) {
                        $(itemEle).find("[id^='typeDetailsContainer']").show();
                        $(itemEle).find(".menuItemsContainer").show();
                    } else {
                        $(itemEle).find("[id^='typeDetailsContainer']").hide();
                    }

                    // Field size
                    if (itemTypeId == 1 || itemTypeId == 2 || itemTypeId == 3 || itemTypeId == 4 || itemTypeId == 7 || itemTypeId == 8 || itemTypeId == 10 || itemTypeId == 11) {
                        $(itemEle).find("[id^='fieldSizeContainer']").show();
                    } else {
                        $(itemEle).find("[id^='fieldSizeContainer']").hide();
                    }

                    // Max length and Field valication
                    if (itemTypeId == 1 || itemTypeId == 10) {
                        $(itemEle).find("[id^='minLengthContainer']").show();
                        $(itemEle).find("[id^='maxLengthContainer']").show();
                        $(itemEle).find("[id^='fieldValidationContainer']").show();
                    } else {
                        $(itemEle).find("[id^='minLengthContainer']").hide();
                        $(itemEle).find("[id^='maxLengthContainer']").hide();
                        $(itemEle).find("[id^='fieldValidationContainer']").hide();
                    }

                    // Default value
                    $(itemEle).find("[id^='singleSelectDefaultValueContainer_']").hide();
                    $(itemEle).find("[id^='multiSelectDefaultValueContainer_']").hide();
                    $(itemEle).find("[id^='textDefaultValueContainer_']").hide();
                    $(itemEle).find("[id^='dateDefaultValueContainer_']").hide();
                    $(itemEle).find("[id^='monthDefaultValueContainer_']").hide();
                    if (itemTypeId == 1 || itemTypeId == 2) {
                        $(itemEle).find("[id^='textDefaultValueContainer_']").show();
                    } else if (itemTypeId == 3) {
                        $(itemEle).find("[id^='singleSelectDefaultValueContainer_']").show();
                    } else if (itemTypeId == 5) {
                        $(itemEle).find("[id^='monthDefaultValueContainer_']").show();
                    } else if (itemTypeId == 6) {
                        $(itemEle).find("[id^='dateDefaultValueContainer_']").show();
                    } else if (itemTypeId == 8) {
                        $(itemEle).find("[id^='multiSelectDefaultValueContainer_']").show();
                    }

                    // Unique value
                    if (itemTypeId == 1) {
                        $(itemEle).find("[id^='uniqueValueContainer']").show();
                    } else {
                        $(itemEle).find("[id^='uniqueValueContainer']").hide();
                    }

                    toggleWebServicePropertiesDisplay();

                }

                function toggleWebServicePropertiesDisplay() {
                    var displayWebServiceProperties = false;
                    $("select[id^='entryTypeSelect']").each(function () {
                        if ($(this).val() == 4 || $(this).val() == 10 || $(this).val() == 11)
                            displayWebServiceProperties = true;
                    });

                    if (displayWebServiceProperties) {
                        $('.webServicePropertiesContainer').closest('tr').show();
                        $('.webServicePropertiesContainer').show();
                    } else {
                        $('.webServicePropertiesContainer').hide();
                    }
                }

                // ***************** Init Functions ***************************

                function initBtn(ele, type) {

                    $(ele)
                        .mouseover(function () {
                            if (!$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight actionBtn_toggleSelect')) {
                                $(this).removeClass('actionBtn');
                                $(this).addClass('actionBtn_hov');
                            }
                        })
                        .mouseout(function () {
                            if (!$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight actionBtn_toggleSelect')) {
                                $(this).removeClass('actionBtn_hov');
                                $(this).addClass('actionBtn');
                            }
                        });


                    if (type == "toggle") {
                        $(ele)
                            .click(function () {
                                if (!$(this).hasClass('actionBtn_disabled')) {
                                    if ($(this).hasClass('actionBtn_toggleSelectHighlight'))
                                        $(this).removeClass('actionBtn_toggleSelectHighlight').addClass('actionBtn');
                                    else
                                        $(this).removeClass('actionBtn').addClass('actionBtn_toggleSelectHighlight');
                                }

                            });
                    } else if (type == "button") {
                        $(ele)
                            .mousedown(function () {
                                if (!$(this).hasClass('actionBtn_disabled'))
                                    $(this).removeClass('actionBtn actionBtn_hov').addClass('actionBtn_selected');
                            })
                            .mouseup(function () {
                                if (!$(this).hasClass('actionBtn_disabled'))
                                    $(this).removeClass('actionBtn_selected').addClass('actionBtn');
                            });
                    }

                }

                function initInputFilter(inputEle) {
                    var guid = generateGuid();
                    $(inputEle)
                        .attr('filterId', "filteredInput_" + guid)
                        .after($.alphanumeric.generateFilterTooltip("filteredInput_" + guid));

                    if ($(inputEle).is('.simpleName'))
                        $(inputEle).alphanumeric({simpleName: true});
                    else if ($(inputEle).is('.extendedName'))
                        $(inputEle).alphanumeric({extendedName: true});
                    else if ($(inputEle).is('.targetingValue'))
                        $(inputEle).alphanumeric({targetingValue: true});
                    else if ($(inputEle).is('.webSite'))
                        $(inputEle).alphanumeric({webSite: true});
                    else if ($(inputEle).is('.numeric'))
                        $(inputEle).alphanumeric({numeric: true});
                    else if ($(inputEle).is('.connectorName'))
                        $(inputEle).alphanumeric({connectorName: true});
                }

                function initItemContainer(itemEle) {
                    var index = parseId(itemEle);

                    $(itemEle).find(".style_select").styleActionElement({maxItemDisplay: 4});
                    $(itemEle).find(".style_multiselect").styleActionElement({maxItemDisplay: 4});

                    $(itemEle).attr('nesting_level', getNestingLevel(index));

                    $(itemEle).find("[id^='itemGUID_']").attr('value', generateGuid());

                    // Init the date picker
                    var guid = generateGuid();
                    $(itemEle).find('.newDateBinding').attr('id', 'calendar' + guid);
                    $(itemEle).find('.newDateInput').attr('id', 'dateDisplay_calendar' + guid);
                    $(itemEle).find('.newDateInfo').attr('filterid', 'calendar' + guid);
                    $(itemEle).find('.newDateInput').datepicker({
                        showOn			: "both",
                        buttonImage		: 'data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="16"><path class="fill-info" d="M148 288h-40c-6.6 0-12-5.4-12-12v-40c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12zm108-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 96v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm192 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96-260v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V112c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48zm-48 346V160H48v298c0 3.3 2.7 6 6 6h340c3.3 0 6-2.7 6-6z"/></svg>',
                        buttonImageOnly	: true,
                        dateFormat: defaultDateFormat,
                        bindField: true,
                        altFormat: defaultDateFormat,
                        buttonText: client_messages.text.select_date,
                        dayNamesMin: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],
                        changeMonth: true,
                        changeYear: true,
                        onSelect: function (dateText, inst) {
                            $(itemEle).find('.newDateBinding').val(dateText);
                        }
                    });
                    $(itemEle).find(".newDateInput").datepickerCustom();

                    guid = generateGuid();
                    $(itemEle).find('.newMonthBinding').attr('id', 'calendar' + guid);
                    $(itemEle).find('.newMonthInput').attr('id', 'dateDisplay_calendar' + guid);
                    $(itemEle).find('.newMonthInfo').attr('filterid', 'calendar' + guid);
                    $(itemEle).find('.newMonthInput').monthpicker({
                        showOn			: "both",
                        buttonImage		: 'data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="16"><path class="fill-info" d="M148 288h-40c-6.6 0-12-5.4-12-12v-40c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12zm108-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 96v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm192 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96-260v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V112c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48zm-48 346V160H48v298c0 3.3 2.7 6 6 6h340c3.3 0 6-2.7 6-6z"/></svg>',
                        buttonImageOnly	: true,
                        dateFormat: 'M yy',
                        changeMonth: true,
                        changeYear: true,
                        altFormat: 'M yy',
                        showButtonPanel: true,
                        buttonText: client_messages.text.select_date,
                        dayNamesMin: dayNamesMinArray,
                        dayNames: dayNamesArray,
                        monthNamesShort: monthNamesShortArray,
                        onSelect: function (dateText, inst) {
                            $(itemEle).find('.newMonthBinding').val(dateText);
                        }
                    });
                    $(itemEle).find(".newMonthInput").datepickerCustom();

                    $(itemEle).each(function () {
                        $(this).mouseover(function () {
                            $('#itemControlTable_' + index).show();

                            var currentParentGUID = $('#itemParentGUID_' + index).val();
                            var itemContainer = $('#formDefinitionItemContainer_' + index);
                            var currentItemOrder = $(itemContainer).find("[id^='itemOrderInput']").val();


                            var hasPrevSiblings = false;
                            $(itemContainer).prevAll("[id^='formDefinitionItemContainer_'][nesting_level=" + getNestingLevel(index) + "]:visible").each(function () {
                                if ($(this).find("[id^='itemParentGUID'][value='" + currentParentGUID + "']").length != 0)
                                    hasPrevSiblings = true;
                            });

                            var hasNextSiblings = false;
                            $(itemContainer).nextAll("[id^='formDefinitionItemContainer_'][nesting_level=" + getNestingLevel(index) + "]:visible").each(function () {
                                if ($(this).find("[id^='itemParentGUID'][value='" + currentParentGUID + "']").length != 0)
                                    hasNextSiblings = true;
                            });

                            $('#itemControlTable_' + index)
                                .find('#nestButton_' + index + ',#promoteButton_' + index + ',#upButton_' + index + ',#downButton_' + index)
                                .hide();

                            // NEST BUTTON
                            // First item: Can't nest
                            if ((currentItemOrder != 1 && currentParentGUID.length == 0) || hasPrevSiblings)
                                $('#itemControlTable_' + index).find('#nestButton_' + index).show();

                            // PROMOTE BUTTON
                            // First item or first level items: Can't promote
                            if (currentParentGUID.length != 0)
                                $('#itemControlTable_' + index).find('#promoteButton_' + index).show();

                            // UP BUTTON
                            if (hasPrevSiblings)
                                $('#itemControlTable_' + index).find('#upButton_' + index).show();

                            // DOWN BUTTON
                            if (hasNextSiblings)
                                $('#itemControlTable_' + index).find('#downButton_' + index).show();

                        });
                        $(this).mouseout(function () {
                            $('#itemControlTable_' + index).hide();
                        });
                    });

                    // Init order up buttons
                    $(itemEle).find("[id^='upButton']").each(function () {
                        $(this).click(function () {
                            orderAction('up', parseId(this));
                        });
                    });

                    // Init order down buttons
                    $(itemEle).find("[id^='downButton']").each(function () {
                        $(this).click(function () {
                            orderAction('down', parseId(this));
                        });
                    });

                    $(itemEle).find("[id^='deleteButton']").each(function () {
                        $(this).click(function () {
                            removeAction(parseId(this));
                        });
                    });

                    $(itemEle).find("[id^='nestButton']").each(function () {
                        $(this).click(function () {
                            nestAction(parseId(this));
                        });
                    });

                    $(itemEle).find("[id^='promoteButton']").each(function () {
                        $(this).click(function () {
                            promoteAction(parseId(this));
                        });
                    });

                    $(itemEle).find("[id^='insertChildButton']").each(function () {
                        $(this).click(function () {
                            generateItemInterface(parseId(this));
                        });
                    });

                    $(itemEle).find(".inputExpandOnFocus").each(function () {
                        $(this)
                            .focus(function () {
                                $(this).animate({height: '80px'});
                            })
                            .focusout(function () {
                                $(this).animate({height: '17px'});
                            });
                    });

                    toggleItemType(index);

                    initBtn($(itemEle).find(".addMenuValueItemBtn"), "button");
                    initBtn($(itemEle).find(".removeMenuValueItemBtn"), "button");
                    initBtn($(itemEle).find(".findMenuValueItemBtn"), "button");
                    initBtn($(itemEle).find(".setPrimaryDriverBtn"), "toggle");
                    initBtn($(itemEle).find(".setIndicatorBtn"), "toggle");

                    $(itemEle).find(".setPrimaryDriverBtn").click(function () {

                        if ($(itemEle).find('.primaryDriverToggle').is(':checked'))
                            $(itemEle).find('.primaryDriverToggle').removeAttr('checked');
                        else
                            $(itemEle).find('.primaryDriverToggle').attr('checked', 'checked');

                        var currentEntryId = parseId(this);
                        $(".setPrimaryDriverBtn").each(function () {
                            if (parseId(this) != currentEntryId) {
                                $(this).removeClass('actionBtn_toggleSelectHighlight').addClass('actionBtn');
                                $(this).find('.primaryDriverToggle').removeAttr('checked');
                            }
                        });

                    });
                    $(itemEle).find(".setIndicatorBtn").click(function () {

                        if ($(itemEle).find('.indicatorToggle').is(':checked'))
                            $(itemEle).find('.indicatorToggle').removeAttr('checked');
                        else
                            $(itemEle).find('.indicatorToggle').attr('checked', 'checked');

                        var currentEntryId = parseId(this);
                        $(".setIndicatorBtn").each(function () {
                            if (parseId(this) != currentEntryId) {
                                $(this).removeClass('actionBtn_toggleSelectHighlight').addClass('actionBtn');
                                $(this).find('.indicatorToggle').removeAttr('checked');
                            }
                        });

                    });

                    initBtn($(itemEle).find(".mandatoryCreateOnImportBtn"), "toggle");
                    $(itemEle).find(".mandatoryCreateOnImportBtn").click(function () {

                        if ($(this).find("[id^='mandatoryCreateOnImportCheckbox_']").is(':checked'))
                            $(this).find("[id^='mandatoryCreateOnImportCheckbox_']").removeAttr('checked');
                        else
                            $(this).find("[id^='mandatoryCreateOnImportCheckbox_']").attr('checked', 'checked');

                    });
                    if ($(itemEle).find("[id^='mandatoryCreateOnImportCheckbox_']").is(':checked'))
                        $(itemEle).find(".mandatoryCreateOnImportBtn").removeClass('actionBtn').addClass('actionBtn_toggleSelectHighlight');

                    function initAddValuesPopup(button, predefinedValues) {

                        $(button).popupFactory({
                            title: client_messages.title.add_menu_item,
                            popupLocation: "left",
                            trigger: "instant",
                            width: 300,
                            fnSetContent: function (o) {

                                var index = parseId($(o.targetEle).closest('.formDefinitionItemContainer'));

                                var popupEle = $("<div><div align=\"left\" style=\"font-size: 11px; margin: 8px 16px;\">" +
                                    "<div align=\"left\" style=\"font-size: 11px; font-weight: bold;\">" + client_messages.text.add_items_comma_separate_values + "</div>" +
                                    "<div align=\"left\" style=\"white-space: nowrap;\">" +
                                    "<textarea id=\"menuValueItemsInput\" class=\"input250 targetingValue\" filterId=\"menuValueItemsInput\"></textarea>" +
                                    $.alphanumeric.generateFilterTooltip("menuValueItemsInput") +
                                    "</div>" +
                                    "</div>" +
                                    "<div style=\"padding: 8px 16px; background-color: #f5f5f5; border-top: 1px solid #bbb\">" +
                                    "<table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"><tr>" +
                                    "<td/>" +
                                    "<td width=\"1%\">" +
                                    "<input title=\"" + client_messages.text.add + "\" type=\"button\" id=\"confirmAddMenuValueItemsBtn\" style=\"display: none;\" />" +
                                    "</td>" +
                                    "<td width=\"1%\">" +
                                    "<input title=\"" + client_messages.text.close + "\" type=\"button\" id=\"closeAddMenuValueItemsPopupBtn\" onclick=\"javascript:popupFactoryRemove('metadataFormDefinitionEdit_addMenuItemPopup');\" style=\"display: none;\" />" +
                                    "</td>" +
                                    "</tr></table>" +
                                    "</div></div>");

                                if (predefinedValues != undefined)
                                    $(popupEle).find('#menuValueItemsInput').val(predefinedValues);

                                $(popupEle).find('#confirmAddMenuValueItemsBtn').click(function () {
                                    addMenuItemValues(index, $(popupEle));
                                    refreshSelectItems(index);
                                });

                                $(popupEle).find('#confirmAddMenuValueItemsBtn,#closeAddMenuValueItemsPopupBtn').styleActionElement();
                                $(popupEle).find('.targetingValue').alphanumeric({targetingValue: true});

                                return popupEle;
                            }
                        });
                    }

                    $(itemEle).find(".addMenuValueItemBtn").click(function () {
                        if ($(this).is('.actionBtn_disabled'))
                            return;

                        var button = $(this);
                        setTimeout(function () {
                            initAddValuesPopup(button);
                        }, 10);
                    });

                    $(itemEle).find(".removeMenuValueItemBtn").click(function () {
                        if ($(this).is('.actionBtn_disabled'))
                            return;

                        $(itemEle).find(".menuItemValueContainer_selected").remove();
                        if ($(itemEle).find('.menuItemValueContainer').length == 0)
                            $('#menuItemsBindingsContainer_' + index).find('.noMenuValueItemsMsg').showEle('normal');

                        refreshSelectItems(index);
                    });

                    function processMetadataItemValuesResponse(data, button) {
                        var predefinedValues = "";
                        if (data.values != undefined)
                            predefinedValues = data.values.join(",");

                        initAddValuesPopup(button, predefinedValues);
                    };

                    $(itemEle).find(".findMenuValueItemBtn").click(function () {
                        if ($(this).is('.actionBtn_disabled'))
                            return;

                        var stampDate = new Date();
                        var itemDefId = $(this).closest('.formDefinitionItemContainer').find("[id^='itemIdInput_']").val();
                        var button = $(this);
                        $.ajax({
                            type: "GET",
                            url: context + "/getObjectInfo.form?type=metadataItemValues&objectId=" + itemDefId + "&cacheStamp=" + (stampDate.getTime()),
                            dataType: "json",
                            success: function (data) {
                                processMetadataItemValuesResponse(data, button);
                            }
                        });
                    });

                    $(itemEle).find('.menuItemValueContainer').each(function () {
                        initMenuItemValue(this);
                    });
                    if ($(itemEle).find('.menuItemValueContainer').length == 0)
                        $(itemEle).find('.noMenuValueItemsMsg').show();

                    initBtn($(itemEle).find(".addDataConnectionBtn"), "button");
                    $(itemEle).find(".addDataConnectionBtn").click(function () {

                        var connectionTemplate = $('.webServiceDataConnectionContainer_TEMPLATE').clone();
                        var connectionTemplateStr = $($('<div>').append(connectionTemplate)).html();
                        connectionTemplateStr = connectionTemplateStr.replace(/_TEMPLATE/g, '');
                        connectionTemplateStr = connectionTemplateStr.replace(/INDEX_ID/g, index);
                        connectionTemplateStr = connectionTemplateStr.replace(/BIND_MASK/g, '');
                        connectionTemplateStr = connectionTemplateStr.replace(/CDE_COUNT/g, $('.connectorDataElementsSelect').length);


                        connectionTemplate = $(connectionTemplateStr);

                        $(connectionTemplate).find('.connectorDataElementsSelect').addClass('style_select');
                        $(connectionTemplate).find('.style_select').styleActionElement({maxItemDisplay: 4});
                        initBtn($(connectionTemplate).find('.removeMenuValueItemBtn'), "button");
                        $(connectionTemplate).find('.removeMenuValueItemBtn').click(function () {
                            $(this).closest('tr').remove();
                            if ($(itemEle).find('.webServiceDataConnectionContainer').length == 0)
                                $(itemEle).find('.infoMsg_noWebServiceDataConnectionsMsg').showEle('normal');
                        });

                        $(itemEle).find('.webServiceConnectorInsertMarker').before($(connectionTemplate));

                        initInputFilter($(connectionTemplate).find('.simpleName,.connectorName'));

                        $(itemEle).find('.infoMsg_noWebServiceDataConnectionsMsg').hide();
                        $(connectionTemplate).showEle('normal');

                    });

                    if ($(itemEle).find('.webServiceDataConnectionContainer').length == 0)
                        $(itemEle).find('.infoMsg_noWebServiceDataConnectionsMsg').showEle('normal');
                    $(itemEle).find('.webServiceDataConnectionContainer .removeMenuValueItemBtn').click(function () {
                        $(this).closest('tr').remove();
                        if ($(itemEle).find('.webServiceDataConnectionContainer').length == 0)
                            $(itemEle).find('.infoMsg_noWebServiceDataConnectionsMsg').showEle('normal');
                    });

                    $(itemEle).find('.extendedPropertiesToggle').click(function () {
                        var extPropContainer = $('#extendedPropertiesContainer_' + parseId(this));
                        if ($(extPropContainer).is(':visible')) {
                            $(this).find('i').removeClass('fa-minus-square').addClass('fa-plus-square');
                            $(extPropContainer).slideUp();
                        } else {
                            $(this).find('i').removeClass('fa-plus-square').addClass('fa-minus-square');
                            $(extPropContainer).slideDown();
                        }
                    });

                    $(itemEle).find('.detailTip').each(function () {
                        initTip(this);
                    });

                    reorderItems();

                }

                function initMenuItemValue(ele) {
                    $(ele).click(function () {
                        if ($(this).is('.menuItemValueContainer'))
                            $(this).removeClass('menuItemValueContainer').addClass('menuItemValueContainer_selected');
                        else
                            $(this).removeClass('menuItemValueContainer_selected').addClass('menuItemValueContainer');
                    });
                }

                function refreshSelectItems(index) {
                    // For single select
                    $('#singleSelectDefaultValue_' + index).find('option[value="-1"]').siblings().remove();
                    $('#singleSelectDefaultValue_' + index + '_menuTable').remove();
                    // Append updated options
                    var optionIdx = 0;
                    $('#menuItemsBindingsContainer_' + index).find('.menuValueItemBinding').each(function () {
                        var selectOption = '<option value="' + optionIdx + '" id="singleSelectDefaultValue_' + index + '_' + optionIdx + '">' + $(this).val() + '</option>';
                        $('#singleSelectDefaultValue_' + index).append(selectOption);
                        optionIdx++;
                    });
                    // Refresh the select menu
                    $('#singleSelectDefaultValue_' + index).addClass('style_select');
                    $('#singleSelectDefaultValue_' + index).styleActionElement({maxItemDisplay: 4});

                    // For multi select
                    $('[id^="multiSelectDefaultValueOption' + index + '_"]').remove();
                    $('#multiSelectDefaultValue_' + index + '_menuTable').remove();
                    // Append updated options
                    var optionIdx = 0;
                    $('#menuItemsBindingsContainer_' + index).find('.menuValueItemBinding').each(function () {
                        var selectOption = '<div id="multiSelectDefaultValueOption' + index + '_' + optionIdx + '">' +
                            '<input name="metadataFormItemDefinitionVOs[' + index + '].multiSelectDefaultValues" value="' + optionIdx + '" type="checkbox" class="style_multiselect_binding">' +
                            '<input type="hidden" name="_metadataFormItemDefinitionVOs[' + index + '].multiSelectDefaultValues">' +
                            '<span>' + $(this).val() + '</span>' +
                            '</div>';
                        $('#multiSelectDefaultValue_' + index).append(selectOption);
                        optionIdx++;
                    });
                    // Refresh the select menu
                    $('#multiSelectDefaultValue_' + index).addClass('style_multiselect');
                    $('#multiSelectDefaultValue_' + index).styleActionElement({maxItemDisplay: 4});
                }

                function toggleDefaultToToday(index) {
                    if ($('#defaultToTodayCheckbox_' + index).is(':checked')) {
                        $('#defaultDateInput_' + index).hide();
                    } else {
                        $('#defaultDateInput_' + index).show();
                    }
                }

                function updateMaxLength(ele) {
                    var maxLength = $(ele).val();
                    var idx = parseId($(ele).attr('id'));
                    if (maxLength && maxLength > 0) {
                        $('#maxLengthInfo_' + idx).hide();
                    } else {
                        $('#maxLengthInfo_' + idx).show();
                    }
                }

                function updateMinLength(ele) {
                    var minLength = $(ele).val();
                    var idx = parseId($(ele).attr('id'));
                    if (minLength && minLength > 0) {
                        $('#minLengthInfo_' + idx).hide();
                    } else {
                        $('#minLengthInfo_' + idx).show();
                    }
                }

                // #######################################################
                // ############### START INIT ############################
                $(function () {

                    for (var i = 0; i < 10; i++)
                        $('#pageStyles').append(
                            "[nesting_level=\"" + (i + 1) + "\"] { \n" +
                            "margin-left: " + (45 * (i + 1)) + "px;" +
                            "}"
                        );

                    if (!$("[id^='formDefinitionItemContainer']:visible").is(':visible'))
                        $('#noEntriesInfo').showEle('normal');
                    else
                        $('#noEntriesInfo').hide();

                    $("input:button").styleActionElement();
                    $("#formTypeSelect").styleActionElement({labelAlign: true, maxItemDisplay: 4});

                    $("[id^='formDefinitionItemContainer']:visible").each(function () {
                        initItemContainer(this);
                    });

                    $('#formDefinitionMetatags').tagCloud({
                        tagCloudType: 8,
                        inputType: 'metatags'
                    });

                    // CHILDREN: Set parent GUIDs
                    $("[id^='formDefinitionItemContainer_']:visible").each(function () {
                        var parentOrder = $(this).find("[id^='itemParentInput_']").val();
                        if (parentOrder && parentOrder.length != 0) {
                            var parentGUID = $("[id^='itemOrderInput_'][value='" + parentOrder + "']").closest('.formDefinitionItemContainer').find("[id^='itemGUID_']").val();
                            $(this).find("[id^='itemParentGUID']").attr('value', parentGUID);
                        }
                    });
                    $("[id^='formDefinitionItemContainer_']:visible").each(function () {
                        $(this).attr('nesting_level', getNestingLevel(parseId(this)));
                    });
                    reorderItems();

                    toggleWebServicePropertiesDisplay();

                    common.refreshParentIframeHeight();
                });
                // ################ END INIT #############################
                // #######################################################

            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <c:set var="submitType" value="<%= MetadataFormDefinitionEditController.FORM_SUBMIT_TYPE_SUBMIT %>"/>

    <msgpt:BodyNew theme="minimal" type="iframe">

        <form:form modelAttribute="command" enctype="multipart/form-data">

            <div class="contentTableIframeExtended">
                <div class="contentPanel small-content">
                    <div class="pddng-lv3 pddng-vertical-lv4 pddng-bottom-lv0">
                        <form:errors path="*">
                            <msgpt:Information errorMsgs="${messages}" type="error"/>
                        </form:errors>
                        <msgpt:DataTable labelPosition="top" multiColumn="true">
                            <!-- Name -->
                            <msgpt:TableItem label="page.label.name" mandatory="true">
                                <msgpt:InputFilter type="extendedName">
                                    <form:input path="name" cssClass="inputXXL" maxlength="96"/>
                                </msgpt:InputFilter>
                            </msgpt:TableItem>
                            <!-- Type -->
                            <msgpt:TableItem label="page.label.type">
                                <c:choose>
                                    <c:when test="${isReferenced || command.type == 7}">
                                        <c:out value="${command.metadataFormDefinition.typeDisplayLabel}"/>
                                    </c:when>
                                    <c:otherwise>
                                        <form:select id="formTypeSelect" items="${metadataFormDefinitionTypes}"
                                                     itemLabel="name" itemValue="id" path="type"
                                                     cssClass="inputM style_select"/>
                                    </c:otherwise>
                                </c:choose>
                            </msgpt:TableItem>
                            <!-- Description -->
                            <msgpt:TableItem label="page.label.description">
                                <msgpt:InputFilter type="description">
                                    <form:textarea path="description" cssClass="inputXXL" rows="2"/>
                                </msgpt:InputFilter>
                            </msgpt:TableItem>
                            <!-- Metatags -->
                            <c:if test="${command.type != 7}">
                                <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
                                    <msgpt:TableItem label="page.label.metatags" valign="top">
                                        <msgpt:InputFilter type="simpleName">
                                            <form:textarea id="formDefinitionMetatags" path="metatags"
                                                           cssClass="input325" rows="2"/>
                                        </msgpt:InputFilter>
                                    </msgpt:TableItem>
                                </msgpt:IfAuthGranted>
                            </c:if>
                            <msgpt:TableItem label="page.label.data.web.service"
                                             cssClass="webServicePropertiesContainer" style="display: none;">
                                <table cellspacing="0" cellpadding="0" border="0" class="innerCellTable"
                                       style="margin-top: 3px;">
                                    <tr>
                                        <td style="padding-right: 4px; padding-bottom: 4px;" class="itemText">
                                            <fmtSpring:message code="page.label.url"/></td>
                                        <td style="padding-right: 4px; padding-bottom: 4px;">
                                            <msgpt:InputFilter type="webSite">
                                                <form:input id="webServiceUrl" path="url" cssClass="inputXL"/>
                                            </msgpt:InputFilter>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding-right: 4px; padding-bottom: 4px;" class="itemText">
                                            <fmtSpring:message code="page.label.username"/></td>
                                        <td style="padding-right: 4px; padding-bottom: 4px;">
                                            <msgpt:InputFilter type="targetingValue">
                                                <form:input id="webServiceUsername" path="username" cssClass="inputXL"/>
                                            </msgpt:InputFilter>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding-right: 4px; padding-bottom: 4px;" class="itemText">
                                            <fmtSpring:message code="page.label.password"/></td>
                                        <td style="padding-right: 4px; padding-bottom: 4px;">
                                            <msgpt:InputFilter type="targetingValue">
                                                <form:password showPassword="true" id="webServicePassword"
                                                               path="password" cssClass="inputXL"/>
                                            </msgpt:InputFilter>
                                        </td>
                                    </tr>
                                </table>
                            </msgpt:TableItem>
                        </msgpt:DataTable>
                    </div>
                    <!-- ITEMS -->
                    <div id="noEntriesInfo" class="pddng-lv3" style="display: none;">
                        <div class="InfoSysContainer_info">
                            <i class="fa icon fa-info-circle" aria-hidden="true"></i>
                            <p><fmtSpring:message code="page.text.template.has.no.metadata.items"/></p>
                            <p>
                                <em><fmtSpring:message code="page.text.click.button.to.add.items"/></em>
                            </p>
                        </div>
                    </div>
                    <div id="itemBindingsContainer" class="pddng-horizontal-lv3">
                        <!-- PERSISTED: ITEMS -->
                        <c:forEach var="currentFormDefinitionItemVO"
                                   items="${command.metadataFormItemDefinitionVOs}" varStatus="itemStat">
                            <div id="formDefinitionItemContainer_${itemStat.index}"
                                 class="formDefinitionItemContainer ${not empty command.metadataFormItemDefinitionVOs[itemStat.index].parentOrder ? 'nestedFormDefinitionItem' : ''}">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0" class="tableOutline">
                                    <!-- Row: Item Order/Name -->
                                    <tr>
                                        <!-- Order -->
                                        <td id="itemOrderContainer_${itemStat.index}" width="40px"
                                            class="tableIndicatorCol cellTopLeft">
                                            <div class="itemIndicator">
												<span id="itemOrderDisplay_${itemStat.index}">
													<c:out value="${currentFormDefinitionItemVO.order}"/>
												</span>
                                                <form:hidden id="itemOrderInput_${itemStat.index}"
                                                             path="metadataFormItemDefinitionVOs[${itemStat.index}].order"
                                                             cssClass="input1digit"/>
                                                <form:hidden id="itemActionInput_${itemStat.index}"
                                                             path="metadataFormItemDefinitionVOs[${itemStat.index}].action"
                                                             cssClass="input1digit"/>
                                                <form:hidden id="itemParentInput_${itemStat.index}"
                                                             path="metadataFormItemDefinitionVOs[${itemStat.index}].parentOrder"
                                                             cssClass="input1digit"/>
                                                <form:hidden id="itemIdInput_${itemStat.index}"
                                                             path="metadataFormItemDefinitionVOs[${itemStat.index}].id"
                                                             cssClass="input1digit"/>
                                                <input type="hidden" id="itemGUID_${itemStat.index}" value=""/>
                                                <input type="hidden" id="itemParentGUID_${itemStat.index}"
                                                       value=""/>
                                            </div>
                                        </td>
                                        <td colspan="2" class="tableContentCol cellTopRight"
                                            style="white-space: nowrap;">
                                            <!-- Name -->
                                            <div style="display: inline-block;">
                                                <msgpt:InputFilter type="simpleName">
                                                    <form:input
                                                            path="metadataFormItemDefinitionVOs[${itemStat.index}].name"
                                                            id="itemName_${itemStat.index}" cssClass="inputExtended"
                                                            cssStyle="width: 825px;"/>
                                                </msgpt:InputFilter>
                                            </div>
                                            <c:if test="${command.type == 7}">
                                                <div style="display: inline-block;">
                                                    <div class="mandatoryCreateOnImportBtn actionBtn_roundAll actionBtn"
                                                         style="white-space: nowrap; margin-bottom: 6px;">
                                                        <i class="fa fa-exclamation fa-mp-btn detailTip"
                                                           title="|<div class='detailTipText'>${msgpt:getMessage('page.text.manadatory.create.on.import')}</div>"></i>
                                                        <form:checkbox
                                                                id="mandatoryCreateOnImportCheckbox_${itemStat.index}"
                                                                path="metadataFormItemDefinitionVOs[${itemStat.index}].autoGenerateOnImport"
                                                                cssStyle="display: none;"/>
                                                    </div>
                                                </div>
                                            </c:if>
                                        </td>
                                    </tr>
                                    <!-- Row: Item Actions/Details -->
                                    <tr>
                                        <!-- Item Actions -->
                                        <td rowspan="2" class="tableIndicatorCol cellBottomLeft"
                                            style="border-bottom: none; position: relative;">

                                            <table id="itemControlTable_${itemStat.index}" width="100%"
                                                   cellspacing="0" cellpadding="0" border="0" class="innerCellTable"
                                                   style="display:none;">
                                                <tr>
                                                    <td colspan="3" align="center" class="fa-mp-container"><i
                                                            id="upButton_${itemStat.index}"
                                                            class="fa fa-caret-up fa-mp-btn fa-lg detailTip"
                                                            title="|<div class='detailTipText'>${msgpt:getMessage('page.label.move.up')}</div>"
                                                            style="margin-left: 1px;"></i></td>
                                                </tr>
                                                <tr>
                                                    <td width="1%" align="center" class="fa-mp-container"><i
                                                            id="promoteButton_${itemStat.index}"
                                                            class="fa fa-caret-left fa-mp-btn fa-lg detailTip"
                                                            title="|<div class='detailTipText'>${msgpt:getMessage('page.label.promote')}</div>"
                                                            style="padding-left: 4px; position: relative; top: 1px; display: none;"></i>
                                                    </td>
                                                    <td></td>
                                                    <td width="1%" align="center" class="fa-mp-container"><i
                                                            id="nestButton_${itemStat.index}"
                                                            class="fa fa-caret-right fa-mp-btn fa-lg detailTip"
                                                            title="|<div class='detailTipText'>${msgpt:getMessage('page.label.nest')}</div>"
                                                            style="padding-right: 4px; position: relative; top: 1px; display: none;"></i>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="3" align="center" class="fa-mp-container"><i
                                                            id="downButton_${itemStat.index}"
                                                            class="fa fa-caret-down fa-mp-btn fa-lg detailTip"
                                                            title="|<div class='detailTipText'>${msgpt:getMessage('page.label.move.down')}</div>"
                                                            style="margin-left: 1px;"></i></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="3" align="center" class="fa-mp-container"
                                                        style="padding-top: 12px;"><i
                                                            id="deleteButton_${itemStat.index}"
                                                            class="far fa-times fa-mp-btn fa-lg detailTip"
                                                            title="|<div class='detailTipText'>${msgpt:getMessage('page.label.remove.item.plus.children')}</div>"
                                                            style=""></i></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="3" align="center" class="fa-mp-container"
                                                        style="padding-top: 12px;">
														<span id="insertChildButton_${itemStat.index}"
                                                              class="fa-stack insertChildButton detailTip"
                                                              title="|<div class='detailTipText'>${msgpt:getMessage('page.label.add.child')}</div>">
															<i class="fa fa-reply fa-mp-btn fa-stack-1x"></i>
															<i class="fa fa-plus fa-mp-btn fa-stack-1x"></i>
														</span>
                                                    </td>
                                                </tr>
                                            </table>

                                            <div id="extendedPropertiesToggle_${itemStat.index}"
                                                 class="extendedPropertiesToggle fa-mp-container">
                                                <i class="fa fa-plus-square fa-mp-btn"></i>
                                            </div>

                                        </td>
                                        <!-- Detail: Primary -->
                                        <td class="tableContentCol"
                                            style="border-bottom: none; vertical-align: top;">

                                            <table cellspacing="0" cellpadding="0" border="0"
                                                   class="innerCellTable">
                                                <tr>
                                                    <td style="padding-right: 4px;" class="itemText">
                                                        <fmtSpring:message code="page.label.description"/></td>
                                                    <td style="padding-right: 4px;">
                                                        <form:textarea
                                                                path="metadataFormItemDefinitionVOs[${itemStat.index}].description"
                                                                cssClass="inputExpandOnFocus input325"
                                                                style="margin: 6px 5px; height: 17px;"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="padding-right: 4px; padding-bottom: 8px;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.type"/></td>
                                                    <td style="padding-right: 4px; padding-bottom: 8px;">
                                                        <form:select id="entryTypeSelect_${itemStat.index}"
                                                                     onchange="toggleItemType(${itemStat.index})"
                                                                     path="metadataFormItemDefinitionVOs[${itemStat.index}].type"
                                                                     items="${metadataFormItemTypes}" itemValue="id"
                                                                     itemLabel="name"
                                                                     cssClass="inputXL style_select"/>
                                                    </td>
                                                </tr>
                                                <tr id="primaryConnectorContainer_${itemStat.index}">
                                                    <td style="padding-right: 4px;" class="itemText">
                                                        <fmtSpring:message code="page.label.connector"/></td>
                                                    <td style="padding-right: 4px;">
                                                        <form:input
                                                                path="metadataFormItemDefinitionVOs[${itemStat.index}].primaryConnector"
                                                                cssClass="input325" cssStyle="margin: 6px 5px;"/>
                                                    </td>
                                                </tr>
                                                <tr id="displayCriteriaContainer_${itemStat.index}"
                                                    style="${not empty command.metadataFormItemDefinitionVOs[itemStat.index].parentOrder ? '' : 'display: none;'}">
                                                    <td style="padding-right: 4px;" class="itemText">
                                                        <fmtSpring:message code="page.label.criteria"/></td>
                                                    <td style="padding-right: 4px;">
                                                        <form:textarea id="displayCriteriaInput_${itemStat.index}"
                                                                       path="metadataFormItemDefinitionVOs[${itemStat.index}].displayCriteria"
                                                                       cssClass="inputExpandOnFocus input325"
                                                                       style="margin: 6px 5px; height: 17px;"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="padding-right: 4px;" class="itemText">
                                                        <fmtSpring:message code="page.label.mandatory"/></td>
                                                    <td style="padding-right: 4px;">
                                                        <form:checkbox
                                                                path="metadataFormItemDefinitionVOs[${itemStat.index}].isMandatory"
                                                                cssClass="checkbox" cssStyle="margin: 10px 5px;"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                        <!-- Detail: Secondary -->
                                        <td class="tableContentCol cellBottomRight" width="50%"
                                            style="border-bottom: none; border-left: 1px #bdbdbd solid; vertical-align: top;">

                                            <table id="typeDetailsContainer_${itemStat.index}" cellspacing="0"
                                                   cellpadding="0" border="0" class="innerCellTable"
                                                   style="display: none">
                                                <tr class="menuItemsContainer typeDetailValue">
                                                    <td style="padding-right: 30px; vertical-align: top;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.items"/></td>
                                                    <td style="padding-right: 4px;">
                                                        <table cellspacing="0" cellpadding="0" border="0"
                                                               class="innerCellTable">
                                                            <tr>
                                                                <td>
                                                                    <div id="menuItemsBindingsContainer_${itemStat.index}"
                                                                         style="border: 1px solid #bbb; border-radius: 4px; width: 350px; height: 125px; overflow: auto;">
                                                                        <div class="InfoSysContainer_info noMenuValueItemsMsg"
                                                                             style="margin: 8px; display: none;">
                                                                            <fmtSpring:message
                                                                                    code="page.text.no.menu.value.items"/>
                                                                        </div>
                                                                        <c:forEach var="currentMenuItemValue"
                                                                                   items="${command.metadataFormItemDefinitionVOs[itemStat.index].menuValueItems}">
                                                                            <div class="menuItemValueContainer">
                                                                                <c:out value="${currentMenuItemValue}"/>
                                                                                <input class="menuValueItemBinding"
                                                                                       type="hidden"
                                                                                       name="metadataFormItemDefinitionVOs[${itemStat.index}].menuValueItems"
                                                                                       value="${currentMenuItemValue}"/>
                                                                            </div>
                                                                        </c:forEach>
                                                                    </div>
                                                                </td>
                                                                <td style="vertical-align: top; padding-left: 6px;">
                                                                    <div class="addMenuValueItemBtn actionBtn_roundAll actionBtn fa-mp-container"
                                                                         style="white-space: nowrap; margin-bottom: 6px;">
                                                                        <i class="fa fa-plus fa-mp-btn detailTip"
                                                                           title="|<div class='detailTipText'>${msgpt:getMessage('page.label.add.menu.item')}</div>"></i>
                                                                    </div>
                                                                    <div class="removeMenuValueItemBtn actionBtn_roundAll actionBtn"
                                                                         style="white-space: nowrap; margin-bottom: 6px;">
                                                                        <i class="fa fa-minus fa-mp-btn detailTip"
                                                                           title="|<div class='detailTipText'>${msgpt:getMessage('page.label.remove.menu.item')}</div>"></i>
                                                                    </div>
                                                                    <div class="findMenuValueItemBtn actionBtn_roundAll actionBtn"
                                                                         style="white-space: nowrap;">
                                                                        <i class="fa fa-search fa-mp-btn detailTip"
                                                                           title="|<div class='detailTipText'>${msgpt:getMessage('page.label.find.menu.items')}</div>"></i>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr class="webServiceDetailsContainer typeDetailValue">
                                                    <td style="padding-right: 4px; padding-bottom: 4px;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.request"/></td>
                                                    <td style="padding-right: 4px; padding-bottom: 4px;">
                                                        <form:select id="webServiceRequest_${itemStat.index}"
                                                                     cssClass="style_select inputL webServiceRequest"
                                                                     items="${webServiceRefreshTypes}"
                                                                     itemLabel="name" itemValue="id"
                                                                     path="metadataFormItemDefinitionVOs[${itemStat.index}].webServiceRefreshTypeId"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr id="extendedPropertiesContainer_${itemStat.index}" style="display: none;">
                                        <td colspan="2" class="tableContentCol cellBottomRight"
                                            style="border-bottom: none; vertical-align: top; border-top: 1px solid #bdbdbd;">

                                            <table cellspacing="0" cellpadding="0" border="0"
                                                   class="innerCellTable">
                                                <tr id="fieldSizeContainer_${itemStat.index}"
                                                    style="display: none;">
                                                    <td style="padding-right: 4px; padding-bottom: 8px;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.size"/></td>
                                                    <td style="padding-right: 4px; padding-bottom: 8px;">
                                                        <form:select id="fieldSizeTypeSelect_${itemStat.index}"
                                                                     path="metadataFormItemDefinitionVOs[${itemStat.index}].fieldSizeTypeId"
                                                                     items="${metadataFormFieldSizeTypes}"
                                                                     itemValue="id" itemLabel="name"
                                                                     cssClass="inputXL style_select"/>
                                                    </td>
                                                </tr>
                                                <tr id="minLengthContainer_${itemStat.index}"
                                                    style="display: none;">
                                                    <td style="padding-right: 4px; padding-bottom: 8px;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.min.Length"/></td>
                                                    <td style="padding-right: 4px; padding-bottom: 8px;">
                                                        <msgpt:InputFilter type="numeric">
                                                            <form:input id="minLengthInput_${itemStat.index}"
                                                                        path="metadataFormItemDefinitionVOs[${itemStat.index}].minLength"
                                                                        cssClass="input3digit"
                                                                        cssStyle="margin: 6px 5px;"
                                                                        onkeyup="updateMinLength(this)"/>
                                                        </msgpt:InputFilter>
                                                    </td>
                                                </tr>
                                                <tr id="maxLengthContainer_${itemStat.index}"
                                                    style="display: none;">
                                                    <td style="padding-right: 4px; padding-bottom: 8px;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.max.Length"/></td>
                                                    <td style="padding-right: 4px; padding-bottom: 8px;">
                                                        <msgpt:InputFilter type="numeric">
                                                            <form:input id="maxLengthInput_${itemStat.index}"
                                                                        path="metadataFormItemDefinitionVOs[${itemStat.index}].maxLength"
                                                                        cssClass="input3digit"
                                                                        cssStyle="margin: 6px 5px;"
                                                                        onkeyup="updateMaxLength(this)"/>
                                                        </msgpt:InputFilter>
                                                        <c:set var="maxLength"
                                                               value="${metadataFormItemDefinitionVOs[itemStat.index].maxLength}"/>
                                                        <span id="maxLengthInfo_${itemStat.index}"
                                                              style="font-size: 10px; ${(not empty maxLength && maxLength > 0)?'':'display: none;' }">(<fmtSpring:message
                                                                code="page.text.unlimited"/>)</span>
                                                    </td>
                                                </tr>
                                                <tr id="singleSelectDefaultValueContainer_${itemStat.index}"
                                                    style="display: none;">
                                                    <td style="padding-right: 4px; padding-bottom: 8px;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.default.value"/></td>
                                                    <td style="padding-right: 4px; padding-bottom: 8px;">
                                                        <form:select id="singleSelectDefaultValue_${itemStat.index}"
                                                                     path="metadataFormItemDefinitionVOs[${itemStat.index}].singleSelectDefaultValue"
                                                                     cssClass="inputXL style_select">
                                                            <form:option value="-1"><fmtSpring:message
                                                                    code="page.text.select.no.default.style"/></form:option>
                                                            <c:if test="${not empty command.metadataFormItemDefinitionVOs[itemStat.index].menuValueItems}">
                                                                <c:forEach var="currentMenuItemValue"
                                                                           items="${command.metadataFormItemDefinitionVOs[itemStat.index].menuValueItems}"
                                                                           varStatus="forStatus">
                                                                    <form:option
                                                                            value="${forStatus.index}">${currentMenuItemValue}</form:option>
                                                                </c:forEach>
                                                            </c:if>
                                                        </form:select>
                                                    </td>
                                                </tr>
                                                <tr id="multiSelectDefaultValueContainer_${itemStat.index}"
                                                    style="display: none;">
                                                    <td style="padding-right: 4px; padding-bottom: 8px;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.default.value"/></td>
                                                    <td style="padding-right: 4px; padding-bottom: 8px;">
                                                        <div id="multiSelectDefaultValue_${itemStat.index}"
                                                             class="style_multiselect inputXL">
                                                            <c:forEach var="currentMenuItemValue"
                                                                       items="${command.metadataFormItemDefinitionVOs[itemStat.index].menuValueItems}"
                                                                       varStatus="forStatus">
                                                                <div id="multiSelectDefaultValueOption${itemStat.index}_${forStatus.index}">
                                                                    <fmtSpring:bind
                                                                            path="metadataFormItemDefinitionVOs[${itemStat.index}].multiSelectDefaultValues">
                                                                        <input name="metadataFormItemDefinitionVOs[${itemStat.index}].multiSelectDefaultValues"
                                                                               value="${forStatus.index}"
                                                                               type="checkbox"
                                                                            ${msgpt:contains( command.metadataFormItemDefinitionVOs[itemStat.index].multiSelectDefaultValues, forStatus.index) ? 'checked="checked"' : '' }
                                                                               class="style_multiselect_binding"/>
                                                                        <input type="hidden"
                                                                               name="_metadataFormItemDefinitionVOs[${itemStat.index}].multiSelectDefaultValues"/>
                                                                    </fmtSpring:bind>
                                                                    <span><c:out
                                                                            value="${currentMenuItemValue}"/></span>
                                                                </div>
                                                            </c:forEach>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr id="textDefaultValueContainer_${itemStat.index}">
                                                    <td style="padding-right: 4px; padding-bottom: 8px;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.default.value"/></td>
                                                    <td style="padding-right: 4px; padding-bottom: 8px;">
                                                        <form:input
                                                                path="metadataFormItemDefinitionVOs[${itemStat.index}].textDefaultValue"
                                                                cssClass="input325" cssStyle="margin: 6px 5px;"/>
                                                    </td>
                                                </tr>
                                                <tr id="dateDefaultValueContainer_${itemStat.index}">
                                                    <td style="padding-right: 4px; padding-bottom: 8px;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.default.value"/></td>
                                                    <td style="padding-right: 4px; padding-bottom: 8px;">
                                                        <form:checkbox id="defaultToTodayCheckbox_${itemStat.index}"
                                                                       path="metadataFormItemDefinitionVOs[${itemStat.index}].defaultDateValueToTodaysDate"
                                                                       cssClass="checkbox"
                                                                       cssStyle="margin: 10px 5px;"
                                                                       label="${msgpt:getMessage('page.label.todays.date')}"
                                                                       onchange="toggleDefaultToToday(${itemStat.index})"/>
                                                        <span id="defaultDateInput_${itemStat.index}"
                                                              style="margin-left: 10px; ${command.metadataFormItemDefinitionVOs[itemStat.index].defaultDateValueToTodaysDate?'display: none;':''}">
														 	<msgpt:Calendar
                                                                    path="metadataFormItemDefinitionVOs[${itemStat.index}].dateDefaultValue"
                                                                    viewableDateFormat="${viewableDateFormat}"/>
														</span>
                                                    </td>
                                                </tr>
                                                <tr id="monthDefaultValueContainer_${itemStat.index}">
                                                    <td style="padding-right: 4px; padding-bottom: 8px;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.default.value"/></td>
                                                    <td style="padding-right: 4px; padding-bottom: 8px;">
														<span id="defaultDateInput_${itemStat.index}"
                                                              style="margin-left: 5px;">
														 	<msgpt:Calendar
                                                                    path="metadataFormItemDefinitionVOs[${itemStat.index}].monthDefaultValue"
                                                                    type="popupMonth"/>
														</span>
                                                    </td>
                                                </tr>
                                                <tr id="uniqueValueContainer_${itemStat.index}">
                                                    <td style="padding-right: 4px; padding-bottom: 8px;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.unique.value"/></td>
                                                    <td style="padding-right: 4px; padding-bottom: 8px;">
                                                        <form:checkbox id="uniqueValueCheckbox_${itemStat.index}"
                                                                       path="metadataFormItemDefinitionVOs[${itemStat.index}].uniqueValue"
                                                                       cssClass="checkbox"
                                                                       cssStyle="margin: 10px 5px;"/>
                                                    </td>
                                                </tr>
                                                <tr id="fieldValidationContainer_${itemStat.index}"
                                                    style="display: none;">
                                                    <td style="padding-right: 4px; padding-bottom: 8px;"
                                                        class="itemText"><fmtSpring:message
                                                            code="page.label.validation"/></td>
                                                    <td style="padding-right: 4px; padding-bottom: 8px;">
                                                        <form:select
                                                                id="fieldValidationTypeSelect_${itemStat.index}"
                                                                path="metadataFormItemDefinitionVOs[${itemStat.index}].fieldValidationTypeId"
                                                                items="${metadataFormFieldValidationTypes}"
                                                                itemValue="id" itemLabel="name"
                                                                cssClass="inputXL style_select"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </c:forEach>
                        <!-- END: PERSISTED: ITEMS -->
                    </div>
                    <!-- TEMPLATE: ITEM -->
                    <div id="formDefinitionItemContainer_TEMPLATE" class="formDefinitionItemContainer"
                         style="display: none;">
                        <table width="100%" cellspacing="0" cellpadding="0" border="0" class="tableOutline">
                            <!-- Row: Item Order/Name -->
                            <tr>
                                <!-- Order -->
                                <td id="itemOrderContainer_INDEX_ID" width="40px"
                                    class="tableIndicatorCol cellTopLeft">
                                    <div class="itemIndicator">
                                        <span id="itemOrderDisplay_INDEX_ID">0</span>
                                        <input type="hidden" id="itemOrderInput_INDEX_ID"
                                               name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].order"
                                               class="input1digit"/>
                                        <input type="hidden" id="itemActionInput_INDEX_ID"
                                               name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].action"
                                               class="input1digit"/>
                                        <input type="hidden" id="itemParentInput_INDEX_ID"
                                               name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].parentOrder"
                                               class="input1digit"/>
                                        <input type="hidden" id="itemGUID_INDEX_ID" value=""/>
                                        <input type="hidden" id="itemParentGUID_INDEX_ID" value=""/>
                                    </div>
                                </td>
                                <!-- Name -->
                                <td colspan="2" class="tableContentCol cellTopRight" style="white-space: nowrap;">
                                    <div style="display: inline-block;">
                                        <input type="text"
                                               name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].name"
                                               id="itemName_INDEX_ID" class="inputExtended simpleName"
                                               style="width: 825px;"/>
                                    </div>
                                    <c:if test="${command.type == 7}">
                                        <div style="display: inline-block;">
                                            <div class="mandatoryCreateOnImportBtn actionBtn_roundAll actionBtn"
                                                 style="white-space: nowrap; margin-bottom: 6px;">
                                                <i class="fa fa-exclamation fa-mp-btn detailTip"
                                                   title="|<div class='detailTipText'>${msgpt:getMessage('page.text.manadatory.create.on.import')}</div>"></i>
                                                <input id="mandatoryCreateOnImportCheckbox_INDEX_ID"
                                                       name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].autoGenerateOnImport"
                                                       type="checkbox" style="display: none;"/>
                                            </div>
                                        </div>
                                    </c:if>
                                </td>
                            </tr>
                            <!-- Row: Item Actions/Details -->
                            <tr>
                                <!-- Item Actions -->
                                <td rowspan="2" class="tableIndicatorCol cellBottomLeft"
                                    style="border-bottom: none; position: relative;">

                                    <table id="itemControlTable_INDEX_ID" width="100%" cellspacing="0"
                                           cellpadding="0" border="0" class="innerCellTable" style="display:none;">
                                        <tr>
                                            <td colspan="3" align="center" class="fa-mp-container"><i
                                                    id="upButton_INDEX_ID"
                                                    class="fa fa-caret-up fa-mp-btn fa-lg detailTip"
                                                    title="|<div class='detailTipText'>${msgpt:getMessage('page.label.move.up')}</div>"
                                                    style="margin-left: 1px;"></i></td>
                                        </tr>
                                        <tr>
                                            <td width="1%" align="center" class="fa-mp-container"><i
                                                    id="promoteButton_INDEX_ID"
                                                    class="fa fa-caret-left fa-mp-btn fa-lg detailTip"
                                                    title="|<div class='detailTipText'>${msgpt:getMessage('page.label.promote')}</div>"
                                                    style="padding-left: 4px; position: relative; top: 1px; display: none;"></i>
                                            </td>
                                            <td></td>
                                            <td width="1%" align="center" class="fa-mp-container"><i
                                                    id="nestButton_INDEX_ID"
                                                    class="fa fa-caret-right fa-mp-btn fa-lg detailTip"
                                                    title="|<div class='detailTipText'>${msgpt:getMessage('page.label.nest')}</div>"
                                                    style="padding-right: 4px; position: relative; top: 1px; display: none;"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="3" align="center" class="fa-mp-container"><i
                                                    id="downButton_INDEX_ID"
                                                    class="fa fa-caret-down fa-mp-btn fa-lg detailTip"
                                                    title="|<div class='detailTipText'>${msgpt:getMessage('page.label.move.down')}</div>"
                                                    style="margin-left: 1px;"></i></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3" align="center" class="fa-mp-container"
                                                style="padding-top: 12px;"><i id="deleteButton_INDEX_ID"
                                                                              class="far fa-times fa-mp-btn fa-lg detailTip"
                                                                              title="|<div class='detailTipText'>${msgpt:getMessage('page.label.remove.item.plus.children')}</div>"
                                                                              style=""></i></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3" align="center" class="fa-mp-container"
                                                style="padding-top: 12px;">
												<span id="insertChildButton_INDEX_ID"
                                                      class="fa-stack insertChildButton detailTip"
                                                      title="|<div class='detailTipText'>${msgpt:getMessage('page.label.add.child')}</div>">
													<i class="fa fa-reply fa-mp-btn fa-stack-1x"></i>
													<i class="fa fa-plus fa-mp-btn fa-stack-1x"></i>
												</span>
                                            </td>
                                        </tr>
                                    </table>

                                    <div id="extendedPropertiesToggle_INDEX_ID"
                                         class="extendedPropertiesToggle fa-mp-container">
                                        <i class="fa fa-plus-square fa-mp-btn"></i>
                                    </div>

                                </td>
                                <!-- Detail: Primary -->
                                <td class="tableContentCol" style="border-bottom: none; vertical-align: top;">

                                    <table cellspacing="0" cellpadding="0" border="0" class="innerCellTable">
                                        <tr>
                                            <td style="padding-right: 4px;" class="itemText"><fmtSpring:message
                                                    code="page.label.description"/></td>
                                            <td style="padding-right: 4px;">
                                                    <textarea
                                                            name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].description"
                                                            class="inputExpandOnFocus input325"
                                                            style="margin: 6px 5px; height: 17px;"></textarea>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding-right: 4px; padding-bottom: 8px;" class="itemText">
                                                <fmtSpring:message code="page.label.type"/></td>
                                            <td style="padding-right: 4px; padding-bottom: 8px;">
                                                <select id="entryTypeSelect_INDEX_ID"
                                                        onchange="toggleItemType(INDEX_ID)"
                                                        name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].type"
                                                        class="inputXL style_select">
                                                    <c:forEach var="currentType" items="${metadataFormItemTypes}">
                                                        <option value="${currentType.id}"><c:out
                                                                value="${currentType.name}"/></option>
                                                    </c:forEach>
                                                </select>
                                            </td>
                                        </tr>
                                        <tr id="primaryConnectorContainer_INDEX_ID">
                                            <td style="padding-right: 4px;" class="itemText"><fmtSpring:message
                                                    code="page.label.connector"/></td>
                                            <td style="padding-right: 4px;">
                                                <input type="text"
                                                       name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].primaryConnector"
                                                       class="input325" style="margin: 8px 5px;"/>
                                            </td>
                                        </tr>
                                        <tr id="displayCriteriaContainer_INDEX_ID" style="display: none">
                                            <td style="padding-right: 4px;" class="itemText"><fmtSpring:message
                                                    code="page.label.criteria"/></td>
                                            <td style="padding-right: 4px;">
                                                    <textarea id="displayCriteriaInput_INDEX_ID"
                                                              name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].displayCriteria"
                                                              class="inputExpandOnFocus input325"
                                                              style="margin: 6px 5px; height: 17px;"></textarea>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding-right: 4px;" class="itemText"><fmtSpring:message
                                                    code="page.label.mandatory"/></td>
                                            <td style="padding-right: 4px;">
                                                <input type="checkbox"
                                                       name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].isMandatory"
                                                       class="checkbox" style="margin: 10px 5px;"/>
                                            </td>
                                        </tr>
                                    </table>

                                </td>
                                <!-- Detail: Secondary -->
                                <td class="tableContentCol cellBottomRight" width="50%"
                                    style="border-bottom: none; border-left: 1px #bdbdbd solid; vertical-align: top;">

                                    <table id="typeDetailsContainer_INDEX_ID" cellspacing="0" cellpadding="0"
                                           border="0" class="innerCellTable" style="display:none;">
                                        <tr class="menuItemsContainer typeDetailValue">
                                            <td style="padding-right: 4px; vertical-align: top;" class="itemText">
                                                <fmtSpring:message code="page.label.items"/></td>
                                            <td style="padding-right: 4px;">
                                                <table cellspacing="0" cellpadding="0" border="0"
                                                       class="innerCellTable">
                                                    <tr>
                                                        <td>
                                                            <div id="menuItemsBindingsContainer_INDEX_ID"
                                                                 style="border: 1px solid #bbb; border-radius: 4px; width: 350px; height: 125px; overflow: auto;">
                                                                <div class="InfoSysContainer_info noMenuValueItemsMsg"
                                                                     style="margin: 8px; display: none;">
                                                                    <fmtSpring:message
                                                                            code="page.text.no.menu.value.items"/>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td style="vertical-align: top; padding-left: 6px;">
                                                            <div class="addMenuValueItemBtn actionBtn_roundAll actionBtn"
                                                                 style="white-space: nowrap; margin-bottom: 6px;">
                                                                <i class="fa fa-plus fa-mp-btn detailTip"
                                                                   title="|<div class='detailTipText'>${msgpt:getMessage('page.label.add.menu.item')}</div>"></i>
                                                            </div>
                                                            <div class="removeMenuValueItemBtn actionBtn_roundAll actionBtn"
                                                                 style="white-space: nowrap;">
                                                                <i class="fa fa-minus fa-mp-btn detailTip"
                                                                   title="|<div class='detailTipText'>${msgpt:getMessage('page.label.remove.menu.item')}</div>"></i>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr class="webServiceDetailsContainer typeDetailValue">
                                            <td style="padding-right: 4px; padding-bottom: 4px;" class="itemText">
                                                <fmtSpring:message code="page.label.request"/></td>
                                            <td style="padding-right: 4px; padding-bottom: 4px;">
                                                <select id="webServiceRequest_INDEX_ID"
                                                        class="style_select inputL webServiceRequest"
                                                        name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].webServiceRefreshTypeId">
                                                    <c:forEach var="currentRefreshType"
                                                               items="${webServiceRefreshTypes}">
                                                        <option id="webServiceRequest_INDEX_ID_${currentRefreshType.id}"
                                                                value="${currentRefreshType.id}"><c:out
                                                                value="${currentRefreshType.name}"/></option>
                                                    </c:forEach>
                                                </select>
                                            </td>
                                        </tr>
                                    </table>

                                </td>
                            </tr>
                            <tr id="extendedPropertiesContainer_INDEX_ID" style="display: none;">
                                <td colspan="2" class="tableContentCol cellBottomRight"
                                    style="border-bottom: none; vertical-align: top; border-top: 1px solid #bdbdbd;">
                                    <table cellspacing="0" cellpadding="0" border="0" class="innerCellTable">
                                        <tr id="fieldSizeContainer_INDEX_ID" style="display: none;">
                                            <td style="padding-right: 4px; padding-bottom: 8px;" class="itemText">
                                                <fmtSpring:message code="page.label.size"/></td>
                                            <td style="padding-right: 4px; padding-bottom: 8px;">
                                                <select id="fieldSizeTypeSelect_INDEX_ID"
                                                        onchange="toggleItemType(INDEX_ID)"
                                                        name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].fieldSizeTypeId"
                                                        class="inputXL style_select">
                                                    <c:forEach var="currentType"
                                                               items="${metadataFormFieldSizeTypes}">
                                                        <option value="${currentType.id}" ${currentType.id == 5?'selected':''}>
                                                            <c:out value="${currentType.name}"/></option>
                                                    </c:forEach>
                                                </select>
                                            </td>
                                        </tr>
                                        <tr id="minLengthContainer_INDEX_ID" style="display: none;">
                                            <td style="padding-right: 4px;" class="itemText"><fmtSpring:message
                                                    code="page.label.min.Length"/></td>
                                            <td style="padding-right: 4px;">
                                                <input id="minLengthInput_INDEX_ID" type="text"
                                                       name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].minLength"
                                                       class="numeric input3digit" style="margin: 6px 5px;"
                                                       onkeyup="updateMinLength(this)"/>
                                            </td>
                                        </tr>
                                        <tr id="maxLengthContainer_INDEX_ID" style="display: none;">
                                            <td style="padding-right: 4px;" class="itemText"><fmtSpring:message
                                                    code="page.label.max.Length"/></td>
                                            <td style="padding-right: 4px;">
                                                <input id="maxLengthInput_INDEX_ID" type="text"
                                                       name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].maxLength"
                                                       class="numeric input3digit" style="margin: 6px 5px;"
                                                       onkeyup="updateMaxLength(this)"/>
                                                <span id="maxLengthInfo_INDEX_ID"
                                                      style="font-size: 10px;">(<fmtSpring:message
                                                        code="page.text.unlimited"/>)</span>
                                            </td>
                                        </tr>
                                        <tr id="singleSelectDefaultValueContainer_INDEX_ID" style="display: none;">
                                            <td style="padding-right: 4px; padding-bottom: 8px;" class="itemText">
                                                <fmtSpring:message code="page.label.default.value"/></td>
                                            <td style="padding-right: 4px; padding-bottom: 8px;">
                                                <select id="singleSelectDefaultValue_INDEX_ID"
                                                        name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].singleSelectDefaultValue"
                                                        class="inputXL style_select">
                                                    <option value="-1"><fmtSpring:message
                                                            code="page.text.select.no.default.style"/></option>
                                                </select>
                                            </td>
                                        </tr>
                                        <tr id="multiSelectDefaultValueContainer_INDEX_ID" style="display: none;">
                                            <td style="padding-right: 4px; padding-bottom: 8px;" class="itemText">
                                                <fmtSpring:message code="page.label.default.value"/></td>
                                            <td style="padding-right: 4px; padding-bottom: 8px;">
                                                <div id="multiSelectDefaultValue_INDEX_ID"
                                                     class="style_multiselect inputXL">
                                                </div>
                                            </td>
                                        </tr>
                                        <tr id="textDefaultValueContainer_INDEX_ID" style="display: none;">
                                            <td style="padding-right: 4px; padding-bottom: 8px;" class="itemText">
                                                <fmtSpring:message code="page.label.default.value"/></td>
                                            <td style="padding-right: 4px; padding-bottom: 8px;">
                                                <input type="text"
                                                       name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].textDefaultValue"
                                                       class="input325" style="margin: 8px 5px;"/>
                                            </td>
                                        </tr>
                                        <tr id="dateDefaultValueContainer_INDEX_ID">
                                            <td style="padding-right: 4px; padding-bottom: 8px;" class="itemText">
                                                <fmtSpring:message code="page.label.default.value"/></td>
                                            <td style="padding-right: 4px; padding-bottom: 8px;">
                                                <input id="defaultToTodayCheckbox_INDEX_ID" type="checkbox"
                                                       name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].defaultDateValueToTodaysDate"
                                                       class="checkbox" style="margin: 10px 5px;"
                                                       onchange="toggleDefaultToToday(INDEX_ID)"/>
                                                <label for="defaultToTodayCheckbox_INDEX_ID"><fmtSpring:message
                                                        code="page.label.todays.date"/></label>
                                                <span id="defaultDateInput_INDEX_ID" style="margin-left: 10px;">
													<input name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].dateDefaultValue"
                                                           class="newDateBinding" type="hidden">
													<input class="newDateInput inputDate"/>
													<a href="#" class="newDateInfo infoIcon" style="display: none;"
                                                       title="<div align=left class=clueTipTitle><fmtSpring:message code="page.label.date.selection"/></div>|<div align=left class=clueTipContent><fmtSpring:message code="page.text.select.date"/></div>"><!--NULL--></a>
												</span>
                                            </td>
                                        </tr>
                                        <tr id="monthDefaultValueContainer_INDEX_ID">
                                            <td style="padding-right: 4px; padding-bottom: 8px;" class="itemText">
                                                <fmtSpring:message code="page.label.default.value"/></td>
                                            <td style="padding-right: 4px; padding-bottom: 8px;">
												<span id="defaultDateInput_INDEX_ID" style="margin-left: 5px;">
													<input name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].monthDefaultValue"
                                                           class="newMonthBinding" type="hidden">
													<input class="newMonthInput inputDate"/>
													<a href="#" class="newMonthInfo infoIcon" style="display: none;"
                                                       title="<div align=left class=clueTipTitle><fmtSpring:message code="page.label.date.selection"/></div>|<div align=left class=clueTipContent><fmtSpring:message code="page.text.select.date"/></div>"><!--NULL--></a>
												</span>
                                            </td>
                                        </tr>
                                        <tr id="uniqueValueContainer_INDEX_ID" style="display: none;">
                                            <td style="padding-right: 4px;" class="itemText"><fmtSpring:message
                                                    code="page.label.unique.value"/></td>
                                            <td style="padding-right: 4px;">
                                                <input type="checkbox"
                                                       name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].uniqueValue"
                                                       class="checkbox" style="margin: 10px 5px;"/>
                                            </td>
                                        </tr>
                                        <tr id="fieldValidationContainer_INDEX_ID" style="display: none;">
                                            <td style="padding-right: 4px; padding-bottom: 8px;" class="itemText">
                                                <fmtSpring:message code="page.label.validation"/></td>
                                            <td style="padding-right: 4px; padding-bottom: 8px;">
                                                <select id="fieldValidationTypeSelect_INDEX_ID"
                                                        onchange="toggleItemType(INDEX_ID)"
                                                        name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].fieldValidationTypeId"
                                                        class="inputXL style_select">
                                                    <c:forEach var="currentType"
                                                               items="${metadataFormFieldValidationTypes}">
                                                        <option value="${currentType.id}"><c:out
                                                                value="${currentType.name}"/></option>
                                                    </c:forEach>
                                                </select>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <!-- END: TEMPLATE: ITEM -->
                    <!-- TEMPLATE: CONNECTOR -->
                    <table style="display: none;">
                        <tr class="webServiceDataConnectionContainer_TEMPLATE" style="display: none;">
                            <td width="1%" style="white-space: nowrap;">
                                <input type="text"
                                       name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].connectorNames"
                                       class="input185 connectorName"/>
                            </td>
                            <td>
                                <select id="connectedDataElementSelect_INDEX_ID_CDE_COUNT"
                                        name="BIND_MASKmetadataFormItemDefinitionVOs[INDEX_ID].connectorDataElements"
                                        class="input160 connectorDataElementsSelect">
                                    <c:forEach var="currentElement" items="${connectedDataElements}">
                                        <option value="${currentElement.id}"><c:out
                                                value="${currentElement.name}"/></option>
                                    </c:forEach>
                                </select>
                            </td>
                            <td width="1%" style="padding-right: 8px;">
                                <div class="removeMenuValueItemBtn actionBtn_roundAll actionBtn fa-mp-container"
                                     style="white-space: nowrap;">
                                    <i class="fa fa-minus fa-mp-btn fa-lg detailTip"
                                       title="|<div class='detailTipText'>${msgpt:getMessage('page.label.remove.connection')}</div>"
                                       style="margin: 6px 6px 0px 6px; font-size: 15px;"></i>
                                </div>
                            </td>
                        </tr>
                    </table>
                    <!-- END: TEMPLATE: CONNECTOR -->
                </div>
            </div>
            <div class="pddng-lv2 pddng-horizontal-lv3 align-content-right">
                <div class="inline-block-item mrgn-right-lv2">
                    <msgpt:Button label="page.label.cancel" URL="javascript:closeIframe();" flowControl="true"/>
                </div>
                <div class="inline-block-item mrgn-right-lv2">
                    <div class="btn btn-primary buttonTagBtn" onclick="addAction();">
                        <div class="btnText">
                            <i class="fa fa-mp-button-icon fa-plus-circle">&nbsp;</i>
                            <a id="link_Cancel" style="white-space: nowrap;" href="#"><fmtSpring:message
                                    code="page.label.add.item"/></a>
                        </div>
                    </div>
                </div>
                <div class="inline-block-item">
                    <msgpt:Button label="page.label.save" URL="javascript:doSubmit('${submitType}');" flowControl="true"
                                  primary="true" icon="fa-save"/>
                </div>
            </div>
        </form:form>
    </msgpt:BodyNew>
</msgpt:Html5>