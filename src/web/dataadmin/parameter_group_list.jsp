<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.data" viewType="<%=MessagepointHeader.ViewType.EDIT %>">

        <msgpt:Script>
            <script>

                // *********  INIT: START  *********
                var iFramePopup_fullFrameAttr_cust = JSON.parse(JSON.stringify(iFramePopup_fullFrameAttr));
                iFramePopup_fullFrameAttr_cust.width = 650;

                $(function () {
                    $('#addConSelGrpBtn').iFramePopup($.extend({
                        src: context + "/dataadmin/parameter_group_edit.form",
                        appliedParams: {tk: "${param.tk}"},
                        beforePopup: function (inst) {
                            if ($('#addConSelGrpBtn').hasClass('highlightedBtnDisabled'))
                                return false;
                        },
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr_cust));
                });

                // List actions: Edit
                function editListItem(listItemId) {
                    $('#listItem_' + listItemId).iFramePopup($.extend({
                        src: context + "/dataadmin/parameter_group_edit.form",
                        displayOnInit: true,
                        appliedParams: {'tk': "${param.tk}", 'paramgrpid': listItemId},
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr_cust));
                }
            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <msgpt:BodyNew>
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_DATA_ADMIN %>"/>
        <msgpt:LowerContainer fullPanel="true">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel>
                <h1 class="h4 pb-2 mb-4"><fmtSpring:message code="page.label.parameter.groups"/></h1>
                <div class="box-shadow-4 rounded bg-white p-4" style="min-height: 35rem;">
                    <div class="px-2 py-1">
                        <msgpt:IfAuthGranted authority="ROLE_TOUCHPOINT_DATA_EDIT">
                            <div class="mb-4 pb-1">
                                <button id="addConSelGrpBtn" class="btn btn-primary" type="button">
                                    <i class="far fa-plus-circle mr-2" aria-hidden="true"></i>
                                    <fmtSpring:message code="page.label.add.content.selector.group"/>
                                </button>
                            </div>
                        </msgpt:IfAuthGranted>
                        <c:choose>
                            <c:when test="${empty paramgroups}">
                                <div class="alert alert-info p-2 text-center" role="alert">
                                    <strong class="mr-2">
                                        <i class="fas fa-info-circle fa-lg mr-2"
                                           aria-hidden="true"></i><fmtSpring:message
                                            code="page.label.info"/>:
                                    </strong>
                                    <fmtSpring:message code="page.text.parameter.noparametergroups"/>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <msgpt:CalculatePageNavigation itemsName="paramgroups" page="${param.page}"
                                                               pageSize="4"
                                                               maxPages="${param.maxpages}"
                                                               outputName="parameters"
                                                               className="com.prinova.messagepoint.model.admin.ParameterGroup"/>
                                <!-- Pagination -->
                                <msgpt:OutputPageNavigation page="${param.page}" pageSize="4"
                                                            maxPages="${param.maxpages}">
                                    <msgpt:URLBuilder page="/dataadmin/parameter_group_list.form"/>
                                </msgpt:OutputPageNavigation>
                                <msgpt:DataTable staticData="true">
                                    <c:forEach var="paramGroup" items="${parameters}"
                                               varStatus="parameterLoopStatus">
                                        <msgpt:TableListGroup>
                                            <!-- Name -->
                                            <msgpt:TableElement label="page.label.name" align="left">
                                                <c:choose>
                                                    <c:when test="${userHasEditPermission}">
                                                        <a id="listItem_${paramGroup.id}"
                                                           href="javascript:editListItem(${paramGroup.id});">
                                                            <c:out value="${paramGroup.name}"/>
                                                        </a>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <c:out value="${paramGroup.name}"/>
                                                    </c:otherwise>
                                                </c:choose>
                                            </msgpt:TableElement>
                                            <!-- parameter Names -->
                                            <msgpt:TableElement label="page.label.parameters" align="left">
                                                <c:out value="${paramGroup.allParameterNames}"/>
                                            </msgpt:TableElement>
                                            <!-- Delete -->
                                            <c:if test="${userHasEditPermission}">
                                                <msgpt:TableElement label="&nbsp;">
                                                    <c:choose>
                                                        <c:when test="${!paramGroup.referenced}">
                                                            <a role="button" data-toggle="tooltip"
                                                               title="${msgpt:getMessage('page.label.delete')}"
                                                               href="parameter_group_delete.form?parameter=id&id=${paramGroup.id}">
                                                                <i class="far fa-trash-alt table-icon" aria-hidden="true"></i>
                                                                <span class="sr-only"><fmtSpring:message
                                                                        code="page.label.delete"/></span>
                                                            </a>
                                                        </c:when>
                                                        <c:otherwise>
                                                            &nbsp;
                                                        </c:otherwise>
                                                    </c:choose>
                                                </msgpt:TableElement>
                                            </c:if>
                                        </msgpt:TableListGroup>
                                    </c:forEach>
                                </msgpt:DataTable>
                                <!-- Pagination -->
                                <msgpt:OutputPageNavigation page="${param.page}" pageSize="4"
                                                            maxPages="${param.maxpages}">
                                    <msgpt:URLBuilder page="/dataadmin/parameter_group_list.form"/>
                                </msgpt:OutputPageNavigation>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
    </msgpt:BodyNew>
</msgpt:Html5>