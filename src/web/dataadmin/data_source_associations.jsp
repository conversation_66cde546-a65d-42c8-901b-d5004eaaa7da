<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.data.source.associations" viewType="<%= MessagepointHeader.ViewType.EDIT %>">


        <msgpt:Script>
            <script>

                // *********  INIT: START  *********
                $(function () {
                    $('#addDataAssoBtn').iFramePopup($.extend({
                        src: context + "/dataadmin/data_source_association_edit.form",
                        appliedParams: {tk: "${param.tk}"},
                        beforePopup: function (inst) {
                            if ($('#addDataAssoBtn').hasClass('highlightedBtnDisabled'))
                                return false;
                        },
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr));
                });

                // List actions: Edit
                function editListItem(listItemId) {
                    $('#listItem_' + listItemId).iFramePopup($.extend({
                        src: context + "/dataadmin/data_source_association_edit.form",
                        displayOnInit: true,
                        appliedParams: {'tk': "${param.tk}", 'associationid': listItemId},
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr));
                }
            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <msgpt:GetPagedHibernateObjects hqlFromQuery="from com.prinova.messagepoint.model.admin.DataSourceAssociation"
                                    orderBy=" order by name" outputName="dataSourceAssociations" page="${param.page}"
                                    pageSize="4" maxPages="${param.maxpages}" scope="request"/>

    <msgpt:BodyNew>
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_DATA_ADMIN %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_DATA_ADMIN %>"/>
        <msgpt:LowerContainer>
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel>
                <h1 class="h4 pb-2 mb-4"><fmtSpring:message code="page.label.data.source.associations"/></h1>
                <div class="box-shadow-4 rounded bg-white p-4" style="min-height: 35rem;">
                    <div class="px-2 py-1">
                        <msgpt:IfAuthGranted authority="ROLE_TOUCHPOINT_DATA_EDIT">
                            <div class="mb-4 pb-1">
                                <button id="addDataAssoBtn" class="btn btn-primary" type="button">
                                    <i class="far fa-plus-circle mr-2" aria-hidden="true"></i>
                                    <c:out value='${msgpt:getMessage("page.label.add.data.collection")}'/>
                                </button>
                            </div>
                        </msgpt:IfAuthGranted>
                        <c:choose>
                            <c:when test="${empty dataSourceAssociations}">
                                <div class="alert alert-info p-2 text-center" role="alert">
                                    <strong class="mr-2">
                                        <i class="fas fa-info-circle fa-lg mr-2"
                                           aria-hidden="true"></i><fmtSpring:message
                                            code="page.label.info"/>:
                                    </strong>
                                    <c:out value='${msgpt:getMessage("page.text.no.data.source.associations")}'/>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <!-- Pagination -->
                                <msgpt:OutputPageNavigation page="${param.page}" pageSize="${param.pagesize}"
                                                            maxPages="${param.maxpages}">
                                    <msgpt:URLBuilder page="/dataadmin/data_source_associations.jsp"/>
                                </msgpt:OutputPageNavigation>
                                <msgpt:DataTable staticData="true" id="dataSourceAssociations">
                                    <c:forEach var="dataSourceAssociation" items="${dataSourceAssociations}"
                                               varStatus="status">
                                        <msgpt:TableListGroup>
                                            <!-- Name -->
                                            <msgpt:TableElement align="left" label="page.label.name">
                                                <msgpt:IfAuthGranted authority="ROLE_TOUCHPOINT_DATA_EDIT">
                                                    <c:if test="${dataSourceAssociation.updatable}">
                                                        <a id="listItem_${dataSourceAssociation.id}"
                                                           href="javascript:editListItem(${dataSourceAssociation.id});">
                                                            <c:out value="${dataSourceAssociation.name}"/>
                                                        </a>
                                                    </c:if>
                                                    <c:if test="${!dataSourceAssociation.updatable}">
                                                        <c:out value="${dataSourceAssociation.name}"/>
                                                    </c:if>
                                                </msgpt:IfAuthGranted>
                                                <msgpt:IfAuthGranted elseStatement="true"
                                                                     authority="ROLE_TOUCHPOINT_DATA_EDIT">
                                                    <c:out value="${dataSourceAssociation.name}"/>
                                                </msgpt:IfAuthGranted>
                                            </msgpt:TableElement>
                                            <!-- Primary Data Source -->
                                            <msgpt:TableElement label="page.label.primary.data.source" align="left">
                                                <c:out value="${dataSourceAssociation.primaryDataSource.name}"/>
                                            </msgpt:TableElement>
                                            <!-- Reference Associations -->
                                            <msgpt:TableElement label="page.label.reference.data" align="left">
                                                <c:out value="${fn:length(dataSourceAssociation.referenceConnections)}"/>
                                            </msgpt:TableElement>
                                            <!-- Touchpoints -->
                                            <msgpt:TableElement label="page.label.touchpoints" align="left">
                                                <c:out value="${fn:length(dataSourceAssociation.documents)}"/>
                                            </msgpt:TableElement>
                                        </msgpt:TableListGroup>
                                    </c:forEach>
                                </msgpt:DataTable>
                                <!-- Pagination -->
                                <msgpt:OutputPageNavigation page="${param.page}" pageSize="${param.pagesize}"
                                                            maxPages="${param.maxpages}">
                                    <msgpt:URLBuilder page="/dataadmin/data_source_associations.jsp"/>
                                </msgpt:OutputPageNavigation>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
    </msgpt:BodyNew>
</msgpt:Html5>