
<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.util.ConditionElementUtil" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.target" viewType="<%= MessagepointHeader.ViewType.EDIT %>">


        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>

        <msgpt:CalendarIncludes/>

        <msgpt:Script src="includes/javascript/checkboxSelect.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iFramePopup/jquery.iFramePopup.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/dragAndDropSelect/jquery.dragAndDropSelect.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/dragAndDropSelect/jquery.dragAndDropSelect.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/ruleBuilder/jquery.ruleValueManager.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/ruleBuilder/ruleBuilder.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

        <msgpt:Stylesheet href="includes/themes/commoncss/targeting.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/taskManager/jquery.taskManager.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/taskManager/taskManager.css"/>

        <msgpt:Script src="_ux/js/mp.stickyBar.js"/>
        <msgpt:Script src="_ux/js/mp.postTrigger.js"/>
        <msgpt:Script src="_ux/js/mp.toggleSwitch.js"/>
        <msgpt:Script src="_ux/js/mp.complexCombobox.js"/>
        <msgpt:Script src="_ux/js/mp.dragAndDropList.js"/>

        <style type="text/css">
            .stageContainer table.tableOutline {
                -moz-box-shadow: none;
                -webkit-box-shadow: none;
                box-shadow: none;
            }
        </style>

        <c:if test="${empty param.nprSaveSuccess}">
            <msgpt:Script>
                <script>

                    function asyncGetRuleItems() {

                        var searchValue = $('#ruleSearchInput').val();
                        if (searchValue == defaultSearchText)
                            searchValue = "";

                        $('#ruleListContainerNone').hide();
                        $('.ruleListContainer').html('');
                        $('#ruleListContainerLoading').show();

                        var stampDate = new Date();
                        // Async Request
                        $.ajax({
                            type: "GET",
                            url: context + "/getRules.form?type=rulesList&numCap=500&sSearch=" + encodeURIComponent(escapeHtmlEntities(searchValue)) + "&documentId=${documentId}&advanced=" + $('#advancedSearchIcon').is('.advancedSearchIconDiv_active') + "&cacheStamp=" + stampDate.getTime(),
                            dataType: "json",
                            success: function (data) {
                                processRuleItems(data);
                            }
                        });
                    }

                    function initParameterizeToggle(ruleId) {
                        if ($('#parameterizeRuleBinding_' + ruleId).is(':visible') && $('#parameterizeRuleToggleContainer_' + ruleId).find('.ibutton-container').length == 0)
                            $('#parameterizeRuleBinding_' + ruleId).iButton({
                                labelOn: $('#parameterizeRuleBinding_' + ruleId).attr('title').split(';')[1],
                                labelOff: $('#parameterizeRuleBinding_' + ruleId).attr('title').split(';')[0],
                                resizeHandle: false,
                                resizeContainer: "auto",
                                change: function (checkEle) {
                                    toggleRuleParameterization(ruleId, checkEle);
                                }
                            });
                    }

                    function initRuleActionControls(ele) {

                        // No options to edit: Don't show action controls
                        if ($(ele).find('.conditionBinding ').length <= 1 && $(ele).find("[id^='parameterizeRuleBinding_']").length == 0)
                            $(ele).find('.ruleEditButtonContainer,.ruleDoneButtonContainer').remove();

                        $(ele).find('table:first')
                            .mouseover(function () {
                                $(this).find('.ruleActionsContainer').show();
                            })
                            .mouseout(function () {
                                if ($(this).find('.ruleEditButtonContainer:visible').length != 0 || $(this).find('.ruleEditButtonContainer').length == 0)
                                    $(this).find('.ruleActionsContainer').hide();
                            });

                        $(ele).find('.removeIcon').click(function () {
                            $(this).closest('.ruleDisplayContainer').next('.ruleRelationshipIndicator').remove();
                            $(this).closest('.ruleDisplayContainer').remove();
                            toggleRuleRelationships();
                            setDisabledRuleTokens();
                            toggleRuleRelationshipToggle();
                            toggleNoRuleInfoDisplay();
                            validateRulesSize();
                            common.refreshParentIframeHeight();
                        });

                        $(ele).find('.ruleActionBtn').click(function () {
                            toggleRuleEdit(this);
                        });
                    }

                    function processRuleDefinition(data, use) {

                        var ruleTemplate = $('#TEMPLATE_RULE').clone();

                        if (!data.can_be_parameterized)
                            $(ruleTemplate).find('.ruleCanBeParameterized').remove();

                        if (!(data.conditions.length > 1 || data.can_be_parameterized)) {
                            $(ruleTemplate).find('.ruleEditButtonContainer').remove();
                            $(ruleTemplate).find('.ruleDoneButtonContainer').remove();
                        }

                        var ruleTemplateStr = $($('<div>').append(ruleTemplate)).html();
                        ruleTemplateStr = ruleTemplateStr.replace(/RULE_ID/g, data.id);
                        ruleTemplateStr = ruleTemplateStr.replace(/RULE_NAME/g, data.name);
                        ruleTemplateStr = ruleTemplateStr.replace(/BIND_MASK/g, "");

                        var newRuleEle = $(ruleTemplateStr);

                        for (var i = 0; i < data.conditions.length; i++) {

                            var conditionTemplate = $('#TEMPLATE_CONDITION').clone();

                            if (data.conditions[i].is_parameterized)
                                $(conditionTemplate).find('.ruleConditionStaticContainer').remove();
                            else
                                $(conditionTemplate).find('.ruleConditionValueContainer').remove();

                            var conditionTemplateStr = $($('<div>').append(conditionTemplate)).html();
                            conditionTemplateStr = conditionTemplateStr.replace(/RULE_ID/g, data.id);
                            conditionTemplateStr = conditionTemplateStr.replace(/RADIO_OR_CHECKBOX/g, data.select_type);
                            conditionTemplateStr = conditionTemplateStr.replace(/CONDITION_ID/g, data.conditions[i].id);
                            conditionTemplateStr = conditionTemplateStr.replace(/CONDITION_NAME/g, data.conditions[i].name);
                            conditionTemplateStr = conditionTemplateStr.replace(/CONDITION_VARIABLE_NAME/g, data.conditions[i].variable_name);
                            conditionTemplateStr = conditionTemplateStr.replace(/CONDITION_COMPARATOR_NAME/g, data.conditions[i].comparator);

                            if (data.conditions[i].is_parameterized) {
                                conditionTemplateStr = conditionTemplateStr.replace(/IS_RANGE_VALUE/g, data.conditions[i].is_range_value);
                                conditionTemplateStr = conditionTemplateStr.replace(/IS_DATE_VALUE/g, data.conditions[i].is_date_value);
                            } else {
                                if (data.conditions[i].comparator_id == 7 || data.conditions[i].comparator_id == 16)
                                    conditionTemplateStr = conditionTemplateStr.replace(/CONDITION_DISPLAY_VALUE/g, "");
                                else
                                    conditionTemplateStr = conditionTemplateStr.replace(/CONDITION_DISPLAY_VALUE/g, (data.conditions[i].is_date_value || data.conditions[i].is_date_file_value ?
                                        data.conditions[i].display_value :
                                        "'" + data.conditions[i].display_value + "'"));
                            }

                            conditionTemplateStr = conditionTemplateStr.replace(/CONDITION_RELATIONSHIP/g, data.conditions[i].relationship);

                            conditionTemplateStr = conditionTemplateStr.replace(/CONDITION_FILTER_DISPLAY_VALUE/g, data.conditions[i].filter_display_value ? " " + client_messages.text.filter_condition.when + " " + data.conditions[i].filter_display_value : "");

                            conditionTemplateStr = conditionTemplateStr.replace(/BIND_MASK/g, "");

                            $(newRuleEle).find('.ruleConditionTable').append($(conditionTemplateStr).find('.templateTable tbody').children());
                            $(newRuleEle).find('.stageLabel').tooltip();
                        }

                        if (data.select_type == "radio")
                            $(newRuleEle).find('.conditionRelationshipIndicator').remove();

                        if (use == 'active') {

                            var $newRuleElement = $(newRuleEle).children().addClass('hidden');

                            $('.ruleItemsContainer').prepend($newRuleElement);

                            if (!(data.conditions.length > 1)) {
                                $('#ruleContainer_' + data.id).find('.conditionBindingContainer').hide();
                            }

                            $('#ruleContainer_' + data.id).find('.conditionValueBindingContainer').each(function () {
                                $(this).ruleValueManager({
                                    comparatorTriggers: {
                                        range: 17,
                                        notEmpty: 16,
                                        empty: 7
                                    },
                                    dateValueTypes: ${dateValueTypesJSON},
                                    type: 'value',
                                    externalUpdateTrigger: $('.flowControlBtn')
                                });
                            });

                            toggleRuleRelationships();
                            toggleConditionRelationships();

                            $('#ruleContainer_' + data.id).find('.ruleActionsContainer').show();
                            initRuleActionControls($('#ruleContainer_' + data.id));

                            $('#ruleContainer_' + data.id).find("input:button").styleActionElement();

                            $('#ruleContainer_' + data.id).find("input:radio:first").attr('checked', 'checked');
                            $('#ruleContainer_' + data.id).find(".conditionBinding:input:checkbox").click(function () {
                                toggleRuleDoneStatusBtn();
                            });
                            toggleRuleDoneStatusBtn();

                            initParameterizeToggle(data.id);

                            setDisabledRuleTokens();

                            toggleRuleRelationshipToggle();
                            toggleNoRuleInfoDisplay();
                            validateRulesSize();

                            common.refreshParentIframeHeight();
                            $newRuleElement.removeClass('hidden');

                        } else if (use == 'preview') {
                            var rulePreview = $('<div style="font-size: 11px; padding-top: 2px;"><div align="left" class="rulePreviewHeader"><b>' + client_messages.text.metatags + ':</b>&nbsp;&nbsp;' + data.metatags + '</div></div>').append($(newRuleEle).children());

                            $(rulePreview).find('.ruleRelationshipIndicator').remove();
                            $(rulePreview).find('.ruleHeaderContainer').remove();
                            $(rulePreview).find('.conditionBindingContainer').html('<div style="margin-top: 3px;" class="arrowRightIconDiv"></div>');
                            $(rulePreview).find('.ruleCanBeParameterized').remove();
                            $(rulePreview).find('.conditionValueBindingContainer').remove();
                            $(rulePreview).find('.ruleConditionParameterizedContainer').show();
                            $(rulePreview).find('.tableOutline').css({'border': 'none', 'box-shadow': 'none'});
                            $(rulePreview).find('.conditionRelationshipIndicator td').css({'padding': '0px 0px 2px 22px'});

                            setTimeout(function () {
                                toggleConditionRelationships($(rulePreview));
                            }, 100);

                            return $(rulePreview);
                        }
                    }

                    function processRuleItems(data) {

                        var $ruleListContainer = $('.ruleListContainer');

                        $ruleListContainer.html('');
                        $('#ruleListContainerLoading').hide();

                        if (data.rules.length === 0) {
                            $('#ruleListContainerNone').show();
                        }

                        for (var i = 0; i < data.rules.length; i++) {
                            var ruleTokenHTML = $("<div class=\"ruleToken draggable\" id=\"ruleToken_" + data.rules[i].id + "\">" +
                                "<div class=\"tokenLabel\">" + data.rules[i].name + "</div>" +
                                "<div class=\"draggable-item-indicator\"><i class=\"fa fa-ellipsis-v\" aria-hidden=\"true\"></i><i class=\"fa fa-ellipsis-v\" aria-hidden=\"true\"></i></div>" +
                                "</div>");

                            $(ruleTokenHTML).dblclick(function () {
                                if ($(this).hasClass('ruleTokenDisabled'))
                                    return;
                                retrieveRuleDefinition(this, 'active');
                            });

                            var stampDate = new Date();
                            $(ruleTokenHTML).popupFactory({
                                popupLocation: "right",
                                title: $(ruleTokenHTML).find('.tokenLabel').html(),
                                width: 500,
                                asyncSetContentURL: context + "/getRules.form?type=ruleDefinition&ruleId=" + data.rules[i].id + "&cacheStamp=" + (stampDate.getTime()),
                                asyncSetContentHandler: function (o, data) {
                                    return processRuleDefinition(data, 'preview');
                                }
                            });

                            $(ruleTokenHTML).draggable({
                                cursorAt: {top: 32, left: 32},
                                opacity: 0.8,
                                helper: function (e) {
                                    return $("<div class=\"ruleToken draggable\" id=\"ruleToken_" + parseId(this) + "\">" +
                                        "<div class=\"tokenLabel\">" + $(this).find('.tokenLabel').html() + "</div>" +
                                        "<div class=\"draggable-item-indicator\"><i class=\"fa fa-ellipsis-v\" aria-hidden=\"true\"></i><i class=\"fa fa-ellipsis-v\" aria-hidden=\"true\"></i></div>" +
                                        "</div>");
                                },
                                scroll: false,
                                appendTo: 'body'
                            });

                            $ruleListContainer.append(ruleTokenHTML);
                            var height = 0;

                            $ruleListContainer.find('.ruleToken').each(function (i) {

                                if (i === 5)
                                    return false;

                                height += $(this).outerHeight();

                            });

                            $ruleListContainer.closest('.ruleListContainerWrapper').height(height);

                            setDisabledRuleTokens();
                        }

                    }

                    function retrieveRuleDefinition(ele, use) {
                        var ruleId = parseId(ele);

                        var stampDate = new Date();
                        // Async Request
                        $.ajax({
                            type: "GET",
                            url: context + "/getRules.form?type=ruleDefinition&ruleId=" + ruleId + "&cacheStamp=" + stampDate.getTime(),
                            dataType: "json",
                            success: function (data) {
                                processRuleDefinition(data, use);
                            }
                        });
                    }

                    function setDisabledRuleTokens() {
                        $('.ruleToken').removeClass('ruleTokenDisabled');
                        $('.ruleToken').draggable('enable');
                        var activeRules = $('.ruleDisplayContainer');
                        for (var i = 0; i < activeRules.length; i++) {
                            var ruleId = parseId(activeRules[i]);
                            $('#ruleToken_' + ruleId).addClass('ruleTokenDisabled');
                            $('#ruleToken_' + ruleId).draggable('disable');
                        }
                    }

                    function validateRulesSize() {
                        if ($('.ruleItemsContainer .ruleDisplayContainer').length > 0) {
                            $("#containsConditionElements").val("true").trigger('change');
                        } else {
                            $("#containsConditionElements").val("false").trigger('change');
                        }
                    }

                    function toggleConditionRelationships(targetRule) {
                        var scope = $('.conditionRelationshipIndicator');
                        if (targetRule)
                            scope = $(targetRule).find('.conditionRelationshipIndicator');

                        $(scope).each(function () {
                            if ($(this).prev('.conditionDisplayContainer:visible').length > 0 && $(this).nextAll('.conditionDisplayContainer:visible').length > 0)
                                $(this).showEle('normal');
                            else
                                $(this).hide();
                        });
                    }

                    function toggleNoRuleInfoDisplay() {
                        if ($('.ruleItemsContainer .ruleDisplayContainer').length > 0)
                            $('#info_noActiveRules').hide();
                        else
                            $('#info_noActiveRules').showEle('normal');
                    }

                    function toggleRuleDoneStatusBtn() {
                        $('.ruleDisplayContainer').each(function () {
                            var ruleId = parseId(this);

                            if ($('#ruleDoneButton_' + ruleId).length == 0 || $(this).find('.conditionBinding:checkbox').length == 0)
                                return;

                            if ($(this).find('.conditionBinding:checkbox:checked').length == 0) {
                                $('#ruleDoneButton_' + ruleId).disableElement();
                                $(this).find('.ruleActionsContainer').show();
                                if (!$('#ruleDoneButton_' + ruleId + '_button').is(':visible'))
                                    toggleRuleEdit($('#ruleEditButton_' + ruleId));

                            } else
                                $('#ruleDoneButton_' + ruleId).enableElement();
                        });

                    }

                    function toggleRuleEdit(btn) {

                        var ruleId = parseId(btn);

                        if ($(btn).is('.ruleEditButton')) {

                            $('#ruleContainer_' + ruleId).find('.ruleEditButtonContainer').hide();
                            $('#ruleContainer_' + ruleId).find('.ruleDoneButtonContainer').showEle('normal');

                            $('#parameterizeRuleToggleContainer_' + ruleId).showEle('normal');
                            if ($('#ruleContainer_' + ruleId).find('.conditionBindingContainer').length > 1)
                                $('#ruleContainer_' + ruleId).find('.conditionBindingContainer').showEle('normal');
                            $('#ruleContainer_' + ruleId).find('.conditionDisplayContainer').showEle('normal');

                            initParameterizeToggle(ruleId);
                            toggleRuleDoneStatusBtn();

                        } else {

                            $('#ruleContainer_' + ruleId).find('.ruleDoneButtonContainer').hide();
                            $('#ruleContainer_' + ruleId).find('.ruleEditButtonContainer').showEle('normal');

                            $('#parameterizeRuleToggleContainer_' + ruleId).hide();
                            $('#ruleContainer_' + ruleId).find('.conditionBindingContainer').hide();
                            $('#ruleContainer_' + ruleId).find('.conditionDisplayContainer').each(function () {
                                if (!$(this).find('.conditionBinding').is(':checked'))
                                    $(this).hide();
                            });

                        }

                        toggleConditionRelationships();

                    }

                    function toggleRuleParameterization(ruleId, checkEle) {

                        if ($(checkEle).is(':checked')) {
                            $('#ruleContainer_' + ruleId).find('.conditionValueBindingContainer').hide();
                            $('#ruleContainer_' + ruleId).find('.ruleConditionParameterizedContainer').showEle('normal');
                        } else {
                            $('#ruleContainer_' + ruleId).find('.ruleConditionParameterizedContainer').hide();
                            $('#ruleContainer_' + ruleId).find('.conditionValueBindingContainer').showEle('normal');
                        }

                    }

                    function toggleRuleRelationships() {
                        var indicator = $('#ruleRelationshipToggle').is(':checked') ? client_messages.text.targeting.AND : client_messages.text.targeting.OR;
                        $('.ruleRelationshipIndicator').each(function (i) {
                            $(this).html(indicator);
                            if ($(this).prev('.ruleDisplayContainer').length > 0 && $(this).next('.ruleDisplayContainer').length > 0)
                                $(this).showEle('normal');
                            else
                                $(this).hide();
                        });
                    }

                    function toggleRuleRelationshipToggle() {
                        if ($('.ruleItemsContainer .ruleDisplayContainer').length > 1)
                            $('.ruleRelationshipToggleDisplay').showEle('normal');
                        else
                            $('.ruleRelationshipToggleDisplay').hide();
                    }

                    function initBtn(ele, type) {
                        $(ele)
                            .mouseover(function () {
                                if (!$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelect')) {
                                    $(this).removeClass('actionBtn');
                                    $(this).addClass('actionBtn_hov');
                                }
                            })
                            .mouseout(function () {
                                if (!$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelect')) {
                                    $(this).removeClass('actionBtn_hov');
                                    $(this).addClass('actionBtn');
                                }
                            });

                        if (type == "button") {
                            $(ele)
                                .mousedown(function () {
                                    if (!$(this).hasClass('actionBtn_disabled'))
                                        $(this).removeClass('actionBtn actionBtn_hov').addClass('actionBtn_selected');
                                })
                                .mouseup(function () {
                                    if (!$(this).hasClass('actionBtn_disabled'))
                                        $(this).removeClass('actionBtn_selected').addClass('actionBtn');
                                })
                        }
                    }

                    function toggleTpAssignmentPanel() {
                        if ($('#toggleTpAssignmentPanelBtn').is('.actionBtn_toggleSelect')) {
                            // Touchpoint Assignment Panel: Hide
                            $('#toggleTpAssignmentPanelBtn').removeClass('actionBtn_toggleSelect');
                            $('#toggleTpAssignmentPanelBtn').addClass('actionBtn');
                            $('#tpAssignmentPanelContainer').animate({width: "0px", left: "4px", height: "0px"});
                        } else {
                            // Touchpoint Assignment Panel: Reveal
                            $('#toggleTpAssignmentPanelBtn').removeClass('actionBtn actionBtn_hov');
                            $('#toggleTpAssignmentPanelBtn').addClass('actionBtn_toggleSelect');
                            // Height
                            $('#tpAssignmentPanelContainer').animate({
                                width: "820px",
                                height: "375px",
                                left: "-718px",
                                top: "40px"
                            }, function () {
                            });
                        }
                    }

                    function pollForSegResult() {
                        if ($('#segStatusPollingContainer').length == 0)
                            return;

                        var stampDate = new Date();
                        $.ajax({
                            type: "GET",
                            url: context + "/getSegResult.form?modelType=3&modelId=${param.targetgroupid}&documentId=${documentId}&tk=${param.tk}&cacheStamp=" + stampDate.getTime(),
                            dataType: "json",
                            success: function (data) {
                                processSegPollResult(data);
                            }
                        });

                        window.setTimeout(function () {
                            pollForSegResult()
                        }, 2000);
                    }

                    function processSegPollResult(data) {
                        if (data.status != "in_process") {
                            $('#segStatusPollingContainer').remove();
                            $('#segResultContainer').text(data.reach);
                        }
                    }

                    var searchLock = false;
                    var defaultSearchText = null;

                    $(function () {

                        // CSS Include: Popup is built top window
                        if ($(getTopFrame().document).find('#minimalCSS').length == 0)
                            $(getTopFrame().document).find('head').append("<link id=\"minimalCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"../includes/themes/commoncss/theme_minimal.css\" />");
                        if ($(getTopFrame().document).find('#targetingCSS').length == 0)
                            $(getTopFrame().document).find('head').append("<link id=\"targetingCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"../includes/themes/commoncss/targeting.css\" />");

                        if (${command.targetGroupId > 0 ? command.targetGroupId : -1} >
                        0
                    )
                        $('#taskIndicator').taskManager({
                            item_type: 7,
                            item_id: ${command.targetGroupId},
                        });

                        $('#ruleRelationshipToggle').each(function () {

                            var $this = $(this);

                            if (!$this.closest('.targeting-body').length) {

                                var currentTitle = $this.attr('title');

                                $this.iButton({
                                    labelOn: currentTitle.split(';')[1],
                                    labelOff: currentTitle.split(';')[0],
                                    resizeHandle: false,
                                    resizeContainer: "auto",
                                    change: function () {
                                        toggleRuleRelationships();
                                    }
                                });

                            } else {

                                $this.toggleSwitch().on('change', function () {
                                    toggleRuleRelationships();
                                });

                            }

                        });

                        toggleRuleRelationshipToggle();

                        $("[id^='ruleConditionBinding']").each(function () {
                            $(this).ruleValueManager({
                                comparatorTriggers: {
                                    range: 17,
                                    notEmpty: 16,
                                    empty: 7
                                },
                                dateValueTypes: ${dateValueTypesJSON},
                                type: 'value',
                                externalUpdateTrigger: $('.flowControlBtn')
                            });
                        });

                        toggleRuleRelationships();
                        toggleConditionRelationships();

                        defaultSearchText = $('#ruleSearchInput').val();
                        $('#ruleSearchInput')
                            .focus(function () {
                                if ($(this).val() == defaultSearchText)
                                    $(this).val('');
                            })
                            .focusout(function () {
                                if ($(this).val() == '')
                                    $(this).val(defaultSearchText);
                            })
                            .keyup(function (e) {
                                if (!searchLock) {
                                    searchLock = true;
                                    setTimeout(function () {
                                        searchLock = false;
                                        asyncGetRuleItems();
                                    }, 250);
                                }
                            })
                            .bind('paste', function () {
                                if (!searchLock) {
                                    searchLock = true;
                                    setTimeout(function () {
                                        searchLock = false;
                                        asyncGetRuleItems();
                                    }, 250);
                                }
                            });

                        asyncGetRuleItems();

                        $('.ruleDisplayContainer').each(function () {
                            initRuleActionControls(this);
                        });

                        $("input:button").each(function () {
                            if ($(this).closest('#TEMPLATE_RULE').length == 0)
                                $(this).styleActionElement();
                        });

                        $('.ruleContainer').droppable({
                            accept: '.ruleToken',
                            hoverClass: 'droppable',
                            tolerance: 'pointer',
                            drop: function (event, ui) {
                                retrieveRuleDefinition(ui.helper, 'active');
                            }
                        });

                        $('#advancedSearchIcon').click(function () {
                            if ($(this).is('.advancedSearchIconDiv'))
                                $(this).removeClass('advancedSearchIconDiv').addClass('advancedSearchIconDiv_active');
                            else
                                $(this).removeClass('advancedSearchIconDiv_active').addClass('advancedSearchIconDiv');
                            updatePersistedClass(this);
                            $('#ruleSearchInput').keyup();
                        });

                        toggleNoRuleInfoDisplay();
                        toggleRuleDoneStatusBtn();
                        validateRulesSize();

                        $(".conditionBinding:input:checkbox").click(function () {
                            toggleRuleDoneStatusBtn();
                        });

                        $('#targetGroupMetatags').tagCloud({
                            tagCloudType: 6
                        });

                        if (!$('#ruleSearchInput').hasClass('inactive')) {
                            $('#ruleSearchInput').tagCloud({
                                tagCloudType: 5,
                                inputType: 'search',
                                beforeInputValueChange: function (o, val) {
                                    if ($('#ruleSearchInput').val() == defaultSearchText)
                                        $('#ruleSearchInput').val('');
                                },
                                afterInputValueChange: function (o, val) {
                                    asyncGetRuleItems();
                                    if ($('#ruleSearchInput').val() == '')
                                        $('#ruleSearchInput').val(defaultSearchText);
                                }
                            });
                        }

                        $('#toggleTpAssignmentPanelBtn').each(function () {
                            initBtn(this, "button");
                        });
                        $('#toggleTpAssignmentPanelBtn').click(function () {
                            toggleTpAssignmentPanel();
                        });

                        $('#touchpointAssignments').dragAndDropSelect({
                            itemLabel: client_messages.title.touchpoints,
                            searchTypes: ['Name'],
                            var_noSelectedItems: client_messages.text.no_touchpoints_selected,
                            var_noMatchingItems: client_messages.text.no_touchpoints_match_criteria
                        });

                        $('#tpAssignmentPanelContainer').one('shown.bs.modal', function () {

                            $('#touchpointAssociations').dragAndDropList({});

                        });

                        // Segmentation analysis result polling init
                        $("#segStatusPollingContainer").each(function () {
                            if ($(this).attr('pollingInit') == undefined || $(this).attr('pollingInit') != "true") {
                                $(this).attr('pollingInit', 'true');
                                pollForSegResult();
                            }
                        });

                        $("[id^='parameterizeRuleBinding']").each(function () {
                            var ruleId = this.id.replace('parameterizeRuleBinding_', '');
                            if ($.isNumeric(ruleId))
                                toggleRuleParameterization(ruleId, this);
                        });

                        $('#common-buttons').find('.buttonTagBtn').click(function () {
                            common.disableFixedSidebar();
                        });

                        $('#targetGroupEditSaveBtnGroup').on('change', function () {

                            doSubmit($(this).val());

                        });

                        $('#targetGroupEditCancelBtn').on('click', function () {

                            doSubmit('cancelandexit');

                        });

                    });

                </script>
            </msgpt:Script>
        </c:if>

    </msgpt:HeaderNew>

    <c:set var="paramSimple" value="<%= ConditionElementUtil.TYPE_SIMPLE %>"/>
    <c:set var="paramSimpleRange" value="<%= ConditionElementUtil.TYPE_SIMPLE_RANGE %>"/>
    <c:set var="paramDate" value="<%= ConditionElementUtil.TYPE_DATE %>"/>
    <c:set var="paramDateRange" value="<%= ConditionElementUtil.TYPE_DATE_RANGE %>"/>
    <c:set var="viewMetatagsPermission" value="false"/>
    <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
        <c:set var="viewMetatagsPermission" value="true"/>
    </msgpt:IfAuthGranted>
    <c:set var="useNewTargetEdit" value="false"/>
    <c:if test="${!empty param.newtgedit}">
        <c:set var="useNewTargetEdit" value="true"/>
    </c:if>
    <c:if test="${!canUpdate}">
        <msgpt:Information type="error">
            <fmtSpring:message code="error.message.action.not.permitted"/>
        </msgpt:Information>
    </c:if>
    <c:if test="${canUpdate}">
        <c:choose>
            <c:when test="${not useNewTargetEdit}">
                <msgpt:BodyNew theme="minimal" type="iframe">
                    <div id="popupHeaderTitle" style="display: none;">
                        <span class="titleText">
                            <c:choose>
                                <c:when test="${not empty param.targetgroupid}">
                                    <fmtSpring:message code="page.label.edit.target.group"/>
                                </c:when>
                                <c:otherwise>
                                    <fmtSpring:message code="page.label.Add.target.group"/>
                                </c:otherwise>
                            </c:choose>
                        </span>
                    </div>
                    <c:if test="${empty param.nprSaveSuccess}">
                        <form:form modelAttribute="command" method="post">
                            <div class="contentTableIframe contentTableIFrameNoTabs">
                                <div class="targetGroupDetailsContainer">
                                    <fmtSpring:hasBindErrors name="command">
                                        <div class="pddng-lv2">
                                            <form:errors path="*">
                                                <msgpt:Information errorMsgs="${messages}" type="error"/>
                                            </form:errors>
                                        </div>
                                    </fmtSpring:hasBindErrors>
                                    <!-- INFO: Reference parameterized rule -->
                                    <c:if test="${command.referencedParameterized}">
                                        <div class="pddng-lv2">
                                            <div class="InfoSysContainer_info">
                                                <i class="fa icon fa-info-circle" aria-hidden="true"></i>
                                                <p><fmtSpring:message code="page.text.target.group.is.parameterized"/></p>
                                            </div>
                                        </div>
                                    </c:if>
                                    <c:set var="pageRequestType" value="0"/>
                                    <c:set var="targetGroupDisabled" value="${command.referencedParameterized}"/>
                                    <input type="hidden" id="isModifiable" value="${not command.referenced}"/>
                                    <div class="row align-items-middle pddng-lv3">
                                        <c:if test="${displaySegmentationAnalysis}">
                                            <div class="col-3of12">
                                                <div class="targetingSegmentationAnalysis">
                                                    <div>
                                                        <fmtSpring:message code="page.label.reach"/>
                                                    </div>
                                                    <c:choose>
                                                        <c:when test="${segmentationInProcess}">
                                                            <em id="segStatusPollingContainer"></em><br>
                                                            <em id="segResultContainer">--</em><br>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <em><c:out value="${command.segmentationPercentage}"/></em>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </c:if>
                                        <div class="col">
                                            <div class="formControl horizontal-control row">
                                                <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                        code="page.label.name"/></span></label>
                                                <div class="controlWrapper col-10of12">
                                                    <msgpt:InputFilter type="simpleName">
                                                        <form:input path="nameText"/>
                                                    </msgpt:InputFilter>
                                                </div>
                                            </div>
                                            <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
                                                <div class="formControl horizontal-control row">
                                                    <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                            code="page.label.metatags"/></span></label>
                                                    <div class="controlWrapper col-10of12">
                                                        <div class="d-flex">
                                                            <msgpt:InputFilter type="description">
                                                                <form:input id="targetGroupMetatags" path="metatags" cssClass="w-100"/>
                                                            </msgpt:InputFilter>
                                                        </div>
                                                    </div>
                                                </div>
                                            </msgpt:IfAuthGranted>
                                        </div>
                                    </div>

                                </div> <!-- .targetGroupDetailsContainer -->

                                <div class="row no-gutters">
                                    <div class="ruleSearchContainer col-3of12">
                                        <div class="ruleSearchInputContainer">
                                            <div class="position-relative">
                                                <input id="ruleSearchInput"
                                                       class="input ruleSearchInput ${viewMetatagsPermission?'':'inactive'} w-100"
                                                       value="${msgpt:getMessage('page.text.search.for.rules')}"/>
                                                <div id="advancedSearchIcon"
                                                     title="|<div class='detailTipText'>${msgpt:getMessage("page.label.advanced.search")}</div>"
                                                     class="advancedSearchIconDiv persistedClass detailTip position-absolute" style="line-height: 1;">
                                                    <i class="fa fa-crosshairs fa-med fa-mp-style"
                                                       style="position: relative; top: 1px;"></i>
                                                </div>
                                                <i class="searchIconDiv fa fa-med fa-search fa-mp-style"></i>
                                            </div>
                                        </div>
                                        <div class="dark">
                                            <div class="ruleListContainer">

                                            </div>
                                        </div>
                                    </div>
                                    <div class="ruleContentContainer col-9of12">
                                        <div class="ruleRelationshipToggleContainer">
                                            <div class="row align-items-middle align-items-end">
                                                <div class="ruleRelationshipToggleDisplay col">
                                                    <div class="row align-items-middle pddng-lv2 pddng-vertical-lv0">
                                                        <div class="col-auto pddng-horizontal-lv0">
                                                            <form:checkbox
                                                                    id="ruleRelationshipToggle" path="conditionRelationship"
                                                                    title="${msgpt:getMessage('page.label.at.least.one')};${msgpt:getMessage('page.label.all')}"/>
                                                        </div>
                                                        <div class="col-auto">
                                                            <fmtSpring:message
                                                                    code="page.text.rules.must.pass.for.target.group.to.qualify"/>:
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- BUTTON: Target Group Touchpoint Assignment: Lightbox toggle -->
                                                <div class="col-auto">
                                                    <div id="toggleTpAssignmentPanelBtn"
                                                         class="actionBtn_roundAll actionBtn txtFmtTip"
                                                         title="|<div class='detailTipText'>${msgpt:getMessage("page.text.toggle.touchpoint.assignment.panel")}</div>">
                                                        <div class="actionBtnText"><fmtSpring:message
                                                                code="page.label.visibility"/></div>
                                                    </div>

                                                    <div id="tpAssignmentPanelContainer"
                                                         style="overflow: hidden; position: absolute; width: 0px; left: 0px; top: -3px; height: 0px; z-index: 21;">
                                                        <div class="stageContainer" style="position: relative;">
                                                            <table width="100%" cellspacing="0" cellpadding="0"
                                                                   border="0" class="tableOutline">
                                                                <tr>
                                                                    <td align="left"
                                                                        class="tableIndicatorCol cellTopLeft cellTopRight"
                                                                        style="border-right: none;">
                                                                        <table width="100%" cellspacing="0"
                                                                               cellpadding="0" border="0">
                                                                            <tr>
                                                                                <td align="left">
                                                                                    <div style="margin: 8px 15px; font-size: 14px; font-weight: bold; white-space: nowrap;">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.touchpoint.assignments"/>
                                                                                    </div>
                                                                                </td>
                                                                                <td align="right">
                                                                                    <i class="fa fa-times closeAssignments"
                                                                                       style="margin-right: 16px; cursor: pointer;"
                                                                                       onclick="toggleTpAssignmentPanel()"/>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td align="center"
                                                                                    class="tableContentCol cellBottomLeft cellBottomRight"
                                                                                    style="padding: 0px; border-bottom: none;">
                                                                                    <div>
                                                                                        <msgpt:DataTable>
                                                                                            <!-- Interface Definition: Touchpoint Assignments -->
                                                                                            <c:if test="${fn:length(availableTouchpoints) > 0}">
                                                                                                <msgpt:TableItem>
                                                                                                    <div id="touchpointAssignments"
                                                                                                         style="display: none;">
                                                                                                        <c:forEach
                                                                                                                var="touchpoint"
                                                                                                                items="${availableTouchpoints}">
                                                                                                            <div id="touchpointItem_${touchpoint.id}"
                                                                                                                 touchpointId="${touchpoint.id}"
                                                                                                                 level="${touchpoint.projectDepth}"
                                                                                                                 class="dragAndDropItem">
                                                                                                                <table cellspacing="0"
                                                                                                                       cellpadding="0"
                                                                                                                       border="0">
                                                                                                                    <tr>
                                                                                                                        <td style="padding: 0px;">
                                                                                                                            <form:checkbox
                                                                                                                                    path="touchpointAssignments"
                                                                                                                                    value="${touchpoint.id}"
                                                                                                                                    cssStyle="display : none;"/>
                                                                                                                                ${touchpoint.name}
                                                                                                                        </td>
                                                                                                                    </tr>
                                                                                                                </table>
                                                                                                            </div>
                                                                                                        </c:forEach>
                                                                                                    </div>
                                                                                                </msgpt:TableItem>
                                                                                            </c:if>
                                                                                            <!-- Info: No Touchpoints -->
                                                                                            <c:if test="${fn:length(availableTouchpoints) == 0}">
                                                                                                <msgpt:TableItem>
                                                                                                    <div class="fullLineLabel"
                                                                                                         style="padding: 2px 8px">
                                                                                                        <fmtSpring:message
                                                                                                                code="page.label.AVAILABLE.TOUCHPOINTS"/>
                                                                                                    </div>
                                                                                                    <div class="infoDiv_minimal">
                                                                                                        <fmtSpring:message
                                                                                                                code="page.label.no.available.touchpoints"/>
                                                                                                    </div>
                                                                                                </msgpt:TableItem>
                                                                                            </c:if>
                                                                                        </msgpt:DataTable>
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                <c:if test="${hasTaskViewPermission}">
                                                    <div id="taskIndicator" class="col-auto"></div>
                                                </c:if>
                                            </div>
                                        </div>

                                        <div class="ruleContainer">
                                            <div id="info_noActiveRules" class="InfoSysContainer_info">
                                                <i class="fa icon fa-info-circle" aria-hidden="true"></i>
                                                <p><fmtSpring:message
                                                        code="page.text.drag.and.drop.rule.into.target.group"/></p>
                                            </div>
                                            <div class="droppable-item-placement mrgn-bottom-lv4">
                                                <i class="mp-icon fa fa-hand-o-right" aria-hidden="true"></i>
                                                &nbsp;
                                            </div>
                                            <form:hidden path="containsConditionElements"/>
                                            <div class="ruleItemsContainer">
                                                <c:forEach var="element" items="${command.conditionElements}">

                                                    <div id="ruleContainer_${element.id}"
                                                         class="ruleDisplayContainer stageContainer">
                                                        <table width="100%" cellspacing="0" cellpadding="0" border="0"
                                                               class="tableOutline ruleConditionTable">
                                                            <tr class="ruleHeaderContainer">

                                                                <!-- Rule Name -->
                                                                <td width="99%" align="left"
                                                                    class="tableIndicatorCol cellTopLeft"
                                                                    style="border-right: none;">
                                                                    <div class="stageLabel" data-toggle="tooltip"
                                                                         title="<c:out value="${element.name}"/>">
                                                                        <c:out value="${element.name}"/>
                                                                    </div>

                                                                    <!-- Rule Binding -->
                                                                    <fmtSpring:bind path="command.conditionElements">
                                                                        <input type="hidden" name="${status.expression}"
                                                                               type="checkbox" value="${element.id}"
                                                                               class="checkbox" checked="checked"/>
                                                                    </fmtSpring:bind>
                                                                    <input type="hidden" value="1"
                                                                           name="_conditionElements"/>
                                                                </td>

                                                                <!-- Rule Actions: Edit, Remove -->
                                                                <td class="tableIndicatorCol cellTopRight"
                                                                    style="border-right: none;">
                                                                    <table width="100%" cellspacing="0" cellpadding="0"
                                                                           border="0"
                                                                           class="innerCellTable ruleActionsContainer"
                                                                           style="display:none;">
                                                                        <tr>
                                                                            <td class="ruleEditButtonContainer">
                                                                                <input id="ruleEditButton_${element.id}"
                                                                                       title="${msgpt:getMessage('page.label.edit')}"
                                                                                       type="button"
                                                                                       class="ruleEditButton ruleActionBtn"
                                                                                       style="display: none;"/>
                                                                            </td>
                                                                            <td class="ruleDoneButtonContainer"
                                                                                style="display: none;">
                                                                                <input id="ruleDoneButton_${element.id}"
                                                                                       title="${msgpt:getMessage('page.label.done')}"
                                                                                       type="button"
                                                                                       class="ruleDoneButton ruleActionBtn"
                                                                                       style="display: none;"/>
                                                                            </td>
                                                                            <td style="vertical-align: middle; padding-left: 6px; padding-top: 4px; padding-right: 16px;">
                                                                                <div class="removeIcon">
                                                                                    <i class="fa fa-times"
                                                                                       aria-hidden="true"></i>
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>

                                                                <!-- Rule Display -->
                                                                <td align="left"
                                                                    class="tableContentCol cellBottomLeft cellBottomRight"
                                                                    colspan="2"
                                                                    style="border-bottom: none;">

                                                                    <c:set var="ruleCanBeParameterized" value="false"/>

                                                                    <table cellspacing="0" cellpadding="0" border="0">
                                                                        <c:forEach var="subelement"
                                                                                   items="${element.subElements}"
                                                                                   varStatus="subelementCount">

                                                                            <tr class="conditionDisplayContainer"
                                                                                style="${msgpt:containsValue(command.conditionSubelementMap[element.id], subelement.id) ? '' : 'display:none;'}">

                                                                                <td width="1%"
                                                                                    class="conditionBindingContainer"
                                                                                    style="padding: 4px 4px 4px 8px; vertical-align: top; display: none;">

                                                                                    <!-- Condition Binding -->
                                                                                    <c:set var="inputType"
                                                                                           value="${subelement.conditionType.id == 1 ? 'radio' : 'checkbox'}"/>
                                                                                    <fmtSpring:bind
                                                                                            path="command.conditionSubelementMap[${element.id}]">
                                                                                        <input name="${status.expression}"
                                                                                               type="${inputType}"
                                                                                               value="${subelement.id}"
                                                                                               class="checkbox conditionBinding" ${msgpt:containsValue(status.value, subelement.id) ? 'checked="checked"' : ''} />
                                                                                    </fmtSpring:bind>
                                                                                    <input type="hidden"
                                                                                           value="${msgpt:containsValue(command.conditionSubelementMap[element.id], subelement.id) ? 1: 0 }"
                                                                                           name="_conditionSubelementMap[${element.id}]"/>

                                                                                </td>

                                                                                <td align="left"
                                                                                    style="padding: 4px 4px 4px 8px; vertical-align: top;">
                                                                                    <!-- Condition Name -->
                                                                                    <span class="conditionNameContainer">
                                                                    <c:out value="${subelement.name}"/>:
                                                                </span>
                                                                                </td>

                                                                                <td align="left"
                                                                                    style="padding: 4px 8px 4px 4px; vertical-align: top;">

                                                                                    <div style="display: inline-block; vertical-align: top;">
                                                                    <span>
                                                                        <fmtSpring:message
                                                                                code="page.label.If"/> <span
                                                                            style="padding: 0px 2px;"
                                                                            class="conditionVariableNameContainer">
                                                                            <c:out value="${subelement.dataElementVariable.displayName}"/>
                                                                        </span>
                                                                    </span>
                                                                                        <span class="conditionComparatorContainer">
                                                                        <fmtSpring:message
                                                                                code="${subelement.dataComparison.name}"/>
                                                                    </span>
                                                                                    </div>

                                                                                    <!-- Condition Value -->
                                                                                    <c:choose>
                                                                                        <c:when test="${subelement.parameterized == true}">

                                                                                            <c:set var="ruleCanBeParameterized"
                                                                                                   value="true"/>

                                                                                            <div style="display: inline-block; vertical-align: middle;">
                                                                                                <c:set var="subElementType"
                                                                                                       value="${command.conditionSubelementValueTypes[subelement.id]}"/>
                                                                                                <div id="ruleConditionBinding_${element.id}_${subelement.id}"
                                                                                                     class="conditionValueBindingContainer"
                                                                                                     isRangeValue="${subElementType eq paramSimpleRange || subElementType eq paramDateRange}"
                                                                                                     isDateValue="${subElementType eq paramDate || subElementType eq paramDateRange}"
                                                                                                    ${parameterizeRuleMap[element.id]?'style="display: none;"':''}>
                                                                                                    <!-- Value String Binding -->
                                                                                                    <form:input
                                                                                                            id="conditionValueString_${element.id}_${subelement.id}"
                                                                                                            cssClass="variableComparedToValue"
                                                                                                            path="conditionSubelementValues[${subelement.id}]"
                                                                                                            cssStyle="display:none;"/>
                                                                                                </div>
                                                                                                <div class="ruleConditionParameterizedContainer" ${parameterizeRuleMap[element.id]?'':'style="display: none;"'}>
                                                                                                    <fmtSpring:message
                                                                                                            code="page.label.user.specified.values"/>
                                                                                                </div>
                                                                                            </div>

                                                                                        </c:when>
                                                                                        <c:otherwise>

                                                                                            <div style="display: inline-block; vertical-align: middle;">
                                                                            <span style="padding: 0px 2px;">
                                                                                <c:choose>
                                                                                    <c:when test="${subelement.isValueComparison}">
                                                                                        '<c:out
                                                                                            value="${subelement.dataElementDisplayValue}"
                                                                                            escapeXml="false"/>'
                                                                                    </c:when>
                                                                                    <c:otherwise>
                                                                                        <c:out value="${subelement.dataElementDisplayValue}"
                                                                                               escapeXml="false"/>
                                                                                    </c:otherwise>
                                                                                </c:choose>
                                                                            </span>
                                                                                            </div>

                                                                                        </c:otherwise>
                                                                                    </c:choose>

                                                                                    <c:if test="${subelement.filterCondition != null}">
                                                                                        <div style="display: inline-block; vertical-align: middle;">
                                                                                            <fmtSpring:message
                                                                                                    code="page.label.WHEN"/>
                                                                                            <c:out value="${subelement.filterCondition.displayValue}"
                                                                                                   escapeXml="false"/>
                                                                                        </div>
                                                                                    </c:if>

                                                                                </td>

                                                                            </tr>

                                                                            <c:if test="${element.conditionType.id != 1}">
                                                                                <tr class="conditionRelationshipIndicator"
                                                                                    style="display: none;">
                                                                                    <td align="left" colspan="3">
                                                                                        <div style="padding-left: 9px; font-size: 11px;">
                                                                                            <fmtSpring:message
                                                                                                    code="${element.conditionType.id == 2 ? 'page.label.and' : 'page.label.or'}"/></div>
                                                                                    </td>
                                                                                </tr>
                                                                            </c:if>

                                                                        </c:forEach>    <!-- foreach: selected condition -->
                                                                    </table>

                                                                </td>

                                                            </tr>

                                                            <c:if test="${ruleCanBeParameterized == 'true'}">
                                                                <tr id="parameterizeRuleToggleContainer_${element.id}"
                                                                    class="ruleCanBeParameterized" style="display: none;">
                                                                    <td class="tableContentCol cellBottomLeft cellBottomRight"
                                                                        colspan="2"
                                                                        style="border-top: 1px solid #fff; vertical-align: middle;">

                                                                        <div style="display: inline-block; vertical-align: middle; padding-left: 10px; padding-right: 8px;">
                                                                            <fmtSpring:message
                                                                                    code="page.text.user.specified.values"/></div>
                                                                        <div style="display: inline-block; vertical-align: middle;">
                                                                            <form:checkbox
                                                                                    path="parameterizeRuleMap[${element.id}]"
                                                                                    id="parameterizeRuleBinding_${element.id}"
                                                                                    title="No;Yes"
                                                                                    value="${parameterizeRuleMap[element.id]?parameterizeRuleMap[element.id]:'true'}"
                                                                                    disabled="${command.referenced}"/>

                                                                        </div>
                                                                        <c:if test="${command.referenced}">
                                                                        <div style="display: inline-block; vertical-align: middle;"
                                                                             data-toggle="tooltip"
                                                                             title="${msgpt:getMessage("page.text.target.group.may_not_be_enabled")}">
                                                                            <span class="InfoSysContainer_info"
                                                                                  style="margin-left: 5px; padding-right: 10px;"><fmtSpring:message
                                                                                    code="page.text.locked.target.group.referenced"/></span>
                                                                        </div>
                                                                        </c:if>
                                                                    <td>
                                                                </tr>
                                                            </c:if>

                                                        </table>
                                                    </div>
                                                    <!-- .ruleDisplayContainer -->

                                                    <div class="ruleRelationshipIndicator" style="display: none;"></div>

                                                </c:forEach>    <!-- foreach: selected rule -->
                                            </div>
                                        </div>            <!-- .ruleContainerDisplay -->
                                    </div>            <!-- .ruleContentContainer -->
                                </div>


                                <!--  TEMPLATES: START -->

                                <div id="TEMPLATE_RULE" style="display: none;">

                                    <!-- TEMPLATE: RULE: START -->
                                    <div id="ruleContainer_RULE_ID" class="ruleDisplayContainer stageContainer">
                                        <table width="100%" cellspacing="0" cellpadding="0" border="0" class="tableOutline">
                                            <tr class="ruleHeaderContainer">

                                                <!-- Rule Name -->
                                                <td width="99%" align="left" class="tableIndicatorCol cellTopLeft"
                                                    style="border-right: none;">
                                                    <div class="stageLabel" title="RULE_NAME">RULE_NAME</div>
                                                    <!-- Rule Binding -->
                                                    <input type="hidden" name="BIND_MASKconditionElements" type="checkbox"
                                                           value="RULE_ID" class="checkbox" checked="checked"/>
                                                    <input type="hidden" value="1" name="BIND_MASK_conditionElements"/>
                                                </td>

                                                <!-- Rule Actions: Edit, Remove -->
                                                <td class="tableIndicatorCol cellTopRight" style="border-right: none;">
                                                    <table width="100%" cellspacing="0" cellpadding="0" border="0"
                                                           class="innerCellTable ruleActionsContainer"
                                                           style="display:none;">
                                                        <tr>
                                                            <!-- IF: # conditions > 1 -->
                                                            <td class="ruleEditButtonContainer" align="center"
                                                                style="display: none; vertical-align: middle; padding-left: 4px; padding-right: 8px;">
                                                                <input id="ruleEditButton_RULE_ID"
                                                                       title="${msgpt:getMessage('page.label.edit')}"
                                                                       type="button"
                                                                       class="ruleEditButton ruleActionBtn"
                                                                       style="display: none;"/>
                                                            </td>
                                                            <td class="ruleDoneButtonContainer"
                                                                style="vertical-align: middle; padding-left: 4px; padding-right: 8px;">
                                                                <input id="ruleDoneButton_RULE_ID"
                                                                       title="${msgpt:getMessage('page.label.done')}"
                                                                       type="button"
                                                                       class="ruleDoneButton ruleActionBtn "
                                                                       style="display: none;"/>
                                                            </td>
                                                            <!-- END IF -->
                                                            <td style="vertical-align: middle; padding-left: 6px; padding-top: 4px; padding-right: 16px;">
                                                                <div class="removeIcon">
                                                                    <i class="fa fa-times" aria-hidden="true"></i>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>

                                                <!-- Rule Display -->
                                                <td class="tableContentCol cellBottomLeft cellBottomRight" colspan="2"
                                                    style="border-bottom: none; vertical-align: middle;">
                                                    <table cellspacing="0" cellpadding="0" border="0"
                                                           class="ruleConditionTable">

                                                    </table>
                                                </td>

                                            </tr>

                                            <!-- IF: Rule can be parameterized -->
                                            <tr id="parameterizeRuleToggleContainer_RULE_ID" class="ruleCanBeParameterized">
                                                <td class="tableContentCol cellBottomLeft cellBottomRight" colspan="2"
                                                    style="border-top: 1px solid #fff; vertical-align: middle;">

                                                    <div style="display: inline-block; vertical-align: middle; padding-left: 10px; padding-right: 8px;">
                                                        <fmtSpring:message code="page.text.user.specified.values"/></div>
                                                    <div style="display: inline-block; vertical-align: middle;">
                                                        <input name="BIND_MASKparameterizeRuleMap[RULE_ID]" type="checkbox"
                                                               id="parameterizeRuleBinding_RULE_ID" title="No;Yes"/>
                                                        <input type="hidden" value="0"
                                                               name="BIND_MASK_parameterizeRuleMap[RULE_ID]"/>
                                                    </div>

                                                <td>
                                            </tr>

                                        </table>
                                    </div>
                                    <div class="ruleRelationshipIndicator" style="display: none;"></div>
                                    <!-- TEMPLATE: RULE: END -->
                                </div>

                                <div id="TEMPLATE_CONDITION" style="display: none;">

                                    <table class="templateTable">
                                        <!-- TEMPLATE: CONDITION: START -->
                                        <tr class="conditionDisplayContainer">

                                            <td width="1%" class="conditionBindingContainer"
                                                style="padding: 4px 4px 4px 8px; vertical-align: middle;">

                                                <!-- Condition Binding -->
                                                <input name="BIND_MASKconditionSubelementMap[RULE_ID]"
                                                       type="RADIO_OR_CHECKBOX"
                                                       value="CONDITION_ID" class="checkbox conditionBinding"/>
                                                <input type="hidden" value="0"
                                                       name="BIND_MASK_conditionSubelementMap[RULE_ID]"/>

                                            </td>

                                            <td align="left" style="padding: 4px 4px 4px 8px; vertical-align: middle;">
                                                <!-- Condition Name -->
                                                <span class="conditionNameContainer">
                                    CONDITION_NAME:
                                </span>
                                            </td>

                                            <td align="left" style="padding: 4px 8px 4px 4px; vertical-align: middle;">

                                                <div style="display: inline-block; vertical-align: middle;">
                                    <span>
                                        <fmtSpring:message code="page.label.If"/> <span style="padding: 0px 2px;"
                                                                                        class="conditionVariableNameContainer">
                                            CONDITION_VARIABLE_NAME
                                        </span>
                                    </span>
                                                    <span class="conditionComparatorContainer">
                                        CONDITION_COMPARATOR_NAME
                                    </span>
                                                </div>

                                                <!-- Condition Value -->
                                                <!-- CHOOSE ONE: ruleConditionValueContainer or ruleConditionStaticContainer -->
                                                <div class="ruleConditionValueContainer"
                                                     style="display: inline-block; vertical-align: middle;">

                                                    <div id="BIND_MASKruleConditionBinding_RULE_ID_CONDITION_ID"
                                                         class="conditionValueBindingContainer"
                                                         isRangeValue="IS_RANGE_VALUE"
                                                         isDateValue="IS_DATE_VALUE">
                                                        <!-- Value String Binding -->
                                                        <input id="conditionValueString_RULE_ID_CONDITION_ID"
                                                               class="variableComparedToValue"
                                                               name="BIND_MASKconditionSubelementValues[CONDITION_ID]"
                                                               style="display:none;"/>
                                                    </div>
                                                    <div class="ruleConditionParameterizedContainer" style="display: none;">
                                                        <fmtSpring:message code="page.label.user.specified.values"/>
                                                    </div>
                                                </div>
                                                <div class="ruleConditionStaticContainer"
                                                     style="display: inline-block; vertical-align: middle;">
                                    <span style="padding: 0px 2px;">
                                        CONDITION_DISPLAY_VALUE
                                    </span>
                                                </div>

                                                <div style="display: inline-block; vertical-align: middle;">
                                                    CONDITION_FILTER_DISPLAY_VALUE
                                                </div>

                                            </td>

                                        </tr>

                                        <!-- IF: CONDITION TYPE 1 or 2 -->
                                        <tr class="conditionRelationshipIndicator" style="display: none;">
                                            <td align="left" colspan="3">
                                                <div style="padding-left: 9px; font-size: 11px;">CONDITION_RELATIONSHIP
                                                </div>
                                            </td>
                                        </tr>
                                    </table>

                                    <!-- TEMPLATE: CONDITION: END -->

                                </div>

                                <!-- TEMPLATES: END -->

                            </div>
                            <!-- Save and Cancel Buttons - Variable by referencing page -->
                            <div id="common-buttons" class="pddng-lv3 align-content-center">
                                <c:choose>
                                    <c:when test="${!empty param.contentObjectId}">
                                        <c:set var="requestRedirect"
                                               value="../content/content_object_edit_targeting.form?contentObjectId=${param.contentObjectId}"/>
                                        <msgpt:Button URL="javascript:submitWithParameter( 'contentObjectId', '${param.contentObjectId}');"
                                                      label="page.label.save" flowControl="true" primary="true"
                                                      icon="fa-save"/>
                                        <msgpt:Button URL="../content/content_object_edit_targeting.form?contentObjectId=${param.contentObjectId}"
                                                      label="page.label.cancel" flowControl="true"/>
                                    </c:when>
                                    <c:when test="${!empty param.insertId}">
                                        <msgpt:Button
                                                URL="javascript:submitWithParameter( 'insertId', '${param.insertId}');"
                                                label="page.label.save" flowControl="true" primary="true"
                                                icon="fa-save"/>
                                        <msgpt:Button URL="javascript:closeIframe()" label="page.label.cancel"
                                                      flowControl="true"/>
                                    </c:when>
                                    <c:when test="${!empty param.tagId}">
                                        <c:set var="requestRedirect"
                                               value="../tpadmin/tag_targeting_edit.form?tagId=${param.tagId}"/>
                                        <msgpt:Button URL="javascript:submitWithParameter( 'tagId', '${param.tagId}');"
                                                      label="page.label.save" flowControl="true" primary="true"
                                                      icon="fa-save"/>
                                        <msgpt:Button URL="javascript:closeIframe()" label="page.label.cancel"
                                                      flowControl="true"/>
                                    </c:when>
                                    <c:when test="${!empty param.attachmentId}">
                                        <msgpt:Button
                                                URL="javascript:submitWithParameter( 'attachmentId', '${param.attachmentId}');"
                                                label="page.label.save" flowControl="true" primary="true" icon="fa-save"/>
                                        <msgpt:Button URL="javascript:closeIframe()" label="page.label.cancel"
                                                      flowControl="true"/>
                                    </c:when>
                                    <c:when test="${!empty param.touchpointTargetingId}">
                                        <c:set var="requestRedirect"
                                               value="../tpadmin/touchpoint_targeting_edit.form?touchpointTargetingId=${param.touchpointTargetingId}"/>
                                        <msgpt:Button
                                                URL="javascript:submitWithParameter( 'touchpointTargetingId', '${param.touchpointTargetingId}');"
                                                label="page.label.save" flowControl="true" primary="true" icon="fa-save"/>
                                        <msgpt:Button URL="javascript:closeIframe()" label="page.label.cancel"
                                                      flowControl="true"/>
                                    </c:when>
                                    <c:when test="${!empty param.contentTargetingId}">
                                        <c:set var="requestRedirect"
                                               value="../tpadmin/content_targeting_edit.form?contentTargetingId=${param.contentTargetingId}"/>
                                        <msgpt:Button
                                                URL="javascript:submitWithParameter( 'contentTargetingId', '${param.contentTargetingId}');"
                                                label="page.label.save" flowControl="true" primary="true" icon="fa-save"/>
                                        <msgpt:Button URL="javascript:closeIframe()" label="page.label.cancel"
                                                      flowControl="true"/>
                                    </c:when>
                                    <c:otherwise>
                                        <msgpt:Button URL="javascript:closeIframe()" label="page.label.cancel"
                                                      flowControl="true"/>
                                        <div class="inline-block-item mrgn-left-lv2">
                                            <msgpt:Button URL="javascript:document.getElementById('command').submit();"
                                                          label="page.label.save" flowControl="true" primary="true"
                                                          disabled="${command.referencedParameterized}" icon="fa-save"/>
                                        </div>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </form:form>
                    </c:if>
                </msgpt:BodyNew>
            </c:when>
            <c:otherwise>
                <msgpt:BodyNew cssClass="targeting-body">
                    <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_TARGET %>"/>
                    <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_TARGET %>"/>
                    <div id="content" class="d-table-row h-100">
                        <div class="container mt-5 shiftContainer">
                            <c:if test="${not empty param.saveSuccess && param.saveSuccess == 'true'}">
                                <msgpt:Information type="success">
                                    <fmtSpring:message code="page.label.save.complete"/>
                                </msgpt:Information>
                            </c:if>
                            <h1 class="h4 pb-2 mb-4">
                                <div id="popupHeaderTitle">
                                    <span class="titleText">
                                        <c:choose>
                                            <c:when test="${not empty param.targetgroupid}">
                                                <fmtSpring:message code="page.label.edit.target.group"/>
                                            </c:when>
                                            <c:otherwise>
                                                <fmtSpring:message code="page.label.Add.target.group"/>
                                            </c:otherwise>
                                        </c:choose>
                                    </span>
                                </div>
                            </h1>
                            <form:form modelAttribute="command" method="post">
                                <div class="box-shadow-4 rounded bg-white">
                                    <div class="position-relative">
                                        <div class="border-bottom" data-sticky="top"
                                             data-fixedclass="container px-0 box-shadow-4 bg-white border-white rounded-top">
                                            <div class="d-flex align-items-center mx-4 bg-white rounded-top">
                                                <div class="square-lg rounded-circle bg-secondary-lightest text-center text-secondary p-2 mr-3">
                                                    <i class="fad fa-bullseye-pointer fa-lg align-middle"
                                                       aria-hidden="true"></i>
                                                </div>
                                                <hgroup class="my-3">
                                                    <h1 class="h5 mb-1">
                                                        <fmtSpring:message
                                                                code="page.text.targeting_rules_and_conditions"/>
                                                    </h1>
                                                    <h2 class="h6 m-0 text-muted font-weight-normal fs-xs">
                                                        <fmtSpring:message code="page.text.a_target_group_is"/>
                                                    </h2>
                                                </hgroup>
                                                <div class="d-flex ml-auto my-3">
                                                    <c:choose>
                                                        <c:when test="${!empty param.insertId}">
                                                            <button type="button"
                                                                    class="btn btn-outline-light post-trigger text-body"
                                                                    onclick="common.history.goBack()">
                                                                <i class="far fa-times-circle mr-2"
                                                                   aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.cancel"/>
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-primary post-trigger ml-3"
                                                                    onclick="submitWithParameter( 'insertId', '${param.insertId}');">
                                                                <i class="far fa-save mr-2" aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.save"/>
                                                            </button>
                                                        </c:when>
                                                        <c:when test="${!empty param.tagId}">
                                                            <c:set var="requestRedirect"
                                                                   value="../tpadmin/tag_targeting_edit.form?tagId=${param.tagId}"/>
                                                            <button type="button"
                                                                    class="btn btn-outline-light post-trigger text-body"
                                                                    onclick="common.history.goBack()">
                                                                <i class="far fa-times-circle mr-2"
                                                                   aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.cancel"/>
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-primary post-trigger ml-3"
                                                                    onclick="submitWithParameter( 'tagId', '${param.tagId}');">
                                                                <i class="far fa-save mr-2" aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.save"/>
                                                            </button>
                                                        </c:when>
                                                        <c:when test="${!empty param.attachmentId}">
                                                            <button type="button"
                                                                    class="btn btn-outline-light post-trigger text-body"
                                                                    onclick="common.history.goBack()">
                                                                <i class="far fa-times-circle mr-2"
                                                                   aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.cancel"/>
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-primary post-trigger ml-3"
                                                                    onclick="submitWithParameter( 'attachmentId', '${param.attachmentId}');">
                                                                <i class="far fa-save mr-2" aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.save"/>
                                                            </button>
                                                        </c:when>
                                                        <c:when test="${!empty param.touchpointTargetingId}">
                                                            <c:set var="requestRedirect"
                                                                   value="../tpadmin/touchpoint_targeting_edit.form?touchpointTargetingId=${param.touchpointTargetingId}"/>
                                                            <button type="button"
                                                                    class="btn btn-outline-light post-trigger text-body"
                                                                    onclick="javascriptHref('${contextPath}/tpadmin/touchpoint_targeting_edit.form?touchpointTargetingId=${param.touchpointTargetingId}')">
                                                                <i class="far fa-times-circle mr-2"
                                                                   aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.cancel"/>
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-primary post-trigger ml-3"
                                                                    onclick="submitWithParameter( 'touchpointTargetingId', '${param.touchpointTargetingId}');">
                                                                <i class="far fa-save mr-2" aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.save"/>
                                                            </button>
                                                        </c:when>
                                                        <c:when test="${!empty param.contentTargetingId}">
                                                            <c:set var="requestRedirect"
                                                                   value="../tpadmin/content_targeting_edit.form?contentTargetingId=${param.contentTargetingId}"/>
                                                            <button type="button"
                                                                    class="btn btn-outline-light post-trigger text-body"
                                                                    onclick="common.history.goBack()">
                                                                <i class="far fa-times-circle mr-2"
                                                                   aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.cancel"/>
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-primary post-trigger ml-3"
                                                                    onclick="javascript:submitWithParameter( 'contentTargetingId', '${param.contentTargetingId}');">
                                                                <i class="far fa-save mr-2" aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.save"/>
                                                            </button>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <button id="targetGroupEditCancelBtn" type="button"
                                                                    class="btn btn-outline-light text-body post-trigger">
                                                                <i class="far fa-times-circle mr-2"
                                                                   aria-hidden="true"></i><fmtSpring:message
                                                                    code="page.label.cancel"/>
                                                            </button>
                                                            <div class="btn-group ml-3">
                                                                <button type="button"
                                                                        class="btn btn-primary btn-placeholder" disabled>
                                                                    <i class="fad fa-spinner-third fa-lg fa-spin align-middle mx-3"
                                                                       aria-hidden="true"></i>
                                                                </button>
                                                                <select id="targetGroupEditSaveBtnGroup"
                                                                        class="complex-combobox-select"
                                                                        aria-label="${msgpt:getMessage("page.label.actions")}"
                                                                        data-toggle="complex-combobox"
                                                                        data-combobox-class="btn-primary">
                                                                    <c:if test="${not empty param.cfSource && param.cfSource == 'true'}">
                                                                        <option id="btnSaveAndBack" value="saveandgoback"
                                                                                class="${command.referencedParameterized?'disabled':''}"
                                                                                data-icon="far fa-arrow-left"
                                                                                data-customitem="true"><fmtSpring:message
                                                                                code="page.label.save.back"/>
                                                                        </option>
                                                                    </c:if>
                                                                    <option id="btnSaveAndStay" value="saveandstay"
                                                                            class="${command.referencedParameterized?'disabled':''}"
                                                                            data-icon="fas fa-save"><fmtSpring:message
                                                                            code="page.label.save"/>
                                                                    </option>
                                                                    <option id="btnSaveAndAdd" value="saveandadd"
                                                                            class="${command.referencedParameterized?'disabled':''}"
                                                                            data-icon="far fa-plus"><fmtSpring:message
                                                                            code="page.label.save.and.add"/>
                                                                    </option>
                                                                    <option id="btnSaveAndGoToList" value="saveandgotolist"
                                                                            class="${command.referencedParameterized?'disabled':''}"
                                                                            data-icon="far fa-list-alt"><fmtSpring:message
                                                                            code="page.label.save.and.go.to.list"/>
                                                                    </option>
                                                                </select>
                                                                <button type="button"
                                                                        class="btn btn-primary dropdown-toggle dropdown-toggle-split"
                                                                        data-toggle="dropdown" aria-haspopup="true"
                                                                        aria-expanded="false" disabled>
                                                                        <span class="sr-only"><fmtSpring:message
                                                                                code="page.text.toggle.dropdown"/></span>
                                                                </button>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row m-0">
                                        <div class="col-auto d-flex">
                                            <div class="p-3 d-flex align-items-stretch">
                                                <div class="bg-lightest my-3 p-3 rounded">
                                                    <div class="position-relative p-3">
                                                        <div data-sticky="top" data-offset="6.5625"
                                                             data-indexclass="z-sticky"
                                                             data-handlewidth="true"
                                                             class="position-relative bg-white rounded box-shadow-2 rulesSearchContainer">
                                                            <div class="bg-ribbon position-absolute rounded-top py-1 w-100"
                                                                 role="presentation"></div>
                                                            <div class="pt-4 px-4 pb-3">
                                                                <h2 class="h6 text-primary text-uppercase mb-3">
                                                                    <fmtSpring:message
                                                                            code="page.label.rule.list"/>
                                                                </h2>
                                                                <div class="form-group position-relative mt-1 mb-0">
                                                                    <input id="ruleSearchInput" autocomplete="off"
                                                                           class="form-control bg-lightest has-control-l has-control-r-2 border-0 ${viewMetatagsPermission?'':'inactive'}"
                                                                           placeholder="${msgpt:getMessage('page.label.search')}"
                                                                           aria-label="${msgpt:getMessage('page.label.search')}">
                                                                    <button id="advancedSearchIcon"
                                                                            class="btn r-control-2 text-dark w-auto h-auto advancedSearchIconDiv persistedClass"
                                                                            type="button" data-toggle="button"
                                                                            aria-pressed="false"
                                                                            aria-label="${msgpt:getMessage('page.label.advanced.search')}">
                                                                        <i class="far fa-crosshairs"
                                                                           aria-hidden="true"></i>
                                                                    </button>
                                                                    <i class="far fa-search text-body fs-sm py-1 mt-2 ml-3 position-absolute top-0"
                                                                       aria-hidden="true"></i>
                                                                </div>
                                                            </div>
                                                            <div class="pb-3">
                                                                <div class="ruleListContainerWrapper">
                                                                    <div id="ruleListContainerLoading"
                                                                         class="position-absolute px-4 py-2">
                                                                        <i class="fas fa-spin fa-spinner text-secondary mr-2"></i>
                                                                        <fmtSpring:message
                                                                                code="page.label.loading.rules"/>
                                                                    </div>
                                                                    <div id="ruleListContainerNone"
                                                                         class="position-absolute px-4 py-2 text-muted"
                                                                         style="display: none">
                                                                        <fmtSpring:message
                                                                                code="page.label.rules.none.found"/>
                                                                    </div>
                                                                    <div class="ruleListContainer">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="px-3">
                                                <div class="border-0 py-3 ruleContentContainer">
                                                    <div class="my-3 mr-3 pr-3 py-3">
                                                        <fmtSpring:hasBindErrors name="command">
                                                            <form:errors path="*">
                                                                <msgpt:Information errorMsgs="${messages}"
                                                                                   type="error"/>
                                                            </form:errors>
                                                        </fmtSpring:hasBindErrors>
                                                        <!-- INFO: Reference parameterized rule -->
                                                        <c:if test="${command.referencedParameterized}">
                                                            <div class="alert alert-info my-3">
                                                                <strong class="mr-2">
                                                                    <i class="fas fa-info-circle fa-lg mr-2"
                                                                       aria-hidden="true"></i><fmtSpring:message
                                                                        code="page.label.info"/>:
                                                                </strong>
                                                                <fmtSpring:message
                                                                        code="page.text.target.group.is.parameterized"/>
                                                            </div>
                                                        </c:if>
                                                        <div class="py-3">
                                                            <c:set var="pageRequestType" value="0"/>
                                                            <c:set var="targetGroupDisabled"
                                                                   value="${command.referencedParameterized}"/>
                                                            <input type="hidden" id="isModifiable"
                                                                   value="${not command.referenced}"/>
                                                            <div class="row align-items-middle">
                                                                <c:if test="${displaySegmentationAnalysis}">
                                                                    <div class="col-3">
                                                                        <div class="targetingSegmentationAnalysis">
                                                                            <div>
                                                                                <fmtSpring:message
                                                                                        code="page.label.reach"/>
                                                                            </div>
                                                                            <c:choose>
                                                                                <c:when test="${segmentationInProcess}">
                                                                                    <em id="segStatusPollingContainer"></em><br>
                                                                                    <em id="segResultContainer">--</em><br>
                                                                                </c:when>
                                                                                <c:otherwise>
                                                                                    <em><c:out
                                                                                            value="${command.segmentationPercentage}"/></em>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                        </div>
                                                                    </div>
                                                                </c:if>
                                                                <div class="col">
                                                                    <div class="form-group row align-items-xl-center">
                                                                        <label for="nameText" class="col-xl-2">
                                                                            <fmtSpring:message code="page.label.name"/>
                                                                        </label>
                                                                        <div class="col-xl-10">
                                                                            <msgpt:InputFilter type="simpleName">
                                                                                <form:input cssClass="form-control"
                                                                                            path="nameText"/>
                                                                            </msgpt:InputFilter>
                                                                        </div>
                                                                    </div>
                                                                    <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
                                                                        <div class="form-group row align-items-xl-center mb-2">
                                                                            <label for="targetGroupMetatags"
                                                                                   class="col-xl-2">
                                                                                <fmtSpring:message
                                                                                        code="page.label.metatags"/>
                                                                            </label>
                                                                            <div class="col-xl-10">
                                                                                <msgpt:InputFilter type="description">
                                                                                    <form:input cssClass="form-control"
                                                                                                id="targetGroupMetatags"
                                                                                                path="metatags"/>
                                                                                </msgpt:InputFilter>
                                                                            </div>
                                                                        </div>
                                                                    </msgpt:IfAuthGranted>
                                                                </div>
                                                            </div>

                                                        </div>
                                                        <div class="d-flex align-items-center mb-4 pt-4 border-top border-thick">
                                                            <h3 class="h6 flex-shrink-0 text-uppercase mr-3 mb-0 pr-1">
                                                                <fmtSpring:message
                                                                        code="page.label.targetgroup.details"/></h3>
                                                            <span class="font-weight-normal px-3 border-left ruleRelationshipToggleDisplay"
                                                                  style="display: none;">
                                                                <span class="d-inline-block mx-1">
                                                                    <span class="custom-control custom-switch d-inline mr-2">
                                                                        <form:checkbox
                                                                                id="ruleRelationshipToggle"
                                                                                class="custom-control-input targetGroupRelationshipToggle"
                                                                                path="conditionRelationship"
                                                                                data-uncheckedtext="${msgpt:getMessage('page.label.at.least.one')}"
                                                                                data-checkedtext="${msgpt:getMessage('page.label.all')}"/>
                                                                        <label class="custom-control-label font-weight-bold"
                                                                               for="ruleRelationshipToggle">
                                                                                ${msgpt:getMessage('page.label.at.least.one')}
                                                                        </label>
                                                                    </span>
                                                                    <span class="text-muted">
                                                                        <fmtSpring:message
                                                                                code="page.text.must.pass.for.target.group.to.qualify"/>
                                                                    </span>
                                                                </span>
                                                            </span>
                                                            <div class="ml-auto" data-toggle="tooltip"
                                                                 title="${msgpt:getMessage("page.text.toggle.touchpoint.assignment.panel")}">
                                                                <button id="toggleTpAssignmentPanelBtnNew"
                                                                        class="btn btn-sm btn-outline-light text-body"
                                                                        type="button"
                                                                        data-toggle="modal"
                                                                        data-target="#tpAssignmentPanelContainer">
                                                                    <fmtSpring:message code="page.label.visibility"/>
                                                                </button>
                                                            </div>
                                                            <c:if test="${hasTaskViewPermission and not empty param.targetgroupid}">
                                                                <div class="fs-md ml-3 mr-2 position-relative">
                                                                    <div id="taskIndicator" class="pl-1"></div>
                                                                </div>
                                                            </c:if>
                                                        </div>
                                                        <div class="ruleContainer">
                                                            <div class="droppable-item-placement rounded py-2 px-3 border border-dashed border-thick mb-4">
                                                                <i class="fas fa-long-arrow-right text-primary ml-1 mt-2 d-inline-block"
                                                                   aria-hidden="true"></i>
                                                            </div>
                                                            <div id="info_noActiveRules"
                                                                 class="border border-thick border-dashed rounded py-5 text-center"
                                                                 style="display: none;">
                                                                <span class="fa-container-shadow fa-container-shadow-secondary mt-5">
                                                                    <i class="fad fa-ruler"
                                                                       aria-hidden="true"></i>
                                                                </span>
                                                                <h4 class="h5 text-capitalize py-1 mt-2 mb-0">
                                                                    <fmtSpring:message
                                                                            code="page.label.targetgroup.details"/>
                                                                </h4>
                                                                <p class="mx-auto mb-5 fs-xs text-muted w-50">
                                                                    <fmtSpring:message
                                                                            code="page.text.drag.and.drop.rule.into.target.group"/></p>
                                                            </div>
                                                            <form:hidden path="containsConditionElements"/>
                                                            <div class="ruleItemsContainer">
                                                                <c:forEach var="element"
                                                                           items="${command.conditionElements}">
                                                                    <div id="ruleContainer_${element.id}"
                                                                         class="ruleDisplayContainer stageContainer">
                                                                        <table width="100%" cellspacing="0"
                                                                               cellpadding="0"
                                                                               border="0"
                                                                               class="tableOutline ruleConditionTable">
                                                                            <tr class="ruleHeaderContainer">

                                                                                <!-- Rule Name -->
                                                                                <td width="99%" align="left"
                                                                                    class="tableIndicatorCol cellTopLeft"
                                                                                    style="border-right: none;">
                                                                                    <div class="stageLabel"
                                                                                         data-toggle="tooltip"
                                                                                         title="<c:out value="${element.name}"/>">
                                                                                        <c:out value="${element.name}"/>
                                                                                    </div>

                                                                                    <!-- Rule Binding -->
                                                                                    <fmtSpring:bind
                                                                                            path="command.conditionElements">
                                                                                        <input type="hidden"
                                                                                               name="${status.expression}"
                                                                                               type="checkbox"
                                                                                               value="${element.id}"
                                                                                               class="checkbox"
                                                                                               checked="checked"/>
                                                                                    </fmtSpring:bind>
                                                                                    <input type="hidden" value="1"
                                                                                           name="_conditionElements"/>
                                                                                </td>

                                                                                <!-- Rule Actions: Edit, Remove -->
                                                                                <td class="tableIndicatorCol cellTopRight"
                                                                                    style="border-right: none;">
                                                                                    <table width="100%" cellspacing="0"
                                                                                           cellpadding="0"
                                                                                           border="0"
                                                                                           class="innerCellTable ruleActionsContainer"
                                                                                           style="display:none;">
                                                                                        <tr>
                                                                                            <td class="ruleEditButtonContainer">
                                                                                                <input id="ruleEditButton_${element.id}"
                                                                                                       title="${msgpt:getMessage('page.label.edit')}"
                                                                                                       type="button"
                                                                                                       class="ruleEditButton ruleActionBtn"
                                                                                                       style="display: none;"/>
                                                                                            </td>
                                                                                            <td class="ruleDoneButtonContainer"
                                                                                                style="display: none;">
                                                                                                <input id="ruleDoneButton_${element.id}"
                                                                                                       title="${msgpt:getMessage('page.label.done')}"
                                                                                                       type="button"
                                                                                                       class="ruleDoneButton ruleActionBtn"
                                                                                                       style="display: none;"/>
                                                                                            </td>
                                                                                            <td style="vertical-align: middle; padding-left: 6px; padding-top: 4px; padding-right: 16px;">
                                                                                                <div class="removeIcon">
                                                                                                    <i class="fa fa-times"
                                                                                                       aria-hidden="true"></i>
                                                                                                </div>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>

                                                                                <!-- Rule Display -->
                                                                                <td align="left"
                                                                                    class="tableContentCol cellBottomLeft cellBottomRight"
                                                                                    colspan="2"
                                                                                    style="border-bottom: none;">

                                                                                    <c:set var="ruleCanBeParameterized"
                                                                                           value="false"/>

                                                                                    <table cellspacing="0"
                                                                                           cellpadding="0"
                                                                                           border="0">
                                                                                        <c:forEach var="subelement"
                                                                                                   items="${element.subElements}"
                                                                                                   varStatus="subelementCount">

                                                                                            <tr class="conditionDisplayContainer"
                                                                                                style="${msgpt:containsValue(command.conditionSubelementMap[element.id], subelement.id) ? '' : 'display:none;'}">

                                                                                                <td width="1%"
                                                                                                    class="conditionBindingContainer"
                                                                                                    style="padding: 4px 4px 4px 8px; vertical-align: top; display: none;">

                                                                                                    <!-- Condition Binding -->
                                                                                                    <c:set var="inputType"
                                                                                                           value="${subelement.conditionType.id == 1 ? 'radio' : 'checkbox'}"/>
                                                                                                    <fmtSpring:bind
                                                                                                            path="command.conditionSubelementMap[${element.id}]">
                                                                                                        <input name="${status.expression}"
                                                                                                               type="${inputType}"
                                                                                                               value="${subelement.id}"
                                                                                                               class="checkbox conditionBinding" ${msgpt:containsValue(status.value, subelement.id) ? 'checked="checked"' : ''} />
                                                                                                    </fmtSpring:bind>
                                                                                                    <input type="hidden"
                                                                                                           value="${msgpt:containsValue(command.conditionSubelementMap[element.id], subelement.id) ? 1: 0 }"
                                                                                                           name="_conditionSubelementMap[${element.id}]"/>

                                                                                                </td>

                                                                                                <td align="left"
                                                                                                    style="padding: 4px 4px 4px 8px; vertical-align: top;">
                                                                                                    <!-- Condition Name -->
                                                                                                    <span class="conditionNameContainer">
                                                                <c:out value="${subelement.name}"/>:
                                                            </span>
                                                                                                </td>

                                                                                                <td align="left"
                                                                                                    style="padding: 4px 8px 4px 4px; vertical-align: top;">

                                                                                                    <div style="display: inline-block; vertical-align: top;">
                                                                <span>
                                                                    <fmtSpring:message
                                                                            code="page.label.If"/> <span
                                                                        style="padding: 0px 2px;"
                                                                        class="conditionVariableNameContainer">
                                                                        <c:out value="${subelement.dataElementVariable.displayName}"/>
                                                                    </span>
                                                                </span>
                                                                                                        <span class="conditionComparatorContainer">
                                                                    <fmtSpring:message
                                                                            code="${subelement.dataComparison.name}"/>
                                                                </span>
                                                                                                    </div>

                                                                                                    <!-- Condition Value -->
                                                                                                    <c:choose>
                                                                                                        <c:when test="${subelement.parameterized == true}">

                                                                                                            <c:set var="ruleCanBeParameterized"
                                                                                                                   value="true"/>

                                                                                                            <div style="display: inline-block; vertical-align: middle;">
                                                                                                                <c:set var="subElementType"
                                                                                                                       value="${command.conditionSubelementValueTypes[subelement.id]}"/>
                                                                                                                <div id="ruleConditionBinding_${element.id}_${subelement.id}"
                                                                                                                     class="conditionValueBindingContainer"
                                                                                                                     isRangeValue="${subElementType eq paramSimpleRange || subElementType eq paramDateRange}"
                                                                                                                     isDateValue="${subElementType eq paramDate || subElementType eq paramDateRange}"
                                                                                                                    ${parameterizeRuleMap[element.id]?'style="display: none;"':''}>
                                                                                                                    <!-- Value String Binding -->
                                                                                                                    <form:input
                                                                                                                            id="conditionValueString_${element.id}_${subelement.id}"
                                                                                                                            cssClass="variableComparedToValue"
                                                                                                                            path="conditionSubelementValues[${subelement.id}]"
                                                                                                                            cssStyle="display:none;"/>
                                                                                                                </div>
                                                                                                                <div class="ruleConditionParameterizedContainer" ${parameterizeRuleMap[element.id]?'':'style="display: none;"'}>
                                                                                                                    <fmtSpring:message
                                                                                                                            code="page.label.user.specified.values"/>
                                                                                                                </div>
                                                                                                            </div>

                                                                                                        </c:when>
                                                                                                        <c:otherwise>

                                                                                                            <div style="display: inline-block; vertical-align: middle;">
                                                                        <span style="padding: 0px 2px;">
                                                                            <c:choose>
                                                                                <c:when test="${subelement.isValueComparison}">
                                                                                    '<c:out
                                                                                        value="${subelement.dataElementDisplayValue}"
                                                                                        escapeXml="false"/>'
                                                                                </c:when>
                                                                                <c:otherwise>
                                                                                    <c:out value="${subelement.dataElementDisplayValue}"
                                                                                           escapeXml="false"/>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                        </span>
                                                                                                            </div>

                                                                                                        </c:otherwise>
                                                                                                    </c:choose>

                                                                                                    <c:if test="${subelement.filterCondition != null}">
                                                                                                        <div style="display: inline-block; vertical-align: middle;">
                                                                                                            <fmtSpring:message
                                                                                                                    code="page.label.WHEN"/>
                                                                                                            <c:out value="${subelement.filterCondition.displayValue}"
                                                                                                                   escapeXml="false"/>
                                                                                                        </div>
                                                                                                    </c:if>

                                                                                                </td>

                                                                                            </tr>

                                                                                            <c:if test="${element.conditionType.id != 1}">
                                                                                                <tr class="conditionRelationshipIndicator"
                                                                                                    style="display: none;">
                                                                                                    <td align="left"
                                                                                                        colspan="3">
                                                                                                        <div style="padding-left: 9px; font-size: 11px;">
                                                                                                            <fmtSpring:message
                                                                                                                    code="${element.conditionType.id == 2 ? 'page.label.and' : 'page.label.or'}"/></div>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </c:if>

                                                                                        </c:forEach>
                                                                                        <!-- foreach: selected condition -->
                                                                                    </table>

                                                                                </td>

                                                                            </tr>

                                                                            <c:if test="${ruleCanBeParameterized == 'true'}">
                                                                                <tr id="parameterizeRuleToggleContainer_${element.id}"
                                                                                    class="ruleCanBeParameterized"
                                                                                    style="display: none;">
                                                                                    <td class="tableContentCol cellBottomLeft cellBottomRight"
                                                                                        colspan="2"
                                                                                        style="border-top: 1px solid #fff; vertical-align: middle;">

                                                                                        <div style="display: inline-block; vertical-align: middle; padding-left: 10px; padding-right: 8px;">
                                                                                            <fmtSpring:message
                                                                                                    code="page.text.user.specified.values"/></div>
                                                                                        <div style="display: inline-block; vertical-align: middle;">
                                                                                            <form:checkbox
                                                                                                    path="parameterizeRuleMap[${element.id}]"
                                                                                                    id="parameterizeRuleBinding_${element.id}"
                                                                                                    title="No;Yes"
                                                                                                    value="${parameterizeRuleMap[element.id]?parameterizeRuleMap[element.id]:'true'}"
                                                                                                    disabled="${command.referenced}"/>

                                                                                        </div>
                                                                                        <c:if test="${command.referenced}">
                                                                                        <div style="display: inline-block; vertical-align: middle;"
                                                                                             data-toggle="tooltip"
                                                                                             title="${msgpt:getMessage("page.text.target.group.may_not_be_enabled")}">
                                                                                            <span class="InfoSysContainer_info"
                                                                                                  style="margin-left: 5px; padding-right: 10px;"><fmtSpring:message
                                                                                                    code="page.text.locked.target.group.referenced"/></span>
                                                                                        </div>
                                                                                        </c:if>
                                                                                    <td>
                                                                                </tr>
                                                                            </c:if>

                                                                        </table>
                                                                    </div>
                                                                    <div class="ruleRelationshipIndicator"
                                                                         style="display: none;"></div>
                                                                </c:forEach>    <!-- foreach: selected rule -->

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="tpAssignmentPanelContainer" class="modal fade"
                                     tabindex="-1" role="dialog">
                                    <div class="modal-dialog modal-lg" role="document">
                                        <div class="modal-content">
                                            <div class="bg-ribbon position-absolute rounded-top py-1 w-100"
                                                 role="presentation"></div>
                                            <div class="modal-header border-0">
                                                <h4 class="modal-title"
                                                    id="exampleModalLabel"><fmtSpring:message
                                                        code="page.label.touchpoint.assignments"/></h4>
                                                <button type="button" class="close"
                                                        data-dismiss="modal"
                                                        aria-label="${msgpt:getMessage('page.label.close')}">
                                                    <i class="far fa-times m-2"
                                                       aria-hidden="true"></i>
                                                </button>
                                            </div>
                                            <div class="modal-body pt-2">
                                                <div id="touchpointAssociations"
                                                     class="drag-and-drop row no-gutters align-items-stretch">
                                                    <div class="col-5">
                                                        <h5><fmtSpring:message code="page.label.available.touchpoints"/></h5>
                                                        <div class="droppable-section">
                                                            <div class="form-control p-0 bg-lightest rounded-bottom-0 h-auto">
                                                                <div class="position-relative z-index-1">
                                                                    <input class="form-control form-control-lg bg-lightest px-5 border-0 shadow-none"
                                                                           type="text"
                                                                           data-toggle="tagcloud" data-cloud-type="4"
                                                                           aria-label="${msgpt:getMessage('page.label.search.for')}"
                                                                           placeholder="${msgpt:getMessage('page.label.search')}"
                                                                           disabled>
                                                                    <button class="btn btn-lg btn-icon-toggle bg-transparent shadow-none py-0 px-4 mr-2 position-absolute h-100 top-0 left-0"
                                                                            type="button"
                                                                            aria-label="${msgpt:getMessage('page.label.search')}"
                                                                            tabindex="-1" disabled>
                                                                        <i class="far fa-search"
                                                                           aria-hidden="true"></i>
                                                                        <i class="far fa-times text-primary"
                                                                           aria-hidden="true"></i>
                                                                    </button>
                                                                </div>
                                                                <div class="pl-3 mb-1 text-muted">
                                                                    <div class="d-inline-block custom-control custom-checkbox w-auto no-text mr-3">
                                                                        <input type="checkbox" class="custom-control-input selectAll" id="selectAll" style="min-height: auto;">
                                                                        <label class="custom-control-label pl-2" for="selectAll">Select All</label>
                                                                    </div>
                                                                    <div class="d-inline-block custom-control custom-checkbox w-auto no-text mr-3">
                                                                        <input type="checkbox" class="custom-control-input viewSelected" id="viewSelected" style="min-height: auto;">
                                                                        <label class="custom-control-label pl-2" for="viewSelected">View Selected</label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="position-relative flex-grow-1 border border-top-0 border-light rounded-lg rounded-top-0 overflow-hidden">
                                                                <div class="draggable-list-wrapper">
                                                                    <div class="draggable-list"></div>
                                                                    <div class="draggable-loading">
                                                                        <div class="progress-loader progress-loader-lg">
                                                                            <i class="progress-loader-icon far fa-spinner-third"
                                                                               aria-hidden="true"></i>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col align-self-center">
                                                        <div class="text-center text-muted">
                                                            <div class="d-inline-block square-lg bg-secondary-lightest rounded-circle p-2 text-secondary fs-md">
                                                                <i class="far fa-hand-paper drag-and-drop-indicator"
                                                                   aria-hidden="true"></i>
                                                            </div>
                                                            <div class="mt-2 mb-n1 fs-xs">
                                                                <fmtSpring:message
                                                                        code="client_messages.text.drag_and_drop"/>
                                                            </div>
                                                            <i class="fas fa-arrows-h" aria-hidden="true"></i>
                                                        </div>
                                                        <div class="drag-and-drop-datasource">
                                                            <c:if test="${fn:length(availableTouchpoints) > 0}">
                                                                <c:forEach var="touchpoint"
                                                                           items="${availableTouchpoints}">
                                                                    <form:checkbox id="touchpointCheck${touchpoint.id}"
                                                                                   path="touchpointAssignments"
                                                                                   value="${touchpoint.id}"
                                                                                   aria-label="${touchpoint.name}"
                                                                                   data-filtervalue="${touchpoint.name}"
                                                                                   data-filtermetatags="${touchpoint.metatags}"
                                                                                   data-level="${touchpoint.projectDepth}"/>
                                                                </c:forEach>
                                                            </c:if>
                                                        </div>
                                                    </div>
                                                    <div class="col-5">
                                                        <h5><fmtSpring:message code="page.label.selected.touchpoints"/></h5>
                                                        <div class="droppable-section">
                                                            <div class="form-control p-0 bg-lightest rounded-bottom-0 h-auto">
                                                                <div class="position-relative z-index-1">
                                                                    <input class="form-control form-control-lg bg-lightest px-5 border-0 shadow-none"
                                                                           type="text"
                                                                           data-toggle="tagcloud" data-cloud-type="4"
                                                                           aria-label="${msgpt:getMessage('page.label.search.for')}"
                                                                           placeholder="${msgpt:getMessage('page.label.search')}"
                                                                           disabled>
                                                                    <button class="btn btn-lg btn-icon-toggle bg-transparent shadow-none py-0 px-4 mr-2 position-absolute h-100 top-0 left-0"
                                                                            type="button"
                                                                            aria-label="${msgpt:getMessage('page.label.search')}"
                                                                            tabindex="-1" disabled>
                                                                        <i class="far fa-search"
                                                                           aria-hidden="true"></i>
                                                                        <i class="far fa-times text-primary"
                                                                           aria-hidden="true"></i>
                                                                    </button>
                                                                </div>
                                                                <div class="pl-3 mb-1 text-muted">
                                                                    <div class="d-inline-block custom-control custom-checkbox w-auto no-text mr-3">
                                                                        <input type="checkbox" class="custom-control-input selectAll" id="selectAll2" style="min-height: auto;">
                                                                        <label class="custom-control-label pl-2" for="selectAll2">Select All</label>
                                                                    </div>
                                                                    <div class="d-inline-block custom-control custom-checkbox w-auto no-text mr-3">
                                                                        <input type="checkbox" class="custom-control-input viewSelected" id="viewSelected2" style="min-height: auto;">
                                                                        <label class="custom-control-label pl-2" for="viewSelected2">View Selected</label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="position-relative flex-grow-1 border border-top-0 border-light rounded-lg rounded-top-0 overflow-hidden">
                                                                <div class="draggable-list-wrapper">
                                                                    <div class="draggable-list"></div>
                                                                    <div class="draggable-loading">
                                                                        <div class="progress-loader progress-loader-lg">
                                                                            <i class="progress-loader-icon far fa-spinner-third"
                                                                               aria-hidden="true"></i>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- TEMPLATES: START -->
                                <div id="TEMPLATE_RULE" style="display: none;">

                                    <!-- TEMPLATE: RULE: START -->
                                    <div id="ruleContainer_RULE_ID" class="ruleDisplayContainer stageContainer">
                                        <table width="100%" cellspacing="0" cellpadding="0" border="0"
                                               class="tableOutline">
                                            <tr class="ruleHeaderContainer">

                                                <!-- Rule Name -->
                                                <td width="99%" align="left"
                                                    class="tableIndicatorCol cellTopLeft"
                                                    style="border-right: none;">
                                                    <div class="stageLabel" title="RULE_NAME">RULE_NAME</div>
                                                    <!-- Rule Binding -->
                                                    <input type="hidden" name="BIND_MASKconditionElements"
                                                           type="checkbox"
                                                           value="RULE_ID" class="checkbox" checked="checked"/>
                                                    <input type="hidden" value="1"
                                                           name="BIND_MASK_conditionElements"/>
                                                </td>

                                                <!-- Rule Actions: Edit, Remove -->
                                                <td class="tableIndicatorCol cellTopRight"
                                                    style="border-right: none;">
                                                    <table width="100%" cellspacing="0" cellpadding="0"
                                                           border="0"
                                                           class="innerCellTable ruleActionsContainer"
                                                           style="display:none;">
                                                        <tr>
                                                            <!-- IF: # conditions > 1 -->
                                                            <td class="ruleEditButtonContainer" align="center"
                                                                style="display: none; vertical-align: middle; padding-left: 4px; padding-right: 8px;">
                                                                <input id="ruleEditButton_RULE_ID"
                                                                       title="${msgpt:getMessage('page.label.edit')}"
                                                                       type="button"
                                                                       class="ruleEditButton ruleActionBtn"
                                                                       style="display: none;"/>
                                                            </td>
                                                            <td class="ruleDoneButtonContainer"
                                                                style="vertical-align: middle; padding-left: 4px; padding-right: 8px;">
                                                                <input id="ruleDoneButton_RULE_ID"
                                                                       title="${msgpt:getMessage('page.label.done')}"
                                                                       type="button"
                                                                       class="ruleDoneButton ruleActionBtn "
                                                                       style="display: none;"/>
                                                            </td>
                                                            <!-- END IF -->
                                                            <td style="vertical-align: middle; padding-left: 6px; padding-top: 4px; padding-right: 16px;">
                                                                <div class="removeIcon">
                                                                    <i class="fa fa-times"
                                                                       aria-hidden="true"></i>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>

                                                <!-- Rule Display -->
                                                <td class="tableContentCol cellBottomLeft cellBottomRight"
                                                    colspan="2"
                                                    style="border-bottom: none; vertical-align: middle;">
                                                    <table cellspacing="0" cellpadding="0" border="0"
                                                           class="ruleConditionTable">

                                                    </table>
                                                </td>

                                            </tr>

                                            <!-- IF: Rule can be parameterized -->
                                            <tr id="parameterizeRuleToggleContainer_RULE_ID"
                                                class="ruleCanBeParameterized">
                                                <td class="tableContentCol cellBottomLeft cellBottomRight"
                                                    colspan="2"
                                                    style="border-top: 1px solid #fff; vertical-align: middle;">

                                                    <div style="display: inline-block; vertical-align: middle; padding-left: 10px; padding-right: 8px;">
                                                        <fmtSpring:message
                                                                code="page.text.user.specified.values"/></div>
                                                    <div style="display: inline-block; vertical-align: middle;">
                                                        <input name="BIND_MASKparameterizeRuleMap[RULE_ID]"
                                                               type="checkbox"
                                                               id="parameterizeRuleBinding_RULE_ID"
                                                               title="No;Yes"
                                                               ${command.referenced ? 'disabled' : ''} />
                                                        <input type="hidden" value="0"
                                                               name="BIND_MASK_parameterizeRuleMap[RULE_ID]"/>
                                                    </div>
                                                    <c:if test="${command.referenced}">
                                                    <div style="display: inline-block; vertical-align: middle;"
                                                         data-toggle="tooltip"
                                                         title="${msgpt:getMessage("page.text.target.group.may_not_be_enabled")}">
                                                        <span class="InfoSysContainer_info"
                                                              style="margin-left: 5px; padding-right: 10px;"><fmtSpring:message
                                                                code="page.text.locked.target.group.referenced"/></span>
                                                    </div>
                                                    </c:if>

                                                <td>
                                            </tr>

                                        </table>
                                    </div>            <!-- .ruleDisplayContainer -->

                                    <div class="ruleRelationshipIndicator" style="display: none;"></div>
                                    <!-- TEMPLATE: RULE: END -->

                                </div>
                                <div id="TEMPLATE_CONDITION" style="display: none;">

                                    <table class="templateTable">
                                        <!-- TEMPLATE: CONDITION: START -->
                                        <tr class="conditionDisplayContainer">

                                            <td width="1%" class="conditionBindingContainer"
                                                style="padding: 4px 4px 4px 8px; vertical-align: middle;">

                                                <!-- Condition Binding -->
                                                <input name="BIND_MASKconditionSubelementMap[RULE_ID]"
                                                       type="RADIO_OR_CHECKBOX"
                                                       value="CONDITION_ID" class="checkbox conditionBinding"/>
                                                <input type="hidden" value="0"
                                                       name="BIND_MASK_conditionSubelementMap[RULE_ID]"/>

                                            </td>

                                            <td align="left"
                                                style="padding: 4px 4px 4px 8px; vertical-align: middle;">
                                                <!-- Condition Name -->
                                                <span class="conditionNameContainer">
                                CONDITION_NAME:
                            </span>
                                            </td>

                                            <td align="left"
                                                style="padding: 4px 8px 4px 4px; vertical-align: middle;">

                                                <div style="display: inline-block; vertical-align: middle;">
                                <span>
                                    <fmtSpring:message code="page.label.If"/> <span style="padding: 0px 2px;"
                                                                                    class="conditionVariableNameContainer">
                                        CONDITION_VARIABLE_NAME
                                    </span>
                                </span>
                                                    <span class="conditionComparatorContainer">
                                    CONDITION_COMPARATOR_NAME
                                </span>
                                                </div>

                                                <!-- Condition Value -->
                                                <!-- CHOOSE ONE: ruleConditionValueContainer or ruleConditionStaticContainer -->
                                                <div class="ruleConditionValueContainer"
                                                     style="display: inline-block; vertical-align: middle;">

                                                    <div id="BIND_MASKruleConditionBinding_RULE_ID_CONDITION_ID"
                                                         class="conditionValueBindingContainer"
                                                         isRangeValue="IS_RANGE_VALUE"
                                                         isDateValue="IS_DATE_VALUE">
                                                        <!-- Value String Binding -->
                                                        <input id="conditionValueString_RULE_ID_CONDITION_ID"
                                                               class="variableComparedToValue"
                                                               name="BIND_MASKconditionSubelementValues[CONDITION_ID]"
                                                               style="display:none;"/>
                                                    </div>
                                                    <div class="ruleConditionParameterizedContainer"
                                                         style="display: none;">
                                                        <fmtSpring:message
                                                                code="page.label.user.specified.values"/>
                                                    </div>
                                                </div>
                                                <div class="ruleConditionStaticContainer"
                                                     style="display: inline-block; vertical-align: middle;">
                                <span style="padding: 0px 2px;">
                                    CONDITION_DISPLAY_VALUE
                                </span>
                                                </div>

                                                <div style="display: inline-block; vertical-align: middle;">
                                                    CONDITION_FILTER_DISPLAY_VALUE
                                                </div>

                                            </td>

                                        </tr>

                                        <!-- IF: CONDITION TYPE 1 or 2 -->
                                        <tr class="conditionRelationshipIndicator" style="display: none;">
                                            <td align="left" colspan="3">
                                                <div style="padding-left: 9px; font-size: 11px;">
                                                    CONDITION_RELATIONSHIP
                                                </div>
                                            </td>
                                        </tr>
                                    </table>

                                    <!-- TEMPLATE: CONDITION: END -->

                                </div>
                                <!-- TEMPLATES: END -->
                            </form:form>
                        </div>
                    </div>
                </msgpt:BodyNew>
            </c:otherwise>
        </c:choose>
    </c:if>
</msgpt:Html5>