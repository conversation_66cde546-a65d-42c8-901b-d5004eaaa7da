<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.model.admin.RecordType"%>
<%@page import="com.prinova.messagepoint.tag.view.tree.TreeConstants"%>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.viewdatasource" >


	<msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js" />
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css" />

	<c:if test="${!empty param.dsid}">
		<msgpt:GetHibernateObject hibernateObjectName="com.prinova.messagepoint.model.admin.DataSource" id="${param.dsid}" outputName="currentDataSource" />
	</c:if>

	<c:if test="${empty param.dsid}">
		<c:set var="datasourceid" value="${datasources[0].id}" />
	</c:if>
	<c:if test="${!empty param.dsid}">
		<c:set var="datasourceid" value="${param.dsid}" />
	</c:if>
	
    <style>
        .dataSourceTree {
            background-color: #FFF;
            min-height: 350px;
            padding: 10px 20px;
        }
        span.nodeLabel {
        	font-weight: normal;
        	font-size: 14px;
        	white-space: pre;
        }
        .jstree-node, ul.jstree-children, ul.jstree-container-ul {
        	margin-top: 3px;
        }
    </style>
	<msgpt:Script>
        <script>
            // Init Javascript
            $( function() {
                //$('#xmlDataTagTable_wrapper').hide();
                $("input:button").styleActionElement();
                $('#add-xml-tag-btn_button').iFramePopup({
                    width			: 960,
                    title			: client_messages.title.add_xml_data_tag,
                    src				: "xml_data_tag_definition_edit.form",
                    appliedParams	: {dsid : "${datasourceid}", "new" : "true", tk : "${param.tk}", action : "insertxmldatatag"},
                    closeBtnId		: "cancelBtn_button",
                    onSave			: customCallback
                });

                $("select#data-sources-select").styleActionElement();

                var dsid = "${currentDataSource.id}";

                if (dsid.length > 0)
                {
                    $('#data-source-btn_button').iFramePopup({
                        width			: 960,
                        title			: client_messages.title.edit_data_source,
                        src				: "data_source_edit.form",
                        appliedParams	: {dsid : dsid, tk : "${param.tk}"},
                        closeBtnId		: "cancelBtn_button",
                        onSave			: customCallback
                    });

                    setDataSourceInfo(dsid);

                    $('#add-data-source-btn2_button').iFramePopup({
                        width			: 960,
                        title			: client_messages.title.add_data_source,
                        src				: "data_source_edit.form",
                        appliedParams	: {tk : "${param.tk}"},
                        closeBtnId		: "cancelBtn_button",
                        onSave			: customCallback
                    });

                    var dsReferenced = "${currentDataSource.referenced}";

                    if (dsReferenced == "false")
                    {
                        $('#del-data-source-btn_button').iFramePopup({
                            width			: 800,
                            title			: client_messages.title.delete_data_source,
                            src				: "data_source_delete.form",
                            appliedParams	: {dsid : dsid, parameter : "dsid", successParameter : "dsid", tk : "${param.tk}"},
                            closeBtnId		: "cancelBtn_button",
                            onSave			: customCallback
                        });
                    }
                }
                $('#add-data-source-btn_button').iFramePopup({
                    width			: 960,
                    title			: client_messages.title.add_data_source,
                    src				: "data_source_edit.form",
                    appliedParams	: {tk : "${param.tk}"},
                    closeBtnId		: "cancelBtn_button",
                    onSave			: customCallback
                });

                //init flip toggle switch
                if ($("input#detail-simple-view").length)
                {
                    $("input#detail-simple-view").iButton({
                        labelOn		: $("input#detail-simple-view").attr('title').split(';')[0],
                        labelOff	: $("input#detail-simple-view").attr('title').split(';')[1],
                        resizeHandle: false,
                        change		: function() {
                            var location = removeParam(window.location.href, 'detailedView')
                            if ( $("input#detail-simple-view").is(':checked') )
                                window.location.href = location + "&detailedView=true";
                            else
                                window.location.href = location;
                        }
                    });
                }

                $(getTopFrame().document).find('#backgroundTasksPlaceholder').refresh();
            });

            function customCallback(iframe) {
                var iFramePopupPluginSrc = $(iframe).attr('src');
                var iFrame = $('iframe').first();

                if (iFrame !== null) {
                    //this is to reload the Data Records to reflect the current Data Source Record Type
                    if ( iFramePopupPluginSrc.indexOf('data_source_delete.form') != -1 ){
                        getTopFrame().location.href = "datasources.form?tk="+getParam('tk');
                    }else if (iFramePopupPluginSrc.indexOf('data_source_edit.form') != -1 || iFramePopupPluginSrc.indexOf('data_record_edit.form') != -1 ){
                        if (gup('globalContext') === "true") {
                            var newUrl = removeParam(window.location.href, "dsid");
                            newUrl = addParam(newUrl, "dsid=" + gup("dsid", $(iframe)[0].contentWindow.location.href));
                            javascriptHref(newUrl);
                        } else {
                            window.parent.location.reload();
                        }
                    }else{
                        window.location.reload();
                    }
                }
            }

            function setDataSourceInfo(dsid) {
                var src = 'datamodel_view_datasource_info.jsp?dsid=' + dsid + '&iframetarget=1&tk=${param.tk}'

                $('iframe#data-source-info-iframe').attr('src', src);
                //datamodel_view_datasource_info.jsp?dsid=${datasources[0].id}&iframetarget=1&tk=${param.tk}
            }

            function changeDataSource(selectEle) {
                var dataSourceId = $(selectEle).find('option:selected').val();
                var target = "datasources.form?dsid=" + dataSourceId;
                if (getParam('documentId') !== "")
                    target += "&documentId=" + gup('documentId');
                if (getParam('globalContext') !== "")
                    target += "&globalContext=" + gup('globalContext');
                getTopFrame().location.href = target + "&tk=" + getParam('tk');
            }
        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:BodyNew>
	<msgpt:BannerNew  edit="false" tab="<%= NavigationTab.TAB_ID_DATA_ADMIN %>" />
	<msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_DATA_ADMIN %>" />
	<msgpt:ContextBarNew globalContextApplied="true" languageContextApplied="false" />
	<msgpt:LowerContainer fullPanel="true" extendedWidth="true">
	
		<c:if test="${not trashTpContext }">
			<msgpt:DropDownMenu />
			<!-- Data Model - Application with DataTables and iFrame plugins -->
			<c:if test="${!empty datasources}">		
				<!-- <div id="data-source" class="data-model"> -->
					<div id="data-source-select-and-info" class="data-source-box">
						<div id="data-source-select" class="ds-left" style="float: left;">
							<select title="${msgpt:getMessage('page.label.view.data.source')}:" id="data-sources-select" class="style_select inputL" onchange="changeDataSource(this);">
								<c:forEach var="datasource" items="${datasources}">
									<c:if test="${datasource.sourceType.id != 4}">
										<option value="${datasource.id}" ${currentDataSource.id == datasource.id ? 'selected="selected"' : ''}>
											<c:out value="${datasource.name}" />
										</option>
									</c:if>
								</c:forEach>
							</select>
						</div>
						
						<div class="ds-right" style="float: left;">
							<msgpt:IfAuthGranted authority="ROLE_TOUCHPOINT_DATA_EDIT">
								<c:if test="${currentDataSource.updatable}">
									<input id="data-source-btn" type="button" title="${msgpt:getMessage('action.button.label.update')}" />
								</c:if>
							</msgpt:IfAuthGranted>
						</div>
						<div id="flat-file-layout-type-add" class="ds-right" style="float: left;">
							<msgpt:IfAuthGranted authority="ROLE_TOUCHPOINT_DATA_EDIT">
								<c:if test="${currentDataSource.updatable}">
									<input id="add-data-source-btn2" type="button" title="${msgpt:getMessage('page.label.ADD')}" />
								</c:if>
							</msgpt:IfAuthGranted>
						</div>
						<div class="ds-right" style="float: left;">
							<msgpt:IfAuthGranted authority="ROLE_TOUCHPOINT_DATA_EDIT">
								<c:if test="${currentDataSource.updatable}">
									<c:if test="${!currentDataSource.referenced}">	
										<input id="del-data-source-btn" type="button" title="${msgpt:getMessage('page.label.delete.DEL')}" />
									</c:if>
									<c:if test="${currentDataSource.referenced}">
										<input id="del-data-source-btn" type="button" title="${msgpt:getMessage('page.label.delete.DEL')}" disabled="disabled"/>
									</c:if>
								</c:if>
							</msgpt:IfAuthGranted>
						</div>
	
						<table class="data-source-info" border="0" width="100%">
							<tr>
								<td>
									<iframe id="data-source-info-iframe" class="iframe-box noOnShowAnimation" src="" frameborder="0" width="100%" scrolling="no" onload="if (typeof adjustIFrameHeight == 'function'){adjustIFrameHeight(this);}" style="display:none;"></iframe>
								</td>						
							</tr>
						</table>
	
					</div>
				</c:if>
	
			<c:if test="${!empty datasources}">
				<c:if test="${empty currentDataSource.xmlDataTagDefinitions}">
					<div style="margin: 10px 0px;">
						<div class="InfoSysContainer_info">
							<table class="innerContentTable" cellspacing="0" cellpadding="0" border="0"><tr>
								<td style="padding: 0px; vertical-align: middle;">
									<div style="white-space: nowrap;"><fmtSpring:message code="page.text.no.tags.defined"/>:</div>
								</td>
								<td style="padding: 0px; vertical-align: middle;">
									<input id="add-xml-tag-btn" type="button" title="${msgpt:getMessage('page.label.add.tag')}" />
								</td>
							</tr></table>
						</div>
					</div>
				</c:if>
				<c:if test="${!empty currentDataSource.xmlDataTagDefinitions}">
					<div id="dataSourceTreeHeaderOuterContainer" class="dataTables_wrapper no-footer " style="margin-bottom: 0px">
						<div id="dataSourceTreeHeaderInnerContainer" class="fg-toolbar ui-toolbar ui-widget-header ui-helper-clearfix ui-corner-tl ui-corner-tr">
							<div id="dataSourceTreeHeader" class="non-default-dt-table-header listTableHeader">
								<fmtSpring:message code="page.label.root.tags" />
							</div>
						</div>
					</div>
					<msgpt:DataSourceTree id="dataSourceTree" selectedNodeId="${param.selectedNodeId}" dataSource="${currentDataSource}" />
				</c:if>
			</c:if>
		</c:if>	
				
		<c:if test="${trashTpContext}">
			<div style="min-height: 400px;">
				<div class="InfoSysContainer_info" style="margin-top: 20px;">
					<div style="display: inline-block; padding-right: 12px; position: relative;">
							<fmtSpring:message code="page.text.trash.touchpoint.does.not.apply"/>
						</div>
				</div>
			</div>
		</c:if>
	</msgpt:LowerContainer>
    <!-- If authorized to edit: Dynamic table with edit links  -->
    <msgpt:IfAuthGranted authority="ROLE_TOUCHPOINT_DATA_EDIT">
        <c:if test="${currentDataSource.updatable}">
            <msgpt:ContextMenu name="xmlDataTagTable">
                <msgpt:ContextMenuEntry name="context-menu-action-type-id" link="#updatexmldatatag"><fmtSpring:message code="page.label.edit"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="context-menu-action-type-id" link="#deletexmldatatag"><fmtSpring:message code="page.label.delete"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="context-menu-action-type-id" link="#insertxmldatatag"><fmtSpring:message code="page.label.add.tag"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="context-menu-action-type-id" link="#uploadxmldatatags"><fmtSpring:message code="page.label.upload.tags"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="context-menu-action-type-id" link="#insertxmlattribute"><fmtSpring:message code="page.label.add.attribute"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="context-menu-action-type-id" link="#insertxmldataelement"><fmtSpring:message code="page.label.add.element"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="context-menu-action-type-id" link="#updatexmlattribute"><fmtSpring:message code="page.label.edit"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="context-menu-action-type-id" link="#deletexmldataelement"><fmtSpring:message code="page.label.delete"/></msgpt:ContextMenuEntry>
            </msgpt:ContextMenu>

        </c:if>
    </msgpt:IfAuthGranted>
</msgpt:BodyNew>
</msgpt:Html5>