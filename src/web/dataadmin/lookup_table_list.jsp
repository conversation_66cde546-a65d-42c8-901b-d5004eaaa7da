<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.lookup.tables" extendedScripts="true">

        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>
        <msgpt:CalendarIncludes/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>
        <msgpt:Script>
            <script>

                var $updateBtn, $deleteBtn, $actionMenu;

                // *********  INIT: START  *********
                $(function () {

                    $updateBtn = $('#updateBtn');
                    $deleteBtn = $('#deleteBtn');
                    $actionMenu = $('#actionMenu');

                    $('#addLookupTableBtn').iFramePopup({
                        width: 600,
                        src: context + "/dataadmin/lookup_table_edit.form",
                        appliedParams: {tk: "${param.tk}"},
                        closeBtnId: "cancelBtn_button",
                        beforePopupClose: function () {
                            rebuildListTable();
                        }
                    });

                    $('.iconListToggleContainer')
                        .click(function () {
                            if ($(this).is('.actionBtn_selected')) {
                                $(this).removeClass('actionBtn_selected');
                                $(this).addClass('actionBtn');
                            } else {
                                $(this).removeClass('actionBtn');
                                $(this).addClass('actionBtn_selected');
                            }
                            updatePersistedClass($(this));
                            getTopFrame().location.reload();
                        });

                    // Onload asset popup: E-mail links
                    if (getParam('lookupTableId') && getParam('lookupTableId') != "")
                        iFrameView(context + '/dataadmin/lookup_table_edit.form?documentId=' + getParam('documentId') + '&lookupTableId=' + getParam('lookupTableId'), 'lookupTableList', 'null');

                });
                // *********  INIT END  *********

                // *********  LIST TABLE FUNCTIONS: START  *********
                function iFrameView(path, eleId, e) {
                    if (e != undefined) {
                        if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
                            $("#" + eleId).iFramePopup({
                                width: 600,
                                src: path,
                                displayOnInit: true,
                                id: "lookupTableFrame",
                                appliedParams: {
                                    tk: "${param.tk}"
                                },
                                closeBtnId: "cancelBtn_button",
                                beforePopupClose: function () {
                                    $('#iFramePopup_workflowHistoryFrame').remove();
                                    rebuildListTable(true);
                                    $('#backgroundTasksPlaceholder').refresh();
                                }
                            });
                        }
                        e.preventDefault ? e.stopPropagation() : e.returnValue = false;
                    }
                }

                function toggleFilter(select) {
                    rebuildListTable(false);
                }

                function rebuildListTable(maintainCurrentPage) {
                    $('#lookupTableList').DataTable().ajax.reload(null, !maintainCurrentPage);
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.columns = [
                        {
                            columnMap: 'name',
                            columnName: client_messages.text.name,
                            sort: true,
                            width: '35%',
                            colVisToggle: false
                        },
                        {
                            columnMap: 'versioning',
                            columnName: client_messages.text.state,
                            sort: false,
                            width: '5%',
                            colVisToggle: false
                        },
                        {
                            columnMap: 'assignedTo',
                            columnName: client_messages.text.assigned,
                            sort: true,
                            width: '20%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'data',
                            columnName: client_messages.text.data,
                            sort: false,
                            width: '40%',
                            colVisToggle: true
                        }
                    ];
                    return obj;
                }

                function getAsyncExtParams() {
                    var stampDate = new Date();
                    var obj = [
                        {"name": "listTableType", "value": "23"},
                        {"name": "documentId", "value": getParam('documentId') != "" ? getParam('documentId') : -1},
                        {
                            "name": "collectionId",
                            "value": getParam('collectionId') != "" ? getParam('collectionId') : -1
                        },
                        {"name": "globalContext", "value": $("#globalContextContainer").is('.globalContextEnabled')},
                        {"name": "lookupTableAssignmentFilterId", "value": $('#lookupTableListAssignmentFilter').val()},
                        {"name": "lookupTableStatusFilterId", "value": $('#lookupTableListStatusFilter').val()},
                        {"name": "displayMode", "value": "full"},
                        {"name": "cacheStamp", "value": stampDate.getTime()}
                    ];
                    return obj;
                }

                function postListDrawCallback(nTable) {
                    $(nTable).find('.detailTip').each(function () {
                        initTip(this);
                    });

                    $(nTable).find('.sourceEditor').each(function () {
                        var instId = parseId($(this));
                        var readOnlyParam = $(this).hasClass('readOnly') ? "&readOnly=true" : "";
                        $(this).click(function () {
                            popupSourceEditorIframe(this, 2, $(this).find('input').attr('value'), true, '../dataadmin/lookup_table_source_editor.form?lookupTableId=' + instId + readOnlyParam);
                        });
                    });
                }

                function postListRenderFlagInjection(oObj) {
                    var iFrameId = "lookup-table-id";
                    var iFrameSrc = context + "/dataadmin/lookup_table_list_detail.form?lookupTableId=" + oObj.aData.dt_RowId;

                    if ($('#listSearchInput').val() != "")
                        iFrameSrc += "&sSearch=" + $('#listSearchInput').val();

                    var binding = oObj.aData.binding;

                    var text = oObj.aData.name;
                    text += "<input id='iFrameId' value=" + iFrameId + " type='hidden' class='iframe-data' />";
                    text += "<input id='iFrameSrc' value=" + iFrameSrc + " type='hidden' class='iframe-data' />";

                    //Selection check select
                    text += binding;

                    //Selection permission flags
                    if (oObj.aData.flags.canUpdate)
                        text += "<input type='hidden' id='canUpdate_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReassign)
                        text += "<input type='hidden' id='canReassign_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReleaseForApproval)
                        text += "<input type='hidden' id='canReleaseForApproval_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canApprove)
                        text += "<input type='hidden' id='canApprove_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReject)
                        text += "<input type='hidden' id='canReject_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canDiscard)
                        text += "<input type='hidden' id='canDiscard_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canArchive)
                        text += "<input type='hidden' id='canArchive_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.archived)
                        text += "<input type='hidden' id='archived_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canCreateWorkingCopy)
                        text += "<input type='hidden' id='canCreateWorkingCopy_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canDeleteArchive)
                        text += "<input type='hidden' id='canDeleteArchive_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.hasNoStepForWorkflow)
                        text += "<input type='hidden' id='hasNoStepForWorkflow_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.workflowOwner)
                        text += "<input type='hidden' id='workflowOwner_" + oObj.aData.dt_RowId + "' />";

                    return text;
                }

                // *********  LIST TABLE FUNCTIONS: END  *********

                function actionMenuSelected(el) {

                    var elSelectedId = $(el).children(':selected').attr('id');

                    if (elSelectedId.indexOf('actionOption_') !== -1)
                        actionSelected(el);

                    else
                        iFrameAction(elSelectedId.replace('actioniFrame_', ''));

                }

                // List actions: Edit
                function iFrameAction(actionId) {
                    var lookupTableId;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        lookupTableId = this.id.replace('listItemCheck_', '');
                    });

                    if (actionId == '1') {
                        $('#actioniFrame_' + actionId).iFramePopup({
                            width: 600,
                            src: context + "/dataadmin/lookup_table_edit.form",
                            displayOnInit: true,
                            id: "lookupTableFrame",
                            appliedParams: {
                                tk: "${param.tk}",
                                'lookupTableId': lookupTableId
                            },
                            closeBtnId: "cancelBtn_button",
                            beforePopupClose: function () {
                                $('#iFramePopup_workflowHistoryFrame').remove();
                                rebuildListTable(true);
                                $('#backgroundTasksPlaceholder').refresh();
                            }
                        });
                    }
                }

                // Retrieve selected ids seperated by ','
                function getSelectedIds() {
                    var selectedIds = '';
                    var selectedCount = $("input[id^='listItemCheck']:checked").length;
                    var count = 0;
                    $("input[id^='listItemCheck']:checked").each(
                        function () {
                            selectedIds += this.id.replace('listItemCheck_', '');
                            count++;
                            if (count != selectedCount)
                                selectedIds += '_';
                        }
                    );
                    return selectedIds;
                }

                function validateActionReq(lookupTableInstanceId) {
                    var singleSelect = true;

                    var canUpdate = true;
                    var canReassign = true;
                    var canReleaseForApproval = true;
                    var canApprove = true;
                    var canReject = true;
                    var canDiscard = true;
                    var archived = true;
                    var canArchive = true;
                    var canCreateWorkingCopy = true;
                    var canDeleteArchive = true;
                    var allHasWorkflowStep = true;
                    var allAreWorkflowOwner = true;

                    // Resolve selection flags
                    if ($("input[id^='listItemCheck_']:checked").length != 1)
                        singleSelect = false;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        var lookupTableId = this.id.replace('listItemCheck_', '');
                        if (!exists('canUpdate_' + lookupTableId))
                            canUpdate = false;
                        if (!exists('canReassign_' + lookupTableId))
                            canReassign = false;
                        if (!exists('canReleaseForApproval_' + lookupTableId))
                            canReleaseForApproval = false;
                        if (!exists('canApprove_' + lookupTableId))
                            canApprove = false;
                        if (!exists('canReject_' + lookupTableId))
                            canReject = false;
                        if (!exists('canDiscard_' + lookupTableId))
                            canDiscard = false;
                        if (!exists('canArchive_' + lookupTableId))
                            canArchive = false;
                        if (!exists('archived_' + lookupTableId))
                            archived = false;
                        if (!exists('canCreateWorkingCopy_' + lookupTableId))
                            canCreateWorkingCopy = false;
                        if (!exists('canDeleteArchive_' + lookupTableId))
                            canDeleteArchive = false;
                        if (exists('hasNoStepForWorkflow_' + lookupTableId)) {
                            allHasWorkflowStep = false;
                        } else {
                            allHasNoWorkflowStep = false;
                        }
                        if (exists('workflowOwner_' + lookupTableId)) {
                            allAreNotWorkflowOwner = false;
                        } else {
                            allAreWorkflowOwner = false;
                        }
                    });

                    //Disable all context menu entries
                    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
                    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');
                    $actionMenu.data('complexDropdown').disableAllTheOptions();
                    common.disableElement($updateBtn);

                    //Enable context menu entries based on flag status
                    if (singleSelect) {
                        if (canUpdate) {
                            $('a#actioniFrame_1').removeClass('disabled');
                            common.enableElement($updateBtn);
                        }
                        if (canReassign) {
                            $('a#actionOption_5').removeClass('disabled');
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_5');	// Reassign
                        }
                    }

                    if ($("input[id^='listItemCheck_']:checked").length > 0) {
                        $('ul.contextMenu').find("a[id^='actionOption']").show();
                        $('ul.contextMenu').find("a[id^='actioniFrame']").show();
                        $actionMenu.data('complexDropdown').showAllTheOptions();

                        if (allHasWorkflowStep) {
                            if (allAreWorkflowOwner) {
                                $('a#actionOption_20').show();	// Approve and override
                                $('a#actionOption_21').show();	// Reject and override
                                $('a#actionOption_17').hide();	// Approve
                                $('a#actionOption_18').hide();	// Reject
                                $actionMenu.data('complexDropdown').showOptionById('actionOption_20'); // Approve and override
                                $actionMenu.data('complexDropdown').showOptionById('actionOption_21'); // Reject and override
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_17'); // Approve
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_18'); // Reject
                            } else {
                                $('a#actionOption_17').show(); // Approve
                                $('a#actionOption_18').show(); // Reject
                                $('a#actionOption_20').hide();	// Approve and override
                                $('a#actionOption_21').hide();	// Reject and override
                                $actionMenu.data('complexDropdown').showOptionById('actionOption_17'); // Approve
                                $actionMenu.data('complexDropdown').showOptionById('actionOption_18'); // Reject
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_20'); // Approve and override
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_21'); // Reject and override
                            }

                            $('a#actionOption_6').show();  // Release for approval

                            $('a#actionOption_19').hide();	// Activate
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_19');	// Activate
                        } else {
                            $('a#actionOption_19').show(); // Activate
                            $('a#actionOption_20').hide();	// Approve and override
                            $('a#actionOption_21').hide();	// Reject and override
                            $('a#actionOption_6').hide();	// Release for approval
                            $('a#actionOption_17').hide();	// Approve
                            $('a#actionOption_18').hide();	// Reject
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_19'); // Activate
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_20'); // Approve and override
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_21'); // Reject and override
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_6'); // Release for approval
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_17'); // Approve
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_18'); // Reject
                        }

                        if (canReleaseForApproval) {
                            $('a#actionOption_6').removeClass('disabled');	// Release for approval
                            $('a#actionOption_19').removeClass('disabled');	// Activate
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_6');	// Release for approval
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_19'); // Activate
                        }
                        if (canApprove) {
                            $('a#actionOption_17').removeClass('disabled'); // Approve
                            $('a#actionOption_20').removeClass('disabled'); // Approve and override
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_17'); // Approve
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_20'); // Approve and override
                        }
                        if (canReject) {
                            $('a#actionOption_18').removeClass('disabled'); // Reject
                            $('a#actionOption_21').removeClass('disabled'); // Reject and override
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_18'); // Reject
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_21'); // Reject and override
                        }

                        if (canCreateWorkingCopy) {
                            $('a#actionOption_2').hide();
                            $('a#actionOption_1').show();
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_2'); // Discard WC
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_1'); // Create WC
                            $('a#actionOption_1').removeClass('disabled'); // Create WC
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_1'); // Create WC
                        } else {
                            $('a#actionOption_2').hide();
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_2');	// Discard WC
                        }
                        if (canDiscard) {
                            $('a#actionOption_1').hide();
                            $('a#actionOption_2').show();
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_1'); // Create WC
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_2'); // Discard WC
                            $('a#actionOption_2').removeClass('disabled'); // Discard WC
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_2'); // Discard WC
                        }
                        if (canArchive) {
                            $('a#actionOption_4').hide();	// Delete Archive
                            $('a#actionOption_3').show();	// Archive
                            $('a#actionOption_3').removeClass('disabled'); // Archive
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_4'); // Delete Archive
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_3'); // Archive
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_3'); // Archive
                        } else {
                            $('a#actionOption_4').hide();	// Delete Archive
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_4'); // Delete Archive
                        }
                        if (archived) {
                            common.disableElement($updateBtn);
                            $('ul.contextMenu').find("a[id^='actionOption']").hide();
                            $('ul.contextMenu').find("a[id^='actioniFrame']").hide();
                            $('a#actionOption_1').show();	// Create WC
                            $('a#actionOption_4').show();	// Delete Archive
                            $('a#actionOption_1').removeClass('disabled'); // Create WC
                            $('a#actionOption_4').removeClass('disabled'); // Delete Archive
                            $actionMenu.data('complexDropdown').hideAllTheOptions();
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_1'); // Create WC
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_4'); // Delete Archive
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_1'); // Create WC
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_4'); // Delete Archive
                        }
                    }
                }

                // ******* CONTEXT ACTIONS: END *******
            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>
    <!-- FLAGS AND PERMISSIONS -->
    <c:set var="viewLookupTablePermission" value="false"/>
    <msgpt:IfAuthGranted authority="ROLE_LOOKUP_TABLE_VIEW">
        <c:set var="viewLookupTablePermission" value="true"/>
    </msgpt:IfAuthGranted>
    <!-- END FLAGS AND PERMISSIONS -->
    <msgpt:BodyNew>
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_DATA_ADMIN %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_DATA_ADMIN %>"/>
        <msgpt:ContextBarNew globalContextApplied="true" languageContextApplied="false" collectionContextApplied="true"
                             workflowURL="lookup_table_workflow_edit.form?type=14"
                             workflowPermission="ROLE_LOOKUP_TABLE_SETUP"/>
        <msgpt:LowerContainer fullPanel="true" extendedWidth="false">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="false">
                <c:if test="${not trashTpContext }">
                    <form:form method="post" modelAttribute="command">
                        <form:errors path="*">
                            <msgpt:Information errorMsgs="${messages}" type="error"/>
                        </form:errors>
                        <h1 class="h4 pb-2 mb-4"><fmtSpring:message code="page.label.lookup.tables"/></h1>
                        <div class="box-shadow-4 rounded bg-white p-4">
                            <div class="px-2 py-1">
                                <div class="mb-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <form:hidden path="actionValue" id="actionElement"/>
                                        <msgpt:IfAuthGranted authority="ROLE_LOOKUP_TABLE_EDIT">
                                            <div class="mr-3">
                                                <button id="addLookupTableBtn" class="btn btn-primary" type="button">
                                                    <i class="far fa-plus-circle mr-2" aria-hidden="true"></i>
                                                    <fmtSpring:message code="page.label.add"/>
                                                </button>
                                            </div>
                                        </msgpt:IfAuthGranted>
                                        <div class="d-flex mr-auto" role="group"
                                             aria-label="${msgpt:getMessage("page.label.actions")}">
                                            <button id="updateBtn" type="button" class="btn btn-dark mr-3"
                                                    onclick="iFrameAction(1);" disabled>
                                                <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="action.button.label.update"/>
                                            </button>
                                            <select title="${msgpt:getMessage('page.label.more')}"
                                                    id="actionMenu"
                                                    class="complex-dropdown-select"
                                                    aria-label="${msgpt:getMessage('page.label.more')}"
                                                    data-toggle="complex-dropdown"
                                                    data-action="menu"
                                                    data-dropdown-class="btn-dark"
                                                    onchange="actionMenuSelected(this)">
                                                <option id="actionOption_1" disabled><fmtSpring:message
                                                        code="page.label.create.working.copy"/></option>
                                                <option id="actionOption_2" class="d-none" disabled><fmtSpring:message
                                                        code="page.label.discard.working.copy"/></option>
                                                <msgpt:IfAuthGranted authority="ROLE_LOOKUP_TABLE_SETUP">
                                                    <option id="actionOption_3" disabled>
                                                        <fmtSpring:message code="page.label.archive"/></option>
                                                    <option id="actionOption_4" class="d-none" disabled>
                                                        <fmtSpring:message
                                                                code="page.label.delete.archive"/></option>
                                                </msgpt:IfAuthGranted>
                                                <option id="actionOption_5" disabled><fmtSpring:message
                                                        code="page.label.reassign.to.user"/></option>
                                                <option id="actionOption_6" disabled><fmtSpring:message
                                                        code="page.label.release.for.approval"/></option>
                                                <option id="actionOption_17" disabled><fmtSpring:message
                                                        code="page.label.approve"/></option>
                                                <option id="actionOption_18" disabled><fmtSpring:message
                                                        code="page.label.reject"/></option>
                                                <option id="actionOption_19" class="d-none" disabled><fmtSpring:message
                                                        code="page.label.activate"/></option>
                                                <option id="actionOption_20" class="d-none" disabled><fmtSpring:message
                                                        code="page.label.approve.and.override"/></option>
                                                <option id="actionOption_21" class="d-none" disabled><fmtSpring:message
                                                        code="page.label.reject.and.override"/></option>
                                            </select>
                                        </div>
                                        <div class="mr-3 colVisToggleContainer" data-toggle="tooltip"
                                             title="${msgpt:getMessage('client_messages.label.toggle_columns')}">
                                        </div>
                                        <div class="form-group position-relative d-inline-block m-0">
                                            <label for="listSearchInput" class="sr-only"><fmtSpring:message
                                                    code="page.label.search"/></label>
                                            <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                               style="z-index: 1;" aria-hidden="true"></i>
                                            <msgpt:InputFilter type="description">
                                                <input id="listSearchInput" type="text"
                                                       class="form-control bg-light has-control-l border-0"
                                                       placeholder="${msgpt:getMessage('page.label.search')}"
                                                       size="25"/>
                                            </msgpt:InputFilter>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center pt-1">
                                        <span class="text-dark mr-1" id="filter">
                                            <fmtSpring:message code="page.label.filter"/>:
                                        </span>
                                        <div class="mx-2">
                                            <select id="lookupTableListAssignmentFilter"
                                                    class="complex-dropdown-select persistedValue"
                                                    aria-labelledby="filter"
                                                    data-toggle="complex-dropdown"
                                                    data-menu-class="dropdown-custom mt-2"
                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                    onchange="toggleFilter(this)">
                                                <c:forEach items="${primaryFilterTypes}" var="currentFilter">
                                                    <option id="lookupTableListAssignmentFilter_${currentFilter.id}"
                                                            value="${currentFilter.id}"><fmtSpring:message
                                                            code="${currentFilter.displayMessageCode}"/></option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                        <span class="text-dark mx-1" id="lookup-table-status">
                                            <fmtSpring:message code="page.text.lookup.tables.which.are"/>
                                        </span>
                                        <div class="ml-2">
                                            <select id="lookupTableListStatusFilter"
                                                    class="complex-dropdown-select persistedValue"
                                                    aria-labelledby="lookup-table-status"
                                                    data-toggle="complex-dropdown"
                                                    data-menu-class="dropdown-custom mt-2"
                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                    onchange="toggleFilter(this)">
                                                <c:forEach items="${lookupTableStatusFilterTypes}"
                                                           var="currentFilter">
                                                    <option id="lookupTableListStatusFilter_${currentFilter.id}"
                                                            value="${currentFilter.id}"><fmtSpring:message
                                                            code="${currentFilter.displayMessageCode}"/></option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white">
                                    <msgpt:DataTable id="lookupTableList" listHeader="page.label.messages"
                                                     async="true" columnReorder="true" numUnreorderableCols="2"
                                                     columnVisibility="true" drillDown="true" multiSelect="true"
                                                     searchFilter="true">
                                    </msgpt:DataTable>
                                </div>
                            </div>
                        </div>

                        <!-- POPUP DATA -->
                        <div id="actionSpecs" style="display: none;">
                            <!-- ACTIONS POPUP DATA -->
                            <div id="actionSpec_1" type="simpleConfirm" submitId="4"> <!-- Create working copy -->
                                <div id="actionTitle_1"><fmtSpring:message
                                        code="page.label.confirm.create.working.copy"/></div>
                                <div id="actionInfo_1"><fmtSpring:message
                                        code="page.text.create.working.copy.of.selected.lookup.tables"/></div>
                            </div>
                            <div id="actionSpec_2" type="simpleConfirm" submitId="5"> <!-- Discard working copy -->
                                <div id="actionTitle_2"><fmtSpring:message
                                        code="page.label.confirm.discard.working.copy"/></div>
                                <div id="actionInfo_2"><fmtSpring:message
                                        code="page.text.discard.working.copy.of.lookup.table"/></div>
                            </div>
                            <div id="actionSpec_3" submitId="6"> <!-- Archive -->
                                <div id="actionTitle_3"><fmtSpring:message code="page.label.confirm.archive"/></div>
                                <div id="actionInfo_3"><fmtSpring:message code="page.label.confirm.archive"/></div>
                                <div id="actionNote_3" required="true"><fmtSpring:message
                                        code="page.text.note.required.brackets"/></div>
                            </div>
                            <div id="actionSpec_4" type="simpleConfirm" submitId="7"> <!-- Delete archive -->
                                <div id="actionTitle_4"><fmtSpring:message
                                        code="page.label.confirm.delete.archive"/></div>
                                <div id="actionInfo_4"><fmtSpring:message
                                        code="page.text.delete.archived.lookup.table"/></div>
                            </div>
                            <div id="actionSpec_5" submitId="3"> <!-- Reassign to user -->
                                <div id="actionTitle_5"><fmtSpring:message
                                        code="page.label.confirm.reassign.to.user"/></div>
                                <div id="actionInfo_5"><fmtSpring:message
                                        code="page.text.select.user.for.lookup.table.reassign"/></div>
                                <div id="actionNote_5"></div>
                                <div id="actionUserSelect_5" required="true" type="lookupTable_currentStageUsers"></div>
                            </div>
                            <div id="actionSpec_6" submitId="9"> <!-- Release for approval -->
                                <div id="actionTitle_6"><fmtSpring:message
                                        code="page.label.confirm.release.for.approval"/></div>
                                <div id="actionInfo_6"><fmtSpring:message
                                        code="page.text.provide.description.of.action.required"/></div>
                                <div id="actionNote_6"></div>
                            </div>
                            <!-- APPROVE -->
                            <div id="actionSpec_17" type="simpleConfirm" submitId="17" contentWidth="330px">
                                <!-- Approve -->
                                <div id="actionTitle_17"><fmtSpring:message code="page.label.approve"/></div>
                                <div id="actionInfo_17"><fmtSpring:message
                                        code="page.text.would.you.like.to.approve"/></div>
                                <div id="actionApproval_17" approveSubmitId="17"></div>
                            </div>
                            <!-- REJECT -->
                            <div id="actionSpec_18" type="simpleConfirm" submitId="18" contentWidth="330px">
                                <!-- Reject -->
                                <div id="actionTitle_18"><fmtSpring:message code="page.label.reject"/></div>
                                <div id="actionInfo_18"><fmtSpring:message
                                        code="page.text.would.you.like.reject"/></div>
                                <div id="actionNote_18" required="true"><fmtSpring:message
                                        code="page.text.note.required.to.reject.brackets"/></div>
                                <div id="actionUserSelect_18" required="true" type="lookupTable_rejectToUsers"></div>
                                <div id="actionReject_18" rejectSubmitId="18"></div>
                            </div>
                            <!-- Activate -->
                            <div id="actionSpec_19" submitId="9">
                                <div id="actionTitle_19"><fmtSpring:message code="page.label.confirm.activate"/></div>
                                <div id="actionInfo_19"><fmtSpring:message
                                        code="page.text.would.you.like.to.activate.lookup.tables"/></div>
                            </div>
                            <!-- Override approve -->
                            <div id="actionSpec_20" type="simpleConfirm" submitId="17" contentWidth="330px">
                                <!-- Approve -->
                                <div id="actionTitle_20"><fmtSpring:message
                                        code="page.label.approve.workflow.owner"/></div>
                                <div id="actionInfo_20"><fmtSpring:message
                                        code="page.text.workflow.owners.may.approve.step"/></div>
                                <div id="actionApproval_20" approveSubmitId="17"></div>
                            </div>
                            <!-- Override reject -->
                            <div id="actionSpec_21" type="simpleConfirm" submitId="18" contentWidth="330px">
                                <!-- Reject -->
                                <div id="actionTitle_21"><fmtSpring:message
                                        code="page.label.reject.workflow.owner"/></div>
                                <div id="actionInfo_21"><fmtSpring:message
                                        code="page.text.workflow.owners.may.reject.step"/></div>
                                <div id="actionNote_21" required="true"><fmtSpring:message
                                        code="page.text.note.required.to.reject.brackets"/></div>
                                <div id="actionUserSelect_21" required="true" type="lookupTable_rejectToUsers"></div>
                                <div id="actionReject_21" rejectSubmitId="18"></div>
                            </div>
                        </div>

                        <!-- POPUP INTERFACE -->
                        <msgpt:Popup id="actionPopup" theme="minimal">
                            <div id="actionPopupInfoFrame">
                                <div id="actionPopupInfo">&nbsp;</div>
                            </div>
                            <div id="actionPopupNote" state="default"
                                 style="padding: 3px 15px 6px 15px; white-space:nowrap;" align="left">
                                <msgpt:InputFilter type="comment">
                                    <form:textarea path="userNote" onkeyup="validatePopupReq();"
                                                   cssStyle="width: 270px; padding: 4px;" rows="3"
                                                   onclick="initTextarea(this)"/>
                                </msgpt:InputFilter>
                            </div>
                            <div id="actionPopupUserSelect" style="padding: 2px 8px 6px 8px;" align="center">
                                <form:select id="userSelect" path="assignedToUser" cssClass="inputL"
                                             onchange="validatePopupReq()" onkeyup="validatePopupReq()">
                                    <option id="0" value="0"><fmtSpring:message code="page.text.loading"/></option>
                                </form:select>
                            </div>
                            <div id="actionPopupApprovalButtons" class="actionPopupButtonsContainer">
                                <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                <span id="approveBtn"><msgpt:Button URL="#" label="page.flow.approve"
                                                                    primary="true"/></span>
                                <span id="approveBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                   label="page.flow.approve"
                                                                                                   disabled="true"/></span>
                            </div>
                            <div id="actionPopupRejectButtons" class="actionPopupButtonsContainer">
                                <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                <span id="rejectBtn"><msgpt:Button URL="#" label="page.flow.reject"
                                                                   primary="true"/></span>
                                <span id="rejectBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                  label="page.flow.reject"
                                                                                                  disabled="true"/></span>
                            </div>
                            <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                                <span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                  label="page.label.cancel"
                                                                                                  disabled="true"/></span>
                                <span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                          label="page.label.cancel"/></span>
                                <span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                    label="page.label.continue"
                                                                                                    disabled="true"/></span>
                                <span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue"
                                                                            primary="true"/></span>
                            </div>
                        </msgpt:Popup>
                    </form:form>
                </c:if>
                <c:if test="${trashTpContext}">
                    <div class="alert alert-info" role="alert">
                        <strong class="mr-2">
                            <i class="fas fa-info-circle fa-lg mr-2"
                               aria-hidden="true"></i><fmtSpring:message
                                code="page.label.info"/>:
                        </strong>
                        <fmtSpring:message code="page.text.trash.touchpoint.does.not.apply"/>
                    </div>
                </c:if>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
        <msgpt:ContextMenu name="lookupTableList">
            <msgpt:ContextMenuEntry name="actioniFrame_1" link="#iFrameAction:1"><fmtSpring:message
                    code="page.label.edit"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_1" link="#actionSelected:1"><fmtSpring:message
                    code="page.label.create.working.copy"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_2" link="#actionSelected:2"><fmtSpring:message
                    code="page.label.discard.working.copy"/></msgpt:ContextMenuEntry>
            <msgpt:IfAuthGranted authority="ROLE_LOOKUP_TABLE_SETUP">
                <msgpt:ContextMenuEntry name="actionOption_3" link="#actionSelected:3"><fmtSpring:message
                        code="page.label.archive"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="actionOption_4" link="#actionSelected:4"><fmtSpring:message
                        code="page.label.delete.archive"/></msgpt:ContextMenuEntry>
            </msgpt:IfAuthGranted>
            <msgpt:ContextMenuEntry name="actionOption_5" link="#actionSelected:5"><fmtSpring:message
                    code="page.label.reassign.to.user"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_6" link="#actionSelected:6"><fmtSpring:message
                    code="page.label.release.for.approval"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_17" link="#actionSelected:17"><fmtSpring:message
                    code="page.label.approve"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_18" link="#actionSelected:18"><fmtSpring:message
                    code="page.label.reject"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_19" link="#actionSelected:19"><fmtSpring:message
                    code="page.label.activate"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_20" link="#actionSelected:20"><fmtSpring:message
                    code="page.label.approve.and.override"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_21" link="#actionSelected:21"><fmtSpring:message
                    code="page.label.reject.and.override"/></msgpt:ContextMenuEntry>
        </msgpt:ContextMenu>

    </msgpt:BodyNew>
</msgpt:Html5>