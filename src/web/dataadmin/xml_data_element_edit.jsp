<%@page import="com.prinova.messagepoint.controller.admin.DataSourceEditController"%>
<%@page import="com.prinova.messagepoint.model.admin.LayoutType"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp"%>
<msgpt:Html5>
	
	<msgpt:HeaderNew title="page.label.edit.data.element" viewType="<%= MessagepointHeader.ViewType.EDIT %>">

		<msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js" />
		<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css" />
		
		<msgpt:Script src="dataadmin/javascript/data_element_common.js" />
		
		<msgpt:Script src="includes/javascript/handlebars/handlebars-v4.7.8.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/listInput/jquery.listInput.js" />
		<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/listInput/listInput.css" />
			
	    <msgpt:Script>
            <script>
	            function toggleAnonymized() {
	                if ($('#anonymizedToggle').is(':checked'))
	                    $("[id='anonymizationTypeContainer_td']").each(function () {
	                        $(this).show();
	                    });
	                else
	                    $("[id='anonymizationTypeContainer_td']").each(function () {
	                        $(this).hide();
	                    });

	                toggleAnonymizationType();

					common.refreshParentIframeHeight();
	            }

                function toggleAnonymizationType() {
					if ( $('#anonymizationTypeInput').val() == 1 && $('#anonymizedToggle').is(':checked') )
	                    $("[id='anonymizationCustomMasksContainer_td']").each(function () {
	                        $(this).show();
	                    });
					else
	                    $("[id='anonymizationCustomMasksContainer_td']").each(function () {
	                        $(this).hide();
	                    });

					if ( ($('#anonymizationTypeInput').val() == 100 || $('#anonymizationTypeInput').val() == 101 || $('#anonymizationTypeInput').val() == 500 ||
							$('#anonymizationTypeInput').val() == 501 || $('#anonymizationTypeInput').val() == 700) && $('#anonymizedToggle').is(':checked') )
	                    $("[id='anonymizationLengthsContainer_td']").each(function () {
	                        $(this).show();
	                    });
					else
	                    $("[id='anonymizationLengthsContainer_td']").each(function () {
	                        $(this).hide();
	                    }); 

					common.refreshParentIframeHeight();
                }

                // On load functions
                $( function() {
                    $("input:button").styleActionElement();
                    $(".style_select").styleActionElement({maxItemDisplay: 5});
                    //init();
                    setSelectedEFT();

                    if ( $("#anonymizedToggle:visible").length != 0 && $("#anonymizedToggle:visible").closest('.ibutton-container').length == 0 )
                        $("#anonymizedToggle").iButton({
                            labelOn		: $("#anonymizedToggle").attr('title').split(';')[0],
                            labelOff	: $("#anonymizedToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function() {
                                toggleAnonymized();
                            }
                        });

                    $('#anonymizationCustomMasksInput').listInput({
                        listAreaWidth			: 500,
                        type					: 'string',
						multipleValueDelimiter  : '${dataAnonymizerDelimiter}'
                    });
                    
                    toggleAnonymized();
                    toggleAnonymizationType();

                    

                });

            </script>
        </msgpt:Script>
	</msgpt:HeaderNew>

	<msgpt:BodyNew theme="minimal" type="iframe">
		<form:form modelAttribute="command">

			<msgpt:iFrameContainer>
				<form:errors path="*">
					<msgpt:Information errorMsgs="${messages}" type="error" />
				</form:errors>

				<c:set var="dtID" value="${command.dataSubTypeForView}" />
				<c:set var="submitType" value="<%= DataSourceEditController.FORM_SUBMIT_TYPE_SUBMIT%>" />
				<fmtSpring:bind path="command.externalFormatText">
					<input type="hidden" id="eftSelect_0" disabled=disabled name="externalFormatText" />
				</fmtSpring:bind>
				<input type="hidden" id="selectedExternalFormatText" value="${command.externalFormatText}" />

				<msgpt:FormLayout labelWidth="125px" inputWidth="200px" columns="2">
					<!-- ID -->
					<msgpt:FormField label="page.label.id">
						<c:out value='${empty param.new ? command.id : msgpt:getMessage("page.label.new")}' />
					</msgpt:FormField>
					<!-- Name -->
					<msgpt:FormField label="page.label.edit.xml.element.name">
						<msgpt:InputFilter type="simpleName">
							<form:input cssClass="inputL" path="name" />
						</msgpt:InputFilter>
					</msgpt:FormField>
					<c:if test="${param.isAttributeDataElement}">

						<msgpt:FormField label="page.label.edit.xml.element.attribute.name" >
							<input type="hidden" id="isAttributeDataElement" />
							<msgpt:InputFilter type="xmlName">
								<form:input cssClass="inputXL" path="attributeName"/>
							</msgpt:InputFilter>
						</msgpt:FormField>
					</c:if>
				</msgpt:FormLayout>
<%@ include file="data_element_common_items.jsp"%>
				
				<msgpt:DataTable labelWidths="15%">
                     <!-- Anonymized -->
                     <msgpt:TableItem label="page.label.anonymized">
                         <form:checkbox path="anonymized" id="anonymizedToggle" cssClass="radioBtn"
                                        title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                     </msgpt:TableItem>
                     <!-- Mask -->
                     <msgpt:TableItem label="page.label.mask" id="anonymizationTypeContainer">
                         <div style="position: relative; left: -0px;">
                             <form:select id="anonymizationTypeInput" path="anonymizationTypeId" cssClass="inputL" onchange="toggleAnonymizationType()"
                                          items="${anonymizationMasks}" itemValue="id" itemLabel="name"/>
                         </div>
                     </msgpt:TableItem>
                     <!-- Custom Masks -->
                     <msgpt:TableItem label="page.label.custom.masks" id="anonymizationCustomMasksContainer">
                            <form:input id="anonymizationCustomMasksInput" path="anonymizationCustomMasks" cssClass="inputL" />
                     </msgpt:TableItem>
                     <!-- Anonymization Min Length -->
                     <msgpt:TableItem label="page.label.minimum.length" id="anonymizationLengthsContainer">
                            <form:input path="anonymizationMinLength" cssClass="input5digit" />
                     </msgpt:TableItem>
                     <!-- Anonymization Max Length -->
                     <msgpt:TableItem label="page.label.maximum.length" id="anonymizationLengthsContainer">
                            <form:input path="anonymizationMaxLength" cssClass="input5digit" />
                     </msgpt:TableItem>
         		</msgpt:DataTable>

				<msgpt:FlowLayout align="center">
					<msgpt:FlowLayoutItem>
						<input title="${msgpt:getMessage('page.label.CANCEL')}" type="button" id="cancelBtn" onclick="" />
					</msgpt:FlowLayoutItem>
					<msgpt:FlowLayoutItem>
						<input title="${msgpt:getMessage('page.label.SAVE')}" type="button" id="saveBtn" onclick="javascript:doSubmit('${submitType}')" />
					</msgpt:FlowLayoutItem>
				</msgpt:FlowLayout>

			</msgpt:iFrameContainer>



		</form:form>
	</msgpt:BodyNew>
</msgpt:Html5>