<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="client_messages.pinc.webhooks" extendedScripts="true">
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />

        <msgpt:Script src="includes/node_modules/react-bootstrap/dist/react-bootstrap.min.js" />

        <msgpt:Script src="includes/javascript/asyncJsonTable/react.asyncJsonTable.js" />
        <msgpt:Script src="includes/javascript/asyncJsonTable/react.asyncJsonTableRow.js" />
        <msgpt:Script src="includes/javascript/asyncJsonTable/react.asyncJsonTableHeader.js" />
        <msgpt:Script src="includes/javascript/asyncJsonTable/react.asyncJsonTableFooter.js" />
        <msgpt:Script src="content/javascript/pinc/AsyncJsonTableRowSelectCell.js" />
        <msgpt:Script src="content/javascript/pinc/AsyncJsonTableRowDrillDownCell.js" />

        <msgpt:Script src="content/javascript/pinc/PincPermission.js" />
        <msgpt:Script src="content/javascript/pinc/PincApiContext.js" />
        <msgpt:Script src="content/javascript/pinc/UserPermissionsContext.js" />
        <msgpt:Script src="content/javascript/pinc/PincUtils.js" />
        <msgpt:Script src="content/javascript/pinc/GenericEntityReducer.js" />
        <msgpt:Script src="content/javascript/pinc/ListPageContext.js" />
        <msgpt:Script src="content/javascript/pinc/ListPage.js" />
        <msgpt:Script src="content/javascript/pinc/Modal.js" />
        <msgpt:Script src="content/javascript/pinc/Alert.js" />
        <msgpt:Script src="content/javascript/pinc/Buttons.js" />
        <msgpt:Script src="content/javascript/pinc/SelectControls.js" />
        <msgpt:Script src="content/javascript/pinc/components/ArrayInput.js" />
        <msgpt:Script src="content/javascript/pinc/components/FormInputs.js" />
        <msgpt:Script src="content/javascript/pinc/Main.js" />
        <msgpt:Script src="content/javascript/pinc/companies/CompaniesContext.js" />
        <msgpt:Script src="content/javascript/pinc/components/hooks/useEnvironments.js" />
        <msgpt:Script src="content/javascript/pinc/applications/useApplications.js" />

        <msgpt:Script src="content/javascript/pinc/webhooks/WebhookType.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/WebhooksContext.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/WebhookList.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/WebhookRow.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/forms/JobWebhookAddForm.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/forms/JobWebhookEditForm.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/forms/JobWebhookForm.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/forms/WebhookAddForm.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/forms/WebhookEditForm.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/forms/WebhookEditFormFactory.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/forms/WebhookForm.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/forms/WebhookBasicAuthForm.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/forms/WebhookBearerAuthForm.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/forms/WebhookOAuth2AuthForm.js" />
        <msgpt:Script src="content/javascript/pinc/webhooks/Webhooks.js" />

    </msgpt:HeaderNew>

    <msgpt:BodyNew>
        <msgpt:BannerNew  edit="false" tab="<%= NavigationTab.TAB_ID_PINC %>" />
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_PINC %>" />
        <msgpt:LowerContainer fullPanel="true" extendedWidth="true">
            <msgpt:ContentPanel extendedWidth="true">

                <div id="root_container">
                        <%-- React component goes here --%>
                </div>

            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
    </msgpt:BodyNew>
</msgpt:Html5>
