<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.brand.profile" extendedScripts="true">

        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:CalendarIncludes/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>

        <msgpt:Script>
            <script>

                // *********  INIT: START  *********
                var $updateBtn, $deleteBtn;

                $(function () {

                    $updateBtn = $('#updateBtn');
                    $deleteBtn = $('#deleteBtn');

                    $('#addForm').click( function() {
                        javascriptHref(context + "/brand/brand_profile_edit.form")
                    });

                });
                // *********  INIT END  *********

                // *********  LIST TABLE FUNCTIONS: START  *********
                function cleariFramePopupParams() {
                    var currentURL = window.document.URL;
                    getTopFrame().location.href = currentURL;
                }

                function rebuildListTable() {
                    $('#brandProfileList').DataTable().ajax.reload(null, true);
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.columns = [
                        {
                            columnMap: 'name',
                            columnName: client_messages.text.name,
                            sort: true,
                            width: '80%',
                            colVisToggle: false
                        },
                        {
                            columnMap: 'validations',
                            columnName: client_messages.text.enabled_validations,
                            sort: false,
                            width: '20%',
                            colVisToggle: true
                        }
                    ];

                    return obj;
                }

                function getAsyncExtParams() {
                    var stampDate = new Date();
                    var obj = [
                        {"name": "listTableType", "value": "53"},
                        {"name": "displayMode", "value": "full"},
                        {"name": "cacheStamp", "value": stampDate.getTime()}
                    ];
                    return obj;
                }

                function postListDrawCallback(nTable) {
                    $(nTable).find('[data-toggle=tooltip]').tooltip();
                }

                function postListRenderFlagInjection(oObj) {
                    var iFrameId = "brand-profile-id";
                    var iFrameSrc = context + "/brand/brand_profile_list_detail.form?brandProfileId=" + oObj.aData.dt_RowId;

                    if ($('#listSearchInput').val() != "")
                        iFrameSrc += "&sSearch=" + $('#listSearchInput').val();

                    var binding = oObj.aData.binding;

                    var text = oObj.aData.name;
                    text += "<input id='iFrameId' value=" + iFrameId + " type='hidden' class='iframe-data' />";
                    text += "<input id='iFrameSrc' value=" + iFrameSrc + " type='hidden' class='iframe-data' />";

                    //Selection check select
                    text += binding;

                    //Selection permission flags
                    if (oObj.aData.flags.canUpdate)
                        text += "<input type='hidden' id='canUpdate_" + oObj.aData.dt_RowId + "' />";

                    return text;
                }

                // *********  LIST TABLE FUNCTIONS: END  *********

                // List actions: Edit
                function iFrameAction(actionId) {

                    var brandProfileId;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        brandProfileId = this.id.replace('listItemCheck_', '');
                    });

                    if (actionId == '1')
                        javascriptHref(context + "/brand/brand_profile_edit.form?brandProfileId=" + brandProfileId);

                }

                // Brand profiles list validation
                function validateActionReq(formDefinitionId) {
                    var singleSelect = true;
                    var canUpdate = true;

                    // Resolve selection flags
                    if ($("input[id^='listItemCheck_']:checked").length != 1) {
                        singleSelect = false;
                    }
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        var formDefinitionId = this.id.replace('listItemCheck_', '');
                        if (!exists('canUpdate_' + formDefinitionId))
                            canUpdate = false;
                    });

                    //Disable all context menu entries
                    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
                    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');
                    common.disableElement($updateBtn);
                    common.disableElement($deleteBtn);

                    if (singleSelect) {
                        if (canUpdate) {
                            $('a#actioniFrame_1').removeClass('disabled');
                            common.enableElement($updateBtn);
                        }
                    }

                    if ($("input[id^='listItemCheck_']:checked").length > 0) {
                        $('a#actionOption_2').removeClass('disabled'); 		// Delete
                        common.enableElement($deleteBtn); 	// Delete
                    }
                }

            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <msgpt:BodyNew>
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_ADMIN %>"/>
        <msgpt:ContextBarNew touchpointContextDisabled="true"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_ADMIN %>"/>
        <msgpt:LowerContainer fullPanel="true" extendedWidth="false">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="false">
                <c:if test="${not trashTpContext }">
                    <form:form method="post" modelAttribute="command">
                        <form:errors path="*">
                            <msgpt:Information errorMsgs="${messages}" type="error"/>
                        </form:errors>
                        <h1 class="h4 pb-2 mb-4"><fmtSpring:message code="page.label.brand.profiles"/></h1>
                        <div class="box-shadow-4 rounded bg-white p-4">
                            <div class="px-2 py-1">
                                <div class="mb-4">
                                    <div class="d-flex align-items-center pb-1">
                                        <form:hidden path="actionValue" id="actionElement"/>
                                        <div class="mr-3">
                                            <button id="addForm" class="btn btn-primary" type="button">
                                                <i class="far fa-plus-circle mr-2" aria-hidden="true"></i>
                                                <c:out value='${msgpt:getMessage("page.label.add")}'/>
                                            </button>
                                        </div>
                                        <div class="d-flex mr-auto" role="group"
                                             aria-label="${msgpt:getMessage("page.label.actions")}">
                                            <button id="updateBtn" type="button" class="btn btn-dark mr-3"
                                                    onclick="iFrameAction(1);" disabled>
                                                <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="action.button.label.update"/>
                                            </button>
                                            <button id="deleteBtn" type="button" class="btn btn-dark"
                                                    onclick="actionSelected(2);" disabled>
                                                <i class="far fa-trash-alt mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="page.label.delete"/>
                                            </button>
                                        </div>
                                        <div class="mr-3 colVisToggleContainer" data-toggle="tooltip"
                                             title="${msgpt:getMessage('client_messages.label.toggle_columns')}">
                                        </div>
                                        <div class="form-group position-relative d-inline-block m-0">
                                            <label for="listSearchInput" class="sr-only"><fmtSpring:message
                                                    code="page.label.search"/></label>
                                            <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                               style="z-index: 1;" aria-hidden="true"></i>
                                            <msgpt:InputFilter type="description">
                                                <input id="listSearchInput" type="text" size="25"
                                                       class="form-control bg-lightest has-control-x border-0"
                                                       placeholder="${msgpt:getMessage('page.label.search')}"/>
                                            </msgpt:InputFilter>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white">
                                    <msgpt:DataTable id="brandProfileList"
                                                     listHeader="page.label.brand.profiles"
                                                     async="true" columnReorder="true" numUnreorderableCols="2"
                                                     columnVisibility="true" drillDown="true" multiSelect="true"
                                                     searchFilter="true">
                                    </msgpt:DataTable>
                                </div>
                            </div>
                        </div>

                        <!-- POPUP DATA -->
                        <div id="actionSpecs" style="display: none;">
                            <!-- ACTIONS POPUP DATA -->
                            <div id="actionSpec_2" type="simpleConfirm" submitId="2"> <!-- ACTION -->
                                <div id="actionTitle_2"><fmtSpring:message
                                        code="page.label.confirm.delete.brand.profile"/></div>
                                <div id="actionInfo_2"><fmtSpring:message
                                        code="page.text.delete.selected.brand.profile"/></div>
                            </div>
                        </div>

                        <!-- POPUP INTERFACE -->
                        <msgpt:Popup id="actionPopup" theme="minimal">
                            <div id="actionPopupInfoFrame">
                                <div id="actionPopupInfo">&nbsp;</div>
                            </div>

                            <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                                <span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                  label="page.label.cancel"
                                                                                                  disabled="true"/></span>
                                <span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                          label="page.label.cancel"/></span>
                                <span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                    label="page.label.continue"
                                                                                                    disabled="true"/></span>
                                <span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue"
                                                                            primary="true"/></span>
                            </div>
                        </msgpt:Popup>
                    </form:form>
                </c:if>
                <c:if test="${trashTpContext}">
                    <div class="alert alert-info" role="alert">
                        <strong class="mr-2">
                            <i class="fas fa-info-circle fa-lg mr-2"
                               aria-hidden="true"></i><fmtSpring:message
                                code="page.label.info"/>:
                        </strong>
                        <fmtSpring:message code="page.text.trash.touchpoint.does.not.apply"/>
                    </div>
                </c:if>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
        <msgpt:ContextMenu name="brandProfileList">
            <msgpt:ContextMenuEntry name="actioniFrame_1" link="#iFrameAction:1"><fmtSpring:message
                    code="page.label.edit"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_2" link="#actionSelected:2"><fmtSpring:message
                    code="page.label.delete"/></msgpt:ContextMenuEntry>
        </msgpt:ContextMenu>
    </msgpt:BodyNew>
</msgpt:Html5>