<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>

    <msgpt:HeaderNew>

        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Script src="includes/javascript/moreOrLessWidget.js"/>

        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>

        <msgpt:CalendarIncludes/>

        <style id="defaultContentViewStyles">
            ${default_css}
        </style>

        <msgpt:Script>
            <script>
                var iFramePopup_fullFrameAttr_cust = JSON.parse(JSON.stringify(iFramePopup_fullFrameAttr));
                iFramePopup_fullFrameAttr_cust.width = 1240;

                function iFrameView(path, eleId, e) {
                    if (e != undefined) {
                        if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
                            $("#" + eleId).iFramePopup($.extend({
                                src: path,
                                displayOnInit: true,
                                id: "globalDashboardBrandFrame",
                                screenMask: false,
                                appliedParams: {tk: "${param.tk}"},
                                beforePopupClose: function () {
                                    rebuildListTable();
                                }
                            }, iFramePopup_fullFrameAttr_cust));
                        }
                        e.preventDefault ? e.stopPropagation() : e.returnValue = false;
                    }
                }

                function iFrameAction(actionId) {
                    if (actionId == '2') {
                        openAddTaskModal(getSelectedIdsForTasks(), $('#taskMetadataSelect'), true);
                    }
                }

                function rebuildListTable(maintainCurrentPage) {
                    $("table.contentTable[id^='globalDashboardBrandDetails_']").DataTable().ajax.reload(null, !maintainCurrentPage);
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.columns = [
                        {
                            columnMap: 'name',
                            width: '90%',
                            sort: false
                        }
                    ];

                    return obj;
                }

                function getAsyncExtParams() {
                    var obj;
                    if (filterExistsAndIsSelected()) {
                        var filterSelection = getFilterSelection();
                        if (filterSelection.indexOf('\'') !== -1 || filterSelection.indexOf('\'') !== -1) {
                            filterSelection = encodeURIComponent(filterSelection);
                        }
                        obj = [
                            {"name": "listTableType", "value": "64"},
                            {
                                "name": "selectedBrandItemId", "value": getParam('selectedBrandItemId')
                            },
                            {
                                "name": "selectedItemId", "value": getParam('selectedItemId')
                            },
                            {
                                "name": "showMarkup", "value": getShowMarkupSelection()
                            },
                            {
                                "name": "filterSelection", "value": filterSelection
                            }
                        ];
                    } else {
                        obj = [
                            {"name": "listTableType", "value": "64"},
                            {
                                "name": "selectedBrandItemId", "value": getParam('selectedBrandItemId')
                            },
                            {
                                "name": "selectedItemId", "value": getParam('selectedItemId')
                            },
                            {
                                "name": "showMarkup", "value": getShowMarkupSelection()
                            }
                        ];
                    }
                    return obj;
                }

                function postListDrawCallback(nTable, o) {

                    formatResultContent( $(nTable) );

                    $(nTable).find('.dataTableLink,.dataTableSecondaryLink').each(function () {
                        var instId = parseId($(this));
                        $('.expandableTxtEle').each(function () {
                            initExpandableTxt($(this));
                        });
                    });

                    $(nTable).find('thead th .dataTableRecordCount').remove();
                    $(nTable).find('thead th:nth-of-type(2) div').append("<span class=\"dataTableRecordCount\" style=\"padding-left: 12px;\">(" + o._iRecordsTotal.toLocaleString() + ")</span>");
                    $(nTable).find('[data-toggle="tooltip"]').tooltip();

                    adjustIFrameHeight(frameElement);
                }

                function toggleMarkupBtn() {
                    if ($('#toggleMarkupBtn').is('.fa-toggle-off')) {
                        $('#toggleMarkupBtn').removeClass('fa-toggle-off').addClass('fa-toggle-on');
                    } else {
                        $('#toggleMarkupBtn').removeClass('fa-toggle-on').addClass('fa-toggle-off');
                    }
                    rebuildListTable(true);
                }

                function getShowMarkupSelection() {
                    return $('#toggleMarkupBtn').is('.fa-toggle-on');
                }

                function filterExistsAndIsSelected() {
                    if($("#filterSelect").length === 0) {
                        return false;
                    }

                    return $("#filterSelect").val() !== '';
                }

                function getFilterSelection() {
                    return $("#filterSelect").val();
                }

                function adjustIFrameHeight(ele) {
                    if (ele == null || !ele.contentWindow) {
                        return;
                    }
                    var contentHeight = $(ele.contentWindow.document.body).find('#page').height();
                    $(ele).css('height', contentHeight + 'px');
                    window.parent.adjustIFrameHeight(window.frameElement);
                    common.refreshParentIframeHeight();
                }

                function postListRenderFlagInjection(oObj) {
                    var binding = oObj.aData.binding;
                    var text = oObj.aData.name;

                    //Selection check select
                    text += binding;

                    //Selection permission flags
                    if (oObj.aData.flags.canAddTask)
                        text += "<input type='hidden' id='canAddTask_" + oObj.aData.dt_RowId + "' />";

                    return text;
                }

                function validateActionReq() {
                    var singleSelect = true;
                    var canAddTask = true;

                    // Resolve selection flags
                    if ($("input[id^='listItemCheck']:checked").length != 1)
                        singleSelect = false;
                    $("input[id^='listItemCheck']:checked").each(
                        function () {
                            var modelId = this.id.replace('listItemCheck_', '');
                            if (!exists('canAddTask_' + modelId))
                                canAddTask = false;
                        }
                    );

                    // Disable all actions
                    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
                    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');

                    // Enable actions based on flag status
                    if ($("input[id^='listItemCheck']:checked").length > 0) {

                        if(canAddTask){
                            $('.contextMenu a#actioniFrame_2').removeClass('disabled'); // Add task
                            $('.contextMenu a#actionOption_3').removeClass('disabled'); // Add task
                        }
                    }
                }

                window.crossLinkOpen = function (targetURL) {
                    if (targetURL.indexOf('?') !== -1) {
                        targetURL += '&tk=${param.tk}';
                    } else {
                        targetURL += '?tk=${param.tk}';
                    }

                    if (window.parent.opener != null) {
                        window.parent.opener.location.href = context + "/" + targetURL;
                    } else {
                        getTopFrame().location.href = context + "/" + targetURL;
                    }
                };

                $(function () {
                    $(".style_select").styleActionElement({labelAlign: true});
                });
            </script>
        </msgpt:Script>
        <style>
            .dataTables_wrapper > .dataTablesContentWrapper > table {
                position: inherit;
            }
            .resultContentContainer.contentContainer {
                max-width: 757px;
                overflow-x: auto;
                overflow-y: hidden;
            }
            .dataTables_wrapper {
                padding-bottom: 10px;
            }
        </style>
    </msgpt:HeaderNew>

    <msgpt:BodyNew type="minimal" cssStyle="background-color: transparent;">
        <form:form method="post" modelAttribute="command">
            <form:errors path="*">
                <msgpt:Information errorMsgs="${messages}" type="error"/>
            </form:errors>
            <div style="overflow: hidden; width: 842px;">
                <div class="actionsBarFilterContainer" style="border: none; padding-left: 15px; height: 30px;  width: 770px">
                    <div id="markupToggleContainer">
                        <div style="display: inline-block; vertical-align: middle;">
                            <i id="toggleMarkupBtn" onclick="toggleMarkupBtn()"
                               style="font-size: 19px; cursor: pointer; padding-top: 4px;"
                               class="far fa-toggle-on"></i>
                        </div>
                        <div style="display: inline-block; vertical-align: middle; font-size: 11px; padding-right: 8px; padding-left: 4px;">
                            <fmtSpring:message code="page.label.show.markup"/>
                        </div>
                    </div>
                    <div class="mainFilterSection">
                        <c:if test="${not empty command.filterItemsList}">
                            <div class="filterControl" style="padding-right: 6px">
                                <label for="filterSelect" style="padding-right: 6px"><fmtSpring:message
                                        code="page.label.filter"/></label>
                                <select id="filterSelect" onchange="rebuildListTable(true)">
                                    <option value=""><fmtSpring:message code="page.label.none"/></option>
                                    <c:forEach var="crtTerm" items="${command.filterItemsList}">
                                        <option value="${crtTerm}"><c:out value="${crtTerm}"/></option>
                                    </c:forEach>
                                </select>
                            </div>
                        </c:if>
                    </div>
                </div>
                    <msgpt:DataTable id="globalDashboardBrandDetails_${timeStamp}"
                                     async="true"
                                     numUnreorderableCols="0"
                                     columnVisibility="true"
                                     searchFilter="true"
                                     permitsShowAll="false"
                                     multiSelect="true" >
                    </msgpt:DataTable>
            </div>

            <!-- POPUP DATA -->
            <div id="actionSpecs" style="display: none;">
                <div id="actionSpec_3"> <!-- Add task with metadata -->
                    <div id="actionTitle_3"><fmtSpring:message code="page.label.add.task"/></div>
                    <div id="actionTaskMetadataSelect_3"></div>
                    <div id="actionTaskMetadataButtons_3"></div>
                </div>
            </div>

            <!-- POPUP INTERFACE -->
            <msgpt:Popup id="actionPopup">
                <div id="actionPopupInfoFrame">
                    <div id="actionPopupInfo">&nbsp;</div>
                </div>
                <div id="actionPopupTaskMetadataSelect">
                    <div class="formControl">
                        <label><span class="labelText"><fmtSpring:message
                                code="page.label.metadata.optional"/></span></label>
                        <div class="controlWrapper">
                            <select id="taskMetadataSelect" class="style_select">
                                <option value="0"><fmtSpring:message code="page.label.none"/></option>
                                <c:forEach var="taskMetadataFormDefinition"
                                           items="${taskMetadataFormDefinitions}">
                                    <option id="taskMetadataFormDefinitionOption_${taskMetadataFormDefinition.id}"
                                            value="${taskMetadataFormDefinition.id}">
                                        <c:out value="${taskMetadataFormDefinition.name}"/>
                                    </option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                </div>
                <!-- Add Task w/ Metadata -->
                <div id="actionPopupTaskMetadataButtons" class="actionPopupButtonsContainer">
                        <span id="taskCustomCancelBtnEnabled">
                            <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                        </span>
                    <span id="taskCustomContinueBtnEnabled">
                            <msgpt:Button URL="javascript:iFrameAction(2);"
                                          label="page.label.continue" primary="true"/>
                        </span>
                </div>
            </msgpt:Popup>
        </form:form>

        <msgpt:ContextMenu name="globalDashboardBrandDetails_${timeStamp}">
            <c:if test="${not hasTaskMetadataFormDef}">
                <msgpt:ContextMenuEntry name="actioniFrame_2" link="#iFrameAction:2"><fmtSpring:message
                        code="page.label.add.task"/></msgpt:ContextMenuEntry>
            </c:if>
            <c:if test="${hasTaskMetadataFormDef}">
                <msgpt:ContextMenuEntry name="actionOption_3" link="#actionSelected:3"><fmtSpring:message
                        code="page.label.add.task"/></msgpt:ContextMenuEntry>
            </c:if>
        </msgpt:ContextMenu>
    </msgpt:BodyNew>
</msgpt:Html5>
