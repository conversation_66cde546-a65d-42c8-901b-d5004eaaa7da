<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew extendedScripts="true" title="page.label.global.dashboard">

        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:CalendarIncludes/>

        <msgpt:Script src="includes/javascript/popupActions.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

        <msgpt:Script src="dashboards/javascript/globalDashboardUtil.js"/>

        <msgpt:Script>
            <script>

                function rebuildListTable(maintainCurrentPage) {
                    $('#rationalizerBrand').DataTable().ajax.reload(null, !maintainCurrentPage);
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.columns = [
                        {
                            columnMap: 'name',
                            columnName: client_messages.text.brand_description,
                            width: '90%',
                            sort: false
                        },
                        {
                            columnMap: 'countDisplayString',
                            columnName: client_messages.text.brand_violation,
                            width: '10%',
                            sort: false
                        }
                    ];

                    return obj;
                }

                function getAsyncExtParams() {
                    var obj = [
                        {"name": "listTableType", "value": "63"},
                        {
                            "name": "selectedItemId", "value": getParam('selectedItemId')
                        }
                    ];
                    return obj;
                }

                function postListDrawCallback(nTable, o) {
                    var listSubType = $(nTable).attr('id').split('_')[1];
                    $('#countSummary_'+listSubType).html( o._iRecordsTotal.toLocaleString() );

                    $(nTable).find('.expandableTxtEle').each( function() {
                        initExpandableTxt($(this));
                    });

                    common.refreshParentIframeHeight();
                }

                function postListRenderFlagInjection(oObj,nTable) {
                    var iFrameId = "brand-item-id";
                    var iFrameSrc = context + "/dashboards/global_dashboard_brand_details.form?selectedBrandItemId=" + oObj.aData.dt_RowId + "&selectedItemId=" + getSelectedNavTreeNodeId();

                    var text = oObj.aData.name;
                    text += "<input id='iFrameId' value="+iFrameId+" type='hidden' class='iframe-data' />";
                    text += "<input id='iFrameSrc' value="+iFrameSrc+" type='hidden' class='iframe-data' />";

                    return text;
                }

                $(function () {
                    var navTreeSrc = "${contextPath}/dashboards/global_dashboard_navigation_widget.form?tk=" + getParam('tk') + "&selectedItemId=" + getSelectedNavTreeNodeId();
                    $('#globalDashboardNavigationWidget').attr('src', navTreeSrc);
                });

                function getSelectedNavTreeNodeId() {
                    var selectedItemId = getParam('selectedItemId');
                    return selectedItemId ? selectedItemId : -1;
                }

                $.ajax({
                    type: "GET",
                    url: context + "/asyncGlobalDashboardInfo.form?" + "tk=" + getParam('tk') + "&action=5" + "&selectedItemId=" + getSelectedNavTreeNodeId(),
                    dataType: "json",
                    success: function (data) {
                        $('#brandViolationsNumberDisplayString').html(fmtClientMessage(client_messages.text.dashboard_brand_number, data.brandCount));
                    }
                });

                $.ajax({
                    type: "GET",
                    url: context+"/asyncGlobalDashboardInfo.form?" + "tk=" + getParam('tk') + "&action=6" + "&selectedItemId=" + getSelectedNavTreeNodeId(),
                    dataType: "json",
                    success: function(data) {
                        $('#title-selection-spinner').hide();
                        $('#title-selection').html(data.titleSelection);
                        $('#title-selection').show();
                    }
                });
            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <msgpt:BodyNew>
        <c:if test="${param.windowType != 'stand_alone'}">
            <msgpt:BannerNew edit="false"/>
            <msgpt:NewNavigationTabs edit="false" tab="0"/>
        </c:if>
        <msgpt:LowerContainer fullPanel="true" extendedWidth="true">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="true" style="width:1450px">
                <form:form method="post" modelAttribute="command" name="globalDashboardForm">
                    <form:errors path="*">
                        <msgpt:Information errorMsgs="${messages}" type="error"/>
                    </form:errors>
                    <c:if test="${param.windowType != 'stand_alone'}">
                        <h1 class="d-flex justify-content-start align-items-center h4 pb-2 mb-4">
                            <span class="text-dark"><fmtSpring:message code="page.label.global.dashboard"/></span>
                            <div class="d-flex ml-auto" role="group">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary active" type="button"
                                            onclick="javascriptHref('global_dashboard.form');">
                                        <fmtSpring:message code="page.label.global.dashboard"/>
                                    </button>
                                    <button class="btn btn-outline-primary" type="button"
                                            onclick="javascriptHref('my_dashboard.form');">
                                        <fmtSpring:message code="page.label.homepage"/>
                                        <span class="sr-only">(<fmtSpring:message
                                                code="client_messages.text.current"/>)</span>
                                    </button>
                                </div>
                            </div>
                        </h1>
                    </c:if>
                    <div class="row flex-nowrap mt-4">
                        <c:if test="${param.windowType != 'stand_alone'}">
                            <div id="navigationWidgetContainer" class="col-auto" style="width: 30rem;margin-right: 2rem; margin-left: 1rem;">
                                <div class="box-shadow-4 rounded bg-white" style="width: 30rem;">
                                    <div class="pb-1 border-top">
                                        <iframe
                                                id="globalDashboardNavigationWidget"
                                                name="globalDashboardNavigationWidget"
                                                src=""
                                                allowtransparency="true"
                                                frameborder="0"
                                                style="width: 30rem; height: 37rem; ">
                                        </iframe>
                                    </div>
                                </div>
                            </div>
                        </c:if>
                        <div class="col dashboardCardResultsContainer" style="width: 920px; padding-left: 0px">
                            <h5 class="d-flex justify-content-start align-items-center h5 pb-2 mb-2">
                                <i id="title-selection-spinner" class="fas fa-spinner fa-spin"></i>
                                <span id ="title-selection" class="text-dark"></span>
                            </h5>
                            <div class="box-shadow-4 rounded bg-white p-4">
                                <div class="px-2 pb-1">
                                    <c:if test="${param.windowType != 'stand_alone'}">
                                        <a style="color: #21150c; text-decoration: none;" href="global_dashboard.form?selectedItemId=${not empty param.selectedItemId ? param.selectedItemId : -1}">
                                            <i class="fas fa-chevron-left"></i> <fmtSpring:message code="page.label.back"/></a>
                                        <h6><br></h6>
                                    </c:if>
                                    <h5 style="color: #6D3075"><fmtSpring:message code="page.label.dashboard.brand.title"/></h5>
                                    <p id= "brandViolationsNumberDisplayString" style="font-weight: bold;"></p>
                                    <p><br></p>
                                    <msgpt:DataTable id="rationalizerBrand"
                                                     async="true"
                                                     numUnreorderableCols="0"
                                                     columnVisibility="true"
                                                     searchFilter="true"
                                                     permitsShowAll="false"
                                                     drillDown="true"
                                                     multiDrillDown="true" style="position: relative;">
                                    </msgpt:DataTable>
                                </div>
                            </div>
                        </div>
                    </div>
                </form:form>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
    </msgpt:BodyNew>
</msgpt:Html5>