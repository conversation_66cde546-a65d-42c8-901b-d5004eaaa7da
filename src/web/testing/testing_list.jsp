<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.testing" extendedScripts="true">

        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:CalendarIncludes/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>

        <msgpt:Script>
            <script>

                var iFramePopup_fullFrameAttr_cust = iFramePopup_fullFrameAttr;
                iFramePopup_fullFrameAttr_cust.width = 800;

                var $updateBtn, $deleteBtn, $actionMenu;

                $(function () {

                    $updateBtn = $('#updateBtn');
                    $deleteBtn = $('#deleteBtn');
                    $actionMenu = $('#actionMenu');

                    if ($actionMenu.length)
                        validateActionReq();
                    
                    $('#addTestBtn').iFramePopup($.extend({
                        src: context + "/testing/scenario_edit.form",
                        appliedParams: {tk: "${param.tk}",
                            documentId: "${param.documentId ? param.documentId : -1}",
                            collectionId: "${param.collectionId ? param.collectionId : -1}"
                        },
                        beforePopup: function (inst) {
                            if ($('#addTestBtn').hasClass('highlightedBtnDisabled'))
                                return false;
                        },
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr_cust));

                    pollForTestResult();

                });

                // *********  LIST TABLE FUNCTIONS: START  *********
                function pollForTestResult() {
                    var pollingElements = $('[id^="testStatusPollingContainer_"]');

                    if (pollingElements.length === 0) {
                        return;
                    }

                    var testIds = [];

                    // Test result polling init
                    pollingElements.each(function () {
                        if ($(this).attr('pollingInit') === undefined || $(this).attr('pollingInit') != "true") {
                            $(this).attr('pollingInit', 'true');
                            testIds.push(parseId(this));
                        }
                    });
                    if (testIds.length === 0) {
                        return;
                    }

                    var testIdParams = '';

                    for (var i = 0; i < testIds.length; i++) {
                        testIdParams += (((i > 0) ? '&id=' : 'id=') + testIds[i]);
                    }

                    var stampDate = new Date();
                    $.ajax({
                        type: "GET",
                        url: context + "/getTestingListResults.form?" + testIdParams + "&tk=" + getParam('tk') + "&cacheStamp=" + stampDate.getTime(),
                        dataType: "json",
                        success: function (data) {
                            processTestPollResult(data);
                        }
                    });

                    window.setTimeout(function () {
                        pollForTestResult()
                    }, 10000);
                }

                function processTestPollResult(data) {
                    if (data.error) {
                        console.error(data.error);
                    }

                    if (data.items && data.items.length > 0) {

                        for (var i = 0; i < data.items.length; i++) {
                            var _item = data.items[i];

                            var _job = $('[data-testid="' + _item.id + '"]');
                            _job && _job.replaceWith(_item.job);

                            var _status = $('#testStatusPollingContainer_' + _item.id);
                            _status && _status.replaceWith(_item.status);

                            var _date = $('#testDatePollingContainer_' + _item.id);
                            _date && _date.replaceWith(_item.date);

                            var _user = $('#testUserPollingContainer_' + _item.id);
                            _user && _user.replaceWith(_item.user);

                            var _results = $('#testResultContainer_' + _item.id);
                            _results && $(_results.parent()[0]).html(_item.results);

                            var _executionTimes = $('#testTimesContainer_' + _item.id);
                            _executionTimes && _executionTimes.replaceWith(_item.executionTimes);
                        }
                    }
                }

                function actionMenuSelected(el) {

                    var elSelectedId = $(el).children(':selected').attr('id');

                    if (elSelectedId.indexOf('actionOption_') !== -1) {
                        actionSelected(el);
                    } else {
                        iFrameAction(elSelectedId.replace('actioniFrame_', ''));
                    }
                }

                function iFrameView(path, eleId, e) {
                    if (e != undefined) {
                        if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
                            $("#" + eleId).iFramePopup($.extend({
                                src: path,
                                displayOnInit: true,
                                id: "testFrame",
                                appliedParams: {tk: "${param.tk}"},
                                beforePopupClose: function () {
                                    rebuildListTable();
                                }
                            }, iFramePopup_fullFrameAttr_cust));
                        }
                        e.preventDefault ? e.stopPropagation() : e.returnValue = false;
                    }
                }

                function toggleFilter(select) {
                    rebuildListTable();
                }

                function rebuildListTable() {
                    $('#testList').DataTable().ajax.reload(null, true);
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.rowId = 'dt_RowId';
                    obj.columns = [
                        {
                            columnMap: 'name',
                            columnName: client_messages.text.name,
                            sort: true,
                            width: '35%',
                            colVisToggle: false
                        },
                        {
                            columnMap: 'job',
                            columnName: client_messages.text.job,
                            sort: false,
                            width: '10%',
                            colVisToggle: false
                        },
                        {
                            columnMap: 'date',
                            columnName: client_messages.text.last_run_date,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'scheduled',
                            columnName: client_messages.text.next_scheduled_date,
                            sort: false,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'user',
                            columnName: client_messages.text.user,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'status',
                            columnName: client_messages.text.status,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'results',
                            columnName: client_messages.text.results,
                            sort: false,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'executionTimes',
                            columnName: client_messages.text.execution.execution_times_hms,
                            sort: false,
                            width: '15%',
                            colVisToggle: true
                        }
                    ];
                    return obj;
                }

                // Inject icon in list headers
                function headerLabelOverride(nHead, aData, iStart, iEnd, aiDisplay) {
                    $(nHead).find('th').each(function () {
                        var iconColumn = false;
                        if ($(this).text().indexOf(client_messages.text.next_scheduled_date) != -1) {
                            $(this).append("<i class=\"far fa-clock table-icon\"></i>");
                            iconColumn = true;
                        }

                        if (iconColumn)
                            $(this)
                                .contents()
                                .filter(function () {
                                    return this.nodeType == 3; //Node.TEXT_NODE
                                }).remove();
                    });
                }

                function getAsyncExtParams() {
                    var stampDate = new Date();
                    var obj = [
                        {"name": "listTableType", "value": "12"},
                        {"name": "displayMode", "value": "full"},
                        {"name": "documentId", "value": getParam('documentId') != "" ? getParam('documentId') : -1},
                        {
                            "name": "collectionId",
                            "value": getParam('collectionId') != "" ? getParam('collectionId') : -1
                        },
                        {"name": "testsAssignmentFilterId", "value": $('#testsListAssignmentFilter').val()},
                        {"name": "testsStatusFilterId", "value": $('#testsListStatusFilter').val()},
                        {"name": "globalContext", "value": $("#globalContextContainer").is('.globalContextEnabled')},
                        {"name": "cacheStamp", "value": stampDate.getTime()}
                    ];
                    return obj;
                }

                function postListDrawCallback(nTable) {
                    // Proof polling init
                    if ($(nTable).find("[id^='testStatusPollingContainer_']").length > 0) {
                        pollForTestResult();
                    }
                }

                function postListRenderFlagInjection(oObj) {
                    var binding = oObj.aData.binding;
                    var text = oObj.aData.name;

                    //Selection check select
                    text += binding;

                    //Selection permission flags
                    if (oObj.aData.flags.canUpdate)
                        text += "<input type='hidden' id='canUpdate_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canDelete)
                        text += "<input type='hidden' id='canDelete_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canRerun)
                        text += "<input type='hidden' id='canRerun_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.stateChanged)
                        text += "<input type='hidden' id='stateChanged_" + oObj.aData.dt_RowId + "' />";

                    return text;
                }

                // *********  LIST TABLE FUNCTIONS: END  *********

                // List actions: Edit
                function iFrameAction(actionId) {
                    var testId;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        testId = this.id.replace('listItemCheck_', '');
                    });

                    if (actionId == '1' || actionId == '6') {
                        $(actionId == '1' ? '#actioniFrame_' + actionId : '#actionOption_' + actionId).iFramePopup($.extend({
                            src: context + "/testing/scenario_edit.form",
                            displayOnInit: true,
                            id: "testFrame",
                            appliedParams: {'tk': "${param.tk}", 'testid': testId},
                            beforePopupClose: function () {
                                rebuildListTable();
                            }
                        }, iFramePopup_fullFrameAttr_cust));
                    }

                    actionCancel();
                }

                // Test list validation
                function validateActionReq() {
                    var singleSelect = true;
                    var canUpdate = true;
                    var canDelete = true;
                    var canRerun = true;
                    var stateChanged = false;
                    var editTestPerm = exists('editTestPerm');

                    // Resolve selection flags
                    if ($("input[id^='listItemCheck_']:checked").length != 1) {
                        singleSelect = false;
                    }
                    $("input[id^='listItemCheck_']:checked").each(
                        function () {
                            var testId = this.id.replace('listItemCheck_', '');
                            if (exists('canUpdate_' + testId)) {
                                canUpdate = true;
                            }
                            if (!exists('canDelete_' + testId)) {
                                canDelete = false;
                            }
                            if (!exists('canRerun_' + testId)) {
                                canRerun = false;
                            }
                            if (exists('stateChanged_' + testId)) {
                                stateChanged = true;
                            }
                        }
                    );

                    //Disable all context menu entries
                    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
                    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');
                    common.disableElement($deleteBtn);
                    common.disableElement($updateBtn);
                    $actionMenu.data('complexDropdown').disableAllTheOptions();

                    if ($("input[id^='listItemCheck_']:checked").length > 0) {
                        if (singleSelect) {
                            if (canUpdate) {
                                $('a#actioniFrame_1').removeClass('disabled');
                                common.enableElement($updateBtn);
                            }
                        }
                        if (getSelectedIds() !== '') {
                            $('a#actionOption_7').removeClass('disabled');
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_7');
                        } else {
                            $('a#actionOption_7').addClass('disabled');
                            $actionMenu.data('complexDropdown').disableOptionById('actionOption_7');
                        }
                        if (canUpdate) {
                            if (!stateChanged) {	// State does not change
                                if(canRerun) {
                                    $('a#actionOption_3').removeClass('disabled');
                                    $actionMenu.data('complexDropdown').enableOptionById('actionOption_3');
                                    $actionMenu.data('complexDropdown').showOptionById('actionOption_3');
                                }
                                $('a#actionOption_6').hide();
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_6');
                            } else {				// State chagned
                                $('a#actionOption_3').hide();
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_3');
                                if(canRerun){
                                    $('a#actionOption_6').removeClass('disabled');
                                    $actionMenu.data('complexDropdown').enableOptionById('actionOption_6');
                                }
                            }
                        }
                        if (canDelete) {
                            $('a#actionOption_2').removeClass('disabled');
                            common.enableElement($deleteBtn);
                        }

                        if (${showSendToQaModule and enableSendToQaModule}) {
                            $('a#actionOption_8').removeClass('disabled');
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_8');
                        }
                    }
                }

                // Retrieve selected ids seperated by ','
                function getSelectedIds() {
                    var selectedIds = '';
                    var selectedCount = $("input[id^='listItemCheck']:checked").length;
                    var count = 0;
                    $("input[id^='listItemCheck']:checked").each(
                        function () {
                            selectedIds += this.id.replace('listItemCheck_', '');
                            count++;
                            if (count != selectedCount)
                                selectedIds += '_';
                        }
                    );
                    return selectedIds;
                }

            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>
    <msgpt:BodyNew>
        <!-- FLAGS AND PERMISSIONS -->
        <msgpt:IfAuthGranted authority="ROLE_TEST_EDIT">
            <input type="hidden" id="editTestPerm">
        </msgpt:IfAuthGranted>
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_TESTING %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_TESTING %>"/>
        <msgpt:ContextBarNew languageContextApplied="false" collectionContextApplied="true"
                             touchpointSetupContext="true" globalContextApplied="true"
                             channelContextApplied="${not isGlobalContext}"/>
        <msgpt:LowerContainer fullPanel="true" extendedWidth="true" cssClass="fluid-content">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="true">
                <c:if test="${not trashTpContext }">
                    <form:form method="post" modelAttribute="command">
                        <form:errors path="*">
                            <msgpt:Information errorMsgs="${messages}" type="error"/>
                        </form:errors>
                        <msgpt:IfAuthGranted authority="ROLE_TEST_EDIT">
                            <c:choose>
                                <c:when test="${isGlobalContext}">
                                    <div id="infoMsg_singleSelectorVariant" class="alert alert-info" role="alert">
                                        <strong class="mr-2">
                                            <i class="fas fa-info-circle fa-lg mr-2"
                                               aria-hidden="true"></i><fmtSpring:message
                                                code="page.label.info"/>:
                                        </strong>
                                        <fmtSpring:message
                                                code="page.text.select.touchpoint.context.to.add.test"/>
                                    </div>
                                </c:when>
                            </c:choose>
                        </msgpt:IfAuthGranted>
                        <h1 class="h4 d-flex justify-content-start align-items-center pb-2 mb-4">
                            <fmtSpring:message code="page.label.tests"/>
                            <span class="ml-3 pl-3 border-left">
                                <a class="d-flex btn btn-primary btn-sm"
                                   href="javascript:javascriptHref('test_suite_list.form');">
                                    <fmtSpring:message code="page.label.suites"/>
                                </a>
                            </span>
                        </h1>
                        <div class="box-shadow-4 rounded bg-white p-4">
                            <div class="px-2 py-1">
                                <div class="mb-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <form:hidden path="actionValue" id="actionElement"/>
                                        <msgpt:IfAuthGranted authority="ROLE_TEST_EDIT">
                                            <div class="mr-3">
                                                <button id="addTestBtn" class="btn btn-primary"
                                                        type="button" ${isGlobalContext ? 'disabled' : '' }>
                                                    <i class="far fa-plus-circle mr-2"
                                                       aria-hidden="true"></i>
                                                    <c:out value='${msgpt:getMessage("page.label.add")}'/>
                                                </button>
                                            </div>
                                        </msgpt:IfAuthGranted>
                                        <div class="btn-group border-separate mr-auto" role="group"
                                             aria-label="${msgpt:getMessage("page.label.actions")}">
                                            <button id="updateBtn" type="button"
                                                    class="btn btn-dark"
                                                    onclick="iFrameAction(1);" disabled>
                                                <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="action.button.label.update"/>
                                            </button>
                                            <button id="deleteBtn" type="button" class="btn btn-dark"
                                                    onclick="actionSelected(2);" disabled>
                                                <i class="far fa-trash-alt mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="page.label.delete"/>
                                            </button>
                                            <div class="btn-group">
                                                <select title="${msgpt:getMessage('page.label.more')}"
                                                        id="actionMenu"
                                                        class="complex-dropdown-select"
                                                        aria-label="${msgpt:getMessage('page.label.more')}"
                                                        data-toggle="complex-dropdown"
                                                        data-action="menu"
                                                        data-dropdown-class="btn-dark"
                                                        onchange="actionMenuSelected(this)">
                                                    <option id="actionOption_7" disabled>
                                                        <fmtSpring:message code="page.label.download.results"/>
                                                    </option>
                                                    <option id="actionOption_3" disabled>
                                                        <fmtSpring:message code="page.label.rerun"/>
                                                    </option>
                                                    <option id="actionOption_6" class="d-none" disabled>
                                                        <fmtSpring:message code="page.label.rerun"/>
                                                    </option>
                                                    <c:if test="${showSendToQaModule}">
                                                        <option id="actionOption_8" disabled>
                                                            <fmtSpring:message code="page.label.upload.blue.relay"/>
                                                        </option>
                                                    </c:if>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="mr-3 colVisToggleContainer" data-toggle="tooltip"
                                             title="${msgpt:getMessage('client_messages.label.toggle_columns')}">
                                        </div>
                                        <div class="form-group position-relative d-inline-block m-0">
                                            <label for="listSearchInput"
                                                   class="sr-only"><fmtSpring:message
                                                    code="page.label.search"/></label>
                                            <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                               style="z-index: 1;" aria-hidden="true"></i>
                                            <msgpt:InputFilter type="description">
                                                <input id="listSearchInput" type="text" size="25"
                                                       class="form-control bg-lightest has-control-l border-0"
                                                       placeholder="${msgpt:getMessage('page.label.search')}"/>
                                            </msgpt:InputFilter>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center pt-1">
                                            <span class="text-dark mr-1" id="filter">
                                                <fmtSpring:message code="page.label.filter"/>:
                                            </span>
                                        <div class="mx-2">
                                            <select id="testsListAssignmentFilter"
                                                    class="complex-dropdown-select persistedValue"
                                                    aria-labelledby="filter"
                                                    data-toggle="complex-dropdown"
                                                    data-menu-class="dropdown-custom mt-2"
                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                    onchange="toggleFilter(this)">
                                                <c:forEach items="${testAssignmentFilterTypes}"
                                                           var="currentFilter">
                                                    <option id="testsListAssignmentFilterOption_${currentFilter.id}"
                                                            value="${currentFilter.id}"><fmtSpring:message
                                                            code="${currentFilter.displayMessageCode}"/></option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                        <span class="text-dark mx-1" id="test-status">
                                                <fmtSpring:message code="page.text.tests.which.are"/>
                                            </span>
                                        <div class="ml-2">
                                            <select id="testsListStatusFilter"
                                                    class="complex-dropdown-select persistedValue"
                                                    aria-labelledby="test-status"
                                                    data-toggle="complex-dropdown"
                                                    data-menu-class="dropdown-custom mt-2"
                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                    onchange="toggleFilter(this)">
                                                <option id="testsListStatusFilter_0" value="0">
                                                    <fmtSpring:message
                                                            code="page.label.any.status"/></option>
                                                <c:forEach items="${testStatusFilterTypes}"
                                                           var="currentFilter">
                                                    <option id="testsListStatusFilter_${currentFilter.id}"
                                                            value="${currentFilter.id}"><fmtSpring:message
                                                            code="${currentFilter.displayMessageCode}"/></option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white">
                                    <msgpt:DataTable id="testList" listHeader="page.label.tests"
                                                     async="true"
                                                     columnReorder="true" numUnreorderableCols="2"
                                                     columnVisibility="true"
                                                     drillDown="false" multiSelect="true"
                                                     tableToolsButtons="true"
                                                     searchFilter="true">
                                    </msgpt:DataTable>
                                </div>
                            </div>
                        </div>

                        <!-- POPUP DATA -->
                        <div id="actionSpecs" style="display: none;">
                            <!-- ACTIONS POPUP DATA -->
                            <div id="actionSpec_2" type="simpleConfirm" submitId="2"> <!-- Delete test scenario -->
                                <div id="actionTitle_2"><fmtSpring:message code="page.label.confirm.delete.test"/></div>
                                <div id="actionInfo_2"><fmtSpring:message code="page.text.delete.tests"/></div>
                            </div>
                            <div id="actionSpec_6"> <!-- Test state changed, redirect confirmation -->
                                <div id="actionTitle_6"><fmtSpring:message code="page.label.update.test"/></div>
                                <div id="actionInfo_6"><fmtSpring:message
                                        code="page.text.working.copies.associated.with.test.have.changed"/></div>
                                <div id="actionCustomButtons_6"></div>
                            </div>
                            <div id="actionSpec_7" type="simpleConfirm" submitId="7">
                                <div id="actionTitle_7"><fmtSpring:message code="page.label.confirm.download"/></div>
                                <div id="actionInfo_7"><fmtSpring:message code="page.text.download.test.results"/></div>
                            </div>
                            <div id="actionSpec_8" type="simpleConfirm" submitId="8">
                                <div id="actionTitle_8"><fmtSpring:message
                                        code="page.label.confirm.upload.blue.relay"/></div>
                                <div id="actionInfo_8"><fmtSpring:message code="page.text.upload.blue.relay"/></div>
                            </div>
                        </div>

                        <!-- POPUP INTERFACE -->
                        <msgpt:Popup id="actionPopup" theme="minimal">
                            <div id="actionPopupInfoFrame">
                                <div id="actionPopupInfo">&nbsp;</div>
                            </div>
                            <div id="actionPopupCustomButtons" class="actionPopupButtonsContainer">
                                <span id="customCancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                                label="page.label.cancel"/></span>
                                <span id="customContinueBtnEnabled"><msgpt:Button URL="javascript:iFrameAction(6)"
                                                                                  label="page.label.continue"
                                                                                  primary="true"/></span>
                            </div>
                            <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                                <span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                  label="page.label.cancel"
                                                                                                  disabled="true"/></span>
                                <span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                          label="page.label.cancel"/></span>
                                <span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                    label="page.label.continue"
                                                                                                    disabled="true"/></span>
                                <span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue"
                                                                            primary="true"/></span>
                            </div>
                        </msgpt:Popup>
                    </form:form>
                </c:if>

                <c:if test="${trashTpContext}">
                    <div class="alert alert-info" role="alert">
                        <strong class="mr-2">
                            <i class="fas fa-info-circle fa-lg mr-2"
                               aria-hidden="true"></i><fmtSpring:message
                                code="page.label.info"/>:
                        </strong>
                        <fmtSpring:message code="page.text.trash.touchpoint.does.not.apply"/>
                    </div>
                </c:if>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
        <msgpt:ContextMenu name="testList">
            <msgpt:ContextMenuEntry name="actioniFrame_1" link="#iFrameAction:1"><fmtSpring:message
                    code="page.label.edit"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_2" link="#actionSelected:2"><fmtSpring:message
                    code="page.label.delete"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_7" link="#actionSelected:7"><fmtSpring:message
                    code="page.label.download.results"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_3" link="#actionSelected:3"><fmtSpring:message
                    code="page.label.rerun"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_6" link="#actionSelected:6"><fmtSpring:message
                    code="page.label.rerun"/></msgpt:ContextMenuEntry>
            <c:if test="${showSendToQaModule}">
                <msgpt:ContextMenuEntry name="actionOption_8" link="#actionSelected:8">
                    <fmtSpring:message code="page.label.upload.blue.relay"/>
                </msgpt:ContextMenuEntry>
            </c:if>
        </msgpt:ContextMenu>
    </msgpt:BodyNew>
</msgpt:Html5>