<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.model.testing.TestScenario"%>
<%@page import="com.prinova.messagepoint.model.admin.EventType"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>
<%@ page import="com.prinova.messagepoint.controller.testing.TestSuiteEditController" %>

<%@ include file="../includes/includes.jsp" %>

<c:set var="formSubmitTypeSubmit" value="<%=TestSuiteEditController.SUBMIT_TYPE_SUBMIT%>" />

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.testing" viewType="<%= MessagepointHeader.ViewType.EDIT %>">
	<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />
	<msgpt:CalendarIncludes/>

	<msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js" />
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css" />
	
	<msgpt:Script src="includes/javascript/dynamic_tables.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/dataFilesPopup/jquery.dataFilesPopup.js" />
	<msgpt:Script src="includes/javascript/popupActions.js" />

	<msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

	<msgpt:Script>
		<script>

            function initBtn(ele, type) {

                $(ele)
                    .mouseover( function() {
                        if ( !$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight .actionBtn_toggleSelect]') ) {
                            $(this).removeClass('actionBtn');
                            $(this).addClass('actionBtn_hov');
                        }
                    })
                    .mouseout( function() {
                        if ( !$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight .actionBtn_toggleSelect') ) {
                            $(this).removeClass('actionBtn_hov');
                            $(this).addClass('actionBtn');
                        }
                    });


                if ( type == "toggle" ) {
                    $(ele)
                        .click( function() {
                            if ( !$(this).hasClass('actionBtn_disabled') ) {
                                if ( $(this).hasClass('actionBtn_toggleSelectHighlight') )
                                    $(this).removeClass('actionBtn_toggleSelectHighlight').addClass('actionBtn');
                                else
                                    $(this).removeClass('actionBtn').addClass('actionBtn_toggleSelectHighlight');
                            }

                        });
                } else if ( type == "button" ) {
                    $(ele)
                        .mousedown( function() {
                            if ( !$(this).hasClass('actionBtn_disabled') )
                                $(this).removeClass('actionBtn actionBtn_hov').addClass('actionBtn_selected');
                        })
                        .mouseup( function() {
                            if ( !$(this).hasClass('actionBtn_disabled') )
                                $(this).removeClass('actionBtn_selected').addClass('actionBtn');
                        });
                }

            }

            function toggleWorkingCopyChecks(isToggleEvent) {
                if ( isToggleEvent ) {
                    if ( $('#selectAllWorkingCopiesCheckToggle').is(':checked') )
                        $('.wcCheckbox:visible:not(:checked)').click();
                    else
                        $('.wcCheckbox:visible:checked').click();
                } else {
                    if ( $('.wcCheckbox:visible:checked').length == $('.wcCheckbox:visible').length )
                        $('#selectAllWorkingCopiesCheckToggle').attr('checked','checked');
                    else
                        $('#selectAllWorkingCopiesCheckToggle').removeAttr('checked');
                }
            }

            function requestDataForCompositionPackageSelect(targetId) {
                var stampDate = new Date();
                var urlLink;

                if ( ${not empty tpCollection} )
                    urlLink = context+"/getDataForSelectMenu.form?touchpointCollectionId="+targetId+"&type=tp_collection_compositionFileSet&cacheStamp="+(stampDate.getTime());
                else
                    urlLink = context+"/getDataForSelectMenu.form?documentId="+targetId+"&type=touchpoint_compositionFileSet&cacheStamp="+(stampDate.getTime());

                $.ajax({
                    type: "GET",
                    url: urlLink,
                    dataType: "xml",
                    success: function(data) {
                        initCompositionPackageSelect(data);
                    }
                });
            };

            function initCompositionPackageSelect(data) {
                if ( data.getElementsByTagName("packageOptions").length > 0 ) {
                    var packageOptions = $(data).find('packageOptions').text();
                    $('#compositionFilePackageSelect').replaceAllOptions(packageOptions);
                    if ( $('#initCompositionFileSetValue').val() != "" )
                        $('#compositionFilePackageSelect').selectOptionByAttr("value",$('#initCompositionFileSetValue').val());
                    else
                        $('#compositionFilePackageSelect').selectOptionByAttr("value",$('#compositionFilePackageSelect option:selected').val());
                }
            }

            function useAllInProcessChange() {
                if ( $('.itemSelection').length > 0 ) {
                    $('.itemSelection').toggle();
                } else {
                    var x = document.getElementById('command');
                    if( x.action.indexOf("?") != -1 ) {
                        x.action = x.action + "&useAllInProcess=false";
                    } else {
                        x.action = x.action + "?useAllInProcess=false";
                    }
                    var form = document.forms['command'];
                    form.submit();
                }
            }

			function pdfAnnotations() {
				if ( $('.itemSelection').length > 0 ) {
					$('.itemSelection').toggle();
				} else {
					var x = document.getElementById('command');
					if( x.action.indexOf("?") != -1 ) {
						x.action = x.action + "&pdfAnnotations=false";
					} else {
						x.action = x.action + "?pdfAnnotations=false";
					}
					var form = document.forms['command'];
					form.submit();
				}
			}

            function onClickFunction( input ) {
                // get elements by name
                var inputs = document.getElementsByName(input.name);

                for( var i = 0; i < inputs.length ; i++ ) {
                    var thisInput = inputs[i];
                    if( thisInput.value == input.value )
                        thisInput.checked = input.checked;
                }

                // Manage wc/active icons
                var contentObjectId = $(input).val();
                if ( $(input).is(':checked') ) {
                    $('.itemSelection').find("#activeIcon_"+contentObjectId).each(function(){$(this).hide();});
                    $('.itemSelection').find("#workingCopyIcon_"+contentObjectId).each(function(){$(this).showEle('normal');});
                } else {
                    $('.itemSelection').find("#workingCopyIcon_"+contentObjectId).each(function(){$(this).hide();});
                    $('.itemSelection').find("#activeIcon_"+contentObjectId).each(function(){$(this).showEle('normal');});
                }

                toggleWorkingCopyChecks(false);
            }

            function dataResourceAddAction(){
// 		$("body").append( "<div class=\"iFramePopupSubPageScreen\"></div>" );
// 		$('.iFramePopupSubPageScreen').css({height: $(window.document).height(), opacity: 0}).fadeTo(1000,0.55);
                $('#addDataResourceBtn').iFramePopup({
                    width			: 600,
                    height 			: 400,
                    displayOnInit	: true,
                    screenMask		: false,
                    src				: context+"/dataadmin/data_resource_edit.form",
                    appliedParams	: {tk : "${param.tk}", touchpointContext: true, documentId: "${empty tpCollection ? document.id : '-1'}", collectionId: "${not empty tpCollection ? tpCollection.id : '-1'}"},
                    closeBtnId		: "cancelBtn_button",
                    beforePopupClose: function() {
                        window.location.reload();
                    }
                });
            }

            function toggleChannelContextMessages() {
                if ( $('#channelSelect').length != 0 ) {
                    var channelContextId = $('#channelSelect').val();
                    $("[channel_context=2],[channel_context=3],[channel_context=4]").hide();
                    $("[channel_context=" + channelContextId + "]").show();

					if (channelContextId == 2) {
						$('.compositionPackageContainer').show();
					} else {
						$('.compositionPackageContainer').hide();
					}

					if(channelContextId == 4){
						$("#pdfAnnotations").hide();
					} else {
						$("#pdfAnnotations").show();
						$("#flipToggle_pdfAnnotations").each( function() {
							var currentBinding = $(this);
							$(this).iButton({
								labelOn: $(currentBinding).attr('title').split(';')[0],
								labelOff: $(currentBinding).attr('title').split(';')[1],
								resizeHandle: false,
								resizeContainer: "auto",
							});
						});
					}
                }
            }

			function setupActionPopupVisibilityObserver(){
				var observer = new MutationObserver(onActionPopupVisible);
				observer.observe(document.querySelector('div#actionPopup'), {
					attributes: true,
					attributeFilter: ["style"],
					attributeOldValue: false
				});
			}

			function onActionPopupVisible(mutations) {
				var stop = false;
				mutations.forEach(function(mutation) {
					if(!stop && mutation.attributeName === 'style'){
						if(mutation.target.style.display !== 'none'){
							setDefaultCheckboxForPopup();
							stop = true;
						}
					}
				});
			}

			function setDefaultCheckboxForPopup(){
				$('#rerunTestSuiteCheckbox').prop("checked",true);
				changeRerunChecks($('#rerunTestSuiteCheckbox'));
			}

            function changeRerunChecks(ele){
                if($(ele).is(':checked')){
                    $('input.rerunCheckbox').each(function(){
                        if($(this).attr('id') != $(ele).attr('id')){
                            $(this).prop('checked', false);
                        }
                    });
                }

                if($('input.rerunCheckbox:checked').length!=1){
                    $('#continueBtnEnabled').hide();
                    $('#continueBtnDisabled').show();
                }else{
                    $('#continueBtnEnabled').show();
                    $('#continueBtnDisabled').hide();
                }
            }

            $( function() {
            	setupActionPopupVisibilityObserver();
				loadTimeSelect();

                $("input:button,.style_select").styleActionElement({labelAlign: true, maxItemsInList: 10, maxMenuItemLength: 60, maxItemDisplay: 4});
                requestDataForCompositionPackageSelect(${not empty tpCollection? tpCollection.id : document.id});

                toggleChannelContextMessages();

                $('#dataResourceSelectBox').dataFilesPopup({
                    popupLocation	: 'right',
                    type			: 'style_select',
                    nTk				: '${nodeGUID}',
                    uTk				: '${webDAVToken}',
                    permitEditing	: ${canEditDataFiles}
                });

                $('#compositionFilePackageSelect_menuTable').hoverIntent({
                    sensitivity		: 50, // number = sensitivity threshold (must be 1 or higher)
                    interval		: 700,   // number = milliseconds of polling interval
                    over			: function(e) {
                        var currentPackage 		= $('#compositionFilePackageSelect').find('option:selected');
                        var packageId 			= $(currentPackage).val();
                        var configFileName 		= $(currentPackage).attr('configurationFileName');
                        var templateFileName 	= $(currentPackage).attr('templateFileName');

                        if ( packageId != 0 ) {
                            $(this).popupFactory({
                                title				: $(currentPackage).attr('packageName'),
                                popupLocation		: "right",
                                trigger				: "instant",
                                fnSetContent		: function(o) {
                                    return 	"<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" +
                                        "<div style=\"word-wrap: break-word;\">" + client_messages.text.package_file + ":" + configFileName + "</div>" +
                                        "<div style=\"word-wrap: break-word;\">" + client_messages.text.template_file + ":" + templateFileName + "</div>" +
                                        "</div>";
                                }
                            });
                        }
                    },  // function = onMouseOver callback (required)
                    out				: function() {
                        popupFactoryRemove('testscenarioedit_package');
                    }
                });

                $("#flipToggle_useAllInProcess").each( function() {
                    var currentBinding = $(this);
                    $(this).iButton({
                        labelOn: $(currentBinding).attr('title').split(';')[0],
                        labelOff: $(currentBinding).attr('title').split(';')[1],
                        resizeHandle: false,
                        resizeContainer: "auto",
                        change: function() {
                            useAllInProcessChange();
                        }
                    });
                });

                $("#flipToggle_useAllInProcessLib").each( function() {
                    var currentBinding = $(this);
                    $(this).iButton({
                        labelOn: $(currentBinding).attr('title').split(';')[0],
                        labelOff: $(currentBinding).attr('title').split(';')[1],
                        resizeHandle: false,
                        resizeContainer: "auto"
                    });
                });

				$("#flipToggle_pdfAnnotations").each( function() {
					var currentBinding = $(this);
					$(this).iButton({
						labelOn: $(currentBinding).attr('title').split(';')[0],
						labelOff: $(currentBinding).attr('title').split(';')[1],
						resizeHandle: false,
						resizeContainer: "auto"
					});
				});

                $("#flipToggle_debugMode").each( function() {
                    var currentBinding = $(this);
                    $(this).iButton({
                        labelOn: $(currentBinding).attr('title').split(';')[0],
                        labelOff: $(currentBinding).attr('title').split(';')[1],
                        resizeHandle: false,
                        resizeContainer: "auto",
                    });
                });

				$("#flipToggle_autoQaSend").each( function() {
					var currentBinding = $(this);
					$(this).iButton({
						labelOn: $(currentBinding).attr('title').split(';')[0],
						labelOff: $(currentBinding).attr('title').split(';')[1],
						resizeHandle: false,
						resizeContainer: "auto",
					});
				});

                removeQueryStringParameter(document.getElementById('command'), "useAllInProcess");

                initBtn($('#addDataResourceBtn'),"button");

                toggleWorkingCopyChecks(false);

                $(".style_multiselect").styleActionElement({ maxItemDisplay: 4 });
                $(".actionBtnMultiselect").css("margin-left", "0px");
            });

            function updateSubmitTypeAndSubmitForm(subitValue){
                form = document.forms['command'];
                submitElement = form.elements['submittype'];
                submitElement.value = subitValue;
                form.submit();
            }

            function submitForm(){
                updateSubmitTypeAndSubmitForm("${formSubmitTypeSubmit}");
            }

			function schedulerChange(){
				var x = document.getElementById('command');
				var schedParams = "deliveryEventType=" + $('#schedulerTypeCheckbox input:checked').val() +
						(exists('repeatingCheckbox')? "&repeatingEvent=" + $('#repeatingCheckbox input:checked').val() : '') +
						(exists('frequencyTypeCheckbox')? "&sFrequencyTypeId=" + $('#frequencyTypeCheckbox input:checked').val() : '') +
						(exists('dailyFrequencyCheckbox')? "&sDailyFrequencyId=" + $('#dailyFrequencyCheckbox input:checked').val() : '') +
						(exists('hiddenDateTimeField')? "&sStartDate=" + new Date($('#hiddenDateTimeField').val()).getTime() : '') +
						(exists('endDate')? "&sEndDate=" + new Date($('#endDate').val()).getTime() : '');

				var urlParams = getUrlParameter(x.action);
				var baseURL = x.action.split('?')[0];
				x.action = baseURL + '?tk=' + urlParams['tk'] + '&testSuiteId=' + (urlParams['testSuiteId']?urlParams['testSuiteId']:'-1') + '&' + schedParams;
				var form = document.forms['command'];
				form.submit();
			}

			function getUrlParameter(url) {
				var vars = {};
				var parts = url.replace(/[?&]+([^=&]+)=([^&]*)/gi, function(m,key,value) {
					vars[key] = value;
				});
				return vars;
			}

			function timeChange(element) {
				var hour = document.getElementById('time_hours');
				var minutes = document.getElementById('time_minutes');
				if (element.options[element.selectedIndex].innerHTML == '--') {
					hour.options[0].selected = 'true';
					minutes.options[0].selected = 'true';
				}
				setTimeDateBindingValue();
			}



			function getTimeValue() {
				var hour = document.getElementById('time_hours');
				var minutes = document.getElementById('time_minutes');
				var am_pm = document.getElementById('time_AM_PM');

				var time = hour.options[hour.selectedIndex].innerHTML + ":" +
						minutes.options[minutes.selectedIndex].innerHTML + " " +
						am_pm.options[am_pm.selectedIndex].innerHTML;
				if ( time.indexOf("--") != -1 )
					time = "";
				return time;
			}

			function setTimeDateBindingValue() {
				var time = getTimeValue();
				var date = document.getElementById("startDate").value;
				document.getElementById("hiddenDateTimeField").value = date + " " + time;
			}

			// Scheduler
			function loadTimeSelect() {
				if ($("#startDate").length == 0)
					return;
				var hours = new Array;
				hours[0] = createOption("--");
				hours[1] = createOption("01");
				hours[2] = createOption("02");
				hours[3] = createOption("03");
				hours[4] = createOption("04");
				hours[5] = createOption("05");
				hours[6] = createOption("06");
				hours[7] = createOption("07");
				hours[8] = createOption("08");
				hours[9] = createOption("09");
				hours[10] = createOption("10");
				hours[11] = createOption("11");
				hours[12] = createOption("12");
				setOptions("time_hours",hours);

				var minutes = new Array;
				minutes[0] = createOption("--");
				minutes[1] = createOption("00");
				minutes[2] = createOption("15");
				minutes[3] = createOption("30");
				minutes[4] = createOption("45");
				setOptions("time_minutes",minutes);

				var am_pm = new Array;
				am_pm[0] = createOption("AM");
				am_pm[1] = createOption("PM");
				setOptions("time_AM_PM",am_pm);

				var rawDateTime = trim(document.getElementById("hiddenDateTimeField").value);
				var dateTimeComponents = rawDateTime.split(' ');

				if ( rawDateTime.indexOf(',') != -1 ) {
					document.getElementById("startDate").value = dateTimeComponents[0]+" "+dateTimeComponents[1]+" "+dateTimeComponents[2];
				}
				if ( rawDateTime.indexOf(',') != -1 && rawDateTime.indexOf(':') != -1 ) {
					var time = dateTimeComponents[3];
					setSelectValue("time_hours", time.split(':')[0]);
					setSelectValue("time_minutes", time.split(':')[1]);
					setSelectValue("time_AM_PM", dateTimeComponents[4]);
				} else if ( rawDateTime.indexOf(':') != -1 ) {
					var time = dateTimeComponents[0];
					setSelectValue("time_hours", time.split(':')[0]);
					setSelectValue("time_minutes", time.split(':')[1]);
					setSelectValue("time_AM_PM", dateTimeComponents[1]);
				}
			}

			function setSelectValue(id, value) {
				var selectElement = document.getElementById(id);
				for (var i=0; i<selectElement.options.length; i++)
					if (selectElement.options[i].innerHTML == value)
						selectElement.options[i].selected = 'true';
			}

			function createOption(optionName) {
				var option = new Option(optionName);
				return option;
			}

			function setOptions(id, optionArray) {
				selectElement = document.getElementById(id);
				for (var i=0; i<optionArray.length; i++) {
					selectElement.options[i] = optionArray[i];
				}
			}



		</script>
	</msgpt:Script>
</msgpt:HeaderNew>
<msgpt:BodyNew theme="minimal" type="iframe">
	
	<c:set var="isNew" value="${empty param.testSuiteId}" />
	<c:set var="newEditKey" value="page.label.createnew" />
	<c:choose>
		<c:when test="${!isNew}">
			<c:set var="newEditKey" value="page.label.edit" />
		</c:when>
		<c:otherwise>
			<input type="hidden" id="isNew" />
		</c:otherwise>
	</c:choose>
		
	<div style="display:none ">
		<!-- Write out select boxes representing each data resources. -->
		<c:forEach var="document" items="${documents}">
			<select id='documentDataResourceSB${document.id}' >
				<c:forEach var="dataResource" items="${dataResourceMap[document]}" >
					<option value="${dataResource.id}" ${ ( dataResource.id == command.dataResource.id )? 'selected' : '' }>${dataResource.name}</option>
				</c:forEach>
			</select>
		</c:forEach>
	</div>

	<form:form>
		<c:if test="${!canUpdate}">
			<msgpt:Information type="error">
				<fmtSpring:message code="error.message.action.not.permitted"/>
			</msgpt:Information>
		</c:if>
		<c:if test="${empty param.nprSaveSuccess && canUpdate}">
		
			<div id="popupHeaderTitle" style="display: none;">
				<span class="titleText">
					<c:out value='${msgpt:getMessage(newEditKey)} ${msgpt:getMessage("page.label.test.suite")} ${not empty command.testSuite.name ? ":" : ""} ${command.testSuite.name}' />&nbsp;
				</span>
			</div>
			
			<div class="label" style="border-bottom: 1px solid #bbb;">
				<!-- Buttons -->
				<table cellspacing="0" cellpadding="0" border="0" width="100%"><tbody><tr>
					<td style="vertical-align: middle; padding: 8px 0px;">
						<div style="margin-left: 30px; font-size: 15px;">
							<fmtSpring:message code="page.text.test.suite.of"/>&nbsp;<span style="font-weight: bold;
							font-size: 15px;"><msgpt:TxtFmt maxLength="50">${not empty tpCollection ? tpCollection.name : document.name}</msgpt:TxtFmt></span>
						</div>
					</td>
				</tr></tbody></table>
			</div>

			<msgpt:iFrameContainer>
				<div class="contentPanel">
					<form:errors path="*">
						<msgpt:Information errorMsgs="${messages}" type="error" />
					</form:errors>

					<!--  POPUP DATA -->
					<div id="actionSpecs" style="display: none;">
						<!-- ACTIONS POPUP DATA -->
						<div id="actionSpec_2"  submitId="2"> <!-- Rerun test suite -->
							<div id="actionTitle_2"><fmtSpring:message code="page.label.confirm.rerun.test.suite"/></div>
							<div id="actionInfo_2"><fmtSpring:message code="page.text.rerun.test.suite"/><br/><br/><fmtSpring:message code="page.text.rerun.test.suite.note"/></div>
							<div id="actionRerunOptions_2"></div>
						</div>
					</div>

					<!-- POPUP INTERFACE -->
					<msgpt:Popup id="actionPopup">
						<div id="actionPopupInfoFrame">
							<div id="actionPopupInfo">&nbsp;</div>
						</div>
						<div id="actionPopupRerunOptions" style="padding: 2px 8px 6px 8px;" align="center">
							<table class="innerContentTable" cellspacing="0" cellpadding="0" border="0">
								<tr>
									<td style="padding: 2px 0px; vertical-align: middle; color: #444; white-space: nowrap;"><fmtSpring:message code="page.label.rerun.test.suite"/>:</td>
									<td style="padding: 2px 0px; vertical-align: middle; color: #444;">
										<form:checkbox id="rerunTestSuiteCheckbox" path="rerunTestSuite" cssClass="checkbox rerunCheckbox" cssStyle="padding-left: 10px;" onclick="changeRerunChecks(this)" />
									</td>
								</tr>
								<tr>
									<td style="padding: 2px 0px; vertical-align: middle; color: #444; white-space: nowrap;">
										<c:choose>
											<c:when test="${not empty previousStagedBundleDate}"><fmtSpring:message code="page.label.rerun.all.tests.use.bundle.from" arguments="${previousStagedBundleDate}" argumentSeparator=";"/></c:when>
											<c:otherwise><fmtSpring:message code="page.label.rerun.all.test.scenarios"/></c:otherwise>
										</c:choose>:
									</td>
									<td style="padding: 2px 0px; vertical-align: middle; color: #444;">
										<form:checkbox id="rerunAllTestScenariosCheckbox" path="rerunAllTestScenarios" cssClass="checkbox rerunCheckbox" cssStyle="padding-left: 10px;" onclick="changeRerunChecks(this)" disabled="${empty previousStagedBundleDate}"/>
									</td>
								</tr>
								<tr>
									<td style="padding: 2px 0px; vertical-align: middle; color: #444; white-space: nowrap;">
										<c:choose>
											<c:when test="${not empty previousStagedBundleDate}"><fmtSpring:message code="page.label.run.new.test.scenarios.reuse.bundle.from" arguments="${previousStagedBundleDate}" argumentSeparator=";"/></c:when>
											<c:otherwise><fmtSpring:message code="page.label.run.new.test.scenarios"/></c:otherwise>
										</c:choose>
										:</td>
									<td style="padding: 2px 0px; vertical-align: middle; color: #444;">
										<form:checkbox id="runNewTestScenariosCheckbox" path="runNewTestScenarios" cssClass="checkbox rerunCheckbox" cssStyle="padding-left: 10px;" onclick="changeRerunChecks(this)" disabled="${empty previousStagedBundleDate}"/>
									</td>
								</tr>
							</table>
						</div>
						<div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
							<span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#" label="page.label.cancel" disabled="true" /></span>
							<span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel" /></span>
							<span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#" label="page.label.continue" disabled="true" /></span>
							<span id="continueBtnEnabled"><msgpt:Button URL="javascript:submitForm();" label="page.label.continue" primary="true" /></span>
						</div>
					</msgpt:Popup>

					<msgpt:DataTable labelPosition="top" staticData="true">
						<!-- Name -->
						<msgpt:TableItem label="page.label.name">
							<msgpt:InputFilter type="simpleName">
								<form:input cssClass="inputXXL" path="testSuite.name" />
							</msgpt:InputFilter>
						</msgpt:TableItem>
						<!-- Bundle Delivery -->
						<msgpt:TableItem label="page.label.bundle.delivery">
							<msgpt:BundleDeliverySelect id="deServerSelect" deServerGuidPath="command.DEServerGuid" filenameOverridePath="command.bundleNameOverride" eventTypeFilter="<%= EventType.TYPE_TEST %>" />
						</msgpt:TableItem>
						<!-- Channel -->
						<c:if test="${isOmniChannel}">
							<msgpt:TableItem label="page.label.channel">
								<form:select cssClass="inputL style_select" id="channelSelect" path="channelContextId" onchange="toggleChannelContextMessages()" items="${appliedChannels}" itemValue="id" itemLabel="name" />
							</msgpt:TableItem>
						</c:if>
						<!-- Composition Package -->
						<c:if test="${(not empty command.document && command.document.isPrintTouchpoint && not command.document.isNativeCompositionTouchpoint && not command.document.isTemplateControlled) ||
										(not empty command.touchpointCollection && !command.touchpointCollection.isMessagepointCompositionCollection && !command.touchpointCollection.isTemplateControlled)}">							<msgpt:TableItem label="page.label.composition.package" cssClass="compositionPackageContainer">
								<form:select cssClass="inputXXL style_select" id="compositionFilePackageSelect" path="compositionFileSet">
									<form:option value="0"><fmtSpring:message code="page.text.loading"/></form:option>
								</form:select>
								<input type="hidden" id="initCompositionFileSetValue" value="${command.compositionFileSet.id}" />
							</msgpt:TableItem>
						</c:if>
						<!-- Test Data -->
						<msgpt:TableItem label="page.label.test.data">
							<div style="display: inline-block; vertical-align: top;">
								<select id="dataResourceSelect"
										name="dataResources"
										class="complex-dropdown-select"
										title="${msgpt:getMessage('page.label.test.data')}"
										aria-label="${msgpt:getMessage('page.label.test.data')}"
										data-toggle="complex-dropdown"
										data-enablefilter="true"
										data-enable-tag-cloud="true"
										data-tag-cloud-type="20"
										data-enable-selectall="true"
										data-enable-view-selected="true"
										data-menu-class="dropdown-menu-left"
										data-dropdown-class="border-light dropdown-flexblock mr-2"
										data-show-titles="true"
										data-min-children-to-display-filter="1"
										multiple>
									<c:forEach var="currentDataResource" items="${availdataResources}">
										<fmtSpring:bind path="command.dataResources">
											<option id="touchpointOption_${currentDataResource.id}"
													class="ml-0" value="${currentDataResource.id}"
													data-filtervalue="${currentDataResource.metatags}"
													${msgpt:contains( command.dataResources, currentDataResource) ? 'selected="selected"': ''}>
													${currentDataResource.name}
											</option>
										</fmtSpring:bind>
									</c:forEach>
								</select>
							</div>
							<div align="left" style="display: inline-block; vertical-align: top;">
								<!-- BUTTON: ADD DATA RESOURCE -->
								<msgpt:IfAuthGranted authority="ROLE_TOUCHPOINT_DATA_EDIT" >
									<div id="addDataResourceBtn" class="addDataResourceBtn actionBtn_roundAll actionBtn detailTip fa-mp-container" style="white-space: nowrap; height: 38px;" onclick="javascript:dataResourceAddAction();" title="|<div class='detailTipText'>${msgpt:getMessage('page.text.add.data.resource')}</div>">
										<i class="fa fa-plus fa-mp-btn"></i>
									</div>
								</msgpt:IfAuthGranted>
							</div>
						</msgpt:TableItem>
						<!-- Test Date -->
						<msgpt:TableItem label="page.label.test.date">
							<msgpt:Calendar path="testSuite.requestDate" viewableDateFormat="${viewableDateFormat}" />
						</msgpt:TableItem>

						<!-- Scheduler Run Type -->
						<msgpt:TableItem label="page.label.touchpoint.deliveryevent.production.runtype" id="schedulerTypeCheckbox" >
							<c:forEach var="aType"	items="${deliveryEventTypes}">
								<form:radiobutton path="deliveryEventTypeId"  value="${aType.id}" label="${aType.displayName}" cssClass="radioBtn" onclick="schedulerChange();"/>
								<br>
							</c:forEach>
						</msgpt:TableItem>

						<c:if test="${command.deliveryEventTypeId == '3'}">
							<msgpt:TableItem label="page.label.start.date">
								<msgpt:Calendar id="startDate" onchange="javascript:setTimeDateBindingValue()" viewableDateFormat="${viewableDateFormat}" />
								<div>
									<select id="time_hours" class="selectTime" onchange="timeChange(this)"></select>&nbsp<b>:</b>
									<select id="time_minutes" class="selectTime" onchange="timeChange(this)"></select>
									<select id="time_AM_PM" class="selectTime" onchange="timeChange(this)"></select>
								</div>
								<form:hidden id="hiddenDateTimeField" path="startDate" />
							</msgpt:TableItem>
							<!-- Repeating Events -->
							<msgpt:TableItem label="page.label.repeatingevents" id="repeatingCheckbox">
								<form:checkbox path="repeating" cssClass="radioBtn" onclick="schedulerChange();" />
							</msgpt:TableItem>
						</c:if>
						<c:if test="${command.repeating && command.deliveryEventTypeId == '3'}">
							<!-- End Date -->
							<msgpt:TableItem label="page.label.end.date">
								<msgpt:Calendar id="endDate" path="endDate" viewableDateFormat="${viewableDateFormat}" />
							</msgpt:TableItem>
							<!-- Frequency -->
							<msgpt:TableItem label="page.label.frequency" id="frequencyTypeCheckbox">
								<c:forEach var="aFrequency" items="${frequencyTypes}">
									<form:radiobutton path="frequencyTypeId" value="${aFrequency.id}" label="${aFrequency.displayName}" cssClass="radioBtn" onclick="schedulerChange();"/>
									<br>
								</c:forEach>
							</msgpt:TableItem>
							<!-- Days of the Week -->
							<c:if test="${command.frequencyTypeId == 1}">
								<msgpt:TableItem label="page.label.daysofweek" id="dailyFrequencyCheckbox">
									<c:forEach var="aDailyFrequency" items="${dailyFrequencyTypes}">
										<form:radiobutton path="dailyFrequencyId" value="${aDailyFrequency.id}" label="${aDailyFrequency.displayName}" cssClass="radioBtn" onclick="schedulerChange();"/>
										<br>
									</c:forEach>
								</msgpt:TableItem>
							</c:if>
						</c:if>

						<!-- Blue Relay Target Folder -->
						<c:if test="${empty tpCollection && not empty command.document.blueRelayEndpoint}">
							<msgpt:TableItem label="page.label.blue.relay.blue.relay.target.folder">
								<msgpt:InputFilter type="filename">
									<form:input path="blueRelayTargetFolder" maxlength="255" cssClass="inputXXL"/>
								</msgpt:InputFilter>
							</msgpt:TableItem>
						</c:if>
						<!--  Debug Mode -->
						<msgpt:TableItem label="page.label.debug.mode">
							<form:checkbox id="flipToggle_debugMode" path="debugMode" title="${msgpt:getMessage('page.label.on')};${msgpt:getMessage('page.label.off')}" cssClass="checkbox" />
						</msgpt:TableItem>
						<c:if test="${enableSendToQaModule}">
							<!--  Automatically Send to QA Module -->
							<msgpt:TableItem label="page.label.upload.auto.send.to.qa.module">
								<form:checkbox id="flipToggle_autoQaSend" path="autoQAModuleSend" title="${msgpt:getMessage('page.label.on')};${msgpt:getMessage('page.label.off')}" cssClass="checkbox" />
							</msgpt:TableItem>
						</c:if>
						<!-- Use All In Process for Library Assets -->
						<msgpt:TableItem label="page.label.use.all.in.process.library.assets">
							<form:checkbox id="flipToggle_useAllInProcessLib" path="useAllInProcessLibraryAssets" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" cssClass="checkbox" />
						</msgpt:TableItem>
						<!-- Use All In Process -->
						<msgpt:TableItem label="page.label.use.all.in.process">
							<form:checkbox id="flipToggle_useAllInProcess" path="useAllInProcess" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" cssClass="checkbox" />
						</msgpt:TableItem>
						<!--  PDF Annoations -->
						<c:if test="${not empty command.document && ( command.document.isGMCTouchpoint() || command.document.isSefasCompositionTouchpoint() ||
						command.document.isMPHCSCompositionTouchpoint())}">
							<msgpt:TableItem id="pdfAnnotations">
								<table>
									<tbody>
									<tr>
										<td style="font-weight: bold; font-size: 12px; font-size: 12px; padding: 0px 0 4px !important; color: #626262;">
											<fmtSpring:message code="page.label.pdf.annotations"/>
										</td>
										<c:if test="${not empty command.document && command.document.isGMCTouchpoint()}">
											<td style="padding: 5px;">
												<i class="far fa-info-circle text-info mr-1 annotationInfo"
												   aria-hidden="true" title="${msgpt:getMessage('page.label.pdf.annotations.info')}" data-toggle="tooltip" data-placement="left"></i>
											</td>
										</c:if>
									</tr>
									</tbody>
								</table>
								<form:checkbox id="flipToggle_pdfAnnotations" path="pdfAnnotationsEnabled" title="${msgpt:getMessage('page.label.on')};${msgpt:getMessage('page.label.off')}" cssClass="checkbox" />
							</msgpt:TableItem>
						</c:if>


						<c:if test="${!command.useAllInProcess}">
							<msgpt:TableItem label="" cssClass="itemSelection" style="${command.useAllInProcess ? 'display:none' : ''}">
								<table summary="Page Form" cellspacing="0" cellpadding="0" border="0" class="contentTableLg" style="width: 680px;" >
									<tbody >
									<tr class="contentTableHeaderTRsecond">
										<td colspan="2" align="left" style="padding-left: 15px; border-right: none;">
											<fmtSpring:message code="page.label.in.process.items.to.include"/>
										</td>
										<td width="10%" style="border-left: 0px; white-space: nowrap;" align="right">
											<fmtSpring:message code="page.label.select.all"/>
											<input type="checkbox" id="selectAllWorkingCopiesCheckToggle" class="checkbox" onclick="toggleWorkingCopyChecks(true)" />
										</td>
									</tr>

									<c:choose>
										<c:when test="${not empty tpCollection}">
											<!-- User select Collection -->
											<!-- Messages - By Touchpoint -->
											<c:set var="hasMessages" value="false" />
											<c:set var="documentsList" value="${command.touchpointCollection.documents}" />
											<c:forEach var="currentDoc" items="${documentsList}">
												<c:set var="docMessages" value="${currentDoc.currentWorkgroupZoneMessageWithWorkingCopyList}" />
												<c:if test="${fn:length(docMessages) > 0}">
													<tr id="tableRow" class="alwaysVisible"><td style="padding-left: 50px; background-color: #ddd;" colspan="3">
														<c:out value="${currentDoc.name}" />
													</td></tr>
													<c:forEach var="message" items="${docMessages}" >
														<c:if test="${not hasMessages}"><c:set var="hasMessages" value="true" /></c:if>
														<c:choose>
															<c:when test="${message.hasWorkingData}">
																<c:set var="isMessageSelected" value="${ msgpt:contains(command.messages, message) }" />
																<tr id="tableRow" class="alwaysVisible" channel_context="${message.channelContextId}">
																	<td>
																		<c:if test="${not empty message.hasActiveData}">
																			<i id="activeIcon_${message.id}" class="activeIcon fa fa-check" style="font-size: 14px; cursor: default; ${ !(isMessageSelected && message.hasWorkingData) ? '' : 'display:none;' }">&nbsp;</i>
																		</c:if>
																		<i id="workingCopyIcon_${message.id}" class="workingCopyIcon fa fa-pencil" style="font-size: 14px; cursor: default; ${ isMessageSelected && message.hasWorkingData ? '' : 'display:none;'}">&nbsp;</i>
																	</td>
																	<td width="87%" style="padding-left: 30px;">
																		<fmtSpring:bind path='command.messages'>
																			<input style="position: relative; top: -1px;" name='messages' type='checkbox' value='${message.id}' class='tree-check-box wcCheckbox'
																				   <c:if test="${isMessageSelected}">checked='checked'</c:if>
																				   onclick='javascript:onClickFunction(this)' />
																			<input type='hidden' value='${isMessageSelected ? 1: 0 }' name='_messages'/>
																		</fmtSpring:bind>
																		<c:out value="${message.name}"/>
																	</td><td/>
																</tr>
															</c:when>
														</c:choose>
													</c:forEach>
												</c:if>
											</c:forEach>
											<c:if test="${not hasMessages}">
												<tr><td colspan="3" style="padding-left: 15px;"><fmtSpring:message code="page.text.no.messages.for.collection"/></td></tr>
											</c:if>
											<!-- End user select Collection -->
										</c:when>
										<c:otherwise>
											<!-- User select Touchpoint -->
											<c:choose>
												<c:when test="${empty command.document}">
													<tr><td colspan="3">${msgpt:getMessage("error.message.mustselecttouchpoint")}</td></tr>
												</c:when>
												<c:otherwise>
													<!-- Messages - By Zone -->
													<c:set var="hasMessages" value="false" />
													<c:set var="zoneMessagesMap" value="${command.document.currentWorkgroupZoneMessageWithWorkingCopyMap}" />
													<c:forEach var="currentZone" items="${command.document.currentWorkgroupZones}">
														<c:set var="zoneMessages" value="${zoneMessagesMap[currentZone]}" />
														<c:if test="${fn:length(zoneMessages) > 0}">
															<tr id="tableRow" class="alwaysVisible"><td style="padding-left: 50px; background-color: #ddd;" colspan="3">
																<c:out value="${currentZone.friendlyName}" />
															</td></tr>
															<c:forEach var="message" items="${zoneMessages}" >
																<c:if test="${not hasMessages}"><c:set var="hasMessages" value="true" /></c:if>
																<c:choose>
																	<c:when test="${message.hasWorkingData}">
																		<c:set var="isMessageSelected" value="${ msgpt:contains(command.messages, message) }" />
																		<tr id="tableRow" class="alwaysVisible" channel_context="${message.channelContextId}">
																			<td>
																				<c:if test="${message.hasActiveData}">
																					<i id="activeIcon_${message.id}" class="activeIcon fa fa-check" style="font-size: 14px; cursor: default; ${ !(isMessageSelected && message.hasWorkingData) ? '' : 'display:none;' }">&nbsp;</i>
																				</c:if>
																				<i id="workingCopyIcon_${message.id}" class="workingCopyIcon fa fa-pencil" style="font-size: 14px; cursor: default; ${ isMessageSelected && message.hasWorkingData ? '' : 'display:none;'}">&nbsp;</i>
																			</td>
																			<td width="87%" style="padding-left: 30px;">
																				<fmtSpring:bind path='command.messages'>
																					<input style="position: relative; top: -1px;" name='messages' type='checkbox' value='${message.id}' class='tree-check-box wcCheckbox'
																						   <c:if test="${isMessageSelected}">checked='checked'</c:if>
																						   onclick='javascript:onClickFunction(this)' />
																					<input type='hidden' value='${isMessageSelected ? 1: 0 }' name='_messages'/>
																				</fmtSpring:bind>
																				<c:out value="${message.name}"/>
																			</td><td/>
																		</tr>
																	</c:when>
																</c:choose>
															</c:forEach>
														</c:if>
													</c:forEach>
													<c:if test="${not hasMessages}">
														<tr><td colspan="3" style="padding-left: 15px;"><fmtSpring:message code="page.text.no.messages.for.touchpoint"/></td></tr>
													</c:if>
												</c:otherwise>
											</c:choose>
											<!-- End user select Touchpoint -->
										</c:otherwise>
									</c:choose>

									</tbody>
								</table>
							</msgpt:TableItem>
						</c:if>

					</msgpt:DataTable>

					<input type="hidden" name="submittype" />
				</div>

				<msgpt:FlowLayout align="center">
					<msgpt:FlowLayoutItem>
						<msgpt:Button URL="javascript:closeIframe()" label="page.label.cancel" flowControl="true"/>
					</msgpt:FlowLayoutItem>

					<msgpt:FlowLayoutItem>
						<msgpt:Button URL="javascript:doSubmit('save');" label="page.label.save" flowControl="true" primary="${command.deliveryEventTypeId == 3 ? 'true' : 'false'}"/>
					</msgpt:FlowLayoutItem>


					<c:if test="${command.deliveryEventTypeId == 1}">
						<c:choose>
							<c:when test="${!isNew}">
								<msgpt:FlowLayoutItem>
									<msgpt:Button URL="javascript:actionSelected(2);" label="page.label.run" primary="true" />
								</msgpt:FlowLayoutItem>
							</c:when>
							<c:otherwise>
								<msgpt:FlowLayoutItem>
									<msgpt:Button URL="javascript:doSubmit('process');" label="page.label.run" flowControl="true" primary="true" />
								</msgpt:FlowLayoutItem>
							</c:otherwise>
						</c:choose>
					</c:if>

				</msgpt:FlowLayout>
			</msgpt:iFrameContainer>
		</c:if>
	</form:form>
</msgpt:BodyNew>
</msgpt:Html5>