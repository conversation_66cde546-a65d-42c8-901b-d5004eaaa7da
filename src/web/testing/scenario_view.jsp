<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.model.testing.TestScenario"%>
<%@page import="com.prinova.messagepoint.MessagepointLicenceManager"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>
<%
	boolean licencedForInsertManagement = MessagepointLicenceManager.getInstance().isLicencedForInsertManagement();
	pageContext.setAttribute("licencedForInsertManagement", licencedForInsertManagement);
%>
<msgpt:Html5>
<msgpt:HeaderNew title="page.label.testing" extendedScripts="true" viewType="<%= MessagepointHeader.ViewType.VIEW %>">

	<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />
	
	
	<msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js" />
	<msgpt:Script src="includes/javascript/dynamic_tables.js" />
	<msgpt:Script src="includes/javascript/popupActions.js" />
	
	<style>
		.dataTable a {
			color: #2c40bb;
		}
	</style>

    <msgpt:Script>
        <script>

            // Init Javascript
            $( function() {
                // Style action bar elements
                $("input:button").styleActionElement();
                $("#actionMenu").styleActionElement();

                // Test Scenario status polling initialization
                $("[id*='inProcess']").actionStatusPolling({
                    type			: 'testscenario',
                    responseAction	: 'pageReload'
                });

                $('.compositionPackage').each( function() {

                    var configFileName = $(this).attr('configurationFileName');
                    var templateFileName = $(this).attr('templateFileName');
                    $(this).popupFactory({
                        title				: $(this).attr('packageName'),
                        popupLocation		: "right",
                        trigger				: "hover",
                        fnSetContent		: function(o) {
                            return 	"<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" +
                                "<div style=\"word-wrap: break-word;\">" + client_messages.text.package_file + ":" + configFileName + "</div>" +
                                "<div style=\"word-wrap: break-word;\">" + client_messages.text.template_file + ":" + templateFileName + "</div>" +
                                "</div>";
                        }
                    });

                });
            });
        </script>
    </msgpt:Script>
</msgpt:HeaderNew>
<c:set var="command" value="${command.testScenario}" />
<msgpt:GetHibernateObject hibernateObjectName="com.prinova.messagepoint.model.deliveryevent.Job" id="${command.deliveryEvent.job.id}" outputName="job"></msgpt:GetHibernateObject>
<msgpt:BodyNew theme="minimal" type="iframe">
	
	<form:form method="post" modelAttribute="command">
		<c:if test="${empty param.nprSaveSuccess}">
		
			<div id="popupHeaderTitle" style="display: none;">
				<span class="titleText">
					<c:out value='${msgpt:getMessage("page.label.view")} ${msgpt:getMessage("page.label.test")}: ${command.name}' />
				</span>	
			</div>
					
			<div class="contentTableIframeExtended backgroundTile_10p">
				<div class="contentPanel" style="padding: 10px 35px; min-height: 635px;">
				
					<form:errors path="*">
						<msgpt:Information errorMsgs="${messages}" type="error" />
					</form:errors>
									
					<form:hidden path="actionValue" id="actionElement"/>
					
					<c:if test="${command.inProcess}">
						<input type="hidden" id="inProcess_${param.testid}"/>
					</c:if>
														
					<!--  POPUP DATA -->
					<div id="actionSpecs" style="display: none;">
						<!-- ACTIONS POPUP DATA -->
						<div id="actionSpec_2" submitId="2"> <!-- Delete test scenario -->
							<div id="actionTitle_2"><fmtSpring:message code="page.label.confirm.delete.test"/></div>
							<div id="actionInfo_2"><fmtSpring:message code="page.text.delete.test"/></div>
						</div>
					</div>
					
					<!-- POPUP INTERFACE -->
					<msgpt:Popup id="actionPopup">
						<div id="actionPopupInfoFrame">
							<div id="actionPopupInfo">&nbsp;</div>
						</div>
						<div id="actionPopupApprovalButtons" class="actionPopupButtonsContainer">
							<msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel" />
							<span id="rejectBtn"><msgpt:Button URL="#" label="page.flow.reject" /></span>
							<span id="rejectBtnDisabled" style="display: none;"><msgpt:Button URL="#" label="page.flow.reject" disabled="true" /></span>
							<span id="approveBtn"><msgpt:Button URL="#" label="page.flow.approve" /></span>
						</div>
						<div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
							<span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#" label="page.label.cancel" disabled="true" /></span>
							<span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel" /></span>
							<span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#" label="page.label.continue" disabled="true" /></span>
							<span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue" primary="true" /></span>
						</div>
					</msgpt:Popup>
										
					<msgpt:DataTable labelPosition="top" multiColumn="true">
						<!-- Name -->
						<msgpt:TableItem label="page.label.name"> 
							<c:out value="${command.name}"/>
						</msgpt:TableItem>
						<!-- Touchpoint -->
						<c:if test="${not empty command.document}">
							<msgpt:TableItem label="page.label.touchpoint"> 
								<c:out value="${command.document.name}"/>
							</msgpt:TableItem>						
						</c:if>
						<c:if test="${not empty command.document && command.document.isPrintTouchpoint && !command.document.isTemplateControlled}">
							<!-- Composition Package -->
							<msgpt:TableItem label="page.label.composition.package">
								<span class="compositionPackage" 
										packageName="${command.compositionFileSet.name}" 
										templateFileName="${command.compositionFileSet.templateFileName}" 
										configurationFileName="${command.compositionFileSet.compositionConfigurationFileName}" > 
									<c:out value="${command.compositionFileSet.name}"/>
								</span>
							</msgpt:TableItem>
						</c:if>
						<!-- Data Resource -->
						<msgpt:TableItem label="page.label.test.data">
							<c:out value="${command.dataResource.name}"/>
						</msgpt:TableItem>
						<!-- Test Run Date -->
						<msgpt:TableItem label="page.label.test.run.date"> 
							<fmtJSTL:formatDate value="${command.startDate}" pattern="${dateFormat}" />
						</msgpt:TableItem>
						<!-- Active Run Type -->
						<c:if test="${command.deliveryEventType.displayName != null}">
							<msgpt:TableItem label="page.label.touchpoint.deliveryevent.production.runtype.view">
								<c:out value="${command.deliveryEventType.displayName}"/>
							</msgpt:TableItem>
						</c:if>
						<!-- Test Schedule Date -->
						<c:if test="${not empty command.deliveryEventSchedule}">
							<msgpt:TableItem label="page.label.test.scheduled.date">
								<c:out value="${command.deliveryEventSchedule.displayText}"/> &nbsp;
								<c:out value="${command.getNextScheduledDateStr('true')}"/>.
							</msgpt:TableItem>
						</c:if>
						<!-- Debug Mode -->
						<msgpt:TableItem label="page.label.debug.mode">
							<c:choose>
								<c:when test="${command.debugMode}"><fmtSpring:message code="page.label.yes" /></c:when>
								<c:otherwise><fmtSpring:message code="page.label.no" /></c:otherwise>
							</c:choose>
						</msgpt:TableItem>
						<!-- Automatically Send to QA Module -->
						<c:if test="${enableSendToQaModule}">
							<msgpt:TableItem label="page.label.upload.auto.send.to.qa.module">
								<c:choose>
									<c:when test="${command.autoQAModuleSend}"><fmtSpring:message code="page.label.yes" /></c:when>
									<c:otherwise><fmtSpring:message code="page.label.no" /></c:otherwise>
								</c:choose>
							</msgpt:TableItem>
						</c:if>
						<!--  PDF Annoations -->
						<c:if test="${not empty command.document && command.document.isGMCTouchpoint() && !isChannelEmail || (not empty command.document && (command.document.isSefasCompositionTouchpoint() || command.document.isMPHCSCompositionTouchpoint()))}">
							<msgpt:TableItem label="page.label.pdf.annotations">
								<c:choose>
									<c:when test="${command.pdfAnnotationsEnabled}"><fmtSpring:message code="page.label.yes" /></c:when>
									<c:otherwise><fmtSpring:message code="page.label.no" /></c:otherwise>
								</c:choose>
							</msgpt:TableItem>
						</c:if>
						<!-- Use All In Process for Library Assets -->
						<msgpt:TableItem label="page.label.use.all.in.process.library.assets">
							<c:choose>
								<c:when test="${command.useAllInProcessLibraryAssets}"><fmtSpring:message code="page.label.yes" /></c:when>
								<c:otherwise><fmtSpring:message code="page.label.no" /></c:otherwise>
							</c:choose>
						</msgpt:TableItem>
						<!-- Use All In Process -->
						<msgpt:TableItem label="page.label.use.all.in.process">
							<c:choose>
								<c:when test="${command.useAllInProcess}"><fmtSpring:message code="page.label.yes" /></c:when>
								<c:otherwise><fmtSpring:message code="page.label.no" /></c:otherwise>
							</c:choose>
						</msgpt:TableItem>	
						<!-- Job Number -->
						<c:if test="${command.deliveryEvent.job != null}">
							<msgpt:TableItem label="page.lable.job.number"> 
								<c:out value="${command.deliveryEvent.job.id}"/>
							</msgpt:TableItem>				
						</c:if>
						<!-- File Package -->
						<c:if test="${command.isFilePackage() && command.compositionFileSet != null}">
							<msgpt:TableItem label="page.label.file.package">
								<c:out value="${command.compositionFileSet.name}"/>
							</msgpt:TableItem>
						</c:if>
						<!-- Blue Relay Target Folder -->
						<c:if test="${not empty command.document && not empty command.document.blueRelayEndpoint}">
							<msgpt:TableItem label="page.label.blue.relay.blue.relay.target.folder">
								<c:out value="${command.blueRelayTargetFolder}"/>
							</msgpt:TableItem>
						</c:if>
						<msgpt:IfAuthGranted authority="ROLE_TEST_EDIT">
							<msgpt:TableItem>
								<c:if test="${hasStateChanged}">
									<div style="margin: 10px 5px; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
										<fmtSpring:message code="page.text.scenarios.changed"/>
									</div>
								</c:if>
							</msgpt:TableItem>												
						</msgpt:IfAuthGranted>
					</msgpt:DataTable>
	
					<c:if test="${!command.useAllInProcess}">
			            <table summary="Page Form" cellspacing="0" cellpadding="0" border="0" class="contentTableLg" style="width: 715px; margin-bottom: 25px;">
			                <tbody >
								<tr class="contentTableHeaderTRsecond">
									<td align="left" style="padding-left: 15px; border-right: none;">
										${msgpt:getMessage("page.label.in.process.items.included")}
									</td>
									<td style="padding-right: 10px; border-left: 0px; white-space: nowrap;" align="right" >
										<span class="tableExpandAll" onclick="javascript:expandContractTree('showAll')" title="${msgpt:getMessage('page.label.expand.all')}"
										 onmousedown="this.style.backgroundPosition='left bottom'" onmouseup="this.style.backgroundPosition='left top'"
										 onmouseover="this.style.backgroundPosition='left top'" onmouseout="this.style.backgroundPosition='left center'">&nbsp;</span>
										<span class="tableContractAll" onclick="javascript:expandContractTree('hideAll')" title="${msgpt:getMessage('page.label.contract.all')}"
										 onmousedown="this.style.backgroundPosition='left bottom'" onmouseup="this.style.backgroundPosition='left top'"
										 onmouseover="this.style.backgroundPosition='left top'" onmouseout="this.style.backgroundPosition='left center'">&nbsp;</span>
									</td>
								</tr>
	                				<!-- Messages - By Zone -->
									<c:set var="hasMessages" value="false" />
									<c:forEach var="currentZone" items="${command.selectedZones}">
											<tr id="tableRow"><td style="padding-left: 15px; background-color: #eee;" colspan="2">
												<c:out value="${currentZone.friendlyName}" />
											</td></tr>
			                				<c:forEach var="message" items="${command.selectedZoneMessageMap[currentZone]}" >
												<c:if test="${not hasMessages}"><c:set var="hasMessages" value="true" /></c:if>
												<tr id="tableRow"><td style="padding-left: 30px;" colspan="2">
													<i class="workingCopyIcon fa fa-pencil" style="font-size: 14px; cursor: default;">&nbsp;</i>
				                					<c:out value="${message.name}"/>
				                				</td></tr>
			                				</c:forEach>														
									</c:forEach>
									<c:if test="${not hasMessages}">
										<tr><td style="padding-left: 15px;"><fmtSpring:message code="page.text.no.working.copies.selected.for.test"/></td></tr>
									</c:if>
							</tbody>
						</table>
					</c:if>

					<c:if test="${ command.id != 0 }">
						<msgpt:DataTable columnReorder="false">
							<msgpt:TableListGroup>
								<msgpt:TableElement label="page.label.name">
									<c:choose>
										<c:when test="${command.error || command.deliveryEvent.isCompletedWithError || not empty command.outputLink || command.logsAvailable }">
											<a href="javascript:popItUpWithDimensions('../includes/delivery_event_logs.jsp?id=${command.id}&deliveryid=${command.deliveryEvent.id}&class=${command.class.name}', 720, 480);">
												<fmtSpring:message code="page.label.logs" />
											</a>
										</c:when>
										<c:otherwise>
											<span style="font-style: italic">${msgpt:getMessage("page.label.prepare.output") }</span>
				  						     	</c:otherwise>
									</c:choose>
								</msgpt:TableElement>
								<msgpt:TableElement label="page.label.status">
						        	<c:choose>
						        		<c:when test="${not empty command.outputLink || command.logsAvailable}">
									        <fmtSpring:message code="page.label.state.completed" />		
						        		</c:when>
						        		<c:when test="${(command.error || command.deliveryEvent.isCompletedWithError) && empty command.outputLink}">
											<c:out value="${command.deliveryEvent.statusDisplayString}" />
						        		</c:when>
						        		<c:otherwise>
									        <fmtSpring:message code="page.label.state.inprocess" />		
						        		</c:otherwise>
						        	</c:choose>
								</msgpt:TableElement>
								<msgpt:TableElement label="page.label.date">
									<fmtJSTL:formatDate value="${command.lastRunDate}" pattern="${dateTimeFormat}"/>
								</msgpt:TableElement>
								<msgpt:TableElement label="page.label.user">
									<c:out value="${command.updatedByName}" />
								</msgpt:TableElement>
							</msgpt:TableListGroup>
							
							<c:if test="${not ( empty command.outputLink || isDigitalTest) }">
								<msgpt:TableListGroup>
									<msgpt:TableElement>
										<c:choose>
											<c:when test="${ not empty command.outputLink }">
												<a href="javascript:javascriptHref('${contextPath}/download/pdf.form?resource=${msgpt:getResourceToken().add("file", command.outputLink).value}')">
													<fmtSpring:message code="page.label.output"/>
												</a>
											</c:when>
									        <c:otherwise>
				      						        <span style="font-style: italic">${msgpt:getMessage("page.label.prepare.output") }</span>
									        </c:otherwise>
										</c:choose>
									</msgpt:TableElement>
									<msgpt:TableElement>
							        	<c:choose>
							        		<c:when test="${command.error|| command.deliveryEvent.isCompletedWithError}">
												<c:out value="${command.deliveryEvent.statusDisplayString}"/>
							        		</c:when>
							        		<c:when test="${ not empty command.outputLink}">
												<c:out value="${command.deliveryEvent.statusDisplayString}"/>
							        		</c:when>
							        		<c:otherwise>
										        <fmtSpring:message code="page.label.state.inprocess" />		
							        		</c:otherwise>
							        	</c:choose>
									</msgpt:TableElement>
									<msgpt:TableElement>
										<fmtJSTL:formatDate value="${command.lastRunDate}" pattern="${dateTimeFormat}"/>
									</msgpt:TableElement>
									<msgpt:TableElement>
										<c:out value="${command.updatedByName}" />
									</msgpt:TableElement>
								</msgpt:TableListGroup>
							</c:if>
							
							<c:if test="${not empty command.dialogueOutputLink}">
								<c:if test="${command.document.isDialogueTouchpoint}">
									<msgpt:TableListGroup>
										<msgpt:TableElement>
											<c:choose>
												<c:when test="${ not empty command.dialogueOutputLink }">
													<a href="${contextPath}/download/data.form?resource=${msgpt:getResourceToken().add('file', command.dialogueOutputLink).value}"><fmtSpring:message code="page.label.dialogue.output"/></a>
												</c:when>
										        <c:otherwise>
				       						        <span style="font-style: italic">${msgpt:getMessage("page.label.prepare.output") }</span>
										        </c:otherwise>
											</c:choose>
										</msgpt:TableElement>
										<msgpt:TableElement>
								        	<c:choose>
								        		<c:when test="${command.error|| command.deliveryEvent.isCompletedWithError}">
													<c:out value="${command.deliveryEvent.statusDisplayString}"/>
								        		</c:when>
								        		<c:when test="${ not empty command.outputLink}">
													<c:out value="${command.deliveryEvent.statusDisplayString}"/>
								        		</c:when>
								        		<c:otherwise>
											        <fmtSpring:message code="page.label.state.inprocess" />		
								        		</c:otherwise>
								        	</c:choose>
										</msgpt:TableElement>
										<msgpt:TableElement>
											<fmtJSTL:formatDate value="${command.lastRunDate}" pattern="${dateTimeFormat}"/>
										</msgpt:TableElement>
										<msgpt:TableElement>
											<c:out value="${command.updatedByName}" />
										</msgpt:TableElement>
									</msgpt:TableListGroup>
								</c:if>
							</c:if>	
							<msgpt:TableListGroup>
								<msgpt:TableElement>
									 <c:choose>
										<c:when test="${command.deliveryEvent.isCompletedStateExist && not empty command.deliveryEvent.logFilePath}">
											<a href="${contextPath}/download/data.form?resource=${msgpt:getResourceToken().add('file', command.deliveryEvent.logFilePath).value}">
												<fmtSpring:message code="page.label.full.job.logs" />
											</a>
										</c:when>
										<c:otherwise>
											<span style="font-style: italic">${msgpt:getMessage("page.label.prepare.report") }</span>
										</c:otherwise>
									</c:choose>
								</msgpt:TableElement>
								<msgpt:TableElement>
						        	<c:choose>
						        		<c:when test="${command.deliveryEvent.isCompletedStateExist}">
											<c:out value="${command.deliveryEvent.statusDisplayString}"/>
						        		</c:when>
						        		<c:otherwise>
								        <fmtSpring:message code="page.label.state.inprocess" />		
						        		</c:otherwise>
						        	</c:choose>
						      </msgpt:TableElement>
								<msgpt:TableElement>					
									<fmtJSTL:formatDate value="${command.lastRunDate}" pattern="${dateTimeFormat}"/>
								</msgpt:TableElement>
								<msgpt:TableElement>
									<c:out value="${command.updatedByName}" />
								</msgpt:TableElement>
							</msgpt:TableListGroup>




							<c:choose>
								<c:when test="${isJobStatisticsEnabled && hasJobStatisticReports}">
									<msgpt:TableListGroup>
										<msgpt:TableElement>
											<c:choose>
												<c:when test="${job.error }" >
													<fmtSpring:message code="page.label.messages" />
												</c:when>
												<c:when test="${ job.complete }">
													<a href="javascript:popItUpWithDimensions('scenario_message_report_view.form?scenarioid=${param.testid}&jobid=${job.id}', 960, 480);">
														<fmtSpring:message code="page.label.messages" />
													</a>
												</c:when>
												<c:otherwise>
													<span style="font-style: italic">${msgpt:getMessage("page.label.prepare.report") }</span>
												</c:otherwise>
											</c:choose>
										</msgpt:TableElement>
										<msgpt:TableElement>
											<c:choose>
												<c:when test="${ job.error || job.complete}">
													<c:out value="${command.deliveryEvent.statusDisplayString}"/>
												</c:when>
												<c:otherwise>
													<fmtSpring:message code="page.label.state.inprocess" />
												</c:otherwise>
											</c:choose>
										</msgpt:TableElement>
										<msgpt:TableElement>
											<fmtJSTL:formatDate value="${command.lastRunDate}" pattern="${dateTimeFormat}"/>
										</msgpt:TableElement>
										<msgpt:TableElement>
											<c:out value="${command.updatedByName}" />
										</msgpt:TableElement>
									</msgpt:TableListGroup>

									<msgpt:TableListGroup>
										<msgpt:TableElement>
											<c:choose>
												<c:when test="${ job.error }">
													<fmtSpring:message code="page.label.customers" />
												</c:when>
												<c:when test="${ job.complete }">
													<a href="javascript:popItUpWithDimensions('scenario_customer_report_view.form?scenarioid=${param.testid}&jobid=${job.id}', 960, 480);">
														<fmtSpring:message code="page.label.customers" />
													</a>
												</c:when>
												<c:otherwise>
													<span style="font-style: italic">${msgpt:getMessage("page.label.prepare.report") }</span>
												</c:otherwise>
											</c:choose>
										</msgpt:TableElement>
										<msgpt:TableElement>
											<c:choose>
												<c:when test="${ job.error || job.complete}">
													<c:out value="${command.deliveryEvent.statusDisplayString}"/>
												</c:when>
												<c:otherwise>
													<fmtSpring:message code="page.label.state.inprocess" />
												</c:otherwise>
											</c:choose>
										</msgpt:TableElement>
										<msgpt:TableElement>
											<fmtJSTL:formatDate value="${command.lastRunDate}" pattern="${dateTimeFormat}"/>
										</msgpt:TableElement>
										<msgpt:TableElement>
											<c:out value="${command.updatedByName}" />
										</msgpt:TableElement>
									</msgpt:TableListGroup>

									<c:if test="${licencedForInsertManagement}">
										<msgpt:TableListGroup>
											<msgpt:TableElement>
												<c:choose>
													<c:when test="${ job.error }">
														<fmtSpring:message code="page.label.inserts" />
													</c:when>
													<c:when test="${ job.complete }">
														<a href="javascript:popItUpWithDimensions('scenario_insert_report_view.form?scenarioid=${param.testid}&jobid=${job.id}', 960, 480);">
															<fmtSpring:message code="page.label.inserts" />
														</a>
													</c:when>
													<c:otherwise>
														<span style="font-style: italic">${msgpt:getMessage("page.label.prepare.report") }</span>
													</c:otherwise>
												</c:choose>
											</msgpt:TableElement>
											<msgpt:TableElement>
												<c:choose>
													<c:when test="${ job.error || job.complete}">
														<c:out value="${command.deliveryEvent.statusDisplayString}"/>
													</c:when>
													<c:otherwise>
														<fmtSpring:message code="page.label.state.inprocess" />
													</c:otherwise>
												</c:choose>
											</msgpt:TableElement>
											<msgpt:TableElement>
												<fmtJSTL:formatDate value="${command.lastRunDate}" pattern="${dateTimeFormat}"/>
											</msgpt:TableElement>
											<msgpt:TableElement>
												<c:out value="${command.updatedByName}" />
											</msgpt:TableElement>
										</msgpt:TableListGroup>

										<msgpt:TableListGroup>
											<msgpt:TableElement>
												<c:choose>
													<c:when test="${ job.error }">
														<fmtSpring:message code="page.label.insert.recipients" />
													</c:when>
													<c:when test="${ job.complete }">
														<a href="javascript:popItUpWithDimensions('scenario_customer_insert_report_view.form?scenarioid=${param.testid}&jobid=${job.id}', 960, 480);">
															<fmtSpring:message code="page.label.insert.recipients" />
														</a>
													</c:when>
													<c:otherwise>
														<span style="font-style: italic">${msgpt:getMessage("page.label.prepare.report") }</span>
													</c:otherwise>
												</c:choose>
											</msgpt:TableElement>
											<msgpt:TableElement>
												<c:choose>
													<c:when test="${ job.error || job.complete}">
														<c:out value="${command.deliveryEvent.statusDisplayString}"/>
													</c:when>
													<c:otherwise>
														<fmtSpring:message code="page.label.state.inprocess" />
													</c:otherwise>
												</c:choose>
											</msgpt:TableElement>
											<msgpt:TableElement>
												<fmtJSTL:formatDate value="${command.lastRunDate}" pattern="${dateTimeFormat}"/>
											</msgpt:TableElement>
											<msgpt:TableElement>
												<c:out value="${command.updatedByName}" />
											</msgpt:TableElement>
										</msgpt:TableListGroup>
									</c:if>
								</c:when>
								<c:when test="${isJobStatisticsEnabled && !hasJobStatisticReports}">
									<msgpt:TableListGroup>
										<msgpt:TableElement>
											<span style="font-style: italic">${msgpt:getMessage("page.text.report.statistics.do.not.exists") }</span>
										</msgpt:TableElement>
										<msgpt:TableElement><c:out value="" /></msgpt:TableElement>
										<msgpt:TableElement><c:out value="" /></msgpt:TableElement>
										<msgpt:TableElement><c:out value="" /></msgpt:TableElement>
									</msgpt:TableListGroup>
								</c:when>
							</c:choose>
						</msgpt:DataTable>
					</c:if>
				</div>
			</div>
			
			<msgpt:IfAuthGranted authority="ROLE_TEST_EDIT">
				<div class="label">
					<table width="100%"><tbody><tr><td align="center" style="padding: 6px 12px;">
						<!-- Buttons -->
						<c:choose>
							<c:when test="${param.viewFromSuite == 'true' }">
								<msgpt:Button URL="javascript:closeIframe()" label="page.label.ok" flowControl="true"/>
							</c:when>
							<c:otherwise>
								<msgpt:Button URL="javascript:closeIframe()" label="page.label.cancel" flowControl="true"/>
								<msgpt:Button URL="javascript:submitAction(1, this);" label="page.label.update.test.scenario" primary="true" />
								<msgpt:Button URL="javascript:actionSelected(2);" label="page.label.delete.test.scenario" />
								<msgpt:Button URL="javascript:submitAction(3);" label="page.label.execute.test" disabled="${hasStateChanged ? 'true' : ''}"/>
							</c:otherwise>
						</c:choose>
					</td></tr></tbody></table>
				</div>														
			</msgpt:IfAuthGranted>
		</c:if>
	</form:form>
</msgpt:BodyNew>
</msgpt:Html5>