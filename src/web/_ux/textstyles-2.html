<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="Messagepoint Inc.">
    <title>Messagepoint</title>
    <!-- Favicon -->
    <!-- Styles -->
    <link rel="stylesheet" href="scss/themes/messagepoint/styles.css">
    <style>
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
    <script src="../includes/javascript/mp.common.js"></script>
</head>
<body class="override-default-sizing">
<a href="#content" class="sr-only sr-only-focusable h6 mx-1">Skip to main content</a>
<div id="page">
    <header class="box-shadow">
        <nav class="navbar navbar-expand navbar-dark" aria-label="Main navigation">
            <div class="nav-item">
                <a class="navbar-brand nav-link" href="#" data-toggle="tooltip" title="←&nbsp; Back to home page">
                    <img src="img/brands/messagepoint-logo-dark-theme.svg" class="d-inline-block align-middle"
                         alt="Header Logo">
                </a>
            </div>
            <div class="nav-container navbar-collapse flex-row">
                <ul id="main-navigation" class="nav-main navbar-nav" aria-label="Primary navigation">
                    <li class="nav-item dropdown active">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown01" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Touchpoint
                            <span class="sr-only">(current)</span>
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown01">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Touchpoints</a>
                            <a class="dropdown-item" href="#">Touchpoint Collections</a>
                            <a class="dropdown-item" href="#">Output Tags</a>
                            <a class="dropdown-item" href="#">Delivery Events</a>
                            <div class="dropdown-divider" role="separator"></div>
                            <h6 class="dropdown-header">New</h6>
                            <a class="dropdown-item" href="#">Touchpoint...</a>
                            <a class="dropdown-item" href="#">Import...</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown02" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Task
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown02">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Tasks</a>
                            <a class="dropdown-item" href="#">Projects</a>
                            <a class="dropdown-item" href="#">System Tasks</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown03" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Target
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown03">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Target Groups</a>
                            <a class="dropdown-item" href="#">Target Rules</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown04" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Content
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown04">
                            <h6 class="dropdown-header">Styles</h6>
                            <a class="dropdown-item" href="#">Texts</a>
                            <a class="dropdown-item" href="#">Paragraph</a>
                            <a class="dropdown-item" href="#">List</a>
                            <div class="dropdown-divider" role="separator"></div>
                            <h6 class="dropdown-header">Content</h6>
                            <a class="dropdown-item" href="#">Smart Text</a>
                            <a class="dropdown-item" href="#">Smart Canvas</a>
                            <a class="dropdown-item" href="#">Image Library</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown05" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Test
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown05">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Reports</a>
                            <a class="dropdown-item" href="#">Simulations</a>
                            <a class="dropdown-item" href="#">Connected</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a id="dropdown07a" class="nav-link dropdown-toggle" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Insert
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown07a">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Inserts</a>
                            <a class="dropdown-item" href="#">Insert Schedules</a>
                            <a class="dropdown-item" href="#">Insert Schedules Setup</a>
                            <a class="dropdown-item" href="#">Rate Sheets</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a id="dropdown07b" class="nav-link dropdown-toggle" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Report
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown07b">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Reports</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a id="dropdown07c" class="nav-link dropdown-toggle" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Data
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown07c">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Data Sources</a>
                            <a class="dropdown-item" href="#">Data Groups</a>
                            <a class="dropdown-item" href="#">Data Collections</a>
                            <a class="dropdown-item" href="#">Data Files</a>
                            <a class="dropdown-item" href="#">Data Resources</a>
                            <a class="dropdown-item" href="#">Variables</a>
                            <a class="dropdown-item" href="#">Selector Groups</a>
                            <a class="dropdown-item" href="#">External Event</a>
                            <a class="dropdown-item" href="#">Lookup Tables</a>
                            <a class="dropdown-item" href="#">Metadata Templates</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a id="dropdown07d" class="nav-link dropdown-toggle" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Setup
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown07d">
                            <div class="dropdown-content">
                                <h6 class="dropdown-header">View</h6>
                                <a class="dropdown-item" href="#">Workflow Library</a>
                                <a class="dropdown-item" href="#">Master Admin: System
                                    Settings
                                </a>
                                <a class="dropdown-item" href="#">Master Admin: Domains</a>
                                <a class="dropdown-item" href="#">Master Admin:
                                    Maintenance
                                </a>
                                <a class="dropdown-item" href="#">Master Admin: Licenses</a>
                                <a class="dropdown-item" href="#">Bundle Delivery</a>
                                <a class="dropdown-item" href="#">System Settings</a>
                                <a class="dropdown-item" href="#">Security Settings</a>
                                <a class="dropdown-item" href="#">Locale Settings</a>
                                <a class="dropdown-item" href="#">License Information</a>
                                <a class="dropdown-item" href="#">Feature activation</a>
                                <a class="dropdown-item" href="#">User Interface Settings
                                </a>
                                <a class="dropdown-item" href="#">Roles</a>
                                <a class="dropdown-item" href="#">Workgroups</a>
                                <a class="dropdown-item" href="#">Users</a>
                                <a class="dropdown-item" href="#">Auditing</a>
                                <div class="dropdown-divider" role="separator"></div>
                                <h6 class="dropdown-header">New</h6>
                                <a class="dropdown-item" href="#">Workgroup</a>
                            </div>
                        </div>
                    </li>
                </ul>
                <div class="navbar-nav nav-right">
                    <div class="nav-item dropdown" data-toggle="tooltip" data-placement="left"
                         title="Notification: No background tasks">
                        <a id="backgroundTasksPlaceholder" class="nav-link nav-icontool" href="#"
                           data-toggle="dropdown"
                           role="button" aria-haspopup="true"
                           aria-expanded="false" aria-label="Background task manager">
                            <span class="badge badge-danger">12</span>
                            <i class="far fa-bell fa-lg" aria-hidden="true"></i>
                        </a>
                        <div class="dropdown-menu dropdown-custom w-small"
                             aria-labelledby="backgroundTasksPlaceholder">
                            <h5 class="dropdown-header dropdown-heading">BACKGROUND TASK MANAGER</h5>
                            <div class="dropdown-divider mb-0" role="separator"></div>
                            <div class="dropdown-content tasks-list">
                                <div class="bgTask_listContainer">
                                    <div class="bg-light px-4 border-bottom" role="heading">
                                        <b>In progress</b>
                                    </div>
                                    <div class="bgTask_taskContainer">
                                        <div class="dropdown-item no-clickable border-bottom">
                                            <div class="row no-gutters align-items-center py-1">
                                                <div class="col-2" role="presentation">
                                                    <small class="text-secondary">
                                                        <i class="fas fa-cog fa-spin fa-2x"
                                                           aria-hidden="true"></i>
                                                    </small>
                                                </div>
                                                <div class="col-10">
                                                    <div class="d-flex align-items-center w-100"><span
                                                            class="w-100 text-nowrap text-truncate">Touchpoint Import</span>
                                                        <a href="#" class="bgtask_detailIcon ml-3" role="button"
                                                           data-toggle="tooltip" title="Show Details">
                                                            <i class="far fa-window-restore" aria-hidden="true"></i>
                                                            <span class="sr-only">Show Details</span>
                                                        </a>
                                                    </div>
                                                    <div class="text-muted text-nowrap text-truncate">10:45 PM -
                                                        GRIDCC_TP_XML
                                                    </div>
                                                    <div class="d-flex align-items-center w-100">
                                                        <div class="progress w-100 mr-3">
                                                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                                                 role="progressbar" style="width: 80%"
                                                                 aria-valuenow="80" aria-valuemin="0"
                                                                 aria-valuemax="100">80%
                                                            </div>
                                                        </div>
                                                        <small class="text-muted">
                                                            <span class="small">13s</span>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bg-light px-4 border-bottom" role="heading">
                                        <b>Complete</b>
                                    </div>
                                    <div id="bgTask_10214" class="bgTask_taskContainer">
                                        <div class="dropdown-item no-clickable border-bottom">
                                            <div class="row no-gutters align-items-center py-1">
                                                <div class="col-2" role="presentation">
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle fa-2x"
                                                           aria-hidden="true"></i>
                                                    </small>
                                                </div>
                                                <div class="col-10">
                                                    <div class="d-flex align-items-center w-100"><span
                                                            class="w-100 text-nowrap text-truncate">Touchpoint Import</span>
                                                        <a href="#" class="bgtask_downloadIcon ml-3"
                                                           role="button"
                                                           data-toggle="tooltip" title="Download">
                                                            <i
                                                                    class="far fa-download"
                                                                    aria-hidden="true"></i>
                                                            <span class="sr-only">Download</span>
                                                        </a>
                                                        <a href="#"
                                                           class="bgtask_removeTaskIcon ml-3"
                                                           role="button"
                                                           data-toggle="tooltip"
                                                           title="Remove item">
                                                            <i class="far fa-trash-alt" aria-hidden="true"></i>
                                                            <span class="sr-only">Remove item</span>
                                                        </a>
                                                    </div>
                                                    <div class="text-muted text-nowrap text-truncate">
                                                        10:41 PM - Happy Birthday Test
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="bgTask_9771" class="bgTask_taskContainer">
                                        <div class="dropdown-item no-clickable border-bottom">
                                            <div class="row no-gutters align-items-center py-1">
                                                <div class="col-2" role="presentation">
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle fa-2x"
                                                           aria-hidden="true"></i>
                                                    </small>
                                                </div>
                                                <div class="col-10">
                                                    <div class="d-flex align-items-center w-100"><span
                                                            class="w-100 text-nowrap text-truncate">Touchpoint Import</span>
                                                        <a href="#" class="bgtask_downloadIcon ml-3" role="button"
                                                           data-toggle="tooltip" title="Download">
                                                            <i class="far fa-download" aria-hidden="true"></i>
                                                            <span class="sr-only">Download</span>
                                                        </a>
                                                        <a href="#"
                                                           class="bgtask_removeTaskIcon ml-3"
                                                           role="button"
                                                           data-toggle="tooltip"
                                                           title="Remove item">
                                                            <i class="far fa-trash-alt" aria-hidden="true"></i>
                                                            <span class="sr-only">Remove item</span>
                                                        </a>
                                                    </div>
                                                    <div class="text-muted text-nowrap text-truncate">10:41 PM -
                                                        Developer Touchpoint
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="bgTask_1229" class="bgTask_taskContainer">
                                        <div class="dropdown-item no-clickable border-bottom">
                                            <div class="row no-gutters align-items-center py-1">
                                                <div class="col-2" role="presentation">
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle fa-2x"
                                                           aria-hidden="true"></i>
                                                    </small>
                                                </div>
                                                <div class="col-10">
                                                    <div class="d-flex align-items-center w-100"><span
                                                            class="w-100 text-nowrap text-truncate">Touchpoint Import</span>
                                                        <a href="#" class="bgtask_downloadIcon ml-3" role="button"
                                                           data-toggle="tooltip" title="Download">
                                                            <i class="far fa-download" aria-hidden="true"></i>
                                                            <span class="sr-only">Download</span>
                                                        </a>
                                                        <a href="#"
                                                           class="bgtask_removeTaskIcon ml-3"
                                                           role="button"
                                                           data-toggle="tooltip"
                                                           title="Remove item">
                                                            <i class="far fa-trash-alt" aria-hidden="true"></i>
                                                            <span class="sr-only">Remove item</span>
                                                        </a>
                                                    </div>
                                                    <div class="text-muted text-nowrap text-truncate">Jul 17,
                                                        2018 - Credit Card Statement - Whit...
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a id="userInfo" class="user-info nav-link dropdown-toggle" href="#" role="button"
                           aria-label="Signed in as Wilvin Jimenez - click to toggle user menu panel"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <div id="user-avatar" class="avatar col-auto p-0" role="presentation">WJ</div>
                            <div class="col-auto pr-0">
                                <div id="user-name" class="pb-1" aria-label="User name">Wilvin Jimenez</div>
                                <div>
                                    <small id="current-branch" aria-label="Branch" class="text-uppercase">
                                        Messagepoint
                                    </small>
                                    <i class="far fa-xs fa-angle-double-right mx-1" aria-hidden="true"></i>
                                    <small id="current-instance" aria-label="Instance" class="text-uppercase">
                                        Production
                                    </small>
                                </div>
                            </div>
                        </a>
                        <div id="userPanel" class="dropdown-menu dropdown-custom w-75"
                             aria-label="User menu actions"
                             role="menu">
                            <h5 class="dropdown-header dropdown-heading">User Panel</h5>
                            <div class="dropdown-divider" role="separator"></div>
                            <h6 class="dropdown-header">Take me to...</h6>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-tachometer-alt mr-3" aria-hidden="true"></i>
                                Dashboard
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-sliders-h mr-3" aria-hidden="true"></i>
                                Settings
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-plug mr-3" aria-hidden="true"></i>
                                Connected
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-chart-bar mr-3" aria-hidden="true"></i>
                                Rationalizer
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-exchange-alt mr-3" aria-hidden="true"></i>
                                Touchpoint Exchange
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-server mr-3" aria-hidden="true"></i>
                                Job Center
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-sign-out mr-3" aria-hidden="true"></i>
                                Log out
                            </a>
                            <div class="dropdown-divider" role="separator"></div>
                            <div id="userPanelAccordion" role="group">
                                <div class="h6 mb-0" role="heading">
                                    <a class="dropdown-header dropdown-item dropdown-toggle dropdown-flexblock collapsed"
                                       data-toggle="collapse" data-target="#languages" aria-expanded="false"
                                       aria-controls="languages" tabindex="0">Language (EN)
                                    </a>
                                </div>
                                <div id="languages" class="collapse" aria-label="Available languages"
                                     data-parent="#userPanelAccordion" role="region" style="">
                                    <div role="menu" aria-labelledby="languages">
                                        <a class="dropdown-item" href="#" role="menuitem">French (CA)</a>
                                        <a class="dropdown-item active" href="#" role="menuitem">English (US)
                                            <span
                                                    class="sr-only">(current)</span>
                                        </a>
                                        <a class="dropdown-item" href="#" role="menuitem">Spanish (ES)</a>
                                    </div>
                                </div>
                                <div class="dropdown-divider" role="presentation"></div>
                                <div class="h6 mb-0" role="heading">
                                    <a class="dropdown-header dropdown-item dropdown-toggle dropdown-flexblock"
                                       data-toggle="collapse"
                                       data-target="#instances" aria-expanded="false" aria-controls="instances"
                                       tabindex="0">Instance (Production)
                                    </a>
                                </div>
                                <div id="instances" class="collapse" aria-label="Instances"
                                     data-parent="#userPanelAccordion" role="region">
                                    <div role="menu" aria-labelledby="instances">
                                        <a class="dropdown-item" href="#">Auto</a>
                                        <a class="dropdown-item" href="#">Test</a>
                                        <a class="dropdown-item active" href="#">
                                            Production
                                            <b role="presentation">&nbsp;•</b>
                                            <span class="sr-only">(current)</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <main id="content">
        <div class="container py-5" style="min-width: 82rem;">
            <div class="box-shadow-4 rounded bg-white p-4">
                <div class="px-2 pb-1">
                    <h4 class="d-flex align-items-center">Text Styles
                        <button class="btn btn-link btn-link-inline ml-auto fs-xs font-weight-bold text-uppercase text-dark"
                                data-toggle="button" aria-pressed="false">
                            <i class="far fa-expand-alt fs-md mr-2"></i>Expand
                        </button>
                    </h4>
                    <div class="my-4">
                        <div class="d-flex align-items-center mb-3">
                            <button type="button" class="btn btn-primary mr-3">
                                <i class="far fa-plus-circle mr-2" aria-hidden="true"></i>Add
                            </button>
                            <div class="btn-group border-separate mr-auto" role="group" aria-label="Actions">
                                <button type="button" class="btn btn-dark">
                                    <i class="far fa-edit mr-2" aria-hidden="true"></i>Edit
                                </button>
                                <button type="button" class="btn btn-dark">
                                    <i class="far fa-clone mr-2" aria-hidden="true"></i>Clone
                                </button>
                                <div class="btn-group" role="group">
                                    <select id="btnGroupDrop1" title="Audit" class="complex-dropdown-select"
                                            aria-label="Audit" data-toggle="complex-dropdown"
                                            data-action="menu" data-dropdown-class="btn-dark">
                                        <option disabled>Generate message report</option>
                                        <option class="divider" disabled>&nbsp;</option>
                                        <option id="actionOption_11" disabled>Processing - Aug 27, 2018 05:42 PM
                                        </option>
                                    </select>
                                </div>
                                <div class="btn-group" role="group">
                                    <select id="btnGroupDrop2" title="More" class="complex-dropdown-select"
                                            aria-label="Audit" data-toggle="complex-dropdown" data-action="menu"
                                            data-dropdown-class="btn-dark">
                                        <option>Create working copy</option>
                                        <option id="wil-01">Archive</option>
                                        <option disabled>Hold</option>
                                        <option>Reassign to user</option>
                                    </select>
                                </div>
                            </div>
                            <div class="dropdown mr-2" data-toggle="tooltip" title="Toggle columns">
                                <button type="button" class="btn btn-sm btn-icon dropdown-toggle py-2 fs-md text-dark"
                                        data-toggle="dropdown" id="dropdownMenuButton" aria-label="Toggle columns"
                                        aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-columns" aria-hidden="true"></i>
                                </button>
                                <div class="dropdown-menu dropdown-custom mt-2" aria-labelledby="dropdownMenuButton">
                                    <h6 class="dropdown-header">Visible columns</h6>
                                    <div class="dropdown-item dropdown-item-checkbox">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="column1">
                                            <label class="custom-control-label" for="column1">Name</label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item dropdown-item-checkbox">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="column2">
                                            <label class="custom-control-label" for="column2">State</label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item dropdown-item-checkbox">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="column3">
                                            <label class="custom-control-label" for="column3">Assigned</label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item dropdown-item-checkbox">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="column4">
                                            <label class="custom-control-label" for="column4">Zone</label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item dropdown-item-checkbox">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="column5">
                                            <label class="custom-control-label" for="column5">Type</label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item dropdown-item-checkbox">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="column6">
                                            <label class="custom-control-label" for="column6">Target</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group position-relative m-0">
                                <label for="search" class="sr-only">Search</label>
                                <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                   aria-hidden="true"></i>
                                <input type="text" class="form-control bg-light pl-5 pr-5 border-0" id="search"
                                       placeholder="Search" size="20">
                            </div>
                        </div>
                    </div>
                    <div>
                        <table class="table table-sm table-hover border-bottom">
                            <thead>
                            <tr>
                                <th scope="col" style="width: 1px;"></th>
                                <th scope="col">Name</th>
                                <th scope="col">Font Name</th>
                                <th scope="col">Applied Font</th>
                                <th scope="col">Point Size</th>
                                <th scope="col">Bold</th>
                                <th scope="col">Italic</th>
                                <th scope="col">Underline</th>
                                <th scope="col">Color</th>
                                <th scope="col">Connector</th>
                                <th scope="col">Tag</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>
                                    <div class="custom-control custom-checkbox no-text">
                                        <input type="checkbox" class="custom-control-input" id="customCheckbox">
                                        <label class="custom-control-label py-2" for="customCheckbox">
                                            <span class="sr-only">Select</span>
                                        </label>
                                    </div>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm w-auto"
                                           value="Address_7_5pt_Bold"
                                           style="font-size: inherit;" size="18">
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm w-auto" value="Arial"
                                           style="font-size: inherit;" size="18">
                                </td>
                                <td>
                                    <div class="input-group input-group-sm bg-white rounded m-0">
                                        <input type="text" class="form-control" value="arial,helvetica,sans-serif"
                                               aria-describedby="button-addon2" style="font-size: inherit;" size="18">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-info"
                                                    type="button"
                                                    aria-label="Library" data-toggle="tooltip" title="Library">
                                                <i class="fas fa-book" aria-hidden="true"></i>
                                            </button>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    13
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1"
                                            type="button"
                                            aria-label="Multiple point size" data-toggle="tooltip"
                                            title="Multiple point size">
                                        <i class="far fa-font fa-xs" aria-hidden="true"></i>
                                        <i class="far fa-font ml--1" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <div class="d-inline-block custom-control custom-checkbox no-text align-middle">
                                        <input type="checkbox" class="custom-control-input" id="customCheckbox9"
                                               checked>
                                        <label class="custom-control-label py-2" for="customCheckbox9">
                                            <span class="sr-only">Select</span>
                                        </label>
                                    </div>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1 text-info"
                                            type="button"
                                            aria-label="Multiple color" data-toggle="tooltip" title="Multiple color">
                                        <i class="fas fa-bold" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <div class="d-inline-block custom-control custom-checkbox no-text align-middle">
                                        <input type="checkbox" class="custom-control-input" id="customCheckbox6"
                                               checked>
                                        <label class="custom-control-label py-2" for="customCheckbox6">
                                            <span class="sr-only">Select</span>
                                        </label>
                                    </div>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1 text-info"
                                            type="button"
                                            aria-label="Multiple color" data-toggle="tooltip" title="Multiple color">
                                        <i class="fas fa-italic" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <div class="d-inline-block custom-control custom-checkbox no-text align-middle">
                                        <input type="checkbox" class="custom-control-input" id="customCheckbox5"
                                               checked>
                                        <label class="custom-control-label py-2" for="customCheckbox5">
                                            <span class="sr-only">Select</span>
                                        </label>
                                    </div>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1 text-info"
                                            type="button"
                                            aria-label="Multiple color" data-toggle="tooltip" title="Multiple color">
                                        <i class="fas fa-underline" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <button class="btn btn-blank btn-icon pl-1 border-0" style="background: #000;"
                                            data-toggle="tooltip" title="#000" type="button">
                                        <span class="sr-only">#000</span>
                                    </button>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1" type="button"
                                            aria-label="Multiple color" data-toggle="tooltip" title="Multiple color">
                                        <i class="far fa-fill-drip fa-xs" aria-hidden="true"></i>
                                        <i class="far fa-fill-drip ml--1" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm w-auto"
                                           value="ADB13" style="font-size: inherit;" size="18">
                                </td>
                                <td>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0" type="button"
                                            aria-label="Tagging override" data-toggle="tooltip"
                                            title="Tagging override">
                                        <i class="far fa-code" aria-hidden="true"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="custom-control custom-checkbox no-text">
                                        <input type="checkbox" class="custom-control-input" id="customCheckbox2">
                                        <label class="custom-control-label py-2" for="customCheckbox2">
                                            <span class="sr-only">Select</span>
                                        </label>
                                    </div>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm w-auto"
                                           value="Address_7_5pt" style="font-size: inherit;" size="18">
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm w-auto"
                                           value="Arial" style="font-size: inherit;" size="18">
                                </td>
                                <td>
                                    <div class="input-group input-group-sm bg-white rounded m-0">
                                        <input type="text" class="form-control" value="arial,helvetica,sans-serif"
                                               aria-describedby="button-addon2" style="font-size: inherit;" size="18">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-info"
                                                    type="button"
                                                    aria-label="Library" data-toggle="tooltip" title="Library">
                                                <i class="fas fa-book" aria-hidden="true"></i>
                                            </button>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    5
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1"
                                            type="button"
                                            aria-label="Multiple point size" data-toggle="tooltip"
                                            title="Multiple point size">
                                        <i class="far fa-font fa-xs" aria-hidden="true"></i>
                                        <i class="far fa-font ml--1" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <div class="d-inline-block custom-control custom-checkbox no-text align-middle">
                                        <input type="checkbox" class="custom-control-input" id="customCheckbox10"
                                               checked>
                                        <label class="custom-control-label py-2" for="customCheckbox10">
                                            <span class="sr-only">Select</span>
                                        </label>
                                    </div>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1 text-info"
                                            type="button"
                                            aria-label="Multiple color" data-toggle="tooltip" title="Multiple color">
                                        <i class="fas fa-bold" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <div class="d-inline-block custom-control custom-checkbox no-text align-middle">
                                        <input type="checkbox" class="custom-control-input" id="customCheckbox7">
                                        <label class="custom-control-label py-2" for="customCheckbox7">
                                            <span class="sr-only">Select</span>
                                        </label>
                                    </div>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1 text-info"
                                            type="button"
                                            aria-label="Multiple color" data-toggle="tooltip" title="Multiple color">
                                        <i class="fas fa-italic" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0" type="button"
                                            aria-label="Multiple color" data-toggle="tooltip" title="Multiple color">
                                        <i class="far fa-underline" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <button class="btn btn-blank btn-icon pl-1 border-0" style="background: #f00;"
                                            data-toggle="tooltip" title="#f00" type="button">
                                        <span class="sr-only">#f00</span>
                                    </button>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1" type="button"
                                            aria-label="Multiple color" data-toggle="tooltip" title="Multiple color">
                                        <i class="far fa-fill-drip fa-xs" aria-hidden="true"></i>
                                        <i class="far fa-fill-drip ml--1" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm w-auto"
                                           value="Arial13Grey" style="font-size: inherit;" size="18">
                                </td>
                                <td>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0" type="button"
                                            aria-label="Tagging override" data-toggle="tooltip"
                                            title="Tagging override">
                                        <i class="far fa-code" aria-hidden="true"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="custom-control custom-checkbox no-text">
                                        <input type="checkbox" class="custom-control-input" id="customCheckbox3">
                                        <label class="custom-control-label py-2" for="customCheckbox3">
                                            <span class="sr-only">Select</span>
                                        </label>
                                    </div>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm w-auto"
                                           value="Arial13Grey" style="font-size: inherit;" size="18">
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm w-auto"
                                           value="Arial" style="font-size: inherit;" size="18">
                                </td>
                                <td>
                                    <div class="input-group input-group-sm bg-white rounded m-0">
                                        <input type="text" class="form-control" value="arial,helvetica,sans-serif"
                                               aria-describedby="button-addon2" style="font-size: inherit;" size="18">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-info"
                                                    type="button"
                                                    aria-label="Library" data-toggle="tooltip" title="Library">
                                                <i class="fas fa-book" aria-hidden="true"></i>
                                            </button>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <a href="javascript:void(0);" role="button">Multiple (5)</a>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1 text-info"
                                            type="button"
                                            aria-label="Multiple point size" data-toggle="tooltip"
                                            title="Multiple point size">
                                        <i class="fas fa-font fa-xs" aria-hidden="true"></i>
                                        <i class="fas fa-font ml--1" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <div class="d-inline-block custom-control custom-checkbox no-text align-middle">
                                        <input type="checkbox" class="custom-control-input" id="customCheckbox11"
                                               checked>
                                        <label class="custom-control-label py-2" for="customCheckbox11">
                                            <span class="sr-only">Select</span>
                                        </label>
                                    </div>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1 text-info"
                                            type="button"
                                            aria-label="Multiple color" data-toggle="tooltip" title="Multiple color">
                                        <i class="fas fa-bold" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <div class="d-inline-block custom-control custom-checkbox no-text align-middle">
                                        <input type="checkbox" class="custom-control-input" id="customCheckbox8"
                                               checked>
                                        <label class="custom-control-label py-2" for="customCheckbox8">
                                            <span class="sr-only">Select</span>
                                        </label>
                                    </div>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1 text-info"
                                            type="button"
                                            aria-label="Multiple color" data-toggle="tooltip" title="Multiple color">
                                        <i class="fas fa-italic" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <div class="d-inline-block custom-control custom-checkbox no-text align-middle">
                                        <input type="checkbox" class="custom-control-input" id="customCheckbox4">
                                        <label class="custom-control-label py-2" for="customCheckbox4">
                                            <span class="sr-only">Select</span>
                                        </label>
                                    </div>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1 text-info"
                                            type="button"
                                            aria-label="Multiple color" data-toggle="tooltip" title="Multiple color">
                                        <i class="fas fa-underline" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <a href="javascript:void(0);" role="button">Multiple (3)</a>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0 ml-1 text-info"
                                            type="button"
                                            aria-label="Multiple color" data-toggle="tooltip" title="Multiple color">
                                        <i class="fas fa-fill-drip fa-xs" aria-hidden="true"></i>
                                        <i class="fas fa-fill-drip ml--1" aria-hidden="true"></i>
                                    </button>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm w-auto"
                                           value="Arial14Blue" style="font-size: inherit;" size="18">
                                </td>
                                <td>
                                    <button class="btn btn-blank btn-icon py-1 px-2 border-0" type="button"
                                            aria-label="Tagging override" data-toggle="tooltip"
                                            title="Tagging override">
                                        <i class="far fa-code" aria-hidden="true"></i>
                                    </button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="d-flex align-items-center">
                            <div class="mr-auto">
                                <div class="form-group row align-items-center mb-0">
                                    <div class="col-sm-auto pr-2">
                                        <select id="entry-select"
                                                class="custom-select custom-select-sm bg-lightest border-0 fs-xs">
                                            <option value="10" selected>10</option>
                                            <option value="20">20</option>
                                            <option value="30">30</option>
                                            <option value="50">50</option>
                                            <option value="0">All</option>
                                        </select>
                                    </div>
                                    <label for="entry-select" class="col-sm-auto col-form-label pl-1 fs-xs">entries per
                                        page. Showing 1 to 10 of 48 entries.</label>
                                </div>
                            </div>
                            <nav aria-label="Table navigation">
                                <ul class="pagination m-0">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Previous" tabindex="-1">
                                            <i class="far fa-angle-double-left" aria-hidden="true"></i>
                                            <span class="sr-only">Previous</span>
                                        </a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Previous" tabindex="-1">
                                            <i class="far fa-angle-left" aria-hidden="true"></i>
                                            <span class="sr-only">Previous</span>
                                        </a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1 <span class="sr-only">(current)</span></a>
                                    </li>
                                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                                    <li class="page-item"><a class="page-link" href="#">4</a></li>
                                    <li class="page-item"><a class="page-link" href="#">5</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#" aria-label="Next">
                                            <i class="far fa-angle-right" aria-hidden="true"></i>
                                            <span class="sr-only">Next</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="#" aria-label="Next">
                                            <i class="far fa-angle-double-right" aria-hidden="true"></i>
                                            <span class="sr-only">Next</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <footer id="legal" class="footer-main">
        <div class="footer-content footer-dark d-flex justify-content-between align-items-center w-100">
            <b class="mr-auto">
                &copy; 2006-2018 Messagepoint Inc. All rights reserved.
                <a class="ml-3" href="#" target="_blank" rel="noopener">Privacy</a>
            </b>
            <b class="ml-3">_v18.1.1.000</b>
            <span id="developerMode" class="d-inline-block h-100 ml-4" aria-hidden="true" data-toggle="tooltip"
                  title="dev_mode"><span class="developer-mode box-shadow"><i
                    class="far fa-user-secret"></i></span></span>
        </div>
    </footer>
</div>
<!-- Scripts -->
<script src="lib/handlebars/handlebars-v4.0.11.js"></script>
<script src="../includes/node_modules/underscore/underscore-min.js"></script>
<script src="lib/jquery-3.3.1/jquery-3.3.1.min.js"></script>
<script src="../includes/node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Dummy texts needed
    var client_messages = {text: {}};
    client_messages.text.more = "More";
    client_messages.text.modules = "Modules";

    $(document).ready(function () {
        $('.test-checkbox').prop('indeterminate', true);
    });

</script>
<script src="../includes/javascript/mp.common.js"></script>
<script src="js/mp.complexDropdown.js"></script>
<script src="js/mp.priorityMainNavigation.js"></script>
<script src="js/mp.main.js"></script>
<script>
    $(function () {

        $('#btnGroupDrop1').data('complexDropdown').refreshAllTheOptions();
        $('#btnGroupDrop2').data('complexDropdown').disableOptionById('wil-01');
        $('#btnGroupDrop2').data('complexDropdown').disableAllTheOptions();
        $('#btnGroupDrop2').data('complexDropdown').enableOptionById('wil-01');

    });
</script>
</body>
</html>