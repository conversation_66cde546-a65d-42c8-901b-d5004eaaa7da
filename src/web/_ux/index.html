<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="Messagepoint Inc.">
    <title>Messagepoint</title>
    <!-- Favicon -->
    <!-- Styles -->
    <link rel="stylesheet" href="scss/themes/messagepoint/styles.css">
</head>
<body class="override-default-sizing">
<a href="#content" class="sr-only sr-only-focusable h6 mx-1">Skip to main content</a>
<div id="page">
    <header class="box-shadow">
        <nav class="navbar navbar-expand navbar-dark" aria-label="Main navigation">
            <div class="nav-item">
                <a class="navbar-brand nav-link" href="#" data-toggle="tooltip" title="←&nbsp; Back to home page">
                    <img src="img/brands/messagepoint-logo-dark-theme.svg" class="d-inline-block align-middle"
                         alt="Header Logo">
                </a>
            </div>
            <div class="nav-container navbar-collapse flex-row">
                <ul id="main-navigation" class="nav-main navbar-nav" aria-label="Primary navigation">
                    <li class="nav-item dropdown active">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown01" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Touchpoint
                            <span class="sr-only">(current)</span>
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown01">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Touchpoints</a>
                            <a class="dropdown-item" href="#">Touchpoint Collections</a>
                            <a class="dropdown-item" href="#">Output Tags</a>
                            <a class="dropdown-item" href="#">Delivery Events</a>
                            <div class="dropdown-divider" role="separator"></div>
                            <h6 class="dropdown-header">New</h6>
                            <a class="dropdown-item" href="#">Touchpoint...</a>
                            <a class="dropdown-item" href="#">Import...</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown02" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Task
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown02">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Tasks</a>
                            <a class="dropdown-item" href="#">Projects</a>
                            <a class="dropdown-item" href="#">System Tasks</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown03" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Target
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown03">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Target Groups</a>
                            <a class="dropdown-item" href="#">Target Rules</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown04" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Content
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown04">
                            <h6 class="dropdown-header">Styles</h6>
                            <a class="dropdown-item" href="#">Texts</a>
                            <a class="dropdown-item" href="#">Paragraph</a>
                            <a class="dropdown-item" href="#">List</a>
                            <div class="dropdown-divider" role="separator"></div>
                            <h6 class="dropdown-header">Content</h6>
                            <a class="dropdown-item" href="#">Smart Text</a>
                            <a class="dropdown-item" href="#">Smart Canvas</a>
                            <a class="dropdown-item" href="#">Image Library</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown05" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Test
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown05">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Reports</a>
                            <a class="dropdown-item" href="#">Simulations</a>
                            <a class="dropdown-item" href="#">Connected</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a id="dropdown07a" class="nav-link dropdown-toggle" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Insert</a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown07a">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Inserts</a>
                            <a class="dropdown-item" href="#">Insert Schedules</a>
                            <a class="dropdown-item" href="#">Insert Schedules Setup</a>
                            <a class="dropdown-item" href="#">Rate Sheets</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a id="dropdown07b" class="nav-link dropdown-toggle" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Report</a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown07b">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Reports</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a id="dropdown07c" class="nav-link dropdown-toggle" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Data</a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown07c">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Data Sources</a>
                            <a class="dropdown-item" href="#">Data Groups</a>
                            <a class="dropdown-item" href="#">Data Collections</a>
                            <a class="dropdown-item" href="#">Data Files</a>
                            <a class="dropdown-item" href="#">Data Resources</a>
                            <a class="dropdown-item" href="#">Variables</a>
                            <a class="dropdown-item" href="#">Selector Groups</a>
                            <a class="dropdown-item" href="#">External Event</a>
                            <a class="dropdown-item" href="#">Lookup Tables</a>
                            <a class="dropdown-item" href="#">Metadata Templates</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a id="dropdown07d" class="nav-link dropdown-toggle" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Setup
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown07d">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Workflow Library</a>
                            <a class="dropdown-item" href="#">Master Admin: System
                                Settings
                            </a>
                            <a class="dropdown-item" href="#">Master Admin: Domains</a>
                            <a class="dropdown-item" href="#">Master Admin:
                                Maintenance
                            </a>
                            <a class="dropdown-item" href="#">Master Admin: Licenses</a>
                            <a class="dropdown-item" href="#">Bundle Delivery</a>
                            <a class="dropdown-item" href="#">System Settings</a>
                            <a class="dropdown-item" href="#">Security Settings</a>
                            <a class="dropdown-item" href="#">Locale Settings</a>
                            <a class="dropdown-item" href="#">License Information</a>
                            <a class="dropdown-item" href="#">Feature activation</a>
                            <a class="dropdown-item" href="#">User Interface Settings
                            </a>
                            <a class="dropdown-item" href="#">Roles</a>
                            <a class="dropdown-item" href="#">Workgroups</a>
                            <a class="dropdown-item" href="#">Users</a>
                            <a class="dropdown-item" href="#">Auditing</a>
                            <div class="dropdown-divider" role="separator"></div>
                            <h6 class="dropdown-header">New</h6>
                            <a class="dropdown-item" href="#">Workgroup</a>
                        </div>
                    </li>
                </ul>
                <div class="navbar-nav nav-right">
                    <div class="nav-item dropdown" data-toggle="tooltip" data-placement="left"
                         title="Notification: No background tasks">
                        <a id="backgroundTasksPlaceholder" class="nav-link nav-icontool" href="#"
                           data-toggle="dropdown"
                           role="button" aria-haspopup="true"
                           aria-expanded="false" aria-label="Background task manager">
                            <span class="badge badge-danger">12</span>
                            <i class="far fa-bell fa-lg" aria-hidden="true"></i>
                        </a>
                        <div class="dropdown-menu dropdown-custom w-small"
                             aria-labelledby="backgroundTasksPlaceholder">
                            <h5 class="dropdown-header dropdown-heading">BACKGROUND TASK MANAGER</h5>
                            <div class="dropdown-divider mb-0" role="separator"></div>
                            <!-- TABS -->
                            <div align="center" style="padding: 18px;">
                                <table cellspacing="0" cellpadding="0" border="0">
                                    <tr>
                                        <td style="padding: 0px;">
                                            <div align="center"
                                                 class="selectMyTask workflowTabSelected workflowTabFirst"
                                                 onclick="javascript:backgroundManagerSelectMyTasks(this, event);">
                                                <div class="workflowTabText">
                                                    <fmtSpring:message code="page.label.my.tasks"/>
                                                </div>
                                            </div>
                                        </td>
                                        <td style="padding: 0px;">
                                            <div align="center" class="selectAllTask workflowTab workflowTabLast"
                                                 onclick="javascript:backgroundManagerSelectAllTasks(this, event);">
                                                <div class="workflowTabText">
                                                    <fmtSpring:message code="page.label.all.tasks"/>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="dropdown-divider mb-0" role="separator"></div>
                            <div class="dropdown-content tasks-list">
                                <div class="bgTask_listContainer">
                                    <div class="bg-light px-4 border-bottom" role="heading">
                                        <b>In progress</b>
                                    </div>
                                    <div class="bgTask_taskContainer">
                                        <div class="dropdown-item no-clickable border-bottom">
                                            <div class="row no-gutters align-items-center py-1">
                                                <div class="col-2" role="presentation">
                                                    <small class="text-secondary">
                                                        <i class="fas fa-cog fa-spin fa-2x"
                                                           aria-hidden="true"></i>
                                                    </small>
                                                </div>
                                                <div class="col-10">
                                                    <div class="d-flex align-items-center w-100"><span
                                                            class="w-100 text-nowrap text-truncate">Touchpoint Import</span>
                                                        <a href="#" class="bgtask_detailIcon ml-3" role="button"
                                                           data-toggle="tooltip" title="Show Details">
                                                            <i class="far fa-window-restore" aria-hidden="true"></i>
                                                            <span class="sr-only">Show Details</span>
                                                        </a>
                                                    </div>
                                                    <div class="text-muted text-nowrap text-truncate">10:45 PM -
                                                        GRIDCC_TP_XML
                                                    </div>
                                                    <div class="d-flex align-items-center w-100">
                                                        <div class="progress w-100 mr-3">
                                                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                                                 role="progressbar" style="width: 80%"
                                                                 aria-valuenow="80" aria-valuemin="0"
                                                                 aria-valuemax="100">80%
                                                            </div>
                                                        </div>
                                                        <small class="text-muted">
                                                            <span class="small">13s</span>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="dropdown-item no-clickable border-bottom">
                                            <div class="row no-gutters align-items-center py-1">
                                                <div class="col-2" role="presentation">
                                                    <small class="text-secondary">
                                                        <i class="fas fa-cog fa-spin fa-2x"
                                                           aria-hidden="true"></i>
                                                    </small>
                                                </div>
                                                <div class="col-10">
                                                    <div class="d-flex align-items-center w-100"><span
                                                            class="w-100 text-nowrap text-truncate">Indeterminate example</span>
                                                        <a href="#" class="bgtask_detailIcon ml-3" role="button"
                                                           data-toggle="tooltip" title="Show Details">
                                                            <i class="far fa-window-restore" aria-hidden="true"></i>
                                                            <span class="sr-only">Show Details</span>
                                                        </a>
                                                    </div>
                                                    <div class="text-muted text-nowrap text-truncate">10:45 PM -
                                                        DEV_TP_XMLS_SAMPLE
                                                    </div>
                                                    <div class="d-flex align-items-center w-100">
                                                        <div class="progress w-100 mr-3">
                                                            <div class="progress-bar progress-bar-striped progress-bar-animated w-100"
                                                                 role="progressbar" aria-valuemin="0"
                                                                 aria-valuetext="Loading"
                                                                 aria-valuemax="100">
                                                                <span class="text-lowercase">Loading</span>
                                                            </div>
                                                        </div>
                                                        <small class="text-muted">
                                                            <span class="small">--</span>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bg-light px-4 border-bottom" role="heading">
                                        <b>Complete</b>
                                    </div>
                                    <div id="bgTask_10214" class="bgTask_taskContainer">
                                        <div class="dropdown-item no-clickable border-bottom">
                                            <div class="row no-gutters align-items-center py-1">
                                                <div class="col-2" role="presentation">
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle fa-2x"
                                                           aria-hidden="true"></i>
                                                    </small>
                                                </div>
                                                <div class="col-10">
                                                    <div class="d-flex align-items-center w-100"><span
                                                            class="w-100 text-nowrap text-truncate">Touchpoint Import</span>
                                                        <a href="#" class="bgtask_downloadIcon ml-3"
                                                           role="button"
                                                           data-toggle="tooltip" title="Download">
                                                            <i
                                                                    class="far fa-download"
                                                                    aria-hidden="true"></i>
                                                            <span class="sr-only">Download</span>
                                                        </a>
                                                        <a href="#"
                                                           class="bgtask_removeTaskIcon ml-3"
                                                           role="button"
                                                           data-toggle="tooltip"
                                                           title="Remove item">
                                                            <i class="far fa-trash-alt" aria-hidden="true"></i>
                                                            <span class="sr-only">Remove item</span>
                                                        </a>
                                                    </div>
                                                    <div class="text-muted text-nowrap text-truncate">
                                                        10:41 PM - Happy Birthday Test
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="bgTask_9771" class="bgTask_taskContainer">
                                        <div class="dropdown-item no-clickable border-bottom">
                                            <div class="row no-gutters align-items-center py-1">
                                                <div class="col-2" role="presentation">
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle fa-2x"
                                                           aria-hidden="true"></i>
                                                    </small>
                                                </div>
                                                <div class="col-10">
                                                    <div class="d-flex align-items-center w-100"><span
                                                            class="w-100 text-nowrap text-truncate">Touchpoint Import</span>
                                                        <a href="#" class="bgtask_downloadIcon ml-3" role="button"
                                                           data-toggle="tooltip" title="Download">
                                                            <i class="far fa-download" aria-hidden="true"></i>
                                                            <span class="sr-only">Download</span>
                                                        </a>
                                                        <a href="#"
                                                           class="bgtask_removeTaskIcon ml-3"
                                                           role="button"
                                                           data-toggle="tooltip"
                                                           title="Remove item">
                                                            <i class="far fa-trash-alt" aria-hidden="true"></i>
                                                            <span class="sr-only">Remove item</span>
                                                        </a>
                                                    </div>
                                                    <div class="text-muted text-nowrap text-truncate">10:41 PM -
                                                        Developer Touchpoint
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="bgTask_1229" class="bgTask_taskContainer">
                                        <div class="dropdown-item no-clickable border-bottom">
                                            <div class="row no-gutters align-items-center py-1">
                                                <div class="col-2" role="presentation">
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle fa-2x"
                                                           aria-hidden="true"></i>
                                                    </small>
                                                </div>
                                                <div class="col-10">
                                                    <div class="d-flex align-items-center w-100"><span
                                                            class="w-100 text-nowrap text-truncate">Touchpoint Import</span>
                                                        <a href="#" class="bgtask_downloadIcon ml-3" role="button"
                                                           data-toggle="tooltip" title="Download">
                                                            <i class="far fa-download" aria-hidden="true"></i>
                                                            <span class="sr-only">Download</span>
                                                        </a>
                                                        <a href="#"
                                                           class="bgtask_removeTaskIcon ml-3"
                                                           role="button"
                                                           data-toggle="tooltip"
                                                           title="Remove item">
                                                            <i class="far fa-trash-alt" aria-hidden="true"></i>
                                                            <span class="sr-only">Remove item</span>
                                                        </a>
                                                    </div>
                                                    <div class="text-muted text-nowrap text-truncate">Jul 17,
                                                        2018 - Credit Card Statement - Whit...
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a id="userInfo" class="user-info nav-link dropdown-toggle" href="#" role="button"
                           aria-label="Signed in as Wilvin Jimenez - click to toggle user menu panel"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <div id="user-avatar" class="avatar col-auto p-0" role="presentation">WJ</div>
                            <div class="col-auto pr-0">
                                <div id="user-name" class="pb-1" aria-label="User name">Wilvin Jimenez</div>
                                <div>
                                    <small id="current-branch" aria-label="Branch" class="text-uppercase">
                                        Messagepoint
                                    </small>
                                    <i class="far fa-xs fa-angle-double-right mx-1" aria-hidden="true"></i>
                                    <small id="current-instance" aria-label="Instance" class="text-uppercase">
                                        Production
                                    </small>
                                </div>
                            </div>
                        </a>
                        <div id="userPanel" class="dropdown-menu dropdown-custom w-75"
                             aria-label="User menu actions"
                             role="menu">
                            <h5 class="dropdown-header dropdown-heading">User Panel</h5>
                            <div class="dropdown-divider" role="separator"></div>
                            <h6 class="dropdown-header">Take me to...</h6>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-tachometer-alt mr-3" aria-hidden="true"></i>
                                Dashboard
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-sliders-h mr-3" aria-hidden="true"></i>
                                Settings
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-plug mr-3" aria-hidden="true"></i>
                                Connected
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-chart-bar mr-3" aria-hidden="true"></i>
                                Rationalizer
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-exchange-alt mr-3" aria-hidden="true"></i>
                                Touchpoint Exchange
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-server mr-3" aria-hidden="true"></i>
                                Job Center
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-sign-out mr-3" aria-hidden="true"></i>
                                Log out
                            </a>
                            <div class="dropdown-divider" role="separator"></div>
                            <div id="userPanelAccordion" role="group">
                                <div class="h6 mb-0" role="heading">
                                    <a class="dropdown-header dropdown-item dropdown-toggle dropdown-flexblock collapsed"
                                       data-toggle="collapse" data-target="#languages" aria-expanded="false"
                                       aria-controls="languages" tabindex="0">Language (EN)
                                    </a>
                                </div>
                                <div id="languages" class="collapse" aria-label="Available languages"
                                     data-parent="#userPanelAccordion" role="region" style="">
                                    <div role="menu" aria-labelledby="languages">
                                        <a class="dropdown-item" href="#" role="menuitem">French (CA)</a>
                                        <a class="dropdown-item active" href="#" role="menuitem">English (US)
                                            <span
                                                    class="sr-only">(current)</span>
                                        </a>
                                        <a class="dropdown-item" href="#" role="menuitem">Spanish (ES)</a>
                                    </div>
                                </div>
                                <div class="dropdown-divider" role="presentation"></div>
                                <div class="h6 mb-0" role="heading">
                                    <a class="dropdown-header dropdown-item dropdown-toggle dropdown-flexblock"
                                       data-toggle="collapse"
                                       data-target="#instances" aria-expanded="false" aria-controls="instances"
                                       tabindex="0">Instance (Production)
                                    </a>
                                </div>
                                <div id="instances" class="collapse" aria-label="Instances"
                                     data-parent="#userPanelAccordion" role="region">
                                    <div role="menu" aria-labelledby="instances">
                                        <a class="dropdown-item" href="#">Auto</a>
                                        <a class="dropdown-item" href="#">Test</a>
                                        <a class="dropdown-item active" href="#">
                                            Production
                                            <b role="presentation">&nbsp;•</b>
                                            <span class="sr-only">(current)</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <div id="contextBar" class="context-bar btn-toolbar bg-white box-shadow" role="toolbar"
         aria-label="Context bar">
        <div class="btn-group border-right" data-toggle="tooltip" title="Current touchpoint" role="group">
            <button class="btn btn-blank rounded-0 dropdown-toggle" aria-label="Current touchpoint">
                <i class="far fa-bars mr-3" aria-hidden="true"></i>
                Credit Card Statement
            </button>
        </div>
        <div class="btn-group border-right" role="group" aria-label="Touchpoint channel">
            <div class="btn-group" data-toggle="tooltip" title="Touchpoint channel" role="group">
                <button class="btn btn-blank fs-sm rounded-0 dropdown-toggle" aria-label="Content view">
                            <span class="badge badge-light p-2"><i class="far fa-file-alt fa-lg mr-1"
                                                                   aria-hidden="true"></i>Print</span>
                </button>
            </div>
        </div>
        <div class="btn-group border-right mr-auto" role="group" aria-label="Touchpoint tools">
            <div class="btn-group" data-toggle="tooltip" title="Touchpoint setup" role="group">
                <button class="btn btn-blank rounded-0 fs-md dropdown-toggle" aria-label="Touchpoint setup">
                    <i class="fas fa-wrench" aria-hidden="true"></i>
                </button>
            </div>
            <div class="btn-group" data-toggle="tooltip" title="Content search" role="group">
                <button class="btn btn-blank fs-md dropdown-toggle" aria-label="Content search">
                    <i class="far fa-search" aria-hidden="true"></i>
                </button>
            </div>
            <div class="btn-group" data-toggle="tooltip" title="Variant finder" role="group">
                <button class="btn btn-blank dropdown-toggle" aria-label="Variant finder">
                    <i class="far fa-sitemap" aria-hidden="true"></i>
                </button>
            </div>
            <div class="btn-group" data-toggle="tooltip" title="Variant actions" role="group">
                <button class="btn btn-blank rounded-0 fs-md dropdown-toggle" aria-label="Variant actions">
                    <i class="far fa-cog" aria-hidden="true"></i>
                </button>
            </div>
        </div>
        <div class="btn-group border-left" role="group" aria-label="Touchpoint content">
            <div class="btn-group" data-toggle="tooltip" title="Content view" role="group">
                <button id="test" class="btn btn-blank rounded-0 dropdown-toggle"
                        aria-label="Content view">
                    Message
                </button>
            </div>
            <div class="btn-group border-left" data-toggle="tooltip" title="Content language" role="group">
                <button class="btn btn-blank rounded-0 dropdown-toggle"
                        aria-label="Content language">
                    <i class="far fa-language fa-rotate-315 mr-2" aria-hidden="true"></i>
                    (EN)
                </button>
            </div>
        </div>
    </div>
    <main id="content">
        <div class="container py-5">
            <button type="button" class="btn btn-primary">Primary</button>
            <button type="button" class="btn btn-secondary">Secondary</button>
            <button type="button" class="btn btn-success">Success</button>
            <button type="button" class="btn btn-danger">Danger</button>
            <button type="button" class="btn btn-warning">Warning</button>
            <button type="button" class="btn btn-info">Info</button>
            <button type="button" class="btn btn-light">Light</button>
            <button type="button" class="btn btn-dark">Dark</button>
            <button type="button" class="btn btn-link">Link</button>
            <br>
            <br>
            <button type="button" class="btn btn-outline-primary">Primary</button>
            <button type="button" class="btn btn-outline-secondary">Secondary</button>
            <button type="button" class="btn btn-outline-success">Success</button>
            <button type="button" class="btn btn-outline-danger">Danger</button>
            <button type="button" class="btn btn-outline-warning">Warning</button>
            <button type="button" class="btn btn-outline-info">Info</button>
            <button type="button" class="btn btn-outline-light">Light</button>
            <button type="button" class="btn btn-outline-dark">Dark</button>
            <br>
            <br>
            <div class="progress-loader">
                <i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>
            </div>
            <div class="progress-loader progress-loader-sm">
                <i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>
            </div>
            <div class="progress-loader progress-loader-md">
                <i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>
            </div>
            <div class="progress-loader progress-loader-lg">
                <i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>
            </div>
            <br>
            <br>
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input test-checkbox" id="customCheck1">
                <label class="custom-control-label" for="customCheck1">Check this custom checkbox</label>
            </div>
            <br>
            <br>
            <div class="custom-control custom-radio">
                <input type="radio" id="customRadio1" name="customRadio" class="custom-control-input">
                <label class="custom-control-label" for="customRadio1">Toggle this custom radio</label>
            </div>
            <div class="custom-control custom-radio">
                <input type="radio" id="customRadio2" name="customRadio" class="custom-control-input">
                <label class="custom-control-label" for="customRadio2">Or toggle this other custom radio</label>
            </div>
            <br>
            <br>
            <div class="d-inline-block custom-control custom-switch">
                <input type="checkbox" class="custom-control-input" id="customSwitch1"
                       data-checkedtext="At least one" data-uncheckedtext="More than one available">
                <label class="custom-control-label" for="customSwitch1">-</label>
            </div>
            <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input" disabled id="customSwitch2">
                <label class="custom-control-label" for="customSwitch2">Disabled switch element</label>
            </div>
            <div class="p-4">
                <div class="form-group">
                    <label for="inputState">State</label>
                    <select id="inputState" class="custom-select">
                        <option selected>Open this select menu</option>
                        <option value="1">One</option>
                        <option value="2">Two</option>
                        <option value="3">Three</option>
                    </select>
                </div>
            </div>
            <br>
            <div class="input-group input-group-sm mb-3">
                <div class="input-group-prepend">
                    <span class="input-group-text" id="inputGroup-sizing-sm">Small</span>
                </div>
                <input type="text" class="form-control" aria-label="Sizing example input"
                       aria-describedby="inputGroup-sizing-sm">
            </div>

            <div class="input-group mb-3">
                <div class="input-group-prepend">
                    <span class="input-group-text" id="inputGroup-sizing-default">Default</span>
                </div>
                <input type="text" class="form-control" aria-label="Sizing example input"
                       aria-describedby="inputGroup-sizing-default">
            </div>

            <div class="input-group input-group-lg">
                <div class="input-group-prepend">
                    <span class="input-group-text" id="inputGroup-sizing-lg">Large</span>
                </div>
                <input type="text" class="form-control" aria-label="Sizing example input"
                       aria-describedby="inputGroup-sizing-lg">
            </div>
            <br>
            <div class="input-group input-group-sm bg-white rounded m-0">
                <input type="text" class="form-control" value="arial,helvetica,sans-serif"
                       style="font-size: inherit;" size="18">
                <div class="input-group-append">
                    <button class="btn btn-outline-info"
                            type="button"
                            aria-label="Library" data-toggle="tooltip" title="Library">
                        <i class="fas fa-book" aria-hidden="true"></i>
                    </button>
                </div>
            </div>
            <br>
            <br>
            <div class="dropdown ml-4">
                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"
                        id="dropdownMenuButton"
                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Widgets
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <div class="dropdown-item p-0">
                        <div class="form-check p-0">
                            <label class="form-check-label w-100 py-1 px-4">
                                <input name="enabledWidgets" class="form-check-input fs-md mx-0" value="13"
                                       type="checkbox"
                                       checked="checked">
                                <input type="hidden" name="_enabledWidgets">
                                <span class="ml-4">Metadata Search</span>
                            </label>
                        </div>
                    </div>
                    <div class="dropdown-item p-0">
                        <div class="form-check p-0">
                            <label class="form-check-label w-100 py-1 px-4">
                                <input name="enabledWidgets" class="form-check-input fs-md mx-0" value="13"
                                       type="checkbox">
                                <input type="hidden" name="_enabledWidgets">
                                <span class="ml-4">Metadata Search</span>
                            </label>
                        </div>
                    </div>
                    <div class="dropdown-item p-0">
                        <div class="form-check p-0">
                            <label class="form-check-label w-100 py-1 px-4">
                                <input name="enabledWidgets" class="form-check-input fs-md mx-0" value="13"
                                       type="checkbox">
                                <input type="hidden" name="_enabledWidgets">
                                <span class="ml-4">Metadata Search</span>
                            </label>
                        </div>
                    </div>
                    <div class="dropdown-item p-0">
                        <div class="form-check p-0">
                            <label class="form-check-label w-100 py-1 px-4">
                                <input name="enabledWidgets" class="form-check-input fs-md mx-0" value="13"
                                       type="checkbox">
                                <input type="hidden" name="_enabledWidgets">
                                <span class="ml-4">Metadata Search</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <footer id="legal" class="footer-main">
        <div class="footer-content footer-dark d-flex justify-content-between align-items-center w-100">
            <b class="mr-auto">
                &copy; 2006-2018 Messagepoint Inc. All rights reserved.
                <a class="ml-3" href="#" target="_blank" rel="noopener">Privacy</a>
            </b>
            <b class="ml-3">_v18.1.1.000</b>
            <span id="developerMode" class="d-inline-block h-100 ml-4" aria-hidden="true" data-toggle="tooltip"
                  title="dev_mode"><span class="developer-mode box-shadow"><i
                    class="far fa-user-secret"></i></span></span>
        </div>
    </footer>
</div>
<div id="errorModal" class="modal modal-prompt fade" tabindex="-1" role="dialog" aria-labelledby="errorModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-ribbon bg-danger position-absolute rounded-top py-1 w-100" role="presentation"></div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-auto fs-xs pr-0">
                        <i class="fas fa-minus-circle fa-3x text-danger" aria-hidden="true"></i>
                    </div>
                    <div class="col">
                        <h1 class="h3" id="errorModalLabel">Error</h1>
                        <p class="fs-md">
                            Uploaded file <strong>"eicarcom2.zip"</strong> could not be accepted.
                        </p>
                        <div class="text-left bg-lightest rounded py-2">
                            <div class="px-3 py-1">
                                <a class="d-block text-decoration-none text-muted fs-xs collapse-toggle collapsed"
                                   data-toggle="collapse" href="#errorDetailsCollapse" role="button"
                                   aria-expanded="false" aria-controls="errorDetailsCollapse">
                                    <span class="text-uppercase font-weight-bold">
                                        details
                                    </span>
                                </a>
                                <div id="errorDetailsCollapse" class="collapse">
                                    <p class="mt-1 mb-0">
                                        Anti-virus scan tested positive for <strong>"Win.Test.EICAR_HBD-1"</strong>.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <button type="button" class="btn btn-danger text-uppercase px-4" data-dismiss="modal">
                                Dismiss
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script>
    // Dummy texts needed
    var client_messages = {text: {}};
    client_messages.text.more = "More";
    client_messages.text.modules = "Modules";
</script>

<script src="lib/handlebars/handlebars-v4.0.11.js"></script>
<script src="../includes/node_modules/underscore/underscore-min.js"></script>
<script src="lib/jquery-3.3.1/jquery-3.3.1.min.js"></script>
<script src="../includes/javascript/jQueryPlugins/jquery-ui-1.12.1.custom/jquery-ui.min.js"></script>
<script src="../includes/node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../includes/node_modules/tiny-slider/dist/min/tiny-slider.js"></script>
<script src="lib/datedropper/datedropper.pro.min.js"></script>
<script src="../includes/javascript/mp.common.js"></script>
<script src="js/mp.postTrigger.js"></script>
<script src="js/mp.complexDropdown.js"></script>
<script src="js/mp.priorityMainNavigation.js"></script>
<script src="js/mp.stickyBar.js"></script>
<script src="js/mp.iFrameModal.js"></script>
<script src="js/mp.globalSearch.js"></script>
<script src="js/mp.main.js"></script>
<script>
    $(document).ready(function () {

        $('.test-checkbox').prop('indeterminate', true);

        $('#errorModal').modal({
            show: true,
            keyboard: false,
            backdrop: 'static'
        });

    });
</script>

</body>
</html>