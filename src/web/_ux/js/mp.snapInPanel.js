(function ($, window, document) {

    $.snapInPanel = function (el, options) {

        var constants = {
            POPUP_PANEL_TEMPLATE: '<div class="box-shadow-4 rounded m-4 position-absolute z-sticky">' +
                '                <div class="bg-secondary position-absolute rounded-top pt-1 w-100" role="presentation"></div>' +
                '                <div class="box-shadow-3 bg-white rounded pt-1">' +
                '                    <h5 class="d-flex align-items-stretch mb-0 py-3 px-4 cursor-draggable">' +
                '                        <span class="text-uppercase align-self-center fs-sm mr-auto">' +
                '                            <span class="mr-2 opacity-65">' +
                '                                <i class="far fa-ellipsis-v" aria-hidden="true"></i>' +
                '                                <i class="far fa-ellipsis-v" aria-hidden="true"></i>' +
                '                            </span>' +
                '                            <span class="snapin-title"></span>' +
                '                        </span>' +
                '                        <div class="mr-1 ml-3" data-toggle="tooltip" title="' + client_messages.text.compress + '">' +
                '                            <button class="compress-toggle btn btn-blank btn-icon py-0 px-2 border-0 h-100" ' +
                '                               aria-label="' + client_messages.text.compress + '" data-toggle="collapse" ' +
                '                               aria-expanded="false" type="button">' +
                '                               <i class="far fs-md text-dark fa-minus" aria-hidden="true"></i>' +
                '                           </button>' +
                '                        </div>' +
                '                        <button class="snapoff-btn btn btn-blank btn-icon py-0 px-2 border-0 mr-n2" ' +
                '                           aria-label="' + client_messages.text.snap_in + '" data-toggle="tooltip" title="' + client_messages.text.snap_in + '" type="button">' +
                '                           <i class="far fa-long-arrow-alt-down fa-rotate-45 fs-md text-dark" aria-hidden="true"></i>' +
                '                        </button>' +
                '                    </h5>' +
                '                    <div class="collapse show">' +
                '                       <div class="px-4 py-3 border-top overflow-auto" style="max-height: 17rem">' +
                '                           <div class="snapin-content">' +
                '                           </div>' +
                '                       </div>' +
                '                    </div>' +
                '                </div>' +
                '            </div>',
            ANIMATION_END: 'webkitAnimationEnd oanimationend msAnimationEnd animationend'
        };

        var base = this,
            localStorageKey,
            debounceDelay = 200;

        base.$el = $(el);
        base.el = el;
        base.initialized = !1;

        base.$el.data('snapInPanel', base);

        base.init = function () {

            base.options = $.extend({}, $.snapInPanel.defaultOptions, options);

            if (!base.options.panelId) {

                alert('snapInPanel: "panelId" required.');
                return;

            }

            if (!base.options.controlTrigger) {

                alert('snapInPanel: "controlTrigger" required.');
                return;

            }

            setupPopup();
            base.addListeners();

            if (base.options.autoSnap) {

                if (!base.options.persistedPosition || (base.options.persistedPosition && getPopupPersistedPosition() !== null))
                    base.snapInPopup();

            }

        };

        base.addListeners = function () {

            $(base.options.controlTrigger).on('click', function (e) {

                e.preventDefault();

                base.snapInPopup();

            });

            base.$popup.on("dragstop", function (event, ui) {

                setPopupPersistedPosition(ui.offset);

            });

            base.$popup.find('.snapoff-btn').on('click', function () {

                $(this).tooltip('hide');

                base.snapOffPopup();

            });

            base.$popup.find('.compress-toggle').on('click', function () {

                var $this = $(this);

                _.defer(function () {

                    if ($this.is('.collapsed')) {

                        $this
                            .parent()
                            .tooltip('hide')
                            .attr({
                                'aria-label': client_messages.text.expand,
                                'data-original-title': client_messages.text.expand
                            });

                        $this
                            .children()
                            .removeClass('fa-minus')
                            .addClass('fa-plus');

                    } else {

                        $this
                            .parent()
                            .tooltip('hide')
                            .attr({
                                'aria-label': client_messages.text.compress,
                                'data-original-title': client_messages.text.compress
                            });

                        $this
                            .children()
                            .addClass('fa-minus')
                            .removeClass('fa-plus');

                    }

                });

            });

            base.$popup.find('.collapse').on('shown.bs.collapse hidden.bs.collapse', function () {

                base.updatePopupPosition();

            });

            $(window).on('resize', _.debounce(function () {

                base.updatePopupPosition();

            }, debounceDelay));

        };

        base.snapInPopup = function () {

            if (base.options.beforeSnapIn !== null)
                base.options.beforeSnapIn();

            base.$popupContent.append(base.$el);

            $(base.options.panelContainer).append(base.$popup);

            var popupPosition = getPopupPosition();

            base.$popup
                .width(base.$popup.outerWidth())
                .one(constants.ANIMATION_END, function () {

                    $(this).removeClass('anim-growExaggerated-enter');

                    if (base.options.afterSnapIn !== null)
                        base.options.afterSnapIn();

                })
                .addClass('anim-growExaggerated-enter')
                .css(popupPosition);

            base.updatePopupPosition();

        };

        base.snapOffPopup = function () {

            if (base.options.beforeSnapOff !== null)
                base.options.beforeSnapOff();

            base.$popup.one(constants.ANIMATION_END, function () {

                base.$initialPanelContainer.append(base.$el);

                $(this)
                    .removeClass('anim-shrinkSubtle-exit')
                    .detach();

                if (base.options.afterSnapOff !== null)
                    base.options.afterSnapOff();

            }).addClass('anim-shrinkSubtle-exit');

            localStorage.removeItem(localStorageKey);

        };

        base.updatePopupPosition = function () {

            var $containment = $(base.$popup.draggable("option", "containment")),
                containmentPosition = $containment.offset(),
                isOut = isPopupOut(base.$popup.offset());

            if (isOut.top)
                base.$popup.css('top', containmentPosition.top);

            else if (isOut.bottom)
                base.$popup.css('top', containmentPosition.top + $containment.outerHeight() - base.$popup.outerHeight(!0));

            if (isOut.left)
                base.$popup.css('left', containmentPosition.left);

            else if (isOut.right)
                base.$popup.css('left', containmentPosition.left + $containment.outerWidth() - base.$popup.outerWidth(!0));

            setPopupPersistedPosition({top: parseFloat(base.$popup.css('top')), left: parseFloat(base.$popup.css('left'))});

        };

        function setupPopup() {

            if (!base.initialized) {

                var popupId = base.options.panelId + "_snapInPanel",
                    collapsiblePanelId = base.options.panelId + '_collapse';

                localStorageKey = 'msgpt_' + popupId;

                base.$popup = $(constants.POPUP_PANEL_TEMPLATE).attr('id', popupId);
                base.$popupContent = base.$popup.find('.snapin-content');
                base.$popup.find('.snapin-title').html(base.options.panelTitle);
                base.$popup.find('.collapse').attr('id', collapsiblePanelId);
                base.$popup.find('.compress-toggle').attr({
                    'aria-controls': collapsiblePanelId,
                    'data-target': '#' + collapsiblePanelId
                });

                base.$popup.draggable({
                    containment: base.options.panelContainer,
                    scroll: base.options.scroll,
                    handle: base.options.panelHandle
                });

                base.$popup.find('[data-toggle="tooltip"]').tooltip({
                    container: base.$popup
                });

                base.$initialPanelContainer = base.$el.parent();
                base.initialized = !0;

            }

        }

        function getPopupPosition() {

            var popPosition = {
                top: base.$initialPanelContainer.offset().top,
                left: base.$initialPanelContainer.offset().left
            };

            if (base.options.persistedPosition)
                return getPopupPersistedPosition() || popPosition;

            return popPosition;

        }

        function isPopupOut(popupPosition) {

            var $containment = $(base.$popup.draggable("option", "containment")),
                containmentPosition = $containment.offset(),
                result = {
                    top: !1,
                    right: !1,
                    bottom: !1,
                    left: !1,
                    any: !1
                };

            if (($containment.outerWidth() - (containmentPosition.left + base.$popup.outerWidth() + parseFloat(base.$popup.css('margin-right')) + popupPosition.left)) < 0)
                result.right = !0;

            else if (containmentPosition.left > (popupPosition.left - parseFloat(base.$popup.css('margin-left'))))
                result.left = !0;

            if (($containment.outerHeight() - (containmentPosition.top + base.$popup.outerHeight() + parseFloat(base.$popup.css('margin-bottom')) + popupPosition.top)) < 0)
                result.bottom = !0;

            else if (containmentPosition.top > (popupPosition.top - parseFloat(base.$popup.css('margin-top'))))
                result.top = !0;

            result.any = result.top || result.right || result.bottom || result.left;

            return result;

        }

        function getPopupPersistedPosition() {

            return JSON.parse(localStorage.getItem(localStorageKey));

        }

        function setPopupPersistedPosition(position) {

            if (base.options.persistedPosition)
                localStorage.setItem(localStorageKey, JSON.stringify(position));

        }

        base.init();

    };

    $.snapInPanel.defaultOptions = {
        panelId: null,
        panelContainer: '#content',
        panelHandle: 'h5',
        scroll: !1,
        controlTrigger: null,
        autoSnap: !0,
        persistedPosition: !0,
        beforeSnapIn: null,
        afterSnapIn: null,
        beforeSnapOff: null,
        afterSnapOff: null
    };

    $.fn.snapInPanel = function (options) {

        return this.each(function () {

            var config = $.extend({}, options, {});

            (new $.snapInPanel(this, config));

        });

    };

})(window.jQuery, window, document);