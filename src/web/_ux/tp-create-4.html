<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="Messagepoint Inc.">
    <title>Messagepoint</title>
    <!-- Favicon -->
    <!-- Styles -->
    <style>

        .scale-up-hover-items {
            position: relative;
            z-index: 1;
            pointer-events: none;
        }

        .scale-up-hover-items > * {
            position: relative;
            cursor: pointer;
            pointer-events: auto;
            will-change: transform, margin;
            transition: .2s transform, .2s margin;
        }

        .scale-up-hover-items:hover > *:hover {
            z-index: 1;
            margin-left: .7rem;
            margin-right: .7rem;
            transform: scale(1.1);
        }

    </style>
    <link rel="stylesheet" href="../includes/javascript/jQueryPlugins/jquery-ui-1.12.1.custom/jquery-ui.min.css">
    <link rel="stylesheet" href="scss/themes/messagepoint/styles.css">
    <script src="../includes/javascript/mp.common.js"></script>
</head>
<body class="override-default-sizing">
<a href="#content" class="sr-only sr-only-focusable h6 mx-1">Skip to main content</a>
<div id="page">
    <header class="box-shadow">
        <nav class="navbar navbar-expand navbar-dark" aria-label="Main navigation">
            <div class="nav-item">
                <a class="navbar-brand nav-link" href="#" data-toggle="tooltip" title="←&nbsp; Back to home page">
                    <img src="img/brands/messagepoint-logo-dark-theme.svg" class="d-inline-block align-middle"
                         alt="Header Logo">
                </a>
            </div>
            <div class="nav-container navbar-collapse flex-row">
                <ul id="main-navigation" class="nav-main navbar-nav" aria-label="Primary navigation">
                    <li class="nav-item dropdown active">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown01" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Touchpoint
                            <span class="sr-only">(current)</span>
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown01">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Touchpoints</a>
                            <a class="dropdown-item" href="#">Touchpoint Collections</a>
                            <a class="dropdown-item" href="#">Output Tags</a>
                            <a class="dropdown-item" href="#">Delivery Events</a>
                            <div class="dropdown-divider" role="separator"></div>
                            <h6 class="dropdown-header">New</h6>
                            <a class="dropdown-item" data-toggle="modal" href="#tpCreateModal">Touchpoint...</a>
                            <a class="dropdown-item" href="#">Import...</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown02" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Task
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown02">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Tasks</a>
                            <a class="dropdown-item" href="#">Projects</a>
                            <a class="dropdown-item" href="#">System Tasks</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown03" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Target
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown03">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Target Groups</a>
                            <a class="dropdown-item" href="#">Target Rules</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown04" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Content
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown04">
                            <h6 class="dropdown-header">Styles</h6>
                            <a class="dropdown-item" href="#">Texts</a>
                            <a class="dropdown-item" href="#">Paragraph</a>
                            <a class="dropdown-item" href="#">List</a>
                            <div class="dropdown-divider" role="separator"></div>
                            <h6 class="dropdown-header">Content</h6>
                            <a class="dropdown-item" href="#">Smart Text</a>
                            <a class="dropdown-item" href="#">Smart Canvas</a>
                            <a class="dropdown-item" href="#">Image Library</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdown05" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false">
                            Test
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown05">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Reports</a>
                            <a class="dropdown-item" href="#">Simulations</a>
                            <a class="dropdown-item" href="#">Connected</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a id="dropdown07a" class="nav-link dropdown-toggle" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Insert
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown07a">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Inserts</a>
                            <a class="dropdown-item" href="#">Insert Schedules</a>
                            <a class="dropdown-item" href="#">Insert Schedules Setup</a>
                            <a class="dropdown-item" href="#">Rate Sheets</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a id="dropdown07b" class="nav-link dropdown-toggle" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Report
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown07b">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Reports</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a id="dropdown07c" class="nav-link dropdown-toggle" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Data
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown07c">
                            <h6 class="dropdown-header">View</h6>
                            <a class="dropdown-item" href="#">Data Sources</a>
                            <a class="dropdown-item" href="#">Data Groups</a>
                            <a class="dropdown-item" href="#">Data Collections</a>
                            <a class="dropdown-item" href="#">Data Files</a>
                            <a class="dropdown-item" href="#">Data Resources</a>
                            <a class="dropdown-item" href="#">Variables</a>
                            <a class="dropdown-item" href="#">Selector Groups</a>
                            <a class="dropdown-item" href="#">External Event</a>
                            <a class="dropdown-item" href="#">Lookup Tables</a>
                            <a class="dropdown-item" href="#">Metadata Templates</a>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a id="dropdown07d" class="nav-link dropdown-toggle" href="#"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Setup
                        </a>
                        <div class="dropdown-menu dropdown-custom" aria-labelledby="dropdown07d">
                            <div class="dropdown-content">
                                <h6 class="dropdown-header">View</h6>
                                <a class="dropdown-item" href="#">Workflow Library</a>
                                <a class="dropdown-item" href="#">Master Admin: System
                                    Settings
                                </a>
                                <a class="dropdown-item" href="#">Master Admin: Domains</a>
                                <a class="dropdown-item" href="#">Master Admin:
                                    Maintenance
                                </a>
                                <a class="dropdown-item" href="#">Master Admin: Licenses</a>
                                <a class="dropdown-item" href="#">Bundle Delivery</a>
                                <a class="dropdown-item" href="#">System Settings</a>
                                <a class="dropdown-item" href="#">Security Settings</a>
                                <a class="dropdown-item" href="#">Locale Settings</a>
                                <a class="dropdown-item" href="#">License Information</a>
                                <a class="dropdown-item" href="#">Feature activation</a>
                                <a class="dropdown-item" href="#">User Interface Settings
                                </a>
                                <a class="dropdown-item" href="#">Roles</a>
                                <a class="dropdown-item" href="#">Workgroups</a>
                                <a class="dropdown-item" href="#">Users</a>
                                <a class="dropdown-item" href="#">Auditing</a>
                                <div class="dropdown-divider" role="separator"></div>
                                <h6 class="dropdown-header">New</h6>
                                <a class="dropdown-item" href="#">Workgroup</a>
                            </div>
                        </div>
                    </li>
                </ul>
                <div class="navbar-nav nav-right">
                    <div class="nav-item dropdown">
                        <a id="userInfo" class="user-info nav-link dropdown-toggle" href="#" role="button"
                           aria-label="Signed in as Wilvin Jimenez - click to toggle user menu panel"
                           data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <div id="user-avatar" class="avatar col-auto p-0" role="presentation">WJ</div>
                            <div class="col-auto pr-0">
                                <div id="user-name" class="pb-1" aria-label="User name">Wilvin Jimenez</div>
                                <div>
                                    <small id="current-branch" aria-label="Branch" class="text-uppercase">
                                        Messagepoint
                                    </small>
                                    <i class="far fa-xs fa-angle-double-right mx-1" aria-hidden="true"></i>
                                    <small id="current-instance" aria-label="Instance" class="text-uppercase">
                                        Production
                                    </small>
                                </div>
                            </div>
                        </a>
                        <div id="userPanel" class="dropdown-menu dropdown-custom w-75"
                             aria-label="User menu actions"
                             role="menu">
                            <h5 class="dropdown-header dropdown-heading">User Panel</h5>
                            <div class="dropdown-divider" role="separator"></div>
                            <h6 class="dropdown-header">Take me to...</h6>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-tachometer-alt mr-3" aria-hidden="true"></i>
                                Dashboard
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-sliders-h mr-3" aria-hidden="true"></i>
                                Settings
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-plug mr-3" aria-hidden="true"></i>
                                Connected
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-chart-bar mr-3" aria-hidden="true"></i>
                                Rationalizer
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-exchange-alt mr-3" aria-hidden="true"></i>
                                Touchpoint Exchange
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-server mr-3" aria-hidden="true"></i>
                                Job Center
                            </a>
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="icon far fa-sign-out mr-3" aria-hidden="true"></i>
                                Log out
                            </a>
                            <div class="dropdown-divider" role="separator"></div>
                            <div id="userPanelAccordion" role="group">
                                <div class="h6 mb-0" role="heading">
                                    <a class="dropdown-header dropdown-item dropdown-toggle dropdown-flexblock collapsed"
                                       data-toggle="collapse" data-target="#languages" aria-expanded="false"
                                       aria-controls="languages" tabindex="0">Language (EN)
                                    </a>
                                </div>
                                <div id="languages" class="collapse" aria-label="Available languages"
                                     data-parent="#userPanelAccordion" role="region" style="">
                                    <div role="menu" aria-labelledby="languages">
                                        <a class="dropdown-item" href="#" role="menuitem">French (CA)</a>
                                        <a class="dropdown-item active" href="#" role="menuitem">English (US)
                                            <span
                                                    class="sr-only">(current)</span>
                                        </a>
                                        <a class="dropdown-item" href="#" role="menuitem">Spanish (ES)</a>
                                    </div>
                                </div>
                                <div class="dropdown-divider" role="presentation"></div>
                                <div class="h6 mb-0" role="heading">
                                    <a class="dropdown-header dropdown-item dropdown-toggle dropdown-flexblock"
                                       data-toggle="collapse"
                                       data-target="#instances" aria-expanded="false" aria-controls="instances"
                                       tabindex="0">Instance (Production)
                                    </a>
                                </div>
                                <div id="instances" class="collapse" aria-label="Instances"
                                     data-parent="#userPanelAccordion" role="region">
                                    <div role="menu" aria-labelledby="instances">
                                        <a class="dropdown-item" href="#">Auto</a>
                                        <a class="dropdown-item" href="#">Test</a>
                                        <a class="dropdown-item active" href="#">
                                            Production
                                            <b role="presentation">&nbsp;•</b>
                                            <span class="sr-only">(current)</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <main id="content">
    </main>
    <footer id="legal" class="footer-main">
        <div class="footer-content footer-dark d-flex justify-content-between align-items-center w-100">
            <b class="mr-auto">
                &copy; 2006-2018 Messagepoint Inc. All rights reserved.
                <a class="ml-3" href="#" target="_blank" rel="noopener">Privacy</a>
            </b>
            <b class="ml-3">_v18.1.1.000</b>
            <span id="developerMode" class="d-inline-block h-100 ml-4" aria-hidden="true" data-toggle="tooltip"
                  title="dev_mode"><span class="developer-mode box-shadow"><i
                    class="far fa-user-secret"></i></span></span>
        </div>
    </footer>
</div>
<div id="tpCreateModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="tpCreateModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-ribbon bg-ribbon position-absolute rounded-top py-1 w-100" role="presentation"></div>
            <div class="modal-header">
                <h4 class="modal-title" id="tpCreateModalLabel">Create Touchpoint</h4>
                <button type="button" class="close position-relative" data-dismiss="modal">
                    <i class="far fa-times m-2" aria-hidden="true"></i>
                </button>
            </div>
            <div class="modal-body p-0">
                <div class="px-3">
                    <div class="px-3 pb-4">
                        <div class="d-flex">
                            <div class="ml-n2">
                                <span class="fa-container-shadow fa-container-shadow-secondary fs-md">
                                    <i class="fad fad fa-mail-bulk fa-swap-opacity" aria-hidden="true"></i>
                                </span>
                            </div>
                            <p class="ml-4">
                                A Touchpoint is a method of communication from your company to any recipient. This can
                                be as simple as a letter, an e-mail, a text message or a website. Fill the form below to
                                start working on your new Touchpoint!
                            </p>
                        </div>
                        <div class="form-group">
                            <label for="field1">Name</label>
                            <input type="text" class="form-control" id="field1">
                        </div>
                        <fieldset class="mb-4">
                            <legend class="form-label">Channel</legend>
                            <div class="d-flex btn-group-toggle mx-n2" data-toggle="buttons">
                                <label class="flex-fill btn btn-blank btn-check text-darkest mx-2">
                                    <input type="radio" name="channelId" id="channel2" value="2">
                                    <i class="far fa-file-alt mr-2" aria-hidden="true"></i>Print
                                </label>
                                <label class="flex-fill btn btn-blank btn-check text-darkest mx-2">
                                    <input type="radio" name="channelId" id="channel3" value="3">
                                    <i class="far fa-browser mr-2" aria-hidden="true"></i>Web
                                </label>
                                <label class="flex-fill btn btn-blank btn-check text-darkest mx-2 active">
                                    <input type="radio" name="channelId" id="channel4" value="4" checked>
                                    <i class="far fa-at mr-2" aria-hidden="true"></i>Email
                                </label>
                                <label class="flex-fill btn btn-blank btn-check text-darkest mx-2">
                                    <input type="radio" name="channelId" id="channel5" value="5">
                                    <i class="far fa-comment-lines mr-2" aria-hidden="true"></i>SMS
                                </label>
                            </div>
                        </fieldset>
                        <div id="connectorFormGroup">
                            <fieldset class="slider-container">
                                <div class="d-flex justify-content-between">
                                    <legend class="form-label mb-1 mr-2">Connector</legend>
                                    <div id="sliderButtonsConnector2"
                                         class="slider-buttons btn-group anim-transition-opacity d-none"
                                         data-channel="2">
                                        <button type="button" class="btn btn-blank btn-icon p-1 fs-xs"
                                                aria-label="Previous">
                                            <i class="fas fa-arrow-left fa-sm fa-fw text-body" aria-hidden="true"></i>
                                        </button>
                                        <button type="button" class="btn btn-blank btn-icon p-1 fs-xs"
                                                aria-label="Next">
                                            <i class="fas fa-arrow-right fa-sm fa-fw text-body"
                                               aria-hidden="true"></i>
                                        </button>
                                    </div>
                                    <div id="sliderButtonsConnector3"
                                         class="slider-buttons btn-group anim-transition-opacity d-none"
                                         data-channel="3">
                                        <button type="button" class="btn btn-blank btn-icon p-1 fs-xs"
                                                aria-label="Previous">
                                            <i class="fas fa-arrow-left fa-sm fa-fw text-body" aria-hidden="true"></i>
                                        </button>
                                        <button type="button" class="btn btn-blank btn-icon p-1 fs-xs"
                                                aria-label="Next">
                                            <i class="fas fa-arrow-right fa-sm fa-fw text-body"
                                               aria-hidden="true"></i>
                                        </button>
                                    </div>
                                    <div id="sliderButtonsConnector4"
                                         class="slider-buttons btn-group anim-transition-opacity" data-channel="4">
                                        <button type="button" class="btn btn-blank btn-icon p-1 fs-xs"
                                                aria-label="Previous">
                                            <i class="fas fa-arrow-left fa-sm fa-fw text-body" aria-hidden="true"></i>
                                        </button>
                                        <button type="button" class="btn btn-blank btn-icon p-1 fs-xs"
                                                aria-label="Next">
                                            <i class="fas fa-arrow-right fa-sm fa-fw text-body"
                                               aria-hidden="true"></i>
                                        </button>
                                    </div>
                                    <div id="sliderButtonsConnector5"
                                         class="slider-buttons btn-group anim-transition-opacity d-none"
                                         data-channel="5">
                                        <button type="button" class="btn btn-blank btn-icon p-1 fs-xs"
                                                aria-label="Previous">
                                            <i class="fas fa-arrow-left fa-sm fa-fw text-body" aria-hidden="true"></i>
                                        </button>
                                        <button type="button" class="btn btn-blank btn-icon p-1 fs-xs"
                                                aria-label="Next">
                                            <i class="fas fa-arrow-right fa-sm fa-fw text-body"
                                               aria-hidden="true"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="mt-1 mx-n3">
                                    <div class="mx-n3 mt-n3">
                                        <div id="sliderConnector2"
                                             class="slider align-items-stretch scale-up-hover-items cursor-draggable pt-3 pb-4 d-none"
                                             data-toggle="buttons" data-channel="2">
                                            <div class="btn-group-toggle flex-shrink-0">
                                                <label class="btn btn-block btn-lg btn-check border-light box-shadow-1 text-darkest d-flex align-items-center justify-content-center h-100">
                                                    <input type="radio" name="connectorId" id="connector3" value="3"
                                                           data-channel="2">
                                                    <img src="img/connectors/connector-3.svg" alt="OpenText"
                                                         class="m-1">
                                                </label>
                                            </div>
                                            <div class="btn-group-toggle flex-shrink-0">
                                                <label class="btn btn-block btn-lg btn-check border-light box-shadow-1 text-darkest d-flex align-items-center justify-content-center h-100">
                                                    <input type="radio" name="connectorId" id="connector9" value="9"
                                                           data-channel="2">
                                                    <img src="img/connectors/connector-9.svg" alt="Quadient"
                                                         class="m-1">
                                                </label>
                                            </div>
                                            <div class="btn-group-toggle flex-shrink-0">
                                                <label class="btn btn-block btn-lg btn-check border-light box-shadow-1 text-darkest d-flex align-items-center justify-content-center h-100">
                                                    <input type="radio" name="connectorId" id="connector16" value="16"
                                                           data-channel="2">
                                                    <img src="img/connectors/connector-16.svg"
                                                         alt="Messagepoint Composition" class="m-1">
                                                </label>
                                            </div>
                                        </div>
                                        <div id="sliderConnector3"
                                             class="slider align-items-stretch scale-up-hover-items cursor-draggable pt-3 pb-4 d-none"
                                             data-toggle="buttons" data-channel="3">
                                            <div class="btn-group-toggle flex-shrink-0">
                                                <label class="btn btn-block btn-lg btn-check border-light box-shadow-1 text-darkest d-flex align-items-center justify-content-center h-100">
                                                    <input type="radio" name="connectorId" id="connector15" value="15"
                                                           data-channel="3">
                                                    <img src="img/connectors/connector-15.svg" alt="FTP" class="m-1">
                                                </label>
                                            </div>
                                        </div>
                                        <div id="sliderConnector4"
                                             class="slider d-flex align-items-stretch scale-up-hover-items cursor-draggable pt-3 pb-4 anim-slideLeft-enter"
                                             data-toggle="buttons" data-channel="4">
                                            <div class="btn-group-toggle flex-shrink-0">
                                                <label class="btn btn-block btn-lg btn-check border-light box-shadow-1 text-darkest d-flex align-items-center justify-content-center h-100 active">
                                                    <input type="radio" name="connectorId" id="connector12" value="12"
                                                           data-channel="4" checked>
                                                    <img src="img/connectors/connector-12.svg" alt="Sendmail"
                                                         class="m-1">
                                                </label>
                                            </div>
                                            <div class="btn-group-toggle flex-shrink-0">
                                                <label class="btn btn-block btn-lg btn-check border-light box-shadow-1 text-darkest d-flex align-items-center justify-content-center h-100">
                                                    <input type="radio" name="connectorId" id="connector13" value="13"
                                                           data-channel="4">
                                                    <img src="img/connectors/connector-13.svg"
                                                         alt="Salesforce Marketing Cloud" class="m-1">
                                                </label>
                                            </div>
                                            <div class="btn-group-toggle flex-shrink-0">
                                                <label class="btn btn-block btn-lg btn-check border-light box-shadow-1 text-darkest d-flex align-items-center justify-content-center h-100">
                                                    <input type="radio" name="connectorId" id="connector17" value="17"
                                                           data-channel="4">
                                                    <img src="img/connectors/connector-17.svg"
                                                         alt="Salesforce Journey Builder" class="m-1">
                                                </label>
                                            </div>
                                            <div class="btn-group-toggle flex-shrink-0">
                                                <label class="btn btn-block btn-lg btn-check border-light box-shadow-1 text-darkest d-flex align-items-center justify-content-center h-100">
                                                    <input type="radio" name="connectorId" id="connector00" value="00"
                                                           data-channel="4">
                                                    <img src="img/connectors/connector-00.svg" alt="SparkPost"
                                                         class="m-1">
                                                </label>
                                            </div>
                                        </div>
                                        <div id="sliderConnector5"
                                             class="slider align-items-stretch scale-up-hover-items cursor-draggable pt-3 pb-4 d-none"
                                             data-toggle="buttons" data-channel="5">
                                            <div class="btn-group-toggle flex-shrink-0">
                                                <label class="btn btn-block btn-lg btn-check border-light box-shadow-1 text-darkest d-flex align-items-center justify-content-center h-100">
                                                    <input type="radio" name="connectorId" id="connector14" value="14"
                                                           data-channel="5">
                                                    <img src="img/connectors/connector-14.svg" alt="Clickatell"
                                                         class="m-1">
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="languageSelect">Default language</label>
                                <select id="languageSelect" name="defaultLanguageId"
                                        class="complex-dropdown-select"
                                        data-enablefilter="true"
                                        data-dropdown-class="btn-blank btn-block border-light text-darkest dropdown-flexblock">
                                    <option id="languageOption_25966" value="25966" data-favourite="true" selected>
                                        English &nbsp;(EN)
                                    </option>
                                    <option id="languageOption_24934" value="24934" data-favourite="false">
                                        Afrikaans &nbsp;(AF)
                                    </option>
                                    <option id="languageOption_29553" value="29553" data-favourite="false">
                                        Albanian &nbsp;(SQ)
                                    </option>
                                    <option id="languageOption_24946" value="24946" data-favourite="false">
                                        Arabic &nbsp;(AR)
                                    </option>
                                    <option id="languageOption_25973" value="25973" data-favourite="false">
                                        Basque &nbsp;(EU)
                                    </option>
                                    <option id="languageOption_25203" value="25203" data-favourite="false">
                                        Bosnian &nbsp;(BS)
                                    </option>
                                    <option id="languageOption_25202" value="25202" data-favourite="false">
                                        Breton &nbsp;(BR)
                                    </option>
                                    <option id="languageOption_26731" value="26731" data-favourite="false">
                                        Cantonese &nbsp;(HK)
                                    </option>
                                    <option id="languageOption_25441" value="25441" data-favourite="false">
                                        Catalan &nbsp;(CA)
                                    </option>
                                    <option id="languageOption_31336" value="31336" data-favourite="false">
                                        Chinese &nbsp;(ZH)
                                    </option>
                                    <option id="languageOption_26738" value="26738" data-favourite="false">
                                        Croatian &nbsp;(HR)
                                    </option>
                                    <option id="languageOption_25459" value="25459" data-favourite="false">
                                        Czech &nbsp;(CS)
                                    </option>
                                    <option id="languageOption_25697" value="25697" data-favourite="false">
                                        Danish &nbsp;(DA)
                                    </option>
                                    <option id="languageOption_28268" value="28268" data-favourite="false">
                                        Dutch &nbsp;(NL)
                                    </option>
                                    <option id="languageOption_25972" value="25972" data-favourite="false">
                                        Estonian &nbsp;(ET)
                                    </option>
                                    <option id="languageOption_26223" value="26223" data-favourite="false">
                                        Faroese &nbsp;(FO)
                                    </option>
                                    <option id="languageOption_26217" value="26217" data-favourite="false">
                                        Finnish &nbsp;(FI)
                                    </option>
                                    <option id="languageOption_26226" value="26226" data-favourite="true">
                                        French &nbsp;(FR)
                                    </option>
                                    <option id="languageOption_26476" value="26476" data-favourite="false">
                                        Galician &nbsp;(GL)
                                    </option>
                                    <option id="languageOption_25701" value="25701" data-favourite="true">
                                        German &nbsp;(DE)
                                    </option>
                                    <option id="languageOption_26725" value="26725" data-favourite="false">
                                        Hebrew &nbsp;(HE)
                                    </option>
                                    <option id="languageOption_26729" value="26729" data-favourite="false">
                                        Hindi &nbsp;(HI)
                                    </option>
                                    <option id="languageOption_26741" value="26741" data-favourite="false">
                                        Hungarian &nbsp;(HU)
                                    </option>
                                    <option id="languageOption_26995" value="26995" data-favourite="false">
                                        Icelandic &nbsp;(IS)
                                    </option>
                                    <option id="languageOption_26988" value="26988" data-favourite="false">
                                        Ilokano &nbsp;(IL)
                                    </option>
                                    <option id="languageOption_26980" value="26980" data-favourite="false">
                                        Indonesian &nbsp;(ID)
                                    </option>
                                    <option id="languageOption_26465" value="26465" data-favourite="false">
                                        Irish &nbsp;(GA)
                                    </option>
                                    <option id="languageOption_26996" value="26996" data-favourite="true">
                                        Italian &nbsp;(IT)
                                    </option>
                                    <option id="languageOption_27233" value="27233" data-favourite="false">
                                        Japanese &nbsp;(JA)
                                    </option>
                                    <option id="languageOption_27503" value="27503" data-favourite="false">
                                        Korean &nbsp;(KO)
                                    </option>
                                    <option id="languageOption_27746" value="27746" data-favourite="false">
                                        Luxembourgish &nbsp;(LB)
                                    </option>
                                    <option id="languageOption_28019" value="28019" data-favourite="false">
                                        Malay &nbsp;(MS)
                                    </option>
                                    <option id="languageOption_28271" value="28271" data-favourite="false">
                                        Norwegian &nbsp;(NO)
                                    </option>
                                    <option id="languageOption_28780" value="28780" data-favourite="false">
                                        Polish &nbsp;(PL)
                                    </option>
                                    <option id="languageOption_28788" value="28788" data-favourite="false">
                                        Portuguese &nbsp;(PT)
                                    </option>
                                    <option id="languageOption_28769" value="28769" data-favourite="false">
                                        Punjabi &nbsp;(PA)
                                    </option>
                                    <option id="languageOption_29295" value="29295" data-favourite="false">
                                        Romanian &nbsp;(RO)
                                    </option>
                                    <option id="languageOption_29301" value="29301" data-favourite="false">
                                        Russian &nbsp;(RU)
                                    </option>
                                    <option id="languageOption_26468" value="26468" data-favourite="false">
                                        Scottish Gaelic &nbsp;(GD)
                                    </option>
                                    <option id="languageOption_29554" value="29554" data-favourite="false">
                                        Serbian &nbsp;(SR)
                                    </option>
                                    <option id="languageOption_29547" value="29547" data-favourite="false">
                                        Slovak &nbsp;(SK)
                                    </option>
                                    <option id="languageOption_29548" value="29548" data-favourite="false">
                                        Slovenian &nbsp;(SL)
                                    </option>
                                    <option id="languageOption_25971" value="25971" data-favourite="true">
                                        Spanish &nbsp;(ES)
                                    </option>
                                    <option id="languageOption_29559" value="29559" data-favourite="false">
                                        Swahili &nbsp;(SW)
                                    </option>
                                    <option id="languageOption_29558" value="29558" data-favourite="false">
                                        Swedish &nbsp;(SV)
                                    </option>
                                    <option id="languageOption_29793" value="29793" data-favourite="false">
                                        Tamil &nbsp;(TA)
                                    </option>
                                    <option id="languageOption_29800" value="29800" data-favourite="false">
                                        Thai &nbsp;(TH)
                                    </option>
                                    <option id="languageOption_30059" value="30059" data-favourite="false">
                                        Ukrainian &nbsp;(UK)
                                    </option>
                                    <option id="languageOption_30313" value="30313" data-favourite="false">
                                        Vietnamese &nbsp;(VI)
                                    </option>
                                    <option id="languageOption_30561" value="30561" data-favourite="false">
                                        Walloon &nbsp;(WA)
                                    </option>
                                </select>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="localeSelect">Locale</label>
                                <select id="localeSelect" name="defaultLocaleId"
                                        class="complex-dropdown-select" data-enablefilter="true"
                                        data-menu-class="dropdown-menu-icon"
                                        data-dropdown-class="btn-blank btn-block border-light text-darkest dropdown-flexblock">
                                    <option id="localeOption_4" value="4" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-au flag-icon-lg mr-2">
                                        Australia &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_5" value="5" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-bz flag-icon-lg mr-2">
                                        Belize &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_2" value="2" data-language="25966" data-favourite="true"
                                            data-icon="flag-icon flag-icon-ca flag-icon-lg mr-2" selected>
                                        Canada &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_6" value="6" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-bs flag-icon-lg mr-2">
                                        Caribbean &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_7" value="7" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-in flag-icon-lg mr-2">
                                        India &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_8" value="8" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-ie flag-icon-lg mr-2">
                                        Ireland &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_9" value="9" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-jm flag-icon-lg mr-2">
                                        Jamaica &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_10" value="10" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-my flag-icon-lg mr-2">
                                        Malaysia &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_11" value="11" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-nz flag-icon-lg mr-2">
                                        New Zealand &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_12" value="12" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-ph flag-icon-lg mr-2">
                                        Philippines &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_13" value="13" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-sg flag-icon-lg mr-2">
                                        Singapore &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_14" value="14" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-za flag-icon-lg mr-2">
                                        South Africa &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_15" value="15" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-tt flag-icon-lg mr-2">
                                        Trinidad and Tobago &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_3" value="3" data-language="25966" data-favourite="true"
                                            data-icon="flag-icon flag-icon-gb flag-icon-lg mr-2">
                                        UK &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_1" value="1" data-language="25966" data-favourite="true"
                                            data-icon="flag-icon flag-icon-us flag-icon-lg mr-2">
                                        US &nbsp;(EN)
                                    </option>
                                    <option id="localeOption_16" value="16" data-language="25966" data-favourite="false"
                                            data-icon="flag-icon flag-icon-zw flag-icon-lg mr-2">
                                        Zimbabwe &nbsp;(EN)
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-end p-3 bg-lightest rounded-bottom">
                    <div class="px-3">
                        <button type="button" class="btn btn-lightest" data-dismiss="modal">
                            <i class="far fa-times-circle mr-2" aria-hidden="true"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-primary post-trigger ml-2">
                            <i class="fas fa-save mr-2" aria-hidden="true"></i>Save
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Scripts -->

<script>
    // Dummy texts needed
    var client_messages = {text: {}};
    client_messages.text.more = "More";
    client_messages.text.none = "None";
    client_messages.text.modules = "Modules";
</script>

<script src="lib/handlebars/handlebars-v4.0.11.js"></script>
<script src="../includes/node_modules/underscore/underscore-min.js"></script>
<script src="lib/jquery-3.3.1/jquery-3.3.1.min.js"></script>
<script src="../includes/javascript/jQueryPlugins/jquery-ui-1.12.1.custom/jquery-ui.min.js"></script>
<script src="../includes/node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../includes/node_modules/tiny-slider/dist/min/tiny-slider.js"></script>
<script src="lib/datedropper/datedropper.pro.min.js"></script>
<script src="../includes/javascript/mp.common.js"></script>
<script src="js/mp.postTrigger.js"></script>
<script src="js/mp.complexDropdown.js"></script>
<script src="js/mp.priorityMainNavigation.js"></script>
<script src="js/mp.stickyBar.js"></script>
<script src="js/mp.iFrameModal.js"></script>
<script src="js/mp.globalSearch.js"></script>
<script src="js/mp.main.js"></script>
<script>

    $(function () {

        var $connectorFormGroup = $('#connectorFormGroup'),
            $channels = $('[name="channelId"]'),
            $connectors = $('[name="connectorId"]'),
            connectorSliders = [],
            sliderOptions = {
                container: '.slider',
                controlsContainer: '.slider-buttons',
                edgePadding: 32,
                gutter: 16,
                items: 2.25,
                loop: false,
                mouseDrag: true,
                freezable: false,
                nav: false,
                speed: 400,
                onInit: function () {

                    $('.tns-ovh')
                        .off()
                        .on('scroll', function (e) {

                            e.preventDefault();
                            $(this).scrollLeft(0);

                        });

                }
            },
            $languageSelect = $('#languageSelect'),
            $localeSelect = $('#localeSelect');

        setupSliders();
        addListeners();
        setupLanguageSelectionControls();

        function setupSliders() {

            $channels.each(function () {

                var channelId = $(this).val();

                var options = $.extend({}, sliderOptions, {
                    container: '#sliderConnector' + channelId,
                    controlsContainer: '#sliderButtonsConnector' + channelId
                });

                connectorSliders.push({
                    channel: channelId,
                    connectorSlider: tns(options)
                })

            });

        }

        function addListeners() {

            $channels.on('change', function () {

                var id = $(this).val(),
                    $sliders = $connectorFormGroup.find('.slider'),
                    $slidersButtons = $connectorFormGroup.find('.slider-buttons'),
                    $targetSlider = $sliders.filter('[data-channel="' + id + '"]'),
                    $targetSlidersButtons = $slidersButtons.filter('[data-channel="' + id + '"]');

                $sliders
                    .not($targetSlider)
                    .filter(':visible')
                    .removeClass('d-flex anim-slideLeft-enter')
                    .addClass('d-none');

                $targetSlider
                    .removeClass('d-none')
                    .addClass('d-flex anim-slideLeft-enter');

                $connectors
                    .parent()
                    .removeClass('active');

                $connectors
                    .filter('[data-channel="' + id + '"]')
                    .first()
                    .prop('checked', true)
                    .trigger('change')
                    .parent()
                    .addClass('active');

                $slidersButtons
                    .not($targetSlidersButtons)
                    .filter(':visible')
                    .addClass('d-none');

                $targetSlidersButtons.removeClass('d-none');

            });

            $connectors.on('change', function () {

                var $this = $(this),
                    targetChannel = $this.data('channel') + '';

                $.each(connectorSliders, function (index, obj) {

                    var channel = obj.channel,
                        connectorSlider = obj.connectorSlider;

                    if (targetChannel === channel) {

                        var currentIndex = connectorSlider.getInfo().index,
                            newIndex = $this.closest('.tns-item').index();

                        if ((currentIndex === 0 && newIndex === $this.closest('.tns-item').siblings().length) || newIndex < currentIndex)
                            connectorSlider.goTo(newIndex);

                        else if (newIndex > currentIndex && (newIndex - currentIndex + 1) > connectorSlider.getInfo().items)
                            connectorSlider.goTo('next');

                        return false;

                    }

                });

            });

        }

        function setupLanguageSelectionControls() {

            var SHOW_ALL_VALUE = 'SHOW_ALL',
                SHOW_ALL_LANGUAGES_OPTION_DIVIDER = 'showAllLanguagesOptionDivider',
                SHOW_ALL_LANGUAGES_OPTION = 'showAllLanguagesOption',
                SHOW_ALL_LOCALE_OPTION_DIVIDER = 'showAllLocaleOptionDivider',
                SHOW_ALL_LOCALE_OPTION = 'showAllLocaleOption',
                DIVIDER_CLASS = 'divider',
                SHOW_ALL_CLASS = 'fixedOption text-capitalize';

            $languageSelect.children('[data-favourite="false"]').addClass(('d-none'));
            $languageSelect.append('<option id="' + SHOW_ALL_LANGUAGES_OPTION_DIVIDER + '" class="' + DIVIDER_CLASS + '"></option>');
            $languageSelect.append('<option id="' + SHOW_ALL_LANGUAGES_OPTION + '" class="fixedOption text-capitalize" value="' + SHOW_ALL_VALUE + '">Show all</option>');
            var languageSelectDropdown = $languageSelect.complexDropdown().data('complexDropdown');

            $localeSelect.children('[data-favourite="false"]').addClass(('d-none'));
            $localeSelect.append('<option id="' + SHOW_ALL_LOCALE_OPTION_DIVIDER + '" class="' + DIVIDER_CLASS + '"></option>');
            $localeSelect.append('<option id="' + SHOW_ALL_LOCALE_OPTION + '" class="' + SHOW_ALL_CLASS + '" value="' + SHOW_ALL_VALUE + '">Show all</option>');
            var localeSelectDropdown = $localeSelect.complexDropdown().data('complexDropdown');

            languageSelectDropdown.$control
                .find('.dropdown-item')
                .filter('[data-option-id="' + SHOW_ALL_LANGUAGES_OPTION + '"]').on('click', function () {

                return toggleFavorites(languageSelectDropdown, SHOW_ALL_LANGUAGES_OPTION_DIVIDER, SHOW_ALL_LANGUAGES_OPTION);

            });

            localeSelectDropdown.$control
                .find('.dropdown-item')
                .filter('[data-option-id="' + SHOW_ALL_LOCALE_OPTION + '"]').on('click', function () {

                return toggleFavorites(localeSelectDropdown, SHOW_ALL_LOCALE_OPTION_DIVIDER, SHOW_ALL_LOCALE_OPTION);

            });

            function toggleFavorites(dropdown, dividerId, showAllId) {

                _.defer(function () {

                    dropdown.hideOptionById(dividerId);
                    dropdown.hideOptionById(showAllId);
                    dropdown.$toggle.dropdown('toggle');

                });

                dropdown.$toggle.dropdown('hide');
                dropdown.showAllTheOptions();

                return false;

            }

        }

    });

</script>

</body>
</html>