{"_class": "MSArchiveHeader", "version": 103, "compatibilityVersion": 99, "metadata": {"commit": "4e7e2f5d7940a711b59f89190b5b7e3029f050f5", "appVersion": "50.2", "build": 55047, "app": "com.bohemiancoding.sketch3", "compatibilityVersion": 99, "version": 103, "variant": "NONAPPSTORE"}, "root": {"_class": "sharedStyle", "name": "Body4/Left/Bold/Light", "value": {"_class": "style", "endDecorationType": 0, "miterLimit": 10, "sharedObjectID": "631EC0B5-F2B1-4C6B-B5D2-D1B89CD8B8AC", "startDecorationType": 0, "textStyle": {"_class": "textStyle", "encodedAttributes": {"paragraphStyle": {"_class": "paragraphStyle", "alignment": 0, "maximumLineHeight": 18, "minimumLineHeight": 18, "allowsDefaultTighteningForTruncation": 0}, "MSAttributedStringFontAttribute": {"_class": "fontDescriptor", "attributes": {"name": "MuseoSans-700", "size": 10}}, "MSAttributedStringColorAttribute": {"_class": "color", "alpha": 1, "blue": 1, "green": 1, "red": 1}}, "verticalAlignment": 0}}}}