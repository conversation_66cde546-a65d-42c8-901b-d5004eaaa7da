{"_class": "MSArchiveHeader", "version": 103, "compatibilityVersion": 99, "metadata": {"commit": "4e7e2f5d7940a711b59f89190b5b7e3029f050f5", "appVersion": "50.2", "build": 55047, "app": "com.bohemiancoding.sketch3", "compatibilityVersion": 99, "version": 103, "variant": "NONAPPSTORE"}, "root": {"_class": "sharedStyle", "name": "Body3/Center/Regular/Uppercase/Light", "value": {"_class": "style", "endDecorationType": 0, "miterLimit": 10, "sharedObjectID": "BE4B89C8-3936-468F-ACFD-A0F70F314B2B", "startDecorationType": 0, "textStyle": {"_class": "textStyle", "encodedAttributes": {"MSAttributedStringFontAttribute": {"_class": "fontDescriptor", "attributes": {"name": "MuseoSans-500", "size": 12}}, "MSAttributedStringColorAttribute": {"_class": "color", "alpha": 1, "blue": 1, "green": 1, "red": 1}, "MSAttributedStringTextTransformAttribute": 1, "paragraphStyle": {"_class": "paragraphStyle", "alignment": 2, "maximumLineHeight": 21.60000038146973, "minimumLineHeight": 21.60000038146973, "allowsDefaultTighteningForTruncation": 0}}, "verticalAlignment": 0}}}}