<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>

    <c:set var="contentObject" value="${command.contentObject}"/>

    <c:set var="pageTitle" value="page.label.messages"/>
    <c:if test="${contentObject.isGlobalContentObject}">
        <c:set var="pageTitle" value="page.label.content"/>
    </c:if>

    <msgpt:HeaderNew title="${pageTitle}" viewType="<%= MessagepointHeader.ViewType.EDIT %>">
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>
        <msgpt:Stylesheet href="includes/themes/messagepoint.content-object-edit.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editManager/jquery.editManager.js"/>
        <msgpt:Script src="_ux/js/mp.contentTabs.js"/>
        <msgpt:Script src="_ux/js/mp.postTrigger.js"/>
        <msgpt:Script src="_ux/js/mp.complexCombobox.js"/>
        <msgpt:Script src="_ux/js/mp.toggleSwitch.js"/>
        <msgpt:Script src="_ux/js/mp.listbox.js"/>
        <msgpt:Script src="_ux/lib/datedropper/datedropper.pro.min.js"/>
        <msgpt:Script src="_ux/js/mp.styledDateDropper.js"/>
        <msgpt:Script src="_ux/js/mp.dragAndDropList.js"/>
        <msgpt:Script src="_ux/js/mp.serializedObservableForm.js"/>
        <msgpt:Script src="content/javascript/mp.contentObjectEditDetails.js"/>
        <msgpt:Script src="_ux/js/mp.historyQueue.js"/>

        <msgpt:Script>
            <script>
                var pageDataModule = (function ($, window, document, undefined) {
                    'use strict';

                    var pageDataObj = {};

                    function init() {

                        pageDataObj = {
                            contextPath: context,
                            contentObjectId: '${contentObject.id}',
                            contentObjectName: "${msgpt:removeSpecialCharacters(contentObject.name)}",
                            isNewContentObject: ${contentObject.id == 0 ? true : false},
                            isGlobalContentObject: ${isGlobalContentObject},
                            cfSource: '${param.cfSource}',
                            statusViewId: '${param.statusViewId}',
                            isGlobalSmartCanvas: ${contentObject.isGlobalSmartCanvas},
                            isGlobalSmartText: ${contentObject.isGlobalSmartText},
                            isGlobalImage: ${contentObject.isGlobalImage},
                            isLocalSmartCanvas: ${contentObject.isLocalSmartCanvas},
                            isLocalSmartText: ${contentObject.isLocalSmartText},
                            isLocalImage: ${contentObject.isLocalImage},
                            isMessage: ${contentObject.isMessage},
                            hasStructuredContent: ${hasStructuredContent},
                            hasWorkingDynamicVariant: ${hasWorkingDynamicVariant},
                            hasActiveDynamicVariant: ${hasActiveDynamicVariant},
                            hasArchivedDynamicVariant: ${hasArchivedDynamicVariant},
                            hasAnyDynamicVariant: ${hasAnyDynamicVariant},
                            hasVariableContent: ${hasVariableContent},
                            isFreeform: '${param.isFreeform || isFreeform}',
                            documentParm: '${fn:length(command.selectedTouchpointIds) != 0 ? command.selectedTouchpointIds[0] : -1}',
                            tagCloudType: '${tagCloudType}',
                            documentId: '${document.id}',
                            noTargetingSet:${contentObject.noTargetingSet},
                            showBarCodes: ${command.showBarCodes},
                            showSupportForms: ${command.showSupportForms},
                            inputType: 'metatags'
                        }

                        $('#frontFacing').change(function () {
                            handleCheckboxChange('frontFacing', 'frontFacingEvery');
                        });

                        $('#frontFacingEvery').change(function () {
                            handleCheckboxChange('frontFacingEvery', 'frontFacing');
                        });
                    }

                    init();

                    return pageDataObj;

                })(window.jQuery, window, document);

                function handleCheckboxChange(changedId, otherId) {
                    var changedCheckbox = $('#' + changedId);
                    var otherCheckbox = $('#' + otherId);
                    var otherLabel = $('label[for="' + otherId + '"]');

                    if (changedCheckbox.prop('checked') && otherCheckbox.prop('checked')) {
                        otherCheckbox.prop('checked', false);
                        var uncheckedText = otherCheckbox.data('uncheckedtext');
                        otherLabel.text(uncheckedText);
                    }
                }

            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <msgpt:BodyNew cssClass="content-object-edit-page">
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
        <div id="content" class="d-table-row h-100">
            <div class="shiftContainer noShiftSpacing h-100 m-0">
                <form:form method="post" modelAttribute="command">
                    <c:set var="isNewContentObject" value="${empty param.contentObjectId}"/>
                    <c:set var="newEditKey" value="page.label.createnew"/>
                    <c:if test="${!isNewContentObject}">
                        <c:set var="newEditKey" value="page.label.edit"/>
                    </c:if>
                    <form:errors path="*">
                        <msgpt:Information errorMsgs="${messages}" type="error" floating="true"/>
                    </form:errors>
                    <c:if test="${not empty param.saveSuccess && param.saveSuccess == 'true'}">
                        <msgpt:Information type="success" floating="true">
                            <fmtSpring:message code="page.label.save.complete"/>
                        </msgpt:Information>
                    </c:if>
                    <c:if test="${!canUpdate}">
                        <msgpt:Information type="error" floating="true">
                            <fmtSpring:message code="error.message.action.not.permitted"/>
                        </msgpt:Information>
                    </c:if>
                    <c:if test="${canUpdate}">
                        <div class="d-flex flex-column w-100 h-100">
                            <div class="box-shadow-4 bg-white position-sticky z-index-3 top-0">
                                <div id="history-queue-container"></div>
                                <div class="d-flex align-items-center py-3 px-4 bg-white">
                                    <h1 class="h5 m-0">
                                        <div id="popupHeaderTitle" class="d-flex align-items-center">
                                            <div class="titleText" class="d-flex flex-column">
                                                <c:choose>
                                                    <c:when test="${contentObject.isGlobalSmartCanvas}">
                                                        <c:set var="assetType"
                                                               value="${msgpt:getMessage('page.label.smart.canvas')}"/>
                                                    </c:when>
                                                    <c:when test="${contentObject.isGlobalSmartText}">
                                                        <c:set var="assetType"
                                                               value="${msgpt:getMessage('page.label.smart.text')}"/>
                                                    </c:when>
                                                    <c:when test="${contentObject.isGlobalImage}">
                                                        <c:set var="assetType"
                                                               value="${msgpt:getMessage('page.label.image')}"/>
                                                    </c:when>
                                                    <c:when test="${contentObject.isLocalSmartCanvas}">
                                                        <c:set var="assetType"
                                                               value="${msgpt:getMessage('page.label.local.smart.canvas')}"/>
                                                    </c:when>
                                                    <c:when test="${contentObject.isLocalSmartText}">
                                                        <c:set var="assetType"
                                                               value="${msgpt:getMessage('page.label.local.smart.text')}"/>
                                                    </c:when>
                                                    <c:when test="${contentObject.isLocalImage}">
                                                        <c:set var="assetType"
                                                               value="${msgpt:getMessage('page.label.local.image')}"/>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <c:set var="assetType"
                                                               value="${msgpt:getMessage('page.label.message')}"/>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="line-height-1">
                                                    <c:out value='${msgpt:getMessage(newEditKey)} ${assetType}${contentObject.name != null ? ":" : ""} ${contentObject.name}'/>
                                                </div>
                                                <c:if test="${not contentObject.isGlobalSmartCanvas && not contentObject.isGlobalSmartText && not contentObject.isGlobalImage && not empty contentObject.document}">
                                                    <div class="mt-2 font-weight-normal fs-xs text-muted line-height-1">
                                                        <c:out value='${contentObject.document.name}'/>
                                                    </div>
                                                </c:if>
                                            </div>
                                            <div class="font-weight-normal fs-xs text-muted pl-1 ml-3">
                                                <div class="d-inline-flex ml-1">
                                                    <span class="d-flex align-items-center btn btn-sm rounded-pill btn-outline-light bg-lightest text-dark fs-xs">
                                                        <span class="text-uppercase font-weight-bold mr-1">
                                                            <c:if test="${not contentObject.onHold}">
                                                                <i class="workingCopyIconDiv fa fa-pencil fs-xs mr-2"></i>
                                                            </c:if>
                                                            <c:if test="${contentObject.onHold}">
                                                                <i class="fa fa-pause fs-xs mr-2"></i>
                                                            </c:if>
                                                            <span class="labelText"><fmtSpring:message
                                                                    code="page.label.WORKING.COPY"/></span>
                                                            <c:if test="${contentObject.onHold}">
                                                                <span class="labelText"> <fmtSpring:message
                                                                        code="page.text.ON.HOLD"/></span>
                                                            </c:if>
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </h1>
                                    <div class="position-relative d-flex ml-auto">
                                        <c:choose>
                                            <c:when test="${isNewContentObject}">
                                                <button id="contentObjectEditCancelBtn"
                                                        type="button"
                                                        value="cancelandexit"
                                                        class="btn btn-sm btn-outline-light text-body post-trigger">
                                                    <i class="far fa-times-circle mr-2"
                                                       aria-hidden="true"></i>
                                                    <fmtSpring:message code="page.label.cancel"/>
                                                </button>
                                            </c:when>
                                            <c:otherwise>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button"
                                                            class="btn btn-outline-light border-right-0 text-body btn-placeholder"
                                                            disabled>
                                                        <i class="fad fa-spinner-third fa-lg fa-spin align-middle mx-3"
                                                           aria-hidden="true"></i>
                                                    </button>
                                                    <select id="contentObjectEditCancelBtnGroup"
                                                            class="complex-combobox-select"
                                                            aria-label="${msgpt:getMessage("page.label.actions")}"
                                                            data-toggle="complex-combobox"
                                                            data-combobox-class="btn btn-outline-light border-right-0 text-body">
                                                        <option id="btnCancelAndExit"
                                                                value="cancelandexit"
                                                                data-icon="far fa-times-circle">
                                                            <fmtSpring:message
                                                                    code="page.label.cancel_and_go_to_list"/>
                                                        </option>
                                                        <option id="btnCancelAndGoToView"
                                                                value="gotoview"
                                                                data-icon="far fa-glasses-alt">
                                                            <fmtSpring:message
                                                                    code="page.label.cancel_and_view"/>
                                                        </option>
                                                        <option id="btnCancelAndGoBack"
                                                                value="goback"
                                                                class="d-none"
                                                                data-icon="far fa-left">
                                                            <fmtSpring:message
                                                                    code="page.label.cancel_and_go_back"/>
                                                        </option>
                                                    </select>
                                                    <button type="button"
                                                            class="btn btn-outline-light border-left-0 text-body dropdown-toggle dropdown-toggle-split"
                                                            data-toggle="dropdown"
                                                            aria-haspopup="true"
                                                            aria-expanded="false" disabled>
                                                                <span class="sr-only"><fmtSpring:message
                                                                        code="page.text.toggle.dropdown"/></span>
                                                    </button>
                                                </div>
                                            </c:otherwise>
                                        </c:choose>
                                        <div class="btn-group btn-group-sm ml-3">
                                            <button type="button"
                                                    class="btn btn-primary btn-placeholder"
                                                    disabled>
                                                <i class="fad fa-spinner-third fa-lg fa-spin align-middle mx-3"
                                                   aria-hidden="true"></i>
                                            </button>
                                            <select id="contentObjectEditSaveBtnGroup"
                                                    class="complex-combobox-select"
                                                    aria-label="${msgpt:getMessage("page.label.actions")}"
                                                    data-toggle="complex-combobox"
                                                    data-combobox-class="btn-primary">
                                                <c:if test="${not empty param.cfSource && param.cfSource == 'true'}">
                                                    <option id="btnSaveAndBack"
                                                            value="saveandgoback"
                                                            data-icon="far fa-arrow-left"
                                                            data-customitem="true">
                                                        <fmtSpring:message
                                                                code="page.label.save.back"/>
                                                    </option>
                                                </c:if>
                                                <option id="btnSaveAndStay" value="saveandstay"
                                                        data-icon="fas fa-save"><fmtSpring:message
                                                        code="page.label.save"/>
                                                </option>
                                                <option id="btnSaveAndView" value="saveandview"
                                                        data-icon="far fa-glasses-alt">
                                                    <fmtSpring:message
                                                            code="page.label.save.and.view"/>
                                                </option>
                                                <option id="btnSaveAndGoToList"
                                                        value="saveandgotolist"
                                                        data-icon="far fa-list-alt">
                                                    <fmtSpring:message
                                                            code="page.label.save.and.go.to.list"/>
                                                </option>
                                            </select>
                                            <button type="button"
                                                    class="btn btn-primary dropdown-toggle dropdown-toggle-split"
                                                    data-toggle="dropdown" aria-haspopup="true"
                                                    aria-expanded="false" disabled>
                                                                <span class="sr-only"><fmtSpring:message
                                                                        code="page.text.toggle.dropdown"/></span>
                                            </button>
                                        </div>
                                        <div class="btn-group btn-group-sm border-separate ml-3">
                                            <button type="button" id="btnSaveAndContinue"
                                                    class="btn btn-primary post-trigger"
                                                    value="saveandforward" data-toggle="tooltip"
                                                    title="<i class='fad fa-info-circle fa-lg mr-2'></i>${msgpt:getMessage("page.label.save.continue")}"
                                                    data-html="true">
                                                <i class="far fa-arrow-alt-right" aria-hidden="true"></i>
                                                <span class="sr-only"><fmtSpring:message
                                                        code="page.label.continue"/></span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="container mt-2 mb-5 pb-3 pt-4">
                                <div class="row flex-nowrap no-gutters h-100">
                                    <div class="col-auto py-1 px-2">
                                        <ul id="content-tabs" class="nav nav-column py-4 px-1">
                                            <li class="nav-item d-flex">
                                                <a class="nav-link nav-toggle border-0 text-body active"
                                                   data-toggle="tooltip" data-placement="right"
                                                   title="${msgpt:getMessage('page.label.details')}" href="#">
                                                    <span class="d-flex align-items-center justify-content-center square-lg p-1">
                                                        <i class="fad fa-list" aria-hidden="true"></i>
                                                    </span>
                                                    <span class="sr-only"><fmtSpring:message
                                                            code="page.label.details"/></span>
                                                </a>
                                            </li>
                                            <li class="item d-flex my-2">
                                                <a class="nav-link nav-toggle border-0 ${isNewContentObject ? 'disabled' : 'text-body'}" ${isNewContentObject ? 'aria-disabled="true"' : ''}
                                                   data-toggle="tooltip"
                                                   data-placement="right"
                                                   title="${msgpt:getMessage('page.label.content')}"
                                                   href="javascript:javascriptHref('${contextPath}/content/content_object_edit_content.form?contentObjectId=${contentObject.id}${param.cfSource? '&cfSource=true' : ''}')">
                                                    <span class="d-flex align-items-center justify-content-center square-lg p-1">
                                                        <i class="fad fa-newspaper" aria-hidden="true"></i>
                                                    </span>
                                                    <span class="sr-only"><fmtSpring:message
                                                            code="page.label.content"/></span>
                                                </a>
                                            </li>
                                            <c:if test="${not contentObject.isGlobalImage && not contentObject.isLocalImage}">
                                                <li id="advancedTab"
                                                    class="nav-item${command.contentObject.variableContentEnabled == true ?' d-flex' : ' d-none' }">
                                                    <a class="nav-link nav-toggle border-0 ${isNewContentObject ? 'disabled' : 'text-body'}" ${isNewContentObject ? 'aria-disabled="true"' : ''}
                                                       data-toggle="tooltip" data-placement="right"
                                                       title="${msgpt:getMessage('page.label.targeting')}"
                                                       href="javascript:javascriptHref('${contextPath}/content/content_object_edit_targeting.form?contentObjectId=${contentObject.id}${param.cfSource? '&cfSource=true' : ''}')">
                                                    <span class="d-flex align-items-center justify-content-center square-lg p-1">
                                                        <i class="fad fa-bullseye-pointer" aria-hidden="true"></i>
                                                    </span>
                                                        <span class="sr-only"><fmtSpring:message
                                                                code="page.label.targeting"/></span>
                                                    </a>
                                                </li>
                                            </c:if>
                                        </ul>
                                    </div>
                                    <div class="col position-static">
                                        <div class="box-shadow-4 rounded bg-white">
                                            <div class="p-4">
                                                <div class="pt-2 px-2">
                                                    <div class="row">
                                                        <c:if test="${not isTouchpointLocal && not isGlobalContentObject}">
                                                            <div id="side-section"
                                                                 class="col-auto mx-auto pr-3 persistedClass">
                                                                <div class="bg-lightest p-4 rounded">
                                                                    <div class="px-2 mx-1">
                                                                        <div id="docDisplay_${document.id}" class="pt-2"
                                                                             style="width: 296px">
                                                                            <msgpt:DocumentTag
                                                                                    document="${currentLayout}"
                                                                                    contentObject="${contentObject}"
                                                                                    type="standard"
                                                                                    readOnly="true"
                                                                                    hasPartAction="false"
                                                                                    maxWidth="296"/>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </c:if>
                                                        <div class="col-lg-9 mx-lg-auto${isTouchpointLocal || isGlobalContentObject ? '' : ' col-xl'}">
                                                            <div class="${not isTouchpointLocal && not isGlobalContentObject ? 'mt-4 pt-1' : 'mt-3'}">
                                                                <section id="basic-info"
                                                                         class="border-bottom pb-2 mb-4">
                                                                    <c:choose>
                                                                        <c:when test="${not empty command.tpSelection.parent}">
                                                                            <hgroup class="pb-2 mb-3">
                                                                                <h1 class="h5 anchor-title mb-1">
                                                                    <span class="anchor-title-inner">
                                                                        <fmtSpring:message
                                                                                code="page.label.basic.info"/>
                                                                        <a href="#basic-info">
                                                                            <i class="far fa-hashtag"
                                                                               aria-hidden="true"></i>
                                                                            <span class="sr-only">
                                                                                <fmtSpring:message
                                                                                        code="page.label.anchor"/>
                                                                            </span>
                                                                        </a>
                                                                    </span>
                                                                                </h1>
                                                                                <h2 class="h6 font-weight-normal text-muted m-0">
                                                                                    <fmtSpring:message
                                                                                            code="page.label.message.for"/>&nbsp;${command.tpSelection.name}</h2>
                                                                            </hgroup>
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            <h1 class="h5 anchor-title mb-3">
                                                                <span class="anchor-title-inner">
                                                                    <fmtSpring:message
                                                                            code="page.label.basic.info"/>
                                                                    <a href="#basic-info">
                                                                        <i class="far fa-hashtag"
                                                                           aria-hidden="true"></i>
                                                                        <span class="sr-only">
                                                                            <fmtSpring:message
                                                                                    code="page.label.anchor"/>
                                                                        </span>
                                                                    </a>
                                                                </span>
                                                                            </h1>
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                    <div class="form-group">
                                                                        <label for="contentObjectName">
                                                                            <fmtSpring:message code="page.label.name"/>
                                                                        </label>
                                                                        <msgpt:InputFilter type="simpleName">
                                                                            <form:input id="contentObjectName"
                                                                                        path="contentObject.name"
                                                                                        cssClass="form-control"
                                                                                        maxlength="96"/>
                                                                        </msgpt:InputFilter>
                                                                    </div>
                                                                    <c:if test="${not isTouchpointLocal && not isGlobalContentObject}">
                                                                        <div class="form-group">
                                                                            <label for="contentObjectName">
                                                                                <fmtSpring:message
                                                                                        code="page.label.delivered.to"/>
                                                                            </label>
                                                                            <div id="deliveryNavTree"
                                                                                 class="d-flex mx-n1">
                                                                                <c:out value="${contentObject.deliveryNavTreeHTML}"
                                                                                       escapeXml="false"/>
                                                                            </div>
                                                                        </div>
                                                                    </c:if>
                                                                    <div class="form-row">
                                                                        <c:if test="${not isGlobalContentObject}">
                                                                            <div class="form-group col-md-6">
                                                                                <label for="externalId">
                                                                                    <fmtSpring:message
                                                                                            code="page.label.id"/>
                                                                                </label>
                                                                                <msgpt:InputFilter type="code">
                                                                                    <form:input id="externalId"
                                                                                                path="externalId"
                                                                                                maxlength="48"
                                                                                                placeholder="${msgpt:getMessage('page.label.optional')}"
                                                                                                cssClass="form-control"/>
                                                                                </msgpt:InputFilter>
                                                                            </div>
                                                                        </c:if>
                                                                        <msgpt:IfAuthGranted
                                                                                authority="ROLE_METATAGS_VIEW">
                                                                            <div class="form-group col-md">
                                                                                <label for="contentObjectMetatags">
                                                                                    <fmtSpring:message
                                                                                            code="page.label.metatags"/>
                                                                                </label>
                                                                                <div class="d-flex flex-column">
                                                                                    <msgpt:InputFilter
                                                                                            type="simpleName">
                                                                                        <form:input
                                                                                                id="contentObjectMetatags"
                                                                                                path="metatags"
                                                                                                placeholder="${msgpt:getMessage('page.label.optional')}"
                                                                                                cssClass="form-control"
                                                                                                maxlength="255"/>
                                                                                    </msgpt:InputFilter>
                                                                                </div>
                                                                            </div>
                                                                        </msgpt:IfAuthGranted>
                                                                    </div>
                                                                    <div class="form-row">
                                                                        <c:if test="${!isGlobalContentObject && isVariantTouchpoint}">
                                                                            <div class="form-group col-md-4">
                                                                <span class="form-label d-inline-block mb-2">
                                                                    <fmtSpring:message
                                                                            code="page.label.structured.content"/>
                                                                </span>
                                                                                <div class="custom-control custom-switch mt-1">
                                                                                    <form:checkbox
                                                                                            id="structuredContentEnabled"
                                                                                            class="custom-control-input"
                                                                                            path="contentObject.structuredContentEnabled"
                                                                                            data-toggle="toggleswitch"
                                                                                            data-uncheckedtext="${msgpt:getMessage('page.label.disabled')}"
                                                                                            data-checkedtext="${msgpt:getMessage('page.label.enabled')}"/>
                                                                                    <label class="custom-control-label font-weight-bold"
                                                                                           for="structuredContentEnabled">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.enabled"/>
                                                                                    </label>
                                                                                </div>
                                                                            </div>
                                                                        </c:if>
                                                                        <c:if test="${isGlobalContentObject}">
                                                                            <div class="form-group col-md-4">
                                                                <span class="form-label d-inline-block mb-2">
                                                                    <fmtSpring:message
                                                                            code="page.label.allow.variables"/>
                                                                </span>
                                                                                <span class="d-inline-block ml-2"
                                                                                      data-toggle="tooltip"
                                                                                      title="${msgpt:getMessage('page.text.use.variables.info')}">
                                                                        <i class="far fa-info-circle text-info mr-1"
                                                                           aria-hidden="true"></i>
                                                                </span>
                                                                                <div class="custom-control custom-switch mt-1">
                                                                                    <form:checkbox
                                                                                            id="variableContentEnabled"
                                                                                            class="custom-control-input"
                                                                                            path="contentObject.variableContentEnabled"
                                                                                            data-toggle="toggleswitch"
                                                                                            data-uncheckedtext="${msgpt:getMessage('page.label.disabled')}"
                                                                                            data-checkedtext="${msgpt:getMessage('page.label.enabled')}"
                                                                                            disabled="${hasVariableContent}"/>
                                                                                    <label class="custom-control-label font-weight-bold"
                                                                                           for="variableContentEnabled">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.enabled"/>
                                                                                    </label>
                                                                                </div>
                                                                            </div>
                                                                        </c:if>
                                                                        <c:if test="${!contentObject.structuredContentEnabled || true}">
                                                                            <div id="variedByContainer"
                                                                                 class="form-group col-md">
                                                                                <label for="selectorSelectBtn">
                                                                                    <fmtSpring:message
                                                                                            code="page.label.varied.by"/>
                                                                                </label>
                                                                                <div id="selectorListbox"
                                                                                     class="dropdown"
                                                                                     data-toggle="listbox"
                                                                                     data-async="true"
                                                                                     data-url="${contextPath}/parametergroups.form?documentId=${contentObject.globalContentObject?-1:document.id}"
                                                                                     data-noselectiontext="${msgpt:getMessage("page.text.no.content.selector")}"
                                                                                     data-noselectionvalue="0">
                                                                                    <form:hidden id="selectorSelect"
                                                                                                 path="parameterGroup"
                                                                                                 data-text="${command.contentObject.getParameterGroup().name}"/>
                                                                                    <button id="selectorSelectBtn"
                                                                                            type="button"
                                                                                            class="btn dropdown-toggle btn-blank btn-block border-light text-darkest dropdown-flexblock"
                                                                                            aria-haspopup="true"
                                                                                            aria-expanded="false"
                                                                                            data-toggle="dropdown">
                                                                    <span class="dropdown-label text-truncate"><fmtSpring:message
                                                                            code="client_messages.text.loading"/></span>
                                                                                    </button>
                                                                                    <div class="dropdown-menu"
                                                                                         aria-labelledby="selectorSelectBtn"
                                                                                         style="min-width: 100%">
                                                                                        <div role="listbox"
                                                                                             class="dropdown-content"></div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </c:if>
                                                                    </div>
                                                                    <div id="variableContentChangeAlert"
                                                                         class="alert alert-warning alert-dismissible fade show mb-4 w-100 d-none"
                                                                         role="alert">
                                                                        <strong class="text-uppercase">
                                                                            <i class="fas fa-exclamation-circle fa-lg mr-2"
                                                                               aria-hidden="true"></i><fmtSpring:message
                                                                                code="page.label.warning"/>
                                                                        </strong>
                                                                        <p id="variableContentChangeAlertMessage"
                                                                           class="mt-1 mb-2">
                                                                            <fmtSpring:message
                                                                                    code="page.text.warning.object.change"/>
                                                                        </p>
                                                                        <hr class="mt-0">
                                                                        <button id="discardVariableContentChangeBtn"
                                                                                type="button"
                                                                                class="btn btn-warning btn-sm">
                                                                            <i class="far fa-undo mr-2"
                                                                               aria-hidden="true"></i>
                                                                            <fmtSpring:message code="page.label.undo"/>
                                                                        </button>
                                                                        <button type="button" class="close mt-1"
                                                                                data-dismiss="alert"
                                                                                aria-label="${msgpt:getMessage('page.label.close')}">
                                                                            <i class="far fa-times"
                                                                               aria-hidden="true"></i>
                                                                        </button>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <label for="contentObjectDescription">
                                                                            <fmtSpring:message
                                                                                    code="page.label.description"/>
                                                                        </label>
                                                                        <msgpt:InputFilter type="description">
                                                                            <form:textarea id="contentObjectDescription"
                                                                                           path="contentObject.description"
                                                                                           placeholder="${msgpt:getMessage('page.label.optional')}"
                                                                                           cssClass="form-control"
                                                                                           rows="3"/>
                                                                        </msgpt:InputFilter>
                                                                    </div>
                                                                </section>
                                                                <section id="configuration"
                                                                         class="border-bottom pb-2 mb-4${contentObject.isLocalImage ? ' d-none' : ''}">
                                                                    <h1 class="h5 anchor-title mb-3">
                                                        <span class="anchor-title-inner">
                                                            <fmtSpring:message
                                                                    code="page.label.configuration"/>
                                                            <a href="#configuration">
                                                                <i class="far fa-hashtag"
                                                                   aria-hidden="true"></i>
                                                                <span class="sr-only">
                                                                    <fmtSpring:message
                                                                            code="page.label.anchor"/>
                                                                </span>
                                                            </a>
                                                        </span>
                                                                    </h1>
                                                                    <div class="form-row">
                                                                        <c:if test="${isOmniChannel}">
                                                                            <div class="form-group col-md-6">
                                                                                <label for="channelContextSelect">
                                                                                    <fmtSpring:message
                                                                                            code="page.label.channel.restriction"/>
                                                                                </label>
                                                                                <c:choose>
                                                                                    <c:when test="${!isChannelSpecificDelivery}">
                                                                                        <form:select
                                                                                                id="channelContextSelect"
                                                                                                path="contentObject.channelContextId"
                                                                                                cssClass="custom-select">
                                                                                            <form:option
                                                                                                    value="0"><fmtSpring:message
                                                                                                    code="page.label.none"/></form:option>
                                                                                            <form:options
                                                                                                    items="${appliedChannels}"
                                                                                                    itemValue="id"
                                                                                                    itemLabel="name"/>
                                                                                        </form:select>
                                                                                    </c:when>
                                                                                    <c:otherwise>
                                                                                        <input type="text" readonly
                                                                                               class="form-control-plaintext"
                                                                                               id="channelContextSelect"
                                                                                               value="${command.contentObject.channelContextDisplay}">
                                                                                    </c:otherwise>
                                                                                </c:choose>
                                                                            </div>
                                                                        </c:if>
                                                                        <c:if test="${not isTouchpointLocal && not isGlobalContentObject}">
                                                                            <div class="form-group col-md-6">
                                                                                <label for="deliveryTypeSelect">
                                                                                    <fmtSpring:message
                                                                                            code="page.label.delivery.type"/>
                                                                                </label>
                                                                                <form:select id="deliveryTypeSelect"
                                                                                             path="deliveryType"
                                                                                             cssClass="custom-select">
                                                                                    <form:option
                                                                                            value="1"><fmtSpring:message
                                                                                            code="page.label.mandatory"/></form:option>
                                                                                    <form:option
                                                                                            value="2"><fmtSpring:message
                                                                                            code="page.label.optional"/></form:option>
                                                                                </form:select>
                                                                            </div>
                                                                        </c:if>
                                                                        <c:if test="${not isFreeform}">
                                                                            <c:if test="${not isTouchpointLocal && (command.contentObject.isText || command.contentObject.isMarkup)}">
                                                                                <div class="form-group col-md">
                                                                                    <label for="contentSpecializationTypeSelect">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.content"/>
                                                                                    </label>
                                                                                    <form:select
                                                                                            id="contentSpecializationTypeSelect"
                                                                                            path="contentObject.contentType"
                                                                                            cssClass="custom-select">
                                                                                        <c:forEach var="contentType"
                                                                                                   items="${contentTypes}">
                                                                                            <form:option
                                                                                                    value="${contentType.id}"
                                                                                                    label="${msgpt:getMessage(contentType.name)}"/>
                                                                                        </c:forEach>
                                                                                    </form:select>
                                                                                </div>
                                                                            </c:if>
                                                                            <c:if test="${licencedForMessagepointInteractive && isGlobalContentObject}">
                                                                                <fieldset
                                                                                        class="col-xl-8 col-xxl-7 mb-4">
                                                                                    <legend class="form-label">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.usage"/>
                                                                                    </legend>
                                                                                    <div class="d-flex btn-group-toggle mx-n2"
                                                                                         data-toggle="buttons">
                                                                                        <label class="btn btn-blank btn-check text-darkest mx-2">
                                                                                            <form:radiobutton
                                                                                                    id="usageTypeId1"
                                                                                                    path="contentObject.usageTypeId"
                                                                                                    value="1"/>
                                                                                            <i class="far fa-envelope mr-1"
                                                                                               aria-hidden="true"></i>
                                                                                            <fmtSpring:message
                                                                                                    code="page.label.message"/>
                                                                                        </label>
                                                                                        <label class="btn btn-blank btn-check text-darkest mx-2">
                                                                                            <form:radiobutton
                                                                                                    id="usageTypeId2"
                                                                                                    path="contentObject.usageTypeId"
                                                                                                    value="2"/>
                                                                                            <i class="far fa-plug mr-1"
                                                                                               aria-hidden="true"></i>
                                                                                            <fmtSpring:message
                                                                                                    code="page.label.interactive"/>
                                                                                        </label>
                                                                                        <label class="btn btn-blank btn-check text-darkest mx-2">
                                                                                            <form:radiobutton
                                                                                                    id="usageTypeId3"
                                                                                                    path="contentObject.usageTypeId"
                                                                                                    value="3"/>
                                                                                            <i class="far fa-shield-check mr-1"
                                                                                               aria-hidden="true"></i>
                                                                                            <fmtSpring:message
                                                                                                    code="page.label.unrestricted"/>
                                                                                        </label>
                                                                                    </div>
                                                                                </fieldset>
                                                                            </c:if>
                                                                        </c:if>
                                                                    </div>
                                                                    <div class="form-row">
                                                                        <c:if test="${isTouchpointLocal && not empty placeholders}">
                                                                            <div class="form-group col-md">
                                                                                <label for="placeholderSelect">
                                                                                    <fmtSpring:message
                                                                                            code="page.label.placeholder"/>
                                                                                </label>
                                                                                <c:choose>
                                                                                    <c:when test="${isNewContentObject || empty command.placeholder}">
                                                                                        <form:select
                                                                                                id="placeholderSelect"
                                                                                                path="placeholder"
                                                                                                cssClass="custom-select">
                                                                                            <form:option
                                                                                                    value="0"><fmtSpring:message
                                                                                                    code="page.label.none"/></form:option>
                                                                                            <form:options
                                                                                                    items="${placeholders}"
                                                                                                    itemLabel="friendlyName"
                                                                                                    itemValue="id"/>
                                                                                        </form:select>
                                                                                    </c:when>
                                                                                    <c:otherwise>
                                                                                        <input type="text" readonly
                                                                                               class="form-control-plaintext"
                                                                                               id="placeholderSelect"
                                                                                               value="${command.placeholder.friendlyName}">
                                                                                    </c:otherwise>
                                                                                </c:choose>
                                                                            </div>
                                                                        </c:if>
                                                                        <c:if test="${appliesMixedDataGroups}">
                                                                            <div class="form-group col-md">
                                                                                <label for="dataGroupSelect">
                                                                                    <fmtSpring:message
                                                                                            code="page.label.data.group"/>
                                                                                </label>
                                                                                <form:select id="dataGroupSelect"
                                                                                             path="contentObject.dataGroup"
                                                                                             cssClass="custom-select">
                                                                                    <form:options items="${dataGroups}"
                                                                                                  itemLabel="name"
                                                                                                  itemValue="id"/>
                                                                                </form:select>
                                                                            </div>
                                                                        </c:if>
                                                                    </div>
                                                                    <c:if test="${(isTouchpointLocal || isGlobalContentObject) && (not contentObject.isGraphic)}">
                                                                        <fieldset id="componentsContainer" class="mb-4">
                                                                            <legend class="form-label">
                                                                                <fmtSpring:message
                                                                                        code="page.label.components"/>
                                                                            </legend>
                                                                            <div class="d-flex mx-n2">
                                                                                <div class="btn-group-toggle mx-2 textOnlyGroup"
                                                                                     data-toggle="buttons">
                                                                                    <label class="btn btn-blank btn-block btn-check text-darkest${not (isNewContentObject || not command.supportsTables) ? ' disabled' : ''}">
                                                                                        <form:checkbox id="supports1"
                                                                                                       path="supportsTables"
                                                                                                       disabled="${not (isNewContentObject || not command.supportsTables)}"/>
                                                                                        <i class="far fa-table mr-1"
                                                                                           aria-hidden="true"></i>
                                                                                        <fmtSpring:message
                                                                                                code="page.label.tables"/>
                                                                                    </label>
                                                                                </div>
                                                                                <div class="btn-group-toggle mx-2 textOnlyGroup"
                                                                                     style="${(command.showBarCodes || command.contentObject.variableContentEnabled == false) ? '':' display: none;' }"
                                                                                     data-toggle="buttons"
                                                                                     id="supportsForms">
                                                                                    <label class="btn btn-blank btn-block btn-check text-darkest${not (isNewEmbeddedContent || not command.supportsForms) ? ' disabled' : ''}">
                                                                                        <form:checkbox id="supports2"
                                                                                                       path="supportsForms"
                                                                                                       disabled="${not (isNewContentObject || not command.supportsForms)}"/>
                                                                                        <i class="far fa-list-alt mr-1"
                                                                                           aria-hidden="true"></i>
                                                                                        <fmtSpring:message
                                                                                                code="page.label.forms"/>
                                                                                    </label>
                                                                                </div>
                                                                                <div class="btn-group-toggle mx-2 textOnlyGroup"
                                                                                     style="${(command.showBarCodes || command.contentObject.variableContentEnabled == false) ? '':' display: none;' }"
                                                                                     data-toggle="buttons"
                                                                                     id="supportsBarcodes">
                                                                                    <label class="btn btn-blank btn-block btn-check text-darkest${not (isNewEmbeddedContent || not command.supportsBarcodes) ? ' disabled' : ''}">
                                                                                        <form:checkbox id="supports3"
                                                                                                       path="supportsBarcodes"
                                                                                                       disabled="${not (isNewContentObject || not command.supportsBarcodes)}"/>
                                                                                        <i class="far fa-barcode-read mr-1"
                                                                                           aria-hidden="true"></i>
                                                                                        <fmtSpring:message
                                                                                                code="page.label.barcodes"/>
                                                                                    </label>
                                                                                </div>

                                                                                <div class="btn-group-toggle mx-2 textOnlyGroup contentMenuToggleGroup"
                                                                                     data-toggle="buttons">
                                                                                    <label class="btn btn-blank btn-block btn-check text-darkest${not (isNewEmbeddedContent || not command.supportsContentMenus) ? ' disabled' : ''}">
                                                                                        <form:checkbox id="supports4"
                                                                                                       path="supportsContentMenus"
                                                                                                       disabled="${not (isNewContentObject || not command.supportsContentMenus)}"/>
                                                                                        <i class="far fa-stream mr-1"
                                                                                           aria-hidden="true"></i>
                                                                                        <fmtSpring:message
                                                                                                code="page.label.content.menus"/>
                                                                                    </label>
                                                                                </div>
                                                                            </div>
                                                                        </fieldset>
                                                                        <c:if test="${permitMixedDxfTaggedText}">
                                                                            <div class="mb-4 pt-1 textOnlyGroup contentMenuToggleGroup">
                                                                                <div class="custom-control custom-checkbox">
                                                                                    <form:checkbox
                                                                                            id="renderAsTaggedText"
                                                                                            path="contentObject.renderAsTaggedText"
                                                                                            cssClass="custom-control-input"/>
                                                                                    <label class="custom-control-label"
                                                                                           for="renderAsTaggedText">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.render.tagged.text"/>
                                                                                        <span class="d-inline-block ml-2"
                                                                                              data-toggle="tooltip"
                                                                                              title='${msgpt:getMessage('page.text.when.rendering.tagged.text.enabled')}'>
                                                                            <i class="far fa-info-circle text-info mr-1"
                                                                               aria-hidden="true"></i>
                                                                        </span>
                                                                                    </label>
                                                                                </div>
                                                                            </div>
                                                                        </c:if>
                                                                    </c:if>
                                                                </section>
                                                                <c:if test="${show_front_facing == 'true'}">
                                                                    <section id="page"
                                                                             class="border-bottom pb-2 mb-4">
                                                                        <h1 class="h5 anchor-title mb-3">
                                                            <span class="anchor-title-inner">
                                                                <fmtSpring:message
                                                                        code="page.label.page"/>
                                                                <a href="#page">
                                                                    <i class="far fa-hashtag"
                                                                       aria-hidden="true"></i>
                                                                    <span class="sr-only">
                                                                        <fmtSpring:message
                                                                                code="page.label.anchor"/>
                                                                    </span>
                                                                </a>
                                                            </span>
                                                                        </h1>
                                                                        <div class="form-row">
                                                                            <div class="form-group col-md-4">
                                                                <span class="form-label d-inline-block mb-2">
                                                                    <fmtSpring:message
                                                                            code="page.label.only.first.page.starts.front.facing"/>
                                                                </span>
                                                                                <div class="custom-control custom-switch mt-1">
                                                                                    <form:checkbox
                                                                                            id="frontFacing"
                                                                                            class="custom-control-input"
                                                                                            path="contentObject.startsFrontFacing"
                                                                                            data-toggle="toggleswitch"
                                                                                            data-uncheckedtext="${msgpt:getMessage('page.label.disabled')}"
                                                                                            data-checkedtext="${msgpt:getMessage('page.label.enabled')}"/>
                                                                                    <label class="custom-control-label font-weight-bold"
                                                                                           for="frontFacing">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.enabled"/>
                                                                                    </label>
                                                                                </div>
                                                                            </div>

                                                                            <div class="form-group col-md-4">
                                                                <span class="form-label d-inline-block mb-2">
                                                                    <fmtSpring:message
                                                                            code="page.label.every.page.starts.front.facing"/>
                                                                </span>
                                                                                <div class="custom-control custom-switch mt-1">
                                                                                    <form:checkbox
                                                                                            id="frontFacingEvery"
                                                                                            class="custom-control-input"
                                                                                            path="contentObject.startsFrontFacingEvery"
                                                                                            data-toggle="toggleswitch"
                                                                                            data-uncheckedtext="${msgpt:getMessage('page.label.disabled')}"
                                                                                            data-checkedtext="${msgpt:getMessage('page.label.enabled')}"/>
                                                                                    <label class="custom-control-label font-weight-bold"
                                                                                           for="frontFacingEvery">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.enabled"/>
                                                                                    </label>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </section>
                                                                </c:if>
                                                                <c:if test="${isGlobalContentObject}">
                                                                    <section id="touchpoint-assignments"
                                                                             class="border-bottom pb-2 mb-4${command.contentObject.variableContentEnabled == false ? ' d-none' : ''}">
                                                                        <hgroup class="pb-2 mb-3">
                                                                            <h1 class="h5 anchor-title mb-1">
                                                                <span class="anchor-title-inner">
                                                                    <fmtSpring:message
                                                                            code="page.label.touchpoint.assignments"/>
                                                                    <a href="#touchpoint-assignments">
                                                                        <i class="far fa-hashtag"
                                                                           aria-hidden="true"></i>
                                                                        <span class="sr-only">
                                                                            <fmtSpring:message
                                                                                    code="page.label.anchor"/>
                                                                        </span>
                                                                    </a>
                                                                </span>
                                                                            </h1>
                                                                            <h2 class="h6 font-weight-normal text-muted m-0">
                                                                                <fmtSpring:message
                                                                                        code="page.text.share.object.across.touchpoints"/>:</h2>
                                                                        </hgroup>
                                                                        <div id="touchpointAssociations"
                                                                             class="drag-and-drop row no-gutters align-items-stretch pb-4">
                                                                            <div class="col-5">
                                                                                <h5><fmtSpring:message
                                                                                        code="page.label.available.touchpoints"/></h5>
                                                                                <div class="droppable-section">
                                                                                    <div class="form-control p-0 bg-lightest rounded-bottom-0 h-auto">
                                                                                        <div class="position-relative z-index-1">
                                                                                            <input class="form-control form-control-lg bg-lightest px-5 border-0 shadow-none"
                                                                                                   type="text"
                                                                                                   data-toggle="tagcloud"
                                                                                                   data-cloud-type="4"
                                                                                                   aria-label="${msgpt:getMessage('page.label.search.for')}"
                                                                                                   placeholder="${msgpt:getMessage('page.label.search')}"
                                                                                                   disabled>
                                                                                            <button class="btn btn-lg btn-icon-toggle bg-transparent shadow-none py-0 px-4 mr-2 position-absolute h-100 top-0 left-0"
                                                                                                    type="button"
                                                                                                    aria-label="${msgpt:getMessage('page.label.search')}"
                                                                                                    tabindex="-1"
                                                                                                    disabled>
                                                                                                <i class="far fa-search"
                                                                                                   aria-hidden="true"></i>
                                                                                                <i class="far fa-times text-primary"
                                                                                                   aria-hidden="true"></i>
                                                                                            </button>
                                                                                        </div>
                                                                                        <div class="pl-3 mb-1 text-muted">
                                                                                            <div class="d-inline-block custom-control custom-checkbox w-auto no-text mr-3">
                                                                                                <input type="checkbox"
                                                                                                       class="custom-control-input selectAll"
                                                                                                       id="selectAll"
                                                                                                       style="min-height: auto;">
                                                                                                <label class="custom-control-label pl-2"
                                                                                                       for="selectAll">Select
                                                                                                    All</label>
                                                                                            </div>
                                                                                            <div class="d-inline-block custom-control custom-checkbox w-auto no-text mr-3">
                                                                                                <input type="checkbox"
                                                                                                       class="custom-control-input viewSelected"
                                                                                                       id="viewSelected"
                                                                                                       style="min-height: auto;">
                                                                                                <label class="custom-control-label pl-2"
                                                                                                       for="viewSelected">View
                                                                                                    Selected</label>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="position-relative flex-grow-1 border border-top-0 border-light rounded-lg rounded-top-0 overflow-hidden">
                                                                                        <div class="draggable-list-wrapper">
                                                                                            <div class="draggable-list"></div>
                                                                                            <div class="draggable-loading">
                                                                                                <div class="progress-loader progress-loader-lg">
                                                                                                    <i class="progress-loader-icon far fa-spinner-third"
                                                                                                       aria-hidden="true"></i>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="col align-self-center">
                                                                                <div class="text-center text-muted">
                                                                                    <div class="d-inline-block square-lg bg-secondary-lightest rounded-circle p-2 text-secondary fs-md">
                                                                                        <i class="far fa-hand-paper drag-and-drop-indicator"
                                                                                           aria-hidden="true"></i>
                                                                                    </div>
                                                                                    <div class="mt-2 mb-n1 fs-xs">
                                                                                        <fmtSpring:message
                                                                                                code="client_messages.text.drag_and_drop"/>
                                                                                    </div>
                                                                                    <i class="fas fa-arrows-h"
                                                                                       aria-hidden="true"></i>
                                                                                </div>
                                                                                <div class="drag-and-drop-datasource">
                                                                                    <c:if test="${fn:length(availableTouchpoints) > 0}">
                                                                                        <c:forEach var="touchpoint"
                                                                                                   items="${availableTouchpoints}">
                                                                                            <form:checkbox
                                                                                                    id="touchpointCheck${touchpoint.id}"
                                                                                                    path="selectedTouchpointIds"
                                                                                                    value="${touchpoint.id}"
                                                                                                    aria-label="${touchpoint.name}"
                                                                                                    data-filtervalue="${touchpoint.name}"
                                                                                                    data-filtermetatags="${touchpoint.metatags}"
                                                                                                    data-level="${touchpoint.projectDepth}"/>
                                                                                        </c:forEach>
                                                                                        <c:forEach var="collection"
                                                                                                   items="${availableCollections}">
                                                                                            <form:checkbox
                                                                                                    id="collectionCheck${collection.id}"
                                                                                                    path="selectedTouchpointCollectionIds"
                                                                                                    value="${collection.id}"
                                                                                                    aria-label="${collection.name}"
                                                                                                    data-filtervalue="${collection.name}"/>
                                                                                        </c:forEach>
                                                                                    </c:if>
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-5">
                                                                                <h5><fmtSpring:message
                                                                                        code="page.label.selected.touchpoints"/></h5>
                                                                                <div class="droppable-section">
                                                                                    <div class="form-control p-0 bg-lightest rounded-bottom-0 h-auto">
                                                                                        <div class="position-relative z-index-1">
                                                                                            <input class="form-control form-control-lg bg-lightest px-5 border-0 shadow-none"
                                                                                                   type="text"
                                                                                                   data-toggle="tagcloud"
                                                                                                   data-cloud-type="4"
                                                                                                   aria-label="${msgpt:getMessage('page.label.search.for')}"
                                                                                                   placeholder="${msgpt:getMessage('page.label.search')}"
                                                                                                   disabled>
                                                                                            <button class="btn btn-lg btn-icon-toggle bg-transparent shadow-none py-0 px-4 mr-2 position-absolute h-100 top-0 left-0"
                                                                                                    type="button"
                                                                                                    aria-label="${msgpt:getMessage('page.label.search')}"
                                                                                                    tabindex="-1"
                                                                                                    disabled>
                                                                                                <i class="far fa-search"
                                                                                                   aria-hidden="true"></i>
                                                                                                <i class="far fa-times text-primary"
                                                                                                   aria-hidden="true"></i>
                                                                                            </button>
                                                                                        </div>
                                                                                        <div class="pl-3 mb-1 text-muted">
                                                                                            <div class="d-inline-block custom-control custom-checkbox w-auto no-text mr-3">
                                                                                                <input type="checkbox"
                                                                                                       class="custom-control-input selectAll"
                                                                                                       id="selectAll2"
                                                                                                       style="min-height: auto;">
                                                                                                <label class="custom-control-label pl-2"
                                                                                                       for="selectAll2">Select
                                                                                                    All</label>
                                                                                            </div>
                                                                                            <div class="d-inline-block custom-control custom-checkbox w-auto no-text mr-3">
                                                                                                <input type="checkbox"
                                                                                                       class="custom-control-input viewSelected"
                                                                                                       id="viewSelected2"
                                                                                                       style="min-height: auto;">
                                                                                                <label class="custom-control-label pl-2"
                                                                                                       for="viewSelected2">View
                                                                                                    Selected</label>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="position-relative flex-grow-1 border border-top-0 border-light rounded-lg rounded-top-0 overflow-hidden">
                                                                                        <div class="draggable-list-wrapper">
                                                                                            <div class="draggable-list"></div>
                                                                                            <div class="draggable-loading">
                                                                                                <div class="progress-loader progress-loader-lg">
                                                                                                    <i class="progress-loader-icon far fa-spinner-third"
                                                                                                       aria-hidden="true"></i>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </section>
                                                                </c:if>
                                                                <c:if test="${not empty command.formWrapper}">
                                                                    <section id="metadata"
                                                                             class="border-bottom pb-2 mb-4">
                                                                        <h1 class="h5 anchor-title mb-3">
                                                            <span class="anchor-title-inner">
                                                                <fmtSpring:message
                                                                        code="page.label.metadata"/>
                                                                <a href="#metadata">
                                                                    <i class="far fa-hashtag" aria-hidden="true"></i>
                                                                    <span class="sr-only">
                                                                        <fmtSpring:message
                                                                                code="page.label.anchor"/>
                                                                    </span>
                                                                </a>
                                                            </span>
                                                                        </h1>
                                                                        <jsp:include
                                                                                page="../metadata/metadata_form_edit.jsp"/>
                                                                    </section>
                                                                </c:if>
                                                                <c:if test="${not isTouchpointLocal && command.contentObject.zone != null && (command.contentObject.zone.isFooter || command.contentObject.zone.isHeader)}">
                                                                    <section id="delivery-zone"
                                                                             class="border-bottom pb-2 mb-4">
                                                                        <h1 class="h5 anchor-title mb-3">
                                                            <span class="anchor-title-inner">
                                                                <fmtSpring:message
                                                                        code="${command.contentObject.zone.isFooter ? 'page.label.footer' : 'page.label.header'}"/>
                                                                <a href="#delivery-zone">
                                                                    <i class="far fa-hashtag" aria-hidden="true"></i>
                                                                    <span class="sr-only">
                                                                        <fmtSpring:message
                                                                                code="page.label.anchor"/>
                                                                    </span>
                                                                </a>
                                                            </span>
                                                                        </h1>
                                                                        <div class="form-group">
                                                                            <label for="flowTypeSelect">
                                                                                <fmtSpring:message
                                                                                        code="page.label.flow.type"/>
                                                                            </label>
                                                                            <form:select id="flowTypeSelect"
                                                                                         cssClass="custom-select"
                                                                                         path="contentObject.flowType"
                                                                                         items="${flowTypes}"
                                                                                         itemLabel="name"
                                                                                         itemValue="id"/>
                                                                        </div>
                                                                    </section>
                                                                </c:if>
                                                                <c:if test="${not contentObject.isGlobalImage}">
                                                                    <section id="timing"
                                                                             class="border-bottom pb-2 mb-4">
                                                                        <h1 class="h5 anchor-title mb-3">
                                                            <span class="anchor-title-inner">
                                                                <fmtSpring:message
                                                                        code="page.label.timing"/>
                                                                <a href="#timing">
                                                                    <i class="far fa-hashtag"
                                                                       aria-hidden="true"></i>
                                                                    <span class="sr-only">
                                                                        <fmtSpring:message
                                                                                code="page.label.anchor"/>
                                                                    </span>
                                                                </a>
                                                            </span>
                                                                        </h1>
                                                                        <div class="form-row">
                                                                            <div class="form-group col-md">
                                                                                <label for="startDate">
                                                                                    <fmtSpring:message
                                                                                            code="page.label.start.date"/>
                                                                                    <span class="d-inline-block ml-2"
                                                                                          data-toggle="tooltip"
                                                                                          title="${msgpt:getMessage('page.text.timing.starts.now.info')}">
                                                                        <i class="far fa-info-circle text-info mr-1"
                                                                           aria-hidden="true"></i>
                                                                    </span>
                                                                                </label>
                                                                                <div class="input-group">
                                                                                    <div class="input-group-prepend">
                                                                        <span class="input-group-text bg-lightest text-muted fs-md"
                                                                              id="startDateAddon">
                                                                            <i class="far fa-calendar-day"
                                                                               aria-hidden="true"></i>
                                                                            <span class="sr-only">
                                                                                <fmtSpring:message
                                                                                        code="page.label.date.selection"/>
                                                                            </span>
                                                                        </span>
                                                                                    </div>
                                                                                    <form:input id="startDate"
                                                                                                cssClass="form-control"
                                                                                                path="contentObject.startDate"
                                                                                                placeholder="${msgpt:getMessage('page.text.timing.starts.now')}"
                                                                                                data-toggle="datedropper"
                                                                                                data-clear="startDateClear"
                                                                                                aria-describedby="startDateAddon"/>
                                                                                    <div class="input-group-append">
                                                                                        <button type="button"
                                                                                                id="startDateClear"
                                                                                                class="btn btn-outline-light text-dark"
                                                                                                aria-label="${msgpt:getMessage('page.label.clear')}"
                                                                                                title="${msgpt:getMessage('page.label.clear.date')}"
                                                                                                data-toggle="tooltip">
                                                                                            <i class="far fa-times fa-sm"
                                                                                               aria-hidden="true"></i>
                                                                                        </button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="form-group col-md">
                                                                                <label for="endDate">
                                                                                    <fmtSpring:message
                                                                                            code="page.label.end.date"/>
                                                                                    <span class="d-inline-block ml-2"
                                                                                          data-toggle="tooltip"
                                                                                          title="${msgpt:getMessage('page.text.timing.no.end.date.info')}">
                                                                        <i class="far fa-info-circle text-info mr-1"
                                                                           aria-hidden="true"></i>
                                                                    </span>
                                                                                </label>
                                                                                <div class="input-group">
                                                                                    <div class="input-group-prepend">
                                                                        <span class="input-group-text bg-lightest text-muted fs-md"
                                                                              id="endDateAddon">
                                                                            <i class="far fa-calendar-day fa-flip-horizontal"
                                                                               aria-hidden="true"></i>
                                                                            <span class="sr-only">
                                                                                <fmtSpring:message
                                                                                        code="page.label.date.selection"/>
                                                                            </span>
                                                                        </span>
                                                                                    </div>
                                                                                    <form:input id="endDate"
                                                                                                cssClass="form-control"
                                                                                                path="contentObject.endDate"
                                                                                                placeholder="${msgpt:getMessage('page.text.timing.no.end.date')}"
                                                                                                data-toggle="datedropper"
                                                                                                data-clear="endDateClear"
                                                                                                aria-describedby="endDateAddon"/>
                                                                                    <div class="input-group-append">
                                                                                        <button type="button"
                                                                                                id="endDateClear"
                                                                                                class="btn btn-outline-light text-dark"
                                                                                                aria-label="${msgpt:getMessage('page.label.clear')}"
                                                                                                title="${msgpt:getMessage('page.label.clear.date')}"
                                                                                                data-toggle="tooltip">
                                                                                            <i class="far fa-times fa-sm"
                                                                                               aria-hidden="true"></i>
                                                                                        </button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="form-group col-xl-3 pt-xl-4">
                                                                                <div class="custom-control custom-checkbox pt-xl-3 mt-xl-n1">
                                                                                    <form:checkbox
                                                                                            id="repeatDatesAnnually"
                                                                                            path="contentObject.repeatDatesAnnually"
                                                                                            cssClass="custom-control-input"/>
                                                                                    <label class="custom-control-label"
                                                                                           for="repeatDatesAnnually">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.repeat.annually"/>
                                                                                    </label>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </section>
                                                                </c:if>
                                                                <c:if test="${contentObject.isGlobalSmartText || contentObject.isGlobalSmartCanvas || contentObject.isTouchpointLocal}">
                                                                    <section id="delivery"
                                                                             class="border-bottom mb-4">
                                                                        <h1 class="h5 anchor-title mb-3">
                                                            <span class="anchor-title-inner">
                                                                <fmtSpring:message
                                                                        code="page.label.Delivery"/>
                                                                <a href="#delivery">
                                                                    <i class="far fa-hashtag" aria-hidden="true"></i>
                                                                    <span class="sr-only">
                                                                        <fmtSpring:message
                                                                                code="page.label.anchor"/>
                                                                    </span>
                                                                </a>
                                                            </span>
                                                                        </h1>
                                                                        <div class="form-row">
                                                                            <div class="form-group col-9">
                                                                                <p>
                                                                                    <fmtSpring:message
                                                                                            code="page.text.if.timing.or.targeting.of.asset.does.not.apply"/>
                                                                                </p>
                                                                                <div class="custom-control custom-switch">
                                                                                    <input type="checkbox"
                                                                                           id="deliveryTypeCheck"
                                                                                           class="custom-control-input"
                                                                                           data-toggle="toggleswitch"
                                                                                           data-uncheckedtext="${msgpt:getMessage('page.label.do.not.send.message')}"
                                                                                           data-checkedtext="${msgpt:getMessage('page.label.send.message.anyway')}"
                                                                                        ${(command.deliveryType == 0) ? 'checked' : ''}/>
                                                                                    <label class="custom-control-label font-weight-bold"
                                                                                           for="deliveryTypeCheck">
                                                                                            ${msgpt:getMessage('page.label.send.message.anyway')}
                                                                                    </label>
                                                                                </div>
                                                                                <form:select id="deliveryTypeSelect"
                                                                                             path="deliveryType"
                                                                                             items="${deliveryTypes}"
                                                                                             itemLabel="name"
                                                                                             itemValue="id"
                                                                                             cssClass="d-none"/>
                                                                            </div>
                                                                        </div>
                                                                    </section>
                                                                </c:if>
                                                                <section id="comments" class="mb-4">
                                                                    <h1 class="h5 anchor-title mb-3">
                                                        <span class="anchor-title-inner">
                                                            <fmtSpring:message
                                                                    code="page.label.comments"/>
                                                            <c:if test="${not empty command.contentObject.comments}">
                                                                (${command.contentObject.comments.size()})
                                                            </c:if>
                                                            <a href="#comments">
                                                                <i class="far fa-hashtag" aria-hidden="true"></i>
                                                                <span class="sr-only">
                                                                    <fmtSpring:message
                                                                            code="page.label.anchor"/>
                                                                </span>
                                                            </a>
                                                        </span>
                                                                    </h1>
                                                                    <div class="form-group">
                                                                        <msgpt:InputFilter type="comment">
                                                                            <form:textarea id="newComment"
                                                                                           aria-label="${msgpt:getMessage('page.label.new')}"
                                                                                           placeholder="${msgpt:getMessage('page.label.new')}"
                                                                                           path="contentObject.newComment.comment"
                                                                                           cssClass="form-control"
                                                                                           rows="3"/>
                                                                        </msgpt:InputFilter>
                                                                    </div>
                                                                    <c:if test="${not empty command.contentObject.comments}">
                                                                        <div class="pt-1">
                                                                            <c:forEach var="comment"
                                                                                       items="${command.contentObject.commentsOrdered}"
                                                                                       varStatus="status">
                                                                                <blockquote class="blockquote">
                                                                                    <p class="blockquote-body">
                                                                            <span class="blockquote-text">
                                                                                <c:out value="${comment.comment}"/>
                                                                            </span>
                                                                                    </p>
                                                                                    <footer class="blockquote-footer">
                                                                        <span class="mr-4">
                                                                            <fmtJSTL:formatDate
                                                                                    value="${comment.created}"
                                                                                    pattern="${dateTimeFormat}"/>
                                                                            <span class="fs-md ml-2">
                                                                                <i class="far fa-clock"
                                                                                   aria-hidden="true"></i>
                                                                            </span>
                                                                        </span>
                                                                                        <cite title="${comment.user.fullName}">
                                                                                            <c:out value="${comment.user.fullName}"/>
                                                                                            <span class="fs-md ml-2">
                                                                                <i class="far fa-user"
                                                                                   aria-hidden="true"></i>
                                                                            </span>
                                                                                        </cite>
                                                                                    </footer>
                                                                                </blockquote>
                                                                            </c:forEach>
                                                                        </div>
                                                                    </c:if>
                                                                </section>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:if>
                </form:form>
            </div>
        </div>
    </msgpt:BodyNew>

</msgpt:Html5>