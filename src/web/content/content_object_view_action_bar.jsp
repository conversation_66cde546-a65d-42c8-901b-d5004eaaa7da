<%@ include file="../includes/includes.jsp" %>

<c:set var="contentObject" value="${command.contentObject}"/>
<c:set var="releaseForApproval" value="true"/>
<c:set var="assignedUser" value="true"/>
<c:set var="activeView" value="true"/>
<c:set var="showEditMenu"
       value="${(empty param.viewOnly || param.viewOnly == false) && (empty param.flowparameter || param.flowparameter != 'viewOnly') && not contentObject.accessRestricted}"/>

<msgpt:Section>
    <msgpt:Script src="includes/javascript/popupActions.js"/>

    <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
    <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>

    <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
    <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

    <msgpt:Script src="includes/javascript/jQueryPlugins/taskManager/jquery.taskManager.js"/>
    <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/taskManager/taskManager.css"/>

    <msgpt:Script src="_ux/js/mp.complexCombobox.js"/>

    <msgpt:CalendarIncludes/>

    <msgpt:Script>
        <script>
            function addLookupValue_new(index) {
                var currentValue = $('#dataValueInput_new_' + index).attr('value');
                var selectedValue = $('#lookupSelect_new_' + index).find(':selected').attr('value');
                if (currentValue == "") {
                    $('#dataValueInput_new_' + index).attr('value', selectedValue);
                } else {
                    $('#dataValueInput_new_' + index).attr('value', currentValue + ',' + selectedValue);
                }
            }

            function updateSelected(selectEle) {
                var actionId = selectEle.options[selectEle.selectedIndex].id.replace('actionOption_', '');
                selectEle.selectedIndex = 0;
                selectEle.selectedIndex = 0;
                submitAction(actionId, selectEle);
            }

            function showOrHideWorkflowSelect(popupId, actionSpecId){
                var isInSubworkflowStep = ${contentObject.inSubworkflowStep};

                if (isInSubworkflowStep){
                    $('#actionPopupWorkflowSelect').show();
                    if (exists('approveBtnDisabled')) {	$('#approveBtnDisabled').show(); $('#approveBtn').hide(); }
                    if (exists('releaseBtnDisabled')) {	$('#releaseBtnDisabled').show(); $('#releaseBtn').hide(); }
                    if(actionSpecId == 20 || actionSpecId == 22){ // Reject or Reject and Override
                        $('#actionPopupUserSelect').hide();
                        $('#rejectBtnDisabled').show(); $('#rejectBtn').hide();
                    }
                }else{
                    $('#actionPopupWorkflowSelect').hide();
                    if(actionSpecId == 19 || actionSpecId == 21) { // Approve or Approve and Override
                        $('#approveBtn').show(); $('#approveBtnDisabled').hide();
                    }
                    if(actionSpecId == 20 || actionSpecId == 22) { // Reject or Reject and Override
                        $('#actionPopupUserSelect').show();
                        $('#rejectBtnDisabled').show(); $('#rejectBtn').hide();
                    }
                }
            }

            function selectWorkflow(){
                var actionId = $('#popupActionId').val();
                if(actionId == 43){ // Reassign for translation
                    // Reload the user select based on the workflow selection
                    requestDataForSelectMenu($('#actionUserSelect_'+actionId).attr('type'));
                }
                validatePopupReq();
            }

            function selectTask(){
                $('#actionPopupNote').find('textarea').val('');
                if ($('#taskSelect').val() === '0'){
                    $('#actionPopupNote').find('textarea').removeAttr("disabled");
                    $('#actionPopupNote').attr('state','clear');
                    $('#newTaskTypeDiv').show();
                    $('#taskVariantDiv').show();
                    selectTaskType();
                }else{
                    $('#actionPopupNote').find('textarea').attr("disabled", true);
                    $('#newTaskTypeDiv').hide();
                    $('#newTaskLanguageDiv').hide();
                    $('#taskVariantDiv').hide();
                    var taskNameWithDescription = $('#taskSelect').find(':selected').text();
                    var match = taskNameWithDescription.match(/\(([^)]+)\)/);
                    if(match){
                        $('#actionPopupNote').find('textarea').val(match[1]);
                    }
                }
                validatePopupReq();
            }

            function selectTaskType(){
                if ($('#taskTypeSelect').val() === '2'){
                    $('#newTaskLanguageDiv').show();
                }
                else{
                    $('#newTaskLanguageDiv').hide();
                }
            }

            function continueToLinkTask() {
                if(${command.hasTaskToLink}) {
                    $.ajax({
                        type: "GET",
                        url: context+"/getDataForSelectMenu.form?type=linkedToTasks&contentObjectId="+${contentObject.id}+"&cacheStamp="+(stampDate.getTime()),
                        dataType: "xml",
                        success: function(data) {
                            var taskOptions = $(data).find("taskOptions");
                            if (taskOptions.length > 0) {
                                var taskOptions = $(data).find('taskOptions').text();
                                $('#taskSelect').html(taskOptions);
                                $('#taskSelect').addClass('style_select');
                                $('#taskSelect_menuTable').remove();
                                $("#taskSelect").styleActionElement();
                                $("#taskTypeSelect").styleActionElement();
                                $("#newTasklanguageSelect").styleActionElement();
                            }
                        }
                    });

                    $.ajax({
                        type: "GET",
                        url: context+"/getDataForSelectMenu.form?type=variantsForContentObject&contentObjectId="+${contentObject.id}+"&documentId="+${touchpointContext.id}+"&cacheStamp="+(stampDate.getTime()),
                        dataType: "xml",
                        success: function(data) {
                            if ($(data).find("varOptions").length > 0) {
                                var varOptions = $(data).find('varOptions').text();
                                $('#taskVariantSelect').html(varOptions);
                                $('#taskVariantSelect').addClass('style_select');
                                $('#taskVariantSelect_menuTable').remove();
                                $("#taskVariantSelect").styleActionElement();
                            }
                        }
                    });

                    actionSelected(46);
                }else{
                    submitAction('4');
                }
            }

            function iFrameView(path, eleId) {
                $("#" + eleId).iFramePopup($.extend({
                    src: path,
                    displayOnInit: true,
                    appliedParams: {tk: "${param.tk}"}
                }, iFramePopup_fullFrameAttr));
                iFramePopupReq(eleId);
            }

            function validateActionReq() {
            }

            function iFrameAction(actionSelect) {

                var actionId;

                if ($(actionSelect).find('option').length > 0) {

                    actionId = actionSelect.options[actionSelect.selectedIndex].id.replace('actioniFrame_', '');

                } else {

                    actionId = actionSelect;

                }

                var langArr = actionId.toString().split("_");
                if (langArr.length > 1)
                    actionId = langArr[1];
                if (actionId == '8') {	// upload variants
                    $('#actioniFrame_' + actionId).iFramePopup($.extend({
                        src: context + "/content/content_import.form",
                        displayOnInit: true,
                        appliedParams: {'tk': "${param.tk}", 'appId': "${contentObject.id}", 'expectType': '6'},
                        closeBtnId: "cancelBtn_button",
                        screenMask: false,
                        draggable: false,
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                            return true;
                        }
                    }, iFramePopup_fullFrameAttr));
                }
                if (actionId == '22') {	// Preview
                    $('#actioniFrame_' + langArr[0] + '_' + actionId).iFramePopup({
                        width: 600,
                        id: "messagePreviewFrame",
                        displayOnInit: true,
                        title: client_messages.title.preview,
                        src: context + "/touchpoints/touchpoint_content_object_preview.form",
                        appliedParams: {
                            documentId: "${touchpointContext.id}",
                            contentObjectId: "${param.contentObjectId}",
                            localeId: langArr[0],
                            tk: "${param.tk}"
                        },
                        closeBtnId: "cancelBtn_button",
                        screenMask: false,
                        draggable: false,
                        onSave: function () {
                            location.reload();
                        }
                    });
                }
                if (actionId == '28') {	// Move
                    $('#actioniFrame_0_' + actionId).iFramePopup({
                        width: 600,
                        id: "messageMoveFrame",
                        displayOnInit: true,
                        title: client_messages.title.move,
                        src: context + "/touchpoints/touchpoint_content_object_move_to_zone.form",
                        appliedParams: {
                            documentId: "${touchpointContext.id}",
                            contentObjectId: "${param.contentObjectId}",
                            tk: "${param.tk}"
                        },
                        closeBtnId: "cancelBtn_button",
                        screenMask: false,
                        draggable: false,
                        onSave: function () {
                            return true;
                        }
                    });
                }
                if (actionId == '38') {
                    // assetIds=sas-123_234+ili-245_231
                    var assetIds = 'msg-${param.contentObjectId}';
                    var parameters = {
                        tk: getParam('tk'),
                        assetIds: assetIds,
                        documentId: "${touchpointContext.id}",
                        metadataId: $('#taskMetadataSelect').val()
                    };

                    $editTaskModal.options.params = parameters;
                    $editTaskModal.showModal();

                    // Reset metadata select
                    $('#taskMetadataSelect').selectOption('taskMetadataSelect_0');
                    actionCancel();
                }
            }

            function initPriorityPopup(o) {

                var targetZoneId = -1;
                var target = $('#priorityMenu').length > 0 ? $('#priorityMenu') : $('#priorityBtn');
                if ($('#priorityMenu').length > 0)
                    targetZoneId = $(o).find('option:selected').val();
                else
                    targetZoneId = o;

                $(target).iFramePopup({
                    width: 900,
                    vertOffset: -85,
                    id: "messagePriorityFrame",
                    displayOnInit: true,
                    title: client_messages.title.manage_zone_message_priority,
                    src: context + "/content/content_object_zone_priority_edit.form",
                    appliedParams: {
                        zoneId: targetZoneId,
                        tk: "${param.tk}",
                        touchpointSelectionId: ${selectionContext}
                    },
                    closeBtnId: "cancelBtn_button",
                    screenMask: false,
                    draggable: false,
                    onSave: function () {
                        return true;
                    }
                });

            }

            function contentHistoryPopup() {
                var zoneId = -1;
                var zonePartId = -1;
                var localeId = -1;
                var $partSelect = $("select[id^='partSelect_zone']");
                var $contentLanguageSelect = $("#contentLanguageSelectcontentData");
                if($partSelect.length){
                    var zonePartValue = $partSelect.val();
                    if (zonePartValue) {
                        var zoneIdMatch = zonePartValue.match(/zone(\d+)/);
                        var zonePartIdMatch = zonePartValue.match(/part(\d+)/);
                        if (zoneIdMatch && zonePartIdMatch) {
                            zoneId = zoneIdMatch[1];
                            zonePartId = zonePartIdMatch[1];
                        }
                    }
                }
                if($contentLanguageSelect.length) {
                    localeId = $contentLanguageSelect.val();
                }

                $('#contentHistoryBtn').iFramePopup({
                    width: 800,
                    vertOffset: -50,
                    id: "contentHistoryFrame",
                    displayOnInit: true,
                    title: client_messages.title.content_history,

                    src: context + "/content/content_history.form",
                    appliedParams: {
                        zoneId: zoneId,
                        tk: "${param.tk}",
                        variantId: ${not empty variant?variant.id:-1},
                        modelId: ${contentObject.id},
                        zonePartId: zonePartId,
                        localeId: localeId
                    },
                    closeBtnId: "cancelBtn_button",
                    screenMask: false,
                    draggable: false,
                    onSave: function () {
                        return true;
                    }
                });
            }

            function variantContentComparePopup() {
                $('#variantContentCompareBtn').iFramePopup({
                    width: 800,
                    vertOffset: -50,
                    id: "contentCompareFrame",
                    displayOnInit: true,
                    title: client_messages.title.content_compare,
                    src: context + "/content/variant_content_compare.form",
                    appliedParams: {
                        zoneId: -1,
                        tk: "${param.tk}",
                        variantId: ${not empty variant?variant.id:-1},
                        modelId: ${contentObject.id},
                        zonePartId: -1,
                        objectType: 'message'
                    },
                    closeBtnId: "cancelBtn_button",
                    screenMask: false,
                    draggable: false,
                    onSave: function () {
                        return true;
                    }
                });
            }

            function changeNewContentTab(tabEle) {
                $("div[id^='newContentTab']").removeClass('tabSelected_minimal tab_minimal tabHov_minimal');
                $("div[id^='newContentTab']").addClass('tab_minimal');
                $("div[id^='newContentDiv']").hide();

                eleId = $(tabEle).attr('id');
                type = eleId.substring(eleId.indexOf('_') + 1);

                $('#newContentTab_' + type).removeClass('tab_minimal');
                $('#newContentTab_' + type).addClass('tabSelected_minimal');
                $('#newContentDiv_' + type).show();
                if (type == "dataValues") {
                    $('#isNewDataValueCollection').attr('checked', 'checked');
                } else {
                    sharedItemInit();
                    $('#isNewDataValueCollection').removeAttr('checked');
                }
            }

            function submitPreAction(submitId) {
                if (submitId == '33') {
                    var whereUsedReportId = $('#whereUsedReportId').val();
                    popItUpWithDimensions('../where_used_report.jsp?whereUsedReportId=' + whereUsedReportId, 850, 900);
                }
                return false;
            }

            function sharedItemInit() {
                var selectedSharedItemId = $('#sharedItemSelect').find(':selected').val();
                if (selectedSharedItemId == null)
                    return;
                currentSharedItemId = $('#currentSharedCollectionId').val();
                if (currentSharedItemId == selectedSharedItemId)
                    return;

                dataValuesTreeInit(selectedSharedItemId);
            }

            <c:if test="${showEditMenu }">

            function dataValuesTreeInit(sharedItemId) {
                $('#currentSharedCollectionId').val(sharedItemId);
                common.collectionDataValuesTreeInit('sharedPreviewDataValueTree', sharedItemId)
            }

            function targetSelectionNodeSelected(event, data) {
                var id = data.node != null ? data.node.id : "";
                $('#cloneToSelectionId').val(id);
                if (!_.isEmpty(id)) {
                    $('#continueBtnDisabled').hide();
                    $('#continueBtnEnabled').show();
                } else {
                    $('#continueBtnEnabled').hide();
                    $('#continueBtnDisabled').show();
                }

            }

            </c:if>

            function dataValueKeyEvent(type, index) {
                var nextIndex = parseInt(index) + 1;
                if ($('#dataValueInput_' + type + '_' + index).val().indexOf(',') != -1) {
                    while (exists('dataValueInput_' + type + '_' + nextIndex)) {
                        $('#dataValueInput_' + type + '_' + nextIndex).attr('disabled', 'disabled').val('');
                        styleInput($('#dataValueInput_' + type + '_' + nextIndex).get(0));
                        nextIndex++;
                    }
                } else {
                    while (exists('dataValueInput_' + type + '_' + nextIndex)) {
                        $('#dataValueInput_' + type + '_' + nextIndex).removeAttr('disabled');
                        styleInput($('#dataValueInput_' + type + '_' + nextIndex).get(0));
                        nextIndex++;
                    }
                }
            }

            function menuAction(_this) {

                var $selectedOption = $(_this).find('option:selected'),
                    selectedOptionId = $selectedOption.attr('id').split('_'),
                    actionType = selectedOptionId[0];


                if (actionType === 'actionOption') {

                    actionSelected(_this);

                } else if (actionType === 'actioniFrame') {

                    iFrameAction(_this);

                }
            }

            $(document).ready(function () {
                $('#taskVariantSelect').on('change', function() {
                    const selectedOption = $(this).find('option:selected');
                    console.log(selectedOption.attr('data-clean'))
                    $('#taskVariantSelect_menuTable .actionSelectMenuText.selectedOption').text(selectedOption.attr('data-clean'));
                });
            });

            // Init Javascript
            $(function () {

                if (getParam('nprSaveSuccess') == "true")
                    getTopFrame().location.reload();

                $("#messageSelect,#taskMetadataSelect").styleActionElement({maxItemDisplay: 13});

                $('#taskIndicator').taskManager({
                    item_type: ${contentObject.isTouchpointLocal ? 6 : 2},
                    item_id: ${contentObject.id},
                    popup_location: 'right'
                });

                $("#workflowHistoryButton").each(function () {
                    if (!$(this).hasClass('actionBtn_disabled')) {
                        $(this)
                            .click(function () {
                                popupWorkflowIframe(this, '16', '3', '${contentObject.id}', false);
                                $("body").append("<div class=\"iFramePopupSubPageScreen\"></div>");
                                $('.iFramePopupSubPageScreen').css({
                                    height: $(window.document).height(),
                                    opacity: 0
                                }).fadeTo(1000, 0.55);
                            });
                    }
                });

                $("#variantContentCompareBtn").each(function () {
                    if (!$(this).hasClass('actionBtn_disabled')) {
                        $(this)
                            .click(function () {
                                variantContentComparePopup();
                            });
                    }
                });

                // Previews status polling init
                $("[id*='itemInProcess']").actionStatusPolling({
                    itemLabel: client_messages.text.preview,
                    type: 'preview',
                    postItemInit: function ($item) {
                        var $option = $('#' + $item.attr('id')),
                            $select = $option.parent(),
                            itemIndex = $select.children().index($option),
                            $dropdownItem = $select.siblings('.dropdown').find('.dropdown-content').children().eq(itemIndex);

                        if ($item.attr('id').indexOf('itemInProcess') < 0)
                            $dropdownItem.find('.progress-loader').remove();
                        else
                            $dropdownItem.prepend('<div class="progress-loader mr-2">' +
                                '<i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>' +
                                '</div>');

                    },
                    onInit: function (o) {
                        var $option = $('#' + o.option.attr('id')),
                            $select = $option.parent(),
                            itemIndex = $select.children().index($option),
                            $dropdown = $select.siblings('.dropdown'),
                            $dropdownItem = $dropdown.find('.dropdown-content').children().eq(itemIndex);

                        $dropdownItem.prepend('<div class="progress-loader mr-2">' +
                            '<i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>' +
                            '</div>');


                        // TODO: REVIEW RENDER/PAINT ISSUE WITH MULTIPLE SPINNING (ANIMATED) ELEMENTS
                        /*$dropdown.children('.dropdown-toggle').prepend('<div class="progress-loader mr-2 bg-transparent p-0">' +
                            '<i class="progress-loader-icon far fa-spinner-third text-white" aria-hidden="true"></i>' +
                            '</div>');*/
                    }
                });
                getTopFrame().previewReturnURL = window.location.href;

                // Init iframe action selected
                $("[id^='menuOption_actioniFrame']").click(function () {
                    if ($(this).is('.actionOptionDisabled'))
                        return;
                    var actionId = this.id.replace('menuOption_actioniFrame_', '');
                    iFrameAction(actionId);
                });

                validateActionReq();

                $('#contentObjectViewCancelBtnGroup').on('change', function () {

                    var selectedValue = $(this).val();

                    if (selectedValue === 'cancelandexit') {
                        submitAction(99, this);
                    }
                });

            });
        </script>
    </msgpt:Script>

    <input type="checkbox" checked="checked" style="display: none;" id="listItemCheck_${contentObject.id}"/>
    <input type="hidden" id="typeMessageView"/>
    <c:set var="isArchive" value="${command.archive}" scope="request"/>

    <c:set var="approvePerm" value="false" scope="request"/>
    <msgpt:IfAuthGranted authority="ROLE_MESSAGE_APPROVE_EDIT">
        <c:set var="approvePerm" value="true" scope="request"/>
    </msgpt:IfAuthGranted>
    <c:set var="editPerm" value="false" scope="request"/>
    <msgpt:IfAuthGranted authority="ROLE_MESSAGE_EDIT">
        <c:set var="editPerm" value="true" scope="request"/>
    </msgpt:IfAuthGranted>
    <c:set var="statusViewType" value="${(command.active || command.archive) ? 2 : 1}" scope="request"/>

    <c:choose>
        <c:when test="${showEditMenu}">

            <form:hidden path="actionValue" id="actionElement"/>
            <form:hidden path="exportId" id="exportId"/>
            <form:hidden path="exportName" id="exportName"/>
            <div class="position-relative">
                <div class="border-bottom" data-sticky="top"
                     data-fixedclass="container px-0 box-shadow-4 bg-white border-white rounded-top">
                    <div class="d-flex align-items-center mx-4 py-3 bg-white rounded-top">
                        <div class="d-flex align-items-center">
                            <div class="btn-group border-separate mr-3">
                                <button id="updateBtn" type="button" class="btn btn-dark post-trigger"
                                        onclick="submitAction(1,this);" ${!command.canUpdate ? 'disabled' : ''}>
                                    <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                    <fmtSpring:message code="action.button.label.update"/>
                                </button>
                                <button id="cloneBtn" type="button" class="btn btn-dark"
                                        onclick="${contentObject.variantType ? 'actionSelected(12)' : 'actionSelected(23)'}" ${!command.canClone ? 'disabled' : ''}>
                                    <i class="far fa-clone mr-2" aria-hidden="true"></i>
                                    <fmtSpring:message code="page.label.clone"/>
                                </button>
                                <c:if test="${not contentObject.structuredContentEnabled && not touchpointContext.smsTouchpoint && not isArchive && contentObject.objectType == 0}">
                                    <div class="btn-group">
                                        <select title="${msgpt:getMessage('page.label.preview')}" id="previewMenu"
                                                class="complex-dropdown-select"
                                                onchange="infoItemAction(this,-1)"
                                                aria-label="${msgpt:getMessage('page.label.preview')}"
                                                data-toggle="complex-dropdown"
                                                data-action="menu"
                                                data-dropdown-class="btn-dark">
                                            <c:forEach var="locale" items="${locales}" varStatus="langStat">
                                                <option id="actioniFrame_${locale.id}_22"><fmtSpring:message
                                                        code="page.label.request"/><c:out value=" "/>
                                                    <c:out value="${fn:length(locales) > 1 ? locale.name : ''}"/>
                                                    <c:if test="${fn:length(locales) > 1}">
                                                        <c:out value=" "/>
                                                    </c:if>
                                                    <fmtSpring:message code="page.text.preview"/></option>
                                                <c:forEach var="currentPreview" items="${previews[locale.id]}"
                                                           varStatus="previewStat">
                                                    <c:choose>
                                                        <c:when test="${not currentPreview.complete && not currentPreview.error}"> <!-- Preview in process -->
                                                            <option id="itemInProcess_${currentPreview.id}"
                                                                    type="inProcess" disabled>
                                                                <fmtSpring:message code="page.label.processing"/> -
                                                                <fmtJSTL:formatDate
                                                                        value="${currentPreview.requestDate}"
                                                                        pattern="${dateTimeFormat}"/>
                                                            </option>
                                                        </c:when>
                                                        <c:when test="${currentPreview.error}"> <!-- Preview error -->
                                                            <option id="itemError_${currentPreview.id}"
                                                                    class="text-danger"
                                                                    deliveryId="${currentPreview.deliveryEvent.id}"
                                                                    itemClass="${currentPreview.class.name}">
                                                                <fmtSpring:message code="page.label.view.log"/> -
                                                                <fmtJSTL:formatDate
                                                                        value="${currentPreview.requestDate}"
                                                                        pattern="${dateTimeFormat}"/>
                                                            </option>
                                                        </c:when>
                                                        <c:otherwise> <!-- Report -->
                                                            <c:choose>
                                                                <c:when test="${currentPreview.isEmailOrWebPreview}"> <!-- HTML -->
                                                                    <option id="itemHtml_${currentPreview.id}"
                                                                            value="${currentPreview.outputPath}"
                                                                            type="preview"
                                                                            width="${currentPreview.emailTemplateWidth}"
                                                                            height="${currentPreview.emailTemplateHeight}">
                                                                        <fmtSpring:message
                                                                                code="page.label.view.preview"/> -
                                                                        <fmtJSTL:formatDate
                                                                                value="${currentPreview.requestDate}"
                                                                                pattern="${dateTimeFormat}"/>
                                                                    </option>
                                                                </c:when>
                                                                <c:otherwise> <!-- PDF -->
                                                                    <option id="itemPDF_${currentPreview.id}"
                                                                            value="${currentPreview.resourceToken}">
                                                                        <fmtSpring:message
                                                                                code="page.label.view.preview"/> -
                                                                        <fmtJSTL:formatDate
                                                                                value="${currentPreview.requestDate}"
                                                                                pattern="${dateTimeFormat}"/>
                                                                    </option>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </c:forEach>
                                                <c:if test="${langStat.count != fn:length(locales)}">
                                                    <option class="divider" disabled/>
                                                </c:if>
                                            </c:forEach>
                                        </select>
                                    </div>
                                </c:if>
                            </div>
                            <c:if test="${hasTaskViewPermission}">
                                <div id="taskIndicator"
                                     class="position-relative ml-1 fs-md actionBarTaskIndicator"></div>
                            </c:if>
                        </div>
                        <div class="d-flex ml-auto">
                            <button type="button" class="cancelBtn btn btn-outline-light text-body post-trigger"
                                    onclick="submitAction(99, this);">
                                <i class="far fa-times-circle mr-2" aria-hidden="true"></i><fmtSpring:message
                                    code="page.label.cancel"/>
                            </button>
                            <div class="cancelBtnGroup btn-group d-none">
                                <button type="button"
                                        class="btn btn-outline-light border-right-0 text-body btn-placeholder"
                                        disabled>
                                    <i class="fad fa-spinner-third fa-lg fa-spin align-middle mx-3"
                                       aria-hidden="true"></i>
                                </button>
                                <select id="contentObjectViewCancelBtnGroup"
                                        class="complex-combobox-select"
                                        aria-label="${msgpt:getMessage("page.label.actions")}"
                                        data-toggle="complex-combobox"
                                        data-combobox-class="btn btn-outline-light border-right-0 text-body">
                                    <option id="btnCancelAndExit" value="cancelandexit"
                                            data-icon="far fa-times-circle"><fmtSpring:message
                                            code="page.label.cancel_and_go_to_list"/>
                                    </option>
                                    <option id="btnCancelAndGoBack" value="goback"
                                            class="d-none"
                                            data-icon="far fa-left"><fmtSpring:message
                                            code="page.label.cancel_and_go_back"/>
                                    </option>
                                </select>
                                <button type="button"
                                        class="btn btn-outline-light border-left-0 text-body dropdown-toggle dropdown-toggle-split"
                                        data-toggle="dropdown" aria-haspopup="true"
                                        aria-expanded="false" disabled>
                                                        <span class="sr-only"><fmtSpring:message
                                                                code="page.text.toggle.dropdown"/></span>
                                </button>
                            </div>
                            <div class="btn-group border-separate ml-3">
                                <div class="btn-group">
                                    <select title="${msgpt:getMessage('page.label.actions')}" id="actionMenu"
                                            class="complex-dropdown-select"
                                            onchange="menuAction(this)"
                                            aria-label="${msgpt:getMessage('page.label.actions')}"
                                            data-toggle="complex-dropdown"
                                            data-action="menu"
                                            data-dropdown-class="btn-dark">
                                        <option id="actionOption_1" ${!command.canCreateWorkingCopy ? 'disabled' : ''}>
                                            <fmtSpring:message code="page.label.create.working.copy"/></option>
                                        <c:choose>
                                            <c:when test="${not isArchive}">
                                                <c:choose>
                                                    <c:when test="${command.hasNoStepForWorkflow}"> <!-- No workflow step, activate directly -->
                                                        <option id="actionOption_16" ${!command.canReleaseForApproval ? 'disabled' : ''}>
                                                            <fmtSpring:message code="page.label.activate"/></option>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <option id="actionOption_6" ${!command.canReleaseForApproval ? 'disabled' : ''}>
                                                            <fmtSpring:message
                                                                    code="page.label.release.for.approval"/></option>
                                                        <c:choose>
                                                            <c:when test="${command.workflowOwner}">
                                                                <option id="actionOption_21" ${!(command.canApprove and contentObject.inApprovalStep) ? 'disabled' : ''}>
                                                                    <fmtSpring:message
                                                                            code="page.label.approve.and.override"/></option>
                                                                <option id="actionOption_29" ${!(command.canReleaseFromTranslation and contentObject.inTranslationStep) ? 'disabled' : ''}>
                                                                    <fmtSpring:message
                                                                            code="page.label.release.from.translation"/></option>
                                                                <option id="actionOption_22" ${!command.canReject ? 'disabled' : ''}>
                                                                    <fmtSpring:message
                                                                            code="page.label.reject.and.override"/></option>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <option id="actionOption_19" ${!(command.canApprove and contentObject.inApprovalStep) ? 'disabled' : ''}>
                                                                    <fmtSpring:message
                                                                            code="page.label.approve"/></option>
                                                                <option id="actionOption_29" ${!(command.canReleaseFromTranslation and contentObject.inTranslationStep and command.currentTranslator) ? 'disabled' : ''}>
                                                                    <fmtSpring:message
                                                                            code="page.label.release.from.translation"/></option>
                                                                <option id="actionOption_20" ${!command.canReject ? 'disabled' : ''}>
                                                                    <fmtSpring:message
                                                                            code="page.label.reject"/></option>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </c:otherwise>
                                                </c:choose>
                                                <option id="actionOption_42" ${!command.canAbort ? 'disabled' : ''}>
                                                    <fmtSpring:message code="page.label.abort"/></option>
                                                <option id="actionOption_45" ${!command.canRetry ? 'disabled' : ''}>
                                                    <fmtSpring:message code="page.label.retry.translation"/></option>
                                                <option id="actionOption_2" ${!command.canDiscard ? 'disabled' : ''}>
                                                    <fmtSpring:message code="page.label.discard.working.copy"/></option>
                                                <option class="divider" disabled/>
                                                <c:if test="${not contentObject.inTranslationStep}">
                                                    <option id="actionOption_5" ${!command.canReassign ? 'disabled' : ''}>
                                                        <fmtSpring:message code="page.label.reassign"/></option>
                                                </c:if>
                                                <c:if test="${contentObject.inTranslationStep}">
                                                    <option id="actionOption_43" ${!command.canTranslationReassign ? 'disabled' : ''}>
                                                        <fmtSpring:message code="page.label.reassign"/></option>
                                                </c:if>
                                                <c:if test="${not contentObject.isTouchpointLocal}">
                                                    <c:if test="${command.canMove}">
                                                        <option id="actioniFrame_0_28"><fmtSpring:message
                                                                code="page.label.move"/></option>
                                                    </c:if>
                                                    <c:if test="${contentObject.structuredContentEnabled}">
                                                        <c:if test="${!command.canUnhold}">
                                                            <option id="actionOption_24" ${!command.canHold ? 'disabled' : ''}>
                                                                <fmtSpring:message code="page.label.hold"/></option>
                                                        </c:if>
                                                        <c:if test="${command.canUnhold}">
                                                            <option id="actionOption_25"><fmtSpring:message
                                                                    code="page.label.unhold"/></option>
                                                        </c:if>
                                                    </c:if>
                                                    <msgpt:IfAuthGranted authority="ROLE_MESSAGE_ACTIVATION">
                                                        <c:if test="${!command.canRestore}">
                                                            <option id="actionOption_26" ${!command.canSuppress ? 'disabled' : ''}>
                                                                <fmtSpring:message code="page.label.suppress"/></option>
                                                        </c:if>
                                                        <c:if test="${command.canRestore}">
                                                            <option id="actionOption_27"><fmtSpring:message
                                                                    code="page.label.restore"/></option>
                                                        </c:if>
                                                    </msgpt:IfAuthGranted>
                                                </c:if>
                                                <msgpt:IfAuthGranted authority="ROLE_MESSAGE_ACTIVATION">
                                                    <option id="actionOption_3" ${!command.canArchive ? 'disabled' : ''}>
                                                        <fmtSpring:message code="page.label.archive"/></option>
                                                </msgpt:IfAuthGranted>
                                                <c:if test="${contentObject.objectType > 0 && contentObject.contentTypeId != 8}">
                                                    <option id="actionOption_33"><fmtSpring:message
                                                            code="page.label.where.used"/></option>
                                                </c:if>
                                                <c:if test="${command.selectable}">
                                                    <option id="actionOption_41"><fmtSpring:message
                                                            code="page.label.download.variants"/></option>
                                                </c:if>
                                                <c:if test="${command.selectable == true && command.canUpdate == true}">
                                                    <option id="actioniFrame_8" ${!command.canUpdate ? 'disabled' : ''}>
                                                        <fmtSpring:message code="page.label.upload.variants"/></option>
                                                    <option id="actionOption_30" ${!command.canUpdate ? 'disabled' : ''}>
                                                        <fmtSpring:message code="page.label.add.variant"/></option>
                                                    <option id="actionOption_31" ${!command.canUpdate ? 'disabled' : ''}>
                                                        <fmtSpring:message code="page.label.copy.content.to"/></option>
                                                </c:if>
                                            </c:when>
                                            <c:otherwise>
                                                <msgpt:IfAuthGranted authority="ROLE_MESSAGE_ACTIVATION">
                                                    <option id="actionOption_4"><fmtSpring:message
                                                            code="page.label.delete.archive"/></option>
                                                </msgpt:IfAuthGranted>
                                                <c:if test="${command.selectable}">
                                                    <option id="actionOption_41"><fmtSpring:message
                                                            code="page.label.download.variants"/></option>
                                                </c:if>
                                            </c:otherwise>
                                        </c:choose>
                                        <option class="divider" disabled/>
                                        <c:if test="${not hasTaskMetadataFormDef}">
                                            <option id="actioniFrame_38" ${!command.canAddTask ? 'disabled' : ''}>
                                                <fmtSpring:message code="page.label.add.task"/>
                                            </option>
                                        </c:if>
                                        <c:if test="${hasTaskMetadataFormDef}">
                                            <option id="actionOption_39" ${!command.canAddTask ? 'disabled' : ''}>
                                                <fmtSpring:message code="page.label.add.task"/>
                                            </option>
                                        </c:if>
                                    </select>
                                </div>
                                <msgpt:IfAuthGranted authority="ROLE_PRIORITY_ADMIN">
                                    <!-- PRIORITY -->
                                    <c:if test="${not empty contentObject.zone}">
                                        <button type="button" id="priorityBtn" class="btn btn-dark"
                                                onclick="initPriorityPopup(${contentObject.zone.id})">
                                            <fmtSpring:message code="page.label.prioritize"/>
                                        </button>
                                    </c:if>
                                </msgpt:IfAuthGranted>
                                <button type="button" class="btn btn-dark" id="contentHistoryBtn"
                                        onclick="contentHistoryPopup()">
                                    <fmtSpring:message code="page.label.history"/>
                                </button>
                            </div>
                            <div class="btn-group border-separate ml-3">
                                <c:if test="${contentObject.dynamicVariantEnabled || contentObject.structuredContentEnabled}">
                                    <button id="variantContentCompareBtn" type="button"
                                            class="btn btn-dark" data-toggle="tooltip"
                                            aria-label="${msgpt:getMessage('page.label.content.compare')}"
                                            title="${msgpt:getMessage('page.label.content.compare')}">
                                        <i class="far fa-exchange fs-md" aria-hidden="true"></i>
                                    </button>
                                </c:if>
                                <button id="workflowHistoryButton" type="button"
                                        class="btn btn-dark" data-toggle="tooltip"
                                        aria-label="${msgpt:getMessage('page.label.workflow.history')}"
                                        title="${msgpt:getMessage('page.label.workflow.history')}">
                                    <i class="far fa-tasks fs-md" aria-hidden="true"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <a id="actionLabel_${contentObject.id}" itemName="${contentObject.name}"
               style="display: none;">${contentObject.name}</a>

            <!-- POPUP DATA -->
            <div id="actionSpecs" style="display: none;">
                <!-- ACTIONS POPUP DATA -->
                <div id="actionSpec_1" type="simpleConfirm" submitId="4"> <!-- Create working copy -->
                    <div id="actionTitle_1"><fmtSpring:message code="page.label.confirm.create.working.copy"/></div>
                    <div id="actionInfo_1"><p><b><fmtSpring:message code="page.text.create.message.working.copy"/></b>
                    </p></div>
                    <div id="actionContinueToTaskSelectButtons_1"></div>
                </div>
                <div id="actionSpec_2" type="simpleConfirm" submitId="5"> <!-- Discard working copy -->
                    <div id="actionTitle_2"><fmtSpring:message code="page.label.confirm.discard.working.copy"/></div>
                    <div id="actionInfo_2"><p><b><fmtSpring:message code="page.text.discard.message.working.copy"/></b>
                    </p></div>
                </div>
                <div id="actionSpec_3" submitId="6"> <!-- Archive -->
                    <div id="actionTitle_3"><fmtSpring:message code="page.label.confirm.archive"/></div>
                    <div id="actionInfo_3"><p><b><fmtSpring:message code="page.text.archive.message.active"/></b></p>
                    </div>
                    <div id="actionNote_3" required="true"><fmtSpring:message
                            code="page.text.note.required.brackets"/></div>
                </div>
                <div id="actionSpec_4" type="simpleConfirm" submitId="7"> <!-- Delete archive -->
                    <div id="actionTitle_4"><fmtSpring:message code="page.label.confirm.delete.archive"/></div>
                    <div id="actionInfo_4"><p><b><fmtSpring:message code="page.text.delete.message.archive"/></b></p>
                    </div>
                </div>
                <div id="actionSpec_5" submitId="3"> <!-- Reassign to user -->
                    <div id="actionTitle_5"><fmtSpring:message code="page.label.confirm.reassign.to.user"/></div>
                    <div id="actionInfo_5"><p><b><fmtSpring:message code="page.text.assign.message.to.user"/></b></p>
                    </div>
                    <div id="actionNote_5"></div>
                    <div id="actionUserSelect_5" required="true" type="currentStageUsers"></div>
                </div>
                <div id="actionSpec_43" submitId="43"> <!-- Transalation reassign to user -->
                    <div id="actionTitle_43"><fmtSpring:message code="page.label.confirm.reassign.to.user"/></div>
                    <div id="actionInfo_43"><p><b><fmtSpring:message code="page.text.assign.message.to.user"/></b></p>
                    </div>
                    <div id="actionNote_43"></div>
                    <div id="actionWorkflowSelect_43" required="true" type="messages_releaseFromTransWorkflows"></div>
                    <div id="actionUserSelect_43" required="true" type="currentTranslators"></div>
                </div>
                <div id="actionSpec_6" submitId="9"> <!-- Release for approval -->
                    <div id="actionTitle_6"><fmtSpring:message code="page.label.confirm.release.for.approval"/></div>
                    <div id="actionInfo_6"><p><b><fmtSpring:message
                            code="page.text.provide.description.of.action.required"/></b></p></div>
                    <div id="actionNote_6"></div>
                </div>
                <div id="actionSpec_16" submitId="10"> <!-- Activate -->
                    <div id="actionTitle_16"><fmtSpring:message code="page.label.confirm.activate"/></div>
                    <div id="actionInfo_16"><p><b><fmtSpring:message code="page.text.activate.this.message"/></b></p>
                    </div>
                    <div id="actionNote_16"></div>
                </div>
                <!-- NEW MESSAGE POPUP DATA -->
                <div id="actionSpec_10" submitId="15" contentWidth="320px"> <!-- New message -->
                    <div id="actionTitle_10"><fmtSpring:message code="page.label.new.message"/></div>
                    <div id="actionInfo_10"><p><b><fmtSpring:message
                            code="page.text.select.content.type.for.message"/></b></p></div>
                    <div id="actionNewMessage_10"></div>
                </div>
                <!-- CLONE -->
                <div id="actionSpec_12" submitId="2" contentWidth="330px"> <!-- Clone -->
                    <div id="actionTitle_12"><fmtSpring:message code="page.label.clone.message"/></div>
                    <div id="actionInfo_12">
                        <c:choose>
                        <c:when test="${not contentObject.structuredContentEnabled}">
                        <p><b><fmtSpring:message code="page.text.select.target.variant.for.message.clone"/></b></p>
                    </div>
                    </c:when>
                    <c:otherwise>
                    <p><b><fmtSpring:message code="page.text.would.you.like.to.clone.message"/></b></p></div>
                </c:otherwise>
                </c:choose>

                <div id="actionClone_12" required="true"></div>
                <c:if test="${not contentObject.structuredContentEnabled}">
                    <div id="actionTargetSelections_12"></div>
                </c:if>
            </div>
            <!-- APPROVE MESSAGE -->
            <div id="actionSpec_19" type="simpleConfirm" submitId="19" contentWidth="330px"> <!-- Approve -->
                <div id="actionTitle_19"><fmtSpring:message code="page.label.approve"/></div>
                <div id="actionInfo_19"><p><b><fmtSpring:message code="page.text.approve.asset"/></b></p></div>
                <div id="actionNote_19"></div>
                <div id="actionWorkflowSelect_19" required="true" type="messages_approveWorkflows"></div>
                <div id="actionApproval_19" approveSubmitId="19"></div>
            </div>
            <!-- Translation Step approve -->
            <div id="actionSpec_29" type="simpleConfirm" submitId="19" contentWidth="330px">
                <!-- Approve -->
                <div id="actionTitle_29"><fmtSpring:message code="page.label.approve.workflow.translation"/></div>
                <div id="actionInfo_29"><p><b><fmtSpring:message
                        code="page.text.workflow.step.release.from.translation"/></b></p></div>
                <div id="actionWorkflowSelect_29" required="true" type="messages_releaseFromTransWorkflows"></div>
                <div id="actionReleaseFromTranslation_29" approveSubmitId="19"></div>
            </div>
            <!-- APPROVE AND OVERRIDE MESSAGE -->
            <div id="actionSpec_21" type="simpleConfirm" submitId="19" contentWidth="330px"><!-- Approve and override-->
                <div id="actionTitle_21"><fmtSpring:message code="page.label.approve.workflow.owner"/></div>
                <div id="actionInfo_21"><p><b><fmtSpring:message code="page.text.workflow.owners.may.approve.step"/></b>
                </p></div>
                <div id="actionWorkflowSelect_21" required="true" type="messages_approveWorkflows"></div>
                <div id="actionApproval_21" approveSubmitId="19"></div>
            </div>
            <!-- REJECT MESSAGE -->
            <div id="actionSpec_20" type="simpleConfirm" submitId="20" contentWidth="330px"> <!-- Reject -->
                <div id="actionTitle_20"><fmtSpring:message code="page.label.reject"/></div>
                <div id="actionInfo_20"><p><b><fmtSpring:message code="page.text.would.you.like.reject"/></b></p></div>
                <div id="actionNote_20" required="true"><fmtSpring:message
                        code="page.text.note.required.to.reject.brackets"/></div>
                <div id="actionWorkflowSelect_20" required="true" type="messages_rejectWorkflows"></div>
                <div id="actionUserSelect_20" required="true" type="message_rejectToUsers"></div>
                <div id="actionReject_20" rejectSubmitId="20"></div>
            </div>
            <!-- REJECT AND OVERRIDE MESSAGE -->
            <div id="actionSpec_22" type="simpleConfirm" submitId="20" contentWidth="330px"><!-- Reject and override -->
                <div id="actionTitle_22"><fmtSpring:message code="page.label.reject.workflow.owner"/></div>
                <div id="actionInfo_22"><p><b><fmtSpring:message code="page.text.workflow.owners.may.reject.step"/></b>
                </p></div>
                <div id="actionNote_22" required="true"><fmtSpring:message
                        code="page.text.note.required.to.reject.brackets"/></div>
                <div id="actionWorkflowSelect_22" required="true" type="messages_rejectWorkflows"></div>
                <div id="actionUserSelect_22" required="true" type="message_rejectToUsers"></div>
                <div id="actionReject_22" rejectSubmitId="20"></div>
            </div>
            <div id="actionSpec_42" type="simpleConfirm" submitId="42" contentWidth="330px"> <!-- Abort workflow -->
                <div id="actionTitle_42"><fmtSpring:message code="page.label.abort"/></div>
                <div id="actionInfo_42"><p><b><fmtSpring:message
                        code="page.text.would.you.like.to.abort"/></b></p></div>
                <div id="actionNote_42"></div>
                <div id="actionAbort_42" abortSubmitId="42"></div>
            </div>
            <div id="actionSpec_45" type="simpleConfirm" submitId="45" contentWidth="330px"> <!-- Retry translation -->
                <div id="actionTitle_45"><fmtSpring:message code="page.label.retry.translation"/></div>
                <div id="actionInfo_45"><p><b><fmtSpring:message
                        code="page.text.would.you.like.to.retry.translation"/></b></p></div>
                <div id="actionNote_45"></div>
                <div id="actionWorkflowSelect_45" required="true" type="messages_retryWorkflows"></div>
            </div>
            <div id="actionSpec_46" type="simpleConfirm" submitId="4" contentWidth="400px"> <!-- Link task -->
                <div id="actionTitle_46"><fmtSpring:message code="page.label.link.task"/></div>
                <div id="actionInfo_46"><p><b><fmtSpring:message
                        code="page.text.would.you.like.to.link.with.an.existing.task"/></b></p></div>
                <div id="actionTaskSelect_46" required="true" type="linkedToTasks"></div>
                <c:if test="${requiresDescriptionOnTasks}">
                    <div id="actionNote_46" required="true"><fmtSpring:message
                            code="page.text.description.required.to.create.new.task.brackets"/></div>
                </c:if>
                <c:if test="${!requiresDescriptionOnTasks}">
                    <div id="actionNote_46"></div>
                </c:if>
            </div>
            <c:if test="${contentObject.structuredContentEnabled}">
                <!-- Hold -->
                <div id="actionSpec_24" submitId="24"> <!-- Hold -->
                    <div id="actionTitle_24"><fmtSpring:message code="page.label.confirm.hold"/></div>
                    <div id="actionInfo_24"><p><b><fmtSpring:message
                            code="page.text.on.hold.messages.not.used.in.production"/></b></p></div>
                    <div id="actionSuppress_24"></div>
                </div>
                <!-- Unhold -->
                <div id="actionSpec_25" submitId="25"> <!-- Unhold -->
                    <div id="actionTitle_25"><fmtSpring:message code="page.label.confirm.unhold"/></div>
                    <div id="actionInfo_25"><p><b><fmtSpring:message
                            code="page.text.message.now.used.in.production"/></b></p></div>
                    <div id="actionUpdateVariantVersioning_25"></div>
                </div>
            </c:if>
            <msgpt:IfAuthGranted authority="ROLE_MESSAGE_ACTIVATION">
                <!-- Suppress -->
                <div id="actionSpec_26" submitId="26"> <!-- Suppress -->
                    <div id="actionTitle_26"><fmtSpring:message code="page.label.confirm.suppress"/></div>
                    <div id="actionInfo_26"><p><b><fmtSpring:message code="page.text.suppress.message"/></b></p></div>
                </div>
                <!-- Restore -->
                <div id="actionSpec_27" submitId="27"> <!-- Restore -->
                    <div id="actionTitle_27"><fmtSpring:message code="page.label.confirm.restore"/></div>
                    <div id="actionInfo_27"><p><b><fmtSpring:message code="page.text.restore.message"/></b></p></div>
                </div>
            </msgpt:IfAuthGranted>
            <!-- Clone -->
            <div id="actionSpec_23" submitId="2" contentWidth="500px"> <!-- Clone -->
                <div id="actionTitle_23"><fmtSpring:message code="page.label.confirm.clone"/></div>
                <div id="actionInfo_23"><p><b><fmtSpring:message code="page.text.would.you.like.to.clone.message"/></b>
                </p></div>
                <div id="actionClone_23" required="true"></div>
            </div>
            <!-- Export Variants -->
            <div id="actionSpec_41" submitId="41"> <!-- Export -->
                <div id="actionTitle_41"><fmtSpring:message code="page.label.download.variants"/></div>
                <div id="actionInfo_41"><p><b><fmtSpring:message
                        code="page.text.would.you.like.to.download.variants"/></b></p></div>
                <div id="actionExtTypeSelect_41"></div>
            </div>
            <!-- SELCTABLE CONTENT ACTION -->
            <div id="actionSpec_30" submitId="30" contentWidth="420px"> <!-- Add variant -->
                <div id="actionTitle_30"><fmtSpring:message code="page.label.add.variant"/></div>
                <div id="actionInfo_30"><b><fmtSpring:message code="page.label.variant"/>:</b>
                    <c:choose>
                        <c:when test="${command.contentTreeNode.parameterGroup.parameters.size() > 1}">
                            <c:out value="${command.contentTreeNode.name}"/>
                        </c:when>
                        <c:otherwise>
                            <fmtSpring:message code="client_messages.text.master" />
                        </c:otherwise>
                    </c:choose>
                </div>
                <div id="actionNewSelectableContent_30"></div>
            </div>
            <!-- Copy content to -->
            <c:if test="${command.selectable == true && command.canUpdate == true}">
                <div id="actionSpec_31" submitId="28">
                    <div id="actionTitle_31"><fmtSpring:message code="page.label.copy.content.to"/></div>
                    <div id="actionInfo_31"><p><b><fmtSpring:message
                            code="page.text.select.variant.to.copy.content.to"/><br>
                        <em><fmtSpring:message code="page.text.all.variant.content.will.be.replaced"/></em></b></p>
                    </div>
                    <div id="actionCopyContentTo_31"></div>
                </div>
            </c:if>
            <!-- Where Used -->
            <div id="actionSpec_33" submitId="33"> <!-- Where used -->
                <div id="actionTitle_33"><fmtSpring:message code="page.label.confirm.generate.where.used"/></div>
                <div id="actionInfo_33"><fmtSpring:message code="page.text.report.loads.in.separate.window"/></div>
                <div id="actionWhereUsedOptions_33"></div>
            </div>
            <div id="actionSpec_39"> <!-- Add task with metadata -->
                <div id="actionTitle_39"><fmtSpring:message code="page.label.add.task"/></div>
                <div id="actionTaskMetadataSelect_39"></div>
                <div id="actionTaskMetadataButtons_39"></div>
            </div>
            </div>

            <!-- POPUP INTERFACE -->
            <form:hidden path="whereUsedReportId" id="whereUsedReportId"/>
            <msgpt:Popup id="actionPopup" theme="minimal">
                <div id="actionPopupInfoFrame">
                    <div id="actionPopupInfo">&nbsp;</div>
                </div>
                <div id="actionPopupExtTypeSelect" style="padding: 2px 8px 6px 8px;" align="center">
                    <form:select id="extType" path="extType" cssClass="inputL">
                        <option id="0" value="0"><fmtSpring:message code="page.label.xml"/></option>
                        <c:if test="${variantExcelExportSettingEnabled}">
                            <option id="1" value="1"><fmtSpring:message code="page.label.excel"/></option>
                        </c:if>
                    </form:select>
                </div>
                <div id="actionPopupWorkflowSelect">
                    <br>
                    <div class="formControl">
                        <div class="controlWrapper">
                            <form:select id="workflowSelect" path="actionToWorkflow" cssClass="inputL"
                                         onchange="selectWorkflow()" onkeyup="selectWorkflow()" >
                                <option id="0" value="0"><fmtSpring:message
                                        code="page.text.loading"/></option>
                            </form:select>
                        </div>
                    </div>
                </div>
                <div id="actionPopupUserSelect">
                    <br>
                    <div class="formControl">
                        <div class="controlWrapper">
                            <form:select id="userSelect" path="assignedToUser" cssClass="inputL"
                                         onchange="validatePopupReq()"
                                         onkeyup="validatePopupReq()">
                                <option id="0" value="0"><fmtSpring:message code="page.text.loading"/></option>
                            </form:select>
                        </div>
                    </div>
                </div>
                <div id="actionPopupClone">
                    <div class="formControl">
                        <label><span class="labelText"><fmtSpring:message code="page.label.name"/></span></label>
                        <div class="controlWrapper">
                            <msgpt:InputFilter type="simpleName">
                                <form:input cssClass="inputL" path="cloneName" onkeyup="validatePopupReq();"
                                            onchange="validatePopupReq();" id="clone_newInstanceName"
                                            onfocus="this.select()"/>
                            </msgpt:InputFilter>
                        </div>
                    </div>
                </div>
                <div id="actionPopupNewMessage">
                    <div class="formControl">
                        <label><span class="labelText"><fmtSpring:message code="page.text.content.type"/></span></label>
                        <div class="controlWrapper">
                            <form:select path="contentType" items="${contentTypes}" itemLabel="name" itemValue="id"/>
                        </div>
                    </div>
                </div>
                <div id="actionPopupTargetSelections">
                    <form:hidden id="cloneToSelectionId" path="cloneToSelectionId"/>
                    <c:if test="${not contentObject.structuredContentEnabled}">
                        <msgpt:VariantTree id="targetSelectionTree" onChange="targetSelectionNodeSelected"
                                           expanded="false"
                                           dataBinding="${firstLevelCloneSelectionVO}" cssClass="folderTreeDiv"
                                           style="width: 300px; height: 100px;"/>
                    </c:if>
                </div>
                <c:if test="${contentObject.structuredContentEnabled}">
                    <div id="actionPopupSuppress" align="center"
                         class="${command.canSuppress ? 'canSuppressOnHold' : ''}">
                        <div class="InfoSysContainer_question">
                            <p>
                                <b><fmtSpring:message code="page.text.suppress.production"/></b>
                            </p>
                            <br>
                            <form:checkbox id="isSuppressedOnHold" path="suppressedOnHold"
                                           cssClass="flipToggleBinding defaultFalse"
                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                        </div>
                    </div>
                    <div id="actionPopupUpdateVariantVersioning"
                         class="${command.contentSuppressed ? 'versionTogglePermitted' : ''}">
                        <div class="InfoSysContainer_question">
                            <p>
                                <b><fmtSpring:message code="page.text.create.variant.working.copies"/></b>
                            </p>
                            <br>
                            <form:checkbox id="updateVariantVersioning" path="updateVariantVersioning"
                                           cssClass="flipToggleBinding defaultTrue"
                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                        </div>
                    </div>
                </c:if>
                <c:if test="${command.selectable == true && command.canUpdate == true}">
                    <div id="actionPopupCopyContentTo">
                        <msgpt:VariantTree id="copyContentToTree" expanded="true" dataBinding="${contentObject}"
                                           onChange="onCopyContentToTreeSelected" cssClass="folderTreeDiv"
                                           style="display: none; width: 250px;" initializeFetch="false"/>
                        <form:hidden id="copyContentToBinding" path="cloneToTreeNodeId"/>
                    </div>
                </c:if>
                <div id="actionPopupNewSelectableContent" style="padding: 3px 0 6px;" align="left">
                    <form:checkbox id="isNewDataValueCollection" path="newTreeNode.creatingNewCollection" value="true"
                                   cssStyle="display: none;"/>
                    <div style="padding-left: 7px;">
                        <table cellspacing="0" cellpadding="0" border="0">
                            <tr>
                                <td style="padding: 0px; vertical-align: bottom;">
                                    <div align="center" class="tabSelected_minimal IEtabFix"
                                         id="newContentTab_dataValues"
                                         onclick="changeNewContentTab(this)" onmouseover="tabHover(this,'in')"
                                         onmouseout="tabHover(this,'out')">
                                        <div style="padding-top: 6px; font-size: 12px;"><fmtSpring:message
                                                code="page.label.new"/></div>
                                    </div>
                                </td>
                                <td style="padding: 0px; vertical-align: bottom;">
                                    <div align="center" class="tab_minimal IEtabFix" id="newContentTab_shared"
                                         onclick="changeNewContentTab(this)" onmouseover="tabHover(this,'in')"
                                         onmouseout="tabHover(this,'out')">
                                        <div style="padding-top: 6px; font-size: 12px;"><fmtSpring:message
                                                code="page.label.shared"/></div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div id="newContentDiv_dataValues"
                         style="border: 1px solid #848484; padding: 15px 8px; background-color: #f0f0f0;">
                        <table width="100%" cellspacing="0" cellpadding="0" border="0">
                            <tr>
                                <td style="color: #444;" colspan="3" align="center"><fmtSpring:message
                                        code="page.text.specify.name.and.data.for.content.variant"/></td>
                            </tr>
                            <tr>
                                <td style="color: #444;"><fmtSpring:message code="page.label.name"/></td>
                                <td colspan="2" style="white-space: nowrap;">
                                    <msgpt:InputFilter type="simpleName">
                                        <form:input id="addParamInstanceName" path="newTreeNode.name"
                                                    cssClass="inputL"/>
                                    </msgpt:InputFilter>
                                </td>
                            </tr>
                            <c:forEach var="currentParameter" items="${command.newTreeNode.parameters}"
                                       varStatus="paramStat">
                                <tr>
                                    <td style="padding: 0px;"/>
                                    <td style="padding: 0px; padding-left: 4px; vertical-align: top;">
                                        <div class="fullLineLabel" style="word-break: break-all; white-space: normal;"><c:out value="${currentParameter.name}"/></div>
                                    </td>
                                </tr>
                                <tr><td style="padding-top: 0px;"/>
                                <c:if test="${msgpt:size(command.parameterLookupValues[currentParameter]) > 0}">
                                    <td style="padding-top: 0px; vertical-align: top; white-space: nowrap">
                                        <select class="inputL" id="lookupSelect_new_${paramStat.index}"
                                                style="margin-right: 4px;">
                                            <c:forEach var="currentValue"
                                                       items="${command.parameterLookupValues[currentParameter]}">
                                                <option value="${currentValue.value}"><c:out
                                                        value="${currentValue.displayValue}"/></option>
                                            </c:forEach>
                                        </select>
                                        <msgpt:SlimButton label="page.label.add"
                                                          URL="javascript:addLookupValue_new(${paramStat.index});"/>
                                    </td>
                                    </tr>
                                    <tr><td style="padding-top: 0px;"/>
                                </c:if>
                                <td class="davaValueInputContainer" style="padding-top: 0px; white-space: nowrap;">
                                    <div style="${currentParameter.isDateParameter ? 'display: none;' : ''}">
                                        <msgpt:InputFilter type="dataElementVariable">
                                            <form:textarea id="dataValueInput_new_${paramStat.index}"
                                                           path="newTreeNode.newCollectionFirstValue[${paramStat.index}]"
                                                           cssClass="inputL dataValueInput" rows="3"
                                                           onkeyup="dataValueKeyEvent('new','${paramStat.index}')"/>
                                        </msgpt:InputFilter>
                                    </div>
                                    <c:if test="${currentParameter.isDateParameter}">
                                        <div style="display: inline-block; vertical-align: top; position: relative;">
                                            <msgpt:Calendar id="newDataValueDateInput_after_${paramStat.index}"
                                                            cssClass="dataValueDateInput_after"
                                                            onchange="setDateDataValue(this)"
                                                            onkeyup="setDateDataValue(this)"/>
                                            <div class="dataValueDateIndicator"
                                                 style="position: relative; left: 12px; font-size: 11px;">
                                                (<fmtSpring:message code="page.text.timing.starts.now"/>)
                                            </div>
                                        </div>
                                        <div style="display: inline-block;" class="pt-2 pl-2">
                                            <fmtSpring:message code="page.text.range_to"/>
                                        </div>
                                        <div style="display: inline-block; vertical-align: top; position: relative; margin-left: 4px;">
                                            <msgpt:Calendar id="newDataValueDateInput_before_${paramStat.index}"
                                                            cssClass="dataValueDateInput_before"
                                                            onchange="setDateDataValue(this)"
                                                            onkeyup="setDateDataValue(this)"/>
                                            <div class="dataValueDateIndicator"
                                                 style="position: relative; left: 12px; font-size: 11px;">
                                                (<fmtSpring:message code="page.text.timing.no.end.date"/>)
                                            </div>
                                        </div>
                                    </c:if>
                                </td>
                                </tr>
                            </c:forEach>
                            <tr>
                                <td style="color: #444;"><fmtSpring:message code="page.label.shared"/>&nbsp;</td>
                                <td colspan="2"><form:checkbox path="newTreeNode.shared" cssClass="checkbox"/></td>
                            </tr>
                        </table>
                    </div>
                    <div id="newContentDiv_shared"
                         style="border: 1px solid #848484; padding: 15px 8px; background-color: #f0f0f0; display: none; width: 320px;">
                        <table width="100%" class="innerContentTable" cellspacing="0" cellpadding="0" border="0">
                            <tr>
                                <td style="color: #444;" align="center"><fmtSpring:message
                                        code="page.text.select.shared.data.for.content.selection"/></td>
                            </tr>
                            <c:choose>
                                <c:when test="${not empty sharedPGICollections}">
                                    <tr>
                                        <td style="color: #444;" align="center">
                                            <form:select cssClass="inputL" path="newTreeNode.pgCollectionId"
                                                         id="sharedItemSelect" onchange="sharedItemInit()">
                                                <c:forEach var="currentSharedItem" items="${sharedPGICollections}">
                                                    <option id="sharedOption_${currentSharedItem.id}"
                                                            value="${currentSharedItem.id}"><c:out
                                                            value="${currentSharedItem.name}"/></option>
                                                </c:forEach>
                                            </form:select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="color: #444;" align="center">
                                            <input type="hidden" id="currentSharedCollectionId" class="maintainValue"/>

                                            <msgpt:CollectionDataValuesTree id="sharedPreviewDataValueTree"
                                                                            style="width: 250px; padding-left: 0px; border: 1px solid #848484;"/>
                                        </td>
                                    </tr>
                                </c:when>
                                <c:otherwise>
                                    <tr>
                                        <td style="color: #444;" align="center">
                                            <div style="margin: 10px 5px; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
                                                <fmtSpring:message code="page.text.no.data.definitions.shared"/>
                                            </div>
                                        </td>
                                    </tr>
                                </c:otherwise>
                            </c:choose>
                        </table>
                    </div>
                    <form:hidden path="newTreeNode.guid"/>
                </div>
                <div id="actionPopupTaskMetadataSelect">
                    <div class="formControl">
                        <label><span class="labelText"><fmtSpring:message
                                code="page.label.metadata.optional"/></span></label>
                        <div class="controlWrapper">
                            <select id="taskMetadataSelect" class="style_select">
                                <option value="0"><fmtSpring:message code="page.label.none"/></option>
                                <c:forEach var="taskMetadataFormDefinition"
                                           items="${taskMetadataFormDefinitions}">
                                    <option id="taskMetadataFormDefinitionOption_${taskMetadataFormDefinition.id}"
                                            value="${taskMetadataFormDefinition.id}">
                                        <c:out value="${taskMetadataFormDefinition.name}"/>
                                    </option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                </div>
                <div id="actionPopupTaskSelect">
                    <div class="formControl">
                        <label><span class="labelText"><fmtSpring:message code="page.label.name"/></span></label>
                        <span><c:out value="${contentObject.name}"/></span>
                    </div>
                    <div class="formControl" id="taskSelectDiv">
                        <label><span class="labelText"><fmtSpring:message code="page.label.task"/></span></label>
                        <div class="controlWrapper">
                            <form:select id="taskSelect" path="linkedTaskId" cssClass="inputXXL"
                                         onchange="selectTask()" onkeyup="selectTask()">
                                <option id="0" value="0"><fmtSpring:message code="page.text.loading"/></option>
                            </form:select>
                        </div>
                    </div>
                    <div class="formControl" id="newTaskTypeDiv">
                        <label><span class="labelText"><fmtSpring:message code="page.label.type"/></span></label>
                        <div class="controlWrapper">
                            <form:select id="taskTypeSelect" path="createTaskType" cssClass="inputXXL style_select"
                                         onchange="selectTaskType()" items="${taskTypes}" itemLabel="name" itemValue="id">
                            </form:select>
                        </div>
                    </div>
                    <div class="formControl" id="newTaskLanguageDiv" style="display: none">
                        <label><span class="labelText"><fmtSpring:message code="page.label.language"/></span></label>
                        <div class="controlWrapper">
                            <form:select id="newTasklanguageSelect" path="messagepointLocaleId" cssClass="inputXXL style_select">
                                <c:forEach var="currentLocale" items="${messagepointLocales}">
                                    <option id="localeOption_${currentLocale.id}" value="${currentLocale.id}">
                                        <c:out value="${currentLocale.name}"/>
                                    </option>
                                </c:forEach>
                            </form:select>
                        </div>
                    </div>
                    <div class="formControl" id="taskVariantDiv">
                        <label><span class="labelText"><fmtSpring:message code="page.label.variant"/></span></label>
                        <div class="controlWrapper">
                            <form:select id="taskVariantSelect" path="taskVariantId" cssClass="inputXXL">
                                <option id="0" value="0"><fmtSpring:message code="page.label.no.selected.items"/></option>
                            </form:select>
                        </div>
                    </div>
                    <div id="taskDescriptionDiv">
                    </div>
                </div>
                <div id="actionPopupNote" state="default">
                    <br/>
                    <div class="formControl">
                        <label><span id="descriptionLabel" class="labelText" style="display: none;"><fmtSpring:message code="page.label.description"/></span></label>
                        <div class="controlWrapper">
                            <msgpt:InputFilter type="comment">
                                <form:textarea path="userNote" onkeyup="validatePopupReq();"
                                               rows="3" onclick="initTextarea(this)"/>
                            </msgpt:InputFilter>
                        </div>
                    </div>
                </div>
                <div id="actionPopupApprovalButtons" class="actionPopupButtonsContainer">
                    <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                    <span id="approveBtn">
                        <msgpt:Button URL="#" primary="true" label="page.flow.approve"/>
                    </span>
                    <span id="approveBtnDisabled" style="display: none;">
                        <msgpt:Button URL="#" label="page.flow.approve" disabled="true"/>
                    </span>
                </div>
                <div id="actionPopupReleaseFromTranslationButtons" class="actionPopupButtonsContainer">
                    <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                    <span id="releaseBtn">
                        <msgpt:Button URL="#" primary="true" label="page.label.release"/>
                    </span>
                    <span id="releaseBtnDisabled" style="display: none;">
                        <msgpt:Button URL="#" label="page.label.release" disabled="true"/>
                    </span>
                </div>
                <div id="actionPopupRejectButtons" class="actionPopupButtonsContainer">
                    <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                    <span id="rejectBtn">
                        <msgpt:Button URL="#" primary="true" label="page.flow.reject"/>
                    </span>
                    <span id="rejectBtnDisabled" style="display: none;">
                        <msgpt:Button URL="#" label="page.flow.reject" disabled="true"/>
                    </span>
                </div>
                <div id="actionPopupAbortButtons" class="actionPopupButtonsContainer">
                    <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                    <span id="abortBtn">
                        <msgpt:Button URL="#" primary="true" label="page.label.abort"/>
                    </span>
                    <span id="abortBtnDisabled" style="display: none;">
                        <msgpt:Button URL="#" label="page.label.abort" disabled="true"/>
                    </span>
                </div>
                <!-- Add Task w/ Metadata -->
                <div id="actionPopupTaskMetadataButtons" class="actionPopupButtonsContainer">
                    <span id="taskCustomCancelBtnEnabled">
                        <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                    </span>
                    <span id="taskCustomContinueBtnEnabled">
                        <msgpt:Button URL="javascript:iFrameAction(38);"
                                      label="page.label.continue" primary="true"/>
                    </span>
                </div>
                <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                    <span id="cancelBtnEnabled">
                        <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                    </span>
                    <span id="cancelBtnDisabled" style="display: none;">
                        <msgpt:Button URL="#" label="page.label.cancel" disabled="true"/>
                    </span>
                    <span id="continueBtnEnabled">
                        <msgpt:Button URL="#" primary="true" label="page.label.continue"/>
                    </span>
                    <span id="continueBtnDisabled" style="display: none;">
                        <msgpt:Button URL="#" label="page.label.continue" disabled="true"/>
                    </span>
                </div>
                <div id="actionPopupContinueToTaskSelectButtons" class="actionPopupButtonsContainer">
                    <span id="toLinkTaskCustomCancelBtnEnabled">
                        <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                    </span>
                    <span id="toLinkTaskCustomContinueBtnEnabled">
                        <msgpt:Button URL="javascript:continueToLinkTask();" label="page.label.continue" primary="true"/>
                    </span>
                </div>
            </msgpt:Popup>

        </c:when>
        <c:otherwise>
            <div class="position-relative">
                <div class="border-bottom" data-sticky="top"
                     data-fixedclass="container px-0 box-shadow-4 bg-white border-white rounded-top">
                    <div class="d-flex align-items-center mx-4 py-3 bg-white rounded-top">
                        <button type="button" class="btn btn-outline-light text-body post-trigger"
                                onclick="submitAction(99, this);">
                            <i class="far fa-arrow-left mr-2" aria-hidden="true"></i><fmtSpring:message
                                code="page.label.back"/>
                        </button>
                    </div>
                </div>
            </div>
        </c:otherwise>
    </c:choose>
</msgpt:Section>