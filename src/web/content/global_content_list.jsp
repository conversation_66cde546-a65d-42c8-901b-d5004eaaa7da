<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.content" extendedScripts="true">

        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>
        <msgpt:CalendarIncludes/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/sharedTextFastEdit/jquery.sharedTextFastEdit.js"/>
        <msgpt:Script src="includes/javascript/mp.cardViewList.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/styleManager/jquery.styleManager.js"/>
        <msgpt:Script src="_ux/js/mp.postTrigger.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js"/>
        <msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.editorActions.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/scrollTo/jquery.scrollTo-2.1.2.min.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.varTagAttrManager.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.smartTextTagAttrManager.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.placeholderTagAttrManager.js"/>

        <msgpt:Script>
            <script>

                var $updateBtn, $cloneBtn, $exportMenu, $actionMenu;
                var $listTypeToggle = $('.cardViewToggle');
                var showContentJSONExport = ${showContentJSONExport ? 'true' : 'false'};

                // *********  INIT: START  *********
                $(function () {

                    $("#metadataSelect, #taskMetadataSelect").styleActionElement({labelAlign: true});

                    $(".style_multiselect").styleActionElement({maxItemDisplay: 10});
                    $(".actionBtnMultiselect").css("margin-left", "0px");

                    $updateBtn = $('#updateBtn');
                    $cloneBtn = $('#cloneBtn');
                    $exportMenu = $('#exportMenu');
                    $actionMenu = $('#actionMenu');

                    initTip($('.detailTip'));

                    $("[id*='itemInProcess']").actionStatusPolling({
                        itemLabel: client_messages.text.audit_report,
                        type: 'auditreport',
                        postItemInit: function ($item) {
                            var $option = $('#' + $item.attr('id')),
                                $select = $option.parent(),
                                itemIndex = $select.children().index($option),
                                $dropdownItem = $select.siblings('.dropdown').find('.dropdown-content').children().eq(itemIndex);

                            if ($item.attr('id').indexOf('itemInProcess') < 0)
                                $dropdownItem.find('.progress-loader').remove();
                            else
                                $dropdownItem.prepend('<div class="progress-loader mr-2">' +
                                    '<i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>' +
                                    '</div>');

                        },
                        onInit: function (o) {
                            var $option = $('#' + o.option.attr('id')),
                                $select = $option.parent(),
                                itemIndex = $select.children().index($option),
                                $dropdown = $select.siblings('.dropdown'),
                                $dropdownItem = $dropdown.find('.dropdown-content').children().eq(itemIndex);

                            $dropdownItem.prepend('<div class="progress-loader mr-2">' +
                                '<i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>' +
                                '</div>');


                            // TODO: REVIEW RENDER/PAINT ISSUE WITH MULTIPLE SPINNING (ANIMATED) ELEMENTS
                            /*$dropdown.children('.dropdown-toggle').prepend('<div class="progress-loader mr-2 bg-transparent p-0">' +
                                '<i class="progress-loader-icon far fa-spinner-third text-white" aria-hidden="true"></i>' +
                                '</div>');*/
                        }
                    });

                    if ("${hasMetadataFormDef}" == "true") {
                        $('#addContentObjectBtn').click(function () {
                            // Clear the previous selection
                            $('#metadataSelect_menu').find('.selectedOption').text($('#metadataSelect').find('option:first').text());
                            actionSelected(33);
                        });
                    } else {
                        initAddContentObjectBtn(false);
                    }

                    // Onload asset popup: E-mail links/Content search
                    if (getParam('contentObjectId') && getParam('contentObjectId') != "" && getParam('paramInstId') && getParam('paramInstId') != "" && getParam('statusViewId') && getParam('statusViewId') != "")
                        iFrameView(context + '/content/content_object_view_content_dynamic_variant.form?documentId=' + getParam('documentId') + '&contentObjectId=' + getParam('contentObjectId') + '&paramInstId=' + getParam('paramInstId') + '&statusViewId=' + getParam('statusViewId'), 'smartContentList', 'null');
                    else if (((getParam('contentSelectionId') && getParam('contentSelectionId') != "") || (getParam('touchpointSelectionId') && getParam('touchpointSelectionId') != "")) && getParam('statusViewId') && getParam('statusViewId') != "")
                        iFrameView(context + '/tpadmin/touchpoint_content_selection_view.form?contentSelectionId=' + getParam('contentSelectionId') + '&touchpointSelectionId=' + getParam('touchpointSelectionId') + '&contentObjectId=' + getParam('contentObjectId') + '&statusViewId=' + getParam('statusViewId'), 'smartContentList', 'null');
                    else if (getParam('contentObjectId') && getParam('contentObjectId') != "")
                        iFrameView(context + '/content/content_object_view.form?documentId=' + getParam('documentId') + '&contentObjectId=' + getParam('contentObjectId') + '&statusViewId=1', 'smartContentList', 'null');

                    var $searchTagCloud = $('#listSearchInput[data-toggle="tagcloud"]');

                    $searchTagCloud.tagCloud({
                        tagCloudType: $searchTagCloud.data('cloud-type'),
                        inputType: 'search',
                        rightOffsetAdj: 16,
                        topOffsetAdj: 12,
                        popupLocation: 'bottom-left',
                        afterInputValueChange: function (o, val) {
                            $searchTagCloud.keyup();
                        }
                    });

                    $listTypeToggle
                        .removeClass('focus')
                        .click(function () {
                            _.defer(function () {
                                updatePersistedClass($listTypeToggle);
                                getTopFrame().location.reload();
                            });
                        });

                    if ($listTypeToggle.is('.active')) {
                        $listTypeToggle.attr('aria-pressed', true);
                        $('.colVisToggleContainer').hide();
                        $('.cardSortInterface').show();
                    }

                    handleOnLoadAssets(); // Onload asset popup: E-mail links/Content search

                });

                $(document).ready(function () {
                    $('#taskVariantSelect').on('change', function() {
                        const selectedOption = $(this).find('option:selected');
                        console.log(selectedOption.attr('data-clean'))
                        $('#taskVariantSelect_menuTable .actionSelectMenuText.selectedOption').text(selectedOption.attr('data-clean'));
                    });
                });

                function handleOnLoadAssets() {

                    var url;

                    if (getParam('contentObjectId') && getParam('contentObjectId') != "" && getParam('paramInstId') && getParam('paramInstId') != "" && getParam('statusViewId') && getParam('statusViewId') != "")
                        url = context + '/content/content_object_view_content_dynamic_variant.form?documentId=' + getParam('documentId') + '&contentObjectId=' + getParam('contentObjectId') + '&paramInstId=' + getParam('paramInstId') + '&statusViewId=' + getParam('statusViewId');

                    else if (getParam('contentObjectId') && getParam('contentObjectId') != "")
                        url = context + '/content/content_object_view.form?documentId=' + getParam('documentId') + '&contentObjectId=' + getParam('contentObjectId') + '&statusViewId=1';

                    if (url) {

                        var $body = $('body'),
                            $fullPageLoader = $('<div id="fullPageLoader" class="modal fade d-block">' +
                                '<div class="modal-dialog">' +
                                '<div class="m-5 p-1 text-center">' +
                                '<div class="progress-loader progress-loader-lg mb-3">' +
                                '<i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>' +
                                '</div>' +
                                '<p class="text-white">' +
                                client_messages.text.loading +
                                '</p>' +
                                '</div>' +
                                '</div>' +
                                '</div>');

                        $body
                            .addClass('modal-open')
                            .append($fullPageLoader)
                            .append('<div class="modal-backdrop fade"></div>');

                        _.defer(function () {

                            $('.modal-backdrop').addClass('show');

                            _.delay(function () {

                                $('#fullPageLoader').addClass('show');

                            }, 150);

                        });

                        javascriptHref(url);

                    }

                }

                function initAddContentObjectBtn(applyMetadata) {

                    $('#addContentObjectBtn').on('click', function () {

                        javascriptHref(context + '/content/content_object_edit.form?documentId=${param.documentId}' +
                            '&isGlobalContentObject=true&localContentType=' + ((getParam('localContentType') != "") ? getParam('localContentType') : 1) + '&isFreeform=' + ((getParam('isFreeform') != "") ? getParam('isFreeform') : false) +
                            '&metadataDefinitionId=' + (applyMetadata ? $('#metadataSelect').val() : -1)
                        );

                    });

                }

                function showOrHideWorkflowSelect(popupId, actionSpecId){
                    var isInSubworkflowStep = false;
                    $("input[id^='listItemCheck_']:checked").each(function(){
                        var objectId =$(this).attr('id').split('_')[1];
                        if (exists('isInSubworkflowStep_' + objectId)){
                            isInSubworkflowStep = true;
                        }
                    });
                    if (isInSubworkflowStep){
                        $('#actionPopupWorkflowSelect').show();
                        if (exists('approveBtnDisabled')) {	$('#approveBtnDisabled').show(); $('#approveBtn').hide(); }
                        if (exists('releaseBtnDisabled')) {	$('#releaseBtnDisabled').show(); $('#releaseBtn').hide(); }
                        if(actionSpecId == 18 || actionSpecId == 21){ // Reject or Reject and Override
                            $('#actionPopupUserSelect').hide();
                            $('#rejectBtnDisabled').show(); $('#rejectBtn').hide();
                        }
                    }else{
                        $('#actionPopupWorkflowSelect').hide();
                        if(actionSpecId == 17 || actionSpecId == 20) { // Approve or Approve and Override
                            $('#approveBtn').show(); $('#approveBtnDisabled').hide();
                        }

                        if(actionSpecId == 18 || actionSpecId == 21) { // Reject or Reject and Override
                            $('#actionPopupUserSelect').show();
                            $('#rejectBtnDisabled').show(); $('#rejectBtn').hide();
                        }
                    }
                }

                function selectWorkflow(){
                    var actionId = $('#popupActionId').val();
                    if(actionId == 41){ // Reassign for translation
                        // Reload the user select based on the workflow selection
                        requestDataForSelectMenu($('#actionUserSelect_'+actionId).attr('type'));
                    }
                    validatePopupReq();
                }

                function selectTask(){
                    $('#actionPopupNote').find('textarea').val('');
                    if ($('#taskSelect').val() === '0'){
                        $('#actionPopupNote').find('textarea').removeAttr("disabled");
                        $('#actionPopupNote').attr('state','clear');
                        $('#newTaskTypeDiv').show();
                        $('#taskVariantDiv').show();
                        selectTaskType();
                    }else{
                        $('#actionPopupNote').find('textarea').attr("disabled", true);
                        $('#newTaskTypeDiv').hide();
                        $('#newTaskLanguageDiv').hide();
                        $('#taskVariantDiv').hide();
                        let taskNameWithDescription = $('#taskSelect').find(':selected').text();
                        let match = taskNameWithDescription.match(/\(([^)]+)\)/);
                        if(match){
                            $('#actionPopupNote').find('textarea').val(match[1]);
                        }
                    }
                    validatePopupReq();
                }

                function selectTaskType(){
                    if ($('#taskTypeSelect').val() === '2'){
                        $('#newTaskLanguageDiv').show();
                    }
                    else{
                        $('#newTaskLanguageDiv').hide();
                    }
                }


                // *********  INIT END  *********

                // *********  LIST TABLE FUNCTIONS: START  *********

                function iFrameView(path, eleId, e) {
                    if (e != undefined) {
                        if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
                            $("#" + eleId).iFramePopup($.extend({
                                src: path,
                                displayOnInit: true,
                                id: "contentObjectFrame",
                                appliedParams: {tk: "${param.tk}"},
                                beforePopupClose: function () {
                                    $('#iFramePopup_workflowHistoryFrame').remove();
                                    rebuildListTable(true);
                                    $('#backgroundTasksPlaceholder').refresh();
                                }
                            }, iFramePopup_fullFrameAttr));
                        }
                        $('.contentPopup_popupContainer').remove();
                        e.preventDefault ? e.stopPropagation() : e.returnValue = false;
                    }
                }

                // Inject icon in list headers
                function headerLabelOverride(nHead, aData, iStart, iEnd, aiDisplay) {
                    $(nHead).find('th').each(function () {
                        var iconColumn = false;
                        if ($(this).text().indexOf(client_messages.text.targeted) != -1) {
                            $(this).append("<i class=\"far fa-bullseye table-icon\"></i>");
                            iconColumn = true;
                        } else if ($(this).text().indexOf(client_messages.text.timed) != -1) {
                            $(this).append("<i class=\"far fa-clock table-icon\"></i>");
                            iconColumn = true;
                        } else if ($(this).text().indexOf(client_messages.text.variable) != -1) {
                            $(this).append("<i class=\"far fa-clone table-icon\"></i>");
                            iconColumn = true;
                        }

                        if (iconColumn)
                            $(this)
                                .contents()
                                .filter(function () {
                                    return this.nodeType == 3; //Node.TEXT_NODE
                                }).remove();
                    });
                }

                function toggleFilter(select) {
                    rebuildListTable(false);
                }

                function toggleSort() {
                    rebuildListTable(false);
                }

                function rebuildListTable(maintainCurrentPage) {
                    $('#contentObjectList').DataTable().ajax.reload(null, !maintainCurrentPage);
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";

                    var cardDisplay = $listTypeToggle.is('.active');

                    obj.columns = [
                        {
                            columnMap: 'name',
                            columnName: client_messages.text.name,
                            sort: !cardDisplay,
                            width: '100%',
                            colVisToggle: false,
                            applied: true
                        },
                        {
                            columnMap: 'versioning',
                            columnName: client_messages.text.state,
                            sort: false,
                            width: '100%',
                            colVisToggle: false,
                            applied: true
                        },
                        {
                            columnMap: 'assignedTo',
                            columnName: client_messages.text.assigned,
                            sort: true,
                            width: '100%',
                            colVisToggle: true,
                            applied: true
                        },
                        {
                            columnMap: 'type',
                            columnName: client_messages.text.type,
                            sort: true,
                            width: '100%',
                            colVisToggle: true,
                            applied: true
                        },
                        {
                            columnMap: 'timing',
                            columnName: client_messages.text.timed,
                            sort: true,
                            width: '100%',
                            colVisToggle: true,
                            applied: ${param.localContentType != 2}
                        },
                        {
                            columnMap: 'targeting',
                            columnName: client_messages.text.targeted,
                            sort: true,
                            width: '100%',
                            colVisToggle: true,
                            applied: ${param.localContentType != 2}
                        }
                    ];

                    var cardSortCols = ['name', 'assignedTo'];
                    if (cardDisplay) {
                        $('#contentObjectListSortBySelect').html('');
                        for (var i = 0; i < obj.columns.length; i++) {
                            if (obj.columns[i].applied && cardSortCols.indexOf(obj.columns[i].columnMap) != -1) {
                                var label = obj.columns[i].columnMap == 'name' ? client_messages.text.name : obj.columns[i].columnName;
                                $('#contentObjectListSortBySelect').append("<option id=\"contentObjectListSortBySelect_asc_" + i + "\" value=\"" + obj.columns[i].columnMap + "_asc\">" + label + " (asc)</option>");
                                $('#contentObjectListSortBySelect').append("<option id=\"contentObjectListSortBySelect_desc_" + i + "\" value=\"" + obj.columns[i].columnMap + "_desc\">" + label + " (desc)</option>");
                            }
                            if (i != 0)
                                obj.columns[i].applied = false;
                        }
                        var persistedValue = getPersistedValue($('#contentObjectListSortBySelect'));
                        if (persistedValue)
                            $('#contentObjectListSortBySelect').val(persistedValue);
                        $('#contentObjectListSortBySelect').data('complexDropdown').refreshAllTheOptions();
                    }


                    return obj;
                }

                function getAsyncExtParams() {
                    var stampDate = new Date();
                    var obj = [
                        {"name": "listTableType", "value": getParam('localContentType') == "2" ? "6" : "4"},
                        {"name": "documentId", "value": getParam('documentId') != "" ? getParam('documentId') : -1},
                        {
                            "name": getParam('localContentType') == 1 ? "embeddedContentsAssignmentFilterId" : "contentLibrarysAssignmentFilterId",
                            "value": $('#contentObjectsListAssignmentFilter').val()
                        },
                        {
                            "name": getParam('localContentType') == 1 ? "embeddedContentsStatusFilterId" : "contentLibrarysStatusFilterId",
                            "value": $('#contentObjectListStatusFilter').val()
                        },
                        {"name": "displayMode", "value": "full"},
                        {"name": "globalContext", "value": $("#globalContextContainer").is('.globalContextEnabled')},
                        {
                            "name": "isSharedCanvas",
                            "value": getParam('isFreeform') != "" ? getParam('isFreeform') : false
                        },
                        {
                            "name": "listDisplayFormat",
                            "value": $listTypeToggle.is('.active') ? "card" : "table"
                        },
                        {"name": "cacheStamp", "value": stampDate.getTime()}
                    ];

                    if ($listTypeToggle.is('.active'))
                        obj[obj.length] = {"name": "cardSort", "value": $('#contentObjectListSortBySelect').val()}

                    return obj;
                }

                function requestContentForCard(contentContainer) {
                    var requestParam = "contentType=${(empty param.localContentType || param.localContentType == 1) ? 'embeddedText' : 'contentLibrary'}" +
                        "&viewType=list" +
                        "&contentItemId=" + $(contentContainer).attr('contentItemId') +
                        "&statusViewId=" + $(contentContainer).attr('statusViewId') +
                        "&statusFilterId=" + ($('#contentObjectListStatusFilter').val() != undefined && $('#contentObjectListStatusFilter').val() != null ? $('#contentObjectListStatusFilter').val() : 1) +
                        "&localeId=0" +
                        "&getStyles=false";

                    // Async Request
                    var stampDate = new Date();
                    $.ajax({
                        type: "GET",
                        url: context + "/getContentPreview.form?" + requestParam + "&cacheStamp=" + (stampDate.getTime()),
                        dataType: "json",
                        success: function (data) {
                            // mp.cardViewList.js
                            processCardContent(data, contentContainer, $(contentContainer).attr('contentItemId'));
                        }
                    });
                }

                function postListDrawCallback(nTable) {

                    $(nTable).find('.dataTableLink').each(function () {

                        if ($listTypeToggle.is('.active')) {

                            toggleCardDisplayVis();

                            var contentContainer = $(this).closest('tr').find('.contentContainer');
                            requestContentForCard(contentContainer);

                        } else {

                            // Smart Text:  Init content popup
                            var instId = parseId($(this));
                            $(this).closest('td').contentPopup({
                                trigger: "dual",
                                contentType: '${(empty param.localContentType || param.localContentType == 1) ? "embeddedText" : "contentLibrary"}',
                                contentItemId: instId,
                                popupLocation: 'right',
                                fnBeforeContentRequest: function (o) {
                                    // Use system default locale -1 since here a user cannot change the language view (for user context selected locale this would be 0)
                                    o.data.contentLocaleId = -1;
                                },
                                fnExtServerParams: function (o) {
                                    var obj = [
                                        {
                                            "name": "statusFilterId",
                                            "value": $('#contentObjectListStatusFilter').val() != undefined && $('#contentObjectListStatusFilter').val() != null ? $('#contentObjectListStatusFilter').val() : 1
                                        }
                                    ];
                                    return obj;
                                }
                            });

                        }
                    });

                    $(nTable).find('.popupTargetingSummary').each(function () {
                        // Targeting:  Init targeting popup
                        var instId = parseId($(this));
                        var stampDate = new Date();
                        $(this).closest('td').popupFactory({
                            title: client_messages.title.targeting,
                            popupLocation: "left",
                            width: 250,
                            asyncDataType: "xml",
                            asyncSetContentURL: context + "/getTargeting.form?type=summary&contentObjectId=" + instId + "&cacheStamp=" + (stampDate.getTime()),
                            asyncSetContentHandler: function (o, data) {
                                if ($(data).find("content").length > 0)
                                    return "<div align=\"left\" style=\"padding: 6px 12px; font-size: 10px;\">" +
                                        $(data).find("content").text() +
                                        "</div>";
                                else
                                    return "<div>Error: " + client_messages.text.bad_request_for_targeting_summary + "</div>";
                            }
                        });
                    });

                    $(nTable).find('.detailTip').each(function () {
                        initTip(this);
                    });

                    $(nTable).find('[data-toggle=tooltip]').tooltip();
                }

                function postListRenderFlagInjection(oObj) {
                    var iFrameId = "message-instant-id";
                    var iFrameSrc = context + "/touchpoints/touchpoint_content_object_list_detail.form?contentObjectId=" + oObj.aData.dt_RowId;


                    if ($('#listSearchInput').val() != "")
                        iFrameSrc += "&sSearch=" + $('#listSearchInput').val();

                    var binding = oObj.aData.binding;

                    var text = oObj.aData.name;
                    text += "<input id='iFrameId' value=" + iFrameId + " type='hidden' class='iframe-data' />";
                    text += "<input id='iFrameSrc' value=" + iFrameSrc + " type='hidden' class='iframe-data' />";

                    //Selection check select
                    text += binding;

                    //Selection permission flags
                    if (oObj.aData.flags.canUpdate)
                        text += "<input type='hidden' id='canUpdate_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReassign)
                        text += "<input type='hidden' id='canReassign_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canTranslationReassign)
                        text += "<input type='hidden' id='canTranslationReassign_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReleaseFromTranslation)
                        text += "<input type='hidden' id='canReleaseFromTranslation_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canClone)
                        text += "<input type='hidden' id='canClone_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canClone)
                        text += "<input type='hidden' id='canContentExport_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReleaseForApproval)
                        text += "<input type='hidden' id='canReleaseForApproval_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canApprove)
                        text += "<input type='hidden' id='canApprove_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReject)
                        text += "<input type='hidden' id='canReject_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canDiscard)
                        text += "<input type='hidden' id='canDiscard_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canArchive)
                        text += "<input type='hidden' id='canArchive_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.archived)
                        text += "<input type='hidden' id='archived_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canCreateWorkingCopy)
                        text += "<input type='hidden' id='canCreateWorkingCopy_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canDeleteArchive)
                        text += "<input type='hidden' id='canDeleteArchive_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.hasNoStepForWorkflow)
                        text += "<input type='hidden' id='hasNoStepForWorkflow_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.workflowOwner)
                        text += "<input type='hidden' id='workflowOwner_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canBulkTouchpointAssign)
                        text += "<input type='hidden' id='canBulkTouchpointAssign_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canMakeLocal)
                        text += "<input type='hidden' id='canMakeLocal_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canMoveToLocal)
                        text += "<input type='hidden' id='canMoveToLocal_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.inTranslationStep)
                        text += "<input type='hidden' id='isInTranslationStep_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.inApprovalStep)
                        text += "<input type='hidden' id='isInApprovalStep_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.inSubworkflowStep)
                        text += "<input type='hidden' id='isInSubworkflowStep_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.currentTranslator)
                        text += "<input type='hidden' id='isCurrentTranslator_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canAddTask)
                        text += "<input type='hidden' id='canAddTask_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canAbort)
                        text += "<input type='hidden' id='canAbort_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canRetry)
                        text += "<input type='hidden' id='canRetry_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.hasTaskToLink)
                        text += "<input type='hidden' id='hasTaskToLink_" + oObj.aData.dt_RowId + "' />";

                    return text;
                }

                // *********  LIST TABLE FUNCTIONS: END  *********

                // ******* CONTEXT ACTIONS: START *******

                function actionMenuSelected(el) {

                    var elSelectedId = $(el).children(':selected').attr('id');

                    if (elSelectedId.indexOf('actionOption_') !== -1)
                        actionSelected(el);

                    else
                        iFrameAction(elSelectedId.replace('actioniFrame_', ''));

                }

                function continueToLinkTask(saveCurrentSelection) {
                    if(saveCurrentSelection){
                        // Save the current selection
                        var currentTaskLinkedObjectId = $('#currentTaskLinkedObjectId').val();
                        var currentLinkedTaskBinding = '<input id=\'taskLinkedMap_'+currentTaskLinkedObjectId+'\' type=\'hidden\' name=\'taskLinkedMap['+currentTaskLinkedObjectId+']\' value=\''+$('#taskSelect').val()+'\'>';
                        var currentTaskDescriptionBinding = '<input id=\'taskDescriptionMap_'+currentTaskLinkedObjectId+'\' type=\'hidden\' name=\'taskDescriptionMap['+currentTaskLinkedObjectId+']\' value=\''+$('#actionPopupNote').find('textarea').val()+'\'>';
                        var currentTaskTypeBinding = '<input id=\'taskTypeMap_'+currentTaskLinkedObjectId+'\' type=\'hidden\' name=\'taskTypeMap['+currentTaskLinkedObjectId+']\' value=\''+$('#taskTypeSelect').val()+'\'>';

                        if($('#taskTypeSelect').val() === '2'){
                            var currentTaskLocaleBinding = '<input id=\'taskLocaleMap_'+currentTaskLinkedObjectId+'\' type=\'hidden\' name=\'taskLocaleMap['+currentTaskLinkedObjectId+']\' value=\''+$('#newTasklanguageSelect').val()+'\'>';
                            $('#newTaskLanguageDiv').append(currentTaskLocaleBinding);
                        }

                        if ($('#taskVariantSelect').val() && $('#taskVariantSelect').val() !== "0") {
                            var tokens = $('#taskVariantSelect').val().split('_');
                            if (tokens.length > 1) {
                                var currentTaskVariantBinding = '<input id=\'taskVariantMap_'+currentTaskLinkedObjectId+'\' type=\'hidden\' name=\'taskVariantMap['+currentTaskLinkedObjectId+']\' value=\''+tokens[1]+'\'>';
                                $('#taskVariantDiv').append(currentTaskVariantBinding);
                            }
                        }

                        $('#taskSelectDiv').append(currentLinkedTaskBinding);
                        $('#taskDescriptionDiv').append(currentTaskDescriptionBinding);
                        $('#newTaskTypeDiv').append(currentTaskTypeBinding);
                    }else{
                        // Clear the selections
                        $("input[id^='taskLinkedMap_']").remove();
                        $("input[id^='taskDescriptionMap_']").remove();
                        $("input[id^='taskTypeMap_']").remove();
                        $("input[id^='taskLocaleMap_']").remove();
                        $("input[id^='taskVariantMap_']").remove();
                    }

                    var selectedIds = getSelectedIds().split('_');
                    var nextUnlinkedObjectId = 0;
                    for(var i = 0; i < selectedIds.length; i++) {
                        // Check if the selected object has a task to link
                        if(exists('hasTaskToLink_'+selectedIds[i])) {
                            var linkedToTaskId = $('#taskLinkedMap_' + selectedIds[i]).val();
                            if (!exists('taskLinkedMap_'+selectedIds[i])) {  // If the task is not linked before
                                nextUnlinkedObjectId = selectedIds[i];
                                break;
                            }
                        }
                    }

                    if(nextUnlinkedObjectId > 0) {
                        // Set the popup to the next unlinked task
                        $('#currentTaskLinkedObjectId').val(nextUnlinkedObjectId);
                        $('#linkTaskToObjectName').text($('#actionLabel_'+nextUnlinkedObjectId).attr('itemname'));
                        $.ajax({
                            type: "GET",
                            url: context+"/getDataForSelectMenu.form?type=linkedToTasks&contentObjectId="+nextUnlinkedObjectId+"&cacheStamp="+(stampDate.getTime()),
                            dataType: "xml",
                            success: function(data) {
                                var taskOptions = $(data).find("taskOptions");
                                if (taskOptions.length > 0) {
                                    var taskOptions = $(data).find('taskOptions').text();
                                    $('#taskSelect').html(taskOptions);
                                    $('#taskSelect').addClass('style_select');
                                    $('#taskSelect_menuTable').remove();
                                    $("#taskSelect").styleActionElement();
                                    $("#taskTypeSelect").styleActionElement();
                                    $("#newTasklanguageSelect").styleActionElement();
                                }
                            }
                        });

                        $.ajax({
                            type: "GET",
                            url: context+"/getDataForSelectMenu.form?type=variantsForContentObject&contentObjectId="+nextUnlinkedObjectId+"&documentId="+${param.documentId}+"&cacheStamp="+(stampDate.getTime()),
                            dataType: "xml",
                            success: function(data) {
                                if ($(data).find("varOptions").length > 0) {
                                    var varOptions = $(data).find('varOptions').text();
                                    $('#taskVariantSelect').html(varOptions);
                                    $('#taskVariantSelect').addClass('style_select');
                                    $('#taskVariantSelect_menuTable').remove();
                                    $("#taskVariantSelect").styleActionElement();
                                }
                            }
                        });

                        actionSelected(46);
                    }else{
                        submitAction('4');
                    }
                }

                // List actions: Edit
                function iFrameAction(actionId) {
                    var contentObjectId = '',
                        applyMetadata = "${hasMetadataFormDef}" == "true",
                        isInTranslationStep = false,
                        isInApprovalStep = false;

                    $("input[id^='listItemCheck_']:checked").each(function () {
                        contentObjectId = this.id.replace('listItemCheck_', '');
                        isInTranslationStep = exists('isInTranslationStep_' + contentObjectId);
                        isInApprovalStep = exists('isInApprovalStep_'+contentObjectId);
                    });

                    if (actionId == '1') {

                        javascriptHref(context + '/content/content_object_edit_content.form?contentObjectId=' + contentObjectId +
                            '&statusViewId=' + ((gup('statusViewId') != "") ? gup('statusViewId') : '1') +
                            '&translationCompare=' + isInTranslationStep
                        );

                    } else if (actionId == '2') {

                        javascriptHref(context + '/content/content_object_edit.form?documentId=${param.documentId}' +
                            '&isGlobalContentObject=true&localContentType=' + ((getParam('localContentType') != "") ? getParam('localContentType') : 1) + '&isFreeform=' + ((getParam('isFreeform') != "") ? getParam('isFreeform') : false) +
                            '&metadataDefinitionId=' + (applyMetadata ? $('#metadataSelect').val() : -1)
                        );

                    } else if (actionId == '10') {
                        $('#actioniFrame_' + actionId).iFramePopup({
                            width: 820,
                            displayOnInit: true,
                            title: client_messages.title.set_touchpoints,
                            src: "content_object_touchpoint_assignment.form",
                            appliedParams: {selectedIds: getSelectedIds(), tk: "${param.tk}"},
                            closeBtnId: "cancelBtn_button",
                            beforePopupClose: function () {
                                rebuildListTable(true);
                            }
                        });
                    } else if (actionId == '26') {
                        $('.contextMenu a#actioniFrame_26').addClass('disabled');
                        $actionMenu.data('complexDropdown').disableOptionById('actioniFrame_26');
                        $.ajax({
                            type: "GET",
                            url: context + "/messageSyncOperations.form?action=runRehashTask&objectType=10&objectId=" + contentObjectId,
                            dataType: "json",
                            success: function (data) {
                                getTopFrame().location.reload();
                            },
                            error: function (data) {
                                getTopFrame().location.reload();
                            }
                        });
                    } else if (actionId == '30') {
                        // assetIds=sas-123_234+ili-245_231
                        var assetIds = 'sas-' + getSelectedIds();
                        openAddTaskModal(assetIds, $('#taskMetadataSelect'), true);
                    } else if (actionId == '35') {
                        $('#uploadBtn').iFramePopup({
                            width: 820,
                            displayOnInit: true,
                            title: client_messages.title.image_library_upload,
                            src: "content_object_bulk_image_upload.jsp",
                            appliedParams: {tk: "${param.tk}"},
                            closeBtnId: "cancelBtn_button",
                            beforePopupClose: function () {
                                rebuildListTable(true);
                            }
                        });
                    } else if (actionId == '40') {
                        $('#uploadCmsBtn').iFramePopup({
                            width: 820,
                            vertOffset: -60,
                            displayOnInit: true,
                            title: client_messages.title.image_library_upload,
                            src: "content_library_cms_upload.form",
                            appliedParams: {tk: "${param.tk}"},
                            closeBtnId: "cancelBtn_button",
                            beforePopupClose: function () {
                                rebuildListTable(true);
                            }
                        });
                    } else if (actionId == '50') {
                        $('#uploadSftpRepoBtn').iFramePopup({
                            width: 820,
                            vertOffset: -60,
                            displayOnInit: true,
                            title: client_messages.title.image_library_upload,
                            src: "content_object_sftp_image_upload.form",
                            appliedParams: {tk: "${param.tk}"},
                            closeBtnId: "cancelBtn_button",
                            beforePopupClose: function () {
                                location.reload();
                            }
                        });
                    }

                }

                // Retrieve selected ids seperated by ','
                function getSelectedIds() {
                    var selectedIds = '';
                    var selectedCount = $("input[id^='listItemCheck']:checked").length;
                    var count = 0;
                    $("input[id^='listItemCheck']:checked").each(
                        function () {
                            selectedIds += this.id.replace('listItemCheck_', '');
                            count++;
                            if (count != selectedCount)
                                selectedIds += '_';
                        }
                    );
                    return selectedIds;
                }

                function submitPreAction(submitId) {
                    if (submitId == '23') {
                        var whereUsedReportId = $('#whereUsedReportId').val();
                        popItUpWithDimensions('../where_used_report.jsp?whereUsedReportId=' + whereUsedReportId, 850, 900);
                    }
                    return false;
                }

                function validateActionReq(contentObjectInstanceId) {
                    var singleSelect = true;

                    var canUpdate = true;
                    var canReassign = true;
                    var canTranslationReassign = true;
                    var canReleaseFromTranslation = true;
                    var canReleaseForApproval = true;
                    var canApprove = true;
                    var canReject = true;
                    var canClone = true;
                    var canContentExport = showContentJSONExport;
                    var canDiscard = true;
                    var archived = true;
                    var canArchive = true;
                    var canCreateWorkingCopy = true;
                    var canDeleteArchive = true;
                    var allHasWorkflowStep = true;
                    var allHasNoWorkflowStep = true;
                    var allAreWorkflowOwner = true;
                    var allAreNotWorkflowOwner = true;
                    var canBulkTouchpointAssign = true;
                    var canMakeLocal = true;
                    var canMoveToLocal = true;
                    var allIsInTranslationStep = true;
                    var allNotIsInTranslationStep = true;
                    var allIsInApprovalStep = true;
                    var isCurrentTranslator = true;
                    var canAddTask = true;
                    var canAbort = true;
                    var canRetry = true;

                    // Resolve selection flags
                    if ($("input[id^='listItemCheck_']:checked").length != 1)
                        singleSelect = false;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        var contentObjectId = this.id.replace('listItemCheck_', '');
                        if (!exists('canUpdate_' + contentObjectId))
                            canUpdate = false;
                        if (!exists('canReassign_' + contentObjectId))
                            canReassign = false;
                        if (!exists('canTranslationReassign_' + contentObjectId))
                            canTranslationReassign = false;
                        if (!exists('canReleaseFromTranslation_' + contentObjectId))
                            canReleaseFromTranslation = false;
                        if (!exists('canClone_' + contentObjectId))
                            canClone = false;
                        if (!exists('canReleaseForApproval_' + contentObjectId))
                            canReleaseForApproval = false;
                        if (!exists('canApprove_' + contentObjectId))
                            canApprove = false;
                        if (!exists('canReject_' + contentObjectId))
                            canReject = false;
                        if (!exists('canAbort_' + contentObjectId))
                            canAbort = false;
                        if (!exists('canRetry_' + contentObjectId))
                            canRetry = false;
                        if (!exists('canDiscard_' + contentObjectId))
                            canDiscard = false;
                        if (!exists('canArchive_' + contentObjectId))
                            canArchive = false;
                        if (!exists('archived_' + contentObjectId))
                            archived = false;
                        if (!exists('canCreateWorkingCopy_' + contentObjectId))
                            canCreateWorkingCopy = false;
                        if (!exists('canDeleteArchive_' + contentObjectId))
                            canDeleteArchive = false;
                        if (!exists('canAddTask_' + contentObjectId))
                            canAddTask = false;
                        if (exists('hasNoStepForWorkflow_' + contentObjectId)) {
                            allHasWorkflowStep = false;
                        } else {
                            allHasNoWorkflowStep = false;
                        }
                        if (exists('workflowOwner_' + contentObjectId)) {
                            allAreNotWorkflowOwner = false;
                        } else {
                            allAreWorkflowOwner = false;
                        }
                        if (!exists('canBulkTouchpointAssign_' + contentObjectId))
                            canBulkTouchpointAssign = false;
                        if (!exists('canMakeLocal_' + contentObjectId))
                            canMakeLocal = false;
                        if (!exists('canMoveToLocal_' + contentObjectId))
                            canMoveToLocal = false;

                        if (exists('isInTranslationStep_' + contentObjectId)) {
                            allNotIsInTranslationStep = false;
                        } else {
                            allIsInTranslationStep = false;
                        }

                        if (!exists('isInApprovalStep_' + contentObjectId)){
                            allIsInApprovalStep = false;
                        }

                        if (!exists('isCurrentTranslator_' + contentObjectId))
                            isCurrentTranslator = false;
                    });

                    //Disable all context menu entries
                    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
                    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');
                    $actionMenu.data('complexDropdown').disableAllTheOptions();
                    common.disableElement($updateBtn);
                    common.disableElement($cloneBtn);
                    // Request report: Disable
                    $exportMenu.data('complexDropdown').disableOptionById('actionOption_11');

                    //Enable context menu entries based on flag status
                    if (singleSelect) {
                        if (canUpdate) {
                            $('a#actioniFrame_1').removeClass('disabled');
                            common.enableElement($updateBtn);
                        }
                        if (canClone) {
                            $('a#actionOption_22').removeClass('disabled');
                            common.enableElement($cloneBtn);
                        }
                        $('a#actionOption_23').removeClass('disabled');		// Where Used
                        $actionMenu.data('complexDropdown').enableOptionById('actionOption_23');

                        $('a#actioniFrame_26').removeClass('disabled'); // Calculate hash
                        $actionMenu.data('complexDropdown').enableOptionById('actioniFrame_26');
                    }

                    if ($("input[id^='listItemCheck_']:checked").length > 0) {
                        $('ul.contextMenu').find("a[id^='actionOption']").show();
                        $('ul.contextMenu').find("a[id^='actioniFrame']").show();
                        $actionMenu.data('complexDropdown').showAllTheOptions();

                        if (canBulkTouchpointAssign) {
                            $('a#actioniFrame_10').removeClass('disabled');	// Associate touchpoints
                            $actionMenu.data('complexDropdown').enableOptionById('actioniFrame_10'); // Associate touchpoints
                        }

                        if (canReassign) {
                            $('a#actionOption_5').removeClass('disabled');
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_5');	// Reassign
                        }

                        // Translation Reassign
                        if (canTranslationReassign) {
                            $('a#actionOption_41').show().removeClass('disabled');
                            $('a#actionOption_5').hide();   // Reassign
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_41');
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_41');
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_5');  // Reassign
                        }else{
                            $('a#actionOption_41').hide();
                            $('a#actionOption_5').show();   // Reassign
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_41');
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_5');   // Reassign
                        }

                        if (canMakeLocal) {
                            $('a#actionOption_24').removeClass('disabled');	// Create local
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_24'); // Create local
                        }
                        if (canMoveToLocal) {
                            $('a#actionOption_25').removeClass('disabled');	// Move to local
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_25'); // Move to local
                        }
                        if (canContentExport) {
                            $('a#actionOption_43').removeClass('disabled');
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_43'); // Content export
                        }

                        if (allHasWorkflowStep) {
                            if (allAreWorkflowOwner) {
                                $('.contextMenu a#actionOption_20').show();	// Approve and override
                                if(allNotIsInTranslationStep || allIsInApprovalStep){
                                    $('.contextMenu a#actionOption_20').show();	// Approve and override
                                    $actionMenu.data('complexDropdown').showOptionById('actionOption_20'); // Approve and override
                                }
                                if(allIsInTranslationStep){
                                    $('.contextMenu a#actionOption_29').show();	// Release from translation (Approve)
                                    $actionMenu.data('complexDropdown').showOptionById('actionOption_29'); // Release from translation (Approve)
                                }

                                $('.contextMenu a#actionOption_21').show();	// Reject and override
                                $('.contextMenu a#actionOption_17').hide();	// Approve
                                $('.contextMenu a#actionOption_18').hide();	// Reject

                                $actionMenu.data('complexDropdown').showOptionById('actionOption_21'); // Reject and override
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_17'); // Approve
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_18'); // Reject
                            } else {
                                if (allNotIsInTranslationStep || allIsInApprovalStep) {
                                    $('a#actionOption_17').show(); // Approve
                                    $actionMenu.data('complexDropdown').showOptionById('actionOption_17'); // Approve
                                }
                                if (allIsInTranslationStep && isCurrentTranslator) {
                                    $('a#actionOption_29').show(); // Release from translation (Approve)
                                    $actionMenu.data('complexDropdown').showOptionById('actionOption_29'); // Release from translation (Approve)
                                }
                                $('a#actionOption_18').show(); // Reject
                                $('a#actionOption_20').hide();	// Approve and override
                                $('a#actionOption_21').hide();	// Reject and override

                                $actionMenu.data('complexDropdown').showOptionById('actionOption_18'); // Reject
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_20'); // Approve and override
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_21'); // Reject and override
                            }

                            $('a#actionOption_6').show();  // Release for approval

                            $('a#actionOption_19').hide();	// Activate
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_19');	// Activate
                        } else {
                            $('a#actionOption_19').show(); // Activate
                            $('a#actionOption_20').hide();	// Approve and override
                            $('a#actionOption_21').hide();	// Reject and override
                            $('a#actionOption_6').hide();	// Release for approval
                            $('a#actionOption_17').hide();	// Approve
                            $('a#actionOption_29').hide();	// Release from translation (Approve)
                            $('a#actionOption_18').hide();	// Reject
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_19'); // Activate
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_20'); // Approve and override
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_21'); // Reject and override
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_6'); // Release for approval
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_17'); // Approve
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_29'); // Release from translation (Approve)
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_18'); // Reject
                        }

                        if (canReleaseForApproval) {
                            $('a#actionOption_6').removeClass('disabled');	// Release for approval
                            $('a#actionOption_19').removeClass('disabled');	// Activate
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_6');	// Release for approval
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_19');	// Activate
                        }
                        if (canApprove) {
                            if(allIsInApprovalStep){
                                $('.contextMenu a#actionOption_17').removeClass('disabled'); // Approve
                                $actionMenu.data('complexDropdown').enableOptionById('actionOption_17'); // Approve
                            }

                            if(allIsInApprovalStep && allAreWorkflowOwner) {
                                $('.contextMenu a#actionOption_20').removeClass('disabled'); // Approve and override
                                $actionMenu.data('complexDropdown').enableOptionById('actionOption_20'); // Approve and override
                            }
                        }
                        if (canReleaseFromTranslation) {
                            if(allIsInTranslationStep && (isCurrentTranslator || allAreWorkflowOwner)){
                                $('.contextMenu a#actionOption_29').removeClass('disabled'); // Release from translation (Approve)
                                $actionMenu.data('complexDropdown').enableOptionById('actionOption_29'); // Release from translation (Approve)
                            }
                        }
                        if (canReject) {
                            $('a#actionOption_18').removeClass('disabled'); // Reject
                            $('a#actionOption_21').removeClass('disabled'); // Reject and override
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_18'); // Reject
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_21'); // Reject and override
                        }
                        if (canAbort){
                            $('a#actionOption_40').removeClass('disabled'); // Abort
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_40'); // Abort
                        }
                        if (canRetry){
                            $('a#actionOption_45').removeClass('disabled'); // Retry
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_45'); // Abort
                        }

                        if (canCreateWorkingCopy) {
                            $('a#actionOption_2').hide();
                            $('a#actionOption_1').show();
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_2');	// Discard WC
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_1');	// Create WC
                            $('a#actionOption_1').removeClass('disabled'); // Create WC
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_1'); // Create WC
                        } else {
                            $('a#actionOption_2').hide();
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_2');	// Discard WC
                        }
                        if (canDiscard) {
                            $('a#actionOption_1').hide();
                            $('a#actionOption_2').show();
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_1');	// Create WC
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_2');	// Discard WC
                            $('a#actionOption_2').removeClass('disabled'); // Discard WC
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_2'); // Discard WC
                        }
                        if (canArchive) {
                            $('a#actionOption_4').hide();	// Delete Archive
                            $('a#actionOption_3').show();	// Archive
                            $('a#actionOption_3').removeClass('disabled'); // Archive
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_4'); // Delete Archive
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_3');	// Archive
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_3'); // Archive
                        } else {
                            $('a#actionOption_4').hide();	// Delete Archive
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_4'); // Delete Archive
                        }
                        if (archived) {
                            common.disableElement($updateBtn);
                            common.enableElement($cloneBtn);
                            $('ul.contextMenu').find("a[id^='actionOption']").hide();
                            $('ul.contextMenu').find("a[id^='actioniFrame']").hide();
                            $('a#actionOption_1').show();	// Create WC
                            $('a#actionOption_4').show();	// Delete Archive
                            $('a#actionOption_1').removeClass('disabled'); // Create WC
                            $('a#actionOption_4').removeClass('disabled'); // Delete Archive
                            $actionMenu.data('complexDropdown').hideAllTheOptions();
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_1');	// Create WC
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_4');	// Delete Archive
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_22');	// Clone
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_1'); // Create WC
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_4'); // Delete Archive
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_22');	// Clone
                        }
                        if ($("input[id^='listItemCheck_']:checked").length == 1) {
                            // Request report
                            $exportMenu.data('complexDropdown').enableOptionById('actionOption_11');
                        }

                        if (canAddTask) {
                            $('.contextMenu a#actioniFrame_30').removeClass('disabled'); // Add task
                            $actionMenu.data('complexDropdown').enableOptionById('actioniFrame_30'); // Add task
                            $('.contextMenu a#actionOption_31').removeClass('disabled'); // Add task
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_31'); // Add task
                        }
                    }

                    hideDisabledContextMenus();
                    $actionMenu.data('complexDropdown').hideAllDisabledOptions();
                }
                // ******* CONTEXT ACTIONS: END *******

            </script>
        </msgpt:Script>
        <style id="defaultContentViewStyles">
            ${default_css}
        </style>
    </msgpt:HeaderNew>
    <!-- FLAGS AND PERMISSIONS -->
    <c:set var="viewObjectPermission" value="false"/>
    <c:set var="editObjectPermission" value="false"/>
    <c:set var="adminObjectPermission" value="false"/>
    <c:set var="viewMetatagsPermission" value="false"/>
    <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
        <c:set var="viewMetatagsPermission" value="true"/>
    </msgpt:IfAuthGranted>
    <!-- END FLAGS AND PERMISSIONS -->
    <c:choose>
        <c:when test="${not empty param.isFreeform && param.isFreeform == true}">
            <c:set var="typeLabelCode" value="smart.canvas"/>
            <c:set var="addBtnToolTip" value="page.label.add.smart.canvas"/>
            <c:set var="isImageLibrary" value="false"/>
            <input type="hidden" id="localContentType" value="1"/>
            <msgpt:IfAuthGranted authority="ROLE_EMBEDDED_CONTENT_VIEW">
                <c:set var="viewObjectPermission" value="true"/>
            </msgpt:IfAuthGranted>
            <msgpt:IfAuthGranted authority="ROLE_EMBEDDED_CONTENT_EDIT">
                <c:set var="editObjectPermission" value="true"/>
            </msgpt:IfAuthGranted>
            <msgpt:IfAuthGranted authority="ROLE_EMBEDDED_CONTENT_ADMIN">
                <c:set var="adminObjectPermission" value="true"/>
            </msgpt:IfAuthGranted>
        </c:when>
        <c:when test="${empty param.localContentType || param.localContentType == 1}">
            <c:set var="typeLabelCode" value="smart.text"/>
            <c:set var="addBtnToolTip" value="page.label.add.smart.text"/>
            <c:set var="isImageLibrary" value="false"/>
            <input type="hidden" id="localContentType" value="1"/>
            <msgpt:IfAuthGranted authority="ROLE_EMBEDDED_CONTENT_VIEW">
                <c:set var="viewObjectPermission" value="true"/>
            </msgpt:IfAuthGranted>
            <msgpt:IfAuthGranted authority="ROLE_EMBEDDED_CONTENT_EDIT">
                <c:set var="editObjectPermission" value="true"/>
            </msgpt:IfAuthGranted>
            <msgpt:IfAuthGranted authority="ROLE_EMBEDDED_CONTENT_ADMIN">
                <c:set var="adminObjectPermission" value="true"/>
            </msgpt:IfAuthGranted>
        </c:when>
        <c:otherwise>
            <c:set var="typeLabelCode" value="image.library"/>
            <c:set var="addBtnToolTip" value="page.label.add.image"/>
            <c:set var="isImageLibrary" value="true"/>
            <input type="hidden" id="localContentType" value="2"/>
            <msgpt:IfAuthGranted authority="ROLE_CONTENT_LIBRARY_VIEW">
                <c:set var="viewObjectPermission" value="true"/>
            </msgpt:IfAuthGranted>
            <msgpt:IfAuthGranted authority="ROLE_CONTENT_LIBRARY_EDIT">
                <c:set var="editObjectPermission" value="true"/>
            </msgpt:IfAuthGranted>
            <msgpt:IfAuthGranted authority="ROLE_CONTENT_LIBRARY_ADMIN">
                <c:set var="adminObjectPermission" value="true"/>
            </msgpt:IfAuthGranted>
        </c:otherwise>
    </c:choose>
    <msgpt:BodyNew>
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_CONTENTS %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_CONTENTS %>"/>
        <c:if test="${param.localContentType == 1}">
            <msgpt:ContextBarNew globalContextApplied="true"
                                 touchpointSetupContext="true"
                                 workflowURL="global_content_workflow_edit.form?type=11"
                                 workflowPermission="ROLE_EMBEDDED_CONTENT_ADMIN" searchContextApplied="true"
                                 searchContextType="embeddedText"/>
        </c:if>
        <c:if test="${param.localContentType == 2}">
            <msgpt:ContextBarNew globalContextApplied="true" touchpointSetupContext="true"
                                 workflowURL="global_content_workflow_edit.form?type=12"
                                 workflowPermission="ROLE_CONTENT_LIBRARY_ADMIN"/>
        </c:if>
        <msgpt:LowerContainer fullPanel="true" extendedWidth="false">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="false">
                <c:if test="${not noTouchpoints or trashTpContext}">
                    <form:form method="post" modelAttribute="command">
                        <form:errors path="*">
                            <msgpt:Information errorMsgs="${messages}" type="error"/>
                        </form:errors>
                        <c:if test="${not empty param.saveSuccess && param.saveSuccess == 'true'}">
                            <msgpt:Information type="success">
                                <fmtSpring:message code="page.label.save.complete"/>
                            </msgpt:Information>
                        </c:if>
                        <h1 class="h4 pb-2 mb-4"><fmtSpring:message code="page.label.${typeLabelCode}"/>
                            <c:if test="${not trashTpContext && isImageLibrary}">
                                <msgpt:IfAuthGranted authority="ROLE_CONTENT_LIBRARY_EDIT">
                                    <div class="ml-3 pl-3 border-left" style="display: inline-block;">
                                        <div class="btn-group btn-group-sm align-top border-separate" role="group"
                                             aria-label="${msgpt:getMessage("page.label.actions")}">
                                            <button id="uploadBtn" class="btn btn-primary" type="button"
                                                    onclick="iFrameAction(35);">
                                                <i class="far fa-cloud-upload mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="page.label.bulk.upload"/>
                                            </button>
                                            <c:if test="${cmsEnabled}">
                                                <button id="uploadCmsBtn" class="btn btn-primary" type="button"
                                                        onclick="iFrameAction(40);">
                                                    <i class="far fa-cloud-upload mr-2" aria-hidden="true"></i>
                                                    <fmtSpring:message code="page.label.cms.upload"/>
                                                </button>
                                            </c:if>
                                            <c:if test="${sftpRepoEnabled}">
                                                <button id="uploadSftpRepoBtn" class="btn btn-primary"
                                                        type="button" onclick="iFrameAction(50);">
                                                    <i class="far fa-cloud-upload mr-2" aria-hidden="true"></i>
                                                    <fmtSpring:message code="page.label.sftprepo.upload"/>
                                                </button>
                                            </c:if>
                                        </div>
                                    </div>
                                </msgpt:IfAuthGranted>
                            </c:if>
                        </h1>
                        <div class="box-shadow-4 rounded bg-white p-4">
                            <div class="px-2 py-1">
                                <div class="mb-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <form:hidden path="actionValue" id="actionElement"/>
                                        <c:if test="${not trashTpContext }">
                                            <!-- Button: Add Smart Text -->
                                            <c:if test="${editObjectPermission}">
                                                <div class="mr-3">
                                                    <button id="addContentObjectBtn"
                                                            class="btn btn-primary ${hasMetadataFormDef ? '' : 'post-trigger'}"
                                                            type="button">
                                                        <i class="far fa-plus-circle mr-2" aria-hidden="true"></i>
                                                        <fmtSpring:message code="page.label.add"/>
                                                    </button>
                                                </div>
                                            </c:if>
                                        </c:if>
                                        <div class="btn-group border-separate mr-auto" role="group"
                                             aria-label="${msgpt:getMessage("page.label.actions")}">
                                            <button id="updateBtn" type="button" class="btn btn-dark post-trigger"
                                                    onclick="iFrameAction(1);" disabled>
                                                <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="action.button.label.update"/>
                                            </button>
                                            <button id="cloneBtn" type="button" class="btn btn-dark"
                                                    onclick="actionSelected(22);" disabled>
                                                <i class="far fa-clone mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="page.label.clone"/>
                                            </button>
                                            <div class="btn-group">
                                                <select title="${msgpt:getMessage('page.label.audit')}"
                                                        id="exportMenu"
                                                        class="complex-dropdown-select"
                                                        onchange="infoItemAction(this,-1)"
                                                        aria-label="${msgpt:getMessage('page.label.audit')}"
                                                        data-toggle="complex-dropdown"
                                                        data-action="menu"
                                                        data-dropdown-class="btn-dark">
                                                    <option id="actionOption_11"><fmtSpring:message
                                                            code="page.label.generate.audit.report"/></option>
                                                    <c:if test="${not empty auditReport}">
                                                        <option class="divider" disabled/>
                                                        <c:choose>
                                                            <c:when test="${auditReport.statusId == 1}"> <!-- Audit Report in process -->
                                                                <option id="itemInProcess_${auditReport.id}"
                                                                        type="inProcess" disabled>
                                                                    <fmtSpring:message code="page.label.processing"/> -
                                                                    <fmtJSTL:formatDate
                                                                            value="${auditReport.requestDate}"
                                                                            pattern="${dateTimeFormat}"/>
                                                                </option>
                                                            </c:when>
                                                            <c:when test="${auditReport.statusId == 3 }"> <!-- Audit Report error -->
                                                                <option id="itemError_${auditReport.id}" deliveryId="-1"
                                                                        itemClass="null" class="text-danger">
                                                                    <fmtSpring:message code="page.label.error"/> -
                                                                    <fmtJSTL:formatDate
                                                                            value="${auditReport.requestDate}"
                                                                            pattern="${dateTimeFormat}"/>
                                                                </option>
                                                            </c:when>
                                                            <c:otherwise> <!-- View report -->
                                                                <option id="itemReport_${auditReport.id}"
                                                                        value="${auditReport.reportPath}">
                                                                    <fmtSpring:message
                                                                            code="page.label.view.audit.report"/> -
                                                                    <fmtJSTL:formatDate
                                                                            value="${auditReport.requestDate}"
                                                                            pattern="${dateTimeFormat}"/>
                                                                </option>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </c:if>
                                                </select>
                                            </div>
                                            <div class="btn-group">
                                                <select title="${msgpt:getMessage('page.label.more')}"
                                                        id="actionMenu"
                                                        class="complex-dropdown-select"
                                                        aria-label="${msgpt:getMessage('page.label.more')}"
                                                        data-toggle="complex-dropdown"
                                                        data-action="menu"
                                                        data-dropdown-class="btn-dark"
                                                        onchange="actionMenuSelected(this)">
                                                    <option id="actionOption_1" disabled><fmtSpring:message
                                                            code="page.label.create.working.copy"/></option>
                                                    <option id="actionOption_19" class="d-none" disabled>
                                                        <fmtSpring:message code="page.label.activate"/></option>
                                                    <option id="actionOption_6" disabled><fmtSpring:message
                                                            code="page.label.release.for.approval"/></option>
                                                    <option id="actionOption_17" disabled><fmtSpring:message
                                                            code="page.label.approve"/></option>
                                                    <option id="actionOption_29" disabled><fmtSpring:message
                                                            code="page.label.release.from.translation"/></option>
                                                    <option id="actionOption_18" disabled><fmtSpring:message
                                                            code="page.label.reject"/></option>
                                                    <option id="actionOption_20" class="d-none" disabled>
                                                        <fmtSpring:message
                                                                code="page.label.approve.and.override"/></option>
                                                    <option id="actionOption_21" class="d-none" disabled>
                                                        <fmtSpring:message
                                                                code="page.label.reject.and.override"/></option>
                                                    <option id="actionOption_40" disabled>
                                                        <fmtSpring:message
                                                                code="page.label.abort"/></option>
                                                    <option id="actionOption_45" disabled>
                                                        <fmtSpring:message
                                                                code="page.label.retry.translation"/></option>
                                                    <option id="actionOption_2" class="d-none" disabled>
                                                        <fmtSpring:message
                                                                code="page.label.discard.working.copy"/></option>
                                                    <option class="divider" disabled/>
                                                    <option id="actionOption_5" disabled><fmtSpring:message
                                                            code="page.label.reassign"/></option>
                                                    <option id="actionOption_41" disabled><fmtSpring:message
                                                            code="page.label.reassign"/></option>
                                                    <option id="actionOption_43" disabled><fmtSpring:message
                                                            code="page.label.content.export"/></option>
                                                    <option id="actionOption_24" disabled><fmtSpring:message
                                                            code="page.label.create.local"/></option>
                                                    <option id="actionOption_25" disabled><fmtSpring:message
                                                            code="page.label.move.to.local"/></option>
                                                    <c:if test="${adminObjectPermission}">
                                                        <option id="actioniFrame_10" disabled>
                                                            <fmtSpring:message
                                                                    code="page.label.associate.touchpoints"/></option>
                                                    </c:if>
                                                    <c:if test="${adminObjectPermission}">
                                                        <option id="actionOption_3" disabled>
                                                            <fmtSpring:message
                                                                    code="page.label.archive"/></option>
                                                        <option id="actionOption_4" class="d-none" disabled>
                                                            <fmtSpring:message
                                                                    code="page.label.delete.archive"/></option>
                                                    </c:if>
                                                    <c:if test="${empty param.isFreeform || param.isFreeform == false}">
                                                        <option id="actionOption_23" disabled><fmtSpring:message
                                                                code="page.label.where.used"/></option>
                                                    </c:if>
                                                    <option class="divider" disabled/>
                                                    <option id="actioniFrame_26" disabled><fmtSpring:message
                                                            code="page.label.calculate.hash"/></option>
                                                    <c:if test="${not hasTaskMetadataFormDef}">
                                                        <option id="actioniFrame_30" disabled>
                                                            <fmtSpring:message
                                                                    code="page.label.add.task"/></option>
                                                    </c:if>
                                                    <c:if test="${hasTaskMetadataFormDef}">
                                                        <option id="actionOption_31" disabled>
                                                            <fmtSpring:message
                                                                    code="page.label.add.task"/></option>
                                                    </c:if>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="mr-3 ml-5" data-toggle="tooltip"
                                             title="${msgpt:getMessage('client_messages.label.toggle_list_display')}">
                                            <div class="btn-group">
                                                <button id="listTypeToggleGlobalList" type="button"
                                                        data-toggle="button"
                                                        aria-pressed="false"
                                                        class="btn btn-link btn-link-inline mark-pressed text-dark persistedClass cardViewToggle">
                                                    <i class="far fa-th fa-lg" aria-hidden="true"></i>
                                                    <span class="sr-only"><fmtSpring:message
                                                            code="client_messages.label.toggle_list_display"/></span>
                                                </button>
                                                <button type="button"
                                                        class="btn dropdown-toggle dropdown-toggle-split cardViewVisToggle"
                                                        style="display: none; padding-right: 0.5rem; padding-left: 0.5rem"
                                                        data-toggle="dropdown" aria-haspopup="true"
                                                        aria-expanded="false">
                                                    <span class="sr-only">Toggle Dropdown</span>
                                                </button>
                                                <div class="dropdown-menu dropdown-custom">
                                                    <div class="dropdown-item">
                                                        <div class="custom-control custom-checkbox">
                                                            <input type="checkbox"
                                                                   class="custom-control-input persistedCheckbox"
                                                                   checked="checked" id="cardDisplayToggle_name">
                                                            <label class="custom-control-label"
                                                                   for="cardDisplayToggle_name"><fmtSpring:message
                                                                    code="page.label.name"/></label>
                                                        </div>
                                                    </div>
                                                    <div class="dropdown-item">
                                                        <div class="custom-control custom-checkbox">
                                                            <input type="checkbox"
                                                                   class="custom-control-input persistedCheckbox"
                                                                   checked="checked" id="cardDisplayToggle_content">
                                                            <label class="custom-control-label"
                                                                   for="cardDisplayToggle_content"><fmtSpring:message
                                                                    code="page.label.content"/></label>
                                                        </div>
                                                    </div>
                                                    <div class="dropdown-item">
                                                        <div class="custom-control custom-checkbox">
                                                            <input type="checkbox"
                                                                   class="custom-control-input persistedCheckbox"
                                                                   checked="checked" id="cardDisplayToggle_details">
                                                            <label class="custom-control-label"
                                                                   for="cardDisplayToggle_details"><fmtSpring:message
                                                                    code="page.label.details"/></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mr-3 colVisToggleContainer" data-toggle="tooltip"
                                             title="${msgpt:getMessage('client_messages.label.toggle_columns')}">
                                        </div>
                                        <div class="form-group position-relative d-inline-block m-0">
                                            <label for="listSearchInput" class="sr-only"><fmtSpring:message
                                                    code="page.label.search"/></label>
                                            <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                               style="z-index: 1;" aria-hidden="true"></i>
                                            <msgpt:InputFilter type="description">
                                                <input id="listSearchInput" type="text"
                                                       class="form-control bg-lightest has-control-x border-0"
                                                    ${viewMetatagsPermission ? 'data-toggle="tagcloud"' :''}
                                                       data-cloud-type="${param.localContentType == 1 ? 2 : 3}"
                                                       size="25"
                                                       placeholder="${msgpt:getMessage('page.label.search')}"/>
                                            </msgpt:InputFilter>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center pt-1">
                                        <span class="text-dark mr-1" id="filter">
                                            <fmtSpring:message code="page.label.filter"/>:
                                        </span>
                                        <div class="mx-2">
                                            <select id="contentObjectsListAssignmentFilter" aria-labelledby="filter"
                                                    data-toggle="complex-dropdown"
                                                    data-menu-class="dropdown-custom mt-2"
                                                    class="complex-dropdown-select persistedValue"
                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                    onchange="toggleFilter(this)">
                                                <c:forEach items="${primaryFilterTypes}" var="currentFilter">
                                                    <option id="contentObjectsListAssignmentFilter_${currentFilter.id}"
                                                            value="${currentFilter.id}"><fmtSpring:message
                                                            code="${currentFilter.displayMessageCode}"/></option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                        <span class="text-dark mx-1" id="embedded-status">
                                            <fmtSpring:message code="page.text.${typeLabelCode}.which.are"/>
                                        </span>
                                        <div class="ml-2">
                                            <form:select id="contentObjectListStatusFilter"
                                                    path="statusFilterId"
                                                    aria-labelledby="embedded-status"
                                                    data-toggle="complex-dropdown"
                                                    data-menu-class="dropdown-custom mt-2"
                                                    class="complex-dropdown-select persistedValue"
                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                    onchange="toggleFilter(this)">
                                                <c:forEach items="${contentObjectStatusFilterTypes}"
                                                           var="currentFilter">
                                                    <form:option id="contentObjectListStatusFilter_${currentFilter.id}"
                                                            value="${currentFilter.id}"><fmtSpring:message
                                                            code="${currentFilter.displayMessageCode}"/></form:option>
                                                </c:forEach>
                                            </form:select>
                                        </div>
                                        <span class="text-dark mx-1 cardSortInterface" id="content-type"
                                              style="display: none;">
                                        	<fmtSpring:message code="page.text.sorted.by"/>
                                       	</span>
                                        <div class="mx-2 cardSortInterface" style="display: none;">
                                            <select id="contentObjectListSortBySelect"
                                                    class="complex-dropdown-select persistedValue"
                                                    aria-labelledby="content-type"
                                                    data-toggle="complex-dropdown"
                                                    data-menu-class="dropdown-custom mt-2"
                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                    onchange="toggleSort(this)">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white">
                                    <msgpt:DataTable id="contentObjectList" listHeader="page.label.messages"
                                                     async="true"
                                                     columnReorder="true" numUnreorderableCols="2"
                                                     columnVisibility="true"
                                                     drillDown="true" multiSelect="true" searchFilter="true">
                                    </msgpt:DataTable>
                                </div>
                            </div>
                        </div>

                        <!-- POPUP DATA -->
                        <div id="actionSpecs" style="display: none;">
                            <!-- ACTIONS POPUP DATA -->
                            <div id="actionSpec_1" type="simpleConfirm" submitId="4"> <!-- Create working copy -->
                                <div id="actionTitle_1"><fmtSpring:message
                                        code="page.label.confirm.create.working.copy"/></div>
                                <div id="actionInfo_1"><fmtSpring:message
                                        code="page.text.create.working.copy.of.selected.assets"/></div>
                                <div id="actionContinueToTaskSelectButtons_1"></div>
                            </div>
                            <div id="actionSpec_2" type="simpleConfirm" submitId="5"> <!-- Discard working copy -->
                                <div id="actionTitle_2"><fmtSpring:message
                                        code="page.label.confirm.discard.working.copy"/></div>
                                <div id="actionInfo_2"><fmtSpring:message
                                        code="page.text.discard.working.copy.of.asset"/></div>
                            </div>
                            <div id="actionSpec_3" submitId="6"> <!-- Archive -->
                                <div id="actionTitle_3"><fmtSpring:message code="page.label.confirm.archive"/></div>
                                <div id="actionInfo_3"><fmtSpring:message code="page.text.archive.active.asset"/></div>
                                <div id="actionNote_3" required="true"><fmtSpring:message
                                        code="page.text.note.required.brackets"/></div>
                            </div>
                            <div id="actionSpec_4" type="simpleConfirm" submitId="7"> <!-- Delete archive -->
                                <div id="actionTitle_4"><fmtSpring:message
                                        code="page.label.confirm.delete.archive"/></div>
                                <div id="actionInfo_4"><fmtSpring:message code="page.text.delete.archived.asset"/></div>
                            </div>
                            <div id="actionSpec_5" submitId="3"> <!-- Reassign to user -->
                                <div id="actionTitle_5"><fmtSpring:message
                                        code="page.label.confirm.reassign.to.user"/></div>
                                <div id="actionInfo_5"><fmtSpring:message
                                        code="page.text.reassign.asset.to.user"/></div>
                                <div id="actionNote_5"></div>
                                <div id="actionUserSelect_5" required="true"
                                     type="${param.localContentType == 1 ? 'embeddedContent_currentStageUsers' : 'contentLibrary_currentStageUsers'}"></div>
                            </div>
                            <div id="actionSpec_41" submitId="41"> <!-- Translation Reassign to user -->
                                <div id="actionTitle_41"><fmtSpring:message
                                        code="page.label.confirm.reassign.to.user"/></div>
                                <div id="actionInfo_41"><p><b><fmtSpring:message
                                        code="page.text.assign.message.to.user"/></b></p></div>
                                <div id="actionNote_41"></div>
                                <div id="actionWorkflowSelect_41" required="true" type="messages_releaseFromTransWorkflows"></div>
                                <div id="actionUserSelect_41" required="true" type="currentTranslators"></div>
                            </div>
                            <div id="actionSpec_6" submitId="9"> <!-- Release for approval -->
                                <div id="actionTitle_6"><fmtSpring:message
                                        code="page.label.confirm.release.for.approval"/></div>
                                <div id="actionInfo_6"><fmtSpring:message
                                        code="page.text.provide.description.of.action.required"/></div>
                                <div id="actionNote_6"></div>
                            </div>
                            <!-- AUDIT POPUP DATA -->
                            <div id="actionSpec_11" submitId="16" contentWidth="330px"> <!-- Generate audit report -->
                                <div id="actionTitle_11"><fmtSpring:message
                                        code="page.label.generate.audit.report"/></div>
                                <div id="actionInfo_11"><fmtSpring:message
                                        code="page.text.about.to.generate.audit.report"/></div>
                                <div id="actionReportOptions_11"></div>
                            </div>
                            <!-- APPROVE -->
                            <div id="actionSpec_17" type="simpleConfirm" submitId="17" contentWidth="330px">
                                <!-- Approve -->
                                <div id="actionTitle_17"><fmtSpring:message code="page.label.approve"/></div>
                                <div id="actionInfo_17"><fmtSpring:message
                                        code="page.text.would.you.like.to.approve"/></div>
                                <div id="actionNote_17"></div>
                                <div id="actionWorkflowSelect_17" required="true" type="messages_approveWorkflows"></div>
                                <div id="actionApproval_17" approveSubmitId="17"></div>
                            </div>
                            <!-- Translation Step approve -->
                            <div id="actionSpec_29" type="simpleConfirm" submitId="17" contentWidth="330px">
                                <!-- Approve -->
                                <div id="actionTitle_29"><fmtSpring:message
                                        code="page.label.approve.workflow.translation"/></div>
                                <div id="actionInfo_29"><p><b><fmtSpring:message
                                        code="page.text.workflow.step.release.from.translation"/></b></p></div>
                                <div id="actionWorkflowSelect_29" required="true" type="messages_releaseFromTransWorkflows"></div>
                                <div id="actionReleaseFromTranslation_29" approveSubmitId="17"></div>
                            </div>
                            <!-- REJECT -->
                            <div id="actionSpec_18" type="simpleConfirm" submitId="18" contentWidth="330px">
                                <!-- Reject -->
                                <div id="actionTitle_18"><fmtSpring:message code="page.label.reject"/></div>
                                <div id="actionInfo_18"><fmtSpring:message
                                        code="page.text.would.you.like.reject"/></div>
                                <div id="actionNote_18" required="true"><fmtSpring:message
                                        code="page.text.note.required.to.reject.brackets"/></div>
                                <div id="actionWorkflowSelect_18" required="true" type="messages_rejectWorkflows"></div>
                                <div id="actionUserSelect_18" required="true"
                                     type="${param.localContentType == 1 ? 'embeddedContent_rejectToUsers' : 'contentLibrary_rejectToUsers'}"></div>
                                <div id="actionReject_18" rejectSubmitId="18"></div>
                            </div>
                            <!-- RETRY -->
                            <div id="actionSpec_45" type="simpleConfirm" submitId="45">
                                <!-- Reject -->
                                <div id="actionTitle_45"><fmtSpring:message code="page.label.retry.translation"/></div>
                                <div id="actionInfo_45"><fmtSpring:message
                                        code="page.text.would.you.like.to.retry.translation"/></div>
                                <div id="actionNote_45"></div>
                                <div id="actionWorkflowSelect_45" required="true" type="messages_retryWorkflows"></div>
                            </div>
                            <div id="actionSpec_46" submitId="4"> <!-- Link task -->
                                <div id="actionTitle_46"><fmtSpring:message code="page.label.link.task"/></div>
                                <div id="actionInfo_46"><p><b><fmtSpring:message
                                        code="page.text.would.you.like.to.link.with.an.existing.task"/></b></p></div>
                                <div id="actionTaskSelect_46" required="true"></div>
                                <c:if test="${requiresDescriptionOnTasks}">
                                    <div id="actionNote_46" required="true"><fmtSpring:message
                                            code="page.text.description.required.to.create.new.task.brackets"/></div>
                                </c:if>
                                <c:if test="${!requiresDescriptionOnTasks}">
                                    <div id="actionNote_46"></div>
                                </c:if>
                                <div id="actionContinueTaskSelectButtons_46"></div>
                            </div>
                            <!-- Activate -->
                            <div id="actionSpec_19" submitId="9">
                                <div id="actionTitle_19"><fmtSpring:message code="page.label.confirm.activate"/></div>
                                <div id="actionInfo_19"><fmtSpring:message code="page.text.activate.assets"/></div>
                            </div>
                            <!-- Override approve -->
                            <div id="actionSpec_20" type="simpleConfirm" submitId="17" contentWidth="330px">
                                <!-- Approve -->
                                <div id="actionTitle_20"><fmtSpring:message
                                        code="page.label.approve.workflow.owner"/></div>
                                <div id="actionInfo_20"><fmtSpring:message
                                        code="page.text.workflow.owners.may.approve.step"/></div>
                                <div id="actionWorkflowSelect_20" required="true" type="messages_approveWorkflows"></div>
                                <div id="actionApproval_20" approveSubmitId="17"></div>
                            </div>
                            <!-- Override reject -->
                            <div id="actionSpec_21" type="simpleConfirm" submitId="18" contentWidth="330px">
                                <!-- Reject -->
                                <div id="actionTitle_21"><fmtSpring:message
                                        code="page.label.reject.workflow.owner"/></div>
                                <div id="actionInfo_21"><fmtSpring:message
                                        code="page.text.workflow.owners.may.reject.step"/></div>
                                <div id="actionNote_21" required="true"><fmtSpring:message
                                        code="page.text.note.required.to.reject.brackets"/></div>
                                <div id="actionWorkflowSelect_21" required="true" type="messages_rejectWorkflows"></div>
                                <div id="actionUserSelect_21" required="true"
                                     type="${param.localContentType == 1 ? 'embeddedContent_rejectToUsers' : 'contentLibrary_rejectToUsers'}"></div>
                                <div id="actionReject_21" rejectSubmitId="18"></div>
                            </div>
                            <!-- Clone -->
                            <div id="actionSpec_22" submitId="2" contentWidth="500px"> <!-- Clone -->
                                <div id="actionTitle_22"><fmtSpring:message code="page.label.confirm.clone"/></div>
                                <div id="actionInfo_22"><p><fmtSpring:message
                                        code="page.text.would.you.like.to.clone.selected.asset"/></p></div>
                                <div id="actionClone_22" required="true"></div>
                            </div>
                            <!-- Content Export -->
                            <div id="actionSpec_43" submitId="43"> <!-- Content Export -->
                                <div id="actionTitle_43"><fmtSpring:message code="page.label.confirm.content.export"/></div>
                                <div id="actionInfo_43"><p><b><fmtSpring:message
                                        code="page.text.content.export.selected.messages"/></b></p></div>
                                <div id="actionPopupContentJSONExport_43"></div>
                            </div>
                            <!-- Where Used -->
                            <div id="actionSpec_23" submitId="23"> <!-- Where used -->
                                <div id="actionTitle_23"><fmtSpring:message
                                        code="page.label.confirm.generate.where.used"/></div>
                                <div id="actionInfo_23"><fmtSpring:message
                                        code="page.text.report.loads.in.separate.window"/></div>
                                <div id="actionWhereUsedOptions_23"></div>
                            </div>
                            <div id="actionSpec_24" submitId="24"> <!-- Create local -->
                                <div id="actionTitle_24"><fmtSpring:message
                                        code="page.label.confirm.create.local"/></div>
                                <div id="actionInfo_24"><fmtSpring:message
                                        code="page.text.create.selected.smart.text.local"/></div>
                                <c:if test="${not empty variants}">
                                    <div id="actionMakeLocalVariantsSelect_24"></div>
                                </c:if>
                            </div>
                            <div id="actionSpec_25" submitId="25"> <!-- Move to local -->
                                <div id="actionTitle_25"><fmtSpring:message
                                        code="page.label.confirm.move.to.local"/></div>
                                <div id="actionInfo_25"><fmtSpring:message
                                        code="page.text.move.selected.smart.text.local"/></div>
                                <c:if test="${not empty variants}">
                                    <div id="actionMoveToLocalVariantsSelect_25"></div>
                                </c:if>
                            </div>
                            <div id="actionSpec_33"> <!-- Add with metadata -->
                                <div id="actionTitle_33"><fmtSpring:message code="${addBtnToolTip}"/></div>
                                <div id="actionMetadataSelect_33"></div>
                                <div id="actionMetadataButtons_33"></div>
                            </div>
                            <div id="actionSpec_31"> <!-- Add task with metadata -->
                                <div id="actionTitle_31"><fmtSpring:message code="page.label.add.task"/></div>
                                <div id="actionTaskMetadataSelect_31"></div>
                                <div id="actionTaskMetadataButtons_31"></div>
                            </div>
                            <div id="actionSpec_40" submitId="40"> <!-- Abort workflow -->
                                <div id="actionTitle_40"><fmtSpring:message code="page.label.abort"/></div>
                                <div id="actionInfo_40"><p><b><fmtSpring:message
                                        code="page.text.would.you.like.to.abort"/></b></p></div>
                                <div id="actionNote_40"></div>
                                <div id="actionAbort_40" abortSubmitId="40"></div>
                            </div>
                        </div>

                        <!-- POPUP INTERFACE -->
                        <form:hidden path="whereUsedReportId" id="whereUsedReportId"/>
                        <msgpt:Popup id="actionPopup" theme="minimal">
                            <div id="actionPopupInfoFrame">
                                <div id="actionPopupInfo">&nbsp;</div>
                            </div>
                            <div id="actionPopupWorkflowSelect">
                                <br>
                                <div class="formControl">
                                    <div class="controlWrapper">
                                        <form:select id="workflowSelect" path="actionToWorkflow" cssClass="inputL"
                                                     onchange="selectWorkflow()" onkeyup="selectWorkflow()" >
                                            <option id="0" value="0"><fmtSpring:message
                                                    code="page.text.loading"/></option>
                                        </form:select>
                                    </div>
                                </div>
                            </div>
                            <div id="actionPopupUserSelect">
                                <br>
                                <div class="formControl">
                                    <div class="controlWrapper">
                                        <form:select id="userSelect" path="assignedToUser" cssClass="inputL"
                                                     onchange="validatePopupReq()" onkeyup="validatePopupReq()">
                                            <option id="0" value="0"><fmtSpring:message
                                                    code="page.text.loading"/></option>
                                        </form:select>
                                    </div>
                                </div>
                            </div>
                            <div id="actionPopupClone">
                                <div class="formControl">
                                    <label><span class="labelText"><fmtSpring:message
                                            code="page.label.name"/></span></label>
                                    <div class="controlWrapper">
                                        <msgpt:InputFilter type="simpleName">
                                            <form:input path="cloneName"
                                                        onkeyup="validatePopupReq();"
                                                        onchange="validatePopupReq();"
                                                        id="clone_newInstanceName" onfocus="this.select()"/>
                                        </msgpt:InputFilter>
                                    </div>
                                </div>
                            </div>
                            <div id="actionPopupMakeLocalVariantsSelect" style="padding: 2px 8px 24px 8px;"
                                 align="left">
                                <div style="color: rgb(68, 68, 68); display: block; padding-top: 6px; padding-bottom: 2px; text-align: left;">
                                    <fmtSpring:message code="page.label.variants"/>
                                </div>
                                <div id="variantsSelect" class="style_multiselect inputXL">
                                    <c:forEach var="currentVariant" items="${variants}">
                                        <div id="variantOption_${currentVariant.id}" class="">
                                            <fmtSpring:bind path="command.variants">
                                                <!--Input implements redundant check onclick to offset onclick check implemented by parent div-->
                                                <input id="checkbox_variant_${currentVariant.id}" name="variants"
                                                       value="${currentVariant.id}" type="checkbox"
                                                    ${msgpt:contains( command.variants, currentVariant)?'checked="checked"':'' }
                                                       class="style_multiselect_binding" disabled/>
                                                <input type="hidden" name="_variants"/>
                                            </fmtSpring:bind>
                                            <span class="detailTip"
                                                  title="|<div class='detailTipText'>${currentVariant.breadCrumbStr}</div>">
												<c:out value="${currentVariant.nameWithParentInfo}"/>
											</span>
                                        </div>
                                    </c:forEach>
                                </div>
                            </div>
                            <div id="actionPopupMoveToLocalVariantsSelect" style="padding: 2px 8px 24px 8px;"
                                 align="left">
                                <label for="singleVariantSelect"
                                       style="color: rgb(68, 68, 68); display: block; padding-top: 6px; padding-bottom: 2px; text-align: left;">
                                    <fmtSpring:message code="page.label.variant"/>
                                </label>
                                <form:select id="singleVariantSelect" path="variants" cssClass="custom-select"
                                             multiple="false" disabled="true">
                                    <c:forEach var="currentVariant" items="${variants}">
                                        <form:option id="singleVariantOption_${currentVariant.id}"
                                                     value="${currentVariant.id}">
                                            <c:out value="${currentVariant.nameWithParentInfo}"/>
                                        </form:option>
                                    </c:forEach>
                                </form:select>
                            </div>
                            <div id="actionPopupMetadataSelect" style="padding: 0px 8px 12px 8px;" align="center">
                                <div class="formControl">
                                    <label><span class="labelText"><fmtSpring:message
                                            code="page.label.metadata.optional"/></span></label>
                                    <div class="controlWrapper" style="text-align: left;">
                                        <select id="metadataSelect" class="inputXL style_select">
                                            <option value="0"><fmtSpring:message code="page.label.none"/></option>
                                            <c:forEach var="metadataFormDefinition" items="${metadataFormDefinitions}">
                                                <option id="metadataFormDefinitionOption_${metadataFormDefinition.id}"
                                                        value="${metadataFormDefinition.id}">
                                                    <c:out value="${metadataFormDefinition.name}"/>
                                                </option>
                                            </c:forEach>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div id="actionPopupTaskMetadataSelect">
                                <div class="formControl">
                                    <label><span class="labelText"><fmtSpring:message
                                            code="page.label.metadata.optional"/></span></label>
                                    <div class="controlWrapper" style="text-align: left;">
                                        <select id="taskMetadataSelect" class="style_select">
                                            <option value="0"><fmtSpring:message code="page.label.none"/></option>
                                            <c:forEach var="taskMetadataFormDefinition"
                                                       items="${taskMetadataFormDefinitions}">
                                                <option id="taskMetadataFormDefinitionOption_${taskMetadataFormDefinition.id}"
                                                        value="${taskMetadataFormDefinition.id}">
                                                    <c:out value="${taskMetadataFormDefinition.name}"/>
                                                </option>
                                            </c:forEach>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div id="actionPopupTaskSelect">
                                <div class="formControl">
                                    <input type="hidden" id="currentTaskLinkedObjectId">
                                    <label><span class="labelText"><fmtSpring:message code="page.label.name"/></span></label>
                                    <span id="linkTaskToObjectName"></span>
                                </div>
                                <div class="formControl" id="taskSelectDiv">
                                    <label><span class="labelText"><fmtSpring:message code="page.label.task"/></span></label>
                                    <div class="controlWrapper">
                                        <form:select id="taskSelect" path="currentLinkedTaskId" cssClass="inputXXL"
                                                     onchange="selectTask()" onkeyup="selectTask()">
                                            <option id="0" value="0"><fmtSpring:message code="page.label.create.new.task"/></option>
                                        </form:select>
                                    </div>
                                </div>
                                <div class="formControl" id="newTaskTypeDiv">
                                    <label><span class="labelText"><fmtSpring:message code="page.label.type"/></span></label>
                                    <div class="controlWrapper">
                                        <form:select id="taskTypeSelect" path="createTaskType" cssClass="style_select"
                                                     onchange="selectTaskType()" items="${taskTypes}" itemLabel="name" itemValue="id">
                                        </form:select>
                                    </div>
                                </div>
                                <div class="formControl" id="newTaskLanguageDiv" style="display: none">
                                    <label><span class="labelText"><fmtSpring:message code="page.label.language"/></span></label>
                                    <div class="controlWrapper">
                                        <form:select id="newTasklanguageSelect" path="messagepointLocaleId" cssClass="inputXXL style_select">
                                            <c:forEach var="currentLocale" items="${messagepointLocales}">
                                                <option id="localeOption_${currentLocale.id}" value="${currentLocale.id}">
                                                    <c:out value="${currentLocale.name}"/>
                                                </option>
                                            </c:forEach>
                                        </form:select>
                                    </div>
                                </div>
                                <div class="formControl" id="taskVariantDiv">
                                    <label><span class="labelText"><fmtSpring:message code="page.label.variant"/></span></label>
                                    <div class="controlWrapper">
                                        <form:select id="taskVariantSelect" path="taskVariantId" cssClass="inputXXL">
                                            <option id="0" value="0"><fmtSpring:message code="page.label.no.selected.items"/></option>
                                        </form:select>
                                    </div>
                                </div>
                                <div id="taskDescriptionDiv">
                                </div>
                            </div>
                            <div id="actionPopupNote" state="default">
                                <br/>
                                <div class="formControl">
                                    <label><span id="descriptionLabel" class="labelText" style="display: none;"><fmtSpring:message code="page.label.description"/></span></label>
                                    <div class="controlWrapper">
                                        <msgpt:InputFilter type="comment">
                                            <form:textarea path="userNote" onkeyup="validatePopupReq();"
                                                           rows="3"
                                                           onclick="initTextarea(this)"/>
                                        </msgpt:InputFilter>
                                    </div>
                                </div>
                            </div>
                            <div id="actionPopupApprovalButtons" class="actionPopupButtonsContainer">
                                <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                <span id="approveBtn"><msgpt:Button URL="#" label="page.flow.approve"
                                                                    primary="true"/></span>
                                <span id="approveBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                   label="page.flow.approve"
                                                                                                   disabled="true"/></span>
                            </div>
                            <div id="actionPopupReleaseFromTranslationButtons" class="actionPopupButtonsContainer">
                                <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                <span id="releaseBtn">
                                        <msgpt:Button URL="#" primary="true" label="page.label.release"/>
                                    </span>
                                <span id="releaseBtnDisabled" style="display: none;">
                                        <msgpt:Button URL="#" label="page.label.release" disabled="true"/>
                                    </span>
                            </div>
                            <div id="actionPopupRejectButtons" class="actionPopupButtonsContainer">
                                <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                <span id="rejectBtn"><msgpt:Button URL="#" label="page.flow.reject"
                                                                   primary="true"/></span>
                                <span id="rejectBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                  label="page.flow.reject"
                                                                                                  disabled="true"/></span>
                            </div>
                            <div id="actionPopupAbortButtons" class="actionPopupButtonsContainer">
                                <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                <span id="abortBtn">
                                        <msgpt:Button URL="#" primary="true" label="page.label.abort"/>
                                    </span>
                                <span id="abortBtnDisabled" style="display: none;">
                                        <msgpt:Button URL="#" label="page.label.abort" disabled="true"/>
                                    </span>
                            </div>
                            <!-- Add Smart Text w/ Metadata -->
                            <div id="actionPopupMetadataButtons" class="actionPopupButtonsContainer">
                                <span id="customCancelBtnEnabled">
                                        <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                    </span>
                                <span id="customContinueBtnEnabled">
                                    <msgpt:Button URL="javascript:iFrameAction(2);"
                                                  label="page.label.continue" primary="true"/>
                                </span>
                            </div>
                            <!-- Add Task w/ Metadata -->
                            <div id="actionPopupTaskMetadataButtons" class="actionPopupButtonsContainer">
                                    <span id="taskCustomCancelBtnEnabled">
                                        <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                    </span>
                                <span id="taskCustomContinueBtnEnabled">
                                        <msgpt:Button URL="javascript:iFrameAction(30);"
                                                      label="page.label.continue" primary="true"/>
                                    </span>
                            </div>
                            <div id="actionPopupContentJSONExport" style="padding: 2px 8px 6px 8px;" align="center">
                                <label><span class="labelText"><fmtSpring:message
                                        code="page.label.target.locale"/></span></label>
                                <form:select items="${exportTargetLocales}"
                                             itemLabel="name"
                                             itemValue="id"
                                             cssClass="custom-select custom-select-lg"
                                             id="exportTargetLocaleSelect"
                                             path="exportTargetLocale"/>
                            </div>
                            <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                                <span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                  label="page.label.cancel"
                                                                                                  disabled="true"/></span>
                                <span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                          label="page.label.cancel"/></span>
                                <span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                    label="page.label.continue"
                                                                                                    disabled="true"/></span>
                                <span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue"
                                                                            primary="true"/></span>
                            </div>
                            <div id="actionPopupContinueToTaskSelectButtons" class="actionPopupButtonsContainer">
                                <span id="toLinkTaskCustomCancelBtnEnabled">
                                    <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                </span>
                                <span id="toLinkTaskCustomContinueBtnEnabled">
                                    <msgpt:Button URL="javascript:continueToLinkTask(false);" label="page.label.continue" primary="true"/>
                                </span>
                            </div>
                            <div id="actionPopupContinueTaskSelectButtons" class="actionPopupButtonsContainer">
                                <span id="linkTaskCustomCancelBtnEnabled">
                                    <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                </span>
                                <span id="linkTaskCustomContinueBtnEnabled">
                                    <msgpt:Button URL="javascript:continueToLinkTask(true);" label="page.label.continue" primary="true"/>
                                </span>
                                <span id="linkTaskCustomContinueBtnDisabled" style="display: none;">
                                    <msgpt:Button URL="#" label="page.label.continue" disabled="true"/>
                                </span>
                            </div>
                        </msgpt:Popup>
                    </form:form>
                </c:if>
                <c:if test="${noTouchpoints and not trashTpContext}">
                    <div class="InfoSysContainer_info">
                        <i class="fa icon fa-info-circle" aria-hidden="true"></i>
                        <p><fmtSpring:message code="page.text.no.touchpoints.available.to.you"/></p>
                    </div>
                </c:if>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
        <msgpt:ContextMenu name="contentObjectList">
            <msgpt:ContextMenuEntry name="actionOption_1" link="#actionSelected:1"><fmtSpring:message
                    code="page.label.create.working.copy"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actioniFrame_1" link="#iFrameAction:1"><fmtSpring:message
                    code="page.label.edit"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_19" link="#actionSelected:19"><fmtSpring:message
                    code="page.label.activate"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_6" link="#actionSelected:6"><fmtSpring:message
                    code="page.label.release.for.approval"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_17" link="#actionSelected:17"><fmtSpring:message
                    code="page.label.approve"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_29" link="#actionSelected:29"><fmtSpring:message
                    code="page.label.release.from.translation"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_18" link="#actionSelected:18"><fmtSpring:message
                    code="page.label.reject"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_20" link="#actionSelected:20"><fmtSpring:message
                    code="page.label.approve.and.override"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_21" link="#actionSelected:21"><fmtSpring:message
                    code="page.label.reject.and.override"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_40" link="#actionSelected:40"><fmtSpring:message
                    code="page.label.abort"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_45" link="#actionSelected:45"><fmtSpring:message
                    code="page.label.retry.translation"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_2" link="#actionSelected:2"><fmtSpring:message
                    code="page.label.discard.working.copy"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="separator"><li class="separator"></li></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_5" link="#actionSelected:5"><fmtSpring:message
                    code="page.label.reassign"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_41" link="#actionSelected:41"><fmtSpring:message
                    code="page.label.reassign"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_22" link="#actionSelected:22"><fmtSpring:message
                    code="page.label.clone"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_43" link="#actionSelected:43"><fmtSpring:message
                    code="page.label.content.export"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_24" link="#actionSelected:24"><fmtSpring:message
                    code="page.label.create.local"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_25" link="#actionSelected:25"><fmtSpring:message
                    code="page.label.move.to.local"/></msgpt:ContextMenuEntry>
            <c:if test="${adminObjectPermission}">
                <msgpt:ContextMenuEntry name="actioniFrame_10" link="#iFrameAction:10"><fmtSpring:message
                        code="page.label.associate.touchpoints"/></msgpt:ContextMenuEntry>
            </c:if>
            <c:if test="${adminObjectPermission}">
                <msgpt:ContextMenuEntry name="actionOption_3" link="#actionSelected:3"><fmtSpring:message
                        code="page.label.archive"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="actionOption_4" link="#actionSelected:4"><fmtSpring:message
                        code="page.label.delete.archive"/></msgpt:ContextMenuEntry>
            </c:if>
            <c:if test="${empty param.isFreeform || param.isFreeform == false}">
                <msgpt:ContextMenuEntry name="actionOption_23" link="#actionSelected:23"><fmtSpring:message
                        code="page.label.where.used"/></msgpt:ContextMenuEntry>
            </c:if>
            <msgpt:ContextMenuEntry name="separator"><li class="separator"></li></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actioniFrame_26" link="#iFrameAction:26"><fmtSpring:message
                    code="page.label.calculate.hash"/></msgpt:ContextMenuEntry>
            <c:if test="${not hasTaskMetadataFormDef}">
                <msgpt:ContextMenuEntry name="actioniFrame_30" link="#iFrameAction:30"><fmtSpring:message
                        code="page.label.add.task"/></msgpt:ContextMenuEntry>
            </c:if>
            <c:if test="${hasTaskMetadataFormDef}">
                <msgpt:ContextMenuEntry name="actionOption_31" link="#actionSelected:31"><fmtSpring:message
                        code="page.label.add.task"/></msgpt:ContextMenuEntry>
            </c:if>
        </msgpt:ContextMenu>
    </msgpt:BodyNew>
</msgpt:Html5>