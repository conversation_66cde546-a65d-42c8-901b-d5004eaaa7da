
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
	<msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">
	

		<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />
		
		<msgpt:Stylesheet href="includes/javascript/${tinymceDir}/skins/lightgray/skin.min.css" />

		<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/jquery.colorpicker.js"/>
		<link href="../includes/javascript/jQueryPlugins/colorpicker_new/jquery.colorpicker.css" rel="stylesheet" type="text/css"/>
		<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/swatches/jquery.ui.colorpicker-pantone.js"/>
		<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/parts/jquery.ui.colorpicker-memory.js"/>
		<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/parsers/jquery.ui.colorpicker-cmyk-parser.js"/>
		<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/parsers/jquery.ui.colorpicker-cmyk-percentage-parser.js"/>
		
		<msgpt:Stylesheet href="includes/themes/commoncss/tinyMCEEditorContent.css" />
		<msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js" />
		
		<msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js" />
		<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css" />
		
		<msgpt:Script src="includes/javascript/handlebars/handlebars-v4.7.8.js" />

		<msgpt:Script src="includes/javascript/jQueryPlugins/hoverIntent/jquery.hoverIntent.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.editorActions.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.varTagAttrManager.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.smartTextTagAttrManager.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/disableSelection/jquery.disable.text.select.pack.js" />
		
		<style>
			div.mce-panel .header-label {
				font-weight: bold;
				padding-bottom: 10px;
				font-size: 16px;
			}
			div.mce-panel .property-label {
				line-height: 30px;
				vertical-align: middle;
				width: 125px;
			}
			div.mce-panel .property-value {
				vertical-align: middle;
				text-align: left;
				white-space: nowrap;
			}
			div.mce-panel .ptUnits, .canvasUnits {
				font-size: 12px;
				padding-left: 3px;
			}
			
			.ui-dialog .ui-dialog-buttonpane {
			    font-size: 13px;
			}
			#tab_values.mce-active, #valuesInterfaceContainer, #tab_alttext.mce-active, #altTextInterfaceContainer, #tab_ID.mce-active, #IDInterfaceContainer, #valueEditInterfaceContainer, #conditionalEnableInterfaceContainer {
				background-color: #fff;
			}
			#layoutInterfaceContainer.mce-panel {
				background-color: #fff;
			}
			
			/* Values Interface CSS */
			.menuItemsContainer, .conditionalEnableItemsContainer {
				padding: 12px;
				overflow: auto;
			}
			.menuItemsContainer {
				height: 350px;
			}
			.conditionalEnableItemsContainer {
				height: 390px;
			}
			.menuItemTextContainer, .conditionalEnableItemTargetTextContainer, .conditionalEnableItemValueTextContainer {
				white-space: normal;
			}
			
			.menuItem, .conditionalEnableItem {
				border: 1px solid #ccc;
				background-color: #f5f5f5;
				padding: 10px;
				margin-bottom: 3px;
			}
			.itemRemoveButton, .itemEditButton, .defaultSelectionBtn {
				cursor: pointer; 
				color: #999;
			}
			.itemRemoveButton:hover, .itemEditButton:hover, .defaultSelectionBtn:hover {
				color: #555;
			}
			
			.sortPlaceholder {
				height: 35px;
				background-color: #fdfdfd;
				border: 1px dashed #ccc;
			}
		</style>

        <msgpt:Script>
            <script>
                var PX_PER_CM = 39.37007874015748;

                var ed 			= window.parent.tinymce.activeEditor;
                var settings 	= ed.settings;
                var unitScale 	= ed.settings.mp_fn.get_units() == 'cm' ? PX_PER_CM : 100;

                var isMessageContext = ed.settings.is_global_context === false;

                var altTextEditorData = {
                	content_css						: ed.settings.content_css,
                    async_variables 				: { type : isMessageContext ? 'message' : 'embedded_content' },
					async_embedded_content 			: { type : isMessageContext ? 'message' : 'embedded_content', snippets_only: true },
					async_local_embedded_content 	: { type : 'message', enable : isMessageContext },
					markers_list					: ed.settings.insertvariable_markerlist,
                    applies_text_styles				: false,
                    applies_paragraph_styles		: false,
                    applies_templates				: false,
					is_global_context				: ed.settings.is_global_context,
					content_object_id				: ed.settings.content_object_id,
                    content_menu					: {
									                        applied: ed.settings.content_menu.applied,
									                        mode: ed.settings.content_menu.mode
									                    },
                    can_edit_source					: ${sourceEditPerm}
                };

                var valueEditorData = {
                		content_css					: ed.settings.content_css,
                     async_variables 				: { type : isMessageContext ? 'message' : 'embedded_content' },
 					 async_embedded_content 		: { type : isMessageContext ? 'message' : 'embedded_content' },
 					 async_local_embedded_content 	: { type : 'message', enable : isMessageContext },
 					 markers_list					: ed.settings.insertvariable_markerlist,
                     applies_text_styles			: false,
                     applies_paragraph_styles		: false,
                     applies_templates				: false,
                     canvas_dimensions				: '5.5:1.5',
                     unrestricted_frame_width		: true,
					is_global_context				: ed.settings.is_global_context,
                     content_object_id				: ed.settings.content_object_id,
                     content_menu					: {
						                        applied: ed.settings.content_menu.applied,
						                        mode: ed.settings.content_menu.mode
						                    },
                     can_edit_source				: ${sourceEditPerm},
                     snippet_mode					: true
                 };

                // MENU VALUE FUNCTIONS
                function initMenuItem(item) {
                    $(item).find('.itemEditButton').click( function() {
                        $('#valuesInterfaceContainer,#addValueButton,#applyPropertiesButton,#cancelButton').hide();
                        $('#valueEditInterfaceContainer,#setValueButton,#menuItemBackButton').show();

                        // INIT: TinyMCE
                        if ( !( $('#valueEditInterfaceContainer').find('.mce-tinymce').length > 0
                            && $('#valueEditInterfaceContainer').find('.mce-tinymce').is(':visible') ) ) {
                        	tinyMCEvalueEditorInit("menuValueEditor", "658", "352", valueEditorData);
                        }

                		function pollForEditorLoaded(menuItem) {
                			if ( $('#valueEditInterfaceContainer').find('.mce-tinymce').length > 0
                				 && $('#valueEditInterfaceContainer').find('.mce-tinymce').is(':visible') ) {

                                $('#targetMenuItemIndex').val( parseId(menuItem) );
                                tinymce.get('editor_menuValueEditor').setContent( $(menuItem).find('.menuItemInput').val() );
                				
                			} else {
                				window.setTimeout( function(){ pollForEditorLoaded(menuItem); }, 100);
                			}
                		}
                		pollForEditorLoaded( $(this).closest('.menuItem') );

                    });
                    $(item).find('.itemRemoveButton').click( function() {
                        $(this).closest('.menuItem').remove();
                        toggleInfoDisplay();
                    });
                    $(item).find('.defaultSelectionBtn').click( function() {
						var isCurrentlyDefaultValue = $(this).attr('default_value') == 'true';
						// Menu: Enforce single selection
                        if ( $('#menuTypeSelect').val() == 'combo')
                            $('#menuItemsListContainer').find('.defaultSelectionBtn')
                            	.attr('default_value','false').removeClass('fa-check-square').addClass('fa-square');
                        if ( isCurrentlyDefaultValue )
                        	$(this).removeClass('fa-check-square').addClass('fa-square').attr('default_value','false');
                        else
                        	$(this).removeClass('fa-square').addClass('fa-check-square').attr('default_value','true');
                    });
                }

                function generateMenuItem(targetContent, menuItemIndex, isSelected) {
                    var zMenuItemTemplate = _zMenuItemTemplate || (_zMenuItemTemplate = Handlebars.compile($('#menuItemTemplate').html()));
                    var indData = {
                        index		: menuItemIndex,
                        content		: $('<div>').append(targetContent).html()
                    };

                    var newMenuItem = $(zMenuItemTemplate(indData))[0];

                    if ( isSelected && isSelected == 'true' ) {
						newMenuItem = $(newMenuItem);
						$(newMenuItem).find('.defaultSelectionBtn').removeClass('fa-square').addClass('fa-check-square').attr('default_value','true');
                    }
                    
                    $('.menuItemsListContainer').append( newMenuItem );

                    initMenuItem( $('.menuItemsListContainer').find("[id^='menuItem_"+menuItemIndex+"']") );
                }
                // END MENU VALUE FUNCTIONS

				// CONDITIONAL ENABLE FUNCTIOLNS
				function initConditionalEnableItem(item) {
					$(item).find('.itemEditButton').click( function() {
						$('#conditionalEnableInterfaceContainer,#addConditionButton,#applyPropertiesButton,#cancelButton').hide();
						$('#conditionalEnableEditInterfaceContainer,#setConditionButton,#conditionItemBackButton').show();

						// INIT: TinyMCE
						if ( !( $('#conditionalEnableInterfaceContainer').find('.mce-tinymce').length > 0
								&& $('#conditionalEnableInterfaceContainer').find('.mce-tinymce').is(':visible') ) ) {
							tinyMCEvalueEditorInit("conditionalEnableTargetEditor", "658", "100", valueEditorData);
							tinyMCEvalueEditorInit("conditionalEnableValueEditor", "658", "100", valueEditorData);
						}
						$('#conditionalEnableOperatorSelect').val( $(item).find('.conditionalEnableOperatorItemInput').val() );

						function pollForEditorLoaded(conditionItem) {
							if ( $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').length > 0
									&& $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').is(':visible') ) {

								$('#targetConditionalEnableItemIndex').val( parseId(item) );
								tinymce.get('editor_conditionalEnableTargetEditor').setContent( $(conditionItem).find('.conditionalEnableTargetItemInput').val() );
								tinymce.get('editor_conditionalEnableValueEditor').setContent( $(conditionItem).find('.conditionalEnableValueItemInput').val() );

							} else {
								window.setTimeout( function(){ pollForEditorLoaded(conditionItem); }, 100);
							}
						}
						pollForEditorLoaded( $(this).closest('.conditionalEnableItem') );

					});
					$(item).find('.itemRemoveButton').click( function() {
						$(this).closest('.conditionalEnableItem').remove();
						toggleInfoDisplay();
					});
				}

				function generateConditionalEnableItem(valueContent, targetContent, op, itemIndex) {
					var zConditionalEnableItemTemplate = _zConditionalEnableItemTemplate || (_zConditionalEnableItemTemplate = Handlebars.compile($('#conditionalEnableItemTemplate').html()));
					var indData = {
						index			: itemIndex,
						value_content	: $('<div>').append(valueContent).html(),
						target_content	: $('<div>').append(targetContent).html(),
						operator		: op
					};

					var newItem = $(zConditionalEnableItemTemplate(indData))[0];

					$('.conditionalEnableItemsListContainer').append( newItem );

					initConditionalEnableItem( $('.conditionalEnableItemsListContainer').find("[id^='conditionalEnableItem_"+itemIndex+"']") );
				}
				// END CONDITIONAL ENABLE FUNCTIONS

                function initTabs() {
                    $("#elementPropertyTabs [id^='tab_']").each( function() {

                        $(this).click( function() {
                            if ( !$(this).is('.mce-active') ) {
                                $('#elementPropertyTabs .mce-tab').removeClass('mce-active');
                                $(this).addClass('mce-active');

                                $('#layoutInterfaceContainer, #valuesInterfaceContainer, #altTextInterfaceContainer, #IDInterfaceContainer, #conditionalEnableInterfaceContainer, #addConditionButton, #addValueButton, #setValueButton, #setConditionButton, #menuItemBackButton, #conditionItemBackButton').hide();
                                if ( $(this).attr('id').indexOf('layout') != -1 ) {
                                    $('#layoutInterfaceContainer').show();
                                } else if ( $(this).attr('id').indexOf('value') != -1 ) {
                                    $('#valuesInterfaceContainer,#addValueButton').show();
                                } else if ( $(this).attr('id').indexOf('alttext') != -1 ) {
                                    $('#altTextInterfaceContainer').show();

                                    // INIT: TinyMCE
                                    if ( !( $('#altTextInterfaceContainer').find('.mce-tinymce').length > 0
                                        && $('#altTextInterfaceContainer').find('.mce-tinymce').is(':visible') ) ) {
                                        tinyMCEvalueEditorInit("menuAltTextEditor", "658", "352", altTextEditorData);
                                    }
                                } else if ( $(this).attr('id').indexOf('ID') != -1 ) {
                                    $('#IDInterfaceContainer').show();

                                    // INIT: TinyMCE
                                    if ( !( $('#IDInterfaceContainer').find('.mce-tinymce').length > 0
                                        && $('#IDInterfaceContainer').find('.mce-tinymce').is(':visible') ) ) {
                                        tinyMCEvalueEditorInit("menuIDEditor", "658", "352", valueEditorData);
                                    }
                                } else if ( $(this).attr('id').indexOf('conditionalEnable') != -1 ) {
									$('#conditionalEnableInterfaceContainer,#addConditionButton').show();
								}
                            }
                        });

                    });

                }

                function toggleBorderColorContainer() {
                    if ( $('#borderWidthInput').val().length > 0 && $('#borderWidthInput').val() != 0 )
                        $('#borderColorContainer').showEle('normal')
                    else
                        $('#borderColorContainer').hide();
                }

                function toggleBorderType() {
                    if ( $('#borderTypeSelect').val() == "none" ) {
                        $('#borderWidthContainer,#borderColorContainer').hide();
                    } else if ( $('#borderTypeSelect').val() == "box" || $('#borderTypeSelect').val() == "bottom" ) {
                        $('#borderWidthContainer,#borderColorContainer').showEle('normal');
                    }
                }

                function toggleFieldWidthContainers() {
					if ( $('#fitToWidthToggle').is(':checked') )
						$('#fixedFieldWidthContainer').hide();
					else
						$('#fixedFieldWidthContainer').show();
                }

                function toggleLabelWidthContainers() {
					if ( $('#minimizeWidthToggle').is(':checked') )
						$('#fixedLabelWidthContainer').hide();
					else
						$('#fixedLabelWidthContainer').show();
                }

                function toggleDefaultValueType() {
                    var type = $('#defaultValueTypeSelect').val();
					if ( type == '0' || type == '2' ) {
						$('#menuItemsListContainer').find('.defaultSelectionBtn')
							.removeAttr('default_value').removeClass('fa-check-square').addClass('fa-square').hide();
					} else if ( type == '1') {
						$('#menuItemsListContainer').find('.defaultSelectionBtn').showEle('normal');
					}
					
					$('#defaultSelectionVariableContainer').hide();
					if ( type == '2' ) {
						$('#defaultSelectionVariableContainer').show();
					}
                }

                function toggleInfoDisplay() {
                    if ( $('.menuItemsListContainer .menuItem').length != 0 )
                        $('.menuItemsContainer .addItemInfoContainer').hide();
                    else
                        $('.menuItemsContainer .addItemInfoContainer').showEle('normal');

					if ( $('.conditionalEnableItemsListContainer .conditionalEnableItem').length != 0 )
						$('.conditionalEnableItemsContainer .addItemInfoContainer').hide();
					else
						$('.conditionalEnableItemsContainer .addItemInfoContainer').showEle('normal');
                }

                function toggleLabelType() {
                    if ( $('#labelTypeSelect').val() == "none" )
                        $('.labelPropertyContainer').hide();
                    else
                        $('.labelPropertyContainer').showEle('normal');
                }

                function toggleType() {
                    if ( $('#menuTypeSelect').val() == 'list' ) {
                        $('#listBoxHeightContainer').showEle('normal');
                    } else {
                        $('#listBoxHeightContainer').hide();
                        // Enforce single select for menus
                        $("[default_value='true']:not(:first)").attr('default_value','false').removeClass('fa-check-square').addClass('fa-square');
                    }
				}

                function validateTabOrder() {
                    if ( $('#tabOrderInput').val().length == 0 )
                        return;

                    var tabOrderArray = new Array();
                    $(window.parent.tinymce.activeEditor.getBody()).find('.mceFormInput').each( function() {
                        if ( $(this).attr('tab_order') != undefined )
                            tabOrderArray[tabOrderArray.length] = $(this).attr('tab_order');
                    });

                    if ( tabOrderArray.indexOf( $('#tabOrderInput').val() ) != -1 )
                        $('#tabOrderWarningContainer').showEle('normal');
                    else
                        $('#tabOrderWarningContainer').hide();
                }

                // INIT
                var _zMenuItemTemplate = null;
				var _zConditionalEnableItemTemplate = null;
                $( function() {

                    if ( getTopFrame().MAIN_DEV_MODE() )
                        $(document).find('body')
                            .append("<div style=\"padding: 5px; position: absolute; top: "+($(document).find('body').height()-45)+"px; left: 0px;\">" +
                                "<input id=\"devModeBtn\" type=\"button\" value=\"DEV MODE: RELOAD FRAME\" style=\"width: 150px;\" title=\"Toggle 'devMode' in iFramePopup init\" onclick=\"javascript:window.location.reload()\" />" +
                                "</div>");

                    popupFactoryRemove('tinymceMenuInit');

                    ed.settings.mp_fn.position_popup();

                    // INIT TEXT STYLES
                    if ( settings.style_formats != null && settings.style_formats != undefined && settings.style_formats.length != 0 ) {
                        var textStyleOptions = "<option value=\"none\">" + client_messages.content_editor.none + "</option>";
                        for (var i=0; i < settings.style_formats.length; i++)
                            textStyleOptions += "<option value=\"" + settings.style_formats[i].title + "\">" + settings.style_formats[i].title + "</option>";
                        $('#textStyleSelect').html( textStyleOptions );
                    } else {
                        $('#textStylesContainer').remove();
                    }

                    // INIT PARAGRAPH STYLES
                    if ( settings.paragraph_style_data != null && settings.paragraph_style_data != undefined && settings.paragraph_style_data.length != 0 ) {
                        var paragraphStyleOptions = "<option value=\"none\">" + client_messages.content_editor.none + "</option>";
                        for (var i=0; i < settings.paragraph_style_data.length; i++) {
                            var currentStyle = mpParseJSON(settings.paragraph_style_data[i]);
                            paragraphStyleOptions += "<option value=\"" + currentStyle.name + "\">" + currentStyle.name + "</option>";
                        }
                        $('#paragraphStyleSelect').html( paragraphStyleOptions );
                    } else {
                        $('#paragraphStylesContainer').remove();
                    }

                    var targetEle = $(ed.getBody()).find('.mceContentSelected.mceMenuElement');
					if ( $(ed.getBody()).find('.mceInlineSelected.mceMenuElement').length != 0 )
						targetEle = $(ed.getBody()).find('.mceInlineSelected');
                    if ( targetEle.length == 0 )
                        targetEle = $(ed.selection.getStart()).closest('.mceMenuElement');

                    if ( targetEle.length != 0 ) {

                        // INIT LABEL ATTRIBUTES
                        var labelEle = $(targetEle).find('.mceFormInputLabel');
                        $('#labelTypeSelect').val( labelEle.length != 0 ? $(labelEle).attr('type') : 'none' );

                        if ( labelEle.length != 0 ) {
                            var firstParagraph = $(labelEle).find('p:first');
                            if ( $(firstParagraph).length != 0 && $(firstParagraph).attr('paragraphclass') != undefined )
                                $('#paragraphStyleSelect').val( $(firstParagraph).attr('paragraphclass').replace(settings.paragraph_class_prefix, '') );

                            var firstSpan = $(labelEle).find('p span:first-child');
                            if ( $(firstSpan).length != 0 && $(firstSpan).attr('class') != undefined )
                                $('#textStyleSelect').val( $(firstSpan).attr('class') );

                            if ( $(labelEle).attr('minimize_width') != undefined && $(labelEle).attr('minimize_width') == "true" )
                                $('#minimizeWidthToggle').attr('checked','checked');
                            else
                            	$('#minimizeWidthToggle').removeAttr('checked');
                            
                            var width = Math.round( ($(labelEle).outerWidth() / unitScale) * 100 ) / 100;
                            $('#labelWidthInput').val( width );
                        }

                        // INIT INPUT ATTRIBUTES
                        var inputEle = $(targetEle).find('.mceMenuInput');

                        if ( $(targetEle).find('.mceFormEleAltText').length != 0 ) {
                            var value = $(targetEle).find('.mceFormEleAltText').html();
                            $('#editor_menuAltTextEditor').val(value);
                        }
                        if ( $(targetEle).find('.mceFormEleID').length != 0 ) {
                            var value = $(targetEle).find('.mceFormEleID').html();
                            $('#editor_menuIDEditor').val(value);
                        }

                        $('#menuTypeSelect').val( $(inputEle).attr('type') );

                        var width = Math.round( ($(inputEle).outerWidth() / unitScale) * 100 ) / 100;
                        $('#fieldWidthInput').val( width );

                        if ( $(inputEle).attr('type') == 'list' ) {
	                        var height = Math.round( ($(inputEle).height() / unitScale) * 100 ) / 100;
	                        $('#listBoxHeightInput').val( height );
                        }

                        if ( $(inputEle).attr('font_size') != undefined )
                            $('#fontSizeInput').val( $(inputEle).attr('font_size') );

                        if ( $(inputEle).attr('border_width') != undefined )
                            $('#borderWidthInput').val( $(inputEle).attr('border_width') );

                        if ( $(inputEle).attr('border_color') != undefined )
                            $('#borderColorInput').val( $(inputEle).attr('border_color') );

                        if ( $(inputEle).attr('border_type') != undefined )
                            $('#borderTypeSelect').val( $(inputEle).attr('border_type') );

                        if ( $(inputEle).attr('tab_order') != undefined )
                            $('#tabOrderInput').val( $(inputEle).attr('tab_order') );

                        // INIT VALUES
                        var menuItemList = $(targetEle).find('.mceMenuItems');
                        $(menuItemList).find('.mceMenuItem').each( function() {
                            var menuItemIndex = $('.menuItemsListContainer .menuItem').length;
                            generateMenuItem( $(this).html(), menuItemIndex, $(this).attr('is_selected') );
                        });

						// INIT CONDITIONAL ENABLE ITEMS
						var conditionalEnableItemList = $(targetEle).find('.mceConditionalEnableItems');
						$(conditionalEnableItemList).find('.mceConditionalEnableItem').each( function() {
							var itemIndex = $('.conditionalEnableItemsContainer .conditionalEnableItem').length;
							generateConditionalEnableItem( $(this).find('.mceConditionalEnableValue').html(), $(this).find('.mceConditionalEnableTarget').html(), $(this).attr('operator'), itemIndex );
						});

                        if ( $(menuItemList).is('[selected_variable_id]') ) {
                            $('#defaultSelectionVariableSelect').append("<option value\"" + $(menuItemList).attr('selected_variable_id') + "\">Loading</option>");
                            $('#defaultValueTypeSelect').val('2');
                       	} else if ( $(targetEle).find(".mceMenuItems [is_selected='true']").length > 0 ) {
                       		$('#defaultValueTypeSelect').val('1');
                        }

                        if ( $(inputEle).attr('fit_to_width') != undefined && $(inputEle).attr('fit_to_width') == "true" )
                            $('#fitToWidthToggle').attr('checked','checked');
                        else
                        	$('#fitToWidthToggle').removeAttr('checked');

                    }

                    if ( $('#tabOrderInput').val() == "" ) {
                        var tabOrder = 0;
                        $(ed.getBody()).find('.mceFormInput').each( function() {
                            if ( $(this).attr('tab_order') != undefined && parseInt($(this).attr('tab_order')) > tabOrder )
                                tabOrder = parseInt($(this).attr('tab_order'));
                        });
                        $('#tabOrderInput').val( tabOrder + 1 );
                    }

                    if ( $('#labelWidthInput').val() == "" )
                        $('#labelWidthInput').val(settings.mp_fn.get_units() == 'cm' ? 2 : 1);
                    if ( $('#fieldWidthInput').val() == "" )
                        $('#fieldWidthInput').val(settings.mp_fn.get_units() == 'cm' ? 4 : 2);
                    if ( $('#listBoxHeightInput').val() == "" )
                        $('#listBoxHeightInput').val(settings.mp_fn.get_units() == 'cm' ? 4 : 2);

                    $('.style_select:not(#defaultSelectionVariableSelect)').styleActionElement({maxItemDisplay: 10});
                    $('#defaultSelectionVariableSelect').each(function () {
                    	var currentSelect = $(this);
						$(currentSelect)
	        				.styleActionElement({
	        					maxItemsInList	: 30,
	                            maxItemDisplay	: 5,
	        					isAsync			: true,
	        					getItemsAsync 	: {
	        						loadOnDemand		: false,
	        						getItemsURL			: context+"/getVariables.form",
	        						fnExtServerParams	: function () {
	        													var stampDate = new Date();
	        													var currentVariableId = $(currentSelect).find('option:selected').val();
	        													
	        													var obj = 	[
	        																	{"name" : "type", "value" : "variablesList"},
	        																	{"name" : "selectedVariableId", "value" : (currentVariableId && currentVariableId != "" && currentVariableId != null ? currentVariableId : -1) }
	        																];
	        													return obj;
	        											  }
	        					  	}
	        				});
                    });

                    // UPDATE TEXT FIELD: Copy text field properties to target text field or create new
                    $('#applyPropertiesButton').click( function() {

                        var params = {};

                        params.label_type 			= $('#labelTypeSelect').val();
                        params.text_style 			= $('#textStyleSelect').length != 0 && $('#textStyleSelect').val() != 0 && $('#textStyleSelect').val() != 'none' ? $('#textStyleSelect').val() : null;
                        params.paragraph_style 		= $('#paragraphStyleSelect').length != 0 && $('#paragraphStyleSelect').val() != 'none' ? $('#paragraphStyleSelect').val() : null;
                        params.label_width 			= $('#labelWidthInput').val().length != 0 ? $('#labelWidthInput').val() * unitScale : null;
                        params.field_type			= $('#menuTypeSelect').val();
                        params.field_height			= $('#listBoxHeightInput').val().length != 0 ? $('#listBoxHeightInput').val() * unitScale : null;
                        params.field_width			= $('#fieldWidthInput').val().length != 0 ? $('#fieldWidthInput').val() * unitScale : null;
                        params.font_size			= $('#fontSizeInput').val().length != 0 ? $('#fontSizeInput').val() : 12;
                        params.border_type			= $('#borderTypeSelect').val();
                        params.border_width			= $('#borderWidthInput').val().length != 0 ? $('#borderWidthInput').val() : null;
                        params.border_color			= $('#borderColorInput').val();
                        params.tab_order			= $('#tabOrderInput').val().length != 0 ? $('#tabOrderInput').val() : null;
                        params.alt_text				= $('#editor_menuAltTextEditor').val();
                        params.input_id				= $('#editor_menuIDEditor').val();
                        params.selected_variable_id	= ( $('#defaultValueTypeSelect').val() == '2' && $('#defaultSelectionVariableSelect').val() != '0' ) ? $('#defaultSelectionVariableSelect').val() : null;
                        params.fit_to_width			= $('#fitToWidthToggle').length != 0 && $('#fitToWidthToggle').is(':checked');
                        params.minimize_label		= $('#minimizeWidthToggle').length != 0 && $('#minimizeWidthToggle').is(':checked');
                        
                        var valuesArray = new Array();
                        $('.menuItemsListContainer .menuItem').each( function() {
                        	valuesArray[valuesArray.length] = 
                            	{
                                	'value' 	: $(this).find('.menuItemInput').val(),
                                	'selected' 	: ( $('#defaultValueTypeSelect').val() == '1' ? ($(this).find('.defaultSelectionBtn').attr('default_value') == 'true') : false )
                        		}
                        });
                        params.values			= valuesArray;

						var conditionsArray = new Array();
						$('.conditionalEnableItemsContainer .conditionalEnableItem').each( function() {
							conditionsArray[conditionsArray.length] =
									{
										'target' 	: $(this).find('.conditionalEnableTargetItemInput').val(),
										'value'		: $(this).find('.conditionalEnableValueItemInput').val(),
										'operator'	: $(this).find('.conditionalEnableOperatorItemInput').val()
									}
						});
						params.conditional_enable_items = conditionsArray;

                        ed.execCommand("mceUpdateMenuProperties", false, params);

                        ed.windowManager.close();

                    });

                    toggleLabelType();
                    toggleType();
                    toggleBorderColorContainer();
                    toggleBorderType();
                    toggleDefaultValueType();
                    toggleFieldWidthContainers();
                    toggleLabelWidthContainers();
					toggleInfoDisplay();
                    validateTabOrder();

                    $('#cancelButton').click( function() {
                        ed.windowManager.close();
                    });

                    $('.canvasUnits').html(settings.mp_fn.get_units());

                    $('#borderColorInput').colorpicker({
                        showOn			: 'both',
                        buttonImageOnly	: true,
                        buttonImage		: '../includes/javascript/jQueryPlugins/colorpicker_new/images/ui-colorpicker.png',
                        modal			: true,
                        parts			: ['map', 'bar', 'hex', 'hsv', 'lab', 'rgb', 'cmyk', 'preview', 'swatches', 'memory', 'footer'],
                        buttonColorize	: true,
                        colorFormat		: 'RGB',
                        showNoneButton	: true,
                        color			: '#000000',
                        open			: function(formatted, colorPicker) {
                            $(colorPicker.colorPicker.dialog).css({'z-index': 3});
                            $('.ui-widget-overlay').css({'z-index': 2});
                        }
                    });

                    // VALUES INTERFACE INIT
                    $('#addValueButton').click( function() {

                        $('#valuesInterfaceContainer,#addValueButton,#applyPropertiesButton,#cancelButton').hide();
                        $('#valueEditInterfaceContainer,#setValueButton,#menuItemBackButton').show();
                        
                        // INIT: TinyMCE
                        if ( !( $('#valueEditInterfaceContainer').find('.mce-tinymce').length > 0
                            && $('#valueEditInterfaceContainer').find('.mce-tinymce').is(':visible') ) ) {
                        	tinyMCEvalueEditorInit("menuValueEditor", "658", "352", valueEditorData);
                        }

                		function pollForEditorLoaded() {
                			if ( $('#valueEditInterfaceContainer').find('.mce-tinymce').length > 0
                				 && $('#valueEditInterfaceContainer').find('.mce-tinymce').is(':visible') &&
                				 tinymce.get('editor_menuValueEditor') && tinymce.get('editor_menuValueEditor').settings &&
                				 tinymce.get('editor_menuValueEditor').settings.utils ) {
            					
                                $('#targetMenuItemIndex').val('-1');
                                var ciEd = tinymce.get('editor_menuValueEditor');
                                ciEd.setContent("<p></p>");
                                ciEd.settings.utils.focus();
                                ciEd.selection.setCursorLocation($(ciEd.getBody()).find('p:first').get(0), 0);
                				
                			} else {
                				window.setTimeout( function(){ pollForEditorLoaded(); }, 100);
                			}
                		}
                		pollForEditorLoaded();

                    });
                    
                    $('#setValueButton').click( function() {
                        var targetContent = $('#editor_menuValueEditor').val();
                        targetContent = targetContent.replace(/<p>/g,'').replace(/<\/p>/g,'');

                        var menuItemIndex = $('#targetMenuItemIndex').val() == -1 ? $('.menuItemsListContainer .menuItem').length : $('#targetMenuItemIndex').val();
                        if ( $('#targetMenuItemIndex').val() == -1 ) {

                            generateMenuItem(targetContent, menuItemIndex, false);

                        } else {

                            $('#menuItem_'+menuItemIndex).find('.menuItemInput').val(targetContent);
                            $('#menuItem_'+menuItemIndex).find('.menuItemTextContainer').html(targetContent);

                        }

                        $('#valueEditInterfaceContainer,#setValueButton,#menuItemBackButton').hide();
                        $('#valuesInterfaceContainer,#addValueButton,#applyPropertiesButton,#cancelButton').show();
                        toggleInfoDisplay();
                    });
                    
                    $('#menuItemBackButton').click( function() {
                    	$('#valueEditInterfaceContainer,#setValueButton,#menuItemBackButton').hide();
                        $('#valuesInterfaceContainer,#addValueButton,#applyPropertiesButton,#cancelButton').show();
                    });
                    // END VALUES INTERFACE INIT

					// CONDITIONAL ENABLE INTERFACE INIT
					$('#addConditionButton').click( function() {

						$('#conditionalEnableInterfaceContainer,#addConditionButton,#applyPropertiesButton,#cancelButton').hide();
						$('#conditionalEnableEditInterfaceContainer,#setConditionButton,#conditionItemBackButton').show();

						// INIT: TinyMCE
						if ( !( $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').length > 0
								&& $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').is(':visible') ) ) {
							tinyMCEvalueEditorInit("conditionalEnableTargetEditor", "658", "100", valueEditorData);
							tinyMCEvalueEditorInit("conditionalEnableValueEditor", "658", "100", valueEditorData);
						}
						$('#conditionalEnableOperatorSelect').val('disable');

						function pollForEditorLoaded() {
							if ( $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').length > 0
									&& $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').is(':visible') &&
									tinymce.get('editor_conditionalEnableTargetEditor') && tinymce.get('editor_conditionalEnableTargetEditor').settings &&
									tinymce.get('editor_conditionalEnableTargetEditor').settings.utils ) {

								$('#targetConditionalEnableItemIndex').val('-1');
								var ciEd = tinymce.get('editor_conditionalEnableTargetEditor');
								ciEd.setContent("<p></p>");
							} else {
								window.setTimeout( function(){ pollForEditorLoaded(); }, 100);
							}

							if ( $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').length > 0
									&& $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').is(':visible') &&
									tinymce.get('editor_conditionalEnableValueEditor') && tinymce.get('editor_conditionalEnableValueEditor').settings &&
									tinymce.get('editor_conditionalEnableValueEditor').settings.utils ) {

								$('#targetConditionalEnableItemIndex').val('-1');
								var ciEd = tinymce.get('editor_conditionalEnableValueEditor');
								ciEd.setContent("<p></p>");
								ciEd.settings.utils.focus();
								ciEd.selection.setCursorLocation($(ciEd.getBody()).find('p:first').get(0), 0);

							} else {
								window.setTimeout( function(){ pollForEditorLoaded(); }, 100);
							}
						}
						pollForEditorLoaded();

					});

					$('#setConditionButton').click( function() {

						function fmtCondition(conditionStr) {
							var returnCondition = $("<div>" + conditionStr + "</div>");
							$(returnCondition).find('*').each( function() {
								if ( $(this).closest('.staticContentItem').length == 0 )
									$(this).contents().unwrap();
							});
							return $(returnCondition).html();
						}

						var targetContent = fmtCondition( $('#editor_conditionalEnableTargetEditor').val() );
						var valueContent = fmtCondition( $('#editor_conditionalEnableValueEditor').val() );
						var operator = $('#conditionalEnableOperatorSelect').val();

						var conditionItemIndex = $('#targetConditionalEnableItemIndex').val() == -1 ? $('.conditionalEnableItemsListContainer .conditionEnableItem').length : $('#targetConditionalEnableItemIndex').val();

						if ( $('#targetConditionalEnableItemIndex').val() == -1 ) {

							generateConditionalEnableItem(valueContent, targetContent, operator, conditionItemIndex);

						} else {

							$('#conditionalEnableItem_'+conditionItemIndex).find('.conditionalEnableTargetItemInput').val(targetContent);
							$('#conditionalEnableItem_'+conditionItemIndex).find('.conditionalEnableItemTargetTextContainer').html(targetContent);

							$('#conditionalEnableItem_'+conditionItemIndex).find('.conditionalEnableOperatorItemInput').val(operator);
							$('#conditionalEnableItem_'+conditionItemIndex).find('.conditionalEnableItemOperatorTextContainer').html(operator);

							$('#conditionalEnableItem_'+conditionItemIndex).find('.conditionalEnableValueItemInput').val(valueContent);
							$('#conditionalEnableItem_'+conditionItemIndex).find('.conditionalEnableItemValueTextContainer').html(valueContent);

						}

						$('#conditionalEnableEditInterfaceContainer,#setConditionButton,#conditionItemBackButton').hide();
						$('#conditionalEnableInterfaceContainer,#addConditionButton,#applyPropertiesButton,#cancelButton').show();
						toggleInfoDisplay();
					});

					$('#conditionItemBackButton').click( function() {
						$('#conditionalEnableEditInterfaceContainer,#setConditionButton,#conditionItemBackButton').hide();
						$('#conditionalEnableInterfaceContainer,#addConditionButton,#applyPropertiesButton,#cancelButton').show();
					});
					// END CONDITIONAL ENABLE INTERFACE INIT


					// Sort: Initialize container layer sorting
                    $('.sortableList').sortable({
                        cancel		: ".disableSorting",
                        placeholder	: "sortPlaceholder"
                    });

                    $('#menuItemsListContainer').styleManager({
                        text_data		: ${!empty textStyleData ? textStyleData : "null"},
                        paragraph_data	: null
                    });

                    toggleInfoDisplay();
                    initTabs();

                });

            </script>
        </msgpt:Script>

	</msgpt:HeaderNew>

	<msgpt:BodyNew cssStyle="border-radius: 0 0 6px 6px; overflow-y: hidden; font-size: 10px;">
	
		<script id="menuItemTemplate" type="text/x-handlebars-template">
		
			<div class="menuItem" id="menuItem_{{index}}">
				<table width="100%" cellspacing="0" cellpadding="0" border="0">
					<tr>
						<td style="padding: 4px 12px; vertical-align: middle;" align="left">
							<div id="menuItemTextContainer_{{index}}" class="menuItemTextContainer">{{{content}}}</div>
							<input type="hidden" id="menuItemInput_{{index}}" class="menuItemInput" value="{{content}}" />
						</td>
						<td width="1%" style="padding: 0px 10px; vertical-align: middle; white-space: nowrap; border-left: 1px solid #bbb;">
							<i class="fa fa-times itemRemoveButton" style="font-size: 16px; margin-right: 15px;"></i>
							<i class="fa fa-pencil itemEditButton" style="font-size: 16px;"></i>
							<i class="far fa-square defaultSelectionBtn" style="font-size: 16px; margin-left: 15px; min-width: 18px; display: none;"></i>
						</td>
					</tr>
				</table>
			</div>
		
		</script>

		<script id="conditionalEnableItemTemplate" type="text/x-handlebars-template">

			<div class="conditionalEnableItem" id="conditionalEnableItem_{{index}}">
				<table width="100%" cellspacing="0" cellpadding="0" border="0">
					<tr>
						<td style="padding: 4px 12px; vertical-align: middle;" align="left">
							<div>
								<span style="font-size: 12px;"><fmtSpring:message code="page.text.when.input.value.is"/></span>
							</div>
							<div id="conditionalEnableItemValueTextContainer_{{index}}" style="font-size: 14px;" class="conditionalEnableItemValueTextContainer">{{{value_content}}}</div>
							<input type="hidden" id="conditionalEnableValueItemInput_{{index}}" class="conditionalEnableValueItemInput" value="{{value_content}}" />

							<div style="display: inline-block; vertical-align: middle;">
								<span id="conditionalEnableItemOperatorTextContainer_{{index}}" style="font-size: 12px; text-transform: capitalize; font-style: italic;" class="conditionalEnableItemOperatorTextContainer">{{{operator}}}</span>
								<span style="font-size: 12px;"><fmtSpring:message code="page.text.the.following.named.inputs"/></span>
							</div>
							<input type="hidden" id="conditionalEnableOperatorItemInput_{{index}}" class="conditionalEnableOperatorItemInput" value="{{operator}}" />

							<div id="conditionalEnableItemTargetTextContainer_{{index}}" style="font-size: 14px;" class="conditionalEnableItemTargetTextContainer">{{{target_content}}}</div>
							<input type="hidden" id="conditionalEnableTargetItemInput_{{index}}" class="conditionalEnableTargetItemInput" value="{{target_content}}" />

						</td>
						<td width="1%" style="padding: 0px 10px; vertical-align: middle; white-space: nowrap; border-left: 1px solid #bbb;">
							<i class="fa fa-times itemRemoveButton" style="font-size: 16px; margin-right: 15px;"></i>
							<i class="fa fa-pencil itemEditButton" style="font-size: 16px;"></i>
						</td>
					</tr>
				</table>
			</div>

		</script>
	
		<div style="height: 446px;">
		
			<!-- TABS -->
			<div id="elementPropertyTabs" class="mce-container">
				<div class="mce-tabs" >
					<div id="tab_layout" class="mce-tab mce-active" unselectable="on"><fmtSpring:message code="page.label.layout"/></div><div id="tab_values" class="mce-tab" unselectable="on"><fmtSpring:message code="page.label.values"/></div><div id="tab_alttext" class="mce-tab" unselectable="on"><fmtSpring:message code="page.label.alt.text"/></div><div id="tab_ID" class="mce-tab" unselectable="on"><fmtSpring:message code="page.label.name"/></div><div id="tab_conditionalEnable" class="mce-tab" unselectable="on"><fmtSpring:message code="page.label.conditional.enable"/></div>
				</div>
			</div>

			<!-- TAB: LAYOUT -->
			<div class="mce-panel" id="layoutInterfaceContainer" style="height: 100%; padding: 20px 20px;">

				<div class="mce-widget header-label">
					<fmtSpring:message code="page.label.input" />
				</div>
				
				<div style="white-space: nowrap; vertical-align: top;">
					<!-- INPUT: Left column -->
					<div style="display: inline-block; width: 50%; vertical-align: top;">
						<table cellspacing="0" cellpadding="0" border="0">
							<tr>
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.type" />
								</td>
								<td class="property-value">
									<div>
										<select id="menuTypeSelect" onchange="toggleType();" class="style_select inputS">
											<option value="combo"><fmtSpring:message code="page.label.combo.box" /></option>
											<option value="list"><fmtSpring:message code="page.label.list.box" /></option>
										</select>
									</div>
								</td>
							</tr>
							<tr id="listBoxHeightContainer" style="display: none;">
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.height" />
								</td>
								<td class="property-value">
									<div style="display: inline-block;">
										<msgpt:InputFilter type="decimal">
											<input type="text" id="listBoxHeightInput" class="inputS" />
										</msgpt:InputFilter>
									</div>
									<div class="canvasUnits" style="display: inline-block;">
									</div>
								</td>
							</tr>
							<tr>
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.width" />
								</td>
								<td class="property-value">
									<div id="fitToWidthContainer"> 
										<div style="display: inline-block;">
											<input id="fitToWidthToggle" checked="checked" type="checkbox" class="checkbox" onclick="toggleFieldWidthContainers()" />
										</div>
										<div style="display: inline-block; font-size:12px; padding-left: 3px;">
											<fmtSpring:message code="page.label.fit.to.container" />
										</div>
									</div>
									<div id="fixedFieldWidthContainer" style="display: none;"> 
										<div style="display: inline-block;">
											<msgpt:InputFilter type="decimal">
												<input type="text" id="fieldWidthInput" value="" class="inputS" />
											</msgpt:InputFilter>
										</div>
										<div class="canvasUnits" style="display: inline-block;">
										</div>
									</div>
								</td>
							</tr>
							<tr>
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.font.size" />
								</td>
								<td class="property-value">
									<div style="display: inline-block;">
										<msgpt:InputFilter type="decimal">
											<input type="text" id="fontSizeInput" value="12" class="inputS" />
										</msgpt:InputFilter>							
									</div>
									<div class="ptUnits" style="display: inline-block;">
										pt
									</div>
								</td>
							</tr>
						</table>
					</div>
					<!-- INPUT: Right column -->
					<div style="display: inline-block; width: 50%; vertical-align: top;">
						<table cellspacing="0" cellpadding="0" border="0">
							<tr>
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.tab.order" />
								</td>
								<td class="property-value">
									<div style="display: inline-block;">
										<msgpt:InputFilter type="numeric">
											<input type="text" id="tabOrderInput" onchange="validateTabOrder()" onkeyup="validateTabOrder()" class="inputS" />
										</msgpt:InputFilter>
									</div>
									<div id="tabOrderWarningContainer" style="display: inline-block;">
										<i class="fa fa-info-circle detailTip" style="display: none; padding-left: 6px; color: #de2f19; font-size: 16px;" title="|<div class='detailTipText'>${msgpt:getMessage("page.text.duplicate.tab.order")}</div>"></i>
									</div>
								</td>
							</tr>
							<tr id="borderTypeContainer">
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.border" />
								</td>
								<td class="property-value">
									<div>
										<select id="borderTypeSelect" onchange="toggleBorderType();" class="style_select inputS">
											<option value="box"><fmtSpring:message code="page.label.box" /></option>
											<option value="none"><fmtSpring:message code="page.label.none" /></option>
										</select>
									</div>
								</td>
							</tr>
							<tr id="borderWidthContainer">
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.border.width" />
								</td>
								<td class="property-value">
									<div style="display: inline-block;">
										<msgpt:InputFilter type="decimal">
											<input type="text" id="borderWidthInput" onchange="toggleBorderColorContainer()" onkeyup="toggleBorderColorContainer()" value="1" class="inputS" />
										</msgpt:InputFilter>
									</div>
									<div class="ptUnits" style="display: inline-block;">
										pt
									</div>
								</td>
							</tr>
							<tr id="borderColorContainer" style="display: none;">
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.border.color" />
								</td>
								<td class="property-value">
									<input type="text" id="borderColorInput" value="rgb(0,0,0)" style="display: none;" />
								</td>
							</tr>
						</table>
					</div>
				</div>
			
				<div class="mce-widget header-label" style="margin-top: 10px;">
					<fmtSpring:message code="page.label.label" />
				</div>
			
				<div style="white-space: nowrap; vertical-align: top;">
					<!-- LABEL: Left column -->
					<div style="display: inline-block; width: 50%; vertical-align: top;">
						<table cellspacing="0" cellpadding="0" border="0">
							<tr>
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.type" />
								</td>
								<td class="property-value">
									<div>
										<select id="labelTypeSelect" onchange="toggleLabelType()" class="style_select inputS">
											<option value="none"><fmtSpring:message code="page.label.no.label" /></option>
											<option value="above" selected="selected"><fmtSpring:message code="page.label.above" /></option>
											<option value="left"><fmtSpring:message code="page.label.left" /></option>
											<option value="right"><fmtSpring:message code="page.label.right" /></option>
										</select>
									</div>
								</td>
							</tr>
							<tr class="labelPropertyContainer">
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.width" />
								</td>
								<td class="property-value">
									<div id="minimizeWidthContainer"> 
										<div style="display: inline-block;">
											<input id="minimizeWidthToggle" checked="checked" type="checkbox" class="checkbox" onclick="toggleLabelWidthContainers()" />
										</div>
										<div style="display: inline-block; font-size:12px; padding-left: 3px;">
											<fmtSpring:message code="page.label.minimize" />
										</div>
									</div>
									<div id="fixedLabelWidthContainer" style="display: none;"> 
										<div style="display: inline-block;">
											<msgpt:InputFilter type="decimal">
												<input type="text" id="labelWidthInput" value="" class="inputS" />
											</msgpt:InputFilter>
										</div>
										<div class="canvasUnits" style="display: inline-block;">
										</div>
									</div>
								</td>
							</tr>
						</table>
					</div>
					<!-- LABEL: Right column -->
					<div style="display: inline-block; width: 50%; vertical-align: top;">
						<table cellspacing="0" cellpadding="0" border="0">
							<tr id="textStylesContainer" class="labelPropertyContainer">
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.text" />
								</td>
								<td class="property-value">
									<div>
										<select id="textStyleSelect" class="style_select inputXL">
											<option value="0"><fmtSpring:message code="page.text.loading" /></option>
										</select>
									</div>
								</td>
							</tr>
							<tr id="paragraphStylesContainer" class="labelPropertyContainer">
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.paragraph" />
								</td>
								<td class="property-value">
									<div>
										<select id="paragraphStyleSelect" class="style_select inputXL">
											<option value="0"><fmtSpring:message code="page.text.loading" /></option>
										</select>
									</div>
								</td>
							</tr>
						</table>
					</div>
				</div>
			
			</div> <!-- END layoutInterfaceContainer -->
			
			<!-- TAB: VALUES -->
			<div id="valuesInterfaceContainer" style="display: none;">
				<div class="mce-panel" style="padding: 4px 12px; border-bottom: 1px solid #ccc;">
					<table cellspacing="0" cellpadding="0" border="0">
						<tr>
							<td></td>
							<td class="mce-widget property-label">
								<fmtSpring:message code="page.label.default.value.selection" />
							</td>
							<td class="property-value" width="1%" style="white-space: nowrap;">
								<div style="display: inline-block; vertical-align: middle;">
									<select id="defaultValueTypeSelect" onchange="toggleDefaultValueType();" class="style_select inputM">
										<option value="0"><fmtSpring:message code="page.label.none" /></option>
										<option value="1"><fmtSpring:message code="page.label.select" /></option>
										<option value="2"><fmtSpring:message code="page.label.variable" /></option>
									</select>
								</div>
								<div id="defaultSelectionVariableContainer" style="display: inline-block; vertical-align: middle; min-width: 30px;">
									<select id="defaultSelectionVariableSelect" class="inputL style_select">
										<option value="0">Loading...</option>
									</select>
								</div>
							</td>
						</tr>
					</table>
				</div>
				<div style="white-space: nowrap; vertical-align: top;">								
					<div class="menuItemsContainer">
						<div class="addItemInfoContainer" style="margin: 8px; display: none;">
							<div class="InfoSysContainer_info" style="font-szie: 12px;">
								<fmtSpring:message code="page.text.click.to.add.value" />
							</div>
						</div>
						
						<div id="menuItemsListContainer" class="menuItemsListContainer sortableList">
						</div>
					</div>
				</div>
			</div> <!-- END valuesInterfaceContainer -->
			
			<!-- TAB: ALT TEXT -->
			<div id="altTextInterfaceContainer" style="display: none;">
			
				<textarea id="editor_menuAltTextEditor" class="mceEditor_menuAltTextEditor" >
				</textarea>
			
			</div> <!-- END altTextInterfaceContainer -->
			
			<!-- TAB: ID -->
			<div id="IDInterfaceContainer" style="display: none;">
			
				<textarea id="editor_menuIDEditor" class="mceEditor_menuIDEditor" >
				</textarea>
			
			</div> <!-- END IDInterfaceContainer -->

			<!-- TAB: CONDITIONAL ENABLE -->
			<div id="conditionalEnableInterfaceContainer" style="display: none;">
				<div style="white-space: nowrap; vertical-align: top;">
					<div class="conditionalEnableItemsContainer">
						<div class="addItemInfoContainer" style="margin: 8px; display: none;">
							<div class="InfoSysContainer_info" style="font-szie: 12px;">
								<fmtSpring:message code="page.text.click.to.add.conditional.enable.criteria" />
							</div>
						</div>

						<div id="conditionalEnableItemsListContainer" class="conditionalEnableItemsListContainer sortableList">
						</div>
					</div>
				</div>
			</div> <!-- END conditionalEnableInterfaceContainer -->

			<div id="valueEditInterfaceContainer" style="display: none; position: relative; top: -30px;">

				<div class="mce-container">
					<div class="mce-tabs" >
						<div class="mce-tab" unselectable="on"><fmtSpring:message code="page.label.edit.value"/></div>
					</div>
				</div>

				<input type="hidden" id="targetMenuItemIndex" value="" />
				<textarea id="editor_menuValueEditor" class="mceEditor_menuValueEditor" >
				</textarea>

			</div> <!-- END valueInterfaceContainer -->

			<div id="conditionalEnableEditInterfaceContainer" style="display: none; position: relative; top: -30px;">
			
				<div class="mce-container">
					<div class="mce-tabs" >
						<div class="mce-tab" unselectable="on"><fmtSpring:message code="page.label.edit.condition"/></div>
					</div>
				</div>
			
				<input type="hidden" id="targetConditionalEnableItemIndex" value="" />

				<div style="background-color: #fff; padding: 3px 8px;">
					<span style="font-size: 14px;"><fmtSpring:message code="page.text.when.input.value.is"/></span>
				</div>
				<textarea id="editor_conditionalEnableValueEditor" class="mceEditor_conditionalEnableValueEditor" >
				</textarea>

				<div style="background-color: #fff; padding: 7px 8px;">
					<div style="display: inline-block; vertical-align: middle;">
						<select id="conditionalEnableOperatorSelect" style="font-size: 14px;">
							<option value="enable"><fmtSpring:message code="page.label.enable"/></option>
							<option value="disable"><fmtSpring:message code="page.label.disable"/></option>
						</select>
					</div>
					<div style="display: inline-block; vertical-align: middle; padding-left: 8px;">
						<span style="font-size: 14px;"><fmtSpring:message code="page.text.the.following.named.inputs"/></span>
					</div>
				</div>

				<textarea id="editor_conditionalEnableTargetEditor" class="mceEditor_conditionalEnableTargetEditor" >
				</textarea>

			</div> <!-- END conditionalEnableEditInterfaceContainer -->

		</div>
		
		<div id="buttonPanel" class="mce-container mce-panel mce-foot" role="group" tabindex="-1" hidefocus="1" style="border-width: 1px 0px 0px; left: 0px; top: 0px; width: 100%; height: 50px;">
			<div class="mce-container-body mce-abs-layout" style="width: 100%; height: 50px;">
				<div class="mce-abs-end"></div>
				<div id="addValueButton" class="mce-widget mce-btn mce-abs-layout-item" tabindex="-1" role="button" style="right: 145px; top: 10px; width: 80px; height: 28px; display: none;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.add.value" /></button>
				</div>
				<div id="addConditionButton" class="mce-widget mce-btn mce-abs-layout-item" tabindex="-1" role="button" style="right: 145px; top: 10px; width: 105px; height: 28px; display: none;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.add.condition" /></button>
				</div>
				<div id="applyPropertiesButton" class="mce-widget mce-btn mce-primary mce-first mce-abs-layout-item" tabindex="-1" role="button" style="right: 85px; top: 10px; width: 50px; height: 28px;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.apply" /></button>
				</div>
				<div id="cancelButton" class="mce-widget mce-btn mce-last mce-abs-layout-item" tabindex="-1" role="button" style="right: 10px; top: 10px; width: 64px; height: 28px;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.cancel" /></button>
				</div>
				
				<div id="setValueButton" class="mce-widget mce-primary mce-btn mce-abs-layout-item" tabindex="-1" role="button" style="right: 10px; top: 10px; width: 75px; height: 28px; display: none;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.set.value" /></button>
				</div>
				<div id="setConditionButton" class="mce-widget mce-primary mce-btn mce-abs-layout-item" tabindex="-1" role="button" style="right: 10px; top: 10px; width: 100px; height: 28px; display: none;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.set.condition" /></button>
				</div>
				<div id="menuItemBackButton" class="mce-widget mce-btn mce-abs-layout-item" tabindex="-1" role="button" style="right: 95px; top: 10px; width: 64px; height: 28px; display: none;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.back" /></button>
				</div>
				<div id="conditionItemBackButton" class="mce-widget mce-btn mce-abs-layout-item" tabindex="-1" role="button" style="right: 120px; top: 10px; width: 64px; height: 28px; display: none;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.back" /></button>
				</div>
			</div>
		</div>
	</msgpt:BodyNew>
</msgpt:Html5>