<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.content" extendedScripts="true">

        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:CalendarIncludes/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>
        <msgpt:Script src="_ux/js/mp.postTrigger.js"/>

        <msgpt:Script>
            <script>

                var $updateBtn, $deleteBtn, $cloneBtn, $actionMenu;

                // *********  INIT: START  *********
                $(function () {

                    $updateBtn = $('#updateBtn');
                    $deleteBtn = $('#deleteBtn');
                    $cloneBtn = $('#cloneBtn');
                    $actionMenu = $('#actionMenu');

                    $('#addParagraphStyleBtn').on('click', function () {

                        javascriptHref(context + '/content/paragraph_style_edit.form');

                    });

                });

                function iFrameView(path, eleId, e) {
                    if (e != undefined) {
                        if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
                            $("#" + eleId).iFramePopup($.extend({
                                src: path,
                                displayOnInit: true,
                                id: "paragraphStyleFrame",
                                appliedParams: {tk: "${param.tk}"},
                                beforePopupClose: function () {
                                    rebuildListTable();
                                },
                                onSave: function () {
                                }
                            }, iFramePopup_fullFrameAttr));
                        }
                        $('.contentPopup_popupContainer').remove();
                        e.preventDefault ? e.stopPropagation() : e.returnValue = false;
                    }
                }

                function rebuildListTable() {
                    $('#paragraphStyleList').DataTable().ajax.reload(null, true);
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.columns = [
                        {
                            columnMap: 'name',
                            columnName: client_messages.text.name,
                            sort: true,
                            width: '15%',
                            colVisToggle: false
                        },
                        {
                            columnMap: 'connectorName',
                            columnName: client_messages.text.connector_name,
                            sort: true,
                            width: '15%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'alignment',
                            columnName: client_messages.text.alignment,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'lineSpacing',
                            columnName: client_messages.text.line_spacing,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'indentation',
                            columnName: client_messages.text.indentation,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'marginLR',
                            columnName: client_messages.text.indent_left_right,
                            sort: false,
                            width: '15%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'marginTB',
                            columnName: client_messages.text.spacing_before_after,
                            sort: false,
                            width: '15%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'border',
                            columnName: client_messages.text.border,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        }
                    ];
                    return obj;
                }

                function getAsyncExtParams() {
                    var stampDate = new Date();
                    var obj = [
                        {"name": "listTableType", "value": "9"},
                        {"name": "displayMode", "value": "full"},
                        {"name": "cacheStamp", "value": stampDate.getTime()}
                    ];
                    return obj;
                }

                function postListRenderFlagInjection(oObj) {
                    var iFrameId = "paragraph-id";
                    var iFrameSrc = context + "/content/paragraph_style_list_detail.form?paraStyleId=" + oObj.aData.dt_RowId;

                    if ($('#listSearchInput').val() != "")
                        iFrameSrc += "&sSearch=" + $('#listSearchInput').val();

                    var binding = oObj.aData.binding;

                    var text = oObj.aData.name;
                    text += "<input id='iFrameId' value=" + iFrameId + " type='hidden' class='iframe-data' />";
                    text += "<input id='iFrameSrc' value=" + iFrameSrc + " type='hidden' class='iframe-data' />";

                    //Selection check select
                    text += binding;

                    //Selection permission flags
                    if (oObj.aData.flags.canUpdate)
                        text += "<input type='hidden' id='canUpdate_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canDelete)
                        text += "<input type='hidden' id='canDelete_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canClone)
                        text += "<input type='hidden' id='canClone_" + oObj.aData.dt_RowId + "' />";
                    return text;
                }

                // List actions: Edit
                function iFrameAction(actionId) {
                    var paraStyleId;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        paraStyleId = this.id.replace('listItemCheck_', '');
                    });

                    if (actionId == '1') {

                        javascriptHref(context + '/content/paragraph_style_edit.form?paraStyleId=' + paraStyleId);

                    }
                }

                function actionMenuSelected(el) {

                    var elSelectedId = $(el).children(':selected').attr('id');

                    if (parseId($(el).find('option:selected').attr('id')) == "5") {
                        $(el).find('option:selected').iFramePopup({
                            width: 500,
                            title: client_messages.title.bulk_delete,
                            src: context + "/wtu/delete_unref_assets.form",
                            displayOnInit: true,
                            appliedParams: {tk: "${param.tk}", assetTypeId: '2'},
                            closeBtnId: "cancelBtn_button",
                            beforePopupClose: function () {
                                location.reload();
                            }
                        });
                    } else if (elSelectedId.indexOf('actionOption_') !== -1)
                        actionSelected(el);

                    else
                        iFrameAction(elSelectedId.replace('actioniFrame_', ''));

                }

                function validateActionReq(paraStyleId) {
                    var singleSelect = true;

                    var canUpdate = true;
                    var canClone = true;
                    var canDelete = true;

                    // Resolve selection flags
                    if ($("input[id^='listItemCheck_']:checked").length != 1)
                        singleSelect = false;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        var paraStyleId = this.id.replace('listItemCheck_', '');
                        if (!exists('canUpdate_' + paraStyleId))
                            canUpdate = false;
                        if (!exists('canClone_' + paraStyleId))
                            canClone = false;
                        if (!exists('canDelete_' + paraStyleId))
                            canDelete = false;
                    });

                    //Disable all context menu entries
                    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
                    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');
                    $actionMenu.data('complexDropdown').disableAllTheOptions();
                    common.disableElement($updateBtn);
                    common.disableElement($deleteBtn);
                    common.disableElement($cloneBtn);

                    $actionMenu.data('complexDropdown').enableOptionById('actionOption_5');	// Delete unreferenced
                    //Enable context menu entries based on flag status
                    if (singleSelect) {
                        if (canUpdate) {
                            $('a#actioniFrame_1').removeClass('disabled');
                            common.enableElement($updateBtn);
                        }
                        if (canClone) {
                            $('a#actionOption_2').removeClass('disabled');
                            common.enableElement($cloneBtn);
                        }
                        if (canDelete) {
                            $('a#actionOption_3').removeClass('disabled');
                            common.enableElement($deleteBtn);
                        }
                        $('a#actionOption_4').removeClass('disabled');		// Where Used
                        $actionMenu.data('complexDropdown').enableOptionById('actionOption_4');
                    }
                }

                function submitPreAction(submitId) {
                    if (submitId == '4') {
                        var whereUsedReportId = $('#whereUsedReportId').val();
                        popItUpWithDimensions('../where_used_report.jsp?whereUsedReportId=' + whereUsedReportId, 850, 900);
                    }
                    return false;
                }
            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>
    <msgpt:BodyNew>
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_CONTENTS %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_CONTENTS %>"/>
        <msgpt:ContextBarNew touchpointContextDisabled="true"/>
        <msgpt:LowerContainer fullPanel="true" extendedWidth="true">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="true">
                <form:form method="post" modelAttribute="command">
                    <form:errors path="*">
                        <msgpt:Information errorMsgs="${messages}" type="error"/>
                    </form:errors>
                    <c:if test="${not empty param.saveSuccess && param.saveSuccess == 'true'}">
                        <msgpt:Information type="success">
                            <fmtSpring:message code="page.label.save.complete"/>
                        </msgpt:Information>
                    </c:if>
                    <h1 class="h4 pb-2 mb-4"><fmtSpring:message code="page.label.paragraph.style"/></h1>
                    <div class="box-shadow-4 rounded bg-white p-4">
                        <div class="px-2 py-1">
                            <div class="mb-4">
                                <div class="d-flex align-items-center pb-1">
                                    <form:hidden path="actionValue" id="actionElement"/>
                                    <msgpt:IfAuthGranted authority="ROLE_STYLES_UPDATE">
                                        <div class="mr-3">
                                            <button id="addParagraphStyleBtn" class="btn btn-primary post-trigger" type="button">
                                                <i class="far fa-plus-circle mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="page.label.add"/>
                                            </button>
                                        </div>
                                    </msgpt:IfAuthGranted>
                                    <div class="btn-group border-separate mr-auto" role="group"
                                         aria-label="${msgpt:getMessage("page.label.actions")}">
                                        <button id="updateBtn" type="button" class="btn btn-dark post-trigger"
                                                onclick="iFrameAction(1,this);" disabled>
                                            <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                            <fmtSpring:message code="action.button.label.update"/>
                                        </button>
                                        <button id="deleteBtn" type="button" class="btn btn-dark"
                                                onclick="actionSelected(3);" disabled>
                                            <i class="far fa-trash-alt mr-2" aria-hidden="true"></i>
                                            <fmtSpring:message code="page.label.delete"/>
                                        </button>
                                        <button id="cloneBtn" type="button" class="btn btn-dark"
                                                onclick="actionSelected(2);" disabled>
                                            <i class="far fa-clone mr-2" aria-hidden="true"></i>
                                            <fmtSpring:message code="page.label.clone"/>
                                        </button>
                                        <div class="btn-group">
                                            <select title="${msgpt:getMessage('page.label.more')}"
                                                    id="actionMenu"
                                                    class="complex-dropdown-select"
                                                    aria-label="${msgpt:getMessage('page.label.more')}"
                                                    data-toggle="complex-dropdown"
                                                    data-action="menu"
                                                    data-dropdown-class="btn-dark"
                                                    onchange="actionMenuSelected(this)">
                                                <option id="actionOption_4" disabled><fmtSpring:message
                                                        code="page.label.where.used"/></option>
                                                <option id="actionOption_5"><fmtSpring:message
                                                        code="page.label.bulk.delete"/></option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="mr-3 colVisToggleContainer" data-toggle="tooltip"
                                         title="${msgpt:getMessage('client_messages.label.toggle_columns')}">
                                    </div>
                                    <div class="form-group position-relative d-inline-block m-0">
                                        <label for="listSearchInput" class="sr-only"><fmtSpring:message
                                                code="page.label.search"/></label>
                                        <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                           style="z-index: 1;" aria-hidden="true"></i>
                                        <msgpt:InputFilter type="description">
                                            <input id="listSearchInput" type="text" size="25"
                                                   class="form-control bg-lightest has-control-l border-0"
                                                   placeholder="${msgpt:getMessage('page.label.search')}"/>
                                        </msgpt:InputFilter>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white">
                                <msgpt:DataTable id="paragraphStyleList" listHeader="page.label.paragraph.styles"
                                                 async="true"
                                                 columnReorder="true" numUnreorderableCols="2" columnVisibility="true"
                                                 drillDown="true" singleSelect="true" searchFilter="true">
                                </msgpt:DataTable>
                            </div>
                        </div>
                    </div>

                    <!-- POPUP DATA -->
                    <div id="actionSpecs" style="display: none;">
                        <!-- ACTIONS POPUP DATA -->
                        <!-- Clone -->
                        <div id="actionSpec_2" submitId="2" contentWidth="500px"> <!-- Clone -->
                            <div id="actionTitle_2"><fmtSpring:message code="page.label.confirm.clone"/></div>
                            <div id="actionInfo_2"><p><fmtSpring:message
                                    code="page.text.would.you.like.to.clone.selected.asset"/></p></div>
                            <div id="actionClone_2" required="true"></div>
                        </div>
                        <!-- Delete -->
                        <div id="actionSpec_3" type="simpleConfirm" submitId="3"> <!-- Discard working copy -->
                            <div id="actionTitle_3"><fmtSpring:message
                                    code="page.label.confirm.delete.paragraph.style"/></div>
                            <div id="actionInfo_3"><fmtSpring:message
                                    code="page.text.delete.selected.paragraph.style"/></div>
                        </div>
                        <!-- Where used -->
                        <div id="actionSpec_4" submitId="4">
                            <div id="actionTitle_4"><fmtSpring:message
                                    code="page.label.confirm.generate.where.used"/></div>
                            <div id="actionInfo_4"><fmtSpring:message
                                    code="page.text.report.loads.in.separate.window"/></div>
                            <div id="actionWhereUsedOptions_4"></div>
                        </div>
                    </div>

                    <!-- POPUP INTERFACE -->
                    <form:hidden path="whereUsedReportId" id="whereUsedReportId"/>
                    <msgpt:Popup id="actionPopup" theme="minimal">
                        <div id="actionPopupInfoFrame">
                            <div id="actionPopupInfo">&nbsp;</div>
                        </div>
                        <div id="actionPopupClone">
                            <div class="formControl">
                                <label><span class="labelText"><fmtSpring:message
                                        code="page.label.name"/></span></label>
                                <div class="controlWrapper">
                                    <msgpt:InputFilter type="simpleName">
                                        <form:input path="cloneName"
                                                    onkeyup="validatePopupReq();"
                                                    onchange="validatePopupReq();"
                                                    id="clone_newInstanceName" onfocus="this.select()"/>
                                    </msgpt:InputFilter>
                                </div>
                            </div>
                        </div>
                        <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                            <span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                              label="page.label.cancel"
                                                                                              disabled="true"/></span>
                            <span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                      label="page.label.cancel"/></span>
                            <span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                label="page.label.continue"
                                                                                                disabled="true"/></span>
                            <span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue"
                                                                        primary="true"/></span>
                        </div>
                    </msgpt:Popup>
                </form:form>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
        <msgpt:ContextMenu name="paragraphStyleList">
            <msgpt:ContextMenuEntry name="actioniFrame_1" link="#iFrameAction:1"><fmtSpring:message
                    code="page.label.edit"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_2" link="#actionSelected:2"><fmtSpring:message
                    code="page.label.clone"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_3" link="#actionSelected:3"><fmtSpring:message
                    code="page.label.delete"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_4" link="#actionSelected:4"><fmtSpring:message
                    code="page.label.where.used"/></msgpt:ContextMenuEntry>
        </msgpt:ContextMenu>
    </msgpt:BodyNew>
</msgpt:Html5>