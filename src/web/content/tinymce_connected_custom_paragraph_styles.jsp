
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
	<msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">

		<style>
			.mce-container i, .mce-widget i, .mce-reset i {
				font-family: "Font Awesome 6 Pro";
			}
		</style>

        <msgpt:Script>
            <script>
				var ed = window.parent.tinymce.activeEditor;
				var originalParagraphAttr = {};

				function init(isRefreshing) {
					isRefreshing = isRefreshing || false;
					if(isRefreshing && modalContent !== null) {
						$('#modalContent').html(modalContent);
					} else {
						modalContent = $('#modalContent').html();
					}

					// ed.settings.mp_fn.position_popup();

					// ed.undoManager.add();

					// Input Listeners to update Paragraph Attributes
					$('#alignment, #leftMargin, #increments, #rightMargin, #spacingBefore, #tab_incr, #spacingAfter, #lineSpacingType, #lineSpacing, #special, #specialIndentation').on('keyup change', enableApplyButton);

					toggleLineSpacingType();
					toggleSpecialType();
					updateParagraphModalInputValues();
					// updateParagraph();

					if (ed.settings.customparagraph.pasting_object) {
						$('#pasteFormatting').prop('disabled', false);
					}

					disableApplyButton();

				}

				function toggleLineSpacingType() {
					if ($('#lineSpacingType').val() == 1)
						$('#lineSpacingUnits').html('<abbr class="text-monospace" title="Lines">lns</abbr>');
					else
						$('#lineSpacingUnits').html('<abbr class="text-monospace" title="Points">pts</abbr>');
				}

				function toggleSpecialType() {
					if ($('#special').val() == "") {
						$('#specialIndentation').val('0');
						$('#specialIndentation').prop('disabled', true);
					} else {
						$('#specialIndentation').prop('disabled', false);
					}
				}

				function toggleAttributeIconState(attribute, originalValue, newValue) {

					var paragraph = $(ed.settings.customparagraph.selected_ele);
					var paragraphStyleAttr = 'paragraphclass';

					var selectors = {
						'text_align': "label[for='alignment']",
						'spacing_left': "label[for='leftMargin']",
						'spacing_right': "label[for='rightMargin']",
						'spacing_before': "label[for='spacingBefore']",
						'spacing_after': "label[for='spacingAfter']",
						'tab_incr': "label[for='tab_incr']",
						'line_spacing_type': "label[for='lineSpacingType']",
						'line_spacing': "label[for='lineSpacing']",
						'indent_type': "label[for='indent_type']",
						'special_indent': "label[for='special']",
					};

					var hoverTitles = {
						'text_align': client_messages.text.text_align,
						'spacing_left': client_messages.text.spacing,
						'spacing_right': client_messages.text.spacing,
						'spacing_before': client_messages.text.spacing,
						'spacing_after': client_messages.text.spacing,
						'tab_incr': client_messages.text.tab_incr,
						'line_spacing_type': client_messages.text.line_spacing_type,
						'line_spacing': client_messages.text.line_spacing,
						'indent_type': client_messages.text.special_indent,
						'special_indent': client_messages.text.special_indent,
					};

					if (!selectors.hasOwnProperty(attribute)) {
						return;
					}

					var overridesMessage = paragraph.attr(paragraphStyleAttr)
							? client_messages.text.attribute_overrides_applied_paragraph_style
							: client_messages.text.attribute_overrides_default_paragraph_formatting;

					var hoverTitle = hoverTitles[attribute] && overridesMessage
							? overridesMessage.replace('{0}', hoverTitles[attribute].toLowerCase())
							: '';

					var iconHtml = '<span title="' + hoverTitle +'"><i class="fa ml-1 small" aria-hidden="true"></i></span>';

					var selector = selectors[attribute];
					var $targetElement = $(selector);

					if ($targetElement.length > 0) {
						if ($targetElement.find('i').length === 0) {
							$targetElement.append(iconHtml);
						}

						$targetElement.find('i').toggleClass('fa-unlink', originalValue !== newValue);
					}
				}

				function setLeftMarginStep() {
					$('#leftMargin').attr('step', $('#tab_incr').val());
				}

				function getAlignment(alignmentValue) {
					return alignmentValue ? alignmentValue.replace('justified', 'justify') : 'left';
				}

				function updateParagraphModalInputValues() {
					if (!ed.settings.customparagraph.selected_ele) {
						$('#toggleBulletValues').val('');
						return;
					}

					var classPrefix = 'tinymce_paragraphstyle_';
					var paragraphStyleAttr = 'paragraphclass';

					var paragraph = $(ed.settings.customparagraph.selected_ele);

					var targetNode = paragraph;

					while ( $(targetNode).parent().closest('p').length != 0 ) {
						targetNode = $(targetNode).parent().closest('p');
					}

					var appliedParagraphStyle = targetNode.attr(paragraphStyleAttr) ? targetNode.attr(paragraphStyleAttr).replace(classPrefix, '') : '';

					originalParagraphAttr = ed.settings.mp_fn.get_original_paragraph_attributes(paragraph);

					var paragraphAttr = ed.settings.mp_fn.parse_paragraph_attributes(paragraph);

					if (paragraphAttr.text_align) {
						$('#alignment').val(paragraphAttr.text_align);
					}

					if(paragraphAttr.spacing) {
						$('#leftMargin').val(paragraphAttr.spacing.left);
						$('#rightMargin').val(paragraphAttr.spacing.right);
						$('#spacingBefore').val(paragraphAttr.spacing.top);
						$('#spacingAfter').val(paragraphAttr.spacing.bottom);
					}

					if (paragraphAttr.tab_incr) {
						$('#tab_incr').val(paragraphAttr.tab_incr);
					}

					if (paragraphAttr.line_spacing) {
						$('#lineSpacingType').val(paragraphAttr.line_spacing.toString().slice(-2) === 'pt' ? 2 : 1);
						$('#lineSpacing').val(parseFloat(paragraphAttr.line_spacing));
						toggleLineSpacingType();
					}

					if (paragraphAttr.special) {
						$('#special').val(paragraphAttr.special.type);
						$('#specialIndentation').val(Math.abs(paragraphAttr.special.value));
						toggleSpecialType();
					}

					if (appliedParagraphStyle) {
						$('.appliedParagraphStyleRow').show();
						$('#appliedParagraphStyle').text(appliedParagraphStyle);
					} else {
						$('.appliedParagraphStyleRow').hide();
						$('#appliedParagraphStyle').text('Default');
					}
				}

				function updateParagraph() {

					if (!ed.settings.customparagraph.selected_ele) {
						return;
					}

					function updateAttribute(attribute, originalValue, newValue) {
						if (originalValue !== newValue) {
							paragraph.attr(attribute, newValue);
						// } else {
						// 	paragraph.removeAttr(attribute);
						}
					};

					// Updating list custom attributes
					var paragraph = $(ed.settings.customparagraph.selected_ele);

					var originalAlignment = getAlignment(originalParagraphAttr.text_align);
					var alignmentValue = getAlignment($('#alignment').val());
					updateAttribute('text_align', originalAlignment, alignmentValue);
					toggleAttributeIconState('text_align', originalAlignment,alignmentValue);

					var originalParagraphSpacing = 	originalParagraphAttr.spacing.top + 'in:' +
													originalParagraphAttr.spacing.right + 'in:' +
													originalParagraphAttr.spacing.bottom + 'in:' +
													originalParagraphAttr.spacing.left + 'in';

					var paragraphSpacing = 	$('#spacingBefore').val() + 'in:' +
											$('#rightMargin').val() + 'in:' +
											$('#spacingAfter').val() + 'in:' +
											$('#leftMargin').val() + 'in';

					updateAttribute('spacing', originalParagraphSpacing, paragraphSpacing);

					toggleAttributeIconState('spacing_left', originalParagraphAttr.spacing.left.toString(), $('#leftMargin').val());
					toggleAttributeIconState('spacing_right', originalParagraphAttr.spacing.right.toString(), $('#rightMargin').val());
					toggleAttributeIconState('spacing_before', originalParagraphAttr.spacing.top.toString(), $('#spacingBefore').val());
					toggleAttributeIconState('spacing_after', originalParagraphAttr.spacing.bottom.toString(), $('#spacingAfter').val());

					var originalIncrements = originalParagraphAttr.tab_incr;
					var incrementsValue = parseFloat($('#tab_incr').val());
					updateAttribute('tab_incr', originalIncrements, incrementsValue);
					toggleAttributeIconState('tab_incr', originalIncrements, incrementsValue);

					var originalLineSpacingType = originalParagraphAttr.line_spacing.replace(/[^a-zA-Z]/g, "");
					var lineSpacingType = $('#lineSpacingType').val() == 2 ? 'pt' : '';
					toggleAttributeIconState('line_spacing_type', originalLineSpacingType, lineSpacingType);

					var originalLineSpacing = originalParagraphAttr.line_spacing;
					var lineSpacingValue = parseFloat($('#lineSpacing').val()).toFixed(2);
					var lineSpacing = lineSpacingValue + lineSpacingType;
					updateAttribute('line_spacing', originalLineSpacing, lineSpacing);
					toggleAttributeIconState('line_spacing', originalLineSpacing, lineSpacingValue);

					var originalSpecialType = originalParagraphAttr.special.type;
					var specialType = $('#special').val();
					updateAttribute('indent_type', originalSpecialType, specialType);
					toggleAttributeIconState('indent_type', originalSpecialType, specialType);

					var originalSpecialIndentation = originalParagraphAttr.special.value;
					var specialIndentation = parseFloat($('#specialIndentation').val()) * ($('#special').val() === 'first' ? 1 : -1);
					updateAttribute('special_indent', originalSpecialIndentation, specialIndentation);
					toggleAttributeIconState('special_indent', originalSpecialIndentation, specialIndentation);

					ed.settings.mp_fn.update_paragraph_styles_from_attributes(paragraph, true);
					ed.undoManager.add();

					$('#applyFormatting').prop('disabled', true);

                    // simulate shift+tab
                    ed.execCommand('mceFocus', false, ed.id);
				}

				function toggleElementsDisabled(disabled) {
					$('#clearFormatting').prop('disabled', disabled);
					$('#copyFormatting').prop('disabled', disabled);
					$('#pasteFormatting').prop('disabled', disabled || !ed.settings.customparagraph.pasting_object);

					$('#alignment, #leftMargin, #increments, #rightMargin, #spacingBefore, #tab_incr, #spacingAfter, #lineSpacingType, #lineSpacing, #special').prop('disabled', disabled);

					if (!$('#specialIndentation').prop('disabled')) {
						$('#specialIndentation').prop('disabled', disabled);
					}
				}

				function setOrRemoveAttr(paragraph, attr, value) {
					if (value) {
						paragraph.attr(attr, value);
					} else {
						paragraph.removeAttr(attr);
					}
				}

				function clearFormatting() {
					var paragraph = $(ed.settings.customparagraph.selected_ele);

					paragraph.removeAttr('text_align');
					paragraph.removeAttr('spacing');
					paragraph.removeAttr('tab_incr');
					paragraph.removeAttr('line_spacing');
					paragraph.removeAttr('indent_type');
					paragraph.removeAttr('special_indent');

					ed.settings.mp_fn.update_paragraph_styles_from_attributes(paragraph);
					ed.settings.mp_fn.update_connected_custom_paragraph_styles_dialogue();
					ed.undoManager.add();

				}

				function copyFormatting() {
					var paragraph = $(ed.settings.customparagraph.selected_ele);

					var textAlign = paragraph.attr('text_align');
					var spacing = paragraph.attr('spacing');
					var increments = paragraph.attr('tab_incr');
					var lineSpacing = paragraph.attr('line_spacing');
					var indentType = paragraph.attr('indent_type');
					var specialIndent = paragraph.attr('special_indent');

					var pastingObject = {
						textAlign: textAlign,
						spacing: spacing,
						increments: increments,
						lineSpacing: lineSpacing,
						indentType: indentType,
						specialIndent: specialIndent
					};

					ed.settings.customparagraph.pasting_object = pastingObject;
					$('#pasteFormatting').prop('disabled', false);
				}

				function pasteFormatting() {
					var paragraph = $(ed.settings.customparagraph.selected_ele);
					var pastingObject = ed.settings.customparagraph.pasting_object;

					if (!pastingObject) {
						return;
					}

					setOrRemoveAttr(paragraph, 'text_align', pastingObject.textAlign);
					setOrRemoveAttr(paragraph, 'spacing', pastingObject.spacing);
					setOrRemoveAttr(paragraph, 'increments', pastingObject.increments);
					setOrRemoveAttr(paragraph, 'line_spacing', pastingObject.lineSpacing);
					setOrRemoveAttr(paragraph, 'indent_type', pastingObject.indentType);
					setOrRemoveAttr(paragraph, 'special_indent', pastingObject.specialIndent);

					ed.settings.mp_fn.update_paragraph_styles_from_attributes(paragraph);
					ed.settings.mp_fn.update_connected_custom_paragraph_styles_dialogue();
					ed.undoManager.add();
				}

				function enableApplyButton() {
					$('#applyFormatting').prop('disabled', false);
				}

				function disableApplyButton() {
					$('#applyFormatting').prop('disabled', true);
				}

				function refreshParagraph() {
					updateParagraphModalInputValues();
					updateParagraph();
				}

				$( function() {
					init();
				});

            </script>
        </msgpt:Script>

	</msgpt:HeaderNew>

	<msgpt:BodyNew type="minimal" cssStyle="width: 100%; background: white; overflow: hidden;">
		<div id="modalContent">
			<div style="width: 100%;">
				<div class="mce-panel" style="box-sizing: border-box; padding: 15px; padding-top: 5px;">
					<section id="setParagraphFormatting" class="text-right" style="margin-bottom: -5px;">
						<button id="clearFormatting" class="btn btn-sm btn-outline-dark mr-1" type="button"
								onclick="clearFormatting();" title="${msgpt:getMessage('page.label.clear.custom.paragraph.style')}">
							<div class="btn-content d-inline-block">
								<i class="fas fa-eraser mr-1" aria-hidden="true"></i>
								<span class="btn-text"><fmtSpring:message code="page.label.clear"/></span>
							</div>
						</button>
						<button id="copyFormatting" class="btn btn-sm btn-outline-dark mr-1" type="button"
								onclick="copyFormatting();" title="${msgpt:getMessage('page.label.copy.custom.paragraph.style')}">
							<div class="btn-content d-inline-block">
								<i class="fas fa-clone mr-1" aria-hidden="true"></i>
								<span class="btn-text"><fmtSpring:message code="page.label.copy"/></span>
							</div>
						</button>
						<button id="pasteFormatting" class="btn btn-sm btn-outline-dark" type="button"
								onclick="pasteFormatting();" disabled="true" title="${msgpt:getMessage('page.label.paste.custom.paragraph.style')}">
							<div class="btn-content d-inline-block">
								<i class="fas fa-brush mr-1" aria-hidden="true"></i>
								<span class="btn-text"><fmtSpring:message code="page.label.paste"/></span>
							</div>
						</button>
						<button id="applyFormatting" class="btn btn-sm btn-primary" type="button"
								onclick="updateParagraph();" disabled="true" title="${msgpt:getMessage('page.label.apply.custom.paragraph.style')}">
							<div class="btn-content d-inline-block">
								<i class="fas fa-floppy-disk-pen mr-1" aria-hidden="true"></i>
								<span class="btn-text"><fmtSpring:message code="page.label.apply"/></span>
							</div>
						</button>
					</section>
					<section id="setParagraphContainer" class="pb-3 mb-3">
						<div class="form-row">
							<div class="form-group col-6 mt-4 mb-2">
								<label for="alignment">
									<fmtSpring:message code="page.label.alignment"/>
								</label>
								<div class="input-group">
									<select id="alignment" name="alignment" class="custom-select">
										<c:forEach var="currentAlignment" items="${paragraphAlignments}">
											<option value="${currentAlignment.name.toLowerCase()}">
												<fmtSpring:message code="${currentAlignment.displayMessageCode}"/>
											</option>
										</c:forEach>
									</select>
								</div>
							</div>
							<div class="form-group col-6 mt-4 mb-2">
								<label for="tab_incr">
									<fmtSpring:message code="page.label.increments"/>
								</label>
								<div class="input-group">
									<input id="tab_incr" name="tab_incr" class="form-control decimal" min="0" aria-describedby="incrementsDesc" type="number" step="0.25" value="0" onchange="setLeftMarginStep()" />
									<div class="input-group-append">
										<span class="input-group-text" id="incrementsDesc">
											<abbr class="text-monospace" title="Inches">in</abbr>
										</span>
									</div>
								</div>
							</div>
						</div>
						<div class="form-row">
							<div class="form-group col-6 mb-2">
								<label for="leftMargin" class="spacingLabel">
									<fmtSpring:message code="page.label.left"/>
								</label>
								<div class="input-group">
									<input id="leftMargin" name="leftMargin" class="form-control decimal" min="0" aria-describedby="leftMarginDesc" type="number" step="0.25" value="0" />
									<div class="input-group-append">
										<span class="input-group-text" id="leftMarginDesc">
											<abbr class="text-monospace" title="Inches">in</abbr>
										</span>
									</div>
								</div>
							</div>
							<div class="form-group col-6 mb-2">
								<label for="rightMargin" class="spacingLabel">
									<fmtSpring:message code="page.label.right"/>
								</label>
								<div class="input-group">
									<input id="rightMargin" name="rightMargin" class="form-control decimal" min="0" aria-describedby="rightMarginDesc" type="number" step="0.25" value="0" />
									<div class="input-group-append">
										<span class="input-group-text" id="rightMarginDesc">
											<abbr class="text-monospace" title="Inches">in</abbr>
										</span>
									</div>
								</div>
							</div>
						</div>
						<div class="form-row">
							<div class="form-group col-6 mb-2">
								<label for="spacingBefore" class="spacingLabel">
									<fmtSpring:message code="page.label.spacing.before"/>
								</label>
								<div class="input-group">
									<input id="spacingBefore" name="spacingBefore" class="form-control decimal" min="0" aria-describedby="spacingBeforeDesc" type="number" step="0.25" value="0" />
									<div class="input-group-append">
										<span class="input-group-text" id="spacingBeforeDesc">
											<abbr class="text-monospace" title="Inches">in</abbr>
										</span>
									</div>
								</div>
							</div>
							<div class="form-group col-6 mb-2">
								<label for="spacingAfter" class="spacingLabel">
									<fmtSpring:message code="page.label.spacing.after"/>
								</label>
								<div class="input-group">
									<input id="spacingAfter" name="spacingAfter" class="form-control decimal" min="0" aria-describedby="spacingAfterDesc" type="number" step="0.25" value="0" />
									<div class="input-group-append">
										<span class="input-group-text" id="spacingAfterDesc">
											<abbr class="text-monospace" title="Inches">in</abbr>
										</span>
									</div>
								</div>
							</div>
						</div>
						<div class="form-row">
							<div class="form-group col-6 mb-2">
								<label for="lineSpacingType">
									<fmtSpring:message code="page.label.line.spacing.type"/>
								</label>
								<div class="input-group">
									<select id="lineSpacingType" name="lineSpacingType" class="custom-select" onchange="toggleLineSpacingType();">
										<c:forEach var="lineSpacingType" items="${lineSpacingTypes}">
											<option value="${lineSpacingType.id}">
												<c:out value="${lineSpacingType.name}"/>
											</option>
										</c:forEach>
									</select>
								</div>
							</div>
							<div class="form-group col-6 mb-2">
								<label for="lineSpacing">
									<fmtSpring:message code="page.label.line.spacing"/>
								</label>
								<div class="input-group">
									<input id="lineSpacing" name="lineSpacing" class="form-control decimal" min="0" aria-describedby="lineSpacingDesc" type="number" step="0.25" value="0" />
									<div class="input-group-append">
										<span class="input-group-text" id="lineSpacingUnits">
											<abbr class="text-monospace" title="Lines">lns</abbr>
										</span>
									</div>
								</div>
							</div>
						</div>
						<div class="form-row special-row">
							<div class="form-group col-6 mb-2">
								<label for="special">
									<fmtSpring:message code="page.label.special"/>
								</label>
								<div class="input-group">
									<select id="special" name="hangingIndent" class="custom-select" onchange="toggleSpecialType();">
										<option value="">
											<fmtSpring:message code="page.label.none"/>
										</option>
										<option value="first">
											<fmtSpring:message code="page.text.first.line.indent"/>
										</option>
										<option value="hanging">
											<fmtSpring:message code="page.text.all.but.first.line.indent"/>
										</option>
									</select>
								</div>
							</div>
							<div class="form-group col-6 mb-2">
								<label for="specialIndentation">&nbsp;</label>
								<div class="input-group">
									<input id="specialIndentation" name="specialIndentation" class="form-control decimal" min="0" aria-describedby="specialIndentationDesc" type="number" step="0.25" value="0" />
									<div class="input-group-append">
										<span class="input-group-text" id="specialIndentationDesc">
											<abbr class="text-monospace" title="Inches">in</abbr>
										</span>
									</div>
								</div>
							</div>
						</div>
						<div class="form-row mt-3 appliedParagraphStyleRow">
							<div class="col-12">
								<b><fmtSpring:message code="page.label.applied.paragraph.style"/>:</b> <span id="appliedParagraphStyle"></span>
							</div>
						</div>
					</section>
				</div>
			</div>
		</div>
	</msgpt:BodyNew>
</msgpt:Html5>