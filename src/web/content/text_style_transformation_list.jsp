<%@ taglib prefix="msppt" uri="http://messagepoint.prinova.com/taglib" %>
<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@ page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.content" extendedScripts="true">
        <msppt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:CalendarIncludes/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>
        <msgpt:Script src="_ux/js/mp.complexCombobox.js"/>
        <msgpt:Script src="_ux/js/mp.toggleSwitch.js"/>
        <msgpt:Script src="_ux/js/mp.listbox.js"/>
        <msgpt:Script src="_ux/js/mp.postTrigger.js"/>

        <msgpt:Script>
            <script type="text/javascript">

                var $updateBtn, $deleteBtn;

                // *********  INIT: START  *********
                $(function () {
                    $updateBtn = $('#updateBtn');
                    $deleteBtn = $('#deleteBtn');

                    $('#addListStyleBtn').on('click', function () {

                        javascriptHref(context + '/content/text_style_transformation_edit.form');

                    });

                    $('#transformAllTouchpoints, #transformAllObjects').on('change', function () {
                        // Check if the checkbox is checked
                        if ($(this).is(':checked')) {
                            // If checked, hide the corresponding select
                            if ($(this).attr('id') === 'transformAllTouchpoints') {
                                $('#touchpointSelect').closest('.form-row').hide();
                            } else if ($(this).attr('id') === 'transformAllObjects') {
                                $('#objectTypeSelect').closest('.form-row').hide();
                            }
                        } else {
                            // If not checked, show the corresponding select
                            if ($(this).attr('id') === 'transformAllTouchpoints') {
                                $('#touchpointSelect').closest('.form-row').show();
                            } else if ($(this).attr('id') === 'transformAllObjects') {
                                $('#objectTypeSelect').closest('.form-row').show();
                            }
                        }
                    });

                });

                function rebuildListTable() {
                    $('#textStyleTransformationList').DataTable().ajax.reload(null, true);
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.columns = [
                        {
                            columnMap: 'name',
                            columnName: client_messages.text.name,
                            sort: true,
                            width: '90%',
                            colVisToggle: false
                        },
                        {
                            columnMap: 'type',
                            columnName: client_messages.text.type,
                            sort: true,
                            width: '10%',
                            colVisToggle: false
                        },
                    ];
                    return obj;
                }

                function getAsyncExtParams() {
                    var stampDate = new Date();
                    var obj = [
                        {"name": "listTableType", "value": "70"},
                        {"name": "displayMode", "value": "full"},
                        {"name": "cacheStamp", "value": stampDate.getTime()}
                    ];
                    return obj;
                }

                function postListRenderFlagInjection(oObj) {
                    var binding = oObj.aData.binding;
                    var text = oObj.aData.name;

                    //Selection check select
                    text += binding;

                    //Selection permission flags
                    if (oObj.aData.flags.canUpdate)
                        text += "<input type='hidden' id='canUpdate_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canDelete)
                        text += "<input type='hidden' id='canDelete_" + oObj.aData.dt_RowId + "' />";

                    return text;
                }

                // List actions: Edit
                function iFrameAction(actionId) {
                    var textStyleTransformationId;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        textStyleTransformationId = this.id.replace('listItemCheck_', '');
                    });

                    if (actionId == '1') {
                        javascriptHref(context + '/content/text_style_transformation_edit.form?textStyleTransformationProfileId=' + textStyleTransformationId);
                    }
                }

                function validateActionReq(textStyleTransformationId) {
                    var singleSelect = true;

                    var canUpdate = true;
                    var canDelete = true;

                    // Resolve selection flags
                    if ($("input[id^='listItemCheck_']:checked").length != 1)
                        singleSelect = false;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        var textStyleTransformationId = this.id.replace('listItemCheck_', '');
                        if (!exists('canUpdate_' + textStyleTransformationId))
                            canUpdate = false;
                        if (!exists('canDelete_' + textStyleTransformationId))
                            canDelete = false;
                    });

                    //Disable all context menu entries
                    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
                    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');
                    common.disableElement($updateBtn);
                    common.disableElement($deleteBtn);

                    //Enable context menu entries based on flag status
                    if (singleSelect) {
                        if (canUpdate) {
                            $('a#actioniFrame_1').removeClass('disabled');
                            common.enableElement($updateBtn);
                        }
                        if (canDelete) {
                            $('a#actionOption_3').removeClass('disabled');
                            common.enableElement($deleteBtn);
                        }
                        $('a#actionOption_4').removeClass('disabled'); // Apply transform
                    }
                }
            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>
    <msgpt:BodyNew>
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_CONTENTS %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_CONTENTS %>"/>
        <msgpt:ContextBarNew touchpointContextDisabled="true"/>
        <msgpt:LowerContainer fullPanel="true" extendedWidth="true">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="true">
                <msgpt:ContextMenu name="textStyleTransformationList">
                    <msgpt:ContextMenuEntry name="actioniFrame_1" link="#iFrameAction:1"><fmtSpring:message
                            code="page.label.edit"/></msgpt:ContextMenuEntry>
                    <msgpt:ContextMenuEntry name="actionOption_3" link="#actionSelected:3"><fmtSpring:message
                            code="page.label.delete"/></msgpt:ContextMenuEntry>
                    <c:if test="${styleTransformActionEnabled}">
                    <msgpt:ContextMenuEntry name="actionOption_4" link="#actionSelected:4"><fmtSpring:message
                            code="page.label.apply.transform"/></msgpt:ContextMenuEntry>
                    </c:if>
                </msgpt:ContextMenu>
                <form:form method="post" modelAttribute="command">
                    <form:errors path="*">
                        <msgpt:Information errorMsgs="${messages}" type="error"/>
                    </form:errors>
                    <c:if test="${not empty param.saveSuccess && param.saveSuccess == 'true'}">
                        <msgpt:Information type="success">
                            <fmtSpring:message code="page.label.save.complete"/>
                        </msgpt:Information>
                    </c:if>

                    <div align="center" style="padding: 12px; border-bottom: 1px solid #ddd;">
                        <table cellspacing="0" cellpadding="0" border="0">
                            <tr>
                                <td style="padding: 0px;">
                                    <div align="center" class="workflowTab workflowTabFirst"
                                         onclick="javascriptHref('text_style_list.form');">
                                        <div class="workflowTabText"><fmtSpring:message
                                                code="page.label.text.styles"/></div>
                                    </div>
                                </td>
                                <td style="padding: 0px;">
                                    <div align="center" class="workflowTab"
                                         onclick="javascriptHref('font_list.form');">
                                        <div class="workflowTabText"><fmtSpring:message code="page.label.fonts"/></div>
                                    </div>
                                </td>
                                <td style="padding: 0px;">
                                    <div align="center" class="workflowTabSelected workflowTabLast">
                                        <div class="workflowTabText"><fmtSpring:message code="page.label.transformation.profiles"/></div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="py-3">
                        <div class="box-shadow-4 rounded bg-white p-4">
                        <div class="px-2 py-1">
                            <div class="mb-4">
                                <div class="d-flex align-items-center pb-1">
                                    <form:hidden path="actionValue" id="actionElement"/>
                                    <msgpt:IfAuthGranted authority="ROLE_STYLES_UPDATE">
                                        <div class="mr-3">
                                            <button id="addListStyleBtn" class="btn btn-primary post-trigger" type="button">
                                                <i class="far fa-plus-circle mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="page.label.add"/>
                                            </button>
                                        </div>
                                    </msgpt:IfAuthGranted>
                                    <div class="btn-group border-separate mr-3" role="group"
                                         aria-label="${msgpt:getMessage("page.label.actions")}">
                                        <button id="updateBtn" type="button" class="btn btn-dark post-trigger"
                                                onclick="iFrameAction(1);" disabled>
                                            <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                            <fmtSpring:message code="action.button.label.update"/>
                                        </button>
                                        <button id="deleteBtn" type="button" class="btn btn-dark"
                                                onclick="actionSelected(3);" disabled>
                                            <i class="far fa-trash-alt mr-2" aria-hidden="true"></i>
                                            <fmtSpring:message code="page.label.delete"/>
                                        </button>
                                    </div>
                                    <div class="mr-auto"></div>
                                    <div class="form-group position-relative d-inline-block m-0">
                                        <label for="listSearchInput" class="sr-only"><fmtSpring:message
                                                code="page.label.search"/></label>
                                        <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                           style="z-index: 1;" aria-hidden="true"></i>
                                        <msgpt:InputFilter type="description">
                                            <input id="listSearchInput" type="text" size="25"
                                                   class="form-control bg-lightest has-control-l border-0"
                                                   placeholder="${msgpt:getMessage('page.label.search')}"/>
                                        </msgpt:InputFilter>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white">
                                <msgpt:DataTable id="textStyleTransformationList" listHeader="page.label.text.style.transformation.profile"
                                                 async="true" columnReorder="false" numUnreorderableCols="2"
                                                 columnVisibility="true" drillDown="fqlse" singleSelect="true"
                                                 searchFilter="true">
                                </msgpt:DataTable>
                            </div>
                        </div>
                    </div>
                    </div>
                    <!-- POPUP DATA -->
                    <div id="actionSpecs" style="display: none;">
                        <!-- ACTIONS POPUP DATA -->
                        <!-- Delete -->
                        <div id="actionSpec_3" type="simpleConfirm" submitId="3" contentWidth="410px">
                            <div id="actionTitle_3"><fmtSpring:message
                                    code="page.label.confirm.delete.text.style.transformation.profile"/></div>
                            <div id="actionInfo_3"><fmtSpring:message
                                    code="page.text.delete.selected.text.style.transformation.profile"/></div>
                        </div>
                        <div id="actionSpec_4" type="simpleConfirm" submitId="4" contentWidth="410px">
                            <div id="actionTitle_4"><fmtSpring:message
                                    code="page.label.apply.transform"/></div>
                            <div id="actionInfo_4"><fmtSpring:message
                                    code="page.text.apply.transform"/></div>
                            <div id="actionApplyTransform_4"></div>
                        </div>
                    </div>
                    <msgpt:Popup id="actionPopup" theme="minimal">
                        <div id="actionPopupInfoFrame">
                            <div id="actionPopupInfo">&nbsp;</div>
                        </div>
                        <div id="actionPopupApplyTransform">

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                                 <span class="form-label d-inline-block mb-2">
                                                     <fmtSpring:message code="page.label.all.touchpoints"/>
                                                </span>
                                    <div class="custom-control custom-switch mt-1">
                                        <form:checkbox
                                                id="transformAllTouchpoints"
                                                class="custom-control-input"
                                                path="transformAllTouchpoints"
                                                data-toggle="toggleswitch"
                                                data-uncheckedtext="${msgpt:getMessage('page.label.disabled')}"
                                                data-checkedtext="${msgpt:getMessage('page.label.enabled')}"
                                                disabled="false"/>
                                        <label class="custom-control-label font-weight-bold"
                                               for="transformAllTouchpoints">
                                            <fmtSpring:message code="page.label.enabled"/>
                                        </label>
                                    </div>
                                </div>

                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                                 <span class="form-label d-inline-block mb-2">
                                                     <fmtSpring:message code="page.label.touchpoints"/>
                                                </span>
                                    <div class="mt-1">
                                        <select id="touchpointSelect"
                                                name="transformTouchpoints"
                                                class="complex-dropdown-select"
                                                title="${msgpt:getMessage('page.label.touchpoint.s')}"
                                                aria-label="${msgpt:getMessage('page.label.touchpoints')}"
                                                data-toggle="complex-dropdown"
                                                data-enablefilter="true"
                                                data-enable-selectall="true"
                                                data-enable-tag-cloud="true"
                                                data-enable-view-selected="true"
                                                data-menu-class="dropdown-menu-left"
                                                data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                                data-show-titles="true"
                                                multiple>
                                            <c:forEach var="currentDocument" items="${documents}">
                                                <fmtSpring:bind path="command.transformTouchpoints">
                                                    <option id="touchpointOption_${currentDocument.id}"
                                                            class="ml-0 level_${currentDocument.projectDepth}" value="${currentDocument.id}"
                                                            data-filtervalue="${currentDocument.metatags}"
                                                        ${msgpt:contains( command.transformTouchpoints, currentDocument) ? 'selected="selected"': ''}>
                                                            ${currentDocument.name}
                                                    </option>
                                                </fmtSpring:bind>
                                            </c:forEach>
                                        </select>
                                    </div>
                                </div>

                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                                 <span class="form-label d-inline-block mb-2">
                                                     <fmtSpring:message code="page.label.all.objects"/>
                                                </span>
                                    <div class="custom-control custom-switch mt-1">
                                        <form:checkbox
                                                id="transformAllObjects"
                                                class="custom-control-input"
                                                path="transformAllObjects"
                                                data-toggle="toggleswitch"
                                                data-uncheckedtext="${msgpt:getMessage('page.label.disabled')}"
                                                data-checkedtext="${msgpt:getMessage('page.label.enabled')}"
                                                disabled="false"/>
                                        <label class="custom-control-label font-weight-bold"
                                               for="transformAllObjects">
                                            <fmtSpring:message code="page.label.enabled"/>
                                        </label>
                                    </div>
                                </div>

                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                                 <span class="form-label d-inline-block mb-2">
                                                     <fmtSpring:message code="page.label.objects"/>
                                                </span>
                                    <div class="mt-1">
                                        <select id="objectTypeSelect"
                                                name="transformObjects"
                                                class="complex-dropdown-select"
                                                title="${msgpt:getMessage('page.label.objects')}"
                                                aria-label="${msgpt:getMessage('page.label.objects')}"
                                                data-toggle="complex-dropdown"
                                                data-enablefilter="true"
                                                data-enable-selectall="false"
                                                data-enable-tag-cloud="false"
                                                data-enable-view-selected="true"
                                                data-menu-class="dropdown-menu-left"
                                                data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                                data-show-titles="true"
                                        >
                                            <fmtSpring:bind path="command.transformObjects">
                                                <option id="objectTypeOption_0" class="ml-0" value="0">
                                                    <fmtSpring:message code="page.label.messages"/>
                                                </option>
                                                <option id="objectTypeOption_1" class="ml-0" value="1">
                                                    <fmtSpring:message code="page.label.local.smart.text"/>
                                                </option>
                                                <option id="objectTypeOption_4" class="ml-0" value="4">
                                                    <fmtSpring:message code="page.label.smart.text"/>
                                                </option>
                                            </fmtSpring:bind>
                                        </select>
                                    </div>
                                </div>

                            </div>
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                                 <span class="form-label d-inline-block mb-2">
                                                     <fmtSpring:message code="page.label.status"/>
                                                </span>
                                    <div class="mt-1">
                                        <select id="statusSelect"
                                                name="transformObjectsStatus"
                                                class="complex-dropdown-select"
                                                title="${msgpt:getMessage('page.label.status')}"
                                                aria-label="${msgpt:getMessage('page.label.status')}"
                                                data-toggle="complex-dropdown"
                                                data-enablefilter="true"
                                                data-enable-selectall="false"
                                                data-enable-tag-cloud="false"
                                                data-enable-view-selected="true"
                                                data-menu-class="dropdown-menu-left"
                                                data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                                data-show-titles="true"
                                        >
                                            <fmtSpring:bind path="command.transformObjectsStatus">
                                                <option id="objectTypeOption_1" class="ml-0" value="1">
                                                    <fmtSpring:message code="page.label.working.copy"/>
                                                </option>
                                                <option id="objectTypeOption_2" class="ml-0" value="2">
                                                    <fmtSpring:message code="page.label.active"/>
                                                </option>
                                                <option id="objectTypeOption_3" class="ml-0" value="3">
                                                    <fmtSpring:message code="page.label.working.copy.active"/>
                                                </option>
                                                <option id="objectTypeOption_7" class="ml-0" value="7">
                                                    <fmtSpring:message code="page.label.working.copy.active.archive"/>
                                                </option>
                                            </fmtSpring:bind>
                                        </select>
                                    </div>
                                </div>

                            </div>

                        </div>
                        <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                            <span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                   label="page.label.cancel"
                                                                                   disabled="true"/></span>
                            <span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                      label="page.label.cancel"/></span>
                            <span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                     label="page.label.continue"
                                                                                     disabled="true"/></span>
                            <span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue"
                                                                        primary="true"/></span>
                        </div>
                    </msgpt:Popup>

                </form:form>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
    </msgpt:BodyNew>
</msgpt:Html5>