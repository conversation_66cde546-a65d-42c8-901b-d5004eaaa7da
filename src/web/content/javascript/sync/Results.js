var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var _this = this;
var ObjectType;
(function (ObjectType) {
    ObjectType[ObjectType["all"] = 0] = "all";
    ObjectType[ObjectType["message"] = 1] = "message";
    ObjectType[ObjectType["zone"] = 2] = "zone";
    ObjectType[ObjectType["section"] = 3] = "section";
    ObjectType[ObjectType["variant"] = 4] = "variant";
    ObjectType[ObjectType["localSmartText"] = 5] = "localSmartText";
    ObjectType[ObjectType["localImage"] = 6] = "localImage";
    ObjectType[ObjectType["localSmartCanvas"] = 7] = "localSmartCanvas";
    ObjectType[ObjectType["document"] = 8] = "document";
    ObjectType[ObjectType["smartCanvas"] = 9] = "smartCanvas";
    ObjectType[ObjectType["smartText"] = 10] = "smartText";
    ObjectType[ObjectType["contentLibrary"] = 11] = "contentLibrary";
    ObjectType[ObjectType["targetGroup"] = 12] = "targetGroup";
    ObjectType[ObjectType["targetRule"] = 13] = "targetRule";
    ObjectType[ObjectType["parameterGroup"] = 14] = "parameterGroup";
    ObjectType[ObjectType["variable"] = 15] = "variable";
    ObjectType[ObjectType["documentSettings"] = 16] = "documentSettings";
    ObjectType[ObjectType["dataSource"] = 17] = "dataSource";
    ObjectType[ObjectType["dataCollection"] = 18] = "dataCollection";
    ObjectType[ObjectType["lookupTable"] = 19] = "lookupTable";
    //    textStyle = 20,
    //    paragraphStyle = 21,
    //    listStyle = 22,
    ObjectType[ObjectType["metadataFormDefinition"] = 23] = "metadataFormDefinition";
    ObjectType[ObjectType["compositionPackage"] = 24] = "compositionPackage";
    ObjectType[ObjectType["textStyle"] = 25] = "textStyle";
    ObjectType[ObjectType["dataFile"] = 40] = "dataFile";
    ObjectType[ObjectType["textStyleFont"] = 26] = "textStyleFont";
    ObjectType[ObjectType["listStyle"] = 27] = "listStyle";
    ObjectType[ObjectType["paragraphStyle"] = 28] = "paragraphStyle";
    ObjectType[ObjectType["dataResource"] = 41] = "dataResource";
    ObjectType[ObjectType["channelConfiguration"] = 52] = "channelConfiguration";
})(ObjectType || (ObjectType = {}));
var ObjectTypeDropDown = [
    { id: ObjectType.message, name: client_messages.sync.message },
    { id: ObjectType.localSmartText, name: client_messages.sync.local_smart_text },
    { id: ObjectType.smartText, name: client_messages.sync.smart_text },
    { id: ObjectType.contentLibrary, name: client_messages.sync.image_library },
    { id: ObjectType.localImage, name: client_messages.sync.local_image_library },
    { id: ObjectType.variant, name: client_messages.sync.variant },
    { id: ObjectType.targetGroup, name: client_messages.sync.target_group },
    { id: ObjectType.targetRule, name: client_messages.sync.target_rule },
    { id: ObjectType.dataSource, name: client_messages.sync.data_source },
    { id: ObjectType.dataCollection, name: client_messages.sync.data_collection },
    { id: ObjectType.lookupTable, name: client_messages.sync.lookup_table },
    { id: ObjectType.parameterGroup, name: client_messages.sync.selector_group },
    { id: ObjectType.variable, name: client_messages.sync.variable },
    { id: ObjectType.metadataFormDefinition, name: client_messages.sync.metadata_template },
    { id: ObjectType.documentSettings, name: client_messages.sync.touchpoint_setting },
    { id: ObjectType.compositionPackage, name: client_messages.sync.composition_package },
    { id: ObjectType.textStyle, name: client_messages.sync.text_style },
    { id: ObjectType.dataFile, name: client_messages.sync.data_file },
    { id: ObjectType.textStyleFont, name: client_messages.sync.text_style_font },
    { id: ObjectType.dataResource, name: client_messages.sync.data_resource },
    { id: ObjectType.channelConfiguration, name: client_messages.sync.channel_configuration },
    { id: ObjectType.listStyle, name: client_messages.sync.list_style },
    { id: ObjectType.paragraphStyle, name: client_messages.sync.paragraph_style },
];
var FilterStatusType;
(function (FilterStatusType) {
    FilterStatusType[FilterStatusType["all"] = 0] = "all";
    FilterStatusType[FilterStatusType["noConflict"] = 1] = "noConflict";
    FilterStatusType[FilterStatusType["conflict"] = 2] = "conflict";
    FilterStatusType[FilterStatusType["rejected"] = 3] = "rejected";
    FilterStatusType[FilterStatusType["selected"] = 4] = "selected";
    FilterStatusType[FilterStatusType["notSelected"] = 5] = "notSelected";
})(FilterStatusType || (FilterStatusType = {}));
var SyncProcessStage;
(function (SyncProcessStage) {
    SyncProcessStage[SyncProcessStage["init"] = 0] = "init";
    SyncProcessStage[SyncProcessStage["start"] = 1] = "start";
    SyncProcessStage[SyncProcessStage["syncing"] = 2] = "syncing";
    SyncProcessStage[SyncProcessStage["finishedSyncing"] = 3] = "finishedSyncing";
    SyncProcessStage[SyncProcessStage["updating"] = 4] = "updating";
    SyncProcessStage[SyncProcessStage["completed"] = 5] = "completed";
    SyncProcessStage[SyncProcessStage["error"] = 6] = "error";
})(SyncProcessStage || (SyncProcessStage = {}));
var endpoints = {
    START_GET_SYNC_LIST: '../startGetSyncList.form',
    GET_SYNC_LIST_STATUS: '../getSyncListStatus.form',
    GET_SYNC_LIST: '../getSyncList.form',
    GET_SYNC_PROCESS_STATUS: '../getSyncProcessStatus.form',
    INIT_SYNC_PROCESS: '../initSyncProcess.form',
    INIT_SYNC_PRIORITY: '../initSyncPriority.form',
    INIT_DATA_EXPORT_PROCESS: '../initExportSyncDataProcess.form',
    GET_DATA_EXPORT_PROCESS_STATUS: '../getExportSyncDataProcessStatus.form',
    GET_SYNC_OBJECT_DETAIL_VIEW: '../getSyncObjectDetailView.form'
};
var syncListStage = {
    INIT: 'init',
    SEARCHING: 'searching',
    COMPLETED: 'completed',
    ERROR: 'error',
    UNAVAILABLE: 'unavailable',
};
var sortKeys = {
    OBJECT_NAME: 'objectName',
    OBJECT_TYPE: 'objectType',
    VARIANT_NAME: 'variantName',
    STATUS: 'status',
    DEPENDENCIES: 'dependencies',
};
var sortDirections = {
    ASCENDING: 'asc',
    DESCENDING: 'desc',
};
var defaults = {
    pageIndex: 1,
    pageSize: 10,
    sortKey: sortKeys.OBJECT_NAME,
    sortDirection: 'asc',
    searchFilter: '',
    objectTypeFilter: ObjectType.all,
    statusTypeFilter: FilterStatusType.all,
    projectNameFilter: null,
};
var Results = function (_a) {
    var visible = _a.visible, documentDataResponse = _a.documentDataResponse, syncDocumentsConfig = _a.syncDocumentsConfig, direction = _a.direction, languages = _a.languages, settings = _a.settings, setSettings = _a.setSettings, compareData = _a.compareData, setCompareData = _a.setCompareData, showContentCompare = _a.showContentCompare, setShowContentCompare = _a.setShowContentCompare, syncObjectToCompare = _a.syncObjectToCompare, setSyncObjectToCompare = _a.setSyncObjectToCompare;
    var _b = React.useState(false), isGeneratingList = _b[0], setIsGeneratingList = _b[1];
    var _c = React.useState(false), isProcessingList = _c[0], setIsProcessingList = _c[1];
    var _d = React.useState(false), isDataExtracting = _d[0], setIsDataExtracting = _d[1];
    var _e = React.useState(false), showSyncListLoadingProgress = _e[0], setShowSyncListLoadingProgress = _e[1];
    var _f = React.useState(false), showSyncList = _f[0], setShowSyncList = _f[1];
    var _g = React.useState(false), showSyncProcessProgress = _g[0], setShowSyncProcessProgress = _g[1];
    var _h = React.useState(false), showNoDataInfo = _h[0], setShowNoDataInfo = _h[1];
    var _j = React.useState(false), listHasData = _j[0], setListHasData = _j[1];
    var _k = React.useState(false), showGenerateListConfirmation = _k[0], setShowGenerateListConfirmation = _k[1];
    var _l = React.useState(false), showDataExportConfirmation = _l[0], setShowDataExportConfirmation = _l[1];
    var _m = React.useState(defaults.pageIndex), pageIndex = _m[0], setPageIndex = _m[1];
    var _o = React.useState(defaults.sortKey), sortKey = _o[0], setSortKey = _o[1];
    var _p = React.useState(defaults.sortDirection), sortDirection = _p[0], setSortDirection = _p[1];
    var _q = React.useState(defaults.searchFilter), textFilter = _q[0], setTextFilter = _q[1];
    var _r = React.useState(defaults.objectTypeFilter), objectTypeFilter = _r[0], setObjectTypeFilter = _r[1];
    var _s = React.useState(defaults.projectNameFilter), projectNameFilter = _s[0], setProjectNameFilter = _s[1];
    var _t = React.useState(null), startGetSyncListResponse = _t[0], setStartGetSyncListResponse = _t[1];
    var _u = React.useState(null), syncListFetchPromise = _u[0], setSyncListFetchPromise = _u[1];
    var _v = React.useState(null), guidSync = _v[0], setGuidSync = _v[1];
    var _w = React.useState(false), showInitSyncProcessConfirmation = _w[0], setShowInitSyncProcessConfirmation = _w[1];
    var _x = React.useState(false), isSyncing = _x[0], setIsSyncing = _x[1];
    var _y = React.useState(false), isRequestingSync = _y[0], setIsRequestingSync = _y[1];
    var _z = React.useState(null), syncTaskGuid = _z[0], setSyncTaskGuid = _z[1];
    var _0 = React.useState(null), syncStatusResponse = _0[0], setSyncStatusResponse = _0[1];
    var _1 = React.useState(false), showInitSyncPrioritiesProcessConfirmation = _1[0], setShowInitSyncPrioritiesProcessConfirmation = _1[1];
    var _2 = React.useState(false), showInitHideChangesProcessConfirmation = _2[0], setShowInitHideChangesProcessConfirmation = _2[1];
    var _3 = React.useState(false), isSyncingPriorities = _3[0], setIsSyncingPriorities = _3[1];
    var _4 = React.useState(null), syncPrioritiesResponse = _4[0], setSyncPrioritiesResponse = _4[1];
    var _5 = React.useState(null), syncPrioritiesTaskGuid = _5[0], setSyncPrioritiesTaskGuid = _5[1];
    var _6 = React.useState(false), showDependenciesListModal = _6[0], setShowDependenciesListModal = _6[1];
    var _7 = React.useState(null), dependenciesToDisplay = _7[0], setDependenciesToDisplay = _7[1];
    var _8 = React.useState(false), showReferencesListModal = _8[0], setShowReferencesListModal = _8[1];
    var _9 = React.useState(null), referencesToDisplay = _9[0], setReferencesToDisplay = _9[1];
    var _10 = React.useState(null), syncListTable = _10[0], setSyncListTable = _10[1];
    var _11 = React.useState(false), refreshSyncListTable = _11[0], setRefreshSyncListTable = _11[1];
    var _12 = React.useState(false), refreshSelectedTabPage = _12[0], setRefreshSelectedTabPage = _12[1];
    var _13 = React.useState(null), selectedCount = _13[0], setSelectedCount = _13[1];
    var _14 = React.useState(0), deleteWcDueToSyncActiveOnly = _14[0], setDeleteWcDueToSyncActiveOnly = _14[1];
    var _15 = React.useState({ rows: {} }), rejectIds = _15[0], setRejectIds = _15[1];
    var _16 = React.useState(null), rejectedCount = _16[0], setRejectedCount = _16[1];
    var _17 = React.useContext(SyncContext), resultsInfo = _17.resultsInfo, setResultsInfo = _17.setResultsInfo, statusTypeFilter = _17.statusTypeFilter, setStatusTypeFilter = _17.setStatusTypeFilter, tableId = _17.tableId;
    var _18 = React.useState(null), dataExportFileName = _18[0], setDataExportFileName = _18[1];
    var _19 = React.useState(null), dataExportResource = _19[0], setDataExportResource = _19[1];
    var _20 = React.useState(null), syncDataExtractTaskGuid = _20[0], setSyncDataExtractTaskGuid = _20[1];
    var _21 = React.useState(false), syncDataExtractTaskStatus = _21[0], setSyncDataExtractTaskStatus = _21[1];
    var getLocaleMismatchError = React.useContext(SyncContext).getLocaleMismatchError;
    var _22 = React.useState(false), showSaveAsMenu = _22[0], setShowSaveAsMenu = _22[1];
    var isGenerateDisabled = function () {
        var localeMismatchError = getLocaleMismatchError(documentDataResponse, syncDocumentsConfig, syncDocumentsConfig.selectableDocument.selectedInstanceId, syncDocumentsConfig.selectableDocument.selectedSiblingId);
        return localeMismatchError != undefined && localeMismatchError.length > 0;
    };
    var getObjectTypeFilterName = function (filterObjectType) {
        switch (filterObjectType) {
            case ObjectType.all:
                return client_messages.sync.all_objects;
            case ObjectType.message:
                return client_messages.sync.message;
            case ObjectType.zone:
                return client_messages.sync.zone;
            case ObjectType.section:
                return client_messages.sync.section;
            case ObjectType.variant:
                return client_messages.sync.variant;
            case ObjectType.localSmartText:
                return client_messages.sync.local_smart_text;
            case ObjectType.localImage:
                return client_messages.sync.local_image_library;
            case ObjectType.localSmartCanvas:
                return client_messages.sync.local_smart_canvas;
            case ObjectType.document:
                return client_messages.sync.document;
            case ObjectType.smartCanvas:
                return client_messages.sync.smart_canvas;
            case ObjectType.smartText:
                return client_messages.sync.smart_text;
            case ObjectType.contentLibrary:
                return client_messages.sync.image_library;
            case ObjectType.targetGroup:
                return client_messages.sync.target_group;
            case ObjectType.targetRule:
                return client_messages.sync.target_rule;
            case ObjectType.parameterGroup:
                return client_messages.sync.selector_group;
            case ObjectType.variable:
                return client_messages.sync.variable;
            case ObjectType.documentSettings:
                return client_messages.sync.touchpoint_setting;
            case ObjectType.dataSource:
                return client_messages.sync.data_source;
            case ObjectType.dataCollection:
                return client_messages.sync.data_collection;
            case ObjectType.lookupTable:
                return client_messages.sync.lookup_table;
            case ObjectType.textStyle:
                return client_messages.sync.text_style;
            case ObjectType.textStyleFont:
                return client_messages.sync.text_style_font;
            case ObjectType.paragraphStyle:
                return client_messages.sync.paragraph_style;
            case ObjectType.listStyle:
                return client_messages.sync.list_style;
            case ObjectType.paragraphStyle:
                return client_messages.sync.paragraph_style;
            case ObjectType.metadataFormDefinition:
                return client_messages.sync.metadata_template;
            case ObjectType.compositionPackage:
                return client_messages.sync.composition_package;
            case ObjectType.dataFile:
                return client_messages.sync.data_file;
            case ObjectType.dataResource:
                return client_messages.sync.data_resource;
            case ObjectType.channelConfiguration:
                return client_messages.sync.channel_configuration;
            default:
                return client_messages.sync.unknown_entry;
        }
    };
    var getSortKeyName = function (sortKey) {
        switch (sortKey) {
            case sortKeys.OBJECT_NAME:
                return client_messages.sync.object_name;
            case sortKeys.OBJECT_TYPE:
                return client_messages.sync.object_type;
            case sortKeys.VARIANT_NAME:
                return client_messages.sync.variant_name;
            case sortKeys.STATUS:
                return client_messages.sync.status;
            case sortKeys.DEPENDENCIES:
                return client_messages.sync.dependencies;
            default:
                return client_messages.sync.unknown_entry;
        }
    };
    var getCanBeSyncedByAssignedUser = function (forceSync, item) {
        if (forceSync)
            return true;
        if (item.isAssignedToCurrentUser != null)
            return item.isAssignedToCurrentUser;
        return true;
    };
    var hasImpactOnDependencies = function (item) {
        if (item.impactTargetDependencies != null) {
            return item.impactTargetDependencies;
        }
        return false;
    };
    var isNotSyncable = function (item) {
        if (item.notSyncable != null) {
            return item.notSyncable;
        }
        return false;
    };
    var getAffectedObjects = function (item) {
        if (item.affectedObjects != null) {
            if (item.affectedObjects.length > 0) {
                return item.affectedObjects;
            }
        }
        return Array();
    };
    var getAutoAccepts = function (item) {
        if (item.autoAccepts != null) {
            return item.autoAccepts;
        }
        return Array();
    };
    var getAutUnaccepts = function (item) {
        if (item.autoUnaccepts != null) {
            return item.autoUnaccepts;
        }
        return Array();
    };
    var getCanBeSyncedByReferences = function (item) {
        var working = item.status['source'];
        var active = item.status['sourceActiveCopy'];
        var notSyncable = item.notSyncable != null && item.notSyncable;
        if (notSyncable)
            return false;
        return !(item.targetDependencies && item.targetDependencies.isReferenced &&
            ((working && working.id === StatusType.deleted) || (active && (active.id === StatusType.archive || active.id === StatusType.deleted))));
    };
    var getCanBeRejected = function (forceSync, item) {
        var excludeFromRejection = item.excludeFromRejection;
        if (excludeFromRejection)
            return false;
        if (forceSync)
            return true;
        if (item.isAssignedToCurrentUser != null)
            return item.isAssignedToCurrentUser;
        return true;
    };
    var objectCanBeSynced = function (forceSync, item) {
        var notSyncable = item.notSyncable != null && item.notSyncable;
        if (notSyncable)
            return false;
        return (getCanBeSyncedByAssignedUser(forceSync, item) && getCanBeSyncedByReferences(item));
    };
    var objectCanBeRejected = function (forceSync, item) {
        return (getCanBeSyncedByAssignedUser(forceSync, item) && getCanBeRejected(forceSync, item));
    };
    var getSyncListStageName = function (stage) {
        switch (stage) {
            case syncListStage.INIT:
                return client_messages.sync.initializing_list;
            case syncListStage.SEARCHING:
                return client_messages.sync.finding_changes;
            case syncListStage.COMPLETED:
                return client_messages.sync.completed;
            case syncListStage.ERROR:
                return client_messages.sync.error_generating_list;
            case syncListStage.UNAVAILABLE:
                return client_messages.sync.error_unavailable;
            default:
                return client_messages.sync.unknown_entry;
        }
    };
    var getSyncProcessStageName = function (stage) {
        switch (stage) {
            case SyncProcessStage.init:
                return client_messages.sync.initializing_sync_process;
            case SyncProcessStage.start:
                return client_messages.sync.start_processing;
            case SyncProcessStage.syncing:
                return client_messages.sync.syncing;
            case SyncProcessStage.finishedSyncing:
                return client_messages.sync.finished_syncing;
            case SyncProcessStage.updating:
                return client_messages.sync.updating_document;
            case SyncProcessStage.completed:
                return client_messages.sync.sync_completed;
            case SyncProcessStage.error:
                return client_messages.sync.error_syncing;
            default:
                return client_messages.sync.unknown_entry;
        }
    };
    var getSelectedLanguages = function () {
        if (languages.length < 1) {
            return syncDocumentsConfig.selectableDocument.tree
                .filter(function (node) { return node.selected; })[0].languages;
        }
        return languages;
    };
    var getLanguageIds = function (list) {
        return list.map(function (lang) { return lang.id; }).join();
    };
    var getFilters = function () {
        var result = new URLSearchParams();
        if (textFilter)
            result.append('filterText', textFilter);
        if (statusTypeFilter)
            result.append('statusType', statusTypeFilter.toString());
        if (objectTypeFilter)
            result.append('objectType', objectTypeFilter.toString());
        if (projectNameFilter)
            result.append('projectName', projectNameFilter);
        return result;
    };
    var getUniqueId = function () {
        var dt = new Date().getTime();
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = (dt + Math.random() * 16) % 16 | 0;
            dt = Math.floor(dt / 16);
            return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
        });
    };
    var getListFetchHandle = function (params, filters, newFetch) { return __awaiter(_this, void 0, void 0, function () {
        var customParams, objectIds, selectedIdsArray, rejectIdsArray, queryString;
        return __generator(this, function (_a) {
            customParams = newFetch ? new URLSearchParams() : new URLSearchParams(filters);
            objectIds = null;
            setPageIndex(params.pageIndex);
            customParams.append('pageIndex', pageIndex < 1 ? defaults.pageIndex.toString() : params.pageIndex.toString());
            customParams.append('pageSize', params.pageSize.toString());
            customParams.append('sortKey', newFetch ? defaults.sortKey : sortKey);
            customParams.append('sortDirection', newFetch ? defaults.sortDirection : sortDirection);
            customParams.append('sourceDocumentId', resultsInfo.sourceDocumentId.toString());
            customParams.append('sourceInstanceId', resultsInfo.sourceInstanceId.toString());
            customParams.append('targetDocumentId', resultsInfo.targetDocumentId.toString());
            customParams.append('targetInstanceId', resultsInfo.targetInstanceId.toString());
            customParams.append('acceptAll', 'false');
            selectedIdsArray = syncListTable ? syncListTable.getSelectedIds() : [];
            rejectIdsArray = Object.keys(rejectIds.rows);
            if (statusTypeFilter === FilterStatusType.selected) {
                objectIds = { objectIds: selectedIdsArray };
            }
            else if (statusTypeFilter === FilterStatusType.rejected) {
                objectIds = { objectIds: rejectIdsArray };
            }
            else if (statusTypeFilter === FilterStatusType.notSelected) {
                objectIds = { objectIds: selectedIdsArray.concat(rejectIdsArray) };
            }
            customParams.append('languages', getLanguageIds(resultsInfo.selectedLanguages));
            customParams.append('forceSync', settings.forceSync.toString());
            customParams.append('activeOnly', resultsInfo.activeOnly.toString());
            customParams.append('syncActiveOnly', resultsInfo.syncActiveOnly.toString());
            customParams.append('syncMessagePriority', settings.syncMessagePriority.toString());
            customParams.append('includeDifferencesInLog', settings.includeDifferencesInLog.toString());
            customParams.append('includeNotVisibleTargetRules', settings.includeNotVisibleTargetRules.toString());
            customParams.append('includeRejectedChanges', settings.includeRejectedChanges.toString());
            customParams.append('notSyncLayoutChanges', settings.notSyncLayoutChanges.toString());
            customParams.append('listKey', startGetSyncListResponse.key);
            customParams.append('guidSync', guidSync);
            customParams.append('tk', gup('tk'));
            queryString = customParams.toString();
            return [2 /*return*/, getFetchPromise("".concat(params.apiService, "?").concat(queryString), {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json; charset=utf-8' },
                    body: objectIds ? JSON.stringify(objectIds) : null,
                })];
        });
    }); };
    var getListFetch = function (params, newFetch) {
        return getListFetchHandle(params, getFilters(), newFetch);
    };
    var listPrefetchHandler = function (params) {
        var response = syncListFetchPromise;
        if (response) {
            setSyncListFetchPromise(null);
            return response;
        }
        return getListFetch(params, false);
    };
    var preloadInitialData = function (newFetch) { return __awaiter(_this, void 0, void 0, function () {
        var params, newResultsInfo, guid, queryString, result, response;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    params = new URLSearchParams();
                    newResultsInfo = resultsInfo;
                    if (newFetch) {
                        newResultsInfo = {
                            sourceInstanceId: null,
                            sourceInstance: null,
                            sourceDocumentId: null,
                            sourceDocumentName: null,
                            targetInstanceId: null,
                            targetInstance: null,
                            targetDocumentId: null,
                            targetDocumentName: null,
                            selectedLanguages: getSelectedLanguages(),
                            activeOnly: settings.activeOnly,
                            syncActiveOnly: false,
                            direction: direction,
                        };
                        if (syncDocumentsConfig.direction === Direction.update) {
                            newResultsInfo.sourceInstanceId = syncDocumentsConfig.selectableDocument.selectedInstanceId;
                            newResultsInfo.sourceInstance = syncDocumentsConfig.selectableDocument.selectedInstanceName;
                            newResultsInfo.sourceDocumentId = syncDocumentsConfig.selectableDocument.selectedSiblingId;
                            newResultsInfo.sourceDocumentName = syncDocumentsConfig.selectableDocument.selectedSiblingName;
                            newResultsInfo.targetInstanceId = syncDocumentsConfig.currentDocument.instanceId;
                            newResultsInfo.targetInstance = syncDocumentsConfig.currentDocument.instanceName;
                            newResultsInfo.targetDocumentId = syncDocumentsConfig.currentDocument.documentId;
                            newResultsInfo.targetDocumentName = syncDocumentsConfig.currentDocument.documentName;
                            newResultsInfo.syncActiveOnly = syncDocumentsConfig.currentDocument.acceptOnlyActiveObjects;
                        }
                        else {
                            newResultsInfo.sourceInstanceId = syncDocumentsConfig.currentDocument.instanceId;
                            newResultsInfo.sourceInstance = syncDocumentsConfig.currentDocument.instanceName;
                            newResultsInfo.sourceDocumentId = syncDocumentsConfig.currentDocument.documentId;
                            newResultsInfo.sourceDocumentName = syncDocumentsConfig.currentDocument.documentName;
                            newResultsInfo.targetInstanceId = syncDocumentsConfig.selectableDocument.selectedInstanceId;
                            newResultsInfo.targetInstance = syncDocumentsConfig.selectableDocument.selectedInstanceName;
                            newResultsInfo.targetDocumentId = syncDocumentsConfig.selectableDocument.selectedSiblingId;
                            newResultsInfo.targetDocumentName = syncDocumentsConfig.selectableDocument.selectedSiblingName;
                            newResultsInfo.syncActiveOnly = syncDocumentsConfig.selectableDocument.acceptOnlyActiveObjects;
                        }
                        setResultsInfo(newResultsInfo);
                    }
                    params.append('pageIndex', defaults.pageIndex.toString());
                    params.append('pageSize', defaults.pageSize.toString());
                    params.append('sortKey', sortKey);
                    params.append('sortDirection', sortDirection);
                    params.append('sourceDocumentId', newResultsInfo.sourceDocumentId.toString());
                    params.append('sourceInstanceId', newResultsInfo.sourceInstanceId.toString());
                    params.append('targetDocumentId', newResultsInfo.targetDocumentId.toString());
                    params.append('targetInstanceId', newResultsInfo.targetInstanceId.toString());
                    params.append('languages', getLanguageIds(newResultsInfo.selectedLanguages));
                    params.append('forceSync', settings.forceSync.toString());
                    params.append('activeOnly', newResultsInfo.activeOnly.toString());
                    params.append('syncActiveOnly', newResultsInfo.syncActiveOnly.toString());
                    params.append('syncMessagePriority', settings.syncMessagePriority.toString());
                    params.append('includeDifferencesInLog', settings.includeDifferencesInLog.toString());
                    params.append('includeNotVisibleTargetRules', settings.includeNotVisibleTargetRules.toString());
                    params.append('includeRejectedChanges', settings.includeRejectedChanges.toString());
                    params.append('notSyncLayoutChanges', settings.notSyncLayoutChanges.toString());
                    guid = getUniqueId();
                    params.append('guidSync', guid);
                    setGuidSync(guid);
                    params.append('tk', gup('tk'));
                    queryString = params.toString();
                    setIsGeneratingList(true);
                    setDependenciesToDisplay(null);
                    setReferencesToDisplay(null);
                    return [4 /*yield*/, getFetchPromise("".concat(endpoints.START_GET_SYNC_LIST, "?").concat(queryString))];
                case 1:
                    result = _a.sent();
                    return [4 /*yield*/, result.json()];
                case 2:
                    response = _a.sent();
                    setStartGetSyncListResponse(response);
                    setRejectIds(function () { return ({ rows: {} }); });
                    setIsProcessingList(true);
                    setShowSyncListLoadingProgress(true);
                    setShowSyncProcessProgress(false);
                    setShowSyncList(false);
                    return [2 /*return*/];
            }
        });
    }); };
    var loadInitialData = function (newFetch) { return __awaiter(_this, void 0, void 0, function () {
        var promise;
        return __generator(this, function (_a) {
            promise = getListFetch({
                apiService: endpoints.GET_SYNC_LIST,
                pageIndex: defaults.pageIndex,
                pageSize: defaults.pageSize,
                sortDirection: sortDirection,
                sortKey: sortKey,
            }, newFetch);
            if (newFetch) {
                setRefreshSyncListTable(false);
                setSortKey(defaults.sortKey);
                setSortDirection(defaults.sortDirection);
                setTextFilter(defaults.searchFilter);
                setObjectTypeFilter(defaults.objectTypeFilter);
                setStatusTypeFilter(defaults.statusTypeFilter);
                setProjectNameFilter(defaults.projectNameFilter);
            }
            setSyncListFetchPromise(promise);
            setShowNoDataInfo(false);
            setListHasData(true);
            setIsGeneratingList(false);
            setIsProcessingList(false);
            setShowSyncListLoadingProgress(false);
            setShowSyncList(true);
            setSyncListTable(AsyncJsonTable.getInstance(tableId));
            return [2 /*return*/];
        });
    }); };
    var handleGenerateList = function (confirm) {
        if (confirm)
            setShowGenerateListConfirmation(true);
        else {
            preloadInitialData(true);
        }
        setIsRequestingSync(false);
        setIsSyncing(false);
        setSyncTaskGuid(null);
    };
    var handleGenerateListConfirmation = function () {
        preloadInitialData(true);
    };
    var handleGenerateListConfirmationModalClose = function () {
        setShowGenerateListConfirmation(false);
    };
    var handleSyncPriorityChange = function (event) {
        setSettings(__assign(__assign({}, settings), { syncMessagePriority: event.currentTarget.checked }));
    };
    /* ---------------- */
    var getTransformedObjectId = function (objectType, id, statusCode) {
        return "".concat(objectType, "_").concat(id, "_").concat(statusCode);
    };
    var isDependencyMandatory = function (item, syncObject) {
        if (syncObject === void 0) { syncObject = undefined; }
        var isEligibleStatus = function (status) {
            var sourceWC = status['source'];
            var sourceAC = status['sourceActiveCopy'];
            var targetWC = status['target'];
            var targetAC = status['targetActiveCopy'];
            if (targetWC.id === StatusType.notApplicable && (!targetAC || (targetAC && targetAC.id === StatusType.notApplicable))) {
                if (sourceWC.id === StatusType.new)
                    return true;
                else if (sourceAC)
                    return sourceAC.id === StatusType.new;
            }
            return false;
        };
        var isMandatory = false;
        if (item.objectType === ObjectType.textStyle || item.objectType === ObjectType.dataCollection) {
            isMandatory = true;
        }
        else if (item.objectType === ObjectType.channelConfiguration && syncObject != undefined) {
            isMandatory = isEligibleStatus(syncObject.status);
        }
        else {
            isMandatory = isEligibleStatus(item.status);
        }
        return isMandatory;
    };
    var GetAssociatedDependencies = function (item) {
        var deps = [];
        if (item.sourceDependencies.hasDependencies && item.sourceDependencies.needExisting) {
            item.sourceDependencies.needExisting.forEach(function (dep, index) {
                if (!dep.noAction) {
                    if (isDependencyMandatory(dep, item))
                        deps.push(dep);
                }
            });
        }
        return deps;
    };
    var GetAssociatedReferences = function (item) {
        var refs = [];
        if (item.targetDependencies && item.targetDependencies.isReferenced) {
            item.targetDependencies.objects.forEach(function (ref, index) {
                if (ref.statusCode !== undefined) {
                    refs.push(ref);
                }
            });
        }
        return refs;
    };
    var updateSyncListSelectedObjectsIds = function (select, selectedObjectIds, actionObjects) {
        var rows = {};
        if (select) {
            var autoAccepts_1 = Array();
            startGetSyncListResponse.data.forEach(function (item) {
                var soObjId = getTransformedObjectId(item.objectType, item.id, item.statusCode);
                if (selectedObjectIds.indexOf(soObjId) >= 0) {
                    if (item.hasOwnProperty("autoAccepts") && item.autoAccepts != null) {
                        autoAccepts_1.push.apply(autoAccepts_1, item.autoAccepts);
                    }
                }
            });
            selectedObjectIds.push.apply(selectedObjectIds, autoAccepts_1);
            var rejectedRows_1 = Object.keys(rejectIds.rows)
                .filter(function (key) { return selectedObjectIds.indexOf(key) < 0; })
                .reduce(function (target, key) {
                var _a;
                return Object.assign(target, (_a = {}, _a[key] = syncListTable.state.selectedRows[key], _a));
            }, {});
            setRejectIds(function () { return ({ rows: rejectedRows_1 }); });
            selectedObjectIds.forEach(function (item) {
                rows[item] = true;
            });
            syncListTable.setState(function (prevState) { return ({
                selectedRows: __assign(__assign({}, prevState.selectedRows), rows)
            }); });
        }
        else {
            var autoUnaccepts_1 = Array();
            startGetSyncListResponse.data.forEach(function (item) {
                var soObjId = getTransformedObjectId(item.objectType, item.id, item.statusCode);
                if (selectedObjectIds.indexOf(soObjId) >= 0) {
                    if (item.hasOwnProperty("autoUnaccepts") && item.autoUnaccepts != null) {
                        autoUnaccepts_1.push.apply(autoUnaccepts_1, item.autoUnaccepts);
                    }
                }
            });
            selectedObjectIds.push.apply(selectedObjectIds, autoUnaccepts_1);
            /*
                        let syncObjects = startGetSyncListResponse.data;
                        actionObjects.forEach((action) => {
                            syncObjects.forEach((item) => {
                                let objectDeps = GetAssociatedDependencies(item);
                                objectDeps.forEach((dep) => {
                                    if (dep.id === action.id) {
                                        let objId = getTransformedObjectId(item.objectType, item.id, item.statusCode);
                                        if (selectedObjectIds.indexOf(objId) < 0)
                                            selectedObjectIds.push(objId);
                                    }
                                });
                            });
                        });
            */
            var selectedRows_1 = syncListTable.getSelectedIds()
                .filter(function (key) { return selectedObjectIds.indexOf(key) < 0; })
                .reduce(function (target, key) {
                var _a;
                return Object.assign(target, (_a = {}, _a[key] = syncListTable.state.selectedRows[key], _a));
            }, {});
            syncListTable.setState(function () { return ({ selectedRows: selectedRows_1 }); });
        }
    };
    var updateRejectedObjectsIds = function (reject, rejectedObjectIds, actionObjects) {
        var rows = {};
        if (reject) {
            var autoUnaccepts_2 = Array();
            startGetSyncListResponse.data.forEach(function (item) {
                var soObjId = getTransformedObjectId(item.objectType, item.id, item.statusCode);
                if (rejectedObjectIds.indexOf(soObjId) >= 0) {
                    if (item.hasOwnProperty("autoUnaccepts") && item.autoUnaccepts != null) {
                        autoUnaccepts_2.push.apply(autoUnaccepts_2, item.autoUnaccepts);
                    }
                }
            });
            //            rejectedObjectIds.push(...autoUnaccepts);
            /*
                        let syncObjects = startGetSyncListResponse.data;
                        syncObjects.forEach((item) => {
                            let objectDeps = GetAssociatedDependencies(item);
            
                            actionObjects.forEach((action) => {
                                objectDeps.forEach((dep) => {
                                    if (dep.id === action.id) {
                                        let objId = getTransformedObjectId(item.objectType, item.id, item.statusCode);
                                        if (rejectedObjectIds.indexOf(objId) < 0)
                                            rejectedObjectIds.push(objId);
                                    }
                                });
                            });
                        });
            */
            rejectedObjectIds.forEach(function (item) {
                rows[item] = true;
            });
            setRejectIds(function (prevState) { return ({
                rows: __assign(__assign({}, prevState.rows), rows)
            }); });
            var selectedRows_2 = syncListTable.getSelectedIds()
                .filter(function (key) { return rejectedObjectIds.indexOf(key) < 0 && autoUnaccepts_2.indexOf(key) < 0; })
                .reduce(function (target, key) {
                var _a;
                return Object.assign(target, (_a = {},
                    _a[key] = syncListTable.state.selectedRows[key],
                    _a));
            }, {});
            syncListTable.setState(function () { return ({ selectedRows: selectedRows_2 }); });
        }
        else {
            var syncObjects = startGetSyncListResponse.data;
            syncObjects.forEach(function (item) {
                var objectDeps = GetAssociatedDependencies(item);
                actionObjects.forEach(function (action) {
                    objectDeps.forEach(function (dep) {
                        if (dep.id === action.id) {
                            var objId = getTransformedObjectId(item.objectType, item.id, item.statusCode);
                            if (rejectedObjectIds.indexOf(objId) < 0)
                                rejectedObjectIds.push(objId);
                        }
                    });
                });
            });
            var rejectedRows_2 = Object.keys(rejectIds.rows)
                .filter(function (key) { return rejectedObjectIds.indexOf(key) < 0; })
                .reduce(function (target, key) {
                var _a;
                return Object.assign(target, (_a = {}, _a[key] = syncListTable.state.selectedRows[key], _a));
            }, {});
            setRejectIds(function () { return ({ rows: rejectedRows_2 }); });
        }
    };
    var updateDeleteWorkingCopyCount = function () {
        var deleteCount = 0;
        var selectedIds = syncListTable.getSelectedIds();
        startGetSyncListResponse.data.forEach(function (item) {
            if (item.multiVersion && item.syncActiveOnly && item.workingCopyWillBeDeleted) {
                var soObjId = getTransformedObjectId(item.objectType, item.id, item.statusCode);
                if (selectedIds.indexOf(soObjId) >= 0) {
                    ++deleteCount;
                }
            }
        });
        setDeleteWcDueToSyncActiveOnly(deleteCount);
    };
    var handleSelectAll = function (selectAll) {
        if (!selectAll && objectTypeFilter === ObjectType.all && (statusTypeFilter === FilterStatusType.all || statusTypeFilter === FilterStatusType.selected))
            syncListTable.setState(function () { return ({ selectedRows: {} }); });
        else {
            var selectedObjectIds_1 = [];
            var rejectedObjectIds_1 = statusTypeFilter === FilterStatusType.rejected ? Object.keys(rejectIds.rows) : [];
            var filteredObjects = startGetSyncListResponse.data.filter(function (syncObj) {
                var select = true;
                if (textFilter !== null && textFilter !== '')
                    select = syncObj.name.toUpperCase().includes(textFilter.toUpperCase());
                if (select && objectTypeFilter !== ObjectType.all)
                    select = syncObj.objectType === objectTypeFilter;
                if (select && statusTypeFilter !== FilterStatusType.all) {
                    if (statusTypeFilter === FilterStatusType.conflict)
                        select = syncObj.isConflict;
                    else if (statusTypeFilter === FilterStatusType.noConflict)
                        select = !syncObj.isConflict;
                    else if (statusTypeFilter === FilterStatusType.selected)
                        select = syncListTable.getSelectedIds().indexOf(getTransformedObjectId(syncObj.objectType, syncObj.id, syncObj.statusCode)) >= 0;
                    else if (statusTypeFilter === FilterStatusType.rejected)
                        select = rejectedObjectIds_1.indexOf(getTransformedObjectId(syncObj.objectType, syncObj.id, syncObj.statusCode)) >= 0;
                }
                if (syncObj.notSyncable != null && syncObj.notSyncable)
                    select = false;
                return select;
            });
            filteredObjects.forEach(function (item) {
                if (objectCanBeSynced(settings.forceSync, item)) {
                    var objId = getTransformedObjectId(item.objectType, item.id, item.statusCode);
                    if (selectedObjectIds_1.indexOf(objId) < 0)
                        selectedObjectIds_1.push(objId);
                    if (selectAll) {
                        var dependencies = GetAssociatedDependencies(item);
                        dependencies.forEach(function (dep) {
                            var depId = getTransformedObjectId(dep.objectType, dep.id, dep.statusCode);
                            if (selectedObjectIds_1.indexOf(depId) < 0)
                                selectedObjectIds_1.push(depId);
                        });
                    }
                }
            });
            updateSyncListSelectedObjectsIds(selectAll, selectedObjectIds_1, filteredObjects);
        }
        setRefreshSelectedTabPage(true);
    };
    var handleSelect = function (selected, objectId, objectToSelect) {
        var refresh = (statusTypeFilter === FilterStatusType.selected && !selected)
            || (statusTypeFilter === FilterStatusType.notSelected && selected)
            || (statusTypeFilter === FilterStatusType.rejected && selected);
        var selectedObjectIds = [];
        selectedObjectIds = getSelectedObjectIds(selected, objectId, objectToSelect, selectedObjectIds);
        updateSyncListSelectedObjectsIds(selected, selectedObjectIds, [objectToSelect]);
        setRefreshSelectedTabPage(refresh);
    };
    var getSelectedObjectIds = function (selected, objectId, objectToSelect, selectedObjectIds) {
        if (selectedObjectIds.includes(objectId))
            return selectedObjectIds;
        selectedObjectIds.push(objectId);
        /*
                if(selected) { // Accept
                    const autoAccepts = objectToSelect.autoAccepts;
                    for(let depObjId in autoAccepts) {
                        const result = getSelectedObjectIds(selected, depObjId, objectToSelect, selectedObjectIds);
                    }
                } else { // Unaccept
                    const autoAccepts = objectToSelect.autoUnaccepts;
                    for(let depObjId in autoAccepts) {
                        const result = getSelectedObjectIds(selected, depObjId, objectToSelect, selectedObjectIds);
                    }
                }
        */
        var setObjectIds = function (item, selected) {
            var objId = getTransformedObjectId(item.objectType, item.id, item.statusCode);
            var objectToSelect = getDependencySyncObject(false, item.id);
            var result = getSelectedObjectIds(selected, objId, objectToSelect, selectedObjectIds);
        };
        if (selected) {
            GetAssociatedDependencies(objectToSelect).forEach(function (dep) { return setObjectIds(dep, selected); });
        }
        else {
            startGetSyncListResponse.data.forEach(function (item) {
                if (GetAssociatedDependencies(item).find(function (dep) { return dep.id === objectToSelect.id; })) {
                    setObjectIds(item, selected);
                }
            });
        }
        return selectedObjectIds;
    };
    var handleRejectAll = function (rejectAll) {
        if (!rejectAll && objectTypeFilter === ObjectType.all && (statusTypeFilter === FilterStatusType.all || statusTypeFilter === FilterStatusType.rejected)) {
            setRejectIds(function () { return ({ rows: {} }); });
        }
        else {
            var selectedObjectIds_2 = [];
            var rejectedObjectIds_2 = statusTypeFilter === FilterStatusType.rejected ? Object.keys(rejectIds.rows) : [];
            var filteredObjects = startGetSyncListResponse.data.filter(function (syncObj) {
                var select = true;
                if (textFilter !== null && textFilter !== '')
                    select = syncObj.name.toUpperCase().includes(textFilter.toUpperCase());
                if (select && objectTypeFilter !== ObjectType.all)
                    select = syncObj.objectType === objectTypeFilter;
                if (select && statusTypeFilter !== FilterStatusType.all) {
                    if (statusTypeFilter === FilterStatusType.conflict)
                        select = syncObj.isConflict;
                    else if (statusTypeFilter === FilterStatusType.noConflict)
                        select = !syncObj.isConflict;
                    else if (statusTypeFilter === FilterStatusType.selected)
                        select = syncListTable.getSelectedIds().indexOf(getTransformedObjectId(syncObj.objectType, syncObj.id, syncObj.statusCode)) >= 0;
                    else if (statusTypeFilter === FilterStatusType.rejected)
                        select = rejectedObjectIds_2.indexOf(getTransformedObjectId(syncObj.objectType, syncObj.id, syncObj.statusCode)) >= 0;
                }
                if (!objectCanBeRejected(settings.forceSync, syncObj))
                    select = false;
                return select;
            });
            filteredObjects.forEach(function (item) {
                if (!objectCanBeRejected(settings.forceSync, item))
                    return;
                var objId = getTransformedObjectId(item.objectType, item.id, item.statusCode);
                if (selectedObjectIds_2.indexOf(objId) < 0)
                    selectedObjectIds_2.push(objId);
                if (rejectAll) {
                    var dependencies = GetAssociatedReferences(item);
                    dependencies.forEach(function (dep) {
                        var dependencySyncObject = getDependencySyncObject(true, dep.id);
                        if (dependencySyncObject === undefined || dependencySyncObject === null)
                            return;
                        if (!objectCanBeRejected(settings.forceSync, dependencySyncObject))
                            return;
                        var id = dependencySyncObject ? dependencySyncObject.id : dep.id;
                        var depId = getTransformedObjectId(dep.objectType, id, dep.statusCode);
                        if (selectedObjectIds_2.indexOf(depId) < 0)
                            selectedObjectIds_2.push(depId);
                    });
                }
            });
            updateRejectedObjectsIds(rejectAll, selectedObjectIds_2, filteredObjects);
        }
        setRefreshSelectedTabPage(true);
    };
    var findDependentObjects = function (syncObject, processedDependencies) {
        if (processedDependencies === void 0) { processedDependencies = []; }
        var dependents = [];
        startGetSyncListResponse.data.forEach(function (item) {
            var objectDeps = GetAssociatedDependencies(item);
            objectDeps.forEach(function (dep) {
                if (processedDependencies.includes(dep))
                    return;
                processedDependencies.push(dep);
                if (dep.id === syncObject.id) {
                    dependents.push(item);
                    dependents.push.apply(dependents, findDependentObjects(item, processedDependencies));
                }
            });
        });
        return dependents;
    };
    var handleReject = function (rejected, objectId, objectToReject) {
        if (!objectCanBeRejected(settings.forceSync, objectToReject))
            return;
        var refresh = (statusTypeFilter === FilterStatusType.rejected && !rejected)
            || (statusTypeFilter === FilterStatusType.notSelected && rejected)
            || (statusTypeFilter === FilterStatusType.selected && rejected);
        var refs = GetAssociatedReferences(objectToReject);
        var rejectedObjectIds = [];
        rejectedObjectIds.push(objectId);
        var sourceWC = objectToReject.status['source'];
        var sourceAC = objectToReject.status['sourceActiveCopy'];
        if (rejected) {
            if ((sourceWC && sourceWC.id === StatusType.new) || (sourceAC && sourceAC.id === StatusType.new) || objectToReject.objectType == ObjectType.channelConfiguration) {
                var dependentObjects = findDependentObjects(objectToReject);
                dependentObjects.forEach(function (item) {
                    var objId = getTransformedObjectId(item.objectType, item.id, item.statusCode);
                    if (rejectedObjectIds.indexOf(objId) < 0) {
                        rejectedObjectIds.push(objId);
                    }
                });
            }
        }
        if (refs.length > 0 && rejected) {
            refs.forEach(function (ref) {
                if (isDependencyMandatory(ref, objectToReject)) {
                    var dependencySyncObject = getDependencySyncObject(true, ref.id);
                    if (dependencySyncObject === undefined || dependencySyncObject === null)
                        return;
                    var id = dependencySyncObject ? dependencySyncObject.id : ref.id;
                    rejectedObjectIds.push(getTransformedObjectId(ref.objectType, id, ref.statusCode));
                }
            });
        }
        var deps = GetAssociatedDependencies(objectToReject);
        if (deps.length > 0 && !rejected) {
            deps.forEach(function (dep) {
                var dependencySyncObject = getDependencySyncObject(false, dep.id);
                if (dependencySyncObject === undefined || dependencySyncObject === null)
                    return;
                if (!objectCanBeRejected(settings.forceSync, dependencySyncObject))
                    return;
                var id = dependencySyncObject ? dependencySyncObject.id : dep.id;
                rejectedObjectIds.push(getTransformedObjectId(dep.objectType, dep.id, dep.statusCode));
            });
        }
        updateRejectedObjectsIds(rejected, rejectedObjectIds, [objectToReject]);
        setRefreshSelectedTabPage(refresh);
    };
    var handleDependencySelect = function (checked, dependencyId, isReference) {
        var objectToSelect;
        objectToSelect = getDependencySyncObject(isReference, dependencyId);
        handleSelect(checked, getTransformedObjectId(objectToSelect.objectType, objectToSelect.id, objectToSelect.statusCode), objectToSelect);
    };
    var getDependencySyncObject = function (isReference, dependencyId) {
        var objectToSelect;
        startGetSyncListResponse.data.forEach(function (item) {
            var itemId = isReference ? item.targetObjectId : item.id;
            if (itemId === dependencyId)
                objectToSelect = item;
        });
        return objectToSelect;
    };
    var getReferencedByObjectId = function (isReference, refId) {
        var obj = getDependencySyncObject(isReference, refId);
        return getTransformedObjectId(obj.objectType, obj.id, obj.statusCode);
    };
    /* ---------------- */
    var handleInitSyncProcess = function () {
        updateDeleteWorkingCopyCount();
        setShowInitSyncProcessConfirmation(true);
    };
    var handleInitSyncProcessConfirmation = function () {
        initSyncProcess();
    };
    var initSyncProcess = function (rejectChanges) {
        if (rejectChanges === void 0) { rejectChanges = false; }
        return __awaiter(_this, void 0, void 0, function () {
            var params, queryString, result, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        params = new URLSearchParams();
                        params.append('sourceInstanceId', resultsInfo.sourceInstanceId.toString());
                        params.append('sourceDocumentId', resultsInfo.sourceDocumentId.toString());
                        params.append('targetInstanceId', resultsInfo.targetInstanceId.toString());
                        params.append('targetDocumentId', resultsInfo.targetDocumentId.toString());
                        params.append('acceptAll', 'false');
                        params.append('forceSync', settings.forceSync.toString());
                        params.append('activeOnly', resultsInfo.activeOnly.toString());
                        params.append('syncActiveOnly', resultsInfo.syncActiveOnly.toString());
                        params.append('syncMessagePriority', settings.syncMessagePriority.toString());
                        params.append('newWorkingAfterActive', settings.newWorkingAfterActive.toString());
                        params.append('maintainTargetRelativePriority', settings.maintainTargetRelativePriority.toString());
                        params.append('includeDifferencesInLog', settings.includeDifferencesInLog.toString());
                        params.append('includeNotVisibleTargetRules', settings.includeNotVisibleTargetRules.toString());
                        params.append('includeRejectedChanges', settings.includeRejectedChanges.toString()); // Include the changes we rejected before
                        params.append('hideUntilNextChanges', rejectChanges.toString()); // We want to reject the changes now
                        params.append('notSyncLayoutChanges', settings.notSyncLayoutChanges.toString());
                        params.append('languages', getLanguageIds(resultsInfo.selectedLanguages));
                        params.append('guidSync', guidSync);
                        params.append('tk', gup('tk'));
                        queryString = params.toString();
                        setIsSyncing(true);
                        setDependenciesToDisplay(null);
                        setReferencesToDisplay(null);
                        return [4 /*yield*/, getFetchPromise("".concat(endpoints.INIT_SYNC_PROCESS, "?").concat(queryString), {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json; charset=utf-8' },
                                body: JSON.stringify({ objectIds: syncListTable.getSelectedIds(), rejectIds: Object.keys(rejectIds.rows) }),
                            })];
                    case 1:
                        result = _a.sent();
                        return [4 /*yield*/, result.json()];
                    case 2:
                        response = _a.sent();
                        if (response.status === 'success') {
                            setIsRequestingSync(true);
                            setSyncStatusResponse(response);
                            setSyncTaskGuid(response.taskGuid);
                            setShowSyncProcessProgress(true);
                            setShowSyncList(false);
                            setSyncListTable(null);
                            if (response.syncPrioritiesTaskId)
                                setSyncPrioritiesTaskGuid(response.syncPrioritiesTaskId);
                            else
                                setSyncPrioritiesTaskGuid(null);
                        }
                        else {
                            setShowInitSyncProcessConfirmation(false);
                            setShowInitHideChangesProcessConfirmation(false);
                            setIsRequestingSync(false);
                            setIsSyncing(false);
                            setSyncTaskGuid(null);
                            alert("Error: ".concat(response.errorMessage));
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    var onCsvFileDownload = function () {
        window.resetActivityCount();
    };
    var refreshBackgroundTasks = function () {
        if ($('#backgroundTasksPlaceholder').length != 0)
            $('#backgroundTasksPlaceholder').backgroundTasks();
    };
    var handleSaveAsSelect = function (key, e) {
        e.stopPropagation();
        if (key === 'includeSourceAndTargetContents') {
            var newSettings = __assign({}, settings);
            newSettings.includeRawContents = !newSettings.includeRawContents;
            setSettings(newSettings);
        }
    };
    var handleSaveAsMenuToggle = function (isOpen, e, metadata) {
        setShowSaveAsMenu(!(metadata.source === 'rootClose'));
    };
    var showDataExportModal = function () {
        setShowDataExportConfirmation(true);
    };
    var hideDataExportModal = function () {
        setShowDataExportConfirmation(false);
    };
    var handleInitExportSyncDataProcess = function () {
        setDataExportFileName(null);
        setDataExportResource(null);
        setSyncDataExtractTaskGuid(null);
        setIsDataExtracting(true);
        setSyncDataExtractTaskStatus(false);
        initExportSyncDataProcess();
        showDataExportModal();
    };
    var handleDataExportClosedModal = function () {
        hideDataExportModal();
        setIsDataExtracting(false);
        setSyncDataExtractTaskGuid(null);
        setDataExportResource(null);
        setDataExportFileName(null);
        setSyncDataExtractTaskStatus(false);
    };
    var initExportSyncDataProcess = function () { return __awaiter(_this, void 0, void 0, function () {
        var params, queryString, result, response;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    params = new URLSearchParams();
                    params.append('guidSync', guidSync);
                    params.append('sourceInstanceId', resultsInfo.sourceInstanceId.toString());
                    params.append('sourceDocumentId', resultsInfo.sourceDocumentId.toString());
                    params.append('targetInstanceId', resultsInfo.targetInstanceId.toString());
                    params.append('targetDocumentId', resultsInfo.targetDocumentId.toString());
                    params.append('includeRawContents', settings.includeRawContents.toString());
                    params.append('tk', gup('tk'));
                    queryString = params.toString();
                    return [4 /*yield*/, getFetchPromise("".concat(endpoints.INIT_DATA_EXPORT_PROCESS, "?").concat(queryString), {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json; charset=utf-8' },
                            body: JSON.stringify({ objectIds: syncListTable.getSelectedIds(), rejectIds: Object.keys(rejectIds.rows) }),
                        })];
                case 1:
                    result = _a.sent();
                    return [4 /*yield*/, result.json()];
                case 2:
                    response = _a.sent();
                    setSyncDataExtractTaskGuid(response.taskGuid);
                    requestSyncDataExtractTaskStatus();
                    return [2 /*return*/];
            }
        });
    }); };
    var getSyncDataExtractTaskStatus = function () { return __awaiter(_this, void 0, void 0, function () {
        var result, response, params, queryString, taskGuid;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!(isDataExtracting && syncDataExtractTaskGuid)) return [3 /*break*/, 3];
                    result = void 0;
                    response = void 0;
                    params = new URLSearchParams();
                    params.append('taskGuid', syncDataExtractTaskGuid);
                    params.append('tk', gup('tk'));
                    queryString = params.toString();
                    return [4 /*yield*/, getFetchPromise("".concat(endpoints.GET_DATA_EXPORT_PROCESS_STATUS, "?").concat(queryString))];
                case 1:
                    result = _a.sent();
                    return [4 /*yield*/, result.json()];
                case 2:
                    response = _a.sent();
                    if (response) {
                        taskGuid = response.taskGuid;
                        if (taskGuid && syncDataExtractTaskGuid === taskGuid && (response.status === "error" || response.resource)) {
                            if (response.fileName && response.resource) {
                                setDataExportFileName(response.fileName);
                                setDataExportResource(response.resource);
                            }
                        }
                        else {
                            requestSyncDataExtractTaskStatus();
                        }
                    }
                    else {
                        requestSyncDataExtractTaskStatus();
                    }
                    _a.label = 3;
                case 3: return [2 /*return*/];
            }
        });
    }); };
    var requestSyncDataExtractTaskStatus = function () { return __awaiter(_this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            setTimeout(function () {
                setSyncDataExtractTaskStatus(true);
            }, 2000);
            return [2 /*return*/];
        });
    }); };
    var handleInitSyncProcessConfirmationModalClose = function () {
        setShowInitSyncProcessConfirmation(false);
    };
    var handleInitSyncPrioritiesProcess = function () {
        setShowInitSyncPrioritiesProcessConfirmation(true);
    };
    var handleInitSyncPrioritiesProcessConfirmationModalClose = function () {
        setShowInitSyncPrioritiesProcessConfirmation(false);
    };
    var handleInitSyncPrioritiesProcessConfirmation = function () {
        initSyncPrioritiesProcess();
    };
    var handleInitHideChangesProcess = function () {
        setShowInitHideChangesProcessConfirmation(true);
    };
    var handleInitHideChangesProcessConfirmationModalClose = function () {
        setShowInitHideChangesProcessConfirmation(false);
    };
    var handleInitHideChangesProcessConfirmation = function () {
        initSyncProcess(true);
    };
    var initSyncPrioritiesProcess = function () { return __awaiter(_this, void 0, void 0, function () {
        var params, sourceInstanceId, sourceDocumentId, targetInstanceId, targetDocumentId, queryString, result, response;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    params = new URLSearchParams();
                    if (showSyncList) {
                        sourceInstanceId = resultsInfo.sourceInstanceId;
                        sourceDocumentId = resultsInfo.sourceDocumentId;
                        targetInstanceId = resultsInfo.targetInstanceId;
                        targetDocumentId = resultsInfo.targetDocumentId;
                    }
                    else {
                        if (syncDocumentsConfig.direction === Direction.update) {
                            sourceInstanceId = syncDocumentsConfig.selectableDocument.selectedInstanceId;
                            sourceDocumentId = syncDocumentsConfig.selectableDocument.selectedSiblingId;
                            targetInstanceId = syncDocumentsConfig.currentDocument.instanceId;
                            targetDocumentId = syncDocumentsConfig.currentDocument.documentId;
                        }
                        else {
                            sourceInstanceId = syncDocumentsConfig.currentDocument.instanceId;
                            sourceDocumentId = syncDocumentsConfig.currentDocument.documentId;
                            targetInstanceId = syncDocumentsConfig.selectableDocument.selectedInstanceId;
                            targetDocumentId = syncDocumentsConfig.selectableDocument.selectedSiblingId;
                        }
                    }
                    params.append('sourceInstanceId', sourceInstanceId.toString());
                    params.append('sourceDocumentId', sourceDocumentId.toString());
                    params.append('targetInstanceId', targetInstanceId.toString());
                    params.append('targetDocumentId', targetDocumentId.toString());
                    params.append('newWorkingAfterActive', settings.newWorkingAfterActive.toString());
                    params.append('maintainTargetRelativePriority', settings.maintainTargetRelativePriority.toString());
                    params.append('guidSync', guidSync);
                    params.append('tk', gup('tk'));
                    queryString = params.toString();
                    setIsSyncingPriorities(true);
                    return [4 /*yield*/, getFetchPromise("".concat(endpoints.INIT_SYNC_PRIORITY, "?").concat(queryString))];
                case 1:
                    result = _a.sent();
                    return [4 /*yield*/, result.json()];
                case 2:
                    response = _a.sent();
                    setIsSyncingPriorities(false);
                    setShowInitSyncPrioritiesProcessConfirmation(false);
                    if (response.status === 'success') {
                        setSyncPrioritiesResponse(response);
                        setSyncPrioritiesTaskGuid(response.taskGuid);
                        refreshBackgroundTasks();
                    }
                    else {
                        alert("Error: ".concat(response.errorMessage));
                    }
                    return [2 /*return*/];
            }
        });
    }); };
    var handleSortingSelect = function (key) {
        setPageIndex(0);
        setRefreshSyncListTable(true);
        if (key === sortDirections.ASCENDING || key === sortDirections.DESCENDING)
            setSortDirection(key);
        else
            setSortKey(key);
    };
    var handleSearchFilterChange = _.debounce(function (text) {
        var search = text.trim();
        setPageIndex(0);
        setTextFilter(search);
        syncListTable.setFilter(search);
    }, 400);
    var handleObjectTypeFilterSelect = function (key) {
        setPageIndex(0);
        setRefreshSyncListTable(true);
        setObjectTypeFilter(+key);
    };
    var handleStatusTypeFilterChange = function (e, newType) {
        e.preventDefault();
        setPageIndex(0);
        setRefreshSyncListTable(true);
        setStatusTypeFilter(newType);
    };
    var handleDependenciesListModalClose = function () {
        setShowDependenciesListModal(false);
    };
    var handleReferencesListModalClose = function () {
        setShowReferencesListModal(false);
    };
    React.useEffect(function () {
        var startProgress = null;
        if (startGetSyncListResponse && isProcessingList) {
            var response_1;
            var syncListTaskStatus_1 = function () { return __awaiter(_this, void 0, void 0, function () {
                var params, result;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            params = new URLSearchParams();
                            params.append('listKey', startGetSyncListResponse.key);
                            params.append('tk', gup('tk'));
                            return [4 /*yield*/, getFetchPromise("".concat(endpoints.GET_SYNC_LIST_STATUS, "?").concat(params.toString()))];
                        case 1:
                            result = _a.sent();
                            return [4 /*yield*/, result.json()];
                        case 2:
                            response_1 = _a.sent();
                            return [2 /*return*/];
                    }
                });
            }); };
            startProgress = setInterval(function () {
                if (response_1) {
                    setStartGetSyncListResponse(response_1);
                    if (response_1.stage === syncListStage.COMPLETED || response_1.stage === syncListStage.ERROR || response_1.stage === syncListStage.UNAVAILABLE) {
                        clearInterval(startProgress);
                        if (response_1.stage === syncListStage.COMPLETED) {
                            if (response_1.data.length > 0) {
                                loadInitialData(true);
                            }
                            else {
                                setSyncListFetchPromise(null);
                                setShowNoDataInfo(true);
                                setListHasData(false);
                                setShowSyncListLoadingProgress(false);
                                setShowSyncList(false);
                                setIsGeneratingList(false);
                                setIsProcessingList(false);
                                setSyncListTable(null);
                            }
                        }
                        else {
                            setSyncListFetchPromise(null);
                            setListHasData(false);
                            setShowSyncList(false);
                            setIsGeneratingList(false);
                            setIsProcessingList(false);
                            setSyncListTable(null);
                        }
                    }
                    else
                        syncListTaskStatus_1();
                }
            }, 3000);
            setShowGenerateListConfirmation(false);
            syncListTaskStatus_1();
            return function () { return clearInterval(startProgress); };
        }
    }, [isProcessingList]);
    React.useEffect(function () {
        var startProgress = null;
        if (syncTaskGuid && isRequestingSync) {
            var result_1;
            var response_2;
            var fetchTaskStatus_1 = function () { return __awaiter(_this, void 0, void 0, function () {
                var params, queryString;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            params = new URLSearchParams();
                            params.append('taskId', syncTaskGuid);
                            params.append('targetInstanceId', resultsInfo.targetInstanceId.toString());
                            params.append('tk', gup('tk'));
                            queryString = params.toString();
                            return [4 /*yield*/, getFetchPromise("".concat(endpoints.GET_SYNC_PROCESS_STATUS, "?").concat(queryString))];
                        case 1:
                            result_1 = _a.sent();
                            return [4 /*yield*/, result_1.json()];
                        case 2:
                            response_2 = _a.sent();
                            return [2 /*return*/];
                    }
                });
            }); };
            startProgress = setInterval(function () { return __awaiter(_this, void 0, void 0, function () {
                var syncStatus;
                return __generator(this, function (_a) {
                    if (response_2) {
                        syncStatus = {
                            stage: response_2.stage,
                            percentage: response_2.percentage,
                            currentObjectName: response_2.currentObjectName,
                            completedObjects: response_2.completedObjects,
                            remainingObjects: response_2.remainingObjects,
                        };
                        setSyncStatusResponse(syncStatus);
                        if (isGeneratingList) {
                            clearInterval(startProgress);
                            setIsRequestingSync(false);
                            setIsSyncing(false);
                        }
                        else if (syncStatus.stage === SyncProcessStage.completed || syncStatus.stage === SyncProcessStage.error) {
                            clearInterval(startProgress);
                            setIsRequestingSync(false);
                            setIsSyncing(false);
                            if (syncStatus.stage === SyncProcessStage.completed && syncPrioritiesTaskGuid)
                                refreshBackgroundTasks();
                        }
                        else
                            fetchTaskStatus_1();
                    }
                    return [2 /*return*/];
                });
            }); }, 3000);
            setShowInitSyncProcessConfirmation(false);
            setShowInitHideChangesProcessConfirmation(false);
            fetchTaskStatus_1();
            if (isGeneratingList)
                clearInterval(startProgress);
            return function () { return clearInterval(startProgress); };
        }
        else {
            if (isGeneratingList) {
                clearInterval(startProgress);
            }
        }
    }, [syncTaskGuid, isRequestingSync, isGeneratingList]);
    React.useEffect(function () {
        if (syncDataExtractTaskStatus) {
            setSyncDataExtractTaskStatus(false);
            getSyncDataExtractTaskStatus();
        }
    }, [syncDataExtractTaskStatus]);
    React.useEffect(function () {
        if (syncListTable)
            syncListTable.setState(function () { return ({ selectedRows: {} }); });
    }, [settings.forceSync]);
    React.useEffect(function () {
        if (syncListTable && refreshSyncListTable)
            syncListTable.setColumnSort(sortKey, sortDirection);
    }, [sortKey, sortDirection]);
    React.useEffect(function () {
        if (syncListTable && refreshSyncListTable)
            syncListTable.refreshCurrentPage();
    }, [statusTypeFilter, objectTypeFilter, projectNameFilter]);
    React.useEffect(function () {
        if (syncListTable && refreshSelectedTabPage) {
            syncListTable.refreshCurrentPage();
            setRefreshSelectedTabPage(false);
        }
    }, [refreshSelectedTabPage]);
    return (React.createElement(React.Fragment, null,
        React.createElement("div", { className: "position-relative ".concat(showContentCompare ? 'd-none' : 'd-flex', " flex-column box-shadow-4 rounded bg-white ").concat(!visible ? 'invisible' : 'anim-slideUp-enter') },
            React.createElement("div", { className: "border-bottom" },
                React.createElement("div", { className: "d-flex align-items-center mx-4 bg-white rounded-top" },
                    React.createElement("hgroup", { className: "my-3 mr-4 flex-grow-1" },
                        React.createElement("h1", { className: "h5 ".concat(showSyncList ? "m-0" : "my-2") }, client_messages.sync.sync_list),
                        showSyncList &&
                            React.createElement(React.Fragment, null,
                                React.createElement("h2", { className: "row no-gutters h6 m-0 text-muted font-weight-normal fs-xs" },
                                    React.createElement(ReactBootstrap.OverlayTrigger, { placement: "bottom", delay: { show: 1000, hide: 0 }, overlay: function (props) { return syncTooltip(props, "source_document_".concat(resultsInfo.sourceDocumentId), resultsInfo.sourceDocumentName); } },
                                        React.createElement("div", { className: "col-auto d-flex align-items-center cursor-default", style: { maxWidth: '45%' } },
                                            React.createElement("div", { className: "mr-1 text-uppercase text-nowrap" },
                                                resultsInfo.sourceInstance,
                                                ","),
                                            React.createElement("div", { className: "text-truncate" }, resultsInfo.sourceDocumentName))),
                                    React.createElement("div", { className: "col-auto mx-2 px-1 text-light" },
                                        React.createElement("i", { className: "fas fa-long-arrow-right fa-lg", "aria-hidden": true }),
                                        React.createElement("span", { className: "sr-only" }, client_messages.sync.to)),
                                    React.createElement(ReactBootstrap.OverlayTrigger, { placement: "bottom", delay: { show: 1000, hide: 0 }, overlay: function (props) { return syncTooltip(props, "target_document_".concat(resultsInfo.targetDocumentId), resultsInfo.targetDocumentName); } },
                                        React.createElement("div", { className: "col-auto d-flex align-items-center cursor-default", style: { maxWidth: '45%' } },
                                            React.createElement("div", { className: "mr-1 text-uppercase text-nowrap" },
                                                resultsInfo.targetInstance,
                                                ","),
                                            React.createElement("div", { className: "text-truncate" }, resultsInfo.targetDocumentName)))))),
                    React.createElement("div", { className: "d-flex flex-shrink-0 ml-auto my-3" },
                        (showSyncList || showSyncListLoadingProgress || showSyncProcessProgress) &&
                            React.createElement(React.Fragment, null,
                                React.createElement(ReactBootstrap.Button, { variant: "outline-primary", disabled: (!showSyncList && isGeneratingList) || isGenerateDisabled(), onClick: (!showSyncList && isGeneratingList) ? null : function () { return handleGenerateList(showSyncList); } },
                                    React.createElement("span", { className: "mr-2" }, ((!showSyncList && isGeneratingList && !isProcessingList)) ? client_messages.sync.loading : client_messages.sync.generate),
                                    React.createElement("i", { className: ((!showSyncList && isGeneratingList && !isProcessingList)) ? "fas fa-spinner fa-spin" : "far fa-arrow-alt-right", "aria-hidden": "true" })),
                                !(isRequestingSync || showSyncListLoadingProgress || showSyncProcessProgress) &&
                                    React.createElement(ReactBootstrap.Dropdown, { bsPrefix: "btn-group", className: "ml-3" },
                                        React.createElement(ReactBootstrap.Button, { variant: (syncListTable && selectedCount < 1 && rejectedCount < 1) ? 'outline-primary' : 'primary', className: (syncListTable && selectedCount < 1 && rejectedCount < 1) ? 'border-right-0' : '', onClick: (syncListTable && selectedCount < 1 && rejectedCount < 1) ? null : handleInitSyncProcess, disabled: (syncListTable && selectedCount < 1 && rejectedCount < 1) },
                                            React.createElement("span", { className: "mr-2" }, client_messages.sync.text),
                                            React.createElement("i", { className: "far fa-sync-alt", "aria-hidden": "true" })),
                                        React.createElement(ReactBootstrap.Dropdown.Toggle, { variant: "primary", id: "SyncDropdown", split: true }),
                                        React.createElement(ReactBootstrap.Dropdown.Menu, { align: "right" },
                                            React.createElement(ReactBootstrap.Dropdown.Item, { href: "#/action-1", onClick: handleInitSyncPrioritiesProcess },
                                                React.createElement("i", { className: "fas fa-random fa-fw", "aria-hidden": "true" }),
                                                React.createElement("span", { className: "ml-3" }, client_messages.sync.sync_priorities_only)))),
                                !(isRequestingSync || showSyncListLoadingProgress || showSyncProcessProgress) &&
                                    React.createElement(ReactBootstrap.Dropdown, { bsPrefix: "btn-group", className: "ml-3", onSelect: handleSaveAsSelect, onToggle: handleSaveAsMenuToggle, show: showSaveAsMenu },
                                        React.createElement(ReactBootstrap.Dropdown.Toggle, { variant: "outline-dark", id: "dataExportDropdown2", split: true },
                                            client_messages.text.save_as,
                                            "\u00A0\u00A0"),
                                        React.createElement(ReactBootstrap.Dropdown.Menu, { align: "right", role: "menu" },
                                            React.createElement(CustomDropdownItem, { inputId: "includeRawContents", eventKey: "includeSourceAndTargetContents", checked: settings.includeRawContents, title: client_messages.sync.include_raw_contents, subtitle: client_messages.sync.include_source_and_target_contents_in_export }),
                                            React.createElement(ReactBootstrap.Dropdown.Divider, null),
                                            React.createElement(ReactBootstrap.Dropdown.Item, { onClick: handleInitExportSyncDataProcess },
                                                React.createElement("i", { className: "fas", "aria-hidden": "true" }),
                                                React.createElement("span", { className: "ml-3" }, client_messages.text.csv))))),
                        (!showSyncList) &&
                            React.createElement(ReactBootstrap.Button, { variant: (showSyncListLoadingProgress || showSyncProcessProgress) ? 'primary' : 'outline-primary', className: (showSyncListLoadingProgress || showSyncProcessProgress) ? 'ml-3' : null, disabled: isSyncing, onClick: isSyncing ? null : handleInitSyncPrioritiesProcess },
                                React.createElement("span", { className: "mr-2" }, client_messages.sync.sync_priorities_only),
                                React.createElement("i", { className: "fas fa-random", "aria-hidden": "true" }))))),
            showSyncListLoadingProgress &&
                React.createElement("div", { className: "m-5 p-3" },
                    React.createElement("div", { className: "d-flex flex-column align-items-center mt-n2" },
                        React.createElement("span", { className: "fa-container-shadow fa-container-shadow-secondary" },
                            React.createElement("i", { className: "fad ".concat((startGetSyncListResponse.stage === syncListStage.ERROR || startGetSyncListResponse.stage === syncListStage.UNAVAILABLE) ? 'fa-ban' : 'fa-cog fa-swap-opacity fa-spin', " mb-1"), "aria-hidden": true })),
                        React.createElement("div", { className: "my-2 font-weight-bold fs-sm" }, getSyncListStageName(startGetSyncListResponse.stage)),
                        React.createElement("div", { className: "bg-lightest rounded px-3 py-2 text-uppercase" },
                            React.createElement("div", { className: "d-flex align-items-center py-1 text-center text-muted" },
                                React.createElement("div", { className: "px-1" },
                                    React.createElement("div", { className: "h5 mb-0 text-body" },
                                        startGetSyncListResponse.percentage,
                                        "%"),
                                    React.createElement("div", { className: "fs-xs" }, client_messages.sync.progress)))))),
            (!showSyncList && !showSyncListLoadingProgress && !showSyncProcessProgress) &&
                React.createElement("div", { className: "py-5" },
                    React.createElement("div", { className: "d-flex justify-content-center py-4" },
                        React.createElement("div", { className: "d-flex flex-column" },
                            (showNoDataInfo && !listHasData) &&
                                React.createElement("div", { className: "text-center mt-n2" },
                                    React.createElement("span", { className: "fa-container-shadow fa-container-shadow-muted" },
                                        React.createElement("i", { className: "fad fa-file-search mb-1", "aria-hidden": true })),
                                    React.createElement("p", { className: "mt-2 mb-3 pt-1" }, client_messages.sync.we_couldnt_find_any_changes_to_sync)),
                            React.createElement(ReactBootstrap.Button, { variant: "primary", className: "align-self-center", size: (showNoDataInfo && !listHasData) ? null : 'lg', disabled: isGeneratingList || isGenerateDisabled(), onClick: !isGeneratingList ? function () { return handleGenerateList(false); } : null },
                                React.createElement("span", { className: "text-uppercase mr-2" }, isGeneratingList ? client_messages.sync.loading : client_messages.sync.generate),
                                React.createElement("i", { className: "".concat(isGeneratingList ? "fas fa-spinner fa-spin" : "far fa-arrow-alt-right", " ").concat((syncListFetchPromise && !listHasData) ? '' : 'fa-lg'), "aria-hidden": "true" })),
                            !showNoDataInfo &&
                                React.createElement("p", { className: "w-75 mx-auto mt-3 mb-4 px-5 text-center text-muted" }, client_messages.sync.generates_a_list_of_objects_that_have_changed_in_the_source_touchpoint_for_sync)))),
            showSyncList &&
                React.createElement(ListContextState, { setSelectedCount: setSelectedCount, setDeleteWcDueToSyncActiveOnly: setDeleteWcDueToSyncActiveOnly, setRejectedCount: setRejectedCount, rejectIds: rejectIds, settings: settings, getCanBeSyncedByAssignedUser: getCanBeSyncedByAssignedUser, getCanBeSyncedByReferences: getCanBeSyncedByReferences, getCanBeRejected: getCanBeRejected, setDependenciesToDisplay: setDependenciesToDisplay, setShowDependenciesListModal: setShowDependenciesListModal, setReferencesToDisplay: setReferencesToDisplay, setShowReferencesListModal: setShowReferencesListModal, handleSelect: handleSelect, handleReject: handleReject, compareData: compareData, setCompareData: setCompareData, setShowContentCompare: setShowContentCompare, syncObjectToCompare: syncObjectToCompare, setSyncObjectToCompare: setSyncObjectToCompare },
                    React.createElement("div", { className: "d-flex align-items-center mt-4 mb-1 pt-1 px-4" },
                        React.createElement("div", { className: "d-flex align-items-center mr-auto" },
                            React.createElement(ReactBootstrap.Dropdown, { className: "mr-4", onSelect: handleObjectTypeFilterSelect },
                                React.createElement(ReactBootstrap.Dropdown.Toggle, { variant: "blank", className: "border-light text-darkest", id: "objectTypeFilter" },
                                    React.createElement("span", { className: "text-muted mr-2" },
                                        React.createElement("i", { className: "fas fa-sliders-h mr-2", "aria-hidden": true }),
                                        client_messages.sync.filter,
                                        ":"),
                                    React.createElement("span", { className: "ml-1" }, getObjectTypeFilterName(objectTypeFilter))),
                                React.createElement(ReactBootstrap.Dropdown.Menu, null,
                                    React.createElement(ReactBootstrap.Dropdown.Item, { as: "button", active: objectTypeFilter === ObjectType.all, eventKey: ObjectType.all }, getObjectTypeFilterName(ObjectType.all)),
                                    React.createElement(ReactBootstrap.Dropdown.Divider, null),
                                    ObjectTypeDropDown
                                        .sort(function (item1, item2) { return item1.name.localeCompare(item2.name); })
                                        .map(function (item) {
                                        return React.createElement(ReactBootstrap.Dropdown.Item, { as: "button", active: objectTypeFilter === item.id, eventKey: item.id, key: item.id }, item.name);
                                    }))),
                            React.createElement(ReactBootstrap.Dropdown, { onSelect: handleSortingSelect },
                                React.createElement(ReactBootstrap.Dropdown.Toggle, { variant: "blank", className: "border-light text-darkest", id: "syncListSort" },
                                    React.createElement("span", { className: "text-muted mr-2" },
                                        React.createElement("i", { className: "fas fa-sort-amount-".concat(sortDirection === sortDirections.DESCENDING ? 'down' : 'up', " mr-2"), "aria-hidden": true }),
                                        client_messages.sync.sort,
                                        ":"),
                                    React.createElement("span", { className: "ml-1" }, getSortKeyName(sortKey))),
                                React.createElement(ReactBootstrap.Dropdown.Menu, null,
                                    React.createElement(ReactBootstrap.Dropdown.Item, { as: "button", active: sortKey === sortKeys.OBJECT_NAME, eventKey: sortKeys.OBJECT_NAME }, getSortKeyName(sortKeys.OBJECT_NAME)),
                                    React.createElement(ReactBootstrap.Dropdown.Item, { as: "button", active: sortKey === sortKeys.OBJECT_TYPE, eventKey: sortKeys.OBJECT_TYPE }, getSortKeyName(sortKeys.OBJECT_TYPE)),
                                    React.createElement(ReactBootstrap.Dropdown.Item, { as: "button", active: sortKey === sortKeys.VARIANT_NAME, disabled: objectTypeFilter !== ObjectType.all &&
                                            objectTypeFilter !== ObjectType.message &&
                                            objectTypeFilter !== ObjectType.localSmartText, eventKey: sortKeys.VARIANT_NAME }, getSortKeyName(sortKeys.VARIANT_NAME)),
                                    React.createElement(ReactBootstrap.Dropdown.Item, { as: "button", active: sortKey === sortKeys.STATUS, eventKey: sortKeys.STATUS }, getSortKeyName(sortKeys.STATUS)),
                                    React.createElement(ReactBootstrap.Dropdown.Item, { as: "button", active: sortKey === sortKeys.DEPENDENCIES, eventKey: sortKeys.DEPENDENCIES }, getSortKeyName(sortKeys.DEPENDENCIES)),
                                    React.createElement(ReactBootstrap.Dropdown.Divider, null),
                                    React.createElement(ReactBootstrap.Dropdown.Header, { className: "text-uppercase fs-xs" }, client_messages.sync.direction),
                                    React.createElement(ReactBootstrap.Dropdown.Item, { as: "button", active: sortDirection === sortDirections.ASCENDING, eventKey: sortDirections.ASCENDING }, client_messages.sync.ascending),
                                    React.createElement(ReactBootstrap.Dropdown.Item, { as: "button", active: sortDirection === sortDirections.DESCENDING, eventKey: sortDirections.DESCENDING }, client_messages.sync.descending)))),
                        React.createElement("div", { className: "form-group position-relative d-inline-block m-0" },
                            React.createElement("label", { htmlFor: "syncListSearchInput", className: "sr-only" }, "Search"),
                            React.createElement("i", { className: "fas fa-search ml-3 position-absolute text-muted top-30 z-index-1", "aria-hidden": "true" }),
                            React.createElement(ReactBootstrap.Form.Control, { id: "syncListSearchInput", htmlSize: 45, onChange: function (e) { return handleSearchFilterChange(e.target.value); }, className: "bg-lightest border-lightest has-control-x", type: "text", placeholder: client_messages.sync.search, autoComplete: "off" }))),
                    React.createElement("div", { className: "d-flex align-items-center mt-3" },
                        React.createElement("div", { className: "ml-3 mb-1" },
                            React.createElement(ReactBootstrap.Dropdown, null,
                                React.createElement(ReactBootstrap.Dropdown.Toggle, { variant: "blank", size: "sm", id: "selectAllCheck", className: "text-muted" },
                                    React.createElement("i", { className: "fas fa-sync-alt", "aria-hidden": "true" }),
                                    React.createElement("span", { className: "sr-only" }, client_messages.sync.text)),
                                React.createElement(ReactBootstrap.Dropdown.Menu, null,
                                    React.createElement(ReactBootstrap.Dropdown.Item, { disabled: statusTypeFilter === FilterStatusType.selected, as: "button", onClick: statusTypeFilter === FilterStatusType.selected ? null : function () { return handleSelectAll(true); } }, client_messages.sync.accept_all),
                                    React.createElement(ReactBootstrap.Dropdown.Item, { disabled: statusTypeFilter === FilterStatusType.notSelected, as: "button", onClick: statusTypeFilter === FilterStatusType.notSelected ? null : function () { return handleSelectAll(false); } }, client_messages.sync.unaccept_all),
                                    React.createElement(ReactBootstrap.Dropdown.Item, { onClick: function () { return handleRejectAll(true); } }, client_messages.sync.reject_all),
                                    React.createElement(ReactBootstrap.Dropdown.Item, { onClick: function () { return handleRejectAll(false); } }, client_messages.sync.unreject_all)))),
                        React.createElement("ul", { className: "nav nav-tabs-line flex-grow-1 text-uppercase font-weight-bold ml-n1" },
                            React.createElement("li", { className: "nav-item mx-3 px-1" },
                                React.createElement("a", { className: "nav-link p-0 py-2 ".concat(statusTypeFilter === FilterStatusType.all ? 'active' : ''), "aria-pressed": statusTypeFilter === FilterStatusType.all, role: "button", href: "#", onClick: function (e) { return handleStatusTypeFilterChange(e, FilterStatusType.all); } },
                                    React.createElement("span", { className: "d-inline-block align-middle" }, client_messages.sync.all_changes))),
                            React.createElement("li", { className: "nav-item mx-3 px-1" },
                                React.createElement("a", { className: "nav-link p-0 py-2 ".concat(statusTypeFilter === FilterStatusType.noConflict ? 'active' : ''), "aria-pressed": statusTypeFilter === FilterStatusType.noConflict, role: "button", href: "#", onClick: function (e) { return handleStatusTypeFilterChange(e, FilterStatusType.noConflict); } },
                                    React.createElement("span", { className: "d-inline-block align-middle" }, client_messages.sync.no_conflicts))),
                            React.createElement("li", { className: "nav-item mx-3 px-1" },
                                React.createElement("a", { className: "nav-link p-0 py-2 ".concat(statusTypeFilter === FilterStatusType.conflict ? 'active' : ''), "aria-pressed": statusTypeFilter === FilterStatusType.conflict, role: "button", href: "#", onClick: function (e) { return handleStatusTypeFilterChange(e, FilterStatusType.conflict); } },
                                    React.createElement("span", { className: "d-inline-block align-middle" }, client_messages.sync.conflicts))),
                            React.createElement("li", { className: "nav-item mx-3 px-1" },
                                React.createElement("a", { className: "nav-link p-0 py-2 ".concat(statusTypeFilter === FilterStatusType.selected ? 'active' : ''), "aria-pressed": statusTypeFilter === FilterStatusType.selected, role: "button", href: "#", onClick: function (e) { return handleStatusTypeFilterChange(e, FilterStatusType.selected); } },
                                    React.createElement("span", { className: "d-inline-block align-middle" }, client_messages.sync.accepted),
                                    React.createElement("small", { className: "".concat(statusTypeFilter === FilterStatusType.selected ? 'bg-secondary-dark' : 'bg-dark', " align-middle rounded-pill font-weight-bold text-white px-2 py-1 ml-2") }, selectedCount))),
                            React.createElement("li", { className: "nav-item mx-3 px-1" },
                                React.createElement("a", { className: "nav-link p-0 py-2 ".concat(statusTypeFilter === FilterStatusType.rejected ? 'active' : ''), "aria-pressed": statusTypeFilter === FilterStatusType.rejected, role: "button", href: "#", onClick: function (e) { return handleStatusTypeFilterChange(e, FilterStatusType.rejected); } },
                                    React.createElement("span", { className: "d-inline-block align-middle" }, client_messages.sync.rejected),
                                    React.createElement("small", { className: "".concat(statusTypeFilter === FilterStatusType.rejected ? 'bg-secondary-dark' : 'bg-dark', " align-middle rounded-pill font-weight-bold text-white px-2 py-1 ml-2") }, rejectedCount))),
                            React.createElement("li", { className: "nav-item mx-3 px-1" },
                                React.createElement("a", { className: "nav-link p-0 py-2 ".concat(statusTypeFilter === FilterStatusType.notSelected ? 'active' : ''), "aria-pressed": statusTypeFilter === FilterStatusType.notSelected, role: "button", href: "#", onClick: function (e) { return handleStatusTypeFilterChange(e, FilterStatusType.notSelected); } },
                                    React.createElement("span", { className: "d-inline-block align-middle" }, client_messages.sync.not_accepted),
                                    startGetSyncListResponse.data &&
                                        React.createElement("small", { className: "".concat(statusTypeFilter === FilterStatusType.notSelected ? 'bg-secondary-dark' : 'bg-dark', " align-middle rounded-pill font-weight-bold text-white px-2 py-1 ml-2") }, startGetSyncListResponse.data.length - selectedCount - rejectedCount))))),
                    React.createElement("div", { className: "d-flex flex-column pb-4" },
                        React.createElement(AsyncJsonTable, { tableId: tableId, dataType: SyncListRow, apiService: endpoints.GET_SYNC_LIST, externalApi: null, prefetchHandler: listPrefetchHandler, fetchCallback: null, showHeader: false, columns: null, footerClass: "mt-3 px-4", isMultiselect: true, pageSizeAll: false }))),
            (syncStatusResponse && (isRequestingSync || (!isRequestingSync && showSyncProcessProgress))) &&
                React.createElement("div", { className: "m-5 p-3" },
                    React.createElement("div", { className: "d-flex flex-column align-items-center mt-n2" },
                        React.createElement("span", { className: "fa-container-shadow fa-container-shadow-secondary" },
                            React.createElement("i", { className: "fad ".concat(syncStatusResponse.stage === SyncProcessStage.completed ? 'fa-check' : syncStatusResponse.stage === SyncProcessStage.error ? 'fa-ban mb-1' : 'fa-sync-alt fa-spin mb-2'), "aria-hidden": true })),
                        React.createElement("div", { className: "my-2 font-weight-bold fs-sm" }, getSyncProcessStageName(syncStatusResponse.stage)),
                        React.createElement("div", { className: "bg-lightest rounded px-3 py-2 text-uppercase" },
                            React.createElement("div", { className: "d-flex align-items-center p-1 text-center text-muted" },
                                React.createElement("div", null,
                                    React.createElement("div", { className: "h5 mb-0 text-body" }, syncStatusResponse.remainingObjects),
                                    React.createElement("div", { className: "fs-xs" }, client_messages.sync.remaining)),
                                React.createElement("div", { className: "mx-3 px-3 border-left border-right" },
                                    React.createElement("div", { className: "h5 mb-0 text-body" },
                                        syncStatusResponse.percentage,
                                        "%"),
                                    React.createElement("div", { className: "fs-xs" }, client_messages.sync.progress)),
                                React.createElement("div", null,
                                    React.createElement("div", { className: "h5 mb-0 text-body" }, syncStatusResponse.completedObjects),
                                    React.createElement("div", { className: "fs-xs" }, client_messages.sync.completed))))))),
        React.createElement(ReactBootstrap.Modal, { className: "modal-prompt", show: showDataExportConfirmation },
            React.createElement("div", { className: "modal-ribbon bg-ribbon position-absolute rounded-top py-1 w-100", role: "presentation" }),
            React.createElement(ReactBootstrap.Modal.Body, null,
                React.createElement("div", { className: "h4" }, client_messages.sync.exporting_sync_list),
                React.createElement("div", { className: "fs-md mb-4" }, client_messages.sync.data_export_message),
                React.createElement("div", { className: "fs-md mb-4" },
                    React.createElement("b", null, client_messages.sync.data_export_note)),
                dataExportResource &&
                    React.createElement("a", { className: "d-inline-block text-decoration-none text-nowrap mb-2", href: context + '/download/excel' + '.form?resource=' + dataExportResource, onClick: function (e) { return onCsvFileDownload(); } },
                        React.createElement("i", { className: "far fa-download mr-2", "aria-hidden": "true" }),
                        React.createElement("span", { className: "task-file fs-xs text-monospace" }, dataExportFileName),
                        React.createElement("br", null),
                        React.createElement("br", null)),
                !dataExportResource &&
                    React.createElement("div", { className: "fs-md mb-4" }, client_messages.sync.exporting),
                React.createElement("div", { className: "container" },
                    React.createElement("div", { className: "text-center" },
                        React.createElement(ReactBootstrap.Button, { variant: "lightest", className: "text-uppercase text-body", onClick: handleDataExportClosedModal }, client_messages.text.close))))),
        React.createElement(ReactBootstrap.Modal, { className: "modal-prompt", show: showGenerateListConfirmation, onHide: !isGeneratingList ? handleGenerateListConfirmationModalClose : null },
            React.createElement("div", { className: "modal-ribbon bg-ribbon position-absolute rounded-top py-1 w-100", role: "presentation" }),
            React.createElement(ReactBootstrap.Modal.Body, null,
                React.createElement("div", { className: "h4" }, client_messages.title.would_you_like_to_continue),
                React.createElement("div", { className: "fs-md mb-4" }, client_messages.sync.you_are_about_to_generate_a_new_sync_list_as_a_result_the_current_list_will_be_cleared),
                React.createElement(ReactBootstrap.Button, { variant: "primary", className: "text-uppercase mr-3", disabled: isGeneratingList, onClick: !isGeneratingList ? handleGenerateListConfirmation : null }, isGeneratingList ? React.createElement(React.Fragment, null,
                    React.createElement("i", { className: "fad fa-spinner-third fa-lg fa-spin mx-4", "aria-hidden": true }),
                    React.createElement("span", { className: "sr-only" }, client_messages.sync.loading)) : client_messages.sync.continue),
                React.createElement(ReactBootstrap.Button, { variant: "lightest", className: "text-uppercase text-body", disabled: isGeneratingList, onClick: !isGeneratingList ? handleGenerateListConfirmationModalClose : null }, client_messages.sync.cancel))),
        React.createElement(ReactBootstrap.Modal, { className: "modal-prompt", show: showInitSyncProcessConfirmation, onHide: !isSyncing ? handleInitSyncProcessConfirmationModalClose : null },
            React.createElement("div", { className: "modal-ribbon bg-ribbon position-absolute rounded-top py-1 w-100", role: "presentation" }),
            React.createElement(ReactBootstrap.Modal.Body, null,
                React.createElement("div", { className: "h4" }, client_messages.title.would_you_like_to_continue),
                React.createElement("div", { className: "fs-md mb-2 pb-1" },
                    client_messages.sync.you_are_about_to_sync,
                    React.createElement("strong", { className: "h4 mx-2 px-2 py-1 rounded bg-primary-lightest text-primary" }, new Intl.NumberFormat('en-US').format(selectedCount)),
                    selectedCount === 1 ? client_messages.sync.object : client_messages.sync.objects,
                    ":"),
                resultsInfo &&
                    React.createElement(React.Fragment, null,
                        React.createElement("div", { className: "border rounded mb-3" },
                            React.createElement("div", { className: "px-3 py-2 border-bottom" },
                                React.createElement("div", { className: "d-flex align-items-center justify-content-between text-uppercase" },
                                    React.createElement("span", { className: "text-muted pr-2" }, resultsInfo.sourceInstance),
                                    React.createElement("span", { className: "badge badge-pill badge-dark py-1 px-2 fs-xs" },
                                        React.createElement("small", { className: "font-weight-bold align-middle" }, client_messages.sync.source))),
                                resultsInfo.sourceDocumentName),
                            React.createElement("div", { className: "px-3 py-2" },
                                React.createElement("div", { className: "d-flex align-items-center justify-content-between text-uppercase" },
                                    React.createElement("span", { className: "text-muted pr-2" }, resultsInfo.targetInstance),
                                    React.createElement("span", { className: "badge badge-pill badge-primary py-1 px-2 fs-xs" },
                                        React.createElement("small", { className: "font-weight-bold align-middle" }, client_messages.sync.target))),
                                resultsInfo.targetDocumentName)),
                        React.createElement("div", { className: "mb-3 px-3 py-2 border rounded" },
                            React.createElement("div", { className: "text-muted" }, client_messages.sync.languages),
                            React.createElement("div", { className: "d-flex flex-wrap" }, resultsInfo.selectedLanguages.map(function (lang, index) {
                                return (React.createElement("span", { key: index, className: "text-nowrap" },
                                    resultsInfo.selectedLanguages.length > (index + 1) ? "".concat(lang.displayName, ",") : lang.displayName,
                                    "\u00A0"));
                            })))),
                rejectedCount > 0 &&
                    React.createElement("div", { className: "fs-md mb-2 pb-1" },
                        "Rejecting changes in",
                        React.createElement("strong", { className: "h4 mx-2 px-2 py-1 rounded bg-danger-lightest text-danger" }, new Intl.NumberFormat('en-US').format(rejectedCount)),
                        rejectedCount === 1 ? client_messages.sync.object : client_messages.sync.objects,
                        "."),
                deleteWcDueToSyncActiveOnly > 0 &&
                    React.createElement("div", { className: "fs-md mb-2 pb-1" },
                        client_messages.sync.working_copy_will_be_deleted_in,
                        React.createElement("strong", { className: "h4 mx-2 px-2 py-1 rounded bg-danger-lightest text-danger" }, new Intl.NumberFormat('en-US').format(deleteWcDueToSyncActiveOnly)),
                        deleteWcDueToSyncActiveOnly === 1 ? client_messages.sync.object : client_messages.sync.objects,
                        "."),
                React.createElement(ReactBootstrap.Form.Group, { controlId: "syncPriorityCheck", className: "fs-md" },
                    React.createElement(ReactBootstrap.Form.Check, { id: "syncPriorityCheck", type: "checkbox", label: client_messages.sync.sync_priorities_for_all_zones, checked: settings.syncMessagePriority, disabled: isSyncing, onChange: !isSyncing ? handleSyncPriorityChange : null, custom: true })),
                React.createElement(ReactBootstrap.Button, { variant: "primary", className: "text-uppercase mr-3", disabled: isSyncing, onClick: !isSyncing ? handleInitSyncProcessConfirmation : null }, isSyncing ? React.createElement(React.Fragment, null,
                    React.createElement("i", { className: "fad fa-spinner-third fa-lg fa-spin mx-4", "aria-hidden": true }),
                    React.createElement("span", { className: "sr-only" }, client_messages.sync.loading)) : client_messages.sync.continue),
                React.createElement(ReactBootstrap.Button, { variant: "lightest", className: "text-uppercase text-body", disabled: isSyncing, onClick: !isSyncing ? handleInitSyncProcessConfirmationModalClose : null }, client_messages.sync.cancel))),
        React.createElement(ReactBootstrap.Modal, { className: "modal-prompt", show: showInitHideChangesProcessConfirmation, onHide: !isSyncing ? handleInitHideChangesProcessConfirmationModalClose : null },
            React.createElement("div", { className: "modal-ribbon bg-ribbon position-absolute rounded-top py-1 w-100", role: "presentation" }),
            React.createElement(ReactBootstrap.Modal.Body, null,
                React.createElement("div", { className: "h4" }, client_messages.title.would_you_like_to_continue),
                React.createElement("div", { className: "fs-md mb-2 pb-1" },
                    client_messages.sync.you_are_about_to_reject_changes,
                    React.createElement("strong", { className: "h4 mx-2 px-2 py-1 rounded bg-primary-lightest text-primary" }, new Intl.NumberFormat('en-US').format(selectedCount)),
                    selectedCount === 1 ? client_messages.sync.object : client_messages.sync.objects,
                    ":"),
                resultsInfo &&
                    React.createElement(React.Fragment, null,
                        React.createElement("div", { className: "border rounded mb-3" },
                            React.createElement("div", { className: "px-3 py-2 border-bottom" },
                                React.createElement("div", { className: "d-flex align-items-center justify-content-between text-uppercase" },
                                    React.createElement("span", { className: "text-muted pr-2" }, resultsInfo.sourceInstance),
                                    React.createElement("span", { className: "badge badge-pill badge-dark py-1 px-2 fs-xs" },
                                        React.createElement("small", { className: "font-weight-bold align-middle" }, client_messages.sync.source))),
                                resultsInfo.sourceDocumentName)),
                        React.createElement("div", { className: "mb-3 px-3 py-2 border rounded" },
                            React.createElement("div", { className: "text-muted" }, client_messages.sync.languages),
                            React.createElement("div", { className: "d-flex flex-wrap" }, resultsInfo.selectedLanguages.map(function (lang, index) {
                                return (React.createElement("span", { key: index, className: "text-nowrap" },
                                    resultsInfo.selectedLanguages.length > (index + 1) ? "".concat(lang.displayName, ",") : lang.displayName,
                                    "\u00A0"));
                            })))),
                React.createElement(ReactBootstrap.Button, { variant: "primary", className: "text-uppercase mr-3", disabled: isSyncing, onClick: !isSyncing ? handleInitHideChangesProcessConfirmation : null }, isSyncing ? React.createElement(React.Fragment, null,
                    React.createElement("i", { className: "fad fa-spinner-third fa-lg fa-spin mx-4", "aria-hidden": true }),
                    React.createElement("span", { className: "sr-only" }, client_messages.sync.loading)) : client_messages.sync.continue),
                React.createElement(ReactBootstrap.Button, { variant: "lightest", className: "text-uppercase text-body", disabled: isSyncing, onClick: !isSyncing ? handleInitHideChangesProcessConfirmationModalClose : null }, client_messages.sync.cancel))),
        React.createElement(ReactBootstrap.Modal, { className: "modal-prompt", show: showInitSyncPrioritiesProcessConfirmation, onHide: !isSyncingPriorities ? handleInitSyncPrioritiesProcessConfirmationModalClose : null },
            React.createElement("div", { className: "modal-ribbon bg-ribbon position-absolute rounded-top py-1 w-100", role: "presentation" }),
            React.createElement(ReactBootstrap.Modal.Body, null,
                React.createElement("div", { className: "h4" }, client_messages.title.would_you_like_to_continue),
                React.createElement("div", { className: "fs-md mb-3" }, client_messages.sync.you_are_about_to_sync_priorities_for_all_zones),
                React.createElement(ReactBootstrap.Button, { variant: "primary", className: "text-uppercase mr-3", disabled: isSyncingPriorities, onClick: !isSyncingPriorities ? handleInitSyncPrioritiesProcessConfirmation : null }, isSyncingPriorities ? React.createElement(React.Fragment, null,
                    React.createElement("i", { className: "fad fa-spinner-third fa-lg fa-spin mx-4", "aria-hidden": true }),
                    React.createElement("span", { className: "sr-only" }, client_messages.sync.loading)) : client_messages.sync.continue),
                React.createElement(ReactBootstrap.Button, { variant: "lightest", className: "text-uppercase text-body", disabled: isSyncingPriorities, onClick: !isSyncingPriorities ? handleInitSyncPrioritiesProcessConfirmationModalClose : null }, client_messages.sync.cancel))),
        (syncListTable && dependenciesToDisplay) &&
            React.createElement(ReactBootstrap.Modal, { show: showDependenciesListModal, onHide: handleDependenciesListModalClose },
                React.createElement("div", { className: "modal-ribbon bg-ribbon position-absolute rounded-top py-1 w-100", role: "presentation" }),
                React.createElement(ReactBootstrap.Modal.Header, { className: "border-0" },
                    React.createElement("h4", { className: "modal-title" }, client_messages.sync.dependencies_list),
                    React.createElement(ReactBootstrap.Button, { bsPrefix: "close", "aria-label": "Close", onClick: handleDependenciesListModalClose },
                        React.createElement("i", { className: "far fa-times m-2", "aria-hidden": "true" }))),
                React.createElement(ReactBootstrap.Modal.Body, { className: "pt-0" },
                    React.createElement("hgroup", null,
                        React.createElement("h5", { className: "text-info" }, dependenciesToDisplay.name),
                        React.createElement("h6", { className: "mb-3 font-weight-normal" },
                            client_messages.sync.references_the_following_objects,
                            ":")),
                    React.createElement("div", { className: "table-responsive overflow-y-hidden" },
                        React.createElement("table", { className: "table m-0" },
                            React.createElement("thead", null,
                                React.createElement("tr", { className: "text-uppercase" },
                                    React.createElement("th", { scope: "col", className: "pl-0" }, "#"),
                                    React.createElement("th", { scope: "col" }, client_messages.sync.name),
                                    React.createElement("th", { scope: "col" }, client_messages.sync.type),
                                    React.createElement("th", { scope: "col", className: "pr-0 text-center" }, client_messages.sync.text))),
                            React.createElement("tbody", null, dependenciesToDisplay.sourceDependencies.needExisting.map(function (dep, index) {
                                return (React.createElement("tr", { key: index },
                                    React.createElement("th", { scope: "row", className: "pl-0 align-middle", style: { width: 0 } }, index + 1),
                                    React.createElement("td", { className: "align-middle" }, dep.name),
                                    React.createElement("td", { className: "align-middle text-nowrap", style: { width: 0 } }, dep.objectTypeName),
                                    React.createElement("td", { className: "pr-0 align-middle", style: { width: 0 } },
                                        !dep.noAction &&
                                            React.createElement("div", { className: "d-flex justify-content-center px-4" },
                                                React.createElement(ReactBootstrap.Form.Check, { type: "checkbox", id: "dependency_".concat(dep.id, "_check"), label: React.createElement("span", { className: "sr-only" }, "Select"), onChange: function (e) { return handleDependencySelect(e.target.checked, dep.id, false); }, checked: syncListTable.getSelectedIds().indexOf(getTransformedObjectId(dep.objectType, dep.id, dep.statusCode)) >= 0, disabled: syncListTable.getSelectedIds().indexOf(getTransformedObjectId(dep.objectType, dep.id, dep.statusCode)) >= 0 && isDependencyMandatory(dep) && dependenciesToDisplay.selected, className: "py-2 mr-1 no-text", custom: true, readOnly: true })),
                                        dep.noAction &&
                                            React.createElement("span", { className: "badge badge-light py-1 text-body text-uppercase align-middle" }, client_messages.sync.no_actions))));
                            })))))),
        (syncListTable && referencesToDisplay) &&
            React.createElement(ReactBootstrap.Modal, { show: showReferencesListModal, onHide: handleReferencesListModalClose },
                React.createElement("div", { className: "modal-ribbon bg-ribbon position-absolute rounded-top py-1 w-100", role: "presentation" }),
                React.createElement(ReactBootstrap.Modal.Header, { className: "border-0" },
                    React.createElement("h4", { className: "modal-title" }, client_messages.sync.references_list),
                    React.createElement(ReactBootstrap.Button, { bsPrefix: "close", "aria-label": "Close", onClick: handleReferencesListModalClose },
                        React.createElement("i", { className: "far fa-times m-2", "aria-hidden": "true" }))),
                React.createElement(ReactBootstrap.Modal.Body, { className: "pt-0" },
                    React.createElement("hgroup", null,
                        React.createElement("h5", { className: "text-info" }, referencesToDisplay.name),
                        React.createElement("h6", { className: "mb-3 font-weight-normal" },
                            client_messages.sync.it_is_referenced_by_the_following_objects,
                            ":")),
                    React.createElement("div", { className: "table-responsive overflow-y-hidden" },
                        React.createElement("table", { className: "table m-0" },
                            React.createElement("thead", null,
                                React.createElement("tr", { className: "text-uppercase" },
                                    React.createElement("th", { scope: "col", className: "pl-0" }, "#"),
                                    React.createElement("th", { scope: "col" }, "".concat(referencesToDisplay.objectType === ObjectType.parameterGroup ? client_messages.sync.document : client_messages.sync.object)),
                                    referencesToDisplay.objectType !== ObjectType.parameterGroup &&
                                        React.createElement("th", { scope: "col" }, client_messages.sync.type),
                                    React.createElement("th", { scope: "col", className: "pr-0 text-center" }, client_messages.sync.text))),
                            React.createElement("tbody", null, referencesToDisplay.targetDependencies.objects.map(function (ref, index) {
                                return (React.createElement("tr", { key: index },
                                    React.createElement("th", { scope: "row", className: "pl-0 align-middle", style: { width: 0 } }, index + 1),
                                    React.createElement("td", { className: "align-middle" },
                                        React.createElement("div", { className: "".concat(ref.documentName ? 'font-weight-bold' : '') }, (ref.noAction || referencesToDisplay.objectType === ObjectType.parameterGroup) ? ref.name : getDependencySyncObject(true, ref.id).name),
                                        ref.documentName &&
                                            React.createElement("div", { className: "text-muted mt-1" }, ref.documentName)),
                                    referencesToDisplay.objectType !== ObjectType.parameterGroup &&
                                        React.createElement("td", { className: "align-middle text-nowrap", style: { width: 0 } }, ref.objectTypeName),
                                    React.createElement("td", { className: "pr-0 align-middle", style: { width: 0 } },
                                        !ref.noAction &&
                                            React.createElement("div", { className: "d-flex justify-content-center px-4" },
                                                React.createElement(ReactBootstrap.Form.Check, { type: "checkbox", id: "reference_".concat(ref.id, "_check"), label: React.createElement("span", { className: "sr-only" }, client_messages.sync.select), onChange: function (e) { return handleDependencySelect(e.target.checked, ref.id, true); }, checked: syncListTable.getSelectedIds().indexOf(getReferencedByObjectId(true, ref.id)) >= 0, className: "py-2 mr-1 no-text", custom: true, readOnly: true })),
                                        ref.noAction &&
                                            React.createElement("span", { className: "badge badge-light py-1 text-body text-uppercase align-middle" }, client_messages.sync.no_actions))));
                            }))))))));
};
