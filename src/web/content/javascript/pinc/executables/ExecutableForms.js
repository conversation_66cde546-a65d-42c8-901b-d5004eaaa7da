var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var ExecutableAddForm = function () {
    var pincConfig = React.useContext(PincApiContext).pincConfig;
    var addExecutable = React.useContext(ExecutablesContext).addExecutable;
    return (React.createElement(ExecutableForm, { formType: ModalType.Add, executable: {
            name: '',
            type: ExecutableType.Java,
            description: '',
            companyId: formatUuid(pincConfig.companyId)
        }, onSubmit: addExecutable }));
};
var ExecutableEditForm = function (props) {
    var executable = props.executable;
    var updateExecutable = React.useContext(ExecutablesContext).updateExecutable;
    return (React.createElement(ExecutableForm, { formType: ModalType.Edit, executable: executable, onSubmit: updateExecutable }));
};
var ExecutableForm = function (props) {
    var formType = props.formType, executable = props.executable, onSubmit = props.onSubmit;
    var _a = React.useContext(PincApiContext), errors = _a.errors, setErrors = _a.setErrors;
    var closeDialog = React.useContext(ExecutablesContext).closeDialog;
    var companies = React.useContext(CompaniesContext).companies;
    var _b = React.useState(executable.name), name = _b[0], setName = _b[1];
    var _c = React.useState(executable.type), type = _c[0], setType = _c[1];
    var _d = React.useState(executable.description), description = _d[0], setDescription = _d[1];
    var _e = React.useState(executable.companyId), companyId = _e[0], setCompanyId = _e[1];
    var hasValidationErrors = function () {
        var newErrors = [];
        if (name.trim().length == 0) {
            newErrors.push(client_messages.pinc.name_required);
        }
        if (description.length == 0) {
            newErrors.push(client_messages.pinc.description_required);
        }
        setErrors(newErrors);
        return newErrors.length > 0;
    };
    var onSubmitClick = function () {
        if (hasValidationErrors()) {
            return;
        }
        onSubmit({ id: executable.id, name: name, type: type, description: description, companyId: companyId });
    };
    var AllowedExecutableTypes = new Map(__spreadArray([], Array.from(ExecutableTypeLabels).filter(function (_a) {
        var key = _a[0];
        return key !== ExecutableType.Java;
    }), true));
    return (React.createElement(ModalDialogTemplate, { title: client_messages.pinc.executable, type: formType, body: React.createElement("div", { className: "container" },
            React.createElement(TextInput, { label: client_messages.pinc.name, value: name, onChange: setName }),
            React.createElement(FormInputDecorator, { label: client_messages.pinc.type, input: React.createElement(SelectDropdown, { options: AllowedExecutableTypes, selectedKey: type, onChange: function (value) { return setType(value); }, disabled: formType === ModalType.Edit }) }),
            React.createElement(MultilineInput, { label: client_messages.pinc.description, value: description, onChange: setDescription, rows: 3, maxLength: 1024 }),
            React.createElement(FormInputDecorator, { label: client_messages.pinc.company, input: React.createElement(SelectDropdown, { options: companies, selectedKey: companyId, onChange: function (value) { return setCompanyId(value); }, disabled: formType === ModalType.Edit }) })), onSubmit: onSubmitClick, onCancel: closeDialog, errors: errors }));
};
