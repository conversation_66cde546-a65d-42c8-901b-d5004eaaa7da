var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var EngineHarmonieModuleName;
(function (EngineHarmonieModuleName) {
    EngineHarmonieModuleName["pydlexec"] = "pydlexec";
    EngineHarmonieModuleName["techcodr"] = "techcodr";
    EngineHarmonieModuleName["techgof"] = "techgof";
    EngineHarmonieModuleName["techps"] = "techps";
    EngineHarmonieModuleName["techpdf"] = "techpdf";
    EngineHarmonieModuleName["techsort"] = "techsort";
    EngineHarmonieModuleName["techpcl"] = "techpcl";
    EngineHarmonieModuleName["techafp"] = "techafp";
    EngineHarmonieModuleName["techcarry"] = "techcarry";
    EngineHarmonieModuleName["python_executable"] = "python_executable"; //not part of the original (api) enum
})(EngineHarmonieModuleName || (EngineHarmonieModuleName = {}));
var EngineHarmonieStepTemplate = __assign(__assign({}, __assign(__assign({}, EngineStepTemplate), { args: undefined })), { modules: [] });
var EngineHarmonieStep = function (props) {
    var settings = props.settings, setSettings = props.setSettings;
    if (setSettings === undefined) {
        return React.createElement(ViewEngineHarmonieStep, { settings: settings });
    }
    else {
        return React.createElement(EditEngineHarmonieStep, { settings: settings, setSettings: setSettings });
    }
};
var ViewEngineHarmonieStep = function (props) {
    var _a;
    var settings = props.settings;
    return (React.createElement("div", { className: "container" },
        React.createElement(ViewEngineSetting, { engineId: settings.engineId, engineVersionId: settings.engineVersionId, enginePath: settings.enginePath, licenseType: settings.licenseType, args: 'moduleName' in settings ? settings.args : null }),
        'moduleName' in settings &&
            React.createElement(React.Fragment, null,
                React.createElement("div", { className: "row mt-3" },
                    React.createElement("div", { className: "col font-weight-bold" }, client_messages.pinc.module_name)),
                React.createElement("div", { className: "row" },
                    React.createElement("div", { className: "col" }, settings.moduleName))),
        'modules' in settings &&
            React.createElement(React.Fragment, null,
                React.createElement("div", { className: "row mt-3" },
                    React.createElement("div", { className: "col font-weight-bold" }, client_messages.pinc.modules)),
                React.createElement("div", { className: "row" },
                    React.createElement("div", { className: "col" }, (_a = settings.modules) === null || _a === void 0 ? void 0 : _a.map(function (module, index) {
                        return React.createElement("div", { key: index, className: "container card mt-2 pb-3" },
                            React.createElement(ViewModuleItem, { module: module }));
                    }))))));
};
var ViewModuleItem = function (props) {
    var module = props.module;
    if ('executableId' in module) {
        return React.createElement(ViewExecutableModuleItem, { module: module });
    }
    else {
        return (React.createElement(React.Fragment, null,
            React.createElement("div", { className: "row mt-3" },
                React.createElement("div", { className: "col font-weight-bold" }, client_messages.pinc.name),
                React.createElement("div", { className: "col font-weight-bold" }, client_messages.pinc.command_line_arguments)),
            React.createElement("div", { className: "row" },
                React.createElement("div", { className: "col" }, module.name),
                React.createElement("div", { className: "col" }, module.args.join(', ')))));
    }
};
var ViewExecutableModuleItem = function (props) {
    var _a = props.module, executableId = _a.executableId, executableVersionId = _a.executableVersionId, args = _a.args, runtimeArgs = _a.runtimeArgs;
    var executableLabels = useExecutableLabels(executableId, executableVersionId);
    return (React.createElement(React.Fragment, null,
        React.createElement("div", { className: "row mt-3" },
            React.createElement("div", { className: "col font-weight-bold" }, client_messages.pinc.executable),
            React.createElement("div", { className: "col font-weight-bold" }, client_messages.pinc.executable_version),
            React.createElement("div", { className: "col font-weight-bold" }, client_messages.pinc.command_line_arguments),
            React.createElement("div", { className: "col font-weight-bold" }, client_messages.pinc.python_runtime_arguments)),
        React.createElement("div", { className: "row" },
            React.createElement("div", { className: "col" }, executableLabels.name),
            React.createElement("div", { className: "col" }, executableLabels.version),
            React.createElement("div", { className: "col" }, args.join(', ')),
            React.createElement("div", { className: "col" }, runtimeArgs.join(', ')))));
};
var EditEngineHarmonieStep = function (props) {
    var _a;
    var settings = props.settings, setSettings = props.setSettings;
    var addModule = function (moduleName) {
        if (moduleName === EngineHarmonieModuleName.python_executable) {
            setSettings(__assign(__assign({}, settings), { modules: __spreadArray(__spreadArray([], settings.modules, true), [{ executableId: '', executableVersionId: '', args: [], runtimeArgs: [] }], false) }));
        }
        else {
            setSettings(__assign(__assign({}, settings), { modules: __spreadArray(__spreadArray([], settings.modules, true), [{ name: moduleName, args: [] }], false) }));
        }
    };
    var deleteModule = function (index) {
        var modules = __spreadArray([], settings.modules, true);
        modules.splice(index, 1);
        setSettings(__assign(__assign({}, settings), { modules: modules }));
    };
    // Legacy way with moduleName
    if ('moduleName' in settings) {
        return (React.createElement(React.Fragment, null,
            React.createElement(EditEngineSettings, { settings: settings, setSettings: setSettings, engineType: EngineType.HARMONIE }),
            React.createElement(FormInputDecorator, { label: client_messages.pinc.module_name, input: React.createElement(SelectDropdown, { options: new Map(Object.entries(EngineHarmonieModuleName).filter(function (_a) {
                        var key = _a[0], value = _a[1];
                        return value !== EngineHarmonieModuleName.python_executable;
                    })), selectedKey: settings.moduleName, onChange: function (moduleName) { return setSettings(__assign(__assign({}, settings), { moduleName: moduleName })); } }) })));
    }
    return (React.createElement(React.Fragment, null,
        React.createElement(EditEngineSettings, { settings: settings, setSettings: setSettings, engineType: EngineType.HARMONIE, noArgs: true }),
        React.createElement("div", { className: "row mt-3" },
            React.createElement("div", { className: "col font-weight-bold" }, client_messages.pinc.modules)), (_a = settings.modules) === null || _a === void 0 ? void 0 :
        _a.map(function (module, index) {
            return React.createElement("div", { key: index, className: "container shadow mt-2 pt-2 pb-3" },
                React.createElement("div", { className: "row" },
                    React.createElement("div", { className: "col font-weight-bold" }, 'name' in module ? module.name : client_messages.pinc.python_executable),
                    React.createElement("div", { className: "col d-flex justify-content-end" },
                        React.createElement("button", { type: "button", onClick: function () { return deleteModule(index); }, className: "btn btn-sm btn-outline-dark" },
                            React.createElement("i", { className: "far fa-trash-alt", "aria-hidden": "true" })))),
                React.createElement(EditModuleItem, { module: module, setModule: function (module) {
                        var modules = __spreadArray([], settings.modules, true);
                        modules[index] = module;
                        setSettings(__assign(__assign({}, settings), { modules: modules }));
                    } }));
        }),
        React.createElement(ReactBootstrap.DropdownButton, { id: 'add_module_button', title: React.createElement(React.Fragment, null,
                React.createElement("i", { className: "far fa-plus-circle mr-2", "aria-hidden": "true" }),
                client_messages.pinc.add_module), as: ReactBootstrap.ButtonGroup, variant: 'outline-dark', size: 'sm', className: 'mt-3' }, Object.values(EngineHarmonieModuleName).map(function (module) {
            return React.createElement(ReactBootstrap.Dropdown.Item, { key: module, onClick: function () { return addModule(module); } }, module);
        }))));
};
var EditModuleItem = function (props) {
    var module = props.module, setModule = props.setModule;
    if ('executableId' in module) {
        return (React.createElement(EditExecutableModuleItem, { module: module, setModule: function (module) { return setModule(module); } }));
    }
    else {
        return (React.createElement(React.Fragment, null,
            React.createElement(ArrayInput, { label: client_messages.pinc.command_line_arguments, values: module.args, setValues: function (args) { return setModule(__assign(__assign({}, module), { args: args })); } })));
    }
};
var EditExecutableModuleItem = function (props) {
    var module = props.module, setModule = props.setModule;
    var applicationVersion = React.useContext(ApplicationVersionsContext).entity;
    var executables = useExecutables(true);
    var executableVersions = useExecutableVersions(module.executableId, applicationVersion.os, applicationVersion.arch);
    return (React.createElement(React.Fragment, null,
        React.createElement(FormInputDecorator, { label: client_messages.pinc.executable, input: React.createElement(SelectDropdown, { options: executables, selectedKey: module.executableId, onChange: function (id) { return setModule(__assign(__assign({}, module), { executableId: id })); } }) }),
        React.createElement(FormInputDecorator, { label: client_messages.pinc.executable_version, input: React.createElement(SelectDropdown, { options: executableVersions, selectedKey: module.executableVersionId, onChange: function (id) { return setModule(__assign(__assign({}, module), { executableVersionId: id })); } }) }),
        React.createElement(ArrayInput, { label: client_messages.pinc.command_line_arguments, values: module.args, setValues: function (args) { return setModule(__assign(__assign({}, module), { args: args })); } }),
        React.createElement(ArrayInput, { label: client_messages.pinc.python_runtime_arguments, values: module.runtimeArgs, setValues: function (args) { return setModule(__assign(__assign({}, module), { runtimeArgs: args })); } })));
};
