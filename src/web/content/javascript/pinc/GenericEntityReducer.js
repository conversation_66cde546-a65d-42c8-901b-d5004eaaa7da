var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
//todo: Revisit SUCCESS, SET and SUCCESS_AND_SET scenarios
var ActionType;
(function (ActionType) {
    ActionType["ADD"] = "ADD";
    ActionType["EDIT"] = "EDIT";
    ActionType["DELETE"] = "DELETE";
    ActionType["VIEW"] = "VIEW";
    ActionType["CANCEL"] = "CANCEL";
    ActionType["SUCCESS"] = "SUCCESS";
    ActionType["ERROR"] = "ERROR";
    ActionType["SET"] = "SET";
    ActionType["SUCCESS_AND_SET"] = "SUCCESS_AND_SET";
    ActionType["SET_ENDPOINT"] = "SET_ENDPOINT";
    ActionType["SET_LOADING"] = "SET_LOADING";
})(ActionType || (ActionType = {}));
var genericEntityReducer = function (state, action) {
    switch (action.type) {
        case ActionType.ADD:
        case ActionType.EDIT:
        case ActionType.DELETE:
            return __assign(__assign({}, state), { showDialog: true, actionType: action.type, actionPayload: action.payload });
        case ActionType.VIEW:
            return __assign(__assign({}, state), { showDialog: true, actionType: action.type, actionPayload: action.payload });
        case ActionType.SUCCESS:
            return __assign(__assign({}, state), { showDialog: false, actionType: null, actionPayload: action.payload !== undefined ? action.payload : null, refresh: state.refresh + 1, actionErrors: [], loading: false });
        case ActionType.CANCEL:
            return __assign(__assign({}, state), { showDialog: false, actionType: null, actionPayload: null, actionErrors: [] });
        case ActionType.ERROR:
            return __assign(__assign({}, state), { actionErrors: action.payload, loading: false });
        case ActionType.SET:
            return __assign(__assign({}, state), { entity: action.payload });
        case ActionType.SUCCESS_AND_SET:
            return __assign(__assign({}, state), { showDialog: false, actionType: null, actionPayload: null, refresh: state.refresh + 1, actionErrors: [], entity: action.payload, loading: false });
        case ActionType.SET_ENDPOINT:
            return __assign(__assign({}, state), { apiEndpoint: action.payload });
        case ActionType.SET_LOADING:
            return __assign(__assign({}, state), { loading: action.payload });
        default:
            return state;
    }
};
