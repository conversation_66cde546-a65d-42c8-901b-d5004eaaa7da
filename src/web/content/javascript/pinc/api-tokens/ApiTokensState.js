var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var ApiTokensContext = React.createContext(null);
var ApiTokensActionType;
(function (ApiTokensActionType) {
    ApiTokensActionType["ACTIVATE"] = "ACTIVATE";
    ApiTokensActionType["DEACTIVATE"] = "DEACTIVATE";
    ApiTokensActionType["EXTEND_EXPIRY"] = "EXTEND_EXPIRY";
    ApiTokensActionType["NEW_API_TOKEN"] = "NEW_API_TOKEN";
    ApiTokensActionType["RESET_TOKEN"] = "RESET_TOKEN";
})(ApiTokensActionType || (ApiTokensActionType = {}));
var apiTokensReducer = function (state, action) {
    switch (action.type) {
        case ApiTokensActionType.NEW_API_TOKEN:
            return __assign(__assign({}, state), { token: action.payload, showDialog: true, actionType: ApiTokensActionType.NEW_API_TOKEN, actionErrors: [] });
        case ApiTokensActionType.RESET_TOKEN:
            return __assign(__assign({}, state), { token: '', showDialog: false, actionType: null, refresh: state.refresh + 1 });
        case ApiTokensActionType.ACTIVATE:
        case ApiTokensActionType.DEACTIVATE:
        case ApiTokensActionType.EXTEND_EXPIRY:
            return __assign(__assign({}, state), { showDialog: true, actionType: action.type });
        default:
            return state;
    }
};
var apiTokensRootReducer = reduceReducers(genericEntityReducer, apiTokensReducer);
var ApiTokensState = function (props) {
    var _a = React.useContext(PincApiContext), pincConfig = _a.pincConfig, apiPost = _a.apiPost, bulkDelete = _a.bulkDelete, bulkPatch = _a.bulkPatch;
    var isPermitted = React.useContext(UserPermissionsContext).isPermitted;
    var initialState = {
        token: '',
        apiEndpoint: pincConfig.apiServer + getApiEndpoint(ApiEndpoint.API_TOKENS),
        showDialog: false,
        actionType: null,
        refresh: 0,
        actionErrors: []
    };
    var _b = React.useReducer(apiTokensRootReducer, initialState), state = _b[0], dispatch = _b[1];
    var addApiToken = function (name, scopes, authenticationKeyIds, companyId) {
        var newApiToken = {
            name: name.trim(),
            scopes: scopes,
            authenticationKeyIds: authenticationKeyIds,
            companyId: companyId
        };
        apiPost(state.apiEndpoint, newApiToken, function (data) {
            var token = data.id + ':' + data.code;
            dispatch({ type: ApiTokensActionType.NEW_API_TOKEN, payload: token });
        }, function (error) { return dispatch({ type: ActionType.ERROR, payload: [error] }); });
    };
    var deleteApiToken = function (ids, items) {
        if (isActionPermitted(items, PincPermission.API_TOKEN_DELETE_ADMIN)) {
            bulkDelete(ids, state.apiEndpoint, function () { return dispatch({ type: ActionType.SUCCESS }); }, function (errors) { return dispatch({ type: ActionType.ERROR, payload: errors }); });
        }
        else {
            dispatch({ type: ActionType.ERROR, payload: [client_messages.pinc.api_token_delete_not_permitted] });
        }
    };
    var activateApiToken = function (ids, items) {
        setActive(ids, items, true);
    };
    var deactivateApiToken = function (ids, items) {
        setActive(ids, items, false);
    };
    var setActive = function (ids, items, value) {
        if (isActionPermitted(items, PincPermission.API_TOKEN_UPDATE_ADMIN)) {
            bulkPatch(ids, state.apiEndpoint, { active: value }, function () { return dispatch({ type: ActionType.SUCCESS }); }, function (errors) { return dispatch({ type: ActionType.ERROR, payload: errors }); });
        }
        else {
            dispatch({ type: ActionType.ERROR, payload: [client_messages.pinc.api_token_update_not_permitted] });
        }
    };
    var extendExpiry = function (ids, items) {
        if (isActionPermitted(items, PincPermission.API_TOKEN_UPDATE_ADMIN)) {
            var expiresAt = new Date();
            expiresAt.setFullYear(expiresAt.getFullYear() + 1, expiresAt.getMonth(), expiresAt.getDate() - 1);
            bulkPatch(ids, state.apiEndpoint, { expiresAt: expiresAt.toISOString() }, function () { return dispatch({ type: ActionType.SUCCESS }); }, function (errors) { return dispatch({ type: ActionType.ERROR, payload: errors }); });
        }
        else {
            dispatch({ type: ActionType.ERROR, payload: [client_messages.pinc.api_token_update_not_permitted] });
        }
    };
    var isActionPermitted = function (items, permission) {
        if (!isPermitted(permission)) {
            for (var _i = 0, items_1 = items; _i < items_1.length; _i++) {
                var value = items_1[_i];
                if (isAdminScope(value.scopes)) {
                    return false;
                }
            }
        }
        return true;
    };
    var isAdminScope = function (scopes) {
        return scopes.filter(function (value) { return value.indexOf('admin:') > -1 || value.indexOf('monitor:') > -1; }).length > 0;
    };
    var resetToken = function () { return dispatch({ type: ApiTokensActionType.RESET_TOKEN }); };
    var openDialog = function (actionType) { return dispatch({ type: actionType }); };
    var closeDialog = function () { return dispatch({ type: ActionType.CANCEL }); };
    return (React.createElement(ApiTokensContext.Provider, { value: __assign(__assign({}, state), { addApiToken: addApiToken, resetToken: resetToken, deleteApiToken: deleteApiToken, activateApiToken: activateApiToken, deactivateApiToken: deactivateApiToken, extendExpiry: extendExpiry, openDialog: openDialog, closeDialog: closeDialog }), children: props.children }));
};
