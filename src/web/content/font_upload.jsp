<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>

    <msgpt:HeaderNew extendedScripts="true">

        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>

        <!-- The Templates plugin is included to render the upload/download listings -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/tmpl.min.js"/>
        <!-- The Load Image plugin is included for the preview images and image resizing functionality -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/load-image.min.js"/>
        <!-- The Canvas to Blob plugin is included for image resizing functionality -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/canvas-to-blob.min.js"/>
        <!-- jQuery Image Gallery -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.image-gallery.min.js"/>
        <!-- The Iframe Transport is required for browsers without support for XHR file uploads -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.iframe-transport.js"/>
        <!-- The basic File Upload plugin -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.fileupload.js"/>
        <!-- The File Upload file processing plugin -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.fileupload-fp.js"/>
        <!-- The File Upload user interface plugin -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.fileupload-ui.js"/>
        <!-- The File Upload jQuery UI plugin -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.fileupload-jui.js"/>

        <!-- CSS to style the file input field as button and adjust the jQuery UI progress bars -->
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/fileUpload/css/jquery.fileupload-ui.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

        <style type="text/css">
            .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
                background: url('../includes/javascript/jQueryPlugins/styledInterfaces/images/fill_grads.gif') repeat-x 0px 0px;
                border: 1px solid #bbb;
            }

            .ui-button .ui-button-text {
                padding: 0em 1.0em 0.1em 2.1em;
                line-height: 1.5;
                text-shadow: 0px 1px 0px #fff;
            }
            .fileupload-buttonbar .ui-button {
                margin: 5px 0px;
            }

            .fileupload-buttonbar .fileinput-button {
                margin-right: 5px;
            }

            .fileupload-progress {
                font-size: 11px;
            }

            .fileupload-progress {
                font-size: 10px;
                line-height: 12px;
            }

            .table-striped tbody > tr:nth-child(2n+1) > td, .table-striped tbody > tr:nth-child(2n+1) > th {
                background-color: #F9F9F9;
            }

            .table th, .table td {
                border-top: 1px solid #DDDDDD;
                line-height: 20px;
                padding: 8px;
                text-align: left;
                vertical-align: top;
                white-space: nowrap;
            }

            .table td.delete, .table td.cancel {
                text-align: right;
                padding-right: 20px;
            }

            .table td.preview {
                padding-left: 15px;
            }

            .table {
                border-collapse: collapse;
                border-spacing: 0;
            }

            .progress {
                background-color: #F7F7F7;
                background-image: linear-gradient(to bottom, #F5F5F5, #F9F9F9);
                background-repeat: repeat-x;
                border-radius: 4px 4px 4px 4px;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset;
                height: 20px;
                overflow: hidden;
            }

            .files .progress {
                width: 100px;
            }

            .fileSize {
                font-size: 10px;
            }

            .sandboxFileContainer {
                height: 130px;
                overflow: auto;
                background-color: #fff;
                border-radius: 0px 0px 6px 6px;
                border: 1px solid #bbb;
                border-top: 0px;
                padding-bottom: 10px;
                min-height: 90px;
                height: 100%;
                margin-bottom: 3px;
            }
            .marginLeft {
                margin-left: 30px !important;
            }

            #nameContainer {
                background-color: #eee;
                border-radius: 6px 6px 0 0;
                clear: right;
                margin-left: 38px;
                width: 718px;
                margin-bottom: 5px;
            }

            .previewImg {
                max-width: 150px;
                max-height: 75px;
                border: 1px solid #eee;
                -moz-box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
                -webkit-box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
                box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
            }

            canvas {
                border: 1px solid #eee;
                -moz-box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
                -webkit-box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
                box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
            }

            body {
                padding-top: 0px;
                line-height: 18px;
                color: #333333;
            }

            td {
                padding: 5px;
            }

            .pendingUploadContainer {
                border: 1px dashed #aaa;
                background-color: #f5f5f5;
                padding: 8px 15px;
                -webkit-border-radius: 6px;
                -moz-border-radius: 6px;
                border-radius: 6px;
            }


        </style>

        <msgpt:Script>
            <script>


                function downloadFile(path) {
                    javascriptHref(context + '/download/font.form?' + path + '&cacheStamp=' + cacheStamp);
                }

                function beforeFileUploadAdd(e, data) {
                    var permittedFileTypes = "/(\.|\/)(ttf|otf)$/i";
                    var permittedFileTypesString = client_messages.text.TTF;

                    var parts = permittedFileTypes.split('/'), modifiers = parts.pop();
                    parts.shift();
                    var acceptFileTypesRegEx = new RegExp(parts.join('/'), modifiers);

                    // Validation: File type
                    var hasValidationError = false;
                    for (var i = 0; i < data.files.length; i++) {
                        if (!acceptFileTypesRegEx.test(data.files[i].name)) {
                            var fileUploadElement = $("#fileUploadButton_add");
                            if (e.handleObj.namespace == 'fileupload2') {
                                fileUploadElement = $("#fileBoldUploadButton_add");
                            } else if (e.handleObj.namespace == 'fileupload4') {
                                fileUploadElement = $("#fileItalicUploadButton_add");
                            } else if (e.handleObj.namespace == 'fileupload6') {
                                fileUploadElement = $("#fileBoldItalicUploadButton_add");
                            }
                            fileUploadElement.popupFactory({
                                title: client_messages.content_editor.oops,
                                trigger: "instant",
                                fnSetContent: function () {
                                    return "<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" + fmtClientMessage(client_messages.text.permitted_file_upload, permittedFileTypesString) + "</div>";
                                }
                            });
                            hasValidationError = true;
                        } else {
                            // Current file extension
                            var currentExt = "TTF";
                            if (endsWith(data.files[i].name.toUpperCase(), "OTF"))
                                currentExt = "OTF";

                            // Detect duplicate file types
                            var extCount = 0;
                            for (var k = 0; k < data.originalFiles.length; k++) {
                                if (endsWith(data.originalFiles[k].name.toUpperCase(), currentExt))
                                    extCount++;
                            }
                            if(e.handleObj.namespace == 'fileupload0') {
                                $("[filename^='pendingUpload_']").each(function () {
                                    if (endsWith($(this).attr('filename').toUpperCase(), currentExt))
                                        $(this).remove();
                                });
                                if (extCount > 0) {
                                    for (var j = 0; j < data.originalFiles.length; j++) {
                                        if (endsWith(data.originalFiles[j].name.toUpperCase(), currentExt)) {
                                            $("[filename='pendingUpload_" + data.originalFiles[j].name + "']").remove();
                                            data.originalFiles.splice(j, j);
                                            break;
                                        }
                                    }
                                }
                            } else if(e.handleObj.namespace == 'fileupload2') {
                                $("[filename^='pendingUploadBold_']").each(function () {
                                    if (endsWith($(this).attr('filename').toUpperCase(), currentExt))
                                        $(this).remove();
                                });
                                if (extCount > 0) {
                                    for (var j = 0; j < data.originalFiles.length; j++) {
                                        if (endsWith(data.originalFiles[j].name.toUpperCase(), currentExt)) {
                                            $("[filename='pendingUploadBold_" + data.originalFiles[j].name + "']").remove();
                                            data.originalFiles.splice(j, j);
                                            break;
                                        }
                                    }
                                }
                            } else if(e.handleObj.namespace == 'fileupload4') {
                                $("[filename^='pendingUploadItalic_']").each(function () {
                                    if (endsWith($(this).attr('filename').toUpperCase(), currentExt))
                                        $(this).remove();
                                });
                                if (extCount > 0) {
                                    for (var j = 0; j < data.originalFiles.length; j++) {
                                        if (endsWith(data.originalFiles[j].name.toUpperCase(), currentExt)) {
                                            $("[filename='pendingUploadItalic_" + data.originalFiles[j].name + "']").remove();
                                            data.originalFiles.splice(j, j);
                                            break;
                                        }
                                    }
                                }
                            }else if(e.handleObj.namespace == 'fileupload6') {
                                $("[filename^='pendingUploadBoldItalic_']").each(function () {
                                    if (endsWith($(this).attr('filename').toUpperCase(), currentExt))
                                        $(this).remove();
                                });
                                if (extCount > 0) {
                                    for (var j = 0; j < data.originalFiles.length; j++) {
                                        if (endsWith(data.originalFiles[j].name.toUpperCase(), currentExt)) {
                                            $("[filename='pendingUploadBoldItalic_" + data.originalFiles[j].name + "']").remove();
                                            data.originalFiles.splice(j, j);
                                            break;
                                        }
                                    }
                                }
                            }

                            // Update display
                            if (endsWith(currentExt, "TTF") || endsWith(currentExt, "OTF")) {
                                if (e.handleObj.namespace == 'fileupload2') {
                                    $('#pendingBoldUploadContainer_TTF').remove();
                                } else if (e.handleObj.namespace == 'fileupload0') {
                                    $('#pendingUploadContainer_TTF').remove();
                                } else if (e.handleObj.namespace == 'fileupload4') {
                                    $('#pendingItalicUploadContainer_TTF').remove();
                                } else if (e.handleObj.namespace == 'fileupload6') {
                                    $('#pendingBoldItalicUploadContainer_TTF').remove();
                                }
                            }

                            $('#nameContainer').addClass("marginLeft")
                        }
                    }
                    return !hasValidationError;
                }

                var fileUploadKey = "${fileUploadKey}";
                var fontId = ${not empty textStyleFont ? textStyleFont.id : 'null'};
                var fileUploadCount = 0;
                $(function () {
                    $("#cancelBtn").styleActionElement();
                    $("#saveBtn").styleActionElement();

                    if ($.browser.msie != true || parseInt($.browser.version) > 10)
                        $('#dragAndDropText').show();

                    $('#fileupload').fileupload({
                        url: context + '/uploadFileHandler.form' +
                        '?type=textStyleFonts' +
                        '&action=upload' +
                        '&fileUploadSyncKey=' + fileUploadKey +
                        '&textStyleFontId=' + fontId +
                        '&tk=' + getParam('tk'),
                        acceptFileTypes: /(\.|\/)(ttf|otf)$/i,
                        sequentialUploads: true,
                        submit: function () {
                            var hasValidationError = false;

                            return !hasValidationError;
                        },
                        dropZone: null
                    });

                    $('#fileuploadBold').fileupload({
                        url: context + '/uploadFileHandler.form' +
                            '?type=textStyleFontsBold' +
                            '&action=upload' +
                            '&fileUploadSyncKey=' + fileUploadKey +
                            '&textStyleFontBoldId=' + fontId +
                            '&tk=' + getParam('tk'),
                        acceptFileTypes: /(\.|\/)(ttf|otf)$/i,
                        sequentialUploads: true,
                        submit: function () {
                            var hasValidationError = false;

                            return !hasValidationError;
                        },
                        dropZone: null
                    });

                    $('#fileuploadItalic').fileupload({
                        url: context + '/uploadFileHandler.form' +
                            '?type=textStyleFontsItalic' +
                            '&action=upload' +
                            '&fileUploadSyncKey=' + fileUploadKey +
                            '&textStyleFontItalicId=' + fontId +
                            '&tk=' + getParam('tk'),
                        acceptFileTypes: /(\.|\/)(ttf|otf)$/i,
                        sequentialUploads: true,
                        submit: function () {
                            var hasValidationError = false;

                            return !hasValidationError;
                        },
                        dropZone: null
                    });

                    $('#fileuploadBoldItalic').fileupload({
                        url: context + '/uploadFileHandler.form' +
                            '?type=textStyleFontsBoldItalic' +
                            '&action=upload' +
                            '&fileUploadSyncKey=' + fileUploadKey +
                            '&textStyleFontItalicId=' + fontId +
                            '&tk=' + getParam('tk'),
                        acceptFileTypes: /(\.|\/)(ttf|otf)$/i,
                        sequentialUploads: true,
                        submit: function () {
                            var hasValidationError = false;

                            return !hasValidationError;
                        },
                        dropZone: null
                    });

                    $('#fileupload')
                        .bind('fileuploadsubmit',function (e, data) {
                            var inputs = data.form.find(':input');
                            if (inputs.filter('[required][value=""]').first().focus().length) {
                                return false;
                            }
                            data.formData = inputs.serializeArray();
                            data.formData[data.formData.length] = {name: "metatags", value: $('#nameInput').val()};
                        })
                        .bind('fileuploaddone', function (e, data) {
                            fileUploadCount++;
                            if (fileUploadCount > 0) {
                                $('#fileUploadCompleteContainer').showEle('normal');
                                $('#filePackageNameDisplay').html($('#nameInput').val());

                                // Set the sandbox ids for the controller binding
                                var fontType = data.result.files[0].fontType;
                                var sandboxId = data.result.files[0].sandboxId;
                                if (fontType == "ttf" || fontType == "otf")
                                    $('#ttfSandboxId').val(sandboxId);
                            }
                        });

                    $('#fileuploadBold')
                        .bind('fileuploadsubmit', function (e, data) {
                            var inputs = data.form.find(':input');
                            if (inputs.filter('[required][value=""]').first().focus().length) {
                                return false;
                            }
                            data.formData = inputs.serializeArray();
                            data.formData[data.formData.length] = {name: "metatags", value: $('#nameInput').val()};
                        })
                        .bind('fileuploaddone', function (e, data) {
                            fileUploadCount++;
                            if (fileUploadCount > 0) {
                                $('#fileBoldUploadCompleteContainer').showEle('normal');
                                $('#fileBoldPackageNameDisplay').html($('#nameInput').val());

                                // Set the sandbox ids for the controller binding
                                var fontType = data.result.files[0].fontType;
                                var sandboxId = data.result.files[0].sandboxId;
                                if (fontType == "ttf" || fontType == "otf")
                                    $('#ttfBoldSandboxId').val(sandboxId);
                            }
                        });

                    $('#fileuploadItalic')
                        .bind('fileuploadsubmit', function (e, data) {
                            var inputs = data.form.find(':input');
                            if (inputs.filter('[required][value=""]').first().focus().length) {
                                return false;
                            }
                            data.formData = inputs.serializeArray();
                            data.formData[data.formData.length] = {name: "metatags", value: $('#nameInput').val()};
                        })
                        .bind('fileuploaddone', function (e, data) {
                            fileUploadCount++;
                            if (fileUploadCount > 0) {
                                $('#fileItalicUploadCompleteContainer').showEle('normal');
                                $('#fileItalicPackageNameDisplay').html($('#nameInput').val());

                                // Set the sandbox ids for the controller binding
                                var fontType = data.result.files[0].fontType;
                                var sandboxId = data.result.files[0].sandboxId;
                                if (fontType == "ttf" || fontType == "otf")
                                    $('#ttfItalicSandboxId').val(sandboxId);
                            }
                        });

                    $('#fileuploadBoldItalic')
                        .bind('fileuploadsubmit', function (e, data) {
                            var inputs = data.form.find(':input');
                            if (inputs.filter('[required][value=""]').first().focus().length) {
                                return false;
                            }
                            data.formData = inputs.serializeArray();
                            data.formData[data.formData.length] = {name: "metatags", value: $('#nameInput').val()};
                        })
                        .bind('fileuploaddone', function (e, data) {
                            fileUploadCount++;
                            if (fileUploadCount > 0) {
                                $('#fileBoldItalicUploadCompleteContainer').showEle('normal');
                                $('#fileBoldItalicPackageNameDisplay').html($('#nameInput').val());

                                // Set the sandbox ids for the controller binding
                                var fontType = data.result.files[0].fontType;
                                var sandboxId = data.result.files[0].sandboxId;
                                if (fontType == "ttf" || fontType == "otf")
                                    $('#ttfBoldItalicSandboxId').val(sandboxId);
                            }
                        });



                    $(document)
                        .bind('drop dragover', function (e) {
                            e.preventDefault();
                        })
                        .bind('drop', function (e) {
                            $(".fileinput-button").popupFactory({
                                title: client_messages.content_editor.oops,
                                trigger: "instant",
                                fnSetContent: function (o) {
                                    return "<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" + client_messages.text.drag_and_drop_not_applicable_for_text_style_font_upload + "</div>";
                                }
                            });
                        });
                });
            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <msgpt:BodyNew theme="minimal" type="iframe" cssClass="fileUploadBody">

        <msgpt:iFrameContainer>
            <form:form enctype="multipart/form-data">

                <div style="padding-left: 10px; padding-right: 40px; margin-bottom: 10px; float: right;">

                    <button type="button" onclick="closeIframe()"
                            class="btn btn-light">
                                                <span><fmtSpring:message
                                                        code="page.label.cancel"/></span>
                    </button>
                    <span style="margin-left: 8px">
                                            <msgpt:Button label="page.label.save" URL="javascript:doSubmit()"
                                                          primary="true" icon="fa-save"/>
                                        </span>

                </div>

                <table id="nameContainer">
                 <tr>
                    <td >
                        <div class="fullLineLabel" style="padding: 6px 8px;"><fmtSpring:message
                                code="page.label.NAME"/></div>
                    </td>
                    <td >
                        <msgpt:InputFilter type="simpleName">
                            <form:input id="nameInput" cssClass="inputXXL" path="textStyleFont.name"
                                        maxlength="96"/>
                        </msgpt:InputFilter>
                    </td>
                </tr>
                </table>


            <msgpt:ContentPanel>
                <div id="fileupload" class="container">
                    <!-- The file upload form used as target for the file upload widget -->

                        <c:if test="${empty param.nprSaveSuccess}">
                            <!-- Hidden Binding for the sandbox ids -->
                            <form:hidden path="ttfSandboxId"/>

                            <form:errors path="*">
                                <msgpt:Information errorMsgs="${messages}" type="error"/>
                            </form:errors>

                            <!-- The fileupload-buttonbar contains buttons to add/delete files and start/cancel the upload -->
                            <div class="row fileupload-buttonbar" style="margin: 0px">
                                <table class="actionsBarContainer" width="100%" cellspacing="0" cellpadding="0"
                                       border="0" style="background: #444; border-radius: 6px 6px 0 0;">
                                    <tr>
                                        <td style="vertical-align: middle; padding-left: 10px; padding-right: 10px; white-space: nowrap;">

                                                <span style="color:white; font-weight: bold"><fmtSpring:message
                                                        code="page.lable.regular.font"/> </span>

                                            </span>

                                        </td>
                                        <td align="right" style="padding-top:5px;">
                                            <!-- The global progress information -->
                                            <div class="fileupload-progress fade" style="padding-right: 5px;">
                                                <!-- The global progress bar -->
                                                <div class="progress progress-success progress-striped active"
                                                     role="progressbar" aria-valuemin="0" aria-valuemax="100">
                                                    <div class="bar" style="width:0%;"></div>
                                                </div>
                                                <!-- The extended global progress information -->
                                                <div class="progress-extended text-white">&nbsp;</div>
                                            </div>
                                        </td>
                                        <td  style="vertical-align: middle; padding-left: 10px; float:right; margin-top:5px;">
                                            <!-- The fileinput-button span is used to style the file input field as button -->
                                            <span id="fileUploadButton_add"
                                                  class="formControls btn btn-light fileinput-button btn-sm">
                                            <i class="far fa-plus fa-sm mr-1"></i>
                                            <span><fmtSpring:message
                                                    code="page.label.add.files"/></span>
                                            <input type="file" name="files[]" multiple>
                                        </span>
                                            <button id="fileUploadButton_start" type="submit" style="margin-top: 2px;"
                                                    class="formControls btn btn-light btn-sm start">
                                                <i class="far fa-upload fa-sm mr-1"></i>
                                                <span><fmtSpring:message
                                                        code="page.label.start.upload"/></span>
                                            </button>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <!-- The loading indicator is shown during file processing -->
                            <div class="fileupload-loading"></div>

                            <table width="100%" cellspacing="0" cellpadding="0" border="0"
                                   style="border-left: 1px solid #bbb; border-right: 1px solid #bbb;">

                                <tr id="fileUploadCompleteContainer" style="display: none;">
                                    <td align="left">
                                        <a></a>
                                        <div class="InfoSysContainer_info" style="margin: 5px;">
                                            <fmtSpring:message code="page.text.fonts.upload.for"/> <span
                                                id="filePackageNameDisplay"></span> <fmtSpring:message
                                                code="page.text.has.completed.click.save"/>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            <div class="sandboxFileContainer">

                                <!-- The table listing the files available for upload/download -->
                                <table role="presentation" class="table table-striped" width="100%">
                                    <tbody class="files" data-toggle="modal-gallery" data-target="#modal-gallery">
                                    <tr id="pendingUploadContainer_TTF">
                                        <td colspan="4" style="background-color: #fff; border-top: none; padding: 2px;">
                                            <div class="pendingUploadContainer">

                                                <c:if test="${not empty textStyleFont}">
                                                    <div style="display: inline-block; vertical-align: middle; padding-right: 15px;">
                                                        <a href="javascript:downloadFile('font_id=${textStyleFont.id}&type=font_ttf');">
                                                            <c:out value="${textStyleFont.ttfFile.fileName}"/>
                                                        </a>
                                                    </div>
                                                </c:if>
                                                <div style="display: inline-block; vertical-align: middle;">
                                                    <fmtSpring:message code="page.text.select.a"/><c:out
                                                        value=" "/><c:if
                                                        test="${not empty textStyleFont}"><fmtSpring:message
                                                        code="page.text.replacement"/><c:out value=" "/></c:if>
                                                    <fmtSpring:message code="page.label.truetype.font.TTF"/><c:out
                                                        value=" "/>
                                                    <fmtSpring:message code="page.label.or"/> <c:out value=" "/>
                                                    <fmtSpring:message code="page.label.truetype.font.OTF"/><c:out
                                                        value=" "/>
                                                    <fmtSpring:message code="page.text.for.upload"/>

                                                </div>

                                            </div>

                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </c:if>
                </div>



        <!-- The template to display files available for upload -->
        <script id="template-upload" type="text/x-tmpl">
{% for (var i=0, file; file=o.files[i]; i++) { %}
    <tr class="template-upload fade" filename="pendingUpload_{%=file.name%}">
        <td class="name" >
			<div>{%=file.name%}</div>
			<div class="fileSize">{%=o.formatFileSize(file.size)%}</div>
		</td>
        {% if (file.error) { %}
            <td width="1%" class="error" colspan="2"><span class="label label-important"><fmtSpring:message
                code="page.label.error"/></span> {%=file.error%}</td>
        {% } else if (o.files.valid && !i) { %}
            <td width="1%">
                <div class="progress progress-success progress-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0"><div class="bar" style="width:0%;"></div></div>
            </td>
            <td width="1%" class="start">{% if (!o.options.autoUpload) { %}
                <button class="btn btn-success btn-sm" style="display: none;">
                    <i class="far fa-upload fa-sm mr-1"></i>
                    <span><fmtSpring:message code="page.label.start"/></span>
                </button>
            {% } %}</td>
        {% } else { %}
            <td width="1%" colspan="2"></td>
        {% } %}
        <td width="1%" class="cancel">{% if (!i) { %}
            <button class="btn btn-warning btn-sm" style="display: none;">
                <i class="far fa-ban fa-sm mr-1"></i>
                <span><fmtSpring:message code="page.label.cancel"/></span>
            </button>
        {% } %}</td>
    </tr>
{% } %}


        </script>

        <!-- The template to display files available for download -->
        <script id="template-download" type="text/x-tmpl">
{% for (var i=0, file; file=o.files[i]; i++) { %}
    <tr class="template-download fade">
        {% if (file.error) { %}
            <td colspan="2" class="name" style="padding-left: 15px;">
				<span>{%=file.name%}</span>
				<div class="fileSize">{%=o.formatFileSize(file.size)%}</div>
			</td>
            <td align="right" class="error" colspan="2"><span class="label label-important"><fmtSpring:message
                code="page.label.error"/></span> {%=file.error%}</td>
        {% } else { %}
            <td class="name" colspan="5" style="padding-left: 15px;">
                <div>{%=file.name%} <span style="padding-left: 8px; font-size: 10px;"><fmtSpring:message
                code="page.label.complete.brackets"/></span<</div>
				<div class="fileSize">{%=o.formatFileSize(file.size)%}</div>
            </td>
        {% } %}
    </tr>
{% } %}


        </script>

                <div id="fileuploadBold" class="container">
                    <!-- The file upload form used as target for the file upload widget -->
                        <c:if test="${empty param.nprSaveSuccess}">
                            <!-- Hidden Binding for the sandbox ids -->
                            <form:hidden path="ttfBoldSandboxId"/>

                            <!-- The fileupload-buttonbar contains buttons to add/delete files and start/cancel the upload -->
                            <div class="row fileupload-buttonbar" style="margin: 0px">
                                <table class="actionsBarContainer" width="100%" cellspacing="0" cellpadding="0"
                                       border="0" style="background: #444; border-radius: 6px 6px 0 0;">
                                    <tr>
                                        <td width="1%" align="right"
                                            style="vertical-align: middle; padding-left: 10px; padding-right: 10px; white-space: nowrap;">

                                                <span style="color:white; font-weight: bold"><fmtSpring:message
                                                        code="page.lable.bold.font"/> </span>

                                            </span>

                                        </td>
                                        <td>
                                            <!-- The global progress information -->
                                            <div class="fileupload-progress fade" style="padding-right: 5px;">
                                                <!-- The global progress bar -->
                                                <div class="progress progress-success progress-striped active"
                                                     role="progressbar" aria-valuemin="0" aria-valuemax="100">
                                                    <div class="bar" style="width:0%;"></div>
                                                </div>
                                                <!-- The extended global progress information -->
                                                <div class="progress-extended text-white">&nbsp;</div>
                                            </div>
                                        </td>
                                        <td align="left" style="vertical-align: middle; padding-left: 10px; float:right; margin-top:5px;">
                                            <!-- The fileinput-button span is used to style the file input field as button -->
                                            <span id="fileBoldUploadButton_add"
                                                  class="formControls btn btn-light fileinput-button btn-sm">
                                            <i class="far fa-plus fa-sm mr-1"></i>
                                            <span><fmtSpring:message
                                                    code="page.label.add.files"/></span>
                                            <input type="file" name="files[]" multiple>
                                        </span>
                                            <button id="fileBoldUploadButton_start" type="submit"
                                                    class="formControls btn btn-light btn-sm start" style="margin-top: 2px;">
                                                <i class="far fa-upload fa-sm mr-1"></i>
                                                <span><fmtSpring:message
                                                        code="page.label.start.upload"/></span>
                                            </button>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <!-- The loading indicator is shown during file processing -->
                            <div class="fileupload-loading"></div>

                            <table width="100%" cellspacing="0" cellpadding="0" border="0"
                                   style="border-left: 1px solid #bbb; border-right: 1px solid #bbb;">
                                <tr id="fileBoldUploadCompleteContainer" style="display: none;">
                                    <td align="left">
                                        <a></a>
                                        <div class="InfoSysContainer_info" style="margin: 5px;">
                                            <fmtSpring:message code="page.text.fonts.upload.for"/> <span
                                                id="fileBoldPackageNameDisplay"></span> <fmtSpring:message
                                                code="page.text.has.completed.click.save"/>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            <div class="sandboxFileContainer">

                                <!-- The table listing the files available for upload/download -->
                                <table role="presentation" class="table table-striped" width="100%">
                                    <tbody class="files" data-toggle="modal-gallery" data-target="#modal-gallery">
                                    <tr id="pendingBoldUploadContainer_TTF">
                                        <td colspan="4" style="background-color: #fff; border-top: none; padding: 2px;">
                                            <div class="pendingUploadContainer">

                                                <c:if test="${not empty textStyleFont}">
                                                    <div style="display: inline-block; vertical-align: middle; padding-right: 15px;">
                                                        <a href="javascript:downloadFile('font_id=${textStyleFont.id}&type=font_ttf_bold');">
                                                            <c:out value="${textStyleFont.ttfBoldFile.fileName}"/>
                                                        </a>
                                                    </div>
                                                </c:if>
                                                <div style="display: inline-block; vertical-align: middle;">
                                                    <fmtSpring:message code="page.text.select.a"/><c:out
                                                        value=" "/><c:if
                                                        test="${not empty textStyleFont}"><fmtSpring:message
                                                        code="page.text.replacement"/><c:out value=" "/></c:if>
                                                    <fmtSpring:message code="page.label.truetype.font.TTF"/><c:out
                                                        value=" "/>
                                                    <fmtSpring:message code="page.label.or"/> <c:out value=" "/>
                                                    <fmtSpring:message code="page.label.truetype.font.OTF"/><c:out
                                                        value=" "/>
                                                    <fmtSpring:message code="page.text.for.upload"/>

                                                </div>

                                            </div>

                                        </td>
                                    </tr>

                                    </tbody>
                                </table>
                            </div>
                        </c:if>

                </div>

                <div id="fileuploadItalic" class="container">
                    <!-- The file upload form used as target for the file upload widget -->
                    <c:if test="${empty param.nprSaveSuccess}">
                        <!-- Hidden Binding for the sandbox ids -->
                        <form:hidden path="ttfItalicSandboxId"/>

                        <!-- The fileupload-buttonbar contains buttons to add/delete files and start/cancel the upload -->
                        <div class="row fileupload-buttonbar" style="margin: 0px">
                            <table class="actionsBarContainer" width="100%" cellspacing="0" cellpadding="0"
                                   border="0" style="background: #444; border-radius: 6px 6px 0 0;">
                                <tr>
                                    <td width="1%" align="left"
                                        style="vertical-align: middle; padding-left: 10px; padding-right: 10px; white-space: nowrap;">

                                                <span style="color:white; font-weight: bold"><fmtSpring:message
                                                        code="page.lable.italic.font"/> </span>

                                        </span>

                                    </td>
                                    <td>
                                        <!-- The global progress information -->
                                        <div class="fileupload-progress fade" style="padding-right: 5px;">
                                            <!-- The global progress bar -->
                                            <div class="progress progress-success progress-striped active"
                                                 role="progressbar" aria-valuemin="0" aria-valuemax="100">
                                                <div class="bar" style="width:0%;"></div>
                                            </div>
                                            <!-- The extended global progress information -->
                                            <div class="progress-extended text-white">&nbsp;</div>
                                        </div>
                                    </td>
                                    <td align="left" style="vertical-align: middle; padding-left: 10px;float:right; margin-top: 5px;">
                                        <!-- The fileinput-button span is used to style the file input field as button -->
                                        <span id="fileItalicUploadButton_add"
                                              class="formControls btn btn-light fileinput-button btn-sm">
                                            <i class="far fa-plus fa-sm mr-1"></i>
                                            <span><fmtSpring:message
                                                    code="page.label.add.files"/></span>
                                            <input type="file" name="files[]" multiple>
                                        </span>
                                        <button id="fileItalicUploadButton_start" type="submit"
                                                class="formControls btn btn-light btn-sm start" style="margin-top: 2px;">
                                            <i class="far fa-upload fa-sm mr-1"></i>
                                            <span><fmtSpring:message
                                                    code="page.label.start.upload"/></span>
                                        </button>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- The loading indicator is shown during file processing -->
                        <div class="fileupload-loading"></div>

                        <table width="100%" cellspacing="0" cellpadding="0" border="0"
                               style="border-left: 1px solid #bbb; border-right: 1px solid #bbb;">
                            <tr id="fileItalicUploadCompleteContainer" style="display: none;">
                                <td align="left">
                                    <a></a>
                                    <div class="InfoSysContainer_info" style="margin: 5px;">
                                        <fmtSpring:message code="page.text.fonts.upload.for"/> <span
                                            id="fileItalicPackageNameDisplay"></span> <fmtSpring:message
                                            code="page.text.has.completed.click.save"/>
                                    </div>
                                </td>
                            </tr>
                        </table>
                        <div class="sandboxFileContainer">

                            <!-- The table listing the files available for upload/download -->
                            <table role="presentation" class="table table-striped" width="100%">
                                <tbody class="files" data-toggle="modal-gallery" data-target="#modal-gallery">
                                <tr id="pendingItalicUploadContainer_TTF">
                                    <td colspan="4" style="background-color: #fff; border-top: none; padding: 2px;">
                                        <div class="pendingUploadContainer">

                                            <c:if test="${not empty textStyleFont}">
                                                <div style="display: inline-block; vertical-align: middle; padding-right: 15px;">
                                                    <a href="javascript:downloadFile('font_id=${textStyleFont.id}&type=font_ttf_italic');">
                                                        <c:out value="${textStyleFont.ttfItalicFile.fileName}"/>
                                                    </a>
                                                </div>
                                            </c:if>
                                            <div style="display: inline-block; vertical-align: middle;">
                                                <fmtSpring:message code="page.text.select.a"/><c:out
                                                    value=" "/><c:if
                                                    test="${not empty textStyleFont}"><fmtSpring:message
                                                    code="page.text.replacement"/><c:out value=" "/></c:if>
                                                <fmtSpring:message code="page.label.truetype.font.TTF"/><c:out
                                                    value=" "/>
                                                <fmtSpring:message code="page.label.or"/> <c:out value=" "/>
                                                <fmtSpring:message code="page.label.truetype.font.OTF"/><c:out
                                                    value=" "/>
                                                <fmtSpring:message code="page.text.for.upload"/>

                                            </div>

                                        </div>

                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </c:if>

                </div>

                <div id="fileuploadBoldItalic" class="container">
                    <!-- The file upload form used as target for the file upload widget -->
                    <c:if test="${empty param.nprSaveSuccess}">
                        <!-- Hidden Binding for the sandbox ids -->
                        <form:hidden path="ttfBoldItalicSandboxId"/>

                        <!-- The fileupload-buttonbar contains buttons to add/delete files and start/cancel the upload -->
                        <div class="row fileupload-buttonbar" style="margin: 0px">
                            <table class="actionsBarContainer" width="100%" cellspacing="0" cellpadding="0"
                                   border="0" style="background: #444; border-radius: 6px 6px 0 0;">
                                <tr>
                                    <td width="1%" align="left"
                                        style="vertical-align: middle; padding-left: 10px; padding-right: 10px; white-space: nowrap;">

                                                <span style="color:white; font-weight: bold"><fmtSpring:message
                                                        code="page.lable.bold.italic.font"/> </span>

                                        </span>

                                    </td>
                                    <td>
                                        <!-- The global progress information -->
                                        <div class="fileupload-progress fade" style="padding-right: 5px;">
                                            <!-- The global progress bar -->
                                            <div class="progress progress-success progress-striped active"
                                                 role="progressbar" aria-valuemin="0" aria-valuemax="100">
                                                <div class="bar" style="width:0%;"></div>
                                            </div>
                                            <!-- The extended global progress information -->
                                            <div class="progress-extended text-white">&nbsp;</div>
                                        </div>
                                    </td>
                                    <td align="left" style="vertical-align: middle; padding-left: 10px; float:right; margin-top:5px;">
                                        <!-- The fileinput-button span is used to style the file input field as button -->
                                        <span id="fileBoldItalicUploadButton_add"
                                              class="formControls btn btn-light fileinput-button btn-sm">
                                            <i class="far fa-plus fa-sm mr-1"></i>
                                            <span><fmtSpring:message
                                                    code="page.label.add.files"/></span>
                                            <input type="file" name="files[]" multiple>
                                        </span>
                                        <button id="fileBoldItalicUploadButton_start" type="submit"
                                                class="formControls btn btn-light btn-sm start" style="margin-top: 2px;">
                                            <i class="far fa-upload fa-sm mr-1"></i>
                                            <span><fmtSpring:message
                                                    code="page.label.start.upload"/></span>
                                        </button>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- The loading indicator is shown during file processing -->
                        <div class="fileupload-loading"></div>

                        <table width="100%" cellspacing="0" cellpadding="0" border="0"
                               style="border-left: 1px solid #bbb; border-right: 1px solid #bbb;">
                            <tr id="fileBoldItalicUploadCompleteContainer" style="display: none;">
                                <td align="left">
                                    <a></a>
                                    <div class="InfoSysContainer_info" style="margin: 5px;">
                                        <fmtSpring:message code="page.text.fonts.upload.for"/> <span
                                            id="fileBoldItalicPackageNameDisplay"></span> <fmtSpring:message
                                            code="page.text.has.completed.click.save"/>
                                    </div>
                                </td>
                            </tr>
                        </table>
                        <div class="sandboxFileContainer">

                            <!-- The table listing the files available for upload/download -->
                            <table role="presentation" class="table table-striped" width="100%">
                                <tbody class="files" data-toggle="modal-gallery" data-target="#modal-gallery">
                                <tr id="pendingBoldItalicUploadContainer_TTF">
                                    <td colspan="4" style="background-color: #fff; border-top: none; padding: 2px;">
                                        <div class="pendingUploadContainer">

                                            <c:if test="${not empty textStyleFont}">
                                                <div style="display: inline-block; vertical-align: middle; padding-right: 15px;">
                                                    <a href="javascript:downloadFile('font_id=${textStyleFont.id}&type=font_ttf_bold_italic');">
                                                        <c:out value="${textStyleFont.ttfBoldItalicFile.fileName}"/>
                                                    </a>
                                                </div>
                                            </c:if>
                                            <div style="display: inline-block; vertical-align: middle;">
                                                <fmtSpring:message code="page.text.select.a"/><c:out
                                                    value=" "/><c:if
                                                    test="${not empty textStyleFont}"><fmtSpring:message
                                                    code="page.text.replacement"/><c:out value=" "/></c:if>
                                                <fmtSpring:message code="page.label.truetype.font.TTF"/><c:out
                                                    value=" "/>
                                                <fmtSpring:message code="page.label.or"/> <c:out value=" "/>
                                                <fmtSpring:message code="page.label.truetype.font.OTF"/><c:out
                                                    value=" "/>
                                                <fmtSpring:message code="page.text.for.upload"/>

                                            </div>

                                        </div>

                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </c:if>

                </div>

            </msgpt:ContentPanel>
    </form:form>
        </msgpt:iFrameContainer>


        <!-- The template to display files available for upload -->
        <script id="template-upload_bold" type="text/x-tmpl">
{% for (var i=0, file; file=o.files[i]; i++) { %}
    <tr class="template-upload fade" filename="pendingUploadBold_{%=file.name%}">
        <td class="name" style="padding-left: 15px;">
			<div>{%=file.name%}</div>
			<div class="fileSize">{%=o.formatFileSize(file.size)%}</div>
		</td>
        {% if (file.error) { %}
            <td width="1%" class="error" colspan="2"><span class="label label-important"><fmtSpring:message
                code="page.label.error"/></span> {%=file.error%}</td>
        {% } else if (o.files.valid && !i) { %}
            <td width="1%">
                <div class="progress progress-success progress-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0"><div class="bar" style="width:0%;"></div></div>
            </td>
            <td width="1%" class="start">{% if (!o.options.autoUpload) { %}
                <button class="btn btn-success btn-sm" style="display: none;">
                    <i class="far fa-upload fa-sm mr-1"></i>
                    <span><fmtSpring:message code="page.label.start"/></span>
                </button>
            {% } %}</td>
        {% } else { %}
            <td width="1%" colspan="2"></td>
        {% } %}
        <td width="1%" class="cancel">{% if (!i) { %}
            <button class="btn btn-warning btn-sm" style="display: none;">
                <i class="far fa-ban fa-sm mr-1"></i>
                <span><fmtSpring:message code="page.label.cancel"/></span>
            </button>
        {% } %}</td>
    </tr>
{% } %}

        </script>

        <script id="template-upload_italic" type="text/x-tmpl">
{% for (var i=0, file; file=o.files[i]; i++) { %}
    <tr class="template-upload fade" filename="pendingUploadItalic_{%=file.name%}">
        <td class="name" style="padding-left: 15px;">
			<div>{%=file.name%}</div>
			<div class="fileSize">{%=o.formatFileSize(file.size)%}</div>
		</td>
        {% if (file.error) { %}
            <td width="1%" class="error" colspan="2"><span class="label label-important"><fmtSpring:message
                code="page.label.error"/></span> {%=file.error%}</td>
        {% } else if (o.files.valid && !i) { %}
            <td width="1%">
                <div class="progress progress-success progress-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0"><div class="bar" style="width:0%;"></div></div>
            </td>
            <td width="1%" class="start">{% if (!o.options.autoUpload) { %}
                <button class="btn btn-success btn-sm" style="display: none;">
                    <i class="far fa-upload fa-sm mr-1"></i>
                    <span><fmtSpring:message code="page.label.start"/></span>
                </button>
            {% } %}</td>
        {% } else { %}
            <td width="1%" colspan="2"></td>
        {% } %}
        <td width="1%" class="cancel">{% if (!i) { %}
            <button class="btn btn-warning btn-sm" style="display: none;">
                <i class="far fa-ban fa-sm mr-1"></i>
                <span><fmtSpring:message code="page.label.cancel"/></span>
            </button>
        {% } %}</td>
    </tr>
{% } %}

        </script>

        <script id="template-upload_bold_italic" type="text/x-tmpl">
{% for (var i=0, file; file=o.files[i]; i++) { %}
    <tr class="template-upload fade" filename="pendingUploadBoldItalic_{%=file.name%}">
        <td class="name" style="padding-left: 15px;">
			<div>{%=file.name%}</div>
			<div class="fileSize">{%=o.formatFileSize(file.size)%}</div>
		</td>
        {% if (file.error) { %}
            <td width="1%" class="error" colspan="2"><span class="label label-important"><fmtSpring:message
                code="page.label.error"/></span> {%=file.error%}</td>
        {% } else if (o.files.valid && !i) { %}
            <td width="1%">
                <div class="progress progress-success progress-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0"><div class="bar" style="width:0%;"></div></div>
            </td>
            <td width="1%" class="start">{% if (!o.options.autoUpload) { %}
                <button class="btn btn-success btn-sm" style="display: none;">
                    <i class="far fa-upload fa-sm mr-1"></i>
                    <span><fmtSpring:message code="page.label.start"/></span>
                </button>
            {% } %}</td>
        {% } else { %}
            <td width="1%" colspan="2"></td>
        {% } %}
        <td width="1%" class="cancel">{% if (!i) { %}
            <button class="btn btn-warning btn-sm" style="display: none;">
                <i class="far fa-ban fa-sm mr-1"></i>
                <span><fmtSpring:message code="page.label.cancel"/></span>
            </button>
        {% } %}</td>
    </tr>
{% } %}

        </script>

        <!-- The template to display files available for download -->
        <script id="template-download" type="text/x-tmpl">
{% for (var i=0, file; file=o.files[i]; i++) { %}
    <tr class="template-download fade">
        {% if (file.error) { %}
            <td colspan="2" class="name" style="padding-left: 15px;">
				<span>{%=file.name%}</span>
				<div class="fileSize">{%=o.formatFileSize(file.size)%}</div>
			</td>
            <td align="right" class="error" colspan="2"><span class="label label-important"><fmtSpring:message
                code="page.label.error"/></span> {%=file.error%}</td>
        {% } else { %}
            <td class="name" colspan="5" style="padding-left: 15px;">
                <div>{%=file.name%} <span style="padding-left: 8px; font-size: 10px;"><fmtSpring:message
                code="page.label.complete.brackets"/></span<</div>
				<div class="fileSize">{%=o.formatFileSize(file.size)%}</div>
            </td>
        {% } %}
    </tr>
{% } %}


        </script>

    </msgpt:BodyNew>

</msgpt:Html5>