<%@ include file="../includes/includes.jsp" %>
<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.model.admin.ContentType" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<msgpt:Html5>

    <c:set var="contentObject" value="${command.contentObject}"/>
    <c:set var="pageTitle" value="page.label.messages"/>
    <c:if test="${contentObject.isGlobalContentObject}">
        <c:set var="pageTitle" value="page.label.content"/>
    </c:if>
    <msgpt:HeaderNew title="${pageTitle}" viewType="<%= MessagepointHeader.ViewType.EDIT %>">

        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:Stylesheet href="includes/themes/messagepoint.contenteditor.css"/>
        <msgpt:Script src="includes/javascript/popupActions.js"/>

        <!-- The following javascript must be declared inside the body tag.
        This is a TinyMCE issue. -->
        <msgpt:Stylesheet href="${command.contentObject.defaultViewCSSFilePath}?cacheStamp=${timeStamp}"
                          standalone="true"/>
        <c:if test="${not empty command.contentObject.styles}">
            <msgpt:Stylesheet href="${command.contentObject.CSSFilename}?cacheStamp=${timeStamp}" standalone="true"/>
        </c:if>
        <c:if test="${!empty command.contentObject.paragraphStyles}">
            <msgpt:Stylesheet href="${command.contentObject.paragraphCSSFilename}?cacheStamp=${timeStamp}"
                              standalone="true"/>
        </c:if>
        <c:if test="${not empty command.contentObject.listStyles}">
            <msgpt:Stylesheet href="${command.contentObject.listCSSFilename}?cacheStamp=${timeStamp}"
                              standalone="true"/>
        </c:if>

        <msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/editManager/jquery.editManager.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/contentAssistants/jquery.contentAssistants.js"/>

        <msgpt:Script>
            <script>

                global_data.spellcheck_languages = ${contentObject.spellcheckLanguages};
                $(function () {

                    var editorData = {
                        content_css: "${contentObject.defaultEditorCSSFilePath}?cacheStamp=${timeStamp}" +
                            "<c:if test="${!empty contentObject.styles}">,${contentObject.CSSFilename}?cacheStamp=${timeStamp}</c:if>",
                        text_data: ${!empty contentObject.styles ? contentObject.textStyleData : "null"},
                        async_variables: {type: '${contentObject.globalContentObject?"embedded_content":"message"}'},
                        async_embedded_content: {type: '${contentObject.globalContentObject?"embedded_content":"message"}'},
                        markers_list: ${systemVariablesData},
                        smart_canvas_list: ${smartCanvasContentData},
                        local_smart_canvas_list: ${localSmartCanvasContentData},
                        ${!contentObject.globalContentObject? "async_local_embedded_content: {type: 'message'},":""}
                        placeholders_list: ${placeholdersData},
                        applies_text_styles: ${!empty contentObject.styles},
                        applies_paragraph_styles: ${!empty contentObject.paragraphStyles},
                        paragraph_style_css: "<c:if test="${!empty command.contentObject.paragraphStyles}">${contentObject.paragraphCSSFilename}?cacheStamp=${timeStamp}</c:if>",
                        paragraph_style_data: ${!empty contentObject.paragraphStyles ? contentObject.paragraphStyleData : "null"},
                        applies_list_styles: ${!empty contentObject.listStyles},
                        list_style_css: "<c:if test="${!empty contentObject.listStyles}">${contentObject.listCSSFilename}?cacheStamp=${timeStamp}</c:if>",
                        list_style_data: ${!empty contentObject.listStyles ? contentObject.listStyleData : "null"},
                        apply_rotation: ${applyContentRotation},
                        applies_templates: ${isCommunicationContent},
                        applies_freeform: ${contentObject.isFreeform},
                        applies_forms: ${contentObject.appliesForms},
                        applies_barcodes: ${contentObject.appliesBarcodes},
                        applies_images: ${contentObject.appliesImages},
                        applies_tables: ${contentObject.appliesTables},
                        content_menu: {
                            applied: ${((contentObject.isTouchpointLocal || contentObject.isGlobalSmartText) && contentObject.appliesContentMenus) || !contentObject.isTouchpointLocal || isCommunicationOnlyContent},
                            mode: '${((contentObject.isTouchpointLocal || contentObject.isGlobalSmartText) && contentObject.appliesContentMenus) || contentObject.appliesContentMenusForConnected ? "edit" : "content"}'
                        },
                        support_unicode: ${contentObject.supportsUnicode},
                        canvas_dimensions: "${contentObject.canvasDimensions}",
                        zone_rotation: ${contentObject.zoneRotation},
                        apply_super_sub: ${applySuperSubScript},
                        zone_id: ${contentObject.isMessage ? contentObject.zone.id : 0},
                        channel: ${contentObject.isTouchpointLocal ? contentObject.document.connectorConfiguration.connector.channel.id : contentObject.appliedChannels},
                        connector: ${contentObject.isTouchpointLocal ? contentObject.document.connectorConfiguration.connector.id : contentObject.appliedConnectors},
                        is_exstream_html: ${contentObject.isExstreamHtml},
                        is_exstream_dxf: ${contentObject.isExstreamDxf},
                        is_exstream_runtime_dxf: ${contentObject.isExstreamRunTimeDxf},
                        can_edit_source: ${sourceEditPerm},
                        marcie_flags: ${marcieFlags},
                        applies_connected_authoring: ${contentObject.appliesConnectedAuthoring},
                        is_global_context: ${contentObject.globalContentObject},
                        content_object_id: ${contentObject.id},
                        variant_id: ${not empty param.paramInstId ? param.paramInstId : -1},
                        is_translation_compare: ${contentObject.inTranslationStep},
                        starter_style: "${contentObject.zoneStarterTextStyle}",
                        supports_custom_paragraph: ${contentObject.zone != null ? contentObject.zone.supportsCustomParagraphs : false},
                        table_of_contents_enabled: ${tableOfContentsEnabled},
                        is_new_editor: true
                    };

                    var width = "100%";
                    var height = ${contentObject.multipartType ? 480 : 585};

                    <c:choose>
                    <c:when test="${contentObject.isVideoDelivery}">
                    tinyMCEvalueEditorInit("textContentInput", width, "10", editorData);
                    </c:when>
                    <c:when test="${command.contentObject.isEmailSubjectDelivery || command.contentObject.isSmsDelivery || contentObject.isMarkup}">
                    tinyMCEsimpleTextEditorInit(width, ${contentObject.isMarkup ? "height" : "160"}, "${command.contentObject.isSmsDelivery ? 'MULTILINE' : 'SINGLELINE'}", editorData);
                    </c:when>
                    <c:when test="${command.contentObject.isEmailContentDelivery || command.contentObject.isWebDelivery}">
                    tinyMCEemailInit(width, height, editorData);
                    </c:when>
                    <c:otherwise>
                    tinyMCEinit(width, height, editorData);
                    </c:otherwise>
                    </c:choose>
                    tinyMCEvalueEditorInit("graphicImageLink", "100%", "10", editorData);
                    tinyMCEvalueEditorInit("graphicImageAltText", "100%", "10", editorData);
                    tinyMCEvalueEditorInit("graphicImageExtLink", "100%", "10", editorData);
                    tinyMCEvalueEditorInit("graphicImageExtPath", "100%", "10", editorData);
                });

                function setContentAreaWidth(o) {
                    $("#contentData").resize();
                }

                function contextMenuOptionSelected(optionSelected, selectedItemId) {
                    if (optionSelected == 0) {
                        goToVariant(selectedItemId);
                    } else if (optionSelected == 1) {
                        doSubmitWithParameter(1, 'continueInstId', selectedItemId);
                    } else if (optionSelected >= 2) {
                        $('#parentNode_id').val(selectedItemId);
                        loadVariantPopupIframe(selectedItemId, optionSelected);
                        displayPopup();
                    }
                }

                function variantSelected(event, data) {
                    goToVariant(data.node.id);
                }

            </script>
        </msgpt:Script>
        <msgpt:Script src="includes/javascript/jQueryPlugins/hoverIntent/jquery.hoverIntent.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.editorActions.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.varTagAttrManager.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.smartTextTagAttrManager.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.placeholderTagAttrManager.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/disableSelection/jquery.disable.text.select.pack.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/contentEditor/jquery.contentEditor.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/fastNavigation/jquery.fastNavigation.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/sharedTextFastEdit/jquery.sharedTextFastEdit.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/smartTextContentViewer/jquery.smartTextContentViewer.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/contentEditor/contentEditor.css"/>
        <msgpt:Script src="includes/javascript/pdfObject/pdfobject.min.js"/>
        <msgpt:Script src="_ux/js/mp.postTrigger.js"/>
        <msgpt:Script src="_ux/js/mp.historyQueue.js"/>
        <msgpt:Script src="_ux/js/mp.complexCombobox.js"/>
        <msgpt:Script src="_ux/js/mp.snapInPanel.js"/>
        <msgpt:Script src="_ux/js/mp.contentPanelToggle.js"/>

        <!-- Content editor: Async file upload JS -->
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.image-gallery.min.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.iframe-transport.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.fileupload.js"/>
        <msgpt:Script src="_ux/js/mp.serializedObservableForm.js"/>
        <msgpt:Script>
            <script>

                // MULTIPART FUNCTIONS START

                function deliverySelected_context(docId, zoneId) {
                    $("div:first[id^='zone" + zoneId + "_part']").each(function () {
                        partClickedAction(this);
                    });
                }

                // onclick of zone part: adjust display for 'Zone Content
                // Selection' and associted content display
                function partClickedAction(part) {

                    function triggerContentGroupChange(partId) {
                        if ($('#partSelect_' + zoneId).find('option:selected').attr('content') != 'graphic' &&
                            $('.editorContentPanel').find('.mce-tinymce.mce-container').length == 0 && $('#select_' + partId).val() == 0)
                            setTimeout(function () {
                                triggerContentGroupChange(partId);
                            }, 100);
                        else
                            $('#contentData').changeContentGroup(partId.split('_')[1]);
                    }

                    var partId = $(part).attr('id');
                    $("[class*='zoneContentMenu_']").hide();

                    $('.zonePartSelected').removeClass('zonePartSelected');
                    $(part).addClass('zonePartSelected');

                    $('.zoneContentMenu_' + partId).show();

                    $('#currentContentId').val(partId);

                    $(part).siblings().css('z-index', '0');
                    $(part).css('z-index', '1');

                    var zoneId = partId.split('_')[0];
                    $('#partSelect_' + zoneId).selectOption('partOption_' + partId);
                    $("[id^='partSelectDiv']").hide();
                    $('#partSelectDiv_' + zoneId).show();

                    triggerContentGroupChange(partId);
                }


                function changePartSelect(partSelect) {
                    var partId = $(partSelect).find(':selected').val();
                    $('#' + partId).click();
                }

                function customContentSelectChange(selectElement, maintainContent) {
                    var contentOption = false;

                    if ($(selectElement).attr('checked') == "checked") {
                        contentOption = true;
                    }

                    if (contentOption)
                        $('#contentData').setCurrentGroupStateToLeaveEmpty();
                    else {
                        if (!maintainContent)
                            $('#contentData').clearContentForCurrentGroup();
                        $('#contentData').setCurrentGroupStateToEdit();
                    }

                    common.refreshParentIframeHeight();
                }

                function toggleDeliveryPanel() {
                    if ($('#toggleDeliveryPanelBtn').is('.actionBtn_toggleSelect')) {
                        // DELIVERY PANEL: Hide
                        $('#toggleDeliveryPanelBtn').removeClass('actionBtn_toggleSelect');
                        $('#toggleDeliveryPanelBtn').addClass('actionBtn');
                        $('.summaryPanelCollapsable').animate({
                            width: "0px",
                            height: "0px",
                            left: "0px",
                            top: "0px"
                        }, function () {
                            $(this).hide();
                        });
                    } else {
                        // DELIVERY PANEL: Reveal
                        $('#toggleDeliveryPanelBtn').removeClass('actionBtn actionBtn_hov');
                        $('#toggleDeliveryPanelBtn').addClass('actionBtn_toggleSelect');
                        $('.summaryPanelCollapsable').show().animate({
                            width: "346px",
                            height: "500px",
                            left: "-10px",
                            top: "-10px"
                        });
                    }
                }

                // MULTIPART FUNCTIONS END

                // Popup Init
                $(function () {

                    $('#messageEditSaveBtnGroup').on('change', function () {

                        doSubmit($(this).val());

                    });

                    $('#messageEditCancelBtnGroup').on('change', function () {

                        var selectedValue = $(this).val();

                        if (selectedValue === 'gotoview')
                            javascriptHref('${contextPath}/content/content_object_view_content_dynamic_variant.form?contentObjectId=${contentObject.id}&paramInstId=${param.paramInstId}${not empty param.statusViewId ? '&statusViewId=' : ''}${not empty param.statusViewId ? param.statusViewId : ''}');

                        else if (selectedValue !== 'goback')
                            doSubmit(selectedValue);

                    });

                    $(".style_menu,.style_select").styleActionElement();

                    $("#contentData").contentEditor({
                        text_data:    ${!empty contentObject.styles ? contentObject.textStyleData : "null"},
                        paragraph_data:    ${!empty contentObject.paragraphStyles ? contentObject.paragraphStyleData : "null"},
                        contextPath: '${contextPath}',
                        defaultLocaleId: '${defaultLocaleId}',
                        defaultLanguage: '${defaultLanguage}',
                        focusLocaleId: '${focusLocaleId}',
                        globalContentObject: ${contentObject.isGlobalContentObject},
                        editorInitData:    ${contentObject.isEmailSubjectDelivery || contentObject.isSmsDelivery || contentObject.isMarkup ? 'tinymceEditorDef_simpleText' : (contentObject.isVideoDelivery ? 'tinymceEditorDef_value' : 'tinymceEditorDef_standard')},
                        languages:    ${languagesJSON},
                        contentObjectId: '${contentObject.id}',
                        contentObjectDna: '${contentObject.dna}',
                        onload: function () {
                            $('#multipartInterfaceContainer').show();
                        },
                        applies_freeform: ${contentObject.isFreeform},
                        library_only: ${contentObject.appliesImagelibraryOnly},
                        topPadding: 10,
                        usage: 'edit',
                        channel: ${contentObject.isTouchpointLocal ? contentObject.document.connectorConfiguration.connector.channel.id : contentObject.appliedChannels},
                        connector: ${contentObject.isTouchpointLocal ? contentObject.document.connectorConfiguration.connector.id : contentObject.appliedConnectors},
                        nTk: '${nodeGUID}',
                        uTk: '${webDAVToken}',
                        enabledFileEdit: true,
                        statusViewId: 1,
                        inheritOrCustomAll: ${inheritOrCustomAll},
                        onload: function () {
                            // FIX tab display anomaly: mis aligned tabs
                            setTimeout(function () {
                                $("#contentData").resize();
                            }, 250);
                        }
                    });

                    $('.zoneSelectedClass .multipartZonePart:first').each(function () {
                        partClickedAction(this);
                    });
                });

                var pageDataModule = {
                    contentObjectId: '${contentObject.id}',
                    contentObjectName: "${msgpt:removeSpecialCharacters(contentObject.name)}",
                    contentTreeNodeId: '${command.contentTreeNode.id}',
                    contentTreeNodeName: "${msgpt:removeSpecialCharacters(command.contentTreeNode.name)}",
                };

                serializedObservableForm.init({
                    formID: '#command'
                });
            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <!-- Content Type IDs -->
    <c:set var="textContentTypeID" value="<%= ContentType.TEXT %>"/>
    <c:set var="graphicContentTypeID" value="<%= ContentType.GRAPHIC %>"/>
    <c:set var="freeformContentTypeID" value="<%= ContentType.SHARED_FREEFORM %>"/>
    <c:set var="videoContentTypeID" value="<%= ContentType.VIDEO %>"/>

    <msgpt:BodyNew cssClass="content-editor-page">
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
        <div id="content" class="d-table-row h-100 fullscreenBoundary">
            <div class="shiftContainer noShiftSpacing h-100 m-0">
                <form:form modelAttribute="command" method="post" enctype="multipart/form-data" cssClass="h-100">
                    <c:if test="${!canUpdate}">
                        <msgpt:Information type="error" floating="true">
                            <fmtSpring:message code="error.message.action.not.permitted"/>
                        </msgpt:Information>
                    </c:if>
                    <c:if test="${canUpdate}">
                        <div class="d-flex flex-column w-100 h-100">
                            <div class="box-shadow-4 bg-white position-sticky z-index-3 top-0">
                                <div id="history-queue-container"></div>
                                <div class="d-flex align-items-center py-3 px-4 bg-white">
                                    <h1 class="h5 m-0"
                                        title="${(not contentObject.isGlobalSmartCanvas && not contentObject.isGlobalSmartText && not contentObject.isGlobalImage && not empty contentObject.document) ? contentObject.document.name : ''}">
                                        <msgpt:Breadcrumb
                                                titleText="${msgpt:getMessage('page.text.edit.content.for').concat(' ')}"
                                                contentObjectName="${contentObject.name}"/>
                                    </h1>
                                    <div class="position-relative d-flex ml-auto">
                                        <input type="hidden" id="contentTrimTypeSelect"
                                               value="${contentObject.contentTrimType}"/>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button"
                                                    class="btn btn-outline-light border-right-0 text-body btn-placeholder"
                                                    disabled>
                                                <i class="fad fa-spinner-third fa-lg fa-spin align-middle mx-3"
                                                   aria-hidden="true"></i>
                                            </button>
                                            <select id="messageEditCancelBtnGroup"
                                                    class="complex-combobox-select"
                                                    aria-label="${msgpt:getMessage("page.label.actions")}"
                                                    data-toggle="complex-combobox"
                                                    data-combobox-class="btn btn-outline-light border-right-0 text-body">
                                                <option id="btnCancelAndExit" value="cancelandexit"
                                                        data-icon="far fa-times-circle"><fmtSpring:message
                                                        code="page.label.cancel_and_go_to_list"/>
                                                </option>
                                                <option id="btnCancelAndGoToView" value="gotoview"
                                                        data-icon="far fa-glasses-alt"><fmtSpring:message
                                                        code="page.label.cancel_and_view"/>
                                                </option>
                                                <option id="btnCancelAndGoBack" value="goback"
                                                        class="d-none"
                                                        data-icon="far fa-left"><fmtSpring:message
                                                        code="page.label.cancel_and_go_back"/>
                                                </option>
                                            </select>
                                            <button type="button"
                                                    class="btn btn-outline-light border-left-0 text-body dropdown-toggle dropdown-toggle-split"
                                                    data-toggle="dropdown" aria-haspopup="true"
                                                    aria-expanded="false" disabled>
                                                        <span class="sr-only"><fmtSpring:message
                                                                code="page.text.toggle.dropdown"/></span>
                                            </button>
                                        </div>
                                        <div class="btn-group btn-group-sm ml-3">
                                            <button type="button" class="btn btn-primary btn-placeholder" disabled>
                                                <i class="fad fa-spinner-third fa-lg fa-spin align-middle mx-3"
                                                   aria-hidden="true"></i>
                                            </button>
                                            <select id="messageEditSaveBtnGroup"
                                                    class="complex-combobox-select"
                                                    aria-label="${msgpt:getMessage("page.label.actions")}"
                                                    data-toggle="complex-combobox"
                                                    data-combobox-class="btn-primary">
                                                <c:if test="${not empty param.cfSource && param.cfSource == 'true'}">
                                                    <option id="btnSaveAndBack" value="saveandgoback"
                                                            data-icon="far fa-arrow-left"
                                                            data-customitem="true"><fmtSpring:message
                                                            code="page.label.save.back"/>
                                                    </option>
                                                </c:if>
                                                <option id="btnSaveAndStay" value="saveandstay"
                                                        data-icon="fas fa-save"><fmtSpring:message
                                                        code="page.label.save"/>
                                                </option>
                                                <option id="btnSaveAndView" value="saveandview"
                                                        data-icon="far fa-glasses-alt"><fmtSpring:message
                                                        code="page.label.save.and.view"/>
                                                </option>
                                                <option id="btnSaveAndGoToList" value="saveandgotolist"
                                                        data-icon="far fa-list-alt"><fmtSpring:message
                                                        code="page.label.save.and.go.to.list"/>
                                                </option>
                                            </select>
                                            <button type="button"
                                                    class="btn btn-primary dropdown-toggle dropdown-toggle-split"
                                                    data-toggle="dropdown" aria-haspopup="true"
                                                    aria-expanded="false" disabled>
                                                        <span class="sr-only"><fmtSpring:message
                                                                code="page.text.toggle.dropdown"/></span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row flex-nowrap no-gutters h-100">
                                <div class="col-auto py-1 px-2">
                                    <ul class="nav nav-column py-4 px-1">
                                        <li class="nav-item d-flex my-2">
                                            <a id="content-options-toggle" href="#"
                                               data-toggle="tooltip" data-placement="right"
                                               title="${msgpt:getMessage('page.label.content')}"
                                               class="nav-link nav-toggle position-relative border-0 text-body active" role="button">
                                                <span class="d-flex align-items-center justify-content-center square-lg pr-2 py-1">
                                                    <i class="fad fa-newspaper" aria-hidden="true"></i>
                                                </span>
                                                <span class="sr-only"><fmtSpring:message
                                                        code="page.label.content"/></span>
                                                <span class="position-absolute right-0 top-30 fs-xs mr-2 px-1 text-muted">
                                                    <i class="fas fa-caret-right fa-sm line-height-1" aria-hidden="true"></i>
                                                </span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col position-static bg-white">
                                    <c:if test="${not empty param.saveSuccess && param.saveSuccess == 'true'}">
                                        <msgpt:Information type="success" floating="true">
                                            <fmtSpring:message code="page.label.save.complete"/>
                                        </msgpt:Information>
                                    </c:if>
                                    <form:errors path="*">
                                        <msgpt:Information errorMsgs="${messages}" type="error" floating="true"/>
                                    </form:errors>
                                    <div class="row no-gutters flex-nowrap bg-lightest h-100">
                                        <div id="content-options-col"
                                             class="col-3 mr-3 d-flex flex-column py-4">
                                            <div class="d-flex flex-column h-100 bg-white border rounded">
                                                <ul class="nav nav-tabs-line nav-fill px-4 border-bottom" id="myTab"
                                                    role="tablist">
                                                    <li class="nav-item" role="presentation">
                                                        <a class="nav-link w-100 p-0 py-3 active" href="#"
                                                           id="content-tab"
                                                           data-toggle="tab"
                                                           data-target="#content-tab-panel"
                                                           role="tab" aria-controls="content-tab-panel"
                                                           aria-selected="true">
                                                            <fmtSpring:message code="page.label.content"/>
                                                        </a>
                                                    </li>
                                                    <li class="nav-item" role="presentation">
                                                        <a class="nav-link w-100 p-0 py-3" href="#" id="variants-tab"
                                                           data-toggle="tab"
                                                           data-target="#variants-tab-panel"
                                                           role="tab" aria-controls="variants-tab-panel"
                                                           aria-selected="false">
                                                            <fmtSpring:message code="page.label.variants"/>
                                                        </a>
                                                    </li>
                                                    <c:if test="${contentObject.multipartType}">
                                                        <li class="nav-item" role="presentation">
                                                            <a class="nav-link w-100 p-0 py-3" href="#"
                                                               id="delivery-tab"
                                                               data-toggle="tab"
                                                               data-target="#delivery-tab-panel"
                                                               role="tab" aria-controls="delivery-tab-panel"
                                                               aria-selected="false">
                                                                <fmtSpring:message code="page.label.Delivery"/>
                                                            </a>
                                                        </li>
                                                    </c:if>
                                                </ul>
                                                <div class="tab-content flex-grow-1 fs-sm p-4" id="myTabContent">
                                                    <div class="tab-pane fade show active" id="content-tab-panel"
                                                         role="tabpanel"
                                                         aria-labelledby="content-tab">
                                                        <!-- Language Selection -->
                                                        <div id="languageSelectionSection" class="d-none">
                                                            <div class="mb-3 pb-2 border-bottom font-weight-bold">
                                                                <fmtSpring:message
                                                                        code="client_messages.text.language"/>
                                                            </div>
                                                            <div class="input-group input-group-sm mb-4">
                                                                <select class="custom-select custom-select-sm"
                                                                        id="contentLanguageSelect"
                                                                        aria-label="${msgpt:getMessage('client_messages.content_editor.content_language')}"></select>
                                                            </div>
                                                        </div>
                                                        <!-- Multipart select -->
                                                        <c:if test="${contentObject.multipartType}">
                                                            <div id="multipartInterfaceContainer" class="mt-4"
                                                                 style="${contentType.id == contentType_CUSTOM ? '' : 'display: none;'}">
                                                                <div class="mb-3 pb-2 border-bottom font-weight-bold">
                                                                    <fmtSpring:message
                                                                            code="page.label.multipart"/>
                                                                </div>
                                                                <!-- PART SELECT -->
                                                                <div class="form-group mb-3">
                                                                    <c:set var="zoneId"
                                                                           value="${contentObject.zone.id}"/>
                                                                    <c:set var="zoneParts"
                                                                           value="${contentObject.zone.partsInOrder}"/>
                                                                    <label for="partSelect_zone${zoneId}">
                                                                        <fmtSpring:message code="page.label.part"/>
                                                                    </label>
                                                                    <div id="partSelectDiv_zone${zoneId}">
                                                                        <select id="partSelect_zone${zoneId}"
                                                                                class="custom-select custom-select-sm"
                                                                                onchange="javascript:changePartSelect(this);">
                                                                            <c:forEach var="currentPart"
                                                                                       items="${zoneParts}"
                                                                                       varStatus="partsStat">
                                                                                <option id="partOption_zone${zoneId}_part${currentPart.id}"
                                                                                        value="zone${zoneId}_part${currentPart.id}"
                                                                                        content="${currentPart.contentType.id == textContentTypeID ? 'text' : 'graphic'}">
                                                                                    <c:out value="${currentPart.nameInContext}"/></option>
                                                                            </c:forEach>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                                <!-- CONTENT SELECT -->
                                                                <!-- Determine number of multipart zones -->
                                                                <c:set var="currentZone"
                                                                       value="${contentObject.zone}"/>
                                                                <!-- MULTIPART: Generate Zone Content Selection for each multipart zone -->
                                                                <c:set var="zoneId"
                                                                       value="${command.viewingMPContent.zoneVO.zoneId}"/>
                                                                <c:set var="zoneParts"
                                                                       value="${command.viewingMPContent.zoneVO.parts}"/>
                                                                <div id="zoneContentSelect_zone${zoneId}"
                                                                     style="${currentZone.id == zoneId ? '' : 'display: none;'}">
                                                                    <c:forEach items="${zoneParts}" var="part"
                                                                               varStatus="partsStat">
                                                                        <c:set var="partContentId"
                                                                               value="${command.viewingMPContent.zoneVO.parts[partsStat.index].emptyZonePart == true ? -99 : 0}"/>
                                                                        <c:set var="currentPart"
                                                                               value="${part.zonePart}"/>
                                                                        <div class="zoneContentMenu_zone${zoneId}_part${currentPart.id}"
                                                                             style="${partsStat.index == 0 ? '' : 'display: none;'}">
                                                                            <div class="mb-2 custom-control custom-switch">
                                                                                <form:checkbox
                                                                                        id="customContentSelect_zone${zoneId}_part${currentPart.id}"
                                                                                        path="viewingMPContent.zoneVO.parts[${partsStat.index}].emptyZonePart"
                                                                                        cssClass="custom-control-input"
                                                                                        onclick="customContentSelectChange(this);"/>
                                                                                <label for="customContentSelect_zone${zoneId}_part${currentPart.id}"
                                                                                       class="custom-control-label"><fmtSpring:message
                                                                                        code="client_messages.content_editor.leave_empty"/></label>
                                                                                <c:if test="${partsStat.index == 0}">
                                                                                    <input type="hidden"
                                                                                           id="defaultZoneContent_zone${zoneId}"
                                                                                           value="${partContentId}"/>
                                                                                </c:if>
                                                                                <c:if test="${partsStat.index == 0}">
                                                                                    <input type="hidden"
                                                                                           id="currentContentId"
                                                                                           value="zone${zoneId}_part${currentPart.id}"/>
                                                                                </c:if>
                                                                            </div>
                                                                        </div>
                                                                    </c:forEach>
                                                                </div>
                                                                <!-- END CONTENT SELECT -->
                                                            </div>
                                                        </c:if>
                                                        <!-- Same as parent selection -->
                                                        <div id="same-as-parent-container" class="mt-4"></div>
                                                        <!-- Mini editors -->
                                                        <div id="mini-editor"
                                                             class="contentEditor_MiniEditorsContainer"></div>
                                                    </div>
                                                    <div class="tab-pane fade" id="variants-tab-panel" role="tabpanel"
                                                         aria-labelledby="variants-tab">
                                                        <div id="variantSelectTreePanel" class="h-100">
                                                            <div class="d-flex flex-column h-100">
                                                                <msgpt:VariantTree
                                                                        id="variantSelect"
                                                                        selectedNodeId="${param.paramInstId}"
                                                                        style="max-height: 24rem"
                                                                        cssClass="variantSelectionContainer w-100 overflow-auto"
                                                                        expanded="true"
                                                                        dataBinding="${contentObject}"
                                                                        showContentStatus="true"
                                                                        onChange="variantSelected"
                                                                        canUpdate="${command.canUpdate}"
                                                                        onContextMenuSelect="contextMenuOptionSelected"/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <c:if test="${contentObject.multipartType}">
                                                        <div class="tab-pane fade" id="delivery-tab-panel"
                                                             role="tabpanel"
                                                             aria-labelledby="delivery-tab">
                                                            <!-- TOUCHPOINT WIDGET -->
                                                            <div id="docDisplay_${touchpointContext.id}">
                                                                <msgpt:DocumentTag
                                                                        document="${touchpointContext}"
                                                                        contentObject="${contentObject}"
                                                                        type="standard"
                                                                        readOnly="true"
                                                                        hasPartAction="true"
                                                                        maxWidth="300"/>
                                                            </div>
                                                            <!-- DELIVERY SUMMARY -->
                                                            <div class="mt-3 p-3 bg-white border rounded text-center fs-xs">
                                                                <div class="mb-1">
                                                                    <fmtSpring:message
                                                                            code="page.label.TARGET.ZONE"/>&nbsp;
                                                                    (${message.deliveryTypeText}&nbsp;
                                                                    <fmtSpring:message
                                                                            code="page.text.delivery"/>)
                                                                </div>
                                                                <div id="deliveryNavTree">
                                                                    <c:out value="${contentObject.deliveryNavTreeHTML}"
                                                                           escapeXml="false"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </c:if>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col position-static bg-white border-left">
                                            <div class="d-flex flex-column h-100">
                                                <div id="editorPanel" class="editorContentPanel contentPanel">
                                                    <c:if test="${ not contentObject.multipartType }">
                                                        <c:set var="currentZone" value="${contentObject.zone}"/>
                                                        <!-- DEFAULT Content by language -->
                                                        <div id="contentData" globalState="edit"
                                                             globalRefObjLabel="UNUSED"
                                                             style="display: none;">
                                                            <div class="contentGroup"
                                                                 groupId="zone${contentObject.id}"
                                                                 groupState="inherit"
                                                                 backgroundColor="${contentObject.zoneBackgroundColor}"
                                                                 canvasDimensions="${contentObject.canvasDimensions}"
                                                                 canvasRotation="${contentObject.canvasRotation}"
                                                                 content="${(contentObject.isText || contentObject.isMarkup || contentObject.isFreeform || contentObject.isVideoDelivery) ? 'text' : 'graphic'}">
                                                                <c:forEach var="locale" items="${locales}"
                                                                           varStatus="langStat">
                                                                    <c:set var="contentState" value="inherit"/>
                                                                    <c:if test="${command.viewingContent.langContentMap[locale.id].reference}">
                                                                        <c:set var="contentState"
                                                                               value="sameAsParent"/>
                                                                    </c:if>

                                                                    <div class="contentEntry"
                                                                         contentState="${contentState}"
                                                                         localeId="${locale.id}">

                                                                        <form:checkbox
                                                                                path="viewingContent.langContentMap[${locale.id}].reference"
                                                                                cssClass="sameAsParentInput custom-control-input"/>
                                                                        <c:if test="${contentObject.isText || contentObject.isMarkup || contentObject.isFreeform || contentObject.isVideoDelivery}">
                                                                            <div class="textContentInput">
                                                                                <form:textarea
                                                                                        cssClass="mceEditor_textContentInput textContentInput ${locale.id == defaultLocaleId ? 'defaultLocaleContent' : ''}"
                                                                                        path="viewingContent.langContentMap[${locale.id}].content"/>
                                                                            </div>
                                                                        </c:if>
                                                                        <c:if test="${contentObject.isGraphic}">
                                                                            <c:if test="${not contentObject.isTouchpointLocal}">
                                                                                <div class="useImageLibrary">
                                                                                    <div class="useContentLibraryToggle">
                                                                                        <form:checkbox
                                                                                                path="viewingContent.langContentMap[${locale.id}].isGlobalImageLibrary"/>
                                                                                    </div>
                                                                                    <div class="useContentLibrarySelect">
                                                                                        <form:select
                                                                                                id="conLibSelect_${currentZone.id}_0_${locale.id}"
                                                                                                cssClass="inputXL"
                                                                                                onchange=""
                                                                                                path="viewingContent.langContentMap[${locale.id}].imageLibraryId">
                                                                                            <option id="0"
                                                                                                    value="0">
                                                                                                <fmtSpring:message
                                                                                                        code="page.text.loading"/></option>
                                                                                        </form:select>
                                                                                    </div>
                                                                                    <c:if test="${not contentObject.isGlobalImage}">
                                                                                        <div class="useLocalContentLibraryToggle">
                                                                                            <form:checkbox
                                                                                                    path="viewingContent.langContentMap[${locale.id}].isLocalImageLibrary"/>
                                                                                        </div>
                                                                                        <div class="useLocalContentLibrarySelect">
                                                                                            <form:select
                                                                                                    id="localConLibSelect_${currentZone.id}_0_${locale.id}"
                                                                                                    cssClass="inputXL"
                                                                                                    onchange=""
                                                                                                    path="viewingContent.langContentMap[${locale.id}].imageLibraryId">
                                                                                                <option id="0"
                                                                                                        value="0">
                                                                                                    <fmtSpring:message
                                                                                                            code="page.text.loading"/></option>
                                                                                            </form:select>
                                                                                        </div>
                                                                                    </c:if>
                                                                                    <form:hidden
                                                                                            cssClass="useImageLibraryBinding"
                                                                                            path="viewingContent.langContentMap[${locale.id}].useImageLibrary"/>

                                                                                    <form:hidden
                                                                                            cssClass="imageLibraryObjectTypeBinding"
                                                                                            path="viewingContent.langContentMap[${locale.id}].imageLibraryObjectType"/>

                                                                                    <input type="hidden"
                                                                                           id="defaultContentLibraryId_${currentZone.id}_0_${locale.id}"
                                                                                           value="${command.viewingContent.langContentMap[locale.id].imageLibraryId}"/>
                                                                                    <c:if test="${not contentObject.isGlobalImage}">
                                                                                        <input type="hidden"
                                                                                               id="defaultLocalContentLibraryId_${currentZone.id}_0_${locale.id}"
                                                                                               value="${command.viewingContent.langContentMap[locale.id].imageLibraryId}"/>
                                                                                        <input type="hidden"
                                                                                               id="documentId_${currentZone.id}_0_${locale.id}"
                                                                                               value="${touchpointContext.id}"/>
                                                                                    </c:if>
                                                                                    <input type="hidden"
                                                                                           id="extType_${currentZone.id}_0_${locale.id}"
                                                                                           value="${currentZone.subContentType.name}"/>
                                                                                    <input type="hidden"
                                                                                           id="contentObjectId_${locale.id}"
                                                                                           value="${contentObject.id}"/>
                                                                                </div>
                                                                            </c:if>
                                                                            <c:if test="${!parentLangContentMap.isEmpty()}">
                                                                                <div class="parentGraphicFileInput"
                                                                                     imgPath="${parentLangContentMap[locale.id].imageLocation}"
                                                                                     imgName="${parentLangContentMap[locale.id].imageName}"
                                                                                     contentId="${parentLangContentMap[locale.id].contentId}"
                                                                                     uploadDate="<fmtJSTL:formatDate value="${parentLangContentMap[locale.id].imageUploadedDate}" pattern="${dateTimeFormat}"/>"
                                                                                     apppliedImageFilename="${parentLangContentMap[locale.id].appliedImageFilename}"
                                                                                     imageLink="${parentLangContentMap[locale.id].imageLink.encodedValue}"
                                                                                     imageAltText="${parentLangContentMap[locale.id].imageAltText.encodedValue}"
                                                                                     imageExtLink="${parentLangContentMap[locale.id].imageLink.encodedValue}"
                                                                                     imageExtPath="${parentLangContentMap[locale.id].imageExtPath.encodedValue}"
                                                                                     fileType="${contentObject.isTouchpointLocal ? 'ANY_IMAGE' : currentZone.graphicTypeRegEx}"
                                                                                >
                                                                                </div>
                                                                            </c:if>
                                                                            <div class="graphicFileInput"
                                                                                 imgPath="${command.viewingContent.langContentMap[locale.id].imageLocation}"
                                                                                 imgName="${command.viewingContent.langContentMap[locale.id].imageName}"
                                                                                 contentId="${command.viewingContent.langContentMap[locale.id].contentId}"
                                                                                 uploadDate="<fmtJSTL:formatDate value="${command.viewingContent.langContentMap[locale.id].imageUploadedDate}" pattern="${dateTimeFormat}"/>"
                                                                                 fileType="${contentObject.isTouchpointLocal ? 'ANY_IMAGE' : currentZone.graphicTypeRegEx}">
                                                                                <form:input
                                                                                        path="viewingContent.langContentMap[${locale.id}].sandboxFileId"
                                                                                        style="display: none;"/>
                                                                            </div>
                                                                            <div class="graphicAppliedImageName">
                                                                                <msgpt:InputFilter
                                                                                        type="imageName">
                                                                                    <form:input
                                                                                            path="viewingContent.langContentMap[${locale.id}].appliedImageFilename"
                                                                                            maxlength="40"
                                                                                            cssClass="inputL"/>
                                                                                </msgpt:InputFilter>
                                                                            </div>
                                                                            <c:if test="${(contentObject.isMessage && currentZone.applyImageLink) || (!contentObject.isMessage && contentObject.deliveredToWebOrEmailTouchpoint)}">
                                                                                <div class="graphicImageLink">
                                                                                    <form:textarea
                                                                                            cssClass="mceEditor_graphicImageLink imageLinkInput"
                                                                                            path="viewingContent.langContentMap[${locale.id}].imageLink.encodedValue"/>
                                                                                </div>
                                                                            </c:if>
                                                                            <c:if test="${(contentObject.isMessage && currentZone.applyAltText) || !contentObject.isMessage}">
                                                                                <div class="graphicImageAltText">
                                                                                    <form:textarea
                                                                                            cssClass="mceEditor_graphicImageAltText imageAltTextInput"
                                                                                            path="viewingContent.langContentMap[${locale.id}].imageAltText.encodedValue"/>
                                                                                </div>
                                                                            </c:if>
                                                                            <c:if test="${(contentObject.isMessage && (currentZone.applyImageExtLink || channelZoneApplyImageExtLink)) || (!contentObject.isMessage && (contentObject.deliveredToWebOrEmailTouchpoint || contentObject.isOmniChannel))}">
                                                                                <div class="graphicImageExtLink">
                                                                                    <form:textarea
                                                                                            cssClass="mceEditor_graphicImageExtLink imageExtLinkInput"
                                                                                            path="viewingContent.langContentMap[${locale.id}].imageExtLink.encodedValue"/>
                                                                                </div>
                                                                            </c:if>
                                                                            <c:if test="${(contentObject.isMessage && currentZone.applyImageExtPath) || (!contentObject.isMessage && (contentObject.deliveredToSefasTouchpoint || contentObject.GMCTouchpoint))}">
                                                                                <div class="graphicImageExtPath">
                                                                                    <form:textarea
                                                                                            cssClass="mceEditor_graphicImageExtPath imageExtPathInput"
                                                                                            path="viewingContent.langContentMap[${locale.id}].imageExtPath.encodedValue"/>
                                                                                </div>
                                                                            </c:if>
                                                                        </c:if>
                                                                    </div>
                                                                </c:forEach>
                                                            </div>
                                                        </div>
                                                    </c:if>
                                                    <c:if test="${ contentObject.multipartType }">
                                                        <div id="contentData" globalState="edit"
                                                             globalRefObjLabel="UNUSED"
                                                             style="display: none;">

                                                            <c:set var="zoneId"
                                                                   value="${command.viewingMPContent.zoneVO.zoneId}"/>
                                                            <c:set var="zoneParts"
                                                                   value="${command.viewingMPContent.zoneVO.parts}"/>

                                                            <c:forEach items="${zoneParts}" var="part"
                                                                       varStatus="partsStat">
                                                                <c:set var="mpVO"
                                                                       value="${command.viewingMPContent.zoneVO.parts[partsStat.index]}"/>
                                                                <c:set var="currentPart"
                                                                       value="${part.zonePart}"/>

                                                                <c:set var="groupState" value="edit"/>
                                                                <c:choose>
                                                                    <c:when test="${mpVO.emptyZonePart == true}">
                                                                        <c:set var="groupState"
                                                                               value="leaveEmpty"/>
                                                                    </c:when>
                                                                    <c:when test="${mpVO.emptyZonePart == false}">
                                                                        <c:set var="groupState" value="edit"/>
                                                                    </c:when>
                                                                </c:choose>

                                                                <div class="contentGroup"
                                                                     groupId="part${currentPart.id}"
                                                                     groupState="${groupState}"
                                                                     groupRefObjLabel="UNUSED"
                                                                     backgroundColor="${currentPart.backgroundColor}"
                                                                     canvasDimensions="${currentPart.canvasDimensions}"
                                                                     ,
                                                                     canvasRotation="${currentPart.canvasRotation}"
                                                                     content="${currentPart.contentType.id == textContentTypeID ? 'text' : 'graphic'}">
                                                                    <c:forEach var="locale" items="${locales}"
                                                                               varStatus="langStat">
                                                                        <c:set var="contentState"
                                                                               value="inherit"/>
                                                                        <c:if test="${command.viewingMPContent.zoneVO.parts[partsStat.index].languageContentVOs[locale.id].reference}">
                                                                            <c:set var="contentState"
                                                                                   value="sameAsParent"/>
                                                                        </c:if>

                                                                        <div class="contentEntry"
                                                                             contentState="${contentState}"
                                                                             localeId="${locale.id}">

                                                                            <form:checkbox
                                                                                    path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs['${locale.id}'].reference"
                                                                                    cssClass="sameAsParentInput custom-control-input"/>
                                                                            <c:if test="${currentPart.contentType.id == textContentTypeID}">
                                                                                <div class="textContentInput">
                                                                                    <form:textarea
                                                                                            class="${locale.id == defaultLocaleId ? 'defaultLocaleContent' : ''}"
                                                                                            path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs['${locale.id}'].content"/>
                                                                                </div>
                                                                            </c:if>
                                                                            <c:if test="${currentPart.contentType.id == graphicContentTypeID}">
                                                                                <div class="useImageLibrary">
                                                                                    <div class="useContentLibraryToggle">
                                                                                        <form:checkbox
                                                                                                path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs[${locale.id}].isGlobalImageLibrary"/>
                                                                                    </div>
                                                                                    <div class="useContentLibrarySelect">
                                                                                        <form:select
                                                                                                id="conLibSelect_${zoneId}_${currentPart.id}_${locale.id}"
                                                                                                cssClass="inputXL"
                                                                                                onchange=""
                                                                                                path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs[${locale.id}].imageLibraryId">
                                                                                            <option id="0"
                                                                                                    value="0">
                                                                                                <fmtSpring:message
                                                                                                        code="page.text.loading"/></option>
                                                                                        </form:select>
                                                                                    </div>
                                                                                    <div class="useLocalContentLibraryToggle">
                                                                                        <form:checkbox
                                                                                                path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs[${locale.id}].isLocalImageLibrary"/>
                                                                                    </div>
                                                                                    <div class="useLocalContentLibrarySelect">
                                                                                        <form:select
                                                                                                id="localConLibSelect_${zoneId}_${currentPart.id}_${locale.id}"
                                                                                                cssClass="inputXL"
                                                                                                onchange=""
                                                                                                path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs[${locale.id}].imageLibraryId">
                                                                                            <option id="0"
                                                                                                    value="0">
                                                                                                <fmtSpring:message
                                                                                                        code="page.text.loading"/></option>
                                                                                        </form:select>
                                                                                    </div>
                                                                                    <form:hidden
                                                                                            cssClass="useImageLibraryBinding"
                                                                                            path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs[${locale.id}].useImageLibrary"/>
                                                                                    <form:hidden
                                                                                            cssClass="imageLibraryObjectTypeBinding"
                                                                                            path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs[${locale.id}].imageLibraryObjectType"/>
                                                                                    <input type="hidden"
                                                                                           id="contentObjectId_${locale.id}"
                                                                                           value="${contentObject.id}"/>
                                                                                    <input type="hidden"
                                                                                           id="defaultContentLibraryId_${zoneId}_${currentPart.id}_${locale.id}"
                                                                                           value="${mpVO.languageContentVOs[locale.id].imageLibraryId}"/>
                                                                                    <input type="hidden"
                                                                                           id="defaultLocalContentLibraryId_${zoneId}_${currentPart.id}_${locale.id}"
                                                                                           value="${mpVO.languageContentVOs[locale.id].imageLibraryId}"/>
                                                                                    <input type="hidden"
                                                                                           id="extType_${zoneId}_${currentPart.id}_${locale.id}"
                                                                                           value="${currentPart.subContentType.name}"/>
                                                                                    <input type="hidden"
                                                                                           id="documentId_${zoneId}_${currentPart.id}_${locale.id}"
                                                                                           value="${touchpointContext.id}"/>
                                                                                </div>
                                                                                <div class="graphicFileInput"
                                                                                     imgPath="${mpVO.languageContentVOs[locale.id].imageLocation}"
                                                                                     imgName="${mpVO.languageContentVOs[locale.id].imageName}"
                                                                                     contentId="${mpVO.languageContentVOs[locale.id].contentId}"
                                                                                     uploadDate="<fmtJSTL:formatDate value="${mpVO.languageContentVOs[locale.id].imageUploadedDate}" pattern="${dateTimeFormat}"/>"
                                                                                     fileType="${currentPart.graphicTypeRegEx}">
                                                                                    <form:input
                                                                                            path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs['${locale.id}'].sandboxFileId"
                                                                                            style="display: none;"/>
                                                                                </div>
                                                                                <div class="graphicAppliedImageName">
                                                                                    <msgpt:InputFilter
                                                                                            type="imageName">
                                                                                        <form:input
                                                                                                path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs['${locale.id}'].appliedImageFilename"
                                                                                                maxlength="40"
                                                                                                cssClass="inputL"/>
                                                                                    </msgpt:InputFilter>
                                                                                </div>
                                                                                <c:if test="${currentPart.zone.applyImageLink}">
                                                                                    <div class="graphicImageLink">
                                                                                        <form:textarea
                                                                                                cssClass="mceEditor_graphicImageLink imageLinkInput"
                                                                                                path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs[${locale.id}].imageLink.encodedValue"/>
                                                                                    </div>
                                                                                </c:if>
                                                                                <c:if test="${currentPart.zone.applyAltText}">
                                                                                    <div class="graphicImageAltText">
                                                                                        <form:textarea
                                                                                                cssClass="mceEditor_graphicImageAltText imageAltTextInput"
                                                                                                path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs[${locale.id}].imageAltText.encodedValue"/>
                                                                                    </div>
                                                                                </c:if>
                                                                                <c:if test="${currentPart.zone.applyImageExtLink || channelZoneApplyImageExtLink}">
                                                                                    <div class="graphicImageExtLink">
                                                                                        <form:textarea
                                                                                                cssClass="mceEditor_graphicImageExtLink imageExtLinkInput"
                                                                                                path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs[${locale.id}].imageExtLink.encodedValue"/>
                                                                                    </div>
                                                                                </c:if>
                                                                                <c:if test="${currentPart.zone.applyImageExtPath}">
                                                                                    <div class="graphicImageExtPath">
                                                                                        <form:textarea
                                                                                                cssClass="mceEditor_graphicImageExtPath imageExtPathInput"
                                                                                                path="viewingMPContent.zoneVO.parts[${partsStat.index}].languageContentVOs[${locale.id}].imageExtPath.encodedValue"/>
                                                                                    </div>
                                                                                </c:if>
                                                                            </c:if>
                                                                        </div>
                                                                    </c:forEach>
                                                                </div>
                                                            </c:forEach>
                                                        </div>
                                                    </c:if>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <jsp:include page="dynamic_variant_popup.jsp"/>
                    </c:if>
                </form:form>
            </div>
        </div>
    </msgpt:BodyNew>
</msgpt:Html5>