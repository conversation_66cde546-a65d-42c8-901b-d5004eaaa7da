<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.model.Document" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">
        <msgpt:CalendarIncludes/>

        <msgpt:Stylesheet href="workflow/workflow.css"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/dragAndDropSelect/jquery.dragAndDropSelect.css"/>

        <msgpt:Script src="includes/javascript/popupActions.js" />

        <style>
            #approverSelectTable {
                box-sizing: border-box;
            }
        </style>

        <msgpt:Script>
            <script>
                // Scheduler
                function loadTimeSelect(index) {
                    if ($('#startDateCal_' + index).length == 0)
                        return;
                    var hours = new Array;
                    hours[0] = createOption("--");
                    hours[1] = createOption("01");
                    hours[2] = createOption("02");
                    hours[3] = createOption("03");
                    hours[4] = createOption("04");
                    hours[5] = createOption("05");
                    hours[6] = createOption("06");
                    hours[7] = createOption("07");
                    hours[8] = createOption("08");
                    hours[9] = createOption("09");
                    hours[10] = createOption("10");
                    hours[11] = createOption("11");
                    hours[12] = createOption("12");
                    setOptions("time_hours_"+index,hours);

                    var minutes = new Array;
                    minutes[0] = createOption("--");
                    minutes[1] = createOption("00");
                    minutes[2] = createOption("15");
                    minutes[3] = createOption("30");
                    minutes[4] = createOption("45");
                    setOptions("time_minutes_"+index,minutes);

                    var am_pm = new Array;
                    am_pm[0] = createOption("AM");
                    am_pm[1] = createOption("PM");
                    setOptions("time_AM_PM_"+index,am_pm);

                    var rawDateTime = trim($('#hiddenDateTimeField_'+index).val());
                    var dateTimeComponents = rawDateTime.split(' ');

                    if ( rawDateTime.indexOf(',') != -1 ) {
                        $('#startDateCal_' + index).val(dateTimeComponents[0]+" "+dateTimeComponents[1]+" "+dateTimeComponents[2]);
                    }
                    if ( rawDateTime.indexOf(',') != -1 && rawDateTime.indexOf(':') != -1 ) {
                        var time = dateTimeComponents[3];
                        setSelectValue("time_hours_"+index, time.split(':')[0]);
                        setSelectValue("time_minutes_"+index, time.split(':')[1]);
                        setSelectValue("time_AM_PM_"+index, dateTimeComponents[4]);
                    } else if ( rawDateTime.indexOf(':') != -1 ) {
                        var time = dateTimeComponents[0];
                        setSelectValue("time_hours_"+index, time.split(':')[0]);
                        setSelectValue("time_minutes_"+index, time.split(':')[1]);
                        setSelectValue("time_AM_PM_"+index, dateTimeComponents[1]);
                    }
                }

                function setSelectValue(id, value) {
                    var selectElement = document.getElementById(id);
                    for (var i=0; i<selectElement.options.length; i++)
                        if (selectElement.options[i].innerHTML == value)
                            selectElement.options[i].selected = 'true';
                }

                function createOption(optionName) {
                    var option = new Option(optionName);
                    return option;
                }

                function setOptions(id, optionArray) {
                    selectElement = document.getElementById(id);
                    for (var i=0; i<optionArray.length; i++) {
                        selectElement.options[i] = optionArray[i];
                    }
                }

                function timeChange(element, index) {
                    var hour = $('#time_hours_'+index);
                    var minutes = $('#time_minutes_'+index);
                    if (element.options[element.selectedIndex].innerHTML == '--') {
                        hour.options[0].selected = 'true';
                        minutes.options[0].selected = 'true';
                    }
                    setTimeDateBindingValue(index);
                }

                function setTimeDateBindingValue(index) {
                    var time = getTimeValue(index);
                    var date = $('#startDateCal_'+index).val();
                    $('#hiddenDateTimeField_'+index).val(date + " " + time);
                }

                function getTimeValue(index) {
                    var hour = $('#time_hours_'+index);
                    var minutes = $('#time_minutes_'+index);
                    var am_pm = $('#time_AM_PM_'+index);

                    var time = $(hour).val() + ":" + $(minutes).val() + " " + $(am_pm).val();
                    if ( time.indexOf("--") != -1 )
                        time = "";
                    return time;
                }

                // ***************** Approvers Drag and Drop Functions ************************

                // Drag: Init assignable approvers as draggable
                function initDraggable() {
                    $('#approverSelectTable tbody tr').each(function () {
                        var elem = $(this);
                        $(elem).draggable({
                            cursor: 'default',
                            cursorAt: {top: 35, left: 35},
                            opacity: 0.8,
                            helper: function (e) {

                                // Highlight Drag Item: highlight target drag item if not already
                                if (!$(elem).hasClass('row_selected')){
                                    $(elem).addClass('row_selected');
                                }

                                // Build/Return Helper Icon: icon displays number of items being dragged
                                var itemCount = $('#approverSelectTable tbody tr.row_selected').length;
                                let retVal = $('<div class="dragAndDropToken dragAndDropTokenX">' +
                                    '<div style=\"padding-top: 7px; padding-right: 3px;\">' + itemCount + '</div>' +
                                    '</div>');

                                return retVal;
                            },
                            scroll: false,
                            appendTo: 'body'
                        });

                    });
                }

                // Drop: Init selected approvers drop area (anywhere in target step)
                function initDroppable() {

                    $("[id^='approvalContainer']").each(function () {
                        $(this).droppable({
                            accept: 'tr.row_selected',
                            tolerance: 'pointer',
                            drop: function (event, ui) {

                                var targetStep = this;
                                var targetStepIndex = parseId(targetStep);
                                $('tr.row_selected').each(function () {
                                    $(this).find("[id^='assignableApprover']").each(function () {
                                        var currentUserId = parseId(this);
                                        $(targetStep)
                                            .find("[id^='approverCheck_" + targetStepIndex + "_'][value='" + currentUserId + "']")
                                            .attr('checked', 'checked');
                                        $(targetStep)
                                            .find("[id^='translatorCheck_" + targetStepIndex + "_'][value='" + currentUserId + "']")
                                            .attr('checked', 'checked');
                                    });
                                });
                                updateAssignedApproversDisplay(targetStepIndex);
                                clearApproversTable();

                            },
                            over: function (event, ui) {
                                toggleToken('over', ui.helper);
                            },
                            out: function (event, ui) {
                                toggleToken('out', ui.helper);
                            }
                        });
                    });

                }

                // Helper token: Toggle display
                function toggleToken(action, helperEle) {
                    if (action == 'over')
                        $(helperEle).removeClass('dragAndDropTokenX').addClass('dragAndDropTokenCheck');
                    else
                        $(helperEle).removeClass('dragAndDropTokenCheck').addClass('dragAndDropTokenX');
                }

                // ***************** Step Action Functions ************************

                // Add step: Add new step to end of step display
                function addAction() {
                    // Max number of step revealed: Do nothing
                    if ($("[id^='approvalContainer']").length == $("[id^='approvalContainer']:visible").length) {
                        return;
                    }

                    var newStepEnabledEleId = parseId($("[id^='approvalStepEnabled']:not(:checked):first"));
                    $('#approvalStepEnabled_' + newStepEnabledEleId).attr('checked', 'checked');
                    $('#approvalStepEnabled_' + newStepEnabledEleId).attr('value', 'true');
                    $('#flipToggle_isEditable_' + newStepEnabledEleId).attr('checked', 'checked');
                    $('#flipToggle_isEditable_' + newStepEnabledEleId).attr('value', 'true');

                    if ($("[id^='approvalContainer']:visible:last").length != 0) {
                        var lastStepEnabledEleId = parseId($("[id^='approvalContainer']:visible:last"));
                        $('#approvalContainer_' + lastStepEnabledEleId).after($('#approvalStepEnabled_' + newStepEnabledEleId).closest("[id^='approvalContainer']"));
                    }

                    $('#approvalContainer_' + newStepEnabledEleId).showEle('normal');

                    initFlipToggles();

                    reorderApprovals();

                    if ($("[id^='approvalContainer']").length == $("[id^='approvalContainer']:visible").length) {
                        $('#addButton').hide();
                    }
                }

                // Remove step: Remove selected step
                function removeAction(index) {
                    $('#approvalContainer_' + index).hide();
                    $('#approvalContainer_' + index).find('input:checkbox').each(function () {
                        $(this).removeAttr('checked');
                    });
                    $('#approvalContainer_' + index).find('input').each(function () {
                        // Clear all inputs values except for selected approvers checkbox bindings
                        if ($(this).attr('id') != undefined && $(this).attr('id').indexOf('approverCheck_') == -1)
                            $(this).val('');
                    });
                    $('#approvalOrderInput_' + index).val(0);
                    $('#flipToggle_approvalType_' + index).iButton("toggle", false);
                    $('#flipToggle_dueBy_' + index).iButton("toggle", false);
                    $('#flipToggle_translationStep_' + index).iButton("toggle", false);
                    $('#flipToggle_allowEditDefault_' + index).iButton("toggle", false);

                    updateAssignedApproversDisplay(index);

                    reorderApprovals();

                    if ($("[id^='approvalContainer']").length > $("[id^='approvalContainer']:visible").length) {
                        $('#addButton').show();
                    }
                }

                // Re-arrange the approvals by their orders
                // [IS THIS NEEDED]  Orginally implemented to control step ordering on load
                function rearrangeApprovalsByOrder() {
                    // Count how many steps are in used (maximum is 5)
                    var numStepInUse = $("[id^='approvalStepEnabled']:checked").length;
                    var finished = 0;
                    var lastIndex = 0;
                    var order = 1;
                    while (finished < numStepInUse) {
                        $("[id^='approvalOrderInput']").each(function () {
                            var index = $(this).attr('id').substring($(this).attr('id').indexOf('_') + 1);
                            if ($(this).val() == order) {
                                if ($(this).val() != 1)
                                    $('#approvalContainer_' + lastIndex).after($('#approvalContainer_' + index));
                                lastIndex = index;
                                finished++;
                                order++;
                            }
                        });
                    }
                }

                // Step ordering: Update step indicators
                function reorderApprovals() {
                    var order = 1;
                    $("[id^='approvalOrderInput']").each(function () {
                        var index = $(this).attr('id').substring($(this).attr('id').indexOf('_') + 1);
                        if ($('#approvalStepEnabled_' + index).is(':checked')) {
                            $('#approvalOrderDisplay_' + index).html(order);
                            $(this).val(order++);
                        }
                    });
                    updateNextStepIndicators();
                    updateInfoSystemsForPage();
                }

                // Step ordering: Order steps up and down
                function orderAction(action, stepId) {
                    var currentStepOrder = $('#approvalOrderInput_' + stepId).val();

                    if (action == 'up') {

                        // First step: Can't order up
                        if (currentStepOrder == "1")
                            return;

                        $("[id^='approvalOrderInput']").each(function () {
                            if ($(this).val() == (parseInt(currentStepOrder) - 1)) {
                                $('#approvalContainer_' + stepId).hide();
                                $('#approvalContainer_' + stepId).find('#stepControlTable_' + stepId).hide();
                                $(this).closest("[id^='approvalContainer']").before($('#approvalContainer_' + stepId));
                                $('#approvalContainer_' + stepId).showEle('normal');
                            }
                        });
                    } else if (action == 'down') {

                        var numberOfVisibleSteps = $("[id^='approvalContainer']:visible").length;
                        // Last step: Can't order down
                        if (currentStepOrder == numberOfVisibleSteps)
                            return;

                        $("[id^='approvalOrderInput']").each(function () {
                            if ($(this).val() == (parseInt(currentStepOrder) + 1)) {
                                $('#approvalContainer_' + stepId).hide();
                                $('#approvalContainer_' + stepId).find('#stepControlTable_' + stepId).hide();
                                $(this).closest("[id^='approvalContainer']").after($('#approvalContainer_' + stepId));
                                $('#approvalContainer_' + stepId).showEle('normal');
                            }
                        });
                    }

                    reorderApprovals();
                }

                // ***************** Display Functions ************************

                // Due by: Toggling timeframe (unrestricted/scheduled) causes related attributes to toggle
                function toggleTimeframe(index) {
                    if ($('#flipToggle_dueBy_' + index).is(':checked')) {
                        $('#timeFrameContainer_' + index).showEle('normal');
                    } else {
                        $('#timeFrameContainer_' + index).hide();
                    }
                }

                // Step Indicators: Update display of intra-stage indicators (down arrow)
                // Note: Conducts a reinit each time; DOM manipulation of steps causes to many
                //		 corner cases for managing a static set of indicators
                function updateNextStepIndicators() {
                    $('.nextStepIndicator').remove();
                    $("[id^='approvalContainer']:visible").each(function () {
                        var numberOfVisibleSteps = $("[id^='approvalContainer']:visible").length;
                        var currentIndex = parseId(this);
                        // Append indicators: Don't append indicator to last step
                        if ($('#approvalOrderInput_' + currentIndex).val() != numberOfVisibleSteps)
                            $(this).after('<div class="nextStepIndicator" style="margin: 8px 28px;"/>');
                    });
                }

                // Selected users: Based on users selected in binding, updates display
                function updateAssignedApproversDisplay(index) {
                    $("[id^='approverCheck_" + index + "_']").each(function () {
                        var currentUserId = $(this).val();
                        if ($(this).is(':checked'))
                            $('#assignedApprover_' + index + '_' + currentUserId).showEle('normal');
                        else {
                            $('#assignedApprover_' + index + '_' + currentUserId).hide();
                        }
                    });

                    $("[id^='translatorCheck_" + index + "_']").each(function () {
                        var currentUserId = $(this).val();
                        if ($(this).is(':checked'))
                            $('#assignedTranslator_' + index + '_' + currentUserId).showEle('normal');
                        else {
                            $('#assignedTranslator_' + index + '_' + currentUserId).hide();
                        }
                    });
                    updateInfoSystemsForStep(index);
                }

                function updateInfoSystemsForStep(index) {
                    if ($("#approvalContainer_" + index + " .assignedApproverContainer:visible").length == 0)
                        $('#noApproversInfoBlock_' + index).showEle('normal');
                    else
                        $('#noApproversInfoBlock_' + index).hide();

                    if ($("#approvalContainer_" + index + " .assignedTranslatorContainer:visible").length == 0)
                        $('#noTranslatorsInfoBlock_' + index).showEle('normal');
                    else
                        $('#noTranslatorsInfoBlock_' + index).hide();
                }

                function updateInfoSystemsForPage() {
                    if ($("[id^='approvalContainer']:visible").length == 0)
                        $('#noStepsInfo').showEle('normal');
                    else
                        $('#noStepsInfo').hide();
                }


                // ***************** Init Functions ***************************

                // Init Flip Toggles: Convert checkboxes to flip toggle widget
                // [Note: Must be initialized on visible area or display width will be corrupt]
                function initFlipToggles() {
                    $("[id^='flipToggle']:visible").each(function () {
                        var currentBinding = $(this);
                        $(this).iButton({
                            labelOn: $(currentBinding).attr('title').split(';')[1],
                            labelOff: $(currentBinding).attr('title').split(';')[0],
                            resizeHandle: false,
                            resizeContainer: "auto",
                            change: function () {
                                if ($(currentBinding).attr('id').indexOf('dueBy') != -1) {
                                    var index = parseId(currentBinding).split('_')[1];
                                    toggleTimeframe(index);
                                }
                            }
                        });
                    });
                }

                function initAssignedApproversBehavior() {
                    $('.assignedApproverContainer').each(function () {
                        $(this).click(function () {
                            var stepIndex = parseId(this).split('_')[0];
                            var approverId = parseId(this).split('_')[1];
                            $("[id^='approverCheck_" + stepIndex + "_'][value='" + approverId + "']").removeAttr('checked');
                            updateAssignedApproversDisplay(stepIndex);
                        });
                    });

                    $('.assignedTranslatorContainer').each(function () {
                        $(this).click(function () {
                            var stepIndex = parseId(this).split('_')[0];
                            var approverId = parseId(this).split('_')[1];
                            $("[id^='translatorCheck_" + stepIndex + "_'][value='" + approverId + "']").removeAttr('checked');
                            updateAssignedApproversDisplay(stepIndex);
                        });
                    });
                }

                function clearApproversTable() {
                    $('table#approverSelectTable tbody tr').each(function () {
                        $(this).removeClass(rowSelected);
                    });
                }

                function filterApprovers() {
                    var searchVal = $('#approverSearchInput').val();

                    $("[id^='assignableApprover_']").each(function () {
                        if (searchVal == defaultSearchText || $(this).text().toLowerCase().indexOf(searchVal.toLowerCase()) != -1)
                            $(this).closest('tr').show();
                        else
                            $(this).closest('tr').hide();
                    });

                    if ($("[id^='assignableApprover_']:visible").length > 0)
                        $('#noApproversMessageContainer').hide();
                    else
                        $('#noApproversMessageContainer').show();

                }

                function toggleStepType(index) {
                    var stepTypeId = $('#stepTypeSelect_' + index).val();
                    if ( stepTypeId == 1 ) {   // Approval
                        $('#translationDetailsRow_' + index).hide();
                        $('#subworkflowsDetailsRow_' + index).hide();
                        $('#translationLangSelectRow_' + index).hide();
                        $('#translationApprovalLangSelectRow_' + index).hide();
                        $('#approversDetailsRow_' + index).showEle('normal');
                        $('#autoApproveDetailsRow_' + index).showEle('normal');
                        $('#timeFramesRow_' + index).showEle('normal');
                        $('#translatorSelectRow_' + index).hide();
                        $('#serviceSelectRow_' + index).hide();
                        $('#styleTransformProfileSelectRow_' + index).hide();
                    } else if ( stepTypeId == 2 ) {    // Translation
                        $('#translationDetailsRow_' + index).showEle('normal');
                        $('#subworkflowsDetailsRow_' + index).hide();
                        $('#translationLangSelectRow_' + index).showEle('normal');
                        $('#approversDetailsRow_' + index).hide();
                        $('#timeFramesRow_' + index).hide();
                        $('#translatorSelectRow_' + index).showEle('normal');
                        $('#autoApproveDetailsRow_' + index).hide();
                        $('#translationApprovalLangSelectRow_' + index).hide();
                        toggleTranslatorType(index);

                        // "Allow edit default language" flip toggle needs to be initialized if not initialized
                        if(!$('#flipToggle_allowEditDefault_' + index).parent().hasClass("ibutton-container")){
                            $('#flipToggle_allowEditDefault_' + index).each( function() {
                                var currentBinding = $(this);
                                $(this).iButton({
                                    labelOn: $(currentBinding).attr('title').split(';')[1],
                                    labelOff: $(currentBinding).attr('title').split(';')[0],
                                    resizeHandle: false,
                                    resizeContainer: "auto"
                                });
                            });
                        }
                    } else if ( stepTypeId == 3 ){    // Sub-workflow
                        $('#translationLangSelectRow_' + index).hide();
                        $('#translationDetailsRow_' + index).hide();
                        $('#approversDetailsRow_' + index).hide();
                        $('#subworkflowsDetailsRow_' + index).showEle('normal');
                        $('#translatorSelectRow_' + index).hide();
                        $('#serviceSelectRow_' + index).hide();
                        $('#styleTransformProfileSelectRow_' + index).hide();
                        $('#autoApproveDetailsRow_' + index).hide();
                        $('#timeFramesRow_' + index).hide();
                        $('#translationApprovalLangSelectRow_' + index).hide();
                    } else if ( stepTypeId == 4 ) { // Translation Approval
                        $('#translationDetailsRow_' + index).hide();
                        $('#translationLangSelectRow_' + index).hide();
                        $('#subworkflowsDetailsRow_' + index).hide();
                        $('#translationApprovalLangSelectRow_' + index).showEle('normal');
                        $('#approversDetailsRow_' + index).showEle('normal');
                        $('#autoApproveDetailsRow_' + index).showEle('normal');
                        $('#timeFramesRow_' + index).showEle('normal');
                        $('#translatorSelectRow_' + index).hide();
                        $('#serviceSelectRow_' + index).hide();
                        $('#styleTransformProfileSelectRow_' + index).hide();
                    }
                }

                function toggleTranslatorType(index) {
                    var translatorTypeId = $('#translatorTypeSelect_' + index).val();
                    if ( translatorTypeId == 1 ) {   // User
                        $('#serviceSelectRow_' + index).hide();
                        $('#styleTransformProfileSelectRow_' + index).hide();
                        $('#translationDetailsRow_' + index).showEle('normal');
                    } else {    // Service
                        $('#serviceSelectRow_' + index).showEle('normal');
                        $('#styleTransformProfileSelectRow_' + index).showEle('normal');
                        $('#translationDetailsRow_' + index).hide();
                    }
                }

                // #######################################################
                // ############### START INIT ############################
                $(function () {

                    if ($('.availableApproversContainer').length == 0)
                        return;

                    // rearrangeApprovalsByOrder should take care of hiding or showing boxes
                    rearrangeApprovalsByOrder();

                    initFlipToggles();
                    updateNextStepIndicators();

                    $("#ownerSelect").styleActionElement({maxMenuItemLength: 20, labelAlign: true});
                    $("[id^='stepTypeSelect_']").styleActionElement({maxMenuItemLength: 20, labelAlign: true});
                    $("input:button").styleActionElement();
                    $(".style_multiselect").styleActionElement();
                    $("#usageTypeSelect").styleActionElement();
                    $("[id^='translatorTypeSelect_']").styleActionElement();
                    $("[id^='serviceSelect_']").styleActionElement();
                    $("[id^='styleTransformProfileSelect_']").styleActionElement();
                    $("[id^='frequencyTypeSelect_']").styleActionElement();

                    $("[id^='approvalContainer']").each(function () {
                        var index = parseId(this);
                        updateAssignedApproversDisplay(index);
                        toggleTimeframe(index);
                        toggleStepType(index);
                        loadTimeSelect(index);
                    });

                    initDraggable();
                    initDroppable();

                    initAssignedApproversBehavior();

                    updateInfoSystemsForPage();

                    // Init order up buttons
                    $("[id^='upButton']").each(function () {
                        $(this).click(function () {
                            orderAction('up', parseId(this));
                        });
                    });

                    // Init order down buttons
                    $("[id^='downButton']").each(function () {
                        $(this).click(function () {
                            orderAction('down', parseId(this));
                        });
                    });

                    $("[id^='deleteButton']").each(function () {
                        $(this).click(function () {
                            removeAction(parseId(this));
                        });
                    });

                    $("[id^='approvalContainer']").each(function () {
                        var index = parseId(this);
                        $(this).find('table:first').mouseover(function () {
                            $('#stepControlTable_' + index).show();
                        });
                        $(this).find('table:first').mouseout(function () {
                            $('#stepControlTable_' + index).hide();
                        });
                    });

                    if ($("[id^='approvalContainer']").length == $("[id^='approvalContainer']:visible").length) {
                        $('#addButton').hide();
                    }

                    // Approvers list scroll management
                    var defaultApproversContainerTop = $('.availableApproversContainer').position().top;
                    $(window).scroll(function () {
                        var doc = document.documentElement, body = document.body;
                        var pageTop = (doc && doc.scrollTop || body && body.scrollTop || 0);
                        var addButtonTop = $('#addButton').position().top;
                        if (!$('#addButton').is(':visible')) {
                            $('#addButton').show();
                            addButtonTop = $('#addButton').position().top;
                            $('#addButton').hide();
                        }
                        if (( ($('.availableApproversContainer').position().top < pageTop) ||
                                ($('.availableApproversContainer').position().top > pageTop && (pageTop - defaultApproversContainerTop) > 0) ) &&
                            $('.availableApproversContainer').position().top + $('.availableApproversContainer').height() <= addButtonTop)
                            $('.availableApproversContainer').css('top', (pageTop - defaultApproversContainerTop) + 'px');
                        else if ($('.availableApproversContainer').position().top + $('.availableApproversContainer').height() > addButtonTop &&
                            (addButtonTop - $('.availableApproversContainer').height() - defaultApproversContainerTop) > 0)
                            $('.availableApproversContainer').css('top', (addButtonTop - $('.availableApproversContainer').height() - defaultApproversContainerTop) + 'px');
                        else if ((pageTop - defaultApproversContainerTop) < 0)
                            $('.availableApproversContainer').css('top', '0px');

                    });

                    if ($("[id^='assignableApprover_']").length > 10) {
                        $('#approverSelectTable .listTableHeaderTR')
                            .before("<tr><td colspan=\"3\">" +
                                "<div style=\"display: inline-block; position: relative;\">" +
                                "<input id=\"approverSearchInput\" class=\"input searchInput\" style=\"width: 270px; padding-left: 40px;\" value=\"" + client_messages.text.search_for_approvers + "\" />" +
                                "<i class=\"searchIconDiv fa fa-med fa-search fa-mp-style\" style=\"top: 12px;\"></i>" +
                                "</div>" +
                                "</td></tr>");
                        $('#approverSelectTable tbody').after("<tr id=\"noApproversMessageContainer\"><td colspan=\"3\">" +
                            client_messages.text.no_matching_approvers +
                            "</td></tr>");

                        defaultSearchText = $('#approverSearchInput').val();
                        $('#approverSearchInput')
                            .focus(function () {
                                if ($(this).val() == defaultSearchText)
                                    $(this).val('');
                            })
                            .focusout(function () {
                                if ($(this).val() == '')
                                    $(this).val(defaultSearchText);
                            })
                            .keyup(function (e) {
                                filterApprovers();
                            })
                            .bind('paste', function () {
                                filterApprovers();
                            });

                        filterApprovers();
                    }

                });
                // ################ END INIT #############################
                // #######################################################

            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <c:set var="userHasSharedContentViewPermission" value="false"/>
    <c:set var="userHasEmbeddedContentViewPermission" value="false"/>
    <c:set var="userHasContentLibraryViewPermission" value="false"/>
    <c:set var="userHasCommunicationsViewPermission" value="false"/>
    <c:set var="userHasLookupTableViewPermission" value="false"/>
    <c:set var="userHasProjectViewPermission" value="false"/>
    <c:set var="userHasWorkflowSetupPermission" value="false"/>

    <msgpt:IfAuthGranted authority="ROLE_MESSAGE_VIEW_ALL">
        <c:set var="userHasSharedContentViewPermission" value="true"/>
    </msgpt:IfAuthGranted>
    <msgpt:IfAuthGranted authority="ROLE_EMBEDDED_CONTENT_VIEW">
        <c:set var="userHasEmbeddedContentViewPermission" value="true"/>
    </msgpt:IfAuthGranted>
    <msgpt:IfAuthGranted authority="ROLE_CONTENT_LIBRARY_VIEW">
        <c:set var="userHasContentLibraryViewPermission" value="true"/>
    </msgpt:IfAuthGranted>
    <msgpt:IfAuthGranted authority="ROLE_LICENCED_COMMUNICATIONS_VIEW">
        <c:set var="userHasCommunicationsViewPermission" value="true"/>
    </msgpt:IfAuthGranted>
    <msgpt:IfAuthGranted authority="ROLE_LICENCED_LOOKUP_TABLE_VIEW">
        <c:set var="userHasLookupTableViewPermission" value="true"/>
    </msgpt:IfAuthGranted>
    <msgpt:IfAuthGranted authority="ROLE_PROJECT_VIEW">
        <c:set var="userHasProjectViewPermission" value="true"/>
    </msgpt:IfAuthGranted>
    <msgpt:IfAuthGranted authority="ROLE_WORKFLOW_ADMIN_EDIT">
        <c:set var="userHasWorkflowSetupPermission" value="true"/>
    </msgpt:IfAuthGranted>

    <msgpt:BodyNew theme="minimal">
        <c:choose>
            <c:when test="${isSharedContentWorkflow || isEmbeddedContentWorkflow || isContentLibraryWorkflow}">
                <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_CONTENTS %>"/>
            </c:when>
            <c:when test="${isProjectTaskWorkflow}">
                <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_TASKS %>"/>
            </c:when>
            <c:when test="${isWorkflowLibraryWorkflow}">
                <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_ADMIN %>"/>
            </c:when>
            <c:otherwise>
                <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
            </c:otherwise>
        </c:choose>
        <c:choose>
            <c:when test="${isSharedContentWorkflow || isEmbeddedContentWorkflow || isContentLibraryWorkflow}">
                <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_CONTENTS %>"/>
            </c:when>
            <c:when test="${isProjectTaskWorkflow}">
                <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_TASKS %>"/>
            </c:when>
            <c:when test="${isWorkflowLibraryWorkflow}">
                <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_ADMIN %>"/>
            </c:when>
            <c:otherwise>
                <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
            </c:otherwise>
        </c:choose>
        <c:if test="${not isEmbeddedContentWorkflow && not isContentLibraryWorkflow && not isLookupTableWorkflow && not isProjectTaskWorkflow && not isWorkflowLibraryWorkflow}">
            <msgpt:ContextBarNew globalContextApplied="false" languageContextApplied="false"
                                 touchpointSetupContext="${isMessageWorkflow || isCommunicationsWorkflow}"/>
        </c:if>
        <msgpt:LowerContainer fullPanel="true">
            <msgpt:ContentPanel>

                <c:if test="${not trashTpContext }">
                    <c:set var="pageTitle" value="page.label.update.message.workflow"/>
                    <c:if test="${isSharedContentWorkflow}">
                        <c:set var="pageTitle" value="page.label.update.shared.content.workflow"/>
                    </c:if>
                    <c:if test="${isEmbeddedContentWorkflow}">
                        <c:set var="pageTitle" value="page.label.update.embedded.content.workflow"/>
                    </c:if>
                    <c:if test="${isContentLibraryWorkflow}">
                        <c:set var="pageTitle" value="page.label.update.content.library.workflow"/>
                    </c:if>
                    <c:if test="${isCommunicationsWorkflow}">
                        <c:set var="pageTitle" value="page.label.update.communications.workflow"/>
                    </c:if>
                    <c:if test="${isLookupTableWorkflow}">
                        <c:set var="pageTitle" value="page.label.update.lookup.table.workflow"/>
                    </c:if>
                    <c:if test="${isProjectTaskWorkflow}">
                        <c:set var="pageTitle" value="page.label.update.project.workflow"/>
                    </c:if>
                    <c:if test="${isWorkflowLibraryWorkflow}">
                        <c:set var="pageTitle" value="page.label.update.workflow"/>
                    </c:if>

                    <%--@elvariable id="command" type=""--%>
                    <form:form modelAttribute="command" enctype="multipart/form-data">

                        <form:errors path="*">
                            <msgpt:Information errorMsgs="${messages}" type="error"/>
                        </form:errors>

                        <c:if test="${not empty param.saveSuccess && param.saveSuccess == 'true'}">
                            <msgpt:Information type="success">
                                <fmtSpring:message code="page.label.save.complete"/>
                            </msgpt:Information>
                        </c:if>

                        <msgpt:ContentData title="${pageTitle}">
                            <c:choose>
                                <c:when test="${isCommunicationsWorkflow && document.smsTouchpoint}">

                                    <div id="infoMsg_documentNotVariableMsg" class="InfoSysContainer_info"
                                         style="margin-top: 20px; margin-bottom: 400px;">
                                        <fmtSpring:message
                                                code="page.text.touchpoint.does.not.apply.for.communications.workflow"/>
                                    </div>

                                </c:when>
                                <c:otherwise>

                                    <msgpt:DataTable labelPosition="top" multiColumn="true">

                                        <!-- NAME -->
                                        <c:if test="${isProjectTaskWorkflow or isWorkflowLibraryWorkflow}">
                                            <msgpt:TableItem label="&nbsp;">
                                            </msgpt:TableItem>
                                            <msgpt:TableItem label="page.label.name">
                                                <msgpt:InputFilter type="simpleName">
                                                    <form:input path="workflow.name" htmlEscape="true"
                                                                cssClass="inputXXXL"/>
                                                </msgpt:InputFilter>
                                            </msgpt:TableItem>
                                        </c:if>

                                        <c:if test="${isWorkflowLibraryWorkflow}">
                                            <msgpt:TableItem label="&nbsp;">
                                            </msgpt:TableItem>
                                            <!-- Usage -->
                                            <msgpt:TableItem label="page.label.usage">
                                                <c:out value="${command.usageTypeStr}"></c:out>
                                            </msgpt:TableItem>

                                            <msgpt:TableItem label="&nbsp;">
                                            </msgpt:TableItem>
                                            
                                            <c:if test="${not isRationalizerWorkflow and not isAllTypeWorkflow}">
	                                            <!-- Touchpoints -->
	                                            <msgpt:TableItem label="page.label.touchpoints">
                                                    <select id="touchpointSelect"
                                                            name="documents"
                                                            class="complex-dropdown-select"
                                                            title="${msgpt:getMessage('page.label.touchpoint.s')}"
                                                            aria-label="${msgpt:getMessage('page.label.touchpoints')}"
                                                            data-toggle="complex-dropdown"
                                                            data-enablefilter="true"
                                                            data-enable-selectall="true"
                                                            data-enable-tag-cloud="true"
                                                            data-enable-view-selected="true"
                                                            data-menu-class="dropdown-menu-left"
                                                            data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                                            data-show-titles="true"
                                                            multiple>
                                                        <c:forEach var="currentDocument" items="${availableTouchpoints}">
                                                            <fmtSpring:bind path="command.documents">
                                                                <option id="touchpointOption_${currentDocument.id}"
                                                                        class="ml-0 level_${currentDocument.projectDepth}" value="${currentDocument.id}"
                                                                        data-filtervalue="${currentDocument.metatags}"
                                                                    ${msgpt:contains( command.documents, currentDocument) ? 'selected="selected"': ''}>
                                                                        ${currentDocument.name}
                                                                </option>
                                                            </fmtSpring:bind>
                                                        </c:forEach>
                                                    </select>
	                                            </msgpt:TableItem>
                                            </c:if>
                                            <c:if test="${isRationalizerWorkflow or isAllTypeWorkflow}">
                                            		<msgpt:TableItem label="&nbsp;">
                                            		</msgpt:TableItem>
                                            </c:if>
                                        </c:if>

                                        <msgpt:TableItem label="&nbsp;">

                                            <div class="availableApproversContainer"
                                                 style="top: -25px; padding-right: 8px; max-width: 300px;">

                                                <!-- Owner -->
                                                <msgpt:DataTable labelPosition="top" style="margin-bottom: 0px;">
                                                    <msgpt:TableItem label="page.label.owner">
                                                        <form:select path="owner" id="ownerSelect"
                                                                     cssClass="style_select inputL"
                                                                     cssStyle="display: none;">
                                                            <form:option value="0"
                                                                         label="${msgpt:getMessage('page.label.no.owner')}"/>
                                                            <form:options items="${availUsers}" itemLabel="fullName"/>
                                                        </form:select>
                                                    </msgpt:TableItem>
                                                </msgpt:DataTable>

                                                <!-- Approvers List -->
                                                <msgpt:DataTable id="approverSelectTable"
                                                                 listHeader="page.label.approvers" multiSelect="true"
                                                                 dragAndDrop="true">
                                                    <c:forEach var="user" items="${availUsers}" varStatus="status">
                                                        <msgpt:TableListGroup>
                                                            <msgpt:TableElement label="" align="center"
                                                                                style="vertical-align: middle; max-width: 25px; width: 25px; padding: 0px;">
                                                                <c:if test="${user.emailNotifyRealTime}">
                                                                    <div class="emailIcon"/>
                                                                </c:if>
                                                            </msgpt:TableElement>
                                                            <msgpt:TableElement label="page.label.name" sortable="true">
                                                                <div id="assignableApprover_${user.id}"
                                                                     style="padding: 3px 0px; white-space: nowrap;">
                                                                    <msgpt:TxtFmt maxLength="23">
                                                                        <c:out value="${user.fullName}"/>
                                                                    </msgpt:TxtFmt>
                                                                </div>
                                                            </msgpt:TableElement>
                                                        </msgpt:TableListGroup>
                                                    </c:forEach>
                                                </msgpt:DataTable>

                                            </div>

                                        </msgpt:TableItem>
                                        <msgpt:TableItem label="&nbsp;">

                                            <div style="position:relative; top: -15px; min-width: 610px;">

                                                <div id="noStepsInfo" class="InfoSysContainer_info"
                                                     style="display:none;">
                                                    <div><fmtSpring:message
                                                            code="page.text.workflow.has.no.steps"/></div>
                                                    <div style="padding-top: 6px;"><i><fmtSpring:message
                                                            code="page.text.click.button.to.add.steps"/></i></div>
                                                </div>

                                                <c:forEach var="currentApproval" items="${command.approvals}"
                                                           varStatus="stepStat">
                                                    <div id="approvalContainer_${stepStat.index}"
                                                         style="${command.approvals[stepStat.index].enabled ? '' : 'display: none;'}">

                                                        <table width="100%" cellspacing="0" cellpadding="0" border="0"
                                                               class="tableOutline">

                                                            <!-- Row: Step Order/Name -->
                                                            <tr>
                                                                <!-- Order -->
                                                                <td id="stepName_${stepStat.index}" width="40px"
                                                                    class="tableIndicatorCol cellTopLeft">
                                                                    <div class="stepIndicator">
																<span id="approvalOrderDisplay_${stepStat.index}">
																	<c:out value="${command.approvals[stepStat.index].order}"/>
																</span>
                                                                        <form:hidden
                                                                                id="approvalOrderInput_${stepStat.index}"
                                                                                path="approvals[${stepStat.index}].order"
                                                                                cssClass="input1digit"/>
                                                                    </div>
                                                                </td>
                                                                <!-- Name -->
                                                                <td colspan="2" class="tableContentCol cellTopRight">
                                                                    <msgpt:InputFilter type="simpleName">
                                                                        <form:input
                                                                                path="approvals[${stepStat.index}].stepName"
                                                                                id="approvalName_${stepStat.index}"
                                                                                cssClass="inputXXXL workflowInput"/>
                                                                    </msgpt:InputFilter>
                                                                    <span style="display:none;">
																<form:checkbox
                                                                        id="approvalStepEnabled_${stepStat.index}"
                                                                        path="approvals[${stepStat.index}].enabled"
                                                                        label="${msgpt:getMessage('page.label.enabled')}"
                                                                        cssStyle="width: 10px;"/><br/>
															</span>
                                                                </td>
                                                            </tr>

                                                            <!-- Row: Step Actions/Approvers -->
                                                            <tr>
                                                                <!-- Actions -->
                                                                <td rowspan="3" class="tableIndicatorCol cellBottomLeft"
                                                                    style="border-bottom: none;">

                                                                    <table id="stepControlTable_${stepStat.index}"
                                                                           width="100%" cellspacing="0" cellpadding="0"
                                                                           border="0" class="innerCellTable"
                                                                           style="display:none;">
                                                                        <tr>
                                                                            <td align="center">
                                                                                <div id="upButton_${stepStat.index}"
                                                                                     class="orderUpIcon"/>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td align="center">
                                                                                <div id="downButton_${stepStat.index}"
                                                                                     class="orderDownIcon"/>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td align="center">
                                                                                <div id="deleteButton_${stepStat.index}"
                                                                                     class="removeIcon"/>
                                                                            </td>
                                                                        </tr>
                                                                    </table>

                                                                </td>
                                                                <!-- Details -->
                                                                <td colspan="2" id="approverList_${stepStat.index}"
                                                                    class="tableContentCol">

                                                                    <table cellspacing="0" cellpadding="0" border="0"
                                                                           class="innerCellTable">
                                                                        <tr style="${(!isMessageWorkflow and !isEmbeddedContentWorkflow and !isContentLibraryWorkflow and !isVariantWorkflow and !isAllTypeWorkflow) ? 'display: none;' : ''}">
                                                                            <td class="workflowText"
                                                                                style="padding-top: 5px; padding-right: 4px; vertical-align: top;">
                                                                                <fmtSpring:message
                                                                                        code="page.label.step.type"/></td>
                                                                            <td style="vertical-align: top;">
                                                                                <form:select id="stepTypeSelect_${stepStat.index}" itemLabel="name" itemValue="id"
                                                                                             cssClass="style_select inputL" cssStyle="display: none;"
                                                                                             path="approvals[${stepStat.index}].stepType" items="${stepTypes}"
                                                                                             onchange="toggleStepType(${stepStat.index});"/>
                                                                            </td>
                                                                        </tr>
                                                                        <tr id="translatorSelectRow_${stepStat.index}">
                                                                            <td class="workflowText"
                                                                                style="padding-top: 10px; padding-right: 4px; vertical-align: top;">
                                                                                <fmtSpring:message
                                                                                        code="page.label.translator.type"/></td>
                                                                            <td style="padding-top: 5px; vertical-align: top;">
                                                                                <form:select id="translatorTypeSelect_${stepStat.index}" itemLabel="name" itemValue="id"
                                                                                             cssClass="style_select inputL" cssStyle="display: none;"
                                                                                             path="approvals[${stepStat.index}].translatorType" items="${translatorTypes}"
                                                                                             onchange="toggleTranslatorType(${stepStat.index});"/>
                                                                            </td>
                                                                        </tr>
                                                                        <tr id="serviceSelectRow_${stepStat.index}">
                                                                            <td class="workflowText"
                                                                                style="padding-top: 10px; padding-right: 4px; vertical-align: top;">
                                                                                <fmtSpring:message
                                                                                        code="page.label.service"/></td>
                                                                            <td style="padding-top: 5px; vertical-align: top;">
                                                                                <form:select id="serviceSelect_${stepStat.index}"
                                                                                             cssClass="style_select inputL" cssStyle="display: none;"
                                                                                             path="approvals[${stepStat.index}].translationServiceGuid">
                                                                                    <form:option id="0" value="0"><fmtSpring:message code="page.label.no.selection"/></form:option>
                                                                                    <form:options items="${translationServices}" itemValue="guid" itemLabel="name"/>
                                                                                </form:select>
                                                                            </td>
                                                                        </tr>
                                                                        <tr id="styleTransformProfileSelectRow_${stepStat.index}">
                                                                            <td class="workflowText"
                                                                                style="padding-top: 10px; padding-right: 4px; vertical-align: top;">
                                                                                <fmtSpring:message
                                                                                        code="page.label.style.transformation"/></td>
                                                                            <td style="padding-top: 5px; vertical-align: top;">
                                                                                <form:select id="styleTransformProfileSelect_${stepStat.index}" itemLabel="name" itemValue="id"
                                                                                             cssClass="style_select inputL" cssStyle="display: none;"
                                                                                             path="approvals[${stepStat.index}].styleTransformProfile">
                                                                                    <form:option value="0"><fmtSpring:message code="page.label.none"/></form:option>
                                                                                    <form:options items="${styleTransformProfiles}" itemValue="id" itemLabel="name"/>
                                                                                </form:select>
                                                                            </td>
                                                                        </tr>
                                                                        <tr id="translationDetailsRow_${stepStat.index}">
                                                                            <td class="workflowText"
                                                                                style="padding-top: 10px; vertical-align: top;">
                                                                                <fmtSpring:message
                                                                                        code="page.label.translator"/>
                                                                            </td>
                                                                            <td style="padding-top: 5px; vertical-align: top;">
                                                                                <div id="noTranslatorsInfoBlock_${stepStat.index}"
                                                                                     class="InfoSysContainer_info"
                                                                                     style="display:none;">
                                                                                    <fmtSpring:message
                                                                                            code="page.text.workflow.step.must.contain.at.least.one.approver"/>
                                                                                </div>
                                                                                <c:forEach items="${availUsers}" var="currentUser">
                                                                                    <div id="assignedTranslator_${stepStat.index}_${currentUser.id}"
                                                                                         class="assignedTranslatorContainer"
                                                                                         style="display: none;">
                                                                                        <msgpt:TxtFmt maxLength="30">
                                                                                            <c:out value="${currentUser.fullName}"/>
                                                                                        </msgpt:TxtFmt>
                                                                                    </div>
                                                                                </c:forEach>
                                                                            </td>
                                                                        </tr>
                                                                        <tr id="translationLangSelectRow_${stepStat.index}">
                                                                            <td class="workflowText"
                                                                                style="padding-top: 10px; vertical-align: top;">
                                                                                <fmtSpring:message
                                                                                        code="page.label.languages"/>
                                                                            </td>
                                                                            <td style="padding: 5px; vertical-align: top;">
                                                                                <select id="languageSelect_${stepStat.index}"
                                                                                        name="approvals[${stepStat.index}].languages"
                                                                                        class="complex-dropdown-select"
                                                                                        title="${msgpt:getMessage('page.label.language.s')}"
                                                                                        aria-label="${msgpt:getMessage('page.label.languages')}"
                                                                                        data-toggle="complex-dropdown"
                                                                                        data-enablefilter="true"
                                                                                        data-enable-selectall="true"
                                                                                        data-enable-view-selected="true"
                                                                                        data-min-children-to-display-filter="1"
                                                                                        data-menu-class="dropdown-menu-left"
                                                                                        data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                                                                        data-show-titles="true"
                                                                                        multiple>
                                                                                    <c:forEach var="currentLanguage" items="${availableLanguages}">
                                                                                        <fmtSpring:bind path="approvals[${stepStat.index}].languages">
                                                                                            <option id="langOption_${stepStat.index}_${currentLanguage.id}"
                                                                                                    class="ml-0" value="${currentLanguage.id}"
                                                                                                ${msgpt:contains( command.approvals[stepStat.index].languages, currentLanguage) ? 'selected="selected"': ''}>
                                                                                                    ${currentLanguage.name}
                                                                                            </option>
                                                                                        </fmtSpring:bind>
                                                                                    </c:forEach>
                                                                                </select>
                                                                            </td>
                                                                            <td class="workflowText"
                                                                                style="padding-top: 10px; padding-left: 20px; vertical-align: top;">
                                                                                <fmtSpring:message
                                                                                        code="page.label.allow.edit.default.language"/>
                                                                            </td>
                                                                            <td style="padding: 5px; vertical-align: top;">
                                                                                <form:checkbox
                                                                                        id="flipToggle_allowEditDefault_${stepStat.index}"
                                                                                        path="approvals[${stepStat.index}].allowEditDefaultLanguage"
                                                                                        title="${msgpt:getMessage('page.label.no')};${msgpt:getMessage('page.label.yes')}"
                                                                                        value="0"/>
                                                                                <!-- 0 no; 1 yes -->
                                                                            </td>
                                                                        </tr>
                                                                        <tr id="subworkflowsDetailsRow_${stepStat.index}">
                                                                            <td class="workflowText"
                                                                                style="padding-top: 10px; vertical-align: top;">
                                                                                <fmtSpring:message
                                                                                        code="page.label.workflow"/>
                                                                            </td>
                                                                            <td style="padding-top: 10px; vertical-align: top;">
                                                                                <select id="subworkflowSelect_${stepStat.index}"
                                                                                        name="approvals[${stepStat.index}].subworkflows"
                                                                                        class="complex-dropdown-select"
                                                                                        title="${msgpt:getMessage('page.label.workflow.s')}"
                                                                                        aria-label="${msgpt:getMessage('page.label.workflows')}"
                                                                                        data-toggle="complex-dropdown"
                                                                                        data-enablefilter="true"
                                                                                        data-enable-selectall="true"
                                                                                        data-enable-view-selected="true"
                                                                                        data-min-children-to-display-filter="1"
                                                                                        data-menu-class="dropdown-menu-left"
                                                                                        data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                                                                        data-show-titles="true"
                                                                                        multiple>
                                                                                    <c:forEach var="currentWorkflow" items="${availWorkflows}">
                                                                                        <fmtSpring:bind path="approvals[${stepStat.index}].subworkflows">
                                                                                            <option id="workflowOption_${stepStat.index}_${currentWorkflow.id}"
                                                                                                    class="ml-0" value="${currentWorkflow.id}"
                                                                                                ${msgpt:contains( command.approvals[stepStat.index].subworkflows, currentWorkflow) ? 'selected="selected"': ''}>
                                                                                                    ${currentWorkflow.name}
                                                                                            </option>
                                                                                        </fmtSpring:bind>
                                                                                    </c:forEach>
                                                                                </select>
                                                                            </td>
                                                                        </tr>
                                                                        <tr id="translationApprovalLangSelectRow_${stepStat.index}">
                                                                            <td class="workflowText"
                                                                                style="padding-top: 10px; vertical-align: top;">
                                                                                <fmtSpring:message
                                                                                        code="page.label.languages"/>
                                                                            </td>
                                                                            <td style="padding: 5px; vertical-align: top;" colspan="3">
                                                                                <select id="languageSelect_${stepStat.index}"
                                                                                        name="approvals[${stepStat.index}].translationApprovalLanguages"
                                                                                        class="complex-dropdown-select"
                                                                                        title="${msgpt:getMessage('page.label.language.s')}"
                                                                                        aria-label="${msgpt:getMessage('page.label.languages')}"
                                                                                        data-toggle="complex-dropdown"
                                                                                        data-enablefilter="true"
                                                                                        data-enable-selectall="true"
                                                                                        data-enable-view-selected="true"
                                                                                        data-min-children-to-display-filter="1"
                                                                                        data-menu-class="dropdown-menu-left"
                                                                                        data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                                                                        data-show-titles="true"
                                                                                        multiple>
                                                                                    <c:forEach var="currentLanguage" items="${availableLanguages}">
                                                                                        <fmtSpring:bind path="approvals[${stepStat.index}].translationApprovalLanguages">
                                                                                            <option id="langOption_${stepStat.index}_${currentLanguage.id}"
                                                                                                    class="ml-0" value="${currentLanguage.id}"
                                                                                                ${msgpt:contains( command.approvals[stepStat.index].translationApprovalLanguages, currentLanguage) ? 'selected="selected"': ''}>
                                                                                                    ${currentLanguage.name}
                                                                                            </option>
                                                                                        </fmtSpring:bind>
                                                                                    </c:forEach>
                                                                                </select>
                                                                            </td>
                                                                        </tr>
                                                                        <tr id="approversDetailsRow_${stepStat.index}">
                                                                            <td class="workflowText"
                                                                                style="padding-top: 10px; padding-right: 4px; vertical-align: top;">
                                                                                <fmtSpring:message
                                                                                        code="page.text.approval.required.from"/></td>
                                                                            <td style="padding-top: 5px; vertical-align: top;">
                                                                                <form:checkbox
                                                                                        id="flipToggle_approvalType_${stepStat.index}"
                                                                                        path="approvals[${stepStat.index}].approveType"
                                                                                        title="${msgpt:getMessage('page.text.one.of')};${msgpt:getMessage('page.text.all.of')}"
                                                                                        value="1"/>
                                                                                <!-- 0 any; 1 all -->
                                                                            </td>
                                                                            <td class="workflowText"
                                                                                style="padding-top: 10px; padding-left: 4px; vertical-align: top;">
                                                                                <fmtSpring:message
                                                                                        code="page.text.the.following"/>:
                                                                            </td>
                                                                            <td style="padding: 5px;">
                                                                                <div id="noApproversInfoBlock_${stepStat.index}"
                                                                                     class="InfoSysContainer_info"
                                                                                     style="display:none;">
                                                                                    <fmtSpring:message
                                                                                            code="page.text.workflow.step.must.contain.at.least.one.approver"/>
                                                                                </div>
                                                                                <c:forEach items="${availUsers}"
                                                                                           var="currentUser">
                                                                                    <div id="assignedApprover_${stepStat.index}_${currentUser.id}"
                                                                                         class="assignedApproverContainer"
                                                                                         style="display: none;">
                                                                                        <msgpt:TxtFmt maxLength="30">
                                                                                            <c:out value="${currentUser.fullName}"/>
                                                                                        </msgpt:TxtFmt>
                                                                                    </div>
                                                                                </c:forEach>
                                                                            </td>
                                                                        </tr>
                                                                    </table>

                                                                    <div id="approverCheckContainer_${stepStat.index}"
                                                                         class="checkboxArea" style="display: none;">
                                                                        <form:checkboxes
                                                                                id="approverCheck_${stepStat.index}_"
                                                                                items="${availUsers}"
                                                                                path="approvals[${stepStat.index}].users"
                                                                                itemLabel="name" delimiter="<br />"/>
                                                                    </div>
                                                                    <div id="translatorCheckContainer_${stepStat.index}"
                                                                         class="checkboxArea" style="display: none;">
                                                                        <form:checkboxes
                                                                                id="translatorCheck_${stepStat.index}_"
                                                                                items="${availUsers}"
                                                                                path="approvals[${stepStat.index}].translators"
                                                                                itemLabel="name" delimiter="<br />"/>
                                                                    </div>
                                                                </td>
                                                            </tr>

                                                            <!-- Row: Step Actions/Timeframe -->
                                                            <tr id="autoApproveDetailsRow_${stepStat.index}">
                                                                <td id="dueByContainer_${stepStat.index}"
                                                                    class="tableContentCol"
                                                                    style="border-bottom: none; vertical-align: middle;">

                                                                    <table cellspacing="0" cellpadding="0" border="0"
                                                                           class="innerCellTable">
                                                                        <tr>
                                                                            <td class="workflowText"
                                                                                style="padding-right: 4px; white-space: nowrap;">
                                                                                <span id="approvalTimeframeText_${stepStat.index}">
                                                                                    <fmtSpring:message code="page.text.approval.timeframe.is"/>
                                                                                </span>
                                                                            </td>
                                                                            <td>
                                                                                <form:checkbox
                                                                                        id="flipToggle_dueBy_${stepStat.index}"
                                                                                        path="approvals[${stepStat.index}].dueByChecked"
                                                                                        title="${msgpt:getMessage('page.label.unrestricted')};${msgpt:getMessage('page.label.scheduled')}"/>
                                                                                <!-- false unrestricted; true scheduled -->
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <c:if test="${isCommunicationsWorkflow}">
                                                                <tr>
                                                                    <td id="dueByContainer_${stepStat.index}"
                                                                        class="tableContentCol"
                                                                        style="border-bottom: none; vertical-align: middle;">
                                                                        <table cellspacing="0" cellpadding="0" border="0"
                                                                               class="innerCellTable">
                                                                            <td class="workflowText"
                                                                                style="padding-right: 4px; white-space: nowrap;">
                                                                                        <span class="workflowText">
                                                                                            <fmtSpring:message code="page.text.is.order.editable"/>
                                                                                        </span>
                                                                            <td>
                                                                                <form:checkbox
                                                                                        id="flipToggle_isEditable_${stepStat.index}"
                                                                                        path="approvals[${stepStat.index}].isOrderEditable"
                                                                                        title="${msgpt:getMessage('page.label.no')};${msgpt:getMessage('page.label.yes')}"/>
                                                                            </td>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </c:if>
                                                            <tr id="timeFramesRow_${stepStat.index}">
                                                                <td id="timeFrameContainer_${stepStat.index}"
                                                                    class="tableContentCol" colspan="2"
                                                                    style="border-bottom: none; vertical-align: middle;">

                                                                    <table cellspacing="0" cellpadding="0" border="0"
                                                                           class="innerCellTable">
                                                                        <tr>
                                                                            <td class="workflowText pt-2 pr-3" style="vertical-align: top;">
                                                                                <fmtSpring:message code="page.label.start.date"/>
                                                                            </td>
                                                                            <td id="startDateContainer_${stepStat.index}">
                                                                                <msgpt:Calendar id="startDateCal_${stepStat.index}" onchange="setTimeDateBindingValue(${stepStat.index})" viewableDateFormat="${viewableDateFormat}" />
                                                                                <div class="pt-2">
                                                                                    <select id="time_hours_${stepStat.index}" class="selectTime" onchange="timeChange(this,${stepStat.index})"></select>&nbsp<b>:</b>
                                                                                    <select id="time_minutes_${stepStat.index}" class="selectTime" onchange="timeChange(this,${stepStat.index})"></select>
                                                                                    <select id="time_AM_PM_${stepStat.index}" class="selectTime" onchange="timeChange(this,${stepStat.index})"></select>
                                                                                </div>
                                                                                <form:hidden id="hiddenDateTimeField_${stepStat.index}" path="approvals[${stepStat.index}].startDate" />
                                                                            </td>
                                                                        </tr>
                                                                        <tr id="timeFrameEndDateRow_${stepStat.index}">
                                                                            <td class="workflowText pt-2 pr-3" style="vertical-align: top;">
                                                                                <fmtSpring:message code="page.label.end.date"/>
                                                                            </td>
                                                                            <td id="endDateContainer_${stepStat.index}" class="pt-2">
                                                                                <msgpt:Calendar id="endDateCal_${stepStat.index}" path="approvals[${stepStat.index}].endDate" viewableDateFormat="${viewableDateFormat}" />
                                                                            </td>
                                                                        </tr>
                                                                        <tr id="timeFrameFrequencyRow_${stepStat.index}">
                                                                            <td class="workflowText pt-2 pr-3" style="vertical-align: top;">
                                                                                <fmtSpring:message code="page.label.frequency"/>
                                                                            </td>
                                                                            <td class="pt-2">
                                                                                <form:select id="frequencyTypeSelect_${stepStat.index}" itemLabel="displayName" itemValue="id"
                                                                                         cssClass="style_select inputL" cssStyle="display: none;"
                                                                                         path="approvals[${stepStat.index}].frequencyType" items="${frequencyTypes}"/>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>


                                                    </div>


                                                </c:forEach>

                                                <div align="left" style="padding: 10px;">
                                                    <table class="btn" id="addButton" onclick="addAction();"
                                                           cellspacing="0" cellpadding="0" border="0"
                                                           class="innerCellTable" style="line-height: 38px">
                                                        <tr>
                                                            <td>
                                                                <div class="addStepButtonIcon"/>
                                                            </td>
                                                            <td><fmtSpring:message code="page.label.add.step"/></td>
                                                        </tr>
                                                    </table>


                                                </div>

                                            </div>

                                        </msgpt:TableItem>
                                    </msgpt:DataTable>

                                    <!-- Buttons -->
                                    <div class="pddng-lv2 pddng-horizontal-lv3 align-content-center">
                                        <div class="inline-block-item mrgn-right-lv2">
                                            <c:choose>
                                                <c:when test="${isEmbeddedContentWorkflow && userHasEmbeddedContentViewPermission}">
                                                    <msgpt:Button label="page.label.cancel"
                                                                  URL="global_content_list.form" flowControl="true"/>
                                                </c:when>
                                                <c:when test="${isContentLibraryWorkflow && userHasContentLibraryViewPermission}">
                                                    <msgpt:Button label="page.label.cancel"
                                                                  URL="global_content_list.form?localContentType=2" flowControl="true"/>
                                                </c:when>
                                                <c:when test="${isCommunicationsWorkflow && userHasCommunicationsViewPermission}">
                                                    <msgpt:Button label="page.label.cancel"
                                                                  URL="${contextPath}/touchpoints/touchpoint_communications_list.form"
                                                                  flowControl="true"/>
                                                </c:when>
                                                <c:when test="${isLookupTableWorkflow && userHasLookupTableViewPermission}">
                                                    <msgpt:Button label="page.label.cancel"
                                                                  URL="${contextPath}/dataadmin/lookup_table_list.form"/>
                                                </c:when>
                                                <c:when test="${isProjectTaskWorkflow && userHasProjectViewPermission}">
                                                    <msgpt:Button label="page.label.cancel"
                                                                  URL="${contextPath}/projects/project_task_workflow_list.form"/>
                                                </c:when>
                                                <c:when test="${isWorkflowLibraryWorkflow && userHasWorkflowSetupPermission}">
                                                    <msgpt:Button label="page.label.cancel"
                                                                  URL="${contextPath}/workflow/workflow_library.form"/>
                                                </c:when>
                                                <c:otherwise>
                                                    <c:choose>
                                                        <c:when test="${empty param.documentId}">
                                                            <msgpt:Button label="page.label.cancel"
                                                                          URL="${contextPath}${returnFromSetupPagePath}"
                                                                          flowControl="true"/>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <msgpt:Button label="page.label.cancel"
                                                                          URL="${contextPath}${returnFromSetupPagePath}${fn:contains(returnFromSetupPagePath,'?') ? '&' : '?'}docid=${param.documentId}"
                                                                          flowControl="true"/>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                        <div class="inline-block-item mrgn-right-lv2">
                                            <msgpt:Button label="page.label.save" primary="true"
                                                          URL="javascript:doSubmit()" flowControl="true"
                                                          icon="fa-save"/>
                                        </div>

                                        <c:if test="${not empty param.workflowId}">
                                            <div class="inline-block-item">
                                                <c:if test="${!isCurrentlyUsedByAssets}">
                                                    <msgpt:Button label="page.label.save.and.cascade" primary="true" URL="javascript:doSubmit()"
                                                        flowControl="true" icon="fa-save"/>
                                                </c:if>
                                                <c:if test="${isCurrentlyUsedByAssets}">
                                                    <msgpt:Button label="page.label.save.and.cascade" primary="true" URL="javascript:actionSelected('2');"
                                                        icon="fa-save"/>
                                                </c:if>
                                                <span class="d-inline-block ml-2" data-toggle="tooltip"
                                                      title="${msgpt:getMessage('page.text.workflow.override.save.info')}">
                                                    <i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
                                                </span>
                                            </div>
                                        </c:if>
                                    </div>

                                    <!-- POPUP DATA -->
                                    <div id="actionSpecs" style="display: none;">
                                    <!-- ACTIONS POPUP DATA -->
                                        <div id="actionSpec_2" type="simpleConfirm" submitId="2"> <!-- Save and Cascade -->
                                            <div id="actionTitle_2"><fmtSpring:message code="page.label.confirm.save.and.cascade"/></div>
                                            <div id="actionInfo_2"><p><b><fmtSpring:message code="page.text.save.and.cascade.workflow.changes"/></b>
                                            </p></div>
                                        </div>
                                    </div>

                                    <!-- POPUP INTERFACE -->
                                    <msgpt:Popup id="actionPopup" theme="minimal">
                                        <div id="actionPopupInfoFrame">
                                            <div id="actionPopupInfo">&nbsp;</div>
                                        </div>
                                        <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                                            <span id="cancelBtnEnabled">
                                                <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                            </span>
                                                <span id="cancelBtnDisabled" style="display: none;">
                                                <msgpt:Button URL="#" label="page.label.cancel" disabled="true"/>
                                            </span>
                                                <span id="continueBtnEnabled">
                                                <msgpt:Button URL="#" primary="true" label="page.label.continue"/>
                                            </span>
                                                <span id="continueBtnDisabled" style="display: none;">
                                                <msgpt:Button URL="#" label="page.label.continue" disabled="true"/>
                                            </span>
                                        </div>

                                    </msgpt:Popup>
                                </c:otherwise>
                            </c:choose>
                        </msgpt:ContentData>
                    </form:form>
                </c:if>

                <c:if test="${trashTpContext}">
                    <div class="pddng-vertical-lv3 pddng-bottom-lv0">
                        <div class="InfoSysContainer_info">
                            <i class="fa icon fa-info-circle" aria-hidden="true"></i>
                            <p>
                                <fmtSpring:message code="page.text.trash.touchpoint.does.not.apply"/>
                            </p>
                        </div>
                    </div>
                </c:if>

            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
    </msgpt:BodyNew>
</msgpt:Html5>