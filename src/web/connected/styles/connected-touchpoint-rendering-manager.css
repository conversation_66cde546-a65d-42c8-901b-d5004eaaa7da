[data-zone-id] p, [data-zone-id] div.mceListItemContent {
    letter-spacing: 0.007em;
}

[contenteditable] {
    outline: 0px solid transparent;
}

.mceDraggable {
    position: absolute;
}

#main-container {
    font-family: Arial;
    font-size: 10.42pt;
    justify-content: center;
    margin-top: 15px;
    display: flex;
}

#toolbar {
    height: 0px;
}
.toolbar-shadow-box {
    box-shadow: 0px 1px 8px 2px rgb(0 0 0 / 10%);
    clip-path: inset(0px -1000px -10000px -1000px);
    border-top: 1px solid #eee;
}

#items-panel {
    border-bottom: 2px solid #ddd;
    height: 95vh;
    overflow-y: auto;
}

#document-name {
    padding-bottom: 6px;
}

p {
    margin-block-start: 0px;
    margin-block-end: 0px;
    line-height: 1;
}

.vertical-layout {
    display: flex;
    flex-direction: column;
}

.horizontal-layout {
    display: flex;
}

.connected-zone, .connected-zone.mce-edit-focus {
    outline: 2px dotted #cfcfcf;
}

.connected-zone.connected-zone-editing {
    outline-color: #8fdd4e;
}

.interactive-page-break {
    border-top: 2px dotted #8fdd4e;
    box-sizing: border-box;
    margin-bottom: 2px;
}

.debug-border {
    outline: 1px solid lightblue;
}

#document-area {
    width: fit-content;
}

[data-mce-bogus="all"].mce-offscreen-selection {
    display:none;
}

/* Did not set yet display:none for 'mce-drag-container' class, as it is too risky. */
/*[data-mce-bogus="all"].mce-drag-container {*/
/*    display:none;*/
/*}*/

.transparent-page {
    background-color: transparent;
    box-sizing: border-box;
    margin: 0px 20px 40px;
    border: 0px;
}

.page {
    background-color: white;
    box-shadow: 0 1px 5px 1px rgb(0 0 0 / 10%), 1px 0 5px 1px rgb(0 0 0 / 10%);
    box-sizing: border-box;
    margin: 0px 20px 40px;
}

.continuous-page {
    background-color: white;
    box-sizing: border-box;
    margin: 0px 20px 40px;
    border: 1px dashed gray;
}

.template-page {
    margin: 0 20px;
    background: white;
    box-shadow: 0 1px 5px 1px rgb(0 0 0 / 10%), 1px 0 5px 1px rgb(0 0 0 / 10%);
}

.loading-page {
    width: 850px;
    height: 1100px;
}

/* action toolbar */
#toolbar-container{
    display: flex;
    justify-content: center;
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #F7F7F7;
    box-shadow: 0px 1px 8px 2px rgb(0 0 0 / 10%);
}

#action-toolbar {
    min-width: 880px;
    width: 100%;
}

.back-interview-action {
    color: gray;
    text-decoration: none;
}

#action-toolbar > div:first-child {
    justify-content: space-between;
    align-items: flex-end;
    box-sizing: border-box;
    padding: 0 12px 10px;
    height: 60px;
}

#submit-action.disabled {
    opacity: 0.4;
    box-shadow: none;
    cursor: not-allowed;
    pointer-events: none;
}

#action-toolbar > div:first-child > * {
    height: auto;
}

#document-actions {
    box-sizing: border-box;
    align-items: center;
    padding: 0 18px;
    background-color: white;
    height: 60px;
}

#document-actions > *, #editor-toolbar > * {
    margin-right: 10px;
    font-size: 14px;
}

#document-actions > *:last-child, #editor-toolbar > *:last-child {
    margin-right: 0;
}

#editor-toolbar {
    background-color: white;
    border-top: 1px solid rgba(0,0,0, 0.1);
    padding: 10px;
}

#marcie-panel{
    border-top: 1px solid rgba(0,0,0, 0.1);
}

div#editor-toolbar {
    height: auto;
}

#containerLower {
    padding: 0;
}

/*end action toolbar*/

/* loading overlay*/

#loading-overlay > i {
    font-size: 3rem;
    --fa-primary-color: #5F1467;
    position: fixed;
    top: 50vh;
    left: 50%;
    transform: translate(-50%, -50%);
}

#loading-overlay, #error-overlay {
    position: absolute;
    background: rgba(0, 0, 0, 0.2);
    height: 100%;
    width: 100%;
    z-index: 500;
}

#error-container {
    position: fixed;
    top: 50vh;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 500px;
    background-color: #fff;
}

/* end loading overlay*/

/*left right panel*/
.right {
    display: flex;
    position: relative;
    z-index: 1;
}

.left {
    position: relative;
    z-index: 1;
    background-color: #ECECEC;
    box-shadow: 5px 0 5px -5px rgba(0, 0, 0, 0.19), 0px 5px 8px -4px rgba(0, 0, 0, 0.2) inset;
    border-right: 1px solid #d0d1d2;
    min-width: 55px;
    max-width: 290px;
}

.right {
    justify-content: flex-end;
}

.middle {
    margin: 14px 30px 0 30px;
    flex-grow: 1;
}

/* end left right panel*/

.center-area {
    width: 100%;
}

.rendering-manager-content {
    flex-grow: 1;
    position: relative;
    /*overflow:hidden;*/
}

.rendering-manager-zone {
    overflow: hidden;
}

#containerLower .contentPanelInnerContainer.extendedPanel {
    width: auto;
    display: flex;
    height: auto;
    position: relative;
}

#contextBar {
    position: relative;
    z-index: 2;
}

#touchpoint-selection {
    width: 100%;
}

#touchpoint-selection-dropdown {
    width: 98%;
}

.wrap-content {
    flex-wrap: wrap;
    word-break: break-word;
}


/* debug panel*/
.tab-panel {
    display: flex;
    flex-direction: column;
}

.tab-content {
    height: 100%;
    overflow: auto;
}

.log-line {
    display: flex;
    margin-top: 5px;
    margin-left: 10px;
}

.error-line {
    font-weight: bold;
}

.log-message {
    margin-left: 10px;
}

.log-message-type-error {
    color: red;
}

.json-logger-valid {
    margin-left: 60px !important;
}

.json-logger-invalid {

}

#json-viewer {
    min-height: 600px;
    margin-top: 0;
    margin-left: 60px;
}

.json-formatter-container {
    margin-left: 15px;
    width: 600px
}

.debugger {
    width: 500px;
    height: 600px;
    background-color: white;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
    position: absolute;
}

/* Style the tab */
#tab-links {
    overflow: hidden;
    border: 1px solid #ccc;
    background-color: #f1f1f1;
    padding: 10px;
}

/* Style the buttons inside the tab */
#tab-links button {
    background-color: inherit;
    float: left;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 5px;
    transition: 0.3s;
    font-size: 17px;
}

/* Change background color of buttons on hover */
#tab-links button:hover {
    background-color: #ddd;
}

/* Create an active/current tablink class */
#tab-links button.active {
    background-color: #ccc;
}

#main-container .staticContentItem {
    vertical-align: inherit;
}

.staticContentItem {
    color: inherit;
}

.variableValueHighlight {
    background-color: #ffff01 !important;
}

.variableValueHighlightSingle {
    background-color: #d0d0d0 !important;
}

.carousel-container {
}

.carousel-button {
    display: inline-block;
    vertical-align: middle;
    font-size: 24px;
    width: 38px;
    height: 38px;
    border-radius: 6px;
    margin: 6px 6px;
    text-align: center;
    z-index: 2;
    position: relative;
}

.carousel-button {
    color: #7c1a87;
    opacity: 0.7;
}

.carousel-button:hover {
    color: #7c1a87;
    opacity: 0.85;
}

.carousel-button:active {
    color: #7c1a87;
    opacity: 1;
}

.carousel-button:focus {
    outline: none;
}

.carousel-button.disabled {
    color: #999;
    opacity: 0.7;
}

#right-container {
    background-color: white;
    /*margin-top: -48px;*/
    box-shadow: -2px 4px 4px 0 rgba(0, 0, 0, 0.1);
    /*border-left: 1px solid #efefef;*/
}

.container-fluid {
    padding: 0;
}

.upload-container {
    position: sticky;
    top: 20px;
    width: 500px;
    margin: 20px;
    /*border-style: dashed;*/
    /*border-color: rgba(180, 184, 198, 0.35);*/
    /*border: 2px dashed rgba(180, 184, 198, 0.35);*/
    background-image: linear-gradient(to right, rgba(180, 184, 198, 0.35) 50%, transparent 50%),
    linear-gradient(to right, rgba(180, 184, 198, 0.35) 50%, transparent 50%),
    linear-gradient(to bottom, rgba(180, 184, 198, 0.35) 50%, transparent 50%),
    linear-gradient(to bottom, rgba(180, 184, 198, 0.35) 50%, transparent 50%);
    background-position: left top, left bottom, left top, right top;
    background-repeat: repeat-x, repeat-x, repeat-y, repeat-y;
    background-size: 20px 3px, 20px 3px, 3px 20px, 3px 20px;
    line-height: 1.6;
    padding: 20px;
    border-radius: 8px;
}

.dnd-title {
    color: #909090;
    font-weight: 300;
    font-size: 25px;
    line-height: 2.6;
    text-align: center;
}

.dnd-subtitle {
    color: #a7a4a4;
    line-height: 1.5;
    font-size: 19px;
    font-weight: 300;
    text-align: center;
}

.browse-button {
    color: #7c1a87;
    font-weight: bold;
    height: 45px;
    background-color: white;
    font-size: 18px;
    border: 2px solid #7c1a87;
    border-radius: 5px;
    position: relative;
    left: 30%;
}

.browse-button.centered {
    position: relative;
    left: 32%;
}

.browse-container {
    margin: 0 0 0 0;
    position: relative;
    overflow: hidden;
    padding: 20px 0 15px 20px;
    width: 100%
}

.file-upload {
    font-size: 100px;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
}

#imageSelect {
    width: 100%;
}

#imageSelect select {
    border: none;
    color: black;
    background-color: transparent;
    position: absolute;
}

.imageUploadBtn {
    color: #666;
    margin-left: 22px;
}

.imageUploadBtn:hover {
    color: #444;
}

.imageUploadIcon.active {
    color: #7c1a87;
}

.imageUploadBtn.active {
    border-color: #7c1a87;
}

#imageUploadIcon {
    font-size: 16px;
}

/*edit actions container*/
#edit-actions-container {
    position: sticky;
    top: 0;
}

#edit-actions-container-header {
    display: flex;
    line-height: 50px;
    height: 50px;
    border-bottom: 1px solid #fff;
    background-color: #e9ebec;
    font-size: 13px;
    font-weight: 500;
    width: 100%;
    box-shadow: inherit;
    position: relative;
}

#edit-actions-container-state-icon {
    color: #979BA1;
    padding: 0 23px;
    font-size: 14px;
    font-weight: 100;
    align-self: center;
    right: 0px;
    cursor: pointer;
}

.edit-action-item-mandatory-icon {
    color: #ff0000;
    margin-left: -8px;
    margin-right: 3px;
    margin-top: -2px;
}

.edit-action-item {
    display: flex;
    align-items: center;
    padding-left: 20px;
    min-height: 35px;
    color: #8d8d8e;
    font-size: 12px;
    border-bottom: 1px solid #fff;
    box-sizing: border-box;
}
.edit-action-item-name {
    padding: 5px 8px 5px 8px;
}

.edit-action-item.selected:hover, .edit-action-item.selected {
    font-weight: 500;
    color: #fff;
    background-color: #686A6A;
    border-bottom: none;
}

.edit-action-item:hover {
    font-weight: 500;
    background-color: #D8D8D8;
    color: #808080;
}

.edit-action-item-name {
    width: 90%;
}

.edit-action-item-completion-icon {
    font-size: 13px;
    color: #fff;
}

#left-container {
    width: 300px;
    height: 100%;
    background-color: #ECECEC;
    box-shadow: 5px 0 5px -5px rgba(0, 0, 0, 0.19), 0px 5px 8px -4px rgba(0, 0, 0, 0.2) inset;
    border-right: 1px solid #d0d1d2;
}

.left-container-sticky {
    position: sticky;
    top: 0;
}

.left-container-header {
    line-height: 50px;
    height: 50px;
    border-bottom: 1px solid #fff;
    background-color: #e9ebec;
    font-size: 13px;
    font-weight: 500;
    width: 100%;
    box-shadow: inherit;
    /*text-shadow: 0.5px 0.5px lightgrey*/
}

.left-container-row {
    border-bottom: 1px solid #fff;
    padding-left: 20px;
    color: #8D8D8E;
    margin-left: 0;
    font-size: 12px;
    width: 100%;
    /*height: 40px;*/
    display: flex;
    overflow: hidden;
}

.left-container-row-child {
    align-self: center;
    line-height: 1.1;
    padding: 10px 0;
}

.fa-check-circle:before {
    content: "\f058";
    font-size: 13px;
    border-radius: 50%;
    background-color: #fff;
    color: #6ade2d;
}

.left-container-row:hover {
    font-weight: 500;
    background-color: #D8D8D8;
    color: #808080;
}

.left-container-row.active {
    font-weight: 500;
    color: #fff;
    background-color: #686A6A;
    border-bottom: none;
}

#left-arrow-left-container {
    color: #979BA1;
    padding: 20px 20px 20px 0;
    font-size: 14px;
    font-weight: 100;
    margin-right: 9px;
}

#right-arrow-left-container {
    color: #979BA1;
    padding: 20px;
    font-size: 14px;
    font-weight: 100;
    margin-right: 9px;
}

.tooltip-inner {
    background-color: #686A6A;
}

.tooltip.bottom .tooltip-arrow {
    background-color: #686A6A;
}

.tooltip.top .tooltip-arrow {
    background-color: #686A6A;
}

.collapsed-left-container {
    width: 68px !important;
}

/*end edit actions container*/

.imgToggleBtn {
    color: #7c1a87;
    opacity: 0.7;
    margin: 6px 12px;
    font-size: 24px;
}

.imgToggleBtn:hover {
    color: #7c1a87;
    opacity: 0.85;
}

.imgToggleBtn-left.disabled {
    color: #999;
    opacity: 0.7;
}

.imgToggleBtn-right.disabled {
    color: #999;
    opacity: 0.7;
}

.active-icon {
    color: #7c1a87 !important;
}

/*insert point*/
[data-edit-mode='inline_insert_point'] {
    text-decoration: none;
    background-color: #edeeee;
}
[data-edit-mode='inline_insert_point'].active-edit-point {
    background-color: #e1f2ff;
}

[data-edit-mode='inline_insert_point'] > * {
    background-color: inherit;
}

[data-edit-mode='inline_insert_point'] span {
    background-color: inherit;
}

[data-edit-mode='block_insert_point'] {
    background-color: #edeeee;
    text-decoration: none;
}
[data-edit-mode='block_insert_point'].active-edit-point {
    background-color: #e1f2ff;
}

[data-edit-mode='block_insert_point'] > * {
    background-color: inherit;
}

.block-insert-point-icon-container {
    display: flex;
    justify-content: center;
    position: relative;
    background: none;
}

.block-insert-point-icon-container > div {
    width: 100%;
    position: absolute;
    height: 1px;
    border-bottom: 1px dashed #c8c6c6;
    top: 50%;
}

.block-insert-point-icon-container > i {
    z-index: 1;
}

[data-edit-mode='connected_zone_edit_point'] {
    background-color: #edeeee;
}
[data-edit-mode='connected_zone_edit_point'].active-edit-point {
    background-color: #e1f2ff;
}

/*end insert point*/

/* tiny mce override*/
[data-edit-mode].mce-content-body {
    line-height: inherit;
}

/* end tiny mce override*/

.alert-danger {
    text-align: start;
}

/* Life events styling*/
.life-event-container {
    width: 500px;
    height: 1000px;
    overflow-y: auto;
}

.life-event-container-title {
    position: sticky;
    top: 0;
    text-align: left;
    padding: 0 20px 0 20px;
    font-size: 13px;
    font-weight: 500;
    box-shadow: inherit;
    text-shadow: 0.5px 0.5px #c8c6c6;
    border-bottom: 1px solid #c8c6c6;
    height: 50px;
    line-height: 50px;
    background-color: #e9ebec;
}

.life-events {
    text-align: left;
}

.life-event {
    padding: 15px 15px 15px 15px;
    line-height: 1.4;
    border-bottom: 1px solid #c8c6c6;
}

.life-event:hover {
    color: #fff;
    background-color: #868686;
}

.life-event:active {
    font-weight: 500;
    color: #fff;
    background-color: #868686;
}

.life-event.active {
    font-weight: 500;
    color: #fff;
    background-color: #868686;
}

.life-event-title {
    font-weight: 800;
}

.life-event-description {
    font-weight: 300;
}

/*drop down menu*/
var[type="15"],
#main-container .staticContentItem[type='15'] {
    background: inherit !important;
    background-color: inherit !important;
    position: relative;
    outline: 0 !important;
    text-decoration: underline blue;
    font-style: normal;
}

var[type="15"] > span:first-child {
    display: none;
}

var[type="15"] .far.fa-caret-down.fa-mp-ed-ico {
    color: red;
    margin-left: 2px;
}

.popupFactory_popupContainer.current_popup {
    z-index: 9;
}

/*end dropdown menu*/

/* custom round checkbox*/
.round-checkbox {
    position: relative;
    top: -4px;
    /*max-height: 15px;*/
    transform: translate(0, 40%);
}

.round-checkbox label {
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 50%;
    cursor: pointer;
    height: 15px;
    left: 0;
    position: absolute;
    top: 0;
    width: 15px;
}

.round-checkbox label.green:after {
    border: 2px solid #fff;
    border-top: none;
    border-right: none;
    content: "";
    height: 6px;
    left: 2px;
    opacity: 0;
    position: absolute;
    top: 2px;
    transform: rotate(-45deg);
    width: 9px;
}

.round-checkbox input[type="checkbox"] {
    visibility: hidden;
}

.round-checkbox input[type="checkbox"]:checked + label {
    background-color: #7c1a87;
    border-color: #fff;
}

.round-checkbox input[type="checkbox"]:checked + label.green {
    background-color: #5FAD00;
    border-color: #fff;
}

.round-checkbox input[type="checkbox"]:checked + label:after {
    opacity: 1;
}

.connected-zone-name.active {
    font-weight: 500;
    color: #fff;
    background-color: #868686;
}

#debug-screen-collapsed {
    left: 16%;
}

div#debugger {
    left: 80%;
    top: 15px;
    z-index: 501;
    min-width: 370px;
    position: absolute;
}

#tab-panel-collapsed {
    top: 3px;
    left: 470px;
    position: absolute;
}

.ui-draggable {
    top: 12px;
    left: 10%;
    text-align: start;
    position: absolute !important;
}

.active-debug-panel {
    background-color: #ccc !important;
}

.fixedOption {
    background-color: #ffffff;
}

#use-debug-files:checked ~ .custom-control-label::before {
    color: #fff;
    border-color: #7c1a87;
    background-color: #7c1a87;
    box-shadow: none;
}

/*tinymce toolbar*/
div.mce-floatpanel.rendering-manager-tinymce-toolbar {
    position: relative;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    display: block;
    border-width: 0px !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
}

#mce_readability_btn, #mce_sentiment_btn, #mce_brandcheck_btn {
    border-right: 0 !important;
}

#mce_readability_btn:hover, #mce_sentiment_btn:hover, #mce_brandcheck_btn:hover {
    background-color: rgb(99, 97, 97) !important;
    color: white;
}

div#editor-toolbar.rendering-manager-tinymce-toolbar-location {
    padding: 0;
    display: block !important;
}

/* proof button*/
.noProofAction {
    border-top-right-radius: 3px !important;
    border-bottom-right-radius: 3px !important;
}

#proof-polling-container-menu{
    z-index: 66000;
}

.spinning.dropdown-toggle::after {
    content: '\f110' !important;
    -webkit-animation: spinner-spin 1s steps(8) infinite;
    -moz-animation: spinner-spin 1s steps(8) infinite;
    -o-animation: spinner-spin 1s steps(8) infinite;
    animation: spinner-spin 1s steps(8) infinite;
}

.complete.dropdown-toggle::after {
    content: '\f00c' !important;
    color: #1fad29;
}

.error.dropdown-toggle::after {
    content: '\f00d' !important;
    color: #d91815;
}

/*style override*/
ul, ol, dl {
    margin: 0;
}

/*style override*/

/*hide anchors*/
a[type="anchor"] {
    display: none;
}

@-webkit-keyframes spinner-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@-moz-keyframes spinner-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@-o-keyframes spinner-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@-ms-keyframes spinner-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes spinner-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* BASIC LIST STYLES */
#main-container ul li:before {
    content: "\2022";
}
#main-container li ul > li:before {
    content: "\25E6";
}
#main-container li li ul > li:before {
    content: "\25AA";
}
#main-container ol > li:before {
    content: counter(level1, decimal) ".";
}

#main-container li  {
    position: relative;
}
#main-container li:before {
    display: inline-block;
    position: absolute;
}
#main-container li > div {
    position: relative;
}

#main-container li, #main-container li li, #main-container li li li,
#main-container li li li li, #main-container li li li li li,
#main-container li li li li li li, #main-container li li li li li li li,
#main-container li li li li li li li li, #main-container li li li li li li li li li,
#main-container li li li li li li li li li li {
    left: 0px;
}

#main-container ol > li:before {
    content: counter(level1, decimal) ".";
}
#main-container li:before,
#main-container [listclass] li:before {
    left: 25px;
}
#main-container li li:before,
#main-container [listclass] li li:before {
    left: 50px;
}
#main-container li li li:before,
#main-container [listclass] li li li:before {
    left: 75px;
}
#main-container li li li li:before,
#main-container [listclass] li li li li:before {
    left: 100px;
}
#main-container li li li li li:before,
#main-container [listclass] li li li li li:before {
    left: 125px;
}
#main-container li li li li li li:before,
#main-container [listclass] li li li li li li:before {
    left: 150px;
}
#main-container li li li li li li li:before,
#main-container [listclass] li li li li li li li:before {
    left: 175px;
}
#main-container li li li li li li li li:before,
#main-container [listclass] li li li li li li li li:before {
    left: 200px;
}
#main-container li li li li li li li li li:before,
#main-container [listclass] li li li li li li li li li:before {
    left: 225px;
}
#main-container li li li li li li li li li li:before,
#main-container [listclass] li li li li li li li li li li:before {
    left: 250px;
}

#main-container li > div.mceListItemContent,
#main-container [listclass] li > div.mceListItemContent
{
    left: 50px;
    width: calc(100% - 50px);
}
#main-container li li > div.mceListItemContent,
#main-container [listclass] li li > div.mceListItemContent
{
    left: 75px;
    width: calc(100% - 75px);
}
#main-container li li li > div.mceListItemContent,
#main-container [listclass] li li li > div.mceListItemContent
{
    left: 100px;
    width: calc(100% - 100px);
}
#main-container li li li li > div.mceListItemContent,
#main-container [listclass] li li li li > div.mceListItemContent
{
    left: 125px;
    width: calc(100% - 125px);
}
#main-container li li li li li > div.mceListItemContent,
#main-container [listclass] li li li li li > div.mceListItemContent
{
    left: 150px;
    width: calc(100% - 150px);
}
#main-container li li li li li li > div.mceListItemContent,
#main-container [listclass] li li li li li li > div.mceListItemContent
{
    left: 175px;
    width: calc(100% - 175px);
}
#main-container li li li li li li li > div.mceListItemContent,
#main-container [listclass] li li li li li li li > div.mceListItemContent
{
    left: 200px;
    width: calc(100% - 200px);
}
#main-container li li li li li li li li > div.mceListItemContent,
#main-container [listclass] li li li li li li li li > div.mceListItemContent
{
    left: 225px;
    width: calc(100% - 225px);
}
#main-container li li li li li li li li li > div.mceListItemContent,
#main-container [listclass] li li li li li li li li li > div.mceListItemContent
{
    left: 250px;
    width: calc(100% - 250px);
}
#main-container li li li li li li li li li li > div.mceListItemContent,
#main-container [listclass] li li li li li li li li li li > div.mceListItemContent
{
    left: 275px;
    width: calc(100% - 275px);
}


#main-container li {
    line-height: 1em;
}
#main-container ul, #main-container ol {
    list-style: none;
    padding: 0px;
    margin: 0px;
}

#main-container ul[data-list-style-type=circle] > li:before {
    content: "\2022" !important;
}
#main-container ul[data-list-style-type=disc] > li:before {
    content: "\25E6" !important;
}
#main-container ul[data-list-style-type=square] > li:before {
    content: "\25AA" !important;
}

#main-container ol                              { counter-reset: level1 }
#main-container li ol                           { counter-reset: level2 }
#main-container li li ol                        { counter-reset: level3 }
#main-container li li li ol                     { counter-reset: level4 }
#main-container li li li li ol                  { counter-reset: level5 }
#main-container li li li li li ol               { counter-reset: level6 }
#main-container li li li li li li ol            { counter-reset: level7 }
#main-container li li li li li li li ol         { counter-reset: level8 }
#main-container li li li li li li li li ol      { counter-reset: level9 }
#main-container li li li li li li li li li ol   { counter-reset: level10 }
#main-container ol > li::before                             { content: counter(level1, decimal) "."; counter-increment: level1; }
#main-container li ol > li::before                          { content: counter(level2, decimal) "."; counter-increment: level2; }
#main-container li li ol > li::before                       { content: counter(level3, decimal) "."; counter-increment: level3; }
#main-container li li li ol > li::before                    { content: counter(level4, decimal) "."; counter-increment: level4; }
#main-container li li li li ol > li::before                 { content: counter(level5, decimal) "."; counter-increment: level5; }
#main-container li li li li li ol > li::before              { content: counter(level6, decimal) "."; counter-increment: level6; }
#main-container li li li li li li ol > li::before           { content: counter(level7, decimal) "."; counter-increment: level7; }
#main-container li li li li li li li ol > li::before        { content: counter(level8, decimal) "."; counter-increment: level8; }
#main-container li li li li li li li li ol > li::before     { content: counter(level9, decimal) "."; counter-increment: level9; }
#main-container li li li li li li li li li ol > li::before  { content: counter(level10, decimal) "."; counter-increment: level10; }

#main-container ol[data-list-style-type=lower-alpha] > li::before                             { content: counter(level1, lower-alpha) "."; counter-increment: level1; }
#main-container li ol[data-list-style-type=lower-alpha] > li::before                          { content: counter(level2, lower-alpha) "."; counter-increment: level2; }
#main-container li li ol[data-list-style-type=lower-alpha] > li::before                       { content: counter(level3, lower-alpha) "."; counter-increment: level3; }
#main-container li li li ol[data-list-style-type=lower-alpha] > li::before                    { content: counter(level4, lower-alpha) "."; counter-increment: level4; }
#main-container li li li li ol[data-list-style-type=lower-alpha] > li::before                 { content: counter(level5, lower-alpha) "."; counter-increment: level5; }
#main-container li li li li li ol[data-list-style-type=lower-alpha] > li::before              { content: counter(level6, lower-alpha) "."; counter-increment: level6; }
#main-container li li li li li li ol[data-list-style-type=lower-alpha] > li::before           { content: counter(level7, lower-alpha) "."; counter-increment: level7; }
#main-container li li li li li li li ol[data-list-style-type=lower-alpha] > li::before        { content: counter(level8, lower-alpha) "."; counter-increment: level8; }
#main-container li li li li li li li li ol[data-list-style-type=lower-alpha] > li::before     { content: counter(level9, lower-alpha) "."; counter-increment: level9; }
#main-container li li li li li li li li li ol[data-list-style-type=lower-alpha] > li::before  { content: counter(level10, lower-alpha) "."; counter-increment: level10; }

#main-container ol[data-list-style-type=upper-alpha] > li::before                             { content: counter(level1, upper-alpha) "."; counter-increment: level1; }
#main-container li ol[data-list-style-type=upper-alpha] > li::before                          { content: counter(level2, upper-alpha) "."; counter-increment: level2; }
#main-container li li ol[data-list-style-type=upper-alpha] > li::before                       { content: counter(level3, upper-alpha) "."; counter-increment: level3; }
#main-container li li li ol[data-list-style-type=upper-alpha] > li::before                    { content: counter(level4, upper-alpha) "."; counter-increment: level4; }
#main-container li li li li ol[data-list-style-type=upper-alpha] > li::before                 { content: counter(level5, upper-alpha) "."; counter-increment: level5; }
#main-container li li li li li ol[data-list-style-type=upper-alpha] > li::before              { content: counter(level6, upper-alpha) "."; counter-increment: level6; }
#main-container li li li li li li ol[data-list-style-type=upper-alpha] > li::before           { content: counter(level7, upper-alpha) "."; counter-increment: level7; }
#main-container li li li li li li li ol[data-list-style-type=upper-alpha] > li::before        { content: counter(level8, upper-alpha) "."; counter-increment: level8; }
#main-container li li li li li li li li ol[data-list-style-type=upper-alpha] > li::before     { content: counter(level9, upper-alpha) "."; counter-increment: level9; }
#main-container li li li li li li li li li ol[data-list-style-type=upper-alpha] > li::before  { content: counter(level10, upper-alpha) "."; counter-increment: level10; }

#main-container ol[data-list-style-type=lower-greek] > li::before                             { content: counter(level1, lower-greek) "."; counter-increment: level1; }
#main-container li ol[data-list-style-type=lower-greek] > li::before                          { content: counter(level2, lower-greek) "."; counter-increment: level2; }
#main-container li li ol[data-list-style-type=lower-greek] > li::before                       { content: counter(level3, lower-greek) "."; counter-increment: level3; }
#main-container li li li ol[data-list-style-type=lower-greek] > li::before                    { content: counter(level4, lower-greek) "."; counter-increment: level4; }
#main-container li li li li ol[data-list-style-type=lower-greek] > li::before                 { content: counter(level5, lower-greek) "."; counter-increment: level5; }
#main-container li li li li li ol[data-list-style-type=lower-greek] > li::before              { content: counter(level6, lower-greek) "."; counter-increment: level6; }
#main-container li li li li li li ol[data-list-style-type=lower-greek] > li::before           { content: counter(level7, lower-greek) "."; counter-increment: level7; }
#main-container li li li li li li li ol[data-list-style-type=lower-greek] > li::before        { content: counter(level8, lower-greek) "."; counter-increment: level8; }
#main-container li li li li li li li li ol[data-list-style-type=lower-greek] > li::before     { content: counter(level9, lower-greek) "."; counter-increment: level9; }
#main-container li li li li li li li li li ol[data-list-style-type=lower-greek] > li::before  { content: counter(level10, lower-greek) "."; counter-increment: level10; }

#main-container ol[data-list-style-type=lower-roman] > li::before                             { content: counter(level1, lower-roman) "."; counter-increment: level1; }
#main-container li ol[data-list-style-type=lower-roman] > li::before                          { content: counter(level2, lower-roman) "."; counter-increment: level2; }
#main-container li li ol[data-list-style-type=lower-roman] > li::before                       { content: counter(level3, lower-roman) "."; counter-increment: level3; }
#main-container li li li ol[data-list-style-type=lower-roman] > li::before                    { content: counter(level4, lower-roman) "."; counter-increment: level4; }
#main-container li li li li ol[data-list-style-type=lower-roman] > li::before                 { content: counter(level5, lower-roman) "."; counter-increment: level5; }
#main-container li li li li li ol[data-list-style-type=lower-roman] > li::before              { content: counter(level6, lower-roman) "."; counter-increment: level6; }
#main-container li li li li li li ol[data-list-style-type=lower-roman] > li::before           { content: counter(level7, lower-roman) "."; counter-increment: level7; }
#main-container li li li li li li li ol[data-list-style-type=lower-roman] > li::before        { content: counter(level8, lower-roman) "."; counter-increment: level8; }
#main-container li li li li li li li li ol[data-list-style-type=lower-roman] > li::before     { content: counter(level9, lower-roman) "."; counter-increment: level9; }
#main-container li li li li li li li li li ol[data-list-style-type=lower-roman] > li::before  { content: counter(level10, lower-roman) "."; counter-increment: level10; }

#main-container ol[data-list-style-type=upper-roman] > li::before                             { content: counter(level1, upper-roman) "."; counter-increment: level1; }
#main-container li ol[data-list-style-type=upper-roman] > li::before                          { content: counter(level2, upper-roman) "."; counter-increment: level2; }
#main-container li li ol[data-list-style-type=upper-roman] > li::before                       { content: counter(level3, upper-roman) "."; counter-increment: level3; }
#main-container li li li ol[data-list-style-type=upper-roman] > li::before                    { content: counter(level4, upper-roman) "."; counter-increment: level4; }
#main-container li li li li ol[data-list-style-type=upper-roman] > li::before                 { content: counter(level5, upper-roman) "."; counter-increment: level5; }
#main-container li li li li li ol[data-list-style-type=upper-roman] > li::before              { content: counter(level6, upper-roman) "."; counter-increment: level6; }
#main-container li li li li li li ol[data-list-style-type=upper-roman] > li::before           { content: counter(level7, upper-roman) "."; counter-increment: level7; }
#main-container li li li li li li li ol[data-list-style-type=upper-roman] > li::before        { content: counter(level8, upper-roman) "."; counter-increment: level8; }
#main-container li li li li li li li li ol[data-list-style-type=upper-roman] > li::before     { content: counter(level9, upper-roman) "."; counter-increment: level9; }
#main-container li li li li li li li li li ol[data-list-style-type=upper-roman] > li::before  { content: counter(level10, upper-roman) "."; counter-increment: level10; }

/* BASIC TABLE STYLES */

#main-container td {
    vertical-align: top;
}

/* LIST PROPS */
#main-container .listPropertiesIcon {
    /*border-radius: 3px;*/
    opacity: 0.95;
    position: absolute;
    left: -24px;
    top: 0px;
    z-index: 2;
    /*border: 1px solid #ddd;*/
    width: 20px;
    height: 20px;
    color: #555;
    font-size: 14px;
}
/*#main-container .listPropertiesIcon:not(.activeEditorIcon):hover {*/
/*    background-color: #fafafa;*/
/*}*/

.mp-edit-variables {
    position: relative !important;
    border: 0px;
    box-shadow: none;
    max-height: none;
    min-width: 490px;
    padding-top: 0px;
    padding-bottom: 0px;
    height: 100vh;
    overflow-y: scroll;
    text-align: left !important;
}
.mp-variables-main-panel {
    padding: 20px;
    width: 500px;
    max-width: 500px;
    text-align: left;
}

.mp-variable-icons{
    font-size: 14px;
    margin: 2px;
    opacity: 0.8;
    color: #555;
    border-radius: 5px;
}

.mp-variable-name {
    overflow-wrap:anywhere;
    font-size:  12px;
}

.mp-variable-view {
    border-right: 5px;
    background-color: #EAEAEA;
    padding: 0px 5px;
    border-radius: 6px;
    display: inline-block;
    width: 430px;
}

.mp-variable-edit {
    border:1px solid black;
    padding: 2px 5px;
    background-color: #e1f2ff;
    display: inline-block;
    width: 425px;
}

.mp-variable-occurrence {
    color: #7c1a87;
    font-weight: bold;
    display: inline-block;
    padding-left: 15px;
}

.mp-variable-item, .mp-variable-item-single {
    padding-top: 20px;
    padding-bottom: 10px;
    border-top: 1px solid #C0C0C0;
}

.mp-variable-item:hover, .mp-variable-item-single:hover {
    background-color: #F6F6F6;
}

/*
 * Styles for the Tag Diff
 */
span.diff-tag-html {
    font-family: "Andale Mono" monospace;
    font-size: 80%;
}

span.diff-tag-removed {
    font-size: 100%;
    text-decoration: line-through;
    background-color: #fdc6c6; /* light red */
}

span.diff-tag-added {
    font-size: 100%;
    background-color: #ccffcc; /* light green */
}

span.diff-tag-conflict {
    font-size: 100%;
    background-color: #f781be; /* light rose */
}

/*
 * Styles for the HTML Diff
 */
span.diff-html-added {
    font-size: 100%;
    background-color: #ccffcc; /* light green */
    cursor: pointer;
}

span.diff-html-removed {
    font-size: 100%;
    text-decoration: line-through;
    background-color: #fdc6c6; /* light red */
    cursor: pointer;
}

span.diff-html-conflict {
    /*  background: url(../images/diffunderline.gif) bottom repeat-x; */
    background-color: #f781be; /* light rose */
}

span.diff-html-selected {
    background-color: #FF8800; /* light orange */
    cursor: pointer;
}

span.diff-html-selected img{
    border: 2px solid #FF8800; /* light orange */
}

span.diff-html-added img{
    border: 2px solid #ccffcc;
}

span.diff-html-removed img{
    border: 2px solid #fdc6c6;
}

span.diff-html-changed img{
    border: 2px dotted #000099;

}

div.diff-removed-image, div.diff-added-image, div.diff-conflict-image {
    height: 300px;
    width: 200px;
    position: absolute;
    opacity : 0.55;
    filter: alpha(opacity=55);
    -moz-opacity: 0.55;
}

div.diff-removed-image, div.diff-added-image, div.diff-conflict-image  {
    margin-top: 2px;
    margin-bottom: 2px;
    margin-right: 2px;
    margin-left: 2px;
}

div.diff-removed-image {
    background-color: #fdc6c6;
    background-image: url(../images/diffmin.gif);
}
div.diff-added-image {
    background-color: #ccffcc;
    background-image: url(../images/diffplus.gif);
    background-repeat: no-repeat;
}

div.diff-conflict-image {
    background-color: #f781be;
    background-repeat: no-repeat;
}

img.diff-icon {
    background-color: #FF8800;
    width: 16px;
    height: 16px;
    border: 0px none;
}

table.diff-tooltip-link, table.diff-tooltip-link-changed {
    width: 100%;
    text-align: center;
    Vertical-align: middle;
}

table.diff-tooltip-link-changed {
    border-top: thin dashed #000000;
    margin-top: 3px;
    padding-top: 3px
}
td.diff-tooltip-prev {
    text-align: left;
}

td.diff-tooltip-next {
    text-align: right;
}

table.diffpage-html-firstlast {
    width: 100%;
    Vertical-align: middle;
}

div.diff-topbar{
    border-bottom: 2px solid #FF8800;
    border-left: 1px solid #FF8800;
    border-right: 1px solid #FF8800;
    background-color: #FFF5F5;
}

a.diffpage-html-a, a.diffpage-html-a:hover, a.diffpage-html-a:link, a.diffpage-html-a:visited, a.diffpage-html-a:active {
    text-decoration: none;
    color: #FF8800;
}

.diffpage-html-firstlast a img, .dsydiff-prevnextnav a img {
    vertical-align: middle;
}

ul.changelist {
    padding-left: 15px;
}

body{
    margin-top: 0px;
}
