<%@ taglib prefix="input" uri="http://www.springframework.org/tags/form" %>
<%@ include file="../includes/includes.jsp" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<msgpt:Html5>

<msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">

	<style type="text/css">
		.header-content {
			margin: 20px;
			padding: 8px;
			text-align: right;
			flex-direction: row;
		}

		.nav-tabs li{
			width: 50% !important;
			font-weight: 400;
		}
	</style>

    <msgpt:Script>
		<script type="text/javascript" src="https://plugins.stripo.email/static/latest/stripo.js"></script>

		<script type="text/javascript">
			var inputHTML = "";
			var inputCSS = "";

			function loadStripo() {
				window.Stripo.init({
					settingsId: 'stripoSettingsContainer',
					previewId: 'stripoPreviewContainer',
					codeEditorButtonId: 'btnShowEditor',
					undoButtonId:'btnUndo',
					redoButtonId:'btnRedo',
					html: inputHTML,
					css: inputCSS,
					apiRequestData: {
						emailId: '8675309'
					},
					mergeTags: [
						{
							"category": "Messagepoint",
							"entries": [
								{
									"label": "First Name",
									"value": "*|FNAME|*"
								}
							]
						}
					],
					getAuthToken: function(callback) {
						var baseRestURL = "https://plugins.stripo.email/api/v1/auth";
						var pluginID = "ce08b1e6d823407d845498847a310570";
						var secretKey = "618119ec8ab04e2c8f02f7c187304be4";
						var role="ADMIN";
						var method = "POST";
						var postData = "{\"pluginId\": \"" + pluginID + "\",\"secretKey\": \"" + secretKey + "\",\"role\":\"" + role + "\"}";
						var async = true;
						var request = new XMLHttpRequest();
						request.onload = function() {
							var status = request.status; // HTTP response status, e.g., 200 for "200 OK"
							console.log("Authenticated to the Stripo Plugin - Click to Continue");
							var jsonResponse = JSON.parse(request.responseText);
							var token = jsonResponse.token;
							return callback(token);
						}
						request.open(method, baseRestURL, async);
						request.setRequestHeader("Content-Type", "application/json");
						request.setRequestHeader("Accept", "application/json");
						request.send(postData);
					},
					"extensions": [
						{
							"globalName": "MpZoneBlockExtension",
							"url": "${webRoot}" + "/includes/javascript/stripo/mp.stripoExtension.js"
						}
					]
				});
			}

			// Function to download data to a file
			function saveTemplate() {
				window.StripoApi.getTemplate( (html, css) => {
					$('#templateHtmlFormInput').val(html);
					populateZoneBindings();

					doSubmit('submit');
				});

			}

			function populateZoneBindings(){
				var iFrame = $('iframe.stripo-preview-frame').contents();
				$('#templateWidthInput').val( $(iFrame).width() );
				$('#templateHeightInput').val( $(iFrame).height() );

				var zoneDefinitions = $("[id^='zoneDefinitions_']").first();
				var defaultLang = $(zoneDefinitions).attr('id').replace(/zoneDefinitions_/g,'');
				var idx = 0;
				$(iFrame).find('.esd-mp-zone-block').each(function(){
					var relativeZonePos = getRelativeElePos(this,iFrame);
					var connectorName = $(this).find('img').attr('alt');

					var zoneDefinitionHtml = 	"<input name=\"templateZoneMap[" + defaultLang + "][" + idx + "].width\" type=\"text\" value=\"" + $(this).width() + "\">" +
                                                "<input name=\"templateZoneMap[" + defaultLang + "][" + idx + "].height\" type=\"text\" value=\"" + $(this).height() + "\">" +
                                                "<input name=\"templateZoneMap[" + defaultLang + "][" + idx + "].topX\" type=\"text\" value=\"" + relativeZonePos[0] + "\">" +
                                                "<input name=\"templateZoneMap[" + defaultLang + "][" + idx + "].topY\" type=\"text\" value=\"" + relativeZonePos[1] + "\">" +
                                                "<input name=\"templateZoneMap[" + defaultLang + "][" + idx + "].connectorName\" type=\"text\" value=\"" + connectorName + "\">" +
                                                "<input name=\"templateZoneMap[" + defaultLang + "][" + idx + "].zoneType\" type=\"text\" value=\"1\">";
					$(zoneDefinitions).append(zoneDefinitionHtml);
					idx++;
				});
			}

			var getRelativeElePos = function(ele, container) {
				var ele = $(ele).get(0);
				var posArray = new Array(0,0);
				while ( ele != $(container).get(0) && ele != null ) {
					posArray[1] += ele.offsetTop || 0;
					posArray[0] += ele.offsetLeft || 0;
					ele = ele.offsetParent || null;
				}
				return posArray;
			}

			$(function(){
				inputHTML = $('#templateHtmlFormInput').val();
				loadStripo();

				if ( getParam('nprSaveSuccess') == "true" )
					getTopFrame().location.reload();
			});
		</script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:BodyNew type="iframe">
	<form:form method="post" modelAttribute="command">
		<c:if test="${empty param.nprSaveSuccess}">
			<div id="toolbarDiv" class="border-bottom text-right">
				<button id="btnRedo" type="button"
						class="btn btn-dark m-2">
					<i class="far fa-redo mr-2" aria-hidden="true"></i>
					<fmtSpring:message code="page.label.redo"/>
				</button>
				<button id="btnUndo" type="button"
						class="btn btn-dark m-2">
					<i class="far fa-undo mr-2" aria-hidden="true"></i>
					<fmtSpring:message code="page.label.undo"/>
				</button>
				<button id="btnShowEditor" type="button"
						class="btn btn-dark m-2">
					<i class="far fa-code mr-2" aria-hidden="true"></i>
					<fmtSpring:message code="page.label.show.html.source"/>
				</button>
				<button id="btnSaveFile" type="button"
						class="btn btn-dark m-2"
						onclick="saveTemplate()">
					<i class="far fa-save mr-2" aria-hidden="true"></i>
					<fmtSpring:message code="page.label.save"/>
				</button>
			</div>
			<div id="editorDiv">
				<table width="100%">
					<tr>
						<td style="vertical-align:top; width:320px;">
							<div id="stripoSettingsContainer" style="width:320px;"></div>
						</td>
						<td style="vertical-align:top;">
							<form:errors path="*">
								<msgpt:Information errorMsgs="${messages}" type="error"/>
							</form:errors>
							<div id="stripoPreviewContainer"></div>
							<form:hidden id="templateHtmlFormInput" path="templateHtml"/>
						</td>
					</tr>
				</table>
			</div>
		</c:if>

		<!-- Bindings: Template properties -->
		<div id="templateBindingsContainer" style="display: none;">
			<div>
				[template width  ] <form:input path="templateWidth" id="templateWidthInput" /><br>
				[template height ] <form:input path="templateHeight" id="templateHeightInput" />
			</div>
			<c:forEach var="locale" items="${locales}">
				<br>Language Code: ${locale.languageCode} [Default: ${defaultLocale.languageCode == locale.languageCode}]
				<div id="zoneDefinitions_${locale.languageCode}" defaultLanguage="${defaultLocale.languageCode == locale.languageCode ? 'true' : 'false'}">
				</div>
			</c:forEach>
		</div>
	</form:form>
</msgpt:BodyNew>

</msgpt:Html5>
		