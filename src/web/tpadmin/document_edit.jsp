<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.model.SystemPropertyKeys.WebAppSecurity" %>
<%@page import="com.prinova.messagepoint.util.ApplicationUtil" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>

    <%@page import="com.prinova.messagepoint.model.admin.EventType" %>
    <%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>
    <msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">

        <msgpt:Script src="includes/javascript/documentWidget.js"/>

        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>

        <msgpt:Script src="includes/javascript/checkboxSelect.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/dataFilesPopup/jquery.dataFilesPopup.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

        <msgpt:Stylesheet href="includes/themes/commoncss/tinyMCEEditorContent.css"/>
        <msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js"/>

        <msgpt:Script>
            <script>
                $(function () {
                    onHasInsertManagementChanged();
                    onHasSelectionManagementChanged();

                    $('#documentMetatags').tagCloud({
                        tagCloudType: 4,
                        inputType: 'metatags'
                    });
                });

                function onHasInsertManagementChanged() {
                    if ($('#hasInsertCheckbox').is(':checked')) {
                        $("[id^='insertAttr_']").showEle('normal');
                    } else {
                        $("[id^='insertAttr_']").hide();
                    }
                }

                function onHasSelectionManagementChanged() {
                    if ($('#hasSelectionsCheckbox').is(':checked')) {
                        $("[id^='selectionsAttr_']").showEle('normal');
                    } else {
                        $("[id^='selectionsAttr_']").hide();
                    }

                    if ($("#variantDefaultVisibilityToggle:visible").length != 0 && $("#variantDefaultVisibilityToggle:visible").closest('.ibutton-container').length == 0)
                        $("#variantDefaultVisibilityToggle").iButton({
                            labelOn: $("#variantDefaultVisibilityToggle").attr('title').split(';')[0],
                            labelOff: $("#variantDefaultVisibilityToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {

                            }
                        });

                    if ($("#variantWorkflowToggle:visible").length != 0 && $("#variantWorkflowToggle:visible").closest('.ibutton-container').length == 0)
                        $("#variantWorkflowToggle").iButton({
                            labelOn: $("#variantWorkflowToggle").attr('title').split(';')[0],
                            labelOff: $("#variantWorkflowToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {

                            }
                        });
                }

                function onCommunicationPrimaryDriverToggle() {
                    if ($('#communicationPrimaryDriverCheckbox').is(':checked'))
                        $('#communicationPrimaryDriverWebServiceContainer').show();
                    else
                        $('#communicationPrimaryDriverWebServiceContainer').hide();
                }

                function onCommunicationCompositionResultsToggle() {
                    if ($('#communicationCompositionResultsCheckbox').is(':checked')) {
                        $('#communicationCompositionResultsWebServiceContainer').show();
                        var $communicationTransientZoneContent = $('#communicationTransientZoneContent');

                        if ($communicationTransientZoneContent.is(":visible") && $communicationTransientZoneContent.closest('.ibutton-container').length === 0) {

                            $communicationTransientZoneContent.iButton({
                                labelOn: $communicationTransientZoneContent.attr('title').split(';')[0],
                                labelOff: $communicationTransientZoneContent.attr('title').split(';')[1],
                                resizeHandle: false
                            });
                        }
                    } else {
                        $('#communicationCompositionResultsWebServiceContainer').hide();
                    }
                }

                function onCommunicationProductionStatusToggle() {
                    if ($('#communicationProductionStatusCheckbox').is(':checked'))
                        $('#communicationProductionStatusWebServiceContainer').show();
                    else
                        $('#communicationProductionStatusWebServiceContainer').hide();
                }

                function onCommunicationNotificationToggle() {
                    if ($('#communicationNotificationCheckbox').is(':checked'))
                        $('#communicationNotificationWebServiceContainer').show();
                    else
                        $('#communicationNotificationWebServiceContainer').hide();
                }

                function onHasSegmentationAnalysisChanged() {
                    if ($('#segmentationEnabledCheckbox').is(':checked')) {
                        $("[id^='segmentationAttr_']").showEle('normal');
                    } else {
                        $("[id^='segmentationAttr_']").hide();
                    }
                }

                function onHasConnectedManagementChanged() {
                    if ($('#connectedEnabledCheckbox').is(':checked')) {
                        $("[id^='connectedAttr_']").showEle('normal');
                    } else {
                        $("[id^='connectedAttr_']").hide();
                    }

                    $('#communicationOrderEntryEnabledCheckbox, #communicationPrimaryDriverCheckbox, #communicationCompositionResultsCheckbox, #communicationProductionStatusCheckbox, #communicationNotificationCheckbox, #connectedFastOrderIndicatorEditCheckbox').each(function () {
                        if ($(this).is(":visible") && $(this).closest('.ibutton-container').length == 0) {
                            var currentEle = $(this);
                            $(this).iButton({
                                labelOn: $(currentEle).attr('title').split(';')[0],
                                labelOff: $(currentEle).attr('title').split(';')[1],
                                resizeHandle: false,
                                change: function () {
                                    if ($(currentEle).attr('id') == 'communicationPrimaryDriverCheckbox')
                                        onCommunicationPrimaryDriverToggle();
                                    else if ($(currentEle).attr('id') == 'communicationCompositionResultsCheckbox')
                                        onCommunicationCompositionResultsToggle();
                                    else if ($(currentEle).attr('id') == 'communicationProductionStatusCheckbox')
                                        onCommunicationProductionStatusToggle();
                                    else if ($(currentEle).attr('id') == 'communicationNotificationCheckbox')
                                        onCommunicationNotificationToggle();
                                }
                            });
                        }
                    });
                }

                function  toggleCommunicationsProductionTypeOptions() {
                    if ( $('#connectedEnabledCheckbox').is(':checked') && $('#connectedUseBeta').is(':checked') ) {
                        $('#communicationsProductionTypeSelect').disableOption('communicationsProductionTypeOption_1');
                        $('#communicationsProductionTypeSelect').disableOption('communicationsProductionTypeOption_2');
                    } else {
                        $('#communicationsProductionTypeSelect').enableOption('communicationsProductionTypeOption_1');
                        $('#communicationsProductionTypeSelect').enableOption('communicationsProductionTypeOption_2');
                    }
                }

                // Function to toggle password visibility
                function togglePasswordVisibility() {
                    // Find all password inputs
                    const passwordInputs = document.querySelectorAll('input[type="password"]');

                    passwordInputs.forEach(input => {
                        // Create a container for the input and the icon
                        const container = document.createElement('div');
                        container.style.position = 'relative';
                        container.style.display = 'inline-block';
                        container.style.width = '100%';

                        // Set the input width to 100%
                        input.style.width = '100%';

                        // Create the eye icon element
                        const eyeIcon = document.createElement('i');
                        eyeIcon.classList.add('fa', 'fa-eye');
                        eyeIcon.style.cursor = 'pointer';
                        eyeIcon.style.position = 'absolute';
                        eyeIcon.style.top = '50%';
                        eyeIcon.style.transform = 'translateY(-50%)';

                        // Adjust the right offset based on the input class
                        if (input.classList.contains('right25')) {
                            eyeIcon.style.right = '-25px';
                        } else {
                            eyeIcon.style.right = '10px';
                        }

                        // Insert the input and the eye icon into the container
                        input.parentNode.insertBefore(container, input);
                        container.appendChild(input);
                        container.appendChild(eyeIcon);

                        // Add click event listener to the eye icon
                        eyeIcon.addEventListener('click', () => {
                            // Toggle the type attribute
                            if (input.type === 'password') {
                                input.type = 'text';
                                eyeIcon.classList.remove('fa-eye');
                                eyeIcon.classList.add('fa-eye-slash');
                            } else {
                                input.type = 'password';
                                eyeIcon.classList.remove('fa-eye-slash');
                                eyeIcon.classList.add('fa-eye');
                            }
                        });
                    });
                }

            </script>
        </msgpt:Script>
        <msgpt:Script>
            <script>
                $(function () {
                    $('#defaultTextStyleSelect,#defaultParagraphStyleSelect,#defaultListStyleSelect').each(function () {
                        var currentSelect = $(this);
                        var type = "null";
                        if ($(currentSelect).attr('id').indexOf('ParagraphStyle') != -1)
                            type = "paragraph_styles";
                        else if ($(currentSelect).attr('id').indexOf('ListStyle') != -1)
                            type = "list_styles";
                        if ($(currentSelect).attr('id').indexOf('TextStyle') != -1)
                            type = "text_styles";
                        var isTextStyleList = (type == "text_styles");

                        $(currentSelect).styleActionElement({
                            isAsync: true,
                            getItemsAsync: {
                                getItemsURL: context + "/getDataForSelectMenu.form",
                                fnExtServerParams: function (o) {
                                    return [
                                        {"name": "type", "value": type},
                                        {"name": "selectedItemId", "value": o.inputEle.val()},
                                        {"name": "isFixedDefinition", "value": !isTextStyleList},
                                    ];
                                }
                            }
                        });
                    });
                    $('#multiRecipientIdentifierSelect').each(function () {
                    	var currentSelect = $(this);
						$(currentSelect)
	        				.styleActionElement({
	        					maxItemsInList	: 30,
	                            maxItemDisplay	: 5,
	        					isAsync			: true,
	        					getItemsAsync 	: {
	        						loadOnDemand		: false,
	        						getItemsURL			: context+"/getVariables.form",
	        						fnExtServerParams	: function () {
	        													var stampDate = new Date();
	        													var currentVariableId = $(currentSelect).find('option:selected').val();
	        													
	        													var obj = 	[
	        																	{"name" : "type", "value" : "variablesList"},
	        																	{"name" : "documentId", "value" :  $('#documentId').val()},
	        																	{"name" : "applyOptionNone", "value" : true},
	        																	{"name" : "selectedVariableId", "value" : (currentVariableId && currentVariableId != "" && currentVariableId != null ? currentVariableId : -1) }
	        																];
	        													return obj;
	        											  }
	        					  	}
	        				});
                    });

                    $("input:button,.style_select:not(#defaultTextStyleSelect,#defaultParagraphStyleSelect,#defaultListStyleSelect,#multiRecipientIdentifierSelect)").styleActionElement();
                    $('#communicationsDataResourceSelect').dataFilesPopup({
                        type: 'style_select',
                        nTk: '${nodeGUID}',
                        uTk: '${webDAVToken}',
                        permitEditing: ${canEditDataFiles}
                    });

                    $('#hasInsertCheckbox, #documentEnabledToggle, #documentEnabledTargetOfSyncToggle, #documentAcceptOnlyActiveObjectsToggle, #documentActiveObjectsSyncThroughWorkflowToggle, #documentRemoveToggle, #documentPublishedToggle, #hasSelectionsCheckbox, #connectedEnabledCheckbox, ' +
                        '#segmentationEnabledCheckbox, #connectedExternalValidationCheckbox, #connectedResolveVariableValuesCheckbox, #connectedAppliesTagCloudToggle, ' +
                        '#connectedAppliesCopiesToggle, #connectedSuppressNonEditableZonesCheckbox, #connectedAppliesThumbnailToggle, #connectedSkipInteractiveWhenNoZones, ' +
                        '#maintainVariantHierarchyCheckbox, #connectedUseBeta, #enableStripoCheckbox').each(function () {
                        var currentEle = $(this);
                        $(currentEle).iButton({
                            labelOn: $(currentEle).attr('title').split(';')[0],
                            labelOff: $(currentEle).attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {
                                if ($(currentEle).attr('id') == "hasInsertCheckbox")
                                    onHasInsertManagementChanged();
                                else if ($(currentEle).attr('id') == "hasSelectionsCheckbox")
                                    onHasSelectionManagementChanged();
                                else if ($(currentEle).attr('id') == "connectedEnabledCheckbox") {
                                    onHasConnectedManagementChanged();
                                    toggleCommunicationsProductionTypeOptions();
                                } else if ($(currentEle).attr('id') == "segmentationEnabledCheckbox")
                                    onHasSegmentationAnalysisChanged();
                                else if ($(currentEle).attr('id') == "connectedUseBeta")
                                    toggleCommunicationsProductionTypeOptions();
                            }
                        });
                    });
                    onHasSelectionManagementChanged();
                    onHasConnectedManagementChanged();

                    onCommunicationPrimaryDriverToggle();
                    onCommunicationCompositionResultsToggle();
                    onCommunicationProductionStatusToggle();
                    onCommunicationNotificationToggle();
                    onHasSegmentationAnalysisChanged();

                    toggleCommunicationsProductionTypeOptions();

                    togglePasswordVisibility();

                    var editorData = {
                        content_css: "../includes/themes/commoncss/tinyMCEEditorContent.css",
                    };
                    $("#editor_description").each(function () {
                        tinyMCEdescriptionEditorInit("description", "460", "30", editorData);
                    });

                    $('#touchpointPropertiesLabel').dblclick( function() {
						$('#connectorAsZoneIdContainer').toggle();
                    });

                });
            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <c:set var="viewMetatagsPermission" value="false"/>
    <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
        <c:set var="viewMetatagsPermission" value="true"/>
    </msgpt:IfAuthGranted>

    <msgpt:BodyNew theme="minimal">

   		<input type="hidden" id="documentId" value="${command.document.id}"/>

        <msgpt:BannerNew edit="true" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>

        <msgpt:NewNavigationTabs edit="true" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
        <msgpt:LowerContainer>
            <msgpt:ContentPanel>

                <form:form modelAttribute="command">

                    <form:errors path="*">
                        <msgpt:Information errorMsgs="${messages}" type="error"/>
                    </form:errors>

                    <div class="box-shadow-4 rounded bg-white">
                        <div class="p-4">
                            <div class="pt-2 px-2">

                                <div class="row">
                                    <div class="col-lg-9 mx-lg-auto col-xl">
                                        <div class="mt-4 pt-1">

                                            <div class="border-bottom pb-4 mb-4 bg-white">
                                                <div style="float: left;" class="d-flex flex-column mt-2">
                                                    <!-- Touchpoint Properties -->
                                                    <hgroup id="touchpointPropertiesLabel">
                                                        <h1 class="h4 anchor-title mb-1">
                                                        <span class="anchor-title-inner" style="text-transform: capitalize;">
                                                            <fmtSpring:message code="page.label.touchpoint.setup"/>
                                                                <a href="#basic-info">
                                                                    <i class="far fa-hashtag" aria-hidden="true"></i>
                                                                    <span class="sr-only">
                                                                        <fmtSpring:message code="page.label.anchor"/>
                                                                    </span>
                                                                </a>
                                                        </span>
                                                        </h1>
                                                    </hgroup>
                                                </div>
                                                <!-- BUTTONS -->
                                                <div class="d-flex flex-column" style="text-align: right;">

                                                    <c:choose>
                                                        <c:when test="${not empty param.docid}">
                                                            <c:set var="cancelURL"
                                                                   value="${contextPath}/tpadmin/document_view.form?docid=${param.docid}"/>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:set var="cancelURL" value="${contextPath}/tpadmin/document_view.form"/>
                                                        </c:otherwise>
                                                    </c:choose>

                                                    <div class="py-1 p-xl-0 mt-xl-0 ml-xl-auto order-xl-1">
                                                        <div class="btn-group ml-3">
                                                            <button id="touchpointSetupEditCancelBtn" onclick="javascriptHref('${cancelURL}')" type="button"
                                                                    class="btn btn-outline-light text-body post-trigger">
                                                                <i class="far fa-times-circle mr-2" aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.cancel"/>
                                                            </button>
                                                        </div>
                                                        <div class="btn-group border-separate ml-3">
                                                            <button id="touchpointSetupEditSaveBtn" type="button"
                                                                    onclick="javascript:document.getElementById('command').submit();"
                                                                    class="btn btn-primary post-trigger">
                                                                <i class="far fa-save mr-2" aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.save"/>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- END BUTTONS -->
                                            </div>

                                            <!-- Touchpoint Propeties -->
                                            <section id="basic-info" class="border-bottom pb-4 mb-4">

                                                <div class="form-row">
                                                    <!-- Name -->
                                                    <div class="form-group col-md-6">
                                                        <label for="documentName">
                                                            <fmtSpring:message
                                                                    code="page.label.touchpoint.name"/>
                                                        </label>
                                                        <msgpt:InputFilter type="simpleName">
                                                            <form:input id="documentName" path="document.name" cssClass="form-control" maxlength="255"/>
                                                        </msgpt:InputFilter>
                                                    </div>
                                                    <!-- Tags -->
                                                    <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
                                                        <div class="form-group col-md">
                                                            <label for="documentMetatags">
                                                                <fmtSpring:message code="page.label.metatags"/>
                                                            </label>
                                                            <div class="d-flex flex-column">
                                                                <msgpt:InputFilter type="simpleName">
                                                                    <form:input id="documentMetatags"
                                                                                path="document.metatags"
                                                                                placeholder="${msgpt:getMessage('page.label.optional')}"
                                                                                cssClass="form-control"
                                                                                maxlength="255"/>
                                                                </msgpt:InputFilter>
                                                            </div>
                                                        </div>
                                                    </msgpt:IfAuthGranted>
                                                </div>

                                                <div class="form-row">
                                                    <!-- Enabled -->
                                                    <div class="form-group col-md-6">
                                                        <label for="documentEnabledToggle">
                                                            <fmtSpring:message
                                                                    code="page.label.enabled"/>
                                                        </label>
                                                        <form:checkbox id="documentEnabledToggle" cssClass="radioBtn"
                                                                       title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"
                                                                       path="document.enabled"/>
                                                    </div>

                                                    <!-- Enabled target of sync -->
                                                    <div class="form-group col-md-6">
                                                        <label for="documentEnabledTargetOfSyncToggle">
                                                            <fmtSpring:message
                                                                    code="page.label.enable.as.target.of.sync"/>
                                                        </label>
                                                        <form:checkbox id="documentEnabledTargetOfSyncToggle" cssClass="radioBtn"
                                                                       title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"
                                                                       path="document.targetOfSync"/>
                                                    </div>

                                                    <!-- Accept only active objects -->
                                                    <div class="form-group col-md-6">
                                                        <label for="documentAcceptOnlyActiveObjectsToggle">
                                                            <fmtSpring:message
                                                                    code="page.label.accept.only.active.objects"/>
                                                        </label>
                                                        <form:checkbox id="documentAcceptOnlyActiveObjectsToggle" cssClass="radioBtn"
                                                                       title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"
                                                                       path="document.acceptOnlyActiveObjects"/>
                                                    </div>

                                                    <!-- Require Active Objects synced into this Touchpoint to pass through workflow -->
                                                    <div class="form-group col-md-6">
                                                        <label for="documentActiveObjectsSyncThroughWorkflowToggle">
                                                            <fmtSpring:message
                                                                    code="page.label.active.objects.sync.through.workflow"/>
                                                        </label>
                                                        <form:checkbox id="documentActiveObjectsSyncThroughWorkflowToggle" cssClass="radioBtn"
                                                                       title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"
                                                                       path="document.activeObjectsSyncThroughWorkflow"/>
                                                    </div>


                                                    <!-- Removed -->
                                                    <div class="form-group col-md-6">
                                                        <label for="documentRemoveToggle"><fmtSpring:message
                                                                code="page.label.remove"/></label>
                                                        <form:checkbox id="documentRemoveToggle" cssClass="radioBtn"
                                                                       title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"
                                                                       path="document.removed"/>
                                                    </div>
                                                </div>
                                                <div class="form-row">
                                                    <!-- Fiscal Year Start Month -->
                                                    <div class="form-group col-md-6">
                                                        <label for="fiscalStartMonthSelect"><fmtSpring:message
                                                                code="page.label.fiscal.year.start.month"/></label>
                                                        <form:select cssClass="inputL style_select"
                                                                     id="fiscalStartMonthSelect"
                                                                     path="document.fiscalYearStartMonth">
                                                            <option value="1" ${command.document.fiscalYearStartMonth == 1 ? 'selected' : ''  }>
                                                                <fmtSpring:message
                                                                        code="page.label.month.january"/></option>
                                                            <option value="2" ${command.document.fiscalYearStartMonth == 2 ? 'selected' : ''  }>
                                                                <fmtSpring:message
                                                                        code="page.label.month.february"/></option>
                                                            <option value="3" ${command.document.fiscalYearStartMonth == 3 ? 'selected' : ''  }>
                                                                <fmtSpring:message code="page.label.month.march"/></option>
                                                            <option value="4" ${command.document.fiscalYearStartMonth == 4 ? 'selected' : ''  }>
                                                                <fmtSpring:message code="page.label.month.april"/></option>
                                                            <option value="5" ${command.document.fiscalYearStartMonth == 5 ? 'selected' : ''  }>
                                                                <fmtSpring:message code="page.label.month.may"/></option>
                                                            <option value="6" ${command.document.fiscalYearStartMonth == 6 ? 'selected' : ''  }>
                                                                <fmtSpring:message code="page.label.month.june"/></option>
                                                            <option value="7" ${command.document.fiscalYearStartMonth == 7 ? 'selected' : ''  }>
                                                                <fmtSpring:message code="page.label.month.july"/></option>
                                                            <option value="8" ${command.document.fiscalYearStartMonth == 8 ? 'selected' : ''  }>
                                                                <fmtSpring:message code="page.label.month.august"/></option>
                                                            <option value="9" ${command.document.fiscalYearStartMonth == 9 ? 'selected' : ''  }>
                                                                <fmtSpring:message
                                                                        code="page.label.month.september"/></option>
                                                            <option value="10" ${command.document.fiscalYearStartMonth == 10 ? 'selected' : ''  }>
                                                                <fmtSpring:message
                                                                        code="page.label.month.october"/></option>
                                                            <option value="11" ${command.document.fiscalYearStartMonth == 11 ? 'selected' : ''  }>
                                                                <fmtSpring:message
                                                                        code="page.label.month.november"/></option>
                                                            <option value="12" ${command.document.fiscalYearStartMonth == 12 ? 'selected' : ''  }>
                                                                <fmtSpring:message
                                                                        code="page.label.month.december"/></option>
                                                        </form:select>
                                                    </div>
                                                    <!-- Data Group -->
                                                    <div class="form-group col-md-6">
                                                        <label for="dataGroupSelect"><fmtSpring:message
                                                                code="page.label.data.group"/></label>
                                                        <form:select id="dataGroupSelect" cssClass="style_select"
                                                                     path="document.dataGroup">
                                                            <form:option value="0"
                                                                         label="${msgpt:getMessage('page.label.none')}"/>
                                                            <form:options itemLabel="name" itemValue="id"
                                                                          items="${dataGroups}"/>
                                                        </form:select>
                                                    </div>

                                                </div>

                                                <c:if test="${not command.document.isSmsTouchpoint}">
                                                    <div class="form-row">

                                                        <!-- Default Text Style -->
                                                        <div class="form-group col-md-4">
                                                            <label for="defaultTextStyleSelect"><fmtSpring:message
                                                                    code="page.label.default.text.style"/></label>
                                                            <form:select path="document.defaultTextStyle"
                                                                         id="defaultTextStyleSelect"
                                                                         cssClass="style_select">
                                                                <c:if test="${not empty command.document.defaultTextStyle }">
                                                                    <option value="${command.document.defaultTextStyle.id}"
                                                                            selected="selected"><c:out
                                                                            value="${command.document.defaultTextStyle.name}"/></option>
                                                                </c:if>
                                                            </form:select>
                                                        </div>

                                                        <!-- Default Paragraph Style -->
                                                        <div class="form-group col-md-4">
                                                            <label for="defaultParagraphStyleSelect"><fmtSpring:message
                                                                    code="page.label.default.paragraph.style"/></label>
                                                            <form:select path="document.defaultParagraphStyle"
                                                                         id="defaultParagraphStyleSelect"
                                                                         cssClass="style_select">
                                                                <c:if test="${not empty command.document.defaultParagraphStyle }">
                                                                    <option value="${command.document.defaultParagraphStyle.id}"
                                                                            selected="selected"><c:out
                                                                            value="${command.document.defaultParagraphStyle.name}"/></option>
                                                                </c:if>
                                                            </form:select>
                                                        </div>

                                                        <!-- Default List Style -->
                                                        <div class="form-group col-md-4">
                                                            <label for="defaultListStyleSelect"><fmtSpring:message
                                                                    code="page.label.default.list.style"/></label>
                                                            <form:select path="document.defaultListStyle"
                                                                         id="defaultListStyleSelect"
                                                                         cssClass="style_select">
                                                                <c:if test="${not empty command.document.defaultListStyle }">
                                                                    <option value="${command.document.defaultListStyle.id}"
                                                                            selected="selected"><c:out
                                                                            value="${command.document.defaultListStyle.name}"/></option>
                                                                </c:if>
                                                            </form:select>
                                                        </div>


                                                    </div>
                                                </c:if>

                                                <div class="form-row">

                                                    <!-- Publish -->
                                                    <c:if test="${isExchangeNode}">
                                                        <div class="form-group col-md-4">
                                                            <label for="documentPublishedToggle"><fmtSpring:message
                                                                    code="page.label.published"/></label>
                                                            <form:checkbox id="documentPublishedToggle" cssClass="radioBtn"
                                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"
                                                                           path="document.published"/>
                                                        </div>
                                                    </c:if>

                                                    <!-- Sequence -->
                                                    <c:if test="${command.document.belongToExecutableCollection && not command.document.isNativeCompositionTouchpoint}">
                                                        <div class="form-group col-md-4">
                                                            <label for="sequenceInput"><fmtSpring:message
                                                                    code="page.label.sequence"/></label>
                                                            <msgpt:InputFilter type="numeric">
                                                                <form:input id="sequenceInput" cssClass="input100" path="document.sequence"
                                                                            maxlength="10" autocomplete="false"/>
                                                            </msgpt:InputFilter>
                                                        </div>
                                                    </c:if>

                                                    <!-- Use Connector As Zone Id -->
                                                    <div class="form-group col-md-4" id="connectorAsZoneIdContainer" style="display: none;">
                                                        <label for="useConnectorAsZoneIdInput">Use Connector as Zone ID</label>
                                                        <div class="d-flex flex-column">
                                                            <form:checkbox id="useConnectorAsZoneIdInput" cssClass="checkbox" path="document.useConnectorAsZoneId"/>
                                                        </div>
                                                    </div>

                                                </div>

                                                <div class="form-row">

                                                    <!-- Description -->
                                                    <div class="form-group col-md">
                                                        <label for="editor_description"><fmtSpring:message
                                                                code="page.label.description"/></label>
                                                        <div class="d-flex flex-column">
                                                            <form:textarea path="document.description"
                                                                           cssClass="mceEditor_description"
                                                                           id="editor_description"/>
                                                        </div>
                                                    </div>



                                                </div>
                                                <!-- END - Touchpoint Properties -->


                                            </section>

                                            <!-- Touchpoint Management -->
                                            <c:if test="${licencedForVariantManagement}">
                                                <section id="touchpoint-management" class="border-bottom pb-4 mb-4">

                                                    <hgroup class="pb-2 mb-3">
                                                        <h1 class="h5 anchor-title mb-1">
                                                            <span class="anchor-title-inner">
                                                                <fmtSpring:message code="page.label.touchpoint.management"/>
                                                                    <a href="#touchpoint-management">
                                                                        <i class="far fa-hashtag"
                                                                           aria-hidden="true"></i>
                                                                        <span class="sr-only">
                                                                        <fmtSpring:message
                                                                                code="page.label.anchor"/>
                                                                        </span>
                                                                    </a>
                                                            </span>
                                                        </h1>
                                                    </hgroup>

                                                    <div class="form-row">

                                                        <div class="form-group col-md-4">
                                                            <label for="hasSelectionsCheckbox"><fmtSpring:message
                                                                    code="page.label.variation.enabled"/></label>
                                                            <form:checkbox cssClass="radioBtn" path="hasSelections"
                                                                           id="hasSelectionsCheckbox"
                                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                                        </div>
                                                        <div class="form-group col-md-4">
                                                            <label for="variantDefaultVisibilityToggle"><fmtSpring:message
                                                                    code="page.label.variants.fully.visible.by.default"/></label>
                                                            <form:checkbox id="variantDefaultVisibilityToggle"
                                                                           cssClass="radioBtn"
                                                                           path="document.selectionVisibleByDefault"
                                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>

                                                        </div>
                                                        <div class="form-group col-md-4">
                                                            <label for="variantWorkflowToggle"><fmtSpring:message
                                                                    code="page.label.variant.workflow"/></label>
                                                            <form:checkbox id="variantWorkflowToggle" cssClass="radioBtn"
                                                                           path="document.enabledForVariantWorkflow"
                                                                           title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                                        </div>
                                                    </div>

                                                    <div class="form-row">
                                                        <div id="selectionsAttr_3" class="form-group col-md">
                                                            <label for="selectionParameterGroupSelect"><fmtSpring:message
                                                                    code="page.label.selector"/></label>
                                                                <c:choose>
                                                                    <c:when test="${isSelectorNotEditable}">
                                                                        <div class="d-flex flex-column"><c:out value="${command.selectionParameterGroup.name}"/></div>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <form:select id="selectionParameterGroupSelect" path="selectionParameterGroup">
                                                                            <c:choose>
                                                                                <c:when test="${fn:length(parameterGroups) == 0}">
                                                                                    <option value="0">-- <fmtSpring:message code="page.text.no.selectors"/> --</option>
                                                                                </c:when>
                                                                                <c:otherwise>
                                                                                    <option value="-1"><fmtSpring:message code="page.label.please.select"/></option>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                            <c:forEach var="currentSelector" items="${parameterGroups}">
                                                                                <option value="${currentSelector.id}" ${command.selectionParameterGroup.id == currentSelector.id ? "selected='selected'" : ""}><c:out value="${currentSelector.name}" /></option>
                                                                            </c:forEach>
                                                                        </form:select>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                        </div>

                                                    </div>

                                                </section>
                                            </c:if>
                                            <!-- END - Touchpoint Management -->

                                            <!-- Insert Management -->
                                            <c:if test="${licencedForInsertManagement && not command.document.isEmailTouchpoint && not command.document.isSmsTouchpoint && not command.document.isWebTouchpoint}">
                                                <section id="insert-management" class="border-bottom pb-4 mb-4">

                                                    <hgroup class="pb-2 mb-3">
                                                        <h1 class="h5 anchor-title mb-1">
                                                            <span class="anchor-title-inner">
                                                                <fmtSpring:message code="page.label.insert.management"/>
                                                                    <a href="#insert-management">
                                                                        <i class="far fa-hashtag"
                                                                           aria-hidden="true"></i>
                                                                        <span class="sr-only">
                                                                        <fmtSpring:message
                                                                                code="page.label.anchor"/>
                                                                        </span>
                                                                    </a>
                                                            </span>
                                                        </h1>
                                                    </hgroup>

                                                    <div class="form-row">

                                                        <div class="form-group col-md">
                                                            <label for="hasInsertCheckbox"><fmtSpring:message
                                                                    code="page.label.inserts.enabled"/></label>
                                                            <form:checkbox cssClass="radioBtn" path="hasInsert"
                                                                           id="hasInsertCheckbox"
                                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                                        </div>
                                                    </div>

                                                    <div class="form-row">

                                                        <div class="form-group col-md-6" id="insertAttr_0">
                                                            <label for="insertManagementParameterGroupSelect"><fmtSpring:message
                                                                    code="page.label.selector"/></label>

                                                            <div class="d-flex flex-column">
                                                                <c:choose>
                                                                    <c:when test="${not command.document.insertSelectorEditable}">
                                                                        <span class="control-static"></span><c:out
                                                                            value="${command.parameterGroup.name}"/>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <form:select id="insertManagementParameterGroupSelect" cssClass="style_select" path="parameterGroup">
                                                                            <c:choose>
                                                                                <c:when test="${fn:length(parameterGroupsForInsert) == 0}">
                                                                                    <form:option value="0">-- <fmtSpring:message
                                                                                            code="page.text.no.selectors"/> --</form:option>
                                                                                </c:when>
                                                                                <c:otherwise>
                                                                                    <form:option value="-1"><fmtSpring:message
                                                                                            code="page.label.please.select"/></form:option>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                            <c:forEach var="currentSelector" items="${parameterGroupsForInsert}">
                                                                                <option value="${currentSelector.id}" ${command.parameterGroup.id == currentSelector.id ? "selected='selected'" : ""}><c:out value="${currentSelector.name}" /></option>
                                                                            </c:forEach>
                                                                        </form:select>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </div>

                                                        <div class="form-group col-md-4" id="insertAttr_3">
                                                            <label for="defaultRateScheduleSelect"><fmtSpring:message
                                                                    code="page.label.default.rate.sheet"/></label>
                                                            <div class="d-flex flex-column">
                                                                <form:select id="defaultRateScheduleSelect" cssClass="style_select" path="defaultRateSchedule">
                                                                    <c:choose>
                                                                        <c:when test="${fn:length(rateSchedules) == 0}">
                                                                            <form:option value="0">-- <fmtSpring:message
                                                                                    code="page.text.no.rate.sheets"/> --</form:option>
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            <form:option value="-1"><fmtSpring:message
                                                                                    code="page.label.please.select"/></form:option>
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                    <form:options items="${rateSchedules}" itemValue="id"
                                                                                  itemLabel="name"/>
                                                                </form:select>
                                                            </div>
                                                        </div>

                                                    </div>

                                                    <div class="form-row">

                                                        <div class="form-group col-md-6" id="insertAttr_1">
                                                            <label for="firstPageWeightInput"><fmtSpring:message
                                                                    code="page.label.first.page.weight"/></label>
                                                                <div class="d-flex mr-auto" style="white-space: nowrap;">
                                                                    <msgpt:InputFilter type="decimal">
                                                                        <form:input id="firstPageWeightInput" path="firstPageWeight"/>
                                                                        <span class="control-hint" style="padding-left: 8px;"><c:out value="${defaultWeightUnit.displayText}"></c:out></span>
                                                                    </msgpt:InputFilter>
                                                                </div>
                                                        </div>
                                                        <div class="form-group col-md-6" id="insertAttr_2">
                                                            <label for="otherPagesWeightInput"><fmtSpring:message
                                                                    code="page.label.other.page.weights"/></label>
                                                                <div class="d-flex mr-auto" style="white-space: nowrap;">
                                                                    <msgpt:InputFilter type="decimal">
                                                                        <form:input id="otherPagesWeightInput" path="otherPagesWeight"/>
                                                                        <span class="control-hint" style="padding-left: 8px;"><c:out value="${defaultWeightUnit.displayText}"></c:out></span>
                                                                    </msgpt:InputFilter>
                                                                </div>
                                                        </div>

                                                    </div>
                                                </section>
                                            </c:if>
                                            <!-- END Insert Management -->

                                            <!-- Language Management -->
                                            <section id="language-management" class="border-bottom pb-4 mb-4">

                                                <hgroup class="pb-2 mb-3">
                                                    <h1 class="h5 anchor-title mb-1">
                                                                <span class="anchor-title-inner">
                                                                    <fmtSpring:message code="page.label.language.management"/>
                                                                        <a href="#language-management">
                                                                            <i class="far fa-hashtag"
                                                                               aria-hidden="true"></i>
                                                                            <span class="sr-only">
                                                                            <fmtSpring:message
                                                                                    code="page.label.anchor"/>
                                                                            </span>
                                                                        </a>
                                                                </span>
                                                    </h1>
                                                </hgroup>

                                                <div class="form-row">

                                                    <div class="form-group col-md" >

                                                    <c:choose>
                                                        <c:when test="${command.document.isMultiLanguage}">

                                                            <label for="languageSelectorSelect"><fmtSpring:message code="page.label.selector"/></label>

                                                            <c:choose>
                                                                <c:when test="${not command.document.languageSelectorEditable}">
                                                                    <div class="d-flex flex-column">
                                                                        <span class="control-static"><c:out value="${command.languageParameterGroup.name}"/></span>
                                                                    </div>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <form:select id="languageSelectorSelect" cssClass="style_select" path="languageParameterGroup">
                                                                        <c:choose>
                                                                            <c:when test="${fn:length(parameterGroupsForLanguage) == 0}">
                                                                                <option id="languageSelectorSelect_0" value="0">-- <fmtSpring:message code="page.text.no.selectors"/> --</option>
                                                                            </c:when>
                                                                            <c:otherwise>
                                                                                <option id="languageSelectorSelect_-1" value="-1"><fmtSpring:message code="page.label.please.select"/></option>
                                                                            </c:otherwise>
                                                                        </c:choose>
                                                                        <c:forEach var="currentSelector" items="${parameterGroupsForLanguage}">
                                                                            <option id="languageSelectorSelect_${currentSelector.id}" value="${currentSelector.id}" ${command.languageParameterGroup.id == currentSelector.id ? "selected='selected'" : ""}><c:out value="${currentSelector.name}" /></option>
                                                                        </c:forEach>
                                                                    </form:select>
                                                                </c:otherwise>
                                                            </c:choose>

                                                        </c:when>
                                                        <c:otherwise>
                                                            <label><fmtSpring:message code="page.label.default.language"/></label>
                                                            <div class="d-flex flex-column">
                                                                <span class="control-static"><c:out value="${command.document.defaultTouchpointLanguage.name}"/></span>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    </div>

                                                </div>
                                            </section>
                                            <!-- END Language Management -->

                                            <!-- Connected Management -->
                                            <c:if test="${licencedForMessagepointInteractive && not command.document.isSmsTouchpoint}">
                                                <section id="connected-management" class="border-bottom pb-4 mb-4">

                                                    <hgroup class="pb-2 mb-3">
                                                        <h1 class="h5 anchor-title mb-1">
                                                                        <span class="anchor-title-inner">
                                                                            <fmtSpring:message code="page.label.communications.management"/>
                                                                                <a href="#connected-management">
                                                                                    <i class="far fa-hashtag"
                                                                                       aria-hidden="true"></i>
                                                                                    <span class="sr-only">
                                                                                    <fmtSpring:message
                                                                                            code="page.label.anchor"/>
                                                                                    </span>
                                                                                </a>
                                                                        </span>
                                                        </h1>
                                                    </hgroup>

                                                    <div class="form-row">
                                                        <div class="form-group col-md">
                                                            <label for="connectedEnabledCheckbox"><fmtSpring:message code="page.label.connected.enabled"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="document.connectedEnabled"
                                                                           id="connectedEnabledCheckbox"
                                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>

                                                        </div>
                                                    </div>

                                                    <div class="form-row">
                                                        <div class="form-group col-md-6" id="connectedAttr_1">
                                                            <label for="communicationsDataResourceSelect"><fmtSpring:message code="page.label.data.resource"/></label>
                                                            <form:select cssClass="style_select"
                                                                         id="communicationsDataResourceSelect"
                                                                         path="document.communicationsDataResource">
                                                                <form:option value="0">-- <fmtSpring:message
                                                                        code="page.text.select.resource"/> --</form:option>
                                                                <form:options items="${dataResources}" itemValue="id"
                                                                              itemLabel="name"/>
                                                            </form:select>
                                                        </div>
                                                        <div class="form-group col-md-6" id="connectedAttr_6">
                                                            <label for="communicationsProductionTypeSelect"><fmtSpring:message code="page.label.production.type"/></label>
                                                            <form:select cssClass="style_select"
                                                                         id="communicationsProductionTypeSelect"
                                                                         path="document.communicationProductionTypeId">
                                                                <form:option id="communicationsProductionTypeOption_1" value="1"><fmtSpring:message
                                                                        code="page.label.on.approval"/></form:option>
                                                                <form:option id="communicationsProductionTypeOption_2" value="2"><fmtSpring:message
                                                                        code="page.label.batch"/></form:option>
                                                                <form:option id="communicationsProductionTypeOption_3" value="3"><fmtSpring:message
                                                                        code="page.label.on.approval.mini.bundle"/></form:option>
                                                            </form:select>
                                                        </div>
                                                    </div>
                                                    <div class="form-row">
                                                        <div class="form-group col-md-6" id="connectedAttr_4">
                                                            <label><fmtSpring:message code="page.label.server.access"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="useWebServiceForCommunicationCompositionResults"
                                                                           id="communicationCompositionResultsCheckbox"
                                                                           title="${msgpt:getMessage('page.label.web.service')};${msgpt:getMessage('page.label.cloud')}"/>
                                                            <div class="d-flex flex-column">
                                                                <div id="communicationCompositionResultsWebServiceContainer" style="display: none;" class="pddng-lv3">

                                                                    <div class="formControl horizontal-control row">
                                                                        <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                                                code="page.label.bundle.delivery"/></span></label>
                                                                        <div class="controlWrapper col-5of12">
                                                                            <msgpt:BundleDeliverySelect id="compositionResultsWebService"
                                                                                                        deServerGuidPath="command.document.communicationCompositionResultsWebService.DEServerGuid"
                                                                                                        eventTypeFilter="<%= EventType.TYPE_COMMUNICATION_PRODUCTION %>" />
                                                                        </div>
                                                                    </div>

                                                                    <c:if test="${command.showExistingCompositionResultsWebService}">
                                                                        <div class="formControl horizontal-control row">
                                                                            <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                                                    code="page.label.url"/></span></label>
                                                                            <div class="controlWrapper col-5of12">
                                                                                <msgpt:InputFilter type="webSite">
                                                                                    <form:input
                                                                                            path="document.communicationCompositionResultsWebService.url"
                                                                                            cssClass="webServiceUrl"/>
                                                                                </msgpt:InputFilter>
                                                                            </div>
                                                                        </div>
                                                                        <div class="formControl horizontal-control row">
                                                                            <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                                                    code="page.label.username"/></span></label>
                                                                            <div class="controlWrapper col-5of12">
                                                                                <msgpt:InputFilter type="targetingValue">
                                                                                    <form:input
                                                                                            path="document.communicationCompositionResultsWebService.username"
                                                                                            cssClass="webServiceUsername"/>
                                                                                </msgpt:InputFilter>
                                                                            </div>
                                                                        </div>
                                                                        <div class="formControl horizontal-control row">
                                                                            <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                                                    code="page.label.password"/></span></label>
                                                                            <div class="controlWrapper col-5of12">
                                                                                <msgpt:InputFilter type="targetingValue">
                                                                                    <form:password showPassword="true"
                                                                                                   path="document.communicationCompositionResultsWebService.password"
                                                                                                   cssClass="webServicePassword right25"/>
                                                                                </msgpt:InputFilter>
                                                                            </div>
                                                                        </div>

                                                                    </c:if>

                                                                    <div class="formControl horizontal-control row">
                                                                        <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                                                code="page.label.connected.zone.content"/></span></label>
                                                                        <div class="controlWrapper col-5of12">
                                                                            <form:checkbox cssClass="radioBtn"
                                                                                           path="document.communicationZoneContentTransient"
                                                                                           id="communicationTransientZoneContent"
                                                                                           title="${msgpt:getMessage('page.label.transient')};${msgpt:getMessage('page.label.unrestricted')}"/>
                                                                        </div>
                                                                    </div>
                                                                    <div class="formControl horizontal-control row" id="connectedAttr_12">
                                                                        <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                                                code="page.label.resolve.variable.values"/></span></label>
                                                                        <div class="controlWrapper col-5of12">
                                                                            <form:checkbox cssClass="radioBtn"
                                                                                           path="document.communicationResolveVariableValues"
                                                                                           id="connectedResolveVariableValuesCheckbox"
                                                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form-group col-md-6" id="connectedAttr_5">
                                                            <label for="communicationProductionStatusCheckbox"><fmtSpring:message code="page.label.production.status"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="useWebServiceForCommunicationProductionStatus"
                                                                           id="communicationProductionStatusCheckbox"
                                                                           title="${msgpt:getMessage('page.label.web.service')};${msgpt:getMessage('page.label.not.tracked')}"/>
                                                            <div class="d-flex flex-column">
                                                                <div id="communicationProductionStatusWebServiceContainer"
                                                                     style="display: none;" class="pddng-lv3">
                                                                    <div class="formControl horizontal-control row">
                                                                        <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                                                code="page.label.url"/></span></label>
                                                                        <div class="controlWrapper col-5of12">
                                                                            <msgpt:InputFilter type="webSite">
                                                                                <form:input
                                                                                        path="document.communicationProductionStatusWebService.url"
                                                                                        cssClass="webServiceUrl"/>
                                                                            </msgpt:InputFilter>
                                                                        </div>
                                                                    </div>
                                                                    <div class="formControl horizontal-control row">
                                                                        <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                                                code="page.label.username"/></span></label>
                                                                        <div class="controlWrapper col-5of12">
                                                                            <msgpt:InputFilter type="targetingValue">
                                                                                <form:input
                                                                                        path="document.communicationProductionStatusWebService.username"
                                                                                        cssClass="inputXL webServiceUsername"/>
                                                                            </msgpt:InputFilter>
                                                                        </div>
                                                                    </div>
                                                                    <div class="formControl horizontal-control row">
                                                                        <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                                                code="page.label.password"/></span></label>
                                                                        <div class="controlWrapper col-5of12">
                                                                            <msgpt:InputFilter type="targetingValue">
                                                                                <form:password showPassword="true"
                                                                                               path="document.communicationProductionStatusWebService.password"
                                                                                               cssClass="webServicePassword right25"/>
                                                                            </msgpt:InputFilter>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-row">
                                                        <div class="form-group col-md-6" id="connectedAttr_7">
                                                            <label for="multiRecipientIdentifierSelect"><fmtSpring:message code="page.label.multi.recipient.identifier"/></label>
                                                            <form:select cssClass="style_select"
                                                                         id="multiRecipientIdentifierSelect"
                                                                         path="document.communicationMultiRecipientIdentifier">
                                                                <c:if test="${not empty command.document.communicationMultiRecipientIdentifier }">
                                                                    <option value="${command.document.communicationMultiRecipientIdentifier.id}"
                                                                            selected="selected"><c:out
                                                                            value="${command.document.communicationMultiRecipientIdentifier.displayName}"/></option>
                                                                </c:if>
                                                            </form:select>
                                                        </div>
                                                        <div class="form-group col-md-6" id="connectedAttr_8">
                                                            <label for="connectedExternalValidationCheckbox"><fmtSpring:message code="page.label.socialize.proofs"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="document.communicationExternalValidationEnabled"
                                                                           id="connectedExternalValidationCheckbox"
                                                                               title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>

                                                        </div>
                                                    </div>
                                                    <div class="form-row">
                                                        <div class="form-group col-md-6" id="connectedAttr_10">
                                                            <label for="connectedAppliesCopiesToggle"><fmtSpring:message code="page.label.copies.input"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="document.communicationAppliesCopiesInput"
                                                                           id="connectedAppliesCopiesToggle"
                                                                           title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                                        </div>
                                                        <div class="form-group col-md-6" id="connectedAttr_11">
                                                            <label for="connectedAppliesThumbnailToggle"><fmtSpring:message code="page.label.thumbnail"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="document.communicationDisplayTouchpointThumbnail"
                                                                           id="connectedAppliesThumbnailToggle"
                                                                               title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                                        </div>
                                                    </div>
                                                    <div class="form-row">
                                                        <div class="form-group col-md-6" id="connectedAttr_9">
                                                            <label for="connectedAppliesTagCloudToggle"><fmtSpring:message code="page.label.tag.cloud"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="document.communicationAppliesTagCloud"
                                                                           id="connectedAppliesTagCloudToggle"
                                                                           title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                                        </div>
                                                        <div class="form-group col-md-6" id="connectedAttr_17">
                                                            <label for="communicationForcedProofingDriverValue"><fmtSpring:message code="page.label.forced.proofing.primary.value"/></label>
                                                            <div class="d-flex flex-column">
                                                                <form:input id="communicationForcedProofingDriverValue" cssClass="inputL" path="document.communicationForcedProofingDriverValue" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-row">
                                                        <div class="form-group col-md-6" id="connectedAttr_13">
                                                            <label for="connectedSuppressNonEditableZonesCheckbox"><fmtSpring:message code="page.label.suppress.noneditable.zones"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="document.communicationSuppressNonEditableZones"
                                                                           id="connectedSuppressNonEditableZonesCheckbox"
                                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                                        </div>
                                                        <div class="form-group col-md-6" id="connectedAttr_14">
                                                            <label><fmtSpring:message code="page.label.skip.interactive.if.no.connected.zones.exist"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="document.communicationSkipInteractiveWhenNoZones"
                                                                           id="connectedSkipInteractiveWhenNoZones"
                                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                                        </div>
                                                    </div>
                                                    <div class="form-row">
                                                        <div class="form-group col-md-6" id="connectedAttr_15">
                                                            <label for="connectedUseBeta"><fmtSpring:message code="page.label.use.connected.beta"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="document.communicationUseBeta"
                                                                           id="connectedUseBeta"
                                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                                        </div>
                                                        <div class="form-group col-md-6" id="connectedAttr_16">
                                                            <label><fmtSpring:message code="page.label.enable.notifications"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="useWebServiceForCommunicationNotification"
                                                                                   id="communicationNotificationCheckbox"
                                                                                   title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                                            <div class="d-flex flex-column">
                                                                <div id="communicationNotificationWebServiceContainer"
                                                                     style="display: none;" class="pddng-lv3">
                                                                    <div class="formControl horizontal-control row">
                                                                        <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                                                code="page.label.url"/></span></label>
                                                                        <div class="controlWrapper col-5of12">
                                                                            <msgpt:InputFilter type="webSite">
                                                                                <form:input
                                                                                        path="document.communicationNotificationWebService.url"
                                                                                        cssClass="webServiceUrl"/>
                                                                            </msgpt:InputFilter>
                                                                        </div>
                                                                    </div>
                                                                    <div class="formControl horizontal-control row">
                                                                        <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                                                code="page.label.username"/></span></label>
                                                                        <div class="controlWrapper col-5of12">
                                                                            <msgpt:InputFilter type="targetingValue">
                                                                                <form:input
                                                                                        path="document.communicationNotificationWebService.username"
                                                                                        cssClass="inputXL webServiceUsername"/>
                                                                            </msgpt:InputFilter>
                                                                        </div>
                                                                    </div>
                                                                    <div class="formControl horizontal-control row">
                                                                        <label class="col-2of12"><span class="labelText"><fmtSpring:message
                                                                                code="page.label.password"/></span></label>
                                                                        <div class="controlWrapper col-5of12">
                                                                            <msgpt:InputFilter type="targetingValue">
                                                                                <form:password showPassword="true"
                                                                                               path="document.communicationNotificationWebService.password"
                                                                                               cssClass="webServicePassword"/>
                                                                            </msgpt:InputFilter>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-row">
                                                        <div class="form-group col-md-6" id="connectedAttr_18">
                                                            <label for="connectedFastOrderIndicatorEditCheckbox"><fmtSpring:message code="page.label.fast.order.indicator.edit"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="document.communicationFastOrderIndicatorEdit"
                                                                           id="connectedFastOrderIndicatorEditCheckbox"
                                                                           title="${msgpt:getMessage('page.label.enable')};${msgpt:getMessage('page.label.disable')}"/>
                                                        </div>
                                                    </div>
                                                </section>

                                            </c:if>
                                            <!-- END Connected Management -->

                                            <!-- Segmentation Analysis -->
                                            <c:if test="${licencedForSegmentationAnalysis}">

                                                <section id="segmentation-management" class="border-bottom pb-4 mb-4">

                                                    <hgroup class="pb-2 mb-3">
                                                        <h1 class="h5 anchor-title mb-1">
                                                            <span class="anchor-title-inner">
                                                                <fmtSpring:message code="page.label.segmentation.analysis"/>
                                                                    <a href="#segmentation-management">
                                                                        <i class="far fa-hashtag"
                                                                           aria-hidden="true"></i>
                                                                        <span class="sr-only">
                                                                        <fmtSpring:message
                                                                                code="page.label.anchor"/>
                                                                        </span>
                                                                </a>
                                                            </span>
                                                        </h1>
                                                    </hgroup>

                                                    <div class="form-row">
                                                        <div class="form-group col-md">
                                                            <label for="segmentationEnabledCheckbox"><fmtSpring:message code="page.label.analysis.enabled"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="document.segmentationAnalysisEnabled"
                                                                           id="segmentationEnabledCheckbox"
                                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                                        </div>
                                                    </div>
                                                    <div class="form-row">
                                                        <div class="form-group col-md" id="segmentationAttr_0">
                                                            <label for="segmentationAnalysisResource"><fmtSpring:message code="page.label.data.resource"/></label>
                                                            <form:select id="segmentationAnalysisResource" cssClass="style_select" path="document.segmentationAnalysisResource">
                                                                <c:choose>
                                                                    <c:when test="${fn:length(dataResourceList) == 0}">
                                                                        <form:option value="0">-- <fmtSpring:message
                                                                                code="page.text.no.data.resources"/> --</form:option>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <form:option value="-1"><fmtSpring:message
                                                                                code="page.label.please.select"/></form:option>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                                <form:options items="${dataResourceList}" itemValue="id"
                                                                              itemLabel="name"/>
                                                            </form:select>
                                                        </div>
                                                    </div>
                                                </section>
                                            </c:if>
                                            <!-- END Segmentation Analysis -->

                                            <!-- Metadata -->
                                            <section id="metadata-management" class="border-bottom pb-4 mb-4">

                                                <hgroup class="pb-2 mb-3">
                                                    <h1 class="h5 anchor-title mb-1">
                                                        <span class="anchor-title-inner">
                                                            <fmtSpring:message code="page.label.metadata"/>
                                                                <a href="#metadata-management">
                                                                    <i class="far fa-hashtag"
                                                                       aria-hidden="true"></i>
                                                                    <span class="sr-only">
                                                                    <fmtSpring:message
                                                                            code="page.label.anchor"/>
                                                                    </span>
                                                            </a>
                                                        </span>
                                                    </h1>
                                                </hgroup>

                                                <div class="form-row">
                                                    <div class="form-group col-md-4">
                                                        <label for="touchpointMetadataSelect"><fmtSpring:message code="page.label.touchpoint"/></label>

                                                        <c:choose>
                                                            <c:when test="${command.document.hasAppliedTouchpointMetadata}">
                                                                <div class="d-flex flex-column">
                                                                    <span class="control-static">
                                                                        <c:out value="${command.document.touchpointMetadataFormDefinition.name}"/>
                                                                    </span>
                                                                </div>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <form:select id="touchpointMetadataSelect"
                                                                             path="document.touchpointMetadataFormDefinition"
                                                                             cssClass="style_select">
                                                                    <form:option value="0"><fmtSpring:message
                                                                            code="page.label.none"/></form:option>
                                                                    <form:options items="${touchpointMetadataForms}"
                                                                                  itemValue="id" itemLabel="name"/>
                                                                </form:select>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                    <div class="form-group col-md-4">
                                                        <label for="variantMetadataSelect"><fmtSpring:message code="page.label.variant"/></label>
                                                        <c:choose>
                                                            <c:when test="${command.document.hasAppliedVariantMetadata}">
                                                                <div class="d-flex flex-column">
                                                                    <span class="control-static">
                                                                        <c:out value="${command.document.variantMetadataFormDefinition.name}"/>
                                                                    </span>
                                                                </div>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <form:select id="variantMetadataSelect"
                                                                             path="document.variantMetadataFormDefinition"
                                                                             cssClass="style_select">
                                                                    <form:option value="0"><fmtSpring:message
                                                                            code="page.label.none"/></form:option>
                                                                    <form:options items="${variantMetadataForms}" itemValue="id"
                                                                                  itemLabel="name"/>
                                                                </form:select>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                    <div class="form-group col-md-4">
                                                        <label for="maintainVariantHierarchyCheckbox"><fmtSpring:message code="page.label.variant.hierarchy"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="document.maintainVariantHierarchy"
                                                                           id="maintainVariantHierarchyCheckbox"
                                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                                    </div>
                                                </div>
                                            </section>
                                            <!-- END Metadata -->

                                            <!-- SFTP Settings -->
                                            <section id="sftp-management" class="border-bottom pb-4 mb-4">

                                                <hgroup class="pb-2 mb-3">
                                                    <h1 class="h5 anchor-title mb-1">
                                                            <span class="anchor-title-inner">
                                                                <fmtSpring:message code="page.label.sftp.settings"/>
                                                                    <a href="#sftp-management">
                                                                        <i class="far fa-hashtag"
                                                                           aria-hidden="true"></i>
                                                                        <span class="sr-only">
                                                                        <fmtSpring:message
                                                                                code="page.label.anchor"/>
                                                                        </span>
                                                                </a>
                                                            </span>
                                                    </h1>
                                                </hgroup>

                                                <div class="form-row">
                                                    <div class="form-group col-md-6">
                                                        <label for="sftpIPAddress"><fmtSpring:message code="page.label.ip.address"/></label>
                                                        <div class="d-flex flex-column">
                                                            <msgpt:InputFilter type="webSite">
                                                                <form:input id="sftpIPAddress" path="document.sftpIPAddress" maxlength="256"/>
                                                            </msgpt:InputFilter>
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <label for="sftpFolderPath"><fmtSpring:message code="page.label.folder"/></label>
                                                        <div class="d-flex flex-column">
                                                            <msgpt:InputFilter type="filename">
                                                                <form:input id="sftpFolderPath" path="document.sftpFolderPath" maxlength="1024"/>
                                                            </msgpt:InputFilter>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-row">
                                                    <div class="form-group col-md-6">
                                                        <label for="sftpUsername"><fmtSpring:message code="page.label.username"/></label>
                                                        <div class="d-flex flex-column">
                                                            <msgpt:InputFilter type="username">
                                                                <form:input id="sftpUsername" path="document.sftpUsername"
                                                                            maxlength="256"/>
                                                            </msgpt:InputFilter>
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <label for="sftpPassword"><fmtSpring:message code="page.label.password"/></label>
                                                        <div class="d-flex flex-column">
                                                            <msgpt:InputFilter type="password">
                                                                <form:password showPassword="true" id="sftpPassword" path="sftpPasswordDecrypted"
                                                                            maxlength="256"/>
                                                            </msgpt:InputFilter>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-row">
                                                    <div class="form-group col-md-6">
                                                        <label for="sftpSSHKey"><fmtSpring:message code="page.label.ssh.key"/></label>
                                                        <div class="d-flex flex-column">
                                                            <msgpt:InputFilter type="username">
                                                                <form:password showPassword="true" id="sftpSSHKey" path="sftpSSHKeyDecrypted" maxlength="256"/>
                                                            </msgpt:InputFilter>
                                                        </div>
                                                    </div>
                                                </div>
                                            </section>
                                            <!-- END SFTP Settings -->

                                            <!-- Blue Relay Configuration -->
                                            <section id="bluerelay-management" class="border-bottom pb-4 mb-4">

                                                <hgroup class="pb-2 mb-3">
                                                    <h1 class="h5 anchor-title mb-1">
                                                        <span class="anchor-title-inner">
                                                            <fmtSpring:message code="page.label.blue.relay.configuration"/>
                                                                <a href="#bluerelay-management">
                                                                    <i class="far fa-hashtag"
                                                                       aria-hidden="true"></i>
                                                                    <span class="sr-only">
                                                                    <fmtSpring:message
                                                                            code="page.label.anchor"/>
                                                                    </span>
                                                            </a>
                                                        </span>
                                                    </h1>
                                                </hgroup>

                                                <div class="form-row">
                                                    <div class="form-group col-md-6">
                                                        <label for="blueRelayEndpoint"><fmtSpring:message code="page.label.blue.relay.endpoint"/></label>
                                                        <div class="d-flex flex-column">
                                                            <msgpt:InputFilter type="webSite">
                                                                <form:input id="blueRelayEndpoint" path="document.blueRelayEndpoint" maxlength="255"/>
                                                            </msgpt:InputFilter>
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <label for="blueRelayUsername"><fmtSpring:message code="page.label.username"/></label>
                                                        <div class="d-flex flex-column">
                                                            <msgpt:InputFilter type="username">
                                                                <form:input id="blueRelayUsername" path="document.blueRelayUsername" maxlength="255"/>
                                                            </msgpt:InputFilter>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-row">
                                                    <div class="form-group col-md-6">
                                                        <label for="blueRelayToken"><fmtSpring:message code="page.label.blue.relay.token"/></label>
                                                        <div class="d-flex flex-column">
                                                            <msgpt:InputFilter type="password">
                                                                <form:password showPassword="true" id="blueRelayToken" path="document.blueRelayToken" maxlength="255"/>
                                                            </msgpt:InputFilter>
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <label for="blueRelayTargetFolder"><fmtSpring:message code="page.label.blue.relay.target.folder"/></label>
                                                        <div class="d-flex flex-column">
                                                            <msgpt:InputFilter type="filename">
                                                                <form:input id="blueRelayTargetFolder" path="document.blueRelayTargetFolder" maxlength="255"/>
                                                            </msgpt:InputFilter>
                                                        </div>
                                                    </div>
                                                </div>
                                            </section>
                                            <!-- END - Blue Relay Configuration -->

                                            <!-- Brand Configuration -->
                                            <section id="brand-management" class="border-bottom pb-4 mb-4">

                                                <hgroup class="pb-2 mb-3">
                                                    <h1 class="h5 anchor-title mb-1">
                                                            <span class="anchor-title-inner">
                                                                <fmtSpring:message code="page.label.brand"/>
                                                                    <a href="#brand-management">
                                                                        <i class="far fa-hashtag"
                                                                           aria-hidden="true"></i>
                                                                        <span class="sr-only">
                                                                        <fmtSpring:message
                                                                                code="page.label.anchor"/>
                                                                        </span>
                                                                </a>
                                                            </span>
                                                    </h1>
                                                </hgroup>

                                                <div class="form-row">
                                                    <div class="form-group col-md">
                                                        <label for="brandProfileSelect"><fmtSpring:message code="page.label.brand.profile"/></label>
                                                        <form:select id="brandProfileSelect" path="document.brandProfile" cssClass="style_select inputXL">
                                                            <form:option value="-1"><fmtSpring:message code="page.label.none" /></form:option>
                                                            <form:options items="${brandProfiles}" itemValue="id" itemLabel="name" />
                                                        </form:select>
                                                    </div>
                                                </div>
                                            </section>
                                            <!-- END - Brand Configuration -->

                                            <!-- Stripo Configuration -->
                                            <c:if test="${command.document.isOmniChannel || command.document.isEmailTouchpoint || command.document.isWebTouchpoint}">
                                                <section id="stripo-management" class="border-bottom pb-4 mb-4">

                                                    <hgroup class="pb-2 mb-3">
                                                        <h1 class="h5 anchor-title mb-1">
                                                                    <span class="anchor-title-inner">
                                                                        <fmtSpring:message code="page.label.stripo.email.editor"/>
                                                                            <a href="#stripo-management">
                                                                                <i class="far fa-hashtag"
                                                                                   aria-hidden="true"></i>
                                                                                <span class="sr-only">
                                                                                <fmtSpring:message
                                                                                        code="page.label.anchor"/>
                                                                                </span>
                                                                        </a>
                                                                    </span>
                                                        </h1>
                                                    </hgroup>

                                                    <div class="form-row">
                                                        <div class="form-group col-md">
                                                            <label for="enableStripoCheckbox"><fmtSpring:message code="page.label.editor.enabled"/></label>
                                                            <form:checkbox cssClass="radioBtn"
                                                                           path="stripoEnabled"
                                                                           id="enableStripoCheckbox"
                                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                                        </div>
                                                    </div>
                                                </section>
                                            </c:if>
                                            <!-- END - Stripo Configuration -->

                                            <!-- BUTTONS -->
                                            <div class="d-flex flex-column flex-xl-row pb-4 bg-white" style="text-align: right;">

                                                <div class="py-1 p-xl-0 mt-xl-0 ml-xl-auto order-xl-1">
                                                    <div class="btn-group ml-3">
                                                        <button id="touchpointSetupEditCancelBtn" onclick="javascriptHref('${cancelURL}')" type="button"
                                                                class="btn btn-outline-light text-body post-trigger">
                                                            <i class="far fa-times-circle mr-2" aria-hidden="true"></i>
                                                            <fmtSpring:message code="page.label.cancel"/>
                                                        </button>
                                                    </div>
                                                    <div class="btn-group border-separate ml-3">
                                                        <button id="touchpointSetupEditSaveBtn" type="button"
                                                                onclick="javascript:document.getElementById('command').submit();"
                                                                class="btn btn-primary post-trigger">
                                                            <i class="far fa-save mr-2" aria-hidden="true"></i>
                                                            <fmtSpring:message code="page.label.save"/>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- END BUTTONS -->

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </form:form>

            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
    </msgpt:BodyNew>
</msgpt:Html5>