<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.controller.tpadmin.ZoneDataGroupBulkEditController"%>
<%@page import="com.prinova.messagepoint.model.admin.DataType"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.zone" viewType="<%= MessagepointHeader.ViewType.EDIT %>">


	<msgpt:Script>
        <script>
            function closeIframe_local() {
                var currentURL = getTopFrame().location.href;
                currentURL = removeParam(currentURL, "zonedatagroupbulkedit");
                getTopFrame().location.href = currentURL;
            }

            function refSourceChange() {
                $("[id^='refElements_']").each( function() {
                    if($(this).attr('id') == 'refElements_'+$('#refDataSourceSelect').val()) {
                        $(this).removeAttr("disabled");
                        $(this).show();
                    }else{
                        $(this).attr('disabled', true);
                        $(this).hide();
                    }
                });
            }

            $( function() {
                refSourceChange();

                $('.style_select').styleActionElement();
            });
        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:BodyNew theme="minimal" type="iframe">

	<form:form method="post" modelAttribute="command" cssStyle="padding: 0px; margin: 0px;">
		<form:errors path="*">
			<msgpt:Information errorMsgs="${messages}" type="error" />
		</form:errors>
		
		<div id="popupHeaderTitle" style="display: none;">
			<span class="titleText">
				<c:out value='${msgpt:getMessage("page.label.touchpoint")} ${not empty command.document.name ? ":" : ""} ${command.document.name}' />
			</span>
		</div>

		<div class="contentTableIframeExtended">
			<div class="contentPanel" style="padding: 20px 40px;">		
				<c:choose>										
					<c:when test="${fn:length(zones) != 0}">							
						<c:set var="disabled" value="false" />
						<msgpt:DataTable id="primaryTable" staticData="true" listHeader='${msgpt:getMessage("page.label.zones.without.datagroup.definition")}'>
							<c:forEach var="currentZone" items="${zones}">
								<msgpt:TableListGroup>
									<!-- Zone  -->
									<msgpt:TableElement label="page.label.zone" >
										<msgpt:TxtFmt maxLength="50">
											<c:out value="${currentZone.friendlyName}" />
										</msgpt:TxtFmt>
									</msgpt:TableElement>
									
									<!-- Data Group -->
									<msgpt:TableElement label="page.label.data.group" >
										<form:select cssClass="inputXXL style_select" path="zonesDataGroupsMap[${currentZone}]" disabled="${disabled}">
											<form:option value="0">-- <fmtSpring:message code="page.label.none"/></form:option>
											<form:option value="-99"><i><fmtSpring:message code="page.label.mixed"/></i></form:option>
									    	<form:options items="${dataGroups}" itemLabel="name" itemValue="id" />
								    	</form:select>
								   </msgpt:TableElement>
											
								</msgpt:TableListGroup>		
							</c:forEach>
						</msgpt:DataTable>
					</c:when>
					<c:otherwise>
						<msgpt:DataTable>
							<msgpt:TableItem>
								<fmtSpring:message code="page.text.no.zone.with.unset.data.group" />
							</msgpt:TableItem>	
						</msgpt:DataTable>
					</c:otherwise>
				</c:choose>
			</div>

            <div class="label">
                <table width="100%" class="formBorderlessTable"><tbody><tr><td align="right" style="padding: 2px 12px;">
                    <!-- Buttons -->
                    <c:choose>
                        <c:when test="${fn:length(zones) != 0 }">
                            <c:set var="vecFormSubmit" value="submit" />
							<msgpt:Button label="page.label.cancel" URL="javascript:closeIframe_local()" />
							<msgpt:Button label="page.label.save" URL="javascript:doSubmit()" primary="true" icon="fa-save" />
                        </c:when>
                        <c:otherwise>
                            <msgpt:Button label="page.label.cancel" URL="javascript:closeIframe_local()" />

                        </c:otherwise>
                    </c:choose>
                </td></tr></tbody></table>
            </div>
		</div>
		

	</form:form>
</msgpt:BodyNew>
</msgpt:Html5>
