<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.model.admin.SystemState"%>
<%@page import="com.prinova.messagepoint.model.admin.EventType" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp"%>

<c:set var="isFilePackage" value="${not empty command.document && command.document.isTemplateControlled && (command.document.isSefasCompositionTouchpoint || command.document.isMPHCSCompositionTouchpoint)}" />

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.admin" viewType="<%= MessagepointHeader.ViewType.EDIT %>">

	<msgpt:CalendarIncludes/>

	<msgpt:Script src="includes/javascript/deliveryevents.js" />
	
	<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />
	
	<msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js" />
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css" />

    <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
    <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

    <msgpt:Script>
        <script>
            function loadTimeSelect() {
                if ($("#startDate").length == 0)
                    return;
                var hours = new Array;
                hours[0] = createOption("--");
                hours[1] = createOption("01");
                hours[2] = createOption("02");
                hours[3] = createOption("03");
                hours[4] = createOption("04");
                hours[5] = createOption("05");
                hours[6] = createOption("06");
                hours[7] = createOption("07");
                hours[8] = createOption("08");
                hours[9] = createOption("09");
                hours[10] = createOption("10");
                hours[11] = createOption("11");
                hours[12] = createOption("12");
                setOptions("time_hours",hours);

                var minutes = new Array;
                minutes[0] = createOption("--");
                minutes[1] = createOption("00");
                minutes[2] = createOption("15");
                minutes[3] = createOption("30");
                minutes[4] = createOption("45");
                setOptions("time_minutes",minutes);

                var am_pm = new Array;
                am_pm[0] = createOption("AM");
                am_pm[1] = createOption("PM");
                setOptions("time_AM_PM",am_pm);

                var rawDateTime = trim(document.getElementById("hiddenDateTimeField").value);
                var dateTimeComponents = rawDateTime.split(' ');

                if ( rawDateTime.indexOf(',') != -1 ) {
                    document.getElementById("startDate").value = dateTimeComponents[0]+" "+dateTimeComponents[1]+" "+dateTimeComponents[2];
                }
                if ( rawDateTime.indexOf(',') != -1 && rawDateTime.indexOf(':') != -1 ) {
                    var time = dateTimeComponents[3];
                    setSelectValue("time_hours", time.split(':')[0]);
                    setSelectValue("time_minutes", time.split(':')[1]);
                    setSelectValue("time_AM_PM", dateTimeComponents[4]);
                } else if ( rawDateTime.indexOf(':') != -1 ) {
                    var time = dateTimeComponents[0];
                    setSelectValue("time_hours", time.split(':')[0]);
                    setSelectValue("time_minutes", time.split(':')[1]);
                    setSelectValue("time_AM_PM", dateTimeComponents[1]);
                }
            }

            function setSelectValue(id, value) {
                var selectElement = document.getElementById(id);
                for (var i=0; i<selectElement.options.length; i++)
                    if (selectElement.options[i].innerHTML == value)
                        selectElement.options[i].selected = 'true';
            }

            function createOption(optionName) {
                var option = new Option(optionName);
                return option;
            }

            function setOptions(id, optionArray) {
                selectElement = document.getElementById(id);
                for (var i=0; i<optionArray.length; i++) {
                    selectElement.options[i] = optionArray[i];
                }
            }

            function timeChange(element) {
                var hour = document.getElementById('time_hours');
                var minutes = document.getElementById('time_minutes');
                if (element.options[element.selectedIndex].innerHTML == '--') {
                    hour.options[0].selected = 'true';
                    minutes.options[0].selected = 'true';
                }
                setTimeDateBindingValue();
            }

            function setTimeDateBindingValue() {
                var time = getTimeValue();
                var date = document.getElementById("startDate").value;
                document.getElementById("hiddenDateTimeField").value = date + " " + time;
            }

            function getTimeValue() {
                var hour = document.getElementById('time_hours');
                var minutes = document.getElementById('time_minutes');
                var am_pm = document.getElementById('time_AM_PM');

                var time = hour.options[hour.selectedIndex].innerHTML + ":" +
                    minutes.options[minutes.selectedIndex].innerHTML + " " +
                    am_pm.options[am_pm.selectedIndex].innerHTML;
                if ( time.indexOf("--") != -1 )
                    time = "";
                return time;
            }

            function trim(inputString) {
                if (typeof inputString != "string") { return inputString; }
                var retValue = inputString;
                var ch = retValue.substring(0, 1);
                while (ch == " ") { // Check for spaces at the beginning of the string
                    retValue = retValue.substring(1, retValue.length);
                    ch = retValue.substring(0, 1);
                }
                ch = retValue.substring(retValue.length-1, retValue.length);
                while (ch == " ") { // Check for spaces at the end of the string
                    retValue = retValue.substring(0, retValue.length-1);
                    ch = retValue.substring(retValue.length-1, retValue.length);
                }
                return retValue;
            }

            $( function() {
                loadTimeSelect();
                removeQueryStringParameter( document.getElementById( 'command'), 'tenId' );
                removeQueryStringParameter( document.getElementById( 'command'), 'tpid' );
            });

            function sendBundleDownloadRequest(jobId) {
                    var reqUrl = context + '/download/data.form?type=bundle_file&bundle_type=diagnostic&job_id=' + jobId +'&cacheStamp=' + stampDate.getTime();
                    javascriptHref(reqUrl);
            }
            
            function toggleDeliveryEventType() {
                if ( $('#flipToggle_delivery_event_type').is(':checked') ) {
                    $('#docSelectBox').selectOptionByAttr('value',0);
                    $('#touchpointSelectContainer').hide();
                    $('#touchpointCollectionSelectContainer').show();
                } else {
                    $('#touchpointCollectionSelectBox').selectOptionByAttr('value',0);
                    $('#touchpointCollectionSelectContainer').hide();
                    $('#touchpointSelectContainer').show();
                }
            }

            function toggleTarget(select) {
                if ( select.selectedIndex != 0 && $(select).attr('id') == 'docSelectBox' ) {
                    $('#touchpointCollectionSelectBox').selectOptionByAttr('value',0);
                } else 	if ( select.selectedIndex != 0 && $(select).attr('id') == 'touchpointCollectionSelectBox' ) {
                    $('#docSelectBox').selectOptionByAttr('value',0);
                }
            }

            function toggleUpdateConnectedProduction() {
                if ( $('#flipToggle_updateConnectedProduction').is(':checked') && ${command.isConnectedTouchpoint} == true )
                    $('.packageSelectContainers').hide();
                else
                    $('.packageSelectContainers').show();
            }

            function dataResourceAddAction(){
                 $('#addDataResourceBtn').iFramePopup({
                     width			: 600,
                     height 			: 400,
                     displayOnInit	: true,
                     screenMask		: false,
                     src				: context+"/dataadmin/data_resource_edit.form",
                     appliedParams	: {tk : "${param.tk}", touchpointContext: true, documentId: "${empty tpCollection ? document.id : '-1'}", collectionId: "${not empty tpCollection ? tpCollection.id : '-1'}"},
                     closeBtnId		: "cancelBtn_button",
                     beforePopupClose: function() {
                         window.location.reload();
                     }
                 });
           }

           function saveOnly(command) {
                $('#saveOnly').val(true);
                doSubmit(command);
           }
                    

            $( function() {

                $('.style_select').styleActionElement();

                if ( $('#flipToggle_delivery_event_type').length != 0 )
                    $('#flipToggle_delivery_event_type').iButton({
                        labelOn: $('#flipToggle_delivery_event_type').attr('title').split(';')[0],
                        labelOff: $('#flipToggle_delivery_event_type').attr('title').split(';')[1],
                        resizeHandle: false,
                        resizeContainer: "auto",
                        change: function() {
                            toggleDeliveryEventType();
                        }
                    });
                $(".flipToggle_updateConnectedProduction").each( function() {
                    var currentBinding = $(this);
                    $(this).iButton({
                        labelOn: $(currentBinding).attr('title').split(';')[0],
                        labelOff: $(currentBinding).attr('title').split(';')[1],
                        resizeHandle: false,
                        resizeContainer: "auto",
                        change: function() {
                            toggleUpdateConnectedProduction();
                        }
                    });
                });
                $(".flipToggle_notification").each( function() {
                    var currentBinding = $(this);
                    $(this).iButton({
                        labelOn: $(currentBinding).attr('title').split(';')[0],
                        labelOff: $(currentBinding).attr('title').split(';')[1],
                        resizeHandle: false,
                        resizeContainer: "auto"
                    });
                });
                $(".flipToggle_resetTPContentChangeFlag").each( function() {
                    var currentBinding = $(this);
                    $(this).iButton({
                        labelOn: $(currentBinding).attr('title').split(';')[0],
                        labelOff: $(currentBinding).attr('title').split(';')[1],
                        resizeHandle: false,
                        resizeContainer: "auto"
                    });
                });
                toggleDeliveryEventType();
                toggleUpdateConnectedProduction();

            });

        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<c:if test="${not empty param.dpeid}">
	<msgpt:GetPagedHibernateObjects 
		hqlFromQuery="from DeliveryEvent de where de.item.id = ${param.dpeid} and de.eventTypeId =4" 
		orderBy="order by de.scheduledTime desc"  
		page="${param.page}" 
		pageSize="${param.pagesize}" 
		maxPages="${param.maxpages}" 
		scope="request" 
		startingItemName="startingItem" 
		endingItemName="endingItem"  
		totalItemsName="totalItems" 
		totalPagesName="totalPages" 
		outputName="deliveryevents" />
</c:if>

<msgpt:BodyNew theme="minimal" type="iframe">
				
	<form:form>
        <form:hidden id="saveOnly" path="saveOnly" />
		<c:if test="${empty param.nprSaveSuccess}">
			<div id="popupHeaderTitle" style="display: none;">
				<span class="titleText">
					<c:out value='${msgpt:getMessage("page.label.edit")} ${msgpt:getMessage("page.label.Delivery")} ${msgpt:getMessage("page.label.event")}' />
				</span>
			</div>

            <msgpt:iFrameContainer>
                <msgpt:ContentPanel>
                    <form:errors path="*">
                        <msgpt:Information errorMsgs="${messages}" type="error" />
                    </form:errors>

                    <msgpt:FormLayout labelWidth="265px">
                        <!-- Touchpoint Collection-->
                        <c:if test="${fn:length(packagedCollections) > 0}">
                            <msgpt:FormField label="page.label.type">
                                <input type="checkbox" id="flipToggle_delivery_event_type" title="${msgpt:getMessage('page.label.collection')};${msgpt:getMessage('page.label.touchpoint')}" ${not empty command.touchpointCollection ? 'checked="checked"' :''} />
                            </msgpt:FormField>
                            <msgpt:FormField label="page.label.touchpoint.collection" id="touchpointCollectionSelectContainer">
                                <div style="position: relative; left: -5px;">
                                    <form:select id="touchpointCollectionSelectBox"
                                                 path="touchpointCollection"
                                                 class="complex-dropdown-select"
                                                 onchange="javascript:toggleTarget(this);javascript:doSubmit('update');"
                                                 title="${msgpt:getMessage('page.label.collection.s')}"
                                                 aria-label="${msgpt:getMessage('page.label.collections')}"
                                                 data-toggle="complex-dropdown"
                                                 data-enablefilter="true"
                                                 data-menu-class="dropdown-menu-left"
                                                 data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                                 data-show-titles="true"
                                                 data-min-children-to-display-filter="1">
                                        <option value="0"><fmtSpring:message code="page.text.select.touchpoint.collection" /></option>
                                        <c:forEach var="touchpointCollection" items="${packagedCollections}">
                                            <option id="touchpointOption_${touchpointCollection.id}"
                                                         class="ml-0" value="${touchpointCollection.id}"
                                                         ${command.touchpointCollection.id == touchpointCollection.id ? 'selected="selected"': ''}>
                                                ${touchpointCollection.name}
                                            </option>
                                        </c:forEach>
                                    </form:select>
                                </div>
                            </msgpt:FormField>
                        </c:if>
                        <!-- Touchpoint -->
                        <msgpt:FormField label="page.label.touchpoint" id="touchpointSelectContainer">
                            <div style="position: relative; left: -5px;">
                                <form:select id="docSelectBox"
                                        path="document"
                                        class="complex-dropdown-select"
                                        onchange="javascript:toggleTarget(this);javascript:doSubmit('update');"
                                        title="${msgpt:getMessage('page.label.touchpoint.s')}"
                                        aria-label="${msgpt:getMessage('page.label.touchpoints')}"
                                        data-toggle="complex-dropdown"
                                        data-enablefilter="true"
                                        data-enable-tag-cloud="true"
                                        data-menu-class="dropdown-menu-left"
                                        data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                        data-show-titles="true"
                                        data-min-children-to-display-filter="1">
                                    <option value="0"><fmtSpring:message code="page.text.select.touchpoint" /></option>
                                    <c:forEach var="currentDocument" items="${touchpoints}">
                                        <option id="touchpointOption_${currentDocument.id}"
                                                class="ml-0 level_${currentDocument.projectDepth}" value="${currentDocument.id}"
                                                data-filtervalue="${currentDocument.metatags}"
                                                ${command.document.id == currentDocument.id ? 'selected="selected"': ''}>
                                                ${currentDocument.name}
                                        </option>
                                    </c:forEach>
                                </form:select>
                            </div>
                        </msgpt:FormField>

                        <!-- Name -->
                        <msgpt:FormField label="page.label.name">
                            <msgpt:FlowLayout>
                                <msgpt:FlowLayoutItem>
                                    <msgpt:InputFilter type="simpleName">
                                        <form:input path="name" cssClass="inputXL" />
                                    </msgpt:InputFilter>
                                </msgpt:FlowLayoutItem>
                                <msgpt:FlowLayoutItem>
                                    <div>
                                        <form:checkbox path="enabled" cssClass="checkbox" />
                                        <fmtSpring:message code="page.label.enabled" />
                                    </div>
                                </msgpt:FlowLayoutItem>
                            </msgpt:FlowLayout>
                        </msgpt:FormField>

                        <c:if test="${hasOnPremPermission}">
                            <msgpt:FormField label="page.label.bundle.delivery">
                                <msgpt:BundleDeliverySelect id="deServerSelect" deServerGuidPath="command.DEServerGuid" filenameOverridePath="command.bundleNameOverride" eventTypeFilter="<%= EventType.TYPE_PRODUCTION %>" />
                            </msgpt:FormField>

                        </c:if>

                        <msgpt:FormField label="page.label.update.staged.production.bundle">
                            <form:checkbox class="flipToggle_updateConnectedProduction" id="flipToggle_updateConnectedProduction" path="updateConnectedProductionBundle" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
                        </msgpt:FormField>

                        <!-- Composition package -->
                        <c:if test="${(command.appliesCompositionFileSet && !command.document.isTemplateControlled) || (isFilePackage && filePackageCount > 0)}">
                            <msgpt:FormField label="${isFilePackage ? 'page.label.bundled.file.package' : 'page.label.bundled.composition.package'}" cssClass="packageSelectContainers">
                                <div style="position: relative; left: -5px;">
                                    <form:select id="compositionPackageSelect" path="compositionFileSetId" cssClass="inputXL style_select">
                                        <form:option value="0">
                                            <fmtSpring:message code="page.label.none" />
                                        </form:option>
                                        <c:forEach var="currentPackage" items="${command.compositionFileSets}">
                                            <form:option value="${currentPackage.id}">
                                                <c:out value="${currentPackage.name}" />
                                                <c:if test="${currentPackage.id == command.defaultCompositionFileSetId}"> (<fmtSpring:message code="page.label.default" />)</c:if>
                                            </form:option>
                                        </c:forEach>
                                    </form:select>
                                </div>
                            </msgpt:FormField>
                        </c:if>
                        
                        <!--  Resource files -->
                        <msgpt:FormField label="page.label.bundled.data.resource" cssClass="packageSelectContainers">
                        	<msgpt:FlowLayout>
                                <msgpt:FlowLayoutItem>
		                            <div style="position: relative; left: -5px;">
		                                <form:select id="dataResourceSelect" path="dataResourceId" cssClass="inputXL style_select">
		                                	<c:if test="${command.defaultDataResourceId <= 0}"> 
			                                    <form:option value="0">
			                                        <fmtSpring:message code="page.label.none" />
			                                    </form:option>
		                                    </c:if>
		                                    <c:forEach var="currentDataResource" items="${dataResourceList}">
		                                        <option value="${currentDataResource.id}" ${currentDataResource.id==command.dataResourceId?'selected="selected"':''}>
		                                            <c:out value="${currentDataResource.name}" />
		                                            <c:if test="${currentDataResource.id == command.defaultDataResourceId}"> (<fmtSpring:message code="page.label.default" />)</c:if>
		                                        </option>
		                                    </c:forEach>
		                                </form:select>
		                            </div>
                            	</msgpt:FlowLayoutItem>
                                <msgpt:FlowLayoutItem>
	                            	<div  align="left">
										<!-- BUTTON: ADD DATA RESOURCE -->
										<msgpt:IfAuthGranted authority="ROLE_TOUCHPOINT_DATA_EDIT" >
											<div id="addDataResourceBtn" class="addDataResourceBtn actionBtn_roundAll actionBtn detailTip fa-mp-container" style="white-space: nowrap; height: 38px;" onclick="javascript:dataResourceAddAction();" title="|<div class='detailTipText'>${msgpt:getMessage('page.text.add.data.resource')}</div>">
												<i class="fa fa-plus fa-mp-btn"></i>
											</div>
										</msgpt:IfAuthGranted>
									</div>
								</msgpt:FlowLayoutItem>
							</msgpt:FlowLayout>
                        </msgpt:FormField>
                            
                        <!-- GUID -->
                        <c:if test="${not empty command.guid}">
                            <msgpt:FormField label="page.label.guid">
                                <c:out value="${command.guid}" />
                            </msgpt:FormField>
                        </c:if>
                        <!-- Email Notification on delivery event failure -->
                        <msgpt:FormField label="page.label.receive.notification.on.deliveryevent.failure">
                            <form:checkbox class="flipToggle_notification" id="flipToggle_notification" path="receiveFailureNotification" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
                        </msgpt:FormField>

                        <!-- Reset tpContentChanged Flag (relevant in case of touchpoint only) -->
                        <c:if test="${not empty command.document}">
                            <msgpt:FormField label="page.label.reset.tp.content.changed.flag">
                                <form:checkbox class="flipToggle_resetTPContentChangeFlag" id="flipToggle_resetTPContentChangeFlag" path="resetTPContentChangedFlag" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
                            </msgpt:FormField>
                        </c:if>
                        <!-- Production Run Type -->
                        <msgpt:FormField label="page.label.touchpoint.deliveryevent.production.runtype" >
                            <c:forEach var="aType"	items="${deliveryEventTypes}">
                                <form:radiobutton path="eventTypeId"  value="${aType.id}" cssClass="radioBtn" onclick="javascript:doSubmit('update');"/>
                                <fmtSpring:message code="${aType}"/>
                                <br>
                            </c:forEach>
                        </msgpt:FormField>

                        <c:if test="${command.eventTypeId == '3'}">
                            <msgpt:FormField label="page.label.start.date">
                                <msgpt:Calendar id="startDate" onchange="javascript:setTimeDateBindingValue()" viewableDateFormat="${viewableDateFormat}" />
                                <div>
                                    <select id="time_hours" class="selectTime" onchange="javascript:timeChange(this)"></select>&nbsp<b>:</b>
                                    <select id="time_minutes" class="selectTime" onchange="javascript:timeChange(this)"></select>
                                    <select id="time_AM_PM" class="selectTime" onchange="javascript:timeChange(this)"></select>
                                </div>
                                <form:hidden id="hiddenDateTimeField" path="startDate" />
                            </msgpt:FormField>
                            <!-- Repeating Events -->
                            <msgpt:FormField label="page.label.repeatingevents">
                                <form:checkbox path="repeat" cssClass="radioBtn" onclick="javascript:doSubmit('update');" />
                            </msgpt:FormField>
                        </c:if>
                        <c:if test="${command.repeat && command.eventTypeId == '3'}">
                            <!-- End Date -->
                            <msgpt:FormField label="page.label.end.date">
                                <msgpt:Calendar path="endDate" viewableDateFormat="${viewableDateFormat}" />
                            </msgpt:FormField>
                            <!-- Frequency -->
                            <msgpt:FormField label="page.label.frequency">
                                <c:forEach var="aFrequency" items="${frequencyTypes}">
                                    <form:radiobutton path="frequencyTypeId"  value="${aFrequency.id}" cssClass="radioBtn" onclick="javascript:doSubmit('update');"/>
                                    <fmtSpring:message code="${aFrequency}"/>
                                    <br>
                                </c:forEach>
                            </msgpt:FormField>
                            <!-- Days of the Week -->
                            <c:if test="${command.frequencyTypeId == 1}">
                                <msgpt:FormField label="page.label.daysofweek">
                                    <c:forEach var="aDailyFrequency"
                                               items="${dailyFrequencyTypes}">
                                        <form:radiobutton path="dailyTypeId" value="${aDailyFrequency.id}" cssClass="radioBtn" />
                                        <fmtSpring:message code="${aDailyFrequency}"/>
                                        <br>
                                    </c:forEach>
                                </msgpt:FormField>
                            </c:if>
                        </c:if>
                    </msgpt:FormLayout>

                    <!-- Pagination -->
                    <c:if test="${not empty deliveryevents}">
                        <msgpt:OutputPageNavigation page="${param.page}" pageSize="${param.pagesize}" maxPages="${param.maxpages}">
                            <msgpt:URLBuilder page="/tpadmin/deliveryevent_edit.form?dpeid=${param.dpeid}&edit=${param.edit}"/>
                        </msgpt:OutputPageNavigation>
                    </c:if>

                    <c:choose>
                        <c:when test="${empty deliveryevents}">
                            <!-- No Delivery Events -->
                            <div style="margin: 10px 5px; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
                                <fmtSpring:message code="page.label.touchpoint.no.deliveryeventhistory.found" />
                            </div>
                        </c:when>
                        <c:otherwise>
                            <h2><c:out value="${msgpt:getMessage('page.label.touchpoint.deliveryeventhistory')}" /></h2>
                            <!-- Event History -->
                            <msgpt:DataTable staticData="true">
                                <c:forEach var="event" items="${deliveryevents}">
                                    <c:if test="${event.eventTypeId == 4}">
                                        <msgpt:TableListGroup>
                                            <!-- Scheduled Run Time -->
                                            <msgpt:TableElement label="page.label.touchpoint.deliveryevent.scheduledruntime">
                                                <fmtJSTL:formatDate value="${event.scheduledTime}" pattern="${dateTimeFormat}" />
                                            </msgpt:TableElement>
                                            <!-- Logs -->
                                            <msgpt:TableElement label="page.label.logs">
                                                <c:if test="${ event.isCompletedStateExist}">
                                                    <a href="javascript:popItUpWithDimensions('../includes/delivery_event_logs.jsp?id=${event.item.id}&deliveryid=${event.id}&class=${event.item.class.name}', 720, 480);"><fmtSpring:message code="page.label.logs"/>	</a>&nbsp;
                                                </c:if>
                                            </msgpt:TableElement>
                                            <!-- full log -->
                                            <msgpt:TableElement label="client_messages.text.result">
                                                <c:if test="${not empty demap[event.job.id]}">
                                                	<a href="javascript:sendBundleDownloadRequest(${ event.job.id });"><i class="fa icon fa-cloud-download" aria-hidden="true"></i></a>
                                                </c:if>
                                            </msgpt:TableElement>
                                            <!-- Job ID -->
                                            <msgpt:TableElement label="page.label.touchpoint.deliveryevent.jobid">
                                                <c:out value ="${ event.job.id }"/>
                                            </msgpt:TableElement>
                                            <!-- Delivery Status -->
                                            <msgpt:TableElement label="page.label.touchpoint.deliveryevent.deliverystatus">
                                                <c:out value="${event.statusDisplayString}"/>
                                            </msgpt:TableElement>
                                            <!-- Event Creation Time -->
                                            <msgpt:TableElement label="page.label.touchpoint.deliveryevent.creationtime">
                                                <fmtJSTL:formatDate value="${event.created}" pattern="${dateTimeFormat}" />
                                            </msgpt:TableElement>
                                            <!-- Last Updated Time -->
                                            <msgpt:TableElement label="page.label.touchpoint.deliveryevent.updatetime">
                                                <fmtJSTL:formatDate value="${event.updated}" pattern="${dateTimeFormat}" />
                                            </msgpt:TableElement>
                                            <!-- Execution Times -->
                                            <msgpt:TableElement label="client_messages.text.execution.execution_times">
                                                <c:if test="${ event.isCompletedStateExist}">
                                                    <a href="javascript:popItUpWithDimensions('../includes/job_execution_times.jsp?id=${event.item.id}&deliveryid=${event.id}&class=${event.item.class.name}', 720, 480);"><fmtSpring:message code="page.label.times"/>	</a>
                                                    &nbsp;
                                                </c:if>
                                            </msgpt:TableElement>
                                            
                                        </msgpt:TableListGroup>
                                    </c:if>
                                </c:forEach>
                            </msgpt:DataTable>
                        </c:otherwise>
                    </c:choose>

                    <!-- Pagination -->
                    <c:if test="${not empty deliveryevents}">
                        <msgpt:OutputPageNavigation page="${param.page}" pageSize="${param.pagesize}" maxPages="${param.maxpages}">
                            <msgpt:URLBuilder page="/tpadmin/deliveryevent_edit.form?dpeid=${param.dpeid}&edit=${param.edit}"/>
                        </msgpt:OutputPageNavigation>
                    </c:if>

                    <input type="hidden" name="submitType" />

                    <msgpt:FlowLayout align="right" style="margin-top:16px" cssClass="fixedButtonBar">
                        <msgpt:FlowLayoutItem>
                            <msgpt:Button URL="javascript:doSubmit('process');" label="page.label.save.continue" primary="true" icon="fa-save" />
                        </msgpt:FlowLayoutItem>
                        <c:if test="${command.eventTypeId == '1'}">
                            <msgpt:FlowLayoutItem>
                                <msgpt:Button URL="javascript:saveOnly('process')" label="page.label.save" icon="fa-save" />
                            </msgpt:FlowLayoutItem>
                        </c:if>
                        <msgpt:FlowLayoutItem>
                            <msgpt:Button URL="javascript:closeIframe()" label="page.label.cancel" />
                        </msgpt:FlowLayoutItem>
                    </msgpt:FlowLayout>
                </msgpt:ContentPanel>

            </msgpt:iFrameContainer>

		</c:if>	
	</form:form>
</msgpt:BodyNew>
</msgpt:Html5>