<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.touchpoints" extendedScripts="true"
                     viewType="<%= MessagepointHeader.ViewType.EDIT %>">


        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>

        <msgpt:Script src="includes/javascript/documentWidget.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tableActions/jquery.tableActions.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tableActions/tableActions.css"/>

        <msgpt:Script src="includes/javascript/popupActions.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/sectionImagePopup/jquery.sectionImagePopup.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>

        <style>
            .stageHeader .stageLabel, .stageHeader .stageActions {
                display: inline-block;
            }

            .actionBarButtonsContainer {
                margin-top: 8px;
                margin-right: 8px;
            }

            .popupContentWidth {
                width: 600px;
            }
        </style>

        <msgpt:Script>
            <script>
                var popupLock = false;

                function clicked(zone) {

                }

                function actionSelected_localHandler(selectEle) {
                    var actionId = $(selectEle).find(':selected').attr('id').replace('actionOption_', '');
                    if (actionId == "5") {
                        var packagePath = $(selectEle).find(':selected').val();
                        javascriptHref(context + '/download/zip.form?file=' + packagePath + '&type=tp_template&cacheStamp=' + cacheStamp);
                    } else if (actionId == '33') {
                        $('#actionOption_' + actionId).iFramePopup({
                            width: 900,
                            src: context + "/tpadmin/template_variants_edit.form",
                            displayOnInit: true,
                            appliedParams: {'tk': "${param.tk}", 'documentId': "${param.docid}"},
                            beforePopupClose: function () {
                                location.reload();
                            },
                            onSave: function () {
                            }
                        });
                    } else if (actionId == '23') {
                        $('#actionOption_' + actionId).iFramePopup($.extend({
                            width: 1020,
                            src: context + "/tpadmin/tp_access_control.form",
                            displayOnInit: true,
                            id: "tpAccessFrame",
                            appliedParams: {'tk': "${param.tk}", 'docid': "${param.docid}"},
                            beforePopupClose: function () {
                                getTopFrame().location.reload();
                            }
                        }, iFramePopup_fullFrameAttr));
                    } else if (actionId == '10') {
                        // ADD CHANNEL
                        createTouchpointAction(getParam('docid'))
                    } else if (actionId == '19') {
                        // Update Touchpoint from XML
                        javascriptHref(context + '/tpadmin/touchpoint_import.form?tk=${param.tk}&updatedocid=${param.docid}');
                    } else {
                        actionSelected(selectEle);
                    }
                }

                function submitPreAction(submitId) {
                    return false;
                }

                function reportingDataOnSave() {
                    document.getElementById('extendedReportingDataIFrame').contentWindow.location.reload(true);
                    return true;
                }

                // Sync Project
                function syncProjectAction(documentId) {
                    $('#syncProjectBtn').iFramePopup($.extend(iFramePopup_fullFrameAttr, {
                        id: "touchpoint_sync_project",
                        width: 1080,
                        src: context + "/tpadmin/touchpoint_sync_project.form",
                        displayOnInit: true,
                        applySnapOffToggle: true,
                        appliedParams: {'tk': "${param.tk}", 'documentId': documentId},
                        beforePopupClose: function (popup) {
                            var $f = $("#iFramePopup_" + popup.data.frameId + " iframe.iFramePopupFrame", getTopFrame().document);
                            if ($f.length > 0) {
                                var cw = $f[0].contentWindow || $f[0].contentDocument;
                                if ($.isFunction(cw.clearLongOperationHandle)) cw.clearLongOperationHandle();
                            }
                            return true;
                        },
                        afterPopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }));
                }

                // Data Collection
                function dataSourceAssociationAction(documentId) {
                    $('#editDataAssoBtn').iFramePopup($.extend({
                        src: context + "/tpadmin/touchpoint_data_source_configuration_edit.form",
                        displayOnInit: true,
                        appliedParams: {'tk': "${param.tk}", 'documentId': documentId},
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr));
                }

                // Template Modifier Setup
                function templateModifierSetupAction(documentId) {
                    $('#editTemplateModifierBtn').iFramePopup($.extend({
                        src: context + "/tpadmin/touchpoint_template_modifier_list.form",
                        displayOnInit: true,
                        appliedParams: {'tk': "${param.tk}", 'documentId': documentId},
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr));
                }

                // Variable Mapping Resolution
                function variableMappingResolveAction(documentId) {
                    var variableIdList = "";
                    if ($('#variableMapLinkBtnVariableIds').length != 0) {
                        variableIdList = $('#variableMapLinkBtnVariableIds').val();
                    }

                    var dataSourceIdList = "";
                    if ($('#variableMapLinkBtnDataSourceIds').length != 0) {
                        dataSourceIdList = $('#variableMapLinkBtnDataSourceIds').val();
                    }

                    $('#editVariableMappingBtn').iFramePopup($.extend({
                        src: context + "/dataadmin/bridge_variables.form",
                        displayOnInit: true,
                        appliedParams: {
                            'tk': "${param.tk}",
                            'documentId': documentId,
                            'variableIds': variableIdList,
                            'dataSourceIds': dataSourceIdList
                        },
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr));
                }

                // Variable All Mapping Resolution
                function variableAllMappingResolveAction(documentId) {
                    var variableIdList = "";
                    if ($('#variableMapLinkBtnVariableIds').length != 0) {
                        variableIdList = $('#variableMapLinkBtnVariableIds').val();
                    }

                    var dataSourceIdList = "";
                    if ($('#variableMapLinkBtnDataSourceIds').length != 0) {
                        dataSourceIdList = $('#variableMapLinkBtnDataSourceIds').val();
                    }

                    $('#editVariableAllMappingBtn').iFramePopup($.extend({
                        src: context + "/dataadmin/bridge_variables.form",
                        displayOnInit: true,
                        appliedParams: {
                            'tk': "${param.tk}",
                            'documentId': documentId,
                            'dataSourceIds': dataSourceIdList
                        },
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr));
                }

                // Zone Resolve
                function dataZoneResolveAction(documentId) {
                    $('#editZoneDataGroupBtn').iFramePopup($.extend({
                        src: context + "/tpadmin/touchpoint_zone_data_group_bulk_edit.form",
                        displayOnInit: true,
                        appliedParams: {'tk': "${param.tk}", 'docid': documentId},
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr));
                }

                // Language List Validation
                function validateLanguageActionReq(languageId) {
                    var singleSelect = true;
                    var canDelete = true;

                    // Resolve selection flags
                    if ($("input[id^='languageListItemCheck_']:checked").length != 1)
                        singleSelect = false;
                    $("input[id^='languageListItemCheck_']:checked").each(
                        function () {
                            var languageId = this.id.replace('languageListItemCheck_', '');
                            if (!exists('canDelete_language_' + languageId))
                                canDelete = false;
                        }
                    );

                    // Disable all actions
                    $('#languageUpdateBtn').disableElement();
                    $("#languagesActionMenu").disableAllOptions();

                    if (singleSelect) {
                        $('#languageUpdateBtn').enableElement(); // Update
                        $('#languagesActionMenu').enableOption('actionOption_24');	// Customize locale
                        $('#languagesActionMenu').enableOption('actionOption_25');	// Restore locale
                        if (canDelete) {
                            $('#languagesActionMenu').enableOption('actionOption_14');  // Delete language
                            $('#languagesActionMenu').enableOption('actionOption_18');  // Make default language
                        }
                    }
                    //if ('${fn:length(command.languageList)}' < '9')                 // Maximum 9 languages for one TP
                    $('#languagesActionMenu').enableOption('actionOption_12');  // Add language
                    $('#languagesActionMenu').enableOption('actionOption_26');	// Repair
                }

                function languageActionSelected(select) {
                    var actionId = parseId($(select).find('option:selected'));
                    if (actionId == 12)
                        $(select).iFramePopup({
                            width: 300,
                            displayOnInit: true,
                            id: "languageAddFrame",
                            title: client_messages.title.add_language,
                            src: "touchpoint_language_edit.form",
                            appliedParams: {docid: getParam('docid'), tk: "${param.tk}"},
                            closeBtnId: "link_" + client_messages.text.cancel,
                            onSave: function () {
                                getTopFrame().location.reload();
                            }
                        });
                    else if (actionId == 24)
                        $(select).iFramePopup({
                            src: context + "/admin/locale_settings_edit.form",
                            displayOnInit: true,
                            appliedParams: {
                                touchpointLanguageId: parseId($("[id^='languageListItemCheck_']:checked:first")),
                                tk: "${param.tk}"
                            },
                            title: client_messages.title.customize_locale,
                            id: "customizeLocaleFrame",
                            closeBtnId: "link_" + client_messages.text.cancel,
                            width: 976,
                            vertOffset: -100,
                            onSave: function () {
                                getTopFrame().location.reload();
                            }
                        });
                    else if (actionId = 14)
                        actionSelected(select);

                }

                function languageIframeAction(languageId) {
                    var langId = languageId;
                    if (languageId == undefined) {
                        $("input[id^='languageListItemCheck_']:checked").each(function () {
                            langId = this.id.replace('languageListItemCheck_', '');
                        });
                    }

                    $('#languageLink_' + langId).iFramePopup({
                        width: 300,
                        displayOnInit: true,
                        id: "languageEditFrame",
                        title: client_messages.title.edit_language,
                        src: "touchpoint_language_edit.form",
                        appliedParams: {
                            docid: getParam('docid'),
                            languageHash: $('#languageLink_' + langId).attr('langHash'),
                            tk: "${param.tk}"
                        },
                        closeBtnId: "link_" + client_messages.text.cancel,
                        onSave: function () {
                            getTopFrame().location.reload();
                        }
                    });
                }


                // Attachment Management List Validation
                function validateAttachmentManagementActionReq(attachmentId) {
                    var singleSelect = true;

                    // Resolve selection flags
                    if ($("input[id^='attachmentListItemCheck_']:checked").length != 1)
                        singleSelect = false;

                    // Disable all actions
                    $('#attachmentMgtUpdateBtn').disableElement();
                    $("#attachmentMgtActionMenu").disableAllOptions();

                    if ($("input[id^='attachmentListItemCheck_']:checked").length > 0) {
                        $('#attachmentMgtActionMenu').enableOption('actionOption_31');  // Delete attachment
                        if (singleSelect) {
                            $('#attachmentMgtUpdateBtn').enableElement();
                        }
                    }
                    $('#attachmentMgtActionMenu').enableOption('actionOption_30');  // Add attachment
                }


                // Composition package List Validation
                function validateCompositionPackageActionReq(compositionPackageId) {
                    var singleSelect = true;

                    // Resolve selection flags
                    if ($("input[id^='compositionPackageListItemCheck_']:checked").length != 1)
                        singleSelect = false;

                    // Disable all actions
                    $('#compositionPackageUpdateBtn').disableElement();
                    $("#compositionPackageActionMenu").disableAllOptions();

                    if ($("input[id^='compositionPackageListItemCheck_']:checked").length > 0) {
                        $('#compositionPackageActionMenu').enableOption('actionOption_21');  // Delete composition package
                        if (singleSelect) {
                            $('#compositionPackageUpdateBtn').enableElement();
                            $('#compositionPackageActionMenu').enableOption('actionOption_27'); // Make default composition package
                        }
                    }
                    $('#compositionPackageActionMenu').enableOption('actionOption_20');  // Add composition package
                }

                function compositionPackageAction(ele) {

                    if ($(ele).attr('id') == 'compositionPackageUpdateBtn') {

                        var packageId = 0;
                        $("input[id^='compositionPackageListItemCheck_']:checked").each(function () {
                            packageId = this.id.replace('compositionPackageListItemCheck_', '');
                        });

                        $(ele).iFramePopup({
                            width: 820,
                            displayOnInit: true,
                            title: ${isFilePackage ? 'client_messages.title.edit_file_package' : 'client_messages.title.edit_composition_package' },
                            src: "composition_files_upload.form",
                            appliedParams: {
                                documentId: "${param.docid}",
                                connectorType: "${currentChannelDocument.connectorConfiguration.connector.id}",
                                compositionFileSetId: packageId,
                                tk: "${param.tk}"
                            },
                            closeBtnId: "fileUploadButton_close",
                            beforePopupClose: function () {
                                getTopFrame().location.reload();
                            }
                        });
                        return;
                    }

                    var actionId = parseId($(ele).find('option:selected'));
                    if (actionId == 20)
                        $(ele).iFramePopup({
                            id: "filePackageAddFrame",
                            width: 820,
                            displayOnInit: true,
                            title: ${isFilePackage ? 'client_messages.title.add_file_package' : 'client_messages.title.add_composition_package' },
                            src: "composition_files_upload.form",
                            appliedParams: {
                                documentId: "${param.docid}",
                                connectorType: "${currentChannelDocument.connectorConfiguration.connector.id}",
                                tk: "${param.tk}"
                            },
                            closeBtnId: "fileUploadButton_close",
                            beforePopupClose: function () {
                                getTopFrame().location.reload();
                            }
                        });
                    else if (actionId = 21)
                        actionSelected(ele);
                }

                function attachmentMgtAction(ele) {

                    if ($(ele).attr('id') == 'attachmentMgtUpdateBtn') {

                        var attachmentId = 0;
                        $("input[id^='attachmentListItemCheck_']:checked").each(function () {
                            attachmentId = this.id.replace('attachmentListItemCheck_', '');

                            //Edit
                            var attachmentParam = attachmentId !== 0 ? "&attachmentId=" + attachmentId : "";
                            btnJavascriptHref('${contextPath}/attachment/attachment_overview_edit.form?documentId=${param.docid}'+attachmentParam,this);
                        });
                        return;
                    }

                    var actionId = parseId($(ele).find('option:selected'));
                    if (actionId == 30)     // Add
                        btnJavascriptHref('${contextPath}/attachment/attachment_overview_edit.form?documentId=${param.docid}',this);
                    else if (actionId = 31)     //Delete
                        actionSelected(ele);
                }

                function iFrameView(path, eleId, e) {
                    if (e != undefined) {
                        if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
                            $("#" + eleId).iFramePopup($.extend({
                                src: path,
                                displayOnInit: true,
                                appliedParams: {tk: "${param.tk}"},
                                onSave: function () {
                                },
                                beforePopupClose: function () {
                                    cleariFramePopupParams();
                                }
                            }, iFramePopup_fullFrameAttr));
                        }
                        e.preventDefault ? e.stopPropagation() : e.returnValue = false;
                    }
                }

                function cleariFramePopupParams() {
                    var currentURL = window.document.URL;
                    currentURL = removeParam(currentURL, "zonedatagroupbulkedit");
                    currentURL = removeParam(currentURL, "datasourceconfigedit");
                    getTopFrame().location.href = currentURL;
                }

                function changeTpRemoveChecks() {
                    if ($('input.removeTpCheckbox:not(:checked)').length > 0) {
                        $('#removeBtnEnabled').hide();
                        $('#removeBtnDisabled').show();
                    } else {
                        $('#removeBtnEnabled').show();
                        $('#removeBtnDisabled').hide();
                    }
                }

                function instanceSelection() {
                    var nodeId = $('#instanceSelect').val();
                    var documentId = ${param.docid};
                    var type = 'cloneFromInstance';

                    var stampDate = new Date();
                    $.ajax({
                        type: "GET",
                        url: context + "/getDataForSelectMenu.form?contentObjectId=" + nodeId + "&type=" + type + "&documentId=" + documentId + "&cacheStamp=" + (stampDate.getTime()),
                        dataType: "xml",
                        success: function (data) {
                            initDataSelect(data);
                        }
                    });
                }

                function actionCancel_Local() {
                    actionCancel();
                }

                function initAttachmentPopup() {
                    $("#attachmentMgtTable").find('.attachmentDetailsPopup').each(function () {
                        // Content:  Init content popup
                        var instId = parseId($(this));

                        $(this).closest('td').contentPopup({
                            trigger: "dual",
                            contentType: 'attachmentMgtDetails',
                            contentItemId: instId,
                            popupLocation: 'right',
                            fnBeforeContentRequest: function (o) {
                                o.data.contentLocaleId = 0;
                            },
                            fnExtServerParams: function (o) {
                                var obj = [
                                    {
                                        "name": "documentId",
                                        "value": getParam('docid') != "" ? getParam('docid') : -1
                                    }
                                ];
                                return obj;
                            },
                            maxWidth: 600,
                            maxHeight: 300
                        });
                    });
                }

                function initAttachmentTgtDetailsPopup() {
                    $("#attachmentMgtTable").find('.attachmentTgtDetailsPopup').each(function () {
                        // Targeting:  Init targeting popup
                        var attachmentId = parseId($(this));
                        var stampDate = new Date();
                        $(this).closest('td').popupFactory({
                            title: client_messages.title.targeting,
                            popupLocation: "left",
                            width: 250,
                            asyncDataType: "xml",
                            asyncSetContentURL: context + "/getTargeting.form?type=summary&attachmentId=" + attachmentId + "&cacheStamp=" + (stampDate.getTime()),
                            asyncSetContentHandler: function (o, data) {
                                if ($(data).find("content").length > 0)
                                    return "<div align=\"left\" style=\"padding: 6px 12px; font-size: 10px;\">" +
                                        $(data).find("content").text() +
                                        "</div>";
                                else
                                    return "<div>Error: " + client_messages.text.bad_request_for_targeting_summary + "</div>";
                            }
                        });
                    });
                }

                $(function () {
                    $("input:button").not("#updateBtn_Edit").styleActionElement();
                    $("#updateBtn_Edit").styleActionElement({highlighted: true});
                    $("#actionMenu").styleActionElement();
                    $("#languagesActionMenu").styleActionElement();
                    $("#attachmentMgtActionMenu").styleActionElement();
                    $("#compositionPackageActionMenu").styleActionElement();
                    $("#backgroundTasks").styleActionElement();
                    $("div#actionPopupCloneTouchpointOptions div#languagesSelect_${document.id}").styleActionElement();
                    $("div#actionPopupCloneTouchpointOptions div#cloneOptions").styleActionElement();

                    var cloneTargetInstancePrevValue;

                    changeTpRemoveChecks();
                    common.setFixedSidebar($('#allZoneDivs'), 32);

                    $('#instanceSelect').on('focus', function () {
                        cloneTargetInstancePrevValue = this.value;
                    }).change(function () {
                        var targetInstanceLocale = $('#instanceSelect :selected').attr('locale');
                        var currentInstanceLocale = "${currentInstance.systemDefaultLocale.code}";

                        if (targetInstanceLocale === currentInstanceLocale) {
                            $('#actionPopupErrorFrame').hide();
                            instanceSelection();
                        } else {
                            $('#actionPopupErrorFrame').show();
                            $('#actionPopupErrorFrame').show();
                            this.value = cloneTargetInstancePrevValue;
                        }

                        var targetInstanceId = $('#instanceSelect').val();
                        if(targetInstanceId == "${currentInstance.id}") {
                            $('#cloneOptionsList').hide();
                        }
                        else {
                            $('#cloneOptionsList').show();
                        }
                    });

                    $('#extReportingDataEditBtn_button').iFramePopup({
                        width: 820,
                        title: client_messages.title.external_reporting_data,
                        src: "document_extended_reporting_variables_edit.form",
                        appliedParams: {documentId: "${param.docid}", tk: "${param.tk}"},
                        closeBtnId: "cancelBtn_button",
                        onSave: function () {
                            reportingDataOnSave();
                        }
                    });

                    validateLanguageActionReq();
                    validateCompositionPackageActionReq();
                    instanceSelection();

                    $("#languagesTable").tableActions({defaultRows: 4});
                    $("#compositionPackagesTable").tableActions({defaultRows: 4});

                    $(document).bind('drop dragover', function (e) {
                        e.preventDefault();
                    });

                    $("[id*='taskInProcess']").actionStatusPolling({
                        itemLabel: client_messages.text.background_task,
                        type: 'backgroundtask',
                        postItemInit: function (item) {
                            var menuEleId = $(item).closest('select').attr('id') + '_menuTable';
                            if ($('#' + menuEleId).find("[id*='taskInProcess']").length == 0)
                                $('#' + menuEleId).find('.menuItemInProcessIcon,.menuItemInProcessSpacer').remove();
                        },
                        onInit: function (o) {
                            if ($(o.option).closest('.actionBtnTable').find('.actionBtnText').find('.menuItemInProcessIcon').length == 0)
                                $(o.option).closest('.actionBtnTable').find('.actionBtnText').prepend("<div class=\"menuItemInProcessIcon\" style=\"display: inline-block; position: absolute;\"></div><div class=\"menuItemInProcessSpacer\" style=\"display: inline-block; width: 22px; height: 1px;\"></div>");
                        }
                    });

                    $("[id^='compositionPackage_']").each(function () {

                        var configFileName = $(this).attr('configurationFileName');
                        var templateFileName = $(this).attr('templateFileName');
                        $(this).popupFactory({
                            title: $(this).attr('packageName'),
                            popupLocation: "right",
                            trigger: "hover",
                            fnSetContent: function (o) {
                                return "<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" +
                                    "<div style=\"word-wrap: break-word;\">" + client_messages.text.package_file + ":" + configFileName + "</div>" +
                                    "<div style=\"word-wrap: break-word;\">" + client_messages.text.template_file + ":" + templateFileName + "</div>" +
                                    "</div>";
                            }
                        });

                    });

                    initAttachmentPopup();
                    initAttachmentTgtDetailsPopup();

                    $("div#downloadImportedXmlBtn").click(function () {
                        var documentHistoryId = $(this).attr("dh");
                        javascriptHref(context + '/download/xml.form?document_history=' + documentHistoryId + '&type=importedxml&cacheStamp=' + cacheStamp);
                    });

                    $("div#downloadDocumentOriginationLogBtn").click(function () {
                        var documentHistoryId = $(this).attr("dh");
                        javascriptHref(context + '/download/log.form?document_history=' + documentHistoryId + '&type=doc_origination_log&cacheStamp=' + cacheStamp);
                    });

                    $("div#downloadTouchpointXSDBtn").click(function () {
                        var xsdFileName = $("#touchpointXsdName").val();
                        if(xsdFileName.length == 0) {
                            javascriptHref(context + '/download/xml.form?type=touchpoint_export_xsd&cacheStamp=' + cacheStamp);
                        }
                        else {
                            javascriptHref(context + '/download/xml.form?type=touchpoint_export_xsd&filename=' + encodeURIComponent(xsdFileName) + '&cacheStamp=' + cacheStamp);
                        }
                    });

                    if (getParam('zonedatagroupbulkedit') && getParam('zonedatagroupbulkedit') != "")
                        iFrameView(context + '/tpadmin/touchpoint_zone_data_group_bulk_edit.form?docid=' + getParam('docid'), 'updateBtn', 'null');
                    else if (getParam('datasourceconfigedit') && getParam('datasourceconfigedit') != "")
                        iFrameView(context + '/tpadmin/touchpoint_data_source_configuration_edit.form?documentId=' + getParam('docid'), 'updateBtn', 'null');
                });

            </script>
        </msgpt:Script>

    </msgpt:HeaderNew>


    <msgpt:BodyNew theme="minimal">

        <input type="hidden" id="docid" value="${param.docid}"/>

        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
        <msgpt:ContextBarNew languageContextApplied="false" touchpointSetupContext="true" touchpointAdminContext="true"
                             channelContextApplied="true"/>

        <msgpt:LowerContainer fullPanel="true" extendedWidth="true">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="true">

                <form:form modelAttribute="command" enctype="multipart/form-data">

                    <form:hidden path="actionValue" id="actionElement"/>

                    <c:if test="${not noTouchpoints && not trashTpContext}">

                        <h5 class="mb-4">
                            <c:set var="contentPanelTitle" value="${msgpt:getMessage('page.label.setup.touchpoint')}"/>
                            <c:if test="${not noTouchpoints}">
                                <c:choose>
                                    <c:when test="${isExchangeNode}">
                                        <c:set var="contentPanelTitle"
                                               value="${msgpt:getMessage('page.label.setup.touchpoint')} - ${document.name}&nbsp;&nbsp;&nbsp;[${msgpt:getMessage( document.enabled ? 'page.label.enabled' : 'page.label.disabled' )}]&nbsp;&nbsp;[${msgpt:getMessage( document.published ? 'page.label.published' : 'page.label.unpublished' )}]"/>
                                    </c:when>
                                    <c:otherwise>
                                        <c:set var="contentPanelTitle"
                                               value="${msgpt:getMessage('page.label.setup.touchpoint')} - ${document.name}&nbsp;&nbsp;&nbsp;[${msgpt:getMessage( document.enabled ? 'page.label.enabled' : 'page.label.disabled' )}]"/>
                                    </c:otherwise>
                                </c:choose>
                            </c:if>
                            <c:out value="${contentPanelTitle}" escapeXml="false"/>
                        </h5>

                        <!-- START - ERROR/MESSAGES -->
                        <form:errors path="*">
                            <msgpt:Information errorMsgs="${messages}" type="error"/>
                        </form:errors>

                        <c:if test="${(currentChannelDocument.isEmailTouchpoint || currentChannelDocument.isWebTouchpoint)}">
                            <c:if test="${not currentChannelDocument.stripoEnabled && not templatePackageExists}">
                                <msgpt:Information type="warning">
                                    <fmtSpring:message code="page.text.warning.no.template.package.for.touchpoint"/>
                                </msgpt:Information>
                            </c:if>
                            <c:if test="${currentChannelDocument.stripoEnabled && not stripoTemplateExists}">
                                <msgpt:Information type="warning">
                                    <fmtSpring:message code="page.text.warning.no.stripo.template.for.touchpoint"/>
                                </msgpt:Information>
                            </c:if>
                        </c:if>
                        <c:if test="${document.dataSourceAssociation == null}">
                            <msgpt:Information type="warning">
                                <fmtSpring:message code="page.text.warning.no.data.collection.for.touchpoint"/>
                            </msgpt:Information>
                        </c:if>
                        <c:if test="${document.enabledForVariantWorkflow && document.masterTouchpointSelection.workflow == null}">
                            <msgpt:Information type="warning">
                                <fmtSpring:message code="page.text.warning.no.master.variant.workflow.assignment"/>
                            </msgpt:Information>
                        </c:if>
                        <c:if test="${not (noTouchpoints and not trashTpContext) and not hasDefaultTouchpointLanguage}">
                            <msgpt:Information type="error">
                                <fmtSpring:message code="page.label.touchpoint.default.language.not_found"/>
                            </msgpt:Information>
                        </c:if>
                        <msgpt:IfAuthGranted authority="ROLE_TOUCHPOINT_DATA_EDIT">
                            <c:if test="${not empty documentZoneDataGroupMissing && document.dataSourceAssociation != null}">
                                <!-- Cross Link: DATA GROUPS -->
                                <msgpt:Information type="warning">
                                    <p><c:out
                                            value="${documentZoneDataGroupMissing}"/></p>
                                    <div id="editButton_bulkDataGroups">
                                        <input title="${msgpt:getMessage('page.label.RESOLVE.DATA.GROUPS')}"
                                               type="button" id="editZoneDataGroupBtn"
                                               onclick="javascript:dataZoneResolveAction(${document.id});"
                                               style="display: none;"/>
                                    </div>
                                </msgpt:Information>
                            </c:if>
                            <c:if test="${not empty notBridgedVariableIdList && document.dataSourceAssociation != null}">
                                <!-- Cross Link: VARIABLE MAPPING -->
                                <msgpt:Information type="error">
                                    <c:choose>
                                        <c:when test="${not empty targetDataSources }">
                                            <p><fmtSpring:message
                                                    code="page.text.warning.upmapped.variables.for.touchpoint"/></p>
                                            <div id="editButton_variableMapping">
                                                    <span>
                                                        <input title="${msgpt:getMessage('page.label.run.health.check.and.resolve')}"
                                                               type="button" id="editVariableMappingBtn"
                                                               onclick="javascript:variableMappingResolveAction(${document.id});"
                                                               style="display: none;"/>
                                                    </span>
                                                <span>
                                                        <input title="${msgpt:getMessage('page.label.SHOW.AND.MAP.VARIABLES')}"
                                                               type="button" id="editVariableAllMappingBtn"
                                                               onclick="javascript:variableAllMappingResolveAction(${document.id});"
                                                               style="display: none;"/>
                                                    </span>
                                                <input type="hidden" id="variableMapLinkBtnVariableIds"
                                                       value="${notBridgedVariableIdList}"/>
                                                <input type="hidden" id="variableMapLinkBtnDataSourceIds"
                                                       value="${targetDataSources}"/>
                                            </div>
                                        </c:when>
                                        <c:otherwise>
                                            <p><fmtSpring:message
                                                    code="page.text.warning.upmapped.reference.variables.for.touchpoint"/>
                                                <br><fmtSpring:message
                                                        code="page.text.review.touchpoint.data.collection"/></p>
                                        </c:otherwise>
                                    </c:choose>
                                </msgpt:Information>
                            </c:if>
                        </msgpt:IfAuthGranted>
                        <!-- END - ERROR/MESSAGES -->

                        <div class="stageCardsSectionWrapper">

                            <!-- Document -->
                            <div id="allZoneDivs" class="zonesSection">
                                <msgpt:DocumentTag maxWidth="350" document="${currentChannelDocument}"
                                                   type="admin" showDisabled="true"/>
                                <div class="zonesNavigation">
                                    <div id="sectionChangeTd_prev" title="${msgpt:getMessage('page.label.previous')}"
                                         class="navigationBtn"
                                         style="${numberOfSections == '1' ? 'display:none;' : ''}"
                                         onclick="sectionAction('previous','${document.id}');">
                                        <i class="far fa-angle-double-left fa-sm"></i>
                                    </div>
                                    <div id="sectionChangeTd_next" title="${msgpt:getMessage('page.label.next')}"
                                         class="navigationBtn"
                                         style="${numberOfSections == '1' ? 'display:none;' : ''}"
                                         onclick="sectionAction('next','${document.id}');">
                                        <i class="far fa-angle-double-right fa-sm"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="stageSectionContainer">
                                <div class="actionBtnToolbar">
                                    <select title="${msgpt:getMessage('page.label.actions')}" id="actionMenu"
                                            class="inputM style_menu" onchange="actionSelected_localHandler(this)"
                                            style="display: none;">
                                        <c:if test="${not noTouchpoints}">
                                            <option id="actionOption_9"
                                                    value="1" ${empty channels ? 'disabled="disabled"' : '' }>
                                                <fmtSpring:message code="page.label.clone.touchpoint"/></option>
                                            <option id="actionOption_6"
                                                    value="${document.id}"  ${empty documentZoneDataGroupMissing && empty notBridgedVariableIdList && document.dataSourceAssociation != null ? '' : 'disabled="disabled"' } >
                                                <fmtSpring:message code="page.label.export.touchpoint"/></option>
                                            <c:if test="${showContentJSONExport}">
                                                <option id="actionOption_28"
                                                        value="${document.id}"><fmtSpring:message
                                                        code="page.label.export.touchpoint.content.export"/></option>
                                            </c:if>
                                            <option id="actionOption_19"
                                                    value="${document.id}"><fmtSpring:message
                                                    code="page.label.touchpoint.update"/></option>
                                            <option id="actionOption_11" value="${document.id}"><fmtSpring:message
                                                    code="page.label.delete"/></option>
                                            <c:if test="${licencedForSegmentationAnalysis && document.segmentationAnalysisEnabled}">
                                                <option id="actionOption_22" value="${document.id}"><fmtSpring:message
                                                        code="page.label.refresh.segmentation.analysis"/></option>
                                            </c:if>
                                            <c:if test="${ tpAccessControlable }">
                                                <option id="actionOption_23" value="${document.id}"><fmtSpring:message
                                                        code="page.label.access.control"/></option>
                                            </c:if>
                                        </c:if>
                                        <option id="actionOption_10" ${not canAddChannel ? 'disabled="disabled"' : '' }>
                                            <fmtSpring:message code="page.label.add.channel"/></option>
                                        <option id="actionOption_29" >
                                            <fmtSpring:message code="page.label.download.export.XSD"/></option>
                                    </select>
                                    <c:if test="${not noTouchpoints}">
                                        <input title="${msgpt:getMessage('action.button.label.update')}" type="button"
                                               id="updateBtn_Edit"
                                               onclick="javascript:javascriptHref('../tpadmin/document_edit.form?docid=${param.docid}')"
                                               style="display: none;"/>
                                    </c:if>
                                </div>
                                <c:if test="${not empty document.originObject}">
                                    <!-- Touchpoint Project -->
                                    <div class="stageContainer">
                                        <div class="stageHeader">
                                            <div class="stageLabel">
                                                <c:choose>
                                                    <c:when test="${not empty document.checkoutTimestamp}">
                                                        <fmtSpring:message
                                                                code="page.label.touchpoint.project"/>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <fmtSpring:message code="page.label.touchpoint.info"/>
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                            <div class="stageActions">
                                                <msgpt:IfAuthGranted authority="ROLE_PROJECT_SYNC_EDIT">
                                                    <c:if test="${not empty document.checkoutTimestamp}">
                                                        <input title="${msgpt:getMessage('action.button.label.sync')}"
                                                               type="button" id="syncProjectBtn"
                                                               onclick="javascript:syncProjectAction(${document.id});"
                                                               style="display: none;"/>
                                                    </c:if>
                                                </msgpt:IfAuthGranted>
                                            </div>
                                        </div>
                                        <div class="stageContent">
                                            <div class="descriptionList singleCol">
                                                <div class="leftCol">
                                                    <!-- Touchpoint -->
                                                    <div class="descriptionTerm">
                                                        <c:choose>
                                                            <c:when test="${not empty document.checkoutTimestamp}">
                                                                <fmtSpring:message
                                                                        code="page.label.touchpoint"/>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <fmtSpring:message
                                                                        code="page.label.cloned.from"/>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                    <div class="descriptionDefinition">
                                                        <c:out value="${document.originObject.name}"/>
                                                    </div>
                                                    <!-- Created Time -->
                                                    <div class="descriptionTerm">
                                                        <fmtSpring:message code="page.label.created"/>
                                                    </div>
                                                    <div class="descriptionDefinition">
                                                        <c:choose>
                                                            <c:when test="${not empty document.created}">
                                                                <c:out value="${document.created}"/>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <fmtSpring:message
                                                                        code="page.label.no.default"/>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                    <c:if test="${not empty document.checkoutTimestamp}">
                                                        <!-- Checkout Time -->
                                                        <div class="descriptionTerm">
                                                            <fmtSpring:message code="page.label.synch.in"/>
                                                        </div>
                                                        <div class="descriptionDefinition">
                                                            <c:choose>
                                                                <c:when test="${not empty document.checkoutTimestamp}">
                                                                    <c:out value="${document.checkoutTimestamp}"/>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <fmtSpring:message code="page.label.never"/>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                        <!-- Checkin Time -->
                                                        <div class="descriptionTerm">
                                                            <fmtSpring:message code="page.label.synch.out"/>
                                                        </div>
                                                        <div class="descriptionDefinition">
                                                            <c:choose>
                                                                <c:when test="${not empty document.checkinTimestamp}">
                                                                    <c:out value="${document.checkinTimestamp}"/>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <fmtSpring:message code="page.label.never"/>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:if>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- END - Touchpoint Project -->
                                </c:if>

                                <c:if test="${not document.isSmsTouchpoint}">
                                </c:if>

                                <!-- Touchpoint Properties -->
                                <div class="stageContainer">
                                    <div class="stageHeader">
                                        <div class="stageLabel">
                                            <fmtSpring:message code="page.label.touchpoint.properties"/>
                                        </div>
                                        <div class="stageActions">
                                            <input title="${msgpt:getMessage('action.button.label.update')}"
                                                   type="button" id="updateBtn"
                                                   onclick="javascript:javascriptHref('../tpadmin/document_edit.form?docid=${param.docid}')"
                                                   style="display: none;"/>
                                        </div>
                                    </div>
                                    <div class="stageContent">
                                        <div class="descriptionList singleCol">
                                            <div class="leftCol">
                                                <!-- GUID -->
                                                <div class="descriptionTerm"><fmtSpring:message
                                                        code="page.label.guid"/></div>
                                                <div class="descriptionDefinition"><c:out
                                                        value="${document.guid}"/></div>

                                                <!-- Enabled as target of sync -->
                                                <div class="descriptionTerm"><fmtSpring:message
                                                        code="page.label.enable.as.target.of.sync"/></div>
                                                <div class="descriptionDefinition">
                                                    <c:choose>
                                                        <c:when test="${document.targetOfSync}">
                                                            <fmtSpring:message
                                                                    code="page.label.enabled"/>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <fmtSpring:message
                                                                    code="page.label.no"/>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>

                                                <!-- Accept only active objects -->
                                                <div class="descriptionTerm"><fmtSpring:message
                                                        code="page.label.accept.only.active.objects"/></div>

                                                <div class="descriptionDefinition">
                                                    <c:choose>
                                                        <c:when test="${document.acceptOnlyActiveObjects}">
                                                            <fmtSpring:message
                                                                    code="page.label.enabled"/>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <fmtSpring:message
                                                                    code="page.label.no"/>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>

                                                <!-- Require Active Objects synced into this Touchpoint to pass through workflow -->
                                                <div class="descriptionTerm"><fmtSpring:message
                                                        code="page.label.active.objects.sync.through.workflow"/></div>

                                                <div class="descriptionDefinition">
                                                    <c:choose>
                                                        <c:when test="${document.activeObjectsSyncThroughWorkflow}">
                                                            <fmtSpring:message
                                                                    code="page.label.enabled"/>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <fmtSpring:message
                                                                    code="page.label.no"/>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>

                                                <c:if test="${not document.isSmsTouchpoint}">
                                                    <!-- Default Text Style -->
                                                    <div class="descriptionTerm"><fmtSpring:message
                                                            code="page.label.default.text.style"/></div>
                                                    <div class="descriptionDefinition">
                                                        <c:choose>
                                                            <c:when test="${not empty document.defaultTextStyle}">
                                                                <c:out value="${document.defaultTextStyle.name}"/>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <fmtSpring:message
                                                                        code="page.label.no.default"/>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>

                                                    <!-- Default Paragraph Style -->
                                                    <div class="descriptionTerm"><fmtSpring:message
                                                            code="page.label.default.paragraph.style"/></div>
                                                    <div class="descriptionDefinition">
                                                        <c:choose>
                                                            <c:when test="${not empty document.defaultParagraphStyle}">
                                                                <c:out value="${document.defaultParagraphStyle.name}"/>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <fmtSpring:message
                                                                        code="page.label.no.default"/>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                </c:if>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END - Touchpoint Properties -->

                                <c:if test="${licencedForVariantManagement}">
                                    <!-- Touchpoint Selection Management -->
                                    <div class="stageContainer">
                                        <div class="stageHeader">
                                            <div class="stageLabel">
                                                <fmtSpring:message code="page.label.touchpoint.management"/>
                                            </div>
                                            <div class="stageActions">
                                                <input title="${msgpt:getMessage('action.button.label.update')}"
                                                       type="button" id="updateBtn"
                                                       onclick="javascript:javascriptHref('../tpadmin/document_edit.form?docid=${param.docid}#touchpoint-management')"
                                                       style="display: none;"/>
                                            </div>
                                        </div>
                                        <div class="stageContent">
                                            <div class="descriptionList singleCol">
                                                <div class="leftCol">
                                                    <c:choose>
                                                        <c:when test="${!isVariantTouchpoint}">
                                                            <!-- Enabled -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.variation.enabled"/></div>
                                                            <div class="descriptionDefinition">
                                                                <fmtSpring:message code="page.label.no"/></div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <!-- Enabled -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.variation.enabled"/></div>
                                                            <div class="descriptionDefinition">
                                                                <fmtSpring:message code="page.label.yes"/></div>
                                                            <!-- Workflow -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.variant.workflow"/></div>
                                                            <div class="descriptionDefinition">
                                                                <c:choose>
                                                                    <c:when test="${document.enabledForVariantWorkflow}">
                                                                        <fmtSpring:message
                                                                                code="page.label.enabled"/>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <fmtSpring:message
                                                                                code="page.label.disabled"/>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                            <!-- Selector -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.selector"/></div>
                                                            <div class="descriptionDefinition"><c:out
                                                                    value="${contentSelectorName}"/></div>
                                                            <!-- Selections -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.variants"/></div>
                                                            <div class="descriptionDefinition"><c:out
                                                                    value="${fn:length(document.visibleTouchpointSelections)}"/></div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- END - Touchpoint Selection Management -->
                                </c:if>

                                <!-- Language Management -->
                                <div class="stageContainer">
                                    <div class="stageHeader">
                                        <div class="stageLabel">
                                            <fmtSpring:message code="page.label.language.management"/>
                                        </div>
                                        <div class="stageActions">
                                            <input title="${msgpt:getMessage('action.button.label.update')}"
                                                   type="button" id="updateBtn"
                                                   onclick="javascript:javascriptHref('../tpadmin/document_edit.form?docid=${param.docid}#language-management')"
                                                   style="display: none;"/>
                                        </div>
                                    </div>
                                    <div class="stageContent">
                                        <div class="descriptionList singleCol">
                                            <div class="leftCol">
                                                <div class="descriptionTerm">
                                                    <c:choose>
                                                        <c:when test="${document.isMultiLanguage}">
                                                            <fmtSpring:message code="page.label.selector"/>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <fmtSpring:message
                                                                    code="page.label.default.language"/>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                                <div class="descriptionDefinition">
                                                    <c:choose>
                                                        <c:when test="${document.isMultiLanguage}">
                                                            <c:choose>
                                                                <c:when test="${not empty languageSelectorName}">
                                                                    <c:out value="${languageSelectorName}"/>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <fmtSpring:message
                                                                            code="page.label.not.specified"/>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:out value="${document.defaultTouchpointLanguage.name}"/>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END - Language Management -->

                                <c:if test="${licencedForInsertManagement && not currentChannelDocument.isEmailTouchpoint && not currentChannelDocument.isSmsTouchpoint && not currentChannelDocument.isWebTouchpoint}">
                                    <!-- Insert Management -->
                                    <div class="stageContainer">
                                        <div class="stageHeader">
                                            <div class="stageLabel">
                                                <fmtSpring:message code="page.label.insert.management"/>
                                            </div>
                                            <div class="stageActions">
                                                <input title="${msgpt:getMessage('action.button.label.update')}"
                                                       type="button" id="updateBtn"
                                                       onclick="javascript:javascriptHref('../tpadmin/document_edit.form?docid=${param.docid}#insert-management')"
                                                       style="display: none;"/>
                                            </div>
                                        </div>
                                        <div class="stageContent">
                                            <div class="descriptionList singleCol">
                                                <div class="leftCol">
                                                    <c:choose>
                                                        <c:when test="${!hasInsert}">
                                                            <!-- Enabled -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.enabled"/></div>
                                                            <div class="descriptionDefinition">
                                                                <fmtSpring:message code="page.label.no"/></div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <!-- Enabled -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.enabled"/></div>
                                                            <div class="descriptionDefinition">
                                                                <fmtSpring:message code="page.label.yes"/></div>
                                                            <!-- Selector -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.selector"/></div>
                                                            <div class="descriptionDefinition"><c:out
                                                                    value="${insertSelectorName}"/></div>
                                                            <!-- Page Weights -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.page.weight.first.other"/></div>
                                                            <div class="descriptionDefinition">
                                                                <c:out value="${firstPageWeight}"/> / <c:out
                                                                    value="${otherPagesWeight}"/>&nbsp;&nbsp;<c:out
                                                                    value="${defaultWeightUnit.displayText}"></c:out>
                                                            </div>
                                                            <!-- Default Rate Sheet -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.default.rate.sheet"/></div>
                                                            <div class="descriptionDefinition">
                                                                <c:choose>
                                                                    <c:when test="${not empty defaultRateScheduleName}">
                                                                        <c:out value="${defaultRateScheduleName}"/>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <fmtSpring:message
                                                                                code="page.label.none"/>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                            <!-- Default Schedule Layout -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.default.schedule.layout"/></div>
                                                            <div class="descriptionDefinition">
                                                                <c:choose>
                                                                    <c:when test="${not empty defaultInsertScheduleName}">
                                                                        <c:out value="${defaultInsertScheduleName}"/>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <fmtSpring:message
                                                                                code="page.label.none"/>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- END - Insert Management -->
                                </c:if>

                                <c:if test="${not empty document}">
                                    <!-- Touchpoint Content Change Flag -->
                                    <div class="stageContainer">
                                        <div class="stageHeader">
                                            <div class="stageLabel">
                                                <fmtSpring:message
                                                        code="page.label.touchpoint.production.change.status"/>
                                            </div>
                                        </div>
                                        <div class="stageContent">
                                            <div class="descriptionList singleCol">
                                                <div class="leftCol">
                                                    <c:choose>
                                                        <c:when test="${document.tpContentChanged}">
                                                            <!-- Enabled -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.touchpoint.production.change.status"/></div>
                                                            <div class="descriptionDefinition">
                                                                <fmtSpring:message
                                                                        code="page.label.changed.since.last.run"/></div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <!-- Enabled -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.touchpoint.production.change.status"/></div>
                                                            <div class="descriptionDefinition">
                                                                <fmtSpring:message
                                                                        code="page.label.unchanged.since.last.run"/></div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- END - Touchpoint Content Change Flag -->
                                </c:if>

                                <c:if test="${licencedForMessagepointInteractive}">
                                    <!-- Connected Management -->
                                    <div class="stageContainer">
                                        <div class="stageHeader">
                                            <div class="stageLabel">
                                                <fmtSpring:message code="page.label.connected.management"/>
                                            </div>
                                            <div class="stageActions">
                                                <input title="${msgpt:getMessage('action.button.label.update')}"
                                                       type="button" id="updateBtn"
                                                       onclick="javascript:javascriptHref('../tpadmin/document_edit.form?docid=${param.docid}#connected-management')"
                                                       style="display: none;"/>
                                            </div>
                                        </div>
                                        <div class="stageContent">
                                            <div class="descriptionList singleCol">
                                                <div class="leftCol">
                                                    <c:choose>
                                                        <c:when test="${!document.connectedEnabled}">
                                                            <!-- Enabled -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.enabled"/></div>
                                                            <div class="descriptionDefinition">
                                                                <fmtSpring:message code="page.label.no"/></div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <!-- Enabled -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.enabled"/></div>
                                                            <div class="descriptionDefinition">
                                                                <fmtSpring:message code="page.label.yes"/></div>
                                                            <!-- Order Entry Enabled -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.order.entry.enabled"/></div>
                                                            <div class="descriptionDefinition">
                                                                <c:choose>
                                                                    <c:when test="${document.communicationOrderEntryEnabled}">
                                                                        <fmtSpring:message
                                                                                code="page.label.enabled"/>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <fmtSpring:message
                                                                                code="page.label.disabled"/>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- END - Connected Management -->
                                </c:if>

                                <c:if test="${licencedForSegmentationAnalysis}">
                                    <!-- Segmentation Analysis -->
                                    <div class="stageContainer">
                                        <div class="stageHeader">
                                            <div class="stageLabel">
                                                <fmtSpring:message code="page.label.segmentation.analysis"/>
                                            </div>
                                            <div class="stageActions">
                                                <input title="${msgpt:getMessage('action.button.label.update')}"
                                                       type="button" id="updateBtn"
                                                       onclick="javascript:javascriptHref('../tpadmin/document_edit.form?docid=${param.docid}#segmentation-management')"
                                                       style="display: none;"/>
                                            </div>
                                        </div>
                                        <div class="stageContent">
                                            <div class="descriptionList singleCol">
                                                <div class="leftCol">
                                                    <c:choose>
                                                        <c:when test="${!document.segmentationAnalysisEnabled}">
                                                            <!-- Enabled -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.enabled"/></div>
                                                            <div class="descriptionDefinition">
                                                                <fmtSpring:message code="page.label.no"/></div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <!-- Enabled -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.enabled"/></div>
                                                            <div class="descriptionDefinition">
                                                                <fmtSpring:message code="page.label.yes"/></div>
                                                            <!-- Data Resource -->
                                                            <div class="descriptionTerm"><fmtSpring:message
                                                                    code="page.label.data.resource"/></div>
                                                            <div class="descriptionDefinition">
                                                                <c:choose>
                                                                    <c:when test="${not empty document.segmentationAnalysisResource}">
                                                                        <c:out value="${document.segmentationAnalysisResource.name}"/>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <fmtSpring:message
                                                                                code="page.label.none"/>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- END - Segmentation Analysis -->
                                </c:if>

                                <c:if test="${ currentChannelDocument.isWebTouchpoint || currentChannelDocument.isEmailTouchpoint }">
                                    <!-- Template Modifier Setup : Only display if Channel.CHANNEL_EMAIL_ID == 4 -->
                                    <div class="stageContainer">
                                        <div class="stageHeader">
                                            <div class="stageLabel">
                                                <fmtSpring:message
                                                        code="page.label.template.modifiers"/>
                                            </div>
                                            <div class="stageActions">
                                                <div id="editButton_templateModifiers">
                                                    <input title="${msgpt:getMessage('action.button.label.update')}"
                                                           type="button" id="editTemplateModifierBtn"
                                                           onclick="javascript:templateModifierSetupAction(${currentChannelDocument.id});"
                                                           style="display: none;"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="stageContent">
                                            <div class="descriptionList singleCol">
                                                <div class="leftCol">
                                                    <div class="descriptionTerm"><fmtSpring:message
                                                            code="page.label.template.modifiers"/></div>
                                                    <div class="descriptionDefinition"><c:out
                                                            value="${totalNumberOfTemplateModifiers}"/></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- END - Template Modifier Setup -->
                                </c:if>

                                <!-- Data Collection Configuration -->
                                <div class="stageContainer">
                                    <div class="stageHeader">
                                        <div class="stageLabel">
                                            <fmtSpring:message code="page.label.data.collection.configuration"/>
                                        </div>
                                        <div class="stageActions">
                                            <div id="editButton_dataCollection">
                                                <input title="${msgpt:getMessage('action.button.label.update')}"
                                                       type="button" id="editDataAssoBtn"
                                                       onclick="javascript:dataSourceAssociationAction(${document.id});"
                                                       style="display: none;"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="stageContent">
                                        <div class="descriptionList singleCol">
                                            <div class="leftCol">
                                                <div class="descriptionTerm"><fmtSpring:message
                                                        code="page.label.data.source.association.name"/></div>
                                                <div class="descriptionDefinition"><c:out
                                                        value="${document.dataSourceAssociation.name}"/></div>

                                                <div class="descriptionTerm"><fmtSpring:message
                                                        code="page.label.primary.data.source"/></div>
                                                <div class="descriptionDefinition"><c:out
                                                        value="${document.dataSourceAssociation.primaryDataSource.name}"/></div>

                                                <div class="descriptionTerm"><fmtSpring:message
                                                        code="page.label.reference.data.sources"/></div>
                                                <div class="descriptionDefinition"><c:out
                                                        value="${fn:length(document.dataSourceAssociation.referenceConnections)}"/></div>

                                                <div class="descriptionTerm"><fmtSpring:message
                                                        code="page.label.customer.primary.key"/></div>
                                                <div class="descriptionDefinition"><c:out
                                                        value="${document.dataSourceAssociation.customerDataElementId.name}"/></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END - Data Collection Configuration -->

                                <!-- Channel Configuration -->
                                <div class="stageContainer">
                                    <div class="stageHeader">
                                        <div class="stageLabel">
                                            <fmtSpring:message code="page.label.channel.configuration"/>
                                        </div>
                                        <div class="stageActions">
                                            <div id="editButton_channelConfig">
                                                <input title="${msgpt:getMessage('action.button.label.update')}"
                                                       type="button" id="channelConnectorEditBtn"
                                                       onclick="btnJavascriptHref('touchpoint_channel_configuration_edit.form?documentid=${param.docid}',this);"
                                                       style="display: none;"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="stageContent">
                                        <div class="descriptionList singleCol">
                                            <div class="leftCol">
                                                <div class="descriptionTerm"><fmtSpring:message
                                                        code="page.label.channel"/></div>
                                                <c:if test="${not empty currentChannelDocument.connectorConfiguration}">
                                                    <div class="descriptionDefinition"><fmtSpring:message
                                                            code="${currentChannelDocument.connectorConfiguration.connector.channel.presentationName}"/></div>

                                                    <div class="descriptionTerm"><fmtSpring:message
                                                            code="page.label.connector"/></div>
                                                    <div class="descriptionDefinition"><fmtSpring:message
                                                            code="${currentChannelDocument.connectorConfiguration.connector.presentationName}"/></div>

                                                    <c:if test="${currentChannelDocument.connectorConfiguration.connector.id == 15}">
                                                        <div class="descriptionTerm"><fmtSpring:message
                                                                code="page.label.server"/></div>
                                                        <div class="descriptionDefinition"><c:out
                                                                value="${currentChannelDocument.connectorConfiguration.serverName}"/></div>
                                                    </c:if>
                                                </c:if>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END - Channel Configuration -->

                                <!-- Languages -->
                                <c:set var="languageVOs" value="${command.languageList}"/>

                                <!-- LANGUAGES ACTION BAR: UPDATE, ACTIONS -->
                                <div class="stageContainer">
                                    <div class="tableComponentWrapper">
                                        <div class="actionBarContainer">
                                            <div class="actionBarHeaderLabel"><span
                                                    class="stageLabel">${msgpt:getMessage('page.label.languages')} (${fn:length(languageVOs)})</span>
                                            </div>
                                            <div class="actionBarButtonsContainer">
                                                <input title="${msgpt:getMessage('action.button.label.update')}"
                                                       type="button"
                                                       id="languageUpdateBtn" ${empty languageVOs ? 'disabled="disabled"' : '' }
                                                       onclick="languageIframeAction()" style="display: none;"/>
                                                <select title="${msgpt:getMessage('page.label.actions')}"
                                                        id="languagesActionMenu" class="inputM style_menu"
                                                        onchange="languageActionSelected(this)"
                                                        style="display: none;">
                                                    <option id="actionOption_12"><fmtSpring:message
                                                            code="page.label.add.language"/></option>
                                                    <option id="actionOption_14"><fmtSpring:message
                                                            code="page.label.delete.language"/></option>
                                                    <option id="actionOption_18"><fmtSpring:message
                                                            code="page.label.make.default"/></option>
                                                    <option id="actionOption_24"><fmtSpring:message
                                                            code="page.label.customize.locale"/></option>
                                                    <option id="actionOption_25"><fmtSpring:message
                                                            code="page.label.restore.locale"/></option>
                                                    <c:if test="${document.hasTouchpointSelections && fn:length(languageVOs) > 1}">
                                                        <option id="actionOption_26"><fmtSpring:message
                                                                code="page.label.repair"/></option>
                                                    </c:if>
                                                </select>
                                            </div>
                                        </div>
                                        <!-- Languages: List -->
                                        <msgpt:DataTable id="languagesTable" staticData="true">
                                            <c:forEach var="currentLanguageVO" items="${languageVOs}"
                                                       varStatus="status">
                                                <c:set var="language" value="${currentLanguageVO.touchpointLanguage}"/>
                                                <msgpt:TableListGroup>
                                                    <msgpt:TableElement width="7%" label=""
                                                                        style="vertical-align: middle;">
                                                        <c:if test="${not language.isDefaultLanguage}">
                                                            <input type="hidden"
                                                                   id="canDelete_language_${language.id}"/>
                                                        </c:if>
                                                        <form:checkbox
                                                                path="languageMap[${language.id}].selectedForAction"
                                                                id="languageListItemCheck_${language.id}"
                                                                cssClass="checkbox checkboxAlign"
                                                                onclick="validateLanguageActionReq(${language.id});"/>
                                                    </msgpt:TableElement>
                                                    <msgpt:TableElement label="page.label.name">
                                                        <a id="languageLink_${language.id}"
                                                           langHash="${msgpt:hashCode( language )}"
                                                           href="javascript:languageIframeAction(${language.id})">
                                                            <c:out value="${language.name}"/>
                                                        </a>
                                                    </msgpt:TableElement>
                                                    <msgpt:TableElement label="page.label.locale.code">
                                                        <c:out value="${language.messagepointLocale.code}"/>
                                                    </msgpt:TableElement>
                                                    <msgpt:TableElement label="page.label.default">
                                                        <c:if test="${language.isDefaultLanguage}">
                                                            <div class="checkmarkIcon dataTable_icon_align"/>
                                                        </c:if>
                                                    </msgpt:TableElement>
                                                    <msgpt:TableElement label="page.label.locale.customization">
                                                        <c:if test="${not empty language.touchpointLocale}">
                                                            <div class="checkmarkIcon dataTable_icon_align"/>
                                                        </c:if>
                                                    </msgpt:TableElement>
                                                </msgpt:TableListGroup>
                                            </c:forEach>
                                        </msgpt:DataTable>
                                    </div>
                                </div>
                                <!-- END - Languages -->

                                <!-- COMPOSITION PACKAGES -->
                                <c:if test="${currentChannelDocument.isPrintTouchpoint && !currentChannelDocument.isNativeCompositionTouchpoint}">

                                    <!-- Composition Packages -->
                                    <c:set var="compositionPackages" value="${command.compositionFileSetsList}"/>
                                    <div class="stageContainer">
                                        <div class="tableComponentWrapper">
                                            <!-- COMPOSITION PACKAGES ACTION BAR: ACTIONS -->
                                            <div class="actionBarContainer">
                                                <div class="actionBarHeaderLabel">
                                                <span class="stageLabel">
                                                    <c:choose>
                                                        <c:when test="${isFilePackage}">
                                                            <c:out value="${msgpt:getMessage('page.label.file.packages')} (${fn:length(compositionPackages)})"/>
                                                        </c:when>
                                                        <c:when test="${fn:length(compositionPackages) == 0}">
                                                            <fmtSpring:message code="page.label.composition.packages.zero"/>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:out value="${msgpt:getMessage('page.label.composition.packages')} (${fn:length(compositionPackages)})"/>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </span>
                                                </div>
                                                <div class="actionBarButtonsContainer">
                                                    <input title="${msgpt:getMessage('action.button.label.update')}"
                                                           type="button" id="compositionPackageUpdateBtn"
                                                           disabled="disabled"
                                                           onclick="compositionPackageAction(this)"
                                                           style="display: none;"/>
                                                    <select title="${msgpt:getMessage('page.label.actions')}"
                                                            id="compositionPackageActionMenu"
                                                            class="inputM style_menu"
                                                            onchange="compositionPackageAction(this)"
                                                            style="display: none;">
                                                        <option id="actionOption_20"><fmtSpring:message
                                                                code="page.label.add"/></option>
                                                        <option id="actionOption_27"><fmtSpring:message
                                                                code="page.label.make.default"/></option>
                                                        <option id="actionOption_21"><fmtSpring:message
                                                                code="page.label.delete"/></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <c:choose>
                                                <c:when test="${fn:length(compositionPackages) == 0}">
                                                    <!-- Composition Packages: List -->
                                                    <msgpt:DataTable id="compositionPackagesTable">
                                                        <msgpt:TableListGroup>
                                                            <msgpt:TableElement width="7%" style="vertical-align: middle;">
                                                                <c:choose>
                                                                    <c:when test="${isFilePackage}">
                                                                        <fmtSpring:message code="page.text.no.file.package.uploaded"/>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <fmtSpring:message code="page.text.no.composition.package.uploaded"/>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </msgpt:TableElement>
                                                        </msgpt:TableListGroup>
                                                    </msgpt:DataTable>

                                                </c:when>
                                                <c:otherwise>

                                                    <!-- Composition Packages: List -->
                                                    <msgpt:DataTable id="compositionPackagesTable">
                                                        <c:forEach var="currentPackageVO" items="${compositionPackages}"
                                                                   varStatus="status">
                                                            <c:set var="currentPackage"
                                                                   value="${currentPackageVO.compositionFileSet}"/>
                                                            <msgpt:TableListGroup>
                                                                <msgpt:TableElement width="7%" label=""
                                                                                    style="vertical-align: middle;">
                                                                    <form:checkbox
                                                                            path="compositionFileSetsMap[${currentPackage.id}].selectedForAction"
                                                                            id="compositionPackageListItemCheck_${currentPackage.id}"
                                                                            cssClass="checkbox checkboxAlign"
                                                                            onclick="validateCompositionPackageActionReq(${currentPackage.id});"/>
                                                                </msgpt:TableElement>
                                                                <msgpt:TableElement label="page.label.name">
                                                                    <span id="compositionPackage_${currentPackage.id}"
                                                                          packageName="${currentPackage.name}"
                                                                          templateFileName="${currentPackage.templateFileName}"
                                                                          configurationFileName="${currentPackage.compositionConfigurationFileName}">
                                                                        <c:out value="${currentPackage.name}"/>
                                                                    </span>
                                                                </msgpt:TableElement>
                                                                <msgpt:TableElement label="page.label.created">
                                                                    <fmtJSTL:formatDate
                                                                            value="${currentPackage.created}"
                                                                            pattern="${dateTimeFormat}"/>
                                                                </msgpt:TableElement>
                                                                <msgpt:TableElement label="page.label.default">
                                                                    <c:if test="${defaultCompositionPackageId == currentPackage.id}">
                                                                        <div class="checkmarkIcon dataTable_icon_align"/>
                                                                    </c:if>
                                                                </msgpt:TableElement>
                                                            </msgpt:TableListGroup>
                                                        </c:forEach>
                                                    </msgpt:DataTable>

                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                </c:if>
                                <!-- END - COMPOSITION PACKAGES -->

                                <!-- Attachment Management -->
                                <c:if test="${currentChannelDocument.isEmailTouchpoint}">
                                    <c:set var="attachments" value="${command.attachmentList}"/>
                                    <div class="stageContainer">
                                        <div class="tableComponentWrapper">
                                            <!-- Attachment Management ACTION BAR: ACTIONS -->
                                            <div class="actionBarContainer">
                                                <div class="actionBarHeaderLabel">
                                                <span class="stageLabel">
                                                    <fmtSpring:message code="page.label.attachment.management"/>
                                                </span>
                                                </div>
                                                <div class="actionBarButtonsContainer">
                                                    <input title="${msgpt:getMessage('action.button.label.update')}"
                                                           type="button" id="attachmentMgtUpdateBtn"
                                                           disabled="disabled"
                                                           onclick="attachmentMgtAction(this)"
                                                           style="display: none;"/>
                                                    <select title="${msgpt:getMessage('page.label.actions')}"
                                                            id="attachmentMgtActionMenu"
                                                            class="inputM style_menu"
                                                            onchange="attachmentMgtAction(this)"
                                                            style="display: none;">
                                                        <option id="actionOption_30"><fmtSpring:message
                                                                code="page.label.add"/></option>
                                                        <option id="actionOption_31"><fmtSpring:message
                                                                code="page.label.delete"/></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <c:choose>
                                                <c:when test="${fn:length(attachments) == 0}">
                                                    <!-- Attachment Management: List -->
                                                    <msgpt:DataTable id="attachmentMgtTable">
                                                        <msgpt:TableListGroup>
                                                            <msgpt:TableElement width="7%" style="vertical-align: middle;">
                                                                <fmtSpring:message code="page.text.no.files.uploaded.for.touchpoint"/>
                                                            </msgpt:TableElement>
                                                        </msgpt:TableListGroup>
                                                    </msgpt:DataTable>
                                                </c:when>
                                                <c:otherwise>
                                                    <!-- Attachment Management: List -->
                                                    <msgpt:DataTable id="attachmentMgtTable">
                                                        <c:forEach var="currAttachmentVO" items="${attachments}"
                                                                   varStatus="status">
                                                            <c:set var="currAttachment"
                                                                   value="${currAttachmentVO.attachment}"/>
                                                            <msgpt:TableListGroup>
                                                                <msgpt:TableElement width="7%" label="" style="vertical-align: middle;">
                                                                    <form:checkbox
                                                                            path="attachmentMap[${currAttachment.id}].selectedForAction"
                                                                            id="attachmentListItemCheck_${currAttachment.id}"
                                                                            cssClass="checkbox checkboxAlign"
                                                                            onclick="validateAttachmentManagementActionReq(${currAttachment.id});"/>
                                                                </msgpt:TableElement>
                                                                <msgpt:TableElement label="page.label.name">
                                                                    <span id="attachment_${currAttachment.id}" class="attachmentDetailsPopup">
                                                                        <c:out value="${currAttachment.name}"/>
                                                                    </span>
                                                                </msgpt:TableElement>
                                                                <msgpt:TableElement label="client_messages.text.targeted">
                                                                    <c:if test="${currAttachment.hasTarget}">
                                                                        <span id="attachmentTgt_${currAttachment.id}"
                                                                              class="d-inline-block ml-1 attachmentTgtDetailsPopup">
                                                                            <i class="far fa-bullseye" aria-hidden="true"></i>
                                                                        </span>
                                                                    </c:if>
                                                                </msgpt:TableElement>
                                                                <msgpt:TableElement label="page.label.created">
                                                                    <fmtJSTL:formatDate
                                                                            value="${currAttachment.created}"
                                                                            pattern="${dateTimeFormat}"/>
                                                                </msgpt:TableElement>
                                                            </msgpt:TableListGroup>
                                                        </c:forEach>
                                                    </msgpt:DataTable>

                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                </c:if>
                                <!-- END - COMPOSITION PACKAGES -->
                                <!-- External Reporting Data -->
                                <div class="stageContainer">
                                    <div class="stageHeader">
                                        <div class="stageLabel">
                                            <fmtSpring:message code="page.label.external.reporting.data"/>
                                        </div>
                                        <div class="stageActions">
                                            <div id="editButton_extReportingData">
                                                <input title="${msgpt:getMessage('action.button.label.update')}"
                                                       type="button" id="extReportingDataEditBtn"
                                                       style="display: none;"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="stageContent">
                                        <iframe id="extendedReportingDataIFrame"
                                                src="document_view_extended_reporting_variables.form?docid=${param.docid}&iframetarget=1&tk=${param.tk}"
                                                frameborder="0" width="800px" scrolling="no"></iframe>
                                    </div>
                                </div>
                                <!-- END - External Reporting Data -->

                                <c:if test="${(currentChannelDocument.isEmailTouchpoint || currentChannelDocument.isSmsTouchpoint || currentChannelDocument.isWebTouchpoint || currentChannelDocument.isDialogueTouchpoint || currentChannelDocument.isGMCTouchpoint || currentChannelDocument.isNativeCompositionTouchpoint || currentChannelDocument.isSefasCompositionTouchpoint || currentChannelDocument.isMPHCSCompositionTouchpoint) }">
                                    <!-- Targeting -->
                                    <div id="touchpointTargetingContainer" class="stageContainer">
                                        <div class="stageHeader">
                                            <div class="stageLabel">
                                                <fmtSpring:message
                                                        code="page.label.touchpoint.targeting"/>
                                            </div>
                                            <div class="stageActions">
                                                <c:if test="${touchpointTargeting.hasTarget}">
                                                    <a id="toggleTargetingLink"
                                                       href="javascript:toggleTargetingDetails('touchpointTargetingContainer')"><fmtSpring:message
                                                            code="page.label.DETAILS"/></a>
                                                </c:if>
                                                <div id="editButton_targeting">
                                                    <input title="${msgpt:getMessage('action.button.label.update')}"
                                                           type="button" id="targetingEditBtn"
                                                           onclick="btnJavascriptHref('${contextPath}/tpadmin/touchpoint_targeting_edit.form?touchpointTargetingId=${touchpointTargeting.id}',this);"
                                                           style="display: none;"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="stageContent">
                                            <c:if test="${touchpointTargeting.hasTarget}">
                                                <c:set var="targetableObj" value="${touchpointTargeting}"
                                                       scope="request"/>
                                                <jsp:include page="../targeting_summary.jsp"/>
                                            </c:if>
                                            <c:if test="${not touchpointTargeting.hasTarget}">
                                                <div class="descriptionList singleCol">
                                                    <div class="leftCol">
                                                        <div class="descriptionDefinition"><fmtSpring:message
                                                                code="page.label.none"/></div>
                                                    </div>
                                                </div>
                                            </c:if>
                                        </div>
                                    </div>
                                </c:if>

                                <!-- Blue Relay Configuration -->
                                <div class="stageContainer">
                                    <div class="stageHeader">
                                        <div class="stageLabel">
                                            <fmtSpring:message code="page.label.blue.relay.configuration"/>
                                        </div>
                                        <div class="stageActions">
                                            <div id="editButton_blueRelayConfig">
                                                <input id="editBlueRelayBtn"
                                                       title="${msgpt:getMessage('action.button.label.update')}"
                                                       type="button"
                                                       onclick="javascript:javascriptHref('../tpadmin/document_edit.form?docid=${param.docid}#bluerelay-management')"
                                                       style="display: none;"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="stageContent">
                                        <div class="descriptionList singleCol">
                                            <div class="leftCol">
                                                <div class="descriptionTerm">
                                                    <fmtSpring:message code="page.label.blue.relay.endpoint"/>
                                                </div>
                                                <div class="descriptionDefinition">
                                                    <c:out value="${document.blueRelayEndpoint}"/>
                                                </div>

                                                <div class="descriptionTerm">
                                                    <fmtSpring:message code="page.label.username"/>
                                                </div>
                                                <div class="descriptionDefinition">
                                                    <c:out value="${document.blueRelayUsername}"/>
                                                </div>

                                                <div class="descriptionTerm">
                                                    <fmtSpring:message code="page.label.blue.relay.token"/>
                                                </div>
                                                <div class="descriptionDefinition">
                                                    <c:out value="${document.blueRelayToken}"/>
                                                </div>

                                                <div class="descriptionTerm">
                                                    <fmtSpring:message code="page.label.blue.relay.target.folder"/>
                                                </div>
                                                <div class="descriptionDefinition">
                                                    <c:out value="${document.blueRelayTargetFolder}"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END - Blue Relay Configuration -->

                                <!-- Brand Configuration -->
                                <div class="stageContainer">
                                    <div class="stageHeader">
                                        <div class="stageLabel">
                                            <fmtSpring:message code="page.label.brand"/>
                                        </div>
                                        <div class="stageActions">
                                            <div id="editButton_brandConfig">
                                                <input id="editBrandBtn"
                                                       title="${msgpt:getMessage('action.button.label.update')}"
                                                       type="button"
                                                       onclick="javascript:javascriptHref('../tpadmin/document_edit.form?docid=${param.docid}#brand-management')"
                                                       style="display: none;"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="stageContent">
                                        <div class="descriptionList singleCol">
                                            <div class="leftCol">
                                                <div class="descriptionTerm">
                                                    <fmtSpring:message code="page.label.brand.profile"/>
                                                </div>
                                                <div class="descriptionDefinition">
                                                    <c:choose>
                                                        <c:when test="${not empty document.brandProfile}">
                                                            <c:out value="${document.brandProfile.name}"/>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <fmtSpring:message code="page.text.apply.default.brand"/>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END - Blue Relay Configuration -->

                                <!-- Stripo Configuration -->
                                <c:if test="${currentChannelDocument.isOmniChannel || currentChannelDocument.isEmailTouchpoint || currentChannelDocument.isWebTouchpoint}">
                                    <div class="stageContainer">
                                        <div class="stageHeader">
                                            <div class="stageLabel">
                                                <fmtSpring:message code="page.label.stripo.email.editor"/>
                                            </div>
                                            <div class="stageActions">
                                                <div id="editButton_stripoConfig">
                                                    <input id="editStripoBtn"
                                                           title="${msgpt:getMessage('action.button.label.update')}"
                                                           type="button"
                                                           onclick="javascript:javascriptHref('../tpadmin/document_edit.form?docid=${param.docid}#stripo-management')"
                                                           style="display: none;"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="stageContent">
                                            <div class="descriptionList singleCol">
                                                <div class="leftCol">
                                                    <div class="descriptionTerm"><fmtSpring:message
                                                            code="page.label.editor.enabled"/></div>
                                                    <c:choose>
                                                        <c:when test="${!document.stripoEnabled}">
                                                            <div class="descriptionDefinition">
                                                                <fmtSpring:message code="page.label.no"/></div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="descriptionDefinition">
                                                                <fmtSpring:message code="page.label.yes"/></div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </c:if>
                                <!-- END - Stripo Configuration -->

                                <!-- Document origination -->
                                <c:if test="${not empty documentHistory}">
                                    <div class="stageContainer">
                                        <div class="stageHeader">
                                            <div class="stageLabel">
                                                <fmtSpring:message code="page.label.document.origination"/>
                                            </div>
                                            <div class="stageActions">
                                            </div>
                                        </div>
                                        <div class="stageContent">
                                            <div class="descriptionList singleCol">
                                                <div class="leftCol">
                                                    <div class="descriptionTerm">
                                                        <fmtSpring:message code="page.label.type"/>
                                                    </div>

                                                    <div class="descriptionDefinition">
                                                        <c:if test="${documentHistory.isCreated}">
                                                            <fmtSpring:message code="page.label.created"/>
                                                        </c:if>
                                                        <c:if test="${documentHistory.isImported}">
                                                            <fmtSpring:message code="page.label.imported.from.xml"/>
                                                        </c:if>
                                                        <c:if test="${documentHistory.isCloned}">
                                                            <fmtSpring:message code="page.label.cloned"/>
                                                        </c:if>
                                                        <c:if test="${documentHistory.isVersioned}">
                                                            <fmtSpring:message code="page.label.versioned"/>
                                                        </c:if>
                                                        <c:if test="${documentHistory.isSubscribed}">
                                                            <fmtSpring:message code="page.label.subscribed"/>
                                                        </c:if>
                                                    </div>

                                                    <c:if test="${documentHistory.isImported}">
                                                        <div class="descriptionTerm">
                                                            <fmtSpring:message code="page.label.xml.file.name"/>
                                                        </div>
                                                        <div class="descriptionDefinition">
                                                            <div style="display: inline;">
                                                                <c:out value="${documentHistory.importFileName}"/>
                                                            </div>
                                                            <div id="downloadImportedXmlBtn"
                                                                 dh="${documentHistory.id}"
                                                                 style="display: inline;"
                                                                 class="ctionBtn_roundAll actionBtn detailTip fa-mp-container"
                                                                 title="|<div class='detailTipText'>${msgpt:getMessage('page.label.download.imported.XML')}</div>">
                                                                <i class="far fa-download"></i>
                                                            </div>
                                                        </div>
                                                    </c:if>

                                                    <c:if test="${documentHistory.isCloned || documentHistory.isVersioned || documentHistory.isSubscribed}">
                                                        <div class="descriptionTerm">
                                                            <fmtSpring:message code="page.label.source.domain"/>
                                                        </div>
                                                        <div class="descriptionDefinition">
                                                            <c:out value="${documentHistory.sourceDomainName}"/>
                                                        </div>

                                                        <div class="descriptionTerm">
                                                            <fmtSpring:message code="page.label.source.instance"/>
                                                        </div>
                                                        <div class="descriptionDefinition">
                                                            <c:out value="${documentHistory.sourceInstanceName}"/>
                                                        </div>

                                                        <div class="descriptionTerm">
                                                            <fmtSpring:message code="page.label.source.document"/>
                                                        </div>
                                                        <div class="descriptionDefinition">
                                                            <c:out value="${documentHistory.sourceDocumentName}"/>
                                                        </div>
                                                    </c:if>

                                                    <c:if test="${documentHistory.isImported || documentHistory.isCloned || documentHistory.isVersioned || documentHistory.isSubscribed}">
                                                        <div class="descriptionTerm">
                                                            <fmtSpring:message code="page.label.log.message"/>
                                                        </div>
                                                        <div class="descriptionDefinition">
                                                            <c:out value="${documentHistory.logFileName}"/>
                                                            <div id="downloadDocumentOriginationLogBtn"
                                                                 dh="${documentHistory.id}"
                                                                 style="display: inline;"
                                                                 class="ctionBtn_roundAll actionBtn detailTip fa-mp-container"
                                                                 title="|<div class='detailTipText'>${msgpt:getMessage('page.label.download.log.file')}</div>">
                                                                <i class="far fa-download"></i>
                                                            </div>
                                                        </div>
                                                    </c:if>

                                                    <div class="descriptionTerm">
                                                        <fmtSpring:message code="page.label.date.and.time"/>
                                                    </div>
                                                    <div class="descriptionDefinition">
                                                        <c:out value="${documentHistory.created}"/>
                                                    </div>
                                                    <div class="descriptionTerm">
                                                        <fmtSpring:message code="page.label.by"/>
                                                    </div>
                                                    <div class="descriptionDefinition">
                                                        <c:out value="${documentHistory.userName}"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </c:if>
                                <!-- Document origination -->
                            </div>
                        </div>
                        <!-- END - No noTouchpoints -->
                    </c:if>

                    <!-- POPUP DATA -->
                    <div id="actionSpecs" style="display: none;">
                        <!-- ACTIONS POPUP DATA -->
                        <div id="actionSpec_9" submitId="9"> <!-- Clone Touchpoint -->
                            <div id="actionTitle_9"><fmtSpring:message code="page.label.confirm.clone"/></div>
                            <div id="actionInfo_9"><p><b><fmtSpring:message code="page.text.clone.touchpoint"/></b></p>
                            </div>
                            <div id="actionCloneTouchpointOptions_9"></div>
                        </div>
                        <div id="actionSpec_11" submitId="11"> <!-- Delete Touchpoint -->
                            <div id="actionTitle_11"><fmtSpring:message code="page.label.confirm.delete"/></div>
                            <div id="actionInfo_11"><p><b>${deleteTpMsgPartOne} <fmtSpring:message
                                    code="page.text.delete.touchpoint.part.two"/></b></p></div>
                            <div id="actionDeleteTpOptions_11"></div>
                            <div id="actionCustomButtons_11"></div>
                        </div>
                        <div id="actionSpec_6" submitId="6"> <!-- Export touchpoint -->
                            <div id="actionTitle_6"><fmtSpring:message
                                    code="page.label.confirm.export.touchpoint"/></div>
                            <c:choose>
                                <c:when test="${document.isEmailTouchpoint && templatePackageExists}">
                                    <p id="actionInfo_6"><b><fmtSpring:message
                                            code="page.text.link.to.download.touchpoint.will.popup"/><br><br><fmtSpring:message
                                            code="page.text.warning.email.touchpoint.contains.template.package.match.with.export"/></b>
                                    </p>
                                </c:when>
                                <c:otherwise>
                                    <p id="actionInfo_6"><b><fmtSpring:message
                                            code="page.text.link.to.download.touchpoint.will.popup"/></b></p>
                                </c:otherwise>
                            </c:choose>
                            <div id="actionExportOptions_6"></div>
                        </div>
                        <div id="actionSpec_14" type="simpleConfirm" submitId="14"> <!-- Delete language -->
                            <div id="actionTitle_14"><fmtSpring:message
                                    code="page.label.confirm.delete.language"/></div>
                            <div id="actionInfo_14"><p><b><fmtSpring:message code="page.text.delete.language"/></b></p>
                            </div>
                        </div>
                        <div id="actionSpec_18" type="simpleConfirm" submitId="18"> <!-- Make default language -->
                            <div id="actionTitle_18"><fmtSpring:message
                                    code="page.label.confirm.default.language.change"/></div>
                            <div id="actionInfo_18"><p><b><fmtSpring:message
                                    code="page.text.select.default.language"/></b></p></div>
                        </div>
                        <div id="actionSpec_21" type="simpleConfirm" submitId="21" contentWidth="330px">
                            <!-- Delete composition package -->
                            <div id="actionTitle_21"><fmtSpring:message
                                    code="${isFilePackage ? 'page.label.confirm.delete.file.packages' : 'page.label.confirm.delete.composition.packages'}"/></div>
                            <div id="actionInfo_21"><p><b><fmtSpring:message
                                    code="${isFilePackage ? 'page.text.delete.selected.file.packages' : 'page.text.delete.selected.composition.packages'}"/></b></p></div>
                        </div>
                        <div id="actionSpec_22" type="simpleConfirm" submitId="22" contentWidth="330px">
                            <!-- Refresh segmentation analysis -->
                            <div id="actionTitle_22"><fmtSpring:message
                                    code="page.label.confirm.refresh.segmentation.analysis"/></div>
                            <div id="actionInfo_22"><p><b><fmtSpring:message
                                    code="page.text.refresh.segmentation.analysis"/></b></p></div>
                        </div>
                        <div id="actionSpec_25" type="simpleConfirm" submitId="25" contentWidth="330px">
                            <!-- Restore locale -->
                            <div id="actionTitle_25"><fmtSpring:message code="page.label.confirm.restore.locale"/></div>
                            <div id="actionInfo_25"><p><b><fmtSpring:message code="page.text.restore.locale"/></b></p>
                            </div>
                        </div>
                        <div id="actionSpec_26" type="simpleConfirm" submitId="26" contentWidth="330px">
                            <!-- Repair languages -->
                            <div id="actionTitle_26"><fmtSpring:message
                                    code="page.label.confirm.language.repair"/></div>
                            <div id="actionInfo_26"><p><b><fmtSpring:message code="page.text.repair.lanugages"/></b></p>
                            </div>
                        </div>
                        <!-- Make default composition package -->
                        <div id="actionSpec_27" type="simpleConfirm" submitId="27" contentWidth="380px">
                            <div id="actionTitle_27">
                                <fmtSpring:message code="${isFilePackage ? 'page.label.confirm.default.file.package.change' : 'page.label.confirm.default.composition.package.change'}"/>
                            </div>
                            <div id="actionInfo_27">
                                <p><b><fmtSpring:message code="${isFilePackage ?
                                    'page.text.select.default.file.package' : 'page.text.select.default.composition.package'}"/></b></p>
                            </div>
                        </div>

                        <!-- Export all contents to JSON -->
                        <div id="actionSpec_28" submitId="28"> <!-- Export all content in touchpoint -->
                            <div id="actionTitle_28"><fmtSpring:message code="page.label.confirm.content.export"/></div>
                            <div id="actionInfo_28"><p><b><fmtSpring:message
                                    code="page.text.content.export.all.content"/></b></p></div>
                            <div id="actionPopupContentJSONExport_28"></div>
                        </div>

                        <!-- Export Touchpoint XML Schema Definition xsd -->
                        <div id="actionSpec_29" submitId="29"> <!-- Export tpi.xsd -->
                            <div id="actionTitle_29"><fmtSpring:message code="page.label.confirm.xsd.export"/></div>
                            <div id="actionInfo_29">
                                <p><b>
                                    <fmtSpring:message code="page.text.export.touchpoint.xml.schema.definition"/></b>
                                </p>
                            </div>
                            <div id="actionPopupTpExportXsdExport_29"></div>
                            <div id="actionCloseButton_29"></div>
                        </div>
                    </div>

                    <div id="actionSpec_31" type="simpleConfirm" submitId="31" contentWidth="330px" style="display:none">
                        <!-- Delete attachment -->
                        <div id="actionTitle_31">
                            <fmtSpring:message code="page.label.confirm.delete.attachment"/>
                        </div>
                        <div id="actionInfo_31">
                            <p><b><fmtSpring:message code="page.text.delete.selected.attachments"/></b></p>
                        </div>
                    </div>

                    <!-- POPUP INTERFACE -->
                    <form:hidden path="exportId" id="exportId"/>
                    <form:hidden path="exportName" id="exportName"/>
                    <msgpt:Popup id="actionPopup">
                        <div id="actionPopupInfoFrame">
                            <p id="actionPopupInfo">&nbsp</p>
                        </div>

                        <div id="actionPopupErrorFrame" display="none">
                            <msgpt:Information type="error" dismissible="false">
                                ${msgpt:getMessage('client_messages.clone.system_locale_mismatch')}
                            </msgpt:Information>
                        </div>

                        <div id="actionPopupCloneTouchpointOptions">
                            <p>
                                <fmtSpring:message code="page.text.include.messages"/>:
                                <form:checkbox id="includeMessagesCheckbox" path="includeMessages"
                                               cssClass="checkbox"/>
                            </p>
                            <p>
                                <fmtSpring:message code="page.text.include.all.variables"/>:
                                <form:checkbox id="includeAllVariablesCheckbox" path="includeAllVariables"
                                               cssClass="checkbox"/>
                            </p>
                            <table>
                                <td style="color: #444; vertical-align: middle;"><fmtSpring:message
                                        code="page.label.instance"/>:
                                    &nbsp;
                                </td>
                                <td>
                                    <form:select id="instanceSelect" path="cloneToInstance"
                                                 cssClass="inputL maintainValue"
                                                 cssStyle="min-width: 200px;"
                                                 onchange="validatePopupReq()"
                                                 onkeyup="validatePopupReq()"
                                    >
                                        <c:forEach var="instance" items="${domainInstances}">
                                            <option value="${instance.id}" locale="${instance.systemDefaultLocale.code}"
                                                    <c:if test="${currentInstance.id == instance.id}">selected='selected'</c:if>>${instance.name}</option>
                                        </c:forEach>

                                        <c:forEach items="${subDomains}" var="subDomainEntry">
                                            <c:forEach var="instance" items="${subDomainEntry.value}">
                                                <option value="${instance.id}"
                                                        locale="${instance.systemDefaultLocale.code}">
                                                        ${subDomainEntry.key} &gt; ${instance.name}
                                                </option>
                                            </c:forEach>
                                        </c:forEach>
                                    </form:select>
                                </td>
                            </table>
                            <table>
                                <td style="color: #444; vertical-align: middle;"><fmtSpring:message
                                        code="page.label.data.source.association"/>:
                                    &nbsp;
                                </td>
                                <td>
                                    <form:select id="userSelect" path="useDataCollectionId" cssClass="inputL"
                                                 onchange="validatePopupReq()" onkeyup="validatePopupReq()">
                                        <option id="0" value="0"><fmtSpring:message code="page.text.loading"/></option>
                                    </form:select>
                                </td>
                            </table>
                            <table>
                                <td style="color: #444; vertical-align: middle;"><fmtSpring:message
                                        code="page.label.name"/>:
                                    &nbsp; &nbsp; &nbsp;
                                </td>
                                <td>
                                    <msgpt:InputFilter type="simpleName">
                                        <form:input id="cloneNameInput" cssClass="inputL maintainValue"
                                                    path="cloneTouchpointName"/>
                                    </msgpt:InputFilter>
                                </td>
                            </table>
                            <table>
                                <tbody>
                                <tr>
                                    <td style="color: #444; vertical-align: middle;">
                                        <fmtSpring:message code="page.label.languages"/>:
                                        &nbsp;
                                    </td>

                                    <td>
                                        <div id="languagesSelect_${document.id}"
                                             class="style_multiselect languagesSelect"
                                             style="display: none;">
                                            <c:forEach var="currentLanguage" items="${touchpointLocalesForSync}"
                                                       varStatus="stat">
                                                <div id="langageOption_${document.id}_${currentLanguage.id}"
                                                        <c:if test="${stat.first}">
                                                            disabled="disabled"
                                                            class="disabled"
                                                        </c:if>
                                                >
                                                    <input id="checkbox_language_${currentLanguage.id}"
                                                           name="languagesForSync"
                                                           value="${currentLanguage.id}"
                                                           type="checkbox"
                                                           checked="checked"
                                                            <c:if test="${stat.first}">
                                                                readonly="readonly"
                                                                onclick="return false;"
                                                            </c:if>
                                                           class="style_multiselect_binding maintainValue"/>
                                                    <input type="hidden" name="_languagesForSync"
                                                           value="${currentLanguage.id}"
                                                           class="maintainValue"
                                                    />
                                                    <span><c:out value="${currentLanguage.name}"/></span>
                                                </div>
                                            </c:forEach>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>

                            <table id="cloneOptionsList" style="display: none;">
                                <tbody>
                                    <tr>
                                        <td style="color: #444; vertical-align: middle;">
                                            <fmtSpring:message code="page.label.donot.override.options"/>:
                                            &nbsp;
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div id="cloneOptions" class="style_multiselect cloneOptions">
                                                <c:forEach var="currentOption" items="${cloneOptions}" varStatus="stat">
                                                    <div id="cloneOption_${currentOption.id}">
                                                        <input id="checkbox_cloneOption_${currentOption.id}"
                                                               name="cloneOptions"
                                                               value="${currentOption.id}"
                                                               type="checkbox"
                                                               checked="checked"
                                                               class="style_multiselect_binding maintainValue"/>
                                                        <input type="hidden" name="_cloneOptions"
                                                               value="${currentOption.id}"
                                                               class="maintainValue"
                                                        />
                                                        <span><c:out value="${currentOption.name}"/></span>
                                                    </div>
                                                </c:forEach>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div id="actionPopupDeleteTpOptions">
                            <p>
                                <input type="checkbox" class="removeTpCheckbox"
                                       onclick="changeTpRemoveChecks()"/>
                                    ${xMessagesToDeleteStr}
                            </p>
                            <p>
                                <input type="checkbox" class="removeTpCheckbox"
                                       onclick="changeTpRemoveChecks()"/>
                                    ${xVariantsToDeleteStr}
                            </p>
                            <p>
                                <input type="checkbox" class="removeTpCheckbox"
                                       onclick="changeTpRemoveChecks()"/>
                                    ${xTestsToDeleteStr}
                            </p>
                        </div>
                        <div id="actionPopupExportOptions">
                            <p>
                                <form:checkbox id="includeImagePathOnlyCheckbox" path="includeImagePathOnly"
                                               cssClass="checkbox"/>
                                <fmtSpring:message code="page.text.export.image.path"/>:
                            </p>
                            <p>
                                <b><fmtSpring:message code="page.text.copy.images.to.path.location.specified"/></b>
                            </p>
                            <p>
                                <form:checkbox id="includeAllLanguageLocalesForGlobalContentObjectsCheckbox"
                                               path="includeAllLanguageLocalesForGlobalContentObjects"
                                               cssClass="checkbox"/>
                                <fmtSpring:message code="page.text.export.all.language.locales"/>
                            </p>

                            <c:if test="${hasDataFilesOrDataResources || hasConnectedDataResource || hasSegmentationAnalysisDataResource}">
                                <p><b><fmtSpring:message code="page.text.export.datafiles.and.dataresources"/>:</b></p>
                            </c:if>

                            <c:if test="${hasConnectedDataResource}">
                                <p>
                                    <form:checkbox
                                            id="includeDataFilesAndDataResourcesForConnectedInterviewCheckbox"
                                            path="includeDataFilesAndDataResourcesForConnectedInterview"
                                            cssClass="checkbox"/>
                                    <fmtSpring:message
                                            code="page.text.export.datafiles.and.dataresources.for.connected.interview"/>
                                </p>
                            </c:if>

                            <c:if test="${hasSegmentationAnalysisDataResource}">
                                <p>
                                    <form:checkbox
                                            id="includeDataFilesAndDataResourcesForSegmentationAnalysisCheckbox"
                                            path="includeDataFilesAndDataResourcesForSegmentationAnalysis"
                                            cssClass="checkbox"/>
                                    <fmtSpring:message
                                            code="page.text.export.datafiles.and.dataresources.for.segmentation.analysis"/>
                                </p>
                            </c:if>

                            <c:if test="${hasDataFilesOrDataResources}">
                                <p>
                                    <form:checkbox id="includeDataFilesAndDataResourcesCheckbox"
                                                   path="includeDataFilesAndDataResources" cssClass="checkbox"/>
                                    <fmtSpring:message
                                            code="page.text.export.datafiles.and.dataresources.for.all.other.purposes"/>
                                </p>
                            </c:if>

                            <p>
                                <b><fmtSpring:message code="page.text.when.both.active.and.working.exist"/>:</b>
                            </p>
                            <p>
                                <form:checkbox id="exportActiveCopyWhenBothActiveAndWorkingExist" path="exportActiveCopyWhenBothActiveAndWorkingExist"
                                               cssClass="checkbox"/>
                                <fmtSpring:message code="page.text.export.active.copy"/>
                            </p>

                            <p>
                                <b><fmtSpring:message code="page.text.export.text.styles"/>:</b>
                            </p>
                            <p>
                                <form:checkbox id="exportReferencedTextStylesOnly" path="exportReferencedTextStylesOnly"
                                               cssClass="checkbox"/>
                                <fmtSpring:message code="page.text.export.referenced.text.styles.only"/>
                            </p>
                        </div>

                        <div id="actionPopupContentJSONExport" style="padding: 2px 8px 6px 8px;" >
                            <div align="center">
                                <p>
                                    <form:checkbox id="tryActiveDataIfNoWorkingData"
                                                   path="tryActiveDataIfNoWorkingData"
                                                   cssClass="checkbox"/>
                                    <fmtSpring:message code="page.label.export.active.content.where.no.working.copy.exist"/>
                                </p>
                            </div>

                            <label><span class="labelText"><fmtSpring:message
                                    code="page.label.target.locale"/></span></label>
                            <form:select items="${exportTargetLocales}"
                                         itemLabel="name"
                                         itemValue="id"
                                         cssClass="custom-select custom-select-lg"
                                         id="exportTargetLocaleSelect"
                                         path="exportTargetLocale"/>
                        </div>
                        <div id="actionPopupTpExportXsdExport" style="padding: 2px 8px 6px 8px;" >
                            <table>
                                <tr>
                                    <td>
                                        <div>
                                            <msgpt:InputFilter type="filename">
                                                <form:input id="touchpointXsdName" cssClass="inputXXL maintainValue"
                                                            path="touchpointXsdName"/>
                                            </msgpt:InputFilter>
                                        </div>
                                    </td>
                                    <td>
                                        <div id="downloadTouchpointXSDBtn"
                                             class="ctionBtn_roundAll actionBtn detailTip fa-mp-container"
                                             title="|<div class='detailTipText'>${msgpt:getMessage('page.label.download.export.XSD')}</div>">
                                            <i class="far fa-download"></i>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div id="actionPopupFileSelect">
                            <br>
                            <div class="formControl">
                                <label><span class="labelText"><fmtSpring:message
                                        code="page.label.template"/></span></label>
                                <div class="controlWrapper">
                                    <fmtSpring:bind path="command.templateFile">
                                        <input type="file" name="${status.expression}" size="50" class="inputFile"/>
                                    </fmtSpring:bind>
                                </div>
                            </div>
                        </div>
                        <div id="actionPopupCustomButtons" class="actionPopupButtonsContainer">
                            <span id="customCancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel_Local();"
                                                                            label="page.label.cancel"/></span>
                            <span id="removeBtnEnabled" style="display: none;"><msgpt:Button
                                    URL="javascript:submitAction(11, this);" label="page.label.delete"
                                    primary="true"/></span>
                            <span id="removeBtnDisabled"><msgpt:Button URL="#" label="page.label.delete"
                                                                       disabled="true"/></span>
                        </div>
                        <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                            <div style="display: table; margin: 0 auto;">
                                <span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel_Local();"
                                                                          label="page.label.cancel"/></span>
                                <span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                  label="page.label.cancel"
                                                                                                  disabled="true"/></span>
                                <span id="continueBtnEnabled"><msgpt:Button URL="#" primary="true"
                                                                            label="page.label.continue"/></span>
                                <span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                    label="page.label.continue"
                                                                                                    disabled="true"/></span>
                            </div>
                        </div>
                        <div id="actionPopupCloseButton" class="actionPopupButtonsContainer">
                            <msgpt:Button URL="javascript:actionCancel();" label="page.label.close"/>
                        </div>
                    </msgpt:Popup>

                    <!-- NO TOUCHPOINTS -->
                    <c:if test="${noTouchpoints}">
                        <div style="min-height: 400px;">
                            <div class="InfoSysContainer_info" style="margin-top: 20px;">

                                <div style="display: inline-block; padding-right: 12px; position: relative; top: -6px">
                                    <fmtSpring:message code="page.text.add.or.import.touchpoint"/>
                                </div>
                                <div style="display: inline-block;">
                                    <table cellspacing="0" cellpadding="0" border="0" style="width: 300px;">
                                        <tr>
                                            <td
                                                    style="padding: 0px; vertical-align: middle; white-space: nowrap;">
                                                <input title="${msgpt:getMessage('page.label.IMPORT.TOUCHPOINT')}"
                                                       type="button" id="importTouchpointBtn"
                                                       onclick="javascript:javascriptHref('touchpoint_import.form');"
                                                       style="display: none;"/>
                                            </td>
                                            <c:if test="${not empty channels}">
                                            <td
                                                    style="padding: 0px; padding-left: 10px; vertical-align: middle;">
                                                <input title="${msgpt:getMessage('page.label.ADD.TOUCHPOINT')}"
                                                       type="button" id="addTouchpointBtn"
                                                       onclick="javascript:createTouchpointAction();"
                                                       style="display: none;"/>
                                            </td>
                                            </c:if>
                                            <td width="98%"/>
                                        <tr>
                                    </table>
                                </div>

                            </div>
                        </div>

                        <form:errors path="*">
                            <msgpt:Information errorMsgs="${messages}" type="error"/>
                        </form:errors>
                    </c:if>

                    <c:if test="${not noTouchpoints and trashTpContext }">
                        <div class="InfoSysContainer_info">
                            <i class="fa icon fa-info-circle" aria-hidden="true"></i>
                            <p>
                                <fmtSpring:message code="page.text.trash.touchpoint.does.not.apply"/>
                            </p>
                        </div>
                    </c:if>

                </form:form>

            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
    </msgpt:BodyNew>
</msgpt:Html5>