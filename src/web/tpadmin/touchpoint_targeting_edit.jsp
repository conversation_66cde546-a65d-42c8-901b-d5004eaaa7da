<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.model.workflow.Workflow" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">


        <msgpt:CalendarIncludes/>

        <msgpt:Script src="includes/javascript/checkboxSelect.js"/>

        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:Script src="includes/javascript/popupActions.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/ruleBuilder/jquery.ruleValueManager.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/ruleBuilder/ruleBuilder.css"/>

        <msgpt:Stylesheet href="includes/themes/commoncss/targeting.css"/>
        <msgpt:Script src="includes/javascript/targetable_object_edit.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>
        <style type="text/css">

            .targetGroupContentContainer {
                border-right: solid 1px #dadada;
                border-bottom: solid 1px #dadada;
            }

            .addTargetGroupInputContainer,
            .targetGroupSearchContainer > .smooth-scroll-wrapper,
            .targetGroupSearchInputContainer {
                border-left: solid 1px #dadada;
            }

        </style>
        <msgpt:Script>
            <script>
                // TARGETABLE OBJECT EDIT data
                var dateValueTypesJSON = ${dateValueTypesJSON};
                var documentId = ${documentId};

                function createTargetGroup() {
                    $('#newTargetGroupBtn').iFramePopup($.extend({
                        src: context + "/dataadmin/target_group_edit.form",
                        displayOnInit: true,
                        id: "targetGroupFrame",
                        appliedParams: {'tk': "${param.tk}", 'touchpointTargetingId': '${param.touchpointTargetingId}'},
                        onSave: function () {
                            getTopFrame().location.reload();
                        }
                    }, iFramePopup_fullFrameAttr));
                }

                $(function () {

                    $('.workflowTabsContainer').hide();

                    $("input:button").styleActionElement();
                    setPrimaryBtn('save');

                    common.refreshParentIframeHeight();

                    $('.workflowButtonsContainer').removeClass('workflowButtonsContainer');

                    //common.setFixedSidebar($('.targetGroupSearchContainer'), -1, false, null);
                    // TODO: Pending to change sidebar to animated version outside the iframe

                });
            </script>
        </msgpt:Script>

    </msgpt:HeaderNew>

    <msgpt:BodyNew theme="minimal">

        <msgpt:BannerNew edit="true" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>

        <msgpt:NewNavigationTabs edit="true" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>

        <msgpt:LowerContainer fullPanel="true">
            <msgpt:ContentPanel>
                <msgpt:WorkflowTab parameter="touchpointTargetingId"
                                   type="<%= Workflow.TOUCHPOINT_TARGETING_WORKFLOW_ID %>" edit="true">
                    <form:form modelAttribute="command" method="post">
                        <div class="mrgn-bottom-lv3">
                            <h3 class="mrgn-bottom-lv3">
                                <fmtSpring:message code="page.label.touchpoint.targeting"/> (<c:out
                                    value="${document.name}"/>)
                            </h3>
                            <div class="row no-gutters">
                                <div class="targetGroupSearchContainer col-3of12">
                                    <div class="targetGroupSearchInputContainer">
                                        <div style="display: inline-block; position: relative;">
                                            <input id="targetGroupSearchInput"
                                                   class="input targetGroupSearchInput"
                                                   value="${msgpt:getMessage('page.text.search.for.target.groups')}"/>
                                            <div id="advancedSearchIcon"
                                                 title="|<div class='detailTipText'>${msgpt:getMessage('page.label.advanced.search')}</div>"
                                                 class="advancedSearchIconDiv persistedClass detailTip"
                                                 style="display: inline-block; position: absolute;">
                                                <i class="fa fa-crosshairs fa-med fa-mp-style"
                                                   style="position: relative; top: 1px;"></i>
                                            </div>
                                            <i class="searchIconDiv fa fa-med fa-search fa-mp-style"></i>
                                        </div>

                                    </div>
                                    <div>
                                        <div id="targetGroupListContainerLoading" style="padding: 8px">
                                            <i class="fas fa-spin fa-spinner"></i>
                                            <span style="margin-left: 8px"><fmtSpring:message code="page.label.loading.target.groups" /></span>
                                        </div>
                                        <div id="targetGroupListContainerNone" style="padding: 8px; display: none">
                                            <span><fmtSpring:message code="page.label.target.groups.none.found" /></span>
                                        </div>
                                        <div class="targetGroupListContainer">
                                        </div>
                                    </div>
                                    <msgpt:IfAuthGranted authority="ROLE_TARGETING_EDIT">
                                        <div class="addTargetGroupInputContainer">
                                            <input title="${msgpt:getMessage('page.label.add.target.group')}"
                                                   type="button" id="newTargetGroupBtn"
                                                   onclick="createTargetGroup()" style="display: none;"/>
                                        </div>
                                    </msgpt:IfAuthGranted>
                                </div>
                                <div class="targetGroupContentContainer col-9of12">
                                    <!-- TARGETING INTERFACE: REFERENCE DATA -->
                                    <input type="hidden" id="paramSimple" value="${paramSimple}"/>
                                    <input type="hidden" id="paramSimpleRange" value="${paramSimpleRange}"/>
                                    <input type="hidden" id="paramDate" value="${paramDate}"/>
                                    <input type="hidden" id="paramDateRange" value="${paramDateRange}"/>
                                    <!-- END TARGETING INTERFACE: REFERENCE DATA -->
                                    <!-- Included, Include Extended, Exclude Target Groups -->
                                    <c:forEach var="currentIndex" items="${command.mapIndex}">
                                        <c:set var="tableInd" value="${command.tableIndicator[currentIndex]}"/>
                                        <div id="targetGroupsCategoryContainer_${currentIndex}"
                                             class="targetGroupsCategoryContainer"
                                             style="${currentIndex == 2 && fn:length(command.targetGroupsMap[currentIndex]) == 0 ? 'display:none;' : ''}">
                                            <div class="targetGroupRelationshipToggleContainer">
                                                <div class="targetGroupHeaderLabel">
                                                    <c:out value="${command.tableLabel[currentIndex]}"/>
                                                </div>
                                                <div class="targetGroupRelationshipToggleDisplay">
                                                    <div class="row align-items-middle">
                                                        <div class="col-auto pddng-horizontal-lv0"><form:checkbox
                                                                id="targetGroupRelationshipToggle_${currentIndex}"
                                                                class="targetGroupRelationshipToggle"
                                                                path="targetGroupRelationship[${currentIndex}]"
                                                                title="${msgpt:getMessage('page.label.at.least.one')};${msgpt:getMessage('page.label.all')}"/></div>
                                                        <div class="col-auto"><fmtSpring:message
                                                                code="page.text.of.these.target.groups.must.pass"/>&nbsp;<fmtSpring:message
                                                                code="${currentIndex == 3 ? 'page.text.disqualify' : 'page.text.qualify'}"/>&nbsp;<fmtSpring:message
                                                                code="page.text.asset"/>:
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="targetableDetailsContainer">
                                                <fmtSpring:hasBindErrors name="command">
                                                    <div class="pddng-lv2">
                                                        <form:errors path="*">
                                                            <msgpt:Information errorMsgs="${messages}" type="error"/>
                                                        </form:errors>
                                                    </div>
                                                </fmtSpring:hasBindErrors>
                                                <div id="playToAllDiv" class="pddng-lv3"
                                                     style="display: none;">
                                                    <!-- Play to all -->
                                                    <div id="info_playToAll"
                                                         class="InfoSysContainer_question mrgn-bottom-lv0">
                                                        <i class="fa icon fa-exclamation-circle" aria-hidden="true"></i>
                                                        <p><fmtSpring:message code="page.text.playtoallcustomers"/></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="targetGroupContainer">
                                                <div id="info_noActiveTargetGroups_${currentIndex}"
                                                     class="InfoSysContainer_info info_noActiveTargetGroups"
                                                     style="display: none;">
                                                    <i class="fa icon fa-info-circle" aria-hidden="true"></i>
                                                    <p><fmtSpring:message
                                                            code="page.text.drag.and.drop.target.group.to.extend.targeting"/></p>
                                                </div>
                                                <div class="droppable-item-placement mrgn-bottom-lv4">
                                                    <i class="mp-icon fa fa-hand-o-right" aria-hidden="true"></i>
                                                    &nbsp;
                                                </div>
                                                <div class="targetGroupItemsContainer">
                                                    <!-- FOR EACH: TARGET GROUP -->
                                                    <c:forEach var="targetGroupWrapper"
                                                               items="${command.targetGroupsMap[currentIndex]}"
                                                               varStatus="targetGroupStat">
                                                        <c:set var="targetGroup"
                                                               value="${targetGroupWrapper.targetGroup}"/>
                                                        <c:if test="${not empty targetGroup}"> <!-- Validation: Handle removed targeting -->
                                                            <c:set var="instance"
                                                                   value="${targetGroupWrapper.instance}"/>
                                                            <div id="targetGroupContainer_${targetGroup.id}"
                                                                 class="targetGroupDisplayContainer stageContainer">
                                                                <table width="100%" cellspacing="0"
                                                                       cellpadding="0" border="0"
                                                                       class="tableOutline targetGroupTable">
                                                                    <tr class="targetGroupHeaderContainer">

                                                                        <!-- Target Group Name -->
                                                                        <td width="99%" align="left"
                                                                            class="tableIndicatorCol cellTopLeft"
                                                                            style="border-right: none;">
                                                                            <div class="stageLabel">
                                                                                <c:out value="${targetGroup.name}"/>
                                                                            </div>

                                                                            <!-- Target Group Binding -->
                                                                            <form:input
                                                                                    path="targetGroupsMap[${currentIndex}][${targetGroupStat.index}].targetGroupId"
                                                                                    cssClass="targetGroupIdInput"
                                                                                    cssStyle="display: none;"/>
                                                                            <form:input
                                                                                    path="targetGroupsMap[${currentIndex}][${targetGroupStat.index}].instanceId"
                                                                                    cssClass="instanceIdInput"
                                                                                    cssStyle="display: none;"/>
                                                                        </td>

                                                                        <!-- Target Group Actions: Remove -->
                                                                        <td class="tableIndicatorCol cellTopRight"
                                                                            style="border-right: none;">
                                                                            <div class="targetGroupActionsContainer"
                                                                                 style="display:none;">
                                                                                <div class="removeIcon">
                                                                                    <i class="fa fa-times"
                                                                                       aria-hidden="true"></i>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>

                                                                        <!-- Target Group Display -->
                                                                        <td align="left"
                                                                            class="tableContentCol cellBottomLeft cellBottomRight"
                                                                            colspan="2"
                                                                            style="border-bottom: none; vertical-align: middle;">

                                                                            <table cellspacing="0"
                                                                                   cellpadding="0" border="0"
                                                                                   class="innerContentTable rulesTable">
                                                                                <!-- FOR EACH: RULE -->
                                                                                <c:forEach var="currentItem"
                                                                                           items="${instance.conditionItemsSorted}">
                                                                                    <c:set var="element"
                                                                                           value="${currentItem.conditionElement}"/>

                                                                                    <tr class="ruleNameDisplayContainer">
                                                                                        <td align="left"
                                                                                            style="padding: 4px 4px 4px 12px;vertical-align: middle;"
                                                                                            colspan="3">
                                                                                            <c:out value="${element.name}"/>
                                                                                        </td>
                                                                                    </tr>

                                                                                    <!-- FOR EACH: CONDITION -->
                                                                                    <c:forEach
                                                                                            var="currentItemValue"
                                                                                            items="${currentItem.conditionItemValues}">
                                                                                        <c:set var="subelement"
                                                                                               value="${currentItemValue.conditionSubelement}"/>

                                                                                        <c:if test="${msgpt:containsValue(conditionItemValueMap[currentItem.id], currentItemValue.id)}">

                                                                                            <tr class="conditionDisplayContainer">

                                                                                                <td width="1%"
                                                                                                    class="conditionBindingContainer"
                                                                                                    style="padding: 4px 4px 4px 12px; vertical-align: middle;">

                                                                                                </td>

                                                                                                <td align="left"
                                                                                                    style="padding: 4px 4px 4px 12px; vertical-align: middle;">
                                                                                                    <!-- Condition Name -->
                                                                                                    <span class="conditionNameContainer">
                                                                                                <c:out value="${subelement.name}"/>:
                                                                                            </span>
                                                                                                </td>

                                                                                                <td align="left"
                                                                                                    style="padding: 4px 8px 4px 4px; vertical-align: middle;">

                                                                                                    <div style="display: inline-block; vertical-align: middle;">
                                                                                                <span>
                                                                                                    <fmtSpring:message
                                                                                                            code="page.label.If"/> <span
                                                                                                        style="padding: 0px 2px;"
                                                                                                        class="conditionVariableNameContainer">
                                                                                                        <c:out value="${not empty subelement.dataElementVariable ? subelement.dataElementVariable.displayName : msgpt:getMessage('page.text.no.variable.brackets')}"/>
                                                                                                    </span>
                                                                                                </span>
                                                                                                        <span class="conditionComparatorContainer">
                                                                                                    <fmtSpring:message
                                                                                                            code="${subelement.dataComparison.name}"/>
                                                                                                </span>
                                                                                                    </div>

                                                                                                    <!-- Condition Value -->
                                                                                                    <!-- command.targetGroupsMap[currentIndex][targetGroupStat.index].parameterizeRuleMap[currentItem] -->
                                                                                                    <c:choose>
                                                                                                        <c:when test="${instance.conditionParamMap[currentItem] == true && subelement.parameterized}">

                                                                                                            <div id="ruleConditionBindingContainer_${currentIndex}_${element.id}_${subelement.id}"
                                                                                                                 class="ruleConditionValueContainer"
                                                                                                                 style="display: inline-block; vertical-align: middle;">
                                                                                                                <c:set var="subElementType"
                                                                                                                       value="${conditionSubelementValueTypes[subelement.id]}"/>
                                                                                                                <div id="ruleConditionBinding_${currentIndex}_${element.id}_${subelement.id}"
                                                                                                                     class="conditionValueBindingContainer"
                                                                                                                     isRangeValue="${subElementType eq paramSimpleRange || subElementType eq paramDateRange}"
                                                                                                                     isDateValue="${subElementType eq paramDate || subElementType eq paramDateRange}">
                                                                                                                    <!-- Value String Binding -->
                                                                                                                    <form:input
                                                                                                                            id="conditionValueString_${currentIndex}_${element.id}_${subelement.id}"
                                                                                                                            cssClass="variableComparedToValue"
                                                                                                                            path="targetGroupsMap[${currentIndex}][${targetGroupStat.index}].conditionSubelementValues[${subelement.id}]"
                                                                                                                            cssStyle="display:none;"/>
                                                                                                                </div>
                                                                                                            </div>

                                                                                                        </c:when>
                                                                                                        <c:otherwise>

                                                                                                            <div id="ruleConditionStaticContainer_${currentIndex}_${element.id}_${subelement.id}"
                                                                                                                 class="ruleConditionStaticContainer"
                                                                                                                 style="display: inline-block; vertical-align: middle;">
                                                                                                                <c:choose>
                                                                                                                    <c:when test="${subelement.parameterized}">
                                                                                                                        <c:set var="currentDisplayValue"
                                                                                                                               value="${command.targetGroupsMap[currentIndex][targetGroupStat.index].conditionSubelementValues[subelement.id]}"/>
                                                                                                                        <!-- Hidden binding required to preserve value on validation (lazy loading) -->
                                                                                                                        <form:hidden
                                                                                                                                path="targetGroupsMap[${currentIndex}][${targetGroupStat.index}].conditionSubelementValues[${subelement.id}]"/>
                                                                                                                    </c:when>
                                                                                                                    <c:otherwise>
                                                                                                                        <c:set var="currentDisplayValue"
                                                                                                                               value="${subelement.dataElementDisplayValue}"/>
                                                                                                                    </c:otherwise>
                                                                                                                </c:choose>
                                                                                                                <span class="ruleConditionDisplayValueContainer"
                                                                                                                      style="padding: 0px 2px;">
                                                                                                            <c:choose>
                                                                                                                <c:when test="${subelement.isValueComparison}">
                                                                                                                    '<c:out
                                                                                                                        value="${currentDisplayValue}"
                                                                                                                        escapeXml="false"/>'
                                                                                                                </c:when>
                                                                                                                <c:otherwise>
                                                                                                                    <c:out value="${currentDisplayValue}"
                                                                                                                           escapeXml="false"/>
                                                                                                                </c:otherwise>
                                                                                                            </c:choose>
                                                                                                        </span>
                                                                                                            </div>

                                                                                                        </c:otherwise>
                                                                                                    </c:choose>

                                                                                                    <c:if test="${subelement.filterCondition != null}">
                                                                                                        <div style="display: inline-block; vertical-align: middle;">
                                                                                                            <fmtSpring:message
                                                                                                                    code="page.label.WHEN"/>
                                                                                                            <c:out value="${subelement.filterCondition.displayValue}"
                                                                                                                   escapeXml="false"/>
                                                                                                        </div>
                                                                                                    </c:if>

                                                                                                </td>

                                                                                            </tr>

                                                                                            <c:if test="${element.conditionType.id != 1}">
                                                                                                <tr class="conditionRelationshipIndicator"
                                                                                                    style="display: none;">
                                                                                                    <td align="left"
                                                                                                        colspan="3">
                                                                                                        <div style="padding-left: 24px; font-size: 11px;">
                                                                                                            <fmtSpring:message
                                                                                                                    code="${element.conditionType.id == 2 ? 'page.label.and' : 'page.label.or'}"/></div>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </c:if>

                                                                                        </c:if>
                                                                                    </c:forEach> <!-- foreach: CONDITION -->

                                                                                    <tr class="ruleRelationshipIndicator"
                                                                                        style="display: none;">
                                                                                        <td align="left"
                                                                                            colspan="3">
                                                                                            <div style="padding-left: 4px; font-size: 11px;">
                                                                                                <fmtSpring:message
                                                                                                        code="${targetGroup.conditionRelationship == 2 ? 'page.label.and' : 'page.label.or'}"/></div>
                                                                                        </td>
                                                                                    </tr>

                                                                                </c:forEach>
                                                                                <!-- foreach: RULE -->
                                                                            </table>

                                                                        </td>
                                                                    </tr>

                                                                </table>
                                                            </div>

                                                            <div class="targetGroupRelationshipIndicator"
                                                                 style="display: none;">

                                                            </div>

                                                        </c:if>
                                                    </c:forEach>    <!-- FOR EACH: TARGET GROUP -->

                                                </div>            <!-- .targetGroupItemsContainer -->

                                            </div>            <!-- .targetGroupContainer -->

                                        </div>
                                        <!-- #targetGroupsCategoryContainer_TABLEIND -->

                                    </c:forEach>    <!-- currentIndex -->

                                </div>  <!-- .targetGroupContentContainer -->
                            </div>
                        </div>
                        <!-- .contentTableIframe -->
                        <!-- TEMPLATES: START -->
                        <div id="TEMPLATE_TARGET_GROUP" style="display: none;">

                            <!-- TEMPLATE: TARGET_GROUP: START -->
                            <div id="targetGroupContainer_TARGET_GROUP_ID"
                                 class="targetGroupDisplayContainer stageContainer">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0" class="tableOutline">
                                    <tr class="targetGroupHeaderContainer">

                                        <!-- Target Group Name -->
                                        <td width="99%" align="left" class="tableIndicatorCol cellTopLeft"
                                            style="border-right: none;">
                                            <div class="stageLabel">TARGET_GROUP_NAME</div>
                                            <!-- Target Group Binding -->
                                            <input id="BIND_MASKtargetGroupsMapGROUP_INDEXGROUP_ITEM_INDEX.targetGroupId"
                                                   class="targetGroupIdInput inputEnabled" type="text"
                                                   value="TARGET_GROUP_ID" style="display: none;"
                                                   name="BIND_MASKtargetGroupsMap[GROUP_INDEX][GROUP_ITEM_INDEX].targetGroupId">
                                            <input id="BIND_MASKtargetGroupsMapGROUP_INDEXGROUP_ITEM_INDEX.instanceId"
                                                   class="instanceIdInput inputEnabled" type="text"
                                                   value="TARGET_GROUP_INSTANCE_ID" style="display: none;"
                                                   name="BIND_MASKtargetGroupsMap[GROUP_INDEX][GROUP_ITEM_INDEX].instanceId">
                                        </td>

                                        <!-- Target Group Actions: Remove -->
                                        <td class="tableIndicatorCol cellTopRight" style="border-right: none;">
                                            <div class="targetGroupActionsContainer" style="display:none;">
                                                <div class="removeIcon">
                                                    <i class="fa fa-times" aria-hidden="true"></i>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>

                                        <!-- Target Group Display -->
                                        <td class="tableContentCol cellBottomLeft cellBottomRight" colspan="2"
                                            style="border-bottom: none; vertical-align: middle;">
                                            <table cellspacing="0" cellpadding="0" border="0"
                                                   class="innerContentTable rulesTable">

                                            </table>
                                        </td>

                                    </tr>
                                </table>
                            </div>            <!-- .targetGroupDisplayContainer -->

                            <div class="targetGroupRelationshipIndicator" style="display: none;"></div>
                            <!-- TEMPLATE: RULE: END -->

                        </div>

                        <div id="TEMPLATE_CONDITION" style="display: none;">

                            <table class="templateTable">
                                <!-- TEMPLATE: CONDITION: START -->
                                <tr class="conditionDisplayContainer">

                                    <td width="1%" class="conditionBindingContainer"
                                        style="padding: 4px 4px 4px 12px; vertical-align: middle;">

                                    </td>

                                    <td align="left" style="padding: 4px 4px 4px 12px; vertical-align: middle;">
                                        <!-- Condition Name -->
                                        <span class="conditionNameContainer">
									CONDITION_NAME:
								</span>
                                    </td>

                                    <td align="left" style="padding: 4px 8px 4px 4px; vertical-align: middle;">

                                        <div style="display: inline-block; vertical-align: middle;">
									<span>
										<fmtSpring:message code="page.label.If"/> <span style="padding: 0px 2px;"
                                                                                        class="conditionVariableNameContainer">
											CONDITION_VARIABLE_NAME
										</span>
									</span>
                                            <span class="conditionComparatorContainer">
										CONDITION_COMPARATOR_NAME
									</span>
                                        </div>

                                        <!-- Condition Value -->
                                        <!-- CHOOSE ONE: ruleConditionValueContainer or ruleConditionStaticContainer -->
                                        <div id="ruleConditionBindingContainer_GROUP_INDEX_RULE_ID_CONDITION_ID"
                                             class="ruleConditionValueContainer"
                                             style="display: inline-block; vertical-align: middle;">

                                            <div id="BIND_MASKruleConditionBinding_GROUP_INDEX_RULE_ID_CONDITION_ID"
                                                 class="conditionValueBindingContainer"
                                                 isRangeValue="IS_RANGE_VALUE"
                                                 isDateValue="IS_DATE_VALUE">
                                                <!-- Value String Binding -->
                                                <input id="conditionValueString_GROUP_INDEX_RULE_ID_CONDITION_ID"
                                                       class="variableComparedToValue"
                                                       name="BIND_MASKtargetGroupsMap[GROUP_INDEX][GROUP_ITEM_INDEX].conditionSubelementValues[CONDITION_ID]"
                                                       style="display:none;"/>
                                            </div>
                                            <div id="ruleConditionParameterizedContainer_GROUP_INDEX_RULE_ID_CONDITION_ID"
                                                 class="ruleConditionParameterizedContainer" style="display: none;">
                                                <fmtSpring:message code="page.label.user.specified.values"/>
                                            </div>
                                        </div>
                                        <div id="ruleConditionStaticContainer_GROUP_INDEX_RULE_ID_CONDITION_ID"
                                             class="ruleConditionStaticContainer"
                                             style="display: inline-block; vertical-align: middle;">
                                            <input style="display: none;"
                                                   name="BIND_MASKtargetGroupsMap[GROUP_INDEX][GROUP_ITEM_INDEX].conditionSubelementValues[CONDITION_ID]"
                                                   value="CONDITION_DISPLAY_RAW_VALUE"/>
                                            <span class="ruleConditionDisplayValueContainer" style="padding: 0px 2px;">
										CONDITION_DISPLAY_VALUE
									</span>
                                        </div>

                                        <div style="display: inline-block; vertical-align: middle;">
                                            CONDITION_FILTER_DISPLAY_VALUE
                                        </div>

                                    </td>

                                </tr>

                                <!-- IF: CONDITION TYPE 1 or 2 -->
                                <tr class="conditionRelationshipIndicator" style="display: none;">
                                    <td align="left" colspan="3">
                                        <div style="padding-left: 24px; font-size: 11px;">CONDITION_RELATIONSHIP</div>
                                    </td>
                                </tr>
                            </table>

                        </div>
                        <!-- TEMPLATE: CONDITION: END -->

                        <!-- TEMPLATES: END -->

                    </form:form>
                </msgpt:WorkflowTab>

            </msgpt:ContentPanel>
        </msgpt:LowerContainer>

    </msgpt:BodyNew>
</msgpt:Html5>