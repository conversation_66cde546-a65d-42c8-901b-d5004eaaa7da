<?xml version="1.0" encoding="UTF-8"?>
<SecurityMetadata>

	<Users>
		<!-- 
			UserId=1 is RESERVED for the Default Super User
		-->
		
		<!-- 
		<User id="2">
			<FirstName>Content</FirstName>
			<LastName>Author</LastName>
			<Email><EMAIL></Email>
			<Username><EMAIL></Username>
			<Password>Prinova1</Password>
			<MasterAdminStatus>0</MasterAdminStatus>
		</User>
		<User id="3">
			<FirstName>Content</FirstName>
			<LastName>Tester</LastName>
			<Email><EMAIL></Email>
			<Username><EMAIL></Username>
			<Password>Prinova1</Password>
			<MasterAdminStatus>0</MasterAdminStatus>
		</User>
		<User id="4">
			<FirstName>Content</FirstName>
			<LastName>Approver</LastName>
			<Email><EMAIL></Email>
			<Username><EMAIL></Username>
			<Password>Prinova1</Password>
			<MasterAdminStatus>0</MasterAdminStatus>
		</User>
		<User id="5">
			<FirstName>Touchpoint</FirstName>
			<LastName>Admin</LastName>
			<Email><EMAIL></Email>
			<Username><EMAIL></Username>
			<Password>Prinova1</Password>
			<MasterAdminStatus>0</MasterAdminStatus>
		</User>
		-->
		
		<User id="900">
			<FirstName>Pod</FirstName>
			<LastName>Admin</LastName>
			<Email><EMAIL></Email>
			<Username><EMAIL></Username>
			<Password>$Prin4v$44</Password>
			<defaultTabId>8</defaultTabId>
			<MasterAdminStatus>2</MasterAdminStatus>
			<workgroupId>0</workgroupId>
		</User>
		<User id="901">
			<FirstName>Messagepoint</FirstName>
			<LastName>Web Services</LastName>
			<Email><EMAIL></Email>
			<Username><EMAIL></Username>
			<Password>Prinovamaster1</Password>
			<defaultTabId>8</defaultTabId>
			<MasterAdminStatus>0</MasterAdminStatus>
			<workgroupId>0</workgroupId>
		</User>
		<User id="999">
			<FirstName>Domain</FirstName>
			<LastName>Admin</LastName>
			<Email><EMAIL></Email>
			<Username><EMAIL></Username>
			<Password>%Prin5v%55</Password>
			<defaultTabId>8</defaultTabId>
			<MasterAdminStatus>1</MasterAdminStatus>
			<workgroupId>0</workgroupId>
		</User>
	</Users>
	

	<PermissionCategories>
		<!-- Task -->
		<PermissionCategory id="1">
			<Name>page.label.all.tasks</Name>
			<Description>The Permission Category including all the permissions regarding All Tasks.</Description>
			<Priority>1</Priority>
			<Permissions>
				<Permission refid="39" /> <!-- ROLE_TASK_REASSIGN -->
				<Permission refid="40" /> <!-- ROLE_TASK_SETUP -->
				<Permission refid="43" /> <!-- ROLE_TASK_VIEW_ALL -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="2">
			<Name>page.label.my.tasks</Name>
			<Description>The Permission Category including all the permissions regarding My Tasks.</Description>
			<Priority>2</Priority>
			<Permissions>
				<Permission refid="42" /> <!-- ROLE_TASK_VIEW_MY -->
				<Permission refid="41" /> <!-- ROLE_TASK_EDIT -->
			</Permissions>
		</PermissionCategory>		
		<PermissionCategory id="3">
			<Name>page.label.messages</Name>
			<Description>The Permission Category including all the permissions regarding Messages.</Description>
			<Priority>5</Priority>
			<Permissions>
				<Permission refid="37" /> <!-- ROLE_MESSAGE_VIEW_ALL -->
				<Permission refid="7" />  <!-- ROLE_MESSAGE_EDIT -->
				<Permission refid="22" /> <!-- ROLE_MESSAGE_APPROVE_EDIT -->
				<Permission refid="65" /> <!-- ROLE_MESSAGE_REASSIGN -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="4">
			<Name>page.label.reports</Name>
			<Description>The Permission Category including all the permissions regarding Reports.</Description>
			<Priority>8</Priority>
			<Permissions>
				<Permission refid="16" /> <!-- ROLE_REPORT_VIEW -->
				<Permission refid="17" /> <!-- ROLE_REPORT_EDIT -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="5">
			<Name>page.label.testing</Name>
			<Description>The Permission Category including all the permissions regarding Testing.</Description>
			<Priority>9</Priority>
			<Permissions>
				<Permission refid="26" /> <!-- ROLE_TEST_VIEW -->
				<Permission refid="27" /> <!-- ROLE_TEST_EDIT -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="7">
			<Name>page.label.data</Name>
			<Description>The Permission Category including all the permissions regarding Data.</Description>
			<Priority>14</Priority>
			<Permissions>
				<Permission refid="51" /> <!-- ROLE_TOUCHPOINT_DATA_LIST -->
				<Permission refid="54" /> <!-- ROLE_TOUCHPOINT_DATA_EDIT -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="8">
			<Name>page.label.targeting</Name>
			<Description>The Permission Category including all the permissions regarding Targeting.</Description>
			<Priority>15</Priority>
			<Permissions>
				<Permission refid="28" /> <!-- ROLE_TARGETING_EDIT -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="9">
			<Name>page.label.settings</Name>
			<Description>The Permission Category including all the permissions regarding Settings.</Description>
			<Priority>16</Priority>
			<Permissions>
				<Permission refid="4" />  <!-- ROLE_SYSTEM_ADMIN -->
				<Permission refid="55" /> <!-- ROLE_ADMIN_SETTINGS_EDIT -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="10">
			<Name>page.label.tenants</Name>
			<Description>The Permission Category including all the permissions regarding Tenants.</Description>
			<Priority>17</Priority>
			<Permissions>
				<Permission refid="52" /> <!-- ROLE_ADMIN_TENANT_VIEW -->
				<Permission refid="53" /> <!-- ROLE_ADMIN_TENANT_EDIT -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="11">
			<Name>page.label.roles</Name>
			<Description>The Permission Category including all the permissions regarding Roles.</Description>
			<Priority>18</Priority>
			<Permissions>
				<Permission refid="46" /> <!-- ROLE_ADMIN_ROLE_VIEW -->
				<Permission refid="47" /> <!-- ROLE_ADMIN_ROLE_EDIT -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="12">
			<Name>page.label.users</Name>
			<Description>The Permission Category including all the permissions regarding Users.</Description>
			<Priority>19</Priority>
			<Permissions>
				<Permission refid="44" /> <!-- ROLE_ADMIN_USER_VIEW -->
				<Permission refid="45" /> <!-- ROLE_ADMIN_USER_EDIT -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="13">
			<Name>page.label.rules</Name>
			<Description>The Permission Category including all the permissions regarding Rules.</Description>
			<Priority>12</Priority>
			<Permissions>
				<Permission refid="58" /> <!-- ROLE_RULE_VIEW -->
				<Permission refid="59" /> <!-- ROLE_RULE_EDIT -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="15">
			<Name>page.label.simulation</Name>
			<Description>The Permission Category including all the permissions regarding Simulations.</Description>
			<Priority>10</Priority>
			<Permissions>
				<Permission refid="62" /> <!-- ROLE_SIMULATION_VIEW -->
				<Permission refid="63" /> <!-- ROLE_SIMULATION_EDIT -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="16">
			<Name>page.label.archive.messages</Name>
			<Description>The Permission Category including all the permissions regarding Message activation.</Description>
			<Priority>4</Priority>
			<Permissions>
				<Permission refid="64" /> <!-- ROLE_MESSAGE_ACTIVATION -->
			</Permissions>
		</PermissionCategory>	

		<!-- Priority Administration -->
		<PermissionCategory id="48">
			<Name>page.label.prioritize.messages</Name>
			<Description>The Permission Category including permission for Message Priority.</Description>
			<Priority>6</Priority>
			<Permissions>
				<Permission refid="150" /> <!-- ROLE_PRIORITY_ADMIN -->
			</Permissions>
		</PermissionCategory>
		
		<PermissionCategory id="18">
			<Name>page.label.workgroups</Name>
			<Description>The Permission Category including all the permissions regarding Workgroups.</Description>
			<Priority>20</Priority>
			<Permissions>
				<Permission refid="66" /> <!-- ROLE_WORKGROUP_VIEW -->
				<Permission refid="67" /> <!-- ROLE_WORKGROUP_EDIT -->
			</Permissions>
		</PermissionCategory>

		<PermissionCategory id="22">
			<Name>page.label.switch.tenants</Name>
			<Description>The Permission Category including view permission for an user to assume tenant supervisor.</Description>
			<Priority>22</Priority>
			<Permissions>
				<Permission refid="76" /> <!-- ROLE_TENANT_SUPERVISOR_VIEW -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Inserts -->
		<PermissionCategory id="23">
			<Name>page.label.inserts</Name>
			<Description>The Permission Category including view edit permission for inserts.</Description>
			<Priority>23</Priority>
			<Permissions>
				<Permission refid="82" /> <!-- ROLE_INSERT_VIEW -->
				<Permission refid="83" /> <!-- ROLE_INSERT_EDIT -->
				<Permission refid="84" /> <!-- ROLE_INSERT_APPROVE -->
				<Permission refid="86" /> <!-- ROLE_INSERT_REASSIGN -->
			</Permissions>
		</PermissionCategory>
	
		<PermissionCategory id="24">
			<Name>page.label.archive.inserts</Name>
			<Description>The Permission Category including permission for insert archive.</Description>
			<Priority>24</Priority>
			<Permissions>
				<Permission refid="85" /> <!-- ROLE_INSERT_ARCHIVE -->
			</Permissions>
		</PermissionCategory>

		<!-- Insert Schedules -->
		<PermissionCategory id="26">
			<Name>page.label.insert.schedules</Name>
			<Description>The Permission Category including view edit permission for insert schedules.</Description>
			<Priority>26</Priority>
			<Permissions>
				<Permission refid="92" /> <!-- ROLE_INSERT_SCHEDULE_VIEW -->
				<Permission refid="93" /> <!-- ROLE_INSERT_SCHEDULE_EDIT -->
				<Permission refid="94" /> <!-- ROLE_INSERT_SCHEDULE_APPROVE -->
				<Permission refid="95" /> <!-- ROLE_INSERT_SCHEDULE_SETUP -->
				<Permission refid="97" /> <!-- ROLE_INSERT_SCHEDULE_REASSIGN -->
			</Permissions>
		</PermissionCategory>

		<PermissionCategory id="28">
			<Name>page.label.tenant.insert.schedules</Name>
			<Description>The Permission Category including view edit permission for tenant schedules.</Description>
			<Priority>28</Priority>
			<Permissions>
				<Permission refid="130" />  <!-- ROLE_TENANT_INSERT_SCHEDULE_VIEW -->
				<Permission refid="131" />  <!-- ROLE_TENANT_INSERT_SCHEDULE_EDIT -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Rate Sheets -->
		<PermissionCategory id="30">
			<Name>page.label.rate.sheet</Name>
			<Description>The Permission Category including view edit permission for rate sheets.</Description>
			<Priority>30</Priority>
			<Permissions>
				<Permission refid="104" /> <!-- ROLE_RATE_SCHEDULE_VIEW -->
				<Permission refid="105" /> <!-- ROLE_RATE_SCHEDULE_EDIT -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Touchpoint Variants -->
		<PermissionCategory id="31">
			<Name>page.label.touchpoint.variant.content</Name>
			<Description>The Permission Category including view edit permission for touchpoint variants.</Description>
			<Priority>31</Priority>
			<Permissions>
				<Permission refid="108" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_VIEW -->
				<Permission refid="109" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_EDIT -->
				<Permission refid="110" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_APPROVE -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="35">
			<Name>page.label.remove.touchpoint.variants</Name>
			<Description>The Permission Category including permission for touchpoint variant archive.</Description>
			<Priority>33</Priority>
			<Permissions>
				<Permission refid="116" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_ARCHIVE -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="32">
			<Name>page.label.setup.touchpoint.variants</Name>
			<Description>The Permission Category including view edit permission for touchpoint variant admin.</Description>
			<Priority>35</Priority>
			<Permissions>
				<Permission refid="111" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW -->
				<Permission refid="112" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_ADMIN_EDIT -->
				<Permission refid="113" /> <!-- ROLE_TOUCHPOINT_SELECTION_REASSIGN -->
				<Permission refid="199" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_ADMIN_SETUP -->
			</Permissions>
		</PermissionCategory>

		<PermissionCategory id="36">
			<Name>page.label.composition.tags</Name>
			<Description>The Permission Category including view edit permission for tags.</Description>
			<Priority>36</Priority>
			<Permissions>
				<Permission refid="117" /> <!-- ROLE_TAG_VIEW -->
				<Permission refid="118" /> <!-- ROLE_TAG_EDIT -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="37">
			<Name>page.label.view.all.composition.tags</Name>
			<Description>The Permission Category including permission for tag view all.</Description>
			<Priority>38</Priority>
			<Permissions>
				<Permission refid="119" /> <!-- ROLE_TAG_VIEW_ALL -->
			</Permissions>
		</PermissionCategory>

		<PermissionCategory id="38">
			<Name>page.label.external.events</Name>
			<Description>The Permission Category including permission for external events.</Description>
			<Priority>39</Priority>
			<Permissions>
				<Permission refid="120" /> <!-- ROLE_EXTERNAL_EVENT_VIEW -->
				<Permission refid="121" /> <!-- ROLE_EXTERNAL_EVENT_EDIT -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Smart Text/Smart Canvas -->
		<PermissionCategory id="39">
			<Name>page.label.embedded.content.and.canvas</Name>
			<Description>The Permission Category including all the permissions regarding Smart Text/Smart Canvas.</Description>
			<Priority>40</Priority>
			<Permissions>
				<Permission refid="132" /> 	<!-- ROLE_EMBEDDED_CONTENT_VIEW -->
				<Permission refid="133" />  <!-- ROLE_EMBEDDED_CONTENT_EDIT -->
				<Permission refid="134" /> 	<!-- ROLE_EMBEDDED_CONTENT_APPROVE -->
				<Permission refid="248" /> 	<!-- ROLE_EMBEDDED_CONTENT_REASSIGN -->
				<Permission refid="135" />
			</Permissions>
		</PermissionCategory>
		
		<!-- Image Library -->
		<PermissionCategory id="41">
			<Name>page.label.content.library</Name>
			<Description>The Permission Category including all the permissions regarding Image Library.</Description>
			<Priority>42</Priority>
			<Permissions>
				<Permission refid="136" /> 	<!-- ROLE_CONTENT_LIBRARY_VIEW -->
				<Permission refid="137" />  <!-- ROLE_CONTENT_LIBRARY_EDIT -->
				<Permission refid="138" /> 	<!-- ROLE_CONTENT_LIBRARY_APPROVE -->
				<Permission refid="249" /> 	<!-- ROLE_CONTENT_LIBRARY_REASSIGN -->
				<Permission refid="139" /> 	<!-- ROLE_CONTENT_LIBRARY_SETUP -->
			</Permissions>
		</PermissionCategory>

		<!-- Web Service -->
		<PermissionCategory id="43">
			<Name>page.label.web.service</Name>
			<Description>The Permission Category including permission for Web Service.</Description>
			<Priority>44</Priority>
			<Permissions>
				<Permission refid="140" /> <!-- WEB_SERVICE_UPDATE -->
			</Permissions>
		</PermissionCategory>

		<!-- Style -->
		<PermissionCategory id="44">
			<Name>page.label.styles</Name>
			<Description>The Permission Category including permission for Styles.</Description>
			<Priority>45</Priority>
			<Permissions>
				<Permission refid="141" /> <!-- STYLES_UPDATE -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Workflow Setup-->
		<PermissionCategory id="45">
			<Name>page.label.setup.workflow</Name>
			<Description>The Permission Category including permission for workflow setup.</Description>
			<Priority>46</Priority>
			<Permissions>
				<Permission refid="142" /> <!-- ROLE_WORKFLOW_ADMIN_EDIT -->
			</Permissions>
		</PermissionCategory>

		<PermissionCategory id="76">
			<Name>page.label.workflow.abort</Name>
			<Description>The Permission Category including permission for workflow abort.</Description>
			<Priority>46</Priority>
			<Permissions>
				<Permission refid="212" /> <!-- ROLE_WORKFLOW_ABORT -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Communications-->
		<PermissionCategory id="46">
			<Name>page.label.connected</Name>
			<Description>The Permission Category including permission for Communications (Messagepoint Connected).</Description>
			<Priority>47</Priority>
			<Permissions>
				<Permission refid="143" /> <!-- ROLE_COMMUNICATIONS_VIEW -->
				<Permission refid="144" /> <!-- ROLE_COMMUNICATIONS_EDIT -->
				<Permission refid="145" /> <!-- ROLE_COMMUNICATIONS_APPROVE -->
				<Permission refid="246" /> <!-- ROLE_COMMUNICATIONS_REASSIGN -->
				<Permission refid="152" /> <!-- ROLE_COMMUNICATIONS_SETUP -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="50">
			<Name>page.label.connected.assigned</Name>
			<Description>The Permission Category including permission for Connected Assigned filter.</Description>
			<Priority>48</Priority>
			<Permissions>
				<Permission refid="260" /> <!-- ROLE_COMMUNICATIONS_ASSIGNED -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="90">
			<Name>page.label.connected.communication.api</Name>
			<Description>The Permission Category including permission for Communications API.</Description>
			<Priority>48</Priority>
			<Permissions>
				<Permission refid="262" /> <!-- ROLE_COMMUNICATIONS_API_SETUP -->
			</Permissions>
		</PermissionCategory>

		<!-- Proofing Data -->
		<PermissionCategory id="47">
			<Name>page.label.proofing.data</Name>
			<Description>The Permission Category including permission for Proofing Data.</Description>
			<Priority>49</Priority>
			<Permissions>
				<Permission refid="149" /> <!-- ROLE_PROOFING_DATA_UPDATE -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Collection Setup -->
		<PermissionCategory id="49">
			<Name>page.label.collection.setup</Name>
			<Description>The Permission Category including permission for Collection Setup.</Description>
			<Priority>50</Priority>
			<Permissions>
				<Permission refid="151" /> <!-- ROLE_COLLECTION_SETUP -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Auditing -->
		<PermissionCategory id="51">
			<Name>page.label.auditing</Name>
			<Description>The Permission Category including permission for Auditing.</Description>
			<Priority>51</Priority>
			<Permissions>
				<Permission refid="165" /> <!-- ROLE_AUDITING_EDIT -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Metatags -->
		<PermissionCategory id="52">
			<Name>page.label.tag.cloud</Name>
			<Description>The Permission Category including permission for Metatags.</Description>
			<Priority>52</Priority>
			<Permissions>
				<Permission refid="166" /> <!-- ROLE_METATAGS_VIEW -->
				<Permission refid="255" /> <!-- ROLE_METATAGS_ADMIN -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Global Asset -->
		<PermissionCategory id="53">
			<Name>page.label.global.asset</Name>
			<Description>The Permission Category including permission for Global Asset.</Description>
			<Priority>53</Priority>
			<Permissions>
				<Permission refid="167" /> <!-- ROLE_GLOBAL_ASSET_EDIT -->
			</Permissions>
		</PermissionCategory>

		<!-- Ecatalog -->
		<PermissionCategory id="54">
			<Name>page.label.ecatalog</Name>
			<Description>The Permission Category including permission for eCatalog.</Description>
			<Priority>54</Priority>
			<Permissions>
				<Permission refid="161" />	<!-- ROLE_ECATALOG_VIEW -->
				<Permission refid="162" />	<!-- ROLE_ECATALOG_EDIT -->
				<Permission refid="201" />	<!-- ROLE_ECATALOG_APPROVE -->
				<Permission refid="196" />	<!-- ROLE_ECATALOG_ADMIN -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Touchpoint Versioning -->
		<PermissionCategory id="55">
			<Name>page.label.touchpoint.versioning</Name>
			<Description>The Permission Category including permission for Touchpoint Versioning.</Description>
			<Priority>55</Priority>
			<Permissions>
				<Permission refid="168" /> <!-- ROLE_TOUCHPOINT_VERSIONING_EDIT -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Project Sync -->
		<PermissionCategory id="56">
			<Name>page.label.project.sync</Name>
			<Description>The Permission Category including permission for Project Sync.</Description>
			<Priority>56</Priority>
			<Permissions>
				<Permission refid="169" /> <!-- ROLE_PROJECT_SYNC_EDIT -->
			</Permissions>
		</PermissionCategory>

		<!-- Lookup Table-->
		<PermissionCategory id="57">
			<Name>page.label.lookup.table</Name>
			<Description>The Permission Category including permission for Lookup Table.</Description>
			<Priority>57</Priority>
			<Permissions>
				<Permission refid="170" /> <!-- ROLE_LOOKUP_TABLE_VIEW -->
				<Permission refid="171" /> <!-- ROLE_LOOKUP_TABLE_EDIT -->
				<Permission refid="172" /> <!-- ROLE_LOOKUP_TABLE_APPROVE -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="58">
			<Name>page.label.lookup.table.setup</Name>
			<Description>The Permission Category including permission for Lookup Table Setup.</Description>
			<Priority>58</Priority>
			<Permissions>
				<Permission refid="173" /> <!-- ROLE_LOOKUP_TABLE_SETUP -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Touchpoint Content -->
		<PermissionCategory id="59">
			<Name>page.label.touchpoint.content</Name>
			<Description>The Permission Category including all the permissions regarding Touchpoint Content.</Description>
			<Priority>59</Priority>
			<Permissions>
				<Permission refid="178" /> 	<!-- ROLE_TOUCHPOINT_CONTENT_VIEW -->
				<Permission refid="179" />  <!-- ROLE_TOUCHPOINT_CONTENT_EDIT -->
				<Permission refid="180" /> 	<!-- ROLE_TOUCHPOINT_CONTENT_APPROVE -->
				<Permission refid="5" /> 	<!-- ROLE_TOUCHPOINT_ADMIN -->
			</Permissions>
		</PermissionCategory>
		<PermissionCategory id="60">
			<Name>page.label.touchpoint.content.setup</Name>
			<Description>The Permission Category including permission for Touchpoint Content admin.</Description>
			<Priority>60</Priority>
			<Permissions>
				<Permission refid="181" /> <!-- ROLE_TOUCHPOINT_CONTENT_ADMIN -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Metadata Forms -->
		<PermissionCategory id="61">
			<Name>page.label.metadata.forms.setup</Name>
			<Description>The Permission Category including permission for Metadata Form admin.</Description>
			<Priority>61</Priority>
			<Permissions>
				<Permission refid="182" /> <!-- ROLE_METADATA_FORM_ADMIN -->
			</Permissions>
		</PermissionCategory>
		
		<PermissionCategory id="64">
			<Name>page.label.content.source</Name>
			<Description>The Permission Category including permission for Content Source.</Description>
			<Priority>62</Priority>
			<Permissions>
				<Permission refid="183" /> <!-- ROLE_CONTENT_SOURCE_EDIT -->
			</Permissions>
		</PermissionCategory>		
		
		<!-- Project -->
		<PermissionCategory id="65">
			<Name>page.label.project</Name>
			<Description>The Permission Category including permission for Project.</Description>
			<Priority>63</Priority>
			<Permissions>
				<Permission refid="185" /> <!-- ROLE_PROJECT_VIEW -->
				<Permission refid="186" /> <!-- ROLE_PROJECT_EDIT -->
				<Permission refid="187" /> <!-- ROLE_PROJECT_APPROVE -->
				<Permission refid="192" /> <!-- ROLE_PROJECT_REASSIGN -->
				<Permission refid="188" /> <!-- ROLE_PROJECT_SETUP -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Touchpoint Exchange -->
		<PermissionCategory id="66">
			<Name>page.label.touchpoint.exchange</Name>
			<Description>The Permission Category including permission for Touchpoint Exchange.</Description>
			<Priority>64</Priority>
			<Permissions>
				<Permission refid="189" /> <!-- ROLE_TOUCHPOINT_EXCHANGE_VIEW -->
				<Permission refid="190" /> <!-- ROLE_TOUCHPOINT_EXCHANGE_SETUP -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Global Search -->
		<PermissionCategory id="67">
			<Name>page.label.global.search</Name>
			<Description>The Permission Category including permission for Global Search.</Description>
			<Priority>65</Priority>
			<Permissions>
				<Permission refid="191" /> <!-- ROLE_GLOBAL_SEARCH_VIEW -->
			</Permissions>
		</PermissionCategory>

        <!-- Job Center -->
        <PermissionCategory id="68">
            <Name>page.label.job.center</Name>
            <Description>The Permission Category including permission for Job Center</Description>
            <Priority>68</Priority>
            <Permissions>
                <Permission refid="193" /> <!-- ROLE_JOB_CENTER_SETUP -->
            </Permissions>
        </PermissionCategory>
        
        <!-- Local Global Management -->
        <PermissionCategory id="69">
            <Name>page.label.local.global.management</Name>
            <Description>The Permission Category including permission for Local Global Management</Description>
            <Priority>69</Priority>
            <Permissions>
                <Permission refid="195" /> <!-- ROLE_LOCAL_GLOBAL_MANAGEMENT -->
            </Permissions>
        </PermissionCategory>
        
        <!-- Power Edit -->
        <PermissionCategory id="70">
            <Name>page.label.power.edit</Name>
            <Description>The Permission Category including permission for Power Edit</Description>
            <Priority>70</Priority>
            <Permissions>
                <Permission refid="198" /> <!-- ROLE_POWER_EDIT -->
            </Permissions>
        </PermissionCategory>

		<!-- OnPrem Server Edit -->
		<PermissionCategory id="71">
			<Name>page.label.bundle.delivery</Name>
			<Description>The permission category including permission for OnPrem server setup</Description>
			<Priority>71</Priority>
			<Permissions>
				<Permission refid="203" /> <!-- OnPrem DEServer Management -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Dictionary -->
		<PermissionCategory id="72">
			<Name>page.label.shared.dictionary.management</Name>
			<Description>The Permission Category including permission for Dictionary.</Description>
			<Priority>72</Priority>
			<Permissions>
				<Permission refid="205" /> <!-- ROLE_DICTIONARY_EDIT -->
			</Permissions>
		</PermissionCategory>
		
		<!-- Shared Clipboard -->
		<PermissionCategory id="73">
			<Name>page.label.shared.clipboard</Name>
			<Description>The Permission Category including permission for Shared Clipboard.</Description>
			<Priority>73</Priority>
			<Permissions>
				<Permission refid="207" /> <!-- ROLE_SHARED_CLIPBOARD_VIEW -->
				<Permission refid="208" /> <!-- ROLE_SHARED_CLIPBOARD_EDIT -->
			</Permissions>
		</PermissionCategory>

		<!-- Brand -->
		<PermissionCategory id="74">
			<Name>page.label.brand</Name>
			<Description>The Permission Category including permission for Brand.</Description>
			<Priority>74</Priority>
			<Permissions>
				<Permission refid="209" /> <!-- ROLE_BRAND_VIEW -->
				<Permission refid="210" /> <!-- ROLE_BRAND_SETUP -->
			</Permissions>
		</PermissionCategory>

		<!-- Unrestricted Search -->
		<PermissionCategory id="75">
			<Name>page.label.unrestricted.search</Name>
			<Description>The Permission Category including permission for unrestricted search.</Description>
			<Priority>66</Priority>
			<Permissions>
				<Permission refid="211" /> <!-- ROLE_UNRESTRICTED_SEARCH -->
			</Permissions>
		</PermissionCategory>

		<!-- Unrestricted Search -->
		<PermissionCategory id="87">
			<Name>page.label.global.dashboard</Name>
			<Description>The Permission Category including permission for global dashboard.</Description>
			<Priority>68</Priority>
			<Permissions>
				<Permission refid="245" /> <!-- ROLE_GLOBAL_DASHBOARD_VIEW -->
			</Permissions>
		</PermissionCategory>

		<!-- Changes -->
		<PermissionCategory id="88">
			<Name>page.label.changes</Name>
			<Description>The Permission Category including permission for changes.</Description>
			<Priority>68</Priority>
			<Permissions>
				<Permission refid="256" /> <!-- ROLE_CHANGE_VIEW -->
				<Permission refid="257" /> <!-- ROLE_CHANGE_EDIT -->
			</Permissions>
		</PermissionCategory>

		<!-- API Tokens -->
		<PermissionCategory id="89">
			<Name>page.label.api.tokens</Name>
			<Description>The Permission Category including permission for API tokens.</Description>
			<Priority>69</Priority>
			<Permissions>
				<Permission refid="259" /> <!-- ROLE_API_TOKEN_ADMIN-->
			</Permissions>
		</PermissionCategory>

	</PermissionCategories>
	
	<Permissions>
		<Permission id="999">
			<Name>ROLE_MASTER_ADMIN</Name>
			<Description>Role that will be granted to the user if she has Master Admin User status.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="900">
			<Name>ROLE_MASTER_USER</Name>
			<Description>Role that will be granted to the user if she has Master User status.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="2">
			<Name>ROLE_USER</Name>
			<Description>User role that every authenticated user can have</Description>
			<Type>1</Type>
		</Permission>
<!-- 	
		<Permission id="3">
			<Name>ROLE_SUPER_ADMIN</Name>
			<Description>Admin role that allows the user to do and see anything in the system</Description>
		</Permission>
-->		
		<Permission id="4">
			<Name>ROLE_SYSTEM_ADMIN</Name>
			<Description>Admin role that allows the user to view the settings of the messagepoint application</Description>
			<Type>1</Type>
		</Permission>

		<Permission id="55">
			<Name>ROLE_ADMIN_SETTINGS_EDIT</Name>
			<Description>Admin role that allows the user to configure the settings of the messagepoint application</Description>
			<Type>2</Type>
		</Permission>

		<!-- Touchpoint Administration Permissions -->
		<Permission id="5">
			<Name>ROLE_TOUCHPOINT_ADMIN</Name>
			<Description>Admin role that allows the user to administer touchpoints or documents</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="28">
			<Name>ROLE_TARGETING_EDIT</Name>
			<Description>Report role that allows the user to view and create and edit target groups.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="41">
			<Name>ROLE_TASK_EDIT</Name>
			<Description>Task role that allows the user to edit task content</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="42">
			<Name>ROLE_TASK_VIEW_MY</Name>
			<Description>Task role that allows the user to view their task content</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="43">
			<Name>ROLE_TASK_VIEW_ALL</Name>
			<Description>Task role that allows the user to view all task content</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="40">
			<Name>ROLE_TASK_SETUP</Name>
			<Description>Task role that allows the user to setup task content</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="39">
			<Name>ROLE_TASK_REASSIGN</Name>
			<Description>Task role that allows the user to reassign task content</Description>
			<Type>4</Type>
		</Permission>
		
		<!-- Message Permissions -->
		<Permission id="7">
			<Name>ROLE_MESSAGE_EDIT</Name>
			<Description>Message role that allows the user to view and edit message content</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="22">
			<Name>ROLE_MESSAGE_APPROVE_EDIT</Name>
			<Description>Approval role that allows the user to approve message content</Description>
			<Type>3</Type>
		</Permission>
		<!-- Message Approval Permissions -->
		<Permission id="36">
			<Name>ROLE_MESSAGE_VIEW_MY</Name>
			<Description>Message role that allows the user to view their messages</Description>
			<Type>10</Type>
		</Permission>
		<Permission id="37">
			<Name>ROLE_MESSAGE_VIEW_ALL</Name>
			<Description>Message role that allows the user to view all messages</Description>
			<Type>1</Type>
		</Permission>
				
		<!-- Report Permissions -->
		<Permission id="16">
			<Name>ROLE_REPORT_VIEW</Name>
			<Description>Report role that allows the user to view previously generated Reports</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="17">
			<Name>ROLE_REPORT_EDIT</Name>
			<Description>Report role that allows the user to view and generate Reports</Description>
			<Type>2</Type>
		</Permission>
		
		<Permission id="26">
			<Name>ROLE_TEST_VIEW</Name>
			<Description>Report role that allows the user to view previously generated Tests</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="27">
			<Name>ROLE_TEST_EDIT</Name>
			<Description>Report role that allows the user to view and generate Tests</Description>
			<Type>2</Type>
		</Permission>

		<!--  System Permissions  -->
		<Permission id="1">
			<Name>ROLE_SYSTEM_MULTI_TENANT</Name>
			<Description>Permission that is automatically added to Users' authorities if the System is enabled for multi tenant.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="48">
			<Name>ROLE_TENANT_LEVEL_PROVIDER</Name>
			<Description>Permission that is automatically added to Users' authorities if the System is enabled for multi tenant and the tenant level of the user is the highest.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="49">
			<Name>ROLE_TENANT_LEVEL_SUBTENANT</Name>
			<Description>Permission that is automatically added to Users' authorities if the System is enabled for multi tenant and the tenant level of the user is the second highest.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="50">
			<Name>ROLE_TENANT_LEVEL_3</Name>
			<Description>Permission that is automatically added to Users' authorities if the System is enabled for multi tenant and the tenant level of the user is the third level or more.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="56">
			<Name>ROLE_PROVIDER_SYSTEM_ADMIN</Name>
			<Description>Permission that is automatically added to Users' authorities if the System is enabled for multi tenant and the tenant level of the user is the highest and user has system admin permission.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="57">
			<Name>ROLE_PROVIDER_SYSTEM_ADMIN_EDIT</Name>
			<Description>Permission that is automatically added to Users' authorities if the System is enabled for multi tenant and the tenant level of the user is the highest and user has settings edit permission.</Description>
			<Type>9</Type>
		</Permission>

		<Permission id="44">
			<Name>ROLE_ADMIN_USER_VIEW</Name>
			<Description>Admin role that allows the user to view Users</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="45">
			<Name>ROLE_ADMIN_USER_EDIT</Name>
			<Description>Admin role that allows the user to create and edit Users</Description>
			<Type>2</Type>
		</Permission>
		
		<Permission id="46">
			<Name>ROLE_ADMIN_ROLE_VIEW</Name>
			<Description>Admin role that allows the user to view Roles</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="47">
			<Name>ROLE_ADMIN_ROLE_EDIT</Name>
			<Description>Admin role that allows the user to create and edit Roles</Description>
			<Type>2</Type>
		</Permission>

		<Permission id="51">
			<Name>ROLE_TOUCHPOINT_DATA_LIST</Name>
			<Description>Touchpoint role that allows the user to list Data Models</Description>
			<Type>1</Type>
		</Permission>

		<Permission id="52">
			<Name>ROLE_ADMIN_TENANT_VIEW</Name>
			<Description>Admin role that allows the user to view Tenants</Description>
			<Type>1</Type>
		</Permission>

		<Permission id="53">
			<Name>ROLE_ADMIN_TENANT_EDIT</Name>
			<Description>Admin role that allows the user to create and edit Tenants and Tenant Permissions</Description>
			<Type>2</Type>
		</Permission>
		
		<Permission id="54">
			<Name>ROLE_TOUCHPOINT_DATA_EDIT</Name>
			<Description>Touchpoint role that allows the user to edit Data Models</Description>
			<Type>2</Type>
		</Permission>

		<Permission id="58">
			<Name>ROLE_RULE_VIEW</Name>
			<Description>Touchpoint role that allows the user to view Rules</Description>
			<Type>1</Type>
		</Permission>

		<Permission id="59">
			<Name>ROLE_RULE_EDIT</Name>
			<Description>Touchpoint role that allows the user to edit Rules</Description>
			<Type>2</Type>
		</Permission>

		<Permission id="62">
			<Name>ROLE_SIMULATION_VIEW</Name>
			<Description>Role that allows the user to view Simulations</Description>
			<Type>1</Type>
		</Permission>

		<Permission id="63">
			<Name>ROLE_SIMULATION_EDIT</Name>
			<Description>Role that allows the user to edit Simulations</Description>
			<Type>2</Type>
		</Permission>
		
		<Permission id="64">
			<Name>ROLE_MESSAGE_ACTIVATION</Name>
			<Description>Role that allows the user to archive/activate Message</Description>
			<Type>2</Type>
		</Permission>

		<Permission id="65">
			<Name>ROLE_MESSAGE_REASSIGNMENT</Name>
			<Description>Role that allows the user to reassign and broke the lock Message</Description>
			<Type>4</Type>
		</Permission>
		
		<Permission id="66">
			<Name>ROLE_WORKGROUP_VIEW</Name>
			<Description>Role that allows the user to view Workgroups</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="67">
			<Name>ROLE_WORKGROUP_EDIT</Name>
			<Description>Role that allows the user to edit Workgroups</Description>
			<Type>2</Type>
		</Permission>
		
		<Permission id="70">
			<Name>ROLE_WORKGROUP_FOLDER_EDIT</Name>
			<Description>Role that will be granted to the user to do work group folder admin.</Description>
			<Type>2</Type>
		</Permission>

		<Permission id="76">
			<Name>ROLE_TENANT_SUPERVISOR</Name>
			<Description>Role that will be granted to the user who can act as tenant supervisor.</Description>
			<Type>1</Type>
		</Permission>

		<Permission id="77">
			<Name>ROLE_WORKGROUP_FOLDER_VIEW</Name>
			<Description>Role that will be granted to the user to view work group folders.</Description>
			<Type>1</Type>
		</Permission>

		<Permission id="78">
			<Name>LICENCED_SIMULATION_VIEW</Name>
			<Description>Permission that will be granted to the user upon sign-in if Licenced for Simulation and has ROLE_SIMULATION_VIEW.</Description>
			<Type>9</Type>
		</Permission>

		<Permission id="79">
			<Name>LICENCED_SIMULATION_EDIT</Name>
			<Description>Permission that will be granted to the user upon sign-in if Licenced for Simulation and has ROLE_SIMULATION_EDIT.</Description>
			<Type>9</Type>
		</Permission>

		<!-- Insert Permissions -->
		<Permission id="82">
			<Name>ROLE_INSERT_VIEW</Name>
			<Description>Permission that will be granted to the user to view inserts.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="83">
			<Name>ROLE_INSERT_EDIT</Name>
			<Description>Permission that will be granted to the user to edit inserts.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="84">
			<Name>ROLE_INSERT_APPROVE</Name>
			<Description>Permission that will be granted to the user to approve inserts.</Description>
			<Type>3</Type>
		</Permission>
		<Permission id="85">
			<Name>ROLE_INSERT_ARCHIVE</Name>
			<Description>Permission that will be granted to the user to archive inserts.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="86">
			<Name>ROLE_INSERT_REASSIGN</Name>
			<Description>Permission that will be granted to the user to reassign inserts.</Description>
			<Type>4</Type>
		</Permission>

		<Permission id="87">
			<Name>ROLE_LICENCED_INSERT_VIEW</Name>
			<Description>Role that will be granted to the user if she has Insert view permission and Insert Management licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="88">
			<Name>ROLE_LICENCED_INSERT_EDIT</Name>
			<Description>Role that will be granted to the user if she has Insert edit permission and Insert Management Licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="89">
			<Name>ROLE_LICENCED_INSERT_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Insert approve permission and Insert Management Licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="90">
			<Name>ROLE_LICENCED_INSERT_ARCHIVE</Name>
			<Description>Role that will be granted to the user if she has Insert archive permission and Insert Management Licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="91">
			<Name>ROLE_LICENCED_INSERT_REASSIGN</Name>
			<Description>Role that will be granted to the user if she has Insert reassign permission and Insert Management Licence.</Description>
			<Type>9</Type>
		</Permission>
		
		<Permission id="92">
			<Name>ROLE_INSERT_SCHEDULE_VIEW</Name>
			<Description>Role that will be granted to the user if she has Insert Schedule view permission and Insert Management licence.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="93">
			<Name>ROLE_INSERT_SCHEDULE_EDIT</Name>
			<Description>Role that will be granted to the user if she has Insert Schedule edit permission and Insert Management Licence.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="94">
			<Name>ROLE_INSERT_SCHEDULE_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Insert Schedule approve permission and Insert Management Licence.</Description>
			<Type>3</Type>
		</Permission>
		<Permission id="95">
			<Name>ROLE_INSERT_SCHEDULE_SETUP</Name>
			<Description>Role that will be granted to the user if she has Insert Schedule setup permission and Insert Management Licence.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="97">
			<Name>ROLE_INSERT_SCHEDULE_REASSIGN</Name>
			<Description>Role that will be granted to the user if she has Insert Schedule reassign permission and Insert Management Licence.</Description>
			<Type>4</Type>
		</Permission>
		
		<Permission id="98">
			<Name>ROLE_LICENCED_INSERT_SCHEDULE_VIEW</Name>
			<Description>Role that will be granted to the user if she has Insert Schedule view permission and Insert Management licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="99">
			<Name>ROLE_LICENCED_INSERT_SCHEDULE_EDIT</Name>
			<Description>Role that will be granted to the user if she has Insert Schedule edit permission and Insert Management Licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="100">
			<Name>ROLE_LICENCED_INSERT_SCHEDULE_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Insert Schedule approve permission and Insert Management Licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="101">
			<Name>ROLE_LICENCED_INSERT_SCHEDULE_SETUP</Name>
			<Description>Role that will be granted to the user if she has Insert Schedule setup permission and Insert Management Licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="103">
			<Name>ROLE_LICENCED_INSERT_SCHEDULE_REASSIGN</Name>
			<Description>Role that will be granted to the user if she has Insert Schedule reassign permission and Insert Management Licence.</Description>
			<Type>9</Type>
		</Permission>

		<Permission id="104">
			<Name>ROLE_RATE_SCHEDULE_VIEW</Name>
			<Description>Role that will be granted to the user if she has Rate Schedule view permission and Insert Management licence.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="105">
			<Name>ROLE_RATE_SCHEDULE_EDIT</Name>
			<Description>Role that will be granted to the user if she has Rate Schedule edit permission and Insert Management Licence.</Description>
			<Type>2</Type>
		</Permission>

		<Permission id="106">
			<Name>ROLE_LICENCED_RATE_SCHEDULE_VIEW</Name>
			<Description>Role that will be granted to the user if she has Rate Schedule view permission and Insert Management licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="107">
			<Name>ROLE_LICENCED_RATE_SCHEDULE_EDIT</Name>
			<Description>Role that will be granted to the user if she has Rate Schedule edit permission and Insert Management Licence.</Description>
			<Type>9</Type>
		</Permission>

		<!-- Touchpoint Variants -->
		<Permission id="108">
			<Name>ROLE_TOUCHPOINT_SELECTIONS_VIEW</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variants view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="109">
			<Name>ROLE_TOUCHPOINT_SELECTIONS_EDIT</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variants edit permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="110">
			<Name>ROLE_TOUCHPOINT_SELECTIONS_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variants approve permission.</Description>
			<Type>3</Type>
		</Permission>
		<Permission id="111">
			<Name>ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variants admin view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="112">
			<Name>ROLE_TOUCHPOINT_SELECTIONS_ADMIN_EDIT</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variants admin view permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="199">
			<Name>ROLE_TOUCHPOINT_SELECTIONS_ADMIN_SETUP</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variants admin setup permission.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="113">
			<Name>ROLE_TOUCHPOINT_SELECTION_REASSIGN</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variant reassign permission.</Description>
			<Type>4</Type>
		</Permission>
		<Permission id="116">
			<Name>ROLE_TOUCHPOINT_SELECTIONS_ARCHIVE</Name>
			<Description>Permission that will be granted to the user to remove Touchpoint Variants.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="154">
			<Name>ROLE_LICENCED_TOUCHPOINT_SELECTIONS_VIEW</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variants view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="155">
			<Name>ROLE_LICENCED_TOUCHPOINT_SELECTIONS_EDIT</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variants edit permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="156">
			<Name>ROLE_LICENCED_TOUCHPOINT_SELECTIONS_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variants approve permission.</Description>
			<Type>3</Type>
		</Permission>
		<Permission id="157">
			<Name>ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ARCHIVE</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variants archive permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="158">
			<Name>ROLE_LICENCED_TOUCHPOINT_SELECTION_REASSIGN</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variants reassign permission.</Description>
			<Type>4</Type>
		</Permission>
		<Permission id="159">
			<Name>ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_VIEW</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variant admin view permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="160">
			<Name>ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_EDIT</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variant admin edit permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="200">
			<Name>ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_SETUP</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Variant admin setup permission.</Description>
			<Type>5</Type>
		</Permission>	
		
		<!-- Tags -->
		<Permission id="117">
			<Name>ROLE_TAG_VIEW</Name>
			<Description>Role that will be granted to the user if she has Tag view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="118">
			<Name>ROLE_TAG_EDIT</Name>
			<Description>Role that will be granted to the user if she has Tag edit permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="119">
			<Name>ROLE_TAG_VIEW_ALL</Name>
			<Description>Role that will be granted to the user if she has Tag view all permission.</Description>
			<Type>1</Type>
		</Permission>

		<!-- External Event -->
		<Permission id="120">
			<Name>ROLE_EXTERNAL_EVENT_VIEW</Name>
			<Description>Role that will be granted to the user if she has external event view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="121">
			<Name>ROLE_EXTERNAL_EVENT_EDIT</Name>
			<Description>Role that will be granted to the user if she has external event edit permission.</Description>
			<Type>2</Type>
		</Permission>
		
		<!-- Tenant Insert Schedule -->
		<Permission id="130">
			<Name>ROLE_TENANT_INSERT_SCHEDULE_VIEW</Name>
			<Description>Role that will be granted to the user if she has tenant insert schedule view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="131">
			<Name>ROLE_TENANT_INSERT_SCHEDULE_EDIT</Name>
			<Description>Role that will be granted to the user if she has tenant insert schedule edit permission.</Description>
			<Type>2</Type>
		</Permission>
		
		<!-- Smart Text -->
		<Permission id="132">
			<Name>ROLE_EMBEDDED_CONTENT_VIEW</Name>
			<Description>Role that will be granted to the user if she has Smart Text view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="133">
			<Name>ROLE_EMBEDDED_CONTENT_EDIT</Name>
			<Description>Role that will be granted to the user if she has Smart Text edit permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="134">
			<Name>ROLE_EMBEDDED_CONTENT_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Smart Text approve permission.</Description>
			<Type>3</Type>
		</Permission>
		<Permission id="248">
			<Name>ROLE_EMBEDDED_CONTENT_REASSIGN</Name>
			<Description>Role that will be granted to the user if she has Smart Text reassign permission.</Description>
			<Type>4</Type>
		</Permission>
		<Permission id="135">
			<Name>ROLE_EMBEDDED_CONTENT_ADMIN</Name>
			<Description>Role that will be granted to the user if she has Smart Text admin permission.</Description>
			<Type>5</Type>
		</Permission>	
		
		<!-- Image Library -->
		<Permission id="136">
			<Name>ROLE_CONTENT_LIBRARY_VIEW</Name>
			<Description>Role that will be granted to the user if she has Image Library view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="137">
			<Name>ROLE_CONTENT_LIBRARY_EDIT</Name>
			<Description>Role that will be granted to the user if she has Image Library edit permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="138">
			<Name>ROLE_CONTENT_LIBRARY_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Image Library approve permission.</Description>
			<Type>3</Type>
		</Permission>
		<Permission id="249">
			<Name>ROLE_CONTENT_LIBRARY_REASSIGN</Name>
			<Description>Role that will be granted to the user if she has Image Library reassign permission.</Description>
			<Type>4</Type>
		</Permission>
		<Permission id="139">
			<Name>ROLE_CONTENT_LIBRARY_ADMIN</Name>
			<Description>Role that will be granted to the user if she has Image Library admin permission.</Description>
			<Type>5</Type>
		</Permission>

		<!-- Web Service -->
		<Permission id="140">
			<Name>ROLE_WEB_SERVICE_UPDATE</Name>
			<Description>Role that will be granted to the user if she has Web Service update permission.</Description>
			<Type>2</Type>
		</Permission>
		
		<!-- Styles -->
		<Permission id="141">
			<Name>ROLE_STYLES_UPDATE</Name>
			<Description>Role that will be granted to the user if she has Styles update permission.</Description>
			<Type>2</Type>
		</Permission>
		
		<!-- Workflow -->
		<Permission id="142">
			<Name>ROLE_WORKFLOW_ADMIN_EDIT</Name>
			<Description>Role that will be granted to the user if she has Workflow setup permission.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="212">
			<Name>ROLE_WORKFLOW_ABORT</Name>
			<Description>Role that will be granted to the user if she has Workflow abort permission.</Description>
			<Type>3</Type>
		</Permission>
		
		<!-- Communications -->
		<Permission id="143">
			<Name>ROLE_COMMUNICATIONS_VIEW</Name>
			<Description>Role that will be granted to the user if she has Communications view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="144">
			<Name>ROLE_COMMUNICATIONS_EDIT</Name>
			<Description>Role that will be granted to the user if she has Communications edit permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="145">
			<Name>ROLE_COMMUNICATIONS_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Communications approval permission.</Description>
			<Type>3</Type>
		</Permission>
		<Permission id="246">
			<Name>ROLE_COMMUNICATIONS_REASSIGN</Name>
			<Description>Role that will be granted to the user if she has Communications reassign permission.</Description>
			<Type>4</Type>
		</Permission>
		<Permission id="152">
			<Name>ROLE_COMMUNICATIONS_SETUP</Name>
			<Description>Role that will be granted to the user if she has Communications setup permission.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="260">
			<Name>ROLE_COMMUNICATIONS_ASSIGNED</Name>
			<Description>Role that will be granted to the user if she has Communications assigned permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="262">
			<Name>ROLE_COMMUNICATIONS_API_SETUP</Name>
			<Description>Permission for setting up communications API access.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="146">
			<Name>ROLE_LICENCED_COMMUNICATIONS_VIEW</Name>
			<Description>Role that will be granted to the user if she has Communications view permission and Messagepoint Connected licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="147">
			<Name>ROLE_LICENCED_COMMUNICATIONS_EDIT</Name>
			<Description>Role that will be granted to the user if she has Communications edit permission and Messagepoint Connected licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="148">
			<Name>ROLE_LICENCED_COMMUNICATIONS_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Communications approval permission and Messagepoint Connected licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="247">
			<Name>ROLE_LICENCED_COMMUNICATIONS_REASSIGN</Name>
			<Description>Role that will be granted to the user if she has Communications reassign permission and Messagepoint Connected licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="153">
			<Name>ROLE_LICENCED_COMMUNICATIONS_SETUP</Name>
			<Description>Role that will be granted to the user if she has Communications setup permission and Messagepoint Connected licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="261">
			<Name>ROLE_LICENCED_COMMUNICATIONS_ASSIGNED</Name>
			<Description>Role that will be granted to the user if she has Communications assigned permission and Messagepoint Connected licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="263">
			<Name>ROLE_LICENCED_COMMUNICATIONS_API_SETUP</Name>
			<Description>Permission for setting up licenced communications API access.</Description>
			<Type>5</Type>
		</Permission>

		<!-- Proofing Data -->
		<Permission id="149">
			<Name>ROLE_PROOFING_DATA_UPDATE</Name>
			<Description>Role that will be granted to the user if she has Proofing Data update permission.</Description>
			<Type>2</Type>
		</Permission>
		
		<!-- Priority Admin -->
		<Permission id="150">
			<Name>ROLE_PRIORITY_ADMIN</Name>
			<Description>Role that will be granted to the user if she has Priority admin permission.</Description>
			<Type>2</Type>
		</Permission>
		
		<!-- Collection Setup -->
		<Permission id="151">
			<Name>ROLE_COLLECTION_SETUP</Name>
			<Description>Role that will be granted to the user if she has Collection Setup update permission.</Description>
			<Type>5</Type>
		</Permission>
		
		<!-- Ecatalog Setup -->
		<Permission id="161">
			<Name>ROLE_ECATALOG_VIEW</Name>
			<Description>Role that will be granted to the user if she has ECatalog view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="162">
			<Name>ROLE_ECATALOG_EDIT</Name>
			<Description>Role that will be granted to the user if she has ECatalog edit permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="201">
			<Name>ROLE_ECATALOGUE_APPROVE</Name>
			<Description>Role that will be granted to the user if she has ECatalog approve permission.</Description>
			<Type>3</Type>
		</Permission>
		<Permission id="196">
			<Name>ROLE_ECATALOGUE_ADMIN</Name>
			<Description>Role that will be granted to the user if she has ECatalog admin permission.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="163">
			<Name>ROLE_LICENCED_ECATALOG_VIEW</Name>
			<Description>Role that will be granted to the user if she has ECatalog view permission and ECatalog licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="164">
			<Name>ROLE_LICENCED_ECATALOG_EDIT</Name>
			<Description>Role that will be granted to the user if she has ECatalog edit permission and ECatalog licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="202">
			<Name>ROLE_LICENCED_ECATALOG_APPROVE</Name>
			<Description>Role that will be granted to the user if she has ECatalog approve permission and ECatalog licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="197">
			<Name>ROLE_LICENCED_ECATALOG_ADMIN</Name>
			<Description>Role that will be granted to the user if she has ECatalog admin permission and ECatalog licence.</Description>
			<Type>9</Type>
		</Permission>
		
		<!-- Auditing -->
		<Permission id="165">
			<Name>ROLE_AUDITING_EDIT</Name>
			<Description>Role that will be granted to the user if she has Auditing edit permission.</Description>
			<Type>2</Type>
		</Permission>
		
		<!-- Metatags -->
		<Permission id="166">
			<Name>ROLE_METATAGS_VIEW</Name>
			<Description>Role that will be granted to the user if she has Metatags permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="255">
			<Name>ROLE_METATAGS_ADMIN</Name>
			<Description>Role that will be granted to the user if she has Metatags setup permission.</Description>
			<Type>5</Type>
		</Permission>
		
		<!-- Global Smart Text -->
		<Permission id="167">
			<Name>ROLE_GLOBAL_ASSET_EDIT</Name>
			<Description>Role that will be granted to the user if she has Global Smart Text edit permission.</Description>
			<Type>2</Type>
		</Permission>
		
		<!-- Project Versioning -->
		<Permission id="168">
			<Name>ROLE_TOUCHPOINT_VERSIONING_EDIT</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Versioning edit permission.</Description>
			<Type>2</Type>
		</Permission>
		
		<!-- Project Sync -->
		<Permission id="169">
			<Name>ROLE_PROJECT_SYNC_EDIT</Name>
			<Description>Role that will be granted to the user if she has Project Sync edit permission.</Description>
			<Type>2</Type>
		</Permission>
		
		<!-- Lookup Table -->
		<Permission id="170">
			<Name>ROLE_LOOKUP_TABLE_VIEW</Name>
			<Description>Role that will be granted to the user if she has Lookup Table view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="171">
			<Name>ROLE_LOOKUP_TABLE_EDIT</Name>
			<Description>Role that will be granted to the user if she has Lookup Table edit permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="172">
			<Name>ROLE_LOOKUP_TABLE_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Lookup Table approve permission.</Description>
			<Type>3</Type>
		</Permission>
		<Permission id="173">
			<Name>ROLE_LOOKUP_TABLE_SETUP</Name>
			<Description>Role that will be granted to the user if she has Lookup Table setup permission.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="174">
			<Name>ROLE_LICENCED_LOOKUP_TABLE_VIEW</Name>
			<Description>Role that will be granted to the user if she has Lookup Table view permission and Lookup Table licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="175">
			<Name>ROLE_LICENCED_LOOKUP_TABLE_EDIT</Name>
			<Description>Role that will be granted to the user if she has Lookup Table edit permission and Lookup Table licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="176">
			<Name>ROLE_LICENCED_LOOKUP_TABLE_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Lookup Table approval permission and Lookup Table licence.</Description>
			<Type>9</Type>
		</Permission>
		<Permission id="177">
			<Name>ROLE_LICENCED_LOOKUP_TABLE_SETUP</Name>
			<Description>Role that will be granted to the user if she has Lookup Table setup permission and Lookup Table licence.</Description>
			<Type>9</Type>
		</Permission>	
		
		<!-- Touchpoint Content -->
		<Permission id="178">
			<Name>ROLE_TOUCHPOINT_CONTENT_VIEW</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Content view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="179">
			<Name>ROLE_TOUCHPOINT_CONTENT_EDIT</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Content edit permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="180">
			<Name>ROLE_TOUCHPOINT_CONTENT_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Content approve permission.</Description>
			<Type>3</Type>
		</Permission>
		<Permission id="181">
			<Name>ROLE_TOUCHPOINT_CONTENT_ADMIN</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Content admin permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="182">
			<Name>ROLE_METADATA_FORM_ADMIN</Name>
			<Description>Role that will be granted to the user if she has Metadata Form admin permission.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="183">
			<Name>ROLE_CONTENT_SOURCE_EDIT</Name>
			<Description>Role that will be granted to the user if she has Content Source edit permission.</Description>
			<Type>2</Type>
		</Permission>																
		
		<!-- Project -->
		<Permission id="185">
			<Name>ROLE_PROJECT_VIEW</Name>
			<Description>Role that will be granted to the user if she has Project view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="186">
			<Name>ROLE_PROJECT_EDIT</Name>
			<Description>Role that will be granted to the user if she has Project edit permission.</Description>
			<Type>2</Type>
		</Permission>
		<Permission id="187">
			<Name>ROLE_PROJECT_APPROVE</Name>
			<Description>Role that will be granted to the user if she has Project approve permission.</Description>
			<Type>3</Type>
		</Permission>
		<Permission id="192">
			<Name>ROLE_PROJECT_REASSIGN</Name>
			<Description>Role that will be granted to the user if she has Project reassign permission.</Description>
			<Type>4</Type>
		</Permission>
		<Permission id="188">
			<Name>ROLE_PROJECT_SETUP</Name>
			<Description>Role that will be granted to the user if she has Project setup permission.</Description>
			<Type>5</Type>
		</Permission>
		
		<!-- Touchpoint Exchange -->
		<Permission id="189">
			<Name>ROLE_TOUCHPOINT_EXCHANGE_VIEW</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Exchange view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="190">
			<Name>ROLE_TOUCHPOINT_EXCHANGE_SETUP</Name>
			<Description>Role that will be granted to the user if she has Touchpoint Exchange setup permission.</Description>
			<Type>5</Type>
		</Permission>
		
		<!-- Global Search -->
		<Permission id="191">
			<Name>ROLE_GLOBAL_SEARCH_VIEW</Name>
			<Description>Role that will be granted to the user if she has Global Search view permission.</Description>
			<Type>1</Type>
		</Permission>

        <!-- Job Center -->
        <Permission id="193">
            <Name>ROLE_JOB_CENTER_SETUP</Name>
            <Description>Role that will be granted to the user to give them the ability to download job bundles</Description>
            <Type>5</Type>
        </Permission>
        
        <!-- Local Global Management -->
        <Permission id="195">
            <Name>ROLE_LOCAL_GLOBAL_MANAGEMENT</Name>
            <Description>Role that will be granted to the user to manage the local global synchronization</Description>
            <Type>5</Type>
        </Permission>
        
        <!-- Power Edit -->
        <Permission id="198">
            <Name>ROLE_POWER_EDIT</Name>
            <Description>Role that will be granted to the user to power edit assets</Description>
            <Type>2</Type>
        </Permission>

		<!-- OnPrem DEServer Management -->
		<Permission id="203">
			<Name>ROLE_ONPREM_SERVER_MANAGEMENT</Name>
			<Description>Role that will be granted to the user to manage onprem deserver instances</Description>
			<Type>5</Type>
		</Permission>

		<!-- Dictionary -->
		<Permission id="205">
			<Name>ROLE_DICTIONARY_EDIT</Name>
			<Description>Role that will be granted to the user if she has Dictionary edit permission.</Description>
			<Type>2</Type>
		</Permission>
		
		<!-- Clipboard -->
		<Permission id="207">
			<Name>ROLE_SHARED_CLIPBOARD_VIEW</Name>
			<Description>Role that will be granted to the user if she has Shared Clipboard view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="208">
			<Name>ROLE_SHARED_CLIPBOARD_EDIT</Name>
			<Description>Role that will be granted to the user if she has Shared Clipboard edit permission.</Description>
			<Type>2</Type>
		</Permission>

		<!-- Brand -->
		<Permission id="209">
			<Name>ROLE_BRAND_VIEW</Name>
			<Description>Role that will be granted to the user if she has Brand view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="210">
			<Name>ROLE_BRAND_SETUP</Name>
			<Description>Role that will be granted to the user if she has Brand setup permission.</Description>
			<Type>5</Type>
		</Permission>

		<!-- Unrestricted Search -->
		<Permission id="211">
			<Name>ROLE_UNRESTRICTED_SEARCH</Name>
			<Description>Role that will be granted to the user if she has unrestricted Global Search permission.</Description>
			<Type>1</Type>
		</Permission>

		<!-- Global Dashboard -->
		<Permission id="245">
			<Name>ROLE_GLOBAL_DASHBOARD_VIEW</Name>
			<Description>Role that will be granted to the user if she has global dashboard view permission.</Description>
			<Type>1</Type>
		</Permission>

		<!-- Change permissions -->
		<Permission id="256">
			<Name>ROLE_CHANGE_VIEW</Name>
			<Description>Role that will be granted to the user who has changes view permission.</Description>
			<Type>1</Type>
		</Permission>
		<Permission id="257">
			<Name>ROLE_CHANGE_EDIT</Name>
			<Description>Role that will be granted to the user who has changes edit permission.</Description>
			<Type>2</Type>
		</Permission>

		<!-- PINC ROLE PERMISSION -->
		<Permission id="250">
			<Name>ROLE_PINC_COMPANY_ADMIN</Name>
			<Description>Role that will be granted to the user who has company admin permission.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="251">
			<Name>ROLE_PINC_COMPANY_READ</Name>
			<Description>Role that will be granted to the user who has company read permission.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="252">
			<Name>ROLE_PINC_COMPANY_TEST</Name>
			<Description>Role that will be granted to the user who has company test permission.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="253">
			<Name>ROLE_PINC_COMPANY_PRODUCTION</Name>
			<Description>Role that will be granted to the user who has company production permission.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="254">
			<Name>ROLE_PINC_COMPANY_AUTHOR</Name>
			<Description>Role that will be granted to the user who has company author permission.</Description>
			<Type>5</Type>
		</Permission>
		<Permission id="258">
			<Name>ROLE_PINC_ADMIN_OPERATOR</Name>
			<Description>Role that will be granted to the user who has admin operator permission.</Description>
			<Type>5</Type>
		</Permission>

		<!-- API Tokens -->
		<Permission id="259">
			<Name>ROLE_API_TOKEN_ADMIN</Name>
			<Description>Role that will be granted to the user who can manage api tokens.</Description>
			<Type>5</Type>
		</Permission>

	</Permissions>

	<Roles>
		<Role id="1">
			<Name>Content Author</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users/>
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="36" /> <!-- ROLE_MESSAGE_VIEW MINE -->
				<Permission refid="37" /> <!-- ROLE_MESSAGE_VIEW ALL -->
				<Permission refid="7" /> <!-- ROLE_MESSAGE_EDIT -->
				<Permission refid="22" /> <!-- ROLE_MESSAGE_APPROVE_EDIT -->
				<Permission refid="28" /> <!-- ROLE_TARGETING_EDIT -->
				<Permission refid="16" /> <!-- ROLE_REPORT_VIEW -->
				<Permission refid="17" /> <!-- ROLE_REPORT_EDIT -->
				<Permission refid="41" /> <!-- ROLE_TASK_EDIT -->
				<Permission refid="42" /> <!-- ROLE_TASK_VIEW_MY -->
				<Permission refid="108" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_VIEW --> 	
				<Permission refid="109" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_EDIT --> 	
				<Permission refid="110" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_APPROVALS -->
				<Permission refid="132" /> <!-- ROLE_EMBEDDED_CONTENT_VIEW -->
				<Permission refid="133" /> <!-- ROLE_EMBEDDED_CONTENT_EDIT -->
				<Permission refid="248" /> <!-- ROLE_EMBEDDED_CONTENT_REASSIGN -->
				<Permission refid="134" /> <!-- ROLE_EMBEDDED_CONTENT_APPROVE -->
				<Permission refid="136" /> <!-- ROLE_CONTENT_LIBRARY_VIEW -->
				<Permission refid="137" /> <!-- ROLE_CONTENT_LIBRARY_EDIT -->
				<Permission refid="249" /> <!-- ROLE_CONTENT_LIBRARY_REASSIGN -->
				<Permission refid="138" /> <!-- ROLE_CONTENT_LIBRARY_APPROVE -->
				<Permission refid="178" /> <!-- ROLE_TOUCHPOINT_CONTENT_VIEW -->
				<Permission refid="179" /> <!-- ROLE_TOUCHPOINT_CONTENT_EDIT -->
				<Permission refid="180" /> <!-- ROLE_TOUCHPOINT_CONTENT_APPROVE -->
				<Permission refid="183" /> <!-- ROLE_CONTENT_SOURCE_EDIT -->
			</Permissions>
		</Role>
		<Role id="3">
			<Name>Tester</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users/>
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="36" /> <!-- ROLE_MESSAGE_VIEW MINE -->
				<Permission refid="37" /> <!-- ROLE_MESSAGE_VIEW ALL -->
				<Permission refid="16" /> <!-- ROLE_REPORT_VIEW -->
				<Permission refid="17" /> <!-- ROLE_REPORT_EDIT -->
				<Permission refid="22" /> <!-- ROLE_MESSAGE_APPROVE_EDIT -->
				<Permission refid="26" /> <!-- ROLE_TESTING_VIEW -->
				<Permission refid="27" /> <!-- ROLE_TESTING_EDIT -->
				<Permission refid="62" /> <!-- ROLE_SIMULATION_VIEW -->
				<Permission refid="63" /> <!-- ROLE_SIMULATION_EDIT -->	
				<Permission refid="41" /> <!-- ROLE_TASK_EDIT -->
				<Permission refid="42" /> <!-- ROLE_TASK_VIEW_MY -->
				<Permission refid="256" /> <!-- ROLE_CHANGE_VIEW -->
				<Permission refid="257" /> <!-- ROLE_CHANGE_EDIT -->
			</Permissions>
		</Role>
		<Role id="7">
			<Name>Touchpoint Administrator</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users/>
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="5" /> <!-- ROLE_TOUCHPOINT_ADMIN -->
				<Permission refid="36" /> <!-- ROLE_MESSAGE_VIEW MINE -->
				<Permission refid="37" /> <!-- ROLE_MESSAGE_VIEW ALL -->
				<Permission refid="7" /> <!-- ROLE_MESSAGE_EDIT -->
				<Permission refid="16" /> <!-- ROLE_REPORT_VIEW -->
				<Permission refid="17" /> <!-- ROLE_REPORT_EDIT -->
				<Permission refid="22" /> <!-- ROLE_MESSAGE_APPROVE_EDIT -->
				<Permission refid="26" /> <!-- ROLE_TESTING_VIEW -->
				<Permission refid="27" /> <!-- ROLE_TESTING_EDIT -->
				<Permission refid="28" /> <!-- ROLE_TARGETING_EDIT -->
				<Permission refid="39" /> <!-- ROLE_TASK_REASSIGN -->
				<Permission refid="40" /> <!-- ROLE_TASK_SETUP -->
				<Permission refid="41" /> <!-- ROLE_TASK_EDIT -->
				<Permission refid="42" /> <!-- ROLE_TASK_VIEW_MY -->
				<Permission refid="51" /> <!-- ROLE_TOUCHPOINT_DATA_LIST -->
				<Permission refid="54" /> <!-- ROLE_TOUCHPOINT_DATA_EDIT -->
				<Permission refid="58" /> <!-- ROLE_RULE_VIEW -->
				<Permission refid="59" /> <!-- ROLE_RULOE_EDIT -->
				<Permission refid="64" /> <!-- ROLE_MESSAGE_ACTIVATION -->
				<Permission refid="65" /> <!-- ROLE_MESSAGE_REASSIGNMENT -->	
				<Permission refid="77" /> <!-- ROLE_WORKGROUPFOLDER_VIEW -->
				<Permission refid="70" /> <!-- ROLE_WORKGROUPFOLDER_EDIT -->
				<Permission refid="108" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_VIEW --> 	
				<Permission refid="109" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_EDIT --> 	
				<Permission refid="110" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_APPROVALS -->
				<Permission refid="111" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW -->
				<Permission refid="112" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_ADMIN_EDIT --> 
				<Permission refid="199" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_ADMIN_SETUP --> 
				<Permission refid="113" /> <!-- ROLE_TOUCHPOINT_SELECTION_REASSIGN -->
				<Permission refid="116" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_ARCHIVE -->
				<Permission refid="132" /> <!-- ROLE_EMBEDDED_CONTENT_VIEW -->
				<Permission refid="133" /> <!-- ROLE_EMBEDDED_CONTENT_EDIT -->
				<Permission refid="134" /> <!-- ROLE_EMBEDDED_CONTENT_APPROVE -->
				<Permission refid="248" /> <!-- ROLE_EMBEDDED_CONTENT_REASSIGN -->
				<Permission refid="135" /> <!-- ROLE_EMBEDDED_CONTENT_ADMIN -->
				<Permission refid="136" /> <!-- ROLE_CONTENT_LIBRARY_VIEW -->
				<Permission refid="137" /> <!-- ROLE_CONTENT_LIBRARY_EDIT -->
				<Permission refid="138" /> <!-- ROLE_CONTENT_LIBRARY_APPROVE -->
				<Permission refid="249" /> <!-- ROLE_CONTENT_LIBRARY_REASSIGN -->
				<Permission refid="139" /> <!-- ROLE_CONTENT_LIBRARY_ADMIN -->
				<Permission refid="168" /> <!-- ROLE_TOUCHPOINT_VERSIONING_EDIT -->
				<Permission refid="169" /> <!-- ROLE_PROJECT_SYNC_EDIT -->
				<Permission refid="178" /> <!-- ROLE_TOUCHPOINT_CONTENT_VIEW -->
				<Permission refid="179" /> <!-- ROLE_TOUCHPOINT_CONTENT_EDIT -->
				<Permission refid="180" /> <!-- ROLE_TOUCHPOINT_CONTENT_APPROVE -->
				<Permission refid="181" /> <!-- ROLE_TOUCHPOINT_CONTENT_ADMIN -->
				<Permission refid="182" /> <!-- ROLE_METADATA_FORM_ADMIN -->
				<Permission refid="183" /> <!-- ROLE_CONTENT_SOURCE_EDIT -->
				<Permission refid="189" /> <!-- ROLE_TOUCHPOINT_EXCHANGE_VIEW -->
				<Permission refid="190" /> <!-- ROLE_TOUCHPOINT_EXCHANGE_SETUP -->
				<Permission refid="256" /> <!-- ROLE_CHANGE_VIEW -->
				<Permission refid="257" /> <!-- ROLE_CHANGE_EDIT -->
			</Permissions>
		</Role>
		<Role id="8">
			<Name>System Administrator</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="41" /> <!-- ROLE_TASK_EDIT -->
				<Permission refid="42" /> <!-- ROLE_TASK_VIEW_MY -->
				<Permission refid="43" /> <!-- ROLE_TASK_VIEW_ALL -->
				<Permission refid="4" /> <!-- ROLE_SYSTEM_ADMIN -->
				<Permission refid="55" /> <!-- ROLE_ADMIN_SETTINGS_EDIT -->
				<Permission refid="52" /> <!-- ROLE_ADMIN_TENANT_VIEW -->
				<Permission refid="53" /> <!-- ROLE_ADMIN_TENANT_EDIT -->
				<Permission refid="44" /> <!-- ROLE_ADMIN_USER_VIEW -->
				<Permission refid="45" /> <!-- ROLE_ADMIN_USER_EDIT -->
				<Permission refid="46" /> <!-- ROLE_ADMIN_ROLE_VIEW -->
				<Permission refid="47" /> <!-- ROLE_ADMIN_ROLE_EDIT -->
				<Permission refid="165" /> <!-- ROLE_AUDITING_EDIT -->
				<Permission refid="203" /> <!-- ROLE_ONPREM_SERVER_MANAGEMENT -->
				<Permission refid="256" /> <!-- ROLE_CHANGE_VIEW -->
				<Permission refid="257" /> <!-- ROLE_CHANGE_EDIT -->
			</Permissions>
		</Role>
		<Role id="9">
			<Name>Super Administrator</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users>
				<User refid="1" />
			</Users>
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<!-- <Permission refid="3" />  ROLE_SUPER_ADMIN -->
				<Permission refid="4" /> <!-- ROLE_SYSTEM_ADMIN -->
				<Permission refid="55" /> <!-- ROLE_ADMIN_SETTINGS_EDIT -->
				<Permission refid="52" /> <!-- ROLE_ADMIN_TENANT_VIEW -->
				<Permission refid="53" /> <!-- ROLE_ADMIN_TENANT_EDIT -->
				<Permission refid="44" /> <!-- ROLE_ADMIN_USER_VIEW -->
				<Permission refid="45" /> <!-- ROLE_ADMIN_USER_EDIT -->
				<Permission refid="46" /> <!-- ROLE_ADMIN_ROLE_VIEW -->
				<Permission refid="47" /> <!-- ROLE_ADMIN_ROLE_EDIT -->
				<Permission refid="5" /> <!-- ROLE_TOUCHPOINT_ADMIN -->
				<Permission refid="36" /> <!-- ROLE_MESSAGE_VIEW MINE -->
				<Permission refid="37" /> <!-- ROLE_MESSAGE_VIEW ALL -->
				<Permission refid="7" /> <!-- ROLE_MESSAGE_EDIT -->
				<Permission refid="16" /> <!-- ROLE_REPORT_VIEW -->
				<Permission refid="17" /> <!-- ROLE_REPORT_EDIT -->
				<Permission refid="22" /> <!-- ROLE_MESSAGE_APPROVE_EDIT -->
				<Permission refid="26" /> <!-- ROLE_TESTING_VIEW -->
				<Permission refid="27" /> <!-- ROLE_TESTING_EDIT -->
				<Permission refid="28" /> <!-- ROLE_TARGETING_EDIT -->
				<Permission refid="39" /> <!-- ROLE_TASK_REASSIGN -->
				<Permission refid="40" /> <!-- ROLE_TASK_SETUP -->
				<Permission refid="41" /> <!-- ROLE_TASK_EDIT -->
				<Permission refid="42" /> <!-- ROLE_TASK_VIEW_MY -->
				<Permission refid="43" /> <!-- ROLE_TASK_VIEW_ALL -->
				<Permission refid="51" /> <!-- ROLE_TOUCHPOINT_DATA_LIST -->
				<Permission refid="54" /> <!-- ROLE_TOUCHPOINT_DATA_EDIT -->
				<Permission refid="58" /> <!-- ROLE_RULE_VIEW -->
				<Permission refid="59" /> <!-- ROLE_RULE_EDIT -->
				<Permission refid="62" /> <!-- ROLE_SIMULATION_VIEW -->
				<Permission refid="63" /> <!-- ROLE_SIMULATION_EDIT -->								
				<Permission refid="64" /> <!-- ROLE_MESSAGE_ACTIVATION -->						
				<Permission refid="65" /> <!-- ROLE_MESSAGE_REASSIGNMENT -->
				<Permission refid="66" /> <!-- ROLE_WORKGROUP_VIEW -->
				<Permission refid="67" /> <!-- ROLE_WORKGROUP_EDIT -->		
				<Permission refid="77" /> <!-- ROLE_WORKGROUPFOLDER_VIEW -->
				<Permission refid="70" /> <!-- ROLE_WORKGROUPFOLDER_EDIT -->															
				<Permission refid="76" /> <!-- ROLE_TENANT_SUPERVISOR -->
				<Permission refid="82" /> <!-- ROLE_INSERT_VIEW -->
				<Permission refid="83" /> <!-- ROLE_INSERT_EDIT --> 															
				<Permission refid="84" /> <!-- ROLE_INSERT_APPROVE --> 	
				<Permission refid="85" /> <!-- ROLE_INSERT_ARCHIVE -->
				<Permission refid="86" /> <!-- ROLE_INSERT_REASSIGN -->
				<Permission refid="92" /> <!-- ROLE_INSERT_SCHEDULE_VIEW -->
				<Permission refid="93" /> <!-- ROLE_INSERT_SCHEDULE_EDIT --> 															
				<Permission refid="94" /> <!-- ROLE_INSERT_SCHEDULE_APPROVE --> 	
				<Permission refid="95" /> <!-- ROLE_INSERT_SCHEDULE_SETUP --> 	
				<Permission refid="97" /> <!-- ROLE_INSERT_SCHEDULE_REASSIGN -->
				<Permission refid="104" /> <!-- ROLE_RATE_SCHEDULE_VIEW -->
				<Permission refid="105" /> <!-- ROLE_RATE_SCHEDULE_EDIT -->
				<Permission refid="108" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_VIEW --> 	
				<Permission refid="109" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_EDIT --> 	
				<Permission refid="110" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_APPROVALS -->
				<Permission refid="111" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW -->
				<Permission refid="112" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_ADMIN_EDIT --> 
				<Permission refid="199" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_ADMIN_SETUP --> 
				<Permission refid="113" /> <!-- ROLE_TOUCHPOINT_SELECTION_REASSIGN -->
				<Permission refid="116" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_ARCHIVE -->
				<Permission refid="117" /> <!-- ROLE_TAG_VIEW --> 	
				<Permission refid="118" /> <!-- ROLE_TAG_EDIT -->
				<Permission refid="119" /> <!-- ROLE_TAG_VIEW_ALL -->
				<Permission refid="120" /> <!-- ROLE_EXTERNAL_EVENT_VIEW -->
				<Permission refid="121" /> <!-- ROLE_EXTERNAL_EVENT_EDIT -->
				<Permission refid="132" /> <!-- ROLE_EMBEDDED_CONTENT_VIEW -->
				<Permission refid="133" /> <!-- ROLE_EMBEDDED_CONTENT_EDIT -->
				<Permission refid="134" /> <!-- ROLE_EMBEDDED_CONTENT_APPROVE -->
				<Permission refid="248" /> <!-- ROLE_EMBEDDED_CONTENT_REASSIGN -->
				<Permission refid="135" /> <!-- ROLE_EMBEDDED_CONTENT_ADMIN -->
				<Permission refid="136" /> <!-- ROLE_CONTENT_LIBRARY_VIEW -->
				<Permission refid="137" /> <!-- ROLE_CONTENT_LIBRARY_EDIT -->
				<Permission refid="138" /> <!-- ROLE_CONTENT_LIBRARY_APPROVE -->
				<Permission refid="249" /> <!-- ROLE_CONTENT_LIBRARY_APPROVE -->
				<Permission refid="139" /> <!-- ROLE_CONTENT_LIBRARY_ADMIN -->
				<Permission refid="140" /> <!-- ROLE_WEB_SERVICE_UPDATE -->
				<Permission refid="141" /> <!-- ROLE_STYLES_UPDATE -->
				<Permission refid="142" /> <!-- ROLE_WORKFLOW_ADMIN_EDIT -->
				<Permission refid="143" /> <!-- ROLE_COMMUNICATIONS_VIEW -->
				<Permission refid="144" /> <!-- ROLE_COMMUNICATIONS_EDIT -->
				<Permission refid="145" /> <!-- ROLE_COMMUNICATIONS_APPROVE -->
				<Permission refid="246" /> <!-- ROLE_COMMUNICATIONS_REASSIGN -->
				<Permission refid="152" /> <!-- ROLE_COMMUNICATIONS_SETUP -->
				<Permission refid="149" /> <!-- ROLE_PROOFING_DATA_UPDATE -->
				<Permission refid="150" /> <!-- ROLE_PRIORITY_ADMIN -->
				<Permission refid="151" /> <!-- ROLE_COLLECTION_SETUP -->
				<Permission refid="161"	/> <!-- ROLE_ECATALOG_VIEW -->
				<Permission refid="162"	/> <!-- ROLE_ECATALOG_EDIT -->
				<Permission refid="201"	/> <!-- ROLE_ECATALOG_APPROVE -->
				<Permission refid="196" /> <!-- ROLE_ECATALOG_ADMIN -->
				<Permission refid="165" /> <!-- ROLE_AUDITING_EDIT -->
				<Permission refid="166" /> <!-- ROLE_METATAGS_VIEW -->
				<Permission refid="255" /> <!-- ROLE_METATAGS_ADMIN -->
				<Permission refid="167" /> <!-- ROLE_GLOBAL_EMBEDDED_CONTENT_EDIT -->
				<Permission refid="168" /> <!-- ROLE_TOUCHPOINT_VERSIONING_EDIT -->
				<Permission refid="169" /> <!-- ROLE_PROJECT_SYNC_EDIT -->
				<Permission refid="170" /> <!-- ROLE_LOOKUP_TABLE_VIEW -->
				<Permission refid="171" /> <!-- ROLE_LOOKUP_TABLE_EDIT -->
				<Permission refid="172" /> <!-- ROLE_LOOKUP_TABLE_APPROVE -->
				<Permission refid="173" /> <!-- ROLE_LOOKUP_TABLE_SETUP -->
				<Permission refid="178" /> <!-- ROLE_TOUCHPOINT_CONTENT_VIEW -->
				<Permission refid="179" /> <!-- ROLE_TOUCHPOINT_CONTENT_EDIT -->
				<Permission refid="180" /> <!-- ROLE_TOUCHPOINT_CONTENT_APPROVE -->
				<Permission refid="181" /> <!-- ROLE_TOUCHPOINT_CONTENT_ADMIN -->
				<Permission refid="182" /> <!-- ROLE_METADATA_FORM_ADMIN -->
				<Permission refid="183" /> <!-- ROLE_CONTENT_SOURCE_EDIT -->
				<Permission refid="185" /> <!-- ROLE_PROJECT_VIEW -->
				<Permission refid="186" /> <!-- ROLE_PROJECT_EDIT -->
				<Permission refid="187" /> <!-- ROLE_PROJECT_APPROVE -->
				<Permission refid="192" /> <!-- ROLE_PROJECT_REASSIGN -->
				<Permission refid="188" /> <!-- ROLE_PROJECT_SETUP -->
				<Permission refid="189" /> <!-- ROLE_TOUCHPOINT_EXCHANGE_VIEW -->
				<Permission refid="190" /> <!-- ROLE_TOUCHPOINT_EXCHANGE_SETUP -->
				<Permission refid="191" /> <!-- ROLE_GLOBAL_SEARCH_VIEW -->
                <Permission refid="193" /> <!-- ROLE_JOB_CENTER_SETUP -->
                <Permission refid="195" /> <!-- ROLE_LOCAL_GLOBAL_MANAGEMENT -->
                <Permission refid="198" /> <!-- ROLE_POWER_EDIT -->
				<Permission refid="203" /> <!-- ROLE_ONPREM_SERVER_MANAGEMENT -->
				<Permission refid="205" /> <!-- ROLE_DICTIONARY_EDIT -->
				<Permission refid="207" /> <!-- ROLE_SHARED_CLIPBOARD_VIEW -->
				<Permission refid="208" /> <!-- ROLE_SHARED_CLIPBOARD_EDIT -->
				<Permission refid="209" /> <!-- ROLE_BRAND_EDIT -->
				<Permission refid="210" /> <!-- ROLE_BRAND_SETUP -->
				<Permission refid="211" /> <!-- ROLE_UNRESTRICTED_SEARCH -->
				<Permission refid="212" /> <!-- ROLE_WORKFLOW_ABORT -->
				<Permission refid="245" /> <!-- ROLE_GLOBAL_DASHBOARD_VIEW -->
				<Permission refid="256" /> <!-- ROLE_CHANGE_VIEW -->
				<Permission refid="257" /> <!-- ROLE_CHANGE_EDIT -->
				<Permission refid="259" /> <!-- ROLE_API_TOKEN_ADMIN -->
				<Permission refid="262" /> <!-- ROLE_COMMUNICATIONS_API_SETUP -->
			</Permissions>
		</Role>
		<Role id="10">
			<Name>Report Producer</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users/>
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="41" /> <!-- ROLE_TASK_EDIT -->
				<Permission refid="42" /> <!-- ROLE_TASK_VIEW_MY -->
				<Permission refid="43" /> <!-- ROLE_TASK_VIEW_ALL -->
				<Permission refid="16" /> <!-- ROLE_REPORT_VIEW -->
				<Permission refid="17" /> <!-- ROLE_REPORT_EDIT -->
			</Permissions>
		</Role>
		
		<Role id="96">
			<Name>Connected Access</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users/>
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="41" /> <!-- ROLE_TASK_EDIT -->
				<Permission refid="42" /> <!-- ROLE_TASK_VIEW_MY -->
				<Permission refid="143" /> <!-- ROLE_COMMUNICATIONS_VIEW -->
				<Permission refid="144" /> <!-- ROLE_COMMUNICATIONS_EDIT -->
				<Permission refid="145" /> <!-- ROLE_COMMUNICATIONS_APPROVE -->
				<Permission refid="246" /> <!-- ROLE_COMMUNICATIONS_REASSIGN -->
				<Permission refid="152" /> <!-- ROLE_COMMUNICATIONS_SETUP -->
				<Permission refid="166" /> <!-- ROLE_METATAGS_VIEW -->
				<Permission refid="262" /> <!-- ROLE_COMMUNICATIONS_API_SETUP -->
			</Permissions>
		</Role>
		
		<Role id="97">
			<Name>Workflow Access</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users/>
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="22" /> <!-- ROLE_MESSAGE_APPROVE_EDIT -->
				<Permission refid="36" /> <!-- ROLE_MESSAGE_VIEW MINE -->
				<Permission refid="37" /> <!-- ROLE_MESSAGE_VIEW ALL -->
				<Permission refid="41" /> <!-- ROLE_TASK_EDIT -->
				<Permission refid="42" /> <!-- ROLE_TASK_VIEW_MY -->
				<Permission refid="82" /> <!-- ROLE_INSERT_VIEW -->
				<Permission refid="84" /> <!-- ROLE_INSERT_APPROVE --> 	
				<Permission refid="92" /> <!-- ROLE_INSERT_SCHEDULE_VIEW -->
				<Permission refid="94" /> <!-- ROLE_INSERT_SCHEDULE_APPROVE --> 	
				<Permission refid="108" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_VIEW --> 	
				<Permission refid="110" /> <!-- ROLE_TOUCHPOINT_SELECTIONS_APPROVALS -->
				<Permission refid="132" /> <!-- ROLE_EMBEDDED_CONTENT_VIEW -->
				<Permission refid="134" /> <!-- ROLE_EMBEDDED_CONTENT_APPROVE -->
				<Permission refid="136" /> <!-- ROLE_CONTENT_LIBRARY_VIEW -->
				<Permission refid="138" /> <!-- ROLE_CONTENT_LIBRARY_APPROVE -->
				<Permission refid="143" /> <!-- ROLE_COMMUNICATIONS_VIEW -->
				<Permission refid="145" /> <!-- ROLE_COMMUNICATIONS_APPROVE -->
				<Permission refid="170" /> <!-- ROLE_LOOKUP_TABLE_VIEW -->
				<Permission refid="172" /> <!-- ROLE_LOOKUP_TABLE_APPROVE -->
				<Permission refid="178" /> <!-- ROLE_TOUCHPOINT_CONTENT_VIEW -->
				<Permission refid="180" /> <!-- ROLE_TOUCHPOINT_CONTENT_APPROVE -->
				<Permission refid="166" /> <!-- ROLE_METATAGS_VIEW -->
			</Permissions>
		</Role>
		<Role id="98">
			<Name>Default Access</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users/>
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
			</Permissions>
		</Role>
		<Role id="99">
			<Name>Restricted Access</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users/>
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
			</Permissions>
		</Role>
		
		<Role id="900">
			<Name>Master Administrator</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<HiddenFlag>TRUE</HiddenFlag>
			<Users>
				<User refid="900" />
				<User refid="999" />
			</Users>
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="4" /> <!-- ROLE_SYSTEM_ADMIN -->
				<Permission refid="55" /> <!-- ROLE_ADMIN_SETTINGS_EDIT -->
				<Permission refid="203" /> <!-- ROLE_ONPREM_SERVER_MANAGEMENT -->
				<Permission refid="259" /> <!-- ROLE_API_TOKEN_ADMIN -->
			</Permissions>
		</Role>
		
		<Role id="901">
			<Name>Messagepoint Web Services</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<HiddenFlag>TRUE</HiddenFlag>
			<Users>
				<User refid="901" />
			</Users>
			<Permissions>
				<Permission refid="140" /> <!-- ROLE_WEB_SERVICE_UPDATE -->
			</Permissions>
		</Role>

		<Role id="10001">
			<Name>pinc-company-admin</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users />
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="250" /> <!-- ROLE_PINC_COMPANY_ADMIN -->
			</Permissions>
		</Role>

		<Role id="10002">
			<Name>pinc-company-read</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users />
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="251" /> <!-- ROLE_PINC_COMPANY_READ -->
			</Permissions>
		</Role>

		<Role id="10003">
			<Name>pinc-company-test</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users />
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="252" /> <!-- ROLE_PINC_COMPANY_TEST -->
			</Permissions>
		</Role>

		<Role id="10004">
			<Name>pinc-company-production</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users />
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="253" /> <!-- ROLE_PINC_COMPANY_PRODUCTION -->
			</Permissions>
		</Role>

		<Role id="10005">
			<Name>pinc-company-author</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users />
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="254" /> <!-- ROLE_PINC_COMPANY_AUTHOR -->
			</Permissions>
		</Role>

		<Role id="10101">
			<Name>pinc-admin-operator</Name>
			<Description>Role that will be granted to the user who has admin operator permission</Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users />
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
				<Permission refid="258" /> <!-- ROLE_PINC_ADMIN_OPERATOR -->
			</Permissions>
		</Role>

		<Role id="10201">
			<Name>pinc-cluster-read</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users />
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
			</Permissions>
		</Role>

		<Role id="10202">
			<Name>pinc-cluster-admin</Name>
			<Description></Description>
			<Active>TRUE</Active>
			<Visibility>1</Visibility>
			<Users />
			<Permissions>
				<Permission refid="2" /> <!-- ROLE_USER -->
			</Permissions>
		</Role>

	</Roles>

	<CategoryGroups>
		<CategoryGroup id="1">
			<Name>page.label.category.touchpoint.setup</Name>
			<Description>Permissions related touchpoint administration including touchpoint content, collections, external events, proofing data, touchpoint exchange, and metatag management</Description>
			<Priority>1</Priority>
			<PermissionCategories>
				<PermissionCategory refid="47" />
				<PermissionCategory refid="31" />
				<PermissionCategory refid="35" />
				<PermissionCategory refid="32" />
				<PermissionCategory refid="61" />
				<PermissionCategory refid="59" />
				<PermissionCategory refid="60" />
				<PermissionCategory refid="49" />
				<PermissionCategory refid="66" />
				<PermissionCategory refid="64" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="2">
			<Name>page.label.category.workflow</Name>
			<Description>Workflow setup permissions</Description>
			<Priority>2</Priority>
			<PermissionCategories>
				<PermissionCategory refid="45" />
				<PermissionCategory refid="76" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="3">
			<Name>page.label.category.targeting</Name>
			<Description>Targeting management permissions</Description>
			<Priority>3</Priority>
			<PermissionCategories>
				<PermissionCategory refid="13" />
				<PermissionCategory refid="8" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="4">
			<Name>page.label.category.messaging</Name>
			<Description>Targeting management permissions</Description>
			<Priority>4</Priority>
			<PermissionCategories>
				<PermissionCategory refid="3" />
				<PermissionCategory refid="16" />
				<PermissionCategory refid="48" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="5">
			<Name>page.label.category.shared.libraries</Name>
			<Description>Shared library management permissions</Description>
			<Priority>5</Priority>
			<PermissionCategories>
				<PermissionCategory refid="39" />
				<PermissionCategory refid="41" />
				<PermissionCategory refid="44" />
				<PermissionCategory refid="53" />
                <PermissionCategory refid="70" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="6">
			<Name>page.label.category.tasks</Name>
			<Description>Task management permissions</Description>
			<Priority>6</Priority>
			<PermissionCategories>
				<PermissionCategory refid="1" />
				<PermissionCategory refid="2" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="7">
			<Name>page.label.category.test.proof.simulation</Name>
			<Description>Test, proof, and simulation management permissions</Description>
			<Priority>7</Priority>
			<PermissionCategories>
				<PermissionCategory refid="5" />
				<PermissionCategory refid="47" />
				<PermissionCategory refid="15" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="8">
			<Name>page.label.category.versions</Name>
			<Description>Version management permissions</Description>
			<Priority>8</Priority>
			<PermissionCategories>
				<PermissionCategory refid="55" />
				<PermissionCategory refid="56" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="9">
			<Name>page.label.category.data</Name>
			<Description>Data management permissions</Description>
			<Priority>9</Priority>
			<PermissionCategories>
				<PermissionCategory refid="7" />
				<PermissionCategory refid="51" />
				<PermissionCategory refid="61" />
				<PermissionCategory refid="57" />
				<PermissionCategory refid="58" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="10">
			<Name>page.label.category.administrative</Name>
			<Description>Administrative management permissions</Description>
			<Priority>10</Priority>
			<PermissionCategories>
				<PermissionCategory refid="9" />
				<PermissionCategory refid="10" />
				<PermissionCategory refid="11" />
				<PermissionCategory refid="12" />
				<PermissionCategory refid="18" />
				<PermissionCategory refid="43" />
				<PermissionCategory refid="51" />
				<PermissionCategory refid="71" />
				<PermissionCategory refid="88" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="11">
			<Name>page.label.category.inserts</Name>
			<Description>Insert management permissions</Description>
			<Priority>11</Priority>
			<PermissionCategories>
				<PermissionCategory refid="23" />
				<PermissionCategory refid="24" />
				<PermissionCategory refid="26" />
				<PermissionCategory refid="30" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="12">
			<Name>page.label.category.reporting</Name>
			<Description>Reporting management permissions</Description>
			<Priority>12</Priority>
			<PermissionCategories>
				<PermissionCategory refid="4" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="13">
			<Name>page.label.category.connected</Name>
			<Description>Connected management permissions</Description>
			<Priority>13</Priority>
			<PermissionCategories>
				<PermissionCategory refid="46" />
				<PermissionCategory refid="50" />
				<PermissionCategory refid="90" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="15">
			<Name>page.label.category.projects</Name>
			<Description>Project management permissions</Description>
			<Priority>15</Priority>
			<PermissionCategories>
				<PermissionCategory refid="65" />
			</PermissionCategories>
		</CategoryGroup>
		<CategoryGroup id="20">
			<Name>page.label.category.miscellaneous</Name>
			<Description>Miscellaneous management permissions</Description>
			<Priority>20</Priority>
			<PermissionCategories>
				<PermissionCategory refid="36" />
				<PermissionCategory refid="52" />
				<PermissionCategory refid="37" />
				<PermissionCategory refid="54" />
				<PermissionCategory refid="22" />
				<PermissionCategory refid="67" />
				<PermissionCategory refid="75" />
                <PermissionCategory refid="38" />
                <PermissionCategory refid="68" />
                <PermissionCategory refid="69" />
                <PermissionCategory refid="72" />
                <PermissionCategory refid="73" />
				<PermissionCategory refid="74" />
				<PermissionCategory refid="87" />
			</PermissionCategories>
		</CategoryGroup>
	</CategoryGroups>

</SecurityMetadata>
