drop table aggregation_operator cascade constraints;
drop table all_reference_query cascade constraints;
drop table app_version cascade constraints;
drop table application_locale cascade constraints;
drop table approval cascade constraints;
drop table approval_detail cascade constraints;
drop table approval_user cascade constraints;
drop table attachment cascade constraints;
drop table attachment_document cascade constraints;
drop table attachment_tg_instance_map cascade constraints;
drop table attchmnt_target_group_excluded cascade constraints;
drop table attchmnt_target_group_extended cascade constraints;
drop table attchmnt_target_group_included cascade constraints;
drop table audit_event cascade constraints;
drop table audit_report cascade constraints;
drop table background_themes cascade constraints;
drop table barcode_type cascade constraints;
drop table branch cascade constraints;
drop table brand_profile cascade constraints;
drop table cache_data cascade constraints;
drop table category_group cascade constraints;
drop table clickatell_configuration cascade constraints;
drop table clipboard_content cascade constraints;
drop table columnar_indicators cascade constraints;
drop table comm_ext_proof_validation_map cascade constraints;
drop table comm_prod_event_communications cascade constraints;
drop table comm_zone_content_association cascade constraints;
drop table communication cascade constraints;
drop table communication_mini_prod_event cascade constraints;
drop table communication_production_event cascade constraints;
drop table communication_proof cascade constraints;
drop table complex_value cascade constraints;
drop table complex_value_content_object cascade constraints;
drop table complex_value_data_elements cascade constraints;
drop table complex_value_lookup_tables cascade constraints;
drop table complex_value_variable cascade constraints;
drop table composition_additional_files cascade constraints;
drop table composition_file_set cascade constraints;
drop table compound_key cascade constraints;
drop table compound_key_item cascade constraints;
drop table condition_element cascade constraints;
drop table condition_item cascade constraints;
drop table condition_item_attrib cascade constraints;
drop table condition_item_value cascade constraints;
drop table condition_operator cascade constraints;
drop table condition_param_map cascade constraints;
drop table condition_sub_attrib cascade constraints;
drop table condition_subelement cascade constraints;
drop table condition_type cascade constraints;
drop table configurable_link cascade constraints;
drop table connected_tp_sel_visible_user cascade constraints;
drop table connection_resource cascade constraints;
drop table content cascade constraints;
drop table content_content_object_type cascade constraints;
drop table content_content_targeting cascade constraints;
drop table content_images cascade constraints;
drop table content_list_style cascade constraints;
drop table content_object cascade constraints;
drop table content_object_association cascade constraints;
drop table content_object_comment cascade constraints;
drop table content_object_data cascade constraints;
drop table content_object_document_map cascade constraints;
drop table content_object_tg_excluded_map cascade constraints;
drop table content_object_tg_extended_map cascade constraints;
drop table content_object_tg_included_map cascade constraints;
drop table content_object_tg_instance_map cascade constraints;
drop table content_object_tpc_map cascade constraints;
drop table content_object_zone_priority cascade constraints;
drop table content_paragraph_style cascade constraints;
drop table content_placeholders cascade constraints;
drop table content_target_group_excluded cascade constraints;
drop table content_target_group_extended cascade constraints;
drop table content_target_group_included cascade constraints;
drop table content_targeting cascade constraints;
drop table content_text_style cascade constraints;
drop table content_tg_instance_map cascade constraints;
drop table content_type cascade constraints;
drop table content_variable cascade constraints;
drop table daily_frequency_type cascade constraints;
drop table dashboard_filters cascade constraints;
drop table data_comparison cascade constraints;
drop table data_element_core cascade constraints;
drop table data_element_flat cascade constraints;
drop table data_element_variable cascade constraints;
drop table data_element_xml cascade constraints;
drop table data_file cascade constraints;
drop table data_file_document cascade constraints;
drop table data_file_preview_languages cascade constraints;
drop table data_file_tpc cascade constraints;
drop table data_group cascade constraints;
drop table data_group_record cascade constraints;
drop table data_record cascade constraints;
drop table data_record_level cascade constraints;
drop table data_resource cascade constraints;
drop table data_source cascade constraints;
drop table data_source_association cascade constraints;
drop table data_subtype cascade constraints;
drop table data_subtype_comparison cascade constraints;
drop table data_type cascade constraints;
drop table data_type_subtype cascade constraints;
drop table database_file cascade constraints;
drop table date_data_value cascade constraints;
drop table de_state_transition cascade constraints;
drop table deactivate_types cascade constraints;
drop table delimited_indicators cascade constraints;
drop table delivery_event cascade constraints;
drop table delivery_event_schedule cascade constraints;
drop table delivery_event_type cascade constraints;
drop table deserver cascade constraints;
drop table deserver_bundle_type_state cascade constraints;
drop table deserver_communicationtype cascade constraints;
drop table dev_de_map cascade constraints;
drop table dev_ds_hash_map cascade constraints;
drop table dialogue_configuration cascade constraints;
drop table dialogue_data_mapping cascade constraints;
drop table dialogue_metadata_tags cascade constraints;
drop table dictionary cascade constraints;
drop table document cascade constraints;
drop table document_history cascade constraints;
drop table document_preview cascade constraints;
drop table document_production_event cascade constraints;
drop table document_section cascade constraints;
drop table document_tenant_email_address cascade constraints;
drop table domain cascade constraints;
drop table domain_pod cascade constraints;
drop table emessaging_configuration cascade constraints;
drop table encoding_type cascade constraints;
drop table event_type cascade constraints;
drop table exact_target_last_query cascade constraints;
drop table exacttarget_configuration cascade constraints;
drop table ext_reporting_vars_doc cascade constraints;
drop table external_event cascade constraints;
drop table external_proof_validation cascade constraints;
drop table fileroot_man_profile cascade constraints;
drop table filter_condition cascade constraints;
drop table filter_condition_value_map cascade constraints;
drop table folder_insert cascade constraints;
drop table folders cascade constraints;
drop table frequency_type cascade constraints;
drop table ftp_configuration cascade constraints;
drop table generic_configuration cascade constraints;
drop table gmc_configuration cascade constraints;
drop table hist_content cascade constraints;
drop table hist_content_content_object_t cascade constraints;
drop table hist_content_content_targeting cascade constraints;
drop table hist_content_images cascade constraints;
drop table hist_content_list_style cascade constraints;
drop table hist_content_obj_association cascade constraints;
drop table hist_content_object_data cascade constraints;
drop table hist_content_paragraph_style cascade constraints;
drop table hist_content_placeholders cascade constraints;
drop table hist_content_text_style cascade constraints;
drop table hist_content_variable cascade constraints;
drop table hist_meta_form_item_value_set cascade constraints;
drop table hist_metadata_form cascade constraints;
drop table hist_metadata_form_item cascade constraints;
drop table hist_rat_shared_content cascade constraints;
drop table hist_rationalizer_content cascade constraints;
drop table history_task cascade constraints;
drop table history_task_user cascade constraints;
drop table insert_document cascade constraints;
drop table insert_obj cascade constraints;
drop table insert_rate_schedule cascade constraints;
drop table insert_schedule cascade constraints;
drop table insert_schedule_bin_assignment cascade constraints;
drop table insert_schedule_collection cascade constraints;
drop table insert_target_group_excluded cascade constraints;
drop table insert_target_group_extended cascade constraints;
drop table insert_target_group_included cascade constraints;
drop table insert_tg_instance_map cascade constraints;
drop table item_type cascade constraints;
drop table job cascade constraints;
drop table language_content_hash cascade constraints;
drop table language_content_hash_map cascade constraints;
drop table language_selection cascade constraints;
drop table layout_type cascade constraints;
drop table licence cascade constraints;
drop table licence_application cascade constraints;
drop table licence_attributes cascade constraints;
drop table licence_audit cascade constraints;
drop table licence_audit_action cascade constraints;
drop table licence_change_reasons cascade constraints;
drop table licence_customer cascade constraints;
drop table licence_customer_keypair cascade constraints;
drop table licence_history cascade constraints;
drop table licence_history_reason cascade constraints;
drop table licence_module cascade constraints;
drop table licence_representative cascade constraints;
drop table licence_resource cascade constraints;
drop table licence_server cascade constraints;
drop table licence_server_ip_address cascade constraints;
drop table list_style cascade constraints;
drop table list_style_cust cascade constraints;
drop table list_style_cust_map cascade constraints;
drop table lookup_table cascade constraints;
drop table lookup_table_document cascade constraints;
drop table lookup_table_instance cascade constraints;
drop table lookup_table_tpc cascade constraints;
drop table lookup_table_version_map cascade constraints;
drop table lookup_value cascade constraints;
drop table message_delivery_report cascade constraints;
drop table message_simulation_report cascade constraints;
drop table messagepoint_locale cascade constraints;
drop table meta_form_item_def_con_ele_map cascade constraints;
drop table metadata_form cascade constraints;
drop table metadata_form_definition cascade constraints;
drop table metadata_form_item cascade constraints;
drop table metadata_form_item_definition cascade constraints;
drop table metadata_form_item_value_set cascade constraints;
drop table metadata_points_of_interest cascade constraints;
drop table mp_channel cascade constraints;
drop table mp_connector cascade constraints;
drop table mp_licence cascade constraints;
drop table mp_qualification_output cascade constraints;
drop table msg_delivery_report_doc cascade constraints;
drop table msg_delivery_report_msg cascade constraints;
drop table msg_simulation_report_doc cascade constraints;
drop table msg_simulation_report_msg cascade constraints;
drop table native_comp_configuration cascade constraints;
drop table navigation_drop_down_menu cascade constraints;
drop table navigation_drop_down_menu_item cascade constraints;
drop table navigation_menu_item_perm cascade constraints;
drop table navigation_tab_default_map cascade constraints;
drop table navigation_tab_permission cascade constraints;
drop table navigation_tabs cascade constraints;
drop table navigation_tree cascade constraints;
drop table node cascade constraints;
drop table notification_email cascade constraints;
drop table notification_settings cascade constraints;
drop table notification_settings_tp cascade constraints;
drop table oidc_signing_keys cascade constraints;
drop table old_workflow cascade constraints;
drop table operations_report_doc cascade constraints;
drop table operations_report_event cascade constraints;
drop table operations_report_scenario cascade constraints;
drop table order_entry_def_con_ele_map cascade constraints;
drop table order_entry_item cascade constraints;
drop table order_entry_item_con_ele_map cascade constraints;
drop table order_entry_item_definition cascade constraints;
drop table order_item_def_selections cascade constraints;
drop table order_item_selections cascade constraints;
drop table para_style_cust cascade constraints;
drop table para_style_cust_line_spacings cascade constraints;
drop table para_style_cust_map cascade constraints;
drop table paragraph_style cascade constraints;
drop table paragraph_style_line_spacings cascade constraints;
drop table parameter cascade constraints;
drop table parameter_group cascade constraints;
drop table parameter_group_instance cascade constraints;
drop table parameter_group_item cascade constraints;
drop table password_history cascade constraints;
drop table password_recovery cascade constraints;
drop table permission cascade constraints;
drop table permission_category cascade constraints;
drop table pg_instance_collection cascade constraints;
drop table pg_tree_node cascade constraints;
drop table pod cascade constraints;
drop table pod_status cascade constraints;
drop table pod_type cascade constraints;
drop table project cascade constraints;
drop table project_applied_wf_steps cascade constraints;
drop table project_step_req_note_map cascade constraints;
drop table project_task cascade constraints;
drop table project_wf_step_user_map cascade constraints;
drop table proof cascade constraints;
drop table proof_definition cascade constraints;
drop table rat_hist_shared_content cascade constraints;
drop table rat_hist_shared_content_form cascade constraints;
drop table rate_schedule cascade constraints;
drop table rate_schedule_collection cascade constraints;
drop table rate_schedule_detail cascade constraints;
drop table rationalizer_app_nav_tree cascade constraints;
drop table rationalizer_app_visible_user cascade constraints;
drop table rationalizer_application cascade constraints;
drop table rationalizer_document cascade constraints;
drop table rationalizer_document_content cascade constraints;
drop table rationalizer_filter_item_defs cascade constraints;
drop table rationalizer_hist_content cascade constraints;
drop table rationalizer_hist_form cascade constraints;
drop table rationalizer_query cascade constraints;
drop table rationalizer_query_component cascade constraints;
drop table rationalizer_shared_content cascade constraints;
drop table record_type cascade constraints;
drop table redirection_info cascade constraints;
drop table reference_connection cascade constraints;
drop table reference_query cascade constraints;
drop table report_scenario cascade constraints;
drop table report_scenario_delivery_event cascade constraints;
drop table report_scenario_message cascade constraints;
drop table role cascade constraints;
drop table role_permission cascade constraints;
drop table sandbox_file cascade constraints;
drop table search_result_ids cascade constraints;
drop table security_settings cascade constraints;
drop table sefas_configuration cascade constraints;
drop table seg_analysis_analyzables cascade constraints;
drop table segmentation_analysis cascade constraints;
drop table sendmail_configuration cascade constraints;
drop table simulation cascade constraints;
drop table simulation_content_object cascade constraints;
drop table source_type cascade constraints;
drop table status_polling_background_task cascade constraints;
drop table sub_content_type cascade constraints;
drop table sync_history cascade constraints;
drop table sync_history_locales cascade constraints;
drop table system_notification cascade constraints;
drop table system_property cascade constraints;
drop table system_state cascade constraints;
drop table system_task cascade constraints;
drop table system_task_type cascade constraints;
drop table system_task_user cascade constraints;
drop table system_theme cascade constraints;
drop table tag cascade constraints;
drop table tag_cloud cascade constraints;
drop table tag_content_object cascade constraints;
drop table tag_document cascade constraints;
drop table tag_insert cascade constraints;
drop table tag_target_group_excluded cascade constraints;
drop table tag_target_group_extended cascade constraints;
drop table tag_target_group_included cascade constraints;
drop table tag_tg_instance_map cascade constraints;
drop table tag_touchpoint_collection cascade constraints;
drop table tag_type cascade constraints;
drop table target_group cascade constraints;
drop table target_group_document cascade constraints;
drop table target_group_instance cascade constraints;
drop table task cascade constraints;
drop table task_document cascade constraints;
drop table task_item cascade constraints;
drop table task_user cascade constraints;
drop table template_modifier cascade constraints;
drop table template_variant cascade constraints;
drop table tenant_metadata cascade constraints;
drop table tenant_permission cascade constraints;
drop table tenant_theme_info cascade constraints;
drop table tenants cascade constraints;
drop table test_scenario cascade constraints;
drop table test_scenario_content_object cascade constraints;
drop table test_suite cascade constraints;
drop table text_style cascade constraints;
drop table text_style_colors cascade constraints;
drop table text_style_cust cascade constraints;
drop table text_style_cust_colors cascade constraints;
drop table text_style_cust_map cascade constraints;
drop table text_style_cust_point_sizes cascade constraints;
drop table text_style_font cascade constraints;
drop table text_style_point_sizes cascade constraints;
drop table touchpoint_collection cascade constraints;
drop table touchpoint_language cascade constraints;
drop table touchpoint_locale cascade constraints;
drop table touchpoint_selection cascade constraints;
drop table touchpoint_selection_workgroup cascade constraints;
drop table touchpoint_targeting cascade constraints;
drop table touchpoint_tg_instance_map cascade constraints;
drop table tp_collection_touchpoint cascade constraints;
drop table tp_delivery_report_doc cascade constraints;
drop table tp_delivery_report_event cascade constraints;
drop table tp_delivery_report_scenario cascade constraints;
drop table tp_instance_locales cascade constraints;
drop table tp_instance_visibility_map cascade constraints;
drop table tp_sel_visible_user cascade constraints;
drop table tp_target_group_excluded cascade constraints;
drop table tp_target_group_extended cascade constraints;
drop table tp_target_group_included cascade constraints;
drop table user_permission cascade constraints;
drop table user_role cascade constraints;
drop table users cascade constraints;
drop table variable_data_element_map cascade constraints;
drop table variable_document cascade constraints;
drop table version_activity_reason cascade constraints;
drop table version_status cascade constraints;
drop table web_service_config cascade constraints;
drop table workflow cascade constraints;
drop table workflow_action cascade constraints;
drop table workflow_action_history cascade constraints;
drop table workflow_instance cascade constraints;
drop table workflow_library_document cascade constraints;
drop table workflow_position cascade constraints;
drop table workflow_position_permission cascade constraints;
drop table workflow_position_user cascade constraints;
drop table workflow_property cascade constraints;
drop table workflow_state cascade constraints;
drop table workflow_step cascade constraints;
drop table workflow_step_langs cascade constraints;
drop table workflow_tab cascade constraints;
drop table workflow_tab_permission cascade constraints;
drop table workgroup cascade constraints;
drop table workgroup_zone cascade constraints;
drop table xml_data_tag_definition cascade constraints;
drop table zone cascade constraints;
drop table zone_attributes cascade constraints;
drop table zone_image_assets cascade constraints;
drop table zone_list_style cascade constraints;
drop table zone_paragraph_style cascade constraints;
drop table zone_part cascade constraints;
drop table zone_smart_text_assets cascade constraints;
drop table zone_style cascade constraints;
drop sequence hibernate_sequence;
create table aggregation_operator (id number(10,0) not null, operator varchar2(255 char) not null, primary key (id));
create table all_reference_query (id number(19,0) not null, object_class_name varchar2(255 char) not null, direct_ref_class_name varchar2(255 char), f_query varchar2(4000 char), f_type varchar2(8 char), primary key (id));
create table app_version (id number(19,0) not null, version_key varchar2(255 char), version_value varchar2(255 char), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table application_locale (id number(19,0) not null, mp_locale_id number(19,0), enable number(1,0) not null, accessible number(1,0) not null, primary key (id));
create table approval (id number(19,0) not null, model_type number(10,0) not null, model_id number(19,0) not null, model_guid varchar2(255 char), model_data_guid varchar2(255 char), user_id number(19,0), notes varchar2(255 char), state_id number(19,0), step_name varchar2(255 char), approved number(1,0) default 0 not null, valid number(1,0) default 1 not null, due_date timestamp, updated timestamp, updated_by_id number(19,0), created timestamp not null, created_by_id number(19,0), primary key (id));
create table approval_detail (id number(19,0) not null, wf_action_id number(19,0), user_id number(19,0), approved_date timestamp, approved number(10,0) not null, created timestamp, notes varchar2(255 char), is_owner number(1,0) default 0 not null, primary key (id));
create table approval_user (wf_step_id number(19,0) not null, user_id number(19,0) not null, primary key (wf_step_id, user_id));
create table attachment (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, recipient_name_value_id number(19,0), recipient_location_value_id number(19,0), delivery_type_id number(10,0), origin_object_id number(19,0), dna varchar2(255 char) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), include_tg_relation number(10,0), excluded_tg_relation number(10,0), extended_tg_relation number(10,0), primary key (id));
create table attachment_document (attachment_id number(19,0) not null, document_id number(19,0) not null, primary key (attachment_id, document_id));
create table attachment_tg_instance_map (tg_id number(19,0) not null, attachment_id number(19,0) not null, instance_id number(19,0) not null, primary key (tg_id, attachment_id));
create table attchmnt_target_group_excluded (attachment_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (attachment_id, sequence));
create table attchmnt_target_group_extended (attachment_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (attachment_id, sequence));
create table attchmnt_target_group_included (attachment_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (attachment_id, sequence));
create table audit_event (id number(19,0) not null, timestamp timestamp, user_id number(19,0), event_type number(10,0), object_type number(10,0), object_label varchar2(255 char), object_id number(19,0), action_type number(10,0), metadata clob, primary key (id));
create table audit_report (id number(19,0) not null, request_date timestamp, user_id number(19,0), type_id number(10,0), status_id number(10,0), xml_path varchar2(255 char), report_path varchar2(255 char), guid varchar2(255 char) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), external_event_id number(19,0), primary key (id));
create table background_themes (id number(19,0) not null, name varchar2(96 char) not null, filename varchar2(255 char) not null, primary key (id));
create table barcode_type (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), sample_image varchar2(255 char), enabled number(1,0), type number(10,0), primary key (id));
create table branch (id number(19,0) not null, guid varchar2(255 char) not null, branch_code varchar2(4 char) not null, name varchar2(96 char) not null, friendly_name varchar2(255 char), dcs_schema_name varchar2(255 char), status number(10,0) not null, parent_id number(19,0), branch_type_id number(10,0), enabled number(1,0) not null, sso_type number(10,0) not null, sso_idp_id varchar2(255 char), sso_secret_key varchar2(255 char), sso_error_page_url varchar2(255 char), sso_logout_page_url varchar2(255 char), sso_autousercreation_enabled number(1,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table brand_profile (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(255 char) not null, primary_profile number(1,0) not null, apply_restricted_terms number(1,0) not null, restricted_terms_definition clob, apply_preferred_contractions number(1,0) not null, preferred_contractions_def clob, apply_restricted_contractions number(1,0) not null, restricted_contractions_def clob, apply_max_sentence_length number(1,0) not null, max_sentence_length number(10,0), apply_single_bullet_detection number(1,0) not null, apply_single_number_detection number(1,0) not null, apply_compound_space_detection number(1,0) not null, apply_lead_space_detection number(1,0) not null, apply_url_format number(1,0) not null, url_format number(10,0) not null, apply_phone_number_format number(1,0) not null, phone_number_format varchar2(255 char), enforce_legal_mark number(1,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table cache_data (id number(19,0) not null, master_id number(19,0), master_guid varchar2(400 char), alternate_id number(19,0), alternate_guid varchar2(400 char), json_data clob not null, type varchar2(255 char) not null, object_id number(19,0), api_version number(19,0) not null, version number(19,0) not null, primary key (id));
create table category_group (category_group_id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), priority number(10,0) not null, primary key (category_group_id));
create table clickatell_configuration (id number(19,0) not null, created timestamp, updated timestamp, connector_id number(19,0), qualification_output_id number(19,0), customer_driver_file varchar2(255 char), execute_incloud_test number(1,0), execute_incloud_preview number(1,0), execute_incloud_proof number(1,0), execute_incloud_simulation number(1,0), pre_qualengine_script varchar2(255 char), post_qualengine_script varchar2(255 char), post_connector_script varchar2(255 char), bundle_filenames_for_images number(1,0) not null, filename_separator_for_images varchar2(255 char), play_messages_on_empty_var number(1,0) not null, override_remote_server number(1,0), remote_server_ip varchar2(255 char), remote_server_port varchar2(255 char), remote_server_user varchar2(255 char), remote_server_password varchar2(255 char), applied_de_version varchar2(255 char), input_character_encoding number(10,0), output_character_encoding number(10,0), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), output_filename_cplex_val_id number(19,0), output_doc_title_cplex_val_id number(19,0), validate_prod_bundle number(1,0), escape_driver_data_tags number(1,0) not null, convert_table_border_px_to_pt number(1,0) not null, updated_by_id number(19,0), customer_phone_number_var_id number(19,0), primary key (id));
create table clipboard_content (id number(19,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), metatags varchar2(512 char), is_shared number(1,0) not null, owner_id number(19,0), workgroup_id number(19,0), content_id number(19,0), primary key (id));
create table columnar_indicators (data_source_id number(19,0) not null, list_order number(10,0) not null, indicator_start number(10,0) not null, indicator_length number(10,0) not null, primary key (data_source_id, list_order));
create table comm_ext_proof_validation_map (communication_id number(19,0) not null, communication_proof_id number(19,0) not null, ext_proof_validation_id number(19,0) not null, primary key (communication_id, communication_proof_id));
create table comm_prod_event_communications (comm_prod_event_id number(19,0) not null, communication_id number(19,0) not null, primary key (comm_prod_event_id, communication_id));
create table comm_zone_content_association (id number(19,0) not null, communication_id number(19,0), zone_id number(19,0), content_id number(19,0), ref_image_library_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), guid varchar2(255 char) not null, sha256_hash varchar2(90 char), primary key (id));
create table communication (id number(19,0) not null, guid varchar2(255 char) not null, customerIdentifier varchar2(4000 char), messagepoint_locale_id number(19,0), metatags varchar2(255 char), document_id number(19,0) not null, channel_context_id number(19,0), tp_selection_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), assignee_id number(19,0), wf_action_id number(19,0), last_production_date timestamp, polling_prod_status_result number(1,0) not null, prod_status_results varchar2(4000 char), number_of_copies number(10,0), test_order number(1,0) not null, debug_order number(1,0) not null, debug_reference_data clob, audit_data clob, primary key (id));
create table communication_mini_prod_event (id number(19,0) not null, name varchar2(96 char), guid varchar2(255 char) not null, enabled number(1,0), document_id number(19,0), tp_collection_id number(19,0), delivery_event_type_id number(10,0), delivery_event_schedule_id number(19,0), last_scheduler_rundate timestamp, output_filename varchar2(255 char), output_path varchar2(255 char), updated timestamp, created timestamp, completed_date timestamp, request_date timestamp, updated_by_id number(19,0), batch_window_start_date timestamp, batch_window_end_date timestamp, generate_new_bundle number(1,0), complete number(1,0), error number(1,0), primary key (id));
create table communication_production_event (id number(19,0) not null, name varchar2(96 char), guid varchar2(255 char) not null, enabled number(1,0), document_id number(19,0), tp_collection_id number(19,0), delivery_event_type_id number(10,0), delivery_event_schedule_id number(19,0), last_scheduler_rundate timestamp, output_filename varchar2(255 char), output_path varchar2(255 char), updated timestamp, created timestamp, completed_date timestamp, request_date timestamp, updated_by_id number(19,0), batch_window_start_date timestamp, batch_window_end_date timestamp, complete number(1,0), error number(1,0), primary key (id));
create table communication_proof (id number(19,0) not null, communication_id number(19,0), no_production_content_error number(1,0), no_matching_recipient_error number(1,0), pre_proof_document_id number(19,0), pre_proof_customer_identifier varchar2(4000 char), pre_proof_reference_data clob, proof_email_recipient varchar2(255 char), is_pre_proof number(1,0), applies_web_service number(1,0) not null, web_service_result_complete number(1,0) not null, output_filename varchar2(255 char), output_path varchar2(255 char), updated timestamp, created timestamp, completed_date timestamp, request_date timestamp, updated_by_id number(19,0), user_id number(19,0), complete number(1,0), error number(1,0), channel_context_id number(19,0), primary key (id));
create table complex_value (id number(19,0) not null, guid varchar2(255 char) not null, value clob, origin_object_id number(19,0), checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table complex_value_content_object (complex_value_id number(19,0) not null, content_object_id number(19,0) not null, primary key (complex_value_id, content_object_id));
create table complex_value_data_elements (complex_value_id number(19,0) not null, data_element_id number(19,0) not null, primary key (complex_value_id, data_element_id));
create table complex_value_lookup_tables (complex_value_id number(19,0) not null, lookup_table_id number(19,0) not null, primary key (complex_value_id, lookup_table_id));
create table complex_value_variable (complex_value_id number(19,0) not null, variable_id number(19,0) not null, primary key (complex_value_id, variable_id));
create table composition_additional_files (composition_file_set_id number(19,0) not null, database_file_id number(19,0) not null, primary key (composition_file_set_id, database_file_id));
create table composition_file_set (id number(19,0) not null, name varchar2(96 char), template_file_name varchar2(512 char), composition_config_file_name varchar2(512 char), created timestamp, updated timestamp, updated_by_id number(19,0), document_id number(19,0), tp_collection_id number(19,0), file_upload_sync_key varchar2(96 char), primary key (id));
create table compound_key (id number(19,0) not null, guid varchar2(255 char) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table compound_key_item (id number(19,0) not null, guid varchar2(255 char) not null, compound_key_id number(19,0), variable_id number(19,0), item_order number(10,0) not null, primary key (id));
create table condition_element (id number(19,0) not null, name varchar2(255 char), guid varchar2(255 char) not null, metatags varchar2(255 char), search_value clob, condition_type_id number(10,0), use_default_value number(1,0) not null, default_value number(1,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, sha256_hash varchar2(90 char), primary key (id));
create table condition_item (id number(19,0) not null, target_group_id number(19,0), condition_element_id number(19,0), guid varchar2(255 char) not null, primary key (id));
create table condition_item_attrib (civ_id number(19,0) not null, attribute_name varchar2(255 char) not null, attribute_value varchar2(4000 char), primary key (civ_id, attribute_name));
create table condition_item_value (id number(19,0) not null, condition_item_id number(19,0), condition_subelement_id number(19,0), primary key (id));
create table condition_operator (id number(10,0) not null, operator varchar2(255 char) not null, primary key (id));
create table condition_param_map (tgi_id number(19,0) not null, condition_item_id number(19,0) not null, param_value number(1,0), primary key (tgi_id, condition_item_id));
create table condition_sub_attrib (cs_id number(19,0) not null, attribute_name varchar2(255 char) not null, attribute_value varchar2(4000 char), primary key (cs_id, attribute_name));
create table condition_subelement (id number(19,0) not null, parameterized number(1,0), condition_element_id number(19,0), condition_type_id number(10,0), name varchar2(96 char), guid varchar2(255 char) not null, data_element_variable_id number(19,0), condition_operator_id number(10,0), data_element_comparison_id number(19,0), data_file_path varchar2(512 char), sequence number(10,0), primary key (id));
create table condition_type (id number(10,0) not null, name varchar2(96 char), parameterized number(1,0) default 1, description varchar2(255 char), primary key (id));
create table configurable_link (id number(19,0) not null, label varchar2(20 char) not null, link varchar2(400 char) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table connected_tp_sel_visible_user (tp_selection_id number(19,0) not null, user_id number(19,0) not null, primary key (tp_selection_id, user_id));
create table connection_resource (id number(19,0) not null, guid varchar2(255 char) not null, remote_reference_df_path varchar2(255 char), data_resource_id number(19,0), reference_data_file_id number(19,0), reference_connection_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table content (id number(19,0) not null, guid varchar2(255 char) not null, sha256_hash varchar2(90 char), text_content clob, unformatted_text_content clob, image_location varchar2(512 char), image_name varchar2(255 char), image_uploaded timestamp, applied_image_filename varchar2(40 char), asset_id varchar2(255 char), asset_url varchar2(2048 char), asset_site varchar2(255 char), asset_last_update timestamp, asset_last_sync timestamp, image_link number(19,0), image_alt_text number(19,0), image_ext_link number(19,0), image_ext_path number(19,0), dirty number(1,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table content_content_object_type (content_id number(19,0) not null, content_object_id number(19,0) not null, object_type number(10,0), primary key (content_id, content_object_id));
create table content_content_targeting (content_id number(19,0) not null, content_targeting_id number(19,0) not null, primary key (content_id, content_targeting_id));
create table content_images (content_id number(19,0) not null, database_file_id number(19,0) not null, primary key (content_id, database_file_id));
create table content_list_style (content_id number(19,0) not null, list_style_id number(19,0) not null, primary key (content_id, list_style_id));
create table content_object (id number(19,0) not null, guid varchar2(255 char) not null, sha256_hash varchar2(90 char), name varchar2(256 char) not null, description varchar2(255 char), object_type number(10,0) not null, content_type_id number(19,0), graphic_type_id number(10,0), channel_context_id number(19,0), usage_type_id number(10,0), advanced number(1,0), zone_id number(19,0), document_id number(19,0), tp_selection_id number(19,0), last_lock_time timestamp, removed number(1,0), suppressed number(1,0), on_hold number(1,0), structured_content_enabled number(1,0), variable_content_enabled number(1,0), origin_object_id number(19,0), locked_for_user_id number(19,0), updated timestamp, updated_by_user_id number(19,0), created timestamp, created_by_user_id number(19,0), global_parent_object_id number(19,0), last_global_po_sync_date timestamp, dna varchar2(255 char) not null, wf_action_id number(19,0), ready_for_approval number(1,0), state_id number(19,0), primary key (id));
create table content_object_association (id number(19,0) not null, type_id number(10,0), content_object_id number(19,0), data_type number(10,0), co_pg_tn_id number(19,0), ref_co_pg_tn_id number(19,0), tp_pg_tn_id number(19,0), ref_tp_pg_tn_id number(19,0), zone_part_id number(19,0), messagepoint_locale_id number(19,0), ref_messagepoint_locale_id number(19,0), content_id number(19,0), ref_image_library_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), guid varchar2(255 char) not null, sha256_hash varchar2(90 char), primary key (id));
create table content_object_comment (id number(19,0) not null, user_id number(19,0), comment_data varchar2(1000 char), created timestamp, content_object_id number(19,0), data_type number(10,0), primary key (id));
create table content_object_data (content_object_id number(19,0) not null, data_type number(10,0) not null, guid varchar2(255 char) not null, sha256_hash varchar2(90 char), dna varchar2(255 char) not null, updated timestamp, updated_by_user_id number(19,0), created timestamp, created_by_user_id number(19,0), parameter_group_id number(19,0), start_date timestamp, end_date timestamp, repeat_dates_annually number(1,0), delivery_type number(10,0), flow_type_id number(10,0) not null, data_group_id number(19,0), metatags varchar2(255 char), status_id number(19,0) not null, comp_var_format_type number(10,0), insert_as_block_content number(1,0) not null, content_trim_type number(10,0), segmentation_data varchar2(4000 char), canvas_max_width number(10,0), canvas_max_height number(10,0), canvas_trim_width number(10,0), canvas_trim_height number(10,0), supports_tables number(1,0) not null, supports_forms number(1,0) not null, supports_barcodes number(1,0) not null, supports_content_menus number(1,0) not null, keep_content_together number(1,0) not null, render_as_tagged_text number(1,0) not null, metadata_form_id number(19,0), creation_user_note varchar2(255 char), creation_reason_id number(19,0), include_tg_relation number(10,0), excluded_tg_relation number(10,0), extended_tg_relation number(10,0), language_content_hash_id number(19,0), attributes_hash varchar2(90 char), primary key (content_object_id, data_type));
create table content_object_document_map (content_object_id number(19,0) not null, document_id number(19,0) not null, primary key (content_object_id, document_id));
create table content_object_tg_excluded_map (content_object_id number(19,0) not null, data_type number(10,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (content_object_id, data_type, sequence));
create table content_object_tg_extended_map (content_object_id number(19,0) not null, data_type number(10,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (content_object_id, data_type, sequence));
create table content_object_tg_included_map (content_object_id number(19,0) not null, data_type number(10,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (content_object_id, data_type, sequence));
create table content_object_tg_instance_map (tg_id number(19,0) not null, content_object_id number(19,0) not null, data_type number(10,0) not null, instance_id number(19,0) not null, primary key (tg_id, content_object_id, data_type));
create table content_object_tpc_map (content_object_id number(19,0) not null, tp_collection_id number(19,0) not null, primary key (content_object_id, tp_collection_id));
create table content_object_zone_priority (id number(19,0) not null, update_time timestamp not null, guid varchar2(255 char) not null, content_object_id number(19,0), data_type number(10,0), zone_id number(19,0), touchpoint_selection_id number(19,0), content_object_priority number(19,0), is_suppress number(1,0) not null, is_repeat_with_next number(1,0) not null, create_time timestamp, created_by number(19,0), updated_by number(19,0), primary key (id));
create table content_paragraph_style (content_id number(19,0) not null, paragraph_style_id number(19,0) not null, primary key (content_id, paragraph_style_id));
create table content_placeholders (content_id number(19,0) not null, placeholder_id number(19,0) not null, primary key (content_id, placeholder_id));
create table content_target_group_excluded (content_targeting_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (content_targeting_id, sequence));
create table content_target_group_extended (content_targeting_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (content_targeting_id, sequence));
create table content_target_group_included (content_targeting_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (content_targeting_id, sequence));
create table content_targeting (id number(19,0) not null, guid varchar2(255 char) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), include_tg_relation number(10,0), excluded_tg_relation number(10,0), extended_tg_relation number(10,0), primary key (id));
create table content_text_style (content_id number(19,0) not null, style_id number(19,0) not null, primary key (content_id, style_id));
create table content_tg_instance_map (tg_id number(19,0) not null, content_targeting_id number(19,0) not null, instance_id number(19,0) not null, primary key (tg_id, content_targeting_id));
create table content_type (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table content_variable (content_id number(19,0) not null, variable_id number(19,0) not null, primary key (content_id, variable_id));
create table daily_frequency_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table dashboard_filters (id number(19,0) not null, keyword varchar2(255 char) not null, applies_to varchar2(255 char), object_id number(19,0), section varchar2(255 char), type varchar2(255 char), operation varchar2(255 char), value varchar2(255 char), primary key (id));
create table data_comparison (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), scheme varchar2(255 char), primary key (id));
create table data_element_core (id number(19,0) not null, data_element_type varchar2(8 char) not null, name varchar2(255 char), variable_external_id number(19,0), external_format_text varchar2(255 char), decimal_places number(10,0), data_type_id number(10,0), data_subtype_id number(10,0), anonymized number(1,0) not null, anonymization_type_id number(10,0), anonymization_custom_masks clob, anonymization_min_length number(10,0), anonymization_max_length number(10,0), updated timestamp, created timestamp, updated_by_id number(19,0), created_by_id number(19,0), xml_data_tag_definition_id number(19,0), data_record_id number(19,0), dna varchar2(255 char) not null, primary key (id));
create table data_element_flat (data_element_id number(19,0) not null, start_location number(19,0), length number(19,0), primary key (data_element_id));
create table data_element_variable (id number(19,0) not null, name varchar2(255 char), guid varchar2(255 char) not null, dna varchar2(255 char) not null, updated timestamp, created timestamp, friendly_name varchar2(255 char), enabled_for_content number(1,0), enabled_for_rules number(1,0), enabled_for_connected number(1,0), parameter_id number(19,0), expression number(19,0), script number(19,0), sql_expression varchar2(4000 char), type_id number(10,0) not null, system_variable_type_id number(10,0), updated_by_id number(19,0), default_value varchar2(255 char), sample_value varchar2(4000 char), data_element_id number(19,0), external_id varchar2(255 char), is_reference_variable number(1,0) not null, non_driver_agg_op_id number(10,0), non_driver_data_group_id number(19,0), sha256_hash varchar2(90 char), primary key (id));
create table data_element_xml (data_element_id number(19,0) not null, is_attribute number(1,0), attribute_name varchar2(256 char), primary key (data_element_id));
create table data_file (id number(19,0) not null, name varchar2(255 char), filename varchar2(255 char), source_type_id number(10,0) default 1 not null, created timestamp, updated timestamp, updated_by_id number(19,0), remote_df_path varchar2(255 char), primary key (id));
create table data_file_document (document_id number(19,0) not null, data_file_id number(19,0) not null, primary key (data_file_id, document_id));
create table data_file_preview_languages (id number(19,0) not null, customer_number varchar2(255 char) not null, language varchar2(3 char) not null, data_file_id number(19,0), primary key (id));
create table data_file_tpc (data_file_id number(19,0) not null, tp_collection_id number(19,0) not null, primary key (data_file_id, tp_collection_id));
create table data_group (id number(19,0) not null, name varchar2(255 char) not null, data_source_id number(19,0), data_record_level_id number(10,0), parent_data_group_id number(19,0), start_data_record_id number(19,0), break_data_record_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table data_group_record (data_record_id number(19,0) not null, data_group_id number(19,0) not null, primary key (data_group_id, data_record_id));
create table data_record (id number(19,0) not null, data_source_id number(19,0), record_indicator varchar2(512 char), record_position number(10,0), start_customer number(1,0), break_indicator varchar2(32 char), repeating number(1,0), enabled number(1,0), updated timestamp, dna varchar2(255 char) not null, primary key (id));
create table data_record_level (id number(10,0) not null, name varchar2(96 char), f_value number(10,0), primary key (id));
create table data_resource (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, document_id number(19,0), primary_data_file_id number(19,0), remote_primary_df_path varchar2(255 char), origin_object_id number(19,0), dna varchar2(255 char) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), tp_collection_id number(19,0), primary key (id));
create table data_source (id number(19,0) not null, guid varchar2(255 char) not null, external_id varchar2(255 char), customer_driver_file number(1,0), delimeter varchar2(255 char), name varchar2(255 char), headers number(10,0), enabled number(1,0), updated timestamp, encoding_type_id number(10,0), layout_type_id number(10,0), source_type_id number(10,0), record_type_id number(10,0), dna varchar2(255 char) not null, sha256_hash varchar2(90 char), primary key (id));
create table data_source_association (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, primary_data_source_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), customer_data_element_id number(19,0), is_one_of_separator varchar2(3 char), fiscal_year_start_month number(10,0), from_import_crv_a_id number(19,0), from_import_crv_b_id number(19,0), dna varchar2(255 char) not null, sha256_hash varchar2(90 char), primary key (id));
create table data_subtype (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), selectable number(1,0), primary key (id));
create table data_subtype_comparison (data_subtype_id number(10,0) not null, data_comparison_id number(19,0) not null, primary key (data_subtype_id, data_comparison_id));
create table data_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), selectable number(1,0), primary key (id));
create table data_type_subtype (data_type_id number(10,0) not null, data_subtype_id number(10,0) not null, primary key (data_type_id, data_subtype_id));
create table database_file (id number(19,0) not null, type number(10,0), file_name varchar2(255 char), content_type varchar2(255 char), created timestamp, file_content blob not null, metadata varchar2(4000 char), sha256_hash varchar2(90 char), primary key (id));
create table date_data_value (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table de_state_transition (id number(19,0) not null, delivery_event_id number(19,0), system_state_id number(19,0), error number(10,0), created timestamp, updated timestamp, error_message varchar2(4000 char), primary key (id));
create table deactivate_types (id number(19,0) not null, name varchar2(96 char), primary key (id));
create table delimited_indicators (data_source_id number(19,0) not null, list_order number(10,0) not null, indicator_location number(10,0) not null, primary key (data_source_id, list_order));
create table delivery_event (id number(19,0) not null, event_type_id number(10,0), updated timestamp, request_date timestamp, scheduled_time timestamp, created timestamp, bundling timestamp, preprocessing timestamp, sending timestamp, waiting_for_result timestamp, receiving_result timestamp, processing_result timestamp, processing_report timestamp, post_processing timestamp, completed timestamp, waiting_in_queue_time number(19,0), bundling_time number(19,0), preprocessing_time number(19,0), sending_time number(19,0), waiting_for_result_time number(19,0), receiving_result_time number(19,0), processing_result_time number(19,0), processing_report_time number(19,0), post_processing_time number(19,0), completed_time number(19,0), dews_waiting_in_queue_time number(19,0), dews_processing_time number(19,0), dews_qualification_engine_time number(19,0), dews_connector_time number(19,0), dews_qepost_time number(19,0), dews_completed_time number(19,0), save_xml number(1,0), log_file_path varchar2(255 char), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), user_id number(19,0), updated_by_id number(19,0), job_id number(19,0), state_bit_mask number(19,0), reference_id number(19,0), primary key (id));
create table delivery_event_schedule (id number(19,0) not null, start_date timestamp, end_date timestamp, repeating number(1,0), frequency_type_id number(10,0), daily_frequency_type_id number(10,0), primary key (id));
create table delivery_event_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table deserver (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(255 char) not null, description varchar2(255 char), url varchar2(4000 char), filename_pattern varchar2(4000 char), notification_emails varchar2(4000 char), is_default number(1,0) not null, post_process_script varchar2(4000 char), notify_error_only number(1,0) not null, communication_type number(10,0) not null, private_sshkey_file_id number(19,0), public_sshkey_file_id number(19,0), bundle_state_id number(19,0) not null, availability number(10,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table deserver_bundle_type_state (id number(19,0) not null, default_preview number(1,0), default_test number(1,0), default_simulation number(1,0), default_production number(1,0), default_import number(1,0), default_proof number(1,0), default_comm_proof number(1,0), default_comm_production number(1,0), default_test_suite number(1,0), default_segmentation number(1,0), enable_preview number(1,0), enable_test number(1,0), enable_simulation number(1,0), enable_production number(1,0), enable_import number(1,0), enable_proof number(1,0), enable_comm_proof number(1,0), enable_comm_production number(1,0), enable_test_suite number(1,0), enable_segmentation number(1,0), primary key (id));
create table deserver_communicationtype (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table dev_de_map (dev_id number(19,0) not null, data_source_id number(19,0) not null, de_id number(19,0) not null, primary key (dev_id, data_source_id));
create table dev_ds_hash_map (dev_id number(19,0) not null, data_source_id number(19,0) not null, sha256_hash varchar2(90 char), primary key (dev_id, data_source_id));
create table dialogue_configuration (id number(19,0) not null, created timestamp, updated timestamp, connector_id number(19,0), qualification_output_id number(19,0), customer_driver_file varchar2(255 char), execute_incloud_test number(1,0), execute_incloud_preview number(1,0), execute_incloud_proof number(1,0), execute_incloud_simulation number(1,0), pre_qualengine_script varchar2(255 char), post_qualengine_script varchar2(255 char), post_connector_script varchar2(255 char), bundle_filenames_for_images number(1,0) not null, filename_separator_for_images varchar2(255 char), play_messages_on_empty_var number(1,0) not null, override_remote_server number(1,0), remote_server_ip varchar2(255 char), remote_server_port varchar2(255 char), remote_server_user varchar2(255 char), remote_server_password varchar2(255 char), applied_de_version varchar2(255 char), input_character_encoding number(10,0), output_character_encoding number(10,0), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), output_filename_cplex_val_id number(19,0), output_doc_title_cplex_val_id number(19,0), validate_prod_bundle number(1,0), escape_driver_data_tags number(1,0) not null, convert_table_border_px_to_pt number(1,0) not null, updated_by_id number(19,0), composition_file_set_id number(19,0), composition_version varchar2(255 char), pub_file varchar2(255 char), report varchar2(255 char), report_updated timestamp, supports_styles number(1,0), legacy_dxf_mode number(1,0) not null, mixed_dxf_tagged_text number(1,0) not null, primary key (id));
create table dialogue_data_mapping (id number(19,0) not null, dialogue_data_type varchar2(255 char), data_type_id number(10,0), primary key (id));
create table dialogue_metadata_tags (id number(19,0) not null, config_id number(19,0), tagendstag number(1,0), bold varchar2(255 char), boldend varchar2(255 char), italic varchar2(255 char), italicend varchar2(255 char), underline varchar2(255 char), underlineend varchar2(255 char), normal varchar2(255 char), bolditalic varchar2(255 char), boldunderline varchar2(255 char), italicunderline varchar2(255 char), bolditalicunderline varchar2(255 char), paragraph varchar2(255 char), softreturn varchar2(255 char), tab varchar2(255 char), nobreak varchar2(255 char), lefttag varchar2(255 char), center varchar2(255 char), righttag varchar2(255 char), variabletag varchar2(255 char), variableend varchar2(255 char), updated timestamp, updated_by_id number(19,0), created timestamp, primary key (id));
create table dictionary (id number(19,0) not null, name varchar2(96 char), dictionary_path varchar2(255 char), lang_code varchar2(10 char) not null, locale_code varchar2(10 char), enabled number(1,0), type number(10,0), created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), primary key (id));
create table document (id number(19,0) not null, guid varchar2(255 char) not null, metatags varchar2(255 char), name varchar2(96 char), external_id varchar2(255 char), description varchar2(4000 char), enabled number(1,0), removed number(1,0) not null, tp_content_changed number(1,0), is_trash_tp number(1,0), parent_document_id number(19,0), origin_document_id number(19,0), channel_parent_id number(19,0), exchange_instance_guid varchar2(255 char), exchange_touchpoint_guid varchar2(255 char), exchange_published_timestamp timestamp, exchange_updated_timestamp timestamp, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), sequence number(10,0), connector_name varchar2(96 char), default_text_style_id number(19,0), default_paragraph_style_id number(19,0), default_list_style_id number(19,0), selection_parameter_group_id number(19,0), process_using_combined_content number(1,0), insert_parameter_group_id number(19,0), first_page_weight number(10,0), other_pages_weight number(10,0), rate_schedule_collection_id number(19,0), selection_type_id number(10,0), selection_visible_by_default number(1,0), language_parameter_group_id number(19,0), data_source_association_id number(19,0), data_group_id number(19,0), fiscal_year_start_month number(10,0), customer_rpt_variable_a_id number(19,0), customer_rpt_variable_b_id number(19,0), connected_enabled number(1,0) not null, communications_dataresource_id number(19,0), communication_marker_style_id number(19,0), comm_order_entry_enabled number(1,0) not null, comm_comp_result_webserv_id number(19,0), comm_prod_status_webserv_id number(19,0), comm_notification_webserv_id number(19,0), comm_data_feed_webserv_id number(19,0), comm_prod_type_id number(10,0) not null, comm_multi_recipient_id number(19,0), comm_ext_validation_enabled number(1,0) not null, comm_applied_tag_cloud number(1,0) not null, comm_applies_copies_input number(1,0) not null, comm_display_thumbnail number(1,0) not null, comm_applies_tp_selection number(1,0) not null, comm_zone_content_transient number(1,0) not null, comm_forced_proof_driver_val varchar2(255 char), comm_resolve_variable_values number(1,0) not null, comm_suppress_nonedit_zones number(1,0) not null, comm_pdf_conversion_quality number(10,0), comm_skip_inter_when_no_zone number(1,0) not null, comm_use_beta number(1,0) not null, segmentation_enabled number(1,0) not null, seg_data_resource_id number(19,0), sftp_ip_address varchar2(255 char), sftp_folder_path varchar2(1024 char), sftp_user_id varchar2(255 char), sftp_password varchar2(255 char), sftp_ssh_key varchar2(255 char), metadata_form_id number(19,0), touchpoint_metadata_def_id number(19,0), variant_metadata_def_id number(19,0), maintain_variant_hierarchy number(1,0), use_connector_as_zone_id number(1,0) not null, dna varchar2(255 char) not null, message_wf_id number(19,0), connected_wf_id number(19,0), br_endpoint varchar2(255 char), br_username varchar2(255 char), br_token varchar2(255 char), br_target_folder varchar2(255 char), brand_profile_id number(19,0), stripo_enabled number(1,0), primary key (id));
create table document_history (id number(19,0) not null, document_id number(19,0) not null, operation_type_id number(10,0) not null, import_file_name varchar2(255 char), source_domain_name varchar2(96 char), source_instance_name varchar2(96 char), source_document_name varchar2(96 char), log_file_path_name varchar2(255 char), created timestamp, created_by_id number(19,0), primary key (id));
create table document_preview (id number(19,0) not null, content_object_id number(19,0), zone_id number(19,0), document_id number(19,0), data_resource_id number(19,0), pg_tree_node_id number(19,0), output_filename varchar2(255 char), output_path varchar2(255 char), updated timestamp, created timestamp, completed_date timestamp, request_date timestamp, updated_by_id number(19,0), user_id number(19,0), complete number(1,0), error number(1,0), channel_context_id number(19,0), workgroup_id number(19,0), messagepoint_locale_id number(19,0) not null, primary key (id));
create table document_production_event (id number(19,0) not null, name varchar2(96 char), guid varchar2(255 char) not null, enabled number(1,0), document_id number(19,0), tp_collection_id number(19,0), delivery_event_type_id number(10,0), delivery_event_schedule_id number(19,0), last_scheduler_rundate timestamp, receive_failure_notification number(1,0) not null, reset_tp_content_changed number(1,0), output_filename varchar2(255 char), output_path varchar2(255 char), updated timestamp, created timestamp, completed_date timestamp, request_date timestamp, updated_by_id number(19,0), channel_context_id number(19,0), complete number(1,0), error number(1,0), api_retrieve number(1,0) not null, update_connected_prod_bundle number(1,0) not null, composition_fileset_id number(19,0), data_resource_id number(19,0), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), primary key (id));
create table document_section (id number(19,0) not null, parent_document_section_id number(19,0), guid varchar2(255 char) not null, name varchar2(96 char), override_name number(1,0), section_order number(10,0), override_section_order number(1,0), image_location varchar2(512 char), image_name varchar2(255 char), original_image_file_id number(19,0), shrink_background_to_fit number(1,0) not null, apply_background_for_test number(1,0) not null, override_image number(1,0), document_id number(19,0), override_dimensions number(1,0), width number(10,0), height number(10,0), layout_type_id number(10,0) not null, section_type_id number(10,0) not null, override_margin number(1,0), margin_top number(10,0), margin_right number(10,0), margin_bottom number(10,0), margin_left number(10,0), override_header number(1,0), header_height number(10,0), override_footer number(1,0), footer_height number(10,0), override_region_left number(1,0), region_left_width number(10,0), override_region_right number(1,0), region_right_width number(10,0), guide_data varchar2(4000 char), override_guides number(1,0), origin_object_id number(19,0), dna varchar2(255 char) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), sha256_hash varchar2(90 char), primary key (id));
create table document_tenant_email_address (id number(19,0) not null, document_id number(19,0), tenant_email_address varchar2(255 char), primary key (id));
create table domain (id number(19,0) not null, name varchar2(96 char) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table domain_pod (id number(19,0) not null, domain number(19,0) not null, domain_guid varchar2(255 char) not null, pod number(19,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), is_pinc_enabled number(1,0) not null, dcs_guid varchar2(255 char), primary key (id));
create table emessaging_configuration (id number(19,0) not null, created timestamp, updated timestamp, connector_id number(19,0), qualification_output_id number(19,0), customer_driver_file varchar2(255 char), execute_incloud_test number(1,0), execute_incloud_preview number(1,0), execute_incloud_proof number(1,0), execute_incloud_simulation number(1,0), pre_qualengine_script varchar2(255 char), post_qualengine_script varchar2(255 char), post_connector_script varchar2(255 char), bundle_filenames_for_images number(1,0) not null, filename_separator_for_images varchar2(255 char), play_messages_on_empty_var number(1,0) not null, override_remote_server number(1,0), remote_server_ip varchar2(255 char), remote_server_port varchar2(255 char), remote_server_user varchar2(255 char), remote_server_password varchar2(255 char), applied_de_version varchar2(255 char), input_character_encoding number(10,0), output_character_encoding number(10,0), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), output_filename_cplex_val_id number(19,0), output_doc_title_cplex_val_id number(19,0), validate_prod_bundle number(1,0), escape_driver_data_tags number(1,0) not null, convert_table_border_px_to_pt number(1,0) not null, updated_by_id number(19,0), customer_email_address_var_id number(19,0), customer_phone_number_var_id number(19,0), primary key (id));
create table encoding_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table event_type (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table exact_target_last_query (id number(19,0) not null, last_bounce_query timestamp, last_sent_query timestamp, primary key (id));
create table exacttarget_configuration (id number(19,0) not null, created timestamp, updated timestamp, connector_id number(19,0), qualification_output_id number(19,0), customer_driver_file varchar2(255 char), execute_incloud_test number(1,0), execute_incloud_preview number(1,0), execute_incloud_proof number(1,0), execute_incloud_simulation number(1,0), pre_qualengine_script varchar2(255 char), post_qualengine_script varchar2(255 char), post_connector_script varchar2(255 char), bundle_filenames_for_images number(1,0) not null, filename_separator_for_images varchar2(255 char), play_messages_on_empty_var number(1,0) not null, override_remote_server number(1,0), remote_server_ip varchar2(255 char), remote_server_port varchar2(255 char), remote_server_user varchar2(255 char), remote_server_password varchar2(255 char), applied_de_version varchar2(255 char), input_character_encoding number(10,0), output_character_encoding number(10,0), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), output_filename_cplex_val_id number(19,0), output_doc_title_cplex_val_id number(19,0), validate_prod_bundle number(1,0), escape_driver_data_tags number(1,0) not null, convert_table_border_px_to_pt number(1,0) not null, updated_by_id number(19,0), customer_email_address_var_id number(19,0), customer_key_var_id number(19,0), primary key (id));
create table ext_reporting_vars_doc (document_id number(19,0) not null, data_element_variable_id number(19,0) not null, primary key (document_id, data_element_variable_id));
create table external_event (id number(19,0) not null, name varchar2(96 char), event_type number(10,0), event_key varchar2(255 char), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table external_proof_validation (id number(19,0) not null, communication_id number(19,0), communication_proof_id number(19,0), status_id number(10,0), external_validation_email varchar2(255 char), response_date timestamp, validation_feedback blob, created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), primary key (id));
create table fileroot_man_profile (id number(19,0) not null, purge_ex_prod number(1,0) not null, purge_ex_test number(1,0) not null, purge_ex_proof number(1,0) not null, purge_ex_connected number(1,0) not null, prod_ex_duration number(10,0) not null, test_ex_duration number(10,0) not null, proof_ex_duration number(10,0) not null, connected_ex_duration number(10,0) not null, primary key (id));
create table filter_condition (id number(19,0) not null, guid varchar2(255 char), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), data_element_variable_id number(19,0), data_element_comparison_id number(19,0), data_file_path varchar2(512 char), primary key (id));
create table filter_condition_value_map (filter_condition_id number(19,0) not null, map_key varchar2(255 char) not null, map_value varchar2(255 char), primary key (filter_condition_id, map_key));
create table folder_insert (folder_id number(19,0) not null, insert_id number(19,0) not null, last_visited_time timestamp, primary key (folder_id, insert_id));
create table folders (id number(19,0) not null, folder_type varchar2(15 char) not null, name varchar2(96 char) not null, description varchar2(255 char), parent_id number(19,0), hierarchy_depth number(10,0) not null, system_managed number(1,0) not null, guid varchar2(255 char) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), owner_id number(19,0), primary key (id));
create table frequency_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table ftp_configuration (id number(19,0) not null, created timestamp, updated timestamp, connector_id number(19,0), qualification_output_id number(19,0), customer_driver_file varchar2(255 char), execute_incloud_test number(1,0), execute_incloud_preview number(1,0), execute_incloud_proof number(1,0), execute_incloud_simulation number(1,0), pre_qualengine_script varchar2(255 char), post_qualengine_script varchar2(255 char), post_connector_script varchar2(255 char), bundle_filenames_for_images number(1,0) not null, filename_separator_for_images varchar2(255 char), play_messages_on_empty_var number(1,0) not null, override_remote_server number(1,0), remote_server_ip varchar2(255 char), remote_server_port varchar2(255 char), remote_server_user varchar2(255 char), remote_server_password varchar2(255 char), applied_de_version varchar2(255 char), input_character_encoding number(10,0), output_character_encoding number(10,0), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), output_filename_cplex_val_id number(19,0), output_doc_title_cplex_val_id number(19,0), validate_prod_bundle number(1,0), escape_driver_data_tags number(1,0) not null, convert_table_border_px_to_pt number(1,0) not null, server_id number(19,0), web_url varchar2(255 char), recipient_file_location varchar2(255 char), is_embedded number(1,0), updated_by_id number(19,0), recipfile_cplex_val_id number(19,0), primary key (id));
create table generic_configuration (id number(19,0) not null, created timestamp, updated timestamp, connector_id number(19,0), qualification_output_id number(19,0), customer_driver_file varchar2(255 char), execute_incloud_test number(1,0), execute_incloud_preview number(1,0), execute_incloud_proof number(1,0), execute_incloud_simulation number(1,0), pre_qualengine_script varchar2(255 char), post_qualengine_script varchar2(255 char), post_connector_script varchar2(255 char), bundle_filenames_for_images number(1,0) not null, filename_separator_for_images varchar2(255 char), play_messages_on_empty_var number(1,0) not null, override_remote_server number(1,0), remote_server_ip varchar2(255 char), remote_server_port varchar2(255 char), remote_server_user varchar2(255 char), remote_server_password varchar2(255 char), applied_de_version varchar2(255 char), input_character_encoding number(10,0), output_character_encoding number(10,0), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), output_filename_cplex_val_id number(19,0), output_doc_title_cplex_val_id number(19,0), validate_prod_bundle number(1,0), escape_driver_data_tags number(1,0) not null, convert_table_border_px_to_pt number(1,0) not null, file_type number(10,0), file_name varchar2(255 char), format_type number(10,0), xslt_file_location varchar2(255 char), pre_xslt_file varchar2(255 char), post_xslt_file varchar2(255 char), primary key (id));
create table gmc_configuration (id number(19,0) not null, created timestamp, updated timestamp, connector_id number(19,0), qualification_output_id number(19,0), customer_driver_file varchar2(255 char), execute_incloud_test number(1,0), execute_incloud_preview number(1,0), execute_incloud_proof number(1,0), execute_incloud_simulation number(1,0), pre_qualengine_script varchar2(255 char), post_qualengine_script varchar2(255 char), post_connector_script varchar2(255 char), bundle_filenames_for_images number(1,0) not null, filename_separator_for_images varchar2(255 char), play_messages_on_empty_var number(1,0) not null, override_remote_server number(1,0), remote_server_ip varchar2(255 char), remote_server_port varchar2(255 char), remote_server_user varchar2(255 char), remote_server_password varchar2(255 char), applied_de_version varchar2(255 char), input_character_encoding number(10,0), output_character_encoding number(10,0), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), output_filename_cplex_val_id number(19,0), output_doc_title_cplex_val_id number(19,0), validate_prod_bundle number(1,0), escape_driver_data_tags number(1,0) not null, convert_table_border_px_to_pt number(1,0) not null, updated_by_id number(19,0), composition_file_set_id number(19,0), composition_version varchar2(255 char), workflow_file varchar2(255 char), workflow_file_updated timestamp, control_styles number(1,0) not null, text_style_comp_type_id number(10,0), paragraph_style_comp_type_id number(10,0), list_style_comp_type_id number(10,0), restricted_quotes number(1,0) not null, primary key (id));
create table hist_content (id number(19,0) not null, guid varchar2(255 char) not null, sha256_hash varchar2(90 char), text_content clob, unformatted_text_content clob, image_location varchar2(512 char), image_name varchar2(255 char), image_uploaded timestamp, applied_image_filename varchar2(40 char), asset_id varchar2(255 char), asset_url varchar2(2048 char), asset_site varchar2(255 char), asset_last_update timestamp, asset_last_sync timestamp, image_link number(19,0), image_alt_text number(19,0), image_ext_link number(19,0), image_ext_path number(19,0), action_type_id number(10,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table hist_content_content_object_t (content_id number(19,0) not null, content_object_id number(19,0) not null, object_type number(10,0), primary key (content_id, content_object_id));
create table hist_content_content_targeting (content_id number(19,0) not null, content_targeting_id number(19,0) not null, primary key (content_id, content_targeting_id));
create table hist_content_images (content_id number(19,0) not null, database_file_id number(19,0) not null, primary key (content_id, database_file_id));
create table hist_content_list_style (content_id number(19,0) not null, list_style_id number(19,0) not null, primary key (content_id, list_style_id));
create table hist_content_obj_association (id number(19,0) not null, guid varchar2(255 char) not null, type_id number(10,0), content_object_id number(19,0), data_type number(10,0), content_object_data_guid varchar2(255 char), co_pg_tn_id number(19,0), ref_co_pg_tn_id number(19,0), tp_pg_tn_id number(19,0), ref_tp_pg_tn_id number(19,0), zone_part_id number(19,0), messagepoint_locale_id number(19,0), ref_messagepoint_locale_id number(19,0), content_id number(19,0), ref_image_library_id number(19,0), action_type_id number(10,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table hist_content_object_data (id number(19,0) not null, content_object_id number(19,0), data_type number(10,0), content_object_data_guid varchar2(255 char), archived timestamp, archived_by_user_id number(19,0), activated timestamp, activated_by_user_id number(19,0), created timestamp, created_by_user_id number(19,0), primary key (id));
create table hist_content_paragraph_style (content_id number(19,0) not null, paragraph_style_id number(19,0) not null, primary key (content_id, paragraph_style_id));
create table hist_content_placeholders (content_id number(19,0) not null, placeholder_id number(19,0) not null, primary key (content_id, placeholder_id));
create table hist_content_text_style (content_id number(19,0) not null, style_id number(19,0) not null, primary key (content_id, style_id));
create table hist_content_variable (content_id number(19,0) not null, variable_id number(19,0) not null, primary key (content_id, variable_id));
create table hist_meta_form_item_value_set (metadata_form_item_id number(19,0) not null, string_value varchar2(4000 char) not null, primary key (metadata_form_item_id, string_value));
create table hist_metadata_form (id number(19,0) not null, guid varchar2(255 char) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), form_definition_id number(19,0), primary key (id));
create table hist_metadata_form_item (id number(19,0) not null, guid varchar2(255 char) not null, value varchar2(4000 char), aux_value_data varchar2(4000 char), upload_file_id number(19,0), form_item_definition_id number(19,0), metadata_form_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table hist_rat_shared_content (id number(19,0) not null, name varchar2(255 char), metatags varchar2(255 char), text_content clob, markup_content clob, rat_shared_content_id number(19,0), shared_last_action varchar2(255 char), shared_last_action_by varchar2(255 char), created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), primary key (id));
create table hist_rationalizer_content (id number(19,0) not null, name varchar2(255 char), metatags varchar2(255 char), text_content clob, markup_content clob, rationalizer_content_id number(19,0), rationalizer_shared_content_id number(19,0), content_last_action varchar2(255 char), content_last_action_by varchar2(255 char), created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), primary key (id));
create table history_task (id number(19,0) not null, original_id number(19,0), task_type_id number(19,0), name varchar2(96 char), action_required varchar2(255 char), description varchar2(1000 char), updated timestamp, due_date timestamp, updated_by_id number(19,0), created timestamp, primary key (id));
create table history_task_user (task_id number(19,0) not null, user_id number(19,0) not null, primary key (task_id, user_id));
create table insert_document (insert_id number(19,0) not null, document_id number(19,0) not null, primary key (insert_id, document_id));
create table insert_obj (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, description varchar2(255 char), delivery_type_id number(10,0), stock_id varchar2(96 char), weight number(10,0), front_content_path varchar2(512 char), back_content_path varchar2(512 char), locked_for_id number(19,0), last_editor_id number(19,0), status_id number(19,0) not null, start_date timestamp, end_date timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), include_tg_relation number(10,0), excluded_tg_relation number(10,0), extended_tg_relation number(10,0), primary key (id));
create table insert_rate_schedule (insert_schedule_id number(19,0) not null, number_of_sheets number(19,0) not null, rate_schedule_collection_id number(19,0) not null, primary key (insert_schedule_id, number_of_sheets));
create table insert_schedule (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, schedule_id varchar2(96 char), keywords varchar2(255 char), number_of_bins number(10,0) not null, locked_for_id number(19,0), last_editor_id number(19,0), status_id number(19,0) not null, default_schedule number(1,0) not null, start_date timestamp not null, end_date timestamp, pgi_collection_id number(19,0), schedule_collection_id number(19,0), next_id number(19,0), previous_id number(19,0), description varchar2(255 char), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table insert_schedule_bin_assignment (id number(19,0) not null, guid varchar2(255 char) not null, bin_no number(10,0) not null, priority number(10,0), available number(1,0) not null, start_date timestamp, end_date timestamp, insert_schedule_id number(19,0), insert_id number(19,0), primary key (id));
create table insert_schedule_collection (id number(19,0) not null, guid varchar2(255 char) not null, document_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table insert_target_group_excluded (insert_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (insert_id, sequence));
create table insert_target_group_extended (insert_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (insert_id, sequence));
create table insert_target_group_included (insert_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (insert_id, sequence));
create table insert_tg_instance_map (tg_id number(19,0) not null, insert_id number(19,0) not null, instance_id number(19,0) not null, primary key (tg_id, insert_id));
create table item_type (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table job (id number(19,0) not null, event_type_id number(10,0), message_delivery_filename varchar2(255 char), host_ip_address varchar2(255 char), host_name varchar2(255 char), updated timestamp, created timestamp, updated_by_id number(19,0), system_state_id number(19,0), primary key (id));
create table language_content_hash (id number(19,0) not null, primary key (id));
create table language_content_hash_map (language_content_hash_id number(19,0) not null, messagepoint_locale_id number(19,0) not null, sha256_hash varchar2(255 char) not null, primary key (language_content_hash_id, messagepoint_locale_id));
create table language_selection (id number(19,0) not null, guid varchar2(255 char) not null, messagepoint_locale_id number(19,0) not null, document_id number(19,0), pg_tree_node_id number(19,0), origin_object_id number(19,0), dna varchar2(255 char) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table layout_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table licence (id number(19,0) not null, description varchar2(255 char), startDate timestamp not null, stopDate timestamp not null, creationDate timestamp not null, guid varchar2(255 char) not null, type varchar2(255 char) not null, xml long, license_manager_enabled number(1,0) not null, license_manager_hardware_key_a varchar2(4000 char), license_manager_hardware_key_b varchar2(4000 char), license_manager_hardware_key_c varchar2(4000 char), license_manager_hardware_key_d varchar2(4000 char), license_manager_hardware_key_e varchar2(4000 char), license_manager_hardware_key_f varchar2(4000 char), number_of_full_users number(10,0), number_of_restricted_users number(10,0), print_channels_authorized number(1,0), web_channels_authorized number(1,0), email_channels_authorized number(1,0), sms_channels_authorized number(1,0), hp_dialogue_authorized number(1,0), hp_dialogue_dxf_authorized number(1,0), sefas_authorized number(1,0), gmc_printnet_authorized number(1,0), send_mail_authorized number(1,0), exact_target_authorized number(1,0), clickatell_authorized number(1,0), ftp_web_authorized number(1,0), native_print_authorized number(1,0), emsg_email_authorized number(1,0), emsg_sms_authorized number(1,0), applicationId number(19,0), customerId number(19,0) not null, primary key (id));
create table licence_application (id number(19,0) not null, name varchar2(255 char) not null, majorVersion varchar2(255 char) not null, minorVersion varchar2(255 char) not null, uuid varchar2(255 char), description varchar2(255 char) not null, ownerName varchar2(255 char) not null, primary key (id));
create table licence_attributes (licence_id number(19,0) not null, attribute_name varchar2(255 char) not null, attribute_value varchar2(255 char), primary key (licence_id, attribute_name));
create table licence_audit (id number(19,0) not null, timestamp timestamp not null, extraInfo long, licenceId number(19,0) not null, auditActionId number(19,0) not null, primary key (id));
create table licence_audit_action (id number(19,0) not null, name varchar2(255 char) not null, description varchar2(255 char) not null, primary key (id));
create table licence_change_reasons (id number(19,0) not null, type varchar2(255 char) not null, description varchar2(255 char) not null, primary key (id));
create table licence_customer (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(255 char) not null, emailAddress varchar2(255 char) not null, keypairId number(19,0) not null, representativeId number(19,0), primary key (id));
create table licence_customer_keypair (id number(19,0) not null, publicKey blob not null, privateKey blob not null, primary key (id));
create table licence_history (id number(19,0) not null, licenceId number(19,0) not null, description varchar2(255 char), startDate timestamp not null, stopDate timestamp not null, creationDate timestamp not null, insertDate timestamp not null, guid varchar2(255 char), type varchar2(255 char) not null, xml long, number_of_full_users number(10,0), number_of_restricted_users number(10,0), print_channels_authorized number(1,0), web_channels_authorized number(1,0), email_channels_authorized number(1,0), sms_channels_authorized number(1,0), hp_dialogue_authorized number(1,0), hp_dialogue_dxf_authorized number(1,0), sefas_authorized number(1,0), gmc_printnet_authorized number(1,0), send_mail_authorized number(1,0), exact_target_authorized number(1,0), clickatell_authorized number(1,0), ftp_web_authorized number(1,0), native_print_authorized number(1,0), emsg_email_authorized number(1,0), emsg_sms_authorized number(1,0), customerInfo varchar2(1028 char), applicationInfo varchar2(1028 char), serverInfo varchar2(1028 char), primary key (id));
create table licence_history_reason (history_id number(19,0) not null, reason_id number(19,0) not null, primary key (history_id, reason_id));
create table licence_module (id number(19,0) not null, name varchar2(255 char) not null, description varchar2(255 char), guid varchar2(255 char) not null, applicationId number(19,0) not null, primary key (id));
create table licence_representative (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(255 char) not null, emailAddress varchar2(255 char) not null, phoneNumber varchar2(255 char), primary key (id));
create table licence_resource (id number(19,0) not null, name varchar2(255 char) not null, licenceId number(19,0), hash long raw, sortOrder number(10,0), primary key (id));
create table licence_server (id number(19,0) not null, name varchar2(255 char) not null, licenceId number(19,0), primary key (id));
create table licence_server_ip_address (serverId number(19,0) not null, ipAddress varchar2(255 char));
create table list_style (id number(19,0) not null, identifier varchar2(96 char), name varchar2(255 char), connector_name varchar2(255 char), alignment_id number(10,0), list_spacing_before number(10,0), list_spacing_after number(10,0), list_spacing_right number(10,0), border_type_id number(10,0), border_width number(10,0), line_spacing number(10,0) not null, line_spacing_type_id number(10,0), text_style_id number(19,0), tagging_override varchar2(2048 char), bullet_left_margin number(10,0), bullet_right_margin number(10,0), bullet_top_margin number(10,0), bullet_bottom_margin number(10,0), bullet_symbol_overrides varchar2(255 char), indent number(10,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table list_style_cust (id number(19,0) not null, connector_name varchar2(255 char), alignment_id number(10,0), list_spacing_before number(10,0), list_spacing_after number(10,0), list_spacing_right number(10,0), border_type_id number(10,0), border_width number(10,0), line_spacing number(10,0) not null, line_spacing_type_id number(10,0), text_style_id number(19,0), tagging_override varchar2(2048 char), bullet_left_margin number(10,0), bullet_right_margin number(10,0), bullet_top_margin number(10,0), bullet_bottom_margin number(10,0), bullet_symbol_overrides varchar2(255 char), indent number(10,0), master_list_style_id number(19,0), origin_object_id number(19,0), checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table list_style_cust_map (list_style_id number(19,0) not null, document_id number(19,0) not null, list_style_cust_id number(19,0) not null, primary key (list_style_id, document_id));
create table lookup_table (id number(19,0) not null, guid varchar2(255 char) not null, dna varchar2(255 char) not null, checkout_time timestamp, status_id number(19,0) not null, created timestamp, created_by_id number(19,0), updated timestamp, updated_by_id number(19,0), sha256_hash varchar2(90 char), primary key (id));
create table lookup_table_document (lookup_table_instance_id number(19,0) not null, document_id number(19,0) not null, primary key (lookup_table_instance_id, document_id));
create table lookup_table_instance (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, lookup_file_id number(19,0), delimiter varchar2(1 char), input_character_encoding number(10,0), data_source_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), wf_action_id number(19,0), state_id number(19,0), ready_for_approval number(1,0), fully_visible number(1,0) default 1 not null, cloned_from_id number(19,0), sha256_hash varchar2(90 char), primary key (id));
create table lookup_table_tpc (lookup_table_instance_id number(19,0) not null, tp_collection_id number(19,0) not null, primary key (lookup_table_instance_id, tp_collection_id));
create table lookup_table_version_map (lookup_table_id number(19,0) not null, lookup_table_instance_id number(19,0) not null, start_date timestamp, end_date timestamp, creation_user_note varchar2(255 char), creation_reason_id number(19,0), expiry_user_note varchar2(255 char), expiry_reason_id number(19,0), latest_archived number(1,0), user_id number(19,0), status_id number(19,0) not null, created timestamp, created_by number(19,0), updated timestamp, updated_by number(19,0), primary key (lookup_table_id, lookup_table_instance_id));
create table lookup_value (id number(19,0) not null, name varchar2(255 char), lookup_value varchar2(255 char), data_element_variable_id number(19,0), primary key (id));
create table message_delivery_report (id number(19,0) not null, user_id number(19,0), data_filename varchar2(255 char), output_filename varchar2(255 char), updated timestamp, updated_by_id number(19,0), created timestamp, start_date timestamp, end_date timestamp, completed_date timestamp, request_date timestamp, primary key (id));
create table message_simulation_report (id number(19,0) not null, user_id number(19,0), data_filename varchar2(255 char), output_filename varchar2(255 char), updated timestamp, created timestamp, start_date timestamp, end_date timestamp, completed_date timestamp, request_date timestamp, primary key (id));
create table messagepoint_locale (id number(19,0) not null, name varchar2(96 char) not null, display_code varchar2(96 char) not null, code varchar2(10 char) not null, favourite number(1,0), default_locale number(1,0), language_id number(19,0), language_name varchar2(96 char) not null, language_display_code varchar2(96 char) not null, language_code varchar2(10 char) not null, language_favourite number(1,0), language_group number(19,0), dictionary varchar2(255 char), date_format varchar2(255 char), currency_symbol varchar2(255 char), suffix_currency_symbol number(1,0), thousands_separator varchar2(255 char), decimal_symbol varchar2(255 char), number_of_decimals varchar2(255 char), boolean_symbol_true varchar2(255 char), boolean_symbol_false varchar2(255 char), short_month_names varchar2(255 char), long_month_names varchar2(255 char), drop_decimal_for_whole_num number(1,0) not null, primary key (id));
create table meta_form_item_def_con_ele_map (metadata_form_item_def_id number(19,0) not null, connector varchar2(255 char) not null, data_element_id number(19,0) not null, primary key (metadata_form_item_def_id, connector));
create table metadata_form (id number(19,0) not null, guid varchar2(255 char) not null, origin_document_id number(19,0), parent_form_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), form_definition_id number(19,0), sha256_hash varchar2(90 char), primary key (id));
create table metadata_form_definition (id number(19,0) not null, guid varchar2(255 char) not null, metatags varchar2(255 char), name varchar2(512 char), description varchar2(4000 char), type number(10,0), origin_document_id number(19,0), parent_form_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), web_service_config_id number(19,0), sha256_hash varchar2(90 char), primary key (id));
create table metadata_form_item (id number(19,0) not null, guid varchar2(255 char) not null, value varchar2(4000 char), aux_value_data varchar2(4000 char), upload_file_id number(19,0), form_item_definition_id number(19,0), metadata_form_id number(19,0), parent_form_id number(19,0), is_manifest_item number(1,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table metadata_form_item_definition (id number(19,0) not null, guid varchar2(255 char) not null, item_order number(10,0), name varchar2(255 char) not null, data_element_id number(19,0), metadata_form_item_type_id number(10,0), menu_value_items clob, web_service_refresh_type_id number(10,0), primary_connector varchar2(255 char), is_mandatory number(1,0) not null, description varchar2(4000 char), parent_item_order number(10,0), display_trigger_values varchar2(4000 char), auto_generate_on_import number(1,0) not null, metadata_form_definition_id number(19,0), parent_form_id number(19,0), field_size_type_id number(10,0) not null, field_max_length number(10,0), input_validation_type_id number(10,0) not null, default_input_value varchar2(255 char), default_to_today number(1,0), unique_value number(1,0), meta_form_item_origin_type_id number(10,0), meta_form_item_data_type_id number(10,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table metadata_form_item_value_set (metadata_form_item_id number(19,0) not null, string_value varchar2(4000 char) not null, primary key (metadata_form_item_id, string_value));
create table metadata_points_of_interest (id number(19,0) not null, label varchar2(4000 char), description varchar2(4000 char), comparision_operator number(10,0), comparision_value varchar2(4000 char), form_item_definition_id number(19,0), rationalizer_application_id number(19,0), primary key (id));
create table mp_channel (id number(19,0) not null, name varchar2(96 char), presentation_name varchar2(96 char), created timestamp, updated timestamp, primary key (id));
create table mp_connector (id number(19,0) not null, name varchar2(96 char), presentation_name varchar2(96 char), created timestamp, updated timestamp, channel_id number(19,0), primary key (id));
create table mp_licence (id number(19,0) not null, key varchar2(255 char), value varchar2(255 char), accessible number(10,0), primary key (id));
create table mp_qualification_output (id number(19,0) not null, name varchar2(96 char), created timestamp, updated timestamp, primary key (id));
create table msg_delivery_report_doc (message_delivery_report_id number(19,0) not null, document_id number(19,0) not null, primary key (message_delivery_report_id, document_id));
create table msg_delivery_report_msg (message_delivery_report_id number(19,0) not null, content_object_id number(19,0) not null, primary key (message_delivery_report_id, content_object_id));
create table msg_simulation_report_doc (message_simulation_report_id number(19,0) not null, document_id number(19,0) not null, primary key (message_simulation_report_id, document_id));
create table msg_simulation_report_msg (message_simulation_report_id number(19,0) not null, content_object_id number(19,0) not null, primary key (message_simulation_report_id, content_object_id));
create table native_comp_configuration (id number(19,0) not null, created timestamp, updated timestamp, connector_id number(19,0), qualification_output_id number(19,0), customer_driver_file varchar2(255 char), execute_incloud_test number(1,0), execute_incloud_preview number(1,0), execute_incloud_proof number(1,0), execute_incloud_simulation number(1,0), pre_qualengine_script varchar2(255 char), post_qualengine_script varchar2(255 char), post_connector_script varchar2(255 char), bundle_filenames_for_images number(1,0) not null, filename_separator_for_images varchar2(255 char), play_messages_on_empty_var number(1,0) not null, override_remote_server number(1,0), remote_server_ip varchar2(255 char), remote_server_port varchar2(255 char), remote_server_user varchar2(255 char), remote_server_password varchar2(255 char), applied_de_version varchar2(255 char), input_character_encoding number(10,0), output_character_encoding number(10,0), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), output_filename_cplex_val_id number(19,0), output_doc_title_cplex_val_id number(19,0), validate_prod_bundle number(1,0), escape_driver_data_tags number(1,0) not null, convert_table_border_px_to_pt number(1,0) not null, advanced_composition number(1,0), apply_fillable_forms number(1,0), output_file_type number(10,0), starts_on_odd_page number(1,0), duplex_output number(1,0), primary key (id));
create table navigation_drop_down_menu (id number(19,0) not null, name varchar2(255 char) not null, ordering number(10,0) not null, tab_id number(19,0), string_id varchar2(255 char), requires varchar2(255 char), primary key (id));
create table navigation_drop_down_menu_item (id number(19,0) not null, name varchar2(255 char), type varchar2(2 char), icon varchar2(255 char), ordering number(10,0) not null, menu_id number(19,0) not null, tree_id number(19,0), string_id varchar2(255 char), url varchar2(255 char), requires varchar2(255 char), store_in_session number(1,0), authorization_type varchar2(2 char) not null, primary key (id));
create table navigation_menu_item_perm (navigation_menu_item_id number(19,0) not null, permission_id number(19,0) not null, primary key (navigation_menu_item_id, permission_id));
create table navigation_tab_default_map (tab_id number(19,0) not null, type varchar2(255 char) not null, item_id number(19,0) not null, primary key (tab_id, type));
create table navigation_tab_permission (navigation_tab_id number(19,0) not null, permission_id number(19,0) not null, primary key (navigation_tab_id, permission_id));
create table navigation_tabs (id number(19,0) not null, ordering number(10,0) not null, name varchar2(255 char), string_id varchar2(255 char), url varchar2(255 char), default_menu_item_id number(19,0), authorization_type varchar2(2 char) not null, primary key (id));
create table navigation_tree (id number(19,0) not null, f_query varchar2(510 char), parent_id number(19,0), parameter varchar2(255 char), url varchar2(255 char), icon varchar2(255 char), type number(10,0), sequence number(10,0), primary key (id));
create table node (id number(19,0) not null, guid varchar2(255 char) not null, node_type number(10,0) not null, name varchar2(96 char) not null, friendly_name varchar2(255 char), schema_name varchar2(96 char), parent_id number(19,0), branch_id number(19,0), node_count_number number(10,0), enabled number(1,0) not null, status number(10,0) not null, is_default_node number(1,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table notification_email (id number(19,0) not null, recipient_id number(19,0), notification_action_type number(10,0), notification_object_type number(10,0), object_id number(19,0), notification_action_text clob, notification_action_link varchar2(255 char), created timestamp, created_by_id number(19,0), primary key (id));
create table notification_settings (id number(19,0) not null, user_id number(19,0), event_type number(10,0), object_type number(10,0), object_label varchar2(255 char), object_id number(19,0), action_type number(10,0), metadata clob, primary key (id));
create table notification_settings_tp (notification_id number(19,0) not null, touchpoint_id number(19,0) not null, primary key (notification_id, touchpoint_id));
create table oidc_signing_keys (id number(19,0) not null, guid varchar2(255 char) not null, branch_guid varchar2(255 char) not null, private_key blob not null, public_key blob not null, valid_from timestamp not null, valid_to timestamp not null, primary key (id));
create table old_workflow (id number(19,0) not null, name varchar2(255 char) not null, class_name varchar2(255 char) not null, primary key (id));
create table operations_report_doc (operations_report_id number(19,0) not null, document_id number(19,0) not null, primary key (operations_report_id, document_id));
create table operations_report_event (report_id number(19,0) not null, delivery_event_id number(19,0) not null, primary key (report_id, delivery_event_id));
create table operations_report_scenario (id number(19,0) not null, name varchar2(96 char), start_date timestamp, created timestamp, updated timestamp, complete number(1,0), error number(1,0), updated_by_id number(19,0), end_date timestamp, xml_path varchar2(255 char), report_path varchar2(255 char), primary key (id));
create table order_entry_def_con_ele_map (order_entry_item_def_id number(19,0) not null, connector varchar2(255 char) not null, data_element_variable_id number(19, 0) not null,  primary key (order_entry_item_def_id, connector));
create table order_entry_item (id number(19,0) not null, guid varchar2(255 char) not null, document_id number(19,0) not null, item_order number(10,0), is_primary_driver_entry number(1,0) not null, is_indicator_entry number(1,0) not null, name varchar2(255 char) not null, data_element_variable_id number(19, 0), order_entry_type_id number(10,0), data_privacy_type_id number(10,0), uploaded_file_type_id number(10,0) not null, uploaded_file_data_source_id number(19,0), menu_value_items varchar2(4000 char), web_service_refresh_type_id number(10,0), primary_connector varchar2(255 char), is_mandatory number(1,0) not null, description varchar2(4000 char), parent_item_order number(10,0), display_trigger_values varchar2(4000 char), field_size_type_id number(10,0) not null, field_max_length number(10,0), input_validation_type_id number(10,0) not null, upload_file_id number(19,0), default_input_value varchar2(255 char), default_to_today number(1,0), unique_value number(1,0), communication_id number(19,0), value varchar2(4000 char), aux_value_data varchar2(4000 char), parent_form_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table order_entry_item_con_ele_map (order_entry_item_id number(19,0) not null, connector varchar2(255 char) not null, data_element_variable_id number(19, 0) not null, primary key (order_entry_item_id, connector));
create table order_entry_item_definition (id number(19,0) not null, guid varchar2(255 char) not null, document_id number(19,0) not null, item_order number(10,0), is_primary_driver_entry number(1,0) not null, is_indicator_entry number(1,0) not null, name varchar2(255 char) not null, order_entry_type_id number(10,0), data_privacy_type_id number(10,0), uploaded_file_type_id number(10,0) not null, uploaded_file_data_source_id number(19,0), menu_value_items varchar2(4000 char), web_service_refresh_type_id number(10,0), primary_connector varchar2(255 char), is_mandatory number(1,0) not null, description varchar2(4000 char), parent_item_order number(10,0), display_trigger_values varchar2(4000 char), field_size_type_id number(10,0) not null, field_max_length number(10,0), input_validation_type_id number(10,0) not null, default_input_value varchar2(255 char), default_to_today number(1,0), unique_value number(1,0), parent_form_id number(19,0), data_element_variable_id number(19, 0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table order_item_def_selections (order_entry_item_definition_id number(19,0) not null, touchpoint_selection_id number(19,0) not null, primary key (order_entry_item_definition_id, touchpoint_selection_id));
create table order_item_selections (order_entry_item_id number(19,0) not null, touchpoint_selection_id number(19,0) not null, primary key (order_entry_item_id, touchpoint_selection_id));
create table para_style_cust (id number(19,0) not null, connector_name varchar2(255 char), allignment_id number(10,0), para_spacing_before number(10,0), para_spacing_after number(10,0), line_spacing number(10,0) not null, line_spacing_type_id number(10,0), left_margin number(10,0), right_margin number(10,0), indent number(10,0), bullet_symbol_id number(10,0), num_list_type_id number(10,0), border_type_id number(10,0), border_width number(10,0), is_bulleted_list_applicable number(1,0), tagging_override varchar2(2048 char), toggle_alignment number(1,0) not null, toggle_line_spacing number(1,0) not null, toggle_left_margin number(1,0) not null, bullet_left_margin number(10,0), bullet_right_margin number(10,0), bullet_top_margin number(10,0), bullet_bottom_margin number(10,0), text_style_id number(19,0), master_para_style_id number(19,0), origin_object_id number(19,0), checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table para_style_cust_line_spacings (para_style_cust_id number(19,0) not null, value varchar2(255 char));
create table para_style_cust_map (para_style_id number(19,0) not null, document_id number(19,0) not null, para_style_cust_id number(19,0) not null, primary key (para_style_id, document_id));
create table paragraph_style (id number(19,0) not null, identifier varchar2(96 char), name varchar2(255 char), connector_name varchar2(255 char), allignment_id number(10,0), para_spacing_before number(10,0), para_spacing_after number(10,0), line_spacing number(10,0) not null, line_spacing_type_id number(10,0), left_margin number(10,0), right_margin number(10,0), indent number(10,0), bullet_symbol_id number(10,0), num_list_type_id number(10,0), border_type_id number(10,0), border_width number(10,0), is_bulleted_list_applicable number(1,0), tagging_override varchar2(2048 char), toggle_alignment number(1,0) not null, toggle_line_spacing number(1,0) not null, toggle_left_margin number(1,0) not null, bullet_left_margin number(10,0), bullet_right_margin number(10,0), bullet_top_margin number(10,0), bullet_bottom_margin number(10,0), style_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table paragraph_style_line_spacings (paragraph_style_id number(19,0) not null, value varchar2(255 char));
create table parameter (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, description varchar2(255 char), data_element_variable_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table parameter_group (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, description varchar2(255 char), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), sha256_hash varchar2(90 char), primary key (id));
create table parameter_group_instance (id number(19,0) not null, guid varchar2(255 char) not null, pg_id number(19,0), pg_instance_collection_id number(19,0), pg_item_value_1 varchar2(255 char) not null, pg_item_value_2 varchar2(255 char), pg_item_value_3 varchar2(255 char), pg_item_value_4 varchar2(255 char), pg_item_value_5 varchar2(255 char), pg_item_value_6 varchar2(255 char), primary key (id));
create table parameter_group_item (id number(19,0) not null, guid varchar2(255 char) not null, pg_id number(19,0), parameter_id number(19,0), item_order number(10,0) not null, primary key (id));
create table password_history (id number(19,0) not null, user_id number(19,0), password varchar2(255 char), created timestamp, primary key (id));
create table password_recovery (id number(19,0) not null, name varchar2(96 char), reset_key varchar2(255 char) not null, user_id number(19,0), is_used number(1,0) default 0 not null, reset_date timestamp, reset_ip varchar2(96 char), created timestamp, created_by_id number(19,0), updated timestamp, updated_by_id number(19,0), action number(10,0) default 3 not null, primary key (id));
create table permission (id number(19,0) not null, name varchar2(255 char), description varchar2(255 char), type varchar2(2 char) not null, updated timestamp, updated_by_id number(19,0), category_id number(19,0), created timestamp, primary key (id));
create table permission_category (id number(19,0) not null, name varchar2(255 char), description varchar2(255 char), priority number(10,0) not null, category_group_id number(19,0), primary key (id));
create table pg_instance_collection (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, shared number(1,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table pg_tree_node (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, parameter_group_id number(19,0), parent_node_id number(19,0), pgi_collection_id number(19,0), document_id number(19,0), origin_object_id number(19,0), dna varchar2(255 char) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, data_type number(10,0), user_id number(19,0), last_editor_id number(19,0), status_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table pod (id number(19,0) not null, name varchar2(96 char) not null, description varchar2(255 char), url varchar2(255 char) not null, sso_idp_id varchar2(255 char), sso_secret_key varchar2(255 char), internal_umh_ip varchar2(255 char), internal_oracle_ip varchar2(255 char), pod_master_schema varchar2(96 char), pod_master_pwd varchar2(96 char), web_services_user varchar2(255 char), web_services_user_pwd varchar2(255 char), type number(10,0), status number(10,0), enabled number(1,0) not null, is_online number(1,0) not null, scan_date timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table pod_status (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table pod_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table project (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(255 char), description varchar2(255 char), status_id number(10,0), notice_status_id number(10,0), metatags varchar2(255 char), start_date timestamp, due_date timestamp, metadata_form_id number(19,0), wf_instance_id number(19,0), owner_id number(19,0), created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), primary key (id));
create table project_applied_wf_steps (project_task_id number(19,0) not null, list_order number(10,0) not null, wf_step_id number(19,0) not null, primary key (project_task_id, list_order));
create table project_step_req_note_map (project_task_id number(19,0) not null, wf_step_id number(19,0) not null, note varchar2(255 char), primary key (project_task_id, wf_step_id));
create table project_task (id number(19,0) not null, task_id number(19,0), project_id number(19,0), created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), primary key (id));
create table project_wf_step_user_map (project_task_id number(19,0) not null, wf_step_id number(19,0) not null, user_id number(19,0), primary key (project_task_id, wf_step_id));
create table proof (id number(19,0) not null, guid varchar2(255 char) not null, is_staled number(1,0) not null, is_active number(1,0) not null, output_filename varchar2(255 char), output_path varchar2(255 char), request_date timestamp, completed_date timestamp, complete number(1,0), error number(1,0), has_data_error number(1,0), channel_context_id number(19,0), proof_definition_id number(19,0), external_id varchar2(255 char), created timestamp, created_by_id number(19,0), updated timestamp, updated_by_id number(19,0), type number(10,0) not null, content_object_id number(19,0), context_zone_id number(19,0), primary key (id));
create table proof_definition (id number(19,0) not null, guid varchar2(255 char) not null, recepient_range_from number(10,0), recepient_range_to number(10,0), language varchar2(3 char) not null, status_id number(10,0) not null, data_resource_id number(19,0), tp_selection_id number(19,0), created timestamp, created_by_id number(19,0), updated timestamp, updated_by_id number(19,0), primary key (id));
create table rat_hist_shared_content (rat_shared_content_id number(19,0) not null, hist_rat_shared_content_id number(19,0) not null, primary key (rat_shared_content_id, hist_rat_shared_content_id));
create table rat_hist_shared_content_form (rat_shared_content_id number(19,0) not null, hist_metadata_form_id number(19,0) not null, primary key (rat_shared_content_id, hist_metadata_form_id));
create table rate_schedule (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, start_date timestamp not null, end_date timestamp, description varchar2(255 char), rate_schedule_collection_id number(19,0), next_id number(19,0), previous_id number(19,0), first_page_weight number(10,0), other_pages_weight number(10,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table rate_schedule_collection (id number(19,0) not null, guid varchar2(255 char) not null, envelope_name varchar2(96 char) not null, envelope_weight number(10,0) not null, weight_unit_id number(10,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table rate_schedule_detail (id number(19,0) not null, guid varchar2(255 char) not null, rate number(10,0) not null, weight number(10,0) not null, rate_schedule_id number(19,0), primary key (id));
create table rationalizer_app_nav_tree (rationalizer_app_id number(19,0) not null, list_order number(10,0) not null, form_item_def_id number(19,0) not null, primary key (rationalizer_app_id, list_order));
create table rationalizer_app_visible_user (rationalizer_app_id number(19,0) not null, user_id number(19,0) not null, primary key (rationalizer_app_id, user_id));
create table rationalizer_application (id number(19,0) not null, name varchar2(255 char), description varchar2(255 char), metatags varchar2(255 char), full_visibility number(1,0), brand_profile_id number(19,0), guid varchar2(255 char) not null, app_sync_status number(10,0) not null, combine_within_document number(1,0) not null, linked_document_id number(19,0), parsed_doc_metadata_def_id number(19,0), parsed_content_metadata_def_id number(19,0), parent_app_id number(19,0), rationalizer_wf_id number(19,0), created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), primary key (id));
create table rationalizer_document (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(255 char), file_name varchar2(1024 char), file_path varchar2(1024 char), metatags varchar2(255 char), description varchar2(255 char), parsed_document_form_id number(19,0), rationalizer_application_id number(19,0), parent_doc_id number(19,0), created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), primary key (id));
create table rationalizer_document_content (id number(19,0) not null, guid varchar2(255 char) not null, hash_code varchar2(32 char) not null, name varchar2(255 char), metatags varchar2(255 char), text_content clob, markup_content clob, zone_connector varchar2(4000 char), message_name varchar2(4000 char), content_order number(10,0), parsed_content_form_id number(19,0), rationalizer_document_id number(19,0), rationalizer_shared_content_id number(19,0), parent_content_id number(19,0), assignee_id number(19,0), wf_action_id number(19,0), status_type number(10,0), created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), primary key (id));
create table rationalizer_filter_item_defs (rationalizer_application number(19,0) not null, metadata_form_definition_id number(19,0) not null, primary key (rationalizer_application, metadata_form_definition_id));
create table rationalizer_hist_content (rationalizer_content_id number(19,0) not null, hist_rationalizer_content_id number(19,0) not null, primary key (rationalizer_content_id, hist_rationalizer_content_id));
create table rationalizer_hist_form (rationalizer_content_id number(19,0) not null, hist_metadata_form_id number(19,0) not null, primary key (rationalizer_content_id, hist_metadata_form_id));
create table rationalizer_query (id number(19,0) not null, name varchar2(255 char), metatags varchar2(255 char), description varchar2(255 char), simple_comparision_value clob, simple_search_value clob, any_of_these_search_value clob, all_of_these_search_value clob, none_of_these_search_value clob, other_settings clob, query_type_id number(10,0) not null, query_tab_id number(10,0) not null, apply_comparison number(1,0) not null, content_comparision_value clob, comparison_threshold number(10,0), comparison_threshold_max number(10,0), similarity_algorithm_type_id number(10,0), compare_to_all number(1,0) not null, compare_results_to_all number(1,0) not null, apply_grouping number(1,0) not null, trim_whitespace number(1,0) not null, exact_matches_only number(1,0) not null, case_sensitive number(1,0) not null, rationalizer_application_id number(19,0), created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), primary key (id));
create table rationalizer_query_component (id number(19,0) not null, target_data_type_id number(10,0), comparator_type_id number(10,0), metadata_connector_value varchar2(255 char), comparision_value clob, component_intra_operator_id number(10,0), chain_previous_component number(1,0) not null, document_filter_id number(10,0) not null, created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), rationalizer_query_id number(19,0), sequence number(10,0), primary key (id));
create table rationalizer_shared_content (id number(19,0) not null, name varchar2(255 char), metatags varchar2(255 char), description varchar2(255 char), guid varchar2(255 char) not null, text_content clob, markup_content clob, hash_code varchar2(32 char) not null, parsed_content_form_id number(19,0), rationalizer_application_id number(19,0), parent_index_id number(19,0), created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), primary key (id));
create table record_type (id number(10,0) not null, name varchar2(96 char), string_id varchar2(255 char), primary key (id));
create table redirection_info (id number(19,0) not null, domain number(19,0) not null, production number(19,0), transition number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table reference_connection (id number(19,0) not null, guid varchar2(255 char) not null, data_source_association_id number(19,0), reference_data_source_id number(19,0), primary_key_variable_id number(19,0), reference_key_variable_id number(19,0), primary_compound_key_id number(19,0), reference_compound_key_id number(19,0), connector_parameter varchar2(255 char), updated timestamp, created_by_id number(19,0), created timestamp, updated_by_id number(19,0), primary key (id));
create table reference_query (id number(19,0) not null, object_class_name varchar2(255 char) not null, direct_ref_class_name varchar2(255 char), f_query varchar2(4000 char), f_type varchar2(8 char), primary key (id));
create table report_scenario (id number(19,0) not null, name varchar2(96 char), start_date timestamp, created timestamp, updated timestamp, complete number(1,0), error number(1,0), channel_context_id number(19,0), updated_by_id number(19,0), end_date timestamp, primary key (id));
create table report_scenario_delivery_event (report_id number(19,0) not null, delivery_event_id number(19,0) not null, primary key (report_id, delivery_event_id));
create table report_scenario_message (report_id number(19,0) not null, content_object_id number(19,0) not null, primary key (report_id, content_object_id));
create table role (id number(19,0) not null, name varchar2(255 char), description varchar2(255 char), visibility varchar2(2 char), active number(1,0), updated timestamp, hidden_flag number(1,0), updated_by_id number(19,0), created timestamp, primary key (id));
create table role_permission (role_id number(19,0) not null, permission_id number(19,0) not null, primary key (role_id, permission_id));
create table sandbox_file (id number(19,0) not null, sandbox_type number(10,0), file_name varchar2(255 char), content_type varchar2(255 char), created timestamp, file_content blob not null, primary key (id));
create table search_result_ids (id number(19,0) not null, guid varchar2(255 char) not null, list_item_ids clob, user_id number(19,0), expiry timestamp, created timestamp, primary key (id));
create table security_settings (id number(19,0) not null, name varchar2(96 char), updated timestamp, updated_by_id number(19,0), created timestamp, min_length varchar2(96 char), max_length varchar2(96 char), requires_uppercase number(1,0), requires_lowercase number(1,0), requires_numeral number(1,0), requires_symbol number(1,0), no_repeats number(1,0), track_flag number(1,0), max_attempts varchar2(96 char), alphanumeric_only number(1,0), user_id_min_length varchar2(96 char), user_id_max_length varchar2(96 char), pw_reset_keep_alive varchar2(96 char), pw_expires number(1,0), pw_expire_days number(10,0), prevent_repeated_pw number(1,0), pw_history_entries number(10,0), pw_limit_reuse_period number(1,0) not null, pw_limit_months number(10,0) not null, session_expire_mins varchar2(96 char), soft_deactivation_enabled number(1,0) not null, soft_deactivation_limit_days number(10,0) not null, hard_deactivation_enabled number(1,0) not null, hard_deactivation_limit_dyas number(10,0) not null, primary key (id));
create table sefas_configuration (id number(19,0) not null, created timestamp, updated timestamp, connector_id number(19,0), qualification_output_id number(19,0), customer_driver_file varchar2(255 char), execute_incloud_test number(1,0), execute_incloud_preview number(1,0), execute_incloud_proof number(1,0), execute_incloud_simulation number(1,0), pre_qualengine_script varchar2(255 char), post_qualengine_script varchar2(255 char), post_connector_script varchar2(255 char), bundle_filenames_for_images number(1,0) not null, filename_separator_for_images varchar2(255 char), play_messages_on_empty_var number(1,0) not null, override_remote_server number(1,0), remote_server_ip varchar2(255 char), remote_server_port varchar2(255 char), remote_server_user varchar2(255 char), remote_server_password varchar2(255 char), applied_de_version varchar2(255 char), input_character_encoding number(10,0), output_character_encoding number(10,0), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), output_filename_cplex_val_id number(19,0), output_doc_title_cplex_val_id number(19,0), validate_prod_bundle number(1,0), escape_driver_data_tags number(1,0) not null, convert_table_border_px_to_pt number(1,0) not null, updated_by_id number(19,0), composition_file_set_id number(19,0), composition_version varchar2(255 char), output_file_type number(10,0), starts_on_odd_page number(1,0), duplex_output number(1,0), primary key (id));
create table seg_analysis_analyzables (seg_analysis_id number(19,0) not null, model_type number(10,0) not null, model_id number(19,0) not null, primary key (seg_analysis_id, model_type, model_id));
create table segmentation_analysis (id number(19,0) not null, document_id number(19,0), output_filename varchar2(255 char), output_path varchar2(255 char), updated timestamp, created timestamp, completed_date timestamp, request_date timestamp, updated_by_id number(19,0), user_id number(19,0), complete number(1,0), error number(1,0), channel_context_id number(19,0), primary key (id));
create table sendmail_configuration (id number(19,0) not null, created timestamp, updated timestamp, connector_id number(19,0), qualification_output_id number(19,0), customer_driver_file varchar2(255 char), execute_incloud_test number(1,0), execute_incloud_preview number(1,0), execute_incloud_proof number(1,0), execute_incloud_simulation number(1,0), pre_qualengine_script varchar2(255 char), post_qualengine_script varchar2(255 char), post_connector_script varchar2(255 char), bundle_filenames_for_images number(1,0) not null, filename_separator_for_images varchar2(255 char), play_messages_on_empty_var number(1,0) not null, override_remote_server number(1,0), remote_server_ip varchar2(255 char), remote_server_port varchar2(255 char), remote_server_user varchar2(255 char), remote_server_password varchar2(255 char), applied_de_version varchar2(255 char), input_character_encoding number(10,0), output_character_encoding number(10,0), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), output_filename_cplex_val_id number(19,0), output_doc_title_cplex_val_id number(19,0), validate_prod_bundle number(1,0), escape_driver_data_tags number(1,0) not null, convert_table_border_px_to_pt number(1,0) not null, updated_by_id number(19,0), customer_email_address_var_id number(19,0), override_smtp number(1,0), smtp_host varchar2(255 char), smtp_port varchar2(255 char), smtp_security varchar2(255 char), smtp_account varchar2(255 char), smtp_password varchar2(255 char), smtp_custom_header varchar2(512 char), primary key (id));
create table simulation (id number(19,0) not null, name varchar2(96 char), run_date timestamp, request_date timestamp, completed_date timestamp, user_id number(19,0), workgroup_id number(19,0), output_filename varchar2(255 char), output_path varchar2(255 char), document_id number(19,0), data_resource_id number(19,0), use_all_in_process number(1,0), tp_collection_id number(19,0), is_customer_level number(1,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table simulation_content_object (simulation_id number(19,0) not null, content_object_id number(19,0) not null, primary key (simulation_id, content_object_id));
create table source_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table status_polling_background_task (id number(19,0) not null, guid varchar2(255 char) not null, type number(10,0), name varchar2(255 char), description varchar2(4000 char), is_visible number(1,0) not null, refresh_page number(1,0), host_ip_address varchar2(255 char), host_name varchar2(255 char), thread_id number(19,0), progress_in_percent number(10,0), is_active number(1,0) not null, is_staled number(1,0) not null, has_input_data_error number(1,0), target_object_id number(19,0), complete number(1,0), error number(1,0), output_filename varchar2(255 char), output_path varchar2(255 char), request_date timestamp, completed_date timestamp, channel_context_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table sub_content_type (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), parent_content_id number(19,0), primary key (id));
create table sync_history (id number(19,0) not null, document_id number(19,0) not null, object_id number(19,0) not null, object_type_id number(10,0) not null, hide_until_next_change number(1,0) default 0 not null, instance_guid varchar2(255 char), sync_type_id number(10,0) not null, sync_time timestamp not null, sync_by_id number(19,0) not null, source_document_id number(19,0) not null, source_object_id number(19,0) not null, source_model_hash varchar2(90 char) not null, source_archived_copy_hash varchar2(90 char), source_active_copy_hash varchar2(90 char), source_working_copy_hash varchar2(90 char), source_archived_copy_attr_hash varchar2(90 char), source_active_copy_attr_hash varchar2(90 char), source_working_copy_attr_hash varchar2(90 char), target_model_hash varchar2(90 char) not null, target_archived_copy_hash varchar2(90 char), target_active_copy_hash varchar2(90 char), target_working_copy_hash varchar2(90 char), target_archived_copy_attr_hash varchar2(90 char), target_active_copy_attr_hash varchar2(90 char), target_working_copy_attr_hash varchar2(90 char), source_archived_copy_lang_hash number(19,0), source_active_copy_lang_hash number(19,0), source_working_copy_lang_hash number(19,0), target_archived_copy_lang_hash number(19,0), target_active_copy_lang_hash number(19,0), target_working_copy_lang_hash number(19,0), primary key (id));
create table sync_history_locales (sync_history_id number(19,0) not null, messagepoint_locale_id number(19,0));
create table system_notification (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char), number_of_prompts number(10,0), message varchar2(4000 char), active number(1,0), start_time timestamp, end_time timestamp, created timestamp, created_by_id number(19,0), updated timestamp, updated_by_id number(19,0), primary key (id));
create table system_property (id number(19,0) not null, prop_key varchar2(255 char), prop_value varchar2(255 char), prop_desc varchar2(255 char), readonly number(1,0), primary key (id));
create table system_state (id number(19,0) not null, name varchar2(96 char), string_code varchar2(255 char), description varchar2(255 char), bit_flag number(19,0), primary key (id));
create table system_task (id number(19,0) not null, task_type_id number(19,0), name varchar2(96 char), description varchar2(1000 char), updated timestamp, due_date timestamp, updated_by_id number(19,0), created_by_id number(19,0), created timestamp, item_type_id number(10,0), item_id number(19,0), item_guid varchar2(255 char), primary key (id));
create table system_task_type (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table system_task_user (task_id number(19,0) not null, user_id number(19,0) not null, primary key (task_id, user_id));
create table system_theme (id number(19,0) not null, name varchar2(96 char), folder varchar2(255 char), uploaded number(1,0), primary key (id));
create table tag (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, connector_name varchar2(96 char), content varchar2(255 char), tag_content number(19,0), priority number(10,0), max_cap number(10,0), usage_type_id number(10,0) not null, injection_location_type_id number(10,0) not null, tag_type_id number(19,0), owner_user_id number(19,0), document_id number(19,0), status_id number(19,0) not null, start_date timestamp, end_date timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), include_tg_relation number(10,0), excluded_tg_relation number(10,0), extended_tg_relation number(10,0), primary key (id));
create table tag_cloud (id number(19,0) not null, name varchar2(255 char), type_id number(19,0), document_id number(19,0), rationlizer_application_id number(19,0), status number(10,0), primary key (id));
create table tag_content_object (tag_id number(19,0) not null, content_object_id number(19,0) not null, primary key (tag_id, content_object_id));
create table tag_document (tag_id number(19,0) not null, document_id number(19,0) not null, primary key (tag_id, document_id));
create table tag_insert (tag_id number(19,0) not null, insert_id number(19,0) not null, primary key (tag_id, insert_id));
create table tag_target_group_excluded (tag_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (tag_id, sequence));
create table tag_target_group_extended (tag_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (tag_id, sequence));
create table tag_target_group_included (tag_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (tag_id, sequence));
create table tag_tg_instance_map (tg_id number(19,0) not null, tag_id number(19,0) not null, instance_id number(19,0) not null, primary key (tg_id, tag_id));
create table tag_touchpoint_collection (tag_id number(19,0) not null, touchpoint_collection_id number(19,0) not null, primary key (tag_id, touchpoint_collection_id));
create table tag_type (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char) not null, connector_name varchar2(96 char), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table target_group (id number(19,0) not null, name varchar2(255 char), metatags varchar2(255 char), guid varchar2(255 char) not null, condition_relationship number(10,0), instance_id number(19,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, segmentation_data varchar2(4000 char), dna varchar2(255 char) not null, sha256_hash varchar2(90 char), primary key (id));
create table target_group_document (target_group_id number(19,0) not null, document_id number(19,0) not null, primary key (target_group_id, document_id));
create table target_group_instance (id number(19,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), guid varchar2(255 char) not null, search_value clob, primary key (id));
create table task (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(255 char), status_id number(10,0), notice_status_id number(10,0), metatags varchar2(255 char), due_date timestamp, near_term_hours number(10,0), requirement blob, metadata_form_id number(19,0), wf_action_id number(19,0), asset_wf_id number(19,0), project_task_id number(19,0), created timestamp, updated timestamp, created_by_id number(19,0), updated_by_id number(19,0), primary key (id));
create table task_document (task_id number(19,0) not null, document_id number(19,0) not null, primary key (task_id, document_id));
create table task_item (task_id number(19,0) not null, model_type number(10,0) not null, model_id number(19,0) not null, primary key (task_id, model_type, model_id));
create table task_user (task_id number(19,0) not null, user_id number(19,0) not null, primary key (task_id, user_id));
create table template_modifier (id number(19,0) not null, name varchar2(255 char), connector_name varchar2(255 char), is_active number(1,0), template_managed number(1,0), value_type number(10,0), referencing_tp_selection_id number(19,0), tp_selection_id number(19,0), document_id number(19,0), complex_value_id number(19,0), primary key (id));
create table template_variant (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(255 char), document_id number(19,0), primary key (id));
create table tenant_metadata (id number(19,0) not null, model_signature varchar2(255 char), external_id varchar2(255 char), internal_id number(19,0), primary key (id));
create table tenant_permission (tenant_id number(19,0) not null, permission_id number(19,0) not null, primary key (tenant_id, permission_id));
create table tenant_theme_info (tenant_id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char), logo_location varchar2(255 char), header_text clob, provider_logo_location varchar2(255 char), provider_text clob, header_theme_color clob not null, header_theme_type_id number(10,0) not null, system_theme number(19,0), background_theme number(19,0), primary key (tenant_id));
create table tenants (id number(19,0) not null, name varchar2(96 char) not null, code varchar2(255 char) not null, parent_id number(19,0), contact_name varchar2(64 char), contact_title varchar2(16 char), contact_email varchar2(96 char), contact_street_address varchar2(128 char), contact_suit_unit varchar2(16 char), contact_city varchar2(32 char), contact_country varchar2(50 char), contact_province_state varchar2(50 char), contact_postal_zip_code varchar2(10 char), contact_web_site varchar2(96 char), enabled number(1,0) not null, primary key (id));
create table test_scenario (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(255 char), start_date timestamp, created timestamp, updated timestamp, complete number(1,0), error number(1,0), updated_by_id number(19,0), request_date timestamp, last_run_date timestamp, completed_date timestamp, user_id number(19,0), workgroup_id number(19,0), output_filename varchar2(255 char), output_path varchar2(255 char), document_id number(19,0), data_resource_id number(19,0), use_all_in_process number(1,0), use_all_in_process_lib number(1,0), debug_mode number(1,0), delivery_event_type_id number(10,0), delivery_event_schedule_id number(19,0), composition_file_set_id number(19,0), tp_collection_id number(19,0), test_suite_id number(19,0), channel_context_id number(19,0), de_server_guid varchar2(255 char), bundle_name_override varchar2(255 char), primary key (id));
create table test_scenario_content_object (test_id number(19,0) not null, content_object_id number(19,0) not null, primary key (test_id, content_object_id));
create table test_suite (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char), user_id number(19,0), created timestamp, updated timestamp, complete number(1,0), error number(1,0), updated_by_id number(19,0), request_date timestamp, completed_date timestamp, bundle_name_override varchar2(255 char), primary key (id));
create table text_style (id number(19,0) not null, identifier varchar2(96 char), name varchar2(96 char), connector_name varchar2(96 char), color varchar2(6 char), font_name varchar2(96 char), web_font_name varchar2(255 char), point_size number(10,0), bold number(1,0), underline number(1,0), italic number(1,0), tagging_override varchar2(2048 char), toggle_bold number(1,0) not null, toggle_underline number(1,0) not null, toggle_italic number(1,0) not null, toggle_point_size number(1,0) not null, toggle_color number(1,0) not null, font_id number(19,0), apply_font number(1,0), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table text_style_colors (text_style_id number(19,0) not null, value varchar2(255 char));
create table text_style_cust (id number(19,0) not null, connector_name varchar2(96 char), color varchar2(6 char), font_name varchar2(96 char), web_font_name varchar2(255 char), point_size number(10,0), bold number(1,0), underline number(1,0), italic number(1,0), tagging_override varchar2(2048 char), toggle_bold number(1,0) not null, toggle_underline number(1,0) not null, toggle_italic number(1,0) not null, toggle_point_size number(1,0) not null, toggle_color number(1,0) not null, font_id number(19,0), apply_font number(1,0), master_text_style_id number(19,0), origin_object_id number(19,0), checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table text_style_cust_colors (text_style_cust_id number(19,0) not null, value varchar2(255 char));
create table text_style_cust_map (text_style_id number(19,0) not null, document_id number(19,0) not null, text_style_cust_id number(19,0) not null, primary key (text_style_id, document_id));
create table text_style_cust_point_sizes (text_style_cust_id number(19,0) not null, value varchar2(255 char));
create table text_style_font (id number(19,0) not null, name varchar2(255 char), ttf_file_id number(19,0), eot_file_id number(19,0), primary key (id));
create table text_style_point_sizes (text_style_id number(19,0) not null, value varchar2(255 char));
create table touchpoint_collection (id number(19,0) not null, guid varchar2(255 char) not null, name varchar2(96 char), description varchar2(4000 char), enabled number(1,0) not null, is_executable number(1,0), metadata_form_id number(19,0), composition_file_set_id number(19,0), output_filename_cplex_val_id number(19,0), output_doc_title_cplex_val_id number(19,0), primary key (id));
create table touchpoint_language (id number(19,0) not null, name varchar2(96 char), is_default_langauge number(1,0), document_id number(19,0), messagepoint_locale_id number(19,0), touchpoint_locale_id number(19,0), primary key (id));
create table touchpoint_locale (id number(19,0) not null, dictionary varchar2(255 char), date_format varchar2(255 char), currency_symbol varchar2(255 char), suffix_currency_symbol number(1,0), thousands_separator varchar2(255 char), decimal_symbol varchar2(255 char), number_of_decimals varchar2(255 char), boolean_symbol_true varchar2(255 char), boolean_symbol_false varchar2(255 char), short_month_names varchar2(255 char), long_month_names varchar2(255 char), drop_decimal_for_whole_num number(1,0) not null, master_locale_id number(19,0), primary key (id));
create table touchpoint_selection (id number(19,0) not null, guid varchar2(255 char) not null, archive_type_id number(10,0), fully_visible number(1,0) default 1 not null, connected_fully_visible number(1,0) default 1 not null, own_content_ready number(1,0), assignee_id number(19,0), document_id number(19,0), pg_tree_node_id number(19,0), wf_id number(19,0), connected_wf_id number(19,0), wf_action_id number(19,0), content_last_updated timestamp, content_last_updated_by_id number(19,0), latest_production_date timestamp, template_variant_id number(19,0), origin_object_id number(19,0), checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), segmentation_data varchar2(4000 char), alternate_layout_id number(19,0), inherit_metadata number(1,0) not null, metadata_form_id number(19,0), dna varchar2(255 char) not null, sha256_hash varchar2(90 char), primary key (id));
create table touchpoint_selection_workgroup (tp_selection_id number(19,0) not null, workgroup_id number(19,0) not null, primary key (tp_selection_id, workgroup_id));
create table touchpoint_targeting (id number(19,0) not null, guid varchar2(255 char) not null, document_id number(19,0) not null, origin_object_id number(19,0), checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), include_tg_relation number(10,0), excluded_tg_relation number(10,0), extended_tg_relation number(10,0), primary key (id));
create table touchpoint_tg_instance_map (tg_id number(19,0) not null, touchpoint_targeting_id number(19,0) not null, instance_id number(19,0) not null, primary key (tg_id, touchpoint_targeting_id));
create table tp_collection_touchpoint (id number(19,0) not null, tp_order number(10,0) not null, collection_id number(19,0), document_id number(19,0), primary key (id));
create table tp_delivery_report_doc (tp_delivery_report_id number(19,0) not null, document_id number(19,0) not null, primary key (tp_delivery_report_id, document_id));
create table tp_delivery_report_event (report_id number(19,0) not null, delivery_event_id number(19,0) not null, primary key (report_id, delivery_event_id));
create table tp_delivery_report_scenario (id number(19,0) not null, name varchar2(96 char), start_date timestamp, created timestamp, updated timestamp, complete number(1,0), error number(1,0), updated_by_id number(19,0), end_date timestamp, xml_path varchar2(255 char), report_path varchar2(255 char), primary key (id));
create table tp_instance_locales (tpivm_id number(19,0) not null, messagepoint_locale_id number(19,0) not null, primary key (tpivm_id, messagepoint_locale_id));
create table tp_instance_visibility_map (id number(19,0) not null, tp_guid varchar2(255 char) not null, dcs_guid varchar2(255 char) not null, instance_guid varchar2(255 char) not null, primary key (id));
create table tp_sel_visible_user (tp_selection_id number(19,0) not null, user_id number(19,0) not null, primary key (tp_selection_id, user_id));
create table tp_target_group_excluded (touchpoint_targeting_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (touchpoint_targeting_id, sequence));
create table tp_target_group_extended (touchpoint_targeting_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (touchpoint_targeting_id, sequence));
create table tp_target_group_included (touchpoint_targeting_id number(19,0) not null, sequence number(10,0) not null, target_group_id number(19,0) not null, primary key (touchpoint_targeting_id, sequence));
create table user_permission (user_id number(19,0) not null, permission_id number(19,0) not null, primary key (user_id, permission_id));
create table user_role (user_id number(19,0) not null, role_id number(19,0) not null, primary key (user_id, role_id));
create table users (id number(19,0) not null, guid varchar2(255 char) not null, email varchar2(96 char), notes varchar2(4000 char), pinc_permissions varchar2(4000 char), password varchar2(255 char), user_id varchar2(255 char), passwordexpired number(1,0) default 0, first_name varchar2(255 char), last_name varchar2(255 char), isactive number(1,0) default 1 not null, isenabled number(1,0) default 1 not null, email_verified number(1,0) default 0 not null, soft_deactivated number(1,0) not null, default_tab_id number(10,0) default 13, email_notify_daily number(1,0) default 1, email_notify_realtime number(1,0) default 1, apply_notification_digest number(1,0) default 0, email_token varchar2(255 char), webdav_token varchar2(255 char), salt varchar2(255 char), app_locale_id number(19,0), updated timestamp, updated_by_id number(19,0), created timestamp, last_login timestamp, invalid_signins number(10,0) not null, deactiv_reason_id number(19,0), context_attributes varchar2(4000 char), default_list_filter_id number(10,0), insert_list_filter_id number(10,0), insert_schedule_list_filter_id number(10,0), tp_selection_list_filter_id number(10,0), tp_cont_seln_list_filter_id number(10,0), tag_list_filter_id number(10,0), testing_list_filter_id number(10,0), variable_list_filter_id number(10,0), report_list_filter_id number(10,0), workgroup_id number(19,0), supervisor_id number(19,0), hidden_supervisor number(1,0), password_last_updated timestamp, master_admin_status number(10,0) not null, idp_type number(10,0) not null, idp_identifier varchar2(255 char), idp_subject varchar2(255 char), idp_user_guid varchar2(255 char), idp_user_groups varchar2(255 char), default_node_id number(19,0) default 0 not null, licensed_type number(10,0) not null, primary key (id));
create table variable_data_element_map (id number(19,0) not null, de_id number(19,0), op_id number(10,0), data_record_level_id number(10,0), primary key (id));
create table variable_document (variable_id number(19,0) not null, document_id number(19,0) not null, primary key (variable_id, document_id));
create table version_activity_reason (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), string_code varchar2(255 char), primary key (id));
create table version_status (id number(19,0) not null, name varchar2(96 char), string_code varchar2(255 char), description varchar2(255 char), primary key (id));
create table web_service_config (id number(19,0) not null, guid varchar2(255 char) not null, url varchar2(255 char), user_id varchar2(255 char), password varchar2(255 char), de_server_guid varchar2(255 char), primary key (id));
create table workflow (id number(19,0) not null, name varchar2(96 char), guid varchar2(255 char) not null, wf_model_type number(10,0) not null, wf_usage_type number(10,0), document_id number(19,0), created timestamp, created_by_id number(19,0), updated timestamp, updated_by_id number(19,0), primary key (id));
create table workflow_action (id number(19,0) not null, model_type number(10,0) not null, model_id number(19,0) not null, wf_step_id number(19,0), previous_action_id number(19,0), next_action_id number(19,0), is_active number(1,0) default 0 not null, is_due_by_notified number(1,0) default 0 not null, release_for_approval_date timestamp, created timestamp, created_by_id number(19,0), updated timestamp, updated_by_id number(19,0), primary key (id));
create table workflow_action_history (id number(19,0) not null, wf_action_id number(19,0), model_type number(10,0) not null, model_id number(19,0) not null, model_data_guid varchar2(255 char), action number(10,0), state_prior number(10,0), state_current number(10,0), action_type number(10,0), hc_step_name varchar2(255 char), user_id number(19,0), assigned_to number(19,0), action_date timestamp, notes varchar2(255 char), primary key (id));
create table workflow_instance (id number(19,0) not null, guid varchar2(255 char) not null, workflow_id number(19,0), status_id number(19,0), owner number(19,0), created timestamp, created_by_id number(19,0), updated timestamp, updated_by_id number(19,0), primary key (id));
create table workflow_library_document (workflow_library_id number(19,0) not null, document_id number(19,0) not null, primary key (workflow_library_id, document_id));
create table workflow_position (id number(19,0) not null, workflow_id number(19,0) not null, workflow_state_id number(19,0) not null, required number(1,0) default 1, requires_permission number(1,0) default 0, enabled number(1,0), name varchar2(255 char) not null, authorization_type varchar2(2 char) not null, previous number(19,0), next number(19,0), primary key (id));
create table workflow_position_permission (workflow_position_id number(19,0) not null, permission_id number(19,0) not null, primary key (workflow_position_id, permission_id));
create table workflow_position_user (workflow_position_id number(19,0) not null, user_id number(19,0) not null, primary key (workflow_position_id, user_id));
create table workflow_property (id number(19,0) not null, workflow_position_id number(19,0) not null, required number(1,0) default 1, name varchar2(255 char) not null, primary key (id));
create table workflow_state (id number(19,0) not null, name varchar2(96 char), string_id varchar2(255 char), primary key (id));
create table workflow_step (id number(19,0) not null, guid varchar2(255 char) not null, state varchar2(255 char) not null, is_mandatory number(1,0) not null, is_approval number(1,0) not null, removed number(1,0) default 0 not null, approve_type number(10,0) not null, wf_instance_id number(19,0), previous_step_id number(19,0), next_step_id number(19,0), step_type number(10,0) not null, allow_edit_default number(1,0) not null, is_dueby number(1,0) not null, dueby_type number(10,0), dueby_days number(10,0), dueby_hours number(10,0), condition_type varchar2(20 char), created timestamp, created_by_id number(19,0), updated timestamp, updated_by_id number(19,0), primary key (id));
create table workflow_step_langs (wf_step_id number(19,0) not null, lang_id number(19,0) not null, primary key (wf_step_id, lang_id));
create table workflow_tab (id number(19,0) not null, workflow_position_id number(19,0) not null, name varchar2(255 char) not null, parameter varchar2(255 char) not null, string_id varchar2(255 char), authorization_type varchar2(2 char) not null, edit_url varchar2(255 char), view_url varchar2(255 char), list_url varchar2(255 char), visibility_toggle_attr varchar2(255 char), required_id number(19,0), primary key (id));
create table workflow_tab_permission (workflow_tab_id number(19,0) not null, permission_id number(19,0) not null, primary key (workflow_tab_id, permission_id));
create table workgroup (id number(19,0) not null, name varchar2(96 char) not null, description varchar2(255 char), default_workgroup number(1,0) not null, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table workgroup_zone (zone_id number(19,0) not null, workgroup_id number(19,0) not null, primary key (workgroup_id, zone_id));
create table xml_data_tag_definition (id number(19,0) not null, data_source_id number(19,0), tag_name varchar2(256 char), start_customer number(1,0), start_data_group number(1,0), break_indicator varchar2(64 char), repeating number(1,0), updated timestamp, created timestamp, updated_by_id number(19,0), created_by_id number(19,0), parent_tag_id number(19,0), data_group_id number(19,0), dna varchar2(255 char) not null, primary key (id));
create table zone (id number(19,0) not null, name varchar2(96 char), override_name number(1,0), friendly_name varchar2(255 char), override_friendly_name number(1,0), description varchar2(255 char), parent_zone_id number(19,0), content_type_id number(19,0), sub_content_type_id number(19,0), zone_type_id number(10,0), enabled number(1,0), override_enabled number(1,0), origin_object_id number(19,0), checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), top_x number(10,0), top_y number(10,0), override_position number(1,0), width number(10,0), height number(10,0), override_dimensions number(1,0), page number(10,0), rotation_angle number(10,0) not null, background_color varchar2(48 char), override_background_color number(1,0), default_canvas_width number(10,0), can_flow number(1,0), can_grow number(1,0), repeats number(1,0), repeating_zone_type_id number(10,0), minimum_size number(1,0), absolute_positioning number(1,0) not null, rendered_container_type_id number(10,0) not null, image_dimensions_type_id number(10,0) not null, enforce_min_height number(1,0) not null, split_tables number(1,0) not null, default_text_style_id number(19,0), override_default_text_style number(1,0), default_paragraph_style_id number(19,0), override_default_para_style number(1,0), default_list_style_id number(19,0), override_default_list_style number(1,0), dxf_output number(1,0) not null, html_output number(1,0) not null, supports_tables number(1,0) not null, supports_forms number(1,0) not null, supports_barcodes number(1,0) not null, is_freeform number(1,0) not null, export_to_single_message number(1,0) not null, data_group_id number(19,0), mixed_data_groups number(1,0) not null, document_id number(19,0), document_section_id number(19,0), override_document_section number(1,0), template_image_id number(19,0), template_smart_text_id number(19,0), permit_comm_content_edit number(1,0) not null, override_comm_template number(1,0), restrict_shared_assets number(1,0) not null, override_shared_assets number(1,0), permit_message_content_edit number(1,0) not null, apply_alt_text number(1,0), apply_image_link number(1,0), apply_image_ext_link number(1,0), apply_image_ext_path number(1,0), override_text_styles number(1,0), override_paragraph_styles number(1,0), override_list_styles number(1,0), override_workgroups number(1,0), dna varchar2(255 char) not null, sha256_hash varchar2(90 char), primary key (id));
create table zone_attributes (zone_id number(19,0) not null, attribute_name varchar2(255 char) not null, attribute_value varchar2(255 char) not null, primary key (zone_id, attribute_name));
create table zone_image_assets (zone_id number(19,0) not null, image_id number(19,0) not null, primary key (zone_id, image_id));
create table zone_list_style (zone_id number(19,0) not null, list_style_id number(19,0) not null, primary key (zone_id, list_style_id));
create table zone_paragraph_style (zone_id number(19,0) not null, paragraph_style_id number(19,0) not null, primary key (zone_id, paragraph_style_id));
create table zone_part (id number(19,0) not null, parent_zone_part_id number(19,0), name varchar2(96 char), override_name number(1,0), width number(10,0), height number(10,0), override_dimensions number(1,0), topx number(10,0), topy number(10,0), override_position number(1,0), optional number(1,0), sequence number(10,0), zone_id number(19,0) not null, content_type number(19,0), sub_content_type_id number(19,0), origin_object_id number(19,0), dna varchar2(255 char) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), sha256_hash varchar2(90 char), primary key (id));
create table zone_smart_text_assets (zone_id number(19,0) not null, smart_text_id number(19,0) not null, primary key (zone_id, smart_text_id));
create table zone_style (zone_id number(19,0) not null, style_id number(19,0) not null, primary key (zone_id, style_id));
alter table attachment add constraint uc_guid_attachment unique (guid);
alter table audit_report add constraint uc_guid_audit_report unique (guid);
alter table branch add constraint uc_guid_branch unique (guid);
alter table branch add constraint uc_dcs_schema_name_branch unique (dcs_schema_name);
alter table communication add constraint uc_guid_communication unique (guid);
alter table complex_value add constraint uc_guid_complex_value unique (guid);
alter table compound_key add constraint uc_guid_compound_key unique (guid);
alter table compound_key_item add constraint uc_guid_compound_key_item unique (guid);
alter table condition_element add constraint uc_guid_condition_element unique (guid);
alter table condition_item add constraint uc_guid_condition_item unique (guid);
alter table condition_subelement add constraint uc_guid_condition_subelement unique (guid);
alter table content_object add constraint uc_document_id_dna_375726533 unique (document_id, dna);
alter table content_object add constraint uc_guid_content_object unique (guid);
alter table content_object_data add constraint uc_guid_content_object_data unique (guid);
alter table content_object_zone_priority add constraint uc_guid_584982563 unique (guid);
alter table content_targeting add constraint uc_guid_content_targeting unique (guid);
alter table data_element_core add constraint uc_dna_data_element_core unique (dna);
alter table data_element_variable add constraint uc_guid_data_element_variable unique (guid);
alter table data_element_variable add constraint uc_dna_data_element_variable unique (dna);
alter table data_record add constraint uc_dna_data_record unique (dna);
alter table data_source add constraint uc_dna_data_source unique (dna);
alter table data_source_association add constraint uc_dna_data_source_association unique (dna);
alter table deserver add constraint uc_guid_deserver unique (guid);
alter table document add constraint uc_guid_document unique (guid);
alter table document_section add constraint uc_document_id_dna_1691178047 unique (document_id, dna);
alter table document_section add constraint uc_guid_document_section unique (guid);
alter table domain add constraint uc_name_domain unique (name);
alter table domain_pod add constraint uc_domain_guid_pod_domain_pod unique (domain_guid, pod);
alter table folders add constraint uc_guid_folders unique (guid);
alter table hist_metadata_form add constraint uc_guid_hist_metadata_form unique (guid);
alter table insert_obj add constraint uc_guid_insert_obj unique (guid);
alter table insert_schedule add constraint uc_guid_insert_schedule unique (guid);
alter table insert_schedule_bin_assignment add constraint uc_guid_1927042009 unique (guid);
alter table insert_schedule_collection add constraint uc_guid_1675987264 unique (guid);
alter table language_selection add constraint uc_guid_language_selection unique (guid);
alter table licence add constraint uc_guid_licence unique (guid);
alter table licence_audit_action add constraint uc_name_licence_audit_action unique (name);
alter table licence_customer add constraint uc_guid_licence_customer unique (guid);
alter table licence_customer add constraint uc_name_licence_customer unique (name);
alter table licence_module add constraint uc_guid_licence_module unique (guid);
alter table licence_representative add constraint uc_guid_licence_representative unique (guid);
alter table licence_representative add constraint uc_name_licence_representative unique (name);
alter table lookup_table add constraint uc_guid_lookup_table unique (guid);
alter table lookup_table add constraint uc_dna_lookup_table unique (dna);
alter table lookup_table_instance add constraint uc_guid_lookup_table_instance unique (guid);
alter table messagepoint_locale add constraint uc_code_messagepoint_locale unique (code);
alter table metadata_form add constraint uc_guid_metadata_form unique (guid);
alter table metadata_form_definition add constraint uc_guid_1260596894 unique (guid);
alter table node add constraint uc_guid_node unique (guid);
alter table parameter add constraint uc_guid_parameter unique (guid);
alter table parameter_group add constraint uc_guid_parameter_group unique (guid);
alter table parameter_group_instance add constraint uc_guid_10780245 unique (guid);
alter table parameter_group_item add constraint uc_guid_parameter_group_item unique (guid);
alter table password_recovery add constraint uc_reset_key_password_recovery unique (reset_key);
alter table pg_instance_collection add constraint uc_guid_pg_instance_collection unique (guid);
alter table pg_tree_node add constraint uc_guid_pg_tree_node unique (guid);
alter table pod add constraint uc_name_pod unique (name);
alter table pod add constraint uc_url_pod unique (url);
alter table project add constraint uc_guid_project unique (guid);
alter table proof add constraint uc_guid_proof unique (guid);
alter table proof_definition add constraint uc_guid_proof_definition unique (guid);
alter table rate_schedule add constraint uc_guid_rate_schedule unique (guid);
alter table rate_schedule_collection add constraint uc_guid_948135783 unique (guid);
alter table rate_schedule_detail add constraint uc_guid_rate_schedule_detail unique (guid);
alter table rationalizer_application add constraint uc_guid_1477266083 unique (guid);
alter table rationalizer_document add constraint uc_guid_rationalizer_document unique (guid);
alter table rationalizer_document_content add constraint uc_guid_1594405224 unique (guid);
alter table rationalizer_shared_content add constraint uc_guid_1056476018 unique (guid);
alter table redirection_info add constraint uc_domain_redirection_info unique (domain);
alter table search_result_ids add constraint uc_guid_search_result_ids unique (guid);
alter table status_polling_background_task add constraint uc_guid_404367405 unique (guid);
alter table system_notification add constraint uc_guid_system_notification unique (guid);
alter table tag add constraint uc_guid_tag unique (guid);
alter table tag_type add constraint uc_guid_tag_type unique (guid);
alter table target_group add constraint uc_guid_target_group unique (guid);
alter table target_group add constraint uc_dna_target_group unique (dna);
alter table task add constraint uc_guid_task unique (guid);
alter table template_variant add constraint uc_guid_template_variant unique (guid);
alter table tenant_theme_info add constraint uc_guid_tenant_theme_info unique (guid);
alter table test_scenario add constraint uc_guid_test_scenario unique (guid);
alter table test_suite add constraint uc_guid_test_suite unique (guid);
alter table touchpoint_collection add constraint uc_guid_touchpoint_collection unique (guid);
alter table touchpoint_selection add constraint uc_document_id_dna_408229762 unique (document_id, dna);
alter table touchpoint_selection add constraint uc_guid_touchpoint_selection unique (guid);
alter table touchpoint_targeting add constraint uc_guid_touchpoint_targeting unique (guid);
alter table users add constraint uc_guid_users unique (guid);
alter table workflow add constraint uc_guid_workflow unique (guid);
alter table workflow_instance add constraint uc_guid_workflow_instance unique (guid);
alter table workflow_step add constraint uc_guid_workflow_step unique (guid);
alter table xml_data_tag_definition add constraint uc_dna_xml_data_tag_definition unique (dna);
alter table zone add constraint uc_document_id_dna_zone unique (document_id, dna);
alter table zone_part add constraint uc_zone_id_dna_zone_part unique (zone_id, dna);
alter table columnar_indicators add constraint FKlp4jw6awtyen9uxx0q4jqyst2 foreign key (data_source_id) references data_source;
alter table delimited_indicators add constraint FK6j1bgfu1hnfi4yysde9f58lu7 foreign key (data_source_id) references data_source;
alter table domain_pod add constraint fk_domain_domain_pod foreign key (domain) references domain;
alter table domain_pod add constraint fk_pod_domain_pod foreign key (pod) references pod;
alter table redirection_info add constraint fk_domain_redirection_info foreign key (domain) references domain;
alter table redirection_info add constraint fk_production_redirection_info foreign key (production) references pod;
alter table redirection_info add constraint fk_transition_redirection_info foreign key (transition) references pod;
alter table tenant_theme_info add constraint FK9vepu9c47ggmfriabvy439kis foreign key (tenant_id) references tenants;
CREATE INDEX CI_CO_ASSOCIATION_O1 ON CONTENT_OBJECT_ASSOCIATION (CONTENT_OBJECT_ID, DATA_TYPE);
CREATE INDEX CI_CO_COMMENT_O1 ON CONTENT_OBJECT_COMMENT (CONTENT_OBJECT_ID, DATA_TYPE);
CREATE INDEX CI_CO_ZONE_ASSOCIATION_O1 ON CONTENT_OBJECT_ZONE_PRIORITY (TOUCHPOINT_SELECTION_ID,ZONE_ID);
CREATE INDEX CI_CO_ZONE_ASSOCIATION_O2 ON CONTENT_OBJECT_ZONE_PRIORITY (CONTENT_OBJECT_ID, DATA_TYPE);
CREATE INDEX CI_CO_ZONE_ASSOCIATION_O3 ON CONTENT_OBJECT_ZONE_PRIORITY (CONTENT_OBJECT_PRIORITY);
CREATE INDEX CI_HIST_CO_DATA_O1 ON HIST_CONTENT_OBJECT_DATA (CONTENT_OBJECT_DATA_GUID);
CREATE INDEX CI_HIST_CO_ASSOCIATION_O1 ON HIST_CONTENT_OBJ_ASSOCIATION (CONTENT_OBJECT_DATA_GUID);
CREATE INDEX CI_APPROVAL_O1 ON APPROVAL (MODEL_DATA_GUID);
CREATE INDEX CI_WORKFLOW_ACTION_HISTORY_O1 ON WORKFLOW_ACTION_HISTORY (MODEL_DATA_GUID);
CREATE INDEX CI_CONTENT_TEXT_CONTENT ON CONTENT(text_content) INDEXTYPE IS CTXSYS.CONTEXT PARAMETERS('sync (on commit) transactional');
CREATE INDEX CI_TEXT_STYLE_IDENTIFIER ON TEXT_STYLE(identifier);
CREATE INDEX CI_PARAGRAPH_STYLE_IDENTIFIER ON PARAGRAPH_STYLE(identifier);
CREATE INDEX CI_CONTENT_HASH ON CONTENT(sha256_hash, id);
CREATE INDEX CI_PG_TREE_NODE_DNA ON PG_TREE_NODE (DNA);
CREATE INDEX CI_DOCUMENT_DNA ON DOCUMENT (DNA);
CREATE INDEX CI_DOCUMENT_SECTION_DNA ON DOCUMENT_SECTION (DNA);
CREATE INDEX CI_LANGUAGE_SELECTION_DNA ON LANGUAGE_SELECTION (DNA);
CREATE INDEX CI_TOUCHPOINT_SELECTION_DNA ON TOUCHPOINT_SELECTION (DNA);
CREATE INDEX CI_ZONE_DNA ON ZONE (DNA);
CREATE INDEX CI_ZONE_PART_DNA ON ZONE_PART (DNA);
CREATE INDEX CI_CONTENT_OBJECT_DNA ON CONTENT_OBJECT (DNA);
CREATE INDEX CI_TARGET_GROUP_DNA ON TARGET_GROUP (DNA);
CREATE INDEX CI_LOOKUP_TABLE_DNA ON LOOKUP_TABLE (DNA);
CREATE INDEX CI_ATTACHMENT_DNA ON ATTACHMENT (DNA);
CREATE INDEX CI_DATA_RESOURCE_DNA ON DATA_RESOURCE (DNA);
CREATE INDEX CI_DATA_ELEMENT_VARIABLE_DNA ON DATA_ELEMENT_VARIABLE (DNA);
CREATE INDEX CI_DATA_SOURCE_DNA ON DATA_SOURCE (DNA);
CREATE INDEX CI_DATA_SOURCE_ASSOCIATION_DNA ON DATA_SOURCE_ASSOCIATION (DNA);
CREATE INDEX CI_PARAMETER_GROUP_ITEM_PG_ID ON PARAMETER_GROUP_ITEM (PG_ID);
CREATE INDEX RD_INDEX_ON_APP_ID ON RATIONALIZER_DOCUMENT (RATIONALIZER_APPLICATION_ID);
CREATE INDEX RDC_INDEX_ON_DOCUMENT_ID ON RATIONALIZER_DOCUMENT_CONTENT (RATIONALIZER_DOCUMENT_ID);
CREATE INDEX MF_INDEX_ON_FORM_DEF_ID ON METADATA_FORM (FORM_DEFINITION_ID);
CREATE INDEX MFI_INDEX_ON_MFI_FIDI  ON METADATA_FORM_ITEM (METADATA_FORM_ID, FORM_ITEM_DEFINITION_ID);
CREATE INDEX CI_SYNC_HISTORY_01 ON SYNC_HISTORY (DOCUMENT_ID, OBJECT_ID, OBJECT_TYPE_ID, SYNC_TYPE_ID);
CREATE INDEX CI_SYNC_HISTORY_02 ON SYNC_HISTORY (DOCUMENT_ID, OBJECT_ID, OBJECT_TYPE_ID, SYNC_TYPE_ID, INSTANCE_GUID, SOURCE_DOCUMENT_ID, SOURCE_OBJECT_ID);
CREATE INDEX CI_JOB_97 ON JOB (EVENT_TYPE_ID,SYSTEM_STATE_ID);
CREATE INDEX CI_ORDER_ENTRY_ITEM_97 ON ORDER_ENTRY_ITEM (COMMUNICATION_ID,ID);
CREATE INDEX CI_DELIVERY_EVENT_97 ON DELIVERY_EVENT (JOB_ID);
CREATE INDEX CI_DELIVERY_EVENT_98 ON DELIVERY_EVENT (SCHEDULED_TIME);
CREATE INDEX CI_DELIVERY_EVENT_99 ON DELIVERY_EVENT (EVENT_TYPE_ID, REFERENCE_ID, CREATED);
create sequence hibernate_sequence start with 1000;

drop table schema_info;
create table schema_info (version number(11,0));
drop table data_migration_info;
create table data_migration_info (version number(11,0));
drop table data_migration_info_static;
create table data_migration_info_static (filename varchar2(255 char) not null, version varchar2(32 char) not null, primary key (filename));
drop table schema_migrations;
