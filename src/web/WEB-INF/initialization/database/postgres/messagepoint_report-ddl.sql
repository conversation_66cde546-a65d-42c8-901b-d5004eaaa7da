drop table if exists batch_operation_statistics cascade;
drop table if exists batch_statistics_summary cascade;
drop table if exists complete_insert_report cascade;
drop table if exists complete_report cascade;
drop table if exists condition_coverage_report cascade;
drop table if exists customer cascade;
drop table if exists customer_field cascade;
drop table if exists executed_variable cascade;
drop table if exists job_insert cascade;
drop table if exists job_insert_recipient cascade;
drop table if exists job_insert_recipient_report cascade;
drop table if exists job_insert_schedule cascade;
drop table if exists job_insert_schedule_insert_id cascade;
drop table if exists job_insert_schedule_rates cascade;
drop table if exists job_insert_schedule_statistic cascade;
drop table if exists job_insert_statistic cascade;
drop table if exists job_message cascade;
drop table if exists job_rate_schedule cascade;
drop table if exists job_rate_schedule_statistic cascade;
drop table if exists job_statistics_variant cascade;
drop table if exists job_weight_limit cascade;
drop table if exists log_entry cascade;
drop table if exists message_coverage_report cascade;
drop table if exists msg_target_coverage_report cascade;
drop table if exists report cascade;
drop table if exists report_graphic cascade;
drop table if exists report_job_metadata cascade;
drop table if exists report_job_metadata_detail cascade;
drop table if exists report_job_metadata_part cascade;
drop table if exists report_job_metadata_xml cascade;
drop table if exists rule_coverage_report cascade;
drop table if exists simulation_coverage_report cascade;
drop table if exists target_group_coverage_report cascade;
drop table if exists unhandled_delivery_updates cascade;
drop table if exists zone_coverage_report cascade;
create table batch_operation_statistics (id int8 not null, job_id int8, batch_id int8, qual_eng_start timestamp, qual_eng_finish timestamp, delivery_start timestamp, delivery_finish timestamp, recipient_count int8, primary key (id));
create table batch_statistics_summary (id int8 not null, job_id int8, batch_id int8, job_type int8, document_id int8, year varchar(2), month varchar(2), rundate timestamp, recipients int8, primary key (id));
create table complete_insert_report (id int8 not null, insert_id int8, scenario_id int8, scenario_type_id int4, qualified int8, delivered int8, total int8, primary key (id));
create table complete_report (id int8 not null, content_object_id int8, scenario_id int8, scenario_type_id int4, qualified int8, delivered int8, total int8, primary key (id));
create table condition_coverage_report (id int8 not null, guid varchar(255), condition_instance_id int8, passes int8, attempts int8, data_group_id int8, rule_coverage_report_id int8, primary key (id));
create table customer (job_id int8 not null, part_id int4 not null, transaction_id varchar(255) not null, customer_id varchar(255) not null, language_code varchar(255) not null, email_guid varchar(255), email_status int4, email_address varchar(255), document_delivered boolean, updated timestamp, primary key (job_id, part_id, transaction_id, customer_id));
create table customer_field (job_id int8 not null, part_id int4 not null, transaction_id varchar(255) not null, customer_id_value varchar(255) not null, field varchar(255) not null, f_value varchar(255), primary key (job_id, part_id, transaction_id, customer_id_value, field));
create table executed_variable (job_id int8 not null, part_id int4 not null, transaction_id varchar(255) not null, sq int8 not null, name varchar(96), f_value varchar(4000), report_sequence int8, primary key (job_id, part_id, transaction_id, sq));
create table job_insert (id int8 not null, job_id int8, insert_id int8, name varchar(96) not null, insert_external_id varchar(96), weight int8, delivery_type varchar(20), primary key (id));
create table job_insert_recipient (id int8 not null, batch_statistics_id int8, recipient_id varchar(255), insert_schedule_id int8, rate_schedule_id int8, sheet_count int8, delivery_weight int8, delivery_cost int8, primary key (id));
create table job_insert_recipient_report (id int8 not null, job_insert_recipient_id int8, insert_id int8, delivered boolean, primary key (id));
create table job_insert_schedule (id int8 not null, job_id int8, insert_schedule_id int8, name varchar(96) not null, schedule_id varchar(96), primary key (id));
create table job_insert_schedule_insert_id (job_insert_schedule_id int8 not null, insert_id int8 not null, primary key (job_insert_schedule_id, insert_id));
create table job_insert_schedule_rates (job_insert_schedule_id int8 not null, number_of_sheets int8 not null, rate_schedule_id int8, primary key (job_insert_schedule_id, number_of_sheets));
create table job_insert_schedule_statistic (id int8 not null, batch_statistics_id int8, insert_schedule_id int8, qualified int8, primary key (id));
create table job_insert_statistic (id int8 not null, batch_statistics_id int8, insert_id int8, qualified int8, delivered int8, primary key (id));
create table job_message (id int8 not null, job_id int8, content_object_id int8, message_external_id varchar(255), name varchar(256) not null, job_metadata_model_id int8, primary key (id));
create table job_rate_schedule (id int8 not null, job_id int8, rate_schedule_id int8, name varchar(96) not null, envelope_name varchar(255), envelope_weight int8, weight_limits_units int8, primary key (id));
create table job_rate_schedule_statistic (id int8 not null, batch_statistics_id int8, rate_schedule_id int8, qualified int8, primary key (id));
create table job_statistics_variant (job_id int8 not null, part_id int4 not null, transaction_id varchar(255) not null, content_object_id int8 not null, variant_id int8 not null, qualified int8, delivered int8, total int8, primary key (job_id, part_id, transaction_id, content_object_id, variant_id));
create table job_weight_limit (id int8 not null, job_rate_schedule_id int8, weight_limit int8, cost int8, primary key (id));
create table log_entry (id int8 not null, guid varchar(255) not null, detail_id int8, type_f int4, text varchar(255), log_time timestamp, updated timestamp, primary key (id));
create table message_coverage_report (id int8 not null, guid varchar(255), zone_coverage_report_id int8, content_object_id int8, qualified int8, disqualified int8, attempts int8, primary key (id));
create table msg_target_coverage_report (message_coverage_report_id int8 not null, targetgrp_coverage_report_id int8 not null, primary key (message_coverage_report_id, targetgrp_coverage_report_id));
create table report (job_id int8 not null, part_id int4 not null, transaction_id varchar(255) not null, sq int8 not null, customer_id varchar(255), zone_id int8, content_object_id int8, variant_id int8, tp_selectable_id int8, played boolean, primary key (job_id, part_id, transaction_id, sq));
create table report_graphic (job_id int8 not null, batch_id int4 not null, transaction_id varchar(255) not null, seq int8 not null, report_sequence int8, part_number int8, content_library_id int8, content_library_selection_id int8, primary key (job_id, batch_id, transaction_id, seq));
create table report_job_metadata (id int8 not null, delivery_event_id int8, delivery_event_type_id int8, delivery_event_request_date timestamp, job_id int8, job_creation_date timestamp, primary key (id));
create table report_job_metadata_detail (id int8 not null, job_id int8, return_code int4, log text, completion_date varchar(255), part_id int8, primary key (id));
create table report_job_metadata_part (id int8 not null, name varchar(96), sequence int4, primary key (id));
create table report_job_metadata_xml (xml_id int4 not null, parent_id int8 not null, xml_data text, xml_compressed boolean, primary key (xml_id, parent_id));
create table rule_coverage_report (id int8 not null, guid varchar(255), rule_instance_id int8, passes int8, attempts int8, or_conditions boolean, target_coverage_report_id int8, primary key (id));
create table simulation_coverage_report (id int8 not null, job_id int8, customer_count int8, primary key (id));
create table target_group_coverage_report (id int8 not null, guid varchar(255), model_id int8, target_group_id int8, passes int8, attempts int8, exclude boolean, or_rules boolean, primary key (id));
create table unhandled_delivery_updates (id int8 not null, email_guid varchar(255), email_status int4, created timestamp, primary key (id));
create table zone_coverage_report (id int8 not null, zone_id int8, data_group_count int8, messages_qualified int8, qualification_attempts int8, simulation_coverage_report_id int8, primary key (id));
alter table log_entry add constraint uc_guid_log_entry unique (guid);
alter table condition_coverage_report add constraint fk_condition_coverage_report_rule_coverage_report_id foreign key (rule_coverage_report_id) references rule_coverage_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table job_insert_recipient_report add constraint fk_job_insert_recipient_report_job_insert_recipient_id foreign key (job_insert_recipient_id) references job_insert_recipient on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table job_insert_schedule_insert_id add constraint fk_job_insert_schedule_insert_id_job_insert_schedule_id foreign key (job_insert_schedule_id) references job_insert_schedule on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table job_insert_schedule_rates add constraint fk_job_insert_schedule_rates_job_insert_schedule_id foreign key (job_insert_schedule_id) references job_insert_schedule on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table job_weight_limit add constraint fk_job_weight_limit_job_rate_schedule_id foreign key (job_rate_schedule_id) references job_rate_schedule on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table message_coverage_report add constraint fk_message_coverage_report_zone_coverage_report_id foreign key (zone_coverage_report_id) references zone_coverage_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_target_coverage_report add constraint fk_msg_target_coverage_report_targetgrp_coverage_report_id foreign key (targetgrp_coverage_report_id) references target_group_coverage_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_target_coverage_report add constraint fk_msg_target_coverage_report_message_coverage_report_id foreign key (message_coverage_report_id) references message_coverage_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table report_job_metadata_detail add constraint fk_report_job_metadata_detail_part_id foreign key (part_id) references report_job_metadata_part on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table report_job_metadata_xml add constraint fk_report_job_metadata_xml_parent_id foreign key (parent_id) references report_job_metadata on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rule_coverage_report add constraint fk_rule_coverage_report_target_coverage_report_id foreign key (target_coverage_report_id) references target_group_coverage_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_coverage_report add constraint fk_zone_coverage_report_simulation_coverage_report_id foreign key (simulation_coverage_report_id) references simulation_coverage_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
CREATE INDEX CI_REPORT_JOB_METADATA_XML_01 ON report_job_metadata_xml (xml_id);
CREATE INDEX CI_REPORT_JOB_METADATA_XML_02 ON report_job_metadata_xml (parent_id);

drop table schema_info_report;
create table schema_info_report (version numeric(11,0));
drop table data_migration_info_report;
create table data_migration_info_report (version numeric(11,0));
drop table schema_migrations_report;
