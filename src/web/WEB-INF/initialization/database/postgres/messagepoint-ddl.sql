drop table if exists action_entry cascade;
drop table if exists aggregation_operator cascade;
drop table if exists all_reference_query cascade;
drop table if exists app_version cascade;
drop table if exists application_locale cascade;
drop table if exists approval cascade;
drop table if exists approval_detail cascade;
drop table if exists approval_user cascade;
drop table if exists attachment cascade;
drop table if exists attachment_document cascade;
drop table if exists attachment_tg_instance_map cascade;
drop table if exists attchmnt_target_group_excluded cascade;
drop table if exists attchmnt_target_group_extended cascade;
drop table if exists attchmnt_target_group_included cascade;
drop table if exists audit_event cascade;
drop table if exists audit_event_doc cascade;
drop table if exists audit_report cascade;
drop table if exists background_themes cascade;
drop table if exists barcode_type cascade;
drop table if exists branch cascade;
drop table if exists brand_profile cascade;
drop table if exists cache_data cascade;
drop table if exists category_group cascade;
drop table if exists clickatell_configuration cascade;
drop table if exists clipboard_content cascade;
drop table if exists columnar_indicators cascade;
drop table if exists comm_ext_proof_validation_map cascade;
drop table if exists comm_mini_prod_event_communications cascade;
drop table if exists comm_prod_event_communications cascade;
drop table if exists comm_zone_content_association cascade;
drop table if exists communication cascade;
drop table if exists communication_mini_prod_event cascade;
drop table if exists communication_production_event cascade;
drop table if exists communication_proof cascade;
drop table if exists complex_value cascade;
drop table if exists complex_value_content_object cascade;
drop table if exists complex_value_data_elements cascade;
drop table if exists complex_value_lookup_tables cascade;
drop table if exists complex_value_variable cascade;
drop table if exists composition_additional_files cascade;
drop table if exists composition_file_set cascade;
drop table if exists compound_key cascade;
drop table if exists compound_key_item cascade;
drop table if exists condition_element cascade;
drop table if exists condition_item cascade;
drop table if exists condition_item_attrib cascade;
drop table if exists condition_item_value cascade;
drop table if exists condition_operator cascade;
drop table if exists condition_param_map cascade;
drop table if exists condition_sub_attrib cascade;
drop table if exists condition_subelement cascade;
drop table if exists condition_type cascade;
drop table if exists configurable_link cascade;
drop table if exists connected_tp_sel_visible_user cascade;
drop table if exists connection_resource cascade;
drop table if exists content cascade;
drop table if exists content_assistant cascade;
drop table if exists content_assistant_target_locales cascade;
drop table if exists content_assistant_target_tps cascade;
drop table if exists content_content_object_type cascade;
drop table if exists content_content_targeting cascade;
drop table if exists content_images cascade;
drop table if exists content_list_style cascade;
drop table if exists content_object cascade;
drop table if exists content_object_association cascade;
drop table if exists content_object_comment cascade;
drop table if exists content_object_data cascade;
drop table if exists content_object_document_map cascade;
drop table if exists content_object_tg_excluded_map cascade;
drop table if exists content_object_tg_extended_map cascade;
drop table if exists content_object_tg_included_map cascade;
drop table if exists content_object_tg_instance_map cascade;
drop table if exists content_object_tpc_map cascade;
drop table if exists content_object_zone_priority cascade;
drop table if exists content_paragraph_style cascade;
drop table if exists content_placeholders cascade;
drop table if exists content_target_group_excluded cascade;
drop table if exists content_target_group_extended cascade;
drop table if exists content_target_group_included cascade;
drop table if exists content_targeting cascade;
drop table if exists content_text_style cascade;
drop table if exists content_tg_instance_map cascade;
drop table if exists content_type cascade;
drop table if exists content_variable cascade;
drop table if exists daily_frequency_type cascade;
drop table if exists dashboard_filters cascade;
drop table if exists data_comparison cascade;
drop table if exists data_element_core cascade;
drop table if exists data_element_flat cascade;
drop table if exists data_element_json cascade;
drop table if exists data_element_variable cascade;
drop table if exists data_element_xml cascade;
drop table if exists data_expiration_schedule cascade;
drop table if exists data_file cascade;
drop table if exists data_file_document cascade;
drop table if exists data_file_preview_languages cascade;
drop table if exists data_file_tpc cascade;
drop table if exists data_group cascade;
drop table if exists data_record cascade;
drop table if exists data_record_level cascade;
drop table if exists data_resource cascade;
drop table if exists data_source cascade;
drop table if exists data_source_association cascade;
drop table if exists data_subtype cascade;
drop table if exists data_subtype_comparison cascade;
drop table if exists data_type cascade;
drop table if exists data_type_subtype cascade;
drop table if exists database_file cascade;
drop table if exists date_data_value cascade;
drop table if exists de_state_transition cascade;
drop table if exists deactivate_types cascade;
drop table if exists delimited_indicators cascade;
drop table if exists delivery_event cascade;
drop table if exists delivery_event_metadata cascade;
drop table if exists delivery_event_schedule cascade;
drop table if exists delivery_event_type cascade;
drop table if exists deserver cascade;
drop table if exists deserver_bundle_type_state cascade;
drop table if exists deserver_communicationtype cascade;
drop table if exists dev_de_map cascade;
drop table if exists dev_ds_hash_map cascade;
drop table if exists dialogue_configuration cascade;
drop table if exists dialogue_data_mapping cascade;
drop table if exists dialogue_metadata_tags cascade;
drop table if exists dictionary cascade;
drop table if exists document cascade;
drop table if exists document_history cascade;
drop table if exists document_preview cascade;
drop table if exists document_production_event cascade;
drop table if exists document_section cascade;
drop table if exists document_tenant_email_address cascade;
drop table if exists domain cascade;
drop table if exists domain_pod cascade;
drop table if exists emessaging_configuration cascade;
drop table if exists encoding_type cascade;
drop table if exists event_type cascade;
drop table if exists exact_target_last_query cascade;
drop table if exists exacttarget_configuration cascade;
drop table if exists ext_reporting_vars_doc cascade;
drop table if exists external_event cascade;
drop table if exists external_proof_validation cascade;
drop table if exists fileroot_man_profile cascade;
drop table if exists filter_condition cascade;
drop table if exists filter_condition_value_map cascade;
drop table if exists folder_insert cascade;
drop table if exists folders cascade;
drop table if exists formatting_selection cascade;
drop table if exists frequency_type cascade;
drop table if exists ftp_configuration cascade;
drop table if exists generic_configuration cascade;
drop table if exists gmc_configuration cascade;
drop table if exists hash_alias cascade;
drop table if exists hist_content cascade;
drop table if exists hist_content_content_object_t cascade;
drop table if exists hist_content_content_targeting cascade;
drop table if exists hist_content_images cascade;
drop table if exists hist_content_list_style cascade;
drop table if exists hist_content_obj_association cascade;
drop table if exists hist_content_object_data cascade;
drop table if exists hist_content_paragraph_style cascade;
drop table if exists hist_content_placeholders cascade;
drop table if exists hist_content_text_style cascade;
drop table if exists hist_content_variable cascade;
drop table if exists hist_meta_form_item_value_set cascade;
drop table if exists hist_metadata_form cascade;
drop table if exists hist_metadata_form_item cascade;
drop table if exists hist_rat_shared_content cascade;
drop table if exists hist_rationalizer_content cascade;
drop table if exists insert_document cascade;
drop table if exists insert_obj cascade;
drop table if exists insert_rate_schedule cascade;
drop table if exists insert_schedule cascade;
drop table if exists insert_schedule_bin_assignment cascade;
drop table if exists insert_schedule_collection cascade;
drop table if exists insert_target_group_excluded cascade;
drop table if exists insert_target_group_extended cascade;
drop table if exists insert_target_group_included cascade;
drop table if exists insert_tg_instance_map cascade;
drop table if exists item_type cascade;
drop table if exists job cascade;
drop table if exists json_data_definition cascade;
drop table if exists language_content_hash cascade;
drop table if exists language_content_hash_map cascade;
drop table if exists language_selection cascade;
drop table if exists layout_type cascade;
drop table if exists licence cascade;
drop table if exists licence_application cascade;
drop table if exists licence_attributes cascade;
drop table if exists licence_audit cascade;
drop table if exists licence_audit_action cascade;
drop table if exists licence_change_reasons cascade;
drop table if exists licence_customer cascade;
drop table if exists licence_customer_keypair cascade;
drop table if exists licence_history cascade;
drop table if exists licence_history_reason cascade;
drop table if exists licence_module cascade;
drop table if exists licence_representative cascade;
drop table if exists licence_resource cascade;
drop table if exists licence_server cascade;
drop table if exists licence_server_ip_address cascade;
drop table if exists list_style cascade;
drop table if exists list_style_cust cascade;
drop table if exists list_style_cust_map cascade;
drop table if exists lookup_table cascade;
drop table if exists lookup_table_document cascade;
drop table if exists lookup_table_instance cascade;
drop table if exists lookup_table_tpc cascade;
drop table if exists lookup_table_version_map cascade;
drop table if exists lookup_value cascade;
drop table if exists message_delivery_report cascade;
drop table if exists message_simulation_report cascade;
drop table if exists messagepoint_locale cascade;
drop table if exists meta_form_item_def_con_ele_map cascade;
drop table if exists metadata_form cascade;
drop table if exists metadata_form_definition cascade;
drop table if exists metadata_form_item cascade;
drop table if exists metadata_form_item_definition cascade;
drop table if exists metadata_form_item_value_set cascade;
drop table if exists metadata_points_of_interest cascade;
drop table if exists mp_channel cascade;
drop table if exists mp_connector cascade;
drop table if exists mp_licence cascade;
drop table if exists mp_qualification_output cascade;
drop table if exists mphcs_configuration cascade;
drop table if exists msg_delivery_report_doc cascade;
drop table if exists msg_delivery_report_msg cascade;
drop table if exists msg_simulation_report_doc cascade;
drop table if exists msg_simulation_report_msg cascade;
drop table if exists native_comp_configuration cascade;
drop table if exists navigation_drop_down_menu cascade;
drop table if exists navigation_drop_down_menu_item cascade;
drop table if exists navigation_menu_item_perm cascade;
drop table if exists navigation_tab_default_map cascade;
drop table if exists navigation_tab_permission cascade;
drop table if exists navigation_tabs cascade;
drop table if exists navigation_tree cascade;
drop table if exists node cascade;
drop table if exists notification_email cascade;
drop table if exists notification_settings cascade;
drop table if exists notification_settings_tp cascade;
drop table if exists object_wf_action_map cascade;
drop table if exists oidc_signing_keys cascade;
drop table if exists old_workflow cascade;
drop table if exists operations_report_doc cascade;
drop table if exists operations_report_event cascade;
drop table if exists operations_report_scenario cascade;
drop table if exists order_entry_def_con_ele_map cascade;
drop table if exists order_entry_item cascade;
drop table if exists order_entry_item_con_ele_map cascade;
drop table if exists order_entry_item_definition cascade;
drop table if exists order_item_def_selections cascade;
drop table if exists order_item_selections cascade;
drop table if exists para_style_cust cascade;
drop table if exists para_style_cust_line_spacings cascade;
drop table if exists para_style_cust_map cascade;
drop table if exists paragraph_style cascade;
drop table if exists paragraph_style_line_spacings cascade;
drop table if exists parameter cascade;
drop table if exists parameter_group cascade;
drop table if exists parameter_group_instance cascade;
drop table if exists parameter_group_item cascade;
drop table if exists password_history cascade;
drop table if exists password_recovery cascade;
drop table if exists permission cascade;
drop table if exists permission_category cascade;
drop table if exists pg_instance_collection cascade;
drop table if exists pg_tree_node cascade;
drop table if exists pod cascade;
drop table if exists pod_status cascade;
drop table if exists pod_type cascade;
drop table if exists project cascade;
drop table if exists project_applied_wf_steps cascade;
drop table if exists project_step_req_note_map cascade;
drop table if exists project_task cascade;
drop table if exists project_wf_step_user_map cascade;
drop table if exists proof cascade;
drop table if exists proof_definition cascade;
drop table if exists rat_hist_shared_content cascade;
drop table if exists rat_hist_shared_content_form cascade;
drop table if exists rate_schedule cascade;
drop table if exists rate_schedule_collection cascade;
drop table if exists rate_schedule_detail cascade;
drop table if exists rationalizer_app_nav_tree cascade;
drop table if exists rationalizer_app_tp_reference cascade;
drop table if exists rationalizer_app_visible_user cascade;
drop table if exists rationalizer_application cascade;
drop table if exists rationalizer_document cascade;
drop table if exists rationalizer_document_content cascade;
drop table if exists rationalizer_filter_item_defs cascade;
drop table if exists rationalizer_hist_content cascade;
drop table if exists rationalizer_hist_form cascade;
drop table if exists rationalizer_query cascade;
drop table if exists rationalizer_query_component cascade;
drop table if exists rationalizer_shared_content cascade;
drop table if exists record_type cascade;
drop table if exists redirection_info cascade;
drop table if exists reference_connection cascade;
drop table if exists reference_query cascade;
drop table if exists report_scenario cascade;
drop table if exists report_scenario_delivery_event cascade;
drop table if exists report_scenario_message cascade;
drop table if exists rest_api_token cascade;
drop table if exists role cascade;
drop table if exists role_permission cascade;
drop table if exists sandbox_file cascade;
drop table if exists search_result_ids cascade;
drop table if exists security_settings cascade;
drop table if exists sefas_configuration cascade;
drop table if exists seg_analysis_analyzables cascade;
drop table if exists segmentation_analysis cascade;
drop table if exists sendmail_configuration cascade;
drop table if exists services cascade;
drop table if exists simulation cascade;
drop table if exists simulation_content_object cascade;
drop table if exists source_type cascade;
drop table if exists status_polling_background_task cascade;
drop table if exists sub_content_type cascade;
drop table if exists sync_history cascade;
drop table if exists sync_history_locales cascade;
drop table if exists sync_object cascade;
drop table if exists sync_process cascade;
drop table if exists sync_request cascade;
drop table if exists sync_worker cascade;
drop table if exists system_notification cascade;
drop table if exists system_property cascade;
drop table if exists system_state cascade;
drop table if exists system_theme cascade;
drop table if exists tag cascade;
drop table if exists tag_cloud cascade;
drop table if exists tag_cloud_type cascade;
drop table if exists tag_content_object cascade;
drop table if exists tag_document cascade;
drop table if exists tag_insert cascade;
drop table if exists tag_target_group_excluded cascade;
drop table if exists tag_target_group_extended cascade;
drop table if exists tag_target_group_included cascade;
drop table if exists tag_tg_instance_map cascade;
drop table if exists tag_touchpoint_collection cascade;
drop table if exists tag_type cascade;
drop table if exists target_group cascade;
drop table if exists target_group_document cascade;
drop table if exists target_group_instance cascade;
drop table if exists task cascade;
drop table if exists task_document cascade;
drop table if exists task_item cascade;
drop table if exists task_user cascade;
drop table if exists template_modifier cascade;
drop table if exists template_variant cascade;
drop table if exists tenant_metadata cascade;
drop table if exists tenant_permission cascade;
drop table if exists tenant_theme_info cascade;
drop table if exists tenants cascade;
drop table if exists test_scenario cascade;
drop table if exists test_scenario_content_object cascade;
drop table if exists test_suite cascade;
drop table if exists text_style cascade;
drop table if exists text_style_colors cascade;
drop table if exists text_style_cust cascade;
drop table if exists text_style_cust_colors cascade;
drop table if exists text_style_cust_map cascade;
drop table if exists text_style_cust_point_sizes cascade;
drop table if exists text_style_font cascade;
drop table if exists text_style_point_sizes cascade;
drop table if exists text_style_transformation_profile cascade;
drop table if exists touchpoint_collection cascade;
drop table if exists touchpoint_language cascade;
drop table if exists touchpoint_locale cascade;
drop table if exists touchpoint_selection cascade;
drop table if exists touchpoint_selection_workgroup cascade;
drop table if exists touchpoint_targeting cascade;
drop table if exists touchpoint_tg_instance_map cascade;
drop table if exists tp_collection_touchpoint cascade;
drop table if exists tp_delivery_report_doc cascade;
drop table if exists tp_delivery_report_event cascade;
drop table if exists tp_delivery_report_scenario cascade;
drop table if exists tp_instance_locales cascade;
drop table if exists tp_instance_visibility_map cascade;
drop table if exists tp_sel_visible_user cascade;
drop table if exists tp_target_group_excluded cascade;
drop table if exists tp_target_group_extended cascade;
drop table if exists tp_target_group_included cascade;
drop table if exists translation_provider cascade;
drop table if exists tstp_language cascade;
drop table if exists user_permission cascade;
drop table if exists user_role cascade;
drop table if exists users cascade;
drop table if exists variable_data_element_map cascade;
drop table if exists variable_document cascade;
drop table if exists version_activity_reason cascade;
drop table if exists version_status cascade;
drop table if exists web_service_config cascade;
drop table if exists workflow cascade;
drop table if exists workflow_action cascade;
drop table if exists workflow_action_history cascade;
drop table if exists workflow_instance cascade;
drop table if exists workflow_library_document cascade;
drop table if exists workflow_position cascade;
drop table if exists workflow_position_permission cascade;
drop table if exists workflow_position_user cascade;
drop table if exists workflow_property cascade;
drop table if exists workflow_state cascade;
drop table if exists workflow_step cascade;
drop table if exists workflow_step_langs cascade;
drop table if exists workflow_step_subwfs cascade;
drop table if exists workflow_tab cascade;
drop table if exists workflow_tab_permission cascade;
drop table if exists workgroup cascade;
drop table if exists workgroup_zone cascade;
drop table if exists xml_data_tag_definition cascade;
drop table if exists zone cascade;
drop table if exists zone_attributes cascade;
drop table if exists zone_image_assets cascade;
drop table if exists zone_list_style cascade;
drop table if exists zone_paragraph_style cascade;
drop table if exists zone_part cascade;
drop table if exists zone_smart_text_assets cascade;
drop table if exists zone_style cascade;
drop sequence if exists hibernate_sequence;
create table action_entry (id int8 not null, text_style_from_id int8, text_style_to_id int8, attributes varchar(512), item_order int4 not null, tstp_id int8, primary key (id));
create table aggregation_operator (id int4 not null, operator varchar(255) not null, primary key (id));
create table all_reference_query (id int8 not null, object_class_name varchar(255) not null, direct_ref_class_name varchar(255), f_query varchar(4000), f_type varchar(8), primary key (id));
create table app_version (id int8 not null, version_key varchar(255), version_value varchar(255), updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table application_locale (id int8 not null, mp_locale_id int8, enable boolean not null, accessible boolean not null, primary key (id));
create table approval (id int8 not null, model_type int4 not null, model_id int8 not null, model_guid varchar(255), model_data_guid varchar(255), user_id int8, notes varchar(255), state_id int8, step_name varchar(255), approved boolean default false not null, valid boolean default true not null, due_date timestamp, updated timestamp, updated_by_id int8, created timestamp not null, created_by_id int8, primary key (id));
create table approval_detail (id int8 not null, wf_action_id int8, user_id int8, approved_date timestamp, approved int4 not null, created timestamp, notes varchar(255), is_owner boolean default false not null, primary key (id));
create table approval_user (wf_step_id int8 not null, user_id int8 not null, primary key (wf_step_id, user_id));
create table attachment (id int8 not null, guid varchar(255) not null, name varchar(96) not null, recipient_name_value_id int8, recipient_location_value_id int8, delivery_type_id int4, origin_object_id int8, dna varchar(255) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, include_tg_relation int4, excluded_tg_relation int4, extended_tg_relation int4, primary key (id));
create table attachment_document (attachment_id int8 not null, document_id int8 not null, primary key (attachment_id, document_id));
create table attachment_tg_instance_map (tg_id int8 not null, attachment_id int8 not null, instance_id int8 not null, primary key (tg_id, attachment_id));
create table attchmnt_target_group_excluded (attachment_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (attachment_id, sequence));
create table attchmnt_target_group_extended (attachment_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (attachment_id, sequence));
create table attchmnt_target_group_included (attachment_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (attachment_id, sequence));
create table audit_event (id int8 not null, timestamp timestamp, user_id int8, event_type int4, object_type int4, object_label varchar(255), object_id int8, action_type int4, metadata text, primary key (id));
create table audit_event_doc (audit_event_id int8 not null, document_id int8 not null, primary key (audit_event_id, document_id));
create table audit_report (id int8 not null, request_date timestamp, user_id int8, type_id int4, status_id int4, xml_path varchar(255), report_path varchar(255), guid varchar(255) not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, external_event_id int8, primary key (id));
create table background_themes (id int8 not null, name varchar(96) not null, filename varchar(255) not null, primary key (id));
create table barcode_type (id int8 not null, name varchar(96), description varchar(255), sample_image varchar(255), enabled boolean, type int4, primary key (id));
create table branch (id int8 not null, guid varchar(255) not null, branch_code varchar(4) not null, name varchar(96) not null, friendly_name varchar(255), dcs_schema_name varchar(255), status int4 not null, parent_id int8, branch_type_id int4, enabled boolean not null, sso_type int4 not null, sso_idp_id varchar(255), sso_secret_key varchar(255), sso_error_page_url varchar(255), sso_logout_page_url varchar(255), sso_autousercreation_enabled boolean not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table brand_profile (id int8 not null, guid varchar(255) not null, name varchar(255) not null, primary_profile boolean not null, apply_restricted_terms boolean not null, restricted_terms_definition text, apply_preferred_contractions boolean not null, preferred_contractions_def text, apply_restricted_contractions boolean not null, restricted_contractions_def text, apply_max_sentence_length boolean not null, max_sentence_length int4, apply_single_bullet_detection boolean not null, apply_single_number_detection boolean not null, apply_compound_space_detection boolean not null, apply_lead_space_detection boolean not null, apply_url_format boolean not null, url_format int4 not null, apply_phone_number_format boolean not null, phone_number_format varchar(255), enforce_legal_mark boolean not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table cache_data (id int8 not null, master_id int8, master_guid varchar(400), alternate_id int8, alternate_guid varchar(400), json_data text not null, type varchar(255) not null, object_id int8, api_version int8 not null, version int8 not null, primary key (id));
create table category_group (category_group_id int8 not null, name varchar(96), description varchar(255), priority int4 not null, primary key (category_group_id));
create table clickatell_configuration (id int8 not null, created timestamp, updated timestamp, connector_id int8, qualification_output_id int8, sha256_hash varchar(90), customer_driver_file varchar(255), execute_incloud_test boolean, execute_incloud_preview boolean, execute_incloud_proof boolean, execute_incloud_simulation boolean, pre_qualengine_script varchar(255), post_qualengine_script varchar(255), post_connector_script varchar(255), bundle_filenames_for_images boolean not null, filename_separator_for_images varchar(255), play_messages_on_empty_var boolean not null, override_remote_server boolean, remote_server_ip varchar(255), remote_server_port varchar(255), remote_server_user varchar(255), remote_server_password varchar(255), applied_de_version varchar(255), input_character_encoding int4, output_character_encoding int4, de_server_guid varchar(255), bundle_name_override varchar(255), output_filename_cplex_val_id int8, output_doc_title_cplex_val_id int8, validate_prod_bundle boolean, escape_driver_data_tags boolean not null, convert_table_border_px_to_pt boolean not null, eval_not_equal_on_missing_tag boolean not null, play_empty_agg_first_last_var boolean not null, remove_zero_from_style_con boolean not null, comp_time_parent_tagging boolean not null, data_group_expression_var_proc boolean not null, list_style_control_type_id int4, color_output_format_type_id int4, script_var_applies_undefined boolean not null, correct_paragraph_text_styles boolean not null, fix_inline_targeting_styles boolean not null, preserve_data_whitespace boolean not null, nbsp_composed_as_space boolean not null, normalize_image_library boolean not null, normalize_embedded_content boolean not null, gmc_span_to_t_tag boolean not null, blue_underline_links boolean not null, use_default_image int4, updated_by_id int8, customer_phone_number_var_id int8, primary key (id));
create table clipboard_content (id int8 not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, metatags varchar(512), is_shared boolean not null, owner_id int8, workgroup_id int8, content_id int8, primary key (id));
create table columnar_indicators (data_source_id int8 not null, list_order int4 not null, indicator_start int4 not null, indicator_length int4 not null, primary key (data_source_id, list_order));
create table comm_ext_proof_validation_map (communication_id int8 not null, communication_proof_id int8 not null, ext_proof_validation_id int8 not null, primary key (communication_id, communication_proof_id));
create table comm_mini_prod_event_communications (comm_prod_event_id int8 not null, communication_id int8 not null, primary key (comm_prod_event_id, communication_id));
create table comm_prod_event_communications (comm_prod_event_id int8 not null, communication_id int8 not null, primary key (comm_prod_event_id, communication_id));
create table comm_zone_content_association (id int8 not null, communication_id int8, zone_id int8, content_id int8, ref_image_library_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, guid varchar(255) not null, sha256_hash varchar(90), primary key (id));
create table communication (id int8 not null, guid varchar(255) not null, customerIdentifier varchar(4000), messagepoint_locale_id int8, metatags varchar(255), document_id int8 not null, channel_context_id int8, tp_selection_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, assignee_id int8, wf_action_id int8, last_production_date timestamp, polling_prod_status_result boolean not null, prod_status_results varchar(4000), number_of_copies int4, test_order boolean not null, debug_order boolean not null, debug_reference_data text, debug_package text, audit_data text, interview_configuration text, wf_pending_proof_timestamp timestamp, supporting_data text, indicator varchar(4000), last_lock_time timestamp, pmgr_order_uuid varchar(255), pmgr_preproof_uuid varchar(255), pmgr_proof_uuid varchar(255), primary key (id));
create table communication_mini_prod_event (id int8 not null, name varchar(96), guid varchar(255) not null, enabled boolean, document_id int8, tp_collection_id int8, delivery_event_type_id int4, delivery_event_schedule_id int8, last_scheduler_rundate timestamp, output_filename varchar(255), output_path varchar(255), updated timestamp, created timestamp, completed_date timestamp, request_date timestamp, updated_by_id int8, batch_window_start_date timestamp, batch_window_end_date timestamp, generate_new_bundle boolean, complete boolean, error boolean, primary key (id));
create table communication_production_event (id int8 not null, name varchar(96), guid varchar(255) not null, enabled boolean, document_id int8, tp_collection_id int8, delivery_event_type_id int4, delivery_event_schedule_id int8, last_scheduler_rundate timestamp, output_filename varchar(255), output_path varchar(255), updated timestamp, created timestamp, completed_date timestamp, request_date timestamp, updated_by_id int8, batch_window_start_date timestamp, batch_window_end_date timestamp, complete boolean, error boolean, primary key (id));
create table communication_proof (id int8 not null, communication_id int8, no_production_content_error boolean, no_matching_recipient_error boolean, pre_proof_document_id int8, pre_proof_customer_identifier varchar(4000), pre_proof_reference_data text, proof_email_recipient varchar(255), is_pre_proof boolean, applies_web_service boolean not null, web_service_result_complete boolean not null, output_filename varchar(255), output_path varchar(255), updated timestamp, created timestamp, completed_date timestamp, request_date timestamp, updated_by_id int8, user_id int8, complete boolean, error boolean, channel_context_id int8, primary key (id));
create table complex_value (id int8 not null, guid varchar(255) not null, value text, origin_object_id int8, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, sha256_hash varchar(90), primary key (id));
create table complex_value_content_object (complex_value_id int8 not null, content_object_id int8 not null, primary key (complex_value_id, content_object_id));
create table complex_value_data_elements (complex_value_id int8 not null, data_element_id int8 not null, primary key (complex_value_id, data_element_id));
create table complex_value_lookup_tables (complex_value_id int8 not null, lookup_table_id int8 not null, primary key (complex_value_id, lookup_table_id));
create table complex_value_variable (complex_value_id int8 not null, variable_id int8 not null, primary key (complex_value_id, variable_id));
create table composition_additional_files (composition_file_set_id int8 not null, database_file_id int8 not null, primary key (composition_file_set_id, database_file_id));
create table composition_file_set (id int8 not null, name varchar(96), template_file_name varchar(512), composition_config_file_name varchar(512), created timestamp, updated timestamp, updated_by_id int8, document_id int8, tp_collection_id int8, file_upload_sync_key varchar(96), dna varchar(255) not null, sha256_hash varchar(90), primary key (id));
create table compound_key (id int8 not null, guid varchar(255) not null, sha256_hash varchar(90), updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table compound_key_item (id int8 not null, guid varchar(255) not null, compound_key_id int8, variable_id int8, item_order int4 not null, primary key (id));
create table condition_element (id int8 not null, name varchar(255), guid varchar(255) not null, metatags varchar(255), search_value text, condition_type_id int4, use_default_value boolean not null, default_value boolean not null, updated timestamp, updated_by_id int8, created timestamp, sha256_hash varchar(90), primary key (id));
create table condition_item (id int8 not null, target_group_id int8, condition_element_id int8, guid varchar(255) not null, primary key (id));
create table condition_item_attrib (civ_id int8 not null, attribute_name varchar(255) not null, attribute_value varchar(4000), primary key (civ_id, attribute_name));
create table condition_item_value (id int8 not null, condition_item_id int8, condition_subelement_id int8, primary key (id));
create table condition_operator (id int4 not null, operator varchar(255) not null, primary key (id));
create table condition_param_map (tgi_id int8 not null, condition_item_id int8 not null, param_value boolean, primary key (tgi_id, condition_item_id));
create table condition_sub_attrib (cs_id int8 not null, attribute_name varchar(255) not null, attribute_value varchar(4000), primary key (cs_id, attribute_name));
create table condition_subelement (id int8 not null, parameterized boolean, condition_element_id int8, condition_type_id int4, name varchar(96), guid varchar(255) not null, data_element_variable_id int8, condition_operator_id int4, data_element_comparison_id int8, data_file_path varchar(512), sequence int4, primary key (id));
create table condition_type (id int4 not null, name varchar(96), parameterized boolean default true, description varchar(255), primary key (id));
create table configurable_link (id int8 not null, label varchar(20) not null, link varchar(400) not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table connected_tp_sel_visible_user (tp_selection_id int8 not null, user_id int8 not null, primary key (tp_selection_id, user_id));
create table connection_resource (id int8 not null, guid varchar(255) not null, sha256_hash varchar(90), remote_reference_df_path varchar(255), data_resource_id int8, reference_data_file_id int8, reference_connection_id int8 not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table content (id int8 not null, guid varchar(255) not null, sha256_hash varchar(90), text_content text, unformatted_text_content text, image_location varchar(512), image_name varchar(255), image_uploaded timestamp, applied_image_filename varchar(40), asset_id varchar(255), asset_url varchar(2048), asset_site varchar(255), asset_last_update timestamp, asset_last_sync timestamp, image_link int8, image_alt_text int8, image_ext_link int8, image_ext_path int8, dirty boolean, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table content_assistant (id int8 not null, name varchar(255), query text, enabled boolean, trigger_criteria varchar(255), system_controlled boolean, content_selector varchar(255), batch_throttle int4, target_all_touchpoints boolean, target_all_objects boolean, target_objects varchar(255), target_objects_status varchar(255), target_all_locales boolean, primary key (id));
create table content_assistant_target_locales (content_assistant_id int8 not null, locale_id int8 not null, primary key (content_assistant_id, locale_id));
create table content_assistant_target_tps (content_assistant_id int8 not null, document_id int8 not null, primary key (content_assistant_id, document_id));
create table content_content_object_type (content_id int8 not null, content_object_id int8 not null, object_type int4, primary key (content_id, content_object_id));
create table content_content_targeting (content_id int8 not null, content_targeting_id int8 not null, primary key (content_id, content_targeting_id));
create table content_images (content_id int8 not null, database_file_id int8 not null, primary key (content_id, database_file_id));
create table content_list_style (content_id int8 not null, list_style_id int8 not null, primary key (content_id, list_style_id));
create table content_object (id int8 not null, guid varchar(255) not null, sha256_hash varchar(90), name varchar(256) not null, description varchar(255), object_type int4 not null, content_type_id int8, graphic_type_id int4, channel_context_id int8, usage_type_id int4, advanced boolean, zone_id int8, document_id int8, tp_selection_id int8, last_lock_time timestamp, removed boolean, suppressed boolean, on_hold boolean, structured_content_enabled boolean, variable_content_enabled boolean, origin_object_id int8, locked_for_user_id int8, updated timestamp, updated_by_user_id int8, created timestamp, created_by_user_id int8, global_parent_object_id int8, last_global_po_sync_date timestamp, dna varchar(255) not null, ready_for_approval boolean, state_id int8, primary key (id));
create table content_object_association (id int8 not null, type_id int4, content_object_id int8, data_type int4, co_pg_tn_id int8, ref_co_pg_tn_id int8, tp_pg_tn_id int8, ref_tp_pg_tn_id int8, zone_part_id int8, messagepoint_locale_id int8, ref_messagepoint_locale_id int8, content_id int8, ref_image_library_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, guid varchar(255) not null, sha256_hash varchar(90), primary key (id));
create table content_object_comment (id int8 not null, user_id int8, comment_data varchar(1000), created timestamp, content_object_id int8, data_type int4, primary key (id));
create table content_object_data (content_object_id int8 not null, data_type int4 not null, guid varchar(255) not null, sha256_hash varchar(90), dna varchar(255) not null, updated timestamp, updated_by_user_id int8, created timestamp, created_by_user_id int8, parameter_group_id int8, start_date timestamp, end_date timestamp, repeat_dates_annually boolean, delivery_type int4, flow_type_id int4 not null, data_group_id int8, data_group_comp_var_format_type int4, metatags varchar(255), status_id int8 not null, comp_var_format_type int4, insert_as_block_content boolean not null, content_trim_type int4, segmentation_data varchar(4000), canvas_max_width int4, canvas_max_height int4, canvas_trim_width int4, canvas_trim_height int4, supports_tables boolean not null, supports_forms boolean not null, supports_barcodes boolean not null, supports_content_menus boolean not null, keep_content_together boolean not null, render_as_tagged_text boolean not null, metadata_form_id int8, creation_user_note varchar(255), creation_reason_id int8, include_tg_relation int4, excluded_tg_relation int4, extended_tg_relation int4, starts_front_facing boolean, starts_front_facing_every boolean, language_content_hash_id int8, attributes_hash varchar(90), primary key (content_object_id, data_type));
create table content_object_document_map (content_object_id int8 not null, document_id int8 not null, primary key (content_object_id, document_id));
create table content_object_tg_excluded_map (content_object_id int8 not null, data_type int4 not null, sequence int4 not null, target_group_id int8 not null, primary key (content_object_id, data_type, sequence));
create table content_object_tg_extended_map (content_object_id int8 not null, data_type int4 not null, sequence int4 not null, target_group_id int8 not null, primary key (content_object_id, data_type, sequence));
create table content_object_tg_included_map (content_object_id int8 not null, data_type int4 not null, sequence int4 not null, target_group_id int8 not null, primary key (content_object_id, data_type, sequence));
create table content_object_tg_instance_map (tg_id int8 not null, content_object_id int8 not null, data_type int4 not null, instance_id int8 not null, primary key (tg_id, content_object_id, data_type));
create table content_object_tpc_map (content_object_id int8 not null, tp_collection_id int8 not null, primary key (content_object_id, tp_collection_id));
create table content_object_zone_priority (id int8 not null, update_time timestamp not null, guid varchar(255) not null, content_object_id int8, data_type int4, zone_id int8, touchpoint_selection_id int8, content_object_priority int8, is_suppress boolean not null, is_repeat_with_next boolean not null, create_time timestamp, created_by int8, updated_by int8, primary key (id));
create table content_paragraph_style (content_id int8 not null, paragraph_style_id int8 not null, primary key (content_id, paragraph_style_id));
create table content_placeholders (content_id int8 not null, placeholder_id int8 not null, primary key (content_id, placeholder_id));
create table content_target_group_excluded (content_targeting_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (content_targeting_id, sequence));
create table content_target_group_extended (content_targeting_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (content_targeting_id, sequence));
create table content_target_group_included (content_targeting_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (content_targeting_id, sequence));
create table content_targeting (id int8 not null, guid varchar(255) not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, include_tg_relation int4, excluded_tg_relation int4, extended_tg_relation int4, primary key (id));
create table content_text_style (content_id int8 not null, style_id int8 not null, primary key (content_id, style_id));
create table content_tg_instance_map (tg_id int8 not null, content_targeting_id int8 not null, instance_id int8 not null, primary key (tg_id, content_targeting_id));
create table content_type (id int8 not null, name varchar(96), description varchar(255), primary key (id));
create table content_variable (content_id int8 not null, variable_id int8 not null, primary key (content_id, variable_id));
create table daily_frequency_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table dashboard_filters (id int8 not null, keyword varchar(255) not null, applies_to varchar(255), object_id int8, section varchar(255), type varchar(255), operation varchar(255), value varchar(255), primary key (id));
create table data_comparison (id int8 not null, name varchar(96), description varchar(255), scheme varchar(255), primary key (id));
create table data_element_core (id int8 not null, data_element_type varchar(8) not null, name varchar(255), variable_external_id int8, external_format_text varchar(255), decimal_places int4, data_type_id int4, data_subtype_id int4, data_image_type_id int4, anonymized boolean not null, anonymization_type_id int4, anonymization_custom_masks text, anonymization_min_length int4, anonymization_max_length int4, updated timestamp, created timestamp, updated_by_id int8, created_by_id int8, xml_data_tag_definition_id int8, data_record_id int8, dna varchar(255) not null, primary key (id));
create table data_element_flat (data_element_id int8 not null, start_location int8, length int8, primary key (data_element_id));
create table data_element_json (data_element_id int8 not null, primary key (data_element_id));
create table data_element_variable (id int8 not null, name varchar(255), metatags varchar(255), guid varchar(255) not null, dna varchar(255) not null, updated timestamp, created timestamp, friendly_name varchar(255), enabled_for_content boolean, enabled_for_rules boolean, enabled_for_connected boolean, enabled_for_connected_interview boolean, enabled_for_markup_content boolean, parameter_id int8, expression int8, script int8, sql_expression varchar(4000), type_id int4 not null, system_variable_type_id int4, updated_by_id int8, default_value varchar(255), sample_value varchar(4000), external_id varchar(255), is_reference_variable boolean not null, non_driver_agg_op_id int4, non_driver_data_group_id int8, sha256_hash varchar(90), primary key (id));
create table data_element_xml (data_element_id int8 not null, is_attribute boolean, attribute_name varchar(256), primary key (data_element_id));
create table data_expiration_schedule (id int8 not null, name varchar(255) not null, enabled boolean not null, duration int4, created timestamp not null, updated timestamp, primary key (id));
create table data_file (id int8 not null, name varchar(255), description varchar(4000), filename varchar(255), source_type_id int4 default 1 not null, created timestamp, updated timestamp, updated_by_id int8, remote_df_path varchar(255), dna varchar(255) not null, sha256_hash varchar(90), primary key (id));
create table data_file_document (document_id int8 not null, data_file_id int8 not null, primary key (data_file_id, document_id));
create table data_file_preview_languages (id int8 not null, customer_number varchar(255) not null, language varchar(3) not null, data_file_id int8, primary key (id));
create table data_file_tpc (data_file_id int8 not null, tp_collection_id int8 not null, primary key (data_file_id, tp_collection_id));
create table data_group (id int8 not null, name varchar(255) not null, data_source_id int8, break_data_record_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table data_record (id int8 not null, data_source_id int8, parent_data_record_id int8, record_indicator varchar(512), record_position int4, start_customer boolean, break_indicator varchar(32), start_data_group boolean, repeating boolean, enabled boolean, updated timestamp, data_group_id int8, dna varchar(255) not null, primary key (id));
create table data_record_level (id int4 not null, name varchar(96), f_value int4, primary key (id));
create table data_resource (id int8 not null, guid varchar(255) not null, name varchar(96) not null, metatags varchar(255), sha256_hash varchar(90), document_id int8, primary_data_file_id int8, remote_primary_df_path varchar(255), origin_object_id int8, dna varchar(255) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, tp_collection_id int8, primary key (id));
create table data_source (id int8 not null, guid varchar(255) not null, external_id varchar(255), customer_driver_file boolean, delimeter varchar(255), name varchar(255), headers int4, enabled boolean, updated timestamp, encoding_type_id int4, layout_type_id int4, source_type_id int4, record_type_id int4, dna varchar(255) not null, sha256_hash varchar(90), primary key (id));
create table data_source_association (id int8 not null, guid varchar(255) not null, name varchar(96) not null, primary_data_source_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, customer_data_element_id int8, is_one_of_separator varchar(3), fiscal_year_start_month int4, from_import_crv_a_id int8, from_import_crv_b_id int8, dna varchar(255) not null, sha256_hash varchar(90), primary key (id));
create table data_subtype (id int4 not null, name varchar(96), description varchar(255), selectable boolean, primary key (id));
create table data_subtype_comparison (data_subtype_id int4 not null, data_comparison_id int8 not null, primary key (data_subtype_id, data_comparison_id));
create table data_type (id int4 not null, name varchar(96), description varchar(255), selectable boolean, primary key (id));
create table data_type_subtype (data_type_id int4 not null, data_subtype_id int4 not null, primary key (data_type_id, data_subtype_id));
create table database_file (id int8 not null, type int4, file_name varchar(255), content_type varchar(255), created timestamp, file_content BYTEA not null, metadata varchar(4000), sha256_hash varchar(90), primary key (id));
create table date_data_value (id int8 not null, name varchar(96), description varchar(255), primary key (id));
create table de_state_transition (id int8 not null, delivery_event_id int8, system_state_id int8, error int4, created timestamp, updated timestamp, error_message varchar(4000), primary key (id));
create table deactivate_types (id int8 not null, name varchar(96), primary key (id));
create table delimited_indicators (data_source_id int8 not null, list_order int4 not null, indicator_location int4 not null, primary key (data_source_id, list_order));
create table delivery_event (id int8 not null, event_type_id int4, updated timestamp, request_date timestamp, scheduled_time timestamp, created timestamp, bundling timestamp, preprocessing timestamp, sending timestamp, waiting_for_result timestamp, receiving_result timestamp, processing_result timestamp, processing_report timestamp, post_processing timestamp, completed timestamp, waiting_in_queue_time int8, bundling_time int8, preprocessing_time int8, sending_time int8, waiting_for_result_time int8, receiving_result_time int8, processing_result_time int8, processing_report_time int8, post_processing_time int8, completed_time int8, dews_waiting_in_queue_time int8, dews_processing_time int8, dews_qualification_engine_time int8, dews_connector_time int8, dews_qepost_time int8, dews_completed_time int8, save_xml boolean, log_file_path varchar(255), de_server_guid varchar(255), bundle_name_override varchar(255), user_id int8, updated_by_id int8, job_id int8, state_bit_mask int8, system_state_id int8, reference_id int8 not null, primary key (id));
create table delivery_event_metadata (id int8 not null, delivery_event_id int8, metadata_name varchar(255), return_code int4, created timestamp, primary key (id));
create table delivery_event_schedule (id int8 not null, start_date timestamp, end_date timestamp, repeating boolean, frequency_type_id int4, daily_frequency_type_id int4, primary key (id));
create table delivery_event_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table deserver (id int8 not null, guid varchar(255) not null, name varchar(255) not null, description varchar(255), url varchar(4000), filename_pattern varchar(4000), notification_emails varchar(4000), is_default boolean not null, post_process_script varchar(4000), notify_error_only boolean not null, communication_type int4 not null, private_sshkey_file_id int8, public_sshkey_file_id int8, bundle_state_id int8 not null, availability int4 not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table deserver_bundle_type_state (id int8 not null, default_preview boolean, default_test boolean, default_simulation boolean, default_production boolean, default_import boolean, default_proof boolean, default_comm_proof boolean, default_comm_production boolean, default_test_suite boolean, default_segmentation boolean, enable_preview boolean, enable_test boolean, enable_simulation boolean, enable_production boolean, enable_import boolean, enable_proof boolean, enable_comm_proof boolean, enable_comm_production boolean, enable_test_suite boolean, enable_segmentation boolean, primary key (id));
create table deserver_communicationtype (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table dev_de_map (dev_id int8 not null, data_source_id int8 not null, de_id int8 not null, primary key (dev_id, data_source_id));
create table dev_ds_hash_map (dev_id int8 not null, data_source_id int8 not null, sha256_hash varchar(90), primary key (dev_id, data_source_id));
create table dialogue_configuration (id int8 not null, created timestamp, updated timestamp, connector_id int8, qualification_output_id int8, sha256_hash varchar(90), customer_driver_file varchar(255), execute_incloud_test boolean, execute_incloud_preview boolean, execute_incloud_proof boolean, execute_incloud_simulation boolean, pre_qualengine_script varchar(255), post_qualengine_script varchar(255), post_connector_script varchar(255), bundle_filenames_for_images boolean not null, filename_separator_for_images varchar(255), play_messages_on_empty_var boolean not null, override_remote_server boolean, remote_server_ip varchar(255), remote_server_port varchar(255), remote_server_user varchar(255), remote_server_password varchar(255), applied_de_version varchar(255), input_character_encoding int4, output_character_encoding int4, de_server_guid varchar(255), bundle_name_override varchar(255), output_filename_cplex_val_id int8, output_doc_title_cplex_val_id int8, validate_prod_bundle boolean, escape_driver_data_tags boolean not null, convert_table_border_px_to_pt boolean not null, eval_not_equal_on_missing_tag boolean not null, play_empty_agg_first_last_var boolean not null, remove_zero_from_style_con boolean not null, comp_time_parent_tagging boolean not null, data_group_expression_var_proc boolean not null, list_style_control_type_id int4, color_output_format_type_id int4, script_var_applies_undefined boolean not null, correct_paragraph_text_styles boolean not null, fix_inline_targeting_styles boolean not null, preserve_data_whitespace boolean not null, nbsp_composed_as_space boolean not null, normalize_image_library boolean not null, normalize_embedded_content boolean not null, gmc_span_to_t_tag boolean not null, blue_underline_links boolean not null, use_default_image int4, updated_by_id int8, composition_file_set_id int8, composition_version varchar(255), pub_file varchar(255), report varchar(255), report_updated timestamp, supports_styles boolean, legacy_dxf_mode boolean not null, mixed_dxf_tagged_text boolean not null, run_time_dxf boolean not null, primary key (id));
create table dialogue_data_mapping (id int8 not null, dialogue_data_type varchar(255), data_type_id int4, primary key (id));
create table dialogue_metadata_tags (id int8 not null, config_id int8, tagendstag boolean, bold varchar(255), boldend varchar(255), italic varchar(255), italicend varchar(255), underline varchar(255), underlineend varchar(255), normal varchar(255), bolditalic varchar(255), boldunderline varchar(255), italicunderline varchar(255), bolditalicunderline varchar(255), paragraph varchar(255), softreturn varchar(255), tab varchar(255), nobreak varchar(255), lefttag varchar(255), center varchar(255), righttag varchar(255), variabletag varchar(255), variableend varchar(255), updated timestamp, updated_by_id int8, created timestamp, primary key (id));
create table dictionary (id int8 not null, name varchar(96), dictionary_path varchar(255), lang_code varchar(10) not null, locale_code varchar(10), enabled boolean, type int4, created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, primary key (id));
create table document (id int8 not null, guid varchar(255) not null, metatags varchar(255), name varchar(96), external_id varchar(255), description varchar(4000), enabled boolean, removed boolean not null, tp_content_changed boolean, is_trash_tp boolean, parent_document_id int8, origin_document_id int8, channel_parent_id int8, exchange_instance_guid varchar(255), exchange_touchpoint_guid varchar(255), exchange_published_timestamp timestamp, exchange_updated_timestamp timestamp, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, sequence int4, connector_name varchar(96), default_text_style_id int8, default_paragraph_style_id int8, default_list_style_id int8, selection_parameter_group_id int8, process_using_combined_content boolean, insert_parameter_group_id int8, first_page_weight int4, other_pages_weight int4, rate_schedule_collection_id int8, selection_type_id int4, selection_visible_by_default boolean, language_parameter_group_id int8, varied_by_tp_variant boolean not null, formatting_parameter_group_id int8, data_source_association_id int8, data_group_id int8, fiscal_year_start_month int4, customer_rpt_variable_a_id int8, customer_rpt_variable_b_id int8, connected_enabled boolean not null, communications_dataresource_id int8, communication_marker_style_id int8, comm_order_entry_enabled boolean not null, comm_comp_result_webserv_id int8, comm_prod_status_webserv_id int8, comm_notification_webserv_id int8, comm_data_feed_webserv_id int8, comm_prod_type_id int4 not null, comm_multi_recipient_id int8, comm_ext_validation_enabled boolean not null, comm_applied_tag_cloud boolean not null, comm_applies_copies_input boolean not null, comm_display_thumbnail boolean not null, comm_applies_tp_selection boolean not null, comm_zone_content_transient boolean not null, comm_forced_proof_driver_val varchar(255), comm_resolve_variable_values boolean not null, comm_suppress_nonedit_zones boolean not null, comm_pdf_conversion_quality int4, comm_skip_inter_when_no_zone boolean not null, comm_use_beta boolean not null, comm_fast_ind_edit boolean not null, segmentation_enabled boolean not null, seg_data_resource_id int8, sftp_ip_address varchar(255), sftp_folder_path varchar(1024), sftp_user_id varchar(255), sftp_password varchar(255), sftp_ssh_key varchar(255), private_key varchar(255), metadata_form_id int8, touchpoint_metadata_def_id int8, variant_metadata_def_id int8, maintain_variant_hierarchy boolean, use_connector_as_zone_id boolean not null, dna varchar(255) not null, message_wf_id int8, connected_wf_id int8, br_endpoint varchar(255), br_username varchar(255), br_token varchar(255), br_target_folder varchar(255), brand_profile_id int8, stripo_enabled boolean, target_of_sync boolean default true not null, accept_only_active_objects boolean default false not null, active_objects_sync_thru_wf boolean default false not null, communication_unique_index int8, dictionary_identifier varchar(255), primary key (id));
create table document_history (id int8 not null, document_id int8 not null, operation_type_id int4 not null, import_file_name varchar(255), source_domain_name varchar(96), source_instance_name varchar(96), source_document_name varchar(96), log_file_path_name varchar(255), created timestamp, created_by_id int8, primary key (id));
create table document_preview (id int8 not null, content_object_id int8, zone_id int8, document_id int8, data_resource_id int8, pg_tree_node_id int8, output_filename varchar(255), output_path varchar(255), updated timestamp, created timestamp, completed_date timestamp, request_date timestamp, updated_by_id int8, user_id int8, complete boolean, error boolean, channel_context_id int8, workgroup_id int8, messagepoint_locale_id int8 not null, primary key (id));
create table document_production_event (id int8 not null, name varchar(96), guid varchar(255) not null, enabled boolean, document_id int8, tp_collection_id int8, delivery_event_type_id int4, delivery_event_schedule_id int8, last_scheduler_rundate timestamp, receive_failure_notification boolean not null, reset_tp_content_changed boolean, output_filename varchar(255), output_path varchar(255), updated timestamp, created timestamp, completed_date timestamp, request_date timestamp, updated_by_id int8, channel_context_id int8, complete boolean, error boolean, api_retrieve boolean not null, update_connected_prod_bundle boolean not null, composition_fileset_id int8, data_resource_id int8, de_server_guid varchar(255), bundle_name_override varchar(255), primary key (id));
create table document_section (id int8 not null, parent_document_section_id int8, guid varchar(255) not null, name varchar(96), override_name boolean, section_order int4, override_section_order boolean, image_location varchar(512), image_name varchar(255), original_image_file_id int8, shrink_background_to_fit boolean not null, apply_background_for_test boolean not null, override_image boolean, document_id int8, override_dimensions boolean, width int4, height int4, layout_type_id int4 not null, section_type_id int4 not null, page_numbering_type_id int4 not null, override_margin boolean, margin_top int4, margin_right int4, margin_bottom int4, margin_left int4, override_header boolean, header_height int4, override_footer boolean, footer_height int4, override_region_left boolean, region_left_width int4, override_region_right boolean, region_right_width int4, starts_front_facing boolean not null, afp_copy_group_name varchar(255), guide_data varchar(4000), override_guides boolean, origin_object_id int8, dna varchar(255) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, sha256_hash varchar(90), primary key (id));
create table document_tenant_email_address (id int8 not null, document_id int8, tenant_email_address varchar(255), primary key (id));
create table domain (id int8 not null, name varchar(96) not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table domain_pod (id int8 not null, domain int8 not null, pod int8 not null, domain_guid varchar(255) not null, dcs_schema varchar(255), dcs_guid varchar(255), parent_dcs_guid varchar(255), updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, is_pinc_enabled boolean not null, sso_type int4 not null, sso_idp_id varchar(255), sso_secret_key varchar(255), sso_error_page_url varchar(255), sso_logout_page_url varchar(255), primary key (id));
create table emessaging_configuration (id int8 not null, created timestamp, updated timestamp, connector_id int8, qualification_output_id int8, sha256_hash varchar(90), customer_driver_file varchar(255), execute_incloud_test boolean, execute_incloud_preview boolean, execute_incloud_proof boolean, execute_incloud_simulation boolean, pre_qualengine_script varchar(255), post_qualengine_script varchar(255), post_connector_script varchar(255), bundle_filenames_for_images boolean not null, filename_separator_for_images varchar(255), play_messages_on_empty_var boolean not null, override_remote_server boolean, remote_server_ip varchar(255), remote_server_port varchar(255), remote_server_user varchar(255), remote_server_password varchar(255), applied_de_version varchar(255), input_character_encoding int4, output_character_encoding int4, de_server_guid varchar(255), bundle_name_override varchar(255), output_filename_cplex_val_id int8, output_doc_title_cplex_val_id int8, validate_prod_bundle boolean, escape_driver_data_tags boolean not null, convert_table_border_px_to_pt boolean not null, eval_not_equal_on_missing_tag boolean not null, play_empty_agg_first_last_var boolean not null, remove_zero_from_style_con boolean not null, comp_time_parent_tagging boolean not null, data_group_expression_var_proc boolean not null, list_style_control_type_id int4, color_output_format_type_id int4, script_var_applies_undefined boolean not null, correct_paragraph_text_styles boolean not null, fix_inline_targeting_styles boolean not null, preserve_data_whitespace boolean not null, nbsp_composed_as_space boolean not null, normalize_image_library boolean not null, normalize_embedded_content boolean not null, gmc_span_to_t_tag boolean not null, blue_underline_links boolean not null, use_default_image int4, updated_by_id int8, customer_email_address_var_id int8, customer_phone_number_var_id int8, primary key (id));
create table encoding_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table event_type (id int8 not null, name varchar(96), description varchar(255), primary key (id));
create table exact_target_last_query (id int8 not null, last_bounce_query timestamp, last_sent_query timestamp, primary key (id));
create table exacttarget_configuration (id int8 not null, created timestamp, updated timestamp, connector_id int8, qualification_output_id int8, sha256_hash varchar(90), customer_driver_file varchar(255), execute_incloud_test boolean, execute_incloud_preview boolean, execute_incloud_proof boolean, execute_incloud_simulation boolean, pre_qualengine_script varchar(255), post_qualengine_script varchar(255), post_connector_script varchar(255), bundle_filenames_for_images boolean not null, filename_separator_for_images varchar(255), play_messages_on_empty_var boolean not null, override_remote_server boolean, remote_server_ip varchar(255), remote_server_port varchar(255), remote_server_user varchar(255), remote_server_password varchar(255), applied_de_version varchar(255), input_character_encoding int4, output_character_encoding int4, de_server_guid varchar(255), bundle_name_override varchar(255), output_filename_cplex_val_id int8, output_doc_title_cplex_val_id int8, validate_prod_bundle boolean, escape_driver_data_tags boolean not null, convert_table_border_px_to_pt boolean not null, eval_not_equal_on_missing_tag boolean not null, play_empty_agg_first_last_var boolean not null, remove_zero_from_style_con boolean not null, comp_time_parent_tagging boolean not null, data_group_expression_var_proc boolean not null, list_style_control_type_id int4, color_output_format_type_id int4, script_var_applies_undefined boolean not null, correct_paragraph_text_styles boolean not null, fix_inline_targeting_styles boolean not null, preserve_data_whitespace boolean not null, nbsp_composed_as_space boolean not null, normalize_image_library boolean not null, normalize_embedded_content boolean not null, gmc_span_to_t_tag boolean not null, blue_underline_links boolean not null, use_default_image int4, updated_by_id int8, customer_email_address_var_id int8, customer_key_var_id int8, primary key (id));
create table ext_reporting_vars_doc (document_id int8 not null, data_element_variable_id int8 not null, primary key (document_id, data_element_variable_id));
create table external_event (id int8 not null, name varchar(96), event_type int4, event_key varchar(255), updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table external_proof_validation (id int8 not null, communication_id int8, communication_proof_id int8, status_id int4, external_validation_email varchar(255), response_date timestamp, validation_feedback BYTEA, created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, primary key (id));
create table fileroot_man_profile (id int8 not null, purge_ex_prod boolean not null, purge_ex_test boolean not null, purge_ex_proof boolean not null, purge_ex_connected boolean not null, prod_ex_duration int4 not null, test_ex_duration int4 not null, proof_ex_duration int4 not null, connected_ex_duration int4 not null, primary key (id));
create table filter_condition (id int8 not null, guid varchar(255), updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, data_element_variable_id int8, data_element_comparison_id int8, data_file_path varchar(512), primary key (id));
create table filter_condition_value_map (filter_condition_id int8 not null, map_key varchar(255) not null, map_value varchar(255), primary key (filter_condition_id, map_key));
create table folder_insert (folder_id int8 not null, insert_id int8 not null, last_visited_time timestamp, primary key (folder_id, insert_id));
create table folders (id int8 not null, folder_type varchar(15) not null, name varchar(96) not null, description varchar(255), parent_id int8, hierarchy_depth int4 not null, system_managed boolean not null, guid varchar(255) not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, owner_id int8, primary key (id));
create table formatting_selection (id int8 not null, guid varchar(255) not null, transform_profile_id int8, alternate_document_id int8, document_id int8, pg_tree_node_id int8, origin_object_id int8, dna varchar(255) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table frequency_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table ftp_configuration (id int8 not null, created timestamp, updated timestamp, connector_id int8, qualification_output_id int8, sha256_hash varchar(90), customer_driver_file varchar(255), execute_incloud_test boolean, execute_incloud_preview boolean, execute_incloud_proof boolean, execute_incloud_simulation boolean, pre_qualengine_script varchar(255), post_qualengine_script varchar(255), post_connector_script varchar(255), bundle_filenames_for_images boolean not null, filename_separator_for_images varchar(255), play_messages_on_empty_var boolean not null, override_remote_server boolean, remote_server_ip varchar(255), remote_server_port varchar(255), remote_server_user varchar(255), remote_server_password varchar(255), applied_de_version varchar(255), input_character_encoding int4, output_character_encoding int4, de_server_guid varchar(255), bundle_name_override varchar(255), output_filename_cplex_val_id int8, output_doc_title_cplex_val_id int8, validate_prod_bundle boolean, escape_driver_data_tags boolean not null, convert_table_border_px_to_pt boolean not null, eval_not_equal_on_missing_tag boolean not null, play_empty_agg_first_last_var boolean not null, remove_zero_from_style_con boolean not null, comp_time_parent_tagging boolean not null, data_group_expression_var_proc boolean not null, list_style_control_type_id int4, color_output_format_type_id int4, script_var_applies_undefined boolean not null, correct_paragraph_text_styles boolean not null, fix_inline_targeting_styles boolean not null, preserve_data_whitespace boolean not null, nbsp_composed_as_space boolean not null, normalize_image_library boolean not null, normalize_embedded_content boolean not null, gmc_span_to_t_tag boolean not null, blue_underline_links boolean not null, use_default_image int4, server_id int8, web_url varchar(255), recipient_file_location varchar(255), is_embedded boolean, updated_by_id int8, recipfile_cplex_val_id int8, primary key (id));
create table generic_configuration (id int8 not null, created timestamp, updated timestamp, connector_id int8, qualification_output_id int8, sha256_hash varchar(90), customer_driver_file varchar(255), execute_incloud_test boolean, execute_incloud_preview boolean, execute_incloud_proof boolean, execute_incloud_simulation boolean, pre_qualengine_script varchar(255), post_qualengine_script varchar(255), post_connector_script varchar(255), bundle_filenames_for_images boolean not null, filename_separator_for_images varchar(255), play_messages_on_empty_var boolean not null, override_remote_server boolean, remote_server_ip varchar(255), remote_server_port varchar(255), remote_server_user varchar(255), remote_server_password varchar(255), applied_de_version varchar(255), input_character_encoding int4, output_character_encoding int4, de_server_guid varchar(255), bundle_name_override varchar(255), output_filename_cplex_val_id int8, output_doc_title_cplex_val_id int8, validate_prod_bundle boolean, escape_driver_data_tags boolean not null, convert_table_border_px_to_pt boolean not null, eval_not_equal_on_missing_tag boolean not null, play_empty_agg_first_last_var boolean not null, remove_zero_from_style_con boolean not null, comp_time_parent_tagging boolean not null, data_group_expression_var_proc boolean not null, list_style_control_type_id int4, color_output_format_type_id int4, script_var_applies_undefined boolean not null, correct_paragraph_text_styles boolean not null, fix_inline_targeting_styles boolean not null, preserve_data_whitespace boolean not null, nbsp_composed_as_space boolean not null, normalize_image_library boolean not null, normalize_embedded_content boolean not null, gmc_span_to_t_tag boolean not null, blue_underline_links boolean not null, use_default_image int4, file_type int4, file_name varchar(255), format_type int4, xslt_file_location varchar(255), pre_xslt_file varchar(255), post_xslt_file varchar(255), primary key (id));
create table gmc_configuration (id int8 not null, created timestamp, updated timestamp, connector_id int8, qualification_output_id int8, sha256_hash varchar(90), customer_driver_file varchar(255), execute_incloud_test boolean, execute_incloud_preview boolean, execute_incloud_proof boolean, execute_incloud_simulation boolean, pre_qualengine_script varchar(255), post_qualengine_script varchar(255), post_connector_script varchar(255), bundle_filenames_for_images boolean not null, filename_separator_for_images varchar(255), play_messages_on_empty_var boolean not null, override_remote_server boolean, remote_server_ip varchar(255), remote_server_port varchar(255), remote_server_user varchar(255), remote_server_password varchar(255), applied_de_version varchar(255), input_character_encoding int4, output_character_encoding int4, de_server_guid varchar(255), bundle_name_override varchar(255), output_filename_cplex_val_id int8, output_doc_title_cplex_val_id int8, validate_prod_bundle boolean, escape_driver_data_tags boolean not null, convert_table_border_px_to_pt boolean not null, eval_not_equal_on_missing_tag boolean not null, play_empty_agg_first_last_var boolean not null, remove_zero_from_style_con boolean not null, comp_time_parent_tagging boolean not null, data_group_expression_var_proc boolean not null, list_style_control_type_id int4, color_output_format_type_id int4, script_var_applies_undefined boolean not null, correct_paragraph_text_styles boolean not null, fix_inline_targeting_styles boolean not null, preserve_data_whitespace boolean not null, nbsp_composed_as_space boolean not null, normalize_image_library boolean not null, normalize_embedded_content boolean not null, gmc_span_to_t_tag boolean not null, blue_underline_links boolean not null, use_default_image int4, updated_by_id int8, composition_file_set_id int8, composition_version varchar(255), workflow_file varchar(255), workflow_file_updated timestamp, control_styles boolean not null, text_style_comp_type_id int4, paragraph_style_comp_type_id int4, list_style_comp_type_id int4, restricted_quotes boolean not null, primary key (id));
create table hash_alias (object_hash_key varchar(90) not null, match_hash varchar(90), alias_hash varchar(90), primary key (object_hash_key));
create table hist_content (id int8 not null, guid varchar(255) not null, sha256_hash varchar(90), text_content text, unformatted_text_content text, image_location varchar(512), image_name varchar(255), image_uploaded timestamp, applied_image_filename varchar(40), asset_id varchar(255), asset_url varchar(2048), asset_site varchar(255), asset_last_update timestamp, asset_last_sync timestamp, image_link int8, image_alt_text int8, image_ext_link int8, image_ext_path int8, action_type_id int4 not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table hist_content_content_object_t (content_id int8 not null, content_object_id int8 not null, object_type int4, primary key (content_id, content_object_id));
create table hist_content_content_targeting (content_id int8 not null, content_targeting_id int8 not null, primary key (content_id, content_targeting_id));
create table hist_content_images (content_id int8 not null, database_file_id int8 not null, primary key (content_id, database_file_id));
create table hist_content_list_style (content_id int8 not null, list_style_id int8 not null, primary key (content_id, list_style_id));
create table hist_content_obj_association (id int8 not null, guid varchar(255) not null, type_id int4, content_object_id int8, data_type int4, content_object_data_guid varchar(255), co_pg_tn_id int8, ref_co_pg_tn_id int8, tp_pg_tn_id int8, ref_tp_pg_tn_id int8, zone_part_id int8, messagepoint_locale_id int8, ref_messagepoint_locale_id int8, content_id int8, ref_image_library_id int8, action_type_id int4 not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table hist_content_object_data (id int8 not null, content_object_id int8, data_type int4, content_object_data_guid varchar(255), archived timestamp, archived_by_user_id int8, archived_action_type_id int4 not null, activated timestamp, activated_by_user_id int8, activated_action_type_id int4 not null, created timestamp, created_by_user_id int8, created_action_type_id int4 not null, primary key (id));
create table hist_content_paragraph_style (content_id int8 not null, paragraph_style_id int8 not null, primary key (content_id, paragraph_style_id));
create table hist_content_placeholders (content_id int8 not null, placeholder_id int8 not null, primary key (content_id, placeholder_id));
create table hist_content_text_style (content_id int8 not null, style_id int8 not null, primary key (content_id, style_id));
create table hist_content_variable (variable_id int8 not null, content_id int8 not null, primary key (variable_id, content_id));
create table hist_meta_form_item_value_set (metadata_form_item_id int8 not null, string_value varchar(4000) not null, primary key (metadata_form_item_id, string_value));
create table hist_metadata_form (id int8 not null, guid varchar(255) not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, form_definition_id int8, primary key (id));
create table hist_metadata_form_item (id int8 not null, guid varchar(255) not null, value text, aux_value_data varchar(4000), upload_file_id int8, form_item_definition_id int8, metadata_form_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table hist_rat_shared_content (id int8 not null, name varchar(255), metatags varchar(255), text_content text, markup_content text, rat_shared_content_id int8, shared_last_action varchar(255), shared_last_action_by varchar(255), created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, primary key (id));
create table hist_rationalizer_content (id int8 not null, name varchar(255), metatags varchar(255), text_content text, markup_content text, rationalizer_content_id int8, rationalizer_shared_content_id int8, content_last_action varchar(255), content_last_action_by varchar(255), created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, primary key (id));
create table insert_document (insert_id int8 not null, document_id int8 not null, primary key (insert_id, document_id));
create table insert_obj (id int8 not null, guid varchar(255) not null, name varchar(96) not null, description varchar(255), delivery_type_id int4, stock_id varchar(96), weight int4, front_content_path varchar(512), back_content_path varchar(512), locked_for_id int8, last_editor_id int8, status_id int8 not null, start_date timestamp, end_date timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, include_tg_relation int4, excluded_tg_relation int4, extended_tg_relation int4, primary key (id));
create table insert_rate_schedule (insert_schedule_id int8 not null, number_of_sheets int8 not null, rate_schedule_collection_id int8 not null, primary key (insert_schedule_id, number_of_sheets));
create table insert_schedule (id int8 not null, guid varchar(255) not null, name varchar(96) not null, schedule_id varchar(96), keywords varchar(255), number_of_bins int4 not null, locked_for_id int8, last_editor_id int8, status_id int8 not null, default_schedule boolean not null, start_date timestamp not null, end_date timestamp, pgi_collection_id int8, schedule_collection_id int8, next_id int8, previous_id int8, description varchar(255), updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table insert_schedule_bin_assignment (id int8 not null, guid varchar(255) not null, bin_no int4 not null, priority int4, available boolean not null, start_date timestamp, end_date timestamp, insert_schedule_id int8, insert_id int8, primary key (id));
create table insert_schedule_collection (id int8 not null, guid varchar(255) not null, document_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table insert_target_group_excluded (insert_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (insert_id, sequence));
create table insert_target_group_extended (insert_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (insert_id, sequence));
create table insert_target_group_included (insert_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (insert_id, sequence));
create table insert_tg_instance_map (tg_id int8 not null, insert_id int8 not null, instance_id int8 not null, primary key (tg_id, insert_id));
create table item_type (id int8 not null, name varchar(96), description varchar(255), primary key (id));
create table job (id int8 not null, event_type_id int4, message_delivery_filename varchar(255), host_ip_address varchar(255), host_name varchar(255), updated timestamp, created timestamp, updated_by_id int8, system_state_id int8, primary key (id));
create table json_data_definition (id int8 not null, name varchar(256), start_customer boolean, start_data_group boolean, break_indicator varchar(64), repeating boolean, definition_type int4, dna varchar(255) not null, parent_def_id int8, data_source_id int8, data_group_id int8, data_element_id int8, updated timestamp, created timestamp, updated_by_id int8, created_by_id int8, primary key (id));
create table language_content_hash (id int8 not null, primary key (id));
create table language_content_hash_map (language_content_hash_id int8 not null, messagepoint_locale_id int8 not null, sha256_hash varchar(255) not null, primary key (language_content_hash_id, messagepoint_locale_id));
create table language_selection (id int8 not null, guid varchar(255) not null, messagepoint_locale_id int8 not null, document_id int8, pg_tree_node_id int8, origin_object_id int8, dna varchar(255) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table layout_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table licence (id int8 not null, description varchar(255), startDate timestamp not null, stopDate timestamp not null, creationDate timestamp not null, guid varchar(255) not null, type varchar(255) not null, xml varchar(8192), license_manager_enabled boolean not null, license_manager_hardware_key_a varchar(4000), license_manager_hardware_key_b varchar(4000), license_manager_hardware_key_c varchar(4000), license_manager_hardware_key_d varchar(4000), license_manager_hardware_key_e varchar(4000), license_manager_hardware_key_f varchar(4000), number_of_full_users int4, number_of_restricted_users int4, print_channels_authorized boolean, web_channels_authorized boolean, email_channels_authorized boolean, sms_channels_authorized boolean, hp_dialogue_authorized boolean, hp_dialogue_dxf_authorized boolean, sefas_authorized boolean, sefas_hcs_authorized boolean, gmc_printnet_authorized boolean, send_mail_authorized boolean, exact_target_authorized boolean, clickatell_authorized boolean, ftp_web_authorized boolean, native_print_authorized boolean, emsg_email_authorized boolean, emsg_sms_authorized boolean, applicationId int8, customerId int8 not null, primary key (id));
create table licence_application (id int8 not null, name varchar(255) not null, majorVersion varchar(255) not null, minorVersion varchar(255) not null, uuid varchar(255), description varchar(255) not null, ownerName varchar(255) not null, primary key (id));
create table licence_attributes (licence_id int8 not null, attribute_name varchar(255) not null, attribute_value varchar(255), primary key (licence_id, attribute_name));
create table licence_audit (id int8 not null, timestamp timestamp not null, extraInfo varchar(4096), licenceId int8 not null, auditActionId int8 not null, primary key (id));
create table licence_audit_action (id int8 not null, name varchar(255) not null, description varchar(255) not null, primary key (id));
create table licence_change_reasons (id int8 not null, type varchar(255) not null, description varchar(255) not null, primary key (id));
create table licence_customer (id int8 not null, guid varchar(255) not null, name varchar(255) not null, emailAddress varchar(255) not null, keypairId int8 not null, representativeId int8, primary key (id));
create table licence_customer_keypair (id int8 not null, publicKey BYTEA not null, privateKey BYTEA not null, primary key (id));
create table licence_history (id int8 not null, licenceId int8 not null, description varchar(255), startDate timestamp not null, stopDate timestamp not null, creationDate timestamp not null, insertDate timestamp not null, guid varchar(255), type varchar(255) not null, xml varchar(8192), number_of_full_users int4, number_of_restricted_users int4, print_channels_authorized boolean, web_channels_authorized boolean, email_channels_authorized boolean, sms_channels_authorized boolean, hp_dialogue_authorized boolean, hp_dialogue_dxf_authorized boolean, sefas_authorized boolean, sefas_hcs_authorized boolean, gmc_printnet_authorized boolean, send_mail_authorized boolean, exact_target_authorized boolean, clickatell_authorized boolean, ftp_web_authorized boolean, native_print_authorized boolean, emsg_email_authorized boolean, emsg_sms_authorized boolean, customerInfo varchar(1028), applicationInfo varchar(1028), serverInfo varchar(1028), primary key (id));
create table licence_history_reason (history_id int8 not null, reason_id int8 not null, primary key (history_id, reason_id));
create table licence_module (id int8 not null, name varchar(255) not null, description varchar(255), guid varchar(255) not null, applicationId int8 not null, primary key (id));
create table licence_representative (id int8 not null, guid varchar(255) not null, name varchar(255) not null, emailAddress varchar(255) not null, phoneNumber varchar(255), primary key (id));
create table licence_resource (id int8 not null, name varchar(255) not null, licenceId int8, hash bytea, sortOrder int4, primary key (id));
create table licence_server (id int8 not null, name varchar(255) not null, licenceId int8, primary key (id));
create table licence_server_ip_address (serverId int8 not null, ipAddress varchar(255));
create table list_style (id int8 not null, identifier varchar(96), name varchar(255), connector_name varchar(255), alignment_id int4, list_spacing_before int4, list_spacing_after int4, list_spacing_right int4, border_type_id int4, border_width int4, line_spacing int4 not null, line_spacing_type_id int4, text_style_id int8, tagging_override varchar(2048), bullet_left_margin int4, bullet_right_margin int4, bullet_top_margin int4, bullet_bottom_margin int4, bullet_spacing_data varchar(2048), bullet_symbol_overrides varchar(2048), indent int4, toggle_alignment boolean not null, sha256_hash varchar(90), updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table list_style_cust (id int8 not null, connector_name varchar(255), alignment_id int4, list_spacing_before int4, list_spacing_after int4, list_spacing_right int4, border_type_id int4, border_width int4, line_spacing int4 not null, line_spacing_type_id int4, text_style_id int8, tagging_override varchar(2048), bullet_left_margin int4, bullet_right_margin int4, bullet_top_margin int4, bullet_bottom_margin int4, bullet_spacing_data varchar(2048), bullet_symbol_overrides varchar(2048), indent int4, master_list_style_id int8, origin_object_id int8, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table list_style_cust_map (list_style_id int8 not null, document_id int8 not null, list_style_cust_id int8 not null, primary key (list_style_id, document_id));
create table lookup_table (id int8 not null, guid varchar(255) not null, dna varchar(255) not null, checkout_time timestamp, status_id int8 not null, created timestamp, created_by_id int8, updated timestamp, updated_by_id int8, sha256_hash varchar(90), primary key (id));
create table lookup_table_document (lookup_table_instance_id int8 not null, document_id int8 not null, primary key (lookup_table_instance_id, document_id));
create table lookup_table_instance (id int8 not null, guid varchar(255) not null, name varchar(96) not null, lookup_file_id int8, delimiter varchar(1), input_character_encoding int4, data_source_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, wf_action_id int8, state_id int8, ready_for_approval boolean, fully_visible boolean default true not null, cloned_from_id int8, sha256_hash varchar(90), primary key (id));
create table lookup_table_tpc (lookup_table_instance_id int8 not null, tp_collection_id int8 not null, primary key (lookup_table_instance_id, tp_collection_id));
create table lookup_table_version_map (lookup_table_id int8 not null, lookup_table_instance_id int8 not null, start_date timestamp, end_date timestamp, creation_user_note varchar(255), creation_reason_id int8, expiry_user_note varchar(255), expiry_reason_id int8, latest_archived boolean, user_id int8, status_id int8 not null, created timestamp, created_by int8, updated timestamp, updated_by int8, primary key (lookup_table_id, lookup_table_instance_id));
create table lookup_value (id int8 not null, name varchar(255), lookup_value varchar(255), data_element_variable_id int8, primary key (id));
create table message_delivery_report (id int8 not null, user_id int8, data_filename varchar(255), output_filename varchar(255), updated timestamp, updated_by_id int8, created timestamp, start_date timestamp, end_date timestamp, completed_date timestamp, request_date timestamp, primary key (id));
create table message_simulation_report (id int8 not null, user_id int8, data_filename varchar(255), output_filename varchar(255), updated timestamp, created timestamp, start_date timestamp, end_date timestamp, completed_date timestamp, request_date timestamp, primary key (id));
create table messagepoint_locale (id int8 not null, name varchar(96) not null, display_code varchar(96) not null, code varchar(10) not null, favourite boolean, default_locale boolean, language_id int8, language_name varchar(96) not null, language_display_code varchar(96) not null, language_code varchar(10) not null, language_favourite boolean, language_group int8, dictionary varchar(255), date_format varchar(255), currency_symbol varchar(255), suffix_currency_symbol boolean, thousands_separator varchar(255), decimal_symbol varchar(255), number_of_decimals varchar(255), boolean_symbol_true varchar(255), boolean_symbol_false varchar(255), short_month_names varchar(255), long_month_names varchar(255), drop_decimal_for_whole_num boolean not null, primary key (id));
create table meta_form_item_def_con_ele_map (metadata_form_item_def_id int8 not null, connector varchar(255) not null, data_element_id int8 not null, primary key (metadata_form_item_def_id, connector));
create table metadata_form (id int8 not null, guid varchar(255) not null, origin_document_id int8, parent_form_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, form_definition_id int8, sha256_hash varchar(90), primary key (id));
create table metadata_form_definition (id int8 not null, guid varchar(255) not null, metatags varchar(255), name varchar(512), description varchar(4000), type int4, origin_document_id int8, parent_form_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, web_service_config_id int8, sha256_hash varchar(90), primary key (id));
create table metadata_form_item (id int8 not null, guid varchar(255) not null, value text, aux_value_data varchar(4000), upload_file_id int8, form_item_definition_id int8, metadata_form_id int8, parent_form_id int8, is_manifest_item boolean not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table metadata_form_item_definition (id int8 not null, guid varchar(255) not null, item_order int4, name varchar(255) not null, data_element_id int8, metadata_form_item_type_id int4, menu_value_items text, web_service_refresh_type_id int4, primary_connector varchar(255), is_mandatory boolean not null, is_locked_for_edit boolean not null, description varchar(4000), parent_item_order int4, display_trigger_values varchar(4000), auto_generate_on_import boolean not null, metadata_form_definition_id int8, parent_form_id int8, field_size_type_id int4 not null, field_min_length int4, field_max_length int4, input_validation_type_id int4 not null, default_input_value varchar(255), default_to_today boolean, unique_value boolean, meta_form_item_origin_type_id int4, meta_form_item_data_type_id int4, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table metadata_form_item_value_set (metadata_form_item_id int8 not null, string_value varchar(4000) not null, primary key (metadata_form_item_id, string_value));
create table metadata_points_of_interest (id int8 not null, label varchar(4000), description varchar(4000), comparision_operator int4, comparision_value varchar(4000), form_item_definition_id int8, rationalizer_application_id int8, primary key (id));
create table mp_channel (id int8 not null, name varchar(96), presentation_name varchar(96), created timestamp, updated timestamp, primary key (id));
create table mp_connector (id int8 not null, name varchar(96), presentation_name varchar(96), created timestamp, updated timestamp, channel_id int8, primary key (id));
create table mp_licence (id int8 not null, key varchar(255), value varchar(255), accessible int4, primary key (id));
create table mp_qualification_output (id int8 not null, name varchar(96), created timestamp, updated timestamp, primary key (id));
create table mphcs_configuration (id int8 not null, created timestamp, updated timestamp, connector_id int8, qualification_output_id int8, sha256_hash varchar(90), customer_driver_file varchar(255), execute_incloud_test boolean, execute_incloud_preview boolean, execute_incloud_proof boolean, execute_incloud_simulation boolean, pre_qualengine_script varchar(255), post_qualengine_script varchar(255), post_connector_script varchar(255), bundle_filenames_for_images boolean not null, filename_separator_for_images varchar(255), play_messages_on_empty_var boolean not null, override_remote_server boolean, remote_server_ip varchar(255), remote_server_port varchar(255), remote_server_user varchar(255), remote_server_password varchar(255), applied_de_version varchar(255), input_character_encoding int4, output_character_encoding int4, de_server_guid varchar(255), bundle_name_override varchar(255), output_filename_cplex_val_id int8, output_doc_title_cplex_val_id int8, validate_prod_bundle boolean, escape_driver_data_tags boolean not null, convert_table_border_px_to_pt boolean not null, eval_not_equal_on_missing_tag boolean not null, play_empty_agg_first_last_var boolean not null, remove_zero_from_style_con boolean not null, comp_time_parent_tagging boolean not null, data_group_expression_var_proc boolean not null, list_style_control_type_id int4, color_output_format_type_id int4, script_var_applies_undefined boolean not null, correct_paragraph_text_styles boolean not null, fix_inline_targeting_styles boolean not null, preserve_data_whitespace boolean not null, nbsp_composed_as_space boolean not null, normalize_image_library boolean not null, normalize_embedded_content boolean not null, gmc_span_to_t_tag boolean not null, blue_underline_links boolean not null, use_default_image int4, updated_by_id int8, composition_file_set_id int8, composition_version varchar(255), output_file_type int4, starts_on_odd_page boolean, duplex_output boolean, template_control boolean, accessibility boolean, linespace_position int4, table_padding_in_pts boolean, primary key (id));
create table msg_delivery_report_doc (message_delivery_report_id int8 not null, document_id int8 not null, primary key (message_delivery_report_id, document_id));
create table msg_delivery_report_msg (message_delivery_report_id int8 not null, content_object_id int8 not null, primary key (message_delivery_report_id, content_object_id));
create table msg_simulation_report_doc (message_simulation_report_id int8 not null, document_id int8 not null, primary key (message_simulation_report_id, document_id));
create table msg_simulation_report_msg (message_simulation_report_id int8 not null, content_object_id int8 not null, primary key (message_simulation_report_id, content_object_id));
create table native_comp_configuration (id int8 not null, created timestamp, updated timestamp, connector_id int8, qualification_output_id int8, sha256_hash varchar(90), customer_driver_file varchar(255), execute_incloud_test boolean, execute_incloud_preview boolean, execute_incloud_proof boolean, execute_incloud_simulation boolean, pre_qualengine_script varchar(255), post_qualengine_script varchar(255), post_connector_script varchar(255), bundle_filenames_for_images boolean not null, filename_separator_for_images varchar(255), play_messages_on_empty_var boolean not null, override_remote_server boolean, remote_server_ip varchar(255), remote_server_port varchar(255), remote_server_user varchar(255), remote_server_password varchar(255), applied_de_version varchar(255), input_character_encoding int4, output_character_encoding int4, de_server_guid varchar(255), bundle_name_override varchar(255), output_filename_cplex_val_id int8, output_doc_title_cplex_val_id int8, validate_prod_bundle boolean, escape_driver_data_tags boolean not null, convert_table_border_px_to_pt boolean not null, eval_not_equal_on_missing_tag boolean not null, play_empty_agg_first_last_var boolean not null, remove_zero_from_style_con boolean not null, comp_time_parent_tagging boolean not null, data_group_expression_var_proc boolean not null, list_style_control_type_id int4, color_output_format_type_id int4, script_var_applies_undefined boolean not null, correct_paragraph_text_styles boolean not null, fix_inline_targeting_styles boolean not null, preserve_data_whitespace boolean not null, nbsp_composed_as_space boolean not null, normalize_image_library boolean not null, normalize_embedded_content boolean not null, gmc_span_to_t_tag boolean not null, blue_underline_links boolean not null, use_default_image int4, advanced_composition boolean, apply_fillable_forms boolean, output_file_type int4, starts_on_odd_page boolean, duplex_output boolean, primary key (id));
create table navigation_drop_down_menu (id int8 not null, name varchar(255) not null, ordering int4 not null, tab_id int8, string_id varchar(255), requires varchar(255), primary key (id));
create table navigation_drop_down_menu_item (id int8 not null, name varchar(255), type varchar(2), icon varchar(255), ordering int4 not null, menu_id int8 not null, tree_id int8, string_id varchar(255), url varchar(255), requires varchar(255), store_in_session boolean, authorization_type varchar(2) not null, primary key (id));
create table navigation_menu_item_perm (navigation_menu_item_id int8 not null, permission_id int8 not null, primary key (navigation_menu_item_id, permission_id));
create table navigation_tab_default_map (tab_id int8 not null, type varchar(255) not null, item_id int8 not null, primary key (tab_id, type));
create table navigation_tab_permission (navigation_tab_id int8 not null, permission_id int8 not null, primary key (navigation_tab_id, permission_id));
create table navigation_tabs (id int8 not null, ordering int4 not null, name varchar(255), string_id varchar(255), url varchar(255), default_menu_item_id int8, authorization_type varchar(2) not null, primary key (id));
create table navigation_tree (id int8 not null, f_query varchar(510), parent_id int8, parameter varchar(255), url varchar(255), icon varchar(255), type int4, sequence int4, primary key (id));
create table node (id int8 not null, guid varchar(255) not null, node_type int4 not null, name varchar(96) not null, friendly_name varchar(255), schema_name varchar(96), parent_id int8, branch_id int8, node_count_number int4, enabled boolean not null, status int4 not null, is_default_node boolean not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table notification_email (id int8 not null, recipient_id int8, notification_action_type int4, notification_object_type int4, object_id int8, notification_action_text text, notification_action_link text, created timestamp, created_by_id int8, primary key (id));
create table notification_settings (id int8 not null, user_id int8, event_type int4, object_type int4, object_label varchar(255), object_id int8, action_type int4, metadata text, primary key (id));
create table notification_settings_tp (notification_id int8 not null, touchpoint_id int8 not null, primary key (notification_id, touchpoint_id));
create table object_wf_action_map (id int8 not null, model_type int4 not null, model_id int8 not null, wf_action_id int8, parent_wf_action_id int8, primary key (id));
create table oidc_signing_keys (id int8 not null, guid varchar(255) not null, branch_guid varchar(255) not null, private_key BYTEA not null, public_key BYTEA not null, valid_from timestamp not null, valid_to timestamp not null, mp_type varchar(255) not null, issuer varchar(255), requester varchar(255), primary key (id));
create table old_workflow (id int8 not null, name varchar(255) not null, class_name varchar(255) not null, primary key (id));
create table operations_report_doc (operations_report_id int8 not null, document_id int8 not null, primary key (operations_report_id, document_id));
create table operations_report_event (report_id int8 not null, delivery_event_id int8 not null, primary key (report_id, delivery_event_id));
create table operations_report_scenario (id int8 not null, name varchar(96), start_date timestamp, created timestamp, updated timestamp, complete boolean, error boolean, updated_by_id int8, end_date timestamp, xml_path varchar(255), report_path varchar(255), primary key (id));
create table order_entry_def_con_ele_map (order_entry_item_def_id int8 not null, connector varchar(255) not null, data_element_variable_id int8 not null, primary key (order_entry_item_def_id, connector));
create table order_entry_item (id int8 not null, guid varchar(255) not null, document_id int8 not null, item_order int4, is_primary_driver_entry boolean not null, is_indicator_entry boolean not null, name varchar(255) not null, data_element_variable_id int8, order_entry_type_id int4, data_privacy_type_id int4, uploaded_file_type_id int4 not null, uploaded_file_data_source_id int8, menu_value_items varchar(4000), web_service_refresh_type_id int4, primary_connector varchar(255), is_mandatory boolean not null, description varchar(4000), parent_item_order int4, display_trigger_values varchar(4000), field_size_type_id int4 not null, field_max_length int4, input_validation_type_id int4 not null, upload_file_id int8, default_input_value varchar(255), default_to_today boolean, unique_value boolean, communication_id int8, value varchar(4000), aux_value_data varchar(4000), parent_form_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, metadata_definition_id int8, primary key (id));
create table order_entry_item_con_ele_map (order_entry_item_id int8 not null, connector varchar(255) not null, data_element_variable_id int8 not null, primary key (order_entry_item_id, connector));
create table order_entry_item_definition (id int8 not null, guid varchar(255) not null, document_id int8 not null, item_order int4, is_primary_driver_entry boolean not null, is_indicator_entry boolean not null, name varchar(255) not null, data_element_variable_id int8, order_entry_type_id int4, data_privacy_type_id int4, uploaded_file_type_id int4 not null, uploaded_file_data_source_id int8, menu_value_items varchar(4000), web_service_refresh_type_id int4, primary_connector varchar(255), is_mandatory boolean not null, is_locked_for_edit boolean not null, description varchar(4000), parent_item_order int4, display_trigger_values varchar(4000), field_size_type_id int4 not null, field_max_length int4, input_validation_type_id int4 not null, default_input_value varchar(255), default_to_today boolean, unique_value boolean, criteria_trigger_values_json varchar(255), regex_validation varchar(255), criteria_operator varchar(255), repeating_data_type_id int4 not null, parent_form_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table order_item_def_selections (order_entry_item_definition_id int8 not null, touchpoint_selection_id int8 not null, primary key (order_entry_item_definition_id, touchpoint_selection_id));
create table order_item_selections (order_entry_item_id int8 not null, touchpoint_selection_id int8 not null, primary key (order_entry_item_id, touchpoint_selection_id));
create table para_style_cust (id int8 not null, connector_name varchar(255), allignment_id int4, para_spacing_before int4, para_spacing_after int4, line_spacing int4 not null, line_spacing_type_id int4, left_margin int4, right_margin int4, indent int4, bullet_symbol_id int4, num_list_type_id int4, border_type_id int4, border_width int4, is_bulleted_list_applicable boolean, tagging_override varchar(2048), toggle_alignment boolean not null, toggle_line_spacing boolean not null, toggle_left_margin boolean not null, text_style_id int8, master_para_style_id int8, origin_object_id int8, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, sha256_hash varchar(90), primary key (id));
create table para_style_cust_line_spacings (para_style_cust_id int8 not null, value varchar(255));
create table para_style_cust_map (para_style_id int8 not null, document_id int8 not null, para_style_cust_id int8 not null, primary key (para_style_id, document_id));
create table paragraph_style (id int8 not null, identifier varchar(96), name varchar(255), connector_name varchar(255), allignment_id int4, para_spacing_before int4, para_spacing_after int4, line_spacing int4 not null, line_spacing_type_id int4, left_margin int4, right_margin int4, indent int4, bullet_symbol_id int4, num_list_type_id int4, border_type_id int4, border_width int4, is_bulleted_list_applicable boolean, tagging_override varchar(2048), toggle_alignment boolean not null, toggle_line_spacing boolean not null, toggle_left_margin boolean not null, style_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, sha256_hash varchar(90), primary key (id));
create table paragraph_style_line_spacings (paragraph_style_id int8 not null, value varchar(255));
create table parameter (id int8 not null, guid varchar(255) not null, name varchar(96) not null, description varchar(255), data_element_variable_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table parameter_group (id int8 not null, guid varchar(255) not null, name varchar(96) not null, description varchar(255), updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, sha256_hash varchar(90), primary key (id));
create table parameter_group_instance (id int8 not null, guid varchar(255) not null, pg_id int8, pg_instance_collection_id int8, pg_item_value_1 varchar(255) not null, pg_item_value_2 varchar(255), pg_item_value_3 varchar(255), pg_item_value_4 varchar(255), pg_item_value_5 varchar(255), pg_item_value_6 varchar(255), primary key (id));
create table parameter_group_item (id int8 not null, guid varchar(255) not null, pg_id int8, parameter_id int8, item_order int4 not null, primary key (id));
create table password_history (id int8 not null, user_id int8, password varchar(255), created timestamp, primary key (id));
create table password_recovery (id int8 not null, name varchar(96), reset_key varchar(255) not null, user_id int8, is_used boolean default false not null, reset_date timestamp, reset_ip varchar(96), created timestamp, created_by_id int8, updated timestamp, updated_by_id int8, action int4 default 3 not null, primary key (id));
create table permission (id int8 not null, name varchar(255), description varchar(255), type varchar(2) not null, updated timestamp, updated_by_id int8, category_id int8, created timestamp, primary key (id));
create table permission_category (id int8 not null, name varchar(255), description varchar(255), priority int4 not null, category_group_id int8, primary key (id));
create table pg_instance_collection (id int8 not null, guid varchar(255) not null, name varchar(96) not null, shared boolean not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table pg_tree_node (id int8 not null, guid varchar(255) not null, name varchar(96) not null, parameter_group_id int8, parent_node_id int8, pgi_collection_id int8, origin_object_id int8, dna varchar(255) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, content_object_id int8, data_type int4, user_id int8, last_editor_id int8, status_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table pod (id int8 not null, name varchar(96) not null, description varchar(255), url varchar(255) not null, sso_idp_id varchar(255), sso_secret_key varchar(255), internal_umh_ip varchar(255), internal_oracle_ip varchar(255), pod_master_schema varchar(96), pod_master_pwd varchar(96), web_services_user varchar(255), web_services_user_pwd varchar(255), type int4, status int4, enabled boolean not null, is_online boolean not null, scan_date timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table pod_status (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table pod_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table project (id int8 not null, guid varchar(255) not null, name varchar(255), description varchar(255), status_id int4, notice_status_id int4, metatags varchar(255), start_date timestamp, due_date timestamp, metadata_form_id int8, wf_instance_id int8, owner_id int8, created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, primary key (id));
create table project_applied_wf_steps (project_task_id int8 not null, list_order int4 not null, wf_step_id int8 not null, primary key (project_task_id, list_order));
create table project_step_req_note_map (project_task_id int8 not null, wf_step_id int8 not null, note varchar(255), primary key (project_task_id, wf_step_id));
create table project_task (id int8 not null, task_id int8, project_id int8, created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, primary key (id));
create table project_wf_step_user_map (project_task_id int8 not null, wf_step_id int8 not null, user_id int8, primary key (project_task_id, wf_step_id));
create table proof (id int8 not null, guid varchar(255) not null, is_staled boolean not null, is_active boolean not null, output_filename varchar(255), output_path varchar(255), request_date timestamp, completed_date timestamp, complete boolean, error boolean, has_data_error boolean, channel_context_id int8, proof_definition_id int8, external_id varchar(255), created timestamp, created_by_id int8, updated timestamp, updated_by_id int8, type int4 not null, content_object_id int8, context_zone_id int8, primary key (id));
create table proof_definition (id int8 not null, guid varchar(255) not null, recepient_range_from int4, recepient_range_to int4, language varchar(3) not null, status_id int4 not null, data_resource_id int8, tp_selection_id int8, created timestamp, created_by_id int8, updated timestamp, updated_by_id int8, primary key (id));
create table rat_hist_shared_content (rat_shared_content_id int8 not null, hist_rat_shared_content_id int8 not null, primary key (rat_shared_content_id, hist_rat_shared_content_id));
create table rat_hist_shared_content_form (rat_shared_content_id int8 not null, hist_metadata_form_id int8 not null, primary key (rat_shared_content_id, hist_metadata_form_id));
create table rate_schedule (id int8 not null, guid varchar(255) not null, name varchar(96) not null, start_date timestamp not null, end_date timestamp, description varchar(255), rate_schedule_collection_id int8, next_id int8, previous_id int8, first_page_weight int4, other_pages_weight int4, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table rate_schedule_collection (id int8 not null, guid varchar(255) not null, envelope_name varchar(96) not null, envelope_weight int4 not null, weight_unit_id int4 not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table rate_schedule_detail (id int8 not null, guid varchar(255) not null, rate int4 not null, weight int4 not null, rate_schedule_id int8, primary key (id));
create table rationalizer_app_nav_tree (rationalizer_app_id int8 not null, list_order int4 not null, form_item_def_id int8 not null, primary key (rationalizer_app_id, list_order));
create table rationalizer_app_tp_reference (id int8 not null, tp_guid varchar(255), rationalizer_app_id int8, tp_request_date timestamp, tp_requested_by_id int8, primary key (id));
create table rationalizer_app_visible_user (rationalizer_app_id int8 not null, user_id int8 not null, primary key (rationalizer_app_id, user_id));
create table rationalizer_application (id int8 not null, name varchar(255), description varchar(255), metatags varchar(255), full_visibility boolean, brand_profile_id int8, guid varchar(255) not null, app_sync_status int4 not null, combine_within_document boolean not null, linked_document_id int8, parsed_doc_metadata_def_id int8, parsed_content_metadata_def_id int8, parent_app_id int8, rationalizer_wf_id int8, created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, primary key (id));
create table rationalizer_document (id int8 not null, guid varchar(255) not null, name varchar(255), file_name varchar(1024), file_path varchar(1024), metatags varchar(255), description varchar(255), parsed_document_form_id int8, rationalizer_application_id int8, parent_doc_id int8, created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, primary key (id));
create table rationalizer_document_content (id int8 not null, guid varchar(255) not null, hash_code varchar(32) not null, name varchar(255), metatags varchar(255), text_content text, markup_content text, zone_connector varchar(4000), message_name varchar(4000), content_order int4, parsed_content_form_id int8, rationalizer_document_id int8, rationalizer_shared_content_id int8, parent_content_id int8, assignee_id int8, wf_action_id int8, status_type int4, created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, primary key (id));
create table rationalizer_filter_item_defs (rationalizer_application int8 not null, metadata_form_definition_id int8 not null, primary key (rationalizer_application, metadata_form_definition_id));
create table rationalizer_hist_content (rationalizer_content_id int8 not null, hist_rationalizer_content_id int8 not null, primary key (rationalizer_content_id, hist_rationalizer_content_id));
create table rationalizer_hist_form (rationalizer_content_id int8 not null, hist_metadata_form_id int8 not null, primary key (rationalizer_content_id, hist_metadata_form_id));
create table rationalizer_query (id int8 not null, name varchar(255), metatags varchar(255), description varchar(255), simple_comparision_value text, simple_search_value text, any_of_these_search_value text, all_of_these_search_value text, none_of_these_search_value text, other_settings text, query_type_id int4 not null, query_tab_id int4 not null, apply_comparison boolean not null, content_comparision_value text, comparison_threshold int4, comparison_threshold_max int4, similarity_algorithm_type_id int4, compare_to_all boolean not null, compare_results_to_all boolean not null, apply_grouping boolean not null, trim_whitespace boolean not null, exact_matches_only boolean not null, case_sensitive boolean not null, rationalizer_application_id int8, created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, primary key (id));
create table rationalizer_query_component (id int8 not null, target_data_type_id int4, comparator_type_id int4, metadata_connector_value varchar(255), comparision_value text, component_intra_operator_id int4, chain_previous_component boolean not null, document_filter_id int4 not null, created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, rationalizer_query_id int8, sequence int4, primary key (id));
create table rationalizer_shared_content (id int8 not null, name varchar(255), metatags varchar(255), description varchar(255), guid varchar(255) not null, text_content text, markup_content text, hash_code varchar(32) not null, parsed_content_form_id int8, rationalizer_application_id int8, parent_index_id int8, created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, primary key (id));
create table record_type (id int4 not null, name varchar(96), string_id varchar(255), primary key (id));
create table redirection_info (id int8 not null, domain int8 not null, production int8, transition int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table reference_connection (id int8 not null, guid varchar(255) not null, sha256_hash varchar(90), data_source_association_id int8 not null, reference_data_source_id int8, primary_key_variable_id int8, reference_key_variable_id int8, primary_compound_key_id int8, reference_compound_key_id int8, connector_parameter varchar(255), updated timestamp, created_by_id int8, created timestamp, updated_by_id int8, primary key (id));
create table reference_query (id int8 not null, object_class_name varchar(255) not null, direct_ref_class_name varchar(255), f_query varchar(4000), f_type varchar(8), primary key (id));
create table report_scenario (id int8 not null, name varchar(96), start_date timestamp, created timestamp, updated timestamp, complete boolean, error boolean, channel_context_id int8, updated_by_id int8, end_date timestamp, primary key (id));
create table report_scenario_delivery_event (report_id int8 not null, delivery_event_id int8 not null, primary key (report_id, delivery_event_id));
create table report_scenario_message (report_id int8 not null, content_object_id int8 not null, primary key (report_id, content_object_id));
create table rest_api_token (id int8 not null, guid varchar(256) not null, name varchar(256) not null, description varchar(256), updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, api_token varchar(256), clientSecret varchar(256), user_id int8, scopes varchar(4000), primary key (id));
create table role (id int8 not null, name varchar(255), description varchar(255), visibility varchar(2), active boolean, updated timestamp, hidden_flag boolean, updated_by_id int8, created timestamp, primary key (id));
create table role_permission (role_id int8 not null, permission_id int8 not null, primary key (role_id, permission_id));
create table sandbox_file (id int8 not null, sandbox_type int4, file_name varchar(255), content_type varchar(255), created timestamp, file_content BYTEA not null, primary key (id));
create table search_result_ids (id int8 not null, guid varchar(255) not null, list_item_ids text, user_id int8, expiry timestamp, created timestamp, primary key (id));
create table security_settings (id int8 not null, name varchar(96), updated timestamp, updated_by_id int8, created timestamp, min_length varchar(96), max_length varchar(96), requires_uppercase boolean, requires_lowercase boolean, requires_numeral boolean, requires_symbol boolean, no_repeats boolean, track_flag boolean, max_attempts varchar(96), alphanumeric_only boolean, user_id_min_length varchar(96), user_id_max_length varchar(96), pw_reset_keep_alive varchar(96), pw_expires boolean, pw_expire_days int4, prevent_repeated_pw boolean, pw_history_entries int4, pw_limit_reuse_period boolean not null, pw_limit_months int4 not null, session_expire_mins varchar(96), soft_deactivation_enabled boolean not null, soft_deactivation_limit_days int4 not null, hard_deactivation_enabled boolean not null, hard_deactivation_limit_dyas int4 not null, primary key (id));
create table sefas_configuration (id int8 not null, created timestamp, updated timestamp, connector_id int8, qualification_output_id int8, sha256_hash varchar(90), customer_driver_file varchar(255), execute_incloud_test boolean, execute_incloud_preview boolean, execute_incloud_proof boolean, execute_incloud_simulation boolean, pre_qualengine_script varchar(255), post_qualengine_script varchar(255), post_connector_script varchar(255), bundle_filenames_for_images boolean not null, filename_separator_for_images varchar(255), play_messages_on_empty_var boolean not null, override_remote_server boolean, remote_server_ip varchar(255), remote_server_port varchar(255), remote_server_user varchar(255), remote_server_password varchar(255), applied_de_version varchar(255), input_character_encoding int4, output_character_encoding int4, de_server_guid varchar(255), bundle_name_override varchar(255), output_filename_cplex_val_id int8, output_doc_title_cplex_val_id int8, validate_prod_bundle boolean, escape_driver_data_tags boolean not null, convert_table_border_px_to_pt boolean not null, eval_not_equal_on_missing_tag boolean not null, play_empty_agg_first_last_var boolean not null, remove_zero_from_style_con boolean not null, comp_time_parent_tagging boolean not null, data_group_expression_var_proc boolean not null, list_style_control_type_id int4, color_output_format_type_id int4, script_var_applies_undefined boolean not null, correct_paragraph_text_styles boolean not null, fix_inline_targeting_styles boolean not null, preserve_data_whitespace boolean not null, nbsp_composed_as_space boolean not null, normalize_image_library boolean not null, normalize_embedded_content boolean not null, gmc_span_to_t_tag boolean not null, blue_underline_links boolean not null, use_default_image int4, updated_by_id int8, composition_file_set_id int8, composition_version varchar(255), output_file_type int4, starts_on_odd_page boolean, duplex_output boolean, template_control boolean, accessibility boolean, linespace_position int4, table_padding_in_pts boolean, primary key (id));
create table seg_analysis_analyzables (seg_analysis_id int8 not null, model_type int4 not null, model_id int8 not null, primary key (seg_analysis_id, model_type, model_id));
create table segmentation_analysis (id int8 not null, document_id int8, output_filename varchar(255), output_path varchar(255), updated timestamp, created timestamp, completed_date timestamp, request_date timestamp, updated_by_id int8, user_id int8, complete boolean, error boolean, channel_context_id int8, primary key (id));
create table sendmail_configuration (id int8 not null, created timestamp, updated timestamp, connector_id int8, qualification_output_id int8, sha256_hash varchar(90), customer_driver_file varchar(255), execute_incloud_test boolean, execute_incloud_preview boolean, execute_incloud_proof boolean, execute_incloud_simulation boolean, pre_qualengine_script varchar(255), post_qualengine_script varchar(255), post_connector_script varchar(255), bundle_filenames_for_images boolean not null, filename_separator_for_images varchar(255), play_messages_on_empty_var boolean not null, override_remote_server boolean, remote_server_ip varchar(255), remote_server_port varchar(255), remote_server_user varchar(255), remote_server_password varchar(255), applied_de_version varchar(255), input_character_encoding int4, output_character_encoding int4, de_server_guid varchar(255), bundle_name_override varchar(255), output_filename_cplex_val_id int8, output_doc_title_cplex_val_id int8, validate_prod_bundle boolean, escape_driver_data_tags boolean not null, convert_table_border_px_to_pt boolean not null, eval_not_equal_on_missing_tag boolean not null, play_empty_agg_first_last_var boolean not null, remove_zero_from_style_con boolean not null, comp_time_parent_tagging boolean not null, data_group_expression_var_proc boolean not null, list_style_control_type_id int4, color_output_format_type_id int4, script_var_applies_undefined boolean not null, correct_paragraph_text_styles boolean not null, fix_inline_targeting_styles boolean not null, preserve_data_whitespace boolean not null, nbsp_composed_as_space boolean not null, normalize_image_library boolean not null, normalize_embedded_content boolean not null, gmc_span_to_t_tag boolean not null, blue_underline_links boolean not null, use_default_image int4, updated_by_id int8, customer_email_address_var_id int8, override_smtp boolean, smtp_host varchar(255), smtp_port varchar(255), smtp_security varchar(255), smtp_account varchar(255), smtp_password varchar(255), smtp_custom_header varchar(512), primary key (id));
create table services (id int8 not null, service_type int4 not null, service_sub_type int4, service_status int4 not null, guid varchar(255) not null, name varchar(255) not null, description varchar(4000), api_identifier varchar(255), client_id varchar(255), client_secret varchar(255), allowed_origins varchar(4000), allowed_redirect_urls varchar(4000), allowed_logout_redirect_urls varchar(4000), token_expiration int4, rotation_enabled boolean, reuse_interval int4, absolute_expiration_enabled boolean, absolute_expiration int4, inactivity_expiration_enabled boolean, inactivity_expiration int4, allowed_domains varchar(4000), database_file_id int8, scopes varchar(4000), translation_provider_id int8, last_used timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table simulation (id int8 not null, name varchar(96), run_date timestamp, request_date timestamp, completed_date timestamp, user_id int8, workgroup_id int8, output_filename varchar(255), output_path varchar(255), document_id int8, data_resource_id int8, use_all_in_process boolean, tp_collection_id int8, is_customer_level boolean, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table simulation_content_object (simulation_id int8 not null, content_object_id int8 not null, primary key (simulation_id, content_object_id));
create table source_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table status_polling_background_task (id int8 not null, guid varchar(255) not null, type int4, name varchar(255), description varchar(4000), is_visible boolean not null, refresh_page boolean, host_ip_address varchar(255), host_name varchar(255), thread_id int8, progress_in_percent int4, is_active boolean not null, is_staled boolean not null, has_input_data_error boolean, target_object_id int8, complete boolean, error boolean, output_filename varchar(255), output_path varchar(255), request_date timestamp, completed_date timestamp, channel_context_id int8, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table sub_content_type (id int8 not null, name varchar(96), description varchar(255), parent_content_id int8, primary key (id));
create table sync_history (id int8 not null, document_id int8 not null, object_id int8 not null, object_type_id int4 not null, hide_until_next_change boolean default false not null, target_is_null boolean default false, instance_guid varchar(255), sync_type_id int4 not null, sync_time timestamp not null, sync_by_id int8 not null, source_document_id int8 not null, source_object_id int8 not null, source_model_hash varchar(90) not null, source_archived_copy_hash varchar(90), source_active_copy_hash varchar(90), source_working_copy_hash varchar(90), source_archived_copy_attr_hash varchar(90), source_active_copy_attr_hash varchar(90), source_working_copy_attr_hash varchar(90), target_model_hash varchar(90), target_archived_copy_hash varchar(90), target_active_copy_hash varchar(90), target_working_copy_hash varchar(90), target_archived_copy_attr_hash varchar(90), target_active_copy_attr_hash varchar(90), target_working_copy_attr_hash varchar(90), source_archived_copy_lang_hash int8, source_active_copy_lang_hash int8, source_working_copy_lang_hash int8, target_archived_copy_lang_hash int8, target_active_copy_lang_hash int8, target_working_copy_lang_hash int8, primary key (id));
create table sync_history_locales (sync_history_id int8 not null, messagepoint_locale_id int8);
create table sync_object (id int8 not null, sync_process_id int8, sync_object_type int4, source_object_id int8, target_object_id int8, source_object_dna varchar(255), created timestamp, updated timestamp, error_message varchar(8000), sync_state int4, primary key (id));
create table sync_process (id int8 not null, user_id int8 not null, created timestamp, updated timestamp, error_message varchar(4000), state int4, primary key (id));
create table sync_request (id int8 not null, sync_process_id int8 not null, source_document_id int8 not null, source_instance_id int8 not null, target_document_id int8 not null, target_instance_id int8 not null, user_id int8 not null, status_polling_task_id int8 not null, force_sync boolean not null, hide_until_next boolean not null, log_differences boolean not null, total_selected_objects int8, total_languages int4, guid varchar(255) not null, created timestamp, updated timestamp, primary key (id));
create table sync_worker (id int8 not null, sync_process_id int8, created timestamp, updated timestamp, sync_state int4 not null, worker_type int4 not null, objects_processed int8, objects_errored_out int8, objects_skipped int8, total_selected_objects int8, primary key (id));
create table system_notification (id int8 not null, guid varchar(255) not null, name varchar(96), number_of_prompts int4, message varchar(4000), active boolean, start_time timestamp, end_time timestamp, created timestamp, created_by_id int8, updated timestamp, updated_by_id int8, primary key (id));
create table system_property (id int8 not null, prop_key varchar(255), prop_value varchar(1020), prop_desc varchar(255), readonly boolean, primary key (id));
create table system_state (id int8 not null, name varchar(96), string_code varchar(255), description varchar(255), bit_flag int8, primary key (id));
create table system_theme (id int8 not null, name varchar(96), folder varchar(255), uploaded boolean, primary key (id));
create table tag (id int8 not null, guid varchar(255) not null, name varchar(96) not null, connector_name varchar(96), content varchar(255), tag_content int8, priority int4, max_cap int4, usage_type_id int4 not null, injection_location_type_id int4 not null, tag_type_id int8, owner_user_id int8, document_id int8, status_id int8 not null, start_date timestamp, end_date timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, include_tg_relation int4, excluded_tg_relation int4, extended_tg_relation int4, primary key (id));
create table tag_cloud (id int8 not null, name varchar(255), type_id int8, document_id int8, rationlizer_application_id int8, status int4, primary key (id));
create table tag_cloud_type (id int4 not null, name varchar(150), primary key (id));
create table tag_content_object (tag_id int8 not null, content_object_id int8 not null, primary key (tag_id, content_object_id));
create table tag_document (tag_id int8 not null, document_id int8 not null, primary key (tag_id, document_id));
create table tag_insert (tag_id int8 not null, insert_id int8 not null, primary key (tag_id, insert_id));
create table tag_target_group_excluded (tag_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (tag_id, sequence));
create table tag_target_group_extended (tag_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (tag_id, sequence));
create table tag_target_group_included (tag_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (tag_id, sequence));
create table tag_tg_instance_map (tg_id int8 not null, tag_id int8 not null, instance_id int8 not null, primary key (tg_id, tag_id));
create table tag_touchpoint_collection (tag_id int8 not null, touchpoint_collection_id int8 not null, primary key (tag_id, touchpoint_collection_id));
create table tag_type (id int8 not null, guid varchar(255) not null, name varchar(96) not null, connector_name varchar(96), updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table target_group (id int8 not null, name varchar(255), metatags varchar(255), guid varchar(255) not null, condition_relationship int4, instance_id int8 not null, updated timestamp, updated_by_id int8, created timestamp, segmentation_data varchar(4000), dna varchar(255) not null, sha256_hash varchar(90), primary key (id));
create table target_group_document (target_group_id int8 not null, document_id int8 not null, primary key (target_group_id, document_id));
create table target_group_instance (id int8 not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, guid varchar(255) not null, search_value text, primary key (id));
create table task (id int8 not null, guid varchar(255) not null, status_id int8, notice_status_id int4, task_type int4, metatags varchar(255), due_date timestamp, near_term_hours int4, reporter_id int8, requirement BYTEA, metadata_form_id int8, wf_action_id int8, asset_wf_id int8, project_task_id int8, messagepoint_locale_id int8, content_id int8, content_sha256_hash varchar(255), auto_created boolean, pg_tree_node_dna varchar(255), created timestamp, updated timestamp, created_by_id int8, updated_by_id int8, primary key (id));
create table task_document (task_id int8 not null, document_id int8 not null, primary key (task_id, document_id));
create table task_item (task_id int8 not null, model_type int4 not null, model_id int8 not null, primary key (task_id, model_type, model_id));
create table task_user (task_id int8 not null, user_id int8 not null, primary key (task_id, user_id));
create table template_modifier (id int8 not null, name varchar(255), connector_name varchar(255), is_active boolean, template_managed boolean, value_type int4, referencing_tp_selection_id int8, tp_selection_id int8, document_id int8, complex_value_id int8, primary key (id));
create table template_variant (id int8 not null, guid varchar(255) not null, name varchar(255), document_id int8, primary key (id));
create table tenant_metadata (id int8 not null, model_signature varchar(255), external_id varchar(255), internal_id int8, primary key (id));
create table tenant_permission (tenant_id int8 not null, permission_id int8 not null, primary key (tenant_id, permission_id));
create table tenant_theme_info (tenant_id int8 not null, guid varchar(255) not null, name varchar(96), logo_location varchar(255), header_text text, provider_logo_location varchar(255), provider_text text, header_theme_color text not null, header_theme_type_id int4 not null, system_theme int8, background_theme int8, primary key (tenant_id));
create table tenants (id int8 not null, name varchar(96) not null, code varchar(255) not null, parent_id int8, contact_name varchar(64), contact_title varchar(16), contact_email varchar(96), contact_street_address varchar(128), contact_suit_unit varchar(16), contact_city varchar(32), contact_country varchar(50), contact_province_state varchar(50), contact_postal_zip_code varchar(10), contact_web_site varchar(96), enabled boolean not null, primary key (id));
create table test_scenario (id int8 not null, guid varchar(255) not null, name varchar(255), start_date timestamp, created timestamp, updated timestamp, complete boolean, error boolean, updated_by_id int8, request_date timestamp, last_run_date timestamp, completed_date timestamp, user_id int8, workgroup_id int8, output_filename varchar(255), output_path varchar(255), document_id int8, data_resource_id int8, use_all_in_process boolean, use_all_in_process_lib boolean, use_objects_in_workflow boolean, debug_mode boolean, pdf_annotations boolean, delivery_event_type_id int4, delivery_event_schedule_id int8, composition_file_set_id int8, tp_collection_id int8, test_suite_id int8, channel_context_id int8, de_server_guid varchar(255), bundle_name_override varchar(255), br_target_folder varchar(255), auto_qa_send boolean, primary key (id));
create table test_scenario_content_object (test_id int8 not null, content_object_id int8 not null, primary key (test_id, content_object_id));
create table test_suite (id int8 not null, guid varchar(255) not null, name varchar(96), user_id int8, created timestamp, updated timestamp, complete boolean, error boolean, updated_by_id int8, request_date timestamp, completed_date timestamp, bundle_name_override varchar(255), delivery_event_type_id int4, delivery_event_schedule_id int8, last_run_date timestamp, auto_qa_send boolean, primary key (id));
create table text_style (id int8 not null, identifier varchar(96), name varchar(96), connector_name varchar(96), color varchar(255), font_name varchar(96), web_font_name varchar(255), point_size int4, bold boolean, underline boolean, italic boolean, tagging_override varchar(2048), toggle_bold boolean not null, toggle_underline boolean not null, toggle_italic boolean not null, toggle_point_size boolean not null, toggle_color boolean not null, sha256_hash varchar(90), font_id int8, apply_font boolean, style_set_defaults varchar(2048), updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table text_style_colors (text_style_id int8 not null, value varchar(255));
create table text_style_cust (id int8 not null, connector_name varchar(96), color varchar(255), font_name varchar(96), web_font_name varchar(255), point_size int4, bold boolean, underline boolean, italic boolean, tagging_override varchar(2048), toggle_bold boolean not null, toggle_underline boolean not null, toggle_italic boolean not null, toggle_point_size boolean not null, toggle_color boolean not null, font_id int8, apply_font boolean, master_text_style_id int8, origin_object_id int8, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table text_style_cust_colors (text_style_cust_id int8 not null, value varchar(255));
create table text_style_cust_map (text_style_id int8 not null, document_id int8 not null, text_style_cust_id int8 not null, primary key (text_style_id, document_id));
create table text_style_cust_point_sizes (text_style_cust_id int8 not null, value varchar(255));
create table text_style_font (id int8 not null, name varchar(255), sha256_hash varchar(90), guid varchar(255) not null, ttf_file_id int8, eot_file_id int8, ttf_bold_file_id int8, ttf_italic_file_id int8, ttf_bold_italic_file_id int8, primary key (id));
create table text_style_point_sizes (text_style_id int8 not null, value varchar(255));
create table text_style_transformation_profile (id int8 not null, name varchar(96), transform_default_styles boolean, type int4 not null, primary key (id));
create table touchpoint_collection (id int8 not null, guid varchar(255) not null, name varchar(96), description varchar(4000), enabled boolean not null, is_executable boolean, metadata_form_id int8, composition_file_set_id int8, output_filename_cplex_val_id int8, output_doc_title_cplex_val_id int8, primary key (id));
create table touchpoint_language (id int8 not null, name varchar(96), is_default_langauge boolean, document_id int8, messagepoint_locale_id int8, touchpoint_locale_id int8, primary key (id));
create table touchpoint_locale (id int8 not null, dictionary varchar(255), date_format varchar(255), currency_symbol varchar(255), suffix_currency_symbol boolean, thousands_separator varchar(255), decimal_symbol varchar(255), number_of_decimals varchar(255), boolean_symbol_true varchar(255), boolean_symbol_false varchar(255), short_month_names varchar(255), long_month_names varchar(255), drop_decimal_for_whole_num boolean not null, master_locale_id int8, primary key (id));
create table touchpoint_selection (id int8 not null, guid varchar(255) not null, archive_type_id int4, fully_visible boolean default true not null, connected_fully_visible boolean default true not null, own_content_ready boolean, assignee_id int8, document_id int8, pg_tree_node_id int8, wf_id int8, connected_wf_id int8, wf_action_id int8, content_last_updated timestamp, content_last_updated_by_id int8, latest_production_date timestamp, template_variant_id int8, origin_object_id int8, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, segmentation_data varchar(4000), alternate_layout_id int8, inherit_metadata boolean not null, metadata_form_id int8, dna varchar(255) not null, sha256_hash varchar(90), attributes_hash varchar(90), active_copy_hash varchar(90), ac_language_content_hash_id int8, working_copy_hash varchar(90), wc_language_content_hash_id int8, primary key (id));
create table touchpoint_selection_workgroup (tp_selection_id int8 not null, workgroup_id int8 not null, primary key (tp_selection_id, workgroup_id));
create table touchpoint_targeting (id int8 not null, guid varchar(255) not null, document_id int8 not null, origin_object_id int8, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, include_tg_relation int4, excluded_tg_relation int4, extended_tg_relation int4, primary key (id));
create table touchpoint_tg_instance_map (tg_id int8 not null, touchpoint_targeting_id int8 not null, instance_id int8 not null, primary key (tg_id, touchpoint_targeting_id));
create table tp_collection_touchpoint (id int8 not null, tp_order int4 not null, collection_id int8, document_id int8, primary key (id));
create table tp_delivery_report_doc (tp_delivery_report_id int8 not null, document_id int8 not null, primary key (tp_delivery_report_id, document_id));
create table tp_delivery_report_event (report_id int8 not null, delivery_event_id int8 not null, primary key (report_id, delivery_event_id));
create table tp_delivery_report_scenario (id int8 not null, name varchar(96), start_date timestamp, created timestamp, updated timestamp, complete boolean, error boolean, updated_by_id int8, end_date timestamp, xml_path varchar(255), report_path varchar(255), primary key (id));
create table tp_instance_locales (tpivm_id int8 not null, messagepoint_locale_id int8 not null, primary key (tpivm_id, messagepoint_locale_id));
create table tp_instance_visibility_map (id int8 not null, tp_guid varchar(255) not null, dcs_guid varchar(255) not null, instance_guid varchar(255) not null, primary key (id));
create table tp_sel_visible_user (tp_selection_id int8 not null, user_id int8 not null, primary key (tp_selection_id, user_id));
create table tp_target_group_excluded (touchpoint_targeting_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (touchpoint_targeting_id, sequence));
create table tp_target_group_extended (touchpoint_targeting_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (touchpoint_targeting_id, sequence));
create table tp_target_group_included (touchpoint_targeting_id int8 not null, sequence int4 not null, target_group_id int8 not null, primary key (touchpoint_targeting_id, sequence));
create table translation_provider (id int8 not null, name varchar(255), code varchar(255), name_code varchar(255), primary key (id));
create table tstp_language (tstp_id int8 not null, messagepoint_locale_id int8 not null, primary key (tstp_id, messagepoint_locale_id));
create table user_permission (user_id int8 not null, permission_id int8 not null, primary key (user_id, permission_id));
create table user_role (user_id int8 not null, role_id int8 not null, primary key (user_id, role_id));
create table users (id int8 not null, guid varchar(255) not null, email varchar(96), notes varchar(4000), pinc_permissions varchar(4000), password varchar(255), user_id varchar(255), passwordexpired boolean default false, first_name varchar(255), last_name varchar(255), isactive boolean default true not null, isenabled boolean default true not null, email_verified boolean default false not null, soft_deactivated boolean not null, default_tab_id int4 default 13, email_notify_daily boolean default true, email_notify_realtime boolean default true, apply_notification_digest boolean default false, email_token varchar(255), webdav_token varchar(255), salt varchar(255), app_locale_id int8, updated timestamp, updated_by_id int8, created timestamp, last_login timestamp, invalid_signins int4 not null, deactiv_reason_id int8, context_attributes varchar(4000), default_list_filter_id int4, insert_list_filter_id int4, insert_schedule_list_filter_id int4, tp_selection_list_filter_id int4, tp_cont_seln_list_filter_id int4, tag_list_filter_id int4, testing_list_filter_id int4, variable_list_filter_id int4, report_list_filter_id int4, workgroup_id int8, supervisor_id int8, hidden_supervisor boolean, password_last_updated timestamp, master_admin_status int4 not null, idp_type int4 not null, idp_identifier varchar(255), idp_subject varchar(255), idp_user_guid varchar(255), idp_user_groups varchar(255), idp_mp_domain varchar(255), idp_mp_dcs_schema varchar(255), idp_mp_pod_url varchar(255), idp_mp_domain_pod_id int8, default_node_id int8 default 0 not null, licensed_type int4 not null, primary key (id));
create table variable_data_element_map (id int8 not null, de_id int8, op_id int4, data_record_level_id int4, primary key (id));
create table variable_document (variable_id int8 not null, document_id int8 not null, primary key (variable_id, document_id));
create table version_activity_reason (id int8 not null, name varchar(96), description varchar(255), string_code varchar(255), primary key (id));
create table version_status (id int8 not null, name varchar(96), string_code varchar(255), description varchar(255), primary key (id));
create table web_service_config (id int8 not null, guid varchar(255) not null, url varchar(255), user_id varchar(255), password varchar(255), de_server_guid varchar(255), primary key (id));
create table workflow (id int8 not null, name varchar(96), guid varchar(255) not null, wf_model_type int4 not null, wf_usage_type int4, document_id int8, created timestamp, created_by_id int8, updated timestamp, updated_by_id int8, primary key (id));
create table workflow_action (id int8 not null, model_type int4 not null, model_id int8 not null, wf_step_id int8, previous_action_id int8, next_action_id int8, is_active boolean default false not null, is_due_by_notified boolean default false not null, release_for_approval_date timestamp, created timestamp, created_by_id int8, updated timestamp, updated_by_id int8, request_guid varchar(255), service_code int4, primary key (id));
create table workflow_action_history (id int8 not null, wf_action_id int8, model_type int4 not null, model_id int8 not null, model_data_guid varchar(255), action int4, state_prior int4, state_current int4, action_type int4, hc_step_name varchar(255), user_id int8, assigned_to int8, action_date timestamp, notes varchar(255), primary key (id));
create table workflow_instance (id int8 not null, guid varchar(255) not null, workflow_id int8, status_id int8, owner int8, created timestamp, created_by_id int8, updated timestamp, updated_by_id int8, primary key (id));
create table workflow_library_document (workflow_library_id int8 not null, document_id int8 not null, primary key (workflow_library_id, document_id));
create table workflow_position (id int8 not null, workflow_id int8 not null, workflow_state_id int8 not null, required boolean default true, requires_permission boolean default false, enabled boolean, name varchar(255) not null, authorization_type varchar(2) not null, previous int8, next int8, primary key (id));
create table workflow_position_permission (workflow_position_id int8 not null, permission_id int8 not null, primary key (workflow_position_id, permission_id));
create table workflow_position_user (workflow_position_id int8 not null, user_id int8 not null, primary key (workflow_position_id, user_id));
create table workflow_property (id int8 not null, workflow_position_id int8 not null, required boolean default true, name varchar(255) not null, primary key (id));
create table workflow_state (id int8 not null, name varchar(96), string_id varchar(255), primary key (id));
create table workflow_step (id int8 not null, guid varchar(255) not null, state varchar(255) not null, is_order_editable boolean not null, is_mandatory boolean not null, is_approval boolean not null, removed boolean default false not null, approve_type int4 not null, wf_instance_id int8, previous_step_id int8, next_step_id int8, step_type int4 not null, translator_type int4 not null, allow_edit_default boolean not null, ts_trans_profile_id int8, service_guid varchar(255), start_date timestamp, end_date timestamp, frequency_type_id int4, is_dueby boolean not null, dueby_type int4, created timestamp, created_by_id int8, updated timestamp, updated_by_id int8, primary key (id));
create table workflow_step_langs (wf_step_id int8 not null, lang_id int8 not null, primary key (wf_step_id, lang_id));
create table workflow_step_subwfs (wf_step_id int8 not null, wf_id int8 not null, primary key (wf_step_id, wf_id));
create table workflow_tab (id int8 not null, workflow_position_id int8 not null, name varchar(255) not null, parameter varchar(255) not null, string_id varchar(255), authorization_type varchar(2) not null, edit_url varchar(255), view_url varchar(255), list_url varchar(255), visibility_toggle_attr varchar(255), required_id int8, primary key (id));
create table workflow_tab_permission (workflow_tab_id int8 not null, permission_id int8 not null, primary key (workflow_tab_id, permission_id));
create table workgroup (id int8 not null, name varchar(96) not null, description varchar(255), default_workgroup boolean not null, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table workgroup_zone (zone_id int8 not null, workgroup_id int8 not null, primary key (workgroup_id, zone_id));
create table xml_data_tag_definition (id int8 not null, data_source_id int8, tag_name varchar(256), start_customer boolean, start_data_group boolean, break_indicator varchar(64), repeating boolean, updated timestamp, created timestamp, updated_by_id int8, created_by_id int8, parent_tag_id int8, data_group_id int8, dna varchar(255) not null, primary key (id));
create table zone (id int8 not null, name varchar(96), metatags varchar(255), override_name boolean, friendly_name varchar(255), override_friendly_name boolean, description varchar(255), parent_zone_id int8, content_type_id int8, sub_content_type_id int8, zone_type_id int4, enabled boolean, override_enabled boolean, origin_object_id int8, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, top_x int4, top_y int4, override_position boolean, width int4, height int4, sequence int4, override_dimensions boolean, page int4, rotation_angle int4 not null, background_color varchar(48), override_background_color boolean, default_canvas_width int4, can_flow boolean, can_grow boolean, repeats boolean, repeating_zone_type_id int4, minimum_size boolean, absolute_positioning boolean not null, rendered_container_type_id int4 not null, image_dimensions_type_id int4 not null, enforce_min_height boolean not null, split_tables boolean not null, default_text_style_id int8, override_default_text_style boolean, starter_text_style_id int8, default_paragraph_style_id int8, override_default_para_style boolean, default_list_style_id int8, override_default_list_style boolean, dxf_output boolean not null, html_output boolean not null, supports_tables boolean not null, supports_forms boolean not null, supports_barcodes boolean not null, supports_custom_paragraphs boolean not null, is_freeform boolean not null, export_to_single_message boolean not null, is_flow_zone boolean not null, flow_into_zone_id int8, vertical_alignment_id int4 not null, is_backer boolean, vertically_relative_to int4 not null, relative_distance int4 not null, minimum_keep_together int4, data_group_id int8, mixed_data_groups boolean not null, document_id int8, document_section_id int8, override_document_section boolean, template_image_id int8, template_smart_text_id int8, comm_data_image_var_id int8, permit_comm_content_edit boolean not null, override_comm_template boolean, restrict_shared_assets boolean not null, override_shared_assets boolean, permit_message_content_edit boolean not null, apply_alt_text boolean, apply_image_link boolean, apply_image_ext_link boolean, apply_image_ext_path boolean, override_text_styles boolean, override_paragraph_styles boolean, override_list_styles boolean, override_workgroups boolean, dna varchar(255) not null, sha256_hash varchar(90), primary key (id));
create table zone_attributes (zone_id int8 not null, attribute_name varchar(255) not null, attribute_value varchar(255) not null, primary key (zone_id, attribute_name));
create table zone_image_assets (zone_id int8 not null, image_id int8 not null, primary key (zone_id, image_id));
create table zone_list_style (zone_id int8 not null, list_style_id int8 not null, primary key (zone_id, list_style_id));
create table zone_paragraph_style (zone_id int8 not null, paragraph_style_id int8 not null, primary key (zone_id, paragraph_style_id));
create table zone_part (id int8 not null, parent_zone_part_id int8, name varchar(96), override_name boolean, width int4, height int4, override_dimensions boolean, topx int4, topy int4, override_position boolean, optional boolean, sequence int4, zone_id int8 not null, content_type int8, sub_content_type_id int8, origin_object_id int8, dna varchar(255) not null, checkout_timestamp timestamp, checkin_timestamp timestamp, updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, sha256_hash varchar(90), primary key (id));
create table zone_smart_text_assets (zone_id int8 not null, smart_text_id int8 not null, primary key (zone_id, smart_text_id));
create table zone_style (zone_id int8 not null, style_id int8 not null, primary key (zone_id, style_id));
alter table attachment add constraint uc_guid_attachment unique (guid);
alter table audit_report add constraint uc_guid_audit_report unique (guid);
alter table branch add constraint uc_guid_branch unique (guid);
alter table branch add constraint uc_dcs_schema_name_branch unique (dcs_schema_name);
alter table communication add constraint uc_guid_communication unique (guid);
alter table complex_value add constraint uc_guid_complex_value unique (guid);
alter table composition_file_set add constraint uc_2094031055_674441748 unique (document_id, tp_collection_id, dna);
alter table compound_key add constraint uc_guid_compound_key unique (guid);
alter table compound_key_item add constraint uc_guid_compound_key_item unique (guid);
alter table condition_element add constraint uc_guid_condition_element unique (guid);
alter table condition_item add constraint uc_guid_condition_item unique (guid);
alter table condition_subelement add constraint uc_guid_condition_subelement unique (guid);
alter table content_object add constraint uc_document_id_dna_375726533 unique (document_id, dna);
alter table content_object add constraint uc_guid_content_object unique (guid);
alter table content_object_data add constraint uc_guid_content_object_data unique (guid);
alter table content_object_zone_priority add constraint uc_guid_584982563 unique (guid);
alter table content_targeting add constraint uc_guid_content_targeting unique (guid);
alter table data_element_core add constraint uc_dna_data_element_core unique (dna);
alter table data_element_variable add constraint uc_guid_data_element_variable unique (guid);
alter table data_element_variable add constraint uc_dna_data_element_variable unique (dna);
alter table data_file add constraint uc_dna_data_file unique (dna);
alter table data_record add constraint uc_dna_data_record unique (dna);
alter table data_resource add constraint uc_697479807_235144451 unique (document_id, dna, tp_collection_id);
alter table data_source add constraint uc_dna_data_source unique (dna);
alter table data_source_association add constraint uc_dna_data_source_association unique (dna);
alter table deserver add constraint uc_guid_deserver unique (guid);
alter table document add constraint uc_guid_document unique (guid);
alter table document_section add constraint uc_document_id_dna_1691178047 unique (document_id, dna);
alter table document_section add constraint uc_guid_document_section unique (guid);
alter table domain add constraint uc_name_domain unique (name);
alter table folders add constraint uc_guid_folders unique (guid);
alter table formatting_selection add constraint uc_guid_formatting_selection unique (guid);
alter table hist_metadata_form add constraint uc_guid_hist_metadata_form unique (guid);
alter table insert_obj add constraint uc_guid_insert_obj unique (guid);
alter table insert_schedule add constraint uc_guid_insert_schedule unique (guid);
alter table insert_schedule_bin_assignment add constraint uc_guid_1927042009 unique (guid);
alter table insert_schedule_collection add constraint uc_guid_1675987264 unique (guid);
alter table json_data_definition add constraint uc_dna_json_data_definition unique (dna);
alter table language_selection add constraint uc_guid_language_selection unique (guid);
alter table licence add constraint uc_guid_licence unique (guid);
alter table licence_audit_action add constraint uc_name_licence_audit_action unique (name);
alter table licence_customer add constraint uc_guid_licence_customer unique (guid);
alter table licence_customer add constraint uc_name_licence_customer unique (name);
alter table licence_module add constraint uc_guid_licence_module unique (guid);
alter table licence_representative add constraint uc_guid_licence_representative unique (guid);
alter table licence_representative add constraint uc_name_licence_representative unique (name);
alter table lookup_table add constraint uc_guid_lookup_table unique (guid);
alter table lookup_table add constraint uc_dna_lookup_table unique (dna);
alter table lookup_table_instance add constraint uc_guid_lookup_table_instance unique (guid);
alter table messagepoint_locale add constraint uc_code_messagepoint_locale unique (code);
alter table metadata_form add constraint uc_guid_metadata_form unique (guid);
alter table metadata_form_definition add constraint uc_guid_1260596894 unique (guid);
alter table node add constraint uc_guid_node unique (guid);
alter table parameter add constraint uc_guid_parameter unique (guid);
alter table parameter_group add constraint uc_guid_parameter_group unique (guid);
alter table parameter_group_instance add constraint uc_guid_10780245 unique (guid);
alter table parameter_group_item add constraint uc_guid_parameter_group_item unique (guid);
alter table password_recovery add constraint uc_reset_key_password_recovery unique (reset_key);
alter table pg_instance_collection add constraint uc_guid_pg_instance_collection unique (guid);
alter table pg_tree_node add constraint uc_guid_pg_tree_node unique (guid);
alter table pod add constraint uc_name_pod unique (name);
alter table pod add constraint uc_url_pod unique (url);
alter table project add constraint uc_guid_project unique (guid);
alter table proof add constraint uc_guid_proof unique (guid);
alter table proof_definition add constraint uc_guid_proof_definition unique (guid);
alter table rate_schedule add constraint uc_guid_rate_schedule unique (guid);
alter table rate_schedule_collection add constraint uc_guid_948135783 unique (guid);
alter table rate_schedule_detail add constraint uc_guid_rate_schedule_detail unique (guid);
alter table rationalizer_application add constraint uc_guid_1477266083 unique (guid);
alter table rationalizer_document add constraint uc_guid_rationalizer_document unique (guid);
alter table rationalizer_document_content add constraint uc_guid_1594405224 unique (guid);
alter table rationalizer_shared_content add constraint uc_guid_1056476018 unique (guid);
alter table redirection_info add constraint uc_domain_redirection_info unique (domain);
alter table rest_api_token add constraint uc_guid_rest_api_token unique (guid);
alter table search_result_ids add constraint uc_guid_search_result_ids unique (guid);
alter table services add constraint uc_guid_services unique (guid);
alter table status_polling_background_task add constraint uc_guid_404367405 unique (guid);
alter table system_notification add constraint uc_guid_system_notification unique (guid);
alter table tag add constraint uc_guid_tag unique (guid);
alter table tag_type add constraint uc_guid_tag_type unique (guid);
alter table target_group add constraint uc_guid_target_group unique (guid);
alter table target_group add constraint uc_dna_target_group unique (dna);
alter table task add constraint uc_guid_task unique (guid);
alter table template_variant add constraint uc_guid_template_variant unique (guid);
alter table tenant_theme_info add constraint uc_guid_tenant_theme_info unique (guid);
alter table test_scenario add constraint uc_guid_test_scenario unique (guid);
alter table test_suite add constraint uc_guid_test_suite unique (guid);
alter table text_style_font add constraint uc_guid_text_style_font unique (guid);
alter table touchpoint_collection add constraint uc_guid_touchpoint_collection unique (guid);
alter table touchpoint_selection add constraint uc_document_id_dna_408229762 unique (document_id, dna);
alter table touchpoint_selection add constraint uc_guid_touchpoint_selection unique (guid);
alter table touchpoint_targeting add constraint uc_guid_touchpoint_targeting unique (guid);
alter table users add constraint uc_guid_users unique (guid);
alter table workflow add constraint uc_guid_workflow unique (guid);
alter table workflow_instance add constraint uc_guid_workflow_instance unique (guid);
alter table workflow_step add constraint uc_guid_workflow_step unique (guid);
alter table xml_data_tag_definition add constraint uc_dna_xml_data_tag_definition unique (dna);
alter table zone add constraint uc_document_id_dna_zone unique (document_id, dna);
alter table zone_part add constraint uc_zone_id_dna_zone_part unique (zone_id, dna);
alter table action_entry add constraint fk_action_entry_text_style_from_id foreign key (text_style_from_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table action_entry add constraint fk_action_entry_text_style_to_id foreign key (text_style_to_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table action_entry add constraint fk_action_entry_tstp_id foreign key (tstp_id) references text_style_transformation_profile on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table application_locale add constraint fk_application_locale_mp_locale_id foreign key (mp_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table approval add constraint fk_approval_state_id foreign key (state_id) references workflow_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table approval_detail add constraint fk_approval_detail_wf_action_id foreign key (wf_action_id) references workflow_action on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table approval_user add constraint fk_approval_user_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table approval_user add constraint fk_approval_user_wf_step_id foreign key (wf_step_id) references workflow_step on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attachment add constraint fk_attachment_recipient_name_value_id foreign key (recipient_name_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attachment add constraint fk_attachment_recipient_location_value_id foreign key (recipient_location_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attachment_document add constraint fk_attachment_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attachment_document add constraint fk_attachment_document_attachment_id foreign key (attachment_id) references attachment on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attachment_tg_instance_map add constraint fk_attachment_tg_instance_map_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attachment_tg_instance_map add constraint fk_attachment_tg_instance_map_tg_id foreign key (tg_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attchmnt_target_group_excluded add constraint fk_attchmnt_target_group_excluded_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attchmnt_target_group_excluded add constraint fk_attchmnt_target_group_excluded_attachment_id foreign key (attachment_id) references attachment on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attchmnt_target_group_extended add constraint fk_attchmnt_target_group_extended_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attchmnt_target_group_extended add constraint fk_attchmnt_target_group_extended_attachment_id foreign key (attachment_id) references attachment on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attchmnt_target_group_included add constraint fk_attchmnt_target_group_included_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attchmnt_target_group_included add constraint fk_attchmnt_target_group_included_attachment_id foreign key (attachment_id) references attachment on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table audit_event_doc add constraint fk_audit_event_doc_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table audit_event_doc add constraint fk_audit_event_doc_audit_event_id foreign key (audit_event_id) references audit_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table branch add constraint fk_branch_parent_id foreign key (parent_id) references branch on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clickatell_configuration add constraint fk_clickatell_configuration_customer_phone_number_var_id foreign key (customer_phone_number_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clickatell_configuration add constraint fk_clickatell_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clickatell_configuration add constraint fk_clickatell_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clickatell_configuration add constraint fk_clickatell_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clickatell_configuration add constraint fk_clickatell_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clipboard_content add constraint fk_clipboard_content_workgroup_id foreign key (workgroup_id) references workgroup on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clipboard_content add constraint fk_clipboard_content_content_id foreign key (content_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table columnar_indicators add constraint fk_columnar_indicators_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_ext_proof_validation_map add constraint fk_comm_ext_proof_validation_map_ext_proof_validation_id foreign key (ext_proof_validation_id) references external_proof_validation on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_ext_proof_validation_map add constraint fk_comm_ext_proof_validation_map_communication_id foreign key (communication_id) references communication on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_ext_proof_validation_map add constraint fk_comm_ext_proof_validation_map_communication_proof_id foreign key (communication_proof_id) references communication_proof on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_mini_prod_event_communications add constraint fk_comm_mini_prod_event_communications_communication_id foreign key (communication_id) references communication on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_mini_prod_event_communications add constraint fk_comm_mini_prod_event_communications_comm_prod_event_id foreign key (comm_prod_event_id) references communication_mini_prod_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_prod_event_communications add constraint fk_comm_prod_event_communications_communication_id foreign key (communication_id) references communication on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_prod_event_communications add constraint fk_comm_prod_event_communications_comm_prod_event_id foreign key (comm_prod_event_id) references communication_production_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_zone_content_association add constraint fk_comm_zone_content_association_communication_id foreign key (communication_id) references communication on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_zone_content_association add constraint fk_comm_zone_content_association_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table communication add constraint fk_communication_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_content_object add constraint fk_complex_value_content_object_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_content_object add constraint fk_complex_value_content_object_complex_value_id foreign key (complex_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_data_elements add constraint fk_complex_value_data_elements_data_element_id foreign key (data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_data_elements add constraint fk_complex_value_data_elements_complex_value_id foreign key (complex_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_lookup_tables add constraint fk_complex_value_lookup_tables_lookup_table_id foreign key (lookup_table_id) references lookup_table on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_lookup_tables add constraint fk_complex_value_lookup_tables_complex_value_id foreign key (complex_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_variable add constraint fk_complex_value_variable_variable_id foreign key (variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_variable add constraint fk_complex_value_variable_complex_value_id foreign key (complex_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table composition_additional_files add constraint fk_composition_additional_files_database_file_id foreign key (database_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table composition_additional_files add constraint fk_composition_additional_files_composition_file_set_id foreign key (composition_file_set_id) references composition_file_set on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table composition_file_set add constraint fk_composition_file_set_tp_collection_id foreign key (tp_collection_id) references touchpoint_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table compound_key_item add constraint fk_compound_key_item_compound_key_id foreign key (compound_key_id) references compound_key on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table compound_key_item add constraint fk_compound_key_item_variable_id foreign key (variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_item add constraint fk_condition_item_target_group_id foreign key (target_group_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_item_attrib add constraint fk_condition_item_attrib_civ_id foreign key (civ_id) references condition_item_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_item_value add constraint fk_condition_item_value_condition_item_id foreign key (condition_item_id) references condition_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_item_value add constraint fk_condition_item_value_condition_subelement_id foreign key (condition_subelement_id) references condition_subelement on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_param_map add constraint fk_condition_param_map_tgi_id foreign key (tgi_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_param_map add constraint fk_condition_param_map_condition_item_id foreign key (condition_item_id) references condition_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_sub_attrib add constraint fk_condition_sub_attrib_cs_id foreign key (cs_id) references condition_subelement on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_subelement add constraint fk_condition_subelement_condition_element_id foreign key (condition_element_id) references condition_element on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_subelement add constraint fk_condition_subelement_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_subelement add constraint fk_condition_subelement_condition_operator_id foreign key (condition_operator_id) references condition_operator on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table connected_tp_sel_visible_user add constraint fk_connected_tp_sel_visible_user_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table connected_tp_sel_visible_user add constraint fk_connected_tp_sel_visible_user_tp_selection_id foreign key (tp_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table connection_resource add constraint fk_connection_resource_data_resource_id foreign key (data_resource_id) references data_resource on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table connection_resource add constraint fk_connection_resource_reference_data_file_id foreign key (reference_data_file_id) references data_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table connection_resource add constraint fk_connection_resource_reference_connection_id foreign key (reference_connection_id) references reference_connection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content add constraint fk_content_image_link foreign key (image_link) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content add constraint fk_content_image_alt_text foreign key (image_alt_text) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content add constraint fk_content_image_ext_link foreign key (image_ext_link) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content add constraint fk_content_image_ext_path foreign key (image_ext_path) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_assistant_target_locales add constraint fk_content_assistant_target_locales_locale_id foreign key (locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_assistant_target_locales add constraint fk_content_assistant_target_locales_content_assistant_id foreign key (content_assistant_id) references content_assistant on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_assistant_target_tps add constraint fk_content_assistant_target_tps_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_assistant_target_tps add constraint fk_content_assistant_target_tps_content_assistant_id foreign key (content_assistant_id) references content_assistant on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_content_object_type add constraint fk_content_content_object_type_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_content_object_type add constraint fk_content_content_object_type_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_content_targeting add constraint fk_content_content_targeting_content_targeting_id foreign key (content_targeting_id) references content_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_content_targeting add constraint fk_content_content_targeting_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_images add constraint fk_content_images_database_file_id foreign key (database_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_images add constraint fk_content_images_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_list_style add constraint fk_content_list_style_list_style_id foreign key (list_style_id) references list_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_list_style add constraint fk_content_list_style_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object add constraint fk_content_object_content_type_id foreign key (content_type_id) references content_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object add constraint fk_content_object_tp_selection_id foreign key (tp_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object add constraint fk_content_object_global_parent_object_id foreign key (global_parent_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object add constraint fk_content_object_state_id foreign key (state_id) references workflow_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object add constraint fk_content_object_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_co_pg_tn_id foreign key (co_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_ref_co_pg_tn_id foreign key (ref_co_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_tp_pg_tn_id foreign key (tp_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_ref_tp_pg_tn_id foreign key (ref_tp_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_zone_part_id foreign key (zone_part_id) references zone_part on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_ref_messagepoint_locale_id foreign key (ref_messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_ref_image_library_id foreign key (ref_image_library_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_comment add constraint fk_content_object_comment_content_object_id_data_type foreign key (content_object_id, data_type) references content_object_data on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_data add constraint fk_content_object_data_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_data add constraint fk_content_object_data_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_data add constraint fk_content_object_data_metadata_form_id foreign key (metadata_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_document_map add constraint fk_content_object_document_map_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_document_map add constraint fk_content_object_document_map_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_excluded_map add constraint fk_content_object_tg_excluded_map_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_excluded_map add constraint fk_content_object_tg_excluded_map_content_object_id_data_type foreign key (content_object_id, data_type) references content_object_data on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_extended_map add constraint fk_content_object_tg_extended_map_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_extended_map add constraint fk_content_object_tg_extended_map_content_object_id_data_type foreign key (content_object_id, data_type) references content_object_data on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_included_map add constraint fk_content_object_tg_included_map_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_included_map add constraint fk_content_object_tg_included_map_content_object_id_data_type foreign key (content_object_id, data_type) references content_object_data on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_instance_map add constraint fk_content_object_tg_instance_map_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_instance_map add constraint fk_content_object_tg_instance_map_tg_id foreign key (tg_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tpc_map add constraint fk_content_object_tpc_map_tp_collection_id foreign key (tp_collection_id) references touchpoint_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tpc_map add constraint fk_content_object_tpc_map_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_paragraph_style add constraint fk_content_paragraph_style_paragraph_style_id foreign key (paragraph_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_paragraph_style add constraint fk_content_paragraph_style_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_placeholders add constraint fk_content_placeholders_placeholder_id foreign key (placeholder_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_placeholders add constraint fk_content_placeholders_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_target_group_excluded add constraint fk_content_target_group_excluded_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_target_group_excluded add constraint fk_content_target_group_excluded_content_targeting_id foreign key (content_targeting_id) references content_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_target_group_extended add constraint fk_content_target_group_extended_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_target_group_extended add constraint fk_content_target_group_extended_content_targeting_id foreign key (content_targeting_id) references content_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_target_group_included add constraint fk_content_target_group_included_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_target_group_included add constraint fk_content_target_group_included_content_targeting_id foreign key (content_targeting_id) references content_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_text_style add constraint fk_content_text_style_style_id foreign key (style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_text_style add constraint fk_content_text_style_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_tg_instance_map add constraint fk_content_tg_instance_map_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_tg_instance_map add constraint fk_content_tg_instance_map_tg_id foreign key (tg_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_variable add constraint fk_content_variable_variable_id foreign key (variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_variable add constraint fk_content_variable_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_core add constraint fk_data_element_core_xml_data_tag_definition_id foreign key (xml_data_tag_definition_id) references xml_data_tag_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_core add constraint fk_data_element_core_data_record_id foreign key (data_record_id) references data_record on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_flat add constraint fk_data_element_flat_data_element_id foreign key (data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_json add constraint fk_data_element_json_data_element_id foreign key (data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_variable add constraint fk_data_element_variable_parameter_id foreign key (parameter_id) references parameter on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_variable add constraint fk_data_element_variable_expression foreign key (expression) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_variable add constraint fk_data_element_variable_script foreign key (script) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_variable add constraint fk_data_element_variable_non_driver_agg_op_id foreign key (non_driver_agg_op_id) references aggregation_operator on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_variable add constraint fk_data_element_variable_non_driver_data_group_id foreign key (non_driver_data_group_id) references data_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_xml add constraint fk_data_element_xml_data_element_id foreign key (data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_file add constraint fk_data_file_source_type_id foreign key (source_type_id) references source_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_file_document add constraint fk_data_file_document_data_file_id foreign key (data_file_id) references data_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_file_document add constraint fk_data_file_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_file_preview_languages add constraint fk_data_file_preview_languages_data_file_id foreign key (data_file_id) references data_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_file_tpc add constraint fk_data_file_tpc_tp_collection_id foreign key (tp_collection_id) references touchpoint_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_file_tpc add constraint fk_data_file_tpc_data_file_id foreign key (data_file_id) references data_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_group add constraint fk_data_group_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_record add constraint fk_data_record_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_record add constraint fk_data_record_parent_data_record_id foreign key (parent_data_record_id) references data_record on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_record add constraint fk_data_record_data_group_id foreign key (data_group_id) references data_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_resource add constraint fk_data_resource_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_resource add constraint fk_data_resource_primary_data_file_id foreign key (primary_data_file_id) references data_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_source add constraint fk_data_source_encoding_type_id foreign key (encoding_type_id) references encoding_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_source add constraint fk_data_source_layout_type_id foreign key (layout_type_id) references layout_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_source add constraint fk_data_source_source_type_id foreign key (source_type_id) references source_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_source add constraint fk_data_source_record_type_id foreign key (record_type_id) references record_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_source_association add constraint fk_data_source_association_primary_data_source_id foreign key (primary_data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_source_association add constraint fk_data_source_association_customer_data_element_id foreign key (customer_data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_subtype_comparison add constraint fk_data_subtype_comparison_data_comparison_id foreign key (data_comparison_id) references data_comparison on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_subtype_comparison add constraint fk_data_subtype_comparison_data_subtype_id foreign key (data_subtype_id) references data_subtype on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_type_subtype add constraint fk_data_type_subtype_data_subtype_id foreign key (data_subtype_id) references data_subtype on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_type_subtype add constraint fk_data_type_subtype_data_type_id foreign key (data_type_id) references data_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table de_state_transition add constraint fk_de_state_transition_delivery_event_id foreign key (delivery_event_id) references delivery_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table de_state_transition add constraint fk_de_state_transition_system_state_id foreign key (system_state_id) references system_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table delimited_indicators add constraint fk_delimited_indicators_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table delivery_event add constraint fk_delivery_event_job_id foreign key (job_id) references job on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table delivery_event_metadata add constraint fk_delivery_event_metadata_delivery_event_id foreign key (delivery_event_id) references delivery_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table delivery_event_schedule add constraint fk_delivery_event_schedule_frequency_type_id foreign key (frequency_type_id) references frequency_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table delivery_event_schedule add constraint fk_delivery_event_schedule_daily_frequency_type_id foreign key (daily_frequency_type_id) references daily_frequency_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table deserver add constraint fk_deserver_communication_type foreign key (communication_type) references deserver_communicationtype on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table deserver add constraint fk_deserver_private_sshkey_file_id foreign key (private_sshkey_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table deserver add constraint fk_deserver_public_sshkey_file_id foreign key (public_sshkey_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table deserver add constraint fk_deserver_bundle_state_id foreign key (bundle_state_id) references deserver_bundle_type_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dev_de_map add constraint fk_dev_de_map_de_id foreign key (de_id) references variable_data_element_map on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dev_de_map add constraint fk_dev_de_map_dev_id foreign key (dev_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dev_ds_hash_map add constraint fk_dev_ds_hash_map_dev_id foreign key (dev_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_configuration add constraint fk_dialogue_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_configuration add constraint fk_dialogue_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_configuration add constraint fk_dialogue_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_configuration add constraint fk_dialogue_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_data_mapping add constraint fk_dialogue_data_mapping_data_type_id foreign key (data_type_id) references data_subtype on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_metadata_tags add constraint fk_dialogue_metadata_tags_config_id foreign key (config_id) references dialogue_configuration on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_communications_dataresource_id foreign key (communications_dataresource_id) references data_resource on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_communication_marker_style_id foreign key (communication_marker_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_comm_comp_result_webserv_id foreign key (comm_comp_result_webserv_id) references web_service_config on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_comm_prod_status_webserv_id foreign key (comm_prod_status_webserv_id) references web_service_config on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_comm_notification_webserv_id foreign key (comm_notification_webserv_id) references web_service_config on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_comm_data_feed_webserv_id foreign key (comm_data_feed_webserv_id) references web_service_config on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_comm_multi_recipient_id foreign key (comm_multi_recipient_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_touchpoint_metadata_def_id foreign key (touchpoint_metadata_def_id) references metadata_form_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_variant_metadata_def_id foreign key (variant_metadata_def_id) references metadata_form_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_brand_profile_id foreign key (brand_profile_id) references brand_profile on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_data_source_association_id foreign key (data_source_association_id) references data_source_association on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_history add constraint fk_document_history_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_preview add constraint fk_document_preview_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_preview add constraint fk_document_preview_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_preview add constraint fk_document_preview_workgroup_id foreign key (workgroup_id) references workgroup on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_preview add constraint fk_document_preview_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_section add constraint fk_document_section_parent_document_section_id foreign key (parent_document_section_id) references document_section on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_section add constraint fk_document_section_original_image_file_id foreign key (original_image_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_section add constraint fk_document_section_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_tenant_email_address add constraint fk_document_tenant_email_address_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table domain_pod add constraint fk_domain_pod_domain foreign key (domain) references domain on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table domain_pod add constraint fk_domain_pod_pod foreign key (pod) references pod on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table emessaging_configuration add constraint fk_emessaging_configuration_customer_email_address_var_id foreign key (customer_email_address_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table emessaging_configuration add constraint fk_emessaging_configuration_customer_phone_number_var_id foreign key (customer_phone_number_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table emessaging_configuration add constraint fk_emessaging_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table emessaging_configuration add constraint fk_emessaging_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table emessaging_configuration add constraint fk_emessaging_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table emessaging_configuration add constraint fk_emessaging_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table exacttarget_configuration add constraint fk_exacttarget_configuration_customer_email_address_var_id foreign key (customer_email_address_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table exacttarget_configuration add constraint fk_exacttarget_configuration_customer_key_var_id foreign key (customer_key_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table exacttarget_configuration add constraint fk_exacttarget_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table exacttarget_configuration add constraint fk_exacttarget_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table exacttarget_configuration add constraint fk_exacttarget_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table exacttarget_configuration add constraint fk_exacttarget_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ext_reporting_vars_doc add constraint fk_ext_reporting_vars_doc_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ext_reporting_vars_doc add constraint fk_ext_reporting_vars_doc_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table filter_condition add constraint fk_filter_condition_id foreign key (id) references condition_subelement on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table filter_condition add constraint fk_filter_condition_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table filter_condition_value_map add constraint fk_filter_condition_value_map_filter_condition_id foreign key (filter_condition_id) references filter_condition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table folder_insert add constraint fk_folder_insert_folder_id foreign key (folder_id) references folders on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table folder_insert add constraint fk_folder_insert_insert_id foreign key (insert_id) references insert_obj on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table folders add constraint fk_folders_parent_id foreign key (parent_id) references folders on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table formatting_selection add constraint fk_formatting_selection_transform_profile_id foreign key (transform_profile_id) references text_style_transformation_profile on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table formatting_selection add constraint fk_formatting_selection_alternate_document_id foreign key (alternate_document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table formatting_selection add constraint fk_formatting_selection_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ftp_configuration add constraint fk_ftp_configuration_recipfile_cplex_val_id foreign key (recipfile_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ftp_configuration add constraint fk_ftp_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ftp_configuration add constraint fk_ftp_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ftp_configuration add constraint fk_ftp_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ftp_configuration add constraint fk_ftp_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table generic_configuration add constraint fk_generic_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table generic_configuration add constraint fk_generic_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table generic_configuration add constraint fk_generic_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table generic_configuration add constraint fk_generic_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table gmc_configuration add constraint fk_gmc_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table gmc_configuration add constraint fk_gmc_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table gmc_configuration add constraint fk_gmc_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table gmc_configuration add constraint fk_gmc_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content add constraint fk_hist_content_image_link foreign key (image_link) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content add constraint fk_hist_content_image_alt_text foreign key (image_alt_text) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content add constraint fk_hist_content_image_ext_link foreign key (image_ext_link) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content add constraint fk_hist_content_image_ext_path foreign key (image_ext_path) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_content_object_t add constraint fk_hist_content_content_object_t_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_content_object_t add constraint fk_hist_content_content_object_t_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_content_targeting add constraint fk_hist_content_content_targeting_content_targeting_id foreign key (content_targeting_id) references content_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_content_targeting add constraint fk_hist_content_content_targeting_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_images add constraint fk_hist_content_images_database_file_id foreign key (database_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_images add constraint fk_hist_content_images_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_list_style add constraint fk_hist_content_list_style_list_style_id foreign key (list_style_id) references list_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_list_style add constraint fk_hist_content_list_style_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_co_pg_tn_id foreign key (co_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_ref_co_pg_tn_id foreign key (ref_co_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_tp_pg_tn_id foreign key (tp_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_ref_tp_pg_tn_id foreign key (ref_tp_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_zone_part_id foreign key (zone_part_id) references zone_part on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_ref_messagepoint_locale_id foreign key (ref_messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_ref_image_library_id foreign key (ref_image_library_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_paragraph_style add constraint fk_hist_content_paragraph_style_paragraph_style_id foreign key (paragraph_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_paragraph_style add constraint fk_hist_content_paragraph_style_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_placeholders add constraint fk_hist_content_placeholders_placeholder_id foreign key (placeholder_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_placeholders add constraint fk_hist_content_placeholders_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_text_style add constraint fk_hist_content_text_style_style_id foreign key (style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_text_style add constraint fk_hist_content_text_style_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_variable add constraint fk_hist_content_variable_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_variable add constraint fk_hist_content_variable_variable_id foreign key (variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_meta_form_item_value_set add constraint fk_hist_meta_form_item_value_set_metadata_form_item_id foreign key (metadata_form_item_id) references hist_metadata_form_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_metadata_form add constraint fk_hist_metadata_form_form_definition_id foreign key (form_definition_id) references metadata_form_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_metadata_form_item add constraint fk_hist_metadata_form_item_upload_file_id foreign key (upload_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_metadata_form_item add constraint fk_hist_metadata_form_item_form_item_definition_id foreign key (form_item_definition_id) references metadata_form_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_metadata_form_item add constraint fk_hist_metadata_form_item_metadata_form_id foreign key (metadata_form_id) references hist_metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_rat_shared_content add constraint fk_hist_rat_shared_content_rat_shared_content_id foreign key (rat_shared_content_id) references rationalizer_shared_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_rationalizer_content add constraint fk_hist_rationalizer_content_rationalizer_content_id foreign key (rationalizer_content_id) references rationalizer_document_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_document add constraint fk_insert_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_document add constraint fk_insert_document_insert_id foreign key (insert_id) references insert_obj on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_obj add constraint fk_insert_obj_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_rate_schedule add constraint fk_insert_rate_schedule_rate_schedule_collection_id foreign key (rate_schedule_collection_id) references rate_schedule_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_rate_schedule add constraint fk_insert_rate_schedule_insert_schedule_id foreign key (insert_schedule_id) references insert_schedule on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_schedule add constraint fk_insert_schedule_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_schedule add constraint fk_insert_schedule_schedule_collection_id foreign key (schedule_collection_id) references insert_schedule_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_schedule_bin_assignment add constraint fk_insert_schedule_bin_assignment_insert_schedule_id foreign key (insert_schedule_id) references insert_schedule on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_target_group_excluded add constraint fk_insert_target_group_excluded_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_target_group_excluded add constraint fk_insert_target_group_excluded_insert_id foreign key (insert_id) references insert_obj on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_target_group_extended add constraint fk_insert_target_group_extended_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_target_group_extended add constraint fk_insert_target_group_extended_insert_id foreign key (insert_id) references insert_obj on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_target_group_included add constraint fk_insert_target_group_included_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_target_group_included add constraint fk_insert_target_group_included_insert_id foreign key (insert_id) references insert_obj on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_tg_instance_map add constraint fk_insert_tg_instance_map_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_tg_instance_map add constraint fk_insert_tg_instance_map_tg_id foreign key (tg_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table job add constraint fk_job_system_state_id foreign key (system_state_id) references system_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table json_data_definition add constraint fk_json_data_definition_parent_def_id foreign key (parent_def_id) references json_data_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table json_data_definition add constraint fk_json_data_definition_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table json_data_definition add constraint fk_json_data_definition_data_group_id foreign key (data_group_id) references data_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table language_content_hash_map add constraint fk_language_content_hash_map_language_content_hash_id foreign key (language_content_hash_id) references language_content_hash on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table language_selection add constraint fk_language_selection_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table language_selection add constraint fk_language_selection_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence add constraint fk_licence_applicationId foreign key (applicationId) references licence_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence add constraint fk_licence_customerId foreign key (customerId) references licence_customer on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_attributes add constraint fk_licence_attributes_licence_id foreign key (licence_id) references licence on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_audit add constraint fk_licence_audit_licenceId foreign key (licenceId) references licence on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_audit add constraint fk_licence_audit_auditActionId foreign key (auditActionId) references licence_audit_action on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_customer add constraint fk_licence_customer_keypairId foreign key (keypairId) references licence_customer_keypair on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_customer add constraint fk_licence_customer_representativeId foreign key (representativeId) references licence_representative on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_history_reason add constraint fk_licence_history_reason_reason_id foreign key (reason_id) references licence_change_reasons on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_history_reason add constraint fk_licence_history_reason_history_id foreign key (history_id) references licence_history on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_module add constraint fk_licence_module_applicationId foreign key (applicationId) references licence_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_resource add constraint fk_licence_resource_licenceId foreign key (licenceId) references licence on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_server add constraint fk_licence_server_licenceId foreign key (licenceId) references licence on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_server_ip_address add constraint fk_licence_server_ip_address_serverId foreign key (serverId) references licence_server on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table list_style add constraint fk_list_style_text_style_id foreign key (text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table list_style_cust add constraint fk_list_style_cust_text_style_id foreign key (text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table list_style_cust add constraint fk_list_style_cust_master_list_style_id foreign key (master_list_style_id) references list_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table list_style_cust_map add constraint fk_list_style_cust_map_list_style_cust_id foreign key (list_style_cust_id) references list_style_cust on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table list_style_cust_map add constraint fk_list_style_cust_map_list_style_id foreign key (list_style_id) references list_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table list_style_cust_map add constraint fk_list_style_cust_map_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table add constraint fk_lookup_table_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_document add constraint fk_lookup_table_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_document add constraint fk_lookup_table_document_lookup_table_instance_id foreign key (lookup_table_instance_id) references lookup_table_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_instance add constraint fk_lookup_table_instance_lookup_file_id foreign key (lookup_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_instance add constraint fk_lookup_table_instance_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_instance add constraint fk_lookup_table_instance_state_id foreign key (state_id) references workflow_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_tpc add constraint fk_lookup_table_tpc_tp_collection_id foreign key (tp_collection_id) references touchpoint_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_tpc add constraint fk_lookup_table_tpc_lookup_table_instance_id foreign key (lookup_table_instance_id) references lookup_table_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_version_map add constraint fk_lookup_table_version_map_lookup_table_id foreign key (lookup_table_id) references lookup_table on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_version_map add constraint fk_lookup_table_version_map_lookup_table_instance_id foreign key (lookup_table_instance_id) references lookup_table_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_version_map add constraint fk_lookup_table_version_map_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_value add constraint fk_lookup_value_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table meta_form_item_def_con_ele_map add constraint fk_meta_form_item_def_con_ele_map_data_element_id foreign key (data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table meta_form_item_def_con_ele_map add constraint fk_meta_form_item_def_con_ele_map_metadata_form_item_def_id foreign key (metadata_form_item_def_id) references metadata_form_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form add constraint fk_metadata_form_form_definition_id foreign key (form_definition_id) references metadata_form_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_definition add constraint fk_metadata_form_definition_web_service_config_id foreign key (web_service_config_id) references web_service_config on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_item add constraint fk_metadata_form_item_upload_file_id foreign key (upload_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_item add constraint fk_metadata_form_item_form_item_definition_id foreign key (form_item_definition_id) references metadata_form_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_item add constraint fk_metadata_form_item_metadata_form_id foreign key (metadata_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_item_definition add constraint fk_metadata_form_item_definition_data_element_id foreign key (data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_item_definition add constraint fk_metadata_form_item_definition_metadata_form_definition_id foreign key (metadata_form_definition_id) references metadata_form_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_item_value_set add constraint fk_metadata_form_item_value_set_metadata_form_item_id foreign key (metadata_form_item_id) references metadata_form_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_points_of_interest add constraint fk_metadata_points_of_interest_form_item_definition_id foreign key (form_item_definition_id) references metadata_form_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_points_of_interest add constraint fk_metadata_points_of_interest_rationalizer_application_id foreign key (rationalizer_application_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table mp_connector add constraint fk_mp_connector_channel_id foreign key (channel_id) references mp_channel on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table mphcs_configuration add constraint fk_mphcs_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table mphcs_configuration add constraint fk_mphcs_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table mphcs_configuration add constraint fk_mphcs_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table mphcs_configuration add constraint fk_mphcs_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_delivery_report_doc add constraint fk_msg_delivery_report_doc_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_delivery_report_doc add constraint fk_msg_delivery_report_doc_message_delivery_report_id foreign key (message_delivery_report_id) references message_delivery_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_delivery_report_msg add constraint fk_msg_delivery_report_msg_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_delivery_report_msg add constraint fk_msg_delivery_report_msg_message_delivery_report_id foreign key (message_delivery_report_id) references message_delivery_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_simulation_report_doc add constraint fk_msg_simulation_report_doc_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_simulation_report_doc add constraint fk_msg_simulation_report_doc_message_simulation_report_id foreign key (message_simulation_report_id) references message_simulation_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_simulation_report_msg add constraint fk_msg_simulation_report_msg_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_simulation_report_msg add constraint fk_msg_simulation_report_msg_message_simulation_report_id foreign key (message_simulation_report_id) references message_simulation_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table native_comp_configuration add constraint fk_native_comp_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table native_comp_configuration add constraint fk_native_comp_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table native_comp_configuration add constraint fk_native_comp_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table native_comp_configuration add constraint fk_native_comp_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_drop_down_menu add constraint fk_navigation_drop_down_menu_tab_id foreign key (tab_id) references navigation_tabs on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_drop_down_menu_item add constraint fk_navigation_drop_down_menu_item_menu_id foreign key (menu_id) references navigation_drop_down_menu on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_drop_down_menu_item add constraint fk_navigation_drop_down_menu_item_tree_id foreign key (tree_id) references navigation_tree on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_menu_item_perm add constraint fk_navigation_menu_item_perm_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_menu_item_perm add constraint fk_navigation_menu_item_perm_navigation_menu_item_id foreign key (navigation_menu_item_id) references navigation_drop_down_menu_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tab_default_map add constraint fk_navigation_tab_default_map_item_id foreign key (item_id) references navigation_drop_down_menu_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tab_default_map add constraint fk_navigation_tab_default_map_tab_id foreign key (tab_id) references navigation_tabs on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tab_permission add constraint fk_navigation_tab_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tab_permission add constraint fk_navigation_tab_permission_navigation_tab_id foreign key (navigation_tab_id) references navigation_tabs on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tabs add constraint fk_navigation_tabs_default_menu_item_id foreign key (default_menu_item_id) references navigation_drop_down_menu_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tree add constraint fk_navigation_tree_parent_id foreign key (parent_id) references navigation_tree on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table node add constraint fk_node_parent_id foreign key (parent_id) references node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table node add constraint fk_node_branch_id foreign key (branch_id) references branch on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table notification_settings_tp add constraint fk_notification_settings_tp_touchpoint_id foreign key (touchpoint_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table notification_settings_tp add constraint fk_notification_settings_tp_notification_id foreign key (notification_id) references notification_settings on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table object_wf_action_map add constraint fk_object_wf_action_map_wf_action_id foreign key (wf_action_id) references workflow_action on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table operations_report_doc add constraint fk_operations_report_doc_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table operations_report_doc add constraint fk_operations_report_doc_operations_report_id foreign key (operations_report_id) references operations_report_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table operations_report_event add constraint fk_operations_report_event_delivery_event_id foreign key (delivery_event_id) references delivery_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table operations_report_event add constraint fk_operations_report_event_report_id foreign key (report_id) references operations_report_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_def_con_ele_map add constraint fk_order_entry_def_con_ele_map_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_def_con_ele_map add constraint fk_order_entry_def_con_ele_map_order_entry_item_def_id foreign key (order_entry_item_def_id) references order_entry_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item add constraint fk_order_entry_item_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item add constraint fk_order_entry_item_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item add constraint fk_order_entry_item_upload_file_id foreign key (upload_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item add constraint fk_order_entry_item_communication_id foreign key (communication_id) references communication on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item_con_ele_map add constraint fk_order_entry_item_con_ele_map_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item_con_ele_map add constraint fk_order_entry_item_con_ele_map_order_entry_item_id foreign key (order_entry_item_id) references order_entry_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item_definition add constraint fk_order_entry_item_definition_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item_definition add constraint fk_order_entry_item_definition_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_item_def_selections add constraint fk_order_item_def_selections_touchpoint_selection_id foreign key (touchpoint_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_item_def_selections add constraint fk_order_item_def_selections_order_entry_item_definition_id foreign key (order_entry_item_definition_id) references order_entry_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_item_selections add constraint fk_order_item_selections_touchpoint_selection_id foreign key (touchpoint_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_item_selections add constraint fk_order_item_selections_order_entry_item_id foreign key (order_entry_item_id) references order_entry_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table para_style_cust add constraint fk_para_style_cust_text_style_id foreign key (text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table para_style_cust add constraint fk_para_style_cust_master_para_style_id foreign key (master_para_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table para_style_cust_line_spacings add constraint fk_para_style_cust_line_spacings_para_style_cust_id foreign key (para_style_cust_id) references para_style_cust on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table para_style_cust_map add constraint fk_para_style_cust_map_para_style_cust_id foreign key (para_style_cust_id) references para_style_cust on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table para_style_cust_map add constraint fk_para_style_cust_map_para_style_id foreign key (para_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table para_style_cust_map add constraint fk_para_style_cust_map_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table paragraph_style add constraint fk_paragraph_style_style_id foreign key (style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table paragraph_style_line_spacings add constraint fk_paragraph_style_line_spacings_paragraph_style_id foreign key (paragraph_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table parameter_group_instance add constraint fk_parameter_group_instance_pg_id foreign key (pg_id) references parameter_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table parameter_group_instance add constraint fk_parameter_group_instance_pg_instance_collection_id foreign key (pg_instance_collection_id) references pg_instance_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table parameter_group_item add constraint fk_parameter_group_item_pg_id foreign key (pg_id) references parameter_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table parameter_group_item add constraint fk_parameter_group_item_parameter_id foreign key (parameter_id) references parameter on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table password_history add constraint fk_password_history_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table permission add constraint fk_permission_category_id foreign key (category_id) references permission_category on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table permission_category add constraint fk_permission_category_category_group_id foreign key (category_group_id) references category_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table pg_tree_node add constraint fk_pg_tree_node_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table pg_tree_node add constraint fk_pg_tree_node_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table pg_tree_node add constraint fk_pg_tree_node_pgi_collection_id foreign key (pgi_collection_id) references pg_instance_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table pod add constraint fk_pod_type foreign key (type) references pod_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table pod add constraint fk_pod_status foreign key (status) references pod_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project add constraint fk_project_metadata_form_id foreign key (metadata_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_applied_wf_steps add constraint fk_project_applied_wf_steps_wf_step_id foreign key (wf_step_id) references workflow_step on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_applied_wf_steps add constraint fk_project_applied_wf_steps_project_task_id foreign key (project_task_id) references project_task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_step_req_note_map add constraint fk_project_step_req_note_map_project_task_id foreign key (project_task_id) references project_task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_step_req_note_map add constraint fk_project_step_req_note_map_wf_step_id foreign key (wf_step_id) references workflow_step on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_task add constraint fk_project_task_task_id foreign key (task_id) references task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_task add constraint fk_project_task_project_id foreign key (project_id) references project on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_task add constraint fk_project_task_id foreign key (id) references project_task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_wf_step_user_map add constraint fk_project_wf_step_user_map_project_task_id foreign key (project_task_id) references project_task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_wf_step_user_map add constraint fk_project_wf_step_user_map_wf_step_id foreign key (wf_step_id) references workflow_step on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table proof add constraint fk_proof_proof_definition_id foreign key (proof_definition_id) references proof_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table proof_definition add constraint fk_proof_definition_tp_selection_id foreign key (tp_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rat_hist_shared_content add constraint fk_rat_hist_shared_content_hist_rat_shared_content_id foreign key (hist_rat_shared_content_id) references hist_rat_shared_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rat_hist_shared_content add constraint fk_rat_hist_shared_content_rat_shared_content_id foreign key (rat_shared_content_id) references rationalizer_shared_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rat_hist_shared_content_form add constraint fk_rat_hist_shared_content_form_hist_metadata_form_id foreign key (hist_metadata_form_id) references hist_metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rat_hist_shared_content_form add constraint fk_rat_hist_shared_content_form_rat_shared_content_id foreign key (rat_shared_content_id) references rationalizer_shared_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rate_schedule add constraint fk_rate_schedule_rate_schedule_collection_id foreign key (rate_schedule_collection_id) references rate_schedule_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rate_schedule_detail add constraint fk_rate_schedule_detail_rate_schedule_id foreign key (rate_schedule_id) references rate_schedule on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_app_nav_tree add constraint fk_rationalizer_app_nav_tree_form_item_def_id foreign key (form_item_def_id) references metadata_form_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_app_nav_tree add constraint fk_rationalizer_app_nav_tree_rationalizer_app_id foreign key (rationalizer_app_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_app_tp_reference add constraint fk_rationalizer_app_tp_reference_rationalizer_app_id foreign key (rationalizer_app_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_app_visible_user add constraint fk_rationalizer_app_visible_user_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_app_visible_user add constraint fk_rationalizer_app_visible_user_rationalizer_app_id foreign key (rationalizer_app_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_application add constraint fk_rationalizer_application_brand_profile_id foreign key (brand_profile_id) references brand_profile on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_document add constraint fk_rationalizer_document_parsed_document_form_id foreign key (parsed_document_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_document add constraint fk_rationalizer_document_rationalizer_application_id foreign key (rationalizer_application_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_document_content add constraint fk_rationalizer_document_content_parsed_content_form_id foreign key (parsed_content_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_document_content add constraint fk_rationalizer_document_content_rationalizer_document_id foreign key (rationalizer_document_id) references rationalizer_document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_document_content add constraint fk_rationalizer_document_content_rationalizer_shared_content_id foreign key (rationalizer_shared_content_id) references rationalizer_shared_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_filter_item_defs add constraint fk_rationalizer_filter_item_defs_metadata_form_definition_id foreign key (metadata_form_definition_id) references metadata_form_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_filter_item_defs add constraint fk_rationalizer_filter_item_defs_rationalizer_application foreign key (rationalizer_application) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_hist_content add constraint fk_rationalizer_hist_content_hist_rationalizer_content_id foreign key (hist_rationalizer_content_id) references hist_rationalizer_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_hist_content add constraint fk_rationalizer_hist_content_rationalizer_content_id foreign key (rationalizer_content_id) references rationalizer_document_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_hist_form add constraint fk_rationalizer_hist_form_hist_metadata_form_id foreign key (hist_metadata_form_id) references hist_metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_hist_form add constraint fk_rationalizer_hist_form_rationalizer_content_id foreign key (rationalizer_content_id) references rationalizer_document_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_query add constraint fk_rationalizer_query_rationalizer_application_id foreign key (rationalizer_application_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_query_component add constraint fk_rationalizer_query_component_rationalizer_query_id foreign key (rationalizer_query_id) references rationalizer_query on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_shared_content add constraint fk_rationalizer_shared_content_parsed_content_form_id foreign key (parsed_content_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_shared_content add constraint fk_rationalizer_shared_content_rationalizer_application_id foreign key (rationalizer_application_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table redirection_info add constraint fk_redirection_info_domain foreign key (domain) references domain on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table redirection_info add constraint fk_redirection_info_production foreign key (production) references pod on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table redirection_info add constraint fk_redirection_info_transition foreign key (transition) references pod on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table reference_connection add constraint fk_reference_connection_data_source_association_id foreign key (data_source_association_id) references data_source_association on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table reference_connection add constraint fk_reference_connection_reference_data_source_id foreign key (reference_data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table reference_connection add constraint fk_reference_connection_primary_key_variable_id foreign key (primary_key_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table reference_connection add constraint fk_reference_connection_reference_key_variable_id foreign key (reference_key_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table reference_connection add constraint fk_reference_connection_primary_compound_key_id foreign key (primary_compound_key_id) references compound_key on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table reference_connection add constraint fk_reference_connection_reference_compound_key_id foreign key (reference_compound_key_id) references compound_key on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table report_scenario_delivery_event add constraint fk_report_scenario_delivery_event_delivery_event_id foreign key (delivery_event_id) references delivery_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table report_scenario_delivery_event add constraint fk_report_scenario_delivery_event_report_id foreign key (report_id) references report_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table report_scenario_message add constraint fk_report_scenario_message_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table report_scenario_message add constraint fk_report_scenario_message_report_id foreign key (report_id) references report_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table role_permission add constraint fk_role_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table role_permission add constraint fk_role_permission_role_id foreign key (role_id) references role on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table search_result_ids add constraint fk_search_result_ids_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sefas_configuration add constraint fk_sefas_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sefas_configuration add constraint fk_sefas_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sefas_configuration add constraint fk_sefas_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sefas_configuration add constraint fk_sefas_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table seg_analysis_analyzables add constraint fk_seg_analysis_analyzables_seg_analysis_id foreign key (seg_analysis_id) references segmentation_analysis on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sendmail_configuration add constraint fk_sendmail_configuration_customer_email_address_var_id foreign key (customer_email_address_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sendmail_configuration add constraint fk_sendmail_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sendmail_configuration add constraint fk_sendmail_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sendmail_configuration add constraint fk_sendmail_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sendmail_configuration add constraint fk_sendmail_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table simulation add constraint fk_simulation_workgroup_id foreign key (workgroup_id) references workgroup on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table simulation_content_object add constraint fk_simulation_content_object_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table simulation_content_object add constraint fk_simulation_content_object_simulation_id foreign key (simulation_id) references simulation on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sub_content_type add constraint fk_sub_content_type_parent_content_id foreign key (parent_content_id) references content_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sync_history_locales add constraint fk_sync_history_locales_sync_history_id foreign key (sync_history_id) references sync_history on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sync_object add constraint fk_sync_object_sync_process_id foreign key (sync_process_id) references sync_process on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sync_worker add constraint fk_sync_worker_sync_process_id foreign key (sync_process_id) references sync_process on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag add constraint fk_tag_tag_content foreign key (tag_content) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag add constraint fk_tag_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag add constraint fk_tag_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_content_object add constraint fk_tag_content_object_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_content_object add constraint fk_tag_content_object_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_document add constraint fk_tag_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_document add constraint fk_tag_document_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_insert add constraint fk_tag_insert_insert_id foreign key (insert_id) references insert_obj on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_insert add constraint fk_tag_insert_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_target_group_excluded add constraint fk_tag_target_group_excluded_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_target_group_excluded add constraint fk_tag_target_group_excluded_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_target_group_extended add constraint fk_tag_target_group_extended_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_target_group_extended add constraint fk_tag_target_group_extended_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_target_group_included add constraint fk_tag_target_group_included_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_target_group_included add constraint fk_tag_target_group_included_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_tg_instance_map add constraint fk_tag_tg_instance_map_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_tg_instance_map add constraint fk_tag_tg_instance_map_tg_id foreign key (tg_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_touchpoint_collection add constraint fk_tag_touchpoint_collection_touchpoint_collection_id foreign key (touchpoint_collection_id) references touchpoint_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_touchpoint_collection add constraint fk_tag_touchpoint_collection_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table target_group add constraint fk_target_group_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table target_group_document add constraint fk_target_group_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table target_group_document add constraint fk_target_group_document_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task add constraint fk_task_reporter_id foreign key (reporter_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task add constraint fk_task_metadata_form_id foreign key (metadata_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task add constraint fk_task_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task add constraint fk_task_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task_document add constraint fk_task_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task_document add constraint fk_task_document_task_id foreign key (task_id) references task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task_item add constraint fk_task_item_task_id foreign key (task_id) references task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task_user add constraint fk_task_user_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task_user add constraint fk_task_user_task_id foreign key (task_id) references task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table template_modifier add constraint fk_template_modifier_complex_value_id foreign key (complex_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table template_modifier add constraint fk_template_modifier_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table template_modifier add constraint fk_template_modifier_tp_selection_id foreign key (tp_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table template_variant add constraint fk_template_variant_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tenant_permission add constraint fk_tenant_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tenant_permission add constraint fk_tenant_permission_tenant_id foreign key (tenant_id) references tenants on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tenant_theme_info add constraint fk_tenant_theme_info_system_theme foreign key (system_theme) references system_theme on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tenant_theme_info add constraint fk_tenant_theme_info_background_theme foreign key (background_theme) references background_themes on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tenants add constraint fk_tenants_parent_id foreign key (parent_id) references tenants on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tenants add constraint fk_tenants_id foreign key (id) references tenant_theme_info on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table test_scenario add constraint fk_test_scenario_workgroup_id foreign key (workgroup_id) references workgroup on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table test_scenario add constraint fk_test_scenario_test_suite_id foreign key (test_suite_id) references test_suite on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table test_scenario_content_object add constraint fk_test_scenario_content_object_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table test_scenario_content_object add constraint fk_test_scenario_content_object_test_id foreign key (test_id) references test_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_colors add constraint fk_text_style_colors_text_style_id foreign key (text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_cust add constraint fk_text_style_cust_master_text_style_id foreign key (master_text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_cust_colors add constraint fk_text_style_cust_colors_text_style_cust_id foreign key (text_style_cust_id) references text_style_cust on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_cust_map add constraint fk_text_style_cust_map_text_style_cust_id foreign key (text_style_cust_id) references text_style_cust on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_cust_map add constraint fk_text_style_cust_map_text_style_id foreign key (text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_cust_map add constraint fk_text_style_cust_map_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_cust_point_sizes add constraint fk_text_style_cust_point_sizes_text_style_cust_id foreign key (text_style_cust_id) references text_style_cust on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_font add constraint fk_text_style_font_ttf_file_id foreign key (ttf_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_font add constraint fk_text_style_font_eot_file_id foreign key (eot_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_font add constraint fk_text_style_font_ttf_bold_file_id foreign key (ttf_bold_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_font add constraint fk_text_style_font_ttf_italic_file_id foreign key (ttf_italic_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_font add constraint fk_text_style_font_ttf_bold_italic_file_id foreign key (ttf_bold_italic_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_point_sizes add constraint fk_text_style_point_sizes_text_style_id foreign key (text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_collection add constraint fk_touchpoint_collection_metadata_form_id foreign key (metadata_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_collection add constraint fk_touchpoint_collection_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_collection add constraint fk_touchpoint_collection_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_language add constraint fk_touchpoint_language_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_selection add constraint fk_touchpoint_selection_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_selection add constraint fk_touchpoint_selection_template_variant_id foreign key (template_variant_id) references template_variant on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_selection_workgroup add constraint fk_touchpoint_selection_workgroup_workgroup_id foreign key (workgroup_id) references workgroup on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_selection_workgroup add constraint fk_touchpoint_selection_workgroup_tp_selection_id foreign key (tp_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_targeting add constraint fk_touchpoint_targeting_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_tg_instance_map add constraint fk_touchpoint_tg_instance_map_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_tg_instance_map add constraint fk_touchpoint_tg_instance_map_tg_id foreign key (tg_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_collection_touchpoint add constraint fk_tp_collection_touchpoint_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_collection_touchpoint add constraint fk_tp_collection_touchpoint_collection_id foreign key (collection_id) references touchpoint_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_delivery_report_doc add constraint fk_tp_delivery_report_doc_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_delivery_report_doc add constraint fk_tp_delivery_report_doc_tp_delivery_report_id foreign key (tp_delivery_report_id) references tp_delivery_report_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_delivery_report_event add constraint fk_tp_delivery_report_event_delivery_event_id foreign key (delivery_event_id) references delivery_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_delivery_report_event add constraint fk_tp_delivery_report_event_report_id foreign key (report_id) references tp_delivery_report_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_instance_locales add constraint fk_tp_instance_locales_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_instance_locales add constraint fk_tp_instance_locales_tpivm_id foreign key (tpivm_id) references tp_instance_visibility_map on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_sel_visible_user add constraint fk_tp_sel_visible_user_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_sel_visible_user add constraint fk_tp_sel_visible_user_tp_selection_id foreign key (tp_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_target_group_excluded add constraint fk_tp_target_group_excluded_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_target_group_excluded add constraint fk_tp_target_group_excluded_touchpoint_targeting_id foreign key (touchpoint_targeting_id) references touchpoint_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_target_group_extended add constraint fk_tp_target_group_extended_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_target_group_extended add constraint fk_tp_target_group_extended_touchpoint_targeting_id foreign key (touchpoint_targeting_id) references touchpoint_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_target_group_included add constraint fk_tp_target_group_included_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_target_group_included add constraint fk_tp_target_group_included_touchpoint_targeting_id foreign key (touchpoint_targeting_id) references touchpoint_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tstp_language add constraint fk_tstp_language_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tstp_language add constraint fk_tstp_language_tstp_id foreign key (tstp_id) references text_style_transformation_profile on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table user_permission add constraint fk_user_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table user_permission add constraint fk_user_permission_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table user_role add constraint fk_user_role_role_id foreign key (role_id) references role on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table user_role add constraint fk_user_role_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table variable_data_element_map add constraint fk_variable_data_element_map_de_id foreign key (de_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table variable_data_element_map add constraint fk_variable_data_element_map_op_id foreign key (op_id) references aggregation_operator on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table variable_data_element_map add constraint fk_variable_data_element_map_data_record_level_id foreign key (data_record_level_id) references data_record_level on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table variable_document add constraint fk_variable_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table variable_document add constraint fk_variable_document_variable_id foreign key (variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow add constraint fk_workflow_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_instance add constraint fk_workflow_instance_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_instance add constraint fk_workflow_instance_workflow_id foreign key (workflow_id) references workflow on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_library_document add constraint fk_workflow_library_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_library_document add constraint fk_workflow_library_document_workflow_library_id foreign key (workflow_library_id) references workflow on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position add constraint fk_workflow_position_workflow_id foreign key (workflow_id) references old_workflow on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position add constraint fk_workflow_position_workflow_state_id foreign key (workflow_state_id) references workflow_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position add constraint fk_workflow_position_previous foreign key (previous) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position add constraint fk_workflow_position_next foreign key (next) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position_permission add constraint fk_workflow_position_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position_permission add constraint fk_workflow_position_permission_workflow_position_id foreign key (workflow_position_id) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position_user add constraint fk_workflow_position_user_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position_user add constraint fk_workflow_position_user_workflow_position_id foreign key (workflow_position_id) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_property add constraint fk_workflow_property_workflow_position_id foreign key (workflow_position_id) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step add constraint fk_workflow_step_ts_trans_profile_id foreign key (ts_trans_profile_id) references text_style_transformation_profile on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step add constraint fk_workflow_step_frequency_type_id foreign key (frequency_type_id) references frequency_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step add constraint fk_workflow_step_wf_instance_id foreign key (wf_instance_id) references workflow_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step_langs add constraint fk_workflow_step_langs_lang_id foreign key (lang_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step_langs add constraint fk_workflow_step_langs_wf_step_id foreign key (wf_step_id) references workflow_step on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step_subwfs add constraint fk_workflow_step_subwfs_wf_id foreign key (wf_id) references workflow on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step_subwfs add constraint fk_workflow_step_subwfs_wf_step_id foreign key (wf_step_id) references workflow_step on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_tab add constraint fk_workflow_tab_workflow_position_id foreign key (workflow_position_id) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_tab add constraint fk_workflow_tab_required_id foreign key (required_id) references workflow_tab on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_tab_permission add constraint fk_workflow_tab_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_tab_permission add constraint fk_workflow_tab_permission_workflow_tab_id foreign key (workflow_tab_id) references workflow_tab on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workgroup_zone add constraint fk_workgroup_zone_workgroup_id foreign key (workgroup_id) references workgroup on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workgroup_zone add constraint fk_workgroup_zone_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table xml_data_tag_definition add constraint fk_xml_data_tag_definition_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table xml_data_tag_definition add constraint fk_xml_data_tag_definition_parent_tag_id foreign key (parent_tag_id) references xml_data_tag_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table xml_data_tag_definition add constraint fk_xml_data_tag_definition_data_group_id foreign key (data_group_id) references data_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_parent_zone_id foreign key (parent_zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_default_text_style_id foreign key (default_text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_starter_text_style_id foreign key (starter_text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_default_paragraph_style_id foreign key (default_paragraph_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_default_list_style_id foreign key (default_list_style_id) references list_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_template_image_id foreign key (template_image_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_template_smart_text_id foreign key (template_smart_text_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_comm_data_image_var_id foreign key (comm_data_image_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_document_section_id foreign key (document_section_id) references document_section on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_attributes add constraint fk_zone_attributes_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_image_assets add constraint fk_zone_image_assets_image_id foreign key (image_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_image_assets add constraint fk_zone_image_assets_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_list_style add constraint fk_zone_list_style_list_style_id foreign key (list_style_id) references list_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_list_style add constraint fk_zone_list_style_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_paragraph_style add constraint fk_zone_paragraph_style_paragraph_style_id foreign key (paragraph_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_paragraph_style add constraint fk_zone_paragraph_style_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_part add constraint fk_zone_part_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_part add constraint fk_zone_part_content_type foreign key (content_type) references content_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_part add constraint fk_zone_part_sub_content_type_id foreign key (sub_content_type_id) references sub_content_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_smart_text_assets add constraint fk_zone_smart_text_assets_smart_text_id foreign key (smart_text_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_smart_text_assets add constraint fk_zone_smart_text_assets_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_style add constraint fk_zone_style_style_id foreign key (style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_style add constraint fk_zone_style_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
CREATE INDEX CI_CO_ASSOCIATION_O1 ON CONTENT_OBJECT_ASSOCIATION (CONTENT_OBJECT_ID, DATA_TYPE);
CREATE INDEX CI_CO_ASSOCIATION_O2 ON CONTENT_OBJECT_ASSOCIATION (co_pg_tn_id);
CREATE INDEX CI_CO_ASSOCIATION_O3 ON CONTENT_OBJECT_ASSOCIATION (tp_pg_tn_id);
CREATE INDEX CI_CO_ASSOCIATION_O4 ON CONTENT_OBJECT_ASSOCIATION (ref_co_pg_tn_id);
CREATE INDEX CI_CO_ASSOCIATION_O5 ON CONTENT_OBJECT_ASSOCIATION (ref_tp_pg_tn_id);
CREATE INDEX CI_CO_COMMENT_O1 ON CONTENT_OBJECT_COMMENT (CONTENT_OBJECT_ID, DATA_TYPE);
CREATE INDEX CI_CO_ZONE_ASSOCIATION_O1 ON CONTENT_OBJECT_ZONE_PRIORITY (TOUCHPOINT_SELECTION_ID,ZONE_ID);
CREATE INDEX CI_CO_ZONE_ASSOCIATION_O2 ON CONTENT_OBJECT_ZONE_PRIORITY (CONTENT_OBJECT_ID, DATA_TYPE);
CREATE INDEX CI_CO_ZONE_ASSOCIATION_O3 ON CONTENT_OBJECT_ZONE_PRIORITY (CONTENT_OBJECT_PRIORITY);
CREATE INDEX CI_HIST_CO_DATA_O1 ON HIST_CONTENT_OBJECT_DATA (CONTENT_OBJECT_DATA_GUID);
CREATE INDEX CI_HIST_CO_ASSOCIATION_O1 ON HIST_CONTENT_OBJ_ASSOCIATION (CONTENT_OBJECT_DATA_GUID);
CREATE INDEX CI_HIST_CO_ASSOCIATION_O2 ON HIST_CONTENT_OBJ_ASSOCIATION (CONTENT_OBJECT_ID, MESSAGEPOINT_LOCALE_ID, CO_PG_TN_ID, TP_PG_TN_ID, ID);
CREATE INDEX CI_HIST_CO_ASSOCIATION_O3 ON HIST_CONTENT_OBJ_ASSOCIATION (CONTENT_OBJECT_ID, MESSAGEPOINT_LOCALE_ID, CO_PG_TN_ID, TP_PG_TN_ID, ZONE_PART_ID, ID);
CREATE INDEX CI_HIST_CO_ASSOCIATION_O4 ON HIST_CONTENT_OBJ_ASSOCIATION (co_pg_tn_id);
CREATE INDEX CI_HIST_CO_ASSOCIATION_O5 ON HIST_CONTENT_OBJ_ASSOCIATION (tp_pg_tn_id);
CREATE INDEX CI_HIST_CO_ASSOCIATION_O6 ON HIST_CONTENT_OBJ_ASSOCIATION (ref_co_pg_tn_id);
CREATE INDEX CI_HIST_CO_ASSOCIATION_O7 ON HIST_CONTENT_OBJ_ASSOCIATION (ref_tp_pg_tn_id);
CREATE INDEX CI_APPROVAL_O1 ON APPROVAL (MODEL_DATA_GUID);
CREATE INDEX CI_WORKFLOW_ACTION_HISTORY_O1 ON WORKFLOW_ACTION_HISTORY (MODEL_DATA_GUID);
CREATE INDEX CI_TEXT_STYLE_IDENTIFIER ON TEXT_STYLE(identifier);
CREATE INDEX CI_PARAGRAPH_STYLE_IDENTIFIER ON PARAGRAPH_STYLE(identifier);
CREATE INDEX CI_CONTENT_256HASH ON CONTENT(sha256_hash);
CREATE INDEX CI_PG_TREE_NODE_DNA ON PG_TREE_NODE (DNA);
CREATE INDEX CI_DOCUMENT_DNA ON DOCUMENT (DNA);
CREATE INDEX CI_DOCUMENT_SECTION_DNA ON DOCUMENT_SECTION (DNA);
CREATE INDEX CI_LANGUAGE_SELECTION_DNA ON LANGUAGE_SELECTION (DNA);
CREATE INDEX CI_TOUCHPOINT_SELECTION_DNA ON TOUCHPOINT_SELECTION (DNA);
CREATE INDEX CI_ZONE_DNA ON ZONE (DNA);
CREATE INDEX CI_ZONE_PART_DNA ON ZONE_PART (DNA);
CREATE INDEX CI_CONTENT_OBJECT_DNA ON CONTENT_OBJECT (DNA);
CREATE INDEX CI_TARGET_GROUP_DNA ON TARGET_GROUP (DNA);
CREATE INDEX CI_LOOKUP_TABLE_DNA ON LOOKUP_TABLE (DNA);
CREATE INDEX CI_ATTACHMENT_DNA ON ATTACHMENT (DNA);
CREATE INDEX CI_DATA_RESOURCE_DNA ON DATA_RESOURCE (DNA);
CREATE INDEX CI_DATA_ELEMENT_VARIABLE_DNA ON DATA_ELEMENT_VARIABLE (DNA);
CREATE INDEX CI_DATA_SOURCE_DNA ON DATA_SOURCE (DNA);
CREATE INDEX CI_DATA_SOURCE_ASSOCIATION_DNA ON DATA_SOURCE_ASSOCIATION (DNA);
CREATE INDEX CI_PARAMETER_GROUP_ITEM_PG_ID ON PARAMETER_GROUP_ITEM (PG_ID);
CREATE INDEX RD_INDEX_ON_APP_ID ON RATIONALIZER_DOCUMENT (RATIONALIZER_APPLICATION_ID);
CREATE INDEX RDC_INDEX_ON_DOCUMENT_ID ON RATIONALIZER_DOCUMENT_CONTENT (RATIONALIZER_DOCUMENT_ID);
CREATE INDEX MF_INDEX_ON_FORM_DEF_ID ON METADATA_FORM (FORM_DEFINITION_ID);
CREATE INDEX MFI_INDEX_ON_MFI_FIDI  ON METADATA_FORM_ITEM (METADATA_FORM_ID, FORM_ITEM_DEFINITION_ID);
CREATE INDEX CI_SYNC_HISTORY_01 ON SYNC_HISTORY (DOCUMENT_ID, OBJECT_ID, OBJECT_TYPE_ID, SYNC_TYPE_ID);
CREATE INDEX CI_SYNC_HISTORY_02 ON SYNC_HISTORY (DOCUMENT_ID, OBJECT_ID, OBJECT_TYPE_ID, SYNC_TYPE_ID, INSTANCE_GUID, SOURCE_DOCUMENT_ID, SOURCE_OBJECT_ID);
CREATE INDEX CI_SYNC_HISTORY_LOCALES ON SYNC_HISTORY_LOCALES (SYNC_HISTORY_ID);
CREATE INDEX CI_JOB_97 ON JOB (EVENT_TYPE_ID,SYSTEM_STATE_ID);
CREATE INDEX CI_ORDER_ENTRY_ITEM_97 ON ORDER_ENTRY_ITEM (COMMUNICATION_ID,ID);
CREATE INDEX CI_DELIVERY_EVENT_96 ON DELIVERY_EVENT (EVENT_TYPE_ID, REFERENCE_ID);
CREATE INDEX CI_DELIVERY_EVENT_97 ON DELIVERY_EVENT (JOB_ID);
CREATE INDEX CI_DELIVERY_EVENT_98 ON DELIVERY_EVENT (SCHEDULED_TIME);
CREATE INDEX CI_DELIVERY_EVENT_99 ON DELIVERY_EVENT (EVENT_TYPE_ID, REFERENCE_ID, CREATED);
CREATE INDEX CI_PARAMETER_GROUP_INSTANCE_01 ON parameter_group_instance (pg_instance_collection_id, pg_item_value_1, pg_item_value_2, pg_item_value_3, pg_item_value_4, pg_item_value_5, pg_item_value_6);
CREATE INDEX CI_PG_TREE_NODE_O1 ON pg_tree_node (parent_node_id);
CREATE INDEX CI_PG_TREE_NODE_O2 ON pg_tree_node (id, parent_node_id);
CREATE INDEX CI_PG_TREE_NODE_O3 ON pg_tree_node (content_object_id, data_type);
CREATE INDEX CI_PG_TREE_NODE_O4 ON pg_tree_node (pgi_collection_id);
CREATE INDEX CI_DOCUMENT_01 ON document (name, id);
CREATE INDEX CI_DOCUMENT_02 ON document (channel_parent_id, enabled, id);
CREATE INDEX CI_DPE_01 ON document_production_event (name, tp_collection_id, id);
CREATE INDEX CI_SYSTEM_PROPERTY_01 ON system_property (prop_key);
CREATE INDEX CI_TEXT_STYLE_COLORS_01 ON text_style_colors (text_style_id);
CREATE INDEX CI_TEXT_STYLE_COLORS_02 ON text_style_colors (text_style_id, value);
CREATE INDEX CI_TOUCHPOINT_SELECTION_01 ON touchpoint_selection (pg_tree_node_id, id);
CREATE INDEX CI_TOUCHPOINT_SELECTION_02 ON touchpoint_selection (archive_type_id, id);
CREATE INDEX CI_TOUCHPOINT_SELECTION_03 ON touchpoint_selection (document_id, id);
CREATE INDEX CI_WORKFLOW_01 ON workflow (name, id);
CREATE INDEX CI_WORKGROUP_ZONE_01 ON workgroup_zone (workgroup_id);
CREATE INDEX CI_WORKGROUP_ZONE_02 ON workgroup_zone (zone_id);
CREATE INDEX CI_ZONE_01 ON zone (name, id);
CREATE INDEX CI_ZONE_02 ON zone (document_id, enabled, id);
CREATE INDEX CI_ZONE_03 ON zone (parent_zone_id, document_section_id, id);
CREATE INDEX CI_ZONE_04 ON zone (parent_zone_id, name);
CREATE UNIQUE INDEX CI_CONTENT_OBJECT_DOCDNA ON CONTENT_OBJECT (DNA) WHERE DOCUMENT_ID IS NULL;
CREATE UNIQUE INDEX ci_node_schema_name ON node ((LOWER(schema_name)));
CREATE UNIQUE INDEX ci_node_branch_is_default_node ON node (branch_id) WHERE is_default_node;
alter table condition_item_value add constraint uc_condition_item_value unique (condition_item_id, condition_subelement_id);
alter table domain_pod add constraint uc_domain_guid_pod_domain_pod unique (domain_guid, pod);
CREATE SEQUENCE hibernate_sequence START WITH 1000;

drop table schema_info;
create table schema_info (version numeric(11,0));
drop table data_migration_info;
create table data_migration_info (version numeric(11,0));
drop table data_migration_info_static;
create table data_migration_info_static (filename varchar(255) not null, version varchar(32) not null, primary key (filename));
drop table schema_migrations;
drop table hash_algorithm_change_objects;
create table hash_algorithm_change_objects(id int8 not null, guid varchar(255), class_name varchar(255));
