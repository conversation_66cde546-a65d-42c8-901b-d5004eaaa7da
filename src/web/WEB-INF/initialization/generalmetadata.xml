<?xml version="1.0" encoding="UTF-8"?>
<GeneralMetadata>

	<AppVersions>
		<AppVersion id="1">
			<Key>app.version.major</Key>
			<Value>app.version.major</Value>
		</AppVersion>
		<AppVersion id="2">
			<Key>app.version.minor</Key>
			<Value>app.version.minor</Value>
		</AppVersion>
		<AppVersion id="3">
			<Key>app.version.maitenance</Key>
			<Value>app.version.revision</Value>
		</AppVersion>
	</AppVersions>

	<ItemTypes>
		<ItemType id="1">
			Content Object
		</ItemType>
		<ItemType id="2">
			Document
		</ItemType>
		<ItemType id="3">
			Zone
		</ItemType>
		<ItemType id="4">
			Content Object Instance
		</ItemType>
		<ItemType id="5">
			Delivery Event
		</ItemType>
		<ItemType id="6">
			Task
		</ItemType>
		<ItemType id="7">
			Application
		</ItemType>
		<ItemType id="8">
			Data Element
		</ItemType>
		<ItemType id="9">
			Variable
		</ItemType>
		<ItemType id="10">
			Base Rule
		</ItemType>
		<ItemType id="11">
			Targeting Rule
		</ItemType>
		<ItemType id="12">
			Job
		</ItemType>
		<ItemType id="13">
			Customer
		</ItemType>
		<ItemType id="14">
			Report
		</ItemType>
		<ItemType id="15">
			Test
		</ItemType>
		<ItemType id="16">
			Message Part Category
		</ItemType>
		<ItemType id="18">
			Message Content Variant
		</ItemType>
		<ItemType id="19">
			Messagepart
		</ItemType>
		<ItemType id="20">
			Insert
		</ItemType>
		<ItemType id="26">
			Smart Text
		</ItemType>
		<ItemType id="27">
			Image Library
		</ItemType>
		<ItemType id="28">
			Communication
		</ItemType>
	</ItemTypes>

	<SystemStates>
		<SystemState id="1">
			<Name>New</Name>
			<StringCode>page.label.state.new</StringCode>
		</SystemState>
		<SystemState id="2">
			<Name>Requested</Name>
			<StringCode>page.label.state.requested</StringCode>
		</SystemState>
		<SystemState id="3">
			<Name>In Process</Name>
			<StringCode>page.label.state.inprocess</StringCode>
			<BitFlag>4</BitFlag>
		</SystemState>
		<SystemState id="4">
			<Name>Completed Sucessfully</Name>
			<StringCode>page.label.state.completed</StringCode>
			<BitFlag>131072</BitFlag>
		</SystemState>
		<SystemState id="5">
			<Name>Frozen</Name>
			<StringCode>page.label.state.frozen</StringCode>
			<BitFlag>2</BitFlag>
		</SystemState>
		<SystemState id="6">
			<Name>Packed</Name>
			<StringCode>page.label.state.packed</StringCode>
			<BitFlag>16</BitFlag>
		</SystemState>
		<SystemState id="7">
			<Name>Exported</Name>
			<StringCode>page.label.state.exported</StringCode>
		</SystemState>
		<SystemState id="8">
			<Name>Pending Packing</Name>
			<StringCode>page.label.state.pendingpacking</StringCode>
		</SystemState>
		<SystemState id="10">
			<Name>Pending Report Processing</Name>
			<StringCode>page.label.state.pendingreportprocessing</StringCode>
		</SystemState>
		<SystemState id="11">
			<Name>Completed with Error</Name>
			<StringCode>page.label.state.error</StringCode>
			<BitFlag>65536</BitFlag>
		</SystemState>
		<SystemState id="12">
			<Name>Approved</Name>
			<StringCode>page.label.state.approved</StringCode>
		</SystemState>
		<SystemState id="13">
			<Name>Pending Scenario Processing Frozen</Name>
			<StringCode>page.label.state.pendingscenarioprocessingfrozen</StringCode>
		</SystemState>
		<SystemState id="14">
			<Name>Pending Scenario Processing</Name>
			<StringCode>page.label.state.pendingscenarioprocessing</StringCode>
		</SystemState>
		<SystemState id="15">
			<Name>Not Started</Name>
			<StringCode>page.label.state.notstarted</StringCode>
		</SystemState>
		<SystemState id="16">
			<Name>Ready to Start</Name>
			<StringCode>page.label.state.readytostart</StringCode>
		</SystemState>
		<SystemState id="17">
			<Name>Started</Name>
			<StringCode>page.label.state.started</StringCode>
		</SystemState>
		<SystemState id="18">
			<Name>n/a</Name>
			<StringCode>page.label.state.notapplicable</StringCode>
		</SystemState>
		<SystemState id="19">
			<Name>Waiting for Result</Name>
			<StringCode>page.label.state.waiting.for.result</StringCode>
			<BitFlag>128</BitFlag>
		</SystemState>
		<SystemState id="20">
			<Name>Receiving Result</Name>
			<StringCode>page.label.state.receiving.result</StringCode>
			<BitFlag>256</BitFlag>
		</SystemState>
		<SystemState id="21">
			<Name>Processing Result</Name>
			<StringCode>page.label.state.processing.result</StringCode>
			<BitFlag>512</BitFlag>
		</SystemState>
		<SystemState id="22">
			<Name>PreProcessing</Name>
			<StringCode>page.label.state.preprocessing</StringCode>
			<BitFlag>32</BitFlag>
		</SystemState>
		<SystemState id="23">
			<Name>Forced Packing</Name>
			<StringCode>page.label.state.forced.packing</StringCode>
		</SystemState>
		<SystemState id="25">
			<Name>Job Request Created</Name>
			<StringCode>page.label.state.waiting.in.queue</StringCode>
			<BitFlag>1</BitFlag>
		</SystemState>
		<SystemState id="26">
			<Name>Bundling</Name>
			<StringCode>page.label.state.bundling</StringCode>
			<BitFlag>8</BitFlag>
		</SystemState>
		<SystemState id="27">
			<Name>Sending Bundle</Name>
			<StringCode>page.label.state.sending.bundle</StringCode>
			<BitFlag>64</BitFlag>
		</SystemState>
		<SystemState id="28">
			<Name>Result is ready</Name>
			<StringCode>page.label.state.result.ready</StringCode>
			<BitFlag>1024</BitFlag>
		</SystemState>
		<SystemState id="29">
			<Name>Running batch reporter</Name>
			<StringCode>page.label.state.running.batch.reporter</StringCode>
			<BitFlag>2048</BitFlag>
		</SystemState>
		<SystemState id="30">
			<Name>Running delivery reporter</Name>
			<StringCode>page.label.state.running.delivery.reporter</StringCode>
			<BitFlag>4096</BitFlag>
		</SystemState>
		<SystemState id="31">
			<Name>Postprocessing - importing delivery report</Name>
			<StringCode>page.label.state.importing.delivery.report</StringCode>
			<BitFlag>8192</BitFlag>
		</SystemState>
		<SystemState id="32">
			<Name>Postprocessing - running diagnostics</Name>
			<StringCode>page.label.state.running.diagnostics</StringCode>
			<BitFlag>16384</BitFlag>
		</SystemState>
		<SystemState id="33">
			<Name>Completed with Warning</Name>
			<StringCode>page.label.state.completed.warning</StringCode>
			<BitFlag>32768</BitFlag>
		</SystemState>
		<SystemState id="34">
			<Name>Waiting for staged bundle</Name>
			<StringCode>page.text.waiting.for.staged.bundle</StringCode>
		</SystemState>
		<SystemState id="36">
			<Name>Never Run</Name>
			<StringCode>page.label.state.never.run</StringCode>
		</SystemState>
		<SystemState id="38">
			<Name>Scheduled</Name>
			<StringCode>page.label.state.scheduled</StringCode>
			<BitFlag>262144</BitFlag>
		</SystemState>
		<SystemState id="40">
			<Name>Cancelled</Name>
			<StringCode>page.label.state.cancelled</StringCode>
			<BitFlag>524288</BitFlag>
		</SystemState>
		<SystemState id="42">
			<Name>Bundling Error</Name>
			<StringCode>page.label.state.bundling.error</StringCode>
			<BitFlag>1048576</BitFlag>
		</SystemState>
		<SystemState id="44">
			<Name>Pre-Processing Error</Name>
			<StringCode>page.label.state.preprocessing.error</StringCode>
			<BitFlag>2097152</BitFlag>
		</SystemState>
		<SystemState id="46">
			<Name>Sending Bundle Error</Name>
			<StringCode>page.label.state.sending.bundle.error</StringCode>
			<BitFlag>4194304</BitFlag>
		</SystemState>
		<SystemState id="48">
			<Name>Completed without statistics</Name>
			<StringCode>page.label.state.completed.without.statistics</StringCode>
			<BitFlag>8388608</BitFlag>
		</SystemState>
		<SystemState id="50">
			<Name>Completed without PDF</Name>
			<StringCode>page.label.state.completed.without.pdf</StringCode>
			<BitFlag>16777216</BitFlag>
		</SystemState>
	</SystemStates>

	<ContentTypes>
		<ContentType id="1">
			static.type.content.type.text
		</ContentType>
		<ContentType id="2">
			static.type.content.type.graphic
		</ContentType>
		<ContentType id="3">
			static.type.content.type.text.or.graphic
		</ContentType>
		<ContentType id="4">
			static.type.content.type.multi_part
		</ContentType>
		<ContentType id="8">
			static.type.content.type.shared.freeform
		</ContentType>
		<ContentType id="16">
			static.type.content.type.markup
		</ContentType>
		<ContentType id="32">
			static.type.content.type.video
		</ContentType>
	</ContentTypes>

	<SubContentTypes>
		<SubContentType id ="1">
		    <Name>JPEG</Name>
		    <Description>JPEG</Description>
		    <ContentType refid="2"/> 
		</SubContentType>
		<SubContentType id ="2">
		    <Name>TIFF</Name>
		    <Description>TIFF</Description>
		    <ContentType refid="2"/> 
		</SubContentType>
		<SubContentType id ="5">
		    <Name>PDF</Name>
		    <Description>PDF</Description>
		    <ContentType refid="2"/> 
		</SubContentType>
		<SubContentType id ="6">
		    <Name>RTF</Name>
		    <Description>RTF</Description>
		    <ContentType refid="2"/> 
		</SubContentType>
		<SubContentType id ="9">
		    <Name>GIF</Name>
		    <Description>GIF</Description>
		    <ContentType refid="2"/> 
		</SubContentType>
		<SubContentType id ="10">
		    <Name>IMG</Name>
		    <Description>IMG</Description>
		    <ContentType refid="2"/> 
		</SubContentType>
		<SubContentType id ="11">
		    <Name>PNG</Name>
		    <Description>PNG</Description>
		    <ContentType refid="2"/> 
		</SubContentType>
		<SubContentType id ="12">
		    <Name>WEB</Name>
		    <Description>WEB</Description>
		    <ContentType refid="2"/> 
		</SubContentType>
		<SubContentType id ="13">
		    <Name>DXF</Name>
		    <Description>DXF</Description>
		    <ContentType refid="2"/> 
		</SubContentType>
		<SubContentType id ="14">
		    <Name>DLF</Name>
		    <Description>DLF</Description>
		    <ContentType refid="2"/> 
		</SubContentType>
		<SubContentType id ="15">
		    <Name>DOCX</Name>
		    <Description>DOCX</Description>
		    <ContentType refid="2"/> 
		</SubContentType>
		<SubContentType id ="16">
		    <Name>EPS</Name>
		    <Description>EPS</Description>
		    <ContentType refid="2"/> 
		</SubContentType>
		<SubContentType id ="17">
			<Name>PSEG</Name>
			<Description>PSEG</Description>
			<ContentType refid="2"/>
		</SubContentType>
		<SubContentType id ="18">
			<Name>DATA</Name>
			<Description>DATA</Description>
			<ContentType refid="2"/>
		</SubContentType>
	</SubContentTypes>	
	
	<DailyFrequencyTypes>
		<DailyFrequencyType id="1">
			static.type.daily.frequency.type.weekdays
		</DailyFrequencyType>
		<DailyFrequencyType id="2">
			static.type.daily.frequency.type.everyday
		</DailyFrequencyType>	
	</DailyFrequencyTypes>

	<FrequencyTypes>
		<FrequencyType id="1">
			static.type.frequency.type.daily
		</FrequencyType>
		<FrequencyType id="2">
			static.type.frequency.type.weekly
		</FrequencyType>
		<FrequencyType id="3">
			static.type.frequency.type.monthly
		</FrequencyType>
		<FrequencyType id="4">
			static.type.frequency.type.quarterly
		</FrequencyType>
		<FrequencyType id="5">
			static.type.frequency.type.yearly
		</FrequencyType>
	</FrequencyTypes>

	<DeliveryEventTypes>
		<DeliveryEventType id="1">
			static.type.production.event.type.immediate
		</DeliveryEventType>
		<DeliveryEventType id="2">
			static.type.production.event.type.external.trigger
		</DeliveryEventType>
		<DeliveryEventType id="3">
			static.type.production.event.type.schedule
		</DeliveryEventType>
	</DeliveryEventTypes>
		
	<EventTypes>
		<EventType id="1">
			Preview
		</EventType>
		<EventType id="2">
			Test
		</EventType>
		<EventType id="3">
			Simulation
		</EventType>
		<EventType id="4">
			Production
		</EventType>
        <EventType id="5">
            Import
        </EventType>
		<EventType id="6">
			Proof
		</EventType>
		<EventType id="7">
			CommunicationProof
		</EventType>
        <EventType id="8">
            CommunicationProduction
        </EventType>
        <EventType id="9">
            Segmentation
        </EventType>
        <EventType id="10">
            TestSuite
        </EventType>
        <EventType id="11">
            CommunicationMiniProduction
        </EventType>
	</EventTypes>

	<SourceTypes>
		<SourceType id="1">
			static.type.source.type.primary
		</SourceType>
		<SourceType id="2">
			static.type.source.type.reference
		</SourceType>
		<SourceType id="3">
			static.type.source.type.reference.connected
		</SourceType>
		<SourceType id="4">
			static.type.source.type.lookup.table
		</SourceType>		
	</SourceTypes>
	
	<PodTypes>
		<PodType id="1">
			static.type.pod.type.generic
		</PodType>
		<PodType id="2">
			static.type.pod.type.production
		</PodType>
		<PodType id="3">
			static.type.pod.type.transition
		</PodType>
	</PodTypes>
	
	<PodStatuses>
		<PodStatus id="1">
			static.type.pod.status.online
		</PodStatus>
		<PodStatus id="2">
			static.type.pod.status.offline
		</PodStatus>
		<PodStatus id="4">
			static.type.pod.status.invalid.schema
		</PodStatus>
		<PodStatus id="256">
			static.type.pod.status.scan.success
		</PodStatus>
		<PodStatus id="512">
			static.type.pod.status.scan.fail
		</PodStatus>
	</PodStatuses>
		
	<DEServerCommunicationTypes>
		<CommunicationType id="1">
			static.type.deserver.communication.type.sftp
		</CommunicationType>
		<CommunicationType id="2">
			static.type.deserver.communication.type.scp
		</CommunicationType>
		<CommunicationType id="3">
			static.type.deserver.communication.type.dews
		</CommunicationType>
		<CommunicationType id="4">
			static.type.deserver.communication.type.stop
		</CommunicationType>
	</DEServerCommunicationTypes>

	<LayoutTypes>
		<LayoutType id="1">
			static.type.layout.type.delimited
		</LayoutType>
		<LayoutType id="2">
			static.type.layout.type.columnar
		</LayoutType>
		<LayoutType id="3">
			static.type.layout.type.xml
		</LayoutType>
		<LayoutType id="4">
			static.type.layout.type.json
		</LayoutType>
	</LayoutTypes>

	<EncodingTypes>
		<EncodingType id="1">
			ASCII
		</EncodingType>
		<EncodingType id="2">
			EBCDIC
		</EncodingType>
		<EncodingType id="3">
			Native
		</EncodingType>
	</EncodingTypes>

	<DataRecordLevels>
		<DataRecordLevel id="1">
			static.type.data.record.level.level_0
		</DataRecordLevel>
		<DataRecordLevel id="2">
			static.type.data.record.level.level_1
		</DataRecordLevel>
		<DataRecordLevel id="3">
			static.type.data.record.level.level_2
		</DataRecordLevel>
		<DataRecordLevel id="4">
			static.type.data.record.level.level_3
		</DataRecordLevel>
		<DataRecordLevel id="5">
			static.type.data.record.level.level_4
		</DataRecordLevel>
		<DataRecordLevel id="6">
			static.type.data.record.level.level_5
		</DataRecordLevel>
		<DataRecordLevel id="7">
			static.type.data.record.level.level_6
		</DataRecordLevel>
		<DataRecordLevel id="8">
			static.type.data.record.level.level_7
		</DataRecordLevel>
		<DataRecordLevel id="9">
			static.type.data.record.level.level_8
		</DataRecordLevel>
		<DataRecordLevel id="10">
			static.type.data.record.level.level_9
		</DataRecordLevel>
		<DataRecordLevel id="11">
			static.type.data.record.level.level_10
		</DataRecordLevel>
		<DataRecordLevel id="12">
			static.type.data.record.level.level_11
		</DataRecordLevel>
		<DataRecordLevel id="13">
			static.type.data.record.level.level_12
		</DataRecordLevel>
		<DataRecordLevel id="14">
			static.type.data.record.level.level_13
		</DataRecordLevel>
		<DataRecordLevel id="15">
			static.type.data.record.level.level_14
		</DataRecordLevel>
		<DataRecordLevel id="16">
			static.type.data.record.level.level_15
		</DataRecordLevel>
		<DataRecordLevel id="17">
			static.type.data.record.level.level_16
		</DataRecordLevel>
		<DataRecordLevel id="18">
			static.type.data.record.level.level_17
		</DataRecordLevel>
		<DataRecordLevel id="19">
			static.type.data.record.level.level_18
		</DataRecordLevel>
		<DataRecordLevel id="20">
			static.type.data.record.level.level_19
		</DataRecordLevel>
		<DataRecordLevel id="21">
			static.type.data.record.level.level_20
		</DataRecordLevel>
	</DataRecordLevels>

	<!-- IMPORTANT: Do NOT change the words contained here in ANY way, as they are written out into the XML exactly as they appear here! -->
	<AggregationOperators>
		<AggregationOperator id="1">
			SUM
		</AggregationOperator>
		<AggregationOperator id="2">
			MIN
		</AggregationOperator>
		<AggregationOperator id="3">
			MAX
		</AggregationOperator>
		<AggregationOperator id="4">
			COUNT
		</AggregationOperator>
		<AggregationOperator id="5">
			AVG
		</AggregationOperator>
		<AggregationOperator id="9">
			FIRST
		</AggregationOperator>
		<AggregationOperator id="10">
			LAST
		</AggregationOperator>
		<AggregationOperator id="11">
			N/A
		</AggregationOperator>
	</AggregationOperators>

	<!-- IMPORTANT: The ID's of this set must not overlap the ID's from AggregationOperators -->
	<ConditionOperators>
		<ConditionOperator id="6">
			ANY
		</ConditionOperator>
		<ConditionOperator id="7">
			ALL
		</ConditionOperator>
		<ConditionOperator id="8">
			NONE
		</ConditionOperator>
	</ConditionOperators>

	<DataComparisonTypes>
		<DataComparisonType id="1" >
			<Name>static.type.data.comparison.type.equals</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="2">
			<Name>static.type.data.comparison.type.not_equal</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="3">
			<Name>static.type.data.comparison.type.contains</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="4">
			<Name>static.type.data.comparison.type.is_one_of</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="5">
			<Name>static.type.data.comparison.type.starts_with</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="6">
			<Name>static.type.data.comparison.type.ends_with</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="7">
			<Name>static.type.data.comparison.type.empty_or_null</Name>
		</DataComparisonType>
		<DataComparisonType id="8">
			<Name>static.type.data.comparison.type.greater_than</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="9">
			<Name>static.type.data.comparison.type.greater_than_or_equal_to</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="10">
			<Name>static.type.data.comparison.type.less_than</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="11">
			<Name>static.type.data.comparison.type.less_than_or_equal_to</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="12">
			<Name>static.type.data.comparison.type.occurs_before</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="13">
			<Name>static.type.data.comparison.type.occurs_on_or_before</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="14">
			<Name>static.type.data.comparison.type.occurs_after</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="15">
			<Name>static.type.data.comparison.type.occurs_on_or_after</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="16">
			<Name>static.type.data.comparison.type.not_empty_or_null</Name>
		</DataComparisonType>
		<DataComparisonType id="17">
			<Name>static.type.data.comparison.type.between</Name>
			<Scheme>
				Simple
			</Scheme>
			<Scheme>
				Advanced
			</Scheme>
		</DataComparisonType>
		<DataComparisonType id="18">
			<Name>static.type.data.comparison.type.is_not_one_of</Name>
			<Scheme>
				Simple
			</Scheme>
		</DataComparisonType>
	</DataComparisonTypes>

	<DataSubtypes>
		<DataSubtype id="100">
			<Name>static.type.data.sub.type.string</Name>
			<Selectable>TRUE</Selectable>
			<DataComparisonTypes>
				<DataComparisonType refid="1" />
				<DataComparisonType refid="2" />
				<DataComparisonType refid="3" />
				<DataComparisonType refid="4" />
				<DataComparisonType refid="18" />
				<DataComparisonType refid="5" />
				<DataComparisonType refid="6" />
				<DataComparisonType refid="7" />
				<DataComparisonType refid="16" />
			</DataComparisonTypes>
		</DataSubtype>
		<DataSubtype id="200">
			<Name>static.type.data.sub.type.integer</Name>
			<Selectable>TRUE</Selectable>
			<DataComparisonTypes>
				<DataComparisonType refid="1" />
				<DataComparisonType refid="2" />
				<DataComparisonType refid="4" />
				<DataComparisonType refid="18" />
				<DataComparisonType refid="8" />
				<DataComparisonType refid="9" />
				<DataComparisonType refid="10" />
				<DataComparisonType refid="11" />
				<DataComparisonType refid="17" />
				<DataComparisonType refid="7" />
				<DataComparisonType refid="16" />
			</DataComparisonTypes>
		</DataSubtype>
		<DataSubtype id="201">
			<Name>static.type.data.sub.type.uinteger</Name>
			<Selectable>TRUE</Selectable>
			<DataComparisonTypes>
				<DataComparisonType refid="1" />
				<DataComparisonType refid="2" />
				<DataComparisonType refid="4" />
				<DataComparisonType refid="18" />
				<DataComparisonType refid="8" />
				<DataComparisonType refid="9" />
				<DataComparisonType refid="10" />
				<DataComparisonType refid="11" />
				<DataComparisonType refid="17" />
				<DataComparisonType refid="7" />
				<DataComparisonType refid="16" />
			</DataComparisonTypes>
		</DataSubtype>
		<DataSubtype id="202">
			<Name>static.type.data.sub.type.short</Name>
			<Selectable>TRUE</Selectable>
			<DataComparisonTypes>
				<DataComparisonType refid="1" />
				<DataComparisonType refid="2" />
				<DataComparisonType refid="4" />
				<DataComparisonType refid="18" />
				<DataComparisonType refid="8" />
				<DataComparisonType refid="9" />
				<DataComparisonType refid="10" />
				<DataComparisonType refid="11" />
				<DataComparisonType refid="17" />
				<DataComparisonType refid="7" />
				<DataComparisonType refid="16" />
			</DataComparisonTypes>
		</DataSubtype>
		<DataSubtype id="203">
			<Name>static.type.data.sub.type.ushort</Name>
			<Selectable>TRUE</Selectable>
			<DataComparisonTypes>
				<DataComparisonType refid="1" />
				<DataComparisonType refid="2" />
				<DataComparisonType refid="4" />
				<DataComparisonType refid="18" />
				<DataComparisonType refid="8" />
				<DataComparisonType refid="9" />
				<DataComparisonType refid="10" />
				<DataComparisonType refid="11" />
				<DataComparisonType refid="17" />
				<DataComparisonType refid="7" />
				<DataComparisonType refid="16" />
			</DataComparisonTypes>
		</DataSubtype>
		<DataSubtype id="400">
			<Name>static.type.data.sub.type.currency</Name>
			<Selectable>TRUE</Selectable>
			<DataComparisonTypes>
				<DataComparisonType refid="1" />
				<DataComparisonType refid="2" />
				<DataComparisonType refid="4" />
				<DataComparisonType refid="18" />
				<DataComparisonType refid="8" />
				<DataComparisonType refid="9" />
				<DataComparisonType refid="10" />
				<DataComparisonType refid="11" />
				<DataComparisonType refid="17" />
				<DataComparisonType refid="7" />
				<DataComparisonType refid="16" />
			</DataComparisonTypes>
		</DataSubtype>
		<DataSubtype id="401">
			<Name>static.type.data.sub.type.float</Name>
			<Selectable>TRUE</Selectable>
			<DataComparisonTypes>
				<DataComparisonType refid="1" />
				<DataComparisonType refid="2" />
				<DataComparisonType refid="4" />
				<DataComparisonType refid="18" />
				<DataComparisonType refid="8" />
				<DataComparisonType refid="9" />
				<DataComparisonType refid="10" />
				<DataComparisonType refid="11" />
				<DataComparisonType refid="17" />
				<DataComparisonType refid="7" />
				<DataComparisonType refid="16" />
			</DataComparisonTypes>
		</DataSubtype>
		<DataSubtype id="402">
			<Name>static.type.data.sub.type.double</Name>
			<Selectable>TRUE</Selectable>
			<DataComparisonTypes>
				<DataComparisonType refid="1" />
				<DataComparisonType refid="2" />
				<DataComparisonType refid="4" />
				<DataComparisonType refid="18" />
				<DataComparisonType refid="8" />
				<DataComparisonType refid="9" />
				<DataComparisonType refid="10" />
				<DataComparisonType refid="11" />
				<DataComparisonType refid="17" />
				<DataComparisonType refid="7" />
				<DataComparisonType refid="16" />
			</DataComparisonTypes>
		</DataSubtype>
		<DataSubtype id="499">
			<Name>static.type.data.sub.type.numeric</Name>
			<Selectable>TRUE</Selectable>
			<DataComparisonTypes>
				<DataComparisonType refid="1" />
				<DataComparisonType refid="2" />
				<DataComparisonType refid="4" />
				<DataComparisonType refid="18" />
				<DataComparisonType refid="8" />
				<DataComparisonType refid="9" />
				<DataComparisonType refid="10" />
				<DataComparisonType refid="11" />
				<DataComparisonType refid="17" />
				<DataComparisonType refid="7" />
				<DataComparisonType refid="16" />
			</DataComparisonTypes>
		</DataSubtype>
		<DataSubtype id="600">
			<Name>static.type.data.sub.type.date</Name>
			<Selectable>TRUE</Selectable>
			<DataComparisonTypes>
				<DataComparisonType refid="1" />
				<DataComparisonType refid="2" />
				<DataComparisonType refid="7" />
				<DataComparisonType refid="8" />
				<DataComparisonType refid="9" />
				<DataComparisonType refid="10" />
				<DataComparisonType refid="11" />
				<DataComparisonType refid="16" />
				<DataComparisonType refid="17" />
				<DataComparisonType refid="7" />
				<DataComparisonType refid="16" />
			</DataComparisonTypes>
		</DataSubtype>
		<DataSubtype id="800">
			<Name>static.type.data.sub.type.boolean</Name>
			<Selectable>TRUE</Selectable>
			<DataComparisonTypes>
				<DataComparisonType refid="1" />
				<DataComparisonType refid="2" />
				<DataComparisonType refid="7" />
				<DataComparisonType refid="16" />
			</DataComparisonTypes>
		</DataSubtype>
		<DataSubtype id="998">
			<Name>static.type.data.sub.type.image</Name>
			<Selectable>FALSE</Selectable>
		</DataSubtype>
		<DataSubtype id="999">
			<Name>static.type.data.sub.type.unknown</Name>
			<Selectable>FALSE</Selectable>
			<DataComparisonTypes>
				<DataComparisonType refid="1" />
				<DataComparisonType refid="2" />
				<DataComparisonType refid="12" />
				<DataComparisonType refid="13" />
				<DataComparisonType refid="14" />
				<DataComparisonType refid="15" />
			</DataComparisonTypes>
		</DataSubtype>
	</DataSubtypes>

	<DataTypes>
		<DataType id="1">
			<Name>static.type.data.type.string</Name>
			<Selectable>TRUE</Selectable>
			<DataSubtypes>
				<DataSubtype refid="100" />
				<DataSubtype refid="998" />
				<DataSubtype refid="999" />
			</DataSubtypes>
		</DataType>
		<DataType id="2">
			<Name>static.type.data.type.numeric</Name>
			<Selectable>TRUE</Selectable>
			<DataSubtypes>
				<DataSubtype refid="200" />
				<DataSubtype refid="201" />
				<DataSubtype refid="202" />
				<DataSubtype refid="203" />
				<DataSubtype refid="400" />
				<DataSubtype refid="401" />
				<DataSubtype refid="402" />
				<DataSubtype refid="499" />
				<DataSubtype refid="999" />
			</DataSubtypes>
		</DataType>
		<DataType id="3">
			<Name>static.type.data.type.date</Name>
			<Selectable>TRUE</Selectable>
			<DataSubtypes>
				<DataSubtype refid="600" />
				<DataSubtype refid="999" />
			</DataSubtypes>
		</DataType>
		<DataType id="4">
			<Name>static.type.data.type.boolean</Name>
			<Selectable>TRUE</Selectable>
			<DataSubtypes>
				<DataSubtype refid="800" />
				<DataSubtype refid="999" />
			</DataSubtypes>
		</DataType>
		<DataType id="5">
			<Name>static.type.data.type.integer</Name>
			<Selectable>TRUE</Selectable>
			<DataSubtypes>
				<DataSubtype refid="200" />
				<DataSubtype refid="999" />
			</DataSubtypes>
		</DataType>
		<DataType id="6">
			<Name>static.type.data.type.currency</Name>
			<Selectable>TRUE</Selectable>
			<DataSubtypes>
				<DataSubtype refid="400" />
				<DataSubtype refid="999" />
			</DataSubtypes>
		</DataType>
		<DataType id="7">
			<Name>static.type.data.type.double</Name>
			<Selectable>TRUE</Selectable>
			<DataSubtypes>
				<DataSubtype refid="402" />
				<DataSubtype refid="999" />
			</DataSubtypes>
		</DataType>
	</DataTypes>

	<DateDataValueTypes>
		<DateDataValueType id="1">
			Today
		</DateDataValueType>
		<DateDataValueType id="2">
			RunDate
		</DateDataValueType>
		<DateDataValueType id="3">
			StartOfMonth
		</DateDataValueType>
		<DateDataValueType id="4">
			EndOfMonth
		</DateDataValueType>
		<DateDataValueType id="5">
			StartOfLastMonth
		</DateDataValueType>
		<DateDataValueType id="6">
			EndOfLastMonth
		</DateDataValueType>
		<DateDataValueType id="7">
			StartOfCalendarYear
		</DateDataValueType>
		<DateDataValueType id="8">
			EndOfCalendarYear
		</DateDataValueType>
		<DateDataValueType id="9">
			StartOfLastCalendarYear
		</DateDataValueType>
		<DateDataValueType id="10">
			EndOfLastCalendarYear
		</DateDataValueType>
		<DateDataValueType id="11">
			StartOfCalendarQuarter
		</DateDataValueType>
		<DateDataValueType id="12">
			EndOfCalendarQuarter
		</DateDataValueType>
		<DateDataValueType id="13">
			StartOfLastCalendarQuarter
		</DateDataValueType>
		<DateDataValueType id="14">
			EndOfLastCalendarQuarter
		</DateDataValueType>
		<DateDataValueType id="15">
			SpecificDate
		</DateDataValueType>
	</DateDataValueTypes>

	<ConditionTypes>
		<ConditionType id="1">
			<Name>static.type.condition.type.one_of</Name>
			<Parameterized>true</Parameterized>
		</ConditionType>
		<ConditionType id="2">
			<Name>static.type.condition.type.all_of</Name>
			<Parameterized>true</Parameterized>
		</ConditionType>
		<ConditionType id="3">
			<Name>static.type.condition.type.any_of</Name>
			<Parameterized>true</Parameterized>
		</ConditionType>
		<!-- <ConditionType id="3">
			Dropdown
		</ConditionType> -->
		<!-- <ConditionType id="4">
			Parameterized Radio
		</ConditionType>
		<ConditionType id="5">
			Parameterized Checkbox
		</ConditionType>
		<ConditionType id="6">
			Textbox
		</ConditionType> -->
	</ConditionTypes>

    <RecordTypes>
    	<!-- string id's reference a property file for language localization -->
        <RecordType id="1" stringid="page.text.record_types">
            <Name>Record Types</Name>
        </RecordType>
        <RecordType id="2" stringid="page.text.customer_identifiers">
            <Name>Customer Identifiers</Name>
        </RecordType>
        <RecordType id="3" stringid="page.text.fixed_record_count">
            <Name>Fixed Record Count</Name>
        </RecordType>
    </RecordTypes>

	<JobMetadataDetailParts>
		<JobMetadataDetailPart id="1" sequence="3">
			<Name>QEPreProcessor</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="2" sequence="7">
			<Name>QualificationEngine</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="3" sequence="8">
			<Name>Connector</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="4" sequence="12">
			<Name>QEPostProcessor</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="5" sequence="13">
			<Name>ReportProcessor</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="6" sequence="14">
			<Name>ScenarioProcessor</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="7" sequence="9">
			<Name>QE_eDeliveryEngine</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="8" sequence="10">
			<Name>QE_eDeliveryProcessor</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="9" sequence="11">
			<Name>ExactTargetProcessor</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="10" sequence="6">
			<Name>PreQualificationEngine</Name>
		</JobMetadataDetailPart>				
		<JobMetadataDetailPart id="11" sequence="15">
			<Name>TransitionAPI</Name>
		</JobMetadataDetailPart>				
		<JobMetadataDetailPart id="12" sequence="16">
			<Name>ProcessManager</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="13" sequence="17">
			<Name>PreQualEngineCustomScript</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="14" sequence="18">
			<Name>PostQualEngineCustomScript</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="15" sequence="19">
			<Name>PostConnectorCustomScript</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="16" sequence="20">
			<Name>OnPrem</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="17" sequence="1">
			<Name>BundleRequest</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="18" sequence="2">
			<Name>Bundling</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="19" sequence="4">
			<Name>Sending</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="20" sequence="5">
			<Name>WaitingForResult</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="21" sequence="21">
			<Name>ReceivingResult</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="22" sequence="22">
			<Name>BatchReporter</Name>
		</JobMetadataDetailPart>
		<JobMetadataDetailPart id="23" sequence="23">
			<Name>ClientProcessManager</Name>
		</JobMetadataDetailPart>
	</JobMetadataDetailParts>

	<SystemThemes>
		<SystemTheme id="1">
			<Name>static.type.system.theme.ocean_blue</Name>
			<Folder>theme_dark_blue</Folder>
		</SystemTheme>
		<SystemTheme id="2">
			<Name>static.type.system.theme.red_rose</Name>
			<Folder>theme_red_grey</Folder>
		</SystemTheme>
		<SystemTheme id="3">
			<Name>static.type.system.theme.ever_green</Name>
			<Folder>theme_dark_green</Folder>
		</SystemTheme>
		<SystemTheme id="4">
			<Name>static.type.system.theme.monochrome</Name>
			<Folder>theme_monochrome</Folder>
		</SystemTheme>
	</SystemThemes>

	<BackgroundThemes>
		<BackgroundTheme id="1">
			<Name>static.type.background.theme.red</Name>
			<Filename>gradient_red.gif</Filename>
		</BackgroundTheme>
		<BackgroundTheme id="2">
			<Name>static.type.background.theme.light_blue</Name>
			<Filename>gradient_light_blue.gif</Filename>
		</BackgroundTheme>
		<BackgroundTheme id="3">
			<Name>static.type.background.theme.blue</Name>
			<Filename>gradient_blue.gif</Filename>
		</BackgroundTheme>
		<BackgroundTheme id="4">
			<Name>static.type.background.theme.dark_green</Name>
			<Filename>gradient_dark_green.gif</Filename>
		</BackgroundTheme>	
		<BackgroundTheme id="5">
			<Name>static.type.background.theme.gray</Name>
			<Filename>gradient_gray.gif</Filename>
		</BackgroundTheme>
		<BackgroundTheme id="6">
			<Name>static.type.background.theme.yellow</Name>
			<Filename>gradient_yellow.gif</Filename>
		</BackgroundTheme>
		<BackgroundTheme id="7">
			<Name>static.type.background.theme.orange</Name>
			<Filename>gradient_orange.gif</Filename>
		</BackgroundTheme>	
	</BackgroundThemes>

	<UserDeactivateReasonTypes>
		<DeactivateReasonType id="1">user.deactivate.reason.1</DeactivateReasonType>
		<DeactivateReasonType id="2">user.deactivate.reason.2</DeactivateReasonType>
		<DeactivateReasonType id="3">user.deactivate.reason.3</DeactivateReasonType>
	</UserDeactivateReasonTypes>
	
	<SystemProperties>
		<SystemProperty id="1" readonly="true">
			<Key>message.folder.images</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/dynamiccontent/images</Value>
		</SystemProperty>
		<SystemProperty id="2" readonly="true">
			<Key>message.folder.output</Key>
			<Value>@@MESSAGEPOINT_OUTPUT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/dynamiccontent/output</Value>
		</SystemProperty>
		<SystemProperty id="3" readonly="false">
			<Key>message.folder.applicationcache</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/files/applications/cache</Value>
		</SystemProperty>
		<SystemProperty id="4" readonly="false">
			<Key>message.folder.applicationreport</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/files/applications/configuration/dialogue/appreport</Value>
		</SystemProperty>
		<SystemProperty id="6" readonly="false">
			<Key>job.folder.outgoing</Key>
			<Value>@@MESSAGEPOINTJOB_FILEROOT@@/outgoing/</Value>
		</SystemProperty>
		<SystemProperty id="7" readonly="false">
			<Key>job.folder.incoming</Key>
			<Value>@@MESSAGEPOINTJOB_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/incoming/</Value>
		</SystemProperty>
		<SystemProperty id="8" readonly="false">
			<Key>job.folder.working</Key>
			<Value>@@MESSAGEPOINTJOB_WORKING_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/temp/</Value>
		</SystemProperty>
		<SystemProperty id="9" readonly="false">
			<Key>site.page.defaultPage</Key>
			<Value>1</Value>
		</SystemProperty>
		<SystemProperty id="10" readonly="false">
			<Key>site.page.defaultPageSize</Key>
			<Value>50</Value>
		</SystemProperty>
		<SystemProperty id="11" readonly="false">
			<Key>site.page.defaultMaxPages</Key>
			<Value>5</Value>
		</SystemProperty>
		<SystemProperty id="12" readonly="true">
			<Key>system.date.format</Key>
			<Value>MMM dd, yyyy</Value>
		</SystemProperty>
		<SystemProperty id="13" readonly="false">
			<Key>message.folder.dispatch</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/dynamiccontent/application/dispatch/</Value>
		</SystemProperty>
		<SystemProperty id="14" readonly="false">
			<Key>message.folder.application</Key>
			<Value>/dynamiccontent/application/@@MESSAGEPOINT_SCHEMA@@/</Value>
		</SystemProperty>
		<SystemProperty id="16" readonly="true">
			<Key>message.folder.datafiles</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/files/datafiles</Value>
		</SystemProperty>
		<SystemProperty id="17" readonly="false">
			<Key>email.cfg.systemAdmins</Key>
			<Value><EMAIL></Value>
		</SystemProperty>
		<SystemProperty id="18" readonly="false">
			<Key>email.cfg.appFromAddress</Key>
			<Value><EMAIL></Value>
		</SystemProperty>
		<SystemProperty id="19" readonly="false">
			<Key>job.folder.incoming.sucess</Key>
			<Value>@@MESSAGEPOINTJOB_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/success/</Value>
		</SystemProperty>
		<SystemProperty id="20" readonly="false">
			<Key>job.folder.incoming.failure</Key>
			<Value>@@MESSAGEPOINTJOB_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/failure/</Value>
		</SystemProperty>
		<SystemProperty id="24" readonly="false">
			<Key>theme.system</Key>
			<Value>Ocean Blue</Value>
		</SystemProperty>
		<SystemProperty id="25" readonly="true">
			<Key>platform.encodingtype.preview</Key>
			<Value>ASCII</Value>
		</SystemProperty>
		<SystemProperty id="26" readonly="true">
			<Key>platform.encodingtype.test</Key>
			<Value>ASCII</Value>
		</SystemProperty>
		<SystemProperty id="27" readonly="true">
			<Key>platform.encodingtype.production</Key>
			<Value>ASCII</Value>
		</SystemProperty>
		<SystemProperty id="28" readonly="true">
			<Key>platform.encodingtype.default</Key>
			<Value>ASCII</Value>
		</SystemProperty>
		<SystemProperty id="31" readonly="true">
			<Key>message.messageContent.history.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/history/messagecontent</Value>
		</SystemProperty>
		<SystemProperty id="32" readonly="false">
			<Key>global.sendemail.booleanflag</Key>
			<Value>true</Value>
		</SystemProperty>
		<SystemProperty id="33" readonly="false">
			<Key>email.server.outgoing</Key>
			<Value>127.0.0.1</Value>
		</SystemProperty>
		<SystemProperty id="34" readonly="false">
			<Key>email.server.outgoing.port</Key>
			<Value>1025</Value>
		</SystemProperty>
		<SystemProperty id="35" readonly="false">
			<Key>email.server.outgoing.user</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="36" readonly="false">
			<Key>email.server.outgoing.password</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="37" readonly="false">
			<Key>.model.admin.DataTypeFormat.booleanSeparatorKey</Key>
			<Value>;</Value>
		</SystemProperty>
		<SystemProperty id="38" readonly="true">
			<Key>.model.admin.DataTypeFormat.defaultStringFormat</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="39" readonly="true">
			<Key>.model.admin.DataTypeFormat.defaultDateFormat</Key>
			<Value>M dd, yyyy</Value>
		</SystemProperty>
		<SystemProperty id="40" readonly="false">
			<Key>.model.admin.DataTypeFormat.defaultBooleanFormat.true</Key>
			<Value>T</Value>
		</SystemProperty>
		<SystemProperty id="41" readonly="false">
			<Key>.model.admin.DataTypeFormat.defaultBooleanFormat.false</Key>
			<Value>F</Value>
		</SystemProperty>
		<SystemProperty id="42" readonly="true">
			<Key>.model.admin.DataTypeFormat.defaultBooleanFormat.separator</Key>
			<Value>;</Value>
		</SystemProperty>
		<SystemProperty id="47"  readonly="false">
			<Key>mode.development</Key>
			<Value>false</Value> 
		</SystemProperty>
		<SystemProperty id="48"  readonly="true">
			<Key>version</Key>
			<Value>2.0.001</Value> 
		</SystemProperty>
		<SystemProperty id="50" readonly="false">
			<Key>email.pop3.host</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="51" readonly="false">
			<Key>email.pop3.user</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="52" readonly="false">
			<Key>email.pop3.password</Key>
			<Value></Value>
		</SystemProperty>	
		<SystemProperty id="53" readonly="false">
			<Key>email.pop3.port</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="54" readonly="false">
			<Key>email.server.connection.security</Key>
			<Value>0</Value>
		</SystemProperty>

		<SystemProperty id="55" readonly="false">
			<Key>contactSupport.emailAddress</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="56" readonly="false">
			<Key>contactSupport.instructions</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="57" readonly="false">
			<Key>contactSupport.phoneNumber</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="64" readonly="false">
			<Key>job.test.customers.max</Key>
			<Value>500</Value>
		</SystemProperty>
		<SystemProperty id="74" readonly="false">
			<Key>job.proof.customers.max</Key>
			<Value>500</Value>
		</SystemProperty>
		<SystemProperty id="65" readonly="false">
			<Key>simulation.export.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/simulation/</Value>
		</SystemProperty>				
		<SystemProperty id="69" readonly="false">
			<Key>xmlconnector.xslt.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/files/xsltfiles/</Value>
		</SystemProperty>			
		<SystemProperty id="71" readonly="false">
			<Key>insert.startingBinNumber</Key>
			<Value>1</Value>
		</SystemProperty>
		<SystemProperty id="72" readonly="false">
			<Key>insert.weightUnits</Key>
			<Value>2</Value>
		</SystemProperty>
		<SystemProperty id="73" readonly="false">
			<Key>insert.schedule.export.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/insertschedule/</Value>
		</SystemProperty>				
		<SystemProperty id="75" readonly="false">
			<Key>message.export.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/messageexport/</Value>
		</SystemProperty>	
		<SystemProperty id="76" readonly="false">
			<Key>email.fileroot.template.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/emailTemplates/</Value>
		</SystemProperty>
		<SystemProperty id="77" readonly="false">
			<Key>email.webroot.dir</Key>
			<Value>/dynamiccontent/email/@@MESSAGEPOINT_SCHEMA@@/</Value>
		</SystemProperty>
		<SystemProperty id="78" readonly="false">
			<Key>email.fileroot.preview.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/emailPreviews/</Value>
		</SystemProperty>
		<SystemProperty id="79" readonly="false">
			<Key>where.used.reports.fileroot.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/whereUsedReports/</Value>
		</SystemProperty>
		<SystemProperty id="80" readonly="false">
			<Key>messagepoint.object.export.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/messagepointObjectExport/</Value>
		</SystemProperty>	
		<SystemProperty id="81" readonly="false">
			<Key>audit.reports.fileroot.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/auditReports/</Value>
		</SystemProperty>
		<SystemProperty id="82" readonly="false">
			<Key>reports.fileroot.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/reports/</Value>
		</SystemProperty>
		<SystemProperty id="83" readonly="false">
			<Key>diagnostics.reports.fileroot.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/diagnosticsReports/</Value>
		</SystemProperty>	
		<SystemProperty id="84" readonly="false">
			<Key>messagepoint.qe.fileroot</Key>
			<Value>@@MESSAGEPOINTQE_FILEROOT@@/</Value>
		</SystemProperty>
		
		<SystemProperty id="85" readonly="false">
			<Key>exact.target.wsdl.location</Key>
			<Value>https://webservice.s4.exacttarget.com/etframework.wsdl</Value>
		</SystemProperty>
		<SystemProperty id="86" readonly="false">
			<Key>exact.target.web.service.user</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="87" readonly="false">
			<Key>exact.target.web.service.password</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="88" readonly="false">
			<Key>exact.target.web.service.file.location</Key>
			<Value>File://ETFTP/Import/</Value>
		</SystemProperty>
		<SystemProperty id="89" readonly="false">
			<Key>exact.target.ftp.host</Key>
			<Value>ftp.s4.exacttarget.com</Value>
		</SystemProperty>
		<SystemProperty id="90" readonly="false">
			<Key>exact.target.ftp.user</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="91" readonly="false">
			<Key>exact.target.ftp.password</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="92" readonly="false">
			<Key>exact.target.ftp.import.location</Key>
			<Value>/Import/</Value>
		</SystemProperty>
		
		<SystemProperty id="93" readonly="false">
			<Key>services.axis2.repository.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/repository</Value>
		</SystemProperty>
		
		<SystemProperty id="95" readonly="false">
			<Key>clickatell.user</Key>
			<Value></Value>
		</SystemProperty>	
		<SystemProperty id="96" readonly="false">
			<Key>clickatell.password</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="97" readonly="false">
			<Key>clickatell.appid</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="98" readonly="false">
			<Key>clickatell.from</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="99" readonly="false">
			<Key>clickatell.auth.api.url</Key>
			<Value>https://api.clickatell.com/http/auth</Value>
		</SystemProperty>
		<SystemProperty id="100" readonly="false">
			<Key>clickatell.send.api.url</Key>
			<Value>https://platform.clickatell.com/messages/http/send</Value>
		</SystemProperty>											
		<SystemProperty id="101" readonly="false">
			<Key>sso.url.redirect</Key>
			<Value>http://www.messagepoint.com</Value>
		</SystemProperty>	

		<SystemProperty id="102" readonly="false">
			<Key>exact.target.ant.path</Key>
			<Value>@@ANT_HOME@@/bin/ant</Value>
		</SystemProperty>
		<SystemProperty id="103" readonly="false">
			<Key>exact.target.build.xml.path</Key>
			<Value>@@UMH_BUILD_XML@@</Value>
		</SystemProperty>
		
		<SystemProperty id="104" readonly="false">
			<Key>ftp1.host</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="105" readonly="false">
			<Key>ftp1.user</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="106" readonly="false">
			<Key>ftp1.password</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="107" readonly="false">
			<Key>ftp2.host</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="108" readonly="false">
			<Key>ftp2.user</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="109" readonly="false">
			<Key>ftp2.password</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="110" readonly="false">
			<Key>ftp3.host</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="111" readonly="false">
			<Key>ftp3.user</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="112" readonly="false">
			<Key>ftp3.password</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="113" readonly="false">
			<Key>system.default.language</Key>
			<Value>en</Value>
		</SystemProperty>
		<SystemProperty id="114" readonly="false">
			<Key>system.server.csrf.domain.strict</Key>
			<Value>true</Value>
		</SystemProperty>
		<SystemProperty id="115" readonly="true">
			<Key>message.folder.composition.files.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/files/compositionfiles</Value>
		</SystemProperty>
		<SystemProperty id="116" readonly="false">
			<Key>remote.server.ip</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="117" readonly="false">
			<Key>remote.server.port</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="119" readonly="false">
			<Key>remote.server.user</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="120" readonly="false">
			<Key>remote.server.password</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="121" readonly="false">
			<Key>target.rule.separator.for.is.one.of</Key>
			<Value>,</Value>
		</SystemProperty>
		<SystemProperty id="122" readonly="false">
			<Key>de.versions</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="123" readonly="false">
			<Key>system.server.analytics.namespace</Key>
			<Value>messagepoint.co</Value>
		</SystemProperty>
		<SystemProperty id="124" readonly="true">
			<Key>targeting.fileroot.datafiles.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/files/targeting/datafiles</Value>
		</SystemProperty>
		<SystemProperty id="125" readonly="false">
			<Key>signin.theme</Key>
			<Value>0</Value>
		</SystemProperty>
		<SystemProperty id="126" readonly="false">
			<Key>signin.restrictSigninAutocomplete</Key>
			<Value>false</Value>
		</SystemProperty>
		<SystemProperty id="127" readonly="false">
			<Key>system.server.test.mode</Key>
			<Value>false</Value>
		</SystemProperty>
		
		<SystemProperty id="128" readonly="false">
			<Key>sso.ping.saas.id</Key>
			<Value>439f6c05-b67a-4499-84f9-f49488bd6773</Value>
		</SystemProperty>	
		<SystemProperty id="129" readonly="false">
			<Key>sso.ping.init.sso.url</Key>
			<Value><![CDATA[https://sso.connect.pingidentity.com/sso/sp/initsso?saasid=%s&idpid=%s]]></Value>
		</SystemProperty>	
		<SystemProperty id="130" readonly="false">
			<Key>sso.ping.token.resolution.url</Key>
			<Value><![CDATA[https://sso.connect.pingidentity.com/sso/TXS/2.0/2/%s]]></Value>
		</SystemProperty>	
		<SystemProperty id="131" readonly="false">
			<Key>sso.ping.rest.api.client.id</Key>
			<Value>fa0b2927-af33-4dd7-9248-0ec4a95bca8b</Value>
		</SystemProperty>	
		<SystemProperty id="132" readonly="false">
			<Key>sso.ping.rest.api.client.key</Key>
			<Value>Prinova1</Value>
		</SystemProperty>	
		
		<SystemProperty id="133" readonly="false">
			<Key>cms.host</Key>
			<Value><![CDATA[http://localhost:8080/cs/]]></Value>
		</SystemProperty>
		<SystemProperty id="134" readonly="false">
			<Key>cms.user</Key>
			<Value>fwadmin</Value>
		</SystemProperty>
		<SystemProperty id="135" readonly="false">
			<Key>cms.password</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="136" readonly="false">
			<Key>cms.image.asset.definition</Key>
			<Value><![CDATA[avisports:AVIImage:imageFile, FirstSiteII:Media_C:FSII_ImageFile]]></Value>
		</SystemProperty>
		<SystemProperty id="137" readonly="false">
			<Key>cms.image.category.definition</Key>
			<Value><![CDATA[avisports:ImageCategory, FirstSiteII:Media_P]]></Value>
		</SystemProperty>
		<SystemProperty id="138" readonly="false">
			<Key>proxy.sso.idpid</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="139" readonly="false">
			<Key>proxy.sso.secretkey</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="140" readonly="false">
			<Key>proxy.sso.masterpageurl</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="141" readonly="false">
			<Key>proxy.sso.pod.role</Key>
			<Value>2</Value>
		</SystemProperty>
		<SystemProperty id="142" readonly="false">
			<Key>connected.push.fileroot.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/connected/push/</Value>
		</SystemProperty>	
		<SystemProperty id="143" readonly="false">
			<Key>signin.marketing.url</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="144" readonly="false">
			<Key>content.history.recording</Key>
			<Value>true</Value>
		</SystemProperty>
		<SystemProperty id="145" readonly="false">
			<Key>content.selector.list.accessible</Key>
			<Value>false</Value>
		</SystemProperty>
		<SystemProperty id="146" readonly="false">
			<Key>metadata.reports.fileroot.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/metadataReports/</Value>
		</SystemProperty>
		<SystemProperty id="147" readonly="false">
			<Key>sftprepo.host</Key>
			<Value><![CDATA[]]></Value>
		</SystemProperty>
		<SystemProperty id="148" readonly="false">
			<Key>sftprepo.user</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="149" readonly="false">
			<Key>sftprepo.password</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="150" readonly="false">
			<Key>sftprepo.folder</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="151" readonly="false">
			<Key>sftprepo.port</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="152" readonly="false">
			<Key>sftprepo.bulksize</Key>
			<Value></Value>
		</SystemProperty>
		
		<SystemProperty id="154" readonly="false">
			<Key>composition.engines.fileroot</Key>
			<Value>@@COMPOSITIONENGINES_FILEROOT@@/</Value>
		</SystemProperty>
		
		<SystemProperty id="155" readonly="false">
			<Key>tasklog.fileroot.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/files/tasklogs/</Value>
		</SystemProperty>
		
		<SystemProperty id="156" readonly="false">
			<Key>email.delivery.event.support.notification</Key>
			<Value><EMAIL></Value>
		</SystemProperty>
		
		<SystemProperty id="157" readonly="true">
            <Key>tpexport.import.flag.code.1</Key>
            <Value>bRDvJHND+Xz7eqs3cI2Do22z/kAZfsLJqU2HjBII6Ac=</Value>
		</SystemProperty>
		
		<SystemProperty id="158" readonly="false">
			<Key>dictionary.fileroot.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/dictionaries/</Value>
		</SystemProperty>

		<SystemProperty id="159" readonly="false">
			<Key>bundle.delivery.post.process.scripts.folder</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/bundleDeliveryPostProcessScripts/</Value>
		</SystemProperty>

		<SystemProperty id="160" readonly="false">
			<Key>system.server.analytics.amplitude.api.key</Key>
			<Value>187c89fefcf5a38ecc6ee2c3d1bce9f9</Value>
		</SystemProperty>

		<SystemProperty id="161" readonly="false">
			<Key>system.server.analytics.amplitude.enabled</Key>
			<Value>true</Value>
		</SystemProperty>

		<SystemProperty id="162" readonly="false">
			<Key>system.server.analytics.fullstory.enabled</Key>
			<Value>false</Value>
		</SystemProperty>

		<SystemProperty id="163" readonly="false">
			<Key>rationalizer.files.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/rationalizer/files/</Value>
		</SystemProperty>
		
		<SystemProperty id="164" readonly="false">
			<Key>content.intelligence.target.readability.level</Key>
			<Value>4</Value>
		</SystemProperty>

		<SystemProperty id="165" readonly="false">
			<Key>content.intelligence.content.compare.enabled</Key>
			<Value>false</Value>
		</SystemProperty>

		<SystemProperty id="166" readonly="false">
			<Key>content.intelligence.translate.engine</Key>
			<Value>genai</Value>
		</SystemProperty>


		<SystemProperty id="172" readonly="false">
			<Key>statistic.report.pod.level.generating</Key>
			<Value>true</Value>
		</SystemProperty>

		<SystemProperty id="173" readonly="false">
			<Key>site.page.disallowUploadFileType</Key>
			<Value>sh,exe,py,ps1,bash,vbs,bat,com</Value>
		</SystemProperty>

		<SystemProperty id="174" readonly="false">
			<Key>data.anonymizer.delimeter</Key>
			<Value>,</Value>
		</SystemProperty>

		<SystemProperty id="175" readonly="false">
			<Key>site.page.allowUploadFileType</Key>
			<Value></Value>
		</SystemProperty>

        <SystemProperty id="176" readonly="false">
            <Key>document.history.dir</Key>
            <Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/files/document_history/</Value>
        </SystemProperty>

		<SystemProperty id="177" readonly="false">
			<Key>editor.inlineSpellChecking</Key>
			<Value>true</Value>
		</SystemProperty>
		<SystemProperty id="178" readonly="false">
			<Key>pinc.server</Key>
			<Value></Value>
		</SystemProperty>
		<SystemProperty id="179" readonly="false">
			<Key>emailproof.email.address</Key>
			<Value><EMAIL></Value>
		</SystemProperty>

		<SystemProperty id="189" readonly="false">
			<Key>marcie.boot.url</Key>
			<Value></Value>
		</SystemProperty>

		<SystemProperty id="190" readonly="false">
			<Key>marcie.boot.api.key</Key>
			<Value></Value>
		</SystemProperty>

		<SystemProperty id="191" readonly="false">
			<Key>content.delivery.network.enabled</Key>
			<Value>false</Value>
		</SystemProperty>

		<SystemProperty id="192" readonly="false">
			<Key>content.delivery.network.domain</Key>
			<Value></Value>
		</SystemProperty>

		<SystemProperty id="193" readonly="false">
			<Key>web.assets.service.url</Key>
			<Value></Value>
		</SystemProperty>

		<SystemProperty id="194" readonly="false">
			<Key>job.production.max.concurrent</Key>
			<Value>2</Value>
		</SystemProperty>

		<SystemProperty id="195" readonly="false">
			<Key>job.proof.max.concurrent</Key>
			<Value>10</Value>
		</SystemProperty>

		<SystemProperty id="196" readonly="false">
			<Key>job.other.max.concurrent</Key>
			<Value>5</Value>
		</SystemProperty>

		<SystemProperty id="197" readonly="false">
			<Key>job.send.chunk.size</Key>
			<Value>20971520</Value>
		</SystemProperty>

		<SystemProperty id="198" readonly="false">
			<Key>allow.duplicate.external.ids</Key>
			<Value>false</Value>
		</SystemProperty>

		<SystemProperty id="199" readonly="false">
			<Key>backend.component.repository.s3.bucket</Key>
			<Value></Value>
		</SystemProperty>

		<SystemProperty id="200" readonly="false">
			<Key>backend.component.repository.access.key.id</Key>
			<Value></Value>
		</SystemProperty>

		<SystemProperty id="201" readonly="false">
			<Key>backend.component.repository.secret.access.key</Key>
			<Value></Value>
		</SystemProperty>

		<SystemProperty id="202" readonly="false">
			<Key>rationalizer.max.doc.per.app</Key>
			<Value>500</Value>
		</SystemProperty>

		<SystemProperty id="203" readonly="false">
			<Key>de.log.default.windows.location</Key>
			<Value>/messagepoint</Value>
		</SystemProperty>

		<SystemProperty id="204" readonly="false">
			<Key>de.log.default.linux.location</Key>
			<Value>/var/log</Value>
		</SystemProperty>

		<SystemProperty id="205" readonly="false">
			<Key>de.logging.enabled</Key>
			<Value>false</Value>
		</SystemProperty>

		<SystemProperty id="206" readonly="false">
			<Key>connected.permitted.domains</Key>
			<Value>app.ccpdev.shared.banksvcs.net, app.ccpsit.shared.banksvcs.net, app.ccpuat.shared.banksvcs.net, app.ccp.shared.banksvcs.net</Value>
		</SystemProperty>

		<SystemProperty id="207" readonly="false">
			<Key>connected.no.job.preproof</Key>
			<Value>true</Value>
		</SystemProperty>
		<SystemProperty id="208" readonly="false">
			<Key>connected.no.job.proof</Key>
			<Value>true</Value>
		</SystemProperty>
		<SystemProperty id="209" readonly="false">
			<Key>connected.no.job.production</Key>
			<Value>true</Value>
		</SystemProperty>
		<SystemProperty id="210" readonly="false">
			<Key>bypass.touchpoint.association.validation</Key>
			<Value>false</Value>
		</SystemProperty>
		<SystemProperty id="211" readonly="false">
			<Key>domain.level.spellcheck.dictionary</Key>
			<Value>false</Value>
		</SystemProperty>
		<SystemProperty id="212" readonly="false">
			<Key>spellcheck.dictionary.type</Key>
			<Value>2</Value>
		</SystemProperty>
		<SystemProperty id="213" readonly="false">
			<Key>system.server.variant.excel.exports.for.domain</Key>
			<Value>false</Value>
		</SystemProperty>
		<SystemProperty id="214" readonly="false">
			<Key>transform.reports.fileroot.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/transformReports/</Value>
		</SystemProperty>
		<SystemProperty id="215" readonly="false">
			<Key>content.assistant.reports.fileroot.dir</Key>
			<Value>@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@/contentAssistantReports/</Value>
		</SystemProperty>
		<SystemProperty id="216" readonly="false">
			<Key>system.server.require.description.on.tasks</Key>
			<Value>false</Value>
		</SystemProperty>
		<SystemProperty id="217" readonly="false">
			<Key>site.page.newEditor</Key>
			<Value>true</Value>
		</SystemProperty>
		<SystemProperty id="218" readonly="false">
			<Key>connected.new.undo</Key>
			<Value>true</Value>
		</SystemProperty>
		<SystemProperty id="219" readonly="false">
			<Key>connected.rest.api.enabled</Key>
			<Value>true</Value>
		</SystemProperty>
		<SystemProperty id="220" readonly="false">
			<Key>dynamic.variants.inherit.or.custom.all</Key>
			<Value>false</Value>
		</SystemProperty>
	</SystemProperties>

	<MessagepointLocales>
		<MessagepointLocale id="1">
			<Name>English (US)</Name>
			<DisplayCode>page.locale.english.us</DisplayCode>
			<Code>en_us</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="2">
			<Name>English (Canada)</Name>
			<DisplayCode>page.locale.english.ca</DisplayCode>
			<Code>en_ca</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="3">
			<Name>English (UK)</Name>
			<DisplayCode>page.locale.english.uk</DisplayCode>
			<Code>en_gb</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="4">
			<Name>English (Australia)</Name>
			<DisplayCode>page.locale.english.au</DisplayCode>
			<Code>en_au</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="5">
			<Name>English (Belize)</Name>
			<DisplayCode>page.locale.english.bz</DisplayCode>
			<Code>en_bz</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="6">
			<Name>English (Caribbean)</Name>
			<DisplayCode>page.locale.english.bs</DisplayCode>
			<Code>en_bs</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="7">
			<Name>English (India)</Name>
			<DisplayCode>page.locale.english.in</DisplayCode>
			<Code>en_in</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="8">
			<Name>English (Ireland)</Name>
			<DisplayCode>page.locale.english.ie</DisplayCode>
			<Code>en_ie</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="9">
			<Name>English (Jamaica)</Name>
			<DisplayCode>page.locale.english.jm</DisplayCode>
			<Code>en_jm</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="10">
			<Name>English (Malaysia)</Name>
			<DisplayCode>page.locale.english.my</DisplayCode>
			<Code>en_my</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="11">
			<Name>English (New Zealand)</Name>
			<DisplayCode>page.locale.english.nz</DisplayCode>
			<Code>en_nz</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="12">
			<Name>English (Philippines)</Name>
			<DisplayCode>page.locale.english.ph</DisplayCode>
			<Code>en_ph</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="13">
			<Name>English (Singapore)</Name>
			<DisplayCode>page.locale.english.sg</DisplayCode>
			<Code>en_sg</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="14">
			<Name>English (South Africa)</Name>
			<DisplayCode>page.locale.english.za</DisplayCode>
			<Code>en_za</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="15">
			<Name>English (Trinidad and Tobago)</Name>
			<DisplayCode>page.locale.english.tt</DisplayCode>
			<Code>en_tt</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="16">
			<Name>English (Zimbabwe)</Name>
			<DisplayCode>page.locale.english.zw</DisplayCode>
			<Code>en_zw</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25966</LanguageId>
			<LanguageName>English</LanguageName>
			<LanguageDisplayCode>page.locale.english</LanguageDisplayCode>
			<LanguageCode>en</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="17">
			<Name>French (France)</Name>
			<DisplayCode>page.locale.french.fr</DisplayCode>
			<Code>fr_fr</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26226</LanguageId>
			<LanguageName>French</LanguageName>
			<LanguageDisplayCode>page.locale.french</LanguageDisplayCode>
			<LanguageCode>fr</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="18">
			<Name>French (Canada)</Name>
			<DisplayCode>page.locale.french.ca</DisplayCode>
			<Code>fr_ca</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26226</LanguageId>
			<LanguageName>French</LanguageName>
			<LanguageDisplayCode>page.locale.french</LanguageDisplayCode>
			<LanguageCode>fr</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="19">
			<Name>French (Belgium)</Name>
			<DisplayCode>page.locale.french.be</DisplayCode>
			<Code>fr_be</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26226</LanguageId>
			<LanguageName>French</LanguageName>
			<LanguageDisplayCode>page.locale.french</LanguageDisplayCode>
			<LanguageCode>fr</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="20">
			<Name>French (Luxembourg)</Name>
			<DisplayCode>page.locale.french.lu</DisplayCode>
			<Code>fr_lu</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26226</LanguageId>
			<LanguageName>French</LanguageName>
			<LanguageDisplayCode>page.locale.french</LanguageDisplayCode>
			<LanguageCode>fr</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="21">
			<Name>French (Monaco)</Name>
			<DisplayCode>page.locale.french.mc</DisplayCode>
			<Code>fr_mc</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26226</LanguageId>
			<LanguageName>French</LanguageName>
			<LanguageDisplayCode>page.locale.french</LanguageDisplayCode>
			<LanguageCode>fr</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="22">
			<Name>French (Switzerland)</Name>
			<DisplayCode>page.locale.french.ch</DisplayCode>
			<Code>fr_ch</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26226</LanguageId>
			<LanguageName>French</LanguageName>
			<LanguageDisplayCode>page.locale.french</LanguageDisplayCode>
			<LanguageCode>fr</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="23">
			<Name>French (Netherlands)</Name>
			<DisplayCode>page.locale.french.nl</DisplayCode>
			<Code>fr_nl</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26226</LanguageId>
			<LanguageName>French</LanguageName>
			<LanguageDisplayCode>page.locale.french</LanguageDisplayCode>
			<LanguageCode>fr</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="24">
			<Name>German (Germany)</Name>
			<DisplayCode>page.locale.german.de</DisplayCode>
			<Code>de_de</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>25701</LanguageId>
			<LanguageName>German</LanguageName>
			<LanguageDisplayCode>page.locale.german</LanguageDisplayCode>
			<LanguageCode>de</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="25">
			<Name>German (Austria)</Name>
			<DisplayCode>page.locale.german.at</DisplayCode>
			<Code>de_at</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25701</LanguageId>
			<LanguageName>German</LanguageName>
			<LanguageDisplayCode>page.locale.german</LanguageDisplayCode>
			<LanguageCode>de</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="26">
			<Name>German (Liechtenstein)</Name>
			<DisplayCode>page.locale.german.li</DisplayCode>
			<Code>de_li</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25701</LanguageId>
			<LanguageName>German</LanguageName>
			<LanguageDisplayCode>page.locale.german</LanguageDisplayCode>
			<LanguageCode>de</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="27">
			<Name>German (Luxembourg)</Name>
			<DisplayCode>page.locale.german.lu</DisplayCode>
			<Code>de_lu</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25701</LanguageId>
			<LanguageName>German</LanguageName>
			<LanguageDisplayCode>page.locale.german</LanguageDisplayCode>
			<LanguageCode>de</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="28">
			<Name>German (Switzerland)</Name>
			<DisplayCode>page.locale.german.ch</DisplayCode>
			<Code>de_ch</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25701</LanguageId>
			<LanguageName>German</LanguageName>
			<LanguageDisplayCode>page.locale.german</LanguageDisplayCode>
			<LanguageCode>de</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="29">
			<Name>Italian (Italy)</Name>
			<DisplayCode>page.locale.italian.it</DisplayCode>
			<Code>it_it</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26996</LanguageId>
			<LanguageName>Italian</LanguageName>
			<LanguageDisplayCode>page.locale.italian</LanguageDisplayCode>
			<LanguageCode>it</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="30">
			<Name>Italian (Switzerland)</Name>
			<DisplayCode>page.locale.italian.ch</DisplayCode>
			<Code>it_ch</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26996</LanguageId>
			<LanguageName>Italian</LanguageName>
			<LanguageDisplayCode>page.locale.italian</LanguageDisplayCode>
			<LanguageCode>it</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="31">
			<Name>Spanish (Spain)</Name>
			<DisplayCode>page.locale.spanish.es</DisplayCode>
			<Code>es_es</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="32">
			<Name>Spanish (Argentina)</Name>
			<DisplayCode>page.locale.spanish.ar</DisplayCode>
			<Code>es_ar</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="33">
			<Name>Spanish (Venezuela)</Name>
			<DisplayCode>page.locale.spanish.ve</DisplayCode>
			<Code>es_ve</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="34">
			<Name>Spanish (Bolivia)</Name>
			<DisplayCode>page.locale.spanish.bo</DisplayCode>
			<Code>es_bo</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="35">
			<Name>Spanish (Chile)</Name>
			<DisplayCode>page.locale.spanish.cl</DisplayCode>
			<Code>es_cl</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="36">
			<Name>Spanish (Colombia)</Name>
			<DisplayCode>page.locale.spanish.co</DisplayCode>
			<Code>es_co</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="37">
			<Name>Spanish (Costa Rica)</Name>
			<DisplayCode>page.locale.spanish.cr</DisplayCode>
			<Code>es_cr</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="38">
			<Name>Spanish (Dominican Republic)</Name>
			<DisplayCode>page.locale.spanish.do</DisplayCode>
			<Code>es_do</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="39">
			<Name>Spanish (Ecuador)</Name>
			<DisplayCode>page.locale.spanish.ec</DisplayCode>
			<Code>es_ec</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="40">
			<Name>Spanish (El Salvador)</Name>
			<DisplayCode>page.locale.spanish.sv</DisplayCode>
			<Code>es_sv</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="41">
			<Name>Spanish (Guatemala)</Name>
			<DisplayCode>page.locale.spanish.gt</DisplayCode>
			<Code>es_gt</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="42">
			<Name>Spanish (Honduras)</Name>
			<DisplayCode>page.locale.spanish.hn</DisplayCode>
			<Code>es_hn</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="43">
			<Name>Spanish (Mexico)</Name>
			<DisplayCode>page.locale.spanish.mx</DisplayCode>
			<Code>es_mx</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="44">
			<Name>Spanish (Nicaragua)</Name>
			<DisplayCode>page.locale.spanish.ni</DisplayCode>
			<Code>es_ni</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="45">
			<Name>Spanish (Panama)</Name>
			<DisplayCode>page.locale.spanish.pa</DisplayCode>
			<Code>es_pa</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="46">
			<Name>Spanish (Paraguay)</Name>
			<DisplayCode>page.locale.spanish.py</DisplayCode>
			<Code>es_py</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="47">
			<Name>Spanish (Peru)</Name>
			<DisplayCode>page.locale.spanish.pe</DisplayCode>
			<Code>es_pe</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="48">
			<Name>Spanish (Puerto Rico)</Name>
			<DisplayCode>page.locale.spanish.pr</DisplayCode>
			<Code>es_pr</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="49">
			<Name>Spanish (US)</Name>
			<DisplayCode>page.locale.spanish.us</DisplayCode>
			<Code>es_us</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="50">
			<Name>Spanish (Uruguay)</Name>
			<DisplayCode>page.locale.spanish.uy</DisplayCode>
			<Code>es_uy</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25971</LanguageId>
			<LanguageName>Spanish</LanguageName>
			<LanguageDisplayCode>page.locale.spanish</LanguageDisplayCode>
			<LanguageCode>es</LanguageCode>
			<LanguageFavourite>true</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="51">
			<Name>Afrikaans (South Africa)</Name>
			<DisplayCode>page.locale.afrikaans.za</DisplayCode>
			<Code>af_za</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>24934</LanguageId>
			<LanguageName>Afrikaans</LanguageName>
			<LanguageDisplayCode>page.locale.afrikaans</LanguageDisplayCode>
			<LanguageCode>af</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="52">
			<Name>Albanian (Albania)</Name>
			<DisplayCode>page.locale.albanian.al</DisplayCode>
			<Code>sq_al</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29553</LanguageId>
			<LanguageName>Albanian</LanguageName>
			<LanguageDisplayCode>page.locale.albanian</LanguageDisplayCode>
			<LanguageCode>sq</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>0</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="53">
			<Name>Basque (Basque)</Name>
			<DisplayCode>page.locale.basque.eu</DisplayCode>
			<Code>eu_eu</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>25973</LanguageId>
			<LanguageName>Basque</LanguageName>
			<LanguageDisplayCode>page.locale.basque</LanguageDisplayCode>
			<LanguageCode>eu</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="54">
			<Name>Breton (France)</Name>
			<DisplayCode>page.locale.breton.fr</DisplayCode>
			<Code>br_fr</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>25202</LanguageId>
			<LanguageName>Breton</LanguageName>
			<LanguageDisplayCode>page.locale.breton</LanguageDisplayCode>
			<LanguageCode>br</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="55">
			<Name>Catalan (Catalan)</Name>
			<DisplayCode>page.locale.catalan.ad</DisplayCode>
			<Code>ca_ad</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>25441</LanguageId>
			<LanguageName>Catalan</LanguageName>
			<LanguageDisplayCode>page.locale.catalan</LanguageDisplayCode>
			<LanguageCode>ca</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="56">
			<Name>Danish (Denmark)</Name>
			<DisplayCode>page.locale.danish.dk</DisplayCode>
			<Code>da_dk</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>25697</LanguageId>
			<LanguageName>Danish</LanguageName>
			<LanguageDisplayCode>page.locale.danish</LanguageDisplayCode>
			<LanguageCode>da</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="57">
			<Name>Dutch (Belgium)</Name>
			<DisplayCode>page.locale.dutch.be</DisplayCode>
			<Code>nl_be</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>28268</LanguageId>
			<LanguageName>Dutch</LanguageName>
			<LanguageDisplayCode>page.locale.dutch</LanguageDisplayCode>
			<LanguageCode>nl</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="58">
			<Name>Dutch (Netherlands)</Name>
			<DisplayCode>page.locale.dutch.nl</DisplayCode>
			<Code>nl_nl</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>28268</LanguageId>
			<LanguageName>Dutch</LanguageName>
			<LanguageDisplayCode>page.locale.dutch</LanguageDisplayCode>
			<LanguageCode>nl</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="59">
			<Name>Estonian (Estonia)</Name>
			<DisplayCode>page.locale.estonian.ee</DisplayCode>
			<Code>et_ee</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>25972</LanguageId>
			<LanguageName>Estonian</LanguageName>
			<LanguageDisplayCode>page.locale.estonian</LanguageDisplayCode>
			<LanguageCode>et</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="60">
			<Name>Faroese (Faroe Islands)</Name>
			<DisplayCode>page.locale.faroese.fo</DisplayCode>
			<Code>fo_fo</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26223</LanguageId>
			<LanguageName>Faroese</LanguageName>
			<LanguageDisplayCode>page.locale.faroese</LanguageDisplayCode>
			<LanguageCode>fo</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="61">
			<Name>Finnish (Finland)</Name>
			<DisplayCode>page.locale.finnish.fi</DisplayCode>
			<Code>fi_fi</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26217</LanguageId>
			<LanguageName>Finnish</LanguageName>
			<LanguageDisplayCode>page.locale.finnish</LanguageDisplayCode>
			<LanguageCode>fi</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="62">
			<Name>Galician (Galician)</Name>
			<DisplayCode>page.locale.galician.gl</DisplayCode>
			<Code>gl_gl</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26476</LanguageId>
			<LanguageName>Galician</LanguageName>
			<LanguageDisplayCode>page.locale.galician</LanguageDisplayCode>
			<LanguageCode>gl</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="63">
			<Name>Icelandic (Iceland)</Name>
			<DisplayCode>page.locale.icelandic.is</DisplayCode>
			<Code>is_is</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26995</LanguageId>
			<LanguageName>Icelandic</LanguageName>
			<LanguageDisplayCode>page.locale.icelandic</LanguageDisplayCode>
			<LanguageCode>is</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="64">
			<Name>Irish (Ireland)</Name>
			<DisplayCode>page.locale.irish.ie</DisplayCode>
			<Code>ga_ie</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26465</LanguageId>
			<LanguageName>Irish</LanguageName>
			<LanguageDisplayCode>page.locale.irish</LanguageDisplayCode>
			<LanguageCode>ga</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="65">
			<Name>Luxembourgish (Luxembourg)</Name>
			<DisplayCode>page.locale.luxembourgish.lu</DisplayCode>
			<Code>lb_lu</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>27746</LanguageId>
			<LanguageName>Luxembourgish</LanguageName>
			<LanguageDisplayCode>page.locale.luxembourgish</LanguageDisplayCode>
			<LanguageCode>lb</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="66">
			<Name>Norwegian (Bokmal)</Name>
			<DisplayCode>page.locale.norwegian.nb</DisplayCode>
			<Code>no_nb</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>28271</LanguageId>
			<LanguageName>Norwegian</LanguageName>
			<LanguageDisplayCode>page.locale.norwegian</LanguageDisplayCode>
			<LanguageCode>no</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="67">
			<Name>Norwegian (Nynorsk)</Name>
			<DisplayCode>page.locale.norwegian.nn</DisplayCode>
			<Code>no_nn</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>28271</LanguageId>
			<LanguageName>Norwegian</LanguageName>
			<LanguageDisplayCode>page.locale.norwegian</LanguageDisplayCode>
			<LanguageCode>no</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="68">
			<Name>Portuguese (Brazil)</Name>
			<DisplayCode>page.locale.portuguese.br</DisplayCode>
			<Code>pt_br</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>28788</LanguageId>
			<LanguageName>Portuguese</LanguageName>
			<LanguageDisplayCode>page.locale.portuguese</LanguageDisplayCode>
			<LanguageCode>pt</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="69">
			<Name>Portuguese (Portugal)</Name>
			<DisplayCode>page.locale.portuguese.pt</DisplayCode>
			<Code>pt_pt</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>28788</LanguageId>
			<LanguageName>Portuguese</LanguageName>
			<LanguageDisplayCode>page.locale.portuguese</LanguageDisplayCode>
			<LanguageCode>pt</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="70">
			<Name>Scottish Gaelic (UK)</Name>
			<DisplayCode>page.locale.scottish.gaelic.bg</DisplayCode>
			<Code>gd_gb</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26468</LanguageId>
			<LanguageName>Scottish Gaelic</LanguageName>
			<LanguageDisplayCode>page.locale.scottish.gaelic</LanguageDisplayCode>
			<LanguageCode>gd</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="71">
			<Name>Swahili (Comoros)</Name>
			<DisplayCode>page.locale.swahili.km</DisplayCode>
			<Code>sw_km</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>29559</LanguageId>
			<LanguageName>Swahili</LanguageName>
			<LanguageDisplayCode>page.locale.swahili</LanguageDisplayCode>
			<LanguageCode>sw</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="72">
			<Name>Swahili (Kenya)</Name>
			<DisplayCode>page.locale.swahili.ke</DisplayCode>
			<Code>sw_ke</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29559</LanguageId>
			<LanguageName>Swahili</LanguageName>
			<LanguageDisplayCode>page.locale.swahili</LanguageDisplayCode>
			<LanguageCode>sw</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="73">
			<Name>Swahili (Tanzania)</Name>
			<DisplayCode>page.locale.swahili.tz</DisplayCode>
			<Code>sw_tz</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>29559</LanguageId>
			<LanguageName>Swahili</LanguageName>
			<LanguageDisplayCode>page.locale.swahili</LanguageDisplayCode>
			<LanguageCode>sw</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="74">
			<Name>Swahili (Uganda)</Name>
			<DisplayCode>page.locale.swahili.ug</DisplayCode>
			<Code>sw_ug</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>29559</LanguageId>
			<LanguageName>Swahili</LanguageName>
			<LanguageDisplayCode>page.locale.swahili</LanguageDisplayCode>
			<LanguageCode>sw</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="75">
			<Name>Swedish (Finland)</Name>
			<DisplayCode>page.locale.swedish.fi</DisplayCode>
			<Code>sv_fi</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>29558</LanguageId>
			<LanguageName>Swedish</LanguageName>
			<LanguageDisplayCode>page.locale.swedish</LanguageDisplayCode>
			<LanguageCode>sv</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="76">
			<Name>Swedish (Sweden)</Name>
			<DisplayCode>page.locale.swedish.se</DisplayCode>
			<Code>sv_se</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29558</LanguageId>
			<LanguageName>Swedish</LanguageName>
			<LanguageDisplayCode>page.locale.swedish</LanguageDisplayCode>
			<LanguageCode>sv</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="77">
			<Name>Walloon (Belgium)</Name>
			<DisplayCode>page.locale.wallon.be</DisplayCode>
			<Code>wa_be</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>30561</LanguageId>
			<LanguageName>Walloon</LanguageName>
			<LanguageDisplayCode>page.locale.wallon</LanguageDisplayCode>
			<LanguageCode>wa</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="78">
			<Name>Walloon (France)</Name>
			<DisplayCode>page.locale.wallon.fr</DisplayCode>
			<Code>wa_fr</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>30561</LanguageId>
			<LanguageName>Walloon</LanguageName>
			<LanguageDisplayCode>page.locale.wallon</LanguageDisplayCode>
			<LanguageCode>wa</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="79">
			<Name>Hungarian (Hungary)</Name>
			<DisplayCode>page.locale.hungarian.hu)</DisplayCode>
			<Code>hu_hu</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26741</LanguageId>
			<LanguageName>Hungarian</LanguageName>
			<LanguageDisplayCode>page.locale.hungarian</LanguageDisplayCode>
			<LanguageCode>hu</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>2</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="80">
			<Name>Polish (Poland)</Name>
			<DisplayCode>page.locale.polish.pl</DisplayCode>
			<Code>pl_pl</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>28780</LanguageId>
			<LanguageName>Polish</LanguageName>
			<LanguageDisplayCode>page.locale.polish</LanguageDisplayCode>
			<LanguageCode>pl</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>2</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="81">
			<Name>Bosnian (Bosnia - Latin)</Name>
			<DisplayCode>page.locale.bosnian.ba</DisplayCode>
			<Code>bs_ba</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>25203</LanguageId>
			<LanguageName>Bosnian</LanguageName>
			<LanguageDisplayCode>page.locale.bosnian</LanguageDisplayCode>
			<LanguageCode>bs</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>2</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="82">
			<Name>Croatian (Croatia)</Name>
			<DisplayCode>page.locale.croatian.hr</DisplayCode>
			<Code>hr_hr</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26738</LanguageId>
			<LanguageName>Croatian</LanguageName>
			<LanguageDisplayCode>page.locale.croatian</LanguageDisplayCode>
			<LanguageCode>hr</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>2</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="83">
			<Name>Czech (Czech Republic)</Name>
			<DisplayCode>page.locale.czech.cz</DisplayCode>
			<Code>cs_cz</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>25459</LanguageId>
			<LanguageName>Czech</LanguageName>
			<LanguageDisplayCode>page.locale.czech</LanguageDisplayCode>
			<LanguageCode>cs</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>2</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="84">
			<Name>Romanian (Romania)</Name>
			<DisplayCode>page.locale.romanian.ro</DisplayCode>
			<Code>ro_ro</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29295</LanguageId>
			<LanguageName>Romanian</LanguageName>
			<LanguageDisplayCode>page.locale.romanian</LanguageDisplayCode>
			<LanguageCode>ro</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>2</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="85">
			<Name>Serbian (Serbia - Latin)</Name>
			<DisplayCode>page.locale.serbian.sp</DisplayCode>
			<Code>sr_sp</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29554</LanguageId>
			<LanguageName>Serbian</LanguageName>
			<LanguageDisplayCode>page.locale.serbian</LanguageDisplayCode>
			<LanguageCode>sr</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>2</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="86">
			<Name>Serbian (Bosnia - Latin)</Name>
			<DisplayCode>page.locale.serbian.ba</DisplayCode>
			<Code>sr_ba</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>29554</LanguageId>
			<LanguageName>Serbian</LanguageName>
			<LanguageDisplayCode>page.locale.serbian</LanguageDisplayCode>
			<LanguageCode>sr</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>2</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="87">
			<Name>Serbian (Montenegro - Latin)</Name>
			<DisplayCode>page.locale.serbian.cs</DisplayCode>
			<Code>sr_cs</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>29554</LanguageId>
			<LanguageName>Serbian</LanguageName>
			<LanguageDisplayCode>page.locale.serbian</LanguageDisplayCode>
			<LanguageCode>sr</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>2</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="88">
			<Name>Slovak (Slovakia)</Name>
			<DisplayCode>page.locale.slovak.sk</DisplayCode>
			<Code>sk_sk</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29547</LanguageId>
			<LanguageName>Slovak</LanguageName>
			<LanguageDisplayCode>page.locale.slovak</LanguageDisplayCode>
			<LanguageCode>sk</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>2</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="89">
			<Name>Slovenian (Slovenia)</Name>
			<DisplayCode>page.locale.slovenian.si</DisplayCode>
			<Code>sl_si</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29548</LanguageId>
			<LanguageName>Slovenian</LanguageName>
			<LanguageDisplayCode>page.locale.slovenian</LanguageDisplayCode>
			<LanguageCode>sl</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>2</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="90">
			<Name>Chinese (China)</Name>
			<DisplayCode>page.locale.chinese.cn</DisplayCode>
			<Code>zh_cn</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>31336</LanguageId>
			<LanguageName>Chinese</LanguageName>
			<LanguageDisplayCode>page.locale.chinese</LanguageDisplayCode>
			<LanguageCode>zh</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="91">
			<Name>Chinese (Hong Kong S.A.R.)</Name>
			<DisplayCode>page.locale.chinese.hk</DisplayCode>
			<Code>zh_hk</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>31336</LanguageId>
			<LanguageName>Chinese</LanguageName>
			<LanguageDisplayCode>page.locale.chinese</LanguageDisplayCode>
			<LanguageCode>zh</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="92">
			<Name>Chinese (Macau S.A.R.)</Name>
			<DisplayCode>page.locale.chinese.mo</DisplayCode>
			<Code>zh_mo</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>31336</LanguageId>
			<LanguageName>Chinese</LanguageName>
			<LanguageDisplayCode>page.locale.chinese</LanguageDisplayCode>
			<LanguageCode>zh</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="93">
			<Name>Chinese (Singapore)</Name>
			<DisplayCode>page.locale.chinese.sg</DisplayCode>
			<Code>zh_sg</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>31336</LanguageId>
			<LanguageName>Chinese</LanguageName>
			<LanguageDisplayCode>page.locale.chinese</LanguageDisplayCode>
			<LanguageCode>zh</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="94">
			<Name>Chinese (Taiwan)</Name>
			<DisplayCode>page.locale.chinese.tw</DisplayCode>
			<Code>zh_tw</Code>
			<Favourite>false</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>31336</LanguageId>
			<LanguageName>Chinese</LanguageName>
			<LanguageDisplayCode>page.locale.chinese</LanguageDisplayCode>
			<LanguageCode>zh</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="95">
			<Name>Japanese (Japan)</Name>
			<DisplayCode>page.locale.japanese.jp</DisplayCode>
			<Code>ja_jp</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>27233</LanguageId>
			<LanguageName>Japanese</LanguageName>
			<LanguageDisplayCode>page.locale.japanese</LanguageDisplayCode>
			<LanguageCode>ja</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="96">
			<Name>Korean (Korea)</Name>
			<DisplayCode>page.locale.korean.kr</DisplayCode>
			<Code>ko_kr</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>27503</LanguageId>
			<LanguageName>Korean</LanguageName>
			<LanguageDisplayCode>page.locale.korean</LanguageDisplayCode>
			<LanguageCode>ko</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="97">
			<Name>Vietnamese (Vietnam)</Name>
			<DisplayCode>page.locale.vietnamese.vn</DisplayCode>
			<Code>vi_vn</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>30313</LanguageId>
			<LanguageName>Vietnamese</LanguageName>
			<LanguageDisplayCode>page.locale.vietnamese</LanguageDisplayCode>
			<LanguageCode>vi</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="98">
			<Name>Cantonese (Hong Kong S.A.R.)</Name>
			<DisplayCode>page.locale.cantonese.hk</DisplayCode>
			<Code>hk_hk</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26731</LanguageId>
			<LanguageName>Cantonese</LanguageName>
			<LanguageDisplayCode>page.locale.cantonese</LanguageDisplayCode>
			<LanguageCode>hk</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
	
		<MessagepointLocale id="99">
			<Name>Cantonese (Macau S.A.R.)</Name>
			<DisplayCode>page.locale.cantonese.mo</DisplayCode>
			<Code>hk_mo</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26731</LanguageId>
			<LanguageName>Cantonese</LanguageName>
			<LanguageDisplayCode>page.locale.cantonese</LanguageDisplayCode>
			<LanguageCode>hk</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
		
		<MessagepointLocale id="100">
			<Name>Ilokano (Philippines)</Name>
			<DisplayCode>page.locale.ilokano.ph</DisplayCode>
			<Code>il_ph</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26988</LanguageId>
			<LanguageName>Ilokano</LanguageName>
			<LanguageDisplayCode>page.locale.ilokano</LanguageDisplayCode>
			<LanguageCode>il</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="101">
			<Name>Hebrew (Israel)</Name>
			<DisplayCode>page.locale.hebrew.il</DisplayCode>
			<Code>he_il</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26725</LanguageId>
			<LanguageName>Hebrew</LanguageName>
			<LanguageDisplayCode>page.locale.hebrew</LanguageDisplayCode>
			<LanguageCode>he</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="102">
			<Name>Russian (Russia)</Name>
			<DisplayCode>page.locale.russian.ru</DisplayCode>
			<Code>ru_ru</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29301</LanguageId>
			<LanguageName>Russian</LanguageName>
			<LanguageDisplayCode>page.locale.russian</LanguageDisplayCode>
			<LanguageCode>ru</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="103">
			<Name>Thai (Thailand)</Name>
			<DisplayCode>page.locale.thai.th</DisplayCode>
			<Code>th_th</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29800</LanguageId>
			<LanguageName>Thai</LanguageName>
			<LanguageDisplayCode>page.locale.thai</LanguageDisplayCode>
			<LanguageCode>th</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="104">
			<Name>Tamil (India)</Name>
			<DisplayCode>page.locale.tamil.in</DisplayCode>
			<Code>ta_in</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29793</LanguageId>
			<LanguageName>Tamil</LanguageName>
			<LanguageDisplayCode>page.locale.tamil</LanguageDisplayCode>
			<LanguageCode>ta</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="105">
			<Name>Hindi (India)</Name>
			<DisplayCode>page.locale.hindi.in</DisplayCode>
			<Code>hi_in</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26729</LanguageId>
			<LanguageName>Hindi</LanguageName>
			<LanguageDisplayCode>page.locale.hindi</LanguageDisplayCode>
			<LanguageCode>hi</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="106">
			<Name>Punjabi (India)</Name>
			<DisplayCode>page.locale.punjabi.in</DisplayCode>
			<Code>pa_in</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>28769</LanguageId>
			<LanguageName>Punjabi</LanguageName>
			<LanguageDisplayCode>page.locale.punjabi</LanguageDisplayCode>
			<LanguageCode>pa</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="107">
			<Name>Ukrainian (Ukraine)</Name>
			<DisplayCode>page.locale.ukrainian.ua</DisplayCode>
			<Code>uk_ua</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>30059</LanguageId>
			<LanguageName>Ukrainian</LanguageName>
			<LanguageDisplayCode>page.locale.ukrainian</LanguageDisplayCode>
			<LanguageCode>uk</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="108">
			<Name>Indonesian (Indonesia)</Name>
			<DisplayCode>page.locale.indonesian.id</DisplayCode>
			<Code>id_id</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26980</LanguageId>
			<LanguageName>Indonesian</LanguageName>
			<LanguageDisplayCode>page.locale.indonesian</LanguageDisplayCode>
			<LanguageCode>id</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="109">
			<Name>Malay (Malaysia)</Name>
			<DisplayCode>page.locale.malay.my</DisplayCode>
			<Code>ms_my</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>28019</LanguageId>
			<LanguageName>Malay</LanguageName>
			<LanguageDisplayCode>page.locale.malay</LanguageDisplayCode>
			<LanguageCode>ms</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="110">
			<Name>Malay (Brunei)</Name>
			<DisplayCode>page.locale.malay.bn</DisplayCode>
			<Code>ms_bn</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>28019</LanguageId>
			<LanguageName>Malay</LanguageName>
			<LanguageDisplayCode>page.locale.malay</LanguageDisplayCode>
			<LanguageCode>ms</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="111">
			<Name>Arabic (Saudi Arabia)</Name>
			<DisplayCode>page.locale.arabic.sa</DisplayCode>
			<Code>ar_sa</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="112">
			<Name>Arabic (United Arab Emirates)</Name>
			<DisplayCode>page.locale.arabic.ae</DisplayCode>
			<Code>ar_ae</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="113">
			<Name>Arabic (Algeria)</Name>
			<DisplayCode>page.locale.arabic.dz</DisplayCode>
			<Code>ar_dz</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="114">
			<Name>Arabic (Bahrain)</Name>
			<DisplayCode>page.locale.arabic.bh</DisplayCode>
			<Code>ar_bh</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="115">
			<Name>Arabic (Egypt)</Name>
			<DisplayCode>page.locale.arabic.eg</DisplayCode>
			<Code>ar_eg</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="116">
			<Name>Arabic (Iraq)</Name>
			<DisplayCode>page.locale.arabic.iq</DisplayCode>
			<Code>ar_iq</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="117">
			<Name>Arabic (Jordan)</Name>
			<DisplayCode>page.locale.arabic.jo</DisplayCode>
			<Code>ar_jo</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="118">
			<Name>Arabic (Kuwait)</Name>
			<DisplayCode>page.locale.arabic.kw</DisplayCode>
			<Code>ar_kw</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="119">
			<Name>Arabic (Lebanon)</Name>
			<DisplayCode>page.locale.arabic.lb</DisplayCode>
			<Code>ar_lb</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="120">
			<Name>Arabic (Libya)</Name>
			<DisplayCode>page.locale.arabic.ly</DisplayCode>
			<Code>ar_ly</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="121">
			<Name>Arabic (Morocco)</Name>
			<DisplayCode>page.locale.arabic.ma</DisplayCode>
			<Code>ar_ma</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="122">
			<Name>Arabic (Oman)</Name>
			<DisplayCode>page.locale.arabic.om</DisplayCode>
			<Code>ar_om</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="123">
			<Name>Arabic (Qatar)</Name>
			<DisplayCode>page.locale.arabic.qa</DisplayCode>
			<Code>ar_qa</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="124">
			<Name>Arabic (Syria)</Name>
			<DisplayCode>page.locale.arabic.sy</DisplayCode>
			<Code>ar_sy</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="125">
			<Name>Arabic (Tunisia)</Name>
			<DisplayCode>page.locale.arabic.tn</DisplayCode>
			<Code>ar_tn</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>
			
		<MessagepointLocale id="126">
			<Name>Arabic (Yemen)</Name>
			<DisplayCode>page.locale.arabic.ye</DisplayCode>
			<Code>ar_ye</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>24946</LanguageId>
			<LanguageName>Arabic</LanguageName>
			<LanguageDisplayCode>page.locale.arabic</LanguageDisplayCode>
			<LanguageCode>ar</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="127">
			<Name>Armenian (Armenia)</Name>
			<DisplayCode>page.locale.armenian.am</DisplayCode>
			<Code>hy_am</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26745</LanguageId>
			<LanguageName>Armenian</LanguageName>
			<LanguageDisplayCode>page.locale.armenian</LanguageDisplayCode>
			<LanguageCode>hy</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="128">
			<Name>Tagalog (Philippines)</Name>
			<DisplayCode>page.locale.tagalog.ph</DisplayCode>
			<Code>tl_ph</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29804</LanguageId>
			<LanguageName>Tagalog</LanguageName>
			<LanguageDisplayCode>page.locale.tagalog</LanguageDisplayCode>
			<LanguageCode>tl</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>1</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="129">
			<Name>Khmer (Cambodia)</Name>
			<DisplayCode>page.locale.khmer.kh</DisplayCode>
			<Code>km_kh</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>27501</LanguageId>
			<LanguageName>Khmer</LanguageName>
			<LanguageDisplayCode>page.locale.khmer</LanguageDisplayCode>
			<LanguageCode>km</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="130">
			<Name>Hmong (China)</Name>
			<DisplayCode>page.locale.hmong.cn</DisplayCode>
			<Code>hm_cn</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26733</LanguageId>
			<LanguageName>Hmong</LanguageName>
			<LanguageDisplayCode>page.locale.hmong</LanguageDisplayCode>
			<LanguageCode>hm</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="131">
			<Name>Hmong (Laos)</Name>
			<DisplayCode>page.locale.hmong.la</DisplayCode>
			<Code>hm_la</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26733</LanguageId>
			<LanguageName>Hmong</LanguageName>
			<LanguageDisplayCode>page.locale.hmong</LanguageDisplayCode>
			<LanguageCode>hm</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="132">
			<Name>Hmong (Myanmar)</Name>
			<DisplayCode>page.locale.hmong.mm</DisplayCode>
			<Code>hm_mm</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26733</LanguageId>
			<LanguageName>Hmong</LanguageName>
			<LanguageDisplayCode>page.locale.hmong</LanguageDisplayCode>
			<LanguageCode>hm</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="133">
			<Name>Hmong (Vietnam)</Name>
			<DisplayCode>page.locale.hmong.vn</DisplayCode>
			<Code>hm_vn</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26733</LanguageId>
			<LanguageName>Hmong</LanguageName>
			<LanguageDisplayCode>page.locale.hmong</LanguageDisplayCode>
			<LanguageCode>hm</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="134">
			<Name>Hmong (Thailand)</Name>
			<DisplayCode>page.locale.hmong.th</DisplayCode>
			<Code>hm_th</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26733</LanguageId>
			<LanguageName>Hmong</LanguageName>
			<LanguageDisplayCode>page.locale.hmong</LanguageDisplayCode>
			<LanguageCode>hm</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="135">
			<Name>Lao (Laos)</Name>
			<DisplayCode>page.locale.lao.la</DisplayCode>
			<Code>lo_la</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>27759</LanguageId>
			<LanguageName>Lao</LanguageName>
			<LanguageDisplayCode>page.locale.lao</LanguageDisplayCode>
			<LanguageCode>lo</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="136">
			<Name>Lao (Thailand)</Name>
			<DisplayCode>page.locale.lao.th</DisplayCode>
			<Code>lo_th</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>27759</LanguageId>
			<LanguageName>Lao</LanguageName>
			<LanguageDisplayCode>page.locale.lao</LanguageDisplayCode>
			<LanguageCode>lo</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="137">
			<Name>Lao (Cambodia)</Name>
			<DisplayCode>page.locale.lao.kh</DisplayCode>
			<Code>lo_kh</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>27759</LanguageId>
			<LanguageName>Lao</LanguageName>
			<LanguageDisplayCode>page.locale.lao</LanguageDisplayCode>
			<LanguageCode>lo</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="138">
			<Name>Lao (Vietnam)</Name>
			<DisplayCode>page.locale.lao.vn</DisplayCode>
			<Code>lo_vn</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>27759</LanguageId>
			<LanguageName>Lao</LanguageName>
			<LanguageDisplayCode>page.locale.lao</LanguageDisplayCode>
			<LanguageCode>lo</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="139">
			<Name>Lao (China)</Name>
			<DisplayCode>page.locale.lao.cn</DisplayCode>
			<Code>lo_cn</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>27759</LanguageId>
			<LanguageName>Lao</LanguageName>
			<LanguageDisplayCode>page.locale.lao</LanguageDisplayCode>
			<LanguageCode>lo</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="140">
			<Name>Greek (Greece)</Name>
			<DisplayCode>page.locale.greek.gr</DisplayCode>
			<Code>el_gr</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>25964</LanguageId>
			<LanguageName>Greek</LanguageName>
			<LanguageDisplayCode>page.locale.greek</LanguageDisplayCode>
			<LanguageCode>el</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="141">
			<Name>Greek (Cyprus)</Name>
			<DisplayCode>page.locale.greek.cy</DisplayCode>
			<Code>el_cy</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25964</LanguageId>
			<LanguageName>Greek</LanguageName>
			<LanguageDisplayCode>page.locale.greek</LanguageDisplayCode>
			<LanguageCode>el</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="142">
			<Name>Hawaiian (US)</Name>
			<DisplayCode>page.locale.hawaiian.us</DisplayCode>
			<Code>hw_us</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26743</LanguageId>
			<LanguageName>Hawaiian</LanguageName>
			<LanguageDisplayCode>page.locale.hawaiian</LanguageDisplayCode>
			<LanguageCode>hw</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="143">
			<Name>Samoan (Samoa)</Name>
			<DisplayCode>page.locale.samoan.ws</DisplayCode>
			<Code>sm_ws</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29549</LanguageId>
			<LanguageName>Samoan</LanguageName>
			<LanguageDisplayCode>page.locale.samoan</LanguageDisplayCode>
			<LanguageCode>sm</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="144">
			<Name>Samoan (American Samoa)</Name>
			<DisplayCode>page.locale.samoan.as</DisplayCode>
			<Code>sm_as</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>29549</LanguageId>
			<LanguageName>Samoan</LanguageName>
			<LanguageDisplayCode>page.locale.samoan</LanguageDisplayCode>
			<LanguageCode>sm</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="145">
			<Name>Yiddish (Israel)</Name>
			<DisplayCode>page.locale.yiddish.il</DisplayCode>
			<Code>yi_il</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>31081</LanguageId>
			<LanguageName>Yiddish</LanguageName>
			<LanguageDisplayCode>page.locale.yiddish</LanguageDisplayCode>
			<LanguageCode>yi</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="146">
			<Name>Persian (Iran)</Name>
			<DisplayCode>page.locale.persian.ir</DisplayCode>
			<Code>fa_ir</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26209</LanguageId>
			<LanguageName>Persian</LanguageName>
			<LanguageDisplayCode>page.locale.persian</LanguageDisplayCode>
			<LanguageCode>fa</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="147">
			<Name>Persian (Afghanistan)</Name>
			<DisplayCode>page.locale.persian.af</DisplayCode>
			<Code>fa_af</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26209</LanguageId>
			<LanguageName>Persian</LanguageName>
			<LanguageDisplayCode>page.locale.persian</LanguageDisplayCode>
			<LanguageCode>fa</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="148">
			<Name>Persian (Tajikistan)</Name>
			<DisplayCode>page.locale.persian.tj</DisplayCode>
			<Code>fa_tj</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>26209</LanguageId>
			<LanguageName>Persian</LanguageName>
			<LanguageDisplayCode>page.locale.persian</LanguageDisplayCode>
			<LanguageCode>fa</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="149">
			<Name>Haitian Creole (Haiti)</Name>
			<DisplayCode>page.locale.haitian.ht</DisplayCode>
			<Code>ht_ht</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>26740</LanguageId>
			<LanguageName>Haitian Creole</LanguageName>
			<LanguageDisplayCode>page.locale.haitian</LanguageDisplayCode>
			<LanguageCode>ht</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="150">
			<Name>Urdu (Pakistan)</Name>
			<DisplayCode>page.locale.urdu.pk</DisplayCode>
			<Code>ur_pk</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>30066</LanguageId>
			<LanguageName>Urdu</LanguageName>
			<LanguageDisplayCode>page.locale.urdu</LanguageDisplayCode>
			<LanguageCode>ur</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="151">
			<Name>Urdu (India)</Name>
			<DisplayCode>page.locale.urdu.in</DisplayCode>
			<Code>ur_in</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>30066</LanguageId>
			<LanguageName>Urdu</LanguageName>
			<LanguageDisplayCode>page.locale.urdu</LanguageDisplayCode>
			<LanguageCode>ur</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="152">
			<Name>Bangla/Bengali (Bangladesh)</Name>
			<DisplayCode>page.locale.bangla.bd</DisplayCode>
			<Code>bn_bd</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>25198</LanguageId>
			<LanguageName>Bangla/Bengali</LanguageName>
			<LanguageDisplayCode>page.locale.bangla</LanguageDisplayCode>
			<LanguageCode>bn</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="153">
			<Name>Bangla/Bengali (India)</Name>
			<DisplayCode>page.locale.bangla.in</DisplayCode>
			<Code>bn_in</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>25198</LanguageId>
			<LanguageName>Bangla/Bengali</LanguageName>
			<LanguageDisplayCode>page.locale.bangla</LanguageDisplayCode>
			<LanguageCode>bn</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="154">
			<Name>Somali (Somalia)</Name>
			<DisplayCode>page.locale.somali.so</DisplayCode>
			<Code>so_so</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>29551</LanguageId>
			<LanguageName>Somali</LanguageName>
			<LanguageDisplayCode>page.locale.somali</LanguageDisplayCode>
			<LanguageCode>so</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="155">
			<Name>Somali (Djibouti)</Name>
			<DisplayCode>page.locale.somali.dj</DisplayCode>
			<Code>so_dj</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>29551</LanguageId>
			<LanguageName>Somali</LanguageName>
			<LanguageDisplayCode>page.locale.somali</LanguageDisplayCode>
			<LanguageCode>so</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="156">
			<Name>Somali (Ethiopia)</Name>
			<DisplayCode>page.locale.somali.et</DisplayCode>
			<Code>so_et</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>29551</LanguageId>
			<LanguageName>Somali</LanguageName>
			<LanguageDisplayCode>page.locale.somali</LanguageDisplayCode>
			<LanguageCode>so</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="157">
			<Name>Somali (Kenya)</Name>
			<DisplayCode>page.locale.somali.ke</DisplayCode>
			<Code>so_ke</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>29551</LanguageId>
			<LanguageName>Somali</LanguageName>
			<LanguageDisplayCode>page.locale.somali</LanguageDisplayCode>
			<LanguageCode>so</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="158">
			<Name>Burmese (Myanmar)</Name>
			<DisplayCode>page.locale.burmese.mm</DisplayCode>
			<Code>my_mm</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>28025</LanguageId>
			<LanguageName>Burmese</LanguageName>
			<LanguageDisplayCode>page.locale.burmese</LanguageDisplayCode>
			<LanguageCode>my</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="159">
			<Name>Nepali (Nepal)</Name>
			<DisplayCode>page.locale.nepali.np</DisplayCode>
			<Code>ne_np</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>true</DefaultLocale>
			<LanguageId>28261</LanguageId>
			<LanguageName>Nepali</LanguageName>
			<LanguageDisplayCode>page.locale.nepali</LanguageDisplayCode>
			<LanguageCode>ne</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

		<MessagepointLocale id="160">
			<Name>Nepali (India)</Name>
			<DisplayCode>page.locale.nepali.dj</DisplayCode>
			<Code>ne_in</Code>
			<Favourite>true</Favourite>
			<DefaultLocale>false</DefaultLocale>
			<LanguageId>28261</LanguageId>
			<LanguageName>Nepali</LanguageName>
			<LanguageDisplayCode>page.locale.nepali</LanguageDisplayCode>
			<LanguageCode>ne</LanguageCode>
			<LanguageFavourite>false</LanguageFavourite>
			<LanguageGroup>3</LanguageGroup>
		</MessagepointLocale>

	</MessagepointLocales>
	
	<ApplicationLocales>
		<ApplicationLocale id="1">
			<Code>en_us</Code>
			<Enable>true</Enable>
			<Accessible>true</Accessible>
		</ApplicationLocale>
		<ApplicationLocale id="2">
			<Code>fr_ca</Code>
			<Enable>true</Enable>
			<Accessible>true</Accessible>
		</ApplicationLocale>
		<ApplicationLocale id="3">
			<Code>de_de</Code>
			<Enable>true</Enable>
			<Accessible>false</Accessible>
		</ApplicationLocale>
		<ApplicationLocale id="4">
			<Code>es_es</Code>
			<Enable>true</Enable>
			<Accessible>false</Accessible>
		</ApplicationLocale>
		<ApplicationLocale id="5">
			<Code>pt_pt</Code>
			<Enable>true</Enable>
			<Accessible>false</Accessible>
		</ApplicationLocale>
		<ApplicationLocale id="6">
			<Code>zh_cn</Code>
			<Enable>true</Enable>
			<Accessible>false</Accessible>
		</ApplicationLocale>
		<ApplicationLocale id="7">
			<Code>ja_jp</Code>
			<Enable>true</Enable>
			<Accessible>false</Accessible>
		</ApplicationLocale>		
	</ApplicationLocales>

	<TranslationProviders>
		<TranslationProvider id="1">
			<Name>CQFluency</Name>
			<Code>CQF</Code>
			<NameCode>page.label.translation.provider.cqfluency</NameCode>
		</TranslationProvider>
		<TranslationProvider id="2">
			<Name>Trados</Name>
			<Code>TRADOS</Code>
			<NameCode>page.label.translation.provider.trados</NameCode>
		</TranslationProvider>
		<TranslationProvider id="3">
			<Name>TransPerfect</Name>
			<Code>TRPERF</Code>
			<NameCode>page.label.translation.provider.transperfect</NameCode>
		</TranslationProvider>
		<TranslationProvider id="4">
			<Name>Welocalize</Name>
			<Code>WELOCALIZE</Code>
			<NameCode>page.label.translation.provider.marcie</NameCode>
		</TranslationProvider>
	</TranslationProviders>

	<Model class="com.prinova.messagepoint.model.tenant.Tenant">
 		<Property name="id" type="long">1</Property> 
		<Property name="enabled" type="boolean">true</Property>
		<Property name="name" type="java.lang.String" >Provider</Property>
		<Property name="code" type="java.lang.String" >Provider</Property>
		<Property name="permissions" type="java.util.Set" >
			<Query hql="from com.prinova.messagepoint.model.security.Permission p where p.type != com.prinova.messagepoint.model.security.Permission.TYPE_SYSTEM_VALUE_STRING and p.id not in (com.prinova.messagepoint.model.security.Permission.ROLE_TENANT_INSERT_SCHEDULE_VIEW, com.prinova.messagepoint.model.security.Permission.ROLE_TENANT_INSERT_SCHEDULE_EDIT)" />
		</Property>
	</Model>

	<!--   <Model class="com.prinova.messagepoint.model.admin.SystemNotification">
 		<Property name="id" type="long">1</Property> 
 		<property name="guid" type="java.lang.String" >1154b75f44e929bd2c49499a539e90143c57cf21</property>
 		<Property name="name" type="java.lang.String" >Default Maintenance Settings</Property>
		<Property name="active" type="boolean">0</Property>
		<Property name="numberOfPrompts" type="int">30</Property>
	</Model>
	-->
	<Model class="com.prinova.messagepoint.model.Branch">
 		<Property name="id" type="long">1</Property> 
 		<property name="guid" type="java.lang.String" >2154b75f44e929bd2c49499a539e90142c57cf21</property>
 		<Property name="branchCode" type="java.lang.String" >PRNV</Property>
 		<Property name="name" type="java.lang.String" >prinova</Property>
 		<Property name="friendlyName" type="java.lang.String" >POD Controller</Property>
		<Property name="branchTypeId" type="int">0</Property>
		<Property name="dcsSchemaName" type="java.lang.String" ></Property>
		<Property name="enabled" type="boolean">true</Property>
		<Property name="ssoType" type="int">0</Property>
		<Property name="status" type="int">3</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.Node">
 		<Property name="id" type="long">1</Property> 
		<Property name="nodeType" type="int">-1</Property>
 		<Property name="name" type="java.lang.String" >pod-controller</Property>
 		<Property name="friendlyName" type="java.lang.String" >POD Master</Property>
 		<Property name="schemaName" type="java.lang.String" ></Property>
 		<Property name="branch" type="com.prinova.messagepoint.model.Branch">
			<Query first="true" hql="from com.prinova.messagepoint.model.Branch b where b.parentBranch is null" />
 		</Property> 
		<Property name="nodeCountNumber" type="int">1</Property>
		<Property name="isDefaultNode" type="boolean">true</Property>
		<Property name="enabled" type="boolean">true</Property>
		<Property name="status" type="int">3</Property>
	</Model>

	<Model class="com.prinova.messagepoint.model.version.VersionStatus">
 		<Property name="id" type="long">1</Property> 
		<Property name="name" type="java.lang.String" >New</Property>
		<Property name="stringCode" type="java.lang.String">page.label.status.new</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.version.VersionStatus">
 		<Property name="id" type="long">2</Property> 
		<Property name="name" type="java.lang.String" >Production</Property>
		<Property name="stringCode" type="java.lang.String">page.label.status.production</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.version.VersionStatus">
 		<Property name="id" type="long">3</Property> 
		<Property name="name" type="java.lang.String" >Removed</Property>
		<Property name="stringCode" type="java.lang.String">page.label.status.removed</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.version.VersionStatus">
 		<Property name="id" type="long">4</Property> 
		<Property name="name" type="java.lang.String" >Archived</Property>
		<Property name="stringCode" type="java.lang.String">page.label.status.archived</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.version.VersionStatus">
 		<Property name="id" type="long">5</Property> 
		<Property name="name" type="java.lang.String" >WIP</Property>
		<Property name="stringCode" type="java.lang.String">page.label.status.wip</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.version.VersionStatus">
 		<Property name="id" type="long">6</Property> 
		<Property name="name" type="java.lang.String" >Awaiting Approval</Property>
		<Property name="stringCode" type="java.lang.String">page.label.status.waiting.approval</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.version.VersionStatus">
 		<Property name="id" type="long">7</Property> 
		<Property name="name" type="java.lang.String" >Inactive</Property>
		<Property name="stringCode" type="java.lang.String">page.label.status.inactive</Property>
	</Model>					
	<Model class="com.prinova.messagepoint.model.version.VersionStatus">
 		<Property name="id" type="long">8</Property> 
		<Property name="name" type="java.lang.String" >Setup</Property>
		<Property name="stringCode" type="java.lang.String">page.label.status.setup</Property>
	</Model>					
	<Model class="com.prinova.messagepoint.model.version.VersionActivityReason">
 		<Property name="id" type="long">1</Property> 
		<Property name="name" type="java.lang.String" >New</Property>
		<Property name="stringCode" type="java.lang.String">page.label.version.activity.new</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.version.VersionActivityReason">
 		<Property name="id" type="long">2</Property> 
		<Property name="name" type="java.lang.String" >Checkout</Property>
		<Property name="stringCode" type="java.lang.String">page.label.version.activity.checkout</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.version.VersionActivityReason">
 		<Property name="id" type="long">3</Property> 
		<Property name="name" type="java.lang.String" >Checkout</Property>
		<Property name="stringCode" type="java.lang.String">page.label.version.activity.clone</Property>
	</Model>	
	<Model class="com.prinova.messagepoint.model.version.VersionActivityReason">
 		<Property name="id" type="long">11</Property> 
		<Property name="name" type="java.lang.String" >New Production Version Checked in</Property>
		<Property name="stringCode" type="java.lang.String">page.label.version.activity.newversioncheckedin</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.version.VersionActivityReason">
 		<Property name="id" type="long">12</Property> 
		<Property name="name" type="java.lang.String" >New</Property>
		<Property name="stringCode" type="java.lang.String">page.label.version.activity.archived</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.version.VersionActivityReason">
 		<Property name="id" type="long">13</Property> 
		<Property name="name" type="java.lang.String" >Aborted</Property>
		<Property name="stringCode" type="java.lang.String">page.label.version.activity.aborted</Property>
	</Model>

	<Model class="com.prinova.messagepoint.model.workgroup.Workgroup">
 		<Property name="id" type="long">1</Property> 
		<Property name="name" type="java.lang.String" >All</Property>
		<Property name="description" type="java.lang.String" >This is the default workgroup.</Property>
		<Property name="defaultWorkgroup" type="boolean" >true</Property>
		<Property name="zones" type="java.util.Set" >
			<Query hql="from com.prinova.messagepoint.model.Zone z" />
		</Property>
	</Model>
	
	<Model class="com.prinova.messagepoint.model.admin.SecuritySettings">
		<Property name="id" type="long">1</Property>
		<Property name="name" type="java.lang.String"></Property>
		<Property name="requiresUppercase" type="boolean">true</Property>
		<Property name="requiresLowercase" type="boolean">false</Property>
		<Property name="requiresNumeral" type="boolean">true</Property>
		<Property name="requiresSymbol" type="boolean">false</Property>
		<Property name="norepeats" type="boolean">false</Property>
		<Property name="minLength" type="java.lang.String">6</Property>
		<Property name="maxLength" type="java.lang.String">32</Property>
		<Property name="maxAttempts" type="java.lang.String">3</Property>
		<Property name="trackFlag" type="boolean">false</Property>
		<Property name="alphanumericOnly" type="boolean">false</Property>
		<Property name="usernameMinLength" type="java.lang.String">8</Property>
		<Property name="usernameMaxLength" type="java.lang.String">30</Property>
		<Property name="pwResetKeepAlive" type="java.lang.String">120</Property>
		<Property name="pwExpires" type="boolean">false</Property>
		<Property name="pwExpireDays" type="int">30</Property>
		<Property name="preventRepeatedPw" type="boolean">false</Property>
		<Property name="pwHistoryEntries" type="int">3</Property>
		<Property name="pwLimitReusePeriod" type="boolean">false</Property>
		<Property name="pwLimitMonths" type="int">12</Property>
		<Property name="sessionExpireMins" type="java.lang.String">30</Property>
	</Model>

	<Model class="com.prinova.messagepoint.model.folder.LastVisitedFolder">
 		<Property name="id" type="long">2</Property> 
		<Property name="name" type="java.lang.String" >static.type.last_visited_folder</Property>
		<Property name="hierarchyDepth" type="int">1</Property>
		<Property name="systemManaged" type="boolean">true</Property>
		<Property name="guid" type="java.lang.String" >2</Property>
		<Property name="owner" type="com.prinova.messagepoint.model.security.User" >
			<Query hql="from com.prinova.messagepoint.model.security.User u where u.id=1" first="true" />			
		</Property>		
	</Model>

	<Model class="com.prinova.messagepoint.model.folder.FavoritesFolder">
 		<Property name="id" type="long">3</Property> 
		<Property name="name" type="java.lang.String" >static.type.favorites_folder</Property>
		<Property name="hierarchyDepth" type="int">1</Property>
		<Property name="systemManaged" type="boolean">true</Property>
		<Property name="guid" type="java.lang.String" >3</Property>
		<Property name="owner" type="com.prinova.messagepoint.model.security.User" >
			<Query hql="from com.prinova.messagepoint.model.security.User where id=1" first="true" />			
		</Property>		
	</Model>

	<!-- Channels -->
	<Model class="com.prinova.messagepoint.model.admin.Channel">
 		<Property name="id" type="long">1</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.channel.generic</Property>
		<Property name="name" type="java.lang.String" >Generic</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Channel">
 		<Property name="id" type="long">2</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.channel.composition</Property>
		<Property name="name" type="java.lang.String" >Print</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Channel">
 		<Property name="id" type="long">3</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.channel.web</Property>
		<Property name="name" type="java.lang.String" >Web</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Channel">
 		<Property name="id" type="long">4</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.channel.email</Property>
		<Property name="name" type="java.lang.String" >Email</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Channel">
 		<Property name="id" type="long">5</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.channel.sms</Property>
		<Property name="name" type="java.lang.String" >SMS</Property>
	</Model>
	
	<!-- Connectors for Channel: Generic -->
	<Model class="com.prinova.messagepoint.model.admin.Connector">
 		<Property name="id" type="long">1</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.connector.flat_files</Property>
		<Property name="name" type="java.lang.String" >Flat Files</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=1" first="true" />
		</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Connector">
 		<Property name="id" type="long">2</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.connector.xml</Property>
		<Property name="name" type="java.lang.String" >XML</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=1" first="true" />
		</Property>
	</Model>
	
	<!-- Connectors for Channel: Composition -->
	<Model class="com.prinova.messagepoint.model.admin.Connector">
 		<Property name="id" type="long">3</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.connector.hp_exstream</Property>
		<Property name="name" type="java.lang.String" >Dialogue</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=2" first="true" />
		</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Connector">
 		<Property name="id" type="long">9</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.connector.gmc</Property>
		<Property name="name" type="java.lang.String" >GMC</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=2" first="true" />
		</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Connector">
 		<Property name="id" type="long">16</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.connector.native</Property>
		<Property name="name" type="java.lang.String" >Native</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=2" first="true" />	
		</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Connector">
		<Property name="id" type="long">18</Property>
		<Property name="presentationName" type="java.lang.String" >static.type.connector.sefas</Property>
		<Property name="name" type="java.lang.String" >Sefas</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=2" first="true" />
		</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Connector">
		<Property name="id" type="long">19</Property>
		<Property name="presentationName" type="java.lang.String" >static.type.connector.mphcs</Property>
		<Property name="name" type="java.lang.String" >Messagepoint Composer</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=2" first="true" />
		</Property>
	</Model>

	<!-- Connectors for Channel: Web -->	
	<Model class="com.prinova.messagepoint.model.admin.Connector">
 		<Property name="id" type="long">15</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.connector.ftp</Property>
		<Property name="name" type="java.lang.String" >FTP</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=3" first="true" />	
		</Property>
	</Model>
	
	 <!-- Connectors for Channel: Email -->
	<Model class="com.prinova.messagepoint.model.admin.Connector">
 		<Property name="id" type="long">10</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.connector.e_messaging</Property>
		<Property name="name" type="java.lang.String" >e-Messaging</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=4" first="true" />	
		</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Connector">
 		<Property name="id" type="long">12</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.connector.sendmail</Property>
		<Property name="name" type="java.lang.String" >Sendmail</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=4" first="true" />	
		</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Connector">
 		<Property name="id" type="long">13</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.connector.exacttarget</Property>
		<Property name="name" type="java.lang.String" >ExactTarget</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=4" first="true" />	
		</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Connector">
 		<Property name="id" type="long">17</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.connector.sfmc_journeybuilder</Property>
		<Property name="name" type="java.lang.String" >SFMC Journey Builder</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=4" first="true" />	
		</Property>
	</Model>

	 <!-- Connectors for Channel: SMS -->
	<Model class="com.prinova.messagepoint.model.admin.Connector">
 		<Property name="id" type="long">11</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.connector.e_messaging</Property>
		<Property name="name" type="java.lang.String" >e-Messaging</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=5" first="true" />	
		</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.Connector">
 		<Property name="id" type="long">14</Property>
 		<Property name="presentationName" type="java.lang.String" >static.type.connector.clickatell</Property>
		<Property name="name" type="java.lang.String" >Clickatell</Property>
		<Property name="channel" type="com.prinova.messagepoint.model.admin.Channel" >
			<Query hql="from com.prinova.messagepoint.model.admin.Channel where id=5" first="true" />	
		</Property>
	</Model>
	
	<!-- Qulification Output for all Channel/Connector -->
	<Model class="com.prinova.messagepoint.model.admin.QualificationOutput">
		<Property name="id" type="long">1</Property>
		<Property name="name" type="java.lang.String">static.type.qualification.output.flat_files</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.QualificationOutput">
		<Property name="id" type="long">2</Property>
		<Property name="name" type="java.lang.String">static.type.qualification.output.xml_optimized</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.QualificationOutput">
		<Property name="id" type="long">3</Property>
		<Property name="name" type="java.lang.String">static.type.qualification.output.xml_verbose</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.QualificationOutput">
		<Property name="id" type="long">4</Property>
		<Property name="name" type="java.lang.String">static.type.qualification.output.dxf</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.QualificationOutput">
		<Property name="id" type="long">5</Property>
		<Property name="name" type="java.lang.String">static.type.qualification.output.xHTML</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.admin.QualificationOutput">
		<Property name="id" type="long">6</Property>
		<Property name="name" type="java.lang.String">static.type.qualification.output.HTML</Property>
	</Model>

	<!-- ReferencesQuery for Where to used -->
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">2</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObject</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.tag.Tag</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT tag_id FROM tag_content_object WHERE content_object_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>	
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">3</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.FilterCondition</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT id FROM filter_condition WHERE data_element_variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">4</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.FilterCondition</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.ConditionElement</Property>
		<Property name="query" type="java.lang.String"><![CDATA[select distinct ce.id from condition_element ce, condition_subelement cs, filter_condition fc where ce.id = cs.condition_element_id and cs.id = fc.id and fc.data_element_variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">5</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.ConditionElement</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.TargetGroup</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT tg.id FROM condition_item ci, target_group tg WHERE ci.condition_element_id = ? AND ci.target_group_id = tg.instance_id]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">6</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.TargetGroup</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.insert.Insert</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT i.id FROM insert_obj i LEFT JOIN insert_target_group_included ii ON i.id = ii.insert_id LEFT JOIN insert_target_group_excluded ix ON i.id = ix.insert_id LEFT JOIN insert_target_group_extended it ON i.id = it.insert_id WHERE i.status_id != 3 AND (ii.target_group_id = ? OR ix.target_group_id = ? OR it.target_group_id = ?)]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">7</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.TargetGroup</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.tag.Tag</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT t.id FROM tag t LEFT JOIN tag_target_group_included ti ON t.id = ti.tag_id LEFT JOIN tag_target_group_excluded tx ON t.id = tx.tag_id LEFT JOIN tag_target_group_extended tt ON t.id = tt.tag_id WHERE ti.target_group_id = ? OR tx.target_group_id = ? OR tt.target_group_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">8</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.TargetGroup</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObjectData</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cod.guid FROM content_object_data cod LEFT JOIN content_object_tg_included_map codi ON cod.content_object_id = codi.content_object_id AND cod.data_type = codi.data_type LEFT JOIN content_object_tg_excluded_map codx ON cod.content_object_id = codx.content_object_id AND cod.data_type = codx.data_type LEFT JOIN content_object_tg_extended_map codt ON cod.content_object_id = codt.content_object_id AND cod.data_type = codt.data_type INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts ON co.tp_selection_id = ts.id LEFT JOIN content_object_document_map codm ON co.id = codm.content_object_id LEFT JOIN document d1 ON co.document_id = d1.id LEFT JOIN document d2 ON codm.document_id = d2.id WHERE (d1.removed = FALSE OR d2.removed = FALSE) AND (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (codi.target_group_id = ? OR codx.target_group_id = ? OR codt.target_group_id = ?)]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">9</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.insert.Insert</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.tag.Tag</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT ti.tag_id from tag_insert ti WHERE ti.insert_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">10</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.ConditionElement</Property>
		<Property name="query" type="java.lang.String"><![CDATA[select distinct c.id from condition_element c, condition_subelement s left outer join condition_sub_attrib csa on csa.cs_id = s.id where c.id = s.condition_element_id and (s.data_element_variable_id = ? or (csa.attribute_name = 'variableId' and cast(csa.attribute_value as bigint) = ?))]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">13</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.Parameter</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT p.id FROM parameter p WHERE p.data_element_variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">14</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.Parameter</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObjectData</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cod.guid FROM content_object_data cod, parameter_group_item pgi WHERE cod.parameter_group_id = pgi.pg_id AND pgi.parameter_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">15</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.Parameter</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT d.id FROM document d JOIN parameter_group_item pgi ON (d.selection_parameter_group_id = pgi.pg_id OR d.insert_parameter_group_id = pgi.pg_id OR d.language_parameter_group_id = pgi.pg_id) WHERE pgi.parameter_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">16</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.Parameter</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.ParameterGroup</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT pgi.pg_id FROM parameter_group_item pgi WHERE pgi.parameter_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">18</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.ParameterGroup</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObjectData</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT guid FROM content_object_data WHERE parameter_group_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">19</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.ParameterGroup</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
		<Property name="query" type="java.lang.String"><![CDATA[select distinct d.id from document d where d.selection_parameter_group_id = ? or d.insert_parameter_group_id = ? or d.language_parameter_group_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">21</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
		<Property name="query" type="java.lang.String"><![CDATA[(select id from sendmail_configuration where (customer_email_address_var_id = ?)) union (select id from emessaging_configuration where (customer_email_address_var_id = ?) or (customer_phone_number_var_id = ?)) union (select id from clickatell_configuration where (customer_phone_number_var_id = ?)) union (select id from exacttarget_configuration where (customer_email_address_var_id = ?) or (customer_key_var_id = ?))]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>	
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">24</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.attachment.Attachment</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT a.id FROM attachment a LEFT JOIN complex_value_variable cvvn ON a.recipient_name_value_id = cvvn.complex_value_id LEFT JOIN complex_value_variable cvvl ON a.recipient_location_value_id = cvvl.complex_value_id WHERE cvvn.variable_id = ? OR cvvl.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">	
		<Property name="id" type="long">25</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataSourceAssociation</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT da.id FROM data_source_association da, reference_connection dar WHERE da.id = dar.data_source_association_id AND dar.primary_key_variable_id = ? OR dar.reference_key_variable_id = ? UNION SELECT DISTINCT da.id FROM data_source_association da, reference_connection dar, compound_key ck, compound_key_item cki WHERE da.id = dar.data_source_association_id AND dar.reference_compound_key_id = ck.id AND ck.id = cki.compound_key_id AND cki.variable_id = ? UNION SELECT DISTINCT da.id FROM data_source_association da, reference_connection dar, compound_key ck, compound_key_item cki WHERE da.id = dar.data_source_association_id AND dar.primary_compound_key_id = ck.id AND ck.id = cki.compound_key_id AND cki.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
		<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">	
		<Property name="id" type="long">26</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.attachment.Attachment</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT ad.document_id FROM attachment_document ad WHERE ad.attachment_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">	
		<Property name="id" type="long">27</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.TargetGroup</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.attachment.Attachment</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT am.id from attachment am LEFT JOIN attchmnt_target_group_included ami ON am.id = ami.attachment_id LEFT JOIN attchmnt_target_group_excluded amx ON am.id = amx.attachment_id LEFT JOIN attchmnt_target_group_extended amt ON am.id = amt.attachment_id WHERE ami.target_group_id = ? OR amx.target_group_id = ? OR amt.target_group_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
			<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">	
		<Property name="id" type="long">28</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.tag.Tag</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT td.tag_id FROM tag_document td WHERE td.document_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
	 	<Property name="id" type="long">46</Property>
	 	<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
	 	<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
	 	<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT d.id FROM document d, ext_reporting_vars_doc etvs WHERE d.id = etvs.document_id AND (etvs.data_element_variable_id = ? or d.comm_multi_recipient_id = ?)]]></Property>
	 	<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">49</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObjectData</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cod.guid FROM content_variable cv INNER JOIN content c ON c.id = cv.content_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id AND cod.data_type = coa.data_type LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND cv.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">54</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.ftp.FtpConfiguration</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT fc.id FROM ftp_configuration fc LEFT JOIN complex_value_variable cvv ON fc.recipfile_cplex_val_id = cvv.complex_value_id WHERE cvv.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">64</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.TargetGroup</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.TouchpointTargeting</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT tt.id FROM touchpoint_targeting tt LEFT JOIN tp_target_group_included tti ON tt.id = tti.touchpoint_targeting_id LEFT JOIN tp_target_group_excluded ttx ON tt.id = ttx.touchpoint_targeting_id LEFT JOIN tp_target_group_extended ttt ON tt.id = ttt.touchpoint_targeting_id WHERE tti.target_group_id = ? OR ttx.target_group_id = ? OR ttt.target_group_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
	 	<Property name="id" type="long">65</Property>
	 	<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
	 	<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
	 	<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT d.id FROM document d WHERE d.customer_rpt_variable_a_id = ? OR d.customer_rpt_variable_b_id = ? OR d.comm_multi_recipient_id = ?]]></Property>
	 	<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">67</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObject</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Zone</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT z.id FROM zone z WHERE ((z.parent_zone_id IS NULL OR z.override_comm_template IS TRUE) AND (z.template_image_id = ? OR z.template_smart_text_id = ?))]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">68</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObject</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Zone</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT zsta.zone_id FROM zone_smart_text_assets zsta WHERE zsta.smart_text_id = ? UNION SELECT zia.zone_id FROM zone_image_assets zia WHERE zia.image_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">69</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.email.TemplateModifier</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT tm.id FROM template_modifier tm LEFT JOIN complex_value_variable cvv ON tm.complex_value_id = cvv.complex_value_id WHERE cvv.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">70</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObject</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.email.TemplateModifier</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT tm.id FROM template_modifier tm LEFT JOIN complex_value_content_object cvco ON tm.complex_value_id = cvco.complex_value_id WHERE cvco.content_object_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">72</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyleFont</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT t.id FROM text_style t WHERE t.apply_font = true AND t.font_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">73</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyleFont</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyleCustomization</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT tc.id FROM text_style_cust tc WHERE tc.apply_font = true AND tc.font_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">74</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT d.id FROM document d WHERE d.default_text_style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">75</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Zone</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT z.id FROM zone z LEFT JOIN zone_style ts ON z.id = ts.zone_id WHERE ((z.parent_zone_id IS NULL OR z.override_text_styles IS TRUE) AND ts.style_id = ?) OR (z.default_text_style_id = ? AND (z.parent_zone_id IS NULL OR z.override_default_text_style is TRUE))]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">76</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyleCustomization</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT tsc.id FROM text_style_cust tsc WHERE tsc.master_text_style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">77</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyle</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT ps.id FROM paragraph_style ps WHERE ps.style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">78</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyleCustomization</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT psc.id FROM para_style_cust psc WHERE psc.text_style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">79</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObjectData</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cod.guid FROM content_text_style cts INNER JOIN content c ON c.id = cts.content_id INNER JOIN content_object_association coa ON coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id AND cod.data_type = coa.data_type LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND cts.style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">81</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyleCustomization</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT psc.id FROM para_style_cust psc WHERE psc.master_para_style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">82</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT d.id FROM document d WHERE d.default_paragraph_style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">83</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Zone</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT z.id FROM zone z LEFT JOIN zone_paragraph_style ps ON z.id = ps.zone_id WHERE ((z.parent_zone_id IS NULL OR z.override_paragraph_styles IS TRUE) AND ps.paragraph_style_id = ?) OR (z.default_paragraph_style_id = ? AND (z.parent_zone_id IS NULL OR z.override_default_para_style IS TRUE))]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">84</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObjectData</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cod.guid FROM content_paragraph_style cps INNER JOIN content c ON c.id = cps.content_id INNER JOIN content_object_association coa ON coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id AND cod.data_type = coa.data_type LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND cps.paragraph_style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">86</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.Parameter</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT v.id FROM data_element_variable v WHERE v.parameter_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<!-- Local Smart Text Reference -->
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">87</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObject</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObjectData</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cod.guid FROM content_object_data cod INNER JOIN content_object_association coa ON cod.content_object_id = coa.content_object_id AND cod.data_type = coa.data_type INNER JOIN content_content_object_type ccot ON ccot.content_id = coa.content_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND ccot.content_object_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<!-- Image Reference -->
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">89</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObject</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObjectData</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cod.guid FROM content_object_data cod INNER JOIN content_object_association coa ON cod.content_object_id = coa.content_object_id AND cod.data_type = coa.data_type LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND coa.ref_image_library_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">90</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.TargetGroup</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObjectData</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cod.guid FROM content_content_targeting cct LEFT JOIN content_target_group_included cti ON cct.content_targeting_id = cti.content_targeting_id LEFT JOIN content_target_group_excluded ctx ON cct.content_targeting_id = ctx.content_targeting_id LEFT JOIN content_target_group_extended cte ON cct.content_targeting_id = cte.content_targeting_id INNER JOIN content c ON c.id = cct.content_id INNER JOIN content_object_association coa ON coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id AND cod.data_type = coa.data_type LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id LEFT JOIN content_object_document_map codm ON co.id = codm.content_object_id LEFT JOIN document d1 ON co.document_id = d1.id LEFT JOIN document d2 ON codm.document_id = d2.id WHERE (d1.removed = FALSE OR d2.removed = FALSE) AND (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND (cti.target_group_id = ? OR ctx.target_group_id = ? OR cte.target_group_id = ?)]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">91</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ListStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ListStyleCustomization</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT lsc.id FROM list_style_cust lsc WHERE lsc.master_list_style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">92</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ListStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT d.id FROM document d WHERE d.default_list_style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">93</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ListStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Zone</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT z.id FROM zone z LEFT JOIN zone_list_style ls ON z.id = ls.zone_id WHERE ((z.parent_zone_id IS NULL OR z.override_list_styles IS TRUE) AND ls.list_style_id = ?) OR (z.default_list_style_id = ? AND (z.parent_zone_id IS NULL OR z.override_default_list_style IS TRUE))]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">94</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ListStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObjectData</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cod.guid FROM content_list_style cps INNER JOIN content c ON c.id = cps.content_id INNER JOIN content_object_association coa ON coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id AND cod.data_type = coa.data_type LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND cps.list_style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">	
		<Property name="id" type="long">96</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.nativecomposition.NativeCompositionConfiguration</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT nc.id FROM native_comp_configuration nc LEFT JOIN complex_value_variable cvvf ON nc.output_filename_cplex_val_id = cvvf.complex_value_id LEFT JOIN complex_value_variable cvvt ON nc.output_doc_title_cplex_val_id = cvvt.complex_value_id WHERE cvvf.variable_id = ? or cvvt.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">99</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ListStyle</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT ls.id FROM list_style ls WHERE ls.text_style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">100</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ListStyleCustomization</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT lsc.id FROM list_style_cust lsc WHERE lsc.text_style_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">	
		<Property name="id" type="long">101</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.TouchpointCollection</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT tc.id FROM touchpoint_collection tc LEFT JOIN complex_value_variable cvvf ON tc.output_filename_cplex_val_id = cvvf.complex_value_id LEFT JOIN complex_value_variable cvvt ON tc.output_doc_title_cplex_val_id = cvvt.complex_value_id WHERE cvvf.variable_id = ? or cvvt.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">102</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.communication.Communication</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT czca.communication_id FROM comm_zone_content_association czca, content_variable cv WHERE czca.content_id = cv.content_id AND cv.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">103</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT v.id FROM complex_value_variable cvv JOIN data_element_variable v ON cvv.complex_value_id IN (v.expression, v.script) WHERE cvv.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">104</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.tag.Tag</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT t.id FROM tag t, complex_value_variable cvv WHERE t.tag_content = cvv.complex_value_id AND cvv.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">105</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObject</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.tag.Tag</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT t.id FROM tag t, complex_value_content_object cvco WHERE t.tag_content = cvco.complex_value_id AND cvco.content_object_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">106</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ClipboardContent</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cc.id FROM clipboard_content cc , complex_value_variable cvv WHERE cc.content_id = cvv.complex_value_id AND cvv.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">107</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.sefas.SefasConfiguration</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT sc.id FROM sefas_configuration sc LEFT JOIN complex_value_variable cvvf ON sc.output_filename_cplex_val_id = cvvf.complex_value_id LEFT JOIN complex_value_variable cvvt ON sc.output_doc_title_cplex_val_id = cvvt.complex_value_id WHERE cvvf.variable_id = ? or cvvt.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">108</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.sefas.MPHCSConfiguration</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT sc.id FROM mphcs_configuration sc LEFT JOIN complex_value_variable cvvf ON sc.output_filename_cplex_val_id = cvvf.complex_value_id LEFT JOIN complex_value_variable cvvt ON sc.output_doc_title_cplex_val_id = cvvt.complex_value_id WHERE cvvf.variable_id = ? or cvvt.variable_id = ?]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<!-- Variable used in image's properties (image link, alt text, external link and external path) -->
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">109</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObjectData</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cod.guid FROM complex_value_variable cvv INNER JOIN content c ON c.image_link = cvv.complex_value_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND cvv.variable_id = ? UNION SELECT DISTINCT cod.guid FROM complex_value_variable cvv INNER JOIN content c ON c.image_alt_text = cvv.complex_value_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND cvv.variable_id = ? UNION SELECT DISTINCT cod.guid FROM complex_value_variable cvv INNER JOIN content c ON c.image_ext_link = cvv.complex_value_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND cvv.variable_id = ? UNION SELECT DISTINCT cod.guid FROM complex_value_variable cvv INNER JOIN content c ON c.image_ext_path = cvv.complex_value_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND cvv.variable_id = ? ]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<!-- Smart text used in image's properties (image link, alt text, external link and external path) -->
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">110</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObject</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObjectData</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cod.guid FROM complex_value_content_object cvco INNER JOIN content c ON c.image_link = cvco.complex_value_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND cvco.content_object_id = ? UNION SELECT DISTINCT cod.guid FROM complex_value_content_object cvco INNER JOIN content c ON c.image_alt_text = cvco.complex_value_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND cvco.content_object_id = ? UNION SELECT DISTINCT cod.guid FROM complex_value_content_object cvco INNER JOIN content c ON c.image_ext_link = cvco.complex_value_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND cvco.content_object_id = ? UNION SELECT DISTINCT cod.guid FROM complex_value_content_object cvco INNER JOIN content c ON c.image_ext_path = cvco.complex_value_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) AND cvco.content_object_id = ? ]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<!-- Variable used in interview definition (image link, alt text, external link and external path) -->
	<Model class="com.prinova.messagepoint.model.wtu.ReferencesQuery">
		<Property name="id" type="long">111</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.communication.CommunicationOrderEntryItemDefinition</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT odef.id FROM order_entry_item_definition odef WHERE odef.data_element_variable_id = ? ]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">1</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT d.default_text_style_id FROM document d WHERE d.default_text_style_id IS NOT NULL]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">2</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Zone</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT ts.style_id FROM zone z INNER JOIN zone_style ts ON z.id = ts.zone_id WHERE z.parent_zone_id IS NULL OR z.override_text_styles = true UNION SELECT DISTINCT z.default_text_style_id FROM zone z WHERE (z.parent_zone_id IS NULL OR z.override_default_text_style = true) AND z.default_text_style_id IS NOT NULL]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">3</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyleCustomization</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT tsc.master_text_style_id FROM text_style_cust tsc]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
		<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">4</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyle</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT ps.style_id FROM paragraph_style ps WHERE ps.style_id IS NOT NULL]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">5</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyleCustomization</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT psc.text_style_id FROM para_style_cust psc]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">6</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObject</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cts.style_id FROM content_text_style cts INNER JOIN content c ON c.id = cts.content_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id AND cod.data_type = coa.data_type LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL)]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">8</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyleCustomization</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT psc.master_para_style_id FROM para_style_cust psc]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">9</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT d.default_paragraph_style_id FROM document d WHERE d.default_paragraph_style_id IS NOT NULL]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">10</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Zone</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT ps.paragraph_style_id FROM zone z INNER JOIN zone_paragraph_style ps ON z.id = ps.zone_id WHERE z.parent_zone_id IS NULL OR z.override_paragraph_styles = true UNION SELECT DISTINCT z.default_paragraph_style_id FROM zone z WHERE (z.parent_zone_id IS NULL OR z.override_default_para_style = true) AND z.default_paragraph_style_id IS NOT NULL]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">11</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ParagraphStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObject</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cps.paragraph_style_id FROM content_paragraph_style cps INNER JOIN content c ON c.id = cps.content_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id AND cod.data_type = coa.data_type LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL)]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">13</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.FilterCondition</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT f.data_element_variable_id FROM filter_condition f WHERE f.data_element_variable_id IS NOT NULL]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">14</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.targeting.ConditionElement</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT s.data_element_variable_id FROM condition_subelement s UNION SELECT DISTINCT CAST(csa.attribute_value AS INT) FROM condition_sub_attrib csa WHERE csa.attribute_name = 'variableId']]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">16</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.Parameter</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT p.data_element_variable_id FROM parameter p WHERE p.data_element_variable_id IS NOT NULL]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">17</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.Document</Property>
		<Property name="query" type="java.lang.String"><![CDATA[(SELECT DISTINCT customer_email_address_var_id FROM sendmail_configuration WHERE customer_email_address_var_id IS NOT NULL) UNION (SELECT DISTINCT customer_email_address_var_id FROM emessaging_configuration WHERE customer_email_address_var_id IS NOT NULL) UNION (SELECT DISTINCT customer_phone_number_var_id FROM emessaging_configuration WHERE customer_phone_number_var_id IS NOT NULL) UNION (SELECT DISTINCT customer_phone_number_var_id FROM clickatell_configuration WHERE customer_phone_number_var_id IS NOT NULL) UNION (SELECT DISTINCT customer_email_address_var_id FROM exacttarget_configuration WHERE customer_email_address_var_id IS NOT NULL) UNION (SELECT DISTINCT customer_key_var_id FROM exacttarget_configuration WHERE customer_key_var_id IS NOT NULL) UNION (SELECT DISTINCT data_element_variable_id FROM ext_reporting_vars_doc) UNION (SELECT DISTINCT customer_rpt_variable_a_id FROM document) UNION (SELECT DISTINCT customer_rpt_variable_b_id FROM document) UNION (SELECT DISTINCT comm_multi_recipient_id FROM document)]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">	
		<Property name="id" type="long">18</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.attachment.Attachment</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cvv.variable_id FROM complex_value_variable cvv INNER JOIN complex_value cv ON cv.id = cvv.complex_value_id INNER JOIN attachment a ON a.recipient_name_value_id = cv.id WHERE a.id is not null UNION SELECT DISTINCT cvv.variable_id FROM complex_value_variable cvv INNER JOIN complex_value cv ON cv.id = cvv.complex_value_id INNER JOIN attachment a ON a.recipient_location_value_id = cv.id WHERE a.id is not null]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">	
		<Property name="id" type="long">19</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataSourceAssociation</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT primary_key_variable_id FROM reference_connection UNION SELECT DISTINCT reference_key_variable_id FROM reference_connection UNION SELECT DISTINCT variable_id FROM compound_key_item]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">	
		<Property name="id" type="long">20</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.ftp.FtpConfiguration</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cvvf.variable_id FROM ftp_configuration fc LEFT JOIN complex_value_variable cvvf ON fc.output_filename_cplex_val_id = cvvf.complex_value_id WHERE cvvf.variable_id IS NOT NULL]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">21</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.email.TemplateModifier</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cvv.variable_id FROM template_modifier tm LEFT JOIN complex_value_variable cvv ON tm.complex_value_id = cvv.complex_value_id WHERE cvv.variable_id IS NOT NULL]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">	
		<Property name="id" type="long">22</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.nativecomposition.NativeCompositionConfiguration</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cvv.variable_id FROM NATIVE_COMP_CONFIGURATION nc INNER JOIN COMPLEX_VALUE cv on nc.OUTPUT_FILENAME_CPLEX_VAL_ID = cv.id INNER JOIN COMPLEX_VALUE_VARIABLE cvv on cvv.complex_value_id = cv.id UNION SELECT DISTINCT cvv.variable_id FROM NATIVE_COMP_CONFIGURATION nc INNER JOIN COMPLEX_VALUE cv on nc.OUTPUT_DOC_TITLE_CPLEX_VAL_ID = cv.id INNER JOIN COMPLEX_VALUE_VARIABLE cvv on cvv.complex_value_id = cv.id]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">23</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObject</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cv.variable_id FROM content_variable cv INNER JOIN content c ON c.id = cv.content_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id AND cod.data_type = coa.data_type LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL)]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">25</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ListStyle</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT ls.text_style_id FROM list_style ls WHERE ls.text_style_id IS NOT NULL]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">26</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.font.TextStyle</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.font.ListStyleCustomization</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT lsc.text_style_id FROM list_style_cust lsc WHERE lsc.text_style_id IS NOT NULL]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">	
		<Property name="id" type="long">27</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.TouchpointCollection</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cvv.variable_id FROM touchpoint_collection tc INNER JOIN COMPLEX_VALUE cv on tc.OUTPUT_FILENAME_CPLEX_VAL_ID = cv.id INNER JOIN COMPLEX_VALUE_VARIABLE cvv on cvv.complex_value_id = cv.id UNION SELECT DISTINCT cvv.variable_id FROM touchpoint_collection tc INNER JOIN COMPLEX_VALUE cv on tc.OUTPUT_DOC_TITLE_CPLEX_VAL_ID = cv.id INNER JOIN COMPLEX_VALUE_VARIABLE cvv on cvv.complex_value_id = cv.id]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">	
		<Property name="id" type="long">28</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.communication.Communication</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cv.variable_id FROM comm_zone_content_association czca, content_variable cv WHERE czca.content_id = cv.content_id]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">29</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cvv.variable_id FROM data_element_variable v, complex_value_variable cvv WHERE v.expression = cvv.complex_value_id OR v.script = cvv.complex_value_id]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">30</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.tag.Tag</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cvv.variable_id FROM tag t, complex_value_variable cvv WHERE t.tag_content = cvv.complex_value_id]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">31</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ClipboardContent</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cvv.variable_id FROM clipboard_content cc, complex_value_variable cvv WHERE cc.content_id = cvv.complex_value_id]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">32</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.sefas.SefasConfiguration</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cvv.variable_id FROM SEFAS_CONFIGURATION nc INNER JOIN COMPLEX_VALUE cv on nc.OUTPUT_FILENAME_CPLEX_VAL_ID = cv.id INNER JOIN COMPLEX_VALUE_VARIABLE cvv on cvv.complex_value_id = cv.id UNION SELECT DISTINCT cvv.variable_id FROM SEFAS_CONFIGURATION nc INNER JOIN COMPLEX_VALUE cv on nc.OUTPUT_DOC_TITLE_CPLEX_VAL_ID = cv.id INNER JOIN COMPLEX_VALUE_VARIABLE cvv on cvv.complex_value_id = cv.id]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">33</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.sefas.MPHCSConfiguration</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cvv.variable_id FROM MPHCS_CONFIGURATION nc INNER JOIN COMPLEX_VALUE cv on nc.OUTPUT_FILENAME_CPLEX_VAL_ID = cv.id INNER JOIN COMPLEX_VALUE_VARIABLE cvv on cvv.complex_value_id = cv.id UNION SELECT DISTINCT cvv.variable_id FROM SEFAS_CONFIGURATION nc INNER JOIN COMPLEX_VALUE cv on nc.OUTPUT_DOC_TITLE_CPLEX_VAL_ID = cv.id INNER JOIN COMPLEX_VALUE_VARIABLE cvv on cvv.complex_value_id = cv.id]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	<!-- Variables used in image's properties (image link, alt text, external link and external path) -->
	<Model class="com.prinova.messagepoint.model.wtu.AllReferencesQuery">
		<Property name="id" type="long">34</Property>
		<Property name="objectClassName" type="java.lang.String">com.prinova.messagepoint.model.admin.DataElementVariable</Property>
		<Property name="directRefClassName" type="java.lang.String">com.prinova.messagepoint.model.content.ContentObject</Property>
		<Property name="query" type="java.lang.String"><![CDATA[SELECT DISTINCT cvv.variable_id FROM complex_value_variable cvv INNER JOIN content c ON c.image_link = cvv.complex_value_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) UNION SELECT DISTINCT cvv.variable_id FROM complex_value_variable cvv INNER JOIN content c ON c.image_alt_text = cvv.complex_value_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) UNION SELECT DISTINCT cvv.variable_id FROM complex_value_variable cvv INNER JOIN content c ON c.image_ext_link = cvv.complex_value_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL) UNION SELECT DISTINCT cvv.variable_id FROM complex_value_variable cvv INNER JOIN content c ON c.image_ext_path = cvv.complex_value_id INNER JOIN content_object_association coa on coa.content_id = c.id INNER JOIN content_object_data cod ON cod.content_object_id = coa.content_object_id LEFT JOIN touchpoint_selection ts ON coa.tp_pg_tn_id = ts.pg_tree_node_id INNER JOIN content_object co ON cod.content_object_id = co.id LEFT JOIN touchpoint_selection ts2 ON co.tp_selection_id = ts2.id WHERE (ts.archive_type_id != 1 OR ts.archive_type_id IS NULL) AND (ts2.archive_type_id != 1 OR ts2.archive_type_id IS NULL)]]></Property>
		<Property name="type" type="java.lang.String">SQL</Property>
	</Model>
	
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">1</Property> 
		<Property name="name" type="java.lang.String" >Code 11</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Code11.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">2</Property> 
		<Property name="name" type="java.lang.String" >Code 2 of 5 (Standard)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Code2of5Standard.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">3</Property> 
		<Property name="name" type="java.lang.String" >Interleaved 2 of 5 Standard </Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Code2of5ITF.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">4</Property> 
		<Property name="name" type="java.lang.String" >Code 2 of 5 IATA </Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Code2of5IATA.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">5</Property> 
		<Property name="name" type="java.lang.String" >Code 2 of 5 Matrix</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">6</Property> 
		<Property name="name" type="java.lang.String" >Code 2 of 5 Data Logic</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">7</Property> 
		<Property name="name" type="java.lang.String" >Code 2 of 5 Industrial</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">8</Property> 
		<Property name="name" type="java.lang.String" >Code 3 of 9 (Code 39)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Code39.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">9</Property> 
		<Property name="name" type="java.lang.String" >Code 3 of 9 (Code 39) ASCII</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Code93FullAscii.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">10</Property> 
		<Property name="name" type="java.lang.String" >EAN8</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >EAN8.gif</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">11</Property> 
		<Property name="name" type="java.lang.String" >EAN8 - 2 digits add on</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >EAN8P2.gif</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">12</Property> 
		<Property name="name" type="java.lang.String" >EAN8 - 5 digits add on</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >EAN8P5.gif</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">13</Property> 
		<Property name="name" type="java.lang.String" >EAN13</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >EAN13.gif</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">14</Property> 
		<Property name="name" type="java.lang.String" >EAN13 - 2 digits add on</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >EAN13P2.gif</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">15</Property> 
		<Property name="name" type="java.lang.String" >EAN13 - 5 digits add on</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >EAN13P5.gif</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">16</Property> 
		<Property name="name" type="java.lang.String" >EAN128</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >EAN128.gif</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">17</Property> 
		<Property name="name" type="java.lang.String" >UPC 12 Digits</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">18</Property> 
		<Property name="name" type="java.lang.String" >Codabar (2 widths)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >CodaBar2Width.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">19</Property> 
		<Property name="name" type="java.lang.String" >Reserved</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >0</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">20</Property> 
		<Property name="name" type="java.lang.String" >Code128 automatic subset switching / auto compress</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">21</Property> 
		<Property name="name" type="java.lang.String" >Deutsche Post Leitcode</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">22</Property> 
		<Property name="name" type="java.lang.String" >Deutsche Post Identcode</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">23</Property> 
		<Property name="name" type="java.lang.String" >ISBN 13 - 5 digits add on</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >ISBN.gif</Property>
		<Property name="type" type="int" >4</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">24</Property> 
		<Property name="name" type="java.lang.String" >ISMN </Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >4</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">25</Property> 
		<Property name="name" type="java.lang.String" >Code 93</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Code93.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">26</Property> 
		<Property name="name" type="java.lang.String" >ISSN</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >4</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">27</Property> 
		<Property name="name" type="java.lang.String" >ISSN - 2 digits addon</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >4</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">28</Property> 
		<Property name="name" type="java.lang.String" >Flattermarken</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">29</Property> 
		<Property name="name" type="java.lang.String" >GS1 DataBar (RSS-14)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >gs1-databar.gif</Property>
		<Property name="type" type="int" >5</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">30</Property> 
		<Property name="name" type="java.lang.String" >GS1 DataBar Limited (RSS Limited)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >gs1-databar-limited.gif</Property>
		<Property name="type" type="int" >5</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">31</Property> 
		<Property name="name" type="java.lang.String" >GS1 DataBar Expanded (RSS Expanded)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >gs1-databar-expanded.gif</Property>
		<Property name="type" type="int" >5</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">32</Property> 
		<Property name="name" type="java.lang.String" >Telepen Alpha</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">33</Property> 
		<Property name="name" type="java.lang.String" >UCC128 (= EAN128)</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">34</Property> 
		<Property name="name" type="java.lang.String" >UPC A</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >UPCA.gif</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">35</Property> 
		<Property name="name" type="java.lang.String" >UPC A – 2 digit add on </Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">36</Property> 
		<Property name="name" type="java.lang.String" >UPC A – 5 digit add on</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">37</Property> 
		<Property name="name" type="java.lang.String" >UPC E</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >UPCE.gif</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">38</Property> 
		<Property name="name" type="java.lang.String" >UPC E – 2 digit add on</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">39</Property> 
		<Property name="name" type="java.lang.String" >UPC E – 5 digit add on</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >2</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">40</Property> 
		<Property name="name" type="java.lang.String" >USPS PostNet-5 (ZIP 5 digits)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Postnet5.gif</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">41</Property> 
		<Property name="name" type="java.lang.String" >USPS PostNet-6 (ZIP 5 digits + check digit)</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">42</Property> 
		<Property name="name" type="java.lang.String" >USPS PostNet -9 (ZIP + 4)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Postnet9.gif</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">43</Property> 
		<Property name="name" type="java.lang.String" >USPS PostNet-10 (ZIP + 4 + check digit)</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">44</Property> 
		<Property name="name" type="java.lang.String" >USPS PostNet-11 (ZIP + 4 + 2)</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">45</Property> 
		<Property name="name" type="java.lang.String" >USPS PostNet -12 (ZIP + 4 + 2+ check digit)</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">46</Property> 
		<Property name="name" type="java.lang.String" >Plessey Code</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >0</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">47</Property> 
		<Property name="name" type="java.lang.String" >MSI Plessey Code</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >0</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">48</Property> 
		<Property name="name" type="java.lang.String" >SSCC18</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >0</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">49</Property> 
		<Property name="name" type="java.lang.String" >Reserved</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >0</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">50</Property> 
		<Property name="name" type="java.lang.String" >LOGMARS</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >0</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">51</Property> 
		<Property name="name" type="java.lang.String" >Pharmacode One-Track</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Pharmacode.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">52</Property> 
		<Property name="name" type="java.lang.String" >PZN (Pharma Zentral Nummer Germany)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >PZN.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">53</Property> 
		<Property name="name" type="java.lang.String" >Pharmacode Two-Track</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Pharmacode_twotrack.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">54</Property> 
		<Property name="name" type="java.lang.String" >Brazilian CEPNet</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >0</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">55</Property> 
		<Property name="name" type="java.lang.String" >PDF417</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >PDF417.gif</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">56</Property> 
		<Property name="name" type="java.lang.String" >PDF417 Truncated</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >PDF417Truncated.gif</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">57</Property> 
		<Property name="name" type="java.lang.String" >MaxiCode</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >MaxiCode.gif</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">58</Property> 
		<Property name="name" type="java.lang.String" >QR-Code</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >QR-CODE.gif</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">59</Property> 
		<Property name="name" type="java.lang.String" >Code128 (Subset A)</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">60</Property> 
		<Property name="name" type="java.lang.String" >Code128 (Subset B)</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">61</Property> 
		<Property name="name" type="java.lang.String" >Code128 (Subset C)</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">62</Property> 
		<Property name="name" type="java.lang.String" >Code 93 Ascii</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Code39Extended.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">63</Property> 
		<Property name="name" type="java.lang.String" >Australian Post standard customer barcode</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >AustralianPostCustom.gif</Property>
		<Property name="type" type="int" >3</Property>
	</Model>  
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">64</Property> 
		<Property name="name" type="java.lang.String" >Australian Post customer barcode 2</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">65</Property> 
		<Property name="name" type="java.lang.String" >Australian Post customer barcode 3</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">66</Property> 
		<Property name="name" type="java.lang.String" >Australian Post Reply Paid barcode</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">67</Property> 
		<Property name="name" type="java.lang.String" >Australian Post Routing barcode</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">68</Property> 
		<Property name="name" type="java.lang.String" >Australian Post Redirection barcode</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">69</Property> 
		<Property name="name" type="java.lang.String" >ISBN 13 (=EAN13P5)</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >4</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">70</Property> 
		<Property name="name" type="java.lang.String" >Royal Mail 4 State customer code (RM4SCC)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >RoyalMail4State.gif</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">71</Property> 
		<Property name="name" type="java.lang.String" >Data Matrix</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >DataMatrix.gif</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">72</Property> 
		<Property name="name" type="java.lang.String" >EAN-14</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >EAN14.gif</Property>
		<Property name="type" type="int" >2</Property>
	</Model>  
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">73</Property> 
		<Property name="name" type="java.lang.String" >VIN / FIN</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >0</Property>
	</Model>  
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">74</Property> 
		<Property name="name" type="java.lang.String" >Codablock-F</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Codablock_F.jpg</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">75</Property> 
		<Property name="name" type="java.lang.String" >NVE-18</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >0</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">76</Property> 
		<Property name="name" type="java.lang.String" >Japanese Postal customer code</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">77</Property> 
		<Property name="name" type="java.lang.String" >Korean Postal Authority Code</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">78</Property> 
		<Property name="name" type="java.lang.String" >GS1 DataBar Truncated (RSS-14 Truncated)</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >5</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">79</Property> 
		<Property name="name" type="java.lang.String" >GS1 DataBar Stacked (RSS-14 Stacked)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >gs1-databar-stacked.gif</Property>
		<Property name="type" type="int" >5</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">80</Property> 
		<Property name="name" type="java.lang.String" >GS1 DataBar Stacked Omnidirectional (RSS-14 Stacked Omnidirectional)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >gs1-databar-stacked-omni.gif</Property>
		<Property name="type" type="int" >5</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">81</Property> 
		<Property name="name" type="java.lang.String" >GS1 DataBar Expanded Stacked (RSS Expanded Stacked)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >gs1-databar-expanded-stacked.gif</Property>
		<Property name="type" type="int" >5</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">82</Property> 
		<Property name="name" type="java.lang.String" >Planet Code 12 digits</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">83</Property> 
		<Property name="name" type="java.lang.String" >Planet Code 14 digits</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">84</Property> 
		<Property name="name" type="java.lang.String" >MicroPDF417</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">85</Property> 
		<Property name="name" type="java.lang.String" >USPS Intelligent Mail® Barcode</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">86</Property> 
		<Property name="name" type="java.lang.String" >Plessey Code with bidirectional reading support</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >0</Property>
	</Model>  
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">87</Property> 
		<Property name="name" type="java.lang.String" >Telepen</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">88</Property> 
		<Property name="name" type="java.lang.String" >GS1-128 (EAN/UCC-128)</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >EAN128.gif</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">89</Property> 
		<Property name="name" type="java.lang.String" >ITF-14</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >0</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">90</Property> 
		<Property name="name" type="java.lang.String" >KIX</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">91</Property> 
		<Property name="name" type="java.lang.String" >Code 32 (Italian Pharmacode)</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model>  
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">92</Property> 
		<Property name="name" type="java.lang.String" >Aztec Code</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >Aztec.gif</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">93</Property> 
		<Property name="name" type="java.lang.String" >DAFT Code</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">94</Property> 
		<Property name="name" type="java.lang.String" >Italian Postal 2 of 5</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">95</Property> 
		<Property name="name" type="java.lang.String" >Italian Postal 3 of 9</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">96</Property> 
		<Property name="name" type="java.lang.String" >DPD Code</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">97</Property> 
		<Property name="name" type="java.lang.String" >Micro QR-Code</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">98</Property> 
		<Property name="name" type="java.lang.String" >HIBC LIC 128</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model>  
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">99</Property> 
		<Property name="name" type="java.lang.String" >HIBC LIC 39</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">100</Property> 
		<Property name="name" type="java.lang.String" >HIBC PAS 128</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">101</Property> 
		<Property name="name" type="java.lang.String" >HIBC PAS 39</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">102</Property> 
		<Property name="name" type="java.lang.String" >HIBC LIC Data Matrix</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">103</Property> 
		<Property name="name" type="java.lang.String" >HIBC PAS Data Matrix</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model>  
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">104</Property> 
		<Property name="name" type="java.lang.String" >HIBC LIC QR-Code</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model>  
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">105</Property> 
		<Property name="name" type="java.lang.String" >HIBC PAS QR-Code</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">106</Property> 
		<Property name="name" type="java.lang.String" >HIBC LIC PDF417</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">107</Property> 
		<Property name="name" type="java.lang.String" >HIBC PAS PDF417</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">108</Property> 
		<Property name="name" type="java.lang.String" >HIBC LIC MicroPDF417</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model>  
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">109</Property> 
		<Property name="name" type="java.lang.String" >HIBC PAS MicroPDF417</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">110</Property> 
		<Property name="name" type="java.lang.String" >HIBC LIC Codablock-F</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">111</Property> 
		<Property name="name" type="java.lang.String" >HIBC PAS Codablock-F</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">112</Property> 
		<Property name="name" type="java.lang.String" >QR-Code 2005</Property>
		<Property name="enabled" type="boolean" >true</Property>
		<Property name="sampleImage" type="java.lang.String" >QR-CODE.gif</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">113</Property> 
		<Property name="name" type="java.lang.String" >PZN8 (Pharma Zentral Nummer Germany, 8 digits)</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >1</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">114</Property> 
		<Property name="name" type="java.lang.String" >Reserved</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >0</Property>
	</Model> > 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">115</Property> 
		<Property name="name" type="java.lang.String" >DotCode</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">116</Property> 
		<Property name="name" type="java.lang.String" >Han Xin Code</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >6</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">117</Property> 
		<Property name="name" type="java.lang.String" >USPS Intelligent Mail® Package Barcode</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">118</Property> 
		<Property name="name" type="java.lang.String" >Swedish Postal Shipment Item ID Barcode</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model> 
	<Model class="com.prinova.messagepoint.model.barcode.BarcodeType">
 		<Property name="id" type="long">119</Property> 
		<Property name="name" type="java.lang.String" >Royal Mail CMDM Mailmark</Property>
		<Property name="enabled" type="boolean" >false</Property>
		<Property name="sampleImage" type="java.lang.String" >NULL</Property>
		<Property name="type" type="int" >3</Property>
	</Model>

	<Model class="com.prinova.messagepoint.model.filters.WordCountFilter">
		<Property name="appliesTo" type="java.lang.String">MP</Property>
		<Property name="objectId" type="long">-1</Property>
		<Property name="section" type="java.lang.String">exact_matches_similarities</Property>
		<Property name="operation" type="java.lang.String">gt</Property>
		<Property name="value" type="java.lang.String">3</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.filters.WordCountFilter">
		<Property name="appliesTo" type="java.lang.String">MP</Property>
		<Property name="objectId" type="long">-1</Property>
		<Property name="section" type="java.lang.String">exact_matches_similarities</Property>
		<Property name="operation" type="java.lang.String">lt</Property>
		<Property name="value" type="java.lang.String">-1</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.filters.WordCountFilter">
		<Property name="appliesTo" type="java.lang.String">MP</Property>
		<Property name="objectId" type="long">-1</Property>
		<Property name="section" type="java.lang.String">readability</Property>
		<Property name="operation" type="java.lang.String">gt</Property>
		<Property name="value" type="java.lang.String">5</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.filters.WordCountFilter">
		<Property name="appliesTo" type="java.lang.String">MP</Property>
		<Property name="objectId" type="long">-1</Property>
		<Property name="section" type="java.lang.String">readability</Property>
		<Property name="operation" type="java.lang.String">lt</Property>
		<Property name="value" type="java.lang.String">-1</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.filters.WordCountFilter">
		<Property name="appliesTo" type="java.lang.String">MP</Property>
		<Property name="objectId" type="long">-1</Property>
		<Property name="section" type="java.lang.String">sentiment</Property>
		<Property name="operation" type="java.lang.String">gt</Property>
		<Property name="value" type="java.lang.String">5</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.filters.WordCountFilter">
		<Property name="appliesTo" type="java.lang.String">MP</Property>
		<Property name="objectId" type="long">-1</Property>
		<Property name="section" type="java.lang.String">sentiment</Property>
		<Property name="operation" type="java.lang.String">lt</Property>
		<Property name="value" type="java.lang.String">-1</Property>
	</Model>

	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">1</Property>
		<Property name="name" type="java.lang.String" >Message</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">2</Property>
		<Property name="name" type="java.lang.String" >Smart Object</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">3</Property>
		<Property name="name" type="java.lang.String" >Image</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">4</Property>
		<Property name="name" type="java.lang.String" >Touchpoint</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">5</Property>
		<Property name="name" type="java.lang.String" >Rule</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">6</Property>
		<Property name="name" type="java.lang.String" >Target Group</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">7</Property>
		<Property name="name" type="java.lang.String" >Order</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">8</Property>
		<Property name="name" type="java.lang.String" >Metadata</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">9</Property>
		<Property name="name" type="java.lang.String" >Task</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">10</Property>
		<Property name="name" type="java.lang.String" >Project</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">11</Property>
		<Property name="name" type="java.lang.String" >Application</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">12</Property>
		<Property name="name" type="java.lang.String" >Document</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">13</Property>
		<Property name="name" type="java.lang.String" >Content</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">14</Property>
		<Property name="name" type="java.lang.String" >Query</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">15</Property>
		<Property name="name" type="java.lang.String" >Test Order</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">16</Property>
		<Property name="name" type="java.lang.String" >Content and document</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">17</Property>
		<Property name="name" type="java.lang.String" >Shared content</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">18</Property>
		<Property name="name" type="java.lang.String" >Variable</Property>
	</Model>
	<Model class="com.prinova.messagepoint.model.tagcloud.TagCloudType">
		<Property name="id" type="long">19</Property>
		<Property name="name" type="java.lang.String" >Zone</Property>
	</Model>

</GeneralMetadata>
