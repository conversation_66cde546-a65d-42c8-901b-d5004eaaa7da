action.button.label.compare.report=
action.button.label.notification.settings=
action.button.label.sync=
action.button.label.variants=
background.task.sub.type.calculate.hash=
background.task.sub.type.test.results.package=
background_task.sub.type.db.delete.schema=
background_task.sub.type.dynamic.message.import=
background_task.sub.type.export.rationalizer.to.touchpoint=
background_task.sub.type.image.variants.export=
background_task.sub.type.image.variants.import=
background_task.sub.type.job.performance.report.export=
background_task.sub.type.message.template.export=
background_task.sub.type.message.template.import=
background_task.sub.type.message.variants.export=
background_task.sub.type.message.variants.import=
background_task.sub.type.messagepoint.delete.brand=
background_task.sub.type.messagepoint.recompute.brand=
background_task.sub.type.messages.on.approval.export=
background_task.sub.type.push.content.to.elastic=
background_task.sub.type.rationalizer.app.clone=
background_task.sub.type.rationalizer.app.delete=
background_task.sub.type.rationalizer.dashboard.report=
background_task.sub.type.rationalizer.delete.brand=
background_task.sub.type.rationalizer.document.report=
background_task.sub.type.rationalizer.ingest.and.upload.xml=
background_task.sub.type.rationalizer.metadata.report=
background_task.sub.type.rationalizer.migrate.data=
background_task.sub.type.rationalizer.recompute.brand=
background_task.sub.type.rationalizer.reindex=
background_task.sub.type.rationalizer.smilarity.report=
background_task.sub.type.rationalizer.upload.xml=
background_task.sub.type.resave.content=
background_task.sub.type.revert.content.power.edit=
background_task.sub.type.roles.export=
background_task.sub.type.save.content.power.edit=
background_task.sub.type.send.to.qa.module=
background_task.sub.type.sftprepo.contentlib.import=
background_task.sub.type.smart.canvas.variants.export=
background_task.sub.type.smart.text.variants.export=
background_task.sub.type.smart.text.variants.import=
background_task.sub.type.toouchpoint.variants.export=
background_task.sub.type.toouchpoint.variants.import=
background_task.sub.type.tp.clone=
background_task.sub.type.tp.container.import=
background_task.sub.type.tp.delete=
background_task.sub.type.tp.export=
background_task.sub.type.tp.hide.commit=
background_task.sub.type.tp.hide.update=
background_task.sub.type.tp.import.to.update=
background_task.sub.type.tp.import=
background_task.sub.type.tp.project=
background_task.sub.type.tp.sync.commit=
background_task.sub.type.tp.sync.msg.priority.commit=
background_task.sub.type.tp.sync.msg.priority.update=
background_task.sub.type.tp.sync.report=
background_task.sub.type.tp.sync.update=
background_task.sub.type.upload.xml.tags=
background_task.sub.type.users.export=
background_task.sub.type.users.import=
background_task.sub.type.users.update.export=
background_task.sub.type.variant.metadata.export=
background_task.sub.type.variant.metadata.import=
blue.relay.response.status.ok=
blue.relay.task.completed=
blue.relay.task.starting=
blue.relay.test.scenario.no.result=
client_message.zone.template.title=
client_messages.alert.danger=
client_messages.alert.info=
client_messages.alert.success=
client_messages.alert.warning=
client_messages.consolidate_frame_title=
client_messages.content.no_production_content=
client_messages.content_editor.EMPTY=
client_messages.content_editor.alignment_abv=
client_messages.content_editor.alignment_center=
client_messages.content_editor.alignment_full=
client_messages.content_editor.alignment_left=
client_messages.content_editor.alignment_right=
client_messages.content_editor.alt_text=
client_messages.content_editor.apply=
client_messages.content_editor.attributes=
client_messages.content_editor.bold=
client_messages.content_editor.brand=
client_messages.content_editor.canvas_panel=
client_messages.content_editor.canvas_size=
client_messages.content_editor.centimeter_short=
client_messages.content_editor.clipboard=
client_messages.content_editor.clipboard_filter.any=
client_messages.content_editor.clipboard_filter.forms=
client_messages.content_editor.clipboard_filter.freeform=
client_messages.content_editor.clipboard_filter.lists=
client_messages.content_editor.clipboard_filter.tables=
client_messages.content_editor.color=
client_messages.content_editor.confirm=
client_messages.content_editor.confirm_remove_content=
client_messages.content_editor.content_duplicates_and_similarities=
client_messages.content_editor.content_intelligence=
client_messages.content_editor.content_language=
client_messages.content_editor.content_menu=
client_messages.content_editor.custom_value=
client_messages.content_editor.decrease_max_items=
client_messages.content_editor.default_content_compare=
client_messages.content_editor.default_value=
client_messages.content_editor.desired_range=
client_messages.content_editor.dropdown=
client_messages.content_editor.duplicates_and_similarities=
client_messages.content_editor.edit_image_properties=
client_messages.content_editor.edit_list_properties=
client_messages.content_editor.edit_paragraph_properties=
client_messages.content_editor.editable_content_properties=
client_messages.content_editor.elements=
client_messages.content_editor.error_min_canvas_size=
client_messages.content_editor.error_min_trim_size=
client_messages.content_editor.flesch_kincaid_grade_level=
client_messages.content_editor.flesch_readability=
client_messages.content_editor.good=
client_messages.content_editor.hide_toolbar=
client_messages.content_editor.history=
client_messages.content_editor.image=
client_messages.content_editor.image_alt_text=
client_messages.content_editor.image_properties=
client_messages.content_editor.inches_short=
client_messages.content_editor.indent=
client_messages.content_editor.inline_content_only=
client_messages.content_editor.insert_alt_text=
client_messages.content_editor.insert_anchor=
client_messages.content_editor.insert_bookmark=
client_messages.content_editor.insert_edit_alt_text=
client_messages.content_editor.insert_edit_anchor=
client_messages.content_editor.insert_edit_barcode=
client_messages.content_editor.insert_edit_bookmark=
client_messages.content_editor.insert_edit_link=
client_messages.content_editor.insert_link=
client_messages.content_editor.insert_placeholders=
client_messages.content_editor.invalid_table_relative_widths=
client_messages.content_editor.italic=
client_messages.content_editor.line_spacing_abv=
client_messages.content_editor.list=
client_messages.content_editor.list_styles=
client_messages.content_editor.local_content=
client_messages.content_editor.local_image_library=
client_messages.content_editor.local_smart_canvas=
client_messages.content_editor.lookup_expressions=
client_messages.content_editor.menu=
client_messages.content_editor.menu_properties=
client_messages.content_editor.mixed=
client_messages.content_editor.my_items=
client_messages.content_editor.negative=
client_messages.content_editor.neutral=
client_messages.content_editor.no_clipboard_items_to_display=
client_messages.content_editor.no_menu_items_to_display=
client_messages.content_editor.on_flow=
client_messages.content_editor.outdent=
client_messages.content_editor.pin_menu=
client_messages.content_editor.placeholders=
client_messages.content_editor.point_size_abv=
client_messages.content_editor.poor=
client_messages.content_editor.positive=
client_messages.content_editor.possible_violations_found=
client_messages.content_editor.readability=
client_messages.content_editor.readability_analysis=
client_messages.content_editor.referencing_local_image_library=
client_messages.content_editor.remove_link=
client_messages.content_editor.render_content=
client_messages.content_editor.repo_last_sync_date=
client_messages.content_editor.repo_last_update_date=
client_messages.content_editor.reset=
client_messages.content_editor.reset_workspace=
client_messages.content_editor.rotate_left=
client_messages.content_editor.rotate_right=
client_messages.content_editor.rulers=
client_messages.content_editor.save_content_changes=
client_messages.content_editor.save_required=
client_messages.content_editor.select_an_option=
client_messages.content_editor.sentiment=
client_messages.content_editor.sentiment_analysis=
client_messages.content_editor.sentiment_score=
client_messages.content_editor.shared_items=
client_messages.content_editor.show_variable_sample_values=
client_messages.content_editor.smart_canvas=
client_messages.content_editor.special_character=
client_messages.content_editor.submit_button=
client_messages.content_editor.submit_button_properties=
client_messages.content_editor.system=
client_messages.content_editor.tab_order=
client_messages.content_editor.table_spacing=
client_messages.content_editor.table_width=
client_messages.content_editor.tables=
client_messages.content_editor.tag_panel=
client_messages.content_editor.tags.bulleted_list=
client_messages.content_editor.tags.cell=
client_messages.content_editor.tags.checkbox=
client_messages.content_editor.tags.freeform_container=
client_messages.content_editor.tags.list_item=
client_messages.content_editor.tags.local_smart_text=
client_messages.content_editor.tags.menu=
client_messages.content_editor.tags.ordered_list=
client_messages.content_editor.tags.paragraph=
client_messages.content_editor.tags.radio=
client_messages.content_editor.tags.row=
client_messages.content_editor.tags.smart_text=
client_messages.content_editor.tags.submit_button=
client_messages.content_editor.tags.table=
client_messages.content_editor.tags.text_field=
client_messages.content_editor.targeting=
client_messages.content_editor.text=
client_messages.content_editor.text_date_field=
client_messages.content_editor.text_date_field_properties=
client_messages.content_editor.text_field_value=
client_messages.content_editor.toggle_panel_size=
client_messages.content_editor.toggle_table_grid_drag=
client_messages.content_editor.tooltips=
client_messages.content_editor.underline=
client_messages.content_editor.variants=
client_messages.content_editor.view_fullscreen=
client_messages.file_extension_not_supported=
client_messages.image.upload.current.image=
client_messages.image.upload.drag.and.drop.browse=
client_messages.image.upload.drag.and.drop.title=
client_messages.items_selected=
client_messages.label.toggle_columns=
client_messages.label.toggle_details=
client_messages.label.toggle_image_list=
client_messages.label.toggle_list_display=
client_messages.onpremise.component.downloads=
client_messages.onpremise.components=
client_messages.page.text.dashboard.stats.error.message=
client_messages.page.text.dashboard.stats.inProcess.message=
client_messages.page.text.dashboard.stats.inProcess.noProgressPercent.message=
client_messages.page.text.dashboard.stats.inProcess.pending.message=
client_messages.page.text.dashboard.stats.lastUpdated.message=
client_messages.page.text.rationalizer.dashboard.stats.error.message=
client_messages.page.text.rationalizer.dashboard.stats.inProcess.message=
client_messages.page.text.rationalizer.dashboard.stats.inProcess.noProgressPercent.message=
client_messages.page.text.rationalizer.dashboard.stats.inProcess.pending.message=
client_messages.page.text.rationalizer.dashboard.stats.lastUpdated.message=
client_messages.pinc.abort=
client_messages.pinc.abort_not_permitted=
client_messages.pinc.access_key_id=
client_messages.pinc.access_key_required=
client_messages.pinc.activate=
client_messages.pinc.activate_confirmation=
client_messages.pinc.active=
client_messages.pinc.add=
client_messages.pinc.add_application=
client_messages.pinc.add_child_step=
client_messages.pinc.add_step=
client_messages.pinc.all=
client_messages.pinc.and=
client_messages.pinc.any=
client_messages.pinc.api_token=
client_messages.pinc.api_tokens=
client_messages.pinc.app_ver_archive_not_permitted=
client_messages.pinc.application=
client_messages.pinc.application_properties=
client_messages.pinc.application_version=
client_messages.pinc.application_version_required=
client_messages.pinc.archive=
client_messages.pinc.archive_confirmation=
client_messages.pinc.archive_file=
client_messages.pinc.archived=
client_messages.pinc.attach_result=
client_messages.pinc.authentication_http_method=
client_messages.pinc.authentication_request_body=
client_messages.pinc.authentication_token_attribute=
client_messages.pinc.authentication_type=
client_messages.pinc.authentication_url=
client_messages.pinc.back_to_list=
client_messages.pinc.bcc=
client_messages.pinc.body=
client_messages.pinc.body_required=
client_messages.pinc.bucket=
client_messages.pinc.bucket_required=
client_messages.pinc.bundle_required=
client_messages.pinc.cancel=
client_messages.pinc.cc=
client_messages.pinc.channel=
client_messages.pinc.channel_required=
client_messages.pinc.channel_type_label_ftp=
client_messages.pinc.channel_type_label_gmc=
client_messages.pinc.channel_type_label_native=
client_messages.pinc.child_step=
client_messages.pinc.choose_file=
client_messages.pinc.clone=
client_messages.pinc.close=
client_messages.pinc.code=
client_messages.pinc.command_line_arguments=
client_messages.pinc.confirm=
client_messages.pinc.contains=
client_messages.pinc.continue_on_failure=
client_messages.pinc.copy_auth_token=
client_messages.pinc.created=
client_messages.pinc.current_production_version=
client_messages.pinc.deactivate=
client_messages.pinc.deactivate_confirmation=
client_messages.pinc.default=
client_messages.pinc.delete=
client_messages.pinc.delete_app_confirmation=
client_messages.pinc.delete_confirmation=
client_messages.pinc.delete_step_confirmation=
client_messages.pinc.description=
client_messages.pinc.description_required=
client_messages.pinc.details=
client_messages.pinc.disable_accessibility=
client_messages.pinc.docmill_compatible=
client_messages.pinc.download=
client_messages.pinc.driver_file=
client_messages.pinc.edit=
client_messages.pinc.encryption_key=
client_messages.pinc.encryption_keys=
client_messages.pinc.encryption_type=
client_messages.pinc.encryption_type_none=
client_messages.pinc.encryption_type_pgp=
client_messages.pinc.end_time=
client_messages.pinc.engine=
client_messages.pinc.engine_options=
client_messages.pinc.engine_options_required=
client_messages.pinc.engine_required=
client_messages.pinc.engine_version=
client_messages.pinc.engine_version_required=
client_messages.pinc.equals=
client_messages.pinc.executable=
client_messages.pinc.executable_required=
client_messages.pinc.executable_version=
client_messages.pinc.executable_version_required=
client_messages.pinc.executables=
client_messages.pinc.expiration_date=
client_messages.pinc.expired=
client_messages.pinc.failure_level=
client_messages.pinc.failure_level_error=
client_messages.pinc.failure_level_warning=
client_messages.pinc.file=
client_messages.pinc.file_contains_production_data=
client_messages.pinc.file_delete_not_permitted=
client_messages.pinc.file_encrypted_with_key=
client_messages.pinc.file_required=
client_messages.pinc.files=
client_messages.pinc.filter=
client_messages.pinc.fingerprint=
client_messages.pinc.for=
client_messages.pinc.frozen=
client_messages.pinc.host_key_md5=
client_messages.pinc.host_required=
client_messages.pinc.id=
client_messages.pinc.ignore_paths=
client_messages.pinc.in_production=
client_messages.pinc.instance=
client_messages.pinc.job=
client_messages.pinc.job_archive=
client_messages.pinc.job_result=
client_messages.pinc.job_status_aborted=
client_messages.pinc.job_status_completed=
client_messages.pinc.job_status_failed=
client_messages.pinc.job_status_initialized=
client_messages.pinc.job_status_processing=
client_messages.pinc.jobs=
client_messages.pinc.jobs_abort_confirmation=
client_messages.pinc.jobs_restart_confirmation=
client_messages.pinc.last_updated=
client_messages.pinc.last_updated_by=
client_messages.pinc.logging_level=
client_messages.pinc.logging_level_critical=
client_messages.pinc.logging_level_debug=
client_messages.pinc.logging_level_error=
client_messages.pinc.logging_level_info=
client_messages.pinc.logging_level_warning=
client_messages.pinc.message=
client_messages.pinc.messagepoint_instance=
client_messages.pinc.messagepoint_job_id=
client_messages.pinc.name=
client_messages.pinc.name_required=
client_messages.pinc.no=
client_messages.pinc.none=
client_messages.pinc.not_active=
client_messages.pinc.not_configured=
client_messages.pinc.not_in_production=
client_messages.pinc.of=
client_messages.pinc.other_parents=
client_messages.pinc.password=
client_messages.pinc.password_or_ssh_key_required=
client_messages.pinc.pgp_key_as_file=
client_messages.pinc.pgp_public_key=
client_messages.pinc.pgp_public_key_required=
client_messages.pinc.port=
client_messages.pinc.post=
client_messages.pinc.production=
client_messages.pinc.production_job_managers=
client_messages.pinc.production_test=
client_messages.pinc.production_version=
client_messages.pinc.promote=
client_messages.pinc.promote_confirmation=
client_messages.pinc.promoted=
client_messages.pinc.public_key=
client_messages.pinc.put=
client_messages.pinc.queue_size=
client_messages.pinc.reference_files=
client_messages.pinc.region=
client_messages.pinc.region_required=
client_messages.pinc.request_body_required=
client_messages.pinc.restart=
client_messages.pinc.restart_not_permitted=
client_messages.pinc.result_files=
client_messages.pinc.result_filters=
client_messages.pinc.revoke=
client_messages.pinc.revoke_confirmation=
client_messages.pinc.revoke_date=
client_messages.pinc.revoked=
client_messages.pinc.root_folder=
client_messages.pinc.save=
client_messages.pinc.scopes=
client_messages.pinc.scopes_required=
client_messages.pinc.secret_access_key=
client_messages.pinc.secret_key_required=
client_messages.pinc.select_one_or_more=
client_messages.pinc.select_step_type=
client_messages.pinc.server=
client_messages.pinc.splitting_batch_size=
client_messages.pinc.splitting_type=
client_messages.pinc.splitting_type_page=
client_messages.pinc.splitting_type_recipient=
client_messages.pinc.ssh_key=
client_messages.pinc.start_time=
client_messages.pinc.starts=
client_messages.pinc.status=
client_messages.pinc.step=
client_messages.pinc.step_type_delivery_s3=
client_messages.pinc.step_type_delivery_sftp=
client_messages.pinc.step_type_email=
client_messages.pinc.step_type_engine_de=
client_messages.pinc.step_type_engine_ede=
client_messages.pinc.step_type_engine_inspire=
client_messages.pinc.step_type_engine_mpcomp=
client_messages.pinc.step_type_engine_qepost=
client_messages.pinc.step_type_executable=
client_messages.pinc.step_type_get_bundle=
client_messages.pinc.step_type_webhook=
client_messages.pinc.steps=
client_messages.pinc.subject=
client_messages.pinc.subject_required=
client_messages.pinc.submission=
client_messages.pinc.synchronous=
client_messages.pinc.test=
client_messages.pinc.test_job_managers=
client_messages.pinc.to=
client_messages.pinc.to_required=
client_messages.pinc.token=
client_messages.pinc.tokens=
client_messages.pinc.touchpoint=
client_messages.pinc.type=
client_messages.pinc.user=
client_messages.pinc.user_data=
client_messages.pinc.username_required=
client_messages.pinc.version=
client_messages.pinc.version_required=
client_messages.pinc.versions=
client_messages.pinc.webhook_http_method=
client_messages.pinc.webhook_url=
client_messages.pinc.webhook_url_required=
client_messages.pinc.with=
client_messages.pinc.yes=
client_messages.rationalizer.dashboard.compare.all.to.all=
client_messages.rationalizer.dashboard.compare.all.to.filtered=
client_messages.rationalizer.dashboard.compare.filtered.to.all=
client_messages.rationalizer.dashboard.compare.filtered.to.filtered=
client_messages.response=
client_messages.shared_content.text=
client_messages.text.DAT_PUB_AND_ZIP=
client_messages.text.DAT_WFD_AND_ZIP=
client_messages.text.TTF=
client_messages.text._value=
client_messages.text.action=
client_messages.text.activated_on=
client_messages.text.active_copy=
client_messages.text.active_run_type=
client_messages.text.active_tasks=
client_messages.text.add_items_base16_entities=
client_messages.text.add_items_delimiter_separated_values=
client_messages.text.add_menu_item=
client_messages.text.add_to_my_clipboard=
client_messages.text.add_to_shared_clipboard=
client_messages.text.added=
client_messages.text.alt_text_value_required=
client_messages.text.application=
client_messages.text.approved=
client_messages.text.assignee=
client_messages.text.awaiting_approval=
client_messages.text.background_task=
client_messages.text.background_tasks=
client_messages.text.background_tasks_in_progress=
client_messages.text.background_tasks_notifications=
client_messages.text.barcode_value_required=
client_messages.text.barcode_width_height_required=
client_messages.text.brand_description=
client_messages.text.brand_violation=
client_messages.text.bullet_spacing=
client_messages.text.change=
client_messages.text.changed=
client_messages.text.changes=
client_messages.text.clear_selected_items=
client_messages.text.click_to_download_data_file=
client_messages.text.click_to_download_pdf=
client_messages.text.client_bundle=
client_messages.text.collection=
client_messages.text.collections=
client_messages.text.colors=
client_messages.text.communication.type=
client_messages.text.compare=
client_messages.text.compare_from=
client_messages.text.compare_report=
client_messages.text.compare_to=
client_messages.text.comparison_content=
client_messages.text.completed=
client_messages.text.compound_spaces_between=
client_messages.text.compound_spaces_lead=
client_messages.text.compress=
client_messages.text.confirm=
client_messages.text.confirm_clear_touchpoint_metadata=
client_messages.text.confirm_remove_layout=
client_messages.text.connected=
client_messages.text.connected_workflow=
client_messages.text.connector=
client_messages.text.container_dimensions=
client_messages.text.content_compare=
client_messages.text.content_good_readability=
client_messages.text.content_negative_sentiment=
client_messages.text.content_neutral_sentiment=
client_messages.text.content_poor_readability=
client_messages.text.content_positive_sentiment=
client_messages.text.content_status=
client_messages.text.continue_numbering=
client_messages.text.create_sub_project=
client_messages.text.create_touchpoint=
client_messages.text.created_by=
client_messages.text.created_on=
client_messages.text.current=
client_messages.text.custom=
client_messages.text.dashboard_brand_number=
client_messages.text.dashboard_compare_title=
client_messages.text.dashboard_duplicates_number_gt_zero=
client_messages.text.dashboard_duplicates_number_eq_zero=
client_messages.text.dashboard_duplicates_report=
client_messages.text.dashboard_metadata_number=
client_messages.text.global_dashboard_similarities_number=
client_messages.text.rationalizer_dashboard_similarities_number=
client_messages.text.dashboard_similarity_report=
client_messages.text.data=
client_messages.text.date_and_description=
client_messages.text.date_range=
client_messages.text.default_node=
client_messages.text.delivery_event_requester=
client_messages.text.dependencies=
client_messages.text.description=
client_messages.text.diagnostic_bundle=
client_messages.text.dictionary=
client_messages.text.disqualify_on_no_=
client_messages.text.disqualify_on_no_value=
client_messages.text.disqualify_smart_text=
client_messages.text.documentLink=
client_messages.text.document_name=
client_messages.text.domain.licence.type=
client_messages.text.double_click_to_add=
client_messages.text.download=
client_messages.text.download_error=
client_messages.text.drag_and_drop=
client_messages.text.drag_and_drop_not_applicable_for_text_style_font_upload=
client_messages.text.drop_decimals_for_round_numbers=
client_messages.text.drop_it_here=
client_messages.text.due=
client_messages.text.due_soon=
client_messages.text.dueby=
client_messages.text.duplicates_content=
client_messages.text.duplicates_found=
client_messages.text.duplicates_matches=
client_messages.text.element=
client_messages.text.enabled_validations=
client_messages.text.end_date=
client_messages.text.evelope_weight=
client_messages.text.event=
client_messages.text.exact_matches=
client_messages.text.execution.execution_times=
client_messages.text.execution.times.bundling=
client_messages.text.execution.times.connector=
client_messages.text.execution.times.dews=
client_messages.text.execution.times.overall.dews=
client_messages.text.execution.times.overall=
client_messages.text.execution.times.postprocessing=
client_messages.text.execution.times.preprocessing=
client_messages.text.execution.times.processing=
client_messages.text.execution.times.processing_report=
client_messages.text.execution.times.processing_result=
client_messages.text.execution.times.qepost=
client_messages.text.execution.times.qualification.engine=
client_messages.text.execution.times.receiving.result=
client_messages.text.execution.times.sending=
client_messages.text.execution.times.umh.finalizing=
client_messages.text.execution.times.umh.preprocessing=
client_messages.text.execution.times.waiting.for.result=
client_messages.text.execution.times.waiting.in.queue.time=
client_messages.text.expand=
client_messages.text.expression=
client_messages.text.feedback=
client_messages.text.file=
client_messages.text.file_not_found=
client_messages.text.filename.pattern=
client_messages.text.files.blocked.by.sysadmin=
client_messages.text.fix=
client_messages.text.fix_all=
client_messages.text.fixed=
client_messages.text.fixed_row=
client_messages.text.footer=
client_messages.text.footer_height=
client_messages.text.form_name_required=
client_messages.text.group_id=
client_messages.text.group_keep_together_threshold=
client_messages.text.header=
client_messages.text.header_height=
client_messages.text.height.inches=
client_messages.text.hide_changes=
client_messages.text.hide_changes_warning=
client_messages.text.homepage=
client_messages.text.how_many_content_to_add=
client_messages.text.id_provider=
client_messages.text.id_value_required=
client_messages.text.image=
client_messages.text.import_layout_from_xml=
client_messages.text.in_progress=
client_messages.text.info=
client_messages.text.insert_col_after=
client_messages.text.insert_col_before=
client_messages.text.insert_pargraph_after=
client_messages.text.insert_pargraph_before=
client_messages.text.insert_row_after=
client_messages.text.insert_row_before=
client_messages.text.insert_space_after=
client_messages.text.insert_space_before=
client_messages.text.instance.access=
client_messages.text.instance_last_login=
client_messages.text.job=
client_messages.text.job_center=
client_messages.text.job_id=
client_messages.text.keywords=
client_messages.text.label_value_required=
client_messages.text.language_code=
client_messages.text.last_login=
client_messages.text.last_run_date=
client_messages.text.legal_mark_superscripting=
client_messages.text.letter=
client_messages.text.link_value_required=
client_messages.text.list_spacing=
client_messages.text.loading_sftprepo_images=
client_messages.text.locale_code=
client_messages.text.location=
client_messages.text.log=
client_messages.text.marcie_diagnostic=
client_messages.text.margins=
client_messages.text.mark_task_complete=
client_messages.text.master=
client_messages.text.matches=
client_messages.text.max_sentence_length=
client_messages.text.merge_tables=
client_messages.text.message_workflow=
client_messages.text.messagepoint_exchange=
client_messages.text.metadata_description=
client_messages.text.metadata_matches=
client_messages.text.metadata_properties=
client_messages.text.modules=
client_messages.text.more_items=
client_messages.text.my_active_task=
client_messages.text.my_settings=
client_messages.text.name_for_text_style_font_required=
client_messages.text.new_task=
client_messages.text.next_action=
client_messages.text.no_access=
client_messages.text.no_actions=
client_messages.text.no_background_tasks=
client_messages.text.no_items_available=
client_messages.text.no_list_styles_match_search=
client_messages.text.no_list_styles_selected=
client_messages.text.no_matches_found=
client_messages.text.no_menu_value_items=
client_messages.text.no_override=
client_messages.text.no_selection=
client_messages.text.no_sftprepo_images_for_folder=
client_messages.text.no_targeting_unchecked_for_all_customers=
client_messages.text.no_tasks=
client_messages.text.no_values=
client_messages.text.none=
client_messages.text.notification.emails=
client_messages.text.object=
client_messages.text.object_assignee=
client_messages.text.object_name=
client_messages.text.ok=
client_messages.text.or=
client_messages.text.order=
client_messages.text.origin=
client_messages.text.origin_status=
client_messages.text.other_active_task=
client_messages.text.overdue=
client_messages.text.override_proof_inprocess=
client_messages.text.owner=
client_messages.text.page=
client_messages.text.parts=
client_messages.text.placeholder=
client_messages.text.point_sizes=
client_messages.text.pop_out=
client_messages.text.preferred_contraction=
client_messages.text.preferred_contractions=
client_messages.text.primary_data_file=
client_messages.text.project=
client_messages.text.project_requirement=
client_messages.text.project_status=
client_messages.text.properties=
client_messages.text.quit=
client_messages.text.rationalizer=
client_messages.text.rationalizer_documents_report=
client_messages.text.rationalizer_metadata_report=
client_messages.text.rationalizer_workflow=
client_messages.text.readability_info=
client_messages.text.reading_average=
client_messages.text.reading_content_objects=
client_messages.text.region_left=
client_messages.text.region_left_width=
client_messages.text.region_right=
client_messages.text.region_right_width=
client_messages.text.rejected=
client_messages.text.remake.hash=
client_messages.text.remake.hash_confirm.alltouchpoints=
client_messages.text.remake.hash_confirm.touchpoint=
client_messages.text.remove=
client_messages.text.remove_column=
client_messages.text.remove_item=
client_messages.text.remove_menu_item=
client_messages.text.remove_row=
client_messages.text.removed=
client_messages.text.rendering=
client_messages.text.repeat_on_flow=
client_messages.text.repeating=
client_messages.text.report=
client_messages.text.requested_by=
client_messages.text.requirement=
client_messages.text.response_date=
client_messages.text.restricted_contraction=
client_messages.text.restricted_contractions=
client_messages.text.restricted_term=
client_messages.text.restricted_terms=
client_messages.text.save_and_refresh=
client_messages.text.saving=
client_messages.text.schedule_id=
client_messages.text.section_order=
client_messages.text.select=
client_messages.text.select_all=
client_messages.text.select_all_items=
client_messages.text.select_nth_element=
client_messages.text.select_on_this_page=
client_messages.text.selector=
client_messages.text.sentiment_average=
client_messages.text.sentiment_content_objects=
client_messages.text.sentiment_info=
client_messages.text.server_bundle=
client_messages.text.shared_content=
client_messages.text.show_content=
client_messages.text.show_details=
client_messages.text.show_only_selected=
client_messages.text.sibling_status=
client_messages.text.signout=
client_messages.text.similarities=
client_messages.text.similarities_content=
client_messages.text.similarities_found=
client_messages.text.similarity_report=
client_messages.text.single_bullet_list=
client_messages.text.single_number_list=
client_messages.text.skip_to_main_content=
client_messages.text.snap_out=
client_messages.text.snap_in=
client_messages.text.source=
client_messages.text.source_status=
client_messages.text.sql_query=
client_messages.text.start_date=
client_messages.text.starting.background.task.to.calculate.hash=
client_messages.text.starting_number=
client_messages.text.statistics=
client_messages.text.stock_id=
client_messages.text.suppressed=
client_messages.text.sync=
client_messages.text.sync_with_source=
client_messages.text.sync_with_target=
client_messages.text.table_cell=
client_messages.text.tag=
client_messages.text.tagging_override=
client_messages.text.target_status=
client_messages.text.target_zone=
client_messages.text.task=
client_messages.text.task_assets=
client_messages.text.task_details=
client_messages.text.task_in_workflow=
client_messages.text.tasks=
client_messages.text.thresholds=
client_messages.text.toggle_footer_overrides=
client_messages.text.toggle_header_overrides=
client_messages.text.toggle_margin_overrides=
client_messages.text.toggle_region_left_overrides=
client_messages.text.toggle_region_right_overrides=
client_messages.text.toggle_repeating_cells=
client_messages.text.toggle_section_order_override=
client_messages.text.toggle_section_overrides=
client_messages.text.toggle_zone_overrides=
client_messages.text.touchpoint=
client_messages.text.touchpoint_exchange=
client_messages.text.touchpoint_export_config=
client_messages.text.touchpoints=
client_messages.text.unable_to_generate_proof_view_log=
client_messages.text.update=
client_messages.text.updated_by=
client_messages.text.url=
client_messages.text.user_menu_actions=
client_messages.text.variant=
client_messages.text.version=
client_messages.text.view_AFP=
client_messages.text.view_tasks=
client_messages.text.warning_object_change_dynamic=
client_messages.text.warning_object_change_structured=
client_messages.text.widgets=
client_messages.text.width.inches=
client_messages.text.working_copy=
client_messages.text.x_inches=
client_messages.text.y_inches=
client_messages.text.yes_continue=
client_messages.text.you_are_almost_done=
client_messages.title.actions=
client_messages.title.add_content=
client_messages.title.add_layout=
client_messages.title.add_project=
client_messages.title.add_symbol=
client_messages.title.add_task=
client_messages.title.add_tasks=
client_messages.title.additional.information=
client_messages.title.are_you_sure_you_want_to_leave_this_page=
client_messages.title.background_processes=
client_messages.title.bulk_delete=
client_messages.title.clear_metadata=
client_messages.title.content_compare=
client_messages.title.content_history=
client_messages.title.content_metadata=
client_messages.title.content_metadata_template=
client_messages.title.content_targeting=
client_messages.title.customize_locale=
client_messages.title.datarecords_compare=
client_messages.title.default_item_selection_targeting=
client_messages.title.dependencies_list=
client_messages.title.differences_list=
client_messages.title.document_metadata_template=
client_messages.title.edit_data_tag=
client_messages.title.edit_document=
client_messages.title.edit_task=
client_messages.title.edit_text_style_font=
client_messages.title.exact_matches_and_similarities=
client_messages.title.expanded_content_view=
client_messages.title.footer_row=
client_messages.title.footer_row_fixed=
client_messages.title.footer_row_repeated_on_flow=
client_messages.title.form_setup=
client_messages.title.header_row=
client_messages.title.header_row_fixed=
client_messages.title.header_row_repeated_on_flow=
client_messages.title.import.pod.domains.information=
client_messages.title.instance=
client_messages.title.list_properties=
client_messages.title.list_styles=
client_messages.title.oops=
client_messages.title.orders=
client_messages.title.override_proof=
client_messages.title.placeholder=
client_messages.title.placeholder_list_styles=
client_messages.title.placeholder_paragraph_styles=
client_messages.title.placeholder_text_styles=
client_messages.title.placeholder_visibility=
client_messages.title.rationalizer_application_visibility=
client_messages.title.rationalizer_change_structure=
client_messages.title.rationalizer_document_upload=
client_messages.title.rationalizer_touchpoint_export=
client_messages.title.referencedby.objects_list=
client_messages.title.references.objects_list=
client_messages.title.rejection_note=
client_messages.title.remove_layout=
client_messages.title.repeating_footer=
client_messages.title.repeating_header=
client_messages.title.repeating_row=
client_messages.title.request_proof=
client_messages.title.requirement_note=
client_messages.title.row_repeated_on_flow_omit_initial=
client_messages.title.row_repeated_on_flow_omit_last=
client_messages.title.section_properties=
client_messages.title.set_part_size_position=
client_messages.title.set_zone_size_position=
client_messages.title.shared_content_edit=
client_messages.title.shared_content_separate=
client_messages.title.similarity_lower_threshold=
client_messages.title.some_changes_have_not_been_saved=
client_messages.title.source_editor=
client_messages.title.style_connectors=
client_messages.title.suggested_values=
client_messages.title.summary=
client_messages.title.system_variable=
client_messages.title.tags=
client_messages.title.take_me_to=
client_messages.title.task_complete=
client_messages.title.task_in_workflow=
client_messages.title.task_reassignment=
client_messages.title.tasks=
client_messages.title.template_setup=
client_messages.title.toggle_layout_overrides=
client_messages.title.upload.users=
client_messages.title.upload.variant.metadata=
client_messages.title.user_panel=
client_messages.title.zone=
client_messages.title.zone_list_styles=
client_messages.title.zone_references=
code.text.authentication.failure=
code.text.company.redirection.pod.deactivated=
code.text.company.redirection.pod.offline=
code.text.no.web.service.permission=
code.text.password.invalid.redirection.info=
code.text.password.redirection.error=
code.text.password.redirection.info.missing=
code.text.password.redirection.pod.unavailable=
code.text.password.transition.redirection.info.missing=
code.text.security.settings.username.password.invalid=
code.text.soft.deactivated.error=
email.content.body.instance.branch.name=
email.content.body.intro=
email.content.company.and.instnace.name=
email.content.deliveryevent.failed=
email.content.footer=
email.content.label.touchpoint=
email.content.notifications.are.generated=
email.content.project.complete=
email.content.project.create=
email.content.project.overdue=
email.content.socialize.proof=
email.content.sso.user.activate=
email.content.task.create=
email.content.task.near.term=
email.content.task.overdue=
email.content.task.reassign=
email.digest.subject.your.messagepoint.notification=
email.digest.subject.your.messagepoint.notifications=
email.subheader.activated=
email.subheader.deliveryevent.failed=
email.subheader.message.activated=
email.subheader.message.working.copy.created=
email.subheader.password.change.notification=
email.subheader.password.forget=
email.subheader.password.reset.activate=
email.subheader.password.reset=
email.subheader.project.assigned.to.you=
email.subheader.project.complete=
email.subheader.project.create=
email.subheader.project.near.due=
email.subheader.project.overdue=
email.subheader.socialize.proof=
email.subheader.sso.user.activate=
email.subheader.system.task.create=
email.subheader.task.assigned=
email.subheader.task.create=
email.subheader.task.near.term=
email.subheader.task.overdue=
email.subheader.task.reassign=
email.subheader.workflow.approval.individual=
email.subheader.workflow.approval=
email.subheader.workflow.approved.individual=
email.subheader.workflow.approved=
email.subheader.workflow.approvedbyother.individual=
email.subheader.workflow.approvedbyother=
email.subheader.workflow.duebyautoapprove.individual=
email.subheader.workflow.duebyautoapprove=
email.subheader.workflow.duebynotify.individual=
email.subheader.workflow.duebynotify=
email.subheader.workflow.reassigned.individual=
email.subheader.workflow.reassigned=
email.subheader.workflow.rejected.individual=
email.subheader.workflow.rejected=
email.subheader.workflow.translation.individual=
email.subheader.workflow.translation=
email.subheader.working.copy.created=
email.subject.activated=
email.subject.deliveryevent.failed=
email.subject.message.activated=
email.subject.message.working.copy.created=
email.subject.project.complete=
email.subject.project.create=
email.subject.project.overdue=
email.subject.socialize.proof=
email.subject.sso.user.activate=
email.subject.task.create=
email.subject.task.near.term=
email.subject.task.overdue=
email.subject.task.reassign=
email.subject.workflow.approval=
email.subject.workflow.approved=
email.subject.workflow.approvedbyother=
email.subject.workflow.duebyautoapprove=
email.subject.workflow.duebynotify=
email.subject.workflow.reassigned=
email.subject.workflow.rejected=
email.subject.workflow.translation=
email.subject.working.copy.created=
email.subject.your.action.required=
email.workflow.approval.content=
error.aggregation.level.must.match.for.repeating.variables=
error.assets.local.global.reference.other.local.assets=
error.assets.local.global.referenced.by.other.global.assets=
error.assets.make.global.global.working.copy.exists=
error.assets.make.global.local.working.copy.exists=
error.assets.make.global.no.local.active.copy=
error.assets.make.local.image.library.references.another.image=
error.assets.make.local.local.asset.exists=
error.assets.make.local.no.global.active.copy=
error.assets.make.local.no.variant.selected=
error.assets.move.to.local.global.asset.local.reference=
error.assets.move.to.local.global.asset.touchpoint.reference=
error.assets.pull.image.library.references.another.image=
error.assets.pull.local.working.copy.exists=
error.assets.pull.no.global.active.copy=
error.assets.push.global.working.copy.exists=
error.assets.push.no.local.active.copy=
error.blue.relay.folder.id=
error.blue.relay.response.status.not.ok=
error.bundle.delivery.connection.required=
error.bundle.delivery.filename.pattern.must.be.entered=
error.bundle.delivery.invalid.server=
error.bundle.delivery.name.must.be.entered=
error.bundle.delivery.name.must.be.unique=
error.bundle.delivery.password.required=
error.bundle.delivery.port.must.be.entered=
error.bundle.delivery.sshkeys.file.upload.required=
error.bundle.delivery.sshkeys.invalid.privatekey=
error.bundle.delivery.url.must.be.entered=
error.bundle.delivery.username.required=
error.communication.activation.without.proof=
error.communication.approval.without.proof=
error.communication.mandatory.values=
error.communication.must.assign.valid.workflow=
error.communication.order.value.is.used=
error.connection.refused=
error.connection.timeout=
error.data.groups.must.match.for.repeating.variables=
error.delimiter.can.not.be.empty=
error.document.composition.version.is.not.available=
error.file.doesnt.exist=
error.file.doesnt.match.required.pod=
error.file.missing.domains.info=
error.file.should.include.master.pod=
error.global.asset.not.accessible=
error.global.asset.not.visible.to.approver=
error.id.mandatory.and.has.to.be.greater.than.0=
error.order.mandatory.and.has.to.be.greater.than.0=
error.idp.provider.type.is.not.valid=
error.import.file.touchpoint.different.dna=
error.improper.selector.configuration=
error.input.invalid.locale=
error.insert.id.notfound=
error.insertschedule.cannot.release.for.approval.selectors.combination.is.in.use.by.another.schedule.for.list=
error.insertschedule.cannot.release.for.approval.selectors.combination.is.in.use.by.another.schedule=
error.invalid.email=
error.level.not.set.for.row.variable=
error.level.not.set.for.variable=
error.level.of.variable.greater.than.zone.level=
error.local.content.is.being.referenced.on.archive.delete=
error.local.content.is.being.referenced.on.discard.working.copy=
error.local.image.is.being.referenced.on.archive=
error.lookup.table.file.structure.changed=
error.lookup.table.file.structure.column=
error.lookup.table.file.structure.values=
error.lookup.table.is.being.referenced.on.archive.delete=
error.lookup.table.is.being.referenced.on.archive=
error.message.admin.securitySettings.hardDeactivationLimitDays.mustgreaterthan.softdeactivationdays=
error.message.admin.securitySettings.hardDeactivationLimitDays.mustgreaterthanone=
error.message.admin.securitySettings.softDeactivationLimitDays.mustgreaterthanone=
error.message.block.content.not.permitted.for.inline=
error.message.can.not.mix.part.graphic.types.and.restrict.library.assets=
error.message.cannot.delete.reference.styles=
error.message.cannot.delete.referenced.alternate=
error.message.cannot.delete.referenced.data.files=
error.message.cannot.delete.referenced.data.resources=
error.message.cannot.delete.referenced.liststyles=
error.message.cannot.delete.referenced.tasks=
error.message.cannot.delete.referenced.workflows=
error.message.cannot.delete.referenced.zones=
error.message.cannot.delete.variant.active.copy.is.referenced=
error.message.cannot.delete.workflow.engaged.tasks=
error.message.cannot.reassign.asset.currently.being.edited.parameterized=
error.message.cannot.reassign.asset.currently.being.edited=
error.message.composition.package.must.be.uploaded.to.run.connected.bundle=
error.message.composition.package.must.be.uploaded.to.update.connected.bundle=
error.message.conflicting.inline.content=
error.message.connected.close.tab.to.exit=
error.message.connected.portal.cannot.edit.active.order=
error.message.connected.portal.missing.order.guid=
error.message.connected.portal.missing.touchpoint.guid=
error.message.connected.portal.no.reassign.perm=
error.message.connected.portal.order.assigned.to.another.user=
error.message.connected.portal.unknown.connectors=
error.message.connected.portal.unknown.touchpoint=
error.message.connected.portal.unknown.user=
error.message.connected.touchpoint.not.visible.to.user=
error.message.connected.zone.cannot.be.freeform.or.forms=
error.message.content.library.select.an.image=
error.message.content.not.valid=
error.message.content.required=
error.message.contenttype.only.print.documents.apply.markup=
error.message.date.parameter.must.be.lowest.level.in.group=
error.message.default.value.is.required.with.unique.value=
error.message.default.value.not.match.validation.type=
error.message.dictionary.not.available=
error.message.dictionary.user.not.available=
error.message.domain.controller.instance.role.missing=
error.message.domain.not.matching.or.doesnot.existing=
error.message.duplicate.layout.name=
error.message.duplicate.project.name=
error.message.ecatalog.not.matching=
error.message.end.date.must.be.after.start.date=
error.message.exceed.maximum.licence.level.of.home.domain.level=
error.message.exceed.maximum.number.of.connected.users=
error.message.exceed.maximum.number.of.workflow.users=
error.message.expected.object.not.matching=
error.message.fail.to.create.user=
error.message.given.instance.not.existing=
error.message.guid.missing=
error.message.hardware.key.required=
error.message.has.global.sync.target.parameter.group.cannot.be.changed=
error.message.has.global.sync.target.type.cannot.be.changed=
error.message.has.local.sync.target.parameter.group.cannot.be.changed=
error.message.has.local.sync.target.type.cannot.be.changed=
error.message.invalid.guid=
error.message.invalid.parent=
error.message.invalid.selection.data=
error.message.invalid.test.param=
error.message.label.required=
error.message.label.unique=
error.message.language.must.be.selected.for.translation.step=
error.message.language.must.be.used.in.all.touchponts=
error.message.line.spacing.values.required=
error.message.link.required=
error.message.link.valid.url=
error.message.lookuptable.referenced.cannot.restrict=
error.message.metadata.template.required.connector.value=
error.message.metadata.template.requires.at.least.one.input=
error.message.model.not.equal=
error.message.model.not.exsiting=
error.message.model.not.matching=
error.message.must.select.task.object=
error.message.must.select.task.workflow=
error.message.namerequired.for.selector=
error.message.order.digital.proof.email=
error.message.order.entry.display.criteria.required=
error.message.order.entry.unique.connector.values.required=
error.message.order.socialization.email.address.invalid=
error.message.parameter.cannot.disabled.when.referenced=
error.message.parameter.group.not.valid.with.selected.message=
error.message.parameter.groups.not.data.model.compatible=
error.message.parameter.groups.not.have.same.num.of.parameters=
error.message.rationalizer.ingestion.upload.ignore=
error.message.rationalizer.ingestion.upload.manifest.wrong.extension=
error.message.rationalizer.ingestion.upload.manifest.wrong.location=
error.message.rationalizer.ingestion.upload.manifest.wrong.name=
error.message.rationalizer.ingestion.upload.no.files=
error.message.rationalizer.ingestion.upload.no.manifest=
error.message.rationalizer.ingestion.upload.replace=
error.message.rationalizer.ingestion.upload.unique=
error.message.reference.source.required.for.reference.file.type=
error.message.right.bottom.range=
error.message.right.top.range=
error.message.role.is.not.valid.on.the.instance=
error.message.selectavariant=
error.message.selector.name.is.used.by.another.selector=
error.message.spacing.bottom.range=
error.message.spacing.left.range=
error.message.spacing.right.range=
error.message.spacing.top.range=
error.message.style.customization.tagging.override.format=
error.message.tagging.override.format=
error.message.text.style.color.required=
error.message.text.style.name.cannot.start.with.number=
error.message.text.style.name.pattern=
error.message.text.style.point.size.required=
error.message.text.style.tagging.override.format=
error.message.text.styles.cannot.wrap.lists=
error.message.touchpoint.message.content.type.not.compatible=
error.message.touchpoint.message.zone.imagefile.not.compatible=
error.message.touchpoint.must.be.selected=
error.message.touchpoint.or.collection.must.be.selected=
error.message.touchpoint.variation.not.enabled=
error.message.touchpoint.zone.data.group.missing=
error.message.ttf.font.mandatory=
error.message.variable.type.cannot.be.modified.when.referenced=
error.message.variables.in.content.not.data.model.compatible=
error.message.xml.parse_failed=
error.message.zip.does.not.contain.valid.composition.file=
error.message.zone.connector.not.set=
error.message.zone.default.canvas.width.required=
error.message.zone.does.not.exist=
error.message.zone.invalid.image.assigned.for.graphic.type=
error.message.zone.must.be.selected=
error.metadata.brand.profile.is.being.referenced.on.delete=
error.metadata.form.definition.is.being.referenced.on.delete=
error.metadata.form.item.value.is.used=
error.metadata.point.of.interest.label=
error.metadata.point.of.interest.value=
error.name.already.existing.name=
error.no.data.group.set.for.row.variable=
error.no.data.group.set.for.variable=
error.no.data.group.set.for.zone=
error.node.name.cannot.contain.anyspaces=
error.node.schema.guid.must.be.entered=
error.node.schema.guid.must.be.unique=
error.not.found=
error.one.or.more.variables.used.in.this.asset.are.not.mapped.properly=
error.parse.file=
error.pinc.server.https.messagepointpinc.subdomain.required=
error.pod.web.services.user.must.be.entered=
error.pod.web.services.user.pwd.must.be.entered=
error.previous.selected.version=
warning.connector.selection=
error.project.at.least.one.task=
error.project.task.at.least.one.step=
error.rationalizer.order.number.required=
error.rationalizer.query.search.value.required=
error.rationalizer.remove.content.from.shared.asset.before.adding=
error.rationalizer.select.at.least.one.content.item=
error.section.dimensions.invalid=
error.touchpoint.has.child.versions.cannot.be.removed=
error.touchpointselection.selection.name.already.exists.name=
error.touchpointselection.selectors.add.selection.not.valid=
error.upload.file=
error.user.account.notfound=
error.workflow.approvals.at.least.one.approval.step.for.project.workflow=
error.workflow.approvals.user.cannot.be.both.owner.and.translator=
error.workflow.assignment.workflow.must.be.assignment.to.master=
error.workflow.name.must.be.unique=
error.workflow.translation.cannot.apply.to.variant.workflow.touchpoint=
error.workflow.translator.needed.for.each.workflow.step=
info.message.rationalizer.upload.file.contains.no.xml=
info.message.rationalizer.upload.file.contains.xml=
info.message.rationalizer.upload.file.received=
licence.label.ECatalog=
licence.label.LookupTable=
licence.label.MessagepointPINC=
licence.label.NumberOfConnectedUsers=
licence.label.SFMCJBConnector=
licence.label.SefasConnector=
licence.label.TransactionalAPI=
licence.label.sorting.ECatalog=
licence.label.sorting.LookupTable=
licence.label.sorting.MessagepointPINC=
licence.label.sorting.NumberOfConnectedUsers=
licence.label.sorting.SFMCJBConnector=
licence.label.sorting.SefasConnector=
licence.label.sorting.TransactionalAPI=
page.flow.release=
page.label.AVAILABLE.LIST.STYLES=
page.label.CLEAR=
page.label.DEDigital=
page.label.DEServer=
page.label.DEWS=
page.label.EMPTY=
page.label.INSERT.AS.PARAGRAPH=
page.label.MARCIE=
page.label.METADATA=
page.label.MPComp=
page.label.MPComposer=
page.label.NO.VALUE=
page.label.PINC=
page.label.Proof=
page.label.SMART.TEXT.DYNAMIC.CONTENT=
page.label.SMART.TEXT=
page.label.UPLOAD=
page.label.VARIABLE.CONTENT.ENABLED=
page.label.XML.data.tag=
page.label.abbr.ascending=
page.label.abbr.descending=
page.label.accept=
page.label.access=
page.label.accessibility.do.not.read=
page.label.accessibility=
page.label.account=
page.label.action.history=
page.label.activate.all=
page.label.add.and.save=
page.label.add.bundle.delivery=
page.label.add.channel=
page.label.add.child=
page.label.add.condition=
page.label.add.content=
page.label.add.de.server=
page.label.add.dictionary=
page.label.add.document=
page.label.add.font=
page.label.add.form=
page.label.add.image.library=
page.label.add.list.style=
page.label.add.local.image=
page.label.add.local.smart.canvas=
page.label.add.local.smart.text=
page.label.add.lookup.table=
page.label.add.placeholder=
page.label.add.project.workflow=
page.label.add.project=
page.label.add.query=
page.label.add.smart.canvas=
page.label.add.template=
page.label.add.test.communication=
page.label.add.test.suite=
page.label.add.value=
page.label.add.workflow=
page.label.added.content=
page.label.admin.email.notification.settings.update=
page.label.admin.security.settings.hard.deactivation.enabled=
page.label.admin.security.settings.hard.deactivation.limit.days=
page.label.admin.security.settings.soft.deactivation.enabled=
page.label.admin.security.settings.soft.deactivation.limit.days=
page.label.admin.systemproperties.allow_upload_filetypes=
page.label.admin.systemproperties.analytics.amplitude.api.key=
page.label.admin.systemproperties.analytics.amplitude.enabled=
page.label.admin.systemproperties.analytics.fullstory.enabled=
page.label.admin.systemproperties.bundle.delivery.post.process.scripts.folder=
page.label.admin.systemproperties.composition_engines_dir=
page.label.admin.systemproperties.content.delivery.network.domain=
page.label.admin.systemproperties.content.delivery.network.enabled=
page.label.admin.systemproperties.content.delivery.network=
page.label.admin.systemproperties.delivery_event_support_notification=
page.label.admin.systemproperties.dictionary_dir=
page.label.admin.systemproperties.disallow_upload_filetypes=
page.label.admin.systemproperties.document_history_dir=
page.label.admin.systemproperties.marcie_bootstrap_api_key=
page.label.admin.systemproperties.marcie_bootstrap_endpoint=
page.label.admin.systemproperties.marcie_content_compare_endpoint=
page.label.admin.systemproperties.marcie_enable_content_compare=
page.label.admin.systemproperties.marcie_readability_endpoint=
page.label.admin.systemproperties.marcie_sentence_splitter_endpoint=
page.label.admin.systemproperties.marcie_sentiment_endpoint=
page.label.admin.systemproperties.metadata_report_dir=
page.label.admin.systemproperties.rationalizer_files=
page.label.admin.systemproperties.target.readability=
page.label.admin.systemproperties.task_log_dir=
page.label.admin.systemproperties.web.assets.service.url=
page.label.advanced.composition=
page.label.algorithm=
page.label.align=
page.label.all.actions=
page.label.all.assignees=
page.label.all.object.types=
page.label.all.of.these=
page.label.all.owners=
page.label.all.tasks=
page.label.all.touchpoints=
page.label.all.workgroups=
page.label.allow.edit.default.language=
page.label.allow.images=
page.label.alt.text=
page.label.alternate.layout.assignment=
page.label.alternative.text=
page.label.alternative_text=
page.label.anchor=
page.label.anonymization.type.custom=
page.label.any.date=
page.label.any.of.these=
page.label.any.or.no.placeholder=
page.label.any.placeholder=
page.label.any=
page.label.applicable.variants=
page.label.application.download.log=
page.label.application.metatags=
page.label.application.navigation=
page.label.application.not.sync=
page.label.application.report=
page.label.application.sync.status.never.synchronized=
page.label.application.sync.status.server.down=
page.label.application.sync.status.synchronisation.in.progress=
page.label.application.sync.status.synchronized=
page.label.application.visibility=
page.label.apply.cell.padding=
page.label.apply.compound.space.detection=
page.label.apply.notication.digest=
page.label.apply.preferred.contractions=
page.label.apply.restricted.contractions=
page.label.apply.restricted.terms=
page.label.apply.sentence.max.length=
page.label.apply.table.spacing=
page.label.apply.text.style.font.toggle=
page.label.approve.workflow.translation=
page.label.are.ranked.by.similarity=
page.label.assets=
page.label.assignee.s=
page.label.assignee=
page.label.assignees=
page.label.attachment.deleted=
page.label.attribute.active.copy.hash=
page.label.attribute.active.copy=
page.label.attribute.advanced=
page.label.attribute.aggregation.level=
page.label.attribute.aggregation.operator=
page.label.attribute.applicable.variants=
page.label.attribute.archived.copy.hash=
page.label.attribute.archived.copy=
page.label.attribute.canvas.max.height=
page.label.attribute.canvas.max.width=
page.label.attribute.canvas.trim.height=
page.label.attribute.canvas.trim.width=
page.label.attribute.columnar.indicators=
page.label.attribute.comments=
page.label.attribute.compound.variable.format.type=
page.label.attribute.condition.search.value=
page.label.attribute.condition.type=
page.label.attribute.connected.interview.enabled=
page.label.attribute.connected.interview.orders=
page.label.attribute.connected.interview.variant.selection=
page.label.attribute.connector.parameter=
page.label.attribute.connector=
page.label.attribute.content.hash=
page.label.attribute.content.selection.type=
page.label.attribute.content.specialization.type=
page.label.attribute.content.trim.type=
page.label.attribute.content.type=
page.label.attribute.customer.driver.file=
page.label.attribute.customer.primary.key=
page.label.attribute.data.element.name=
page.label.attribute.data.group.name=
page.label.attribute.data.privacy=
page.label.attribute.data.records=
page.label.attribute.data_collection=
page.label.attribute.data_element.anonymization_mask=
page.label.attribute.data_element.anonymization_max_length=
page.label.attribute.data_element.anonymization_min_length=
page.label.attribute.data_element.anonymization_type=
page.label.attribute.data_element.anonymized=
page.label.attribute.data_element.data_group=
page.label.attribute.data_element.data_record=
page.label.attribute.data_element.data_sub_type=
page.label.attribute.data_element.data_type=
page.label.attribute.data_element.decimal_places=
page.label.attribute.data_element.external_format_text=
page.label.attribute.data_element.external_id=
page.label.attribute.data_element.length=
page.label.attribute.data_element.name=
page.label.attribute.data_element.start_location=
page.label.attribute.data_element.variable_external_id=
page.label.attribute.data_element=
page.label.attribute.data_groups=
page.label.attribute.data_record.break_indicator=
page.label.attribute.data_record.data_group=
page.label.attribute.data_record.data_groups=
page.label.attribute.data_record.enabled=
page.label.attribute.data_record.indicator=
page.label.attribute.data_record.position=
page.label.attribute.data_record.repeating=
page.label.attribute.data_record.start_customer=
page.label.attribute.data_source=
page.label.attribute.default.value=
page.label.attribute.delimited.indicators=
page.label.attribute.delimiter=
page.label.attribute.delivery.type=
page.label.attribute.description=
page.label.attribute.display.criteria=
page.label.attribute.dna=
page.label.attribute.driver.aggregation.operator=
page.label.attribute.enabled_for_connected=
page.label.attribute.enabled_for_content=
page.label.attribute.enabled_for_rules=
page.label.attribute.encoding.type=
page.label.attribute.end.date=
page.label.attribute.excluded.target.groups.relationship=
page.label.attribute.excluded.target.groups.search.value=
page.label.attribute.excluded.target.groups=
page.label.attribute.expression=
page.label.attribute.extended.target.groups.relationship=
page.label.attribute.extended.target.groups.search.value=
page.label.attribute.extended.target.groups=
page.label.attribute.external.id=
page.label.attribute.field.max.length=
page.label.attribute.field.size=
page.label.attribute.file.name=
page.label.attribute.flow.type=
page.label.attribute.form.name=
page.label.attribute.friendly.name=
page.label.attribute.global.image.dna=
page.label.attribute.global.image.name=
page.label.attribute.global.smart.text.dna=
page.label.attribute.global.smart.text.name=
page.label.attribute.graphic.type=
page.label.attribute.hash=
page.label.attribute.headers=
page.label.attribute.included.target.groups.relationship=
page.label.attribute.included.target.groups.search.value=
page.label.attribute.included.target.groups=
page.label.attribute.input.validation=
page.label.attribute.input_character_encoding=
page.label.attribute.insert.as.block=
page.label.attribute.instance.content.hash=
page.label.attribute.instance.hash=
page.label.attribute.is.confirmation.needed=
page.label.attribute.is.indicator=
page.label.attribute.is.local=
page.label.attribute.is.mandatory=
page.label.attribute.is.one.of.separator=
page.label.attribute.is.primary=
page.label.attribute.is.reference.variable=
page.label.attribute.keep.together=
page.label.attribute.layout.type=
page.label.attribute.level=
page.label.attribute.linked.message.dna=
page.label.attribute.linked.message.name=
page.label.attribute.local.content.delivery.type=
page.label.attribute.lookup.values=
page.label.attribute.lookup_table=
page.label.attribute.menu.items=
page.label.attribute.metadata.form.name=
page.label.attribute.metatags=
page.label.attribute.model.content.hash=
page.label.attribute.model.hash=
page.label.attribute.name=
page.label.attribute.new.comment=
page.label.attribute.on.hold=
page.label.attribute.owning.touchpoint.variant.dna=
page.label.attribute.owning.touchpoint.variant.name=
page.label.attribute.parameter.group.name=
page.label.attribute.parameter.group=
page.label.attribute.parameter=
page.label.attribute.parent.item=
page.label.attribute.primary.compoundkey=
page.label.attribute.primary.data.source=
page.label.attribute.primary.variable=
page.label.attribute.priority=
page.label.attribute.ready.for.approval=
page.label.attribute.reference.compoundkey=
page.label.attribute.reference.connections=
page.label.attribute.reference.data.element.name=
page.label.attribute.reference.data.source=
page.label.attribute.reference.variable=
page.label.attribute.relationship=
page.label.attribute.removed=
page.label.attribute.repeat.dates.annually=
page.label.attribute.revision=
page.label.attribute.sample.value=
page.label.attribute.script=
page.label.attribute.segment.data=
page.label.attribute.selection.data=
page.label.attribute.source.type=
page.label.attribute.sql.expression=
page.label.attribute.start.date=
page.label.attribute.status=
page.label.attribute.supports.barcodes=
page.label.attribute.supports.forms=
page.label.attribute.supports.tables=
page.label.attribute.suppressed=
page.label.attribute.targetgroup.details=
page.label.attribute.type=
page.label.attribute.unique.value=
page.label.attribute.usage.type=
page.label.attribute.use.default.value=
page.label.attribute.variable.expression=
page.label.attribute.variable.script=
page.label.attribute.version.effect.end.date=
page.label.attribute.version.effect.start.date=
page.label.attribute.version.status=
page.label.attribute.working.copy.hash=
page.label.attribute.working.copy=
page.label.attribute.xml_data_element.attribute_name=
page.label.attribute.xml_data_element.is_attribute=
page.label.attribute.xml_data_element.name=
page.label.attribute.xml_data_element.tag=
page.label.attribute.xml_tag.break_indicator=
page.label.attribute.xml_tag.data_group=
page.label.attribute.xml_tag.name=
page.label.attribute.xml_tag.path=
page.label.attribute.xml_tag.repeating=
page.label.attribute.xml_tag.start_customer=
page.label.attribute.xml_tag.start_datagroup=
page.label.attribute.xml_tag_definitions=
page.label.attribute.zone.name=
page.label.attribute.zone=
page.label.attribute.zones.dnas=
page.label.attribute.zones.names=
page.label.audit.report.type.connected.report=
page.label.audit.report.type.image.library.audit.report=
page.label.audit.report.type.local.image.audit.report=
page.label.audit.report.type.local.smart.canvas.audit.report=
page.label.audit.report.type.local.smart.text.audit.report=
page.label.audit.report.type.project.audit.report=
page.label.audit.report.type.smart.canvas.audit.report=
page.label.audit.report.type.smart.text.audit.report=
page.label.audit.report.type.touchpoint.compare.report=
page.label.auditing.activation=
page.label.auditing.all.actions.where=
page.label.auditing.all.domains.for=
page.label.auditing.all.events.for=
page.label.auditing.all.instances.for=
page.label.auditing.any.user=
page.label.auditing.archived=
page.label.auditing.asset.change=
page.label.auditing.asset.update=
page.label.auditing.attachment.targeting.change=
page.label.auditing.audit.report.requested.and.successfully.generated=
page.label.auditing.audit.report.requested.but.failed=
page.label.auditing.audit.report=
page.label.auditing.authentication.failure=
page.label.auditing.authentication.success=
page.label.auditing.between=
page.label.auditing.changes=
page.label.auditing.cleanup=
page.label.auditing.connected=
page.label.auditing.content.targeting.change=
page.label.auditing.creation=
page.label.auditing.data.element=
page.label.auditing.de.license.content.edit=
page.label.auditing.de.license.list=
page.label.auditing.de.license=
page.label.auditing.deactivation=
page.label.auditing.delete.archive=
page.label.auditing.delete=
page.label.auditing.delivery.event=
page.label.auditing.disabling=
page.label.auditing.discard=
page.label.auditing.domain.license=
page.label.auditing.domain=
page.label.auditing.enabling=
page.label.auditing.ending=
page.label.auditing.events.initiated.by=
page.label.auditing.execute=
page.label.auditing.font.list=
page.label.auditing.image.library.content.change=
page.label.auditing.image.library.content.edit=
page.label.auditing.image.library.list=
page.label.auditing.insert.discard=
page.label.auditing.insert.edit=
page.label.auditing.insert.list=
page.label.auditing.insert.schedule.discard=
page.label.auditing.insert.schedule.edit=
page.label.auditing.insert.schedule.list=
page.label.auditing.insert.targeting.change=
page.label.auditing.instance.license=
page.label.auditing.instance=
page.label.auditing.license=
page.label.auditing.local.image.content.change=
page.label.auditing.local.image.content.edit=
page.label.auditing.local.image.list=
page.label.auditing.local.smart.canvas.content.change=
page.label.auditing.local.smart.canvas.targeting.change=
page.label.auditing.local.smart.text.content.change=
page.label.auditing.local.smart.text.content.edit=
page.label.auditing.local.smart.text.list=
page.label.auditing.local.smart.text.targeting.change=
page.label.auditing.message.activate=
page.label.auditing.message.content.change=
page.label.auditing.message.content.edit=
page.label.auditing.message.list=
page.label.auditing.message.targeting.change=
page.label.auditing.on.any.date=
page.label.auditing.order.round.trip=
page.label.auditing.output.tag.targeting.change=
page.label.auditing.page.access=
page.label.auditing.password.change=
page.label.auditing.password.reset=
page.label.auditing.password.setting=
page.label.auditing.permission.change=
page.label.auditing.power.edit.save=
page.label.auditing.proof.run=
page.label.auditing.purge.notification=
page.label.auditing.purge=
page.label.auditing.report.run=
page.label.auditing.reports.list=
page.label.auditing.requests=
page.label.auditing.role.change=
page.label.auditing.role=
page.label.auditing.signin=
page.label.auditing.simulation.list=
page.label.auditing.simulation.run=
page.label.auditing.smart.canvas.content.change=
page.label.auditing.smart.canvas.targeting.change=
page.label.auditing.smart.text.content.change=
page.label.auditing.smart.text.content.edit=
page.label.auditing.smart.text.list=
page.label.auditing.smart.text.targeting.change=
page.label.auditing.starting=
page.label.auditing.suppress=
page.label.auditing.sync.requested.and.successfully.generated=
page.label.auditing.sync.requested.but.failed=
page.label.auditing.sync=
page.label.auditing.target.group.edit=
page.label.auditing.target.groups.list=
page.label.auditing.target.rule.edit=
page.label.auditing.target.rules.list=
page.label.auditing.test.connection.requested.and.successfully.tested=
page.label.auditing.test.run=
page.label.auditing.tests.list=
page.label.auditing.text.connection.requested.but.failed=
page.label.auditing.text.style.list=
page.label.auditing.touchpoint.setting=
page.label.auditing.touchpoint.targeting.change=
page.label.auditing.tp.version.requested.and.successfully.generated=
page.label.auditing.tp.version.requested.but.failed=
page.label.auditing.umh.license.content.edit=
page.label.auditing.umh.license=
page.label.auditing.user=
page.label.auditing.variants.list=
page.label.auditing.version=
page.label.auditing.web.service=
page.label.auditing.workgroup.change=
page.label.auditing=
page.label.authentication=
page.label.available.connectors=
page.label.available.languages=
page.label.available.touchpoints=
page.label.avoid.consecutive.spaces.between.sentences=
page.label.avoid.consecutive.spaces.within.sentences=
page.label.avoid.single.item.bulleted.lists=
page.label.avoid.single.item.numbered.lists=
page.label.avoid.these.contractions=
page.label.back.single.step=
page.label.background.tasks=
page.label.barcode=
page.label.barcodes=
page.label.basic.info=
page.label.blue.relay.configuration=
page.label.blue.relay.endpoint=
page.label.blue.relay.target.folder=
page.label.blue.relay.token=
page.label.bold.toggle=
page.label.border.type.bottom=
page.label.border.type.full=
page.label.border.type.top=
page.label.branch.user.licence.stats=
page.label.brand.guidance=
page.label.brand.profile=
page.label.brand.profiles=
page.label.brand=
page.label.bulk.delete=
page.label.bullet.spacing.header=
page.label.bullet.spacing=
page.label.bullet.symbol.overrides=
page.label.bundle.combined.content=
page.label.bundle.delivery.var.description.company=
page.label.bundle.delivery.var.description.date_dd=
page.label.bundle.delivery.var.description.date_mm=
page.label.bundle.delivery.var.description.date_yy=
page.label.bundle.delivery.var.description.date_yyyy=
page.label.bundle.delivery.var.description.instance=
page.label.bundle.delivery.var.description.instancetype=
page.label.bundle.delivery.var.description.jobid=
page.label.bundle.delivery.var.description.jobtype=
page.label.bundle.delivery.var.description.podid=
page.label.bundle.delivery.var.description.time_hh=
page.label.bundle.delivery.var.description.time_mm=
page.label.bundle.delivery.var.description.time_ss=
page.label.bundle.delivery.var.description.touchpoint=
page.label.bundle.delivery=
page.label.bundle.images.applying.filename=
page.label.bundle=
page.label.bundled.composition.package=
page.label.bundled.data.resource=
page.label.button=
page.label.by.similarity.range=
page.label.calculate.hash.for.all=
page.label.calculate.hash=
page.label.cascade.password.change=
page.label.category.administrative=
page.label.category.connected=
page.label.category.data=
page.label.category.inserts=
page.label.category.messaging=
page.label.category.miscellaneous=
page.label.category.pinc=
page.label.category.projects=
page.label.category.rationalizer=
page.label.category.reporting=
page.label.category.shared.libraries=
page.label.category.targeting=
page.label.category.tasks=
page.label.category.test.proof.simulation=
page.label.category.touchpoint.setup=
page.label.category.versions=
page.label.category.workflow=
page.label.change.all=
page.label.change.domain=
page.label.change.owner=
page.label.changed.since.last.run=
page.label.changed=
page.label.channel.restriction=
page.label.checked=
page.label.child.linked.messages=
page.label.child=
page.label.clear.content.and.continue=
page.label.clear.date=
page.label.clear.metadata=
page.label.clear.touchpoint.metadata=
page.label.clear=
page.label.clone.interactive=
page.label.clone.interview=
page.label.clone.to.global=
page.label.clone.to.local=
page.label.clone.touchpoint=
page.label.cloned.from=
page.label.cloned=
page.label.collapse.all.results=
page.label.collapse.all=
page.label.collections=
page.label.color.toggle=
page.label.combo.box=
page.label.communications.orders.list=
page.label.compare.content.with=
page.label.compare.definition=
page.label.compare.production=
page.label.compare.to=
page.label.compare.wip=
page.label.compare=
page.label.comparison.content=
page.label.comparison=
page.label.components=
page.label.composition.packages.deleted=
page.label.composition.tags=
page.label.composition=
page.label.compound.format.type.semicolon=
page.label.conditional.enable=
page.label.configurable.links=
page.label.configuration=
page.label.confirm.activate.all.insert.schedules=
page.label.confirm.assets.to.be.removed=
page.label.confirm.bulk.delete=
page.label.confirm.change.owner=
page.label.confirm.clear.metadata=
page.label.confirm.clone.rationalizer.application=
page.label.confirm.clone.to.global=
page.label.confirm.clone.to.local=
page.label.confirm.create.local=
page.label.confirm.create.touchpoint.project=
page.label.confirm.deactivate.all.insert.schedules=
page.label.confirm.default.package.change=
page.label.confirm.delete.brand.profile=
page.label.confirm.delete.cloned.rationalizer.application=
page.label.confirm.delete.data.file=
page.label.confirm.delete.data.resource=
page.label.confirm.delete.de.server=
page.label.confirm.delete.dictionary=
page.label.confirm.delete.list.style=
page.label.confirm.delete.metadata.form=
page.label.confirm.delete.project=
page.label.confirm.delete.rationalizer.application=
page.label.confirm.delete.rationalizer.content=
page.label.confirm.delete.rationalizer.document=
page.label.confirm.delete.rationalizer.query=
page.label.confirm.delete.task=
page.label.confirm.delete.test.suite=
page.label.confirm.delete.user=
page.label.confirm.delete.workflow=
page.label.confirm.disable.dictionary=
page.label.confirm.download=
page.label.confirm.enable.dictionary=
page.label.confirm.generate.report=
page.label.confirm.initialize=
page.label.confirm.language.repair=
page.label.confirm.make.global=
page.label.confirm.make.local=
page.label.confirm.mark.task.complete=
page.label.confirm.move.to.global=
page.label.confirm.move.to.local=
page.label.confirm.pull.from.global=
page.label.confirm.push.to.global=
page.label.confirm.remove.additional.file=
page.label.confirm.reopen.task=
page.label.confirm.rerun.test.suite=
page.label.confirm.restore.locale=
page.label.confirm.scan.domain=
page.label.confirm.soft.deactivate.user=
page.label.confirm.unsubscribe=
page.label.confirm.upgrade.domain=
page.label.confirm.upgrade.instance=
page.label.confirm.upload.blue.relay=
page.label.conflict=
page.label.connected.mini.production=
page.label.connected.production=
page.label.connected.proof=
page.label.connected.setup=
page.label.connected.test.center=
page.label.connected.visibility=
page.label.connected.workflow=
page.label.connected.zone.content=
page.label.connection.error=
page.label.connection.status=
page.label.consolidate.combine.into.shared.object=
page.label.consolidate.content=
page.label.consolidate.duplicates=
page.label.consolidate.existing=
page.label.consolidate.find=
page.label.consolidate.into.existing=
page.label.consolidate.into.new.object=
page.label.consolidate.items.found=
page.label.consolidate.make.searched.content=
page.label.consolidate.new=
page.label.consolidate.searched.content=
page.label.consolidate.select.existing=
page.label.consolidate.selected.objects=
page.label.consolidate.selected.single.object=
page.label.consolidate.selected.shared.contents=
page.label.consolidate.selected.single.shared.content=
page.label.consolidate.similarities=
page.label.consolidate=
page.label.constant=
page.label.constants.none=
page.label.constants=
page.label.contact.email=
page.label.contains=
page.label.content.action.type.clone=
page.label.content.action.type.default=
page.label.content.action.type.import=
page.label.content.action.type.sync=
page.label.content.compare=
page.label.content.container=
page.label.content.history=
page.label.content.intelligence=
page.label.content.is.different=
page.label.content.link=
page.label.content.menus=
page.label.content.metadata=
page.label.content.metatags=
page.label.content.settings=
page.label.content.source=
page.label.content.tags=
page.label.content.targeting=
page.label.content.trim.type.after=
page.label.content.trim.type.before.and.after=
page.label.content.trim.type.before=
page.label.content.trim.type.none=
page.label.contractions=
page.label.control.style.attributes=
page.label.copies.input=
page.label.copy.to.clipboard=
page.label.copy=
page.label.count=
page.label.create.local=
page.label.create.touchpoint.project=
page.label.criteria=
page.label.current.schema=
page.label.current.workflow=
page.label.current=
page.label.custom.header=
page.label.custom.masks=
page.label.custom.value=
page.label.customer.production.files.dataresource=
page.label.customer.production.files.folder=
page.label.customer.smtp.override=
page.label.customer.smtp.settings=
page.label.customize.locale=
page.label.dashboard.all=
page.label.dashboard.brand.compound_space=
page.label.dashboard.brand.lead_compound_space=
page.label.dashboard.brand.legal_mark_superscripting=
page.label.dashboard.brand.max_sentence_length=
page.label.dashboard.brand.phone_number_format=
page.label.dashboard.brand.preferred_contraction=
page.label.dashboard.brand.restricted_contraction=
page.label.dashboard.brand.restricted_term=
page.label.dashboard.brand.single_bullet_list=
page.label.dashboard.brand.single_number_list=
page.label.dashboard.brand.subtitle=
page.label.dashboard.brand.title=
page.label.dashboard.brand.url_format=
page.label.dashboard.content.objects.of.total=
page.label.dashboard.content.objects=
page.label.dashboard.documents.of.total=
page.label.dashboard.documents=
page.label.dashboard.duplicates.report=
page.label.dashboard.duplicates=
page.label.dashboard.exact.matches=
page.label.dashboard.metadata.subtitle=
page.label.dashboard.metadata.title=
page.label.dashboard.reading.flesch.kincaid.score=
page.label.dashboard.reading.flesch.kincaid=
page.label.dashboard.reading.good=
page.label.dashboard.reading.poor=
page.label.dashboard.reading.subtitle=
page.label.dashboard.reading.title=
page.label.dashboard.sentiment.average.negative=
page.label.dashboard.sentiment.average.neutral=
page.label.dashboard.sentiment.average.positive=
page.label.dashboard.sentiment.negative=
page.label.dashboard.sentiment.neutral=
page.label.dashboard.sentiment.positive=
page.label.dashboard.sentiment.subtitle=
page.label.dashboard.sentiment.title=
page.label.dashboard.similarities.count=
page.label.dashboard.similarities.subtitle=
page.label.dashboard.similarities.title=
page.label.dashboard.smart.text=
page.label.dashboard.touchpoint=
page.label.dashboard=
page.label.data.anonymizer.delimeter=
page.label.data.anonymizer=
page.label.data.break.record=
page.label.data.feed=
page.label.data.file.encoding=
page.label.data.record=
page.label.data.web.service=
page.label.de.server.type=
page.label.de.server=
page.label.de.servers=
page.label.deactivate.all=
page.label.default.canvas.width=
page.label.default.content.compare=
page.label.default.customer.level=
page.label.default.exists.for=
page.label.default.list.style=
page.label.default.value.selection=
page.label.default.workflow=
page.label.delete.cloned=
page.label.delete.content=
page.label.delete.keypair=
page.label.delete.test.suite=
page.label.delete.user.detail=
page.label.delete.user=
page.label.deleted=
page.label.delivered.to=
page.label.delivery.events.of.run.type=
page.label.dependencies.list=
page.label.dependent=
page.label.deselectable=
page.label.diagnostics.hide.stacktrace=
page.label.diagnostics.marcie=
page.label.diagnostics.show.stacktrace=
page.label.dictionaries=
page.label.dictionary.type.global=
page.label.dictionary.type.system=
page.label.dictionary.type.user=
page.label.differences.list=
page.label.do.not.send.message=
page.label.document.export.history.all=
page.label.document.export.history.none=
page.label.document.export.history.original=
page.label.document.export.history=
page.label.document.metadata=
page.label.document.metatags=
page.label.document.name=
page.label.document.origination=
page.label.document.setting=
page.label.document.tags=
page.label.documents=
page.label.does.not.contain=
page.label.does.not.equal=
page.label.doesn.not.contain=
page.label.domain.admin.branch=
page.label.domain.admin.licences=
page.label.domain.admin.maintenance=
page.label.domain.admin.system.settings=
page.label.domain.admin=
page.label.domain.controller=
page.label.domain.email.notication=
page.label.domain.languages=
page.label.domain.visibility=
page.label.double=
page.label.download.imported.XML=
page.label.download.log.file=
page.label.download.message.on.approval=
page.label.download.metadata=
page.label.download.pod.statistics=
page.label.download.public.key=
page.label.download.result=
page.label.download.results=
page.label.download.role.template=
page.label.download.similarity.report=
page.label.download.user.template=
page.label.draft=
page.label.drop.decimals.for.round.numbers=
page.label.duplex.output=
page.label.duplicate=
page.label.duplicates.count=
page.label.duplicates=
page.label.duration=
page.label.dxf=
page.label.ecatalog.application.name=
page.label.ecatalog.application=
page.label.ecatalog.interview.form.data=
page.label.ecatalog.interview.form.reference.data=
page.label.ecatalog.letter.name=
page.label.ecatalog.number.of.letters=
page.label.ecatalog.number.of.questions=
page.label.ecatalog.number.of.sections=
page.label.ecatalog.queries=
page.label.ecatalog=
page.label.edit.condition=
page.label.edit.content.metadata.template=
page.label.edit.document.metadata.template=
page.label.edit.document.metadata=
page.label.edit.document=
page.label.edit.menu.item=
page.label.edit.settings=
page.label.edit.value=
page.label.editor=
page.label.elements=
page.label.email.notication.enabled=
page.label.email.notication=
page.label.email.proof.emailaddress.settings=
page.label.email.proof.server.settings=
page.label.email.proof=
page.label.embedded.content.and.canvas=
page.label.embedded.content.content.type.default=
page.label.embedded.content.content.type.markup=
page.label.embedded.content.content.type.shared.freeform=
page.label.embedded.opentype.font.EOT=
page.label.empty.value=
page.label.enable.as.selector=
page.label.enable.bulleted.lists=
page.label.enable.for.connected=
page.label.enforce.legal.mark.superscripting=
page.label.enforce.minimum.height=
page.label.eot.file=
page.label.equals=
page.label.escape.tags.in.driver.data=
page.label.exact.matches.and.similarities=
page.label.exact.matches.only=
page.label.exact.matches=
page.label.exact.word.or.phrase=
page.label.example=
page.label.excel=
page.label.exchange=
page.label.execute.incloud.simulation=
page.label.execute.test.suite=
page.label.exit=
page.label.expand.all.results=
page.label.expiry.duration.one.day=
page.label.expiry.duration.one.month=
page.label.expiry.duration.one.week=
page.label.expiry.duration.six.months=
page.label.expiry.duration.three.months=
page.label.expiry.duration.two.months=
page.label.expiry.duration.year=
page.label.expiry.duration.zero.day=
page.label.export.variant.metadata.template=
page.label.external.proof.validation=
page.label.external=
page.label.feature.activation.info=
page.label.feature.activation.title=
page.label.feature.list=
page.label.field=
page.label.file.size=
page.label.file.type=
page.label.filename.pattern=
page.label.filename.variables=
page.label.fileroot.management=
page.label.fillable.forms=
page.label.filter.assets.which.are=
page.label.filter.results=
page.label.filter.tags=
page.label.find.dependencies=
page.label.find.letters.where.the.value.of=
page.label.find.menu.items=
page.label.fit.to.container=
page.label.fit.to.section.size=
page.label.fix.all=
page.label.fix=
page.label.fixed=
page.label.flesch_kincaid.grade.level=
page.label.flesch_kincaid=
page.label.flesch_readability=
page.label.flow.type=
page.label.folder=
page.label.fonts=
page.label.footer=
page.label.force.sync=
page.label.forced.proofing.primary.value=
page.label.form.format.category.none=
page.label.form.format.category.number=
page.label.form.format.category.percentage=
page.label.form.format.category.special=
page.label.form.format.category=
page.label.form.format.currency=
page.label.form.format.decimal=
page.label.form.format.location.AfterNoSpace=
page.label.form.format.location.AfterSpace=
page.label.form.format.location.BeforeNoSpace=
page.label.form.format.location.BeforeSpace=
page.label.form.format.location=
page.label.form.format.negative.parentheses=
page.label.form.format.negative.redtxt=
page.label.form.format.negative.style=
page.label.form.format.separator=
page.label.form.format.special.custom=
page.label.form.format.special.mask=
page.label.form.format.special.option=
page.label.form.format.special.phone=
page.label.form.format.special.sin=
page.label.form.format.special.zip4=
page.label.form.format.special.zip=
page.label.form.format=
page.label.format=
page.label.full.width.table=
page.label.fuzzy=
page.label.generate.audit.report=
page.label.generate.documents.and.files.report=
page.label.generate.documents.report=
page.label.generate.documents.to.messagepoint=
page.label.generate.domain.report=
page.label.generate.local.image.report=
page.label.generate.local.smart.canvas.report=
page.label.generate.local.smart.text.report=
page.label.generate.metadata.report=
page.label.generate.project.report=
page.label.generate=
page.label.generating.key.pair=
page.label.global.asset=
page.label.global.copy=
page.label.global.dashboard=
page.label.global.results.by=
page.label.global.search.results=
page.label.global.search.subtile=
page.label.global.search=
page.label.go.to.login=
page.label.groove=
page.label.group.name=
page.label.hardware.key.a=
page.label.hardware.key.b=
page.label.hardware.key.c=
page.label.hardware.key.d=
page.label.hardware.key.e=
page.label.hardware.key.f=
page.label.has.any.value=
page.label.header=
page.label.hide.filter=
page.label.hide.variables=
page.label.hide=
page.label.hide_changes=
page.label.homepage=
page.label.horizontal.alignment=
page.label.host.name=
page.label.host=
page.label.hostname=
page.label.hours.before.due.date=
page.label.html.output=
page.label.html=
page.label.identifier=
page.label.ignore=
page.label.is_empty=
page.label.is_not_empty=
page.label.image.dimension.type.fit.to.height=
page.label.image.dimension.type.fit.to.width.and.height=
page.label.image.dimension.type.fit.to.width=
page.label.image.dimension.type.fixed.height=
page.label.image.dimension.type.fixed.width.and.height=
page.label.image.dimension.type.fixed.width=
page.label.image.dimension.type.none=
page.label.image.dimension.type.relative.width=
page.label.image.dimension.type.unaltered=
page.label.image.dimensions=
page.label.image.library=
page.label.image.link=
page.label.imap=
page.label.imaps=
page.label.implied.decimal.places=
page.label.import.and.update.data.source.and.variables=
page.label.import.domains=
page.label.import.only.shared.objects=
page.label.important.message=
page.label.imported.from.xml=
page.label.importing=
page.label.inches.short=
page.label.include.all.messages=
page.label.include.feature.activation.info=
page.label.include.license.management.info=
page.label.include.user.management.info=
page.label.independent=
page.label.indicators.changed=
page.label.information=
page.label.ingestion.log=
page.label.ingestion.upload.allowLowerCase=
page.label.ingestion.upload.allowSpaces=
page.label.ingestion.upload.maxVariableLength=
page.label.ingestion.upload.variable.notation=
page.label.ingestion.upload.variable.settings=
page.label.ingestion=
page.label.inherit.values=
page.label.initialize=
page.label.injection.location=
page.label.inline.spell.checking=
page.label.input.formatting=
page.label.input=
page.label.insert.in.place=
page.label.insert.schedules.setup=
page.label.inset=
page.label.interactive.content=
page.label.interview=
page.label.ip.address=
page.label.is.domain.admin=
page.label.is.pod.admin=
page.label.is.sso.user=
page.label.italic.toggle=
page.label.job.center=
page.label.keep.together=
page.label.keep.with.next=
page.label.keep.with.previous=
page.label.keep.with=
page.label.keep=
page.label.key.authentication=
page.label.key.pair=
page.label.language.override=
page.label.last.login=
page.label.last.message.workflow.action=
page.label.last.run.date=
page.label.last.sync.date=
page.label.legacy.dxf.mode=
page.label.legal.marks=
page.label.less=
page.label.letter.content=
page.label.letter.name=
page.label.letters=
page.label.library=
page.label.license.manager=
page.label.license.sfmcjb=
page.label.limited=
page.label.line.spacing.type=
page.label.line=
page.label.linked.object=
page.label.linked.objects=
page.label.linked.touchpoint=
page.label.links=
page.label.list.box=
page.label.list.content=
page.label.list.filter.type.anyone=
page.label.list.filter.type.association=
page.label.list.filter.type.collection=
page.label.list.filter.type.disabled=
page.label.list.filter.type.editable=
page.label.list.filter.type.edited=
page.label.list.filter.type.enabled=
page.label.list.filter.type.image.library=
page.label.list.filter.type.in.any.state.not.archive=
page.label.list.filter.type.in.any.state=
page.label.list.filter.type.insert.schedule=
page.label.list.filter.type.insert=
page.label.list.filter.type.items.asc=
page.label.list.filter.type.items.desc=
page.label.list.filter.type.latest=
page.label.list.filter.type.local.image=
page.label.list.filter.type.local.text=
page.label.list.filter.type.master=
page.label.list.filter.type.myself=
page.label.list.filter.type.name.asc=
page.label.list.filter.type.name.desc=
page.label.list.filter.type.new=
page.label.list.filter.type.others=
page.label.list.filter.type.rationalizer.content=
page.label.list.filter.type.rationalizer.shared=
page.label.list.filter.type.smart.text=
page.label.list.filter.type.target.group=
page.label.list.filter.type.touchpoint=
page.label.list.filter.type.unchangeable=
page.label.list.filter.type.unedited=
page.label.list.spacing.header=
page.label.list.spacing=
page.label.list.style.composition=
page.label.list.style.customization=
page.label.list.style=
page.label.list.styles.info=
page.label.list.styles=
page.label.list.tag.filter.type.collection=
page.label.list.tag.filter.type.output.file=
page.label.list.tag.filter.type.recipient=
page.label.lists=
page.label.loading.data.source=
page.label.loading.rules=
page.label.loading.target.groups=
page.label.local.domain=
page.label.local.global.management=
page.label.local.image.library=
page.label.local.image=
page.label.local.images=
page.label.local.smart.canvas=
page.label.local.smart.text=
page.label.local.sso=
page.label.locale.customization=
page.label.log.differences=
page.label.lookup.expression=
page.label.lookup.table.setup=
page.label.lookup.table=
page.label.lookup.tables=
page.label.make.global=
page.label.make.local=
page.label.manager=
page.label.map=
page.label.marcie.can.create.application=
page.label.marcie.can.run.job=
page.label.marcie.server.alive=
page.label.mark.complete=
page.label.markup=
page.label.max.Length=
page.label.message.and.content.targeting=
page.label.message.details=
page.label.message.expired=
page.label.message.flow.type.any=
page.label.message.flow.type.first.or.only=
page.label.message.flow.type.first=
page.label.message.flow.type.last.or.only=
page.label.message.flow.type.last=
page.label.message.flow.type.not.first.not.last=
page.label.message.flow.type.not.first=
page.label.message.flow.type.not.last=
page.label.message.flow.type.only=
page.label.message.for=
page.label.message.metadata=
page.label.message.workflow=
page.label.messagepoint.exchange=
page.label.messagepoint.pinc.apitoken=
page.label.messagepoint.pinc.application=
page.label.messagepoint.pinc.company=
page.label.messagepoint.pinc.encryption=
page.label.messagepoint.pinc.engine=
page.label.messagepoint.pinc.executable=
page.label.messagepoint.pinc.file=
page.label.messagepoint.pinc.job=
page.label.messagepoint.pinc.jobmanager=
page.label.messagepoint.pinc.mpinstance=
page.label.messagepoint.pinc.role=
page.label.messagepoint.pinc=
page.label.metadata.field.size.type.extra.large=
page.label.metadata.field.size.type.five.character=
page.label.metadata.field.size.type.full.width=
page.label.metadata.field.size.type.large=
page.label.metadata.field.size.type.medium=
page.label.metadata.field.size.type.one.character=
page.label.metadata.field.size.type.small=
page.label.metadata.field.size.type.two.character=
page.label.metadata.form.setup=
page.label.metadata.forms.setup=
page.label.metadata.forms=
page.label.metadata.input.validation.type.decimal=
page.label.metadata.input.validation.type.integer=
page.label.metadata.input.validation.type.none=
page.label.metadata.items=
page.label.metadata.optional=
page.label.metadata.properties=
page.label.metadata.refresh.type.on.ancestor.value.change=
page.label.metadata.refresh.type.on.any.value.change=
page.label.metadata.refresh.type.on.load=
page.label.metadata=
page.label.middle=
page.label.min=
page.label.minimize=
page.label.minimum.similarity.threshold=
page.label.mixed.dxf.tagged.text.mode=
page.label.modify.content.metadata=
page.label.modify.content=
page.label.modify.documents=
page.label.move.down=
page.label.move.to.global=
page.label.move.to.local=
page.label.move.up=
page.label.multi.recipient.identifier=
page.label.multiline=
page.label.multiple.selections=
page.label.multiple.values=
page.label.near.term.hours=
page.label.negative=
page.label.nest=
page.label.neutral=
page.label.never=
page.label.new.template.uploaded=
page.label.new.touchpoint.language.added=
page.label.no.access=
page.label.no.action=
page.label.no.applications=
page.label.no.assignment=
page.label.no.placeholder.content=
page.label.no.placeholder=
page.label.no.selected.items=
page.label.no.translator=
page.label.node.status.upgrade.error=
page.label.node.status.upgrade.in.progress=
page.label.none.of.these=
page.label.not.applicable=
page.label.not.enabled=
page.label.not.tested=
page.label.notification.deliveryevent=
page.label.notification.emails.cc=
page.label.notification.emails.to=
page.label.notification.emails=
page.label.notification.image.activated=
page.label.notification.image.working.copy.created=
page.label.notification.local.image.activated=
page.label.notification.local.image.working.copy.created=
page.label.notification.local.smart.activated=
page.label.notification.local.smart.text.working.copy.created=
page.label.notification.message.activated=
page.label.notification.message.working.copy.created=
page.label.notification.messages=
page.label.notification.project.complete=
page.label.notification.project.created=
page.label.notification.project.overdue=
page.label.notification.project=
page.label.notification.settings=
page.label.notification.smart.text.activated=
page.label.notification.smart.text.working.copy.created=
page.label.notification.system.task=
page.label.notification.task.created=
page.label.notification.task.near.term=
page.label.notification.task.overdue=
page.label.notification.task.reassigned=
page.label.notification.task=
page.label.notification.user.activation=
page.label.notification.user.deactivation=
page.label.notification.user.forgot.password=
page.label.notification.user.password.change=
page.label.notification.user.password.reset=
page.label.notification.user=
page.label.notification.workflow.approved.by.others=
page.label.notification.workflow.approved=
page.label.notification.workflow.due.by.auto.approval=
page.label.notification.workflow.due.by.notify=
page.label.notification.workflow.reassigned=
page.label.notification.workflow.rejected=
page.label.notification.workflow.released.for.approval=
page.label.notification.workflow.wait.for.approval=
page.label.notification.workflow.wait.for.translation=
page.label.notification.workflow=
page.label.notify.on.error.only=
page.label.number.local.msg=
page.label.number.of.duplicates=
page.label.number.of.similarities=
page.label.number.of.smart.texts=
page.label.number.users=
page.label.object.filter=
page.label.object.type.s=
page.label.object.types=
page.label.omit.first.header=
page.label.omit.last.footer=
page.label.on.approval.mini.bundle=
page.label.on.approval=
page.label.on.values.change=
page.label.one.time=
page.label.one=
page.label.only.include.selected.messages=
page.label.only.show=
page.label.onprem.servers=
page.label.operator=
page.label.option=
page.label.optional.metadata=
page.label.options=
page.label.order.entry.type.file=
page.label.order.entry.type.multiselect.menu=
page.label.order.entry.type.text.editor=
page.label.order.entry.type.web.service.multiselect.menu=
page.label.order.entry.type.web.service.text=
page.label.order.review=
page.label.origin=
page.label.original=
page.label.output.document.title=
page.label.output.file.type.afp=
page.label.output.file.type.html=
page.label.output.file.type.pdf=
page.label.output.file.type=
page.label.output.filename=
page.label.output.tags=
page.label.outset=
page.label.override.bundle.filename=
page.label.overwrite=
page.label.owner.s=
page.label.owner=
page.label.owners=
page.label.package.file.ZIP=
page.label.padding=
page.label.page.break.after=
page.label.page.break.before=
page.label.page.break.none=
page.label.page.break=
page.label.paragraph.style.composition=
page.label.paragraph.styles.default.left.indentation.info=
page.label.paragraph.styles.info=
page.label.paragraph.styles.multiple.line.spacing.info=
page.label.paragraph.styles.selectable.info=
page.label.parent.of.source=
page.label.parent.of.target=
page.label.passwordrecovery.check.your.email=
page.label.passwordrecovery=
page.label.path=
page.label.pdf.view=
page.label.pending.initial.task.complete=
page.label.pending.task.complete=
page.label.permissions=
page.label.pinc.api.tokens=
page.label.pinc.applications=
page.label.pinc.encryption.keys=
page.label.pinc.executables=
page.label.pinc.files=
page.label.pinc.jobs=
page.label.pinc=
page.label.ping.federate.sso=
page.label.ping.sso.adapter.id=
page.label.ping.sso.pickup.resolution.url=
page.label.ping.sso.start.url=
page.label.placeholder=
page.label.placeholders=
page.label.platform.32=
page.label.platform.64=
page.label.platform=
page.label.play.message.on.empty.variables=
page.label.pod.access.type.local=
page.label.pod.access.type.remote=
page.label.pod.access.type=
page.label.pod.adminn=
page.label.pod.domains.import.filename=
page.label.point.size.toggle=
page.label.pop3=
page.label.port=
page.label.portal.gateway.attributes=
page.label.positive=
page.label.possible.violations.found=
page.label.post.processing.script=
page.label.postprocessor.script1=
page.label.postprocessor.script2=
page.label.power.edit=
page.label.pre.proof=
page.label.preferred.contraction=
page.label.preferred.term=
page.label.preprocessor.script=
page.label.previous=
page.label.privacy=
page.label.private.key=
page.label.production.type=
page.label.profile.name=
page.label.project.metadata=
page.label.project.setup=
page.label.project.sync=
page.label.project.versioning=
page.label.project.workflow.manager=
page.label.project.workflows=
page.label.project=
page.label.projects=
page.label.promote=
page.label.proof.browser=
page.label.proof.desktop=
page.label.proof.mobile=
page.label.proof.recipient=
page.label.proofing.error=
page.label.proofing.general.error=
page.label.proofing.message.size.error=
page.label.public.key=
page.label.published=
page.label.pull.from.global=
page.label.purge.expired.connected.orders=
page.label.purge.expired.orphans=
page.label.purge.expired.prod.jobs=
page.label.purge.expired.proofs=
page.label.purge.expired.tests=
page.label.push.to.elastic=
page.label.push.to.global=
page.label.queries=
page.label.query.edit=
page.label.query.exact.matches=
page.label.query.filters=
page.label.query.settings=
page.label.query.similarities=
page.label.query=
page.label.question=
page.label.queued=
page.label.range=
page.label.rationalizer.application=
page.label.rationalizer.content.and.document=
page.label.rationalizer.content.f=
page.label.rationalizer.content.history=
page.label.rationalizer.content=
page.label.rationalizer.dashboard.compare.tooltip=
page.label.rationalizer.dashboard.compare=
page.label.rationalizer.dashboard=
page.label.rationalizer.document.content=
page.label.rationalizer.document.content.field.order=
page.label.rationalizer.document.content.field.messageName=
page.label.rationalizer.document.content.field.zoneConnector=
page.label.rationalizer.document=
page.label.rationalizer.export.configuration=
page.label.rationalizer.export.content=
page.label.rationalizer.export.input.file.invalid=
page.label.rationalizer.export.variation.disabled=
page.label.rationalizer.export.zones=
page.label.rationalizer.query=
page.label.rationalizer.query.consolidated.content.settings.label=
page.label.rationalizer.query.consolidated.content.settings.text=
page.label.rationalizer.query.consolidated.content.text=
page.label.rationalizer.shared.content.f=
page.label.rationalizer.shared.content=
page.label.rationalizer.shared.content.edit.content=
page.label.rationalizer.shared.content.edit.details=
page.label.rationalizer.shared=
page.label.rationalizer.upload.filename=
page.label.rationalizer.upload.info=
page.label.rationalizer.workflow=
page.label.rationalizer=
page.label.read=
page.label.realtime=
page.label.reassign.tasks=
page.label.reassigned.to=
page.label.reassignment=
page.label.receive.notification.on.deliveryevent.failure=
page.label.record.type=
page.label.recurring.content=
page.label.refresh.tree=
page.label.refreshed.segmentation.analysis=
page.label.region.left=
page.label.region.right=
page.label.regular=
page.label.reject.back.one.and.override=
page.label.reject.back.one.workflow.owner=
page.label.reject.back.one=
page.label.reject.single.bullet.lists=
page.label.reject.single.number.lists=
page.label.rejected=
page.label.relative.width=
page.label.release.date=
page.label.release.from.translation=
page.label.reminder=
page.label.remote=
page.label.remove.from.package=
page.label.remove.from.shared=
page.label.remove.item.plus.children=
page.label.rename.tp.if.duplicated=
page.label.render.content=
page.label.render.paragraph.only.when.variable.content.qualifies=
page.label.render.table.border.in.pt=
page.label.render.tagged.text=
page.label.render.variable.sample.values=
page.label.render.when.testing=
page.label.rendered.container.type.fixed.width.and.height=
page.label.rendered.container.type.fixed.width=
page.label.rendered.container.type.none=
page.label.rendered.container.type.relative.width=
page.label.rendering=
page.label.reopen=
page.label.repair.priority=
page.label.repeat.on.data.group=
page.label.repeating.zone.type.all.pages=
page.label.repeating.zone.type.on.flow=
page.label.replace.with=
page.label.replace=
page.label.requested.by=
page.label.required.fields.missing=
page.label.requirement=
page.label.rerun.all.test.scenarios=
page.label.rerun.test.suite=
page.label.resave.content=
page.label.reset.tp.content.changed.flag=
page.label.resolve.variable.values=
page.label.restore.locale=
page.label.restrict.use.of.these.words=
page.label.restricted.contraction=
page.label.restricted.contractions=
page.label.restricted.quotes=
page.label.restricted.term=
page.label.restricted.terms=
page.label.restricted.upper=
page.label.result=
page.label.resynchronization=
page.label.retry.connection=
page.label.return.to.home.page=
page.label.return.to.login=
page.label.revert.tooltip=
page.label.revert=
page.label.ridge=
page.label.role.id=
page.label.rotation=
page.label.row.height=
page.label.row=
page.label.rows=
page.label.rule.list=
page.label.rules.none.found=
page.label.run.in.background=
page.label.run.new.test.scenarios=
page.label.same.as.language=
page.label.same.as.variant=
page.label.sample.value=
page.label.save.and.add=
page.label.save.and.cascade=
page.label.save.and.go.to.list=
page.label.save.and.preview=
page.label.save.and.query=
page.label.save.and.submit=
page.label.save.and.view=
page.label.save.back=
page.label.save.previous=
page.label.saved.but.nothing.changed=
page.label.scan.domain=
page.label.schedule.has.expired=
page.label.schema.guid=
page.label.script=
page.label.search.and.replace=
page.label.search.automatic=
page.label.search.by=
page.label.search.content.similar.to=
page.label.search.for=
page.label.section.name=
page.label.sections.and.zones=
page.label.segmentation.enabled=
page.label.select.all.instances=
page.label.select.items.to.compare=
page.label.select.object=
page.label.select.one=
page.label.selectable=
page.label.selected.items=
page.label.selected.touchpoints=
page.label.send.message.anyway=
page.label.sentence.length=
page.label.sentences.should.be.shorter.than=
page.label.separate=
page.label.separator.for.bundled.image.filenames=
page.label.serach.with.input=
page.label.server.access=
page.label.server.hostname=
page.label.server.port=
page.label.set.condition=
page.label.set.usage=
page.label.set.value=
page.label.sftp.settings=
page.label.sftprepo.bulksize=
page.label.sftprepo.folder=
page.label.sftprepo.host=
page.label.sftprepo.password=
page.label.sftprepo.port=
page.label.sftprepo.settings=
page.label.sftprepo.sync=
page.label.sftprepo.syncfolder=
page.label.sftprepo.syncwith=
page.label.sftprepo.title.syncfolder=
page.label.sftprepo.upload=
page.label.sftprepo.user=
page.label.shared.clipboard=
page.label.shared.content.items.removed=
page.label.shared.content.object=
page.label.shared.content.tags=
page.label.shared.from=
page.label.shared.separate.message=
page.label.show.filter=
page.label.show.markup=
page.label.show.variables=
page.label.sign.in=
page.label.signin.marketing.url=
page.label.similarities.count=
page.label.similarities=
page.label.similarity.compare.all.content=
page.label.similarity.compare.results.to.all=
page.label.similarity.report=
page.label.similarity=
page.label.simple.search=
page.label.skip.interactive.if.no.connected.zones.exist=
page.label.skipped=
page.label.smart.canvas=
page.label.smart.object=
page.label.smart.text=
page.label.smart.texts=
page.label.socialize.proof=
page.label.socialize.proofs=
page.label.soft.deactivate=
page.label.soft.deactivated=
page.label.soft.deactivation.settings=
page.label.source.document=
page.label.source.domain=
page.label.source.instance=
page.label.spacing.bottom=
page.label.spacing.left=
page.label.spacing.right=
page.label.spacing.top=
page.label.specific.date=
page.label.split.tables=
page.label.sql.query=
page.label.sql=
page.label.ssh.key=
page.label.sso.user.auto.creation.enabled=
page.label.sso=
page.label.start.data.group=
page.label.start.on.odd.page=
page.label.state.bundling=
page.label.state.forced.packing=
page.label.state.postprocessing=
page.label.state.preprocessing=
page.label.state.processing.report=
page.label.state.processing.result=
page.label.state.processing=
page.label.state.receiving.result=
page.label.state.sending=
page.label.state.waiting.for.result=
page.label.state.waiting.in.queue=
page.label.state.warning=
page.label.state=
page.label.statistics=
page.label.stats.no_pr=
page.label.stats.pr=
page.label.stopped=
page.label.string.similarity.cosine.similarity.alternate=
page.label.string.similarity.cosine.similarity=
page.label.string.similarity.description.cosine.similarity=
page.label.string.similarity.description.jaccard.index=
page.label.string.similarity.description.jaro.winkler=
page.label.string.similarity.description.metric.longest.common.subsequence=
page.label.string.similarity.description.n.gram=
page.label.string.similarity.description.normalized.levenshtein=
page.label.string.similarity.description.sorensen.dice.coefficient=
page.label.string.similarity.jaccard.index=
page.label.string.similarity.jaro.winkler=
page.label.string.similarity.metric.longest.common.subsequence=
page.label.string.similarity.n.gram=
page.label.string.similarity.normalized.levenshtein=
page.label.string.similarity.sorensen.dice.coefficient=
page.label.structure=
page.label.structured.content=
page.label.style.composition.type.attributes=
page.label.style.composition.type.name.and.attributes=
page.label.style.composition.type.name=
page.label.submit.format=
page.label.submit.method=
page.label.subscribe=
page.label.subscribed=
page.label.suites=
page.label.superscript.all.legal.marks=
page.label.supported.languages=
page.label.suppress.noneditable.zones=
page.label.sync.compare.active.only=
page.label.sync.direction=
page.label.sync.multiway=
page.label.sync.priority.put.new.wc.after.ac=
page.label.sync.with=
page.label.sync=
page.label.synch.in.update=
page.label.synch.in=
page.label.synch.out.commit=
page.label.synch.out=
page.label.synch.with.parent=
page.label.synch.with.sibling=
page.label.system.default=
page.label.system.task=
page.label.system.tasks=
page.label.system.variable.type.bundle.date=
page.label.system.variable.type.index.0.based=
page.label.system.variable.type.index.1.based=
page.label.system.variable.type.page.count=
page.label.system.variable.type.page.number=
page.label.system.variable.type.processing.date=
page.label.system.variable.type.recipient.count=
page.label.system.variable.type.recipient.index=
page.label.system.variable.type.recipient.page.count=
page.label.system.variable.type.recipient.sheet.count=
page.label.system.variable.type.sheet.count=
page.label.system.variable.type.total.page.count=
page.label.system.variable.type.total.sheet.count=
page.label.tab.order=
page.label.table.spacing=
page.label.tag.cloud=
page.label.tag.content=
page.label.tag.documents=
page.label.tag.injection.location.type.bottom=
page.label.tag.injection.location.type.top=
page.label.tagged.text=
page.label.tagging.override=
page.label.tags.create.data.elements=
page.label.tags.create.variables=
page.label.tags.import.filename=
page.label.target.groups.none.found=
page.label.target.id=
page.label.target.rule=
page.label.targetgroup.group_details=
page.label.targetgroup.list=
page.label.task.assets=
page.label.task.created.for.me=
page.label.task.metadata=
page.label.task.setup=
page.label.task.status.type.active=
page.label.task.status.type.cancelled=
page.label.task.status.type.complete=
page.label.task.status.type.edit=
page.label.task.status.type.near.term=
page.label.task.status.type.overdue=
page.label.task.status.type.planning=
page.label.task.status.type.translation=
page.label.task.status.type.workflow=
page.label.test.connection=
page.label.test.order=
page.label.test.suite=
page.label.test.suites=
page.label.testing.connection=
page.label.text.style.composition=
page.label.text.to.display=
page.label.thumbnail=
page.label.times=
page.label.title=
page.label.todays.date=
page.label.together=
page.label.toggle.instance=
page.label.top=
page.label.touchpoint.access.control=
page.label.touchpoint.cloned=
page.label.touchpoint.combine.within.document=
page.label.touchpoint.content.setup=
page.label.touchpoint.content=
page.label.touchpoint.context.disabled=
page.label.touchpoint.context.view.type=
page.label.touchpoint.created=
page.label.touchpoint.default.language.changed=
page.label.touchpoint.deleted=
page.label.touchpoint.dot=
page.label.touchpoint.exchange.update=
page.label.touchpoint.exchange=
page.label.touchpoint.export.combine.to.single.message=
page.label.touchpoint.export.config=
page.label.touchpoint.export.matched.embedded.contents.no.collision=
page.label.touchpoint.export.matched.embedded.contents.with.collision=
page.label.touchpoint.export.matched.messages.no.collision=
page.label.touchpoint.export.matched.messages.with.collision=
page.label.touchpoint.export.matched.variables=
page.label.touchpoint.export.matched.variants=
page.label.touchpoint.export.messages.collision=
page.label.touchpoint.export.messagespoint.unmatched.variables.data.element=
page.label.touchpoint.export.missing.data.source=
page.label.touchpoint.export.path=
page.label.touchpoint.export.rationalizer.unmatched.messages.document.name=
page.label.touchpoint.export.rationalizer.unmatched.messages.document.path=
page.label.touchpoint.export.requested=
page.label.touchpoint.export.smart.texts.collision=
page.label.touchpoint.export.table.item.number.limitation=
page.label.touchpoint.export.toogle.new=
page.label.touchpoint.export.toogle.update=
page.label.touchpoint.export.unmatched.messagepoint.embedded.contents=
page.label.touchpoint.export.unmatched.messagepoint.messages=
page.label.touchpoint.export.unmatched.messagepoint.variables=
page.label.touchpoint.export.unmatched.messagepoint.variants=
page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.content=
page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name=
page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.order=
page.label.touchpoint.export.unmatched.rationalizer.messages=
page.label.touchpoint.export.unmatched.rationalizer.shared.objects=
page.label.touchpoint.export.unmatched.rationalizer.variables=
page.label.touchpoint.export.unmatched.rationalizer.variants=
page.label.touchpoint.export.unmatched.rationalizer.zoneconnector.unknown=
page.label.touchpoint.export.variables=
page.label.touchpoint.export=
page.label.touchpoint.info=
page.label.touchpoint.language=
page.label.touchpoint.languages.deleted=
page.label.touchpoint.metadata=
page.label.touchpoint.production.change.status=
page.label.touchpoint.project.sync=
page.label.touchpoint.project=
page.label.touchpoint.s=
page.label.touchpoint.section.toggle=
page.label.touchpoint.selection=
page.label.touchpoint.to.be.updated=
page.label.touchpoint.update=
page.label.touchpoint.variation=
page.label.touchpoint.versioning=
page.label.touchpoint.widget=
page.label.touchpoint.zone.toggle=
page.label.touchpoint.zone=
page.label.touchpoints.collections=
page.label.toucpoint.version.sync=
page.label.transient=
page.label.translation.from=
page.label.translation.needed=
page.label.translator=
page.label.tree.hide.unselected=
page.label.tree.show.all=
page.label.trim.empty.paragraphs=
page.label.truetype.font.OTF=
page.label.truetype.font.TTF=
page.label.ttf-otf.file=
page.label.two.factor.authentication=
page.label.unable.to.connect=
page.label.unavailable.connection=
page.label.unavailable.credentials=
page.label.unchanged.since.last.run=
page.label.unchecked=
page.label.underline.toggle=
page.label.undo=
page.label.unique.content=
page.label.unique.value=
page.label.uniques=
page.label.units=
page.label.unknown.error=
page.label.unpublished=
page.label.unrestricted.search=
page.label.unsubscribe=
page.label.update.content.library.content=
page.label.update.embedded.content.content=
page.label.update.existing.properties=
page.label.update.lookup.table.workflow=
page.label.update.project.workflow=
page.label.update.staged.production.bundle=
page.label.update.test.suite=
page.label.update.touchpoint.using.xml=
page.label.update.touchpoint=
page.label.update.workflow=
page.label.upgrade.domain=
page.label.upgrade.instance=
page.label.upload.blue.relay=
page.label.upload.complete.wait.for.indexing=
page.label.upload.complete=
page.label.upload.content.metadata.template=
page.label.upload.document.metadata.template=
page.label.upload.documents=
page.label.upload.existing.users=
page.label.upload.ignore=
page.label.upload.ingest.manifest=
page.label.upload.make.unique=
page.label.upload.new.users=
page.label.upload.policy=
page.label.upload.remove.and.replace=
page.label.upload.tags=
page.label.upload.users=
page.label.upload.variant.metadata=
page.label.uploaded.file.pass.through=
page.label.uploaded.file.type.driver=
page.label.uploaded.file.type.none=
page.label.uploaded.file.type.reference=
page.label.usage.options=
page.label.usage.type=
page.label.use.connected.beta=
page.label.use.fast.similarity.query=
page.label.use.name.to.find.shared.objects=
page.label.use.ssl=
page.label.use.these.contractions=
page.label.used.by=
page.label.user.has.no.access=
page.label.user.settings.for.current.instance=
page.label.v.align=
page.label.valid.connection=
page.label.validate.production.bundle=
page.label.validation=
page.label.value.alignment=
page.label.values=
page.label.allow.variables=
page.label.variable.type.driver=
page.label.variable.type.lookup.expression=
page.label.variable.type.script=
page.label.variable.type.system=
page.label.variant.hierarchy=
page.label.variant.metadata=
page.label.variant.selection=
page.label.variant.workflow=
page.label.version=
page.label.versioned=
page.label.versions=
page.label.vertical.align.type.bottom=
page.label.vertical.align.type.middle=
page.label.vertical.align.type.top=
page.label.vertical.alignment=
page.label.view.all.composition.tags=
page.label.view.compare.report=
page.label.view.item=
page.label.view.report=
page.label.web.font.name=
page.label.web.link=
page.label.web.service.audit.report=
page.label.web.service.client.bundle=
page.label.web.service.composition.package.update=
page.label.web.service.composition.package.upload=
page.label.web.service.connected.approve=
page.label.web.service.connected.clone=
page.label.web.service.connected.create=
page.label.web.service.connected.initiateproduction=
page.label.web.service.connected.order.list=
page.label.web.service.connected.release.for.approval.or.activate=
page.label.web.service.connected.remove=
page.label.web.service.content.search=
page.label.web.service.create.data.resource=
page.label.web.service.create.sandbox.instance=
page.label.web.service.data.anonymize=
page.label.web.service.data.source.list=
page.label.web.service.deactivate.user=
page.label.web.service.delete.file=
page.label.web.service.delivery.report=
page.label.web.service.diagnostics.bundle=
page.label.web.service.diagnostics=
page.label.web.service.domain.report=
page.label.web.service.download.background.image=
page.label.web.service.download.data.file=
page.label.web.service.download.file.chunk=
page.label.web.service.edit.task=
page.label.web.service.edit.test=
page.label.web.service.get.background.task.output.file=
page.label.web.service.get.background.task.status=
page.label.web.service.import.job.stats=
page.label.web.service.import.transactional.stats=
page.label.web.service.job.performance.report=
page.label.web.service.job.status=
page.label.web.service.list.composition.packages=
page.label.web.service.list.connectedorder=
page.label.web.service.list.data.files=
page.label.web.service.list.data.resources=
page.label.web.service.list.files=
page.label.web.service.list.tests=
page.label.web.service.list.touchpoint.collections=
page.label.web.service.list.touchpoints=
page.label.web.service.list.variants=
page.label.web.service.message.audit.report=
page.label.web.service.message.list=
page.label.web.service.metadata.report=
page.label.web.service.node.approval=
page.label.web.service.node.list=
page.label.web.service.node.to.node.copy=
page.label.web.service.pods.report=
page.label.web.service.project.audit.report=
page.label.web.service.project.list=
page.label.web.service.remove.task=
page.label.web.service.remove.test=
page.label.web.service.rerun.test=
page.label.web.service.server.bundle=
page.label.web.service.sftprepo.sync=
page.label.web.service.targeting.groups.search=
page.label.web.service.targeting.rules.search=
page.label.web.service.task.list=
page.label.web.service.touchpoint.delivery.event=
page.label.web.service.touchpoint.export.result=
page.label.web.service.touchpoint.export=
page.label.web.service.touchpoint.import=
page.label.web.service.touchpoint.production.job.list=
page.label.web.service.unified.login.report=
page.label.web.service.upload.data.file=
page.label.web.service.upload.file.chunk=
page.label.web.service.upload.job.report=
page.label.web.service.upload.processed.job=
page.label.web.service.users.report=
page.label.web.services.user.password=
page.label.web.services.user=
page.label.web=
page.label.whitespace=
page.label.widget.controls=
page.label.widget.type.content=
page.label.widget.type.metadata=
page.label.widget.type.my.image.approvals=
page.label.widget.type.my.images=
page.label.widget.type.my.message.approvals=
page.label.widget.type.my.messages=
page.label.widget.type.my.recently.completed.proofs=
page.label.widget.type.my.recently.completed.tests=
page.label.widget.type.my.smart.text.approvals=
page.label.widget.type.my.smart.text=
page.label.widget.type.my.task.approvals=
page.label.widget.type.my.tasks=
page.label.widget.type.my.translations=
page.label.widget.type.my.variant.approvals=
page.label.widget.type.my.variants=
page.label.with.next=
page.label.with.previous=
page.label.wizard.settings=
page.label.wizard=
page.label.workflow.assignment=
page.label.workflow.library=
page.label.workflow.tasks=
page.label.workflow.usage.type.connected=
page.label.workflow.usage.type.general=
page.label.workflow.usage.type.message=
page.label.workflow.usage.type.rationalizer=
page.label.workflow.usage.type.variant=
page.label.workflows=
page.label.xml=
page.label.zone.name=
page.lable.XML.data.attribute=
page.lable.XML.data.element=
page.lable.for.salesforce=
page.lable.messagepoint.connected=
page.lable.notification.deliveryevent.failure=
page.message.localcanvas.history.title=
page.message.localcontent.history.title=
page.message.localimage.history.title=
page.text.a.to.z=
page.text.a_target_group_is=
page.text.about.to.generate.audit.report=
page.text.activate.all.insert.schedules=
page.text.activate.assets=
page.text.activate.local.image=
page.text.activate.local.smart.canvas=
page.text.activate.local.smart.text=
page.text.add.application=
page.text.add.data.file=
page.text.add.data.resource=
page.text.add.document.note=
page.text.add.layout=
page.text.add.local.image=
page.text.add.local.smart.text=
page.text.add.metadata=
page.text.add.rationalizer.application=
page.text.add.rationalizer.document.content=
page.text.add.tags.to.rationalizer.content=
page.text.add.tags.to.rationalizer.documents=
page.text.ads.powered.by.messagepoint=
page.text.against.all.content=
page.text.all.domain.data.will.be.erased=
page.text.all.local.image.variants.have.data=
page.text.all.local.smart.text.variants.have.data=
page.text.all.messages.have.valid.priorities=
page.text.and.are.assigned.to=
page.text.and.are.created=
page.text.and.contains=
page.text.and.when.at=
page.text.anonymized.data=
page.text.any.date=
page.text.application.properties=
page.text.apply.default.brand=
page.text.applying=
page.text.archive.selected.active.local.image=
page.text.archive.selected.active.local.smart.canvas=
page.text.archive.selected.active.local.smart.text=
page.text.assign.local.image.to.user=
page.text.assign.local.smart.canvas.to.user=
page.text.assign.local.smart.text.to.user=
page.text.assigned.to=
page.text.attributes=
page.text.auto.release.on.expiry=
page.text.back.to.home.page=
page.text.belonging.to=
page.text.border.width=
page.text.bulk.delete.selected.items=
page.text.by.grouping=
page.text.cannot.sync.object.referenced=
page.text.case.sensitive=
page.text.chain.previous.component=
page.text.change.owner=
page.text.clear.row.keep.together=
page.text.click.plus.to.add.item=
page.text.click.to.add.conditional.enable.criteria=
page.text.click.to.add.value=
page.text.clone.selected.data.file=
page.text.clone.selected.local.image=
page.text.clone.selected.local.smart.canvas=
page.text.clone.selected.local.smart.text=
page.text.clone.selected.rate.schedule=
page.text.clone.selected.rationalizer.application=
page.text.clone.touchpoint=
page.text.communication.external.validation.feedback.submitted=
page.text.communication.external.validation.order.deleted=
page.text.compare.result.content.by.similarity=
page.text.compound.local.content=
page.text.confirm.touchpoint.exchange.unsubscribe=
page.text.conflict.cust.change=
page.text.conflict.model.and.cust.change=
page.text.conflict.model.change=
page.text.content.changes.see.history=
page.text.content.currently.being.edited=
page.text.content.history.is.empty=
page.text.content.metadata=
page.text.content.shared.in=
page.text.content.text=
page.text.content.which.are=
page.text.create.selected.smart.text.local=
page.text.create.touchpoint.info=
page.text.create.touchpoint.project=
page.text.create.working.copies.of.selected.local.image=
page.text.create.working.copies.of.selected.local.smart.canvas=
page.text.create.working.copies.of.selected.local.smart.text=
page.text.create.working.copy.of.selected.assets=
page.text.create.working.copy.of.selected.lookup.tables=
page.text.dashboard.brand.info=
page.text.dashboard.duplicates.info=
page.text.dashboard.metadata.info=
page.text.dashboard.reading.info=
page.text.dashboard.sentiment.info=
page.text.dashboard.similarities.info=
page.text.data.files.containing.pi.should.not.be.uploaded=
page.text.data.type=
page.text.deactivate.all.insert.schedules=
page.text.deactivate.assets=
page.text.delete.archived.lookup.table=
page.text.delete.placeholder=
page.text.delete.selected.archived.local.image=
page.text.delete.selected.archived.local.smart.canvas=
page.text.delete.selected.archived.local.smart.text=
page.text.delete.selected.brand.profile=
page.text.delete.selected.cloned.rationalizer.application=
page.text.delete.selected.data.files=
page.text.delete.selected.data.resources=
page.text.delete.selected.de.server=
page.text.delete.selected.dictionaries=
page.text.delete.selected.list.style=
page.text.delete.selected.metadata.form=
page.text.delete.selected.project=
page.text.delete.selected.rationalizer.application=
page.text.delete.selected.rationalizer.content=
page.text.delete.selected.rationalizer.document=
page.text.delete.selected.rationalizer.query=
page.text.delete.selected.task=
page.text.delete.selected.workflow=
page.text.delete.styles=
page.text.delete.test.suite=
page.text.delete.test.suites=
page.text.delete.touchpoint.part.one.a=
page.text.delete.touchpoint.part.one.b=
page.text.delete.touchpoint.part.two=
page.text.delivery.panel=
page.text.dictionaries.which.are=
page.text.disable.selected.dictionaries=
page.text.discard.local.image.working.copy=
page.text.discard.local.smart.canvas.working.copy=
page.text.discard.local.smart.text.working.copy=
page.text.discard.removed.variants.in.target.asset=
page.text.discard.working.copy.of.lookup.table=
page.text.do.you.want.to.delete.query=
page.text.document.blue.relay.not.configured=
page.text.document.metadata=
page.text.document.name=
page.text.documents=
page.text.double.click.button.to.add.tasks=
page.text.download.template=
page.text.download.test.results=
page.text.duplicate.tab.order=
page.text.dynamic.local.content=
page.text.edit.content.metadata=
page.text.edit.content.fields=
page.text.email.proof.selected.communication=
page.text.enable.selected.dictionaries=
page.text.enter.recipient.emial.for.proof.socialization=
page.text.export.datafiles.and.dataresources.for.all.other.purposes=
page.text.export.datafiles.and.dataresources.for.connected.interview=
page.text.export.datafiles.and.dataresources.for.segmentation.analysis=
page.text.export.datafiles.and.dataresources=
page.text.fast.auto.activate=
page.text.fast.edit.reassign=
page.text.fast.edit.working.copy.init=
page.text.field.name=
page.text.find.values=
page.text.fonts.upload.for=
page.text.forbidden=
page.text.gathering.result=
page.text.global.smart.text=
page.text.go.to.my.homepage=
page.text.has.completed.click.save=
page.text.highest.to.lowest=
page.text.image.library.which.are=
page.text.import.failed=
page.text.include.all.variables=
page.text.include.local.image.activated.after=
page.text.include.local.smart.canvas.activated.after=
page.text.include.local.smart.text.activated.after=
page.text.include.messages=
page.text.initialize.selected.branch=
page.text.input.part.size.position=
page.text.input.zone.size.position=
page.text.insert.schedules.which.are=
page.text.inserts.which.are=
page.text.job.center.jobs.are.stalled=
page.text.jobs.with.type.of=
page.text.layout.override.toggles=
page.text.library.items.only=
page.text.loading.data=
page.text.local.image.which.are=
page.text.local.smart.canvas.which.are=
page.text.local.smart.text.which.are=
page.text.location=
page.text.login.again=
page.text.lookup.tables.which.are=
page.text.lowest.to.highest=
page.text.main.navigation=
page.text.make.selected.image.local=
page.text.make.selected.local.image.global=
page.text.make.selected.local.smart.canvas.global=
page.text.make.selected.local.smart.text.global=
page.text.make.selected.smart.text.local=
page.text.manadatory.create.on.import=
page.text.mark.selected.task.complete=
page.text.message.out.of.sync=
page.text.modify.content.based.on.criteria=
page.text.modify.content.metadata=
page.text.modify.documents.based.on.criteria=
page.text.more=
page.text.move.selected.image.local=
page.text.move.selected.local.image.global=
page.text.move.selected.local.smart.canvas.global=
page.text.move.selected.local.smart.text.global=
page.text.move.selected.smart.text.local=
page.text.must.pass.for.target.group.to.qualify=
page.text.my.local.image.filter.does.not.apply=
page.text.my.local.smart.canvas.filter.does.not.apply=
page.text.my.local.smart.text.filter.does.not.apply=
page.text.no.applicable.paragraph.properties=
page.text.no.available.connected.data.elements=
page.text.no.available.connected.users=
page.text.no.available.data.elements=
page.text.no.collections.matching.search=
page.text.no.embedded.content.edit.permission=
page.text.no.fonts.list=
page.text.no.items.in.list.add.below=
page.text.no.licences.for.instance=
page.text.no.list.styles=
page.text.no.local.content.libraries.qualified.for.link=
page.text.no.project.workflows.available=
page.text.no.rationalizer.applications.exist=
page.text.no.rationalizer.document.content.exists=
page.text.no.tasks.assigned=
page.text.no.touchpoint.content.edit.permission=
page.text.no.unreferenced.items=
page.text.no.widgets.enabled.for.you=
page.text.no.workflow.history.actions=
page.text.no.workflow=
page.text.not.authorized.to.edit.structured.touchpoint.content=
page.text.now.you.need.to.provide.and.confirm.your.new.pasword=
page.text.of.type=
page.text.omit.first.header.on.initial.page=
page.text.omit.last.footer.on.final.page=
page.text.on.hold.local.image.not.used.in.production=
page.text.on.hold.local.smart.canvas.not.used.in.production=
page.text.on.hold.local.smart.text.not.used.in.production=
page.text.open=
page.text.order.entry.item.is.indicator=
page.text.override.part.name.toggle=
page.text.override.zone.background.toggle=
page.text.override.zone.connected.template.toggle=
page.text.override.zone.connector.toggle=
page.text.override.zone.default.list.style.toggle=
page.text.override.zone.default.paragraph.style.toggle=
page.text.override.zone.default.text.style.toggle=
page.text.override.zone.enable.toggle=
page.text.override.zone.list.styles.toggle=
page.text.override.zone.name.toggle=
page.text.override.zone.paragraph.styles.toggle=
page.text.override.zone.shared.assets.toggle=
page.text.override.zone.text.styles.toggle=
page.text.override.zone.workgroups.toggle=
page.text.passwordreset.confirmation1=
page.text.passwordreset.confirmation2=
page.text.pending.translation.from=
page.text.placeholders.which.are=
page.text.primary.navigation=
page.text.project.has.no.available.tasks=
page.text.project=
page.text.projects.which.are=
page.text.proof.validation.feedback=
page.text.pull.selected.local.image.from.global=
page.text.pull.selected.local.smart.canvas.from.global=
page.text.pull.selected.local.smart.text.from.global=
page.text.push.selected.local.image.to.global=
page.text.push.selected.local.smart.canvas.to.global=
page.text.push.selected.local.smart.text.to.global=
page.text.query.has.no.components=
page.text.rationalizer.application.being.cloned=
page.text.rationalizer.combine.as.shared=
page.text.rationalizer.indexing.in.progress=
page.text.rationalizer.navigation.no.metadata.items.selected=
page.text.rationalizer.no.navigation.structure.defined=
page.text.rationalizer.remove.from.shared=
page.text.reassign.asset.to.user=
page.text.redirecting.sessiontimeout=
page.text.remove.additional.file=
page.text.remove.layout=
page.text.remove.selected.tags=
page.text.remove.whitespace=
page.text.reopen.selected.task=
page.text.repair.lanugages=
page.text.replace.editor.content.with.history=
page.text.requester.configuration.required=
client_messages.text.has_no_bridged_variables=
client_messages.text.has_no_variable=
page.text.requires.translation.from=
page.text.rerun.test.suite.note=
page.text.rerun.test.suite=
page.text.rerun.test.suites=
page.text.restore.locale=
page.text.restore.selected.local.image=
page.text.restore.selected.local.smart.canvas=
page.text.restore.selected.local.smart.text=
page.text.save.and.preview.to.view=
page.text.scan.domain=
page.text.section.properties=
page.text.select.default.composition.package=
page.text.select.default.list.style=
page.text.select.history.items.for.compare=
page.text.select.image.to.apply=
page.text.select.local.content.library=
page.text.select.multiway.parents.and.click.apply=
page.text.select.multiway.source.parent.and.click.apply=
page.text.select.multiway.source.parent.mandatory.and.click.apply=
page.text.select.multiway.target.parent.and.click.apply=
page.text.select.only.one.asset.for.workflow=
page.text.select.sync.settings.and.click.apply=
page.text.select.touchpoint.context.to.add.test.suite=
page.text.select.touchpoint.context.to.add.test=
page.text.select.user.for.lookup.table.reassign=
page.text.select.user.for.task.reassign=
page.text.select.user.to.assign.deactivated.assets.to=
page.text.select.variable.usage.options=
page.text.select.variants.for.compare=
page.text.selected.local.image.now.used.in.production=
page.text.selected.local.smart.canvas.now.used.in.production=
page.text.selected.local.smart.text.now.used.in.production=
page.text.set.cell.horizontal.alignment=
page.text.set.cell.vertical.alignment=
page.text.set.cell.width=
page.text.set.new.password=
page.text.set.options.for.domain.report=
page.text.set.row.height=
page.text.set.row.keep.together=
page.text.set.row.keep.with=
page.text.signed.in.as=
page.text.single.selector.touchpoint.value.invalid=
page.text.smart.canvas.which.are=
page.text.soft.deactivate.selected.user=
page.text.sort.attributes.by=
page.text.sort.elements.by=
page.text.sorted.by=
page.text.specify.metadata.form.for.project=
page.text.specify.metadata.form.for.task=
page.text.specify.workflow.for.project=
page.text.static.local.content=
page.text.step.skipped=
page.text.structured.local.content=
page.text.summary.duplicates=
page.text.summary.similarities=
page.text.summary.uniques=
page.text.suppress.selected.local.image=
page.text.suppress.selected.local.smart.canvas=
page.text.suppress.selected.local.smart.text=
page.text.switch.to.master.to.edit=
page.text.sync.message.priority=
page.text.sync.retrieve.data.failed=
page.text.tag.associations.not.applicable=
page.text.tag.targeting.not.applicable=
page.text.targeting_rules_and_conditions=
page.text.tasks.which.are=
page.text.template.has.no.metadata.items=
page.text.test.suite.of=
page.text.tests.count=
page.text.the.following.named.inputs=
page.text.this.field.is.mandatory=
page.text.timing.no.end.date.info=
page.text.timing.starts.now.info=
page.text.tip.press.enter.to.search=
page.text.toggle.dropdown=
page.text.toggle.interactive.content.indicators=
page.text.touchpoint.content.not.assigned.to.user=
page.text.touchpoint.datasource.empty=
page.text.touchpoint.has.no.metadata.items=
page.text.touchpoint.not.applicable.for.shared.canvas=
page.text.touchpoint.template.downloaded=
page.text.transition=
page.text.translation.step=
page.text.translation.timeframe.is=
page.text.trash.touchpoint.does.not.apply=
page.text.turn.domain.off.line=
page.text.turn.domain.online=
page.text.upgrade.folder.locations.for.dcs=
page.text.upgrade.folder.locations.for.node=
page.text.upload.blue.relay=
page.text.upload.files.click.or.drag=
page.text.upload.files.ingestion=
page.text.upload.files=
page.text.upload.suite.blue.relay=
page.text.upload.template=
page.text.validate.selected.branch=
page.text.validator.alphanum.dot.dash.underscore.colon=
page.text.validator.alphanum.space.dot.dash.underscore.colon=
page.text.value=
page.text.variant.working.copy.required.to.edit.touchpoint.content=
page.text.view.attributes.as.a=
page.text.view.elements.as.a=
page.text.violation.description.text.bulletlist=
page.text.violation.description.text.leadspace=
page.text.violation.description.text.maxlength=
page.text.violation.description.text.numberlist=
page.text.violation.description.text.preferredcontractions=
page.text.violation.description.text.restrictedcontractions=
page.text.violation.description.text.restrictedterms=
page.text.violation.description.text.spaces=
page.text.violation.description.text.superscripting=
page.text.violation.description.title.bulletlist=
page.text.violation.description.title.leadspace=
page.text.violation.description.title.maxlength=
page.text.violation.description.title.numberlist=
page.text.violation.description.title.preferredcontractions=
page.text.violation.description.title.restrictedcontractions=
page.text.violation.description.title.restrictedterms=
page.text.violation.description.title.spaces=
page.text.violation.description.title.superscripting=
page.text.visit=
page.text.warning.no.master.variant.workflow.assignment=
page.text.warning.no.workflow.assigned=
page.text.warning.object.change=
page.text.wc.and.active=
page.text.welcome.back=
page.text.when.input.value.is=
page.text.where=
page.text.with=
page.text.workflow.owners.may.reject.back.one.step=
page.text.workflow.step.release.from.translation=
page.text.would.you.like.login.again=
page.text.would.you.like.reject.back.one=
page.text.would.you.like.to.activate.lookup.tables=
page.text.would.you.like.to.clear.metadata=
page.text.would.you.like.to.delete.selected.user=
page.text.would.you.like.to.download.role.template=
page.text.would.you.like.to.download.user.template=
page.text.would.you.like.to.download.variants.metadata.template=
page.text.would.you.like.to.generate.document.report=
page.text.would.you.like.to.generate.duplicates.report=
page.text.would.you.like.to.generate.metatada.report=
page.text.would.you.like.to.generate.similarity.report=
page.text.would.you.like.to.sftprepo.sync.folder=
page.text.would.you.like.to.sftprepo.sync=
page.text.you.are.almost.done=
page.text.yourpersonalsettings.ssoenabled=
page.text.z.to.a=
page.text.zone.absolute.position=
page.title.ecatalog.import.heading=
page.title.signin.marketing=
page.title.touchpoint.import.to.update.confirm.heading=
page.tooltip.add.content=
page.tooltip.compare.content=
page.tooltip.edit.content.metadata=
page.tooltip.edit.content=
page.tooltip.merge.content=
page.tooltip.move.down=
page.tooltip.move.up=
page.tooltip.split.content=
page.tooltip.view.content.metadata=
page.tooltip.view.content=
service.ecatatlog.application.not.valid=
service.email.alternate.introducing.new.zone.or.modifier=
service.import.domain.does.not.exist=
service.import.ecatalog.application.does.not.exist=
service.import.required.value.missing.with.missing.name=
service.import.required.value.missing=
service.import.touchpoint.guid.exists=
service.validation.message.cloneLookupTable.lookupTableDoesNotExist=
sftprepo.async.service.error.invalid.folder=
sftprepo.async.service.error.invalid.parameter=
sftprepo.async.service.error.listing.folder=
sftprepo.async.service.error.request.fail=
sftprepo.async.service.error.too.much.image=
static.ecatalog.question.type.menu=
static.ecatalog.question.type.multi.select.nested=
static.ecatalog.question.type.multi.select.text=
static.ecatalog.question.type.multi.select=
static.ecatalog.question.type.radio=
static.ecatalog.question.type.textfield=
static.type.connector.sefas=
static.type.connector.sfmc_journeybuilder=
static.type.content.selection.type.activated=
static.type.content.selection.type.archived=
static.type.content.selection.type.deleted=
static.type.content.selection.type.different_languages=
static.type.content.selection.type.different_usages=
static.type.content.selection.type.first_time_sync=
static.type.content.selection.type.not_applicable=
static.type.content.selection.type.same=
static.type.content.selection.type.unassociated=
static.type.content.selection.type.unknown=
static.type.content.type.markup=
static.type.content.type.shared.freeform=
static.type.content.type.text.or.graphic=
static.type.deserver.communication.type.dews=
static.type.deserver.communication.type.scp=
static.type.deserver.communication.type.sftp=
static.type.deserver.communication.type.stop=
static.type.line.spacing.type.fixed=
static.type.line.spacing.type.multiple=
static.type.qualification.output.HTML=
static.type.qualification.output.xHTML=
static.type.readability.12thgrade=
static.type.readability.5thgrade=
static.type.readability.6thgrade=
static.type.readability.7thgrade=
static.type.readability.9thgrade=
static.type.readability.college=
static.type.readability.description.12thgrade=
static.type.readability.description.5thgrade=
static.type.readability.description.6thgrade=
static.type.readability.description.7thgrade=
static.type.readability.description.9thgrade=
static.type.readability.description.college=
static.type.readability.description.graduate=
static.type.readability.graduate=
static.type.source.type.lookup.table=
static.type.sync.object.type.all=
static.type.sync.object.type.content.library=
static.type.sync.object.type.data_collection=
static.type.sync.object.type.data_source=
static.type.sync.object.type.document=
static.type.sync.object.type.document_setting=
static.type.sync.object.type.local.image=
static.type.sync.object.type.local.smart.canvas=
static.type.sync.object.type.local.smart.text=
static.type.sync.object.type.lookup_table=
static.type.sync.object.type.message=
static.type.sync.object.type.parameter.group=
static.type.sync.object.type.section=
static.type.sync.object.type.smart.text=
static.type.sync.object.type.target.group=
static.type.sync.object.type.target.rule=
static.type.sync.object.type.variable=
static.type.sync.object.type.variant=
static.type.sync.object.type.zone=
static.type.zone.type.footer.zone=
static.type.zone.type.header.zone=
static.type.zone.type.markup.zone=
static.type.zone.type.placeholder.zone=
verbiage.version.reason.imported.lookup.table=
verbiage.version.reason.new.lookup.table=
xslt_messages.active.connected.users=
xslt_messages.active.regular.users=
xslt_messages.active.workflow.users=
xslt_messages.approved.by=
xslt_messages.approvers=
xslt_messages.assingee=
xslt_messages.comments=
xslt_messages.commit=
xslt_messages.connected.details=
xslt_messages.connected.orders.index=
xslt_messages.connected.orders=
xslt_messages.connectors=
xslt_messages.created.by=
xslt_messages.createddate=
xslt_messages.current.touchpoint=
xslt_messages.details=
xslt_messages.direction=
xslt_messages.domain.specification=
xslt_messages.domain=
xslt_messages.domains=
xslt_messages.feature.activation=
xslt_messages.features=
xslt_messages.final.approver=
xslt_messages.first.name=
xslt_messages.friendly.name=
xslt_messages.guid=
xslt_messages.id=
xslt_messages.idp.type=
xslt_messages.image.libraries=
xslt_messages.image.library.index=
xslt_messages.images.libraries=
xslt_messages.include.all.messages=
xslt_messages.include.feature.activation.info=
xslt_messages.include.license.management.info=
xslt_messages.include.user.management.info=
xslt_messages.index.of.domains=
xslt_messages.inserts=
xslt_messages.instance.specification=
xslt_messages.instance=
xslt_messages.instances=
xslt_messages.languages=
xslt_messages.last.activation.date=
xslt_messages.last.login=
xslt_messages.last.name=
xslt_messages.last.production.date=
xslt_messages.licence.management=
xslt_messages.license.from=
xslt_messages.license.type=
xslt_messages.list.of.domains=
xslt_messages.list.of.hidden.users=
xslt_messages.list.of.pods=
xslt_messages.list.of.visible.users=
xslt_messages.list.style=
xslt_messages.local.image=
xslt_messages.local.images=
xslt_messages.local.smart.canvas=
xslt_messages.local.smart.text=
xslt_messages.local.smart.texts=
xslt_messages.mapping.of.domains=
xslt_messages.master.schema.name=
xslt_messages.maximum.connected.users=
xslt_messages.maximum.regular.users=
xslt_messages.maximum.workflow.users=
xslt_messages.metatags=
xslt_messages.next.action=
xslt_messages.no.direct.reference.for.this.asset=
xslt_messages.no.events.for.model=
xslt_messages.no.messages.for.this.selection=
xslt_messages.no.tasks.assigned=
xslt_messages.paragraph.style=
xslt_messages.parent.touchpoint=
xslt_messages.parent=
xslt_messages.pod.description=
xslt_messages.pod.type=
xslt_messages.prerelease=
xslt_messages.production=
xslt_messages.project.audit.report=
xslt_messages.project.content=
xslt_messages.project.details=
xslt_messages.project.duedate=
xslt_messages.project.id=
xslt_messages.project.index=
xslt_messages.project.name=
xslt_messages.project.or.projects=
xslt_messages.project.owner=
xslt_messages.project.requirement=
xslt_messages.project.status=
xslt_messages.project.task.index=
xslt_messages.project.tasks=
xslt_messages.project=
xslt_messages.projects=
xslt_messages.proof=
xslt_messages.recipient=
xslt_messages.rejected.by=
xslt_messages.requested.for.pod=
xslt_messages.same.as.english=
xslt_messages.schema.name=
xslt_messages.selected.local.images.index=
xslt_messages.selected.local.smart.canvas.index=
xslt_messages.selected.local.smart.texts.index=
xslt_messages.selected.objects.index=
xslt_messages.selected.objects=
xslt_messages.selections=
xslt_messages.siblingparent.touchpoint=
xslt_messages.smart.canvas=
xslt_messages.smart.texts=
xslt_messages.sso.id=
xslt_messages.sso.type=
xslt_messages.sub.domains=
xslt_messages.sync.with.source=
xslt_messages.sync.with.target=
xslt_messages.target.group=
xslt_messages.target.groups=
xslt_messages.text.style=
xslt_messages.type=
xslt_messages.update=
xslt_messages.url=
xslt_messages.user.access=
xslt_messages.user.management=
xslt_messages.username=
xslt_messages.variants=
xslt_messages.workflow.histories=
MPS0010001=æå¤çç³»ç»éè¯¯ãæå³è¯¦ç»ä¿¡æ¯ï¼è¯·åéæ¥å¿ã
MPS0010002=æå¡åç§°æ æã
MPS0010003=æ¨¡åå¯¹è±¡ä¸ºå¿å¡«é¡¹ï¼ä½æ¯ç¼ºå¤±ã
MPS0010004=æ²¡æå¯ç¨ä½æ´æ°ç¨åºçç¨æ· IDã
MPS0010005=æ æ³æç¨æ· ID æ¥æ¾ç¨æ·ã
MPS0010006=å¨éªè¯æå¡æ¶åºç°æå¤å¼å¸¸ã
MPS0010007=åç§°ä¸ºå¿å¡«é¡¹ï¼ä½æ¯ç¼ºå¤±ã
MPS0010008=åç§°å¿é¡»ç±å­æ¯åæ°å­ç»æã
MPS0010009=æ§è¡æå¡æ¶åçæå¤å¼å¸¸ã
MPS0010010=æä»¶ä¸å­å¨ã
MPS0010011=æå¼æä»¶æ¶åºç°å¼å¸¸ã
MPS0010012=æå¡å¨ä½¿ç¨ä¹åæªéªè¯ã
MPS0010014=è¯¥æå¡çæå¡è¯·æ±ç±»åéè¯¯ã
MPS0010015=ææä¾çç¶ææ æã
MPS0010016=æ æ³æäº¤æ°æ®åºäº¤æã
MPS0010017=æ æ³å¯å¨æ°æ®åºäºå¡ã
MPS0010018=ä¸æ¯æè¯¥æä½ã
MPS0020001=æ¶æ¯åå®¹ç±»åå¨æ¶æ¯çææåå®¹ä¸­ä¸ä¸è´ã
MPS0020002=å¿é¡»å­å¨é»è®¤è¯­è¨çåå®¹ã
MPS0020003=å°å¿½ç¥ä¸ååºç¨ç¨åºæ¯æçè¯­è¨çåå®¹ã
MPS0020004=æ æ³æ ID æ¥æ¾åºå
MPS0020005=åç§°åªåºåå«å­æ¯æ°å­å­ç¬¦ãç©ºæ ¼ãç­åçº¿ãä¸åçº¿ã
MPS0020006=æ¶æ¯å¯¼å¥æä½è¢«æç»ï¼å ä¸ºå¶ææ¬åå®¹åå«ä¸åè¢«ç¦æ­¢çå­ç¬¦: CRLFã%% æ |ã
MPS0020008=å¦ä¸ä¸ªå½åç¨æ·å·²ç»æ´æ°äºè¯¥åºåä¸çæ¶æ¯ä¼åçº§ãæ¨çæ´æ¹æ æ³æäº¤ï¼è¯·éè¯ã
MPS0070001=è¯·æ±å¯¹è±¡ä¸è½ä¸º Nullã
MPS0070002=æä»¶åä¸è½ä¸º Null æç©º
MPS0090001=æ§è¡ææå¯é¥æ æã
MPS0090002=æ°æ®å¯¼å¥æ§è¡æ æ³å¨è®¡åæ§è¡çªå£ä¹å¤å¯å¨ã
MPS0090003=ä½ä¸æ æ³å¯å¨ï¼å ä¸ºä¹åçä½ä¸æªå®æã
MPS0090004=ä½ä¸é¡ºåºéè¯¯ã
MPS0090005=å­å¨è¯¥ç§æ·çå¦ä¸ä¸ªæ­£å¨è¿è¡çæ°æ®å¯¼å¥ãå¯¹äºæ¯ä¸ªç§æ·ï¼å¨ä»»ä½ç»å®æ¶é´åªè½è¿è¡ä¸ä¸ªæ°æ®å¯¼å¥æä½ã\t
MPS0090006=å°æªåå¤å¥½è¿è¡æ°æ®å¯¼å¥ãè¯·ç¡®ä¿ææ xml æä»¶é½å·²ç»è¿éªè¯ã
MPS0090007=ä¹åå·²å®æçæ°æ®å¯¼å¥ä½ä¸å°ä¸ä¼éæ°è¿è¡ã
MPS0100001=å¨åå»ºæ°æ®å¯¼å¥æ¶åçæå¤å¼å¸¸ã
MPS0100004=è¾å¥æä»¶çæ°ç®æ æï¼åºè³å°æå®ä¸ä¸ªè¾å¥æä»¶ã
MPS0110001=è¯·æ±å¯¹è±¡ä¸è½ä¸º Nullã
MPS0110003=è¯·æ±å¯¹è±¡ä¸­ç DataImport å¯¹è±¡ä¸º Null
MPS0120001=åå¥ç§æ·æä»¶çåºå ID æ¶åºé - IOException
MPS0120002=åéå¨å¯¼å¥ç¤ºä¾ ZIP æä»¶ä¸­ä½¿ç¨çè¾åºæä»¶æ¶åºé
MPS0120003=å°åç´ åç¼©è³å¯¼å¥æ ·æ¬ ZIP æ¶åºé
MPS0140001=DataImport ä¸è½ä¸º NULL
MPS0140002=ä¸ºæ¹å¤çä½ä¸çæå½ä»¤è¡æ¶åçéè¯¯
MPS0140003=å¿é¡»å®ä¹å±æ§æä»¶ä¸­çè¾åºç®å½\t
MPS0140004=åå¥æ¹å¤çæä»¤æä»¶æ¶åçéè¯¯
MPS0150001=ä½ä¸æåç¨åº - å¨æ°æ®åºä¸­æªåç°ä¼ éäºä»¶
MPS0150002=ä½ä¸æåç¨åº - æ´æ°âä¼ éäºä»¶ä½ä¸âç¶ææ¶åºé
MPS0150003=ä½ä¸æåç¨åº -âä¼ éäºä»¶ä½ä¸âç¶ææ´æ¹ä¸ºâå·²è¯·æ±â
MPS0150004=ä½ä¸æåç¨åº - ä½ä¸åæ°æ®å·²ä¿å­å°æ¥è¡¨æ°æ®åº
MPS0150005=ä½ä¸æåç¨åº - ZIP æä»¶å¯ç¨
MPS0150006=ä½ä¸æåç¨åº -âä¼ éäºä»¶ä½ä¸âç¶ææ´æ¹ä¸ºâå·²æåâ
MPS0160001=å¯¼å¥ä¼ éæ¥è¡¨ - ä»åç«¯è¿ç¨è¿åçä½ä¸åä¸åå«ä¼ éæ¥è¡¨æä»¶ã
MPS0160002=å¯¼å¥ä¼ éæ¥è¡¨ - ä»åç«¯è¿ç¨è¿åçä½ä¸åä¸åå«ææçä¼ éäºä»¶æ è¯ã
MPS0160003=å¯¼å¥ä¼ éæ¥è¡¨ - ä½ä¸å ID åä¼ éäºä»¶ä½ä¸ ID ä¸å¹éã
MPS0160004=å¯¼å¥ä¼ éæ¥è¡¨ - ä¼ éæ¥è¡¨åæéè¯¯ã
MPS0170001=æ¥è¡¨å¤çå¨ - ä¼ éç¶ææä»¶ (CombinedMsgDelivery) å¨ä½ä¸æä»¶å¤¹ä¸­ä¸¢å¤±
MPS0180001=è°ç¨å¤é¨è§¦åäºä»¶ - æ æ³æ GUID å¨æ°æ®åºä¸­æ¥æ¾äºä»¶ãæªè°ç¨äºä»¶ã
MPS0180002=è°ç¨å¤é¨è§¦åäºä»¶ - å¤é¨è§¦åå¨äºä»¶å½åå¤äºç¦ç¨æ¨¡å¼ãæªè°ç¨äºä»¶ã
MPS0180003=è°ç¨å¤é¨è§¦åäºä»¶ - è¿è¡ç¨æ·å¸æ·å¤äºæªæ¿æ´»ç¶æãæªè°ç¨äºä»¶ã
MPS0180004=è°ç¨å¤é¨è§¦åäºä»¶ - å¨åå»ºä¼ éäºä»¶å¯¹è±¡å¹¶å°å¶æå¥æ°æ®åºä¸­æ¶åºéãæªè°ç¨äºä»¶ã
MPS0180005=è°ç¨å¤é¨è§¦åäºä»¶ - å¨æ°æ®åºä¸­æ¾å°æ GUID å½åçå¤é¨è§¦åå¨äºä»¶ãç»§ç»­æä½ä»¥åå»ºäºä»¶ã
MPS0180006=è°ç¨å¤é¨è§¦åäºä»¶ - æåè§¦åäºä»¥ GUID å½åçå¤é¨è§¦åå¨äºä»¶ã
MPS0190001=åªæå·¥ä½ä¸­çæ¬å¯ç­¾å¥è³âæ´»å¨âã
MPS0190002=æ æ³ä»æ°æ®åºä¸­æ£ç´¢è¿è¡äºçæ¬æ§å¶çæ¨¡åå¯¹è±¡ã
MPS0190003=å¯¹è±¡å·²ç­¾åºï¼æå¤äºæ æ³ä»âæ´»å¨âä¸­ç­¾åºçç¶æã
MPS0190004=æ æ³è·åå¯ä¾ç­¾åºçææ°çäº§çæ¬ã
MPS0190005=ä¸æ¯æåéã
MPS0200001=âæå¡è¯·æ±âä¸è½ä¸º Null
MPS0200002=è¯·æå®è¦éæ°åéçæ¶æ¯ã
MPS0200003=è¯·æå®è¦å°å·¥ä½éæ°åéè³çå¯¹è±¡ã
MPS0200004=ç¨æ·æ æå°å·¥ä½éæ°åéè³å¶ä»ç¨æ·ã
MPS0200005=æ²¡æä¸ modelInstance å³èççæ¬æ å°
MPS0210001=å¿é¡»å° modelId æ modelInstanceId ä¼ éå° VersionServiceRequest
MPS0210002=åªè½ç±âåéè³âçç¨æ·æ§è¡å·¥ä½å¯æ¬æ¾å¼æä½ã
MPS0210003=æ æ³ä¸­æ­¢éå·¥ä½å¯æ¬ã
MPS0220001=å¿é¡»å° modelId æ modelInstanceId ä¼ éå° VersionServiceRequestã
MPS0220002=ç¨æ·æ ææ§è¡å­æ¡£ã
MPS0220003=æ æ³å­æ¡£æ¶æ¯ãå®æªå¤äºæ´»å¨ç¶æã
MPS0230001=å¿é¡»å° modelId æ modelInstanceId ä¼ éå° VersionServiceRequestã
MPS0230002=ç¨æ·æ æå é¤å­æ¡£ã
MPS0230003=å¿é¡»åå­æ¡£æ¶æ¯ï¼ç¶åæè½å é¤æ¶æ¯ã
MPS0240001=å·¥ä½ç»åç§°ä¸è½ä¸º nullã
MPS0240002=æè¯·æ±çå·¥ä½ç»åç§°å·²å­å¨äºç¸åçç§æ·ä¸­ã
MPS0240003=å¿é¡»å°å·¥ä½ç»ä¼ éå° UpdateWorkgroupServiceRequestã
MPS0240004=å¦æç§»é¤æè¯·æ±çåºåå³èï¼å°ä½¿ææç³»ç»ç¨æ·æ æ³çå°è¯¥åºåã
MPS0240005=workgroupId ä¸è½å°äºæç­äº 0
MPS0240006=æè¯·æ±çå·¥ä½ç»ä¸å­å¨ã
MPS0240007=æ æ³å é¤æè¯·æ±çå·æå³èåºåçå·¥ä½ç»ã
MPS0240008=æ æ³å é¤æè¯·æ±çå·æå³èç¨æ·çå·¥ä½ç»ã
MPS0240009=ç§æ·å·²å·æé»è®¤çå·¥ä½ç»ã
MPS0250001=è¯·æ±ç¨æ·ä¸æ¯ææç Messagepoint ç¨æ·ã
MPS0250002=ç¶æä»¶å¤¹ä¸æ¯ææçå·¥ä½ç»æä»¶å¤¹ã\t
MPS0250003=å·²è¶åºæå¤§æä»¶å¤¹çº§å«ãæå¤åè®¸äºä¸ªçº§å«ã
MPS0250004=å·¥ä½ç»æä»¶å¤¹åç§°æ¯å¿å¡«å­æ®µã
MPS0250005=å·¥ä½ç»æä»¶å¤¹åç§°å·²è¢«ä½¿ç¨ï¼è¯·æå®å¶ä»åç§°ã
MPS0250006=æ æ³å¨æ°æ®åºä¸­æ¾å°éæ©è¿è¡æ´æ°çåå§æä»¶å¤¹ã
MPS0250007=æ æ³å¯¹æ¶èå¤¹åä¸æ¬¡è®¿é®çé¡µé¢è¿è¡æä»¶å¤¹éå½åã
MPS0250008=å¨å é¤æä»¶å¤¹ä¹åï¼å¿é¡»å é¤æä»¶å¤¹åå®¹ã
MPS0250009=ç³»ç»ç®¡ççæä»¶å¤¹ï¼å¦æ¶èå¤¹ãä¸æ¬¡è®¿é®é¡µé¢ä»¥åé»è®¤ç¨æ·æä»¶å¤¹)ä¸è½å é¤ã
MPS0250010=å¨ä¸ºç¨æ·åå»ºä¸æ¬¡è®¿é®é¡µé¢åæ¶èå¤¹æä»¶å¤¹æ¶åçæå¤éè¯¯ã
MPS0250011=å¨ä¸ºå·¥ä½ç»åå»ºæ°å»ºé¡¹ç®æä»¶å¤¹æ¶åçæå¤éè¯¯ã
MPS0250012=å¨æ¸çç¨æ·æä»¶å¤¹ä»¥å é¤ä¸åå¯¹ç¨æ·å¯è§çæ¶æ¯æ¶åçæå¤éè¯¯ã
MPS0250013=å¨æ¸çå±äºå·¥ä½ç»çæææä»¶å¤¹æ¶åçæå¤éè¯¯ã
MPS0250014=ä¸ºæä»¶å¤¹æ·»å æ¶æ¯æ¶åçæå¤éè¯¯ã
MPS0260001=ä»æ°æ®åºä¸­æ£ç´¢æ¶æ¯æ¶åçæå¤éè¯¯ã
MPS0260002=é¢è§æ°æ®èµæºæªæ­£ç¡®è®¾ç½®ãè¯·ä¸æ¨çæ¥è§¦ç¹ç®¡çåèç³»ã
MPS0270001=å¦ä¸ä¸ªç¨æ·æ­£å¨çç®¡è¯¥ç§æ·
MPS0270002=è¯¥ç§æ·æ²¡æç§æ·çç®¡äºº
MPS0270003=æ æ³å è½½è¯¥ç§æ·
MPS0270004=è¯¥ç§æ·æ­£ç± {0} çç®¡
MPS0280001=æ æ³å¨æ°æ®åºä¸­æ¾å°åæ°ç»æ èç¹ã
MPS0280002=å¯éæ èç¹åç§°å¿é¡»æå®ä¸ä¸è½ä¸ºç©ºã
MPS0280003=æ¨å¿é¡»å·æä¸¤ä¸ªæä¸¤ä¸ªä»¥ä¸å¶ä½åæ°ã
MPS0280004=æ°æ®å¼ {0} æ æ³å é¤ï¼å ä¸ºå®å·²å¨å­åé {1} ä¸­ä½¿ç¨ã
MPS0290001=æ æ³å¨æ°æ®åºä¸­æ¾å°æ¶æ¯åå®¹å³èã
MPS0290002=æ æ³å é¤è¯¥åå®¹åéï¼å ä¸ºå®å·æå­åéï¼è¯·åå é¤è¯¥å­åéã
MPS0290003=æ æ³å é¤è¯¥åå®¹åéï¼å ä¸ºå¨çäº§ä¸­å­å¨æ´»å¨å¯æ¬ã
MPS0300001=æå¥æéå¿é¡»æ¯ä¸ä¸ªæ°å­
MPS0300002=æå¥æéä¸è½ä¸ºé¶ã
MPS0300003=æ æ³æ¾å°æå¥åå®¹çåé¨å¾åã
MPS0300004=æ æ³æ¾å°æå¥åå®¹çåé¨å¾åã
MPS0300005=æ¨æ ææ´æ°æ­¤æå¥ã
MPS0300006=åç§°æ¯å¿å¡«å­æ®µã
MPS0300007=å¨æ°æ®åºä¸­æ¾ä¸å°è¯¥æå¥å¯¹è±¡ã
MPS0300008=å°æªåå¤å¥½æäº¤æå¥å¯¹è±¡ä»¥è¿è¡æ¹åãè¯·ç¡®ä¿å®ä»¬å¤äºéæ´»å¨ç¶æã
MPS0300009=æå¥ææå¥æ¶é´è¡¨æ­£å¨è¿è¡å¤çï¼å°æªæäº¤ä»¥è¿è¡æ¹åãè¯·åæäº¤ä»¥è¿è¡æ¹åã
MPS0300010=æ æ³åç¨æªæ¿æ´»çæå¥ææå¥æ¶é´è¡¨ã
MPS0300011=æå¥ææå¥æ¶é´è¡¨æªéå®ï¼å æ­¤æ æ³éæ°åé
MPS0300012=æ æ³å­æ¡£ï¼å ä¸ºæå¥ææå¥æ¶é´è¡¨å¤äºéè¯¯çç¶æã
MPS0300013=æ¨æ æå­æ¡£æå¥ææå¥æ¶é´è¡¨ã
MPS0300014=æ¢å¤æä½å°ä»åºç¨äºå·²å­æ¡£çæå¥ææå¥æ¶é´è¡¨ã
MPS0300015=æ¨åªè½å é¤å¤äºéæ´»å¨æå­æ¡£ç¶æçæå¥ææå¥æ¶é´è¡¨ã
MPS0300016=æ¨æ æéæ°åéæå¥ã
MPS0300017=æ¨æ æéæ°åéæå¥æ¶é´è¡¨ã
MPS0310001=å¨å³å°åå¸ä»¥ä¾æ¹åçæ¥è§¦ç¹åéä¸­ï¼ä¸ä¸ªæå¤ä¸ªæ¶æ¯å«ææªå®ä¹çåå®¹ç¶æ
MPS0310002={0} å°æªåå¤å¥½æ¿æ´»ï¼å ä¸ºå¼ç¨çåéå·²æ¿æ´»ãè¯·åæ¿æ´»ä»¥ä¸åé: {1}
MPS0310003=æ¨ä¸å¨ä»¥ä¸é¡¹çæ¹åèåè¡¨ä¸­: {0}
MPS0310004=æç»æ¹åä¸è½ä»¥éè¯¯é¡ºåºè¯·æ± - è¯¥åéå°ä»ä¸åæªæ¿æ´»çç¶çº§ä¸­ç»§æ¿åå®¹: {0}
MPS0310005=è¿äºæ¥è§¦ç¹åéæªå¨ç­å¾æ¹å: {0}ãæ¹åè¯·æ±å·²è¢«æç»ã
MPS0310006=è¿äºæ¥è§¦ç¹åéä¸å¨æ¹åæµç¨ä¸­ï¼å æ­¤æ æ³æç»: {0}ã
MPS0310007=æ æ³åå»ºå·¥ä½å¯æ¬ãè¿äºæ¥è§¦ç¹åéå·²å·æå·¥ä½å¯æ¬æèæ²¡ææ¿æ´»çå¯æ¬: {0}ã
MPS0310008=æ¨æ æå é¤æ¥è§¦ç¹åéã
MPS0310009=è¯¥åéæå­çº§ï¼å æ­¤ä¸è½å é¤ã
MPS0310010=æ¨æ æåå»ºå·¥ä½å¯æ¬: {0}ãå·²æç»è¯·æ±ã
MPS0310011=æ¨è¦å é¤çéä¸­åéæ¯ä¸»åéæå·²è¢«å é¤ãå·²æç»è¯·æ±ã
MPS0310012=æç»æ¹åä¸è½ä»¥éè¯¯é¡ºåºè¯·æ± - å¨æ¹åå¶ä»åéä¹åå¿é¡»æ¹åé»è®¤åéã
MPS0310013=æç»æ¹åä¸è½ä»¥éè¯¯é¡ºåºè¯·æ± - ä¸æ¬¡å¿é¡»å¨ä¸ä¸ªçº§å«ä¸æ¹ååé
MPS0310014=å¨å°æ¿æ´»çæ¥è§¦ç¹åéä¸­ï¼ä¸ä¸ªæå¤ä¸ªæ¶æ¯å«ææªå®ä¹çåå®¹ç¶æ
MPS0320001=å¨æ°æ®åºä¸­æ¾ä¸å°è¯¥æ è®°å¯¹è±¡ã
MPS0320002=æ¨æ æéæ°åéæ è®°ã
MPS0330001=æªå¯ç¨æ¥è§¦ç¹ã
MPS0330002=æ¥è§¦ç¹ä¸ç¨äºç»åæ¸ é
MPS0330003=æ¨¡æ¿æä»¶å¿é¡»ä»¥ \"cftemplate.dat\" å½å
MPS0330004=ç»åæä»¶çæ©å±åå¿é¡»ä¸º .pub æ .wfdã
MPS0340001=æ¨ä¸å¨ä»¥ä¸é¡¹çæ¹åèåè¡¨ä¸­: {0}
MPS0340002=è¿äºéä¿¡ä¸å¨æ¹åæµç¨ä¸­ï¼å æ­¤æ æ³æç»: {0}ã
action.button.label.update=ç¼è¾
client_messages.button.add=æ·»å 
client_messages.button.cancel=åæ¶
client_messages.button.cancel_upper=åæ¶
client_messages.button.continue_txt=ç»§ç»­
client_messages.button.copy=å¤å¶
client_messages.button.ok=ç¡®å®
client_messages.button.ok_upper=ç¡®å®
client_messages.button.resolve.variables=å¤çåé
client_messages.button.save=ä¿å­
client_messages.calendar.day_name.friday=ææäº
client_messages.calendar.day_name.monday=ææä¸
client_messages.calendar.day_name.saturday=ææå­
client_messages.calendar.day_name.sunday=ææå¤©
client_messages.calendar.day_name.thursday=ææå
client_messages.calendar.day_name.tuesday=ææäº
client_messages.calendar.day_name.wednesday=ææä¸
client_messages.calendar.day_name_min.friday=å¨äº
client_messages.calendar.day_name_min.monday=å¨ä¸
client_messages.calendar.day_name_min.saturday=å¨å­
client_messages.calendar.day_name_min.sunday=å¨å­
client_messages.calendar.day_name_min.thursday=å¨å
client_messages.calendar.day_name_min.tuesday=å¨å
client_messages.calendar.day_name_min.wednesday=å¨ä¸
client_messages.calendar.day_name_short.friday=å¨äº
client_messages.calendar.day_name_short.monday=å¨ä¸
client_messages.calendar.day_name_short.saturday=å¨å­
client_messages.calendar.day_name_short.sunday=å¨æ¥
client_messages.calendar.day_name_short.thursday=å¨å
client_messages.calendar.day_name_short.tuesday=å¨äº
client_messages.calendar.day_name_short.wednesday=å¨ä¸
client_messages.calendar.month_name.april=åæ
client_messages.calendar.month_name.august=å«æ
client_messages.calendar.month_name.december=åäºæ
client_messages.calendar.month_name.february=äºæ
client_messages.calendar.month_name.january=ä¸æ
client_messages.calendar.month_name.july=ä¸æ
client_messages.calendar.month_name.june=å­æ
client_messages.calendar.month_name.march=ä¸æ
client_messages.calendar.month_name.may=äºæ
client_messages.calendar.month_name.november=åä¸æ
client_messages.calendar.month_name.october=åæ
client_messages.calendar.month_name.september=ä¹æ
client_messages.calendar.month_name_short.april=åæ
client_messages.calendar.month_name_short.august=å«æ
client_messages.calendar.month_name_short.december=åäºæ
client_messages.calendar.month_name_short.february=äºæ
client_messages.calendar.month_name_short.january=ä¸æ
client_messages.calendar.month_name_short.july=ä¸æ
client_messages.calendar.month_name_short.june=å­æ
client_messages.calendar.month_name_short.march=ä¸æ
client_messages.calendar.month_name_short.may=äºæ
client_messages.calendar.month_name_short.november=åä¸æ
client_messages.calendar.month_name_short.october=åæ
client_messages.calendar.month_name_short.september=ä¹æ
client_messages.calendar.next=ä¸ä¸æ­¥
client_messages.calendar.prev=ä¸ä¸æ­¥
client_messages.compound_content_type.all=ææå¹éé¡¹
client_messages.compound_content_type.distinct=æ²¡æéå¤é¡¹
client_messages.compound_content_type.first_non_blank=ç¬¬ä¸ä¸ªéç©ºå¹éé¡¹
client_messages.compound_content_type.non_blank=éç©º
client_messages.compound_content_type.non_blank_and_duplicate=éç©ºæéå¤
client_messages.compound_fmt.and=ä¸
client_messages.compound_fmt.none=æ 
client_messages.compound_fmt.or=æ
client_messages.content_editor.applied_image_name=åºç¨çå¾åå
client_messages.content_editor.bad_graphic_file_type=åªè½ä¸ä¼  GIFãJPGãPDFãPNG å¾å½¢ã
client_messages.content_editor.bad_image_file_type=åªè½ä¸ºè¯¥ä¼ éä¸ä¼  {0} å¾å½¢ã
client_messages.content_editor.character_count=å­ç¬¦è®¡æ°
client_messages.content_editor.checkbox=å¤éæ¡
client_messages.content_editor.checkbox_properties=å¤éæ¡å±æ§
client_messages.content_editor.clear=æ¸é¤
client_messages.content_editor.click=åå»
client_messages.content_editor.click_to_download=åå»âåå®¹æä»¶âé¾æ¥ä»¥ä¸è½½æåºç¨çåå®¹
client_messages.content_editor.cms_last_sync_date=CMS ä¸æ¬¡åæ­¥æ¥æ
client_messages.content_editor.cms_last_update_date=CMS æ´æ°æ¥æ
client_messages.content_editor.constants=å¸¸é
client_messages.content_editor.content_file=åå®¹æä»¶
client_messages.content_editor.content_suppressed=åå®¹å·²ç¦æ­¢
client_messages.content_editor.copy=å¤å¶
client_messages.content_editor.copy_from=å¤å¶èª
client_messages.content_editor.default_field_label_txt=æ ç­¾
client_messages.content_editor.divider=æ°´å¹³åéç¬¦
client_messages.content_editor.edit=ç¼è¾
client_messages.content_editor.font_size=å­ä½å¤§å°
client_messages.content_editor.group=ç»
client_messages.content_editor.horizontal=æ°´å¹³
client_messages.content_editor.image_library=å¾ååº
client_messages.content_editor.image_link=å¾åé¾æ¥
client_messages.content_editor.inherit_from_parent=æ­£å¨ç»§æ¿èªç¶çº§
client_messages.content_editor.insert_character=æå¥å­ç¬¦
client_messages.content_editor.insert_image=æå¥å¾å
client_messages.content_editor.insert_smart_text=æå¥æºè½ææ¬
client_messages.content_editor.insert_variables=æå¥åé
client_messages.content_editor.leave_empty=å°é¨åä¿çä¸ºç©º
client_messages.content_editor.max_length=æå¤§é¿åº¦
client_messages.content_editor.new_guide=æ°å»ºæå
client_messages.content_editor.new_image=æ°å»ºèµäº§
client_messages.content_editor.no_content=æ åå®¹
client_messages.content_editor.no_image=æ å¾å
client_messages.content_editor.no_image_selected=æªéæ©å¾å
client_messages.content_editor.no_limit=æ éå¶
client_messages.content_editor.no_variant_content_to_copy=åéä¸­ä¸åå«è¦å¤å¶çåå®¹
client_messages.content_editor.non_breaking_space=ä¸é´æ­ç©ºæ ¼
client_messages.content_editor.none=æ 
client_messages.content_editor.oops=æ±æ­!
client_messages.content_editor.orientation=æ¹å
client_messages.content_editor.paragraph=æ®µè½
client_messages.content_editor.paragraph_styles=æ®µè½æ ·å¼
client_messages.content_editor.pending_save=ç­å¾ä¿å­
client_messages.content_editor.position=ä½ç½®
client_messages.content_editor.radio=åé
client_messages.content_editor.radio_properties=åéå±æ§
client_messages.content_editor.referencing_image_library=å¼ç¨å¾ååº
client_messages.content_editor.same_as_variant=æ­¤åéæ­£å¨å¼ç¨æ­¤é¡¹çåå®¹:
client_messages.content_editor.select_content_state=è¯·éæ©åå®¹ç¶æ
client_messages.content_editor.shared_content_reference=å±äº«çåå®¹å¼ç¨
client_messages.content_editor.smart_text=æºè½ææ¬
client_messages.content_editor.spellchecker=æ¼åæ£æ¥å¨
client_messages.content_editor.styles=æ ·å¼
client_messages.content_editor.symbols=ç¬¦å·
client_messages.content_editor.table_properties=è¡¨å±æ§
client_messages.content_editor.text_field=ææ¬å­æ®µ
client_messages.content_editor.text_field_properties=ææ¬å­æ®µå±æ§
client_messages.content_editor.to_edit_content_file=ä»¥ä¿®æ¹åå®¹æä»¶
client_messages.content_editor.type=ç±»å
client_messages.content_editor.unknown=æªç¥
client_messages.content_editor.upload_date=ä¸ä¼ æ¥æ
client_messages.content_editor.uploaded=å·²ä¸ä¼ 
client_messages.content_editor.use_content_library=ä½¿ç¨åå®¹åº
client_messages.content_editor.use_same_as_default=ä½¿ç¨ä¸é»è®¤è¯­è¨ç¸åçåå®¹
client_messages.content_editor.use_same_as_system_default=ä½¿ç¨ä¸ç³»ç»é»è®¤è¯­è¨ç¸åçåå®¹
client_messages.content_editor.variables=åé
client_messages.content_editor.vertical=åç´
client_messages.content_type=åå®¹ç±»å
client_messages.error.please_reload_page=æµè§å¨éè¯¯: è¯·å·æ°é¡µé¢ï¼ç¶åéè¯ã
client_messages.format.camel_case=æ¯ä¸ªåè¯çç¬¬ä¸ä¸ªå­ç¬¦å¤§å
client_messages.format.lowercase=å°å
client_messages.format.no_trim=ä¸åªè£
client_messages.format.trim_after=ä¿®åªå
client_messages.format.trim_all=åªè£å¨é¨
client_messages.format.trim_before=åªè£å
client_messages.format.unmodified=æªä¿®æ¹
client_messages.format.uppercase=å¤§å
client_messages.format.uppercase_first_character=ç¬¬ä¸ä¸ªå­ç¬¦å¤§å
client_messages.language.english=è±è¯­
client_messages.language.french=æ³è¯­
client_messages.language.spanish=è¥¿ç­çè¯­
client_messages.text.AND=ä¸
client_messages.text.DAT_and_DAT=PUB å DAT
client_messages.text.DAT_and_WFD=WFD å DAT
client_messages.text.DETAILS=è¯¦ç»ä¿¡æ¯
client_messages.text.HIDE=éè
client_messages.text.OR=æ
client_messages.text.PDF_images_must_be_uploaded_separately=PDF åå¾åæä»¶å¿é¡»åç¬ä¸ä¼ ã
client_messages.text.Proof=è¯æ
client_messages.text.active=æ´»å¨
client_messages.text.add=æ·»å 
client_messages.text.add_and_save=æ·»å å¹¶ä¿å­
client_messages.text.add_items_comma_separate_values=é¡¹ç®: ç¨éå· ',' åå²å¤ä¸ªå¼
client_messages.text.advanced_search=é«çº§æç´¢
client_messages.text.alignment=å¯¹é½
client_messages.text.all=ææ
client_messages.text.alphanumeric=å­æ¯æ°å­
client_messages.text.and=ä¸
client_messages.text.assigned=å·²åé
client_messages.text.audit_report=å®¡æ ¸æ¥è¡¨
client_messages.text.available=å¯ç¨
client_messages.text.bad_request_for_targeting_summary=éå¯¹ç®æ æè¦çéè¯¯è¯·æ±
client_messages.text.bold=ç²ä½
client_messages.text.border=è¾¹æ¡
client_messages.text.cancel=åæ¶
client_messages.text.clear_new_items=æ¸é¤æ°é¡¹ç®
client_messages.text.click_to_download_package=åå»ä»¥ä¸è½½ç°æå
client_messages.text.click_to_view=åå»ä»¥æ¥ç
client_messages.text.clone_of=æ­¤é¡¹çåé:
client_messages.text.close=å³é­
client_messages.text.color=é¢è²
client_messages.text.complete=å®æ
client_messages.text.compound_values=å¤åå¼
client_messages.text.condition_is_referenced_by_target_group=è¯¥æ¡ä»¶è¢«ä¸ä¸ªç®æ ç»å¼ç¨ï¼æ æ³å é¤ã
client_messages.text.conditions=æ¡ä»¶
client_messages.text.connector_name=è¿æ¥å¨åç§°
client_messages.text.constant=å¸¸é
client_messages.text.content=åå®¹
client_messages.text.content_empty=åå®¹ä¸ºç©º
client_messages.text.content_suppressed=åå®¹å·²ç¦æ­¢
client_messages.text.content_type=åå®¹ç±»å
client_messages.text.copy_to_clipboard=å¤å¶å°åªè´´æ¿
client_messages.text.created=å·²åå»º
client_messages.text.currency_symbol=è´§å¸ç¬¦å·
client_messages.text.date=æ¥æ
client_messages.text.days=å¤©æ°
client_messages.text.decimal=å°æ°
client_messages.text.decimal_places=å°æ°ä½æ°
client_messages.text.default_state=é»è®¤
client_messages.text.delimiter=åéç¬¦
client_messages.text.delta=å¢é
client_messages.text.display=æ¾ç¤º
client_messages.text.done=å®æ
client_messages.text.dont_ask_again=ä¸åè¯¢é®
client_messages.text.drag=æå¨
client_messages.text.drag_and_drop_not_applicable_for_composition_file_upload=å¯¹äºç»åæä»¶ä¸ä¼ ï¼ææ¾æä½ä¸å¯ç¨ãå¯æ¹ä¸ºåå»âæ·»å æä»¶...âã
client_messages.text.drag_selection_area=è³éæ©åºåä¹åæä¹å¤
client_messages.text.email=çµå­é®ä»¶
client_messages.text.enabled=å·²å¯ç¨
client_messages.text.enabled_for_content=éå¯¹åå®¹å¯ç¨
client_messages.text.enabled_for_rules=éå¯¹è§åå¯ç¨
client_messages.text.entries=æ¡ç®
client_messages.text.error=éè¯¯
client_messages.text.filter_condition.when=å½
client_messages.text.first=ç¬¬ä¸ä¸ª
client_messages.text.font_name=å­ä½åç§°
client_messages.text.format=æ ¼å¼
client_messages.text.freeform=èªç±æ ¼å¼
client_messages.text.friendly_name=åå¥½åç§°
client_messages.text.hidden=éèï¼
client_messages.text.id=ID
client_messages.text.idptype=IDP ç±»å
client_messages.text.imported=å·²å¯¼å¥
client_messages.text.indent_left_right=ç¼©è¿ï¼å·¦ï¼å³)
client_messages.text.indentation=ç¼©è¿
client_messages.text.inherit_from=æ­£å¨ç»§æ¿èª
client_messages.text.inherited=å·²ç»§æ¿
client_messages.text.inline.first.value=åµå¥ç¬¬ä¸ä¸ªå¼
client_messages.text.insert.as.paragraphs=æå¥ä¸ºæ®µè½
client_messages.text.integer=æ´æ°
client_messages.text.invalid_data=æ æç {0} æ°æ®
client_messages.text.invalid_proofing_data=æ æçæ ¡å¯¹æ°æ®
client_messages.text.italic=æä½
client_messages.text.item=é¡¹ç®
client_messages.text.items=é¡¹ç®
client_messages.text.landscape=æ¨ªå (11 x 8.5)
client_messages.text.last=æåä¸ä¸ª
client_messages.text.layout=å¸å±
client_messages.text.legal_landscape=Legal æ¨ªå (14 x 8.5)
client_messages.text.legal_portrait=Legal çºµå (8.5 x 14)
client_messages.text.legend=å¾ä¾
client_messages.text.less=æ´å°
client_messages.text.letter_case=å¤§å°å
client_messages.text.line_spacing=è¡è·
client_messages.text.list_end_delimiter=åè¡¨ç»æåéç¬¦
client_messages.text.loading=æ­£å¨å è½½...
client_messages.text.loading_CMS_images=æ­£å¨ä» CMS å­å¨åºä¸­å è½½å¾åï¼è¯·ç¨å...
client_messages.text.make=åå»º
client_messages.text.make_possessive=åå»ºæææ ¼
client_messages.text.mandatory=å¿é
client_messages.text.mandatory_insert=å¿éæå¥
client_messages.text.mandatory_inserts=å¿éæå¥
client_messages.text.metatags=åæ è®°
client_messages.text.months=æä»½
client_messages.text.more=æ´å¤
client_messages.text.move=ç§»å¨
client_messages.text.move_zone=ç§»å¨åºå
client_messages.text.multi_language=å¤ç§è¯­è¨
client_messages.text.name=åç§°
client_messages.text.name_for_composition_package_required=å¿é¡»ä¸ºè¯¥ç»åæä»¶åæä¾åç§°
client_messages.text.new_part=æ°å»ºé¨å
client_messages.text.new_zone=æ°å»ºåºå
client_messages.text.next=ä¸ä¸æ­¥
client_messages.text.no=æ 
client_messages.text.no_CMS_images_for_site=æªæ¾å°å½åç«ç¹çå¾åã
client_messages.text.no_inserts_match_search=æ²¡æä¸å½åæç´¢æ¡ä»¶å¹éçæå¥
client_messages.text.no_inserts_selected=å°æªéæ©æå¥
client_messages.text.no_items=--æªå®ä¹é¡¹ç®--
client_messages.text.no_items_selected=å°æªéæ©é¡¹ç®
client_messages.text.no_mandatory_inserts_match_search=æ²¡æä¸å½åæç´¢æ¡ä»¶å¹éçå¿éæå¥
client_messages.text.no_mandatory_inserts_selected=æªéæ©å¿éæå¥
client_messages.text.no_matching_approvers=æ²¡æå¹éçæ¹åè
client_messages.text.no_matching_entries=æªæ¾å°å¹éæ¡ç®
client_messages.text.no_matching_items=æ²¡æä¸å½åæç´¢æ¡ä»¶å¹éçé¡¹ç®
client_messages.text.no_messages_match_search=æ²¡æä¸å½åæç´¢æ¡ä»¶å¹éçæ¶æ¯
client_messages.text.no_messages_selected=å°æªéæ©æ¶æ¯
client_messages.text.no_optional_inserts_match_search=æ²¡æä¸å½åæç´¢æ¡ä»¶å¹éçå¯éæå¥
client_messages.text.no_optional_inserts_selected=å°æªéæ©å¯éæå¥
client_messages.text.no_paragraph_styles_match_search=æ²¡æä¸å½åæç´¢æ¡ä»¶å¹éçæ®µè½æ ·å¼
client_messages.text.no_paragraph_styles_selected=å°æªéæ©æ®µè½æ ·å¼
client_messages.text.no_tags=æ æ è®°
client_messages.text.no_text_styles_match_search=æ²¡æä¸å½åæç´¢æ¡ä»¶å¹éçææ¬æ ·å¼
client_messages.text.no_text_styles_selected=å°æªéæ©ä»»ä½ææ¬æ ·å¼
client_messages.text.no_touchpoints_match_criteria=æ²¡æä¸å½åæç´¢æ¡ä»¶å¹éçæ¥è§¦ç¹
client_messages.text.no_touchpoints_selected=å°æªéæ©ä»»ä½æ¥è§¦ç¹
client_messages.text.no_users_match_criteria=æ²¡æä¸å½åæç´¢æ¡ä»¶å¹éçç¨æ·
client_messages.text.no_users_selected=å°æªéæ©ç¨æ·
client_messages.text.no_variables_match_criteria=æ²¡æä¸å½åæç´¢æ¡ä»¶å¹éçåé
client_messages.text.no_variables_selected=å°æªéæ©åé
client_messages.text.no_variant_matches=æ åéå¹éé¡¹
client_messages.text.no_workgroups_match_criteria=æ²¡æä¸å½åæç´¢æ¡ä»¶å¹éçå·¥ä½ç»
client_messages.text.no_workgroups_selected=å°æªéæ©å·¥ä½ç»
client_messages.text.nodes=å®ä¾
client_messages.text.non_selectable_insert=æ å¯éæå¥
client_messages.text.numeric=æ°å¼
client_messages.text.of=ç
client_messages.text.online=èæº
client_messages.text.only_zip_files_permitted=åªå¯ä»¥ä¸ä¼  zip æä»¶ã
client_messages.text.optional=å¯é
client_messages.text.optional_insert=å¯éæå¥
client_messages.text.optional_inserts=å¯éæå¥
client_messages.text.order_down=éåº
client_messages.text.order_up=ååº
client_messages.text.ordered=å·²æåº
client_messages.text.package_file=åæä»¶
client_messages.text.page_range=é¡µé¢èå´(å¯é)
client_messages.text.part=é¨å
client_messages.text.pending_save=ç­å¾ä¿å­
client_messages.text.per_page=æ¯é¡µ
client_messages.text.permitted_characters=åè®¸å¨æ­¤è¾å¥ä¸­åå«ä»¥ä¸å­ç¬¦
client_messages.text.permitted_file_upload=åªå¯ä»¥ä¸ä¼  {0} ä¸ªæä»¶ã
client_messages.text.pod_type=Pod ç±»å
client_messages.text.pod_url=Pod URL
client_messages.text.point_size=ç¹å¤§å°
client_messages.text.portrait=çºµå (8.5 x 11)
client_messages.text.possessive=æææ ¼
client_messages.text.preview=é¢è§
client_messages.text.previous=ä¸ä¸æ­¥
client_messages.text.priority=ä¼åçº§
client_messages.text.processing=æ­£å¨å¤ç
client_messages.text.proof=è¯æ
client_messages.text.range_to=è³
client_messages.text.recipient=æ¶ä»¶äºº
client_messages.text.referencing_image=å¼ç¨å¾å
client_messages.text.referencing_system_default_content=å¼ç¨ç³»ç»é»è®¤è¯­è¨åå®¹
client_messages.text.referencing_touchpoint_default_content=å¼ç¨é»è®¤è¯­è¨åå®¹
client_messages.text.remove_background_image=ç§»é¤èæ¯å¾å
client_messages.text.remove_section=ç§»é¤è
client_messages.text.request_date=è¯·æ±æ¥æ
client_messages.text.result=ç»æ
client_messages.text.results=ç»æ
client_messages.text.role=è§è²
client_messages.text.rules=è§å
client_messages.text.run_date=è¿è¡æ¥æ
client_messages.text.save_and_edit=ä¿å­åç¼è¾
client_messages.text.save_to_file=ä¿å­å°æä»¶...
client_messages.text.search=æç´¢
client_messages.text.search_content=æç´¢åå®¹
client_messages.text.search_for=å¯¹è±¡
client_messages.text.search_for_approvers=æç´¢æ¹åè
client_messages.text.search_for_touchpoints=æç´¢æ¥è§¦ç¹
client_messages.text.section=è
client_messages.text.segmentation=è®¿é®
client_messages.text.select_date=éæ©æ¥æ
client_messages.text.select_single_item=éæ©è¦æåºçé¡¹ç®
client_messages.text.select_target_section=éæ©ç®æ è
client_messages.text.selected=éä¸­
client_messages.text.sequence=åºå
client_messages.text.show_all=æ¾ç¤ºå¨é¨
client_messages.text.showing=æ¾ç¤º
client_messages.text.showing_zero_entries=æ¾ç¤º 0 æ¡ç®ä¸­ç 0 å° 0
client_messages.text.spacing_before_after=é´è·ï¼åï¼å)
client_messages.text.state=ç¶æ
client_messages.text.status=ç¶æ
client_messages.text.sub_branches=å­å
client_messages.text.tag_cloud=æ è®°äº
client_messages.text.target=ç®æ 
client_messages.text.targeted=ç®æ 
client_messages.text.targeting.AND=ä¸
client_messages.text.targeting.OR=æ
client_messages.text.targeting.between=ä»äº
client_messages.text.targeting.click_to_upload=åå» + ä»¥ä¸ä¼ 
client_messages.text.targeting.days=å¤©
client_messages.text.targeting.file=æä»¶
client_messages.text.targeting.minus=å
client_messages.text.targeting.months=æ
client_messages.text.targeting.plus=å 
client_messages.text.targeting.primary_source=ä¸»è¦æº
client_messages.text.targeting.reference_source=å¼ç¨æº
client_messages.text.targeting.remote_source=è¿ç¨èµæº
client_messages.text.targeting.unknown_type=æªç¥ç±»å
client_messages.text.targeting.upload_data_file=ä¸ä¼ æ°æ®æä»¶
client_messages.text.targeting.user_specified=ç¨æ·æå®
client_messages.text.targeting.value=å¼
client_messages.text.targeting.value_from_file=å¼ï¼èªæ°æ®æä»¶
client_messages.text.targeting.variable=åé
client_messages.text.targeting.years=å¹´
client_messages.text.template_file=æ¨¡æ¿æä»¶
client_messages.text.template_referenced_cannot_be_removed=è¯¥æ¨¡æ¿è¢«åéå¼ç¨ï¼å æ­¤æ æ³å é¤ã
client_messages.text.tenant_assignable_bin=ç§æ·åéç Bin
client_messages.text.text_style_referenced=è¯¥ææ¬æ ·å¼è¢«å¼ç¨ï¼å æ­¤æ æ³å é¤ã
client_messages.text.thousands_separator=åä½åéç¬¦
client_messages.text.timed=è®¡æ¶
client_messages.text.too_many_files_selected=å·²éæ©ä¸ä¸ªä»¥ä¸æä»¶è¿è¡ä¸ä¼ ã
client_messages.text.touchpoint_health_check=æ¥è§¦ç¹è¿è¡ç¶åµæ£æ¥
client_messages.text.trim_on_empty=ä¸ºç©ºæ¶è¿è¡è°æ´
client_messages.text.type=ç±»å
client_messages.text.type_to_search=-- è¦æç´¢çç±»å --
client_messages.text.unable_to_generate_preproof=æ æ³çæé¢æ ¡å¯¹: æ¾ä¸å°çäº§åå®¹ãè¯·èç³»æ¨çç®¡çåã
client_messages.text.unable_to_generate_preproof_view_log=æ æ³çæé¢æ ¡å¯¹: åå»æ¥çæ¥å¿ã
client_messages.text.unable_to_generate_proof=æ æ³çæé¢æ ¡å¯¹: æ¾ä¸å°ææå®æ è¯ç¬¦çæ¶ä»¶äººãè¯·èç³»æ¨çç®¡çåã
client_messages.text.unassignable_bin=æªåé Bin
client_messages.text.underline=ä¸åçº¿
client_messages.text.upload=ä¸ä¼ 
client_messages.text.upload_alternate_template=ä¸ä¼ å¤ç¨æ¨¡æ¿æä»¶
client_messages.text.upload_background_images=åå»æ·»å å¾æ å¯ä¸ä¼ èæ¯å¾åã
client_messages.text.user=ç¨æ·
client_messages.text.user_access=ç¨æ·è®¿é®æé
client_messages.text.user_name=ç¨æ·åç§°
client_messages.text.users=ç¨æ·
client_messages.text.valid_file_upload_types=åªå¯ä»¥ä¸ä¼  {0} ä¸ªæä»¶
client_messages.text.variable=åé
client_messages.text.variable_make_possessive=å½åè¯ä»¥ s ç»æï¼éè¿èªå¨\næ·»å æå·å sï¼ä½¿åéæä¸º\næææ ¼(å¦ David's car æ Jess' car)ã
client_messages.text.variable_trim=å¦ææ­¤åéçå¼ä¸ºç©ºï¼åéæ©å¯¹å©ä½ç©ºæ ¼çç®¡çæ¹å¼ã
client_messages.text.variants=åé
client_messages.text.view_PDF=æ¥ç PDF
client_messages.text.view_data=æ¥çæ°æ®
client_messages.text.view_item=æ¥ç {0}
client_messages.text.view_log=æ¥çæ¥å¿
client_messages.text.view_logs=æ¥çæ¥å¿
client_messages.text.viewing_proof=æ¥çæè¯·æ±çæ ¡å¯¹
client_messages.text.web_font_name=Web å­ä½åç§°
client_messages.text.years=å¹´
client_messages.text.yes=æ¯
client_messages.text.zone=åºå
client_messages.text.zone_properties_have_changed=åºåçå±æ§å·²æ´æ¹ãè¯·å¨ç¼è¾åä¿å­ã
client_messages.title.PDF_upload=ä¸ä¼  PDF
client_messages.title.add_composition_package=æ·»å ç»åå
client_messages.title.add_data_element=æ·»å æ°æ®åç´ 
client_messages.title.add_data_record=æ·»å æ°æ®è®°å½
client_messages.title.add_data_source=æ·»å æ°æ®æº
client_messages.title.add_language=æ·»å è¯­è¨
client_messages.title.add_menu_item=æ·»å èåé¡¹
client_messages.title.add_section=æ·»å è
client_messages.title.add_xml_data_tag=æ·»å  XML æ°æ®æ è®°
client_messages.title.alternate_templates=å¤ç¨æ¨¡æ¿
client_messages.title.compound_smart_text=å¤åæºè½ææ¬
client_messages.title.continue_without_save=ç»§ç»­èä¸ä¿å­ï¼
client_messages.title.data_resource=æ°æ®èµæº
client_messages.title.delete_data_element=å é¤æ°æ®åç´ 
client_messages.title.delete_data_source=å é¤æ°æ®æº
client_messages.title.delete_data_xml_element=å é¤ XML æ°æ®åç´ 
client_messages.title.delete_deta_record=å é¤æ°æ®è®°å½
client_messages.title.edit_attribute=ç¼è¾å±æ§
client_messages.title.edit_composition_package=ç¼è¾ç»åå
client_messages.title.edit_data_element=ç¼è¾æ°æ®åç´ 
client_messages.title.edit_data_record=ç¼è¾æ°æ®è®°å½
client_messages.title.edit_data_source=ç¼è¾æ°æ®æº
client_messages.title.edit_data_xml_element=ç¼è¾ XML æ°æ®åç´ 
client_messages.title.edit_language=ç¼è¾è¯­è¨
client_messages.title.edit_locale=ç¼è¾åºåè®¾ç½®
client_messages.title.external_reporting_data=å¤é¨æ¥è¡¨æ°æ®
client_messages.title.image_library_upload=ä¸ä¼ å¾ååº
client_messages.title.input_filter=è¾å¥ç­éå¨
client_messages.title.insert_attribute=æå¥å±æ§
client_messages.title.insert_data_element=æå¥æ°æ®åç´ 
client_messages.title.insert_message=æå¥æ¶æ¯
client_messages.title.inserts=æå¥
client_messages.title.language=è¯­è¨
client_messages.title.manage_zone_message_priority=ç®¡çåºåæ¶æ¯ä¼åçº§
client_messages.title.messages=æ¶æ¯
client_messages.title.move=ç§»å¨
client_messages.title.move_messages_to=å°æ¶æ¯ç§»å°...
client_messages.title.move_zone_to_section=å°åºåç§»å¨å°è
client_messages.title.note=æ³¨æ
client_messages.title.paragraph_styles=æ®µè½æ ·å¼
client_messages.title.preview=é¢è§
client_messages.title.referenced_text_style=åèçææ¬æ ·å¼
client_messages.title.remove_message=ç§»é¤æ¶æ¯
client_messages.title.save_before_edit=å¨ç¼è¾åä¿å­
client_messages.title.set_modifiers_and_attributes=è®¾ç½®ä¿®é¥°ç¬¦åå±æ§
client_messages.title.set_touchpoints=è®¾ç½®æ¥è§¦ç¹
client_messages.title.tags_for=æ­¤é¡¹çæ è®°:
client_messages.title.targeting=æå®ç®æ 
client_messages.title.template_modifiers=æ¨¡æ¿ä¿®é¥°ç¬¦
client_messages.title.template_variants=æ¨¡æ¿åé
client_messages.title.text_styles=ææ¬æ ·å¼
client_messages.title.touchpoints=æ¥è§¦ç¹
client_messages.title.upload.messages=ä¸ä¼ æ¶æ¯
client_messages.title.upload.variants=ä¸ä¼ åé
client_messages.title.variable=åé
client_messages.title.variables=åé
client_messages.title.variant_visibility=åéå¯è§æ§
client_messages.title.whats_this=è¿æ¯ä»ä¹?
client_messages.title.which.languages=ä»ä¹è¯­è¨?
client_messages.title.workflow_history=å·¥ä½æµåå²è®°å½
client_messages.title.workgroup=å·¥ä½ç»
client_messages.title.workgroups=å·¥ä½ç»
client_messages.title.zone_paragraph_styles=åºåæ®µè½æ ·å¼
client_messages.title.zone_text_styles=åºåææ¬æ ·å¼
client_messages.title.zone_visibility=åºåå¯è§æ§
code.label.datagroup.customer=å®¢æ·
code.log.invalidpassword=å¯ç æ æï¼
code.log.tenantnotactive=ç§æ·æªæ¿æ´»ï¼
code.log.tenantnotmatch=ç¨äºè¯¥ç¨æ·åçç§æ·ä¸æ­£ç¡®ã
code.log.usermaxattemp=è¶åºæå¤§ç»å½å°è¯æ¬¡æ°ï¼
code.log.usernotactive=ç¨æ·æªæ¿æ´»ï¼
code.log.usernotexist=ç¨æ·åæ æï¼
code.text.accessdenied=æ¨æ²¡æè®¿é®è¯¥èµæºçæéï¼
code.text.accountlocked=æ¨å·²è¶è¿æå¤§ç»å½å°è¯æ¬¡æ°ãè¯·ä¸ç®¡çåèç³»ã
code.text.authenticationfailed=æ¨çç»å½å°è¯å·²å¤±è´¥ãå¬å¸ãç¨æ·åæå¯ç ä¸æ­£ç¡®ãè¯·ä¸ç®¡çåèç³»ä»¥è·å¾å¸®å©ã
code.text.company.invalid.group=æ¨çç»å½å°è¯å·²å¤±è´¥ãæ¨æ æ³ç»å½å°æè¯·æ±çå¬å¸ãè¯·ä¸ç®¡çåèç³»ä»¥è·å¾å¸®å©ã
code.text.company.invalid.redirection.info=æ¨çç»å½å°è¯å·²å¤±è´¥ãéå¯¹æè¯·æ±çå¬å¸çéå®åè®¾ç½®æ æãè¯·ä¸ç®¡çåèç³»ä»¥è·å¾å¸®å©ã
code.text.company.invalid=æ¨çç»å½å°è¯å·²å¤±è´¥ãç³»ç»ä¸­ä¸å­å¨æè¯·æ±çå¬å¸ãè¯·ä¸ç®¡çåèç³»ä»¥è·å¾å¸®å©
code.text.company.node.invalid=æ¨çç»å½å°è¯å·²å¤±è´¥ãç³»ç»ä¸­ä¸å­å¨æè¯·æ±çå®ä¾ãè¯·æ£æ¥ URL æä¸ç®¡çåèç³»ä»¥è·å¾å¸®å©ã
code.text.company.node.offline.with.parameters=æè¯·æ±ç \"{0}.{1}\" å¤äºè±æºç¶æãè¯·ç¨åéè¯ï¼æä¸ç®¡çåèç³»ä»¥è·å¾å¸®å©ã
code.text.company.node.offline=æè¯·æ±çå®ä¾å¤äºè±æºç¶æã<br>è¯·ç¨åéè¯ï¼æä¸ç®¡çåèç³»ä»¥è·å¾å¸®å©ã
code.text.company.redirection.error=ç±äºéå®åéè¯¯ï¼æ¨çç»å½å°è¯å·²å¤±è´¥ãè¯·ä¸ç®¡çåèç³»ä»¥è·å¾å¸®å©ã
code.text.company.redirection.info.missing=æ¨çç»å½å°è¯å·²å¤±è´¥ãæ²¡æä¸ºæè¯·æ±çå¬å¸æå®éå®åè®¾ç½®ãè¯·ä¸ç®¡çåèç³»ä»¥è·å¾å¸®å©ã
code.text.company.redirection.pod.unavailable=æ¨çç»å½å°è¯å·²å¤±è´¥ãæè¯·æ±çç³»ç»ç®åä¸å¯ç¨ãè¯·ç¨åéè¯ï¼æä¸ç®¡çåèç³»ä»¥è·å¾å¸®å©ã
code.text.company.transition.redirection.info.missing=æ¨çç»å½å°è¯å·²å¤±è´¥ãæ²¡æéç¨äºæè¯·æ±å¬å¸çé¢åè¡çæ¬ãè¯·ä¸ç®¡çåèç³»ä»¥è·å¾å¸®å©ã
code.text.idpid.invalid=æ¨çç»å½å°è¯å·²å¤±è´¥ãç³»ç»ä¸­ä¸å­å¨æè¯·æ±ç IdpIDãè¯·ä¸ç®¡çåèç³»ä»¥è·å¾å¸®å©ã
code.text.nochanges=æ¾ä¸å°ä»»ä½æ´æ¹ï¼è¯·è¿è¡æ´æ¹ï¼ç¶ååå»âä¿å­âæé®...
code.text.session.timeout=æ¨æªä½¿ç¨æ­¤ä¼è¯æéçç¨æ·åç»å½ï¼æèæ¨çä¼è¯å·²è¶æ¶ã
code.text.signin.unsecure=ç±äºè¿æ¥ä¸å®å¨ï¼æ¨çç»å½å°è¯å·²å¤±è´¥ãè¯·ä½¿ç¨å®å¨çè¿æ¥ã  
code.text.unabletosavesettings=æ±æ­ï¼ç³»ç»æ æ³ä¿å­æ¨çæ´æ¹...
code.text.usersettingssaved=æ¨çæ´æ¹å·²ä¿å­!
email.content.password.change.notification={0}ï¼\n\næ¨ç {1} å¯ç å·²æ´æ¹ã
email.content.password.forget=æ¨å¿è®°äºå¯ç ï¼{0}?\n\næä»¬å·²æ¶å°æ¨è¦å¨å¬å¸å®ä¾ {2} ä¸éç½®å¯ç çè¯·æ±ã\n\nè¦éç½®æ¨çå¯ç ï¼è¯·åå»ä¸é¢çé¾æ¥(æå° URL å¤å¶å¹¶ç²è´´å°æ¨çæµè§å¨ä¸­):\n{1}\n\nä¸é¢çé¾æ¥ä¼å°æ¨å¼å¯¼è³ä¸ä¸ªå¯æ´æ¹å¯ç çå®å¨é¡µé¢ã\n\n\næ¨çç®¡çå
email.content.password.reset.activate=æ¬¢è¿æ¥å° {0}, {1}!\n\nå·²ä½¿ç¨ä¸åå­æ®æ¿æ´»æ¨çå¸æ·:\n\nå¬å¸åç§°: {2}\nç¨æ·å: {3}\n\nè¦è®¾ç½®æ¨çå¯ç ï¼è¯·åå»ä¸é¢çé¾æ¥(æå° URL å¤å¶å¹¶ç²è´´å°æ¨çæµè§å¨ä¸­): \n\n{4}\n\nä¸é¢çé¾æ¥ä¼å°æ¨å¼å¯¼è³ä¸ä¸ªå¯æ´æ¹å¯ç çå®å¨é¡µé¢ã\n\næ¨çç®¡çå
email.content.password.reset=æ¨å¬å¸å®ä¾ {2} çå¯ç å·²éç½®ï¼{0}ã\nè¦åå»ºæ°å¯ç ï¼è¯·åå»ä¸é¢çé¾æ¥(æå° URL å¤å¶å¹¶ç²è´´å°æ¨çæµè§å¨ä¸­):\n\n{1}\n\nä¸é¢çé¾æ¥ä¼å°æ¨å¼å¯¼è³ä¸ä¸ªå¯æ´æ¹å¯ç çå®å¨é¡µé¢ã\n\n\næ¨çç®¡çå
email.content.task.assigned=å·²åæ¨åéäºä¸åä»»å¡: \n\n\tåç§°: {0}\n\tæéæä½: {1}\n\tæªæ­¢æ¥æ: {2}\n\næå³è¯¦ç»ä¿¡æ¯ï¼è¯·ç»å½å° {3} ä»¥æ¥çä»»å¡ã
email.subject.password.change.notification={0} å¯ç æ´æ¹éç¥
email.subject.password.forget=å¿è®° {0} å¯ç ?
email.subject.password.reset.activate=æ¨ç {0} å¬å¸å®ä¾ {1} çå¸æ·å·²æ¿æ´»ã
email.subject.password.reset={0} å¯ç éç½®
email.subject.task.assigned={1} ä»»å¡å·²åé: {0}
email.workflow.approval.content.detail={0}ï¼\n\nä¸å {1} å¨ç­å¾æ¨æ¹å:\n\nåç§°: {2}\næä½: {3}
email.workflow.approval.content.dueby=\næªæ­¢æ¥æ: {0}
email.workflow.approval.subject={1}: {0} - ç­å¾æ¹å
email.workflow.approvedbyother.content={0}ï¼\n\nä¸å {1} å·²æ¹å:\n\nåç§°: {2}\næä½: {3}\n
email.workflow.approvedbyother.subject={1}: {0} - å·²æ¹å
email.workflow.common.content.footer=æå³æ´å¤è¯¦ç»ä¿¡æ¯ï¼è¯·ç»å½å° {0} ä»¥è¿è¡æ¥çã
email.workflow.common.content.footerwithurl=\n\næå³æ´å¤è¯¦ç»ä¿¡æ¯ï¼è¯·åå»ä¸é¢çé¾æ¥(æå° URL å¤å¶å¹¶ç²è´´å°æ¨çæµè§å¨ä¸­)ä»¥æ¥ç: \n\n{0}\n\n
email.workflow.duebyautoapprove.content={0}ï¼\n\nä¸å {1} å·²èªå¨æ¹å:\n\nåç§°: {2}\næä½: {3}\n
email.workflow.duebyautoapprove.subject={1}: {0} - å·²æ¹å
email.workflow.duebynotify.content={0}ï¼\n\nä¸å {1} çæ¹åæ¶é´èå´å·²è¿æ: \n\nåç§°: {2}\næä½: {3}\n
email.workflow.duebynotify.subject={1}: {0} - æ¹åæ­¥éª¤è¶æ¶
email.workflow.reassign.content={0}ï¼\n\nä¸å {1} å·²éæ°åéç»æ¨:\n\nåç§°: {2}\næ³¨é: {3}\n
email.workflow.reassign.subject={1}: {0} - å·²éæ°åé
email.workflow.reject.content={0}ï¼\n\nä¸å {1} å·²è¢«æç»ï¼å¹¶ä¸ç°å¨å·²åéç»æ¨:\n\nåç§°: {2}\næä½: {3}\n
email.workflow.reject.subject={1}: {0} - æ¹åè¢«æç»
error.branch.can.not.be.deleted=æ æ³å é¤å
error.branch.code.must.be.entered=å¿é¡»è¾å¥åä»£ç 
error.branch.code.must.be.max.4.long=åä»£ç é¿åº¦ä¸è½è¶è¿ 4
error.branch.code.must.be.unique=åä»£ç å¿é¡»æ¯å¯ä¸ç
error.branch.max.number.of.domain.exceeded.under.your.licence=æ ¹æ®æ¨çè®¸å¯è¯ï¼å·²è¶è¿æå¤§åæ°éãæå³æ´å¤ä¿¡æ¯ï¼è¯·èç³»ç®¡çåã
error.branch.max.number.of.production.instances.exceeded.under.your.licence=æ ¹æ®æ¨çè®¸å¯è¯ï¼å·²è¶è¿æå¤§çäº§å®ä¾æ°éã
error.branch.max.number.of.sandbox.instances.exceeded.under.your.licence=æ ¹æ®æ¨çè®¸å¯è¯ï¼å·²è¶è¿æå¤§æ²çæ°éã
error.branch.must.be.selected=å¿é¡»éæ©å
error.branch.name.must.be.unique=ååå¿é¡»æ¯å¯ä¸ç
error.branch.parent.domain.configuration.is.not.completed=è¿æªå®æç¶åéç½®ã
error.branch.ssoidpid.must.be.entered=å¿é¡»è¾å¥ SSO IdP ID
error.branch.ssosecretkey.must.be.entered=å¿é¡»è¾å¥ SSO ç§é¥ 
error.cannot.change.insert.selector.document.in.use.by.insert.schedules=æ æ³æ´æ¹æ¥è§¦ç¹æå¥éæ©å¨ï¼å ä¸ºå®æ­£ç±ä»¥ä¸æå¥æ¶é´è¡¨ä½¿ç¨: {0}
error.cannot.change.insert.selector.document.in.use.by.inserts=æ æ³æ´æ¹æ¥è§¦ç¹æå¥éæ©å¨ï¼å ä¸ºå®æ­£ç±ä»¥ä¸æå¥ä½¿ç¨: {0}
error.cannot.change.primary.driver.once.communications.authored=ä¸æ¦ææéä¿¡ï¼å°æ æ³æ´æ¹ä¸»é©±å¨ç¨åºæ¡ç®ãè¯·å é¤æ­¤æ¥è§¦ç¹çææéä¿¡ï¼ç¶åéè¯ã
error.communication.must.specify.positive.integer.number.copies=å¿é¡»è³å°ä¸ºä¸ä¸ª (1) å¯æ¬æåº
error.company.notfound=æ±æ­ï¼æ¨è¾å¥çå¬å¸ä¸å­å¨ãè¯·éè¯...
error.composition.package.is.being.referenced.on.delete=æéç»ååæ­£è¢«å¼ç¨ï¼å æ­¤æ æ³å é¤ã
error.condition.name.cannot.be.empty=æ¡ä»¶åç§°ä¸è½ä¸ºç©ºã
error.condition.variable.not.selected={0} æªéæ©åéä»¥è¿è¡æ¯è¾ã
error.constant.language.override.value.empty=è¯·è¾å¥è¯­è¨æ¿ä»£çå¼
error.content.library.is.being.referenced.on.archive.delete=è¯¥å¾ååºé¡¹ç®çä¸ä¸ªå®ä¾æ­£è¢«å¼ç¨ãå¨å é¤ä¹åï¼è¯·å é¤ææå¼ç¨ã
error.content.library.is.being.referenced.on.archive=æéå¾åæ­£ç±ç³»ç»ä¸­çèµäº§å¼ç¨ãè¯·å é¤å¼ç¨ä»¥å°å¾åå­æ¡£ã
error.dataadmin.another.tag.start.customer=å¦ä¸ä¸ª xml æ°æ®æ è®° {0} å·²ç»å¨æ°æ®æº {1} ä¸­å¯å¨ç¨æ·ã
error.dataadmin.data.group.inconsistance=æ°æ®ç» {0} ä¸æ¯æ°æ®ç» {1} (å¶ä¸­åå«ä¸ä¸ª xml æ è®°ä¸çº§)çå­çº§ã
error.dataadmin.data.group.not.seleted=å¦æ xml æ è®°å¯å¨æ°æ®ç»ï¼åå¿é¡»éæ©ä¸ä¸ªæ°æ®ç»ã
error.dataadmin.data.group.startedbyanothertag=æ°æ®ç» {0} ç± xml æ è®° {1} å¯å¨ã
error.dataadmin.data.tag.child.tag.exist=æ­¤æ è®°å·²å·æä¸ä¸ªå³èæ è®°ãæ¨æ æ³æ·»å æ°æ®åç´ ã
error.dataadmin.data.tag.value.data.exist=æ­¤æ è®°å·²å·ææ°æ®åç´ ãæ¨ä¸è½æ·»å ä»»ä½å¶ä»æ°æ®åç´ ææ è®°ã
error.dataadmin.not.descendent.start.customer=è¯¥æ è®°çä¸çº§æªå¯å¨âå®¢æ·âæ°æ®ãæ­¤æ è®°ä¸è½éå¤ã
error.document.cannot.be.shared.and.enabled.for.tp.selection.management=æ æ³åæ¶ä¸ºæ¥è§¦ç¹åéç®¡çç¨åºå±äº«åå¯ç¨æ¥è§¦ç¹
error.document.cannot.change.tp.selection.management.selector=æ æ³æ´æ¹æ¥è§¦ç¹åéç®¡çç¨åºï¼å ä¸ºå·²ä¸ºå¶åå»ºåé/æ¶æ¯
error.document.cannot.turn.off.tp.selection.management=æ æ³ä¸ºæ¥è§¦ç¹åéç®¡çç¨åºç¦ç¨æ¥è§¦ç¹ï¼å ä¸ºå·²ä¸ºå¶åå»ºåé/æ¶æ¯
error.document.composition.version.invalid=ç»åçæ¬æ æ
error.document.composition.version.required=è¯·è¾å¥ä¸ä¸ªç»åçæ¬ç¼å·
error.document.connector.selection.required=å¿é¡»éæ©è¿æ¥å¨
error.document.has.optional.messages.cannot.change.to.message.selectable=æ¥è§¦ç¹å·æå¯éçå¨å±ä¿¡æ¯ï¼å æ­¤æ æ³è½¬æ¢ä¸ºçµæ´»æ¥è§¦ç¹ã
error.document.in.use.by.insert.schedules=æ æ³ä¸ºæå¥ç¦ç¨æ¥è§¦ç¹ï¼å ä¸ºä¸åæå¥æ¶é´è¡¨æ­£å¨ä½¿ç¨æ¥è§¦ç¹: {0}
error.document.in.use.by.inserts=æ æ³ä¸ºæå¥ç¦ç¨æ¥è§¦ç¹ï¼å ä¸ºä¸åæå¥æ­£å¨ä½¿ç¨æ¥è§¦ç¹: {0}
error.document.name.unique=æ¥è§¦ç¹åç§°å·²ä½¿ç¨ãè¯·è¾å¥å¯ä¸çåç§°ã
error.document.proof.data.range.too.large=æ ¡å¯¹æ°æ®çå®¢æ·èå´è¶åºç³»ç»å±æ§æåè®¸çèå´
error.document.proofing.data.customer.range.not.provided=å¨ä¿å­æ´æ¹åï¼å¿é¡»ä¸ºå®¢æ·è®°å½æ°éèå´æä¾å¼ï¼ç¨äºéè¿æéæ°æ®èµæºè¿è¡æ ¡å¯¹
error.document.proofing.data.range.must.be.numbers.greater.than.zero=å®¢æ·è®°å½æ°éèå´å¿é¡»ä»äº 0 å 99,999,999 ä¹é´
error.document.proofing.data.upper.range.must.be.greater.than.lower.range=å®¢æ·è®°å½æ°éçä¸éèå´å¿é¡»å¤§äºæç­äºä¸éèå´
error.email.template.file.format=åªåè®¸ä½¿ç¨ ZIP æ ¼å¼åæ¥ä¸ä¼ æ¨¡æ¿
error.emailnotfound=æ±æ­ï¼æªæ¾å°æ¨è¾å¥ççµå­é®ä»¶å°åãè¯·éè¯...
error.embedded.content.has.advanced.content.cannot.make.global=å¨è½¬æ¢å°å¨å±ä¹åï¼ä»åå®¹ä¸­å é¤ææåéãæºè½ææ¬ãææ¬æ ·å¼åæ®µè½æ ·å¼ã
error.embedded.content.has.targeting.cannot.make.global=å¨å é¤ç®æ æå®ä¹åï¼ä¸è½å°æºè½ææ¬è½¬æ¢ä¸ºå¨å±ã
error.embedded.content.has.timing.cannot.make.global=å¨å é¤æ¶é´ä¹åï¼ä¸è½å°æºè½ææ¬è½¬æ¢ä¸ºå¨å±ã
error.embedded.content.id.notfound=æ±æ­ï¼æ¾ä¸å°ææå®çåµå¥å¼åå®¹ IDã
error.embedded.content.is.being.referenced.on.archive.delete=è¯¥æºè½ææ¬çä¸ä¸ªå®ä¾æ­£è¢«å¼ç¨ãè¯·å¨å é¤ä¹åï¼å é¤ææå¼ç¨ã
error.embedded.content.is.being.referenced.on.archive=æéæºè½ææ¬æ­£è¢«ç³»ç»ä¸­çèµäº§å¼ç¨ãè¯·å é¤å¼ç¨ä»¥è¿è¡å­æ¡£ã
error.embedded.content.selector.not.mapped.to.touchpoints=å¨ææºè½ææ¬ä¸è½ç±æå®éæ©å¨æ´æ¹ï¼å ä¸ºå®æªæ å°å°æéçæ¥è§¦ç¹
error.embedded.content.touchpoint.can.not.removed=ååºçæ¥è§¦ç¹åºå¨æéæ¥è§¦ç¹åè¡¨ä¸­ï¼å ä¸ºè¯¥æºè½ææ¬å·²å¼ç¨å®ä»¬:
error.embedded.content.variables.not.mapped.to.this.embedded.text.content=å¨è¯¥æºè½ææ¬ä¸­ä½¿ç¨çä¸ä¸ªæå¤ä¸ªåéæªæ­£ç¡®æ å°ãè¯·ä¸ºåéçæ¥è§¦ç¹è§£æåéæ å°ã\n\n
error.emptyemail=æ±æ­ï¼çµå­é®ä»¶ä¸ºç©ºã è¯·éè¯...
error.enter.valid.value=è¯·è¾å¥ææå¼
error.export.message.filter.date.cannot.be.in.future=æ¥æä¸è½æ¯å°æ¥çæ¶é´
error.export.message.filter.date.required=å½å¯ç¨ç­éå¨æ¶ï¼æ¥æä¸ºå¿å¡«é¡¹ã
error.export.touchpointselection.filter.date.cannot.be.in.future={0} ä¸è½æ¯å°æ¥çæ¶é´ã
error.export.touchpointselection.filter.date.required=å½å¯ç¨ {0} ç­éå¨æ¶ï¼{0} ä¸ºå¿å¡«é¡¹ã
error.export.touchpointselection.fromdate.cannot.be.after.todate=å¼å§æ¥æä¸è½æäºæªè³æ¶é´ï¼è¯·éæ©ææçæ¥ææ¶é´ã
error.image.library.has.advanced.content.cannot.make.global=å¨è½¬æ¢å°å¨å±ä¹åï¼ä»åå®¹ä¸­å é¤ææåéãå¾ååºãææ¬æ ·å¼åæ®µè½æ ·å¼ã
error.image.library.selector.not.mapped.to.touchpoints=å¾ååºä¸è½ç±æå®éæ©å¨æ´æ¹ï¼å ä¸ºå®æªæ å°å°æéçæ¥è§¦ç¹
error.image.library.touchpoint.can.not.removed=ååºçæ¥è§¦ç¹åºå¨æéæ¥è§¦ç¹åè¡¨ä¸­ï¼å ä¸ºè¯¥å¾ååºå·²å¼ç¨å®ä»¬:
error.image.library.variables.not.mapped.to.this.embedded.text.content=å¨è¯¥æºè½ææ¬ä¸­ä½¿ç¨çä¸ä¸ªæå¤ä¸ªåéæªæ­£ç¡®æ å°ãè¯·ä¸ºåéçæ¥è§¦ç¹è§£æåéæ å°ã\n\n
error.image.library.variables.not.mapped.to.this.image.library.content=å¨è¯¥å¾åä¸­ä½¿ç¨çä¸ä¸ªæå¤ä¸ªåéæªæ­£ç¡®æ å°ãè¯·ä¸ºåéçæ¥è§¦ç¹è§£æåéæ å°ã\n\n
error.image.library.variables.not.mapped=è¯¥å¾åä¸­ä¸ä¸ªæå¤ä¸ªåéæªæ­£ç¡®æ å°ãè¯·ä¸ºæå®çæ¥è§¦ç¹è§£å³åéæ å°ã
error.import.datasourceassociationisnull=æ°æ®éåä¸º nullãè¯¥æä»¶æ æ³å¯¼å¥ã
error.import.datasourceassociationuniquename=æ°æ®éååç§°å·²ä½¿ç¨ãè¯¥æä»¶æ æ³å¯¼å¥ã
error.import.file.is.a.container=æä»¶ \"{0}\" æ¯å®¹å¨çå¯¼åºæä»¶ãè¯·éæ©è¦å¯¼å¥çæ¥è§¦ç¹æä»¶ã
error.import.file.is.an.old.version=æä»¶ \"{0}\" ä¸æ¯âæ¥è§¦ç¹âå¯¼å¥ææ¯æççæ¬ã
error.import.file.is.empty=è¾å¥å­æ®µä¸ºç©ºãè¯·éæ©è¦å¯¼å¥çæä»¶.
error.import.file.is.not.valid=æä»¶ \"{0}\" æ æã
error.import.in.process=å·²å¨è¿è¡å¯¼å¥ã
error.input.charrestriction={0} åªè½åå«ä»¥ä¸å­ç¬¦: {1}
error.input.email.charrestriction={0} å«ææ æççµå­é®ä»¶å°åå¼ï¼æ­£ç¡®æ ¼å¼ä¸º <EMAIL>
error.input.mandatory={0} ä¸ºå¿éç
error.input.maxlength={0} çé¿åº¦ä¸è½è¶è¿ {1} å­ç¬¦
error.input.maxvalue={0} çæå¤§å¼ä¸º {1}
error.input.minlength={0} å¿é¡»è³å°ä¸º {1} å­ç¬¦é¿åº¦
error.input.minvalue={0} çæå°å¼ä¸º {1}
error.input.numberofdecimals.range=å°æ°ä½å¼çæ°å­åªè½ä»äº 0 å 6 ä¹é´
error.input.script.injection={0} åå«éæ³å­ç¬¦åºå
error.insert.cannot.be.archived.in.use.by.active.schedules.for.list={0} å¯è½æ æ³å­æ¡£ï¼å ä¸ºå®å¨ä¸ä¸ªå·²æ¿æ´»æå³å°æ¿æ´»çæå¥æ¶é´è¡¨ä¸­è¢«åéç» Binãè¦å­æ¡£ï¼æå¥å¿é¡»é¦åä¸åå«å½åæå°æ¥æ¶é´çæææå¥æ¶é´è¡¨åç¦»ã
error.insert.cannot.be.archived.in.use.by.active.schedules=è¯¥æå¥å¯è½æ æ³å­æ¡£ï¼å ä¸ºå®å¨ä¸ä¸ªå·²æ¿æ´»æå³å°æ¿æ´»çæå¥æ¶é´è¡¨ä¸­è¢«åéç» Binãè¦å­æ¡£ï¼è¯¥æå¥å¿é¡»é¦åä¸åå«å½åæå°æ¥æ¶é´çæææå¥æ¶é´è¡¨åç¦»ã
error.insert.cannot.be.deactivated.in.use.by.active.schedules.for.list=æ æ³ç¦ç¨ {0}ï¼å ä¸ºä¸åæ´»å¨/å¾æ¹åçæå¥æ¶é´è¡¨æ­£å¨ä½¿ç¨å®: {1}
error.insert.cannot.be.deactivated.in.use.by.active.schedules=æ æ³ç¦ç¨è¯¥æå¥ï¼å ä¸ºä¸åæ´»å¨/å¾æ¹åçæå¥æ¶é´è¡¨æ­£å¨ä½¿ç¨å®: {0}
error.insert.cannot.be.discarded.in.use.by.schedules.for.list=æ æ³æ¾å¼ {0}ï¼å ä¸ºä¸åæå¥æ¶é´è¡¨æ­£å¨ä½¿ç¨å®: {1}
error.insert.cannot.be.discarded.in.use.by.schedules=æ æ³æ¾å¼è¯¥æå¥ï¼å ä¸ºä¸åæå¥æ¶é´è¡¨æ­£å¨ä½¿ç¨å®: {0}
error.insert.firstpage.weight.range=è¯·ä¸ºç¬¬ä¸é¡µæéè¾å¥ä»äº 0.0001 å 99.9999 ä¹é´çæ°å­
error.insert.list.search.requirement=è¦è¿è¡æç´¢ï¼å¿é¡»æå®åç§°æ ID å¼
error.insert.name.unique=åç§°å·²ä½¿ç¨ãè¯·è¾å¥å¯ä¸çåç§°ã
error.insert.otherpages.weight.range=è¯·å¶ä»é¡µé¢æéè¾å¥ä»äº 0.0001 å 99.9999 çæ°å­
error.insert.selectdocument=è¯·éæ©æ¥è§¦ç¹
error.insert.stockid.unique=åºå­ ID å·²è¢«ä½¿ç¨ãè¯·éæ©å¯ä¸çåºå­ IDã
error.insert.weight.range=è¯·ä¸ºæéè¾å¥ä»äº 0.0001 å 99.9999 ç æ°å­
error.insertschedule.cannot.roll.forward.selectors.combination.is.in.use.by.another.schedule.for.list={0} æ æ³åæ»ï¼å ä¸ºå®çä¸ä¸ªéæ©å¨ (\"{1}\") å·²è¢«å¦ä¸ä¸ªåä¸º \"{2}\" æ¶é´è¡¨ä½¿ç¨
error.insertschedule.cannot.roll.forward.selectors.combination.is.in.use.by.another.schedule=è¯¥æ¶é´è¡¨æ æ³åæ»ï¼å ä¸ºå®çä¸ä¸ªéæ©å¨ (\"{0}\") å·²è¢«å¦ä¸ä¸ªåä¸º \"{1}\" æ¶é´è¡¨ä½¿ç¨
error.insertschedule.end.date.mandatory.for.release.for.approval.for.list=å¨æ¨åå¸ {0} ä»¥è¿è¡æ¹åä¹åï¼å¿é¡»è®¾ç½®å¶ç»ææ¥æ
error.insertschedule.end.date.mandatory.for.release.for.approval=å¨æ¨åå¸æå¥æ¶é´è¡¨ä»¥è¿è¡æ¹åä¹åï¼å¿é¡»è®¾ç½®è¯¥æå¥æ¶é´è¡¨çç»ææ¥æ
error.insertschedule.end.date.mandatory.for.release.for.tenants.for.list=å¨å° {0} åå¸ç»ç§æ·ä¹åï¼å¿é¡»è®¾ç½®å¶ç»ææ¥æ
error.insertschedule.end.date.mandatory.for.release.for.tenants=å¨æ¨å°æå¥æ¶é´è¡¨åå¸ç»ç§æ·ä¹åï¼å¿é¡»è®¾ç½®è¯¥æå¥æ¶é´è¡¨çç»ææ¥æ
error.insertschedule.enddate.after.next.schedule.start.date=ç»ææ¥æå¿é¡»æ©äºä¸ä¸ä¸ªæå¥æ¶é´è¡¨çå¼å§æ¥æ
error.insertschedule.insert.timings.invalid={0} çç»ææ¥æä¸è½æ©äºå¼å§æ¥æ
error.insertschedule.insert.timings.prior.to.schedule.timing={0} æ§è¡æ¶é´æ æï¼å ä¸ºå®æ©äºæ¶é´è¡¨æ¶é´
error.insertschedule.insert.timings.start.date=è¯·ä¸º {0} æä¾å¼å§æ¥æ
error.insertschedule.list.search.requirement=è¦è¿è¡æç´¢ï¼å¿é¡»æå®åç§°ãID æå³é®å­å¼
error.insertschedule.no.inserts.for.list={0} æ æ³åå¸ä»¥è¿è¡æ¹åï¼å ä¸ºæ²¡æä¸ºå®ç bin åéæå¥
error.insertschedule.no.inserts=æå¥æ¶é´è¡¨æ æ³åå¸ä»¥è¿è¡æ¹åï¼å ä¸ºæ²¡æä¸ºå®ç Bin åéæå¥
error.insertschedule.no.selector.provided.for.release.for.approval.for.list=å¨åå¸ {0} ä»¥è¿è¡æ¹åä¹åï¼è¯·æä¾éæ©æ°æ®
error.insertschedule.no.selector.provided.for.release.for.approval=å¨åå¸æå¥æ¶é´è¡¨ä»¥è¿è¡æ¹åä¹åï¼è¯·æä¾éæ©æ°æ®
error.insertschedule.no.selector.provided.for.release.for.use.for.list=å¨åå¸ {0} ä»¥ä¾ä½¿ç¨ä¹åï¼è¯·æä¾éæ©æ°æ®
error.insertschedule.no.selector.provided.for.release.for.use=å¨åå¸æå¥æ¶é´è¡¨ä»¥ä¾ä½¿ç¨ä¹åï¼è¯·æä¾éæ©æ°æ®
error.insertschedule.numberofbins.click.set=è¯·åå»âè®¾ç½®âä»¥æå® Bin çæ°é
error.insertschedule.numberofbins.invalid=è¯·ä¸º Bin æ°éè¾å¥ææçéé¶æ°å­
error.insertschedule.numberofsheets.invalid=è¯·ä¸ºè¡¨æ°éè¾å¥ææçéé¶æ°å­
error.insertschedule.rate.sheet.mandatory.for.release.for.approval.for.list=å¨æ¨åå¸å®ä»¥è¿è¡æ¹åä¹åï¼{0} éè³å°å·æä¸ä¸ªè¯ä»·è¡¨
error.insertschedule.rate.sheet.mandatory.for.release.for.approval=å¨æ¨åå¸æå¥æ¶é´è¡¨ä»¥è¿è¡æ¹åä¹åï¼è¯¥æå¥æ¶é´è¡¨éè³å°å·æä¸ä¸ªè¯ä»·è¡¨
error.insertschedule.rate.sheet.mandatory.for.release.to.tenants=å¨å°æå¥æ¶é´è¡¨åå¸å°ç§æ·ä¹åï¼è¯¥æå¥æ¶é´è¡¨éè³å°å·æä¸ä¸ªè¯ä»·è¡¨
error.insertschedule.rate.sheet.mandatory=è¯·è³å°æ·»å ä¸ä¸ªè¯ä»·è¡¨
error.insertschedule.scheduleid.unique=æ¶é´è¡¨ ID å·²è¢«ä½¿ç¨ãè¯·è¾å¥å¯ä¸çæ¶é´è¡¨ ID
error.insertschedule.selectors.add.not.all.values.provided=è¯·æä¾ææéæ©å¨å¼
error.insertschedule.selectors.combination.is.added.before=\"{0}\" ä¹åå·²æ·»å 
error.insertschedule.selectors.combination.is.in.use.by.another.schedule=æ æ³æ·»å  \"{0}\"ï¼å ä¸º å®å·²è¢«å¦ä¸ä¸ªåä¸º \"{1}\" çæ¶é´è¡¨ä½¿ç¨
error.insertschedule.selectors.remove.no.value.provided=è¯·è³å°æä¾ä¸ä¸ªéæ©å¨å¼
error.insertschedule.selectors.remove.no.value.to.remove=æ²¡æä¸ºè¯¥æ¶é´è¡¨å®ä¹éæ©å¨æ°æ®
error.insertschedule.startdate.before.previous.schedule.end.date=å¼å§æ¥æå¿é¡»æäºä¹åæå¥æ¶é´è¡¨çç»ææ¥æ
error.insertschedule.using.inactive.inserts.for.list=æ æ³åå¸ {0} ä»¥è¿è¡æ¹åï¼å ä¸ºå®æ­£å¨ä½¿ç¨ä¸åéæ´»å¨æå¥: {1}
error.insertschedule.using.inactive.inserts=æ æ³åå¸è¯¥æå¥æ¶é´è¡¨ä»¥è¿è¡æ¹åï¼å ä¸ºå®æ­£å¨ä½¿ç¨ä¸åéæ´»å¨æå¥: {0}
error.invalidcompany=æ±æ­ï¼æ¨è¾å¥çå¬å¸æ æãè¯·éè¯...
error.invalidemail=æ±æ­ï¼æ¨è¾å¥ççµå­é®ä»¶å°åæ æãè¯·éè¯...
error.invalidusername=æ±æ­ï¼æ¨è¾å¥çç¨æ·åæ æãè¯·éè¯...
error.listed.touchpoints.must.be.selected.due.to.image.library.reference=ååºçæ¥è§¦ç¹åºå¨æéæ¥è§¦ç¹åè¡¨ä¸­ï¼å ä¸ºè¯¥å¾åå·²å¼ç¨è¿äºæ¥è§¦ç¹:
error.listed.touchpoints.must.be.selected.due.to.reference=ååºçæ¥è§¦ç¹åºå¨æéæ¥è§¦ç¹åè¡¨ä¸­ï¼å ä¸ºè¯¥æºè½ææ¬å·²å¼ç¨äºè¿äºæ¥è§¦ç¹:
error.maintenance.enddate.must.be.entered=å¿é¡»è¾å¥ç»ææ¥æ
error.maintenance.endtime.cannot.be.same=å¼å§æ¥æåç»ææ¥æä¸è½ç¸å
error.maintenance.message.must.be.entered=å¿é¡»è¾å¥æ¶æ¯
error.maintenance.startdate.must.be.before.enddate=å¼å§æ¥æå¿é¡»æ©äºç»ææ¥æ
error.maintenance.startdate.must.be.entered=å¿é¡»è¾å¥å¼å§æ¥æ
error.maintenance.time.cannot.set.to.past=æ¥æä¸è½è®¾ç½®ä¸ºè¿å»çæ¶é´
error.message.admin.role.rolenameused=è§è²åå·²è¢«ä½¿ç¨ã
error.message.admin.securitySettings.maxattempts=âå¨å¶åéå®âåºä¸ºä»äº 3 å° 12 ä¹é´çæ°å­ã
error.message.admin.securitySettings.maximumPasswordLength.lessthanminimum=âå¯ç é¿åº¦æå¤§å¼âåºå¤§äºâå¯ç é¿åº¦æå°å¼'ã
error.message.admin.securitySettings.maximumPasswordLength.outofrange=âå¯ç é¿åº¦æå¤§å¼âåºä¸ºä»äº 6 å° 32 ä¹é´çæ°å­ã
error.message.admin.securitySettings.maximumUserIdLength.lessthanminimum=âç¨æ·åé¿åº¦æå¤§å¼âåºå¤§äºâç¨æ·åé¿åº¦æå°å¼'ã
error.message.admin.securitySettings.maximumUserIdLength.outofrange=âç¨æ·åé¿åº¦æå¤§å¼âåºä¸ºä»äº 2 å° 80 ä¹é´çæ°å­ã
error.message.admin.securitySettings.minimumPasswordLength.outofrange=âå¯ç é¿åº¦æå°å¼âåºä¸ºä»äº 6 å° 32 ä¹é´çæ°å­ã
error.message.admin.securitySettings.minimumUserIdLength.outofrange=âç¨æ·åé¿åº¦æå°å¼âåºä¸ºä»äº 2 å° 80 ä¹é´çæ°å­ã
error.message.admin.securitySettings.pwExpireDays.mustgreaterthanone=âå¯ç è¿ææ¶é´âå¿é¡»å¤§äº 1ã
error.message.admin.securitySettings.pwHistoryEntries.mustgreaterthanzero=âå¯ç åå²è®°å½æ¡ç®âå¿é¡»å¤§äº 0ã
error.message.admin.securitySettings.pwLimitMonths.mustbebetween=âå¯ç éç¨æ(æ)âåºä¸º 1 å° 24 ä¹é´çä¸ä¸ªæ°å­ã
error.message.admin.securitySettings.pwResetKeepAlive.cannotbezero=âå¯ç éç½®ä¿ææ´»å¨æ¶é´(åé)âä¸è½ä¸º 0ã
error.message.admin.securitySettings.sessionTimeout.outofrange=âä¼è¯è¶æ¶âåºä¸ºä»äº 10 å° 120 ä¹é´çæ°å­ã
error.message.aggregated.variable.cannot.be.used.in.key={0} ä¸è½ç¨ä½å³é®å­ï¼å ä¸ºå®å·²ç»èåã
error.message.all.multipart.content.is.empty=å¨ä¿å­ä¹åï¼å¿é¡»è³å°ä¸ºä¸ä¸ªæ¶æ¯é¨åæä¾åå®¹
error.message.all.multipart.including.parent.content.is.empty=å¨ä¿å­ä¹åï¼å¿é¡»è³å°ä¸ºä¸ä¸ªæ¶æ¯é¨åæç¶åå®¹(å¦æéæ©äºâä»ç¶çº§ä¸­ç»§æ¿â)æä¾åå®¹ã
error.message.approvaldatemustbeinfuture=è¯·éæ©ä¸ä¸ªå°æ¥çæ¥æ
error.message.attribute.charinvalid=å±æ§åç§°æ¯å¿éçï¼ä¸å¿é¡»ä»¥ [a~z] æ [A~Z] èå´ä¸­çå­ç¬¦å¼å¤´ã
error.message.cannot.delete.referenced.constant={0} è¢«åå®¹å¼ç¨ï¼æ æ³å é¤
error.message.cannot.delete.referenced.constants=ä¸åå¸¸éç±äºè¢«å¼ç¨èæ æ³å é¤: {0}
error.message.cannot.delete.referenced.paragraphstyles=ä¸åæ®µæ®µè½æ ·å¼ç±äºè¢«å¼ç¨èæ æ³å é¤: {0}
error.message.cannot.delete.referenced.targetgroups=ä¸åç®æ ç»ç±äºè¢«å¼ç¨èæ æ³å é¤: {0}
error.message.cannot.delete.referenced.targetingrules=ä¸åç®æ è§åç±äºè¢«å¼ç¨èæ æ³å é¤: {0}
error.message.cannot.delete.referenced.variables=ä¸ååéç±äºè¢«å¼ç¨èæ æ³å é¤: {0}
error.message.cannotchange.tpcs.delivery=æ¥è§¦ç¹åå®¹å¯éæ¶æ¯çä¼ éåºæ æ³æ´æ¹ã
error.message.cannotviewyourdefaultpage=å¿é¡»éç½®æ¨çé»è®¤å·¥ä½åºãè¯·ä»åè¡¨ä¸­éæ©ä¸ä¸ªé¡¹ç®ï¼ç¶ååå»âä¿å­âã
error.message.categoryexists=æ­¤åç§°çç±»å«å·²å­å¨ï¼è¯·è¾å¥ä¸ä¸ªä¸åçåç§°ã
error.message.codemustbeunique=è¾å¥çç§æ·ä»£ç ä¸å¯ç¨ãè¯·è¾å¥ä¸ä¸ªæ°çç§æ·ä»£ç ã
error.message.combination.is.used.in.other.node=æä¾çéæ©æ¡ä»¶ (\"{0}\") ä¼éå¤åé \"{1}\" çç°ææ¡ä»¶ï¼å æ­¤æ æ³æ·»å 
error.message.combition.is.duplicated.in.the.entered.value=æä¾çéæ©æ¡ä»¶ (\"{0}\") ä¸æ­¤åéçç°ææ¡ä»¶éå¤ï¼å æ­¤æ æ³æ·»å 
error.message.commentlengthexceeded=æ³¨éé¿åº¦è¶è¿ 255 ä¸ªå­ç¬¦
error.message.condition.elements..not.data.model.compatible=æéè§åå¨æ°æ®æ¨¡åä¸ä¸å¼å®¹
error.message.content.library.imagefile.not.compatible.with.active=å¾å½¢æä»¶çæ ¼å¼ä¸ä¸»å¨å¯æ¬çæ ¼å¼ä¸å¹éãè¯·ä¸ä¼ ç¸åçæ ¼å¼ã
error.message.content.library.imagefile.not.compatible=å¾å½¢æä»¶æ ¼å¼äºç¸ä¸å¹éãè¯·ä¸ºæ¯ä¸ç§è¯­è¨ä¸ä¼ ç¸åçæ ¼å¼ã
error.message.content.library.model.not.matching=æ¨åå¶å¯¼å¥æ°æ®çæ¨¡åä¸ xml æä»¶ä¸å¹éã
error.message.content.spellchecker.invalid.config.file= æ æç dictionaryConfig.properties æä»¶ã
error.message.content.spellchecker.no.config.file= æ æ³å è½½ dictionaryConfig.properties å±æ§æä»¶ã
error.message.content.spellchecker.no.file=æä»¶å¤¹ {0} ä¸æ²¡ææä»¶
error.message.content.spellchecker.no.valid.dictionary=æ²¡æä¸ºè¯­è¨ {0} æä¾ææçå­å¸æ ¼å¼æä»¶
error.message.content.spellchecker.no.valid.folder={0} å­å¸ä¸å­å¨
error.message.contenttypeinvalid=åå®¹ç±»åæ æ
error.message.customernumberformat=è¯·ä¸º {0} è¯­è¨è¾å¥ä¸ä¸ªæææ°å­ä»¥ä½ä¸ºå®¢æ·ç¼å·ã
error.message.customernumberrequired=è¯·ä¸º {0} è¯­è¨è¾å¥ä¸ä¸ªå®¢æ·ç¼å·ã
error.message.dataresourcerequired=è¯·éæ©æ°æ®èµæº
error.message.datasourceassociationuniquename=æ°æ®éåç§°å·²ä½¿ç¨ãè¯·è¾å¥å¯ä¸çåç§°ã
error.message.datavalues.must.be.consecutive=æ°æ®å¼å¿é¡»æ¯è¿ç»­ç
error.message.dateoutofrange=æ¥æè¶åºèå´ãè¯·ç¡®ä¿æ¥æä»äº 1753 å¹´ 1 æ 1 æ¥å 9999 å¹´ 12 æ 31 æ¥ä¹é´ã
error.message.deletevariable.exception=VariableDeleteValidator ä¸­åçæå¤å¼å¸¸ã
error.message.deletevariable.noparamenter=DeleteComand å¿é¡»è¦æåæ°ã
error.message.deletevariable.usedbymessagecontent=æ æ³å é¤è¯¥é¡¹ç®ï¼å ä¸ºå®æ­£ç¨äºæ¶æ¯åå®¹ä¸­
error.message.deletevariable.usedbyparameters= æ æ³å é¤åéï¼å ä¸ºå®æ­£ç±ä¸ä¸ªæå¤ä¸ªåå®¹éæ©å¨ä½¿ç¨ã
error.message.deletevariable.usedbyrules= æ æ³å é¤åéï¼å ä¸ºå®æ­£ç±è§åä½¿ç¨ã
error.message.duplicateAppliedImageName=åç°äºå¼ä¸º {0} çéå¤åºç¨å¾ååç§°ãæ æ³ä¸ºä¸åçè¯­è¨è¾å¥ç¸åçåºç¨å¾ååç§°ãè¯·æ´æ¹éå¤çå¼ï¼ç¶åéè¯ã
error.message.email.zone.parts.cannot.overlap=çµå­é®ä»¶æ¥è§¦ç¹çååºåé¨åä¸å¾éå 
error.message.emptyindicator=è¯·ä¸ºæ¯ä¸ªæç¤ºç¬¦æå®ä¸ä¸ªå¼ï¼æèå é¤ä¸éè¦çæç¤ºç¬¦ã
error.message.emptyindicatorlength=è¯·ä¸ºæ¯ä¸ªé¿åº¦æå®ä¸ä¸ªå¼ï¼æèå é¤ä¸éè¦çæç¤ºç¬¦ã
error.message.end.date.before.start.date=ç»ææ¥æä¸è½æ©äºå¼å§æ¥æã
error.message.enddatebeforestart=è¯·è¾å¥ä¸ä¸ªæäºèµ·å§æ¥æçææç»ææ¥æ
error.message.exceed.maximum.number.of.creating.restricted.users=æ ¹æ®æ¨çè®¸å¯è¯ï¼å·²è¶åºæå¤§åéè®¿é®æéç¨æ·æ°ã
error.message.exceed.maximum.number.of.limited.access.users=æ ¹æ®æ¨å¨å {0} ä¸­çè®¸å¯è¯ï¼å·²è¶åºæå¤§åéè®¿é®æéç¨æ·æ°ã
error.message.exceed.maximum.number.of.regular.users=æ ¹æ®æ¨å¨å {0} ä¸­çè®¸å¯è¯ï¼å·²è¶åºæå¤§å®æ´è®¿é®æéç¨æ·æ°ã
error.message.externalidmustbeunique=è¾å¥ç ID å½åæ­£å¨ä½¿ç¨ãè¯·ä¸º ID è¾å¥ä¸ä¸ªä¸åçå¼ã
error.message.global.message.creation.not.permitted.for.limited.visibility=æªè¢«æäºå¯¹ææåéçå®æ´è®¿é®æéçç¨æ·æ æ³åå»ºå¨å±æ¶æ¯ã
error.message.global.messages.delivered.to.message.selectable.touchpoints.must.be.mandatory=å¨å±æ¶æ¯å¿é¡»ä¼ éå°ä¸ä¸ªçµæ´»æ¥è§¦ç¹ã
error.message.graphicdeliverytype.not.match=æéä¼ éçåå®¹ç±»ååç°ææ¶æ¯åå®¹çå¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·éæ©å¶ä»ä¼ éåºåã
error.message.graphicfilesize.too.large=æéå¾å½¢ {0} è¶åºäºè¯¥ä¼ éåè®¸çæå¤§æä»¶å¤§å° ({1}kb)ã
error.message.graphicfiletype.not.match=å¾å½¢æä»¶æ ¼å¼ä¸æéä¼ éçåå®¹ç±»åä¸å¹éãè¯·ä¸ä¼ ä¸åçæä»¶ã
error.message.id.notfound=æ±æ­ï¼ä½æ¾ä¸å°æå®çæ¶æ¯ IDã
error.message.imagefile.not.compatible.af=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: åéè·å°è¯­å¾å½¢ã
error.message.imagefile.not.compatible.al=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: é¿å°å·´å°¼äºè¯­å¾å½¢ã
error.message.imagefile.not.compatible.br=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: å¸åå¡å°¼è¯­å¾å½¢ã
error.message.imagefile.not.compatible.ca=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: å æ³°ç½å°¼äºè¯­å¾å½¢ã
error.message.imagefile.not.compatible.da=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: ä¸¹éº¦è¯­å¾å½¢ã
error.message.imagefile.not.compatible.de=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: å¾·è¯­å¾å½¢ã
error.message.imagefile.not.compatible.en=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: è±è¯­å¾å½¢ã
error.message.imagefile.not.compatible.es=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: è¥¿ç­çè¯­å¾å½¢ã
error.message.imagefile.not.compatible.et=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: ç±æ²å°¼äºè¯­å¾å½¢ã
error.message.imagefile.not.compatible.eu=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: å·´æ¯åè¯­å¾å½¢ã
error.message.imagefile.not.compatible.fi=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: è¬å°è¯­å¾å½¢ã
error.message.imagefile.not.compatible.fo=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: æ³ç½è¯­å¾å½¢ã
error.message.imagefile.not.compatible.fr=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: æ³è¯­å¾å½¢ã
error.message.imagefile.not.compatible.ga=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: ç±å°å°è¯­å¾å½¢ã
error.message.imagefile.not.compatible.gd=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: èæ ¼å°çå°è¯­å¾å½¢ã
error.message.imagefile.not.compatible.gl=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: å å©è¥¿äºè¯­å¾å½¢ã
error.message.imagefile.not.compatible.is=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: å°å²è¯­å¾å½¢ã
error.message.imagefile.not.compatible.it=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: æå¤§å©è¯­å¾å½¢ã
error.message.imagefile.not.compatible.lb=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: å¢æ£®å ¡è¯­å¾å½¢ã
error.message.imagefile.not.compatible.nl=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: è·å°è¯­å¾å½¢ã
error.message.imagefile.not.compatible.no=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: æªå¨è¯­å¾å½¢ã
error.message.imagefile.not.compatible.pt=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: è¡èçè¯­å¾å½¢ã
error.message.imagefile.not.compatible.sv=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: çå¸è¯­å¾å½¢ã
error.message.imagefile.not.compatible.sw=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: æ¯ç¦è¥¿éè¯­å¾å½¢ã
error.message.imagefile.not.compatible.wa=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ä¼ å¶ä»æä»¶: çªé¾è¯­å¾å½¢ã
error.message.indentation.range=è¯·ä¸ºâç¼©è¿âè¾å¥ä¸ä¸ªä»äº 0.001 å° 10 ä¹é´çæ°å­
error.message.indicatorlength.is.zero=æç¤ºç¬¦ä¸è½æå®é¿åº¦ 0ã
error.message.invalid.content.library.reference=å°æªä¸º {0} éæ©å¾ååºé¡¹ç®
error.message.invalid.externalid=è¯·ä¸ºå¤é¨ ID è¾å¥ä¸ä¸ªæ°å¼!
error.message.invalid.text.content=ææ¬åå®¹ä¸å¾åå«ä¸åå­ç¬¦: %%, |
error.message.languageexists=è¯¥è¯­è¨å¨è¯¥æ¥è§¦ç¹ä¸­å·²å­å¨ã
error.message.left.margin.range=è¯·ä¸ºâå·¦è¾¹è·âè¾å¥ä¸ä¸ªä»äº 0.001 å° 10 ä¹é´çæ°å­
error.message.licence.cannot.be.larger.than={0} ä¸è½å¤§äº {1}
error.message.licence.cannot.be.smaller.than.one={0} ä¸è½å°äº 1ã
error.message.licence.same.name.with.different.email=å·æç¸ååç§°çå¬å¸å·²å¨çµå­é®ä»¶ {0} ä¸­ä½¿ç¨ãè¯·å¹éå³èççµå­é®ä»¶ï¼æèæä¾å¶ä»å¬å¸åç§°ã
error.message.linked.to.message.deleted=æéæ¶æ¯ \"{0}\" ä¸å­å¨ã
error.message.location.value.greater.than.zero=è¯·è¾å¥ä¸ä¸ªå¤§äº 0 çâä½ç½®âå¼ã
error.message.mandatory.attachments.cannot.have.targeting=å¼ºå¶ä¼ ééä»¶ä¸è½æç®æ æå®ãå¨åæ¢å°å¼ºå¶ä¼ éä¹åï¼è¯·ä»éä»¶ä¸­å é¤ç®æ ã
error.message.master.message.require.approval=å¨æ¿æ´»å­åéä¹åï¼å¿é¡»æ¿æ´»å¼ç¨çä¸»æ¶æ¯ - æ¿æ´»ä»¥ä¸ä¿¡æ¯: {0}
error.message.message.not.matching=æ¶æ¯ä¸å¹é
error.message.message.zone.and.target.groups.not.data.model.compatible=æ¶æ¯åºååæéç®æ ç»ä¸æ°æ®æ¨¡åä¸å¼å®¹
error.message.message.zone.has.empty.datagroup=å¶ä¸­ä¸ä¸ªæ¶æ¯åºåå·æç©ºç DataGroup! è¯·å¨æ¥è§¦ç¹è®¾ç½®ä¸­è§£å³æ­¤é®é¢ã
error.message.missing.zone.part.sequence.values=åºåé¨åæåºä¸å½ãä¸ååºåå¼ç¼ºå¤±: {0}
error.message.multipledelivery.graphic.not.compatible=æ¶æ¯ä¸è½å·æå¤ä¸ªä¸åå¾å½¢ç±»åçåºåã
error.message.multipledelivery.selectable=æ¶æ¯å·æä¸ä¸ªä»¥ä¸çä¼ éï¼ä¸è½ä¸åå®¹éæ©å¨æåå®¹éæ©å¨ç»ç¸å³è
error.message.must.enter.data=è¯·æå®è¦æ·»å å°åéæèè¦ä»åéä¸­å é¤çæ°æ®ãå½å°æ°æ®æ·»å å°åéæ¶ï¼{0} æ¯å¿éçã
error.message.must.select.at.least.one.touchpoint=è¯·è³å°éæ©ä¸ä¸ªæ¥è§¦ç¹ã
error.message.must.select.at.least.two.variables.for.compound.key=è¯·ä¸ºæ¯ä¸ªç»åé®è³å°éæ©ä¸¤ä¸ªåéã
error.message.must.select.composition.package=è¯·éæ©ç»åå
error.message.must.select.condition.for.rule=å¿é¡»ä¸ºè§å {0} éæ©æ¡ä»¶
error.message.must.select.data.resource=è¯·éæ©æ°æ®èµæº
error.message.must.select.shared.collection=æ¨å¿é¡»éæ©å±äº«é
error.message.mustentercontactprovincestate=è¯·è¾å¥èç³»äººçç/å·ã
error.message.mustentercontent.for.language=è¯·ä¸º {0} è¯­è¨è¾å¥ææçåå®¹ã
error.message.mustentercontent=é»è®¤è¯­è¨åå®¹å½åä¸ºç©ºãè¯·æä¾æ¶æ¯åå®¹ä»¥ç»§ç»­ã
error.message.mustselectMessages=å¿é¡»è³å°éæ©ä¸æ¡æ¶æ¯ã
error.message.mustselectallprimaryvariables=è¯·ä¸ºææçå¼ç¨æ°æ®å³èéæ©ä¸»æ°æ®åéã
error.message.mustselectallreferencedatasources=è¯·ä¸ºææçå¼ç¨æ°æ®å³èéæ©åèæ°æ®æºã
error.message.mustselectallreferencevariables=è¯·ä¸ºææçå¼ç¨æ°æ®å³èéæ©å¼ç¨æ°æ®åéã
error.message.mustselectcontactcountry=è¯·è¾å¥èç³»äººçå½å®¶/å°åºã
error.message.mustselectdailyfrequencytype=è¯·éæ©æ¯æ¥é¢çç±»åã
error.message.mustselectdatasourceassociation=è¯·éæ©æ°æ®éã
error.message.mustselectdocument=è¯·è³å°éæ©ä¸ä¸ªææ¡£åä¸æ¡æ¶æ¯ã
error.message.mustselectenddate=è¯·è¾å¥ç»ææ¥æ
error.message.mustselectfile=è¯·éæ©æä»¶
error.message.mustselectmessage=è¯·éæ©æ¶æ¯
error.message.mustselectprimarydatafile=è¯·éæ©ä¸»æ°æ®æä»¶ã
error.message.mustselectprimarydatasource=è¯·éæ©ä¸»æ°æ®æºã
error.message.mustselectproductionruntype=è¯·éæ©çäº§è¿è¡ç±»åã
error.message.mustselectreferencedatafile=è¯·ç¡®ä¿ä¸ºæ¯ä¸ªå¼ç¨æ°æ®æºéæ©æ°æ®æä»¶ã
error.message.mustselectrepeatingfrequency=è¯·éæ©éå¤é¢çã
error.message.mustselectrule=è¯·ä¸ºæ­¤ç®æ ç»è³å°éæ©ä¸ä¸ªè§å
error.message.mustselectstartdate=è¯·è¾å¥å¼å§æ¥æ
error.message.mustselectstartdateifenddate=å¦ææå®ç»ææ¥æä¿¡æ¯ï¼è¯·è¾å¥å¼å§æ¥æ
error.message.mustselecttestrundate=è¯·è¾å¥æµè¯è¿è¡æ¥æ
error.message.mustselecttouchpoint=è¯·éæ©æ¥è§¦ç¹
error.message.mustselecttouchpointorcollection=è¯·éæ©æ¥è§¦ç¹ææ¥è§¦ç¹é
error.message.mustselectuser=è¯·éæ©ç¨æ·
error.message.mustselectzone=è¯·éæ©ç®æ åºä»¥è¿è¡ä¼ éã
error.message.name.is.used.by.another.node=åç§°å·²ç±å¦ä¸ä¸ªèç¹ä½¿ç¨
error.message.name.is.used.by.another.shared.node=åç§°å·²ç±å¦ä¸ä¸ªå±äº«èç¹ä½¿ç¨
error.message.namemustbeunique=åç§°å·²ä½¿ç¨ãè¯·è¾å¥å¯ä¸çåç§°ã
error.message.namerequired=è¯·è¾å¥åç§°
error.message.no.multidelivery.on.sms.zone=ä¼ éå° SMS åºåçæ¶æ¯ä¸å¯è¿è¡å¤ä¼ é
error.message.no.multidelivery.on.subject.line.zone=ä¼ éå°âä¸»é¢è¡âåºåçæ¶æ¯ä¸å¯è¿è¡å¤ä¼ é
error.message.no.rules.in.system=ç³»ç»ä¸­æ²¡æå¯ç¨çè§åãè¯·å¨åå»ºç®æ ç»ä¹ååå»ºè§åã
error.message.no.touchpoints.selected=è¯·éæ©ä¸ä¸ªæå¤ä¸ªæ¥è§¦ç¹
error.message.node.domain.is.not.in.valid.state=åçé»è®¤å®ä¾æªå¤äºææç¶æãè¯·ååå§åé»è®¤å®ä¾ã
error.message.non.unique.record.position=ä½ç½®å¼å·²ç±æ­¤æ°æ®æºçå¦ä¸æ¡è®°å½å®ä¹ãè¯·æä¾å¯ä¸çå¼ã
error.message.not.allowed.to.chain.message=æ­¤æ¶æ¯å·æé¾æ¥çå­æ¶æ¯ãæ æ³çææ¶æ¯é¾ã
error.message.not.exists=è¯¥æ¶æ¯ä¸å­å¨ã
error.message.order.entry.data.element.required=å¿é¡»ä¸ºæ¯ä¸ªè®¢åæ¡ç®éç½®éæ©æ°æ®åç´ ã
error.message.order.entry.menu.items.required=å¿é¡»è³å°ä¸ºæ¯ä¸ªèåå­æ®µç±»åéç½®ä¸ä¸ªé¡¹ç®ã
error.message.order.entry.web.service.attributes.required=Web æå¡é©±å¨çèåéç½®éè¦ä½¿ç¨ URLãç¨æ·ååå¯ç ã
error.message.overlapping.freeform.containers={0} åå®¹åå«éå ç»å¸å®¹å¨ã
error.message.packaged.touchpoint.must.belong.to.same.family=æåæ¥è§¦ç¹å¿é¡»å±äºåä¸ä¸ªç³»å(æåçå®ä¹çå­é): {0}
error.message.parameter.group.at.least.two.parameters=æ¨å¿é¡»è³å°éæ©ä¸¤ä¸ªåå®¹éæ©å¨
error.message.parameter.group.name.already.in.use=åç§°å·²ä½¿ç¨ãè¯·è¾å¥å¶ä»åç§°
error.message.parameter.group.required=è¦åå»ºå¨æåå®¹ï¼å¿é¡»ä½¿ç¨éæ©å¨
error.message.parameter.zones.not.data.model.compatible=æéåå®¹éæ©å¨ä¸æéåºåå¨æ°æ®æ¨¡åæ¹é¢ä¸å¼å®¹ã
error.message.parameters.not.data.model.compatible=æéåå®¹éæ©å¨å¨æ°æ®æ¨¡åæ¹é¢ä¸å¼å®¹
error.message.password.expired=æ¨çå¯ç å·²è¿æãæ´æ¹æ¨çå¯ç ä»¥ç»§ç»­ã
error.message.position.value.between.one.and.ninety.nine=è¯·è¾å¥ä¸ä¸ªä»äº 1 å 99 ä¹é´çä½ç½®å¼
error.message.priority.items.have.changed.since.page.load=ä¸æ¡æå¤æ¡æ¶æ¯å·²æ´æ¹äºç¶æãè¯·æ ¹æ®ä¿®è®¢çæ¶æ¯åè¡¨æ£æ¥å¹¶è®¾ç½®ä¼åçº§ã
error.message.recipient.attachment.location.is.mandatory=æ¶ä»¶äººéä»¶ä½ç½®ä¸è½ä¸ºç©º
error.message.recipient.attachment.name.is.mandatory=æ¶ä»¶äººéä»¶å§åä¸è½ä¸ºç©º
error.message.recipient.location.contains.restricted.characters=éä»¶æ¶ä»¶äººä½ç½®ä¸è½åå«ä»¥ä¸ä»»ä½å­ç¬¦: {0}
error.message.recipient.name.contains.restricted.characters=éä»¶æ¶ä»¶äººå§åä¸è½åå«ä»¥ä¸ä»»ä½å­ç¬¦: {0}
error.message.reference.data.source.must.be.unique=è¯·ç¡®ä¿åªä¸ºè¯¥æ°æ®éåéæ©æ¯ä¸ªå¼ç¨æ°æ®æºä¸æ¬¡ã
error.message.relase.for.approval.parent.no.active=ç¶é¾æ¥æ¶æ¯æªæ¿æ´»ãå¨æ¹åä¹åï¼è¯·æ¿æ´»ç¶æ¶æ¯ã
error.message.right.margin.range=è¯·ä¸ºâå³è¾¹è·âè¾å¥ä¸ä¸ªä»äº 0.001 å° 10 ä¹é´çæ°å­
error.message.roleisinuse=å½åæ­£å¨ä½¿ç¨è§è²ï¼å æ­¤æ æ³å°å¶å é¤ã
error.message.ruleconditiontype.cannot.changed.when.referenced=æ æ³æ´æ¹å¼ç¨çè§åçæ¡ä»¶å³ç³»ç»æã
error.message.rulemusthavecondition=è§åå¿é¡»è³å°è¦æä¸ä¸ªæ¡ä»¶ã
error.message.select.at.least.one.data.element=è¯·è³å°éæ©ä¸ä¸ªæ°æ®åç´ ã
error.message.select.data.element=è¯·éæ©æ°æ®åç´ ã
error.message.selectable.multipledelivery=è¯¥æ¶æ¯ç±»ååªè½ä¼ éå°åä¸ªæ¥è§¦ç¹åçåä¸ªåºåã
error.message.selectaparent=è¯·éæ©ç¶çº§ã
error.message.selectarecord=è¯·è³å°éæ©ä¸æ¡æ°æ®è®°å½ã
error.message.selectasource=è¯·éæ©æ°æ®æºã
error.message.selection.data.cannot.be.removed.because.of.being.used.in.other.nodes=æ æ³å é¤éæ©æ°æ®ï¼å ä¸ºå®å¨å¦ä¸ä¸ªåä¸ºâ{0}âçåéä¸­å·æå­çº§
error.message.selection.final.approval.out.of.sequence=æ æ³å¨å¤±åºçæåµä¸è¯·æ±æ¿æ´» - è¯¥åéå°ä»ä¸åæªæ¿æ´»çç¶çº§ä¸­ç»§æ¿åå®¹: {0}
error.message.shared.graphicfilesize.too.large=å±äº«åå®¹ {0} å¼ç¨äºä¸ä¸ªè¶åºè¯¥ä¼ éæåè®¸çæå¤§æä»¶å¤§å° ({1}kb) çå¾å½¢æä»¶ã
error.message.shared.value.combination.is.used.in.other.node=å±äº«çéæ©æ¡ä»¶ (\"{0}\") éå¤ç°ææ¡ä»¶åé \"{1}\"ï¼å æ­¤æ æ³åºç¨
error.message.sharedcontent.imagefiledoesnotmatch=å¾å½¢æä»¶æ ¼å¼ä¸å¹éãè¯·ä¸ºææè¯­è¨ä¸ä¼ ç¸åçå¾åæä»¶æ ¼å¼ã
error.message.sharedcontent.imagefilemustbejpgtiforrtf=å¾å½¢å±äº«åå®¹åªæ¯æ .jpgã .tifã.pdf å .rtf æä»¶ã
error.message.sharedmodeltenants=è¯·è³å°éæ©ä¸ä¸ªç§æ·
error.message.spacing.after.range=è¯·ä¸ºâåé´è·âè¾å¥ä¸ä¸ªä»äº 0 å 720 ä¹é´çæ°å­
error.message.spacing.before.range=è¯·ä¸ºâåé´è·âè¾å¥ä¸ä¸ªä»äº 0 å 720 ä¹é´çæ°å­
error.message.startdateandtimemustbeinfuture=è¯·éæ©å¼å§æ¥æåæªæ¥æ¶é´ã
error.message.tag.cannot.be.activated.due.to.associations.archived=æ æ³æ¿æ´» {0}ï¼å ä¸ºå·²å é¤æå­æ¡£ææç¸å³çä¿¡æ¯å/ææå¥ã
error.message.tag.cannot.be.activated=æ æ³æ¿æ´» {0}ï¼å ä¸ºç®æ åå³èé½æªæå®ã
error.message.tag.name.exists=æ­¤åç§°çæ è®°å·²å­å¨ï¼è¯·è¾å¥å¶ä»åç§°ã
error.message.tag.type.cannot.be.discarded=æ æ³æ¾å¼ {0}ï¼å ä¸ºä¸ä¸ªæå¤ä¸ªæ è®°æ­£å¨å¼ç¨è¯¥æ è®°ç±»åã
error.message.tag.type.name.exists=æ­¤åç§°çæ è®°ç±»åå·²å­å¨ï¼è¯·è¾å¥å¶ä»åç§°ã
error.message.target.group.not.match.touchpoint.data.model=ç®æ ç»ä¸æéæ¥è§¦ç¹åéå¨æ°æ®æ¨¡åæ¹é¢ä¸å¼å®¹ã
error.message.target.groups.not.data.model.compatible=æéç®æ ç»å¨æ°æ®æ¨¡åæ¹é¢ä¸å¼å®¹
error.message.target.groups.not.match.touchpoint.data.model=ä¸ä¸ªæå¤ä¸ªç®æ ç»ä¸æéæ¥è§¦ç¹åéå¨æ°æ®æ¨¡åæ¹é¢ä¸å¼å®¹ã
error.message.target.zone.must.be.selected.for.message.move=å¿é¡»éæ©åºåã
error.message.template.variant.each.template.variant.must.have.a.template.packate.uploaded=å¿é¡»ä¸ºæ¯ä¸ªå¤ç¨æ¨¡æ¿ä¸ä¼ æ¨¡æ¿å
error.message.text.style.name.must.be.unique=å¿é¡»ä¸ºæ¯ä¸ªææ¬æ ·å¼æä¾å¯ä¸çåç§°ã
error.message.text.style.name.required=ææ¬æ ·å¼åç§°æ¯å¿éçã
error.message.there.is.at.least.one.empty.value.before.parameter=å¨åæ° {0} åè³å°æä¸ä¸ªç©ºå¼
error.message.touchpoint.collection.connector.not.dialogue=å¨æåéåä¸­åªåè®¸ä½¿ç¨æå°æ¸ éæ¥è§¦ç¹ã
error.message.touchpoint.not.matching=æ¥è§¦ç¹ä¸å¹é
error.message.touchpoints.composition.package.are.not.the.same=æææ¥è§¦ç¹å¿é¡»ä½¿ç¨ç¸åç»ååæèä¸ºç©ºã
error.message.touchpoints.connector.are.not.the.same=æææ¥è§¦ç¹å¿é¡»ä½¿ç¨ç¸åçæ¸ é/è¿æ¥å¨æèä¸ºç©ºã
error.message.touchpoints.dsa.are.not.the.same=æææ¥è§¦ç¹å¿é¡»ä½¿ç¨ç¸åçæ°æ®æºå³èæèä¸ºç©ºã
error.message.toucpointselectable.notouchpointchanges=æ æ³å¨æ¥è§¦ç¹å¯éæ¶æ¯ä¸æ´æ¹æ¥è§¦ç¹ã
error.message.tp.content.selector.zones.not.data.model.compatible=æ¥è§¦ç¹éæ©å¨ä¸æéåºåå¨æ°æ®æ¨¡åæ¹é¢ä¸å¼å®¹ã
error.message.usagetype.cannot.changed.when.referenced=ä¸è½æ´æ¹æ­¤å¼ç¨èµäº§çä½¿ç¨ç±»åã
error.message.variable.cannot.be.aggregated.because.it.is.used.in.key=æ æ³èååéï¼å ä¸ºå®å¨æ°æ®éåä¸­ç¨ä½å³é®å­ã
error.message.variable.defaultvalue.invalid=é»è®¤å¼æ æ: {0}
error.message.variableedit.inconsistentdataelements=æéæ°æ®åç´ çæ°æ®ç±»åä¸ä¸è´
error.message.variableedit.usedbymessagecontent=åéåå«å¨ä¸æ¡æå¤æ¡æ¶æ¯ä¸­ï¼å æ­¤æ æ³å¨åå®¹ä¸­ç¦ç¨ã
error.message.variableedit.usedbyparameters=åéæ­£ç±ä¸ä¸ªæå¤ä¸ªåå®¹éæ©å¨ä½¿ç¨ï¼å æ­¤æ æ³å¨è§åä¸­ç¦ç¨ã
error.message.variableedit.usedbyrules=åéæ­£ç±ä¸ä¸ªæå¤ä¸ªè§åä½¿ç¨ï¼å æ­¤æ æ³å¨è§åä¸­ç¦ç¨ã
error.message.variablefriendlynameexists=æ­¤åå¥½åç§°çåéå·²ç»å­å¨ãè¯·è¾å¥å¶ä»åå¥½åç§°ã
error.message.variablenameexists=æ­¤åç§°çåéå·²ç»å­å¨ãè¯·è¾å¥å¶ä»åç§°ã
error.message.variables..not.data.model.compatible=æéåéå¨æ°æ®æ¨¡åæ¹é¢ä¸å¼å®¹
error.message.xmldatatag.charinvalid=åç§°åªè½åå«å­ç¬¦ [a~z] æ [A~Z]ã
error.message.zone.content.type.cannot.be.changed=æ­¤æ¶ä¸åè®¸æ´æ¹åå®¹ç±»åãè¯·åä»è¯¥åºåä¸­åæ¶å³èææåå®¹èµäº§ï¼ç¶åéè¯ã
error.message.zone.data.group.linked.cannot.be.changed=å­å¨å·²ä¼ éå°è¯¥åºåçé¾æ¥æ¶æ¯ãè¯·åä»è¯¥åºåä¸­åæ¶å³èæé¾æ¥çæ¶æ¯ï¼ç¶åéè¯
error.message.zone.freeform.toggle.cannot.be.changed=æ æ³æ´æ¹è¯¥åºåçèªç±æ ¼å¼ç¶æï¼ç´å°å é¤ææç°æåå®¹ã
error.message.zone.multipart.atleastxxparts=å¤é¨ååºåè³å°åºå·æ {0} ä¸ªé¨åã
error.message.zone.multipart.contenttypeofausedzonepartnochange=æ æ³æ´æ¹å·²ä½¿ç¨çåºåé¨åçåå®¹ç±»åã{0}
error.message.zone.multipart.messagesRefCannotBeDeleted=æ æ³å é¤åºåé¨åï¼å ä¸ºå­å¨ä¸æ­¤åºåå³èçæ¶æ¯ã
error.message.zones.not.data.model.compatible=æéåºåå¨æ°æ®æ¨¡åæ¹é¢ä¸å¼å®¹ã
error.messages.associated.with.multipart.zone=å½åæå¤é¨åæ¶æ¯ä¸è¯¥åºåé¨å(å·æä¸åçåå®¹ç±»å)å³èã<br />è¦æ´æ¹åºåé¨åçåå®¹ç±»åï¼å¿é¡»åä¿®æ¹ä»¥ä¸æ¶æ¯ã
error.name.cannot.be.empty=åç§°ä¸è½ä¸ºç©ºã
error.node.can.not.be.empty=å®ä¾ä¸è½ä¸ºç©º
error.node.is.not.valid=å®ä¾æ æ
error.node.name.must.be.entered=å¿é¡»è¾å¥å®ä¾åç§°
error.node.name.must.be.unique=å®ä¾åç§°å¿é¡»å¯ä¸
error.node.only.production.type.can.be.create.at.this.time=å®ä¾ç±»åæ æãå¿é¡»ååå»ºçäº§ç±»åå®ä¾ã
error.node.schema.name.must.be.entered=å¿é¡»è¾å¥æ¶æåç§°
error.node.schema.name.must.be.unique=ç»æåç§°å¿é¡»å¯ä¸
error.number.of.sections.to.add.must.be.more.than.one=æ·»å çé¨åæ°ç®å¿é¡»æ¯å¤§äº 0 çæ°å­
error.order.entry.items.must.have.unique.data.element.mapping=æ æ³ä¸ºè¶è¿ä¸ä¸ªæ°æ®ç¹æ å°åä¸ªæ°æ®åç´ ãè¯·ä¿®æ¹æ¨çæ°æ®åç´ /è¾å©æ°æ®åç´ éæ©ã
error.order.entry.primary.driver.must.be.selected=å¿é¡»éæ©ä¸»è®¢åè¾å¥é¡¹ç®ã
error.please.enter.all.long.month.names=è¯·è¾å¥ææå¤§æä»½åç§°
error.please.enter.all.short.month.names=è¯·è¾å¥ææå°æä»½åç§°
error.pod.master.pwd.must.be.entered=å¿é¡»è¾å¥ Pod æ¶æå¯ç 
error.pod.master.schema.must.be.entered=å¿é¡»è¾å¥ Pod ä¸»æ¶æåç§°
error.pod.name.must.be.entered=å¿é¡»è¾å¥ Pod åç§°
error.pod.name.must.be.unique=Pod åç§°å¿é¡»å¯ä¸
error.pod.url.must.be.entered=å¿é¡»è¾å¥ Pod URL
error.pod.url.must.be.unique=Pod URL å¿é¡»å¯ä¸
error.rate.sheet.default.not.covering.whole.period=æ¬æ¶é´è¡¨çé»è®¤è¯ä»·è¡¨æªè¦çæ¶é´è¡¨çæ´ä¸ªæ¶é´æ®µã
error.rate.sheet.discontinue.in.use.by.insert.schedule.for.list=ç»æ­¢ {0} å°ä¼å½±åä»¥ä¸æå¥æ¶é´è¡¨: {1}
error.rate.sheet.discontinue.in.use.by.insert.schedule=ç»æ­¢è¯¥è¯ä»·è¡¨ä¼å½±åä»¥ä¸æå¥æ¶é´è¡¨: {0}
error.rate.sheet.discontinue.start.date.in.future.for.list={0} æ æ³ç»æ­¢ï¼å ä¸ºå®çå¼å§æ¥ææ¯å½æ¥æå°æ¥æä¸ªæ¥æ
error.rate.sheet.discontinue.start.date.in.future=è¯¥è¯ä»·è¡¨æ æ³ç»æ­¢ï¼å ä¸ºå®çå¼å§æ¥ææ¯å½æ¥æå°æ¥æä¸ªæ¥æ
error.rate.sheet.envelope.weight.range=è¯·ä¸ºä¿¡å°ééè¾å¥ä¸ä¸ªä»äº 0.0001 å 99.9999 ä¹é´çæææ°å­
error.rate.sheet.in.use.by.document.for.list={0} ç±ä¸åæ¥è§¦ç¹ä½¿ç¨: {1}
error.rate.sheet.in.use.by.document=è¯¥è¯ä»·è¡¨æ­£ç±ä¸åæ¥è§¦ç¹ä½¿ç¨: {0}
error.rate.sheet.in.use.by.insert.schedule.for.list={0} æ­£ç±ä¸åæå¥æ¶é´è¡¨ä½¿ç¨: {1}
error.rate.sheet.in.use.by.insert.schedule=è¯¥è¯ä»·è¡¨æ­£ç±ä¸åæå¥æ¶é´è¡¨ä½¿ç¨: {0}
error.rate.sheet.name.unique=åç§°å·²ä½¿ç¨ãè¯·è¾å¥å¯ä¸çåç§°ã
error.rate.sheet.rate.range=è¯·ä¸ºéå¼ææ¬è¾å¥ä¸ä¸ªä»äº 0.01 å° 99.99 ä¹é´çæææ°å­
error.rate.sheet.specify.all.consts=è¯·ä¸ºææéå¼ææ¬è¾å¥æææ°å­
error.rate.sheet.startdate.before.end.date=å¼å§æ¥æä¸è½æäºç»ææ¥æ
error.rate.sheet.startdate.before.previous.schedule.end.date=å¼å§æ¥æå¿é¡»æäºä¹åè¯ä»·è¡¨çç»ææ¥æ
error.rate.sheet.startdate.before.previous.schedule.start.date=å¼å§æ¥æå¿é¡»æäºä¹åè¯ä»·è¡¨çå¼å§æ¥æ
error.rate.sheet.weight.range.gram=è¯·ä¸ºééè¾å¥ä¸ä¸ªä»äº 0.0001 å 1999.9999 (æå¤§ä¸ºåä½å°æ°)ä¹é´çæææ°å­
error.rate.sheet.weight.range.ounce=è¯·ä¸ºééè¾å¥ä¸ä¸ªä»äº 0.0001 å 99.9999 (æå¤§ä¸ºåä½å°æ°)ä¹é´çæææ°å­
error.rate.weights.and.rates.order.mismatch=éå¼æéåéå¼çä¸å¹é
error.rationalizer.shared.content.name.unique=That name is already taken
error.same.pod.cannot.be.set.to.both.production.and.transition.for.a.domain=æ æ³ä¸ºåççäº§çæ¬åé¢åå¸çæ¬è®¾ç½®ç¸åç Podã
error.section.containing.zones.cannot.be.deleted=æéé¨ååæ¬ä¸ä¸ªæå¤ä¸ªåºåãä¸åè®¸å é¤é¨åã
error.section.must.specify.name=æ¯ä¸ªæ¥è§¦ç¹é¨åå¿é¡»å·æä¸ä¸ªåç§°ãè¯·ä¸ºä¸åé¨åæä¾åç§°: {0}
error.section.name.must.be.unique=å¿é¡»ä¸ºæ¯ä¸ªæ¥è§¦ç¹é¨åæä¾å¯ä¸çåç§°
error.section.name.required=å¿é¡»æä¾é¨ååç§°
error.section.name.too.long=é¨åçåç§°é¿åº¦ä¸å¾è¶è¿ 96 ä¸ªå­ç¬¦
error.select.file.to.import=è¾å¥å­æ®µä¸ºç©ºãè¯·éæ©è¦å¯¼å¥çæä»¶.
error.selected.import.file.empty=æéæä»¶â{0}âä¸ºç©ºãè¯·éæ©ä¸ä¸ªæææä»¶ä»¥è¿è¡å¯¼å¥ã
error.selected.import.file.not.xml=æéæä»¶â{0}âä¸æ¯ææç XML æä»¶ã
error.simulation.enterrundate=è¯·è¾å¥è¿è¡æ¥æ
error.simulation.selectdataResource=è¯·éæ©æ°æ®èµæº
error.simulation.selectdocument=è¯·éæ©æ¥è§¦ç¹
error.simulation.selectmessage=æ²¡æçäº§ä¸­æ¶æ¯ãè¯·è³å°éæ©ä¸æ¡æ¶æ¯
error.smart.text.variables.not.mapped=å¨è¯¥æºè½ææ¬ä¸­ä½¿ç¨çä¸ä¸ªæå¤ä¸ªåéæªæ­£ç¡®æ å°ãè¯·ä¸ºåéçæ¥è§¦ç¹è§£æåéæ å°ã
error.tenant.management.cannot.duplicate.sheet.counts=å¯¹äºä»»ä½ç»å®ç§æ·ï¼ä¸¤ä¸ªä¸åè¯ä»·è¡¨çè¡¨è®¡æ°éå¼ä¸å¾ç¸åã
error.tenant.management.no.name=è¯·ä¸º {0} æå®ç§æ·æ¶é´è¡¨åç§°
error.tenant.management.no.tenant.bin.assignment=è¯·å¨åå¸ä¹åè³å°æå®ä¸ä¸ª Bin ç»ç§æ·ã
error.tenant.management.tenant.window.closed=âç§æ·âçªå£å·²å³é­ï¼è¯·å¨åå¸ä¹åå±å¼ç§æ·çªå£ã
error.tenant.management.tenant.window.required=è¯·ä¸º {0} æå®ç§æ·çªå£
error.tenant.management.touchpoint.not.shared=è¯·å¨åå¸ä¹åè³å°å°æ¥è§¦ç¹å±äº«ç»ä¸ä¸ªç§æ·ã
error.tenant.manangement.end.date.after.insert.schedule.start.date=æä½ç»ææ¥æå¿é¡»æ©äºæå¥æ¶é´è¡¨å¼å§æ¥æ
error.tenant.manangement.start.date.after.end.date=æä½å¼å§æ¥æå¿é¡»æ©äºç»ææ¥æ
error.tenant.manangement.start.date.after.insert.schedule.start.date=æä½å¼å§æ¥æå¿é¡»æ©äºæå¥æ¶é´è¡¨å¼å§æ¥æ
error.theme.cssfilemissing=é¢ææ¯ä¸ä¸ªåä¸º {0} ç CSS æä»¶
error.theme.duplicate.logo.filename=æéå¾½æ æä»¶åå·²å­å¨ãè¯·æ´æ¹åç§°å¹¶éæ°ä¸ä¼ ã
error.theme.propertiesfilemissing=é¢ææ¯ä¸ä¸ªåä¸º {0} çå±æ§æä»¶
error.touchpoint.collection.is.being.referenced.on.delete=æéæ¥è§¦ç¹éåæ­£è¢«å¼ç¨ãè¯·å¨å é¤ä¹åå é¤ææå¼ç¨ã
error.touchpointselection.cannot.delete.has.been.previously.active=æéçä¸ä¸ªæå¤ä¸ªåéæ æ³å é¤ï¼å ä¸ºå®ä»¬ä¹åå·²è¢«æ¹åå¹¶å¤äºæ´»å¨ç¶æ
error.touchpointselection.cannot.delete.has.nonremoved.message=æéçä¸ä¸ªæå¤ä¸ªåéæ æ³å é¤ï¼å ä¸ºå®ä»¬å·æå¤äºå·¥ä½ãæ´»å¨æå­æ¡£ç¶æçå³èæ¶æ¯ã
error.touchpointselection.cannot.delete.has.selection=æ¨æ æ³å é¤è¯¥åéï¼å ä¸ºå®æå­çº§
error.touchpointselection.cannot.delete.is.master=æ æ³å é¤ä¸»åé
error.touchpointselection.content.custom.content.must.be.provided=å¨ä¿å­æ´æ¹ä¹åï¼è³å°å¿é¡»ä¸ºèªå®ä¹åå®¹æä¾ä¸ç§è¯­è¨
error.touchpointselection.content.default.lang.content.must.be.provided=è¯·ä¸ºé»è®¤è¯­è¨æä¾åå®¹
error.touchpointselection.content.type.must.be.selected=å¨ä¿å­æ´æ¹ä¹åï¼å¿é¡»åéæ©æ¯å¦ç¼åèªå®ä¹åå®¹ãç¦ç¨æéç¨ç°æåå®¹
error.touchpointselection.matching.variant.no.visibility=å·²æ¾å°å¹éçåéï¼ä½æ¯è¯¥èç¹å¯¹æ¨ä¸å¯è§
error.touchpointselection.selection.hierarchy.may.not.exceed.x=æ æ³å¨æ­¤å¤æ·»å åéï¼å ä¸ºåéå±æ¬¡ç»æä¸è½è¶è¿ {0} çº§
error.touchpointselection.selection.name.already.exists=å¨åä¸åæ¯ä¹ä¸å·²å­å¨å·æè¯¥åç§°çåé
error.touchpointselection.selectors.add.no.value.provided=è¯·æä¾è¦æ·»å çéæ©æ¡ä»¶
error.touchpointselection.selectors.add.selection.criteria.conflicts.with.an.existing.selection=æä¾çéæ©æ¡ä»¶ (\"{0}\") ä¼éå¤æ åçåé \"{1}\" ï¼å æ­¤æ æ³æ·»å 
error.touchpointselection.selectors.add.selection.criteria.falls.outside.criteria.for.its.parent.selection=æä¾çéæ©å¼ (\"{0}\") ä¸å¨å¶æ ¹ç¶çº§ {1} çå¼èå´å
error.touchpointselection.selectors.add.selection.redefined.selection.criteria.would.result.in.orphaning.the.criteria.for.the.child.selection=åºäºå¯è§æ§ç®çï¼æ¨å¿é¡»åä»åé {1} ä¸­å é¤æ°æ® {0} ã
error.touchpointselection.selectors.combination.is.added.before=æä¾çéæ©æ¡ä»¶ (\"{0}\") ä¸æ­¤åéçç°ææ¡ä»¶éå¤ï¼å æ­¤æ æ³æ·»å 
error.touchpointselection.selectors.remove.no.value.provided=è¯·æä¾è¦å é¤çéæ©æ¡ä»¶
error.touchpointselection.selectors.remove.no.value.to.remove=æ²¡æå·²å®ä¹çéæ©æ¡ä»¶
error.touchpointselection.visibility.current.user.not.selected=æ æ³éå¶å½åç¨æ·çå¯è§æ§
error.touchpointselection.visibility.no.user.selected=åºäºå¯è§æ§ç®çï¼å¿é¡»è³å°éæ©ä¸ä¸ªç¨æ·
error.upload.file.is.empty={0} è¦æ±ä¸ä¼ ä¸ä¸ªæ°æ®æä»¶
error.user.morethanonefound=éè¿æ¨è¾å¥ççµå­é®ä»¶åç°äºå¤ä¸ªç¨æ·ãè¯·è¾å¥ç¨æ·åã
error.user.notfound=æ±æ­ãæ¨è¾å¥çç¨æ·åä¸å­å¨ãè¯·åè¯ä¸æ¬¡...
error.user.settings.incorrectpassword=å½åå¯ç ä¸æ­£ç¡®
error.user.settings.passwordsdonotmatch=å¯ç ä¸å¹é
error.user.settings.resetKeynotvalid=æ­¤é¾æ¥æ æãè¯·ä¸æ¨çæ¶æ¯ç¹ç®¡çåèç³»ä»¥è¯·æ±æ°é¾æ¥ã
error.user.settings.resetKeynotvalidOr=æ­¤é¾æ¥æ æãè¯·ä¸æ¨çæ¶æ¯ç¹ç®¡çåèç³»ï¼æè 
error.variable.selection.required={0} è¦æ±è¿è¡åééæ©ã
error.variant.name.already.existing=åéåå·²å­å¨ã
error.variant.name.can.not.be.empty=åç§°ä¸è½ä¸ºç©ºã
error.workflow.approvals.approver.is.not.authorized=å¨ç¼è¾è¯¥å·¥ä½æµæé´ä¸ä¸ªæå¤ä¸ªç¨æ·çè®¿é®æéåçæ´æ¹ãå æ­¤å·¥ä½æµå·²æ´æ°ãè¯·ç¡®è®¤ï¼ç¶ååæ¬¡åå»âä¿å­âã
error.workflow.approvals.at.least.one.user.needed.for.each.approval.step=å¿é¡»è³å°æ¯ä¸ªå·¥ä½æµæ­¥éª¤åéä¸ä¸ªæ¹åèã
error.workflow.approvals.owner.is.mandatory.for.workflow.has.step=å¿é¡»éæ©å·¥ä½æµææèã
error.workflow.approvals.owner.is.not.authorized=å·¥ä½æµææèä¸åè·å¾ææãè¯·éæ°ææï¼ææå®æ°çå·¥ä½æµææèãè¯·æ³¨æï¼å·¥ä½æµææèä¸è½æ¯æ´»å¨æ¹åèã
error.workflow.approvals.step.name.cannot.be.empty=å·¥ä½æµæ­¥éª¤çåç§°ä¸è½ä¸ºç©ºã
error.workflow.approvals.timeframe.hour.too.large=æ¶é´æ®µå°æ¶å¼ä¸è½è¶è¿ 23ã
error.workflow.approvals.timeframe.not.specified=å¿é¡»è³å°è¾å¥ä¸å°æ¶çæ¶é´æ®µã
error.workflow.approvals.user.cannot.be.both.owner.and.approver=ç¨æ·ä¸è½åæ¶ä½ä¸ºå·¥ä½æµææèåæ­¥éª¤çæ¹åèã
licence.label.ClickatellConnector=Clickatell
licence.label.DataAnonymizer=æ°æ®å¿åç¨åº
licence.label.DialogueConnector=HP Exstream
licence.label.DialogueDXF=DXF
licence.label.ExactTargetConnector=åç¡®ç®æ 
licence.label.FTPWebConnector=FTP Web
licence.label.GMCConnector=GMC
licence.label.InsertManagement=æå¥ç®¡ç
licence.label.MessagepointInteractive=æ¶æ¯ç¹å·²è¿æ¥
licence.label.NativePrintConnector=æ¬æºç»å
licence.label.NumberOfFullUsers=æ®éç¨æ·
licence.label.NumberOfRestrictedUsers=åéç¨æ·
licence.label.NumberOfSandboxes=æ²ç 
licence.label.NumberOfTenants=çäº§
licence.label.SendMailConnector=åéé®ä»¶è¿æ¥å¨
licence.label.Simulation=æ¨¡æ
licence.label.VariantManagement=åéç®¡ç
licence.label.eMessagingEmailConnector=çµå­æ¶æ¯ççµå­é®ä»¶
licence.label.eMessagingSMSConnector=çµå­æ¶æ¯ç SMS
licence.label.sorting.ClickatellConnector=Clickatell
licence.label.sorting.DataAnonymizer=æ°æ®å¿åç¨åº
licence.label.sorting.DialogueConnector=HP Exstream
licence.label.sorting.DialogueDXF=DXF
licence.label.sorting.ExactTargetConnector=åç¡®ç®æ 
licence.label.sorting.FTPWebConnector=FTP Web
licence.label.sorting.GMCConnector=GMC
licence.label.sorting.InsertManagement=æå¥ç®¡ç
licence.label.sorting.MessagepointInteractive=æ¶æ¯ç¹å·²è¿æ¥
licence.label.sorting.NativePrintConnector=æ¬æºç»å
licence.label.sorting.NumberOfFullUsers=1
licence.label.sorting.NumberOfRestrictedUsers=2
licence.label.sorting.NumberOfSandboxes=4 
licence.label.sorting.NumberOfTenants=3
licence.label.sorting.SendMailConnector=åéé®ä»¶è¿æ¥å¨
licence.label.sorting.Simulation=æ¨¡æ
licence.label.sorting.VariantManagement=åéç®¡ç
licence.label.sorting.eMessagingEmailConnector=çµå­æ¶æ¯ççµå­é®ä»¶
licence.label.sorting.eMessagingSMSConnector=çµå­æ¶æ¯ç SMS
page.error.web.service.failed=æ æ³æ£ç´¢æ°æ®ï¼Web æå¡å¤±è´¥ãè¯·èç³»æ¨çç®¡çåã
page.flow.approve=æ¹å
page.flow.back=ä¸ä¸æ­¥
page.flow.cancel=åæ¶
page.flow.confirm=ç¡®è®¤
page.flow.discard=æ¾å¼
page.flow.forward=ç»§ç»­
page.flow.reject.toauthoring=æç»ææ
page.flow.reject=æç»
page.flow.requestpreview=è¯·æ±é¢è§
page.flow.save=ä¿å­
page.flow.saveandback=ä¸ä¸æ­¥
page.flow.saveandforward=ç»§ç»­
page.label.404error=404 éè¯¯
page.label.ACTIVE=æ´»å¨
page.label.ADD.TOUCHPOINT=æ·»å æ¥è§¦ç¹
page.label.ADD=æ·»å 
page.label.ALL=ææ
page.label.APPLICATION.SIGN.IN=åºç¨ç¨åºç»å½
page.label.ARCHIVE=å­æ¡£
page.label.ASSIGNED.TO=åéå°
page.label.ASSIGNED=å·²åé
page.label.ASSIGNMENT.WINDOW=åéçªå£
page.label.AUDIT=å®¡æ ¸
page.label.AVAILABLE.INSERTS=å¯ç¨æå¥
page.label.AVAILABLE.MANDATORY.INSERTS=å¯ç¨å¼ºå¶æå¥
page.label.AVAILABLE.MESSAGES=å¯ç¨æ¶æ¯
page.label.AVAILABLE.OPTIONAL.INSERTS=å¯ç¨å¯éæå¥
page.label.AVAILABLE.PARAGRAPH.STYLES=å¯ç¨æ®µè½æ ·å¼
page.label.AVAILABLE.TEXT.STYLES=å¯ç¨ææ¬æ ·å¼
page.label.AVAILABLE.TOUCHPOINTS=å¯ç¨æ¥è§¦ç¹
page.label.AVAILABLE.USERS=å¯ç¨ç¨æ·
page.label.AVAILABLE.VARIABLES=å¯ç¨åé
page.label.AVAILABLE.WORKGROUPS=å¯ç¨å·¥ä½ç»
page.label.Add.message=æ·»å æ¶æ¯
page.label.Add.target.group=æ·»å ç®æ ç»
page.label.BACK.TO.MESSAGE=<< è¿åæ¶æ¯
page.label.BACK=è¿å
page.label.CANCEL=åæ¶
page.label.CATEGORY=ç±»å«
page.label.CLONE=åé
page.label.CLOSE=å³é­
page.label.COLLECTION.DEFAULT=éåé»è®¤å¼
page.label.COMPOUND.KEY.VAR=ç»åé® VAR
page.label.COST=ææ¬
page.label.DATE.RANGE=æ¥æèå´
page.label.DELIVERY=ä¼ é
page.label.DESCRIPTION=æè¿°
page.label.DESELECT.ALL=åæ¶å¨é
page.label.DETAILS=è¯¦ç»ä¿¡æ¯
page.label.DOWNLOAD.VARIANTS=ä¸è½½åé
page.label.Delivery=ä¼ é
page.label.EBCDIC=EBCDIC
page.label.FRONT=å­ä½
page.label.GLOBAL=å¨å±
page.label.HP=HP
page.label.ID=ID
page.label.IMAGE.SELECTION=å¾åéæ©
page.label.IMAGES.DISPLAYED.HERE=å¨æ­¤æ¾ç¤ºå¾å
page.label.IMPORT.IMAGES=å¯¼å¥å¾å
page.label.IMPORT.TOUCHPOINT=å¯¼å¥æ¥è§¦ç¹
page.label.INSERT.SCHEDULE=æå¥æ¶é´è¡¨
page.label.INSERTS.SELECTED=æå¥æé
page.label.If=å¦æ
page.label.KEYWORDS=å³é®å­
page.label.LANGUAGE=è¯­è¨
page.label.Location=ä½ç½®
page.label.MAKE.LOCAL.COPY=åå»ºæ¬å°å¯æ¬
page.label.METATAGS=åæ è®°
page.label.NA=ä¸éç¨
page.label.NAME=åç§°
page.label.NEXT.ACTION=ä¸ä¸ä¸ªæä½
page.label.NEXT=ä¸ä¸æ­¥ >>
page.label.ONE=ä¸ä¸ª
page.label.ORGANIZE=ç»ç»
page.label.OWNER=ææè
page.label.PART=é¨å
page.label.PARTS=é¨å
page.label.POD.MASTER=ä¸» POD
page.label.PREV=<< é¢è§
page.label.PREVIEW=é¢è§
page.label.PREVIOUS=<< ä¸ä¸ä¸ª
page.label.PRINT=æå°
page.label.PRIORITIZE=è®¾ç½®ä¼åçº§
page.label.PRIORITY=ä¼åçº§
page.label.PROOF=ææ ·
page.label.PostProcess.XSLT.File=PostProcess XSLT æä»¶
page.label.PreProcess.XSLT.File=PreProcess XSLT æä»¶
page.label.REMOVE=ç§»é¤
page.label.RESET=éç½®
page.label.RESOLVE.DATA.GROUPS=å¤çæ°æ®æ°æ®ç»
page.label.SAVE=ä¿å­
page.label.SCHEDULE.ID=æ¶é´è¡¨ ID
page.label.SECTIONS=è
page.label.SEGMENTATION=åæ®µ
page.label.SELECT.ALL=éæ©å¨é¨
page.label.SELECTED.VARIABLES=æéåé
page.label.SESSION.TIMED.OUT=ä¼è¯è¶æ¶
page.label.SET=è®¾ç½®
page.label.SHARED.AS=å±äº«ä¸º
page.label.SHOW.AND.MAP.VARIABLES=æ¾ç¤ºå¹¶æ å°ææåé
page.label.SHOW=æ¾ç¤º
page.label.SITE=ç«ç¹
page.label.STARTTLS=STARTTLS
page.label.STATUS=ç¶æ
page.label.STOCK.ID=åºå­ ID
page.label.SUBMIT=æäº¤
page.label.TARGET.ZONE=ç®æ åºå
page.label.TENANT.WINDOD=ç§æ·çªå£
page.label.TENANT=ç§æ·
page.label.THROTTLE=éå¶
page.label.TLS=TLS
page.label.TOUCHPOINT.DEFAULT=é»è®¤æ¥è§¦ç¹
page.label.TOUCHPOINT=æ¥è§¦ç¹
page.label.TOUCHPOINTS=æ¥è§¦ç¹
page.label.TYPE=ç±»å
page.label.UPLOAD.VARIANTS=ä¸ä¼ åé
page.label.USAGE=ä½¿ç¨
page.label.USED.BY=ä½¿ç¨è
page.label.VARIABLE=åé
page.label.VIEW.DATA=æ¥çæ°æ®
page.label.VIEW=æ¥ç
page.label.WEIGHT=éé
page.label.WHEN=å½
page.label.WORKING.COPY=å·¥ä½å¯æ¬
page.label.XML.format.type=XML æ ¼å¼ç±»å
page.label.XML=XML
page.label.ZONES=åºå
page.label.above=ä¸æ¹
page.label.access.control=è®¿é®æ§å¶
page.label.action.item=æä½é¡¹ç®
page.label.action.required=æéæä½
page.label.action=æä½
page.label.actions=æä½
page.label.activate=æ¿æ´»
page.label.activation.pending=å¾æ¿æ´»
page.label.active.tag=æ´»å¨æ è®°
page.label.active=æ´»å¨
page.label.activeusers=æ´»å¨ç¨æ·
page.label.actual.deliveries=å®éä¼ é
page.label.add.attribute=æ·»å å±æ§
page.label.add.batch.report=æ°å»ºæ¹
page.label.add.branch=æ·»å å
page.label.add.collection=æ·»å éå
page.label.add.communication=æ·»å éä¿¡
page.label.add.connection=æ·»å è¿æ¥
page.label.add.constant=æ·»å æ°å¸¸é
page.label.add.content.selector.group=æ·»å åå®¹éæ©å¨ç»
page.label.add.content.selector=æ·»å åå®¹éæ©å¨
page.label.add.data.collection=æ·»å æ°æ®éå
page.label.add.data.element=æ·»å æ°æ®åç´ 
page.label.add.data.file=æ·»å æ°æ®æä»¶
page.label.add.data.group=æ·»å æ°æ®ç»
page.label.add.data.record=æ·»å æ°æ®è®°å½
page.label.add.data.resource=æ·»å æ°æ®èµæº
page.label.add.data.source=æ·»å æ°æ®æº
page.label.add.data=æ·»å æ°æ®
page.label.add.delivery.event=æ·»å ä¼ éäºä»¶
page.label.add.element=æ·»å åç´ 
page.label.add.event=æ·»å äºä»¶
page.label.add.files=æ·»å æä»¶...
page.label.add.filter=æ·»å ç­éå¨
page.label.add.image=æ·»å å¾å
page.label.add.indicator=æ·»å æç¤ºç¬¦
page.label.add.insert.schedule=æ·»å æå¥æ¶é´è¡¨
page.label.add.insert=æ·»å æå¥
page.label.add.item=æ·»å é¡¹ç®
page.label.add.language=æ·»å è¯­è¨
page.label.add.licence=æ·»å è®¸å¯è¯
page.label.add.menu.item=æ·»å èåé¡¹
page.label.add.message.delivery.report=æ°å»ºæ¶æ¯ä¼ é
page.label.add.message=æ·»å æ¶æ¯
page.label.add.node=æ·»å å®ä¾
page.label.add.paragraph.style=æ·»å æ®µè½æ ·å¼
page.label.add.pod=æ·»å  Pod
page.label.add.rate.sheet=æ·»å è¯ä»·è¡¨
page.label.add.reference.data=æ·»å 
page.label.add.role=æ·»å è§è²
page.label.add.rule.condition=æ·»å è§åæ¡ä»¶
page.label.add.rule=æ·»å è§å
page.label.add.selection=æ·»å éæ©
page.label.add.simulation=æ°å»ºæ¨¡æ
page.label.add.smart.text=æ·»å æºè½ææ¬
page.label.add.step=æ·»å æ­¥éª¤
page.label.add.style.family=æ·»å æ ·å¼ç³»å
page.label.add.style=æ·»å æ ·å¼
page.label.add.sub.branch=æ·»å å­å
page.label.add.sub.variant=æ·»å å­åé
page.label.add.tag.type=æ·»å æ è®°æ ·å¼
page.label.add.tag=æ·»å æ è®°
page.label.add.target.group=æ·»å ç®æ ç»
page.label.add.task=æ·»å ä»»å¡
page.label.add.test=æ°å»ºæµè¯
page.label.add.to.favorites=æ·»å å°æ¶èå¤¹
page.label.add.touchpoint.delivery.report=æ°å»ºæ¥è§¦ç¹ä¼ é
page.label.add.touchpoint=æ·»å æ¥è§¦ç¹
page.label.add.variable=æ·»å åé
page.label.add.variant=æ·»å åé
page.label.add=æ·»å 
page.label.admin.role.createnewrole=æ·»å è§è²
page.label.admin.role.updaterole=ç¼è¾è§è² - {0}
page.label.admin.systemproperties.audit_report_dir=å®¡æ ¸æ¥è¡¨
page.label.admin.systemproperties.connected_composition_delivery=ç»åä¼ é
page.label.admin.systemproperties.csrf_domain_strict=CSRF åéå¶
page.label.admin.systemproperties.de_versions=DE çæ¬
page.label.admin.systemproperties.default_boolean_format_false=False å¸å°å¼
page.label.admin.systemproperties.default_boolean_format_true=True å¸å°å¼
page.label.admin.systemproperties.default_date_format=æ¥ææ ¼å¼
page.label.admin.systemproperties.default_page=æ¾ç¤ºçç¬¬ä¸é¡µ
page.label.admin.systemproperties.default_page_size=æ¯ä¸ªåè¡¨çæ¡ç®æ°
page.label.admin.systemproperties.default_system_language=ç³»ç»é»è®¤è¯­è¨(åºåè®¾ç½®)
page.label.admin.systemproperties.diagnostics_report_dir=è¯æ­æ¥å
page.label.admin.systemproperties.email_cfg_appFromAddress=åå¤å°å
page.label.admin.systemproperties.email_connection_security=è¿æ¥å®å¨
page.label.admin.systemproperties.email_pop3_host=POP3 ä¸»æº
page.label.admin.systemproperties.email_pop3_password=POP3 å¯ç 
page.label.admin.systemproperties.email_pop3_port=POP3 ç«¯å£
page.label.admin.systemproperties.email_pop3_user=POP3 ç¨æ·
page.label.admin.systemproperties.email_preview_dir=çµå­é®ä»¶é¢è§
page.label.admin.systemproperties.email_server_outgoing=æå¡å¨
page.label.admin.systemproperties.email_server_outgoing_password=å¸æ·å¯ç 
page.label.admin.systemproperties.email_server_outgoing_port=ç«¯å£
page.label.admin.systemproperties.email_server_outgoing_user=å¸æ·å
page.label.admin.systemproperties.email_template_dir=çµå­é®ä»¶æ¨¡æ¿
page.label.admin.systemproperties.email_webroot_dir=çµå­é®ä»¶ç½é¡µæ ¹ç®å½
page.label.admin.systemproperties.insert_startingBinNo=èµ·å§ Bin ç¼å·
page.label.admin.systemproperties.insert_weightUnits=æéåä½
page.label.admin.systemproperties.insertschedule_export_dir=æå¥æ¶é´è¡¨å¯¼åº
page.label.admin.systemproperties.job_folder_incoming=ä¼ å¥ä½ä¸
page.label.admin.systemproperties.job_folder_incoming_failure=å¤±è´¥çä½ä¸
page.label.admin.systemproperties.job_folder_incoming_sucess=æåçä½ä¸
page.label.admin.systemproperties.job_folder_outgoing=ä¼ åºä½ä¸
page.label.admin.systemproperties.job_folder_working=æ­£å¨è¿è¡
page.label.admin.systemproperties.job_proof_customers_max=æ¯ä¸ªææ ·çæå¤§å®¢æ·æ°é
page.label.admin.systemproperties.job_test_customers_max=æ¯ä¸ªæµè¯çæå¤§å®¢æ·æ°é
page.label.admin.systemproperties.message_folder_application=åºç¨ç¨åº Webroot
page.label.admin.systemproperties.message_folder_applicationcache=ç¼å­
page.label.admin.systemproperties.message_folder_applicationreport=åºç¨ç¨åºæ¥è¡¨
page.label.admin.systemproperties.message_folder_compositionfileset=ç»åæä»¶
page.label.admin.systemproperties.message_folder_datafiles=æ°æ®æä»¶
page.label.admin.systemproperties.message_folder_dispatch=è°åº¦
page.label.admin.systemproperties.message_folder_export=æ¶æ¯å¯¼åº
page.label.admin.systemproperties.message_folder_images=å¾å
page.label.admin.systemproperties.message_folder_output=æ¶æ¯è¾åº
page.label.admin.systemproperties.message_folder_targetingdatafiles=ç®æ æ°æ®æä»¶
page.label.admin.systemproperties.message_folder_xmlconnector=XML æ³åè¿æ¥å¨æä»¶
page.label.admin.systemproperties.message_messageContent_history_dir=å¾ååå®¹åå²è®°å½
page.label.admin.systemproperties.messagepoint.qe.fileroot=å®¢æ·ç«¯ DE
page.label.admin.systemproperties.messagepoint_object_export_dir=å¯¹è±¡å¯¼åº
page.label.admin.systemproperties.password_expire_after=å¯ç è¿ææ¶é´
page.label.admin.systemproperties.password_expires=å¯ç è¿æ
page.label.admin.systemproperties.password_history_entries=å¯ç åå²è®°å½æ¡ç®(å«å½åæ¡ç®) 
page.label.admin.systemproperties.password_limit_reuse_period=éå¶å¯ç éç¨æ
page.label.admin.systemproperties.password_limit_reuse_period_months=å¯ç éç¨æ(æ)
page.label.admin.systemproperties.password_reset_keepalive=å¯ç éç½®ä¿ææ´»å¨æ¶é´(åé)
page.label.admin.systemproperties.platform_encodingtype_preview=é¢è§ç¼ç 
page.label.admin.systemproperties.platform_encodingtype_production=æ´»å¨ç¼ç 
page.label.admin.systemproperties.platform_encodingtype_test=æµè¯ç¼ç 
page.label.admin.systemproperties.prevent_repeated_password=é²æ­¢éå¤å¯ç 
page.label.admin.systemproperties.report_dir=æ¥è¡¨
page.label.admin.systemproperties.restrict_signin_autocomplete=éå¶ç»å½èªå¨å®æåè½
page.label.admin.systemproperties.session_timeout=ä¼è¯è¶æ¶(åé)
page.label.admin.systemproperties.simulation_export_dir=æ¨¡æå¯¼åº
page.label.admin.systemproperties.sso_url_redirect=å¨âéåºâURL ä¸
page.label.admin.systemproperties.system_date_format=æ¥ææ ¼å¼
page.label.admin.systemproperties.system_server_port=ç«¯å£
page.label.admin.systemproperties.system_server_protocol=åè®®
page.label.admin.systemproperties.system_server_webroot=Webroot
page.label.admin.systemproperties.target_rule_separator_is_one_of=âå±äºè¿ä¸âçç®æ è§ååéç¬¦
page.label.admin.systemproperties.test_mode=æµè¯æ¨¡å¼
page.label.admin.systemproperties.where_used_report_dir=ä½¿ç¨ä½ç½®æ¥è¡¨
page.label.admin.systemproperties_global_sendemail_booleanflag=å¯ç¨çµå­é®ä»¶
page.label.admin.user.createnewuser=æ°å»ºç¨æ·
page.label.admin.user.updateuser=ç¼è¾ç¨æ· - {0}
page.label.admin=ç®¡çå
page.label.advanced.search=é«çº§æç´¢
page.label.advanced=é«çº§
page.label.aggregation.level=èåçº§å«
page.label.aggregation=èå
page.label.alignment=å¯¹é½
page.label.all.categories=ææç±»å«
page.label.all.content=ææåå®¹
page.label.all.messages=æææ¶æ¯
page.label.all.sections=ææé¨å
page.label.all.shared.content=ææå±æåå®¹
page.label.all.sites=ææç«ç¹
page.label.all.zones=ææåºå
page.label.all=ææ
page.label.alphanumeric.only=ä»éå­æ¯æ°å­å­ç¬¦
page.label.alternate.templates=å¤ç¨æ¨¡æ¿
page.label.alternate_templates=å¤ç¨æ¨¡æ¿
page.label.analysis.enabled=å¯ç¨åæ
page.label.analytics=åæ
page.label.ancestors=ä¸çº§
page.label.and.include=åæ¬
page.label.and.of.these.include.groups=å¶ä¸­åæ¬ç»
page.label.and=ä¸
page.label.anonymization.type.address.apt=å°åè¡ 2 (å¬å¯ãå¥æ¿ãåå)
page.label.anonymization.type.address.street=å°åè¡ 1 (è¡éå°å)
page.label.anonymization.type.age=å¹´é¾
page.label.anonymization.type.city=åå¸
page.label.anonymization.type.currency=è´§å¸
page.label.anonymization.type.date=æ¥æ
page.label.anonymization.type.decimal=å°æ°
page.label.anonymization.type.first.name=åå­
page.label.anonymization.type.integer=æ´æ°
page.label.anonymization.type.last.name=å§æ°
page.label.anonymization.type.name=åç§°
page.label.anonymization.type.num.text=ç¼å·
page.label.anonymization.type.post.zip=é®æ¿æé®æ¿ç¼ç 
page.label.anonymization.type.post=é®ç¼
page.label.anonymization.type.prov.state=çæå·
page.label.anonymization.type.prov=ç
page.label.anonymization.type.sequence=åºå
page.label.anonymization.type.state=ç¶æ
page.label.anonymization.type.telephone=çµè¯å·ç 
page.label.anonymization.type.text=ææ¬
page.label.anonymization.type.yes.no=âæ¯âæâå¦â
page.label.anonymization.type.zip=é®ç¼
page.label.anonymized=å¿å
page.label.any.status=ä»»ä½ç¶æ
page.label.apply.border=è®¾ç½®è¾¹æ¡
page.label.apply.default=åºç¨é»è®¤è®¾ç½®
page.label.apply.to=åºç¨äº
page.label.apply=åºç¨
page.label.approval.type.all.of=ææ
page.label.approval.type.any.of=ä»»ä¸
page.label.approval.type=æ¹åç±»å
page.label.approval=æ¹å
page.label.approvals=æ¹å
page.label.approve.and.override=æ¹ååæ¿ä»£
page.label.approve.reject=æ¹å/æç»
page.label.approve.workflow.owner=æ¹å: å·¥ä½æµææè
page.label.approve=æ¹å
page.label.approved=å·²æ¹å
page.label.approvers=æ¹åè
page.label.archive.inserts=æå¥å­æ¡£
page.label.archive.messages=å­æ¡£æ¶æ¯
page.label.archive=å­æ¡£
page.label.as.embedded=ä½ä¸ºåµå¥å¼
page.label.as.landing.page=ä½ä¸ºç»å½é¡µé¢
page.label.ascii=ASCII
page.label.assign=åé
page.label.assigned.to=åéå°
page.label.assigned=å·²åé
page.label.assignments=åé
page.label.associate.touchpoints=å³èæ¥è§¦ç¹
page.label.associated.data.records=å³èçæ°æ®è®°å½
page.label.association.key=å³èå³é®å­
page.label.association.properties=å³èå±æ§
page.label.associations=å³è
page.label.at.least.one=è³å°ä¸ä¸ª
page.label.attachment.delivery.type.mandatory=å¿é
page.label.attachment.delivery.type.optional=å¯é
page.label.attachment.management=éä»¶ç®¡ç
page.label.attachment.targeting=éä»¶ç®æ æå®
page.label.attachment=éä»¶
page.label.attempts=å°è¯
page.label.attribute=å±æ§
page.label.attributes=å±æ§
page.label.audit.report.status.complete=å®æ
page.label.audit.report.status.error=éè¯¯
page.label.audit.report.status.expired=è¿æ
page.label.audit.report.status.in.process=æ­£å¨è¿è¡
page.label.audit.report.type.insert.audit.report=æå¥å®¡æ ¸æ¥è¡¨
page.label.audit.report.type.message.audit.report=æ¶æ¯å®¡æ ¸æ¥è¡¨
page.label.audit.report.type.touchpoint.audit.report=æ¥è§¦ç¹å®¡æ ¸æ¥è¡¨
page.label.audit.report=å®¡æ ¸æ¥è¡¨...
page.label.audit=å®¡æ ¸
page.label.authentication.failed=èº«ä»½éªè¯å¤±è´¥
page.label.auto.detect=èªå¨æ£æµ
page.label.auto.generated=èªå¨çæ
page.label.autorefresh=èªå¨å·æ°
page.label.auxiliary.data.connections=è¾å©æ°æ®è¿æ¥
page.label.available=å¯ç¨
page.label.axis2.repository=AXIS2 å­å¨åº
page.label.back.to.insert.schedule=<< è¿åæå¥æ¶é´è¡¨
page.label.back.to.insert=<< è¿åæå¥
page.label.back.to.list=<< è¿ååè¡¨
page.label.back=è¿å
page.label.background.color=èæ¯é¢è²
page.label.background.images=èæ¯å¾å
page.label.background=èæ¯
page.label.basic.sso=åºæ¬ SSO
page.label.batch.report=æ¹æ¥è¡¨
page.label.batch=æ¹
page.label.between=ä»äº
page.label.bin.assignments=Bin åé
page.label.bin.no=Bin ç¼å·
page.label.bin.number=Bin ç¼å·
page.label.bin.properties=Bin å±æ§
page.label.bin.reservations=Bin ä¿ç
page.label.bold=ç²ä½
page.label.booleansymbolfalse=å¸å°ç¬¦å·(False)
page.label.booleansymboltrue=å¸å°ç¬¦å·(True)
page.label.border.color=è¾¹æ¡é¢è²
page.label.border.width=è¾¹æ¡å®½åº¦
page.label.border=è¾¹æ¡
page.label.bottom=åºé¨
page.label.box=æ¡
page.label.branch.actions=åæä½
page.label.branch.code=åä»£ç 
page.label.branch.domain=å
page.label.branch.edit=åç¼è¾
page.label.branch.finder=åæ¥æ¾ç¨åº
page.label.branch.settings=åè®¾ç½®
page.label.branch.ssotype=å SSO ç±»å
page.label.branch.type=åç±»å
page.label.branches=å
page.label.breakindicator=æ¢è¡æç¤ºç¬¦
page.label.browse=æµè§
page.label.bulk.upload=æ¹éä¸ä¼ 
page.label.bulleted.lists=é¡¹ç®ç¬¦å·åè¡¨
page.label.button.add.variant=æ·»å åé
page.label.by=ç±
page.label.canada=å æ¿å¤§
page.label.cancel.simulation=åæ¶æ¨¡æ
page.label.cancel.upload=åæ¶ä¸ä¼ 
page.label.cancel=åæ¶
page.label.category=ç±»å«
page.label.cell.padding=ååæ ¼è¾¹è·
page.label.cell.width=ååæ ¼å®½åº¦
page.label.cell=ååæ ¼
page.label.cells=ååæ ¼
page.label.center=ä¸­å¿
page.label.change.language=æ´æ¹è¯­è¨
page.label.change.touchpoint.context=æ´æ¹æ¥è§¦ç¹ä¸ä¸æ
page.label.change.user.workgroup=æ´æ¹ç¨æ·å·¥ä½ç»
page.label.change.variant=æ´æ¹åé
page.label.change=æ´æ¹
page.label.channel.configuration=æ´æ¹éç½®
page.label.channel=æ¸ é
page.label.chars=å­ç¬¦
page.label.check.type.check=å¾å·
page.label.check.type.circle=åå½¢
page.label.check.type.cross=åå­å½¢
page.label.check.type.diamond=è±å½¢
page.label.check.type.square=æ­£æ¹å½¢
page.label.check.type.star=æå½¢
page.label.checkbox=å¤éæ¡
page.label.class=ç±»
page.label.click.add.files=åå»âæ·»å æä»¶â...
page.label.clickatell.appid=åºç¨ç¨åº ID
page.label.clickatell.auth.api.url=AUTH API URL
page.label.clickatell.password=å¯ç 
page.label.clickatell.send.api.url=åé API URL
page.label.clickatell.senderid=åéè ID
page.label.clickatell.settings=Clickatell è®¾ç½®
page.label.clickatell.user=ç¨æ·
page.label.clone.message=åéæ¶æ¯
page.label.clone=åé
page.label.close=å³é­
page.label.cloud=äº
page.label.cms.host=CMS ä¸»æº
page.label.cms.image.asset.definition=CMS å¾åèµäº§å®ä¹
page.label.cms.image.category.definition=CMS å¾åç±»å«å®ä¹
page.label.cms.password=CMS å¯ç 
page.label.cms.settings=CMS è®¾ç½®
page.label.cms.sync=ä¸ CMS åæ­¥
page.label.cms.upload=CMS ä¸ä¼ 
page.label.cms.user=CMS ç¨æ·
page.label.code=ä»£ç 
page.label.cofirm.add.variant=ç¡®è®¤ - æ·»å åé
page.label.collection.setup=éåè®¾ç½®
page.label.collection=éå
page.label.color=é¢è²
page.label.comb=Comb
page.label.comment.history=æ³¨éåå²è®°å½
page.label.comment=æ³¨é
page.label.comments=æ³¨é
page.label.communication=éä¿¡
page.label.communications.management=è¿æ¥çç®¡ç
page.label.communications.setup=éä¿¡è®¾ç½®
page.label.communications=éä¿¡
page.label.company=å¬å¸
page.label.complete.assignments=å®æåé
page.label.complete.brackets=(å®æ)
page.label.complete=å®æ
page.label.composition.package=ç»åå
page.label.composition.packages.zero=ç»åå (0)
page.label.composition.packages=ç»åå
page.label.composition.results=ç»åç»æ
page.label.composition.version=ç»åçæ¬
page.label.compound.format.type.and=ä¸
page.label.compound.format.type.comma=éå·
page.label.compound.format.type.none=æ 
page.label.compound.format.type.or=æ
page.label.compound.key=å¤åé®
page.label.compound.variable.format=å¤ååéæ ¼å¼
page.label.condition=æ¡ä»¶
page.label.configurable.workflow.action.state.type.final.approval=æç»æ¹å
page.label.configurable.workflow.action.state.type.intermediate.approval=ä¸­é´æ¹å
page.label.configurable.workflow.action.state.type.none=æ 
page.label.configurable.workflow.action.state.type.release.for.approval=å¾æ¹åçæ¬
page.label.configurable.workflow.action.state.type.working.copy=å·¥ä½å¯æ¬
page.label.configurable.workflow.action.type.approved=å·²æ¹å
page.label.configurable.workflow.action.type.created=å·²åå»º
page.label.configurable.workflow.action.type.reassigned=å·²éæ°åé
page.label.configurable.workflow.action.type.rejected=å·²æç»
page.label.configure=éç½®
page.label.confirm.activate=ç¡®è®¤ - æ¿æ´»
page.label.confirm.add.communication=ç¡®è®¤ - æ·»å éä¿¡
page.label.confirm.add.external.event=ç¡®è®¤ - æ·»å å¤é¨äºä»¶
page.label.confirm.add.selection=ç¡®è®¤ - æ·»å éæ©
page.label.confirm.add.to.favorites=ç¡®è®¤ - æ·»å å°æ¶èå¤¹
page.label.confirm.add.touchpoint=ç¡®è®¤ - æ·»å æ¥è§¦ç¹
page.label.confirm.approve.reject=ç¡®è®¤ - æ¹å/æç»
page.label.confirm.archive=ç¡®è®¤ - å­æ¡£
page.label.confirm.cancel.simulation=ç¡®è®¤ - åæ¶æ¨¡æ
page.label.confirm.clone=ç¡®è®¤ - åé
page.label.confirm.complete.assignments=ç¡®è®¤ - å®æä½ä¸
page.label.confirm.copy.data=ç¡®è®¤ - å¤å¶æ°æ®
page.label.confirm.create.working.copy=ç¡®è®¤ - åå»ºå·¥ä½å¯æ¬
page.label.confirm.deactivate.user=ç¡®è®¤ - åç¨ç¨æ·
page.label.confirm.deactivate=ç¡®è®¤ - åç¨
page.label.confirm.default.language.change=ç¡®è®¤ - æ´æ¹é»è®¤è¯­è¨
page.label.confirm.delete.archive=ç¡®è®¤ - å é¤å­æ¡£
page.label.confirm.delete.attachment=ç¡®è®¤ - å é¤éä»¶
page.label.confirm.delete.composition.packages=ç¡®è®¤ - å é¤ç»åå
page.label.confirm.delete.constant=ç¡®è®¤ - å é¤å¸¸é
page.label.confirm.delete.external.event=ç¡®è®¤ - å é¤å¤é¨äºä»¶
page.label.confirm.delete.language=ç¡®è®¤ - å é¤è¯­è¨
page.label.confirm.delete.licence=ç¡®è®¤ - å é¤è®¸å¯è¯
page.label.confirm.delete.paragraph.style=ç¡®è®¤ - å é¤æ®µè½æ ·å¼
page.label.confirm.delete.pod=ç¡®è®¤ - å é¤ Pod
page.label.confirm.delete.report=ç¡®è®¤ - å é¤æ¥è¡¨æ¹æ¡
page.label.confirm.delete.simulation=ç¡®è®¤ - å é¤æ¨¡æ
page.label.confirm.delete.target.group=ç¡®è®¤ - å é¤ç®æ ç»
page.label.confirm.delete.targeting.rule=ç¡®è®¤ - å é¤ç®æ è§å
page.label.confirm.delete.test=ç¡®è®¤ - å é¤æµè¯æ¹æ¡
page.label.confirm.delete.variable=ç¡®è®¤ - å é¤åé
page.label.confirm.delete.variant=ç¡®è®¤ - å é¤åé
page.label.confirm.delete=ç¡®è®¤ - å é¤
page.label.confirm.discard.tag.type=ç¡®è®¤: æ¾å¼æ è®°ç±»å
page.label.confirm.discard.working.copy=ç¡®è®¤ - æ¾å¼å·¥ä½å¯æ¬
page.label.confirm.discard=ç¡®è®¤ - æ¾å¼
page.label.confirm.export.touchpoint=ç¡®è®¤ - å¯¼åºæ¥è§¦ç¹
page.label.confirm.export.variants=ç¡®è®¤ - å¯¼åºåé
page.label.confirm.generate.diagnostics.report=ç¡®è®¤ - çæâè¯æ­âæ¥å
page.label.confirm.generate.where.used=ç¡®è®¤ - çæâä½¿ç¨ä½ç½®âæ¥å
page.label.confirm.hold=ç¡®è®¤ - ä¿ç
page.label.confirm.migration=ç¡®è®¤ - è¿ç§»ç¨æ·åè®¾ç½®
page.label.confirm.modify.language=ç¡®è®¤ - ä¿®æ¹è¯­è¨
page.label.confirm.new.password=ç¡®è®¤æ°å¯ç 
page.label.confirm.off.line=ç¡®è®¤ - è±æºæå¼
page.label.confirm.on.line=ç¡®è®¤ - èæºæå¼
page.label.confirm.password.reset=ç¡®è®¤ - å¯ç éç½®
page.label.confirm.password=å½åå¯ç 
page.label.confirm.production.batch=ç¡®è®¤ - çäº§æ¹
page.label.confirm.proof=ç¡®è®¤ - ææ ·
page.label.confirm.reassign.to.user=ç¡®è®¤ - éæ°åéå°ç¨æ·
page.label.confirm.refresh.segmentation.analysis=ç¡®è®¤ - å·æ°åæ®µåæ
page.label.confirm.reinstate=ç¡®è®¤ - æ¢å¤
page.label.confirm.release.for.approval=ç¡®è®¤ - åå¸ä»¥ä¾æ¹å
page.label.confirm.release.for.use=ç¡®è®¤ - åå¸ä»¥ä¾ä½¿ç¨
page.label.confirm.release.to.tenants=ç¡®è®¤ - åå¸å°ç§æ·
page.label.confirm.remove.customization=ç¡®è®¤ - å é¤èªå®ä¹
page.label.confirm.remove.from.favorites=ç¡®è®¤ - ä»æ¶èå¤¹ä¸­å é¤
page.label.confirm.remove=ç¡®è®¤ - å é¤
page.label.confirm.rename.branch=ç¡®è®¤ - éå½åå
page.label.confirm.rename.selection=ç¡®è®¤ - éå½åéæ©
page.label.confirm.rename.variant=ç¡®è®¤ - éå½ååé
page.label.confirm.rename=ç¡®è®¤ - éå½å
page.label.confirm.reopen.tenant.window=ç¡®è®¤ - éæ°æå¼ç§æ·çªå£
page.label.confirm.reset.default.logo=ç¡®è®¤ - éæ°è®¾ç½®å¾½æ 
page.label.confirm.restore=ç¡®è®¤ - è¿å
page.label.confirm.scan.domains=ç¡®è®¤ - æ«æå
page.label.confirm.suppress=ç¡®è®¤ - åæ¶
page.label.confirm.unhold=ç¡®è®¤ - åæ¶ä¿ç
page.label.confirm.upload.template=ç¡®è®¤ - ä¸ä¼ æ¨¡æ¿
page.label.confirm.validate.and.initialize=ç¡®è®¤ - éªè¯ååå§å
page.label.confirm=ç¡®è®¤
page.label.confirmation.required=éè¦ç¡®è®¤
page.label.confirmpassword=ç¡®è®¤å¯ç 
page.label.connected.enabled=è¿æ¥å¯ç¨
page.label.connected.management=è¿æ¥çç®¡ç
page.label.connected=å·²è¿æ¥
page.label.connections=è¿æ¥
page.label.connector.name=è¿æ¥å¨åç§°
page.label.connector.parameter=è¿æ¥å¨åæ°
page.label.connector.string=è¿æ¥å¨å­ç¬¦ä¸²
page.label.connector=è¿æ¥å¨
page.label.connectors.licensed=å·²ææçè¿æ¥å¨
page.label.connectors=è¿æ¥å¨
page.label.consequences=ç»æ
page.label.contact.name=èç³»äººå§å
page.label.content.defaults=é»è®¤åå®¹
page.label.content.file=åå®¹æä»¶
page.label.content.last.edited=ä¸æ¬¡ç¼è¾çåå®¹
page.label.content.library.delivery.type.dependant=ä»å±
page.label.content.library.delivery.type.independent=ç¬ç«
page.label.content.library.setup=å¾ååºè®¾ç½®
page.label.content.library=å¾ååº
page.label.content.name=åå®¹åç§°
page.label.content.search=åå®¹æç´¢
page.label.content.selections=åå®¹åé
page.label.content.type=åå®¹ç±»å
page.label.content=åå®¹
page.label.contentapproval=åå®¹æ¹å
page.label.continue=ç»§ç»­
page.label.contract.all=æ¶ç¼©å¨é¨
page.label.contract.email=æ¶ç¼©çµå­é®ä»¶
page.label.controlclick=æ§å¶ + åå»ä»¥åæ¶éæ©
page.label.consolidate.automatically.content.settings=Content consolidation settings
page.label.consolidate.automatically.consolidate.all=Automatically consolidate all matching content during import?
page.label.consolidate.automatically.prefix.description=- include a 1 to 8 character prefix like 'MP' or 'RAT'
page.label.consolidate.automatically.minWordCount.prefix=Don't consolidate content with fewer than
page.label.consolidate.automatically.minWordCount.suffix=words
page.label.consolidate.automatically.content.characters.description= - include the first 80 characters of shared content
page.text.consolidate.automatically.description=Please choose how any new shared content will be named. You may combine both naming options. At least one naming option must be chosen.
page.text.consolidate.automatically.notes=Note that automatically consolidating all exact matches is an action that cannot be undone. Content already uploaded into this Rationalizer application will be consolidated with newly uploaded content. If shared content object names are identical, Rationalizer will add a number to ensure uniqueness as in 'RAT 1', 'RAT 2', etc.
client_messages.consolidate.automatically.sharedContent.naming.error.title=Shared Content Naming Error
client_messages.consolidate.automatically.sharedContent.naming.error.message=You must enable one or both of the shared content naming options in order to generate unique shared content names. Turning both options on will generate the most descriptive, searchable shared content names.
client_messages.consolidate.automatically.minWordCount.error=The entered number of words must be greater than 0, or may be left blank.
page.label.convert=è½¬æ¢
page.label.copy.content.to=å¤å¶åå®¹å°...
page.label.copy.data.from=å¤å¶æ°æ®èª...
page.label.corporation=å¬å¸
page.label.count.eight=å«
page.label.count.five=äº
page.label.count.four=å
page.label.count.nine=ä¹
page.label.count.one=ä¸
page.label.count.seven=ä¸
page.label.count.six=å­
page.label.count.ten=å
page.label.count.three=ä¸
page.label.count.two=äº
page.label.coverage.analysis=è¦ççåæ
page.label.create.new.branch=æ°å»ºå
page.label.create.working.copy=åå»ºå·¥ä½å¯æ¬
page.label.create=åå»º
page.label.created.by=åå»ºè
page.label.created.date=åå»ºæ¥æ
page.label.created.on=åå»ºäº
page.label.created=å·²åå»º
page.label.createnew=æ°æ·»å 
page.label.currencysymbol=è´§å¸ç¬¦å·
page.label.current.element.name=å½ååç´ åç§°
page.label.current.status=å½åç¶æ
page.label.current.view.global=å½åè§å¾: å¨å±
page.label.current.view.touchpoint=å½åè§å¾: æ¥è§¦ç¹
page.label.currently.varied.by=å½åååä¾æ®
page.label.custom.content=èªå®ä¹åå®¹
page.label.custom=èªå®ä¹
page.label.customer.driver.input.filename=æ¶ä»¶äººé©±å¨ç¨åºè¾å¥æä»¶å
page.label.customer.email.address.variable=æ¶ä»¶äººçµå­é®ä»¶å°ååé
page.label.customer.filename=æ¶ä»¶äººæä»¶å
page.label.customer.input.files.encoding=æ¶ä»¶äººè¾å¥æä»¶ç¼ç 
page.label.customer.key.variable=æ¶ä»¶äººå¯ä¸é®åé
page.label.customer.output.files.encoding=æ¶ä»¶äººè¾åºæä»¶ç¼ç 
page.label.customer.phone.number.variable=å®¢æ·çµè¯å·ç åé
page.label.customer.primary.key=å®¢æ·ä¸»é®
page.label.customer.report.details=å®¢æ·æ¥è¡¨è¯¦ç»ä¿¡æ¯
page.label.customer.report.summary=å®¢æ·æ¥è¡¨æè¦
page.label.customer=å®¢æ·
page.label.customerId=å®¢æ· ID
page.label.customerfield.first=ç¬¬ä¸ä¸ªå®¢æ·å­æ®µ
page.label.customerfield.second=ç¬¬äºä¸ªå®¢æ·å­æ®µ
page.label.customers=å®¢æ·
page.label.customizations=èªå®ä¹
page.label.customize=èªå®ä¹
page.label.dark=æ·±è²çº¿
page.label.dashed=èçº¿
page.label.data.collection.configuration=æ°æ®ééç½®
page.label.data.element=æ°æ®åç´ 
page.label.data.file=æ°æ®æä»¶
page.label.data.files=æ°æ®æä»¶
page.label.data.group=æ°æ®ç»
page.label.data.groups=æ°æ®ç»
page.label.data.resource=æ°æ®èµæº
page.label.data.resources=æ°æ®èµæº
page.label.data.source.association.name=æ°æ®éåç§°
page.label.data.source.association=æ°æ®éå
page.label.data.source.associations=æ°æ®éå
page.label.data.source.name=æ°æ®æºåç§°
page.label.data.source=æ°æ®æº
page.label.data=æ°æ®
page.label.dataelement=æ°æ®åç´ 
page.label.dataelements=æ°æ®åç´ 
page.label.datarecords=æ°æ®è®°å½
page.label.datasources=æ°æ®æº
page.label.datatype=æ°æ®ç±»å
page.label.date.and.time=æ¥æåæ¶é´
page.label.date.range=æ¥æèå´
page.label.date.selection=æ¥æéæ©
page.label.date=æ¥æ
page.label.days=å¤©
page.label.daysofweek=ææå 
page.label.de.server.settings=DE (å³ç­å¼æ)è®¾ç½®
page.label.deactivate=åç¨
page.label.deactive= éæ´»å¨
page.label.debug.mode=åææ¨¡å¼
page.label.debug=è°è¯
page.label.decimal.places=å°æ°ä½æ°
page.label.decimalsymbol=å°æ°ç¹
page.label.decisioning.engine.version=å³ç­å¼æçæ¬
page.label.default.content=é»è®¤åå®¹
page.label.default.instance=é»è®¤å®ä¾
page.label.default.language=é»è®¤è¯­è¨
page.label.default.locale.for.language=è¯­è¨çé»è®¤åºåè®¾ç½®
page.label.default.locale.note=(é»è®¤åºåè®¾ç½®)
page.label.default.locale=é»è®¤åºåè®¾ç½®
page.label.default.month.name.override=é»è®¤çæä»½åç§°æ¿ä»£
page.label.default.paragraph.style=é»è®¤æ®µè½æ ·å¼
page.label.default.rate.sheet=é»è®¤è¯ä»·è¡¨
page.label.default.schedule.layout=é»è®¤æ¶é´è¡¨å¸å±
page.label.default.text.style=é»è®¤ææ¬æ ·å¼
page.label.default.thresholds=é»è®¤éå¼
page.label.default.value=é»è®¤å¼
page.label.default.workspace=é»è®¤å·¥ä½åº
page.label.default=é»è®¤
page.label.defaultdateformat=é»è®¤æ¥ææ ¼å¼
page.label.defaultvalue=é»è®¤å¼
page.label.defaultworkgroup=é»è®¤å·¥ä½ç»
page.label.delete.DEL=DEL
page.label.delete.archive=å é¤å­æ¡£
page.label.delete.attachment=å é¤éä»¶
page.label.delete.constant=å é¤å¸¸é
page.label.delete.event=å é¤äºä»¶
page.label.delete.language=å é¤è¯­è¨
page.label.delete.parameter=å é¤åå®¹éæ©å¨
page.label.delete.selected.target.group=æ¯å¦ç¡®å®è¦å é¤æéçç®æ ç»?
page.label.delete.selected=å é¤éå®
page.label.delete.selection=å é¤éæ©
page.label.delete.simulation=å é¤æ¨¡æ
page.label.delete.test.scenario=å é¤æµè¯
page.label.delete.touchpoints=ç¡®è®¤ - å é¤æ¥è§¦ç¹
page.label.delete.variant=å é¤åé
page.label.delete.workgroup=å é¤å·¥ä½ç»
page.label.delete=å é¤
page.label.delimiter=åéç¬¦
page.label.delivery.date=ä¼ éæ¥æ
page.label.delivery.logs=ä¼ éæ¥å¿
page.label.delivery.report=ä¼ éæ¥å
page.label.delivery.status=ä¼ éç¶æ
page.label.delivery.type=ä¼ éç±»å
page.label.delivery=è®¾ç½®ä¼åçº§
page.label.deliveryevents=ä¼ éäºä»¶
page.label.description=æè¿°
page.label.detailed=è¯¦ç»
page.label.details=è¯¦ç»ä¿¡æ¯
page.label.diagnostics.package=è¯æ­å
page.label.dialogue.output=HP Exstream è¾åº
page.label.dictionary=å­å¸
page.label.disable.deactivate=ç¦ç¨(åç¨)
page.label.disable.offline=ç¦ç¨(è±æº)
page.label.disable=ç¦ç¨
page.label.disabled=å·²ç¦ç¨
page.label.discard.rate.scheet=æ¾å¼è¯ä»·è¡¨
page.label.discard.working.copy=æ¾å¼å·¥ä½å¯æ¬
page.label.discard=æ¾å¼
page.label.discontinue.rate.sheet=ç»æ­¢è¯ä»·è¡¨
page.label.discontinue=ç»æ­¢
page.label.dispatch.settings=è°åº¦è®¾ç½®
page.label.display.settings=æ¾ç¤ºè®¾ç½®
page.label.display=æ¾ç¤º
page.label.disqualified=ä¸åæ ¼
page.label.distinct.customers=ä¸åå®¢æ·
page.label.divider=åéç¬¦
page.label.division=åé
page.label.document.insert.selector=æå¥éæ©å¨
page.label.document.touchpoint.selections.selector=æ¥è§¦ç¹åééæ©å¨
page.label.document=ææ¡£
page.label.domain=å
page.label.domains=å
page.label.done=å®æ
page.label.dotted=ç¹çº¿
page.label.download.exported.XML=ä¸è½½å¯¼åºç XML
page.label.download.message.template=ä¸è½½æ¶æ¯æ¨¡æ¿
page.label.download.variants=ä¸è½½åé
page.label.download=ä¸è½½
page.label.due.by=æªæ­¢å°
page.label.due.date=å°ææ¥æ
page.label.due=å°æ
page.label.dxf.output=DXF è¾åº
page.label.dynamic=å¨æ
page.label.edit.branch.settings=ç¼è¾åè®¾ç½®
page.label.edit.constant=ç¼è¾å¸¸é
page.label.edit.data.element=ç¼è¾æ°æ®åç´ 
page.label.edit.data.record=ç¼è¾æ°æ®è®°å½
page.label.edit.data.source=ç¼è¾æ°æ®æº
page.label.edit.target.group=ç¼è¾ç®æ ç»
page.label.edit.xml.element.attribute.name=å±æ§åç§°
page.label.edit.xml.element.name=åç´ åç§°
page.label.edit.xml.element.value=åç´ å¼
page.label.edit.xml.element=ç¼è¾ XML æ°æ®åç´ 
page.label.edit=ç¼è¾
page.label.edited.by=ç¼è¾è
page.label.edited=å·²ç¼è¾
page.label.editworkgroup=ç¼è¾å·¥ä½ç»
page.label.element=åç´ 
page.label.elements.disk=åç´ (ç£ç)
page.label.elements.memory=åç´ (åå­)
page.label.email.server.settings=çµå­é®ä»¶æå¡å¨è®¾ç½®
page.label.email.templates=çµå­é®ä»¶æ¨¡æ¿
page.label.email.web=Web çµå­é®ä»¶
page.label.email=çµå­é®ä»¶
page.label.embedded.content.delivery.type.dependant=ä»å±
page.label.embedded.content.delivery.type.independent=ç¬ç«
page.label.embedded.content.setup=æºè½ææ¬è®¾ç½®
page.label.embedded.content=æºè½ææ¬
page.label.en=è±è¯­
page.label.enable.activate=å¯ç¨(æ¿æ´»)
page.label.enable.for.content=éå¯¹åå®¹å¯ç¨
page.label.enable.for.rules=éå¯¹è§åå¯ç¨
page.label.enable.online=å¯ç¨(èæº)
page.label.enable=å¯ç¨
page.label.enabled=å·²å¯ç¨
page.label.encoding.type=ç¼ç ç±»å
page.label.end.date=ç»ææ¥æ
page.label.end=ç»æ
page.label.envelope.name=ä¿¡å°åç§°
page.label.envelope.weight=ä¿¡å°éé
page.label.envelope=ä¿¡å°
page.label.error=éè¯¯
page.label.es=è¥¿ç­çè¯­
page.label.evaluations=è¯ä¼°
page.label.evelope.weight=ä¿¡å°(éé)
page.label.event=äºä»¶
page.label.exact.match=ç²¾ç¡®å¹é
page.label.exact.target.ant.path=Ant å¯æ§è¡æä»¶ä½ç½®
page.label.exact.target.build.xml.path=çæ XML æä»¶ä½ç½®
page.label.exact.target.ftp.host=FTP ä¸»æº
page.label.exact.target.ftp.import.location=FTP å¯¼å¥ä½ç½®
page.label.exact.target.ftp.password=FTP å¯ç 
page.label.exact.target.ftp.user=FTP ç¨æ·
page.label.exact.target.settings=åç¡®ç®æ è®¾ç½®
page.label.exact.target.web.service.file.location=Web æå¡æä»¶ä½ç½®
page.label.exact.target.web.service.password=Web æå¡å¯ç 
page.label.exact.target.web.service.user=Web æå¡ç¨æ·
page.label.exact.target.wsdl.location=WSDL ä½ç½®
page.label.exclude.customers=æé¤å®¢æ·
page.label.exclude=æé¤
page.label.execute.incloud.preview=å¨äºé¢è§ä¸­æ§è¡
page.label.execute.incloud.proof=å¨äºææ ·ä¸­æ§è¡
page.label.execute.incloud.test=å¨äºæµè¯ä¸­æ§è¡
page.label.execute.test=æ§è¡æµè¯
page.label.expand.all=æ©å±å¨é¨
page.label.export.CSV.data=å¯¼åº CSV æ°æ®
page.label.export.report=å¯¼åºæ¥å
page.label.export.touchpoint=å¯¼åºæ¥è§¦ç¹
page.label.export.variants=å¯¼åºåé
page.label.export=å¯¼åº
page.label.external.domain=å¤é¨å
page.label.external.event.key=äºä»¶é®
page.label.external.event.none=ç³»ç»ä¸­æ å¤é¨äºä»¶
page.label.external.event=å¤é¨äºä»¶
page.label.external.events=å¤é¨äºä»¶
page.label.external.reporting.data=å¤é¨æ¥è¡¨æ°æ®
page.label.external.sso=å¤é¨ SSO
page.label.externalID=å¤é¨ ID
page.label.externalid=å¤é¨ ID
page.label.failed=å¤±è´¥
page.label.false=éè¯¯
page.label.favourite=æ¶èå¤¹
page.label.feature.activation=åè½æ¿æ´»
page.label.features=åè½
page.label.file.name=æä»¶åç§°
page.label.file=æä»¶
page.label.filter.customers=ç­éå®¢æ·
page.label.filter=ç­é
page.label.find=æ¥æ¾
page.label.first.page.weight=ç¬¬ä¸é¡µæé
page.label.firstname=åå­
page.label.fiscal.year.start.month=è´¢æ¿å¹´åº¦èµ·å§æ
page.label.fivesecs=5 ç§
page.label.fixed.record.count=åºå®è®°å½æ°
page.label.folder.favorites=æ¶èå¤¹
page.label.folder.last.visited=ä¸æ¬¡è®¿é®æ¶é´
page.label.folder.locations=æä»¶å¤¹ä½ç½®
page.label.folder.overview=æä»¶å¤¹æ¦è¿°
page.label.font.name=å­ä½åç§°
page.label.font.size=å­ä½å¤§å°
page.label.font=å­ä½
page.label.forget.password.pending=å¿è®°å¯ç ç­å¾
page.label.forms=è¡¨å
page.label.fr=æ³è¯­
page.label.freeform=èªç±æ ¼å¼
page.label.frequency=é¢ç
page.label.friendly.name=åå¥½åç§°
page.label.friendlyname=åå¥½åç§°
page.label.from.date=èµ·å§æ¥æ
page.label.from.path=ä»è·¯å¾
page.label.from=ä»
page.label.ftp.location=FTP ä¸ä¼ ä½ç½®/è·¯å¾
page.label.ftp.server=FTP æå¡å¨
page.label.ftp1.settings=Ftp 1 è®¾ç½®
page.label.ftp2.settings=Ftp 2 è®¾ç½®
page.label.ftp3.settings=Ftp 3 è®¾ç½®
page.label.full.job.logs=å®æ´ä½ä¸æ¥å¿
page.label.full=å®æ´
page.label.general.settings=å¸¸è§è®¾ç½®
page.label.general.specification=ä¸è¬è§æ ¼
page.label.general=ä¸è¬
page.label.generalerror=å¸¸è§éè¯¯
page.label.generate.key=çæå¯é¥
page.label.generate.message.report=çææ¶æ¯æ¥å
page.label.generate.report=çææ¥å
page.label.generate.reports=è¿è¡
page.label.generate.touchpoint.report=çææ¥è§¦ç¹æ¥å
page.label.generated.by=çæè
page.label.generated=å·²çæ
page.label.generic=éç¨
page.label.global.all.touchpoints=å¨å±: æææ¥è§¦ç¹
page.label.global.folders=å¨å±æä»¶å¤¹
page.label.global=å¨å±
page.label.graphic=å¾è¡¨
page.label.group.level=ç»çº§å«
page.label.group=ç»
page.label.guid=å¯ä¸æ è¯ç¬¦ (GUID)
page.label.hanging=æ¬æç
page.label.header.description=æ é¢æè¿°
page.label.header.logo.filename=æ é¢æ å¿æä»¶åç§°
page.label.header.logo.filepath=æ é¢æ å¿å®æ´æä»¶è·¯å¾ååç§°
page.label.header.logo=æ é¢æ å¿
page.label.header.records=æ é¢è®°å½
page.label.header.settings=é¡µçè®¾ç½®
page.label.header.theme.type.black=æ·±è²çº¿
page.label.header.theme.type.white=æµ
page.label.header.theme.type=æ å¿èæ¯
page.label.health.check=ç¶åµæ£æ¥
page.label.health.checks=ç¶åµæ£æ¥
page.label.height=é«åº¦
page.label.hibernatecachemonitor=ä¼ç ç¼å­çè§å¨
page.label.hidden.user=éèçç¨æ·
page.label.hierarchy=å±çº§
page.label.history=åå²è®°å½
page.label.hits=ç¹å»
page.label.hold=ä¿ç
page.label.hour=å°æ¶
page.label.id=ID
page.label.image=å¾å
page.label.import.complete=å¯¼å¥å®æ
page.label.import.dot=å¯¼å¥...
page.label.import.lookup.values=å¯¼å¥æ¥æ¾å¼
page.label.import.only.data.collection=ä»å¯¼å¥æ°æ®éå
page.label.import=å¯¼å¥
page.label.importing.image=å¯¼å¥å¾å
page.label.in.process.items.included=åæ¬å·¥ä½å¯æ¬
page.label.in.process.items.to.include=è¦åæ¬çå·¥ä½å¯æ¬
page.label.in.process.message.details=å·¥ä½å¯æ¬æ¶æ¯æ¥åè¯¦ç»ä¿¡æ¯
page.label.in.process=å·¥ä½å¯æ¬
page.label.in=è±å¯¸
page.label.inactive.tag=éæ´»å¨æ è®°
page.label.inactive=éæ´»å¨
page.label.inches=è±å¯¸
page.label.include.content=åæ¬åå®¹
page.label.include.groups=åæ¬ç»
page.label.include.headers=åæ¬æ é¢
page.label.include.insert.targeting=åæ¬æå¥ç®æ æå®
page.label.include.targeting=åæ¬ç®æ æå®
page.label.include=åæ¬
page.label.incomplete=ä¸å®æ´
page.label.indefinite=ä¸ç¡®å®
page.label.indentation=ç¼©è¿
page.label.indicator.location=æç¤ºç¬¦ä½ç½®
page.label.indicator=æç¤ºç¬¦
page.label.info=ä¿¡æ¯
page.label.inherited=å·²ç»§æ¿
page.label.input.filter=è¾å¥ç­éå¨
page.label.insert.ID=ID
page.label.insert.as.paragraph=ä½ä¸ºæ®µè½æå¥
page.label.insert.assignment=æå¥ä»»å¡
page.label.insert.associations=æå¥å³è
page.label.insert.deleted=(æå¥å·²å é¤)
page.label.insert.delivery.details.customers=æå¥ä¼ éè¯¦ç»ä¿¡æ¯ - æå®¢æ· ID æåº
page.label.insert.delivery.ordered.customer=æå¥ä¼ éè¯¦ç»ä¿¡æ¯ - æå®¢æ· ID æåº
page.label.insert.delivery.summary=æå¥ä¼ éæ¥å
page.label.insert.delivery.summery.customers=æå¥ä¼ éæè¦ - æå®¢æ·
page.label.insert.delivery.type.mandatory=å¿é
page.label.insert.delivery.type.non.selectable=ä¸å¯é
page.label.insert.delivery.type.optional=å¯é
page.label.insert.end.date=æå¥ç»ææ¥æ
page.label.insert.management=æå¥ç®¡ç
page.label.insert.message=æå¥æ¶æ¯
page.label.insert.name=æå¥åç§°
page.label.insert.recipients=æå¥æ¶ä»¶äºº
page.label.insert.report.details=æå¥æ¥åè¯¦ç»ä¿¡æ¯
page.label.insert.report.summary=æå¥æ¥åæè¦
page.label.insert.report=æå¥æ¥åè¯¦ç»ä¿¡æ¯
page.label.insert.schedule.setup=æå¥æ¶é´è¡¨è®¾ç½®
page.label.insert.schedule=æå¥æ¶é´è¡¨
page.label.insert.schedules=æå¥æ¶é´è¡¨
page.label.insert.search=æç´¢
page.label.insert.settings=æå¥è®¾ç½®
page.label.insert.start.date=æå¥å¼å§æ¥æ
page.label.insert.timing=æå¥è®¡æ¶
page.label.insert=æå¥
page.label.inserts.enabled=å·²å¯ç¨æå¥
page.label.inserts=æå¥
page.label.instance=å®ä¾
page.label.instances=å®ä¾
page.label.instructions=è¯´æ
page.label.interactive.zone=è¿æ¥çåºå
page.label.interactive=å·²è¿æ¥
page.label.internal.pod.db.ip=åé¨ Pod DB IP
page.label.internal.umh.ip=åé¨ UMH IP
page.label.invalid.proofing.data=æ æçæ ¡å¯¹æ°æ®
page.label.invalidsigninattemps=æ æç»å½å°è¯
page.label.is.master.admin=ä¸ºä¸»ç®¡çå
page.label.is.master.user=ä¸ºä¸»ç¨æ·
page.label.italic=æä½
page.label.item=é¡¹ç®
page.label.items.included=åå«çé¡¹ç®
page.label.items.to.include.in.reports=è¦åå«å¨æ¥åä¸­çé¡¹ç®
page.label.items=é¡¹ç®
page.label.job.folders=ä½ä¸æä»¶å¤¹
page.label.job=ä½ä¸
page.label.key.end_date=END_DATE
page.label.key.end_date_time=END_DATE_TIME
page.label.key.end_time=END_TIME
page.label.key.start_date=START_DATE
page.label.key.start_date_time=START_DATE_TIME
page.label.key.start_time=START_TIME
page.label.key.time_delta=TIME_DELTA
page.label.key.time_zone=TIME_ZONE
page.label.keywords=å³é®å­
page.label.label=æ ç­¾
page.label.landscape=æ¨ªå (11 x 8.5)
page.label.language.code=è¯­è¨ä»£ç 
page.label.language.management=è¯­è¨ç®¡ç
page.label.language.selections=è¯­è¨éæ©
page.label.language=è¯­è¨
page.label.languages=è¯­è¨
page.label.last.date.ran=ä¸æ¬¡è¿è¡æ¥æ
page.label.last.edited.by=ä¸æ¬¡ç¼è¾è
page.label.last.edited=ä¸æ¬¡ç¼è¾æ¶é´
page.label.last.modified.by=ä¸æ¬¡ä¿®æ¹è
page.label.last.modified=ä¸æ¬¡ä¿®æ¹æ¶é´
page.label.last.preview=ä¸æ¬¡é¢è§æ¶é´
page.label.last.proof=ä¸æ¬¡ææ ·
page.label.last=æåä¸ä¸ª
page.label.lastname=å§æ°
page.label.latest.preview=ææ°é¢è§
page.label.latest.proof=ææ°ææ ·
page.label.layout.manager=å¸å±ç®¡çå¨
page.label.layout=å¸å±
page.label.layouttype=å¸å±ç±»å
page.label.leave.empty=ä¿çä¸ºç©º
page.label.left=å·¦
page.label.legal_landscape=Legal æ¨ªå (14 x 8.5)
page.label.legal_portrait=Legal çºµå (8.5 x 14)
page.label.length=é¿åº¦
page.label.level=æ°´å¹³
page.label.licence.information=è®¸å¯è¯ä¿¡æ¯
page.label.licence.management=è®¸å¯è¯ç®¡ç
page.label.licence.manager=è®¸å¯è¯ç®¡çå
page.label.licence=è®¸å¯è¯
page.label.licences=è®¸å¯è¯
page.label.license.batch_dialogue=HP Exstream
page.label.license.batch_gmc=GMC Inspire
page.label.license.clickatell=Clickatell
page.label.license.emessaging_email=çµå­æ¶æ¯ççµå­é®ä»¶
page.label.license.emessaging_sms=çµå­æ¶æ¯ç SMS
page.label.license.exacttarget=åç¡®ç®æ 
page.label.license.ftpweb=FTP Web
page.label.license.information=è®¸å¯è¯ä¿¡æ¯
page.label.license.insertmanagement_support=æå¥ç®¡çæ¯æ
page.label.license.msg_mgmt_workflow=æ¶æ¯ç®¡çåå·¥ä½æµ(æ ¸å¿)
page.label.license.msg_reporting_analytics=æ¶æ¯æ¥åååæ(æ ¸å¿)
page.label.license.native=æ¬æºç»å
page.label.license.provisions=è®¸å¯æ¡æ¬¾
page.label.license.sendmail=Sendmail
page.label.license.simulation_support=æ¨¡ææ¯æ
page.label.license.tm=TM
page.label.license.workgroup_support=å·¥ä½ç»æ¯æ(æ ¸å¿)
page.label.light=æµ
page.label.limit.available=æå¤§å¯ç¨æ°
page.label.limit.global.description=* âé¶âæå³çå¯ä½¿ç¨âæå¤§å¯ç¨æ°âçè®¸å¯è¯ï¼æ­¤æ°å¼å¯éæ¶æ´æ¹  
page.label.limit.global=ä¿çå¯é*
page.label.limit.local=æ¬å°å
page.label.limit.subdomains=å­å
page.label.line.spacing=è¡è·
page.label.link=é¾æ¥
page.label.list.filter.my.working.copies=æçå·¥ä½å¯æ¬
page.label.list.filter.type.active=æ´»å¨
page.label.list.filter.type.all=ææ
page.label.list.filter.type.any.status=ä»»ä½ç¶æ
page.label.list.filter.type.any=ä»»ä½
page.label.list.filter.type.archived=å·²å­æ¡£
page.label.list.filter.type.batch=æ¹
page.label.list.filter.type.changed=å·²æ´æ¹
page.label.list.filter.type.complete=å®æ
page.label.list.filter.type.custom=èªå®ä¹
page.label.list.filter.type.error=éè¯¯
page.label.list.filter.type.global=å¨å±
page.label.list.filter.type.inactive=éæ´»å¨
page.label.list.filter.type.interactive=å·²è¿æ¥
page.label.list.filter.type.message.delivery=æ¶æ¯ä¼ é
page.label.list.filter.type.message=æ¶æ¯
page.label.list.filter.type.my.active=æçæ´»å¨é¡¹
page.label.list.filter.type.my.inactive=æçä¸æ´»å¨é¡¹
page.label.list.filter.type.my=æç
page.label.list.filter.type.referenced=ä»éå¼ç¨é¡¹
page.label.list.filter.type.sap=å·²ç»§æ¿
page.label.list.filter.type.setup=è®¾ç½®
page.label.list.filter.type.suppress=ç¦æ­¢
page.label.list.filter.type.touchpoint.delivery=æ¥è§¦ç¹ä¼ é
page.label.list.filter.type.unchanged=æªæ´æ¹
page.label.list.filter.type.unreferenced=ä»éæªå¼ç¨é¡¹
page.label.list.filter.type.variant=åé
page.label.list.filter.type.working.copy=å·¥ä½å¯æ¬
page.label.list=åè¡¨
page.label.local.number.plus.subdomains=æ¬å°åæ°éå ä¸å­åæ°é 
page.label.local.number=æ¬å°åæ°é
page.label.local.productions.number=æ¬å°åçäº§æ°é 
page.label.local=æ¬å°
page.label.locale.settings=åºåè®¾ç½®
page.label.locale=åºå
page.label.location=ä½ç½®/è·¯å¾
page.label.lock.out.after=å¨æ­¤ä¹åéå®
page.label.log.message=æ¥å¿æ¶æ¯
page.label.login.theme=ç»å½ä¸»é¢
page.label.logs=æ¥å¿
page.label.long.month.name=é¿æä»½åç§°
page.label.long.month.names=é¿æä»½åç§°
page.label.mailing.weight=é®å¯éé
page.label.main.folders=ä¸»æä»¶å¤¹
page.label.maintenance.edit=ç¼è¾ç»´æ¤è®¾ç½®
page.label.maintenance.settings=ç»´æ¤è®¾ç½®
page.label.maintenance.view=æ¥çç»´æ¤è®¾ç½®
page.label.maintenance=ç»´æ¤
page.label.make.default=ä½¿ä¹æä¸ºé»è®¤å¼
page.label.mandatory=å¿é
page.label.manual=æå¨
page.label.map.domains=æ å°å
page.label.map.to.data.source=æ å°å°æ°æ®æº
page.label.map.variables=æ å°åé
page.label.mask=æ©ç 
page.label.master.admin.branch=ä¸»ç®¡çå: å
page.label.master.admin.licences=ä¸»ç®¡çå: è®¸å¯è¯
page.label.master.admin.maintenance=ä¸»ç®¡çå: ç»´æ¤
page.label.master.admin.system.settings=ä¸»ç®¡çå: ç³»ç»è®¾ç½®
page.label.master.admin.users=ç®¡çå¤§å¸: ç¨æ·
page.label.master.bin.layout=ä¸» Bin å¸å±
page.label.master.schema.name=ä¸»æ¶æåç§°
page.label.master.unified.login=ä¸»ç®¡çå: ç»ä¸ç»å½
page.label.master=ä¸»
page.label.max.length=æå¤§é¿åº¦
page.label.maximum.available.number=æå¤§å¯ç¨æ°é
page.label.maximum.length=æå¤§é¿åº¦
page.label.maximum=æå¤§å¼
page.label.message.associations=æ¶æ¯å³è
page.label.message.content=æ¶æ¯åå®¹
page.label.message.deleted=(æ¶æ¯å·²å é¤)
page.label.message.delivery.ordered.customer=æ¶æ¯ä¼ éè¯¦ç»ä¿¡æ¯ - æå®¢æ· ID æåº
page.label.message.delivery.report=æ¶æ¯ä¼ éæ¥å
page.label.message.delivery.summary.customer.id=æå¥ä¼ éæè¦ - éå¯¹å®¢æ· ID
page.label.message.delivery.summary.customers=æå¥ä¼ éæè¦ - æå®¢æ·
page.label.message.filter.all=ææ
page.label.message.filter.archived"=ææå·²å­æ¡£æ¶æ¯
page.label.message.filter.archived=å·²å­æ¡£
page.label.message.filter.my.working.copies=æçå·¥ä½å¯æ¬
page.label.message.filter.working.copies=å·¥ä½å¯æ¬
page.label.message.formatting.defaults=æ¶æ¯æ ¼å¼é»è®¤å¼
page.label.message.formatting.overwrite.defaults=æ¶æ¯æ ¼å¼è¦çå¼
page.label.message.name=æ¶æ¯åç§°
page.label.message.priority=æ¶æ¯ä¼åçº§
page.label.message.report.details=æ¶æ¯æ¥åè¯¦ç»ä¿¡æ¯
page.label.message.report.summary=æ¶æ¯æ¥åæè¦
page.label.message.report.tool=æ¶æ¯æ¥åå·¥å·
page.label.message.targeting.analysis=æ¶æ¯ç®æ æå®åæ
page.label.message.targeting=æ¶æ¯ç®æ æå®
page.label.message=æ¶æ¯
page.label.messagepoint.application.error=Messagepoint: åºç¨ç¨åºéè¯¯
page.label.messagepoint.hp=HP Exstream Messagepoint
page.label.messagepoint=Messagepoint
page.label.messages=æ¶æ¯
page.label.metatags=åæ è®°
page.label.migrate.settings=è¿ç§»è®¾ç½®
page.label.minimum.length=æå°é¿åº¦
page.label.misses=ç¼ºå¤±
page.label.mixed=æ··å
page.label.modified=å·²ä¿®æ¹
page.label.modify.language=ä¿®æ¹è¯­è¨
page.label.modify.selector.values=ä¿®æ¹éæ©å¨å¼
page.label.modules.licensed=å·²ææçæ¨¡å
page.label.month.april=åæ
page.label.month.august=å«æ
page.label.month.december=åäºæ
page.label.month.february=äºæ
page.label.month.january=ä¸æ
page.label.month.july=ä¸æ
page.label.month.june=å­æ
page.label.month.march=ä¸æ
page.label.month.may=äºæ
page.label.month.november=åä¸æ
page.label.month.october=åæ
page.label.month.september=ä¹æ
page.label.month=æ
page.label.more=æ´å¤
page.label.move.from.zone=ç§»èªåºå
page.label.move.messages.to=å°æ¶æ¯ç§»å°...
page.label.move.to.zone=ç§»è³åºå
page.label.move=ç§»å¨
page.label.multipart.content.category=å±äº«åå®¹ç±»å«
page.label.multipart.content=å±äº«åå®¹
page.label.multipart.message.content=å¤é¨åæ¶æ¯åå®¹
page.label.multipart=å¤é¨å
page.label.multiple=å¤
page.label.my.shared.content=æçå±äº«åå®¹
page.label.my.tasks.by.due.date=æçä»»å¡å°ææ¥æ
page.label.my.tasks=æçä»»å¡
page.label.mysettings=æçè®¾ç½®
page.label.na=ä¸éç¨
page.label.name=åç§°
page.label.native=æ¬å°
page.label.new.condition=æ°å»ºæ¡ä»¶
page.label.new.content=æ°å»ºåå®¹
page.label.new.message=æ°å»ºæ¶æ¯
page.label.new.password=æ°å»ºå¯ç 
page.label.new.value=æ°å»ºå¼
page.label.new.variant=æ°å»ºåé
page.label.new=æ°
page.label.next.action=ä¸ä¸ä¸ªæä½
page.label.next=ä¸ä¸æ­¥
page.label.no.active.copy=æ æ´»å¨å¯æ¬
page.label.no.active.proofs=æ æ´»å¨ææ ·
page.label.no.available.touchpoints=æ å¯ç¨æ¥è§¦ç¹
page.label.no.border=æ è¾¹æ¡
page.label.no.category=æ ç±»å«
page.label.no.collection.filter=æ éåç­éå¨
page.label.no.content.selector=æ åå®¹éæ©å¨
page.label.no.customers.report=æ²¡æè¦æ¥åçå®¢æ·
page.label.no.default=æ é»è®¤è®¾ç½®
page.label.no.due.date=æ å°ææ¥æ
page.label.no.insert.schedules=æªä½¿ç¨ææ°æ®éæ©çæå¥æ¶é´è¡¨
page.label.no.inserts.assigned=æªåéæå¥
page.label.no.label=æ æ ç­¾
page.label.no.owner=æ ææè
page.label.no.previews=æ é¢è§
page.label.no.primary.data.source=æ å¯ç¨çä¸»æ°æ®æ¥æº
page.label.no.selected.inserts=æ éæ©æå¥
page.label.no.selection=æ éæ©åå®¹
page.label.no.short=N
page.label.no.touchpoint.association=æ æ¥è§¦ç¹å³è
page.label.no.touchpoints=æ æ¥è§¦ç¹
page.label.no.working.copy=æ å·¥ä½å¯æ¬
page.label.no=æ 
page.label.node.name=å®ä¾åç§°
page.label.node.settings=å®ä¾è®¾ç½®
page.label.node.status.cannot.open.db.schema=æ æ³æå¼ DB æ¶æ
page.label.node.status.db.schema.empty=DB æ¶æä¸ºç©º
page.label.node.status.incorrect.version=çæ¬éè¯¯
page.label.node.status.initialization.error=åå§åéè¯¯
page.label.node.status.initialization.in.progress=æ­£å¨åå§å
page.label.node.status.migration.error=è¿ç§»éè¯¯
page.label.node.status.migration.in.progress=æ­£å¨è¿ç§»
page.label.node.status.not.validated=æªéªè¯
page.label.node.status.off.line=è±æº
page.label.node.status.on.line=èæº
page.label.node.status.unknown.database.error=æªç¥æ°æ®åºéè¯¯
page.label.node.status.unknown=æªç¥
page.label.node.status=å®ä¾ç¶æ
page.label.node.type=å®ä¾ç±»å
page.label.node=å®ä¾
page.label.nodes=å®ä¾
page.label.none=æ 
page.label.not.applied=æªåºç¨
page.label.not.specified=æªæå®
page.label.not.tracked=æªè·è¸ª
page.label.note=æ³¨æ
page.label.notes=æ³¨é
page.label.number.content.library=å¾ååºé¡¹ç®æ°é
page.label.number.embedded.content=æºè½ææ¬æ°é
page.label.number.of.bins=Bin æ°é
page.label.number.of.copies=å¯æ¬æ°é
page.label.number.of.prompts=æç¤ºæ¶æ¯æ°é
page.label.number.regular.msg=éææ¶æ¯æ°é
page.label.number.sections=èæ°é
page.label.number.selectable.msg=å¨ææ¶æ¯æ°é
page.label.number.tp.selections=åéæ°é
page.label.number.tpcs.msg=ç»æåæ¶æ¯æ°é
page.label.number.tpms.msg=çµæ´»æ¶æ¯æ°é
page.label.number.zones=åºåæ°é
page.label.number=æ°é
page.label.numberofdecimals=å°æ°ä½æ°
page.label.numeric=æ°å¼
page.label.occurrences=äºä»¶
page.label.of=ç
page.label.off=å³é­
page.label.ok=ç¡®å®
page.label.on=æå¼
page.label.one.based=åºäº 1
page.label.online=èæº
page.label.open=æå¼
page.label.optimized=å·²ä¼å
page.label.optional.inserts=å¯éæå¥
page.label.optional=å¯é
page.label.or=æ
page.label.order.entry.enabled=å·²å¯ç¨è¯·æ±ç¨åº
page.label.order.entry.setup=è¯·æ±ç¨åºè®¾ç½®
page.label.order.entry.type.date.day.month.year=æ¥æ(æ¥/æ/å¹´)
page.label.order.entry.type.date.month.year=æ¥æ(æ/å¹´)
page.label.order.entry.type.select.menu=èå
page.label.order.entry.type.text=ææ¬
page.label.order.entry.type.textarea=ææ¬(å¤§)
page.label.order.entry.type.web.service.menu=èå(æ¥èª Web æå¡çå¼)
page.label.order.entry=è¯·æ±ç¨åº
page.label.order=å½ä»¤
page.label.other.customer.reporting.variable.a=å®¢æ·æ¥ååé A
page.label.other.customer.reporting.variable.b=å®¢æ·æ¥ååé B
page.label.other.page.weights=å¶ä»é¡µé¢æé
page.label.output=è¾åº
page.label.override.default=è¦çé»è®¤å¼
page.label.override.remote.server.settings=è¦çè¿ç¨æå¡å¨è®¾ç½®
page.label.overview.approval=æ¦è¿°æ¹å
page.label.overview=æ¦è¿°
page.label.overwritedateformat=æ¥ææ ¼å¼
page.label.package.file.PUB=åæä»¶ (PUB)
page.label.packaged=å·²æå
page.label.page.weight.first.other=é¡µé¢æé(ç¬¬ä¸ä¸ª/å¶ä»)
page.label.page=é¡µé¢
page.label.pages=é¡µé¢
page.label.paragraph.alignment.center=ä¸­å¿
page.label.paragraph.alignment.full=å·²è¯æææ
page.label.paragraph.alignment.left=å·¦
page.label.paragraph.alignment.right=å³
page.label.paragraph.spacing=æ®µè½é´è·
page.label.paragraph.style.customization=æ®µè½æ ·å¼èªå®ä¹
page.label.paragraph.style=æ®µè½æ ·å¼
page.label.paragraph.styles=æ®µè½æ ·å¼
page.label.paragraph=æ®µè½
page.label.parameter.group=åå®¹éæ©å¨ç»
page.label.parameter.groups=åå®¹éæ©å¨ç»
page.label.parameter=åå®¹éæ©å¨
page.label.parameterdetails=åå®¹éæ©å¨è¯¦ç»ä¿¡æ¯
page.label.parameteroverview=åå®¹éæ©å¨æ¦è¿°
page.label.parameters=åå®¹éæ©å¨
page.label.parent=ç¶çº§
page.label.part.content=åå®¹
page.label.part=é¨å
page.label.parts=é¨å
page.label.passed=å·²éè¿
page.label.password.properties=å¯ç å±æ§
page.label.password.settings=å¯ç è®¾ç½®
page.label.password.special.avoid.repeats=æç»è¿ç»­éå¤å­ç¬¦
page.label.password.special.lowercase=éä½¿ç¨å°åå­æ¯
page.label.password.special.numeral=éä½¿ç¨æ°å­
page.label.password.special.symbol=éä½¿ç¨ç¹æ®ç¬¦å·
page.label.password.special.uppercase=éä½¿ç¨å¤§åå­æ¯
page.label.password=å¯ç 
page.label.pending=å¾å®
page.label.percentage=ç¾åæ¯
page.label.personal.settings=ä¸ªäººè®¾ç½®
page.label.personal=ä¸ªäºº
page.label.phone=çµè¯
page.label.ping.sso.init.url=Ping SSO åå§ URL
page.label.ping.sso.rest.api.client.id=Ping REST API å®¢æ·ç«¯ ID
page.label.ping.sso.rest.api.client.key=Ping REST API å®¢æ·ç«¯å¯é¥
page.label.ping.sso.saas.id=Ping SaaS ID
page.label.ping.sso.settings=Ping SSO è®¾ç½®
page.label.ping.sso.token.resolution.url=Ping SSO æ è®° URL
page.label.ping.sso=Ping SSO
page.label.plain=æ æ ¼å¼
page.label.played=å·²è¿è¡
page.label.please.select=--- è¯·éæ© ---
page.label.pod.type=Pod ç±»å
page.label.pod=Pod
page.label.pods=Pod
page.label.point.size=ç¹å¤§å°
page.label.portrait=çºµå (8.5 x 11)
page.label.position=ä½ç½®
page.label.prepare.output=æ­£å¨åå¤è¾åº...
page.label.prepare.report=æ­£å¨åå¤æ¥å...
page.label.preview.data.resource=é¢è§æ°æ®èµæº
page.label.preview.language.configuration=é¢è§è¯­è¨éç½®
page.label.preview.zone=é¢è§åºå
page.label.preview=é¢è§
page.label.primary.data.file=ä¸»æ°æ®æä»¶
page.label.primary.data.input=ä¸»æ°æ®è¾å¥
page.label.primary.data.source=ä¸»æ°æ®æº
page.label.primary.data.variable=ä¸»æ°æ®åé
page.label.primary=ä¸»è¦
page.label.print=æå°
page.label.prioritize.messages=è®¾ç½®æ¶æ¯çä¼åçº§
page.label.prioritize.optional.inserts=è®¾ç½®å¯éæå¥çä¼åçº§
page.label.prioritize=è®¾ç½®ä¼åçº§
page.label.priority=ä¼åçº§
page.label.processing=æ­£å¨å¤ç
page.label.product=äº§å
page.label.production.message.report=æ´»å¨æ¶æ¯æ¥åè¯¦ç»ä¿¡æ¯
page.label.production.server.settings=çäº§æå¡å¨è®¾ç½®
page.label.production.status=çäº§ç¶æ
page.label.production=çäº§
page.label.proof.active=ææ ·(æ´»å¨)
page.label.proof=è¯æ
page.label.proofing.data.resource=æ£éªæ°æ®èµæº
page.label.proofing.data=æ£éªæ°æ®
page.label.proofs=ææ ·...
page.label.properties=å±æ§
page.label.property=å±æ§
page.label.provider.description=æä¾ç¨åºæè¿°
page.label.provider.logo.filename=æä¾ç¨åºæ è¯æä»¶å
page.label.provider.logo.filepath=æä¾ç¨åºæ è¯æä»¶è·¯å¾ååç§°
page.label.provider.logo=æä¾ç¨åºæ è¯
page.label.provider.settings=æä¾ç¨åºè®¾ç½®
page.label.proxy.sso.idpid=Pod ID
page.label.proxy.sso.masterpageurl=ä¸» URL
page.label.proxy.sso.pod.role.master=ä¸»
page.label.proxy.sso.pod.role.slave=ä»å±
page.label.proxy.sso.pod.role=Pod è§è²
page.label.proxy.sso.secretkey=å¯é¥
page.label.proxy.sso.settings=ç»ä¸ç»å½è®¾ç½®
page.label.puts=æ¾
page.label.qualification.output=èµæ ¼è¾åº
page.label.qualification=èµæ ¼
page.label.qualified=åæ ¼
page.label.quit=éåº
page.label.radio=åé
page.label.rate.sheet.coverage=è¯ä»·è¡¨è¦çèå´
page.label.rate.sheet=è¯ä»·è¡¨
page.label.rate.sheets=è¯ä»·è¡¨
page.label.reach=è®¿é®
page.label.reassign.insert.schedules=éæ°åéæå¥æ¶é´è¡¨
page.label.reassign.inserts=éæ°åéæå¥
page.label.reassign.messages=éæ°åéæ¶æ¯
page.label.reassign.to.user=éæ°åéç»ç¨æ·
page.label.reassign.touchpoint.variants=éæ°åéæ¥è§¦ç¹åé
page.label.reassign=éæ°åé
page.label.recipient.attachment.location=æ¥æ¶éä»¶çä½ç½®
page.label.recipient.attachment.name=æ¥æ¶éä»¶çåç§°
page.label.recipient.details=æ¶ä»¶äººè¯¦ç»ä¿¡æ¯
page.label.recipient.range=æ¶ä»¶äººèå´
page.label.recipient=æ¶ä»¶äºº
page.label.record.level=è®°å½çº§å«
page.label.record=è®°å½
page.label.recordindicator=è®°å½æç¤ºç¬¦
page.label.records=è®°å½
page.label.recordtype=è®°å½ç±»å
page.label.reference.data.file=å¼ç¨æ°æ®æä»¶
page.label.reference.data.source=å¼ç¨æ°æ®æº
page.label.reference.data.sources=å¼ç¨æ°æ®æº
page.label.reference.data.variable=å¼ç¨æ°æ®åé
page.label.reference.data=å¼ç¨æ°æ®
page.label.reference=å¼ç¨
page.label.referenced=å·²å¼ç¨
page.label.refresh.segmentation.analysis=å·æ°åæ®µåæ
page.label.reinstate=æ¢å¤
page.label.reject.and.override=æç»åè¦ç
page.label.reject.workflow.owner=æç»: å·¥ä½æµææè
page.label.reject=æç»
page.label.related.data.has.been.deleted=é¡¹å·²è¢«å é¤
page.label.related.item=ç¸å³é¡¹
page.label.related=ç¸å³
page.label.release.for.approval=å¾æ¹åçæ¬
page.label.release.for.use=åå¸ä»¥ä¾ä½¿ç¨
page.label.release.to.tenants=åå¸å°ç§æ·
page.label.release=åå¸
page.label.remote.server.ip=è¿ç¨æå¡å¨ IP
page.label.remote.server.password=è¿ç¨æå¡å¨å¯ç 
page.label.remote.server.port=è¿ç¨æå¡å¨ç«¯å£
page.label.remote.server.user=è¿ç¨æå¡å¨ç¨æ·
page.label.remove.connection=ç§»é¤è¿æ¥
page.label.remove.data=ç§»é¤æ°æ®
page.label.remove.filter=ç§»é¤ç­éå¨
page.label.remove.from.favorites=ä»æ¶èå¤¹ä¸­ç§»é¤
page.label.remove.menu.item=ç§»é¤èåé¡¹
page.label.remove.message=ç§»é¤æ¶æ¯
page.label.remove.touchpoint.variants=ç§»é¤æ¥è§¦ç¹åé
page.label.remove.variant=ç§»é¤åé
page.label.remove=ç§»é¤
page.label.rename.selection=éå½åéæ©
page.label.rename.variant=éå½ååé
page.label.rename=éå½å
page.label.reopen.tenant.window=éæ°æå¼ç§æ·çªå£
page.label.repair=ä¿®å¤
page.label.repeat.annually=æ¯å¹´éå¤
page.label.repeating=éå¤
page.label.repeatingevents=éå¤äºä»¶?
page.label.repeatingrecord=éå¤è®°å½
page.label.repeats=éå¤
page.label.reply.to.email=åå¤çµå­é®ä»¶
page.label.report=æ¥å
page.label.reports=æ¥è¡¨
page.label.request.date=è¯·æ±æ¥æ
page.label.request.password.reset=è¯·æ±å¯ç éç½®
page.label.request.touchpoint.audit.report=è¯·æ±æ¥è§¦ç¹å®¡æ ¸æ¥å
page.label.request=è¯·æ±
page.label.requestedaction=è¯·æ±çæä½
page.label.rerun=éæ°è¿è¡
page.label.resend.activation=éæ°åéæ¿æ´»
page.label.reservations=ä¿ç
page.label.reserved.number=ä¿çæ°
page.label.reserved=ä¿ç
page.label.reset.password.pending=éç½®å¯ç ç­å¾
page.label.reset.password=éç½®å¯ç 
page.label.reset.template=éç½®æ¨¡æ¿
page.label.reset=éç½®
page.label.resetpassword=éç½®å¯ç 
page.label.resettodefault=éç½®ä¸ºé»è®¤å¼
page.label.restore=è¿å
page.label.restricted=åé
page.label.results=ç»æ
page.label.right=å³
page.label.role.description=è§è²æè¿°
page.label.role.view=æ¥çè§è²
page.label.role.visibility.private=ç§æ
page.label.role.visibility.shared=å±äº«
page.label.role.visibility=è§è²å¯è§æ§
page.label.role=è§è²
page.label.rolename=è§è²åç§°
page.label.roles=è§è²
page.label.roll.forward=åæ»
page.label.root.tags=æ ¹æ è®°
page.label.rule.conditions=è§åæ¡ä»¶
page.label.rule.name=è§ååç§°
page.label.rule=è§å
page.label.rules=è§å
page.label.run.all=è¿è¡å¨é¨
page.label.run.date=è¿è¡æ¥æ
page.label.run.health.check.and.resolve=è¿è¡ç¶åµæ£æ¥åé®é¢è§£å³
page.label.run=è¿è¡
page.label.same.as=ç­åäº
page.label.sample=ç¤ºä¾
page.label.sandbox=æ²ç
page.label.save.and.proof=ä¿å­å¹¶ææ ·
page.label.save.complete=ä¿å­å®æ
page.label.save.continue=ä¿å­å¹¶ç»§ç»­
page.label.save=ä¿å­
page.label.scan.domains=æ«æå
page.label.schedule.ID=æ¶é´è¡¨ ID
page.label.schedule.selector.data=æ¶é´è¡¨éæ©å¨æ°æ®
page.label.schema.name=æ¶æåç§°
page.label.schema.password=æ¶æå¯ç 
page.label.search.content=æç´¢åå®¹
page.label.search=æç´¢
page.label.section=è
page.label.sections=è
page.label.secure.signin=å®å¨ç»å½
page.label.secure=å®å¨
page.label.security.settings=å®å¨è®¾ç½®
page.label.security=å®å¨
page.label.segmentation.analysis=åæ®µåæ
page.label.segmentation=åæ®µ
page.label.select.all=éæ©å¨é¨
page.label.select.by=éæ©è
page.label.select.connector=éæ©è¿æ¥å¨
page.label.select.content.by=åå®¹éæ©æ¹å¼
page.label.select.image=éæ©å¾å
page.label.select=éæ©
page.label.selectable.constant=å¯éæ©çå¸¸é
page.label.selected.by=éæ©è
page.label.selected.inserts=éä¸­çæå¥
page.label.selected=éä¸­
page.label.selection.data=éæ©æ°æ®
page.label.selection.status=éæ©ç¶æ
page.label.selections=éæ©
page.label.selector=éæ©å¨
page.label.selectors=éæ©å¨
page.label.self=èªèº«
page.label.sender.email=åéäººçµå­é®ä»¶
page.label.sender.name=åéäººå§å
page.label.sendmail.server.settings=Sendmail è¿æ¥å¨æå¡å¨è®¾ç½®
page.label.sequence=åºå
page.label.server=æå¡å¨
page.label.service.provider=æå¡æä¾ç¨åº
page.label.session.timeout=ä¼è¯è¶æ¶
page.label.set.background=è®¾ç½®èæ¯
page.label.set.modifiers.and.attributes=è®¾ç½®ä¿®é¥°ç¬¦åå±æ§
page.label.set.modifiers.attributes=è®¾ç½®ä¿®é¥°ç¬¦åå±æ§
page.label.set.modifiers=è®¾ç½®ä¿®é¥°ç¬¦
page.label.set.template=è®¾ç½®æ¨¡æ¿
page.label.set.visibility=è®¾ç½®å¯è§æ§
page.label.settings=è®¾ç½®
page.label.setup.insert.schedules=è®¾ç½®æå¥æ¶é´è¡¨
page.label.setup.required=æéè®¾ç½®
page.label.setup.touchpoint.variants=è®¾ç½®æ¥è§¦ç¹åé
page.label.setup.touchpoint=è®¾ç½®æ¥è§¦ç¹
page.label.setup.workflow=è®¾ç½®å·¥ä½æµ
page.label.setup=è®¾ç½®
page.label.shared.brackets=[å±äº«]
page.label.shared.content.workflow=å±äº«åå®¹å·¥ä½æµ
page.label.shared.content=å±äº«åå®¹
page.label.shared=å±äº«
page.label.sharing.properties=å±äº«å±æ§
page.label.sheets=è¡¨
page.label.short.month.name=ç­æä»½åç§°
page.label.short.month.names=ç­çæä»½åç§°
page.label.show.items=æ¾ç¤ºé¡¹
page.label.show=æ¾ç¤º
page.label.showing=æ¾ç¤º
page.label.siblings=åçº§
page.label.sign.in.settings=ç»å½è®¾ç½®
page.label.signout=æ³¨é
page.label.simulation.customer.flag=åæ¬å®¢æ·è¯¦ç»ä¿¡æ¯
page.label.simulation.data=æ¨¡ææ°æ®
page.label.simulation.date=æ¨¡ææ¥æ
page.label.simulation.run.date=æ¨¡æè¿è¡æ¥æ
page.label.simulation=æ¨¡æ
page.label.simulations=æ¨¡æ
page.label.single.sign.out=åç¹æ³¨é
page.label.single=åä¸ª
page.label.size.memory=å¤§å°(åå­)
page.label.size=å¤§å°
page.label.solid=åºå®
page.label.source.type=æºç±»å
page.label.source=æº
page.label.spacing.after=æ®µåé´è·
page.label.spacing.before=æ®µåé´è·
page.label.spacing=é´è·
page.label.special=ç¹æ®
page.label.specify.new.variant.name=ä¸ºæ°åéæå®åç§°
page.label.ssoerrorpageurl=SSO éè¯¯é¡µé¢ URL
page.label.ssoidpid=SSO IdP ID
page.label.ssologoutpageurl=SSO æ³¨éé¡µé¢ URL
page.label.ssosecretkey=SSO å¯é¥
page.label.stale.brackets=(åæ»)
page.label.stale=åæ»
page.label.start.data.records=å¼å§æ°æ®è®°å½
page.label.start.date=å¼å§æ¥æ
page.label.start.upload=å¼å§ä¸ä¼ 
page.label.start=å¼å§
page.label.startcustomer=å¼å§ç¨æ·
page.label.state.approved=æ­£å¨è¿è¡
page.label.state.completed=å·²å®æ
page.label.state.error=éè¯¯
page.label.state.exported=æ­£å¨è¿è¡
page.label.state.frozen=æ­£å¨è¿è¡
page.label.state.inprocess=æ­£å¨è¿è¡
page.label.state.new=æ°
page.label.state.notapplicable=ä¸éç¨
page.label.state.notstarted=æªå¼å§
page.label.state.packed=æ­£å¨è¿è¡
page.label.state.pendingpacking=æ­£å¨è¿è¡
page.label.state.pendingreportprocessing=æ­£å¨è¿è¡
page.label.state.pendingscenarioprocessing=æ­£å¨è¿è¡
page.label.state.pendingscenarioprocessingfrozen=æ­£å¨è¿è¡
page.label.state.readytostart=åå¤å¥½å¼å§
page.label.state.requested=å·²è¯·æ±
page.label.state.started=å·²å¼å§
page.label.static=éæ
page.label.status.archived=å·²å­æ¡£
page.label.status.inactive=éæ´»å¨
page.label.status.new=æ°
page.label.status.production=æ´»å¨
page.label.status.removed=å·²ç§»é¤
page.label.status.setup=è®¾ç½®
page.label.status.waiting.approval=æ­£å¨ç­å¾æ¹å
page.label.status.wip=å·¥ä½å¯æ¬
page.label.status=ç¶æ
page.label.stock.id=åºå­ ID
page.label.structured=ç»æå
page.label.style.customizations=æ ·å¼èªå®ä¹
page.label.style=æ ·å¼
page.label.styles=æ ·å¼
page.label.submit=æäº¤
page.label.submitted=å·²æäº¤
page.label.succeess=æå
page.label.success=æå
page.label.suffix.currency.symbol=åç¼è´§å¸ç¬¦å·
page.label.suggested.schema.name=å»ºè®®çæ¶æåç§°
page.label.support=æ¯æ
page.label.supports.styles=æ¯ææ ·å¼
page.label.suppress.content=ç¦æ­¢åå®¹
page.label.suppress=ç¦æ­¢
page.label.suppressed=å·²ç¦æ­¢
page.label.switch.tenants=åæ¢ç§æ·
page.label.switch.to.node.instance=åæ¢å°å®ä¾
page.label.switch.to.tenant.user=åæ¢å°ç§æ·ç¨æ·
page.label.system.settings=ç³»ç»è®¾ç½®
page.label.system_view.page=system_view é¡µé¢
page.label.table.alignment=è¡¨æ ¡å
page.label.table=è¡¨
page.label.tables=è¡¨
page.label.tag.name=æ è®°åç§°
page.label.tag.type=æ è®°ç±»å
page.label.tag.types=æ è®°ç±»å
page.label.tag=æ è®°
page.label.tags=æ è®°
page.label.target.message=ç®æ ä¿¡æ¯
page.label.target=ç®æ 
page.label.targetgroup.details=ç®æ è§å
page.label.targetgroup.name=ç®æ ç»åç§°
page.label.targetgroup=ç®æ ç»
page.label.targetgroups=ç®æ ç»
page.label.targeting.summary=ç®æ æè¦
page.label.targeting=æå®ç®æ 
page.label.task.name=ä»»å¡åç§°
page.label.task.overview=ä»»å¡æ¦è¿°
page.label.task.summary=ä»»å¡æè¦
page.label.task=ä»»å¡
page.label.tasks.by.due.date=æå°ææ¥ææåºçä»»å¡
page.label.tasks.by.user=æç¨æ·æåºçä»»å¡
page.label.tasks=ä»»å¡
page.label.template.attributes=å±æ§
page.label.template.modifier.list=æ¨¡æ¿ä¿®é¥°ç¬¦åè¡¨
page.label.template.modifier.setup=æ¨¡æ¿ä¿®é¥°ç¬¦è®¾ç½®
page.label.template.modifiers=æ¨¡æ¿ä¿®é¥°ç¬¦
page.label.template.package=æ¨¡æ¿å
page.label.template=æ¨¡æ¿
page.label.tenant.access=ç§æ·è®¿é®
page.label.tenant.error.correctiveaction=æ´æ­£æä½
page.label.tenant.error.errordetails=éè¯¯è¯¦ç»ä¿¡æ¯
page.label.tenant.error.limitexceed=éè¯¯ - è¶è¿ç§æ·è®¸å¯éå¶
page.label.tenant.insert.schedules=ç§æ·æå¥æ¶é´è¡¨
page.label.tenant.management.schedule.setup=æå¥æ¶é´è¡¨ç§æ·è®¾ç½®
page.label.tenant.management.schedule.state.in.process=æ­£å¨è¿è¡
page.label.tenant.management.schedule.state.released=åå¸å°ç§æ·
page.label.tenant.management.schedule=ç§æ·ç®¡çæ¶é´è¡¨
page.label.tenant.management=ç§æ·ç®¡ç
page.label.tenant.overview=ç§æ·æ¦è¿°
page.label.tenant.permission.settings=ç§æ·æéè®¾ç½®
page.label.tenant.permissions=ç§æ·æé
page.label.tenant.schedule.assignment.setup=ç¼è¾ç§æ·æ¶é´è¡¨
page.label.tenant.schedule.assignment.state.complete=å®æ
page.label.tenant.schedule.assignment.state.in.process=æ­£å¨è¿è¡
page.label.tenant.schedule.assignment=ç§æ·æ¶é´è¡¨
page.label.tenant.schedules=ç§æ·æ¶é´è¡¨
page.label.tenant.settings.code=ç§æ·ä»£ç 
page.label.tenant.settings.contact.address=è¡éå°å
page.label.tenant.settings.contact.city=åå¸
page.label.tenant.settings.contact.country=å½å®¶/å°åº
page.label.tenant.settings.contact.email=çµå­é®ä»¶
page.label.tenant.settings.contact.fax=ä¼ ç
page.label.tenant.settings.contact.mobile=ç§»å¨çµè¯
page.label.tenant.settings.contact.name=åç§°
page.label.tenant.settings.contact.postalcode=é®æ¿/é®æ¿ç¼ç 
page.label.tenant.settings.contact.province=ç/å·
page.label.tenant.settings.contact.suite=æ¿é´/åä½
page.label.tenant.settings.contact.title=æ é¢
page.label.tenant.settings.contact.web=ç½ç«
page.label.tenant.settings.edit.title=ç¼è¾ç§æ·
page.label.tenant.settings.name=ç§æ·åç§°
page.label.tenant.settings.overview=ç§æ·æ¦è¿°
page.label.tenant.settings.primary.contact=ä¸»è¦èç³»äºº
page.label.tenant.settings.provider=ç§æ·çæä¾ç¨åº
page.label.tenant.settings.status=ç¶æ
page.label.tenant.settings.title=æ¥çç§æ·è®¾ç½®
page.label.tenant.settings=ç§æ·è®¾ç½®
page.label.tenant.threshold.overrides=ç§æ·éå¼è¦ç
page.label.tenant.window=ç§æ·çªå£
page.label.tenant=ç§æ·
page.label.tenant_label=ç§æ·
page.label.tenantconfirm.action=è¯·æ±çæä½
page.label.tenantconfirm.activeusers=æ´»å¨ç¨æ·
page.label.tenantconfirm.consequence=ç»æ
page.label.tenantconfirm.instruction=è¯´æ
page.label.tenantconfirm.name=ç§æ·åç§°
page.label.tenantconfirm.provider=ç§æ·çæä¾ç¨åº
page.label.tenantconfirm.status=ç¶æ
page.label.tenants=ç§æ·
page.label.term=æ¡æ¬¾
page.label.test.data=æµè¯æ°æ®
page.label.test.date=æµè¯æ¥æ
page.label.test.run.date=æµè¯è¿è¡æ¥æ
page.label.test=æµè¯
page.label.testing=æµè¯
page.label.tests=æµè¯
page.label.text.field=ææ¬å­æ®µ
page.label.text.formatting=ææ¬æ ¼å¼
page.label.text.style.customization=ææ¬æ ·å¼èªå®ä¹
page.label.text.style=ææ¬æ ·å¼
page.label.text.styles=ææ¬æ ·å¼
page.label.text=ææ¬
page.label.theme.color=ä¸»é¢é¢è²
page.label.theme.settings=ä¸»é¢è®¾ç½®
page.label.theme=ä¸»é¢
page.label.thousandsseparator=åä½åéç¬¦
page.label.throttle=éå¶
page.label.time=æ¶é´
page.label.timing.summary=è®¡æ¶æè¦
page.label.timing=è®¡æ¶
page.label.to.date=æªæ­¢æ¥æ
page.label.to.path=è³è·¯å¾
page.label.to=è³
page.label.touchpoint.assignments=æ¥è§¦ç¹åé
page.label.touchpoint.channel.configuration=æ¥è§¦ç¹æ¸ ééç½®
page.label.touchpoint.collection=æ¥è§¦ç¹éå
page.label.touchpoint.collections=æ¥è§¦ç¹éå
page.label.touchpoint.container.publication.file.path=åå¸æä»¶è·¯å¾
page.label.touchpoint.container.workflow.file.path=å·¥ä½æµæä»¶è·¯å¾
page.label.touchpoint.container.zone.attributes=å±æ§
page.label.touchpoint.container.zone.datagroup=æ°æ®ç»
page.label.touchpoint.container.zone.tenants=ç§æ·
page.label.touchpoint.container.zoneconnector=è¿æ¥å¨åç§°
page.label.touchpoint.container.zonename=åºååç§°
page.label.touchpoint.container=æ¥è§¦ç¹å®¹å¨
page.label.touchpoint.delivery.report=æ¥è§¦ç¹ä¼ éæ¥å
page.label.touchpoint.deliveryevent.creationtime=äºä»¶åå»ºæ¥æ
page.label.touchpoint.deliveryevent.deliverystatus=ä¼ éç¶æ
page.label.touchpoint.deliveryevent.jobid=ä½ä¸ ID
page.label.touchpoint.deliveryevent.production.runtype=æ´»å¨è¿è¡ç±»å
page.label.touchpoint.deliveryevent.requestor=è¯·æ±è
page.label.touchpoint.deliveryevent.scheduledruntime=è®¡åçè¿è¡æ¥æ
page.label.touchpoint.deliveryevent.updatetime=ä¸æ¬¡æ´æ°æ¥æ
page.label.touchpoint.deliveryeventhistory=äºä»¶åå²è®°å½
page.label.touchpoint.health.check=æ¥è§¦ç¹è¿è¡ç¶åµæ£æ¥
page.label.touchpoint.import.filename=å¯¼å¥ XML æä»¶
page.label.touchpoint.management=æ¥è§¦ç¹ç®¡ç
page.label.touchpoint.name=æ¥è§¦ç¹åç§°
page.label.touchpoint.no.deliveryevent.found=æªæ¾å°æ­¤æ¥è§¦ç¹çä¼ éäºä»¶ã
page.label.touchpoint.no.deliveryeventhistory.found=æªæ¾å°äºä»¶åå²è®°å½é¡¹ç®ã
page.label.touchpoint.properties=æ¥è§¦ç¹å±æ§
page.label.touchpoint.setup=æ¥è§¦ç¹è®¾ç½®
page.label.touchpoint.status=æ¥è§¦ç¹ç¶æ
page.label.touchpoint.targeting=æ¥è§¦ç¹ç®æ æå®
page.label.touchpoint.variant.content=æ¥è§¦ç¹åéåå®¹
page.label.touchpoint.variants=æ¥è§¦ç¹åé
page.label.touchpoint=æ¥è§¦ç¹
page.label.touchpointadmin=æ¥è§¦ç¹
page.label.touchpoints=æ¥è§¦ç¹
page.label.track.invalid.sign.ins=è·è¸ªæ æç»å½å°è¯
page.label.transactional=äºå¡æ§
page.label.transition.environment=é¢è§çæ¬
page.label.transition=é¢è§ç
page.label.true=True
page.label.twosecs=2 ç§
page.label.type=ç±»å
page.label.unassigned.brackets=(æªåé)
page.label.unavailable=ä¸å¯ç¨
page.label.underline=ä¸åçº¿
page.label.unexpected.error=æ±æ­!
page.label.unhold=éæ¾
page.label.united.states=ç¾å½
page.label.unlimted=æ éå¶?
page.label.unrestricted=ä¸åéå¶
page.label.update.channel.configuration=ç¼è¾æ¥è§¦ç¹æ¸ ééç½®
page.label.update.communications.workflow=ç¼è¾éä¿¡å·¥ä½æµ
page.label.update.content.library.workflow=ç¼è¾å¾ååºå·¥ä½æµ
page.label.update.embedded.content.workflow=ç¼è¾æºè½ææ¬å·¥ä½æµ
page.label.update.language.settings=ç¼è¾è¯­è¨è®¾ç½®
page.label.update.locale.settings=ç¼è¾åºåè®¾ç½®
page.label.update.master.system.settings=ä¸»ç®¡çå: ç³»ç»è®¾ç½®
page.label.update.message.workflow=ç¼è¾æ¶æ¯å·¥ä½æµ
page.label.update.selectors=ç¼è¾éæ©å¨
page.label.update.shared.content.workflow=ç¼è¾å±äº«åå®¹å·¥ä½æµ
page.label.update.system.settings=ç¼è¾ç³»ç»è®¾ç½®
page.label.update.test.scenario=ç¼è¾æµè¯
page.label.update.test=æ´æ°æµè¯åºæ¯
page.label.update.touchpoint.selection.workflow=ç¼è¾æ¥è§¦ç¹åéå·¥ä½æµ
page.label.update=æ´æ°
page.label.updated=å·²æ´æ°
page.label.updateworkgroup=ç¼è¾å·¥ä½ç»
page.label.updateworkgroupzoneassociations=ç¼è¾å·¥ä½ç»åºåå³è
page.label.upload.bulk.variants=ä¸ä¼ åé
page.label.upload.folders=ä¸ä¼ æä»¶å¤¹
page.label.upload.messages=ä¸ä¼ æ¶æ¯
page.label.upload.theme=ä¸ä¼ ä¸»é¢
page.label.upload.variants=ä¸ä¼ åé
page.label.upload=ä¸ä¼ 
page.label.url=URL
page.label.usage=ä½¿ç¨
page.label.use.all.in.process.library.assets=ä½¿ç¨åºèµäº§çææå·¥ä½å¯æ¬
page.label.use.all.in.process=ä½¿ç¨ææå·¥ä½å¯æ¬
page.label.use.default.template=ä½¿ç¨é»è®¤æ¨¡æ¿
page.label.use=ä½¿ç¨
page.label.user.by.schedules=ä½¿ç¨æ¡ä»¶ - æ¶é´è¡¨
page.label.user.id.properties=ç¨æ·åå±æ§
page.label.user.id.settings=ç¨æ·åè®¾ç½®
page.label.user.interface.settings=ç¨æ·çé¢è®¾ç½®
page.label.user.management=ç¨æ·ç®¡ç
page.label.user.overview=ç¨æ·æ¦è¿°
page.label.user.settings.saved=å·²æåæ´æ°æ¨çè®¾ç½®ãåå»âå®æâå³é­æ­¤çªå£ã
page.label.user.settings=ç¨æ·è®¾ç½®
page.label.user.specified.values=ç¨æ·æå®å¼
page.label.user.view=æ¥çç¨æ·
page.label.user=ç¨æ·
page.label.username=ç¨æ·å
page.label.users=ç¨æ·
page.label.uses=ä½¿ç¨
page.label.utf8=UTF-8
page.label.validate.initialize=éªè¯(åå§å)
page.label.validated=å·²éªè¯
page.label.value=å¼
page.label.variable.details=åéè¯¦ç»ä¿¡æ¯
page.label.variable=åé
page.label.variables.map.to=åéæ å°å°
page.label.variables.none=æ åé
page.label.variables=åé
page.label.variant.actions=åéæä½
page.label.variant.finder=åéæ¥æ¾å¨
page.label.variant.selector.data=åéæ°æ®éæ©å¨
page.label.variant=åé
page.label.variants.fully.visible.by.default=é»è®¤å®æ´å¯è§æ§
page.label.variants=åé
page.label.variation.enabled=å·²å¯ç¨åé
page.label.varibale.lookup.items=åéæ¥æ¾é¡¹
page.label.varied.by=ååæ¹å¼
page.label.verbose=è¯¦ç»
page.label.version.activity.aborted=å·²ä¸­æ­¢
page.label.version.activity.archived=å·²å­æ¡£
page.label.version.activity.checkout=çæ¬ç­¾åº
page.label.version.activity.clone=ä»ç°æçæ¬ä¸­åé
page.label.version.activity.new=æ°
page.label.version.activity.newversioncheckedin=å·²ç­¾å¥æ´æ°çæ¬
page.label.video=è§é¢
page.label.view.all.tags=æ¥çæææ è®°
page.label.view.audit.report=æ¥çå®¡æ ¸æ¥å
page.label.view.data.source=æ¥çæ°æ®æº
page.label.view.data=æ¥çæ°æ®
page.label.view.inclusions=æ¥çåå«é¡¹
page.label.view.language.settings=æ¥çè¯­è¨è®¾ç½®
page.label.view.locale.settings=æ¥çåºåè®¾ç½®
page.label.view.log=æ¥çæ¥å¿
page.label.view.multipart.content=æ¥çå±äº«çåå®¹
page.label.view.preview=æ¥çé¢è§
page.label.view.proof=æ¥çææ ·
page.label.view.selection.data=æ¥çéæ©æ°æ®
page.label.view.system.properties=æ¥çç³»ç»å±æ§
page.label.view.system.settings=æ¥çç³»ç»è®¾ç½®
page.label.view=æ¥ç
page.label.viewdataelement=æ¥çæ°æ®åç´ 
page.label.viewdatasource=æ¥çæ°æ®æº
page.label.viewing=æ¥ç
page.label.viewtask=ä»»å¡æ¦è¿°
page.label.viewworkgroup=æ¥çå·¥ä½ç»
page.label.viewworkgroupzoneassociations=æ¥çå·¥ä½ç»åºåå³è
page.label.visibility.unrestricted=ä¸åéå¶
page.label.visibility=å¯è§æ§
page.label.visible=å¯è§
page.label.warning=è­¦å
page.label.web.folders=Web æä»¶å¤¹
page.label.web.font=Web å­ä½
page.label.web.page=Web é¡µé¢
page.label.web.server.settings=Web æå¡å¨è®¾ç½®
page.label.web.service=Web æå¡
page.label.web.url=Web URL
page.label.weight.unit.grams=å
page.label.weight.unit.ounces=çå¸
page.label.weight.unit=ééåä½
page.label.weight=éé
page.label.welcome.signin=æ¨å·²ç»å½ä¸º
page.label.where.used=ä½¿ç¨ä½ç½®
page.label.width=å®½åº¦
page.label.windows1250=Windows-1250
page.label.windows1252=Windows-1252
page.label.workflow.definition.WFD=å·¥ä½æµå®ä¹ (WFD)
page.label.workflow.due.by.type.notify.and.approve=éç¥åæ¹å
page.label.workflow.due.by.type.notify=éç¥
page.label.workflow.history=å·¥ä½æµåå²è®°å½
page.label.workflow=å·¥ä½æµ
page.label.workgroup=å·¥ä½ç»
page.label.workgroupoverview=å·¥ä½ç»æ¦è¿°
page.label.workgroups=å·¥ä½ç»
page.label.working.copy=å·¥ä½å¯æ¬
page.label.working=æ­£å¨å·¥ä½
page.label.xml.file.name=XML æä»¶å
page.label.xml.tag.break.indicator=æ¢è¡æç¤ºç¬¦
page.label.xml.tag.start.data.group=å¯å¨æ°æ®ç»
page.label.yes.short=Y
page.label.yes=æ¯
page.label.zero.based=åºäº 0
page.label.zone.marker.style=åºåæ è®°æ ·å¼
page.label.zone=åºå
page.label.zoneassociations=åºåå³è
page.label.zonepartlabel=åºåé¨ä»¶æ ç­¾
page.label.zones.without.datagroup.definition=æ æ°æ®ç»å®ä¹çåºå
page.label.zones=åºå
page.lable.always.create.duplicates=      å§ç»åå»ºå¤å¶
page.lable.job.number=ä½ä¸ç¼å·
page.lable.run.diagnostics=è¿è¡è¯æ­ç¨åº
page.locale.afrikaans.za=åéè·å°è¯­(åé)
page.locale.afrikaans=åéè·å°è¯­
page.locale.albanian.al=é¿å°å·´å°¼äºè¯­(é¿å°å·´å°¼äº)
page.locale.albanian=é¿å°å·´å°¼äºè¯­
page.locale.arabic.ae=é¿æä¼¯è¯­(é¿æä¼¯èåéé¿å½)
page.locale.arabic.bh=é¿æä¼¯è¯­(å·´æ)
page.locale.arabic.dz=é¿æä¼¯è¯­(é¿å°åå©äº)
page.locale.arabic.eg=é¿æä¼¯è¯­(åå)
page.locale.arabic.iq=é¿æä¼¯è¯­(ä¼æå)
page.locale.arabic.jo=é¿æä¼¯è¯­(çº¦æ¦)
page.locale.arabic.kw=é¿æä¼¯è¯­(ç§å¨ç¹)
page.locale.arabic.lb=é¿æä¼¯è¯­(é»å·´å«©)
page.locale.arabic.ly=é¿æä¼¯è¯­(å©æ¯äº)
page.locale.arabic.ma=é¿æä¼¯è¯­(æ©æ´å¥)
page.locale.arabic.om=é¿æä¼¯è¯­ (é¿æ¼)
page.locale.arabic.qa=é¿æä¼¯è¯­(å¡å¡å°)
page.locale.arabic.sa=é¿æä¼¯è¯­(æ²ç¹é¿æä¼¯)
page.locale.arabic.sy=é¿æä¼¯è¯­(åå©äº)
page.locale.arabic.tn=é¿æä¼¯è¯­(çªå°¼æ¯)
page.locale.arabic.ye=é¿æä¼¯è¯­(ä¹é¨)
page.locale.arabic=é¿æä¼¯è¯­
page.locale.basque.eu=å·´æ¯åè¯­(å·´æ¯å)
page.locale.basque=å·´æ¯åè¯­
page.locale.bosnian.ba=æ³¢æ¯å°¼äºè¯­(æ³¢æ¯å°¼äº - æä¸)
page.locale.bosnian=æ³¢æ¯å°¼äºè¯­
page.locale.breton.fr=å¸åå¡å°¼è¯­(æ³å½)
page.locale.breton=å¸åå¡å°¼è¯­
page.locale.cantonese.hk=ç²¤è¯­(ä¸­å½é¦æ¸¯ç¹å«è¡æ¿åº)
page.locale.cantonese.mo=ç²¤è¯­(ä¸­å½æ¾³é¨ç¹å«è¡æ¿åº)
page.locale.cantonese=ç²¤è¯­
page.locale.catalan.ad=å æ³°ç½å°¼äºè¯­(å æ³°ç½å°¼äº)
page.locale.catalan=å æ³°ç½å°¼äºè¯­
page.locale.chinese.cn=æ±è¯­(ä¸­å½)
page.locale.chinese.hk=æ±è¯­(ä¸­å½é¦æ¸¯ç¹å«è¡æ¿åº)
page.locale.chinese.mo=æ±è¯­(ä¸­å½æ¾³é¨ç¹å«è¡æ¿åº)
page.locale.chinese.sg=æ±è¯­(æ°å å¡)
page.locale.chinese.tw=æ±è¯­(ä¸­å½å°æ¹¾)
page.locale.chinese=æ±è¯­
page.locale.croatian.hr=åç½å°äºè¯­(åç½å°äº)
page.locale.croatian=åç½å°äºè¯­
page.locale.czech.cz=æ·åè¯­(æ·åå±åå½)
page.locale.czech=æ·åè¯­
page.locale.danish.dk=ä¸¹éº¦è¯­(ä¸¹éº¦)
page.locale.danish=ä¸¹éº¦è¯­
page.locale.dutch.be=è·å°è¯­(æ¯å©æ¶)
page.locale.dutch.nl=è·å°è¯­(è·å°)
page.locale.dutch=è·å°è¯­
page.locale.english.au=è±è¯­(æ¾³å¤§å©äº)
page.locale.english.bs=è±è¯­(å åæ¯æµ·)
page.locale.english.bz=è±è¯­(ä¼¯å©å¹)
page.locale.english.ca=è±è¯­(å æ¿å¤§)
page.locale.english.ie=è±è¯­(ç±å°å°)
page.locale.english.in=è±è¯­(å°åº¦)
page.locale.english.jm=è±è¯­(çä¹°å )
page.locale.english.my=è±è¯­(é©¬æè¥¿äº)
page.locale.english.nz=è±è¯­(æ°è¥¿å°)
page.locale.english.ph=è±è¯­(è²å¾å®¾)
page.locale.english.sg=è±è¯­(æ°å å¡)
page.locale.english.tt=è±è¯­(ç¹ç«å°¼è¾¾å²åå¤å·´å¥)
page.locale.english.uk=è±è¯­(è±å½)
page.locale.english.us=è±è¯­(ç¾å½)
page.locale.english.za=è±è¯­(åé)
page.locale.english.zw=è±è¯­(æ´¥å·´å¸é¦)
page.locale.english=è±è¯­
page.locale.estonian.ee=ç±æ²å°¼äºè¯­(ç±æ²å°¼äº)
page.locale.estonian=ç±æ²å°¼äºè¯­
page.locale.faroese.fo=æ³ç½è¯­(æ³ç½ç¾¤å²)
page.locale.faroese=æ³ç½è¯­
page.locale.finnish.fi=è¬å°è¯­(è¬å°)
page.locale.finnish=è¬å°è¯­
page.locale.french.be=æ³è¯­(æ¯å©æ¶)
page.locale.french.ca=æ³è¯­(å æ¿å¤§)
page.locale.french.ch=æ³è¯­(çå£«)
page.locale.french.fr=æ³è¯­(æ³å½)
page.locale.french.lu=æ³è¯­(å¢æ£®å ¡)
page.locale.french.mc=æ³è¯­(æ©çº³å¥)
page.locale.french.nl=æ³è¯­(è·å°)
page.locale.french=æ³è¯­
page.locale.galician.gl=å å©è¥¿äºè¯­(å å©è¥¿äº)
page.locale.galician=å å©è¥¿äºè¯­
page.locale.german.at=å¾·è¯­(æ¾³å¤§å©äº)
page.locale.german.ch=å¾·è¯­(çå£«)
page.locale.german.de=å¾·è¯­(å¾·å½)
page.locale.german.li=å¾·è¯­(åæ¯æ¦å£«ç»)
page.locale.german.lu=å¾·è¯­(å¢æ£®å ¡)
page.locale.german=å¾·è¯­
page.locale.hebrew.il=å¸ä¼¯æ¥è¯­(ä»¥è²å)
page.locale.hebrew=å¸ä¼¯æ¥è¯­
page.locale.hindi.in=å°å°è¯­(å°åº¦)
page.locale.hindi=å°å°è¯­
page.locale.hungarian.hu=åçå©è¯­(åçå©)
page.locale.hungarian=åçå©è¯­
page.locale.icelandic.is=å°å²è¯­(å°å²)
page.locale.icelandic=å°å²è¯­
page.locale.ilokano.ph=ä¼è¯ºå¡æ´è¯­(è²å¾å®¾)
page.locale.ilokano=ä¼è¯ºå¡æ´è¯­
page.locale.indonesian.id=å°åº¦å°¼è¥¿äºè¯­(å°åº¦å°¼è¥¿äº)
page.locale.indonesian=å°åº¦å°¼è¥¿äºè¯­
page.locale.irish.ie=ç±å°å°è¯­(ç±å°å°)
page.locale.irish=ç±å°å°è¯­ 
page.locale.italian.ch=æå¤§å©è¯­(çå£«)
page.locale.italian.it=æå¤§å©è¯­(æå¤§å©)
page.locale.italian=æå¤§å©è¯­
page.locale.japanese.jp=æ¥è¯­(æ¥æ¬)
page.locale.japanese=æ¥è¯­
page.locale.korean.kr=é©è¯­(é©å½)
page.locale.korean=é©è¯­
page.locale.luxembourgish.lu=å¢æ£®å ¡è¯­(å¢æ£®å ¡)
page.locale.luxembourgish=å¢æ£®å ¡è¯­
page.locale.malay.bn=é©¬æ¥è¯­(æè±)
page.locale.malay.my=é©¬æ¥è¯­(é©¬æ¥è¥¿äº)
page.locale.malay=é©¬æ¥è¯­
page.locale.norwegian.nb=æªå¨è¯­(ä¼¯åæ¢å°)
page.locale.norwegian.nn=æªå¨è¯­(å°¼è¯ºæ¯å)
page.locale.norwegian=æªå¨è¯­
page.locale.polish.pl=æ³¢å°è¯­(æ³¢å°)
page.locale.polish=æ³¢å°è¯­
page.locale.portuguese.br=è¡èçè¯­(å·´è¥¿)
page.locale.portuguese.pt=è¡èçè¯­(è¡èç)
page.locale.portuguese=è¡èçè¯­
page.locale.punjabi.in=æé®æ®è¯­(å°åº¦)
page.locale.punjabi=æé®æ®è¯­
page.locale.romanian.ro=ç½é©¬å°¼äºè¯­(ç½é©¬å°¼äº)
page.locale.romanian=ç½é©¬å°¼äºè¯­
page.locale.russian.ru=ä¿è¯­(ä¿ç½æ¯)
page.locale.russian=ä¿è¯­
page.locale.scottish.gaelic.bg=èæ ¼å°çå°è¯­(è±å½)
page.locale.scottish.gaelic=èæ ¼å°çå°è¯­
page.locale.serbian.ba=å¡å°ç»´äºè¯­(æ³¢æ¯å°¼äº - æä¸)
page.locale.serbian.cs=å¡å°ç»´äºè¯­(é»å±±å±åå½ - æä¸)
page.locale.serbian.sp=å¡å°ç»´äºè¯­(å¡å°ç»´äº-æä¸)
page.locale.serbian=å¡å°ç»´äº
page.locale.slovak.sk=æ¯æ´ä¼åè¯­(æ¯æ´ä¼å)
page.locale.slovak=æ¯æ´ä¼åè¯­
page.locale.slovenian.si=æ¯æ´æå°¼äºè¯­(æ¯æ´æå°¼äº)
page.locale.slovenian=æ¯æ´æå°¼äºè¯­
page.locale.spanish.ar=è¥¿ç­çè¯­(é¿æ ¹å»·)
page.locale.spanish.bo=è¥¿ç­çè¯­(ç»å©ç»´äº)
page.locale.spanish.cl=è¥¿ç­çè¯­(æºå©)
page.locale.spanish.co=è¥¿ç­çè¯­(å¥ä¼¦æ¯äº)
page.locale.spanish.cr=è¥¿ç­çè¯­(å¥æ¯è¾¾é»å )
page.locale.spanish.do=è¥¿ç­çè¯­(å¤ç±³å°¼å å±åå½)
page.locale.spanish.ec=è¥¿ç­çè¯­(åçå¤å°)
page.locale.spanish.es=è¥¿ç­çè¯­(è¥¿ç­ç)
page.locale.spanish.gt=è¥¿ç­çè¯­(å±å°é©¬æ)
page.locale.spanish.hn=è¥¿ç­çè¯­(æ´ªé½ææ¯)
page.locale.spanish.mx=è¥¿ç­çè¯­(å¢¨è¥¿å¥)
page.locale.spanish.ni=è¥¿ç­çè¯­(å°¼å æç)
page.locale.spanish.pa=è¥¿ç­çè¯­(å·´æ¿é©¬)
page.locale.spanish.pe=è¥¿ç­çè¯­(ç§é²)
page.locale.spanish.pr=è¥¿ç­çè¯­(æ³¢å¤é»å)
page.locale.spanish.py=è¥¿ç­çè¯­(å·´æå­)
page.locale.spanish.sv=è¥¿ç­çè¯­(è¨å°ç¦å¤)
page.locale.spanish.us=è¥¿ç­çè¯­(ç¾å½)
page.locale.spanish.uy=è¥¿ç­çè¯­(ä¹æå­)
page.locale.spanish.ve=è¥¿ç­çè¯­(å§åçæ)
page.locale.spanish=è¥¿ç­çè¯­
page.locale.swahili.ke=æ¯ç¦è¥¿éè¯­(è¯å°¼äº)
page.locale.swahili.km=æ¯ç¦è¥¿éè¯­(ç§æ©ç½)
page.locale.swahili.tz=æ¯ç¦è¥¿éè¯­(å¦æ¡å°¼äº)
page.locale.swahili.ug=æ¯ç¦è¥¿éè¯­(ä¹å¹²è¾¾)
page.locale.swahili=æ¯ç¦è¥¿éè¯­
page.locale.swedish.fi=çå¸è¯­(è¬å°)
page.locale.swedish.se=çå¸è¯­(çå¸)
page.locale.swedish=çå¸è¯­
page.locale.tamil.in=æ³°ç±³å°è¯­(å°åº¦)
page.locale.tamil=æ³°ç±³å°è¯­
page.locale.thai.th=æ³°è¯­(æ³°å½)
page.locale.thai=æ³°è¯­
page.locale.ukrainian.ua=ä¹åå°è¯­(ä¹åå°)
page.locale.ukrainian=ä¹åå°è¯­
page.locale.vietnamese.vn=è¶åè¯­(è¶å)
page.locale.vietnamese=è¶åè¯­
page.locale.wallon.be=åéè¯­(æ¯å©æ¶)
page.locale.wallon.fr=åéè¯­(æ³å½)
page.locale.wallon=åéè¯­
page.message.messagecontent.history.title=æ¶æ¯åå®¹åå²è®°å½
page.option.select.data.source=-- éæ©æ°æ®æº
page.table.newpassword.information=æ°å»ºå¯ç ä¿¡æ¯
page.table.newuserid.information=æ°å»ºç¨æ·åä¿¡æ¯
page.table.newuseridpassword.information=æ°å»ºç¨æ·ååå¯ç ä¿¡æ¯
page.text.0.lowest.9999.highest=(æå°å¼ä¸º 0 : æå¤§å¼ä¸º 9999)
page.text.APPLICATION.SIGN.OUT=åºç¨ç¨åºéåº
page.text.CHILD.LINKED.MESSAGES=å­é¾æ¥æ¶æ¯
page.text.DEFAULT.SCHEDULE.LAYOUT=æ¥è§¦ç¹çé»è®¤æ¶é´è¡¨å¸å±
page.text.KEYWORDS=å³é®å­
page.text.NAME=åç§°
page.text.ON.HOLD=(æå)
page.text.OTHERWISE=å¦å
page.text.SEARCH=æç´¢
page.text.SHARED.FROM=å±äº«èª
page.text.SUPPRESSED=(ç¦æ­¢)
page.text.TIMING=è®¡æ¶
page.text.Unlimted=æ éå¶
page.text.activate.messages=è¦æ¿æ´»è¯¥æ¶æ¯å?
page.text.activate.selected.tags=æ¿æ´»æéæ è®°?
page.text.activate.smart.texts=è¦æ¿æ´»è¯¥æºè½ææ¬å?
page.text.activate.this.message=è¦æ¿æ´»è¿æ¡æ¶æ¯å?
page.text.activate.variants=è¦æ¿æ´»è¯¥åéå?
page.text.active.insert.schedules=æ´»å¨æå¥æ¶é´è¡¨
page.text.active.inserts=æ´»å¨æå¥
page.text.active.messages=æ´»å¨æ¶æ¯
page.text.active.tags=æ´»å¨æ è®°
page.text.add.insert.to.favorites.folder=å°æéçæå¥æ·»å å°æ¶èå¤¹?
page.text.add.inserts.to.favorites.folder=å°æéçæå¥æ·»å å°æ¶èæä»¶å¤¹?
page.text.add.or.import.touchpoint=ç³»ç»ä¸­ä¸å­å¨æ¥è§¦ç¹ãè¯·é¦åæ·»å æå¯¼å¥æ¥è§¦ç¹ã
page.text.add.rate.sheet.to.collection=å°è¯ä»·è¡¨æ·»å å°ä¸å½åéå®è¯ä»·è¡¨å³èçéå?
page.text.add.rate.sheets=æ·»å ä¸ä¸ªæå¤ä¸ªè¯ä»·è¡¨
page.text.add.section=æ·»å è
page.text.add.selection.under.selected.item=å¨éå®é¡¹ç®ä¸æ·»å æ°éæ©?
page.text.add.variant.data=æ·»å åéæ°æ®
page.text.add.variant.under.selected.item=å¨éå®é¡¹ç®ä¸æ·»å æ°åé?
page.text.advanced.content.search=é«çº§åå®¹æç´¢
page.text.all.but.first.line.indent=ç¬¬ä¸è¡é¤å¤çå¨é¨(æ¬æçç¼©è¿)
page.text.all.image.variants.have.data=æ¯å¦ææå¨æå¾ååéé½å·ææ°æ®éæ©å¨?
page.text.all.message.variants.have.data=æ¯å¦ææå¨ææ¶æ¯åéé½å·ææ°æ®éæ©å¨?
page.text.all.node.data.will.be.erased=éè¦æç¤º! å¦æå·²éæ©ä¸é¢çéé¡¹ï¼åå®ä¾çæ°æ®å°è¢«æ¸é¤ãæ­¤æä½æ æ³æ¤æ¶ã
page.text.all.of=ææ
page.text.all.primary.variables=ææä¸»åé(æ è®ºæ å°å¦ä½)
page.text.all.records.have.data.group=æ¯å¦æææ°æ®æºè®°å½é½å·²éæ©æ°æ®ç»?
page.text.all.records.have.starting.record=æ¯å¦ææå¼ç¨ç»é½å·²éæ©å¼å§è®°å½?
page.text.all.reference.variables=ææå¼ç¨åé(æ è®ºæ å°å¦ä½)
page.text.all.referenced.variables.have.data.element=æ¯å¦ææå¼ç¨çåéé½å·²éæ©æ°æ®åç´ ?
page.text.all.smart.text.variants.have.data=æ¯å¦ææå¨ææºè½ææ¬åéé½å·ææ°æ®éæ©å¨?
page.text.all.touchpoint.variants.have.data=æ¯å¦æææ¥è§¦ç¹ææ¬åéé½å·ææ°æ®éæ©å¨?
page.text.all.touchpoint.variants.have.visibility=æ¯å¦æææ¥è§¦ç¹åéé½è³å°å¯¹ä¸ä¸ªç¨æ·å·æå¯è§æ§?
page.text.all.used.primary.variables=å·²ä½¿ç¨çåæªæ å°çä¸»åé
page.text.all.used.reference.variables=æç¨çåæ²¡ææ å°çå¼ç¨åé
page.text.all.variant.content.will.be.replaced=æ³¨é: ç®æ åéçææåå®¹å°è¢«æ¿æ¢ã
page.text.all.zone.have.data.group=æ¯å¦ææåºåé½å·²éæ©æ°æ®ç»?
page.text.all.zone.have.visibility=æ¯å¦æææ´»å¨åºåé½è³å°å¯¹ä¸ä¸ªå·¥ä½ç»å·æå¯è§æ§?
page.text.all.zones=ææåºå
page.text.and.of.these.include=å¶ä¸­åæ¬
page.text.any.of=ä»»ä¸
page.text.applies.alternate.template=åºç¨æ¿ä»£æ¨¡æ¿
page.text.applies.template.modifiers=åºç¨æ¨¡æ¿ä¿®é¥°ç¬¦
page.text.apply.default=(åºç¨é»è®¤è®¾ç½®)
page.text.approval.from=æ¹åè
page.text.approval.required.from=æéæ¹åè
page.text.approval.timeframe.is=æ¹åæ¶é´èå´æ¯
page.text.approve.asset=æ¹åæ­¤èµäº§?
page.text.approve.insert.schedule=æ¹åæå¥æ¶é´è¡¨?
page.text.approve.insert=æ¹åæå¥?
page.text.approve.this.asset=è¦æ¹åæ­¤èµäº§å?
page.text.approve.this.image=æ¹åæ­¤å¾å?
page.text.archive.active.asset=æ¨å·²è¯·æ±å­æ¡£éå®çæ´»å¨èµäº§ãè¯·æ³¨æç¸å³å·¥ä½å¯æ¬ç±äºæ­¤æä½èä¿æä¸åã
page.text.archive.active.image=æ¨å·²è¯·æ±å­æ¡£éå®çæ´»å¨å¾åãè¯·æ³¨æç¸å³å·¥ä½å¯æ¬ç±äºæ­¤æä½èä¿æä¸åã
page.text.archive.active.smart.text=æ¨å·²è¯·æ±å­æ¡£éå®çæ´»å¨æºè½ææ¬ãè¯·æ³¨æç¸å³å·¥ä½å¯æ¬ç±äºæ­¤æä½èä¿æä¸åã
page.text.archive.message.active=æ¨å·²è¯·æ±å­æ¡£éå®çæ´»å¨èµäº§ãè¯·æ³¨æï¼å³èå·¥ä½å¯æ¬ä¸ä¼è¢«æ­¤æä½æ´æ¹ï¼å¹¶ä¸ä¸é¾æ¥çæ¶æ¯(å­çº§æç¶çº§)å³èçé¾æ¥å°è¢«å é¤ã
page.text.archive.selected.active.inserts=æ¨å·²è¯·æ±å­æ¡£éå®çæ´»å¨æå¥ã
page.text.archive.selected.active.messages=æ¨å·²è¯·æ±å­æ¡£éå®çæ´»å¨æ¶æ¯ãè¯·æ³¨æï¼å³èå·¥ä½å¯æ¬ä¸ä¼è¢«æ­¤æä½æ´æ¹ï¼å¹¶ä¸ä¸é¾æ¥çæ¶æ¯(å­çº§æç¶çº§)å³èçé¾æ¥å°è¢«å é¤ã
page.text.are.you.sure.you.want.to.delete.brach=æ¯å¦ç¡®å®è¦å é¤æ­¤å? æ­¤æä½æ æ³æ¤æ¶ã
page.text.are.you.sure=æ¯å¦ç¡®å®?
page.text.asked.to.discard.working.copy.with.no.active.copy=æ¨å·²è¦æ±æ¾å¼å·²éåéçå·¥ä½å¯æ¬ï¼æ­¤åéä¸å·å¤æ´»å¨å¯æ¬ã
page.text.asset=èµäº§
page.text.assign.a.insert.schedule.to.user=éæ©è¦åå¶åéæå¥æ¶é´è¡¨çç¨æ·ï¼å¹¶æä¾å¯¹æéæä½çè¯´æã
page.text.assign.deactivated.insert.schedule.to.user=éæ©è¦åå¶åéåç¨çæå¥æ¶é´è¡¨çç¨æ·ã
page.text.assign.deactivated.insert.schedule=éæ©è¦åå¶åéåç¨çæå¥æ¶é´è¡¨çç¨æ·ã
page.text.assign.insert.schedule.to.user=éæ©è¦åå¶åéæå¥æ¶é´è¡¨çç¨æ·ï¼å¹¶æä¾å¯¹æéæä½çè¯´æã
page.text.assign.insert.to.user=éæ©è¦åå¶åéæå¥çç¨æ·ï¼å¹¶æä¾å¯¹æéæä½çè¯´æã
page.text.assign.message.to.user=éæ©è¦åå¶åéæ­¤æ¶æ¯çç¨æ·ï¼å¹¶æä¾å¯¹æéæä½çè¯´æã
page.text.assign.tag.to.user=éæ©è¦åå¶åéæ è®°çç¨æ·ï¼å¹¶æä¾å¯¹æéæä½çè¯´æã
page.text.assign.user.to.tags=éæ©ç¨æ·ï¼å°æ è®°åéå°å¹¶ä¸æä¾æéæä½çè¯´æã
page.text.attachment.has.not.been.specified=å°æªæå®éä»¶
page.text.attachment.name.should.include.file.extension=å¨éç¨æ¶ï¼éä»¶åç§°åºåæ¬æä»¶æ©å±å [ä¾å¦ clientAttachment.pdf]
page.text.auto.approval.on.timeout=å½è¶æ¶æ¶èªå¨æ¹å
page.text.auto.approve.on.expiry=å¨å°ææ¶èªå¨æ¹å
page.text.bin.assignments.not.permitted.for.tenant.managed.schedule=ä¸åè®¸å¯¹ç§æ·ç®¡ççæ¶é´è¡¨è¿è¡ Bin åé
page.text.bins=Bin
page.text.by=ç±
page.text.cancel.simulations=æ¯å¦ç¡®å®è¦åæ¶éå®çæ¨¡æ?
page.text.channel.selection.required=éè¦è¿è¡éééæ©
page.text.clear.background.image=æ¸é¤èæ¯å¾å
page.text.click.bin.to.toggle.availability=(åå» Bin ä»¥åæ¢âå¯ç¨âç¶æ)
page.text.click.bin.to.toggle.tenant.access=(åå» Bin ä»¥åæ¢âç§æ·âè®¿é®)
page.text.click.button.to.add.items=åå»ä¸é¢çæé®æ¥æ·»å é¡¹ç®
page.text.click.button.to.add.steps=åå»ä¸é¢çæé®æ¥æ·»å æ­¥éª¤
page.text.click.here.small=åå»æ­¤å¤
page.text.click.here=åå»æ­¤å¤
page.text.click.to.view=åå»ä»¥æ¥ç
page.text.click=åå»
page.text.clone.communication=æ¯å¦è¦å¤å¶å½åéæ©çéä¿¡?
page.text.clone.selected.message=æ¯å¦è¦å¤å¶å½åéæ©çæ¶æ¯?
page.text.comments.and.suggestions.HP=è¯·éè¿çµå­é®ä»¶å°æ³¨éåå»ºè®®åéå° <a href=\"mailto:<EMAIL>\"><EMAIL></a>
page.text.comments.and.suggestions=è¯·éè¿çµå­é®ä»¶å°æ³¨éåå»ºè®®åéå° <a href=\"mailto:<EMAIL>\"><EMAIL></a>
page.text.communication.data.resource.selected=æ¯å¦å·²éæ©éä¿¡æ°æ®æº?
page.text.communications.which.are=éä¿¡ï¼å®ä»¬æ¯
page.text.composition.package.set.for.touchpoint=æ¯å¦å·²ä¸ºæ¥è§¦ç¹éæ©ç»åç¨åºå?
page.text.compound.smart.text=å¤åæºè½ææ¬
page.text.conditions.from.this.rule=å½åå»ºç®æ ç»åæ¶æ¥èªæ­¤è§åçç¶æã
page.text.conditions.must.pass.for.rule.to.qualify=å¶ä¸­è¿äºæ¡ä»¶å¿é¡»éè¿æ­¤è§åä»¥ç¬¦å
page.text.confirm.cancel=æ¯å¦ç¡®å®è¦åæ¶
page.text.confirm.delete=æ¯å¦ç¡®å®è¦å é¤
page.text.confirm.user.workgroup.change=å¦æå°éå®ç¨æ·ç§»å°è¯·æ±çå·¥ä½ç»ï¼å°ä½¿åå·¥ä½ç»æ²¡æç¨æ·ãè¯·ç¡®è®¤ã
page.text.connected.not.enabled=æ¥è§¦ç¹å°æªå¯ç¨ä»¥ä¾âè¿æ¥ä½¿ç¨âã
page.text.content.for=æ­¤é¡¹çåå®¹
page.text.content.suppressed=åå®¹å·²ç¦æ­¢
page.text.content.type=åå®¹ç±»å
page.text.content=åå®¹
page.text.copy.data.from.node.warning=éè¦æç¤º! å½åé«äº®æ¾ç¤ºçå®ä¾ä¸­çæææ°æ®å°è¢«å é¤ãæ­¤æä½æ æ³æ¤æ¶ã
page.text.copy.data.from.node=éæ©å°ä»ä¸­å¤å¶æ°æ®çå®ä¾ã
page.text.copy.images.to.path.location.specified=éè¦æç¤º! å¦æéæ©ä»¥ä¸éé¡¹ï¼åéå°å¾åå¤å¶å°æå®çè·¯å¾ä½ç½®ã
page.text.copyright.signin=MESSAGEPOINT<sup>Â®</sup> æ¯ PRINOVA INC çäº§åãä¿çæææå©Â© 2006-2014
page.text.copyright=çæææ 2006-2014 Prinova Incãä¿çæææå©ã
page.text.create.message.working.copy=æ¨å·²è¯·æ±åå»ºæ­¤èµäº§çå·¥ä½å¯æ¬ãè¯·æ³¨æï¼å³èçæ´»å¨æ¶æ¯ä¸ä¼è¢«æ­¤æä½æ´æ¹ã
page.text.create.variant.working.copies=åå»ºåéå·¥ä½å¯æ¬?
page.text.create.working.copies.of.selected.messages=æ¨å·²è¯·æ±åå»ºéå®æ¶æ¯çå·¥ä½å¯æ¬ãè¯·æ³¨æï¼å³èçæ´»å¨æ¶æ¯ä¸ä¼è¢«æ­¤æä½æ´æ¹ã
page.text.create.working.copies.of.selected.variants=æ¨å·²è¯·æ±åå»ºéå®åéçå·¥ä½å¯æ¬ãè¯·æ³¨æï¼å³èçæ¥è§¦ç¹ä¸ä¼è¢«æ­¤æä½æ´æ¹ã
page.text.create.working.copy.of.selected.asset=æ¨å·²è¯·æ±åå»ºéå®èµäº§çå·¥ä½å¯æ¬ãè¯·æ³¨æï¼å³èçæ´»å¨èµäº§ä¸ä¼è¢«æ­¤æä½æ´æ¹ã
page.text.create.working.copy.of.selected.image=æ¨å·²è¯·æ±åå»ºéå®å¾åçå·¥ä½å¯æ¬ãè¯·æ³¨æï¼å³èçæ´»å¨å¾åä¸ä¼è¢«æ­¤æä½æ´æ¹ã
page.text.create.working.copy.of.selected.images=æ¨å·²è¯·æ±åå»ºéå®å¾åçå·¥ä½å¯æ¬ãè¯·æ³¨æï¼å³èçæ´»å¨å¾åä¸ä¼è¢«æ­¤æä½æ´æ¹ã
page.text.create.working.copy.of.selected.smart.texts=æ¨å·²è¯·æ±åå»ºéå®æºè½ææ¬çå·¥ä½å¯æ¬ãè¯·æ³¨æï¼å³èçæ´»å¨æºè½ææ¬ä¸ä¼è¢«æ­¤æä½æ´æ¹ã
page.text.ctrl.click.to.select.multiple.values=(âCtrl+åå»âä»¥ä»åä¸çº§å«ä¸­éæ©å¤ä¸ªå¼)
page.text.currentlysignedinas=æ¨å½åå·²ç»å½ä¸º
page.text.custom=èªå®ä¹
page.text.customer_identifiers=å®¢æ·æ è¯ç¬¦
page.text.data.collection.configurated.for.touchpoint=æ¯å¦å·²ä¸ºæ¥è§¦ç¹éç½®æ°æ®éå?
page.text.data.file=æ°æ®æä»¶
page.text.data.source.association.required=å°æªä¸ºéå®æ¥è§¦ç¹å®ä¹æ°æ®éåã
page.text.days=å¤©
page.text.deactivate.selected.branch=éå®çåå°è¢«ç¦ç¨!
page.text.deactivate.selected.tags=ç¦ç¨éå®æ è®°?
page.text.deactivate.selected.user=ç¦ç¨éå®ç¨æ·?
page.text.default.content=é»è®¤åå®¹
page.text.default.schedule.layout.for.touchpoint=æ¥è§¦ç¹çé»è®¤æ¶é´è¡¨å¸å±
page.text.default.select.and.edit.to.override=é»è®¤: éæ©å¹¶ç¼è¾ä»¥è¦ç
page.text.default.task.list.empty=é»è®¤ä»»å¡åè¡¨ä¸ºç©ºææ æ³è®¿é®ï¼è¯·ä»åè¡¨èåä¸­éæ©å¦ä¸ä¸ªéé¡¹! å¦ææ¨æ æ³çå°åè¡¨èåï¼è¯·èç³»æ¨çç®¡çåï¼æå°è¯ç»å½å°å¶ä»å®ä¾ã
page.text.delete.archived.asset=æ¯å¦ç¡®å®è¦å é¤éå®çå·²å­æ¡£èµäº§? æ­¤æä½æ æ³æ¤æ¶ã
page.text.delete.archived.image=æ¯å¦ç¡®å®è¦å é¤éå®çå·²å­æ¡£å¾å? æ­¤æä½æ æ³æ¤æ¶ã
page.text.delete.archived.smart.text=æ¯å¦ç¡®å®è¦å é¤éå®çå·²å­æ¡£æºè½ææ¬? æ­¤æä½æ æ³æ¤æ¶ã
page.text.delete.db.schema=åæ¶å é¤ DB æ¶æ
page.text.delete.language=æ¯å¦å é¤éå®çè¯­è¨?
page.text.delete.message.archive=æ¯å¦ç¡®å®è¦å é¤éå®çå·²å­æ¡£èµäº§? æ­¤æä½æ æ³æ¤æ¶ã
page.text.delete.part=å é¤é¨ä»¶
page.text.delete.report=æ¯å¦ç¡®å®è¦å é¤æ­¤æ¥ååºæ¯?
page.text.delete.reports=æ¯å¦ç¡®å®è¦å é¤éå®çæ¥ååºæ¯?
page.text.delete.selected.archived.messages=æ¯å¦ç¡®å®è¦å é¤éå®çå·²å­æ¡£æ¶æ¯? æ­¤æä½æ æ³æ¤æ¶ã
page.text.delete.selected.communications=æ¯å¦ç¡®å®è¦å é¤éå®çéä¿¡? æ­¤æä½æ æ³æ¤æ¶ã
page.text.delete.selected.composition.packages=æ¯å¦å é¤éå®çç»åå?
page.text.delete.selected.constant=æ¯å¦ç¡®å®è¦å é¤éå®å¸¸é?
page.text.delete.selected.external.events=æ¯å¦ç¡®å®è¦å é¤éå®çå¤é¨äºä»¶?
page.text.delete.selected.inserts=æ¯å¦ç¡®å®è¦å é¤éå®çæå¥? æ­¤æä½æ æ³æ¤æ¶ã
page.text.delete.selected.language.variant=æ¯å¦ç¡®å®è¦å é¤éå®çè¯­è¨éæ©? æ­¤æä½æ æ³æ¤æ¶ã
page.text.delete.selected.licence=æ¯å¦ç¡®å®è¦å é¤éå®è®¸å¯è¯?
page.text.delete.selected.paragraph.style=æ¯å¦ç¡®å®è¦å é¤éå®çæ®µè½æ ·å¼?
page.text.delete.selected.pod=æ¯å¦ç¡®å®è¦å é¤éå®ç Pod?
page.text.delete.selected.touchpoint.collections=æ¯å¦å é¤éå®çæ¥è§¦ç¹éå?
page.text.delete.selected.variable=æ¯å¦ç¡®å®è¦å é¤éå®çåé?
page.text.delete.selected.variants=æ¯å¦ç¡®å®è¦å é¤éå®çåé? æ­¤æä½æ æ³æ¤æ¶ã
page.text.delete.simulations=æ¯å¦ç¡®å®è¦å é¤éå®çæ¨¡æ?
page.text.delete.targeting.rule=æ¨ç¡®å®æ¨è¦å é¤éå®çç®æ æå®è§åå?
page.text.delete.test=æ¯å¦ç¡®å®è¦å é¤æ­¤æµè¯åºæ¯?
page.text.delete.tests=æ¯å¦ç¡®å®è¦å é¤éå®çæµè¯åºæ¯?
page.text.delete.the.selected.variant=æ¯å¦ç¡®å®è¦å é¤éå®çåé? æ­¤æä½æ æ³æ¤æ¶ã
page.text.delete.this.insert=æ¯å¦ç¡®å®è¦å é¤æ­¤æå¥? æ­¤æä½æ æ³æ¤æ¶ã
page.text.delete.touchpoint.attachment=æ¯å¦ç¡®å®è¦å é¤æ­¤æ¥è§¦ç¹çéä»¶? æ­¤æä½æ æ³æ¤æ¶ã
page.text.delete.variant=å é¤åé? ææå³èçåå®¹å°ä¼ä¸¢å¤±ã
page.text.delete.zone=å é¤åºå
page.text.delivery.error.part1=æ­¤ä¼ éåçäºéè¯¯(è¿åä»£ç : 
page.text.delivery.error.part2=)ã<br />è¯·ç¨åéè¯...<br />
page.text.delivery=ä¼ é
page.text.discard.insert.schedule=æ¯å¦ç¡®å®è¦æ¾å¼éå®çæå¥æ¶é´è¡¨? æ­¤æä½æ æ³æ¤æ¶ã
page.text.discard.insert.schedules=æ¯å¦ç¡®å®è¦æ¾å¼éå®çæå¥æ¶é´è¡¨? æ­¤æä½æ æ³æ¤æ¶ã<br /><br />ç§æ·åä¸å°è¢«ç§»é¤ã
page.text.discard.message.working.copy=æ¨å·²è¯·æ±æ¾å¼æ¶æ¯çå·¥ä½å¯æ¬ãè¯·æ³¨ææ­¤æä½æ æ³æ¤æ¶ã
page.text.discard.rate.sheet=æ¾å¼è¯ä»·è¡¨?
page.text.discard.rate.sheets=æ¾å¼éå®çè¯ä»·è¡¨?
page.text.discard.selected.inserts=æ¯å¦ç¡®å®è¦æ¾å¼éå®çæå¥? æ­¤æä½æ æ³æ¤æ¶ã
page.text.discard.selected.tag.types=æ¯å¦æ¾å¼éå®çæ è®°ç±»å?
page.text.discard.this.insert=æ¯å¦ç¡®å®è¦æ¾å¼éæ­¤æå¥? æ­¤æä½æ æ³æ¤æ¶ã
page.text.discard.variant.working.copies.with.no.active.copy=æ¨å·²è¯·æ±æ¾å¼éå®åéçå·¥ä½å¯æ¬ï¼æ­¤åéæ²¡ææ´»å¨å¯æ¬ã
page.text.discard.variant.working.copies=æ¨å·²è¯·æ±æ¾å¼éå®åéçå·¥ä½å¯æ¬ãæ¯å¦ç¡®å®è¦ç»§ç»­?
page.text.discard.working.copy.of.asset=æ¨å·²è¯·æ±æ¾å¼èµäº§çå·¥ä½å¯æ¬ãè¯·æ³¨ææ­¤æä½æ æ³æ¤æ¶ã
page.text.discard.working.copy.of.image=æ¨å·²è¯·æ±æ¾å¼å¾åçå·¥ä½å¯æ¬ãè¯·æ³¨ææ­¤æä½æ æ³æ¤æ¶ã
page.text.discard.working.copy.of.smart.text=æ¨å·²è¯·æ±æ¾å¼æºè½ææ¬çå·¥ä½å¯æ¬ãè¯·æ³¨ææ­¤æä½æ æ³æ¤æ¶ã
page.text.discontinue.rate.sheet=ä¸­æ­è¯ä»·è¡¨? [ææ°è¯ä»·è¡¨å°è®¾ç½®ä¸ºèªä¸ä¸æ¥ä»¥æ¥çç»ææ¶é´]
page.text.discontinue.rate.sheets=ä¸­æ­¢æéçè¯ä»·è¡¨? [æ¯ä¸ªç³»åä¸­ææ°çè¯ä»·è¡¨å°è®¾ç½®ä¸ºèªæ¨æ¥æ¥æä»¥æ¥çç»ææ¶é´]
page.text.disqualify=åæ¶èµæ ¼
page.text.do.you.want.to.delete.branch=æ¨å·²è¯·æ±å®å¨å é¤éå®åãæ­¤æä½æ æ³æ¤æ¶ã
page.text.do.you.want.to.delete.node=æ¯å¦ç¡®å®è¦å é¤æ­¤å®ä¾?
page.text.does.not.contain.any.data=æªåå«ä»»ä½æ°æ®æä¸å­å¨
page.text.double.click.insert.to.jump.to.page.view=(åå»âæå¥âå¾æ å°è·³è½¬è³ç¸å³è§å¾é¡µé¢)
page.text.drag.and.drop.rule.into.target.group=åå»è§åæå°è§åææ¾è³æ­¤é¢æ¿ä»¥çææ¨çç®æ ç»ã
page.text.drag.and.drop.target.group.to.extend.targeting=å°ç®æ ç»ææ¾è³æ­¤é¢æ¿ä¸ï¼ä»¥æ©å±æ­¤èµäº§çç®æ æå®æ¡ä»¶ã
page.text.drag.insert.to.or.from.bin=(å°âæå¥âæå¥æé Bin æä»å¶ä¸­æåº)
page.text.draw.parts=ç»å¾é¨å
page.text.draw.zone=ç»å¾åºå
page.text.dynamic.smart.text=å¨ææºè½ææ¬
page.text.edit.content.for=ç¼è¾æ­¤é¡¹çåå®¹:
page.text.edit.zone=ç¼è¾åºå
page.text.email.server.settings.configured=æ¯å¦å·²éç½®çµå­é®ä»¶æå¡å¨è®¾ç½®?
page.text.empty.value=ä¿çä¸ºç©º
page.text.empty=ç©º
page.text.enabled=æ¯å¦å·²å¯ç¨?
page.text.ending=ç»æ
page.text.erase.db.schema=åå»ºæéå»º(å¦æå·²å­å¨) DB æ¶æ
page.text.error.message=åçäºä¸ä¸ªé®é¢ï¼å¹¶ä¸å·²å°éè¯¯è®°å½å°æ¥å¿ãå¦æé®é¢ç»§ç»­å­å¨ï¼è¯·èç³»æ¨çç®¡çå
page.text.error=éè¯¯
page.text.exact.target.settings.configured=æ¯å¦å·²éç½®åç¡®çç®æ è®¾ç½®?
page.text.example.end.date.time=2013 å¹´ 10 æ 21 æ¥ä¸å 10 ç¹
page.text.example.end.date=ææä¸ï¼10 æ 21 æ¥
page.text.example.end.time=ä¸å 10 ç¹
page.text.example.start.date.time=2013 å¹´ 10 æ 21 æ¥ä¸å 10 ç¹
page.text.example.start.date=ææä¸ï¼10 æ 21 æ¥
page.text.example.start.time=ä¸å 10 ç¹
page.text.example.time.delta=2:45
page.text.example.time.zone=(EST)
page.text.exclude.groups=æé¤ç»
page.text.existing.assignment=ç°æä»»å¡
page.text.existing.modifiers.detected=æ£æµå°çç°æä¿®é¥°ç¬¦
page.text.existing.modifiers.not.detect.and.will.be.treated.as.removed=æ æ³å¨ä¸ä¼ çåä¸­æ£æµå°ç°æä¿®é¥°ç¬¦ï¼è¿äºä¿®é¥°ç¬¦å°è¢«è§ä¸ºå·²å é¤ãå°å¨ Messagepoint ä¸­ç¦ç¨ä»¥ä¸ä¿®é¥°ç¬¦:
page.text.existing.zones.detected=æ£æµå°çç°æåºå
page.text.existing.zones.not.detect.and.will.be.treated.as.removed=æ æ³å¨ä¸ä¼ çåä¸­æ£æµå°ç°æåºåï¼è¿äºåºåå°è¢«è§ä¸ºå·²å é¤ãå°å¨ Messagepoint ä¸­ç¦ç¨ä»¥ä¸åºå:
page.text.export.complete=å¯¼åºå®æãè¯·åå»âä¿å­âä»¥æ£ç´¢ç»æ
page.text.export.error=å¯¼åºéè¯¯!
page.text.export.image.path=ä»å¯¼åºå¾åè·¯å¾
page.text.exported.simulation=å·²å¯¼åºæ¨¡æ
page.text.exporting.simulation.please.wait=æ­£å¨å¯¼åºæ¨¡æï¼è¯·ç¨å...
page.text.file.package.upload.for=æä»¶åä¸ä¼ 
page.text.first.line.indent=é¦è¡(ç¼©è¿)
page.text.fixed=åºå®
page.text.fixed_record_count=åºå®è®°å½æ°
page.text.flexible.touchpoints.do.not.permit.optional.delivery=çµæ´»æ¥è§¦ç¹ä¸åè®¸æ§è¡å¯éçä¸»æ¶æ¯ä¼ éæä½ã
page.text.for.sheets.greater.than=FOR SHEETS > THAN
page.text.for.upload=éå¯¹ä¸ä¼ 
page.text.for.you=éå¯¹æ¨
page.text.for=éå¯¹
page.text.forgotyourpassword=å¿è®°å¯ç ?
page.text.freeform.canvas=èªç±ç»å¶ç»å¸
page.text.generating.export.data=æ­£å¨çæå¯¼åºæ°æ®ï¼è¯·ç¨å...
page.text.has.completed.click.close=å·²ç»å®æãè¯·åå»âå³é­âä»¥ç»§ç»­ã
page.text.has.multiple.values.comma=å·æå¤ä¸ªå¼ï¼
page.text.here=æ­¤å¤
page.text.hours=å°æ¶
page.text.if.timing.or.targeting.of.asset.does.not.apply=å¦ææªå¯¹æ¶ä»¶äººåºç¨æ­¤èµäº§çæå®ç®æ ææ§è¡æ¶é´ï¼ååå«æ­¤èµäº§çæ¶æ¯ä¼ææ ·?
page.text.images.which.are=å¾åï¼å®ä»¬æ¯
page.text.import.is.complete=å¯¼å¥å®æ
page.text.importing.please.wait=æ­£å¨å¯¼å¥...è¯·ç¨åã
page.text.in.priority.order=(æä¼åçº§é¡ºåº)
page.text.in.process=æ­£å¨è¿è¡
page.text.inactive.insert.schedules=éæ´»å¨æå¥æ¶é´è¡¨
page.text.inactive.inserts=éæ´»å¨æå¥
page.text.inactive.tags=éæ´»å¨æ ç­¾
page.text.include.active.only=ä»åæ¬æ´»å¨é¡¹
page.text.include.groups=åæ¬ç»
page.text.include.messages.activated.after=ä»åæ¬å¨æ­¤æ¶é´åä¹åæ¿æ´»çæ¶æ¯
page.text.incomplete.rate.sheet.coverage=ä¸å®æ´çè¯ä»·è¡¨è¦ç: æ²¡æä¸ºæ­¤æå¥æ¶é´è¡¨çææ®µå®ä¹è¯ä»·è¡¨éåã
page.text.indirect.references=é´æ¥å¼ç¨
page.text.inherited.from=å·²ç»§æ¿èª
page.text.insert.not.referenced=æå¥æªå¼ç¨
page.text.insert.schedules=æå¥æ¶é´è¡¨
page.text.language.selector.not.defined.for.touchpoint=å°æªä¸ºæ­¤æ¥è§¦ç¹å®ä¹è¯­è¨éæ©å¨ãè¯·å¨æ¥è§¦ç¹è®¾ç½®ä¸­æå®éæ©å¨ã
page.text.less.than=å°äº
page.text.limited=æé
page.text.link.to.download.touchpoint.will.popup=å°å¨ä¸ä¸ªåç¬å¼¹åºçªå£ä¸­æä¾ç¨äºä¸è½½å¯¼åºç Touchpoint XML çé¾æ¥ã
page.text.loading.communication.data=æ­£å¨å è½½éä¿¡æ°æ®...
page.text.loading=æ­£å¨å è½½...
page.text.locked.referenced.target.group=å·²éå®: å¼ç¨çç®æ ç»
page.text.logged.out=æ¨ç°å¨å·²æ³¨éã
page.text.mark.tenant.schedule.complete=æ¯å¦å°æ­¤ç§æ·æ¶é´è¡¨æ è®°ä¸ºå®æ?
page.text.message.containing.smart.text.will.only.play.if=ä»å¨æ­¤âæºè½ææ¬âçç®æ æå®åæ§è¡æ¶é´éç¨çæåµä¸ï¼æä¼æ¾ç¤ºä¸æ¡åå«æ­¤âæºè½ææ¬âçæ¶æ¯ã
page.text.message.inherited.targeting.from.link=æ­¤æ¶æ¯ç»§æ¿ç®æ æå®èª
page.text.message.inherited.timing.from.link=æ­¤æ¶æ¯ç»§æ¿æ§è¡æ¶é´èª
page.text.message.now.used.in.production=æ­¤æ¶æ¯ç°å¨å°å¨åéè¢«æ¿æ´»æ¶ç¨äºçäº§ã
page.text.message.report.tool=æ¶æ¯æ¥åå·¥å·
page.text.messagepoint.tm=Messagepoint <sup>Â®</sup>
page.text.messages.which.are=æ¶æ¯ï¼å®ä»¬æ¯
page.text.messages.with=æ¶æ¯ï¼å·æ
page.text.migrate.folder.locations.for.node=è¿ç§»éå®å®ä¾çæä»¶å¤¹ä½ç½®ã
page.text.modify.language.for.selected.variant=ä¸ºæéè¯­è¨é¡¹ä¿®æ¹è¯­è¨?
page.text.move.resize.part=ç§»å¨é¨å/è°æ´é¨åå¤§å°
page.text.move.resize.zone=ç§»å¨åºå/è°æ´åºåå¤§å°
page.text.move.zone.to.section=å°åºåç§»å¨å°è
page.text.multipart.no.categories=ç³»ç»ä¸­å½åæªå®ä¹å¤é¨ååå®¹ç±»å«ã
page.text.multipart.zone.repeats=ä¸é¢çå¤é¨ååºåéå¤
page.text.multiple=å¤
page.text.my.filter.does.not.apply.to.active.communications=âMyâç­éå¨ä¸éç¨äºæ´»å¨çéä¿¡ã
page.text.my.messages.filter.does.not.apply=âMyâæ¶æ¯ç­éå¨ä¸éç¨äºæ´»å¨çæå·²å­æ¡£çæ¶æ¯ã
page.text.my.variants.filter.does.not.apply=âMyâåéç­éå¨ä¸éç¨äºæ´»å¨åéã
page.text.new.modifiers.detected=æ£æµå°çæ°ä¿®é¥°ç¬¦
page.text.new.name=æ°åç§°
page.text.new.part=æ°å»ºé¨å
page.text.new.zone=æ°å»ºåºå
page.text.new.zones.detected=æ£æµå°çæ°åºå
page.text.newpassword.action=åºéæ©ä¸ä¸ªæ°å¯ç æ¥æ»¡è¶³ç³»ç»ä¸­å®ä¹çå½åå¯ç è¦æ±ã
page.text.newpassword.consequence1=å¯¹å¯ç çæ´æ°å°ç«å³çæã
page.text.newpassword.consequence2=å¦æä¸æ´æ°å¯ç ï¼åå°æ æ³ç»å½å° Messagepointã
page.text.newpassword.instructions=è¯·éè¿æ­¤é¡µé¢åºé¨çç¸åºæé®ç¡®è®¤æåæ¶å¯ç æ´æ°ã
page.text.newuserid.action=åºéæ©ä¸ä¸ªæ°ç¨æ·åä»¥æ»¡è¶³ç³»ç»ä¸­å®ä¹çå½åç¨æ·åè¦æ±ã
page.text.newuserid.consequence1=å¯¹ç¨æ·åçæ´æ°å°ç«å³çæã
page.text.newuserid.consequence2=å¦æä¸æ´æ°ç¨æ·åï¼åå°æ æ³ç»å½å° Messagepointã
page.text.newuserid.instructions=è¯·éè¿æ­¤é¡µé¢åºé¨çç¸åºæé®ç¡®è®¤æåæ¶ç¨æ·åæ´æ°ã
page.text.newuseridpassword.action=åºéæ©æ°ç¨æ·ååæ°å¯ç ä»¥æ»¡è¶³ç³»ç»ä¸­å®ä¹çå½åç¨æ·ååå¯ç è¦æ±ã
page.text.newuseridpassword.consequence1=å¯¹ç¨æ·ååå¯ç çæ´æ°å°ç«å³çæã
page.text.newuseridpassword.consequence2=å¦æä¸æ´æ°ç¨æ·ååå¯ç ï¼åå°æ æ³ç»å½å° Messagepointã
page.text.newuseridpassword.instructions=è¯·éè¿æ­¤é¡µé¢åºé¨çç¸åºæé®ç¡®è®¤æåæ¶ç¨æ·ååå¯ç æ´æ°ã
page.text.next.section=ä¸ä¸è
page.text.no.active.content=æ æ´»å¨åå®¹ã
page.text.no.active.messages=å½åæ²¡ææ´»å¨ç¶æçæ¶æ¯ã
page.text.no.alternate.templates=æªä¸ä¼ æ¨¡æ¿ãè¯·åå» \"+\" æé®æ·»å æ¨¡æ¿å®ä¹ã
page.text.no.applicable.inserts.for.touchpoint=æ²¡æä¸ºå³èçæ¥è§¦ç¹å®ä¹éå½çæå¥ã
page.text.no.assignable.user=æ²¡æå¯æå®çç¨æ·
page.text.no.associations=æ å³è
page.text.no.auxiliary.data.connections=å°æªå®ä¹è¿æ¥ãè¯·åå» '+' å¾æ å¼å§æ·»å è¿æ¥ã 
page.text.no.available.content=æ²¡ç¨å¯ç¨åå®¹ã
page.text.no.available.instance.to.copy.from=æ²¡æå¯ä»å¶ä¸­å¤å¶æ°æ®çå®ä¾
page.text.no.available.text.styles=æ²¡æå¯ç¨ææ¬æ ·å¼
page.text.no.available.users=æ²¡æå¯ç¨ç¨æ·
page.text.no.available.workgroups=æ²¡æå¯ç¨å·¥ä½ç»
page.text.no.bins.specified=æªæå® Binï¼è¯·è®¾ç½®æéç Bin æ°ç®
page.text.no.child.tags.or.attributes.for.tag=æ²¡ææ­¤æ ç­¾çå­æ ç­¾æå±æ§ã
page.text.no.composition.package.uploaded=å°æªä¸ä¼ ç»ååã
page.text.no.composition.packages.for.touchpoint.collection=æ²¡æç¨äºæ¥è§¦ç¹éåçç»åå
page.text.no.composition.packages.for.touchpoint=æ²¡æç¨äºæ¥è§¦ç¹çç»åå
page.text.no.content.libraries.qualified.for.link=æ²¡ææ´»å¨çå¾ååºèµäº§
page.text.no.content.selector=æ åå®¹éæ©å¨
page.text.no.data.definitions.shared=å°æªå±äº«æ°æ®å®ä¹ã
page.text.no.data.files.list=æ²¡ææ°æ®æä»¶
page.text.no.data.files=æ²¡ææ­¤è¯­è¨çæ°æ®æä»¶
page.text.no.data.resources=æ²¡ææ°æ®èµæº
page.text.no.data.source.associations=æ²¡ææ°æ®é
page.text.no.data.sources=æªæ¾å°æ°æ®æºã
page.text.no.default.content=æ²¡æé»è®¤åå®¹
page.text.no.deliveries=æ²¡æè¦æ¥åçä¼ é
page.text.no.domains.to.map=æ²¡æè¦æ å°çå
page.text.no.external.data.captured=æ²¡ææ­£å¨æè·ä»¥ç¨äºæ¥åçå¤é¨æ°æ®
page.text.no.history.items=æªæ¾å°åå²è®°å½é¡¹ç®ã
page.text.no.image=æ å¾å
page.text.no.images.select.file.to.upload=æ²¡æå¾å: è¯·éæ©ä¸ä¸ªæä»¶ä»¥è¿è¡ä¸ä¼ 
page.text.no.inserts.assigned=å°æªæå®æå¥ã
page.text.no.inserts.report=æ²¡æè¦æ¥åçæå¥
page.text.no.inserts.selected=å°æªéæ©æå¥ã
page.text.no.inserts.with.optional.delivery=å¨å½åçæå¥åéä¸­æ²¡æå·æâå¯éâä¼ éç¶æçæå¥ã
page.text.no.licences.list=æ²¡æè®¸å¯è¯
page.text.no.locked.users=å½åç³»ç»ä¸­æ¯å¦æ²¡æè¢«éå®çç¨æ·?
page.text.no.log.messages=æ²¡ææ¥å¿æ¶æ¯
page.text.no.mandatory.messages=æ²¡æå¼ºå¶æ§æ¶æ¯è¢«ä¼ éå°æ­¤åºåã
page.text.no.matches.found=æªæ¾å°å¹éé¡¹ã
page.text.no.matching.inserts=æ²¡æä¸å½åéæ©æ¡ä»¶å¹éçæå¥ã
page.text.no.matching.items=æ²¡æä¸å½åéæ©æ¡ä»¶å¹éçé¡¹ç®ã
page.text.no.matching.language.selections=æ²¡æä¸å½åéæ©æ¡ä»¶å¹éçè¯­è¨éæ©ã
page.text.no.matching.ratesheets=ç³»ç»ä¸­æªå®ä¹è¯ä»·è¡¨
page.text.no.matching.tags=æ²¡æä¸å½åéæ©æ¡ä»¶å¹éçæ ç­¾ã
page.text.no.matching.variants=æªæ¾å°å¹éåéã
page.text.no.menu.value.items=å°æªä¸ºæ­¤èåå®ä¹é¡¹ç®ãè¯·åå» '+' å¾æ å¼å§æ·»å é¡¹ç®ã
page.text.no.messages.for.collection=æ²¡æéç¨äºéå®éåçæ¶æ¯
page.text.no.messages.for.touchpoint=æ²¡æéç¨äºéå®æ¥è§¦ç¹çæ¶æ¯
page.text.no.messages.qualified.for.link=æ²¡æç¬¦åé¾æ¥è¦æ±çæ¶æ¯
page.text.no.messages.qualified=æ²¡æç¬¦åæè¯·æ±çæ¨¡æçæ¶æ¯ã
page.text.no.messages.report=æ²¡æè¦æ¥åçæ¶æ¯ã
page.text.no.nodes.defined.for.branch=æ²¡æä¸ºæ­¤åå®ä¹å®ä¾ãè¯·è®¿é®æä½èåä»¥åå»ºå®ä¾ã
page.text.no.optional.messages=æ²¡æä¼ éå°æ­¤åºåçå¯éæ¶æ¯ã
page.text.no.override=ä½¿ç¨é»è®¤è®¾ç½®
page.text.no.para.styles=æªå®ä¹æ®µè½æ ·å¼
page.text.no.paragraph.styles=æ²¡æå¯ç¨çæ®µè½æ ·å¼
page.text.no.primary.data.source=æ²¡æä¸»æ°æ®æº
page.text.no.rate.sheets.for.insert.schedule=å°æªä¸ºæ­¤æå¥æ¶é´è¡¨éæ©è¯ä»·è¡¨ã
page.text.no.rate.sheets=æ²¡æè¯ä»·è¡¨
page.text.no.records.defined=æªå®ä¹è®°å½
page.text.no.reference.data.source=æ²¡æå¯ç¨çå¼ç¨æ°æ®æº
page.text.no.restriction=æ éå¶
page.text.no.restrictions=æ éå¶æ¡ä»¶
page.text.no.selector.definition.set.for.insert.schedule=å°æªä¸ºæ­¤æå¥æ¶é´è¡¨çå³èâæ¥è§¦ç¹âè®¾ç½®éæ©å¨å®ä¹ã
page.text.no.selector.values.defined.for.insert.schedule=å°æªä¸ºæ­¤æå¥æ¶é´è¡¨å®ä¹éæ©å¨å¼ã
page.text.no.selector.values.defined.for.touchpoint.variant=å°æªä¸ºæ­¤æ¥è§¦ç¹åéå®ä¹éæ©å¨å¼ã
page.text.no.selectors.for.language.variant=å°æªä¸ºæ­¤è¯­è¨åéå®ä¹éæ©å¨å¼ã
page.text.no.selectors=æ éæ©å¨
page.text.no.tags.defined=æªå®ä¹æ ç­¾
page.text.no.tasks=æ²¡æä¸å½åéæ©æ¡ä»¶å¹éçä»»å¡ã
page.text.no.template.zones.detected=æªæ£æµå°æ­¤çµå­é®ä»¶æ¨¡æ¿çåºåã
page.text.no.text.styles=æªå®ä¹ææ¬æ ·å¼
page.text.no.timing.for.inserts.in.schedule=å°æªä¸ºæ­¤æ¶é´è¡¨çæå¥æå®æ§è¡æ¶é´ã
page.text.no.timing=æ æ§è¡æ¶é´
page.text.no.touchpoint.collections.list=æªå®ä¹âæ¥è§¦ç¹éåâãè¯·åå»âæ·»å éåâã
page.text.no.touchpoints.available.to.you=æ²¡æå¯¹æ¨å¯ç¨çæ¥è§¦ç¹ãè¯·èç³»ç®¡çäººåæ¥è°æ´å¯è§æ§ï¼æéç½®æ¨çç¬¬ä¸ä¸ªæ¥è§¦ç¹ã
page.text.no.touchpoints.matching.search=æ²¡æä¸æç´¢æ¡ä»¶å¹éçæ¥è§¦ç¹ã
page.text.no.variable.brackets=[æ²¡æåé]
page.text.no.variables=æ åé
page.text.no.visible.connected.zones=å°æªä¸ºæ­¤æ¥è§¦ç¹éç½®å¯è§è¿æ¥åºåã
page.text.no.visible.inserts.for.touchpoint=æ²¡æä¸å·²éæ¥è§¦ç¹ç¸å³çå¯è§æ´»å¨æå¥
page.text.no.visible.mandatory.inserts.associated.with.touchpoint=æ²¡æä¸éå®æ¥è§¦ç¹å³èçå¯è§å¼ºå¶æ§æå¥
page.text.no.visible.messages.for.touchpoint=æ²¡æä¸éå®æ¥è§¦ç¹å³èçå¯è§ä¼ éæ¶æ¯
page.text.no.visible.optional.insert.associated.with.touchpoint=æ²¡æä¸éå®æ¥è§¦ç¹å³èçå¯è§å¯éæå¥
page.text.no.workflow.data.to.display=è¯¥èµäº§å¤äºå·¥ä½å¯æ¬ç¶æï¼å æ­¤å°æ å¯æ¾ç¤ºçä»»ä½å·¥ä½æµæ°æ®ã
page.text.no.workgroups.defined=æªå®ä¹å·¥ä½ç»
page.text.no.working.copies.selected.for.test=æ²¡æä¸ºæ­¤æµè¯éæ©å·¥ä½å¯æ¬
page.text.no.zone.associations.defined.for.this.workgroup=æ²¡æä¸ºæ­¤å·¥ä½ç»å®ä¹åºåå³èã
page.text.none.please.select=æ ãè¯·éæ© --->
page.text.none=æ ...
page.text.note.required.brackets=[éè¦æ³¨é]
page.text.note.required.to.reject.brackets=[éè¦æç»çæ³¨é]
page.text.of.these.target.groups.must.pass=(å±äºè¿äºç®æ ç»)å¿é¡»ä¼ éè³
page.text.of.these.users=è¿äºç¨æ·ç
page.text.on.hold.messages.not.used.in.production=å½åéæ¿æ´»æ¶ï¼ä¸ä¼å¨çäº§ä¸­ä½¿ç¨âæåâæ¶æ¯ã
page.text.one.of=ä¹ä¸
page.text.only.one.language.associated.with.touchpoint=ä»ä¸ç§è¯­è¨ä¸æ­¤æ¥è§¦ç¹ç¸å³èãææåå®¹å°è¢«è§ä¸º
page.text.open.in.new.window=å¨æ°çªå£ä¸­æå¼
page.text.or.drag.and.drop.images=æææ¾å¾å
page.text.order.entry.item.is.primary.driver=ä½¿ç¨è¾å¥ä½ä¸ºä¸»é©±å¨ç¨åºå¼ä»¥ç¨äºéä¿¡
page.text.other.messages=å¶ä»æ¶æ¯
page.text.otherwise=å¦å
page.text.override.value=æ°å»ºåå®¹
page.text.page.weights=é¡µé¢æé
page.text.pagenotfound=æ±æ­!
page.text.pagenotfounddescription=æ¨å·²è®¿é®ä¸åå­å¨çé¡µé¢ã
page.text.parameter.noparametergroups=ç³»ç»ä¸­æªå®ä¹åå®¹éæ©å¨ç»
page.text.parameter.noparameters=ç³»ç»ä¸­æ²¡æå®ä¹åå®¹éæ©å¨
page.text.password.reset.selected.user=éç½®éå®ç¨æ·çå¯ç ?
page.text.passwordreset.confirmation=æ¨å°æ¶å°ä¸å°çµå­é®ä»¶ï¼ä»¥å¸®å©æ¨å¿«ééç½®å¯ç ãè¯·åå»<a href=\"./signin.jsp?tenant=Provider\">æ­¤å¤</a>è¿åç»å½é¡µé¢ã
page.text.passwordreset=å¿è®°æ¨çå¯ç ? è¦éç½®å¯ç ï¼è¯·æä¾æ¨çå¸æ·æä½¿ç¨ççµå­é®ä»¶å°åãå°å¾å¿«åæ­¤é®ç®±å°ååéä¸å°çµå­é®ä»¶ï¼å¶ä¸­æä¾ä¸ä¸ªé¾æ¥ä»¥ä¾éç½®æ¨çå¯ç ã
page.text.pending.approval.from=ç­å¾æ¹å
page.text.permitted.characters=åè®¸å¨æ­¤è¾å¥ä¸­åå«ä»¥ä¸å­ç¬¦
page.text.playtoallcustomers=æªè®¾å®ç®æ  - å°éåäºæææ¶ä»¶äºº
page.text.please.contact.messagepoint=è¯·èç³» Messagepoint
page.text.prepopulate.optional=é¢å®è£(å¯é)
page.text.preview=é¢è§
page.text.previous.section=ä¸ä¸è
page.text.primary=ä¸»è¦
page.text.privacy=éç§
page.text.proof.selected.communication=æ¯å¦è¦éªè¯å½åéå®çéä¿¡?
page.text.proof=è¯æ
page.text.provide.description.of.action.required=æä¾å¯¹æéæä½çè¯´æã
page.text.provider.insert.schedules=æä¾ç¨åºæå¥æ¶é´è¡¨
page.text.qualify=åæ ¼
page.text.quick.brown.fox=ææ·çæ£æ¯çç¸ä»æçèº«ä¸è·è¿ã
page.text.range_to=è³
page.text.rate.sheet.coverage=è¯ä»·è¡¨è¦çèå´
page.text.rate.sheet.covers.period.of.insert.schedule=æ­¤è¯ä»·è¡¨è¦çä¸ºæ­¤æå¥æ¶é´è¡¨æå®çæ´ä¸ªå¨æ
page.text.rate.sheet.transition=è¯ä»·è¡¨è½¬æ¢: å¨æ­¤æå¥æ¶é´è¡¨çæ¶é´æ®µåï¼å°å¨ä¸¤ä¸ªè¯ä»·è¡¨ä¹é´è¿è¡è½¬æ¢ã
page.text.reassign.smart.text.to.user=éæ©è¦åå¶åéæ­¤æºè½ææ¬çç¨æ·ï¼å¹¶æä¾å¯¹æéæä½çè¯´æã
page.text.record.contains.no.elements=è®°å½ä¸­ä¸åå«ä»»ä½åç´ 
page.text.record_types=è®°å½ç±»å
page.text.refresh.segmentation.analysis=æ¯å¦å·æ°åæ®µåæ?
page.text.release.for.tenant.usage.before.overrides=å¨è®¿é®éå¼æ¿ä»£ååå¸ä»¥ä¾ç§æ·ä½¿ç¨
page.text.release.for.tenant.usage.for.timing=å¨æ¥çç§æ·æå¥æ§è¡æ¶é´ååå¸ä»¥ä¾ç§æ·ä½¿ç¨
page.text.release.insert.schedule.for.tenant.usage=æ¯å¦åå¸æå¥æ¶é´è¡¨ä»¥ä¾ç§æ·ä½¿ç¨? ç§æ·å°å¯ä»¥å¨æä½æ¶é´æé´ç®¡çæå¥ã
page.text.release.insert.schedule.for.use=æ¯å¦åå¸æå¥æ¶é´è¡¨ä»¥ä¾ä½¿ç¨?
page.text.release.tenant.usage.before.bin.assignment=å¨æ¥çç§æ· bin ä»»å¡ååå¸ä»¥ä¾ç§æ·ä½¿ç¨
page.text.remaining=å©ä½
page.text.remember.my.username=è®°ä½æçç¨æ·å
page.text.remote.server.settings.configured=æ¯å¦å·²éç½®è¿ç¨æå¡å¨è®¾ç½®?
page.text.remove.alternate.template=ç§»é¤æ¿æ¢æ¨¡æ¿
page.text.remove.insert.from.favorites.folder=ä»æ¶èæä»¶å¤¹ä¸­ç§»é¤éå®çæå¥?
page.text.remove.inserts.from.favorites.folder=ä»æ¶èæä»¶å¤¹ä¸­ç§»é¤éå®çæå¥?
page.text.remove.paragraph.style.customization=ç§»é¤éå®çæ®µè½æ ·å¼èªå®ä¹?
page.text.remove.style.customization=ç§»é¤éå®çææ¬æ ·å¼èªå®ä¹?
page.text.remove.variant.data=ç§»é¤åéæ°æ®
page.text.removed.selected.variant=æ¨å·²è¯·æ±å®å¨å é¤éå®åéãæ­¤æä½æ æ³æ¤æ¶ã
page.text.rename.selected.branch=æ¯å¦éå½åå½åéå®çå?
page.text.rename.selected.selection=æ¯å¦éå½åå½åéå®çéæ©?
page.text.rename.selected.variant=æ¯å¦éå½åå½åéå®çåé?
page.text.rename.this.asset=æ¯å¦éå½åè¯¥èµäº§?
page.text.reopen.insert.selection.window=ä¸æ¹ååºçç§æ·å·²ç»å®ææå¥éæ©ãè¯·éæ©è¦ä¸ºå¶éæ°æå¼æå¥éæ©çªå£çç§æ·ã
page.text.repeats.annually.brackets=(æ¯å¹´éå¤)
page.text.replacement=æ¿æ¢
page.text.report.being.prepared=æ­£å¨åå¤æ¥å...
page.text.report.loads.in.separate.window=æ­¤æ¥åå°è½½å¥ä¸ä¸ªåç¬çå¼¹åºçªå£ã
page.text.reports.which.are=æ¥åï¼å®ä»¬æ¯
page.text.requested.HTML.file.of.type=è¯·æ±ç HTML æä»¶ç±»å
page.text.requires.approval.from=éè¦è·åæ¹åèª
page.text.reservation=ä¿ç
page.text.reservations=ä¿ç
page.text.reset.default.header.logo=æ¯å¦ç¡®å®è¦éç½®é»è®¤æ å¤´å¾½æ ?
page.text.reset.default.provider.logo=æ¯å¦ç¡®å®è¦éç½®é»è®¤æä¾åå¾½æ ?
page.text.restore.message=æ¯å¦è¦æ¢å¤éå®æ¶æ¯? è¿äºæ¶æ¯å°åæ¬¡åå«å¨çäº§ä¸­ã
page.text.restore.selected.messages=æ¯å¦è¦æ¢å¤éå®æ¶æ¯? è¿äºæ¶æ¯å°åæ¬¡åå«å¨çäº§ä¸­ã
page.text.restricted.access.note=æ¨çå¸æ·å·æ Messagepoint çåéè®¿é®æéãè¯·èç³»æ¨çç®¡çåã
page.text.restricted.access=éå¶è®¿é®
page.text.review.touchpoint.data.collection=è¯·æ£æ¥æ­¤æ¥è§¦ç¹çæ°æ®éåã
page.text.role.activate.consequence1=æ¿æ´»è§è²å°åè®¸ä»»ä½è¢«åéæ­¤è§è²çæ´»å¨ç¨æ·ç»å½å° Messagepointã
page.text.role.activate.consequence2=å¯å¨ä»¥åéè¿åç¨è§è²æ¥æ¤æ¶æ­¤æä½ã
page.text.role.activate.instructions=è¯·åå»âç»§ç»­âä»¥æ¿æ´»è§è²ï¼æåå»âåæ¶âä»¥åæ¶æä½ã
page.text.role.deactivate.consequence1=åç¨è§è²å°ä¼é»æ­¢ä»»ä½è¢«åéæ­¤è§è²çæ´»å¨ç¨æ·ç»å½ Messagepointã
page.text.role.deactivate.consequence2=å¯å¨ä»¥åéè¿æ¿æ´»è§è²æ¥æ¤æ¶æ­¤æä½ã
page.text.role.deactivate.instructions=è¯·åå»âç»§ç»­âä»¥åç¨ï¼æåå»âåæ¶âä»¥åæ¶æä½ã
page.text.role.delete.consequence1=å é¤è§è²å°ä¼ä» Messagepoint å é¤æ­¤è§è²çä»»ä½è®°å½ã
page.text.role.delete.consequence2=å é¤æä½å°ä¸å¯æ¤æ¶ã
page.text.role.delete.instructions=è¯·æ£æ¥ä¸æ¹ä¿¡æ¯ï¼å¹¶éè¿é¡µé¢åºé¨çç¸åºæé®æ¥ç»§ç»­æä½æåæ¶è§è²å é¤ã
page.text.role.noroles=ç³»ç»ä¸­æªå®ä¹è§è²
page.text.rule.is.parameterized=æ­¤åæ°åè§åæ­£å¨ä½¿ç¨ä¸­ï¼æ æ³ä¿®æ¹ã
page.text.rules.must.pass.for.target.group.to.qualify=è¿äºè§åå¿é¡»ä¼ éå°æ­¤ç®æ ç»ä»¥ä½¿ä¹åæ ¼
page.text.same.as.content=ä¸æ­¤ç¸åçåå®¹
page.text.same.as=ç­åäº
page.text.scan.domains=æ«æææç°æåã
page.text.scenarios.changed=èªä¸æ¬¡æµè¯æ§è¡ä»¥æ¥ï¼æ­¤æµè¯æå¢ä¸­çæäºéå®å·¥ä½å¯æ¬æ¶æ¯å·²æ¿æ´»æå·²ä¸¢å¼ãè¯·ä½¿ç¨æéå·¥ä½å¯æ¬æ¶æ¯æ¥æ´æ°æå¢ä»¥æ§è¡æµè¯ã
page.text.schedule.not.released.for.tenant.usage.threshold.override.not.available=æ­¤æ¶é´è¡¨å°æªåå¸ä¾ç§æ·ä½¿ç¨ãåªæå¨åå¸åï¼æè½æå®éå¼æ¿ä»£ã
page.text.search.for.rules=æç´¢è§å
page.text.search.for.target.groups=æç´¢ç®æ ç»
page.text.select.a.user.to.assign.asset.to=éæ©è¦åå¶åéèµäº§çç¨æ·ã
page.text.select.a=éæ©ä¸ä¸ª
page.text.select.and.add.content.selector=éæ©å¹¶æ·»å åå®¹éæ©å¨
page.text.select.composition.package=éæ©ç»åå
page.text.select.content.library=éæ©è¦åºç¨çå¾å
page.text.select.content.type.for.message=éæ©æ°æ¶æ¯çåå®¹ç±»åã
page.text.select.data.range.for.batch.window=éæ©æ¹çªå£çæ¥æèå´ãæ­¤èå´åçææéä¿¡å°åå«å¨æ¹çªå£ä¸­ã
page.text.select.data.to.remove.from.content.selection=éæ©è¦ä»åå®¹åéæ°æ®å®ä¹ä¸­å é¤çä¸ä¸ªæå¤ä¸ªéæ©æ°æ®å¼ã
page.text.select.data.to.remove.from.variant=éæ©è¦ä»åå®¹åéæ°æ®å®ä¹ä¸­å é¤çæ°æ®ã
page.text.select.date=éè¿åå»æ¥åå¾æ æ¥éæ©æ¥æãæç©ºæ ¼é®ãå é¤é®åéæ ¼é®å°å é¤éå®çæ¥æã
page.text.select.default.language=æ¯å¦è¦ä½¿éå®è¯­è¨æä¸ºé»è®¤è¯­è¨?
page.text.select.default.paragraph.style=éæ©é»è®¤çæ®µè½æ ·å¼
page.text.select.default.text.style=éæ©é»è®¤çææ¬æ ·å¼
page.text.select.from.existing.radio.groups=ä»ç°æåéç»ä¸­éæ©
page.text.select.image.to.upload=åå»âéæ©å¾åâå¼å§å¾åä¸ä¼ è¿ç¨ã
page.text.select.instance=éæ©å®ä¾
page.text.select.message=éæ©æ¶æ¯
page.text.select.no.default.style=-- æ é»è®¤å¼ --
page.text.select.resource=éæ©èµæº
page.text.select.server=éæ©æå¡å¨
page.text.select.shared.data.definition=éæ©è¦ä¸æ°åéå³èçå±äº«æ°æ®å®ä¹ã
page.text.select.shared.data.for.content.selection=éæ©è¦ä¸æ°åéå³èçå±äº«åéæ°æ®å®ä¹ã
page.text.select.target.message.for.linking=éæ©è¦é¾æ¥å°çæ¶æ¯ãå°ä»ç®æ æ¶æ¯ä¸­å¼ç¨æ§è¡æ¶é´åç®æ éå½ãå½åæ¶æ¯ä¸çç°ææ§è¡æ¶é´ææå®ç®æ å°è¢«å é¤ã
page.text.select.target.variant.for.message.clone=éæ©è¦åå¶åéæ¶æ¯çåéã
page.text.select.template.package.for.touchpoint=éæ©ä»£è¡¨æ­¤æ¥è§¦ç¹çæ¨¡æ¿å
page.text.select.tenant=è¯·éæ©ç§æ·
page.text.select.time.range.for.variant.version.history=éæ©å½åæ¥è§¦ç¹åéççæ¬åå²è®°å½çæ¶é´èå´ï¼ç¶åç¹å»âç»§ç»­âã
page.text.select.time.range.for.version.history=éæ©å½åèµäº§ççæ¬åå²è®°å½çæ¶é´èå´ï¼ç¶åç¹å»âç»§ç»­âã
page.text.select.touchpoint.collection=è¯·éæ©éå
page.text.select.touchpoint=è¯·éæ©æ¥è§¦ç¹
page.text.select.user.for.image.reassign=éæ©è¦åå¶åéæ­¤å¾åçç¨æ·ï¼å¹¶æä¾å¯¹æéæä½çè¯´æã
page.text.select.user.to.assign.asset.to=éæ©è¦åå¶åéæ­¤èµäº§çç¨æ·ï¼å¹¶æä¾å¯¹æéæä½çè¯´æã
page.text.select.user.to.assign.asset=éæ©è¦åå¶åéæ­¤èµäº§çç¨æ·ã
page.text.select.user.to.assign.deactivated.insert.to=éæ©è¦åå¶åéæ­¤å·²åç¨æå¥çç¨æ·ã
page.text.select.user.to.assign.insert.to=éæ©è¦åå¶åéæå¥çç¨æ·ï¼å¹¶æä¾å¯¹æéæä½çè¯´æã
page.text.select.user.to.assign.reinstated.insert.to=éæ©è¦åå¶åéå·²æ¢å¤æå¥çç¨æ·ï¼å¹¶æä¾å¯¹æéæä½çè¯´æã
page.text.select.user=éæ©ç¨æ·
page.text.select.variable=éæ©åé
page.text.select.variables.to.define.compound.key=éæ©ä¸ä¸ªæå¤ä¸ªåéä»¥å®ä¹å¤åå¯é¥ã
page.text.select.variant.to.copy.content.to=éæ©è¦åå¶å¤å¶åå®¹çåéã
page.text.select.zone.to.add.message=éæ©è¦æ·»å æ¶æ¯çåºå
page.text.select.zone=åºåéæ©
page.text.selected.messages.now.used.in.production=è¿äºæ¶æ¯ç°å¨å°å¨åéè¢«æ¿æ´»æ¶ç¨äºçäº§ã
page.text.selected.variant.will.be.deleted=éå®çåéå°è¢«å é¤ã
page.text.selected.variants.will.be.deleted=éå®çåéå°è¢«å é¤ã
page.text.selector.definition.not.set.for.touchpoint.variant=å°æªä¸ºæ­¤åéçå³èæ¥è§¦ç¹è®¾ç½®éæ©å¨å®ä¹ã
page.text.selector.no.set.for.language.variant=å°æªä¸ºæ­¤åéçå³èè¯­è¨è®¾ç½®éæ©å¨å®ä¹ã
page.text.sessiontimeout=ä¼è¯è¶æ¶ã
page.text.sessiontimeoutdescription.signin=åºäºå®å¨åå ï¼ç±äºå¤äºä¸æ´»å¨ç¶æï¼æ¨å·²æ³¨ééåºã
page.text.sessiontimeoutdescription=åºäºå®å¨åå ï¼ç±äºå¤äºä¸æ´»å¨ç¶æï¼æ¨å·²æ³¨ééåºãæ¨å¯è½å¯ä»¥éè¿è¿åç»å½é¡µé¢æ¥ç»§ç»­ä½¿ç¨ Messagepointã
page.text.set.cell.border.all=è®¾ç½®ææååæ ¼è¾¹æ¡
page.text.set.cell.border.bottom=è®¾ç½®ååæ ¼åºè¾¹æ¡
page.text.set.cell.border.left=è®¾ç½®ååæ ¼å·¦è¾¹æ¡
page.text.set.cell.border.right=è®¾ç½®ååæ ¼å³è¾¹æ¡
page.text.set.cell.border.top=è®¾ç½®ååæ ¼é¡¶è¾¹æ¡
page.text.set.options.for.audit.report=ä¸ºè¯·æ±çå®¡æ ¸æ¥åè®¾ç½®æééé¡¹ã
page.text.set.table.border.all=è®¾ç½®ææè¡¨æ ¼è¾¹æ¡
page.text.set.table.border.outer=è®¾ç½®å¤é¨è¡¨æ ¼è¾¹æ¡
page.text.share.image.to.touchpoints=å¨ä»¥ä¸æ¥è§¦ç¹ä¸­å±äº«æ­¤å¾å
page.text.share.object.across.touchpoints=å¨ä»¥ä¸æ¥è§¦ç¹ä¸­å±äº«æ­¤æºè½ææ¬å¯¹è±¡
page.text.sign.in.to.transition=ç»å½é¢åå¸çæ¬
page.text.signoutsuccess=æ¨å·²ç»æåæ³¨ééåº Messagepoint!
page.text.simulation.for=æ¨¡æ
page.text.simulations.which.are=æ¨¡æï¼å®ä»¬æ¯
page.text.single.selector.touchpoint.return.to.master=æ­¤æ¥è§¦ç¹éç¨äºåä¸ªåééæ©å¨ãå æ­¤ï¼æ­¤æ¥è§¦ç¹çåéå¯è½ä¸éç¨äºå­åéãè¯·è¿åä¸»è§å¾ä»¥ç»§ç»­æ§è¡åéç®¡çæèåæ¢è³å½ååéçæ¶æ¯è§å¾ã
page.text.smart.text.which.are=æºè½ææ¬ï¼å¶
page.text.specify.data.for.content.selection=æå®è¦æ·»å å°åå®¹åéæ°æ®å®ä¹ä¸­çåéæ°æ®ã
page.text.specify.name.and.channel.for.touchpoint=æå®åç§°å¹¶ä¸ºæ¥è§¦ç¹éæ©æ¸ éåè¿æ¥å¨
page.text.specify.name.and.data.for.content.variant=æå®ä¸æ°åéå³èçåç§°ååéæ°æ®ã
page.text.specify.name.for.external.event=ä¸ºå¤é¨äºä»¶æå®åç§°åéæ©ç±»åã
page.text.specify.new.variant.name=ä¸ºåéæå®æ°åç§°
page.text.specify.variant.data=æå®è¦æ·»å å°åå®¹åéæ°æ®å®ä¹ä¸­çæ°æ®ã
page.text.specify.variant.name.and.data=æå®ä¸æ°åéå³èçåç§°åæ°æ®ã
page.text.starting=å¯å¨
page.text.support.dot=æ¯æã
page.text.suppress.message=æ¯å¦è¦ç¦æ­¢éå®æ¶æ¯? å°ä»çäº§ä¸­æé¤ç¦æ­¢çæ¶æ¯ã
page.text.suppress.production=æ¯å¦ç¦æ­¢çäº§?
page.text.suppress.selected.messages=æ¯å¦è¦ç¦æ­¢éå®æ¶æ¯? å°ä»çäº§ä¸­æé¤ç¦æ­¢çæ¶æ¯ã
page.text.suppressed=å·²ç¦æ­¢
page.text.system.maintenance.default=Messagepoint ç½ç«å°å¨ START_DATE è¿è¡å®æç»´æ¤(çº¦ä» START_TIME å° END_TIME TIME_ZONE)ãå¨æ­¤æé´ï¼ç«ç¹çä¸äºèå¯è½ä¸å¯ç¨ãæè°¢æ¨èå¿ç­å¾ã
page.text.system.upgrade.did.not.finish=ç³»ç»æ´æ°æªå®æ
page.text.target.group.is.parameterized=æ­¤åæ°åç®æ ç»æ­£å¨ä½¿ç¨ä¸­ï¼æ æ³ä¿®æ¹ã
page.text.targeting.search.and=ä¸
page.text.targeting.search.empty.or.null=ç©ºæ Null
page.text.targeting.search.equals=ç¸ç­
page.text.targeting.search.greater.than.or.equal=å¤§äºæç­äº
page.text.targeting.search.greater.than=å¤§äº
page.text.targeting.search.less.than.or.equal=å°äºæç­äº
page.text.targeting.search.less.than=å°äº
page.text.targeting.search.not.empty.or.null=éç©ºæ Null
page.text.targeting.search.not.equal=ä¸ç­
page.text.targeting.search.user.specified=ç¨æ·æå®
page.text.template.contains.zones.with.no.connector.name=æ­¤æ¨¡æ¿åå«æªæå®ææè¿æ¥å¨åç§°çåºåãè¯·ç¡®è®¤ææåºåé½åå«ææ 'alt' å±æ§ã
page.text.template.does.not.specify.all.zone.dimensions=æ­¤æ¨¡æ¿åå«æªæå®ææç»´åº¦å±æ§çåºåãè¯·ç¡®è®¤ææåºåé½åå«ææç 'height' å 'width'å±æ§ã
page.text.template.file.DAT.for.upload=ç¨äºä¸ä¼ çæ¨¡æ¿æä»¶(DAT)
page.text.template.package.uploaded=æ¯å¦å·²ç»ä¸ä¼ æ¨¡æ¿å?
page.text.template.package=æ¨¡æ¿å
page.text.template=æ¨¡æ¿
page.text.tenant.activation.instruction=è¦æ¿æ´»æ­¤ç§æ·ï¼å¿é¡»é¦åæ§è¡ä»¥ä¸ä¸¤ä¸ªæä½ä¹ä¸:<br><br>1) åç¨æ´»å¨çç§æ·ã<br>2) èç³»æ¨ç Messagepoint <sup>TM</sup> æå¡ä»£è¡¨ä»¥è·åå¶ä»ç§æ·ç <br>    è®¸å¯è¯ã<br><br>è¯·åå»ä¸æ¹çâç»§ç»­âæé®ä»¥è¿åæ¨ç Messagepoint <sup>TM</sup> ä¸»é¡µã
page.text.tenant.bin.assignments=ç§æ· Bin åé
page.text.tenant.inserts.timeframe=(ä¾ç§æ·æå®æå¥çæ¶é´èå´)
page.text.tenant.limit.reached.part1=æ¨å·²å°è¯æ¿æ´»ç§æ·ãæ¨å·²è¾¾å°è®¸å¯çæå¤§å¼
page.text.tenant.limit.reached.part2=æ¿æ´»ç§æ·ã
page.text.tenant.limit.reached.part3=æ¨å·²å°è¯æ¿æ´»ç§æ·ãå·²è¾¾å°æå¤§æ´»å¨ç§æ·æ°ã
page.text.tenant.participation.will.also.be.removed.for.schedule=ç§æ·åä¸ä¹å°è¢«å é¤ãå³èçç§æ·å°ä¸åè½å¤æ¥çæåä¸æ­¤æ¶é´è¡¨ã
page.text.tenant.schedules=ç§æ·æ¶é´è¡¨
page.text.tenant.thresholds.same.as.default=ç§æ·éå¼ä¸é»è®¤å®ä¹ç¸å
page.text.tenantconfirm.action.activate=æ¨å·²è¯·æ±æ¿æ´»ä¸æ¹æè¿°çç§æ·ã
page.text.tenantconfirm.action.deactivate=æ¨å·²è¯·æ±åç¨ä¸æ¹æè¿°ççç§æ·ã
page.text.tenantconfirm.consequence.activate1=æ¿æ´»ç§æ·å°ä¼åè®¸ä»»ä½æ´»å¨ç¨æ·ç»å½ Messagepointã
page.text.tenantconfirm.consequence.activate2=å¯å¨ä»¥åéè¿åç¨æ­¤ç§æ·æ¥æ¤æ¶æ¿æ´»æä½ã
page.text.tenantconfirm.consequence.activate3=æ¿æ´»æ­¤ç§æ·åï¼æ¨å°å·æ {0} ä¸ªæ´»å¨ç§æ·ãæ¨å½åè¢«è®¸å¯çæå¤§æ´»å¨ç§æ·æ°æ¯ {1} ä¸ªã
page.text.tenantconfirm.consequence.deactivate1=åç¨ç§æ·å°ä¼é»æ­¢ä»»ä½æ´»å¨ç¨æ·ç»å½ Messagepointã
page.text.tenantconfirm.consequence.deactivate2=å¯å¨ä»¥åéè¿æ¿æ´»æ­¤ç§æ·æ¥æ¤æ¶åç¨æä½ã
page.text.tenantconfirm.instruction.activate=è¯·æ£æ¥ä¸æ¹ä¿¡æ¯ï¼å¹¶éè¿é¡µé¢åºé¨çç¸åºæé®æ¥ç¡®è®¤æåæ¶ç§æ·æ¿æ´»ã
page.text.tenantconfirm.instruction.deactivate=è¯·æ£æ¥ä¸æ¹ä¿¡æ¯ï¼å¹¶éè¿é¡µé¢åºé¨çç¸åºæé®æ¥ç¡®è®¤æåæ¶ç§æ·åç¨ã
page.text.test.of=æµè¯
page.text.tests.which.are=æµè¯ï¼å®ä»¬æ¯
page.text.thank.you=æè°¢æ¨ä½¿ç¨ Messagepointã
page.text.the.following=ä»¥ä¸
page.text.there.are.no.variables=æ²¡æåé
page.text.threshold.overrides.for=æ­¤é¡¹çéå¼æ¿ä»£
page.text.thresholds=éå¼
page.text.timing.no.end.date=æ ç»ææ¥æ
page.text.timing.starts.now=ç«å³å¯å¨
page.text.to.begin=ä»¥å¼å§
page.text.to.request.a.new.one=è¯·æ±æ°é¡¹ã
page.text.toggle.advanced.search.to.include.markup=æç¤º: åæ¢é«çº§æç´¢ï¼ä»¥å°åå®¹æ è®°æ ç­¾åæ¬å¨æ¨çæç´¢ä¸­ã
page.text.toggle.background.images.panel=åæ¢èæ¯å¾åé¢æ¿
page.text.toggle.delivery.panel=åæ¢ä¼ éé¢æ¿
page.text.toggle.touchpoint.assignment.panel=åæ¢æ¥è§¦ç¹åéé¢æ¿
page.text.toreturntohomepage=è¿åè³æ¨çé»è®¤ Messagepoint ä¸»é¡µã
page.text.toreturntosigninpage=ä»¥éæ°ç»å½åºç¨ç¨åºã
page.text.tosignout=ä»¥æ³¨ééåºã
page.text.touchpoint.context.not.apply.for.proofing.data=å½åæ¥è§¦ç¹ç¯å¢ä¸éç¨äºææ ·æ°æ®ã
page.text.touchpoint.does.not.apply.for.communications.workflow=å½åæ¥è§¦ç¹ç¯å¢ä¸éç¨äºéä¿¡å·¥ä½æµã
page.text.touchpoint.does.not.apply.for.order.entry=å½åæ¥è§¦ç¹ç¯å¢ä¸éç¨äºè®¢åè¾å¥ã
page.text.touchpoint.does.not.apply.for.variant.workflow=å½åæ¥è§¦ç¹ç¯å¢ä¸éç¨äºåéå·¥ä½æµã
page.text.touchpoint.has.no.order.entry.items=æ­¤æ¥è§¦ç¹å½åæ²¡æè®¢åè¾å¥é¡¹ã
page.text.touchpoint.variants.have.proofing.data=æ¯å¦å·²ä¸ºæææ¥è§¦ç¹åééç½®ææ ·æ°æ®?
page.text.transparent.background=æ¯å¦ä¸ºéæèæ¯?
page.text.treat.qualified.messages=å°åæ ¼æ¶æ¯è§ä¸ºå·²ä¼ éæ¶æ¯
page.text.turn.node.off.line=éå®å®ä¾å°è±æº!
page.text.turn.node.online=æ¯å¦è¦ä½¿æ­¤å®ä¾è½¬ä¸ºå¨çº¿?
page.text.type.content.you.are.searching.for=å¨ä¸æ¹è¾å¥æ§ä»¶ä¸­é®å¥æ¨è¦æç´¢çåå®¹ï¼å¹¶åå»âæç´¢âã
page.text.unlimited=æ éå¶
page.text.update.data.collection.of.touchpoint.in.packaged.collection=æ­¤æ¥è§¦ç¹æ¯æåéåçä¸é¨åãå¦æä¿®æ¹å³èæ°æ®éåï¼ä¹å°ä¼æ´æ°ä»¥ä¸æ¥è§¦ç¹:
page.text.upload.backgrounds=ä¸ä¼ èæ¯
page.text.uploading.image=æ­£å¨ä¸ä¼ å¾å...
page.text.user.specified.values=ç¨æ·æå®çå¼?
page.text.username.change.notification=ç¨æ·ååæ´éç¥ã
page.text.username.has.been.changed=æ¨çç¨æ·åå·²å¨ Messagepoint(tm) åºç¨ç¨åºä¸è¢«æ´æ¹ã\n\n\næ°ç¨æ·å: {0}\næ§ç¨æ·å: {1}
page.text.users.may.select=ç¨æ·å¯éæ©
page.text.validate.selected.node=éªè¯éå®å®ä¾ç DB æ¶æã
page.text.validator.alphabetic=æå­æ¯é¡ºåº
page.text.validator.alphanum.at.dot.dash.underscore.apos=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãat ç¬¦ãç¹ãæå·
page.text.validator.alphanum.dash.space=å­æ¯æ°å­ãç ´æå·ãç©ºæ ¼
page.text.validator.alphanum.dash.underscore.apos=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãæå·\t
page.text.validator.alphanum.dash.underscore=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·
page.text.validator.alphanum.dot.dash.backslash.slash.colon.space=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãç¹ãåææ ãæ­£ææ ãåå·ãç©ºæ ¼
page.text.validator.alphanum.dot.dash.backslash.slash.space=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãç¹ãåææ ãæ­£ææ ãç©ºæ ¼
page.text.validator.alphanum.dot.dash.underscore=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãç¹
page.text.validator.alphanum.dot.slash=å­æ¯æ°å­ãç¹ãæ­£ææ 
page.text.validator.alphanum.parenth.dash.space=å­æ¯æ°å­ãæ¬å·ãç ´æå·ãç©ºæ ¼
page.text.validator.alphanum.space.dash.underscore.apos=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãæå·ãç©ºæ ¼
page.text.validator.alphanum.space.dash.underscore=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãç©ºæ ¼
page.text.validator.alphanum.space.dot.dash.comma.semi.underscore=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãç¹ãéå·ãåå·ãç©ºæ ¼
page.text.validator.alphanum.space.dot.dash.underscore.apos=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãç¹ãæå·ãç©ºæ ¼
page.text.validator.alphanum.space.dot.dash.underscore=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãç¹ãç©ºæ ¼
page.text.validator.alphanum.symbols.space=å­æ¯æ°å­ãå¤§é¨åç¬¦å·ãç©ºæ ¼
page.text.validator.alphanum.symbols=å­æ¯æ°å­ãå¤§é¨åç¬¦å·
page.text.validator.alphanum.underscore.dash.apos.dot.comma.colon.at.numbersign.square.space=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãæå·ãç¹ãéå·ãåå·ãç¬¦å·ãæ°å­ç¬¦å·ãæ¹æ¬å·ãç©ºæ ¼
page.text.validator.alphanum.underscore.dash.apos.parenth.space=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãæå·ãå·¦å³æ¬å·ãç©ºæ ¼
page.text.validator.alphanum.underscore.dash.numbersign.space=å­æ¯æ°å­ãä¸åçº¿ãç ´æå·ãæ°å­ç¬¦å·ãç©ºæ ¼
page.text.validator.alphanum=å­æ¯æ°å­
page.text.validator.any.character=è¿äºå¼çä»»æå­ç¬¦
page.text.validator.numeric=æ°å¼
page.text.values.must.meet.criteria.for.condition=é½å¿é¡»æ»¡è¶³æ­¤ç¯å¢çæ¡ä»¶ä»¥è¿è¡ä¼ é
page.text.variant.has.no.working.copy=æ­¤åéæ²¡æå·¥ä½å¯æ¬ãè¯·åå»ºä¸ä¸ªå·¥ä½å¯æ¬ä»¥å¼å§ç¼è¾åå®¹ã
page.text.variant.is.not.active=æ­¤åéä¸ä¸ºæ´»å¨ç¶æãå¿é¡»å¨åå®¹åä¸ºæ´»å¨ç¶æåè¿è¡æ¿æ´»/æ¹åã
page.text.variants.for.each.touchpoint.language=æ¯ç§æ¥è§¦ç¹è¯­è¨çåéæ¯å¦å·²åå»ºå¹¶å·æéæ©å¨æ°æ®?
page.text.variants.which.are=åéï¼å®ä»¬æ¯
page.text.view.dynamic.content=æ¥çå¨æåå®¹
page.text.view.fullscreen=æ¥çå¨å±å¹
page.text.view.references.by=æ¥çå¼ç¨
page.text.view.variant.data=æ¥çåéæ°æ®
page.text.view=æ¥ç
page.text.warning.email.touchpoint.contains.template.package.match.with.export=è­¦å! æ­¤çµå­é®ä»¶æ¥è§¦ç¹åå«ä¸ä¸ªæ¨¡æ¿åãæ¨å°éè¦ä¸è½½æ­¤æ¨¡æ¿åå¹¶å°å¶ä¸å¯¼åºæä»¶ä¿å­èµ·æ¥ä»¥è¿è¡å°æ¥çå¯¼å¥ã
page.text.warning.no.data.collection.for.touchpoint=è­¦å! æ­¤æ¥è§¦ç¹æ²¡ææ°æ®éåã
page.text.warning.no.template.package.for.touchpoint=è­¦å! æ­¤çµå­é®ä»¶/ç½ç»æ¥è§¦ç¹å°æªåå«ä¸ä¸ªæ¨¡æ¿åã
page.text.warning.upmapped.reference.variables.for.touchpoint=è­¦å! æ­¤æ¥è§¦ç¹å·ææªè¢«æ å°çå¼ç¨åéä¸å¶ä¸­ä¸ä¸ªæå¤ä¸ªæ æ³æ å°ã
page.text.warning.upmapped.variables.for.touchpoint=è­¦å! æ­¤æ¥è§¦ç¹ææªè¢«æ å°çå·ææ°æ®åç´ çåéã
page.text.warning=è­¦å!
page.text.what.is.customer.identifier.for.communication=æ­¤éä¿¡çå®¢æ·è¯å«ç¬¦æ¯ä»ä¹?
page.text.when.greater.than=å½å¤§äº
page.text.when.selected.comma=å½éä¸­åï¼
page.text.workflow.has.no.steps=æ­¤å·¥ä½æµå½åæ²¡ææ­¥éª¤ãä½¿ç¨æ­¤å·¥ä½æµçé¡¹ç®å¯ä»¥éè¿æå®çç¨æ·è¢«ç´æ¥æ¿æ´»ãå°ä¸éè¦æ¹åã
page.text.workflow.owners.may.approve.step=å·¥ä½æµææèå¯è½ä¼æ¹åä¸ä¸ªæ­¥éª¤ãæ¨æ¯å¦å¸ææ¹åæ­¤èµäº§?
page.text.workflow.owners.may.reject.step=å·¥ä½æµææèå¯è½ä¼æç»æ¹åä¸ä¸ªèµäº§ãæ¨æ¯å¦å¸ææç»æ­¤èµäº§?
page.text.workflow.step.must.contain.at.least.one.approver=æ¯ä¸ä¸ªå·¥ä½æµæ­¥éª¤åå«è³å°ä¸ä¸ªæ¹åèãè¯·å°ä¸ä¸ªæå¤ä¸ªç¨æ·ä»æ¹åèåè¡¨ä¸­æå¨å°æ­¤æ­¥éª¤ä¸­ãè¯·æ³¨æ: æ­¤å·¥ä½æµææèä¸è½ä¸ºä¸ä¸ªæ¹åèã
page.text.working.copies.associated.with.test.have.changed=æ­¤æµè¯æå¢ä¸çæäºéå®çå·¥ä½å¯æ¬æ¶æ¯å·²ç»è¢«æ¿æ´»æå·²ä»ä¸æ¬¡æµè¯æ§è¡åè¢«åºå¼ãè¯·æâç»§ç»­âä»¥æ´æ°æå¢ä½¿å¶å·ææéå·¥ä½å¯æ¬æ¶æ¯æâåæ¶âä»¥æ¾å¼æ§è¡æ­¤æµè¯ã
page.text.working.copies=å·¥ä½å¯æ¬
page.text.would.you.like.activate.branch=æ¨æ¯å¦å¸ææ¿æ´»æ­¤å?
page.text.would.you.like.reject=æ¨æ¯å¦å¸ææç»æ­¤èµäº§?
page.text.would.you.like.to.activate.communications=æ¨æ¯å¦å¸ææ¿æ´»æ­¤éä¿¡?
page.text.would.you.like.to.activate.image=æ¨æ¯å¦å¸ææ¿æ´»æ­¤å¾å?
page.text.would.you.like.to.activate.images=æ¨æ¯å¦å¸ææ¿æ´»æ­¤å¾å?
page.text.would.you.like.to.activate.shared.content=æ¨æ¯å¦å¸ææ¿æ´»æ­¤å±äº«åå®¹?
page.text.would.you.like.to.activate=æ¨æ¯å¦å¸ææ¿æ´»æ­¤èµäº§?
page.text.would.you.like.to.approve.step=æ¨æ¯å¦å¸ææ¹åæ­¤æ­¥éª¤?
page.text.would.you.like.to.approve=æ¨æ¯å¦å¸ææ¹åæ­¤èµäº§?
page.text.would.you.like.to.clone.image=æ¨æ¯å¦å¸æåéå½åéå®çå¾å?
page.text.would.you.like.to.clone.message=æ¨æ¯å¦å¸æåéæ­¤æ¶æ¯?
page.text.would.you.like.to.clone.selected.asset=æ¨æ¯å¦å¸æåéå½åéå®çèµäº§?
page.text.would.you.like.to.clone.this.image=æ¨æ¯å¦å¸æåéæ­¤å¾å?
page.text.would.you.like.to.close=æ¨æ¯å¦å¸æåéæ­¤èµäº§?
page.text.would.you.like.to.cms.sync=æ¨æ¯å¦å¸æåæ­¥ CMS èµäº§?
page.text.would.you.like.to.deactivate.branch=æ¨æ¯å¦å¸æåç¨æ­¤å?
page.text.would.you.like.to.download.message.template=æ¨æ¯å¦å¸æä¸è½½æ¶æ¯æ¨¡æ¿?
page.text.would.you.like.to.download.variants=æ¨æ¯å¦å¸æä¸è½½ææçåé?
page.text.yes.default.locale=æ¯(é»è®¤åºåè®¾ç½®)
page.text.you.are.about.to.discard.working.copy.of.variant=æ¨å³å°åºé¤æ­¤éå®åéçå·¥ä½å¯æ¬ãè¿å°ä¸å¯æ¤éãæ¨ç¡®å®è¦ç»§ç»­å?
page.text.you.have.asked.to.create.working.copy.of.asset=æ¨å·²è¯·æ±åå»ºä¸ä¸ªæ­¤èµäº§çå·¥ä½å¯æ¬ãè¯·æ³¨ææ­¤æä½ä¸æ¹åç¸å³æ´»å¨æ¥è§¦ç¹èµäº§ã
page.text.you.have.asked.to.remove.asset=æ¨å·²è¯·æ±å®å¨å é¤æ­¤èµäº§ãæ­¤æä½ä¸å¯æ¤éã
page.text.yourpersonalsettings=è¯·æ´æ°æ¨ç Messagepoint ä¸ªäººè®¾ç½®å/ææä¾å¹¶ç¡®è®¤æ°å¯ç ã
page.text.zone.can.flow=åºåå¯ä»¥æµå¨è³æ°é¡µé¢
page.text.zone.can.grow=åºåå¯ä»¥å¨é¡µé¢åå¢é¿
page.text.zone.minimum.size=åºåå¿é¡»è³å°ä¸ºæ­¤å¤§å°
page.text.zones.which.are=åºå
page.title.bulk.upload.confirm.heading=æ¹éä¸ä¼ ç¡®è®¤
page.title.bulk.upload.heading=æ¹éä¸ä¼ 
page.title.import.confirm.heading=å¯¼å¥ç¡®è®¤
page.title.newpassword.confirmation=æ°å¯ç ç¡®è®¤
page.title.newuserid.confirmation=æ°ç¨æ·åç¡®è®¤
page.title.newuseridpassword.confirmation=æ°ç¨æ·ååå¯ç ç¡®è®¤
page.title.role.overview=è§è²æ¦è§
page.title.role.permissions=è§è²æé
page.title.tenantconfirm.activate.heading=æ¿æ´»ç§æ·ç¡®è®¤
page.title.tenantconfirm.activate=ç§æ·æ¿æ´»
page.title.tenantconfirm.deactivate.heading=åç¨ç§æ·ç¡®è®¤
page.title.tenantconfirm.deactivate=ç§æ·åç¨
page.title.tenantconfirm.overview=ç§æ·æ¦è¿°
page.title.touchpoint.import.confirm.heading=æ¥è§¦ç¹å¯¼å¥ç¡®è®¤
page.work.flow.action.addcontent=æ·»å åå®¹
page.work.flow.action.approvalready=æ¹åå®æ
page.work.flow.action.markworkcomplete=å¾æ¹åçæ¬
password.validation.errors.maximumLength=å¯ç é¿åº¦ä¸å¾è¶è¿ {0} ä¸ªå­ç¬¦ã
password.validation.errors.minimumLength=å¯ç é¿åº¦å¿é¡»è³å°ä¸º {0} ä¸ªå­ç¬¦ã
password.validation.errors.mustContainAtleastOneLowerCaseChar=å¯ç å¿é¡»è³å°åå«ä¸ä¸ªå°åå­ç¬¦
password.validation.errors.mustContainAtleastOneNumericChar=å¯ç å¿é¡»è³å°åå«ä¸ä¸ªæ°å­å­ç¬¦
password.validation.errors.mustContainAtleastOneSpecialChar=å¯ç å¿é¡»è³å°åå«ä¸ä¸ªç¹æ®å­ç¬¦
password.validation.errors.mustContainAtleastOneUpperCaseChar=å¯ç å¿é¡»è³å°åå«ä¸ä¸ªå¤§åå­ç¬¦
password.validation.errors.mustNotBeRepeated.within.period=éæ©ä¸ä¸ªæªå¨è¿å» {0} æåä½¿ç¨è¿çå¯ç ã
password.validation.errors.mustNotBeRepeated=éæ©ä¸ä¸ªä¸ä¹å {0} å¼ä¸åçå¯ç ã
password.validation.errors.mustNotContainFLName=éæ©ä¸ä¸ªä¸åå«æ¨åå­çå¯ç ã
password.validation.errors.mustNotContainUsername=éæ©ä¸ä¸ªä¸åå«æ¨ç¨æ·åçå¯ç ã
password.validation.errors.repeating=å¯ç ä¸å¾åå«éå¤çè¿ç»­å­ç¬¦ã
service.default.language.email.template.not.found=æªæ¾å°é»è®¤è¯­è¨çµå­é®ä»¶æ¨¡æ¿
service.email.template.image.size.too.large=æ¨¡æ¿å¾åèµäº§ {0} å¤§äºæ­¤ä¼ éæåè®¸çæå¤§æä»¶å¤§å° ({1}kb)ã
service.email.template.language.more.than.one.template.file=ä¸º {1} æ¾å°äºä¸ä¸ªä»¥ä¸çæ¨¡æ¿æä»¶ {0}
service.email.template.language.no.images.directory=æ²¡æä¸º {0} æ¾å°å¾åç®å½
service.email.template.language.no.template.file=æ²¡æä¸º {0} æ¾å°æ¨¡æ¿æä»¶
service.email.template.language.not.found=æ²¡æä¸º {0} æ¾å°ç®å½
service.email.template.none.existing.zone.or.template.modifier.found=æªå¨ä¸»æ¨¡æ¿ä¸­æ¾å°ä¸ä¸ªæå¤ä¸ªåºåææ¨¡æ¿ä¿®é¥°ç¬¦ã
service.email.template.none.language.found=ä¸ä¼ çåä¸­æ²¡æä¸ºæ­¤æ¥è§¦ç¹éç½®çä»»ä½è¯­è¨
service.import.constants.too.long=å°è¯å¯¼å¥å¨å±å¸¸éâ{0}âï¼æ­¤å¸¸éçé¿åº¦è¶è¿ 255 ä¸ªå­ç¬¦ãå¯¼å¥å·²è¢«ä¸­æ­¢!
service.import.content.library.does.not.exist=å¾ååºå®ä¾ä¸å­å¨ã
service.import.instance.does.not.exist=å·æ GUIDâ{0}âçå®ä¾ä¸å­å¨ã
service.import.instance.is.not.working.copy=å®ä¾ {0} ä¸æ¯å·¥ä½å¯æ¬ã
service.import.touchpoint.does.not.exist=å·æ GUIDâ{0}âçæ¥è§¦ç¹ä¸å­å¨ã
service.import.touchpoint.name.exists=å·²å­å¨å·æç¸ååç§°çæ¥è§¦ç¹ã
service.import.variable.exists=å°è¯å¯¼å¥åéâ{0}âï¼æ­¤åéå·²å­å¨äºä¸åçèç¹/ç§æ·çº§å«ãå¯¼å¥è¢«ä¸­æ­¢!
service.proxy.login.delete.mapped.pod=å°è¯å é¤æ å°ç podãæ­¤å é¤æä½å·²è¢«ä¸­æ­¢ã
service.touchpoint.language.exists=è¯­è¨â{0}âå·²å­å¨äºæ­¤æ¥è§¦ç¹ä¸­ã
service.validation.admin.role.roleExists=å·²å­å¨å·ææ­¤åç§°çè§è²ãè¯·è¾å¥å¶ä»åç§°ã
service.validation.admin.role.roleIsInUse=å½åæ­£å¨ä½¿ç¨è§è²ï¼å æ­¤æ æ³å°å¶å é¤ã
service.validation.admin.role.roleNameInValid=è§è²åç§°æ æã
service.validation.admin.securitySettings.maxattempts=æå¤§å°è¯æ¬¡æ°å¿é¡»å¤§äº 0
service.validation.admin.securitySettings.maximumUserIdLength.lessthanminimum=âç¨æ·åé¿åº¦æå¤§å¼âåºå¤§äºâç¨æ·åé¿åº¦æå°å¼'ã
service.validation.admin.securitySettings.maximumUserIdLength.outofrange=âç¨æ·åé¿åº¦æå¤§å¼âåºä¸ºä»äº 2 å° 80 ä¹é´çæ°å­ã
service.validation.admin.securitySettings.minimumPasswordLength.outofrange=âå¯ç é¿åº¦æå°å¼âåºä¸ºä»äº 6 å° 32 ä¹é´çæ°å­ã
service.validation.admin.securitySettings.minimumUserIdLength.outofrange=âç¨æ·åé¿åº¦æå°å¼âåºä¸ºä»äº 2 å° 80 ä¹é´çæ°å­ã
service.validation.admin.user.emaiAddressExists=çµå­é®ä»¶å°åå·²è¢«ä½¿ç¨
service.validation.admin.user.emaiAddressInvalid=çµå­é®ä»¶å°åæ æ
service.validation.admin.user.firstNameInvalid=åå­æ æ
service.validation.admin.user.lastNameInvalid=å§æ°æ æ
service.validation.admin.user.passwordInvalid=å¯ç æ æ
service.validation.admin.user.userIdAlreadyExists=ç¨æ·åå·²è¢«ä½¿ç¨
service.validation.admin.user.userIdCannotContainSpaces=ç¨æ·åä¸è½åå«ä»»ä½ç©ºæ ¼ã
service.validation.admin.user.userIdInvalid=ç¨æ·åæ æ
service.validation.admin.user.userIdLength=ç¨æ·åå¿é¡»åå« {0} è³ {1} ä¸ªå­ç¬¦ã
service.validation.admin.user.usermusthaverole=è¯·ä¸ºç¨æ·éæ©è§è²
service.validation.common.illegalName=åç§°ä¸åæ³ã{0}
service.validation.lookup.import.error.in.file=è¾å¥æä»¶çç¬¬ {0} è¡ä¸­æéè¯¯
service.validation.lookup.import.general.error=æä»¶ä¸­å­å¨å¸¸è§éè¯¯
service.validation.message.cloneContentLibrary.contentLibraryDoesNotExist=å¾ååºé¡¹ç®ä¸å­å¨ã
service.validation.message.cloneEmbeddedContent.embeddedContentDoesNotExist=åµå¥çåå®¹ä¸å­å¨ã
service.validation.message.cloneMessage.messageDoesNotExist=æ¶æ¯ä¸å­å¨ã
static.type.archive.type.deleted=å·²ç§»é¤
static.type.archive.type.none=æ 
static.type.archive.type.temporary=ä¸´æ¶åæ­¢çäº§
static.type.background.theme.blue=èè²
static.type.background.theme.dark_green=æ·±ç»¿
static.type.background.theme.gray=ç°è²
static.type.background.theme.light_blue=æµè
static.type.background.theme.orange=æ©è²
static.type.background.theme.red=çº¢è²
static.type.background.theme.yellow=é»è²
static.type.channel.composition=ç»å
static.type.channel.email=çµå­é®ä»¶
static.type.channel.generic=éç¨
static.type.channel.sms=SMS
static.type.channel.web=Web
static.type.condition.type.all_of=ææ(å¤éæ¡)
static.type.condition.type.any_of=ä»»ä¸(å¤éæ¡)
static.type.condition.type.one_of=ä¸ä¸ª(åéæé®)
static.type.connector.clickatell=Clickatell
static.type.connector.e_messaging=çµå­æ¶æ¯
static.type.connector.exacttarget=åç¡®ç®æ 
static.type.connector.flat_files=å¹³é¢æä»¶
static.type.connector.ftp=FTP
static.type.connector.gmc=GMC
static.type.connector.hp_exstream=HP Exstream
static.type.connector.native=æ¬å°
static.type.connector.sendmail=Sendmail
static.type.connector.xml=XML
static.type.content.association.type.custom=èªå®ä¹
static.type.content.association.type.empty=ç©º
static.type.content.association.type.none=æ 
static.type.content.association.type.reference=å¼ç¨
static.type.content.association.type.same.as.default=ä¸é»è®¤è®¾ç½®ç¸å
static.type.content.association.type.suppress=ç¦æ­¢
static.type.content.selection.type.changed=å·²æ´æ¹
static.type.content.selection.type.content.library=å¨æ
static.type.content.selection.type.embedded=å¨æ
static.type.content.selection.type.global=å¨å±
static.type.content.selection.type.message=å¨æ
static.type.content.selection.type.new=æ°
static.type.content.selection.type.regular=éæ
static.type.content.selection.type.touchpoint.content=ç»æå
static.type.content.selection.type.unchanged=æªæ´æ¹
static.type.content.type.graphic=å¾è¡¨
static.type.content.type.multi_part=å¤é¨å
static.type.content.type.text=ææ¬
static.type.content.type.video=è§é¢
static.type.daily.frequency.type.everyday=æ¯å¤©(ææä¸ - æææ¥)
static.type.daily.frequency.type.weekdays=ä»å·¥ä½æ¥(ææä¸ - ææäº)
static.type.data.comparison.type.between=ä»äº
static.type.data.comparison.type.contains=åå«
static.type.data.comparison.type.empty_or_null=ç©ºæ Null
static.type.data.comparison.type.ends_with=ç»æäº
static.type.data.comparison.type.equals=ç¸ç­
static.type.data.comparison.type.greater_than=å¤§äº
static.type.data.comparison.type.greater_than_or_equal_to=å¤§äºæç­äº
static.type.data.comparison.type.is_not_one_of=ä¸æ¯ä¸ä¸ª
static.type.data.comparison.type.is_one_of=æ¯ä¸ä¸ª
static.type.data.comparison.type.less_than=å°äº
static.type.data.comparison.type.less_than_or_equal_to=å°äºæç­äº
static.type.data.comparison.type.not_empty_or_null=éç©ºæ Null
static.type.data.comparison.type.not_equal=ä¸ç­
static.type.data.comparison.type.occurs_after=åçæ¶é´æäº
static.type.data.comparison.type.occurs_before=åçæ¶é´æ©äº
static.type.data.comparison.type.occurs_on_or_after=åçäºææäº
static.type.data.comparison.type.occurs_on_or_before=åçäºææ©äº
static.type.data.comparison.type.starts_with=å¼å§äº
static.type.data.record.level.level_0=çº§å« 0
static.type.data.record.level.level_10=çº§å« 10
static.type.data.record.level.level_11=çº§å« 11
static.type.data.record.level.level_12=çº§å« 12
static.type.data.record.level.level_13=çº§å« 13
static.type.data.record.level.level_14=çº§å« 14
static.type.data.record.level.level_15=çº§å« 15
static.type.data.record.level.level_16=çº§å« 16
static.type.data.record.level.level_17=çº§å« 17
static.type.data.record.level.level_18=çº§å« 18
static.type.data.record.level.level_19=çº§å« 19
static.type.data.record.level.level_1=çº§å« 1
static.type.data.record.level.level_20=çº§å« 20
static.type.data.record.level.level_2=çº§å« 2
static.type.data.record.level.level_3=çº§å« 3
static.type.data.record.level.level_4=çº§å« 4
static.type.data.record.level.level_5=çº§å« 5
static.type.data.record.level.level_6=çº§å« 6
static.type.data.record.level.level_7=çº§å« 7
static.type.data.record.level.level_8=çº§å« 8
static.type.data.record.level.level_9=çº§å« 9
static.type.data.sub.type.boolean=å¸å°å
static.type.data.sub.type.currency=è´§å¸
static.type.data.sub.type.date=æ¥æ
static.type.data.sub.type.double=å å
static.type.data.sub.type.float=æµ®ç¹å
static.type.data.sub.type.image=å¾å
static.type.data.sub.type.integer=æ´æ°
static.type.data.sub.type.numeric=æ°å¼
static.type.data.sub.type.short=ç­
static.type.data.sub.type.string=å­ç¬¦ä¸²
static.type.data.sub.type.uinteger=UInteger
static.type.data.sub.type.unknown=æªç¥
static.type.data.sub.type.ushort=UShort
static.type.data.type.boolean=å¸å°å
static.type.data.type.date=æ¥æ
static.type.data.type.numeric=æ°å¼
static.type.data.type.string=å­ç¬¦ä¸²
static.type.favorites_folder=æ¶èå¤¹
static.type.frequency.type.daily=æ¯æ¥
static.type.frequency.type.monthly=æ¯æ
static.type.frequency.type.quarterly=æ¯å­£åº¦
static.type.frequency.type.weekly=æ¯å¨
static.type.frequency.type.yearly=æ¯å¹´
static.type.last_visited_folder=ä¸æ¬¡è®¿é®æ¶é´
static.type.layout.type.columnar=åæ å¼
static.type.layout.type.delimited=å¸¦åéç¬¦
static.type.layout.type.xml=Xml
static.type.new.item.folder.description=é»è®¤æ°é¡¹ç®æä»¶å¤¹
static.type.new.item.folder=æ°å»ºé¡¹ç®
static.type.pod.invalid.schema=æ æ POD MASTER æ¶æ
static.type.pod.scan.fail=æ«æå¤±è´¥äº
static.type.pod.scan.success=æ«ææåäº
static.type.pod.status.offline=è±æº
static.type.pod.status.online=èæº
static.type.pod.type.generic=éç¨
static.type.pod.type.production=çäº§
static.type.pod.type.transition=é¢è§ç
static.type.production.event.type.external.trigger=å¤é¨è§¦åå¨
static.type.production.event.type.immediate=ç«å³
static.type.production.event.type.schedule=æ¶é´è¡¨
static.type.proof.definition.status.invalid=æ æ
static.type.proof.definition.status.unknown=æªç¥
static.type.proof.definition.status.valid=ææ
static.type.qualification.output.dxf=DXF
static.type.qualification.output.flat_files=å¹³é¢æä»¶
static.type.qualification.output.xml_optimized=ä¼åç XML
static.type.qualification.output.xml_verbose=è¯¦ç»ç XML
static.type.resource.location.type.local=æ¬å°
static.type.resource.location.type.remote=è¿ç¨
static.type.source.type.primary=ä¸»è¦
static.type.source.type.reference.connected=å¼ç¨(å·²è¿æ¥ç³è¯·è)
static.type.source.type.reference=å¼ç¨
static.type.system.theme.ever_green=ç¿ ç»¿
static.type.system.theme.monochrome=åè²
static.type.system.theme.ocean_blue=æµ·æ´è
static.type.system.theme.red_rose=ç«ç°çº¢
static.type.task.type.follow_up=è·è¿
static.type.task.type.none_complete=æ  - å®æ
static.type.task.type.system=ç³»ç»
static.type.task.type.verification=éªè¯
static.type.text.formatting.cobol.signed=COBOL ç­¾ç½²(å°¾é¨)
static.type.text.formatting.general.number=å¸¸è§æ°å­
static.type.text.formatting.keep.blanks.default=ä¿æç©ºç½(é»è®¤)
static.type.text.formatting.keep.blanks=ä¿æç©ºç½
static.type.text.formatting.lower.keep.blanks=ä¸é¨ï¼ä¿æç©ºç½
static.type.text.formatting.lower.trim.blanks=ä¸é¨ï¼åªè£ç©ºç½
static.type.text.formatting.lower.trim.leading=ä¸é¨ï¼åªè£è¡è·
static.type.text.formatting.lower.trim.trailing=ä¸é¨ï¼åªè£ç»å°¾
static.type.text.formatting.packed=æå(ä»å·¦è³å³)
static.type.text.formatting.trim.blanks=åªè£ç©ºç½
static.type.text.formatting.trim.leading.blanks=åªè£è¡è·ç©ºç½
static.type.text.formatting.trim.trailing.blanks=åªè£ç»å°¾ç©ºç½
static.type.text.formatting.upper.keep.blanks=ä¸é¨ï¼ä¿æç©ºç½
static.type.text.formatting.upper.trim.blanks=ä¸é¨ï¼åªè£ç©ºç½
static.type.text.formatting.upper.trim.leading=ä¸é¨ï¼åªè£è¡è·
static.type.text.formatting.upper.trim.trailing=ä¸é¨ï¼åªè£ç»å°¾
static.type.touchpoint.selection.type.content=ç»æå
static.type.touchpoint.selection.type.message=çµæ´»
static.type.touchpoint.selection.type.regular=æ å
static.type.work.group.all=ææ
static.type.zone.type.document.zone=ææ¡£åºå
static.type.zone.type.email.subject.line.zone=çµå­é®ä»¶ä¸»é¢è¡åºå
static.type.zone.type.email.zone=çµå­é®ä»¶åºå
static.type.zone.type.freeform.page.zone=èªç±æ ¼å¼é¡µé¢
static.type.zone.type.freeform.zone=èªç±æ ¼å¼åºå
static.type.zone.type.interactive.zone=è¿æ¥çåºå
static.type.zone.type.sms.zone=SMS åºå
static.type.zone.type.web.title.line.zone=Web æ é¢è¡åºå
static.type.zone.type.web.zone=Web åºå
user.deactivate.reason.1=å·²åç¨
user.deactivate.reason.2=å°æªæ¿æ´»
user.deactivate.reason.3=å·²éå®
verbiage.version.reason.new.content.library=æ°å»ºå¾å
verbiage.version.reason.new.embedded.content=æ°å»ºæºè½ææ¬
verbiage.version.reason.newmessage=æ°å»ºæ¶æ¯
xslt_messages.ACTIVE=æ´»å¨
xslt_messages.ASSIGNMENT=åé
xslt_messages.BIN=BIN
xslt_messages.DATE.RANGE=æ¥æèå´
xslt_messages.ID=ID
xslt_messages.NAME=åç§°
xslt_messages.TIMING=è®¡æ¶
xslt_messages.WORKING.COPY=å·¥ä½å¯æ¬
xslt_messages.access.log.save.select.package=è¦è®¿é®æ¥å¿ï¼è¯·åå»âä¿å­âå¹¶éæ©è¯æ­åã
xslt_messages.action=æä½
xslt_messages.active.as.of=æ´»å¨ç¶æèª
xslt_messages.active=æ´»å¨
xslt_messages.always=æ»æ¯
xslt_messages.ancestor.info=ä¸çº§ä¿¡æ¯
xslt_messages.and.of.these.include=å¶ä¸­åæ¬
xslt_messages.approvals=æ¹å
xslt_messages.archived=å·²å­æ¡£
xslt_messages.as.of=èª
xslt_messages.assigned.to=åéå°
xslt_messages.back.to.batch.report=è¿åå°æ¹æ¥å
xslt_messages.back.to.the.report=è¿åå°æ¥å
xslt_messages.batch.level=æ¹çº§å«
xslt_messages.batch.num=æ¹ç¼å·
xslt_messages.batch.report=æ¹æ¥å
xslt_messages.batches=æ¹
xslt_messages.bin.assignments=Bin åé
xslt_messages.condition.id=æ¡ä»¶ ID
xslt_messages.conditions=æ¡ä»¶
xslt_messages.constant.name=å¸¸éåç§°
xslt_messages.constant=å¸¸é
xslt_messages.content.suppressed=åå®¹å·²ç¦æ­¢
xslt_messages.content.type=åå®¹ç±»å
xslt_messages.content=åå®¹
xslt_messages.contents=åå®¹
xslt_messages.created=å·²åå»º
xslt_messages.current.state=å½åç¶æ
xslt_messages.custom=èªå®ä¹
xslt_messages.date.range=æ¥æèå´
xslt_messages.date.run=è¿è¡æ¥æ
xslt_messages.date.values=æ°æ®å¼
xslt_messages.date=æ¥æ
xslt_messages.delivered=å·²ä¼ é
xslt_messages.delivery=ä¼ é
xslt_messages.description=æè¿°
xslt_messages.diagnostics.report=è¯æ­æ¥å
xslt_messages.direct.references=ç´æ¥å¼ç¨
xslt_messages.elapsed.time=å·²è¿å»çæ¶é´
xslt_messages.email.address=çµå­é®ä»¶å°å
xslt_messages.email=çµå­é®ä»¶
xslt_messages.empty.content=ç©ºåå®¹
xslt_messages.empty.part=ç©ºé¨å
xslt_messages.envelope=ä¿¡å°
xslt_messages.exclude=æé¤
xslt_messages.export.version=å¯¼åºçæ¬
xslt_messages.external.id=å¤é¨ ID
xslt_messages.failed.deliveries=å¤±è´¥çä¼ é
xslt_messages.failed=å¤±è´¥
xslt_messages.for=éå¯¹
xslt_messages.from.date=èµ·å§æ¥æ
xslt_messages.graphic=å¾è¡¨
xslt_messages.image.library.content.selections=å¾ååºåå®¹éæ©
xslt_messages.image.library.reference.data=å¾ååºå¼ç¨æ°æ®
xslt_messages.image.library=å¾ååº
xslt_messages.in.approval=å¨æ¹åä¸­
xslt_messages.inactive=éæ´»å¨
xslt_messages.include.active.objects.only=ä»åæ¬æ´»å¨å¯¹è±¡
xslt_messages.include.content=åæ¬åå®¹
xslt_messages.include.indirect.map=åæ¬é´æ¥æ å°
xslt_messages.include.insert.targeting=åæ¬æå¥ç®æ æå®
xslt_messages.include.targeting=åæ¬ç®æ æå®
xslt_messages.include=åæ¬
xslt_messages.included.indirect.references=åæ¬çé´æ¥å¼ç¨
xslt_messages.indefinite=ä¸ç¡®å®
xslt_messages.inherited.from=å·²ç»§æ¿èª
xslt_messages.inherited=å·²ç»§æ¿
xslt_messages.insert.schedules=æå¥æ¶é´è¡¨
xslt_messages.inserts.reference.data=æå¥å¼ç¨æ°æ®
xslt_messages.job.count=ä½ä¸è®¡æ°
xslt_messages.job.id=ä½ä¸ ID
xslt_messages.job.level=ä½ä¸çº§å«
xslt_messages.job=ä½ä¸
xslt_messages.language=è¯­è¨
xslt_messages.less.than=å°äº
xslt_messages.level=æ°´å¹³
xslt_messages.log=æ¥å¿
xslt_messages.message.content.selections=æ¶æ¯åå®¹éæ©
xslt_messages.message.priority=æ¶æ¯ä¼åçº§
xslt_messages.message.type=æ¶æ¯ç±»å
xslt_messages.message=æ¶æ¯
xslt_messages.messages=æ¶æ¯
xslt_messages.metadata=åæ°æ®
xslt_messages.name=åç§°
xslt_messages.no.data.for.time.period=æ²¡æå¯ç¨äºæ­¤æ¶é´æ®µçæ°æ®ã
xslt_messages.no.direct.reference.for.variable=æ²¡æéåæ­¤åéçç´æ¥å¼ç¨ã
xslt_messages.no.events.for.date.range=ä¸å­å¨å¯¹åºäºéå®æ¥æèå´çäºä»¶ã
xslt_messages.no.indirect.references.for.variable=æ²¡æå¯¹åºäºæ­¤åéçé´æ¥å¼ç¨ã
xslt_messages.no.proofs.for.selection=æ²¡æå¯¹åºäºæ­¤éæ©çè¯æ®ã
xslt_messages.no.status=æ²¡æç¶æ
xslt_messages.no=æ 
xslt_messages.none=æ 
xslt_messages.notes=æ³¨é
xslt_messages.on.level=å¨çº§å«
xslt_messages.otherwise=å¦å
xslt_messages.part=é¨å
xslt_messages.path=è·¯å¾
xslt_messages.proofs=è¯æ®
xslt_messages.qualification=èµæ ¼
xslt_messages.ran.on=è¿è¡äº
xslt_messages.rate.sheet.reference.data=è¯ä»·è¡¨å¼ç¨æ°æ®
xslt_messages.rate.sheets=è¯ä»·è¡¨
xslt_messages.recipient.id=æ¶ä»¶äºº ID
xslt_messages.recipient.level=æ¶ä»¶äººçº§å«
xslt_messages.recipients.minute=æ¶ä»¶äºº/åé
xslt_messages.recipients=æ¶ä»¶äºº
xslt_messages.reference.map=å¼ç¨æ å°
xslt_messages.reference.name=å¼ç¨åç§°
xslt_messages.referencing.image.library=å¼ç¨å¾ååº
xslt_messages.released=å·²åè¡
xslt_messages.repeats.annually=æ¯å¹´éå¤
xslt_messages.report.request.summary=æ¥åè¯·æ±æè¦
xslt_messages.report.type=æ¥åç±»å
xslt_messages.request.date=è¯·æ±æ¥æ
xslt_messages.request.options=è¯·æ±éé¡¹
xslt_messages.request.overview=è¯·æ±æ¦è¿°
xslt_messages.requested.by=è¯·æ±è
xslt_messages.requested.on=è¯·æ±äº
xslt_messages.requested=å·²è¯·æ±
xslt_messages.rules=è§å
xslt_messages.run.date=è¿è¡æ¥æ
xslt_messages.same.as.default.language=ä¸é»è®¤è¯­è¨ç¸å
xslt_messages.same.as=ç­åäº
xslt_messages.section=è
xslt_messages.selected.insert.schedule.index=éå®çæå¥æ¶é´è¡¨ç´¢å¼
xslt_messages.selected.messages.index=éå®çæ¶æ¯ç´¢å¼
xslt_messages.send=åé
xslt_messages.sent=å·²åé
xslt_messages.sheets=è¡¨
xslt_messages.smart.text=æºè½ææ¬
xslt_messages.state=ç¶æ
xslt_messages.stating=å¯å¨
xslt_messages.status=ç¶æ
xslt_messages.stock.id=åºå­ ID
xslt_messages.succeeded=å·²æå
xslt_messages.suppressed=å·²ç¦æ­¢
xslt_messages.targeting=æå®ç®æ 
xslt_messages.text=ææ¬
xslt_messages.thresholds=éå¼
xslt_messages.through.conditions=éè¿æ¡ä»¶
xslt_messages.through.content=éè¿åå®¹
xslt_messages.to.date=æªæ­¢æ¥æ
xslt_messages.touchpoint.audit.report=æ¥è§¦ç¹å®¡æ ¸æ¥è¡¨
xslt_messages.touchpoint.change.report=æ¥è§¦ç¹åæ´æ¥å
xslt_messages.touchpoint.content=æ¥è§¦ç¹åå®¹
xslt_messages.touchpoint.delivery.report=æ¥è§¦ç¹ä¼ éæ¥å
xslt_messages.touchpoint.index=æ¥è§¦ç¹ç´¢å¼
xslt_messages.touchpoint.or.touchpoints=æ¥è§¦ç¹
xslt_messages.touchpoint.reference.data=æ¥è§¦ç¹å¼ç¨æ°æ®
xslt_messages.touchpoint.selections=æ¥è§¦ç¹éæ©
xslt_messages.touchpoint=æ¥è§¦ç¹
xslt_messages.touchpoints=æ¥è§¦ç¹
xslt_messages.tp.delivery.report=TP ä¼ éæ¥å
xslt_messages.unexported=æªå¯¼åº
xslt_messages.unknown.type=æªç¥ç±»å
xslt_messages.unknown=æªç¥
xslt_messages.unspecified.assuming.requested.date=æªæå®ãåè®¾è¯·æ±æ¥æ
xslt_messages.upload.date=ä¸ä¼ æ¥æ
xslt_messages.use=ä½¿ç¨
xslt_messages.used.by=ä½¿ç¨è:
xslt_messages.user=ç¨æ·
xslt_messages.variable=åé
xslt_messages.versions=çæ¬
xslt_messages.wc.or.archived=å·¥ä½å¯æ¬æå·²å­æ¡£
xslt_messages.web.page=WEB é¡µé¢
xslt_messages.weight=éé
xslt_messages.when.greater.than=å½å¤§äº
xslt_messages.where.used=ä½¿ç¨ä½ç½®
xslt_messages.working.copy=å·¥ä½å¯æ¬
xslt_messages.yes=æ¯
xslt_messages.zone=åºå
