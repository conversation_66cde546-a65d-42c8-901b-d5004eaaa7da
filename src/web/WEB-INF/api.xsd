<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	xmlns:mp="http://messagepoint.com/api/schemas" elementFormDefault="qualified"
	targetNamespace="http://messagepoint.com/api/schemas">

	<!-- All requests should extend from this type -->
	<xs:complexType name="ClientIdentificationType">
		<xs:sequence>
			<xs:element name="BranchId" type="xs:string" minOccurs="1"
				maxOccurs="1" nillable="false" />
			<xs:element name="NodeId" type="xs:string" minOccurs="0"
				maxOccurs="1" nillable="false" />
			<xs:element name="Prerelease" type="xs:string" minOccurs="0"
				maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>


	<!-- List of requests and responses messages -->
	<xs:element name="ActivateExistingSchemaRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="nodeName" type="xs:string" minOccurs="1"
									maxOccurs="1" />
						<xs:element name="nodeFriendlyName" type="xs:string"
									minOccurs="1" maxOccurs="1" />
						<xs:element name="nodeSchemaName" type="xs:string"
									minOccurs="1" maxOccurs="1" />
						<xs:element name="nodeInstanceType" type="xs:string"
									minOccurs="1" maxOccurs="1" />
						<xs:element name="task" type="xs:string"
									minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="ActivateExistingSchemaResponse" type="mp:SimpleResponseType" />

	<xs:element name="AuditReportRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="FromDate" type="xs:date" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="ToDate" type="xs:date" minOccurs="1"
							maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="AuditReportResponse" type="mp:FileResponseType" />
	
	<xs:element name="MetadataReportRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="MetadataReportResponse" type="mp:FileResponseType" />

	<xs:element name="MessageAuditReportRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="MessageAuditReportResponse" type="mp:FileResponseType" />


	<xs:element name="BackgroundImageDownloadRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							maxOccurs="1" />
						<xs:element name="SectionNumber" type="xs:unsignedInt" minOccurs="0"
							maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="BackgroundImageDownloadResponse">
		<xs:complexType>
			<xs:choice>
				<xs:element name="FailureResponse" type="xs:string" />
				<xs:sequence>
					<xs:element name="Touchpoint" type="xs:string"
						minOccurs="1" maxOccurs="1" />
					<xs:element name="Section" type="mp:SectionFileType"
						minOccurs="0" maxOccurs="unbounded" />
				</xs:sequence>
			</xs:choice>
		</xs:complexType>
	</xs:element>


	<xs:element name="CompositionPackageUpdateRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="PackageId" type="xs:long" />
						<xs:element name="CompositionPackage" type="mp:CompositionPackageType" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="CompositionPackageUpdateResponse" type="mp:SimpleResponseType" />


	<xs:element name="SFTPRepoSyncRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="folder" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="metatags" type="xs:string"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="globalContent" type="xs:boolean"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="VariableContentEnabled" type="xs:boolean"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="ActivateTarget" type="xs:boolean"
							minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="SFTPRepoSyncResponse" type="mp:BackgroundTaskIdentificationResponseType" />

	<xs:element name="GetBackgroundTaskStatusRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Id" type="xs:string"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="GetBackgroundTaskStatusResponse" type="mp:BackgroundTaskStatusType" />
	
	<xs:element name="GetBackgroundTaskOutputFileRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Id" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="ChunkNumber" type="xs:unsignedInt"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="ChunkSize" type="xs:unsignedInt"
									minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="GetBackgroundTaskOutputFileResponse" type="mp:FileResponseType" />

	<xs:element name="GetSystemPropertiesRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Type" type="xs:int"
									minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="GetSystemPropertiesResponse" type="mp:KeyValueListResponseType" />

	<xs:element name="SetSystemPropertiesRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Api" type="mp:ApiSystemSettingsType"
									minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="SetSystemPropertiesResponse" type="mp:KeyValueListResponseType" />
	
	<xs:element name="CompositionPackageUploadRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="TouchpointCollection" type="mp:TouchpointCollectionType"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="CompositionPackage" type="mp:CompositionPackageType" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="CompositionPackageUploadResponse" type="mp:ObjectCreateResponseType" />


	<xs:element name="CompositionPackageListRequest" type="mp:ClientIdentificationType" />
	<xs:element name="CompositionPackageListResponse" type="mp:ObjectListResponseType" />


	<xs:element name="ContentSearchRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							maxOccurs="1" />
						<xs:element name="SearchString" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="Advanced" type="xs:boolean"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="ContentSearchResponse" type="mp:ObjectListResponseType" />

	<xs:element name="TargetingGroupsSearchRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							maxOccurs="1" />
						<xs:element name="SearchString" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="Advanced" type="xs:boolean"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TargetingGroupsSearchResponse" type="mp:ObjectListResponseType" />

	<xs:element name="TargetingRulesSearchRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							maxOccurs="1" />
						<xs:element name="SearchString" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="Advanced" type="xs:boolean"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TargetingRulesSearchResponse" type="mp:ObjectListResponseType" />


	<xs:element name="CreateSandboxInstanceRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="FromNode" type="xs:string" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="CreateUser" type="mp:UserType"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="CreateSandboxInstanceResponse" type="mp:SimpleResponseType" />


	<xs:element name="DataAnonymizeRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="TouchpointCollection" type="mp:TouchpointCollectionType"
							minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="DataAnonymizeResponse" type="mp:FileResponseType" />


	<xs:element name="DataFileListRequest" type="mp:ClientIdentificationType" />
	<xs:element name="DataFileListResponse" type="mp:ObjectListResponseType" />


	<xs:element name="DataFileUploadRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="DataFileName" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="SourceType" type="xs:int" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="Filename" type="xs:string" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="ChunkNumber" type="xs:unsignedInt"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="Base64Content" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="0" maxOccurs="unbounded" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="DataFileUploadResponse" type="mp:SimpleResponseType" />

	<xs:element name="DataFileDownloadRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Name" type="xs:string" minOccurs="1"
									maxOccurs="1" />
						<xs:element name="ChunkNumber" type="xs:unsignedInt"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="ChunkSize" type="xs:unsignedInt"
									minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="DataFileDownloadResponse" type="mp:FileResponseType" />

	<xs:element name="CreateDataResourceRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Name" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="PrimaryDataFileName" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="ReferenceDataFileList" type="mp:ReferenceDataFileListType" minOccurs="0"
							maxOccurs="1"/>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="CreateDataResourceResponse" type="mp:SimpleResponseType" />
	
	<xs:element name="UploadProcessedJobRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="PodId" type="xs:string" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="JobId" type="xs:string" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="Type" type="xs:string" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="ChunkNumber" type="xs:unsignedInt"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="Base64Content" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="JobKey" type="xs:string" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="CheckSum" type="xs:string" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="FileSize" type="xs:unsignedInt" minOccurs="0" maxOccurs="1"/>
						<xs:element name="ReturnCode" type="xs:unsignedInt" minOccurs="0"
									maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="UploadProcessedJobResponse" type="mp:SimpleResponseType" />
	
	<xs:element name="UploadJobReportRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Filename" type="xs:string" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="ChunkNumber" type="xs:unsignedInt"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="ChunkSize" type="xs:unsignedInt"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="Base64Content" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="CheckSum" type="xs:string" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="FileSize" type="xs:unsignedInt" minOccurs="0" maxOccurs="1"/>
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="UploadJobReportResponse" type="mp:SimpleResponseType" />

	<xs:element name="UploadEmailTemplateRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
									minOccurs="1" maxOccurs="1" />
						<xs:element name="Filename" type="xs:string" minOccurs="1"
									maxOccurs="1" />
						<xs:element name="ChunkNumber" type="xs:unsignedInt"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="ChunkSize" type="xs:unsignedInt"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="Base64Content" type="xs:string"
									minOccurs="1" maxOccurs="1" />
						<xs:element name="CheckSum" type="xs:string" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="FileSize" type="xs:unsignedInt" minOccurs="0" maxOccurs="1"/>
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="UploadEmailTemplateResponse" type="mp:SimpleResponseType" />

	<xs:element name="DeleteFileRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="FileList" type="mp:FileListType"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="DeleteFileResponse" type="mp:SimpleResponseType" />


	<xs:element name="DeliveryReportRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="FromDate" type="xs:date" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="ToDate" type="xs:date" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="MessageList" type="mp:MessageListType" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="DeliveryReportResponse" type="mp:ObjectListResponseType" />


	<xs:element name="DiagnosticRequest" type="mp:ClientIdentificationType" />
	<xs:element name="DiagnosticResponse" type="mp:FileResponseType" />

	<xs:element name="UsersReportRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="outputType" type="xs:unsignedInt"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="UsersReportResponse" type="mp:FileResponseType" />

	<xs:element name="UsersTemplateRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="userGuid" type="xs:string"
									minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="UsersTemplateResponse" type="mp:FileResponseType" />

	<xs:element name="CreateActivateUserRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Filename" type="xs:string" minOccurs="1"
									maxOccurs="1" />
						<xs:element name="ChunkNumber" type="xs:unsignedInt"
									minOccurs="1" maxOccurs="1" />
						<xs:element name="ChunkSize" type="xs:unsignedInt"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="Base64Content" type="xs:string"
									minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="CreateActivateUserResponse" type="mp:SimpleResponseType" />

	<xs:element name="UnifiedLoginReportRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="outputType" type="xs:unsignedInt"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="includePincLicenseStatus" type="xs:boolean"
									minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="UnifiedLoginReportResponse" type="mp:FileResponseType" />

	<xs:element name="DomainReportRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="outputType" type="xs:unsignedInt"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="includeUserManagementInfo" type="xs:boolean"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="includeLicenseManagementInfo" type="xs:boolean"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="includeFeatureActivationInfo" type="xs:boolean"
							minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="DomainReportResponse" type="mp:FileResponseType" />

	<xs:element name="FileChunkDownloadRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Filename" type="xs:string" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="ChunkNumber" type="xs:unsignedInt"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="FileChunkDownloadResponse" type="mp:FileResponseType" />


	<xs:element name="FileChunkUploadRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Filename" type="xs:string" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="ChunkNumber" type="xs:unsignedInt"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="Base64Content" type="xs:string"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="FileChunkUploadResponse" type="mp:SimpleResponseType" />


	<xs:element name="FileListRequest" type="mp:ClientIdentificationType" />
	<xs:element name="FileListResponse" type="mp:FileListType" />


	<xs:element name="ImportJobStatsRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="JobId" type="xs:long" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="BatchId" type="xs:int" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="FileList" type="mp:FileListType"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="SimulationReport" type="xs:string"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="SegmentationReport" type="xs:string"
							minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="ImportJobStatsResponse" type="mp:SimpleResponseType" />


	<xs:element name="ImportTransactionStatsRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="JobId" type="xs:long" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="TransactionId" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="FileList" type="mp:FileListType"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="ImportTransactionStatsResponse" type="mp:SimpleResponseType" />

	<xs:element name="JobDiagnosticBundleRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="JobId" type="xs:long" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="DeliveryEventId" type="xs:long"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="Chunk" type="xs:unsignedInt"
							minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="JobDiagnosticBundleResponse" type="mp:FileResponseType" />

	<xs:element name="JobServerBundleRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="JobId" type="xs:long" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="DeliveryEventId" type="xs:long"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="Chunk" type="xs:unsignedInt"
							minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="JobServerBundleResponse" type="mp:FileResponseType" />

	<xs:element name="JobClientBundleRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="JobId" type="xs:long" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="DeliveryEventId" type="xs:long"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="Chunk" type="xs:unsignedInt"
							minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="JobClientBundleResponse" type="mp:FileResponseType" />

	<xs:element name="DeactivateUserRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="IdPUserGuid" type="xs:string"
							minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="DeactivateUserResponse" type="mp:SimpleResponseType" />

	<xs:element name="MessageListRequest" type="mp:ClientIdentificationType" />
	<xs:element name="MessageListResponse" type="mp:ObjectListResponseType" />


	<xs:element name="MessageContentQueryRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="LanguageContent" type="xs:string" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="MessageContentQueryResponse" type="mp:ObjectListResponseType" />

	<xs:element name="NodeListRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="verbose" type="xs:string" minOccurs="0"
									maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="NodeListResponse" type="mp:ObjectListResponseType" />

	<xs:element name="NodeToNodeCopyRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="FromNode" type="xs:string" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="ToNode" type="xs:string" minOccurs="1"
							maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="NodeToNodeCopyResponse" type="mp:SimpleResponseType" />

	<xs:element name="RerunTestRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="TestId" type="xs:long" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="TestGuid" type="xs:string" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="JobId" type="xs:long" minOccurs="0"
							maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="RerunTestResponse" type="mp:ObjectCreateResponseType" />

	<xs:element name="TestListRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType" />
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TestListResponse" type="mp:ObjectListResponseType" />

	<xs:element name="DeliveryEventListRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType" />
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="DeliveryEventListResponse"/>

	<xs:element name="TranslationCallbackRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="RequestGuid" type="xs:string" minOccurs="1"
									maxOccurs="1" />
						<xs:element name="Filename" type="xs:string" minOccurs="1"
									maxOccurs="1" />
						<xs:element name="ChunkNumber" type="xs:unsignedInt"
									minOccurs="1" maxOccurs="1" />
						<xs:element name="ChunkSize" type="xs:unsignedInt"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="Base64Content" type="xs:string"
									minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TranslationCallbackResponse" type="mp:SimpleResponseType" />

	<xs:element name="TouchpointDeliveryEventRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="TouchpointCollection" type="mp:TouchpointCollectionType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="APIRetrieve" type="xs:boolean"
							minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TouchpointDeliveryEventResponse" type="mp:ObjectCreateResponseType" />

	<xs:element name="CreateDeliveryEventRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="TouchpointCollection" type="mp:TouchpointCollectionType"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="Enabled" type="xs:boolean"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="DeliveryEventName" type="xs:string"
									minOccurs="1" maxOccurs="1" />
						<xs:element name="EnforceUniqueName" type="xs:boolean"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="BundleDeliveryGuid" type="xs:string"
									minOccurs="1" maxOccurs="1" />
						<xs:element name="UpdateStageProdBundle" type="xs:boolean"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="BundleDataResourceName" type="xs:string"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="BundleCompositionPackageName" type="xs:string"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="ReceiveNotificationOnFailure" type="xs:boolean"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="ResetContentChangedFlag" type="xs:boolean"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="RunType" type="xs:int"
									minOccurs="1" maxOccurs="1" />
						<xs:element name="Schedule" type="mp:ScheduleType"
									minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="CreateDeliveryEventResponse" type="mp:SimpleResponseType" />

	<xs:element name="ExecuteDeliveryEventRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:choice>
							<xs:element name="DeliveryEventGuid" type="xs:string"
										minOccurs="0" maxOccurs="1" />
							<xs:sequence>
								<xs:element name="DeliveryEventName" type="xs:string"
											minOccurs="0" maxOccurs="1" />
								<xs:element name="Touchpoint" type="mp:TouchpointType"
											minOccurs="0" maxOccurs="1" />
								<xs:element name="TouchpointCollection" type="mp:TouchpointCollectionType"
											minOccurs="0" maxOccurs="1" />
							</xs:sequence>
						</xs:choice>
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="ExecuteDeliveryEventResponse" type="mp:ObjectCreateResponseType" />

	<xs:element name="TouchpointProductionJobListRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="TouchpointCollection" type="mp:TouchpointCollectionType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="FromDate" type="xs:date" minOccurs="0"
							maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TouchpointProductionJobListResponse" type="mp:ObjectListResponseType" />


	<xs:element name="TouchpointListRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="RequesterUsername" type="xs:string"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="ListAll" type="xs:boolean" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="ExplicitResponse" type="xs:boolean" minOccurs="0"
							maxOccurs="1" />	
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TouchpointListResponse" type="mp:ObjectListResponseType" />

	<xs:element name="DataResourceListRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
									minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="DataResourceListResponse" type="mp:ObjectListResponseType" />
	
	<xs:element name="TouchpointCollectionListRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="ListAll" type="xs:boolean" minOccurs="0"
							maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TouchpointCollectionListResponse" type="mp:ObjectListResponseType" />

	<xs:element name="TouchpointImportRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Filename" type="xs:string" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="ChunkNumber" type="xs:unsignedInt"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="ChunkSize" type="xs:unsignedInt"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="Base64Content" type="xs:string"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="ActivateTarget" type="xs:boolean"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="RenameIfExisting" type="xs:boolean"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="ImportActiveAsWorking" type="xs:boolean"
									minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TouchpointImportResponse" type="mp:BackgroundTaskIdentificationResponseType" />

	<xs:element name="TouchpointCloneRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="DocumentId" type="xs:string" minOccurs="1"
									maxOccurs="1" />
						<xs:element name="TargetBranchID" type="xs:string" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="TargetNodeId" type="xs:string" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="TargetTouchpointName" type="xs:string" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="IncludeMessages" type="xs:boolean" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="IncludeAllVariables" type="xs:boolean" minOccurs="0"
									maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TouchpointCloneResponse" type="mp:BackgroundTaskIdentificationResponseType" />

	<xs:element name="TouchpointUpdateRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="DocumentId" type="xs:string" minOccurs="1"
									maxOccurs="1" />
						<xs:element name="Filename" type="xs:string" minOccurs="1"
									maxOccurs="1" />
						<xs:element name="ChunkNumber" type="xs:unsignedInt"
									minOccurs="1" maxOccurs="1" />
						<xs:element name="ChunkSize" type="xs:unsignedInt"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="Base64Content" type="xs:string"
									minOccurs="1" maxOccurs="1" />
						<xs:element name="UpdateSharedObjects" type="xs:boolean" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="FindElementsByName" type="xs:boolean" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="UpdateTextStyles" type="xs:boolean" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="CreateDuplicatesOfEmbeddedContent" type="xs:boolean" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="UpdateEmbeddedContent" type="xs:boolean" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="CreateDuplicatesOfContentLibrary" type="xs:boolean" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="UpdateContentLibrary" type="xs:boolean" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="ActivateTarget" type="xs:boolean"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="ImportActiveAsWorking" type="xs:boolean"
									minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TouchpointUpdateResponse" type="mp:BackgroundTaskIdentificationResponseType" />
	
	<xs:element name="TouchpointExportRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="ModelGuid" type="xs:string" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="Type" type="xs:unsignedInt"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="ExtensionType" type="xs:unsignedInt"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="0" maxOccurs="1" />
                        <xs:element name="PreferActive" type="xs:boolean"
                                    minOccurs="0" maxOccurs="1" />
                        <xs:element name="IncludeImagePathOnly" type="xs:boolean"
                                    minOccurs="0" maxOccurs="1" />
                        <xs:element name="IncludeAllLanguageLocalesForGlobalContentObjects" type="xs:boolean"
                                    minOccurs="0" maxOccurs="1" />
                        <xs:element name="TryActiveCopyWhenWorkingNotExist" type="xs:boolean"
                                    minOccurs="0" maxOccurs="1" />
                        <xs:element name="ExportReferencedTextStylesOnly" type="xs:boolean"
                                    minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TouchpointExportResponse" type="mp:BackgroundTaskIdentificationResponseType" />
	
	<xs:element name="DataSourceListRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="SourceType" type="xs:int" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="SlimResponse" type="xs:boolean" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="ExplicitResponse" type="xs:boolean" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="ReferencedDataElements" type="xs:boolean" minOccurs="0"
							maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="DataSourceListResponse" type="mp:ObjectListResponseType" />
	
	<xs:element name="CreateConnectedOrderRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="1" maxOccurs="1" />
						<xs:element name="AuthorUsername" type="xs:string" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="DataPointList" type="mp:ConnectedOrderDataPointListType" minOccurs="1"
							maxOccurs="1"/>
						<xs:element name="NewExternalOrderId" type="xs:string" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="TagCloud" type="xs:string" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="CreateProof" type="xs:boolean" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="Email" type="xs:string" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="AutoApprove" type="xs:boolean" minOccurs="0"
							maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="CreateConnectedOrderResponse" type="mp:CreateConnectedOrderResponseType" />
	
	<xs:element name="CloneConnectedOrderRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="ExternalOrderId" type="xs:string" minOccurs="1"
							maxOccurs="1" />
						<xs:element name="NewExternalOrderId" type="xs:string" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="AuthorUsername" type="xs:string" minOccurs="1"
							maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="CloneConnectedOrderResponse" type="mp:CloneConnectedOrderResponseType" />
	
	<xs:element name="RemoveConnectedOrderRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="RequesterUsername" type="xs:string"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="ExternalOrderIdList" type="mp:ExternalOrderIdListType"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="RemoveConnectedOrderResponse" type="mp:SimpleResponseType" />
		
	<xs:element name="ReleaseForApprovalOrActivateConnectedOrderRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="ExternalOrderId" type="xs:string" minOccurs="1"
							maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="ReleaseForApprovalOrActivateConnectedOrderResponse" type="mp:SimpleResponseType" />
	
	<xs:element name="ApproveConnectedOrderRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="ExternalOrderId" type="xs:string" minOccurs="1"
							maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="ApproveConnectedOrderResponse" type="mp:SimpleResponseType" />
	
	<xs:element name="InitiateConnectedOrderDeliveryRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="ExternalOrderIdList" type="mp:ExternalOrderIdListType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="Activate" type="xs:boolean"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="TouchpointCollection" type="mp:TouchpointCollectionType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="StartDate" type="xs:dateTime" 
						minOccurs="0"	maxOccurs="1" />
						<xs:element name="EndDate" type="xs:dateTime" 
						minOccurs="0"	maxOccurs="1" />
						<xs:element name="IncludedOrders" type="mp:IncludeConnectedOrderType"
							minOccurs="0" maxOccurs="1"/>
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="InitiateConnectedOrderDeliveryResponse" type="mp:SimpleResponseType" />

	<xs:element name="ConnectedOrderListRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="RequesterUsername" type="xs:string"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="AuthorUsername" type="xs:string"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="AssigneeUsername" type="xs:string"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="ExternalOrderIdList" type="mp:ExternalOrderIdListType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="Status" type="mp:ConnectedOrderStatusType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="ExplicitResponse" type="xs:boolean" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="IncludeTestOrders" type="xs:boolean" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="JobStatusStateToInclude" type="xs:string" minOccurs="0"
							maxOccurs="1"/>
						<xs:element name="SlimResponse" type="xs:boolean" minOccurs="0"
							maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="ConnectedOrderListResponse" type="mp:ObjectListResponseType" />
	
	<xs:element name="ProjectListRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="GuidList" type="mp:GuidListType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="Status" type="mp:ProjectStatusType"
							minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="ProjectListResponse" type="mp:ObjectListResponseType" />
	
	<xs:element name="ProjectAuditReportRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Guid" type="xs:string"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="ProjectAuditReportResponse" type="mp:FileResponseType" />

	<xs:element name="JobStatusRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="JobId" type="xs:long" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="ConnectedOrderExternalId" type="xs:string" minOccurs="0"
							maxOccurs="1"/>
						<xs:element name="ConnectedOrderStateToInclude" type="xs:string" minOccurs="0"
							maxOccurs="1"/>	
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="JobStatusResponse" type="mp:JobStatusResponseType" />
	
	<xs:element name="EditTaskRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Guid" type="xs:string"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="OwnerUsername" type="xs:string" minOccurs="0" maxOccurs="1"/>
						<xs:element name="ReporterUsername" type="xs:string" minOccurs="0" maxOccurs="1"/>
						<xs:element name="Description" type="xs:string"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="Tags" type="xs:string"
									 minOccurs="0" maxOccurs="1" />
						<xs:element name="DueDate" type="xs:date"
							minOccurs="0" maxOccurs="1" />						
						<xs:element name="HoursBeforeReminderIsSent" type="xs:long"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="Touchpoint" type="mp:TouchpointType" minOccurs="0" maxOccurs="unbounded" />
						<xs:element name="Message" type="xs:string" minOccurs="0" maxOccurs="1"/>
						<xs:element name="LocalSmartText" type="xs:string" minOccurs="0" maxOccurs="1"/>
						<xs:element name="LocalImage" type="xs:string" minOccurs="0" maxOccurs="1"/>
						<xs:element name="Variant" type="xs:string" minOccurs="0" maxOccurs="1"/>
						<xs:element name="SmartText" type="xs:string" minOccurs="0" maxOccurs="1"/>
						<xs:element name="ImageLibrary" type="xs:string" minOccurs="0" maxOccurs="1"/>
						<xs:element name="TargetGroup" type="xs:string" minOccurs="0" maxOccurs="1"/>
						<xs:element name="ContentId" type="xs:long" minOccurs="0" maxOccurs="1" />
						<xs:element name="PrimaryLanguageHash" type="xs:string" minOccurs="0" maxOccurs="1" />
						<xs:element name="Locale" type="xs:string" minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="EditTaskResponse" type="mp:SimpleResponseType" />
	
	<xs:element name="RemoveTaskRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="GuidList" type="mp:GuidListType"
							minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="RemoveTaskResponse" type="mp:SimpleResponseType" />
	
	<xs:element name="TaskListRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Id" type="xs:long"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="Touchpoint" type="mp:TouchpointType"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="Status" type="xs:string" minOccurs="0" maxOccurs="1"/>
						<xs:element name="OwnerUsername" type="xs:string" minOccurs="0" maxOccurs="1"/>
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="TaskListResponse" type="mp:ObjectListResponseType" />
	
	<xs:element name="JobPerformanceReportRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="TypeList" type="xs:string" minOccurs="0" maxOccurs="1" />
						<xs:element name="StatusList" type="xs:string" minOccurs="0" maxOccurs="1" />	
						<xs:element name="FromDate" type="xs:date" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="ToDate" type="xs:date" minOccurs="0"
							maxOccurs="1" />
						<xs:element name="ExtensionType" type="xs:unsignedInt"
							minOccurs="0" maxOccurs="1" />
						<xs:element name="JobId" type="xs:unsignedInt"
									minOccurs="0" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="JobPerformanceReportResponse" type="mp:BackgroundTaskIdentificationResponseType" />


	<xs:element name="EditTestRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="Guid" type="xs:string" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="Name" type="xs:string" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="Touchpoint" type="mp:TouchpointType"
									minOccurs="0" maxOccurs="1" />
						<xs:element name="TestData" type="mp:DataResourceType" minOccurs="0" maxOccurs="1" />
						<xs:element name="TestDate" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
						<xs:element name="AnalysisMode" type="xs:boolean" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="UseAllWorkCopies" type="xs:boolean" minOccurs="0"
									maxOccurs="1" />
						<xs:element name="UseAllWorkCopiesForLibAssets" type="xs:boolean" minOccurs="0"
									maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="EditTestResponse" type="mp:SimpleResponseType" />

	<xs:element name="RemoveTestRequest">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="mp:ClientIdentificationType">
					<xs:sequence>
						<xs:element name="GuidList" type="mp:GuidListType"
									minOccurs="1" maxOccurs="1" />
					</xs:sequence>
				</xs:extension>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>
	<xs:element name="RemoveTestResponse" type="mp:SimpleResponseType" />

	<!-- List of objects that can be used in requests and responses -->
	<xs:complexType name="CompositionPackageType">
		<xs:all>
			<xs:element name="PackageName" type="xs:string" minOccurs="0"
				maxOccurs="1" />
			<xs:element name="CFTemplateFile" type="mp:UploadFileType"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="CompositionFile" type="mp:UploadFileType"
				minOccurs="0" maxOccurs="1" />
		</xs:all>
	</xs:complexType>
	<xs:complexType name="FileListType">
		<xs:sequence>
			<xs:element name="FileName" type="xs:string" minOccurs="1"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MessageListType">
		<xs:sequence>
			<xs:element name="Message" type="mp:MessageType"
				minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MessageType">
		<xs:sequence>
			<xs:choice>
				<xs:element name="InstanceId" type="xs:long" />
				<xs:element name="ModelId" type="xs:long" />
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TouchpointType">
		<xs:all>
			<xs:element name="Name" type="xs:string" minOccurs="0"
				maxOccurs="1" />
			<xs:element name="Guid" type="xs:string" minOccurs="0"
				maxOccurs="1" />
		</xs:all>
	</xs:complexType>

	<xs:complexType name="TouchpointCollectionType">
		<xs:all>
			<xs:element name="Name" type="xs:string" minOccurs="0"
				maxOccurs="1" />
			<xs:element name="Guid" type="xs:string" minOccurs="0"
				maxOccurs="1" />
		</xs:all>
	</xs:complexType>

	<xs:complexType name="DataResourceType">
		<xs:all>
			<xs:element name="Name" type="xs:string" minOccurs="0"
						maxOccurs="1" />
			<xs:element name="Guid" type="xs:string" minOccurs="0"
						maxOccurs="1" />
		</xs:all>
	</xs:complexType>

	<xs:complexType name="SectionFileType">
		<xs:all>
			<xs:element name="Name" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="Number" type="xs:int" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="Filename" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="Base64Content" type="xs:string"
				minOccurs="1" maxOccurs="1" />
		</xs:all>
	</xs:complexType>

	<xs:complexType name="UploadFileType">
		<xs:all>
			<xs:element name="Filename" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="Base64Content" type="xs:string"
				minOccurs="1" maxOccurs="1" />
		</xs:all>
	</xs:complexType>
	<xs:complexType name="ObjectListType">
		<xs:sequence>
			<xs:any minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="count" type="xs:int" />
	</xs:complexType>

	<!-- List of response types -->
	<xs:complexType name="FileResponseType">
		<xs:sequence>
			<xs:choice>
				<xs:element name="FailureResponse" type="xs:string" />
				<xs:sequence>
					<xs:element name="Filename" type="xs:string" />
					<xs:element name="Base64Content" type="xs:string" />
					<xs:element name="ChunkNumber" type="xs:unsignedInt"
						minOccurs="0" maxOccurs="1" />
					<xs:element name="ChunkSize" type="xs:unsignedInt"
								minOccurs="0" maxOccurs="1" />
					<xs:element name="Chunks" type="xs:unsignedInt"
						minOccurs="0" maxOccurs="1" />
				</xs:sequence>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="JobListType">
		<xs:sequence>
			<xs:element name="FailureResponse" type="xs:string"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="Job" type="mp:JobType" minOccurs="0"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="JobType">
		<xs:sequence>
			<xs:element name="Date" type="xs:dateTime" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="Job" type="xs:long" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="DeliveryEvent" type="xs:long" minOccurs="1"
				maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="NodeListType">
		<xs:sequence>
			<xs:element name="Node" type="mp:NodeType" minOccurs="0"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="NodeType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="FriendlyName" type="xs:string"
				minOccurs="1" maxOccurs="1" />
			<xs:element name="Guid" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="Enabled" type="xs:boolean" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="Default" type="xs:boolean" minOccurs="1"
				maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SimpleResponseType">
		<xs:choice>
			<xs:element name="SuccessResponse" type="xs:string" />
			<xs:element name="FailureResponse" type="xs:string" />
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="ObjectListResponseType">
		<xs:sequence>
			<xs:choice>
				<xs:element name="FailureResponse" type="xs:string" />
				<xs:element name="ObjectList" type="mp:ObjectListType" />
				<xs:element name="TouchpointList" type="mp:TouchpointListType" />
				<xs:element name="ConnectedOrderList" type="mp:ConnectedOrderListType" />
				<xs:element name="DataSourceList" type="mp:DataSourceListType" />
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="ObjectCreateResponseType">
		<xs:sequence>
			<xs:choice>
				<xs:element name="FailureResponse" type="xs:string" />
				<xs:element name="NewObjects" type="mp:ObjectListType" />
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UserType">
		<xs:sequence>
			<xs:element name="FirstName" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="LastName" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="Email" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="Role" type="xs:string" minOccurs="0"
				maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TargetGroupsSearchResponseType">
		<xs:sequence>
			<xs:element name="FirstName" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="LastName" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="Email" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="Role" type="xs:string" minOccurs="0"
				maxOccurs="1" />
			<xs:element name="SearchString" type="xs:string"
				minOccurs="1" maxOccurs="1" />
			<xs:element name="Advanced" type="xs:boolean" minOccurs="1"
				maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TargetRulesSearchResponseType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="Conditions" type="xs:string" minOccurs="1"
				maxOccurs="1" />
			<xs:element name="URL" type="xs:string" minOccurs="1"
				maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="BackgroundTaskStatusType">
		<xs:choice>
			<xs:element name="BackgroundTaskStatusDetails" type="mp:BackgroundTaskStatusDetailsType" />
			<xs:element name="FailureResponse" type="xs:string" />
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="BackgroundTaskStatusDetailsType">
		<xs:sequence>
			<xs:element name="Id" type="xs:string" minOccurs="1" maxOccurs="1" />
			<xs:element name="Type" type="xs:int" minOccurs="1" maxOccurs="1" />
			<xs:element name="SubType" type="xs:string" minOccurs="1" maxOccurs="1" />
			<xs:element name="Description" type="xs:string" minOccurs="1" maxOccurs="1" />
			<xs:element name="Progress" type="xs:int" minOccurs="1" maxOccurs="1" />
			<xs:element name="IsVisible" type="xs:boolean" minOccurs="1" maxOccurs="1" />
			<xs:element name="IsActive" type="xs:boolean" minOccurs="1" maxOccurs="1" />
			<xs:element name="Complete" type="xs:boolean" minOccurs="1" maxOccurs="1" />
			<xs:element name="Error" type="xs:boolean" minOccurs="1" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="BackgroundTaskIdentificationResponseType">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Id" type="xs:string" />
				<xs:element name="FailureResponse" type="xs:string" />
				<xs:element name="SuccessResponse" type="xs:string" />
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CloneConnectedOrderResponseType">
		<xs:sequence>
			<xs:choice>
				<xs:sequence>
					<xs:element name="SuccessResponse" type="xs:string" />
					<xs:element name="NewExternalOrderId" type="xs:string" minOccurs="1" maxOccurs="1" />
				</xs:sequence>
				<xs:element name="FailureResponse" type="xs:string" />
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="IncludeConnectedOrderType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NEW_PRODUCTION" />
			<xs:enumeration value="PRODUCTION" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ConnectedOrderStatusType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="WORKING_COPY" />
			<xs:enumeration value="PENDING_APPROVAL" />
			<xs:enumeration value="ACTIVE" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:complexType name="ExternalOrderIdListType">
		<xs:sequence>
			<xs:element name="ExternalOrderId" type="xs:string" minOccurs="1"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="CreateConnectedOrderResponseType">
		<xs:sequence>
			<xs:choice>
				<xs:sequence>
					<xs:element name="SuccessResponse" type="xs:string" />
					<xs:element name="NewExternalOrderId" type="xs:string" minOccurs="1" maxOccurs="1" />
				</xs:sequence>
				<xs:element name="FailureResponse" type="xs:string" />
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="ConnectedOrderDataPointListType">
		<xs:sequence>
			<xs:element name="DataPoint" type="mp:ConnectedOrderDataPointType"
				minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="ConnectedOrderDataPointType">
		<xs:sequence>
			<xs:choice>
				<xs:element name="Connector" type="xs:string" minOccurs="1" maxOccurs="1"/>
				<xs:element name="Value" type="xs:string" minOccurs="1" maxOccurs="1"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="GuidListType">
		<xs:sequence>
			<xs:element name="Guid" type="xs:string" minOccurs="1"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:simpleType name="ProjectStatusType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="COMPLETE" />
			<xs:enumeration value="OVERDUE" />
			<xs:enumeration value="ACTIVE" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:complexType name="TouchpointListType">
		<xs:sequence>
			<xs:element name="Touchpoint" type="mp:TouchpointInfoType" minOccurs="0"
				maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="count" type="xs:int" />
	</xs:complexType>

	<xs:complexType name="TouchpointInfoType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Guid" type="xs:string"/>
			<xs:element name="Connected" type="xs:boolean"/>
			<xs:element name="SelectionType" type="xs:string"/>
			<xs:element name="Channel" type="xs:string"/>
			<xs:element name="OmniChannel" type="xs:boolean"/>
			<xs:element name="Channels" type="mp:ChannelsListType" minOccurs="0"/>
			<xs:element name="Connector" type="xs:string"/>
			<xs:element name="Description" type="xs:string"/>
			<xs:element name="ProductionChangeStatus" type="xs:string"/>
			<xs:element name="Metadata" type="mp:MetadataType"/>
		</xs:sequence>
	</xs:complexType>

    <xs:complexType name="ChannelsListType">
	<xs:sequence>
			<xs:element name="Channel" type="mp:ChannelsInfoType"/>
		</xs:sequence>
	</xs:complexType>     
	
	<xs:complexType name="ChannelsInfoType">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="id" type="xs:string"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

    <xs:complexType name="MetadataType">
		<xs:sequence>
			<xs:element name="Touchpoint" type="xs:string"/>
			<xs:element name="Variant" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>     
	
	<xs:complexType name="ConnectedOrderListType">
		<xs:sequence>
			<xs:element name="Order" type="mp:ConnectedOrderType" minOccurs="0"
				maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="count" type="xs:int" />
	</xs:complexType>
	
	<xs:complexType name="ConnectedOrderType">
		<xs:sequence>
			<xs:element name="ExternalOrderId" type="xs:string"/>
			<xs:element name="AuthorUsername" type="xs:string" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AuthorInfo" type="mp:UserInfoType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Assigned" type="xs:string" minOccurs="0" maxOccurs="1"/>
			<xs:element name="PrimaryIdentifier" type="xs:string" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Status" type="xs:string" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Channel" type="xs:string" minOccurs="0" maxOccurs="1"/>
			<xs:element name="CreatedDate" type="xs:string" minOccurs="0" maxOccurs="1"/>
			<xs:element name="LastActivationDate" type="xs:string" minOccurs="0" maxOccurs="1"/>
			<xs:element name="AwaitingApprovalFrom" type="mp:UserInfoListType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="ApprovedBy" type="mp:UserInfoListType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="FinalApprover" type="xs:string" minOccurs="0" maxOccurs="1"/>
			<xs:element name="LastProductionDate" type="xs:string" minOccurs="0" maxOccurs="1"/>
			<xs:element name="tpGuid" type="xs:string" minOccurs="0" maxOccurs="1"/>
			<xs:element name="isTestOrder" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
			<xs:element name="OrderData" type="mp:ConnectedOrderDataPointInfoListType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="JobStatus" type="mp:JobStatusType" minOccurs="0" maxOccurs="1" />
			<xs:element name="Error" type="xs:string" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="UserInfoListType">
		<xs:sequence>
			<xs:element name="UserInfo" type="mp:UserInfoType"
						minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="UserInfoType">
		<xs:sequence>
			<xs:element name="Guid" type="xs:string"/>
			<xs:element name="FirstName" type="xs:string"/>
			<xs:element name="LastName" type="xs:string"/>
			<xs:element name="Email" type="xs:string"/>
			<xs:element name="UserName" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
		
	 <xs:complexType name="ConnectedOrderDataPointInfoListType">
	 	<xs:sequence>
			<xs:element name="DataPoint" type="mp:ConnectedOrderDataPointInfoType"
				minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="ConnectedOrderDataPointInfoType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Connector" type="xs:string"/>
			<xs:element name="Value" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>  
	
	<xs:complexType name="JobStatusResponseType">
		<xs:sequence>
			<xs:choice>
				<xs:sequence>
					<xs:element name="Status" type="mp:JobStatusType" minOccurs="1" maxOccurs="1" />
					<xs:element name="ErrorMessage" type="xs:string" minOccurs="0" maxOccurs="1" />
				</xs:sequence>
				<xs:element name="FailureResponse" type="xs:string" />
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="JobStatusType">
		<xs:sequence>
			<xs:element name="Code" type="xs:long" minOccurs="0" maxOccurs="1"/>
			<xs:element name="Description" type="xs:string" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>  
	
	<xs:complexType name="ReferenceDataFileListType">
		<xs:sequence>
			<xs:element name="ReferenceDataFile" type="mp:ReferenceDataFileType"
				minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="ReferenceDataFileType">
		<xs:sequence>
			<xs:choice>
				<xs:element name="ReferenceDataSourceName" type="xs:string"
							minOccurs="1" maxOccurs="1" />
				<xs:element name="ReferenceDataFileName" type="xs:string"
							minOccurs="1" maxOccurs="1" />
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="DataSourceListType">
		<xs:sequence>
			<xs:element name="DataSource" type="mp:DataSourceType" minOccurs="0"
				maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="count" type="xs:int" />
	</xs:complexType>
	
	<xs:complexType name="DataSourceType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Guid" type="xs:string"/>
			<xs:element name="SourceType" type="xs:string" minOccurs="0" maxOccurs="1"/>
			<xs:element name="DataElementList" type="mp:DataElementListType" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
	</xs:complexType>   
	
	 <xs:complexType name="DataElementListType">
	 	<xs:sequence>
			<xs:element name="DataElement" type="mp:DataElementType"
				minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="DataElementType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Type" type="xs:string"/>
			<xs:element name="IsReferenced" type="xs:boolean"/>
		</xs:sequence>
	</xs:complexType>  
		
	<xs:complexType name="TaskListType">
		<xs:sequence>
			<xs:element name="Task" type="mp:TaskType" minOccurs="0"
				maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="count" type="xs:int" />
	</xs:complexType>
	
	<xs:complexType name="TaskType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Guid" type="xs:string"/>
			<xs:element name="Status" type="xs:string"/>
			<xs:element name="Assignee" type="xs:string"/>
			<xs:element name="Due" type="xs:string"/>
			<xs:element name="Created" type="xs:string"/>
			<xs:element name="Type" type="xs:string"/>
			<xs:element name="Project" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="ScheduleType">
		<xs:sequence>
			<xs:element name="StartDateTime" type="xs:dateTime" minOccurs="1"
						maxOccurs="1" />
			<xs:element name="RepeatEvent" type="xs:boolean" minOccurs="0"
						maxOccurs="1" />
			<xs:element name="EndDate" type="xs:date" minOccurs="0"
						maxOccurs="1" />
			<xs:element name="Frequency" type="xs:int" minOccurs="0"
						maxOccurs="1" />
			<xs:element name="DailyFrequency" type="xs:int" minOccurs="0"
						maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="KeyValueListResponseType">
		<xs:sequence>
			<xs:choice>
				<xs:element name="FailureResponse" type="xs:string" />
				<xs:element name="Properties" type="mp:KeyValueType" />
			</xs:choice>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="KeyValueType">
		<xs:sequence>
			<xs:any minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="count" type="xs:int" />
	</xs:complexType>

	<xs:complexType name="ApiSystemSettingsType">
		<xs:sequence>
			<xs:element name="ChunkedUploadEnabled" type="xs:boolean" minOccurs="0"
						maxOccurs="1" />
			<xs:element name="UploadChunkSize" type="xs:unsignedInt" minOccurs="0"
						maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
</xs:schema>
