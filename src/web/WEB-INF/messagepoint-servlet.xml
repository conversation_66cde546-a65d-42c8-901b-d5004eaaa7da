<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:mvc="http://www.springframework.org/schema/mvc"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-4.2.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd">

	<context:component-scan base-package="com.prinova.messagepoint.controller"/>
	<mvc:annotation-driven />
	
	<!-- ========================== WEB DEFINITIONS ======================= -->

	<!-- Common Beans -->	
    <bean id="applicationSecurityManager" class="com.prinova.messagepoint.util.ApplicationSecurityManager"/>
	<bean id="csrfSecurityController" class="com.prinova.messagepoint.security.controller.CSRFSecurityController" />
	
	<bean id="displayMessageController" class="com.prinova.messagepoint.controller.DisplayMessageController" >
		<property name="formView" value="displaymessage"/>
	</bean>
	
	<bean id="contentImportController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointImportController" >
		<property name="formView" value="content/content_import"/>
		<property name="successView" value="bulk_upload_confirm.form"/>
		<property name="validator" ref="touchpointImportValidator" />
	</bean>
	
	<bean id="contentHistoryController" class="com.prinova.messagepoint.controller.content.ContentHistoryController" >
		<property name="formView" value="content/content_history"/>
	</bean>	

	<!-- Embedded Content -->
	<bean id="globalContentListController" class="com.prinova.messagepoint.controller.content.GlobalContentListController">
		<property name="successView" value="global_content_list.form" />
		<property name="contentObjectEditRedirect" value="content_object_edit.form"/>
		<property name="formView" value="content/global_content_list" />
		<property name="validator" ref="globalContentListValidator"/>
	</bean>
	<bean id="globalContentListValidator" class="com.prinova.messagepoint.controller.content.GlobalContentListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.content.GlobalContentListWrapper" />
	</bean>

	<bean id="globalContentWorkflowEditController" class="com.prinova.messagepoint.controller.workflow.WorkflowEditController">
		<property name="formView" value="workflow/workflow_edit"/>
		<property name="successView" value="global_content_list.form"/>
		<property name="validator" ref="globalContentWorkflowEditValidator" />
	</bean>
	<bean id="globalContentWorkflowEditValidator" class="com.prinova.messagepoint.controller.workflow.WorkflowEditValidator" />

	<bean id="contentObjectTouchpointAssignmentEditController" class="com.prinova.messagepoint.controller.content.ContentObjectTouchpointAssignmentEditController">
		<property name="formView" value="content/content_object_touchpoint_assignment"/>
		<property name="successView" value="content_object_touchpoint_assignment.form"/>
		<property name="validator" ref="contentObjectTouchpointAssignmentEditValidator" />
	</bean>
	<bean id="contentObjectTouchpointAssignmentEditValidator" class="com.prinova.messagepoint.controller.content.ContentObjectTouchpointAssignmentEditValidator" />

	<bean id="contentObjectReviewPopupController" class="com.prinova.messagepoint.controller.content.ContentObjectReviewPopupController">
		<property name="formView" value="content/content_object_review_popup" />
	</bean>

	<bean id="contentObjectReviewPopupContentController" class="com.prinova.messagepoint.controller.content.ContentObjectReviewPopupContentController">
		<property name="formView" value="content/content_object_review_popup_content" />
	</bean>

	<!-- Content Library -->
	<bean id="contentObjectSftpImageUploadController" class="com.prinova.messagepoint.controller.content.ContentObjectSftpImageUploadController">
		<property name="formView" value="content/content_object_sftp_image_upload" />
		<property name="successView" value="global_content_list.form" />
	</bean>

	<bean id="userSettingsValidator" class="com.prinova.messagepoint.controller.admin.UserSettingsValidator"/>
	<bean id="messagePointDeletableValidator" class="com.prinova.messagepoint.controller.DeleteCommandController$Validator"/>


	<!-- Target Group Validators -->
	<bean id="targetGroupValidator" class="com.prinova.messagepoint.controller.targeting.TargetGroupValidator">
		<property name="className" value="com.prinova.messagepoint.model.wrapper.TargetGroupWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="nameText" />
        			<property name="label" value="page.label.targetgroup.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
	       	</list>
		</property>
	</bean>

	<!-- Content Object Flow Validators -->
	<bean id="ContentObjectDetailsEditValidator" class="com.prinova.messagepoint.controller.content.ContentObjectDetailsEditValidator">
		<property name="className" value="com.prinova.messagepoint.model.wrapper.ContentObjectOverviewWrapper" />
		<property name="validationEntries">
			<list>
				<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
					<property name="propertyName" value="contentObject.name" />
					<property name="label" value="page.label.name" />
					<property name="propertyType" ref="ObjectName" />
				</bean>
				<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
					<property name="propertyName" value="contentObject.description" />
					<property name="label" value="page.label.description" />
					<property name="propertyType" ref="Description" />
				</bean>
				<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
					<property name="propertyName" value="contentObject.newComment.comment" />
					<property name="label" value="page.label.comments" />
					<property name="propertyType" ref="Comment" />
				</bean>
			</list>
		</property>
	</bean>
	<bean id="ContentObjectContentEditValidator" class="com.prinova.messagepoint.controller.content.ContentObjectContentEditValidator"/>

	<!-- Content Object -->
	<bean id="contentObjectDetailsEditController" class="com.prinova.messagepoint.controller.content.ContentObjectDetailsEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.content.ContentObject" />
		<property name="parameter" value="contentObjectId" />
		<property name="formView" value="content/content_object_edit"/>
		<property name="formViewRedirect" value="content_object_edit.form" />
		<property name="validator" ref="ContentObjectDetailsEditValidator"/>
	</bean>

	<bean id="contentObjectContentEditController" class="com.prinova.messagepoint.controller.content.ContentObjectContentEditController">
		<property name="formView" value="content/content_object_edit_content"/>
		<property name="validator" ref="ContentObjectContentEditValidator"/>
		<property name="persistedClassName" value="com.prinova.messagepoint.model.content.ContentObject" />
		<property name="parameter" value="contentObjectId" />
	</bean>

	<bean id="tinymceTablePropertiesController" class="com.prinova.messagepoint.controller.content.TinymceTablePropertiesController">
		<property name="formView" value="content/tinymce_table_properties"/>
	</bean>
	<bean id="tinymceTableOfContentsPropertiesController" class="com.prinova.messagepoint.controller.content.TinymceTableOfContentsPropertiesController">
		<property name="formView" value="content/tinymce_toc_properties"/>
	</bean>
	<bean id="tinymceCustomListPropertiesController" class="com.prinova.messagepoint.controller.content.TinymceCustomListPropertiesController">
		<property name="formView" value="content/tinymce_custom_list_properties"/>
	</bean>
	<bean id="tinymceCustomParagraphStylesController" class="com.prinova.messagepoint.controller.content.TinymceCustomParagraphStylesController">
		<property name="formView" value="content/tinymce_custom_paragraph_styles"/>
	</bean>
	<bean id="tinymceConnectedCustomListPropertiesController" class="com.prinova.messagepoint.controller.content.TinymceConnectedCustomListPropertiesController">
		<property name="formView" value="content/tinymce_connected_custom_list_properties"/>
	</bean>
	<bean id="tinymceConnectedDocumentHistoryController" class="com.prinova.messagepoint.controller.content.TinymceConnectedDocumentHistoryController">
		<property name="formView" value="content/tinymce_connected_document_history"/>
	</bean>
	<bean id="tinymceConnectedCustomParagraphStylesController" class="com.prinova.messagepoint.controller.content.TinymceConnectedCustomParagraphStylesController">
		<property name="formView" value="content/tinymce_connected_custom_paragraph_styles"/>
	</bean>
	<bean id="tinymceTextFieldPropertiesController" class="com.prinova.messagepoint.controller.content.TinymceFormElementPropertiesController">
		<property name="formView" value="content/tinymce_text_field_properties"/>
	</bean>
	<bean id="tinymceMenuPropertiesController" class="com.prinova.messagepoint.controller.content.TinymceFormElementPropertiesController">
		<property name="formView" value="content/tinymce_menu_properties"/>
	</bean>
	<bean id="tinymceCheckboxRadioPropertiesController" class="com.prinova.messagepoint.controller.content.TinymceFormElementPropertiesController">
		<property name="formView" value="content/tinymce_checkbox_radio_properties"/>
	</bean>
	<bean id="tinymceSubmitButtonPropertiesController" class="com.prinova.messagepoint.controller.content.TinymceFormElementPropertiesController">
		<property name="formView" value="content/tinymce_submit_button_properties"/>
	</bean>
	<bean id="tinymceImageUploadController" class="com.prinova.messagepoint.controller.content.TinymceImageUploadController">
		<property name="formView" value="content/tinymce_image_upload"/>
	</bean>
	<bean id="tinymceImagePropertiesController" class="com.prinova.messagepoint.controller.content.TinymceImagePropertiesController">
		<property name="formView" value="content/tinymce_image_properties"/>
	</bean>
	<bean id="tinymceConnectedImagePropertiesController" class="com.prinova.messagepoint.controller.content.TinymceConnectedImagePropertiesController">
		<property name="formView" value="content/tinymce_connected_image_properties"/>
	</bean>
	<bean id="tinymceContentMenuController" class="com.prinova.messagepoint.controller.content.TinymceContentMenuController">
		<property name="formView" value="content/tinymce_content_menu"/>
	</bean>
	<bean id="tinymceLinksController" class="com.prinova.messagepoint.controller.content.TinymceLinksController">
		<property name="formView" value="content/tinymce_links"/>
	</bean>
	<bean id="tinymceParagraphPropertiesController" class="com.prinova.messagepoint.controller.content.TinymceParagraphPropertiesController">
		<property name="formView" value="content/tinymce_paragraph_properties"/>
	</bean>
	<bean id="tinymceBarcodesController" class="com.prinova.messagepoint.controller.content.TinymceBarcodesController">
		<property name="formView" value="content/tinymce_barcodes"/>
	</bean>
	<bean id="tinymceMarkEditablePropertiesController" class="com.prinova.messagepoint.controller.content.TinymceMarkEditablePropertiesController">
		<property name="formView" value="content/tinymce_mark_editable_properties"/>
	</bean>

	<bean id="contentObjectTargetingEditController" class="com.prinova.messagepoint.controller.content.ContentObjectTargetingEditController">
		<property name="formView" value="content/content_object_edit_targeting"/>
		<property name="validator" ref="targetingValidator"/>
		<property name="persistedClassName" value="com.prinova.messagepoint.model.content.ContentObject" />
		<property name="parameter" value="contentObjectId" />
	</bean>

	<bean id="targetingValidator" class="com.prinova.messagepoint.controller.targeting.TargetingValidator" />
	
	<bean id="contentObjectDynamicVariantEditController" class="com.prinova.messagepoint.controller.content.ContentObjectDynamicVariantEditController">
		<property name="formView" value="content/content_object_edit_content_dynamic_variant"/>
		<property name="successView" value="content_object_view_content_dynamic_variant.form" />
		<property name="validator" ref="contentObjectDynamicVariantEditValidator"/>
	</bean>

	<bean id="contentObjectDynamicVariantEditValidator" class="com.prinova.messagepoint.controller.content.ContentObjectDynamicVariantEditValidator">
		<property name="className" value="com.prinova.messagepoint.model.wrapper.ContentObjectContentSelectionUpdateWrapper" />
	</bean>

	<bean id="contentObjectDynamicVariantViewController" class="com.prinova.messagepoint.controller.content.ContentObjectDynamicVariantViewController">
		<property name="formView" value="content/content_object_view_content_dynamic_variant"/>
		<property name="varianceRemoveRedirect" value="content_object_view.form"/>
		<property name="varianceEditRedirect" value="content_object_edit_content_dynamic_variant.form"/>
		<property name="successView" value="content_object_view_content_dynamic_variant.form" />
		<property name="validator" ref="contentObjectDynamicVariantViewValidator"/>
	</bean>

	<bean id="contentObjectDynamicVariantViewValidator" class="com.prinova.messagepoint.controller.content.ContentObjectDynamicVariantViewValidator">
		<property name="className" value="com.prinova.messagepoint.model.wrapper.ContentObjectContentSelectionViewWrapper" />
	</bean>

	<bean id="contentObjectViewController" class="com.prinova.messagepoint.controller.content.ContentObjectViewController">
		<property name="successView" value="content_object_view.form" />
		<property name="messageEditRedirect" value="content_object_edit_content.form"/>
		<property name="messageOverviewEditRedirect" value="content_object_edit.form"/>
		<property name="formView" value="content/content_object_view" />
		<property name="validator" ref="contentObjectViewValidator" />
	</bean>

	<bean id="contentObjectContentViewController" class="com.prinova.messagepoint.controller.content.ContentObjectContentViewController">
		<property name="successView" value="content_object_view_content.form" />
		<property name="messageEditRedirect" value="content_object_edit_content.form"/>
		<property name="formView" value="content/content_object_view_content" />
		<property name="messageOverviewEditRedirect" value="content_object_edit.form"/>
		<property name="validator" ref="contentObjectViewValidator" />
	</bean>

	<bean id="contentObjectTargetingViewController" class="com.prinova.messagepoint.controller.content.ContentObjectTargetingViewController">
		<property name="successView" value="content_object_view_targeting.form" />
		<property name="messageEditRedirect" value="content_object_edit_targeting.form"/>
		<property name="formView" value="content/content_object_view_targeting" />
		<property name="messageOverviewEditRedirect" value="content_object_edit.form"/>
		<property name="validator" ref="contentObjectViewValidator" />
	</bean>

	<bean id="contentObjectViewValidator" class="com.prinova.messagepoint.controller.content.ContentObjectViewValidator" />

	<!-- Dynamic Variant Tree Controller Beans -->
	<bean id="dynamicVariantPopupContentController" class="com.prinova.messagepoint.controller.content.DynamicVariantPopupContentController">
		<property name="successView" value="content/dynamic_variant_popup_content.form"/>
		<property name="formView" value="content/dynamic_variant_popup_content"/>
	</bean>

	<!-- Inserts Controller Beans -->
	<bean id="insertListController" class="com.prinova.messagepoint.controller.insert.InsertListController">
		<property name="successView" value="insert_list.form" />
		<property name="formView" value="insert/insert_list" />	
		<property name="validator" ref="insertListValidator"/>	
	</bean>
	<bean id="insertListDetailController" class="com.prinova.messagepoint.controller.insert.InsertListDetailController">
		<property name="formView" value="insert/insert_list_detail" />
	</bean>
	<bean id="insertViewController" class="com.prinova.messagepoint.controller.insert.InsertViewController">
		<property name="successView" value="insert_view.form" />
		<property name="formView" value="insert/insert_view"/>
		<property name="editView" value="insert_overview_edit.form"/>
		<property name="validator" ref="insertViewValidator"/>		
	</bean>
	<bean id="insertOverviewEditController" class="com.prinova.messagepoint.controller.insert.InsertOverviewEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.insert.Insert" />
		<property name="parameter" value="insertId" />
		<property name="formView" value="insert/insert_overview_edit"/>
		<property name="formViewRedirect" value="insert_view.form" />
		<property name="validator" ref="insertOverviewEditValidator"/>
	</bean>
	
	<bean id="insertTargetingViewController" class="com.prinova.messagepoint.controller.insert.InsertTargetingViewController">
		<property name="successView" value="insert_targeting_view.form" />
		<property name="editView" value="insert_targeting_edit.form"/>
		<property name="formView" value="insert/insert_targeting_view" />
		<property name="validator" ref="insertViewValidator" />	
	</bean>
	<bean id="insertTargetingEditController" class="com.prinova.messagepoint.controller.insert.InsertTargetingEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.insert.Insert" />
		<property name="parameter" value="insertId" />
		<property name="formView" value="insert/insert_targeting_edit"/>
		<property name="formViewRedirect" value="insert_view.form" />
		<property name="validator" ref="targetingValidator"/>
	</bean>
	<bean id="insertContentEditController" class="com.prinova.messagepoint.controller.insert.InsertContentEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.insert.Insert" />
		<property name="parameter" value="insertId" />
		<property name="formView" value="insert/insert_content_edit"/>
		<property name="formViewRedirect" value="insert_view.form" />
	</bean>
	
	<!-- Insert Validator Beans -->
	<bean id="insertListValidator" class="com.prinova.messagepoint.controller.insert.InsertListValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.insert.InsertListWrapper" />
		<property name="validationEntries">
	       	<list>
        		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="userNote" />
        			<property name="label" value="page.label.note" />
        			<property name="propertyType" ref="Description" />
        		</bean>
	       	</list>
		</property>
	</bean>
	<bean id="insertViewValidator" class="com.prinova.messagepoint.controller.insert.InsertViewValidator">
		<property name="className" value="com.prinova.messagepoint.controller.insert.InsertViewWrapper" />
	</bean>
	<bean id="insertOverviewEditValidator" class="com.prinova.messagepoint.controller.insert.InsertOverviewEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.insert.InsertOverviewEditWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="stockId" />
        			<property name="label" value="page.label.stock.id" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="48" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.dash.underscore" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\-_]*+" />
        		</bean>        		
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="description" />
        			<property name="label" value="page.label.description" />
        			<property name="propertyType" ref="Description" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="weight" />
        			<property name="label" value="page.label.weight" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="10" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9.]*+" />
        		</bean>
	       	</list>
		</property>
	</bean>

	<!-- Insert Schedule Controller Beans -->
	<bean id="insertScheduleListController" class="com.prinova.messagepoint.controller.insert.InsertScheduleListController">
		<property name="successView" value="insert_schedule_list.form" />
		<property name="formView" value="insert/insert_schedule_list" />	
		<property name="editView" value="insert_schedule_overview_edit.form"/>
		<property name="validator" ref="insertScheduleListValidator"/>		
	</bean>
	<bean id="insertScheduleListDetailController" class="com.prinova.messagepoint.controller.insert.InsertScheduleListDetailController">
		<property name="formView" value="insert/insert_schedule_list_detail" />
	</bean>
	<bean id="insertScheduleViewController" class="com.prinova.messagepoint.controller.insert.InsertScheduleOverviewViewController">
		<property name="successView" value="insert_schedule_view.form" />
		<property name="formView" value="insert/insert_schedule_view"/>
		<property name="editView" value="insert_schedule_overview_edit.form"/>
		<property name="validator" ref="insertScheduleViewValidator"/>		
	</bean>
	<bean id="insertScheduleOverviewEditController" class="com.prinova.messagepoint.controller.insert.InsertScheduleOverviewEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.insert.InsertSchedule" />
		<property name="parameter" value="insertSchedId" />
		<property name="formView" value="insert/insert_schedule_overview_edit"/>
		<property name="formViewRedirect" value="insert_schedule_view.form" />
		<property name="validator" ref="insertScheduleOverviewEditValidator"/>
	</bean>
	<bean id="insertScheduleRateScheduleEditController" class="com.prinova.messagepoint.controller.insert.InsertScheduleRateScheduleEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.insert.InsertSchedule" />
		<property name="parameter" value="insertSchedId" />
		<property name="formView" value="insert/insert_schedule_rate_schedule_edit"/>
		<property name="formViewRedirect" value="insert_schedule_view.form" />
		<property name="validator" ref="insertScheduleRateScheduleEditValidator"/>
	</bean>
	<bean id="insertScheduleSelectorEditController" class="com.prinova.messagepoint.controller.insert.InsertScheduleSelectorEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.insert.InsertSchedule" />
		<property name="parameter" value="insertSchedId" />
		<property name="formView" value="insert/insert_schedule_selector_edit"/>
		<property name="formViewRedirect" value="insert_schedule_view.form" />
		<property name="validator" ref="insertScheduleSelectorEditValidator"/>
	</bean>
	<bean id="insertScheduleBinAssignmentEditController" class="com.prinova.messagepoint.controller.insert.InsertScheduleBinAssignmentEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.insert.InsertSchedule" />
		<property name="parameter" value="insertSchedId" />
		<property name="formView" value="insert/insert_schedule_bin_assignment_edit"/>
		<property name="formViewRedirect" value="insert_schedule_view.form" />
	</bean>
	<bean id="insertScheduleInsertTimingEditController" class="com.prinova.messagepoint.controller.insert.InsertScheduleInsertTimingEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.insert.InsertSchedule" />
		<property name="parameter" value="insertSchedId" />
		<property name="formView" value="insert/insert_schedule_insert_timing_edit"/>
		<property name="formViewRedirect" value="insert_schedule_view.form" />
		<property name="validator" ref="insertScheduleInsertTimingEditValidator"/>
	</bean>
	
	<!-- Insert Schedule Validator Beans -->
	<bean id="insertScheduleListValidator" class="com.prinova.messagepoint.controller.insert.InsertScheduleListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.insert.InsertScheduleListWrapper" />
	</bean>
	<bean id="insertScheduleViewValidator" class="com.prinova.messagepoint.controller.insert.InsertScheduleViewValidator">
		<property name="className" value="com.prinova.messagepoint.controller.insert.InsertScheduleViewWrapper" />
	</bean>
	<bean id="insertScheduleOverviewEditValidator" class="com.prinova.messagepoint.controller.insert.InsertScheduleOverviewEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.insert.InsertScheduleOverviewEditWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="description" />
        			<property name="label" value="page.label.description" />
        			<property name="propertyType" ref="Description" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="scheduleId" />
        			<property name="label" value="page.label.externalid" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="48" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.dash.underscore" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\-_]*+" />
        		</bean>        		
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="keywords" />
        			<property name="label" value="page.label.description" />
        			<property name="propertyType" ref="Description" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="numberOfBins" />
        			<property name="label" value="page.label.header.records" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="2" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
	       	</list>
		</property>
	</bean>
	<bean id="insertScheduleRateScheduleEditValidator" class="com.prinova.messagepoint.controller.insert.InsertScheduleRateScheduleEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.insert.InsertScheduleRateScheduleEditWrapper" />
	</bean>
	<bean id="insertScheduleSelectorEditValidator" class="com.prinova.messagepoint.controller.insert.InsertScheduleSelectorEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.insert.InsertScheduleSelectorEditWrapper" />
	</bean>
	<bean id="insertScheduleInsertTimingEditValidator" class="com.prinova.messagepoint.controller.insert.InsertScheduleInsertTimingEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.insert.InsertScheduleInsertTimingEditWrapper" />
	</bean>

	<!-- Rate Sheet Controller Beans -->
	<bean id="rateScheduleListController" class="com.prinova.messagepoint.controller.insert.RateScheduleListController">
		<property name="successView" value="rate_schedule_list.form" />
		<property name="formView" value="insert/rate_schedule_list" />	
		<property name="editView" value="rate_schedule_edit.form"/>	
		<property name="validator" ref="rateScheduleListValidator"/>	
	</bean>
	<bean id="rateScheduleListDetailController" class="com.prinova.messagepoint.controller.insert.RateScheduleListDetailController">
		<property name="formView" value="insert/rate_schedule_list_detail" />
	</bean>
	<bean id="rateScheduleViewController" class="com.prinova.messagepoint.controller.insert.RateScheduleViewController">
		<property name="successView" value="rate_schedule_view.form" />
		<property name="formView" value="insert/rate_schedule_view"/>
		<property name="editView" value="rate_schedule_edit.form"/>		
		<property name="discardSuccessView" value="rate_schedule_list.form"/>
		<property name="validator" ref="rateScheduleViewValidator"/>
	</bean>
	<bean id="rateScheduleEditController" class="com.prinova.messagepoint.controller.insert.RateScheduleEditController">
		<property name="formView" value="insert/rate_schedule_edit"/>
		<property name="successView" value="rate_schedule_view.form" />
		<property name="validator" ref="rateScheduleEditValidator"/>
	</bean>

	<!-- Rate Sheet Validator Beans -->
	<bean id="rateScheduleListValidator" class="com.prinova.messagepoint.controller.insert.RateScheduleListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.insert.RateScheduleListController$RateScheduleListWrapper" />
	</bean>
	<bean id="rateScheduleViewValidator" class="com.prinova.messagepoint.controller.insert.RateScheduleViewValidator">
		<property name="className" value="com.prinova.messagepoint.controller.insert.RateScheduleViewWrapper" />
	</bean>
	<bean id="rateScheduleEditValidator" class="com.prinova.messagepoint.controller.insert.RateScheduleEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.insert.RateScheduleEditWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="description" />
        			<property name="label" value="page.label.description" />
        			<property name="propertyType" ref="Description" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="envelopName" />
        			<property name="label" value="page.label.envelope.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="envelopWeight" />
        			<property name="label" value="page.label.envelope.weight" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="10" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9.]*+" />
        		</bean>
	       	</list>
		</property>
	</bean>

	<bean id="scenarioEditValidator" class="com.prinova.messagepoint.controller.reports.ScenarioEditValidator" >
		<property name="className" value="com.prinova.messagepoint.model.scenario.ReportScenario" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<bean id="scenarioOperationsEditValidator" class="com.prinova.messagepoint.controller.reports.ScenarioOperationsEditValidator" >
		<property name="className" value="com.prinova.messagepoint.model.scenario.OperationsReportScenario" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<bean id="scenarioTpDeliveryEditValidator" class="com.prinova.messagepoint.controller.reports.ScenarioTpDeliveryEditValidator" >
		<property name="className" value="com.prinova.messagepoint.model.scenario.TpDeliveryReportScenario" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="reportsListController" class="com.prinova.messagepoint.controller.reports.ReportsListController">
		<property name="formView" value="reports/reports_list"/>
		<property name="scenarioEditRedirect" value="scenario_edit.form" />
		<property name="scenarioOperationsEditRedirect" value="scenario_operations_edit.form" />
		<property name="scenarioTpDeliveryEditRedirect" value="scenario_tp_delivery_edit.form" />
		<property name="successView" value="reports_list.form"/>
	</bean>

	<bean id="reportScenarioViewController" class="com.prinova.messagepoint.controller.reports.ScenarioViewController" >
		<property name="formView" value="reports/scenario_view"/>
		<property name="successView" value="scenario_view.form"/>
	</bean>
		
	<bean id="reportScenarioEditController" class="com.prinova.messagepoint.controller.reports.ScenarioEditController" >
		<property name="formView" value="reports/scenario_edit"/>
		<property name="successView" value="scenario_view.form"/>
		<property name="validator" ref="scenarioEditValidator" />
	</bean>
	
	<bean id="reportScenarioOperationsViewController" class="com.prinova.messagepoint.controller.reports.ScenarioOperationsViewController" >
		<property name="formView" value="reports/scenario_operations_view"/>
		<property name="successView" value="scenario_operations_view.form"/>
	</bean>
		
	<bean id="reportScenarioOperationsEditController" class="com.prinova.messagepoint.controller.reports.ScenarioOperationsEditController" >
		<property name="formView" value="reports/scenario_operations_edit"/>
		<property name="successView" value="scenario_operations_view.form"/>
		<property name="validator" ref="scenarioOperationsEditValidator" />
	</bean>
	
	<bean id="reportScenarioTpDeliveryViewController" class="com.prinova.messagepoint.controller.reports.ScenarioTpDeliveryViewController" >
		<property name="formView" value="reports/scenario_tp_delivery_view"/>
		<property name="successView" value="scenario_tp_delivery_view.form"/>
	</bean>
		
	<bean id="reportScenarioTpDeliveryEditController" class="com.prinova.messagepoint.controller.reports.ScenarioTpDeliveryEditController" >
		<property name="formView" value="reports/scenario_tp_delivery_edit"/>
		<property name="successView" value="scenario_tp_delivery_view.form"/>
		<property name="validator" ref="scenarioTpDeliveryEditValidator" />
	</bean>
	
	<bean id="reportScenarioDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.scenario.ReportScenario" />
		<property name="successView" value="reports_list.form" />
		<property name="formView" value="delete_confirmation"/>
	</bean>	

	<bean id="reportScenarioReportMessageController" class="com.prinova.messagepoint.controller.reports.ReportScenarioReportMessageController" >
		<property name="formView" value="reports/scenario_message_report_view"/>
	</bean>

	<bean id="reportScenarioReportInsertController" class="com.prinova.messagepoint.controller.reports.ReportScenarioReportInsertController" >
		<property name="formView" value="reports/scenario_insert_report_view"/>
	</bean>

	<bean id="scenarioReportMessageController" class="com.prinova.messagepoint.controller.testing.TestScenarioReportMessageController" >
		<property name="formView" value="testing/scenario_message_report_view"/>
	</bean>

	<bean id="scenarioReportInsertController" class="com.prinova.messagepoint.controller.testing.TestScenarioReportInsertController">
		<property name="formView" value="testing/scenario_insert_report_view"/>
	</bean>

	<bean id="scenarioInsertSubTestingViewController" class="com.prinova.messagepoint.testing.ScenarioInsertSubTestingViewController">
		<property name="formView" value="testing/reports_insert_sub"/>
		<property name="successView" value="reports_insert_sub.form" />
	</bean>

	<bean id="simScenarioReportMessageController" class="com.prinova.messagepoint.controller.simulation.SimScenarioReportMessageController" >
		<property name="formView" value="simulations/scenario_message_report_view"/>
	</bean>

	<!-- Simulation -->
		<!-- Request -->
		
	<bean id="simulationsListController" class="com.prinova.messagepoint.controller.simulation.SimulationsListController" >
		<property name="successView" value="simulations_list.form" />
		<property name="formView" value="simulations/simulations_list" />
		<property name="validator" ref="simulationListValidator"/>
	</bean>
	<bean id="simulationListValidator" class="com.prinova.messagepoint.controller.simulation.SimulationsListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.simulation.SimulationsListWrapper" />
	</bean>
			
	<bean id="simulationEditController" class="com.prinova.messagepoint.controller.simulation.SimulationEditController" >
		<property name="formView" value="simulations/simulation_edit"/>
		<property name="successView" value="simulations_list.form"/>
		<property name="validator" ref="simulationEditValidator" />
	</bean>
	
	<bean id="simulationEditValidator" class="com.prinova.messagepoint.controller.simulation.SimulationEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.simulation.SimulationEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>
		<!-- View Report -->
	<bean id="covaregeReportViewController" class="com.prinova.messagepoint.controller.simulation.CoverageReportViewController" >
		<property name="formView" value="simulations/simulation_coverage_report"/>
	</bean>
	<bean id="messageCovaregeReportViewController" class="com.prinova.messagepoint.controller.simulation.MessageCoverageReportViewController" >
		<property name="formView" value="simulations/simulation_message_coverage_report"/>
	</bean>
		<!-- View Message Inclusion -->
	<bean id="simulationMessageInclusionViewController" class="com.prinova.messagepoint.controller.simulation.SimulationMessageInclusionViewController" >
		<property name="formView" value="simulations/simulation_view_inclusions"/>
	</bean>
		<!-- Old Simulation? Unused? -->
	<bean id="messageSimulationReportValidator" class="com.prinova.messagepoint.controller.simulation.MessageSimulationReportValidator"/>
	
	<bean id="messageSimulationController" class="com.prinova.messagepoint.controller.simulation.MessageSimulationController" >
		<property name="formView" value="simulation/simulation_lab"/>
		<property name="successView" value="simulation_lab.form"/>
		<property name="validator" ref="messageSimulationReportValidator" />
	</bean>
	
	<!-- Testing beans -->
	<bean id="testScenarioEditValidator" class="com.prinova.messagepoint.controller.testing.ScenarioEditValidator" >
		<property name="className" value="com.prinova.messagepoint.model.testing.TestScenario" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<bean id="testScenarioEditController" class="com.prinova.messagepoint.controller.testing.ScenarioEditController" >
		<property name="formView" value="testing/scenario_edit"/>
		<property name="successView" value="testing_list.form"/>
		<property name="validator" ref="testScenarioEditValidator" />
	</bean>
	
	<bean id="testScenarioViewController" class="com.prinova.messagepoint.controller.testing.ScenarioViewController" >
		<property name="formView" value="testing/scenario_view"/>
		<property name="editView" value="scenario_edit.form"/>
		<property name="successView" value="scenario_view.form"/>
		<property name="validator" ref="testScenarioViewValidator"/>
	</bean>
	
	<bean id="testScenarioViewValidator" class="com.prinova.messagepoint.controller.testing.ScenarioViewValidator">
		<property name="className" value="com.prinova.messagepoint.controller.testing.ScenarioViewWrapper" />
	</bean>	
	
	<bean id="testSuiteEditValidator" class="com.prinova.messagepoint.controller.testing.TestSuiteEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.testing.TestSuiteEditWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="testSuite.name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<bean id="testSuiteEditController" class="com.prinova.messagepoint.controller.testing.TestSuiteEditController" >
		<property name="formView" value="testing/test_suite_edit"/>
		<property name="successView" value="test_suite_list.form"/>
		<property name="validator" ref="testSuiteEditValidator" />
	</bean>
	
	<bean id="testSuiteViewController" class="com.prinova.messagepoint.controller.testing.TestSuiteViewController" >
		<property name="formView" value="testing/test_suite_view"/>
		<property name="editView" value="test_suite_edit.form"/>
		<property name="successView" value="test_suite_view.form"/>
		<property name="validator" ref="testSuiteViewValidator"/>
	</bean>
	
	<bean id="testSuiteViewValidator" class="com.prinova.messagepoint.controller.testing.TestSuiteViewValidator">
		<property name="className" value="com.prinova.messagepoint.controller.testing.TestSuiteViewWrapper" />
	</bean>	
	
	<bean id="testDataFileValidator" class="com.prinova.messagepoint.controller.testing.DataFileEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.testing.DataFileEditController$DataFileCommand" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="testDataFileController" class="com.prinova.messagepoint.controller.testing.DataFileEditController" >
		<property name="formView" value="dataadmin/data_files_edit"/>
		<property name="successView" value="data_files.form"/>
		<property name="validator" ref="testDataFileValidator" />
	</bean>



	<!--TOUCHPOINT Beans -->
	
	<!-- Touchpoints Exchange -->
	<bean id="touchpointExchangeListController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointExchangeListController">
		<property name="successView" value="touchpoint_exchange_list.form" />
		<property name="formView" value="tpadmin/touchpoint_exchange_list" />	
		<property name="validator" ref="touchpointExchangeListValidator" />
	</bean>
	<bean id="touchpointExchangeListValidator" class="com.prinova.messagepoint.controller.tpadmin.TouchpointExchangeListValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.TouchpointExchangeListWrapper" />
	</bean>	
	
	<!-- Touchpoints: Attachments -->
	<bean id="attachmentOverviewEditController" class="com.prinova.messagepoint.controller.attachment.AttachmentOverviewEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.attachment.Attachment" />
		<property name="parameter" value="attachmentId" />
		<property name="formView" value="attachment/attachment_overview_edit"/>
		<property name="formViewRedirect" value="tpadmin/document_view.form" />
		<property name="validator" ref="attachmentOverviewEditValidator" />
	</bean>
	<bean id="attachmentTargetingEditController" class="com.prinova.messagepoint.controller.attachment.AttachmentTargetingEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.attachment.Attachment" />
		<property name="parameter" value="attachmentId" />
		<property name="formView" value="attachment/attachment_targeting_edit"/>
		<property name="formViewRedirect" value="tpadmin/document_view.form" />
		<property name="validator" ref="targetingValidator"/>
	</bean>
	
	<bean id="attachmentOverviewEditValidator" class="com.prinova.messagepoint.controller.attachment.AttachmentOverviewEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.attachment.AttachmentOverviewEditWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<!-- Touchpoints: Tags -->
	<bean id="tagsListController" class="com.prinova.messagepoint.controller.tag.TagsListController">
		<property name="successView" value="tags_list.form" />
		<property name="formView" value="tpadmin/tags_list" />	
		<property name="editView" value="tag_overview_edit.form"/>	
		<property name="validator" ref="tagListValidator" />
	</bean>	
	<bean id="tagOverviewViewController" class="com.prinova.messagepoint.controller.tag.TagOverviewViewController">
		<property name="successView" value="tag_overview_view.form" />
		<property name="formView" value="tpadmin/tag_overview_view"/>
		<property name="editView" value="tag_overview_edit.form"/>	
		<property name="validator" ref="tagOverviewViewValidator" />
	</bean>
	<bean id="tagOverviewEditController" class="com.prinova.messagepoint.controller.tag.TagOverviewEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.tag.Tag" />
		<property name="parameter" value="tagId" />
		<property name="formView" value="tpadmin/tag_overview_edit"/>
		<property name="formViewRedirect" value="tag_overview_view.form" />
		<property name="validator" ref="tagOverviewEditValidator" />
	</bean>
	<bean id="tagAssociationsEditController" class="com.prinova.messagepoint.controller.tag.TagAssociationsEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.tag.Tag" />
		<property name="parameter" value="tagId" />
		<property name="formView" value="tpadmin/tag_associations_edit"/>
		<property name="formViewRedirect" value="tag_associations_view.form" />
	</bean>
	<bean id="tagTargetingEditController" class="com.prinova.messagepoint.controller.tag.TagTargetingEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.tag.Tag" />
		<property name="parameter" value="tagId" />
		<property name="formView" value="tpadmin/tag_targeting_edit"/>
		<property name="formViewRedirect" value="tag_targeting_view.form" />
		<property name="validator" ref="targetingValidator"/>
	</bean>

	<!-- Touchpoints: Tags Validation -->
	<bean id="tagListValidator" class="com.prinova.messagepoint.controller.tag.TagListValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tag.TagListWrapper" />
	</bean>
	<bean id="tagOverviewViewValidator" class="com.prinova.messagepoint.controller.tag.TagOverviewViewValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tag.TagOverviewViewWrapper" />
	</bean>
	<bean id="tagOverviewEditValidator" class="com.prinova.messagepoint.controller.tag.TagOverviewEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tag.TagOverviewEditWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />    			
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="content" />
        			<property name="label" value="page.label.content" />
        			<property name="propertyType" ref="Description" />
					<property name="mandatory" value="true" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="priority" />
        			<property name="label" value="page.label.priority" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="4" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="tpContentObjectListController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListController">
		<property name="successView" value="touchpoint_content_object_list.form" />
		<property name="messageEditRedirect" value="content_object_edit.form"/>
		<property name="formView" value="touchpoints/touchpoint_content_object_list" />
		<property name="validator" ref="tpContentObjectListValidator"/>
	</bean>

	<bean id="tpContentListController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListController">
		<property name="successView" value="local_content_list.form" />
		<property name="messageEditRedirect" value="content_object_edit.form"/>
		<property name="formView" value="touchpoints/local_content_list" />
		<property name="validator" ref="tpContentObjectListValidator"/>
	</bean>

	<bean id="tpContentObjectListValidator" class="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListWrapper" />
	</bean>
	<bean id="tpContentObjectListDetailController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListDetailController">
		<property name="successView" value="touchpoint_content_object_list.form" />
		<property name="formView" value="touchpoints/touchpoint_content_object_list_detail" />
	</bean>


	<bean id="tpCollectionListController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointCollectionListController">
		<property name="successView" value="touchpoint_collection_list.form" />
		<property name="collectionEditRedirect" value="touchpoint_collection_edit.form"/>
		<property name="formView" value="touchpoints/touchpoint_collection_list" />
		<property name="validator" ref="tpCollectionListValidator"/>
	</bean>
	<bean id="tpCollectionListValidator" class="com.prinova.messagepoint.controller.touchpoints.TouchpointCollectionListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.touchpoints.TouchpointCollectionListWrapper" />
	</bean>
	
	<bean id="tpCollectionListDetailController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointCollectionListDetailController">
		<property name="successView" value="touchpoint_collection_list_detail.form" />
		<property name="formView" value="touchpoints/touchpoint_collection_list_detail" />
		<property name="validator" ref="tpCollectionListDetailValidator"/>
	</bean>
	<bean id="tpCollectionListDetailValidator" class="com.prinova.messagepoint.controller.touchpoints.TouchpointCollectionListDetailValidator">
		<property name="className" value="com.prinova.messagepoint.controller.touchpoints.TouchpointCollectionListDetailWrapper" />
	</bean>		
	
	<bean id="tpCollectionEditController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointCollectionEditController" >
		<property name="successView" value="touchpoint_collection_edit.form"/>
		<property name="formView" value="touchpoints/touchpoint_collection_edit"/>
		<property name="validator" ref="tpCollectionEditValidator"/>
	</bean>
	<bean id="tpCollectionEditValidator" class="com.prinova.messagepoint.controller.touchpoints.TouchpointCollectionEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.touchpoints.TouchpointCollectionEditWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="touchpointCollection.name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
        </property>		
	</bean>	
		
	<bean id="tpVariantListController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointVariantListController">
		<property name="successView" value="touchpoint_variant_list.form" />
		<property name="formView" value="touchpoints/touchpoint_variant_list" />
		<property name="validator" ref="tpVariantListValidator"/>
	</bean>	
	<bean id="tpVariantListValidator" class="com.prinova.messagepoint.controller.touchpoints.TouchpointVariantListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.touchpoints.TouchpointVariantListWrapper" />
	</bean>
	<bean id="tpVariantListDetailController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointVariantListDetailController">
		<property name="successView" value="touchpoint_variant_list.form" />
		<property name="formView" value="touchpoints/touchpoint_variant_list_detail" />
	</bean>	
	
	<bean id="tpVariantVisibilityController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointVariantVisibilityController" >
		<property name="successView" value="touchpoint_variant_visibility_edit.form"/>
		<property name="formView" value="touchpoints/touchpoint_variant_visibility_edit"/>
		<property name="validator" ref="tpVariantVisibilityValidator"/>
	</bean>	
	<bean id="tpVariantVisibilityValidator" class="com.prinova.messagepoint.controller.touchpoints.TouchpointVariantVisibilityValidator">
		<property name="className" value="com.prinova.messagepoint.controller.touchpoints.TouchpointVariantVisibilityWrapper" />
	</bean>	
	
	<bean id="tpVariantTemplateEditController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointVariantTemplateEditController">
		<property name="formView" value="touchpoints/touchpoint_variant_template_edit"/>
		<property name="successView" value="touchpoint_variant_list.form"/>
	</bean>
	
	<bean id="tpVariantTemplateModifiersEditController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointVariantTemplateModifiersEditController">
		<property name="formView" value="touchpoints/touchpoint_variant_template_modifiers_edit"/>
		<property name="successView" value="touchpoint_variant_list.form"/>
		<property name="validator" ref="tpVariantTemplateModifiersEditValidator" />
	</bean>
	<bean id="tpVariantTemplateModifiersEditValidator" class="com.prinova.messagepoint.controller.touchpoints.TouchpointVariantTemplateModifiersEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.touchpoints.TouchpointVariantTemplateModifiersEditWrapper" />
	</bean>
	
	<bean id="tpVariantContentListDetailController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointSelectionsContentListDetailController">
		<property name="successView" value="touchpoint_selections_content_list_detail.form" />
		<property name="formView" value="touchpoints/touchpoint_selections_content_list_detail" />
	</bean>		
	
	<bean id="touchpointWidgetController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointWidgetController">
		<property name="successView" value="touchpoint_widget.form" />
		<property name="formView" value="touchpoints/touchpoint_widget" />
	</bean>
	<bean id="touchpointWidgetFullController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointWidgetController">
		<property name="successView" value="touchpoint_widget_full.form" />
		<property name="formView" value="touchpoints/touchpoint_widget_full" />
	</bean>	
	<bean id="tpContentObjectPreviewController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectPreviewController" >
		<property name="successView" value="touchpoint_content_object_preview.form"/>
		<property name="formView" value="touchpoints/touchpoint_content_object_preview"/>
		<property name="validator" ref="tpContentObjectPreviewValidator"/>
	</bean>
	<bean id="tpContentObjectMoveToZoneController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectMoveToZoneController" >
		<property name="successView" value="touchpoint_content_object_move_to_zone.form"/>
		<property name="formView" value="touchpoints/touchpoint_content_object_move_to_zone"/>
		<property name="validator" ref="tpContentObjectMoveToZoneValidator"/>
	</bean>
		
	<bean id="contentObjectZonePriorityEditController" class="com.prinova.messagepoint.controller.content.ContentObjectZonePriorityEditController">
		<property name="formView" value="content/content_object_zone_priority_edit"/>
		<property name="validator" ref="zoneContentObjectPriorityEditValidator"/>
	</bean>
	<bean id="zoneContentObjectPriorityEditValidator" class="com.prinova.messagepoint.controller.content.ContentObjectZonePriorityEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.content.ContentObjectZonePriorityEditWrapper" />
	</bean>

	<bean id="selectionDataViewController" class="com.prinova.messagepoint.controller.touchpoints.SelectionDataViewController" >
		<property name="successView" value="selection_data_view.form"/>
		<property name="formView" value="touchpoints/selection_data_view"/>
	</bean>	
	
	<!-- Communications -->
	<bean id="touchpointCommunicationsListController" class="com.prinova.messagepoint.controller.communication.TouchpointCommunicationsListController" >
		<property name="successView" value="touchpoint_communications_list.form"/>
		<property name="formView" value="touchpoints/touchpoint_communications_list"/>
		<property name="validator" ref="touchpointCommunicationsListValidator"/>
		<property name="dataService" ref="cacheDataService"/>
	</bean>
	<bean id="touchpointCommunicationsListValidator" class="com.prinova.messagepoint.controller.communication.TouchpointCommunicationsListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.communication.TouchpointCommunicationsListWrapper" />
	</bean>

	<bean id="touchpointCommunicationsListDetailController" class="com.prinova.messagepoint.controller.communication.TouchpointCommunicationsListDetailController">
		<property name="successView" value="touchpoint_communications_list.form" />
		<property name="formView" value="touchpoints/touchpoint_communications_list_detail" />
		<property name="dataService" ref="cacheDataService"/>
	</bean>
	
	<bean id="communicationContentEditController" class="com.prinova.messagepoint.controller.communication.CommunicationContentEditController" >
		<property name="successView" value="touchpoint_communications_list.form"/>
		<property name="formView" value="communication/communication_content_edit"/>
		<property name="validator" ref="communicationContentEditValidator"/>
	</bean>	
	<bean id="communicationContentEditValidator" class="com.prinova.messagepoint.controller.communication.CommunicationContentEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.communication.CommunicationContentEditWrapper" />
	</bean>
	<bean id="communicationOrderEntryEditController" class="com.prinova.messagepoint.controller.communication.CommunicationOrderEntryEditController" >
		<property name="successView" value="touchpoint_communications_list.form"/>
		<property name="formView" value="communication/communication_order_entry_edit"/>
		<property name="validator" ref="communicationOrderEntryEditValidator"/>
	</bean>
	<bean id="communicationOrderEntryController" class="com.prinova.messagepoint.controller.communication.connected.CommunicationOrderEntryController" />
	<bean id="communicationOrderEntryEditValidator" class="com.prinova.messagepoint.controller.communication.CommunicationOrderEntryEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.communication.CommunicationOrderEntryEditWrapper" />
	</bean>
	
	<bean id="communicationOrderEntrySetupController" class="com.prinova.messagepoint.controller.communication.CommunicationOrderEntrySetupController" >
		<property name="successView" value="communication_order_entry_setup.form"/>
		<property name="formView" value="communication/communication_order_entry_setup"/>
		<property name="validator" ref="communicationOrderEntrySetupValidator"/>
	</bean>	
	<bean id="communicationOrderEntrySetupValidator" class="com.prinova.messagepoint.controller.communication.CommunicationOrderEntrySetupValidator">
		<property name="className" value="com.prinova.messagepoint.controller.metadata.MetadataFormDefinitionWrapper" />
	</bean>
	
	<bean id="communicationExternalValidationController" class="com.prinova.messagepoint.controller.communication.CommunicationExternalValidationController" >
		<property name="successView" value="communication_external_validation.form"/>
		<property name="formView" value="communication_external_validation"/>
	</bean>
	
	<bean id="communicationPortalGatewayController" class="com.prinova.messagepoint.controller.communication.CommunicationPortalGatewayController" >
		<property name="successView" value="connected_portal_gateway.form"/>
		<property name="formView" value="connected_portal_gateway"/>
		<constructor-arg ref="cacheDataService"/>
	</bean>
	
	<bean id="connectedWorkflowAssignmentEditController" class="com.prinova.messagepoint.controller.communication.ConnectedWorkflowAssignmentEditController" >
		<property name="formView" value="communication/connected_workflow_assignment_edit"/>
		<property name="successView" value="connected_workflow_assignment_edit.form"/>
		<property name="validator" ref="connectedWorkflowAssignmentEditValidator" />
	</bean>
	<bean id="connectedWorkflowAssignmentEditValidator" class="com.prinova.messagepoint.controller.communication.ConnectedWorkflowAssignmentEditValidator" >
		<property name="className" value="com.prinova.messagepoint.model.Document" />
	</bean>
	
	<bean id="testCenterCommunicationsListController" class="com.prinova.messagepoint.controller.communication.TouchpointCommunicationsListController" >
		<property name="successView" value="test_center_communications_list.form"/>
		<property name="formView" value="touchpoints/touchpoint_communications_list"/>
		<property name="validator" ref="touchpointCommunicationsListValidator"/>
		<property name="dataService" ref="cacheDataService"/>
	</bean>
	
	<!-- Targeting: Content Targeting -->
	<bean id="contentTargetingEditController" class="com.prinova.messagepoint.controller.content.ContentTargetingEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.ContentTargeting" />
		<property name="parameter" value="contentTargetingId" />
		<property name="formView" value="content/content_targeting_edit"/>
		<property name="validator" ref="targetingValidator"/>
	</bean>
		
	<!-- Touchpoints: Touchpoint Targeting -->
	<bean id="touchpointTargetingEditController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointTargetingEditController">
		<property name="persistedClassName" value="com.prinova.messagepoint.model.TouchpointTargeting" />
		<property name="parameter" value="touchpointTargetingId" />
		<property name="formView" value="tpadmin/touchpoint_targeting_edit"/>
		<property name="formViewRedirect" value="document_view.form" />
		<property name="validator" ref="targetingValidator"/>
	</bean>

	<!-- Touchpoint Selections Controller Beans -->
	<bean id="touchpointSelectionSelectorsEditController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointSelectionSelectorsEditController">
		<property name="formView" value="tpadmin/touchpoint_selection_selectors_edit"/>
		<property name="successView" value="touchpoint_selection_selectors_edit.form" />
		<property name="validator" ref="touchpointSelectionSelectorsEditValidator" />
	</bean>
	<bean id="touchpointContentSelectionViewController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointContentSelectionViewController" >
		<property name="formView" value="tpadmin/touchpoint_content_selection_view"/>
		<property name="successView" value="touchpoint_content_selection_view.form"/>
		<property name="editView" value="touchpoint_content_selection_edit.form"/>
		<property name="validator" ref="touchpointContentSelectionViewValidator" />
	</bean>
	<bean id="touchpointContentSelectionEditController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointContentSelectionEditController" >
		<property name="formView" value="tpadmin/touchpoint_content_selection_edit"/>
		<property name="successView" value="touchpoint_content_selection_view.form"/>
		<property name="validator" ref="touchpointContentSelectionEditValidator" />
	</bean>
	
	<!-- Touchpoint Selections Validator Beans -->
	<bean id="touchpointSelectionSelectorsEditValidator" class="com.prinova.messagepoint.controller.tpadmin.TouchpointSelectionSelectorsEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.TouchpointSelectionSelectorsEditWrapper" />
	</bean>
	<bean id="touchpointContentSelectionViewValidator" class="com.prinova.messagepoint.controller.tpadmin.TouchpointContentSelectionViewValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.TouchpointContentSelectionViewWrapper" />
	</bean>
	<bean id="touchpointContentSelectionEditValidator" class="com.prinova.messagepoint.controller.tpadmin.TouchpointContentSelectionEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.TouchpointContentSelectionEditWrapper" />
	</bean>
	<bean id="tpContentObjectPreviewValidator" class="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectPreviewValidator">
		<property name="className" value="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectPreviewWrapper" />
	</bean>
	<bean id="tpContentObjectMoveToZoneValidator" class="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectMoveToZoneValidator">
		<property name="className" value="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectMoveToZoneWrapper" />
	</bean>	
	<bean id="tpContentObjectDeliveryEditValidator" class="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectDeliveryEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectDeliveryEditWrapper" />
	</bean>	
	
	<!-- Language Selections Controller Beans -->
	<bean id="languageSelectionsListController" class="com.prinova.messagepoint.controller.tpadmin.LanguageSelectionsListController">
		<property name="successView" value="language_selections_list.form"/>
		<property name="formView" value="tpadmin/language_selections_list" />
		<property name="editView" value="language_selection_selectors_edit.form"/>
		<property name="validator" ref="languageSelectionsListValidator" />
	</bean>
	<bean id="languageSelectionSelectorsEditController" class="com.prinova.messagepoint.controller.tpadmin.LanguageSelectionSelectorsEditController">
		<property name="formView" value="tpadmin/language_selection_selectors_edit"/>
		<property name="successView" value="language_selection_selectors_edit.form" />
		<property name="validator" ref="languageSelectionSelectorsEditValidator" />
	</bean>
	<!-- Language Selections Validator Beans -->
	<bean id="languageSelectionsListValidator" class="com.prinova.messagepoint.controller.tpadmin.LanguageSelectionsListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.LanguageSelectionsListWrapper" />
	</bean>
	<bean id="languageSelectionSelectorsEditValidator" class="com.prinova.messagepoint.controller.tpadmin.LanguageSelectionSelectorsEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.LanguageSelectionSelectorsEditWrapper" />
	</bean>

	<!-- Formatting Selections Controller Beans -->
	<bean id="formattingSelectionsListController" class="com.prinova.messagepoint.controller.tpadmin.FormattingSelectionsListController">
		<property name="successView" value="formatting_selections_list.form"/>
		<property name="formView" value="tpadmin/formatting_selections_list" />
		<property name="editView" value="formatting_selection_selectors_edit.form"/>
		<property name="validator" ref="formattingSelectionsListValidator" />
	</bean>
	<!-- Formatting Selections Validator Beans -->
	<bean id="formattingSelectionsListValidator" class="com.prinova.messagepoint.controller.tpadmin.FormattingSelectionsListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.FormattingSelectionsListWrapper" />
	</bean>

	<!-- Connected -->
	<bean id="touchpointCommunicationsProtoController" class="com.prinova.messagepoint.connected.TouchpointCommunicationsProtoController" >
		<property name="successView" value="touchpoint_communications_proto.form"/>
		<property name="formView" value="connected/touchpoint_communications_proto"/>
		<property name="validator" ref="touchpointCommunicationsProtoValidator"/>
	</bean>

	<bean id="touchpointCommunicationsProtoValidator" class="com.prinova.messagepoint.connected.TouchpointCommunicationsProtoValidator">
		<property name="className" value="com.prinova.messagepoint.connected.TouchpointCommunicationsProtoWrapper" />
	</bean>

	<bean id="connectedTouchpointRenderingValidator" class="com.prinova.messagepoint.connected.touchpointrendering.ConnectedTouchpointRenderingManagerValidator">
		<property name="className" value="com.prinova.messagepoint.connected.touchpointrendering.ConnectedTouchpointRenderingManagerWrapper" />
	</bean>

	<bean id="connectedTouchpointRenderingManagerController" class="com.prinova.messagepoint.connected.touchpointrendering.ConnectedTouchpointRenderingManagerController" >
		<property name="successView" value="connected_touchpoint_rendering_manager.form"/>
		<property name="formView" value="connected/connected_touchpoint_rendering_manager"/>
		<property name="validator" ref="connectedTouchpointRenderingValidator"/>
	</bean>

	<bean id="connectedTouchpointRenderingManagerIframeController" class="com.prinova.messagepoint.connected.touchpointrendering.ConnectedTouchpointRenderingManagerIframeController" >
		<property name="successView" value="connected/connected_touchpoint_rendering_manager_iframe"/>
	</bean>

	<bean id="cacheDataRepository" class="com.prinova.messagepoint.connected.data.oracle.OracleCacheDataRepository"/>

	<bean id="cacheDataService" class="com.prinova.messagepoint.connected.data.CacheDataService">
		<constructor-arg ref="cacheDataRepository"/>
	</bean>

	<bean id="asyncConnectedTouchpointRenderingManagerController" class="com.prinova.messagepoint.connected.touchpointrendering.AsyncConnectedTouchpointRenderingManagerController">
		<constructor-arg ref="cacheDataService"/>
	</bean>

	<bean id="asyncStyleManagerController" class="com.prinova.messagepoint.connected.stylemanager.AsyncStyleManagerController">
		<constructor-arg ref="cacheDataRepository"/>
	</bean>

	<bean id="connectedTouchpointInterviewSetupController" class="com.prinova.messagepoint.controller.communication.connected.ConnectedTouchpointInterviewSetupController" >
		<property name="successView" value="connected_touchpoint_interview_setup.form"/>
		<property name="formView" value="communication/connected_touchpoint_interview_setup"/>
	</bean>

	<!-- Touchpoint Admin -->

	<bean id="touchpointImportController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointImportController" >
		<property name="formView" value="tpadmin/touchpoint_import"/>
		<property name="successView" value="touchpoint_import_confirm.form"/>
		<property name="validator" ref="touchpointImportValidator" />
	</bean>

	<bean id="touchpointImportConfirmationController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointImportConfirmationController" >
		<property name="formView" value="tpadmin/touchpoint_import_confirm" />
		<property name="successView" value="document_view.form" />
		<property name="validator" ref="touchpointImportConfirmationValidator" />
	</bean>

	<bean id="contentJsonImportConfirmationController" class="com.prinova.messagepoint.controller.tpadmin.ContentJsonImportConfirmationController" >
		<property name="formView" value="tpadmin/content_json_import_confirm" />
		<property name="successView" value="document_view.form" />
		<property name="validator" ref="contentJsonImportConfirmationValidator" />
	</bean>
	
	<bean id="bulkUploadConfirmationController" class="com.prinova.messagepoint.controller.content.BulkUploadConfirmationController" >
		<property name="formView" value="content/bulk_upload_confirm" />
		<property name="successView" value="global_content_list.form" />
		<property name="validator" ref="bulkUploadConfirmationValidator" />
	</bean>
	<bean id="bulkUploadConfirmationValidator" class="com.prinova.messagepoint.controller.content.BulkUploadConfirmationValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.content.BulkUploadConfirmationController$Command" />
	</bean>

	<bean id="appImportConfirmationController" class="com.prinova.messagepoint.controller.admin.AppImportConfirmationController" >
		<property name="formView" value="tpadmin/tpcontainer_import_confirmation" />
		<property name="successView" value="document_view.form" />
	</bean>

	<bean id="documentEditController" class="com.prinova.messagepoint.controller.tpadmin.DocumentEditController">
		<property name="formView" value="tpadmin/document_edit"/>
		<property name="successView" value="document_view.form"/>
		<property name="validator" ref="documentEditValidator" />
	</bean>
	<bean id="documentEditValidator" class="com.prinova.messagepoint.controller.tpadmin.DocumentEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.DocumentEditWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="document.name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
	       	</list>
		</property>
	</bean>
	
	<bean id="compositionFilesUploadController" class="com.prinova.messagepoint.controller.file.CompositionFilesUploadController">
		<property name="formView" value="tpadmin/composition_files_upload"/>
	</bean>

	<bean id="documentProofingDataEditController" class="com.prinova.messagepoint.controller.tpadmin.DocumentProofingDataEditController" >
		<property name="formView" value="tpadmin/touchpoint_selections_proofing_data_edit"/>
		<property name="successView" value="document_view.form"/>
		<property name="validator" ref="documentProofingDataEditValidator" />
	</bean>
	
	<bean id="selectionProofingDataEditController" class="com.prinova.messagepoint.controller.tpadmin.DocumentProofingDataEditController" >
		<property name="formView" value="tpadmin/selection_proofing_data_edit"/>
		<property name="successView" value="selection_proofing_data_edit.form"/>
		<property name="validator" ref="documentProofingDataEditValidator" />
	</bean>	
	
	<bean id="alternateLayoutAssignmentEditController" class="com.prinova.messagepoint.controller.tpadmin.AlternateLayoutAssignmentEditController" >
		<property name="formView" value="tpadmin/alternate_layout_assignment_edit"/>
		<property name="successView" value="document_view.form"/>
	</bean>

	<bean id="touchpointEmailProofViewController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointEmailProofViewController">
		<property name="formView" value="tpadmin/touchpoint_email_proof_view" />
	</bean>
	
	<bean id="variantWorkflowAssignmentEditController" class="com.prinova.messagepoint.controller.tpadmin.VariantWorkflowAssignmentEditController" >
		<property name="formView" value="tpadmin/variant_workflow_assignment_edit"/>
		<property name="successView" value="variant_workflow_assignment_edit.form"/>
		<property name="validator" ref="variantWorkflowAssignmentEditValidator" />
	</bean>
	<bean id="variantWorkflowAssignmentEditValidator" class="com.prinova.messagepoint.controller.tpadmin.VariantWorkflowAssignmentEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.VariantWorkflowAssignmentEditWrapper" />
	</bean>
	
	<bean id="messageWorkflowAssignmentEditController" class="com.prinova.messagepoint.controller.tpadmin.MessageWorkflowAssignmentEditController" >
		<property name="formView" value="tpadmin/message_workflow_assignment_edit"/>
		<property name="successView" value="message_workflow_assignment_edit.form"/>
		<property name="validator" ref="messageWorkflowAssignmentEditValidator" />
	</bean>
	<bean id="messageWorkflowAssignmentEditValidator" class="com.prinova.messagepoint.controller.tpadmin.MessageWorkflowAssignmentEditValidator" >
		<property name="className" value="com.prinova.messagepoint.model.Document" />
	</bean>
	
	<bean id="documentChannelEditController" class="com.prinova.messagepoint.controller.tpadmin.DocumentChannelEditController" >
		<property name="formView" value="tpadmin/touchpoint_channel_configuration_edit"/>
		<property name="successView" value="document_view.form"/>
		<property name="validator" ref="documentChannelEditValidator" />
	</bean>
	<bean id="documentChannelEditValidator" class="com.prinova.messagepoint.controller.tpadmin.DocumentChannelEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.DocumentChannelEditWrapper" />
		<property name="validationEntries">
	       	<list>
		       	<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
	        		<property name="propertyName" value="customerDriverInputFilename" />
	        		<property name="label" value="page.label.customer.production.files.folder" />
	        		<property name="propertyType" ref="FileName" />
	        	</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="document.connectorConfigWrapper.preXSLTFile.originalFilename" />
        			<property name="label" value="page.label.PreProcess.XSLT.File" />
        			<property name="propertyType" ref="FileName" />
        			<property name="maxLength" value="75" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="document.connectorConfigWrapper.postXSLTFile.originalFilename" />
        			<property name="label" value="page.label.PostProcess.XSLT.File" />
        			<property name="propertyType" ref="FileName" />
        			<property name="maxLength" value="75" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="xmlFileName" />
        			<property name="label" value="page.label.xml.file.name" />
        			<property name="propertyType" ref="FileName" />
        			<property name="maxLength" value="75" />
        		</bean>
	    	</list>
		</property>
	</bean>
	
	<bean id="documentViewExtendedReportingVariablesIFrame" class="com.prinova.messagepoint.controller.tpadmin.DocumentViewController">
		<property name="formView" value="tpadmin/document_view_extended_reporting_variables"/>
	</bean>
	
	<bean id="documentExtendedReportingVariablesController" class="com.prinova.messagepoint.controller.tpadmin.DocumentExtendedReportingVariablesController" >
		<property name="formView" value="tpadmin/document_extended_reporting_variables_edit"/>
		<property name="successView" value="document_extended_reporting_variables_edit.form"/>
	</bean>
	
	<bean id="documentProofingDataEditValidator" class="com.prinova.messagepoint.controller.tpadmin.DocumentProofingDataEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.DocumentProofingDataEditWrapper" />
	</bean>
	
	<bean id="createTouchpointController" class="com.prinova.messagepoint.controller.tpadmin.CreateTouchpointController">
		<property name="formView" value="tpadmin/create_touchpoint"/>
		<property name="successView" value="create_touchpoint.form" />
		<property name="validator" ref="createTouchpointValidator" />
	</bean>
	<bean id="createTouchpointValidator" class="com.prinova.messagepoint.controller.tpadmin.CreateTouchpointValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.CreateTouchpointWrapper" />
	</bean>

	<bean id="documentViewController" class="com.prinova.messagepoint.controller.tpadmin.DocumentViewController">
		<property name="formView" value="tpadmin/document_view"/>
		<property name="successView" value="document_view.form" />
		<property name="formViewParentRedirect" value="document_edit.form" />
		<property name="validator" ref="documentViewValidator" />
		<property name="editLanguageView" value="touchpoint_language_edit.form" />
	</bean>
	<bean id="documentViewValidator" class="com.prinova.messagepoint.controller.tpadmin.DocumentViewValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.DocumentViewWrapper" />
	</bean>
	
	<bean id="tpAccessControlController" class="com.prinova.messagepoint.controller.tpadmin.TPAccessControlController" >
		<property name="successView" value="document_view.form"/>
		<property name="formView" value="tpadmin/tp_access_control"/>
		<property name="validator" ref="tpAccessControlValidator"/>
	</bean>
	<bean id="tpAccessControlValidator" class="com.prinova.messagepoint.controller.tpadmin.TPAccessControlValidator"/>
	
	<bean id="zoneListController" class="com.prinova.messagepoint.controller.tpadmin.ZoneListController">
		<property name="formView" value="tpadmin/zone_list"/>
		<property name="successView" value="zone_list.form"/>
		<property name="zoneEditRedirect" value="touchpoint_zone_edit.form" />
		<property name="validator" ref="zoneListValidator" />
	</bean>	
	<bean id="zoneListValidator" class="com.prinova.messagepoint.controller.tpadmin.ZoneListValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.ZoneListWrapper" />
	</bean>
	<bean id="zoneListDetailController" class="com.prinova.messagepoint.controller.tpadmin.ZoneListDetailController">
		<property name="successView" value="zone_list.form" />
		<property name="formView" value="tpadmin/zone_list_detail" />
	</bean>
	<bean id="touchpointZoneEditController" class="com.prinova.messagepoint.controller.tpadmin.ZoneEditController">
		<property name="formView" value="tpadmin/touchpoint_zone_edit"/>
		<property name="successView" value="zone_list.form"/>
		<property name="validator" ref="zoneEditValidator" />
	</bean>	
	<bean id="zoneEditController" class="com.prinova.messagepoint.controller.tpadmin.ZoneEditController">
		<property name="formView" value="tpadmin/zone_edit"/>
		<property name="successView" value="document_view.form"/>
		<property name="validator" ref="zoneEditValidator" />
	</bean>
	<bean id="zoneEditValidator" class="com.prinova.messagepoint.controller.tpadmin.ZoneEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.ZoneEditWrapper" />
	</bean>
	
	<bean id="touchpointLayoutManagerController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointLayoutManagerController">
		<property name="formView" value="tpadmin/touchpoint_layout_manager"/>
		<property name="successView" value="document_view.form"/>
		<property name="validator" ref="touchpointLayoutManagerEditValidator" />
	</bean>
	<bean id="touchpointLayoutManagerEditValidator" class="com.prinova.messagepoint.controller.tpadmin.TouchpointLayoutManagerValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.TouchpointLayoutManagerWrapper" />
	</bean>
	
	<bean id="zoneVisibilityEditController" class="com.prinova.messagepoint.controller.tpadmin.ZoneVisibilityEditController">
		<property name="formView" value="tpadmin/zone_visibility_edit"/>
		<property name="successView" value="zone_visibility_edit.form"/>
	</bean>
	<bean id="zoneTextStylesEditController" class="com.prinova.messagepoint.controller.tpadmin.ZoneTextStylesEditController">
		<property name="formView" value="tpadmin/zone_text_styles_edit"/>
		<property name="successView" value="zone_text_styles_edit.form"/>
	</bean>
	<bean id="zoneParaStylesEditController" class="com.prinova.messagepoint.controller.tpadmin.ZoneParaStylesEditController">
		<property name="formView" value="tpadmin/zone_para_styles_edit"/>
		<property name="successView" value="zone_para_styles_edit.form"/>
	</bean>
	<bean id="zoneListStylesEditController" class="com.prinova.messagepoint.controller.tpadmin.ZoneListStylesEditController">
		<property name="formView" value="tpadmin/zone_list_styles_edit"/>
		<property name="successView" value="zone_list_styles_edit.form"/>
	</bean>
	<bean id="zoneDataGroupBulkEditController" class="com.prinova.messagepoint.controller.tpadmin.ZoneDataGroupBulkEditController">
		<property name="formView" value="tpadmin/touchpoint_zone_data_group_bulk_edit"/>
		<property name="successView" value="document_view.form"/>
		<property name="validator" ref="zoneDataGroupBulkEditValidator" />
	</bean>
	<bean id="zoneDataGroupBulkEditValidator" class="com.prinova.messagepoint.controller.tpadmin.ZoneDataGroupBulkEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.ZoneDataGroupBulkEditWrapper" />
	</bean>
	
	<bean id="touchpointStyleCustomizationsController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointStyleCustomizationsController">
		<property name="formView" value="tpadmin/touchpoint_style_customizations"/>
		<property name="successView" value="touchpoint_style_customizations.form"/>
		<property name="validator" ref="touchpointStyleCustomizationsValidator" />
	</bean>
	<bean id="touchpointStyleCustomizationsValidator" class="com.prinova.messagepoint.controller.tpadmin.TouchpointStyleCustomizationsValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.TouchpointStyleCustomizationsWrapper" />
	</bean>
	
	<bean id="textStyleCustomizationController" class="com.prinova.messagepoint.controller.tpadmin.TextStyleCustomizationController">
		<property name="formView" value="tpadmin/text_style_customization"/>
		<property name="successView" value="style_customizations.form"/>
		<property name="validator" ref="textStyleCustomizationValidator" />
	</bean>
	<bean id="textStyleCustomizationValidator" class="com.prinova.messagepoint.controller.tpadmin.TextStyleCustomizationValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.TextStyleCustomizationController$Command" />
	</bean>	
	
	<bean id="workflowEditController" class="com.prinova.messagepoint.controller.workflow.WorkflowEditController">
		<property name="formView" value="workflow/workflow_edit"/>
		<property name="validator" ref="workflowEditValidator" />
	</bean>	
	<bean id="workflowEditValidator" class="com.prinova.messagepoint.controller.workflow.WorkflowEditValidator" />
	
	<bean id="workflowHistoryController" class="com.prinova.messagepoint.controller.workflow.WorkflowHistoryController">
		<property name="formView" value="workflow/workflow_history"/>
	</bean>
	
	<!-- Workflow Library -->
	<bean id="workflowLibraryController" class="com.prinova.messagepoint.controller.workflow.WorkflowLibraryController">
		<property name="successView" value="workflow_library.form" />
		<property name="formView" value="workflow/workflow_library" />
		<property name="validator" ref="workflowLibraryValidator"/>
	</bean>	
	<bean id="workflowLibraryValidator" class="com.prinova.messagepoint.controller.workflow.WorkflowLibraryValidator">
		<property name="className" value="com.prinova.messagepoint.controller.workflow.WorkflowLibraryWrapper" />
	</bean>
	<bean id="workflowLibraryDetailController" class="com.prinova.messagepoint.controller.workflow.WorkflowLibraryDetailController">
		<property name="formView" value="workflow/workflow_library_detail" />
	</bean>
	
	<bean id="deleteUnreferencedAssetsController" class="com.prinova.messagepoint.controller.wtu.DeleteUnreferencedAssetsController">
		<property name="successView" value="delete_unref_assets.form" />
		<property name="formView" value="wtu/delete_unref_assets" />
		<property name="validator" ref="deleteUnreferencedAssetsValidator"/>
	</bean>	
	<bean id="deleteUnreferencedAssetsValidator" class="com.prinova.messagepoint.controller.wtu.DeleteUnreferencedAssetsValidator">
		<property name="className" value="com.prinova.messagepoint.controller.wtu.DeleteUnreferencedAssetsWrapper" />
	</bean>
	
	<bean id="sourceEditorController" class="com.prinova.messagepoint.controller.file.SourceEditorController">
		<property name="formView" value="file/source_editor"/>
	</bean>			
		
	<!-- Touchpoint Admin: email templates -->
	<bean id="emailTemplatePreviewController" class="com.prinova.messagepoint.controller.tpadmin.EmailTemplatePreviewController">
		<property name="formView" value="tpadmin/email_template_preview"/>
		<property name="successView" value="document_view.form"/>
	</bean>

    <!-- Job Center -->
    <bean id="jobCenterController" class="com.prinova.messagepoint.controller.JobCenterController">
        <property name="successView" value="job_center.form" />
    </bean>

    <!-- Backend Component Downloads -->
    <bean id="backendComponentDownloads" class="com.prinova.messagepoint.controller.BackendComponentDownloadsController">
        <property name="successView" value="backend_component_downloads.form" />
    </bean>
	
	<!-- ADMIN Beans -->
	
	<bean id="touchpointImportValidator" class="com.prinova.messagepoint.controller.tpadmin.TouchpointImportValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.TouchpointImportController$ImportFilename" />
		<property name="validationEntries">
	       	<list>
	 			<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="importFilename.fileItem.name" />
        			<property name="label" value="page.label.touchpoint.import.filename" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
        		
	       	</list>
		</property>
	</bean>
	
	<bean id="touchpointImportConfirmationValidator" class="com.prinova.messagepoint.controller.tpadmin.TouchpointImportConfirmationValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.TouchpointImportConfirmationController$Command" />
	</bean>

	<bean id="contentJsonImportConfirmationValidator" class="com.prinova.messagepoint.controller.tpadmin.ContentJsonImportConfirmationValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.ContentJsonImportConfirmationController$Command" />
	</bean>
	
	<bean id="touchpointDataSourceAssociationEditController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointDataSourceAssociationEditController">
		<property name="formView" value="tpadmin/touchpoint_data_source_configuration_edit"/>
		<property name="successView" value="document_view.form"/>
		<property name="validator" ref="touchpointDataSourceAssociationEditValidator" /> 
	</bean>
	
	<bean id="touchpointDataSourceAssociationEditValidator" class="com.prinova.messagepoint.controller.tpadmin.TouchpointDataSourceAssociationEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.TouchpointDataSourceAssociationEditController$Command" />
	</bean>
	
	<bean id="touchpointSyncProjectController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointSyncProjectController">
		<property name="formView" value="tpadmin/touchpoint_sync_project"/>
		<property name="successView" value="document_view.form"/>
	</bean>

	<bean id="touchpointSyncListController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointSyncListController">
		<property name="formView" value="tpadmin/touchpoint_sync_list"/>
		<property name="successView" value="touchpoint_sync_list.form"/>
	</bean>

	<bean id="CompareContentSectionController" class="com.prinova.messagepoint.controller.tpadmin.CompareContentSectionController">
		<property name="formView" value="tpadmin/compare_content_section"/>
		<property name="successView" value="compare_content_section.form"/>
	</bean>

	<bean id="touchpointSyncMessagePriorityController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointSyncMessagePriorityController">
		<property name="formView" value="tpadmin/touchpoint_sync_message_priority"/>
		<property name="successView" value="document_view.form"/>
	</bean>
	
	<bean id="touchpointSyncContentCompareController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointSyncContentCompareController" >
		<property name="formView" value="tpadmin/touchpoint_sync_content_compare"/>
	</bean>
	
    <bean id="touchpointSyncDifferencesListController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointSyncDifferencesListController" >
        <property name="formView" value="tpadmin/touchpoint_sync_differences_list"/>
    </bean>
    
	<bean id="variantContentCompareController" class="com.prinova.messagepoint.controller.content.VariantContentCompareController" >
		<property name="formView" value="content/variant_content_compare"/>
	</bean>	
	
	<!-- Touchpoint Admin: Template Modifier Setup -->
	<bean id="touchpointTemplateModifierController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointTemplateModifierController">
		<property name="formView" value="tpadmin/touchpoint_template_modifier_list"/>
		<property name="successView" value="document_view.form"/>
		<!-- property name="templateModifierEditRedirect" value="touchpoint_template_modifier_edit.form"/-->
	</bean>
	
	<!-- Touchpoint Admin: Template Variant Setup -->
	<bean id="templateVariantsEditController" class="com.prinova.messagepoint.controller.tpadmin.TemplateVariantsEditController">
		<property name="formView" value="tpadmin/template_variants_edit"/>
		<property name="successView" value="document_view.form"/>
		<property name="validator" ref="templateVariantsEditValidator" />
	</bean>
	<bean id="templateVariantsEditValidator" class="com.prinova.messagepoint.controller.tpadmin.TemplateVariantsEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.TemplateVariantsEditController$Command" />
	</bean>
	
	<!-- DATA ADMIN - Data Sources -->
	<bean id="dataSourcesController" class="com.prinova.messagepoint.controller.admin.DataSourcesController">
		<property name="formView" value="dataadmin/datasources"/>
		<property name="successView" value="datasources.form"/>
	</bean>
		
	<!-- DATA ADMIN - Data Resources -->
	<bean id="dataResourceEditController" class="com.prinova.messagepoint.controller.admin.DataResourceEditController">
		<property name="formView" value="dataadmin/data_resource_edit"/>
		<property name="successView" value="data_resources.form"/>
		<property name="validator" ref="dataResourceEditValidator" /> 
	</bean>
	<bean id="dataResourceEditValidator" class="com.prinova.messagepoint.controller.admin.DataResourceEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.DataResourceEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>
	<bean id="dataResourceDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.admin.DataResource" />
		<property name="successView" value="data_resources.form" />
		<property name="formView" value="delete_confirmation"/>
	</bean>
	
	<!-- DATA ADMIN -->
	<bean id="dataFileDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.testing.DataFile" />
		<property name="successView" value="data_files.form" />
		<property name="formView" value="delete_confirmation"/>
	</bean>	

	<bean id="testScenarioDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.testing.TestScenario" />
		<property name="successView" value="testing_list.form" />
		<property name="formView" value="delete_confirmation"/>
	</bean>	
	
	<bean id="targetGroupDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.targeting.TargetGroup" />
		<property name="successView" value="targetgroups.form" />
		<property name="formView" value="delete_confirmation"/>
	</bean>
	
	<bean id="variableEditValidator" class="com.prinova.messagepoint.controller.admin.VariableEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.VariableEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="variable.name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="variable.friendlyName" />
        			<property name="label" value="page.label.friendlyname" />
        			<property name="propertyType" ref="ObjectName" />
					<property name="mandatory" value="false" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="variable.defaultValue" />
        			<property name="label" value="page.label.defaultvalue" />
        			<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="64" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.space.dash.underscore" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s\p{L}\p{Sc}\./]*+" />
        		</bean>
	       	</list>
		</property>
	</bean>
	
	<bean id="variableListController" class="com.prinova.messagepoint.controller.admin.DataElementVariablesController">
		<property name="formView" value="dataadmin/variable_list"/>
		<property name="successView" value="variable_list.form"/>
		<property name="variableEditRedirect" value="variable_edit.form"/>
		<property name="validator" ref="dataElementVariablesListValidator" /> 
	</bean>
	<bean id="dataElementVariablesListValidator" class="com.prinova.messagepoint.controller.admin.DataElementVariablesListValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.DataElementVariablesWrapper" />
	</bean>
	
	<bean id="bridgeDataElementVariablesController" class="com.prinova.messagepoint.controller.admin.BridgeDataElementVariablesController" >
		<property name="formView" value="dataadmin/bridge_variables"/>
		<property name="successView" value="variable_list.form"/>
		<property name="validator" ref="bridgeDataElementVariablesValidator" />
	</bean>
	<bean id="bridgeDataElementVariablesValidator" class="com.prinova.messagepoint.controller.admin.BridgeDataElementVariablesValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.BridgeDataElementVariablesWrapper" />
	</bean>
	
	<bean id="variableListDetailController" class="com.prinova.messagepoint.controller.admin.VariableListDetailController">
		<property name="successView" value="variable_list.form" />
		<property name="formView" value="dataadmin/variable_list_detail" />
	</bean>	
	
	<bean id="variableEditController" class="com.prinova.messagepoint.controller.admin.VariableEditController">
		<property name="bindOnNewForm" value="true" />
		<property name="formView" value="dataadmin/variable_edit"/>
		<property name="successView" value="variable_view.form"/>
		<property name="validator" ref="variableEditValidator" />
	</bean>

	<bean id="variableViewController" class="com.prinova.messagepoint.controller.admin.VariableViewController">
		<property name="formView" value="dataadmin/variable_view"/>
		<property name="editView" value="variable_edit.form"/>		
		<property name="successView" value="variable_view.form"/>
	</bean>

	<bean id="variableDeleteValidator" class="com.prinova.messagepoint.controller.admin.VariableDeleteValidator" />
	<bean id="variableDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.admin.DataElementVariable" />
		<property name="successView" value="dataelementvariables.form" />
		<property name="formView" value="delete_confirmation"/>
		<property name="validator" ref="variableDeleteValidator" />
	</bean>
	
	<bean id="dataFileListController" class="com.prinova.messagepoint.controller.admin.DataFileListController">
		<property name="formView" value="dataadmin/data_files" />
		<property name="successView" value="data_files.form"/>
		<property name="validator" ref="dataFileListValidator"/>		
	</bean>
	
	<bean id="dataFileListValidator" class="com.prinova.messagepoint.controller.admin.DataFileListValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.DataFileListWrapper" />
	</bean>
	
	<bean id="dataFileListDetailController" class="com.prinova.messagepoint.controller.admin.DataFileListDetailController">
		<property name="successView" value="data_files.form" />
		<property name="formView" value="dataadmin/data_file_list_detail" />
	</bean>
	
	<bean id="dataResourceListController" class="com.prinova.messagepoint.controller.admin.DataResourceListController">
		<property name="formView" value="dataadmin/data_resources" />
		<property name="successView" value="data_resources.form"/>
		<property name="validator" ref="dataResourceListValidator"/>		
	</bean>
	
	<bean id="dataResourceListValidator" class="com.prinova.messagepoint.controller.admin.DataResourceListValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.DataResourceListWrapper" />
	</bean>
	
	<bean id="dataResourceListDetailController" class="com.prinova.messagepoint.controller.admin.DataResourceListDetailController">
		<property name="successView" value="data_resources.form" />
		<property name="formView" value="dataadmin/data_resource_list_detail" />
	</bean>
	
	<bean id="lookupTableListController" class="com.prinova.messagepoint.controller.dataadmin.LookupTableListController">
		<property name="formView" value="dataadmin/lookup_table_list" />
		<property name="successView" value="lookup_table_list.form"/>
		<property name="validator" ref="lookupTableListValidator"/>		
	</bean>
	<bean id="lookupTableListValidator" class="com.prinova.messagepoint.controller.dataadmin.LookupTableListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.dataadmin.LookupTableListWrapper" />
	</bean>
	
	<bean id="lookupTableListDetailController" class="com.prinova.messagepoint.controller.dataadmin.LookupTableListDetailController">
		<property name="successView" value="lookup_table_list.form" />
		<property name="formView" value="dataadmin/lookup_table_list_detail" />
	</bean>

	<bean id="lookupTableEditValidator" class="com.prinova.messagepoint.controller.dataadmin.LookupTableEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.dataadmin.LookupTableEditWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="lookupTableInstance.name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="lookupTableEditController" class="com.prinova.messagepoint.controller.dataadmin.LookupTableEditController" >
		<property name="formView" value="dataadmin/lookup_table_edit"/>
		<property name="successView" value="lookup_table_edit.form"/>
		<property name="validator" ref="lookupTableEditValidator" />
	</bean>
	
	<bean id="lookupTableWorkflowEditController" class="com.prinova.messagepoint.controller.workflow.WorkflowEditController">
		<property name="formView" value="workflow/workflow_edit"/>
		<property name="successView" value="lookup_table_list.form"/>
		<property name="validator" ref="lookupTableWorkflowEditValidator" />
	</bean>
	<bean id="lookupTableWorkflowEditValidator" class="com.prinova.messagepoint.controller.workflow.WorkflowEditValidator" />
	
	<bean id="lookupTableSourceEditorController" class="com.prinova.messagepoint.controller.dataadmin.LookupTableSourceEditorController">
		<property name="formView" value="file/source_editor"/>
		<property name="validator" ref="lookupTableSourceEditorValidator" />
	</bean>
	<bean id="lookupTableSourceEditorValidator" class="com.prinova.messagepoint.controller.dataadmin.LookupTableSourceEditorValidator">
		<property name="className" value="com.prinova.messagepoint.controller.file.SourceEditorController$SourceCommand" />
	</bean>
	
	<bean id="metadataFormDefinitionListController" class="com.prinova.messagepoint.controller.metadata.MetadataFormDefinitionListController">
		<property name="formView" value="metadata/metadata_form_definition_list"/>
		<property name="successView" value="metadata_form_definition_list.form"/>
		<property name="validator" ref="metadataFormDefinitionListValidator" />
	</bean>
	<bean id="metadataFormDefinitionListValidator" class="com.prinova.messagepoint.controller.metadata.MetadataFormDefinitionListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.metadata.MetadataFormDefinitionListWrapper" />
	</bean>
	<bean id="metadataFormDefinitionListDetailController" class="com.prinova.messagepoint.controller.metadata.MetadataFormDefinitionListDetailController">
		<property name="formView" value="metadata/metadata_form_definition_list_detail"/>
	</bean>
	
	<bean id="metadataFormDefinitionEditController" class="com.prinova.messagepoint.controller.metadata.MetadataFormDefinitionEditController">
		<property name="formView" value="metadata/metadata_form_definition_edit"/>
		<property name="validator" ref="metadataFormDefinitionEditValidator" />
	</bean>
	<bean id="metadataFormDefinitionEditValidator" class="com.prinova.messagepoint.controller.metadata.MetadataFormDefinitionEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.metadata.MetadataFormDefinitionWrapper" />
	</bean>
	
	<bean id="touchpointMetadataFormEditController" class="com.prinova.messagepoint.controller.metadata.TouchpointMetadataFormEditController">
		<property name="formView" value="metadata/touchpoint_metadata_form_edit"/>
		<property name="validator" ref="touchpointMetadataFormEditValidator" />
	</bean>
	<bean id="touchpointMetadataFormEditValidator" class="com.prinova.messagepoint.controller.metadata.TouchpointMetadataFormEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.metadata.TouchpointMetadataFormEditWrapper" />
	</bean>
	
	<bean id="touchpointSelectionMetadataFormEditController" class="com.prinova.messagepoint.controller.metadata.TouchpointSelectionMetadataFormEditController">
		<property name="formView" value="metadata/touchpoint_selection_metadata_form_edit"/>
		<property name="validator" ref="touchpointSelectionMetadataFormEditValidator" />
	</bean>
	<bean id="touchpointSelectionMetadataFormEditValidator" class="com.prinova.messagepoint.controller.metadata.TouchpointSelectionMetadataFormEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.metadata.TouchpointSelectionMetadataFormEditWrapper" />
	</bean>
	<bean id="metadataPointsOfInterestController" class="com.prinova.messagepoint.controller.metadata.MetadataPointsOfInterestController">
		<property name="formView" value="metadata/metadata_points_of_interest_setup"/>
		<property name="validator" ref="metadataPointsOfInterestValidator" />
	</bean>
	<bean id="metadataPointsOfInterestValidator" class="com.prinova.messagepoint.controller.metadata.MetadataPointsOfInterestValidator">
		<property name="className" value="com.prinova.messagepoint.controller.metadata.MetadataPointsOfInterestWrapper"/>
	</bean>

	<!-- Target Group List -->
	<bean id="targetGroupListController" class="com.prinova.messagepoint.controller.targeting.TargetGroupListController">
		<property name="formView" value="dataadmin/targetgroups"/>
		<property name="successView" value="targetgroups.form"/>
		<property name="validator" ref="targetGroupListValidator" /> 
	</bean>
	<bean id="targetGroupListValidator" class="com.prinova.messagepoint.controller.targeting.TargetGroupListValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.targeting.TargetGroupListWrapper" />
	</bean>

	<bean id="targetGroupListDetailController" class="com.prinova.messagepoint.controller.targeting.TargetGroupListDetailController">
		<property name="successView" value="targetgroups.form" />
		<property name="formView" value="dataadmin/targetgroups_detail" />
	</bean>
		
	<bean id="targetGroupTouchpointAssignmentEditController" class="com.prinova.messagepoint.controller.targeting.TargetGroupTouchpointAssignmentEditController">
		<property name="formView" value="dataadmin/target_group_touchpoint_assignment"/>
		<property name="successView" value="target_group_touchpoint_assignment.form"/>
		<property name="validator" ref="targetGroupTouchpointAssignmentEditValidator" />
	</bean>
	<bean id="targetGroupTouchpointAssignmentEditValidator" class="com.prinova.messagepoint.controller.targeting.TargetGroupTouchpointAssignmentEditValidator" />

	<!-- Targeting Rule List -->
	<bean id="targetingRuleListController" class="com.prinova.messagepoint.controller.targeting.TargetingRuleListController">
		<property name="formView" value="dataadmin/conditionelements"/>
		<property name="successView" value="conditionelements.form"/>
		<property name="validator" ref="targetingRuleListValidator" /> 
	</bean>
	<bean id="targetingRuleListValidator" class="com.prinova.messagepoint.controller.targeting.TargetingRuleListValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.targeting.TargetingRuleListWrapper" />
	</bean>

	<bean id="targetingRuleListDetailController" class="com.prinova.messagepoint.controller.targeting.TargetingRuleListDetailController">
		<property name="successView" value="conditionelements.form" />
		<property name="formView" value="dataadmin/conditionelements_detail" />
	</bean>
	 	
	<!-- Target Group Beans -->
	<bean id="targetGroupController" class="com.prinova.messagepoint.controller.targeting.TargetGroupController" >
		<property name="formView" value="dataadmin/target_group_edit"/>
		<property name="successView" value="target_group_success.jsp"/>
		<property name="insertSuccessView" value="insert/insert_targeting_edit.form"/>
		<property name="tagSuccessView" value="tpadmin/tag_targeting_edit.form"/>
		<property name="attachmentSuccessView" value="attachment/attachment_targeting_edit.form"/>
		<property name="touchpointTargetingSuccessView" value="tpadmin/touchpoint_targeting_edit.form"/>
		<property name="validator" ref="targetGroupValidator" />
	</bean>

	
	<bean id="conditionElementDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.targeting.ConditionElement" />
		<property name="successView" value="conditionelements.form" />
		<property name="formView" value="delete_confirmation"/>
	</bean>

	<bean id="conditionElementEditValidator" class="com.prinova.messagepoint.controller.admin.ConditionElementEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.ConditionElementEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.rule.name" />
        			<property name="propertyType" ref="ObjectName" />
        			<property name="minLength" value="1" />
        		</bean>
	       	</list>
		</property>
	</bean>
	
	<bean id="conditionElementEditController" class="com.prinova.messagepoint.controller.admin.ConditionElementEditController">
		<property name="formView" value="dataadmin/condition_element_edit"/>
		<property name="successView" value="conditionelements.form"/>
		<property name="validator" ref="conditionElementEditValidator" />
	</bean>

	<!-- Data Groups -->
	<bean id="dataGroupEditValidator" class="com.prinova.messagepoint.controller.admin.DataGroupEditValidator" >
		<property name="className" value="com.prinova.messagepoint.model.admin.DataGroup" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
	       	</list>
		</property>
	</bean>
	
	<bean id="dataGroupEditController" class="com.prinova.messagepoint.controller.admin.DataGroupEditController">
		<property name="bindOnNewForm" value="true" />
		<property name="formView" value="dataadmin/data_group_edit"/>
		<property name="successView" value="datagroups.jsp"/>
		<property name="validator" ref="dataGroupEditValidator" />
	</bean>
	<bean id="dataGroupDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.admin.DataGroup" />
		<property name="successView" value="datagroups.jsp" />
		<property name="formView" value="delete_confirmation"/>
	</bean>

	<!-- Data Sources -->
	<bean id="dataSourceDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.admin.DataSource" />
		<property name="successView" value="delete_datamodel_confirmation.jsp" />
		<property name="formView" value="dataadmin/delete_datamodel_confirmation"/>
	</bean>

	<bean id="dataSourceEditValidator" class="com.prinova.messagepoint.controller.admin.DataSourceEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.DataSourceEditCommand" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.data.source.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="headers" />
        			<property name="label" value="page.label.header.records" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="2" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="externalId" />
        			<property name="label" value="page.label.externalID" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="12" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="delimeter" />
        			<property name="label" value="page.label.delimiter" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="2" />
					<property name="restrictedCharsList" value="page.text.validator.any.character" />
					<property name="restrictedCharsRegex" value=".*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="headers" />
        			<property name="label" value="page.label.header.records" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="2" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
	       	</list>
		</property>
	</bean>

	<bean id="dataSourceEditController" class="com.prinova.messagepoint.controller.admin.DataSourceEditController">
		<property name="formView" value="dataadmin/data_source_edit"/>
		<property name="successView" value="data_source_edit.form"/>
		<property name="validator" ref="dataSourceEditValidator" />
	</bean>

	<bean id="dataRecordDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.admin.DataRecord" />
		<property name="successView" value="delete_datamodel_confirmation.jsp" />
		<property name="formView" value="dataadmin/delete_datamodel_confirmation"/>
		<property name="validator" ref="messagePointDeletableValidator" />
	</bean>

	<bean id="dataRecordEditValidator" class="com.prinova.messagepoint.controller.admin.DataRecordEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.DataRecordEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="dataRecord.recordIndicator" />
        			<property name="label" value="page.label.recordindicator" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="512" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.space.dot.dash.underscore" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-\.]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="dataRecord.breakIndicator" />
        			<property name="label" value="page.label.breakindicator" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="32" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.dot.dash.underscore" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9_\-\.]*+" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="dataRecordEditController" class="com.prinova.messagepoint.controller.admin.DataRecordEditController">
		<property name="formView" value="dataadmin/data_record_edit"/>
		<property name="successView" value="data_record_edit.form"/>
		<property name="validator" ref="dataRecordEditValidator" />
	</bean>

	<bean id="dataGroupHierarchyViewController" class="com.prinova.messagepoint.controller.admin.DataGroupHierarchyViewController">
		<property name="formView" value="dataadmin/data_group_hierarchy_view"/>
	</bean>

<!--  start XmlDataTagDefinition beans -->
	<bean id="xmlDataTagDefinitionDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataTagDefinition" />
		<property name="successView" value="delete_datamodel_confirmation.jsp" />
		<property name="formView" value="dataadmin/delete_datamodel_confirmation"/>
		<property name="validator" ref="messagePointDeletableValidator" />
	</bean>

	<bean id="xmlDataElementDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataElement" />
		<property name="successView" value="delete_datamodel_confirmation.jsp" />
		<property name="formView" value="dataadmin/delete_datamodel_confirmation"/>
		<property name="validator" ref="messagePointDeletableValidator" />
	</bean>

	<bean id="xmlDataTagUploadEditValidator" class="com.prinova.messagepoint.controller.dataadmin.xmllayout.XmlDataTagUploadEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.dataadmin.xmllayout.XmlDataTagUploadEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="importOriginalFilename" />
        			<property name="label" value="page.label.tags.import.filename" />
        			<property name="propertyType" ref="FileName" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="255" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.dot.dash.backslash.slash.colon.space" />
					<property name="restrictedCharsRegex" value="^(?!~)[A-Za-z0-9!(){}\s_\-./\\:~]*+" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="domainsUploadEditValidator" class="com.prinova.messagepoint.controller.admin.DomainsUploadEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.DomainsUploadEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="importFilename.fileItem.name" />
        			<property name="label" value="page.label.tags.import.filename" />
        			<property name="propertyType" ref="FileName" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="255" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.dot.dash.backslash.slash.colon.space" />
					<property name="restrictedCharsRegex" value="^(?!~)[A-Za-z0-9!(){}\s_\-./\\:~]*+" />
        		</bean>
        	</list>
		</property>
	</bean>


	<bean id="xmlDataTagDefinitionEditValidator" class="com.prinova.messagepoint.controller.dataadmin.xmllayout.XmlDataTagDefinitionEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.dataadmin.xmllayout.XmlDataTagDefinitionEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="256" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.dot.dash.underscore.colon" />
					<property name="restrictedCharsErrorCode" value="error.message.xmldatatag.charinvalid" />
					<property name="restrictedCharsRegex" value="^[A-Za-zÁÉÍÓÚÜÑáéíóúüñ]{1}[A-Za-zÁÉÍÓÚÜÑáéíóúüñ0-9_\-\.:]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="breakIndicator" />
        			<property name="label" value="page.label.xml.tag.break.indicator" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="64" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.dot.dash.underscore.colon" />
					<property name="restrictedCharsRegex" value="^[A-Za-zÁÉÍÓÚÜÑáéíóúüñ0-9_\-\.\:]*+" />
        		</bean>
        	</list>
		</property>
	</bean>
	<bean id="xmlDataElementEditValidator" class="com.prinova.messagepoint.controller.dataadmin.xmllayout.XmlDataElementEditValidator" >
		<property name="className" value="com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataElement" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.edit.xml.element.name" />
                    <property name="minLength" value="1" />
                    <property name="maxLength" value="256" />
                    <property name="restrictedCharsList" value="page.text.validator.alphanum.space.dot.dash.underscore.colon" />
                    <property name="restrictedCharsErrorCode" value="error.message.attribute.charinvalid" />
                    <property name="restrictedCharsRegex" value="^[A-Za-zÁÉÍÓÚÜÑáéíóúüñ]{1}[A-Za-zÁÉÍÓÚÜÑáéíóúüñ0-9_\-\.:\s]*+" />
        		</bean>
        		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="attributeName" />
        			<property name="label" value="page.label.edit.xml.element.attribute.name" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="0" />
					<property name="maxLength" value="256" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.space.dot.dash.underscore.colon" />
					<property name="restrictedCharsErrorCode" value="error.message.attribute.charinvalid" />
					<property name="restrictedCharsRegex" value="^[A-Za-zÁÉÍÓÚÜÑáéíóúüñ]{1}[A-Za-zÁÉÍÓÚÜÑáéíóúüñ0-9_\-\.:]*+" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="xmlDataTagUploadEditController" class="com.prinova.messagepoint.controller.dataadmin.xmllayout.XmlDataTagUploadEditController">
		<property name="formView" value="dataadmin/xml_data_tag_upload_edit"/>
		<property name="successView" value="xml_data_tag_upload_edit.form"/>
		<property name="validator" ref="xmlDataTagUploadEditValidator" />
	</bean>
	<bean id="xmlDataTagDefinitionEditController" class="com.prinova.messagepoint.controller.dataadmin.xmllayout.XmlDataTagDefinitionEditController">
		<property name="formView" value="dataadmin/xml_data_tag_definition_edit"/>
		<property name="successView" value="xml_data_tag_definition_edit.form"/>
		<property name="validator" ref="xmlDataTagDefinitionEditValidator" />
	</bean>
	<bean id="xmlDataElementEditController" class="com.prinova.messagepoint.controller.dataadmin.xmllayout.XmlDataElementEditController">
		<property name="formView" value="dataadmin/xml_data_element_edit"/>
		<property name="successView" value="xml_data_element_edit.form"/>
		<property name="validator" ref="xmlDataElementEditValidator" />
	</bean>
<!-- End of XmlDataTagDefinition beans -->

	<!-- Start of JSONDataTagDefinition beans -->
	<bean id="jsonDataDefinitionDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataDefinition" />
		<property name="successView" value="delete_datamodel_confirmation.jsp" />
		<property name="formView" value="dataadmin/delete_datamodel_confirmation"/>
		<property name="validator" ref="messagePointDeletableValidator" />
	</bean>

	<bean id="jsonDataElementDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataElement" />
		<property name="successView" value="delete_datamodel_confirmation.jsp" />
		<property name="formView" value="dataadmin/delete_datamodel_confirmation"/>
		<property name="validator" ref="messagePointDeletableValidator" />
	</bean>

	<bean id="jsonDataDefinitionEditValidator" class="com.prinova.messagepoint.controller.dataadmin.jsonlayout.JSONDataDefinitionEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.dataadmin.jsonlayout.JSONDataDefinitionEditController$Command" />
		<property name="validationEntries">
			<list>
				<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
					<property name="propertyName" value="breakIndicator" />
					<property name="label" value="page.label.xml.tag.break.indicator" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="64" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.dot.dash.underscore.colon" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9_\-\.\:]*+" />
				</bean>
			</list>
		</property>
	</bean>
	<bean id="jsonDataKeyEditValidator" class="com.prinova.messagepoint.controller.dataadmin.jsonlayout.JSONDataKeyEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.dataadmin.jsonlayout.JSONDataDefinitionEditController$Command" />
		<property name="validationEntries">
			<list>
				<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
					<property name="propertyName" value="breakIndicator" />
					<property name="label" value="page.label.xml.tag.break.indicator" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="64" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.dot.dash.underscore.colon" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9_\-\.\:]*+" />
				</bean>
			</list>
		</property>
	</bean>
	<bean id="jsonDataElementEditValidator" class="com.prinova.messagepoint.controller.dataadmin.jsonlayout.JSONDataElementEditValidator" >
		<property name="className" value="com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataElement" />
		<property name="validationEntries">
			<list>
				<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
					<property name="propertyName" value="name" />
					<property name="label" value="page.label.name" />
					<property name="propertyType" ref="ObjectName" />
				</bean>
			</list>
		</property>
	</bean>
	<bean id="jsonDataDefinitionEditController" class="com.prinova.messagepoint.controller.dataadmin.jsonlayout.JSONDataDefinitionEditController">
		<property name="formView" value="dataadmin/json_data_definition_edit"/>
		<property name="successView" value="json_data_definition_edit.form"/>
		<property name="validator" ref="jsonDataDefinitionEditValidator" />
	</bean>
	<bean id="jsonDataKeyEditController" class="com.prinova.messagepoint.controller.dataadmin.jsonlayout.JSONDataDefinitionEditController">
		<property name="formView" value="dataadmin/json_data_key_edit"/>
		<property name="successView" value="json_data_key_edit.form"/>
		<property name="validator" ref="jsonDataKeyEditValidator" />
	</bean>
	<bean id="jsonDataElementEditController" class="com.prinova.messagepoint.controller.dataadmin.jsonlayout.JSONDataElementEditController">
		<property name="formView" value="dataadmin/json_data_element_edit"/>
		<property name="successView" value="json_data_element_edit.form"/>
		<property name="validator" ref="jsonDataElementEditValidator" />
	</bean>
	<!-- End JSON Data Definition and Element Edit Controllers -->

	<bean id="domainsUploadEditController" class="com.prinova.messagepoint.controller.admin.DomainsUploadEditController">
		<property name="formView" value="admin/domains_upload_edit"/>
		<property name="successView" value="domains_upload_edit.form"/>
		<property name="validator" ref="domainsUploadEditValidator" />
	</bean>

	<bean id="dataElementDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.admin.DataElement" />
		<property name="successView" value="delete_datamodel_confirmation.jsp" />
		<property name="formView" value="dataadmin/delete_datamodel_confirmation"/>
		<property name="validator" ref="messagePointDeletableValidator" />
	</bean>
	<bean id="dataElementEditValidator" class="com.prinova.messagepoint.controller.admin.DataElementEditValidator" >
		<property name="className" value="com.prinova.messagepoint.model.admin.DataElement" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="startLocation" />
        			<property name="label" value="page.label.location" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="5" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="length" />
        			<property name="label" value="page.label.length" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="5" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<bean id="dataElementEditController" class="com.prinova.messagepoint.controller.admin.DataElementEditController">
		<property name="formView" value="dataadmin/data_element_edit"/>
		<property name="successView" value="data_element_edit.form"/>
		<property name="validator" ref="dataElementEditValidator" />
	</bean>

	<bean id="dataSourceAssociationEditController" class="com.prinova.messagepoint.controller.admin.DataSourceAssociationEditController">
		<property name="formView" value="dataadmin/data_source_association_edit"/>
		<property name="successView" value="data_source_associations.jsp"/>
		<property name="validator" ref="dataSourceAssociationEditValidator" /> 
	</bean>

	<bean id="dataSourceAssociationEditValidator" class="com.prinova.messagepoint.controller.admin.DataSourceAssociationEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.DataSourceAssociationEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="defaultPageController" class="com.prinova.messagepoint.controller.DefaultPageController" >
		<property name="successView" value="index.jsp" />
	</bean>

	<!-- TEXT STYLES -->
	<bean id="textStyleListController" class="com.prinova.messagepoint.controller.content.TextStyleListController" >
		<property name="formView" value="content/text_style_list" />
		<property name="successView" value="text_style_list.form" />
	</bean>
	
	<bean id="textStyleConnectorsListController" class="com.prinova.messagepoint.controller.content.TextStyleConnectorsListController" >
		<property name="formView" value="content/text_style_connectors_list" />
		<property name="successView" value="text_style_connectors_list.form" />
	</bean>
	
	<bean id="textStyleListDetailController" class="com.prinova.messagepoint.controller.content.TextStyleListDetailController">
		<property name="successView" value="text_style_list.form" />
		<property name="formView" value="content/text_style_list_detail" />
	</bean>
	
	<!-- PARAGRAPH STYLES -->
	<bean id="paragraphStyleListController" class="com.prinova.messagepoint.controller.content.ParagraphStyleListController" >
		<property name="formView" value="content/paragraph_style_list" />
		<property name="successView" value="paragraph_style_list.form" />
		<property name="validator" ref="paragraphStyleListValidator"/>
	</bean>
	<bean id="paragraphStyleListValidator" class="com.prinova.messagepoint.controller.content.ParagraphStyleListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.content.ParagraphStyleListWrapper" />
	</bean>
	
	<bean id="paragraphStyleListDetailController" class="com.prinova.messagepoint.controller.content.ParagraphStyleListDetailController">
		<property name="successView" value="paragraph_style_list.form" />
		<property name="formView" value="content/paragraph_style_list_detail" />
	</bean>
		
	<bean id="paragraphStyleEditController" class="com.prinova.messagepoint.controller.content.ParagraphStyleEditController" >
		<property name="formView" value="content/paragraph_style_edit" />
		<property name="successView" value="paragraph_style_list.form" />
		<property name="validator" ref="paragraphStyleEditValidator"/>
	</bean>
	
	<bean id="paragraphStyleEditValidator" class="com.prinova.messagepoint.controller.content.ParagraphStyleEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.content.ParagraphStyleEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="connectorName" />
        			<property name="label" value="page.label.connector.name" />
        			<property name="propertyType" ref="ObjectName" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.dash.underscore.apos" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9_\-\.']*+" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<!-- LIST STYLES -->
	<bean id="listStyleListController" class="com.prinova.messagepoint.controller.content.ListStyleListController" >
		<property name="formView" value="content/list_style_list" />
		<property name="successView" value="list_style_list.form" />
		<property name="validator" ref="listStyleListValidator"/>
	</bean>
	<bean id="listStyleListValidator" class="com.prinova.messagepoint.controller.content.ListStyleListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.content.ListStyleListWrapper" />
	</bean>
	
	<bean id="listStyleListDetailController" class="com.prinova.messagepoint.controller.content.ListStyleListDetailController">
		<property name="successView" value="list_style_list.form" />
		<property name="formView" value="content/list_style_list_detail" />
	</bean>
		
	<bean id="listStyleEditController" class="com.prinova.messagepoint.controller.content.ListStyleEditController" >
		<property name="formView" value="content/list_style_edit" />
		<property name="successView" value="list_style_list.form" />
		<property name="validator" ref="listStyleEditValidator"/>
	</bean>
	
	<bean id="listStyleEditValidator" class="com.prinova.messagepoint.controller.content.ListStyleEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.content.ListStyleEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="listStyle.name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>

	<!-- TEXT STYLE TRANSFORMATION -->
	<bean id="textStyleTransformationListController" class="com.prinova.messagepoint.controller.content.TextStyleTransformationListController" >
		<property name="formView" value="content/text_style_transformation_list" />
		<property name="successView" value="text_style_transformation_list.form" />
		<property name="validator" ref="textStyleTransformationListValidator"/>
	</bean>
	<bean id="textStyleTransformationListValidator" class="com.prinova.messagepoint.controller.content.TextStyleTransformationListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.content.TextStyleTransformationListWrapper" />
	</bean>

	<bean id="textStyleTransformationEditController" class="com.prinova.messagepoint.controller.content.TextStyleTransformationEditController" >
		<property name="formView" value="content/text_style_transformation_edit" />
		<property name="successView" value="text_style_transformation_list.form" />
		<property name="validator" ref="textStyleTransformationEditValidator"/>
	</bean>

	<bean id="textStyleTransformationEditValidator" class="com.prinova.messagepoint.controller.content.TextStyleTransformationEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.content.TextStyleTransformationEditController$Command" />
		<property name="validationEntries">
			<list>
				<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
					<property name="propertyName" value="name" />
					<property name="label" value="page.label.name" />
					<property name="propertyType" ref="ObjectName" />
				</bean>
			</list>
		</property>
	</bean>
	
	<bean id="fontListController" class="com.prinova.messagepoint.controller.content.FontListController" >
		<property name="formView" value="content/font_list" />
	</bean>	
	
	<bean id="fontUploadController" class="com.prinova.messagepoint.controller.content.FontUploadController">
		<property name="formView" value="content/font_upload" />
		<property name="validator" ref="fontUploadValidator" />
	</bean>
	<bean id="fontUploadValidator" class="com.prinova.messagepoint.controller.content.FontUploadValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.content.FontUploadWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="textStyleFont.name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<bean id="fontDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.font.TextStyleFont" />
		<property name="successView" value="font_list.form" />
		<property name="formView" value="delete_confirmation"/>
	</bean>	

	<bean id="touchpointLanguageEditValidator" class="com.prinova.messagepoint.controller.tpadmin.TouchpointLanguageEditValidator" >
		<property name="className" value="com.prinova.messagepoint.model.TouchpointLanguage" />
	</bean>
	
	<bean id="touchpointLanguageEditController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointLanguageEditController" >
		<property name="sessionForm" value="true" />
		<property name="formView" value="tpadmin/touchpoint_language_edit"/>
		<property name="successView" value="document_view.form"/>
		<property name="validator" ref="touchpointLanguageEditValidator" />
	</bean>	

	<bean id="touchpointHealthCheckController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointHealthCheckController" >
		<property name="formView" value="tpadmin/touchpoint_health_check" />
		<property name="successView" value="touchpoint_health_check.form" />
	</bean>
	<bean id="touchpointHealthCheckDetailController" class="com.prinova.messagepoint.controller.tpadmin.TouchpointHealthCheckDetailController" >
		<property name="formView" value="tpadmin/touchpoint_health_check_detail" />
		<property name="successView" value="touchpoint_health_check.form" />
	</bean>

	<bean id="stripoEditorController" class="com.prinova.messagepoint.controller.tpadmin.StripoEditorController" >
		<property name="formView" value="tpadmin/stripo_editor" />
		<property name="successView" value="stripo_editor.form" />
		<property name="validator" ref="stripoEditorValidator"/>
	</bean>
	<bean id="stripoEditorValidator" class="com.prinova.messagepoint.controller.tpadmin.StripoEditorValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.tpadmin.EmailTemplatePreviewWrapper"/>
	</bean>

	<bean id="instanceStatisticsController" class="com.prinova.messagepoint.controller.stats.InstanceStatisticsController" >
		<property name="formView" value="stats/instance_statistics" />
		<property name="successView" value="instance_statistics.form" />
	</bean>

	<bean id="statsToolController" class="com.prinova.messagepoint.controller.stats.StatsToolController" >
		<property name="formView" value="stats/stats_tool" />
		<property name="successView" value="stats_tool.form" />
	</bean>
	<bean id="contentPowerEditController" class="com.prinova.messagepoint.controller.content.ContentPowerEditController" >
		<property name="formView" value="content/content_power_edit" />
		<property name="successView" value="content_power_edit.form" />
	</bean>
	<bean id="metatagsEditController" class="com.prinova.messagepoint.controller.dataadmin.MetatagsEditController" >
		<property name="formView" value="dataadmin/metatags_edit" />
		<property name="successView" value="metatags_edit.form" />
	</bean>

	<bean id="contentAssistantEditValidator" class="com.prinova.messagepoint.controller.contentintelligence.ContentAssistantEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.contentintelligence.ContentAssistantEditController$Command" />
		<property name="validationEntries">
			<list>
				<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
					<property name="propertyName" value="name" />
					<property name="label" value="page.label.name" />
					<property name="propertyType" ref="ObjectName" />
				</bean>
			</list>
		</property>
	</bean>
	<bean id="contentAssistantEditController" class="com.prinova.messagepoint.controller.contentintelligence.ContentAssistantEditController" >
		<property name="formView" value="contentintelligence/content_assistant_edit" />
		<property name="successView" value="content_assistant_edit.form" />
		<property name="validator" ref="contentAssistantEditValidator"/>
	</bean>
	<bean id="contentAssistantListController" class="com.prinova.messagepoint.controller.contentintelligence.ContentAssistantListController" >
		<property name="formView" value="contentintelligence/content_assistant_list" />
		<property name="successView" value="content_assistant_list.form" />
	</bean>



	<bean id="passwordRecoveryValidator" class="com.prinova.messagepoint.security.controller.PasswordRecoveryValidator" />
	<bean id="passwordRecoveryController" class="com.prinova.messagepoint.security.controller.PasswordRecoveryController" >
		<property name="formView" value="password_recovery"/>
		<property name="successView" value="password_recovery_confirmation.jsp"/>
		<property name="validator" ref="passwordRecoveryValidator"/>
	</bean>
		
	<!-- Rationalizer -->
	<bean id="rationalizerNavigationWidgetController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerNavigationWidgetController">
		<property name="successView" value="rationalizer_navigation_widget.form" />
		<property name="formView" value="rationalizer/rationalizer_navigation_widget" />
	</bean>
	<bean id="rationalizerCheckboxNavTreeWidgetController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerCheckboxNavTreeWidgetController">
		<property name="successView" value="rationalizer_checkbox_navigation_widget.form" />
		<property name="formView" value="rationalizer/rationalizer_checkbox_navigation_widget" />
	</bean>
	<bean id="rationalizerDocumentsListController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDocumentsListController">
		<property name="successView" value="rationalizer_documents_list.form" />
		<property name="formView" value="rationalizer/rationalizer_documents_list" />
		<property name="validator" ref="rationalizerDocumentsListValidator" />
	</bean>
	<bean id="rationalizerBulkDocumentUploadController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerBulkDocumentUploadController">
		<property name="successView" value="rationalizer_bulk_document_upload.form" />
		<property name="formView" value="rationalizer/rationalizer_bulk_document_upload" />
	</bean>
	<bean id="rationalizerTouchpointUploadController" class="com.prinova.messagepoint.controller.rationalizer.touchpoint.export.RationalizerTouchpointUploadController">
		<property name="successView" value="rationalizer_touchpoint_upload.form" />
		<property name="formView" value="rationalizer/rationalizer_touchpoint_upload" />
		<property name="validator" ref="rationalizerTouchpointUploadValidator" />
	</bean>
	<bean id="rationalizerTouchpointUploadValidator" class="com.prinova.messagepoint.controller.rationalizer.touchpoint.export.RationalizerTouchpointUploadValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.touchpoint.export.RationalizerTouchpointUploadWrapper" />
	</bean>
	<bean id="rationalizerExportToMessagepointController" class="com.prinova.messagepoint.controller.rationalizer.touchpoint.export.RationalizerExportToMessagepointController">
		<property name="successView" value="rationalizer_export_to_messagepoint.form"/>
		<property name="formView" value="rationalizer/rationalizer_export_to_messagepoint"/>
	</bean>
	<bean id="rationalizerDocumentsListValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDocumentsListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerDocumentListWrapper" />
	</bean>
	<bean id="rationalizerContentListController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerContentListController">
		<property name="successView" value="rationalizer_content_list.form" />
		<property name="formView" value="rationalizer/rationalizer_content_list" />
		<property name="validator" ref="rationalizerContentListValidator" />
	</bean>
	<bean id="rationalizerContentListValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerContentListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerContentListWrapper" />
	</bean>
	<bean id="rationalizerContentListDetailController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerContentListDetailController">
		<property name="successView" value="rationalizer_content_list.form" />
		<property name="formView" value="rationalizer/rationalizer_content_list_detail" />
	</bean>
	
	<bean id="rationalizerApplicationVisibilityController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerApplicationVisibilityController" >
		<property name="successView" value="rationalizer_application_visibility_edit.form"/>
		<property name="formView" value="rationalizer/rationalizer_application_visibility_edit"/>
		<property name="validator" ref="rationalizerApplicationVisibilityValidator"/>
	</bean>	
	<bean id="rationalizerApplicationVisibilityValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerApplicationVisibilityValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerApplicationVisibilityWrapper" />
	</bean>
	
	<bean id="rationalizerApplicationNavigationController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerApplicationNavigationController" >
		<property name="successView" value="rationalizer_application_navigation_edit.form"/>
		<property name="formView" value="rationalizer/rationalizer_application_navigation_edit"/>
		<property name="validator" ref="rationalizerApplicationNavigationValidator"/>
	</bean>

	<bean id="rationalizerDashboardCompareController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardCompareController" >
		<property name="successView" value="rationalizer_dashboard_compare.form"/>
		<property name="formView" value="rationalizer/rationalizer_dashboard_compare"/>
	</bean>
	<bean id="rationalizerApplicationNavigationValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerApplicationNavigationValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerApplicationNavigationWrapper" />
	</bean>
	
	<bean id="rationalizerDocumentEditController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDocumentEditController">
		<property name="successView" value="rationalizer_document_edit.form" />
		<property name="formView" value="rationalizer/rationalizer_document_edit" />
		<property name="validator" ref="rationalizerDocumentEditValidator" />
	</bean>
	<bean id="rationalizerDocumentEditValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDocumentEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerDocumentEditWrapper" />
	</bean>
	
	<bean id="rationalizerMetadataEditController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerMetadataEditController">
		<property name="successView" value="rationalizer_metadata_edit.form" />
		<property name="formView" value="rationalizer/rationalizer_metadata_edit" />
		<property name="validator" ref="rationalizerMetadataEditValidator" />
	</bean>

	<bean id="rationalizerMetadataEditValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerMetadataEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerMetadataEditWrapper" />
	</bean>

	<bean id="rationalizerDCFieldsController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDCFieldsController">
		<property name="successView" value="rationalizer_dc_fields.form" />
		<property name="formView" value="rationalizer/rationalizer_dc_fields" />
		<property name="validator" ref="rationalizerDCFieldsValidator" />
	</bean>

	<bean id="rationalizerDCFieldsValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDCFieldsValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerDCFieldsWrapper" />
	</bean>

	<bean id="rationalizerQueryListController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerQueryListController">
		<property name="successView" value="rationalizer_query_list.form" />
		<property name="formView" value="rationalizer/rationalizer_query_list" />
		<property name="validator" ref="rationalizerQueryListValidator" />
	</bean>
	<bean id="rationalizerQueryListValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerQueryListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerQueryListWrapper" />
	</bean>
	<bean id="rationalizerQueryEditController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerQueryEditController">
		<property name="successView" value="rationalizer_query_edit.form" />
		<property name="formView" value="rationalizer/rationalizer_query_edit" />
		<property name="validator" ref="rationalizerQueryEditValidator" />
	</bean>
	<bean id="rationalizerQueryEditValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerQueryEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerQueryEditWrapper" />
	</bean>
	<bean id="rationalizerQueryEditDetailController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerQueryEditDetailController">
		<property name="successView" value="rationalizer_query_edit_detail.form" />
		<property name="formView" value="rationalizer/rationalizer_query_edit_detail" />
		<property name="validator" ref="rationalizerQueryEditDetailValidator" />
	</bean>
	<bean id="rationalizerQueryEditDetailValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerQueryEditDetailValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerQueryEditDetailWrapper" />
	</bean>

	<bean id="rationalizerSharedContentListController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerSharedContentListController">
		<property name="successView" value="rationalizer_shared_content_list.form" />
		<property name="formView" value="rationalizer/rationalizer_shared_content_list" />
		<property name="validator" ref="rationalizerSharedContentListValidator" />
	</bean>
	<bean id="rationalizerSharedContentListValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerSharedContentListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerSharedContentListWrapper" />
	</bean>

	<bean id="rationalizerSharedContentListDetailController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerSharedContentListDetailController">
		<property name="successView" value="rationalizer_shared_content_list_detail.form" />
		<property name="formView" value="rationalizer/rationalizer_shared_content_list_detail"/>
	</bean>

	<bean id="rationalizerSharedContentEditController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerSharedContentEditController">
		<property name="successView" value="rationalizer_shared_content_edit.form" />
		<property name="formView" value="rationalizer/rationalizer_shared_content_edit" />
		<property name="validator" ref="rationalizerSharedContentEditValidator"/>
	</bean>
	<bean id="rationalizerSharedContentEditValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerSharedContentEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerSharedContentEditWrapper" />
	</bean>

	<bean id="rationalizerConsolidateController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerConsolidateController">
		<property name="successView" value="rationalizer_consolidate.form" />
		<property name="formView" value="rationalizer/rationalizer_consolidate" />
		<property name="validator" ref="rationalizerConsolidateValidator"/>
	</bean>
	<bean id="rationalizerConsolidateValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerConsolidateEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerConsolidateWrapper" />
	</bean>

	<bean id="rationalizerConsolidateEditController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerConsolidateEditController">
		<property name="successView" value="rationalizer_consolidate_edit.form" />
		<property name="formView" value="rationalizer/rationalizer_consolidate_edit" />
		<property name="validator" ref="rationalizerConsolidateEditValidator"/>
	</bean>
	<bean id="rationalizerConsolidateEditValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerConsolidateEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerConsolidateEditWrapper" />
	</bean>

	<bean id="rationalizerWorkflowAssignmentEditController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerWorkflowAssignmentEditController" >
		<property name="formView" value="rationalizer/rationalizer_workflow_assignment_edit"/>
		<property name="successView" value="rationalizer_workflow_assignment_edit.form"/>
		<property name="validator" ref="rationalizerWorkflowAssignmentEditValidator" />
	</bean>
	<bean id="rationalizerWorkflowAssignmentEditValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerWorkflowAssignmentEditValidator" >
		<property name="className" value="com.prinova.messagepoint.model.rationalizer.RationalizerApplication" />
	</bean>
	
	<bean id="rationalizerContentHistoryController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerContentHistoryController">
		<property name="successView" value="rationalizer_content_history.form" />
		<property name="formView" value="rationalizer/rationalizer_content_history" />
	</bean>
	
	<bean id="rationalizerMetadataHistoryController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerMetadataHistoryController">
		<property name="successView" value="rationalizer_metadata_history.form" />
		<property name="formView" value="rationalizer/rationalizer_metadata_history" />
	</bean>

	<bean id="rationalizerDashboardController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardController">
		<property name="successView" value="rationalizer_dashboard.form"/>
		<property name="formView" value="rationalizer/rationalizer_dashboard"/>
		<property name="validator" ref="rationalizerAddApplicationValidator"/>
	</bean>

	<bean id="rationalizerDashboardDuplicatesController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardDuplicatesController">
		<property name="successView" value="rationalizer_dashboard_duplicates.form"/>
		<property name="formView" value="rationalizer/rationalizer_dashboard_duplicates"/>
		<property name="validator" ref="rationalizerAddApplicationValidator"/>
	</bean>

	<bean id="rationalizerDashboardSimilaritiesController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardSimilaritiesController">
		<property name="successView" value="rationalizer_dashboard_similarities.form"/>
		<property name="formView" value="rationalizer/rationalizer_dashboard_similarities"/>
		<property name="validator" ref="rationalizerAddApplicationValidator"/>
	</bean>

	<bean id="rationalizerDashboardMetadataController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardMetadataController">
		<property name="successView" value="rationalizer_dashboard_metadata.form"/>
		<property name="formView" value="rationalizer/rationalizer_dashboard_metadata"/>
		<property name="validator" ref="rationalizerAddApplicationValidator"/>
	</bean>

	<bean id="rationalizerDashboardBrandController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardBrandController">
		<property name="successView" value="rationalizer_dashboard_brand.form"/>
		<property name="formView" value="rationalizer/rationalizer_dashboard_brand"/>
		<property name="validator" ref="rationalizerAddApplicationValidator"/>
	</bean>

	<bean id="rationalizerDashboardReadingController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardReadingController">
		<property name="successView" value="rationalizer_dashboard_reading.form"/>
		<property name="formView" value="rationalizer/rationalizer_dashboard_reading"/>
		<property name="validator" ref="rationalizerAddApplicationValidator"/>
	</bean>

	<bean id="rationalizerDashboardSentimentController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardSentimentController">
		<property name="successView" value="rationalizer_dashboard_sentiment.form"/>
		<property name="formView" value="rationalizer/rationalizer_dashboard_sentiment"/>
		<property name="validator" ref="rationalizerAddApplicationValidator"/>
	</bean>

	<bean id="rationalizerDashboardDuplicatesDetailsController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardDuplicatesDetailsController">
		<property name="successView" value="rationalizer_dashboard_duplicates_details.form" />
		<property name="formView" value="rationalizer/rationalizer_dashboard_duplicates_details"/>
		<property name="validator" ref="rationalizerDashboardDuplicatesDetailsValidator"/>
	</bean>
	<bean id="rationalizerDashboardDuplicatesDetailsValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardDuplicatesEditDetailValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardDetailsWrapper" />
	</bean>

	<bean id="rationalizerDashboardSimilaritiesDetailsController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardSimilaritiesDetailsController">
		<property name="successView" value="rationalizer_dashboard_similarities_details.form" />
		<property name="formView" value="rationalizer/rationalizer_dashboard_similarities_details"/>
		<property name="validator" ref="rationalizerDashboardSimilaritiesDetailsValidator"/>
	</bean>
	<bean id="rationalizerDashboardSimilaritiesDetailsValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardSimilaritiesEditDetailValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardDetailsWrapper" />
	</bean>

	<bean id="rationalizerAddApplicationValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerAddApplicationValidator">
	</bean>

	<bean id="rationalizerApplicationSetupValidator" class="com.prinova.messagepoint.controller.rationalizer.RationalizerApplicationPropertiesValidator">
		<property name="className" value="com.prinova.messagepoint.controller.rationalizer.RationalizerApplicationPropertiesWrapper" />
	</bean>

	<bean id="rationalizerDashboardMetadataDetailsController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardMetadataDetailsController">
		<property name="successView" value="rationalizer_dashboard_metadata_details.form" />
		<property name="formView" value="rationalizer/rationalizer_dashboard_metadata_details"/>
	</bean>

	<bean id="rationalizerDashboardBrandDetailsController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardBrandDetailsController">
		<property name="successView" value="rationalizer_dashboard_brand_details.form" />
		<property name="formView" value="rationalizer/rationalizer_dashboard_brand_details"/>
	</bean>

	<bean id="rationalizerDashboardSentimentDetailsController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardSentimentDetailsController">
		<property name="successView" value="rationalizer_dashboard_sentiment_details.form" />
		<property name="formView" value="rationalizer/rationalizer_dashboard_sentiment_details"/>
	</bean>

	<bean id="rationalizerDashboardReadingDetailsController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardReadingDetailsController">
		<property name="successView" value="rationalizer_dashboard_reading_details.form"/>
		<property name="formView" value="rationalizer/rationalizer_dashboard_reading_details"/>
	</bean>

	<bean id="rationalizerApplicationSetupController" class="com.prinova.messagepoint.controller.rationalizer.RationalizerApplicationSetupController">
		<property name="successView" value="rationalizer_application_setup.form"/>
		<property name="formView" value="rationalizer/rationalizer_application_setup"/>
		<property name="validator" ref="rationalizerApplicationSetupValidator" />
	</bean>

	<!-- Content Intelligence -->
	<bean id="contentCompareController" class="com.prinova.messagepoint.controller.contentintelligence.ContentCompareController">
		<property name="successView" value="content_compare.form" />
		<property name="formView" value="content/content_compare" />
		<property name="validator" ref="contentCompareValidator" />
	</bean>
	<bean id="contentCompareValidator" class="com.prinova.messagepoint.controller.contentintelligence.ContentCompareValidator">
		<property name="className" value="com.prinova.messagepoint.controller.contentintelligence.ContentCompareWrapper" />
	</bean>

	<bean id="brandCheckController" class="com.prinova.messagepoint.controller.brand.BrandCheckController">
		<property name="successView" value="brand_check.form" />
		<property name="formView" value="content/brand_check" />
		<property name="validator" ref="brandCheckValidator" />
	</bean>
	<bean id="brandCheckValidator" class="com.prinova.messagepoint.controller.brand.BrandCheckValidator">
		<property name="className" value="com.prinova.messagepoint.controller.brand.BrandCheckWrapper" />
	</bean>
	
	
	<!-- System Property -->
	<bean id="systemPropertyViewController" class="com.prinova.messagepoint.controller.admin.SystemPropertyViewController" >
		<property name="formView" value="admin/systemproperty_view" />
		<property name="successView" value="systemproperty_view.form"/>
	</bean>
	
	<bean id="systemPropertyController" class="com.prinova.messagepoint.controller.admin.SystemPropertyController" >
		<property name="formView" value="admin/systemproperty_edit" />
		<property name="successView" value="systemproperty_view.form" />
		<property name="validator" ref="systemPropertyValidator" />
	</bean>
	
	<bean id="systemPropertyPrinovaController" class="com.prinova.messagepoint.controller.admin.SystemPropertyController" >
		<property name="formView" value="admin/systemproperty_prinova_edit" />
		<property name="successView" value="systemproperty_prinova_edit.form" />
		<property name="validator" ref="systemPropertyValidator" />
	</bean>

	<bean id="systemPropertyValidator" class="com.prinova.messagepoint.controller.admin.SystemPropertyValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.SystemPropertyCommand" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="maxCustomersPerTest" />
        			<property name="label" value="page.label.admin.systemproperties.job_test_customers_max" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="4" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="previewEncoding" />
        			<property name="label" value="page.label.admin.systemproperties.platform_encodingtype_preview" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="5" />
					<property name="maxLength" value="6" />
					<property name="restrictedCharsList" value="page.text.validator.alphabetic" />
					<property name="restrictedCharsRegex" value="^[A-Za-z]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="productionEncoding" />
        			<property name="label" value="page.label.admin.systemproperties.platform_encodingtype_production" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="5" />
					<property name="maxLength" value="6" />
					<property name="restrictedCharsList" value="page.text.validator.alphabetic" />
					<property name="restrictedCharsRegex" value="^[A-Za-z]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="testEncoding" />
        			<property name="label" value="page.label.admin.systemproperties.platform_encodingtype_test" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="5" />
					<property name="maxLength" value="6" />
					<property name="restrictedCharsList" value="page.text.validator.alphabetic" />
					<property name="restrictedCharsRegex" value="^[A-Za-z]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="falseBooleanValue" />
        			<property name="label" value="page.label.admin.systemproperties.default_boolean_format_false" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="10" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="trueBooleanValue" />
        			<property name="label" value="page.label.admin.systemproperties.default_boolean_format_true" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="10" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="dateDefaultFormat" />
        			<property name="label" value="page.label.admin.systemproperties.default_date_format" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="20" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.symbols.space" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-,\./]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="replyToAddress" />
        			<property name="label" value="page.label.admin.systemproperties.email_cfg_appFromAddress" />
					<property name="mandatory" value="true" />
					<property name="propertyType" ref="Email" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="server" />
        			<property name="label" value="page.label.admin.systemproperties.email_server_outgoing" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="64" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.space.dot.dash.underscore.apos" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-\.']*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="accountPassword" />
        			<property name="label" value="page.label.admin.systemproperties.email_server_outgoing_password" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="255" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.symbols.space" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-\'\*!\.$\+=\?,~:;@#%&amp;\\/\[\]\{\}]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="emailServerPort" />
        			<property name="label" value="page.label.admin.systemproperties.email_server_outgoing_port" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="5" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^(\-)?[0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="accountName" />
        			<property name="label" value="page.label.admin.systemproperties.email_server_outgoing_user" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="255" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.symbols.space" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-\'\*!\.$\+=\?,:;@#\\/\[\]]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="emailsEnabled" />
        			<property name="label" value="page.label.admin.systemproperties_global_sendemail_booleanflag" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="4" />
					<property name="maxLength" value="5" />
					<property name="restrictedCharsList" value="page.text.validator.alphabetic" />
					<property name="restrictedCharsRegex" value="^[A-Za-z]*+" />
        		</bean>
		 	<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="emailProofServer" />
        			<property name="label" value="page.label.admin.systemproperties.email_server_outgoing" />
				<property name="mandatory" value="false" />
				<property name="minLength" value="1" />
				<property name="maxLength" value="64" />
				<property name="restrictedCharsList" value="page.text.validator.alphanum.space.dot.dash.underscore.apos" />
				<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-\.']*+" />
        		</bean>
	 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="emailProofServerPort" />
        			<property name="label" value="page.label.admin.systemproperties.email_server_outgoing_port" />
				<property name="mandatory" value="false" />
				<property name="minLength" value="1" />
				<property name="maxLength" value="5" />
				<property name="restrictedCharsList" value="page.text.validator.numeric" />
				<property name="restrictedCharsRegex" value="^(\-)?[0-9]*+" />
        		</bean>
		 	<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="emailProofAccountPassword" />
        			<property name="label" value="page.label.admin.systemproperties.email_server_outgoing_password" />
				<property name="mandatory" value="false" />
				<property name="minLength" value="1" />
				<property name="maxLength" value="255" />
				<property name="restrictedCharsList" value="page.text.validator.alphanum.symbols.space" />
				<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-\'\*!\.$\+=\?,~:;@#%&amp;\\/\[\]\{\}]*+" />
        		</bean>
	 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="emailProofAccountName" />
        			<property name="label" value="page.label.admin.systemproperties.email_server_outgoing_user" />
				<property name="mandatory" value="false" />
				<property name="minLength" value="1" />
				<property name="maxLength" value="255" />
				<property name="restrictedCharsList" value="page.text.validator.alphanum.symbols.space" />
				<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-\'\*!\.$\+=\?,:;@#\\/\[\]]*+" />
        		</bean>
	 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="emailProofEmailAddress" />
        			<property name="label" value="page.label.email.proof.emailaddress.settings" />
        			<property name="propertyType" ref="Email" />
				<property name="mandatory" value="false" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="entriesPerList" />
        			<property name="label" value="page.label.admin.systemproperties.default_page_size" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="3" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="dateFormat" />
        			<property name="label" value="page.label.admin.systemproperties.system_date_format" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="20" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.symbols.space" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-,\./\\]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="webServerPort" />
        			<property name="label" value="page.label.admin.systemproperties.system_server_port" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="2" />
					<property name="maxLength" value="5" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="protocol" />
        			<property name="label" value="page.label.admin.systemproperties.system_server_protocol" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="4" />
					<property name="maxLength" value="5" />
					<property name="restrictedCharsList" value="page.text.validator.alphabetic" />
					<property name="restrictedCharsRegex" value="^[A-Za-z]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="webroot" />
        			<property name="label" value="page.label.admin.systemproperties.system_server_webroot" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="255" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.dot.dash.backslash.slash.space" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-./\\]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="applicationFolder" />
        			<property name="label" value="page.label.admin.systemproperties.message_folder_applicationreport" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="cacheFolder" />
        			<property name="label" value="page.label.admin.systemproperties.message_folder_applicationcache" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="dispatchFolder" />
        			<property name="label" value="page.label.admin.systemproperties.message_folder_dispatch" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="imagesFolder" />
        			<property name="label" value="page.label.admin.systemproperties.message_folder_images" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="messageOutputFolder" />
        			<property name="label" value="page.label.admin.systemproperties.message_folder_output" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="messageExportFolder" />
        			<property name="label" value="page.label.admin.systemproperties.message_folder_export" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="imageContentHistoryFolder" />
        			<property name="label" value="page.label.admin.systemproperties.message_messageContent_history_dir" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="simulationExportFolder" />
        			<property name="label" value="page.label.admin.systemproperties.message_messageContent_history_dir" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="incommingFolder" />
        			<property name="label" value="page.label.admin.systemproperties.job_folder_incoming" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="faildJobsFolder" />
        			<property name="label" value="page.label.admin.systemproperties.job_folder_incoming_failure" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="successfulJobsFolder" />
        			<property name="label" value="page.label.admin.systemproperties.job_folder_incoming_sucess" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="outgoingFolder" />
        			<property name="label" value="page.label.admin.systemproperties.job_folder_outgoing" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="inProcessFolder" />
        			<property name="label" value="page.label.admin.systemproperties.job_folder_working" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="applicationReportsFolder" />
        			<property name="label" value="page.label.admin.systemproperties.message_folder_applicationreport" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="dataFilesFolder" />
        			<property name="label" value="page.label.admin.systemproperties.message_folder_datafiles" />
					<property name="mandatory" value="true" />
        			<property name="propertyType" ref="FileName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="remoteServerPort" />
        			<property name="label" value="page.label.remote.server.port" />
					<property name="minLength" value="3" />
					<property name="maxLength" value="5" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
        		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="deVersions" />
        			<property name="label" value="page.label.admin.systemproperties.de_versions" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="255" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.space.dot.dash.comma.semi.underscore" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-\.,:]*+" />
        		</bean>
        	</list>
		</property>
	</bean>

	<!--
	<bean id="systemPropertiesActions" class="org.springframework.web.servlet.mvc.multiaction.ParameterMethodNameResolver">
	    <property name="paramName">
	    	<value>action</value>
	    </property>
	</bean>
	-->

	<!-- Security Settings -->
	<bean id="securitySettingsValidator" class="com.prinova.messagepoint.controller.admin.SecuritySettingsController$SecuritySettingsValidator" >
		<property name="className" value="com.prinova.messagepoint.model.admin.SecuritySettings" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="minLength" />
        			<property name="label" value="page.label.minimum.length" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="2" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="usernameMinLength" />
        			<property name="label" value="page.label.minimum.length" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="2" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="usernameMaxLength" />
        			<property name="label" value="page.label.maximum.length" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="2" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="maxAttempts" />
        			<property name="label" value="page.label.invalidsigninattemps" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="2" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="pwResetKeepAlive" />
        			<property name="label" value="page.label.admin.systemproperties.password_reset_keepalive" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="3" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="securitySettingsController" class="com.prinova.messagepoint.controller.admin.SecuritySettingsController" >
		<property name="formView" value="admin/password_security_edit"/>
		<property name="successView" value="password_security_view.form"/>
		<property name="validator" ref="securitySettingsValidator" />
	</bean>

	<bean id="securitySettingsViewController" class="com.prinova.messagepoint.controller.admin.SecuritySettingsViewController" >
		<property name="formView" value="admin/password_security_view"/>
		<property name="successView" value="password_security_view.form"/>
	</bean>
	
	<!-- Pod/Domain Settings -->
	<bean id="podListController" class="com.prinova.messagepoint.controller.admin.PodListController">
		<property name="formView" value="admin/pod_list"/>
		<property name="successView" value="pod_list.form"/>
		<property name="podEditRedirect" value="pod_edit.form"/>
		<property name="validator" ref="podListValidator" /> 
	</bean>
	<bean id="podListValidator" class="com.prinova.messagepoint.controller.admin.PodListValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.PodListWrapper" />
	</bean>
	
	<bean id="mapDomainsController" class="com.prinova.messagepoint.controller.admin.MapDomainsController" >
		<property name="formView" value="admin/map_domains"/>
		<property name="successView" value="pod_list.form"/>
		<property name="validator" ref="mapDomainsValidator" />
	</bean>
	<bean id="mapDomainsValidator" class="com.prinova.messagepoint.controller.admin.MapDomainsValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.MapDomainsWrapper" />
	</bean>
	
	<bean id="podListDetailController" class="com.prinova.messagepoint.controller.admin.PodListDetailController">
		<property name="successView" value="pod_list.form" />
		<property name="formView" value="admin/pod_list_detail" />
	</bean>	
	
	<bean id="podEditController" class="com.prinova.messagepoint.controller.admin.PodEditController">
		<property name="bindOnNewForm" value="true" />
		<property name="formView" value="admin/pod_edit"/>
		<property name="successView" value="pod_view.form"/>
		<property name="validator" ref="podEditValidator" />
	</bean>
	
	<bean id="podEditValidator" class="com.prinova.messagepoint.controller.admin.PodEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.PodEditController$Command" />
	</bean>
	
	<bean id="podViewController" class="com.prinova.messagepoint.controller.admin.PodViewController">
		<property name="formView" value="admin/pod_view"/>
		<property name="editView" value="pod_edit.form"/>		
		<property name="successView" value="pod_view.form"/>
	</bean>
	
	<!-- DEServer Settings -->
	<bean id="deServerListController" class="com.prinova.messagepoint.controller.admin.DEServerListController">
		<property name="formView" value="admin/deserver_list"/>
		<property name="successView" value="deserver_list.form"/>
		<property name="deServerEditRedirect" value="deserver_edit.form"/>
		<property name="validator" ref="deServerListValidator" /> 
	</bean>
	
	<bean id="deServerListValidator" class="com.prinova.messagepoint.controller.admin.DEServerListValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.DEServerListWrapper" />
	</bean>
	
	<bean id="deServerListDetailController" class="com.prinova.messagepoint.controller.admin.DEServerListDetailController">
		<property name="successView" value="deserver_list.form" />
		<property name="formView" value="admin/deserver_list_detail" />
	</bean>	
	
	<bean id="deServerEditController" class="com.prinova.messagepoint.controller.admin.DEServerEditController">
		<property name="bindOnNewForm" value="true" />
		<property name="formView" value="admin/deserver_edit"/>
		<property name="successView" value="deserver_view.form"/>
		<property name="validator" ref="deServerEditValidator" />
	</bean>
	
	<bean id="deServerEditValidator" class="com.prinova.messagepoint.controller.admin.DEServerEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.DEServerEditWrapper" />
	</bean>
	
	<bean id="deServerViewController" class="com.prinova.messagepoint.controller.admin.DEServerViewController">
		<property name="formView" value="admin/deserver_view"/>
		<property name="editView" value="deserver_edit.form"/>		
		<property name="successView" value="deserver_view.form"/>
	</bean>

	<bean id="developmentTestController" class="com.prinova.messagepoint.controller.admin.DevelopmentTestController">
		<property name="successView" value="admin/dev_test.form" />
	</bean>
	
	<!--  Branch Settings  -->
	<bean id="branchListController" class="com.prinova.messagepoint.controller.admin.BranchListController">
		<property name="successView" value="branch_list.form" />
		<property name="branchEditRedirect" value="branch_edit.form"/>
		<property name="formView" value="admin/branch_list" />
		<property name="validator" ref="branchListValidator"/>
	</bean>
	
	<bean id="branchListValidator" class="com.prinova.messagepoint.controller.admin.BranchListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.BranchListWrapper" />
	</bean>
	<bean id="branchListDetailController" class="com.prinova.messagepoint.controller.admin.BranchListDetailController">
		<property name="successView" value="branch_list_detail.form" />
		<property name="formView" value="admin/branch_list_detail" />
		<property name="validator" ref="branchListDetailValidator"/>
	</bean>
	<bean id="branchListDetailValidator" class="com.prinova.messagepoint.controller.admin.BranchListDetailValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.BranchListDetailWrapper" />
	</bean>
		
	<bean id="branchEditController" class="com.prinova.messagepoint.controller.admin.BranchEditController" >
		<property name="successView" value="branch_list.form"/>
		<property name="formView" value="admin/branch_edit"/>
		<property name="validator" ref="branchEditValidator"/>
	</bean>
	<bean id="branchEditValidator" class="com.prinova.messagepoint.controller.admin.BranchEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.BranchEditWrapper" />
	</bean>
	
	<!-- Node Settings -->
	<bean id="nodeViewController" class="com.prinova.messagepoint.controller.admin.NodeEditController" >
		<property name="successView" value="branch_list.form"/>
		<property name="formView" value="admin/node_view"/>
	</bean>
	
	<bean id="nodeEditController" class="com.prinova.messagepoint.controller.admin.NodeEditController" >
		<property name="successView" value="branch_list.form"/>
		<property name="formView" value="admin/node_edit"/>
		<property name="validator" ref="nodeEditValidator"/>
	</bean>
	
	<bean id="nodeEditValidator" class="com.prinova.messagepoint.controller.admin.NodeEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.NodeEditWrapper" />
	</bean>
	
	<!--  Maintenance Settings -->
	<bean id="maintenanceViewController" class="com.prinova.messagepoint.controller.admin.MaintenanceViewController" >
		<property name="formView" value="admin/maintenance_view"/>
	</bean>
	
	<bean id="maintenanceEditController" class="com.prinova.messagepoint.controller.admin.MaintenanceEditController">
		<property name="formView" value="admin/maintenance_edit"/>
		<property name="successView" value="maintenance_view.form"/>
		<property name="validator" ref="maintenanceEditValidator" />
	</bean>	
	
	<bean id="maintenanceEditValidator" class="com.prinova.messagepoint.controller.admin.MaintenanceEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.MaintenanceEditController$Command" />
	</bean>
	
	<bean id="deliveryEventEditValidator" class="com.prinova.messagepoint.controller.admin.DeliveryEventEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.DeliveryEventEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="deliveryEventListController" class="com.prinova.messagepoint.controller.admin.DeliveryEventListController">
		<property name="formView" value="tpadmin/delivery_event_list"/>
		<property name="successView" value="delivery_event_list.form"/>
<!--		<property name="validator" ref="deliveryEventListValidator" />-->
	</bean>
	
	<bean id="deliveryEventEditController" class="com.prinova.messagepoint.controller.admin.DeliveryEventEditController">
		<property name="bindOnNewForm" value="true" />
		<property name="formView" value="tpadmin/deliveryevent_edit"/>
		<property name="successView" value="delivery_event_list.form"/>
		<property name="validator" ref="deliveryEventEditValidator" />
	</bean>

	<bean id="productionEventDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.DocumentProductionEvent" />
		<property name="successView" value="delivery_event_list.form" />
		<property name="formView" value="delete_confirmation"/>
	</bean>

	<bean id="userEditValidator" class="com.prinova.messagepoint.validator.MessagepointInputValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.UserEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="firstName" />
        			<property name="label" value="page.label.firstname" />
        			<property name="propertyType" ref="PersonName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="lastName" />
        			<property name="label" value="page.label.lastname" />
        			<property name="propertyType" ref="PersonName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="email" />
        			<property name="label" value="page.label.email" />
        			<property name="propertyType" ref="Email" />
        			<property name="mandatory" value="true" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="phone" />
        			<property name="label" value="page.label.phone" />
        			<property name="propertyType" ref="Phone" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="username" />
        			<property name="label" value="page.label.username" />
					<property name="propertyType" ref="Username" />
        		</bean>
        	</list>
		</property>
	</bean>

	<!--  Services  -->
	<bean id="servicesListController" class="com.prinova.messagepoint.controller.admin.ServicesListController">
		<property name="successView" value="services_list.form" />
		<property name="servicesViewRedirect" value="services_view.form"/>
		<property name="formView" value="admin/services_list" />
		<property name="validator" ref="servicesListValidator"/>
	</bean>

	<bean id="servicesListValidator" class="com.prinova.messagepoint.controller.admin.ServicesListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.ServicesListWrapper" />
	</bean>

	<bean id="servicesViewController" class="com.prinova.messagepoint.controller.admin.ServicesViewController" >
		<property name="formView" value="admin/services_view"/>
		<property name="editView" value="services_edit.form"/>
		<property name="validator" ref="servicesViewValidator"/>
	</bean>

	<bean id="servicesViewValidator" class="com.prinova.messagepoint.controller.admin.ServicesViewValidator"/>

	<bean id="servicesEditController" class="com.prinova.messagepoint.controller.admin.ServicesEditController" >
		<property name="successView" value="services_list.form"/>
		<property name="formView" value="admin/services_edit"/>
		<property name="validator" ref="servicesEditValidator"/>
	</bean>

	<bean id="servicesEditValidator" class="com.prinova.messagepoint.validator.MessagepointInputValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.ServicesEditController$Command" />
		<property name="validationEntries">
			<list>
				<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
					<property name="propertyName" value="name" />
					<property name="label" value="page.label.name" />
					<property name="propertyType" ref="ObjectName" />
				</bean>
			</list>
		</property>
	</bean>

	<!--  Users  -->
	<bean id="userViewController" class="com.prinova.messagepoint.controller.admin.UserViewController" >
		<property name="formView" value="admin/user_view"/>
		<property name="validator" ref="userViewValidator"/>
	</bean>
	<bean id="userViewValidator" class="com.prinova.messagepoint.controller.admin.UserViewValidator"/>
	
	<bean id="userEditController" class="com.prinova.messagepoint.controller.admin.UserEditController" >
		<property name="successView" value="user_list.form"/>
		<property name="formView" value="admin/user_edit"/>
		<property name="validator" ref="userEditValidator"/>
	</bean>
	<bean id="userAccessControlController" class="com.prinova.messagepoint.controller.admin.UserAccessControlController" >
		<property name="successView" value="user_list.form"/>
		<property name="formView" value="admin/user_access_control"/>
		<property name="validator" ref="userAccessControlValidator"/>
	</bean>
	<bean id="userAccessControlValidator" class="com.prinova.messagepoint.controller.admin.UserAccessControlValidator"/>
	
	<bean id="userDeleteDetailController" class="com.prinova.messagepoint.controller.admin.UserDeleteDetailController" >
		<property name="successView" value="user_list.form"/>
		<property name="formView" value="admin/user_delete_detail"/>
		<property name="validator" ref="userDeleteDetailValidator"/>
	</bean>
	<bean id="userDeleteDetailValidator" class="com.prinova.messagepoint.controller.admin.UserDeleteDetailValidator"/>
	
	<!--  User List  -->
	<bean id="userListController" class="com.prinova.messagepoint.controller.admin.UserListController">
		<property name="successView" value="user_list.form" />
		<property name="userViewRedirect" value="user_view.form"/>
		<property name="formView" value="admin/user_list" />
		<property name="validator" ref="userListValidator"/>
	</bean>
	
	<bean id="userListValidator" class="com.prinova.messagepoint.controller.admin.UserListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.UserListWrapper" />
	</bean>
	
	<!-- bean id="userListDetailController" class="com.prinova.messagepoint.controller.admin.UserListDetailController">
		<property name="successView" value="user_list_detail.form" />
		<property name="formView" value="admin/user_list_detail" />
		<property name="validator" ref="userListDetailValidator"/>
	</bean>
	<bean id="userListDetailValidator" class="com.prinova.messagepoint.controller.admin.UserhListDetailValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.UserListDetailWrapper" />
	</bean-->
		
	<bean id="userSettingsController" class="com.prinova.messagepoint.controller.admin.UserSettingsController" >
		<property name="formView" value="user/settings_edit"/>
		<property name="successView" value="settings_edit.form"/>
		<property name="applicationSecurityManager" ref="applicationSecurityManager"/>
		<property name="validator" ref="userSettingsValidator"/>
		<property name="errorPage" value="./displaymessage.form?msgkey=page.label.error"/>
	</bean>
	
	<bean id="editUserIdValidator" class="com.prinova.messagepoint.controller.admin.UserIdEditController$Validator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.UserIdEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="username" />
        			<property name="label" value="page.label.username" />
					<property name="propertyType" ref="Username" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<bean id="editUserIdController" class="com.prinova.messagepoint.controller.admin.UserIdEditController">
		<property name="formView" value="userid_confirmation" />
		<property name="successView" value="index.jsp" />
		<property name="validator" ref="editUserIdValidator"/>
		<property name="bindOnNewForm" value="true" />
	</bean>
	
	<bean id="editPasswordValidator" class="com.prinova.messagepoint.controller.admin.PasswordEditController$Validator" />
	
	<bean id="editPasswordController" class="com.prinova.messagepoint.controller.admin.PasswordEditController">
		<property name="formView" value="password_confirmation" />
		<property name="successView" value="index.jsp" />
		<property name="validator" ref="editPasswordValidator"/>
		<property name="bindOnNewForm" value="true" />
	</bean>
	
	<bean id="editUserIdAndPasswordValidator" class="com.prinova.messagepoint.controller.admin.UserIdAndPasswordEditController$Validator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.UserIdAndPasswordEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="username" />
        			<property name="label" value="page.label.username" />
					<property name="propertyType" ref="Username" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<bean id="editUserIdAndPasswordController" class="com.prinova.messagepoint.controller.admin.UserIdAndPasswordEditController">
		<property name="formView" value="userid_password_confirmation" />
		<property name="successView" value="index.jsp" />
		<property name="validator" ref="editUserIdAndPasswordValidator"/>
		<property name="bindOnNewForm" value="true" />
	</bean>
	
	<!-- Role -->
	<bean id="roleListController" class="com.prinova.messagepoint.controller.admin.RoleListController" >
		<property name="formView" value="admin/role_list" />
		<property name="successView" value="role_list.form" />
		<property name="validator" ref="roleListValidator"/>
	</bean>
	
	<bean id="roleListValidator" class="com.prinova.messagepoint.controller.admin.RoleListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.RoleListWrapper" />
	</bean>
	
	<bean id="roleViewController" class="com.prinova.messagepoint.controller.admin.RoleViewController" >
		<property name="formView" value="admin/role_view" />
		<property name="validator" ref="roleDeleteValidator" />
	</bean>
	<bean id="roleDeleteValidator" class="com.prinova.messagepoint.controller.admin.RoleDeleteValidator"/>
	
	<bean id="roleEditController" class="com.prinova.messagepoint.controller.admin.RoleEditController" >
		<property name="formView" value="admin/role_edit" />
		<property name="successView" value="role_list.form" />
		<property name="validator" ref="roleEditValidator"/>
	</bean>
	
	<bean id="roleEditValidator" class="com.prinova.messagepoint.controller.admin.RoleEditValidator" >
		<property name="className" value="com.prinova.messagepoint.controller.admin.RoleEditController$RolePermissionsCommand" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.rolename" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="description" />
        			<property name="label" value="page.label.role.description" />
        			<property name="propertyType" ref="Description" />
        		</bean>        		
        	</list>
		</property>
	</bean>
	
	<bean id="tenantPermissionEditController" class="com.prinova.messagepoint.controller.admin.TenantPermissionsEditController" >
		<property name="formView" value="admin/tenant_permissions_edit" />
		<property name="successView" value="tenant_permissions_view.form" />
	</bean>

	<bean id="tenantPermissionViewController" class="com.prinova.messagepoint.controller.admin.TenantPermissionsViewController" >
		<property name="formView" value="admin/tenant_permissions_view" />
		<property name="successView" value="tenant_permissions_view.form" />
	</bean>

	<bean id="filerootManagementEditController" class="com.prinova.messagepoint.controller.admin.FilerootManagementEditController">
		<property name="formView" value="admin/fileroot_management_edit"/>
		<property name="successView" value="fileroot_management_edit.form"/>
		<property name="validator" ref="filerootManagementEditValidator" />
	</bean>

	<bean id="dataExpirationEditController" class="com.prinova.messagepoint.controller.admin.DataExpirationEditController">
		<property name="formView" value="admin/data_expiration_edit"/>
		<property name="successView" value="data_expiration_edit.form"/>
	</bean>

	<bean id="marcieDiagnosticController" class="com.prinova.messagepoint.controller.admin.MarcieDiagnosticController">
		<property name="formView" value="admin/marcie_diagnostic"/>
		<property name="successView" value="marcie_diagnostic.form"/>
	</bean>

	<bean id="filerootManagementEditValidator" class="com.prinova.messagepoint.controller.admin.FilerootManagementEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.FilerootManagementEditWrapper" />
	</bean>

	<bean id="workgroupZoneAssociationsViewController" class="com.prinova.messagepoint.controller.workgroup.WorkgroupZoneAssociationsViewController" >
		<property name="formView" value="admin/workgroup_associations_view" />
	</bean>

	<bean id="workgroupZoneAssociationsEditController" class="com.prinova.messagepoint.controller.workgroup.WorkgroupZoneAssociationsEditController" >
		<property name="formView" value="admin/workgroup_associations_edit" />
		<property name="successView" value="workgroup_associations_view.form" />
	</bean>

	<bean id="workgroupViewController" class="com.prinova.messagepoint.controller.workgroup.WorkgroupViewController" >
		<property name="formView" value="admin/workgroup_view" />
	</bean>

	<bean id="workgroupEditController" class="com.prinova.messagepoint.controller.workgroup.WorkgroupEditController" >
		<property name="formView" value="admin/workgroup_edit" />
		<property name="successView" value="workgroup_view.form" />
		<property name="validator" ref="workgroupEditValidator"/>
	</bean>
	
	<bean id="workgroupDeleteController" class="com.prinova.messagepoint.controller.workgroup.WorkgroupDeleteController" >
		<property name="formView" value="admin/workgroup_delete_confirmation" />
		<property name="successView" value="workgroup_associations_view.form" />
	</bean>

	<bean id="workgroupEditValidator" class="com.prinova.messagepoint.validator.MessagepointInputValidator">
		<property name="className" value="com.prinova.messagepoint.controller.workgroup.WorkgroupEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="description" />
        			<property name="label" value="page.label.description" />
        			<property name="propertyType" ref="Description" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<!-- Parameters -->
	<bean id="parameterListController" class="com.prinova.messagepoint.controller.parameter.ParameterListController" >
		<property name="formView" value="dataadmin/parameter_list" />
	</bean>

	<bean id="parameterEditController" class="com.prinova.messagepoint.controller.parameter.ParameterEditController" >
		<property name="formView" value="dataadmin/parameter_edit" />
		<property name="successView" value="parameter_list.form" />
		<property name="validator" ref="parameterEditValidator"/>
	</bean>
	
	<bean id="parameterEditValidator" class="com.prinova.messagepoint.validator.MessagepointInputValidator">
		<property name="className" value="com.prinova.messagepoint.controller.parameter.ParameterEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="description" />
        			<property name="label" value="page.label.description" />
        			<property name="propertyType" ref="Description" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<bean id="parameterDeleteController" class="com.prinova.messagepoint.controller.parameter.ParameterDeleteController">
		<property name="formView" value="dataadmin/parameter_delete_confirmation"/>
		<property name="successView" value="parameter_list.form" />
	</bean>	
	
	<bean id="parameterGroupEditController" class="com.prinova.messagepoint.controller.parameter.ParameterGroupEditController" >
		<property name="formView" value="dataadmin/parameter_group_edit" />
		<property name="successView" value="parameter_group_list.form" />
		<property name="validator" ref="parameterGroupEditValidator"/>
	</bean>

	<bean id="parameterGroupEditValidator" class="com.prinova.messagepoint.controller.parameter.ParameterGroupEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.parameter.ParameterGroupEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="description" />
        			<property name="label" value="page.label.description" />
        			<property name="propertyType" ref="Description" />
        		</bean>
        	</list>
		</property>
	</bean>
	
	<bean id="parameterGroupListController" class="com.prinova.messagepoint.controller.parameter.ParameterGroupListController" >
		<property name="formView" value="dataadmin/parameter_group_list" />
	</bean>
	<bean id="parameterGroupDeleteController" class="com.prinova.messagepoint.controller.DeleteCommandController">
		<property name="className" value="com.prinova.messagepoint.model.admin.ParameterGroup" />
		<property name="successView" value="parameter_group_list.form" />
		<property name="formView" value="delete_confirmation"/>
	</bean>	
	
	<!-- External Event -->	
	<bean id="externalEventListController" class="com.prinova.messagepoint.controller.externalevent.ExternalEventListController">
		<property name="successView" value="external_event_list.form" />
		<property name="formView" value="dataadmin/external_event_list" />
		<property name="validator" ref="externalEventListValidator"/>
	</bean>	
	
	<bean id="externalEventListValidator" class="com.prinova.messagepoint.controller.externalevent.ExternalEventListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.externalevent.ExternalEventListWrapper" />
	</bean>	


	<bean id="scenarioCustomerReportViewController" class="com.prinova.messagepoint.reports.ScenarioCustomerReportViewController" >
		<property name="formView" value="reports/scenario_customer_report_view" />
		<property name="successView" value="scenario_customer_report_view.form" />
		<property name="validator" ref="scenarioCustomerViewValidator"/>
	</bean>

	<bean id="scenarioCustomerReportInsertViewController" class="com.prinova.messagepoint.reports.ScenarioCustomerReportInsertViewController" >
		<property name="formView" value="reports/scenario_customer_insert_report_view" />
		<property name="successView" value="scenario_customer_insert_report_view.form" />
		<property name="validator" ref="scenarioCustomerViewValidator"/>
	</bean>


	<bean id="scenarioCustomerViewValidator" class="com.prinova.messagepoint.validator.MessagepointInputValidator">
		<property name="className" value="com.prinova.messagepoint.reports.ScenarioCustomerViewCommand" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="customerId" />
        			<property name="label" value="page.label.customerId" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="64" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.space.dash.underscore" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-]*+$" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="field1" />
        			<property name="label" value="page.label.customerfield.first" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="64" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.symbols" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-'\*.$!+=\?,:;@\[\]\\/]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="field2" />
        			<property name="label" value="page.label.customerfield.second" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="64" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.symbols" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-'\*.$!+=\?,:;@\[\]\\/]*+" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="scenarioMessageSubReportViewController" class="com.prinova.messagepoint.reports.ScenarioMessageSubReportViewController" >
		<property name="formView" value="reports/reports_message_sub" />
		<property name="successView" value="reports_message_sub.form" />
		<property name="validator" ref="scenarioCustomerViewValidator"/>
	</bean>

	<bean id="scenarioInsertSubReportViewController" class="com.prinova.messagepoint.reports.ScenarioInsertSubReportViewController" >
		<property name="formView" value="reports/reports_insert_sub" />
		<property name="successView" value="reports_insert_sub.form" />
		<property name="validator" ref="scenarioCustomerViewValidator"/>
	</bean>

	<bean id="testingListController" class="com.prinova.messagepoint.controller.testing.TestingListController" >
		<property name="successView" value="testing_list.form" />
		<property name="scenarioEditRedirect" value="scenario_edit.form" />
		<property name="formView" value="testing/testing_list" />
		<property name="validator" ref="testingListValidator"/>
	</bean>
	
	<bean id="testingListValidator" class="com.prinova.messagepoint.controller.testing.TestingListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.testing.TestingListWrapper" />
	</bean>
	
	<bean id="testSuiteListController" class="com.prinova.messagepoint.controller.testing.TestSuiteListController" >
		<property name="successView" value="test_suite_list.form" />
		<property name="testSuiteEditRedirect" value="test_suite_edit.form" />
		<property name="formView" value="testing/test_suite_list" />
		<property name="validator" ref="testSuiteListValidator"/>
	</bean>
	
	<bean id="testSuiteListValidator" class="com.prinova.messagepoint.controller.testing.TestSuiteListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.testing.TestSuiteListWrapper" />
	</bean>	

	<bean id="scenarioCustomerTestingViewController" class="com.prinova.messagepoint.testing.ScenarioCustomerTestingViewController" >
		<property name="formView" value="testing/scenario_customer_report_view" />
		<property name="successView" value="scenario_customer_report_view.form" />
		<property name="validator" ref="scenarioCustomerViewValidator"/>
	</bean>

	<bean id="scenarioCustomerInsertTestingViewController" class="com.prinova.messagepoint.testing.ScenarioCustomerInsertTestingViewController" >
		<property name="formView" value="testing/scenario_customer_insert_report_view" />
		<property name="successView" value="scenario_customer_insert_report_view.form" />
		<!--<property name="validator" ref="scenarioCustomerViewValidator"/>-->
	</bean>

	
	<bean id="scenarioCustomerSimViewController" class="com.prinova.messagepoint.controller.simulation.ScenarioCustomerSimViewController" >
		<property name="formView" value="simulations/scenario_customer_report_view" />
		<property name="successView" value="scenario_customer_report_view.form" />
		<property name="validator" ref="scenarioCustomerViewValidator"/>
	</bean>

	<bean id="scenarioMessageSubTestingViewController" class="com.prinova.messagepoint.testing.ScenarioMessageSubTestingViewController" >
		<property name="formView" value="testing/reports_message_sub" />
		<property name="successView" value="reports_message_sub.form" />
		<property name="validator" ref="scenarioCustomerViewValidator"/>
	</bean>

	<bean id="scenarioMessageSubSimViewController" class="com.prinova.messagepoint.controller.simulation.ScenarioMessageSubSimViewController" >
		<property name="formView" value="simulations/reports_message_sub" />
		<property name="successView" value="reports_message_sub.form" />
		<property name="validator" ref="scenarioCustomerViewValidator"/>
	</bean>

	<bean id="testMessageSubSubViewController" class="com.prinova.messagepoint.controller.testing.MessageSubSubViewController" >
		<property name="formView" value="testing/reports_message_sub_sub"/>
	</bean>

	<bean id="simMessageSubSubViewController" class="com.prinova.messagepoint.controller.simulation.SimMessageSubSubViewController" >
		<property name="formView" value="simulations/reports_message_sub_sub"/>
	</bean>

	<bean id="tenantValidator" class="com.prinova.messagepoint.controller.admin.TenantEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.TenantEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="name" />
        			<property name="label" value="page.label.tenant.settings.name" />
        			<property name="propertyType" ref="PersonName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="code" />
        			<property name="label" value="page.label.tenant.settings.code" />
					<property name="mandatory" value="true" />
					<property name="minLength" value="2" />
					<property name="maxLength" value="32" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.dash.underscore" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\-_]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="contactName" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="PersonName" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="contactTitle" />
        			<property name="label" value="page.label.tenant.settings.contact.title" />
        			<property name="propertyType" ref="PersonName" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="2" />
					<property name="maxLength" value="16" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="contactEmail" />
        			<property name="label" value="page.label.email" />
        			<property name="propertyType" ref="Email" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="contactPhone" />
        			<property name="label" value="page.label.phone" />
        			<property name="propertyType" ref="Phone" />
					<property name="mandatory" value="false" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="contactMobile" />
        			<property name="label" value="page.label.tenant.settings.contact.mobile" />
        			<property name="propertyType" ref="Phone" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="contactFax" />
        			<property name="label" value="page.label.tenant.settings.contact.fax" />
        			<property name="propertyType" ref="Phone" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="contactStreetAddress" />
        			<property name="label" value="page.label.tenant.settings.contact.address" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="2" />
					<property name="maxLength" value="128" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.underscore.dash.apos.dot.comma.colon.at.numbersign.square.space" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s\-_'\.,:@#\[\]]*+" />
	    		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="contactSuitUnit" />
        			<property name="label" value="page.label.tenant.settings.contact.suite" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="16" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.underscore.dash.numbersign.space" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s\-_#]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="contactCity" />
        			<property name="label" value="page.label.tenant.settings.contact.city" />
        			<property name="propertyType" ref="PersonName" />
					<property name="minLength" value="2" />
					<property name="maxLength" value="32" />
					<property name="mandatory" value="false" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="contactPostalZipCode" />
        			<property name="label" value="page.label.tenant.settings.contact.postalcode" />
        			<property name="propertyType" ref="PostalCode" />
					<property name="mandatory" value="false" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="contactWebSite" />
        			<property name="label" value="page.label.tenant.settings.contact.web" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="2" />
					<property name="maxLength" value="96" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.space.dot.dash.underscore" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\-_.\s\?=:/]*+" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="tenantActivateController" class="com.prinova.messagepoint.controller.admin.TenantActivateController">
		<property name="redirectView" value="tenant_view.jsp" />
	</bean>

	<bean id="themeValidator" class="com.prinova.messagepoint.controller.admin.UploadThemeValidator"/>
	<bean id="themeUploadController" class="com.prinova.messagepoint.controller.admin.UploadThemeController">
		<property name="formView" value="admin/theme_upload" />
		<property name="successView" value="theme_list.jsp" />
		<property name="validator" ref="themeValidator" />
	</bean>

	<bean id="switchToNodeController" class="com.prinova.messagepoint.controller.SwitchToNodeController">
		<property name="formView" value="/switch_to_node"/>
		<property name="successView" value="index.jsp"/>
	</bean>
	
	<bean id="myDashboardController" class="com.prinova.messagepoint.controller.dashboards.MyDashboardController">
		<property name="formView" value="dashboards/my_dashboard" />
		<property name="successView" value="my_dashboard.form" />
		<property name="validator" ref="myDashboardValidator" />
	</bean>
	<bean id="myDashboardMobileController" class="com.prinova.messagepoint.controller.dashboards.MyDashboardController">
		<property name="formView" value="dashboards/my_dashboard_mobile" />
		<property name="successView" value="my_dashboard_mobile.form" />
		<property name="validator" ref="myDashboardValidator" />
	</bean>
	<bean id="myDashboardValidator" class="com.prinova.messagepoint.controller.dashboards.MyDashboardValidator">
		<property name="className" value="com.prinova.messagepoint.controller.dashboards.MyDashboardWrapper" />
	</bean>

	<bean id="globalDashBoardController" class="com.prinova.messagepoint.controller.dashboards.GlobalDashboardController">
		<property name="formView" value="dashboards/global_dashboard" />
		<property name="successView" value="global_dashboard.form" />
	</bean>

	<bean id="globalDashboardDuplicatesController" class="com.prinova.messagepoint.controller.dashboards.GlobalDashboardDuplicatesController">
		<property name="successView" value="global_dashboard_duplicates.form"/>
		<property name="formView" value="dashboards/global_dashboard_duplicates"/>
	</bean>

	<bean id="globalDashboardDuplicatesDetailsController" class="com.prinova.messagepoint.controller.dashboards.GlobalDashboardDuplicatesDetailsController">
		<property name="successView" value="global_dashboard_duplicates_details.form"/>
		<property name="formView" value="dashboards/global_dashboard_duplicates_details"/>
	</bean>

	<bean id="globalDashboardSentimentController" class="com.prinova.messagepoint.controller.dashboards.GlobalDashboardSentimentController">
		<property name="successView" value="global_dashboard_sentiment.form"/>
		<property name="formView" value="dashboards/global_dashboard_sentiment"/>
	</bean>

	<bean id="globalDashboardSentimentDetailsController" class="com.prinova.messagepoint.controller.dashboards.GlobalDashboardSentimentDetailsController">
		<property name="successView" value="global_dashboard_sentiment_details.form"/>
		<property name="formView" value="dashboards/global_dashboard_sentiment_details"/>
	</bean>
	<bean id="globalDashboardTranslationController" class="com.prinova.messagepoint.controller.dashboards.GlobalDashboardTranslationController">
		<property name="successView" value="global_dashboard_translation.form"/>
		<property name="formView" value="dashboards/global_dashboard_translation"/>
	</bean>

	<bean id="globalDashboardTranslationDetailsController" class="com.prinova.messagepoint.controller.dashboards.GlobalDashboardTranslationDetailsController">
		<property name="successView" value="global_dashboard_translation_details.form"/>
		<property name="formView" value="dashboards/global_dashboard_translation_details"/>
	</bean>

	<bean id="globalDashboardReadingController" class="com.prinova.messagepoint.controller.dashboards.GlobalDashboardReadingController">
		<property name="successView" value="global_dashboard_reading.form"/>
		<property name="formView" value="dashboards/global_dashboard_reading"/>
	</bean>

	<bean id="globalDashboardReadingDetailsController" class="com.prinova.messagepoint.controller.dashboards.GlobalDashboardReadingDetailsController">
		<property name="successView" value="global_dashboard_reading_details.form"/>
		<property name="formView" value="dashboards/global_dashboard_reading_details"/>
	</bean>

	<bean id="globalDashboardNavigationWidgetController" class="com.prinova.messagepoint.controller.dashboards.GlobalDashboardNavigationWidgetController">
		<property name="successView" value="global_dashboard_navigation_widget.form" />
		<property name="formView" value="dashboards/global_dashboard_navigation_widget" />
	</bean>

	<bean id="globalDashboardBrandController" class="com.prinova.messagepoint.controller.dashboards.GlobalDashboardBrandController">
		<property name="successView" value="global_dashboard_brand.form"/>
		<property name="formView" value="dashboards/global_dashboard_brand"/>
	</bean>

	<bean id="globalDashboardBrandDetailsController" class="com.prinova.messagepoint.controller.dashboards.GlobalDashboardBrandDetailsController">
		<property name="successView" value="global_dashboard_brand_details.form"/>
		<property name="formView" value="dashboards/global_dashboard_brand_details"/>
	</bean>

	<bean id="reportDisplayController" class="com.prinova.messagepoint.controller.ReportDisplayController">
	</bean>
	<bean id="reportScenarioDisplayController" class="com.prinova.messagepoint.controller.ReportScenarioDisplayController">
	</bean>
	<bean id="csrfTokenInterceptor" class="com.prinova.messagepoint.interceptor.CSRFTokenInterceptor">
	</bean>
	<bean id="contentEditPagesInterceptor" class="com.prinova.messagepoint.interceptor.ContentEditPagesInterceptor">
	</bean>
	<bean id="requestParameterRetentionInterceptor" class="com.prinova.messagepoint.interceptor.RequestParameterRetentionInterceptor">
		<property name="retentionParameterSet">
			<set>
				<value>page</value>
			</set>
		</property>
	</bean>
    <bean id="urlMapping" class="org.springframework.web.servlet.handler.SimpleUrlHandlerMapping">
    	<property name="interceptors">
			<list>
				<ref bean="csrfTokenInterceptor" />
				<ref bean="requestParameterRetentionInterceptor" />
				<ref bean="contentEditPagesInterceptor" />
			</list>
		</property>
        <property name="mappings">
            <props>
				<!-- Objects Controllers -->
				<prop key="/entities/entity_list.form">entityListController</prop>

				<!-- Changes Controllers -->
				<prop key="/tasks/change_list.form">changeListController</prop>
				<prop key="/tasks/change_list_detail.form">changeListDetailController</prop>

				<!-- Tasks Controllers -->
				<prop key="/tasks/task_edit.form">taskEditController</prop>
				<prop key="/tasks/task_list.form">taskListController</prop>
				<prop key="/tasks/task_list_detail.form">taskListDetailController</prop>

   	          	<!-- Projects Controllers -->
   	          	<prop key="/projects/project_edit.form">projectEditController</prop>
   	          	<prop key="/projects/project_list.form">projectListController</prop>
   	          	<prop key="/projects/project_list_detail.form">projectListDetailController</prop>
   	          	<prop key="/projects/project_task_workflow_list.form">projectTaskWorkflowListController</prop>
   	          	<prop key="/projects/project_task_workflow_list_detail.form">projectTaskWorkflowListDetailController</prop>
   	          	<prop key="/projects/project_workflow_management.form">projectWorkflowManagementController</prop>

				<!-- Content Flow Controllers -->
            	<prop key="/content/content_import.form">contentImportController</prop>
            	<prop key="/content/content_history.form">contentHistoryController</prop>
            	<prop key="/content/variant_content_compare.form">variantContentCompareController</prop>

            	<!-- Embedded Content Controllers -->
            	<prop key="/content/global_content_list.form">globalContentListController</prop>
                <prop key="/content/global_content_workflow_edit.form">globalContentWorkflowEditController</prop>
                <prop key="/content/content_object_touchpoint_assignment.form">contentObjectTouchpointAssignmentEditController</prop>
				<prop key="/content/content_object_review_popup.form">contentObjectReviewPopupController</prop>
				<prop key="/content/content_object_review_popup_content.form">contentObjectReviewPopupContentController</prop>

	            	<!-- Content Library Controllers -->
            	<prop key="/content/content_object_sftp_image_upload.form">contentObjectSftpImageUploadController</prop>

                <prop key="/content/text_style_list.form">textStyleListController</prop>
                <prop key="/content/text_style_connectors_list.form">textStyleConnectorsListController</prop>
                <prop key="/content/text_style_list_detail.form">textStyleListDetailController</prop>
                <prop key="/content/paragraph_style_list.form">paragraphStyleListController</prop>
                <prop key="/content/paragraph_style_list_detail.form">paragraphStyleListDetailController</prop>
                <prop key="/content/paragraph_style_edit.form">paragraphStyleEditController</prop>
                <prop key="/content/font_list.form">fontListController</prop>
                <prop key="/content/font_upload.form">fontUploadController</prop>
                <prop key="/content/font_delete.form">fontDeleteController</prop>
                <prop key="/content/list_style_list.form">listStyleListController</prop>
                <prop key="/content/list_style_list_detail.form">listStyleListDetailController</prop>
                <prop key="/content/list_style_edit.form">listStyleEditController</prop>
                <prop key="/content/text_style_transformation_list.form">textStyleTransformationListController</prop>
                <prop key="/content/text_style_transformation_edit.form">textStyleTransformationEditController</prop>

                <!-- Editor Controllers -->
				<prop key="/content/tinymce_table_properties.form">tinymceTablePropertiesController</prop>
				<prop key="/content/tinymce_toc_properties.form">tinymceTableOfContentsPropertiesController</prop>
				<prop key="/content/tinymce_custom_list_properties.form">tinymceCustomListPropertiesController</prop>
				<prop key="/content/tinymce_custom_paragraph_styles.form">tinymceCustomParagraphStylesController</prop>
				<prop key="/content/tinymce_connected_custom_list_properties.form">tinymceConnectedCustomListPropertiesController</prop>
				<prop key="/content/tinymce_connected_document_history.form">tinymceConnectedDocumentHistoryController</prop>
				<prop key="/content/tinymce_connected_custom_paragraph_styles.form">tinymceConnectedCustomParagraphStylesController</prop>
                <prop key="/content/tinymce_text_field_properties.form">tinymceTextFieldPropertiesController</prop>
                <prop key="/content/tinymce_menu_properties.form">tinymceMenuPropertiesController</prop>
                <prop key="/content/tinymce_checkbox_radio_properties.form">tinymceCheckboxRadioPropertiesController</prop>
                <prop key="/content/tinymce_submit_button_properties.form">tinymceSubmitButtonPropertiesController</prop>
               	<prop key="/content/tinymce_image_upload.form"> tinymceImageUploadController</prop>
				<prop key="/content/tinymce_image_properties.form"> tinymceImagePropertiesController</prop>
				<prop key="/content/tinymce_connected_image_properties.form"> tinymceConnectedImagePropertiesController</prop>
				<prop key="/content/tinymce_content_menu.form">tinymceContentMenuController</prop>
               	<prop key="/content/tinymce_links.form">tinymceLinksController</prop>
               	<prop key="/content/tinymce_paragraph_properties.form">tinymceParagraphPropertiesController</prop>
               	<prop key="/content/tinymce_barcodes.form">tinymceBarcodesController</prop>
				<prop key="/content/tinymce_mark_editable_properties.form">tinymceMarkEditablePropertiesController</prop>

               	<!-- Content Intelligence -->
               	<prop key="/content/content_compare.form">contentCompareController</prop>
				<prop key="/content/brand_check.form">brandCheckController</prop>


				<!-- Rationalizer -->
				<prop key="/rationalizer/rationalizer_documents_list.form">rationalizerDocumentsListController</prop>
				<prop key="/rationalizer/rationalizer_bulk_document_upload.form">rationalizerBulkDocumentUploadController</prop>
				<prop key="/rationalizer/rationalizer_touchpoint_upload.form">rationalizerTouchpointUploadController</prop>
				<prop key="/rationalizer/rationalizer_export_to_messagepoint.form">rationalizerExportToMessagepointController</prop>
				<prop key="/rationalizer/rationalizer_content_list.form">rationalizerContentListController</prop>
				<prop key="/rationalizer/rationalizer_content_list_detail.form">rationalizerContentListDetailController</prop>
				<prop key="/rationalizer/rationalizer_document_edit.form">rationalizerDocumentEditController</prop>
				<prop key="/rationalizer/rationalizer_metadata_edit.form">rationalizerMetadataEditController</prop>
				<prop key="/rationalizer/rationalizer_dc_fields.form">rationalizerDCFieldsController</prop>
				<prop key="/rationalizer/rationalizer_query_list.form">rationalizerQueryListController</prop>
				<prop key="/rationalizer/rationalizer_query_edit.form">rationalizerQueryEditController</prop>
				<prop key="/rationalizer/rationalizer_query_edit_detail.form">rationalizerQueryEditDetailController</prop>
				<prop key="/rationalizer/rationalizer_application_visibility_edit.form">rationalizerApplicationVisibilityController</prop>
				<prop key="/rationalizer/rationalizer_application_navigation_edit.form">rationalizerApplicationNavigationController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_compare.form">rationalizerDashboardCompareController</prop>
				<prop key="/rationalizer/rationalizer_navigation_widget.form">rationalizerNavigationWidgetController</prop>
				<prop key="/rationalizer/rationalizer_checkbox_navigation_widget.form">rationalizerCheckboxNavTreeWidgetController</prop>
				<prop key="/rationalizer/rationalizer_workflow_assignment_edit.form">rationalizerWorkflowAssignmentEditController</prop>
				<prop key="/rationalizer/rationalizer_content_history.form">rationalizerContentHistoryController</prop>
				<prop key="/rationalizer/rationalizer_metadata_history.form">rationalizerMetadataHistoryController</prop>
				<prop key="/rationalizer/rationalizer_dashboard.form">rationalizerDashboardController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_duplicates.form">rationalizerDashboardDuplicatesController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_similarities.form">rationalizerDashboardSimilaritiesController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_metadata.form">rationalizerDashboardMetadataController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_brand.form">rationalizerDashboardBrandController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_reading.form">rationalizerDashboardReadingController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_sentiment.form">rationalizerDashboardSentimentController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_duplicates_details.form">rationalizerDashboardDuplicatesDetailsController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_similarities_details.form">rationalizerDashboardSimilaritiesDetailsController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_metadata_details.form">rationalizerDashboardMetadataDetailsController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_brand_details.form">rationalizerDashboardBrandDetailsController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_sentiment_details.form">rationalizerDashboardSentimentDetailsController</prop>
				<prop key="/rationalizer/rationalizer_dashboard_reading_details.form">rationalizerDashboardReadingDetailsController</prop>
				<prop key="/rationalizer/rationalizer_shared_content_list.form">rationalizerSharedContentListController</prop>
				<prop key="/rationalizer/rationalizer_shared_content_list_detail.form">rationalizerSharedContentListDetailController</prop>
				<prop key="/rationalizer/rationalizer_shared_content_edit.form">rationalizerSharedContentEditController</prop>
				<prop key="/rationalizer/rationalizer_consolidate.form">rationalizerConsolidateController</prop>
				<prop key="/rationalizer/rationalizer_consolidate_edit.form">rationalizerConsolidateEditController</prop>
				<prop key="/rationalizer/rationalizer_task_list.form">taskListController</prop>
				<prop key="/rationalizer/rationalizer_application_setup.form">rationalizerApplicationSetupController</prop>

	            <!-- Message Flow Controllers -->
				<prop key="/content/content_object_edit.form">contentObjectDetailsEditController</prop>
				<prop key="/content/content_object_edit_content.form">contentObjectContentEditController</prop>
                <prop key="/content/content_object_edit_content_dynamic_variant.form">contentObjectDynamicVariantEditController</prop>
                <prop key="/content/content_object_edit_targeting.form">contentObjectTargetingEditController</prop>
				<prop key="/content/content_object_view.form">contentObjectViewController</prop>
				<prop key="/content/content_object_view_content.form">contentObjectContentViewController</prop>
                <prop key="/content/content_object_view_content_dynamic_variant.form">contentObjectDynamicVariantViewController</prop>
				<prop key="/content/content_object_view_targeting.form">contentObjectTargetingViewController</prop>

				<!-- Dynamic Variant Tree Controllers -->
				<prop key="/content/dynamic_variant_popup_content.form">dynamicVariantPopupContentController</prop>

				<!-- Index pages are redirected to this controller to retrieve and view the default page  -->
				<prop key="/tasks/redirect.form">defaultPageController</prop>
				<prop key="/content/redirect.form">defaultPageController</prop>
				<prop key="/message/redirect.form">defaultPageController</prop>
				<prop key="/insert/redirect.form">defaultPageController</prop>
				<prop key="/reports/redirect.form">defaultPageController</prop>
				<prop key="/admin/redirect.form">defaultPageController</prop>
				<prop key="/testing/redirect.form">defaultPageController</prop>
				<prop key="/tpadmin/redirect.form">defaultPageController</prop>
				<prop key="/dataadmin/redirect.form">defaultPageController</prop>
				<prop key="/simulations/redirect.form">defaultPageController</prop>
				<prop key="/target/redirect.form">defaultPageController</prop>

				<!-- Insert Controllers -->
            	<prop key="/insert/insert_list.form">insertListController</prop>
            	<prop key="/insert/insert_list_detail.form">insertListDetailController</prop>
            	<prop key="/insert/insert_overview_edit.form">insertOverviewEditController</prop>
            	<prop key="/insert/insert_targeting_view.form">insertTargetingViewController</prop>
            	<prop key="/insert/insert_targeting_edit.form">insertTargetingEditController</prop>
            	<prop key="/insert/insert_content_edit.form">insertContentEditController</prop>
            	<prop key="/insert/insert_view.form">insertViewController</prop>

				<!-- Insert Schedule Controllers -->
            	<prop key="/insert/insert_schedule_list.form">insertScheduleListController</prop>
            	<prop key="/insert/insert_schedule_list_detail.form">insertScheduleListDetailController</prop>
            	<prop key="/insert/insert_schedule_overview_edit.form">insertScheduleOverviewEditController</prop>
            	<prop key="/insert/insert_schedule_rate_schedule_edit.form">insertScheduleRateScheduleEditController</prop>
            	<prop key="/insert/insert_schedule_selector_edit.form">insertScheduleSelectorEditController</prop>
            	<prop key="/insert/insert_schedule_bin_assignment_edit.form">insertScheduleBinAssignmentEditController</prop>
            	<prop key="/insert/insert_schedule_insert_timing_edit.form">insertScheduleInsertTimingEditController</prop>
            	<prop key="/insert/insert_schedule_view.form">insertScheduleViewController</prop>

				<!-- Rate Sheet Controllers -->
            	<prop key="/insert/rate_schedule_list.form">rateScheduleListController</prop>
            	<prop key="/insert/rate_schedule_list_detail.form">rateScheduleListDetailController</prop>
            	<prop key="/insert/rate_schedule_view.form">rateScheduleViewController</prop>
            	<prop key="/insert/rate_schedule_edit.form">rateScheduleEditController</prop>

            	<!-- Reports Controllers -->
            	<prop key="/reports/reports_list.form">reportsListController</prop>
            	<prop key="/reports/scenario_view.form">reportScenarioViewController</prop>
            	<prop key="/reports/scenario_edit.form">reportScenarioEditController</prop>
            	<prop key="/reports/scenario_operations_view.form">reportScenarioOperationsViewController</prop>
            	<prop key="/reports/scenario_operations_edit.form">reportScenarioOperationsEditController</prop>
				<prop key="/reports/scenario_tp_delivery_view.form">reportScenarioTpDeliveryViewController</prop>
            	<prop key="/reports/scenario_tp_delivery_edit.form">reportScenarioTpDeliveryEditController</prop>
            	<prop key="/reports/deleteScenario.form">reportScenarioDeleteController</prop>

            	<!-- Simulation Controllers -->
            	<prop key="/simulations/simulations_list.form">simulationsListController</prop>
            	<prop key="/simulations/simulation_coverage_report.form">covaregeReportViewController</prop>
            	<prop key="/simulations/simulation_message_coverage_report.form">messageCovaregeReportViewController</prop>
            	<prop key="/simulation/simulation_lab.form">messageSimulationController</prop>
            	<prop key="/simulations/simulation_view_inclusions.form">simulationMessageInclusionViewController</prop>
            	<prop key="/simulations/simulation_edit.form">simulationEditController</prop>
				<prop key="/simulations/scenario_customer_report_view.form">scenarioCustomerSimViewController</prop>
				<prop key="/simulations/reports_message_sub.form">scenarioMessageSubSimViewController</prop>
				<prop key="/simulations/reports_message_sub_sub.form">simMessageSubSubViewController</prop>
				<prop key="/simulations/scenario_message_report_view.form">simScenarioReportMessageController</prop>

            	<!-- Touchpoint admin Controllers -->
            	<prop key="/tpadmin/tpcontainer_import_confirmation.form">appImportConfirmationController</prop>
            	<prop key="/tpadmin/touchpoint_import.form">touchpointImportController</prop>
            	<prop key="/tpadmin/touchpoint_import_confirm.form">touchpointImportConfirmationController</prop>
				<prop key="/tpadmin/content_json_import_confirm.form">contentJsonImportConfirmationController</prop>
            	<prop key="/content/bulk_upload_confirm.form">bulkUploadConfirmationController</prop>
            	<prop key="/tpadmin/touchpoint_language_edit.form">touchpointLanguageEditController</prop>
            	<prop key="/tpadmin/deliveryevent_edit.form">deliveryEventEditController</prop>
            	<prop key="/tpadmin/deliveryevent_delete.form">productionEventDeleteController</prop>
            	<prop key="/tpadmin/document_edit.form">documentEditController</prop>
            	<prop key="/tpadmin/document_view.form">documentViewController</prop>
            	<prop key="/tpadmin/create_touchpoint.form">createTouchpointController</prop>
            	<prop key="/tpadmin/composition_files_upload.form">compositionFilesUploadController</prop>
            	<prop key="/tpadmin/zone_list.form">zoneListController</prop>
            	<prop key="/tpadmin/zone_list_detail.form">zoneListDetailController</prop>
            	<prop key="/tpadmin/touchpoint_layout_manager.form">touchpointLayoutManagerController</prop>
            	<prop key="/tpadmin/zone_visibility_edit.form">zoneVisibilityEditController</prop>
            	<prop key="/tpadmin/zone_text_styles_edit.form">zoneTextStylesEditController</prop>
            	<prop key="/tpadmin/zone_para_styles_edit.form">zoneParaStylesEditController</prop>
            	<prop key="/tpadmin/zone_list_styles_edit.form">zoneListStylesEditController</prop>
            	<prop key="/tpadmin/touchpoint_zone_edit.form">touchpointZoneEditController</prop>
            	<prop key="/tpadmin/touchpoint_zone_data_group_bulk_edit.form">zoneDataGroupBulkEditController</prop>
            	<prop key="/tpadmin/document_extended_reporting_variables_edit.form">documentExtendedReportingVariablesController</prop>
            	<prop key="/tpadmin/document_view_extended_reporting_variables.form">documentViewExtendedReportingVariablesIFrame</prop>
            	<prop key="/tpadmin/touchpoint_style_customizations.form">touchpointStyleCustomizationsController</prop>
            	<prop key="/tpadmin/text_style_customization.form">textStyleCustomizationController</prop>
            	<prop key="/tpadmin/para_style_customization.form">paragraphStyleEditController</prop>
            	<prop key="/tpadmin/list_style_customization.form">listStyleEditController</prop>
            	<prop key="/tpadmin/touchpoint_health_check.form">touchpointHealthCheckController</prop>
            	<prop key="/tpadmin/touchpoint_health_check_detail.form">touchpointHealthCheckDetailController</prop>
				<prop key="/tpadmin/stripo_editor.form">stripoEditorController</prop>

				<prop key="/stats/instance_statistics.form">instanceStatisticsController</prop>
				<prop key="/stats/stats_tool.form">statsToolController</prop>
				<prop key="/content/content_power_edit.form">contentPowerEditController</prop>
				<prop key="/dataadmin/metatags_edit.form">metatagsEditController</prop>

				<prop key="/contentintelligence/content_assistant_edit.form">contentAssistantEditController</prop>
				<prop key="/contentintelligence/content_assistant_list.form">contentAssistantListController</prop>

            	<!-- Touchpoint: Attachment Controllers -->
            	<prop key="/attachment/attachment_overview_edit.form">attachmentOverviewEditController</prop>
            	<prop key="/attachment/attachment_targeting_edit.form">attachmentTargetingEditController</prop>

            	<!-- Touchpoint: email Template Controllers -->
            	<prop key="/tpadmin/email_template_preview.form">emailTemplatePreviewController</prop>

            	<!-- Touchpoint: Touchpoint Targeting Controller -->
            	<prop key="/tpadmin/touchpoint_targeting_edit.form">touchpointTargetingEditController</prop>

            	<!-- Targeting: Content Targeting Controller -->
            	<prop key="/content/content_targeting_edit.form">contentTargetingEditController</prop>

            	<!-- Touchpoint: Selections Controllers -->
            	<prop key="/tpadmin/touchpoint_selections_proofing_data_edit.form">documentProofingDataEditController</prop>
            	<prop key="/tpadmin/selection_proofing_data_edit.form">selectionProofingDataEditController</prop>
            	<prop key="/tpadmin/alternate_layout_assignment_edit.form">alternateLayoutAssignmentEditController</prop>

				<prop key="/tpadmin/touchpoint_email_proof_view.form">touchpointEmailProofViewController</prop>

            	<prop key="/tpadmin/variant_workflow_assignment_edit.form">variantWorkflowAssignmentEditController</prop>
            	<prop key="/tpadmin/message_workflow_assignment_edit.form">messageWorkflowAssignmentEditController</prop>

            	<prop key="/tpadmin/touchpoint_channel_configuration_edit.form">documentChannelEditController</prop>
            	<prop key="tpadmin/touchpoint_data_source_configuration_edit.form">touchpointDataSourceAssociationEditController</prop>
            	<prop key="tpadmin/touchpoint_sync_project.form">touchpointSyncProjectController</prop>
				<prop key="tpadmin/touchpoint_sync_list.form">touchpointSyncListController</prop>
				<prop key="tpadmin/compare_content_section.form">CompareContentSectionController</prop>
				<prop key="tpadmin/touchpoint_sync_message_priority.form">touchpointSyncMessagePriorityController</prop>
            	<prop key="tpadmin/touchpoint_sync_content_compare.form">touchpointSyncContentCompareController</prop>
                <prop key="tpadmin/touchpoint_sync_differences_list.form">touchpointSyncDifferencesListController</prop>
            	<prop key="tpadmin/touchpoint_template_modifier_list.form">touchpointTemplateModifierController</prop>
            	<prop key="tpadmin/template_variants_edit.form">templateVariantsEditController</prop>
            	<prop key="/tpadmin/touchpoint_selection_selectors_edit.form">touchpointSelectionSelectorsEditController</prop>
            	<prop key="/tpadmin/touchpoint_content_selection_view.form">touchpointContentSelectionViewController</prop>
            	<prop key="/tpadmin/touchpoint_content_selection_edit.form">touchpointContentSelectionEditController</prop>
            	<!-- Touchpoint: Language Selections Controllers -->
            	<prop key="/tpadmin/language_selections_list.form">languageSelectionsListController</prop>
            	<prop key="/tpadmin/language_selection_selectors_edit.form">languageSelectionSelectorsEditController</prop>
				<prop key="/tpadmin/formatting_selections_list.form">formattingSelectionsListController</prop>

            	<!-- Touchpoint Exchange -->
            	<prop key="/tpadmin/touchpoint_exchange_list.form">touchpointExchangeListController</prop>

				<prop key="/tpadmin/delivery_event_list.form">deliveryEventListController</prop>

				<!-- Touchpoint: Tag Controllers -->
				<prop key="/tpadmin/tags_list.form">tagsListController</prop>
            	<prop key="/tpadmin/tag_overview_view.form">tagOverviewViewController</prop>
            	<prop key="/tpadmin/tag_overview_edit.form">tagOverviewEditController</prop>
            	<prop key="/tpadmin/tag_associations_edit.form">tagAssociationsEditController</prop>
            	<prop key="/tpadmin/tag_targeting_edit.form">tagTargetingEditController</prop>
            	<prop key="/tpadmin/tp_access_control.form">tpAccessControlController</prop>

				<prop key="/touchpoints/touchpoint_content_object_list.form">tpContentObjectListController</prop>
				<prop key="/touchpoints/touchpoint_content_object_list_detail.form">tpContentObjectListDetailController</prop>

            	<prop key="/touchpoints/local_content_list.form">tpContentListController</prop>

            	<prop key="/touchpoints/touchpoint_collection_list.form">tpCollectionListController</prop>
            	<prop key="/touchpoints/touchpoint_collection_list_detail.form">tpCollectionListDetailController</prop>
            	<prop key="/touchpoints/touchpoint_collection_edit.form">tpCollectionEditController</prop>
            	<prop key="/touchpoints/touchpoint_variant_list.form">tpVariantListController</prop>
            	<prop key="/touchpoints/touchpoint_variant_list_detail.form">tpVariantListDetailController</prop>
            	<prop key="/touchpoints/touchpoint_selections_content_list_detail.form">tpVariantContentListDetailController</prop>
            	<prop key="/touchpoints/touchpoint_widget.form">touchpointWidgetController</prop>
            	<prop key="/touchpoints/touchpoint_variant_visibility_edit.form">tpVariantVisibilityController</prop>
            	<prop key="touchpoints/touchpoint_variant_template_modifiers_edit.form">tpVariantTemplateModifiersEditController</prop>
            	<prop key="touchpoints/touchpoint_variant_template_edit.form">tpVariantTemplateEditController</prop>
            	<prop key="/touchpoints/touchpoint_widget_full.form">touchpointWidgetFullController</prop>
            	<prop key="/touchpoints/touchpoint_content_object_preview.form">tpContentObjectPreviewController</prop>
            	<prop key="/touchpoints/touchpoint_content_object_move_to_zone.form">tpContentObjectMoveToZoneController</prop>
            	<prop key="/touchpoints/selection_data_view.form">selectionDataViewController</prop>
            	<prop key="/content/content_object_zone_priority_edit.form">contentObjectZonePriorityEditController</prop>

				<!-- Connected -->
				<prop key="/connected/touchpoint_communications_proto.form">touchpointCommunicationsProtoController</prop>
				<prop key="/connected/connected_touchpoint_rendering_manager.form">connectedTouchpointRenderingManagerController</prop>
				<prop key="/connected/connected_touchpoint_rendering_manager_iframe.form">connectedTouchpointRenderingManagerIframeController</prop>
				<prop key="asyncConnectedTouchpointRenderingManager.form">asyncConnectedTouchpointRenderingManagerController</prop>
				<prop key="/communication/connected_touchpoint_interview_setup.form">connectedTouchpointInterviewSetupController</prop>
				<prop key="/communication/connected/asyncConnectedTouchpointInterview.form">asyncConnectedTouchpointInterview</prop>
				<prop key="/communication/connected/asyncCommunicationOrder.form">asyncCommunicationOrderEntry</prop>

				<prop key="/touchpoints/touchpoint_communications_list.form">touchpointCommunicationsListController</prop>
				<prop key="/touchpoints/touchpoint_communications_list_detail.form">touchpointCommunicationsListDetailController</prop>
            	<prop key="/communication/communication_content_edit.form">communicationContentEditController</prop>
            	<prop key="/communication/communication_order_entry_edit.form">communicationOrderEntryEditController</prop>
				<prop key="/communication/communication_order_entry.form">communicationOrderEntryController</prop>
            	<prop key="/communication/communication_order_entry_setup.form">communicationOrderEntrySetupController</prop>
            	<prop key="/communication/connected_workflow_assignment_edit.form">connectedWorkflowAssignmentEditController</prop>
            	<prop key="/communication_external_validation.form">communicationExternalValidationController</prop>
            	<prop key="/connected_portal_gateway.form">communicationPortalGatewayController</prop>
            	<prop key="/testing/test_center_communications_list.form">testCenterCommunicationsListController</prop>

            	<prop key="/dataadmin/bridge_variables.form">bridgeDataElementVariablesController</prop>

				<prop key="/dataadmin/data_source_association_edit.form">dataSourceAssociationEditController</prop>
            	<prop key="/dataadmin/variable_list.form">variableListController</prop>
            	<prop key="/dataadmin/variable_list_detail.form">variableListDetailController</prop>
            	<prop key="/dataadmin/variable_edit.form">variableEditController</prop>
            	<prop key="/dataadmin/variable_view.form">variableViewController</prop>
            	<prop key="/dataadmin/variable_delete.form">variableDeleteController</prop>
            	<prop key="/dataadmin/condition_element_edit.form">conditionElementEditController</prop>
            	<prop key="/dataadmin/condition_element_delete.form">conditionElementDeleteController</prop>
            	<prop key="/dataadmin/target_group_delete.form">targetGroupDeleteController</prop>
            	<prop key="/dataadmin/target_group_edit.form">targetGroupController</prop>
            	<prop key="/dataadmin/conditionelements.form">targetingRuleListController</prop>
            	<prop key="/dataadmin/conditionelements_detail.form">targetingRuleListDetailController</prop>
            	<prop key="/dataadmin/targetgroups.form">targetGroupListController</prop>
            	<prop key="/dataadmin/targetgroups_detail.form">targetGroupListDetailController</prop>
            	<prop key="/dataadmin/target_group_touchpoint_assignment.form">targetGroupTouchpointAssignmentEditController</prop>
            	<prop key="/dataadmin/data_group_edit.form">dataGroupEditController</prop>
            	<prop key="/dataadmin/data_group_delete.form">dataGroupDeleteController</prop>
            	<prop key="/dataadmin/data_source_edit.form">dataSourceEditController</prop>
            	<prop key="/dataadmin/data_source_delete.form">dataSourceDeleteController</prop>
            	<prop key="/dataadmin/data_record_edit.form">dataRecordEditController</prop>
            	<prop key="/dataadmin/data_record_delete.form">dataRecordDeleteController</prop>
				<prop key="/dataadmin/data_group_hierarchy_view.form">dataGroupHierarchyViewController</prop>
            	<prop key="/dataadmin/data_element_edit.form">dataElementEditController</prop>
            	<prop key="/dataadmin/data_element_delete.form">dataElementDeleteController</prop>
            	<prop key="/dataadmin/data_files.form">dataFileListController</prop>
            	<prop key="/dataadmin/data_file_list_detail.form">dataFileListDetailController</prop>
            	<prop key="/dataadmin/data_resources.form">dataResourceListController</prop>
            	<prop key="/dataadmin/data_resource_list_detail.form">dataResourceListDetailController</prop>
            	<prop key="/dataadmin/lookup_table_list.form">lookupTableListController</prop>
            	<prop key="/dataadmin/lookup_table_list_detail.form">lookupTableListDetailController</prop>
            	<prop key="/dataadmin/lookup_table_edit.form">lookupTableEditController</prop>
            	<prop key="/dataadmin/lookup_table_workflow_edit.form">lookupTableWorkflowEditController</prop>
            	<prop key="/dataadmin/lookup_table_source_editor.form">lookupTableSourceEditorController</prop>

            	<!-- Metadata -->
            	<prop key="/metadata/metadata_form_definition_list.form">metadataFormDefinitionListController</prop>
            	<prop key="/metadata/metadata_form_definition_list_detail.form">metadataFormDefinitionListDetailController</prop>
            	<prop key="/metadata/metadata_form_definition_edit.form">metadataFormDefinitionEditController</prop>
            	<prop key="/metadata/touchpoint_metadata_form_edit.form">touchpointMetadataFormEditController</prop>
            	<prop key="/metadata/touchpoint_selection_metadata_form_edit.form">touchpointSelectionMetadataFormEditController</prop>
            	<prop key="/metadata/metadata_points_of_interest_form.form">metadataPointsOfInterestController</prop>

				<!-- xmllayout mappings -->
				<prop key="/dataadmin/xml_data_tag_upload_edit.form">xmlDataTagUploadEditController</prop>
				<prop key="/dataadmin/xml_data_tag_definition_edit.form">xmlDataTagDefinitionEditController</prop>
            	<prop key="/dataadmin/xml_data_tag_definition_delete.form">xmlDataTagDefinitionDeleteController</prop>
            	<prop key="/dataadmin/xml_data_element_edit.form">xmlDataElementEditController</prop>
            	<prop key="/dataadmin/xml_data_element_delete.form">xmlDataElementDeleteController</prop>

				<!-- jsonloyout mappings -->
				<prop key="/dataadmin/json_data_definition_edit.form">jsonDataDefinitionEditController</prop>
				<prop key="/dataadmin/json_data_definition_delete.form">jsonDataDefinitionDeleteController</prop>
				<prop key="/dataadmin/json_data_key_edit.form">jsonDataKeyEditController</prop>
				<prop key="/dataadmin/json_data_element_edit.form">jsonDataElementEditController</prop>
				<prop key="/dataadmin/json_data_element_delete.form">jsonDataElementDeleteController</prop>

            	<prop key="/admin/domains_upload_edit.form">domainsUploadEditController</prop>

				<!-- Admin mappings -->
				<prop key="/admin/systemproperty_view.form">systemPropertyViewController</prop>
            	<prop key="/admin/systemproperty_edit.form">systemPropertyController</prop>
            	<prop key="/admin/systemproperty_prinova_edit.form">systemPropertyPrinovaController</prop>
            	<prop key="/admin/pod_list.form">podListController</prop>
            	<prop key="/admin/pod_list_detail.form">podListDetailController</prop>
            	<prop key="/admin/pod_edit.form">podEditController</prop>
            	<prop key="/admin/pod_view.form">podViewController</prop>
            	<prop key="/admin/map_domains.form">mapDomainsController</prop>
            	<prop key="/admin/branch_list.form">branchListController</prop>
            	<prop key="/admin/branch_list_detail.form">branchListDetailController</prop>
            	<prop key="/admin/branch_edit.form">branchEditController</prop>
            	<prop key="/admin/node_view.form">nodeViewController</prop>
            	<prop key="/admin/node_edit.form">nodeEditController</prop>
				<prop key="/edit_userid.form">editUserIdController</prop>
				<prop key="/edit_password.form">editPasswordController</prop>
				<prop key="/edit_userid_password.form">editUserIdAndPasswordController</prop>

            	<prop key="/admin/password_security_edit.form">securitySettingsController</prop>
            	<prop key="/admin/password_security_view.form">securitySettingsViewController</prop>
				<prop key="/admin/language_settings_edit.form">updateLanguageSettingsController</prop>
				<prop key="/admin/locale_settings_edit.form">updateLocaleSettingsController</prop>
				<prop key="/admin/tenant_permissions_edit.form">tenantPermissionEditController</prop>
				<prop key="/admin/tenant_permissions_view.form">tenantPermissionViewController</prop>

				<!--  new services list, view controllers -->
				<prop key="/admin/services_list.form">servicesListController</prop>
				<prop key="/admin/services_view.form">servicesViewController</prop>
				<prop key="/admin/services_edit.form">servicesEditController</prop>
				<prop key="/admin/create_service.form">servicesEditController</prop>

 				<!--  new user list, view controllers -->
				<prop key="/admin/user_list.form">userListController</prop>
				<prop key="/admin/user_view.form">userViewController</prop>
				<prop key="/admin/user_edit.form">userEditController</prop>
				<prop key="/admin/user_access_control.form">userAccessControlController</prop>
				<prop key="/admin/create_user.form">userEditController</prop>
				<prop key="/admin/role_list.form">roleListController</prop>
				<prop key="/admin/role_view.form">roleViewController</prop>
				<prop key="/admin/role_edit.form">roleEditController</prop>
				<prop key="/admin/user_delete_detail.form">userDeleteDetailController</prop>

				<prop key="/admin/workgroup_associations_view.form">workgroupZoneAssociationsViewController</prop>
				<prop key="/admin/workgroup_associations_edit.form">workgroupZoneAssociationsEditController</prop>

				<prop key="/admin/maintenance_view.form">maintenanceViewController</prop>
            	<prop key="/admin/maintenance_edit.form">maintenanceEditController</prop>

				<prop key="/admin/workgroup_view.form">workgroupViewController</prop>
				<prop key="/admin/workgroup_edit.form">workgroupEditController</prop>
				<prop key="/admin/workgroup_delete.form">workgroupDeleteController</prop>

				<prop key="/admin/deserver_list.form">deServerListController</prop>
            	<prop key="/admin/deserver_list_detail.form">deServerListDetailController</prop>
            	<prop key="/admin/deserver_edit.form">deServerEditController</prop>
            	<prop key="/admin/deserver_view.form">deServerViewController</prop>

<!--			genericEmptyController is empty controller which does nothing, -->
<!--			required only for this configuration to allow JSP page to be rendered. -->
				<prop key="/admin/rest_api_token_list.form">genericEmptyController</prop>
				<prop key="/admin/signing_key_pair_list.form">genericEmptyController</prop>

				<prop key="/admin/dev_test.form">developmentTestController</prop>

				<prop key="/dataadmin/parameter_list.form">parameterListController</prop>
				<prop key="/dataadmin/parameter_edit.form">parameterEditController</prop>
				<prop key="/dataadmin/parameter_delete.form">parameterDeleteController</prop>

				<prop key="/dataadmin/parameter_group_edit.form">parameterGroupEditController</prop>
				<prop key="/dataadmin/parameter_group_list.form">parameterGroupListController</prop>
				<prop key="/dataadmin/parameter_group_delete.form">parameterGroupDeleteController</prop>

				<prop key="/dataadmin/external_event_list.form">externalEventListController</prop>

   	          	<prop key="/dataadmin/data_files_edit.form">testDataFileController</prop>
   	          	<prop key="/dataadmin/delete_data_file.form">dataFileDeleteController</prop>
   	          	<prop key="/dataadmin/datasources.form">dataSourcesController</prop>
   	          	<prop key="/dataadmin/data_resource_edit.form">dataResourceEditController</prop>
				<prop key="/dataadmin/data_resource_delete.form">dataResourceDeleteController</prop>

            	<prop key="/admin/tenant_activate.form">tenantActivateController</prop>
            	<prop key="/admin/theme_upload.form">themeUploadController</prop>
            	<prop key="/admin/user_interface_edit.form">themeEditController</prop>
            	<prop key="/admin/licence_management.form">licenceManagementController</prop>
            	<prop key="/admin/node_licence_management.form">nodeLicenceManagementController</prop>
            	<prop key="/admin/branch_licence_management.form">branchLicenceManagementController</prop>
            	<prop key="/admin/licence_list.form">licenceListController</prop>
            	<prop key="/admin/licence_edit.form">licenceEditController</prop>
            	<prop key="/admin/auditing_list.form">auditingListController</prop>
            	<prop key="/admin/notification_settings_edit.form">notificationSettingsEditController</prop>
				<prop key="/admin/fileroot_management_edit.form">filerootManagementEditController</prop>
				<prop key="/admin/marcie_diagnostic.form">marcieDiagnosticController</prop>
				<prop key="/admin/data_expiration_edit.form">dataExpirationEditController</prop>

            		<!-- Dictionary -->
            		<prop key="/dictionary/dictionary_edit.form">dictionaryEditController</prop>
            		<prop key="/dictionary/dictionary_list.form">dictionaryListController</prop>
            		<prop key="/dictionary/dictionary_list_detail.form">dictionaryListDetailController</prop>
            		<prop key="/dictionary/dictionary_list_detail.form">dictionaryListDetailController</prop>
            		<prop key="/dictionary/dictionary_source_editor.form">dictionarySourceEditorController</prop>

				<!-- Touchpoint Dictionaries -->
				<prop key="/admin/touchpoint_dictionaries_edit.form">touchpointDictionariesEditController</prop>

				<!-- Testing Controllers -->
				<prop key="/testing/testing_list.form">testingListController</prop>
   	      		<prop key="/testing/scenario_edit.form">testScenarioEditController</prop>
   	      		<prop key="/testing/scenario_view.form">testScenarioViewController</prop>
   	      		<prop key="/testing/test_suite_list.form">testSuiteListController</prop>
   	      		<prop key="/testing/test_suite_edit.form">testSuiteEditController</prop>
   	      		<prop key="/testing/test_suite_view.form">testSuiteViewController</prop>
            		<prop key="/testing/deleteScenario.form">testScenarioDeleteController</prop>
				<prop key="/testing/scenario_message_report_view.form">scenarioReportMessageController</prop>
				<prop key="/testing/scenario_insert_report_view.form">scenarioReportInsertController</prop>
				<prop key="/testing/reports_insert_sub.form">scenarioInsertSubTestingViewController</prop>

				<!-- PDF data stream conroller -->
				<prop key="/download/results.form">binaryStreamController</prop>
				<prop key="/download/pdf.form">binaryStreamController</prop>
				<prop key="/download/data.form">binaryStreamController</prop>
				<prop key="/download/font.form">binaryStreamController</prop>
				<prop key="/download/xml.form">binaryStreamController</prop>
				<prop key="/download/excel.form">binaryStreamController</prop>
				<prop key="/download/image.form">binaryStreamController</prop>
				<prop key="/download/log.form">binaryStreamController</prop>
				<prop key="/download/zip.form">binaryStreamController</prop>
				<prop key="/download/downloadWebContent.form">binaryWebStreamController</prop>
				<prop key="/download/metadata.form">binaryWebStreamController</prop>

				<!-- Async Controllers -->
				<prop key="getChatMetadata.form">asyncChatMetadataController</prop>
				<prop key="getChildren.form">mainTreeController</prop>
				<prop key="getContent.form">asyncContentController</prop>
				<prop key="getContentPreview.form">asyncContentPreviewController</prop>
				<prop key="getSelectionTree.form">asyncSelectionTreeController</prop>
				<prop key="getObjectInfo.form">asyncObjectInfoController</prop>
				<prop key="getSimulationExportContent.form">asyncSimulationExportContentController</prop>
				<prop key="getDataForSelectMenu.form">asyncDataForSelectMenuController</prop>
				<prop key="getDataForMultiselectMenu.form">asyncDataForMultiselectMenuController</prop>
				<prop key="getSpellcheckData.form">asyncSpellcheckController</prop>
				<prop key="getItemStatus.form">asyncStatusPollingController</prop>
				<prop key="uploadedImageEvent.form">uploadedImageEventController</prop>
				<prop key="getCollectionDataValues.form">asyncCollectionDataValuesController</prop>
                <prop key="getCollectionDataValuesTreeNodes.form">asyncCollectionDataValuesTreeNodesController</prop>
				<prop key="getSectionImageThumbnail.form">asyncSectionImageThumbnailController</prop>
				<prop key="getInsertImageThumbnail.form">asyncInsertImageThumbnailController</prop>
				<prop key="getInsertScheduleExportContent.form">asyncInsertScheduleExportController</prop>
				<prop key="getTargetGroup.form">asyncTargetingController</prop>
				<prop key="getReportResult.form">asyncReportResultPollingController</prop>
				<prop key="getExportResult.form">asyncExportResultPollingController</prop>
				<prop key="getListTable.form">asyncListTableController</prop>
				<prop key="getWidget.form">asyncWidgetsController</prop>
				<prop key="getCategoryMenuItems.form">asyncCategoryMenuItemsController</prop>
				<prop key="getImportResult.form">touchpointImportConfirmationController</prop>
				<prop key="getJsonContentImportResult.form">contentJsonImportConfirmationController</prop>
				<prop key="getBulkUploadResult.form">bulkUploadConfirmationController</prop>
				<prop key="getContentObject.form">asyncContentObjectController</prop>
				<prop key="getContentObjectDynamicVariant.form">asyncContentObjectDynamicVariantController</prop>
				<prop key="uploadFileHandler.form">asyncFileUploadController</prop>
				<prop key="getVariables.form">asyncVariablesController</prop>
				<prop key="getTargetGroups.form">asyncTargetGroupsController</prop>
				<prop key="getRules.form">asyncRulesController</prop>
				<prop key="getTargeting.form">asyncTargetingController</prop>
				<prop key="getCloudTags.form">asyncTagCloudController</prop>
<!--				<prop key="getDataGroupHierarchy.form">asyncDataGroupHierarchyController</prop>-->
				<prop key="getContentSearchResult.form">asyncContentSearchController</prop>
				<prop key="getReferenceCheck.form">asyncReferenceCheckController</prop>
				<prop key="getCommunicationProofStatus.form">asyncCommunicationProofController</prop>
				<prop key="healthCheck.form">asyncHealthCheckController</prop>
				<prop key="getTestResultStatus.form">asyncTestResultController</prop>
				<prop key="getTestingListResults.form">asyncTestingListResultsController</prop>
				<prop key="getDeliveryEventStatus.form">asyncDeliveryEventStatusController</prop>
				<prop key="context.form">asyncContextController</prop>
				<prop key="getSimulationResultStatus.form">asyncSimulationResultController</prop>
				<prop key="getNodeProcessStatus.form">asyncNodeProcessResultController</prop>
				<prop key="getClientLanguageRefresh.form">asyncClientLanguageRefresh</prop>
				<prop key="getUserLocales.form">asyncUserLocaleController</prop>
				<prop key="getGeneratedSecretKey.form">asyncKeyGenerateController</prop>
				<prop key="getMetadata.form">asyncMetadataController</prop>
				<prop key="getInteractiveData.form">asyncConnectedInteractiveController</prop>
				<prop key="getSegResult.form">asyncSegmentationResultController</prop>
				<prop key="getContentHistory.form">asyncContentHistoryController</prop>
				<prop key="getRationalizerContentHistory.form">asyncRationalizerContentHistoryController</prop>
				<prop key="getContentCompare.form">asyncContentCompareController</prop>
				<prop key="getContentTargeting.form">asyncContentTargetingController</prop>
				<prop key="getLayout.form">asyncLayoutController</prop>
				<prop key="getProject.form">asyncProjectController</prop>
				<prop key="getBackgroundTask.form">asyncBackgroundTaskController</prop>
				<prop key="getBackgroundTaskDetail.form">asyncBackgroundTaskDetailController</prop>
				<prop key="getProjectTask.form">asyncProjectTaskController</prop>
				<prop key="getSessionKeepAlive.form">asyncSessionKeepAliveController</prop>
				<prop key="dataSourceTree.form">asyncDataSourceTreeController</prop>
                <prop key="touchpointExchangeList.form">asyncTouchpointExchangeListController</prop>
				<prop key="getSftpRepoData.form">asyncSftpRepoDataController</prop>
                <prop key="getJobStatusList.form">asyncJobStatusListController</prop>
				<prop key="getEmailProofData.form">asyncEmailProofController</prop>
				<prop key="getTinymceMenuData.form">asyncTinymceMenuController</prop>
                <prop key="messageSyncOperations.form">asyncMessageSyncOperationsController</prop>
                <prop key="getTinymceClipboardData.form">asyncTinymceClipboardController</prop>
                <prop key="getSyncReportResult.form">asyncSyncReportResultController</prop>
				<prop key="asyncJsonListTableController.form">asyncJsonListTableController</prop>
				<prop key="bundleDeliveryTestConnection.form">asyncBundleDeliveryTestConnectionController</prop>
				<prop key="generateBundleDeliverySSHKey.form">asyncGenerateBundleDeliverySSHKey</prop>
				<prop key="/variantTree/data/asyncVariantTree.form">asyncVariantTree</prop>
				<prop key="asyncDynamicVariantTree.form">asyncDynamicVariantTreeController</prop>
				<prop key="/variantTree/data/asyncCheckboxTree.form">asyncCheckboxTree</prop>
				<prop key="asyncContentIntelligence.form">asyncContentIntelligenceController</prop>
				<prop key="asyncContentAssistant.form">asyncContentAssistantController</prop>
				<prop key="asyncRationalizerDashboardInfo.form">asyncRationalizerDashboardInfoController</prop>
				<prop key="asyncRationalizerConsolidate.form">asyncRationalizerConsolidateController</prop>
				<prop key="asyncRationalizerUtil.form">asyncRationalizerUtilController</prop>
				<prop key="asyncConnectedUtil.form">asyncConnectedUtilController</prop>
				<prop key="asyncGlobalDashboardInfo.form">asyncGlobalDashboardInfoController</prop>
				<prop key="asyncMarcieDiagnostic.form">asyncMarcieDiagnosticController</prop>
				<prop key="asyncRationalizerSharedContent.form">asyncRationalizerSharedContentListDetailController</prop>
				<prop key="getDataForTaskObjectSelectMenu.form">asyncDataForTaskObjectSelectMenuController</prop>
				<prop key="getContentPowerEdit.form">asyncContentPowerEditController</prop>
				<prop key="asyncStyleManager.form">asyncStyleManagerController</prop>
				<prop key="getMetatags.form">asyncMetatagsEditController</prop>
				<prop key="getEditorData.form">asyncEditorDataController</prop>

				<prop key="/search/global_search.form">globalSearchController</prop>

				<!-- Context Bar Controllers -->
				<prop key="/contextbar/touchpoint_context_menu.form">touchpointContextMenuController</prop>
				<prop key="/contextbar/touchpoint_setup_context_menu.form">touchpointSetupContextMenuController</prop>

				<prop key="/contextbar/search_context_menu.form">searchContextMenuController</prop>
				<prop key="/contextbar/selection_toggle_context_menu.form">selectionToggleContextMenuController</prop>
				<prop key="/contextbar/selection_actns_context_menu.form">selectionActionsContextMenuController</prop>

				<prop key="/contextbar/language_context_menu.form">languageContextMenuController</prop>

				<prop key="/contextbar/branch_toggle_context_menu.form">branchToggleContextMenuController</prop>
				<prop key="/contextbar/branch_actns_context_menu.form">branchActionsContextMenuController</prop>

                <prop key="/contextbar/asyncLanguageContextMenu.form">asyncLanguageContextMenu</prop>


				<!-- Other Controllers -->
				<prop key="html_display.form">htmlDisplayController</prop>

   	          	<prop key="/user/settings_edit.form">userSettingsController</prop>
   	          	<prop key="/user/workspace_reset.form">userSettingsController</prop>
   	          	<prop key="/user/password_change.form">userSettingsController</prop>
				<prop key="/password_reset.form">userSettingsController</prop>
				<prop key="/verify_email.form">userSettingsController</prop>

				<prop key="/password_recovery.form">passwordRecoveryController</prop>

				<prop key="/reports/scenario_customer_report_view.form">scenarioCustomerReportViewController</prop>
				<prop key="/reports/reports_message_sub.form">scenarioMessageSubReportViewController</prop>
				<prop key="/reports/scenario_message_report_view.form">reportScenarioReportMessageController</prop>
				<prop key="/reports/scenario_insert_report_view.form">reportScenarioReportInsertController</prop>
				<prop key="/reports/scenario_customer_insert_report_view.form">scenarioCustomerReportInsertViewController</prop>
				<prop key="/reports/reports_insert_sub.form">scenarioInsertSubReportViewController</prop>

				<prop key="/testing/scenario_customer_report_view.form">scenarioCustomerTestingViewController</prop>
				<prop key="/testing/reports_message_sub.form">scenarioMessageSubTestingViewController</prop>
				<prop key="/testing/reports_message_sub_sub.form">testMessageSubSubViewController</prop>
				<prop key="/testing/scenario_customer_insert_report_view.form">scenarioCustomerInsertTestingViewController</prop>

				<prop key="/report_display.form">reportDisplayController</prop>
				<prop key="/report_scenario_display.form">reportScenarioDisplayController</prop>

				<prop key="/dashboards/my_dashboard.form">myDashboardController</prop>
				<prop key="/dashboards/my_dashboard_mobile.form">myDashboardMobileController</prop>
				<prop key="/dashboards/global_dashboard.form">globalDashBoardController</prop>
				<prop key="/dashboards/global_dashboard_duplicates.form">globalDashboardDuplicatesController</prop>
				<prop key="/dashboards/global_dashboard_duplicates_details.form">globalDashboardDuplicatesDetailsController</prop>
                <prop key="/dashboards/global_dashboard_sentiment.form">globalDashboardSentimentController</prop>
				<prop key="/dashboards/global_dashboard_sentiment_details.form">globalDashboardSentimentDetailsController</prop>
				<prop key="/dashboards/global_dashboard_reading.form">globalDashboardReadingController</prop>
				<prop key="/dashboards/global_dashboard_reading_details.form">globalDashboardReadingDetailsController</prop>
				<prop key="/dashboards/global_dashboard_navigation_widget.form">globalDashboardNavigationWidgetController</prop>
				<prop key="/dashboards/global_dashboard_brand.form">globalDashboardBrandController</prop>
				<prop key="/dashboards/global_dashboard_brand_details.form">globalDashboardBrandDetailsController</prop>
				<prop key="/dashboards/global_dashboard_translation.form">globalDashboardTranslationController</prop>
				<prop key="/dashboards/global_dashboard_translation_details.form">globalDashboardTranslationDetailsController</prop>

                <prop key="/job_center/job_center.form">jobCenterController</prop>
                <prop key="/job_center/backend_component_downloads.form">backendComponentDownloads</prop>

				<prop key="/switch_to_node.form">switchToNodeController</prop>
				
				<prop key="/security/owasp.csrfguard.form">csrfSecurityController</prop>

				<prop key="/displaymessage.form">displayMessageController</prop>
				
				<prop key="/workflow/workflow_edit.form">workflowEditController</prop>
				<prop key="/workflow/workflow_history.form">workflowHistoryController</prop>
				<prop key="/workflow/workflow_library.form">workflowLibraryController</prop>
				<prop key="/workflow/workflow_library_detail.form">workflowLibraryDetailController</prop>
				
				<prop key="/wtu/delete_unref_assets.form">deleteUnreferencedAssetsController</prop>
				
				<prop key="/file/source_editor.form">sourceEditorController</prop>

                <!-- Web Framework Conrollers -->
                <prop key="/assets/package.form">webAssetBundleController</prop>

				<!-- Node Health Check Controller -->
				<prop key="healthcheck.form">nodeHealthCheckController</prop>

				<!-- Brand -->
				<prop key="/brand/brand_profile_edit.form">brandProfileEditController</prop>
				<prop key="/brand/brand_profile_list.form">brandProfileListController</prop>
				<prop key="/brand/brand_profile_list_detail.form">brandProfileListDetailController</prop>

				<!-- Tag Cloud -->
<!--				<prop key="/brand/brand_profile_edit.form">brandProfileEditController</prop>-->
				<prop key="/dataadmin/tag_cloud_list.form">tagCloudListController</prop>
<!--				<prop key="/brand/brand_profile_list_detail.form">brandProfileListDetailController</prop>-->


				<!-- Configurable Links -->
				<prop key="/link/configurable_links_edit.form">configurableLinksEditController</prop>

				<!-- PINC -->
				<prop key="/pinc/pinc_config.form">pincConfigController</prop>
				<prop key="/pinc/oidc_token_refresh.form">oidcTokenRefreshController</prop>
				<prop key="/pinc/api_token_list.form">genericEmptyController</prop>
				<prop key="/pinc/encryption_key_list.form">genericEmptyController</prop>
				<prop key="/pinc/executable_list.form">genericEmptyController</prop>
				<prop key="/pinc/file_list.form">genericEmptyController</prop>
				<prop key="/pinc/application_list.form">genericEmptyController</prop>
				<prop key="/pinc/job_list.form">genericEmptyController</prop>
				<prop key="/pinc/authentication_key_list.form">genericEmptyController</prop>
				<prop key="/pinc/notification_list.form">genericEmptyController</prop>
				<prop key="/pinc/mp_instance_list.form">genericEmptyController</prop>
				<prop key="/pinc/engine_list.form">genericEmptyController</prop>
				<prop key="/pinc/destination_list.form">genericEmptyController</prop>
				<prop key="/pinc/job_manager_list.form">genericEmptyController</prop>
				<prop key="/pinc/webhook_list.form">genericEmptyController</prop>
				<prop key="/pinc/redirect.form">defaultPageController</prop>


            </props>
        </property>
    </bean>

	<!-- Spring View Resolvers -->
	<bean id="viewResolver" class="org.springframework.web.servlet.view.UrlBasedViewResolver">
		<property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>
		<property name="prefix" value="/"/>
		<property name="suffix" value=".jsp"/>
	</bean>	

	<bean id="defaultHandlerMapping" class="org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping"/>
	
	<!-- Task -->
	<bean id="taskListController" class="com.prinova.messagepoint.controller.tasks.TaskListController">
		<property name="successView" value="task_list.form" />
		<property name="formView" value="tasks/task_list" />
		<property name="validator" ref="taskListValidator"/>
	</bean>	
	<bean id="taskListValidator" class="com.prinova.messagepoint.controller.tasks.TaskListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.tasks.TaskListWrapper" />
	</bean>
	<bean id="taskListDetailController" class="com.prinova.messagepoint.controller.tasks.TaskListDetailController">
		<property name="formView" value="tasks/task_list_detail" />
	</bean>

	<bean id="taskEditController" class="com.prinova.messagepoint.controller.tasks.TaskEditController">
		<property name="formView" value="tasks/task_edit"/>
		<property name="validator" ref="taskEditValidator" />
	</bean>
	<bean id="taskEditValidator" class="com.prinova.messagepoint.controller.tasks.TaskEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.tasks.TaskEditWrapper" />
	</bean>

	<!-- Entity -->
	<bean id="entityListController" class="com.prinova.messagepoint.controller.entity.EntityListController">
		<property name="successView" value="entity_list.form" />
		<property name="formView" value="entities/entity_list" />
	</bean>

	<!-- Change -->
	<bean id="changeListController" class="com.prinova.messagepoint.controller.tasks.ChangeListController">
		<property name="successView" value="change_list.form" />
		<property name="formView" value="tasks/change_list" />
	</bean>
	<bean id="changeListDetailController" class="com.prinova.messagepoint.controller.tasks.ChangeListDetailController">
		<property name="formView" value="tasks/change_list_detail" />
	</bean>

	<!-- Project -->
	<bean id="projectListController" class="com.prinova.messagepoint.controller.projects.ProjectListController">
		<property name="successView" value="project_list.form" />
		<property name="formView" value="projects/project_list" />
		<property name="validator" ref="projectListValidator"/>
	</bean>	
	<bean id="projectListValidator" class="com.prinova.messagepoint.controller.projects.ProjectListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.projects.ProjectListWrapper" />
	</bean>
	<bean id="projectListDetailController" class="com.prinova.messagepoint.controller.projects.ProjectListDetailController">
		<property name="formView" value="projects/project_list_detail" />
	</bean>
	
	<bean id="projectEditController" class="com.prinova.messagepoint.controller.projects.ProjectEditController">
		<property name="formView" value="projects/project_edit"/>
		<property name="validator" ref="projectEditValidator" />
	</bean>
	<bean id="projectEditValidator" class="com.prinova.messagepoint.controller.projects.ProjectEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.projects.ProjectEditWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="project.name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>		
	</bean>
	
	<bean id="projectTaskWorkflowListController" class="com.prinova.messagepoint.controller.projects.ProjectTaskWorkflowListController">
		<property name="successView" value="project_task_workflow_list.form" />
		<property name="formView" value="projects/project_task_workflow_list" />
		<property name="validator" ref="projectTaskWorkflowListValidator"/>
	</bean>	
	<bean id="projectTaskWorkflowListValidator" class="com.prinova.messagepoint.controller.projects.ProjectTaskWorkflowListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.projects.ProjectTaskWorkflowListWrapper" />
	</bean>
	<bean id="projectTaskWorkflowListDetailController" class="com.prinova.messagepoint.controller.projects.ProjectTaskWorkflowListDetailController">
		<property name="formView" value="projects/project_task_workflow_list_detail" />
	</bean>
	
	<bean id="projectWorkflowManagementController" class="com.prinova.messagepoint.controller.projects.ProjectWorkflowManagementController">
		<property name="successView" value="project_list.form" />
		<property name="formView" value="projects/project_workflow_management" />
		<property name="validator" ref="projectWorkflowManagementValidator"/>
	</bean>	
	<bean id="projectWorkflowManagementValidator" class="com.prinova.messagepoint.controller.projects.ProjectWorkflowManagementValidator">
		<property name="className" value="com.prinova.messagepoint.controller.projects.ProjectWorkflowManagementWrapper" />
	</bean>

	<!-- Brand -->
	<bean id="brandProfileEditController" class="com.prinova.messagepoint.controller.brand.BrandProfileEditController">
		<property name="successView" value="brand_profile_edit.form" />
		<property name="formView" value="brand/brand_profile_edit" />
		<property name="validator" ref="brandProfileEditValidator"/>
	</bean>
	<bean id="brandProfileEditValidator" class="com.prinova.messagepoint.controller.brand.BrandProfileEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.brand.BrandProfileEditWrapper" />
	</bean>

	<bean id="brandProfileListController" class="com.prinova.messagepoint.controller.brand.BrandProfileListController">
		<property name="formView" value="brand/brand_profile_list"/>
		<property name="successView" value="brand_profile_list.form"/>
		<property name="validator" ref="brandProfileListValidator" />
	</bean>
	<bean id="brandProfileListValidator" class="com.prinova.messagepoint.controller.brand.BrandProfileListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.brand.BrandProfileListWrapper" />
	</bean>
	<bean id="brandProfileListDetailController" class="com.prinova.messagepoint.controller.brand.BrandProfileListDetailController">
		<property name="formView" value="brand/brand_profile_list_detail"/>
	</bean>

	<!-- Tag Cloud -->
	<bean id="tagCloudListController" class="com.prinova.messagepoint.controller.tagcloud.TagCloudListController">
		<property name="formView" value="dataadmin/tag_cloud_list"/>
		<property name="successView" value="tag_cloud_list.form"/>
		<property name="validator" ref="tagCloudListValidator" />
	</bean>
	<bean id="tagCloudListValidator" class="com.prinova.messagepoint.controller.tagcloud.TagCloudListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.tagcloud.TagCloudListWrapper" />
	</bean>

	<!-- Configurable Links -->
	<bean id="configurableLinksEditController" class="com.prinova.messagepoint.controller.link.ConfigurableLinksEditController">
		<property name="formView" value="link/configurable_links_edit" />
		<property name="successView" value="configurable_links_edit.form" />
		<property name="validator" ref="configurableLinksEditValidator"/>
	</bean>
	<bean id="configurableLinksEditValidator" class="com.prinova.messagepoint.controller.link.ConfigurableLinksEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.link.ConfigurableLinksEditWrapper" />
	</bean>

	<!-- PINC -->
	<bean id="pincConfigController" class="com.prinova.messagepoint.controller.pinc.PincConfigController" />
	<bean id="oidcTokenRefreshController" class="com.prinova.messagepoint.controller.pinc.OIDCTokenRefreshController"/>
	
	<bean id="binaryStreamController" class="com.prinova.messagepoint.controller.BinaryStreamController" >
		<property name="dataService" ref="cacheDataService"/>
	</bean>

	<bean id="binaryWebStreamController" class="com.prinova.messagepoint.controller.BinaryStreamController" >
		<property name="downloadFromWebRoot" value="true" />
		<property name="dataService" ref="cacheDataService"/>
	</bean>
	
	<bean id="htmlDisplayController" class="com.prinova.messagepoint.controller.HtmlDisplayController" />

	<bean id="mainTreeController" class="com.prinova.messagepoint.controller.NavigationTreeController" />
	
	<!-- Asynchronous Controllers -->
	<bean id="asyncContentController" class="com.prinova.messagepoint.controller.AsyncContentController" />
	<bean id="asyncContentPreviewController" class="com.prinova.messagepoint.controller.AsyncContentPreviewController" />
	<bean id="asyncSelectionTreeController" class="com.prinova.messagepoint.controller.AsyncSelectionTreeController" />
	<bean id="asyncObjectInfoController" class="com.prinova.messagepoint.controller.AsyncObjectInfoController" />
	<bean id="asyncSimulationExportContentController" class="com.prinova.messagepoint.controller.simulation.SimulationExportController" />
	<bean id="asyncDataForSelectMenuController" class="com.prinova.messagepoint.controller.AsyncDataForSelectMenuController" />
	<bean id="asyncDataForMultiselectMenuController" class="com.prinova.messagepoint.controller.AsyncDataForMultiselectMenuController" />
	<bean id="uploadedImageEventController" class="com.prinova.messagepoint.controller.AsyncUploadedImageEventController" />
	<bean id="asyncSpellcheckController" class="com.prinova.messagepoint.controller.AsyncSpellcheckController" />
	<bean id="asyncStatusPollingController" class="com.prinova.messagepoint.controller.AsyncStatusPollingController" />
	<bean id="asyncCollectionDataValuesController" class="com.prinova.messagepoint.controller.AsyncCollectionDataValuesController" />
    <bean id="asyncCollectionDataValuesTreeNodesController" class="com.prinova.messagepoint.controller.AsyncCollectionDataValuesTreeNodesController" />
	<bean id="asyncSectionImageThumbnailController" class="com.prinova.messagepoint.controller.AsyncSectionImageThumbnailController" />
	<bean id="asyncInsertImageThumbnailController" class="com.prinova.messagepoint.controller.AsyncInsertImageThumbnailController" />
	<bean id="asyncInsertScheduleExportController" class="com.prinova.messagepoint.controller.AsyncInsertScheduleExportController" />
	<bean id="asyncTargetingController" class="com.prinova.messagepoint.controller.AsyncTargetingController" />
	<bean id="asyncReportResultPollingController" class="com.prinova.messagepoint.controller.AsyncReportResultPollingController" />
	<bean id="asyncExportResultPollingController" class="com.prinova.messagepoint.controller.AsyncExportResultPollingController" />
	<bean id="asyncListTableController" class="com.prinova.messagepoint.controller.AsyncListTableController" />
	<bean id="asyncWidgetsController" class="com.prinova.messagepoint.controller.AsyncWidgetsController" />
	<bean id="asyncCategoryMenuItemsController" class="com.prinova.messagepoint.controller.AsyncCategoryMenuItemsController" />
	<bean id="asyncContentObjectController" class="com.prinova.messagepoint.controller.AsyncContentObjectController" />
	<bean id="asyncContentObjectDynamicVariantController" class="com.prinova.messagepoint.controller.AsyncContentObjectDynamicVariantController" />
	<bean id="asyncFileUploadController" class="com.prinova.messagepoint.controller.AsyncFileUploadController">
		<constructor-arg ref="cacheDataService"/>
		<constructor-arg ref="cacheDataRepository"/>
	</bean>
	<bean id="asyncVariablesController" class="com.prinova.messagepoint.controller.AsyncVariablesController" />
	<bean id="asyncTargetGroupsController" class="com.prinova.messagepoint.controller.AsyncTargetGroupsController" />
	<bean id="asyncRulesController" class="com.prinova.messagepoint.controller.AsyncRulesController" />
	<bean id="asyncTagCloudController" class="com.prinova.messagepoint.controller.AsyncTagCloudController" />
<!--	<bean id="asyncDataGroupHierarchyController" class="com.prinova.messagepoint.controller.AsyncDataGroupHierarchyController" />-->
	<bean id="asyncContentSearchController" class="com.prinova.messagepoint.controller.AsyncContentSearchController" />
	<bean id="asyncReferenceCheckController" class="com.prinova.messagepoint.controller.AsyncReferenceCheckController" />
	<bean id="asyncCommunicationProofController" class="com.prinova.messagepoint.controller.AsyncCommunicationProofController" />
	<bean id="asyncHealthCheckController" class="com.prinova.messagepoint.controller.AsyncHealthCheckController" />
	<bean id="asyncTestResultController" class="com.prinova.messagepoint.controller.AsyncTestResultController" />
	<bean id="asyncTestingListResultsController" class="com.prinova.messagepoint.controller.AsyncTestingListResultsController" />
	<bean id="asyncDeliveryEventStatusController" class="com.prinova.messagepoint.controller.AsyncDeliveryEventStatusController" />
	<bean id="asyncContextController" class="com.prinova.messagepoint.controller.AsyncContextController" />
	<bean id="asyncSimulationResultController" class="com.prinova.messagepoint.controller.AsyncSimulationResultController" />
	<bean id="asyncNodeProcessResultController" class="com.prinova.messagepoint.controller.AsyncNodeProcessResultController" />
	<bean id="asyncClientLanguageRefresh" class="com.prinova.messagepoint.controller.AsyncClientLanguageRefreshController" />
	<bean id="asyncUserLocaleController" class="com.prinova.messagepoint.controller.AsyncUserLocaleController" />
	<bean id="asyncKeyGenerateController" class="com.prinova.messagepoint.controller.AsyncKeyGenerateController" />
	<bean id="asyncMetadataController" class="com.prinova.messagepoint.controller.AsyncMetadataController" />
	<bean id="asyncConnectedInteractiveController" class="com.prinova.messagepoint.controller.AsyncConnectedInteractiveController" />
	<bean id="asyncSegmentationResultController" class="com.prinova.messagepoint.controller.AsyncSegmentationResultController" />
	<bean id="asyncContentHistoryController" class="com.prinova.messagepoint.controller.AsyncContentHistoryController" />
	<bean id="asyncRationalizerContentHistoryController" class="com.prinova.messagepoint.controller.AsyncRationalizerContentHistoryController" />
	<bean id="asyncContentCompareController" class="com.prinova.messagepoint.controller.AsyncContentCompareController" />
	<bean id="asyncContentTargetingController" class="com.prinova.messagepoint.controller.AsyncContentTargetingController" />
	<bean id="asyncLayoutController" class="com.prinova.messagepoint.controller.AsyncLayoutController" />
	<bean id="asyncProjectController" class="com.prinova.messagepoint.controller.AsyncProjectController" />
	<bean id="asyncBackgroundTaskController" class="com.prinova.messagepoint.controller.AsyncBackgroundTaskController" />
	<bean id="asyncBackgroundTaskDetailController" class="com.prinova.messagepoint.controller.AsyncBackgroundTaskDetailController" />
	<bean id="asyncProjectTaskController" class="com.prinova.messagepoint.controller.AsyncProjectTaskController" />
	<bean id="asyncSessionKeepAliveController" class="com.prinova.messagepoint.controller.AsyncSessionKeepAliveController" />
	<bean id="asyncSftpRepoDataController" class="com.prinova.messagepoint.controller.AsyncSftpRepoDataController" />
    <bean id="asyncDataSourceTreeController" class="com.prinova.messagepoint.controller.AsyncDataSourceTreeController" />
    <bean id="asyncTouchpointExchangeListController" class="com.prinova.messagepoint.controller.AsyncTouchpointExchangeListController" />
    <bean id="asyncJobStatusListController" class="com.prinova.messagepoint.controller.AsyncJobStatusListController" />
	<bean id="asyncEmailProofController" class="com.prinova.messagepoint.controller.AsyncEmailProofController" />
    <bean id="asyncMessageSyncOperationsController" class="com.prinova.messagepoint.controller.AsyncMessageSyncOperationsController" />
	<bean id="asyncTinymceMenuController" class="com.prinova.messagepoint.controller.AsyncTinymceMenuController" />
	<bean id="asyncTinymceClipboardController" class="com.prinova.messagepoint.controller.AsyncTinymceClipboardController" />
	<bean id="asyncSyncReportResultController" class="com.prinova.messagepoint.controller.AsyncSyncReportResultController" />
	<bean id="asyncJsonListTableController" class="com.prinova.messagepoint.controller.AsyncJsonListTableController" />
	<bean id="asyncBundleDeliveryTestConnectionController" class="com.prinova.messagepoint.controller.AsyncBundleDeliveryTestConnectionController" />
	<bean id="asyncGenerateBundleDeliverySSHKey" class="com.prinova.messagepoint.controller.AsyncGenerateBundleDeliverySSHKey" />
	<bean id="asyncVariantTree" class="com.prinova.messagepoint.controller.AsyncVariantTreeController" />
	<bean id="asyncDynamicVariantTreeController" class="com.prinova.messagepoint.controller.AsyncDynamicVariantTreeController" />
	<bean id="asyncCheckboxTree" class="com.prinova.messagepoint.controller.AsyncCheckboxTreeController" />
	<bean id="asyncContentIntelligenceController" class="com.prinova.messagepoint.controller.AsyncContentIntelligenceController" />
	<bean id="asyncContentAssistantController" class="com.prinova.messagepoint.controller.contentintelligence.AsyncContentAssistantController" />
	<bean id="asyncRationalizerDashboardInfoController" class="com.prinova.messagepoint.controller.rationalizer.AsyncRationalizerDashboardInfoController" />
	<bean id="asyncRationalizerConsolidateController" class="com.prinova.messagepoint.controller.rationalizer.AsyncRationalizerConsolidateController" />
	<bean id="asyncRationalizerUtilController" class="com.prinova.messagepoint.controller.rationalizer.AsyncRationalizerUtilController" />
	<bean id="asyncConnectedUtilController" class="com.prinova.messagepoint.controller.communication.connected.AsyncConnectedUtilController" />
	<bean id="asyncGlobalDashboardInfoController" class="com.prinova.messagepoint.controller.dashboards.AsyncGlobalDashboardInfoController" />
	<bean id="asyncMarcieDiagnosticController" class="com.prinova.messagepoint.controller.admin.AsyncMarcieDiagnosticController" />
	<bean id="asyncRationalizerSharedContentListDetailController" class="com.prinova.messagepoint.controller.rationalizer.AsyncRationalizerSharedContentListDetailController" />
	<bean id="asyncDataForTaskObjectSelectMenuController" class="com.prinova.messagepoint.controller.tasks.AsyncDataForTaskObjectSelectMenuController" />
	<bean id="asyncChatMetadataController" class="com.prinova.messagepoint.controller.AsyncChatMetadataController" />

	<bean id="asyncConnectedTouchpointInterview" class="com.prinova.messagepoint.controller.communication.connected.AsyncConnectedTouchpointInterviewController">
		<constructor-arg ref="cacheDataService"/>
	</bean>

	<bean id="asyncCommunicationOrderEntry" class="com.prinova.messagepoint.controller.communication.connected.AsyncCommunicationOrderEntryController">
		<constructor-arg ref="cacheDataService"/>
	</bean>

	<bean id="asyncContentPowerEditController" class="com.prinova.messagepoint.controller.AsyncContentPowerEditController" />
	<bean id="asyncMetatagsEditController" class="com.prinova.messagepoint.controller.AsyncMetatagsEditController" />
	<bean id="asyncEditorDataController" class="com.prinova.messagepoint.controller.AsyncEditorDataController" />

    <bean id="globalSearchController" class="com.prinova.messagepoint.controller.search.GlobalSearchController">
		<property name="formView" value="search/global_search"/>
		<property name="validator" ref="globalSearchValidator" />
	</bean>

	<bean id="globalSearchValidator" class="com.prinova.messagepoint.controller.search.GlobalSearchValidator">
		<property name="className" value="com.prinova.messagepoint.controller.search.GlobalSearchWrapper" />
	</bean>
	
    <!-- Context Bar Controllers -->
	<bean id="touchpointContextMenuController" class="com.prinova.messagepoint.controller.contextbar.TouchpointContextMenuController">
		<property name="formView" value="contextbar/touchpoint_context_menu"/>
		<property name="successView" value="touchpoint_context_menu.form" />
	</bean>
	<bean id="searchContextMenuController" class="com.prinova.messagepoint.controller.contextbar.SearchContextMenuController">
		<property name="formView" value="contextbar/search_context_menu"/>
	</bean>
	<bean id="selectionToggleContextMenuController" class="com.prinova.messagepoint.controller.contextbar.SelectionToggleContextMenuController">
		<property name="formView" value="contextbar/selection_toggle_context_menu"/>
		<property name="successView" value="selection_toggle_context_menu.form" />
		<property name="validator" ref="selectionToggleContextMenuValidator" />
	</bean>
	<bean id="selectionToggleContextMenuValidator" class="com.prinova.messagepoint.controller.contextbar.SelectionToggleContextMenuValidator">
		<property name="className" value="com.prinova.messagepoint.controller.contextbar.SelectionToggleContextMenuWrapper" />
	</bean>
	
	<bean id="selectionActionsContextMenuController" class="com.prinova.messagepoint.controller.touchpoints.TouchpointVariantListController">
		<property name="formView" value="contextbar/selection_actns_context_menu"/>
		<property name="successView" value="selection_actns_context_menu.form" />
		<property name="validator" ref="tpVariantListValidator"/>
	</bean>
	<bean id="languageContextMenuController" class="com.prinova.messagepoint.controller.contextbar.LanguageContextMenuController">
		<property name="formView" value="contextbar/language_context_menu"/>
		<property name="successView" value="language_context_menu.form" />
	</bean>
	<bean id="touchpointSetupContextMenuController" class="com.prinova.messagepoint.controller.contextbar.TouchpointSetupContextMenuController">
		<property name="formView" value="contextbar/touchpoint_setup_context_menu"/>
		<property name="successView" value="touchpoint_setup_context_menu.form" />
	</bean>
	<bean id="branchActionsContextMenuController" class="com.prinova.messagepoint.controller.admin.BranchListController">
		<property name="formView" value="contextbar/branch_actns_context_menu"/>
		<property name="successView" value="branch_actns_context_menu.form" />
		<property name="validator" ref="branchListValidator"/>
	</bean>
	<bean id="branchToggleContextMenuController" class="com.prinova.messagepoint.controller.contextbar.BranchToggleContextMenuController">
		<property name="formView" value="contextbar/branch_toggle_context_menu"/>
		<property name="successView" value="branch_toggle_context_menu.form" />
		<property name="validator" ref="branchToggleContextMenuValidator" />
	</bean>
	<bean id="branchToggleContextMenuValidator" class="com.prinova.messagepoint.controller.contextbar.BranchToggleContextMenuValidator">
		<property name="className" value="com.prinova.messagepoint.controller.contextbar.BranchToggleContextMenuWrapper" />
	</bean>

	<bean id="updateLanguageSettingsController" class="com.prinova.messagepoint.controller.admin.UpdateLanguageSettingsController" >
		<property name="formView" 		value="admin/language_settings_edit" />
		<property name="successView" 	value="language_settings_view.jsp" />
	</bean>

	<bean id="updateLocaleSettingsController" class="com.prinova.messagepoint.controller.admin.UpdateLocaleSettingsController" >
		<property name="formView" 		value="admin/locale_settings_edit" />
		<property name="successView" 	value="locale_settings_view.jsp" />
		<property name="validator"    	ref="updateLocaleSettingsValidator" />
	</bean>
	
	<bean id="licenceManagementController" class="com.prinova.messagepoint.controller.admin.LicenceManagementController" >
		<property name="formView" 		value="admin/licence_management" />
		<property name="successView" 	value="licence_management.form" />
		<property name="validator" 		ref="licenceManagementValidator" />
	</bean>
	<bean id="licenceManagementValidator" class="com.prinova.messagepoint.controller.admin.LicenceManagementValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.LicenceManagementWrapper" />
	</bean>
	
	<bean id="nodeLicenceManagementController" class="com.prinova.messagepoint.controller.admin.LicenceManagementController" >
		<property name="formView" 		value="admin/node_licence_management" />
		<property name="successView" 	value="node_licence_management.form" />
		<property name="validator" 		ref="licenceManagementValidator" />
	</bean>
	
	<bean id="branchLicenceManagementController" class="com.prinova.messagepoint.controller.admin.LicenceManagementController" >
		<property name="formView" 		value="admin/branch_licence_management" />
		<property name="successView" 	value="branch_licence_management.form" />
		<property name="validator" 		ref="branchLicenceManagementValidator" />
	</bean>
	<bean id="branchLicenceManagementValidator" class="com.prinova.messagepoint.controller.admin.BranchLicenceManagementValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.LicenceManagementWrapper" />
	</bean>	

	<bean id="updateLocaleSettingsValidator" class="com.prinova.messagepoint.controller.admin.UpdateLocaleSettingsValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.UpdateLocaleSettingsController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="dictionary" />
        			<property name="label" value="page.label.dictionary" />
        			<property name="propertyType" ref="Description" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="64" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum.underscore.dash.apos.parenth.space" />
					<property name="restrictedCharsRegex" value="^[A-Za-z0-9\s_\-')(]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="currencySymbol" />
        			<property name="label" value="page.label.currencysymbol" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="2" />
					<property name="restrictedCharsList" value="page.text.validator.any.character" />
					<property name="restrictedCharsRegex" value=".*" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="thousandsSeparator" />
        			<property name="label" value="page.label.thousandsseparator" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="1" />
					<property name="restrictedCharsList" value="page.text.validator.any.character" />
					<property name="restrictedCharsRegex" value=".*" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="decimalSymbol" />
        			<property name="label" value="page.label.decimalsymbol" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="1" />
					<property name="restrictedCharsList" value="page.text.validator.any.character" />
					<property name="restrictedCharsRegex" value=".*" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="numberOfDecimals" />
        			<property name="label" value="page.label.numberofdecimals" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="1" />
					<property name="restrictedCharsList" value="page.text.validator.numeric" />
					<property name="restrictedCharsRegex" value="^[0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="booleanSymbolTrue" />
        			<property name="label" value="page.label.booleansymboltrue" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="10" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum" />
					<property name="restrictedCharsRegex" value="^[a-zA-Z0-9]*+" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="booleanSymbolFalse" />
        			<property name="label" value="page.label.booleansymbolfalse" />
					<property name="mandatory" value="false" />
					<property name="minLength" value="1" />
					<property name="maxLength" value="10" />
					<property name="restrictedCharsList" value="page.text.validator.alphanum" />
					<property name="restrictedCharsRegex" value="^[a-zA-Z0-9]*+" />
        		</bean>
        	</list>
		</property>
	</bean>

	<bean id="licenceListController" class="com.prinova.messagepoint.controller.admin.LicenceListController" >
		<property name="formView" 		value="admin/licence_list" />
		<property name="successView" 	value="licence_list.form" />
		<property name="validator" 		ref="licenceListValidator" />
	</bean>
	<bean id="licenceListValidator" class="com.prinova.messagepoint.controller.admin.LicenceListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.LicenceListWrapper" />
	</bean>
	
	<bean id="licenceEditController" class="com.prinova.messagepoint.controller.admin.LicenceEditController" >
		<property name="formView" 		value="admin/licence_edit" />
		<property name="successView" 	value="licence_edit.form" />
		<property name="validator" 		ref="licenceEditValidator" />
	</bean>
	<bean id="licenceEditValidator" class="com.prinova.messagepoint.controller.admin.LicenceEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.LicenceEditWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="companyName" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        			<property name="mandatory" value="true" />
        		</bean>
        		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="contractEmail" />
        			<property name="label" value="page.label.contact.email" />
        			<property name="propertyType" ref="Email" />
        			<property name="mandatory" value="true" />        		
        		</bean>
		 	</list>
		 </property>		
	</bean>
	
	<bean id="auditingListController" class="com.prinova.messagepoint.controller.admin.AuditingListController" >
		<property name="formView" 		value="admin/auditing_list" />
		<property name="successView" 	value="auditing_list.form" />
	</bean>
	
	<bean id="notificationSettingsEditController" class="com.prinova.messagepoint.controller.admin.NotificationSettingsEditController" >
		<property name="formView" 		value="admin/notification_settings_edit" />
		<property name="successView" 	value="notification_settings_edit.jsp" />
	</bean>
	
	<!-- Dictionary -->
	<bean id="dictionaryEditController" class="com.prinova.messagepoint.controller.dictionary.DictionaryEditController">
		<property name="formView" value="dictionary/dictionary_edit"/>
		<property name="validator" ref="dictionaryEditValidator" />
	</bean>
	<bean id="dictionaryEditValidator" class="com.prinova.messagepoint.controller.dictionary.DictionaryEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.dictionary.DictionaryEditWrapper" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="dictionary.name" />
        			<property name="label" value="page.label.name" />
        			<property name="propertyType" ref="ObjectName" />
        		</bean>
        	</list>
		</property>		
	</bean>
	
	<bean id="dictionaryListController" class="com.prinova.messagepoint.controller.dictionary.DictionaryListController" >
		<property name="formView" 		value="dictionary/dictionary_list" />
		<property name="successView" 	value="dictionary_list.form" />
		<property name="validator"    		ref="dictionaryListValidator" />
	</bean>
	<bean id="dictionaryListValidator" class="com.prinova.messagepoint.controller.dictionary.DictionaryListValidator">
		<property name="className" value="com.prinova.messagepoint.controller.dictionary.DictionaryListWrapper" />
	</bean>
	<bean id="dictionaryListDetailController" class="com.prinova.messagepoint.controller.dictionary.DictionaryListDetailController">
		<property name="formView" value="dictionary/dictionary_list_detail" />
	</bean>
	
	<bean id="dictionarySourceEditorController" class="com.prinova.messagepoint.controller.dictionary.DictionarySourceEditorController">
		<property name="formView" value="file/source_editor"/>
		<property name="validator" ref="dictionarySourceEditorValidator" />
	</bean>
	<bean id="dictionarySourceEditorValidator" class="com.prinova.messagepoint.controller.dictionary.DictionarySourceEditorValidator">
		<property name="className" value="com.prinova.messagepoint.controller.file.SourceEditorController$SourceCommand" />
	</bean>

	<!-- Touchpoint Dictionaries  -->
	<bean id="touchpointDictionariesEditController" class="com.prinova.messagepoint.controller.admin.TouchpointDictionariesEditController" >
		<property name="formView" value="admin/touchpoint_dictionaries_edit" />
		<property name="successView" value="touchpoint_dictionaries_edit.form"/>
		<property name="validator" ref="touchpointDictionariesEditValidator" />
	</bean>
	<bean id="touchpointDictionariesEditValidator" class="com.prinova.messagepoint.controller.admin.TouchpointDictionariesEditValidator" />

	<bean id="themeEditController" class="com.prinova.messagepoint.controller.admin.ThemeEditController" >
		<property name="formView" 		value="admin/user_interface_edit" />
		<property name="successView" 	value="user_interface_view.jsp" />
		<property name="validator"    		ref="themeEditValidator" />
	</bean>

	<bean id="themeEditValidator" class="com.prinova.messagepoint.controller.admin.ThemeEditValidator">
		<property name="className" value="com.prinova.messagepoint.controller.admin.ThemeEditController$Command" />
		<property name="validationEntries">
	       	<list>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="headertext" />
        			<property name="label" value="page.label.provider.description" />
        			<property name="propertyType" ref="Description" />
        		</bean>	       	
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="providertext" />
        			<property name="label" value="page.label.provider.description" />
        			<property name="propertyType" ref="Description" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="providerLogo.originalFilename" />
        			<property name="label" value="page.label.provider.logo.filename" />
        			<property name="propertyType" ref="FileName" />
        			<property name="maxLength" value="75" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="corporateLogo.originalFilename" />
        			<property name="label" value="page.label.header.logo.filename" />
        			<property name="propertyType" ref="FileName" />
        			<property name="maxLength" value="75" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="providerLogo.fileItem.name" />
        			<property name="label" value="page.label.provider.logo.filepath" />
        			<property name="propertyType" ref="FileName" />
        			<property name="maxLength" value="255" />
        		</bean>
		 		<bean class="com.prinova.messagepoint.validator.MessagepointInputValidationEntry">
        			<property name="propertyName" value="corporateLogo.fileItem.name" />
        			<property name="label" value="page.label.header.logo.filepath" />
        			<property name="propertyType" ref="FileName" />
        			<property name="maxLength" value="255" />
        		</bean>
        		
        	</list>
		</property>
	</bean>

    <bean id="asyncLanguageContextMenu" class="com.prinova.messagepoint.controller.contextbar.AsyncLanguageContextMenu" />

    <!-- Web Framework Controllers -->
    <bean id="webAssetBundleController" class="com.prinova.messagepoint.controller.WebAssetPackageController" />


	<!-- Health Check Controller -->
	<bean id="nodeHealthCheckController" class="com.prinova.messagepoint.controller.NodeHealthCheckController" />

	<!-- GenericEmptyController is empty controller which does nothing, used for React pages -->
	<bean id="genericEmptyController" class="com.prinova.messagepoint.controller.GenericEmptyController" />

</beans>
