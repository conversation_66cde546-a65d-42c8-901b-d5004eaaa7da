<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

  <xsl:include href="compareReportStyleTemplate.xsl"/>

  <xsl:key name="zonekey" match="TouchpointCompare/ReferenceData/Touchpoints/Touchpoint/Sections/Section/Zones/Zone" use="@id"/>
  <xsl:key name="datavaluekey" match="TouchpointCompare/ReferenceData/DataValues/DataValue" use="@id"/>

  <xsl:key name="datasourcekey" match="TouchpointCompare/DataSources/DataSource" use="@id"/>
  <xsl:key name="lookuptablekey" match="TouchpointCompare/LookupTables/LookupTable" use="@id"/>
  <xsl:key name="variablekey" match="TouchpointCompare/Variables/Variable" use="@id"/>
  <xsl:key name="datacollectionkey" match="TouchpointCompare/DataCollections/DataCollection" use="@id"/>
  <xsl:key name="selectorgroupkey" match="TouchpointCompare/SelectorGroups/SelectorGroup" use="@id"/>
  <xsl:key name="targetrulekey" match="TouchpointCompare/TargetRules/TargetRule" use="@id"/>
  <xsl:key name="targetgroupkey" match="TouchpointCompare/TargetGroups/TargetGroup" use="@id"/>

  <xsl:key name="messagekey" match="TouchpointCompare/Messages/Message" use="@id"/>
  <xsl:key name="selectionkey" match="Selection" use="@id"/>
  <xsl:key name="sharedpartkey" match="TouchpointCompare/ReferenceData/SharedContents/SharedContent" use="@id"/>

  <xsl:variable name="showcontent">
    <xsl:value-of select="TouchpointCompare/Metadata/Settings/@content"/>
  </xsl:variable>
  <xsl:variable name="showtargeting">
    <xsl:value-of select="TouchpointCompare/Metadata/Settings/@targeting"/>
  </xsl:variable>
  <xsl:variable name="syncFromOrigin">
    <xsl:value-of select="TouchpointCompare/Metadata/Settings/@syncFromOrigin"/>
  </xsl:variable>
  <xsl:variable name="syncwWithParent">
    <xsl:value-of select="TouchpointCompare/Metadata/Settings/@syncwWithParent"/>
  </xsl:variable>
  <xsl:variable name="localtype">
    <xsl:value-of select="TouchpointCompare/Messages/@localtype"/>
  </xsl:variable>
  <xsl:variable name="syncMultiWay">
    <xsl:value-of select="TouchpointCompare/Metadata/Settings/@syncMultiWay"/>
  </xsl:variable>
  
  <xsl:template match="/">
   <html>
  	<xsl:call-template name="ReportHead"/>
  	<body>
    <div class="sectionHeader"><a class="sectionHeaderPlainLink" name="contents"><xsl:value-of select="$message[@id='xslt_messages.contents']" /></a></div>
    
    <div class="sectionContent">
      <div class="singleLineItem"><xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <a href="#section_request_data"><xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a></div>
		<div class="singleLineItem"><xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <a href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.selected.objects']" /></a></div>
		<div class="singleLineItem"><xsl:value-of select="$message[@id='xslt_messages.section']" /> C: <a href="#section_touchpoint_data"><xsl:value-of select="$message[@id='xslt_messages.touchpoint.reference.data']" /></a></div>
    </div>
    
    <div class="sectionHeader">
      <a name="section_request_data" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
    </div>

    <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.request.overview']" /></div>

    <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.export.version']" />:</td>
          <td class="singleColumnValue"><xsl:value-of select="TouchpointCompare/@ver"/></td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested.by']" />:</td>
          <td class="singleColumnValue"><xsl:value-of select="TouchpointCompare/Metadata/User"/></td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.request.date']" />:</td>
          <td class="singleColumnValue">
            <xsl:variable name="requestdate">
              <xsl:call-template name="formdate">
                <xsl:with-param name="date" select="TouchpointCompare/Metadata/RequestDate"/>
              </xsl:call-template>
            </xsl:variable>
            <xsl:value-of select="$requestdate"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.current.touchpoint']" />:</td>
          <td class="singleColumnValue"><xsl:value-of select="TouchpointCompare/Metadata/Settings/@currentTPName"/></td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.direction']" />:</td>
          <td class="singleColumnValue">
          	<xsl:if test="$syncFromOrigin='true'">
          		<xsl:value-of select="$message[@id='xslt_messages.update']" /> - <xsl:value-of select="$message[@id='xslt_messages.sync.with.source']" />:<xsl:value-of select="TouchpointCompare/Metadata/Settings/@otherTPName"/>
          	</xsl:if>
          	<xsl:if test="$syncFromOrigin='false'"><xsl:value-of select="$message[@id='xslt_messages.commit']" /> - <xsl:value-of select="$message[@id='xslt_messages.sync.with.target']" />:<xsl:value-of select="TouchpointCompare/Metadata/Settings/@otherTPName"/></xsl:if>
          </td>
        </tr>
        <xsl:if test="$syncMultiWay='true'">
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.siblingparent.touchpoint']" />:</td>
          <td class="singleColumnValue"><xsl:value-of select="TouchpointCompare/Metadata/Settings/@siblingParentTPName"/></td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.parent.touchpoint']" />:</td>
          <td class="singleColumnValue"><xsl:value-of select="TouchpointCompare/Metadata/Settings/@parentTPName"/></td>
        </tr>
        </xsl:if>
      </table>
    </div>      
    
    <div class="sectionSubHeader"><a><xsl:value-of select="$message[@id='xslt_messages.touchpoint.selections']" />:</a>
      <xsl:apply-templates select="TouchpointCompare/ReferenceData/Touchpoints" mode="selectiontree"/>
    </div>
    <xsl:if test="count(TouchpointCompare/Messages/Message) > 0">
  		<div class="sectionSubHeader"><a name="messageIndex" href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.messages']" /></a></div>
  		<div class="sectionContent">
      		<table class="reportContentTable">
		        <tr>
		          <td class="tableHeader">
		            <xsl:value-of select="$message[@id='xslt_messages.ID']" />
		          </td>
		          <td class="tableHeader">
		            <xsl:value-of select="$message[@id='xslt_messages.ACTIVE']" />
		          </td>
		          <td class="tableHeader">
		            <xsl:value-of select="$message[@id='xslt_messages.WORKING.COPY']" />
		          </td>
		        </tr>
		        <xsl:for-each select="TouchpointCompare/Messages/Message">
		          <xsl:variable name="messageindex"><xsl:value-of select="@id"/></xsl:variable>
		          <tr>
		            <td><xsl:if test="string-length($messageindex)!=0"><xsl:value-of select="$messageindex"/></xsl:if></td>
		            <td>
		              <xsl:for-each select="Version">
		                <xsl:if test="@status!='Working Copy'">
		                  <a>
		                    <xsl:attribute name="href">#messageIndex_<xsl:number value="$messageindex" format="1" />_a</xsl:attribute>
		                    <xsl:value-of select="Name"/>
		                  </a>
		                </xsl:if>
		              </xsl:for-each>
		            </td>
		            <td>
		              <xsl:for-each select="Version">
		                <xsl:if test="@status='Working Copy'">
		                  <a>
		                    <xsl:attribute name="href">#messageIndex_<xsl:number value="$messageindex" format="1" />_w</xsl:attribute>
		                    <xsl:value-of select="Name"/>
		                  </a>
		                </xsl:if>
		              </xsl:for-each>
		            </td>
		          </tr>
		        </xsl:for-each>
        	</table>
        </div>
    </xsl:if>
	<xsl:if test="count(TouchpointCompare/Messages/LocalSmartText) > 0">
  		<div class="sectionSubHeader"><a name="messageIndex" href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.local.smart.texts']" /></a></div>
  		<div class="sectionContent">
      		<table class="reportContentTable">
		        <tr>
		          <td class="tableHeader">
		            <xsl:value-of select="$message[@id='xslt_messages.ID']" />
		          </td>
		          <td class="tableHeader">
		            <xsl:value-of select="$message[@id='xslt_messages.ACTIVE']" />
		          </td>
		          <td class="tableHeader">
		            <xsl:value-of select="$message[@id='xslt_messages.WORKING.COPY']" />
		          </td>
		        </tr>
		        <xsl:for-each select="TouchpointCompare/Messages/LocalSmartText">
		          <xsl:variable name="messageindex"><xsl:value-of select="@id"/></xsl:variable>
		          <tr>
		            <td><xsl:if test="string-length($messageindex)!=0"><xsl:value-of select="$messageindex"/></xsl:if></td>
		            <td>
		              <xsl:for-each select="Version">
		                <xsl:if test="@status!='Working Copy'">
		                  <a>
		                    <xsl:attribute name="href">#messageIndex_<xsl:number value="$messageindex" format="1" />_a</xsl:attribute>
		                    <xsl:value-of select="Name"/>
		                  </a>
		                </xsl:if>
		              </xsl:for-each>
		            </td>
		            <td>
		              <xsl:for-each select="Version">
		                <xsl:if test="@status='Working Copy'">
		                  <a>
		                    <xsl:attribute name="href">#messageIndex_<xsl:number value="$messageindex" format="1" />_w</xsl:attribute>
		                    <xsl:value-of select="Name"/>
		                  </a>
		                </xsl:if>
		              </xsl:for-each>
		            </td>
		          </tr>
		        </xsl:for-each>
        	</table>
        </div>
    </xsl:if>
   	
   	<xsl:if test="count(TouchpointCompare/Messages/LocalImageLibrary) > 0">
  		<div class="sectionSubHeader"><a name="messageIndex" href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.local.images']" /></a></div>
  		<div class="sectionContent">
      		<table class="reportContentTable">
		        <tr>
		          <td class="tableHeader">
		            <xsl:value-of select="$message[@id='xslt_messages.ID']" />
		          </td>
		          <td class="tableHeader">
		            <xsl:value-of select="$message[@id='xslt_messages.ACTIVE']" />
		          </td>
		          <td class="tableHeader">
		            <xsl:value-of select="$message[@id='xslt_messages.WORKING.COPY']" />
		          </td>
		        </tr>
		        <xsl:for-each select="TouchpointCompare/Messages/LocalImageLibrary">
		          <xsl:variable name="messageindex"><xsl:value-of select="@id"/></xsl:variable>
		          <tr>
		            <td><xsl:if test="string-length($messageindex)!=0"><xsl:value-of select="$messageindex"/></xsl:if></td>
		            <td>
		              <xsl:for-each select="Version">
		                <xsl:if test="@status!='Working Copy'">
		                  <a>
		                    <xsl:attribute name="href">#messageIndex_<xsl:number value="$messageindex" format="1" />_a</xsl:attribute>
		                    <xsl:value-of select="Name"/>
		                  </a>
		                </xsl:if>
		              </xsl:for-each>
		            </td>
		            <td>
		              <xsl:for-each select="Version">
		                <xsl:if test="@status='Working Copy'">
		                  <a>
		                    <xsl:attribute name="href">#messageIndex_<xsl:number value="$messageindex" format="1" />_w</xsl:attribute>
		                    <xsl:value-of select="Name"/>
		                  </a>
		                </xsl:if>
		              </xsl:for-each>
		            </td>
		          </tr>
		        </xsl:for-each>
        	</table>
        </div>
    </xsl:if>
   	<xsl:if test="count(TouchpointCompare/ImageLibraries/ImageLibrary) > 0">
   		<div class="sectionSubHeader"><a name="imagelibrary_index" href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.image.libraries']" /></a></div>
   		<div class="sectionContent">
      		<table class="reportContentTable">
		        <tr>
		          <td class="tableHeader">
		            <xsl:value-of select="$message[@id='xslt_messages.ID']" />
		          </td>
		          <td class="tableHeader">
		            <xsl:value-of select="$message[@id='xslt_messages.ACTIVE']" />
		          </td>
		          <td class="tableHeader">
		            <xsl:value-of select="$message[@id='xslt_messages.WORKING.COPY']" />
		          </td>
		        </tr>
		        	<xsl:for-each select="TouchpointCompare/ImageLibraries/ImageLibrary">
			          <xsl:variable name="messageindex"><xsl:value-of select="@id"/></xsl:variable>
			          <tr>
			            <td><xsl:if test="string-length($messageindex)!=0"><xsl:value-of select="$messageindex"/></xsl:if></td>
			            <td>
			              <xsl:for-each select="Version">
			                <xsl:if test="@status!='Working Copy'">
			                  <a>
			                    <xsl:attribute name="href">#imageLibraryIndex_<xsl:number value="$messageindex" format="1" />_a</xsl:attribute>
			                    <xsl:value-of select="Name"/>
			                  </a>
			                </xsl:if>
			              </xsl:for-each>
			            </td>
			            <td>
			              <xsl:for-each select="Version">
			                <xsl:if test="@status='Working Copy'">
			                  <a>
			                    <xsl:attribute name="href">#imageLibraryIndex_<xsl:number value="$messageindex" format="1" />_w</xsl:attribute>
			                    <xsl:value-of select="Name"/>
			                  </a>
			                </xsl:if>
			              </xsl:for-each>
			            </td>
			          </tr>
			        </xsl:for-each>
        	</table>
        </div>
   	</xsl:if>
   	
   	<xsl:if test="count(TouchpointCompare/SmartTexts/SmartText) > 0">
    		<div class="sectionSubHeader"><a name="smartTextIndex" href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.smart.texts']" /></a></div>
    		<div class="sectionContent">
	      		<table class="reportContentTable">
			        <tr>
			          <td class="tableHeader">
			            <xsl:value-of select="$message[@id='xslt_messages.ID']" />
			          </td>
			          <td class="tableHeader">
			            <xsl:value-of select="$message[@id='xslt_messages.ACTIVE']" />
			          </td>
			          <td class="tableHeader">
			            <xsl:value-of select="$message[@id='xslt_messages.WORKING.COPY']" />
			          </td>
			        </tr>
			        <xsl:for-each select="TouchpointCompare/SmartTexts/SmartText">
			          <xsl:variable name="messageindex"><xsl:value-of select="@id"/></xsl:variable>
			          <tr>
			            <td><xsl:if test="string-length($messageindex)!=0"><xsl:value-of select="$messageindex"/></xsl:if></td>
			            <td>
			              <xsl:for-each select="Version">
			                <xsl:if test="@status!='Working Copy'">
			                  <a>
			                    <xsl:attribute name="href">#smartTextIndex_<xsl:number value="$messageindex" format="1" />_a</xsl:attribute>
			                    <xsl:value-of select="Name"/>
			                  </a>
			                </xsl:if>
			              </xsl:for-each>
			            </td>
			            <td>
			              <xsl:for-each select="Version">
			                <xsl:if test="@status='Working Copy'">
			                  <a>
			                    <xsl:attribute name="href">#smartTextIndex_<xsl:number value="$messageindex" format="1" />_w</xsl:attribute>
			                    <xsl:value-of select="Name"/>
			                  </a>
			                </xsl:if>
			              </xsl:for-each>
            </td>
          </tr>
        </xsl:for-each>
	        	</table>
        	</div>
    	</xsl:if>

        <xsl:if test="count(TouchpointCompare/TargetGroups/TargetGroup) > 0">
            <div class="sectionSubHeader"><a name="targetGroupIndex" href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.target.groups']" /></a></div>
            <div class="sectionContent">
                <table class="reportContentTable">
                    <thead>
                        <tr>
                            <th class="tableHeader" width="30%">
                                <xsl:value-of select="$message[@id='xslt_messages.ID']" />
                            </th>
                            <th class="tableHeader" width="*">
                                <xsl:value-of select="$message[@id='xslt_messages.name']" />
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <xsl:for-each select="TouchpointCompare/TargetGroups/TargetGroup">
                            <xsl:variable name="messageindex"><xsl:value-of select="@id"/></xsl:variable>
                            <tr>
                                <td><xsl:if test="string-length($messageindex)!=0"><xsl:value-of select="$messageindex"/></xsl:if></td>
                                <td>
                                    <a>
                                        <xsl:attribute name="href">#targetGroupIndex_<xsl:number value="$messageindex" format="1" /></xsl:attribute>
                                        <xsl:value-of select="Name"/>
                                    </a>
                                </td>
                            </tr>
                        </xsl:for-each>
                    </tbody>
                </table>
            </div>
        </xsl:if>

        <xsl:if test="count(TouchpointCompare/TargetRules/TargetRule) > 0">
            <div class="sectionSubHeader"><a name="targetRuleIndex" href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.target.rules']" /></a></div>
            <div class="sectionContent">
                <table class="reportContentTable">
                    <thead>
                        <tr>
                            <th class="tableHeader" width="30%">
                                <xsl:value-of select="$message[@id='xslt_messages.ID']" />
                            </th>
                            <th class="tableHeader" width="*">
                                <xsl:value-of select="$message[@id='xslt_messages.name']" />
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <xsl:for-each select="TouchpointCompare/TargetRules/TargetRule">
                            <xsl:variable name="messageindex"><xsl:value-of select="@id"/></xsl:variable>
                            <tr>
                                <td><xsl:if test="string-length($messageindex)!=0"><xsl:value-of select="$messageindex"/></xsl:if></td>
                                <td>
                                    <a>
                                        <xsl:attribute name="href">#targetRuleIndex_<xsl:number value="$messageindex" format="1" /></xsl:attribute>
                                        <xsl:value-of select="Name"/>
                                    </a>
                                </td>
                            </tr>
                        </xsl:for-each>
                    </tbody>
                </table>
            </div>
        </xsl:if>

        <xsl:if test="count(TouchpointCompare/SelectorGroups/SelectorGroup) > 0">
            <div class="sectionSubHeader"><a name="selectorGroupIndex" href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.parameter.groups']" /></a></div>
            <div class="sectionContent">
                <table class="reportContentTable">
                    <thead>
                        <tr>
                            <th class="tableHeader" width="30%">
                                <xsl:value-of select="$message[@id='xslt_messages.ID']" />
                            </th>
                            <th class="tableHeader" width="*">
                                <xsl:value-of select="$message[@id='xslt_messages.name']" />
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <xsl:for-each select="TouchpointCompare/SelectorGroups/SelectorGroup">
                            <xsl:variable name="messageindex"><xsl:value-of select="@id"/></xsl:variable>
                            <tr>
                                <td><xsl:if test="string-length($messageindex)!=0"><xsl:value-of select="$messageindex"/></xsl:if></td>
                                <td>
                                    <a>
                                        <xsl:attribute name="href">#selectorGroupIndex_<xsl:number value="$messageindex" format="1" /></xsl:attribute>
                                        <xsl:value-of select="Name"/>
                                    </a>
                                </td>
                            </tr>
                        </xsl:for-each>
                    </tbody>
                </table>
            </div>
        </xsl:if>

        <xsl:if test="count(TouchpointCompare/DataCollections/DataCollection) > 0">
            <div class="sectionSubHeader"><a name="dataCollectionIndex" href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.data.collections']" /></a></div>
            <div class="sectionContent">
                <table class="reportContentTable">
                    <thead>
                        <tr>
                            <th class="tableHeader" width="30%">
                                <xsl:value-of select="$message[@id='xslt_messages.ID']" />
                            </th>
                            <th class="tableHeader" width="*">
                                <xsl:value-of select="$message[@id='xslt_messages.name']" />
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <xsl:for-each select="TouchpointCompare/DataCollections/DataCollection">
                            <xsl:variable name="messageindex"><xsl:value-of select="@id"/></xsl:variable>
                            <tr>
                                <td><xsl:if test="string-length($messageindex)!=0"><xsl:value-of select="$messageindex"/></xsl:if></td>
                                <td>
                                    <a>
                                        <xsl:attribute name="href">#dataCollectionIndex_<xsl:number value="$messageindex" format="1" /></xsl:attribute>
                                        <xsl:value-of select="Name"/>
                                    </a>
                                </td>
                            </tr>
                        </xsl:for-each>
                    </tbody>
                </table>
            </div>
        </xsl:if>

        <xsl:if test="count(TouchpointCompare/Variables/Variable) > 0">
            <div class="sectionSubHeader"><a name="variableIndex" href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.variables']" /></a></div>
            <div class="sectionContent">
                <table class="reportContentTable">
                    <thead>
                        <tr>
                            <th class="tableHeader" width="30%">
                                <xsl:value-of select="$message[@id='xslt_messages.ID']" />
                            </th>
                            <th class="tableHeader" width="*">
                                <xsl:value-of select="$message[@id='xslt_messages.name']" />
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <xsl:for-each select="TouchpointCompare/Variables/Variable">
                            <xsl:variable name="messageindex"><xsl:value-of select="@id"/></xsl:variable>
                            <tr>
                                <td><xsl:if test="string-length($messageindex)!=0"><xsl:value-of select="$messageindex"/></xsl:if></td>
                                <td>
                                    <a>
                                        <xsl:attribute name="href">#variableIndex_<xsl:number value="$messageindex" format="1" /></xsl:attribute>
                                        <xsl:value-of select="Name"/>
                                    </a>
                                </td>
                            </tr>
                        </xsl:for-each>
                    </tbody>
                </table>
            </div>
        </xsl:if>

        <xsl:if test="count(TouchpointCompare/DataSources/DataSource) > 0">
            <div class="sectionSubHeader"><a name="dataSourceIndex" href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.data.sources']" /></a></div>
            <div class="sectionContent">
                <table class="reportContentTable">
                    <thead>
                        <tr>
                            <th class="tableHeader" width="30%">
                                <xsl:value-of select="$message[@id='xslt_messages.ID']" />
                            </th>
                            <th class="tableHeader" width="*">
                                <xsl:value-of select="$message[@id='xslt_messages.name']" />
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <xsl:for-each select="TouchpointCompare/DataSources/DataSource">
                            <xsl:variable name="messageindex"><xsl:value-of select="@id"/></xsl:variable>
                            <tr>
                                <td><xsl:if test="string-length($messageindex)!=0"><xsl:value-of select="$messageindex"/></xsl:if></td>
                                <td>
                                    <a>
                                        <xsl:attribute name="href">#dataSourceIndex_<xsl:number value="$messageindex" format="1" /></xsl:attribute>
                                        <xsl:value-of select="Name"/>
                                    </a>
                                </td>
                            </tr>
                        </xsl:for-each>
                    </tbody>
                </table>
            </div>
        </xsl:if>

        <xsl:if test="count(TouchpointCompare/LookupTables/LookupTable) > 0">
            <div class="sectionSubHeader"><a name="lookupTableIndex" href="#section_messages"><xsl:value-of select="$message[@id='xslt_messages.lookup.tables']" /></a></div>
            <div class="sectionContent">
                <table class="reportContentTable">
                    <thead>
                        <tr>
                            <th class="tableHeader" width="30%">
                                <xsl:value-of select="$message[@id='xslt_messages.ID']" />
                            </th>
                            <th class="tableHeader" width="*">
                                <xsl:value-of select="$message[@id='xslt_messages.name']" />
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <xsl:for-each select="TouchpointCompare/LookupTables/LookupTable">
                            <xsl:variable name="messageindex"><xsl:value-of select="@id"/></xsl:variable>
                            <tr>
                                <td><xsl:if test="string-length($messageindex)!=0"><xsl:value-of select="$messageindex"/></xsl:if></td>
                                <td>
                                    <a>
                                        <xsl:attribute name="href">#lookupTableIndex_<xsl:number value="$messageindex" format="1" /></xsl:attribute>
                                        <xsl:value-of select="Name"/>
                                    </a>
                                </td>
                            </tr>
                        </xsl:for-each>
                    </tbody>
                </table>
            </div>
        </xsl:if>

        <xsl:variable name="selectionid">
        <xsl:value-of select="/TouchpointCompare/Metadata/Selection/@refid"/>
      </xsl:variable>

      <xsl:variable name="selectionname">
        <xsl:if test="string-length($selectionid) > 0">
          (<xsl:value-of select="key('selectionkey', $selectionid)/Name"/>)
        </xsl:if>
      </xsl:variable>
    
    <div class="sectionHeader">
      <a name="section_messages" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> B: 
      	<xsl:value-of select="$message[@id='xslt_messages.selected.objects']" /></a>
   	</div>
   	
   	<xsl:if test="count(TouchpointCompare/Messages/Message) > 0">
   		<div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.messages']" /></div>
   		<div class="sectionContent">
      	<xsl:for-each select="TouchpointCompare/Messages/Message">
	      <xsl:if test="@type!='Selectable(Touchpoint)'">
	        <xsl:apply-templates select="."/>
	      </xsl:if>
	    </xsl:for-each>
      	</div>
    </xsl:if>
    <xsl:if test="count(TouchpointCompare/Messages/LocalSmartText) > 0">
   		<div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.local.smart.texts']" /></div>
   		<div class="sectionContent">
      		<xsl:for-each select="TouchpointCompare/Messages/LocalSmartText">
	    	<xsl:if test="@type!='Selectable(Touchpoint)'">
	       		<xsl:apply-templates select="."/>
	     	</xsl:if>
	   	</xsl:for-each>
      	</div>
    </xsl:if>
    <xsl:if test="count(TouchpointCompare/Messages/LocalImageLibrary) > 0">
   		<div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.local.images']" /></div>
   		<div class="sectionContent">
      		<xsl:for-each select="TouchpointCompare/Messages/LocalImageLibrary">
	    	<xsl:if test="@type!='Selectable(Touchpoint)'">
	       		<xsl:apply-templates select="."/>
	     	</xsl:if>
	   	</xsl:for-each>
      	</div>
    </xsl:if>
    <xsl:if test="count(TouchpointCompare/ImageLibraries/ImageLibrary) > 0">
   		<div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.image.libraries']" /></div>
   		<div class="sectionContent">
      		<xsl:apply-templates select="TouchpointCompare/ImageLibraries"/>
      	</div>
    </xsl:if>
    <xsl:if test="count(TouchpointCompare/SmartTexts/SmartText) > 0">
   		<div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.smart.texts']" /></div>
   		<div class="sectionContent">
			<xsl:apply-templates select="TouchpointCompare/SmartTexts"/>      	
		</div>
    </xsl:if>

        <xsl:if test="count(TouchpointCompare/TargetGroups/TargetGroup) > 0">
            <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.target.groups']" /></div>
            <div class="sectionContent">
                <xsl:for-each select="TouchpointCompare/TargetGroups/TargetGroup">
                    <xsl:apply-templates select="."/>
                </xsl:for-each>
            </div>
        </xsl:if>

        <xsl:if test="count(TouchpointCompare/TargetRules/TargetRule) > 0">
            <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.target.rules']" /></div>
            <div class="sectionContent">
                <xsl:for-each select="TouchpointCompare/TargetRules/TargetRule">
                    <xsl:apply-templates select="."/>
                </xsl:for-each>
            </div>
        </xsl:if>

        <xsl:if test="count(TouchpointCompare/SelectorGroups/SelectorGroup) > 0">
            <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.parameter.groups']" /></div>
            <div class="sectionContent">
                <xsl:for-each select="TouchpointCompare/SelectorGroups/SelectorGroup">
                    <xsl:apply-templates select="."/>
                </xsl:for-each>
            </div>
        </xsl:if>

        <xsl:if test="count(TouchpointCompare/DataCollections/DataCollection) > 0">
            <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.data.collections']" /></div>
            <div class="sectionContent">
                <xsl:for-each select="TouchpointCompare/DataCollections/DataCollection">
                    <xsl:apply-templates select="."/>
                </xsl:for-each>
            </div>
        </xsl:if>

        <xsl:if test="count(TouchpointCompare/Variables/Variable) > 0">
            <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.variables']" /></div>
            <div class="sectionContent">
                <xsl:for-each select="TouchpointCompare/Variables/Variable">
                    <xsl:apply-templates select="."/>
                </xsl:for-each>
            </div>
        </xsl:if>

        <xsl:if test="count(TouchpointCompare/DataSources/DataSource) > 0">
            <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.data.sources']" /></div>
            <div class="sectionContent">
                <xsl:for-each select="TouchpointCompare/DataSources/DataSource">
                    <xsl:apply-templates select="."/>
                </xsl:for-each>
            </div>
        </xsl:if>

        <xsl:if test="count(TouchpointCompare/LookupTables/LookupTable) > 0">
            <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.lookup.tables']" /></div>
            <div class="sectionContent">
                <xsl:for-each select="TouchpointCompare/LookupTables/LookupTable">
                    <xsl:apply-templates select="."/>
                </xsl:for-each>
            </div>
        </xsl:if>

        <xsl:if test="$localtype=''">
	    <div class="sectionHeader">
	      <a name="section_touchpoint_data" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> C: <xsl:value-of select="$message[@id='xslt_messages.touchpoint.reference.data']" /></a>
	      <xsl:apply-templates select="TouchpointCompare/ReferenceData/Touchpoints"/>
	    </div>
    </xsl:if>
    
    <xsl:if test="count(TouchpointCompare/ReferenceData/ImageLibraries/ImageLibrary) > 0">
      <div class="sectionHeader">
        <a name="section_image_library_data" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> D: <xsl:value-of select="$message[@id='xslt_messages.image.library.reference.data']" /></a>
        <xsl:apply-templates select="TouchpointCompare/ReferenceData/ImageLibraries"/>
      </div>
    </xsl:if>

    </body>
    </html>
  </xsl:template>

    <xsl:template match="TouchpointCompare/TargetGroups/TargetGroup">
        <xsl:variable name="messageindex">
            <xsl:value-of select="position()"/>
        </xsl:variable>
        <xsl:variable name="targetgroupid">
            <xsl:value-of select="@id"/>
        </xsl:variable>
        <xsl:variable name="name">
            <xsl:value-of select="Name"/>
        </xsl:variable>
        <table class="reportContentTable">
            <tr>
                <td class="doubleColumnLabel"><a>
                    <xsl:attribute name="class">plainLink</xsl:attribute>
                    <xsl:attribute name="href">#targetGroupIndex</xsl:attribute>
                    <xsl:attribute name="name">targetGroupIndex_<xsl:number value="$targetgroupid" format="1" />
                    </xsl:attribute>
                    <xsl:value-of select="Name"/>
                </a>
                </td>
                <td class="doubleColumnValue">
                    <div class="contentContainer">
                        <xsl:value-of select="$message[@id='xslt_messages.details']" />:
                        <xsl:value-of disable-output-escaping="yes" select="Details"/>
                    </div>
                </td>
            </tr>
        </table>
    </xsl:template>

    <xsl:template match="TouchpointCompare/TargetRules/TargetRule">
        <xsl:variable name="messageindex">
            <xsl:value-of select="position()"/>
        </xsl:variable>
        <xsl:variable name="targetruleid">
            <xsl:value-of select="@id"/>
        </xsl:variable>
        <xsl:variable name="name">
            <xsl:value-of select="Name"/>
        </xsl:variable>
        <table class="reportContentTable">
            <tr>
                <td class="doubleColumnLabel"><a>
                    <xsl:attribute name="class">plainLink</xsl:attribute>
                    <xsl:attribute name="href">#targetRuleIndex</xsl:attribute>
                    <xsl:attribute name="name">targetRuleIndex_<xsl:number value="$targetruleid" format="1" />
                    </xsl:attribute>
                    <xsl:value-of select="Name"/>
                </a>
                </td>

                <td class="doubleColumnValue">
                    <div class="contentContainer">
                        <xsl:value-of select="$message[@id='xslt_messages.search.value']" />:
                        <xsl:value-of select="SearchValue"/>
                    </div>
                </td>
            </tr>
        </table>
    </xsl:template>

    <xsl:template match="TouchpointCompare/SelectorGroups/SelectorGroup">
        <xsl:variable name="messageindex">
            <xsl:value-of select="position()"/>
        </xsl:variable>
        <xsl:variable name="selectorgroupid">
            <xsl:value-of select="@id"/>
        </xsl:variable>
        <xsl:variable name="name">
            <xsl:value-of select="Name"/>
        </xsl:variable>
        <table class="reportContentTable">
            <tr>
                <td class="doubleColumnLabel"><a>
                    <xsl:attribute name="class">plainLink</xsl:attribute>
                    <xsl:attribute name="href">#selectorGroupIndex</xsl:attribute>
                    <xsl:attribute name="name">selectorGroupIndex_<xsl:number value="$selectorgroupid" format="1" />
                    </xsl:attribute>
                    <xsl:value-of select="Name"/>
                </a>
                </td>

                <td class="doubleColumnValue">
                    <div class="contentContainer">
                        <xsl:value-of select="$message[@id='xslt_messages.selectors']" />:
                        <xsl:for-each select="Selectors/Selector">
                            <xsl:if test="position()>1">, </xsl:if>
                            <xsl:value-of select="Name"/>
                        </xsl:for-each>
                    </div>
                </td>
            </tr>
        </table>
    </xsl:template>

    <xsl:template match="TouchpointCompare/DataCollections/DataCollection">
        <xsl:variable name="messageindex">
            <xsl:value-of select="position()"/>
        </xsl:variable>
        <xsl:variable name="datacollectionid">
            <xsl:value-of select="@id"/>
        </xsl:variable>
        <xsl:variable name="name">
            <xsl:value-of select="Name"/>
        </xsl:variable>
        <table class="reportContentTable">
            <tr>
                <td class="doubleColumnLabel"><a>
                    <xsl:attribute name="class">plainLink</xsl:attribute>
                    <xsl:attribute name="href">#dataCollectionIndex</xsl:attribute>
                    <xsl:attribute name="name">dataCollectionIndex_<xsl:number value="$datacollectionid" format="1" />
                    </xsl:attribute>
                    <xsl:value-of select="Name"/>
                </a>
                </td>
                <td class="doubleColumnValue">
                    <div class="contentContainer">
                        <xsl:value-of select="$message[@id='xslt_messages.primary.data.source']" />:<xsl:value-of select="PrimaryDataSource"/>
                    </div>
                </td>
            </tr>
        </table>
    </xsl:template>

    <xsl:template match="TouchpointCompare/Variables/Variable">
        <xsl:variable name="messageindex">
            <xsl:value-of select="position()"/>
        </xsl:variable>
        <xsl:variable name="variableid">
            <xsl:value-of select="@id"/>
        </xsl:variable>
        <xsl:variable name="name">
            <xsl:value-of select="Name"/>
        </xsl:variable>
        <table class="reportContentTable">
            <tr>
                <td class="doubleColumnLabel"><a>
                    <xsl:attribute name="class">plainLink</xsl:attribute>
                    <xsl:attribute name="href">#variableIndex</xsl:attribute>
                    <xsl:attribute name="name">variableIndex_<xsl:number value="$variableid" format="1" />
                    </xsl:attribute>
                    <xsl:value-of select="Name"/>
                </a>
                </td>

                <td class="doubleColumnValue">
                    <div class="contentContainer">
                        <xsl:value-of select="$message[@id='xslt_messages.variable.type']" />:<xsl:value-of select="VariableType"/>,
                        <xsl:value-of select="$message[@id='xslt_messages.data.type']" />:<xsl:value-of select="DataType"/>
                    </div>
                </td>
            </tr>
        </table>
    </xsl:template>

    <xsl:template match="TouchpointCompare/DataSources/DataSource">
        <xsl:variable name="messageindex">
            <xsl:value-of select="position()"/>
        </xsl:variable>
        <xsl:variable name="datasourceid">
            <xsl:value-of select="@id"/>
        </xsl:variable>
        <xsl:variable name="name">
            <xsl:value-of select="Name"/>
        </xsl:variable>
        <table class="reportContentTable">
            <tr>
                <td class="doubleColumnLabel"><a>
                    <xsl:attribute name="class">plainLink</xsl:attribute>
                    <xsl:attribute name="href">#dataSourceIndex</xsl:attribute>
                    <xsl:attribute name="name">dataSourceIndex_<xsl:number value="$datasourceid" format="1" />
                    </xsl:attribute>
                    <xsl:value-of select="Name"/>
                </a>
                </td>

                <td class="doubleColumnValue">
                    <div class="contentContainer">
                        <xsl:value-of select="$message[@id='xslt_messages.source.type']" />:<xsl:value-of select="SourceType"/>,
                        <xsl:value-of select="$message[@id='xslt_messages.layout.type']" />:<xsl:value-of select="LayoutType"/>
                    </div>
                </td>
            </tr>
        </table>
    </xsl:template>

    <xsl:template match="TouchpointCompare/LookupTables/LookupTable">
        <xsl:variable name="messageindex">
            <xsl:value-of select="position()"/>
        </xsl:variable>
        <xsl:variable name="lookuptableid">
            <xsl:value-of select="@id"/>
        </xsl:variable>
        <xsl:variable name="name">
            <xsl:value-of select="Name"/>
        </xsl:variable>
        <table class="reportContentTable">
            <tr>
                <td class="doubleColumnLabel"><a>
                    <xsl:attribute name="class">plainLink</xsl:attribute>
                    <xsl:attribute name="href">#lookupTableIndex</xsl:attribute>
                    <xsl:attribute name="name">lookupTableIndex_<xsl:number value="$lookuptableid" format="1" />
                    </xsl:attribute>
                    <xsl:value-of select="Name"/>
                </a>
                </td>

                <td class="doubleColumnValue">
                    <div class="contentContainer">
                        <xsl:value-of select="$message[@id='xslt_messages.lookup.table.delimiter']" />:
                        '<xsl:value-of select="Delimiter"/>',
                        <xsl:value-of select="$message[@id='xslt_messages.lookup.table.input.character.encoding']" />:
                        <xsl:value-of select="InputCharacterEncoding"/>
                    </div>
                </td>
            </tr>
        </table>
    </xsl:template>

    <!-- Image Library Templates below -->
  <xsl:template match="TouchpointCompare/ImageLibraries/ImageLibrary">
    <xsl:param name="suppressed">false</xsl:param>
    <xsl:variable name="messageindex">
      <xsl:value-of select="position()"/>
    </xsl:variable>
    <xsl:for-each select="Version">
      <xsl:variable name="versiontype">
        <xsl:if test="@status='Working Copy'">
          <xsl:value-of select="'_w'"/>
        </xsl:if>
        <xsl:if test="@status!='Working Copy'">
          <xsl:value-of select="'_a'"/>
        </xsl:if>
      </xsl:variable>
      <xsl:variable name="imageid">
        <xsl:value-of select="../@id"/>
      </xsl:variable>
      <xsl:variable name="imagetype">
        <xsl:value-of select="../@type"/>
      </xsl:variable>
      <!-- Detail START -->
      
      <table class="reportContentTable">
        <tr>
          <td class="doubleColumnLabel"><a>
          <xsl:attribute name="class">plainLink</xsl:attribute>
          <xsl:attribute name="name">imageLibraryIndex_<xsl:number value="$imageid" format="1" /><xsl:value-of select="$versiontype"/></xsl:attribute>
          <xsl:attribute name="href">#imageLibraryIndex<xsl:value-of select="$versiontype"/></xsl:attribute>
          <xsl:value-of select="Name"/> <xsl:if test="@status='Working Copy'"> (<xsl:value-of select="$message[@id='xslt_messages.working.copy']" />)</xsl:if><xsl:if test="@status!='Working Copy'"> (<xsl:value-of select="$message[@id='xslt_messages.active']" />)</xsl:if>
        	</a>
          </td>
          <td class="doubleColumnValue">
          	<div class="contentContainer">
          		<xsl:value-of select="$message[@id='xslt_messages.content.type']" />:
           		<xsl:variable name="contenttype">
             		<xsl:call-template name="capitalize">
               	<xsl:with-param name="string" select="../@contenttype"/>
             		</xsl:call-template>
            	</xsl:variable>
            	<xsl:value-of select="$contenttype"/>, 
          		<xsl:value-of select="$message[@id='xslt_messages.type']" />:
          		<xsl:variable name="type">
	              <xsl:call-template name="capitalize">
	                <xsl:with-param name="string" select="../@type"/>
	              </xsl:call-template>
	            </xsl:variable>
	            <xsl:value-of select="$type"/>
            </div>
          </td>
        </tr>
      </table>
      <table class="reportContentTable">
          <xsl:if test="( (Content) or (Contents) or (Selections) )">
            <tr>
              <xsl:variable name="backhref">messageSelectionIndex_<xsl:value-of select="$imageid"/></xsl:variable>
              <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.content']" />:</td>
              <td class="singleColumnValue">
                <xsl:if test="$imagetype='Dynamic'">
                  <div>
                    <b>
                      <a class="plainLink">
                        <xsl:attribute name="name"><xsl:value-of select="$backhref"/></xsl:attribute>
                        <xsl:value-of select="$message[@id='xslt_messages.variants']" />
                      </a>
                    </b>
                  </div>
                </xsl:if>
                <xsl:choose>
                  <xsl:when test="$suppressed='true'">
                    <div class="contentContainer">
                      <i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i>
                    </div>
                  </xsl:when>
                  <xsl:otherwise>
                    <xsl:apply-templates mode="TreeView" select="Selections/Selection">
                      <xsl:with-param name="imageid" select="$imageid"/>
                    </xsl:apply-templates>
                    <xsl:for-each select="Contents/Content">
                      <div>
                        <xsl:variable name="locale">
                          <xsl:call-template name="translatelang">
                            <xsl:with-param name="code" select="@locale"/>
                          </xsl:call-template>
                        </xsl:variable>
                        <xsl:value-of select="$locale"/>
                      </div>

                      <xsl:call-template name="imagedata">
                        <xsl:with-param name="imagename" select="@imagename"/>
                        <xsl:with-param name="uploaded" select="@uploaded"/>
                      </xsl:call-template>

                      <div class="contentContainer">
			            <xsl:call-template name="imagelibraryref">
			              <xsl:with-param name="imagelibraryid" select="@imagelibraryrefid"/>
			            </xsl:call-template>
			                                  
                        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
                        <xsl:value-of disable-output-escaping="yes" select="." />
                      </div>
                    </xsl:for-each>
                    <xsl:apply-templates mode="imagelibrary" select="Selections/Selection">
                      <xsl:with-param name="backhref" select="$backhref"/>
                      <xsl:with-param name="imagelibraryid" select="$imageid"/>
                    </xsl:apply-templates>
                  </xsl:otherwise>
                </xsl:choose>
              </td>
            </tr>
          </xsl:if>
        </table>
      <!-- Image library detail END -->
    </xsl:for-each>
  </xsl:template>
  
  <!-- Smart Text Templates below -->
  <xsl:template match="TouchpointCompare/SmartTexts/SmartText">
    <xsl:param name="suppressed">false</xsl:param>
    <xsl:variable name="messageindex">
      <xsl:value-of select="position()"/>
    </xsl:variable>
    
    <xsl:for-each select="Version">
      <xsl:variable name="versiontype">
        <xsl:if test="@status='Working Copy'">
          <xsl:value-of select="'_w'"/>
        </xsl:if>
        <xsl:if test="@status!='Working Copy'">
          <xsl:value-of select="'_a'"/>
        </xsl:if>
      </xsl:variable>
      <xsl:variable name="textid">
        <xsl:value-of select="../@id"/>
      </xsl:variable>
      <xsl:variable name="imagetype">
        <xsl:value-of select="../@type"/>
      </xsl:variable>
      <!-- Detail START -->
      <table class="reportContentTable">
        <tr>
          <td class="doubleColumnLabel"><a>
          <xsl:attribute name="class">plainLink</xsl:attribute>
          <xsl:attribute name="name">smartTextIndex_<xsl:number value="$textid" format="1" /><xsl:value-of select="$versiontype"/></xsl:attribute>
          <xsl:attribute name="href">#smartTextIndex</xsl:attribute>
          <xsl:value-of select="Name"/> <xsl:if test="@status='Working Copy'"> (<xsl:value-of select="$message[@id='xslt_messages.working.copy']" />)</xsl:if><xsl:if test="@status!='Working Copy'"> (<xsl:value-of select="$message[@id='xslt_messages.active']" />)</xsl:if>
        </a>
          </td>
          <td class="doubleColumnValue">
          	<div class="contentContainer">
          		<xsl:value-of select="$message[@id='xslt_messages.content.type']" />:
           		<xsl:variable name="contenttype">
             		<xsl:call-template name="capitalize">
               	<xsl:with-param name="string" select="../@contenttype"/>
             		</xsl:call-template>
            	</xsl:variable>
            	<xsl:value-of select="$contenttype"/>, 
          		<xsl:value-of select="$message[@id='xslt_messages.type']" />:
          		<xsl:variable name="type">
	              <xsl:call-template name="capitalize">
	                <xsl:with-param name="string" select="../@type"/>
	              </xsl:call-template>
	            </xsl:variable>
	            <xsl:value-of select="$type"/>
            </div>
          </td>
        </tr>
      </table>
        <table class="reportContentTable">
          <xsl:if test="( (Content) or (Contents) or (Selections) )">
            <tr>
              <xsl:variable name="backhref">messageSelectionIndex_<xsl:value-of select="$textid"/></xsl:variable>
              <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.content']" />:</td>
              <td class="singleColumnValue">
                <xsl:if test="$imagetype='Dynamic'">
                  <div>
                    <b>
                      <a class="plainLink">
                        <xsl:attribute name="name"><xsl:value-of select="$backhref"/></xsl:attribute>
                        <xsl:value-of select="$message[@id='xslt_messages.variants']" />
                      </a>
                    </b>
                  </div>
                </xsl:if>
                <xsl:choose>
                  <xsl:when test="$suppressed='true'">
                    <div class="contentContainer">
                      <i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i>
                    </div>
                  </xsl:when>
                  <xsl:otherwise>
                    <xsl:apply-templates mode="TreeView" select="Selections/Selection">
                      <xsl:with-param name="textid" select="$textid"/>
                    </xsl:apply-templates>
                    <xsl:for-each select="Contents/Content">
                      <div>
                        <xsl:variable name="locale">
                          <xsl:call-template name="translatelang">
                            <xsl:with-param name="code" select="@locale"/>
                          </xsl:call-template>
                        </xsl:variable>
                        <xsl:value-of select="$locale"/>
                      </div>

                      <xsl:call-template name="imagedata">
                        <xsl:with-param name="imagename" select="@imagename"/>
                        <xsl:with-param name="uploaded" select="@uploaded"/>
                      </xsl:call-template>

                      <div class="contentContainer">
			            <xsl:call-template name="imagelibraryref">
			              <xsl:with-param name="imagelibraryid" select="@imagelibraryrefid"/>
			            </xsl:call-template>
			                                  
                        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
                        <xsl:value-of disable-output-escaping="yes" select="." />
                      </div>
                    </xsl:for-each>
                    <xsl:apply-templates mode="imagelibrary" select="Selections/Selection">
                      <xsl:with-param name="backhref" select="$backhref"/>
                      <xsl:with-param name="imagelibraryid" select="$textid"/>
                    </xsl:apply-templates>
                  </xsl:otherwise>
                </xsl:choose>
              </td>
            </tr>
          </xsl:if>
        </table>
      <!-- Smart Text detail END -->
    </xsl:for-each>
  </xsl:template>
  
  <!--  Write out selection tree -->
  <xsl:template mode="TreeView" match="Selection">
    <xsl:param name="contentObjectId"/>
    <xsl:param name="imagelibraryid"/>
    <xsl:variable name="selectId">
      <xsl:choose>
        <xsl:when test="Name='Default'">
          <xsl:value-of select="'def'"/>
        </xsl:when>
        <xsl:when test="Name='Master'">
          <xsl:value-of select="'def'"/>
        </xsl:when>
        <xsl:otherwise>
          <xsl:value-of select="@id"/>
        </xsl:otherwise>
      </xsl:choose>
    </xsl:variable>
    <ul>
	   <li>
	     <a>
	       <xsl:choose>
	         <xsl:when test="string-length($contentObjectId) > 0">
	           <xsl:attribute name="href">#messageSelectionContent_<xsl:value-of select="$contentObjectId"/>_<xsl:value-of select="$selectId"/></xsl:attribute>
	         </xsl:when>
	         <xsl:otherwise>
	           <xsl:attribute name="href">#imageLibrarySelectionContent_<xsl:value-of select="$imagelibraryid"/>_<xsl:value-of select="$selectId"/></xsl:attribute>
	         </xsl:otherwise>
	       </xsl:choose>
	       <xsl:value-of select="Name"/>
	     </a>
        <xsl:for-each select="Selection">
          <xsl:sort/>
          <xsl:choose>
            <xsl:when test="string-length($contentObjectId) > 0">
              <xsl:apply-templates mode="TreeView" select=".">
                <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
              </xsl:apply-templates> 
            </xsl:when>
            <xsl:otherwise>
              <xsl:apply-templates mode="TreeView" select=".">
                <xsl:with-param name="imagelibraryid" select="$imagelibraryid"/>
              </xsl:apply-templates>            
            </xsl:otherwise>         
          </xsl:choose>
        </xsl:for-each>
      </li>
    </ul>
  </xsl:template>

  <!-- Write out selectable message content -->
  <xsl:template match="Selection" mode="singlepart">
    <xsl:param name="backhref"/>
    <xsl:param name="contentObjectId"/>
    
    <xsl:variable name="selectname">
      <xsl:value-of select="Name"/>
    </xsl:variable>
    <xsl:variable name="selectionId">
      <xsl:choose>
        <xsl:when test="Name='Master'">
          <xsl:value-of select="'def'"/>
        </xsl:when>
        <xsl:when test="Name='Default'">
          <xsl:value-of select="'def'"/>
        </xsl:when>
        <xsl:otherwise>
          <xsl:value-of select="@id"/>
        </xsl:otherwise>
      </xsl:choose>
    </xsl:variable>

    <div>
      <br/>
      <b>
        <a class="plainLink">
          <xsl:attribute name="name">messageSelectionContent_<xsl:value-of select="$contentObjectId"/>_<xsl:value-of select="$selectionId"/></xsl:attribute>
          <xsl:attribute name="href">#<xsl:value-of select="$backhref"/></xsl:attribute>
          <xsl:value-of select="$selectname"/>
          <xsl:choose>
            <xsl:when test="@status='WIP'">
              (<xsl:value-of select="$message[@id='xslt_messages.working.copy']" />)
            </xsl:when>
            <xsl:when test="@status='Active'">
              (<xsl:value-of select="$message[@id='xslt_messages.active']" />)
            </xsl:when>
          </xsl:choose>
        </a>
      </b>
    </div>
    <xsl:for-each select="Contents/Content">
      <div>
        <xsl:variable name="locale">
          <xsl:call-template name="translatelang">
            <xsl:with-param name="code" select="@locale"/>
          </xsl:call-template>
        </xsl:variable>
        <xsl:value-of select="$locale"/>
      </div>
      <xsl:call-template name="imagedata">
        <xsl:with-param name="imagename" select="@imagename"/>
        <xsl:with-param name="uploaded" select="@uploaded"/>
      </xsl:call-template>
      <div class="contentContainer">
        <xsl:call-template name="imagelibraryref">
          <xsl:with-param name="imagelibraryid" select="@imagelibraryid"/>
        </xsl:call-template>
        <xsl:call-template name="imagelibraryToref">
          <xsl:with-param name="imagelibraryid" select="@imagelibrarytoid"/>
        </xsl:call-template>        
        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
        <xsl:if test="@sameasparent='true'"><i><xsl:value-of select="$message[@id='xslt_messages.inherited']" /></i></xsl:if>
        <xsl:if test="@suppress='true'"><i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i></xsl:if>
        <xsl:if test="@empty='true'"><i><xsl:value-of select="$message[@id='xslt_messages.empty.part']" /></i></xsl:if>
        <xsl:value-of disable-output-escaping="yes" select="." />
      </div>
    </xsl:for-each>

    <xsl:apply-templates select="Selection" mode="singlepart">
      <xsl:with-param name="backhref" select="$backhref"/>
      <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
    </xsl:apply-templates>
  </xsl:template>

  <!-- Write out selectable image library content -->
  <xsl:template match="Selection" mode="imagelibrary">
    <xsl:param name="backhref"/>
    <xsl:param name="imagelibraryid"/>
    
    <xsl:variable name="selectname">
      <xsl:value-of select="Name"/>
    </xsl:variable>
    <xsl:variable name="selectionId">
      <xsl:choose>
        <xsl:when test="Name='Master'">
          <xsl:value-of select="'def'"/>
        </xsl:when>
        <xsl:when test="Name='Default'">
          <xsl:value-of select="'def'"/>
        </xsl:when>
        <xsl:otherwise>
          <xsl:value-of select="@id"/>
        </xsl:otherwise>
      </xsl:choose>
    </xsl:variable>

    <div>
      <br/>
      <b>
        <a class="plainLink">
          <xsl:attribute name="name">imageLibrarySelectionContent_<xsl:value-of select="$imagelibraryid"/>_<xsl:value-of select="$selectionId"/></xsl:attribute>
          <xsl:attribute name="href">#<xsl:value-of select="$backhref"/></xsl:attribute>
          <xsl:value-of select="$selectname"/>
          <xsl:choose>
            <xsl:when test="@status='WIP'">
              (<xsl:value-of select="$message[@id='xslt_messages.working.copy']" />)
            </xsl:when>
            <xsl:when test="@status='Active'">
              (<xsl:value-of select="$message[@id='xslt_messages.active']" />)
            </xsl:when>
          </xsl:choose>
        </a>
      </b>
    </div>
    <xsl:for-each select="Contents/Content">
      <div>
        <xsl:variable name="locale">
          <xsl:call-template name="translatelang">
            <xsl:with-param name="code" select="@locale"/>
          </xsl:call-template>
        </xsl:variable>
        <xsl:value-of select="$locale"/>
      </div>
      <xsl:call-template name="imagedata">
        <xsl:with-param name="imagename" select="@imagename"/>
        <xsl:with-param name="uploaded" select="@uploaded"/>
      </xsl:call-template>
      <div class="contentContainer">
        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" />Same as Default Language</i></xsl:if>
        <xsl:if test="@sameasparent='true'"><i><xsl:value-of select="$message[@id='xslt_messages.inherited']" /></i></xsl:if>
         <xsl:if test="@suppress='true'"><i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i></xsl:if>
         <xsl:if test="@empty='true'"><i><xsl:value-of select="$message[@id='xslt_messages.empty.part']" /></i></xsl:if>
        <xsl:value-of disable-output-escaping="yes" select="." />
      </div>
    </xsl:for-each>

    <xsl:apply-templates select="Selection" mode="imagelibrary">
      <xsl:with-param name="backhref" select="$backhref"/>
      <xsl:with-param name="imagelibraryid" select="$imagelibraryid"/>
    </xsl:apply-templates>
  </xsl:template>
  
  <xsl:template match="Selection" mode="multipart">
    <xsl:param name="backhref"/>
    <xsl:param name="contentObjectId"/>
    <xsl:param name="zoneid"/>

    <xsl:variable name="selectionId">
      <xsl:choose>
        <xsl:when test="Name='Default'">
          <xsl:value-of select="'def'"/>
        </xsl:when>
        <xsl:when test="Name='Master'">
          <xsl:value-of select="'def'"/>
        </xsl:when>
        <xsl:otherwise>
          <xsl:value-of select="@id"/>
        </xsl:otherwise>
      </xsl:choose>
    </xsl:variable>

    <div>
      <xsl:variable name="selectname">
        <xsl:value-of select="Name"/>
      </xsl:variable>
      <br/>
      <b>
        <a class="plainLink">
          <xsl:attribute name="name">messageSelectionContent_<xsl:value-of select="$contentObjectId"/>_<xsl:value-of select="$selectionId"/></xsl:attribute>
          <xsl:attribute name="href">#<xsl:value-of select="$backhref"/></xsl:attribute>
          <xsl:value-of select="$selectname"/>
          <xsl:choose>
            <xsl:when test="@status='WIP'">
              (<xsl:value-of select="$message[@id='xslt_messages.working.copy']" />)
            </xsl:when>
            <xsl:when test="@status='Active'">
              (<xsl:value-of select="$message[@id='xslt_messages.active']" />)
            </xsl:when>
          </xsl:choose>
        </a>      
      </b>
    </div>
    <xsl:for-each select="Contents/Content">
      <xsl:if test="@sameasparent='true'">
        <div>
          <xsl:variable name="locale">
            <xsl:call-template name="translatelang">
              <xsl:with-param name="code" select="@locale"/>
            </xsl:call-template>
          </xsl:variable>
          <xsl:value-of select="$locale"/>
        </div>
        <div class="contentContainer">
          <i><xsl:value-of select="$message[@id='xslt_messages.inherited']" /></i>
        </div>
      </xsl:if>
    </xsl:for-each>
    <xsl:for-each select="Contents/Part">
      <xsl:variable name="partnum">
        <xsl:number value="@num" format="1" />
      </xsl:variable>
      <xsl:variable name="sharedid">
        <xsl:value-of select="@sharedcontentid"/>
      </xsl:variable>
      <xsl:variable name="active">
        <xsl:value-of select="@active"/>
      </xsl:variable>

      <div><i><xsl:value-of select="$message[@id='xslt_messages.part']" /> <xsl:value-of select="@num"/></i> -&#160;<b><xsl:value-of select="key('zonekey', $zoneid)/Parts/Part[position()=$partnum]/."/></b></div>

      <xsl:choose>
        <xsl:when test="string-length($sharedid)>0 and string-length($active)>0">
          <xsl:apply-templates select="key('sharedpartkey', $sharedid)/ActiveContents" mode="multipartcontent"/>
        </xsl:when>
        <xsl:when test="string-length($sharedid)>0">
          <xsl:apply-templates select="key('sharedpartkey', $sharedid)/Contents" mode="multipartcontent"/>
        </xsl:when>
      </xsl:choose>

      <xsl:if test="not(node()) and string-length($sharedid)=0"><div class="contentContainer"><i><xsl:value-of select="$message[@id='xslt_messages.empty.part']" /></i></div></xsl:if>

      <xsl:for-each select="Content">
        <div>
          <xsl:variable name="locale">
            <xsl:call-template name="translatelang">
              <xsl:with-param name="code" select="@locale"/>
            </xsl:call-template>
          </xsl:variable>
          <xsl:value-of select="$locale"/>
        </div>
        <xsl:call-template name="imagedata">
          <xsl:with-param name="imagename" select="@imagename"/>
          <xsl:with-param name="uploaded" select="@uploaded"/>
        </xsl:call-template>
        <div class="contentContainer">
          <xsl:call-template name="imagelibraryref">
            <xsl:with-param name="imagelibraryid" select="@imagelibraryid"/>
          </xsl:call-template>
                 
          <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
          <xsl:if test="@sameasparent='true'"><i><xsl:value-of select="$message[@id='xslt_messages.inherited']" /></i></xsl:if>
          <xsl:if test="@empty='true'"><i><xsl:value-of select="$message[@id='xslt_messages.empty.part']" /></i></xsl:if>
          <xsl:if test="@suppress='true'"><i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i></xsl:if>
          <xsl:value-of disable-output-escaping="yes" select="." />
        </div>
      </xsl:for-each>
    </xsl:for-each>

    <xsl:apply-templates mode="multipart" select="Selection">
      <xsl:with-param name="backhref" select="$backhref"/>
      <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
      <xsl:with-param name="zoneid" select="$zoneid"/>
    </xsl:apply-templates>
  </xsl:template>

  <xsl:template match="Contents" mode="multipartcontent">
    <xsl:for-each select="Content">
      <div>
        <xsl:variable name="locale">
          <xsl:call-template name="translatelang">
            <xsl:with-param name="code" select="@locale"/>
          </xsl:call-template>
        </xsl:variable>
        <xsl:value-of select="$locale"/>
      </div>
      <xsl:call-template name="imagedata">
        <xsl:with-param name="imagename" select="@imagename"/>
        <xsl:with-param name="uploaded" select="@uploaded"/>
      </xsl:call-template>
      <div class="contentContainer">
        <xsl:call-template name="imagelibraryref">
          <xsl:with-param name="imagelibraryid" select="@imagelibraryid"/>
        </xsl:call-template>
             
        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
        <xsl:if test="@sameasparent='true'"><i><xsl:value-of select="$message[@id='xslt_messages.inherited']" /></i></xsl:if>
        <xsl:if test="@suppress='true'"><i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i></xsl:if>
        <xsl:if test="@empty='true'"><i><xsl:value-of select="$message[@id='xslt_messages.empty.part']" /></i></xsl:if>
        <xsl:value-of disable-output-escaping="yes" select="." />
      </div>
    </xsl:for-each>
  </xsl:template>

  <xsl:template match="ActiveContents" mode="multipartcontent">
    <xsl:for-each select="Content">
      <div>
        <xsl:variable name="locale">
          <xsl:call-template name="translatelang">
            <xsl:with-param name="code" select="@locale"/>
          </xsl:call-template>
        </xsl:variable>
        <xsl:value-of select="$locale"/>
      </div>
      <xsl:call-template name="imagedata">
        <xsl:with-param name="imagename" select="@imagename"/>
        <xsl:with-param name="uploaded" select="@uploaded"/>
      </xsl:call-template>
      <div class="contentContainer">
        <xsl:call-template name="imagelibraryref">
          <xsl:with-param name="imagelibraryid" select="@imagelibraryid"/>
        </xsl:call-template>
              
        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
        <xsl:if test="@sameasparent='true'"><i><xsl:value-of select="$message[@id='xslt_messages.inherited']" /></i></xsl:if>
        <xsl:if test="@suppress='true'"><i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i></xsl:if>
        <xsl:if test="@empty='true'"><i><xsl:value-of select="$message[@id='xslt_messages.empty.part']" /></i></xsl:if>
        <xsl:value-of disable-output-escaping="yes" select="." />
      </div>
    </xsl:for-each>
  </xsl:template>

  

  <xsl:template match="Message">
    <xsl:param name="suppressed">false</xsl:param>

    <xsl:variable name="messageindex">
      <xsl:value-of select="position()"/>
    </xsl:variable>
    
    <xsl:for-each select="Version">

      <xsl:variable name="versiontype">
        <xsl:if test="@status='Working Copy'">
          <xsl:value-of select="'_w'"/>
        </xsl:if>
        <xsl:if test="@status!='Working Copy'">
          <xsl:value-of select="'_a'"/>
        </xsl:if>
      </xsl:variable>
      <xsl:variable name="messageid">
        <xsl:value-of select="../@externalid"/>
      </xsl:variable>
      <xsl:variable name="contentObjectId">
        <xsl:value-of select="../@id"/>
      </xsl:variable>
      <xsl:variable name="messagetype">
        <xsl:value-of select="../@type"/>
      </xsl:variable>
      
      <!-- MESSAGE START -->
        <table class="reportContentTable">
        <tr>
          <td class="doubleColumnLabel"><a>
	          <xsl:attribute name="class">plainLink</xsl:attribute>
	          <xsl:attribute name="name">messageIndex_<xsl:number value="$contentObjectId" format="1" /><xsl:value-of select="$versiontype"/></xsl:attribute>
	          <xsl:attribute name="href">#messageIndex</xsl:attribute>
	          <xsl:if test="string-length($messageid)!=0">
	            <xsl:value-of select="$messageid"/> :&#160;
	          </xsl:if>
	          <xsl:value-of select="Name"/> <xsl:if test="@status='Working Copy'"> (<xsl:value-of select="$message[@id='xslt_messages.working.copy']" />)</xsl:if><xsl:if test="@status!='Working Copy'"> (<xsl:value-of select="$message[@id='xslt_messages.active']" />)</xsl:if>
	        </a>
          </td>
          <td class="doubleColumnValue">
          	<div class="contentContainer">
          		<xsl:value-of select="$message[@id='xslt_messages.content.type']" />:
           		<xsl:variable name="contenttype">
             		<xsl:call-template name="capitalize">
               	<xsl:with-param name="string" select="../@contenttype"/>
             		</xsl:call-template>
            	</xsl:variable>
            	<xsl:value-of select="$contenttype"/>, 
          		<xsl:value-of select="$message[@id='xslt_messages.type']" />:
          		<xsl:variable name="type">
	              <xsl:call-template name="capitalize">
	                <xsl:with-param name="string" select="../@type"/>
	              </xsl:call-template>
	            </xsl:variable>
	            <xsl:value-of select="$type"/>
            </div>
          </td>
        </tr>
      </table>
        <table class="reportContentTable">
          <xsl:if test="$showcontent='true' and ( (Delivery/Contents) or (Content) or (Contents) or (SelectableContent) )">
            <tr>
              <xsl:variable name="backhref">messageSelectionIndex_<xsl:value-of select="$contentObjectId"/></xsl:variable>

              <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.content']" />:</td>
              <td class="singleColumnValue">
                <xsl:if test="$messagetype='Dynamic'">
                  <div>
                    <b>
                      <a class="plainLink">
                        <xsl:attribute name="name"><xsl:value-of select="$backhref"/></xsl:attribute>
                        <xsl:value-of select="$message[@id='xslt_messages.message.content.selections']" />
                      </a>
                    </b>
                  </div>
                </xsl:if>

                <xsl:choose>
                  <xsl:when test="$suppressed='true'">
                    <div class="contentContainer">
                      <i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i>
                    </div>
                  </xsl:when>
                  <xsl:otherwise>
                    <xsl:apply-templates mode="TreeView" select="SelectableContent/Selection">
                      <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
                    </xsl:apply-templates>

                    <xsl:for-each select="Delivery/Contents/Part">
                      <xsl:variable name="partnum">
                        <xsl:number value="@num" format="1" />
                      </xsl:variable>
                      <xsl:variable name="sharedid">
                        <xsl:value-of select="@sharedcontentid"/>
                      </xsl:variable>
                      <xsl:variable name="active">
                        <xsl:value-of select="@active"/>
                      </xsl:variable>

                      <div><i><xsl:value-of select="$message[@id='xslt_messages.part']" /> <xsl:value-of select="@num"/></i> -&#160;<b><xsl:value-of select="key('zonekey', ../../@zonerefid)/Parts/Part[position()=$partnum]/."/></b>
                      </div>
                      
                      <xsl:choose>
                        <xsl:when test="string-length($sharedid)>0 and string-length($active)>0">
                          <xsl:apply-templates select="key('sharedpartkey', $sharedid)/ActiveContents" mode="multipartcontent"/>
                        </xsl:when>
                        <xsl:when test="string-length($sharedid)>0">
                          <xsl:apply-templates select="key('sharedpartkey', $sharedid)/Contents" mode="multipartcontent"/>
                        </xsl:when>
                      </xsl:choose>

                      <xsl:if test="not(node()) and string-length($sharedid)=0"><div class="contentContainer"><i><xsl:value-of select="$message[@id='xslt_messages.empty.part']" /></i></div></xsl:if>

                      <xsl:for-each select="Content">
                        <div>
                          <xsl:variable name="locale">
                            <xsl:call-template name="translatelang">
                              <xsl:with-param name="code" select="@locale"/>
                            </xsl:call-template>
                          </xsl:variable>
                          <xsl:value-of select="$locale"/>
                        </div>

                        <xsl:call-template name="imagedata">
                          <xsl:with-param name="imagename" select="@imagename"/>
                          <xsl:with-param name="uploaded" select="@uploaded"/>
                        </xsl:call-template>

                        <div class="contentContainer">
					      <xsl:call-template name="imagelibraryref">
					        <xsl:with-param name="imagelibraryid" select="@imagelibraryid"/>
					      </xsl:call-template>
					                            
                          <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
                          <xsl:value-of disable-output-escaping="yes" select="." />
                        </div>
                      </xsl:for-each>
                    </xsl:for-each>

                    <xsl:for-each select="Contents/Content">
                      <div>
                        <xsl:variable name="locale">
                          <xsl:call-template name="translatelang">
                            <xsl:with-param name="code" select="@locale"/>
                          </xsl:call-template>
                        </xsl:variable>
                        <xsl:value-of select="$locale"/>
                      </div>

                      <xsl:call-template name="imagedata">
                        <xsl:with-param name="imagename" select="@imagename"/>
                        <xsl:with-param name="uploaded" select="@uploaded"/>
                      </xsl:call-template>

                      <div class="contentContainer">
			            <xsl:call-template name="imagelibraryref">
			              <xsl:with-param name="imagelibraryid" select="@imagelibraryid"/>
			            </xsl:call-template>
			                                  
                        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
                        <xsl:value-of disable-output-escaping="yes" select="." />
                      </div>
                    </xsl:for-each>

                    <xsl:if test="../@contenttype='Multi-part'">
                      <xsl:apply-templates mode="multipart" select="SelectableContent/Selection">
                        <xsl:with-param name="backhref" select="$backhref"/>
                        <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
                        <xsl:with-param name="zoneid" select="Delivery/@zonerefid"/>
                      </xsl:apply-templates>
                    </xsl:if>

                    <xsl:if test="../@contenttype!='Multi-part'">
                      <xsl:apply-templates mode="singlepart" select="SelectableContent/Selection">
                        <xsl:with-param name="backhref" select="$backhref"/>
                        <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
                      </xsl:apply-templates>
                    </xsl:if>
                  </xsl:otherwise>
                </xsl:choose>
              </td>
            </tr>
          </xsl:if>
        </table>
      <!-- MESSAGE END -->
    </xsl:for-each>
  </xsl:template>
  
  <xsl:template match="LocalSmartText">
    <xsl:param name="suppressed">false</xsl:param>

    <xsl:variable name="messageindex">
      <xsl:value-of select="position()"/>
    </xsl:variable>
    
    <xsl:for-each select="Version">

      <xsl:variable name="versiontype">
        <xsl:if test="@status='Working Copy'">
          <xsl:value-of select="'_w'"/>
        </xsl:if>
        <xsl:if test="@status!='Working Copy'">
          <xsl:value-of select="'_a'"/>
        </xsl:if>
      </xsl:variable>
      <xsl:variable name="messageid">
        <xsl:value-of select="../@externalid"/>
      </xsl:variable>
      <xsl:variable name="contentObjectId">
        <xsl:value-of select="../@id"/>
      </xsl:variable>
      <xsl:variable name="messagetype">
        <xsl:value-of select="../@type"/>
      </xsl:variable>
      
      <!-- Local Smart Text START -->
        <table class="reportContentTable">
        <tr>
          <td class="doubleColumnLabel"><a>
	          <xsl:attribute name="class">plainLink</xsl:attribute>
	          <xsl:attribute name="name">messageIndex_<xsl:number value="$contentObjectId" format="1" /><xsl:value-of select="$versiontype"/></xsl:attribute>
	          <xsl:attribute name="href">#messageIndex</xsl:attribute>
	          <xsl:if test="string-length($messageid)!=0">
	            <xsl:value-of select="$messageid"/> :&#160;
	          </xsl:if>
	          <xsl:value-of select="Name"/> <xsl:if test="@status='Working Copy'"> (<xsl:value-of select="$message[@id='xslt_messages.working.copy']" />)</xsl:if><xsl:if test="@status!='Working Copy'"> (<xsl:value-of select="$message[@id='xslt_messages.active']" />)</xsl:if>
	        </a>
          </td>
          <td class="doubleColumnValue">
          	<div class="contentContainer">
          		<xsl:value-of select="$message[@id='xslt_messages.content.type']" />:
           		<xsl:variable name="contenttype">
             		<xsl:call-template name="capitalize">
               	<xsl:with-param name="string" select="../@contenttype"/>
             		</xsl:call-template>
            	</xsl:variable>
            	<xsl:value-of select="$contenttype"/>, 
          		<xsl:value-of select="$message[@id='xslt_messages.type']" />:
          		<xsl:variable name="type">
	              <xsl:call-template name="capitalize">
	                <xsl:with-param name="string" select="../@type"/>
	              </xsl:call-template>
	            </xsl:variable>
	            <xsl:value-of select="$type"/>
            </div>
          </td>
        </tr>
      </table>
        <table class="reportContentTable">
          <xsl:if test="$showcontent='true' and ( (Delivery/Contents) or (Content) or (Contents) or (SelectableContent) )">
            <tr>
              <xsl:variable name="backhref">messageSelectionIndex_<xsl:value-of select="$contentObjectId"/></xsl:variable>

              <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.content']" />:</td>
              <td class="singleColumnValue">
                <xsl:if test="$messagetype='Dynamic'">
                  <div>
                    <b>
                      <a class="plainLink">
                        <xsl:attribute name="name"><xsl:value-of select="$backhref"/></xsl:attribute>
                        <xsl:value-of select="$message[@id='xslt_messages.message.content.selections']" />
                      </a>
                    </b>
                  </div>
                </xsl:if>

                <xsl:choose>
                  <xsl:when test="$suppressed='true'">
                    <div class="contentContainer">
                      <i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i>
                    </div>
                  </xsl:when>
                  <xsl:otherwise>
                    <xsl:apply-templates mode="TreeView" select="SelectableContent/Selection">
                      <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
                    </xsl:apply-templates>

                    <xsl:for-each select="Delivery/Contents/Part">
                      <xsl:variable name="partnum">
                        <xsl:number value="@num" format="1" />
                      </xsl:variable>
                      <xsl:variable name="sharedid">
                        <xsl:value-of select="@sharedcontentid"/>
                      </xsl:variable>
                      <xsl:variable name="active">
                        <xsl:value-of select="@active"/>
                      </xsl:variable>

                      <div><i><xsl:value-of select="$message[@id='xslt_messages.part']" /> <xsl:value-of select="@num"/></i> -&#160;<b><xsl:value-of select="key('zonekey', ../../@zonerefid)/Parts/Part[position()=$partnum]/."/></b>
                      </div>
                      
                      <xsl:choose>
                        <xsl:when test="string-length($sharedid)>0 and string-length($active)>0">
                          <xsl:apply-templates select="key('sharedpartkey', $sharedid)/ActiveContents" mode="multipartcontent"/>
                        </xsl:when>
                        <xsl:when test="string-length($sharedid)>0">
                          <xsl:apply-templates select="key('sharedpartkey', $sharedid)/Contents" mode="multipartcontent"/>
                        </xsl:when>
                      </xsl:choose>

                      <xsl:if test="not(node()) and string-length($sharedid)=0"><div class="contentContainer"><i><xsl:value-of select="$message[@id='xslt_messages.empty.part']" /></i></div></xsl:if>

                      <xsl:for-each select="Content">
                        <div>
                          <xsl:variable name="locale">
                            <xsl:call-template name="translatelang">
                              <xsl:with-param name="code" select="@locale"/>
                            </xsl:call-template>
                          </xsl:variable>
                          <xsl:value-of select="$locale"/>
                        </div>

                        <xsl:call-template name="imagedata">
                          <xsl:with-param name="imagename" select="@imagename"/>
                          <xsl:with-param name="uploaded" select="@uploaded"/>
                        </xsl:call-template>

                        <div class="contentContainer">
					      <xsl:call-template name="imagelibraryref">
					        <xsl:with-param name="imagelibraryid" select="@imagelibraryid"/>
					      </xsl:call-template>
					                            
                          <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
                          <xsl:value-of disable-output-escaping="yes" select="." />
                        </div>
                      </xsl:for-each>
                    </xsl:for-each>

                    <xsl:for-each select="Contents/Content">
                      <div>
                        <xsl:variable name="locale">
                          <xsl:call-template name="translatelang">
                            <xsl:with-param name="code" select="@locale"/>
                          </xsl:call-template>
                        </xsl:variable>
                        <xsl:value-of select="$locale"/>
                      </div>

                      <xsl:call-template name="imagedata">
                        <xsl:with-param name="imagename" select="@imagename"/>
                        <xsl:with-param name="uploaded" select="@uploaded"/>
                      </xsl:call-template>

                      <div class="contentContainer">
			            <xsl:call-template name="imagelibraryref">
			              <xsl:with-param name="imagelibraryid" select="@imagelibraryid"/>
			            </xsl:call-template>
			                                  
                        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
                        <xsl:value-of disable-output-escaping="yes" select="." />
                      </div>
                    </xsl:for-each>

                    <xsl:if test="../@contenttype='Multi-part'">
                      <xsl:apply-templates mode="multipart" select="SelectableContent/Selection">
                        <xsl:with-param name="backhref" select="$backhref"/>
                        <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
                        <xsl:with-param name="zoneid" select="Delivery/@zonerefid"/>
                      </xsl:apply-templates>
                    </xsl:if>

                    <xsl:if test="../@contenttype!='Multi-part'">
                      <xsl:apply-templates mode="singlepart" select="SelectableContent/Selection">
                        <xsl:with-param name="backhref" select="$backhref"/>
                        <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
                      </xsl:apply-templates>
                    </xsl:if>
                  </xsl:otherwise>
                </xsl:choose>
              </td>
            </tr>
          </xsl:if>
        </table>
      <!-- Local Smart Text END -->
    </xsl:for-each>
  </xsl:template>
  
  <xsl:template match="LocalImageLibrary">
    <xsl:param name="suppressed">false</xsl:param>

    <xsl:variable name="messageindex">
      <xsl:value-of select="position()"/>
    </xsl:variable>
    
    <xsl:for-each select="Version">

      <xsl:variable name="versiontype">
        <xsl:if test="@status='Working Copy'">
          <xsl:value-of select="'_w'"/>
        </xsl:if>
        <xsl:if test="@status!='Working Copy'">
          <xsl:value-of select="'_a'"/>
        </xsl:if>
      </xsl:variable>
      <xsl:variable name="messageid">
        <xsl:value-of select="../@externalid"/>
      </xsl:variable>
      <xsl:variable name="contentObjectId">
        <xsl:value-of select="../@id"/>
      </xsl:variable>
      <xsl:variable name="messagetype">
        <xsl:value-of select="../@type"/>
      </xsl:variable>
      
      <!-- Local Image Library START -->
        <table class="reportContentTable">
        <tr>
          <td class="doubleColumnLabel"><a>
	          <xsl:attribute name="class">plainLink</xsl:attribute>
	          <xsl:attribute name="name">messageIndex_<xsl:number value="$contentObjectId" format="1" /><xsl:value-of select="$versiontype"/></xsl:attribute>
	          <xsl:attribute name="href">#messageIndex</xsl:attribute>
	          <xsl:if test="string-length($messageid)!=0">
	            <xsl:value-of select="$messageid"/> :&#160;
	          </xsl:if>
	          <xsl:value-of select="Name"/> <xsl:if test="@status='Working Copy'"> (<xsl:value-of select="$message[@id='xslt_messages.working.copy']" />)</xsl:if><xsl:if test="@status!='Working Copy'"> (<xsl:value-of select="$message[@id='xslt_messages.active']" />)</xsl:if>
	        </a>
          </td>
          <td class="doubleColumnValue">
          	<div class="contentContainer">
          		<xsl:value-of select="$message[@id='xslt_messages.content.type']" />:
           		<xsl:variable name="contenttype">
             		<xsl:call-template name="capitalize">
               	<xsl:with-param name="string" select="../@contenttype"/>
             		</xsl:call-template>
            	</xsl:variable>
            	<xsl:value-of select="$contenttype"/>, 
          		<xsl:value-of select="$message[@id='xslt_messages.type']" />:
          		<xsl:variable name="type">
	              <xsl:call-template name="capitalize">
	                <xsl:with-param name="string" select="../@type"/>
	              </xsl:call-template>
	            </xsl:variable>
	            <xsl:value-of select="$type"/>
            </div>
          </td>
        </tr>
      </table>
      <!-- Local Image Library END -->
    </xsl:for-each>
  </xsl:template>
  
  <xsl:template match="Touchpoints">
    <xsl:for-each select="Touchpoint">
      <div class="sectionSubHeader">
        <a name="touchpointIndex_1" class="plainLink"><xsl:value-of select="Name"/></a>
      </div>

      <div class="sectionContent">
        <table class="reportContentTable">
          <xsl:for-each select="Sections/Section">
            <tr>
              <td class="dataColumn"><i><xsl:value-of select="$message[@id='xslt_messages.section']" /></i> - <xsl:value-of select="Name"/></td>
              <td></td>
            </tr>
            <xsl:for-each select="Zones/Zone">
              <tr>
                <td class="dataColumn"><div class="level2"><i><xsl:value-of select="$message[@id='xslt_messages.zone']" /></i> - <xsl:value-of select="Name"/></div></td>
                <td>
                  <xsl:value-of select="@type"/>
                  <xsl:if test="string-length(@graphictype)!=0">
                    (<xsl:value-of select="@graphictype"/>)
                  </xsl:if>
                </td>
              </tr>
              <xsl:for-each select="Parts/Part">
                <tr>
                  <td class="dataColumn"><div class="level3"><i><xsl:value-of select="$message[@id='xslt_messages.part']" /></i> - <xsl:value-of select="."/></div></td>
                  <xsl:if test="@type='Text'">
                    <td><xsl:value-of select="$message[@id='xslt_messages.text']" /></td>
                  </xsl:if>
                  <xsl:if test="@type='Graphic'">
                    <td><xsl:value-of select="$message[@id='xslt_messages.graphic']" />(<xsl:value-of select="@graphictype"/>)</td>
                  </xsl:if>
                </tr>
              </xsl:for-each>
            </xsl:for-each>
          </xsl:for-each>
        </table>
      </div>
    </xsl:for-each>
  </xsl:template>
  
  <xsl:template match="Touchpoints" mode="messages">
    <xsl:variable name="selectionid">
      <xsl:value-of select="/TouchpointCompare/Metadata/Selection/@refid"/>
    </xsl:variable>

    <xsl:for-each select="Touchpoint">
      <xsl:apply-templates select="Selections/Selection" mode="touchpointselectablecontent">
        <xsl:with-param name="baseselectionid" select="$selectionid"/>
      </xsl:apply-templates>

      <xsl:apply-templates select="Selections/Selection" mode="touchpointselectablemessage">
        <xsl:with-param name="baseselectionid" select="$selectionid"/>
      </xsl:apply-templates>
    </xsl:for-each>
  </xsl:template>



  <xsl:template match="Selection" mode="touchpointselectablemessage">
    <xsl:param name="baseselectionid"/>

    <xsl:choose>
      <xsl:when test="@id=$baseselectionid">
        <xsl:for-each select="Messages/Message">
          <xsl:variable name="suppressed">
            <xsl:value-of select="@suppressed"/>
          </xsl:variable>
          <xsl:variable name="contentObjectId">
          	<xsl:value-of select="@id"/>
          </xsl:variable>
          <xsl:apply-templates select="key('messagekey', $contentObjectId)">
            <xsl:with-param name="suppressed" select="$suppressed"/>
          </xsl:apply-templates>
        </xsl:for-each>
      </xsl:when>

      <xsl:otherwise>
        <xsl:apply-templates select="Selection" mode="touchpointselectablemessage">
          <xsl:with-param name="baseselectionid" select="$baseselectionid"/>
        </xsl:apply-templates>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>


  <xsl:template match="Contents" mode="selectionnode">
    <xsl:param name="contentObjectId"/>
    <xsl:param name="prevparentrefid" select="0"/>
    <xsl:param name="status"/>
    <xsl:param name="custom"/>

    <xsl:for-each select="Message">
      <xsl:variable name="suppressed">
        <xsl:value-of select="@suppress"/>
      </xsl:variable>
      <xsl:variable name="messagetype">a</xsl:variable>
      <xsl:variable name="sameasparent">
        <xsl:value-of select="@sameasparent"/>
      </xsl:variable>
      <xsl:variable name="empty">
        <xsl:value-of select="@empty"/>
      </xsl:variable>
      <xsl:variable name="parentrefid">
        <xsl:value-of select="@parentrefid"/>
      </xsl:variable>

      <xsl:if test="@refid=$contentObjectId">
        <xsl:choose>
          <xsl:when test="string-length($parentrefid)>0">
            <xsl:apply-templates select="key('selectionkey', $parentrefid)/Contents" mode="selectionnode">
              <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
              <xsl:with-param name="prevparentrefid" select="$prevparentrefid"/>
              <xsl:with-param name="status" select="$status"/>
              <xsl:with-param name="custom" select="$custom"/>
            </xsl:apply-templates>
          </xsl:when>
          <xsl:otherwise>
            <div class="sectionSubHeader">
              <xsl:choose>
                <xsl:when test="string-length(@name)>0">
                  <xsl:value-of select="@name"/>
                </xsl:when>
                <xsl:otherwise>
                  <a href="#messageIndex">
                    <xsl:attribute name="name">messageIndex_<xsl:value-of select="@refid"/></xsl:attribute>
                    <xsl:value-of select="key('messagekey', @refid)/Version/Name"/>
                  </a>
                </xsl:otherwise>
              </xsl:choose>
            </div>

            <div class="sectionContent">
              <table class="reportContentTable">
                <xsl:if test="$showcontent='true'">
                  <xsl:variable name="backhref">messageSelectionIndex_<xsl:value-of select="$contentObjectId"/></xsl:variable>
                  <tr>
                    <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.status']" />:</td>
                    <td class="singleColumnValue"><xsl:value-of select="$status"/></td>
                  </tr>
                  <tr>
                    <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.content']" />:</td>
                    <td class="singleColumnValue">
                      <xsl:choose>
                        <xsl:when test="$prevparentrefid!=0">
                          <div>
                            <i><xsl:value-of select="$message[@id='xslt_messages.same.as']" /> <b><xsl:value-of select="key('selectionkey', $prevparentrefid)/Name"/></b></i>
                          </div>
                        </xsl:when>
                        <xsl:when test="$custom='true'">
                          <div>
                            <i><xsl:value-of select="$message[@id='xslt_messages.custom']" /></i>
                          </div>
                        </xsl:when>
                        <xsl:when test="@suppress='true'">
                          <div>
                            <i><xsl:value-of select="$message[@id='xslt_messages.suppressed']" /></i>
                          </div>
							   </xsl:when>
                      </xsl:choose>
                      <xsl:if test="$messagetype='Dynamic'">
                        <div>
                          <b>
                            <a class="plainLink">
                              <xsl:attribute name="name"><xsl:value-of select="$backhref"/></xsl:attribute>
                              <xsl:value-of select="$message[@id='xslt_messages.message.content.selections']" />
                            </a>
                          </b>
                        </div>
                      </xsl:if>
                      <div>&#160; </div>
                      <xsl:apply-templates mode="TreeView" select="SelectableContent/Selection">
                        <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
                      </xsl:apply-templates>

                      <xsl:for-each select="Part">
                        <xsl:variable name="partnum">
                          <xsl:number value="@num" format="1" />
                        </xsl:variable>
                        <xsl:variable name="sharedid">
                          <xsl:value-of select="@sharedcontentid"/>
                        </xsl:variable>
                        <xsl:variable name="active">
                          <xsl:value-of select="@active"/>
                        </xsl:variable>

                        <div><i><xsl:value-of select="$message[@id='xslt_messages.part']" /> <xsl:value-of select="$partnum"/></i> -&#160;<b><xsl:value-of select="key('zonekey', key('messagekey', $contentObjectId)/Version/Delivery[1]/@zonerefid)/Parts/Part[position()=$partnum]/."/></b>
                        </div>
                        
                        <xsl:choose>
                          <xsl:when test="string-length($sharedid)>0 and string-length($active)>0">
                            <xsl:apply-templates select="key('sharedpartkey', $sharedid)/ActiveContents" mode="multipartcontent"/>
                          </xsl:when>
                          <xsl:when test="string-length($sharedid)>0">
                            <xsl:apply-templates select="key('sharedpartkey', $sharedid)/Contents" mode="multipartcontent"/>
                          </xsl:when>
                        </xsl:choose>

                        <xsl:if test="@empty='true'">
                          <div class="contentContainer"><i><xsl:value-of select="$message[@id='xslt_messages.empty.part']" /></i></div>
                        </xsl:if>

                        <xsl:for-each select="Content">
                          <div>
                            <xsl:variable name="locale">
                              <xsl:call-template name="translatelang">
                                <xsl:with-param name="code" select="@locale"/>
                              </xsl:call-template>
                            </xsl:variable>
                            <xsl:value-of select="$locale"/>
                          </div>

                          <xsl:call-template name="imagedata">
                            <xsl:with-param name="imagename" select="@imagename"/>
                            <xsl:with-param name="uploaded" select="@uploaded"/>
                          </xsl:call-template>

                          <div class="contentContainer">
					        <xsl:call-template name="imagelibraryref">
					          <xsl:with-param name="imagelibraryid" select="@imagelibraryid"/>
					        </xsl:call-template>
					                                
                            <xsl:if test="@sameasparent='true'"><i><xsl:value-of select="$message[@id='xslt_messages.inherited']" /></i></xsl:if>
                            <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
                            <xsl:if test="@empty='true'"><i><xsl:value-of select="$message[@id='xslt_messages.empty.content']" /></i></xsl:if>
                            <xsl:if test="@suppress='true'"><i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i></xsl:if>
                            <xsl:value-of disable-output-escaping="yes" select="." />
                          </div>
                        </xsl:for-each>
                        <div>&#160;</div>
                      </xsl:for-each>

                      <xsl:for-each select="Content">
                        <div>
                          <xsl:variable name="locale">
                            <xsl:call-template name="translatelang">
                              <xsl:with-param name="code" select="@locale"/>
                            </xsl:call-template>
                          </xsl:variable>
                          <xsl:value-of select="$locale"/>
                        </div>

                        <xsl:call-template name="imagedata">
                          <xsl:with-param name="imagename" select="@imagename"/>
                          <xsl:with-param name="uploaded" select="@uploaded"/>
                        </xsl:call-template>

                        <div class="contentContainer">
					      <xsl:call-template name="imagelibraryref">
					        <xsl:with-param name="imagelibraryid" select="@imagelibraryid"/>
					      </xsl:call-template>
					                            
                          <xsl:if test="@sameasparent='true'"><i><xsl:value-of select="$message[@id='xslt_messages.inherited']" /></i></xsl:if>
                          <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
                          <xsl:if test="@empty='true'"><i><xsl:value-of select="$message[@id='xslt_messages.empty.content']" /></i></xsl:if>
                          <xsl:if test="@suppress='true'"><i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i></xsl:if>
                          <xsl:value-of disable-output-escaping="yes" select="." />
                        </div>
                      </xsl:for-each>

                      <xsl:if test="$suppressed='true'">
                        <div class="contentContainer">
                          <i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i>
                        </div>
                      </xsl:if>

                      <xsl:if test="$sameasparent='true'">
                        <div class="contentContainer">
                          <i><xsl:value-of select="$message[@id='xslt_messages.inherited']" /></i>
                        </div>
                      </xsl:if>

                      <xsl:if test="$empty='true'">
                        <div class="contentContainer">
                          <i><xsl:value-of select="$message[@id='xslt_messages.empty.content']" /></i>
                        </div>
                      </xsl:if>

                      <xsl:if test="../@contenttype='Multi-part'">
                        <xsl:apply-templates mode="multipart" select="SelectableContent/Selection">
                          <xsl:with-param name="backhref" select="$backhref"/>
                          <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
                          <xsl:with-param name="zoneid" select="Delivery/@zonerefid"/>
                        </xsl:apply-templates>
                      </xsl:if>

                      <xsl:if test="../@contenttype!='Multi-part'">
                        <xsl:apply-templates mode="singlepart" select="SelectableContent/Selection">
                          <xsl:with-param name="backhref" select="$backhref"/>
                          <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
                        </xsl:apply-templates>
                      </xsl:if>
                    </td>
                  </tr>
                </xsl:if>
              </table>
            </div>
          </xsl:otherwise>
        </xsl:choose>
      </xsl:if>
    </xsl:for-each>
  </xsl:template>


  <xsl:template match="Selection" mode="touchpointselectablecontent">
    <xsl:param name="baseselectionid"/>

    <xsl:if test="@id=$baseselectionid">

      <xsl:for-each select="Contents/Message">

        <xsl:variable name="contentObjectId">
          <xsl:value-of select="@refid"/>
        </xsl:variable>
        <xsl:variable name="parentrefid">
          <xsl:value-of select="@parentrefid"/>
        </xsl:variable>

        <xsl:choose>
          <xsl:when test="string-length($parentrefid)>0">
            <xsl:apply-templates select="key('selectionkey', $parentrefid)/Contents" mode="selectionnode">
              <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
              <xsl:with-param name="prevparentrefid" select="$parentrefid"/>
              <xsl:with-param name="status" select="@status"/>
              <xsl:with-param name="custom" select="@custom"/>
            </xsl:apply-templates>
          </xsl:when>
          <xsl:otherwise>
            <xsl:apply-templates select=".." mode="selectionnode">
              <xsl:with-param name="contentObjectId" select="$contentObjectId"/>
              <xsl:with-param name="status" select="@status"/>
              <xsl:with-param name="custom" select="@custom"/>
            </xsl:apply-templates>
          </xsl:otherwise>
        </xsl:choose>

      </xsl:for-each>
    </xsl:if>

    <xsl:apply-templates select="Selection" mode="touchpointselectablecontent">
      <xsl:with-param name="baseselectionid" select="$baseselectionid" />
    </xsl:apply-templates>

  </xsl:template>


  <xsl:template match="Touchpoints" mode="selectiontree">
    <div class="sectionContent">
      <xsl:for-each select="Touchpoint/Selections/Selection">
        <xsl:value-of select="../../Name"/>
        <xsl:apply-templates select="." mode="TPTreeView"/>
      </xsl:for-each>
    </div>
  </xsl:template>

  <xsl:template match="Selection" mode="TPTreeView">
    <ul>
      <li>
        <xsl:choose>
          <xsl:when test="@id=/TouchpointCompare/Metadata/Selection/@refid">
            <a href="#section_touchpoint_messages"><xsl:value-of select="Name"/></a>
          </xsl:when>
          <xsl:otherwise>
            <xsl:value-of select="Name"/>
          </xsl:otherwise>
        </xsl:choose>
      </li>
      <xsl:apply-templates select="Selection" mode="TPTreeView"/>
    </ul>
  </xsl:template>

  <xsl:template name="imagedata">
    <xsl:param name="imagename"/>
    <xsl:param name="uploaded"/>

    <xsl:if test="string-length($imagename) > 0">
      <div class="contentContainer">
        <xsl:value-of select="$message[@id='xslt_messages.reference.name']" />: <xsl:value-of select="@imagename"/>
      </div>
    </xsl:if>
    <xsl:if test="string-length($uploaded) > 0 and $uploaded != 'unknown'">
      <xsl:variable name="uploaded">
        <xsl:call-template name="formdate">
          <xsl:with-param name="date" select="@uploaded"/>
        </xsl:call-template>
      </xsl:variable>
      <div class="contentContainer">
        <xsl:value-of select="$message[@id='xslt_messages.upload.date']" />: <xsl:value-of select="$uploaded"/>
      </div>
    </xsl:if>
    <xsl:if test="string-length($uploaded) > 0 and $uploaded = 'unknown'">
      <div class="contentContainer">
        <xsl:value-of select="$message[@id='xslt_messages.upload.date']" />: <xsl:value-of select="$message[@id='xslt_messages.unknown']" />
      </div>
    </xsl:if>
  </xsl:template>

  <xsl:template name="imagelibraryref">
    <xsl:param name="imagelibraryid"/>

    <xsl:variable name="imagelibraryname">
      <xsl:value-of select="/TouchpointCompare/ReferenceData/ImageLibraries/ImageLibrary[@id=$imagelibraryid]/Name"/>
    </xsl:variable>
      
    <xsl:if test="$imagelibraryid > 0">
      <xsl:value-of select="$message[@id='xslt_messages.referencing.image.library']" />: 
      <a class="plainLink">
        <xsl:attribute name="href">#imageLibraryIndex_<xsl:value-of select="$imagelibraryid"/></xsl:attribute>
        <xsl:value-of select="$imagelibraryname"/>
      </a>
    </xsl:if>
  </xsl:template>
  
  <xsl:template name="imagelibraryToref">
    <xsl:param name="imagelibraryid"/>

    <xsl:variable name="imagelibraryname">
      <xsl:value-of select="/TouchpointCompare/ReferenceData/ImageLibraries/ImageLibrary[@id=$imagelibraryid]/Name"/>
    </xsl:variable>
      
    <xsl:if test="$imagelibraryid > 0">
      <xsl:value-of select="$message[@id='xslt_messages.referencing.image.library']" />: 
      <a class="plainLink">
        <xsl:attribute name="href">#imageLibraryIndex_<xsl:value-of select="$imagelibraryid"/></xsl:attribute>
        <xsl:value-of select="$imagelibraryname"/>
      </a>
    </xsl:if>
  </xsl:template>
  
  <!-- Utility Templates below -->
  <!-- Capitalize first letter of a string -->
  <xsl:template name="capitalize">
    <xsl:param name="string"/>
    <xsl:param name="caps" select="true()"/>

    <xsl:if test="$caps">
      <xsl:value-of select="translate(substring($string,1,1),'abcdefghijklmnopqrstuvwxyz','ABCDEFGHIJKLMNOPQRSTUVWXYZ')"/>
      <xsl:value-of select="substring($string,2)"/>
    </xsl:if>
    <xsl:if test="not($caps)">
      <xsl:value-of select="$string"/>
    </xsl:if>
  </xsl:template>

  <!-- convert a date in the form yyyy-mm-dd to MMM dd, yyyy -->
  <xsl:template name="formdate">
    <xsl:param name="date"/>
    <xsl:variable name="len">
      <xsl:value-of select="string-length($date)"/>
    </xsl:variable>
    <xsl:variable name="yr">
      <xsl:value-of select="substring($date,1,4)" />
    </xsl:variable>
    <xsl:variable name="mo">
      <xsl:value-of select="substring($date,6,2)" />
    </xsl:variable>
    <xsl:variable name="dt">
      <xsl:value-of select="substring($date,9,2)" />
    </xsl:variable>
    <xsl:variable name="month">
      <xsl:choose>
        <xsl:when test="$mo = '01'">Jan</xsl:when>
        <xsl:when test="$mo = '02'">Feb</xsl:when>
        <xsl:when test="$mo = '03'">Mar</xsl:when>
        <xsl:when test="$mo = '04'">Apr</xsl:when>
        <xsl:when test="$mo = '05'">May</xsl:when>
        <xsl:when test="$mo = '06'">Jun</xsl:when>
        <xsl:when test="$mo = '07'">Jul</xsl:when>
        <xsl:when test="$mo = '08'">Aug</xsl:when>
        <xsl:when test="$mo = '09'">Sep</xsl:when>
        <xsl:when test="$mo = '10'">Oct</xsl:when>
        <xsl:when test="$mo = '11'">Nov</xsl:when>
        <xsl:when test="$mo = '12'">Dec</xsl:when>
      </xsl:choose>
    </xsl:variable>
    <xsl:if test="$len > 0">
      <xsl:value-of select="$month"/>&#160;
      <xsl:value-of select="$dt"/>,&#160;
      <xsl:value-of select="$yr"/>
    </xsl:if>
  </xsl:template>

  <!-- Translate a five letter locale code to a locale description -->
  <xsl:template name="translatelang">
    <xsl:param name="code"/>
    <xsl:choose>
        <xsl:when test="$code='en_us'">English (US)</xsl:when>
        <xsl:when test="$code='en_ca'">English (Canada)</xsl:when>
        <xsl:when test="$code='en_gb'">English (UK)</xsl:when>
        <xsl:when test="$code='en_au'">English (Australia)</xsl:when>
        <xsl:when test="$code='en_bz'">English (Belize)</xsl:when>
        <xsl:when test="$code='en_bs'">English (Caribbean)</xsl:when>
        <xsl:when test="$code='en_in'">English (India)</xsl:when>
        <xsl:when test="$code='en_ie'">English (Ireland)</xsl:when>
        <xsl:when test="$code='en_jm'">English (Jamaica)</xsl:when>
        <xsl:when test="$code='en_my'">English (Malaysia)</xsl:when>
        <xsl:when test="$code='en_nz'">English (New Zealand)</xsl:when>
        <xsl:when test="$code='en_ph'">English (Philippines)</xsl:when>
        <xsl:when test="$code='en_sg'">English (Singapore)</xsl:when>
        <xsl:when test="$code='en_za'">English (South Africa)</xsl:when>
        <xsl:when test="$code='en_tt'">English (Trinidad and Tobago)</xsl:when>
        <xsl:when test="$code='en_zw'">English (Zimbabwe)</xsl:when>
        <xsl:when test="$code='fr_fr'">French (France)</xsl:when>
        <xsl:when test="$code='fr_ca'">French (Canada)</xsl:when>
        <xsl:when test="$code='fr_be'">French (Belgium)</xsl:when>
        <xsl:when test="$code='fr_lu'">French (Luxembourg)</xsl:when>
        <xsl:when test="$code='fr_mc'">French (Monaco)</xsl:when>
        <xsl:when test="$code='fr_ch'">French (Switzerland)</xsl:when>
        <xsl:when test="$code='fr_nl'">French (Netherlands)</xsl:when>
        <xsl:when test="$code='de_de'">German (Germany)</xsl:when>
        <xsl:when test="$code='de_at'">German (Austria)</xsl:when>
        <xsl:when test="$code='de_li'">German (Liechtenstein)</xsl:when>
        <xsl:when test="$code='de_lu'">German (Luxembourg)</xsl:when>
        <xsl:when test="$code='de_ch'">German (Switzerland)</xsl:when>
        <xsl:when test="$code='it_it'">Italian (Italy)</xsl:when>
        <xsl:when test="$code='it_ch'">Italian (Switzerland)</xsl:when>
        <xsl:when test="$code='es_es'">Spanish (Spain)</xsl:when>
        <xsl:when test="$code='es_ar'">Spanish (Argentina)</xsl:when>
        <xsl:when test="$code='es_ve'">Spanish (Venezuela)</xsl:when>
        <xsl:when test="$code='es_bo'">Spanish (Bolivia)</xsl:when>
        <xsl:when test="$code='es_cl'">Spanish (Chile)</xsl:when>
        <xsl:when test="$code='es_co'">Spanish (Colombia)</xsl:when>
        <xsl:when test="$code='es_cr'">Spanish (Costa Rica)</xsl:when>
        <xsl:when test="$code='es_do'">Spanish (Dominican Republic)</xsl:when>
        <xsl:when test="$code='es_ec'">Spanish (Ecuador)</xsl:when>
        <xsl:when test="$code='es_sv'">Spanish (El Salvador)</xsl:when>
        <xsl:when test="$code='es_gt'">Spanish (Guatemala)</xsl:when>
        <xsl:when test="$code='es_hn'">Spanish (Honduras)</xsl:when>
        <xsl:when test="$code='es_mx'">Spanish (Mexico)</xsl:when>
        <xsl:when test="$code='es_ni'">Spanish (Nicaragua)</xsl:when>
        <xsl:when test="$code='es_pa'">Spanish (Panama)</xsl:when>
        <xsl:when test="$code='es_py'">Spanish (Paraguay)</xsl:when>
        <xsl:when test="$code='es_pe'">Spanish (Peru)</xsl:when>
        <xsl:when test="$code='es_pr'">Spanish (Puerto Rico)</xsl:when>
        <xsl:when test="$code='es_us'">Spanish (US)</xsl:when>
        <xsl:when test="$code='es_uy'">Spanish (Uruguay)</xsl:when>
        <xsl:when test="$code='af_za'">Afrikaans (South Africa)</xsl:when>
        <xsl:when test="$code='sq_al'">Albanian (Albania)</xsl:when>
        <xsl:when test="$code='eu_eu'">Basque (Basque)</xsl:when>
        <xsl:when test="$code='br_fr'">Breton (France)</xsl:when>
        <xsl:when test="$code='ca_ad'">Catalan (Catalan)</xsl:when>
        <xsl:when test="$code='da_dk'">Danish (Denmark)</xsl:when>
        <xsl:when test="$code='nl_be'">Dutch (Belgium)</xsl:when>
        <xsl:when test="$code='nl_nl'">Dutch (Netherlands)</xsl:when>
        <xsl:when test="$code='et_ee'">Estonian (Estonia)</xsl:when>
        <xsl:when test="$code='fo_fo'">Faroese (Faroe Islands)</xsl:when>
        <xsl:when test="$code='fi_fi'">Finnish (Finland)</xsl:when>
        <xsl:when test="$code='gl_gl'">Galician (Galician)</xsl:when>
        <xsl:when test="$code='is_is'">Icelandic (Iceland)</xsl:when>
        <xsl:when test="$code='ga_ie'">Irish (Ireland)</xsl:when>
        <xsl:when test="$code='lb_lu'">Luxembourgish (Luxembourg)</xsl:when>
        <xsl:when test="$code='no_nb'">Norwegian (Bokmal)</xsl:when>
        <xsl:when test="$code='no_nn'">Norwegian (Nynorsk)</xsl:when>
        <xsl:when test="$code='pt_br'">Portuguese (Brazil)</xsl:when>
        <xsl:when test="$code='pt_pt'">Portuguese (Portugal)</xsl:when>
        <xsl:when test="$code='gd_gb'">Scottish Gaelic (UK)</xsl:when>
        <xsl:when test="$code='sw_km'">Swahili (Comoros)</xsl:when>
        <xsl:when test="$code='sw_ke'">Swahili (Kenya)</xsl:when>
        <xsl:when test="$code='sw_tz'">Swahili (Tanzania)</xsl:when>
        <xsl:when test="$code='sw_ug'">Swahili (Uganda)</xsl:when>
        <xsl:when test="$code='sv_fi'">Swedish (Finland)</xsl:when>
        <xsl:when test="$code='sv_se'">Swedish (Sweden)</xsl:when>
        <xsl:when test="$code='wa_be'">Walloon (Belgium)</xsl:when>
        <xsl:when test="$code='wa_fr'">Walloon (France)</xsl:when>
        <xsl:when test="$code='hu_hu'">Hungarian (Hungary)</xsl:when>
        <xsl:when test="$code='pl_pl'">Polish (Poland)</xsl:when>
        <xsl:when test="$code='bs_ba'">Bosnian (Bosnia - Latin)</xsl:when>
        <xsl:when test="$code='hr_hr'">Croatian (Croatia)</xsl:when>
        <xsl:when test="$code='cs_cz'">Czech (Czech Republic)</xsl:when>
        <xsl:when test="$code='ro_ro'">Romanian (Romania)</xsl:when>
        <xsl:when test="$code='sr_sp'">Serbian (Serbia - Latin)</xsl:when>
        <xsl:when test="$code='sr_ba'">Serbian (Bosnia - Latin)</xsl:when>
        <xsl:when test="$code='sr_cs'">Serbian (Montenegro - Latin)</xsl:when>
        <xsl:when test="$code='sk_sk'">Slovak (Slovakia)</xsl:when>
        <xsl:when test="$code='sl_si'">Slovenian (Slovenia)</xsl:when>
        <xsl:when test="$code='zh_cn'">Chinese (China)</xsl:when>
        <xsl:when test="$code='zh_hk'">Chinese (Hong Kong S.A.R.)</xsl:when>
        <xsl:when test="$code='zh_mo'">Chinese (Macau S.A.R.)</xsl:when>
        <xsl:when test="$code='zh_sg'">Chinese (Singapore)</xsl:when>
        <xsl:when test="$code='zh_tw'">Chinese (Taiwan)</xsl:when>
        <xsl:when test="$code='ja_jp'">Japanese (Japan)</xsl:when>
        <xsl:when test="$code='ko_kr'">Korean (Korea)</xsl:when>
        <xsl:when test="$code='vi_vn'">Vietnamese (Vietnam)</xsl:when>
        <xsl:when test="$code='hk_hk'">Cantonese (Hong Kong S.A.R.)</xsl:when>
        <xsl:when test="$code='hk_mo'">Cantonese (Macau S.A.R.)</xsl:when>
        <xsl:when test="$code='il_ph'">Ilokano (Philippines)</xsl:when>
        <xsl:when test="$code='he_il'">Hebrew (Israel)</xsl:when>
        <xsl:when test="$code='ru_ru'">Russian (Russia)</xsl:when>
        <xsl:when test="$code='th_th'">Thai (Thailand)</xsl:when>
        <xsl:when test="$code='ta_in'">Tamil (India)</xsl:when>
        <xsl:when test="$code='hi_in'">Hindi (India)</xsl:when>
        <xsl:when test="$code='pa_in'">Punjabi (India)</xsl:when>
        <xsl:when test="$code='uk_ua'">Ukrainian (Ukraine)</xsl:when>
        <xsl:when test="$code='id_id'">Indonesian (Indonesia)</xsl:when>
        <xsl:when test="$code='ms_my'">Malay (Malaysia)</xsl:when>
        <xsl:when test="$code='ms_bn'">Malay (Brunei)</xsl:when>
        <xsl:when test="$code='ar_sa'">Arabic (Saudi Arabia)</xsl:when>
        <xsl:when test="$code='ar_ae'">Arabic (United Arab Emirates)</xsl:when>
        <xsl:when test="$code='ar_dz'">Arabic (Algeria)</xsl:when>
        <xsl:when test="$code='ar_bh'">Arabic (Bahrain)</xsl:when>
        <xsl:when test="$code='ar_eg'">Arabic (Egypt)</xsl:when>
        <xsl:when test="$code='ar_iq'">Arabic (Iraq)</xsl:when>
        <xsl:when test="$code='ar_jo'">Arabic (Jordan)</xsl:when>
        <xsl:when test="$code='ar_kw'">Arabic (Kuwait)</xsl:when>
        <xsl:when test="$code='ar_lb'">Arabic (Lebanon)</xsl:when>
        <xsl:when test="$code='ar_ly'">Arabic (Libya)</xsl:when>
        <xsl:when test="$code='ar_ma'">Arabic (Morocco)</xsl:when>
        <xsl:when test="$code='ar_om'">Arabic (Oman)</xsl:when>
        <xsl:when test="$code='ar_qa'">Arabic (Qatar)</xsl:when>
        <xsl:when test="$code='ar_sy'">Arabic (Syria)</xsl:when>
        <xsl:when test="$code='ar_tn'">Arabic (Tunisia)</xsl:when>
        <xsl:when test="$code='ar_ye'">Arabic (Yemen)</xsl:when>
    </xsl:choose>
  </xsl:template>

  <!-- Image Libraries Templates below -->
  <xsl:template match="ImageLibraries">
  	<xsl:for-each select="ImageLibrary">
  	  <xsl:apply-templates select="."/>
  	</xsl:for-each>
  </xsl:template>
  
  <!-- Image Library Templates below -->
  <xsl:template match="ImageLibrary">
  	  <xsl:param name="suppressed">false</xsl:param>
  	  
      <xsl:variable name="imagelibraryid">
        <xsl:value-of select="@id"/>
      </xsl:variable>
      <xsl:variable name="imagelibrarytype">
        <xsl:value-of select="@type"/>
      </xsl:variable>
  	
  	  <div class="sectionSubHeader">
        <a>
          <xsl:attribute name="class">plainLink</xsl:attribute>
          <xsl:attribute name="name">imageLibraryIndex_<xsl:number value="$imagelibraryid" format="1" /></xsl:attribute>
          <xsl:value-of select="Name"/>
        </a>
      </div>
      
	  <div class="sectionContent">
        <table class="reportContentTable">
          <tr>
            <td class="doubleColumnLabel">Type:</td>
            <td class="doubleColumnValue">
              <xsl:variable name="type">
                <xsl:call-template name="capitalize">
                  <xsl:with-param name="string" select="@type"/>
                </xsl:call-template>
              </xsl:variable>
              <xsl:value-of select="$type"/>
            </td>
          </tr>
          <xsl:if test="$showcontent='true'">
          	<xsl:variable name="backhref">imageLibrarySelectionIndex_<xsl:value-of select="$imagelibraryid"/></xsl:variable>
          	
          	<tr>
              <td class="singleColumnLabel">Content:</td>
              <td class="singleColumnValue">
                <xsl:if test="$imagelibrarytype='Dynamic'">
                  <div>
                    <b>
                      <a class="plainLink">
                        <xsl:attribute name="name"><xsl:value-of select="$backhref"/></xsl:attribute>
                        <xsl:value-of select="$message[@id='xslt_messages.image.library.content.selections']" />
                      </a>
                    </b>
                  </div>
                </xsl:if>
                
                <xsl:choose>
                  <xsl:when test="$suppressed='true'">
                    <div class="contentContainer">
                      <i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i>
                    </div>
                  </xsl:when>
                  <xsl:otherwise>
                    <xsl:apply-templates mode="TreeView" select="SelectableContent/Selection">
                      <xsl:with-param name="imagelibraryid" select="$imagelibraryid"/>
                    </xsl:apply-templates>

                    <xsl:for-each select="Content">
                      <div>
                        <xsl:variable name="locale">
                          <xsl:call-template name="translatelang">
                            <xsl:with-param name="code" select="@locale"/>
                          </xsl:call-template>
                        </xsl:variable>
                        <xsl:value-of select="$locale"/>
                      </div>

                      <xsl:call-template name="imagedata">
                        <xsl:with-param name="imagename" select="@imagename"/>
                        <xsl:with-param name="uploaded" select="@uploaded"/>
                      </xsl:call-template>
				      
                      <div class="contentContainer">
                        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
                        <xsl:value-of disable-output-escaping="yes" select="." />
                      </div>
                    </xsl:for-each>
                    
                    <xsl:for-each select="Contents/Content">
                      <div>
                        <xsl:variable name="locale">
                          <xsl:call-template name="translatelang">
                            <xsl:with-param name="code" select="@locale"/>
                          </xsl:call-template>
                        </xsl:variable>
                        <xsl:value-of select="$locale"/>
                      </div>

                      <xsl:call-template name="imagedata">
                        <xsl:with-param name="imagename" select="@imagename"/>
                        <xsl:with-param name="uploaded" select="@uploaded"/>
                      </xsl:call-template>                     

                      <div class="contentContainer">			                                
                        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
                        <xsl:value-of disable-output-escaping="yes" select="." />
                      </div>
                    </xsl:for-each>

                    <xsl:apply-templates mode="imagelibrary" select="SelectableContent/Selection">
                      <xsl:with-param name="backhref" select="$backhref"/>
                      <xsl:with-param name="imagelibraryid" select="$imagelibraryid"/>
                    </xsl:apply-templates>
                  </xsl:otherwise>
                </xsl:choose>                
              </td>          
          	</tr>
          </xsl:if>
        </table>	  
	  </div>  
  </xsl:template>

  <!-- Smart Texts Templates below -->
  <xsl:template match="SmartTexts">
  	<xsl:for-each select="SmartText">
  	  <xsl:apply-templates select="."/>
  	</xsl:for-each>
  </xsl:template>

    <!-- SmartT Text Templates below -->
  <xsl:template match="SmartText">
  	  <xsl:param name="suppressed">false</xsl:param>
  	  
      <xsl:variable name="smarttextid">
        <xsl:value-of select="@id"/>
      </xsl:variable>
      <xsl:variable name="imagelibrarytype">
        <xsl:value-of select="@type"/>
      </xsl:variable>
  	
  	  <div class="sectionSubHeader">
        <a>
          <xsl:attribute name="class">plainLink</xsl:attribute>
          <xsl:attribute name="name">smartTextIndex_<xsl:number value="$smarttextid" format="1" /></xsl:attribute>
          <xsl:value-of select="Name"/>
        </a>
      </div>
      
	  <div class="sectionContent">
        <table class="reportContentTable">
          <tr>
            <td class="doubleColumnLabel">Type:</td>
            <td class="doubleColumnValue">
              <xsl:variable name="type">
                <xsl:call-template name="capitalize">
                  <xsl:with-param name="string" select="@type"/>
                </xsl:call-template>
              </xsl:variable>
              <xsl:value-of select="$type"/>
            </td>
          </tr>
          <xsl:if test="$showcontent='true'">
          	<xsl:variable name="backhref">imageLibrarySelectionIndex_<xsl:value-of select="$smarttextid"/></xsl:variable>
          	
          	<tr>
              <td class="singleColumnLabel">Content:</td>
              <td class="singleColumnValue">
                <xsl:if test="$imagelibrarytype='Dynamic'">
                  <div>
                    <b>
                      <a class="plainLink">
                        <xsl:attribute name="name"><xsl:value-of select="$backhref"/></xsl:attribute>
                        <xsl:value-of select="$message[@id='xslt_messages.image.library.content.selections']" />
                      </a>
                    </b>
                  </div>
                </xsl:if>
                
                <xsl:choose>
                  <xsl:when test="$suppressed='true'">
                    <div class="contentContainer">
                      <i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i>
                    </div>
                  </xsl:when>
                  <xsl:otherwise>
                    <xsl:apply-templates mode="TreeView" select="SelectableContent/Selection">
                      <xsl:with-param name="imagelibraryid" select="$smarttextid"/>
                    </xsl:apply-templates>

                    <xsl:for-each select="Content">
                      <div>
                        <xsl:variable name="locale">
                          <xsl:call-template name="translatelang">
                            <xsl:with-param name="code" select="@locale"/>
                          </xsl:call-template>
                        </xsl:variable>
                        <xsl:value-of select="$locale"/>
                      </div>

                      <xsl:call-template name="imagedata">
                        <xsl:with-param name="imagename" select="@imagename"/>
                        <xsl:with-param name="uploaded" select="@uploaded"/>
                      </xsl:call-template>
				      
                      <div class="contentContainer">
                        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
                        <xsl:value-of disable-output-escaping="yes" select="." />
                      </div>
                    </xsl:for-each>
                    
                    <xsl:for-each select="Contents/Content">
                      <div>
                        <xsl:variable name="locale">
                          <xsl:call-template name="translatelang">
                            <xsl:with-param name="code" select="@locale"/>
                          </xsl:call-template>
                        </xsl:variable>
                        <xsl:value-of select="$locale"/>
                      </div>

                      <xsl:call-template name="imagedata">
                        <xsl:with-param name="imagename" select="@imagename"/>
                        <xsl:with-param name="uploaded" select="@uploaded"/>
                      </xsl:call-template>                     

                      <div class="contentContainer">			                                
                        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.language']" /></i></xsl:if>
                        <xsl:value-of disable-output-escaping="yes" select="." />
                      </div>
                    </xsl:for-each>

                    <xsl:apply-templates mode="imagelibrary" select="SelectableContent/Selection">
                      <xsl:with-param name="backhref" select="$backhref"/>
                      <xsl:with-param name="imagelibraryid" select="$smarttextid"/>
                    </xsl:apply-templates>
                  </xsl:otherwise>
                </xsl:choose>                
              </td>          
          	</tr>
          </xsl:if>
        </table>	  
	  </div>  
  </xsl:template>
</xsl:stylesheet>
