<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

  <xsl:include href="reportStyleTemplate.xsl"/>
  
  <xsl:template match="/">
  <html>
  	<xsl:call-template name="ReportHead"/>
  		<body>
    	<xsl:call-template name="ReportContents" />
    	<xsl:call-template name="ReportSummary" />
    	<xsl:call-template name="Project" />
    	<xsl:call-template name="ProjectTasks" />
  		</body>
	</html>
  </xsl:template>
  <xsl:template name="ReportContents">
    <div class="sectionHeader">
      <a name="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.contents']" /></a>
    </div>
    <div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <a href="#section_A"><xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
      </div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <a href="#section_B"><xsl:value-of select="$message[@id='xslt_messages.project']" /></a>
      </div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> C: <a href="#section_C"><xsl:value-of select="$message[@id='xslt_messages.project.tasks']" /></a>
      </div>
    </div>
    <br />
  </xsl:template>
  <xsl:template name="ReportSummary">
    <div class="sectionHeader">
      <a class="sectionHeaderPlainLink" name="section_A" href="#contents"><xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
    </div>
    <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.request.overview']" /></div>
    <xsl:apply-templates select="//ProjectAudit" />
    <xsl:apply-templates select="//ProjectAudit/Project/ProjectTasks">
      <xsl:with-param name="bProjectTaskIndex">True</xsl:with-param>
    </xsl:apply-templates>
  </xsl:template>
  <xsl:template match="//ProjectAudit">
    <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.export.version']" />: </td>
          <td>
            <xsl:value-of select="./@ver"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested.by']" />: </td>
          <td>
            <xsl:value-of select="./Metadata/User"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested']" />: </td>
          <td>
            <xsl:call-template name="FormatDate">
              <xsl:with-param name="DateTime">
                <xsl:value-of select="./Metadata/RequestDate"/>
              </xsl:with-param>
            </xsl:call-template>            
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.report.type']" />: </td>
          <td>
          	<xsl:value-of select="./Metadata/ReportType" />
          </td>
        </tr>
      </table>
    </div>
  </xsl:template>
  <xsl:template name="Project">
    <div class="sectionHeader">
      <a name="section_B" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <xsl:value-of select="$message[@id='xslt_messages.project']" /></a>
    </div>
    <xsl:apply-templates select="//ProjectAudit/Project">
      <xsl:with-param name="bProjectIndex">True</xsl:with-param>
    </xsl:apply-templates>
  </xsl:template>
  <xsl:template match="//ProjectAudit/Project">
  <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.project.details']" /></div>
  <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.project.id']" />: </td>
          <td>
            <xsl:value-of select="@id"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.project.name']" />: </td>
          <td>
            <xsl:value-of select="Name"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.project.duedate']" />: </td>
          <td>
            <xsl:call-template name="FormatDate">
              <xsl:with-param name="DateTime">
                <xsl:value-of select="DueDate"/>
              </xsl:with-param>
            </xsl:call-template>            
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.project.owner']" />: </td>
          <td>
          	<xsl:value-of select="Owner"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.metadata']" />:</td>
          <td class="singleColumnValue">
            <xsl:for-each select="//ProjectAudit/Project/MetadataFormItems/MetadataformItem">
            <xsl:variable name="itemindex"><xsl:value-of select="@id"/></xsl:variable>
            <div><xsl:value-of select="ItemLabel"/></div>
            <div class="contentContainer"><xsl:value-of select="ItemValue"/></div>
            </xsl:for-each>
          </td>
        </tr>
      </table>
  </div>
  <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.project.status']" /></div>
  <div class="sectionContent">
    <table class="reportContentTable">
      <xsl:choose>
      	<xsl:when test="count(ProjectTasks/ProjectTask) = 0">
      	    <xsl:value-of select="$message[@id='xslt_messages.no.tasks.assigned']" />
      	</xsl:when>
      	<xsl:otherwise>
	        <xsl:if test="count(WorkFlowSteps/WorkFlowStep) > 0">
	          <tr>
	            <td></td>
	            <xsl:for-each select="./WorkFlowSteps/WorkFlowStep">
	            <td><div><xsl:value-of select="Name"/></div></td>
	            </xsl:for-each>
	          </tr>
	        </xsl:if>
	      
	        <xsl:for-each select="//Project/ProjectTasks/ProjectTask">
	        <xsl:variable name="taskId">
	        <xsl:value-of select="@id"/>
	        </xsl:variable>
	        <xsl:variable name="taskCrtStepId">
	        <xsl:value-of select="@currentstepid"/>
	        </xsl:variable>
	        <tr>
	          <td style="padding-right: 0px;">
	          <!-- TASK NAME -->
			  <div style="border-bottom: 1px solid #999; padding: 5px 10px 5px 0px; white-space: nowrap;">
	          <xsl:value-of select="Name"/>
	          </div>
	          <div style="padding-top: 6px;">
	          <!-- STATUS -->
	          <xsl:value-of select="StatusDescription"/></div>
	          </td>
	          <xsl:for-each select="//Project/WorkFlowSteps/WorkFlowStep">
	            <xsl:variable name="stepId">
	            <xsl:value-of select="@id"/>
	            </xsl:variable>
	            <xsl:choose>
	    			<xsl:when test="$stepId=$taskCrtStepId">
	    				<td class="taskStepDataCell currentStepContainer">
				            <xsl:for-each select="//Project/ProjectTasks/ProjectTask/TaskWFSteps/TaskWFStep">
				              <xsl:if test="@id=$stepId and @taskid=$taskId">
				              <xsl:choose>
				    			<xsl:when test="@status='Active' or @status='Complete' or @status='Overdue'">
				                  <div class="taskStepContainer activeTaskContainer">
				                  	<xsl:choose>
				                    <xsl:when test="count(Assigned) > 0">
				                      <div class="taskAssigneeLabel"><xsl:value-of select="$message[@id='xslt_messages.assingee']" /></div>
				                      <div class="userAction_none"><xsl:value-of select="Assigned"/></div>
				                    </xsl:when>
				                    <xsl:otherwise>
						              <div class="taskAssigneeLabel"><xsl:value-of select="$message[@id='xslt_messages.approvers']" /></div>
						              <div class="stepUsersContainer">
						                <xsl:for-each select="./Approvers/Approver">
						                  <div class="userAction_none"><xsl:value-of select="Name"/></div>
						            	</xsl:for-each>
						              </div>
						            </xsl:otherwise>
						           </xsl:choose>
						            </div>
				            	</xsl:when>
				            	<xsl:otherwise>
				            	  <div class="taskStepContainer">
						              <div class="taskAssigneeLabel"><xsl:value-of select="$message[@id='xslt_messages.approvers']" /></div>
						              <div class="stepUsersContainer">
						                <xsl:for-each select="./Approvers/Approver">
						                  <div class="userAction_none"><xsl:value-of select="Name"/></div>
						            	</xsl:for-each>
						              </div>
						            </div>
				            	</xsl:otherwise>
				            	</xsl:choose>
				              </xsl:if>
				            </xsl:for-each>
				           </td>
	    			</xsl:when>
	    			<xsl:otherwise>
	    				<td class="taskStepDataCell">
				            <xsl:for-each select="//Project/ProjectTasks/ProjectTask/TaskWFSteps/TaskWFStep">
				              <xsl:if test="@id=$stepId and @taskid=$taskId">
				              <xsl:choose>
				    			<xsl:when test="@status='Active' or @status='Complete' or @status='Overdue'">
				                  <div class="taskStepContainer activeTaskContainer">
						              <div class="taskAssigneeLabel"><xsl:value-of select="$message[@id='xslt_messages.approvers']" /></div>
						              <div class="stepUsersContainer">
						                <xsl:for-each select="./Approvers/Approver">
						                  <div class="userAction_none"><xsl:value-of select="Name"/></div>
						            	</xsl:for-each>
						              </div>
						            </div>
				            	</xsl:when>
				            	<xsl:otherwise>
				            	  <div class="taskStepContainer">
						              <div class="taskAssigneeLabel"><xsl:value-of select="$message[@id='xslt_messages.approvers']" /></div>
						              <div class="stepUsersContainer">
						                <xsl:for-each select="./Approvers/Approver">
						                  <div class="userAction_none"><xsl:value-of select="Name"/></div>
						            	</xsl:for-each>
						              </div>
						            </div>
				            	</xsl:otherwise>
				            	</xsl:choose>
				              </xsl:if>
				            </xsl:for-each>
				           </td>
	    			</xsl:otherwise>
	    		</xsl:choose>
	          </xsl:for-each>
	        </tr>
	        </xsl:for-each>
        </xsl:otherwise>
      </xsl:choose>
    </table>
  </div>
  </xsl:template>
  <xsl:template name="ProjectTasks">
    <div class="sectionHeader">
      <a name="section_C" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> C: <xsl:value-of select="$message[@id='xslt_messages.project.tasks']" /></a>
    </div>
    <xsl:apply-templates select="//ProjectAudit/Project/ProjectTasks" />
  </xsl:template>
  <xsl:template match="//ProjectAudit/Project/ProjectTasks" >
    <xsl:param name="bProjectTaskIndex" />
    <div class="sectionSubHeader">
      <a href="#ProjectIndex" name="ProjectIndex" class="plainLink">
        <xsl:value-of select="../Name"/>
      </a>
    </div>
    <xsl:choose>
      <xsl:when test="$bProjectTaskIndex='True'">
        <div class="sectionContent">
          <ul>
            <xsl:call-template name="OutputTask">
              <xsl:with-param name="bProjectTaskIndex">
                <xsl:value-of select="$bProjectTaskIndex"/>
              </xsl:with-param>
            </xsl:call-template>
          </ul>
        </div>
      </xsl:when>
      <xsl:otherwise>
        <xsl:call-template name="OutputTask">
          <xsl:with-param name="bProjectTaskIndex">
            <xsl:value-of select="$bProjectTaskIndex"/>
          </xsl:with-param>
        </xsl:call-template>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>
  <xsl:template name="OutputTask">
    <xsl:param name="bProjectTaskIndex" />
    <xsl:for-each select="./ProjectTask">
      <xsl:variable name="seq" select="position()" />
      <xsl:variable name="TaskName">
        <xsl:value-of select="./Name"/>
      </xsl:variable>
      <xsl:variable name="HrefTTaskName">#<xsl:value-of select="./Name"/>
      </xsl:variable>
      <xsl:choose>
        <xsl:when test="$bProjectTaskIndex='True'">
          <li>
            <a href="{$HrefTTaskName}{$seq}" class="plainLink">
              <xsl:value-of select="$TaskName"/>
            </a>
          </li>
        </xsl:when >
        <xsl:otherwise>
          <div class="sectionSubHeader"><a name="{$TaskName}{$seq}" href="#ProjectTaskIndex" class="plainLink"><xsl:value-of select="$TaskName"/></a></div>
          <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.details']" /></div>
		  <div class="sectionContent">
		    <table class="reportContentTable">
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.ID']" />: </td>
		          <td><xsl:value-of select="@id"/></td>
		        </tr>
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.name']" />: </td>
		          <td><xsl:value-of select="Name"/></td>
		        </tr>
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.status']" />: </td>
		          <td><xsl:value-of select="Status"/></td>
		        </tr>
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.createddate']" />: </td>
		          <td><xsl:call-template name="FormatDate">
		              <xsl:with-param name="DateTime">
		                <xsl:value-of select="CreatedDate"/>
		              </xsl:with-param>
		            </xsl:call-template>            
		          </td>
		        </tr>
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.project.duedate']" />: </td>
		          <td><xsl:call-template name="FormatDate">
		              <xsl:with-param name="DateTime">
		                <xsl:value-of select="DueDate"/>
		              </xsl:with-param>
		            </xsl:call-template>            
		          </td>
		        </tr>
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.assingee']" />: </td>
		          <td><xsl:value-of select="Assignee"/></td>
		        </tr>
		        <xsl:if test="count(TaskMetadataFormItems/TaskMetadataformItem) > 0">
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.metadata']" />:</td>
		          <td class="singleColumnValue">
		            <xsl:for-each select="./TaskMetadataFormItems/TaskMetadataformItem">
		            <xsl:variable name="taskitemindex"><xsl:value-of select="@id"/></xsl:variable>
		            <div><xsl:value-of select="ItemLabel"/></div>
		            <div class="contentContainer"><xsl:value-of select="ItemValue"/></div>
		            </xsl:for-each>
		          </td>
		          </tr>
		        </xsl:if>
		        <xsl:if test="count(TaskSmartTexts/TaskSmartText) > 0">
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.smart.text']" />: </td>
		          <td><div class="contentContainer">
		          <xsl:for-each select="./TaskSmartTexts/TaskSmartText">
		          <p><xsl:value-of select="Name"/></p>
		          </xsl:for-each>
		          </div>
		          </td>
		        </tr>
		        </xsl:if>
		        <xsl:if test="count(TaskImageLibraries/TaskImageLibrary) > 0">
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.image.library']" />: </td>
		          <td><div class="contentContainer">
		          <xsl:for-each select="./TaskImageLibraries/TaskImageLibrary">
		          <p><xsl:value-of select="Name"/></p>
		          </xsl:for-each>
		          </div>
		          </td>
		        </tr>
		        </xsl:if>
		       <xsl:if test="count(TargetGroups/TargetGroup) > 0">
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.target.groups']" />: </td>
		          <td><div class="contentContainer">
		          <xsl:for-each select="./TargetGroups/TargetGroup">
		          <p><xsl:value-of select="Name"/></p>
		          </xsl:for-each>
		          </div>
		          </td>
		        </tr>
		        </xsl:if>
		        <xsl:if test="count(Inserts/Insert) > 0">
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.inserts']" />: </td>
		          <td><div class="contentContainer">
		          <xsl:for-each select="./Inserts/Insert">
		          <p><xsl:value-of select="Name"/></p>
		          </xsl:for-each>
		          </div>
		          </td>
		        </tr>
		        </xsl:if>
		      </table>
		  	</div>
		  	<xsl:if test="count(Touchpoints/Touchpoint) > 0">
		  	<xsl:for-each select="./Touchpoints/Touchpoint">
		  	<div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.touchpoint']" />: <xsl:value-of select="Name"/></div>
		    <div class="sectionContent">
		    <table class="reportContentTable">
		      <xsl:if test="count(./Messages/Message) > 0">
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.messages']" />: </td>
		          <td><div class="contentContainer">
		          <xsl:for-each select="./Messages/Message">
		          <p><xsl:value-of select="Name"/></p>
		          </xsl:for-each>
		          </div>
		          </td>
		        </tr>
		        </xsl:if>
		        <xsl:if test="count(./LocalTexts/LocalText) > 0">
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.local.smart.texts']" />: </td>
		          <td><div class="contentContainer">
		          <xsl:for-each select="./LocalTexts/LocalText">
		          <p><xsl:value-of select="Name"/></p>
		          </xsl:for-each>
		          </div>
		          </td>
		        </tr>
		        </xsl:if>
		        <xsl:if test="count(./Selections/Selection) > 0">
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.touchpoint.selections']" />: </td>
		          <td><div class="contentContainer">
		          <xsl:for-each select="./Selections/Selection">
		          <p><xsl:value-of select="Name"/></p>
		          </xsl:for-each>
		          </div>
		          </td>
		        </tr>
		        </xsl:if>
		        <xsl:if test="count(./InsertSchedules/InsertSchedule) > 0">
		        <tr>
		          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.insert.schedules']" />: </td>
		          <td><div class="contentContainer">
		          <xsl:for-each select="./InsertSchedules/InsertSchedule">
		          <p><xsl:value-of select="Name"/></p>
		          </xsl:for-each>
		          </div>
		          </td>
		        </tr>
		        </xsl:if>
		     </table>
		   </div>
		   
		   </xsl:for-each>
		   </xsl:if>
		   <xsl:if test="(Approvals)">
		    <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.workflow.histories']" /></div>
	        <xsl:apply-templates select="Approvals/ApprovalHistory" />
           </xsl:if>
		</xsl:otherwise>
      </xsl:choose>
    </xsl:for-each>
  </xsl:template>
  <xsl:template match="Approvals/ApprovalHistory">
    <xsl:choose>    
    <xsl:when test="./Descript">
    <div class="sectionContent">
    <table class="reportContentTable">
      <tr>
        <td>
          <xsl:if test="Descript">
            <table class="outsideTable" width="100%">
              <tr>
                <td class="tableHeader" width="25%" ><xsl:value-of select="$message[@id='xslt_messages.state']" /></td>
                <td class="tableHeader" width="14%" ><xsl:value-of select="$message[@id='xslt_messages.action']" /></td>
                <td class="tableHeader" width="17%" ><xsl:value-of select="$message[@id='xslt_messages.date']" /></td>
                <td class="tableHeader" width="12%" ><xsl:value-of select="$message[@id='xslt_messages.user']" /></td>                
                <td class="tableHeader" width="12%" ><xsl:value-of select="$message[@id='xslt_messages.assigned.to']" /></td>
                <td class="tableHeader" width="20%" ><xsl:value-of select="$message[@id='xslt_messages.notes']" /></td>
              </tr>
              <xsl:for-each select="Descript">
                <tr>
                  <td>
                    <xsl:value-of select="."/>
                  </td>
                  <td>
                    <xsl:value-of select="./@action"/>
                  </td>
                  <td>
                    <xsl:call-template name="FormatDate">
                      <xsl:with-param name="DateTime">
                        <xsl:value-of select="./@date" />
                      </xsl:with-param>
                	  <xsl:with-param name="noHour">true</xsl:with-param>
                	  <xsl:with-param name="noMinute">true</xsl:with-param>                       
                    </xsl:call-template>
                  </td>
                  <td>
                    <xsl:value-of select="./@user"/>
                  </td>                  
                  <td>
                    <xsl:value-of select="./@assignedTo"/>
                  </td>
                  <td>
                    <xsl:value-of select="./@notes"/>
                  </td>                  
                </tr>
              </xsl:for-each>
            </table>
          </xsl:if>
        </td>
      </tr>
    </table>
    </div>
    </xsl:when>
    <xsl:otherwise>
    <table class="reportContentTable">
      <tr>
        <td width="14%"></td>
        <td class="outsideTable" ><xsl:value-of select="$message[@id='xslt_messages.no.events.for.model']" /></td>
        <td width="16%"></td> 
      </tr>
     </table>   
     <br />
    </xsl:otherwise>
    </xsl:choose>
  </xsl:template>
  </xsl:stylesheet>