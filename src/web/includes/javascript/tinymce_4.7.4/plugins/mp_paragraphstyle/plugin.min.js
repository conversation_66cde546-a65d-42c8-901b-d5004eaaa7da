
tinymce.PluginManager.add('mp_paragraphstyle', function(ed, url) {

	// Iterate until a P tag is found; return P tag
	function getParagraphNode(n) {
		do {
			if (n.nodeName && (n.nodeName.toLowerCase() == "p" || n.nodeName.toLowerCase() == "li")) {
				return n;
			}
		} while (n = n.parentNode);
		return null;
	}

	// Iterate until a UL/OL tag is found; return UL/OL tag
	function getListNode(n) {
		do {
			if (n.nodeName && (n.nodeName.toLowerCase() == "ul" || n.nodeName.toLowerCase() == "ol")) {
				return n;
			}
		} while (n = n.parentNode);
		return null;
	}
	
	// Recursive tree walker: Marks when selection start node and end node have been 
	// 		detected.  Any node being examined when the start node has been found
	//		but before the end node has been found is considered 'selected'
	function buildSelectedBlocksList(node) {
		
		var t = ed.settings.paragraphstyle;

		if ( node == t.selection.start_block )
			t.selection.found_start_block = true;

		if (t.selection.found_start_block && !t.selection.found_end_block)
			t.selection.selected_blocks.push(node);
		
		if ( node == t.selection.end_block )
			t.selection.found_end_block = true;
		
		$(node).children().each( function() {
			buildSelectedBlocksList(this);
		});
	}

	function buildSelectedTableCells () {
		let nodes = ed.dom.select('td[data-mce-selected],th[data-mce-selected]');
		let pTags = $(nodes).children('p');
		for (let i = 0; i < pTags.length; i++) {
			t.selection.selected_blocks = t.selection.selected_blocks.concat(pTags[i]);
		}
	}
	
	function cleanListItemNodes() {
		
		var t = ed.settings.paragraphstyle;
		
		$(ed.getDoc()).find('li').each( function() {
			var styleAttr = $(this).attr(t.paragraph_style_attr);
			if ( styleAttr != undefined ) {
				var styleValue = styleAttr.replace(t.class_prefix,'').split(t.indicator.primary_joiner)[0];
				if ( t.paragraph_style_is_bullet_enabled_array[styleValue] != undefined && !t.paragraph_style_is_bullet_enabled_array[styleValue] )
					$(this).removeAttr(t.paragraph_style_attr);
			}
		});
	}
	
	// Resolve the common ancestor of two nodes
	function getCommonAncestor(a, b) {
	    var parents= $(a).parents().andSelf();
	    while (b) {
	        var ix= parents.index(b);
	        if (ix!==-1)
	            return b;
	        b= b.parentNode;
	    }
	    return null;
	}
	
	// Custom selection algorithm: Walks tree and resolves all selected blocks
	function getAllSelectedBlocks(start, end) {
		
		var t = ed.settings.paragraphstyle;
		
	    var ancestor = getCommonAncestor(start, end);

	    t.selection = {};
	    $.extend(t.selection,	{	found_start_block: false, 
			    					start_block: start, 
			    					found_end_block: false, 
			    					end_block:end, 
			    					selected_blocks: []
	    					      });

	    buildSelectedBlocksList(ancestor);
		buildSelectedTableCells();
	}
	
	// REGISTER STYLES: Default content
	function refreshAppliedStyleClasses() {
		
		function getStyleData(styleBase) {
			var requestParam = "connector=" + styleBase + "&type=paragraphStyle";
			if ( ed.settings.zone_id != undefined && ed.settings.zone_id != null )
				requestParam += "&zoneId=" + ed.settings.zone_id;
			
			var stampDate = new Date();
			$.ajax({
				type: "GET",
				url: context + "/getObjectInfo.form?" + requestParam + "&cacheStamp=" + (stampDate.getTime()),
				dataType: "json",
				success: function(data) {
					data = mpParseJSON( data[0] );
					if ( data != undefined && data.has_variations ) {
						if ( data.variation_data.toggle_line_spacing )
							data.variation_data.line_spacings = mpParseJSON(data.variation_data.line_spacings);
					}

					t.data[styleBase] = data;
					refreshAppliedStyleClasses();
				}
			});	
		}
		
		var t = ed.settings.paragraphstyle;

		$(ed.getBody()).find('[paragraphclass]').each( function() {

			var currentStyle = $(this).attr('paragraphclass').replace(t.class_prefix,'');
			var styleBase = currentStyle.split(t.indicator.primary_joiner)[0];
			
			if ( !t.data[styleBase] && ed.settings.paragraphstyle.styles_post_loaded.indexOf(styleBase) == -1 ) {
				ed.settings.paragraphstyle.styles_post_loaded[ed.settings.paragraphstyle.styles_post_loaded.length] = styleBase;
				getStyleData(styleBase);
			}
			
			if ( t.data[styleBase] && !ed.formatter.get(currentStyle) ) {
				
				var cssAttrs = new Array();
				cssAttrs.push(t.data[styleBase].css_attributes);
				
				if ( currentStyle.indexOf(t.indicator.primary_joiner) != -1 &&
					 t.data[styleBase].variation_data != null ) {
				
					styleAttr = currentStyle
									.split(t.indicator.primary_joiner)[1]
									.split(t.indicator.sub_joiner);

					for ( var j=0; j < styleAttr.length; j++ ) {
						
						if ( t.data[styleBase].variation_data.toggle_alignment ) {
							if ( styleAttr[j].indexOf("AL") != -1 )
								cssAttrs.push("text-align: left;");
							else if ( styleAttr[j].indexOf("AC") != -1 )
								cssAttrs.push("text-align: center;");
							else if ( styleAttr[j].indexOf("AR") != -1 )
								cssAttrs.push("text-align: right;");
							else if ( styleAttr[j].indexOf("AJ") != -1 )
								cssAttrs.push("text-align: justify;");
						}
						
						if ( t.data[styleBase].variation_data.toggle_line_spacing ) {
							var isFixedSpacing = t.data[styleBase].is_fixed_spacing;
							var scaleFactor = isFixedSpacing ? t.dspScale : 1;
							var unit = isFixedSpacing ? "pt" : "";

							if ( styleAttr[j].indexOf("LS") != -1 ) {
								var lineSpacingValue = parseFloat(styleAttr[j].replace("LS","").replace("_",".")) * scaleFactor;
								cssAttrs.push("line-height: " + lineSpacingValue + unit + ";");
							}
						}
						
						if ( t.data[styleBase].variation_data.toggle_left_margin ) {
							if ( styleAttr[j].indexOf("M") != -1 ) {
								var appliedMargin = getAppliedStyleAttr("leftmargin", $(this).attr('paragraphclass'));
								appliedMargin = appliedMargin != null ? parseFloat(appliedMargin) : 0;
								cssAttrs.push("margin-left: " + (appliedMargin * t.dspScale) + "px");
							}
						}

					};

				};

				// REGISTER STYLE: If not already declared
				var style = t.class_prefix + currentStyle;
				ed.formatter.register(style, {
					block		: 'p,li',
					attributes	: {
						paragraphclass	: style
					}
				});

				// Paragraph
				ed.dom.addStyle("[paragraphclass='" + style + "']:not(li) {" + cssAttrs.join(' ') + "}");
				ed.dom.addStyle("li[paragraphclass='" + style + "'] div.mceListItemContent {" + cssAttrs.join(' ') + "}");

			};

		});
	}

	function handleListTextAlign(node) {
		var originalListAttr = ed.settings.mp_fn.get_original_list_attributes($(node));
		var originalTextAlign = originalListAttr.text_align;
		var textAlign = getComputedStyle(node, 'text-align');

		if (!p.alignleft.disabled() && p.alignleft.active() && textAlign !== 'left') {
			$(node).attr("text_align", "left");
		} else if (!p.alignright.disabled() && p.alignright.active() && textAlign !== 'right') {
			$(node).attr("text_align", "right");
		} else if (!p.aligncenter.disabled() && p.aligncenter.active() && textAlign !== 'center') {
			$(node).attr("text_align", "center");
		} else if (!p.alignjustify.disabled() && p.alignjustify.active() && textAlign !== 'justify') {
			$(node).attr("text_align", "justify");
		}

		if (originalTextAlign === $(node).attr("text_align")) {
			$(node).removeAttr("text_align");
		}
	}
	
	// Applied selected style to target node.  If selected style is null, removes styling from target node
	function applyParagraphStyle(targetNode, selectedStyle) {

		function setCustomStyles(targetNode, selectedStyle) {

			if (targetNode && p.customParagraphStyles) {
				var isFixedSpacing = false;
				var marginIncr = $(targetNode).attr("tab_incr") ? ($(targetNode).attr("tab_incr") * 100) : 25;
				var spacing = false;

				if (t.data[p.paragraphstyle.value()] != undefined) {
					isFixedSpacing = t.data[p.paragraphstyle.value()].is_fixed_spacing;
					var variationData = t.data[p.paragraphstyle.value()].variation_data;

					if (variationData) {
						spacing = variationData.line_spacings ? variationData.line_spacings[0] : false;
						marginIncr = variationData.margin_incr;
					}
				}

				var textAlign = getComputedStyle(targetNode, 'text-align');
				if (!p.alignleft.disabled() && p.alignleft.active() && textAlign !== 'left') {
					$(targetNode).attr("text_align", "left");
				} else if (!p.alignright.disabled() && p.alignright.active() && textAlign !== 'right') {
					$(targetNode).attr("text_align", "right");
				} else if (!p.aligncenter.disabled() && p.aligncenter.active() && textAlign !== 'center') {
					$(targetNode).attr("text_align", "center");
				} else if (!p.alignjustify.disabled() && p.alignjustify.active() && textAlign !== 'justify') {
					$(targetNode).attr("text_align", "justify");
				}

				if (!p.linespacing.disabled()) {
					var selectedLineSpacing = p.linespacing.value();

					if (selectedLineSpacing) {
						var unit = isFixedSpacing ? "pt" : "";
						var lineSpacingValue = parseFloat(selectedLineSpacing);
						if (spacing !== selectedLineSpacing) {
							$(targetNode).attr("line_spacing", lineSpacingValue + unit);
						}
					}
				}

				var appliedMargin = getAppliedStyleAttr("leftmargin", $(targetNode).attr(t.paragraph_style_attr));
				var newMargin = 0;

				var spacingAttr = $(targetNode).attr("spacing");
				var spacingValues = spacingAttr
					? spacingAttr.split(':').map(function (val) {
						return parseFloat(val.replace('in', ''));
					}) : [0, 0, 0, 0];

				if (appliedMargin) {
					appliedMargin = parseFloat(appliedMargin);
				} else {
					appliedMargin = spacingValues[3] * 100;
				}

				if (!p.indent.disabled() && p.indent.active()) {
					newMargin = marginIncr + appliedMargin;
					spacingValues[3] = (newMargin) / 100;
				} else if (!p.outdent.disabled() && p.outdent.active()) {
					newMargin = Math.max(0, appliedMargin - marginIncr);
					spacingValues[3] = (newMargin) / 100;
				}

				if ((!p.indent.disabled() && p.indent.active()) || (!p.outdent.disabled() && p.outdent.active())) {
					var spacing = spacingValues.map(function (val) {
						return val + 'in';
					}).join(':');
					$(targetNode).attr("spacing", spacing);
				}
			}
		}

		function setStyle(targetNode, selectedStyle) {
			if (!targetNode) return;

			var paragraphClass = $(targetNode).attr(t.paragraph_style_attr);
			var variantIndicators 	= [];
			var cssAttrs			= [];
			var isFixedSpacing 		= false;
			var scaleFactor 		= 1;
			var marginIncr 			= $(targetNode).attr("tab_incr") ? ($(targetNode).attr("tab_incr") * 100) : 25;


			var currentStyle = selectedStyle || '';
			var currentParagraphClass = paragraphClass || '';

			// Remove custom paragraph attributes if the selected style is different from the current style or if the selected style is null
			if (currentStyle !== currentParagraphClass.split(t.indicator.primary_joiner)[0]) {
				var customParagraphAttr = ['text_align', 'spacing', 'tab_incr', 'line_spacing', 'indent_type', 'special_indent','custom_paragraph_id'];

				customParagraphAttr.map(function(property) {
					$(targetNode).removeAttr(property);
				});
			}

			// Remove pre-existing paragraph styling
			if (paragraphClass && paragraphClass.toLowerCase().indexOf(t.class_prefix.toLowerCase()) !== -1) {
				$(targetNode).removeAttr(t.paragraph_style_attr);
			}

			var styleKey = p.paragraphstyle.value();
			if ( t.data[styleKey] != undefined ) {
				var styleData = t.data[styleKey];
				cssAttrs.push(styleData.css_attributes);
				isFixedSpacing = styleData.is_fixed_spacing;
				scaleFactor = isFixedSpacing ? t.dspScale : 1;

				if (styleData.variation_data){
					marginIncr = styleData.variation_data.margin_incr;
				}
			}

			if ( !p.alignleft.disabled() && p.alignleft.active() ) {
				variantIndicators.push("AL");
				cssAttrs.push("text-align: left;");
			} else if ( !p.alignright.disabled() && p.alignright.active() ) {
				variantIndicators.push("AR");
				cssAttrs.push("text-align: right;");
			} else if ( !p.aligncenter.disabled() && p.aligncenter.active() ) {
				variantIndicators.push("AC");
				cssAttrs.push("text-align: center;");
			} else if ( !p.alignjustify.disabled() && p.alignjustify.active() ) {
				variantIndicators.push("AJ");
				cssAttrs.push("text-align: justify;");
			} else {
				cssAttrs.push("text-align: left;");
			}

			if ( !p.linespacing.disabled() ) {
				var lineSpacing = p.linespacing.value();
				if (lineSpacing) {
					variantIndicators.push("LS"+lineSpacing.replace(".","_"));
					var unit = isFixedSpacing ? "pt" : "";
					var lineSpacingValue = parseFloat(lineSpacing) * scaleFactor;
					cssAttrs.push("line-height: " + lineSpacingValue + unit + ";");
				}
			}

			var appliedMargin = getAppliedStyleAttr("leftmargin", $(targetNode).attr(t.paragraph_style_attr));
			var newMargin = 0;

			var spacingAttr = $(targetNode).attr("spacing");
			var spacingValues = spacingAttr
				? spacingAttr.split(':').map(function(val) {
					return parseFloat(val.replace('in', ''));
				}): [0, 0, 0, 0];

			if (appliedMargin) {
				appliedMargin = parseFloat(appliedMargin);
			} else {
				appliedMargin = spacingValues[3] * 100;
			}

			if ( !p.indent.disabled() && p.indent.active() ) {
				newMargin = marginIncr + appliedMargin;
				variantIndicators.push(("M"+newMargin).replace(".","_"));
				cssAttrs.push("margin-left: " + (newMargin * scaleFactor) + "px");
			} else if ( !p.outdent.disabled() && p.outdent.active() ) {
				newMargin =  Math.max(0, appliedMargin - marginIncr);
				variantIndicators.push(("M"+newMargin).replace(".","_"));
				cssAttrs.push("margin-left: " + (newMargin * scaleFactor) + "px");
			}

			setCustomStyles(targetNode, selectedStyle);

			if (styleKey) {
				var style = t.class_prefix + styleKey;

				if ( variantIndicators.length > 0){
					style += t.indicator.primary_joiner + variantIndicators.join(t.indicator.sub_joiner);
				}

				// REGISTER STYLE: If not already declared
				if ( !ed.formatter.get(style) ) {
					ed.formatter.register(style, {
						block		: 'p,li',
						attributes	: {
							paragraphclass	: style
						}
					});
					// Paragraph
					ed.dom.addStyle("[paragraphclass='" + style + "']:not(li) {" + cssAttrs.join(' ') + "}");
					ed.dom.addStyle("li[paragraphclass='" + style + "'] div.mceListItemContent {" + cssAttrs.join(' ') + "}");
				}

				// STYLE CONTENT
				$(targetNode).attr(t.paragraph_style_attr, style);

			}

			if (p.customParagraphStyles && ed.settings.mp_fn.update_paragraph_styles_from_attributes) {
				ed.settings.mp_fn.update_paragraph_styles_from_attributes(targetNode, true);
				ed.settings.mp_fn.refresh_custom_paragraph_panel();
			}

		}
		
		var t = ed.settings.paragraphstyle;
		
		if ( selectedStyle === undefined )
			selectedStyle = p.paragraphstyle.value();

		if ( targetNode !== undefined ) {
			setStyle(targetNode, selectedStyle);
		} else {
			var selectedBlocks = ed.selection.getSelectedBlocks();
			if (selectedBlocks) {
				getAllSelectedBlocks(selectedBlocks[0],selectedBlocks[selectedBlocks.length-1]);

				var targetNode;
				var appliedBlocks = [];
				var selectedBlocks = t.selection.selected_blocks;

				for (var i = 0; i < selectedBlocks.length; i++) {
					var block = $(selectedBlocks[i]);

					// Bulleted lists: Do not apply style if style is not applicable for bulleted lists
					if (block.is('ul,ol,li') && selectedStyle != null && !t.paragraph_style_is_bullet_enabled_array[selectedStyle])
						continue;

					// Smart text: Do not apply style if target tag belongs to rendered smart text
					if (block.closest('.staticContentItem').length !== 0)
						continue;
					
					if (block.is('ul,ol')) {
						targetNode = getListNode(selectedBlocks[i]);
						handleListTextAlign(targetNode);
						ed.settings.mp_fn.update_list_styles_from_attributes(targetNode, true);
					} else if (block.is('li,.mceListItemContent')) {
						block.closest('ul,ol').each(function(){
							targetNode = getListNode(this);
							handleListTextAlign(targetNode);
							ed.settings.mp_fn.update_list_styles_from_attributes(targetNode, true);
						});
					} else {
						targetNode = getParagraphNode(selectedBlocks[i]);
						if (appliedBlocks.indexOf(targetNode) === -1) {
							setStyle(targetNode, selectedStyle);
							appliedBlocks[appliedBlocks.length] = targetNode;
						}
					}
	
				}
			}
		}
		
		ed.undoManager.add();
		
		toggleControlValues();
		
	}
	
	// PARSE STYLE ATTR: From current applied style
	function getAppliedStyleAttr(attr, style) {
		
		var t = ed.settings.paragraphstyle;

		var style = style != undefined && style != null ? style : null;
		if ( style == null && t.applied_style != null )
			style = t.applied_style;

		if ( style != null ) {
			var styleAttr = style.split(t.indicator.primary_joiner);
			if ( attr == "style" )
				return styleAttr[0];
			
			if ( styleAttr.length > 1 ) {
				styleAttr = styleAttr[1].split(t.indicator.sub_joiner);

				for ( var i=0; i < styleAttr.length; i++ ) {
					if ( attr == "align" && styleAttr[i].indexOf('A') != -1   )
						return styleAttr[i].replace("A","");
					if ( attr == "leftmargin" && styleAttr[i].indexOf('M') != -1 )
						return styleAttr[i].replace("M","").replace("_",".");
					if ( attr == "linespacing" && styleAttr[i].indexOf('LS') != -1 )	
						return styleAttr[i].replace("LS","").replace("_",".");

				}
			}
			
		}

		return null;
	}

	function getComputedStyle(node, style) {
		var pComputedStyle = window.getComputedStyle(node);

		switch (style) {
			case 'line-height':
				var fontSizePx = pComputedStyle.getPropertyValue('font-size');
				var lineHeightPx = pComputedStyle.getPropertyValue('line-height');

				var unitlessLineHeight = parseFloat(lineHeightPx) / parseFloat(fontSizePx);
				return unitlessLineHeight;
			default:
				return pComputedStyle.getPropertyValue(style);
		}
	}

	function getStyleAttr(css, attr) {
		if (css != undefined && css != '') {
			var attrArray = css.split(";");
			for (var i = 0; i < attrArray.length; i++) {
				var currentAttr = attrArray[i].split(":");
				if ($.trim(currentAttr[0]) == attr)
					return $.trim(currentAttr[1]);
			}
		}
	}
	
	// Disable nodes when style is not permitted for current context
	function toggleMenuItems() {
		
		var t = ed.settings.paragraphstyle;

		if ( t.plugin.menu != undefined ) {
		
			// Is style restricted?
			var styleRestricted = true;
			if ( ed.selection.getSelectedBlocks() ) {
				// Get target nodes
				var selectedBlocks = ed.selection.getSelectedBlocks();
				getAllSelectedBlocks(selectedBlocks[0],selectedBlocks[selectedBlocks.length-1]);
				for (j in t.selection.selected_blocks)
					if ( !$(t.selection.selected_blocks[j]).is('li') )
						styleRestricted = false;

			}

			// Toggle style enable/disable
			if ( t.plugin.menu != undefined ) {
				var menuItems = t.plugin.menu.items();
				for ( var i=0; i < menuItems.length; i++ ) {
					if ( menuItems[i]._id != undefined ) {
						if ( t.paragraph_style_is_bullet_enabled_array[ menuItems[i].value() ] || !styleRestricted )
							menuItems[i].disabled(false);
						else
							menuItems[i].disabled(true);
					}
				}
			}

		}
		
	}
	
	function toggleStyleAttr() {

		if ( this == p.alignleft || this == p.aligncenter || this == p.alignright || this == p.alignjustify ) {
			if ( this != p.alignleft )
				p.alignleft.active(false);
			if ( this != p.aligncenter )
				p.aligncenter.active(false);
			if ( this != p.alignright )
				p.alignright.active(false);
			if ( this != p.alignjustify )
				p.alignjustify.active(false);
		}
		
		if ( (this == p.indent || this == p.outdent) && $(ed.selection.getStart()).closest('li').length != 0 ) {
			if ( this == p.indent )
				ed.execCommand("Indent");
			else if ( this == p.outdent )
				ed.execCommand("Outdent");
		} else {
			this.active( true );
			ed.settings.utils.focus();
			applyParagraphStyle();
		}


	}
	
	// TOGGLE CONTROL VALUES: Set values based on current style context
	function toggleControlValues() {
		
		var t = ed.settings.paragraphstyle;

		if ( !t.plugin )
			return;
		
		var targetNode = getParagraphNode(ed.selection.getStart());

		if ( !targetNode )
			return;

		if (ed.settings.supports_custom_paragraph) {
			for (var i = 1; i <= 10; i++) {
				var spacing = i.toString()
				if (t.line_spacings.indexOf(spacing) == -1) {
					t.line_spacings.push(spacing);
				}
			}

			t.line_spacings.sort(function (a, b) {
				return parseFloat(a) - parseFloat(b);
			});
		}

		// MATCH STYLE
		var targetParagraphStyle = $(targetNode).attr(t.paragraph_style_attr) || null;
		var styleBase = null;

		if ( targetParagraphStyle != null ) {
			targetParagraphStyle = targetParagraphStyle.replace(t.class_prefix,'');
			if ( targetParagraphStyle.indexOf(t.indicator.primary_joiner) !== -1 ) {
				styleBase = targetParagraphStyle.split(t.indicator.primary_joiner)[0];
			} else {
				styleBase = targetParagraphStyle;
			}
		}

		// SET SELECTED STYLE
		t.applied_style = targetParagraphStyle;
		p.paragraphstyle.value(styleBase);

		var customParagraphProperties = ['text_align', 'spacing', 'tab_incr', 'line_spacing', 'indent_type', 'special_indent'];
		var hasCustomAttributes = customParagraphProperties.some(function(property) {
			return targetNode.getAttribute(property);
		});

		var customText = client_messages.text.custom;

		if (styleBase && hasCustomAttributes) {
			customText += " - " + styleBase;
			p.paragraphstyle.text(customText);
		} else {
			p.paragraphstyle.text(p.paragraphstyle.text().replace(customText + " - ", ''));
		}

		toggleControlStates(styleBase);
		
		p.indent.active(false);
		p.outdent.active(false);

		// SET BUTTON VALUES
		setLineSpacing(targetNode, styleBase);
		setAlignment(targetNode);

		setTimeout( function() {
			if (!ed.selection) {
				return;
			}

			cleanListItemNodes(ed);
		}, 0);
	}
	function setLineSpacing(targetNode, styleBase) {
		if (p.linespacing.disabled()) {
			p.linespacing.value(false);
			return;
		}

		// Determine the line spacing value from various sources
		var customLineSpacing = $(targetNode).attr('line_spacing');
		var appliedStyleLineSpacing = getAppliedStyleAttr("linespacing");
		var baseStyleLineSpacing = styleBase &&
			t.data[styleBase] &&
			t.data[styleBase].variation_data &&
			t.data[styleBase].variation_data.line_spacings &&
			t.data[styleBase].variation_data.line_spacings[0];
		var computedLineSpacing = styleBase ? getComputedStyle(targetNode, 'line-height') : false;

		var lineSpacingValue =
			customLineSpacing ||
			appliedStyleLineSpacing ||
			baseStyleLineSpacing ||
			computedLineSpacing ||
			false;

		var parsedValue = parseFloat(lineSpacingValue);

		if (!isNaN(parsedValue)) {
			// Round to an integer if it's a whole number
			parsedValue = (Math.round(parsedValue * 100) / 100).toString();
		} else {
			parsedValue = false;
		}

		p.linespacing.value(parsedValue);
	}

	function setAlignment(targetNode) {
		if ( !p.alignleft.disabled() ) {
			var alignmentMap = { "left": "L", "center": "C", "right": "R", "justify": "J" };
			var appliedAlignment = alignmentMap[$(targetNode).attr('text_align')] ||
				getAppliedStyleAttr("align") ||
				alignmentMap[getComputedStyle(targetNode, 'text-align')] ||
				false;

			p.alignleft.active(appliedAlignment === "L");
			p.aligncenter.active(appliedAlignment === "C");
			p.alignright.active(appliedAlignment === "R");
			p.alignjustify.active(appliedAlignment === "J");
		}
	}

	// TOGGLE STYLE CONTROLS: Enable/disable controls
	function toggleControlStates(style) {

		var variationData = style != null && t.data[style] != undefined ? t.data[style].variation_data : null;

		if ( (style == null || variationData == null)) {

			p.alignleft.disabled( true );
			p.alignright.disabled( true );
			p.aligncenter.disabled( true );
			p.alignjustify.disabled( true );
			p.indent.disabled( true );
			p.outdent.disabled( true );
			p.linespacing.disabled( true );

		} else if ( variationData != null ) {

			if ( variationData.toggle_alignment ) {
				p.alignleft.disabled( false );
				p.alignright.disabled( false );
				p.aligncenter.disabled( false );
				p.alignjustify.disabled( false );
			} else {
				p.alignleft.disabled( true );
				p.alignright.disabled( true );
				p.aligncenter.disabled( true );
				p.alignjustify.disabled( true );
			}
			
			if ( variationData.toggle_left_margin ) {
				p.indent.disabled( false );
				p.outdent.disabled( false );
			} else {
				p.indent.disabled( true );
				p.outdent.disabled( true );
			}

			if ( variationData.toggle_line_spacing ) {
				p.linespacing.disabled(false);
				// DEFAULT LINE SPACING
				if ((variationData.line_spacings && variationData.line_spacings.indexOf( p.linespacing.value() ) == -1) ||
					!p.linespacing.value() )
					p.linespacing.value(variationData.line_spacings[0]);
			} else {
				p.linespacing.disabled(true);
			}

		}

		if (ed.settings.supports_custom_paragraph) {
			p.alignleft.disabled( false );
			p.alignright.disabled( false );
			p.aligncenter.disabled( false );
			p.alignjustify.disabled( false );
			p.indent.disabled( false );
			p.outdent.disabled( false );
			p.linespacing.disabled( false );
		}
		
		if ( $(ed.selection.getStart()).closest('li').length != 0 ) {
			p.indent.disabled( false );
			p.outdent.disabled( false );

			p.alignleft.disabled( true );
			p.alignright.disabled( true );
			p.aligncenter.disabled( true );
			p.alignjustify.disabled( true );

			if (isSefasOrMPComposer() || isExstreamDXF()) {
				p.alignleft.disabled( false );
				p.alignjustify.disabled( false );
			}
		}
	}
	
	function toggleLineSpacingMenuItems() {
		
		var t = ed.settings.paragraphstyle;

		var currentStyle = p.paragraphstyle.value();
		if ( !p || !p.paragraphstyle )
			return;

		var variationData = currentStyle != null && t.data[currentStyle] != undefined ? t.data[currentStyle].variation_data : null;
		
		var menuEle = p.linespacing.menu.$el;

		var lineSpacings = variationData && variationData.line_spacings ? $.extend(true, [], variationData.line_spacings) : [];

		if (ed.settings.supports_custom_paragraph) {
			for (var i = 1; i <= 10; i++) {
				var spacing = i.toString()
				if (lineSpacings.indexOf(spacing) == -1) {
					lineSpacings.push(spacing);
				}
			}

			lineSpacings.sort(function (a, b) {
				return parseFloat(a) > parseFloat(b);
			});
		}

		$(menuEle).find('.mce-menu-item .mce-text').each( function() {
			if ( lineSpacings.indexOf( $.trim($(this).text()) ) == -1 )
				$(this).closest('.mce-menu-item').hide();
			else
				$(this).closest('.mce-menu-item').show();
		});

		if (getAppliedStyleAttr("linespacing") != null && lineSpacings.indexOf(getAppliedStyleAttr("linespacing")) == -1) {
			p.linespacing.value(getAppliedStyleAttr("linespacing"))
		} else if (variationData && variationData.line_spacings) {
			p.linespacing.value(variationData.line_spacings[0]);
		} else {
			p.linespacing.value(false);
		}

	}

	function isSefasOrMPComposer() {
		var settings = window.parent.tinymce.activeEditor.settings;
		return settings.mp_fn.qualify_channel_connectors({'channels': [],'connectors': [18,19] });
	}
	function isExstreamDXF() {
		var settings = window.parent.tinymce.activeEditor.settings;
		return settings.is_exstream_dxf || settings.is_exstream_runtime_dxf;
	}
	
	p = ed.prinovaPlugins;
	if ( !p ) p = {};
	
	// IF APPLY PARAGRAPH STYLES
	if (ed.settings.paragraph_style_css !== false && ed.settings.paragraph_style_css !== null && ed.settings.paragraph_style_css !== undefined && 
			ed.settings.paragraph_style_data != null &&	ed.settings.paragraph_style_data.length > 0) {

		// INIT DATA - START  
		ed.settings.paragraphstyle = {
			applied_style 			: null,
			apply_variations 		: false,
			line_spacings 			: [],
			indicator 				: {
				primary_joiner 		: "--",
				sub_joiner 			: "-"
			},
			dspScale				: (ed.settings.mp_fn.qualify_channel_connectors({'channels': [3,4],'connectors': [] })) ? 1.000 : 1.042,
			css_class_prefix		: '.tinymce_paragraphstyle_',
			class_prefix			: 'tinymce_paragraphstyle_',
			paragraph_style_attr	: 'paragraphclass',
			cssPath					: ed.settings.paragraph_style_css,
			paragraph_style_data	: ed.settings.paragraph_style_data,
			paragraph_style_is_bullet_enabled_array : new Array(),
			styles_post_loaded		: []
		};
		var t =  ed.settings.paragraphstyle;

		ed.settings.paragraph_class_prefix = t.class_prefix;
		
		// Bulleted lists: Create map of styles enable/disable status for bulleted lists
		for ( var i=0; i < t.paragraph_style_data.length; i++ ) {
			let crtParagraphStyleData = t.paragraph_style_data[i];
			if (!crtParagraphStyleData) {
				continue;
			}

			var currentStyle = mpParseJSON( crtParagraphStyleData );
			if (currentStyle && currentStyle.name) {
				t.paragraph_style_is_bullet_enabled_array[currentStyle.name] = currentStyle.bulleted_list_enabled;
			}
		}
		t.paragraph_style_is_bullet_enabled_array["default"] = true;

		// BUILD STYLE LIST
		var styles = [{
				"text" : client_messages.text.none,
				"value" : -1,
				textStyle : function() {
					return "font-size: 14px; color: #000; font-style: italic;";
				}
			}];

		for ( var i=0; i < t.paragraph_style_data.length; i++ ) {
			let crtParagraphStyleData = t.paragraph_style_data[i];
			if ( crtParagraphStyleData && crtParagraphStyleData.has_variations ) {
				
				if ( crtParagraphStyleData.variation_data && crtParagraphStyleData.variation_data.toggle_line_spacing ) {
					crtParagraphStyleData.variation_data.line_spacings = mpParseJSON(crtParagraphStyleData.variation_data.line_spacings);
					for ( var j=0; j < crtParagraphStyleData.variation_data.line_spacings.length; j++ )
						if ( t.line_spacings.indexOf(crtParagraphStyleData.variation_data.line_spacings[j]) == -1 )
							t.line_spacings.push(crtParagraphStyleData.variation_data.line_spacings[j]);
				}

			} else {
				if ( crtParagraphStyleData && crtParagraphStyleData.line_spacing ) {
					if ( t.line_spacings.indexOf(crtParagraphStyleData.line_spacing) == -1 )
						t.line_spacings.push(crtParagraphStyleData.line_spacing);
				}
			}
			t.line_spacings.sort( function(a,b) {
				return parseFloat(a) - parseFloat(b);
			});

			if (crtParagraphStyleData) {
				styles[styles.length] = {
					"text": crtParagraphStyleData.name,
					"value": crtParagraphStyleData.name
				};
			}
		}

		if (ed.settings.supports_custom_paragraph) {
			for (var i = 1; i <= 10; i++) {
				var spacing = i.toString()
				if (t.line_spacings.indexOf(spacing) == -1) {
					t.line_spacings.push(spacing);
				}
			}

			t.line_spacings.sort(function (a, b) {
				return parseFloat(a) - parseFloat(b);
			});
		}


		// DATA: Init style css data
		var styleClasses = new Object();
		for (var i=0; i < t.paragraph_style_data.length; i++) {
			let crtParagraphStyleData = t.paragraph_style_data[i];
			if ( crtParagraphStyleData && crtParagraphStyleData.name) {
				styleClasses[crtParagraphStyleData.name] = {
					css_attributes: crtParagraphStyleData.css_attributes,
					variation_data: crtParagraphStyleData.has_variations ?
						crtParagraphStyleData.variation_data :
						null,
					is_fixed_spacing: crtParagraphStyleData.is_fixed_spacing
				};
			}
		}
		t.data = styleClasses;
		
		// INIT DATA - END
		
		ed.on('init', function(e) {

			var t = ed.settings.paragraphstyle;
			
			if ( ed.settings.styles == undefined ) {
				ed.settings.styles 	= {
					fn	: {}
				};
			}
			
			ed.settings.styles.fn.refresh_applied_paragraph_style_classes = function() { refreshAppliedStyleClasses(); };

			refreshAppliedStyleClasses();

		});	
		
		ed.on('nodeChange', function(e) {
			// Buffer to allow time for click events to complete
			setTimeout( function() {
				if (!ed.selection) {
					return;
				}

				if (p.paragraphstyle)
					toggleControlValues();
			}, 0);

		});
	
		ed.on('ExecCommand', function(e){
			if ( e.command == "InsertUnorderedList" || e.command == "InsertUnorderedList" ) {
				cleanListItemNodes(ed);
			}
		});
	
		// STYLE SELECT
		ed.addButton('paragraphstyle', {
			type		: 'listbox',
			text		: client_messages.content_editor.paragraph,
			onselect	: function(e) {
				// RESET CONTROLS
				p = ed.prinovaPlugins;
				p.alignleft.active( false );
				p.alignright.active( false );
				p.aligncenter.active( false );
				p.alignjustify.active( false );
				p.indent.active( false );
				p.outdent.active( false );
				p.linespacing.value(false);

				if ( this.value() == -1 ) {
					this.value(null);
				} else {
					var variationData = t.data[this.value()] != undefined ? t.data[this.value()].variation_data : null;

					if ( variationData ) {
						var lineSpacings = variationData.line_spacings;
						p.linespacing.value(lineSpacings ? lineSpacings[0] : false);
					} else {
						p.linespacing.value(false);
					}
					
					toggleControlStates(this.value());
				}
	
				ed.settings.utils.focus(); // Focus before accessing editor.selection
				applyParagraphStyle();
	
			},
			values		: styles,
			onPostRender: function(e) {
				t.plugin = this;
				if ( ed.prinovaPlugins == undefined )
					ed.prinovaPlugins = {};
				ed.prinovaPlugins.paragraphstyle = this;
				
				var showMenuFunc = this.showMenu;
				this.showMenu = function() {
					showMenuFunc.apply(this,arguments);
					toggleMenuItems(ed);
				};
			},
			onShow: function(o) {
				ed.execCommand("mceEnhanceMenu", false, o);
			}
		});
		
		// MENU: LINE SPACING
		var lineSpacingItems = new Array();
		for ( var i = 0; i < t.line_spacings.length; i++ ) {
			lineSpacingItems.push({
				text	: t.line_spacings[i],
				value	: t.line_spacings[i]
			});
		}
		ed.addButton('mp_linespacing', {
			type		: 'listbox',
			text		: client_messages.content_editor.line_spacing_abv,
			disabled	: true,
			onselect	: function(e) {
				ed.settings.utils.focus();
				applyParagraphStyle();
			},
			values		: lineSpacingItems,
			onClick		: function(e) {
				toggleLineSpacingMenuItems();
			},
			onPostRender: function(e) {
				p = ed.prinovaPlugins;
				if ( !p ) p = {};
				
				p.linespacing = this;
			}
		});
		
		// BUTTONS: ALIGNMENT
		ed.addButton('mp_alignleft', {
			icon		: 'alignleft',
			disabled	: true,
			tooltip		: client_messages.content_editor.alignment_left,
			onclick		: toggleStyleAttr,
			onPostRender: function(e) {
				p = ed.prinovaPlugins;
				if ( !p ) p = {};
				
				p.alignleft = this;
			},
			selector	: 'figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li'
		});
		ed.addButton('mp_aligncenter', {
			icon		: 'aligncenter',
			disabled	: true,
			tooltip		: client_messages.content_editor.alignment_center,
			onclick		: toggleStyleAttr,
			onPostRender: function(e) {
				p = ed.prinovaPlugins;
				if ( !p ) p = {};
				
				p.aligncenter = this;
			},
			selector	: 'figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div'
		});
		ed.addButton('mp_alignright', {
			icon		: 'alignright',
			disabled	: true,
			tooltip		: client_messages.content_editor.alignment_right,
			onclick		: toggleStyleAttr,
			onPostRender: function(e) {
				p = ed.prinovaPlugins;
				if ( !p ) p = {};
				
				p.alignright = this;
			},
			selector	: 'figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div'
		});
		ed.addButton('mp_alignjustify', {
			icon		: 'alignjustify',
			disabled	: true,
			tooltip		: client_messages.content_editor.alignment_full,
			onclick		: toggleStyleAttr,
			onPostRender: function(e) {
				p = ed.prinovaPlugins;
				if ( !p ) p = {};
				
				p.alignjustify = this;
			},
			selector	: 'figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li'
		});
		
	} // END IF APPLY PARAGRAPH STYLES
	else
	{
		// OTHERWISE CONTROL LIST INDENT
		ed.on('nodeChange', function(e) {
			if (!p.paragraphstyle && p.indent && p.outdent) {
				p.indent.active(false);
				p.outdent.active(false);
				if ( $(ed.selection.getStart()).closest('li').length != 0 ) {
					p.indent.disabled(false);
					p.outdent.disabled(false);
				} else {
					p.indent.disabled(true);
					p.outdent.disabled(true);
				}
			}
		});
	}
	
	// BUTTONS: OUTDENT/INDENT
	ed.addButton('mp_outdent', {
		icon		: 'outdent',
		disabled	: true,
		tooltip		: client_messages.content_editor.outdent,
		onclick		: toggleStyleAttr,
		onPostRender: function(e) {
			p = ed.prinovaPlugins;
			if ( !p ) p = {};

			p.outdent = this;
		}
	});
	ed.addButton('mp_indent', {
		icon		: 'indent',
		disabled	: true,
		tooltip		: client_messages.content_editor.indent,
		onclick		: toggleStyleAttr,
		onPostRender: function(e) {
			p = ed.prinovaPlugins;
			if ( !p ) p = {};

			p.indent = this;
		}
	});
	
	ed.addCommand('mceApplyParagraphStyle', function(ui, v) {
		if ( v != undefined ) {
			var item = {};
			item.value = v.style;
			applyParagraphStyle(v.target, item);
		}
	});
		
});