tinymce.PluginManager.add('mp_contentintel', function(ed, url) {

    function initInterface() {

        function generateButtonBar() {

            var html =
                "<div class=\"mce-contentintel-btn-container mce-container-body  mce-flow-layout\" style=\"display: inline-block;\">" +
                "	<div id=\"mce_summarize_btn\" data-html=\"true\" data-toggle=\"tooltip\" title=\"" + client_messages.content_editor.summarize + "\" class=\"mce-container-body\" style=\"border-right: 1px solid #c5c5c5; padding: 8px 16px; display: none; cursor: pointer;\">" +
                "		<div class=\"mce-btn-label\" style=\"display: inline-block; vertical-align: middle; font-size: 11.5px;\">" +
                "			<i id = \"summarize_btn_icon\" style=\"font-size: 14px;\" class=\"far fa-info-circle fa-mp-button\"></i>" +
                "		</div>" +
                "	</div>" +
                "	<div id=\"mce_readability_btn\" class=\"mce-container-body\" style=\"border-right: 1px solid #c5c5c5; padding: 8px 16px; display: none; cursor: pointer;\">" +
                "		<div class=\"mce-btn-label\" style=\"display: inline-block; vertical-align: middle; padding-right: 8px; font-size: 11.5px;\">" +
                client_messages.content_editor.readability +
                "		</div>" +
                "		<div class=\"mce-status-ind\" style=\"display: inline-block; vertical-align: middle; border: 2px solid #e1e1e1; box-sizing: border-box; border-radius: 6px; width: 12px; height: 12px; background-color: white;\"></div>" +
                "	</div>" +
                "	<div id=\"mce_sentiment_btn\" class=\"mce-container-body\" style=\"border-right: 1px solid #c5c5c5; padding: 8px 16px; display: none; cursor: pointer;\">" +
                "		<div class=\"mce-btn-label\" style=\"display: inline-block; vertical-align: middle; padding-right: 8px; font-size: 11.5px;\">" +
                client_messages.content_editor.sentiment +
                "		</div>" +
                "		<div class=\"mce-status-ind\" style=\"display: inline-block; vertical-align: middle; border: 2px solid #e1e1e1; box-sizing: border-box; border-radius: 6px; width: 12px; height: 12px; background-color: white;\"></div>" +
                "	</div>" +
                "	<div id=\"mce_brandcheck_btn\" data-toggle=\"tooltip\" title=\"" + client_messages.content_editor.brand + "\" class=\"mce-container-body\" style=\"border-right: 1px solid #c5c5c5; padding: 8px 16px; display: none; cursor: pointer;\">" +
                "		<div class=\"mce-btn-label\" style=\"display: inline-block; vertical-align: middle; padding-right: 8px; font-size: 11.5px;\">" +
                client_messages.content_editor.brand +
                "		</div>" +
                "		<div class=\"mce-status-ind\" style=\"display: inline-block; vertical-align: middle; border: 2px solid #e1e1e1; box-sizing: border-box; border-radius: 6px; width: 12px; height: 12px; background-color: white;\"></div>" +
                "	</div>" +
                "	<div id=\"mce_plain_btn\" class=\"mce-container-body\" title=\"" + client_messages.content_editor.plain_tootip  + "\" style=\"border-right: 1px solid #c5c5c5; padding: 8px 16px; display: none; cursor: pointer;\">" +
                "		<div class=\"mce-btn-label\" style=\"display: inline-block; vertical-align: middle; padding-right: 8px; font-size: 11.5px;\">" +
                client_messages.content_editor.plain +
                "		</div>" +
                "	</div>" +
                "	<div id=\"mce_translate_btn\" class=\"mce-container-body\" title=\"" + client_messages.content_editor.translate_tootip  + "\" style=\"border-right: 1px solid #c5c5c5; padding: 8px 16px; display: none; cursor: pointer;\">" +
                "		<div class=\"mce-btn-label\" style=\"display: inline-block; vertical-align: middle; padding-right: 8px; font-size: 11.5px;\">" +
                client_messages.content_editor.translate +
                "		</div>" +
                "		<div class=\"mce-status-ind\" style=\"display: inline-block; vertical-align: middle; border: 2px solid #e1e1e1; box-sizing: border-box; border-radius: 6px; width: 12px; height: 12px; background-color: white;\"></div>" +
                "	</div>" +
                "	<div id=\"mce_contentcompare_btn\" data-toggle=\"tooltip\" title=\"" + client_messages.content_editor.content_duplicates_and_similarities + "\" class=\"mce-container-body\" style=\"border-right: 1px solid #c5c5c5; padding: 8px 16px; display: none; cursor: pointer;\">" +
                "		<div class=\"mce-btn-label\" style=\"display: inline-block; vertical-align: middle; font-size: 11.5px;\">" +
                "			<i style=\"font-size: 14px;\" class=\"far fa-clipboard-list fa-mp-button\"></i>" +
                "		</div>" +
                "	</div>" +
                // "	<div id=\"mce_model_menu\" data-toggle=\"tooltip\" title=\"Select GPT model\" class=\"mce-container-body\" style=\"padding: 0px 16px; display: inline-block;\">" +
                // "		 <select id=\"mce_gpt_model_select\" style=\"display: none; height: 30px;\">" +
                // "           <option value=\"inst\" class=\"optionInst\">Inst</option>" +
                // "           <option value=\"gpt3\">GPT3</option>" +
                // "           <option value=\"chatgpt\">ChatGPT</option>" +
                // "           <option value=\"gpt4\">GPT4</option>" +
                // "        </select>   " +
                // "	</div>" +
                "</div>";

            let statusPanel = $(ed.editorContainer);
            if(ed.settings.connectedContentIntel){
                statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
                statusPanel.append(html);

                let tmpCallback = ed.settings.contentintel.callbackAfterDisplayingContentIntel;
                if (tmpCallback && tmpCallback instanceof Function) {
                    tmpCallback();
                }
            } else {
                statusPanel.find('.mce-statusbar').prepend( html );
            }

            // $(statusPanel).find('.mce-statusbar').dblclick(function() {
            //     $(this).find('#mce_gpt_model_select').toggle();
            // });
            // var model = localStorage.getItem("msgpt_editor_gpt_model") != null ? localStorage.getItem("msgpt_editor_gpt_model") : "gpt4";
            // $(statusPanel).find('#mce_gpt_model_select').val(model);
            // $(statusPanel).find('#mce_gpt_model_select').change(function() {
            //      localStorage.setItem("msgpt_editor_gpt_model", $(this).val());
            // });

            statusPanel.find('#mce_sentiment_btn').click( function() {
                // $('#mce_gpt_model_select').show();
                localStorage.removeItem("contentHash");
                if ( ed.settings.contentintel.readability.is_panel_active )
                    toggleReadabilityPanel();
                if ( ed.settings.contentintel.contentcompare.is_panel_active )
                    toggleContentComparePanel();
                if ( ed.settings.contentintel.brandcheck.is_panel_active )
                    toggleBrandCheckPanel();
                if ( ed.settings.contentintel.summarize.is_panel_active )
                    toggleSummarizePanel();
                if ( ed.settings.contentintel.plain.is_panel_active )
                    togglePlainLanguagePanel();
                if ( ed.settings.contentintel.translate.is_panel_active )
                    toggleTranslatePanel();
                toggleSentimentPanel();
            });
            statusPanel.find('#mce_readability_btn').click( function() {
                // $('#mce_gpt_model_select').show();
                localStorage.removeItem("contentHash");
                if ( ed.settings.contentintel.sentiment.is_panel_active )
                    toggleSentimentPanel();
                if ( ed.settings.contentintel.contentcompare.is_panel_active )
                    toggleContentComparePanel();
                if ( ed.settings.contentintel.brandcheck.is_panel_active )
                    toggleBrandCheckPanel();
                if ( ed.settings.contentintel.summarize.is_panel_active )
                    toggleSummarizePanel();
                if ( ed.settings.contentintel.plain.is_panel_active )
                    togglePlainLanguagePanel();
                if ( ed.settings.contentintel.translate.is_panel_active )
                    toggleTranslatePanel();
                toggleReadabilityPanel();
            });

            statusPanel.find('#mce_contentcompare_btn').click( function() {
                // $('#mce_gpt_model_select').show();
                if ( ed.settings.contentintel.sentiment.is_panel_active )
                    toggleSentimentPanel();
                if ( ed.settings.contentintel.readability.is_panel_active )
                    toggleReadabilityPanel();
                if ( ed.settings.contentintel.brandcheck.is_panel_active )
                    toggleBrandCheckPanel();
                if ( ed.settings.contentintel.summarize.is_panel_active )
                    toggleSummarizePanel();
                if ( ed.settings.contentintel.plain.is_panel_active )
                    togglePlainLanguagePanel();
                if ( ed.settings.contentintel.translate.is_panel_active )
                    toggleTranslatePanel();
                toggleContentComparePanel();
            }).tooltip();

            statusPanel.find('#mce_brandcheck_btn').click( function() {
                // $('#mce_gpt_model_select').show();
                if ( ed.settings.contentintel.sentiment.is_panel_active )
                    toggleSentimentPanel();
                if ( ed.settings.contentintel.readability.is_panel_active )
                    toggleReadabilityPanel();
                if ( ed.settings.contentintel.contentcompare.is_panel_active )
                    toggleContentComparePanel();
                if ( ed.settings.contentintel.summarize.is_panel_active )
                    toggleSummarizePanel();
                if ( ed.settings.contentintel.plain.is_panel_active )
                    togglePlainLanguagePanel();
                if ( ed.settings.contentintel.translate.is_panel_active )
                    toggleTranslatePanel();
                toggleBrandCheckPanel();
            }).tooltip();

            statusPanel.find('#mce_summarize_btn').click( function() {
                // $('#mce_gpt_model_select').show();
                localStorage.removeItem("contentHash");
                if ( ed.settings.contentintel.sentiment.is_panel_active )
                    toggleSentimentPanel();
                if ( ed.settings.contentintel.readability.is_panel_active )
                    toggleReadabilityPanel();
                if ( ed.settings.contentintel.contentcompare.is_panel_active )
                    toggleContentComparePanel();
                if ( ed.settings.contentintel.brandcheck.is_panel_active )
                    toggleBrandCheckPanel();
                if ( ed.settings.contentintel.plain.is_panel_active )
                    togglePlainLanguagePanel();
                if ( ed.settings.contentintel.translate.is_panel_active )
                    toggleTranslatePanel();
                toggleSummarizePanel();
            }).tooltip();

            statusPanel.find('#mce_plain_btn').click( function() {
                // $('#mce_gpt_model_select').hide();
                localStorage.removeItem("contentHash");
                if ( ed.settings.contentintel.sentiment.is_panel_active )
                    toggleSentimentPanel();
                if ( ed.settings.contentintel.readability.is_panel_active )
                    toggleReadabilityPanel();
                if ( ed.settings.contentintel.contentcompare.is_panel_active )
                    toggleContentComparePanel();
                if ( ed.settings.contentintel.brandcheck.is_panel_active )
                    toggleBrandCheckPanel();
                if ( ed.settings.contentintel.summarize.is_panel_active )
                    toggleSummarizePanel();
                if ( ed.settings.contentintel.translate.is_panel_active )
                    toggleTranslatePanel();
                togglePlainLanguagePanel();
            }).tooltip();

            statusPanel.find('#mce_translate_btn').click( function() {
                localStorage.removeItem("contentHash");
                // $('#mce_gpt_model_select').hide();
                 getTranslate();
                if ( ed.settings.contentintel.sentiment.is_panel_active )
                    toggleSentimentPanel();
                if ( ed.settings.contentintel.readability.is_panel_active )
                    toggleReadabilityPanel();
                if ( ed.settings.contentintel.contentcompare.is_panel_active )
                    toggleContentComparePanel();
                if ( ed.settings.contentintel.brandcheck.is_panel_active )
                    toggleBrandCheckPanel();
                if ( ed.settings.contentintel.summarize.is_panel_active )
                    toggleSummarizePanel();
                if ( ed.settings.contentintel.plain.is_panel_active )
                    togglePlainLanguagePanel()
                toggleTranslatePanel();
                // getTranslationAccuracy();
            }).tooltip();
            toggleGutterDisplay();
        }

        const connectedContentIntel = ed.settings.connectedContentIntel;
        if(connectedContentIntel){
            marciePanel = $("#"+ connectedContentIntel.marciePanelId);
            if ( marciePanel.length == 0){
                setTimeout( function() {
                        let actionName = 'init';
                        if (ed.settings.connectedutils
                            && ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_contentintel', actionName)
                        ) {
                            return;
                        }
                        initInterface();
                    }, 1000);
            }
            else {
                generateButtonBar();
            }
        } else {
            if ( $(ed.editorContainer).find('.mce-statusbar:visible').length == 0)
                setTimeout( function() {
                        let actionName = 'init';
                        if (ed.settings.connectedutils
                            && ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_contentintel', actionName)
                        ) {
                            return;
                        }
                        initInterface();
                    }, 1000);
            else if ( $(ed.containter).find('.mce-contentintel-btn-container').length == 0 )
                generateButtonBar();
        }
    }

    function getTranslate() {
        var stampDate = new Date();
        var doc = $(getTopFrame().document);
        var markupContent =$(ed.getBody()).html();
        if (markupContent.indexOf('</p>') == -1)
            markupContent = "<p>" + markupContent + "</p>";
        markupContent = "<div>" + markupContent + "</div>";
        $(markupContent).find('p').each(function() {
            if ( $.trim( $(this).text() ).length == 0 )
                $(this).remove();
        });

        var markupContentClone = $(markupContent).clone();

         var content = $(markupContentClone).html();

            var localeId = "";
            if (ed.settings.connectedContentIntel) {
                localeId = $(doc).find('#targetLocaleSelect').length != 0 ? $(doc).find('#targetLocaleSelect').val() : "18";
            } else if ($(doc).find('#contentLanguageSelect').length) {
                localeId = $(doc).find('#contentLanguageSelect').val();
            }

            var contextParams = "";
            if (ed.settings.content_object_id != undefined)
                contextParams += "&contentObjectId=" + ed.settings.content_object_id;
            if (ed.settings.variant_id != undefined)
                contextParams += "&variantId=" + ed.settings.variant_id;
            if (ed.settings.rationalizer_application_id != undefined)
                contextParams += "&rationalizerApplicationId=" + ed.settings.rationalizer_application_id;

            $.ajax({
                type: "POST",
                url: context + "/asyncContentIntelligence.form" +
                    "?action=translate" +
                    contextParams +
                    "&tk=" + getParam('tk') +
                    "&cacheStamp=" + (stampDate.getTime()),
                data: {content: JSON.stringify({"text": content, "translateLocaleId": localeId})},
                dataType: "json",
                error: function (xhr, status, error) {
                    console.log("AJAX translate info request failed " + error);
                },
                success: function (data) {
                    if (data.error == true) {
                        console.log(data.message);
                        return;
                    } else {
                        updateTranslateDisplay(data);
                    }
                }
            });

    }

    function getTranslationAccuracy(isForTranslatedContent) {

        if ( !ed.settings.contentintel.translate.is_panel_active ) {
            $(ed.editorContainer).find('#mce_translate_btn .mce-status-ind').css({'background-color':'white'});
            $(doc).find('#mce_translate_panel .mce-status-ind').css({'background-color': 'white'});
            return;
        }

        var defaultLocaleBinding = $('div[groupId='+ed.settings.currentContentGroupId+'] .defaultLocaleContent');
        if ( $(defaultLocaleBinding).length == 0 )
            return;

        var doc = $(getTopFrame().document);
        if(!isForTranslatedContent) {
            $('.mcePanelAccuracy').css('opacity', 0.5);
        } else {
            $('.mceSuggestedPanelAccuracy').css('opacity', 0.5);
        }

        var localeId = "";
        if ($(doc).find('#contentLanguageSelect').length) {
            localeId = $(doc).find('#contentLanguageSelect').val();
        }

        // Is default content or no content: No accuracy check needed
        if ( localeId == $(defaultLocaleBinding).closest("[localeId]").attr("localeId") ||
             $.trim( $(ed.getBody()).text()).length == 0 ) {
            $(doc).find('.crtAccuracy').hide();
            $(ed.editorContainer).find('#mce_translate_btn .mce-status-ind').css({'background-color':'white'});
            $(doc).find('#mce_translate_panel .mce-status-ind').css({'background-color': 'white'});
            let statusPanel = $(ed.editorContainer);
            if ( localeId == $(defaultLocaleBinding).closest("[localeId]").attr("localeId")){

                var tooltipTxt = client_messages.content_editor.translate_same_language_tooltip;
                statusPanel.find('#mce_translate_btn')
                    .attr('data-original-title', tooltipTxt)
                    .attr('data-toggle', 'tooltip').tooltip();
                $(doc).find('.mcePanelAccuracy .mce-status-ind')
                    .attr('data-original-title', tooltipTxt)
                    .attr('data-toggle', 'tooltip').tooltip();
            }
            if($.trim( $(ed.getBody()).text()).length == 0){
                var tooltipTxt = client_messages.content_editor.translate_no_content_tooltip;
                statusPanel.find('#mce_translate_btn')
                    .attr('data-original-title', tooltipTxt)
                    .attr('data-toggle', 'tooltip').tooltip();
                $(doc).find('.mcePanelAccuracy .mce-status-ind')
                    .attr('data-original-title', tooltipTxt)
                    .attr('data-toggle', 'tooltip').tooltip();
            }

                return;
        } else {
            $(doc).find('.crtAccuracy').show();
        }

        // Locale content
        var localeHtmlContent;
        if(!isForTranslatedContent) {
            var targetLocaleMarkup = $(ed.getBody()).html();
            targetLocaleMarkup = "<div>" + targetLocaleMarkup + "</div>";

            localeHtmlContent = util_minimizeStaticElements($(targetLocaleMarkup).html());
            var minimizedContent = util_minimizeHTMLattributes(localeHtmlContent);
            localeHtmlContent = minimizedContent.html;
            var minimizedContentSelectionAttr = minimizedContent.attr;
        } else {
            var targetLocaleMarkup = $($(doc).find('.rewriteResultMarkup')).html();
            targetLocaleMarkup = "<div>" + targetLocaleMarkup + "</div>";

            localeHtmlContent = util_minimizeStaticElements($(targetLocaleMarkup).html());
            var minimizedContent = util_minimizeHTMLattributes(localeHtmlContent);
            localeHtmlContent = minimizedContent.html;
            var minimizedContentSelectionAttr = minimizedContent.attr;
        }

        // Default content
        var defaultLocaleMarkup = $(defaultLocaleBinding).val();
        defaultLocaleMarkup = "<div>" + defaultLocaleMarkup + "</div>";

        var defaultHtmlContent = util_minimizeStaticElements( $(defaultLocaleMarkup).html() );
        var minimizedDefaultContent = util_minimizeHTMLattributes(defaultHtmlContent);
        defaultHtmlContent = minimizedDefaultContent.html
        var minimizedDefaultContentSelectionAttr = minimizedDefaultContent.attr;

        var isRationalizerModel = doc[0].title.includes("Rationalizer");

        var localeId ="";
        if ( ed.settings.connectedContentIntel ) {
            localeId = $(doc).find('#targetLocaleSelect').length != 0 ? $(doc).find('#targetLocaleSelect').val() : "18";
        } else if($(doc).find('#contentLanguageSelect').length) {
            localeId = $(doc).find('#contentLanguageSelect').val();
        }

        var model = $(ed.editorContainer).find('#mce_gpt_model_select').length != 0 ? $(ed.editorContainer).find('#mce_gpt_model_select').val() : "gpt4-o";
        if(ed.settings.contentintel.translate.is_panel_active) {
            model = $(ed.editorContainer).find('#mce_gpt_model_select').length != 0 ? $(ed.editorContainer).find('#mce_gpt_model_select').val() : "gpt4-o";
        }

        var defaultLocaleBinding = $('div[groupId='+ed.settings.currentContentGroupId+'] .defaultLocaleContent');
        var sourceLocaleId = $(defaultLocaleBinding).closest("[localeId]").attr("localeId");
        if ( ed.settings.connectedContentIntel )
            sourceLocaleId = "1";

        var contentParam = JSON.stringify({ locale_html: localeHtmlContent, default_html: defaultHtmlContent, targetLocaleId: localeId , isRationalizerModel: isRationalizerModel, sourceLocaleId: sourceLocaleId});

        var stampDate = new Date();
        var contextParams = "";
        if ( ed.settings.rationalizer_application_id != undefined )
            contextParams += "&rationalizerApplicationId=" + ed.settings.rationalizer_application_id;
        if (ed.settings.contentintel.translated_vars === undefined){
            ed.settings.contentintel.translated_vars = [];
        }

        $.ajax({
            type: "POST",
            url: context + "/asyncContentIntelligence.form" +
                "?action=translation_accuracy" +
                "&verbose=true"  +
                contextParams +
                "&model=" + model +
                "&tk=" + getParam('tk') +
                "&cacheStamp=" + (stampDate.getTime()),
            data: { content: contentParam, variables:JSON.stringify({"variables_array": ed.settings.contentintel.translated_vars})},
            dataType: "json",
            error: function(xhr, status, error) {
                console.log("AJAX translation accuracy info request failed " + error);
            },
            success: function(data) {
                if (data.error == true) {
                    console.log(data.message);
                    return;
                } else {
                    updateTranslationAccuracyDisplay(data, isForTranslatedContent);
                    if(!isForTranslatedContent) {
                        $('.mcePanelAccuracy').css('opacity', 1);
                    } else {
                        $('.mceSuggestedPanelAccuracy').css('opacity', 1);
                    }
                }
            }
        });

    }

    function updateContentInfo() {
        // Not enabled: return
        if (!ed.settings.contentintel.translate_enabled
            && !ed.settings.contentintel.plain_enabled
            && !ed.settings.contentintel.summarize_enabled
            && !ed.settings.contentintel.sentiment_enabled
            && !ed.settings.contentintel.readability_enabled
            && !ed.settings.contentintel.contentcompare_enabled
            && !ed.settings.contentintel.brandcheck_enabled)
            return;

        function getSentiment(content_array, variablesArray) {
            var stampDate = new Date();
            var contextParams = "";
            if (ed.settings.rationalizer_application_id != undefined)
                contextParams += "&rationalizerApplicationId=" + ed.settings.rationalizer_application_id;
            $.ajax({
                type: "POST",
                url: context + "/asyncContentIntelligence.form" +
                    "?action=sentiment" +
                    "&verbose=" + ed.settings.contentintel.sentiment.is_panel_active +
                    contextParams +
                    "&tk=" + getParam('tk') +
                    "&cacheStamp=" + (stampDate.getTime()),
                data: {content: JSON.stringify({"txt_array": content_array}), variables:JSON.stringify({"variables_array": variablesArray})},
                dataType: "json",
                error: function(xhr, status, error) {
                    console.log("AJAX sentiment request failed " + error);
                },
                success: function (data) {
                    if (data.error == true) {
                        console.log("error " + data.message);
                        return;
                    } else {
                        updateSentimentDisplay(data);
                    }
                }
            });
        }

        function getReadability(content_array, variablesArray) {
            var stampDate = new Date();
            var contextParams = "";
            if (ed.settings.rationalizer_application_id != undefined)
                contextParams += "&rationalizerApplicationId=" + ed.settings.rationalizer_application_id;
            $.ajax({
                type: "POST",
                url: context + "/asyncContentIntelligence.form" +
                    "?action=readability" +
                    "&verbose=" + ed.settings.contentintel.readability.is_panel_active +
                    contextParams +
                    "&tk=" + getParam('tk') +
                    "&cacheStamp=" + (stampDate.getTime()),
                data: {content: JSON.stringify({"txt_array": content_array}), variables:JSON.stringify({"variables_array": variablesArray})},
                dataType: "json",
                error: function(xhr, status, error) {
                    console.log("AJAX readability request failed " + error);
                },
                success: function (data) {
                    if (data.error == true) {
                        console.log(data.message);
                        return;
                    } else {
                        updateReadabilityDisplay(data);
                    }
                }
            });
        }

        function getSummarize() {
            var stampDate = new Date();

            var markupContent = ed.selection != null ? ed.selection.getContent() : '';
            if ($.trim(markupContent).length == 0)
                markupContent = $(ed.getBody()).html();

            if (markupContent.indexOf('</p>') == -1)
                markupContent = "<p>" + markupContent + "</p>";
            markupContent = "<div>" + markupContent + "</div>";
            $(markupContent).find('p').each(function() {
                if ( $.trim( $(this).text() ).length == 0 )
                    $(this).remove();
            });

            var markupContentClone = $(markupContent).clone();

            $(markupContentClone).find('p,li').each(function () {
                $(this).find('.CmCaReT,#cursorPos,.renderedLabelContainer').remove();
                $(this).find('[type=1]').each(function () {
                    if ($(this).find('.renderedSampleValueContainer').length > 0) {
                        $(this).html($(this).find('.renderedSampleValueContainer').text());
                    } else {
                        $(this).html($(this).text());
                    }
                });
                $(this).find('[type=15]').each(function () {
                    if ($(this).find('.mceContentMenuValue').length > 0)
                        $(this).html($(this).find('.mceContentMenuValue').text());
                });
                $(this).find('.mceHiddenContent').each(function () {
                    $(this).html('');
                });
            });

            var content = $(markupContentClone).html();

            var contextParams = "";
            if (ed.settings.content_object_id != undefined)
                contextParams += "&contentObjectId=" + ed.settings.content_object_id;
            if (ed.settings.variant_id != undefined)
                contextParams += "&variantId=" + ed.settings.variant_id;
            if (ed.settings.rationalizer_application_id != undefined)
                contextParams += "&rationalizerApplicationId=" + ed.settings.rationalizer_application_id;

            $.ajax({
                type: "POST",
                url: context + "/asyncContentIntelligence.form" +
                    "?action=summarize" +
                    contextParams +
                    "&tk=" + getParam('tk') +
                    "&cacheStamp=" + (stampDate.getTime()),
                data: {content: JSON.stringify({"text": content})},
                dataType: "json",
                error: function(xhr, status, error) {
                    console.log("AJAX summary info request failed " + error);
                },
                success: function (data) {
                    if (data.error == true) {
                        console.log(data.message);
                        return;
                    } else {
                        updateSummarizeDisplay(data);
                    }
                }
            });
        }

        function getPlainLanguage() {
            var stampDate = new Date();

            var markupContent = ed.selection != null ? ed.selection.getContent() : '';
            if ($.trim(markupContent).length == 0)
                markupContent = $(ed.getBody()).html();

            if (markupContent.indexOf('</p>') == -1)
                markupContent = "<p>" + markupContent + "</p>";
            markupContent = "<div>" + markupContent + "</div>";
            $(markupContent).find('p').each(function() {
                if ( $.trim( $(this).text() ).length == 0 )
                    $(this).remove();
            });

            var markupContentClone = $(markupContent).clone();

            $(markupContentClone).find('p,li').each(function () {
                $(this).find('.CmCaReT,#cursorPos,.renderedLabelContainer').remove();
                $(this).find('[type=1]').each(function () {
                    if ($(this).find('.renderedSampleValueContainer').length > 0) {
                        $(this).html($(this).find('.renderedSampleValueContainer').text());
                    } else {
                        $(this).html($(this).text());
                    }
                });
                $(this).find('[type=15]').each(function () {
                    if ($(this).find('.mceContentMenuValue').length > 0)
                        $(this).html($(this).find('.mceContentMenuValue').text());
                });
                $(this).find('.mceHiddenContent').each(function () {
                    $(this).html('');
                });
            });

            var content = $(markupContentClone).html();

            var contextParams = "";
            if (ed.settings.content_object_id != undefined)
                contextParams += "&contentObjectId=" + ed.settings.content_object_id;
            if (ed.settings.variant_id != undefined)
                contextParams += "&variantId=" + ed.settings.variant_id;
            if (ed.settings.rationalizer_application_id != undefined)
                contextParams += "&rationalizerApplicationId=" + ed.settings.rationalizer_application_id;

            $.ajax({
                type: "POST",
                url: context + "/asyncContentIntelligence.form" +
                    "?action=plain" +
                    contextParams +
                    "&tk=" + getParam('tk') +
                    "&cacheStamp=" + (stampDate.getTime()),
                data: {content: JSON.stringify({"text": content})},
                dataType: "json",
                error: function(xhr, status, error) {
                    console.log("AJAX plain language info request failed " + error);
                },
                success: function (data) {
                    if (data.error == true) {
                        console.log(data.message);
                        return;
                    } else {
                        updatePlainLanguageDisplay(data);
                    }
                }
            });
        }


        function getHasSimOrDup() {
            var stampDate = new Date();

            var markupContent = ed.selection != null ? ed.selection.getContent() : '';
            if ($.trim(markupContent).length == 0)
                markupContent = $(ed.getBody()).html();

            if (markupContent.indexOf('</p>') == -1)
                markupContent = "<p>" + markupContent + "</p>";
            markupContent = "<div>" + markupContent + "</div>";

            var comparisionContent = $(markupContent).html();

            var contextParams = "";
            if (ed.settings.content_object_id != undefined)
                contextParams += "&contentObjectId=" + ed.settings.content_object_id;
            if (ed.settings.variant_id != undefined)
                contextParams += "&variantId=" + ed.settings.variant_id;
            if (ed.settings.rationalizer_application_id != undefined)
                contextParams += "&rationalizerApplicationId=" + ed.settings.rationalizer_application_id;

            $.ajax({
                type: "POST",
                url: context + "/asyncContentIntelligence.form" +
                    "?action=sim_dup" +
                    contextParams +
                    "&tk=" + getParam('tk') +
                    "&cacheStamp=" + (stampDate.getTime()),
                data: {content: JSON.stringify({"text": escape(escapeHtmlEntities(jQuery.trim(comparisionContent)))})},
                dataType: "json",
                error: function(xhr, status, error) {
                    console.log("AJAX sim_dup request failed " + error);
                },
                success: function (data) {
                    if (data.error == true) {
                        console.log(data.message);
                        return;
                    } else {
                        updateContentCompareDisplay(data);
                    }
                }
            });
        }

        function getBrandCheck() {

            if (ed.settings.contentintel.brandcheck.is_panel_active) {

                clearTimeout(ed.settings.contentintel.brandcheck.is_panel_update_poller);
                ed.settings.contentintel.brandcheck.is_panel_update_poller = setTimeout(function () {
                    let actionName = 'brandcheck.is_panel_update_poller';
                    if (ed.settings.connectedutils
                        && ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_contentintel', actionName)
                    ) {
                        return;
                    }
                    ed.settings.contentintel.brandcheck.is_panel_update_pending = true;
                }, 1000);

            } else {
                var brandCheckInputContent = $(ed.getBody()).html();
                if (ed.settings.connectedContentIntel) {
                    brandCheckInputContent = ed.settings.connectedContentIntel.contentProvider();
                }
                let parsedContentResults = getBrandCheckContent($("<div>" + brandCheckInputContent + "</div>"));

                var contextParams = "";
                if (ed.settings.content_object_id != undefined)
                    contextParams += "&contentObjectId=" + ed.settings.content_object_id;
                if (ed.settings.variant_id != undefined)
                    contextParams += "&variantId=" + ed.settings.variant_id;
                if (ed.settings.rationalizer_application_id != undefined)
                    contextParams += "&rationalizerApplicationId=" + ed.settings.rationalizer_application_id;
                if (ed.settings.communication_id != undefined)
                    contextParams += "&communicationId=" + ed.settings.communication_id;

                const stampDate = new Date();
                $.ajax({
                    type: "POST",
                    url: context + "/asyncContentIntelligence.form" +
                        "?action=brand_check" +
                        "&verbose=false" +
                        contextParams +
                        "&tk=" + getParam('tk') +
                        "&cacheStamp=" + (stampDate.getTime()),
                    data: {content: JSON.stringify({"txt_array": parsedContentResults[1]})},
                    dataType: "json",
                    error: function(xhr, status, error) {
                        console.log("AJAX brand_check request failed " + error);
                    },
                    success: function (data) {
                        if (data.error == true) {
                            console.log(data.message);
                            return;

                        } else {
                            updateBrandCheckDisplay(data);
                        }
                    }
                });

            }

        }

        var editedBody = $(ed.getBody()).html();
        if (ed.settings.connectedContentIntel) {
            editedBody = ed.settings.connectedContentIntel.contentProvider();
        }
        editedBody = editedBody.replace(/<br ?\/?>/gi, "\n");

        var edContent = $("<div>" + editedBody + "</div>");

        var markupContentSelection = ed.selection != null ? ed.selection.getContent() : '';
        if ($.trim(markupContentSelection).length == 0) {
            markupContentSelection = editedBody;
        }

        if ( markupContentSelection.indexOf('</p>') === -1 )
            markupContentSelection = "<p>" + markupContentSelection + "</p>";
        markupContentSelection = "<div>" + markupContentSelection + "</div>";

        var {contentArray, contentClone, variablesArray} = extractSentencesArray(markupContentSelection, ed);
        ed.settings.contentintel.content_template = contentClone;

        // No Content: return
        if ( $.trim( contentArray.join("") ).length == 0 ) {

            $(ed.editorContainer).find('#mce_sentiment_btn .mce-status-ind, #mce_readability_btn .mce-status-ind, #mce_brandcheck_btn .mce-status-ind, #mce_translate_btn .mce-status-ind').css({'background-color':'white'});
            $(ed.editorContainer).find('#mce_sentiment_btn, #mce_readability_btn, #mce_brandcheck_btn, #mce_translate_btn').removeAttr('title').removeAttr('data-toggle');
            $('#mce_contentcompare_btn').find('.fa-exclamation-circle').remove();
            $(getTopFrame().document).find('#mce_summarize_panel,#mce_sentiment_panel,#mce_readability_panel, #iFramePopup_brandCheck, #mce_plain_panel, #mce_translate_panel, #iFramePopup_contentCompare').remove();

            return false;
        }
        var doc = $(getTopFrame().document);
        $(doc).find('#suggest_panel').hide();
        if ( ed.settings.contentintel.sentiment_enabled )
            getSentiment( contentArray, variablesArray);

        if ( ed.settings.contentintel.readability_enabled )
            getReadability( contentArray, variablesArray);

        if ( ed.settings.contentintel.contentcompare_enabled )
            getHasSimOrDup();

        if ( ed.settings.contentintel.summarize_enabled )
            getSummarize();

        if ( ed.settings.contentintel.brandcheck_enabled )
            getBrandCheck();

        if ( ed.settings.contentintel.plain_enabled )
            getPlainLanguage();

        if ( ed.settings.contentintel.translate_enabled && $(doc).find('.mceTranslateContent') !== undefined && $(doc).find('.mceTranslateContent').length > 0)
            getTranslate();

        if ( ed.settings.contentintel.translate_enabled ) {
            getTranslationAccuracy(false);
        }

        return true;

    }

    function updateSummarizeDisplay(data) {
        var doc = $(getTopFrame().document);
        let statusPanel = $(ed.editorContainer);
        if(ed.settings.connectedContentIntel){
            statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
        }

        if ( ed.settings.contentintel.summarize.is_panel_active ) {
            var template = $(ed.settings.contentintel.content_template).clone();
            data.summarize.rewrite_html = data.summarize.rewrite_html.replace(/\\"/g,'"');
            $(template).html(data.summarize.rewrite_html);
            // Remove formatting and other non textual elements
            $(template).find('.mceStaticElement,.mceEditPointIndicator,.interactive-page-break').remove();
            $(template).find('p').each(function() {
                if ( $.trim( $(this).text() ).length == 0 )
                    $(this).remove();
            });

            if ( $(template).find('.mceDraggable').length != 0 )
                template = applyCanvasContainer(template);

            const summarizeInfo =
                "<div style=\"padding: 50px 24px; margin-top: 12px; position: relative; background-color: #f5f5f5;\" class=\"showIfLicenced\">" +
                "<div style=\"position: absolute; left: 10px; top: 4px;\">" +
                "<label class=\"labelText\" style=\"color: black; padding-left:0.625rem; margin-bottom: 0.1rem; margin-top:0.5rem\">" +
                client_messages.text.summarize.label.prefix +
                "&nbsp;&nbsp;</label>" +
                "<msgpt:InputFilter type=\"numeric\">" +
                "<input  type=\"numeric\"  id=\"summarizeDesiredLength\" min=\"10\" step=\"1\"  style=\"padding-left: 2px; padding-right: 2px; width:50px;\" \>" +
                "</msgpt:InputFilter>&nbsp;&nbsp;" +  client_messages.text.summarize.label.suffix +
                "</div>"+
                "<div style=\"position: absolute; left: 10px; top: 30px; padding-bottom: 15px; padding-top: 25px; \">" +
                "<button type=\"button\" id=\"btnPasteRewrite\" class=\"btn btn-outline-primary mr-2 btnPasteRewriteSummarize\" style=\"display: none;\" " +
                "		value=\"pasterewrite\" data-toggle=\"tooltip\"\ title=\"" + client_messages.text.insert_rewrite + "\" data-html=\"true\">" +
                "<i class=\"far fa-paste fa-lg\" aria-hidden=\"true\">&nbsp;</i>" +
                client_messages.text.suggestion_accept +
                "</button>" +
                "<button type=\"button\" id=\"btnSuggestRewrite\" class=\"btn btn-primary\" " +
                "		value=\"suggestrewrite\" data-toggle=\"tooltip\"\ title=\"" +
                client_messages.text.auto_summarize_rewrite + "\" data-html=\"true\">" +
                client_messages.content_editor.summarize +
                "</button>" +
                "<div style=\"display: inline-block; vertical-align: middle;\">" +
                "   <div id=\"rewriteMarkupToggleContainer\" style=\"margin-left: 8px; vertical-align: middle; display: none;\">" +
                "       <div style=\"display: inline-block; vertical-align: middle;\">" +
                "         <i id=\"rewriteToggleMarkupBtn\" " +
                "               style=\"font-size: 19px; cursor: pointer; padding-top: 4px;\" " +
                "               class=\"far fa-toggle-on\"></i>" +
                "       </div>" +
                "       <div style=\"display: inline-block; vertical-align: middle; font-size: 11px; padding-right: 8px; padding-left: 4px;\">" +
                client_messages.text.show_markup +
                "       </div>" +
                "   </div>" +
                "</div>" +
                "</div>" +
                "</div>" +
                "<div class=\"helpTextContainer\" style=\"border-radius: 6px; background-color: #f5f5f5; border: 2px solid #e1e1e1; padding: 10px 24px; max-height:500 px; height: 450px; overflow-y: auto; position: relative;\">" +
                client_messages.content_editor.summarize + ":" +
                "<div id=\"helpText_readability\" class=\"helpText\" style=\"margin-top: 12px;\">" +
                client_messages.text.summary_info +
                "</div>" +
                "</div>";

            $(doc).find('.tooltip').remove();
            $(doc).find('.crtSentencesNo').html(data.summarize.sentences);
            $(doc).find('.crtWordsNo').html(data.summarize.words);
            $(doc).find('.crtCharactersNo').html(data.summarize.characters);
            $(doc).find('.crtAverageReadTime').html(data.summarize.averageReadTime);

            var tooltip = "Words: " + data.summarize.words + "<br> Avg reading time: "+data.summarize.averageReadTime;

            statusPanel.find('#mce_summarize_btn')
                .attr('data-original-title', tooltip)
                .attr('data-toggle', 'tooltip').tooltip();

            $(doc).find('.mceSummarizeContent').html( $(template).html());
            $(doc).find('#suggest_buttons_panel').html(summarizeInfo);
            if(!data.hasAIRewriteLicence) {
                $(doc).find('.showIfLicenced').hide();
                var frameHeight = $(getTopFrame().document).find('#page').height();
                $(getTopFrame().document).find('#mce_summarize_panel .mceSummarizeContent')
                    .css({maxHeight: frameHeight + 'px', height: 250 + 'px'});
            }

            $(doc).find("#summarizeDesiredLength").attr("max", data.summarize.words);
            setSummarizeLimitValue(data);
            // var minWordCountText = $(doc).find('#summarizeDesiredLength').val();
            $($(doc).find("#summarizeDesiredLength")[0])
                .on( "focus", function() {
                    $(getTopFrame().document).find('#summarize_limit_error_popup').hide();
                    $(doc).find('#btnSuggestRewrite').removeAttr('disabled');
                } );

            if(data.hasAIRewriteLicence) {
                $(doc).find('#mce_summarize_panel #btnSuggestRewrite').click(function () {
                    validateAndSaveLimitValue();
                    if ($(doc).find('#mce_summarize_panel #btnSuggestRewrite')[0].getAttribute("disabled") != 'disabled') {
                        requestRewrite('summarize');
                    }
                });
                $(doc).find('#mce_summarize_panel #btnPasteRewrite').click(function () {
                    pasteRewrite('Summarize');
                });
                $(doc).find('#mce_summarize_panel #rewriteToggleMarkupBtn').click(function () {
                    toggleRewriteMarkup(true, this);
                });
            }

            $(doc).find('#mce_summarize_panel [data-toggle=tooltip]').each( function() {
                getTopFrame().initTooltip(this);
            });
        }
    }

    function updatePlainLanguageDisplay(data) {
        var doc = $(getTopFrame().document);
        let statusPanel = $(ed.editorContainer);
        if(ed.settings.connectedContentIntel){
            statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
        }

        if ( ed.settings.contentintel.plain.is_panel_active ) {
            var template = $(ed.settings.contentintel.content_template).clone();
             data.plain.rewrite_html = data.plain.rewrite_html.replace(/\\"/g,'"');
            $(template).html(data.plain.rewrite_html);
            // Remove formatting and other non textual elements
            $(template).find('.mceStaticElement,.mceEditPointIndicator,.interactive-page-break').remove();
            $(template).find('p').each(function() {
                if ( $.trim( $(this).text() ).length == 0 )
                    $(this).remove();
            });

            if ( $(template).find('.mceDraggable').length != 0 )
                template = applyCanvasContainer(template);

            const plainInfo =
                "<div style=\"padding: 40px 24px; margin-top: 12px; position: relative; background-color: #f5f5f5;\" class=\"showIfLicenced\">" +
                "<div style=\"position: absolute; left: 10px; top: 10px; padding-bottom: 15px; padding-top: 15px; \">" +
                "<button type=\"button\" id=\"btnPasteRewrite\" class=\"btn btn-outline-primary mr-2 btnPasteRewritePlain\" style=\"display: none;\" " +
                "		value=\"pasterewrite\" data-toggle=\"tooltip\"\ title=\"" + client_messages.text.insert_rewrite + "\" data-html=\"true\">" +
                "<i class=\"far fa-paste fa-lg\" aria-hidden=\"true\">&nbsp;</i>" +
                client_messages.text.suggestion_accept +
                "</button>" +
                "<button type=\"button\" id=\"btnSuggestRewrite\" class=\"btn btn-primary\" " +
                "		value=\"suggestrewrite\" data-toggle=\"tooltip\"\ title=\"" +
                client_messages.text.auto_plain_rewrite_tooltip + "\" data-html=\"true\">" +
                client_messages.content_editor.plain_rewrite +
                "</button>" +
                "</div>" +
                "</div>" +
                "<div class=\"helpTextContainer\" style=\"border-radius: 6px; background-color: #f5f5f5; border: 2px solid #e1e1e1; padding: 10px 24px; max-height:500 px; height: 450px; overflow-y: auto; position: relative;\">" +
                client_messages.content_editor.plain + ":" +
                "<div id=\"helpText_plain\" class=\"helpText\" style=\"margin-top: 12px;\">" +
                client_messages.text.plain_info +
                "</div>" +
                "</div>";

            $(doc).find('.tooltip').remove();
            $(doc).find('.crtSentencesNo').html(data.plain.sentences);
            $(doc).find('.crtWordsNo').html(data.plain.words);
            $(doc).find('.crtCharactersNo').html(data.plain.characters);
            $(doc).find('.crtAverageReadTime').html(data.plain.averageReadTime);

            $(doc).find('.mcePlainContent').html( $(template).html());
            $(doc).find('#suggest_buttons_panel').html(plainInfo);
            if(!data.hasAIRewriteLicence) {
                $(doc).find('.showIfLicenced').hide();
                var frameHeight = $(doc).find('#page').height();
                $(getTopFrame().document).find('#mce_plain_panel .mcePlainContent')
                    .css({maxHeight: frameHeight + 'px', height: 230 + 'px'});
            }

            if(data.hasAIRewriteLicence) {
                $(doc).find('#mce_plain_panel #btnSuggestRewrite').click(function () {
                    if ($(doc).find('#mce_plain_panel #btnSuggestRewrite')[0].getAttribute("disabled") != 'disabled') {
                        requestRewrite('plain');
                    }
                });
                $(doc).find('#mce_plain_panel #btnPasteRewrite').click(function () {
                    pasteRewrite('Plain Language');
                });
                $(doc).find('#mce_plain_panel #rewriteToggleMarkupBtn').click(function () {
                    toggleRewriteMarkup(true, this);
                });
            }

            $(doc).find('#mce_plain_panel [data-toggle=tooltip]').each( function() {
                getTopFrame().initTooltip(this);
            });
        }
    }

    function updateTranslateDisplay(data) {
        var doc = $(getTopFrame().document);
        let statusPanel = $(ed.editorContainer);
        if(ed.settings.connectedContentIntel){
            statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
        }

        if ( ed.settings.contentintel.translate.is_panel_active ) {
            var template = $(ed.settings.contentintel.content_template).clone();
            data.translate.rewrite_html = data.translate.rewrite_html.replace(/\\"/g,'"');
            $(template).html(data.translate.rewrite_html);
            // Remove formatting and other non textual elements
            $(template).find('.mceStaticElement,.mceEditPointIndicator,.interactive-page-break').remove();
            $(template).find('p').each(function() {
                if ( $.trim( $(this).text() ).length == 0 )
                    $(this).remove();
            });

            if ( $(template).find('.mceDraggable').length != 0 )
                template = applyCanvasContainer(template);

            const translateInfo =
                "<div style=\"padding: 40px 24px;  position: relative; background-color: #f5f5f5;\" class=\"showIfLicenced\">" +
                "<div style=\"position: absolute; left: 10px; top: 10px; padding-bottom: 15px; padding-top: 15px; \">" +
                "<button type=\"button\" id=\"btnPasteRewrite\" class=\"btn btn-outline-primary mr-2 btnPasteRewriteTranslate\" style=\"display: none;\" " +
                "		value=\"pasterewrite\" data-toggle=\"tooltip\"\ title=\"" + client_messages.text.insert_rewrite + "\" data-html=\"true\">" +
                "<i class=\"far fa-paste fa-lg\" aria-hidden=\"true\">&nbsp;</i>" +
                client_messages.text.suggestion_accept +
                "</button>" +
                "<button type=\"button\" id=\"btnSuggestRewrite\" class=\"btn btn-primary\" " +
                "		value=\"suggestrewrite\" data-toggle=\"tooltip\"\ title=\"" +
                client_messages.text.auto_translate_rewrite_tooltip + "\" data-html=\"true\">" +
                client_messages.content_editor.translate_rewrite +
                "</button>" +
                "</div>" +
                "</div>" +
                "<div class=\"helpTextContainer\" style=\"border-radius: 6px; background-color: #f5f5f5; border: 2px solid #e1e1e1; padding: 10px 24px; max-height:500 px; height: 450px; overflow-y: auto; position: relative;\">" +
                client_messages.content_editor.translate + ":" +
                "<div id=\"helpText_translate\" class=\"helpText\" style=\"margin-top: 12px;\">" +
                client_messages.text.translate_info +
                "</div>" +
                "</div>";

            $(doc).find('.tooltip').remove();
            $(doc).find('.crtLanguage').html(data.translate.language);


            $(doc).find('.mceTranslateContent').html( $(template).html());
            $(doc).find('#suggest_buttons_panel').html(translateInfo);
            if(!data.hasAITranslateLicence) {
                $(doc).find('.showIfLicenced').hide();
                var frameHeight = $(doc).find('#page').height();
                $(getTopFrame().document).find('#mce_translate_panel .mceTranslateContent')
                    .css({maxHeight: frameHeight + 'px', height: 200 + 'px'});
            }

            if(data.hasAITranslateLicence) {
                var crtLocaleCode = data.translate.crt_lang_code;
                var defaultLocaleBinding = $('div[groupId='+ed.settings.currentContentGroupId+'] .defaultLocaleContent');
                var defaultLocaleId = $(defaultLocaleBinding).closest("[localeId]").attr("localeId");
                const crtIdsList = data.translate.crt_ids_list.split(',');
                if(crtLocaleCode === data.translate.translate_lang_code) {
                    if(crtIdsList.includes(defaultLocaleId)) {
                        $($(doc).find('#mce_translate_panel #btnSuggestRewrite')[0]).attr('disabled', 'disabled');
                        $(doc).find('#helpText_translate').html("<div style=\"color: black; font-size: 14px;\">NOTE: Selected editor content is already translated to the current language.</div>");
                    } else {
                        $($(doc).find('#mce_translate_panel #btnSuggestRewrite')[0]).removeAttr('disabled');
                    }
                } else {
                    $($(doc).find('#mce_translate_panel #btnSuggestRewrite')[0]).removeAttr('disabled');
                    if(crtIdsList.includes(defaultLocaleId)) {
                        $(doc).find('.crtAccuracy').hide();
                        $(ed.editorContainer).find('#mce_translate_btn .mce-status-ind').css({'background-color':'gray'});
                        $(doc).find('#mce_translate_panel .mce-status-ind').css({'background-color': 'gray'});
                        let statusPanel = $(ed.editorContainer);
                            var tooltipTxt = client_messages.content_editor.translate_tootip;
                            statusPanel.find('#mce_translate_btn')
                                .attr('data-original-title', tooltipTxt)
                                .attr('data-toggle', 'tooltip').tooltip();
                            $(doc).find('.mcePanelAccuracy .mce-status-ind')
                                .attr('data-original-title', tooltipTxt)
                                .attr('data-toggle', 'tooltip').tooltip();
                    }
                }

                $(doc).find('#mce_translate_panel #btnSuggestRewrite').click(function () {
                    if ($(doc).find('#mce_translate_panel #btnSuggestRewrite')[0].getAttribute("disabled") != 'disabled') {
                        requestRewrite('translate');
                    }
                });
                $(doc).find('#mce_translate_panel #btnPasteRewrite').click(function () {
                    pasteRewrite('Translation');
                });
                $(doc).find('#mce_translate_panel #rewriteToggleMarkupBtn').click(function () {
                    toggleRewriteMarkup(true, this);
                });
            }

            $(doc).find('#mce_translate_panel [data-toggle=tooltip]').each( function() {
                getTopFrame().initTooltip(this);
            });
        }
    }

    function updateReadabilityDisplay(data) {

        function getGradeFromScore(score, levels) {
            if ( score < 0 )
                return levels[0];
            if ( score > 100 )
                return levels[levels.length -1];
            for ( var i=0; i < levels.length; i++ ) {
                if ( score >= levels[i].bottom_grade_threshold && score <= levels[i].top_grade_threshold )
                    return levels[i];
            }
        }

        var doc = $(getTopFrame().document);
        var results = data.combined_result;

        var gradeScore = getGradeFromScore( data.combined_result.flesch_kincaid, data.flesch_levels );

        let statusPanel = $(ed.editorContainer);
        if(ed.settings.connectedContentIntel){
            statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
        }
        if ( results && results.flesch_kincaid ) {
            if ( results.flesch_kincaid < data.target_readability.top_grade_threshold ) {
                statusPanel.find('#mce_readability_btn .mce-status-ind').css({'background-color': data.has_difficult_sentence ? '#fcbf55' : '#00d440'});
                $(doc).find('#mce_readability_panel .mce-status-ind').css({'background-color': data.has_difficult_sentence ? '#fcbf55' : '#00d440'});
            } else {
                statusPanel.find('#mce_readability_btn .mce-status-ind').css({'background-color':'#fd0303'});
                $(doc).find('#mce_readability_panel .mce-status-ind').css({'background-color':'#fd0303'});
            }

            var tooltipTxt = client_messages.content_editor.flesch_kincaid_grade_level + ": " +
                (Math.round(data.combined_result.flesch_kincaid * 10) / 10) +
                " (" + (gradeScore.description.charAt(0).toUpperCase() + gradeScore.description.slice(1)) + ")";
            if ( statusPanel.find('#mce_readability_btn').attr('title') != undefined ) {
                statusPanel.find('#mce_readability_btn').tooltip('hide')
                    .attr('data-original-title', tooltipTxt);
            } else {
                statusPanel.find('#mce_readability_btn')
                    .attr('title', tooltipTxt)
                    .attr('data-toggle', 'tooltip').tooltip();
            }

		}

		if ( ed.settings.contentintel.readability.is_panel_active ) {
			var template = $(ed.settings.contentintel.content_template).clone();
			$(template).find("[id^='ind_']").each( function() {
				var currentInd = parseInt(parseId(this));
				if (data.block_results && data.block_results.length > currentInd) {
                    var dataBlock = data.block_results[currentInd];
                    dataBlock=  dataBlock.replaceAll("\n", "<br/>");
					$(this).replaceWith(dataBlock);
				}
			});

			// Remove formatting and other non textual elements
			$(template).find('.mceStaticElement,.mceEditPointIndicator,.interactive-page-break').remove();
			$(template).find('p').each(function() {
				if ( $.trim( $(this).text() ).length == 0 )
					$(this).remove();
			});

            if ( $(template).find('.mceDraggable').length != 0 )
                template = applyCanvasContainer(template);
            var selectionTarget = 				"<div style=\"position: absolute; left: 10px; top: 4px;\"><label>" + client_messages.text.readability.target.label + "&nbsp;<select id=\"targetSuggestedReadability\" name=\"targetReadability\" style='height: 40px;  padding: 0 5px; width: 130px;'>";

            for (let i = 0; i < data.flesch_levels.length; i++) {
                var level = data.flesch_levels[i];
                var selectedOption ="";
                if(data.target_readability.semantexValue == level.semantexValue) {
                    selectedOption = selectedOption + " selected=\"selected\"";
                }
                selectionTarget = selectionTarget + "<option value=\"" + level.semantexValue + "\"" + selectedOption + ">" + level.label + "</option>"
            }
            selectionTarget = selectionTarget+ "</select></label> </div>";

            const readabilityInfo =
                "<div style=\"padding: 50px 24px; margin-top: 12px; position: relative; background-color: #f5f5f5;\" class=\"showIfLicenced\">" +
                selectionTarget +
                "<div style=\"position: absolute; left: 10px; top: 30px; padding-bottom: 15px; padding-top: 25px;\">" +
                "<button type=\"button\" id=\"btnPasteRewrite\" class=\"btn btn-outline-primary mr-2 btnPasteRewriteReadability\" style=\"display: none;\" " +
                "		value=\"pasterewrite\" data-toggle=\"tooltip\"\ title=\"" + client_messages.text.insert_rewrite + "\" data-html=\"true\">" +
                "<i class=\"far fa-paste fa-lg\" aria-hidden=\"true\">&nbsp;</i>" +
                client_messages.text.suggestion_accept +
                "</button>" +
                "<button type=\"button\" id=\"btnSuggestRewrite\" class=\"btn btn-primary\" " +
                "		value=\"suggestrewrite\" data-toggle=\"tooltip\"\ title=\"" +
                client_messages.text.auto_readability_rewrite + "\" data-html=\"true\">" +
                client_messages.text.suggest_rewrite +
                "</button>" +
                "<div style=\"display: inline-block; vertical-align: middle;\">" +
                "   <div id=\"rewriteMarkupToggleContainer\" style=\"margin-left: 8px; vertical-align: middle; display: none;\">" +
                "       <div style=\"display: inline-block; vertical-align: middle;\">" +
                "         <i id=\"rewriteToggleMarkupBtn\" " +
                "               style=\"font-size: 19px; cursor: pointer; padding-top: 4px;\" " +
                "               class=\"far fa-toggle-on\"></i>" +
                "       </div>" +
                "       <div style=\"display: inline-block; vertical-align: middle; font-size: 11px; padding-right: 8px; padding-left: 4px;\">" +
                          client_messages.text.show_markup +
                "       </div>" +
                "   </div>" +
                "</div>" +
                "</div>" +
                "</div>" +
                "<div class=\"helpTextContainer\" style=\"border-radius: 6px; background-color: #f5f5f5; border: 2px solid #e1e1e1; padding: 10px 24px; max-height:500 px; height: 450px; overflow-y: auto; position: relative;\">" +
                client_messages.content_editor.readability + ":" +
                "<div id=\"helpText_readability\" class=\"helpText\" style=\"margin-top: 12px;\">" +
                client_messages.text.readability_info +
                "</div>" +
                "</div>";

            $(doc).find('.tooltip').remove();

            $(doc).find('.mceReadabilityContent').html( $(template).html());
            $(doc).find('#suggest_buttons_panel').html(readabilityInfo);
            if(!data.hasAIRewriteLicence) {
                $(doc).find('.showIfLicenced').hide();
                var frameHeight = $(getTopFrame().document).find('#mce_readability_panel').outerHeight() - 225;
                $(getTopFrame().document).find('#mce_readability_panel .mceReadabilityContent')
                    .css({maxHeight: frameHeight + 'px', height: 250 + 'px'});
            }


            $(doc).find('.mceReadabilityContent .mceSentence').each( function() {
                $(this)
                    .mouseover( function() {
                        $(this).closest('.mceReadabilityContent').find('.mceSentence').css({'opacity':'0.5'});
                        $(this).css({'opacity':'1.0'});
                    }).mouseout( function() {
                    $(this).closest('.mceReadabilityContent').find('.mceSentence').css({'opacity':'1.0'});
                });
            });

            $(doc).find('#mce_readability_panel .fleschKincaidScore').html( (Math.round(data.combined_result.flesch_kincaid * 10) / 10) + " (<span style=\"text-transform: capitalize;\">" + gradeScore.description + "</span>)" );

            $(doc).find('#mce_readability_panel .fleschReadabilityScore')
                .html( gradeScore.label )
                .attr('title',gradeScore.description);
            $(doc).find('#mce_readability_panel .targetReadabilityRange')
                .html( data.target_readability.label )
                .attr('title',data.target_readability.description);
            if(data.hasAIRewriteLicence) {
                $(doc).find('#mce_readability_panel #btnSuggestRewrite').click(function () {
                    requestRewrite('readability');
                });
                $(doc).find('#mce_readability_panel #btnPasteRewrite').click(function () {
                    pasteRewrite('Readability');
                });
                $(doc).find('#mce_readability_panel #rewriteToggleMarkupBtn').click(function () {
                    toggleRewriteMarkup(true, this);
                });
            }

            $(doc).find('#mce_readability_panel [data-toggle=tooltip]').each( function() {
                getTopFrame().initTooltip(this);
            });
        }
    }

    function updateRewriteReadabilityDisplay(data) {

        function getGradeFromScore(score, levels) {
            if (score < 0)
                return levels[0];
            if (score > 100)
                return levels[levels.length - 1];
            for (var i = 0; i < levels.length; i++) {
                if (score >= levels[i].bottom_grade_threshold && score <= levels[i].top_grade_threshold)
                    return levels[i];
            }
        }

        var doc = $(getTopFrame().document);
        var results = data.combined_result;

        var gradeScore = getGradeFromScore(data.combined_result.flesch_kincaid, data.flesch_levels);

        let statusPanel = $(ed.editorContainer);
        if (ed.settings.connectedContentIntel) {
            statusPanel = $("#" + ed.settings.connectedContentIntel.marciePanelId);
        }

        if (ed.settings.contentintel.readability.is_panel_active) {
            var template = $(ed.settings.contentintel.content_template).clone();
            var dataAll = "<div>";
            for (let i = 0; i < data.block_results.length; i++) {
                dataAll = dataAll + "<p>" + data.block_results[i] +"</p>";
            }
            dataAll = dataAll + "</div>";
            $(template).html(dataAll);

            // Remove formatting and other non textual elements
            $(template).find('.mceStaticElement,.mceEditPointIndicator,.interactive-page-break').remove();
            $(template).find('p').each(function () {
                if ($.trim($(this).text()).length == 0)
                    $(this).remove();
            });

            if ($(template).find('.mceDraggable').length != 0)
                template = applyCanvasContainer(template);

            $(doc).find('.tooltip').remove();

            // Configure result text display
            $(doc).find('.rewriteResultText').html( util_restoreStaticElementsText($(template).html()) );
            $(doc).find('.rewriteResultText .mceSentence').each(function () {
                $(this)
                    .mouseover(function () {
                        $(this).closest('.mceReadabilitySugestedContent').find('.mceSentence').css({'opacity': '0.5'});
                        $(this).css({'opacity': '1.0'});
                    }).mouseout(function () {
                    $(this).closest('.mceReadabilitySugestedContent').find('.mceSentence').css({'opacity': '1.0'});
                });
            });

            // Configure result markup display
            var rewriteHTML = util_restoreStaticElementsMarkup(data.rewrite_html);
            rewriteHTML = util_restoreHTMLattributes(rewriteHTML, data.attr);
            $(doc).find('.rewriteResultMarkup').html(rewriteHTML);

            toggleRewriteMarkup(false,$(doc).find('#rewriteToggleMarkupBtn'));

            // Configure result readability display
            $(doc).find('.suggestedFleschKincaidScore').html((Math.round(data.combined_result.flesch_kincaid * 10) / 10) + " (<span style=\"text-transform: capitalize;\">" + gradeScore.description + "</span>)");

            $(doc).find('suggestedFleschReadabilityScore')
                .html(gradeScore.label)
                .attr('title', gradeScore.description);
            $(doc).find('#suggest_panel .targetSuggestedReadabilityRange')
                .html(data.target_readability.label)
                .attr('title', data.target_readability.description);

            $(doc).find('#mce_readability_panel #btnPasteRewrite').click(function( ) {
                ed.undoManager.add();
            });

            $(doc).find('#mce_readability_panel [data-toggle=tooltip]').each( function() {
                getTopFrame().initTooltip(this);
            });
        }

        $(doc).find('.mce-suggest-status-ind').show();

    }

    function updateSentimentDisplay(data) {

        var doc = $(getTopFrame().document);
        var results = data.combined_result;

        if ( results && results.label ) {

            var sentimentScoreData = results.conf;
            var pos = Math.round(parseFloat(sentimentScoreData.positive) * 1000) / 10;
            var neg = Math.round(parseFloat(sentimentScoreData.negative) * 1000) / 10;
            var neut = Math.round(parseFloat(sentimentScoreData.neutral) * 1000) / 10;
            var mix = Math.round(parseFloat(sentimentScoreData.mixed) * 1000) / 10;
            var sentimentScore =
                client_messages.content_editor.positive + " " + pos + "% | " +
                client_messages.content_editor.negative + " " + neg + "% | " +
                client_messages.content_editor.neutral + " " + neut + "% | " +
                client_messages.content_editor.mixed + " " + mix + "%";

            let statusPanel = $(ed.editorContainer);
            if(ed.settings.connectedContentIntel){
                statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
            }
            if ( results.label.toLowerCase() == "negative" ) {
                statusPanel.find('#mce_sentiment_btn .mce-status-ind').css({'background-color':'#fd0303'});
                $(doc).find('#mce_sentiment_panel .mce-status-ind').css({'background-color':'#fd0303'});
            } else if ( results.label.toLowerCase() == "positive" ) {
                statusPanel.find('#mce_sentiment_btn .mce-status-ind').css({'background-color':'#00d440'});
                $(doc).find('#mce_sentiment_panel .mce-status-ind').css({'background-color':'#00d440'});
            } else {
                statusPanel.find('#mce_sentiment_btn .mce-status-ind').css({'background-color':'white'});
                $(doc).find('#mce_sentiment_panel .mce-status-ind').css({'background-color':'white'});
            }

            if (statusPanel.find('#mce_sentiment_btn').attr('title') != undefined) {
                statusPanel.find('#mce_sentiment_btn').tooltip('hide').attr('data-original-title', sentimentScore);
            } else {
                statusPanel.find('#mce_sentiment_btn')
                    .attr('title', sentimentScore)
                    .attr('data-toggle', 'tooltip').tooltip();
            }
            if ( ed.settings.contentintel.sentiment.is_panel_active ) {
                var template = $(ed.settings.contentintel.content_template).clone();
                $(template).find("[id^='ind_']").each( function() {
                    var currentInd = parseInt(parseId(this));
                    if (data.block_results && data.block_results.length > currentInd) {
                        var dataBlock = data.block_results[currentInd];
                        dataBlock=  dataBlock.replaceAll("\n", "<br/>");
                        $(this).replaceWith(dataBlock);
                    }
                });

                // Remove formatting and other non textual elements
                $(template).find('.mceStaticElement,.mceEditPointIndicator,.interactive-page-break').remove();
                $(template).find('p').each(function() {
                    if ( $.trim( $(this).text() ).length == 0 )
                        $(this).remove();
                });
                if ( $(template).find('.mceDraggable').length != 0 )
                    template = applyCanvasContainer(template);

                const sentimentInfo =
                    "<div style=\"padding: 30px 24px; margin-top: 12px; position: relative;  background-color: #f5f5f5;\" class=\"showIfLicenced\">" +
                    "<div style=\"position: absolute; left: 10px; top: 14px;\">" +
                    "<button type=\"button\" id=\"btnPasteRewrite\" class=\"btn btn-outline-primary mr-2 btnPasteRewriteSentiment\" style=\"display: none;\" " +
                    "		value=\"pasterewrite\" data-toggle=\"tooltip\"\ title=\"" + client_messages.text.insert_rewrite + "\" data-html=\"true\">" +
                    "<i class=\"far fa-paste fa-lg\" aria-hidden=\"true\">&nbsp;</i>" +
                        client_messages.text.suggestion_accept +
                    "</button>" +
                    "<button type=\"button\" id=\"btnSuggestRewrite\" class=\"btn btn-primary\" " +
                    "		value=\"suggestrewrite\" data-toggle=\"tooltip\"\ title=\"" +
                    client_messages.text.auto_sentiment_rewrite + "\" data-html=\"true\">" +
                    client_messages.text.suggest_rewrite +
                    "</button>" +
                    "<div style=\"display: inline-block; vertical-align: middle;\">" +
                    "   <div id=\"rewriteMarkupToggleContainer\" style=\"margin-left: 8px; vertical-align: middle; display: none;\">" +
                    "       <div style=\"display: inline-block; vertical-align: middle;\">" +
                    "         <i id=\"rewriteToggleMarkupBtn\" " +
                    "               style=\"font-size: 19px; cursor: pointer; padding-top: 4px;\" " +
                    "               class=\"far fa-toggle-on\"></i>" +
                    "       </div>" +
                    "       <div style=\"display: inline-block; vertical-align: middle; font-size: 11px; padding-right: 8px; padding-left: 4px;\">" +
                                client_messages.text.show_markup +
                    "       </div>" +
                    "   </div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"helpTextContainer\" style=\"border-radius: 6px; background-color: #f5f5f5; border: 1px solid #e1e1e1; padding: 10px 24px; height: 450px; overflow-y: auto; position: relative;\">" +
                    client_messages.content_editor.sentiment + ":" +
                    "<div id=\"helpText_setiment\" class=\"helpText\" style=\"margin-top: 12px;\">" +
                    client_messages.text.sentiment_info +
                    "</div>" +
                    "</div>";

                $(doc).find('.tooltip').remove();

                $(doc).find('.mceSentimentContent').html( $(template).html());
                $(doc).find('#suggest_buttons_panel').html(sentimentInfo);
                $(doc).find('#suggest_buttons_panel').html(sentimentInfo);
                if(!data.hasAIRewriteLicence) {
                    $(doc).find('.showIfLicenced').hide();
                    var frameHeight = $(getTopFrame().document).find('#mce_sentiment_panel').outerHeight() - 225;
                    $(getTopFrame().document).find('#mce_sentiment_panel .mceSentimentContent')
                        .css({maxHeight: frameHeight + 'px', height: 250 + 'px'});
                }

                $(doc).find('.mceSentimentContent .mceSentence').each(function () {
                    $(this)
                        .mouseover(function () {
                            $(this).closest('.mceSentimentContent').find('.mceSentence').css({'opacity': '0.5'});
                            $(this).css({'opacity': '1.0'});
                        }).mouseout(function () {
                        $(this).closest('.mceSentimentContent').find('.mceSentence').css({'opacity': '1.0'});
                    });
                });
                if(data.hasAIRewriteLicence) {
                    $(doc).find('#mce_sentiment_panel #btnSuggestRewrite').click(function () {
                        requestRewrite('sentiment');
                    });
                    $(doc).find('#mce_sentiment_panel #btnPasteRewrite').click(function () {
                        pasteRewrite('Sentiment');
                    });

                    $(doc).find('#mce_sentiment_panel #rewriteToggleMarkupBtn').click(function () {
                        toggleRewriteMarkup(true, this);
                    });
                }

                var sentimentTxtData = data.combined_result.label;
                $(doc).find('.mceSentimentScore').html( sentimentTxtData.charAt(0).toUpperCase() + sentimentTxtData.toLowerCase().slice(1) );
                $(doc).find('.mceSentimentLiteral').html( sentimentScore );

                $(doc).find('#mce_sentiment_panel [data-toggle=tooltip]').each( function() {
                    getTopFrame().initTooltip(this);
                });
            }
        }
    }

    function updateRewriteSentimentDisplay(data) {

        var doc = $(getTopFrame().document);
        var results = data.combined_result;

        if ( results && results.label ) {

            var sentimentScoreData = results.conf;
            var pos = Math.round(parseFloat(sentimentScoreData.positive) * 1000) / 10;
            var neg = Math.round(parseFloat(sentimentScoreData.negative) * 1000) / 10;
            var neut = Math.round(parseFloat(sentimentScoreData.neutral) * 1000) / 10;
            var mix = Math.round(parseFloat(sentimentScoreData.mixed) * 1000) / 10;
            var sentimentScore =
                client_messages.content_editor.positive + " " + pos + "% | " +
                client_messages.content_editor.negative + " " + neg + "% | " +
                client_messages.content_editor.neutral + " " + neut + "% | " +
                client_messages.content_editor.mixed + " " + mix + "%";

            let statusPanel = $(ed.editorContainer);
            if(ed.settings.connectedContentIntel){
                statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
            }

            if (statusPanel.find('#mce_sentiment_btn').attr('title') != undefined) {
                statusPanel.find('#mce_sentiment_btn').tooltip('hide').attr('data-original-title', sentimentScore);
            } else {
                statusPanel.find('#mce_sentiment_btn')
                    .attr('title', sentimentScore)
                    .attr('data-toggle', 'tooltip').tooltip();
            }

            if ( ed.settings.contentintel.sentiment.is_panel_active ) {
                var template = $(ed.settings.contentintel.content_template).clone();
                var dataAll = "<div>";
                for (let i = 0; i < data.block_results.length; i++) {
                    dataAll = dataAll + "<p>" + data.block_results[i] +"</p>";
                }
                dataAll = dataAll + "</div>";
                $(template).html(dataAll);

                // Remove formatting and other non textual elements
                $(template).find('.mceStaticElement,.mceEditPointIndicator,.interactive-page-break').remove();
                $(template).find('p').each(function() {
                    if ( $.trim( $(this).text() ).length == 0 )
                        $(this).remove();
                });

                if ( $(template).find('.mceDraggable').length != 0 )
                    template = applyCanvasContainer(template);

                $(doc).find('.tooltip').remove();

                $(doc).find('.rewriteResultText').html( util_restoreStaticElementsText($(template).html()) );
                $(doc).find('.rewriteMarkupContainer').html( $(template).html());
                var rewriteHTML = util_restoreStaticElementsMarkup(data.rewrite_html);
                rewriteHTML = util_restoreHTMLattributes(rewriteHTML, data.attr);
                $(doc).find('.rewriteResultMarkup').html(rewriteHTML);

                toggleRewriteMarkup(false,$(doc).find('#rewriteToggleMarkupBtn'));

                $(doc).find('.mce_sentiment_panel .mceSentence').each(function () {
                    $(this)
                        .mouseover(function () {
                            $(this).closest('.mceSuggestedSentimentContent').find('.mceSentence').css({'opacity': '0.5'});
                            $(this).css({'opacity': '1.0'});
                        }).mouseout(function () {
                        $(this).closest('.mceSuggestedSentimentContent').find('.mceSentence').css({'opacity': '1.0'});
                    });
                });

                $(doc).find('#mce_sentiment_panel #btnPasteRewrite').click(function( ) {
                    ed.undoManager.add();
                });

                var sentimentTxtData = data.combined_result.label;
                $(doc).find('.mceSuggestedSentimentScore').html( sentimentTxtData.charAt(0).toUpperCase() + sentimentTxtData.toLowerCase().slice(1) );
                $(doc).find('.mceSuggestedSentimentLiteral').html( sentimentScore );

                $(doc).find('#mce_sentiment_panel [data-toggle=tooltip]').each( function() {
                    getTopFrame().initTooltip(this);
                });
            }
        }
    }

    function updateRewriteSummarizeDisplay(data) {

        var doc = $(getTopFrame().document);

        if ( ed.settings.contentintel.summarize.is_panel_active ) {
            var template = $(ed.settings.contentintel.content_template).clone();
            var rewriteTxt = data.summarize.source.replaceAll("\n", "<br/>");
            $(template).html("<p>" + rewriteTxt +"</p>");

            $(template).find('p').each(function() {
                if ( $.trim( $(this).text() ).length == 0 )
                    $(this).remove();
            });
            if ( $(template).find('.mceDraggable').length != 0 )
                template = applyCanvasContainer(template);

            $(doc).find('.tooltip').remove();

            $(doc).find('.suggestedSentencesNo').html(data.summarize.sentences);
            $(doc).find('.suggestedWordsNo').html(data.summarize.words);
            $(doc).find('.suggestedCharactersNo').html(data.summarize.characters);
            $(doc).find('.suggestedAverageReadTime').html(data.summarize.averageReadTime);

            //  Configure result markup display
            var rewriteHTML = util_restoreStaticElementsMarkup(data.summarize.rewrite_html);
            rewriteHTML = util_restoreHTMLattributes(rewriteHTML, data.attr);
            $(doc).find('.rewriteResultMarkup').html(rewriteHTML);

            var markupContent = rewriteHTML;
            if (markupContent.indexOf('</p>') == -1)
                markupContent = "<p>" + markupContent + "</p>";
            markupContent = "<div>" + markupContent + "</div>";
            $(markupContent).find('p').each(function() {
                if ( $.trim( $(this).text() ).length == 0 )
                    $(this).remove();
            });

            var markupContentClone = $(markupContent).clone();
            $(markupContentClone).find('p,li').each(function () {
                $(this).find('.CmCaReT,#cursorPos,.renderedLabelContainer').remove();
                $(this).find('[type=1]').each(function () {
                    if ($(this).find('.renderedSampleValueContainer').length > 0) {
                        $(this).html($(this).find('.renderedSampleValueContainer').text());
                    } else {
                        $(this).html($(this).text());
                    }
                });
                $(this).find('[type=15]').each(function () {
                    if ($(this).find('.mceContentMenuValue').length > 0)
                        $(this).html($(this).find('.mceContentMenuValue').text());
                });
                $(this).find('.mceHiddenContent').each(function () {
                    $(this).html('');
                });
            });

            var content = $(markupContentClone).html();


            $(doc).find('.rewriteResultText').html( content);

            $(doc).find('.rewriteResultMarkup').hide();
            $(doc).find('.rewriteResultText').show();

            $(doc).find('#mce_summarize_panel #btnPasteRewrite').click(function( ) {
                ed.undoManager.add();
            });
        }
    }

    function updateRewritePlainDisplay(data) {

        var doc = $(getTopFrame().document);

        if ( ed.settings.contentintel.plain.is_panel_active ) {
            var template = $(ed.settings.contentintel.content_template).clone();
            var rewriteTxt = data.plain.source.replaceAll("\n", "<br/>");

            $(template).html(data.plain.rewrite_html);
            // Remove formatting and other non textual elements
            $(template).find('.mceStaticElement,.mceEditPointIndicator,.interactive-page-break').remove();
            $(template).find('p').each(function() {
                if ( $.trim( $(this).text() ).length == 0 )
                    $(this).remove();
            });

            if ( $(template).find('.mceDraggable').length != 0 )
                template = applyCanvasContainer(template);

            $(doc).find('.tooltip').remove();

            $(doc).find('.suggestedSentencesNo').html(data.plain.sentences);
            $(doc).find('.suggestedWordsNo').html(data.plain.words);
            $(doc).find('.suggestedCharactersNo').html(data.plain.characters);
            $(doc).find('.suggestedAverageReadTime').html(data.plain.averageReadTime);

            //  Configure result markup display
            var rewriteHTML = util_restoreStaticElementsMarkup(data.plain.rewrite_html);
            rewriteHTML = util_restoreHTMLattributes(rewriteHTML, data.attr);
            $(doc).find('.rewriteResultMarkup').html(rewriteHTML);

            var markupContent = rewriteHTML;
            if (markupContent.indexOf('</p>') == -1)
                markupContent = "<p>" + markupContent + "</p>";
            markupContent = "<div>" + markupContent + "</div>";
            $(markupContent).find('p').each(function() {
                if ( $.trim( $(this).text() ).length == 0 )
                    $(this).remove();
            });

            var markupContentClone = $(markupContent).clone();
            $(markupContentClone).find('p,li').each(function () {
                $(this).find('.CmCaReT,#cursorPos,.renderedLabelContainer').remove();
                $(this).find('[type=1]').each(function () {
                    if ($(this).find('.renderedSampleValueContainer').length > 0) {
                        $(this).html($(this).find('.renderedSampleValueContainer').text());
                    } else {
                        $(this).html($(this).text());
                    }
                });
                $(this).find('[type=15]').each(function () {
                    if ($(this).find('.mceContentMenuValue').length > 0)
                        $(this).html($(this).find('.mceContentMenuValue').text());
                });
                $(this).find('.mceHiddenContent').each(function () {
                    $(this).html('');
                });
            });

            var content = $(markupContentClone).html();
            $(doc).find('.rewriteResultText').html( content);

            $(doc).find('.rewriteResultMarkup').hide();
            $(doc).find('.rewriteResultText').show();

            $(doc).find('#mce_plain_panel #btnPasteRewrite').click(function( ) {
                ed.undoManager.add();
            });
        }
    }

    function updateRewriteTranslateDisplay(data) {

        var doc = $(getTopFrame().document);

        if ( ed.settings.contentintel.translate.is_panel_active ) {
            var template = $(ed.settings.contentintel.content_template).clone();
            var rewriteTxt = data.translate.source.replaceAll("\n", "<br/>");

            $(template).html(data.translate.rewrite_html_with_vars);
            // Remove formatting and other non textual elements
            $(template).find('.mceStaticElement,.mceEditPointIndicator,.interactive-page-break').remove();
            $(template).find('p').each(function() {
                if ( $.trim( $(this).text() ).length == 0 )
                    $(this).remove();
            });

            if ( $(template).find('.mceDraggable').length != 0 )
                template = applyCanvasContainer(template);

            $(doc).find('.tooltip').remove();

            $(doc).find('.suggestedLanguage').html(data.translate.language);

            //  Configure result markup display
            var rewriteHTML = data.translate.rewrite_html_with_vars;
            rewriteHTML = util_restoreHTMLattributes(rewriteHTML, data.attr);
            $(doc).find('.rewriteResultMarkup').html(rewriteHTML);

            var markupContent = data.translate.rewrite_html_with_vars;
            if (markupContent.indexOf('</p>') == -1)
                markupContent = "<p>" + markupContent + "</p>";
            markupContent = "<div>" + markupContent + "</div>";
            $(markupContent).find('p').each(function() {
                if ( $.trim( $(this).text() ).length == 0 )
                    $(this).remove();
            });

            var markupContentClone = $(markupContent).clone();
            var content = $(markupContentClone).html();
            $(doc).find('.rewriteResultText').html( content);

            $(doc).find('.rewriteResultMarkup').hide();
            $(doc).find('.rewriteResultText').show();

            $(doc).find('#mce_translate_panel #btnPasteRewrite').click(function( ) {
                ed.undoManager.add();
            });
        }
    }

    function updateContentCompareDisplay(data) {
        $('#mce_contentcompare_btn').find('.fa-exclamation-circle').remove();
        if ( data.has_sim_or_dup )
            $('#mce_contentcompare_btn .mce-btn-label').append("<i style=\"position: absolute; left: 55%; top: 45%; font-size: 11px; background-color: #fff;\" class=\"fas fa-exclamation-circle\"></i>")
        if ( ed.settings.contentintel.contentcompare.is_panel_active )
            $('#mce_contentcompare_btn .fa-exclamation-circle').hide();
    }

    function updateBrandCheckDisplay(data) {

        var doc = $(getTopFrame().document);
        var statusPanel = $(ed.editorContainer);
        if ( data ) {
            let statusPanel = $(ed.editorContainer);
            if(ed.settings.connectedContentIntel){
                statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
            }
            if ( data.violations != 0 ) {
                statusPanel.find('#mce_brandcheck_btn .mce-status-ind').css({'background-color':'#fd0303'});
            } else {
                statusPanel.find('#mce_brandcheck_btn .mce-status-ind').css({'background-color':'#00d440'});
            }

            var tooltipTxt = client_messages.content_editor.possible_violations_found + ": " + data.violations;
            if ( statusPanel.find('#mce_brandcheck_btn').attr('title') != undefined ) {
                statusPanel.find('#mce_brandcheck_btn').tooltip('hide')
                    .attr('data-original-title', tooltipTxt);
            } else {
                statusPanel.find('#mce_brandcheck_btn')
                    .attr('title', tooltipTxt)
                    .attr('data-toggle', 'tooltip').tooltip();
            }
        }
    }

    function updateTranslationAccuracyDisplay(data, isForTranslatedContent) {

        var doc = $(getTopFrame().document);
        var results = data;

        var statusPanel = $(ed.editorContainer);
        if (ed.settings.connectedContentIntel) {
            statusPanel = $("#" + ed.settings.connectedContentIntel.marciePanelId);
        }
        var iconAccuracySelector ='.mcePanelAccuracy  .mce-status-ind';
        var detailsAccuracySelector ='.crtAccuracy';
        if(isForTranslatedContent){
            iconAccuracySelector = '.mceSuggestedPanelAccuracy .mce-status-ind';
            detailsAccuracySelector ='.suggestedAccuracy';
        }
        if (results && results.result == "success") {
            var tooltipTxt = client_messages.content_editor.translate_tootip;
            if (data.detected_lang_code !== data.context_lang_code) {
                // Content is not translated, don't indicate accuracy
                if(!isForTranslatedContent) {
                    statusPanel.find('#mce_translate_btn .mce-status-ind').css({'background-color': 'white'});
                }
                $(doc).find(iconAccuracySelector).css({'background-color': 'white'});
                tooltipTxt = client_messages.content_editor.translate_no_content_tooltip;
                $(doc).find(detailsAccuracySelector).hide();
            } else if (results.translation_accuracy == 't') {
                if(!isForTranslatedContent) {
                    statusPanel.find('#mce_translate_btn .mce-status-ind').css({'background-color': '#00d440'});
                }
                $(doc).find(iconAccuracySelector).css({'background-color': '#00d440'});
                tooltipTxt = client_messages.content_editor.translate_acurate_tooltip;
                $(doc).find(detailsAccuracySelector).show();
            } else if (results.translation_accuracy == 'no') {
                if (!isForTranslatedContent) {
                    statusPanel.find('#mce_translate_btn .mce-status-ind').css({'background-color': 'gray'});
                }
                $(doc).find(iconAccuracySelector).css({'background-color': 'gray'});
                var tooltipTxt = client_messages.content_editor.translate_tootip;
                $(doc).find(detailsAccuracySelector).hide();
            } else if (results.translation_accuracy == 'f') {
                if(!isForTranslatedContent) {
                    statusPanel.find('#mce_translate_btn .mce-status-ind').css({'background-color': '#fd0303'});
                }
                $(doc).find(iconAccuracySelector).css({'background-color': '#fd0303'});
                tooltipTxt =  client_messages.content_editor.translate_errors_tooltip;
                $(doc).find(detailsAccuracySelector).show();
            }
                $(doc).find(iconAccuracySelector)
                .attr('data-original-title', tooltipTxt)
                .attr('data-toggle', 'tooltip').tooltip();
            if(!isForTranslatedContent) {
                statusPanel.find('#mce_translate_btn')
                    .attr('data-original-title', tooltipTxt)
                    .attr('data-toggle', 'tooltip').tooltip();
            }
            if (ed.settings.contentintel.translate.is_panel_active) {
                var detailsId = "crtAccDetails";
                if(isForTranslatedContent){
                    detailsId = "suggAccDetails";
                }
                var details = constructTranslateAccuracyDetails(results, results.contentErrors, detailsId);
                $(detailsAccuracySelector).html(details);
                if (!isForTranslatedContent) {
                    var crtAccDetails = document.querySelector('.crtAccuracy #crtAccDetails');
                    if (crtAccDetails) {
                        crtAccDetails.addEventListener("toggle", function () {
                            setTranslationAccuracyDetailsHeight('.crtAccuracy #crtAccDetails');
                        })
                    }
                } else{
                    var sAccDetails = document.querySelector('.suggestedAccuracy #suggAccDetails');
                    if (sAccDetails) {
                        sAccDetails.addEventListener("toggle", function () {
                            setTranslationAccuracyDetailsHeight('.suggestedAccuracy #suggAccDetails');
                        })
                    }
                }
            }
        }
    }


    function constructTranslateAccuracyDetails(results, contentErrors, id) {
        if (contentErrors != null || contentErrors != undefined) {
            var errorDetails = results.reason + "<br/>" + contentErrors;
            var widthDetails = "width:420px;";
            if(localStorage.getItem("aaPanelSetting") === "fa-chevron-right") {
                widthDetails = "width:820px;";
            }
            var detailsreason = "<details id='" + id + "' style='overflow: auto; position: relative; max-height:150px;  display:block; "+ widthDetails + "' open><summary id='" + id + "_summary'>" + errorDetails + "</summary></details>";

            return detailsreason;

        } else {
            if (results.reason !== null && results.reason !== undefined ) {
                var widthDetails = "width:420px;";
                if(localStorage.getItem("aaPanelSetting") === "fa-chevron-right") {
                    widthDetails = "width:820px;";
                }
                var detailsreason = "<details id='" + id + "' style='overflow: auto; position: relative; max-height: 150px;  display:block; "+ widthDetails + "' open><summary id='" + id + "_summary'>" + results.reason + "</summary></details>";
                return detailsreason;
            } else {
                return "";
            }
        }
    }

    function applyCanvasContainer(template) {
        var cW = 0; var cH = 0;
        $(template).find('.mceDraggable').each( function() {
            if ( cW < parseInt( $(this).css('left').replace('px','') ) + parseInt( $(this).css('width').replace('px','') ) )
                cW = parseInt( $(this).css('left').replace('px','') ) + parseInt( $(this).css('width').replace('px','') );
            if ( cH < parseInt( $(this).css('top').replace('px','') ) + parseInt( $(this).css('height').replace('px','') ) )
                cH = parseInt( $(this).css('top').replace('px','') ) + parseInt( $(this).css('height').replace('px','') );
        });
        if ( cW > 0 && cH > 0 )
            $(template).html( $("<div style=\"width: "+cW+"px; height: "+cH+"px;\"/>").append($(template).html()) );
        return template;
    }

    function util_minimizeStaticElements(html) {

        var translatedEle = $(html).clone();

        // Translate all static elements to references by index
        // Will be ignored by the translation service and restored after translation
        $(translatedEle).find('[type]').each( function() {
            // If the element is not a child of another element with a type attribute
            if ( $(this).parent().closest('[type]').length == 0 ) {
                ed.settings.contentintel.translated_vars[ed.settings.contentintel.template_index] =
                    $("<div/>").append($(this).clone()).html();
                $(this).replaceWith('__VAR:' + ed.settings.contentintel.template_index + '__');
                ed.settings.contentintel.template_index++;
            }
        });

        return $('<div>').append($(translatedEle)).html();

    }

    function setTranslationAccuracyDetailsHeight(id) {
        var cAccDetails = document.querySelector(id);
        if(cAccDetails === null || cAccDetails === undefined) {
            return;
        }
        if (cAccDetails.hasAttribute("open")) {
            var summaryHeight = $(id).find('summary').height();
            if (summaryHeight < 95) {
                $($(id)[0]).css("height", summaryHeight + "px");
            } else {
                $($(id)[0]).css("height", "95px");
            }
        } else {
            $($(id)[0]).css("height", "25px");
        }
        return;
    }

    function util_restoreStaticElementsMarkup(html) {
        // Restore all static elements from references by index
        // REGEX to match __VAR:INDEX__
        html= html.replaceAll(/__VAR\s*:\s*/g, "__VAR:");
        var regex = /__VAR:(\d+)__/g;
        var match;
        while (match = regex.exec(html)) {
            var index = match[1];
            var ele = ed.settings.contentintel.translated_vars[index];
            html = html.replace('__VAR:' + index + '__', ele);
        }
        return html;
    }

    function util_restoreStaticElementsText(text) {
        // Restore all static elements from references by index
        // REGEX to match __VAR:INDEX__
        var regex = /__VAR:(\d+)__/g;
        var match;
        while (match = regex.exec(text)) {
            var index = match[1];
            var ele = ed.settings.contentintel.translated_vars[index];
            var bodyElem = new DOMParser().parseFromString(ele, 'text/html').body;

            $(bodyElem).find('[type=15]').each(function () {
                if ($(this).find('.mceContentMenuValue').length > 0) {
                    ele= ele.replace($(this)[0].innerHTML , $(this).find('.mceContentMenuValue').text());
                }
            });
            $(bodyElem).find("var[type=4]").each(function () {
                if ($(this).find('.varTagRenderedInline').length > 0) {
                    ele= ele.replace($(this)[0].innerHTML , $(this).find('.varTagRenderedInline').text());
                }
            });
            var name="";
            if ($(ele).find('.renderedSampleValueContainer').length > 0) {
                name= $(ele).find('.renderedSampleValueContainer').text();
            }
            else  if ($(ele).find('.renderedContentContainer').length > 0) {
                name = $(ele).find('.renderedContentContainer').text();
            }
            else{
                name= $(ele).text();
            }

            text = text.replace('__VAR:' + index + '__', name);
        }
        return text;
    }

    // Replace all tag attributes with an index to an array containing associated attributes
    function util_minimizeHTMLattributes(html) {

        let attributeArr = [];

        let $html = $("<div>" + html + "</div>");
        $html.find('*').each(function() {
            let $this = $(this);
            let attrs = {};
            if($this.context.tagName !== "INS"){
                $.each(this.attributes, function(index, attribute) {
                    attrs[attribute.name] = attribute.value;
                });

                let attrIndex = -1;
                for (let i = 0; i < attributeArr.length; i++) {
                    if (_.isEqual(attrs, attributeArr[i])) {
                        attrIndex = i;
                        break;
                    }
                }

                if (attrIndex === -1) {
                    attrIndex = attributeArr.length;
                    attributeArr.push(attrs);
                }

                if ( Object.keys(attrs).length > 0) {
                    $this.attr('mp_at', attrIndex);
                    $this.removeAttr(Object.keys(attrs).join(' '));
                }

            }

        });

        return { html: $html.html(), attr: attributeArr };
    }

    // Restores attributes to minimized HTML
    function util_restoreHTMLattributes(html, attributeArr) {
        let $html = $("<div>" + html + "</div>");
        $html.find('*').each(function() {
            let $this = $(this);
            let attrIndex = $this.attr('mp_at');
            if ( attrIndex && attrIndex < attributeArr.length ) {
                let attrs = attributeArr[attrIndex];
                if(attrs) {
                    $.each(attrs, function (attrName, attrValue) {
                        $this.attr(attrName, attrValue);
                    });

                    $this.removeAttr('mp_at');
                }
            }
        });
        return $html.prop('outerHTML');
    }

    function pasteRewrite(type) {
        if (!ed.selection) {
            return;
        }

        ed.undoManager.add();
        var doc = $(getTopFrame().document);
        var rewriteMarkup = $(doc).find('.rewriteResultMarkup > div').html();
        $(doc).find('#suggest_panel').hide();
        var editorSelection = ed.selection.getContent();

        var uniqueParentsSet = new Set(); // Create a new set to keep unique elements
        var selectedBlocks = $(ed.selection.getSelectedBlocks()); // Get the selected blocks
        selectedBlocks.each(function() {
            // Ignore the selected items that have an ancestor with class 'staticContentItem'
            if ($(this).closest('.staticContentItem').length === 0) {
                var parent = $(this).closest('p, li, td'); // Find the closest block element (P, LI, TD)

                // If we found a block element, add it to the set
                if (parent.length > 0) {
                    uniqueParentsSet.add(parent.get(0));
                }
            }
        });
        // Convert the set to an array and print it to console for verification
        var uniqueParentsArray = Array.from(uniqueParentsSet);
        if(type === "Translation"){
            ed.setContent(rewriteMarkup);
        } else {
            if (uniqueParentsArray.length > 1) {
                for (var i = 0; i < uniqueParentsArray.length; i++) {
                    if (i == uniqueParentsArray.length - 1) {
                        var lastParent = uniqueParentsArray[i];
                        $(lastParent).after(rewriteMarkup);
                    }
                    if ($(uniqueParentsArray[i]).is('p'))
                        $(uniqueParentsArray[i]).remove();
                    else if ($(uniqueParentsArray[i]).is('li'))
                        $(uniqueParentsArray[i]).closest('ul,ol').remove();
                    else if ($(uniqueParentsArray[i]).is('td'))
                        $(uniqueParentsArray[i]).closest('table').remove();
                }
            } else if ($.trim(editorSelection).length > 0) {

                var bodyElem = new DOMParser().parseFromString(editorSelection, 'text/html').body;
                rewriteMarkup = rewriteMarkup.replace(/<\/?p[^>]*>/g, "");
                bodyElem.innerHTML = rewriteMarkup;
                ed.selection.setContent(bodyElem.innerHTML);

            } else {
                ed.setContent(rewriteMarkup);
            }
        }
        var isRationalizerModel = doc[0].title.includes("Rationalizer");
        $.ajax({
            type: "POST",
            url: context + "/asyncContentIntelligence.form" +
                "?action=rewrite_accept" +
                "&tk=" + getParam('tk') +
                "&cacheStamp=" + (stampDate.getTime()),
            data: { content:  JSON.stringify({ markup : rewriteMarkup,  isRationalizerModel: isRationalizerModel, type :type}) },
            dataType: "json",
            success: function(data) {  }
        });
    }

    function requestRewrite(type) {

        var doc = $(getTopFrame().document);
        $(doc).find('.helpTextContainer').hide();
        $(doc).find('.helpText').hide();
        $(doc).find('#btnPasteRewrite,#rewriteMarkupToggleContainer').hide();
        $(doc).find('#suggest_panel').show();
        $(doc).find('.rewriteResultText,.rewriteResultMarkup').html('');
        $(doc).find('#rewriteContainer').show();
        $(doc).find('#rewriteContainer,#rewriteLoadingIndicator').show();

        $(doc).find('#btnSuggestRewrite').attr('disabled', 'disabled');
        $(doc).find('.mceSuggestPanelSummary').hide();
        $(doc).find('.rewriteResultContainer').hide();

        var localeId ="";
        if ( ed.settings.connectedContentIntel ) {
            localeId = $(doc).find('#targetLocaleSelect').val();
        } else if($(doc).find('#contentLanguageSelect').length) {
            localeId = $(doc).find('#contentLanguageSelect').val();
        }
        var stampDate = new Date();
        var contextParams = "";

        var editedBody = $(ed.getBody()).html();
        if (ed.settings.connectedContentIntel) {
            editedBody = ed.settings.connectedContentIntel.contentProvider();
        }
        editedBody = editedBody.replace(/<br ?\/?>/gi, "\n");

        var edContent = $("<div>" + editedBody + "</div>");

        var markupContentSelection = ed.selection != null ? ed.selection.getContent() : '';
        if ($.trim(markupContentSelection).length == 0) {
            markupContentSelection = editedBody;
        }

        if ( markupContentSelection.indexOf('</p>') === -1 )
            markupContentSelection = "<p>" + markupContentSelection + "</p>";
        markupContentSelection = "<div>" + markupContentSelection + "</div>";

        var htmlContentSelection = util_minimizeStaticElements( $(markupContentSelection).html() );
        var minimizedContentSelection = util_minimizeHTMLattributes(htmlContentSelection);
        htmlContentSelection = minimizedContentSelection.html
        var minimizedContentSelectionAttr = minimizedContentSelection.attr;

        var isFirstSuggest = true;
        var storedContentHash = localStorage.getItem("contentHash");
        var currenthash = markupContentSelection.hashCode().toString();
        if(storedContentHash !== undefined && storedContentHash === currenthash) {
            isFirstSuggest = false;
        } else {
            localStorage.removeItem("contentHash");
            localStorage.setItem("contentHash", currenthash);
        }
        var isRationalizerModel = doc[0].title.includes("Rationalizer");

        var model = $(ed.editorContainer).find('#mce_gpt_model_select').length != 0 ? $(ed.editorContainer).find('#mce_gpt_model_select').val() : "gpt4-o";
        if(ed.settings.contentintel.plain.is_panel_active) {
            model = $(ed.editorContainer).find('#mce_gpt_model_select').length != 0 ? $(ed.editorContainer).find('#mce_gpt_model_select').val() : "gpt4-o";
        }
        var markupElement = new DOMParser().parseFromString(markupContentSelection, 'text/html').body.childNodes[0];
        var contentText = markupElement.innerText;
        var verbose = ed.settings.contentintel.sentiment.is_panel_active || ed.settings.contentintel.readability.is_panel_active
            || ed.settings.contentintel.translate.is_panel_active;
        var contentParam = JSON.stringify({ markup : contentText, html: htmlContentSelection, isFirstSuggest: isFirstSuggest,  isRationalizerModel: isRationalizerModel});
        if(ed.settings.contentintel.readability.is_panel_active) {
            contentParam = JSON.stringify({ markup : contentText, html: htmlContentSelection, targetSuggestedReadability: $(doc).find('#targetSuggestedReadability option:selected')[0].value ,  isFirstSuggest: isFirstSuggest, isRationalizerModel: isRationalizerModel});
        } else if(ed.settings.contentintel.summarize.is_panel_active) {
            contentParam = JSON.stringify({ markup : contentText, html: htmlContentSelection, summarizeLimit: $(doc).find('#summarizeDesiredLength').val() ,  isFirstSuggest: isFirstSuggest,  isRationalizerModel: isRationalizerModel});
        } else if(ed.settings.contentintel.translate.is_panel_active) {
            // Default content
            var defaultLocaleId, defaultLocaleMarkup;
            if ( ed.settings.connectedContentIntel ) {
                defaultLocaleMarkup = $(ed.getBody()).html();
                defaultLocaleMarkup = defaultLocaleMarkup.replace(/<br ?\/?>/gi, "\n");
                defaultLocaleId = "1";
            } else {
                var defaultLocaleBinding = $('div[groupId='+ed.settings.currentContentGroupId+'] .defaultLocaleContent');
                defaultLocaleMarkup = $(defaultLocaleBinding).val();
                defaultLocaleId = $(defaultLocaleBinding).closest("[localeId]").attr("localeId");
            }

            defaultLocaleMarkup = "<div>" + defaultLocaleMarkup + "</div>";
            var defaultHtmlContent = util_minimizeStaticElements( $(defaultLocaleMarkup).html() );
            var minimizedDefaultContent = util_minimizeHTMLattributes(defaultHtmlContent);
            defaultHtmlContent = minimizedDefaultContent.html
            minimizedContentSelectionAttr = minimizedDefaultContent.attr;
            contentParam = JSON.stringify({ markup : contentText,  html: htmlContentSelection, targetLocaleId: localeId ,  default_html: defaultHtmlContent,  isFirstSuggest: isFirstSuggest,  isRationalizerModel: isRationalizerModel, sourceLocaleId: defaultLocaleId});
        }
        if ( ed.settings.rationalizer_application_id != undefined )
            contextParams += "&rationalizerApplicationId=" + ed.settings.rationalizer_application_id;
        if (ed.settings.contentintel.translated_vars === undefined){
            ed.settings.contentintel.translated_vars = [];
        }
        $.ajax({
            type: "POST",
            url: context + "/asyncContentIntelligence.form" +
                "?action=rewrite_" + type +
                "&verbose=" + verbose +
                contextParams +
                "&model=" + model +
                "&tk=" + getParam('tk') +
                "&cacheStamp=" + (stampDate.getTime()),
            data: { content: contentParam , variables:JSON.stringify({"variables_array":  ed.settings.contentintel.translated_vars})},
            dataType: "json",
            error: function(xhr, status, error) {
                $(doc).find('#btnSuggestRewrite').removeAttr('disabled');
                console.log("AJAX rewrite request failed " + error);
                $(doc).find('#btnSuggestRewrite').removeAttr('disabled');
                $(doc).find('#btnPasteRewrite,#rewriteMarkupToggleContainer').hide();
                $(doc).find('#rewriteLoadingIndicator').hide();
                $(doc).find('.rewriteResultContainer').html( "<div style=\"color: black; font-size: 14px;\">The request failed. Please try again.</div>" );
                $(doc).find('.rewriteResultContainer').show();
                return;
            },
            success: function(data) {

                $(doc).find('#btnSuggestRewrite').removeAttr('disabled');
                if (data.error == true) {
                    console.log('rewrite request result ' + data);
                    console.log("error " + data.message );
                    $(doc).find('#btnPasteRewrite,#rewriteMarkupToggleContainer').hide();
                    $(doc).find('#rewriteLoadingIndicator').hide();
                    $(doc).find('.rewriteResultContainer').html( "<div style=\"color: black; font-size: 14px;\">" + data.message + "</div>" );
                    $(doc).find('.rewriteResultContainer').show();
                    return;

                } else {
                    if ( data.result == "success") {
                        var storedContentHash = localStorage.getItem("contentHash");
                        if(storedContentHash !== null && storedContentHash !== undefined) {
                            $(doc).find('#suggest_panel').show();
                            $(doc).find('#rewriteContainer').show();
                            $(doc).find('#rewriteLoadingIndicator').hide();
                            $(doc).find('.rewriteResultContainer').html("<div class=\"rewriteResultText\" style=\"display: none;\"></div><div class=\"rewriteResultMarkup\" style=\"display: none;\"></div>");
                            $(doc).find('.mceSuggestPanelSummary').show();
                            $(doc).find('.rewriteResultContainer')
                                .css({maxHeight: 250 + 'px', height: 200  + 'px'});
                            $(doc).find('.rewriteResultContainer').show();
                            var responseHtml = new DOMParser().parseFromString(data.rewrite_html, 'text/html').body;
                            var responseText = $(responseHtml).text();
                            if(responseText.trim() === "No suggestions.") {
                                $(doc).find('#btnPasteRewrite').hide();
                                $(doc).find('.mceSuggestPanelSummary').hide();
                            }

                            data.attr = minimizedContentSelectionAttr;
                            $(doc).find('.helpTextContainer').hide();
                            if(type === 'readability') {
                                $(doc).find('.btnPasteRewriteReadability').show();
                                updateRewriteReadabilityDisplay(data);
                            } else if(type === 'sentiment') {
                                $(doc).find('.btnPasteRewriteSentiment').show();
                                updateRewriteSentimentDisplay(data);
                            } else if(type === 'summarize') {
                                $(doc).find('.btnPasteRewriteSummarize').show();
                                updateRewriteSummarizeDisplay(data);
                            } else if(type === 'plain') {
                                $(doc).find('.btnPasteRewritePlain').show();
                                updateRewritePlainDisplay(data);
                            } else if(type === 'translate') {
                                var currentLangSel = $(doc).find('#contentLanguageSelect').val();
                                if(ed.settings.connectedContentIntel || data.translate.localeId === currentLangSel) {
                                    $(doc).find('.btnPasteRewriteTranslate').show();
                                    updateRewriteTranslateDisplay(data);
                                    getTranslationAccuracy(true);
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    function toggleGutterDisplay() {

        var isLangApplicable = ed.settings.mp_fn.get_current_locale_id() >= 0;
        let statusPanel = $(ed.editorContainer);
        if(ed.settings.connectedContentIntel){
            statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
        }
        if ( ed.settings.contentintel.readability_enabled && isLangApplicable ) {
            statusPanel.find('#mce_readability_btn').css({'display':'inline-block'});
        } else {
            statusPanel.find('#mce_readability_btn').css({'display':'none'});
        }

        if ( ed.settings.contentintel.sentiment_enabled && isLangApplicable ) {
            statusPanel.find('#mce_sentiment_btn').css({'display':'inline-block'});
        } else {
            statusPanel.find('#mce_sentiment_btn').css({'display':'none'});
        }

        if ( ed.settings.contentintel.contentcompare_enabled ) {
            statusPanel.find('#mce_contentcompare_btn').css({'display':'inline-block'});
        } else {
            statusPanel.find('#mce_contentcompare_btn').css({'display':'none'});
        }

        if ( ed.settings.contentintel.brandcheck_enabled ) {
            statusPanel.find('#mce_brandcheck_btn').css({'display':'inline-block'});
        } else {
            statusPanel.find('#mce_brandcheck_btn').css({'display':'none'});
        }

        if ( ed.settings.contentintel.summarize_enabled && isLangApplicable) {
            statusPanel.find('#mce_summarize_btn').css({'display':'inline-block'});
        } else {
            statusPanel.find('#mce_summarize_btn').css({'display':'none'});
        }
        if ( ed.settings.contentintel.plain_enabled && isLangApplicable) {
            statusPanel.find('#mce_plain_btn').css({'display':'inline-block'});
        } else {
            statusPanel.find('#mce_plain_btn').css({'display':'none'});
        }
        var isMessagepoint = $($(getTopFrame().document)).find('#contentLanguageSelect')[0] !== undefined;
        var isMessagepointWithMultipleLanguages = isMessagepoint ? $($(getTopFrame().document)).find('#contentLanguageSelect')[0].length > 1 : false;
        var isDefaultLanguageSelected = isMessagepoint ? $($($(getTopFrame().document)).find('#contentLanguageSelect')[0].selectedOptions[0]).text().indexOf("Default")!=-1  : false;
        if ( ed.settings.contentintel.translate_enabled && isLangApplicable && ed.settings.rationalizer_application_id === undefined && (isMessagepointWithMultipleLanguages || !isDefaultLanguageSelected)) {
            statusPanel.find('#mce_translate_btn').css({'display':'inline-block'});
        } else {
            statusPanel.find('#mce_translate_btn').css({'display':'none'});
        }

        if ( ((ed.settings.contentintel.translate_enabled || ed.settings.contentintel.plain_enabled || ed.settings.contentintel.readability_enabled || ed.settings.contentintel.sentiment_enabled || ed.settings.contentintel.summarize_enabled) && isLangApplicable) ||
            ed.settings.contentintel.contentcompare_enabled || ed.settings.contentintel.brandcheck_enabled ) {
            statusPanel.find('.mce-path').parent().css({'float':'right', 'display':'inline-block'});
        } else {
            statusPanel.find('.mce-path').parent().css({'float':'initial', 'display':'initial'});
        }
    }

    function toggleReadabilityPanel() {

        function initReadabilityPanel() {
            var panelHTML =
                $("<div id=\"mce_readability_panel\" class=\"mce-custompanel\" style=\"z-index: 1000; width: 450px; position: fixed; top: 0px; right: 0px; height: 100%; border-left: 1px solid #979797; background-color: #fff;\">" +
                    "<div id=\"crt_readability_panel\">" +
                    "<div class=\"mcePanelHeader\" style=\"background-color: #666; height: 56px; vertical-align: middle; position: relative; color: #fff;\">" +
                    "<div class=\"panelExpandToggle fa-mp-container\" data-toggle=\"tooltip\" title=\""+client_messages.content_editor.toggle_panel_size+"\" style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 18px 0px 24px;\">" +
                    "<i class=\"far fa-chevron-left\" ></i>" +
                    "</div>" +
                    "<div style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 24px 0px 0px;\">" +
                    client_messages.content_editor.readability_analysis +
                    "</div>" +
                    "<div class=\"mceCloseCustomPanelBtn\" style=\"position: absolute; right: 24px; top: 12px; font-size: 18px; cursor: pointer; width: 12px;\">" +
                    "<i class=\"far fa-times\" ></i>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelSummary\" style=\"background-color: #f5f5f5; padding: 4px 24px; color: #171717; font-size: 13px; white-space: nowrap;\">" +
                    "<div class=\"mce-status-ind\" style=\"display: inline-block; vertical-align: top; border: 2px solid #e1e1e1; box-sizing: border-box; border-radius: 6px; width: 12px; height: 12px; background-color: white; position: relative; top: 5px;\"></div>" +
                    "<div style=\"display: inline-block; vertical-align: top\">" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; display: none;\">" + client_messages.content_editor.flesch_readability + ": <span title=\"TEMP\" data-toggle=\"tooltip\" class=\"fleschReadabilityScore\"></span></div>" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; white-space: normal;\">" + client_messages.content_editor.flesch_kincaid_grade_level + ": " + "  <span class=\"fleschKincaidScore\"></span></div>" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100;\">" + client_messages.content_editor.desired_range + ":  <span title=\"TEMP\" data-toggle=\"tooltip\" class=\"targetReadabilityRange\"></span></div>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelBody\" style=\"padding: 4px 0px;\">" +
                    "<div class=\"mceReadabilityIndex\" style=\"padding: 4px 24px; font-size: 11px;\">" +
                    "<div style=\"background-color: #e4fdc8 ;width: 10px; height: 10px; border-bottom: 1px solid #e1e1e1;  border-left: 1px solid #e1e1e1;  border-right: 2px solid #e1e1e1;  border-top: 2px solid #e1e1e1; box-sizing: border-box; display: inline-block;\"></div>" +
                    "<div style=\"display: inline-block; padding: 0px 24px 0px 8px;\">" + client_messages.content_editor.good + "</div>" +
                    "<div style=\"background-color: #ffe7e7 ;width: 10px; height: 10px; border-bottom: 1px solid #e1e1e1;  border-left: 1px solid #e1e1e1;  border-right: 2px solid #e1e1e1;  border-top: 2px solid #e1e1e1; box-sizing: border-box; display: inline-block;\"></div>" +
                    "<div style=\"display: inline-block; padding: 0px 24px 0px 8px;\">" + client_messages.content_editor.poor + "</div>" +
                    "</div>" +
                    "<div class=\"mceReadabilityContent\" style=\"padding: 8px 24px; line-height: 1.5; overflow: auto; position: relative;\">" +
                    "<div style=\"text-align: center; margin-top: 48px;\">" +
                    "<div class=\"progress-loader progress-loader-lg\">" +
                    "<i class=\"progress-loader-icon far fa-spinner-third\"></i>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div id=\"suggest_buttons_panel\" >" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div id=\"suggest_panel\" style=\"display:none;\" class=\"showIfLicenced\">" +
                    "<div class=\"mceSuggestPanelHeader\" style=\"background-color: #666; height: 56px; vertical-align: middle; position: relative; color: #fff;\">" +
                    "<div class=\"panelExpandToggle fa-mp-container\" data-toggle=\"tooltip\" title=\""+client_messages.content_editor.toggle_panel_size+"\" style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 18px 0px 24px;\">" +
                    "<i class=\"far fa-chevron-left\" ></i>" +
                    "</div>" +
                    "<div style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 24px 0px 0px;\">" +
                    client_messages.content_editor.readability_suggest +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mceSuggestPanelSummary\" style=\"background-color: #f5f5f5; padding: 4px 24px; color: #171717; font-size: 13px; white-space: nowrap;\">" +
                    "<div class=\"mce-suggest-status-ind\" style=\"display: inline-block; vertical-align: top; border: 2px solid #e1e1e1; box-sizing: border-box; border-radius: 6px; width: 12px; height: 12px; background-color: white; position: relative; top: 5px;\"></div>" +
                    "<div style=\"display: inline-block; vertical-align: top\">" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; display: none;\">" + client_messages.content_editor.flesch_readability + ": <span title=\"TEMP\" data-toggle=\"tooltip\" class=\"suggestedFleschReadabilityScore\"></span></div>" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; white-space: normal;\">" + client_messages.content_editor.flesch_kincaid_grade_level + ": " + "  <span class=\"suggestedFleschKincaidScore\"></span></div>" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100;\">" + client_messages.content_editor.desired_range + ":  <span title=\"TEMP\" data-toggle=\"tooltip\" class=\"targetSuggestedReadabilityRange\"></span></div>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelSuggestedBody\" style=\"padding: 4px 0px;\">" +
                    "<div class=\"mceSuggestedReadabilityIndex\" style=\"padding: 4px 24px; font-size: 11px; display: none;\">" +
                    "<div style=\"background-color: #e4fdc8 ;width: 10px; height: 10px; border-bottom: 1px solid #e1e1e1;  border-left: 1px solid #e1e1e1;  border-right: 2px solid #e1e1e1;  border-top: 2px solid #e1e1e1; box-sizing: border-box; display: inline-block;\"></div>" +
                    "<div style=\"display: inline-block; padding: 0px 24px 0px 8px;\">" + client_messages.content_editor.good + "</div>" +
                    "<div style=\"background-color: #ffe7e7 ;width: 10px; height: 10px; border-bottom: 1px solid #e1e1e1;  border-left: 1px solid #e1e1e1;  border-right: 2px solid #e1e1e1;  border-top: 2px solid #e1e1e1; box-sizing: border-box; display: inline-block;\"></div>" +
                    "<div style=\"display: inline-block; padding: 0px 24px 0px 8px;\">" + client_messages.content_editor.poor + "</div>" +
                    "</div>" +
                    "<div class=\"mceReadabilitySugestedContent\" style=\"padding: 8px 24px; line-height: 1.5; overflow: auto; position: relative;\">" +
                    "<div id=\"rewriteContainer\" class=\"rewriteContainer\" style=\"display: none; margin-top: 2px;\">" +
                    "<div id=\"rewriteLoadingIndicator\" class=\"loading-indicator\" style=\"margin-top: 42px; text-align: center;\">" +
                    "<i class=\"far fa-shake fa-pen\" style=\"--fa-animation-duration: 15s; font-size: 20px;\"></i>" +
                    "<p style=\"font-size: 14px; margin-top: 8px;\">" + client_messages.text.writing_in_progress + "...</p>" +
                    "</div>" +
                    "<div id=\"rewriteResultContainer\" class=\"rewriteResultContainer\">" +
                    "   <div class=\"rewriteResultText\" style=\"display: none;\">" +
                    "   </div>" +
                    "   <div class=\"rewriteResultMarkup\" style=\"display: none;\">" +
                    "   </div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>");

            $(panelHTML).find('.mceCloseCustomPanelBtn').click(function( ) {
                toggleReadabilityPanel();
            });

            $(getTopFrame().document).find('body').append( panelHTML );

            $(panelHTML).find('.panelExpandToggle').click( function() {
                togglePanelExpand(this);
            });

            $(panelHTML).find('[data-toggle=tooltip]').tooltip();

            var frameHeight = $(getTopFrame().document).find('#page').height();
            $(getTopFrame().document).find('#mce_readability_panel')
                .css({maxHeight: frameHeight + 'px'});

            var frameHeight = $(getTopFrame().document).find('#mce_readability_panel').outerHeight() - 250;
            $(getTopFrame().document).find('#mce_readability_panel .mceReadabilityContent')
                .css({maxHeight: frameHeight + 'px', height: 180  + 'px'});

            setAAPanel($(getTopFrame().document).find('#mce_readability_panel'));
        }

        ed.settings.contentintel.readability.is_panel_active = !ed.settings.contentintel.readability.is_panel_active;
        let statusPanel = $(ed.editorContainer);
        if(ed.settings.connectedContentIntel){
            statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
        }
        if ( ed.settings.contentintel.readability.is_panel_active ) {
            var hasContent = updateContentInfo();
            if ( hasContent) {
                statusPanel.find('#mce_readability_btn').css({'background-color': '#636161'})
                    .find('.mce-btn-label').css({'color': '#ffffff'});
                initReadabilityPanel();
            }
        } else {
            statusPanel.find('#mce_readability_btn').css({'background-color': 'inherit'})
                .find('.mce-btn-label').css({'color': 'inherit'});
            $(getTopFrame().document).find('#mce_readability_panel').remove();
        }

        shiftPrimaryDisplay();
    }

    function toggleSentimentPanel() {

        function initSentimentPanel() {
            var panelHTML =
                $("<div id=\"mce_sentiment_panel\" class=\"mce-custompanel\" style=\"z-index: 1000; width: 450px; position: fixed; top: 0px; right: 0px; height: 100%; border-left: 1px solid #979797; background-color: #fff;\">" +
                    "<div id=\"crt_sentiment_panel\">" +
                    "<div class=\"mcePanelHeader\" style=\"background-color: #666; height: 56px; vertical-align: middle; position: relative; color: #fff;\">" +
                    "<div class=\"panelExpandToggle fa-mp-container\" data-toggle=\"tooltip\" title=\""+client_messages.content_editor.toggle_panel_size+"\" style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 18px 0px 24px;\">" +
                    "<i class=\"far fa-chevron-left\" ></i>" +
                    "</div>" +
                    "<div style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 24px 0px 0px;\">" +
                    client_messages.content_editor.sentiment_analysis +
                    "</div>" +
                    "<div class=\"mceCloseCustomPanelBtn\" style=\"position: absolute; right: 24px; top: 12px; font-size: 18px; cursor: pointer; width: 12px;\">" +
                    "<i class=\"far fa-times\" ></i>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelSummary\" style=\"background-color: #f5f5f5; padding: 16px 24px; color: #171717; font-size: 13px;\">" +
                    "<div class=\"mce-status-ind\" style=\"display: inline-block; vertical-align: middle; border: 2px solid #e1e1e1; box-sizing: border-box; border-radius: 6px; width: 12px; height: 12px; background-color: white; position: relative; top: -1px;\"></div>" +
                    "<div style=\"display: inline-block; vertical-align: middle; padding: 0px 8px; font-weight: 100;\">" + client_messages.content_editor.sentiment_score + ":</div>" +
                    "<div class=\"mceSentimentScore\" style=\"display: inline-block; vertical-align: middle; padding: 0px; font-weight: 100;\"></div>" +
                    "<div class=\"mceSentimentLiteral\" style=\"vertical-align: middle; padding: 0px 4px 0px 20px; font-size: 10px;\"></div>"  +
                    "</div>" +
                    "<div class=\"mcePanelBody\" style=\"padding: 8px 0px;\">" +
                    "<div class=\"mceSentimentIndex\" style=\"padding: 16px 24px; font-size: 11px;\">" +
                    "<div style=\"background-color: #e4fdc8 ;width: 11px; height: 11px; border-bottom: 1px solid #e1e1e1;  border-left: 1px solid #e1e1e1;  border-right: 2px solid #e1e1e1;  border-top: 2px solid #e1e1e1; box-sizing: border-box; display: inline-block;\"></div>" +
                    "<div style=\"display: inline-block; padding: 0px 24px 0px 8px;\">" + client_messages.content_editor.positive + "</div>" +
                    "<div style=\"background-color: #ffe7e7 ;width: 11px; height: 11px; border-bottom: 1px solid #e1e1e1;  border-left: 1px solid #e1e1e1;  border-right: 2px solid #e1e1e1;  border-top: 2px solid #e1e1e1; box-sizing: border-box; display: inline-block;\"></div>" +
                    "<div style=\"display: inline-block; padding: 0px 24px 0px 8px;\">" + client_messages.content_editor.negative + "</div>" +
                    "<div style=\"background-color: #fff ;width: 11px; height: 11px; border-bottom: 1px solid #e1e1e1;  border-left: 1px solid #e1e1e1;  border-right: 2px solid #e1e1e1;  border-top: 2px solid #e1e1e1; box-sizing: border-box; display: inline-block;\"></div>" +
                    "<div style=\"display: inline-block; padding: 0px 24px 0px 8px;\">" + client_messages.content_editor.neutral + "</div>" +
                    "</div>" +
                    "<div class=\"mceSentimentContent\" style=\"padding: 8px 24px; line-height: 1.5; overflow: auto; position: relative;\">" +
                    "<div style=\"text-align: center; margin-top: 48px;\">" +
                    "<div class=\"progress-loader progress-loader-lg\">" +
                    "<i class=\"progress-loader-icon far fa-spinner-third\"></i>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div id=\"suggest_buttons_panel\" >" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div id=\"suggest_panel\" style=\"display:none;\" class=\"showIfLicenced\">" +
                    "<div class=\"mceSuggestPanelHeader\" style=\"background-color: #666; height: 56px; vertical-align: middle; position: relative; color: #fff;\">" +
                    "<div class=\"panelExpandToggle fa-mp-container\" data-toggle=\"tooltip\" title=\""+client_messages.content_editor.toggle_panel_size+"\" style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 18px 0px 24px;\">" +
                    "<i class=\"far fa-chevron-left\" ></i>" +
                    "</div>" +
                    "<div style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 24px 0px 0px;\">" +
                    client_messages.content_editor.sentiment_suggest +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mceSuggestPanelSummary\" style=\"background-color: #f5f5f5; padding: 16px 24px; color: #171717; font-size: 13px; white-space: nowrap;\">" +
                    "<div class=\"mce-suggest-status-ind\" style=\"display: inline-block; vertical-align: middle; border: 2px solid #e1e1e1; box-sizing: border-box; border-radius: 6px; width: 12px; height: 12px; background-color: white; position: relative; top: -1px;\"></div>" +
                    "<div style=\"display: inline-block; vertical-align: middle; padding: 0px 8px; font-weight: 100;\">" + client_messages.content_editor.sentiment_score + ":</div>" +
                    "<div class=\"mceSuggestedSentimentScore\" style=\"display: inline-block; vertical-align: middle; padding: 0px; font-weight: 100;\"></div>" +
                    "<div class=\"mceSuggestedSentimentLiteral\" style=\"vertical-align: middle; padding: 0px 4px 0px 20px; font-size: 10px;\"></div>"  +
                    "</div>" +
                    "<div class=\"mcePanelSuggestedBody\" style=\"padding: 8px 0px;\">" +
                    "<div class=\"mceSuggestedSentimentIndex\" style=\"padding: 16px 24px; font-size: 11px;\">" +
                    "<div style=\"background-color: #e4fdc8 ;width: 11px; height: 11px; border-bottom: 1px solid #e1e1e1;  border-left: 1px solid #e1e1e1;  border-right: 2px solid #e1e1e1;  border-top: 2px solid #e1e1e1; box-sizing: border-box; display: inline-block;\"></div>" +
                    "<div style=\"display: inline-block; padding: 0px 24px 0px 8px;\">" + client_messages.content_editor.positive + "</div>" +
                    "<div style=\"background-color: #ffe7e7 ;width: 11px; height: 11px; border-bottom: 1px solid #e1e1e1;  border-left: 1px solid #e1e1e1;  border-right: 2px solid #e1e1e1;  border-top: 2px solid #e1e1e1; box-sizing: border-box; display: inline-block;\"></div>" +
                    "<div style=\"display: inline-block; padding: 0px 24px 0px 8px;\">" + client_messages.content_editor.negative + "</div>" +
                    "<div style=\"background-color: #fff ;width: 11px; height: 11px; border-bottom: 1px solid #e1e1e1;  border-left: 1px solid #e1e1e1;  border-right: 2px solid #e1e1e1;  border-top: 2px solid #e1e1e1; box-sizing: border-box; display: inline-block;\"></div>" +
                    "<div style=\"display: inline-block; padding: 0px 24px 0px 8px;\">" + client_messages.content_editor.neutral + "</div>" +
                    "</div>" +
                    "<div class=\"mceSentimentSugestedContent\" style=\"padding: 8px 24px; line-height: 1.5; overflow: auto; position: relative;\">" +
                    "<div id=\"rewriteContainer\" class=\"rewriteContainer\" style=\"display: none; margin-top: 2px;\">" +
                    "<div id=\"rewriteLoadingIndicator\" class=\"loading-indicator\" style=\"margin-top: 42px; text-align: center;\">" +
                    "<i class=\"far fa-shake fa-pen\" style=\"--fa-animation-duration: 15s; font-size: 20px;\"></i>" +
                    "<p style=\"font-size: 14px; margin-top: 8px;\">" + client_messages.text.writing_in_progress + "...</p>" +
                    "</div>" +
                    "<div class=\"rewriteResultContainer\" style=\"padding: 8px 24px; line-height: 1.5; overflow: auto; position: relative; \">" +
                    "   <div class=\"rewriteResultText\" style=\" display: none;\">" +
                    "   </div>" +
                    "   <div class=\"rewriteResultMarkup\" style=\"display: none;\">" +
                    "   </div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>");

            $(panelHTML).find('.mceCloseCustomPanelBtn').click(function( ) {
                toggleSentimentPanel();
            });

            $(panelHTML).find('.panelExpandToggle').click( function() {
                togglePanelExpand(this);
            });

            $(getTopFrame().document).find('body').append( panelHTML );
            $(panelHTML).find('[data-toggle=tooltip]').tooltip();

            var frameHeight = $(getTopFrame().document).find('#page').height();
            $(getTopFrame().document).find('#mce_sentiment_panel')
                .css({maxHeight: frameHeight + 'px'});

            var frameHeight = $(getTopFrame().document).find('#mce_sentiment_panel').outerHeight() - 225;
            $(getTopFrame().document).find('#mce_sentiment_panel .mceSentimentContent')
                .css({maxHeight: frameHeight + 'px', height: 180 + 'px'});

            setAAPanel($(getTopFrame().document).find('#mce_sentiment_panel'));
        }

        ed.settings.contentintel.sentiment.is_panel_active = !ed.settings.contentintel.sentiment.is_panel_active;
        let statusPanel = $(ed.editorContainer);
        if(ed.settings.connectedContentIntel){
            statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
        }
        if ( ed.settings.contentintel.sentiment.is_panel_active ) {
            var hasContent = updateContentInfo();
            if ( hasContent ) {
                statusPanel.find('#mce_sentiment_btn').css({'background-color': '#636161'})
                    .find('.mce-btn-label').css({'color': '#ffffff'});
                initSentimentPanel();
            }
        } else {
            statusPanel.find('#mce_sentiment_btn').css({'background-color': 'inherit'})
                .find('.mce-btn-label').css({'color': 'inherit'});
            $(getTopFrame().document).find('#mce_sentiment_panel').remove();
        }

        shiftPrimaryDisplay();
    }

    function toggleSummarizePanel() {
        function initSummarizePanel() {
            var panelHTML =
                $("<div id=\"mce_summarize_panel\" class=\"mce-custompanel\" style=\"z-index: 1000; width: 450px; position: fixed; top: 0px; right: 0px; height: 100%; border-left: 1px solid #979797; background-color: #fff;\">" +
                    "<div id=\"crt_summarize_panel\">" +
                    "<div class=\"mcePanelHeader\" style=\"background-color: #666; height: 56px; vertical-align: middle; position: relative; color: #fff;\">" +
                    "<div class=\"panelExpandToggle fa-mp-container\" data-toggle=\"tooltip\" title=\""+client_messages.content_editor.toggle_panel_size+"\" style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 18px 0px 24px;\">" +
                    "<i class=\"far fa-chevron-left\" ></i>" +
                    "</div>" +
                    "<div style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 24px 0px 0px;\">" +
                    client_messages.content_editor.summarize_analysis +
                    "</div>" +
                    "<div class=\"mceCloseCustomPanelBtn\" style=\"position: absolute; right: 24px; top: 12px; font-size: 18px; cursor: pointer; width: 12px;\">" +
                    "<i class=\"far fa-times\" ></i>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelSummary\" style=\"background-color: #f5f5f5; padding: 16px 24px; color: #171717; font-size: 13px; white-space: nowrap;\">" +
                    "<div style=\"display: inline-block; vertical-align: top\">" +
                    "<div style=\"display:flex;\">" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; \">" +client_messages.content_editor.sentences + ": <span  class=\"crtSentencesNo\"></span></div>" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; \">" +client_messages.content_editor.words + ": <span  class=\"crtWordsNo\"></span></div>" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; \">" +client_messages.content_editor.characters + ": <span  class=\"crtCharactersNo\"></span></div>" +
                    "</div>" +
                    "<div style=\"display:flex;\">" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; \">" +client_messages.content_editor.averageReadTime + ": <span  class=\"crtAverageReadTime\"></span></div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelBody\" style=\"padding: 8px 0px;\">" +
                    "<div class=\"mceSummarizeContent\" style=\"padding: 8px 24px; line-height: 1.5; overflow: auto; position: relative;\">" +
                    "<div style=\"text-align: center; margin-top: 48px;\">" +
                    "<div class=\"progress-loader progress-loader-lg\">" +
                    "<i class=\"progress-loader-icon far fa-spinner-third\"></i>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div id=\"suggest_buttons_panel\" >" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div id=\"suggest_panel\" style=\"display:none;\" class=\"showIfLicenced\">" +
                    "<div class=\"mceSuggestPanelHeader\" style=\"background-color: #666; height: 56px; vertical-align: middle; position: relative; color: #fff;\">" +
                    "<div class=\"panelExpandToggle fa-mp-container\" data-toggle=\"tooltip\" title=\""+client_messages.content_editor.toggle_panel_size+"\" style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 18px 0px 24px;\">" +
                    "<i class=\"far fa-chevron-left\" ></i>" +
                    "</div>" +
                    "<div style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 24px 0px 0px;\">" +
                    client_messages.content_editor.summarize_suggest +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mceSuggestPanelSummary\" style=\"background-color: #f5f5f5; padding: 16px 24px; color: #171717; font-size: 13px; white-space: nowrap;\">" +
                    "<div style=\"display: inline-block; vertical-align: top\">" +
                    "<div style=\"display:flex;\">" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100;\">" +client_messages.content_editor.sentences + ": <span  class=\"suggestedSentencesNo\"></span></div>" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100;\">" +client_messages.content_editor.words + ": <span  class=\"suggestedWordsNo\"></span></div>" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100;\">" +client_messages.content_editor.characters + ": <span  class=\"suggestedCharactersNo\"></span></div>" +
                    "</div>" +
                    "<div style=\"display:flex;\">" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100;\">" +client_messages.content_editor.averageReadTime + ": <span  class=\"suggestedAverageReadTime\"></span></div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelSuggestedBody\" style=\"padding: 8px 0px;\">" +
                    "<div class=\"mceSummarizeSugestedContent\" style=\"padding: 8px 24px; line-height: 1.5; overflow: auto; position: relative;\">" +
                    "<div id=\"rewriteContainer\" class=\"rewriteContainer\" style=\"display: none; margin-top: 2px;\">" +
                    "<div id=\"rewriteLoadingIndicator\" class=\"loading-indicator\" style=\"margin-top: 42px; text-align: center;\">" +
                    "<i class=\"far fa-shake fa-pen\" style=\"--fa-animation-duration: 15s; font-size: 20px;\"></i>" +
                    "<p style=\"font-size: 14px; margin-top: 8px;\">" + client_messages.text.writing_in_progress + "...</p>" +
                    "</div>" +
                    "<div id=\"rewriteResultContainer\" class=\"rewriteResultContainer\">" +
                    "   <div class=\"rewriteResultText\" style=\"display: none;\">" +
                    "   </div>" +
                    "   <div class=\"rewriteResultMarkup\" style=\"display: none;\">" +
                    "   </div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>");

            $(panelHTML).find('.mceCloseCustomPanelBtn').click(function( ) {
                toggleSummarizePanel();
            });

            $(getTopFrame().document).find('body').append( panelHTML );

            $(panelHTML).find('.panelExpandToggle').click( function() {
                togglePanelExpand(this);
            });

            $(panelHTML).find('[data-toggle=tooltip]').tooltip();

            var frameHeight = $(getTopFrame().document).find('#page').height();
            $(getTopFrame().document).find('#mce_summarize_panel')
                .css({maxHeight: frameHeight + 'px'});

            var frameHeight = $(getTopFrame().document).find('#mce_summarize_panel').outerHeight() - 250;
            $(getTopFrame().document).find('#mce_summarize_panel .mceSummarizeContent')
                .css({maxHeight: frameHeight + 'px', height: 180  + 'px'});

            setAAPanel($(getTopFrame().document).find('#mce_summarize_panel'));
        }

        ed.settings.contentintel.summarize.is_panel_active = !ed.settings.contentintel.summarize.is_panel_active;
        let statusPanel = $(ed.editorContainer);
        if(ed.settings.connectedContentIntel){
            statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
        }
        if ( ed.settings.contentintel.summarize.is_panel_active ) {
            var hasContent = updateContentInfo();
            if ( hasContent) {
                statusPanel.find('#mce_summarize_btn').css({'background-color': '#636161'})
                statusPanel.find('#summarize_btn_icon').css({'color': '#ffffff'});
                initSummarizePanel();
            }
        } else {
            statusPanel.find('#mce_summarize_btn').css({'background-color': 'inherit'})
            statusPanel.find('#summarize_btn_icon').css({'color': 'black'});
            $(getTopFrame().document).find('#summarize_limit_error_popup').hide();
            $(getTopFrame().document).find('#mce_summarize_panel').remove();
        }

        shiftPrimaryDisplay();
    }

    function togglePlainLanguagePanel() {
        function initPlainPanel() {
            var panelHTML =
                $("<div id=\"mce_plain_panel\" class=\"mce-custompanel\" style=\"z-index: 1000; width: 450px; position: fixed; top: 0px; right: 0px; height: 100%; border-left: 1px solid #979797; background-color: #fff;\">" +
                    "<div id=\"crt_plain_panel\">" +
                    "<div class=\"mcePanelHeader\" style=\"background-color: #666; height: 56px; vertical-align: middle; position: relative; color: #fff;\">" +
                    "<div class=\"panelExpandToggle fa-mp-container\" data-toggle=\"tooltip\" title=\""+client_messages.content_editor.toggle_panel_size+"\" style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 18px 0px 24px;\">" +
                    "<i class=\"far fa-chevron-left\" ></i>" +
                    "</div>" +
                    "<div style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 24px 0px 0px;\">" +
                    client_messages.content_editor.pain_analysis +
                    "</div>" +
                    "<div class=\"mceCloseCustomPanelBtn\" style=\"position: absolute; right: 24px; top: 12px; font-size: 18px; cursor: pointer; width: 12px;\">" +
                    "<i class=\"far fa-times\" ></i>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelPlain\" style=\"background-color: #f5f5f5; padding: 16px 24px; color: #171717; font-size: 13px; white-space: nowrap;\">" +
                    "<div style=\"display: inline-block; vertical-align: top\">" +
                    "<div style=\"display:flex;\">" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; \">" +client_messages.content_editor.sentences + ": <span  class=\"crtSentencesNo\"></span></div>" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; \">" +client_messages.content_editor.words + ": <span  class=\"crtWordsNo\"></span></div>" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; \">" +client_messages.content_editor.characters + ": <span  class=\"crtCharactersNo\"></span></div>" +
                    "</div>" +
                    "<div style=\"display:flex;\">" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; \">" +client_messages.content_editor.averageReadTime + ": <span  class=\"crtAverageReadTime\"></span></div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelBody\" style=\"padding: 8px 0px;\">" +
                    "<div class=\"mcePlainContent\" style=\"padding: 8px 24px; line-height: 1.5; overflow: auto; position: relative;\">" +
                    "<div style=\"text-align: center; margin-top: 48px;\">" +
                    "<div class=\"progress-loader progress-loader-lg\">" +
                    "<i class=\"progress-loader-icon far fa-spinner-third\"></i>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div id=\"suggest_buttons_panel\" >" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div id=\"suggest_panel\" style=\"display:none;\" class=\"showIfLicenced\">" +
                    "<div class=\"mceSuggestPanelHeader\" style=\"background-color: #666; height: 56px; vertical-align: middle; position: relative; color: #fff;\">" +
                    "<div class=\"panelExpandToggle fa-mp-container\" data-toggle=\"tooltip\" title=\""+client_messages.content_editor.toggle_panel_size+"\" style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 18px 0px 24px;\">" +
                    "<i class=\"far fa-chevron-left\" ></i>" +
                    "</div>" +
                    "<div style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 24px 0px 0px;\">" +
                    client_messages.content_editor.plain_suggest +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mceSuggestPanelSummary\" style=\"background-color: #f5f5f5; padding: 16px 24px; color: #171717; font-size: 13px; white-space: nowrap;\">" +
                    "<div style=\"display: inline-block; vertical-align: top\">" +
                    "<div style=\"display:flex;\">" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100;\">" +client_messages.content_editor.sentences + ": <span  class=\"suggestedSentencesNo\"></span></div>" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100;\">" +client_messages.content_editor.words + ": <span  class=\"suggestedWordsNo\"></span></div>" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100;\">" +client_messages.content_editor.characters + ": <span  class=\"suggestedCharactersNo\"></span></div>" +
                    "</div>" +
                    "<div style=\"display:flex;\">" +
                    "<div style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100;\">" +client_messages.content_editor.averageReadTime + ": <span  class=\"suggestedAverageReadTime\"></span></div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelSuggestedBody\" style=\"padding: 8px 0px;\">" +
                    "<div class=\"mcePlainSugestedContent\" style=\"padding: 8px 24px; line-height: 1.5; overflow: auto; position: relative;\">" +
                    "<div id=\"rewriteContainer\" class=\"rewriteContainer\" style=\"display: none; margin-top: 2px;\">" +
                    "<div id=\"rewriteLoadingIndicator\" class=\"loading-indicator\" style=\"margin-top: 42px; text-align: center;\">" +
                    "<i class=\"far fa-shake fa-pen\" style=\"--fa-animation-duration: 15s; font-size: 20px;\"></i>" +
                    "<p style=\"font-size: 14px; margin-top: 8px;\">" + client_messages.text.writing_in_progress + "...</p>" +
                    "</div>" +
                    "<div id=\"rewriteResultContainer\" class=\"rewriteResultContainer\">" +
                    "   <div class=\"rewriteResultText\" style=\"display: none;\">" +
                    "   </div>" +
                    "   <div class=\"rewriteResultMarkup\" style=\"display: none;\">" +
                    "   </div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>");

            $(panelHTML).find('.mceCloseCustomPanelBtn').click(function( ) {
                togglePlainLanguagePanel();
            });

            $(getTopFrame().document).find('body').append( panelHTML );

            $(panelHTML).find('.panelExpandToggle').click( function() {
                togglePanelExpand(this);
            });

            $(panelHTML).find('[data-toggle=tooltip]').tooltip();

            var frameHeight = $(getTopFrame().document).find('#page').height();
            $(getTopFrame().document).find('#mce_plain_panel')
                .css({maxHeight: frameHeight + 'px'});

            var frameHeight = $(getTopFrame().document).find('#mce_plain_panel').outerHeight() - 250;
            $(getTopFrame().document).find('#mce_plain_panel .mcePlainContent')
                .css({maxHeight: frameHeight + 'px', height: 200  + 'px'});

            setAAPanel($(getTopFrame().document).find('#mce_plain_panel'));
        }

        ed.settings.contentintel.plain.is_panel_active = !ed.settings.contentintel.plain.is_panel_active;
        let statusPanel = $(ed.editorContainer);
        if(ed.settings.connectedContentIntel){
            statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
        }

        if ( ed.settings.contentintel.plain.is_panel_active ) {
            var hasContent = updateContentInfo();
            if ( hasContent) {
                statusPanel.find('#mce_plain_btn').css({'background-color': '#636161'})
                    .find('.mce-btn-label').css({'color': '#ffffff'});
                initPlainPanel();
            }
        } else {
            statusPanel.find('#mce_plain_btn').css({'background-color': 'inherit'})
                .find('.mce-btn-label').css({'color': 'inherit'});
            $(getTopFrame().document).find('#mce_plain_panel').remove();
        }

        shiftPrimaryDisplay();
    }

    function toggleTranslatePanel() {
        function initTranslatePanel() {
            var panelHTML =
                $("<div id=\"mce_translate_panel\" class=\"mce-custompanel\" style=\"z-index: 1000; width: 450px; position: fixed; top: 0px; right: 0px; height: 100%; border-left: 1px solid #979797; background-color: #fff;\">" +
                    "<div id=\"crt_translate_panel\">" +
                    "<div class=\"mcePanelHeader\" style=\"background-color: #666; height: 56px; vertical-align: middle; position: relative; color: #fff;\">" +
                    "<div class=\"panelExpandToggle fa-mp-container\" data-toggle=\"tooltip\" title=\"" + client_messages.content_editor.toggle_panel_size + "\" style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 18px 0px 24px;\">" +
                    "<i class=\"far fa-chevron-left\" ></i>" +
                    "</div>" +
                    "<div style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 24px 0px 0px;\">" +
                    client_messages.content_editor.current +
                    "</div>" +
                    "<div class=\"mceCloseCustomPanelBtn\" style=\"position: absolute; right: 24px; top: 12px; font-size: 18px; cursor: pointer; width: 12px;\">" +
                    "<i class=\"far fa-times\" ></i>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelTranslate\" style=\"background-color: #f5f5f5; padding: 4px 20px; color: #171717; font-size: 13px; white-space: nowrap;\">" +
                    "<div id=\"targetLocaleContainer\" style=\"display: none; vertical-align: middle\">" +
                    "<div style=\"display: inline-block; font-size: 14px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 18px 0px 0px;\">" +
                    "Translate to" +
                    "</div>" +
                    "<div style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 24px 0px 0px;\">" +
                    "<select id=\"targetLocaleSelect\" class=\"custom-select\">" +
                    "<option value=\"18\">French (Canada)</option>" +
                    "<option selected=\"selected\" value=\"49\">Spanish (US)</option>" +
                    "</select>" +
                    "</div>" +
                    "</div>" +
                    "<div style=\"display: inline-block; vertical-align: top\">" +
                    "<div class=\"mcePanelAccuracy\" style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; \">" +
                    "   <div class=\"mce-status-ind\" style=\"display: inline-block; vertical-align: top; border: 2px solid #e1e1e1; box-sizing: border-box; border-radius: 6px; width: 12px; height: 12px; background-color: white; position: relative; top: 5px;\"></div>&nbsp;" +
                    "   <span  class=\"crtLanguage\"></span>" +
                    "   <div  class=\"crtAccuracy\" style=\"white-space: normal;\">" +
                    "       <div class=\"progress-loader progress-loader-sm\">" +
                    "           <i class=\"progress-loader-icon far fa-spinner-third\"></i>" +
                    "       </div>" +
                    "   </div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelBody\" style=\"padding: 8px 0px;\">" +
                    "<div class=\"mceTranslateContent\" style=\"padding: 4px 20px; line-height: 1.5; overflow: auto; position: relative;\">" +
                    "<div style=\"text-align: center; margin-top: 48px;\">" +
                    "<div class=\"progress-loader progress-loader-lg\">" +
                    "<i class=\"progress-loader-icon far fa-spinner-third\"></i>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div id=\"suggest_buttons_panel\" >" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div id=\"suggest_panel\" style=\"display:none;\" class=\"showIfLicenced\">" +
                    "<div class=\"mceSuggestPanelHeader\" style=\"background-color: #666; height: 56px; vertical-align: middle; position: relative; color: #fff;\">" +
                    "<div class=\"panelExpandToggle fa-mp-container\" data-toggle=\"tooltip\" title=\"" + client_messages.content_editor.toggle_panel_size + "\" style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 18px 0px 24px;\">" +
                    "<i class=\"far fa-chevron-left\" ></i>" +
                    "</div>" +
                    "<div style=\"display: inline-block; font-size: 18px; line-height: 56px; height: 56px; font-weight: 300; vertical-align: middle; padding: 0px 24px 0px 0px;\">" +
                    client_messages.content_editor.translation_suggest +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mceSuggestPanelSummary\" style=\"background-color: #f5f5f5; padding: 4px 20px; color: #171717; font-size: 13px; white-space: nowrap;\">" +
                    "<div style=\"display: inline-block; vertical-align: top\">" +
                    "<div class=\"mceSuggestedPanelAccuracy\" style=\"vertical-align: middle; padding: 0px 8px; font-weight: 100; \">" +
                    "   <div class=\"mce-status-ind\" style=\"display: inline-block; vertical-align: top; border: 2px solid #e1e1e1; box-sizing: border-box; border-radius: 6px; width: 12px; height: 12px; background-color: white; position: relative; top: 5px;\"></div>&nbsp;" +
                    "<span  class=\"suggestedLanguage\"></span>" +
                    "   <div  class=\"suggestedAccuracy\" style=\"white-space: normal; \">" +
                    "       <div class=\"progress-loader progress-loader-sm\">" +
                    "           <i class=\"progress-loader-icon far fa-spinner-third\"></i>" +
                    "       </div>" +
                    "   </div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "<div class=\"mcePanelSuggestedBody\" style=\"padding: 8px 0px;\">" +
                    "<div class=\"mceTranslateSugestedContent\" style=\"padding: 4px 20px; line-height: 1.5; overflow: auto; position: relative;\">" +
                    "<div id=\"rewriteContainer\" class=\"rewriteContainer\" style=\"display: none; margin-top: 2px;\">" +
                    "<div id=\"rewriteLoadingIndicator\" class=\"loading-indicator\" style=\"margin-top: 42px; text-align: center;\">" +
                    "<i class=\"far fa-shake fa-pen\" style=\"--fa-animation-duration: 15s; font-size: 20px;\"></i>" +
                    "<p style=\"font-size: 14px; margin-top: 8px;\">" + client_messages.text.writing_in_progress + "...</p>" +
                    "</div>" +
                    "<div id=\"rewriteResultContainer\" class=\"rewriteResultContainer\">" +
                    "   <div class=\"rewriteResultText\" style=\"display: none;\">" +
                    "   </div>" +
                    "   <div class=\"rewriteResultMarkup\" style=\"display: none;\">" +
                    "   </div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>");

            if (ed.settings.connectedContentIntel) {
                $(panelHTML).find('.crtAccuracy').remove();
                $(panelHTML).find('#targetLocaleContainer').show();
            }

            $(panelHTML).find('.mceCloseCustomPanelBtn').click(function( ) {
                toggleTranslatePanel();
            });

            $(getTopFrame().document).find('body').append( panelHTML );

            $(panelHTML).find('.panelExpandToggle').click( function() {
                togglePanelExpand(this);
            });

            $(panelHTML).find('[data-toggle=tooltip]').tooltip();

            var frameHeight = $(getTopFrame().document).find('#page').height();
            $(getTopFrame().document).find('#mce_translate_panel')
                .css({maxHeight: frameHeight + 'px'});

            var frameHeight = $(getTopFrame().document).find('#mce_translate_panel').outerHeight() - 250;
            $(getTopFrame().document).find('#mce_translate_panel .mceTranslateContent')
                .css({maxHeight: frameHeight + 'px', height: 230  + 'px'});

            setAAPanel($(getTopFrame().document).find('#mce_translate_panel'));
        }

        ed.settings.contentintel.translate.is_panel_active = !ed.settings.contentintel.translate.is_panel_active;
        let statusPanel = $(ed.editorContainer);
        if(ed.settings.connectedContentIntel){
            statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
        }

        if ( ed.settings.contentintel.translate.is_panel_active ) {
           var hasContent = updateContentInfo();
           if ( hasContent) {
                statusPanel.find('#mce_translate_btn').css({'background-color': '#636161'})
                    .find('.mce-btn-label').css({'color': '#ffffff'});
                initTranslatePanel();
           }
        } else {
            statusPanel.find('#mce_translate_btn').css({'background-color': 'inherit'})
                .find('.mce-btn-label').css({'color': 'inherit'});
            $(getTopFrame().document).find('#mce_translate_panel').remove();
        }

        shiftPrimaryDisplay();
    }

    function toggleContentComparePanel() {

        function initContentComparePanel() {

            if ( getTopFrame().tinymce_data == undefined )
                getTopFrame().tinymce_data = {};
            getTopFrame().tinymce_data.current_editor = ed;

            var contextParams = "";
            if ( ed.settings.content_object_id != undefined)
                contextParams += "&contentObjectId=" + ed.settings.content_object_id;
            if ( ed.settings.variant_id != undefined )
                contextParams += "&variantId=" + ed.settings.variant_id;

            $(ed.editorContainer).iFramePopup({
                src					: ed.settings.content_compare_url + contextParams,
                width				: 550,
                id					: "contentCompare",
                type				: "panelRight",
                title				: client_messages.content_editor.content_duplicates_and_similarities,
                displayOnInit		: true,
                appliedParams		: {
                    iframe_popup_id: null
                },
                screenMask			: false,
                setFullModalPopup	: false,
                matchContentHeight	: false,
                draggable			: false,
                applySnapOffToggle 	: true,
                beforePopupClose	: function(o) {
                    toggleContentComparePanel();
                }
            });
        }

        ed.settings.contentintel.contentcompare.is_panel_active = !ed.settings.contentintel.contentcompare.is_panel_active;
        if ( ed.settings.contentintel.contentcompare.is_panel_active ) {
            $(ed.editorContainer).find('#mce_contentcompare_btn').css({'background-color': '#636161'})
                .find('.mce-btn-label i').css({'color': '#ffffff'});
            $(ed.editorContainer).find('#mce_contentcompare_btn .fa-exclamation-circle').hide();
            initContentComparePanel();
        } else {
            $(ed.editorContainer).find('#mce_contentcompare_btn').css({'background-color': 'inherit'})
                .find('.mce-btn-label i').css({'color': 'inherit'});
            $(ed.editorContainer).find('#mce_contentcompare_btn .fa-exclamation-circle').show();
            $(getTopFrame().document).find('#iFramePopup_contentCompare').remove();
        }

        shiftPrimaryDisplay();
    }

    function toggleBrandCheckPanel() {

        function initBrandCheckPanel() {

            if ( getTopFrame().tinymce_data == undefined )
                getTopFrame().tinymce_data = {};
            getTopFrame().tinymce_data.current_editor = ed;

            var contextParams = "";
            if ( ed.settings.content_object_id != undefined )
                contextParams += "&contentObjectId=" + ed.settings.content_object_id;
            if ( ed.settings.variant_id != undefined )
                contextParams += "&variantId=" + ed.settings.variant_id;
            if ( ed.settings.communication_id != undefined )
                contextParams += "&communicationId=" + ed.settings.communication_id;

            let statusPanel = $(ed.editorContainer);
            if(ed.settings.connectedContentIntel){
                statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
            }

            statusPanel.iFramePopup({
                src					: ed.settings.brand_check_url + contextParams,
                width				: 550,
                id					: "brandCheck",
                type				: "panelRight",
                title				: client_messages.content_editor.brand,
                displayOnInit		: true,
                appliedParams		: {
                    iframe_popup_id: null
                },
                screenMask			: false,
                setFullModalPopup	: false,
                matchContentHeight	: false,
                draggable			: false,
                applySnapOffToggle 	: true,
                beforePopupClose	: function(o) {
                    toggleBrandCheckPanel();
                }
            });
        }

        ed.settings.contentintel.brandcheck.is_panel_active = !ed.settings.contentintel.brandcheck.is_panel_active;
        let statusPanel = $(ed.editorContainer);
        if(ed.settings.connectedContentIntel){
            statusPanel = $("#"+ed.settings.connectedContentIntel.marciePanelId);
        }
        if ( ed.settings.contentintel.brandcheck.is_panel_active ) {
            statusPanel.find('#mce_brandcheck_btn').css({'background-color': '#636161'})
                .find('.mce-btn-label').css({'color': '#ffffff'});
            initBrandCheckPanel();
        } else {
            statusPanel.find('#mce_brandcheck_btn').css({'background-color': 'inherit'})
                .find('.mce-btn-label').css({'color': 'inherit'});
            $(getTopFrame().document).find('#iFramePopup_brandCheck').remove();
        }

        shiftPrimaryDisplay();

    }

    function shiftPrimaryDisplay() {
        if ( $(document).find('.shiftContainer').is('.shiftLeft') )
            $(document).find('.shiftContainer').removeClass('shiftLeft');
        else
            $(document).find('.shiftContainer').addClass('shiftLeft');

        var shiftFrame = $("body", getTopFrame().document).find(".shiftFrameContainer:not('#iFramePopup_brandCheck,#iFramePopup_contentCompare,#iFramePopup_summarize')");
        if ( $(shiftFrame).is('.shiftFrameLeft') )
            $(shiftFrame).removeClass('shiftFrameLeft');
        else
            $(shiftFrame).addClass('shiftFrameLeft');
    }

    function togglePanelExpand(container) {
        var panel = $(container).closest('.mce-custompanel');
        let panelExpandToggleArray = $(getTopFrame().document).find('.panelExpandToggle');

        if ( $(container).find('i').is('.fa-chevron-left') ) {
            for (let i = 0; i < panelExpandToggleArray.length; i++) {
                $(panelExpandToggleArray[i]).find('i').removeClass('fa-chevron-left').addClass('fa-chevron-right');
                localStorage.setItem("aaPanelSetting", "fa-chevron-right");
                $($(panel).find('#crtAccDetails')[0]).css('width', '820px');
                $($(panel).find('#suggAccDetails')[0]).css('width', '820px');
                setTranslationAccuracyDetailsHeight('.suggestedAccuracy #suggAccDetails');
                setTranslationAccuracyDetailsHeight('.crtAccuracy #crtAccDetails');
            }
            $(panel).css({'width':'850px'});
        } else {
            for (let i = 0; i < panelExpandToggleArray.length; i++) {
                $(panelExpandToggleArray[i]).find('i').removeClass('fa-chevron-right').addClass('fa-chevron-left');
                localStorage.setItem("aaPanelSetting", "fa-chevron-left");
                $($(panel).find('#crtAccDetails')[0]).css('width', '420px');
                $($(panel).find('#suggAccDetails')[0]).css('width', '420px');
                setTranslationAccuracyDetailsHeight('.suggestedAccuracy #suggAccDetails');
                setTranslationAccuracyDetailsHeight('.crtAccuracy #crtAccDetails');
            }
            $(panel).css({'width':'450px'});
        }
    }

    function toggleRewriteMarkup(isBtnClick,ele) {
        if ( isBtnClick == true ) {
            if ( $(ele).is('.fa-toggle-off') )
                $(ele).removeClass('fa-toggle-off').addClass('fa-toggle-on');
            else
                $(ele).removeClass('fa-toggle-on').addClass('fa-toggle-off');
        }
        if ( $(ele).is('.fa-toggle-on') ) {
            $(ele).closest('.mce-custompanel').find('.rewriteResultText,.mceSuggestedReadabilityIndex,.mceSuggestedSentimentIndex').hide();
            $(ele).closest('.mce-custompanel').find('.rewriteResultMarkup').show();
        } else {
            $(ele).closest('.mce-custompanel').find('.rewriteResultMarkup').hide();
            $(ele).closest('.mce-custompanel').find('.rewriteResultText,.mceSuggestedReadabilityIndex,.mceSuggestedSentimentIndex').show();
        }
    }

    function extractSentencesArray(edContent, ed) {
        var contentArray = new Array();
        var contentClone = $(edContent).clone();

        // Smart text: Transform CSS block content back into P tags
        $(contentClone).find('.varTagRenderedBlock,[block_content=true]').replaceWith(function () {
            return $('<p />').append($(this).contents());
        });
        // Insert in DOM will clean nested block content
        $('body').append("<div id=\"tempContentWrapper\">" + $(contentClone).html() + "</div>");
        contentClone = $('#tempContentWrapper').clone();
        $('#tempContentWrapper').remove();
        var variablesArray =[];
        $(contentClone).find('p,li').each(function () {

            var tempArray = [];
            $(this).find('.CmCaReT,#cursorPos,.renderedLabelContainer').remove();
            $(this).find('[type=1]').each(function () {
                if ($(this).find('.renderedSampleValueContainer').length > 0) {
                    $(this).html($(this).find('.renderedSampleValueContainer').text());
                } else {
                    tempArray.push($(this).text());
                    $(this).html('$$var$$');
                }
            });
            $(this).find('[type=15]').each(function () {
                if ($(this).find('.mceContentMenuValue').length > 0)
                    $(this).html($(this).find('.mceContentMenuValue').text());
            });
            $(this).find('.mceHiddenContent').each(function () {
                $(this).html('');
            });
            if ($.trim($(this).text().replace(/[\u200B-\u200D\uFEFF\u200E\u200F]/g, '')).length > 0) {
                var currentInd = contentArray.length;

                let currentTxt = '';
                if ($(this).find('ul,ol').length != 0)
                    $(this).contents()
                        .filter(function () {
                            return !$(this).is('ul,ol')
                        })
                        .each(function () {
                            currentTxt += $(this).text();
                        });
                else
                    currentTxt = $(this).text();
                contentArray[currentInd] = currentTxt.replace(/\u00A0/g, ' ');

                // Template: Replace text with template marker
                if (ed.settings.contentintel.sentiment.is_panel_active || ed.settings.contentintel.readability.is_panel_active) {
                    if ($(this).find('ul,ol').length != 0)
                        $(this).contents()
                            .filter(function () {
                                return !$(this).is('ul,ol')
                            })
                            .replaceWith("<div id=\"ind_" + currentInd + "\">PLACEHOLDER</div>");
                    else
                        $(this).html("<div id=\"ind_" + currentInd + "\">PLACEHOLDER</div>");
                }
                variablesArray.push(tempArray);
            }

        });
        return {contentArray, contentClone, variablesArray};
    }

    function validateAndSaveLimitValue() {
        var doc = $(getTopFrame().document);
        var crtWordsCountText = $(doc).find('#summarizeDesiredLength')[0].getAttribute("max");
        var minWordCountText = $(doc).find('#summarizeDesiredLength').val();
        if (new String(minWordCountText).trim().length >= 0) {
            var minWordCountNumber = parseInt(minWordCountText);
            if (new String(minWordCountText).trim().length == 0 || isNaN(minWordCountNumber) || minWordCountNumber < 1 || minWordCountNumber > parseInt(crtWordsCountText)) {
                $(doc).find('#btnSuggestRewrite').attr('disabled', 'disabled');
                $(doc).find('.tooltip').remove();
                    $(doc).find("#summarizeDesiredLength").popupFactory({
                        title: "Error",
                        popupLocation: "bottom",
                        trigger: "instant",
                        width: 200,
                        popup_id: "summarize_limit_error_popup",
                        fnSetContent: function (o) {
                            var popupEle = $("<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" + client_messages.summarize.error + "</div>");

                            return popupEle;
                        }
                    });
            } else {
                $(doc).find('#summarize_limit_error_popup').hide();
                $(doc).find('#btnSuggestRewrite').removeAttr('disabled');
                $(doc).find('#mce_summarize_panel [data-toggle=tooltip]').each( function() {
                    getTopFrame().initTooltip(this);
                });
            }
        }
    }

    function setSummarizeLimitValue(data) {
        var doc = $(getTopFrame().document);
        var nbWords = parseInt(data.summarize.words);
        var defaultSummarizeLimitValue = parseInt(Math.round(nbWords / 3));
        $(doc).find("#summarizeDesiredLength").attr("value", defaultSummarizeLimitValue);
    }

    function setAAPanel(panel) {
        var aaPanelSetting = localStorage.getItem("aaPanelSetting");
        if(aaPanelSetting !== "fa-chevron-left") {
            let panelExpandToggleArray = $(getTopFrame().document).find('.panelExpandToggle');
            for (let i = 0; i < panelExpandToggleArray.length; i++) {
                $(panelExpandToggleArray[i]).find('i').removeClass('fa-chevron-left').addClass('fa-chevron-right');
            }
            $(panel).css({'width':'850px'});
        } else{
            let panelExpandToggleArray = $(getTopFrame().document).find('.panelExpandToggle');
            for (let i = 0; i < panelExpandToggleArray.length; i++) {
                $(panelExpandToggleArray[i]).find('i').removeClass('fa-chevron-right').addClass('fa-chevron-left');
            }
            $(panel).css({'width':'450px'});
        }

    }
    if ( (window.document != getTopFrame().document ) || (ed.settings.connectedContentIntel)) {
        $(getTopFrame().document).find('#mce_sentiment_panel,#mce_readability_panel,#iFramePopup_contentCompare').remove();
    }


    String.prototype.hashCode = function() {
        var hash = 0,
            i, chr;
        if (this.length === 0) return hash;
        for (i = 0; i < this.length; i++) {
            chr = this.charCodeAt(i);
            hash = ((hash << 5) - hash) + chr;
            hash |= 0; // Convert to 32bit integer
        }
        return hash;
    }

    ed.on('init', function() {

        setTimeout( function() {
            let actionName = 'init';
            if (ed.settings.connectedutils
                && ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_contentintel', actionName)
            ) {
                return;
            }
            updateContentInfo();

            initInterface();
            toggleGutterDisplay();
            localStorage.removeItem("contentHash");
        }, 500);
    });

    ed.on('keyup', function() {
        clearTimeout(ed.settings.contentintel.content_change_timer);

        ed.settings.contentintel.content_change_timer = setTimeout( function() {
            let actionName = 'keyup.content_change_timer';
            if (ed.settings.connectedutils
                && ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_contentintel', actionName)
            ) {
                return;
            }
            updateContentInfo();
        }, 1000 );

    });

    ed.on('NodeChange', function() {
        clearTimeout(ed.settings.contentintel.content_change_timer);

        ed.settings.contentintel.content_change_timer = setTimeout( function() {
            let actionName = 'NodeChange.content_change_timer';
            if (ed.settings.connectedutils
                && ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_contentintel', actionName)
            ) {
                return;
            }
            if(ed.settings.contentintel.translate.is_panel_active) {
                if (ed.selection != null && ed.selection.getContent() != null && ed.selection.getContent().length > 0) {
                    if (ed.selection.getContent() !== $(ed.getBody()).html()) {
                        return;
                    }
                }
            }
            updateContentInfo();
        }, 1000 );

    });
    ed.on('change', function() {
        clearTimeout(ed.settings.contentintel.content_change_timer);

        ed.settings.contentintel.content_change_timer = setTimeout( function() {
            let actionName = 'change.content_change_timer';
            if (ed.settings.connectedutils
                && ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_contentintel', actionName)
            ) {
                return;
            }
            updateContentInfo();
        }, 1000 );

    });

    ed.on('SetContent', function() {
        toggleGutterDisplay();

        function resetAAPanels() {
            if ($(document).find('.shiftContainer').is('.shiftLeft'))
                $(document).find('.shiftContainer').removeClass('shiftLeft');

            var shiftFrame = $("body", getTopFrame().document).find(".shiftFrameContainer:not('#iFramePopup_brandCheck,#iFramePopup_contentCompare,#iFramePopup_summarize')");
            if ($(shiftFrame).is('.shiftFrameLeft'))
                $(shiftFrame).removeClass('shiftFrameLeft');
            ed.settings.contentintel.readability.is_panel_active = false;
            ed.settings.contentintel.sentiment.is_panel_active = false;
            ed.settings.contentintel.summarize.is_panel_active = false;
            ed.settings.contentintel.plain.is_panel_active = false;
            ed.settings.contentintel.translate.is_panel_active = false;
            ed.settings.contentintel.contentcompare.is_panel_active = false;
            ed.settings.contentintel.brandcheck.is_panel_active = false;
            $(ed.editorContainer).find('#mce_readability_btn').css({'background-color': 'inherit'}).find('.mce-btn-label').css({'color': 'inherit'});
            $(ed.editorContainer).find('#mce_sentiment_btn').css({'background-color': 'inherit'}).find('.mce-btn-label').css({'color': 'inherit'});
            $(ed.editorContainer).find('#mce_summarize_btn').css({'background-color': 'inherit'})
            $(ed.editorContainer).find('#summarize_btn_icon').css({'color': 'black'});
            $(getTopFrame().document).find('#summarize_limit_error_popup').hide();
            $(ed.editorContainer).find('#mce_plain_btn').css({'background-color': 'inherit'}).find('.mce-btn-label').css({'color': 'inherit'});
            $(ed.editorContainer).find('#mce_translate_btn').css({'background-color': 'inherit'}).find('.mce-btn-label').css({'color': 'inherit'});
            $(ed.editorContainer).find('#mce_contentcompare_btn').css({'background-color': 'inherit'})
                .find('.mce-btn-label i').css({'color': 'inherit'});
            $(ed.editorContainer).find('#mce_contentcompare_btn .fa-exclamation-circle').show();

            $(ed.editorContainer).find('#mce_brandcheck_btn').css({'background-color': 'inherit'})
                .find('.mce-btn-label').css({'color': 'inherit'});
        }

        if ( $(getTopFrame().document).find('#mce_summarize_panel,#mce_sentiment_panel,#mce_readability_panel, #iFramePopup_brandCheck, #mce_plain_panel, #mce_translate_panel, #iFramePopup_contentCompare').length > 0 ) {
            $(getTopFrame().document).find('.shiftContainer').addClass('shiftLeft');
            var shiftFrame = $("body", getTopFrame().document).find(".shiftFrameContainer:not('#iFramePopup_brandCheck,#iFramePopup_contentCompare,#iFramePopup_summarize')");
            $(shiftFrame).addClass('shiftFrameLeft');
        } else{
            resetAAPanels();

        }
        updateContentInfo();
    });

    if ( ed.prinovaPlugins == undefined )
        ed.prinovaPlugins = {};
    ed.prinovaPlugins.contentintel = {};

    var contentCompareConfigured = ed.settings.marcie_flags != undefined && ed.settings.marcie_flags.content_compare_enabled;
    var marcieEnabled = ed.settings.marcie_flags != undefined && ed.settings.marcie_flags.marcie_enabled;
    var isMessagepoint = $($(getTopFrame().document)).find('#contentLanguageSelect')[0] !== undefined;
    var isMessagepointWithMultipleLanguages = isMessagepoint ? $($(getTopFrame().document)).find('#contentLanguageSelect')[0].length > 1 : false;
    var isDefaultLanguageSelected = isMessagepoint ? $($($(getTopFrame().document)).find('#contentLanguageSelect')[0].selectedOptions[0]).text().indexOf("Default")!=-1 : false;
    var brandCheckConfigured = ed.settings.marcie_flags != undefined && ed.settings.marcie_flags.brand_check_enabled;
    if(ed.settings.connectedContentIntel === undefined){
        ed.settings.contentintel = {
            summarize_enabled		: localStorage.getItem("msgpt_summarize_enable") == "true" && marcieEnabled,
            readability_enabled		: localStorage.getItem("msgpt_readability_enable") == "true" && marcieEnabled,
            sentiment_enabled		: localStorage.getItem("msgpt_sentiment_enable") == "true" && marcieEnabled,
            contentcompare_enabled	: localStorage.getItem("msgpt_contentcompare_enable") == "true" && contentCompareConfigured,
            brandcheck_enabled		: localStorage.getItem("msgpt_brandcheck_enable") == "true" && brandCheckConfigured,
            plain_enabled	    	: localStorage.getItem("msgpt_plain_enable") == "true" && marcieEnabled,
            translate_enabled		: localStorage.getItem("msgpt_translate_enable") == "true" && marcieEnabled && ed.settings.rationalizer_application_id === undefined && (isMessagepointWithMultipleLanguages || !isDefaultLanguageSelected)
        };
    } else {
        //Connected
        ed.settings.contentintel = ed.settings.connectedContentIntel;
        ed.settings.contentintel.translate_enabled = common.isFeatureEnabled('ConnectedTranslationEnabled');
    }
    ed.settings.contentintel.content_change_timer = null;
    ed.settings.contentintel.content_template = null;
    ed.settings.contentintel.translated_vars = [];
    ed.settings.contentintel.template_index = 0;
    ed.settings.contentintel.readability = {
        is_panel_active: false
    };
    ed.settings.contentintel.sentiment = {
        is_panel_active: false
    };
    ed.settings.contentintel.summarize = {
        is_panel_active: false
    };
    ed.settings.contentintel.plain = {
        is_panel_active: false
    };
    ed.settings.contentintel.translate = {
        is_panel_active: false
    };
    ed.settings.contentintel.contentcompare = {
        is_panel_active: false
    };
    ed.settings.contentintel.brandcheck = {
        is_panel_active: false,
        is_panel_update_pending: false
    };

    if ( marcieEnabled ) {
        // Menu Item: Summarize
        ed.addMenuItem('mp_summarize', {
            text: client_messages.content_editor.summarize,
            selectable: true,
            onclick: function (o) {
                var p = ed.prinovaPlugins;
                ed.settings.contentintel.summarize_enabled = !ed.settings.contentintel.summarize_enabled;
                p.summarize.active(ed.settings.contentintel.summarize_enabled);
                localStorage.setItem("msgpt_summarize_enable", ed.settings.contentintel.summarize_enabled);

                toggleGutterDisplay();
            },
            onPostRender: function (e) {
                ed.prinovaPlugins.summarize = this;
                ed.prinovaPlugins.summarize.active(ed.settings.contentintel.summarize_enabled);
            }
        });
        // Menu Item: Readability
        ed.addMenuItem('mp_readability', {
            text: client_messages.content_editor.readability,
            selectable: true,
            onclick: function (o) {
                var p = ed.prinovaPlugins;
                ed.settings.contentintel.readability_enabled = !ed.settings.contentintel.readability_enabled;
                p.readability.active(ed.settings.contentintel.readability_enabled);
                localStorage.setItem("msgpt_readability_enable", ed.settings.contentintel.readability_enabled);

                toggleGutterDisplay();
            },
            onPostRender: function (e) {
                ed.prinovaPlugins.readability = this;
                ed.prinovaPlugins.readability.active(ed.settings.contentintel.readability_enabled);
            }
        });

        // Menu Item: Sentiment
        ed.addMenuItem('mp_sentiment', {
            text: client_messages.content_editor.sentiment,
            selectable: true,
            onclick: function (o) {
                var p = ed.prinovaPlugins;
                ed.settings.contentintel.sentiment_enabled = !ed.settings.contentintel.sentiment_enabled;
                p.sentiment.active(ed.settings.contentintel.sentiment_enabled);
                localStorage.setItem("msgpt_sentiment_enable", ed.settings.contentintel.sentiment_enabled);

                toggleGutterDisplay();
            },
            onPostRender: function (e) {
                ed.prinovaPlugins.sentiment = this;
                ed.prinovaPlugins.sentiment.active(ed.settings.contentintel.sentiment_enabled);
            }
        });

        if ( contentCompareConfigured ) {
            // Menu Item: Content Compare
            ed.addMenuItem('mp_contentcompare', {
                text: client_messages.content_editor.duplicates_and_similarities,
                selectable: true,
                onclick: function (o) {
                    var p = ed.prinovaPlugins;
                    ed.settings.contentintel.contentcompare_enabled = !ed.settings.contentintel.contentcompare_enabled;
                    p.contentcompare.active(ed.settings.contentintel.contentcompare_enabled);
                    localStorage.setItem("msgpt_contentcompare_enable", ed.settings.contentintel.contentcompare_enabled);

                    toggleGutterDisplay();
                },
                onPostRender: function (e) {
                    ed.prinovaPlugins.contentcompare = this;
                    ed.prinovaPlugins.contentcompare.active(ed.settings.contentintel.contentcompare_enabled);
                }
            });
        }
    }

    // Menu Item: Brand
    if ( brandCheckConfigured ) {

        // JS Include: Required for content parsing
        if ( $(document).find('#jsBrandCheckJavascript').length == 0 )
            $(document).find('head').append("<script id=\"jsBrandCheckJavascript\" src=\"" + context + "/content/javascript/brand_check.js\" >");

        ed.addMenuItem('mp_brandcheck', {
            text: client_messages.content_editor.brand,
            selectable: true,
            onclick: function (o) {
                var p = ed.prinovaPlugins;
                ed.settings.contentintel.brandcheck_enabled = !ed.settings.contentintel.brandcheck_enabled;
                p.brandcheck.active(ed.settings.contentintel.brandcheck_enabled);
                localStorage.setItem("msgpt_brandcheck_enable", ed.settings.contentintel.brandcheck_enabled);

                toggleGutterDisplay();
            },
            onPostRender: function (e) {
                ed.prinovaPlugins.brandcheck = this;
                ed.prinovaPlugins.brandcheck.active(ed.settings.contentintel.brandcheck_enabled);
            }
        });
    }

    // Menu Item: Plain language
    ed.addMenuItem('mp_plain', {
        text: client_messages.content_editor.plain,
        selectable: true,
        onclick: function (o) {
            var p = ed.prinovaPlugins;
            ed.settings.contentintel.plain_enabled = !ed.settings.contentintel.plain_enabled;
            p.plain.active(ed.settings.contentintel.plain_enabled);
            localStorage.setItem("msgpt_plain_enable", ed.settings.contentintel.plain_enabled);

            toggleGutterDisplay();
        },
        onPostRender: function (e) {
            ed.prinovaPlugins.plain = this;
            ed.prinovaPlugins.plain.active(ed.settings.contentintel.plain_enabled);
        }
    });

    // Menu Item: Translate
    if (ed.settings.rationalizer_application_id === undefined && (isMessagepointWithMultipleLanguages || !isDefaultLanguageSelected)) {
        ed.addMenuItem('mp_translate', {
            text: client_messages.content_editor.translate,
            selectable: true,
            onclick: function (o) {
                var p = ed.prinovaPlugins;
                ed.settings.contentintel.translate_enabled = !ed.settings.contentintel.translate_enabled;
                p.translate.active(ed.settings.contentintel.translate_enabled);
                localStorage.setItem("msgpt_translate_enable", ed.settings.contentintel.translate_enabled);

                toggleGutterDisplay();
                let statusPanel = $(ed.editorContainer);
                if (statusPanel.find('#mce_translate_btn').is(':visible')) {
                    getTranslationAccuracy(false);
                }
            },
            onPostRender: function (e) {
                ed.prinovaPlugins.translate = this;
                ed.prinovaPlugins.translate.active(ed.settings.contentintel.translate_enabled);
            }
        });
    }
    // Content Intelligence: Submenu
    if ( marcieEnabled || contentCompareConfigured || brandCheckConfigured ) {
        ed.addMenuItem('mp_contentintel', {
            text: client_messages.content_editor.content_intelligence,
            menu: [ed.menuItems['mp_summarize'], ed.menuItems['mp_readability'], ed.menuItems['mp_sentiment'], ed.menuItems['mp_brandcheck'], ed.menuItems['mp_plain'], ed.menuItems['mp_translate'], ed.menuItems['mp_contentcompare']],
            onPostRender: function (e) {
                ed.prinovaPlugins.contentintel = this;
            }
        });
    }

});
