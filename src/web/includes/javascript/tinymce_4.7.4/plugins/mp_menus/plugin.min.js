
tinymce.PluginManager.add('mp_menus', function(ed, url) {

	ed.addCommand('mceEnhanceMenu', function(ui, v) {
		if ( v != undefined ) {

			var menu = v.control ? v.control.getEl() : v.getEl();
			var items = v.control ? v.control.items() : v.items();

			ed.execCommand("mceLayerPanels", false, this);
			for ( var i = 0; i < items.length; i++ ) {
				var currentItem = items[i].getEl();
				var label = $.trim( $(currentItem).text() );
				if ( !$(currentItem).hasClass('.detailTip') && label.length > 15 ) {
					$(currentItem).addClass('detailTip').attr("title","|<div class='detailTipText'>" + label + "</div>");
					initTip($(currentItem), {zIndex: 70000});
				};
			};

			if ( $(menu).find('.mceMenuSearchContainer').length == 0 && items.length > 10 ) {

				$(menu)
					.prepend(	"<div class=\"mceMenuSearchContainer\">" +
									"<div style=\"display: inline-block; position: relative;\">" +
										"<input type=\"text\" class=\"mceMenuSearchInput\" />" +
										"<i class=\"searchIconDiv fa fa-med fa-search fa-mp-style\" style=\"display: inline-block; margin-top: 4px; position: absolute; font-family: 'Font Awesome 6 Pro';\"></i>" +
									"</div>" +
								"</div>" +
								"<div class=\"mceNoMenuSearchItems\" style=\"display: none; font-size: 12px; color: #555; padding: 8px 8px 0px 8px; font-style: italic; white-space: initial\">" +
									client_messages.text.no_matching_items +
								"</div>");
				
				$(menu).find('.mce-container-body')
					.css({
						'max-height': ed.settings.menu_max_height ? 
										Math.min (ed.settings.menu_max_height, parseFloat($(menu).css('max-height').replace('px',''))) + 'px' : 
										$(menu).css('max-height'),
						'padding-top': $(menu).css('padding-top'),
						'padding-bottom': $(menu).css('padding-bottom'),
						'overflow-y' : 'auto',
						'overflow-x' : 'hidden'
					});
				$(menu).css({
					'max-height':'none',
					'padding-top': '0px',
					'padding-bottom': '0px',
					'z-index' : '11000'
				});

				setTimeout( function(){
					let actionName = 'mceEnhanceMenu';
					if (ed.settings.connectedutils
						&& ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_menus', actionName)
					) {
						return;
					}
					$(menu).find('.mceMenuSearchInput').width( $(menu).find('.mceMenuSearchContainer').width() - 80);
					if ( v.control.submenu && v.control.submenu == true )
						$(menu).css({ 'margin-top':'-32px' });
				}, 10 );

				if ( v.control && v.control.settings.tag_cloud_type )
					$(menu).find('.mceMenuSearchInput').tagCloud({
						tagCloudType			: v.control.settings.tag_cloud_type,
						zoneId					: ed.settings.zone_id && v.control.settings.tag_cloud_type == 1 ? ed.settings.zone_id : -1,
						rightOffsetAdj			: 25,
						topOffsetAdj			: 15,
						popupLocation			: 'right',
						inputType				: 'search',
						afterInputValueChange	: function(o, val) {
							$(menu).find('.mceMenuSearchInput').keyup();
						}
					});
				
				$(menu).find('.mceMenuSearchInput')
					.keyup( function(e) {

						var searchInput 		= this;
						var notEmptySearch		= $(searchInput).val() != '';
						var searchTokens 		= $(searchInput).val().toLowerCase().replace(/\s/g,',').split(',');
						var noItems				= true;

						for ( var i = 0; i < items.length; i++ ) {
								var includeItem = true;
								var currentItem = items[i].getEl();

								if ( notEmptySearch ) {
									for ( var j = 0; j < searchTokens.length; j++ ) {
										if ( searchTokens[j].substring(0,4) == "tag:" ) {
											var tagToken = $.trim( searchTokens[j].replace('tag:','').replace(',','') );
											if ( $(currentItem).attr('tags') != undefined && $(currentItem).attr('tags').toLowerCase().indexOf( tagToken ) == -1 )
												includeItem = false;
										} else if ( searchTokens[j] != '' ) {
											if ( $(currentItem).text().toLowerCase().indexOf( $.trim(searchTokens[j]) ) == -1 )
												includeItem = false;
										}
									}
								}

								if (includeItem) {
									$(currentItem).show();
									noItems = false;
								} else {
									$(currentItem).hide();
								}
						}
						
						if ( noItems )
							$(menu).find('.mceNoMenuSearchItems').show();
						else
							$(menu).find('.mceNoMenuSearchItems').hide();

					})
					.click( function(e) {
						$(this).focus();
					});
			};
			
		};
	});

});
