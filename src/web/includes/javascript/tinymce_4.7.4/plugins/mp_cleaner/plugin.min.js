
tinymce.PluginManager.add('mp_cleaner', function(ed, url) {
	
	ed.addCommand('mceLayerPanels', function(ui, v) {
		setTimeout( function() {
			let actionName = 'mceLayerPanels';
			if (ed.settings.connectedutils
				&& ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_cleaner', actionName)
			) {
				return;
			}
			// Menus
			$(ed.container).closest('body').find('.mce-menu:visible').css({ 'z-index': 1000 });
			// Floating toolbar (tables)
			$(ed.container).closest('body').find('.mce-tinymce-inline:visible').css({ 'z-index': 999 });
		}, 10);
	});

	var getRootStaticItem = function(ele) {
		var target = ele;
		while ( $(target).parent().closest('.staticContentItem').length != 0 )
			target = $(target).parent();
		return target;
	}
	
	var parentAtCursorPos = function() {
		ed.settings.utils.focus(); // Focus before accessing editor.selection
		if (ed.selection.getContent({format:'text'}).length > 0 ||
			$(ed.selection.getContent({format:'html'})).is('.staticContentItem') ) { // Case: Text selection
			return $(ed.selection.getNode());
		} else { // Case: No text selection
			ed.selection.setContent('<span id="cursorPos" style="display:none;">IE_ENFORCE</span>');
			var parentNode = $(ed.getDoc()).find('#cursorPos').parent();
			$(ed.getDoc()).find('#cursorPos').remove();
			return parentNode;
		}
	};

	// Remove TABLE tags that have no TD tags
	function cleanEmptyTables() {
		$(ed.getBody()).find('table').each( function() {
			if ( $(this).find('td,th').length == 0 ) {
				console.log('mp_cleaner: Removing empty table: ', this);
				$(this).remove();
			}
		});
	}

	// data-mce-bogus and #_mce_caret:  Control markers used by tinyMCE (Don't manipulate)
	function cleanSpanTags() {
		
		var currentSpanCursorEle = $(ed.selection.getStart()).closest('span');

		$(ed.getBody()).find('span').each( function() {
			// Remove empty SPAN tags
			if ( $(this).text().length == 0 && $(this).attr('data-mce-bogus') == undefined &&
				 $(this).closest('.staticContentItem,[type],.mceContentMenuItem').length == 0 &&
				 this != $(currentSpanCursorEle).get(0) )
				$(this).remove();

			// SMART CANVAS: Enforce empty paragraph spacing
			if ( $.trim( $(this).text() ).length == 0 && $(this).is('.varTagRenderedBlock') )
				$(this).html("&nbsp;");
			
			// Remove wrapping SPAN tags which impart no style value
			var spanClone = $(this).clone();
			$(spanClone)
				.contents()
				.filter(function() {
					return 	this.nodeType !== 3 && 
							!$(this).is('.mceContentMenuItem,.staticContentItem,[type],.mceNonEditable,.embedded_content_tag,.varTagRenderedInline,#_mce_caret,#mce_noneditablecaret') &&
							$(this).attr('data-mce-bogus') == undefined &&
							$(this).is('span');
				}).each( function() {
					$(this).remove();
				});
				
			if ( $(spanClone).text().length == 0 ) {
				if ( $(this).closest('.staticContentItem').length == 0 &&
					 !$(this).is('.mceContentMenuItem,.staticContentItem,[type],.mceNonEditable,.embedded_content_tag,.varTagRenderedInline,#_mce_caret,#mce_noneditablecaret') &&
					 $(this).attr('data-mce-bogus') == undefined &&
					 this != $(currentSpanCursorEle).get(0) ) {
					$(this).before( $(this).contents() );
					$(this).remove();
				}
			}
			
		});
	}
	
	function cleanImgTags() {
		$(ed.getBody()).find('img:not([image_file_id],[sandbox_file_id],[barcode_id],[dna])').each( function() {
			if ( $(this).attr('src') != undefined && $(this).attr('src').indexOf('http') == -1 && $(this).attr('rel_src') == undefined ) {
				ed.dom.setAttrib( $(this), 'rel_src', $(this).attr('src'));
				ed.dom.setAttrib( $(this), 'src', url + '/img/relative_path_img.png');
			} 
			if ( $(this).attr('rel_src') != undefined && ($(this).attr('src') == undefined || $(this).attr('src').indexOf('relative_path_img.png') == -1) ) {
				ed.dom.setAttrib( $(this), 'src', url + '/img/relative_path_img.png');
			}
		});
	}

	// NOTE: Call before cleanSpanTags or style cleaner will remove
	function cleanEjectedSmartTextContent() {

		// Style actions may remove class atttribute when ST inserted as SPAN
		$(ed.getBody()).find('[type=4]:not(.staticContentItem),[type=10]:not(.staticContentItem),[type=5]:not(.staticContentItem)').each( function() {
			$(this).addClass('staticContentItem mceNonEditable embedded_content_tag');
		});

		// Remove any content that doesn't have a wrapping ST tag
		$(ed.getBody()).find('.renderedLabelContainer,.renderedContentContainer,.innerStaticContentItem').each( function() {
			if ( $(this).closest('.staticContentItem').length == 0 )
				$(this).remove();
		});
	}

	function enforceParagraphs() {
		// Without content paragraphs shrink to 0 height (can't use max-height as it creates drag boxes in IE)
		$(ed.getBody()).children('p').each( function(){
			if ( $(this).html().length == 0 && $(this).text().length == 0 )
				$(this).html('<br data-mce-bogus="1">');
		});
		$(ed.getBody()).find('[data-edit-mode=block_insert_point] > p').each( function(){
			if ( $(this).html().length == 0 && $(this).text().length == 0 )
				$(this).html('<br data-mce-bogus="1">');
		});
	}

	ed.on('change', function(e) {
		cleanEjectedSmartTextContent();
		cleanSpanTags();
		cleanImgTags();
		cleanEmptyTables();
		enforceParagraphs();
		cleanEjectedSmartTextContent();
	});
	ed.on('SetContent', function(e) {
		cleanEjectedSmartTextContent();
		cleanImgTags();
	});
	ed.on('NodeChange', function(e) {
		cleanEjectedSmartTextContent();
		enforceParagraphs();
	})
	
	ed.on('click', function(e) {

		var element = $(e.target);
		var isContentMenuClick = $(e.target).closest('[type=15]').length != 0 && $(e.target).closest('[type=15]').parent().closest('.staticContentItem').length != 0;
		
		if ( $(element).closest('.staticContentItem').length != 0 )
			element = $(element).closest('.staticContentItem').get(0);

		$(ed.getBody()).find('.staticContentItem.active').removeClass('active');
		if ( $(element).is('.staticContentItem') ) {
			$(element).each(function() {
				var target = getRootStaticItem(this);
				$(target).addClass('active');

				if ( !isContentMenuClick ) {
					// Update style controls for selected VAR
					if ( ed.settings.styles != undefined )
						if ( $.isFunction(ed.settings.styles.fn.toggle_control_values) )
							ed.settings.styles.fn.toggle_control_values();
				}
			});
		}
		
		$(ed.getBody()).find('.staticContentItem').closest('.mce-offscreen-selection').remove();

	});
	
	ed.on('mousedown', function(e) {
		
		var element = $(e.target);
		// Prevent editor default drag behavior
		if ( $(element).closest('.mceStaticContainer').length != 0 ) {
			e.preventDefault();
			return false;
		}
		
	});
	
	ed.on('keydown',function(e){

		$(getTopFrame().document).find('.contentPopup_popupContainer').remove();

		var keyCode = e.keyCode;
		if (keyCode == 8 || keyCode == 46) {

			// If cursor is current within a static element, ensure element is selected before processing delete
			var parent = parentAtCursorPos();
			if ( parent != undefined && $(parent).closest('.staticContentItem').length != 0 ) {
				var target = getRootStaticItem(parent);
				if ( !$(target).is('.active') ) {
					$(ed.getBody()).find('.active').removeClass('active');
					$(target).addClass('active');
				}
			}
			
			if ( $(ed.getBody()).find('.active').length > 0 ) {
				var targetEle = $(ed.getBody()).find('.active').first();

				// Current selection is nested within static content item: prevent delete
				if ( $(targetEle).parent().closest('.staticContentItem').length != 0 )
					return;

				if ( $(targetEle).is('.staticContentItem') ) {
					$(targetEle).remove();
					e.preventDefault();
					return false;
				} else if ( $(targetEle).attr('template_fixed_content') != undefined && $(targetEle).attr('template_fixed_content') == "true" ) {
					e.preventDefault();
					return false;
				}
				
			}

		}

		// Deselect vars on keypress
		$(ed.getBody()).find('.staticContentItem').each(function() {
			$(this).removeClass('active');
		});
	});
	
	ed.on('init', function(e) {
		cleanEjectedSmartTextContent();
		cleanSpanTags();
		cleanEmptyTables();
		cleanEjectedSmartTextContent();
		
		if ( $('#contentTrimTypeSelect').length != 0 )
			window.beforeDoSubmit =  function() {

				$('.textContentInput textarea').each( function() {
					
					var text = $(this).val();
					text = text.replace('#MP_TRIM_0','').replace('#MP_TRIM_1','').replace('#MP_TRIM_2','').replace('#MP_TRIM_3','');
					
					var trimType = 0;
					if ( $('#contentTrimTypeSelect').length != 0 )
						trimType = $('#contentTrimTypeSelect').val();
					if ( trimType && trimType != 0 && !ed.settings.canvas_freeform ) {
						$(this).val( text + '#MP_TRIM_' + $('#contentTrimTypeSelect').val() );
					}
					
				});

			}

	});
});