tinymce.PluginManager.add('mp_customlistproperties', function(ed, url) {

    var t = this;
    if ( ed.prinovaPlugins == undefined ) {
        ed.prinovaPlugins = {};
    }
    ed.prinovaPlugins.customListProperties = t;

    //region Panel actions
    function storeOnLoadState(toggleState, left, top) {
        if ( ed.settings.general && ed.settings.general.persist_workspace == false )
            return;

        if ( typeof(Storage)!=="undefined" ) {
            localStorage.setItem("msgpt_editor_onloadstate_customlistpanel", JSON.stringify({
                state: toggleState,
                x: left,
                y: top
            }));
        }
    }

    function generatePanel() {
        var stampDate = new Date();
        var urlParam = "&cacheStamp=" + (stampDate.getTime());
        var height = getPanelHeight();

        var panelProperties = {
            title	: client_messages.content_editor.custom_list_properties,
            url		: ed.settings.custom_list_properties_url,
            width	: 610,
            height	: height,
        };

        var menuHTML = "";

        menuHTML += "<div id='mce-custompanel-custom-list-properties' class='mce-container mce-panel mce-custompanel mce-menu' role='application' style='position: absolute; border-width: 1px; max-height: none; height: " + panelProperties.height + "px; min-width: " + panelProperties.width + "px; padding-top: 0px; padding-bottom: 0px; z-index: 1050 !important;'>" +
            "<div class='mcePanelNameContainer mce-window-head'>" +
            "<div class='mce-title'>" +
            panelProperties.title +
            "</div>" +
            "<button type='button' class='mce-close' aria-hidden='true'><i class='mce-ico mce-i-remove'></i></button>" +
            "</div>" +
            "<div class='mce-container-body mce-stack-layout' role='menu' style='max-height: " + panelProperties.height + "px; padding-top: 5px; padding-bottom: 0px; overflow-y: auto; overflow-x: auto;'>" +
            "<iframe name='customListStylesPanelPropertiesIframe' src='" + panelProperties.url + "' tabindex='-1' style='width:" + panelProperties.width + "px; height: " + (panelProperties.height - 60) + "px'></iframe>" +
            "</div>" +
            "</div>";

        menuHTML = $(menuHTML);

        $(getTopFrame().document).find('#mce-custompanel-custom-list-properties .mcePanelNameContainer').disableSelection();

        return menuHTML;
    }

    function positionPanel() {
        if ( ed.settings.customlist.ele == null )
            return;

        var pos_x, pos_y;
        var scrollTop = $(window).scrollTop();
        var height = $(ed.settings.customlist.ele).height();
        var width = $(ed.settings.customlist.ele).width();
        var windowHeight = $(window).height();
        var windowWidth = $(window).width();
        pos_y = ((windowHeight - height) / 2) + scrollTop;
        pos_x = (windowWidth - width) / 2;

        if ( (!ed.settings.general || ed.settings.general.persist_workspace != false ) &&
            localStorage.getItem("msgpt_editor_onloadstate_customlistpanel") != null ) {
            try {
                var tablePanelState = JSON.parse( localStorage.getItem("msgpt_editor_onloadstate_customlistpanel") );
                pos_x = tablePanelState.x > 0 ? tablePanelState.x : 0;
                pos_y = tablePanelState.y > 0 ? tablePanelState.y : 0;
            } catch (e) {
                console.log(e)
            }
        }

        $(ed.settings.customlist.ele).css({
            top: pos_y + 'px',
            left: pos_x + 'px'
        });
    }

    function animateBorderColor() {
        $(ed.settings.customlist.ele).animate({
            'border-color': 'purple'
        }, 250, function() {
            $(ed.settings.customlist.ele).animate({
                'border-color': '#e2e4e7'
            }, 250);
        });
    }

    function togglePanel(persistedState) {
        if ( ed.settings.customlist && ed.settings.customlist.ele != null) {
            if ( $(ed.settings.customlist.ele).is(':visible') ) {
                animateBorderColor();
            } else {
                positionPanel();
                $(ed.settings.customlist.ele).show();
                storeOnLoadState(true, $(ed.settings.customlist.ele).offset().left, $(ed.settings.customlist.ele).offset().top);
            }
        } else {
            $.extend(ed.settings.customlist, {
                ele: $(generatePanel())
            });

            $(getTopFrame().document).find('body').append(ed.settings.customlist.ele);

            getTopFrame().initDraggable($(ed.settings.customlist.ele), {
                iframeFix: true,
                handle: '.mcePanelNameContainer',
                containment: 'document',
                stop: function (event, ui) {
                    if (typeof (Storage) !== "undefined") {
                        storeOnLoadState(true, $(ed.settings.customlist.ele).offset().left, $(ed.settings.customlist.ele).offset().top);
                    }

                }
            });

            if (persistedState && persistedState.state == false) {
                var panel = $(ed.settings.customlist.ele);
                var topFrameDocument = $(getTopFrame().document);
                var panelWidth = panel.width();
                var panelHeight = panel.width();

                if (persistedState.x < 0) {
                    persistedState.x = 0;
                } else if (persistedState.x > topFrameDocument.width() - panelWidth ) {
                    persistedState.x = topFrameDocument.width() - panelWidth;
                }

                if (persistedState.y < 0) {
                    persistedState.y = 0;
                } else if (persistedState.y > topFrameDocument.height() - panelHeight) {
                    persistedState.y = topFrameDocument.height() - panelHeight;
                }

                panel.css({'top': (persistedState.y) + 'px', 'left': persistedState.x + 'px'});

                storeOnLoadState(true, persistedState.x, persistedState.y);
            } else {
                positionPanel();
                storeOnLoadState(true, $(ed.settings.customlist.ele).offset().left, $(ed.settings.customlist.ele).offset().top);
            }

        }
    }

    function panelIsOpen() {
        return $(getTopFrame().document).find('#mce-custompanel-custom-list-properties').length > 0;
    }

    function closePanel() {
        if (panelIsOpen()) {
            storeOnLoadState(false, $(getTopFrame().document).find('#mce-custompanel-custom-list-properties').offset().left, $(getTopFrame().document).find('#mce-custompanel-custom-list-properties').offset().top);
            $(getTopFrame().document).find('#mce-custompanel-custom-list-properties').remove();
            ed.settings.customlist.ele = null;
            ed.settings.customlist.selected_ele_lock = false;

            if (ed.prinovaPlugins.tagpanel != undefined) {
                ed.execCommand("mceRestoreTagPanel", false);
            }
        }
        $(getTopFrame().document).find('#mce-custompanel-custom-list-properties .mce-close').unbind();
    }

    function refreshPanel() {
        var height = getPanelHeight() + 'px';
        $(getTopFrame().document).find('#mce-custompanel-custom-list-properties').height(height);

        $(getTopFrame().document).find('#mce-custompanel-custom-list-properties .mce-close').unbind();
        var iframe = window.frames['customListStylesPanelPropertiesIframe'];
        if (!iframe || !iframe.init) return;
        iframe.init(true);
    }

    function isSefasOrMPComposer() {
        var settings = window.parent.tinymce.activeEditor.settings;
        return settings.mp_fn.qualify_channel_connectors({'channels': [],'connectors': [18,19] });
    }
    function isExstreamDXF() {
        var settings = window.parent.tinymce.activeEditor.settings;
        return settings.is_exstream_dxf || settings.is_exstream_runtime_dxf;
    }

    function getPanelHeight() {
        var sefasOrExstream = isSefasOrMPComposer() || isExstreamDXF();
        var height = sefasOrExstream ? 940 : 850;

        var selectedBlocks = ed.selection.getSelectedBlocks();
        if (selectedBlocks && selectedBlocks.length >= 1) {
            var selectedBlock = selectedBlocks[0];
            var listNode = getListNode(selectedBlock);
            if (listNode && $(listNode).is('ul')) {
                height = sefasOrExstream ? 800 : 710;
            }
        }

        return height;
    }

    function toggleElementsDisabled(element) {
        if (panelIsOpen()) {
            var listNode = null;

            if (element && !$(element).is("body")) {
                listNode = getListNode(element);
            } else if (ed.selection && ed.selection.getStart()) {
                listNode = getListNode(ed.selection.getStart());
            }

            var iframe = window.frames['customListStylesPanelPropertiesIframe'];

            if (iframe) {
                var interval = setInterval(function() {
                    if (iframe.document && iframe.document.readyState === 'complete'){
                        clearInterval(interval);
                        if (iframe && iframe.toggleElementsDisabled) {
                            iframe.toggleElementsDisabled(!listNode);
                        }
                    }
                }, 100);
            }
        }
    }
    //endregion

    //region Getting list node
    function getListNode(n) {
        do {
            if (n.nodeName.toLowerCase() == "ul" || n.nodeName.toLowerCase() == "ol") {
                return n;
            }
        } while (n = n.parentNode);
        return null;
    }

    function buildSelectedBlocksList(node) {
        if ( node == ed.selection.start_block )
            ed.selection.found_start_block = true;

        if (ed.selection.found_start_block && !ed.selection.found_end_block)
            ed.selection.selected_blocks.push(node);

        if ( node == ed.selection.end_block )
            ed.selection.found_end_block = true;

        $(node).children().each( function() {
            buildSelectedBlocksList(this);
        });
    }

    function getCommonAncestor(a, b) {
        var parents= $(a).parents().andSelf();
        while (b) {
            var ix= parents.index(b);
            if (ix!==-1)
                return b;
            b= b.parentNode;
        }
        return null;
    }

    function getAllSelectedBlocks(start, end) {
        var ancestor = getCommonAncestor(start, end);

        // ed.selection = {};
        $.extend(ed.selection,{
            found_start_block: false,
            start_block: start,
            found_end_block: false,
            end_block:end,
            selected_blocks: []
        });

        buildSelectedBlocksList(ancestor);
    }

    function prepareList() {
        if(ed.settings.utils && ed.selection) {
            ed.settings.utils.focus(); // Focus before accessing ed.selection

            if ( ed.selection.getSelectedBlocks() ) {
                // Get target nodes
                var selectedBlocks = ed.selection.getSelectedBlocks();
                getAllSelectedBlocks(selectedBlocks[0],selectedBlocks[selectedBlocks.length-1]);

                var targetNode;

                for (j in ed.selection.selected_blocks) {
                    if ( $(ed.selection.selected_blocks[j]).is('ul,ol') ) {
                        targetNode = getListNode(ed.selection.selected_blocks[j]);
                        ed.settings.customlist.selected_ele_lock = true;
                        ed.settings.customlist.selected_ele = targetNode;

                    } else if ( $(ed.selection.selected_blocks[j]).is('li,.mceListItemContent') ) {
                        $(ed.selection.selected_blocks[j]).closest('ul,ol').each( function() {
                            targetNode = getListNode(this)
                            ed.settings.customlist.selected_ele_lock = true;
                            ed.settings.customlist.selected_ele = targetNode;
                        });
                    }
                }
            }
        }
    }

    function isSelectingMoreThanOneList(ed) {
        var targetNodes = [];
        var selectedBlocks = ed.selection.getSelectedBlocks();

        if (selectedBlocks && selectedBlocks.length > 1) {
            selectedBlocks.forEach(function(selectedBlock) {
                if ($(selectedBlock).is('ul, ol')) {
                    var targetNode = getListNode(selectedBlock);
                    if (!targetNodes.includes(targetNode)) {
                        targetNodes.push(targetNode);
                    }
                } else if ($(selectedBlock).is('li, .mceListItemContent')) {
                    $(selectedBlock).closest('ul, ol').each(function () {
                        var targetNode = getListNode(this);
                        if (!targetNodes.includes(targetNode)) {
                            targetNodes.push(targetNode);
                        }
                    });
                }
            });
            return targetNodes.length > 1;
        }
        return false;
    }
    //endregion


    function showContinueConfirmationModal(callback, cancelAction) {
        var modalTemplate = '<div class="modal fade" id="formWarningConfirmationModal" ' +
        'tabindex="-1" role="dialog" aria-labelledby="taskDetailsModalLabel" aria-hidden="true">' +
        '<div class="modal-dialog" role="document" style="width: 33rem;">' +
        '<div class="modal-content">' +
        '<div class="bg-warning position-absolute rounded-top py-1 w-100" role="presentation"></div>' +
        '<div class="modal-header pb-3 pl-4 border-0">' +
        '<h4 class="modal-title position-relative pl-5" id="formWarningConfirmationModalLabel">' +
        '<i class="far fa-exclamation-circle fa-lg position-absolute left-0 mt-3 text-warning" aria-hidden="true"></i>' +
        client_messages.title.paragraph_styles_will_be_deleted +
        '</h4>' +
        '</div>' +
        '<div class="modal-body pt-0 pb-2">' +
        '<p class="mx-4 px-3">' +
        client_messages.title.custom_paragraph_style_removes +
        '</p>' +
        '</div>' +
        '<div class="modal-footer bg-lightest py-3 px-4">' +
        '<button type="button" class="btn btn-lightest" data-dismiss="modal">' + client_messages.text.cancel + '</button>' +
        '<button type="button" class="btn btn-warning">' + client_messages.text.yes_continue + '</button>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>';

        var $continueConfirmationModal = $(modalTemplate).modal({show: false});
        var $footer = $continueConfirmationModal.find('.modal-footer');

        if (cancelAction) {
            $footer.children().first().on('click', cancelAction);
        }

        $footer.children().last().on('click', function() {
            $continueConfirmationModal.modal('hide');
            callback();
        });

        $continueConfirmationModal.modal('show');
    }

    function hasParagraphStyles(list) {
        var topLevelList = $(list).parents('ul, ol').length ? $(list).parents('ul,ol').last() : list;
        var paragraphStyleAttr = 'paragraphclass';

        var hasParagraphStyle = $(topLevelList).find('li[' + paragraphStyleAttr + ']').length > 0;

        return hasParagraphStyle;
    }

    function removeParagraphStyles(list) {
        var paragraphStyleAttr = 'paragraphclass';

        // create an interval to check if iframe has finished loading
        var interval = setInterval(function() {
            var iframe = window.frames['customListStylesPanelPropertiesIframe'];
            if (iframe && iframe.document && iframe.document.readyState === 'complete'){
                clearInterval(interval);

                var targetNode = list;

                while ( $(targetNode).parent().closest('ul,ol').length != 0 ) {
                    $(targetNode).find('li').removeAttr(paragraphStyleAttr);
                    targetNode = $(targetNode).parent().closest('ul,ol');
                }

                $(targetNode).find('li').removeAttr(paragraphStyleAttr);

            }
        }, 100);
    }

    function openCustomListPropertiesDialogue(panelState) {
        prepareList();

        var callback = function() {
            togglePanel(panelState);
            removeParagraphStyles(ed.settings.customlist.selected_ele);
        }

        if (hasParagraphStyles(ed.settings.customlist.selected_ele)) {
            showContinueConfirmationModal(callback);
        } else {
            callback();
        }
    }

    function parseListAttributes(list) {

        var $list = $(list);
        var listId = $list.attr('custom_list_id');
        if (listId) {
            // Remove existing custom list styles if any
            ed.dom.remove(ed.dom.select('#customListStyles_' + listId));
        }

        function asciiToHex(str) {
            return Array.from(str)
                .map(char => char.charCodeAt(0).toString(16))
                .join('');
        }

        function rgbToCmyk(args) {
            const r = args[0] / 255;
            const g = args[1] / 255;
            const b = args[2] / 255;

            const k = Math.min(1 - r, 1 - g, 1 - b);
            const c = (1 - r - k) / (1 - k) || 0;
            const m = (1 - g - k) / (1 - k) || 0;
            const y = (1 - b - k) / (1 - k) || 0;

            return [c * 100, m * 100, y * 100, k * 100];
        }

        function getAttr(attr, list) {
            if (!list || list.length === 0) return null;

            var ulComputedStyle = window.getComputedStyle(list[0]);
            var liComputedStyle = window.getComputedStyle($(list).find('li')[0]);
            var liBeforeComputedStyle = window.getComputedStyle($(list).find('li')[0], ':before');
            var contentComputedStyle = window.getComputedStyle($(list).find('li .mceListItemContent')[0]);

            if (list.attr(attr)) {
                switch (attr) {
                    case 'bullet_spacing':
                    case 'list_spacing':
                        var spacingValues = list.attr(attr).split(':').map(function(val) {
                            return parseFloat(val.replace('in', ''));
                        });

                        return {
                            top: spacingValues[0],
                            right: spacingValues[1],
                            bottom: spacingValues[2],
                            left: spacingValues[3]
                        };
                    case 'bullet_symbol':
                        // Here we have the symbol in hex format
                        var symbol = list.attr(attr);
                        symbol = symbol.includes('counter') ? null : list.attr(attr);
                        return symbol;
                    case 'bullet_color':
                    case 'bullet_font':
                    case 'bullet_size':
                    case 'text_align':
                        return list.attr(attr);
                    case 'line_spacing':
                        var lineSpacing = list.attr(attr);
                        var value = parseFloat(lineSpacing);
                        var type = lineSpacing.slice(-1) === 'm' ? 1 : 2;
                        return {
                            value: value,
                            type: type
                        };
                }
            } else {
                switch (attr) {
                    case 'bullet_spacing':
                        var top = contentComputedStyle.getPropertyValue('padding-top');
                        var bottom = contentComputedStyle.getPropertyValue('padding-bottom');
                        var right = parseFloat(contentComputedStyle.getPropertyValue('left')) - parseFloat(liBeforeComputedStyle.getPropertyValue('left'));
                        var left = liBeforeComputedStyle.getPropertyValue('left');

                        return {
                            top: parseFloat(top) / 100,
                            right: parseFloat(right) / 100,
                            bottom: parseFloat(bottom) / 100,
                            left: parseFloat(left) / 100
                        };
                    case 'list_spacing':
                        var top = ulComputedStyle.getPropertyValue('padding-top');
                        var bottom = ulComputedStyle.getPropertyValue('padding-bottom');
                        var right = parseFloat(liComputedStyle.getPropertyValue('width')) - parseFloat(contentComputedStyle.getPropertyValue('width')) - parseFloat(contentComputedStyle.getPropertyValue('left'));
                        var left = 0;
                        return {
                            top: parseFloat(top) / 100,
                            right: parseFloat(right) / 100,
                            bottom: parseFloat(bottom) / 100,
                            left: parseFloat(left) / 100
                        };
                    case 'bullet_symbol':
                        // Here we have the symbol in ascii format
                        var symbol = liBeforeComputedStyle.getPropertyValue('content');
                        symbol = symbol.includes('counter') || symbol == "none" ? "none" : asciiToHex(symbol.replace(/"/g, ''));
                        return symbol;
                    case 'bullet_color':
                        // Conver color from rgb to hex
                        var color = liBeforeComputedStyle.getPropertyValue('color');

                        if (color.includes('rgb')) {
                            var rgb = color.match(/\d+/g);
                            color = rgbToCmyk(rgb).map((num) => Number(num.toFixed(0)));;
                            color = 'cmyk(' + color.join(';') + ')';
                        }

                        return color;
                    case 'bullet_font':
                        return liBeforeComputedStyle.getPropertyValue('font-family').replace(/"/g, '');
                    case 'bullet_size':
                        var fontSize = liBeforeComputedStyle.getPropertyValue('font-size');
                        // convert font size to pt
                        var fontSizePt = (parseFloat(fontSize) * 0.75).toFixed(0);
                        return fontSizePt;
                    case 'text_align':
                        var textAlign = ulComputedStyle.getPropertyValue('text-align');
                        return textAlign;
                    case 'line_spacing':
                        var lineHeight = liComputedStyle.getPropertyValue('line-height');
                        var fontSize = parseFloat(liComputedStyle.getPropertyValue('font-size'));

                        var value = parseFloat(lineHeight);
                        var type = lineHeight.includes('px') ? 1 : 2;

                        if (type === 1) {
                            // Convert px to unitless by dividing by font size
                            value = value / fontSize;
                        }

                        return {
                            value: value,
                            type: type
                        };
                }
            }
            return null;
        }

        var listAttributes = {};

        // List Text Alignment
        listAttributes.text_align = getAttr('text_align', list);

        // Bullet Spacing
        listAttributes.bullet_spacing = getAttr('bullet_spacing', list);

        // List Spacing
        listAttributes.list_spacing = getAttr('list_spacing', list);

        // Bullet Symbol Overrides
        var bulletSymbol = getAttr('bullet_symbol', list);
        var bulletColor = getAttr('bullet_color', list);
        var bulletFont = getAttr('bullet_font', list);
        var bulletSize = getAttr('bullet_size', list);

        if (bulletSymbol || bulletColor || bulletFont || bulletSize) {
            listAttributes.bullet = {
                symbol: bulletSymbol,
                color: bulletColor,
                font: bulletFont,
                size: bulletSize
            };
        }

        // Line Spacing
        listAttributes.line_spacing = getAttr('line_spacing', list);

        return listAttributes;
    }

    function getOriginalListAttributes(list) {
        var $list = $(list);
        var custListAttr = ['text_align', 'bullet_spacing', 'list_spacing', 'bullet_symbol', 'bullet_color', 'bullet_font', 'bullet_size', 'line_spacing', 'custom_list_id'];

        // Temporary removing the custom_list_id attribute to get the computed styles
        var attrBackup = {};
        $list.each(function() {
            $.each(this.attributes, function() {
                if (custListAttr.includes(this.name)) {
                    attrBackup[this.name] = this.value;
                }
            });
        });

        custListAttr.map(function(property) {
            $list.removeAttr(property);
        });

        var originalAttr = parseListAttributes(list);

        // Restore the original attributes
        $.each(attrBackup, function(name, value) {
            $list.attr(name, value);
        });

        return originalAttr;
    }

    function updateListStylesFromAttributes(list, important = false) {

        function componentToHex(c) {
            var hex = Math.round(c).toString(16);
            return hex.length === 1 ? "0" + hex : hex;
        }

        function cmykToHex(cmykString) {
            // Extract CMYK values from the input string
            var cmykValues = cmykString.slice(5, -1).split(';').map(Number);

            // Destructure the array into individual variables
            var c = cmykValues[0];
            var m = cmykValues[1];
            var y = cmykValues[2];
            var k = cmykValues[3];

            // Ensure values are within valid range (0 to 100)
            c = Math.max(0, Math.min(100, c));
            m = Math.max(0, Math.min(100, m));
            y = Math.max(0, Math.min(100, y));
            k = Math.max(0, Math.min(100, k));

            // Convert CMYK to RGB
            var r = 255 * (1 - c / 100) * (1 - k / 100);
            var g = 255 * (1 - m / 100) * (1 - k / 100);
            var b = 255 * (1 - y / 100) * (1 - k / 100);

            // Convert RGB to HEX
            var hex = "#" + componentToHex(r) + componentToHex(g) + componentToHex(b);
            return hex;
        }

        function parseValue(value) {
            return parseFloat(value) * 100 || 0;
        }

        var $list = $(list);

        var custListAttr = ['text_align', 'bullet_spacing','list_spacing','bullet_symbol','bullet_color','bullet_font','bullet_size','line_spacing'];
        // Determine if current list has at least one custom attribute; otherwise return;
        if ( custListAttr.every(function(attr) { return !$list.attr(attr); }) ) {

            // Remove existing custom list styles if any
            if($list.attr('custom_list_id')){
                ed.dom.remove(ed.dom.select('#customListStyles_' + $list.attr('custom_list_id')));
                $list.removeAttr('custom_list_id');
            }

            return;
        }

        var listId = $list.attr('custom_list_id') || Math.random().toString(36).substr(2, 9);
        $list.attr('custom_list_id', listId);

        // Remove existing custom list styles if any
        ed.dom.remove(ed.dom.select('#customListStyles_' + listId));

        var listAttr = parseListAttributes($list);
        var selector = $list.prop('tagName').toLowerCase() + '[custom_list_id="' + listId + '"]';

        var textAlign = listAttr.text_align;

        var bulletRightSpacing = parseValue(listAttr.bullet_spacing.right);
        var bulletLeftSpacing = parseValue(listAttr.bullet_spacing.left);
        var bulletTopSpacing = parseValue(listAttr.bullet_spacing.top);
        var bulletBottomSpacing = parseValue(listAttr.bullet_spacing.bottom);

        var listSpacingTop = parseValue(listAttr.list_spacing.top);
        var listSpacingBottom = parseValue(listAttr.list_spacing.bottom);
        var listSpacingRight = parseValue(listAttr.list_spacing.right);

        var lineSpacingValue = listAttr.line_spacing.value;
        var lineSpacingType = listAttr.line_spacing.type;

        var bullet = listAttr.bullet;
        var bulletSymbol = bullet.symbol;
        var bulletColor = bullet.color;
        var bulletFont = bullet.font;
        var bulletSize = bullet.size;

        var listStyle = '';
        if (listSpacingTop > 0)
            listStyle += 'padding-top:' + listSpacingTop + 'px;';
        if (listSpacingBottom > 0)
            listStyle += 'padding-bottom:' + listSpacingBottom + 'px;';
        if (textAlign)
            listStyle += 'text-align:' + textAlign + ';';

        var bulletContentStyle = "";
        bulletContentStyle += 'padding-top:' + bulletTopSpacing + 'px;';
        bulletContentStyle += 'padding-bottom:' + bulletBottomSpacing + 'px;';
        bulletContentStyle += 'left: ' + (bulletLeftSpacing + bulletRightSpacing) + 'px;';
        bulletContentStyle += 'width: calc(100% - ' + (bulletLeftSpacing + bulletRightSpacing + listSpacingRight) + 'px);';
        bulletContentStyle += 'line-height:' + (lineSpacingValue > 0 ? lineSpacingValue : 1) + (lineSpacingType == 2 ? 'pt': '') + ';';

        var bulletBefore = 'left: ' + bulletLeftSpacing + 'px; padding-top:' + bulletTopSpacing + 'px;';

        if (bullet) {
            if (bulletSymbol && bulletSymbol !== 'none') {
                bulletBefore += 'content: "\\'  + bulletSymbol + '";';
            }
            bulletBefore += "color: " + (bulletColor.includes('cmyk') ? cmykToHex(bulletColor) : bulletColor) + ";" +
                "font-size: " + bulletSize + "pt;" +
                "font-family: " + bulletFont + ";"
        }

        var listStyleCSS =
            '.contentContainer ' + selector + ' > li:before{' +
            bulletBefore +
            '} \n' +
            '.contentContainer ' + selector + ' {' +
            listStyle +
            '} \n' +
            '.contentContainer ' + selector + ' > li > div.mceListItemContent {' +
            bulletContentStyle +
            '} \n';

        if (important) {
             listStyleCSS = listStyleCSS.replace(/;/g, ' !important;');
        }

        // Add new custom list styles
        ed.dom.add(ed.dom.select('head'), 'style', {id: "customListStyles_" + listId, type: "text/css"}, listStyleCSS);
    }

    if ( ed.settings.mp_fn == undefined )
        ed.settings.mp_fn = {};

    ed.settings.mp_fn.get_original_list_attributes = function(list) { return getOriginalListAttributes(list); };
    ed.settings.mp_fn.parse_list_attributes = function(list) { return parseListAttributes(list); };
    ed.settings.mp_fn.update_list_styles_from_attributes = function(list, important) { return updateListStylesFromAttributes(list, important); };

    ed.settings.customlist = {
        selected_ele: null,
        selected_ele_lock : false,
    };

    ed.addCommand('mceOpenCustomListPropertiesDialogue', function() {
        openCustomListPropertiesDialogue();
        ed.settings.customlist.savedBookmark = ed.selection.getBookmark(1);
    });

    ed.addCommand('mceCloseCustomListPropertiesDialogue', function() {
        closePanel();
    });

    ed.addCommand('mceUpdateCustomListPropertiesDialogue', function() {
        if (panelIsOpen()) {
            removeParagraphStyles(ed.settings.customlist.selected_ele);
            prepareList();
            refreshPanel();
        }
    });

    ed.on('nodeChange', function(e){
        toggleElementsDisabled(e.element);
        $(ed.getBody()).find("[custom_list_id] > [paragraphclass]").removeAttr('paragraphclass');
        if(panelIsOpen() && isSelectingMoreThanOneList(ed)) {
            toggleElementsDisabled();
        }
    });

    ed.on('change', function(e) {
        if ( !ed.settings.customlist.selected_ele_lock )
            ed.settings.customlist.selected_ele = $(ed.selection.getStart()).closest('ul, ol').length != 0 ? $(ed.selection.getStart()).closest('ul, ol') : $(ed.getBody()).find('ul:first, ol:first');
    });

    ed.on('mouseup', function(e) {
        var selectedElementList = $(ed.settings.customlist.selected_ele).closest('ul,ol');
        var currentSelectionList = $(ed.selection.getStart()).closest('ul,ol');
        var isSwitchingLists = (selectedElementList.length && currentSelectionList.length) &&
            (!selectedElementList.is(currentSelectionList));

        if (!isSwitchingLists) {
            ed.settings.customlist.savedBookmark = ed.selection.getBookmark(1);
        }

        if (!panelIsOpen()) {
            return;
        }

        if(currentSelectionList.length) {

            if (isSwitchingLists) {
                ed.settings.customlist.selected_ele = currentSelectionList;
                ed.settings.customlist.selected_ele_lock = false;
                function callback() {
                    refreshPanel();
                    animateBorderColor();
                    removeParagraphStyles(ed.settings.customlist.selected_ele);
                }

                function cancelAction() {
                    ed.focus();
                    ed.selection.moveToBookmark(ed.settings.customlist.savedBookmark);
                    ed.settings.customlist.selected_ele = selectedElementList;
                }

                if (hasParagraphStyles(ed.settings.customlist.selected_ele)) {
                    showContinueConfirmationModal(callback, cancelAction);
                } else {
                    refreshPanel();
                    animateBorderColor();
                }
            } else {
                refreshPanel();
                animateBorderColor();
            }
        }
    });

    ed.on('undo redo', function() {
        var selectedList = $(ed.selection.getStart()).closest('ul, ol');
        if (selectedList.length === 0) {
            selectedList = $(ed.getBody()).find('ul:first, ol:first');
            if (selectedList.length === 0) {
                return;
            }
        }

        ed.settings.customlist.selected_ele = selectedList;

        $(getTopFrame().document).find('#mce-custompanel-custom-list-properties .mce-close').unbind();

        var iframe = window.frames['customListStylesPanelPropertiesIframe'];
        if (iframe && iframe.refreshList) {
            iframe.refreshList();
            refreshPanel();
        }
    });

    ed.on('BeforeExecCommand', function(e){
        if (e.command == "InsertUnorderedList" || e.command == "InsertOrderedList" ){
            ed.settings.utils.focus(); // Focus before accessing ed.selection

            if (!panelIsOpen()) {
                return;
            }

            // It's necessary to wait for the editor actions to complete refreshing the list type.
            setTimeout(function(){
                ed.execCommand('mceCloseCustomListPropertiesDialogue');
                ed.execCommand("mceCloseCustomParagraphStylesDialogue");
                ed.execCommand("mceOpenCustomPropertiesDialogue");
                ed.settings.customlist.selected_ele = $(ed.selection.getStart()).closest('ul, ol').length != 0 ? $(ed.selection.getStart()).closest('ul, ol') : $(ed.getBody()).find('ul:first, ol:first');
                animateBorderColor();
            }, 100);
        }
    });

    ed.on('init', function() {
        $(getTopFrame().document).on( 'click', '#mce-custompanel-custom-list-properties .mce-close', function() {
            closePanel();
        });

        // CDD-4921
        // It's necessary to wait for the editor actions to complete adding the custom styles.
        setTimeout(function(){
            $(ed.getBody()).find("ul,ol").each(function(index, list) {
                var customAttributes = ['text_align', 'bullet_spacing', 'list_spacing', 'bullet_symbol', 'bullet_color', 'bullet_font', 'bullet_size', 'line_spacing'];

                var hasCustomAttributes = customAttributes.some(function(attr) {
                    return list.getAttribute(attr) !== null;
                });

                if (!hasCustomAttributes)
                    return;

                updateListStylesFromAttributes(list, true);
            });

            if ( !ed.settings.general || ed.settings.general.persist_workspace != false ) {
                if (localStorage.getItem("msgpt_editor_onloadstate_customlistpanel") != null) {
                    try {
                        var panelState = JSON.parse(localStorage.getItem("msgpt_editor_onloadstate_customlistpanel"));
                        var currentSelectionIsList = $(ed.selection.getStart()).closest('ul, ol').length != 0;
                        if (panelState.state == true && currentSelectionIsList) {
                            openCustomListPropertiesDialogue(panelState);
                            toggleElementsDisabled();
                        }
                    } catch (e) {
                        console.log(e)
                    }
                }
            }
        }, 500);


    });
 });