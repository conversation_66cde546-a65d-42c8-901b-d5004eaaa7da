!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e[f]=d(a[f]);b.apply(null,e)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("d",tinymce.util.Tools.resolve),g("1",["d"],function(a){return a("tinymce.PluginManager")}),h("1p",Array),h("1q",Error),g("f",["1p","1q"],function(a,b){var c=function(){},d=function(a){return function(){return a()}},e=function(a,b){return function(){return a(b.apply(null,arguments))}},f=function(a){return function(){return a}},g=function(a){return a},h=function(a,b){return a===b},i=function(b){for(var c=new a(arguments.length-1),d=1;d<arguments.length;d++)c[d-1]=arguments[d];return function(){for(var d=new a(arguments.length),e=0;e<d.length;e++)d[e]=arguments[e];var f=c.concat(d);return b.apply(null,f)}},j=function(a){return function(){return!a.apply(null,arguments)}},k=function(a){return function(){throw new b(a)}},l=function(a){return a()},m=function(a){a()},n=f(!1),o=f(!0);return{noop:c,noarg:d,compose:e,constant:f,identity:g,tripleEquals:h,curry:i,not:j,die:k,apply:l,call:m,never:n,always:o}}),h("1r",Object),g("g",["f","1r"],function(a,b){var c=a.never,d=a.always,e=function(){return f},f=function(){var f=function(a){return a.isNone()},g=function(a){return a()},h=function(a){return a},i=function(){},j={fold:function(a,b){return a()},is:c,isSome:c,isNone:d,getOr:h,getOrThunk:g,getOrDie:function(a){throw new Error(a||"error: getOrDie called on none.")},or:h,orThunk:g,map:e,ap:e,each:i,bind:e,flatten:e,exists:c,forall:d,filter:e,equals:f,equals_:f,toArray:function(){return[]},toString:a.constant("none()")};return b.freeze&&b.freeze(j),j}(),g=function(a){var b=function(){return a},h=function(){return k},i=function(b){return g(b(a))},j=function(b){return b(a)},k={fold:function(b,c){return c(a)},is:function(b){return a===b},isSome:d,isNone:c,getOr:b,getOrThunk:b,getOrDie:b,or:h,orThunk:h,map:i,ap:function(b){return b.fold(e,function(b){return g(b(a))})},each:function(b){b(a)},bind:j,flatten:b,exists:j,forall:j,filter:function(b){return b(a)?k:f},equals:function(b){return b.is(a)},equals_:function(b,d){return b.fold(c,function(b){return d(a,b)})},toArray:function(){return[a]},toString:function(){return"some("+a+")"}};return k},h=function(a){return null===a||void 0===a?f:g(a)};return{some:g,none:e,from:h}}),h("1s",String),g("e",["g","1p","1q","1s"],function(a,b,c,d){var e=function(){var a=b.prototype.indexOf,c=function(b,c){return a.call(b,c)},d=function(a,b){return u(a,b)};return void 0===a?d:c}(),f=function(b,c){var d=e(b,c);return d===-1?a.none():a.some(d)},g=function(a,b){return e(a,b)>-1},h=function(a,b){return t(a,b).isSome()},i=function(a,b){for(var c=[],d=0;d<a;d++)c.push(b(d));return c},j=function(a,b){for(var c=[],d=0;d<a.length;d+=b){var e=a.slice(d,d+b);c.push(e)}return c},k=function(a,c){for(var d=a.length,e=new b(d),f=0;f<d;f++){var g=a[f];e[f]=c(g,f,a)}return e},l=function(a,b){for(var c=0,d=a.length;c<d;c++){var e=a[c];b(e,c,a)}},m=function(a,b){for(var c=a.length-1;c>=0;c--){var d=a[c];b(d,c,a)}},n=function(a,b){for(var c=[],d=[],e=0,f=a.length;e<f;e++){var g=a[e],h=b(g,e,a)?c:d;h.push(g)}return{pass:c,fail:d}},o=function(a,b){for(var c=[],d=0,e=a.length;d<e;d++){var f=a[d];b(f,d,a)&&c.push(f)}return c},p=function(a,b){if(0===a.length)return[];for(var c=b(a[0]),d=[],e=[],f=0,g=a.length;f<g;f++){var h=a[f],i=b(h);i!==c&&(d.push(e),e=[]),c=i,e.push(h)}return 0!==e.length&&d.push(e),d},q=function(a,b,c){return m(a,function(a){c=b(c,a)}),c},r=function(a,b,c){return l(a,function(a){c=b(c,a)}),c},s=function(b,c){for(var d=0,e=b.length;d<e;d++){var f=b[d];if(c(f,d,b))return a.some(f)}return a.none()},t=function(b,c){for(var d=0,e=b.length;d<e;d++){var f=b[d];if(c(f,d,b))return a.some(d)}return a.none()},u=function(a,b){for(var c=0,d=a.length;c<d;++c)if(a[c]===b)return c;return-1},v=b.prototype.push,w=function(a){for(var d=[],e=0,f=a.length;e<f;++e){if(!b.prototype.isPrototypeOf(a[e]))throw new c("Arr.flatten item "+e+" was not an array, input: "+a);v.apply(d,a[e])}return d},x=function(a,b){var c=k(a,b);return w(c)},y=function(a,b){for(var c=0,d=a.length;c<d;++c){var e=a[c];if(b(e,c,a)!==!0)return!1}return!0},z=function(a,b){return a.length===b.length&&y(a,function(a,c){return a===b[c]})},A=b.prototype.slice,B=function(a){var b=A.call(a,0);return b.reverse(),b},C=function(a,b){return o(a,function(a){return!g(b,a)})},D=function(a,b){for(var c={},e=0,f=a.length;e<f;e++){var g=a[e];c[d(g)]=b(g,e)}return c},E=function(a){return[a]},F=function(a,b){var c=A.call(a,0);return c.sort(b),c},G=function(b){return 0===b.length?a.none():a.some(b[0])},H=function(b){return 0===b.length?a.none():a.some(b[b.length-1])};return{map:k,each:l,eachr:m,partition:n,filter:o,groupBy:p,indexOf:f,foldr:q,foldl:r,find:s,findIndex:t,flatten:w,bind:x,forall:y,exists:h,contains:g,equal:z,reverse:B,chunk:j,difference:C,mapToObject:D,pure:E,sort:F,range:i,head:G,last:H}}),g("1t",["g","1r"],function(a,b){var c=function(){var a=b.keys,c=function(a){var b=[];for(var c in a)a.hasOwnProperty(c)&&b.push(c);return b};return void 0===a?c:a}(),d=function(a,b){for(var d=c(a),e=0,f=d.length;e<f;e++){var g=d[e],h=a[g];b(h,g,a)}},e=function(a,b){return f(a,function(a,c,d){return{k:c,v:b(a,c,d)}})},f=function(a,b){var c={};return d(a,function(d,e){var f=b(d,e,a);c[f.k]=f.v}),c},g=function(a,b){var c={},e={};return d(a,function(a,d){var f=b(a,d)?c:e;f[d]=a}),{t:c,f:e}},h=function(a,b){var c=[];return d(a,function(a,d){c.push(b(a,d))}),c},i=function(b,d){for(var e=c(b),f=0,g=e.length;f<g;f++){var h=e[f],i=b[h];if(d(i,h,b))return a.some(i)}return a.none()},j=function(a){return h(a,function(a){return a})},k=function(a){return j(a).length};return{bifilter:g,each:d,map:e,mapToArray:h,tupleMap:f,find:i,keys:c,values:j,size:k}}),g("1u",["e","f","1p","1q"],function(a,b,c,d){return function(){var e=arguments;return function(){for(var f=new c(arguments.length),g=0;g<f.length;g++)f[g]=arguments[g];if(e.length!==f.length)throw new d('Wrong number of arguments to struct. Expected "['+e.length+']", got '+f.length+" arguments");var h={};return a.each(e,function(a,c){h[a]=b.constant(f[c])}),h}}}),g("1z",["1p","1s"],function(a,b){var c=function(c){if(null===c)return"null";var d=typeof c;return"object"===d&&a.prototype.isPrototypeOf(c)?"array":"object"===d&&b.prototype.isPrototypeOf(c)?"string":d},d=function(a){return function(b){return c(b)===a}};return{isString:d("string"),isObject:d("object"),isArray:d("array"),isNull:d("null"),isBoolean:d("boolean"),isUndefined:d("undefined"),isFunction:d("function"),isNumber:d("number")}}),g("3r",["e","1z","1q"],function(a,b,c){var d=function(a){return a.slice(0).sort()},e=function(a,b){throw new c("All required keys ("+d(a).join(", ")+") were not specified. Specified keys were: "+d(b).join(", ")+".")},f=function(a){throw new c("Unsupported keys for object: "+d(a).join(", "))},g=function(d,e){if(!b.isArray(e))throw new c("The "+d+" fields must be an array. Was: "+e+".");a.each(e,function(a){if(!b.isString(a))throw new c("The value "+a+" in the "+d+" fields was not a string.")})},h=function(a,b){throw new c("All values need to be of type: "+b+". Keys ("+d(a).join(", ")+") were not.")},i=function(b){var e=d(b),f=a.find(e,function(a,b){return b<e.length-1&&a===e[b+1]});f.each(function(a){throw new c("The field: "+a+" occurs more than once in the combined fields: ["+e.join(", ")+"].")})};return{sort:d,reqMessage:e,unsuppMessage:f,validateStrArr:g,invalidTypeMessage:h,checkDupes:i}}),g("1v",["e","f","1t","g","3r","1q","1r"],function(a,b,c,d,e,f,g){return function(h,i){var j=h.concat(i);if(0===j.length)throw new f("You must specify at least one required or optional field.");return e.validateStrArr("required",h),e.validateStrArr("optional",i),e.checkDupes(j),function(f){var k=c.keys(f),l=a.forall(h,function(b){return a.contains(k,b)});l||e.reqMessage(h,k);var m=a.filter(k,function(b){return!a.contains(j,b)});m.length>0&&e.unsuppMessage(m);var n={};return a.each(h,function(a){n[a]=b.constant(f[a])}),a.each(i,function(a){n[a]=b.constant(g.prototype.hasOwnProperty.call(f,a)?d.some(f[a]):d.none())}),n}}}),g("1k",["1u","1v"],function(a,b){return{immutable:a,immutableBag:b}}),g("2c",["1k"],function(a){var b=a.immutable("width","height"),c=a.immutable("rows","columns"),d=a.immutable("row","column"),e=a.immutable("x","y"),f=a.immutable("element","rowspan","colspan"),g=a.immutable("element","rowspan","colspan","isNew"),h=a.immutable("element","rowspan","colspan","row","column"),i=a.immutable("element","cells","section"),j=a.immutable("element","isNew"),k=a.immutable("element","cells","section","isNew"),l=a.immutable("cells","section"),m=a.immutable("details","section"),n=a.immutable("startRow","startCol","finishRow","finishCol");return{dimensions:b,grid:c,address:d,coords:e,extended:h,detail:f,detailnew:g,rowdata:i,elementnew:j,rowdatanew:k,rowcells:l,rowdetails:m,bounds:n}}),g("21",[],function(){return"undefined"==typeof console&&(console={log:function(){}}),console}),h("23",document),g("l",["f","g","1q","21","23"],function(a,b,c,d,e){var f=function(a,b){var c=b||e,f=c.createElement("div");if(f.innerHTML=a,!f.hasChildNodes()||f.childNodes.length>1)throw d.error("HTML does not have a single root node",a),"HTML must have a single root node";return i(f.childNodes[0])},g=function(a,b){var c=b||e,d=c.createElement(a);return i(d)},h=function(a,b){var c=b||e,d=c.createTextNode(a);return i(d)},i=function(b){if(null===b||void 0===b)throw new c("Node cannot be null or undefined");return{dom:a.constant(b)}},j=function(a,c,d){return b.from(a.dom().elementFromPoint(c,d)).map(i)};return{fromHtml:f,fromTag:g,fromText:h,fromDom:i,fromPoint:j}}),g("20",[],function(){return{ATTRIBUTE:2,CDATA_SECTION:4,COMMENT:8,DOCUMENT:9,DOCUMENT_TYPE:10,DOCUMENT_FRAGMENT:11,ELEMENT:1,TEXT:3,PROCESSING_INSTRUCTION:7,ENTITY_REFERENCE:5,ENTITY:6,NOTATION:12}}),g("27",["e","g","l","20","1q","23"],function(a,b,c,d,e,f){var g=d.ELEMENT,h=d.DOCUMENT,i=function(a,b){var c=a.dom();if(c.nodeType!==g)return!1;if(void 0!==c.matches)return c.matches(b);if(void 0!==c.msMatchesSelector)return c.msMatchesSelector(b);if(void 0!==c.webkitMatchesSelector)return c.webkitMatchesSelector(b);if(void 0!==c.mozMatchesSelector)return c.mozMatchesSelector(b);throw new e("Browser lacks native selectors")},j=function(a){return a.nodeType!==g&&a.nodeType!==h||0===a.childElementCount},k=function(b,d){var e=void 0===d?f:d.dom();return j(e)?[]:a.map(e.querySelectorAll(b),c.fromDom)},l=function(a,d){var e=void 0===d?f:d.dom();return j(e)?b.none():b.from(e.querySelector(a)).map(c.fromDom)};return{all:k,is:i,one:l}}),g("24",[],function(){var a=function(a,b){var c=[],d=function(a){return c.push(a),b(a)},e=b(a);do e=e.bind(d);while(e.isSome());return c};return{toArray:a}}),g("5t",[],function(){var a="undefined"!=typeof window?window:Function("return this;")();return a}),g("4y",["5t"],function(a){var b=function(b,c){for(var d=void 0!==c?c:a,e=0;e<b.length&&void 0!==d&&null!==d;++e)d=d[b[e]];return d},c=function(a,c){var d=a.split(".");return b(d,c)},d=function(a,b){return void 0!==a[b]&&null!==a[b]||(a[b]={}),a[b]},e=function(b,c){for(var e=void 0!==c?c:a,f=0;f<b.length;++f)e=d(e,b[f]);return e},f=function(a,b){var c=a.split(".");return e(c,b)};return{path:b,resolve:c,forge:e,namespace:f}}),g("3s",["4y"],function(a){var b=function(b,c){return a.resolve(b,c)},c=function(a,c){var d=b(a,c);if(void 0===d)throw a+" not available on this browser";return d};return{getOrDie:c}}),g("25",["3s"],function(a){var b=function(){var b=a.getOrDie("Node");return b},c=function(a,b,c){return 0!==(a.compareDocumentPosition(b)&c)},d=function(a,d){return c(a,d,b().DOCUMENT_POSITION_PRECEDING)},e=function(a,d){return c(a,d,b().DOCUMENT_POSITION_CONTAINED_BY)};return{documentPositionPreceding:d,documentPositionContainedBy:e}}),g("3b",[],function(){var a=function(a){var b,c=!1;return function(){return c||(c=!0,b=a.apply(null,arguments)),b}};return{cached:a}}),h("67",Number),g("5u",["e","67","1s"],function(a,b,c){var d=function(a,b){for(var c=0;c<a.length;c++){var d=a[c];if(d.test(b))return d}},e=function(a,c){var e=d(a,c);if(!e)return{major:0,minor:0};var f=function(a){return b(c.replace(e,"$"+a))};return h(f(1),f(2))},f=function(a,b){var d=c(b).toLowerCase();return 0===a.length?g():e(a,d)},g=function(){return h(0,0)},h=function(a,b){return{major:a,minor:b}};return{nu:h,detect:f,unknown:g}}),g("4z",["f","5u"],function(a,b){var c="Edge",d="Chrome",e="IE",f="Opera",g="Firefox",h="Safari",i=function(a,b){return function(){return b===a}},j=function(){return k({current:void 0,version:b.unknown()})},k=function(a){var b=a.current,j=a.version;return{current:b,version:j,isEdge:i(c,b),isChrome:i(d,b),isIE:i(e,b),isOpera:i(f,b),isFirefox:i(g,b),isSafari:i(h,b)}};return{unknown:j,nu:k,edge:a.constant(c),chrome:a.constant(d),ie:a.constant(e),opera:a.constant(f),firefox:a.constant(g),safari:a.constant(h)}}),g("50",["f","5u"],function(a,b){var c="Windows",d="iOS",e="Android",f="Linux",g="OSX",h="Solaris",i="FreeBSD",j=function(a,b){return function(){return b===a}},k=function(){return l({current:void 0,version:b.unknown()})},l=function(a){var b=a.current,k=a.version;return{current:b,version:k,isWindows:j(c,b),isiOS:j(d,b),isAndroid:j(e,b),isOSX:j(g,b),isLinux:j(f,b),isSolaris:j(h,b),isFreeBSD:j(i,b)}};return{unknown:k,nu:l,windows:a.constant(c),ios:a.constant(d),android:a.constant(e),linux:a.constant(f),osx:a.constant(g),solaris:a.constant(h),freebsd:a.constant(i)}}),g("51",["f"],function(a){return function(b,c,d){var e=b.isiOS()&&/ipad/i.test(d)===!0,f=b.isiOS()&&!e,g=b.isAndroid()&&3===b.version.major,h=b.isAndroid()&&4===b.version.major,i=e||g||h&&/mobile/i.test(d)===!0,j=b.isiOS()||b.isAndroid(),k=j&&!i,l=c.isSafari()&&b.isiOS()&&/safari/i.test(d)===!1;return{isiPad:a.constant(e),isiPhone:a.constant(f),isTablet:a.constant(i),isPhone:a.constant(k),isTouch:a.constant(j),isAndroid:b.isAndroid,isiOS:b.isiOS,isWebView:a.constant(l)}}}),g("52",["e","5u","1s"],function(a,b,c){var d=function(b,d){var e=c(d).toLowerCase();return a.find(b,function(a){return a.search(e)})},e=function(a,c){return d(a,c).map(function(a){var d=b.detect(a.versionRegexes,c);return{current:a.name,version:d}})},f=function(a,c){return d(a,c).map(function(a){var d=b.detect(a.versionRegexes,c);return{current:a.name,version:d}})};return{detectBrowser:e,detectOs:f}}),g("54",[],function(){var a=function(a,b){return b+a},b=function(a,b){return a+b},c=function(a,b){return a.substring(b)},d=function(a,b){return a.substring(0,a.length-b)};return{addToStart:a,addToEnd:b,removeFromStart:c,removeFromEnd:d}}),g("55",["g","1q"],function(a,b){var c=function(a,b){return a.substr(0,b)},d=function(a,b){return a.substr(a.length-b,a.length)},e=function(b){return""===b?a.none():a.some(b.substr(0,1))},f=function(b){return""===b?a.none():a.some(b.substring(1))};return{first:c,last:d,head:e,tail:f}}),g("3x",["54","55","1q"],function(a,b,c){var d=function(a,b,c){if(""===b)return!0;if(a.length<b.length)return!1;var d=a.substr(c,c+b.length);return d===b},e=function(a,b){var c=function(a){var b=typeof a;return"string"===b||"number"===b};return a.replace(/\${([^{}]*)}/g,function(a,d){var e=b[d];return c(e)?e:a})},f=function(b,c){return l(b,c)?a.removeFromStart(b,c.length):b},g=function(b,c){return m(b,c)?a.removeFromEnd(b,c.length):b},h=function(b,c){return l(b,c)?b:a.addToStart(b,c)},i=function(b,c){return m(b,c)?b:a.addToEnd(b,c)},j=function(a,b){return a.indexOf(b)!==-1},k=function(a){return b.head(a).bind(function(c){return b.tail(a).map(function(a){return c.toUpperCase()+a})}).getOr(a)},l=function(a,b){return d(a,b,0)},m=function(a,b){return d(a,b,a.length-b.length)},n=function(a){return a.replace(/^\s+|\s+$/g,"")},o=function(a){return a.replace(/^\s+/g,"")},p=function(a){return a.replace(/\s+$/g,"")};return{supplant:e,startsWith:l,removeLeading:f,removeTrailing:g,ensureLeading:h,ensureTrailing:i,endsWith:m,contains:j,trim:n,lTrim:o,rTrim:p,capitalize:k}}),g("53",["f","3x"],function(a,b){var c=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,d=function(a){return function(c){return b.contains(c,a)}},e=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(a){var c=b.contains(a,"edge/")&&b.contains(a,"chrome")&&b.contains(a,"safari")&&b.contains(a,"applewebkit");return c}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,c],search:function(a){return b.contains(a,"chrome")&&!b.contains(a,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(a){return b.contains(a,"msie")||b.contains(a,"trident")}},{name:"Opera",versionRegexes:[c,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:d("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:d("firefox")},{name:"Safari",versionRegexes:[c,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(a){return(b.contains(a,"safari")||b.contains(a,"mobile/"))&&b.contains(a,"applewebkit")}}],f=[{name:"Windows",search:d("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(a){return b.contains(a,"iphone")||b.contains(a,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:d("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:d("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:d("linux"),versionRegexes:[]},{name:"Solaris",search:d("sunos"),versionRegexes:[]},{name:"FreeBSD",search:d("freebsd"),versionRegexes:[]}];return{browsers:a.constant(e),oses:a.constant(f)}}),g("3t",["4z","50","51","52","53"],function(a,b,c,d,e){var f=function(f){var g=e.browsers(),h=e.oses(),i=d.detectBrowser(g,f).fold(a.unknown,a.nu),j=d.detectOs(h,f).fold(b.unknown,b.nu),k=c(j,i,f);return{browser:i,os:j,deviceType:k}};return{detect:f}}),h("3u",navigator),g("26",["3b","3t","3u"],function(a,b,c){var d=a.cached(function(){var a=c.userAgent;return b.detect(a)});return{detect:d}}),g("1c",["e","f","25","26","27"],function(a,b,c,d,e){var f=function(a,b){return a.dom()===b.dom()},g=function(a,b){return a.dom().isEqualNode(b.dom())},h=function(c,d){return a.exists(d,b.curry(f,c))},i=function(a,b){var c=a.dom(),d=b.dom();return c!==d&&c.contains(d)},j=function(a,b){return c.documentPositionContainedBy(a.dom(),b.dom())},k=d.detect().browser,l=k.isIE()?j:i;return{eq:f,isEqualNode:g,member:h,contains:l,is:e.is}}),g("1m",["1z","e","f","g","1k","24","1c","l"],function(a,b,c,d,e,f,g,h){var i=function(a){return h.fromDom(a.dom().ownerDocument)},j=function(a){var b=i(a);return h.fromDom(b.dom().documentElement)},k=function(a){var b=a.dom(),c=b.ownerDocument.defaultView;return h.fromDom(c)},l=function(a){var b=a.dom();return d.from(b.parentNode).map(h.fromDom)},m=function(a){return l(a).bind(function(c){var d=u(c);return b.findIndex(d,function(b){return g.eq(a,b)})})},n=function(b,d){for(var e=a.isFunction(d)?d:c.constant(!1),f=b.dom(),g=[];null!==f.parentNode&&void 0!==f.parentNode;){var i=f.parentNode,j=h.fromDom(i);if(g.push(j),e(j)===!0)break;f=i}return g},o=function(a){var c=function(c){return b.filter(c,function(b){return!g.eq(a,b)})};return l(a).map(u).map(c).getOr([])},p=function(a){var b=a.dom();return d.from(b.offsetParent).map(h.fromDom)},q=function(a){var b=a.dom();return d.from(b.previousSibling).map(h.fromDom)},r=function(a){var b=a.dom();return d.from(b.nextSibling).map(h.fromDom)},s=function(a){return b.reverse(f.toArray(a,q))},t=function(a){return f.toArray(a,r)},u=function(a){var c=a.dom();return b.map(c.childNodes,h.fromDom)},v=function(a,b){var c=a.dom().childNodes;return d.from(c[b]).map(h.fromDom)},w=function(a){return v(a,0)},x=function(a){return v(a,a.dom().childNodes.length-1)},y=function(a){return a.dom().childNodes.length},z=function(a){return a.dom().hasChildNodes()},A=e.immutable("element","offset"),B=function(a,b){var c=u(a);return c.length>0&&b<c.length?A(c[b],0):A(a,b)};return{owner:i,defaultView:k,documentElement:j,parent:l,findIndex:m,parents:n,siblings:o,prevSibling:q,offsetParent:p,prevSiblings:s,nextSibling:r,nextSiblings:t,children:u,child:v,firstChild:w,lastChild:x,childNodesCount:y,hasChildNodes:z,leaf:B}}),g("1y",["e","f","27","1m"],function(a,b,c,d){var e=function(a,c){return f(a,c,b.constant(!0))},f=function(b,e,g){return a.bind(d.children(b),function(a){return c.is(a,e)?g(a)?[a]:[]:f(a,e,g)})};return{firstLayer:e,filterFirstLayer:f}}),g("n",["20"],function(a){var b=function(a){var b=a.dom().nodeName;return b.toLowerCase()},c=function(a){return a.dom().nodeType},d=function(a){return a.dom().nodeValue},e=function(a){return function(b){return c(b)===a}},f=function(d){return c(d)===a.COMMENT||"#comment"===b(d)},g=e(a.ELEMENT),h=e(a.TEXT),i=e(a.DOCUMENT);return{name:b,type:c,value:d,isElement:g,isText:h,isDocument:i,isComment:f}}),g("r",["1z","e","1t","n","1q","21"],function(a,b,c,d,e,f){var g=function(b,c,d){if(!(a.isString(d)||a.isBoolean(d)||a.isNumber(d)))throw f.error("Invalid call to Attr.set. Key ",c,":: Value ",d,":: Element ",b),new e("Attribute value was not simple");b.setAttribute(c,d+"")},h=function(a,b,c){g(a.dom(),b,c)},i=function(a,b){var d=a.dom();c.each(b,function(a,b){g(d,b,a)})},j=function(a,b){var c=a.dom().getAttribute(b);return null===c?void 0:c},k=function(a,b){var c=a.dom();return!(!c||!c.hasAttribute)&&c.hasAttribute(b)},l=function(a,b){a.dom().removeAttribute(b)},m=function(a){var b=a.dom().attributes;return void 0===b||null===b||0===b.length},n=function(a){return b.foldl(a.dom().attributes,function(a,b){return a[b.name]=b.value,a},{})},o=function(a,b,c){k(a,c)&&!k(b,c)&&h(b,c,j(a,c))},p=function(a,c,e){d.isElement(a)&&d.isElement(c)&&b.each(e,function(b){o(a,c,b)})};return{clone:n,set:h,setAll:i,get:j,has:k,remove:l,hasNone:m,transfer:p}}),g("35",["3b","l","n","23"],function(a,b,c,d){var e=function(a){var b=c.isText(a)?a.dom().parentNode:a.dom();return void 0!==b&&null!==b&&b.ownerDocument.body.contains(b)},f=a.cached(function(){return g(b.fromDom(d))}),g=function(a){var c=a.dom().body;if(null===c||void 0===c)throw"Body is not available yet";return b.fromDom(c)};return{body:f,getBody:g,inBody:e}}),g("29",["e","35","1m"],function(a,b,c){var d=function(a){return h(b.body(),a)},e=function(b,d,e){return a.filter(c.parents(b,e),d)},f=function(b,d){return a.filter(c.siblings(b),d)},g=function(b,d){return a.filter(c.children(b),d)},h=function(b,d){var e=[];return a.each(c.children(b),function(a){d(a)&&(e=e.concat([a])),e=e.concat(h(a,d))}),e};return{all:d,ancestors:e,siblings:f,children:g,descendants:h}}),g("y",["29","27"],function(a,b){var c=function(a){return b.all(a)},d=function(c,d,e){return a.ancestors(c,function(a){return b.is(a,d)},e)},e=function(c,d){return a.siblings(c,function(a){return b.is(a,d)})},f=function(c,d){return a.children(c,function(a){return b.is(a,d)})},g=function(a,c){return b.all(c,a)};return{all:c,ancestors:d,siblings:e,children:f,descendants:g}}),g("2d",["1z","g"],function(a,b){return function(c,d,e,f,g){return c(e,f)?b.some(e):a.isFunction(g)&&g(e)?b.none():d(e,f,g)}}),g("2a",["1z","e","f","g","35","1c","l","2d"],function(a,b,c,d,e,f,g,h){var i=function(a){return n(e.body(),a)},j=function(b,e,f){for(var h=b.dom(),i=a.isFunction(f)?f:c.constant(!1);h.parentNode;){h=h.parentNode;var j=g.fromDom(h);if(e(j))return d.some(j);if(i(j))break}return d.none()},k=function(a,b,c){var d=function(a){return b(a)};return h(d,j,a,b,c)},l=function(a,b){var c=a.dom();return c.parentNode?m(g.fromDom(c.parentNode),function(c){return!f.eq(a,c)&&b(c)}):d.none()},m=function(a,d){var e=b.find(a.dom().childNodes,c.compose(d,g.fromDom));return e.map(g.fromDom)},n=function(a,b){var c=function(a){for(var e=0;e<a.childNodes.length;e++){if(b(g.fromDom(a.childNodes[e])))return d.some(g.fromDom(a.childNodes[e]));var f=c(a.childNodes[e]);if(f.isSome())return f}return d.none()};return c(a.dom())};return{first:i,ancestor:j,closest:k,sibling:l,child:m,descendant:n}}),g("t",["2a","27","2d"],function(a,b,c){var d=function(a){return b.one(a)},e=function(c,d,e){return a.ancestor(c,function(a){return b.is(a,d)},e)},f=function(c,d){return a.sibling(c,function(a){return b.is(a,d)})},g=function(c,d){return a.child(c,function(a){return b.is(a,d)})},h=function(a,c){return b.one(c,a)},i=function(a,d,f){return c(b.is,e,a,d,f)};return{first:d,ancestor:e,sibling:f,child:g,descendant:h,closest:i}}),h("2e",parseInt),g("j",["e","f","g","2c","1y","r","n","y","t","27","1m","2e"],function(a,b,c,d,e,f,g,h,i,j,k,l){var m=function(d,e,f){var h=void 0!==f?f:b.constant(!1);if(h(e))return c.none();if(a.contains(d,g.name(e)))return c.some(e);var k=function(a){return j.is(a,"table")||h(a)};return i.ancestor(e,d.join(","),k)},n=function(a,b){return m(["td","th"],a,b)},o=function(a){return e.firstLayer(a,"th,td")},p=function(a,b){return m(["caption","tr","tbody","tfoot","thead"],a,b)},q=function(a,b){return k.parent(b).map(function(b){return h.children(b,a)})},r=b.curry(q,"th,td"),s=b.curry(q,"tr"),t=function(a){return i.descendant(a,"th,td")},u=function(a,b){return i.closest(a,"table",b)},v=function(a,b){return m(["tr"],a,b)},w=function(a){return e.firstLayer(a,"tr")},x=function(a,b){return l(f.get(a,b),10)},y=function(a,b,c){var e=x(a,b),f=x(a,c);return d.grid(e,f)};return{cell:n,firstCell:t,cells:o,neighbourCells:r,table:u,row:v,rows:w,notCell:p,neighbourRows:s,attr:x,grid:y}}),g("1w",["e","2c","j","r","n","1m"],function(a,b,c,d,e,f){var g=function(g){var h=c.rows(g);return a.map(h,function(g){var h=g,i=f.parent(h),j=i.bind(function(a){var b=e.name(a);return"tfoot"===b||"thead"===b||"tbody"===b?b:"tbody"}),k=a.map(c.cells(g),function(a){var c=d.has(a,"rowspan")?parseInt(d.get(a,"rowspan"),10):1,e=d.has(a,"colspan")?parseInt(d.get(a,"colspan"),10):1;return b.detail(a,c,e)});return b.rowdata(h,k,j)})},h=function(e,f){return a.map(e,function(e){var g=a.map(c.cells(e),function(a){var c=d.has(a,"rowspan")?parseInt(d.get(a,"rowspan"),10):1,e=d.has(a,"colspan")?parseInt(d.get(a,"colspan"),10):1;return b.detail(a,c,e)});return b.rowdata(e,g,f.section())})};return{fromTable:g,fromPastedRows:h}}),h("3v",Math),g("1x",["e","f","g","2c","3v"],function(a,b,c,d,e){var f=function(a,b){return a+","+b},g=function(a,b,d){var e=a.access()[f(b,d)];return void 0!==e?c.some(e):c.none()},h=function(a,b,d){var e=i(a,function(a){return d(b,a.element())});return e.length>0?c.some(e[0]):c.none()},i=function(b,c){var d=a.bind(b.all(),function(a){return a.cells()});return a.filter(d,c)},j=function(c){var g={},h=[],i=c.length,j=0;a.each(c,function(b,c){var i=[];a.each(b.cells(),function(a,b){for(var h=0;void 0!==g[f(c,h)];)h++;for(var k=d.extended(a.element(),a.rowspan(),a.colspan(),c,h),l=0;l<a.colspan();l++)for(var m=0;m<a.rowspan();m++){var n=c+m,o=h+l,p=f(n,o);g[p]=k,j=e.max(j,o+1)}i.push(k)}),h.push(d.rowdata(b.element(),i,b.section()))});var k=d.grid(i,j);return{grid:b.constant(k),access:b.constant(g),all:b.constant(h)}},k=function(b){var c=a.map(b.all(),function(a){return a.cells()});return a.flatten(c)};return{generate:j,getAt:g,findItem:h,filterItems:i,justCells:k}}),g("3w",[],function(){var a=function(a){return void 0!==a.style};return{isSupported:a}}),h("3y",window),g("22",["1z","e","1t","g","r","35","l","n","3w","3x","1q","21","3y"],function(a,b,c,d,e,f,g,h,i,j,k,l,m){var n=function(b,c,d){if(!a.isString(d))throw l.error("Invalid call to CSS.set. Property ",c,":: Value ",d,":: Element ",b),new k("CSS value must be a string: "+d);i.isSupported(b)&&b.style.setProperty(c,d)},o=function(a,b){i.isSupported(a)&&a.style.removeProperty(b)},p=function(a,b,c){var d=a.dom();n(d,b,c)},q=function(a,b){var d=a.dom();c.each(b,function(a,b){n(d,b,a)})},r=function(a,b){var d=a.dom();c.each(b,function(a,b){a.fold(function(){o(d,b)},function(a){n(d,b,a)})})},s=function(a,b){var c=a.dom(),d=m.getComputedStyle(c),e=d.getPropertyValue(b),g=""!==e||f.inBody(a)?e:t(c,b);return null===g?void 0:g},t=function(a,b){return i.isSupported(a)?a.style.getPropertyValue(b):""},u=function(a,b){var c=a.dom(),e=t(c,b);return d.from(e).filter(function(a){return a.length>0})},v=function(a){var b={},c=a.dom();if(i.isSupported(c))for(var d=0;d<c.style.length;d++){var e=c.style.item(d);b[e]=c.style[e]}return b},w=function(a,b,c){var d=g.fromTag(a);p(d,b,c);var e=u(d,b);return e.isSome()},x=function(a,b){var c=a.dom();o(c,b),e.has(a,"style")&&""===j.trim(e.get(a,"style"))&&e.remove(a,"style")},y=function(a,b){var c=e.get(a,"style"),d=b(a),f=void 0===c?e.remove:e.set;return f(a,"style",c),d},z=function(a,b){var c=a.dom(),d=b.dom();i.isSupported(c)&&i.isSupported(d)&&(d.style.cssText=c.style.cssText)},A=function(a){return a.dom().offsetWidth},B=function(a,b,c){u(a,c).each(function(a){u(b,c).isNone()&&p(b,c,a)})},C=function(a,c,d){h.isElement(a)&&h.isElement(c)&&b.each(d,function(b){B(a,c,b)})};return{copy:z,set:p,preserve:y,setAll:q,setOptions:r,remove:x,get:s,getRaw:u,getAllRaw:v,isValidValue:w,reflow:A,transfer:C}}),g("12",["1m"],function(a){var b=function(b,c){var d=a.parent(b);d.each(function(a){a.dom().insertBefore(c.dom(),b.dom())})},c=function(c,d){var f=a.nextSibling(c);f.fold(function(){var b=a.parent(c);b.each(function(a){e(a,d)})},function(a){b(a,d)})},d=function(b,c){var d=a.firstChild(b);d.fold(function(){e(b,c)},function(a){b.dom().insertBefore(c.dom(),a.dom())})},e=function(a,b){a.dom().appendChild(b.dom())},f=function(c,d,f){a.child(c,f).fold(function(){e(c,d)},function(a){b(a,d)})},g=function(a,c){b(a,c),e(c,a)};return{before:b,after:c,prepend:d,append:e,appendAt:f,wrap:g}}),g("28",["e","12"],function(a,b){var c=function(c,d){a.each(d,function(a){b.before(c,a)})},d=function(c,d){a.each(d,function(a,e){var f=0===e?c:d[e-1];b.after(f,a)})},e=function(c,d){a.each(d.slice().reverse(),function(a){b.prepend(c,a)})},f=function(c,d){a.each(d,function(a){b.append(c,a)})};return{before:c,after:d,prepend:e,append:f}}),g("13",["e","28","1m"],function(a,b,c){var d=function(b){b.dom().textContent="",a.each(c.children(b),function(a){e(a)})},e=function(a){var b=a.dom();null!==b.parentNode&&b.parentNode.removeChild(b)},f=function(a){var d=c.children(a);d.length>0&&b.before(a,d),e(a)};return{empty:d,remove:e,unwrap:f}}),g("h",["e","1t","1k","1w","1x","1y","r","22","l","12","13","27"],function(a,b,c,d,e,f,g,h,i,j,k,l){var m=c.immutable("minRow","minCol","maxRow","maxCol"),n=function(a,c){var d=a.grid().columns(),e=a.grid().rows(),f=e,g=d,h=0,i=0;return b.each(a.access(),function(a){if(c(a)){var b=a.row(),d=b+a.rowspan()-1,e=a.column(),j=e+a.colspan()-1;b<f?f=b:d>h&&(h=d),e<g?g=e:j>i&&(i=j)}}),m(f,g,h,i)},o=function(a,b,c){var d=a[c].element(),e=i.fromTag("td");j.append(e,i.fromTag("br"));var f=b?j.append:j.prepend;f(d,e)},p=function(a,b,c,d){for(var f=b.grid().columns(),g=b.grid().rows(),h=0;h<g;h++)for(var i=!1,j=0;j<f;j++)if(!(h<c.minRow()||h>c.maxRow()||j<c.minCol()||j>c.maxCol())){var k=e.getAt(b,h,j).filter(d).isNone();k?o(a,i,h):i=!0}},q=function(b,c){var d=a.filter(f.firstLayer(b,"tr"),function(a){return 0===a.dom().childElementCount});a.each(d,k.remove),c.minCol()!==c.maxCol()&&c.minRow()!==c.maxRow()||a.each(f.firstLayer(b,"th,td"),function(a){g.remove(a,"rowspan"),g.remove(a,"colspan")}),g.remove(b,"width"),g.remove(b,"height"),h.remove(b,"width"),h.remove(b,"height")},r=function(b,c){var g=function(a){return l.is(a.element(),c)},h=d.fromTable(b),i=e.generate(h),j=n(i,g),m="th:not("+c+"),td:not("+c+")",o=f.filterFirstLayer(b,"th,td",function(a){return l.is(a,m)});return a.each(o,k.remove),p(h,i,j,g),
q(b,j),b};return{extract:r}}),g("k",["r","l","12","28","13","1m"],function(a,b,c,d,e,f){var g=function(a,c){return b.fromDom(a.dom().cloneNode(c))},h=function(a){return g(a,!1)},i=function(a){return g(a,!0)},j=function(c,d){var e=b.fromTag(d),f=a.clone(c);return a.setAll(e,f),e},k=function(a,b){var c=j(a,b),e=f.children(i(a));return d.append(c,e),c},l=function(a,b){var g=j(a,b);c.before(a,g);var h=f.children(a);return d.append(g,h),e.remove(a),g};return{shallow:h,shallowAs:j,deep:i,copy:k,mutate:l}}),g("3p",["26","g","1q"],function(a,b,c){return function(d,e){var f=function(a){if(!d(a))throw new c("Can only get "+e+" value of a "+e+" node");return j(a).getOr("")},g=function(a){try{return h(a)}catch(a){return b.none()}},h=function(a){return d(a)?b.from(a.dom().nodeValue):b.none()},i=a.detect().browser,j=i.isIE()&&10===i.version.major?g:h,k=function(a,b){if(!d(a))throw new c("Can only set raw "+e+" value of a "+e+" node");a.dom().nodeValue=b};return{get:f,getOption:j,set:k}}}),g("1l",["n","3p"],function(a,b){var c=b(a.isText,"text"),d=function(a){return c.get(a)},e=function(a){return c.getOption(a)},f=function(a,b){c.set(a,b)};return{get:d,getOption:e,set:f}}),g("2b",["e","n","1l","1m"],function(a,b,c,d){var e=function(a){return"img"===b.name(a)?1:c.getOption(a).fold(function(){return d.children(a).length},function(a){return a.length})},f=function(a,b){return e(a)===b},g=function(a,b){return 0===b},h="\xa0",i=function(a){return c.getOption(a).filter(function(a){return 0!==a.trim().length||a.indexOf(h)>-1}).isSome()},j=["img","br"],k=function(c){var d=i(c);return d||a.contains(j,b.name(c))};return{getEnd:e,isEnd:f,isStart:g,isCursorPosition:k}}),g("1d",["g","2a","1m","2b"],function(a,b,c,d){var e=function(a){return b.descendant(a,d.isCursorPosition)},f=function(a){return g(a,d.isCursorPosition)},g=function(b,d){var e=function(b){for(var f=c.children(b),g=f.length-1;g>=0;g--){var h=f[g];if(d(h))return a.some(h);var i=e(h);if(i.isSome())return i}return a.none()};return e(b)};return{first:e,last:f}}),g("i",["e","1t","1c","12","k","l","n","r","22","y","1m","1d"],function(a,b,c,d,e,f,g,h,i,j,k,l){var m=function(){var a=f.fromTag("td");return d.append(a,f.fromTag("br")),a},n=function(a,c,d){var f=e.copy(a,c);return b.each(d,function(a,b){null===a?h.remove(f,b):h.set(f,b,a)}),f},o=function(a){return a},p=function(a){return function(){return f.fromTag("tr",a.dom())}},q=function(b,f,g){var h=l.first(b);return h.map(function(h){var i=g.join(","),k=j.ancestors(h,i,function(a){return c.eq(a,b)});return a.foldr(k,function(a,b){var c=e.shallow(b);return d.append(a,c),c},f)}).getOr(f)},r=function(a,b,c){var e=function(b){var e=k.owner(b.element()),h=f.fromTag(g.name(b.element()),e.dom()),j=c.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),l=j.length>0?q(b.element(),h,j):h;return d.append(l,f.fromTag("br")),i.copy(b.element(),h),i.remove(h,"height"),1!==b.colspan()&&i.remove(b.element(),"width"),a(b.element(),h),h};return{row:p(b),cell:e,replace:n,gap:m}},s=function(a){return{row:p(a),cell:m,replace:o,gap:m}};return{cellOperations:r,paste:s}}),g("m",["e","l","1m","23"],function(a,b,c,d){var e=function(a,e){var f=e||d,g=f.createElement("div");return g.innerHTML=a,c.children(b.fromDom(g))},f=function(c,d){return a.map(c,function(a){return b.fromTag(a,d)})},g=function(c,d){return a.map(c,function(a){return b.fromText(a,d)})},h=function(c){return a.map(c,b.fromDom)};return{fromHtml:e,fromTags:f,fromText:g,fromDom:h}}),g("5v",[],function(){return["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"]}),g("56",["5v","e","f","r","1c","22","l","12","28","n","29","2a","13","y","t","1l","1m"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q){return function(){var r=function(a){return g.fromDom(a.dom().cloneNode(!1))},s=function(c){return!!j.isElement(c)&&("body"===j.name(c)||b.contains(a,j.name(c)))},t=function(a){return!!j.isElement(a)&&b.contains(["br","img","hr","input"],j.name(a))},u=function(a,b){return a.dom().compareDocumentPosition(b.dom())},v=function(a,b){var c=d.clone(a);d.setAll(b,c)};return{up:c.constant({selector:o.ancestor,closest:o.closest,predicate:l.ancestor,all:q.parents}),down:c.constant({selector:n.descendants,predicate:k.descendants}),styles:c.constant({get:f.get,getRaw:f.getRaw,set:f.set,remove:f.remove}),attrs:c.constant({get:d.get,set:d.set,remove:d.remove,copyTo:v}),insert:c.constant({before:h.before,after:h.after,afterAll:i.after,append:h.append,appendAll:i.append,prepend:h.prepend,wrap:h.wrap}),remove:c.constant({unwrap:m.unwrap,remove:m.remove}),create:c.constant({nu:g.fromTag,clone:r,text:g.fromText}),query:c.constant({comparePosition:u,prevSibling:q.prevSibling,nextSibling:q.nextSibling}),property:c.constant({children:q.children,name:j.name,parent:q.parent,isText:j.isText,isComment:j.isComment,isElement:j.isElement,getText:p.get,setText:p.set,isBoundary:s,isEmptyTag:t}),eq:e.eq,is:e.is}}}),g("5w",["e","f","g","1k"],function(a,b,c,d){var e=d.immutable("left","right"),f=function(c,d,e){var f=c.property().children(d),g=a.findIndex(f,b.curry(c.eq,e));return g.map(function(a){return{before:b.constant(f.slice(0,a)),after:b.constant(f.slice(a+1))}})},g=function(a,b,c){return f(a,b,c).map(function(c){var d=a.create().clone(b);return a.insert().appendAll(d,c.after()),a.insert().after(b,d),e(b,d)})},h=function(a,b,c){return f(a,b,c).map(function(d){var f=a.create().clone(b);return a.insert().appendAll(f,d.before().concat([c])),a.insert().appendAll(b,d.after()),a.insert().before(b,f),e(f,b)})},i=function(a,b,e,f){var g=d.immutable("first","second","splits"),h=function(b,d,i){var j=g(b,c.none(),i);return e(b)?g(b,d,i):a.property().parent(b).bind(function(d){return f(a,d,b).map(function(a){var b=[{first:a.left,second:a.right}],f=e(d)?d:a.left();return h(f,c.some(a.right()),i.concat(b))}).getOr(j)})};return h(b,c.none(),[])};return{breakToLeft:h,breakToRight:g,breakPath:i}}),g("5x",["e","f","g"],function(a,b,c){var d=function(a,b,c,d){var e=c[0],f=c.slice(1);return d(a,b,e,f)},e=function(a,b,e){return e.length>0?d(a,b,e,f):c.none()},f=function(b,c,d,e){var f=c(b,d);return a.foldr(e,function(a,d){var e=c(b,d);return g(b,a,e)},f)},g=function(a,c,d){return c.bind(function(c){return d.filter(b.curry(a.eq,c))})};return{oneAll:e}}),g("5y",["e","f","g","3v"],function(a,b,c,d){var e=function(a,c){return b.curry(a.eq,c)},f=function(b,f,g,h){var i=b.property().children(f);if(b.eq(f,g[0]))return c.some([g[0]]);if(b.eq(f,h[0]))return c.some([h[0]]);var j=function(c){var d=a.reverse(c),g=a.findIndex(d,e(b,f)).getOr(-1),h=g<d.length-1?d[g+1]:d[g];return a.findIndex(i,e(b,h))},k=j(g),l=j(h);return k.bind(function(a){return l.map(function(b){var c=d.min(a,b),e=d.max(a,b);return i.slice(c,e+1)})})},g=function(c,d,f,g){var h=void 0!==g?g:b.constant(!1),i=[d].concat(c.up().all(d)),j=[f].concat(c.up().all(f)),k=function(b){var c=a.findIndex(b,h);return c.fold(function(){return b},function(a){return b.slice(0,a+1)})},l=k(i),m=k(j),n=a.find(l,function(b){return a.exists(m,e(c,b))});return{firstpath:b.constant(l),secondpath:b.constant(m),shared:b.constant(n)}},h=function(a,b,c){var d=g(a,b,c);return d.shared().bind(function(b){return f(a,b,d.firstpath(),d.secondpath())})};return{subset:h,ancestors:g}}),g("57",["5w","5x","5y"],function(a,b,c){var d=function(a,c,d){return b.oneAll(a,c,d)},e=function(a,b,d){return c.subset(a,b,d)},f=function(a,b,d,e){return c.ancestors(a,b,d,e)},g=function(b,c,d){return a.breakToLeft(b,c,d)},h=function(b,c,d){return a.breakToRight(b,c,d)},i=function(b,c,d,e){return a.breakPath(b,c,d,e)};return{sharedOne:d,subset:e,ancestors:f,breakToLeft:g,breakToRight:h,breakPath:i}}),g("3z",["56","57"],function(a,b){var c=a(),d=function(a,d){return b.sharedOne(c,function(b,c){return a(c)},d)},e=function(a,d){return b.subset(c,a,d)},f=function(a,d,e){return b.ancestors(c,a,d,e)},g=function(a,d){return b.breakToLeft(c,a,d)},h=function(a,d){return b.breakToRight(c,a,d)},i=function(a,d,e){return b.breakPath(c,a,d,function(a,b,c){return e(b,c)})};return{sharedOne:d,subset:e,ancestors:f,breakToLeft:g,breakToRight:h,breakPath:i}}),g("58",["f","g","1x"],function(a,b,c){var d=function(a,b){var c=b.column(),d=b.column()+b.colspan()-1,e=b.row(),f=b.row()+b.rowspan()-1;return c<=a.finishCol()&&d>=a.startCol()&&e<=a.finishRow()&&f>=a.startRow()},e=function(a,b){return b.column()>=a.startCol()&&b.column()+b.colspan()-1<=a.finishCol()&&b.row()>=a.startRow()&&b.row()+b.rowspan()-1<=a.finishRow()},f=function(d,f){for(var g=!0,h=a.curry(e,f),i=f.startRow();i<=f.finishRow();i++)for(var j=f.startCol();j<=f.finishCol();j++)g=g&&c.getAt(d,i,j).exists(h);return g?b.some(f):b.none()};return{inSelection:d,isWithin:e,isRectangular:f}}),g("41",["2c","1x","58","1c","3v"],function(a,b,c,d,e){var f=function(b,c){return a.bounds(e.min(b.row(),c.row()),e.min(b.column(),c.column()),e.max(b.row()+b.rowspan()-1,c.row()+c.rowspan()-1),e.max(b.column()+b.colspan()-1,c.column()+c.colspan()-1))},g=function(a,c,e){var g=b.findItem(a,c,d.eq),h=b.findItem(a,e,d.eq);return g.bind(function(a){return h.map(function(b){return f(a,b)})})},h=function(a,b,d){return g(a,b,d).bind(function(b){return c.isRectangular(a,b)})};return{getAnyBox:g,getBox:h}}),g("40",["e","f","1x","58","41","1c"],function(a,b,c,d,e,f){var g=function(a,b,d,e){return c.findItem(a,b,f.eq).bind(function(b){var f=d>0?b.row()+b.rowspan()-1:b.row(),g=e>0?b.column()+b.colspan()-1:b.column(),h=c.getAt(a,f+d,g+e);return h.map(function(a){return a.element()})})},h=function(f,g,h){return e.getAnyBox(f,g,h).map(function(e){var g=c.filterItems(f,b.curry(d.inSelection,e));return a.map(g,function(a){return a.element()})})},i=function(a,b){var d=function(a,b){return f.contains(b,a)};return c.findItem(a,b,d).bind(function(a){return a.element()})};return{moveBy:g,intercepts:h,parentCell:i}}),g("3q",["j","1w","1x","40","41","1c"],function(a,b,c,d,e,f){var g=function(b,c,e){return a.table(b).bind(function(a){var f=k(a);return d.moveBy(f,b,c,e)})},h=function(a,b,c){var e=k(a);return d.intercepts(e,b,c)},i=function(a,b,c,e,g){var h=k(a),i=f.eq(a,c)?b:d.parentCell(h,b),j=f.eq(a,g)?e:d.parentCell(h,e);return d.intercepts(h,i,j)},j=function(a,b,c){var d=k(a);return e.getBox(d,b,c)},k=function(a){var d=b.fromTable(a);return c.generate(d)};return{moveBy:g,intercepts:h,nestedIntercepts:i,getBox:j}}),g("3l",["e","f","g","3z","3q","1c","y","t","27"],function(a,b,c,d,e,f,g,h,i){var j=function(a,b){return h.ancestor(a,"table")},k=function(a,b,g){return f.eq(a,b)?c.some([a]):j(a,g).bind(function(c){return j(b,g).bind(function(i){return f.eq(c,i)?e.intercepts(c,a,b):f.contains(c,i)?e.nestedIntercepts(c,a,c,b,i):f.contains(i,c)?e.nestedIntercepts(i,a,c,b,i):d.ancestors(a,b).shared().bind(function(d){return h.closest(d,"table",g).bind(function(d){return e.nestedIntercepts(d,a,c,b,i)})})})})},l=function(a,b){var d=g.descendants(a,b);return d.length>0?c.some(d):c.none()},m=function(b,c){return a.find(b,function(a){return i.is(a,c)})},n=function(a,c,e){return h.descendant(a,c).bind(function(c){return h.descendant(a,e).bind(function(a){return d.sharedOne(j,[c,a]).map(function(d){return{first:b.constant(c),last:b.constant(a),table:b.constant(d)}})})})},o=function(a,c){return h.ancestor(a,"table").bind(function(d){return h.descendant(d,c).bind(function(c){return k(c,a).map(function(d){return{boxes:b.constant(d),start:b.constant(c),finish:b.constant(a)}})})})},p=function(a,b,c,d,f){return m(a,f).bind(function(a){return e.moveBy(a,b,c).bind(function(a){return o(a,d)})})};return{identify:k,retrieve:l,shiftSelection:p,getEdges:n}}),g("1o",["3l","g","3q","1c","t"],function(a,b,c,d,e){var f=function(b,c){return a.retrieve(b,c)},g=function(f,g,h){return a.getEdges(f,g,h).bind(function(a){var g=function(a){return d.eq(f,a)},h=e.ancestor(a.first(),"thead,tfoot,tbody,table",g),i=e.ancestor(a.last(),"thead,tfoot,tbody,table",g);return h.bind(function(e){return i.bind(function(f){return d.eq(e,f)?c.getBox(a.table(),a.first(),a.last()):b.none()})})})};return{retrieve:f,retrieveBox:g}}),g("9",["f"],function(a){var b="data-mce-selected",c="td["+b+"],th["+b+"]",d="["+b+"]",e="data-mce-first-selected",f="td["+e+"],th["+e+"]",g="data-mce-last-selected",h="td["+g+"],th["+g+"]";return{selected:a.constant(b),selectedSelector:a.constant(c),attributeSelector:a.constant(d),firstSelected:a.constant(e),firstSelectedSelector:a.constant(f),lastSelected:a.constant(g),lastSelectedSelector:a.constant(h)}}),g("2g",["e","1t","1z","1p","1q","21"],function(a,b,c,d,e,f){var g=function(g){if(!c.isArray(g))throw new e("cases must be an array");if(0===g.length)throw new e("there must be at least one case");var h=[],i={};return a.each(g,function(j,k){var l=b.keys(j);if(1!==l.length)throw new e("one and only one name per case");var m=l[0],n=j[m];if(void 0!==i[m])throw new e("duplicate key detected:"+m);if("cata"===m)throw new e("cannot have a case named cata (sorry)");if(!c.isArray(n))throw new e("case arguments must be an array");h.push(m),i[m]=function(){var c=arguments.length;if(c!==n.length)throw new e("Wrong number of arguments to case "+m+". Expected "+n.length+" ("+n+"), got "+c);for(var i=new d(c),j=0;j<i.length;j++)i[j]=arguments[j];var l=function(c){var d=b.keys(c);if(h.length!==d.length)throw new e("Wrong number of arguments to match. Expected: "+h.join(",")+"\nActual: "+d.join(","));var f=a.forall(h,function(b){return a.contains(d,b)});if(!f)throw new e("Not all branches were specified when using match. Specified: "+d.join(", ")+"\nRequired: "+h.join(", "));return c[m].apply(null,i)};return{fold:function(){if(arguments.length!==g.length)throw new e("Wrong number of arguments to fold. Expected "+g.length+", got "+arguments.length);var a=arguments[k];return a.apply(null,i)},match:l,log:function(a){f.log(a,{constructors:h,constructor:m,params:i})}}}}),i};return{generate:g}}),g("p",["2g"],function(a){var b=a.generate([{none:[]},{multiple:["elements"]},{single:["selection"]}]),c=function(a,b,c,d){return a.fold(b,c,d)};return{cata:c,none:b.none,multiple:b.multiple,single:b.single}}),g("2f",["1o","e","f","g","r","9","p"],function(a,b,c,d,e,f,g){var h=function(a,b){return g.cata(b.get(),c.constant([]),c.identity,c.constant([a]))},i=function(a,c){var f=function(a){return e.has(a,"rowspan")&&parseInt(e.get(a,"rowspan"),10)>1||e.has(a,"colspan")&&parseInt(e.get(a,"colspan"),10)>1},g=h(a,c);return g.length>0&&b.forall(g,f)?d.some(g):d.none()},j=function(b,e){return g.cata(e.get(),d.none,function(e,g){return 0===e.length?d.none():a.retrieveBox(b,f.firstSelectedSelector(),f.lastSelectedSelector()).bind(function(a){return e.length>1?d.some({bounds:c.constant(a),cells:c.constant(e)}):d.none()})},d.none)};return{mergable:j,unmergable:i,selection:h}}),g("o",["f","g","1k","2f"],function(a,b,c,d){var e=function(c){return{element:a.constant(c),mergable:b.none,unmergable:b.none,selection:a.constant([c])}},f=function(b,c,e){return{element:a.constant(e),mergable:a.constant(d.mergable(c,b)),unmergable:a.constant(d.unmergable(e,b)),selection:a.constant(d.selection(e,b))}},g=function(a){return e(a)},h=c.immutable("element","clipboard","generators"),i=function(c,e,f,g,h){return{element:a.constant(f),mergable:b.none,unmergable:b.none,selection:a.constant(d.selection(f,c)),clipboard:a.constant(g),generators:a.constant(h)}};return{noMenu:e,forMenu:f,notCell:g,paste:h,pasteRows:i}}),g("2",["e","f","g","h","i","j","k","l","m","n","o","9","p"],function(a,b,c,d,e,f,g,h,i,j,k,l,m){var n=function(a){return f.table(a[0]).map(g.deep).map(function(a){return[d.extract(a,l.attributeSelector())]})},o=function(a,b){return a.selection.serializer.serialize(b.dom(),{})},p=function(d,g,l,p){d.on("BeforeGetContent",function(c){var e=function(b){c.preventDefault(),n(b).each(function(b){c.content=a.map(b,function(a){return o(d,a)}).join("")})};c.selection===!0&&m.cata(g.get(),b.noop,e,b.noop)}),d.on("BeforeSetContent",function(b){if(b.selection===!0&&b.paste===!0){var g=c.from(d.dom.getParent(d.selection.getStart(),"th,td"));g.each(function(c){var g=h.fromDom(c),m=f.table(g);m.bind(function(c){var f=a.filter(i.fromHtml(b.content),function(a){return"meta"!==j.name(a)});if(1===f.length&&"table"===j.name(f[0])){b.preventDefault();var m=h.fromDom(d.getDoc()),n=e.paste(m),o=k.paste(g,f[0],n);l.pasteCells(c,o).each(function(a){d.selection.setRng(a),d.focus(),p.clear(c)})}})})}})};return{registerEvents:p}}),g("2h",["r","22","l","12","28"],function(a,b,c,d,e){var f=function(){return c.fromTag("table")},g=function(){return c.fromTag("tbody")},h=function(){return c.fromTag("tr")},i=function(){return c.fromTag("th")},j=function(){return c.fromTag("td")},k=function(k,l,m,n){var o=f();b.setAll(o,{"border-collapse":"collapse",width:"100%"}),a.set(o,"border","1");var p=g();d.append(o,p);for(var q=[],r=0;r<k;r++){for(var s=h(),t=0;t<l;t++){var u=r<m||t<n?i():j();t<n&&a.set(u,"scope","row"),r<m&&a.set(u,"scope","col"),d.append(u,c.fromTag("br")),b.set(u,"width",100/l+"%"),d.append(s,u)}q.push(s)}return e.append(p,q),o};return{render:k}}),g("q",["2h"],function(a){return{render:a.render}}),g("s",["l","m","12","28","13","1m"],function(a,b,c,d,e,f){var g=function(a){return a.dom().innerHTML},h=function(g,h){var i=f.owner(g),j=i.dom(),k=a.fromDom(j.createDocumentFragment()),l=b.fromHtml(h,j);d.append(k,l),e.empty(g),c.append(g,k)},i=function(b){var d=a.fromTag("div"),e=a.fromDom(b.dom().cloneNode(!0));return c.append(d,e),g(d)};return{get:g,set:h,getOuter:i}}),g("3",["f","q","l","r","s","t"],function(a,b,c,d,e,f){var g=function(a,b){a.selection.select(b.dom(),!0),a.selection.collapse(!0)},h=function(b,c){f.descendant(c,"td,th").each(a.curry(g,b))},i=function(a,f,g){var i,j=b.render(g,f,0,0);d.set(j,"id","__mce");var k=e.getOuter(j);return a.insertContent(k),i=a.dom.get("__mce"),a.dom.setAttrib(i,"id",null),a.$("tr",i).each(function(b,c){a.fire("newrow",{node:c}),a.$("th,td",c).each(function(b,c){a.fire("newcell",{node:c})})}),a.dom.setAttribs(i,a.settings.table_default_attributes||{}),a.dom.setStyles(i,a.settings.table_default_styles||{}),h(a,c.fromDom(i)),i};return{insert:i}}),g("59",["1z","e","22","3w"],function(a,b,c,d){return function(e,f){var g=function(b,c){if(!a.isNumber(c)&&!c.match(/^[0-9]+$/))throw e+".set accepts only positive integer values. Value was "+c;var f=b.dom();d.isSupported(f)&&(f.style[e]=c+"px")},h=function(a){var b=f(a);if(b<=0||null===b){var d=c.get(a,e);return parseFloat(d)||0}return b},i=h,j=function(a,d){return b.foldl(d,function(b,d){var e=c.get(a,d),f=void 0===e?0:parseInt(e,10);return isNaN(f)?b:b+f},0)},k=function(a,b,c){var d=j(a,c),e=b>d?b-d:0;return e};return{set:g,get:h,getOuter:i,aggregate:j,max:k}}}),g("43",["35","22","59"],function(a,b,c){var d=c("height",function(b){return a.inBody(b)?b.dom().getBoundingClientRect().height:b.dom().offsetHeight}),e=function(a,b){d.set(a,b)},f=function(a){return d.get(a)},g=function(a){return d.getOuter(a)},h=function(a,c){var e=["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"],f=d.max(a,c,e);b.set(a,"max-height",f+"px")};return{set:e,get:f,getOuter:g,setMax:h}}),g("44",["22","59"],function(a,b){var c=b("width",function(a){return a.dom().offsetWidth}),d=function(a,b){c.set(a,b)},e=function(a){return c.get(a)},f=function(a){return c.getOuter(a)},g=function(b,d){var e=["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"],f=c.max(b,d,e);a.set(b,"max-width",f+"px")};return{set:d,get:e,getOuter:f,setMax:g}}),g("42",["26","22","43","44"],function(a,b,c,d){var e=a.detect(),f=function(){return e.browser.isIE()||e.browser.isEdge()},g=function(a,b){var c=parseFloat(a);return isNaN(c)?b:c},h=function(a,c,d){return g(b.get(a,c),d)},i=function(a){var c=h(a,"padding-top",0),d=h(a,"padding-bottom",0),e=h(a,"border-top-width",0),f=h(a,"border-bottom-width",0),g=a.dom().getBoundingClientRect().height,i=b.get(a,"box-sizing"),j=e+f;return"border-box"===i?g:g-c-d-j},j=function(a){return h(a,"width",d.get(a))},k=function(a){return f()?i(a):h(a,"height",c.get(a))};return{getWidth:j,getHeight:k}}),g("2i",["f","g","3x","j","42","n","r","22","43","44","3v","2e"],function(a,b,c,d,e,f,g,h,i,j,k,l){var m=/(\d+(\.\d+)?)(\w|%)*/,n=/(\d+(\.\d+)?)%/,o=/(\d+(\.\d+)?)px|em/,p=function(a,b){h.set(a,"width",b+"px")},q=function(a,b){h.set(a,"width",b+"%")},r=function(a,b){h.set(a,"height",b+"px")},s=function(a){return h.getRaw(a,"height").getOrThunk(function(){return e.getHeight(a)+"px"})},t=function(a,b,c,e){var f=d.table(a).map(function(a){var d=c(a);return k.floor(b/100*d)}).getOr(b);return e(a,f),f},u=function(a,b,d,e){var g=l(a,10);return c.endsWith(a,"%")&&"table"!==f.name(b)?t(b,g,d,e):g},v=function(a){var b=s(a);return b?u(b,a,i.get,r):i.get(a)},w=function(a,b,c){var d=c(a),e=x(a,b);return d/e},x=function(a,b){return g.has(a,b)?l(g.get(a,b),10):1},y=function(a){var c=h.getRaw(a,"width");return c.fold(function(){return b.from(g.get(a,"width"))},function(a){return b.some(a)})},z=function(a,b){return a/b.pixelWidth()*100},A=function(a,b,c){if(n.test(b)){var d=n.exec(b);return parseFloat(d[1],10)}var e=j.get(a),f=l(e,10);return z(f,c)},B=function(a,b){var c=y(a);return c.fold(function(){var c=j.get(a),d=l(c,10);return z(d,b)},function(c){return A(a,c,b)})},C=function(a,b){return a/100*b.pixelWidth()},D=function(a,b,c){if(o.test(b)){var d=o.exec(b);return l(d[1],10)}if(n.test(b)){var e=n.exec(b),f=parseFloat(e[1],10);return C(f,c)}var g=j.get(a);return l(g,10)},E=function(a,b){var c=y(a);return c.fold(function(){var b=j.get(a),c=l(b,10);return c},function(c){return D(a,c,b)})},F=function(a){return w(a,"rowspan",v)},G=function(c){var d=y(c);return d.bind(function(c){if(m.test(c)){var d=m.exec(c);return b.some({width:a.constant(d[1]),unit:a.constant(d[3])})}return b.none()})},H=function(a,b,c){h.set(a,"width",b+c)};return{percentageBasedSizeRegex:a.constant(n),pixelBasedSizeRegex:a.constant(o),setPixelWidth:p,setPercentageWidth:q,setHeight:r,getPixelWidth:E,getPercentageWidth:B,getGenericWidth:G,setGenericWidth:H,getHeight:F,getRawWidth:y}}),g("u",["2i"],function(a){var b=function(b,c){var d=a.getGenericWidth(b);d.each(function(d){var e=d.width()/2;a.setGenericWidth(b,e,d.unit()),a.setGenericWidth(c,e,d.unit())})};return{halve:b}}),g("30",["f"],function(a){var b=function(c,d){var e=function(a,e){return b(c+a,d+e)};return{left:a.constant(c),top:a.constant(d),translate:e}};return b}),g("45",["f","1c","l","n","2a","23"],function(a,b,c,d,e,f){var g=function(d,g){var h=g||c.fromDom(f.documentElement);return e.ancestor(d,a.curry(b.eq,h)).isSome()},h=function(a){var b=a.dom();return b===b.window?a:d.isDocument(a)?b.defaultView||b.parentWindow:null};return{attached:g,windowOf:h}}),g("2z",["30","45","l"],function(a,b,c){var d=function(b){var c=b.getBoundingClientRect();return a(c.left,c.top)},e=function(a,b){return void 0!==a?a:void 0!==b?b:0},f=function(a){var d=a.dom().ownerDocument,f=d.body,g=b.windowOf(c.fromDom(d)),i=d.documentElement,j=e(g.pageYOffset,i.scrollTop),k=e(g.pageXOffset,i.scrollLeft),l=e(i.clientTop,f.clientTop),m=e(i.clientLeft,f.clientLeft);return h(a).translate(k-m,j-l)},g=function(b){var c=b.dom();return a(c.offsetLeft,c.offsetTop)},h=function(e){var f=e.dom(),g=f.ownerDocument,h=g.body,i=c.fromDom(g.documentElement);return h===f?a(h.offsetLeft,h.offsetTop):b.attached(e,i)?d(f):a(0,0)};return{absolute:f,relative:g,viewport:h}}),g("34",["e","f","1k","43","2z","44"],function(a,b,c,d,e,f){var g=c.immutable("row","y"),h=c.immutable("col","x"),i=function(a){var b=e.absolute(a);return b.left()+f.getOuter(a)},j=function(a){return e.absolute(a).left()},k=function(a,b){return h(a,j(b))},l=function(a,b){return h(a,i(b))},m=function(a){return e.absolute(a).top()},n=function(a,b){return g(a,m(b))},o=function(a,b){return g(a,m(b)+d.getOuter(b))},p=function(b,c,d){if(0===d.length)return[];var e=a.map(d.slice(1),function(a,c){return a.map(function(a){return b(c,a)})}),f=d[d.length-1].map(function(a){return c(d.length-1,a)});return e.concat([f])},q=function(a,b){return-a},r={delta:b.identity,positions:b.curry(p,n,o),edge:m},s={delta:b.identity,edge:j,positions:b.curry(p,k,l)},t={delta:q,edge:i,positions:b.curry(p,l,k)};return{height:r,rtl:t,ltr:s}}),g("2j",["34"],function(a){return{ltr:a.ltr,rtl:a.rtl}}),g("v",["2j"],function(a){return function(b){var c=function(c){return b(c).isRtl()?a.rtl:a.ltr},d=function(a,b){return c(b).delta(a,b)},e=function(a,b){return c(b).positions(a,b)},f=function(a){return c(a).edge(a)};return{delta:d,edge:f,positions:e}}}),g("w",["1w","1x"],function(a,b){var c=function(c){var d=a.fromTable(c),e=b.generate(d);return e.grid()};return{getGridSize:c}}),g("46",[],function(){var a=function(b){var c=b,d=function(){return c},e=function(a){c=a},f=function(){return a(d())};return{get:d,set:e,clone:f}};return a}),g("47",["e","f","1t","1z","3r","1q"],function(a,b,c,d,e,f){var g=function(a,b){return h(a,b,{validate:d.isFunction,label:"function"})},h=function(b,d,g){if(0===d.length)throw new f("You must specify at least one required field.");return e.validateStrArr("required",d),e.checkDupes(d),function(f){var h=c.keys(f),i=a.forall(d,function(b){return a.contains(h,b)});i||e.reqMessage(d,h),b(d,h);var j=a.filter(d,function(a){return!g.validate(f[a],a)});return j.length>0&&e.invalidTypeMessage(j,g.label),f}},i=function(b,c){var d=a.filter(c,function(c){return!a.contains(b,c)});d.length>0&&e.unsuppMessage(d)},j=b.noop;return{exactly:b.curry(g,i),ensure:b.curry(g,j),ensureWith:b.curry(h,j)}}),g("2k",["e","f","g","46","47","r","22","2e"],function(a,b,c,d,e,f,g,h){var i=function(a){var c=f.has(a,"colspan")?h(f.get(a,"colspan"),10):1,d=f.has(a,"rowspan")?h(f.get(a,"rowspan"),10):1;return{element:b.constant(a),colspan:b.constant(c),rowspan:b.constant(d)}},j=function(a,b){m(a);var e=d(c.none()),f=void 0!==b?b:i,g=function(b){return a.cell(b)},h=function(a){var b=f(a);return g(b)},j=function(a){var b=h(a);return e.get().isNone()&&e.set(c.some(b)),k=c.some({item:a,replacement:b}),b},k=c.none(),l=function(a,b){return k.fold(function(){return j(a)},function(c){return b(a,c.item)?c.replacement:j(a)})};return{getOrInit:l,cursor:e.get}},k=function(b,e){return function(f){var g=d(c.none());m(f);var h=[],i=function(b,c){return a.find(h,function(a){return c(a.item,b)})},j=function(a){var d=f.replace(a,e,{scope:b});return h.push({item:a,sub:d}),g.get().isNone()&&g.set(c.some(d)),d},k=function(a,b){return i(a,b).fold(function(){return j(a)},function(c){return b(a,c.item)?c.sub:j(a)})};return{replaceOrInit:k,cursor:g.get}}},l=function(a){m(a);var e=d(c.none()),f=function(d){return e.get().isNone()&&e.set(c.some(d)),function(){var c=a.cell({element:b.constant(d),colspan:b.constant(1),rowspan:b.constant(1)});return g.remove(c,"width"),g.remove(d,"width"),c}};return{combine:f,cursor:e.get}},m=e.exactly(["cell","row","replace","gap"]);return{modification:j,transform:k,merging:l}}),g("5a",["e"],function(a){var b=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],c=function(b,c){var d=b.property().name(c);return a.contains(["ol","ul"],d)},d=function(c,d){var e=c.property().name(d);return a.contains(b,e)},e=function(b,c){var d=b.property().name(c);return a.contains(["address","pre","p","h1","h2","h3","h4","h5","h6"],d)},f=function(b,c){var d=b.property().name(c);return a.contains(["h1","h2","h3","h4","h5","h6"],d)},g=function(b,c){return a.contains(["div","li","td","th","blockquote","body","caption"],b.property().name(c))},h=function(b,c){return a.contains(["br","img","hr","input"],b.property().name(c))},i=function(a,b){return"iframe"===a.property().name(b)},j=function(a,b){return!(d(a,b)||h(a,b))&&"li"!==a.property().name(b)};return{isBlock:d,isList:c,isFormatting:e,isHeading:f,isContainer:g,isEmptyTag:h,isFrame:i,isInline:j}}),g("48",["56","5a"],function(a,b){var c=a(),d=function(a){return b.isBlock(c,a)},e=function(a){return b.isList(c,a)},f=function(a){return b.isFormatting(c,a)},g=function(a){return b.isHeading(c,a)},h=function(a){return b.isContainer(c,a)},i=function(a){return b.isEmptyTag(c,a)},j=function(a){return b.isFrame(c,a)},k=function(a){return b.isInline(c,a)};return{isBlock:d,isList:e,isFormatting:f,isHeading:g,isContainer:h,isEmptyTag:i,isFrame:j,isInline:k}}),g("2l",["e","48","1c","28","13","l","n","1l","2a","1m","1d"],function(a,b,c,d,e,f,g,h,i,j,k){var l=function(l){var m=function(a){return"br"===g.name(a)},n=function(b){return a.forall(b,function(a){return m(a)||g.isText(a)&&0===h.get(a).trim().length})},o=function(a){return"li"===g.name(a)||i.ancestor(a,b.isList).isSome()},p=function(a){return j.nextSibling(a).map(function(a){return!!b.isBlock(a)||(b.isEmptyTag(a)?"img"!==g.name(a):void 0)}).getOr(!1)},q=function(a){return k.last(a).bind(function(d){var e=p(d);return j.parent(d).map(function(g){return e===!0||o(g)||m(d)||b.isBlock(g)&&!c.eq(a,g)?[]:[f.fromTag("br")]})}).getOr([])},r=function(){var b=a.bind(l,function(a){var b=j.children(a);return n(b)?[]:b.concat(q(a))});return 0===b.length?[f.fromTag("br")]:b},s=r();e.empty(l[0]),d.append(l[0],s)};return{merge:l}}),g("2m",["e","2c"],function(a,b){var c=function(a,b,c){var d=a.cells(),f=d.slice(0,b),g=d.slice(b),h=f.concat([c]).concat(g);return e(a,h)},d=function(a,b,c){var d=a.cells();d[b]=c},e=function(a,c){return b.rowcells(c,a.section())},f=function(c,d){var e=c.cells(),f=a.map(e,d);return b.rowcells(f,c.section())},g=function(a,b){return a.cells()[b]},h=function(a,b){return g(a,b).element()},i=function(a){return a.cells().length};return{addCell:c,setCells:e,mutateCell:d,getCell:g,getCellElement:h,mapCells:f,cellLength:i}}),g("49",["1z","1p","1q"],function(a,b,c){var d=function(a,b){return b},e=function(b,c){var d=a.isObject(b)&&a.isObject(c);return d?g(b,c):c},f=function(a){return function(){for(var d=new b(arguments.length),e=0;e<d.length;e++)d[e]=arguments[e];if(0===d.length)throw new c("Can't merge zero objects");for(var f={},g=0;g<d.length;g++){var h=d[g];for(var i in h)h.hasOwnProperty(i)&&(f[i]=a(f[i],h[i]))}return f}},g=f(e),h=f(d);return{deepMerge:g,merge:h}}),g("3m",["g"],function(a){var b=function(a){for(var b=[],c=function(a){b.push(a)},d=0;d<a.length;d++)a[d].each(c);return b},c=function(b,c){for(var d=0;d<b.length;d++){var e=c(b[d],d);if(e.isSome())return e}return a.none()},d=function(b,c){for(var d=[],e=0;e<b.length;e++){var f=b[e];if(!f.isSome())return a.none();d.push(f.getOrDie())}return a.some(c.apply(null,d))};return{cat:b,findMap:c,liftN:d}}),g("4a",["e","f","2m"],function(a,b,c){var d=function(b,d){return a.map(b,function(a){return c.getCell(a,d)})},e=function(a,b){return a[b]},f=function(b,c){if(0===b.length)return 0;var d=b[0],e=a.findIndex(b,function(a){return!c(d.element(),a.element())});return e.fold(function(){return b.length},function(a){return a})},g=function(a,c,g,h){var i=e(a,c).cells().slice(g),j=f(i,h),k=d(a,g).slice(c),l=f(k,h);return{colspan:b.constant(j),rowspan:b.constant(l)}};return{subgrid:g}}),g("2p",["e","2c","4a","1x"],function(a,b,c,d){var e=function(d,e){var f=a.map(d,function(b,c){return a.map(b.cells(),function(a,b){return!1})}),g=function(a,b,c,d){for(var e=a;e<a+c;e++)for(var g=b;g<b+d;g++)f[e][g]=!0};return a.map(d,function(h,i){var j=a.bind(h.cells(),function(a,h){if(f[i][h]===!1){var j=c.subgrid(d,i,h,e);return g(i,h,j.rowspan(),j.colspan()),[b.detailnew(a.element(),j.rowspan(),j.colspan(),a.isNew())]}return[]});return b.rowdetails(j,h.section())})},f=function(a,c,e){for(var f=[],g=0;g<a.grid().rows();g++){for(var h=[],i=0;i<a.grid().columns();i++){var j=d.getAt(a,g,i).map(function(a){return b.elementnew(a.element(),e)}).getOrThunk(function(){return b.elementnew(c.gap(),!0)});h.push(j)}var k=b.rowcells(h,a.all()[g].section());f.push(k)}return f};return{toDetails:e,toGrid:f}}),g("2v",["e","f","r","l","12","28","13","k","t","1m"],function(a,b,c,d,e,f,g,h,i,j){
var k=function(a,b,d,e){d===e?c.remove(a,b):c.set(a,b,d)},l=function(c,h){var l=[],m=[],n=function(b,h){var n=i.child(c,h).getOrThunk(function(){var a=d.fromTag(h,j.owner(c).dom());return e.append(c,a),a});g.empty(n);var o=a.map(b,function(b){b.isNew()&&l.push(b.element());var c=b.element();return g.empty(c),a.each(b.cells(),function(a){a.isNew()&&m.push(a.element()),k(a.element(),"colspan",a.colspan(),1),k(a.element(),"rowspan",a.rowspan(),1),e.append(c,a.element())}),c});f.append(n,o)},o=function(a){i.child(c,a).bind(g.remove)},p=function(a,b){a.length>0?n(a,b):o(b)},q=[],r=[],s=[];return a.each(h,function(a){switch(a.section()){case"thead":q.push(a);break;case"tbody":r.push(a);break;case"tfoot":s.push(a)}}),p(q,"thead"),p(r,"tbody"),p(s,"tfoot"),{newRows:b.constant(l),newCells:b.constant(m)}},m=function(b){var c=a.map(b,function(b){var c=h.shallow(b.element());return a.each(b.cells(),function(a){var b=h.deep(a.element());k(b,"colspan",a.colspan(),1),k(b,"rowspan",a.rowspan(),1),e.append(c,b)}),c});return c};return{render:l,copy:m}}),g("5g",["e","g","3m","3v"],function(a,b,c,d){var e=function(a,b){for(var c=[],d=0;d<a;d++)c.push(b(d));return c},f=function(a,b){for(var c=[],d=a;d<b;d++)c.push(d);return c},g=function(b,c){var d=[];return a.each(b,function(a,e){e<b.length-1&&!c(a,b[e+1])?d.push(a):e===b.length-1&&d.push(a)}),d},h=function(e,f){if(f<0||f>=e.length-1)return b.none();var g=e[f].fold(function(){var b=a.reverse(e.slice(0,f));return c.findMap(b,function(a,b){return a.map(function(a){return{value:a,delta:b+1}})})},function(a){return b.some({value:a,delta:0})}),h=e[f+1].fold(function(){var a=e.slice(f+1);return c.findMap(a,function(a,b){return a.map(function(a){return{value:a,delta:b+1}})})},function(a){return b.some({value:a,delta:1})});return g.bind(function(a){return h.map(function(b){var c=b.delta+a.delta;return d.abs(b.value-a.value)/c})})};return{repeat:e,range:f,unique:g,deduce:h}}),g("5b",["e","f","g","1x","5g"],function(a,b,c,d,e){var f=function(c){var f=c.grid(),h=e.range(0,f.columns()),i=e.range(0,f.rows());return a.map(h,function(e){var f=function(){return a.bind(i,function(a){return d.getAt(c,a,e).filter(function(a){return a.column()===e}).fold(b.constant([]),function(a){return[a]})})},h=function(a){return 1===a.colspan()},j=function(){return d.getAt(c,0,e)};return g(f,h,j)})},g=function(b,d,e){var f=b(),g=a.find(f,d),h=g.orThunk(function(){return c.from(f[0]).orThunk(e)});return h.map(function(a){return a.element()})},h=function(c){var f=c.grid(),h=e.range(0,f.rows()),i=e.range(0,f.columns());return a.map(h,function(e){var f=function(){return a.bind(i,function(a){return d.getAt(c,e,a).filter(function(a){return a.row()===e}).fold(b.constant([]),function(a){return[a]})})},h=function(a){return 1===a.rowspan()},j=function(){return d.getAt(c,e,0)};return g(f,h,j)})};return{columns:f,rows:h}}),g("5c",["r","22","l"],function(a,b,c){var d=function(d,e,f,g,h){var i=c.fromTag("div");return b.setAll(i,{position:"absolute",left:e-g/2+"px",top:f+"px",height:h+"px",width:g+"px"}),a.set(i,"data-column",d),i},e=function(d,e,f,g,h){var i=c.fromTag("div");return b.setAll(i,{position:"absolute",left:e+"px",top:f-h/2+"px",height:h+"px",width:g+"px"}),a.set(i,"data-row",d),i};return{col:d,row:e}}),g("5d",[],function(){var a=function(a){var b=a.replace(/\./g,"-"),c=function(a){return b+"-"+a};return{resolve:c}};return{css:a}}),g("4k",["5d"],function(a){var b=a.css("ephox-snooker");return{resolve:b.resolve}}),g("4m",[],function(){return function(a,b,c){var d=c||!1,e=function(){b(),d=!0},f=function(){a(),d=!1},g=function(){var a=d?f:e;a()},h=function(){return d};return{on:e,off:f,toggle:g,isOn:h}}}),g("5e",["e","r"],function(a,b){var c=function(a,c){var d=b.get(a,c);return void 0===d||""===d?[]:d.split(" ")},d=function(a,d,e){var f=c(a,d),g=f.concat([e]);b.set(a,d,g.join(" "))},e=function(d,e,f){var g=a.filter(c(d,e),function(a){return a!==f});g.length>0?b.set(d,e,g.join(" ")):b.remove(d,e)};return{read:c,add:d,remove:e}}),g("4n",["e","5e"],function(a,b){var c=function(a){return void 0!==a.dom().classList},d=function(a){return b.read(a,"class")},e=function(a,c){return b.add(a,"class",c)},f=function(a,c){return b.remove(a,"class",c)},g=function(b,c){a.contains(d(b),c)?f(b,c):e(b,c)};return{get:d,add:e,remove:f,toggle:g,supports:c}}),g("3n",["4m","r","4n"],function(a,b,c){var d=function(a,b){c.supports(a)?a.dom().classList.add(b):c.add(a,b)},e=function(a){var d=c.supports(a)?a.dom().classList:c.get(a);0===d.length&&b.remove(a,"class")},f=function(a,b){if(c.supports(a)){var d=a.dom().classList;d.remove(b)}else c.remove(a,b);e(a)},g=function(a,b){return c.supports(a)?a.dom().classList.toggle(b):c.toggle(a,b)},h=function(b,d){var e=c.supports(b),f=b.dom().classList,g=function(){e?f.remove(d):c.remove(b,d)},h=function(){e?f.add(d):c.add(b,d)};return a(g,h,i(b,d))},i=function(a,b){return c.supports(a)&&a.dom().classList.contains(b)};return{add:d,remove:f,toggle:g,toggler:h,has:i}}),g("4b",["e","5b","1w","1x","5c","4k","12","13","3n","22","y","43","2z","44"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n){var o=f.resolve("resizer-bar"),p=f.resolve("resizer-rows"),q=f.resolve("resizer-cols"),r=7,s=function(b){var c=k.descendants(b.parent(),"."+o);a.each(c,h.remove)},t=function(b,c,d){var e=b.origin();a.each(c,function(a,c){a.each(function(a){var c=d(e,a);i.add(c,o),g.append(b.parent(),c)})})},u=function(a,b,c,d){t(a,b,function(a,b){var f=e.col(b.col(),b.x()-a.left(),c.top()-a.top(),r,d);return i.add(f,q),f})},v=function(a,b,c,d){t(a,b,function(a,b){var f=e.row(b.row(),c.left()-a.left(),b.y()-a.top(),d,r);return i.add(f,p),f})},w=function(a,b,c,d,e,f){var g=m.absolute(b),h=c.length>0?e.positions(c,b):[];v(a,h,g,n.getOuter(b));var i=d.length>0?f.positions(d,b):[];u(a,i,g,l.getOuter(b))},x=function(a,e,f,g){s(a,e);var h=c.fromTable(e),i=d.generate(h),j=b.rows(i),k=b.columns(i);w(a,e,j,k,f,g)},y=function(b,c){var d=k.descendants(b.parent(),"."+o);a.each(d,c)},z=function(a){y(a,function(a){j.set(a,"display","none")})},A=function(a){y(a,function(a){j.set(a,"display","block")})},B=function(a){return i.has(a,p)},C=function(a){return i.has(a,q)};return{refresh:x,hide:z,show:A,destroy:s,isRowBar:B,isColBar:C}}),g("2n",["e","49","f","g","3m","2c","j","1w","2p","1x","2v","34","4b","1c","1m"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o){var p=function(a,b){return i.toGrid(a,b,!1)},q=function(b,c){var d=function(a){var b=e.findMap(a,function(a){return o.parent(a.element()).map(function(a){var b=o.parent(a).isNone();return f.elementnew(a,b)})});return b.getOrThunk(function(){return f.elementnew(c.row(),!0)})};return a.map(b,function(a){var b=d(a.details());return f.rowdatanew(b.element(),a.details(),a.section(),b.isNew())})},r=function(a,b){var c=i.toDetails(a,n.eq);return q(c,b)},s=function(b,c){var d=a.flatten(a.map(b.all(),function(a){return a.cells()}));return a.find(d,function(a){return n.eq(c,a.element())})},t=function(a,b,e,f,g){return function(i,o,q,s,t){var u=h.fromTable(o),v=j.generate(u),w=b(v,q).map(function(b){var d=p(v,s),e=a(d,b,n.eq,g(s)),f=r(e.grid(),s);return{grid:c.constant(f),cursor:e.cursor}});return w.fold(function(){return d.none()},function(a){var b=k.render(o,a.grid());return e(o,a.grid(),t),f(o),m.refresh(i,o,l.height,t),d.some({cursor:a.cursor,newRows:b.newRows,newCells:b.newCells})})}},u=function(a,b){return g.cell(b.element()).bind(function(b){return s(a,b)})},v=function(a,c){return g.cell(c.element()).bind(function(d){return s(a,d).map(function(a){return b.merge(a,{generators:c.generators,clipboard:c.clipboard})})})},w=function(c,f){var h=a.map(f.selection(),function(a){return g.cell(a).bind(function(a){return s(c,a)})}),i=e.cat(h);return i.length>0?d.some(b.merge({cells:i},{generators:f.generators,clipboard:f.clipboard})):d.none()},x=function(a,b){return b.mergable()},y=function(a,b){return b.unmergable()},z=function(b,c){var f=a.map(c.selection(),function(a){return g.cell(a).bind(function(a){return s(b,a)})}),h=e.cat(f);return h.length>0?d.some(h):d.none()};return{run:t,toDetailList:r,onCell:u,onCells:z,onPaste:v,onPasteRows:w,onMergable:x,onUnmergable:y}}),g("5f",["f","g"],function(a,b){var c=function(d){var e=function(a){return d===a},f=function(a){return c(d)},g=function(a){return c(d)},h=function(a){return c(a(d))},i=function(a){a(d)},j=function(a){return a(d)},k=function(a,b){return b(d)},l=function(a){return a(d)},m=function(a){return a(d)},n=function(){return b.some(d)};return{is:e,isValue:a.constant(!0),isError:a.constant(!1),getOr:a.constant(d),getOrThunk:a.constant(d),getOrDie:a.constant(d),or:f,orThunk:g,fold:k,map:h,each:i,bind:j,exists:l,forall:m,toOption:n}},d=function(c){var e=function(a){return a()},f=function(){return a.die(c)()},g=function(a){return a},h=function(a){return a()},i=function(a){return d(c)},j=function(a){return d(c)},k=function(a,b){return a(c)};return{is:a.constant(!1),isValue:a.constant(!1),isError:a.constant(!0),getOr:a.identity,getOrThunk:e,getOrDie:f,or:g,orThunk:h,fold:k,map:i,each:a.noop,bind:j,exists:a.constant(!1),forall:a.constant(!0),toOption:b.none}};return{value:c,error:d}}),g("4c",["e","f","5f","2c","2m","5g","1p","1q","3v"],function(a,b,c,d,e,f,g,h,i){var j=function(a,d,f){if(a.row()>=d.length||a.column()>e.cellLength(d[0]))return c.error("invalid start address out of table bounds, row: "+a.row()+", column: "+a.column());var g=d.slice(a.row()),h=g[0].cells().slice(a.column()),i=e.cellLength(f[0]),j=f.length;return c.value({rowDelta:b.constant(g.length-j),colDelta:b.constant(h.length-i)})},k=function(a,c){var d=e.cellLength(a[0]),f=e.cellLength(c[0]);return{rowDelta:b.constant(0),colDelta:b.constant(d-f)}},l=function(b,c){return a.map(b,function(){return d.elementnew(c.cell(),!0)})},m=function(a,b,c){return a.concat(f.repeat(b,function(b){return e.setCells(a[a.length-1],l(a[a.length-1].cells(),c))}))},n=function(b,c,d){return a.map(b,function(a){return e.setCells(a,a.cells().concat(l(f.range(0,c),d)))})},o=function(a,c,d){var e=c.colDelta()<0?n:b.identity,f=c.rowDelta()<0?m:b.identity,g=e(a,i.abs(c.colDelta()),d),h=f(g,i.abs(c.rowDelta()),d);return h};return{measure:j,measureWidth:k,tailor:o}}),g("2q",["e","g","2c","2m"],function(a,b,c,d){var e=function(a,b,e,f){if(0===a.length)return a;for(var g=b.startRow();g<=b.finishRow();g++)for(var h=b.startCol();h<=b.finishCol();h++)d.mutateCell(a[g],h,c.elementnew(f(),!1));return a},f=function(a,b,e,f){for(var g=!0,h=0;h<a.length;h++)for(var i=0;i<d.cellLength(a[0]);i++){var j=d.getCellElement(a[h],i),k=e(j,b);k===!0&&g===!1?d.mutateCell(a[h],i,c.elementnew(f(),!0)):k===!0&&(g=!1)}return a},g=function(b,c){return a.foldl(b,function(b,d){return a.exists(b,function(a){return c(a.element(),d.element())})?b:b.concat([d])},[])},h=function(e,f,h,i){if(f>0&&f<e.length){var j=e[f-1].cells(),k=g(j,h);a.each(k,function(a){for(var g=b.none(),j=f;j<e.length;j++)for(var k=0;k<d.cellLength(e[0]);k++){var l=e[j].cells()[k],m=h(l.element(),a.element());m&&(g.isNone()&&(g=b.some(i())),g.each(function(a){d.mutateCell(e[j],k,c.elementnew(a,!0))}))}})}return e};return{merge:e,unmerge:f,splitRows:h}}),g("2o",["f","2c","4c","2m","2q"],function(a,b,c,d,e){var f=function(b,c,e,f){var g=d.getCell(b[c],e),h=a.curry(f,g.element()),i=b[c];return b.length>1&&d.cellLength(i)>1&&(e>0&&h(d.getCellElement(i,e-1))||e<i.length-1&&h(d.getCellElement(i,e+1))||c>0&&h(d.getCellElement(b[c-1],e))||c<b.length-1&&h(d.getCellElement(b[c+1],e)))},g=function(a,c,g,h,i){for(var j=a.row(),k=a.column(),l=g.length,m=d.cellLength(g[0]),n=j+l,o=k+m,p=j;p<n;p++)for(var q=k;q<o;q++){f(c,p,q,i)&&e.unmerge(c,d.getCellElement(c[p],q),i,h.cell);var r=d.getCellElement(g[p-j],q-k),s=h.replace(r);d.mutateCell(c[p],q,b.elementnew(s,!0))}return c},h=function(a,b,d,e,f){var h=c.measure(a,b,d);return h.map(function(h){var i=c.tailor(b,h,e);return g(a,i,d,e,f)})},i=function(a,b,d,f,g){e.splitRows(b,a,g,f.cell);var h=c.measureWidth(d,b),i=c.tailor(d,h,f),j=c.measureWidth(b,i),k=c.tailor(b,j,f);return k.slice(0,a).concat(i).concat(k.slice(a,k.length))};return{merge:h,insert:i}}),g("2r",["e","2c","2m"],function(a,b,c){var d=function(a,d,e,f,g){var h=a.slice(0,d),i=a.slice(d),j=c.mapCells(a[e],function(e,h){var i=d>0&&d<a.length&&f(c.getCellElement(a[d-1],h),c.getCellElement(a[d],h)),j=i?c.getCell(a[d],h):b.elementnew(g(e.element(),f),!0);return j});return h.concat([j]).concat(i)},e=function(d,e,f,g,h){return a.map(d,function(a){var d=e>0&&e<c.cellLength(a)&&g(c.getCellElement(a,e-1),c.getCellElement(a,e)),i=d?c.getCell(a,e):b.elementnew(h(c.getCellElement(a,f),g),!0);return c.addCell(a,e,i)})},f=function(d,e,f,g,h){var i=f+1;return a.map(d,function(a,d){var j=d===e,k=j?b.elementnew(h(c.getCellElement(a,f),g),!0):c.getCell(a,f);return c.addCell(a,i,k)})},g=function(a,d,e,f,g){var h=d+1,i=a.slice(0,h),j=a.slice(h),k=c.mapCells(a[d],function(a,c){var d=c===e;return d?b.elementnew(g(a.element(),f),!0):a});return i.concat([k]).concat(j)},h=function(c,d,e){var f=a.map(c,function(a){var c=a.cells().slice(0,d).concat(a.cells().slice(e+1));return b.rowcells(c,a.section())});return a.filter(f,function(a){return a.cells().length>0})},i=function(a,b,c){return a.slice(0,b).concat(a.slice(c+1))};return{insertRowAt:d,insertColumnAt:e,splitCellIntoColumns:f,splitCellIntoRows:g,deleteRowsAt:i,deleteColumnsAt:h}}),g("2s",["e","2c","2m"],function(a,b,c){var d=function(d,e,f,g){var h=function(b){return a.exists(e,function(a){return f(b.element(),a.element())})};return a.map(d,function(a){return c.mapCells(a,function(a){return h(a)?b.elementnew(g(a.element(),f),!0):a})})},e=function(a,b,d,e){return void 0!==c.getCellElement(a[b],d)&&b>0&&e(c.getCellElement(a[b-1],d),c.getCellElement(a[b],d))},f=function(a,b,d){return b>0&&d(c.getCellElement(a,b-1),c.getCellElement(a,b))},g=function(b,g,h,i){var j=a.bind(b,function(a,d){var i=e(b,d,g,h)||f(a,g,h);return i?[]:[c.getCell(a,g)]});return d(b,j,h,i)},h=function(b,c,g,h){var i=b[c],j=a.bind(i.cells(),function(a,d){var h=e(b,c,d,g)||f(i,d,g);return h?[]:[a]});return d(b,j,g,h)};return{replaceColumn:g,replaceRow:h}}),g("5h",[],function(){var a=function(){return f(function(a,b,c,d,e){return a()})},b=function(a){return f(function(b,c,d,e,f){return c(a)})},c=function(a,b){return f(function(c,d,e,f,g){return e(a,b)})},d=function(a,b,c){return f(function(d,e,f,g,h){return g(a,b,c)})},e=function(a,b){return f(function(c,d,e,f,g){return g(a,b)})},f=function(a){return{fold:a}};return{none:a,only:b,left:c,middle:d,right:e}}),g("4d",["e","f","5h","3v"],function(a,b,c,d){var e=function(a,b){return 0===a.length?c.none():1===a.length?c.only(0):0===b?c.left(0,1):b===a.length-1?c.right(b-1,b):b>0&&b<a.length-1?c.middle(b-1,b,b+1):c.none()},f=function(c,f,g,h){var i=c.slice(0),j=e(c,f),k=function(c){return a.map(c,b.constant(0))},l=b.constant(k(i)),m=function(a){return h.singleColumnWidth(i[a],g)},n=function(a,b){if(g>=0){var c=d.max(h.minCellWidth(),i[b]-g);return k(i.slice(0,a)).concat([g,c-i[b]]).concat(k(i.slice(b+1)))}var e=d.max(h.minCellWidth(),i[a]+g),f=i[a]-e;return k(i.slice(0,a)).concat([e-i[a],f]).concat(k(i.slice(b+1)))},o=n,p=function(a,b,c){return n(b,c)},q=function(a,b){if(g>=0)return k(i.slice(0,b)).concat([g]);var c=d.max(h.minCellWidth(),i[b]+g);return k(i.slice(0,b)).concat([c-i[b]])};return j.fold(l,m,o,p,q)};return{determine:f}}),g("4h",["f","r","22","2e"],function(a,b,c,d){var e=function(a,c){return b.has(a,c)&&d(b.get(a,c),10)>1},f=function(a){return e(a,"colspan")},g=function(a){return e(a,"rowspan")},h=function(a,b){return d(c.get(a,b),10)};return{hasColspan:f,hasRowspan:g,minWidth:a.constant(10),minHeight:a.constant(10),getInt:h}}),g("4e",["e","f","5b","2i","4h","5g","22"],function(a,b,c,d,e,f,g){var h=function(a,b,c){return g.getRaw(a,b).fold(function(){return c(a)+"px"},function(a){return a})},i=function(a){return h(a,"width",d.getPixelWidth)},j=function(a){return h(a,"height",d.getHeight)},k=function(d,g,h,i,j){var k=c.columns(d),l=a.map(k,function(a){return a.map(g.edge)});return a.map(k,function(a,c){var d=a.filter(b.not(e.hasColspan));return d.fold(function(){var a=f.deduce(l,c);return i(a)},function(a){return h(a,j)})})},l=function(a){return a.map(function(a){return a+"px"}).getOr("")},m=function(a,b){return k(a,b,i,l)},n=function(a,b,c){return k(a,b,d.getPercentageWidth,function(a){return a.fold(function(){return c.minCellWidth()},function(a){return a/c.pixelWidth()*100})},c)},o=function(a,b,c){return k(a,b,d.getPixelWidth,function(a){return a.getOrThunk(c.minCellWidth)},c)},p=function(d,g,h,i){var j=c.rows(d),k=a.map(j,function(a){return a.map(g.edge)});return a.map(j,function(a,c){var d=a.filter(b.not(e.hasRowspan));return d.fold(function(){var a=f.deduce(k,c);return i(a)},function(a){return h(a)})})},q=function(a,b){return p(a,b,d.getHeight,function(a){return a.getOrThunk(e.minHeight)})},r=function(a,b){return p(a,b,j,l)};return{getRawWidths:m,getPixelWidths:o,getPercentageWidths:n,getPixelHeights:q,getRawHeights:r}}),g("4f",["e","f","1x","2e"],function(a,b,c,d){var e=function(a,b,c){for(var d=0,e=a;e<b;e++)d+=void 0!==c[e]?c[e]:0;return d},f=function(d,f){var g=c.justCells(d);return a.map(g,function(a){var c=e(a.column(),a.column()+a.colspan(),f);return{element:a.element,width:b.constant(c),colspan:a.colspan}})},g=function(d,f){var g=c.justCells(d);return a.map(g,function(a){var c=e(a.row(),a.row()+a.rowspan(),f);return{element:a.element,height:b.constant(c),rowspan:a.rowspan}})},h=function(c,d){return a.map(c.all(),function(a,c){return{element:a.element,height:b.constant(d[c])}})};return{recalculateWidth:f,recalculateHeight:g,matchRowHeight:h}}),g("4g",["e","f","4e","2i","4h","44","3v"],function(a,b,c,d,e,f,g){var h=function(a,g){var h=parseFloat(a,10),i=f.get(g),j=function(a){return a/i*100},k=function(a,b){return[100-a]},l=function(){return e.minWidth()/i*100},m=function(a,b,c){var e=h+c;d.setPercentageWidth(a,e)};return{width:b.constant(h),pixelWidth:b.constant(i),getWidths:c.getPercentageWidths,getCellDelta:j,singleColumnWidth:k,minCellWidth:l,setElementWidth:d.setPercentageWidth,setTableWidth:m}},i=function(f){var h=parseInt(f,10),i=b.identity,j=function(a,b){var c=g.max(e.minWidth(),a+b);return[c-a]},k=function(b,c,e){var f=a.foldr(c,function(a,b){return a+b},0);d.setPixelWidth(b,f)};return{width:b.constant(h),pixelWidth:b.constant(h),getWidths:c.getPixelWidths,getCellDelta:i,singleColumnWidth:j,minCellWidth:e.minWidth,setElementWidth:d.setPixelWidth,setTableWidth:k}},j=function(a,b){if(d.percentageBasedSizeRegex().test(b)){var c=d.percentageBasedSizeRegex().exec(b);return h(c[1],a)}if(d.pixelBasedSizeRegex().test(b)){var e=d.pixelBasedSizeRegex().exec(b);return i(e[1])}var g=f.get(a);return i(g)},k=function(a){var b=d.getRawWidth(a);return b.fold(function(){var b=f.get(a);return i(b)},function(b){return j(a,b)})};return{getTableSize:k}}),g("2t",["e","4d","1w","1x","4e","4f","2i","4g","4h","3v"],function(a,b,c,d,e,f,g,h,i,j){var k=function(a){return d.generate(a)},l=function(b){return a.foldr(b,function(a,b){return a+b},0)},m=function(a){var b=c.fromTable(a);return k(b)},n=function(c,d,e,g){var i=h.getTableSize(c),j=i.getCellDelta(d),k=m(c),l=i.getWidths(k,g,i),n=b.determine(l,e,j,i),o=a.map(n,function(a,b){return a+l[b]}),p=f.recalculateWidth(k,o);a.each(p,function(a){i.setElementWidth(a.element(),a.width())}),e===k.grid().columns()-1&&i.setTableWidth(c,o,j)},o=function(b,c,d,h){var k=m(b),n=e.getPixelHeights(k,h),o=a.map(n,function(a,b){return d===b?j.max(c+a,i.minHeight()):a}),p=f.recalculateHeight(k,o),q=f.matchRowHeight(k,o);a.each(q,function(a){g.setHeight(a.element(),a.height())}),a.each(p,function(a){g.setHeight(a.element(),a.height())});var r=l(o);g.setHeight(b,r)},p=function(b,c,d){var e=h.getTableSize(b),g=k(c),i=e.getWidths(g,d,e),j=f.recalculateWidth(g,i);a.each(j,function(a){e.setElementWidth(a.element(),a.width())});var l=a.foldr(i,function(a,b){return b+a},0);j.length>0&&e.setElementWidth(b,l)};return{adjustWidth:n,adjustHeight:o,adjustWidthTo:p}}),g("x",["e","f","g","1k","2k","2c","2l","j","1w","2m","2n","2o","2p","1x","2q","2r","2s","2t","13"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s){var t=function(a){var b=h.cells(a);0===b.length&&s.remove(a)},u=d.immutable("grid","cursor"),v=function(a,b,c){return w(a,b,c).orThunk(function(){return w(a,0,0)})},w=function(a,b,d){return c.from(a[b]).bind(function(a){return c.from(a.cells()[d]).bind(function(a){return c.from(a.element())})})},x=function(a,b,c){return u(a,w(a,b,c))},y=function(b){return a.foldl(b,function(b,c){return a.exists(b,function(a){return a.row()===c.row()})?b:b.concat([c])},[]).sort(function(a,b){return a.row()-b.row()})},z=function(b){return a.foldl(b,function(b,c){return a.exists(b,function(a){return a.column()===c.column()})?b:b.concat([c])},[]).sort(function(a,b){return a.column()-b.column()})},A=function(a,b,c,d){var e=b.row(),f=b.row(),g=p.insertRowAt(a,f,e,c,d.getOrInit);return x(g,f,b.column())},B=function(b,c,d,e){var f=c[0].row(),g=c[0].row(),h=y(c),i=a.foldl(h,function(a,b){return p.insertRowAt(a,g,f,d,e.getOrInit)},b);return x(i,g,c[0].column())},C=function(a,b,c,d){var e=b.row(),f=b.row()+b.rowspan(),g=p.insertRowAt(a,f,e,c,d.getOrInit);return x(g,f,b.column())},D=function(b,c,d,e){var f=y(c),g=f[f.length-1].row(),h=f[f.length-1].row()+f[f.length-1].rowspan(),i=a.foldl(f,function(a,b){return p.insertRowAt(a,h,g,d,e.getOrInit)},b);return x(i,h,c[0].column())},E=function(a,b,c,d){var e=b.column(),f=b.column(),g=p.insertColumnAt(a,f,e,c,d.getOrInit);return x(g,b.row(),f)},F=function(b,c,d,e){var f=z(c),g=f[0].column(),h=f[0].column(),i=a.foldl(f,function(a,b){return p.insertColumnAt(a,h,g,d,e.getOrInit)},b);return x(i,c[0].row(),h)},G=function(a,b,c,d){var e=b.column(),f=b.column()+b.colspan(),g=p.insertColumnAt(a,f,e,c,d.getOrInit);return x(g,b.row(),f)},H=function(b,c,d,e){var f=c[c.length-1].column(),g=c[c.length-1].column()+c[c.length-1].colspan(),h=z(c),i=a.foldl(h,function(a,b){return p.insertColumnAt(a,g,f,d,e.getOrInit)},b);return x(i,c[0].row(),g)},I=function(a,b,c,d){var e=q.replaceRow(a,b.row(),c,d.replaceOrInit);return x(e,b.row(),b.column())},J=function(a,b,c,d){var e=q.replaceColumn(a,b.column(),c,d.replaceOrInit);return x(e,b.row(),b.column())},K=function(a,b,c,d){var e=q.replaceRow(a,b.row(),c,d.replaceOrInit);return x(e,b.row(),b.column())},L=function(a,b,c,d){var e=q.replaceColumn(a,b.column(),c,d.replaceOrInit);return x(e,b.row(),b.column())},M=function(a,b,c,d){var e=p.splitCellIntoColumns(a,b.row(),b.column(),c,d.getOrInit);return x(e,b.row(),b.column())},N=function(a,b,c,d){var e=p.splitCellIntoRows(a,b.row(),b.column(),c,d.getOrInit);return x(e,b.row(),b.column())},O=function(a,b,c,d){var e=z(b),f=p.deleteColumnsAt(a,e[0].column(),e[e.length-1].column()),g=v(f,b[0].row(),b[0].column());return u(f,g)},P=function(a,b,c,d){var e=y(b),f=p.deleteRowsAt(a,e[0].row(),e[e.length-1].row()),g=v(f,b[0].row(),b[0].column());return u(f,g)},Q=function(a,d,e,f){var h=d.cells();g.merge(h);var i=o.merge(a,d.bounds(),e,b.constant(h[0]));return u(i,c.from(h[0]))},R=function(b,d,e,f){var g=a.foldr(d,function(a,b){return o.unmerge(a,b,e,f.combine(b))},b);return u(g,c.from(d[0]))},S=function(a,b,d,e){var g=function(a,b){var c=i.fromTable(a),d=n.generate(c);return m.toGrid(d,b,!0)},h=g(b.clipboard(),b.generators()),j=f.address(b.row(),b.column()),k=l.merge(j,a,h,b.generators(),d);return k.fold(function(){return u(a,c.some(b.element()))},function(a){var c=v(a,b.row(),b.column());return u(a,c)})},T=function(a,b,c){var d=i.fromPastedRows(a,c),e=n.generate(d);return m.toGrid(e,b,!0)},U=function(a,b,c,d){var e=a[b.cells[0].row()],f=b.cells[0].row(),g=T(b.clipboard(),b.generators(),e),h=l.insert(f,a,g,b.generators(),c),i=v(h,b.cells[0].row(),b.cells[0].column());return u(h,i)},V=function(a,b,c,d){var e=a[b.cells[0].row()],f=b.cells[b.cells.length-1].row()+b.cells[b.cells.length-1].rowspan(),g=T(b.clipboard(),b.generators(),e),h=l.insert(f,a,g,b.generators(),c),i=v(h,b.cells[0].row(),b.cells[0].column());return u(h,i)},W=r.adjustWidthTo;return{insertRowBefore:k.run(A,k.onCell,b.noop,b.noop,e.modification),insertRowsBefore:k.run(B,k.onCells,b.noop,b.noop,e.modification),insertRowAfter:k.run(C,k.onCell,b.noop,b.noop,e.modification),insertRowsAfter:k.run(D,k.onCells,b.noop,b.noop,e.modification),insertColumnBefore:k.run(E,k.onCell,W,b.noop,e.modification),insertColumnsBefore:k.run(F,k.onCells,W,b.noop,e.modification),insertColumnAfter:k.run(G,k.onCell,W,b.noop,e.modification),insertColumnsAfter:k.run(H,k.onCells,W,b.noop,e.modification),splitCellIntoColumns:k.run(M,k.onCell,W,b.noop,e.modification),splitCellIntoRows:k.run(N,k.onCell,b.noop,b.noop,e.modification),eraseColumns:k.run(O,k.onCells,W,t,e.modification),eraseRows:k.run(P,k.onCells,b.noop,t,e.modification),makeColumnHeader:k.run(J,k.onCell,b.noop,b.noop,e.transform("row","th")),unmakeColumnHeader:k.run(L,k.onCell,b.noop,b.noop,e.transform(null,"td")),makeRowHeader:k.run(I,k.onCell,b.noop,b.noop,e.transform("col","th")),unmakeRowHeader:k.run(K,k.onCell,b.noop,b.noop,e.transform(null,"td")),mergeCells:k.run(Q,k.onMergable,b.noop,b.noop,e.merging),unmergeCells:k.run(R,k.onUnmergable,W,b.noop,e.merging),pasteCells:k.run(S,k.onPaste,W,b.noop,e.modification),pasteRowsBefore:k.run(U,k.onPasteRows,b.noop,b.noop,e.modification),pasteRowsAfter:k.run(V,k.onPasteRows,b.noop,b.noop,e.modification)}}),g("z",["1c","l"],function(a,b){var c=function(a){return b.fromDom(a.getBody())},d=function(b){return function(d){return a.eq(d,c(b))}},e=function(a){return a?a.replace(/px$/,""):""},f=function(a){return/^[0-9]+$/.test(a)&&(a+="px"),a};return{getBody:c,getIsRoot:d,addSizeSuffix:f,removePxSuffix:e}}),g("2u",["22"],function(a){var b=function(a,b){return function(d){return"rtl"===c(d)?b:a}},c=function(b){return"rtl"===a.get(b,"direction")?"rtl":"ltr"};return{onDirection:b,getDirection:c}}),g("10",["f","2u"],function(a,b){var c={isRtl:a.constant(!1)},d={isRtl:a.constant(!0)},e=function(a){var e=b.getDirection(a);return"rtl"===e?d:c};return{directionAt:e}}),g("4",["e","f","g","u","v","i","w","x","l","n","r","y","z","10"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n){return function(o,p){var q,r=function(a){return"table"===j.name(m.getBody(a))},s=function(a){var b=g.getGridSize(a);return r(o)===!1||b.rows()>1},t=function(a){var b=g.getGridSize(a);return r(o)===!1||b.columns()>1},u=function(a){return o.fire("newrow",{node:a.dom()}),a.dom()},v=function(a){return o.fire("newcell",{node:a.dom()}),a.dom()};o.settings.table_clone_elements!==!1&&("string"==typeof o.settings.table_clone_elements?q=o.settings.table_clone_elements.split(/[ ,]/):Array.isArray(o.settings.table_clone_elements)&&(q=o.settings.table_clone_elements));var w=c.from(q),x=function(b,d,g,h){return function(j,m){var p=l.descendants(j,"td[data-mce-style],th[data-mce-style]");a.each(p,function(a){k.remove(a,"data-mce-style")});var q=h(),r=i.fromDom(o.getDoc()),s=e(n.directionAt),t=f.cellOperations(g,r,w);return d(j)?b(q,j,m,t,s).bind(function(b){return a.each(b.newRows(),function(a){u(a)}),a.each(b.newCells(),function(a){v(a)}),b.cursor().map(function(a){var b=o.dom.createRng();return b.setStart(a.dom(),0),b.setEnd(a.dom(),0),b})}):c.none()}},y=x(h.eraseRows,s,b.noop,p),z=x(h.eraseColumns,t,b.noop,p),A=x(h.insertRowsBefore,b.always,b.noop,p),B=x(h.insertRowsAfter,b.always,b.noop,p),C=x(h.insertColumnsBefore,b.always,d.halve,p),D=x(h.insertColumnsAfter,b.always,d.halve,p),E=x(h.mergeCells,b.always,b.noop,p),F=x(h.unmergeCells,b.always,b.noop,p),G=x(h.pasteRowsBefore,b.always,b.noop,p),H=x(h.pasteRowsAfter,b.always,b.noop,p),I=x(h.pasteCells,b.always,b.noop,p);return{deleteRow:y,deleteColumn:z,insertRowsBefore:A,insertRowsAfter:B,insertColumnsBefore:C,insertColumnsAfter:D,mergeCells:E,unmergeCells:F,pasteRowsBefore:G,pasteRowsAfter:H,pasteCells:I}}}),g("11",["1w","2n","2p","1x","2v"],function(a,b,c,d,e){var f=function(f,g,h){var i=a.fromTable(f),j=d.generate(i),k=b.onCells(j,g);return k.map(function(a){var d=c.toGrid(j,h,!1),f=d.slice(a[0].row(),a[a.length-1].row()+a[a.length-1].rowspan()),g=b.toDetailList(f,h);return e.copy(g)})};return{copyRows:f}}),g("14",["d"],function(a){return a("tinymce.util.Tools")}),g("2w",["d"],function(a){return a("tinymce.Env")}),g("2x",["14"],function(a){var b=function(a,b,c){var d,e=a.select("td,th",b),f=function(b,d){for(var e=0;e<d.length;e++){var f=a.getStyle(d[e],c);if("undefined"==typeof b&&(b=f),b!=f)return""}return b};return d=f(d,e)},c=function(a,b,c){c&&a.formatter.apply("align"+c,{},b)},d=function(a,b,c){c&&a.formatter.apply("valign"+c,{},b)},e=function(b,c){a.each("left center right".split(" "),function(a){b.formatter.remove("align"+a,{},c)})},f=function(b,c){a.each("top middle bottom".split(" "),function(a){b.formatter.remove("valign"+a,{},c)})};return{applyAlign:c,applyVAlign:d,unApplyAlign:e,unApplyVAlign:f,getTDTHOverallStyle:b}}),g("2y",["f","14","z"],function(a,b,c){var d=function(a,c,d){var e=function(a,d){return d=d||[],b.each(a,function(a){var b={text:a.text||a.title};a.menu?b.menu=e(a.menu):(b.value=a.value,c&&c(b)),d.push(b)}),d};return e(a,d||[])},e=function(a,b){var d=a.dom,e=b.control.rootControl,f=e.toJSON(),g=d.parseStyle(f.style);"style"===b.control.name()?(e.find("#borderStyle").value(g["border-style"]||"")[0].fire("select"),e.find("#borderColor").value(g["border-color"]||"")[0].fire("change"),e.find("#backgroundColor").value(g["background-color"]||"")[0].fire("change"),e.find("#width").value(g.width||"").fire("change"),e.find("#height").value(g.height||"").fire("change")):(g["border-style"]=f.borderStyle,g["border-color"]=f.borderColor,g["background-color"]=f.backgroundColor,g.width=f.width?c.addSizeSuffix(f.width):"",g.height=f.height?c.addSizeSuffix(f.height):""),e.find("#style").value(d.serializeStyle(d.parseStyle(d.serializeStyle(g))))},f=function(a,b){var c=a.parseStyle(a.getAttrib(b,"style")),d={};return c["border-style"]&&(d.borderStyle=c["border-style"]),c["border-color"]&&(d.borderColor=c["border-color"]),c["background-color"]&&(d.backgroundColor=c["background-color"]),d.style=a.serializeStyle(c),d},g=function(b){var c=function(){var a=b.settings.color_picker_callback;if(a)return function(c){return a.call(b,function(a){c.control.value(a).fire("change")},c.control.value())}};return{title:"Advanced",type:"form",defaults:{onchange:a.curry(e,b)},items:[{label:"Style",name:"style",type:"textbox"},{type:"form",padding:0,formItemDefaults:{layout:"grid",alignH:["start","right"]},defaults:{size:7},items:[{label:"Border style",type:"listbox",name:"borderStyle",width:90,onselect:a.curry(e,b),values:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},{label:"Border color",type:"colorbox",name:"borderColor",onaction:c()},{label:"Background color",type:"colorbox",name:"backgroundColor",onaction:c()}]}]}};return{createStyleForm:g,buildListItems:d,updateStyleField:e,extractAdvancedStyles:f}}),g("15",["f","2w","14","3","2x","z","2y"],function(a,b,c,d,e,f,g){function h(a,b,c,d){if("TD"===b.tagName||"TH"===b.tagName)a.setStyle(b,c,d);else if(b.children)for(var e=0;e<b.children.length;e++)h(a,b.children[e],c,d)}var i=function(a,b){var d=a.dom,f={width:d.getStyle(b,"width")||d.getAttrib(b,"width"),height:d.getStyle(b,"height")||d.getAttrib(b,"height"),cellspacing:d.getStyle(b,"border-spacing")||d.getAttrib(b,"cellspacing"),cellpadding:d.getAttrib(b,"data-mce-cell-padding")||d.getAttrib(b,"cellpadding")||e.getTDTHOverallStyle(a.dom,b,"padding"),border:d.getAttrib(b,"data-mce-border")||d.getAttrib(b,"border")||e.getTDTHOverallStyle(a.dom,b,"border"),borderColor:d.getAttrib(b,"data-mce-border-color"),caption:!!d.select("caption",b)[0],"class":d.getAttrib(b,"class")};return c.each("left center right".split(" "),function(c){a.formatter.matchNode(b,"align"+c)&&(f.align=c)}),a.settings.table_advtab!==!1&&c.extend(f,g.extractAdvancedStyles(d,b)),
f},j=function(a,b,d){var e=a.dom,g={},i={};if(g["class"]=d["class"],i.height=f.addSizeSuffix(d.height),e.getAttrib(b,"width")&&!a.settings.table_style_by_css?g.width=f.removePxSuffix(d.width):i.width=f.addSizeSuffix(d.width),a.settings.table_style_by_css?(i["border-width"]=f.addSizeSuffix(d.border),i["border-spacing"]=f.addSizeSuffix(d.cellspacing),c.extend(g,{"data-mce-border-color":d.borderColor,"data-mce-cell-padding":d.cellpadding,"data-mce-border":d.border})):c.extend(g,{border:d.border,cellpadding:d.cellpadding,cellspacing:d.cellspacing}),a.settings.table_style_by_css&&b.children)for(var j=0;j<b.children.length;j++)h(e,b.children[j],{"border-width":f.addSizeSuffix(d.border),"border-color":d.borderColor,padding:f.addSizeSuffix(d.cellpadding)});d.style?c.extend(i,e.parseStyle(d.style)):i=c.extend({},e.parseStyle(e.getAttrib(b,"style")),i),g.style=e.serializeStyle(i),e.setAttribs(b,g)},k=function(a,c,f){var h,i,k=a.dom;g.updateStyleField(a,f),i=f.control.rootControl.toJSON(),i["class"]===!1&&delete i["class"],a.undoManager.transact(function(){c||(c=d.insert(a,i.cols||1,i.rows||1)),j(a,c,i),h=k.select("caption",c)[0],h&&!i.caption&&k.remove(h),!h&&i.caption&&(h=k.create("caption"),h.innerHTML=b.ie?"\xa0":'<br data-mce-bogus="1"/>',c.insertBefore(h,c.firstChild)),e.unApplyAlign(a,c),i.align&&e.applyAlign(a,c,i.align),a.focus(),a.addVisual()})},l=function(b,c){var d,e,f,h,j,l=b.dom,m={};c===!0?(d=l.getParent(b.selection.getStart(),"table"),d&&(m=i(b,d))):(e={label:"Cols",name:"cols"},f={label:"Rows",name:"rows"}),b.settings.table_class_list&&(m["class"]&&(m["class"]=m["class"].replace(/\s*mce\-item\-table\s*/g,"")),h={name:"class",type:"listbox",label:"Class",values:g.buildListItems(b.settings.table_class_list,function(a){a.value&&(a.textStyle=function(){return b.formatter.getCssText({block:"table",classes:[a.value]})})})}),j={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",labelGapCalc:!1,padding:0,layout:"grid",columns:2,defaults:{type:"textbox",maxWidth:50},items:b.settings.table_appearance_options!==!1?[e,f,{label:"Width",name:"width",onchange:a.curry(g.updateStyleField,b)},{label:"Height",name:"height",onchange:a.curry(g.updateStyleField,b)},{label:"Cell spacing",name:"cellspacing"},{label:"Cell padding",name:"cellpadding"},{label:"Border",name:"border"},{label:"Caption",name:"caption",type:"checkbox"}]:[e,f,{label:"Width",name:"width",onchange:a.curry(g.updateStyleField,b)},{label:"Height",name:"height",onchange:a.curry(g.updateStyleField,b)}]},{label:"Alignment",name:"align",type:"listbox",text:"None",values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},h]},b.settings.table_advtab!==!1?b.windowManager.open({title:"Table properties",data:m,bodyType:"tabpanel",body:[{title:"General",type:"form",items:j},g.createStyleForm(b)],onsubmit:a.curry(k,b,d)}):b.windowManager.open({title:"Table properties",data:m,body:j,onsubmit:a.curry(k,b,d)})};return{open:l}}),g("16",["f","14","2x","z","2y"],function(a,b,c,d,e){function f(a,f,g){function i(a,b,c){c&&l.setAttrib(a,b,c)}function j(a,b,c){c&&l.setStyle(a,b,c)}var k,l=a.dom;e.updateStyleField(a,g),k=g.control.rootControl.toJSON(),a.undoManager.transact(function(){b.each(f,function(b){i(b,"scope",k.scope),i(b,"style",k.style),i(b,"class",k["class"]),j(b,"height",d.addSizeSuffix(k.height)),k.type!==b.parentNode.nodeName.toLowerCase()&&h(a.dom,b,k.type),1===f.length&&c.unApplyAlign(a,b),k.align&&c.applyAlign(a,b,k.align)}),a.focus()})}var g=function(a,c){var d=a.dom,f={height:d.getStyle(c,"height")||d.getAttrib(c,"height"),scope:d.getAttrib(c,"scope"),"class":d.getAttrib(c,"class")};return f.type=c.parentNode.nodeName.toLowerCase(),b.each("left center right".split(" "),function(b){a.formatter.matchNode(c,"align"+b)&&(f.align=b)}),a.settings.table_row_advtab!==!1&&b.extend(f,e.extractAdvancedStyles(d,c)),f},h=function(a,b,c){var d=a.getParent(b,"table"),e=b.parentNode,f=a.select(c,d)[0];f||(f=a.create(c),d.firstChild?"CAPTION"===d.firstChild.nodeName?a.insertAfter(f,d.firstChild):d.insertBefore(f,d.firstChild):d.appendChild(f)),f.appendChild(b),e.hasChildNodes()||a.remove(e)},i=function(c){var d,h,i,j,k,l,m=c.dom,n=[];d=c.dom.getParent(c.selection.getStart(),"table"),h=c.dom.getParent(c.selection.getStart(),"td,th"),b.each(d.rows,function(a){b.each(a.cells,function(b){if(m.getAttrib(b,"data-mce-selected")||b==h)return n.push(a),!1})}),i=n[0],i&&(k=n.length>1?{height:"",scope:"","class":"",align:"",type:i.parentNode.nodeName.toLowerCase()}:g(c,i),c.settings.table_row_class_list&&(j={name:"class",type:"listbox",label:"Class",values:e.buildListItems(c.settings.table_row_class_list,function(a){a.value&&(a.textStyle=function(){return c.formatter.getCssText({block:"tr",classes:[a.value]})})})}),l={type:"form",columns:2,padding:0,defaults:{type:"textbox"},items:[{type:"listbox",name:"type",label:"Row type",text:"Header",maxWidth:null,values:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"listbox",name:"align",label:"Alignment",text:"None",maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height"},j]},c.settings.table_row_advtab!==!1?c.windowManager.open({title:"Row properties",data:k,bodyType:"tabpanel",body:[{title:"General",type:"form",items:l},e.createStyleForm(m)],onsubmit:a.curry(f,c,n)}):c.windowManager.open({title:"Row properties",data:k,body:l,onsubmit:a.curry(f,c,n)}))};return{open:i}}),g("17",["f","14","2x","z","2y"],function(a,b,c,d,e){var f=function(a,b){a.style.cssText+=";"+b},g=function(a,c){var d=a.dom,f={width:d.getStyle(c,"width")||d.getAttrib(c,"width"),height:d.getStyle(c,"height")||d.getAttrib(c,"height"),scope:d.getAttrib(c,"scope"),"class":d.getAttrib(c,"class")};return f.type=c.nodeName.toLowerCase(),b.each("left center right".split(" "),function(b){a.formatter.matchNode(c,"align"+b)&&(f.align=b)}),b.each("top middle bottom".split(" "),function(b){a.formatter.matchNode(c,"valign"+b)&&(f.valign=b)}),a.settings.table_cell_advtab!==!1&&b.extend(f,e.extractAdvancedStyles(d,c)),f},h=function(a,g,h){function i(a,b,c){c&&l.setAttrib(a,b,c)}function j(a,b,c){c&&l.setStyle(a,b,c)}var k,l=a.dom;e.updateStyleField(a,h),k=h.control.rootControl.toJSON(),a.undoManager.transact(function(){b.each(g,function(b){i(b,"scope",k.scope),1===g.length?i(b,"style",k.style):f(b,k.style),i(b,"class",k["class"]),j(b,"width",d.addSizeSuffix(k.width)),j(b,"height",d.addSizeSuffix(k.height)),k.type&&b.nodeName.toLowerCase()!==k.type&&(b=l.rename(b,k.type)),1===g.length&&(c.unApplyAlign(a,b),c.unApplyVAlign(a,b)),k.align&&c.applyAlign(a,b,k.align),k.valign&&c.applyVAlign(a,b,k.valign)}),a.focus()})},i=function(b){var c,d,f,i=[];if(i=b.dom.select("td[data-mce-selected],th[data-mce-selected]"),c=b.dom.getParent(b.selection.getStart(),"td,th"),!i.length&&c&&i.push(c),c=c||i[0]){d=i.length>1?{width:"",height:"",scope:"","class":"",align:"",style:"",type:c.nodeName.toLowerCase()}:g(b,c),b.settings.table_cell_class_list&&(f={name:"class",type:"listbox",label:"Class",values:e.buildListItems(b.settings.table_cell_class_list,function(a){a.value&&(a.textStyle=function(){return b.formatter.getCssText({block:"td",classes:[a.value]})})})});var j={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",layout:"grid",columns:2,labelGapCalc:!1,padding:0,defaults:{type:"textbox",maxWidth:50},items:[{label:"Width",name:"width",onchange:a.curry(e.updateStyleField,b)},{label:"Height",name:"height",onchange:a.curry(e.updateStyleField,b)},{label:"Cell type",name:"type",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{label:"Scope",name:"scope",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{label:"H Align",name:"align",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"V Align",name:"valign",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}]},f]};b.settings.table_cell_advtab!==!1?b.windowManager.open({title:"Cell properties",bodyType:"tabpanel",data:d,body:[{title:"General",type:"form",items:j},e.createStyleForm(b)],onsubmit:a.curry(h,b,i)}):b.windowManager.open({title:"Cell properties",data:d,body:j,onsubmit:a.curry(h,b,i)})}};return{open:i}}),g("5",["e","f","g","11","i","j","12","13","k","l","14","z","o","15","16","17"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p){var q=k.each,r=c.none(),s=function(){return r.fold(function(){},function(b){return a.map(b,function(a){return a.dom()})})},t=function(b){var d=a.map(b,j.fromDom);r=c.from(d)},u=function(k,s,t,u){var v=l.getIsRoot(k),w=function(){var a=j.fromDom(k.dom.getParent(k.selection.getStart(),"th,td")),c=f.table(a,v);c.filter(b.not(v)).each(function(a){var b=j.fromText("");g.after(a,b),h.remove(a);var c=k.dom.createRng();c.setStart(b.dom(),0),c.setEnd(b.dom(),0),k.selection.setRng(c)})},x=function(){return j.fromDom(k.dom.getParent(k.selection.getStart(),"th,td"))},y=function(a){return f.table(a,v)},z=function(a){var b=x(),c=y(b);c.each(function(c){var d=m.forMenu(u,c,b);a(c,d).each(function(a){k.selection.setRng(a),k.focus(),t.clear(c)})})},A=function(a){var f=x(),g=y(f);return g.bind(function(a){var g=j.fromDom(k.getDoc()),h=m.forMenu(u,a,f),i=e.cellOperations(b.noop,g,c.none());return d.copyRows(a,h,i)})},B=function(b){r.each(function(c){var d=a.map(c,function(a){return i.deep(a)}),f=x(),g=y(f);g.bind(function(a){var c=j.fromDom(k.getDoc()),g=e.paste(c),h=m.pasteRows(u,a,f,d,g);b(a,h).each(function(b){k.selection.setRng(b),k.focus(),t.clear(a)})})})};q({mceTableSplitCells:function(){z(s.unmergeCells)},mceTableMergeCells:function(){z(s.mergeCells)},mceTableInsertRowBefore:function(){z(s.insertRowsBefore)},mceTableInsertRowAfter:function(){z(s.insertRowsAfter)},mceTableInsertColBefore:function(){z(s.insertColumnsBefore)},mceTableInsertColAfter:function(){z(s.insertColumnsAfter)},mceTableDeleteCol:function(){z(s.deleteColumn)},mceTableDeleteRow:function(){z(s.deleteRow)},mceTableCutRow:function(a){r=A(),z(s.deleteRow)},mceTableCopyRow:function(a){r=A()},mceTablePasteRowBefore:function(a){B(s.pasteRowsBefore)},mceTablePasteRowAfter:function(a){B(s.pasteRowsAfter)},mceTableDelete:w},function(a,b){k.addCommand(b,a)}),q({mceInsertTable:b.curry(n.open,k),mceTableProps:b.curry(n.open,k,!0),mceTableRowProps:b.curry(o.open,k),mceTableCellProps:b.curry(p.open,k)},function(a,b){k.addCommand(b,function(b,c){a(c)})})};return{registerCommands:u,getClipboardRows:s,setClipboardRows:t}}),g("18",["f","g","l","2z","30"],function(a,b,c,d,e){var f=function(d){var f=b.from(d.dom().documentElement).map(c.fromDom).getOr(d);return{parent:a.constant(f),view:a.constant(d),origin:a.constant(e(0,0))}},g=function(b,c){var e=a.curry(d.absolute,c);return{parent:a.constant(c),view:a.constant(b),origin:e}},h=function(b,c){return{parent:a.constant(c),view:a.constant(b),origin:a.constant(e(0,0))}};return{only:f,detached:g,body:h}}),g("31",["e","1k"],function(a,b){return function(c){var d=b.immutable.apply(null,c),e=[],f=function(a){if(void 0===a)throw"Event bind error: undefined handler";e.push(a)},g=function(b){e=a.filter(e,function(a){return a!==b})},h=function(){var b=d.apply(null,arguments);a.each(e,function(a){a(b)})};return{bind:f,unbind:g,trigger:h}}}),g("32",["1t"],function(a){var b=function(b){var c=a.map(b,function(a){return{bind:a.bind,unbind:a.unbind}}),d=a.map(b,function(a){return a.trigger});return{registry:c,trigger:d}};return{create:b}}),g("5z",["47"],function(a){var b=a.exactly(["compare","extract","mutate","sink"]),c=a.exactly(["element","start","stop","destroy"]),d=a.exactly(["forceDrop","drop","move","delayDrop"]);return{mode:b,sink:c,api:d}}),g("68",["5d"],function(a){var b=a.css("ephox-dragster");return{resolve:b.resolve}}),g("60",["68","49","3n","22","l","13"],function(a,b,c,d,e,f){return function(g){var h=b.merge({layerClass:a.resolve("blocker")},g),i=e.fromTag("div");d.setAll(i,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),c.add(i,a.resolve("blocker")),c.add(i,h.layerClass);var j=function(){return i},k=function(){f.remove(i)};return{element:j,destroy:k}}}),g("5l",["f","l"],function(a,b){var c=function(b,c,d,e,f,g,h){return{target:a.constant(b),x:a.constant(c),y:a.constant(d),stop:e,prevent:f,kill:g,raw:a.constant(h)}},d=function(d,e){return function(f){if(d(f)){var g=b.fromDom(f.target),h=function(){f.stopPropagation()},i=function(){f.preventDefault()},j=a.compose(i,h),k=c(g,f.clientX,f.clientY,h,i,j,f);e(k)}}},e=function(b,c,e,f,g){var i=d(e,f);return b.dom().addEventListener(c,i,g),{unbind:a.curry(h,b,c,i,g)}},f=function(a,b,c,d){return e(a,b,c,d,!1)},g=function(a,b,c,d){return e(a,b,c,d,!0)},h=function(a,b,c,d){a.dom().removeEventListener(b,c,d)};return{bind:f,capture:g}}),g("4l",["f","5l"],function(a,b){var c=a.constant(!0),d=function(a,d,e){return b.bind(a,d,c,e)},e=function(a,d,e){return b.capture(a,d,c,e)};return{bind:d,capture:e}}),g("5i",["5z","60","g","30","4l","12","13"],function(a,b,c,d,e,f,g){var h=function(a,b){return d(b.left()-a.left(),b.top()-a.top())},i=function(a){return c.some(d(a.x(),a.y()))},j=function(a,b){a.mutate(b.left(),b.top())},k=function(c,d){var h=b(d),i=e.bind(h.element(),"mousedown",c.forceDrop),j=e.bind(h.element(),"mouseup",c.drop),k=e.bind(h.element(),"mousemove",c.move),l=e.bind(h.element(),"mouseout",c.delayDrop),m=function(){h.destroy(),j.unbind(),k.unbind(),l.unbind(),i.unbind()},n=function(a){f.append(a,h.element())},o=function(){g.remove(h.element())};return a.sink({element:h.element,start:n,stop:o,destroy:m})};return a.mode({compare:h,extract:i,sink:k,mutate:j})}),g("69",["g","31","32"],function(a,b,c){return function(){var d=a.none(),e=function(){d=a.none()},f=function(b,c){var e=d.map(function(a){return b.compare(a,c)});return d=a.some(c),e},g=function(a,b){var c=b.extract(a);c.each(function(a){var c=f(b,a);c.each(function(a){h.trigger.move(a)})})},h=c.create({move:b(["info"])});return{onEvent:g,reset:e,events:h.registry}}}),g("6a",["f"],function(a){return function(b){var c=function(a,b){};return{onEvent:c,reset:a.noop}}}),g("61",["69","6a"],function(a,b){return function(){var c=b(),d=a(),e=c,f=function(){e.reset(),e=d},g=function(){e.reset(),e=c},h=function(a,b){e.onEvent(a,b)},i=function(){return e===d};return{on:f,off:g,isOn:i,onEvent:h,events:d.events}}}),h("6b",clearTimeout),h("6c",setTimeout),g("62",["6b","6c"],function(a,b){var c=function(c,d){var e=null,f=null,g=function(){null!==e&&(a(e),e=null,f=null)},h=function(){f=arguments,null===e&&(e=b(function(){c.apply(null,f),e=null,f=null},d))};return{cancel:g,throttle:h}},d=function(c,d){var e=null,f=function(){null!==e&&(a(e),e=null)},g=function(){var a=arguments;null===e&&(e=b(function(){c.apply(null,a),e=null,a=null},d))};return{cancel:f,throttle:g}},e=function(c,d){var e=null,f=function(){null!==e&&(a(e),e=null)},g=function(){var f=arguments;null!==e&&a(e),e=b(function(){c.apply(null,f),e=null,f=null},d)};return{cancel:f,throttle:g}};return{adaptable:c,first:d,last:e}}),g("5j",["5z","61","62","31","32","1p"],function(a,b,c,d,e,f){var g=function(g,h,i){var j=!1,k=e.create({start:d([]),stop:d([])}),l=b(),m=function(){t.stop(),l.isOn()&&(l.off(),k.trigger.stop())},n=c.last(m,200),o=function(a){t.start(a),l.on(),k.trigger.start()},p=function(a,b){n.cancel(),l.onEvent(a,h)};l.events.move.bind(function(a){h.mutate(g,a.info())});var q=function(){j=!0},r=function(){j=!1},s=function(a){return function(){var b=f.prototype.slice.call(arguments,0);if(j)return a.apply(null,b)}},t=h.sink(a.api({forceDrop:m,drop:s(m),move:s(p),delayDrop:s(n.throttle)}),i),u=function(){t.destroy()};return{element:t.element,go:o,on:q,off:r,destroy:u,events:k.registry}};return{setup:g}}),g("4i",["5i","5j","1p"],function(a,b,c){var d=function(c,d){var e=void 0!==d?d:{},f=void 0!==e.mode?e.mode:a;return b.setup(c,f,d)};return{transform:d}}),g("5k",["31","32"],function(a,b){return function(){var c=b.create({drag:a(["xDelta","yDelta"])}),d=function(a,b){c.trigger.drag(a,b)};return{mutate:d,events:c.registry}}}),g("4j",["g","31","32","5k"],function(a,b,c,d){return function(){var e=c.create({drag:b(["xDelta","yDelta","target"])}),f=a.none(),g=d();g.events.drag.bind(function(a){f.each(function(b){e.trigger.drag(a.xDelta(),a.yDelta(),b)})});var h=function(b){f=a.some(b)},i=function(){return f};return{assign:h,get:i,mutate:g.mutate,events:e.registry}}}),g("4o",["t"],function(a){var b=function(b){return a.first(b).isSome()},c=function(b,c,d){return a.ancestor(b,c,d).isSome()},d=function(b,c){return a.sibling(b,c).isSome()},e=function(b,c){return a.child(b,c).isSome()},f=function(b,c){return a.descendant(b,c).isSome()},g=function(b,c,d){return a.closest(b,c,d).isSome()};return{any:b,ancestor:c,sibling:d,child:e,descendant:f,closest:g}}),g("33",["4i","f","g","31","32","4j","4b","4k","4h","1c","4l","35","n","r","3n","22","4o","t","2e"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s){var t=h.resolve("resizer-bar-dragging");return function(h,u,v){var w=f(),x=a.transform(w,{}),y=c.none(),z=function(a,b){return c.from(n.get(a,b))};w.events.drag.bind(function(a){z(a.target(),"data-row").each(function(b){var c=i.getInt(a.target(),"top");p.set(a.target(),"top",c+a.yDelta()+"px")}),z(a.target(),"data-column").each(function(b){var c=i.getInt(a.target(),"left");p.set(a.target(),"left",c+a.xDelta()+"px")})});var A=function(a,b){var c=i.getInt(a,b),d=s(n.get(a,"data-initial-"+b),10);return c-d};x.events.stop.bind(function(){w.get().each(function(a){y.each(function(b){z(a,"data-row").each(function(c){var d=A(a,"top");n.remove(a,"data-initial-top"),H.trigger.adjustHeight(b,d,s(c,10))}),z(a,"data-column").each(function(c){var d=A(a,"left");n.remove(a,"data-initial-left"),H.trigger.adjustWidth(b,d,s(c,10))}),g.refresh(h,b,v,u)})})});var B=function(a,b){H.trigger.startAdjust(),w.assign(a),n.set(a,"data-initial-"+b,s(p.get(a,b),10)),o.add(a,t),p.set(a,"opacity","0.2"),x.go(h.parent())},C=k.bind(h.parent(),"mousedown",function(a){g.isRowBar(a.target())&&B(a.target(),"top"),g.isColBar(a.target())&&B(a.target(),"left")}),D=function(a){return j.eq(a,h.view())},E=k.bind(h.view(),"mouseover",function(a){"table"===m.name(a.target())||q.ancestor(a.target(),"table",D)?(y="table"===m.name(a.target())?c.some(a.target()):r.ancestor(a.target(),"table",D),y.each(function(a){g.refresh(h,a,v,u)})):l.inBody(a.target())&&g.destroy(h)}),F=function(){C.unbind(),E.unbind(),x.destroy(),g.destroy(h)},G=function(a){g.refresh(h,a,v,u)},H=e.create({adjustHeight:d(["table","delta","row"]),adjustWidth:d(["table","delta","column"]),startAdjust:d([])});return{destroy:F,refresh:G,on:x.on,off:x.off,hideBars:b.curry(g.hide,h),showBars:b.curry(g.show,h),events:H.registry}}}),g("19",["31","32","2t","33","34"],function(a,b,c,d,e){return function(f,g){var h=e.height,i=d(f,g,h),j=b.create({beforeResize:a(["table"]),afterResize:a(["table"]),startDrag:a([])});return i.events.adjustHeight.bind(function(a){j.trigger.beforeResize(a.table());var b=h.delta(a.delta(),a.table());c.adjustHeight(a.table(),b,a.row(),h),j.trigger.afterResize(a.table())}),i.events.startAdjust.bind(function(a){j.trigger.startDrag()}),i.events.adjustWidth.bind(function(a){j.trigger.beforeResize(a.table());var b=g.delta(a.delta(),a.table());c.adjustWidth(a.table(),b,a.column(),g),j.trigger.afterResize(a.table())}),{on:i.on,off:i.off,hideBars:i.hideBars,showBars:i.showBars,destroy:i.destroy,events:j.registry}}}),g("1a",["18","12","13","35","l","22","z"],function(a,b,c,d,e,f,g){var h=function(){var a=e.fromTag("div");return f.setAll(a,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),b.append(d.body(),a),a},i=function(b,c){return b.inline?a.body(g.getBody(b),h()):a.only(e.fromDom(b.getDoc()))},j=function(a,b){a.inline&&c.remove(b.parent())};return{get:i,remove:j}}),g("6",["e","g","18","v","19","l","r","y","1a","10","14"],function(a,b,c,d,e,f,g,h,i,j,k){return function(l){var m,n,o=b.none(),p=b.none(),q=b.none(),r=/(\d+(\.\d+)?)%/,s=function(a){return"TABLE"===a.nodeName},t=function(a){return l.dom.getStyle(a,"width")||l.dom.getAttrib(a,"width")},u=function(){return p},v=function(){return q.getOr(c.only(f.fromDom(l.getBody())))},w=function(){p.each(function(a){a.destroy()}),q.each(function(a){i.remove(l,a)})};return l.on("init",function(){var c=d(j.directionAt),f=i.get(l);if(q=b.some(f),l.settings.object_resizing&&l.settings.table_resize_bars!==!1&&(l.settings.object_resizing===!0||"table"===l.settings.object_resizing)){var k=e(f,c);k.on(),k.events.startDrag.bind(function(a){o=b.some(l.selection.getRng())}),k.events.afterResize.bind(function(b){var c=b.table(),d=h.descendants(c,"td[data-mce-style],th[data-mce-style]");a.each(d,function(a){g.remove(a,"data-mce-style")}),o.each(function(a){l.selection.setRng(a),l.focus()}),l.undoManager.add()}),p=b.some(k)}}),l.on("ObjectResizeStart",function(a){s(a.target)&&(m=a.width,n=t(a.target))}),l.on("ObjectResized",function(a){if(s(a.target)){var b=a.target;if(r.test(n)){var c=parseFloat(r.exec(n)[1],10),d=a.width*c/m;l.dom.setStyle(b,"width",d+"%")}else{var e=[];k.each(b.rows,function(a){k.each(a.cells,function(a){var b=l.dom.getStyle(a,"width",!0);e.push({cell:a,width:b})})}),k.each(e,function(a){l.dom.setStyle(a.cell,"width",a.width),l.dom.setAttrib(a.cell,"width",null)})}}}),{lazyResize:u,lazyWire:v,destroy:w}}}),g("36",[],function(){var a=function(a){return e(function(b,c,d,e){return b(a)})},b=function(a){return e(function(b,c,d,e){return c(a)})},c=function(a,b){return e(function(c,d,e,f){return e(a,b)})},d=function(a){return e(function(b,c,d,e){return e(a)})},e=function(a){return{fold:a}};return{none:a,first:b,middle:c,last:d}}),g("1b",["e","f","36","j","1c"],function(a,b,c,d,e){var f=function(c,f){return d.table(c,f).bind(function(f){var g=d.cells(f),h=a.findIndex(g,function(a){return e.eq(c,a)});return h.map(function(a){return{index:b.constant(a),all:b.constant(g)}})})},g=function(a,b){var d=f(a,b);return d.fold(function(){return c.none(a)},function(b){return b.index()+1<b.all().length?c.middle(a,b.all()[b.index()+1]):c.last(a)})},h=function(a,b){var d=f(a,b);return d.fold(function(){return c.none()},function(b){return b.index()-1>=0?c.middle(a,b.all()[b.index()-1]):c.first(a)})};return{next:g,prev:h}}),g("37",["2g","f"],function(a,b){var c=a.generate([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),d=function(a,b,c,d){return a.fold(b,c,d)},e=function(a){return a.fold(b.identity,b.identity,b.identity)};return{before:c.before,on:c.on,after:c.after,cata:d,getStart:e}}),g("1e",["2g","1k","l","1m","37"],function(a,b,c,d,e){var f=a.generate([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),g=b.immutable("start","soffset","finish","foffset"),h=function(a){return f.exact(a.start(),a.soffset(),a.finish(),a.foffset())},i=function(a){return a.match({domRange:function(a){return c.fromDom(a.startContainer)},relative:function(a,b){return e.getStart(a)},exact:function(a,b,c,d){return a}})},j=function(a){var b=i(a);return d.defaultView(b)};return{domRange:f.domRange,relative:f.relative,exact:f.exact,exactFromRange:h,range:g,getWin:j}}),g("38",["1c","l","1m"],function(a,b,c){var d=function(a,b,d,e){var f=c.owner(a),g=f.dom().createRange();return g.setStart(a.dom(),b),g.setEnd(d.dom(),e),g},e=function(a,c,e,f){var g=d(a,c,e,f);return b.fromDom(g.commonAncestorContainer)},f=function(b,c,e,f){var g=d(b,c,e,f),h=a.eq(b,e)&&c===f;return g.collapsed&&!h};return{after:f,commonAncestorContainer:e}}),g("39",["e","l","23"],function(a,b,c){var d=function(d,e){var f=e||c,g=f.createDocumentFragment();return a.each(d,function(a){g.appendChild(a.dom())}),b.fromDom(g)};return{fromElements:d}}),g("3a",["f","g","1c","l"],function(a,b,c,d){var e=function(a,b){var c=a.document.createRange();return f(c,b),c},f=function(a,b){a.selectNodeContents(b.dom())},g=function(a,b){return b.compareBoundaryPoints(a.END_TO_START,a)<1&&b.compareBoundaryPoints(a.START_TO_END,a)>-1},h=function(a){return a.document.createRange()},i=function(a,b){b.fold(function(b){a.setStartBefore(b.dom())},function(b,c){a.setStart(b.dom(),c)},function(b){a.setStartAfter(b.dom())})},j=function(a,b){b.fold(function(b){a.setEndBefore(b.dom())},function(b,c){a.setEnd(b.dom(),c)},function(b){a.setEndAfter(b.dom())})},k=function(a,b){o(a),a.insertNode(b.dom())},l=function(a,b,d,e){return c.eq(a,d)&&b===e},m=function(a,b,c){var d=a.document.createRange();return i(d,b),j(d,c),d},n=function(a,b,c,d,e){var f=a.document.createRange();return f.setStart(b.dom(),c),f.setEnd(d.dom(),e),f},o=function(a){a.deleteContents()},p=function(a){var b=a.cloneContents();return d.fromDom(b)},q=function(b){return{left:a.constant(b.left),top:a.constant(b.top),right:a.constant(b.right),bottom:a.constant(b.bottom),width:a.constant(b.width),height:a.constant(b.height)}},r=function(a){var c=a.getClientRects(),d=c.length>0?c[0]:a.getBoundingClientRect();return d.width>0||d.height>0?b.some(d).map(q):b.none()},s=function(a){var c=a.getBoundingClientRect();return c.width>0||c.height>0?b.some(c).map(q):b.none()},t=function(a){return a.toString()};return{create:h,replaceWith:k,selectNodeContents:e,selectNodeContentsUsing:f,isCollapsed:l,relativeToNative:m,exactToNative:n,deleteContents:o,cloneFragment:p,getFirstRect:r,getBounds:s,isWithin:g,toString:t}}),g("1n",["2g","f","g","3b","l","3a"],function(a,b,c,d,e,f){var g=a.generate([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),h=function(a,b,c){return b(e.fromDom(c.startContainer),c.startOffset,e.fromDom(c.endContainer),c.endOffset)},i=function(a,e){return e.match({domRange:function(a){return{ltr:b.constant(a),rtl:c.none}},relative:function(b,e){return{ltr:d.cached(function(){return f.relativeToNative(a,b,e)}),rtl:d.cached(function(){return c.some(f.relativeToNative(a,e,b))})}},exact:function(b,e,g,h){return{ltr:d.cached(function(){return f.exactToNative(a,b,e,g,h)}),rtl:d.cached(function(){return c.some(f.exactToNative(a,g,h,b,e))})}}})},j=function(a,b){var c=b.ltr();if(c.collapsed){var d=b.rtl().filter(function(a){return a.collapsed===!1});return d.map(function(a){return g.rtl(e.fromDom(a.endContainer),a.endOffset,e.fromDom(a.startContainer),a.startOffset)}).getOrThunk(function(){return h(a,g.ltr,c)})}return h(a,g.ltr,c)},k=function(a,b){var c=i(a,b);return j(a,c)},l=function(a,b){var c=k(a,b);return c.match({ltr:function(b,c,d,e){var f=a.document.createRange();return f.setStart(b.dom(),c),f.setEnd(d.dom(),e),f},rtl:function(b,c,d,e){var f=a.document.createRange();return f.setStart(d.dom(),e),f.setEnd(b.dom(),c),f}})};return{ltr:g.ltr,rtl:g.rtl,diagnose:k,asLtrRange:l}}),g("5m",["3v"],function(a){var b=function(b,c,d,e,f){if(0===f)return 0;if(c===e)return f-1;for(var g=e,h=1;h<f;h++){var i=b(h),j=a.abs(c-i.left);if(d>i.bottom);else{if(d<i.top||j>g)return h-1;g=j}}return 0},c=function(a,b,c){return b>=a.left&&b<=a.right&&c>=a.top&&c<=a.bottom};return{inRect:c,searchForPoint:b}}),g("5n",["g","3m","1l","5m","3v"],function(a,b,c,d,e){var f=function(a,b,e,f,g){var h=function(c){var d=a.dom().createRange();return d.setStart(b.dom(),c),d.collapse(!0),d},i=function(a){var b=h(a);return b.getBoundingClientRect()},j=c.get(b).length,k=d.searchForPoint(i,e,f,g.right,j);return h(k)},g=function(c,e,g,h){var i=c.dom().createRange();i.selectNode(e.dom());var j=i.getClientRects(),k=b.findMap(j,function(b){return d.inRect(b,g,h)?a.some(b):a.none()});return k.map(function(a){return f(c,e,g,h,a)})};return{locate:g}}),g("4p",["g","3m","n","1m","5m","5n","3v"],function(a,b,c,d,e,f,g){var h=function(c,f,g,h){var j=c.dom().createRange(),k=d.children(f);return b.findMap(k,function(b){return j.selectNode(b.dom()),e.inRect(j.getBoundingClientRect(),g,h)?i(c,b,g,h):a.none()})},i=function(a,b,d,e){var g=c.isText(b)?f.locate:h;return g(a,b,d,e)},j=function(a,b,c,d){var e=a.dom().createRange();e.selectNode(b.dom());var f=e.getBoundingClientRect(),h=g.max(f.left,g.min(f.right,c)),j=g.max(f.top,g.min(f.bottom,d));return i(a,b,h,j)};return{locate:j}}),g("4q",["g","1m","1d"],function(a,b,c){var d=!0,e=!1,f=function(a,b){return b-a.left<a.right-b?d:e},g=function(a,b,c){var d=a.dom().createRange();return d.selectNode(b.dom()),d.collapse(c),d},h=function(a,b,e){var h=a.dom().createRange();h.selectNode(b.dom());var i=h.getBoundingClientRect(),j=f(i,e),k=j===d?c.first:c.last;return k(b).map(function(b){return g(a,b,j)})},i=function(b,c,d){var e=c.dom().getBoundingClientRect(),h=f(e,d);return a.some(g(b,c,h))},j=function(a,c,d){var e=0===b.children(c).length?i:h;return e(a,c,d)};return{search:j}}),g("3c",["g","l","1m","1e","4p","4q","23","3v"],function(a,b,c,d,e,f,g,h){var i=function(b,c,d){return a.from(b.dom().caretPositionFromPoint(c,d)).bind(function(c){if(null===c.offsetNode)return a.none();var d=b.dom().createRange();return d.setStart(c.offsetNode,c.offset),d.collapse(),a.some(d)})},j=function(b,c,d){return a.from(b.dom().caretRangeFromPoint(c,d))},k=function(a,b,c,d){var f=a.dom().createRange();f.selectNode(b.dom());var g=f.getBoundingClientRect(),i=h.max(g.left,h.min(g.right,c)),j=h.max(g.top,h.min(g.bottom,d));return e.locate(a,b,i,j)},l=function(a,d,e){return b.fromPoint(a,d,e).bind(function(b){var g=function(){return f.search(a,b,d)};return 0===c.children(b).length?g():k(a,b,d,e).orThunk(g)})},m=g.caretPositionFromPoint?i:g.caretRangeFromPoint?j:l,n=function(a,c,e){var f=b.fromDom(a.document);return m(f,c,e).map(function(a){return d.range(b.fromDom(a.startContainer),a.startOffset,b.fromDom(a.endContainer),a.endOffset)})};return{fromPoint:n}}),g("3d",["e","l","n","y","27","3a","1n"],function(a,b,c,d,e,f,g){var h=function(b,c,g,h){var i=f.create(b),j=e.is(c,h)?[c]:[],k=j.concat(d.descendants(c,h));return a.filter(k,function(a){return f.selectNodeContentsUsing(i,a),f.isWithin(g,i)})},i=function(a,d,e){var f=g.asLtrRange(a,d),i=b.fromDom(f.commonAncestorContainer);return c.isElement(i)?h(a,i,f,e):[]};return{find:i}}),g("3e",["e","l","n","1e","37"],function(a,b,c,d,e){var f=function(b,d){var f=c.name(b);return"input"===f?e.after(b):a.contains(["br","img"],f)?0===d?e.before(b):e.after(b):e.on(b,d)},g=function(a,b){var c=a.fold(e.before,f,e.after),g=b.fold(e.before,f,e.after);return d.relative(c,g)},h=function(a,b,c,e){var g=f(a,b),h=f(c,e);return d.relative(g,h)},i=function(a){return a.match({domRange:function(a){var c=b.fromDom(a.startContainer),d=b.fromDom(a.endContainer);return h(c,a.startOffset,d,a.endOffset)},relative:g,exact:h})};return{beforeSpecial:f,preprocess:i,preprocessRelative:g,preprocessExact:h}}),g("1f",["g","38","l","39","1m","1e","3a","1n","3c","3d","3e"],function(a,b,c,d,e,f,g,h,i,j,k){var l=function(b,c){a.from(b.getSelection()).each(function(a){a.removeAllRanges(),a.addRange(c)})},m=function(a,b,c,d,e){var f=g.exactToNative(a,b,c,d,e);l(a,f)},n=function(a,b,c){return j.find(a,b,c)},o=function(a,b){return h.diagnose(a,b).match({ltr:function(b,c,d,e){m(a,b,c,d,e)},rtl:function(b,c,d,e){var f=a.getSelection();f.extend?(f.collapse(b.dom(),c),f.extend(d.dom(),e)):m(a,d,e,b,c)}})},p=function(a,b,c,d,e){var f=k.preprocessExact(b,c,d,e);o(a,f)},q=function(a,b,c){var d=k.preprocessRelative(b,c);o(a,d)},r=function(a){var b=f.getWin(a).dom(),c=function(a,c,d,e){return g.exactToNative(b,a,c,d,e)},d=k.preprocess(a);return h.diagnose(b,d).match({ltr:c,rtl:c})},s=function(b){if(b.rangeCount>0){var d=b.getRangeAt(0),e=b.getRangeAt(b.rangeCount-1);return a.some(f.range(c.fromDom(d.startContainer),d.startOffset,c.fromDom(e.endContainer),e.endOffset))}return a.none();
},t=function(d){var e=c.fromDom(d.anchorNode),g=c.fromDom(d.focusNode);return b.after(e,d.anchorOffset,g,d.focusOffset)?a.some(f.range(c.fromDom(d.anchorNode),d.anchorOffset,c.fromDom(d.focusNode),d.focusOffset)):s(d)},u=function(a,b){var c=g.selectNodeContents(a,b);l(a,c)},v=function(a,b){var d=g.selectNodeContents(a,b);return f.range(c.fromDom(d.startContainer),d.startOffset,c.fromDom(d.endContainer),d.endOffset)},w=function(b){var c=b.getSelection();return c.rangeCount>0?t(c):a.none()},x=function(a){return w(a).map(function(a){return f.exact(a.start(),a.soffset(),a.finish(),a.foffset())})},y=function(a,b){var c=h.asLtrRange(a,b);return g.getFirstRect(c)},z=function(a,b){var c=h.asLtrRange(a,b);return g.getBounds(c)},A=function(a,b,c){return i.fromPoint(a,b,c)},B=function(a,b){var c=h.asLtrRange(a,b);return g.toString(c)},C=function(a){var b=a.getSelection();b.removeAllRanges()},D=function(a,b){var c=h.asLtrRange(a,b);return g.cloneFragment(c)},E=function(a,b,c){var e=h.asLtrRange(a,b),f=d.fromElements(c,a.document);g.replaceWith(e,f)},F=function(a,b){var c=h.asLtrRange(a,b);g.deleteContents(c)};return{setExact:p,getExact:w,get:x,setRelative:q,toNative:r,setToElement:u,clear:C,clone:D,replace:E,deleteAt:F,forElement:v,getFirstRect:y,getBounds:z,getAtPoint:A,findWithin:n,getAsString:B}}),g("1g",["d"],function(a){return a("tinymce.util.VK")}),g("7",["e","g","1b","j","1c","l","n","y","t","1d","1e","1f","1g","z","o"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o){var p=function(a,b,d,e){return t(a,b,c.next(d),e)},q=function(a,b,d,e){return t(a,b,c.prev(d),e)},r=function(a,b){var c=k.exact(b,0,b,0);return l.toNative(c)},s=function(b,c){var d=h.descendants(c,"tr");return a.last(d).bind(function(a){return i.descendant(a,"td,th").map(function(a){return r(b,a)})})},t=function(a,c,e,f,g){return e.fold(b.none,b.none,function(b,c){return j.first(c).map(function(b){return r(a,b)})},function(b){return d.table(b,c).bind(function(c){var d=o.noMenu(b);return a.undoManager.transact(function(){f.insertRowsAfter(c,d)}),s(a,c)})})},u=["table","li","dl"],v=function(b,c,h,i){if(b.keyCode===m.TAB){var j=n.getBody(c),k=function(b){var c=g.name(b);return e.eq(b,j)||a.contains(u,c)},l=c.selection.getRng();if(l.collapsed){var o=f.fromDom(l.startContainer);d.cell(o,k).each(function(a){b.preventDefault();var d=b.shiftKey?q:p,e=d(c,k,a,h,i);e.each(function(a){c.selection.setRng(a)})})}}};return{handle:v}}),g("3f",["1k"],function(a){var b=a.immutable("selection","kill");return{response:b}}),g("1j",[],function(){var a=function(a){return function(b){return b===a}},b=a(38),c=a(40),d=function(a){return a>=37&&a<=40};return{ltr:{isBackward:a(37),isForward:a(39)},rtl:{isBackward:a(39),isForward:a(37)},isUp:b,isDown:c,isNavigation:d}});g("4r",["f","l","37","1n"],function(a,b,c,d){var e=function(c,e){var f=d.asLtrRange(c,e);return{start:a.constant(b.fromDom(f.startContainer)),soffset:a.constant(f.startOffset),finish:a.constant(b.fromDom(f.endContainer)),foffset:a.constant(f.endOffset)}},f=function(b,d,e,f){return{start:a.constant(c.on(b,d)),finish:a.constant(c.on(e,f))}};return{convertToRange:e,makeSitus:f}});g("3g",["4r","f","1t","g","l","1e","37","1f"],function(a,b,c,d,e,f,g,h){return function(i){var j=function(a,b){return d.from(i.document.elementFromPoint(a,b)).map(e.fromDom)},k=function(a){return a.dom().getBoundingClientRect()},l=function(a,d,e,g){var j=f.exact(a,d,e,g);return h.getFirstRect(i,j).map(function(a){return c.map(a,b.apply)})},m=function(){return h.get(i).map(function(b){return a.convertToRange(i,b)})},n=function(b){var c=f.relative(b.start(),b.finish());return a.convertToRange(i,c)},o=function(a,c){return h.getAtPoint(i,a,c).map(function(a){return{start:b.constant(g.on(a.start(),a.soffset())),finish:b.constant(g.on(a.finish(),a.foffset()))}})},p=function(){h.clear(i)},q=function(a){h.setToElement(i,a)},r=function(a){h.setExact(i,a.start(),a.soffset(),a.finish(),a.foffset())},s=function(a,b){h.setRelative(i,a,b)},t=function(){return i.innerHeight},u=function(){return i.scrollY},v=function(a,b){i.scrollBy(a,b)};return{elementFromPoint:j,getRect:k,getRangedRect:l,getSelection:m,fromSitus:n,situsFromPoint:o,clearSelection:p,setSelection:r,setRelativeSelection:s,selectContents:q,getInnerHeight:t,getScrollY:u,scrollBy:v}}}),g("3h",["3f","3l","4r","f","g","1c","t","2b","1e","37"],function(a,b,c,d,e,f,g,h,i,j){var k=function(a,b,c,d,h,i,j){return f.eq(c,h)&&d===i?e.none():g.closest(c,"td,th",b).bind(function(c){return g.closest(h,"td,th",b).bind(function(d){return l(a,b,c,d,j)})})},l=function(d,g,i,j,k){if(!f.eq(i,j)){var l=b.identify(i,j,g).getOr([]);if(l.length>0)return k(d,l,i,j),e.some(a.response(e.some(c.makeSitus(i,0,i,h.getEnd(i))),!0))}return e.none()},m=function(a,c,d,e,f){var g=function(a){return f.clear(d),f.selectRange(d,a.boxes(),a.start(),a.finish()),a.boxes()};return b.shiftSelection(e,a,c,f.firstSelectedSelector(),f.lastSelectedSelector()).map(g)};return{sync:k,detect:l,update:m}}),g("5o",["1k"],function(a){var b=a.immutableBag(["left","top","right","bottom"],[]),c=function(a,c){return b({left:a.left(),top:a.top()+c,right:a.right(),bottom:a.bottom()+c})},d=function(a,c){return b({left:a.left(),top:a.top()-c,right:a.right(),bottom:a.bottom()-c})},e=function(a,c){var d=a.bottom()-a.top();return b({left:a.left(),top:c-d,right:a.right(),bottom:c})},f=function(a,c){var d=a.bottom()-a.top();return b({left:a.left(),top:c,right:a.right(),bottom:c+d})},g=function(a,c,d){return b({left:a.left()+c,top:a.top()+d,right:a.right()+c,bottom:a.bottom()+d})},h=function(a){return a.top()},i=function(a){return a.bottom()},j=function(a){return"("+a.left()+", "+a.top()+") -> ("+a.right()+", "+a.bottom()+")"};return{nu:b,moveUp:d,moveDown:c,moveBottomTo:e,moveTopTo:f,getTop:h,getBottom:i,translate:g,toString:j}}),g("5p",["5o","g","n","2b"],function(a,b,c,d){var e=function(a,c,e){return e>=0&&e<d.getEnd(c)?a.getRangedRect(c,e,c,e+1):e>0?a.getRangedRect(c,e-1,c,e):b.none()},f=function(b){return a.nu({left:b.left,top:b.top,right:b.right,bottom:b.bottom})},g=function(a,c){return b.some(a.getRect(c))},h=function(a,d,h){return c.isElement(d)?g(a,d,h).map(f):c.isText(d)?e(a,d,h).map(f):b.none()},i=function(a,e){return c.isElement(e)?g(a,e).map(f):c.isText(e)?a.getRangedRect(e,0,e,d.getEnd(e)).map(f):b.none()};return{getBoxAt:h,getEntireBox:i}}),g("64",["e","g","1k"],function(a,b,c){var d=c.immutable("item","mode"),e=function(a,b,c,e){var g=void 0!==e?e:f;return a.property().parent(b).map(function(a){return d(a,g)})},f=function(a,b,c,e){var f=void 0!==e?e:g;return c.sibling(a,b).map(function(a){return d(a,f)})},g=function(a,b,c,e){var f=void 0!==e?e:g,h=a.property().children(b),i=c.first(h);return i.map(function(a){return d(a,f)})},h=[{current:e,next:f,fallback:b.none()},{current:f,next:g,fallback:b.some(e)},{current:g,next:g,fallback:b.some(f)}],i=function(b,c,d,e,f){var g=void 0!==f?f:h,j=a.find(g,function(a){return a.current===d});return j.bind(function(a){return a.current(b,c,e,a.next).orThunk(function(){return a.fallback.bind(function(a){return i(b,c,a,e)})})})};return{backtrack:e,sidestep:f,advance:g,go:i}}),g("65",["g"],function(a){var b=function(){var b=function(a,b){return a.query().prevSibling(b)},c=function(b){return b.length>0?a.some(b[b.length-1]):a.none()};return{sibling:b,first:c}},c=function(){var b=function(a,b){return a.query().nextSibling(b)},c=function(b){return b.length>0?a.some(b[0]):a.none()};return{sibling:b,first:c}};return{left:b,right:c}}),g("63",["g","64","65"],function(a,b,c){var d=function(c,e,f,g,h,i){var j=b.go(c,e,g,h);return j.bind(function(b){return i(b.item())?a.none():f(b.item())?a.some(b.item()):d(c,b.item(),f,b.mode(),h,i)})},e=function(a,e,f,g){return d(a,e,f,b.sidestep,c.left(),g)},f=function(a,e,f,g){return d(a,e,f,b.sidestep,c.right(),g)};return{left:e,right:f}}),g("5q",["f","63","64","65"],function(a,b,c,d){var e=function(a,b){return 0===a.property().children(b).length},f=function(b,c,d){return h(b,c,a.curry(e,b),d)},g=function(b,c,d){return i(b,c,a.curry(e,b),d)},h=function(a,c,d,e){return b.left(a,c,d,e)},i=function(a,c,d,e){return b.right(a,c,d,e)},j=function(){return{left:d.left,right:d.right}},k=function(a,b,d,e,f){return c.go(a,b,d,e,f)};return{before:f,after:g,seekLeft:h,seekRight:i,walkers:j,walk:k,backtrack:c.backtrack,sidestep:c.sidestep,advance:c.advance}}),g("4w",["56","5q"],function(a,b){var c=a(),d=function(a,d,e){return b.gather(c,a,d,e)},e=function(a,d){return b.before(c,a,d)},f=function(a,d){return b.after(c,a,d)},g=function(a,d,e){return b.seekLeft(c,a,d,e)},h=function(a,d,e){return b.seekRight(c,a,d,e)},i=function(){return b.walkers()},j=function(a,d,e,f){return b.walk(c,a,d,e,f)};return{gather:d,before:e,after:f,seekLeft:g,seekRight:h,walkers:i,walk:j}}),g("4u",["5o","5p","2g","f","g","4w","48","n","2a","3v"],function(a,b,c,d,e,f,g,h,i,j){var k=5,l=100,m=c.generate([{none:[]},{retry:["caret"]}]),n=function(a,b){return a.left()<b.left()||j.abs(b.right()-a.left())<1||a.left()>b.right()},o=function(a,c,e){return i.closest(c,g.isBlock).fold(d.constant(!1),function(c){return b.getEntireBox(a,c).exists(function(a){return n(e,a)})})},p=function(b,c,d,e,f){var g=a.moveDown(f,k);return j.abs(d.bottom()-e.bottom())<1?m.retry(g):d.top()>f.bottom()?m.retry(g):d.top()===f.bottom()?m.retry(a.moveDown(f,1)):o(b,c,f)?m.retry(a.translate(g,k,0)):m.none()},q=function(b,c,d,e,f){var g=a.moveUp(f,k);return j.abs(d.top()-e.top())<1?m.retry(g):d.bottom()<f.top()?m.retry(g):d.bottom()===f.top()?m.retry(a.moveUp(f,1)):o(b,c,f)?m.retry(a.translate(g,k,0)):m.none()},r={point:a.getTop,adjuster:q,move:a.moveUp,gather:f.before},s={point:a.getBottom,adjuster:p,move:a.moveDown,gather:f.after},t=function(a,b,c){return a.elementFromPoint(b,c).filter(function(a){return"table"===h.name(a)}).isSome()},u=function(a,b,c,d,e){return v(a,b,c,b.move(d,k),e)},v=function(a,c,d,f,g){return 0===g?e.some(f):t(a,f.left(),c.point(f))?u(a,c,d,f,g-1):a.situsFromPoint(f.left(),c.point(f)).bind(function(h){return h.start().fold(e.none,function(h,i){return b.getEntireBox(a,h,i).bind(function(b){return c.adjuster(a,h,b,d,f).fold(e.none,function(b){return v(a,c,d,b,g-1)})}).orThunk(function(){return e.some(f)})},e.none)})},w=function(a,b){return a.situsFromPoint(b.left(),b.bottom()+k)},x=function(a,b){return a.situsFromPoint(b.left(),b.top()-k)},y=function(a,b,c){return a.point(b)>c.getInnerHeight()?e.some(a.point(b)-c.getInnerHeight()):a.point(b)<0?e.some(-a.point(b)):e.none()},z=function(a,b,c){var d=a.move(c,k),e=v(b,a,c,d,l).getOr(d);return y(a,e,b).fold(function(){return b.situsFromPoint(e.left(),a.point(e))},function(c){return b.scrollBy(0,c),b.situsFromPoint(e.left(),a.point(e)-c)})};return{tryUp:d.curry(z,r),tryDown:d.curry(z,s),ieTryUp:x,ieTryDown:w,getJumpSize:d.constant(k)}}),g("4v",["2g","3z","1c","t","2b"],function(a,b,c,d,e){var f=a.generate([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),g=function(a,b,c){var d=a.getRect(b),e=a.getRect(c);return e.right>d.left&&e.left<d.right},h=function(a,h,j,k,l,m,n){return d.closest(k,"td,th",n).bind(function(j){return d.closest(h,"td,th",n).map(function(d){return c.eq(j,d)?c.eq(k,j)&&e.getEnd(j)===l?m(d):f.none("in same cell"):b.sharedOne(i,[j,d]).fold(function(){return g(a,d,j)?f.success():m(d)},function(a){return m(d)})})}).getOr(f.none("default"))},i=function(a){return d.closest(a,"tr")},j=function(a,b,c,d,e){return a.fold(b,c,d,e)};return{verify:h,cata:j,adt:f}}),g("5s",["1k"],function(a){var b=a.immutable("element","offset"),c=a.immutable("element","deltaOffset"),d=a.immutable("element","start","finish"),e=a.immutable("begin","end"),f=a.immutable("element","text");return{point:b,delta:c,range:d,points:e,text:f}}),g("66",["e","f","g","1k","1c","2a","y","t","1m"],function(a,b,c,d,e,f,g,h,i){var j=d.immutable("ancestor","descendants","element","index"),k=d.immutable("parent","children","element","index"),l=function(a,b){return f.closest(a,function(a){return i.parent(a).exists(function(a){return e.eq(a,b)})})},m=function(a){return i.parent(a).bind(function(b){var c=i.children(b);return n(c,a).map(function(d){return k(b,c,a,d)})})},n=function(c,d){return a.findIndex(c,b.curry(e.eq,d))},o=function(a,b){return i.parent(a).bind(function(c){var d=g.children(c,b);return n(d,a).map(function(b){return k(c,d,a,b)})})},p=function(a,b,c){return h.closest(a,b).bind(function(b){var d=g.descendants(b,c);return n(d,a).map(function(c){return j(b,d,a,c)})})};return{childOf:l,indexOf:n,indexInParent:m,selectorsInParent:o,descendantsInAncestor:p}}),g("5r",["4v","f","g","5s","n","1l","66","1m","2b","37"],function(a,b,c,d,e,f,g,h,i,j){var k=function(a){return"br"===e.name(a)},l=function(a,b,d){return b(a,d).bind(function(a){return e.isText(a)&&0===f.get(a).trim().length?l(a,b,d):c.some(a)})},m=function(a,b,c){return c.traverse(b).orThunk(function(){return l(b,c.gather,a)}).map(c.relative)},n=function(a,b){return h.child(a,b).filter(k).orThunk(function(){return h.child(a,b-1).filter(k)})},o=function(a,b,c,d){return n(b,c).bind(function(b){return d.traverse(b).fold(function(){return l(b,d.gather,a).map(d.relative)},function(a){return g.indexInParent(a).map(function(a){return j.on(a.parent(),a.index())})})})},p=function(a,c,d,e){var f=k(c)?m(a,c,e):o(a,c,d,e);return f.map(function(a){return{start:b.constant(a),finish:b.constant(a)}})},q=function(b){return a.cata(b,function(a){return c.none("BR ADT: none")},function(){return c.none()},function(a){return c.some(d.point(a,0))},function(a){return c.some(d.point(a,i.getEnd(a)))})};return{tryBr:p,process:q}}),g("4s",["5o","5p","4u","4v","5r","g","5s","26","1c","2b"],function(a,b,c,d,e,f,g,h,i,j){var k=20,l=h.detect(),m=function(a,b,c){return a.getSelection().bind(function(h){return e.tryBr(b,h.finish(),h.foffset(),c).fold(function(){return f.some(g.point(h.finish(),h.foffset()))},function(f){var g=a.fromSitus(f),i=d.verify(a,h.finish(),h.foffset(),g.finish(),g.foffset(),c.failure,b);return e.process(i)})})},n=function(b,c,e,g,h,k){return 0===k?f.none():q(b,c,e,g,h).bind(function(l){var m=b.fromSitus(l),p=d.verify(b,e,g,m.finish(),m.foffset(),h.failure,c);return d.cata(p,function(){return f.none()},function(){return f.some(l)},function(d){return i.eq(e,d)&&0===g?o(b,e,g,a.moveUp,h):n(b,c,d,0,h,k-1)},function(d){return i.eq(e,d)&&g===j.getEnd(d)?o(b,e,g,a.moveDown,h):n(b,c,d,j.getEnd(d),h,k-1)})})},o=function(a,d,e,f,g){return b.getBoxAt(a,d,e).bind(function(b){return p(a,g,f(b,c.getJumpSize()))})},p=function(a,b,c){return l.browser.isChrome()||l.browser.isSafari()||l.browser.isFirefox()||l.browser.isEdge()?b.otherRetry(a,c):l.browser.isIE()?b.ieRetry(a,c):f.none()},q=function(a,c,d,e,f){return b.getBoxAt(a,d,e).bind(function(b){return p(a,f,b)})},r=function(a,b,c){return m(a,b,c).bind(function(d){return n(a,b,d.element(),d.offset(),c,k).map(a.fromSitus)})};return{handle:r}}),g("4t",["2a"],function(a){var b=function(b){return a.first(b).isSome()},c=function(b,c,d){return a.ancestor(b,c,d).isSome()},d=function(b,c,d){return a.closest(b,c,d).isSome()},e=function(b,c){return a.sibling(b,c).isSome()},f=function(b,c){return a.child(b,c).isSome()},g=function(b,c){return a.descendant(b,c).isSome()};return{any:b,ancestor:c,closest:d,sibling:e,child:f,descendant:g}}),g("3i",["3f","3h","4s","4r","e","f","g","26","1c","4t","y","t","1m","2b","1d"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o){var p=h.detect(),q=function(a,b){return j.ancestor(a,function(a){return m.parent(a).exists(function(a){return i.eq(a,b)})})},r=function(a,b,d,e,h){return l.closest(e,"td,th",b).bind(function(e){return l.closest(e,"table",b).bind(function(i){return q(h,i)?c.handle(a,b,d).bind(function(a){return l.closest(a.finish(),"td,th",b).map(function(b){return{start:f.constant(e),finish:f.constant(b),range:f.constant(a)}})}):g.none()})})},s=function(b,c,e,f,h,i){return p.browser.isIE()?g.none():i(f,c).orThunk(function(){return r(b,c,e,f,h).map(function(b){var c=b.range();return a.response(g.some(d.makeSitus(c.start(),c.soffset(),c.finish(),c.foffset())),!0)})})},t=function(b,c){return l.closest(b,"tr",c).bind(function(b){return l.closest(b,"table",c).bind(function(c){var e=k.descendants(c,"tr");return i.eq(b,e[0])?m.prevSibling(c).bind(o.last).map(function(b){var c=n.getEnd(b);return a.response(g.some(d.makeSitus(b,c,b,c)),!0)}):g.none()})})},u=function(b,c){return l.closest(b,"tr",c).bind(function(b){return l.closest(b,"table",c).bind(function(c){var e=k.descendants(c,"tr");return i.eq(b,e[e.length-1])?m.nextSibling(c).bind(o.first).map(function(b){return a.response(g.some(d.makeSitus(b,0,b,0)),!0)}):g.none()})})},v=function(a,c,d,e,f,g,h){return r(a,d,e,f,g).bind(function(a){return b.detect(c,d,a.start(),a.finish(),h)})};return{navigate:s,select:v,firstUpCheck:t,lastDownCheck:u}}),g("3j",["3l","g","1c","t"],function(a,b,c,d){var e=function(a,b){return d.closest(a,"td,th",b)};return function(d,f,g,h){var i=b.none(),j=function(){i=b.none()},k=function(a){h.clear(f),i=e(a.target(),g)},l=function(b){i.each(function(i){h.clear(f),e(b.target(),g).each(function(b){var e=a.identify(i,b,g).getOr([]);(e.length>1||1===e.length&&!c.eq(i,b))&&(h.selectRange(f,e,i,b),d.selectContents(b))})})},m=function(){i.each(j)};return{mousedown:k,mouseover:l,mouseup:m}}}),g("3k",["4u","4v","4w","1m","37"],function(a,b,c,d,e){return{down:{traverse:d.nextSibling,gather:c.after,relative:e.before,otherRetry:a.tryDown,ieRetry:a.ieTryDown,failure:b.adt.failedDown},up:{traverse:d.prevSibling,gather:c.before,relative:e.before,otherRetry:a.tryUp,ieRetry:a.ieTryUp,failure:b.adt.failedUp}}}),g("1h",["3f","1j","3g","3h","3i","3j","3k","3l","f","g","3m","1k","37"],function(a,b,c,d,e,f,g,h,i,j,k,l,m){var n=l.immutable("rows","cols"),o=function(a,b,d,e){var g=c(a),h=f(g,b,d,e);return{mousedown:h.mousedown,mouseover:h.mouseover,mouseup:h.mouseup}},p=function(f,l,o,p){var q=c(f),r=function(){return p.clear(l),j.none()},s=function(c,f,s,t,u,v){var w=c.raw().which,x=c.raw().shiftKey===!0,y=h.retrieve(l,p.selectedSelector()).fold(function(){return b.isDown(w)&&x?i.curry(e.select,q,l,o,g.down,t,f,p.selectRange):b.isUp(w)&&x?i.curry(e.select,q,l,o,g.up,t,f,p.selectRange):b.isDown(w)?i.curry(e.navigate,q,o,g.down,t,f,e.lastDownCheck):b.isUp(w)?i.curry(e.navigate,q,o,g.up,t,f,e.firstUpCheck):j.none},function(c){var e=function(e){return function(){var f=k.findMap(e,function(a){return d.update(a.rows(),a.cols(),l,c,p)});return f.fold(function(){return h.getEdges(l,p.firstSelectedSelector(),p.lastSelectedSelector()).map(function(c){var d=b.isDown(w)||v.isForward(w)?m.after:m.before;return q.setRelativeSelection(m.on(c.first(),0),d(c.table())),p.clear(l),a.response(j.none(),!0)})},function(b){return j.some(a.response(j.none(),!0))})}};return b.isDown(w)&&x?e([n(1,0)]):b.isUp(w)&&x?e([n(-1,0)]):v.isBackward(w)&&x?e([n(0,-1),n(-1,0)]):v.isForward(w)&&x?e([n(0,1),n(1,0)]):b.isNavigation(w)&&x===!1?r:j.none});return y()},t=function(a,c,e,f,g){return h.retrieve(l,p.selectedSelector()).fold(function(){var h=a.raw().which,i=a.raw().shiftKey===!0;return i===!1?j.none():b.isNavigation(h)?d.sync(l,o,c,e,f,g,p.selectRange):j.none()},j.none)};return{keydown:s,keyup:t}};return{mouse:o,keyboard:p}}),g("4x",["e","3n","4n","1p"],function(a,b,c,d){var e=function(c,d){a.each(d,function(a){b.add(c,a)})},f=function(c,d){a.each(d,function(a){b.remove(c,a)})},g=function(c,d){a.each(d,function(a){b.toggle(c,a)})},h=function(c,d){return a.forall(d,function(a){return b.has(c,a)})},i=function(c,d){return a.exists(d,function(a){return b.has(c,a)})},j=function(a){for(var b=a.dom().classList,c=new d(b.length),e=0;e<b.length;e++)c[e]=b.item(e);return c},k=function(a){return c.supports(a)?j(a):c.get(a)};return{add:e,remove:f,toggle:g,hasAll:h,hasAny:i,get:k}}),g("3o",["3n","4x"],function(a,b){var c=function(b){return function(c){a.add(c,b)}},d=function(b){return function(c){a.remove(c,b)}},e=function(a){return function(c){b.remove(c,a)}},f=function(b){return function(c){return a.has(c,b)}};return{addClass:c,removeClass:d,removeClasses:e,hasClass:f}}),g("1i",["e","r","3n","3o","y"],function(a,b,c,d,e){var f=function(b){var f=d.addClass(b.selected()),g=d.removeClasses([b.selected(),b.lastSelected(),b.firstSelected()]),h=function(c){var d=e.descendants(c,b.selectedSelector());a.each(d,g)},i=function(d,e,g,i){h(d),a.each(e,f),c.add(g,b.firstSelected()),c.add(i,b.lastSelected())};return{clear:h,selectRange:i,selectedSelector:b.selectedSelector,firstSelectedSelector:b.firstSelectedSelector,lastSelectedSelector:b.lastSelectedSelector}},g=function(c){var d=function(a){b.remove(a,c.selected()),b.remove(a,c.firstSelected()),b.remove(a,c.lastSelected())},f=function(a){b.set(a,c.selected(),"1")},g=function(b){var f=e.descendants(b,c.selectedSelector());a.each(f,d)},h=function(d,e,h,i){g(d),a.each(e,f),b.set(h,c.firstSelected(),"1"),b.set(i,c.lastSelected(),"1")};return{clear:g,selectRange:h,selectedSelector:c.selectedSelector,firstSelectedSelector:c.firstSelectedSelector,lastSelectedSelector:c.lastSelectedSelector}};return{byClass:f,byAttr:g}}),g("8",["1h","1i","1j","f","g","1k","j","1c","l","n","1l","r","1m","1e","1n","z","10","9"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r){return function(s,t){var u=f.immutableBag(["mousedown","mouseover","mouseup","keyup","keydown"],[]),v=e.none(),w=b.byAttr(r);s.on("init",function(b){var f=s.getWin(),r=p.getBody(s),x=p.getIsRoot(s),y=function(){var a=s.selection,b=i.fromDom(a.getStart()),c=i.fromDom(a.getEnd()),f=g.table(b),j=g.table(c),k=f.bind(function(a){return j.bind(function(b){return h.eq(a,b)?e.some(!0):e.none()})});k.fold(function(){w.clear(r)},d.noop)},z=a.mouse(f,r,x,w),A=a.keyboard(f,r,x,w),B=function(a,b){b.kill()&&a.kill(),b.selection().each(function(a){var b=n.relative(a.start(),a.finish()),c=o.asLtrRange(f,b);s.selection.setRng(c)})},C=function(a){var b=G(a);if(b.raw().shiftKey&&c.isNavigation(b.raw().which)){var d=s.selection.getRng(),e=i.fromDom(d.startContainer),f=i.fromDom(d.endContainer);A.keyup(b,e,d.startOffset,f,d.endOffset).each(function(a){B(b,a)})}},D=function(a){return!(l.has(a,"data-mce-bogus")||"br"===j.name(a)||j.isText(a)&&0===k.get(a).length)},E=function(){var a=i.fromDom(s.getBody()),b=m.lastChild(a),c=function(a){return m.prevSibling(a).bind(function(a){return D(a)?e.some(a):c(a)})};return b.bind(function(a){return D(a)?e.some(a):c(a)})},F=function(a){var b=G(a);t().each(function(a){a.hideBars()}),40===a.which&&E().each(function(a){"table"===j.name(a)&&(s.settings.forced_root_block?s.dom.add(s.getBody(),s.settings.forced_root_block,s.settings.forced_root_block_attrs,"<br/>"):s.dom.add(s.getBody(),"br"))});var d=s.selection.getRng(),e=i.fromDom(s.selection.getStart()),f=i.fromDom(d.startContainer),g=i.fromDom(d.endContainer),h=q.directionAt(e).isRtl()?c.rtl:c.ltr;A.keydown(b,f,d.startOffset,g,d.endOffset,h).each(function(a){B(b,a)}),t().each(function(a){a.showBars()})},G=function(a){var b=i.fromDom(a.target),c=function(){a.stopPropagation()},e=function(){a.preventDefault()},f=d.compose(e,c);return{target:d.constant(b),x:d.constant(a.x),y:d.constant(a.y),stop:c,prevent:e,kill:f,raw:d.constant(a)}},H=function(a){return 0===a.button},I=function(a){return void 0===a.buttons||0!==(1&a.buttons)},J=function(a){H(a)&&z.mousedown(G(a))},K=function(a){I(a)&&z.mouseover(G(a))},L=function(a){H&&z.mouseup(G(a))};s.on("mousedown",J),s.on("mouseover",K),s.on("mouseup",L),s.on("keyup",C),s.on("keydown",F),s.on("nodechange",y),v=e.some(u({mousedown:J,mouseover:K,mouseup:L,keyup:C,keydown:F}))});var x=function(){v.each(function(a){})};return{clear:w.clear,destroy:x}}}),g("a",["1o","z","9","p"],function(a,b,c,d){return function(e){var f=function(){var f=b.getBody(e);return a.retrieve(f,c.selectedSelector()).fold(function(){return void 0===e.selection.getStart()?d.none():d.single(e.selection)},function(a){return d.multiple(a)})};return{get:f}}}),g("b",["f","14","15"],function(a,b,c){var d=b.each,e=function(b){function e(a){return function(){b.execCommand(a)}}var f=[];d("inserttable tableprops deletetable | cell row column".split(" "),function(a){"|"==a?f.push({text:"-"}):f.push(b.menuItems[a])}),b.addButton("table",{type:"menubutton",title:"Table",menu:f}),b.addButton("tableprops",{title:"Table properties",onclick:a.curry(c.open,b,!0),icon:"table"}),b.addButton("tabledelete",{title:"Delete table",onclick:e("mceTableDelete")}),b.addButton("tablecellprops",{title:"Cell properties",onclick:e("mceTableCellProps")}),b.addButton("tablemergecells",{title:"Merge cells",onclick:e("mceTableMergeCells")}),b.addButton("tablesplitcells",{title:"Split cell",onclick:e("mceTableSplitCells")}),b.addButton("tableinsertrowbefore",{title:"Insert row before",onclick:e("mceTableInsertRowBefore")}),b.addButton("tableinsertrowafter",{title:"Insert row after",onclick:e("mceTableInsertRowAfter")}),b.addButton("tabledeleterow",{title:"Delete row",onclick:e("mceTableDeleteRow")}),b.addButton("tablerowprops",{title:"Row properties",onclick:e("mceTableRowProps")}),b.addButton("tablecutrow",{title:"Cut row",onclick:e("mceTableCutRow")}),b.addButton("tablecopyrow",{title:"Copy row",onclick:e("mceTableCopyRow")}),b.addButton("tablepasterowbefore",{title:"Paste row before",onclick:e("mceTablePasteRowBefore")}),b.addButton("tablepasterowafter",{title:"Paste row after",onclick:e("mceTablePasteRowAfter")}),b.addButton("tableinsertcolbefore",{title:"Insert column before",onclick:e("mceTableInsertColBefore")}),b.addButton("tableinsertcolafter",{title:"Insert column after",onclick:e("mceTableInsertColAfter")}),b.addButton("tabledeletecol",{title:"Delete column",onclick:e("mceTableDeleteCol")})},f=function(a){var b=function(b){var c=a.dom.is(b,"table")&&a.getBody().contains(b);return c},c=a.settings.table_toolbar;""!==c&&c!==!1&&(c||(c="tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol"),a.addContextToolbar(b,c))};return{addButtons:e,addToolbars:f}}),g("c",["f","e","g","j","l","3","o","15"],function(a,b,c,d,e,f,g,h){var i=function(i,j){function k(a){return function(){i.execCommand(a)}}var l=c.none(),m=[],n=[],o=[],p=[],q=function(a){a.disabled(!0)},r=function(a){a.disabled(!1)},s=function(){var a=this;m.push(a),l.fold(function(){q(a)},function(b){r(a)})},t=function(){var a=this;n.push(a),l.fold(function(){q(a)},function(b){r(a)})},u=function(){var a=this;o.push(a),l.fold(function(){q(a)},function(b){a.disabled(b.mergable().isNone())})},v=function(){var a=this;p.push(a),l.fold(function(){q(a)},function(b){a.disabled(b.unmergable().isNone())})},w=function(){l.fold(function(){b.each(m,q),b.each(n,q),b.each(o,q),b.each(p,q)},function(a){b.each(m,r),b.each(n,r),b.each(o,function(b){b.disabled(a.mergable().isNone())}),b.each(p,function(b){b.disabled(a.unmergable().isNone())})})};i.on("init",function(){i.on("nodechange",function(a){var b=c.from(i.dom.getParent(i.selection.getStart(),"th,td"));l=b.bind(function(a){var b=e.fromDom(a),c=d.table(b);return c.map(function(a){return g.forMenu(j,a,b)})}),w()})});var x=function(){var a="";a='<table role="grid" class="mce-grid mce-grid-border" aria-readonly="true">';for(var b=0;b<10;b++){a+="<tr>";for(var c=0;c<10;c++)a+='<td role="gridcell" tabindex="-1"><a id="mcegrid'+(10*b+c)+'" href="#" data-mce-x="'+c+'" data-mce-y="'+b+'"></a></td>';a+="</tr>"}return a+="</table>",a+='<div class="mce-text-center" role="presentation">1 x 1</div>'},y=function(a,b,c,d){var e,f,g,h,i,j=d.getEl().getElementsByTagName("table")[0],k=d.isRtl()||"tl-tr"==d.parent().rel;for(j.nextSibling.innerHTML=b+1+" x "+(c+1),k&&(b=9-b),f=0;f<10;f++)for(e=0;e<10;e++)h=j.rows[f].childNodes[e].firstChild,i=(k?e>=b:e<=b)&&f<=c,a.dom.toggleClass(h,"mce-active",i),i&&(g=h);return g.parentNode},z=i.settings.table_grid===!1?{text:"Table",icon:"table",context:"table",onclick:a.curry(h.open,i)}:{text:"Table",icon:"table",context:"table",ariaHideMenu:!0,onclick:function(a){a.aria&&(this.parent().hideAll(),a.stopImmediatePropagation(),h.open(i))},onshow:function(){y(i,0,0,this.menu.items()[0])},onhide:function(){var a=this.menu.items()[0].getEl().getElementsByTagName("a");i.dom.removeClass(a,"mce-active"),i.dom.addClass(a[0],"mce-active")},menu:[{type:"container",html:x(),onPostRender:function(){this.lastX=this.lastY=0},onmousemove:function(a){var b,c,d=a.target;"A"==d.tagName.toUpperCase()&&(b=parseInt(d.getAttribute("data-mce-x"),10),c=parseInt(d.getAttribute("data-mce-y"),10),(this.isRtl()||"tl-tr"==this.parent().rel)&&(b=9-b),b===this.lastX&&c===this.lastY||(y(i,b,c,a.control),this.lastX=b,this.lastY=c))},onclick:function(a){var b=this;"A"==a.target.tagName.toUpperCase()&&(a.preventDefault(),a.stopPropagation(),b.parent().cancel(),i.undoManager.transact(function(){f.insert(i,b.lastX+1,b.lastY+1)}),i.addVisual())}}]},A={text:"Table properties",context:"table",onPostRender:s,onclick:a.curry(h.open,i,!0)},B={text:"Delete table",context:"table",onPostRender:s,cmd:"mceTableDelete"},C={text:"Row",context:"table",menu:[{text:"Insert row before",onclick:k("mceTableInsertRowBefore"),onPostRender:t},{text:"Insert row after",onclick:k("mceTableInsertRowAfter"),onPostRender:t},{text:"Delete row",onclick:k("mceTableDeleteRow"),onPostRender:t},{text:"Row properties",onclick:k("mceTableRowProps"),onPostRender:t},{text:"-"},{text:"Cut row",onclick:k("mceTableCutRow"),onPostRender:t},{text:"Copy row",onclick:k("mceTableCopyRow"),onPostRender:t},{text:"Paste row before",onclick:k("mceTablePasteRowBefore"),onPostRender:t},{text:"Paste row after",onclick:k("mceTablePasteRowAfter"),onPostRender:t}]},D={text:"Column",context:"table",menu:[{text:"Insert column before",onclick:k("mceTableInsertColBefore"),onPostRender:t},{text:"Insert column after",onclick:k("mceTableInsertColAfter"),onPostRender:t},{text:"Delete column",onclick:k("mceTableDeleteCol"),onPostRender:t}]},E={separator:"before",text:"Cell",context:"table",menu:[{text:"Cell properties",onclick:k("mceTableCellProps"),onPostRender:t},{text:"Merge cells",onclick:k("mceTableMergeCells"),onPostRender:u},{text:"Split cell",onclick:k("mceTableSplitCells"),onPostRender:v}]};i.addMenuItem("inserttable",z),i.addMenuItem("tableprops",A),i.addMenuItem("deletetable",B),i.addMenuItem("row",C),i.addMenuItem("column",D),i.addMenuItem("cell",E)};return{addMenuItems:i}}),g("0",["1","2","3","4","5","6","7","8","9","a","b","c"],function(a,b,c,d,e,f,g,h,i,j,k,l){function m(a){var m=this,n=f(a),o=h(a,n.lazyResize),p=d(a,n.lazyWire),q=j(a);e.registerCommands(a,p,o,q),b.registerEvents(a,q,p,o),l.addMenuItems(a,q),k.addButtons(a),k.addToolbars(a),a.on("PreInit",function(){a.serializer.addTempAttr(i.firstSelected()),a.serializer.addTempAttr(i.lastSelected())}),a.settings.table_tab_navigation!==!1&&a.on("keydown",function(b){g.handle(b,a,p,n.lazyWire)}),a.on("remove",function(){n.destroy(),o.destroy()}),m.insertTable=function(b,d){return c.insert(a,b,d)},m.setClipboardRows=e.setClipboardRows,m.getClipboardRows=e.getClipboardRows}return a.add("table",m),function(){}}),d("0")()}();