!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e[f]=d(a[f]);b.apply(null,e)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};g("1",[],function(){var a=function(b){var c=b,d=function(){return c},e=function(a){c=a},f=function(){return a(d())};return{get:d,set:e,clone:f}};return a}),h("5",tinymce.util.Tools.resolve),g("2",["5"],function(a){return a("tinymce.PluginManager")}),g("3",[],function(){var a=function(a){var b=function(){return a.get()};return{isContextMenuVisible:b}};return{get:a}}),g("6",[],function(){var a=function(a){return a.settings.contextmenu_never_use_native},b=function(a){return a.getParam("contextmenu","link openlink image inserttable | cell row column deletetable")};return{shouldNeverUseNative:a,getContextMenu:b}}),g("9",["5"],function(a){return a("tinymce.Env")}),g("a",["5"],function(a){return a("tinymce.dom.DOMUtils")}),g("7",["9","a"],function(a,b){var c=function(a,b){return{x:a,y:b}},d=function(a,b,d){return c(a.x+b,a.y+d)},e=function(a){return c(a.pageX,a.pageY)},f=function(a){return c(a.clientX,a.clientY)},g=function(a,c){if(a&&"static"!==b.DOM.getStyle(a,"position",!0)){var e=b.DOM.getPos(a),f=e.x-a.scrollLeft,g=e.y-a.scrollTop;return d(c,-f,-g)}return d(c,0,0)},h=function(a,c){var e=b.DOM.getPos(a);return d(c,e.x,e.y)},i=function(b){return a.container},j=function(a,b){if(a.inline)return g(i(a),e(b));var c=h(a.getContentAreaContainer(),f(b));return g(i(a),c)};return{getPos:j}}),g("b",["5"],function(a){return a("tinymce.ui.Factory")}),g("c",["5"],function(a){return a("tinymce.util.Tools")}),g("8",["b","c","6"],function(a,b,c){var d=function(d,e){var f,g,h=[];g=c.getContextMenu(d),b.each(g.split(/[ ,]/),function(a){var b=d.menuItems[a];"|"===a&&(b={text:a}),b&&(b.shortcut="",h.push(b))});for(var i=0;i<h.length;i++)"|"===h[i].text&&(0!==i&&i!==h.length-1||h.splice(i,1));return f=a.create("menu",{items:h,context:"contextmenu",classes:"contextmenu"}).renderTo(),f.on("hide",function(a){a.control===this&&e.set(!1)}),d.on("remove",function(){f.remove(),f=null}),f},e=function(a,b,c,e){null===e.get()?e.set(d(a,c)):e.get().show(),e.get().moveTo(b.x,b.y),c.set(!0)};return{show:e}}),g("4",["6","7","8"],function(a,b,c){var d=function(b,c){return c.ctrlKey&&!a.shouldNeverUseNative(b)},e=function(a,e,f){a.on("contextmenu",function(g){d(a,g)||(g.preventDefault(),c.show(a,b.getPos(a,g),e,f))})};return{setup:e}}),g("0",["1","2","3","4"],function(a,b,c,d){return b.add("contextmenu",function(b){var e=a(null),f=a(!1);return d.setup(b,f,e),c.get(f)}),function(){}}),d("0")()}();