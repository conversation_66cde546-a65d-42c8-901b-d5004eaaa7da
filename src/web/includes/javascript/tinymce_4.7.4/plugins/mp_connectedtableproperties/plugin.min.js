
tinymce.PluginManager.add('mp_connectedtableproperties', function(ed, url) {

	function generatePanel(panelProperties) {

		var menuHTML = "";

		menuHTML += "<div id='mce-custompanel-table' class='mce-container mce-panel mce-custompanel mce-menu' role='application' style='position: absolute; border-width: 1px; max-height: none; height: " + panelProperties.height + "px; min-width: " + panelProperties.width + "px; padding-top: 0px; padding-bottom: 0px; z-index: 9999;'>" +
			"<div class='mcePanelNameContainer mce-window-head'>" +
			"<div class='mce-title'>" +
			panelProperties.title +
			"</div>" +
			"<button type='button' class='mce-close' aria-hidden='true'><i class='mce-ico mce-i-remove'></i></button>" +
			"</div>" +
			"<div class='mce-container-body mce-stack-layout' role='menu' style='max-height: " + panelProperties.height + "px; padding-top: 5px; padding-bottom: 0px; overflow-y: auto; overflow-x: auto;'>" +
			"<iframe name='tablePanelPropertiesIframe' src='" + panelProperties.url + "' tabindex='-1' style='width:" + panelProperties.width + "px; height: " + (panelProperties.height - 60) + "px'></iframe>" +
			"</div>" +
			"</div>";

		menuHTML = $(menuHTML);

		$(getTopFrame().document).find('#mce-custompanel-table .mcePanelNameContainer').disableSelection();

		return menuHTML;

	}

	function togglePanel(persistedState, panelProperties) {
		if ( ed.settings.table.ele != null) {
			if ( $(ed.settings.table.ele).is(':visible') ) {
				// Highlight on parent menu item hover
				$(ed.settings.table.ele).animate({
					'border-color': 'purple'
				}, 250, function() {
					$(ed.settings.table.ele).animate({
						'border-color': '#e2e4e7'
					}, 250);
				});
			} else {
				positionPanel();
				$(ed.settings.table.ele).show();
			}
		} else {
			ed.settings.table.ele = $(generatePanel(panelProperties));

			$(getTopFrame().document).find('body').append(ed.settings.table.ele);

			getTopFrame().initDraggable($(ed.settings.table.ele), {
				iframeFix: true,
				handle: '.mcePanelNameContainer',
				containment: 'document',
				stop: function (event, ui) {
					if (typeof (Storage) !== "undefined") {
						storeOnLoadState(true, $(ed.settings.table.ele).offset().left, $(ed.settings.table.ele).offset().top);
					}

				}
			});

			positionPanel();
		}
	}

	function storeOnLoadState(toggleState, left, top) {
		if ( ed.settings.general && ed.settings.general.persist_workspace == false )
			return;

		if ( typeof(Storage)!=="undefined" ) {
			localStorage.setItem("msgpt_editor_onloadstate_tablepanel", JSON.stringify({
				state: toggleState,
				x: left,
				y: top
			}));
		}
	}

	function closePanel() {
		if (tablePanelIsOpen()) {
			if(confirmPanelClose()) {
				storeOnLoadState(false, $(getTopFrame().document).find('#mce-custompanel-table').offset().left, $(getTopFrame().document).find('#mce-custompanel-table').offset().top);
				$(getTopFrame().document).find('#mce-custompanel-table').remove();
				ed.settings.table.ele = null;
				ed.settings.table.selected_ele_lock = false;

				if (ed.prinovaPlugins.tagpanel != undefined) {
					ed.execCommand("mceRestoreTagPanel", false);
				}
			}
		}
		$(document).find('#mce-custompanel-table .mce-close').unbind();
	}

	function refreshPanel() {
		$(getTopFrame().document).find('#mce-custompanel-table .mce-close').unbind();
		var iframe = window.frames['tablePanelPropertiesIframe'];
		if (!iframe || !iframe.init) return;
		iframe.init(true);
	}

	function hasPanelChanges() {
		var iframe = window.frames['tablePanelPropertiesIframe'];
		if (!iframe || !iframe.getHasNewChange) return;
		return iframe.getHasNewChange();
	}

	function setPanelHasNewChange(value) {
		var iframe = window.frames['tablePanelPropertiesIframe'];
		if (!iframe || !iframe.setHasNewChange) return;
		iframe.setHasNewChange(value);
	}

	function tablePanelIsOpen() {
		return $(document).find('#mce-custompanel-table').length > 0;
	}

	function confirmPanelClose() {
		if (hasPanelChanges()) {
			return window.confirm(client_messages.text.warning_object_change_current_table);
		}
		return true;
	}

	function positionPanel() {

		if ( ed.settings.table.ele == null )
			return;

		var pos_x, pos_y;
		var scrollTop = $(window).scrollTop();
		var height = $(ed.settings.table.ele).height();
		var width = $(ed.settings.table.ele).width();
		var windowHeight = $(window).height();
		var windowWidth = $(window).width();
		pos_y = ((windowHeight - height) / 2) + scrollTop;
		pos_x = (windowWidth - width) / 2;

		if ( (!ed.settings.general || ed.settings.general.persist_workspace != false ) &&
			localStorage.getItem("msgpt_editor_onloadstate_tablepanel") != null ) {
			try {
				var tablePanelState = JSON.parse( localStorage.getItem("msgpt_editor_onloadstate_tablepanel") );
				pos_x = tablePanelState.x;
				pos_y = tablePanelState.y;
			} catch (e) {
				console.log(e)
			}
		}

		$(ed.settings.table.ele).css({
			top: pos_y + 'px',
			left: pos_x + 'px'
		});

	}

	function prepareTable(type, ele) {
		var stampDate = new Date();
		setTimeout( function() {
			// Floating toolbar (tables)
			$(ed.container).closest('body').find('.mce-tinymce-inline').css({ 'z-index': 999 });
		}, 10);

		// Calculate window width (based on number of columns)
		var tableCols = 0;
		var targetTable = $(ed.settings.table.selected_ele).closest('table');
		if ( ele != undefined && ele != null && $(ele).length != 0 ) {
			targetTable = $(ele).closest('table');
			ed.settings.table.selected_ele_lock = true;
			ed.settings.table.selected_ele = targetTable;
		}

		// Non editable content: Get closest editable table selection
		if ( $(targetTable).closest('.staticContentItem').length != 0 ) {
			while ( $(targetTable).parent().closest('.staticContentItem').length != 0 )
				targetTable = $(targetTable).parent();
			targetTable = $(targetTable).closest('table');
			ed.settings.table.selected_ele_lock = true;
			ed.settings.table.selected_ele = targetTable;
		}

		if ( !$(targetTable).is('table') && ed.settings.table.last_node_change_table != null ) {
			targetTable = $(ed.settings.table.last_node_change_table)
			ed.settings.table.selected_ele = targetTable;
		}

		if ( !$(targetTable).is('table') )
			return;

		$(targetTable).find('tr:first').closestDescendant('td',true).each( function() {
			tableCols += $(this).attr('colspan') ? parseInt( $(this).attr('colspan') ) : 1;
		});

		var urlParam = "&type=" + type + "&cacheStamp=" + (stampDate.getTime());

		var parentTable = $(targetTable).parent('table');
		if ( $(parentTable).length != 0 && $(parentTable).attr('data_group_id') != undefined )
			urlParam += "&dataGroupId=" + $(parentTable).attr('data_group_id');
		if ( ed.settings.zone_id )
			urlParam += "&zoneId=" + (ed.settings.zone_id ? ed.settings.zone_id : 0);

		pollTableContent();

		var tablePanelProperties = {
			title	: client_messages.content_editor.table_properties,
			url		: ed.settings.table_properties_url + urlParam,
			width	: Math.min( Math.max( (tableCols * 82) + 325 + 7, 725), 970),
			height	: 700,
		};
		ed.settings.table.savedBookmark = ed.selection.getBookmark();
		return tablePanelProperties;
	}

	function updateTableSettingsAndRestoreTagPanel() {
		ed.settings.table.selected_ele = ed.selection.getStart();
		ed.settings.table.last_selection_content = ed.selection.getContent();
		ed.settings.table.selected_ele_lock = false;
		if (ed.prinovaPlugins.tagpanel != undefined) {
			ed.execCommand("mceRestoreTagPanel", false);
		}
	}

	function openTablePropertiesDialogue(type, ele) {
		var tablePanelProperties = prepareTable(type, ele);
		togglePanel(null, tablePanelProperties);
	};

	function refreshTablePropertiesDialogue(type, ele) {
		prepareTable(type, ele);
		refreshPanel();
	};

	function allArrayValuesEqual(targetArray) {
		if ( targetArray.length == 1)
			return true;

		var currentValue = targetArray[0];
		for (var i=1; i < targetArray.length; i++)
			if ( currentValue != targetArray[i])
				return false;

		return true;
	}

	function enforceCursorPosition(e) {
		var cursorFocus;

		var target = e != undefined && e.target != undefined ? $(e.target) : $(ed.selection.getStart());
		if ( $(target).is('td') )
			cursorFocus = $(target).children('p:first');
		else if ( $(target).is('tr') )
			cursorFocus = $(target).children('td:first').children('p:first');
		else if ( $(target).closest('.mceStaticElement').length != 0 )
			cursorFocus = $(target).closest('td').children('p:first');

		if ( cursorFocus != undefined && cursorFocus.length != 0 )
			ed.selection.setCursorLocation($(cursorFocus).get(0), 0);
	}

	function getStyleAttr(ele, attr) {
		if ( $(ele).attr('style') ) {
			var attrArray = $(ele).attr('style').split(";");
			for ( var i = 0; i < attrArray.length; i++ ) {
				var currentAttr = attrArray[i].split(":");
				if ( $.trim(currentAttr[0]) == attr )
					return $.trim(currentAttr[1]);
			}
		}
		return null;
	}

	// !IMPORTANT!  This function is called repetitively.  Minimize executed code.
	var pollTableContent = function() {

		// MERGE CORNER CASE: ex. <tr><td colspan="2"/></tr><tr/>
		function cleanEmptyRowColSpan(table) {

			$(table).find('tr:empty').each( function() {
				var prevRowCell = $(this).prev('tr').find('td');
				if ( prevRowCell.length == 1 && $(prevRowCell).has('rowspan') ) {
					var rowspan = parseInt($(prevRowCell).attr('rowspan'));
					$(prevRowCell).attr('rowspan', rowspan - 1 == 1 ? null : rowspan - 1);
					$(this).remove();
				}
			});

		}
		// NEW TABLE: Init
		function newTableInit(table) {

			// BORDER: Set default borders for new tables
			function applyDefaultCellBorders(table) {

				var rowCount = $(table).find('tr').length;
				var rowIndex = 1;
				$(table).find('tr').each( function() {
					var colIndex = 1;
					$(this).find('td').each( function() {
						var isFirstColumn = (colIndex == 1);
						var isLastRow = (rowIndex == rowCount);

						ed.dom.setAttrib( $(this), 'cell_border', '1px solid rgb(0,0,0):1px solid rgb(0,0,0):1px solid rgb(0,0,0):1px solid rgb(0,0,0)');
						ed.dom.setAttrib( $(this), 'collapsed_cell_border', '1px solid rgb(0,0,0):1px solid rgb(0,0,0):' + (isLastRow ? '1px solid rgb(0,0,0)' : 'none') + ':' + (isFirstColumn ? '1px solid rgb(0,0,0)' : 'none'));
						ed.dom.setAttrib( $(this), 'style', 'border-top: 1px solid rgb(0,0,0); border-right: 1px solid rgb(0,0,0); border-bottom: ' + (isLastRow ? '1px solid rgb(0,0,0)' : 'none') + '; border-left: ' + (isFirstColumn ? '1px solid rgb(0,0,0)' : 'none') + ';' + $(this).attr('style'));

						colIndex++;
					});
					rowIndex++;
				});
				$(table).removeAttr('new_table');

			}

			// CELL WIDTH: Convert relative cell sizing to fixed sizing for print contexts
			function applyFixedCellWidths(table) {
				var tW = $(table).innerWidth();
				$(table).find('td').each( function() {
					var cW = getStyleAttr(this, 'width');
					if ( cW == null || cW.indexOf('%') == -1 )
						cW = 100;
					cW = (tW * (parseFloat(cW.replace('%','')) / 100) ) + 'px';
					$(this).css({
						'width' : cW,
						'min-width' : cW,
						'max-width' : cW,
					})

				});

			}

			if ( $(table).attr('new_table') && $(table).attr('new_table') == 'true' ) {
				applyDefaultCellBorders(table);

				if ( ed.settings.mp_fn.qualify_channel_connectors({'channels': [2],'connectors': [] }) )
					applyFixedCellWidths(table);

				ed.fire("mceNewTable", {'table': table});
			}

		} // END: NEW TABLE: Init
		// LAYOUT: Set default cell sizing
		function setDefaultCellSizing(table) {

			var units = null;
			$(table).closestDescendant('td,th',true).each( function() {

				// REPAIR: WIDTH
				if ( getStyleAttr(this, 'max-width') != null && getStyleAttr(this, 'width') == null )
					ed.dom.setStyle( $(this), 'width', getStyleAttr(this, 'max-width'));

				// WIDTH
				if ( !$(this).attr('style') || getStyleAttr(this, 'width') == null ) {
					var tW = $(this).closest('.mceEditableContainer,body').width();
					if ( $(this).closest('table').parent().closest('table').length != 0 )
						tW = $(this).parent().closest('td').width();

					var cellWidths = new Array();
					// RESOLVE: Cell widths and applied units (units cannot be mixed)
					$(this).closest('tr').closestDescendant('td,th',true).each( function() {
						if ( $(this).attr('style') && getStyleAttr(this, 'width') != null ) {
							if ( units == null )
								units = getStyleAttr($(this),'width').indexOf('%') != -1 ? "%" : "px";
							cellWidths[cellWidths.length] = $(this).css('width').indexOf(units) != -1 ? $(this).css('width') : null;
						} else {
							cellWidths[cellWidths.length] = null;
						}
					});
					// DEFAULT UNITS
					if ( units == null )
						units = "px";

					// RESOLVE: Cell count and widths available to be divided
					var tCellWidths = (units == "px" ? tW : 100);
					var cellCount = 0;
					for ( var i=0; i < cellWidths.length; i++ ) {
						if ( cellWidths[i] != null )
							tCellWidths -= parseFloat(cellWidths[i].replace("%","").replace("px",""));
						else
							cellCount++;
					};

					// CELL WIDTH: Apply width to cells
					var appliedWidth = tCellWidths / cellCount;
					$(this).closest('tr').closestDescendant('td,th',true).each( function() {
						if ( !$(this).attr('style') || getStyleAttr(this, 'width') == null || getStyleAttr(this, 'width').indexOf(units) == -1 ) {
							ed.dom.setStyles( $(this), {
								'width' 	: appliedWidth + units,
								'max-width' : appliedWidth + units,
								'min-width' : appliedWidth + units
							});
						}
					});
				}
				// MAX-WIDTH (Enforces wrapping)
				if ( getStyleAttr(this, 'max-width') == null )
					ed.dom.setStyle( $(this), 'max-width', getStyleAttr(this, 'width'));

				if ( $(this).attr('style').indexOf('padding') == -1 )
					ed.dom.setStyle( $(this), 'padding', '0px');

			});

			refreshTableWidth(table);

		}
		// CELL BORDER
		function enforceCellBorders(table) {

			$(table).closestDescendant('td,th',true).each( function() {
				if ( !$(this).attr('cell_border') ) {
					var bT = "none", bR = "none", bB = "none", bL = "none";
					if ( getStyleAttr($(this),'border') != null ) {
						bT = bR = bB = bL = getStyleAttr($(this),'border');
					} else if ( getStyleAttr($(this),'border-width') != null || getStyleAttr($(this),'border-style') != null || getStyleAttr($(this),'border-color') != null ) {
						var edge = ["top","right","bottom","left"];
						var edgeStyle = new Array();
						for ( var i=0 ; i < edge.length; i++ ) {
							if ( $(this).css('border-'+edge[i]+'-width') != "0px" && $(this).css('border-'+edge[i]+'-style') != "none" )
								edgeStyle[i] = $(this).css('border-'+edge[i]+'-width')  + " " + $(this).css('border-'+edge[i]+'-style') + " " + $(this).css('border-'+edge[i]+'-color');
							else
								edgeStyle[i] = "none";
						}

						bT = edgeStyle[0];
						bR = edgeStyle[1];
						bB = edgeStyle[2];
						bL = edgeStyle[3];
					} else {

						if ( getStyleAttr($(this),'border-top') != null )
							bT = getStyleAttr($(this),'border-top');
						if ( getStyleAttr($(this),'border-right') != null )
							bR = getStyleAttr($(this),'border-right');
						if ( getStyleAttr($(this),'border-bottom') != null )
							bB = getStyleAttr($(this),'border-bottom');
						if ( getStyleAttr($(this),'border-left') != null )
							bL = getStyleAttr($(this),'border-left');
					}
					ed.dom.setAttrib( $(this), 'cell_border', 	(bT.indexOf('0px') != -1 ? "none" : bT) + ":" +
						(bR.indexOf('0px') != -1 ? "none" : bR) + ":" +
						(bB.indexOf('0px') != -1 ? "none" : bB) + ":" +
						(bL.indexOf('0px') != -1 ? "none" : bL) );
				}
			});

		}

		function enforceCellContentContainers(table) {

			// CONTENT: Containers
			$(table).closestDescendant('td,th',true).each(function(){
				// Each cell must contain a content container
				if ( $(this).children('p,ul,ol,div:not(.mceStaticElement),var,table').length == 0 ) {
					if ( $(this).children('div.mceStaticElement').length != 0 ) {
						$(this).append('<p>&#65279;</p>');
					} else {
						var wrapperTag = $('<p>&#65279;</p>').append( $(this).html() );
						$(this).html(wrapperTag);
					}
				}

				// Wrap content if existing without container
				var unwrappedText =  $(this).clone().children().remove().end().text();
				if ( unwrappedText.length != 0 ) {
					var textNodes = $(this).contents().filter(function(){
						return this.nodeType == 3;
					})
					for ( var i = 0; i < textNodes.length; i++ )
						textNodes[i].nodeValue = "";

					$(this).find('p:first').append(unwrappedText);
				}
			});

		}

		// STYLES: Push styles onto content containers
		function enforceStyleApplication(table) {

			var targetClasses = "";
			var targetEles = new Array();
			if ( $(table).attr('class') != undefined && $(table).attr('class') ) {
				targetClasses = $(table).attr('class');
				targetEles = targetEles.concat( $(table).closestDescendant('td',true).children('p,ul,ol,span,div,var') );
			}
			$(table).closestDescendant('tr',true).each( function() {
				if ( $(this).attr('class') != undefined && $(this).attr('class') != "" ) {
					targetClasses += " " + $(this).attr('class');
					targetEles = targetEles.concat ( $(this).closestDescendant('td',true).children('p,ul,ol,span,div,var') );
				}
			});
			$(table).closestDescendant('td,th',true).each( function() {
				if ( $(this).attr('class') != undefined && $(this).attr('class') != "" ) {
					targetClasses += " " + $(this).attr('class');
					targetEles = targetEles.concat( $(this).children('p,ul,ol,span,div,var') );
				}
			});

			if ( targetClasses.length != 0 ) {
				targetClasses = targetClasses.split(" ");
				var tempClassArray = new Array();
				for ( var i=0; i < targetClasses.length; i++ ) {
					if ( targetClasses[i].length != 0 && targetClasses[i].indexOf('mce') != 0 && $.inArray(targetClasses[i], tempClassArray) == -1 )
						tempClassArray[tempClassArray.length] = targetClasses[i];
				}
				targetClasses = tempClassArray;

				if ( targetClasses.length != 0 ) {
					$(table).removeClass(targetClasses.join(' '));
					$(table).closestDescendant('tr,td,th',true).removeClass(targetClasses.join(' '));

					for ( var i=0; i < targetEles.length; i++ )
						for ( var j=0; j < targetClasses.length; j++ )
							if ( !$(targetEles[i]).hasClass(targetClasses[j]) )
								$(targetEles[i]).addClass(targetClasses[j]);
				}
			}

		}

		function onCellSplit(table) {
			// Action on splits (ignore row insert/delete)
			var splitDetected = false;

			var tableId = $(table).attr('mce_obj_id');
			if ( tableId && ed.settings.table.data[parseInt(tableId)] ) {
				var tData = ed.settings.table.data[parseInt(tableId)];

				if ( tData.cell_count < $(table).closestDescendant('td,th',true).length && tData.row_count == $(table).closestDescendant('tr',true).length ) {
					for ( var i = 0; i < tData.rows.length; i++ ) {

						var splitWidth 		= 0;
						var splitCellCount 	= 0;

						for ( var j = 0; j < tData.rows[i].length; j++ ) {

							// Check for SPLIT
							if ( tData.rows[i][j].col_span > 1 && !$(tData.rows[i][j].cell).attr('colspan') ) {

								splitDetected = true
								splitWidth = parseFloat(tData.rows[i][j].width.replace('px','').replace('%','')) / tData.rows[i][j].col_span;
								splitCellCount = tData.rows[i][j].col_span;

								var targetCell = tData.rows[i][j].cell;

								var units = getStyleAttr(targetCell,'width').indexOf('%') != -1 ? '%' : 'px';
								while (splitCellCount > 0 ) {

									// Border: Prefer collapsed definition for new cells (prevent errant border definitions)
									if ( $(targetCell).is('[cell_border]') && $(targetCell).is('[collapsed_cell_border]') )
										$(targetCell).attr('cell_border', $(targetCell).attr('collapsed_cell_border'));

									// Apply SPLIT width
									ed.dom.setStyles( $(targetCell), {
										'width' 	: splitWidth + units,
										'max-width' : splitWidth + units,
										'min-width' : splitWidth + units
									});
									targetCell = $(targetCell).next('td,th');
									splitCellCount--;
								}

							}

						}

					}
				}

			}

			return splitDetected;
		}

		function onCellAdd(table) {

			// Find spanning cells impact by addition of new cells and adjust width for impacted spans

			var addCellDetected = $(table).closestDescendant("td,th",true).filter("[mce_action='new_cell']").length != 0;
			var new_table_layout = getTableLayout(table);

			function findSpansInColumn(colIndex) {
				var spanCells = new Array();
				for ( var i = 0; i < new_table_layout.length; i++ ) {
					if ( $(new_table_layout[i][colIndex]).attr('colspan') &&
						parseInt($(new_table_layout[i][colIndex]).attr('colspan')) > 1 ) {

						if ( $.inArray($(new_table_layout[i][colIndex]), spanCells) == -1 )
							spanCells[spanCells.length] = $(new_table_layout[i][colIndex]);
					}
				}
				return spanCells;
			}

			var tableId = $(table).attr('mce_obj_id');
			if ( tableId && ed.settings.table.data[parseInt(tableId)] ) {
				var tData = ed.settings.table.data[parseInt(tableId)];
				var colIndex = 0;

				while ( $(table).closestDescendant("td,th",true).filter("[mce_action='new_cell']").length != 0 ) {

					$(table).closestDescendant("td,th",true).filter("[mce_action='new_cell']").first().each( function() {

						// Border: Prefer collapsed definition for new cells (prevent errant border definitions)
						if ( $(this).is('[cell_border]') && $(this).is('[collapsed_cell_border]') )
							$(this).attr('cell_border', $(this).attr('collapsed_cell_border'));

						if ( $(this).closest("tr[mce_action='new_row']").length == 0 ) {

							var newCellWidth = parseFloat( getStyleAttr($(this), "width").replace("px","") );

							for ( var i = 0; i < new_table_layout.length; i++ ) {
								for ( var j = 0; j < new_table_layout[i].length; j++ ) {
									if ( $(this).get(0) == $(new_table_layout[i][j]).get(0) ) {
										colIndex = j;
										var impactedCells = findSpansInColumn(j);
										for ( k = 0; k < impactedCells.length; k++ ) {
											var units = getStyleAttr($(impactedCells[k]),'width').indexOf('%') != -1 ? "%" : "px";

											var spanWidth = parseFloat( getStyleAttr($(impactedCells[k]), "width").replace("px","") );

											ed.dom.setStyles( $(impactedCells[k]), {
												'width' 	: spanWidth + newCellWidth + units,
												'max-width' : spanWidth + newCellWidth + units,
												'min-width' : spanWidth + newCellWidth + units
											});
										}
									}

									// Clear other new cell markers in row
									if ( j == colIndex )
										$(new_table_layout[i][j]).attr('mce_action','new_cell_parsed');
								}
							}

						} else {

							$(this).attr('mce_action','new_cell_parsed');

						}

					});

				}

				if ( addCellDetected )
					updateTableData(table);

			}

			return addCellDetected;

		}

		// CELL MERGE: Calculate and apply merged attributes
		function onCellMerge(table) {

			var tableId = $(table).attr('mce_obj_id');
			if ( tableId && ed.settings.table.data[parseInt(tableId)] ) {
				var tData = ed.settings.table.data[parseInt(tableId)];

				// Action on merges (ignore row insert/delete)
				if ( tData.cell_count != $(table).closestDescendant('td,th',true).length && tData.row_count == $(table).closestDescendant('tr',true).length ) {

					var isRowMerge = ed.settings.table.last_selection_content != null &&
						$(ed.settings.table.last_selection_content).is('table') &&
						$(ed.settings.table.last_selection_content).find('tr:first td').length > 1;

					if ( !isRowMerge )
						return false;

					for ( var i = 0; i < tData.rows.length; i++ ) {
						for ( var j = 0; j < tData.rows[i].length; j++ ) {

							// Check for HORIZONTAL MERGE: A cell without a parent indicates removal from the DOM (find all removed cells)
							var mergedCells = new Array();
							var mergedCellIndexes = new Array();
							if ( $(tData.rows[i][j].cell).parent().length == 0 ) {
								// Iterate columns in row: Find cells in row which are now missing due to merge
								var k = j;
								while ( k < tData.rows[i].length && $(tData.rows[i][k].cell).parent().length == 0 ) {
									if ( mergedCells.indexOf( tData.rows[i][k].cell ) == -1 ) {
										mergedCells[mergedCells.length] = tData.rows[i][k].cell;
										mergedCellIndexes[mergedCellIndexes.length] = k;
									}
									k++;
								}
							}

							if ( mergedCells.length > 0 && j - 1 >= 0) {
								// Merge target: Cell remaining after horizontal merge (left of first detected missing cell)
								var mergeTarget = tData.rows[i][j-1];

								// Width: Calculate merged width
								var width = parseFloat(mergeTarget.width.replace('px','').replace('%',''));
								var units = getStyleAttr($(mergeTarget.cell),'width').indexOf('%') != -1 ? '%' : 'px';
								for ( var k = 0 ; k < mergedCellIndexes.length; k++ )
									width += parseFloat(tData.rows[i][mergedCellIndexes[k]].width.replace('px','').replace('%',''));

								ed.dom.setStyles( $(mergeTarget.cell), {
									'width' 	: width + units,
									'max-width' : width + units,
									'min-width' : width + units
								});

								// Border: Prefer collapsed definition for merged cells (prevent errant border definitions)
								if ( $(mergeTarget).is('[cell_border]') && $(mergeTarget).is('[collapsed_cell_border]') )
									$(mergeTarget).attr('cell_border', $(mergeTarget).attr('collapsed_cell_border'));

								// CLEAN EMPTY CELL PARAGRAPHS: Remove empty paragraph placeholders from merged cells
								var paragraphs = $(mergeTarget.cell).children('p');
								var emptyParagraphs = new Array();
								var firstTextIndex = paragraphs.length, lastTextIndex = -1;
								for ( var l = 0; l < paragraphs.length; l++ ) {
									if ( $.trim( $(paragraphs[l]).text() ).length == 0 )
										emptyParagraphs[emptyParagraphs.length] = $(paragraphs[l]);
									else if ( firstTextIndex == paragraphs.length )
										firstTextIndex = l
									else
										lastTextIndex = l
								}
								for ( var m = 0; m < emptyParagraphs.length && m < paragraphs.length - 1; m++ ) {
									if ( $(paragraphs).index($(emptyParagraphs[m])) < firstTextIndex || $(paragraphs).index($(emptyParagraphs[m])) > lastTextIndex )
										ed.dom.remove( $(emptyParagraphs[m]).get(0) );
								}

								return true;

							}

						}
					}

				}
			}

			return false;
		}

		// COLUMN DELETE: Calculate and apply deleted width to spanning cells
		function onColDelete(table) {

			var colDeleteDetected = false;

			var tableId = $(table).attr('mce_obj_id');
			if ( tableId && ed.settings.table.data[parseInt(tableId)] ) {
				var tData = ed.settings.table.data[parseInt(tableId)];
				var mceNewRows = $(table).find("[mce_action='new_row']").length;

				if ( tData.cell_count > $(table).closestDescendant('td,th',true).length && tData.row_count == ($(table).closestDescendant('tr',true).length - mceNewRows) ) {

					var deletedColIndexes = new Array();

					for ( var i = 0; i < tData.table_layout.length; i++ ) {
						for ( var j = 0; j < tData.table_layout[i].length; j++ ) {

							if ( $(ed.getBody()).find( $(tData.table_layout[i][j]) ).length == 0 ) {
								if ( deletedColIndexes.indexOf(j) == -1 ) {
									colDeleteDetected = true;

									deletedColIndexes[deletedColIndexes.length] = j;

									var removedWidth = parseFloat( getStyleAttr($(tData.table_layout[i][j]), "width").replace("%","").replace("px","") );
									var units = getStyleAttr($(tData.table_layout[i][j]), "width").indexOf('%') != -1 ? '%' : 'px';

									for ( var k = 0; k < tData.table_layout.length; k++ ) {
										if ( tData.rows[k][j].col_span != 1 && $(ed.getBody()).find( $(tData.table_layout[k][j]).get(0) ).length != 0 ) {
											var reducedWidth =  parseFloat( getStyleAttr($(tData.table_layout[k][j]), "width").replace(units,"") ) - removedWidth;
											ed.dom.setStyles( $(tData.table_layout[k][j]), {
												'width' 	: reducedWidth + units,
												'max-width' : reducedWidth + units,
												'min-width' : reducedWidth + units
											});
										}
									}

								}
							}


						}
					}


				}
			}

			return colDeleteDetected;
		}

		// TABLE DRAG RESIZE: Adjust cell widths
		function onDragResize(table) {

			if ( $(table).attr('style') && $(table).attr('style').indexOf('width:') != -1 && $(table).css('width') != "auto") {
				var tWidth = parseFloat( getStyleAttr($(table), "width").replace("px","") );
				var units = null;
				var cellWidths = 0;
				$(table).find('tr:first').closestDescendant('td,th',true).each( function() {
					if ( units == null )
						units = getStyleAttr($(this), "width").indexOf('%') != -1 ? "%" : "px";
					cellWidths += parseFloat(getStyleAttr($(this), "width").replace("%","").replace("px",""));
				});

				if ( units == "px" ) {
					var ratio = tWidth / cellWidths;
					$(table).closestDescendant('td,th',true).each( function() {
						var appliedWidth = (parseFloat(getStyleAttr($(this), "width").replace("px","")) * ratio) + units;
						ed.dom.setStyles( $(this), {
							'width' 	: appliedWidth,
							'max-width' : appliedWidth,
							'min-width' : appliedWidth
						});
					});
				}

				refreshTableWidth(table);
			}

			if ( $(table).attr('style') && $(table).attr('style').indexOf('height:') != -1 && $(table).css('height') != "auto") {
				ed.dom.setStyle( $(table), 'height', 'auto');
			}
			// ROW HEIGHT
			$(table).closestDescendant('tr',true).each( function() {
				if ( $(this).attr('row_height') || getStyleAttr(this,'height') != null ) {
					var height = null;
					$(this).closestDescendant('td',true).each( function() {
						if ( height == null &&
							(!$(this).attr('colspan') || $(this).prevAll('td,th').length + 1 == $(this).parent().closestDescendant('td',true).length) &&
							getStyleAttr($(this), "height") != null )
							height = getStyleAttr($(this), "height").replace('px','');
					});
					if ( height != null && parseFloat($(this).attr('row_height')) != parseFloat(height) ) {
						ed.dom.setAttrib( $(this), 'row_height', height);
					}
				}
			});

		}

		// !IMPORTANT!  This function is called repetitively.  Minimize executed code.
		if ( $(ed.getBody()).is(':visible') ) {
			var madeAdjustment = false;
			$(ed.getBody()).find('table').each(function() {

				if ( $(this).hasClass('mceStaticTable') ) {
					return;
				}

				// SMART CANVAS: Do not process canvas tables
				if (  $(this).closest('.mceStaticContainer,.staticContentItem,.mceStaticTable').length != 0 )
					return;

				setDefaultCellSizing(this);
				newTableInit(this);
				cleanEmptyRowColSpan(this);
				enforceCellBorders(this);
				enforceCellContentContainers(this);
				enforceStyleApplication(this);
				if ( !onCellSplit(this) ) {
					if ( !onCellAdd(this) ) {
						if ( !onCellMerge(this) ) {
							if ( !onColDelete(this) ) {
								// (Must be called after onCellMerge and onCellAdd)
								onDragResize(this);
							} else {
								madeAdjustment = true;
							}
						} else {
							madeAdjustment = true;
						}
					} else {
						madeAdjustment = true;
					}
				} else {
					madeAdjustment = true;
				}

				updateTableData(this);

				$(this).find("[mce_action]").removeAttr("mce_action");

			});

			if ( madeAdjustment ) {
				enforceCursorPosition();
				//ed.settings.mp_fn.reset_last_undo();
				ed.fire("mceRefreshFormElementFitToWidth");
				ed.fire("mceRefreshEmbeddedImages");
			}
		}
	};

	var recordTableDimensions = function() {
		$(ed.getBody()).find('table:visible').each( function() {

			// SMART CANVAS: Do not process canvas tables
			if (  $(this).closest('.mceStaticContainer,.staticContentItem,.mceStaticTable').length != 0 )
				return;

			var tableEle = this;
			var tableId = $(this).attr('mce_obj_id');
			var tData = ed.settings.table.data[parseInt(tableId)];

			// TABLE WIDTH
			var tW = $(tableEle).innerWidth();
			if ( tData.rows[0][0].width.indexOf('px') != -1 ) {
				tW = 0;
				for ( var i = 0; i < tData.rows[0].length; i++ )
					tW += parseFloat(tData.rows[0][i].width.replace('px',''));
			}

			// GENERATE LAYOUT FREE OF COLSPAN
			var colCount = 0;
			$(tableEle).find('tr:first').closestDescendant('td',true).each( function() {
				if ( $(this).attr('colspan') != undefined && $(this).attr('colspan').length != 0 )
					colCount += parseInt( $(this).attr('colspan') );
				else
					colCount++;
			});

			var tempRow = $("<tr class=\"colDimCalc\"></tr>");
			for ( var i=0; i < colCount; i++ )
				$(tempRow).append('<td></td>');

			// CALCULATE COLUMN WIDTHS
			var cDimArray = new Array();
			$(tableEle).append(tempRow);
			$(tableEle).find('.colDimCalc').closestDescendant('td',true).each( function() {
				cDimArray[cDimArray.length] = Math.round( (parseFloat(this.getBoundingClientRect().width) / tW) * 100 * 10000 ) / 10000;
			});

			$(tableEle).attr('c_dim', cDimArray.join(":"));
			$(tableEle).find('.colDimCalc').remove();

			// CALCULATE TABLE WIDTH RELATIVE TO CONTAINER
			var cW = null;
			if ( $(tableEle).closest('td').length != 0 ) {
				var parentCell = $(tableEle).closest('td');
				var parentCellWidth = getStyleAttr($(tableEle).closest('td'), "width");
				cW = parentCellWidth.indexOf('%') != -1 ? $(parentCell).width() : parseFloat( parentCellWidth.replace('px','') );
			} else if ( $(tableEle).closest('.mceEditableContainer').length != 0 ) {
				cW = $(tableEle).closest('.mceEditableContainer').width();
			} else {
				cW = $(ed.getBody()).width();
			}
			$(tableEle).attr('t_dim', Math.round( (tW / cW) * 100 * 10000 ) / 10000);
			$(tableEle).attr('t_width', Math.round(tW * 10000) / 10000 );

		});
	};

	var refreshTableWidth = function(table) {

		// SMART CANVAS: Do not process canvas tables
		if (  $(table).closest('.mceStaticContainer,.mceStaticTable').length != 0 || $(table).find('td').length == 0 )
			return;

		if ( $(table).attr('full_width') && $(table).attr('full_width') == "true") {

			ed.dom.setStyle( $(table), 'width', '100%');

		} else if ( !$(table).attr('style') ||
			($(table).attr('style') && $(table).attr('style').indexOf('width:') != -1 && $(table).css('width') != "auto") ) {

			var tableId = $(table).attr('mce_obj_id');
			var tableLayout;
			if ( tableId && ed.settings.table.data[parseInt(tableId)] )
				tableLayout = ed.settings.table.data[parseInt(tableId)].table_layout;
			else
				tableLayout = getTableLayout(table);

			// Don't set table width for relative
			var firstCellWidth = getStyleAttr($(tableLayout[0][0]), "width");
			if ( firstCellWidth == null || getStyleAttr($(tableLayout[0][0]), "width").indexOf('%') != -1 )
				return;

			var rowCells 	= new Array();
			var tableWidth 	= 0;

			for ( var i = 0; i < tableLayout[0].length; i++ )
				if ( $.inArray($(tableLayout[0][i]).get(0), rowCells) == -1 ) {
					rowCells[rowCells.length] = $(tableLayout[0][i]).get(0);
					var currentCellWidth = getStyleAttr($(tableLayout[0][i]), "width");
					if ( currentCellWidth != null )
						tableWidth += parseFloat( getStyleAttr($(tableLayout[0][i]), "width").replace('px','') );
				}
			ed.dom.setStyle( $(table), 'width', tableWidth + 'px');

		}
	};

	var getTableLayout = function(table) {

		// SMART CANVAS: Do not process canvas tables
		if (  $(table).closest('.mceStaticContainer,.mceStaticTable').length != 0 )
			return;

		var tableCellMatrix = new Array();
		var currentRowIndex = 0;
		$(table).closestDescendant('tr,thead,tfoot',true).each( function() {

			var currentColumnIndex = 0;
			$(this).closestDescendant('td,th',true).each( function() {
				for ( var i = 0; i < ($(this).attr('colspan') ? parseInt($(this).attr('colspan')) : 1); i++ ) {
					for ( var j = 0; j < ($(this).attr('rowspan') ? parseInt($(this).attr('rowspan')) : 1); j++ ) {
						if ( !tableCellMatrix[currentRowIndex + j] )
							tableCellMatrix[currentRowIndex + j] = new Array();

						while ( tableCellMatrix[currentRowIndex + j][currentColumnIndex + i] != undefined ) {
							currentColumnIndex++;
						}

						tableCellMatrix[currentRowIndex + j][currentColumnIndex + i] = $(this);
					}
				}
				currentColumnIndex++
			});
			currentRowIndex++;
		});

		return tableCellMatrix;
	};

	var updateTableData = function(table) {

		// SMART CANVAS: Do not process canvas tables and static tables
		if (  $(table).closest('.mceStaticContainer,.mceStaticTable').length != 0 || $(table).find('td').length == 0 )
			return;

		if ( !$(table).attr('mce_obj_id') )
			$(table).attr('mce_obj_id', ed.settings.table.obj_id_index++);
		var objId = parseInt( $(table).attr('mce_obj_id') );

		var tData = {
			id 		: objId,
			rows 	: []
		};

		var cells = 0;
		var rows = 0;
		$(table).closestDescendant('tr,thead,tfoot',true).each( function() {
			var rData = [];
			rows++;

			$(this).closestDescendant('td,th',true).each( function() {
				cells++;

				var border 	= $(this).attr('cell_border');
				var padding = $(this).attr('cell_padding');
				var cData = {
					border_top		: border ? border.split(':')[0] : null,
					border_right	: border ? border.split(':')[1] : null,
					border_bottom	: border ? border.split(':')[2] : null,
					border_left		: border ? border.split(':')[3] : null,
					padding_top		: padding ? padding.split(':')[0] : null,
					padding_right	: padding ? padding.split(':')[1] : null,
					padding_bottom	: padding ? padding.split(':')[2] : null,
					padding_left	: padding ? padding.split(':')[3] : null,
					width			: getStyleAttr($(this),'width'),
					height			: getStyleAttr($(this),'height'),
					col_span		: $(this).attr('colspan') ? parseInt($(this).attr('colspan')) : 1,
					row_span		: $(this).attr('rowspan') ? parseInt($(this).attr('rowspan')) : 1,
					cell			: $(this)
				}

				rData[rData.length] = cData;
			});

			tData.rows[tData.rows.length] = rData;
		});

		tData.cell_count = cells;
		tData.row_count = rows;
		tData.table_layout = getTableLayout(table);

		ed.settings.table.data[objId] = tData;

	}

	//TODO refactor it as ed.settings.mp_fn.update_table_properties
	ed.addCommand('mceUpdateTableProperties', function(ui, v) {

		function fmtColor(c) {

			if ( c != undefined && c.length > 0) {
				// Trim spaces from RGB color notation
				if ( c.indexOf(', ') != -1 && c.toLowerCase().indexOf('rgb') != -1)
					while ( c.indexOf(', ') != -1 )
						c = c.replace(', ',',');
			}

			return c;
		}

		function fmtDisplayWidth(style) {
			return style.replace('0.25','1').replace('0.5','1');
		}

		if ( v != undefined ) {

			var cellIndex = 0;
			var rowIndex = 0;
			var targetTable = $(ed.settings.table.selected_ele).closest('table');

			$(targetTable).closestDescendant('td',true).each( function() {

				var currentStyle = "";
				// Background
				if ( v.cellBackgroundArray[cellIndex] != undefined && v.cellBackgroundArray[cellIndex] != '' && v.cellBackgroundArray[cellIndex] != 'transparent' )
					currentStyle += "background-color: " + fmtColor(v.cellBackgroundArray[cellIndex]) + "; ";

				// Border
				if ( v.cellBorderArray[cellIndex] != null ) {
					ed.dom.setAttrib( $(this), 'cell_border', fmtColor(v.cellBorderArray[cellIndex].top + ":" + v.cellBorderArray[cellIndex].right + ":" + v.cellBorderArray[cellIndex].bottom + ":" + v.cellBorderArray[cellIndex].left) );
				}
				if ( v.collapsedCellBorderArray[cellIndex] != null ) {
					if ( v.collapsedCellBorderArray[cellIndex].top != "none")
						currentStyle += "border-top: " + fmtDisplayWidth(fmtColor(v.collapsedCellBorderArray[cellIndex].top)) + ";";
					if ( v.collapsedCellBorderArray[cellIndex].right != "none")
						currentStyle += "border-right: " + fmtDisplayWidth(fmtColor(v.collapsedCellBorderArray[cellIndex].right)) + ";";
					if ( v.collapsedCellBorderArray[cellIndex].bottom != "none")
						currentStyle += "border-bottom: " + fmtDisplayWidth(fmtColor(v.collapsedCellBorderArray[cellIndex].bottom)) + ";";
					if ( v.collapsedCellBorderArray[cellIndex].left != "none")
						currentStyle += "border-left: " + fmtDisplayWidth(fmtColor(v.collapsedCellBorderArray[cellIndex].left)) + ";";
					ed.dom.setAttrib( $(this), 'collapsed_cell_border', fmtColor(v.collapsedCellBorderArray[cellIndex].top + ":" + v.collapsedCellBorderArray[cellIndex].right + ":" + v.collapsedCellBorderArray[cellIndex].bottom + ":" + v.collapsedCellBorderArray[cellIndex].left));
				}

				// Width
				if ( v.cellWidthArray[cellIndex] != null ) {
					var appliedWidth = v.cellWidthArray[cellIndex] + (v.cellWidthArray[cellIndex].indexOf('%') == -1 ? 'px' : '');
					currentStyle += "width: " + appliedWidth + "; ";
					currentStyle += "max-width: " + appliedWidth + "; ";
					currentStyle += "min-width: " + appliedWidth + "; ";
				}

				// Padding
				if ( v.cellPaddingArray[cellIndex] != null ) {
					var padding = v.cellPaddingArray[cellIndex];
					var tablePadding = parseFloat(v.cellpadding);
					currentStyle += "padding-top: " + (Math.round(((padding[0] + tablePadding) * ed.settings.table.PT_TO_PX) * 1000) / 1000) + "px; " +
						"padding-right:" + (Math.round(((padding[1] + tablePadding) * ed.settings.table.PT_TO_PX) * 1000) / 1000) + "px; " +
						"padding-bottom:" + (Math.round(((padding[2] + tablePadding) * ed.settings.table.PT_TO_PX) * 1000) / 1000) + "px; " +
						"padding-left: " + (Math.round(((padding[3] + tablePadding) * ed.settings.table.PT_TO_PX) * 1000) / 1000) + "px;";
					ed.dom.setAttrib( $(this), 'cell_padding', padding[0] + ":" + padding[1] + ":" + padding[2] + ":" + padding[3] );
				}

				// V. Align
				if ( v.cellVAlignArray[cellIndex] != null )
					currentStyle += "vertical-align: " + v.cellVAlignArray[cellIndex] + ";";
				else
					currentStyle += "vertical-align: top;";

				// H. Align
				if ( v.cellHAlignArray[cellIndex] != null )
					currentStyle += "text-align: " + v.cellHAlignArray[cellIndex] + ";";

				var rowIndex = $(this).closest('tr').prevAll('tr').length;
				if ( v.rowHeightArray[rowIndex] != null )
					currentStyle += "height: " + v.rowHeightArray[rowIndex] + "px; ";
				else
					ed.dom.setStyle( $(this), 'height', null);

				ed.dom.setAttrib( $(this), 'style', currentStyle);

				cellIndex++;
			});
			// Row height
			rowIndex = 0;
			$(targetTable).closestDescendant('tr',true).each( function() {
				if ( v.rowHeightArray[rowIndex] != null )
					$(this).attr('row_height', v.rowHeightArray[rowIndex]);
				else
					$(this).removeAttr('row_height');
				rowIndex++;
			});
			// Row height type
			rowIndex = 0;
			$(targetTable).closestDescendant('tr',true).each( function() {
				if ( v.rowHeightTypeArray[rowIndex] != null )
					$(this).attr('row_height_type', v.rowHeightTypeArray[rowIndex]);
				else
					$(this).removeAttr('row_height_type');
				rowIndex++;
			});
			// Keep with
			rowIndex = 0;
			$(targetTable).closestDescendant('tr',true).each( function() {
				ed.dom.setAttrib( $(this), 'keep_with_next', null);
				ed.dom.setAttrib( $(this), 'keep_with_prev', null);
				if ( v.rowKeepWithArray[rowIndex] != null && v.rowKeepWithArray[rowIndex] == "next" )
					ed.dom.setAttrib( $(this), 'keep_with_next', true);
				else if ( v.rowKeepWithArray[rowIndex] != null && v.rowKeepWithArray[rowIndex] == "prev" )
					ed.dom.setAttrib( $(this), 'keep_with_prev', true);
				rowIndex++;
			});
			// Keep together
			rowIndex = 0;
			$(targetTable).closestDescendant('tr',true).each( function() {
				ed.dom.setAttrib( $(this), 'keep_together', v.rowKeepTogetherArray[rowIndex] != null && v.rowKeepTogetherArray[rowIndex] == true ? true : null);
				rowIndex++;
			});
			// Row data group
			rowIndex = 0;
			$(targetTable).closestDescendant('tr',true).each( function() {
				// Row previously declared a data group but no longer does: Remove row_type and repeat_cell attributes
				if ( $(this).attr('data_group_id') != undefined && v.rowDataGroupArray[rowIndex] == null ) {
					$(this).removeAttr('row_type');
					$(this).find('td,th').each( function() {
						$(this).removeAttr('repeat_cell');
					});
				}

				ed.dom.setAttrib( $(this), 'data_group_id', v.rowDataGroupArray[rowIndex] != null ? v.rowDataGroupArray[rowIndex] : null);
				if ( v.rowDataGroupArray[rowIndex] != null ) {
					$(this).attr('row_type','repeating_row');
					$(this).find('td,th').each( function() {
						$(this).attr('repeat_cell','true');
					});
				}
				rowIndex++;
			});
			ed.dom.setAttrib( $(targetTable), 'cellpadding', v.cellpadding);
			ed.dom.setAttrib( $(targetTable), 'cellspacing', 0);
			ed.dom.setAttrib( $(targetTable), 't_align', v.tableAlign == "default" ? null : v.tableAlign);

			// Row stripe
			rowIndex = 0;
			$(targetTable).closestDescendant('tr',true).each( function() {
				if ( v.rowStripeArray[rowIndex].odd != null && v.rowStripeArray[rowIndex].odd != 'transparent' )
					ed.dom.setAttrib( $(this), 'alt_color_odd', v.rowStripeArray[rowIndex].odd);
				else
					ed.dom.setAttrib( $(this), 'alt_color_odd', null);

				if ( v.rowStripeArray[rowIndex].even != null && v.rowStripeArray[rowIndex].even != 'transparent' )
					ed.dom.setAttrib( $(this), 'alt_color_even', v.rowStripeArray[rowIndex].even);
				else
					ed.dom.setAttrib( $(this), 'alt_color_even', null);
				rowIndex++;
			});

			ed.dom.setAttrib($(targetTable), 'omit_initial_header', null);
			ed.dom.setAttrib($(targetTable), 'omit_last_footer', null);
			if ( v.flexibleHeaderArray != undefined || v.flexibleFooterArray != undefined ) {
				// APPLY FLEXIBLE HEADER/FOOTER
				if ( v.flexibleHeaderArray != undefined ) {
					rowIndex = 0;
					$(targetTable).closestDescendant('tr',true).each( function() {
						if ( $(this).attr('row_type') == 'header' || $(this).attr('row_type') == 'repeating_header' || $(this).attr('row_type') == 'fixed_header' )
							ed.dom.setAttrib( $(this), 'row_type', null);
						if ( (v.flexibleHeaderArray[rowIndex] == undefined || v.flexibleHeaderArray[rowIndex] == null) && (v.flexibleFooterArray[rowIndex] == undefined || v.flexibleFooterArray[rowIndex] == null) )
							ed.dom.setAttrib( $(this), 'repeat', null);
						if ( v.flexibleHeaderArray[rowIndex] != null ) {
							ed.dom.setAttrib($(this), 'row_type', 'header');
							ed.dom.setAttrib($(this), 'repeat', v.flexibleHeaderArray[rowIndex]);
						}
						rowIndex++;
					});
				}
				if ( v.flexibleFooterArray != undefined ) {
					rowIndex = 0;
					$(targetTable).closestDescendant('tr',true).each( function() {
						if ( $(this).attr('row_type') == 'footer' || $(this).attr('row_type') == 'repeating_footer' || $(this).attr('row_type') == 'fixed_footer' )
							ed.dom.setAttrib( $(this), 'row_type', null);
						if ( (v.flexibleHeaderArray[rowIndex] == undefined || v.flexibleHeaderArray[rowIndex] == null) && (v.flexibleFooterArray[rowIndex] == undefined || v.flexibleFooterArray[rowIndex] == null) )
							ed.dom.setAttrib( $(this), 'repeat', null);
						if ( v.flexibleFooterArray[rowIndex] != null ) {
							ed.dom.setAttrib($(this), 'row_type', 'footer');
							ed.dom.setAttrib($(this), 'repeat', v.flexibleFooterArray[rowIndex]);
						}
						rowIndex++;
					});
				}
			} else {
				// APPLY FIXED HEADER/FOOTER
				// Header
				var resetIndex = 0;
				$(targetTable).closestDescendant('tr', true).filter("tr[row_type='repeating_header'],tr[row_type='fixed_header'],tr[row_type='header']").each(function () {
					if (!(resetIndex < parseInt(v.header.rows)))
						ed.dom.setAttrib($(this), 'row_type', null);
					resetIndex++;
				});

				for (var i = 1; i < parseInt(v.header.rows) + 1; i++)
					if ($(targetTable).closestDescendant('tr:nth-child(' + i + ')', false).attr('row_type') == undefined)
						ed.dom.setAttrib($(targetTable).closestDescendant('tr:nth-child(' + i + ')', false), 'row_type', 'repeating_header');
				if (parseInt(v.header.rows) > 0 && v.header.omit_first)
					ed.dom.setAttrib($(targetTable), 'omit_initial_header', 'true');

				// Footer
				var resetIndex = 0;
				$($(targetTable).closestDescendant('tr', true).filter("tr[row_type='repeating_footer'],tr[row_type='fixed_footer',tr[row_type='footer']").get().reverse()).each(function () {
					if (!(resetIndex < parseInt(v.header.rows)))
						ed.dom.setAttrib($(this), 'row_type', null);
					resetIndex++;
				});

				for (var i = 0; i < parseInt(v.footer.rows); i++) {
					var footerRowIndex = $(targetTable).closestDescendant('tr', true).length - i;
					if ($(targetTable).closestDescendant('tr:nth-child(' + footerRowIndex + ')', false).attr('row_type') == undefined)
						ed.dom.setAttrib($(targetTable).closestDescendant('tr:nth-child(' + footerRowIndex + ')', false), 'row_type', 'repeating_footer');
				}
				if (parseInt(v.footer.rows) > 0 && v.footer.omit_last)
					ed.dom.setAttrib($(targetTable), 'omit_last_footer', 'true');
			}

			// Table margins
			if ( v.tableMarginArray ) {
				ed.dom.setAttrib( $(targetTable), 'table_margins', v.tableMarginArray.join(':'));

				let t_align_attr = $(targetTable).attr('t_align');
				if (t_align_attr === "center") {
					ed.dom.setStyles($(targetTable), {
						'margin-top': v.tableMarginArray[0],
						'margin-right': "auto",
						'margin-bottom': v.tableMarginArray[2],
						'margin-left': "auto",
					});
				} else if (t_align_attr === "right") {
					ed.dom.setStyles($(targetTable), {
						'margin-top': v.tableMarginArray[0],
						'margin-right': v.tableMarginArray[1],
						'margin-bottom': v.tableMarginArray[2],
						'margin-left': "auto",
					});
				} else {
					ed.dom.setStyles($(targetTable), {
						'margin-top': v.tableMarginArray[0],
						'margin-right': v.tableMarginArray[1],
						'margin-bottom': v.tableMarginArray[2],
						'margin-left': v.tableMarginArray[3],
					});
				}
			}

			// Full width table
			if ( v.fullWidthTable ) {
				ed.dom.setAttrib( $(targetTable), 'full_width', 'true');
			} else {
				ed.dom.setAttrib( $(targetTable), 'full_width', null);
			}

			// Read table
			if ( v.readTable == false ) {
				ed.dom.setAttrib( $(targetTable), 'read_table', 'false');
			} else {
				ed.dom.setAttrib( $(targetTable), 'read_table', null);
			}

			// Table keep with
			ed.dom.setAttrib( $(targetTable), 'keep_with_next', v.tableKeepWithNext);
			ed.dom.setAttrib( $(targetTable), 'keep_with_prev', v.tableKeepWithPrev);
			ed.dom.setAttrib( $(targetTable), 'keep_together', v.tableKeepTogether);

			// Repeat on data group
			if ( v.repeatOnDataGroup != null && parseInt(v.repeatOnDataGroup) > 0) {
				ed.dom.setAttrib( $(targetTable), 'data_group_id', v.repeatOnDataGroup);
			} else {
				ed.dom.setAttrib( $(targetTable), 'data_group_id', null);
			}

			// CLEAN UNSUPPORTED TABLE ATTR
			ed.dom.setAttrib( $(targetTable), 'border', null);
			if ( $(targetTable).css('float') == 'left' || $(targetTable).css('float') == 'right' )
				ed.dom.setStyle( $(targetTable), 'float', null);
			$(targetTable).closestDescendant('tr',true).each( function() {
				ed.dom.setAttrib( $(this), 'style', null);
			});

			// Table width
			refreshTableWidth(targetTable);

			ed.fire("mceRefreshFormElementFitToWidth");
			ed.fire("mceRefreshEmbeddedImages");

		}
	});

	ed.on('ObjectResizeStart', function(e) {
		// Prevent resize: When contained within non editable
		if ( e.target.nodeName == 'TABLE' && $(e.target).closest('.staticContentItem,.mceStaticTable').length != 0 ) {
			ed.settings.table.block_current_table_drag = true;
			ed.undoManager.add();
		}
	});


	ed.on('change', function(e) {
		pollTableContent();
		recordTableDimensions();

		if ( !ed.settings.table.selected_ele_lock )
			ed.settings.table.selected_ele = $(ed.selection.getStart()).closest('table').length != 0 ? ed.selection.getStart() : $(ed.getBody()).find('table:first');
	});

	ed.on('mceCanvasSizeChange', function(o) {
		recordTableDimensions();
	});

	ed.on('mouseup', function(e) {
		var p = ed.prinovaPlugins;
		var selectedElementTable = $(ed.settings.table.selected_ele).closest('table');
		var currentSelectionTable = $(ed.selection.getStart()).closest('table');
		var isSwitchingTables = (selectedElementTable.length && currentSelectionTable.length) &&
			(selectedElementTable.attr('mce_obj_id') !== currentSelectionTable.attr('mce_obj_id'));


		if ( ed.settings.table.block_current_table_drag ) {
			ed.undoManager.undo();
			ed.settings.table.block_current_table_drag = false;
		}

		// DRAG RESIZE: Capture height changes
		pollTableContent();

		// DRAG RESIZE: Update fit to width form elements
		if ( $(e.target).is('.ephox-dragster-blocker') ) {
			ed.fire("mceRefreshFormElementFitToWidth");
			ed.fire("mceRefreshEmbeddedImages");
		}

		setTimeout( function(){
			$('.mce-floatpanel:visible:not(.mceInit)').each( function() {
				if ( ed.settings.canvas_freeform ) {
					$(this).addClass('mceInit')
					$(this).css({'top': '+=20px'});
				}
			});
		}, 100);

		setTimeout( function(){
			if ( $(e.target).closest('.staticContentItem').length != 0 ) {
				if ( p != undefined && p.tabletoolbar.getEl != undefined )
					$(p.tabletoolbar.getEl()).css({'opacity': 0, 'height' : '0px', 'overflow' : 'hidden'});
			}
		}, 50);

		// TABLE TOOLBAR: Toggle display
		if ( p != undefined && p.tabletoolbar.getEl != undefined ) {
			if ( !ed.settings.table.toolbar_enabled )
				$(p.tabletoolbar.getEl()).css({'opacity': 0, 'height' : '0px', 'overflow' : 'hidden'});
			else
				$(p.tabletoolbar.getEl()).css({'opacity': 1, 'height' : 'auto', 'overflow' : 'visible'});
		}

		enforceCursorPosition(e);


		if(currentSelectionTable.length) {
			if (tablePanelIsOpen()) {
				if (isSwitchingTables) {
					if(hasPanelChanges()) {
						if (window.confirm(client_messages.text.warning_object_change_previous_table)) {
							setPanelHasNewChange(false);
							updateTableSettingsAndRestoreTagPanel();
							refreshPanel();
						} else {
							ed.selection.moveToBookmark(ed.settings.table.savedBookmark);
							ed.settings.table.savedBookmark = ed.selection.getBookmark();
						}
					} else {
						updateTableSettingsAndRestoreTagPanel();
						ed.settings.table.savedBookmark = ed.selection.getBookmark();
						refreshPanel();
					}
				} else {
					ed.settings.table.savedBookmark = ed.selection.getBookmark();
				}
			} else {
				updateTableSettingsAndRestoreTagPanel();
			}
		}

	});

	ed.on('keydown', function(e) {
		enforceCursorPosition(e);
	});

	ed.on('newrow', function(o) {
		$(o.node).attr('mce_action','new_row');
	});

	ed.on('newcell', function(o) {
		var targetCell = $(o.node);
		$(targetCell).attr('mce_action','new_cell');
		if ( ed.settings.table.ext_trigger_clear_new_cells ) {
			$(targetCell).html('<p>&#65279;</p>');
			clearTimeout(ed.settings.table.ext_trigger_clear_timer);
			ext_trigger_clear_timer = setTimeout( function() {
				let actionName = 'newcell';
				if (ed.settings.connectedutils
					&& ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_connectedtableproperties', actionName)
				) {
					return;
				}
				ed.settings.table.ext_trigger_clear_new_cells = false;
			}, 250);
		} else if ( ed.settings.table.ext_trigger_default_new_cells ) {
			var cellChildren = $(targetCell).contents();
			for (var i=0; i < cellChildren.length; i++) {
				if ( i == 0 && $(cellChildren[0]).nodeType != 3 && $(cellChildren[0]).is('p') ) {
					var paragraphChildren = $(cellChildren[0]).contents()
					if ( $(paragraphChildren[0]).nodeType != 3 && $(paragraphChildren[0]).is('span') )
						$(paragraphChildren[0]).html('&#65279;')
					else
						$(cellChildren[0]).html('&#65279;');
				} else {
					$(cellChildren[i]).remove();
				}
			}
			clearTimeout(ed.settings.table.ext_trigger_clear_timer);
			ext_trigger_clear_timer = setTimeout(function () {
				let actionName = 'newcell';
				if (ed.settings.connectedutils
					&& ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_connectedtableproperties', actionName)
				) {
					return;
				}
				ed.settings.table.ext_trigger_default_new_cells = false;
			}, 250);
		} else {
			// Clean errant content on column insert
			$(targetCell).find('i').each(function () {
				if (!$(this).parent().is('[type]'))
					$(this).remove();
			});
		}
	});

	ed.on('ExecCommand', function(e) {
		if (e.command == 'mceTableDeleteRow' || e.command == 'mceTableInsertRowAfter' || e.command == 'mceTableInsertRowBefore' ||
			e.command == 'mceTableDeleteCol' || e.command == 'mceTableInsertColAfter' || e.command == 'mceTableInsertColBefore') {
			setTimeout(function(){
				var currentSelectionTable = $(ed.selection.getStart()).closest('table');
				if(currentSelectionTable.length && tablePanelIsOpen()) {
					refreshPanel();
				}
			}, 100);
		}
	});

	ed.on('BeforeExecCommand', function(e) {
		if ( e.command == 'mceTableInsertRowAfter' || e.command == 'mceTableInsertRowBefore')
			ed.fire('triggerClearCellsOnInsert');
	});

	ed.on('triggerClearCellsOnInsert', function(ui, v) {
		ed.settings.table.ext_trigger_clear_new_cells = true;
		ext_trigger_clear_timer = setTimeout( function() {
			let actionName = 'triggerClearCellsOnInsert';
			if (ed.settings.connectedutils
				&& ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_connectedtableproperties', actionName)
			) {
				return;
			}
			ed.settings.table.ext_trigger_clear_new_cells = false;
		}, 250);
	});

	ed.on('mouseover', function(e) {
		var p = ed.prinovaPlugins;
		if ( (p.tablegridtoggle && !p.tablegridtoggle.active()) || (!p.tablegridtoggle && !ed.settings.table.default_enable_resize) ) {
			var tableElement = ed.dom.getParent(e.target, 'table');
			if (e.target.nodeName === 'table' || tableElement != null)
				setTimeout( function() { $(ed.getDoc()).find('.mce-resize-bar,.ephox-snooker-resizer-bar').remove() }, 10);
		}
	});

	ed.on('undo', function() {
		ed.fire("mceRefreshFormElementFitToWidth");
		ed.fire("mceRefreshEmbeddedImages");
	});
	ed.on('redo', function() {
		ed.fire("mceRefreshFormElementFitToWidth");
		ed.fire("mceRefreshEmbeddedImages");
	});

	ed.on('init', function() {

		if ( ed.prinovaPlugins == undefined )
			ed.prinovaPlugins = {};
		ed.prinovaPlugins.tabletoolbar = {};

		ed.settings.table.selected_ele = $(ed.getBody()).find('table:first');

		if ( ed.buttons.table ) {

			for ( var i=0; i < ed.buttons.table.menu.length; i++ ) {

				var currentItem = ed.buttons.table.menu[i];

				if ( i == 1 ) {
					// Table properties
					//var tableProperties_onclick = currentItem.onclick;
					currentItem.onclick = function() {
						openTablePropertiesDialogue('table');
						return false;
					};
				} else if ( i == 4 ) {
					// Cell properties
					//var cellProperties_onclick = currentItem.menu[0].onclick;
					currentItem.menu[0].onclick = function() {
						openTablePropertiesDialogue('cell');
						return false;
					};
					// Merge cells
					//var mergeCells_onclick = currentItem.menu[1].onclick;
					currentItem.menu[1].onclick = function() {

						var selectedTRs = [];
						var isSefasConnector = ed.settings.mp_fn.qualify_channel_connectors({'channels': [],'connectors': [18,19] });
						$(ed.getBody()).find('[data-mce-selected]').each( function() {
							if ( !$(this).is('table') && $(this).closest('tr').length != 0 && selectedTRs.indexOf( $(this).closest('tr').get(0) ) == -1 )
								selectedTRs.push($(this).closest('tr').get(0));
						});

						if ( selectedTRs.length > 1 && isSefasConnector ) {
							ed.windowManager.alert(client_messages.content_editor.vertical_cell_merge_support);
							return false;
						}

						ed.execCommand('mceTableMergeCells');
						return false;
					};
				} else if ( i == 5 ) {
					// Row
					//var rowProperties_onclick = currentItem.menu[3].onclick;
					currentItem.menu[3].onclick = function() {
						openTablePropertiesDialogue('row');
						return false;
					};
				}

			}

		}

		ed.on('NodeChange', function(e) {
			ed.settings.table.last_node_change_table = e.element && $(e.element).closest('table').length != 0 ? $(e.element).closest('table') : null;
		});

		if ( ed.menuItems.tableprops ) {

			ed.menuItems.tableprops.onclick = function() {
				openTablePropertiesDialogue('table');
				return false;
			}

		}

		$(getTopFrame().document).on( 'click', '#mce-custompanel-table .mce-close', function() {
			closePanel();
		});

		setTimeout( function() {
			let actionName = 'init';
			if (ed.settings.connectedutils
				&& ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_connectedtableproperties', actionName)
			) {
				return;
			}
			pollTableContent();
			$(ed.getBody()).find('table').each( function() {
				ed.dom.setAttrib( $(this), 'cellspacing', 0);
			});
		}, 1000 );

	});

	ed.settings.table = {
		obj_id_index				: 0,
		data						: [],
		default_enable_resize		: false,
		toolbar_enabled				: localStorage.getItem("msgpt_table_toolbar_enable") == "true",
		block_current_table_drag	: false,
		last_selection_content		: null,
		last_node_change_content	: null,
		selected_ele_lock			: false,
		ext_trigger_clear_new_cells	: false,
		ext_trigger_clear_timer		: null,
		ext_trigger_default_new_cells: false,
		PT_TO_PX					: 100/72
	};

	var toggleTableActions = function() {
		var enabledTableActions = $(ed.selection.getStart()).closest('table').length != 0 && $(ed.selection.getStart()).closest('.staticContentItem').length == 0;
		if ( ed.prinovaPlugins.insertrowbefore )
			ed.prinovaPlugins.insertrowbefore.disabled(!enabledTableActions);
		if ( ed.prinovaPlugins.insertrowafter )
			ed.prinovaPlugins.insertrowafter.disabled(!enabledTableActions);
		if ( ed.prinovaPlugins.deleterow )
			ed.prinovaPlugins.deleterow.disabled(!enabledTableActions);
	}

	ed.on('dragstart', function(e) {
		if ( $(e.target).is('table,tbody,th,tr,td') ) {
			e.preventDefault();
			return false;
		}

	});

	ed.on('NodeChange', function(o) {
		toggleTableActions();
	});



	var row_limited = {
		text: 'Row',
		context: 'mp_table',
		menu: [
			{
				text: 'Insert row before',
				onclick: function (e) {
					let params = {
						event: e,
						actionHandled: false,
						currentTableRowInsideTextEditor: null
					};
					TinyMcePluginsConnected.onInsertTableRowBefore(params);

					if (!params.actionHandled && params.currentTableRowInsideTextEditor && params.currentTableRowInsideTextEditor.length > 0) {
						ed.execCommand('mceTableCopyRow');
						ed.settings.table.ext_trigger_default_new_cells = true;
						ext_trigger_clear_timer = setTimeout( function() {
							let actionName = 'onInsertTableRowBefore';
							if (ed.settings.connectedutils
								&& ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_connectedtableproperties', actionName)
							) {
								return;
							}
							ed.settings.table.ext_trigger_default_new_cells = false;
							}, 250);
						ed.execCommand('mceTablePasteRowBefore');

						let sourceTableRow = params.currentTableRowInsideTextEditor[0];
						let targetTableRow = sourceTableRow.previousElementSibling;
						if (!targetTableRow || targetTableRow.nodeType !== 1 || !targetTableRow.nodeName || targetTableRow.nodeName.toLowerCase() !== 'tr') {
							return;
						}
						TinyMcePluginsConnected.onProcessNewTableRow({
							sourceTableRow: sourceTableRow,
							targetTableRow: targetTableRow
						});
					}
				},
				onPostRender: function (e, o) {
					ed.prinovaPlugins.insertrowbefore = this;
					toggleTableActions();
				}
			},
			{
				text: 'Insert row after',
				onclick: function (e) {
					let params = {
						event: e,
						actionHandled: false,
						currentTableRowInsideTextEditor: null
					};
					TinyMcePluginsConnected.onInsertTableRowAfter(params);

					if (!params.actionHandled && params.currentTableRowInsideTextEditor && params.currentTableRowInsideTextEditor.length > 0) {
						ed.execCommand('mceTableCopyRow');
						ed.settings.table.ext_trigger_default_new_cells = true;
						ext_trigger_clear_timer = setTimeout( function() {
							let actionName = 'onInsertTableRowAfter';
							if (ed.settings.connectedutils
								&& ed.settings.connectedutils.checkDestroyedTinyMceInstance('mp_connectedtableproperties', actionName)
							) {
								return;
							}
							ed.settings.table.ext_trigger_default_new_cells = false;
							}, 250);
						ed.execCommand('mceTablePasteRowAfter');

						let sourceTableRow = params.currentTableRowInsideTextEditor[0];
						let targetTableRow = sourceTableRow.nextElementSibling;
						if (!targetTableRow || targetTableRow.nodeType !== 1 || !targetTableRow.nodeName || targetTableRow.nodeName.toLowerCase() !== 'tr') {
							return;
						}
						TinyMcePluginsConnected.onProcessNewTableRow({
							sourceTableRow: sourceTableRow,
							targetTableRow: targetTableRow
						});
					}
				},
				onPostRender: function (e, o) {
					ed.prinovaPlugins.insertrowafter = this;
					toggleTableActions();
				}
			},
			{
				text: 'Delete row',
				onclick: function (e) {
					var params = {
						event: e,
						actionHandled: false,
						closestTable: null
					};
					TinyMcePluginsConnected.onDeleteTableRow(params);

					if (!params.actionHandled) {
						ed.execCommand('mceTableDeleteRow');
						let closestTable = params.closestTable;
						if (
							closestTable
							&& closestTable.length > 0
							&& closestTable.find('tr').length === 0
						) {
							closestTable.remove();
						}

						toggleTableActions();
					}
				}, onPostRender: function (e, o) {
					ed.prinovaPlugins.deleterow = this;
					toggleTableActions();
				}
			}
		]
	};

	ed.addMenuItem('mp_row', row_limited);

	var menuItems = [];
	$.each("tableprops deletetable | mp_row".split(' '), function(i,name) {
		if (name == '|')
			menuItems.push({text: '-'});
		else
			menuItems.push(ed.menuItems[name]);
	});
	ed.addButton("mp_table", {
		icon: "table",
		type: "menubutton",
		title: "Table",
		menu: menuItems
	});



});