
tinymce.PluginManager.add('mp_textstyles', function(ed, url) {

	// ***********  START: Color Panel Functions  *******************

	function renderColorPicker() {

		var t = ed.settings.textstyle;

		var cols, rows;

		rows = ed.settings.textcolor_rows || 5;
		cols = ed.settings.textcolor_cols || 8;

		var ctrl = this, color, html, last, id = ctrl._id, count = 0;

		function getColorCellHtml(color, title) {
			var isNoColor = color == 'transparent';

			return (
				'<td class="mce-grid-cell' + (isNoColor ? ' mce-colorbtn-trans' : '') + '">' +
				'<div id="' + id + '-' + (count++) + '"' +
				' data-mce-color="' + (color ? color : '') + '"' +
				' role="option"' +
				' tabIndex="-1"' +
				' color-value="' + title + '"' +
				' style="' + (color ? 'background-color: ' + color : '') + '"' +
				' title="' + tinymce.translate( p.replaceAll(title.toUpperCase(),t.indicator.color_joiner,",") ) + '">' +
				(isNoColor ? '&#215;' : '') +
				'</div>' +
				'</td>'
			);
		}

		var colors = new Array();
		var currentStyle = p.textstyle.value();
		var variationData = currentStyle != null ? t.data[currentStyle].variation_data : null;

		for ( var i = 0; i < variationData.colors.length; i++ ) {
			colors.push({
				text: variationData.colors[i],
				color: CMYKtoHEX(variationData.colors[i])
			});
		}
		colors.sort( function(a,b) {
			return a.color > b.color;
		});

		html = '<table class="mce-grid mce-grid-border mce-colorbutton-grid" role="list" cellspacing="0">';
		last = colors.length - 1;

		for ( var y = 0; y < rows; y++ ) {
			if (y * cols > last)
				break;

			html += '<tr>';

			for ( var x = 0; x < cols; x++ ) {
				i = y * cols + x;

				if (i > last) {
					if ( y != 0 )
						html += '<td></td>';
				} else {
					color = colors[i];
					html += getColorCellHtml(color.color, color.text);
				}
			}

			html += '</tr>';
		}

		html += '</table>';

		return html;
	}

	function onColorButtonClick(e) {
		var self = this;
		// TOGGLE PANEL
		$(self.$el).find('.mce-open').click();
	}

	function onColorPanelClick(e) {

		var t = ed.settings.textstyle;

		var buttonCtrl = this.parent(), value;

		function selectColor(value, colorEle) {
			t.selected_color = $(colorEle).attr('color-value');
			buttonCtrl.hidePanel();
			buttonCtrl.color(value);
			ed.settings.utils.focus();
			applyTextStyle(buttonCtrl);
		}

		function resetColor() {
			buttonCtrl.hidePanel();
			buttonCtrl.resetColor();
			ed.settings.utils.focus();
			applyTextStyle(buttonCtrl);
		}

		value = e.target.getAttribute('data-mce-color');
		if (value) {
			if (this.lastId) {
				document.getElementById(this.lastId).setAttribute('aria-selected', false);
			}

			e.target.setAttribute('aria-selected', true);
			this.lastId = e.target.id;

			if (value == 'transparent') {
				resetColor();
			} else {
				selectColor(value, e.target);
			}
		} else if (value !== null) {
			buttonCtrl.hidePanel();
		}
	}

	function CMYKtoHEX(cmykVal) {

		var t = ed.settings.textstyle;

		if ( !cmykVal || cmykVal == null )
			return "#000000";

		function padZero(str) {
			return "000000".substr(str.length) + str;
		}

		var hexVal = cmykVal.replace("cmyk(","").replace(")","").replaceAll("d",".");

		var cmykValues = hexVal.split(t.indicator.color_joiner);
		var C = parseFloat(cmykValues[0])/100;
		var M = parseFloat(cmykValues[1])/100;
		var Y = parseFloat(cmykValues[2])/100;
		var K = parseFloat(cmykValues[3])/100;

		var cyan = (C * 255 * (1-K)) << 16;
		var magenta = (M * 255 * (1-K)) << 8;
		var yellow = (Y * 255 * (1-K)) >> 0;

		var black = 255 * (1-K);
		var white = black | black << 8 | black << 16;

		var color = white - (cyan | magenta | yellow );

		return ("#"+padZero(color.toString(16)));

	}

	// ***********  END: Color Panel Functions  **********************

	// ***********  START: Point Size Menu Functions  ****************

	function togglePointSizeMenuItems() {

		var t = ed.settings.textstyle;

		var defaultItem = [{text: 'DEFAULT', value: 'DEFAULT'}];
		if ( !p || !p.textstyle )
			return defaultItem;

		var currentStyle = p.textstyle.value();

		if ( !t.data[currentStyle] )
			return defaultItem;

		var variationData = currentStyle != null ? t.data[currentStyle].variation_data : null;

		var menuEle = p.pointsize.menu.$el;
		$(menuEle).find('.mce-menu-item .mce-text').each( function() {
			if ( variationData.point_sizes.indexOf( $.trim($(this).text()) ) == -1 )
				$(this).closest('.mce-menu-item').hide();
			else
				$(this).closest('.mce-menu-item').show();
		});

		p.pointsize.value(getAppliedStyleAttr("pointsize") != null ?
			getAppliedStyleAttr("pointsize") :
			variationData.point_sizes[0]);

	}

	function fmtPtSizes(ptArray) {
		if (ptArray.length == 0)
			ptArray;
		for (var i=0; i < ptArray.length; i++) {
			if ( ptArray[i].indexOf('.') == -1 )
				ptArray[i] += '.0';
		}
		return ptArray;
	}

	// ***********  END: Point Size Menu Functions  ******************

	// REGISTER STYLES: Default content
	function refreshAppliedStyleClasses() {

		function getStyleData(styleBase) {

			if ( ed.settings.textstyle.control_classes.indexOf(styleBase) != -1 )
				return;
			if ( $.trim(styleBase).length == 0 )
				return;

			var requestParam = "connector=" + styleBase + "&type=textStyle";
			if ( ed.settings.zone_id != undefined && ed.settings.zone_id != null )
				requestParam += "&zoneId=" + ed.settings.zone_id;

			var stampDate = new Date();
			$.ajax({
				type: "GET",
				url: context + "/getObjectInfo.form?" + requestParam + "&cacheStamp=" + (stampDate.getTime()),
				dataType: "json",
				success: function(data) {
					data = mpParseJSON( data[0] );

					if ( !data )
						return;

					if ( data.has_variations ) {
						if ( data.variation_data.toggle_color )
							data.variation_data.colors = mpParseJSON(data.variation_data.colors);

						if ( data.variation_data.toggle_point_size )
							data.variation_data.point_sizes = fmtPtSizes(mpParseJSON(data.variation_data.point_sizes));
					}

					t.data[styleBase] = data;
					refreshAppliedStyleClasses();
				}
			});
		}


		var t = ed.settings.textstyle;

		$(ed.getBody()).find('span,a').each( function() {

			if ( $(this).attr("class") ) {
				var currentClasses = $(this).attr("class").split(" ");
				for ( var i=0; i < currentClasses.length; i++ ) {
					var styleBase = currentClasses[i].split(t.indicator.primary_joiner)[0];

					if ( !t.data[styleBase] && ed.settings.textstyle.styles_post_loaded.indexOf(styleBase) == -1 ) {
						ed.settings.textstyle.styles_post_loaded[ed.settings.textstyle.styles_post_loaded.length] = styleBase;
						getStyleData(styleBase);
					}

					if ( t.data[styleBase] && !ed.formatter.get(currentClasses[i]) ) {
						registerStyle( currentClasses[i] );
					};
				};
			};
		});
	}

	function registerStyle(style) {

		var cssAttrs  = [];
		var rootStyle = style.split(t.indicator.primary_joiner)[0];

		cssAttrs.push(t.data[rootStyle].css_attributes);

		if ( style.split(t.indicator.primary_joiner).length > 1 ) {

			var styleAttr = style.split(t.indicator.primary_joiner)[1].split(t.indicator.sub_joiner);

			var isBold, isItalic, isUnderline, pointSize = null, color = null;
			for (var j = 0; j < styleAttr.length; j++) {
				if (styleAttr[j].indexOf("B") != -1) {
					isBold = true;
				} else if (styleAttr[j].indexOf("I") != -1) {
					isItalic = true;
				} else if (styleAttr[j].indexOf("U") != -1) {
					isUnderline = true;
				} else if (styleAttr[j].indexOf("S") != -1) {
					pointSize = styleAttr[j];
				} else if (styleAttr[j].indexOf("cmyk") != -1) {
					color = styleAttr[j];
				}
			}

			if (isBold)
				cssAttrs.push("font-weight: bold;");
			else {
				// If not toggle property comes from css_attributes
				if ( t.data[rootStyle].variation_data && t.data[rootStyle].variation_data.toggle_bold )
					cssAttrs.push("font-weight: normal;");
			}

			if (isItalic)
				cssAttrs.push("font-style: italic;");
			else {
				// If not toggle property comes from css_attributes
				if ( t.data[rootStyle].variation_data && t.data[rootStyle].variation_data.toggle_italic )
					cssAttrs.push("font-style: normal;");
			}

			if (isUnderline)
				cssAttrs.push("text-decoration: underline;");
			else {
				// If not toggle property comes from css_attributes
				if ( t.data[rootStyle].variation_data && t.data[rootStyle].variation_data.toggle_underline )
					cssAttrs.push("text-decoration: none;");
			}

			if (t.data[rootStyle].variation_data && t.data[rootStyle].variation_data.applied_font_family) {
				var appliedFontFamily = t.data[rootStyle].variation_data.applied_font_family;
				if (t.data[rootStyle].variation_data.applies_font_bold_italic &&
					t.data[rootStyle].variation_data.toggle_italic && isItalic &&
					t.data[rootStyle].variation_data.toggle_bold && isBold) {
					cssAttrs.push("font-family: '" + appliedFontFamily + "-BoldItalic" + "';");
				} else if (t.data[rootStyle].variation_data.applies_font_bold &&
					t.data[rootStyle].variation_data.toggle_bold && isBold) {
					cssAttrs.push("font-family: '" + appliedFontFamily + "-Bold" + "';");
				} else if (t.data[rootStyle].variation_data.applies_font_italic &&
					t.data[rootStyle].variation_data.toggle_italic && isItalic) {
					cssAttrs.push("font-family: '" + appliedFontFamily + "-Italic" + "';");
				}
			}

			if (pointSize != null) {
				var psVal = pointSize.replace("S", "").replace("_", ".");
				// Pt size is expected to present a decimal (for value matching)
				if (psVal.indexOf('.') == -1)
					psVal += '.0';
				cssAttrs.push("font-size: " + (psVal * t.dspScale) + "pt;");
			}
			if (color != null) {
				if (!p.replaceAll)
					p.replaceAll = initPluginReplaceAll;
				cssAttrs.push("color: " + CMYKtoHEX(p.replaceAll(color.replace('cmyk', 'cmyk('), "_", t.indicator.color_joiner).replaceAll('d', '.') + ")") + ";");
			}

		}

		// REGISTER STYLE: If not already declared
		if ( !ed.formatter.get(style) ) {
			ed.formatter.register(style, {
				inline		: 'span',
				attributes	: {
					class	: style
				}
			});
			ed.dom.addStyle('.' + style + ' {' + cssAttrs.join(' ') + '}');
		}

		return cssAttrs;
	}

	// STYLE CONTENT
	function applyTextStyle(ctrl) {

		function isVariablePreStyled(ele) {
			var isPreStyled = true;
			if ( $(ele).parent().is('span') ) {
				$(ele).parent().contents().each( function() {
					// Already determine to be false;
					if ( !isPreStyled )
						return;
					// Not an element (it's text or comment - parent has other children)
					if ( this.nodeType != 1)
						isPreStyled = false;
					// Not the target ele (parent has other children)
					if ( $(ele).get(0) != $(this).get(0) )
						isPreStyled = false;
				});
			} else {
				isPreStyled = false;
			}
			return isPreStyled;
		}

		// PROTECT CONTENT MENUS: From styling content menu child elements
		function toggleContentMenuProtection(state) {

			const nodesInRange = getNodesInSelection();
			const TYPE = '[type=15]';

			nodesInRange.forEach(node => {
				const $node = $(node);
				if ( $node.is(TYPE) ) {

					const contentMenuId = $node.attr('content_menu_id');
					if (!contentMenuId) {
						console.error('Content menu ID not found for node', node);
						return;
					}

					if ( state === 'on' ) {
						ed.settings.styles.content_menu_placeholders.set(contentMenuId, $node.html());
						$node.html('STYLE_PLACEHOLDER');
					} else if ( state === 'off' ) {
						if ( ed.settings.styles.content_menu_placeholders.has(contentMenuId) )
							$node.html( ed.settings.styles.content_menu_placeholders.get(contentMenuId) );
					}
				}

			});

		}

		function styleVariable(v, style) {

			if ( v == undefined || v == null || !$(v).is('.staticContentItem') )
				return false;

			while ( $(v).parent().closest('.staticContentItem').length != 0 )
				v = $(v).parent();

			var preStyled = isVariablePreStyled(v);
			if ( !preStyled )
				$(v).wrap('<span class="'+style+'"></span>');
			else
				$(v).parent().attr('class',style);
			return true;
		}

		function unstyleVariable(v) {
			if ( v == undefined || v == null || !$(v).is('var'))
				return false;

			while ( $(v).parent().closest('.staticContentItem').length != 0 )
				v = $(v).parent();

			var preStyled = isVariablePreStyled(v);
			if ( preStyled ) {
				$(v).parent().removeClass(stylesArray[i]);
				return true;
			}
			return false;
		}

		function toggleSelectionSubStyles(ctrl, style) {

			$(ed.getBody()).find('[sub_style]').each(function() {
				var subStyleEle = $(this);
				var currentStyle = $(subStyleEle).attr('class');
				let alreadyAppliedStyle = false;
				for (var i = 0; currentStyle && i < currentStyle.split(" ").length; i++) {
					var style = currentStyle.split(" ")[i];
					if (style.indexOf(t.indicator.primary_joiner) !== -1) {
						alreadyAppliedStyle = true;
						break;
					}
				}
				if (alreadyAppliedStyle) {
					$(subStyleEle).removeAttr('sub_style');
					return;
				}

				var subStyle = $(subStyleEle).attr('sub_style');
				for (var i = 0; i < subStyle.split(" ").length; i++) {

					var style = subStyle.split(" ")[i];
					if (style.indexOf(t.indicator.primary_joiner) == -1)
						continue;

					var rootStyle = style.split(t.indicator.primary_joiner)[0];
					var styleAttr = style.split(t.indicator.primary_joiner)[1].split(t.indicator.sub_joiner);
					var toggleAttrs = [];

					var isBold, isItalic, isUnderline, pointSize = null, color = null;
					for (var j = 0; j < styleAttr.length; j++) {
						if (styleAttr[j].indexOf("B") != -1) {
							isBold = true;
						} else if (styleAttr[j].indexOf("I") != -1) {
							isItalic = true;
						} else if (styleAttr[j].indexOf("U") != -1) {
							isUnderline = true;
						} else if (styleAttr[j].indexOf("S") != -1) {
							pointSize = styleAttr[j];
						} else if (styleAttr[j].indexOf("cmyk") != -1) {
							color = styleAttr[j];
						}
					}

					//Effra_VStyle--B-I-U-S11_0-cmyk0_93_93_3
					if (isBold || ctrl.func_id == 'toggle_bold') {
						if (ctrl.func_id == 'toggle_bold') {
							if (!p.bold.disabled() && p.bold.active())
								toggleAttrs.push("B");
						} else {
							toggleAttrs.push("B");
						}
					}
					if (isItalic || ctrl.func_id == 'toggle_italic') {
						if (ctrl.func_id == 'toggle_italic') {
							if (!p.italic.disabled() && p.italic.active())
								toggleAttrs.push("I");
						} else {
							toggleAttrs.push("I");
						}
					}
					if (isUnderline || ctrl.func_id == 'toggle_underline') {
						if (ctrl.func_id == 'toggle_underline') {
							if (!p.underline.disabled() && p.underline.active())
								toggleAttrs.push("U");
						} else {
							toggleAttrs.push("U");
						}
					}
					if (pointSize != null || ctrl.func_id == 'select_point_size') {
						if (ctrl.func_id == 'select_point_size') {
							if (!p.pointsize.disabled())
								toggleAttrs.push("S" + p.pointsize.value().replace(".", "_"));
						} else {
							toggleAttrs.push(pointSize);
						}
					}
					if (color != null || ctrl.func_id == 'select_color') {
						if (ctrl.func_id == 'select_color') {
							if (!p.color.disabled())
								toggleAttrs.push(p.color.colorClassValue());
						} else {
							toggleAttrs.push(color);
						}
					}

					var appliedStyle = rootStyle + t.indicator.primary_joiner + toggleAttrs.join(t.indicator.sub_joiner);
					registerStyle( appliedStyle );

					$(subStyleEle).removeAttr('sub_style').attr('class',appliedStyle);

				}

			});

		}

		var t = ed.settings.textstyle;

		var variantIndicators 	= [];
		var cssAttrs			= [];
		var style 				= null;

		if ( t.data[p.textstyle.value()] != undefined ) {

			if ( !p.bold.disabled() && p.bold.active() )
				variantIndicators.push("B");

			if ( !p.italic.disabled() && p.italic.active() )
				variantIndicators.push("I");

			if ( !p.underline.disabled() && p.underline.active() )
				variantIndicators.push("U");

			if ( !p.pointsize.disabled() ) {
				variantIndicators.push("S"+p.pointsize.value().replace(".","_"));
				// PARAGRAPH MIN HEIGHT
				// cssAttrs.push("min-height: " + (parseFloat(p.pointsize.value()) * t.dspScale) + "pt;");
			}
			if ( !p.color.disabled() )
				variantIndicators.push(p.color.colorClassValue());

			var style = p.textstyle.value() +
				( variantIndicators.length > 0 ?
					(t.indicator.primary_joiner + variantIndicators.join(t.indicator.sub_joiner)) :
					"" );


			registerStyle(style);

			//var selectedNodes = getNodesInSelection();
			// If selection AND selection is uniform, mark nested styles for post processing
			var selectedStyles = getSelectedStyles();
			var toggleSubsPostStyling = false;
			if ( selectedStyles.length > 0 && areSameStyleRoot(selectedStyles) && ctrl.func_id != "select_text_style" ) {
				// Copy style class on styleElements to temp attr 'sub_style' for post processing
				toggleSubsPostStyling = true;
				var styleElements = getStyleElements('omit_parent');
				styleElements.forEach(function(node) {
					$(node).attr('sub_style', $(node).attr('class'));
				});
			}
			//t.applied_style = appliedStyle;

			// STYLE CONTENT
			var targetVar = $(ed.getDoc()).find('.staticContentItem.active').filter( function() { return $(this).closest("[data-mce-bogus]").length == 0 });
			if ( targetVar.length != 0 ) {

				while ( $(targetVar).parent().closest('.staticContentItem').length != 0 )
					targetVar = $(targetVar).parent();

				// APPLY STYLE: VARIABLE
				styleVariable( targetVar, style );

			} else {
				// APPLY STYLE: STANDARD

				// Remove empty spans
				$(ed.getBody()).find('span').each( function() {
					var invisibleCharRegex = /[\u200B-\u200D\uFEFF\u00A0]/g;
                    var text = $(this).text().replace(invisibleCharRegex, '');
					if (text.length == 0 || text === ''){
						$(this).remove();
					}
				});

				toggleContentMenuProtection('on');

				ed.formatter.apply(style);
				// FB23344: First style application may rearrange tagging - second application required to get style applied
				// Expectation is second application is benign if not needed
				ed.formatter.apply(style);

				// Style selection was uniform; toggle appropriate attribute of nested styles
				if ( toggleSubsPostStyling )
					toggleSelectionSubStyles(ctrl, style);

				toggleContentMenuProtection('off');
				ed.settings.styles.content_menu_placeholders = new Map();

				var range = ed.selection.getRng();
				var selectedNodes = getNodesInSelection();
				var resetRng = false;
				for ( var i=0; i < selectedNodes.length; i++ ) {
					// Variable styling
					styleVariable($(selectedNodes[i]), style) ? resetRng = true : null;
				}


				if ( resetRng )
					ed.selection.setRng(range);

				// Getting the location before removing empty span to position caret in the right place
				var location = $(ed.selection.getStart());

				// Remove SPANs without styles
				$(ed.getBody()).find('span:not([class])').each( function() {
					console.log('Removing empty span',this);
					$(this).contents().unwrap();
				});

				if (ed.selection.getContent({format:'text'}).length == 0) {
					ed.selection.setCursorLocation(location.get(0), 0);
				}

			}
			ed.undoManager.add();
		} else {

			// DEFAULT STYLE: Clear applied styles
			var stylesArray = new Array();
			for ( format in ed.formatter.get() )
				if ( t.data[format.split(t.indicator.primary_joiner)[0]] || t.data[format] )
					stylesArray[stylesArray.length] = format;
			stylesArray = ed.formatter.matchAll(stylesArray);

			if ( stylesArray.length > 0 ) {
				var targetVar = $(ed.getDoc()).find('.staticContentItem.active');

				while ( $(targetVar).parent().closest('.staticContentItem').length != 0 )
					targetVar = $(targetVar).parent();

				for ( var i=0; i < stylesArray.length; i++ ) {

					if ( targetVar.length != 0 ) {
						// REMOVE STYLE: VARIABLE
						unstyleVariable( targetVar );
					} else {
						// REMOVE STYLE: STANDARD
						ed.formatter.remove(stylesArray[i]);

						var range = ed.selection.getRng();
						var selectedNodes = getNodesInSelection();
						var resetRng = false;
						for ( var j=0; j < selectedNodes.length; j++ )
							unstyleVariable( $(selectedNodes[j]) ) ? resetRng = true: null;

						if ( resetRng )
						 	ed.selection.setRng(range);

					}

				}
				ed.undoManager.add();
			}
		}


		toggleControlValues();
	}

	function getNodesInSelection() {
		var range = ed.selection.getRng(); // Get the selection range
		var selectedNodes = new Set(); // Use a Set to avoid duplicates

		// Function to recursively add node and its children
		var addNodeAndChildren = function(node) {
			if (!selectedNodes.has(node)) {
				selectedNodes.add(node);
				for (var i = 0; i < node.childNodes.length; i++) {
					addNodeAndChildren(node.childNodes[i]);
				}
			}
		};

		// Special handling if the selection is within a single Text Node
		if (range.startContainer === range.endContainer && range.startContainer.nodeType === Node.TEXT_NODE) {
			selectedNodes.add(range.startContainer);
		} else {
			// Walk through the nodes within the range
			var walker = document.createTreeWalker(
				range.commonAncestorContainer,
				NodeFilter.SHOW_ALL,
				{
					acceptNode: function(node) {
						// Only consider nodes that are within the range
						return range.intersectsNode(node) ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
					}
				}
			);

			var node;
			while ((node = walker.nextNode())) {
				addNodeAndChildren(node);
			}
		}

		return Array.from(selectedNodes);
	}

	// Remove non whitelist tags related to text styles (ex <font>)
	function cleanNonWhitelistTags(setCursorPos) {
		$(ed.getBody()).find('font').each( function() {
			$(this).contents().unwrap();
			console.log("Invalid content tag removed: FONT")
			if ( setCursorPos ) {
				// After removing the font tag, the cursor must be moved after the current keypress character
				var selection = ed.selection;
				if (selection) {
					var range = selection.getRng();
					if (range) {
						// Move the cursor ahead one character
						range.setEnd(range.endContainer, range.endOffset + 1);
						range.setStart(range.endContainer, range.endOffset);
						// Update the selection with the new cursor position
						selection.setRng(range);
					}
				}
			}
		});
	}

	// PARSE STYLE ATTR: From current applied style
	function getAppliedStyleAttr(attr) {
		var t = ed.settings.textstyle;

		if ( t.applied_style != null ) {
			var styleAttr = t.applied_style.split(t.indicator.primary_joiner);
			if ( attr == "style" )
				return styleAttr[0];

			if ( styleAttr.length > 1 ) {
				styleAttr = styleAttr[1].split(t.indicator.sub_joiner);
				if ( attr == "bold" )
					return styleAttr.indexOf("B") != -1;
				if ( attr == "italic" )
					return styleAttr.indexOf("I") != -1;
				if ( attr == "underline" )
					return styleAttr.indexOf("U") != -1;

				for ( var i=0; i < styleAttr.length; i++ ) {
					if ( attr == "pointsize" && styleAttr[i].indexOf('S') != -1 ) {
						var psVal = styleAttr[i].replace("S", "").replace("_", ".");
						// Pt size is expected to present a decimal (for value matching)
						if ( psVal.indexOf('.') == -1 )
							psVal += '.0';
						return psVal;
					}
					if ( attr == "color" && styleAttr[i].indexOf('cmyk') != -1 ) {
						if (!p.replaceAll) {
							p.replaceAll = initPluginReplaceAll;
						}
						return p.replaceAll(styleAttr[i].replace('cmyk', 'cmyk('), "_", t.indicator.color_joiner).replaceAll('d','.') + ")";
					}
				}
			}

		}
		if ( attr == "bold" || attr == "italic" || attr == "underline" )
			return false;
		else
			return null;
	}

	function parentAtCursorPos() {
		if (ed.selection.getContent({format:'text'}).length > 0) { // Case: Text selection
			return $(ed.selection.getNode());
		} else { // Case: No text selection
			return $(ed.selection.getStart());
		}
	}

	// TOGGLE STYLE CONTROLS: Enable/disable controls
	function toggleControlStates(style) {

		var t = ed.settings.textstyle;
		var p = ed.prinovaPlugins;
		var variationData = style != null ? t.data[style].variation_data : null;
		var styleSetDefaults = style != null ? t.data[style].style_set_defaults : null;

		if ( style == null || variationData == null ) {

			p.bold.disabled(true);
			p.italic.disabled(true);
			p.underline.disabled(true);
			p.color.disabled(true);
			p.pointsize.disabled(true);

		} else if ( variationData != null ) {

			if ( variationData.toggle_bold )
				p.bold.disabled(false);
			else
				p.bold.disabled(true);

			if ( variationData.toggle_italic )
				p.italic.disabled(false);
			else
				p.italic.disabled(true);

			if ( variationData.toggle_underline )
				p.underline.disabled(false);
			else
				p.underline.disabled(true);

			if ( variationData.toggle_color ) {
				p.color.disabled(false);
				// DEFAULT STYLE COLOR
				if ( styleSetDefaults !== null && styleSetDefaults.color != null ) {
					p.color.color( CMYKtoHEX( styleSetDefaults.color ) );
					t.selected_color = styleSetDefaults.color;
				} else if ( variationData.colors.indexOf( p.color.color() ) == -1 || !p.color.color() ) {
					p.color.color( CMYKtoHEX( variationData.colors[0] ) );
					t.selected_color = variationData.colors[0];
				}
			} else {
				p.color.disabled(true);
			}
			p.color.refresh();

			if ( variationData.toggle_point_size ) {
				p.pointsize.disabled(false);
				// DEFAULT STYLE SIZE
				if ( styleSetDefaults !== null && styleSetDefaults.pointSize != null ) {
					p.pointsize.value(styleSetDefaults.pointSize);

				} else if ( variationData.point_sizes.indexOf( p.pointsize.value() ) == -1 || !p.pointsize.value() ) {
					p.pointsize.value(variationData.point_sizes[0]);
				}
			} else {
				p.pointsize.disabled(true);
			}
		}
	}

	function getStyleElements(includeParent) {

		var selectedNodes = getNodesInSelection();
		var filteredSpanNodes = [];
		selectedNodes.forEach(function(node) {
			var $node = $(node);
			// Directly adding the node if it's a span with a class
			if ( $node.is('span[class]') && $node.closest('.staticContentItem,.mceNonEditable').length === 0 )
				filteredSpanNodes.push(node);

			// Now correctly starting the search from the parent
			var $parentSpan = $node.parent().closest('span[class]');
			if (includeParent == "incl_parent" && $parentSpan.length > 0 && $.inArray($parentSpan[0], filteredSpanNodes) === -1 && $parentSpan.closest('.staticContentItem,.mceNonEditable').length === 0 )
				filteredSpanNodes.push($parentSpan[0]);
		});

		return filteredSpanNodes;
	}

	function getSelectedStyles() {
		// FIND SELECTED NODES
		var currentSelection = "";
		try {
			currentSelection = $(ed.selection.getContent());
		} catch(err) {
			// Handle invalid selections
		}
		var styleElements = new Array();

		if ( $(ed.getBody()).find('.staticContentItem.active').length != 0 ) {
			styleElements = $(ed.getBody()).find('.staticContentItem.active:first');
		} else if ( currentSelection.length == 0 || $(currentSelection).closest('p,span[class]').length == 0 ) {
			styleElements =  parentAtCursorPos();
			if ($(styleElements).closest('p span[class], span[class]').length !== 0) {
				styleElements = $(styleElements).closest('p span[class], span[class]');
			} else if ($(styleElements).is('br')) {
				styleElements = $(styleElements).closest('p').find('span[class]');
			}
		} else {
			styleElements = getStyleElements('incl_parent');
			if ( styleElements.length === 0 )
				styleElements = $(currentSelection);
		}

		// SELECTED VAR TAGS
		for ( var i = 0; i < styleElements.length; i++ )
			if ( $(styleElements[i]).is('.varTagRenderedInline,.mceNonEditable,.staticContentItem') && $(styleElements[i]).closest('span:not(.varTagRenderedInline,.mceNonEditable,.staticContentItem)').length != 0 )
				styleElements[i] = $(styleElements[i]).closest('span:not(.varTagRenderedInline,.mceNonEditable,.staticContentItem)');

		// MATCH APPLIED STYLES
		var selectedStyles = new Array();
		for ( var i=0; i < styleElements.length; i++ ) {
			if ( $(styleElements[i]).attr("class") != undefined ) {
				var currentClasses = $(styleElements[i]).attr("class").split(" ");
				for ( var j=0; j < currentClasses.length; j++ ) {
					var styleBase = null;
					if ( currentClasses[j].indexOf(t.indicator.primary_joiner) != -1 )
						styleBase = currentClasses[j].split(t.indicator.primary_joiner)[0];
					if ( (t.data[currentClasses[j]] != undefined || t.data[styleBase] != undefined) &&
						selectedStyles.indexOf(currentClasses[j]) == -1 )
						selectedStyles[selectedStyles.length] = currentClasses[j];
				}
			}
		}

		return selectedStyles;
	}

	function areSameStyleRoot(arr) {
		if (arr.length === 0) {
			return true; // Empty array is considered as all values being equal
		}

		const firstValue = arr[0].split(t.indicator.primary_joiner)[0]

		for (let i = 1; i < arr.length; i++) {
			if (arr[i].split(t.indicator.primary_joiner)[0] !== firstValue) {
				return false; // If any value is not equal to the first one, return false
			}
		}

		return true; // All values are equal
	}

	// TOGGLE CONTROL VALUES: Set values based on current style context
	function toggleControlValues() {

		var t = ed.settings.textstyle;
		var p = ed.prinovaPlugins;

		// SET SELECTED STYLE
		var selectedStyles = getSelectedStyles();
		var appliedStyle = selectedStyles.length == 1 || areSameStyleRoot(selectedStyles) ? (selectedStyles[0] || null) : null;

		if (appliedStyle == null ) {
			if (canApplyStarterStyle()) {
				console.log("applying starting style");
				var starterStyle = ed.settings.starter_style;
				if (!starterStyle && starterStyle.indexOf(t.indicator.primary_joiner) !== -1){
					starterStyle = starterStyle.split(t.indicator.primary_joiner)[0];
				}
				appliedStyle = starterStyle;
			}
		}

		t.applied_style = appliedStyle;

		var appliedTextStyle = appliedStyle;
		if ( appliedStyle != null && appliedStyle.indexOf(t.indicator.primary_joiner) != -1 )
			appliedTextStyle = appliedStyle.split(t.indicator.primary_joiner)[0];

		if (typeof p.textstyle != 'undefined' && typeof p.textstyle.value == 'function') {
			p.textstyle.value(appliedTextStyle);
			toggleControlStates(appliedTextStyle);
		}

		// SET BUTTON VALUES
		if ( t.applied_style != null && t.data[appliedTextStyle].variation_data != null ) {
			if ( !p.pointsize.disabled() ) {
				p.pointsize.value(getAppliedStyleAttr("pointsize") != null ?
					getAppliedStyleAttr("pointsize") :
					t.data[appliedTextStyle].variation_data.point_sizes[0]);
			} else {
				p.pointsize.value(false);
			}

			if ( !p.color.disabled() ) {
				var appliedColor = getAppliedStyleAttr("color") != null ?
					getAppliedStyleAttr("color") :
					t.data[appliedTextStyle].variation_data.colors[0];
				p.color.color( CMYKtoHEX( appliedColor ) );
				t.selected_color = appliedColor;
			} else {
				p.color.color( "#BBB" );
			}

			p.bold.active( getAppliedStyleAttr("bold") );
			p.italic.active( getAppliedStyleAttr("italic") );
			p.underline.active( getAppliedStyleAttr("underline") );
		}
	}

	function checkStyleSetDefaults() {
		if (!t) return;
		var $selectedNode = $(ed.selection.getNode());
		var appliedStyle = null;
		if ($selectedNode.find('span[class]').length > 0) {
			appliedStyle = $selectedNode.find('span[class]')[0].getAttribute("class");
		}

		if ( appliedStyle && t.data[appliedStyle] && t.data[appliedStyle].style_set_defaults ) {
			setStyleSetDefaultValues(appliedStyle);
			toggleControlStates(appliedStyle);

			applyTextStyle(this);
		}
	}

	function applyStarterStyle() {
		if (!canApplyStarterStyle()) {
			return;
		}

		var starterStyle = ed.settings.starter_style;

		if (!starterStyle && starterStyle.indexOf(t.indicator.primary_joiner) !== -1){
			starterStyle = starterStyle.split(t.indicator.primary_joiner)[0];
		}

		setStyleSetDefaultValues(starterStyle);
		toggleControlStates(starterStyle);
		p.textstyle.value(starterStyle);
		applyTextStyle(this);
	}

	function canApplyStarterStyle() {
		var t = ed.settings.textstyle;
		var p = ed.prinovaPlugins;
		var starterStyle = ed.settings.starter_style;
		var $selectedNode = $(ed.selection.getNode());

		if (!t || !starterStyle) {
			return false;
		}

		t.applied_style = starterStyle;

		if (!starterStyle && starterStyle.indexOf(t.indicator.primary_joiner) !== -1){
			starterStyle = starterStyle.split(t.indicator.primary_joiner)[0];
		}

		var isTargetNode = $selectedNode.is('p') || $selectedNode.is('li');
		var hasNoSpanChildren = $selectedNode.find('span').length == 0;
		var isEmptyText = $selectedNode.text().trim() === '';

		if (isTargetNode &&
			hasNoSpanChildren &&
			isEmptyText &&
			!$selectedNode.prev().is('p')
		) {
			return true;
		}

		return false;

	}

	function setStyleSetDefaultValues(starterStyle) {
		if ( t.data[starterStyle].style_set_defaults != null ) {
			var styleSetDefaults = t.data[starterStyle].style_set_defaults;
			if ( styleSetDefaults.bold != null )
				p.bold.active(styleSetDefaults.bold);
			if ( styleSetDefaults.italic != null )
				p.italic.active(styleSetDefaults.italic);
			if ( styleSetDefaults.underline != null )
				p.underline.active(styleSetDefaults.underline);
			if ( styleSetDefaults.color != null ){
				p.color.color(CMYKtoHEX(styleSetDefaults.color));
				t.selected_color = styleSetDefaults.color;
			}
			if ( styleSetDefaults.pointSize != null ) {
				var num = styleSetDefaults.pointSize;
				if (typeof num === 'string' && num % 1 === 0) {
					num +=  '.0';
				}
				p.pointsize.value(num);
			}
		}
	}

	function toggleStyleAttr(p) {
		var o = this;
		if (p.$el && p.$el.is('.mce-widget'))
			o = p;
		o.active( !o.active() );
		ed.settings.utils.focus();
		applyTextStyle(o);
	}

	if ( ed.settings.style_formats != undefined && ed.settings.style_formats.length > 0 ) {

		// INIT: [Channel: Web = 3, Email = 4]
		ed.settings.textstyle = {
			applied_style 		: null,
			apply_variations 	: false,
			point_sizes 		: [],
			indicator 			: {
				primary_joiner 	: "--",
				sub_joiner 		: "-",
				color_joiner 	: ";"
			},
			dspScale			: (ed.settings.mp_fn.qualify_channel_connectors({'channels': [3,4],'connectors': [] })) ? 1.000 : 1.042,
			styles_post_loaded		: [],
			control_classes		: ["staticContentItem","innerStaticContentItem","mceNonEditable","active","varTagRenderedBlock","varTagRenderedInline",
				"mceInfoTxt","embedded_content_tag","actionVariable","renderedLabelContainer","renderedContentContainer","fa-mp-container"],
			textstyle_selected  : false
		};
		var t =  ed.settings.textstyle;

		// BUILD STYLE LIST
		var styles = [{
			"text" : client_messages.text.none,
			"value" : -1,
			textStyle : function() {
				return "font-size: 14px; color: #000; font-style: italic;";
			}
		}];
		for ( var i=0; i < ed.settings.style_formats.length; i++ ) {
			if ( ed.settings.style_formats[i].has_variations ) {

				if ( ed.settings.style_formats[i].variation_data.toggle_color )
					ed.settings.style_formats[i].variation_data.colors = mpParseJSON(ed.settings.style_formats[i].variation_data.colors);

				if ( ed.settings.style_formats[i].variation_data.toggle_point_size ) {
					ed.settings.style_formats[i].variation_data.point_sizes = fmtPtSizes(mpParseJSON(ed.settings.style_formats[i].variation_data.point_sizes));
					for ( var j=0; j < ed.settings.style_formats[i].variation_data.point_sizes.length; j++ )
						if ( t.point_sizes.indexOf(ed.settings.style_formats[i].variation_data.point_sizes[j]) == -1 )
							t.point_sizes.push(ed.settings.style_formats[i].variation_data.point_sizes[j]);
				}
				t.point_sizes.sort( function(a,b) {
					return parseFloat(a) > parseFloat(b);
				});

			}

			styles[styles.length] = {
				"text" : ed.settings.style_formats[i].title,
				"value" : ed.settings.style_formats[i].title,
				textStyle : function(o) {
					return ed.settings.textstyle.data[this.settings.value].css_attributes;
				}
			};
		}

		ed.on('nodeChange', function(e) {
			// Buffer to allow time for click events to complete
			setTimeout( function() {
				if (!ed.selection) {
					return;
				}

				if (p.textstyle) {
					checkStyleSetDefaults();
					applyStarterStyle();
					toggleControlValues();
				}
			}, 0);
		});

		// Remove FONT tags from content by unwrapping
		// Can be caused by certain nest SPAN combined with a selection that contains multiple SPANs
		ed.on('keyup', function(e) {
			cleanNonWhitelistTags(true);
		});

		ed.on('init', function(e) {

			var t = ed.settings.textstyle;

			if ( ed.settings.styles == undefined ) {
				ed.settings.styles 	= {
					fn	: {},
					content_menu_placeholders : new Map()
				};
			}

			ed.settings.styles.fn.refresh_applied_text_style_classes = function() { refreshAppliedStyleClasses(); };
			ed.settings.styles.fn.toggle_control_values = function() {
				if ( ed.prinovaPlugins.textstyle )
					toggleControlValues();
			};

			// HIDE FORMATS MENU
			if ( ed.buttons.styleselect ) {
				var formatsMenuLabel = ed.buttons.styleselect.text;
				$(ed.getContainer()).find('.mce-menubtn').each( function() {
					if ( $.trim($(this).text()) == $.trim(formatsMenuLabel) )
						$(this).hide();
				});
			}

			refreshAppliedStyleClasses();
			cleanNonWhitelistTags(false);

			// Override Bold, Italic, Underline keyboard shortcuts
			ed.shortcuts.add('ctrl+b','bold_override',function() {
				var p = ed.prinovaPlugins
				if ( p.bold && !p.bold.disabled() )
					toggleStyleAttr(p.bold);
				return;
			});
			ed.shortcuts.add('meta+b','meta_bold_override',function() {
				var p = ed.prinovaPlugins
				if ( p.bold && !p.bold.disabled() )
					toggleStyleAttr(p.bold);
				return;
			});
			ed.shortcuts.add('ctrl+i','italic_override',function() {
				var p = ed.prinovaPlugins
				if ( p.italic && !p.italic.disabled() )
					toggleStyleAttr(p.italic);
				return;
			});
			ed.shortcuts.add('meta+i','meta_italic_override',function() {
				var p = ed.prinovaPlugins
				if ( p.italic && !p.italic.disabled() )
					toggleStyleAttr(p.italic);
				return;
			});
			ed.shortcuts.add('ctrl+u','underline_override',function() {
				var p = ed.prinovaPlugins
				if ( p.underline && !p.underline.disabled() )
					toggleStyleAttr(p.underline);
				return;
			});
			ed.shortcuts.add('meta+u','meta_underline_override',function() {
				var p = ed.prinovaPlugins
				if ( p.underline && !p.underline.disabled() )
					toggleStyleAttr(p.underline);
				return;
			});

		});

		p = ed.prinovaPlugins;
		if ( !p ) p = {};
		initPluginReplaceAll = function pluginReplaceAll(target, str, sub) {
			while (target.indexOf(str) != -1)
				target = target.replace(str,sub);
			return target;
		};
		p.replaceAll = initPluginReplaceAll;

		// DATA: Init style css data
		var styleClasses = new Object();
		for (var i=0; i < ed.settings.style_formats.length; i++) {
			styleClasses[ed.settings.style_formats[i].title] = {
				css_attributes 	: ed.settings.style_formats[i].css_attributes,
				variation_data	: ed.settings.style_formats[i].has_variations ?
					ed.settings.style_formats[i].variation_data :
					null,
				style_set_defaults : ed.settings.style_formats[i].style_set_defaults || null
			};
		}
		t.data = styleClasses;

		ed.addCommand('mceRefreshAppliedStyleClasses', function() {
			refreshAppliedStyleClasses();
		});

		// MENU: TEXT
		ed.addButton('mp_textstyle', {
			type		: 'listbox',
			text		: client_messages.content_editor.text,
			onselect	: function(e) {
				ed.settings.textstyle.textstyle_selected = true;
				if ( this.value() == -1 ) {
					this.value(null);
				} else {
					// RESET CONTROLS
					p = ed.prinovaPlugins;
					p.bold.active( false );
					p.italic.active( false );
					p.underline.active( false );
					var t =  ed.settings.textstyle;
					if ( t.data[this.value()].variation_data != null ) {
						if ( t.data[this.value()].variation_data.point_sizes )
							p.pointsize.value(t.data[this.value()].variation_data.point_sizes[0]);
						if ( t.data[this.value()].variation_data.colors ) {
							var appliedColor = t.data[this.value()].variation_data.colors[0];
							p.color.color( CMYKtoHEX( appliedColor ) );
							t.selected_color = appliedColor;
						}
					}

					setStyleSetDefaultValues(this.value());
					toggleControlStates(this.value());
				}

				ed.settings.utils.focus();
				ed.focus();
				applyTextStyle(this);
			},
			values		: styles,
			onPostRender: function(e) {
				p = ed.prinovaPlugins;
				if ( !p ) p = {};

				this.func_id = "select_text_style";
				p.textstyle = this;
			},
			onShow: function(o) {
				ed.execCommand("mceEnhanceMenu", false, o);
			}
		});

		// BUTTON: BOLD
		ed.addButton('mp_bold', {
			icon		: 'bold',
			disabled	: true,
			tooltip		: client_messages.content_editor.bold,
			onclick		: toggleStyleAttr,
			onPostRender: function(e) {
				p = ed.prinovaPlugins;
				if ( !p ) p = {};

				this.func_id = "toggle_bold";
				p.bold = this;
			}
		});

		// BUTTON: ITALIC
		ed.addButton('mp_italic', {
			icon		: 'italic',
			disabled	: true,
			tooltip		: client_messages.content_editor.italic,
			onclick		: toggleStyleAttr,
			onPostRender: function(e) {
				p = ed.prinovaPlugins;
				if ( !p ) p = {};

				this.func_id = "toggle_italic";
				p.italic = this;
			}
		});

		// BUTTON: UNDERLINE
		ed.addButton('mp_underline', {
			icon		: 'underline',
			disabled	: true,
			tooltip		: client_messages.content_editor.underline,
			onclick		: toggleStyleAttr,
			onPostRender: function(e) {
				p = ed.prinovaPlugins;
				if ( !p ) p = {};

				this.func_id = "toggle_underline";
				p.underline = this;
			}
		});

		// MENU: POINT SIZE
		var pointSizeItems = new Array();
		for ( var i = 0; i < t.point_sizes.length; i++ ) {
			pointSizeItems.push({
				text	: t.point_sizes[i],
				value	: t.point_sizes[i]
			});
		}
		pointSizeItems.sort( function (a,b) {
			return parseFloat(a.value) - parseFloat(b.value);
		});
		ed.addButton('mp_pointsize', {
			type		: 'listbox',
			text		: client_messages.content_editor.point_size_abv,
			disabled	: true,
			onselect	: function(e) {
				ed.settings.utils.focus();
				applyTextStyle(this);
			},
			values		: pointSizeItems,
			onClick		: function(e) {
				togglePointSizeMenuItems();
			},
			onPostRender: function(e) {
				p = ed.prinovaPlugins;
				if ( !p ) p = {};

				this.func_id = "select_point_size";
				p.pointsize = this;
			}
		});

		// PANEL MENU: COLOR
		ed.addButton('mp_color', {
			type		: 'colorbutton',
			disabled	: true,
			tooltip		: client_messages.content_editor.color,
			panel		: {
				role: 'application',
				ariaRemember: true,
				html: renderColorPicker,
				onclick: onColorPanelClick
			},
			onclick: onColorButtonClick,
			onPostRender: function(e) {
				p = ed.prinovaPlugins;
				if ( !p ) p = {};

				this.func_id = "select_color";
				p.color = this;

				p.color.refresh = function() {
					if ( p.color.panel ) {
						p.color.panel.remove();
						p.color.panel = null;
					}
				};
				p.color.colorClassValue = function() {
					var t = ed.settings.textstyle;

					if ( t.selected_color ) {
						var color = t.selected_color
							.replace("(","")
							.replace(")","")
							.replaceAll(".0","")
							.replaceAll(".","d");
						if (!p.replaceAll) {
							p.replaceAll = initPluginReplaceAll;
						}
						return p.replaceAll(color, t.indicator.color_joiner, "_");
					}
					return;
				};

				// BUTTON STYLE
				$(this.$el).find('.mce-preview').css({'height': '16px','margin-top':'0px','top':'25%'});
				$(this.$el).find('.mce-caret').css({'margin-top':'0px'});
			}
		});

	}

});