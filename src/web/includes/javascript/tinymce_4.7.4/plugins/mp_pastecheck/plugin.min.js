tinymce.PluginManager.add("mp_pastecheck", function (ed, url) {

    function validateContent(content) {

        const validElementsMap = {}
        const validElements = ed.settings.valid_elements.split(',');
        for ( i = 0; i < validElements.length; i++ ) {
            let split = validElements[i].split('[');
            let currentTag = split[0].toLowerCase();
            let validAttrs = split[1] && split[1].length != 0 ? split[1].replace(']','').split('|') : null;
            validElementsMap[currentTag] = validAttrs ;
        }

        const mandatoryAttrMap = {
            'table' :	'[t_width],[c_dim]'
        }

        // Iterate through all elements in content and validate again validElementsMap by looking up tag name
        // and validating attributes
        let validationErrors = [];
        let valid = true;
        $($('<div/>').append(content)).find('*').each( function() {
            const tagName = this.nodeName.toLowerCase();
            // Iterate through all attributes and validate against validElementsMap
            if ( tagName != 'meta' ) {
                if (tagName === 'tbody') {
                    return; // Skip validation for the 'tbody' tag
                }
                if ( validElementsMap[tagName] == null ) {
                    const error = 'Invalid tag - '+tagName;
                    if ( validationErrors.indexOf(error) == -1 )
                        validationErrors.push(error);
                    valid = false;
                } else {
                    $.each(this.attributes, function () {
                        if (this.specified) {
                            if (this.name === 'data-content-piece-id' || this.name === 'data-mce-style') {
                                return; // Skip validation for these attributes
                            }
                            if (validElementsMap[tagName] && validElementsMap[tagName] != null && validElementsMap[tagName].indexOf(this.name) == -1) {
                                const error = 'Invalid attr on tag '+tagName + " - [" + this.name + "]";
                                if ( validationErrors.indexOf(error) == -1 )
                                    validationErrors.push(error);
                                valid = false;
                            }
                        }
                    });
                }
            }
            if ( mandatoryAttrMap[tagName] != null ) {
                const error = 'Mandatory attributes on ' + tagName + ' - ' + mandatoryAttrMap[tagName];
                if ( !$(this).is(mandatoryAttrMap[tagName]) && validationErrors.indexOf(error) == -1 ) {
                    validationErrors.push(error);
                    valid = false;
                }
            }
        });

        // Popup validation errors
        if ( validationErrors.length > 0 ) {
            let errorString = "Paste has been processed as plain text due to the following markup issues: " +
                validationErrors.join('; ');
            ed.windowManager.alert(errorString);
        }

        return valid;
    }

    // Paste intercept: Intercept paste event and validate content; if invalid, block paste
    // and popup error message, and print invalid content to console
    ed.on('PastePreProcess', function(e) {

        const isTextPaste = ed.plugins.paste && ed.plugins.paste.clipboard ?
            ed.plugins.paste.clipboard.pasteFormat == 'text' : true;

        if ( !isTextPaste && !validateContent(e.content) ) {
            // Paste clipboard content as plain text
            console.log("Invalid content pasted: ", e.content);

            var clipboardContent = $(e.content);
            $(clipboardContent).find('p,td,h1,h2,h3,h4,h5,th,div').text(function(index, text) {
                return text + 'BR_TAG';
            });
            
            e.content = $(clipboardContent).text().replaceAll("BR_TAG", "<br/>");
        }
    });

});