!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e[f]=d(a[f]);b.apply(null,e)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("u",Array),h("v",Error),g("6",["u","v"],function(a,b){var c=function(){},d=function(a){return function(){return a()}},e=function(a,b){return function(){return a(b.apply(null,arguments))}},f=function(a){return function(){return a}},g=function(a){return a},h=function(a,b){return a===b},i=function(b){for(var c=new a(arguments.length-1),d=1;d<arguments.length;d++)c[d-1]=arguments[d];return function(){for(var d=new a(arguments.length),e=0;e<d.length;e++)d[e]=arguments[e];var f=c.concat(d);return b.apply(null,f)}},j=function(a){return function(){return!a.apply(null,arguments)}},k=function(a){return function(){throw new b(a)}},l=function(a){return a()},m=function(a){a()},n=f(!1),o=f(!0);return{noop:c,noarg:d,compose:e,constant:f,identity:g,tripleEquals:h,curry:i,not:j,die:k,apply:l,call:m,never:n,always:o}}),h("2n",Object),g("z",["6","2n"],function(a,b){var c=a.never,d=a.always,e=function(){return f},f=function(){var f=function(a){return a.isNone()},g=function(a){return a()},h=function(a){return a},i=function(){},j={fold:function(a,b){return a()},is:c,isSome:c,isNone:d,getOr:h,getOrThunk:g,getOrDie:function(a){throw new Error(a||"error: getOrDie called on none.")},or:h,orThunk:g,map:e,ap:e,each:i,bind:e,flatten:e,exists:c,forall:d,filter:e,equals:f,equals_:f,toArray:function(){return[]},toString:a.constant("none()")};return b.freeze&&b.freeze(j),j}(),g=function(a){var b=function(){return a},h=function(){return k},i=function(b){return g(b(a))},j=function(b){return b(a)},k={fold:function(b,c){return c(a)},is:function(b){return a===b},isSome:d,isNone:c,getOr:b,getOrThunk:b,getOrDie:b,or:h,orThunk:h,map:i,ap:function(b){return b.fold(e,function(b){return g(b(a))})},each:function(b){b(a)},bind:j,flatten:b,exists:j,forall:j,filter:function(b){return b(a)?k:f},equals:function(b){return b.is(a)},equals_:function(b,d){return b.fold(c,function(b){return d(a,b)})},toArray:function(){return[a]},toString:function(){return"some("+a+")"}};return k},h=function(a){return null===a||void 0===a?f:g(a)};return{some:g,none:e,from:h}}),h("2m",String),g("y",["z","u","v","2m"],function(a,b,c,d){var e=function(){var a=b.prototype.indexOf,c=function(b,c){return a.call(b,c)},d=function(a,b){return u(a,b)};return void 0===a?d:c}(),f=function(b,c){var d=e(b,c);return d===-1?a.none():a.some(d)},g=function(a,b){return e(a,b)>-1},h=function(a,b){return t(a,b).isSome()},i=function(a,b){for(var c=[],d=0;d<a;d++)c.push(b(d));return c},j=function(a,b){for(var c=[],d=0;d<a.length;d+=b){var e=a.slice(d,d+b);c.push(e)}return c},k=function(a,c){for(var d=a.length,e=new b(d),f=0;f<d;f++){var g=a[f];e[f]=c(g,f,a)}return e},l=function(a,b){for(var c=0,d=a.length;c<d;c++){var e=a[c];b(e,c,a)}},m=function(a,b){for(var c=a.length-1;c>=0;c--){var d=a[c];b(d,c,a)}},n=function(a,b){for(var c=[],d=[],e=0,f=a.length;e<f;e++){var g=a[e],h=b(g,e,a)?c:d;h.push(g)}return{pass:c,fail:d}},o=function(a,b){for(var c=[],d=0,e=a.length;d<e;d++){var f=a[d];b(f,d,a)&&c.push(f)}return c},p=function(a,b){if(0===a.length)return[];for(var c=b(a[0]),d=[],e=[],f=0,g=a.length;f<g;f++){var h=a[f],i=b(h);i!==c&&(d.push(e),e=[]),c=i,e.push(h)}return 0!==e.length&&d.push(e),d},q=function(a,b,c){return m(a,function(a){c=b(c,a)}),c},r=function(a,b,c){return l(a,function(a){c=b(c,a)}),c},s=function(b,c){for(var d=0,e=b.length;d<e;d++){var f=b[d];if(c(f,d,b))return a.some(f)}return a.none()},t=function(b,c){for(var d=0,e=b.length;d<e;d++){var f=b[d];if(c(f,d,b))return a.some(d)}return a.none()},u=function(a,b){for(var c=0,d=a.length;c<d;++c)if(a[c]===b)return c;return-1},v=b.prototype.push,w=function(a){for(var d=[],e=0,f=a.length;e<f;++e){if(!b.prototype.isPrototypeOf(a[e]))throw new c("Arr.flatten item "+e+" was not an array, input: "+a);v.apply(d,a[e])}return d},x=function(a,b){var c=k(a,b);return w(c)},y=function(a,b){for(var c=0,d=a.length;c<d;++c){var e=a[c];if(b(e,c,a)!==!0)return!1}return!0},z=function(a,b){return a.length===b.length&&y(a,function(a,c){return a===b[c]})},A=b.prototype.slice,B=function(a){var b=A.call(a,0);return b.reverse(),b},C=function(a,b){return o(a,function(a){return!g(b,a)})},D=function(a,b){for(var c={},e=0,f=a.length;e<f;e++){var g=a[e];c[d(g)]=b(g,e)}return c},E=function(a){return[a]},F=function(a,b){var c=A.call(a,0);return c.sort(b),c},G=function(b){return 0===b.length?a.none():a.some(b[0])},H=function(b){return 0===b.length?a.none():a.some(b[b.length-1])};return{map:k,each:l,eachr:m,partition:n,filter:o,groupBy:p,indexOf:f,foldr:q,foldl:r,find:s,findIndex:t,flatten:w,bind:x,forall:y,exists:h,contains:g,equal:z,reverse:B,chunk:j,difference:C,mapToObject:D,pure:E,sort:F,range:i,head:G,last:H}}),g("8q",[],function(){var a="undefined"!=typeof window?window:Function("return this;")();return a}),g("6i",["8q"],function(a){var b=function(b,c){for(var d=void 0!==c?c:a,e=0;e<b.length&&void 0!==d&&null!==d;++e)d=d[b[e]];return d},c=function(a,c){var d=a.split(".");return b(d,c)},d=function(a,b){return void 0!==a[b]&&null!==a[b]||(a[b]={}),a[b]},e=function(b,c){for(var e=void 0!==c?c:a,f=0;f<b.length;++f)e=d(e,b[f]);return e},f=function(a,b){var c=a.split(".");return e(c,b)};return{path:b,resolve:c,forge:e,namespace:f}}),g("4m",["6i"],function(a){var b=function(b,c){return a.resolve(b,c)},c=function(a,c){var d=b(a,c);if(void 0===d)throw a+" not available on this browser";return d};return{getOrDie:c}}),g("2q",["4m"],function(a){var b=function(){var b=a.getOrDie("Node");return b},c=function(a,b,c){return 0!==(a.compareDocumentPosition(b)&c)},d=function(a,d){return c(a,d,b().DOCUMENT_POSITION_PRECEDING)},e=function(a,d){return c(a,d,b().DOCUMENT_POSITION_CONTAINED_BY)};return{documentPositionPreceding:d,documentPositionContainedBy:e}}),g("17",[],function(){var a=function(a){var b,c=!1;return function(){return c||(c=!0,b=a.apply(null,arguments)),b}};return{cached:a}}),h("6j",Number),g("44",["y","6j","2m"],function(a,b,c){var d=function(a,b){for(var c=0;c<a.length;c++){var d=a[c];if(d.test(b))return d}},e=function(a,c){var e=d(a,c);if(!e)return{major:0,minor:0};var f=function(a){return b(c.replace(e,"$"+a))};return h(f(1),f(2))},f=function(a,b){var d=c(b).toLowerCase();return 0===a.length?g():e(a,d)},g=function(){return h(0,0)},h=function(a,b){return{major:a,minor:b}};return{nu:h,detect:f,unknown:g}}),g("2h",["6","44"],function(a,b){var c="Edge",d="Chrome",e="IE",f="Opera",g="Firefox",h="Safari",i=function(a,b){return function(){return b===a}},j=function(){return k({current:void 0,version:b.unknown()})},k=function(a){var b=a.current,j=a.version;return{current:b,version:j,isEdge:i(c,b),isChrome:i(d,b),isIE:i(e,b),isOpera:i(f,b),isFirefox:i(g,b),isSafari:i(h,b)}};return{unknown:j,nu:k,edge:a.constant(c),chrome:a.constant(d),ie:a.constant(e),opera:a.constant(f),firefox:a.constant(g),safari:a.constant(h)}}),g("2i",["6","44"],function(a,b){var c="Windows",d="iOS",e="Android",f="Linux",g="OSX",h="Solaris",i="FreeBSD",j=function(a,b){return function(){return b===a}},k=function(){return l({current:void 0,version:b.unknown()})},l=function(a){var b=a.current,k=a.version;return{current:b,version:k,isWindows:j(c,b),isiOS:j(d,b),isAndroid:j(e,b),isOSX:j(g,b),isLinux:j(f,b),isSolaris:j(h,b),isFreeBSD:j(i,b)}};return{unknown:k,nu:l,windows:a.constant(c),ios:a.constant(d),android:a.constant(e),linux:a.constant(f),osx:a.constant(g),solaris:a.constant(h),freebsd:a.constant(i)}}),g("2j",["6"],function(a){return function(b,c,d){var e=b.isiOS()&&/ipad/i.test(d)===!0,f=b.isiOS()&&!e,g=b.isAndroid()&&3===b.version.major,h=b.isAndroid()&&4===b.version.major,i=e||g||h&&/mobile/i.test(d)===!0,j=b.isiOS()||b.isAndroid(),k=j&&!i,l=c.isSafari()&&b.isiOS()&&/safari/i.test(d)===!1;return{isiPad:a.constant(e),isiPhone:a.constant(f),isTablet:a.constant(i),isPhone:a.constant(k),isTouch:a.constant(j),isAndroid:b.isAndroid,isiOS:b.isiOS,isWebView:a.constant(l)}}}),g("2k",["y","44","2m"],function(a,b,c){var d=function(b,d){var e=c(d).toLowerCase();return a.find(b,function(a){return a.search(e)})},e=function(a,c){return d(a,c).map(function(a){var d=b.detect(a.versionRegexes,c);return{current:a.name,version:d}})},f=function(a,c){return d(a,c).map(function(a){var d=b.detect(a.versionRegexes,c);return{current:a.name,version:d}})};return{detectBrowser:e,detectOs:f}}),g("45",[],function(){var a=function(a,b){return b+a},b=function(a,b){return a+b},c=function(a,b){return a.substring(b)},d=function(a,b){return a.substring(0,a.length-b)};return{addToStart:a,addToEnd:b,removeFromStart:c,removeFromEnd:d}}),g("46",["z","v"],function(a,b){var c=function(a,b){return a.substr(0,b)},d=function(a,b){return a.substr(a.length-b,a.length)},e=function(b){return""===b?a.none():a.some(b.substr(0,1))},f=function(b){return""===b?a.none():a.some(b.substring(1))};return{first:c,last:d,head:e,tail:f}}),g("37",["45","46","v"],function(a,b,c){var d=function(a,b,c){if(""===b)return!0;if(a.length<b.length)return!1;var d=a.substr(c,c+b.length);return d===b},e=function(a,b){var c=function(a){var b=typeof a;return"string"===b||"number"===b};return a.replace(/\${([^{}]*)}/g,function(a,d){var e=b[d];return c(e)?e:a})},f=function(b,c){return l(b,c)?a.removeFromStart(b,c.length):b},g=function(b,c){return m(b,c)?a.removeFromEnd(b,c.length):b},h=function(b,c){return l(b,c)?b:a.addToStart(b,c)},i=function(b,c){return m(b,c)?b:a.addToEnd(b,c)},j=function(a,b){return a.indexOf(b)!==-1},k=function(a){return b.head(a).bind(function(c){return b.tail(a).map(function(a){return c.toUpperCase()+a})}).getOr(a)},l=function(a,b){return d(a,b,0)},m=function(a,b){return d(a,b,a.length-b.length)},n=function(a){return a.replace(/^\s+|\s+$/g,"")},o=function(a){return a.replace(/^\s+/g,"")},p=function(a){return a.replace(/\s+$/g,"")};return{supplant:e,startsWith:l,removeLeading:f,removeTrailing:g,ensureLeading:h,ensureTrailing:i,endsWith:m,contains:j,trim:n,lTrim:o,rTrim:p,capitalize:k}}),g("2l",["6","37"],function(a,b){var c=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,d=function(a){return function(c){return b.contains(c,a)}},e=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(a){var c=b.contains(a,"edge/")&&b.contains(a,"chrome")&&b.contains(a,"safari")&&b.contains(a,"applewebkit");return c}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,c],search:function(a){return b.contains(a,"chrome")&&!b.contains(a,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(a){return b.contains(a,"msie")||b.contains(a,"trident")}},{name:"Opera",versionRegexes:[c,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:d("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:d("firefox")},{name:"Safari",versionRegexes:[c,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(a){return(b.contains(a,"safari")||b.contains(a,"mobile/"))&&b.contains(a,"applewebkit")}}],f=[{name:"Windows",search:d("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(a){return b.contains(a,"iphone")||b.contains(a,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:d("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:d("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:d("linux"),versionRegexes:[]},{name:"Solaris",search:d("sunos"),versionRegexes:[]},{name:"FreeBSD",search:d("freebsd"),versionRegexes:[]}];return{browsers:a.constant(e),oses:a.constant(f)}}),g("18",["2h","2i","2j","2k","2l"],function(a,b,c,d,e){var f=function(f){var g=e.browsers(),h=e.oses(),i=d.detectBrowser(g,f).fold(a.unknown,a.nu),j=d.detectOs(h,f).fold(b.unknown,b.nu),k=c(j,i,f);return{browser:i,os:j,deviceType:k}};return{detect:f}}),h("19",navigator),g("7",["17","18","19"],function(a,b,c){var d=a.cached(function(){var a=c.userAgent;return b.detect(a)});return{detect:d}}),g("16",[],function(){return"undefined"==typeof console&&(console={log:function(){}}),console}),h("1b",document),g("a",["6","z","v","16","1b"],function(a,b,c,d,e){var f=function(a,b){var c=b||e,f=c.createElement("div");if(f.innerHTML=a,!f.hasChildNodes()||f.childNodes.length>1)throw d.error("HTML does not have a single root node",a),"HTML must have a single root node";return i(f.childNodes[0])},g=function(a,b){var c=b||e,d=c.createElement(a);return i(d)},h=function(a,b){var c=b||e,d=c.createTextNode(a);return i(d)},i=function(b){if(null===b||void 0===b)throw new c("Node cannot be null or undefined");return{dom:a.constant(b)}},j=function(a,c,d){return b.from(a.dom().elementFromPoint(c,d)).map(i)};return{fromHtml:f,fromTag:g,fromText:h,fromDom:i,fromPoint:j}}),g("1d",[],function(){return{ATTRIBUTE:2,CDATA_SECTION:4,COMMENT:8,DOCUMENT:9,DOCUMENT_TYPE:10,DOCUMENT_FRAGMENT:11,ELEMENT:1,TEXT:3,PROCESSING_INSTRUCTION:7,ENTITY_REFERENCE:5,ENTITY:6,NOTATION:12}}),g("2r",["y","z","a","1d","v","1b"],function(a,b,c,d,e,f){var g=d.ELEMENT,h=d.DOCUMENT,i=function(a,b){var c=a.dom();if(c.nodeType!==g)return!1;if(void 0!==c.matches)return c.matches(b);if(void 0!==c.msMatchesSelector)return c.msMatchesSelector(b);if(void 0!==c.webkitMatchesSelector)return c.webkitMatchesSelector(b);if(void 0!==c.mozMatchesSelector)return c.mozMatchesSelector(b);throw new e("Browser lacks native selectors")},j=function(a){return a.nodeType!==g&&a.nodeType!==h||0===a.childElementCount},k=function(b,d){var e=void 0===d?f:d.dom();return j(e)?[]:a.map(e.querySelectorAll(b),c.fromDom)},l=function(a,d){var e=void 0===d?f:d.dom();return j(e)?b.none():b.from(e.querySelector(a)).map(c.fromDom)};return{all:k,is:i,one:l}}),g("1a",["y","6","2q","7","2r"],function(a,b,c,d,e){var f=function(a,b){return a.dom()===b.dom()},g=function(a,b){return a.dom().isEqualNode(b.dom())},h=function(c,d){return a.exists(d,b.curry(f,c))},i=function(a,b){var c=a.dom(),d=b.dom();return c!==d&&c.contains(d)},j=function(a,b){return c.documentPositionContainedBy(a.dom(),b.dom())},k=d.detect().browser,l=k.isIE()?j:i;return{eq:f,isEqualNode:g,member:h,contains:l,is:e.is}}),g("43",["1a"],function(a){var b=function(b,c){return a.eq(b.element(),c.event().target())};return{isSource:b}}),g("2g",["6"],function(a){return{contextmenu:a.constant("contextmenu"),touchstart:a.constant("touchstart"),touchmove:a.constant("touchmove"),touchend:a.constant("touchend"),gesturestart:a.constant("gesturestart"),mousedown:a.constant("mousedown"),mousemove:a.constant("mousemove"),mouseout:a.constant("mouseout"),mouseup:a.constant("mouseup"),mouseover:a.constant("mouseover"),focusin:a.constant("focusin"),keydown:a.constant("keydown"),input:a.constant("input"),change:a.constant("change"),focus:a.constant("focus"),click:a.constant("click"),transitionend:a.constant("transitionend"),selectstart:a.constant("selectstart")}}),g("t",["2g","6","7"],function(a,b,c){var d={tap:b.constant("alloy.tap")};return{focus:b.constant("alloy.focus"),postBlur:b.constant("alloy.blur.post"),receive:b.constant("alloy.receive"),execute:b.constant("alloy.execute"),focusItem:b.constant("alloy.focus.item"),tap:d.tap,tapOrClick:c.detect().deviceType.isTouch()?d.tap:a.click,longpress:b.constant("alloy.longpress"),sandboxClose:b.constant("alloy.sandbox.close"),systemInit:b.constant("alloy.system.init"),windowScroll:b.constant("alloy.system.scroll"),attachedToDom:b.constant("alloy.system.attached"),detachedFromDom:b.constant("alloy.system.detached"),changeTab:b.constant("alloy.change.tab"),dismissTab:b.constant("alloy.dismiss.tab")}}),g("1i",["u","2m"],function(a,b){var c=function(c){if(null===c)return"null";var d=typeof c;return"object"===d&&a.prototype.isPrototypeOf(c)?"array":"object"===d&&b.prototype.isPrototypeOf(c)?"string":d},d=function(a){return function(b){return c(b)===a}};return{isString:d("string"),isObject:d("object"),isArray:d("array"),isNull:d("null"),isBoolean:d("boolean"),isUndefined:d("undefined"),isFunction:d("function"),isNumber:d("number")}}),g("w",["1i","u","v"],function(a,b,c){var d=function(a,b){return b},e=function(b,c){var d=a.isObject(b)&&a.isObject(c);return d?g(b,c):c},f=function(a){return function(){for(var d=new b(arguments.length),e=0;e<d.length;e++)d[e]=arguments[e];if(0===d.length)throw new c("Can't merge zero objects");for(var f={},g=0;g<d.length;g++){var h=d[g];for(var i in h)h.hasOwnProperty(i)&&(f[i]=a(f[i],h[i]))}return f}},g=f(e),h=f(d);return{deepMerge:g,merge:h}}),g("x",["z","2n"],function(a,b){var c=function(){var a=b.keys,c=function(a){var b=[];for(var c in a)a.hasOwnProperty(c)&&b.push(c);return b};return void 0===a?c:a}(),d=function(a,b){for(var d=c(a),e=0,f=d.length;e<f;e++){var g=d[e],h=a[g];b(h,g,a)}},e=function(a,b){return f(a,function(a,c,d){return{k:c,v:b(a,c,d)}})},f=function(a,b){var c={};return d(a,function(d,e){var f=b(d,e,a);c[f.k]=f.v}),c},g=function(a,b){var c={},e={};return d(a,function(a,d){var f=b(a,d)?c:e;f[d]=a}),{t:c,f:e}},h=function(a,b){var c=[];return d(a,function(a,d){c.push(b(a,d))}),c},i=function(b,d){for(var e=c(b),f=0,g=e.length;f<g;f++){var h=e[f],i=b[h];if(d(i,h,b))return a.some(i)}return a.none()},j=function(a){return h(a,function(a){return a})},k=function(a){return j(a).length};return{bifilter:g,each:d,map:e,mapToArray:h,tupleMap:f,find:i,keys:c,values:j,size:k}}),g("2",["t","6","w","x"],function(a,b,c,d){var e=function(a,b){i(a,a.element(),b,{})},f=function(a,b,c){i(a,a.element(),b,c)},g=function(b){e(b,a.execute())},h=function(a,b,c){i(a,b,c,{})},i=function(a,e,f,g){var h=c.deepMerge({target:e},g);a.getSystem().triggerEvent(f,e,d.map(h,b.constant))},j=function(a,b,c,d){a.getSystem().triggerEvent(c,b,d.event())},k=function(a,b){a.getSystem().triggerFocus(b,a.element())};return{emit:e,emitWith:f,emitExecute:g,dispatch:h,dispatchWith:i,dispatchEvent:j,dispatchFocus:k}}),g("6k",["y","x","1i","u","v","16"],function(a,b,c,d,e,f){var g=function(g){if(!c.isArray(g))throw new e("cases must be an array");if(0===g.length)throw new e("there must be at least one case");var h=[],i={};return a.each(g,function(j,k){var l=b.keys(j);if(1!==l.length)throw new e("one and only one name per case");var m=l[0],n=j[m];if(void 0!==i[m])throw new e("duplicate key detected:"+m);if("cata"===m)throw new e("cannot have a case named cata (sorry)");if(!c.isArray(n))throw new e("case arguments must be an array");h.push(m),i[m]=function(){var c=arguments.length;if(c!==n.length)throw new e("Wrong number of arguments to case "+m+". Expected "+n.length+" ("+n+"), got "+c);for(var i=new d(c),j=0;j<i.length;j++)i[j]=arguments[j];var l=function(c){var d=b.keys(c);if(h.length!==d.length)throw new e("Wrong number of arguments to match. Expected: "+h.join(",")+"\nActual: "+d.join(","));var f=a.forall(h,function(b){return a.contains(d,b)});if(!f)throw new e("Not all branches were specified when using match. Specified: "+d.join(", ")+"\nRequired: "+h.join(", "));return c[m].apply(null,i)};return{fold:function(){if(arguments.length!==g.length)throw new e("Wrong number of arguments to fold. Expected "+g.length+", got "+arguments.length);var a=arguments[k];return a.apply(null,i)},match:l,log:function(a){f.log(a,{constructors:h,constructor:m,params:i})}}}}),i};return{generate:g}}),g("4c",["6k","6"],function(a,b){var c=a.generate([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),d=function(a){return c.defaultedThunk(b.constant(a))},e=function(a){return c.asDefaultedOptionThunk(b.constant(a))},f=function(a){return c.mergeWithThunk(b.constant(a))};return{strict:c.strict,asOption:c.asOption,defaulted:d,defaultedThunk:c.defaultedThunk,asDefaultedOption:e,asDefaultedOptionThunk:c.asDefaultedOptionThunk,mergeWith:f,mergeWithThunk:c.mergeWithThunk}}),g("48",["6","z"],function(a,b){var c=function(d){var e=function(a){return d===a},f=function(a){return c(d)},g=function(a){return c(d)},h=function(a){return c(a(d))},i=function(a){a(d)},j=function(a){return a(d)},k=function(a,b){return b(d)},l=function(a){return a(d)},m=function(a){return a(d)},n=function(){return b.some(d)};return{is:e,isValue:a.constant(!0),isError:a.constant(!1),getOr:a.constant(d),getOrThunk:a.constant(d),getOrDie:a.constant(d),or:f,orThunk:g,fold:k,map:h,each:i,bind:j,exists:l,forall:m,toOption:n}},d=function(c){var e=function(a){return a()},f=function(){return a.die(c)()},g=function(a){return a},h=function(a){return a()},i=function(a){return d(c)},j=function(a){return d(c)},k=function(a,b){return a(c)};return{is:a.constant(!1),isValue:a.constant(!1),isError:a.constant(!0),getOr:a.identity,getOrThunk:e,getOrDie:f,or:g,orThunk:h,fold:k,map:i,each:a.noop,bind:j,exists:a.constant(!1),forall:a.constant(!0),toOption:b.none}};return{value:c,error:d}}),g("49",["6k","y"],function(a,b){var c=a.generate([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),d=function(a){var c=[],d=[];return b.each(a,function(a){a.fold(function(a){c.push(a)},function(a){d.push(a)})}),{errors:c,values:d}},e=function(a,b){return a.fold(function(a){return b.fold(function(b){return c.bothErrors(a,b)},function(b){return c.firstError(a,b)})},function(a){return b.fold(function(b){return c.secondError(a,b)},function(b){return c.bothValues(a,b)})})};return{partition:d,compare:e}}),g("2a",["y","6","w","48","49"],function(a,b,c,d,e){var f=function(a,b){return d.value(c.deepMerge.apply(void 0,[b].concat(a)))},g=function(c){return b.compose(d.error,a.flatten)(c)},h=function(a,b){var c=e.partition(a);return c.errors.length>0?g(c.errors):f(c.values,b)},i=function(a){var b=e.partition(a);return b.errors.length>0?g(b.errors):d.value(b.values)};return{consolidateObj:h,consolidateArr:i}}),g("2b",["y","x"],function(a,b){var c=function(b,c){var d={};return a.each(c,function(a){void 0!==b[a]&&b.hasOwnProperty(a)&&(d[a]=b[a])}),d},d=function(b,c){var d={};return a.each(b,function(a){var b=a[c];d[b]=a}),d},e=function(c,d){var e={};return b.each(c,function(b,c){a.contains(d,c)||(e[c]=b)}),e};return{narrow:c,exclude:e,indexOnKey:d}}),g("2c",["z"],function(a){var b=function(b){return function(c){return c.hasOwnProperty(b)?a.from(c[b]):a.none()}},c=function(a,c){return function(d){return b(a)(d).getOr(c)}},d=function(a,c){return b(c)(a)},e=function(a,b){return a.hasOwnProperty(b)&&void 0!==a[b]&&null!==a[b]};return{readOpt:b,readOr:c,readOptFrom:d,hasKey:e}}),g("2d",["y"],function(a){var b=function(a,b){var c={};return c[a]=b,c},c=function(b){var c={};return a.each(b,function(a){c[a.key]=a.value}),c};return{wrap:b,wrapAll:c}}),g("14",["2a","2b","2c","2d"],function(a,b,c,d){var e=function(a,c){return b.narrow(a,c)},f=function(a,c){return b.exclude(a,c)},g=function(a){return c.readOpt(a)},h=function(a,b){return c.readOr(a,b)},i=function(a,b){return c.readOptFrom(a,b)},j=function(a,b){return d.wrap(a,b)},k=function(a){return d.wrapAll(a)},l=function(a,c){return b.indexOnKey(a,c)},m=function(b,c){return a.consolidateObj(b,c)},n=function(a,b){return c.hasKey(a,b)};return{narrow:e,exclude:f,readOpt:g,readOr:h,readOptFrom:i,wrap:j,wrapAll:k,indexOnKey:l,hasKey:n,consolidate:m}}),g("6n",["4m"],function(a){var b=function(){return a.getOrDie("JSON")},c=function(a){return b().parse(a)},d=function(a,c,d){return b().stringify(a,c,d)};return{parse:c,stringify:d}}),g("4f",["y","x","1i","6n"],function(a,b,c,d){var e=function(a){return c.isObject(a)&&b.keys(a).length>100?" removed due to size":d.stringify(a,null,2)},f=function(b){var c=b.length>10?b.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):b;return a.map(c,function(a){return"Failed path: ("+a.path.join(" > ")+")\n"+a.getErrorInfo()})};return{formatObj:e,formatErrors:f}}),g("6l",["4f","48"],function(a,b){var c=function(a,c){return b.error([{path:a,getErrorInfo:c}])},d=function(b,d,e){return c(b,function(){return'Could not find valid *strict* value for "'+d+'" in '+a.formatObj(e)})},e=function(a,b){return c(a,function(){return'Choice schema did not contain choice key: "'+b+'"'})},f=function(b,d,e){return c(b,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+a.formatObj(d)})},g=function(a,b){return c(a,function(){return"There are unsupported fields: ["+b.join(", ")+"] specified"})},h=function(a,b){return c(a,function(){return b})},i=function(a){return"Failed path: ("+a.path.join(" > ")+")\n"+a.getErrorInfo()};return{missingStrict:d,missingKey:e,missingBranch:f,unsupportedFields:g,custom:h,toString:i}}),g("6m",["6k"],function(a){var b=a.generate([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]}]),c=a.generate([{field:["name","presence","type"]},{state:["name"]}]);return{typeAdt:b,fieldAdt:c}}),g("4d",["4c","14","2a","2c","2d","6l","6m","6k","y","6","w","x","z","48","1i"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o){var p=h.generate([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),q=function(a,b){return p.state(a,j.constant(b))},r=function(a){return p.state(a,j.identity)},s=function(a,b,c){return d.readOptFrom(b,c).fold(function(){return f.missingStrict(a,c,b)},n.value)},t=function(a,b,c){var e=d.readOptFrom(a,b).fold(function(){return c(a)},j.identity);return n.value(e)},u=function(a,b){return n.value(d.readOptFrom(a,b))},v=function(a,b,c){var e=d.readOptFrom(a,b).map(function(b){return b===!0?c(a):b});return n.value(e)},w=function(a,b,c,d){return c.fold(function(c,f,g,h){var i=function(b){return h.extract(a.concat([c]),d,b).map(function(a){return e.wrap(f,d(a))})},l=function(b){return b.fold(function(){var a=e.wrap(f,d(m.none()));return n.value(a)},function(b){return h.extract(a.concat([c]),d,b).map(function(a){return e.wrap(f,d(m.some(a)))})})};return function(){return g.fold(function(){return s(a,b,c).bind(i)},function(a){return t(b,c,a).bind(i)},function(){return u(b,c).bind(l)},function(a){return v(b,c,a).bind(l)},function(a){var d=a(b);return t(b,c,j.constant({})).map(function(a){return k.deepMerge(d,a)}).bind(i)})}()},function(a,c){var f=c(b);return n.value(e.wrap(a,d(f)))})},x=function(a,b,d,e){var f=i.map(d,function(c){return w(a,b,c,e)});return c.consolidateObj(f,{})},y=function(a){var b=function(b,c,d){return a(d).fold(function(a){return f.custom(b,a)},n.value)},c=function(){return"val"},d=function(){return g.typeAdt.itemOf(a)};return{extract:b,toString:c,toDsl:d}},z=function(a){var c=l.keys(a);return i.filter(c,function(c){return b.hasKey(a,c)})},A=function(a){var c=B(a),d=i.foldr(a,function(a,c){return c.fold(function(c){return k.deepMerge(a,b.wrap(c,!0))},j.constant(a))},{}),e=function(a,e,g){var h=o.isBoolean(g)?[]:z(g),j=i.filter(h,function(a){return!b.hasKey(d,a)});return 0===j.length?c.extract(a,e,g):f.unsupportedFields(a,j)};return{extract:e,toString:c.toString,toDsl:c.toDsl}},B=function(a){var b=function(b,c,d){return x(b,d,a,c)},c=function(){var b=i.map(a,function(a){return a.fold(function(a,b,c,d){return a+" -> "+d.toString()},function(a,b){return"state("+a+")"})});return"obj{\n"+b.join("\n")+"}"},d=function(){return g.typeAdt.objOf(i.map(a,function(a){return a.fold(function(a,b,c,d){return g.fieldAdt.field(a,c,d)},function(a,b){return g.fieldAdt.state(a)})}))};return{extract:b,toString:c,toDsl:d}},C=function(a){var b=function(b,d,e){var f=i.map(e,function(c,e){return a.extract(b.concat(["["+e+"]"]),d,c)});return c.consolidateArr(f)},d=function(){return"array("+a.toString()+")"},e=function(){return g.typeAdt.arrOf(a)};return{extract:b,toString:d,toDsl:e}},D=function(b,c){var d=function(a,c){return C(y(b)).extract(a,j.identity,c)},e=function(b,e,f){var g=l.keys(f);return d(b,g).bind(function(d){var g=i.map(d,function(b){return p.field(b,b,a.strict(),c)});return B(g).extract(b,e,f)})},f=function(){return"setOf("+c.toString()+")"},h=function(){return g.typeAdt.setOf(b,c)};return{extract:e,toString:f,toDsl:h}},E=y(n.value),F=j.compose(C,B);return{anyValue:j.constant(E),value:y,obj:B,objOnly:A,arr:C,setOf:D,arrOfObj:F,state:p.state,field:p.field,output:q,snapshot:r}}),g("29",["4c","4d","48","1i"],function(a,b,c,d){var e=function(c){return b.field(c,c,a.strict(),b.anyValue())},f=function(c,d){return b.field(c,c,a.strict(),d)},g=function(e){return b.field(e,e,a.strict(),b.value(function(a){return d.isFunction(a)?c.value(a):c.error("Not a function")}))},h=function(d,e){return b.field(d,d,a.asOption(),b.value(function(a){return c.error("The field: "+d+" is forbidden. "+e)}))},i=function(a,b){return f(a,b)},j=function(c,d){return b.field(c,c,a.strict(),b.obj(d))},k=function(c,d){return b.field(c,c,a.strict(),b.arrOfObj(d))},l=function(c){return b.field(c,c,a.asOption(),b.anyValue())},m=function(c,d){return b.field(c,c,a.asOption(),d)},n=function(c,d){return b.field(c,c,a.asOption(),b.obj(d))},o=function(c,d){return b.field(c,c,a.asOption(),b.objOnly(d))},p=function(c,d){return b.field(c,c,a.defaulted(d),b.anyValue())},q=function(c,d,e){return b.field(c,c,a.defaulted(d),e)},r=function(c,d,e){return b.field(c,c,a.defaulted(d),b.obj(e))},s=function(a,c,d,e){return b.field(a,c,d,e)},t=function(a,c){return b.state(a,c)};return{strict:e,strictOf:f,strictObjOf:j,strictArrayOf:i,strictArrayOfObj:k,strictFunction:g,forbid:h,option:l,optionOf:m,optionObjOf:n,optionObjOfOnly:o,defaulted:p,defaultedOf:q,defaultedObjOf:r,field:s,state:t}}),g("4e",["14","6l","4d","6m","x"],function(a,b,c,d,e){var f=function(d,e,f,g,h){var i=a.readOptFrom(g,h);return i.fold(function(){return b.missingBranch(d,g,h)},function(a){return c.obj(a).extract(d.concat(["branch: "+h]),e,f)})},g=function(c,g){var h=function(d,e,h){var i=a.readOptFrom(h,c);return i.fold(function(){return b.missingKey(d,c)},function(a){return f(d,e,h,g,a)})},i=function(){return"chooseOn("+c+"). Possible values: "+e.keys(g)},j=function(){return d.typeAdt.choiceOf(c,g)};return{extract:h,toString:i,toDsl:j}};return{choose:g}}),g("2e",["4e","4d","4f","6","48","v"],function(a,b,c,d,e,f){var g=b.value(e.value),h=function(a){return b.arrOfObj(a)},i=function(){return b.arr(g)},j=b.arr,k=b.obj,l=b.objOnly,m=b.setOf,n=function(a){return b.value(a)},o=function(a,b,c,d){return b.extract([a],c,d).fold(function(a){return e.error({input:d,errors:a})},e.value)},p=function(a,b,c){return o(a,b,d.constant,c)},q=function(a,b,c){return o(a,b,d.identity,c)},r=function(a){return a.fold(function(a){throw new f(u(a))},d.identity)},s=function(a,b,c){return r(q(a,b,c))},t=function(a,b,c){return r(p(a,b,c))},u=function(a){return"Errors: \n"+c.formatErrors(a.errors)+"\n\nInput object: "+c.formatObj(a.input)},v=function(b,c){return a.choose(b,c)};return{anyValue:d.constant(g),arrOfObj:h,arrOf:j,arrOfVal:i,valueOf:n,setOf:m,objOf:k,objOfOnly:l,asStruct:p,asRaw:q,asStructOrDie:t,asRawOrDie:s,getOrDie:r,formatError:u,choose:v}}),g("47",["29","14","2e","1i","y","6n","6","u","v"],function(a,b,c,d,e,f,g,h,i){var j=function(d){if(!b.hasKey(d,"can")&&!b.hasKey(d,"abort")&&!b.hasKey(d,"run"))throw new i("EventHandler defined by: "+f.stringify(d,null,2)+" does not have can, abort, or run!");return c.asRawOrDie("Extracting event.handler",c.objOfOnly([a.defaulted("can",g.constant(!0)),a.defaulted("abort",g.constant(!1)),a.defaulted("run",g.noop)]),d)},k=function(a,b){return function(){var c=h.prototype.slice.call(arguments,0);return e.foldl(a,function(a,d){return a&&b(d).apply(void 0,c)},!0)}},l=function(a,b){
return function(){var c=h.prototype.slice.call(arguments,0);return e.foldl(a,function(a,d){return a||b(d).apply(void 0,c)},!1)}},m=function(a){return d.isFunction(a)?{can:g.constant(!0),abort:g.constant(!1),run:a}:a},n=function(a){var b=k(a,function(a){return a.can}),c=l(a,function(a){return a.abort}),d=function(){var b=h.prototype.slice.call(arguments,0);e.each(a,function(a){a.run.apply(void 0,b)})};return j({can:b,abort:c,run:d})};return{read:m,fuse:n,nu:j}}),g("3d",["43","2","t","47","14"],function(a,b,c,d,e){var f=e.wrapAll,g=function(a,b){return{key:a,value:d.nu({abort:b})}},h=function(a,b){return{key:a,value:d.nu({can:b})}},i=function(a){return{key:a,value:d.nu({run:function(a,b){b.event().prevent()}})}},j=function(a,b){return{key:a,value:d.nu({run:b})}},k=function(a,b,c){return{key:a,value:d.nu({run:function(a){b.apply(void 0,[a].concat(c))}})}},l=function(a){return function(b){return j(a,b)}},m=function(b){return function(c){return{key:b,value:d.nu({run:function(b,d){a.isSource(b,d)&&c(b,d)}})}}},n=function(a,c){return j(a,function(d,e){d.getSystem().getByUid(c).each(function(c){b.dispatchEvent(c,c.element(),a,e)})})},o=function(a,b,c){var d=b.partUids()[c];return n(a,d)},p=function(a,b){return j(a,function(a,c){a.getSystem().getByDom(c.event().target()).each(function(d){b(a,d,c)})})},q=function(a){return j(a,function(a,b){b.cut()})},r=function(a){return j(a,function(a,b){b.stop()})};return{derive:f,run:j,preventDefault:i,runActionExtra:k,runOnAttached:m(c.attachedToDom()),runOnDetached:m(c.detachedFromDom()),runOnInit:m(c.systemInit()),runOnExecute:l(c.execute()),redirectToUid:n,redirectToPart:o,runWithTarget:p,abort:g,can:h,cutter:q,stopper:r}}),g("4a",["z"],function(a){var b=function(a,b,c){return a},c=function(a,b){return a},d=function(a,b){return a},e=a.none;return{markAsBehaviourApi:b,markAsExtraApi:c,markAsSketchApi:d,getAnnotation:e}}),g("4k",["y","6","u","v"],function(a,b,c,d){return function(){var e=arguments;return function(){for(var f=new c(arguments.length),g=0;g<f.length;g++)f[g]=arguments[g];if(e.length!==f.length)throw new d('Wrong number of arguments to struct. Expected "['+e.length+']", got '+f.length+" arguments");var h={};return a.each(e,function(a,c){h[a]=b.constant(f[c])}),h}}}),g("6p",["y","1i","v"],function(a,b,c){var d=function(a){return a.slice(0).sort()},e=function(a,b){throw new c("All required keys ("+d(a).join(", ")+") were not specified. Specified keys were: "+d(b).join(", ")+".")},f=function(a){throw new c("Unsupported keys for object: "+d(a).join(", "))},g=function(d,e){if(!b.isArray(e))throw new c("The "+d+" fields must be an array. Was: "+e+".");a.each(e,function(a){if(!b.isString(a))throw new c("The value "+a+" in the "+d+" fields was not a string.")})},h=function(a,b){throw new c("All values need to be of type: "+b+". Keys ("+d(a).join(", ")+") were not.")},i=function(b){var e=d(b),f=a.find(e,function(a,b){return b<e.length-1&&a===e[b+1]});f.each(function(a){throw new c("The field: "+a+" occurs more than once in the combined fields: ["+e.join(", ")+"].")})};return{sort:d,reqMessage:e,unsuppMessage:f,validateStrArr:g,invalidTypeMessage:h,checkDupes:i}}),g("4l",["y","6","x","z","6p","v","2n"],function(a,b,c,d,e,f,g){return function(h,i){var j=h.concat(i);if(0===j.length)throw new f("You must specify at least one required or optional field.");return e.validateStrArr("required",h),e.validateStrArr("optional",i),e.checkDupes(j),function(f){var k=c.keys(f),l=a.forall(h,function(b){return a.contains(k,b)});l||e.reqMessage(h,k);var m=a.filter(k,function(b){return!a.contains(j,b)});m.length>0&&e.unsuppMessage(m);var n={};return a.each(h,function(a){n[a]=b.constant(f[a])}),a.each(i,function(a){n[a]=b.constant(g.prototype.hasOwnProperty.call(f,a)?d.some(f[a]):d.none())}),n}}}),g("2o",["4k","4l"],function(a,b){return{immutable:a,immutableBag:b}}),g("6o",["6n","2o","2m"],function(a,b,c){var d=b.immutableBag(["tag"],["classes","attributes","styles","value","innerHtml","domChildren","defChildren"]),e=function(b){var c=f(b);return a.stringify(c,null,2)},f=function(a){return{tag:a.tag(),classes:a.classes().getOr([]),attributes:a.attributes().getOr({}),styles:a.styles().getOr({}),value:a.value().getOr("<none>"),innerHtml:a.innerHtml().getOr("<none>"),defChildren:a.defChildren().getOr("<none>"),domChildren:a.domChildren().fold(function(){return"<none>"},function(a){return 0===a.length?"0 children, but still specified":c(a.length)})}};return{nu:d,defToStr:e,defToRaw:f}}),g("4b",["6o","14","y","x","w","6n","2o"],function(a,b,c,d,e,f,g){var h=["classes","attributes","styles","value","innerHtml","defChildren","domChildren"],i=g.immutableBag([],h),j=function(a){var b={},e=d.keys(a);return c.each(e,function(c){a[c].each(function(a){b[c]=a})}),i(b)},k=function(a){var b=l(a);return f.stringify(b,null,2)},l=function(a){return{classes:a.classes().getOr("<none>"),attributes:a.attributes().getOr("<none>"),styles:a.styles().getOr("<none>"),value:a.value().getOr("<none>"),innerHtml:a.innerHtml().getOr("<none>"),defChildren:a.defChildren().getOr("<none>"),domChildren:a.domChildren().fold(function(){return"<none>"},function(a){return 0===a.length?"0 children, but still specified":String(a.length)})}},m=function(a,c,d){return c.fold(function(){return d.fold(function(){return{}},function(c){return b.wrap(a,c)})},function(c){return d.fold(function(){return b.wrap(a,c)},function(c){return b.wrap(a,c)})})},n=function(c,d){var f=e.deepMerge({tag:c.tag(),classes:d.classes().getOr([]).concat(c.classes().getOr([])),attributes:e.merge(c.attributes().getOr({}),d.attributes().getOr({})),styles:e.merge(c.styles().getOr({}),d.styles().getOr({}))},d.innerHtml().or(c.innerHtml()).map(function(a){return b.wrap("innerHtml",a)}).getOr({}),m("domChildren",d.domChildren(),c.domChildren()),m("defChildren",d.defChildren(),c.defChildren()),d.value().or(c.value()).map(function(a){return b.wrap("value",a)}).getOr({}));return a.nu(f)};return{nu:i,derive:j,merge:n,modToStr:k,modToRaw:l}}),g("27",["3d","4a","4b","29","14","2e","6","w","x","z","17","u","16","v"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n){var o=function(b,c,d){return a.runOnExecute(function(a){d(a,b,c)})},p=function(b,c,d){return a.runOnInit(function(a,e){d(a,b,c)})},q=function(a,b,c,e,g,h){var i=f.objOfOnly(a),j=d.optionObjOf(b,[d.optionObjOfOnly("config",a)]);return u(i,j,b,c,e,g,h)},r=function(a,b,c,e,f,g){var h=a,i=d.optionObjOf(b,[d.optionOf("config",a)]);return u(h,i,b,c,e,f,g)},s=function(a,c,d){var e=function(b){var e=arguments;return b.config({name:g.constant(a)}).fold(function(){throw new n("We could not find any behaviour configuration for: "+a+". Using API: "+d)},function(a){var d=l.prototype.slice.call(e,1);return c.apply(void 0,[b,a.config,a.state].concat(d))})};return b.markAsBehaviourApi(e,d,c)},t=function(a){return{key:a,value:void 0}},u=function(a,d,l,m,n,o,p){var q=function(a){return e.hasKey(a,l)?a[l]():j.none()},r=i.map(n,function(a,b){return s(l,a,b)}),u=i.map(o,function(a,c){return b.markAsExtraApi(a,c)}),v=h.deepMerge(u,r,{revoke:g.curry(t,l),config:function(b){var c=f.asStructOrDie(l+"-config",a,b);return{key:l,value:{config:c,me:v,configAsRaw:k.cached(function(){return f.asRawOrDie(l+"-config",a,b)}),initialConfig:b,state:p}}},schema:function(){return d},exhibit:function(a,b){return q(a).bind(function(a){return e.readOptFrom(m,"exhibit").map(function(c){return c(b,a.config,a.state)})}).getOr(c.nu({}))},name:function(){return l},handlers:function(a){return q(a).bind(function(a){return e.readOptFrom(m,"events").map(function(b){return b(a.config,a.state)})}).getOr({})}});return v};return{executeEvent:o,loadEvent:p,create:q,createModes:r}}),g("6q",["y","6","x","1i","6p","v"],function(a,b,c,d,e,f){var g=function(a,b){return h(a,b,{validate:d.isFunction,label:"function"})},h=function(b,d,g){if(0===d.length)throw new f("You must specify at least one required field.");return e.validateStrArr("required",d),e.checkDupes(d),function(f){var h=c.keys(f),i=a.forall(d,function(b){return a.contains(h,b)});i||e.reqMessage(d,h),b(d,h);var j=a.filter(d,function(a){return!g.validate(f[a],a)});return j.length>0&&e.invalidTypeMessage(j,g.label),f}},i=function(b,c){var d=a.filter(c,function(c){return!a.contains(b,c)});d.length>0&&e.unsuppMessage(d)},j=b.noop;return{exactly:b.curry(g,i),ensure:b.curry(g,j),ensureWith:b.curry(h,j)}}),g("4g",["6q"],function(a){return a.ensure(["readState"])}),h("1w",Math),g("28",["4g","1w"],function(a,b){var c=function(){return a({readState:function(){return"No State required"}})};return{init:c}}),g("q",["27","28","29","14","2e","6"],function(a,b,c,d,e,f){var g=function(a){return d.wrapAll(a)},h=e.objOfOnly([c.strict("fields"),c.strict("name"),c.defaulted("active",{}),c.defaulted("apis",{}),c.defaulted("extra",{}),c.defaulted("state",b)]),i=function(b){var c=e.asRawOrDie("Creating behaviour: "+b.name,h,b);return a.create(c.fields,c.name,c.active,c.apis,c.extra,c.state)},j=e.objOfOnly([c.strict("branchKey"),c.strict("branches"),c.strict("name"),c.defaulted("active",{}),c.defaulted("apis",{}),c.defaulted("extra",{}),c.defaulted("state",b)]),k=function(b){var c=e.asRawOrDie("Creating behaviour: "+b.name,j,b);return a.createModes(e.choose(c.branchKey,c.branches),c.name,c.active,c.apis,c.extra,c.state)};return{derive:g,revoke:f.constant(void 0),noActive:f.constant({}),noApis:f.constant({}),noExtra:f.constant({}),noState:f.constant(b),create:i,createModes:k}}),g("4h",[],function(){return function(a,b,c){var d=c||!1,e=function(){b(),d=!0},f=function(){a(),d=!1},g=function(){var a=d?f:e;a()},h=function(){return d};return{on:e,off:f,toggle:g,isOn:h}}}),g("b",["1d"],function(a){var b=function(a){var b=a.dom().nodeName;return b.toLowerCase()},c=function(a){return a.dom().nodeType},d=function(a){return a.dom().nodeValue},e=function(a){return function(b){return c(b)===a}},f=function(d){return c(d)===a.COMMENT||"#comment"===b(d)},g=e(a.ELEMENT),h=e(a.TEXT),i=e(a.DOCUMENT);return{name:b,type:c,value:d,isElement:g,isText:h,isDocument:i,isComment:f}}),g("4i",["1i","y","x","b","v","16"],function(a,b,c,d,e,f){var g=function(b,c,d){if(!(a.isString(d)||a.isBoolean(d)||a.isNumber(d)))throw f.error("Invalid call to Attr.set. Key ",c,":: Value ",d,":: Element ",b),new e("Attribute value was not simple");b.setAttribute(c,d+"")},h=function(a,b,c){g(a.dom(),b,c)},i=function(a,b){var d=a.dom();c.each(b,function(a,b){g(d,b,a)})},j=function(a,b){var c=a.dom().getAttribute(b);return null===c?void 0:c},k=function(a,b){var c=a.dom();return!(!c||!c.hasAttribute)&&c.hasAttribute(b)},l=function(a,b){a.dom().removeAttribute(b)},m=function(a){var b=a.dom().attributes;return void 0===b||null===b||0===b.length},n=function(a){return b.foldl(a.dom().attributes,function(a,b){return a[b.name]=b.value,a},{})},o=function(a,b,c){k(a,c)&&!k(b,c)&&h(b,c,j(a,c))},p=function(a,c,e){d.isElement(a)&&d.isElement(c)&&b.each(e,function(b){o(a,c,b)})};return{clone:n,set:h,setAll:i,get:j,has:k,remove:l,hasNone:m,transfer:p}}),g("6r",["y","4i"],function(a,b){var c=function(a,c){var d=b.get(a,c);return void 0===d||""===d?[]:d.split(" ")},d=function(a,d,e){var f=c(a,d),g=f.concat([e]);b.set(a,d,g.join(" "))},e=function(d,e,f){var g=a.filter(c(d,e),function(a){return a!==f});g.length>0?b.set(d,e,g.join(" ")):b.remove(d,e)};return{read:c,add:d,remove:e}}),g("4j",["y","6r"],function(a,b){var c=function(a){return void 0!==a.dom().classList},d=function(a){return b.read(a,"class")},e=function(a,c){return b.add(a,"class",c)},f=function(a,c){return b.remove(a,"class",c)},g=function(b,c){a.contains(d(b),c)?f(b,c):e(b,c)};return{get:d,add:e,remove:f,toggle:g,supports:c}}),g("2f",["4h","4i","4j"],function(a,b,c){var d=function(a,b){c.supports(a)?a.dom().classList.add(b):c.add(a,b)},e=function(a){var d=c.supports(a)?a.dom().classList:c.get(a);0===d.length&&b.remove(a,"class")},f=function(a,b){if(c.supports(a)){var d=a.dom().classList;d.remove(b)}else c.remove(a,b);e(a)},g=function(a,b){return c.supports(a)?a.dom().classList.toggle(b):c.toggle(a,b)},h=function(b,d){var e=c.supports(b),f=b.dom().classList,g=function(){e?f.remove(d):c.remove(b,d)},h=function(){e?f.add(d):c.add(b,d)};return a(g,h,i(b,d))},i=function(a,b){return c.supports(a)&&a.dom().classList.contains(b)};return{add:d,remove:f,toggle:g,toggler:h,has:i}}),g("r",["2f"],function(a){var b=function(b,c,d){a.remove(b,d),a.add(b,c)},c=function(a,c,d){b(a.element(),c.alpha(),c.omega())},d=function(a,c,d){b(a.element(),c.omega(),c.alpha())},e=function(b,c,d){a.remove(b.element(),c.alpha()),a.remove(b.element(),c.omega())},f=function(b,c,d){return a.has(b.element(),c.alpha())},g=function(b,c,d){return a.has(b.element(),c.omega())};return{toAlpha:c,toOmega:d,isAlpha:f,isOmega:g,clear:e}}),g("s",["29"],function(a){return[a.strict("alpha"),a.strict("omega")]}),g("1",["q","r","s"],function(a,b,c){return a.create({fields:c,name:"swapping",apis:b})}),g("2p",[],function(){var a=function(a,b){var c=[],d=function(a){return c.push(a),b(a)},e=b(a);do e=e.bind(d);while(e.isSome());return c};return{toArray:a}}),g("10",["1i","y","6","z","2o","2p","1a","a"],function(a,b,c,d,e,f,g,h){var i=function(a){return h.fromDom(a.dom().ownerDocument)},j=function(a){var b=i(a);return h.fromDom(b.dom().documentElement)},k=function(a){var b=a.dom(),c=b.ownerDocument.defaultView;return h.fromDom(c)},l=function(a){var b=a.dom();return d.from(b.parentNode).map(h.fromDom)},m=function(a){return l(a).bind(function(c){var d=u(c);return b.findIndex(d,function(b){return g.eq(a,b)})})},n=function(b,d){for(var e=a.isFunction(d)?d:c.constant(!1),f=b.dom(),g=[];null!==f.parentNode&&void 0!==f.parentNode;){var i=f.parentNode,j=h.fromDom(i);if(g.push(j),e(j)===!0)break;f=i}return g},o=function(a){var c=function(c){return b.filter(c,function(b){return!g.eq(a,b)})};return l(a).map(u).map(c).getOr([])},p=function(a){var b=a.dom();return d.from(b.offsetParent).map(h.fromDom)},q=function(a){var b=a.dom();return d.from(b.previousSibling).map(h.fromDom)},r=function(a){var b=a.dom();return d.from(b.nextSibling).map(h.fromDom)},s=function(a){return b.reverse(f.toArray(a,q))},t=function(a){return f.toArray(a,r)},u=function(a){var c=a.dom();return b.map(c.childNodes,h.fromDom)},v=function(a,b){var c=a.dom().childNodes;return d.from(c[b]).map(h.fromDom)},w=function(a){return v(a,0)},x=function(a){return v(a,a.dom().childNodes.length-1)},y=function(a){return a.dom().childNodes.length},z=function(a){return a.dom().hasChildNodes()},A=e.immutable("element","offset"),B=function(a,b){var c=u(a);return c.length>0&&b<c.length?A(c[b],0):A(a,b)};return{owner:i,defaultView:k,documentElement:j,parent:l,findIndex:m,parents:n,siblings:o,prevSibling:q,offsetParent:p,prevSiblings:s,nextSibling:r,nextSiblings:t,children:u,child:v,firstChild:w,lastChild:x,childNodesCount:y,hasChildNodes:z,leaf:B}}),g("9",["10"],function(a){var b=function(b,c){var d=a.parent(b);d.each(function(a){a.dom().insertBefore(c.dom(),b.dom())})},c=function(c,d){var f=a.nextSibling(c);f.fold(function(){var b=a.parent(c);b.each(function(a){e(a,d)})},function(a){b(a,d)})},d=function(b,c){var d=a.firstChild(b);d.fold(function(){e(b,c)},function(a){b.dom().insertBefore(c.dom(),a.dom())})},e=function(a,b){a.dom().appendChild(b.dom())},f=function(c,d,f){a.child(c,f).fold(function(){e(c,d)},function(a){b(a,d)})},g=function(a,c){b(a,c),e(c,a)};return{before:b,after:c,prepend:d,append:e,appendAt:f,wrap:g}}),g("2s",["y","9"],function(a,b){var c=function(c,d){a.each(d,function(a){b.before(c,a)})},d=function(c,d){a.each(d,function(a,e){var f=0===e?c:d[e-1];b.after(f,a)})},e=function(c,d){a.each(d.slice().reverse(),function(a){b.prepend(c,a)})},f=function(c,d){a.each(d,function(a){b.append(c,a)})};return{before:c,after:d,prepend:e,append:f}}),g("11",["y","2s","10"],function(a,b,c){var d=function(b){b.dom().textContent="",a.each(c.children(b),function(a){e(a)})},e=function(a){var b=a.dom();null!==b.parentNode&&b.parentNode.removeChild(b)},f=function(a){var d=c.children(a);d.length>0&&b.before(a,d),e(a)};return{empty:d,remove:e,unwrap:f}}),g("12",["17","a","b","1b"],function(a,b,c,d){var e=function(a){var b=c.isText(a)?a.dom().parentNode:a.dom();return void 0!==b&&null!==b&&b.ownerDocument.body.contains(b)},f=a.cached(function(){return g(b.fromDom(d))}),g=function(a){var c=a.dom().body;if(null===c||void 0===c)throw"Body is not available yet";return b.fromDom(c)};return{body:f,getBody:g,inBody:e}}),g("3",["2","t","y","z","9","11","12","10"],function(a,b,c,d,e,f,g,h){var i=function(d){a.emit(d,b.detachedFromDom());var e=d.components();c.each(e,i)},j=function(d){var e=d.components();c.each(e,j),a.emit(d,b.attachedToDom())},k=function(a,b){l(a,b,e.append)},l=function(a,b,c){a.getSystem().addToWorld(b),c(a.element(),b.element()),g.inBody(a.element())&&j(b),a.syncComponents()},m=function(a){i(a),f.remove(a.element()),a.getSystem().removeFromWorld(a)},n=function(a){var b=h.parent(a.element()).bind(function(b){return a.getSystem().getByDom(b).fold(d.none,d.some)});m(a),b.each(function(a){a.syncComponents()})},o=function(a){var b=a.components();c.each(b,m),f.empty(a.element()),a.syncComponents()},p=function(a,b){e.append(a,b.element());var d=h.children(b.element());c.each(d,function(a){b.getByDom(a).each(j)})},q=function(a){var b=h.children(a.element());c.each(b,function(b){a.getByDom(b).each(i)}),f.remove(a.element())};return{attach:k,attachWith:l,detach:n,detachChildren:o,attachSystem:p,detachSystem:q}}),g("6s",["y","a","10","1b"],function(a,b,c,d){var e=function(a,e){var f=e||d,g=f.createElement("div");return g.innerHTML=a,c.children(b.fromDom(g))},f=function(c,d){return a.map(c,function(a){return b.fromTag(a,d)})},g=function(c,d){return a.map(c,function(a){return b.fromText(a,d)})},h=function(c){return a.map(c,b.fromDom)};return{fromHtml:e,fromTags:f,fromText:g,fromDom:h}}),g("4n",["a","6s","9","2s","11","10"],function(a,b,c,d,e,f){var g=function(a){return a.dom().innerHTML},h=function(g,h){var i=f.owner(g),j=i.dom(),k=a.fromDom(j.createDocumentFragment()),l=b.fromHtml(h,j);d.append(k,l),e.empty(g),c.append(g,k)},i=function(b){var d=a.fromTag("div"),e=a.fromDom(b.dom().cloneNode(!0));return c.append(d,e),g(d)};return{get:g,set:h,getOuter:i}}),g("4o",["4i","a","9","2s","11","10"],function(a,b,c,d,e,f){var g=function(a,c){return b.fromDom(a.dom().cloneNode(c))},h=function(a){return g(a,!1)},i=function(a){return g(a,!0)},j=function(c,d){var e=b.fromTag(d),f=a.clone(c);return a.setAll(e,f),e},k=function(a,b){var c=j(a,b),e=f.children(i(a));return d.append(c,e),c},l=function(a,b){var g=j(a,b);c.before(a,g);var h=f.children(a);return d.append(g,h),e.remove(a),g};return{shallow:h,shallowAs:j,deep:i,copy:k,mutate:l}}),g("2t",["4n","4o"],function(a,b){var c=function(c){var d=b.shallow(c);return a.getOuter(d)};return{getHtml:c}}),g("13",["2t"],function(a){var b=function(b){return a.getHtml(b)};return{element:b}}),g("15",["z"],function(a){var b=function(a){for(var b=[],c=function(a){b.push(a)},d=0;d<a.length;d++)a[d].each(c);return b},c=function(b,c){for(var d=0;d<b.length;d++){var e=c(b[d],d);if(e.isSome())return e}return a.none()},d=function(b,c){for(var d=[],e=0;e<b.length;e++){var f=b[e];if(!f.isSome())return a.none();d.push(f.getOrDie())}return a.some(c.apply(null,d))};return{cat:b,findMap:c,liftN:d}}),g("4",["t","13","14","y","6","x","15","16","v"],function(a,b,c,d,e,f,g,h,i){var j="unknown",k=!0,l="__CHROME_INSPECTOR_CONNECTION_TO_ALLOY__",m=[],n=["alloy/data/Fields","alloy/debugging/Debugging"],o=function(){if(k===!1)return j;var a=new i;if(void 0!==a.stack){var b=a.stack.split("\n");return d.find(b,function(a){return a.indexOf("alloy")>0&&!d.exists(n,function(b){return a.indexOf(b)>-1})}).getOr(j)}return j},p=function(a,b,c){},q={logEventCut:e.noop,logEventStopped:e.noop,logNoParent:e.noop,logEventNoHandlers:e.noop,logEventResponse:e.noop,write:e.noop},r=function(c,e,f){var g=k&&("*"===m||d.contains(m,c))?function(){var f=[];return{logEventCut:function(a,b,c){f.push({outcome:"cut",target:b,purpose:c})},logEventStopped:function(a,b,c){f.push({outcome:"stopped",target:b,purpose:c})},logNoParent:function(a,b,c){f.push({outcome:"no-parent",target:b,purpose:c})},logEventNoHandlers:function(a,b){f.push({outcome:"no-handlers-left",target:b})},logEventResponse:function(a,b,c){f.push({outcome:"response",purpose:c,target:b})},write:function(){d.contains(["mousemove","mouseover","mouseout",a.systemInit()],c)||h.log(c,{event:c,target:e.dom(),sequence:d.map(f,function(a){return d.contains(["cut","stopped","response"],a.outcome)?"{"+a.purpose+"} "+a.outcome+" at ("+b.element(a.target)+")":a.outcome})})}}}():q,i=f(g);return g.write(),i},s=function(a){var c=function(a){var e=a.spec();return{"(original.spec)":e,"(dom.ref)":a.element().dom(),"(element)":b.element(a.element()),"(initComponents)":d.map(void 0!==e.components?e.components:[],c),"(components)":d.map(a.components(),c),"(bound.events)":f.mapToArray(a.events(),function(a,b){return[b]}).join(", "),"(behaviours)":void 0!==e.behaviours?f.map(e.behaviours,function(b,c){return void 0===b?"--revoked--":{config:b.configAsRaw(),"original-config":b.initialConfig,state:a.readState(c)}}):"none"}};return c(a)},t=function(){return void 0!==window[l]?window[l]:(window[l]={systems:{},lookup:function(a){var d=window[l].systems,e=f.keys(d);return g.findMap(e,function(e){var f=d[e];return f.getByUid(a).toOption().map(function(a){return c.wrap(b.element(a.element()),s(a))})})}},window[l])},u=function(a,b){var c=t();c.systems[a]=b};return{logHandler:p,noLogger:e.constant(q),getTrace:o,monitorEvent:r,isDebugging:e.constant(k),registerInspector:u}}),g("5",[],function(){var a=function(b){var c=b,d=function(){return c},e=function(a){c=a},f=function(){return a(d())};return{get:d,set:e,clone:f}};return a}),g("4p",["1i","z"],function(a,b){return function(c,d,e,f,g){return c(e,f)?b.some(e):a.isFunction(g)&&g(e)?b.none():d(e,f,g)}}),g("2u",["1i","y","6","z","12","1a","a","4p"],function(a,b,c,d,e,f,g,h){var i=function(a){return n(e.body(),a)},j=function(b,e,f){for(var h=b.dom(),i=a.isFunction(f)?f:c.constant(!1);h.parentNode;){h=h.parentNode;var j=g.fromDom(h);if(e(j))return d.some(j);if(i(j))break}return d.none()},k=function(a,b,c){var d=function(a){return b(a)};return h(d,j,a,b,c)},l=function(a,b){var c=a.dom();return c.parentNode?m(g.fromDom(c.parentNode),function(c){return!f.eq(a,c)&&b(c)}):d.none()},m=function(a,d){var e=b.find(a.dom().childNodes,c.compose(d,g.fromDom));return e.map(g.fromDom)},n=function(a,b){var c=function(a){for(var e=0;e<a.childNodes.length;e++){if(b(g.fromDom(a.childNodes[e])))return d.some(g.fromDom(a.childNodes[e]));var f=c(a.childNodes[e]);if(f.isSome())return f}return d.none()};return c(a.dom())};return{first:i,ancestor:j,closest:k,sibling:l,child:m,descendant:n}}),g("1c",["2u"],function(a){var b=function(b){return a.first(b).isSome()},c=function(b,c,d){return a.ancestor(b,c,d).isSome()},d=function(b,c,d){return a.closest(b,c,d).isSome()},e=function(b,c){return a.sibling(b,c).isSome()},f=function(b,c){return a.child(b,c).isSome()},g=function(b,c){return a.descendant(b,c).isSome()};return{any:b,ancestor:c,closest:d,sibling:e,child:f,descendant:g}}),g("8",["6","z","1a","a","1c","10","1b"],function(a,b,c,d,e,f,g){var h=function(a){a.dom().focus()},i=function(a){a.dom().blur()},j=function(a){var b=f.owner(a).dom();return a.dom()===b.activeElement},k=function(a){var c=void 0!==a?a.dom():g;return b.from(c.activeElement).map(d.fromDom)},l=function(b){var d=f.owner(b),g=k(d).filter(function(d){return e.closest(d,a.curry(c.eq,b))});g.fold(function(){h(b)},a.noop)},m=function(a){return k(f.owner(a)).filter(function(b){return a.dom().contains(b.dom())})};return{hasFocus:j,focus:h,blur:i,active:k,search:m,focusInside:l}}),h("1e",tinymce.util.Tools.resolve),g("c",["1e"],function(a){return a("tinymce.ThemeManager")}),g("d",["1e"],function(a){return a("tinymce.dom.DOMUtils")}),g("e",["1b"],function(a){var b=function(b){var c=a.createElement("a");c.target="_blank",c.href=b.href,c.rel="noreferrer noopener";var d=a.createEvent("MouseEvents");d.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),a.body.appendChild(c),c.dispatchEvent(d),a.body.removeChild(c)};return{openLink:b}}),g("f",[],function(){var a=function(a){return a.settings.skin===!1};return{isSkinDisabled:a}}),g("g",["6"],function(a){var b="formatChanged",c="orientationChanged",d="dropupDismissed";return{formatChanged:a.constant(b),orientationChanged:a.constant(c),dropupDismissed:a.constant(d)}}),g("2v",["3d","t","13","2e","y","x"],function(a,b,c,d,e,f){var g=function(a,b){return b.universal()?a:e.filter(a,function(a){return e.contains(b.channels(),a)})},h=function(h){return a.derive([a.run(b.receive(),function(a,b){var i=h.channels(),j=f.keys(i),k=g(j,b);e.each(k,function(e){var f=i[e](),g=f.schema(),h=d.asStructOrDie("channel["+e+"] data\nReceiver: "+c.element(a.element()),g,b.data());f.onReceive()(a,h)})})])};return{events:h}}),g("6t",["29","2e","6"],function(a,b,c){var d=[a.strict("menu"),a.strict("selectedMenu")],e=[a.strict("item"),a.strict("selectedItem")],f=b.objOfOnly(e.concat(d)),g=b.objOfOnly(e);return{menuFields:c.constant(d),itemFields:c.constant(e),schema:c.constant(f),itemSchema:c.constant(g)}}),g("4q",["4","6t","4c","29","2e","y","6","z","48","16"],function(a,b,c,d,e,f,g,h,i,j){var k=d.strictObjOf("initSize",[d.strict("numColumns"),d.strict("numRows")]),l=function(){return d.strictOf("markers",b.itemSchema())},m=function(){return d.strictOf("markers",b.schema())},n=function(){return d.strictObjOf("markers",[d.strict("backgroundMenu")].concat(b.menuFields()).concat(b.itemFields()))},o=function(a){return d.strictObjOf("markers",f.map(a,d.strict))},p=function(b,c,f){var g=a.getTrace();return d.field(c,c,f,e.valueOf(function(d){return i.value(function(){return a.logHandler(b,c,g),d.apply(void 0,arguments)})}))},q=function(a){return p("onHandler",a,c.defaulted(g.noop))},r=function(a){return p("onKeyboardHandler",a,c.defaulted(h.none))},s=function(a){return p("onHandler",a,c.strict())},t=function(a){return p("onKeyboardHandler",a,c.strict())},u=function(a,b){return d.state(a,g.constant(b))},v=function(a){return d.state(a,g.identity)};return{initSize:g.constant(k),itemMarkers:l,menuMarkers:m,tieredMenuMarkers:n,markers:o,onHandler:q,onKeyboardHandler:r,onStrictHandler:s,onStrictKeyboardHandler:t,output:u,snapshot:v}}),g("2w",["4q","29","2e","48"],function(a,b,c,d){return[b.strictOf("channels",c.setOf(d.value,c.objOfOnly([a.onStrictHandler("onReceive"),b.defaulted("schema",c.anyValue())])))]}),g("1f",["q","2v","2w"],function(a,b,c){return a.create({fields:c,name:"receiving",active:b})}),g("2y",["2f"],function(a){var b=function(a,b,c){var d=f(a,b),e=b.aria();e.update()(a,e,d)},c=function(c,d,e){a.toggle(c.element(),d.toggleClass()),b(c,d)},d=function(c,d,e){a.add(c.element(),d.toggleClass()),b(c,d)},e=function(c,d,e){a.remove(c.element(),d.toggleClass()),b(c,d)},f=function(b,c,d){return a.has(b.element(),c.toggleClass())},g=function(a,b,c){var f=b.selected()?d:e;f(a,b,c)};return{onLoad:g,toggle:c,isOn:f,on:d,off:e}}),g("2x",["3d","27","2y","4b","y"],function(a,b,c,d,e){var f=function(a,b,c){return d.nu({})},g=function(d,f){var g=b.executeEvent(d,f,c.toggle),h=b.loadEvent(d,f,c.onLoad);return a.derive(e.flatten([d.toggleOnExecute()?[g]:[],[h]]))};return{exhibit:f,events:g}}),g("4r",["14","y","z","b","4i"],function(a,b,c,d,e){var f=function(a,b,c){e.set(a.element(),"aria-pressed",c),b.syncWithExpanded()&&i(a,b,c)},g=function(a,b,c){e.set(a.element(),"aria-selected",c)},h=function(a,b,c){e.set(a.element(),"aria-checked",c)},i=function(a,b,c){e.set(a.element(),"aria-expanded",c)},j={button:["aria-pressed"],"input:checkbox":["aria-checked"]},k={button:["aria-pressed"],listbox:["aria-pressed","aria-expanded"],menuitemcheckbox:["aria-checked"]},l=function(b){var c=b.element(),f=d.name(c),g="input"===f&&e.has(c,"type")?":"+e.get(c,"type"):"";return a.readOptFrom(j,f+g)},m=function(b){var d=b.element();if(e.has(d,"role")){var f=e.get(d,"role");return a.readOptFrom(k,f)}return c.none()},n=function(a,c,d){var f=m(a).orThunk(function(){return l(a)}).getOr([]);b.each(f,function(b){e.set(a.element(),b,d)})};return{updatePressed:f,updateSelected:g,updateChecked:h,updateExpanded:i,updateAuto:n}}),g("2z",["4r","4q","29","2e","6"],function(a,b,c,d,e){return[c.defaulted("selected",!1),c.strict("toggleClass"),c.defaulted("toggleOnExecute",!0),c.defaultedOf("aria",{mode:"none"},d.choose("mode",{pressed:[c.defaulted("syncWithExpanded",!1),b.output("update",a.updatePressed)],checked:[b.output("update",a.updateChecked)],expanded:[b.output("update",a.updateExpanded)],selected:[b.output("update",a.updateSelected)],none:[b.output("update",e.noop)]}))]}),g("1g",["q","2x","2y","2z"],function(a,b,c,d){return a.create({fields:d,name:"toggling",active:b,apis:c})}),g("4s",["6"],function(a){var b="alloy-id-",c="data-alloy-id";return{prefix:a.constant(b),idAttr:a.constant(c)}}),h("4t",Date),g("3f",["4t","1w","2m"],function(a,b,c){var d=0,e=function(e){var f=new a,g=f.getTime(),h=b.floor(1e9*b.random());return d++,e+"_"+h+d+c(g)};return{generate:e}}),g("4u",["2u","2r","4p"],function(a,b,c){var d=function(a){return b.one(a)},e=function(c,d,e){return a.ancestor(c,function(a){return b.is(a,d)},e)},f=function(c,d){return a.sibling(c,function(a){return b.is(a,d)})},g=function(c,d){return a.child(c,function(a){return b.is(a,d)})},h=function(a,c){return b.one(c,a)},i=function(a,d,f){return c(b.is,e,a,d,f)};return{first:d,ancestor:e,sibling:f,child:g,descendant:h,closest:i}}),g("30",["4s","3f","6","z","4i","b","4u"],function(a,b,c,d,e,f,g){var h=a.prefix(),i=a.idAttr(),j=function(a,c){var d=b.generate(h+a);return e.set(c,i,d),d},k=function(a,b){e.set(a,i,b)},l=function(a){var b=f.isElement(a)?e.get(a,i):null;return d.from(b)},m=function(a,b){return g.descendant(a,b)},n=function(a){return b.generate(a)},o=function(a){e.remove(a,i)};return{revoke:o,write:j,writeOnly:k,read:l,find:m,generate:n,attribute:c.constant(i)}}),g("1h",["30","14","w","z"],function(a,b,c,d){var e=function(e){var f=b.hasKey(e,"uid")?e.uid:a.generate("memento"),g=function(a){return a.getSystem().getByUid(f).getOrDie()},h=function(a){return a.getSystem().getByUid(f).fold(d.none,d.some)},i=function(){return c.deepMerge(e,{uid:f})};return{get:g,getOpt:h,asSpec:i}};return{record:e}}),h("1j",setTimeout),h("1k",window),g("1l",["1f","14","g"],function(a,b,c){var d=function(d,e){return a.config({channels:b.wrap(c.formatChanged(),{onReceive:function(a,b){b.command===d&&e(a,b.state)}})})},e=function(d){return a.config({channels:b.wrap(c.orientationChanged(),{onReceive:d})})},f=function(a,b){return{key:a,value:{onReceive:b}}};return{format:d,orientation:e,receive:f}}),g("i",["6"],function(a){var b="tinymce-mobile",c=function(a){return b+"-"+a};return{resolve:c,prefix:a.constant(b)}}),g("31",["3d","2g","4b","6"],function(a,b,c,d){var e=function(a,b){return c.nu({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})},f=function(c){return a.derive([a.abort(b.selectstart(),d.constant(!0))])};return{events:f,exhibit:e}}),g("1m",["q","31"],function(a,b){return a.create({fields:[],name:"unselecting",active:b})}),g("4w",["8"],function(a){var b=function(b,c){c.ignore()||(a.focus(b.element()),c.onFocus()(b))},c=function(b,c){c.ignore()||a.blur(b.element())},d=function(b){return a.hasFocus(b.element())};return{focus:b,blur:c,isFocused:d}}),g("4v",["3d","t","4w","4b"],function(a,b,c,d){var e=function(a,b){return b.ignore()?d.nu({}):d.nu({attributes:{tabindex:"-1"}})},f=function(d){return a.derive([a.run(b.focus(),function(a,b){c.focus(a,d),b.stop()})])};return{exhibit:e,events:f}}),g("4x",["4q","29"],function(a,b){return[a.onHandler("onFocus"),b.defaulted("ignore",!1)]}),g("32",["q","4v","4w","4x"],function(a,b,c,d){return a.create({fields:d,name:"focusing",active:b,apis:c})}),g("8b",["6"],function(a){return{BACKSPACE:a.constant([8]),TAB:a.constant([9]),ENTER:a.constant([13]),SHIFT:a.constant([16]),CTRL:a.constant([17]),ALT:a.constant([18]),CAPSLOCK:a.constant([20]),
ESCAPE:a.constant([27]),SPACE:a.constant([32]),PAGEUP:a.constant([33]),PAGEDOWN:a.constant([34]),END:a.constant([35]),HOME:a.constant([36]),LEFT:a.constant([37]),UP:a.constant([38]),RIGHT:a.constant([39]),DOWN:a.constant([40]),INSERT:a.constant([45]),DEL:a.constant([46]),META:a.constant([91,93,224]),F10:a.constant([121])}}),g("94",[],function(){var a=function(a,b,c,d){var e=a+b;return e>d?c:e<c?d:e},b=function(a,b,c){return a<=b?b:a>=c?c:a};return{cycleBy:a,cap:b}}),g("7o",["y","12","10"],function(a,b,c){var d=function(a){return h(b.body(),a)},e=function(b,d,e){return a.filter(c.parents(b,e),d)},f=function(b,d){return a.filter(c.siblings(b),d)},g=function(b,d){return a.filter(c.children(b),d)},h=function(b,d){var e=[];return a.each(c.children(b),function(a){d(a)&&(e=e.concat([a])),e=e.concat(h(a,d))}),e};return{all:d,ancestors:e,siblings:f,children:g,descendants:h}}),g("5l",["7o","2r"],function(a,b){var c=function(a){return b.all(a)},d=function(c,d,e){return a.ancestors(c,function(a){return b.is(a,d)},e)},e=function(c,d){return a.siblings(c,function(a){return b.is(a,d)})},f=function(c,d){return a.children(c,function(a){return b.is(a,d)})},g=function(a,c){return b.all(c,a)};return{all:c,ancestors:d,siblings:e,children:f,descendants:g}}),g("7m",["94","y","z","48","2f","5l","4u","v"],function(a,b,c,d,e,f,g,h){var i=function(a,c,d){var g=f.descendants(a.element(),"."+c.highlightClass());b.each(g,function(b){e.remove(b,c.highlightClass()),a.getSystem().getByDom(b).each(function(b){c.onDehighlight()(a,b)})})},j=function(a,b,c,d){var f=o(a,b,c,d);e.remove(d.element(),b.highlightClass()),f&&b.onDehighlight()(a,d)},k=function(a,b,c,d){var f=o(a,b,c,d);i(a,b,c),e.add(d.element(),b.highlightClass()),f||b.onHighlight()(a,d)},l=function(a,b,c){r(a,b,c).each(function(d){k(a,b,c,d)})},m=function(a,b,c){s(a,b,c).each(function(d){k(a,b,c,d)})},n=function(a,b,c,d){q(a,b,c,d).fold(function(a){throw new h(a)},function(d){k(a,b,c,d)})},o=function(a,b,c,d){return e.has(d.element(),b.highlightClass())},p=function(a,b,c){return g.descendant(a.element(),"."+b.highlightClass()).bind(a.getSystem().getByDom)},q=function(a,b,e,g){var h=f.descendants(a.element(),"."+b.itemClass());return c.from(h[g]).fold(function(){return d.error("No element found with index "+g)},a.getSystem().getByDom)},r=function(a,b,c){return g.descendant(a.element(),"."+b.itemClass()).bind(a.getSystem().getByDom)},s=function(a,b,d){var e=f.descendants(a.element(),"."+b.itemClass()),g=e.length>0?c.some(e[e.length-1]):c.none();return g.bind(a.getSystem().getByDom)},t=function(c,d,g,h){var i=f.descendants(c.element(),"."+d.itemClass()),j=b.findIndex(i,function(a){return e.has(a,d.highlightClass())});return j.bind(function(b){var d=a.cycleBy(b,h,0,i.length-1);return c.getSystem().getByDom(i[d])})},u=function(a,b,c){return t(a,b,c,-1)},v=function(a,b,c){return t(a,b,c,1)};return{dehighlightAll:i,dehighlight:j,highlight:k,highlightFirst:l,highlightLast:m,highlightAt:n,isHighlighted:o,getHighlighted:p,getFirst:r,getLast:s,getPrevious:u,getNext:v}}),g("7n",["4q","29"],function(a,b){return[b.strict("highlightClass"),b.strict("itemClass"),a.onHandler("onHighlight"),a.onHandler("onDehighlight")]}),g("5j",["q","7m","7n","u"],function(a,b,c,d){return a.create({fields:c,name:"highlighting",apis:b})}),g("97",["5j","6","8"],function(a,b,c){var d=function(){var a=function(a){return c.search(a.element())},b=function(a,b){a.getSystem().triggerFocus(b,a.element())};return{get:a,set:b}},e=function(){var c=function(b){return a.getHighlighted(b).map(function(a){return a.element()})},d=function(c,d){c.getSystem().getByDom(d).fold(b.noop,function(b){a.highlight(c,b)})};return{get:c,set:d}};return{dom:d,highlights:e}}),g("8t",["y","6"],function(a,b){var c=function(b){return function(c){return a.contains(b,c.raw().which)}},d=function(b){return function(c){return a.forall(b,function(a){return a(c)})}},e=function(a){return function(b){return b.raw().which===a}},f=function(a){return a.raw().shiftKey===!0};return{inSet:c,and:d,is:e,isShift:f,isNotShift:b.not(f)}}),g("8u",["8t","y"],function(a,b){var c=function(b,c){return{matches:a.is(b),classification:c}},d=function(a,b){return{matches:a,classification:b}},e=function(a,c){var d=b.find(a,function(a){return a.matches(c)});return d.map(function(a){return a.classification})};return{basic:c,rule:d,choose:e}}),g("8r",["3d","2g","t","97","4q","8u","29","w"],function(a,b,c,d,e,f,g,h){var i=function(i,j,k,l,m,n){var o=function(){return i.concat([g.defaulted("focusManager",d.dom()),e.output("handler",r),e.output("state",j)])},p=function(a,b,c,d){var e=k(a,b,c,d);return f.choose(e,b.event()).bind(function(e){return e(a,b,c,d)})},q=function(d,e){var f=l(d,e),g=a.derive(n.map(function(b){return a.run(c.focus(),function(a,c){b(a,d,e,c),c.stop()})}).toArray().concat([a.run(b.keydown(),function(a,b){p(a,b,d,e).each(function(a){b.stop()})})]));return h.deepMerge(f,g)},r={schema:o,processKey:p,toEvents:q,toApis:m};return r};return{typical:i}}),g("8s",["y","1w"],function(a,b){var c=function(b,c,d){var e=a.reverse(b.slice(0,c)),f=a.reverse(b.slice(c+1));return a.find(e.concat(f),d)},d=function(b,c,d){var e=a.reverse(b.slice(0,c));return a.find(e,d)},e=function(b,c,d){var e=b.slice(0,c),f=b.slice(c+1);return a.find(f.concat(e),d)},f=function(b,c,d){var e=b.slice(c+1);return a.find(e,d)};return{cyclePrev:c,cycleNext:e,tryPrev:d,tryNext:f}}),g("57",[],function(){var a=function(a){return void 0!==a.style};return{isSupported:a}}),g("39",["1i","y","x","z","4i","12","a","b","57","37","v","16","1k"],function(a,b,c,d,e,f,g,h,i,j,k,l,m){var n=function(b,c,d){if(!a.isString(d))throw l.error("Invalid call to CSS.set. Property ",c,":: Value ",d,":: Element ",b),new k("CSS value must be a string: "+d);i.isSupported(b)&&b.style.setProperty(c,d)},o=function(a,b){i.isSupported(a)&&a.style.removeProperty(b)},p=function(a,b,c){var d=a.dom();n(d,b,c)},q=function(a,b){var d=a.dom();c.each(b,function(a,b){n(d,b,a)})},r=function(a,b){var d=a.dom();c.each(b,function(a,b){a.fold(function(){o(d,b)},function(a){n(d,b,a)})})},s=function(a,b){var c=a.dom(),d=m.getComputedStyle(c),e=d.getPropertyValue(b),g=""!==e||f.inBody(a)?e:t(c,b);return null===g?void 0:g},t=function(a,b){return i.isSupported(a)?a.style.getPropertyValue(b):""},u=function(a,b){var c=a.dom(),e=t(c,b);return d.from(e).filter(function(a){return a.length>0})},v=function(a){var b={},c=a.dom();if(i.isSupported(c))for(var d=0;d<c.style.length;d++){var e=c.style.item(d);b[e]=c.style[e]}return b},w=function(a,b,c){var d=g.fromTag(a);p(d,b,c);var e=u(d,b);return e.isSome()},x=function(a,b){var c=a.dom();o(c,b),e.has(a,"style")&&""===j.trim(e.get(a,"style"))&&e.remove(a,"style")},y=function(a,b){var c=e.get(a,"style"),d=b(a),f=void 0===c?e.remove:e.set;return f(a,"style",c),d},z=function(a,b){var c=a.dom(),d=b.dom();i.isSupported(c)&&i.isSupported(d)&&(d.style.cssText=c.style.cssText)},A=function(a){return a.dom().offsetWidth},B=function(a,b,c){u(a,c).each(function(a){u(b,c).isNone()&&p(b,c,a)})},C=function(a,c,d){h.isElement(a)&&h.isElement(c)&&b.each(d,function(b){B(a,c,b)})};return{copy:z,set:p,preserve:y,setAll:q,setOptions:r,remove:x,get:s,getRaw:u,getAllRaw:v,isValidValue:w,reflow:A,transfer:C}}),g("78",["1i","y","39","57"],function(a,b,c,d){return function(e,f){var g=function(b,c){if(!a.isNumber(c)&&!c.match(/^[0-9]+$/))throw e+".set accepts only positive integer values. Value was "+c;var f=b.dom();d.isSupported(f)&&(f.style[e]=c+"px")},h=function(a){var b=f(a);if(b<=0||null===b){var d=c.get(a,e);return parseFloat(d)||0}return b},i=h,j=function(a,d){return b.foldl(d,function(b,d){var e=c.get(a,d),f=void 0===e?0:parseInt(e,10);return isNaN(f)?b:b+f},0)},k=function(a,b,c){var d=j(a,c),e=b>d?b-d:0;return e};return{set:g,get:h,getOuter:i,aggregate:j,max:k}}}),g("8a",["12","39","78"],function(a,b,c){var d=c("height",function(b){return a.inBody(b)?b.dom().getBoundingClientRect().height:b.dom().offsetHeight}),e=function(a,b){d.set(a,b)},f=function(a){return d.get(a)},g=function(a){return d.getOuter(a)},h=function(a,c){var e=["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"],f=d.max(a,c,e);b.set(a,"max-height",f+"px")};return{set:e,get:f,getOuter:g,setMax:h}}),g("6u",["8b","28","4q","8r","13","8s","8t","8u","29","y","6","z","1a","8","5l","4u","8a"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q){var r=[i.defaulted("selector",'[data-alloy-tabstop="true"]'),i.option("onEscape"),i.option("onEnter"),i.defaulted("firstTabstop",0),i.defaulted("useTabstopAt",k.constant(!0)),i.option("visibilitySelector")],s=function(a,b,c){var d=o.descendants(a.element(),b.selector()),e=j.filter(d,function(a){return t(b,a)}),f=l.from(e[b.firstTabstop()]);f.each(function(b){var c=a.element();a.getSystem().triggerFocus(b,c)})},t=function(a,b){var c=a.visibilitySelector().bind(function(a){return p.closest(b,a)}).getOr(b);return q.get(c)>0},u=function(a,b){return n.search(a.element()).bind(function(a){return p.closest(a,b.selector())})},v=function(a,b,c,d,e){return e(b,c,function(a){return t(d,a)&&d.useTabstopAt(a)}).fold(function(){return l.some(!0)},function(b){var c=a.getSystem(),d=a.element();return c.triggerFocus(b,d),l.some(!0)})},w=function(a,b,c,d){var e=o.descendants(a.element(),c.selector());return u(a,c).bind(function(b){var f=j.findIndex(e,k.curry(m.eq,b));return f.bind(function(b){return v(a,e,b,c,d)})})},x=function(a,b,c,d){return w(a,b,c,f.cyclePrev)},y=function(a,b,c,d){return w(a,b,c,f.cycleNext)},z=function(a,b,c,d){return c.onEnter().bind(function(c){return c(a,b)})},A=function(a,b,c,d){return c.onEscape().bind(function(c){return c(a,b)})},B=k.constant([h.rule(g.and([g.isShift,g.inSet(a.TAB())]),x),h.rule(g.inSet(a.TAB()),y),h.rule(g.inSet(a.ESCAPE()),A),h.rule(g.and([g.isNotShift,g.inSet(a.ENTER())]),z)]),C=k.constant({}),D=k.constant({});return d.typical(r,b.init,B,C,D,l.some(s))}),g("8v",["4i","b"],function(a,b){var c=function(c){return"input"===b.name(c)&&"radio"!==a.get(c,"type")||"textarea"===b.name(c)};return{inside:c}}),g("8w",["8v","8b","2","t","8t","z"],function(a,b,c,d,e,f){var g=function(a,b,e){return c.dispatch(a,e,d.execute()),f.some(!0)},h=function(c,d,h){return a.inside(h)&&e.inSet(b.SPACE())(d.event())?f.none():g(c,d,h)};return{defaultExecute:h}}),g("6v",["8v","8b","28","8r","8w","13","8t","8u","29","6","z"],function(a,b,c,d,e,f,g,h,i,j,k){var l=[i.defaulted("execute",e.defaultExecute),i.defaulted("useSpace",!1),i.defaulted("useEnter",!0),i.defaulted("useDown",!1)],m=function(a,b,c,d){return c.execute()(a,b,a.element())},n=function(c,d,e,f){var i=e.useSpace()&&!a.inside(c.element())?b.SPACE():[],j=e.useEnter()?b.ENTER():[],k=e.useDown()?b.DOWN():[],l=i.concat(j).concat(k);return[h.rule(g.inSet(l),m)]},o=j.constant({}),p=j.constant({});return d.typical(l,c.init,n,o,p,k.none())}),g("4z",["4g","5","6","z"],function(a,b,c,d){var e=function(e){var f=b(d.none()),g=function(a,b){f.set(d.some({numRows:c.constant(a),numColumns:c.constant(b)}))},h=function(){return f.get().map(function(a){return a.numRows()})},i=function(){return f.get().map(function(a){return a.numColumns()})};return a({readState:c.constant({}),setGridSize:g,getNumRows:h,getNumColumns:i})},f=function(a){return a.state()(a)};return{flatgrid:e,init:f}}),g("9y",["39"],function(a){var b=function(a,b){return function(d){return"rtl"===c(d)?b:a}},c=function(b){return"rtl"===a.get(b,"direction")?"rtl":"ltr"};return{onDirection:b,getDirection:c}}),g("8x",["9y"],function(a){var b=function(a){return function(b,c,d,e){var g=a(b.element());return f(g,b,c,d,e)}},c=function(c,d){var e=a.onDirection(c,d);return b(e)},d=function(c,d){var e=a.onDirection(d,c);return b(e)},e=function(a){return function(b,c,d,e){return f(a,b,c,d,e)}},f=function(a,b,c,d,e){var f=d.focusManager().get(b).bind(function(c){return a(b.element(),c,d,e)});return f.map(function(a){return d.focusManager().set(b,a),!0})};return{east:d,west:c,north:e,south:e,move:e}}),g("9z",["y","2o"],function(a,b){var c=b.immutableBag(["index","candidates"],[]),d=function(b,d){return a.findIndex(b,d).map(function(a){return c({index:a,candidates:b})})};return{locate:d}}),g("a0",["6","4h","39"],function(a,b,c){var d=function(d,e,f,g){var h=c.get(d,e);void 0===h&&(h="");var i=h===f?g:f,j=a.curry(c.set,d,e,h),k=a.curry(c.set,d,e,i);return b(j,k,!1)},e=function(a){return d(a,"visibility","hidden","visible")},f=function(a,b){return d(a,"display","none",b)},g=function(a){return a.offsetWidth<=0&&a.offsetHeight<=0},h=function(a){var b=a.dom();return!g(b)};return{toggler:e,displayToggler:f,isVisible:h}}),g("8y",["9z","y","6","1a","5l","a0"],function(a,b,c,d,e,f){var g=function(a,b,c){var d=f.isVisible;return h(a,b,c,d)},h=function(g,h,i,j){var k=c.curry(d.eq,h),l=e.descendants(g,i),m=b.filter(l,f.isVisible);return a.locate(m,k)},i=function(a,c){return b.findIndex(a,function(a){return d.eq(c,a)})};return{locateVisible:g,locateIn:h,findIndex:i}}),g("8z",["94","6","z","1w"],function(a,b,c,d){var e=function(a,b,e,f){var g=d.floor(b/e),h=b%e;return f(g,h).bind(function(b){var d=b.row()*e+b.column();return d>=0&&d<a.length?c.some(a[d]):c.none()})},f=function(d,f,g,h,i){return e(d,f,h,function(e,f){var j=e===g-1,k=j?d.length-e*h:h,l=a.cycleBy(f,i,0,k-1);return c.some({row:b.constant(e),column:b.constant(l)})})},g=function(d,f,g,h,i){return e(d,f,h,function(e,f){var j=a.cycleBy(e,i,0,g-1),k=j===g-1,l=k?d.length-j*h:h,m=a.cap(f,0,l-1);return c.some({row:b.constant(j),column:b.constant(m)})})},h=function(a,b,c,d){return f(a,b,c,d,1)},i=function(a,b,c,d){return f(a,b,c,d,-1)},j=function(a,b,c,d){return g(a,b,c,d,-1)},k=function(a,b,c,d){return g(a,b,c,d,1)};return{cycleDown:k,cycleUp:j,cycleLeft:i,cycleRight:h}}),g("6w",["8b","4z","4q","8r","8w","8x","8y","8t","8u","8z","29","5","6","z","8","4u"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p){var q=[k.strict("selector"),k.defaulted("execute",e.defaultExecute),c.onKeyboardHandler("onEscape"),k.defaulted("captureTab",!1),c.initSize()],r=function(a,b,c){p.descendant(a.element(),b.selector()).each(function(b){a.getSystem().triggerFocus(b,a.element())})},s=function(a,b,c,d){return o.search(a.element(),c.selector()).bind(function(d){return c.execute()(a,b,d)})},t=function(a){return function(b,c,d,e){return g.locateVisible(b,c,d.selector()).bind(function(b){return a(b.candidates(),b.index(),e.getNumRows().getOr(d.initSize().numRows()),e.getNumColumns().getOr(d.initSize().numColumns()))})}},u=function(a,b,c,d){return c.captureTab()?n.some(!0):n.none()},v=function(a,b,c,d){return c.onEscape()(a,b)},w=t(j.cycleLeft),x=t(j.cycleRight),y=t(j.cycleUp),z=t(j.cycleDown),A=m.constant([i.rule(h.inSet(a.LEFT()),f.west(w,x)),i.rule(h.inSet(a.RIGHT()),f.east(w,x)),i.rule(h.inSet(a.UP()),f.north(y)),i.rule(h.inSet(a.DOWN()),f.south(z)),i.rule(h.and([h.isShift,h.inSet(a.TAB())]),u),i.rule(h.and([h.isNotShift,h.inSet(a.TAB())]),u),i.rule(h.inSet(a.ESCAPE()),v),i.rule(h.inSet(a.SPACE().concat(a.ENTER())),s)]),B=m.constant({}),C={};return d.typical(q,b.flatgrid,A,B,C,n.some(r))}),g("90",["94","8y","6","z"],function(a,b,c,d){var e=function(e,f,g,h){return b.locateVisible(e,g,f,c.constant(!0)).bind(function(b){var c=b.index(),e=b.candidates(),f=a.cycleBy(c,h,0,e.length-1);return d.from(e[f])})};return{horizontal:e}}),g("6x",["8v","8b","28","8r","8w","8x","90","8t","8u","29","6","z","8","4u"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n){var o=[j.strict("selector"),j.defaulted("getInitial",l.none),j.defaulted("execute",e.defaultExecute),j.defaulted("executeOnMove",!1)],p=function(a,b,c){return m.search(a.element()).bind(function(d){return c.execute()(a,b,d)})},q=function(a,b){b.getInitial()(a).or(n.descendant(a.element(),b.selector())).each(function(b){a.getSystem().triggerFocus(b,a.element())})},r=function(a,b,c){return g.horizontal(a,c.selector(),b,-1)},s=function(a,b,c){return g.horizontal(a,c.selector(),b,1)},t=function(a){return function(b,c,d){return a(b,c,d).bind(function(){return d.executeOnMove()?p(b,c,d):l.some(!0)})}},u=function(a){return[i.rule(h.inSet(b.LEFT().concat(b.UP())),t(f.west(r,s))),i.rule(h.inSet(b.RIGHT().concat(b.DOWN())),t(f.east(r,s))),i.rule(h.inSet(b.ENTER()),p),i.rule(h.inSet(b.SPACE()),p)]},v=k.constant({}),w=k.constant({});return d.typical(o,c.init,u,v,w,l.some(q))}),g("91",["94","z","2o","1w"],function(a,b,c,d){var e=c.immutableBag(["rowIndex","columnIndex","cell"],[]),f=function(a,c,d){return b.from(a[c]).bind(function(a){return b.from(a[d]).map(function(a){return e({rowIndex:c,columnIndex:d,cell:a})})})},g=function(b,c,d,e){var g=b[c],h=g.length,i=a.cycleBy(d,e,0,h-1);return f(b,c,i)},h=function(b,c,d,e){var g=a.cycleBy(d,e,0,b.length-1),h=b[g].length,i=a.cap(c,0,h-1);return f(b,g,i)},i=function(b,c,d,e){var g=b[c],h=g.length,i=a.cap(d+e,0,h-1);return f(b,c,i)},j=function(b,c,d,e){var g=a.cap(d+e,0,b.length-1),h=b[g].length,i=a.cap(c,0,h-1);return f(b,g,i)},k=function(a,b,c){return g(a,b,c,1)},l=function(a,b,c){return g(a,b,c,-1)},m=function(a,b,c){return h(a,c,b,-1)},n=function(a,b,c){return h(a,c,b,1)},o=function(a,b,c){return i(a,b,c,-1)},p=function(a,b,c){return i(a,b,c,1)},q=function(a,b,c){return j(a,c,b,-1)},r=function(a,b,c){return j(a,c,b,1)};return{cycleRight:k,cycleLeft:l,cycleUp:m,cycleDown:n,moveLeft:o,moveRight:p,moveUp:q,moveDown:r}}),g("6y",["8b","28","8r","8w","8x","8y","8t","8u","91","29","y","6","z","8","5l","4u"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p){var q=[j.strictObjOf("selectors",[j.strict("row"),j.strict("cell")]),j.defaulted("cycles",!0),j.defaulted("previousSelector",m.none),j.defaulted("execute",d.defaultExecute)],r=function(a,b){var c=b.previousSelector()(a).orThunk(function(){var c=b.selectors();return p.descendant(a.element(),c.cell())});c.each(function(b){a.getSystem().triggerFocus(b,a.element())})},s=function(a,b,c){return n.search(a.element()).bind(function(d){return c.execute()(a,b,d)})},t=function(a,b){return k.map(a,function(a){return o.descendants(a,b.selectors().cell())})},u=function(a,b){return function(c,d,e){var g=e.cycles()?a:b;return p.closest(d,e.selectors().row()).bind(function(a){var b=o.descendants(a,e.selectors().cell());return f.findIndex(b,d).bind(function(b){var d=o.descendants(c,e.selectors().row());return f.findIndex(d,a).bind(function(a){var c=t(d,e);return g(c,a,b).map(function(a){return a.cell()})})})})}},v=u(i.cycleLeft,i.moveLeft),w=u(i.cycleRight,i.moveRight),x=u(i.cycleUp,i.moveUp),y=u(i.cycleDown,i.moveDown),z=l.constant([h.rule(g.inSet(a.LEFT()),e.west(v,w)),h.rule(g.inSet(a.RIGHT()),e.east(v,w)),h.rule(g.inSet(a.UP()),e.north(x)),h.rule(g.inSet(a.DOWN()),e.south(y)),h.rule(g.inSet(a.SPACE().concat(a.ENTER())),s)]),A=l.constant({}),B=l.constant({});return c.typical(q,b.init,z,A,B,m.some(r))}),g("6z",["8b","28","8r","8w","8x","90","8t","8u","29","6","z","8","4u"],function(a,b,c,d,e,f,g,h,i,j,k,l,m){var n=[i.strict("selector"),i.defaulted("execute",d.defaultExecute),i.defaulted("moveOnTab",!1)],o=function(a,b,c){return c.focusManager().get(a).bind(function(d){return c.execute()(a,b,d)})},p=function(a,b,c){m.descendant(a.element(),b.selector()).each(function(c){b.focusManager().set(a,c)})},q=function(a,b,c){return f.horizontal(a,c.selector(),b,-1)},r=function(a,b,c){return f.horizontal(a,c.selector(),b,1)},s=function(a,b,c){return c.moveOnTab()?e.move(q)(a,b,c):k.none()},t=function(a,b,c){return c.moveOnTab()?e.move(r)(a,b,c):k.none()},u=j.constant([h.rule(g.inSet(a.UP()),e.move(q)),h.rule(g.inSet(a.DOWN()),e.move(r)),h.rule(g.and([g.isShift,g.inSet(a.TAB())]),s),h.rule(g.and([g.isNotShift,g.inSet(a.TAB())]),t),h.rule(g.inSet(a.ENTER()),o),h.rule(g.inSet(a.SPACE()),o)]),v=j.constant({}),w=j.constant({});return c.typical(n,b.init,u,v,w,k.some(p))}),g("70",["8b","28","4q","8r","8t","8u","29","6","z"],function(a,b,c,d,e,f,g,h,i){var j=[c.onKeyboardHandler("onSpace"),c.onKeyboardHandler("onEnter"),c.onKeyboardHandler("onShiftEnter"),c.onKeyboardHandler("onLeft"),c.onKeyboardHandler("onRight"),c.onKeyboardHandler("onTab"),c.onKeyboardHandler("onShiftTab"),c.onKeyboardHandler("onUp"),c.onKeyboardHandler("onDown"),c.onKeyboardHandler("onEscape"),g.option("focusIn")],k=function(b,c,d){return[f.rule(e.inSet(a.SPACE()),d.onSpace()),f.rule(e.and([e.isNotShift,e.inSet(a.ENTER())]),d.onEnter()),f.rule(e.and([e.isShift,e.inSet(a.ENTER())]),d.onShiftEnter()),f.rule(e.and([e.isShift,e.inSet(a.TAB())]),d.onShiftTab()),f.rule(e.and([e.isNotShift,e.inSet(a.TAB())]),d.onTab()),f.rule(e.inSet(a.UP()),d.onUp()),f.rule(e.inSet(a.DOWN()),d.onDown()),f.rule(e.inSet(a.LEFT()),d.onLeft()),f.rule(e.inSet(a.RIGHT()),d.onRight()),f.rule(e.inSet(a.SPACE()),d.onSpace()),f.rule(e.inSet(a.ESCAPE()),d.onEscape())]},l=function(a,b){return b.focusIn().bind(function(c){return c(a,b)})},m=h.constant({}),n=h.constant({});return d.typical(j,b.init,k,m,n,i.some(l))}),g("4y",["6u","6v","6w","6x","6y","6z","70"],function(a,b,c,d,e,f,g){return{cyclic:a.schema(),flow:d.schema(),flatgrid:c.schema(),matrix:e.schema(),execution:b.schema(),menu:f.schema(),special:g.schema()}}),g("33",["q","4y","4z","14","16"],function(a,b,c,d,e){return a.createModes({branchKey:"mode",branches:b,name:"keying",active:{events:function(a,b){var c=a.handler();return c.toEvents(a,b)}},apis:{focusIn:function(a){a.getSystem().triggerFocus(a.element(),a.element())},setGridSize:function(a,b,c,f,g){d.hasKey(c,"setGridSize")?c.setGridSize(f,g):e.error("Layout does not support setGridSize")}},state:c})}),g("50",["4a","14","y","6","3f","u"],function(a,b,c,d,e,f){var g=e.generate("alloy-premade"),h=e.generate("api"),i=function(a){return b.wrap(g,a)},j=function(a){return b.readOptFrom(a,g)},k=function(b){return a.markAsSketchApi(function(a){var c=f.prototype.slice.call(arguments,0),d=a.config(h);return b.apply(void 0,[d].concat(c))},b)};return{apiConfig:d.constant(h),makeApi:k,premade:i,getPremade:j}}),g("53",["4c","29","2e","6k","6","3f","z"],function(a,b,c,d,e,f,g){var h=d.generate([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),i=b.defaulted("factory",{sketch:e.identity}),j=b.defaulted("schema",[]),k=b.strict("name"),l=b.field("pname","pname",a.defaultedThunk(function(a){return"<alloy."+f.generate(a.name)+">"}),c.anyValue()),m=b.defaulted("defaults",e.constant({})),n=b.defaulted("overrides",e.constant({})),o=c.objOf([i,j,k,l,m,n]),p=c.objOf([i,j,k,m,n]),q=c.objOf([i,j,k,l,m,n]),r=c.objOf([i,j,k,b.strict("unit"),l,m,n]),s=function(a){return a.fold(g.some,g.none,g.some,g.some)},t=function(a){var b=function(a){return a.name()};return a.fold(b,b,b,b)},u=function(a){return a.fold(e.identity,e.identity,e.identity,e.identity)},v=function(a,b){return function(d){var e=c.asStructOrDie("Converting part type",b,d);return a(e)}};return{required:v(h.required,o),external:v(h.external,p),optional:v(h.optional,q),group:v(h.group,r),asNamedPart:s,name:t,asCommon:u,original:e.constant("entirety")}}),g("72",["14","y","x","w","6n","6","6k","v"],function(a,b,c,d,e,f,g,h){var i="placeholder",j=g.generate([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),k=function(a){return b.contains([i],a)},l=function(b,d,g,i){return b.exists(function(a){return a!==g.owner})?j.single(!0,f.constant(g)):a.readOptFrom(i,g.name).fold(function(){throw new h("Unknown placeholder component: "+g.name+"\nKnown: ["+c.keys(i)+"]\nNamespace: "+b.getOr("none")+"\nSpec: "+e.stringify(g,null,2))},function(a){return a.replace()})},m=function(a,b,c,d){return c.uiType===i?l(a,b,c,d):j.single(!1,f.constant(c))},n=function(c,e,f,g){var h=m(c,e,f,g);return h.fold(function(h,i){var j=i(e,f.config,f.validated),k=a.readOptFrom(j,"components").getOr([]),l=b.bind(k,function(a){return n(c,e,a,g)});return[d.deepMerge(j,{components:l})]},function(a,b){var c=b(e,f.config,f.validated);return c})},o=function(a,c,d,e){return b.bind(d,function(b){return n(a,c,b,e)})},p=function(a,b){var c=!1,d=function(){return c},e=function(){if(c===!0)throw new h("Trying to use the same placeholder more than once: "+a);return c=!0,b},g=function(){return b.fold(function(a,b){return a},function(a,b){return a})};return{name:f.constant(a),required:g,used:d,replace:e}},q=function(a,b,d,f){var g=c.map(f,function(a,b){return p(b,a)}),i=o(a,b,d,g);return c.each(g,function(c){if(c.used()===!1&&c.required())throw new h("Placeholder: "+c.name()+" was not found in components list\nNamespace: "+a.getOr("none")+"\nComponents: "+e.stringify(b.components(),null,2))}),i},r=function(a,b){var c=b;return c.fold(function(b,c){return[c(a)]},function(b,c){return c(a)})};return{single:j.single,multiple:j.multiple,isSubstitute:k,placeholder:f.constant(i),substituteAll:o,substitutePlaces:q,singleReplace:r}}),g("71",["53","72","14","y","6","w"],function(a,b,c,d,e,f){var g=function(a,b,d,e){var g=d;return f.deepMerge(b.defaults()(a,d,e),d,{uid:a.partUids()[b.name()]},b.overrides()(a,d,e),{"debug.sketcher":c.wrap("part-"+b.name(),g)})},h=function(c,h,i){var j={},k={};return d.each(i,function(c){c.fold(function(a){j[a.pname()]=b.single(!0,function(b,c,d){return a.factory().sketch(g(b,a,c,d))})},function(b){var c=h.parts()[b.name()]();k[b.name()]=e.constant(g(h,b,c[a.original()]()))},function(a){j[a.pname()]=b.single(!1,function(b,c,d){return a.factory().sketch(g(b,a,c,d))})},function(a){j[a.pname()]=b.multiple(!0,function(b,c,e){var g=b[a.name()]();return d.map(g,function(c){return a.factory().sketch(f.deepMerge(a.defaults()(b,c),c,a.overrides()(b,c)))})})})}),{internals:e.constant(j),externals:e.constant(k)}};return{subs:h}}),g("52",["4q","71","53","72","4c","29","14","2e","y","6","w","x","z"],function(a,b,c,d,e,f,g,h,i,j,k,l,m){var n=function(a,b){var d={};return i.each(b,function(b){c.asNamedPart(b).each(function(b){var c=o(a,b.pname());d[b.name()]=function(d){var e=h.asRawOrDie("Part: "+b.name()+" in "+a,h.objOf(b.schema()),d);return k.deepMerge(c,{config:d,validated:e})}})}),d},o=function(a,b){return{uiType:d.placeholder(),owner:a,name:b}},p=function(a,b,c){return{uiType:d.placeholder(),owner:a,name:b,config:c,validated:{}}},q=function(b){return i.bind(b,function(b){return b.fold(m.none,m.some,m.none,m.none).map(function(b){return f.strictObjOf(b.name(),b.schema().concat([a.snapshot(c.original())]))}).toArray()})},r=function(a){return i.map(a,c.name)},s=function(a,c,d){return b.subs(a,c,d)},t=function(a,b,c){return d.substitutePlaces(m.some(a),b,b.components(),c)},u=function(a,b,c){var d=b.partUids()[c];return a.getSystem().getByUid(d).toOption()},v=function(a,b,c){return u(a,b,c).getOrDie("Could not find part: "+c)},w=function(a,b,c){var d={},e=b.partUids(),f=a.getSystem();return i.each(c,function(a){d[a]=f.getByUid(e[a])}),l.map(d,j.constant)},x=function(a,b){var c=a.getSystem();return l.map(b.partUids(),function(a,b){return j.constant(c.getByUid(a))})},y=function(a,b,c){var d={},e=b.partUids(),f=a.getSystem();return i.each(c,function(a){d[a]=f.getByUid(e[a]).getOrDie()}),l.map(d,j.constant)},z=function(a,b){var c=r(b);return g.wrapAll(i.map(c,function(b){return{key:b,value:a+"-"+b}}))},A=function(a){return f.field("partUids","partUids",e.mergeWithThunk(function(b){return z(b.uid,a)}),h.anyValue())};return{generate:n,generateOne:p,schemas:q,names:r,substitutes:s,components:t,defaultUids:z,defaultUidsSchema:A,getAllParts:x,getPart:u,getPartOrDie:v,getParts:w,getPartsOrDie:y}}),g("73",["4q","72","29","14","2e","y","6","w","x","6n","v"],function(a,b,c,d,e,f,g,h,i,j,k){var l=function(e,g,h){var l=void 0!==h?h:"Unknown owner",m=function(){return[a.output("partUids",{})]},n=void 0!==g?g:m();if(0===e.length&&0===n.length)return m();var o=c.strictObjOf("parts",f.flatten([f.map(e,c.strict),f.map(n,function(a){return c.defaulted(a,b.single(!1,function(){throw new k("The optional part: "+a+" was not specified in the config, but it was used in components")}))})])),p=c.state("partUids",function(a){if(!d.hasKey(a,"parts"))throw new k("Part uid definition for owner: "+l+' requires "parts"\nExpected parts: '+e.join(", ")+"\nSpec: "+j.stringify(a,null,2));var b=i.map(a.parts,function(b,c){return d.readOptFrom(b,"uid").getOrThunk(function(){return a.uid+"-"+c})});return b});return[o,p]},m=function(b,d,e,f){var g=d.length>0?[c.strictObjOf("parts",d)]:[];return g.concat([c.strict("uid"),c.defaulted("dom",{}),c.defaulted("components",[]),a.snapshot("originalSpec"),c.defaulted("debug.sketcher",{})]).concat(e)},n=function(a,b,c,d){var f=m(a,d,c);return e.asRawOrDie(a+" [SpecSchema]",e.objOfOnly(f.concat(b)),c)},o=function(a,b,c,d,f){var g=m(a,d,f,c);return e.asStructOrDie(a+" [SpecSchema]",e.objOfOnly(g.concat(b)),c)},p=function(a,b,c){var d=h.deepMerge(b,c);return a(d)},q=function(a,b){return h.deepMerge(a,b)};return{asRawOrDie:n,asStructOrDie:o,addBehaviours:q,getPartsSchema:l,extend:p}}),g("51",["52","30","73","14","w"],function(a,b,c,d,e){var f=function(a,b,f,g){var i=h(g),j=c.asStructOrDie(a,b,i,[],[]);return e.deepMerge(f(j,i),{"debug.sketcher":d.wrap(a,g)})},g=function(b,f,g,i,j){var k=h(j),l=a.schemas(g),m=a.defaultUidsSchema(g),n=c.asStructOrDie(b,f,k,l,[m]),o=a.substitutes(b,n,g),p=a.components(b,n,o.internals());return e.deepMerge(i(n,p,k,o.externals()),{"debug.sketcher":d.wrap(b,j)})},h=function(a){return e.deepMerge({uid:b.generate("uid")},a)};return{supplyUid:h,single:f,composite:g}}),g("34",["50","51","4a","52","53","29","2e","6","w","x"],function(a,b,c,d,e,f,g,h,i,j){var k=g.objOfOnly([f.strict("name"),f.strict("factory"),f.strict("configFields"),f.defaulted("apis",{}),f.defaulted("extraApis",{})]),l=g.objOfOnly([f.strict("name"),f.strict("factory"),f.strict("configFields"),f.strict("partFields"),f.defaulted("apis",{}),f.defaulted("extraApis",{})]),m=function(d){var e=g.asRawOrDie("Sketcher for "+d.name,k,d),f=function(a){return b.single(e.name,e.configFields,e.factory,a)},l=j.map(e.apis,a.makeApi),m=j.map(e.extraApis,function(a,b){return c.markAsExtraApi(a,b)});return i.deepMerge({name:h.constant(e.name),partFields:h.constant([]),configFields:h.constant(e.configFields),sketch:f},l,m)},n=function(e){var f=g.asRawOrDie("Sketcher for "+e.name,l,e),k=function(a){return b.composite(f.name,f.configFields,f.partFields,f.factory,a)},m=d.generate(f.name,f.partFields),n=j.map(f.apis,a.makeApi),o=j.map(f.extraApis,function(a,b){return c.markAsExtraApi(a,b)});return i.deepMerge({name:h.constant(f.name),partFields:h.constant(f.partFields),configFields:h.constant(f.configFields),sketch:k,parts:h.constant(m)},n,o)};return{single:m,composite:n}}),g("35",["3d","2","2g","t","y","7"],function(a,b,c,d,e,f){var g=function(g){var h=function(b){return a.run(d.execute(),function(a,c){b(a),c.stop()})},i=function(a,c){c.stop(),b.emitExecute(a)},j=function(a,b){b.cut()},k=f.detect().deviceType.isTouch()?[a.run(d.tap(),i)]:[a.run(c.click(),i),a.run(c.mousedown(),j)];return a.derive(e.flatten([g.map(h).toArray(),k]))};return{events:g}}),g("1n",["q","32","33","34","35","29","w"],function(a,b,c,d,e,f,g){var h=function(d,f){var h=e.events(d.action());return{uid:d.uid(),dom:d.dom(),components:d.components(),events:h,behaviours:g.deepMerge(a.derive([b.config({}),c.config({mode:"execution",useSpace:!0,useEnter:!0})]),d.buttonBehaviours()),domModification:{attributes:{type:"button",role:d.role().getOr("button")}},eventOrder:d.eventOrder()}};return d.single({name:"Button",factory:h,configFields:[f.defaulted("uid",void 0),f.strict("dom"),f.defaulted("components",[]),f.defaulted("buttonBehaviours",{}),f.option("action"),f.option("role"),f.defaulted("eventOrder",{})]})}),g("36",["14","y","w","a","b","4n","10","u"],function(a,b,c,d,e,f,g,h){var i=function(d){var e=void 0!==d.dom().attributes?d.dom().attributes:[];return b.foldl(e,function(b,d){return"class"===d.name?b:c.deepMerge(b,a.wrap(d.name,d.value))},{})},j=function(a){return h.prototype.slice.call(a.dom().classList,0)},k=function(a){var b=d.fromHtml(a),h=g.children(b),k=i(b),l=j(b),m=0===h.length?{}:{innerHtml:f.get(b)};return c.deepMerge({tag:e.name(b),classes:l,attributes:k},m)},l=function(a,b,d){return a.sketch(c.deepMerge({dom:k(b)},d))};return{fromHtml:k,sketch:l}}),g("1o",["36","37","i"],function(a,b,c){var d=function(d){var e=b.supplant(d,{prefix:c.prefix()});return a.fromHtml(e)},e=function(a){
var b=d(a);return{dom:b}};return{dom:d,spec:e}}),g("l",["q","1g","1m","1n","w","1l","i","1o"],function(a,b,c,d,e,f,g,h){var i=function(a,b){return m(b,function(){a.execCommand(b)},{})},j=function(c){return a.derive([b.config({toggleClass:g.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),f.format(c,function(a,c){var d=c?b.on:b.off;d(a)})])},k=function(a,b){var c=j(b);return m(b,function(){a.execCommand(b)},c)},l=function(a,b,c,d){var e=j(c);return m(b,d,e)},m=function(b,f,g){return d.sketch({dom:h.dom('<span class="${prefix}-toolbar-button ${prefix}-icon-'+b+' ${prefix}-icon"></span>'),action:f,buttonBehaviours:e.deepMerge(a.derive([c.config({})]),g)})};return{forToolbar:m,forToolbarCommand:i,forToolbarStateAction:l,forToolbarStateCommand:k}}),g("92",["1w"],function(a){var b=function(b,c,d,e){return b<c?b:b>d?d:b===c?c-1:a.max(c,b-e)},c=function(b,c,d,e){return b>d?b:b<c?c:b===d?d+1:a.min(d,b+e)},d=function(b,c,d){return a.max(c,a.min(d,b))},e=function(b,c,e,f,g,h){return h.fold(function(){var b=c-e,h=a.round(b/g)*g;return d(e+h,e-1,f+1)},function(b){var d=(c-b)%g,e=a.round(d/g),h=a.floor((c-b)/g),i=a.floor((f-b)/g),j=a.min(i,h+e),k=b+j*g;return a.max(b,k)})},f=function(b,c,f,g,h,i,j){var k=f-c;if(g<b.left)return c-1;if(g>b.right)return f+1;var l=a.min(b.right,a.max(g,b.left))-b.left,m=d(l/b.width*k+c,c-1,f+1),n=a.round(m);return i&&m>=c&&m<=f?e(b,m,c,f,h,j):n};return{reduceBy:b,increaseBy:c,findValueOfX:f}}),g("74",["2","92","6","z","7","1w"],function(a,b,c,d,e,f){var g="slider.change.value",h=e.detect().deviceType.isTouch(),i=function(a){var b=a.event().raw();return h&&void 0!==b.touches&&1===b.touches.length?d.some(b.touches[0]):h&&void 0!==b.touches?d.none():h||void 0===b.clientX?d.none():d.some(b)},j=function(a){var b=i(a);return b.map(function(a){return a.clientX})},k=function(b,c){a.emitWith(b,g,{value:c})},l=function(a,b){k(a,b.min(),d.none())},m=function(a,b){k(a,b.max(),d.none())},n=function(a,b){k(a,b.max()+1,d.none())},o=function(a,b){k(a,b.min()-1,d.none())},p=function(a,c,d,e){var f=b.findValueOfX(c,d.min(),d.max(),e,d.stepSize(),d.snapToGrid(),d.snapStart());k(a,f)},q=function(a,b,c,d){return j(d).map(function(d){return p(a,c,b,d),d})},r=function(a,c){var e=b.reduceBy(c.value().get(),c.min(),c.max(),c.stepSize());k(a,e,d.none())},s=function(a,c){var e=b.increaseBy(c.value().get(),c.min(),c.max(),c.stepSize());k(a,e,d.none())};return{setXFromEvent:q,setToLedge:o,setToRedge:n,moveLeftFromRedge:m,moveRightFromLedge:l,moveLeft:r,moveRight:s,changeEvent:c.constant(g)}}),g("54",["q","32","33","3d","2g","53","74","29","5","6","z","7"],function(a,b,c,d,e,f,g,h,i,j,k,l){var m=l.detect(),n=m.deviceType.isTouch(),o=function(a,b){return f.optional({name:""+a+"-edge",overrides:function(a){var c=d.derive([d.runActionExtra(e.touchstart(),b,[a])]),f=d.derive([d.runActionExtra(e.mousedown(),b,[a]),d.runActionExtra(e.mousemove(),function(a,c){c.mouseIsDown().get()&&b(a,c)},[a])]);return{events:n?c:f}}})},p=o("left",g.setToLedge),q=o("right",g.setToRedge),r=f.required({name:"thumb",defaults:j.constant({dom:{styles:{position:"absolute"}}}),overrides:function(a){return{events:d.derive([d.redirectToPart(e.touchstart(),a,"spectrum"),d.redirectToPart(e.touchmove(),a,"spectrum"),d.redirectToPart(e.touchend(),a,"spectrum")])}}}),s=f.required({schema:[h.state("mouseIsDown",function(){return i(!1)})],name:"spectrum",overrides:function(f){var h=function(a,b){var c=a.element().dom().getBoundingClientRect();g.setXFromEvent(a,f,c,b)},i=d.derive([d.run(e.touchstart(),h),d.run(e.touchmove(),h)]),j=d.derive([d.run(e.mousedown(),h),d.run(e.mousemove(),function(a,b){f.mouseIsDown().get()&&h(a,b)})]);return{behaviours:a.derive(n?[]:[c.config({mode:"special",onLeft:function(a){return g.moveLeft(a,f),k.some(!0)},onRight:function(a){return g.moveRight(a,f),k.some(!0)}}),b.config({})]),events:n?i:j}}});return[p,q,r,s]}),g("55",["29","5","6","7"],function(a,b,c,d){var e=d.detect().deviceType.isTouch();return[a.strict("min"),a.strict("max"),a.defaulted("stepSize",1),a.defaulted("onChange",c.noop),a.defaulted("onInit",c.noop),a.defaulted("onDragStart",c.noop),a.defaulted("onDragEnd",c.noop),a.defaulted("snapToGrid",!1),a.option("snapStart"),a.strict("getInitialValue"),a.defaulted("sliderBehaviours",{}),a.state("value",function(a){return b(a.min)})].concat(e?[]:[a.state("mouseIsDown",function(){return b(!1)})])}),g("5b",[],function(){var a=function(a,b,c){b.store().manager().onLoad(a,b,c)},b=function(a,b,c){b.store().manager().onUnload(a,b,c)},c=function(a,b,c,d){b.store().manager().setValue(a,b,c,d)},d=function(a,b,c){return b.store().manager().getValue(a,b,c)};return{onLoad:a,onUnload:b,setValue:c,getValue:d}}),g("5a",["3d","27","5b"],function(a,b,c){var d=function(d,e){var f=d.resetOnDom()?[a.runOnAttached(function(a,b){c.onLoad(a,d,e)}),a.runOnDetached(function(a,b){c.onUnload(a,d,e)})]:[b.loadEvent(d,e,c.onLoad)];return a.derive(f)};return{events:d}}),g("5d",["4g","5"],function(a,b){var c=function(){var c=b(null),d=function(){return{mode:"memory",value:c.get()}},e=function(){return null===c.get()},f=function(){c.set(null)};return a({set:c.set,get:c.get,isNotSet:e,clear:f,readState:d})},d=function(){var b=function(){};return a({readState:b})},e=function(){var c=b({}),d=function(){return{mode:"dataset",dataset:c.get()}};return a({readState:d,set:c.set,get:c.get})},f=function(a){return a.store().manager().state(a)};return{memory:c,dataset:e,manual:d,init:f}}),g("75",["5d","4q","29","14","6"],function(a,b,c,d,e){var f=function(a,b,c,d){b.store().getDataKey();c.set({}),b.store().setData()(a,d),b.onSetValue()(a,d)},g=function(a,b,c){var e=b.store().getDataKey()(a),f=c.get();return d.readOptFrom(f,e).fold(function(){return b.store().getFallbackEntry()(e)},function(a){return a})},h=function(a,b,c){b.store().initialValue().each(function(d){f(a,b,c,d)})},i=function(a,b,c){c.set({})};return[c.option("initialValue"),c.strict("getFallbackEntry"),c.strict("getDataKey"),c.strict("setData"),b.output("manager",{setValue:f,getValue:g,onLoad:h,onUnload:i,state:a.dataset})]}),g("76",["28","4q","29","6"],function(a,b,c,d){var e=function(a,b,c){return b.store().getValue()(a)},f=function(a,b,c,d){b.store().setValue()(a,d),b.onSetValue()(a,d)},g=function(a,b,c){b.store().initialValue().each(function(c){b.store().setValue()(a,c)})};return[c.strict("getValue"),c.defaulted("setValue",d.noop),c.option("initialValue"),b.output("manager",{setValue:f,getValue:e,onLoad:g,onUnload:d.noop,state:a.init})]}),g("77",["5d","4q","29"],function(a,b,c){var d=function(a,b,c,d){c.set(d),b.onSetValue()(a,d)},e=function(a,b,c){return c.get()},f=function(a,b,c){b.store().initialValue().each(function(a){c.isNotSet()&&c.set(a)})},g=function(a,b,c){c.clear()};return[c.option("initialValue"),b.output("manager",{setValue:d,getValue:e,onLoad:f,onUnload:g,state:a.memory})]}),g("5c",["75","76","77","4q","29","2e"],function(a,b,c,d,e,f){return[e.defaultedOf("store",{mode:"memory"},f.choose("mode",{memory:c,manual:b,dataset:a})),d.onHandler("onSetValue"),e.defaulted("resetOnDom",!1)]}),g("3g",["q","5a","5b","5c","5d"],function(a,b,c,d,e){var f=a.create({fields:d,name:"representing",active:b,apis:c,extra:{setValueFrom:function(a,b){var c=f.getValue(b);f.setValue(a,c)}},state:e});return f}),g("5m",["39","78"],function(a,b){var c=b("width",function(a){return a.dom().offsetWidth}),d=function(a,b){c.set(a,b)},e=function(a){return c.get(a)},f=function(a){return c.getOuter(a)},g=function(b,d){var e=["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"],f=c.max(b,d,e);a.set(b,"max-width",f+"px")};return{set:d,get:e,getOuter:f,setMax:g}}),g("56",["q","33","3g","3d","2g","52","74","y","6","w","z","7","39","5m"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n){var o=l.detect().deviceType.isTouch(),p=function(l,p,q,r){var s=l.max()-l.min(),t=function(a){var b=a.element().dom().getBoundingClientRect();return(b.left+b.right)/2},u=function(a){return f.getPartOrDie(a,l,"thumb")},v=function(a,b,c){var d=c.value().get();return d<c.min()?f.getPart(a,c,"left-edge").fold(function(){return 0},function(a){return t(a)-b.left}):d>c.max()?f.getPart(a,c,"right-edge").fold(function(){return b.width},function(a){return t(a)-b.left}):(c.value().get()-c.min())/s*b.width},w=function(a){var b=f.getPartOrDie(a,l,"spectrum"),c=b.element().dom().getBoundingClientRect(),d=a.element().dom().getBoundingClientRect(),e=v(a,c,l);return c.left-d.left+e},x=function(a){var b=w(a),c=u(a),d=n.get(c.element())/2;m.set(c.element(),"left",b-d+"px")},y=function(a,b){var c=l.value().get(),d=u(a);return c!==b||m.getRaw(d.element(),"left").isNone()?(l.value().set(b),x(a),l.onChange()(a,d,b),k.some(!0)):k.none()},z=function(a){y(a,l.min(),k.none())},A=function(a){y(a,l.max(),k.none())},B=o?[d.run(e.touchstart(),function(a,b){l.onDragStart()(a,u(a))}),d.run(e.touchend(),function(a,b){l.onDragEnd()(a,u(a))})]:[d.run(e.mousedown(),function(a,b){b.stop(),l.onDragStart()(a,u(a)),l.mouseIsDown().set(!0)}),d.run(e.mouseup(),function(a,b){l.onDragEnd()(a,u(a)),l.mouseIsDown().set(!1)})];return{uid:l.uid(),dom:l.dom(),components:p,behaviours:j.deepMerge(a.derive(h.flatten([o?[]:[b.config({mode:"special",focusIn:function(a){return f.getPart(a,l,"spectrum").map(b.focusIn).map(i.constant(!0))}})],[c.config({store:{mode:"manual",getValue:function(a){return l.value().get()}}})]])),l.sliderBehaviours()),events:d.derive([d.run(g.changeEvent(),function(a,b){y(a,b.event().value())}),d.runOnAttached(function(a,b){l.value().set(l.getInitialValue()());var c=u(a);x(a),l.onInit()(a,c,l.value().get())})].concat(B)),apis:{resetToMin:z,resetToMax:A,refresh:x},domModification:{styles:{position:"relative"}}}};return{sketch:p}}),g("38",["34","54","55","56","1w"],function(a,b,c,d,e){return a.composite({name:"Slider",configFields:c,partFields:b,factory:d.sketch,apis:{resetToMin:function(a,b){a.resetToMin(b)},resetToMax:function(a,b){a.resetToMax(b)},refresh:function(a,b){a.refresh(b)}}})}),g("3a",["l"],function(a){var b=function(b,c,d){return a.forToolbar(c,function(){var a=d();b.setContextToolbar([{label:c+" group",items:a}])},{})};return{button:b}}),g("1p",["q","1f","1g","38","39","1l","i","3a","1o"],function(a,b,c,d,e,f,g,h,i){var j=-1,k=function(b){var h=function(a){return a<0?"black":a>360?"white":"hsl("+a+", 100%, 50%)"},j=function(a,b,c){var d=h(c);e.set(b.element(),"background-color",d)},k=function(a,c,d){var f=h(d);e.set(c.element(),"background-color",f),b.onChange(a,c,f)};return d.sketch({dom:i.dom('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[d.parts()["left-edge"](i.spec('<div class="${prefix}-hue-slider-black"></div>')),d.parts().spectrum({dom:i.dom('<div class="${prefix}-slider-gradient-container"></div>'),components:[i.spec('<div class="${prefix}-slider-gradient"></div>')],behaviours:a.derive([c.config({toggleClass:g.resolve("thumb-active")})])}),d.parts()["right-edge"](i.spec('<div class="${prefix}-hue-slider-white"></div>')),d.parts().thumb({dom:i.dom('<div class="${prefix}-slider-thumb"></div>'),behaviours:a.derive([c.config({toggleClass:g.resolve("thumb-active")})])})],onChange:k,onDragStart:function(a,b){c.on(b)},onDragEnd:function(a,b){c.off(b)},onInit:j,stepSize:10,min:0,max:360,getInitialValue:b.getInitialValue,sliderBehaviours:a.derive([f.orientation(d.refresh)])})},l=function(a){return[k(a)]},m=function(a,b){var c={onChange:function(a,c,d){b.undoManager.transact(function(){b.formatter.apply("forecolor",{value:d}),b.nodeChanged()})},getInitialValue:function(){return j}};return h.button(a,"color",function(){return l(c)})};return{makeItems:l,sketch:m}}),g("3b",["q","1f","1g","38","29","2e","1l","i","1o"],function(a,b,c,d,e,f,g,h,i){var j=f.objOfOnly([e.strict("getInitialValue"),e.strict("onChange"),e.strict("category"),e.strict("sizes")]),k=function(b){var e=f.asRawOrDie("SizeSlider",j,b),k=function(a){return a>=0&&a<e.sizes.length},l=function(a,b,c){k(c)&&e.onChange(c)};return d.sketch({dom:{tag:"div",classes:[h.resolve("slider-"+e.category+"-size-container"),h.resolve("slider"),h.resolve("slider-size-container")]},onChange:l,onDragStart:function(a,b){c.on(b)},onDragEnd:function(a,b){c.off(b)},min:0,max:e.sizes.length-1,stepSize:1,getInitialValue:e.getInitialValue,snapToGrid:!0,sliderBehaviours:a.derive([g.orientation(d.refresh)]),components:[d.parts().spectrum({dom:i.dom('<div class="${prefix}-slider-size-container"></div>'),components:[i.spec('<div class="${prefix}-slider-size-line"></div>')]}),d.parts().thumb({dom:i.dom('<div class="${prefix}-slider-thumb"></div>'),behaviours:a.derive([c.config({toggleClass:h.resolve("thumb-active")})])})]})};return{sketch:k}}),g("58",["1i","6","z","a"],function(a,b,c,d){var e=function(e,f,g){for(var h=e.dom(),i=a.isFunction(g)?g:b.constant(!1);h.parentNode;){h=h.parentNode;var j=d.fromDom(h),k=f(j);if(k.isSome())return k;if(i(j))break}return c.none()},f=function(a,b,d){var f=b(a);return f.orThunk(function(){return d(a)?c.none():e(a,b,d)})};return{ancestor:e,closest:f}}),g("3c",["y","6","z","1a","a","b","39","58","10"],function(a,b,c,d,e,f,g,h,i){var j=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],k="medium",l=2,m=function(a){return c.from(j[a])},n=function(b){return a.findIndex(j,function(a){return a===b})},o=function(a,b){var d=f.isElement(b)?c.some(b):i.parent(b);return d.map(function(b){var c=h.closest(b,function(a){return g.getRaw(a,"font-size")},a);return c.getOrThunk(function(){return g.get(b,"font-size")})}).getOr("")},p=function(b){var c=b.selection.getStart(),f=e.fromDom(c),g=e.fromDom(b.getBody()),h=function(a){return d.eq(g,a)},i=o(h,f);return a.find(j,function(a){return i===a}).getOr(k)},q=function(a,b){var c=p(a);c!==b&&a.execCommand("fontSize",!1,b)},r=function(a){var b=p(a);return n(b).getOr(l)},s=function(a,b){m(b).each(function(b){q(a,b)})};return{candidates:b.constant(j),get:r,apply:s}}),g("1q",["3b","3a","3c","1o"],function(a,b,c,d){var e=c.candidates(),f=function(b){return a.sketch({onChange:b.onChange,sizes:e,category:"font",getInitialValue:b.getInitialValue})},g=function(a){return[d.spec('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),f(a),d.spec('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')]},h=function(a,d){var e={onChange:function(a){c.apply(d,a)},getInitialValue:function(){return c.get(d)}};return b.button(a,"font-size",function(){return g(e)})};return{makeItems:g,sketch:h}});g("79",[],function(){function a(a,b){return e(document.createElement("canvas"),a,b)}function b(b){var d,e;return d=a(b.width,b.height),e=c(d),e.drawImage(b,0,0),d}function c(a){return a.getContext("2d")}function d(a){var b=null;try{b=a.getContext("webgl")||a.getContext("experimental-webgl")}catch(a){}return b||(b=null),b}function e(a,b,c){return a.width=b,a.height=c,a}return{create:a,clone:b,resize:e,get2dContext:c,get3dContext:d}});g("7a",[],function(){function a(a){return a.naturalWidth||a.width}function b(a){return a.naturalHeight||a.height}return{getWidth:a,getHeight:b}}),g("7b",[],function(){function a(a,b){return function(){a.apply(b,arguments)}}function b(b){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof b)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],h(b,a(d,this),a(e,this))}function c(a){var b=this;return null===this._state?void this._deferreds.push(a):void i(function(){var c=b._state?a.onFulfilled:a.onRejected;if(null===c)return void(b._state?a.resolve:a.reject)(b._value);var d;try{d=c(b._value)}catch(b){return void a.reject(b)}a.resolve(d)})}function d(b){try{if(b===this)throw new TypeError("A promise cannot be resolved with itself.");if(b&&("object"==typeof b||"function"==typeof b)){var c=b.then;if("function"==typeof c)return void h(a(c,b),a(d,this),a(e,this))}this._state=!0,this._value=b,f.call(this)}catch(a){e.call(this,a)}}function e(a){this._state=!1,this._value=a,f.call(this)}function f(){for(var a=0,b=this._deferreds.length;a<b;a++)c.call(this,this._deferreds[a]);this._deferreds=null}function g(a,b,c,d){this.onFulfilled="function"==typeof a?a:null,this.onRejected="function"==typeof b?b:null,this.resolve=c,this.reject=d}function h(a,b,c){var d=!1;try{a(function(a){d||(d=!0,b(a))},function(a){d||(d=!0,c(a))})}catch(a){if(d)return;d=!0,c(a)}}if(window.Promise)return window.Promise;var i=b.immediateFn||"function"==typeof setImmediate&&setImmediate||function(a){setTimeout(a,1)},j=Array.isArray||function(a){return"[object Array]"===Object.prototype.toString.call(a)};return b.prototype["catch"]=function(a){return this.then(null,a)},b.prototype.then=function(a,d){var e=this;return new b(function(b,f){c.call(e,new g(a,d,b,f))})},b.all=function(){var a=Array.prototype.slice.call(1===arguments.length&&j(arguments[0])?arguments[0]:arguments);return new b(function(b,c){function d(f,g){try{if(g&&("object"==typeof g||"function"==typeof g)){var h=g.then;if("function"==typeof h)return void h.call(g,function(a){d(f,a)},c)}a[f]=g,0===--e&&b(a)}catch(a){c(a)}}if(0===a.length)return b([]);for(var e=a.length,f=0;f<a.length;f++)d(f,a[f])})},b.resolve=function(a){return a&&"object"==typeof a&&a.constructor===b?a:new b(function(b){b(a)})},b.reject=function(a){return new b(function(b,c){c(a)})},b.race=function(a){return new b(function(b,c){for(var d=0,e=a.length;d<e;d++)a[d].then(b,c)})},b}),g("7c",["4m"],function(a){return function(b,c){var d=a.getOrDie("Blob");return new d(b,c)}}),g("7d",["4m"],function(a){return function(){var b=a.getOrDie("FileReader");return new b}}),g("7e",["4m"],function(a){return function(b){var c=a.getOrDie("Uint8Array");return new c(b)}}),g("7f",["4m"],function(a){var b=function(b){var c=a.getOrDie("requestAnimationFrame");c(b)},c=function(b){var c=a.getOrDie("atob");return c(b)};return{atob:c,requestAnimationFrame:b}}),g("59",["79","7a","7b","z","7c","7d","7e","7f","u","1w"],function(a,b,c,d,e,f,g,h,i,j){function k(a){return new c(function(b){function c(){a.removeEventListener("load",c),b(a)}a.complete?b(a):a.addEventListener("load",c)})}function l(a){return k(a).then(function(a){var b=a.src;return 0===b.indexOf("blob:")?n(b):0===b.indexOf("data:")?p(b):n(b)})}function m(a){return new c(function(b,c){function d(){h(),b(g)}function e(){h(),c("Unable to load data of type "+a.type+": "+f)}var f=URL.createObjectURL(a),g=new Image,h=function(){g.removeEventListener("load",d),g.removeEventListener("error",e)};g.addEventListener("load",d),g.addEventListener("error",e),g.src=f,g.complete&&d()})}function n(a){return new c(function(b){var c=new XMLHttpRequest;c.open("GET",a,!0),c.responseType="blob",c.onload=function(){200==this.status&&b(this.response)},c.send()})}function o(a){var b=a.split(","),c=/data:([^;]+)/.exec(b[0]);if(!c)return d.none();for(var f=c[1],k=b[1],l=1024,m=h.atob(k),n=m.length,o=j.ceil(n/l),p=new i(o),q=0;q<o;++q){for(var r=q*l,s=j.min(r+l,n),t=new i(s-r),u=r,v=0;u<s;++v,++u)t[v]=m[u].charCodeAt(0);p[q]=g(t)}return d.some(e(p,{type:f}))}function p(a){return new c(function(b,c){o(a).fold(function(){c("uri is not base64: "+a)},b)})}function q(a){return 0===a.indexOf("blob:")?n(a):0===a.indexOf("data:")?p(a):null}function r(a,b,d){return b=b||"image/png",HTMLCanvasElement.prototype.toBlob?new c(function(c){a.toBlob(function(a){c(a)},b,d)}):p(a.toDataURL(b,d))}function s(a,b,c){return b=b||"image/png",a.then(function(a){return a.toDataURL(b,c)})}function t(c){return m(c).then(function(c){w(c);var d,e;return e=a.create(b.getWidth(c),b.getHeight(c)),d=a.get2dContext(e),d.drawImage(c,0,0),e})}function u(a){return new c(function(b){var c=new f;c.onloadend=function(){b(c.result)},c.readAsDataURL(a)})}function v(a){return u(a).then(function(a){return a.split(",")[1]})}function w(a){URL.revokeObjectURL(a.src)}return{blobToImage:m,imageToBlob:l,blobToDataUri:u,blobToBase64:v,dataUriToBlobSync:o,canvasToBlob:r,canvasToDataURL:s,blobToCanvas:t,uriToBlob:q}}),g("3e",["59","z"],function(a,b){var c=function(b){return a.blobToImage(b)},d=function(b){return a.imageToBlob(b)},e=function(b){return a.blobToDataUri(b)},f=function(b){return a.blobToBase64(b)},g=function(b){return a.dataUriToBlobSync(b)},h=function(c){return b.from(a.uriToBlob(c))};return{blobToImage:c,imageToBlob:d,blobToDataUri:e,blobToBase64:f,dataUriToBlobSync:g,uriToBlob:h}}),g("1r",["1h","3d","2g","1n","3e","3f","z","1o"],function(a,b,c,d,e,f,g,h){var i=function(a,b){e.blobToBase64(b).then(function(c){a.undoManager.transact(function(){var d=a.editorUpload.blobCache,e=d.create(f.generate("mceu"),b,c);d.add(e);var g=a.dom.createHTML("img",{src:e.blobUri()});a.insertContent(g)})})},j=function(a){var b=a.event(),c=b.raw().target.files||b.raw().dataTransfer.files;return g.from(c[0])},k=function(e){var f={tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},g=a.record({dom:f,events:b.derive([b.cutter(c.click()),b.run(c.change(),function(a,b){j(b).each(function(a){i(e,a)})})])});return d.sketch({dom:h.dom('<span class="${prefix}-toolbar-button ${prefix}-icon-image ${prefix}-icon"></span>'),components:[g.asSpec()],action:function(a){var b=g.get(a);b.element().dom().click()}})};return{sketch:k}}),g("5e",[],function(){var a=function(a){return a.dom().textContent},b=function(a,b){a.dom().textContent=b};return{get:a,set:b}}),g("3h",["6","z","a","4i","5e","4u"],function(a,b,c,d,e,f){var g=function(a){return a.length>0},h=function(a){return void 0===a||null===a?"":a},i=function(a){var c=a.selection.getContent({format:"text"});return{url:"",text:c,title:"",target:"",link:b.none()}},j=function(a){var c=e.get(a),f=d.get(a,"href"),g=d.get(a,"title"),i=d.get(a,"target");return{url:h(f),text:c!==f?h(c):"",title:h(g),target:h(i),link:b.some(a)}},k=function(a){return q(a).fold(function(){return i(a)},function(a){return j(a)})},l=function(a){var b=d.get(a,"href"),c=e.get(a);return b===c},m=function(a,c,d){return d.text.filter(g).fold(function(){return l(a)?b.some(c):b.none()},b.some)},n=function(b,c){var d=c.link.bind(a.identity);d.each(function(a){b.execCommand("unlink")})},o=function(a,b){var c={};return c.href=a,b.title.filter(g).each(function(a){c.title=a}),b.target.filter(g).each(function(a){c.target=a}),c},p=function(b,c){c.url.filter(g).fold(function(){n(b,c)},function(f){var h=o(f,c),i=c.link.bind(a.identity);i.fold(function(){var a=c.text.filter(g).getOr(f);b.insertContent(b.dom.createHTML("a",h,b.dom.encode(a)))},function(a){var b=m(a,f,c);d.setAll(a,h),b.each(function(b){e.set(a,b)})})})},q=function(a){var b=c.fromDom(a.selection.getStart());return f.closest(b,"a")};return{getInfo:k,applyInfo:p,query:q}}),g("3u",["q","3d","29","6"],function(a,b,c,d){var e=function(e,f){var g=b.derive(f);return a.create({fields:[c.strict("enabled")],name:e,active:{events:d.constant(g)}})},f=function(b,c){var f=e(b,c);return{key:b,value:{config:{},me:f,configAsRaw:d.constant({}),initialConfig:{},state:a.noState()}}};return{events:e,config:f}}),g("7g",[],function(){var a=function(a,b,c){return b.find()(a)};return{getCurrent:a}}),g("7h",["29"],function(a){return[a.strict("find")]}),g("5f",["q","7g","7h"],function(a,b,c){return a.create({fields:c,name:"composing",apis:b})}),g("3v",["34","29","w"],function(a,b,c){var d=function(a,b){return{uid:a.uid(),dom:c.deepMerge({tag:"div",attributes:{role:"presentation"}},a.dom()),components:a.components(),behaviours:a.containerBehaviours(),events:a.events(),domModification:a.domModification(),eventOrder:a.eventOrder()}};return a.single({name:"Container",factory:d,configFields:[b.defaulted("components",[]),b.defaulted("containerBehaviours",{}),b.defaulted("events",{}),b.defaulted("domModification",{}),b.defaulted("eventOrder",{})]})}),g("5g",["q","5f","3g","3d","34","29","z"],function(a,b,c,d,e,f,g){var h=function(e,f){return{uid:e.uid(),dom:e.dom(),behaviours:a.derive([c.config({store:{mode:"memory",initialValue:e.getInitialValue()()}}),b.config({find:g.some})]),events:d.derive([d.runOnAttached(function(a,b){c.setValue(a,e.getInitialValue()())})])}};return e.single({name:"DataField",factory:h,configFields:[f.strict("uid"),f.strict("dom"),f.strict("getInitialValue")]})}),g("87",["4b","14"],function(a,b){var c=function(c,d){return a.nu({attributes:b.wrapAll([{key:d.tabAttr(),value:"true"}])})};return{exhibit:c}}),g("88",["29"],function(a){return[a.defaulted("tabAttr","data-alloy-tabstop")]}),g("64",["q","87","88"],function(a,b,c){return a.create({fields:c,name:"tabstopping",active:b})}),g("93",["v"],function(a){var b=function(a){return a.dom().value},c=function(b,c){if(void 0===c)throw new a("Value.set was undefined");b.dom().value=c};return{set:c,get:b}}),g("7i",["q","32","3g","64","4q","29","14","6","w","93"],function(a,b,c,d,e,f,g,h,i,j){var k=[f.option("data"),f.defaulted("inputAttributes",{}),f.defaulted("inputStyles",{}),f.defaulted("type","input"),f.defaulted("tag","input"),e.onHandler("onSetValue"),f.defaulted("styles",{}),f.option("placeholder"),f.defaulted("eventOrder",{}),f.defaulted("hasTabstop",!0),f.defaulted("inputBehaviours",{}),f.defaulted("selectOnFocus",!0)],l=function(e){return i.deepMerge(a.derive([c.config({store:{mode:"manual",initialValue:e.data().getOr(void 0),getValue:function(a){return j.get(a.element())},setValue:function(a,b){var c=j.get(a.element());c!==b&&j.set(a.element(),b)}},onSetValue:e.onSetValue()}),b.config({onFocus:e.selectOnFocus()===!1?h.noop:function(a){var b=a.element(),c=j.get(b);b.dom().setSelectionRange(0,c.length)}}),e.hasTabstop()?d.config({}):d.revoke()]),e.inputBehaviours())},m=function(a){return{tag:a.tag(),attributes:i.deepMerge(g.wrapAll([{key:"type",value:a.type()}].concat(a.placeholder().map(function(a){return{key:"placeholder",value:a}}).toArray())),a.inputAttributes()),styles:a.inputStyles()}};return{schema:h.constant(k),behaviours:l,dom:m}}),g("5h",["34","7i"],function(a,b){var c=function(a,c){return{uid:a.uid(),dom:b.dom(a),components:[],behaviours:b.behaviours(a),eventOrder:a.eventOrder()}};return a.single({name:"Input",configFields:b.schema(),factory:c})}),g("3i",["3u","q","5f","3g","1g","1h","3d","2","2g","1n","3v","5g","5h","z","i","1o"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p){var q="input-clearing",r=function(l,r){var s=f.record(m.sketch({placeholder:r,onSetValue:function(a,b){h.emit(a,i.input())},inputBehaviours:b.derive([c.config({find:n.some})]),selectOnFocus:!1})),t=f.record(j.sketch({dom:p.dom('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(a){var b=s.get(a);d.setValue(b,"")}}));return{name:l,spec:k.sketch({dom:p.dom('<div class="${prefix}-input-container"></div>'),components:[s.asSpec(),t.asSpec()],containerBehaviours:b.derive([e.config({toggleClass:o.resolve("input-container-empty")}),c.config({find:function(a){return n.some(s.get(a))}}),a.config(q,[g.run(i.input(),function(a){var b=s.get(a),c=d.getValue(b),f=c.length>0?e.off:e.on;f(a)})])])})}},s=function(a){return{name:a,spec:l.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return n.none()}})}};return{field:r,hidden:s}}),g("7k",["y","4i","2f","b"],function(a,b,c,d){var e=["input","button","textarea"],f=function(a,b,c){b.disabled()&&n(a,b,c)},g=function(b){return a.contains(e,d.name(b.element()))},h=function(a){return b.has(a.element(),"disabled")},i=function(a){b.set(a.element(),"disabled","disabled")},j=function(a){b.remove(a.element(),"disabled")},k=function(a){return"true"===b.get(a.element(),"aria-disabled")},l=function(a){b.set(a.element(),"aria-disabled","true")},m=function(a){b.set(a.element(),"aria-disabled","false")},n=function(a,b,d){b.disableClass().each(function(b){c.add(a.element(),b)});var e=g(a)?i:l;e(a)},o=function(a,b,d){b.disableClass().each(function(b){c.remove(a.element(),b)});var e=g(a)?j:m;e(a)},p=function(a){return g(a)?h(a):k(a)};return{enable:o,disable:n,isDisabled:p,onLoad:f}}),g("7j",["3d","t","27","7k","4b","y"],function(a,b,c,d,e,f){var g=function(a,b,c){return e.nu({classes:b.disabled()?b.disableClass().map(f.pure).getOr([]):[]})},h=function(e,f){return a.derive([a.abort(b.execute(),function(a,b){return d.isDisabled(a,e,f)}),c.loadEvent(e,f,d.onLoad)])};return{exhibit:g,events:h}}),g("7l",["29"],function(a){return[a.defaulted("disabled",!1),a.option("disableClass")]}),g("5i",["q","7j","7k","7l"],function(a,b,c,d){return a.create({fields:d,name:"disabling",active:b,apis:c})}),g("5k",["q","5f","3g","51","52","53","29","y","w","x"],function(a,b,c,d,e,f,g,h,i,j){var k="form",l=[g.defaulted("formBehaviours",{})],m=function(a){return"<alloy.field."+a+">"},n=function(a){var b=function(){var a=[],b=function(b,c){return a.push(b),e.generateOne(k,m(b),c)};return{field:b,record:function(){return a}}}(),c=a(b),g=b.record(),i=h.map(g,function(a){return f.required({name:a,pname:m(a)})});return d.composite(k,l,i,o,c)},o=function(d,f,g){return i.deepMerge({"debug.sketcher":{Form:g},uid:d.uid(),dom:d.dom(),components:f,behaviours:i.deepMerge(a.derive([c.config({store:{mode:"manual",getValue:function(a){var f=e.getAllParts(a,d);return j.map(f,function(a,d){return a().bind(b.getCurrent).map(c.getValue)})},setValue:function(a,f){j.each(f,function(f,g){e.getPart(a,d,g).each(function(a){b.getCurrent(a).each(function(a){c.setValue(a,f)})})})}}})]),d.formBehaviours())})};return{sketch:n}}),g("1z",["z","5"],function(a,b){var c=function(c){var d=b(a.none()),e=function(){d.get().each(c)},f=function(){e(),d.set(a.none())},g=function(b){e(),d.set(a.some(b))},h=function(){return d.get().isSome()};return{clear:f,isSet:h,set:g}},d=function(){return c(function(a){a.destroy()})},e=function(){return c(function(a){a.unbind()})},f=function(){var c=b(a.none()),d=function(){c.get().each(function(a){a.destroy()})},e=function(){d(),c.set(a.none())},f=function(b){d(),c.set(a.some(b))},g=function(a){c.get().each(a)},h=function(){return c.get().isSome()};return{clear:e,isSet:h,set:f,run:g}},g=function(){var c=b(a.none()),d=function(){c.set(a.none())},e=function(b){c.set(a.some(b))},f=function(a){c.get().each(a)},g=function(){return c.get().isSome()};return{clear:d,set:e,isSet:g,on:f}};return{destroyable:d,unbindable:e,api:f,value:g}}),g("5n",[],function(){var a=1,b=-1,c=0,d=function(a){return{xValue:a,points:[]}},e=function(c,d){if(d===c.xValue)return c;var e=d-c.xValue>0?a:b,f={direction:e,xValue:d},g=function(){if(0===c.points.length)return[];var a=c.points[c.points.length-1];return a.direction===e?c.points.slice(0,c.points.length-1):c.points}();return{xValue:d,points:g.concat([f])}},f=function(d){if(0===d.points.length)return c;var e=d.points[0].direction,f=d.points[d.points.length-1].direction;return e===b&&f===b?b:e===a&&f==a?a:c};return{init:d,move:e,complete:f}}),g("3j",["3u","q","5i","5j","33","1f","3g","1h","3d","2","2g","1n","3v","5k","29","2e","y","5","z","1z","39","5l","4u","5m","1l","5n","i","1o"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B){var C=function(f){var C="navigateEvent",D="serializer-wrapper-events",E="form-events",F=p.objOf([o.strict("fields"),o.defaulted("maxFieldIndex",f.fields.length-1),o.strict("onExecute"),o.strict("getInitialValue"),o.state("state",function(){return{dialogSwipeState:t.value(),currentScreen:r(0)}})]),G=p.asRawOrDie("SerialisedDialog",F,f),H=function(a,d,e){return l.sketch({dom:B.dom('<span class="${prefix}-icon-'+d+' ${prefix}-icon"></span>'),action:function(b){j.emitWith(b,C,{direction:a})},buttonBehaviours:b.derive([c.config({disableClass:A.resolve("toolbar-navigation-disabled"),disabled:!e})])})},I=function(a,b){w.descendant(a.element(),"."+A.resolve("serialised-dialog-chain")).each(function(a){u.set(a,"left",-G.state.currentScreen.get()*b.width+"px")})},J=function(a,b){var c=v.descendants(a.element(),"."+A.resolve("serialised-dialog-screen"));w.descendant(a.element(),"."+A.resolve("serialised-dialog-chain")).each(function(a){G.state.currentScreen.get()+b>=0&&G.state.currentScreen.get()+b<c.length&&(u.getRaw(a,"left").each(function(d){var e=parseInt(d,10),f=x.get(c[0]);
u.set(a,"left",e-b*f+"px")}),G.state.currentScreen.set(G.state.currentScreen.get()+b))})},K=function(a){var b=v.descendants(a.element(),"input"),c=s.from(b[G.state.currentScreen.get()]);c.each(function(b){a.getSystem().getByDom(b).each(function(b){j.dispatchFocus(a,b.element())})});var e=N.get(a);d.highlightAt(e,G.state.currentScreen.get())},L=function(){G.state.currentScreen.set(0),G.state.dialogSwipeState.clear()},M=h.record(n.sketch(function(c){return{dom:B.dom('<div class="${prefix}-serialised-dialog"></div>'),components:[m.sketch({dom:B.dom('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:q.map(G.fields,function(a,b){return b<=G.maxFieldIndex?m.sketch({dom:B.dom('<div class="${prefix}-serialised-dialog-screen"></div>'),components:q.flatten([[H(-1,"previous",b>0)],[c.field(a.name,a.spec)],[H(1,"next",b<G.maxFieldIndex)]])}):c.field(a.name,a.spec)})})],formBehaviours:b.derive([y.orientation(function(a,b){I(a,b)}),e.config({mode:"special",focusIn:function(a){K(a)},onTab:function(a){return J(a,1),s.some(!0)},onShiftTab:function(a){return J(a,-1),s.some(!0)}}),a.config(E,[i.runOnAttached(function(a,b){L();var c=N.get(a);d.highlightFirst(c),G.getInitialValue(a).each(function(b){g.setValue(a,b)})}),i.runOnExecute(G.onExecute),i.run(k.transitionend(),function(a,b){"left"===b.event().raw().propertyName&&K(a)}),i.run(C,function(a,b){var c=b.event().direction();J(a,c)})])])}})),N=h.record({dom:B.dom('<div class="${prefix}-dot-container"></div>'),behaviours:b.derive([d.config({highlightClass:A.resolve("dot-active"),itemClass:A.resolve("dot-item")})]),components:q.bind(G.fields,function(a,b){return b<=G.maxFieldIndex?[B.spec('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})});return{dom:B.dom('<div class="${prefix}-serializer-wrapper"></div>'),components:[M.asSpec(),N.asSpec()],behaviours:b.derive([e.config({mode:"special",focusIn:function(a){var b=M.get(a);e.focusIn(b)}}),a.config(D,[i.run(k.touchstart(),function(a,b){G.state.dialogSwipeState.set(z.init(b.event().raw().touches[0].clientX))}),i.run(k.touchmove(),function(a,b){G.state.dialogSwipeState.on(function(a){b.event().prevent(),G.state.dialogSwipeState.set(z.move(a,b.event().raw().touches[0].clientX))})}),i.run(k.touchend(),function(a){G.state.dialogSwipeState.on(function(b){var c=M.get(a),d=-1*z.complete(b);J(c,d)})})])])}};return{sketch:C}}),g("3k",["6","7"],function(a,b){var c=b.detect(),d=function(a,b){var c=b.selection.getRng();a(),b.selection.setRng(c)},e=function(b,e){var f=c.os.isAndroid()?d:a.apply;f(e,b)};return{forAndroid:e}}),g("1s",["3g","z","17","3h","l","3i","3j","3k"],function(a,b,c,d,e,f,g,h){var i=c.cached(function(c,e){return[{label:"the link group",items:[g.sketch({fields:[f.field("url","Type or paste URL"),f.field("text","Link text"),f.field("title","Link title"),f.field("target","Link target"),f.hidden("link")],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return b.some(d.getInfo(e))},onExecute:function(b){var f=a.getValue(b);d.applyInfo(e,f),c.restoreToolbar(),e.focus()}})]}]}),j=function(a,b){return e.forToolbarStateAction(b,"link","link",function(){var c=i(a,b);a.setContextToolbar(c),h.forAndroid(b,function(){a.focusToolbar()}),d.query(b).each(function(a){b.selection.select(a.dom())})})};return{sketch:j}}),g("3l",[],function(){return[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}]}),g("7q",["14","6","z","4i","2f"],function(a,b,c,d,e){var f=function(c,d,e,f){return a.readOptFrom(d.routes(),f.start()).map(b.apply).bind(function(c){return a.readOptFrom(c,f.destination()).map(b.apply)})},g=function(a,b,c){var d=k(a,b,c);return d.bind(function(d){return h(a,b,c,d)})},h=function(a,c,d,e){return f(a,c,d,e).bind(function(a){return a.transition().map(function(c){return{transition:b.constant(c),route:b.constant(a)}})})},i=function(a,b,c){g(a,b,c).each(function(c){var f=c.transition();e.remove(a.element(),f.transitionClass()),d.remove(a.element(),b.destinationAttr())})},j=function(a,c,e,f){return{start:b.constant(d.get(a.element(),c.stateAttr())),destination:b.constant(f)}},k=function(a,e,f){var g=a.element();return d.has(g,e.destinationAttr())?c.some({start:b.constant(d.get(a.element(),e.stateAttr())),destination:b.constant(d.get(a.element(),e.destinationAttr()))}):c.none()},l=function(a,b,c,e){i(a,b,c),d.has(a.element(),b.stateAttr())&&d.get(a.element(),b.stateAttr())!==e&&b.onFinish()(a,e),d.set(a.element(),b.stateAttr(),e)},m=function(a,b,c,e){d.has(a.element(),b.destinationAttr())&&(d.set(a.element(),b.stateAttr(),d.get(a.element(),b.destinationAttr())),d.remove(a.element(),b.destinationAttr()))},n=function(a,b,c,f){m(a,b,c,f);var g=j(a,b,c,f);h(a,b,c,g).fold(function(){l(a,b,c,f)},function(g){i(a,b,c);var h=g.transition();e.add(a.element(),h.transitionClass()),d.set(a.element(),b.destinationAttr(),f)})},o=function(a,b,e){var f=a.element();return d.has(f,b.stateAttr())?c.some(d.get(f,b.stateAttr())):c.none()};return{findRoute:f,disableTransition:i,getCurrentRoute:k,jumpTo:l,progressTo:n,getState:o}}),g("7p",["3d","2g","7q"],function(a,b,c){var d=function(d,e){return a.derive([a.run(b.transitionend(),function(a,b){var f=b.event().raw();c.getCurrentRoute(a,d,e).each(function(b){c.findRoute(a,d,e,b).each(function(g){g.transition().each(function(g){f.propertyName===g.property()&&(c.jumpTo(a,d,e,b.destination()),d.onTransition()(a,b))})})})}),a.runOnAttached(function(a,b){c.jumpTo(a,d,e,d.initialState())})])};return{events:d}}),g("7r",["4q","29","2e","48"],function(a,b,c,d){return[b.defaulted("destinationAttr","data-transitioning-destination"),b.defaulted("stateAttr","data-transitioning-state"),b.strict("initialState"),a.onHandler("onTransition"),a.onHandler("onFinish"),b.strictOf("routes",c.setOf(d.value,c.setOf(d.value,c.objOfOnly([b.optionObjOfOnly("transition",[b.strict("property"),b.strict("transitionClass")])]))))]}),g("5o",["q","7p","7q","7r","14","x"],function(a,b,c,d,e,f){var g=function(a){var b={};return f.each(a,function(a,c){var d=c.split("<->");b[d[0]]=e.wrap(d[1],a),b[d[1]]=e.wrap(d[0],a)}),b},h=function(a,b,c){return e.wrapAll([{key:a,value:e.wrap(b,c)},{key:b,value:e.wrap(a,c)}])},i=function(a,b,c,d){return e.wrapAll([{key:a,value:e.wrapAll([{key:b,value:d},{key:c,value:d}])},{key:b,value:e.wrapAll([{key:a,value:d},{key:c,value:d}])},{key:c,value:e.wrapAll([{key:a,value:d},{key:b,value:d}])}])};return a.create({fields:d,name:"transitioning",active:b,apis:c,extra:{createRoutes:g,createBistate:h,createTristate:i}})}),g("7t",["28","4c","29","2e","y","6","x","6n","v"],function(a,b,c,d,e,f,g,h,i){var j=function(j,k){var l=e.map(k,function(e){return c.field(e.name(),e.name(),b.asOption(),d.objOf([c.strict("config"),c.defaulted("state",a)]))}),m=d.asStruct("component.behaviours",d.objOf(l),j.behaviours).fold(function(a){throw new i(d.formatError(a)+"\nComplete spec:\n"+h.stringify(j,null,2))},f.identity);return{list:k,data:g.map(m,function(a){var b=a();return f.constant(b.map(function(a){return{config:a.config(),state:a.state().init(a.config())}}))})}},k=function(a){return a.list},l=function(a){return a.data};return{generateFrom:j,getBehaviours:k,getData:l}}),g("7s",["7t","14","y","x","v"],function(a,b,c,d,e){var f=function(a){var e=b.readOptFrom(a,"behaviours").getOr({}),f=c.filter(d.keys(e),function(a){return void 0!==e[a]});return c.map(f,function(b){return a.behaviours[b].me})},g=function(b,c){return a.generateFrom(b,c)},h=function(a){var b=f(a);return g(a,b)};return{generate:h,generateFrom:g}}),g("5q",["6q"],function(a){return a.exactly(["getSystem","config","spec","connect","disconnect","element","syncComponents","readState","components","events"])}),g("6a",["6q"],function(a){return a.exactly(["debugInfo","triggerFocus","triggerEvent","triggerEscape","addToWorld","removeFromWorld","addToGui","removeFromGui","build","getByUid","getByDom","broadcast","broadcastOn"])}),g("5r",["6a","13","6","v"],function(a,b,c,d){return function(e){var f=function(a){return function(){throw new d("The component must be in a context to send: "+a+"\n"+b.element(e().element())+" is not in context.")}};return a({debugInfo:c.constant("fake"),triggerEvent:f("triggerEvent"),triggerFocus:f("triggerFocus"),triggerEscape:f("triggerEscape"),build:f("build"),addToWorld:f("addToWorld"),removeFromWorld:f("removeFromWorld"),addToGui:f("addToGui"),removeFromGui:f("removeFromGui"),getByUid:f("getByUid"),getByDom:f("getByDom"),broadcast:f("broadcast"),broadcastOn:f("broadcastOn")})}}),g("95",["14","x"],function(a,b){var c=function(c,d){var e={};return b.each(c,function(c,f){b.each(c,function(b,c){var g=a.readOr(c,[])(e);e[c]=g.concat([d(f,b)])})}),e};return{byInnerKey:c}}),g("7u",["95","4b","14","y","x","w","6n","6","48"],function(a,b,c,d,e,f,g,h,i){var j=function(a,b){return{name:h.constant(a),modification:b}},k=function(a,b){var e=d.bind(a,function(a){return a.modification().getOr([])});return i.value(c.wrap(b,e))},l=function(a,b,e){return a.length>1?i.error('Multiple behaviours have tried to change DOM "'+b+'". The guilty behaviours are: '+g.stringify(d.map(a,function(a){return a.name()}))+". At this stage, this is not supported. Future releases might provide strategies for resolving this."):0===a.length?i.value({}):i.value(a[0].modification().fold(function(){return{}},function(a){return c.wrap(b,a)}))},m=function(a,b,c,e){return i.error("Mulitple behaviours have tried to change the _"+b+'_ "'+a+'". The guilty behaviours are: '+g.stringify(d.bind(e,function(a){return void 0!==a.modification().getOr({})[b]?[a.name()]:[]}),null,2)+". This is not currently supported.")},n=function(a,b){var f=d.foldl(a,function(d,f){var g=f.modification().getOr({});return d.bind(function(d){var f=e.mapToArray(g,function(e,f){return void 0!==d[f]?m(b,f,g,a):i.value(c.wrap(f,e))});return c.consolidate(f,d)})},i.value({}));return f.map(function(a){return c.wrap(b,a)})},o={classes:k,attributes:n,styles:n,domChildren:l,defChildren:l,innerHtml:l,value:l},p=function(g,h,k,l){var m=f.deepMerge({},h);d.each(k,function(a){m[a.name()]=a.exhibit(g,l)});var n=a.byInnerKey(m,j),p=e.map(n,function(a,b){return d.bind(a,function(a){return a.modification().fold(function(){return[]},function(b){return[a]})})}),q=e.mapToArray(p,function(a,b){return c.readOptFrom(o,b).fold(function(){return i.error("Unknown field type: "+b)},function(c){return c(a,b)})}),r=c.consolidate(q,{});return r.map(b.nu)};return{combine:p}}),g("96",["6n","48","v"],function(a,b,c){var d=function(d,e,f,g){var h=f.slice(0);try{var i=h.sort(function(b,f){var h=b[e](),i=f[e](),j=g.indexOf(h),k=g.indexOf(i);if(j===-1)throw new c("The ordering for "+d+" does not have an entry for "+h+".\nOrder specified: "+a.stringify(g,null,2));if(k===-1)throw new c("The ordering for "+d+" does not have an entry for "+i+".\nOrder specified: "+a.stringify(g,null,2));return j<k?-1:k<j?1:0});return b.value(i)}catch(a){return b.error([a])}};return{sortKeys:d}}),g("6b",["6"],function(a){var b=function(b,c){return{handler:b,purpose:a.constant(c)}},c=function(b,c){return{handler:a.curry.apply(void 0,[b.handler].concat(c)),purpose:b.purpose}},d=function(a){return a.handler};return{nu:b,curryArgs:c,getHandler:d}}),g("7v",["95","96","47","6b","14","y","6","w","x","48","6n","u","v"],function(a,b,c,d,e,f,g,h,i,j,k,l,m){var n=function(a,b){return{name:g.constant(a),handler:g.constant(b)}},o=function(a,b){var c={};return f.each(a,function(a){c[a.name()]=a.handlers(b)}),c},p=function(b,c,d){var e=h.deepMerge(d,o(c,b));return a.byInnerKey(e,n)},q=function(a,b,c,d){var e=p(a,c,d);return u(e,b)},r=function(a){var b=c.read(a);return function(a,c){var d=l.prototype.slice.call(arguments,0);b.abort.apply(void 0,d)?c.stop():b.can.apply(void 0,d)&&b.run.apply(void 0,d)}},s=function(a,b){return new j.error(["The event ("+a+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+k.stringify(f.map(b,function(a){return a.name()}),null,2)])},t=function(a,d,e){var g=d[e];return g?b.sortKeys("Event: "+e,"name",a,g).map(function(a){var b=f.map(a,function(a){return a.handler()});return c.fuse(b)}):s(e,a)},u=function(a,b){var c=i.mapToArray(a,function(a,c){var g=1===a.length?j.value(a[0].handler()):t(a,b,c);return g.map(function(g){var h=r(g),i=a.length>1?f.filter(b,function(b){return f.contains(a,function(a){return a.name()===b})}).join(" > "):a[0].name();return e.wrap(c,d.nu(h,i))})});return e.consolidate(c,{})};return{combine:q}}),g("7w",["4q","6o","4b","4s","4c","29","14","2e","y","6","w","v"],function(a,b,c,d,e,f,g,h,i,j,k,l){var m=function(b){return h.asStruct("custom.definition",h.objOfOnly([f.field("dom","dom",e.strict(),h.objOfOnly([f.strict("tag"),f.defaulted("styles",{}),f.defaulted("classes",[]),f.defaulted("attributes",{}),f.option("value"),f.option("innerHtml")])),f.strict("components"),f.strict("uid"),f.defaulted("events",{}),f.defaulted("apis",j.constant({})),f.field("eventOrder","eventOrder",e.mergeWith({"alloy.execute":["disabling","alloy.base.behaviour","toggling"],"alloy.focus":["alloy.base.behaviour","keying","focusing"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing"]}),h.anyValue()),f.option("domModification"),a.snapshot("originalSpec"),f.defaulted("debug.sketcher","unknown")]),b)},n=function(a){return g.wrap(d.idAttr(),a.uid())},o=function(a){var c={tag:a.dom().tag(),classes:a.dom().classes(),attributes:k.deepMerge(n(a),a.dom().attributes()),styles:a.dom().styles(),domChildren:i.map(a.components(),function(a){return a.element()})};return b.nu(k.deepMerge(c,a.dom().innerHtml().map(function(a){return g.wrap("innerHtml",a)}).getOr({}),a.dom().value().map(function(a){return g.wrap("value",a)}).getOr({})))},p=function(a){return a.domModification().fold(function(){return c.nu({})},c.nu)},q=function(a){return a.apis()},r=function(a){return a.events()};return{toInfo:m,toDefinition:o,toModification:p,toApis:q,toEvents:r}}),g("89",["y","2f","4j","u"],function(a,b,c,d){var e=function(c,d){a.each(d,function(a){b.add(c,a)})},f=function(c,d){a.each(d,function(a){b.remove(c,a)})},g=function(c,d){a.each(d,function(a){b.toggle(c,a)})},h=function(c,d){return a.forall(d,function(a){return b.has(c,a)})},i=function(c,d){return a.exists(d,function(a){return b.has(c,a)})},j=function(a){for(var b=a.dom().classList,c=new d(b.length),e=0;e<b.length;e++)c[e]=b.item(e);return c},k=function(a){return c.supports(a)?j(a):c.get(a)};return{add:e,remove:f,toggle:g,hasAll:h,hasAny:i,get:k}}),g("7x",["6o","y","4i","89","39","a","4n","2s","93","v"],function(a,b,c,d,e,f,g,h,i,j){var k=function(c){if(c.domChildren().isSome()&&c.defChildren().isSome())throw new j("Cannot specify children and child specs! Must be one or the other.\nDef: "+a.defToStr(c));return c.domChildren().fold(function(){var a=c.defChildren().getOr([]);return b.map(a,m)},function(a){return a})},l=function(a){var b=f.fromTag(a.tag());c.setAll(b,a.attributes().getOr({})),d.add(b,a.classes().getOr([])),e.setAll(b,a.styles().getOr({})),g.set(b,a.innerHtml().getOr(""));var j=k(a);return h.append(b,j),a.value().each(function(a){i.set(b,a)}),b},m=function(b){var c=a.nu(b);return l(c)};return{renderToDom:l}}),g("5p",["7s","5q","5r","50","7t","7u","7v","7w","4b","7x","2e","y","5","6","w","1i","6n","10","v"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s){var t=function(t){var u=function(){return N},v=m(c(u)),w=k.getOrDie(h.toInfo(o.deepMerge(t,{behaviours:void 0}))),x=a.generate(t),y=e.getBehaviours(x),z=e.getData(x),A=h.toDefinition(w),B={"alloy.base.modification":h.toModification(w)},C=f.combine(z,B,y,A).getOrDie(),D=i.merge(A,C),E=j.renderToDom(D),F={"alloy.base.behaviour":h.toEvents(w)},G=g.combine(z,w.eventOrder(),y,F).getOrDie(),H=m(w.components()),I=function(a){v.set(a)},J=function(){v.set(c(u))},K=function(){var a=r.children(E),b=l.bind(a,function(a){return v.get().getByDom(a).fold(function(){return[]},function(a){return[a]})});H.set(b)},L=function(a){if(a===d.apiConfig())return w.apis();var b=z,c=p.isFunction(b[a.name()])?b[a.name()]:function(){throw new s("Could not find "+a.name()+" in "+q.stringify(t,null,2))};return c()},M=function(a){return z[a]().map(function(a){return a.state.readState()}).getOr("not enabled")},N=b({getSystem:v.get,config:L,spec:n.constant(t),readState:M,connect:I,disconnect:J,element:n.constant(E),syncComponents:K,components:H.get,events:n.constant(G)});return N};return{build:t}}),g("5s",["3d","t","13","1a","16"],function(a,b,c,d,e){var f=function(a,b,c){return d.eq(b,a.element())&&!d.eq(b,c)};return{events:a.derive([a.can(b.focus(),function(a,d){var g=d.event().originator(),h=d.event().target();return!f(a,g,h)||(e.warn(b.focus()+" did not get interpreted by the desired target. \nOriginator: "+c.element(g)+"\nTarget: "+c.element(h)+"\nCheck the "+b.focus()+" event handlers"),!1)})])}}),g("5t",[],function(){var a=function(a){return a};return{make:a}}),g("3q",["5p","5q","5r","50","5s","30","5t","29","14","2e","y","5","6","w","z","48","a","v"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r){var s=function(a){var b=i.readOr("components",[])(a);return k.map(b,w)},t=function(b){var c=g.make(b),d=s(c),f=n.deepMerge(e,c,i.wrap("components",d));return p.value(a.build(f))},u=function(a){var b=q.fromText(a);return v({element:b})},v=function(a){var e=j.asStructOrDie("external.component",j.objOfOnly([h.strict("element"),h.option("uid")]),a),g=l(c()),i=function(a){g.set(a)},k=function(){g.set(c(function(){return n}))};e.uid().each(function(a){f.writeOnly(e.element(),a)});var n=b({getSystem:g.get,config:o.none,connect:i,disconnect:k,element:m.constant(e.element()),spec:m.constant(a),readState:m.constant("No state"),syncComponents:m.noop,components:m.constant([]),events:m.constant({})});return d.premade(n)},w=function(a){return d.getPremade(a).fold(function(){var b=n.deepMerge({uid:f.generate("")},a);return t(b).getOrDie()},function(a){return a})};return{build:w,premade:d.premade,external:v,text:u}}),g("9b",["32","2","6","8"],function(a,b,c,d){var e="alloy.item-hover",f="alloy.item-focus",g=function(c){(d.search(c.element()).isNone()||a.isFocused(c))&&(a.isFocused(c)||a.focus(c),b.emitWith(c,e,{item:c}))},h=function(a){b.emitWith(a,f,{item:a})};return{hover:c.constant(e),focus:c.constant(f),onHover:g,onFocus:h}}),g("98",["q","32","33","3g","1g","3d","2","2g","t","4q","9b","29","w"],function(a,b,c,d,e,f,g,h,i,j,k,l,m){var n=function(j){return{dom:m.deepMerge(j.dom(),{attributes:{role:j.toggling().isSome()?"menuitemcheckbox":"menuitem"}}),behaviours:m.deepMerge(a.derive([j.toggling().fold(e.revoke,function(a){return e.config(m.deepMerge({aria:{mode:"checked"}},a))}),b.config({ignore:j.ignoreFocus(),onFocus:function(a){k.onFocus(a)}}),c.config({mode:"execution"}),d.config({store:{mode:"memory",initialValue:j.data()}})]),j.itemBehaviours()),events:f.derive([f.runWithTarget(i.tapOrClick(),g.emitExecute),f.cutter(h.mousedown()),f.run(h.mouseover(),k.onHover),f.run(i.focusItem(),b.focus)]),components:j.components(),domModification:j.domModification()}},o=[l.strict("data"),l.strict("components"),l.strict("dom"),l.option("toggling"),l.defaulted("itemBehaviours",{}),l.defaulted("ignoreFocus",!1),l.defaulted("domModification",{}),j.output("builder",n)];return o}),g("99",["3d","t","4q","29"],function(a,b,c,d){var e=function(c){return{dom:c.dom(),components:c.components(),events:a.derive([a.stopper(b.focusItem())])}},f=[d.strict("dom"),d.strict("components"),c.output("builder",e)];return f}),g("a1",["q","3g","53","6"],function(a,b,c,d){var e="item-widget",f=[c.required({name:"widget",overrides:function(c){return{behaviours:a.derive([b.config({store:{mode:"manual",getValue:function(a){return c.data()},setValue:function(){}}})])}}})];return{owner:d.constant(e),parts:d.constant(f)}}),g("9a",["8v","q","32","33","3g","3d","2g","t","4q","a1","9b","52","29","w","z"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o){var p=function(i){var m=l.substitutes(j.owner(),i,j.parts()),p=l.components(j.owner(),i,m.internals()),q=function(a){return l.getPart(a,i,"widget").map(function(a){return d.focusIn(a),a})},r=function(b,c){return a.inside(c.event().target())?o.none():function(){return i.autofocus()?(c.setSource(b.element()),o.none()):o.none()}()};return n.deepMerge({dom:i.dom(),components:p,domModification:i.domModification(),events:f.derive([f.runOnExecute(function(a,b){q(a).each(function(a){b.stop()})}),f.run(g.mouseover(),k.onHover),f.run(h.focusItem(),function(a,b){i.autofocus()?q(a):c.focus(a)})]),behaviours:b.derive([e.config({store:{mode:"memory",initialValue:i.data()}}),c.config({onFocus:function(a){k.onFocus(a)}}),d.config({mode:"special",onLeft:r,onRight:r,onEscape:function(a,b){return c.isFocused(a)||i.autofocus()?i.autofocus()?(b.setSource(a.element()),o.none()):o.none():(c.focus(a),o.some(!0))}})])})},q=[m.strict("uid"),m.strict("data"),m.strict("components"),m.strict("dom"),m.defaulted("autofocus",!1),m.defaulted("domModification",{}),l.defaultUidsSchema(j.parts()),i.output("builder",p)];return q}),g("7y",["97","4q","98","99","9a","53","30","29","2e","6","w"],function(a,b,c,d,e,f,g,h,i,j,k){var l=i.choose("type",{widget:e,item:c,separator:d}),m=function(a,b){return{mode:"flatgrid",selector:"."+a.markers().item(),initSize:{numColumns:b.initSize().numColumns(),numRows:b.initSize().numRows()},focusManager:a.focusManager()}},n=function(a,b){return{mode:"menu",selector:"."+a.markers().item(),moveOnTab:b.moveOnTab(),focusManager:a.focusManager()}},o=[f.group({factory:{sketch:function(a){var b=i.asStructOrDie("menu.spec item",l,a);return b.builder()(b)}},name:"items",unit:"item",defaults:function(a,b){var c=g.generate("");return k.deepMerge({uid:c},b)},overrides:function(a,b){return{type:b.type,ignoreFocus:a.fakeFocus(),domModification:{classes:[a.markers().item()]}}}})],p=[h.strict("value"),h.strict("items"),h.strict("dom"),h.strict("components"),h.defaulted("eventOrder",{}),h.defaulted("menuBehaviours",{}),h.defaultedOf("movement",{mode:"menu",moveOnTab:!0},i.choose("mode",{grid:[b.initSize(),b.output("config",m)],menu:[h.defaulted("moveOnTab",!0),b.output("config",n)]})),b.itemMarkers(),h.defaulted("fakeFocus",!1),h.defaulted("focusManager",a.dom()),b.onHandler("onHighlight")];return{name:j.constant("Menu"),schema:j.constant(p),parts:j.constant(o)}}),g("9c",["6"],function(a){var b="alloy.menu-focus";return{focus:a.constant(b)}}),g("7z",["q","5f","5j","33","3g","3d","2","9b","9c","6","w","v"],function(a,b,c,d,e,f,g,h,i,j,k,l){var m=function(l,m,n,o){return k.deepMerge({dom:k.deepMerge(l.dom(),{attributes:{role:"menu"}}),uid:l.uid(),behaviours:k.deepMerge(a.derive([c.config({highlightClass:l.markers().selectedItem(),itemClass:l.markers().item(),onHighlight:l.onHighlight()}),e.config({store:{mode:"memory",initialValue:l.value()}}),b.config({find:j.identity}),d.config(l.movement().config()(l,l.movement()))]),l.menuBehaviours()),events:f.derive([f.run(h.focus(),function(a,b){var d=b.event();a.getSystem().getByDom(d.target()).each(function(d){c.highlight(a,d),b.stop(),g.emitWith(a,i.focus(),{menu:a,item:d})})}),f.run(h.hover(),function(a,b){var d=b.event().item();c.highlight(a,d)})]),components:m,eventOrder:l.eventOrder()})};return{make:m}}),g("5u",["34","7y","7z"],function(a,b,c){return a.composite({name:"Menu",configFields:b.schema(),partFields:b.parts(),factory:c.make})}),g("5w",["z","1a","8","2u","10"],function(a,b,c,d,e){var f=function(f,g){var h=e.owner(g),i=c.active(h).bind(function(c){var e=function(a){return b.eq(c,a)};return e(g)?a.some(g):d.descendant(g,e)}),j=f(g);return i.each(function(a){c.active(h).filter(function(c){return b.eq(c,a)}).orThunk(function(){c.focus(a)})}),j};return{preserve:f}}),g("3p",["5w","3","y","1a","9"],function(a,b,c,d,e){var f=function(d,e,f,g){b.detachChildren(d),a.preserve(function(){var a=c.map(g,d.getSystem().build);c.each(a,function(a){b.attach(d,a)})},d.element())},g=function(a,c,d,e){var f=a.getSystem().build(e);b.attachWith(a,f,d)},h=function(a,b,c,d){g(a,b,e.append,d)},i=function(a,b,c,d){g(a,b,e.prepend,d)},j=function(a,e,f,g){var h=k(a,e),i=c.find(h,function(a){return d.eq(g.element(),a.element())});i.each(b.detach)},k=function(a,b){return a.components()};return{append:h,prepend:i,remove:j,set:f,contents:k}}),g("1y",["q","3p"],function(a,b){return a.create({fields:[],name:"replacing",apis:b})}),g("a2",["14","y","x","z"],function(a,b,c,d){var e=function(a){return c.tupleMap(a,function(a,b){return{k:a,v:b}})},f=function(b,c,e,g){return a.readOptFrom(e,g).bind(function(g){return a.readOptFrom(b,g).bind(function(a){var g=f(b,c,e,a);return d.some([a].concat(g))})}).getOr([])},g=function(d,g){var h={};c.each(d,function(a,c){b.each(a,function(a){h[a]=c})});var i=g,j=e(g),k=c.map(j,function(a,b){return[b].concat(f(h,i,j,b))});return c.map(h,function(b){return a.readOptFrom(k,b).getOr([b])})};return{generate:g}}),g("9d",["a2","14","y","x","6","z","5"],function(a,b,c,d,e,f,g){return function(){var h=g({}),i=g({}),j=g({}),k=g(f.none()),l=g(e.constant([])),m=function(){h.set({}),i.set({}),j.set({}),k.set(f.none())},n=function(){return k.get().isNone()},o=function(b,c,d,e){k.set(f.some(b)),h.set(d),i.set(c),l.set(e);var g=e(c),m=a.generate(g,d);j.set(m)},p=function(a){return b.readOptFrom(h.get(),a).map(function(c){var d=b.readOptFrom(j.get(),a).getOr([]);return[c].concat(d)})},q=function(a){return b.readOptFrom(j.get(),a).bind(function(a){return a.length>1?f.some(a.slice(1)):f.none()})},r=function(a){return b.readOptFrom(j.get(),a)},s=function(a){return b.readOptFrom(i.get(),a)},t=function(a){var b=l.get()(i.get());return c.difference(d.keys(b),a)},u=function(){return k.get().bind(s)},v=function(){return i.get()};return{setContents:o,expand:p,refresh:r,collapse:q,lookupMenu:s,otherMenus:t,getPrimary:u,getMenus:v,clear:m,isClear:n}}}),g("80",["8v","q","5f","5j","33","1y","3g","3q","3d","2","t","97","5u","9d","9b","9c","14","y","6","w","x","z","15","12","2f","89","4u"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A){var B=function(s,B){var C=function(a,b){return u.map(b,function(b,c){var d=m.sketch(t.deepMerge(b,{value:c,items:b.items,markers:q.narrow(B.markers,["item","selectedItem"]),fakeFocus:s.fakeFocus(),onHighlight:s.onHighlight(),focusManager:s.fakeFocus()?l.highlights():l.dom()}));return a.getSystem().build(d)})},D=n(),E=function(a){var b=C(a,s.data().menus());return D.setContents(s.data().primary(),b,s.data().expansions(),function(b){return G(a,b)}),D.getPrimary()},F=function(a){return g.getValue(a).value},G=function(a,b){return u.map(s.data().menus(),function(a,b){return r.bind(a.items,function(a){return"separator"===a.type?[]:[a.data.value]})})},H=function(a,b){d.highlight(a,b),d.getHighlighted(b).orThunk(function(){return d.getFirst(b)}).each(function(b){j.dispatch(a,b.element(),k.focusItem())})},I=function(a,b){return w.cat(r.map(b,a.lookupMenu))},J=function(a,b,c){return v.from(c[0]).bind(b.lookupMenu).map(function(d){var e=I(b,c.slice(1));r.each(e,function(a){y.add(a.element(),s.markers().backgroundMenu())}),x.inBody(d.element())||f.append(a,h.premade(d)),z.remove(d.element(),[s.markers().backgroundMenu()]),H(a,d);var g=I(b,b.otherMenus(c));return r.each(g,function(b){z.remove(b.element(),[s.markers().backgroundMenu()]),s.stayInDom()||f.remove(a,b)}),d})},K=function(a,b){var c=F(b);return D.expand(c).bind(function(c){return v.from(c[0]).bind(D.lookupMenu).each(function(c){x.inBody(c.element())||f.append(a,h.premade(c)),s.onOpenSubmenu()(a,b,c),d.highlightFirst(c)}),J(a,D,c)})},L=function(a,b){var c=F(b);return D.collapse(c).bind(function(c){return J(a,D,c).map(function(c){return s.onCollapseMenu()(a,b,c),c})})},M=function(a,b){var c=F(b);return D.refresh(c).bind(function(b){return J(a,D,b)})},N=function(b,c){return a.inside(c.element())?v.none():K(b,c)},O=function(b,c){return a.inside(c.element())?v.none():L(b,c)},P=function(a,b){return L(a,b).orThunk(function(){return s.onEscape()(a,b)})},Q=function(a){return function(b,c){return A.closest(c.getSource(),"."+s.markers().item()).bind(function(c){return b.getSystem().getByDom(c).bind(function(c){return a(b,c)})})}},R=i.derive([i.run(p.focus(),function(a,b){var c=b.event().menu();d.highlight(a,c)}),i.runOnExecute(function(a,b){var c=b.event().target();return a.getSystem().getByDom(c).bind(function(b){var c=F(b);return 0===c.indexOf("collapse-item")?L(a,b):K(a,b).orThunk(function(){return s.onExecute()(a,b)})})}),i.runOnAttached(function(a,b){E(a).each(function(b){f.append(a,h.premade(b)),s.openImmediately()&&(H(a,b),s.onOpenMenu()(a,b))})})].concat(s.navigateOnHover()?[i.run(o.hover(),function(a,b){var c=b.event().item();M(a,c),K(a,c),s.onHover()(a,c)})]:[])),S=function(a){d.getHighlighted(a).each(function(b){d.getHighlighted(b).each(function(b){L(a,b)})})};return{uid:s.uid(),dom:s.dom(),behaviours:t.deepMerge(b.derive([e.config({mode:"special",onRight:Q(N),onLeft:Q(O),onEscape:Q(P),focusIn:function(a,b){D.getPrimary().each(function(b){j.dispatch(a,b.element(),k.focusItem())})}}),d.config({highlightClass:s.markers().selectedMenu(),itemClass:s.markers().menu()}),c.config({find:function(a){return d.getHighlighted(a)}}),f.config({})]),s.tmenuBehaviours()),eventOrder:s.eventOrder(),apis:{collapseMenu:S},events:R}};return{make:B,collapseItem:s.constant("collapse-item")}}),g("5v",["34","4q","80","29","14","3f"],function(a,b,c,d,e,f){var g=function(a,b,c){return{primary:a,menus:b,expansions:c}},h=function(a,b){return{primary:a,menus:e.wrap(a,b),expansions:{}}},i=function(a){return{value:f.generate(c.collapseItem()),text:a}};return a.single({name:"TieredMenu",configFields:[b.onStrictKeyboardHandler("onExecute"),b.onStrictKeyboardHandler("onEscape"),b.onStrictHandler("onOpenMenu"),b.onStrictHandler("onOpenSubmenu"),b.onHandler("onCollapseMenu"),d.defaulted("openImmediately",!0),d.strictObjOf("data",[d.strict("primary"),d.strict("menus"),d.strict("expansions")]),d.defaulted("fakeFocus",!1),b.onHandler("onHighlight"),b.onHandler("onHover"),b.tieredMenuMarkers(),d.strict("dom"),d.defaulted("navigateOnHover",!0),d.defaulted("stayInDom",!1),d.defaulted("tmenuBehaviours",{}),d.defaulted("eventOrder",{})],apis:{collapseMenu:function(a,b){a.collapseMenu(b)}},factory:c.make,extraApis:{tieredData:g,singleData:h,collapseItem:i}})}),g("3z",["6","2f","i"],function(a,b,c){var d=c.resolve("scrollable"),e=function(a){b.add(a,d)},f=function(a){b.remove(a,d)};return{register:e,deregister:f,scrollable:a.constant(d)}}),g("3m",["3u","q","3g","1g","5o","3q","1h","3d","1n","5u","5v","14","y","w","x","39","4u","5m","1l","i","3z"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u){var v=function(a){return l.readOptFrom(a,"format").getOr(a.title)},w=function(a,b){var c=y("Styles",[].concat(m.map(a.items,function(b){return x(v(b),b.title,b.isSelected(),b.getPreview(),l.hasKey(a.expansions,v(b)))})),b,!1),d=o.map(a.menus,function(c,d){var e=m.map(c,function(b){
return x(v(b),b.title,void 0!==b.isSelected&&b.isSelected(),void 0!==b.getPreview?b.getPreview():"",l.hasKey(a.expansions,v(b)))});return y(d,e,b,!0)}),e=n.deepMerge(d,l.wrap("styles",c)),f=k.tieredData("styles",e,a.expansions);return{tmenu:f}},x=function(a,c,e,f,g){return{data:{value:a,text:c},type:"item",dom:{tag:"div",classes:g?[t.resolve("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:t.resolve("format-matches"),selected:e},itemBehaviours:b.derive(g?[]:[s.format(a,function(a,b){var c=b?d.on:d.off;c(a)})]),components:[{dom:{tag:"div",attributes:{style:f},innerHtml:c}}]}},y=function(c,d,g,l){return{value:c,dom:{tag:"div"},components:[i.sketch({dom:{tag:"div",classes:[t.resolve("styles-collapser")]},components:l?[{dom:{tag:"span",classes:[t.resolve("styles-collapse-icon")]}},f.text(c)]:[f.text(c)],action:function(a){if(l){var b=g().get(a);k.collapseMenu(b)}}}),{dom:{tag:"div",classes:[t.resolve("styles-menu-items-container")]},components:[j.parts().items({})],behaviours:b.derive([a.config("adhoc-scrollable-menu",[h.runOnAttached(function(a,b){p.set(a.element(),"overflow-y","auto"),p.set(a.element(),"-webkit-overflow-scrolling","touch"),u.register(a.element())}),h.runOnDetached(function(a){p.remove(a.element(),"overflow-y"),p.remove(a.element(),"-webkit-overflow-scrolling"),u.deregister(a.element())})])])}],items:d,menuBehaviours:b.derive([e.config({initialState:"after",routes:e.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},z=function(a){var b=w(a.formats,function(){return d}),d=g.record(k.sketch({dom:{tag:"div",classes:[t.resolve("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(b,d){var e=c.getValue(d);a.handle(d,e.value)},onEscape:function(){},onOpenMenu:function(a,b){var c=r.get(a.element());r.set(b.element(),c),e.jumpTo(b,"current")},onOpenSubmenu:function(a,b,c){var d=r.get(a.element()),f=q.ancestor(b.element(),'[role="menu"]').getOrDie("hacky"),g=a.getSystem().getByDom(f).getOrDie();r.set(c.element(),d),e.progressTo(g,"before"),e.jumpTo(c,"after"),e.progressTo(c,"current")},onCollapseMenu:function(a,b,c){var d=q.ancestor(b.element(),'[role="menu"]').getOrDie("hacky"),f=a.getSystem().getByDom(d).getOrDie();e.progressTo(f,"after"),e.progressTo(c,"current")},navigateOnHover:!1,openImmediately:!0,data:b.tmenu,markers:{backgroundMenu:t.resolve("styles-background-menu"),menu:t.resolve("styles-menu"),selectedMenu:t.resolve("styles-selected-menu"),item:t.resolve("styles-item"),selectedItem:t.resolve("styles-selected-item")}}));return d.asSpec()};return{sketch:z}}),g("3n",["14","y","w"],function(a,b,c){var d=function(b){var d=c.deepMerge(a.exclude(b,["items"]),{menu:!0}),e=f(b.items,b.title),g=c.deepMerge(e.menus,a.wrap(b.title,e.items)),h=c.deepMerge(e.expansions,a.wrap(b.title,b.title));return{item:d,menus:g,expansions:h}},e=function(b){return a.hasKey(b,"items")?d(b):{item:b,menus:{},expansions:{}}},f=function(a){return b.foldr(a,function(a,b){var d=e(b);return{menus:c.deepMerge(a.menus,d.menus),items:[d.item].concat(a.items),expansions:c.deepMerge(a.expansions,d.expansions)}},{menus:{},expansions:{},items:[]})};return{expand:f}}),g("1t",["1g","14","y","6","3f","w","3l","3m","3n"],function(a,b,c,d,e,f,g,h,i){var j=function(a,h){var i=function(b){return function(){return a.formatter.match(b)}},j=function(b){return function(){var c=a.formatter.getCssText(b);return c}},k=function(a){return f.deepMerge(a,{isSelected:i(a.format),getPreview:j(a.format)})},l=function(a){return f.deepMerge(a,{isSelected:d.constant(!1),getPreview:d.constant("")})},m=function(b){var c=e.generate(b.title),d=f.deepMerge(b,{format:c,isSelected:i(c),getPreview:j(c)});return a.formatter.register(c,d),d},n=b.readOptFrom(h,"style_formats").getOr(g),o=function(a){return c.map(a,function(a){if(b.hasKey(a,"items")){var c=o(a.items);return f.deepMerge(l(a),{items:c})}return b.hasKey(a,"format")?k(a):m(a)})};return o(n)},k=function(a,d){var e=function(d){return c.bind(d,function(c){if(void 0!==c.items){var d=e(c.items);return d.length>0?[c]:[]}var f=!b.hasKey(c,"format")||a.formatter.canApply(c.format);return f?[c]:[]})},f=e(d);return i.expand(f)},l=function(b,c,d){var e=k(b,c);return h.sketch({formats:e,handle:function(c,e){b.undoManager.transact(function(){a.isOn(c)?b.formatter.remove(e):b.formatter.apply(e)}),d()}})};return{register:j,ui:l}}),g("h",["q","1f","1g","1h","14","y","6","z","1i","1j","1k","1l","g","i","l","1p","1q","1r","1s","1t"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t){var u=["undo","bold","italic","link","image","bullist","styleselect"],v=function(a){var b=a.replace(/\|/g," ").trim();return b.length>0?b.split(/\s+/):[]},w=function(a){return f.bind(a,function(a){return i.isArray(a)?w(a):v(a)})},x=function(a){var b=void 0!==a.toolbar?a.toolbar:u;return i.isArray(b)?w(b):v(b)},y=function(d,f){var g=function(a){return function(){return o.forToolbarCommand(f,a)}},i=function(a){return function(){return o.forToolbarStateCommand(f,a)}},j=function(a,b,c){return function(){return o.forToolbarStateAction(f,a,b,c)}},k=g("undo"),u=g("redo"),v=i("bold"),w=i("italic"),x=i("underline"),y=g("removeformat"),z=function(){return s.sketch(d,f)},A=j("unlink","link",function(){f.execCommand("unlink",null,!1)}),B=function(){return r.sketch(f)},C=j("unordered-list","ul",function(){f.execCommand("InsertUnorderedList",null,!1)}),D=j("ordered-list","ol",function(){f.execCommand("InsertOrderedList",null,!1)}),E=function(){return q.sketch(d,f)},F=function(){return p.sketch(d,f)},G=t.register(f,f.settings),H=function(){return t.ui(f,G,function(){f.fire("scrollIntoView")})},I=function(){return o.forToolbar("style-formats",function(a){f.fire("toReading"),d.dropup().appear(H,c.on,a)},a.derive([c.config({toggleClass:n.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),b.config({channels:e.wrapAll([l.receive(m.orientationChanged(),c.off),l.receive(m.dropupDismissed(),c.off)])})]))},J=function(a,b){return{isSupported:function(){return a.forall(function(a){return e.hasKey(f.buttons,a)})},sketch:b}};return{undo:J(h.none(),k),redo:J(h.none(),u),bold:J(h.none(),v),italic:J(h.none(),w),underline:J(h.none(),x),removeformat:J(h.none(),y),link:J(h.none(),z),unlink:J(h.none(),A),image:J(h.none(),B),bullist:J(h.some("bullist"),C),numlist:J(h.some("numlist"),D),fontsizeselect:J(h.none(),E),forecolor:J(h.none(),F),styleselect:J(h.none(),I)}},z=function(a,b){var c=x(a),d={};return f.bind(c,function(a){var c=!e.hasKey(d,a)&&e.hasKey(b,a)&&b[a].isSupported()?[b[a].sketch()]:[];return d[a]=!0,c})};return{identify:x,setup:y,detect:z}}),g("3o",["6","a"],function(a,b){var c=function(b,c,d,e,f,g,h){return{target:a.constant(b),x:a.constant(c),y:a.constant(d),stop:e,prevent:f,kill:g,raw:a.constant(h)}},d=function(d,e){return function(f){if(d(f)){var g=b.fromDom(f.target),h=function(){f.stopPropagation()},i=function(){f.preventDefault()},j=a.compose(i,h),k=c(g,f.clientX,f.clientY,h,i,j,f);e(k)}}},e=function(b,c,e,f,g){var i=d(e,f);return b.dom().addEventListener(c,i,g),{unbind:a.curry(h,b,c,i,g)}},f=function(a,b,c,d){return e(a,b,c,d,!1)},g=function(a,b,c,d){return e(a,b,c,d,!0)},h=function(a,b,c,d){a.dom().removeEventListener(b,c,d)};return{bind:f,capture:g}}),g("1u",["6","3o"],function(a,b){var c=a.constant(!0),d=function(a,d,e){return b.bind(a,d,c,e)},e=function(a,d,e){return b.capture(a,d,c,e)};return{bind:d,capture:e}}),h("1v",clearInterval),h("1x",setInterval),g("j",["6","z","7","1u","a","1v","1w","1x"],function(a,b,c,d,e,f,g,h){var i=50,j=1e3/i,k=function(b){var c=b.matchMedia("(orientation: portrait)").matches;return{isPortrait:a.constant(c)}},l=function(a){var b=c.detect().os.isiOS(),d=k(a).isPortrait();return b&&!d?a.screen.height:a.screen.width},m=function(a,c){var g=e.fromDom(a),l=null,m=function(){f(l);var b=k(a);c.onChange(b),o(function(){c.onReady(b)})},n=d.bind(g,"orientationchange",m),o=function(c){f(l);var d=a.innerHeight,e=0;l=h(function(){d!==a.innerHeight?(f(l),c(b.some(a.innerHeight))):e>j&&(f(l),c(b.none())),e++},i)},p=function(){n.unbind()};return{onAdjustment:o,destroy:p}};return{get:k,onChange:m,getActualWidth:l}}),h("86",clearTimeout),g("9e",["86","1j"],function(a,b){return function(c,d){var e=null,f=function(){var a=arguments;e=b(function(){c.apply(null,a),e=null},d)},g=function(){null!==e&&(a(e),e=null)};return{cancel:g,schedule:f}}}),g("8c",["9e","2g","t","14","5","6","z","1a","1w"],function(a,b,c,d,e,f,g,h,i){var j=5,k=400,l=function(a){return void 0===a.raw().touches||1!==a.raw().touches.length?g.none():g.some(a.raw().touches[0])},m=function(a,b){var c=i.abs(a.clientX-b.x()),d=i.abs(a.clientY-b.y());return c>j||d>j},n=function(i){var j=e(g.none()),n=a(function(a){j.set(g.none()),i.triggerEvent(c.longpress(),a)},k),o=function(a){return l(a).each(function(b){n.cancel();var c={x:f.constant(b.clientX),y:f.constant(b.clientY),target:a.target};n.schedule(c),j.set(g.some(c))}),g.none()},p=function(a){return n.cancel(),l(a).each(function(a){j.get().each(function(b){m(a,b)&&j.set(g.none())})}),g.none()},q=function(a){n.cancel();var b=function(b){return h.eq(b.target(),a.target())};return j.get().filter(b).map(function(b){return i.triggerEvent(c.tap(),a)})},r=d.wrapAll([{key:b.touchstart(),value:o},{key:b.touchmove(),value:p},{key:b.touchend(),value:q}]),s=function(a,b){return d.readOptFrom(r,b).bind(function(b){return b(a)})};return{fireIfReady:s}};return{monitor:n}}),g("81",["8c","1u"],function(a,b){var c=function(c){var d=a.monitor({triggerEvent:function(a,b){c.onTapContent(b)}}),e=function(){return b.bind(c.body(),"touchend",function(a){d.fireIfReady(a,"touchend")})},f=function(){return b.bind(c.body(),"touchmove",function(a){d.fireIfReady(a,"touchmove")})},g=function(a){d.fireIfReady(a,"touchstart")};return{fireTouchstart:g,onTouchend:e,onTouchmove:f}};return{monitor:c}}),g("5x",["1g","y","6","7","1a","8","1u","a","b","10","81"],function(a,b,c,d,e,f,g,h,i,j,k){var l=d.detect().os.version.major>=6,m=function(d,m,n){var o=k.monitor(d),p=j.owner(m),q=function(a){return!e.eq(a.start(),a.finish())||a.soffset()!==a.foffset()},r=function(){return f.active(p).filter(function(a){return"input"===i.name(a)}).exists(function(a){return a.dom().selectionStart!==a.dom().selectionEnd})},s=function(){var b=d.doc().dom().hasFocus()&&d.getSelection().exists(q);n.getByDom(m).each((b||r())===!0?a.on:a.off)},t=[g.bind(d.body(),"touchstart",function(a){d.onTouchContent(),o.fireTouchstart(a)}),o.onTouchmove(),o.onTouchend(),g.bind(m,"touchstart",function(a){d.onTouchToolstrip()}),d.onToReading(function(){f.blur(d.body())}),d.onToEditing(c.noop),d.onScrollToCursor(function(a){a.preventDefault(),d.getCursorBox().each(function(a){var b=d.win(),c=a.top()>b.innerHeight||a.bottom()>b.innerHeight,e=c?a.bottom()-b.innerHeight+50:0;0!==e&&b.scrollTo(b.pageXOffset,b.pageYOffset+e)})})].concat(l===!0?[]:[g.bind(h.fromDom(d.win()),"blur",function(){n.getByDom(m).each(a.off)}),g.bind(p,"select",s),g.bind(d.doc(),"selectionchange",s)]),u=function(){b.each(t,function(a){a.unbind()})};return{destroy:u}};return{initEvents:m}}),g("82",["y","6","8","a","b","1j"],function(a,b,c,d,e,f){var g=function(){return function(a){f(function(){a()},0)}},h=function(f){f.focus();var h=d.fromDom(f.document.body),i=c.active().exists(function(b){return a.contains(["input","textarea"],e.name(b))}),j=i?g(h):b.apply;j(function(){c.active().each(c.blur),c.focus(h)})};return{resume:h}}),h("9f",isNaN),h("8h",parseInt),g("83",["4i","9f","8h"],function(a,b,c){var d=function(d,e){var f=c(a.get(d,e),10);return b(f)?0:f};return{safeParse:d}}),g("aa",["7","z","v"],function(a,b,c){return function(d,e){var f=function(a){if(!d(a))throw new c("Can only get "+e+" value of a "+e+" node");return j(a).getOr("")},g=function(a){try{return h(a)}catch(a){return b.none()}},h=function(a){return d(a)?b.from(a.dom().nodeValue):b.none()},i=a.detect().browser,j=i.isIE()&&10===i.version.major?g:h,k=function(a,b){if(!d(a))throw new c("Can only set raw "+e+" value of a "+e+" node");a.dom().nodeValue=b};return{get:f,getOption:j,set:k}}}),g("a3",["b","aa"],function(a,b){var c=b(a.isText,"text"),d=function(a){return c.get(a)},e=function(a){return c.getOption(a)},f=function(a,b){c.set(a,b)};return{get:d,getOption:e,set:f}}),g("9g",["y","b","a3","10"],function(a,b,c,d){var e=function(a){return"img"===b.name(a)?1:c.getOption(a).fold(function(){return d.children(a).length},function(a){return a.length})},f=function(a,b){return e(a)===b},g=function(a,b){return 0===b},h="\xa0",i=function(a){return c.getOption(a).filter(function(a){return 0!==a.trim().length||a.indexOf(h)>-1}).isSome()},j=["img","br"],k=function(c){var d=i(c);return d||a.contains(j,b.name(c))};return{getEnd:e,isEnd:f,isStart:g,isCursorPosition:k}}),g("a4",["6k","6"],function(a,b){var c=a.generate([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),d=function(a,b,c,d){return a.fold(b,c,d)},e=function(a){return a.fold(b.identity,b.identity,b.identity)};return{before:c.before,on:c.on,after:c.after,cata:d,getStart:e}}),g("9h",["6k","2o","a","10","a4"],function(a,b,c,d,e){var f=a.generate([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),g=b.immutable("start","soffset","finish","foffset"),h=function(a){return f.exact(a.start(),a.soffset(),a.finish(),a.foffset())},i=function(a){return a.match({domRange:function(a){return c.fromDom(a.startContainer)},relative:function(a,b){return e.getStart(a)},exact:function(a,b,c,d){return a}})},j=function(a){var b=i(a);return d.defaultView(b)};return{domRange:f.domRange,relative:f.relative,exact:f.exact,exactFromRange:h,range:g,getWin:j}}),g("9i",["1a","a","10"],function(a,b,c){var d=function(a,b,d,e){var f=c.owner(a),g=f.dom().createRange();return g.setStart(a.dom(),b),g.setEnd(d.dom(),e),g},e=function(a,c,e,f){var g=d(a,c,e,f);return b.fromDom(g.commonAncestorContainer)},f=function(b,c,e,f){var g=d(b,c,e,f),h=a.eq(b,e)&&c===f;return g.collapsed&&!h};return{after:f,commonAncestorContainer:e}}),g("9j",["y","a","1b"],function(a,b,c){var d=function(d,e){var f=e||c,g=f.createDocumentFragment();return a.each(d,function(a){g.appendChild(a.dom())}),b.fromDom(g)};return{fromElements:d}}),g("9k",["6","z","1a","a"],function(a,b,c,d){var e=function(a,b){var c=a.document.createRange();return f(c,b),c},f=function(a,b){a.selectNodeContents(b.dom())},g=function(a,b){return b.compareBoundaryPoints(a.END_TO_START,a)<1&&b.compareBoundaryPoints(a.START_TO_END,a)>-1},h=function(a){return a.document.createRange()},i=function(a,b){b.fold(function(b){a.setStartBefore(b.dom())},function(b,c){a.setStart(b.dom(),c)},function(b){a.setStartAfter(b.dom())})},j=function(a,b){b.fold(function(b){a.setEndBefore(b.dom())},function(b,c){a.setEnd(b.dom(),c)},function(b){a.setEndAfter(b.dom())})},k=function(a,b){o(a),a.insertNode(b.dom())},l=function(a,b,d,e){return c.eq(a,d)&&b===e},m=function(a,b,c){var d=a.document.createRange();return i(d,b),j(d,c),d},n=function(a,b,c,d,e){var f=a.document.createRange();return f.setStart(b.dom(),c),f.setEnd(d.dom(),e),f},o=function(a){a.deleteContents()},p=function(a){var b=a.cloneContents();return d.fromDom(b)},q=function(b){return{left:a.constant(b.left),top:a.constant(b.top),right:a.constant(b.right),bottom:a.constant(b.bottom),width:a.constant(b.width),height:a.constant(b.height)}},r=function(a){var c=a.getClientRects(),d=c.length>0?c[0]:a.getBoundingClientRect();return d.width>0||d.height>0?b.some(d).map(q):b.none()},s=function(a){var c=a.getBoundingClientRect();return c.width>0||c.height>0?b.some(c).map(q):b.none()},t=function(a){return a.toString()};return{create:h,replaceWith:k,selectNodeContents:e,selectNodeContentsUsing:f,isCollapsed:l,relativeToNative:m,exactToNative:n,deleteContents:o,cloneFragment:p,getFirstRect:r,getBounds:s,isWithin:g,toString:t}}),g("9l",["6k","6","z","17","a","9k"],function(a,b,c,d,e,f){var g=a.generate([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),h=function(a,b,c){return b(e.fromDom(c.startContainer),c.startOffset,e.fromDom(c.endContainer),c.endOffset)},i=function(a,e){return e.match({domRange:function(a){return{ltr:b.constant(a),rtl:c.none}},relative:function(b,e){return{ltr:d.cached(function(){return f.relativeToNative(a,b,e)}),rtl:d.cached(function(){return c.some(f.relativeToNative(a,e,b))})}},exact:function(b,e,g,h){return{ltr:d.cached(function(){return f.exactToNative(a,b,e,g,h)}),rtl:d.cached(function(){return c.some(f.exactToNative(a,g,h,b,e))})}}})},j=function(a,b){var c=b.ltr();if(c.collapsed){var d=b.rtl().filter(function(a){return a.collapsed===!1});return d.map(function(a){return g.rtl(e.fromDom(a.endContainer),a.endOffset,e.fromDom(a.startContainer),a.startOffset)}).getOrThunk(function(){return h(a,g.ltr,c)})}return h(a,g.ltr,c)},k=function(a,b){var c=i(a,b);return j(a,c)},l=function(a,b){var c=k(a,b);return c.match({ltr:function(b,c,d,e){var f=a.document.createRange();return f.setStart(b.dom(),c),f.setEnd(d.dom(),e),f},rtl:function(b,c,d,e){var f=a.document.createRange();return f.setStart(d.dom(),e),f.setEnd(b.dom(),c),f}})};return{ltr:g.ltr,rtl:g.rtl,diagnose:k,asLtrRange:l}}),g("ab",["1w"],function(a){var b=function(b,c,d,e,f){if(0===f)return 0;if(c===e)return f-1;for(var g=e,h=1;h<f;h++){var i=b(h),j=a.abs(c-i.left);if(d>i.bottom);else{if(d<i.top||j>g)return h-1;g=j}}return 0},c=function(a,b,c){return b>=a.left&&b<=a.right&&c>=a.top&&c<=a.bottom};return{inRect:c,searchForPoint:b}}),g("ac",["z","15","a3","ab","1w"],function(a,b,c,d,e){var f=function(a,b,e,f,g){var h=function(c){var d=a.dom().createRange();return d.setStart(b.dom(),c),d.collapse(!0),d},i=function(a){var b=h(a);return b.getBoundingClientRect()},j=c.get(b).length,k=d.searchForPoint(i,e,f,g.right,j);return h(k)},g=function(c,e,g,h){var i=c.dom().createRange();i.selectNode(e.dom());var j=i.getClientRects(),k=b.findMap(j,function(b){return d.inRect(b,g,h)?a.some(b):a.none()});return k.map(function(a){return f(c,e,g,h,a)})};return{locate:g}}),g("a5",["z","15","b","10","ab","ac","1w"],function(a,b,c,d,e,f,g){var h=function(c,f,g,h){var j=c.dom().createRange(),k=d.children(f);return b.findMap(k,function(b){return j.selectNode(b.dom()),e.inRect(j.getBoundingClientRect(),g,h)?i(c,b,g,h):a.none()})},i=function(a,b,d,e){var g=c.isText(b)?f.locate:h;return g(a,b,d,e)},j=function(a,b,c,d){var e=a.dom().createRange();e.selectNode(b.dom());var f=e.getBoundingClientRect(),h=g.max(f.left,g.min(f.right,c)),j=g.max(f.top,g.min(f.bottom,d));return i(a,b,h,j)};return{locate:j}}),g("ad",["z","2u","10","9g"],function(a,b,c,d){var e=function(a){return b.descendant(a,d.isCursorPosition)},f=function(a){return g(a,d.isCursorPosition)},g=function(b,d){var e=function(b){for(var f=c.children(b),g=f.length-1;g>=0;g--){var h=f[g];if(d(h))return a.some(h);var i=e(h);if(i.isSome())return i}return a.none()};return e(b)};return{first:e,last:f}}),g("a6",["z","10","ad"],function(a,b,c){var d=!0,e=!1,f=function(a,b){return b-a.left<a.right-b?d:e},g=function(a,b,c){var d=a.dom().createRange();return d.selectNode(b.dom()),d.collapse(c),d},h=function(a,b,e){var h=a.dom().createRange();h.selectNode(b.dom());var i=h.getBoundingClientRect(),j=f(i,e),k=j===d?c.first:c.last;return k(b).map(function(b){return g(a,b,j)})},i=function(b,c,d){var e=c.dom().getBoundingClientRect(),h=f(e,d);return a.some(g(b,c,h))},j=function(a,c,d){var e=0===b.children(c).length?i:h;return e(a,c,d)};return{search:j}}),g("9m",["z","a","10","9h","a5","a6","1b","1w"],function(a,b,c,d,e,f,g,h){var i=function(b,c,d){return a.from(b.dom().caretPositionFromPoint(c,d)).bind(function(c){if(null===c.offsetNode)return a.none();var d=b.dom().createRange();return d.setStart(c.offsetNode,c.offset),d.collapse(),a.some(d)})},j=function(b,c,d){return a.from(b.dom().caretRangeFromPoint(c,d))},k=function(a,b,c,d){var f=a.dom().createRange();f.selectNode(b.dom());var g=f.getBoundingClientRect(),i=h.max(g.left,h.min(g.right,c)),j=h.max(g.top,h.min(g.bottom,d));return e.locate(a,b,i,j)},l=function(a,d,e){return b.fromPoint(a,d,e).bind(function(b){var g=function(){return f.search(a,b,d)};return 0===c.children(b).length?g():k(a,b,d,e).orThunk(g)})},m=g.caretPositionFromPoint?i:g.caretRangeFromPoint?j:l,n=function(a,c,e){var f=b.fromDom(a.document);return m(f,c,e).map(function(a){return d.range(b.fromDom(a.startContainer),a.startOffset,b.fromDom(a.endContainer),a.endOffset)})};return{fromPoint:n}}),g("9n",["y","a","b","5l","2r","9k","9l"],function(a,b,c,d,e,f,g){var h=function(b,c,g,h){var i=f.create(b),j=e.is(c,h)?[c]:[],k=j.concat(d.descendants(c,h));return a.filter(k,function(a){return f.selectNodeContentsUsing(i,a),f.isWithin(g,i)})},i=function(a,d,e){var f=g.asLtrRange(a,d),i=b.fromDom(f.commonAncestorContainer);return c.isElement(i)?h(a,i,f,e):[]};return{find:i}}),g("9o",["y","a","b","9h","a4"],function(a,b,c,d,e){var f=function(b,d){var f=c.name(b);return"input"===f?e.after(b):a.contains(["br","img"],f)?0===d?e.before(b):e.after(b):e.on(b,d)},g=function(a,b){var c=a.fold(e.before,f,e.after),g=b.fold(e.before,f,e.after);return d.relative(c,g)},h=function(a,b,c,e){var g=f(a,b),h=f(c,e);return d.relative(g,h)},i=function(a){return a.match({domRange:function(a){var c=b.fromDom(a.startContainer),d=b.fromDom(a.endContainer);return h(c,a.startOffset,d,a.endOffset)},relative:g,exact:h})};return{beforeSpecial:f,preprocess:i,preprocessRelative:g,preprocessExact:h}}),g("85",["z","9i","a","9j","10","9h","9k","9l","9m","9n","9o"],function(a,b,c,d,e,f,g,h,i,j,k){var l=function(b,c){a.from(b.getSelection()).each(function(a){a.removeAllRanges(),a.addRange(c)})},m=function(a,b,c,d,e){var f=g.exactToNative(a,b,c,d,e);l(a,f)},n=function(a,b,c){return j.find(a,b,c)},o=function(a,b){return h.diagnose(a,b).match({ltr:function(b,c,d,e){m(a,b,c,d,e)},rtl:function(b,c,d,e){var f=a.getSelection();f.extend?(f.collapse(b.dom(),c),f.extend(d.dom(),e)):m(a,d,e,b,c)}})},p=function(a,b,c,d,e){var f=k.preprocessExact(b,c,d,e);o(a,f)},q=function(a,b,c){var d=k.preprocessRelative(b,c);o(a,d)},r=function(a){var b=f.getWin(a).dom(),c=function(a,c,d,e){return g.exactToNative(b,a,c,d,e)},d=k.preprocess(a);return h.diagnose(b,d).match({ltr:c,rtl:c})},s=function(b){if(b.rangeCount>0){var d=b.getRangeAt(0),e=b.getRangeAt(b.rangeCount-1);return a.some(f.range(c.fromDom(d.startContainer),d.startOffset,c.fromDom(e.endContainer),e.endOffset))}return a.none()},t=function(d){var e=c.fromDom(d.anchorNode),g=c.fromDom(d.focusNode);return b.after(e,d.anchorOffset,g,d.focusOffset)?a.some(f.range(c.fromDom(d.anchorNode),d.anchorOffset,c.fromDom(d.focusNode),d.focusOffset)):s(d)},u=function(a,b){var c=g.selectNodeContents(a,b);l(a,c)},v=function(a,b){var d=g.selectNodeContents(a,b);return f.range(c.fromDom(d.startContainer),d.startOffset,c.fromDom(d.endContainer),d.endOffset)},w=function(b){var c=b.getSelection();return c.rangeCount>0?t(c):a.none()},x=function(a){return w(a).map(function(a){return f.exact(a.start(),a.soffset(),a.finish(),a.foffset())})},y=function(a,b){var c=h.asLtrRange(a,b);return g.getFirstRect(c)},z=function(a,b){var c=h.asLtrRange(a,b);return g.getBounds(c)},A=function(a,b,c){return i.fromPoint(a,b,c)},B=function(a,b){var c=h.asLtrRange(a,b);return g.toString(c)},C=function(a){var b=a.getSelection();b.removeAllRanges()},D=function(a,b){var c=h.asLtrRange(a,b);return g.cloneFragment(c)},E=function(a,b,c){var e=h.asLtrRange(a,b),f=d.fromElements(c,a.document);g.replaceWith(e,f)},F=function(a,b){var c=h.asLtrRange(a,b);g.deleteContents(c)};return{setExact:p,getExact:w,get:x,setRelative:q,toNative:r,setToElement:u,clear:C,clone:D,replace:E,deleteAt:F,forElement:v,getFirstRect:y,getBounds:z,getAtPoint:A,findWithin:n,getAsString:B}}),g("84",["y","6","a","10","9g","9h","85"],function(a,b,c,d,e,f,g){var h=2,i=function(a){return{left:a.left,top:a.top,right:a.right,bottom:a.bottom,width:b.constant(h),height:a.height}},j=function(a){return{left:b.constant(a.left),top:b.constant(a.top),right:b.constant(a.right),bottom:b.constant(a.bottom),width:b.constant(a.width),height:b.constant(a.height)}},k=function(b){if(b.collapsed){var h=c.fromDom(b.startContainer);return d.parent(h).bind(function(c){var d=f.exact(h,b.startOffset,c,e.getEnd(c)),j=g.getFirstRect(b.startContainer.ownerDocument.defaultView,d);return j.map(i).map(a.pure)}).getOr([])}return a.map(b.getClientRects(),j)},l=function(a){var b=a.getSelection();return void 0!==b&&b.rangeCount>0?k(b.getRangeAt(0)):[]};return{getRectangles:l}}),g("5y",["6","z","1u","a","4i","1w","82","i","83","84"],function(a,b,c,d,e,f,g,h,i,j){var k=50,l="data-"+h.resolve("last-outer-height"),m=function(a,b){e.set(a,l,b)},n=function(a){return i.safeParse(a,l)},o=function(b){return{top:a.constant(b.top()),bottom:a.constant(b.top()+b.height())}},p=function(a){var c=j.getRectangles(a);return c.length>0?b.some(c[0]).map(o):b.none()},q=function(a,c){var d=n(c),e=a.innerHeight;return d>e?b.some(d-e):b.none()},r=function(a,b,c){var d=b.top()>a.innerHeight||b.bottom()>a.innerHeight;return d?f.min(c,b.bottom()-a.innerHeight+k):0},s=function(a,b){var e=d.fromDom(b.document.body),f=function(){g.resume(b)},h=c.bind(d.fromDom(a),"resize",function(){q(a,e).each(function(a){p(b).each(function(c){var d=r(b,c,a);0!==d&&b.scrollTo(b.pageXOffset,b.pageYOffset+d)})}),m(e,a.innerHeight)});m(e,a.innerHeight);var i=function(){h.unbind()};return{toEditing:f,destroy:i}};return{setup:s}}),g("5z",["6","z","1a","1u","a","85"],function(a,b,c,d,e,f){var g=function(a){return b.some(e.fromDom(a.dom().contentWindow.document.body))},h=function(a){return b.some(e.fromDom(a.dom().contentWindow.document))},i=function(a){return b.from(a.dom().contentWindow)},j=function(a){var b=i(a);return b.bind(f.getExact)},k=function(a){return a.getFrame()},l=function(a,b){return function(c){var d=c[a].getOrThunk(function(){var a=k(c);return function(){return b(a)}});return d()}},m=function(a,b,c,e){return a[c].getOrThunk(function(){return function(a){return d.bind(b,e,a)}})},n=function(b){return{left:a.constant(b.left),top:a.constant(b.top),right:a.constant(b.right),bottom:a.constant(b.bottom),width:a.constant(b.width),height:a.constant(b.height)}},o=function(d){var l=k(d),o=function(a){var d=function(a){return c.eq(a.start(),a.finish())&&a.soffset()===a.foffset()},e=function(a){var c=a.start().dom().getBoundingClientRect();return c.width>0||c.height>0?b.some(c).map(n):b.none()};return f.getExact(a).filter(d).bind(e)};return g(l).bind(function(b){return h(l).bind(function(c){return i(l).map(function(g){var h=e.fromDom(c.dom().documentElement),i=d.getCursorBox.getOrThunk(function(){return function(){return f.get(g).bind(function(a){return f.getFirstRect(g,a).orThunk(function(){return o(g)})})}}),k=d.setSelection.getOrThunk(function(){return function(a,b,c,d){f.setExact(g,a,b,c,d)}}),n=d.clearSelection.getOrThunk(function(){return function(){f.clear(g)}});return{body:a.constant(b),doc:a.constant(c),win:a.constant(g),html:a.constant(h),getSelection:a.curry(j,l),setSelection:k,clearSelection:n,frame:a.constant(l),onKeyup:m(d,c,"onKeyup","keyup"),onNodeChanged:m(d,c,"onNodeChanged","selectionchange"),onDomChanged:d.onDomChanged,onScrollToCursor:d.onScrollToCursor,onScrollToElement:d.onScrollToElement,onToReading:d.onToReading,onToEditing:d.onToEditing,onToolbarScrollStart:d.onToolbarScrollStart,onTouchContent:d.onTouchContent,onTapContent:d.onTapContent,onTouchToolstrip:d.onTouchToolstrip,getCursorBox:i}})})})};return{getBody:l("getBody",g),getDoc:l("getDoc",h),getWin:l("getWin",i),getSelection:l("getSelection",j),getFrame:k,getActiveApi:o}}),g("60",["y","7","4i","39","5l"],function(a,b,c,d,e){var f="data-ephox-mobile-fullscreen-style",g="display:none!important;",h="position:absolute!important;",i="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;",j="background-color:rgb(255,255,255)!important;",k=b.detect().os.isAndroid(),l=function(a){var b=d.get(a,"background-color");return void 0!==b&&""!==b?"background-color:"+b+"!important":j},m=function(b,d){var j=function(a){var b=e.siblings(a,"*");return b},m=function(a){return function(b){var d=c.get(b,"style"),e=void 0===d?"no-styles":d.trim();e!==a&&(c.set(b,f,e),c.set(b,"style",a))}},n=e.ancestors(b,"*"),o=a.bind(n,j),p=l(d);a.each(o,m(g)),a.each(n,m(h+i+p));var q=k===!0?"":h;m(q+i+p)(b)},n=function(){var b=e.all("["+f+"]");a.each(b,function(a){var b=c.get(a,f);"no-styles"!==b?c.set(a,"style",b):c.remove(a,"style"),c.remove(a,f)})};return{clobberStyles:m,restoreStyles:n}}),g("61",["9","a","4i","4u"],function(a,b,c,d){var e=function(){var e=d.first("head").getOrDie(),f=function(){var d=b.fromTag("meta");return c.set(d,"name","viewport"),a.append(e,d),d},g=d.first('meta[name="viewport"]').getOrThunk(f),h=c.get(g,"content"),i=function(){c.set(g,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},j=function(){void 0!==h&&null!==h&&h.length>0?c.set(g,"content",h):c.set(g,"content","user-scalable=yes")};return{maximize:i,restore:j}};return{tag:e}}),g("3r",["1z","2f","5x","5y","5z","60","i","61"],function(a,b,c,d,e,f,g,h){var i=function(i,j){var k=h.tag(),l=a.api(),m=a.api(),n=function(){j.hide(),b.add(i.container,g.resolve("fullscreen-maximized")),b.add(i.container,g.resolve("android-maximized")),k.maximize(),b.add(i.body,g.resolve("android-scroll-reload")),l.set(d.setup(i.win,e.getWin(i.editor).getOrDie("no"))),e.getActiveApi(i.editor).each(function(a){f.clobberStyles(i.container,a.body()),m.set(c.initEvents(a,i.toolstrip,i.alloy))})},o=function(){k.restore(),j.show(),b.remove(i.container,g.resolve("fullscreen-maximized")),b.remove(i.container,g.resolve("android-maximized")),f.restoreStyles(),b.remove(i.body,g.resolve("android-scroll-reload")),m.clear(),l.clear()};return{enter:n,exit:o}};return{create:i}}),g("3s",["29","2e","6","a","10","1k"],function(a,b,c,d,e,f){return b.objOf([a.strictObjOf("editor",[a.strict("getFrame"),a.option("getBody"),a.option("getDoc"),a.option("getWin"),a.option("getSelection"),a.option("setSelection"),a.option("clearSelection"),a.option("cursorSaver"),a.option("onKeyup"),a.option("onNodeChanged"),a.option("getCursorBox"),a.strict("onDomChanged"),a.defaulted("onTouchContent",c.noop),a.defaulted("onTapContent",c.noop),a.defaulted("onTouchToolstrip",c.noop),a.defaulted("onScrollToCursor",c.constant({unbind:c.noop})),a.defaulted("onScrollToElement",c.constant({unbind:c.noop})),a.defaulted("onToEditing",c.constant({unbind:c.noop})),a.defaulted("onToReading",c.constant({unbind:c.noop})),a.defaulted("onToolbarScrollStart",c.identity)]),a.strict("socket"),a.strict("toolstrip"),a.strict("dropup"),a.strict("toolbar"),a.strict("container"),a.strict("alloy"),a.state("win",function(a){return e.owner(a.socket).dom().defaultView}),a.state("body",function(a){return d.fromDom(a.socket.dom().ownerDocument.body)}),a.defaulted("translate",c.identity),a.defaulted("setReadOnly",c.noop)])}),g("62",["86","1j"],function(a,b){var c=function(c,d){var e=null,f=null,g=function(){null!==e&&(a(e),e=null,f=null)},h=function(){f=arguments,null===e&&(e=b(function(){c.apply(null,f),e=null,f=null},d))};return{cancel:g,throttle:h}},d=function(c,d){var e=null,f=function(){null!==e&&(a(e),e=null)},g=function(){var a=arguments;null===e&&(e=b(function(){c.apply(null,a),e=null,a=null},d))};return{cancel:f,throttle:g}},e=function(c,d){var e=null,f=function(){null!==e&&(a(e),e=null)},g=function(){var f=arguments;null!==e&&a(e),e=b(function(){c.apply(null,f),e=null,f=null},d)};return{cancel:f,throttle:g}};return{adaptable:c,first:d,last:e}}),g("3t",["q","1g","1h","1n","3v","62","1j","i","1o"],function(a,b,c,d,e,f,g,h,i){var j=function(g,j){var k=c.record(e.sketch({dom:i.dom('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:a.derive([b.config({toggleClass:h.resolve("mask-tap-icon-selected"),toggleOnExecute:!1})])})),l=f.first(g,200);return e.sketch({dom:i.dom('<div class="${prefix}-disabled-mask"></div>'),components:[e.sketch({dom:i.dom('<div class="${prefix}-content-container"></div>'),
components:[d.sketch({dom:i.dom('<div class="${prefix}-content-tap-section"></div>'),components:[k.asSpec()],action:function(a){l.throttle()},buttonBehaviours:a.derive([b.config({toggleClass:h.resolve("mask-tap-icon-selected")})])})]})]})};return{sketch:j}}),g("20",["3q","2e","6","9","39","3r","3s","3t"],function(a,b,c,d,e,f,g,h){var i=function(i){var j=b.asRawOrDie("Getting AndroidWebapp schema",g,i);e.set(j.toolstrip,"width","100%");var k=function(){j.setReadOnly(!0),n.enter()},l=a.build(h.sketch(k,j.translate));j.alloy.add(l);var m={show:function(){j.alloy.add(l)},hide:function(){j.alloy.remove(l)}};d.append(j.container,l.element());var n=f.create(j,m);return{setReadOnly:j.setReadOnly,refreshStructure:c.noop,enter:n.enter,exit:n.exit,destroy:c.noop}};return{produce:i}}),g("63",["q","1y","4q","53","29","6"],function(a,b,c,d,e,f){var g=[e.defaulted("shell",!0),e.defaulted("toolbarBehaviours",{})],h=function(c){return{behaviours:a.derive([b.config({})])}},i=[d.optional({name:"groups",overrides:h})];return{name:f.constant("Toolbar"),schema:f.constant(g),parts:f.constant(i)}}),g("3w",["q","1y","34","52","63","w","z","16","v"],function(a,b,c,d,e,f,g,h,i){var j=function(c,e,j,k){var l=function(a,c){m(a).fold(function(){throw h.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new i("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(a){b.set(a,c)})},m=function(a){return c.shell()?g.some(a):d.getPart(a,c,"groups")},n=c.shell()?{behaviours:[b.config({})],components:[]}:{behaviours:[],components:e};return{uid:c.uid(),dom:c.dom(),components:n.components,behaviours:f.deepMerge(a.derive(n.behaviours),c.toolbarBehaviours()),apis:{setGroups:l},domModification:{attributes:{role:"group"}}}};return c.composite({name:"Toolbar",configFields:e.schema(),partFields:e.parts(),factory:j,apis:{setGroups:function(a,b,c){a.setGroups(b,c)}}})}),g("65",["4q","53","29","6"],function(a,b,c,d){var e=[c.strict("items"),a.markers(["itemClass"]),c.defaulted("hasTabstop",!0),c.defaulted("tgroupBehaviours",{})],f=[b.group({name:"items",unit:"item",overrides:function(a){return{domModification:{classes:[a.markers().itemClass()]}}}})];return{name:d.constant("ToolbarGroup"),schema:d.constant(e),parts:d.constant(f)}}),g("3x",["q","33","64","34","65","w","v"],function(a,b,c,d,e,f,g){var h=function(d,e,g,h){return f.deepMerge({dom:{attributes:{role:"toolbar"}}},{uid:d.uid(),dom:d.dom(),components:e,behaviours:f.deepMerge(a.derive([b.config({mode:"flow",selector:"."+d.markers().itemClass()}),d.hasTabstop()?c.config({}):c.revoke()]),d.tgroupBehaviours()),"debug.sketcher":g["debug.sketcher"]})};return d.composite({name:"ToolbarGroup",configFields:e.schema(),partFields:e.parts(),factory:h})}),g("3y",["6","1u","4i","4u","i"],function(a,b,c,d,e){var f="data-"+e.resolve("horizontal-scroll"),g=function(a){a.dom().scrollTop=1;var b=0!==a.dom().scrollTop;return a.dom().scrollTop=0,b},h=function(a){a.dom().scrollLeft=1;var b=0!==a.dom().scrollLeft;return a.dom().scrollLeft=0,b},i=function(a){return a.dom().scrollTop>0||g(a)},j=function(a){return a.dom().scrollLeft>0||h(a)},k=function(a){c.set(a,f,"true")},l=function(a){return"true"===c.get(a,f)?j:i},m=function(c,e){return b.bind(c,"touchmove",function(b){d.closest(b.target(),e).filter(l).fold(function(){b.raw().preventDefault()},a.noop)})};return{exclusive:m,markAsHorizontal:k}}),g("21",["3u","q","33","1g","3q","3d","3v","3w","3x","y","5","6","39","3y","i","3z","1o"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q){return function(){var r=function(c){var d=c.scrollable===!0?"${prefix}-toolbar-scrollable-group":"";return{dom:q.dom('<div aria-label="'+c.label+'" class="${prefix}-toolbar-group '+d+'"></div>'),tgroupBehaviours:b.derive([a.config("adhoc-scrollable-toolbar",c.scrollable===!0?[f.runOnInit(function(a,b){m.set(a.element(),"overflow-x","auto"),n.markAsHorizontal(a.element()),p.register(a.element())})]:[])]),components:[g.sketch({components:[i.parts().items({})]})],markers:{itemClass:o.resolve("toolbar-group-item")},items:c.items}},s=e.build(h.sketch({dom:q.dom('<div class="${prefix}-toolbar"></div>'),components:[h.parts().groups({})],toolbarBehaviours:b.derive([d.config({toggleClass:o.resolve("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),c.config({mode:"cyclic"})]),shell:!0})),t=e.build(g.sketch({dom:{classes:[o.resolve("toolstrip")]},components:[e.premade(s)],containerBehaviours:b.derive([d.config({toggleClass:o.resolve("android-selection-context-toolbar"),toggleOnExecute:!1})])})),u=function(){h.setGroups(s,v.get()),d.off(s)},v=k([]),w=function(a){v.set(a),u()},x=function(a){return j.map(a,l.compose(i.sketch,r))},y=function(){h.refresh(s)},z=function(a){d.on(s),h.setGroups(s,a)},A=function(){d.isOn(s)&&u()},B=function(){c.focusIn(s)};return{wrapper:l.constant(t),toolbar:l.constant(s),createGroups:x,setGroups:w,setContextToolbar:z,restoreToolbar:A,refresh:y,focus:B}}}),g("22",["q","1y","1","3q","1n","3v","6","2f","i","1o"],function(a,b,c,d,e,f,g,h,i,j){var k=function(a){return d.build(e.sketch({dom:j.dom('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){a.run(function(a){a.setReadOnly(!1)})}}))},l=function(){return d.build(f.sketch({dom:j.dom('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:a.derive([b.config({})])}))},m=function(a,c){b.append(a,d.premade(c))},n=function(a,c){b.remove(a,c)},o=function(a,b,d,e){var f=d===!0?c.toAlpha:c.toOmega;f(e);var g=d?m:n;g(a,b)};return{makeEditSwitch:k,makeSocket:l,updateMode:o}}),g("67",["2f","89","39"],function(a,b,c){var d=function(a,b){return b.getAnimationRoot().fold(function(){return a.element()},function(b){return b(a)})},e=function(a){return a.dimension().property()},f=function(a,b){return a.dimension().getDimension()(b)},g=function(a,c){var e=d(a,c);b.remove(e,[c.shrinkingClass(),c.growingClass()])},h=function(b,d){a.remove(b.element(),d.openClass()),a.add(b.element(),d.closedClass()),c.set(b.element(),e(d),"0px"),c.reflow(b.element())},i=function(a,b){j(a,b);var c=f(b,a.element());return h(a,b),c},j=function(b,d){a.remove(b.element(),d.closedClass()),a.add(b.element(),d.openClass()),c.remove(b.element(),e(d))},k=function(a,b,d){d.setCollapsed(),c.set(a.element(),e(b),f(b,a.element())),c.reflow(a.element()),g(a,b),h(a,b),b.onStartShrink()(a),b.onShrunk()(a)},l=function(b,g,i){i.setCollapsed(),c.set(b.element(),e(g),f(g,b.element())),c.reflow(b.element());var j=d(b,g);a.add(j,g.shrinkingClass()),h(b,g),g.onStartShrink()(b)},m=function(b,f,g){var h=i(b,f),k=d(b,f);a.add(k,f.growingClass()),j(b,f),c.set(b.element(),e(f),h),g.setExpanded(),f.onStartGrow()(b)},n=function(a,b,c){c.isExpanded()||m(a,b,c)},o=function(a,b,c){c.isExpanded()&&l(a,b,c)},p=function(a,b,c){c.isExpanded()&&k(a,b,c)},q=function(a,b,c){return c.isExpanded()},r=function(a,b,c){return c.isCollapsed()},s=function(b,c,e){var f=d(b,c);return a.has(f,c.growingClass())===!0},t=function(b,c,e){var f=d(b,c);return a.has(f,c.shrinkingClass())===!0},u=function(a,b,c){return s(a,b,c)===!0||t(a,b,c)===!0},v=function(a,b,c){var d=c.isExpanded()?l:m;d(a,b,c)};return{grow:n,shrink:o,immediateShrink:p,hasGrown:q,hasShrunk:r,isGrowing:s,isShrinking:t,isTransitioning:u,toggleGrow:v,disableTransitions:g}}),g("66",["3d","2g","67","4b","14","39"],function(a,b,c,d,e,f){var g=function(a,b){var c=b.expanded();return c?d.nu({classes:[b.openClass()],styles:{}}):d.nu({classes:[b.closedClass()],styles:e.wrap(b.dimension().property(),"0px")})},h=function(d,e){return a.derive([a.run(b.transitionend(),function(a,b){var g=b.event().raw();if(g.propertyName===d.dimension().property()){c.disableTransitions(a,d,e),e.isExpanded()&&f.remove(a.element(),d.dimension().property());var h=e.isExpanded()?d.onGrown():d.onShrunk();h(a,b)}})])};return{exhibit:g,events:h}}),g("68",["4q","29","2e","8a","5m"],function(a,b,c,d,e){return[b.strict("closedClass"),b.strict("openClass"),b.strict("shrinkingClass"),b.strict("growingClass"),b.option("getAnimationRoot"),a.onHandler("onShrunk"),a.onHandler("onStartShrink"),a.onHandler("onGrown"),a.onHandler("onStartGrow"),b.defaulted("expanded",!1),b.strictOf("dimension",c.choose("property",{width:[a.output("property","width"),a.output("getDimension",function(a){return e.get(a)+"px"})],height:[a.output("property","height"),a.output("getDimension",function(a){return d.get(a)+"px"})]}))]}),g("69",["4g","5","6"],function(a,b,c){var d=function(d){var e=b(d.expanded()),f=function(){return"expanded: "+e.get()};return a({isExpanded:function(){return e.get()===!0},isCollapsed:function(){return e.get()===!1},setCollapsed:c.curry(e.set,!1),setExpanded:c.curry(e.set,!0),readState:f})};return{init:d}}),g("40",["q","66","67","68","69"],function(a,b,c,d,e){return a.create({fields:d,name:"sliding",active:b,apis:c,state:e})}),g("23",["q","1y","40","3q","3v","6","1k","1l","i"],function(a,b,c,d,e,f,g,h,i){var j=function(j,k){var l=d.build(e.sketch({dom:{tag:"div",classes:i.resolve("dropup")},components:[],containerBehaviours:a.derive([b.config({}),c.config({closedClass:i.resolve("dropup-closed"),openClass:i.resolve("dropup-open"),shrinkingClass:i.resolve("dropup-shrinking"),growingClass:i.resolve("dropup-growing"),dimension:{property:"height"},onShrunk:function(a){j(),k(),b.set(a,[])},onGrown:function(a){j(),k()}}),h.orientation(function(a,b){n(f.noop)})])})),m=function(a,d,e){c.hasShrunk(l)===!0&&c.isTransitioning(l)===!1&&g.requestAnimationFrame(function(){d(e),b.set(l,[a()]),c.grow(l)})},n=function(a){g.requestAnimationFrame(function(){a(),c.shrink(l)})};return{appear:m,disappear:n,component:f.constant(l),element:l.element}};return{build:j}}),g("6c",["8b","t","8c","29","2e","y","7","1u","b","10","1j"],function(a,b,c,d,e,f,g,h,i,j,k){var l=function(b){return b.raw().which===a.BACKSPACE()[0]&&!f.contains(["input","textarea"],i.name(b.target()))},m=g.detect().browser.isFirefox(),n=e.objOfOnly([d.strictFunction("triggerEvent"),d.strictFunction("broadcastEvent"),d.defaulted("stopBackspace",!0)]),o=function(a,b){return m?h.capture(a,"focus",b):h.bind(a,"focusin",b)},p=function(a,b){return m?h.capture(a,"blur",b):h.bind(a,"focusout",b)},q=function(a,d){var i=e.asRawOrDie("Getting GUI events settings",n,d),m=g.detect().deviceType.isTouch()?["touchstart","touchmove","touchend","gesturestart"]:["mousedown","mouseup","mouseover","mousemove","mouseout","click"],q=c.monitor(i),r=f.map(m.concat(["selectstart","input","contextmenu","change","transitionend","dragstart","dragover","drop"]),function(b){return h.bind(a,b,function(a){q.fireIfReady(a,b).each(function(b){b&&a.kill()});var c=i.triggerEvent(b,a);c&&a.kill()})}),s=h.bind(a,"keydown",function(a){var b=i.triggerEvent("keydown",a);b?a.kill():i.stopBackspace===!0&&l(a)&&a.prevent()}),t=o(a,function(a){var b=i.triggerEvent("focusin",a);b&&a.kill()}),u=p(a,function(a){var c=i.triggerEvent("focusout",a);c&&a.kill(),k(function(){i.triggerEvent(b.postBlur(),a)},0)}),v=j.defaultView(a),w=h.bind(v,"scroll",function(a){var c=i.broadcastEvent(b.windowScroll(),a);c&&a.kill()}),x=function(){f.each(r,function(a){a.unbind()}),s.unbind(),t.unbind(),u.unbind(),w.unbind()};return{unbind:x}};return{setup:q}}),g("8d",["14","5"],function(a,b){var c=function(c,d){var e=a.readOptFrom(c,"target").map(function(a){return a()}).getOr(d);return b(e)};return{derive:c}}),g("8e",["5","6","v"],function(a,b,c){var d=function(c,d){var e=a(!1),f=a(!1),g=function(){e.set(!0)},h=function(){f.set(!0)};return{stop:g,cut:h,isStopped:e.get,isCut:f.get,event:b.constant(c),setSource:d.set,getSource:d.get}},e=function(d){var e=a(!1),f=function(){e.set(!0)};return{stop:f,cut:b.noop,isStopped:e.get,isCut:b.constant(!1),event:b.constant(d),setTarget:b.die(new c("Cannot set target of a broadcasted event")),getTarget:b.die(new c("Cannot get target of a broadcasted event"))}},f=function(b,c){var e=a(c);return d(b,e)};return{fromSource:d,fromExternal:e,fromTarget:f}}),g("6d",["6b","8d","8e","6k","y","10","v"],function(a,b,c,d,e,f,g){var h=d.generate([{stopped:[]},{resume:["element"]},{complete:[]}]),i=function(b,d,e,g,i,j){var k=b(d,g),l=c.fromSource(e,i);return k.fold(function(){return j.logEventNoHandlers(d,g),h.complete()},function(b){var c=b.descHandler(),e=a.getHandler(c);return e(l),l.isStopped()?(j.logEventStopped(d,b.element(),c.purpose()),h.stopped()):l.isCut()?(j.logEventCut(d,b.element(),c.purpose()),h.complete()):f.parent(b.element()).fold(function(){return j.logNoParent(d,b.element(),c.purpose()),h.complete()},function(a){return j.logEventResponse(d,b.element(),c.purpose()),h.resume(a)})})},j=function(a,b,c,d,e,f){return i(a,b,c,d,e,f).fold(function(){return!0},function(d){return j(a,b,c,d,e,f)},function(){return!1})},k=function(a,c,d,e,f){var g=b.derive(d,e);return i(a,c,d,e,g,f)},l=function(b,d,f){var g=c.fromExternal(d);return e.each(b,function(b){var c=b.descHandler(),d=a.getHandler(c);d(g)}),g.isStopped()},m=function(a,b,c,d){var e=c.target();return n(a,b,c,e,d)},n=function(a,c,d,e,f){var g=b.derive(d,e);return j(a,c,d,e,g,f)};return{triggerHandler:k,triggerUntilStopped:m,triggerOnUntilStopped:n,broadcast:l}}),g("9p",["2u"],function(a){var b=function(b,c,d){var e=a.closest(b,function(a){return c(a).isSome()},d);return e.bind(c)};return{closest:b}}),g("8f",["9p","6b","30","14","6","x","z","2o","16"],function(a,b,c,d,e,f,g,h,i){var j=h.immutable("element","descHandler"),k=function(a,b){return{id:e.constant(a),descHandler:e.constant(b)}};return function(){var e={},h=function(a,c,d){f.each(d,function(d,f){var g=void 0!==e[f]?e[f]:{};g[c]=b.curryArgs(d,a),e[f]=g})},i=function(a,b){return c.read(b).fold(function(a){return g.none()},function(c){var e=d.readOpt(c);return a.bind(e).map(function(a){return j(b,a)})})},l=function(a){return d.readOptFrom(e,a).map(function(a){return f.mapToArray(a,function(a,b){return k(b,a)})}).getOr([])},m=function(b,c,f){var g=d.readOpt(c),h=g(e);return a.closest(f,function(a){return i(h,a)},b)},n=function(a){f.each(e,function(b,c){b.hasOwnProperty(a)&&delete b[a]})};return{registerId:h,unregisterId:n,filterByType:l,find:m}}}),g("6e",["8f","13","30","14","12","v"],function(a,b,c,d,e,f){return function(){var g=a(),h={},i=function(a){var b=a.element();return c.read(b).fold(function(){return c.write("uid-",a.element())},function(a){return a})},j=function(a,c){var d=h[c];if(d!==a)throw new f('The tagId "'+c+'" is already used by: '+b.element(d.element())+"\nCannot use it for: "+b.element(a.element())+"\nThe conflicting element is"+(e.inBody(d.element())?" ":" not ")+"already in the DOM");l(a)},k=function(a){var b=i(a);d.hasKey(h,b)&&j(a,b);var c=[a];g.registerId(c,b,a.events()),h[b]=a},l=function(a){c.read(a.element()).each(function(a){h[a]=void 0,g.unregisterId(a)})},m=function(a){return g.filterByType(a)},n=function(a,b,c){return g.find(a,b,c)},o=function(a){return d.readOpt(a)(h)};return{find:n,filter:m,register:k,unregister:l,getById:o}}}),g("41",["3q","t","3","6a","3v","4","6b","6c","6d","13","6e","30","y","6","48","1a","8","9","11","b","2f","10","v"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w){var x=function(){var b=a.build(e.sketch({dom:{tag:"div"}}));return y(b)},y=function(e){var j=function(a){return v.parent(e.element()).fold(function(){return!0},function(b){return p.eq(a,b)})},r=k(),u=function(a,b){return r.find(j,a,b)},x=h.setup(e.element(),{triggerEvent:function(a,b){return f.monitorEvent(a,b.target(),function(c){return i.triggerUntilStopped(u,a,b,c)})},broadcastEvent:function(a,b){var c=r.filter(a);return i.broadcast(c,b)}}),y=d({debugInfo:n.constant("real"),triggerEvent:function(a,b,c){f.monitorEvent(a,b,function(d){i.triggerOnUntilStopped(u,a,c,b,d)})},triggerFocus:function(a,c){l.read(a).fold(function(){q.focus(a)},function(d){f.monitorEvent(b.focus(),a,function(d){i.triggerHandler(u,b.focus(),{originator:n.constant(c),target:n.constant(a)},a,d)})})},triggerEscape:function(a,b){y.triggerEvent("keydown",a.element(),b.event())},getByUid:function(a){return H(a)},getByDom:function(a){return I(a)},build:a.build,addToGui:function(a){B(a)},removeFromGui:function(a){C(a)},addToWorld:function(a){z(a)},removeFromWorld:function(a){A(a)},broadcast:function(a){F(a)},broadcastOn:function(a,b){G(a,b)}}),z=function(a){a.connect(y),t.isText(a.element())||(r.register(a),m.each(a.components(),z),y.triggerEvent(b.systemInit(),a.element(),{target:n.constant(a.element())}))},A=function(a){t.isText(a.element())||(m.each(a.components(),A),r.unregister(a)),a.disconnect()},B=function(a){c.attach(e,a)},C=function(a){c.detach(a)},D=function(){x.unbind(),s.remove(e.element())},E=function(a){var c=r.filter(b.receive());m.each(c,function(b){var c=b.descHandler(),d=g.getHandler(c);d(a)})},F=function(a){E({universal:n.constant(!0),data:n.constant(a)})},G=function(a,b){E({universal:n.constant(!1),channels:n.constant(a),data:n.constant(b)})},H=function(a){return r.getById(a).fold(function(){return o.error(new w('Could not find component with uid: "'+a+'" in system.'))},o.value)},I=function(a){return l.read(a).bind(H)};return z(e),{root:n.constant(e),element:e.element,destroy:D,add:B,remove:C,getByUid:H,getByDom:I,addToWorld:z,removeFromWorld:A,broadcast:F,broadcastOn:G}};return{create:x,takeover:y}}),g("24",["q","1","3q","41","3v","6","i"],function(a,b,c,d,e,f,g){var h=f.constant(g.resolve("readonly-mode")),i=f.constant(g.resolve("edit-mode"));return function(f){var j=c.build(e.sketch({dom:{classes:[g.resolve("outer-container")].concat(f.classes)},containerBehaviours:a.derive([b.config({alpha:h(),omega:i()})])}));return d.takeover(j)}}),g("k",["1y","1","6","1z","20","i","21","22","23","24"],function(a,b,c,d,e,f,g,h,i,j){return function(b){var k=j({classes:[f.resolve("android-container")]}),l=g(),m=d.api(),n=h.makeEditSwitch(m),o=h.makeSocket(),p=i.build(c.noop,b);k.add(l.wrapper()),k.add(o),k.add(p.component());var q=function(a){var b=l.createGroups(a);l.setGroups(b)},r=function(a){var b=l.createGroups(a);l.setContextToolbar(b)},s=function(){l.focus()},t=function(){l.restoreToolbar()},u=function(a){m.set(e.produce(a))},v=function(){m.run(function(b){b.exit(),a.remove(o,n)})},w=function(a){h.updateMode(o,n,a,k.root())};return{system:c.constant(k),element:k.element,init:u,exit:v,setToolbarGroups:q,setContextToolbar:r,focusToolbar:s,restoreToolbar:t,updateMode:w,socket:c.constant(o),dropup:c.constant(p)}}}),g("9q",["6"],function(a){var b=function(c,d){var e=function(a,e){return b(c+a,d+e)};return{left:a.constant(c),top:a.constant(d),translate:e}};return b}),g("9r",["6","1a","a","b","2u","1b"],function(a,b,c,d,e,f){var g=function(d,g){var h=g||c.fromDom(f.documentElement);return e.ancestor(d,a.curry(b.eq,h)).isSome()},h=function(a){var b=a.dom();return b===b.window?a:d.isDocument(a)?b.defaultView||b.parentWindow:null};return{attached:g,windowOf:h}}),g("8g",["9q","9r","a"],function(a,b,c){var d=function(b){var c=b.getBoundingClientRect();return a(c.left,c.top)},e=function(a,b){return void 0!==a?a:void 0!==b?b:0},f=function(a){var d=a.dom().ownerDocument,f=d.body,g=b.windowOf(c.fromDom(d)),i=d.documentElement,j=e(g.pageYOffset,i.scrollTop),k=e(g.pageXOffset,i.scrollLeft),l=e(i.clientTop,f.clientTop),m=e(i.clientLeft,f.clientLeft);return h(a).translate(k-m,j-l)},g=function(b){var c=b.dom();return a(c.offsetLeft,c.offsetTop)},h=function(e){var f=e.dom(),g=f.ownerDocument,h=g.body,i=c.fromDom(g.documentElement);return h===f?a(h.offsetLeft,h.offsetTop):b.attached(e,i)?d(f):a(0,0)};return{absolute:f,relative:g,viewport:h}}),g("6f",["8c","y","62","1a","1u","8a","8g","81"],function(a,b,c,d,e,f,g,h){var i=function(a,i,j,k,l){var m=function(){i.run(function(a){a.highlightSelection()})},n=function(){i.run(function(a){a.refreshSelection()})},o=function(a,b){var c=a-k.dom().scrollTop;i.run(function(a){a.scrollIntoView(c,c+b)})},p=function(a){var b=g.absolute(a).top(),c=f.get(a);o(i,k,b,c)},q=function(){a.getCursorBox().each(function(a){o(a.top(),a.height())})},r=function(){i.run(function(a){a.clearSelection()})},s=function(){r(),z.throttle()},t=function(){q(a,i,k),i.run(function(a){a.syncHeight()})},u=function(){var b=f.get(j);i.run(function(a){a.setViewportOffset(b)}),n(i),t(a,i,k)},v=function(){i.run(function(a){a.toEditing()})},w=function(){i.run(function(a){a.toReading()})},x=function(a){i.run(function(b){b.onToolbarTouch(a)})},y=h.monitor(a),z=c.last(t,300),A=[a.onKeyup(s),a.onNodeChanged(n),a.onDomChanged(z.throttle),a.onDomChanged(n),a.onScrollToCursor(function(a){a.preventDefault(),z.throttle()}),a.onScrollToElement(function(a){p(a.element())}),a.onToEditing(v),a.onToReading(w),e.bind(a.doc(),"touchend",function(b){d.eq(a.html(),b.target())||d.eq(a.body(),b.target())}),e.bind(j,"transitionend",function(a){"height"===a.raw().propertyName&&u()}),e.capture(j,"touchstart",function(b){m(),x(b),a.onTouchToolstrip()}),e.bind(a.body(),"touchstart",function(b){r(),a.onTouchContent(),y.fireTouchstart(b)}),y.onTouchmove(),y.onTouchend(),e.bind(a.body(),"click",function(a){a.kill()}),e.bind(j,"touchmove",function(){a.onToolbarScrollStart()})],B=function(){b.each(A,function(a){a.unbind()})};return{destroy:B}};return{initEvents:i}}),g("9s",["8","1j"],function(a,b){var c=function(c){var d=c.dom().selectionStart,e=c.dom().selectionEnd,f=c.dom().selectionDirection;b(function(){c.dom().setSelectionRange(d,e,f),a.focus(c)},50)},d=function(a){var b=a.getSelection();if(b.rangeCount>0){var c=b.getRangeAt(0),d=a.document.createRange();d.setStart(c.startContainer,c.startOffset),d.setEnd(c.endContainer,c.endOffset),b.removeAllRanges(),b.addRange(d)}};return{refreshInput:c,refresh:d}}),g("8p",["1a","8","a","9s"],function(a,b,c,d){var e=function(e,f){b.active().each(function(c){a.eq(c,f)||b.blur(c)}),e.focus(),b.focus(c.fromDom(e.document.body)),d.refresh(e)};return{resume:e}}),g("8i",["y","9","2s","11","1u","a","2f","89","39","10","8p","i","84"],function(a,b,c,d,e,f,g,h,i,j,k,l,m){return function(n,o){var p=n.document,q=f.fromTag("div");g.add(q,l.resolve("unfocused-selections")),b.append(f.fromDom(p.documentElement),q);var r=e.bind(q,"touchstart",function(a){a.prevent(),k.resume(n,o),u()}),s=function(a){var b=f.fromTag("span");return h.add(b,[l.resolve("layer-editor"),l.resolve("unfocused-selection")]),i.setAll(b,{left:a.left()+"px",top:a.top()+"px",width:a.width()+"px",height:a.height()+"px"}),b},t=function(){u();var b=m.getRectangles(n),d=a.map(b,s);c.append(q,d)},u=function(){d.empty(q)},v=function(){r.unbind(),d.remove(q)},w=function(){return j.children(q).length>0};return{update:t,isActive:w,destroy:v,clear:u}}}),g("9w",["y","z","1j"],function(a,b,c){var d=function(e){var f=b.none(),g=[],h=function(a){return d(function(b){i(function(c){b(a(c))})})},i=function(a){k()?m(a):g.push(a)},j=function(a){f=b.some(a),l(g),g=[]},k=function(){return f.isSome()},l=function(b){a.each(b,m)},m=function(a){f.each(function(b){c(function(){a(b)},0)})};return e(j),{get:i,map:h,isReady:k}},e=function(a){return d(function(b){b(a)})};return{nu:d,pure:e}}),g("a7",["u","1j"],function(a,b){var c=function(c){return function(){var d=a.prototype.slice.call(arguments),e=this;b(function(){c.apply(e,d)},0)}};return{bounce:c}}),g("9t",["9w","a7"],function(a,b){var c=function(d){var e=function(a){d(b.bounce(a))},f=function(a){return c(function(b){e(function(c){var d=a(c);b(d)})})},g=function(a){return c(function(b){e(function(c){a(c).get(b)})})},h=function(a){return c(function(b){e(function(c){a.get(b)})})},i=function(){return a.nu(e)};return{map:f,bind:g,anonBind:h,toLazy:i,get:e}},d=function(a){return c(function(b){b(a)})};return{nu:c,pure:d}}),g("9u",["z","1v","1w","1x"],function(a,b,c,d){var e=function(b,d,e){return c.abs(b-d)<=e?a.none():b<d?a.some(b+e):a.some(b-e)},f=function(){var a=null,f=function(f,g,h,i,j,k){var l=!1,m=function(a){l=!0,j(a)};b(a);var n=function(c){b(a),m(c)};a=d(function(){var d=f();e(d,g,h).fold(function(){b(a),m(g)},function(e){if(i(e,n),!l){var h=f();(h!==e||c.abs(h-g)>c.abs(d-g))&&(b(a),m(g))}})},k)};return{animate:f}};return{create:f,adjust:e}}),g("a8",["z","15"],function(a,b){var c=function(c,d){var e=[{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}];return b.findMap(e,function(b){return c<=b.width&&d<=b.height?a.some(b.keyboard):a.none()}).getOr({portrait:d/5,landscape:c/4})};return{findDevice:c}}),g("9v",["39","10","8a","a8","j"],function(a,b,c,d,e){var f=function(a){return d.findDevice(a.screen.width,a.screen.height)},g=function(a){var b=e.get(a).isPortrait(),c=f(a),d=b?c.portrait:c.landscape,g=b?a.screen.height:a.screen.width;return g-a.innerHeight>d?0:d},h=function(a,d){var e=b.owner(a).dom().defaultView,f=c.get(a)+c.get(d),h=g(e);return f-h},i=function(b,d,e){var f=h(d,e),g=c.get(d)+c.get(e)-f;a.set(b,"padding-bottom",g+"px")};return{getGreenzone:h,updatePadding:i}}),g("8n",["6k","y","6","4i","39","5l","10","8a","9v","i","3z","83"],function(a,b,c,d,e,f,g,h,i,j,k,l){var m=a.generate([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),n="data-"+j.resolve("position-y-fixed"),o="data-"+j.resolve("y-property"),p="data-"+j.resolve("scrolling"),q="data-"+j.resolve("last-window-height"),r=function(a){return l.safeParse(a,n)},s=function(a){return d.get(a,o)},t=function(a){return l.safeParse(a,q)},u=function(a,b){var c=s(a);return m.fixed(a,c,b)},v=function(a,b){return m.scroller(a,b)},w=function(a){var b=r(a),c="true"===d.get(a,p)?v:u;return c(a,b)},x=function(a){var c=f.descendants(a,"["+n+"]");return b.map(c,w)},y=function(a){var b=d.get(a,"style");e.setAll(a,{position:"absolute",top:"0px"}),d.set(a,n,"0px"),d.set(a,o,"top");var c=function(){d.set(a,"style",b||""),d.remove(a,n),d.remove(a,o)};return{restore:c}},z=function(a,b,c){var f=d.get(c,"style");k.register(c),e.setAll(c,{position:"absolute",height:b+"px",width:"100%",top:a+"px"}),d.set(c,n,a+"px"),d.set(c,p,"true"),d.set(c,o,"top");var g=function(){k.deregister(c),d.set(c,"style",f||""),d.remove(c,n),d.remove(c,p),d.remove(c,o)};return{restore:g}},A=function(a,b,c){var f=d.get(a,"style");e.setAll(a,{position:"absolute",bottom:"0px"}),d.set(a,n,"0px"),d.set(a,o,"bottom");var g=function(){d.set(a,"style",f||""),d.remove(a,n),d.remove(a,o)};return{restore:g}},B=function(a,b,c){var e=g.owner(a).dom().defaultView,f=e.innerHeight;return d.set(a,q,f+"px"),f-b-c},C=function(a,b,f,j){var k=g.owner(a).dom().defaultView,l=y(f),m=h.get(f),o=h.get(j),p=B(a,m,o),q=z(m,p,a),r=A(j,m,p),s=!0,u=function(){s=!1,l.restore(),q.restore(),r.restore()},v=function(){var b=k.innerHeight,c=t(a);return b>c},w=function(){if(s){var c=h.get(f),g=h.get(j),k=B(a,c,g);d.set(a,n,c+"px"),e.set(a,"height",k+"px"),e.set(j,"bottom",-(c+k+g)+"px"),i.updatePadding(b,a,j)}},x=function(b){var c=b+"px";d.set(a,n,c),w()};return i.updatePadding(b,a,j),{setViewportOffset:x,isExpanding:v,isShrinking:c.not(v),refresh:w,restore:u}};return{findFixtures:x,takeover:C,getYFixedData:r}}),g("8j",["6","9t","4i","89","39","10","1w","9u","8n","i","83"],function(a,b,c,d,e,f,g,h,i,j,k){var l=h.create(),m=15,n=10,o=10,p="data-"+j.resolve("last-scroll-top"),q=function(a){var b=e.getRaw(a,"top").getOr(0);return parseInt(b,10)},r=function(a){return parseInt(a.dom().scrollTop,10)},s=function(c,d,f){return b.nu(function(b){var g=a.curry(r,c),h=function(a){c.dom().scrollTop=a,e.set(c,"top",q(c)+m+"px")},i=function(){c.dom().scrollTop=d,e.set(c,"top",f+"px"),b(d)};l.animate(g,d,m,h,i,o)})},t=function(d,e){return b.nu(function(b){var f=a.curry(r,d);c.set(d,p,f());var h=function(a,b){var e=k.safeParse(d,p);e!==d.dom().scrollTop?b(d.dom().scrollTop):(d.dom().scrollTop=a,c.set(d,p,a))},i=function(){d.dom().scrollTop=e,c.set(d,p,e),b(e)},j=g.abs(e-f()),m=g.ceil(j/n);l.animate(f,e,m,h,i,o)})},u=function(c,d){return b.nu(function(b){var f=a.curry(q,c),h=function(a){e.set(c,"top",a+"px")},i=function(){h(d),b(d)},j=g.abs(d-f()),k=g.ceil(j/n);l.animate(f,d,k,h,i,o)})},v=function(a,b){var c=b+i.getYFixedData(a)+"px";e.set(a,"top",c)},w=function(a,c,d){var e=f.owner(a).dom().defaultView;return b.nu(function(b){v(a,d),v(c,d),e.scrollTo(0,d),b(d)})};return{moveScrollAndTop:s,moveOnlyScroll:t,moveOnlyTop:u,moveWindowScroll:w}}),g("8k",["5","9w"],function(a,b){return function(c){var d=a(b.pure({})),e=function(a){var e=b.nu(function(b){return c(a).get(b)});d.set(e)},f=function(a){d.get().get(function(){a()})};return{start:e,idle:f}}}),g("8l",["6","8h","8j","9v","9s"],function(a,b,c,d,e){var f=function(b,f,g,h,i){var j=d.getGreenzone(f,g),k=a.curry(e.refresh,b);h>j||i>j?c.moveOnlyScroll(f,f.dom().scrollTop-j+i).get(k):h<0&&c.moveOnlyScroll(f,f.dom().scrollTop+h).get(k)};return{scrollIntoView:f}}),g("a9",["y"],function(a){var b=function(b,c){return c(function(c){var d=[],e=0,f=function(a){return function(f){d[a]=f,e++,e>=b.length&&c(d)}};0===b.length?c([]):a.each(b,function(a,b){a.get(f(b))})})};return{par:b}}),g("9x",["y","9t","a9"],function(a,b,c){var d=function(a){return c.par(a,b.nu)},e=function(b,c){var e=a.map(b,c);return d(e)},f=function(a,b){return function(c){return b(c).bind(a)}};return{par:d,mapM:e,compose:f}}),g("8m",["y","9t","9x","39","8j","8n"],function(a,b,c,d,e,f){var g=function(a,c,e,f){var g=e+f;return d.set(a,c,g+"px"),b.pure(f)},h=function(a,b,c){var f=b+c,g=d.getRaw(a,"top").getOr(c),h=f-parseInt(g,10),i=a.dom().scrollTop+h;return e.moveScrollAndTop(a,i,f)},i=function(a,b){return a.fold(function(a,c,d){return g(a,c,b,d)},function(a,c){return h(a,b,c)})},j=function(b,d){var e=f.findFixtures(b),g=a.map(e,function(a){return i(a,d)});return c.par(g)};return{updatePositions:j}}),g("8o",["8","9","11","a","39"],function(a,b,c,d,e){var f=function(f,g){var h=d.fromTag("input");e.setAll(h,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),b.append(f,h),a.focus(h),g(h),c.remove(h)};return{input:f}}),g("6g",["6","z","62","8","1u","12","a","39","1v","86","16","1w","8h","1x","1j","8i","8j","8k","8l","8m","8n","j","8o","84"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x){var y=5,z=function(d,f,h,i,j,k){var l=r(function(a){return q.moveWindowScroll(d,f,a)}),m=function(){var c=x.getRectangles(k);return b.from(c[0]).bind(function(c){var d=c.top()-f.dom().scrollTop,e=d>i.innerHeight+y||d<-y;return e?b.some({top:a.constant(d),bottom:a.constant(d+c.height())}):b.none()})},n=c.last(function(){l.idle(function(){t.updatePositions(h,i.pageYOffset).get(function(){var a=m();a.each(function(a){f.dom().scrollTop=f.dom().scrollTop+a.top()}),l.start(0),j.refresh()})})},1e3),o=e.bind(g.fromDom(i),"scroll",function(){i.pageYOffset<0||n.throttle()});return t.updatePositions(h,i.pageYOffset).get(a.identity),{unbind:o.unbind}},A=function(b){var c=b.cWin(),i=b.ceBody(),j=b.socket(),k=b.toolstrip(),l=b.toolbar(),m=b.contentElement(),n=b.keyboardType(),o=b.outerWindow(),r=b.dropup(),t=u.takeover(j,i,k,r),x=n(b.outerBody(),c,f.body(),m,k,l),y=function(){x.toEditing(),I()},A=function(){x.toReading()},B=function(a){x.onToolbarTouch(a)},C=v.onChange(o,{onChange:a.noop,onReady:t.refresh});C.onAdjustment(function(){t.refresh()});var D=e.bind(g.fromDom(o),"resize",function(){t.isExpanding()&&t.refresh()}),E=z(k,j,b.outerBody(),o,t,c),F=p(c,m),G=function(){F.isActive()&&F.update()},H=function(){F.update()},I=function(){F.clear()},J=function(a,b){s.scrollIntoView(c,j,r,a,b)},K=function(){h.set(m,"height",m.dom().contentWindow.document.body.scrollHeight+"px")},L=function(b){t.setViewportOffset(b),q.moveOnlyTop(j,b).get(a.identity)},M=function(){t.restore(),C.destroy(),E.unbind(),D.unbind(),x.destroy(),F.destroy(),w.input(f.body(),d.blur)};return{toEditing:y,toReading:A,onToolbarTouch:B,refreshSelection:G,clearSelection:I,highlightSelection:H,scrollIntoView:J,updateToolbarPadding:a.noop,setViewportOffset:L,syncHeight:K,refreshStructure:t.refresh,destroy:M}};return{setup:A}}),g("6h",["y","6","8","1u","12","b","8p","8o"],function(a,b,c,d,e,f,g,h){
var i=function(b,e,i,j){var k=function(){g.resume(e,j)},l=function(){h.input(b,c.blur)},m=d.bind(i,"keydown",function(b){a.contains(["input","textarea"],f.name(b.target()))||k()}),n=function(){},o=function(){m.unbind()};return{toReading:l,toEditing:k,onToolbarTouch:n,destroy:o}},j=function(a,d,e,f){var h=function(){c.blur(f)},i=function(){h()},j=function(){h()},k=function(){g.resume(d,f)};return{toReading:j,toEditing:k,onToolbarTouch:i,destroy:b.noop}};return{stubborn:i,timid:j}}),g("42",["6","1z","2o","8","a","2f","39","1b","6f","6g","5z","3y","6h","60","i","3z","61"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q){var r=function(r,s){var t=q.tag(),u=b.value(),v=b.value(),w=b.api(),x=b.api(),y=function(){s.hide();var b=e.fromDom(h);k.getActiveApi(r.editor).each(function(e){u.set({socketHeight:g.getRaw(r.socket,"height"),iframeHeight:g.getRaw(e.frame(),"height"),outerScroll:h.body.scrollTop}),v.set({exclusives:l.exclusive(b,"."+p.scrollable())}),f.add(r.container,o.resolve("fullscreen-maximized")),n.clobberStyles(r.container,e.body()),t.maximize(),g.set(r.socket,"overflow","scroll"),g.set(r.socket,"-webkit-overflow-scrolling","touch"),d.focus(e.body());var k=c.immutableBag(["cWin","ceBody","socket","toolstrip","toolbar","dropup","contentElement","cursor","keyboardType","isScrolling","outerWindow","outerBody"],[]);w.set(j.setup(k({cWin:e.win(),ceBody:e.body(),socket:r.socket,toolstrip:r.toolstrip,toolbar:r.toolbar,dropup:r.dropup.element(),contentElement:e.frame(),cursor:a.noop,outerBody:r.body,outerWindow:r.win,keyboardType:m.stubborn,isScrolling:function(){return v.get().exists(function(a){return a.socket.isScrolling()})}}))),w.run(function(a){a.syncHeight()}),x.set(i.initEvents(e,w,r.toolstrip,r.socket,r.dropup))})},z=function(){t.restore(),x.clear(),w.clear(),s.show(),u.on(function(a){a.socketHeight.each(function(a){g.set(r.socket,"height",a)}),a.iframeHeight.each(function(a){g.set(r.editor.getFrame(),"height",a)}),h.body.scrollTop=a.scrollTop}),u.clear(),v.on(function(a){a.exclusives.unbind()}),v.clear(),f.remove(r.container,o.resolve("fullscreen-maximized")),n.restoreStyles(),p.deregister(r.toolbar),g.remove(r.socket,"overflow"),g.remove(r.socket,"-webkit-overflow-scrolling"),d.blur(r.editor.getFrame()),k.getActiveApi(r.editor).each(function(a){a.clearSelection()})},A=function(){w.run(function(a){a.refreshStructure()})};return{enter:y,refreshStructure:A,exit:z}};return{create:r}}),g("25",["3q","2e","6","39","3s","42","3t"],function(a,b,c,d,e,f,g){var h=function(h){var i=b.asRawOrDie("Getting IosWebapp schema",e,h);d.set(i.toolstrip,"width","100%"),d.set(i.container,"position","relative");var j=function(){i.setReadOnly(!0),m.enter()},k=a.build(g.sketch(j,i.translate));i.alloy.add(k);var l={show:function(){i.alloy.add(k)},hide:function(){i.alloy.remove(k)}},m=f.create(i,l);return{setReadOnly:i.setReadOnly,refreshStructure:m.refreshStructure,enter:m.enter,exit:m.exit,destroy:c.noop}};return{produce:h}}),g("m",["1y","6","1z","25","i","21","22","23","24"],function(a,b,c,d,e,f,g,h,i){return function(j){var k=i({classes:[e.resolve("ios-container")]}),l=f(),m=c.api(),n=g.makeEditSwitch(m),o=g.makeSocket(),p=h.build(function(){m.run(function(a){a.refreshStructure()})},j);k.add(l.wrapper()),k.add(o),k.add(p.component());var q=function(a){var b=l.createGroups(a);l.setGroups(b)},r=function(a){var b=l.createGroups(a);l.setContextToolbar(b)},s=function(){l.focus()},t=function(){l.restoreToolbar()},u=function(a){m.set(d.produce(a))},v=function(){m.run(function(b){a.remove(o,n),b.exit()})},w=function(a){g.updateMode(o,n,a,k.root())};return{system:b.constant(k),element:k.element,init:u,exit:v,setToolbarGroups:q,setContextToolbar:r,focusToolbar:s,restoreToolbar:t,updateMode:w,socket:b.constant(o),dropup:b.constant(p)}}}),g("26",["1e"],function(a){return a("tinymce.EditorManager")}),g("n",["14","26"],function(a,b){var c=function(c){var d=a.readOptFrom(c.settings,"skin_url").fold(function(){return b.baseURL+"/skins/lightgray"},function(a){return a});return{content:d+"/content.mobile.min.css",ui:d+"/skin.mobile.min.css"}};return{derive:c}}),g("o",["y","6","x","g"],function(a,b,c,d){var e=["x-small","small","medium","large","x-large"],f=function(a,b,c){a.system().broadcastOn([d.formatChanged()],{command:b,state:c})},g=function(b,d){var e=c.keys(d.formatter.get());a.each(e,function(a){d.formatter.formatChanged(a,function(c){f(b,a,c)})}),a.each(["ul","ol"],function(a){d.selection.selectorChanged(a,function(c,d){f(b,a,c)})})};return{init:g,fontSizes:b.constant(e)}}),g("p",[],function(){var a=function(a){var b=function(){a._skinLoaded=!0,a.fire("SkinLoaded")};return function(){a.initialized?b():a.on("init",b)}};return{fireSkinLoaded:a}}),g("0",["1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y){var z=f.constant("toReading"),A=f.constant("toEditing");return l.add("mobile",function(l){var B=function(B){var C=w.derive(l);o.isSkinDisabled(l)===!1?(l.contentCSS.push(C.content),m.DOM.styleSheetLoader.load(C.ui,y.fireSkinLoaded(l))):y.fireSkinLoaded(l)();var D=function(){l.fire("scrollIntoView")},E=j.fromTag("div"),F=g.detect().os.isAndroid()?t(D):v(D),G=j.fromDom(B.targetNode);i.after(G,E),c.attachSystem(E,F.system());var H=function(a){return h.search(a).bind(function(a){return F.system().getByDom(a).toOption()})},I=B.targetNode.ownerDocument.defaultView,J=s.onChange(I,{onChange:function(){var a=F.system();a.broadcastOn([p.orientationChanged()],{width:s.getActualWidth(I)})},onReady:f.noop}),K=function(a,b,c){c===!1&&l.selection.collapse(),F.setToolbarGroups(c?a.get():b.get()),l.setMode(c===!0?"readonly":"design"),l.fire(c===!0?z():A()),F.updateMode(c)},L=function(a,b){return l.on(a,b),{unbind:function(){l.off(a)}}};return l.on("init",function(){F.init({editor:{getFrame:function(){return j.fromDom(l.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:f.noop}},onToReading:function(a){return L(z(),a)},onToEditing:function(a){return L(A(),a)},onScrollToCursor:function(a){l.on("scrollIntoView",function(b){a(b)});var b=function(){l.off("scrollIntoView"),J.destroy()};return{unbind:b}},onTouchToolstrip:function(){c()},onTouchContent:function(){var a=j.fromDom(l.editorContainer.querySelector("."+r.resolve("toolbar")));H(a).each(b.emitExecute),F.restoreToolbar(),c()},onTapContent:function(b){var c=b.target();if("img"===k.name(c))l.selection.select(c.dom()),b.kill();else if("a"===k.name(c)){var d=F.system().getByDom(j.fromDom(l.editorContainer));d.each(function(b){a.isAlpha(b)&&n.openLink(c.dom())})}}},container:j.fromDom(l.editorContainer),socket:j.fromDom(l.contentAreaContainer),toolstrip:j.fromDom(l.editorContainer.querySelector("."+r.resolve("toolstrip"))),toolbar:j.fromDom(l.editorContainer.querySelector("."+r.resolve("toolbar"))),dropup:F.dropup(),alloy:F.system(),translate:f.noop,setReadOnly:function(a){K(w,v,a)}});var c=function(){F.dropup().disappear(function(){F.system().broadcastOn([p.dropupDismissed()],{})})};d.registerInspector("remove this",F.system());var g={label:"The first group",scrollable:!1,items:[u.forToolbar("back",function(){l.selection.collapse(),F.exit()},{})]},h={label:"Back to read only",scrollable:!1,items:[u.forToolbar("readonly-back",function(){K(w,v,!0)},{})]},i={label:"The read only mode group",scrollable:!0,items:[]},m=q.setup(F,l),o=q.detect(l.settings,m),s={label:"the action group",scrollable:!0,items:o},t={label:"The extra group",scrollable:!1,items:[]},v=e([h,s,t]),w=e([g,i,t]);x.init(F,l)}),{iframeContainer:F.socket().element().dom(),editorContainer:F.element().dom()}};return{getNotificationManagerImpl:function(){return{open:f.identity,close:f.noop,reposition:f.noop,getArgs:f.identity}},renderUI:B}}),function(){}}),d("0")()}();