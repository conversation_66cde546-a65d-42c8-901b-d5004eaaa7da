var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var AsyncJsonTextStyleRow = /** @class */ (function (_super) {
    __extends(AsyncJsonTextStyleRow, _super);
    function AsyncJsonTextStyleRow(props) {
        var _this = _super.call(this, props) || this;
        //['selected', 'drilldown', 'name', 'fontname', 'font', 'pointSize', 'bold', 'italic', 'underline', 'color', 'connectorName', 'taggingOverride', 'delete']
        _this.KEY_SELECTED = 'selected';
        _this.KEY_DRILLDOWN = 'drilldown';
        _this.KEY_NAME = 'name';
        _this.KEY_FONTNAME = 'fontName';
        _this.KEY_FONT = 'font';
        _this.KEY_POINTSIZE = 'pointSize';
        _this.KEY_BOLD = 'bold';
        _this.KEY_ITALIC = 'italic';
        _this.KEY_UNDERLINE = 'underline';
        _this.KEY_COLOR = 'color';
        _this.KEY_CONNECTORNAME = 'connectorName';
        _this.KEY_TAGGING_OVERRIDE = 'taggingOverride';
        _this.KEY_STYLE_SET_DEFAULTS = 'styleSetDefaults';
        _this.KEY_DELETE = 'delete';
        _this.fonts = [];
        _this._isMounted = null;
        _this.tableCellMethods = {};
        _this.myRef = null;
        var stateObj = {
            selected: props.asyncJsonTable.isRowSelected(props.rowData.id != null ? props.rowData.id : props.rowData._instanceGuid),
            manualEdit: false,
            editState: {
                name: props.rowData._instanceGuid === TextStyleList.currentAddEditGuid,
                isHovered: false,
                fontName: false,
                connectorName: false,
                webFontSelect: false
            },
            textStyle: props.rowData,
            fonts: null,
            showDrillDown: false,
            showDefaultValueChanged: false
        };
        TextStyleList.getFontList().then(function (fonts) {
            if (_this._isMounted !== false) {
                _this.setState(function (prevState) {
                    var newStyle = prevState.textStyle;
                    if (prevState.editState.webFontSelect) {
                        if (newStyle.textStyleFont === null && fonts != null && fonts.length > 0) {
                            newStyle.textStyleFont = fonts[0];
                        }
                    }
                    return {
                        textStyle: newStyle,
                        fonts: fonts
                    };
                });
            }
        });
        if (props.rowData.textStyleFont !== null) {
            stateObj.editState.webFontSelect = true;
        }
        _this.state = stateObj;
        _this.initializeTableCellMethods();
        _this.bindClassMethods();
        _this.myRef = React.createRef();
        if (TextStyleList.asyncJsonTableInstance === null) {
            TextStyleList.asyncJsonTableInstance = props.asyncJsonTable;
        }
        return _this;
    }
    AsyncJsonTextStyleRow.prototype.initializeTableCellMethods = function () {
        var _this = this;
        this.tableCellMethods[this.KEY_SELECTED] = this.getSelectedCell;
        this.tableCellMethods[this.KEY_DRILLDOWN] = this.getDrilldownCell;
        this.tableCellMethods[this.KEY_NAME] = this.getNameCell;
        this.tableCellMethods[this.KEY_FONTNAME] = this.getFontNameCell;
        this.tableCellMethods[this.KEY_FONT] = this.getFontCell;
        this.tableCellMethods[this.KEY_POINTSIZE] = this.getPointSizeCell;
        this.tableCellMethods[this.KEY_BOLD] = this.getBoldCell;
        this.tableCellMethods[this.KEY_ITALIC] = this.getItalicCell;
        this.tableCellMethods[this.KEY_UNDERLINE] = this.getUnderlineCell;
        this.tableCellMethods[this.KEY_COLOR] = this.getColorCell;
        this.tableCellMethods[this.KEY_CONNECTORNAME] = this.getConnectorName;
        this.tableCellMethods[this.KEY_TAGGING_OVERRIDE] = this.getTaggingOverrideCell;
        this.tableCellMethods[this.KEY_STYLE_SET_DEFAULTS] = this.getStyleSetDefaultsCell;
        this.tableCellMethods[this.KEY_DELETE] = this.getDeleteCell;
        Object.keys(this.tableCellMethods).forEach(function (x) { return _this.tableCellMethods[x] = _this.tableCellMethods[x].bind(_this); });
    };
    AsyncJsonTextStyleRow.prototype.bindClassMethods = function () {
        this.deleteTextStyle = this.deleteTextStyle.bind(this);
        this.onConnectorNameChange = this.onConnectorNameChange.bind(this);
        this.onFontNameChange = this.onFontNameChange.bind(this);
        this.onInputKeyPress = this.onInputKeyPress.bind(this);
        this.onNameChange = this.onNameChange.bind(this);
        this.onNonEditToggleCellFocus = this.onNonEditToggleCellFocus.bind(this);
        this.onPointSizeChange = this.onPointSizeChange.bind(this);
        this.onSelectChanged = this.onSelectChanged.bind(this);
        this.onTableRowClick = this.onTableRowClick.bind(this);
        this.onTextStyleFontChange = this.onTextStyleFontChange.bind(this);
        this.onToggleState = this.onToggleState.bind(this);
        this.onToggleWebfontSelect = this.onToggleWebfontSelect.bind(this);
        this.showTaggingOverrideModal = this.showTaggingOverrideModal.bind(this);
        this.showStyleSetDefaultsModal = this.showStyleSetDefaultsModal.bind(this);
        this.toggleCheckbox = this.toggleCheckbox.bind(this);
        this.toggleDrillDown = this.toggleDrillDown.bind(this);
        this.toggleMultipleColor = this.toggleMultipleColor.bind(this);
        this.togglePointSizeState = this.togglePointSizeState.bind(this);
        this.onRowBlur = this.onRowBlur.bind(this);
    };
    AsyncJsonTextStyleRow.prototype.componentDidMount = function () {
        this.props.asyncJsonTable.registerTableRow(this);
        this._isMounted = true;
    };
    AsyncJsonTextStyleRow.prototype.componentWillUnmount = function () {
        this.props.asyncJsonTable.unregisterTableRow(this);
        this._isMounted = false;
    };
    AsyncJsonTextStyleRow.prototype.componentDidUpdate = function (prevProps, prevState, snapshot) {
        var _this = this;
        if (this.state.manualEdit) {
            return;
        }
        var hasFocusUpdate = Object.keys(prevState.editState)
            .map(function (key) { return prevState.editState[key] === _this.state.editState[key]; })
            .indexOf(false) !== -1;
        if (!hasFocusUpdate) {
            if (this.props.rowData._instanceGuid === TextStyleList.currentAddEditGuid && this.state.editState.name) {
                document.getElementById("textStyleName_".concat(this.state.textStyle.id)).focus();
            }
            return;
        }
        if (this.state.editState.name) {
            document.getElementById("textStyleName_".concat(this.state.textStyle.id)).focus();
        }
        else if (this.state.editState.fontName) {
            document.getElementById("textStyleFontName_".concat(this.state.textStyle.id)).focus();
        }
        else if (this.state.editState.connectorName) {
            document.getElementById("textStyleConnectorName_".concat(this.state.textStyle.id)).focus();
        }
    };
    AsyncJsonTextStyleRow.prototype.render = function () {
        var _this = this;
        var tableCells = [this.KEY_SELECTED,
            this.KEY_DRILLDOWN,
            this.KEY_NAME,
            this.KEY_FONTNAME,
            this.KEY_FONT,
            this.KEY_POINTSIZE,
            this.KEY_BOLD,
            this.KEY_ITALIC,
            this.KEY_UNDERLINE,
            this.KEY_COLOR,
            this.KEY_CONNECTORNAME,
            this.KEY_TAGGING_OVERRIDE,
            this.KEY_STYLE_SET_DEFAULTS,
            this.KEY_DELETE
        ];
        if (!TextStyleList.isLicensedForGMC()) {
            tableCells = tableCells.filter(function (x) { return x !== _this.KEY_TAGGING_OVERRIDE; });
        }
        var tableCellImplementations = tableCells.map(function (key) { return _this.tableCellMethods[key](); });
        return ([
            React.createElement("tr", { onBlur: this.onRowBlur, key: this.state.textStyle.id, onMouseEnter: function () { return _this.setHovered(true); }, onMouseLeave: function () { return _this.setHovered(false); }, style: { opacity: TextStyleList.isDeleted(this.state.textStyle.id) ? .50 : 1 } }, tableCellImplementations),
            (this.state.showDrillDown &&
                React.createElement("tr", { key: "".concat(this.state.textStyle.id, "_drill-down") },
                    React.createElement("td", { colSpan: tableCells.length },
                        React.createElement("iframe", { src: "".concat(context, "/content/text_style_list_detail.form?textStyleId=").concat(this.state.textStyle.id, "&iframetarget=1&tk=").concat(gup('tk')), width: "100%", onLoad: window.adjustIFrameHeight, style: { height: '150px', border: 0, overflow: 'hidden' } }))))
        ]);
    };
    AsyncJsonTextStyleRow.acceptNumericInput = function (e) {
        var result = (e.charCode >= 48 && e.charCode <= 57) || e.charCode == 46;
        if (!result) {
            e.preventDefault();
            return false;
        }
        return true;
    };
    AsyncJsonTextStyleRow.prototype.onNameChange = function (event) {
        var newValue = event.target.value;
        this.setState(function (prevState) {
            var updateTextStyle = prevState.textStyle;
            updateTextStyle.name = newValue;
            return {
                textStyle: updateTextStyle
            };
        }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.onEditableToggleCellFocus = function (key) {
        if (this.state.manualEdit) {
            return;
        }
        this.setState(function (prevState) {
            var editState = {
                name: false,
                fontName: false,
                connectorName: false,
                isHovered: prevState.editState.isHovered,
                webFontSelect: prevState.editState.webFontSelect
            };
            editState[key] = true;
            return {
                editState: editState
            };
        }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.onNonEditToggleCellFocus = function () {
        if (this.state.manualEdit) {
            return;
        }
        this.setState(function (prevState) { return ({
            editState: {
                name: false,
                fontName: false,
                connectorName: false,
                isHovered: prevState.editState.isHovered,
                webFontSelect: prevState.editState.webFontSelect
            }
        }); }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.onTextStyleFontChange = function (event) {
        var value = event.target.value;
        this.setState(function (prevState) {
            prevState.textStyle.webFontName = value;
            return prevState;
        }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.onSelectChanged = function (event) {
        if (!this.state.selected) {
            this.props.asyncJsonTable.setSelectedSelectionState(this.getId(), true);
        }
        else {
            this.props.asyncJsonTable.setSelectedSelectionState(this.getId(), false);
        }
    };
    AsyncJsonTextStyleRow.prototype.onTableRowClick = function (event) {
        if (!this.state.selected) {
            this.props.asyncJsonTable.setSelectedSelectionState(this.getId(), true);
        }
    };
    AsyncJsonTextStyleRow.prototype.setSelected = function (isSelected) {
        this.setState(function (prevState) {
            var editState = prevState.editState;
            if (!isSelected) {
                editState.name = false;
                editState.fontName = false;
                editState.connectorName = false;
            }
            return {
                selected: isSelected,
                manualEdit: false,
                editState: editState
            };
        }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.delete = function () {
        this.props.asyncJsonTable.setSelectedSelectionState(this.getId(), false);
        this.deleteTextStyle();
    };
    AsyncJsonTextStyleRow.prototype.isSelected = function () {
        return this.state.selected;
    };
    AsyncJsonTextStyleRow.prototype.getId = function () {
        return this.state.textStyle.id != null ? this.state.textStyle.id : this.state.textStyle._instanceGuid;
    };
    AsyncJsonTextStyleRow.prototype.onFontNameChange = function (event) {
        var newValue = event.target.value;
        this.setState(function (prevState) {
            var updateTextStyle = prevState.textStyle;
            updateTextStyle.fontName = newValue;
            return {
                textStyle: updateTextStyle
            };
        }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.onConnectorNameChange = function (event) {
        var newValue = event.target.value;
        this.setState(function (prevState) {
            var updateTextStyle = prevState.textStyle;
            updateTextStyle.connectorName = newValue;
            return {
                textStyle: updateTextStyle
            };
        }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.onInputKeyPress = function (event, target) {
        var _this = this;
        event.stopPropagation();
        if (event.key === 'Enter') {
            this.setState(function (prevState) { return ({
                editState: {
                    name: false,
                    fontName: false,
                    connectorName: false,
                    isHovered: prevState.editState.isHovered,
                    webFontSelect: prevState.editState.webFontSelect
                }
            }); }, function () {
                _this.afterSetState();
            });
        }
    };
    AsyncJsonTextStyleRow.prototype.onFocusableKeyPress = function (event, control) {
        if (event.key === 'Enter') {
            this.setState(function (prevState) {
                var newState = prevState;
                var newEditState = {
                    name: false,
                    fontName: false,
                    connectorName: false,
                    isHovered: prevState.editState.isHovered,
                    webFontSelect: prevState.editState.webFontSelect
                };
                newEditState[control] = true;
                return {
                    editState: newEditState
                };
            }, this.afterSetState);
        }
    };
    AsyncJsonTextStyleRow.prototype.onToggleWebfontSelect = function () {
        var _this = this;
        this.setState(function (prevState) {
            var state = prevState;
            if (state.fonts.length) {
                state.editState.webFontSelect = !prevState.editState.webFontSelect;
                state.textStyle.applyTextStyleFont = !state.textStyle.applyTextStyleFont;
                if (!state.textStyle.applyTextStyleFont) {
                    state.textStyle.textStyleFont = null;
                }
            }
            return state;
        }, function () {
            if (_this.state.textStyle.textStyleFont === null && _this.state.fonts.length > 0) {
                TextStyleList.getFontList().then(function (x) { return _this.setState(function (prevState) {
                    var state = prevState;
                    state.textStyle.textStyleFont = x[0];
                    return state;
                }); }, _this.afterSetState);
            }
            if (_this.state.fonts.length > 0) {
                _this.afterSetState();
            }
        });
    };
    AsyncJsonTextStyleRow.prototype.toggleCheckbox = function (evt, source) {
        var checked = evt.target.checked;
        this.setState(function (prevState) {
            var newState = {
                textStyle: prevState.textStyle
            };
            newState.textStyle[source] = checked;
            return newState;
        }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.onToggleState = function (evt, source) {
        this.setState(function (prevState) {
            var state = {
                textStyle: prevState.textStyle
            };
            state.textStyle[source] = !prevState.textStyle[source];
            return state;
        }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.updateStyleSetDefaults = function (textStyle) {
        if (!textStyle.styleSetDefaults) {
            return;
        }
        var updatedStyle = JSON.parse(textStyle.styleSetDefaults);
        var defaultValueChanged = false;
        if (!textStyle.togglePointSize && updatedStyle.pointSize !== textStyle.pointSize) {
            updatedStyle.pointSize = textStyle.pointSize;
            defaultValueChanged = true;
        }
        else if (textStyle.togglePointSize && !textStyle.togglePointSizeValues.includes(updatedStyle.pointSize)) {
            updatedStyle.pointSize = textStyle.togglePointSizeValues[0];
            defaultValueChanged = true;
        }
        if (!textStyle.toggleBold && updatedStyle.bold !== textStyle.bold) {
            updatedStyle.bold = textStyle.bold;
            defaultValueChanged = true;
        }
        if (!textStyle.toggleItalic && updatedStyle.italic !== textStyle.italic) {
            updatedStyle.italic = textStyle.italic;
            defaultValueChanged = true;
        }
        if (!textStyle.toggleUnderline && updatedStyle.underline !== textStyle.underline) {
            updatedStyle.underline = textStyle.underline;
            defaultValueChanged = true;
        }
        if (!textStyle.toggleColor && updatedStyle.color !== textStyle.color) {
            updatedStyle.color = textStyle.color;
            defaultValueChanged = true;
        }
        else if (textStyle.toggleColor && !textStyle.toggleColorValues.includes(updatedStyle.color)) {
            updatedStyle.color = textStyle.toggleColorValues[0];
            defaultValueChanged = true;
        }
        textStyle.styleSetDefaults = JSON.stringify(updatedStyle);
        if (defaultValueChanged) {
            this.setState(function (prevState) {
                return Object.assign({}, prevState, {
                    showDefaultValueChanged: true
                });
            });
        }
    };
    AsyncJsonTextStyleRow.prototype.afterSetState = function () {
        this.updateStyleSetDefaults(this.state.textStyle);
        TextStyleList.setUpdatedTextStyle(this.state.textStyle);
    };
    AsyncJsonTextStyleRow.prototype.onFontSelectChange = function (evt, font) {
        this.setState(function (prevState) {
            var textStyle = prevState.textStyle;
            textStyle.applyTextStyleFont = true;
            textStyle.textStyleFont = font;
            return {
                textStyle: textStyle
            };
        }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.setHovered = function (hoverState) {
        this.setState(function (prevState) {
            var state = prevState;
            state.editState.isHovered = hoverState;
            return state;
        });
    };
    AsyncJsonTextStyleRow.prototype.deleteTextStyle = function () {
        TextStyleList.deleteTextStyle(this.state.textStyle.id);
        this.forceUpdate();
    };
    AsyncJsonTextStyleRow.isWhite = function (color) {
        var hexColorRegex = /^([0-9A-F]{3}){1,2}$/i;
        if (color.startsWith('cmyk(')) {
            var cmyk = color.substring(5, color.length - 1).split(';').map(Number);
            return cmyk.length === 4 && cmyk.every(function (value) { return value === 0; });
        }
        else if (hexColorRegex.test(color)) {
            var testColor = (color || '').toLowerCase();
            return testColor.localeCompare('fff') === 0 || testColor.localeCompare('ffffff') === 0;
        }
    };
    AsyncJsonTextStyleRow.hexToRgb = function (hex) {
        var cleanHex = hex.replace(/[^0-9A-Fa-f]/g, '');
        if (cleanHex.length === 3) {
            var r = parseInt(cleanHex[0], 16);
            var g = parseInt(cleanHex[1], 16);
            var b = parseInt(cleanHex[2], 16);
            return { r: r, g: g, b: b };
        }
        else if (cleanHex.length === 6) {
            var r = parseInt(cleanHex.slice(0, 2), 16);
            var g = parseInt(cleanHex.slice(2, 4), 16);
            var b = parseInt(cleanHex.slice(4, 6), 16);
            return { r: r, g: g, b: b };
        }
        return null;
    };
    AsyncJsonTextStyleRow.cmykToRgb = function (c, m, y, k) {
        c = c / 100;
        m = m / 100;
        y = y / 100;
        k = k / 100;
        var r = Math.round(255 * (1 - c) * (1 - k));
        var g = Math.round(255 * (1 - m) * (1 - k));
        var b = Math.round(255 * (1 - y) * (1 - k));
        return { r: r, g: g, b: b };
    };
    AsyncJsonTextStyleRow.colorToRGB = function (color) {
        var hexColorRegex = /^([0-9A-F]{3}){1,2}$/i;
        if (color.startsWith('cmyk(')) {
            var cmyk = color.substring(5, color.length - 1).split(';').map(Number);
            var rgb = this.cmykToRgb(cmyk[0], cmyk[1], cmyk[2], cmyk[3]);
            return "rgb(".concat(rgb.r, ", ").concat(rgb.g, ", ").concat(rgb.b, ")");
        }
        else if (hexColorRegex.test(color)) {
            var rgb = this.hexToRgb(color);
            if (rgb) {
                return "rgb(".concat(rgb.r, ", ").concat(rgb.g, ", ").concat(rgb.b, ")");
            }
        }
        return color;
    };
    AsyncJsonTextStyleRow.prototype.getSelectedCell = function () {
        var _this = this;
        return (React.createElement("td", { onFocusCapture: this.onNonEditToggleCellFocus, key: "".concat(this.state.textStyle.id, "_checkbox"), style: { verticalAlign: 'middle' } },
            React.createElement("div", { className: "custom-control custom-checkbox no-text" },
                React.createElement("input", { type: "checkbox", id: "textStyleSelected_".concat(this.state.textStyle.id), className: "custom-control-input", checked: this.state.selected, onChange: function (evt) { return _this.onSelectChanged(evt); } }),
                React.createElement("label", { id: "textStyleSelect_".concat(this.state.textStyle.id), className: "custom-control-label py-2", htmlFor: "textStyleSelected_".concat(this.state.textStyle.id) },
                    React.createElement("span", { className: "sr-only" }, "Select")))));
    };
    AsyncJsonTextStyleRow.prototype.getDrilldownCell = function () {
        return (React.createElement("td", { onFocusCapture: this.onNonEditToggleCellFocus, key: "".concat(this.state.textStyle.id, "_drilldown") },
            React.createElement("button", { onClick: this.toggleDrillDown, type: "button", style: { lineHeight: 1 }, "aria-label": "Toggle details", className: "btn btn-lg bg-transparent border-0 p-0" },
                React.createElement("i", { className: "drill-down-icon far fa-angle-right drill-down-open-icon" }))));
    };
    AsyncJsonTextStyleRow.prototype.getNameCell = function () {
        var _this = this;
        return (React.createElement("td", { key: "".concat(this.state.textStyle.id, "_name"), onClick: function () { return _this.onEditableToggleCellFocus('name'); }, id: "textStyle_".concat(this.state.textStyle.id, "_name"), onKeyPress: function (evt) { return _this.onFocusableKeyPress(evt, 'name'); }, style: AsyncJsonTextStyleRow.ellipsisOverflowCss }, !this.state.editState.name ? (React.createElement("span", { onFocusCapture: function () { return _this.onEditableToggleCellFocus('name'); }, tabIndex: 0 },
            this.state.textStyle.name,
            " ")) : (React.createElement("input", { type: "text", id: "textStyleName_".concat(this.state.textStyle.id), className: "form-control form-control-sm w-auto", value: this.state.textStyle.name, onChange: this.onNameChange, onKeyPress: function (evt) { return _this.onInputKeyPress(evt, 'name'); }, style: { fontSize: 'inherit' }, size: 18 }))));
    };
    AsyncJsonTextStyleRow.prototype.getFontNameCell = function () {
        var _this = this;
        return (React.createElement("td", { id: "textStyle_".concat(this.state.textStyle.id, "_fontName"), onClick: function () { return _this.onEditableToggleCellFocus('fontName'); }, onKeyPress: function (evt) { return _this.onFocusableKeyPress(evt, 'fontName'); }, key: "".concat(this.state.textStyle.id, "_fontName"), style: AsyncJsonTextStyleRow.ellipsisOverflowCss }, !this.state.editState.fontName ? (React.createElement("span", { onFocusCapture: function () { return _this.onEditableToggleCellFocus('fontName'); }, tabIndex: 0 }, this.state.textStyle.fontName)) : (React.createElement("input", { type: "text", id: "textStyleFontName_".concat(this.state.textStyle.id), className: "form-control form-control-sm w-auto", value: (this.state.textStyle.fontName || ''), onChange: this.onFontNameChange, onKeyPress: function (evt) { return _this.onInputKeyPress(evt, 'fontName'); }, style: { fontSize: 'inherit' }, size: 18 }))));
    };
    AsyncJsonTextStyleRow.prototype.getFontCell = function () {
        var _this = this;
        return (React.createElement("td", { onFocusCapture: this.onNonEditToggleCellFocus, key: "".concat(this.state.textStyle.id, "_font") },
            React.createElement("div", { className: "input-group input-group-sm bg-white rounded m-0" },
                !this.state.editState.webFontSelect ? (React.createElement("input", { type: "text", className: "form-control", value: (this.state.textStyle.webFontName || ''), onChange: this.onTextStyleFontChange, style: { fontSize: 'inherit' }, size: 18 })) : (React.createElement("select", { className: "form-control", key: "textStyleFont_".concat(this.state.textStyle.textStyleFont ? this.state.textStyle.textStyleFont.id : ""), value: (this.state.fonts !== null && this.state.fonts.length > 0 && this.state.textStyle.textStyleFont) ? this.state.textStyle.textStyleFont.id : -1, onChange: function (evt) { return _this.onFontSelectChange(evt, { id: parseInt(evt.target.selectedOptions[0].id), name: evt.target.selectedOptions[0].text }); } }, this.state.fonts == null ?
                    React.createElement("option", { value: -1 }, "Loading...") :
                    this.state.fonts.map(function (font) {
                        return React.createElement("option", { key: "".concat(_this.state.textStyle.id, "_font_").concat(font.id), id: font.id.toString(), value: font.id }, font.name);
                    }))),
                React.createElement("div", { className: "input-group-append" },
                    React.createElement("button", { onClick: this.onToggleWebfontSelect, className: "btn btn-outline-info", style: this.state.editState.webFontSelect ? { backgroundColor: '#2972c2' } : null, type: "button", "aria-label": "Library", "data-toggle": "tooltip", title: "", "data-original-title": "Library", disabled: this.state.fonts ? !this.state.fonts.length : true },
                        React.createElement("i", { className: "fas fa-book", style: this.state.editState.webFontSelect ? { color: '#FFF' } : null, "aria-hidden": "true" }))))));
    };
    AsyncJsonTextStyleRow.prototype.getPointSizeCell = function () {
        var _this = this;
        return (React.createElement("td", { onFocusCapture: this.onNonEditToggleCellFocus, key: "".concat(this.state.textStyle.id, "_pointSize") },
            React.createElement("div", { className: "input-group input-group-sm rounded m-0", style: { placeContent: "flex-end" } },
                this.state.textStyle.togglePointSize ?
                    (React.createElement("div", { className: "input-group-append" },
                        React.createElement("button", { className: "btn btn-outline-info", type: "button", onClick: function () { return _this.showPointSizeModal(); } },
                            React.createElement("i", { className: "fas fa-text-height", "aria-hidden": "true" }),
                            React.createElement("sub", null, (this.state.textStyle.togglePointSizeValues || []).length))))
                    :
                        (React.createElement("input", { type: "text", className: "form-control", size: 2, value: this.state.textStyle.pointSize || '', style: { fontSize: 'inherit' }, onChange: this.onPointSizeChange, onKeyPress: function (evt) { return AsyncJsonTextStyleRow.acceptNumericInput(evt); } })),
                React.createElement("div", { className: "input-group-append" },
                    React.createElement("button", { className: "btn btn-outline-info", type: "button", "aria-label": "Multiple point size", onClick: this.togglePointSizeState, style: this.state.textStyle.togglePointSize ? { backgroundColor: '#2972c2' } : null, "data-toggle": "tooltip", title: "", "data-original-title": "Multiple point size" },
                        React.createElement("i", { className: "fas fa-font fa-xs", "aria-hidden": "true", style: this.state.textStyle.togglePointSize ? { color: '#FFF' } : null }),
                        React.createElement("i", { className: "fas fa-font ml--1", "aria-hidden": "true", style: this.state.textStyle.togglePointSize ? { color: '#FFF' } : null }))))));
    };
    AsyncJsonTextStyleRow.prototype.getBoldCell = function () {
        var _this = this;
        return (React.createElement("td", { onFocusCapture: this.onNonEditToggleCellFocus, key: "".concat(this.state.textStyle.id, "_bold") },
            React.createElement("div", { className: "d-inline-block custom-control custom-checkbox no-text align-middle" },
                React.createElement("input", { type: "checkbox", id: "textStyle_".concat(this.state.textStyle.id, "_boldCheckbox"), className: "custom-control-input", defaultChecked: this.state.textStyle.bold, onChange: function (evt) { return _this.toggleCheckbox(evt, 'bold'); } }),
                React.createElement("label", { className: "custom-control-label py-2", htmlFor: "textStyle_".concat(this.state.textStyle.id, "_boldCheckbox"), hidden: this.state.textStyle.toggleBold },
                    React.createElement("span", { className: "sr-only" }, "Select"))),
            React.createElement("button", { onClick: function (evt) { return _this.onToggleState(evt, 'toggleBold'); }, className: "btn btn-blank btn-icon px-2 border-0 ml-1 text-info", type: "button", "aria-label": "Multiple color", "data-toggle": "tooltip", title: "", "data-original-title": "Multiple color" },
                React.createElement("i", { className: "fas fa-bold", "aria-hidden": "true" }))));
    };
    AsyncJsonTextStyleRow.prototype.getItalicCell = function () {
        var _this = this;
        return (React.createElement("td", { onFocusCapture: this.onNonEditToggleCellFocus, key: "".concat(this.state.textStyle.id, "_italic") },
            React.createElement("div", { className: "d-inline-block custom-control custom-checkbox no-text align-middle" },
                React.createElement("input", { type: "checkbox", id: "textStyle_".concat(this.state.textStyle.id, "_italicCheckbox"), className: "custom-control-input", defaultChecked: this.state.textStyle.italic, onChange: function (evt) { return _this.toggleCheckbox(evt, 'italic'); } }),
                React.createElement("label", { className: "custom-control-label py-2", htmlFor: "textStyle_".concat(this.state.textStyle.id, "_italicCheckbox"), hidden: this.state.textStyle.toggleItalic },
                    React.createElement("span", { className: "sr-only" }, "Select"))),
            React.createElement("button", { onClick: function (evt) { return _this.onToggleState(evt, 'toggleItalic'); }, className: "btn btn-blank btn-icon px-2 border-0 ml-1 text-info", type: "button", "aria-label": "Multiple color", "data-toggle": "tooltip", title: "", "data-original-title": "Multiple color" },
                React.createElement("i", { className: "fas fa-italic", "aria-hidden": "true" }))));
    };
    AsyncJsonTextStyleRow.prototype.getUnderlineCell = function () {
        var _this = this;
        return (React.createElement("td", { onFocusCapture: this.onNonEditToggleCellFocus, key: "".concat(this.state.textStyle.id, "_underline") },
            React.createElement("div", { className: "d-inline-block custom-control custom-checkbox no-text align-middle" },
                React.createElement("input", { type: "checkbox", id: "textStyle_".concat(this.state.textStyle.id, "_underlineCheckbox"), className: "custom-control-input", defaultChecked: this.state.textStyle.underline, onChange: function (evt) { return _this.toggleCheckbox(evt, 'underline'); } }),
                React.createElement("label", { className: "custom-control-label py-2", htmlFor: "textStyle_".concat(this.state.textStyle.id, "_underlineCheckbox"), hidden: this.state.textStyle.toggleUnderline },
                    React.createElement("span", { className: "sr-only" }, "Select"))),
            React.createElement("button", { onClick: function (evt) { return _this.onToggleState(evt, 'toggleUnderline'); }, className: "btn btn-blank btn-icon px-2 border-0 ml-1 text-info", type: "button", "aria-label": "Multiple color", "data-toggle": "tooltip", title: "", "data-original-title": "Multiple color" },
                React.createElement("i", { className: "fas fa-underline", "aria-hidden": "true" }))));
    };
    AsyncJsonTextStyleRow.prototype.getColorCell = function () {
        var _this = this;
        var textStyleId = this.state.textStyle.id != null ? this.state.textStyle.id.toString() : "";
        var modalButton = (React.createElement("button", { className: "btn btn-outline-info", type: "button", onClick: function (e) {
                _this.showColorPickerModal(_this.state.textStyle.toggleColorValues);
            } },
            React.createElement("i", { className: "fas fa-circle", "aria-hidden": "true" }),
            React.createElement("sub", null, (this.state.textStyle.toggleColorValues || []).length)));
        var onToggleDropdown = function (isOpen) {
            _this.setState(function (prevState) {
                var state = prevState;
                state.textStyle.show = isOpen;
                return state;
            });
        };
        var onColorPickerClose = function (isOpen) {
            if (!isOpen && _this.myRef && _this.myRef.current) {
                var currentColor_1 = _this.myRef.current.handleContinue();
                _this.setState(function (prevState) {
                    var state = prevState;
                    state.textStyle.show = false;
                    var cmyk = currentColor_1.cmyk().round(1).array();
                    var cmykColor = "cmyk(".concat(cmyk[0], ";").concat(cmyk[1], ";").concat(cmyk[2], ";").concat(cmyk[3], ")");
                    state.textStyle.color = cmykColor;
                    return state;
                }, _this.afterSetState);
            }
        };
        var dropdownButton = (React.createElement("div", null,
            React.createElement(ReactBootstrap.Dropdown, { id: "".concat(textStyleId, "_colorpicker"), className: "colorpicker-dropdown", show: this.state.textStyle.show, onToggle: onToggleDropdown },
                React.createElement(ReactBootstrap.Dropdown.Toggle, { className: 'btn-outline-info rounded-0 py-3' + (!AsyncJsonTextStyleRow.isWhite(this.state.textStyle.color) ? ' border-0' : ''), style: { background: "".concat(AsyncJsonTextStyleRow.colorToRGB(this.state.textStyle.color)) }, title: "" },
                    React.createElement("span", { className: "sr-only" }, AsyncJsonTextStyleRow.colorToRGB(this.state.textStyle.color))),
                React.createElement(ReactBootstrap.Dropdown.Menu, { className: "p-4" },
                    React.createElement(MpColorPicker, { ref: this.myRef, handleCancel: function () { onToggleDropdown(false); }, handleContinue: function () { onColorPickerClose(false); }, isMultiple: false, colors: this.state.textStyle.color })))));
        return (React.createElement("td", { onFocusCapture: this.onNonEditToggleCellFocus, key: "".concat(textStyleId, "_color") },
            React.createElement("div", { className: "input-group input-group-sm rounded m-0" },
                React.createElement("div", { className: "input-group-append" }, this.state.textStyle.toggleColor ? modalButton : dropdownButton),
                React.createElement("div", { className: "input-group-append" },
                    React.createElement("button", { className: "btn btn-outline-info", type: "button", "aria-label": "Multiple color", "data-toggle": "tooltip", title: "", "data-original-title": "Multiple color", onClick: this.toggleMultipleColor },
                        React.createElement("i", { className: "far fa-fill-drip fa-xs", "aria-hidden": "true" }),
                        React.createElement("i", { className: "far fa-fill-drip ml--1", "aria-hidden": "true" }))))));
    };
    AsyncJsonTextStyleRow.prototype.getConnectorName = function () {
        var _this = this;
        return (React.createElement("td", { id: "textStyle_".concat(this.state.textStyle.id, "_connectorName"), onClick: function () { return _this.onEditableToggleCellFocus('connectorName'); }, onKeyPress: function (evt) { return _this.onFocusableKeyPress(evt, 'connectorName'); }, key: "".concat(this.state.textStyle.id, "_connectorName"), style: AsyncJsonTextStyleRow.ellipsisOverflowCss }, !this.state.editState.connectorName ? (React.createElement("span", { onFocusCapture: function () { return _this.onEditableToggleCellFocus('connectorName'); }, tabIndex: 0 }, this.state.textStyle.connectorName)) : (React.createElement("input", { type: "text", id: "textStyleConnectorName_".concat(this.state.textStyle.id), className: "form-control form-control-sm w-auto", value: this.state.textStyle.connectorName, onChange: this.onConnectorNameChange, onKeyPress: function (evt) { return _this.onInputKeyPress(evt, 'connectorName'); }, style: { fontSize: 'inherit' }, size: 18 }))));
    };
    AsyncJsonTextStyleRow.prototype.getTaggingOverrideCell = function () {
        return (React.createElement("td", { onFocusCapture: this.onNonEditToggleCellFocus, key: "".concat(this.state.textStyle.id, "_taggingOverride") },
            React.createElement("button", { className: "btn btn-blank btn-icon px-2 border-0", type: "button", "aria-label": "Tagging override", onClick: this.showTaggingOverrideModal, style: !_.isEmpty(this.state.textStyle.taggingOverride) ? { backgroundColor: '#2972c2' } : null, "data-toggle": "tooltip", title: "", "data-original-title": "Tagging override" },
                React.createElement("i", { className: "far fa-code", style: !_.isEmpty(this.state.textStyle.taggingOverride) ? { color: '#ffffff' } : null, "aria-hidden": "true" }))));
    };
    AsyncJsonTextStyleRow.prototype.getDeleteCell = function () {
        return (React.createElement("td", { onFocusCapture: this.onNonEditToggleCellFocus, key: "".concat(this.state.textStyle.id, "_delete"), className: "pl-0" },
            React.createElement("button", { className: "btn btn-blank btn-icon px-2 border-0", type: "button", "aria-label": "Delete text style", hidden: !this.state.editState.isHovered, onClick: this.deleteTextStyle, "data-toggle": "tooltip", title: "", "data-original-title": "Delete" },
                React.createElement("i", { className: "far fa-trash", "aria-hidden": "true" }))));
    };
    AsyncJsonTextStyleRow.prototype.getStyleSetDefaultsCell = function () {
        var textStyle = this.state.textStyle;
        var fixedStyle = !textStyle.togglePointSize && !textStyle.toggleBold && !textStyle.toggleItalic && !textStyle.toggleUnderline && !textStyle.toggleColor;
        return (React.createElement("td", { onFocusCapture: this.onNonEditToggleCellFocus, key: "".concat(this.state.textStyle.id, "_style_set_defaults") }, fixedStyle ? (React.createElement("span", { className: "px-2" }, "N/A")) : (React.createElement(React.Fragment, null,
            React.createElement("button", { className: "btn btn-outline-info py-1 px-2", type: "button", "aria-label": "Default Style Set", style: { "fontSize": "0.75rem" }, onClick: this.showStyleSetDefaultsModal, "data-toggle": "tooltip", title: "", "data-original-title": "Default Style Set" }, this.state.textStyle.styleSetDefaults && this.state.textStyle.styleSetDefaults.length ? "YES" : "NO"),
            this.state.showDefaultValueChanged ? (React.createElement("div", { className: "defaultValueChanged", style: { display: "inline-block", marginLeft: "6px" } },
                React.createElement("i", { style: { color: "red", fontSize: "8px" }, className: "fa fa-asterisk" }, "\u00A0"))) : null))));
    };
    AsyncJsonTextStyleRow.prototype.onPointSizeChange = function (event) {
        var newValue = event.target.value;
        this.setState(function (prevState) {
            var newState = prevState;
            newState.textStyle.pointSize = newValue;
            return newState;
        }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.togglePointSizeState = function () {
        this.setState(function (prevState) {
            var newState = prevState;
            newState.textStyle.togglePointSize = !prevState.textStyle.togglePointSize;
            return newState;
        }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.showPointSizeModal = function () {
        return __awaiter(this, void 0, void 0, function () {
            var newPointSizes;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, TextStyleList.showPointSizeModal(this.state.textStyle.togglePointSizeValues)];
                    case 1:
                        newPointSizes = _a.sent();
                        this.setState(function (prevState) {
                            var newState = prevState;
                            newState.textStyle.togglePointSizeValues = newPointSizes;
                            return newState;
                        }, this.afterSetState);
                        return [2 /*return*/];
                }
            });
        });
    };
    AsyncJsonTextStyleRow.prototype.toggleMultipleColor = function () {
        this.setState(function (prevState) {
            var state = prevState;
            state.textStyle.toggleColor = !prevState.textStyle.toggleColor;
            return state;
        }, this.afterSetState);
    };
    AsyncJsonTextStyleRow.prototype.showColorPickerModal = function (colorValue) {
        return __awaiter(this, void 0, void 0, function () {
            var newValues;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, TextStyleList.showColorPickerModal(colorValue)];
                    case 1:
                        newValues = _a.sent();
                        if (newValues == null) {
                            return [2 /*return*/];
                        }
                        this.setState(function (prevState) {
                            var state = prevState;
                            state.textStyle.toggleColorValues = newValues;
                            return state;
                        }, this.afterSetState);
                        return [2 /*return*/];
                }
            });
        });
    };
    AsyncJsonTextStyleRow.prototype.toggleDrillDown = function () {
        this.setState(function (prev) {
            return {
                showDrillDown: !prev.showDrillDown
            };
        });
    };
    AsyncJsonTextStyleRow.prototype.showTaggingOverrideModal = function () {
        return __awaiter(this, void 0, void 0, function () {
            var overrideValue;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, TextStyleList.showTaggingOverrideModal(this.state.textStyle.taggingOverride)];
                    case 1:
                        overrideValue = _a.sent();
                        if (overrideValue !== null) {
                            this.setState(function (prevState) {
                                var textStyle = prevState.textStyle;
                                textStyle.taggingOverride = overrideValue;
                                return {
                                    textStyle: textStyle
                                };
                            }, function () {
                                _this.afterSetState();
                                _this.forceUpdate();
                            });
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AsyncJsonTextStyleRow.prototype.showStyleSetDefaultsModal = function () {
        return __awaiter(this, void 0, void 0, function () {
            var overrideValue;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, TextStyleList.showStyleSetDefaultsModal(this.state.textStyle, this.state.textStyle.styleSetDefaults)];
                    case 1:
                        overrideValue = _a.sent();
                        if (overrideValue !== null) {
                            this.setState(function (prevState) {
                                var textStyle = prevState.textStyle;
                                textStyle.styleSetDefaults = overrideValue;
                                return {
                                    showDefaultValueChanged: false,
                                    textStyle: textStyle
                                };
                            }, function () {
                                _this.afterSetState();
                                _this.forceUpdate();
                            });
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AsyncJsonTextStyleRow.prototype.onRowBlur = function () {
        if (this.state.manualEdit) {
            return;
        }
        this.setState(function (prevState) {
            var editState = prevState.editState;
            editState.name = false;
            editState.fontName = false;
            editState.connectorName = false;
            return {
                editState: editState
            };
        });
    };
    AsyncJsonTextStyleRow.prototype.toggleManualEdit = function () {
        this.setState(function (prevState) {
            var editState = prevState.editState;
            editState.name = true;
            editState.fontName = true;
            editState.connectorName = true;
            return {
                manualEdit: true,
                editState: editState
            };
        });
    };
    AsyncJsonTextStyleRow.ellipsisOverflowCss = { overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap" };
    return AsyncJsonTextStyleRow;
}(React.Component));
