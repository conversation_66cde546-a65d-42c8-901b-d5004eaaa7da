var stampDate = new Date();
var cacheStamp = stampDate.getTime();

// CRSF Stack - Polled for objects awating injection
var tokenInjectionTargets 	= new Array();
var pageRedirectTargets 	= new Array();
var pageRedirectTargetsTop	= new Array();
var windowOpenTargets 		= new Array();

var beforeDoSubmit;

var developerModeCookie = document.cookie
        .split(';')
        .find(function(x) { return x.trim().startsWith('developer-mode')}) || "";
var mpFeatureFlagCookie = document.cookie
	.split(';')
	.find(function(x) { return x.trim().startsWith('mp-feature-flag')}) || "";
var IS_DEVELOPER_MODE = developerModeCookie.length > 0;
var mainDevMode = null;

/**
 * @return {boolean}
 */
function MAIN_DEV_MODE() {

    if (mainDevMode === null) {
        mainDevMode = (document.cookie
                .split(';')
                .find(function (x) {
                    return x.trim().startsWith('main-dev-mode')
                }) || "") !== '';
    }

    return mainDevMode;
}

jQuery(document).ready(function($){

	popupFactoryRemove('main_onload');

	// I18N: Set page submission charset to UTF-8
	$('form').each( function() {
		if ( $(this).attr('accept-charset') == undefined || $(this).attr('accept-charset') == "")
			$(this).attr('accept-charset', 'UTF-8');
	});

	$('#userMenuPlaceholder').userMenu();
	if ( $('#backgroundTasksPlaceholder').length != 0 )
		$('#backgroundTasksPlaceholder').backgroundTasks();
	$('select').each( function() {
		if ( this.options.length == 0 ) {
			var emptyOption = document.createElement('option');
			emptyOption.text = client_messages.text.no_items;
			emptyOption.value = '-1';
			this.options.add(emptyOption);
		}
	});

	// Touchpoint widget: Zone presentation:
	//		Adjust positioning to accommodate border width
	$('.zoneNotSelectedClass,.zoneSelectedClass').each( function() {
		// Don't compensate for border-width on zone edit (messes up slider positioning)
		// Modify when new 'drawing' zone edit interface implemented
		if ( $(this).closest('.zoneEditContainer').length == 0 ) {
			try {
				var borderWidth = parseInt( $(this).css('border-top-width').replace('px','') );
				var top =  parseInt( $(this).css('top').replace('px','') );
				var left =  parseInt( $(this).css('left').replace('px','') );
				$(this).css({'top': top-borderWidth, 'left': left-borderWidth});
			} catch(err) {
				// UNKNOWN error resulting from .css() being UNDEFINED
			}
		}
	});

	// Touchpoint widget: Part toggle:
	//		Cycle through parts that are stacked
	$(document).click(function(e){
		if ( $(eventTarget(e)).is('.zonePartSelected') ) {
			var eleList = getZonePartsAtEventLocation(e);
			if (eleList) {
				var partList = new Array();
				var partIndex = 0;
				for (var i=0; i<eleList.length; i++)
					if ( $(eleList[i]).is('.multipartZonePart') && !$(eleList[i]).is('.zonePartSelected') )
						partList[partIndex++] = eleList[i];
				if ( partList.length > 0) {
					$(eventTarget(e)).css('z-index',0);
					partList.sort( function(a,b){return $(a).css('z-index') > $(b).css('z-index');} );
					for (var j=0; j<partList.length; j++) {
						$(partList[j]).css('z-index',j+1);
						if (j==partList.length - 1)
							$(partList[j]).click();
					}
				}
			}
		}
	});

	// Init item detail tool tip
	$('.detailTip, .txtFmtTip').each(function(){
		initTip(this);
	});

	// Disable flow control buttons on flow control action (prevents double submit)
	$('.flowControlBtn').click( function() {
		var $this = $(this);
		let aTagJQuery = $this.find('a');
		if (aTagJQuery.length > 0) {
			$this.addClass('disabled btnDisabled');
			let aTagUrl = aTagJQuery.attr('href');
			aTagJQuery.attr('href', '#');
			location.href = aTagUrl;
		}
	});

	$(document).click( function() {
		framesClickHandler(getTopFrame(),window);
	});

	$('.buttonTagBtn').each( function() {
		var $link = $(this).find('a');

		if ( $link.length != 0  ) {
			var btnHref = $link.attr('href');
			if ( btnHref.indexOf('javascript:') != -1 ) {
				$(this).attr("onclick", unescape(btnHref.replace('javascript:','')));
			} else if ( btnHref != "#" ) {
				$(this).attr("onclick", "javascriptHref('" + unescape(btnHref) + "')");
			}
            $link.attr('href','#');
			if($link.attr('href') === '#')
                $link.click(function (e) {
					e.preventDefault();
				});
		}

	});

	$("#globalSearchTogger").each(function () {
		$(this).iFrameMenu({
			width: 1024,
			height: 800,
			horiOffset: 600,
			vertOffset: -8,
			src: "../search/global_search.form",
			appliedParams: {searchType: 'abs', tk: getParam('tk')},
			onMenuOpen: function (oMenu) {
				$(oMenu.targetEle).addClass('active');
			},
			onMenuClose: function (oMenu) {
				$(oMenu.targetEle).removeClass('active');
			},
			onSave: function (oMenu, frameURL) {
				return false;
			}
		});
	});

	var buildInfo = $('.buildInfo');

	buildInfo.on('click', function () {

		var count = !_.isUndefined(buildInfo.data('counter')) ? buildInfo.data('counter').ctr : 0;
		buildInfo.data('counter', { ctr: count + 1});

		if (count >= 4) {

			if (mpFeatureFlagCookie.length === 0) {
				document.cookie = 'mp-feature-flag=%7B%7D; path=/';
				document.location.reload();
			} else {
				document.dispatchEvent(new CustomEvent('mp-feature-flag-disable'));
			}


		}
	});

	// CLIENT LANGUAGE: Refresh client language key/values
	// DEMO MODE: Toggle
	$(document).keydown( function(e) {

		if ( (e.altKey || e.altKey == undefined) && (e.keyCode == 76 || e.keyCode == 68)) {

			// Alt+L: Language refresh
			if ( e.keyCode == 76 ) {

				var stampDate = new Date();
				$.ajax({
					type: "GET",
					url: context + "/getClientLanguageRefresh.form?cacheStamp=" + (stampDate.getTime()),
					dataType: "json",
					success: function(data) {
						getTopFrame().location.reload();
					}
				});

				e.preventDefault();
				return false;
			}

			// Alt+D: Demo mode
			if ( e.keyCode == 68 ) {
				if (developerModeCookie.length > 0) {
					document.cookie = 'developer-mode=true; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/';
				} else {
					document.cookie = 'developer-mode=true; path=/';
				}
				e.preventDefault();
				getTopFrame().location.reload();
				return false;
			}

		}

	});

	// SIDEBAR NAV TAB: Save tab in local storage and load it automatically
	$('.nav-item').click(function() {
		var navTab = $(this).attr('id');
		var acceptableNavItems = ["nav-thumbnail-tab", "nav-variants-tab"];
		if (acceptableNavItems.includes(navTab)) {
			localStorage.setItem("msgpt_sidebar_tab", navTab);
		}
	});
	loadSidebarNavTab();

	if ( developerModeCookie.length > 0 ) {

		var winTop = getTopFrame();

		if ($('#developerMode', winTop.document).length === 0) {

			var $devModeIndicator = $($.parseHTML(
									'<div id="developerMode" class="d-inline-block h-100 ml-4" aria-hidden="true" ' +
										'data-toggle="tooltip" title="developer mode">' +
										'<span class="developer-mode box-shadow">' +
											'<i class="far fa-user-secret"></i>' +
										'</span>' +
									'</div>'));

			$("#legal", winTop.document).find('.footer-content').append($devModeIndicator.tooltip());

		}
	}

	_.defer(function () {
        if ($('.contentTableIframe').length > 0) {
            common.refreshParentIframeHeight();
        }
    });
});



// START - GENERAL FUNCTIONS

//-- Create Touchpoint

var $createTpBtnPlaceholder = $('<button id="createTpBtnModalPlaceholder"/>').iFrameModal({
		id: 'createTp',
		modalTitle: client_messages.text.create_touchpoint,
		modalTittleInContent: false,
		src: context + '/tpadmin/create_touchpoint.form',
		iFrameTitle: client_messages.text.create_touchpoint,
		beforeModalClose: function () {

			var url = $createTpModal.$iFrame.get(0).contentWindow.location.href;

			if (getParamFromURL(url, 'nprSaveSuccess') === "true")
				getTopFrame().location.href = context + "/tpadmin/document_view.form?docid=" + getParamFromURL(url, 'documentId') + "&tk=" + getParam('tk');

		}
	}),
	$createTpModal = $createTpBtnPlaceholder.data('iFrameModal');

function createTouchpointAction(parentDocumentId) {

	var parameters = {tk : getParam('tk')};

	if (parentDocumentId !== undefined)
		parameters.channelParentId = parentDocumentId;

	$createTpModal.options.params = parameters;
	$createTpModal.showModal();

}

//-- Add task
var $editTaskBtnPlaceholder = $('<button id="editTaskBtnModalPlaceholder"/>').iFrameModal({
		id: "taskFrame",
		modalTitle: client_messages.title.add_task,
		modalTittleInContent: false,
		iFrameTitle: client_messages.title.add_task,
		src: context + "/tasks/task_edit.form",
		modalClass: 'modal-xl',
		beforeModalClose: function () {
			var url = $editTaskModal.$iFrame.get(0).contentWindow.location.href;

			if (getParamFromURL(url, 'nprSaveSuccess') === "true")
				getTopFrame().location.reload();

			if ($('#addTask').hasClass('highlightedBtnDisabled'))
				return false;
		}
	}),
	$editTaskModal = $editTaskBtnPlaceholder.data('iFrameModal');

function openAddTaskModal(assetIds, $taskMetadataSelect, isMultiSelect, assetLocaleIds = ''){
	var parameters = {
		tk : getParam('tk'),
		assetIds : assetIds,
		metadataId : $taskMetadataSelect.val()};

	if(assetLocaleIds !== ''){
		parameters.assetLocaleIds = assetLocaleIds;
	}
	$editTaskModal.options.params = parameters;

	if(isMultiSelect && $("input[id^='listItemCheck']:checked").length > 1){
		$editTaskModal.options.modalTitle = client_messages.title.add_tasks;
	}else{
		$editTaskModal.options.modalTitle = client_messages.title.add_task;
	}
	$editTaskModal.showModal();

	// Reset metadata select
	$taskMetadataSelect.selectOption('taskMetadataSelect_0');
	actionCancel();
}

function getSelectedIdsForTasks() {
	var selectedIds = '',
		selectedMsgIds = '',
		selectedSTIds = '',
		selectedImgIds = '',
		selectedLteIds = '',
		selectedLimIds = '',
		selectedTgrIds = '';

	$("input[id^='listItemCheck']:checked").each(
		function () {
			var currentSelectedId = this.id.replace('listItemCheck_', '');
			var objectType = $(this).attr('objectType');
			switch (objectType) {
				case '1':{
					selectedMsgIds += '_'+currentSelectedId;
					break;
				}
				case '2':{
					selectedSTIds += '_'+currentSelectedId;
					break;
				}
				case '3':{
					selectedLteIds += '_'+currentSelectedId;
					break;
				}
				case '4':{
					selectedImgIds += '_'+currentSelectedId;
					break;
				}
				case '5':{
					selectedLimIds += '_'+currentSelectedId;
					break;
				}
				case '7':{
					selectedTgrIds += '_'+currentSelectedId;
					break;
				}
			}
		}
	);

	if(selectedMsgIds !== '')
		selectedIds += ('msg-'+selectedMsgIds.replace('_',' ').trim().replace(' ','_'));
	if(selectedSTIds !== '')
		selectedIds += (',sas-'+selectedSTIds.replace('_',' ').trim().replace(' ','_'));
	if(selectedImgIds !== '')
		selectedIds += (',ili-'+selectedImgIds.replace('_',' ').trim().replace(' ','_'));
	if(selectedLteIds !== '')
		selectedIds += (',lte-'+selectedLteIds.replace('_',' ').trim().replace(' ','_'));
	if(selectedLimIds !== '')
		selectedIds += (',lim-'+selectedLimIds.replace('_',' ').trim().replace(' ','_'));
	if(selectedTgrIds !== '')
		selectedIds += (',tgr-'+selectedTgrIds.replace('_',' ').trim().replace(' ','_'));

	return selectedIds.replace(',',' ').trim().replace(' ',',');
}

function runStatisticsReportAction(parentDocumentId) {
	$('.categoryMenuContainer').hide();
	$('#featurePopupAnchor').iFramePopup({
		width			: 820,
		displayOnInit	: true,
		title			: client_messages.text.statistics,
		src				: context+"/stats/instance_statistics.form",
		appliedParams	: {tk : getParam('tk')},
		closeBtnId		: "cancelBtn_button",
        matchContentHeight 	: true,
        applyCloseToggle: false,
		beforePopupClose: function() {

		}
	});
}

function initExpandableTxt(ele) {
	if ( !$(ele).is('.init') ) {
		$(ele).find('.expandTxtInd').click( function() {
			$(this).hide();
			$(this).closest('.expandableTxtEle').addClass('init').find('.expandedTxt').showEle('normal');
		});
		$(ele).find('.contractTxtInd').click( function() {
			$(this).closest('.expandableTxtEle').find('.expandTxtInd').showEle('normal');
			$(this).closest('.expandableTxtEle').find('.expandedTxt').hide();
		});
	}
}

function initTooltip(targetEle) {
	$(targetEle).tooltip();
}
function initTip( targetEle, data ) {
	$(targetEle).each( function() {

		var popupWidth = 150;
		if ( $(this).is('.width_250') )
			popupWidth = 250;

		$(this).cluetip({
			showTitle: false,
			splitTitle: '|',
			width: popupWidth,
			positionBy: 'mouse',
			leftOffset: 10,
			topOffset: -5,
			cursor: 'default',
			cluezIndex: (data && data.zIndex ? data.zIndex : 12000),
			hoverIntent: {
				sensitivity:  2,
				interval:     400,
				timeout:      0
			},
				fx: {
				open:       'fadeIn',
				openSpeed:  '300'
			},
			cluetipClass: 'rounded',
			dropShadow: false,
			onActivate:	function(event) {
				return $(event.target).is(':visible');
			}
		});
	});
}

function initDraggable(ele, options) {
	$(ele).draggable(options);
}
function initTree(f,o) {
	if ( $.isFunction(f) )
		f(o);
}
function triggerModal(ele) {
	$(ele).modal('show');
}
function toggleCodeEditor(source,editor_id) {
	var html_editor_options = {
		animatedScroll: false,
		behavioursEnabled: true,
		cursorStyle: "ace",
		displayIndentGuides: true,
		dragDelay: 150,
		dragEnabled: true,
		enableBlockSelect: true,
		enableMultiselect: true,
		fadeFoldWidgets: false,
		firstLineNumber: 1,
		foldStyle: "markbegin",
		hScrollBarAlwaysVisible: false,
		highlightActiveLine: true,
		highlightGutterLine: true,
		highlightSelectedWord: true,
		indentedSoftWrap: true,
		mergeUndoDeltas: true,
		mode: "ace/mode/html",
		newLineMode: "auto",
		overwrite: false,
		printMargin: 80,
		printMarginColumn: 80,
		readOnly: true,
		scrollPastEnd: 0,
		scrollSpeed: 2,
		selectionStyle: "line",
		showFoldWidgets: true,
		showGutter: true,
		showInvisibles: false,
		showLineNumbers: true,
		showPrintMargin: true,
		tabSize: 4,
		theme: "ace/theme/chrome",
		tooltipFollowsMouse: true,
		useSoftTabs: true,
		useWorker: true,
		vScrollBarAlwaysVisible: false,
		wrap: "off",
		wrapBehavioursEnabled: true
	}

    var beautify_source = html_beautify($.trim(source), {
        indent_size: '2',
        indent_char: ' ',
        max_preserve_newlines: '5',
        preserve_newlines: true,
        keep_array_indentation: false,
        break_chained_methods: false,
        indent_scripts: 'normal',
        brace_style: 'expand',
        space_before_conditional: true,
        unescape_strings: false,
        jslint_happy: false,
        end_with_newline: false,
        wrap_line_length: '80',
        indent_inner_html: true,
        comma_first: false,
        e4x: false
    });

	var sourceEditor = ace.edit(editor_id);
	sourceEditor.setOptions(html_editor_options);
	sourceEditor.session.setUseWrapMode(true);
	sourceEditor.setValue(beautify_source);

}


function tabHover(tabEle, action) {
	if (!$(tabEle).hasClass('tabSelected_minimal')) {
		if (action == 'in') {
			$(tabEle).removeClass('tab_minimal');
			$(tabEle).addClass('tabHov_minimal');
		} else if (action == 'out') {
			$(tabEle).removeClass('tabHov_minimal');
			$(tabEle).addClass('tab_minimal');
		}
	}
}

(function($) {
	$.fn.showEle = function(action) {
		if ( true ) {
			$(this).fadeIn(action);
			$(this).show();
		} else
			$(this).show();
		return $(this);
	};
	$.fn.hideEle = function(action) {
		if ( true ) {
			$(this).fadeOut(action);
			$(this).hide();
		} else
			$(this).hide();
		return $(this);
	};
})(jQuery);

// Touchpoint widget: Support API
//		Retrieve stacked parts
function getZonePartsAtEventLocation(e) {
    var clickX = e.pageX, clickY = e.pageY,
    	list, $list,
    	offset, range,
    	$body = $('body').parents().andSelf();
	if (clickX > 0 && clickY > 0) {
	    $list = $('body .multipartZonePart*').filter(function() {
	        offset = $(this).offset();
	        range = {
	            x: [ offset.left,
	                offset.left + $(this).outerWidth() ],
	            y: [ offset.top,
	                offset.top + $(this).outerHeight() ]
	        };
	        return (clickX >= range.x[0] && clickX <= range.x[1]) && (clickY >= range.y[0] && clickY <= range.y[1]);
	    });

	    $list = $list.add($body);
	}
	return $list;
}

// Section: Background image
function sectionImageOnLoad(index, image) {
	if ( $(image).is('.scaleForFit') ) {
		var scale = parseFloat( $(image).closest("[scale]").attr('scale') );
		$(image).css({
			'width' 	: (scale * image.naturalWidth) + 'px',
			'height' 	: (scale * image.naturalHeight) + 'px'
		});
	}
}

// END - GENERAL FUNCTIONS


//**********************************************

//START - BACKGROUND TASK FUNCTIONS
function backgroundManagerSelectMyTasks(element, event) {

	var taskManagerId = $(element).closest("div[aria-labelledby]").attr('aria-labelledby');
	event.cancelBubble = true;

	if(!$(element).hasClass("active")) {

	    $(element).addClass("active");
	    $(element).parent().parent().children().find(".selectAllTask").removeClass("active");
		$('#' + taskManagerId).refreshIfNotInProgress();

	}

}

function backgroundManagerSelectAllTasks(element, event) {

	var taskManagerId = $(element).closest("div[aria-labelledby]").attr('aria-labelledby');
	event.cancelBubble = true;

	if(!$(element).hasClass("active")) {

	    $(element).addClass("active");
	    $(element).parent().parent().children().find(".selectMyTask").removeClass("active");
		$('#' + taskManagerId).refreshIfNotInProgress();

	}

}
// END - BACKGROUND TASK FUNCTIONS

//**********************************************

// START - FRAME MANAGEMENT FUNCTIONS

function closeIframe(data) {
	// Hide cluetip popup: Mouseout not called when iFrame closes
	var $topBody = $('body', getTopFrame().document);
    $topBody.find('#cluetip').hide();
    $topBody.find('.contentPopup_popupContainer').remove();
    $('.iFramePopupPageScreen', getTopFrame().document).remove();

    if($topBody.is('.fullModalOpen'))
    	common.removeTopFrameBodyClass('fullModalOpen');

	popupFactoryRemove('main_closeIframe');

	if ( data == undefined || data.clearMask == undefined || data.clearMask )
		$(getTopFrame().document).find('.iFramePopupPageScreen').remove();

	var iframeId = $('#popupFrameId').val();

	if($(getTopFrame().document).find('#iFramePopupWrapper_' + iframeId).length)
        $(getTopFrame().document).find('#iFramePopupWrapper_' + iframeId).remove();

	else
        $(getTopFrame().document).find('#iFramePopup_' + iframeId).remove();
}

//frameClickHandler: Iterate all frames and initiate popup cleanup in frame other than the one clicked
function framesClickHandler(currentWindow, targetWindow) {

	try {
		var doc = currentWindow.contentWindow || currentWindow.contentDocument || currentWindow;
		if (doc.document)
		    doc = doc.document;
		var targetDoc = targetWindow.contentWindow || targetWindow.contentDocument || targetWindow;
		if (targetDoc.document)
			targetDoc = targetDoc.document;
	} catch(err) {
		return;
	}

	if ( doc == undefined )
		return;

    if (doc != targetDoc) {
    	// Context Menus: Hide
		var $contextMenu = $(doc).find(".contextMenu");

		if(!$contextMenu.closest('.contextMenuContainer'))
			$contextMenu.hide();

        // Styled Menus: Hide and reset button styling
        $(doc).find("[id^='styledMenu_']").hide();
        $(doc).find('.actionBtn_hov').removeClass('actionBtn_hov').addClass('actionBtn');
        $(doc).find('.actionSelectMenuArrow_hov').removeClass('actionSelectMenuArrow_hov').addClass('actionSelectMenuArrow');

    }

    // Navigate frame stack
    var frames = doc.getElementsByTagName('iframe');
    var currentFrame;
    for (var i=0, len=frames.length; i<len; i++) {
        currentFrame = frames[i];
        framesClickHandler(currentFrame, targetWindow);
    }

}

//BUTTONS: Flag as primary flow button
function setPrimaryBtn(type) {
	if ( type == 'save' )
		$('#link_' + client_messages.button.save).closest('.btn').removeClass('btn-outline-dark').addClass('btn-primary primaryBtn');
	else if ( type == 'continue' )
		$('#link_' + client_messages.button.continue).closest('.btn').removeClass('btn-outline-dark').addClass('btn-primary primaryBtn');
}

function tabToggle(tab, url) {

    var statusViewIdParam = gup('statusViewId');

    if (!_.isEmpty(statusViewIdParam)) {
        url += '&statusViewId=' + statusViewIdParam;
    }

	if ( localStorage.getItem("msgpt_tabWarningPopupToggle") == "on" || !$(tab).closest('.workflowTabsContainer').is('.workflowEditMode') ) {
		javascriptHref(url);
	} else {

		setTimeout( function(){

			$(tab).popupFactory({
				title				: client_messages.title.continue_without_save,
				trigger				: "instant",
				referenceWindow		: "local",
				width				: 240,
				fnSetContent		: function(o) {

					var ele = $("<div>" +
									"<div class='tabWarningPopupToggle pddng-lv1'>" +
										"<b><em><input id=\"tabWarningPopupToggle\" class=\"checkbox\" type=\"checkbox\" />" + " &nbsp; " + client_messages.text.dont_ask_again + "</em></b>" +
									"</div>" +
									"<div class=\"popupFactoryButtonsContainer\">" +
										"<input title=\"" + client_messages.button.cancel + "\" type=\"button\" id=\"cancelTabToggleBtn\" style=\"display: none;\" />" +
				                        "<input title=\"" + client_messages.button.ok + "\" type=\"button\" id=\"continueWithoutSaveBtn\" style=\"display: none;\" />" +
			                        "</div>" +
								"</div>");

					$(ele).find('#cancelTabToggleBtn').click(function () {
                        common.disableFixedSidebar();
                        popupFactoryRemove('main_continuewithoutsave');
					}).styleActionElement();

                    $(ele).find('#continueWithoutSaveBtn').click(function () {
                        common.disableFixedSidebar();
                        javascriptHref(url);
					}).styleActionElement({highlighted: true});

                    if( typeof(Storage)!=="undefined" ) {
						$(ele).find('#tabWarningPopupToggle').click( function() {
							localStorage.setItem("msgpt_"+$(this).attr('id'),$(this).val());
						});
					} else {
						$(ele).find('#tabWarningPopupToggle').closest('div').hide();
					}

					return ele;
				}
			});

		}, 50);

	}

}

// END - FRAME MANAGEMENT FUNCTIONS

// **********************************************

// START - HREF FUNCTIONS

function javascriptHref(url){
	if (url != '')
		pageRedirectTargets.push(url); // CSRF
}

function javascriptHrefTop(url) {
	if (url != '') {
		pageRedirectTargetsTop.push(url);
	}
}

function directHref(url){
	window.location = url;
}

function btnJavascriptHref(url,btn){
	if (!popupSubmitLock && url != '') {
		if ($(btn).attr('disabled') == "disabled")
			return;
		else
			pageRedirectTargets.push(url); // CSRF
	}
}

function getParamFromURL(targetURL, name)
{
	var regexS = "[\\?&]"+name+"=([^&#]*)";
	var regex = new RegExp(regexS);
	var results = regex.exec(targetURL);
	if (results == null)
		return "";
	else
		return results[1];
}

function getParam(name)
{
	var regexS = "[\\?&]"+name+"=([^&#]*)";
	var regex = new RegExp(regexS);
	var tmpURL = window.location.href;
	var results = regex.exec(tmpURL);
	if (results == null)
		return "";
	else
		return results[1];
}

function verifyParamExists(name)
{
	var regexS = "[\\?&]"+name;
	var regex = new RegExp(regexS);
	var tmpURL = window.location.href;
	var results = regex.exec(tmpURL);
	if (results == null)
		return false;
	else
		return true;
}

function gup( name, fromHref )
{
    name = name.replace(/[\[]/,"\\\[").replace(/[\]]/,"\\\]");
    var regexS = "[\\?&]"+name+"=([^&#;]*)";
    var regex = new RegExp( regexS );
    var results = regex.exec( fromHref || window.location.href );
    if( results == null )
        return "";
    else
        return results[1];
}

var submitLock = false;
function doSubmit( type ) {
	if (!submitLock) {

		if ( beforeDoSubmit && $.isFunction(beforeDoSubmit) )
			if ( beforeDoSubmit() == false )
				return

		var x = document.getElementById('command');
		removeQueryStringParameter( x, 'submittype' );
		removeQueryStringParameter( x, 'saveSuccess' );
		removeQueryStringParameter( x, 'nprSaveSuccess' );

		if( x.action.indexOf("?") != -1 )
			x.action = x.action + '&submittype=' + type;
		else
			x.action = x.action + '?submittype=' + type;
		if ( exists('actionElement') )
			$("#actionElement").val(type);
		submitLock = true;
		x.submit();
	} else {
		setTimeout(function(){
			doSubmit(type);
		},100);
	}
}

function doSubmitWithParameters(type, parameters) {
	if (!submitLock) {

		if ( beforeDoSubmit && $.isFunction(beforeDoSubmit) ) {
			if (beforeDoSubmit() == false) {
				return;
			}
		}

		var form = document.getElementById('command');
		removeQueryStringParameter(form, 'submittype');
		removeQueryStringParameter(form, 'saveSuccess' );
		removeQueryStringParameter(form, 'nprSaveSuccess' );

		form.action += (form.action.indexOf('?') != -1 ? '&' : '?') + 'submittype=' + type;

		for (var paramName in parameters) {
			if (parameters.hasOwnProperty(paramName)) {
				removeQueryStringParameter(form, paramName);

				form.action += (form.action.indexOf('?') != -1 ? '&' : '?') + paramName + '=' + parameters[paramName];
			}
		}

		form.submit();
	} else {
		setTimeout(function() { doSubmit(type) }, 0);
	}
}

function doSubmitWithParameter(type, parameter, value) {
	if (!submitLock) {

		if ( beforeDoSubmit && $.isFunction(beforeDoSubmit) )
			if ( beforeDoSubmit() == false )
				return

		var x = document.getElementById('command');
		removeQueryStringParameter( x, 'submittype' );
		removeQueryStringParameter( x, 'saveSuccess' );
		removeQueryStringParameter( x, 'nprSaveSuccess' );
		removeQueryStringParameter( x, parameter );

		x.action += (x.action.indexOf('?') != -1 ? '&' : '?') + 'submittype=' + type + "&" + parameter + "=" + value;
		submitLock = true;
		x.submit();
	} else {
		setTimeout(function(){
			doSubmit(type);
		},100);
	}
}

function submitWithParameter( parameter, value ) {
	if (parameter != '') {

		if ( beforeDoSubmit && $.isFunction(beforeDoSubmit) )
			if ( beforeDoSubmit() == false )
				return

		var x = document.getElementById('command');
		removeQueryStringParameter( x, 'saveSuccess' );
		removeQueryStringParameter( x, 'nprSaveSuccess' );

		if( x.action.indexOf("?") != -1 ) {
			x.action = x.action + "&" + parameter + "=" + value;
		} else {
			x.action = x.action + "?" + parameter + "=" + value;
		}
		x.submit();
	}
}

function removeQueryStringParameter( formObject, parameter ) {
	let stringToSkip = '?' + parameter + '=';

	let startIndex = formObject.action.indexOf(stringToSkip);
	if (startIndex != -1) {
		startIndex += '?'.length;
		stringToSkip = stringToSkip.substring('?'.length)
	} else {
		stringToSkip = '&' + parameter + '=';
		startIndex = formObject.action.indexOf(stringToSkip);
	}

	if ( startIndex >= 0 ) {
		var endIndex = formObject.action.indexOf( '&', startIndex + stringToSkip.length);
		if ( endIndex == -1 ) {
			endIndex = formObject.action.length;
		}
		formObject.action = formObject.action.substring(0, startIndex) + formObject.action.substring(endIndex);
	}
	if ( formObject.action.indexOf('&') != -1 && formObject.action.indexOf('?') == -1 ) {
		formObject.action = formObject.action.replace('&', '?');
	}
}

function addParam(queryString,param) {
	if (queryString.indexOf('?')) {
		return queryString += '&'+param;
	} else {
		return queryString += '?'+param;
	}
}

function removeParam(targetURL, param) {
	if( targetURL.indexOf(param) != -1 ) {
		var startIndex = targetURL.indexOf(param);
		if( startIndex >= 0 ) {
			var endIndex = targetURL.indexOf( '&', targetURL.indexOf(param));
			if( endIndex == -1 ) {
				endIndex = targetURL.length;
			}
			targetURL = targetURL.replace( targetURL.substring( startIndex -1, endIndex), '');
		}
		if ( targetURL.indexOf('&') != -1 && targetURL.indexOf('?') == -1 )
			targetURL = targetURL.replace('&','?');
	}

	return targetURL;
}

function formatQueryString(x) {
	var tmpStr = null;
	if ( x.action.indexOf('&') != -1 ) {
		if (x.action.indexOf('?') != -1) {
			if (x.action.indexOf('?') > x.action.indexOf('&')) {
				tmpStr = x.action.replace('?','&');
				tmpStr = x.action.substring(0,x.action.indexOf('&'))+'?'+x.action.substring(x.action.indexOf('&')+1,x.action.length);
			}
		} else {
			tmpStr = x.action.substring(0,x.action.indexOf('&'))+'?'+x.action.substring(x.action.indexOf('&')+1,x.action.length);
		}
	}
	if (tmpStr != null)
		x.action = tmpStr;
}

function showContinueConfirmationModal(callback) {
	var continueConfirmationModalTemplate =
			'<div class="modal fade" id="FORM_WARNING_CONFIRMATION_MODAL" tabindex="-1" role="dialog" aria-labelledby="taskDetailsModalLabel" aria-hidden="true">' +
			'<div class="modal-dialog" role="document" style="width: 27rem;">' +
			'<div class="modal-content">' +
			'<div class="bg-warning position-absolute rounded-top py-1 w-100" role="presentation"></div>' +
			'<div class="modal-header pb-3 pl-4 border-0">' +
			'<h4 class="modal-title position-relative pl-5" id="formWarningConfirmationModalLabel">' +
			'<i class="far fa-exclamation-circle fa-lg position-absolute left-0 mt-2 text-warning" aria-hidden="true"></i>' +
			client_messages.title.continue_without_save +
			'</h4>' +
			'</div>' +
			'<div class="modal-body pt-0 pb-2">' +
			'<p class="mx-4 px-3">' +
			client_messages.title.some_changes_have_not_been_saved + ' ' +
			client_messages.title.are_you_sure_you_want_to_leave_this_page +
			'</p>' +
			'</div>' +
			'<div class="modal-footer bg-lightest py-3 px-4">' +
			'<button type="button" class="btn btn-lightest" data-dismiss="modal">' + client_messages.text.cancel + '</button>' +
			'<button type="button" class="btn btn-warning">' + client_messages.text.yes_continue + '</button>' +
			'</div>' +
			'</div>' +
			'</div>' +
			'</div>';

	var $continueConfirmationModal = $(continueConfirmationModalTemplate).modal({show: false});

	$continueConfirmationModal.find('.modal-footer').children().last().on('click', callback);
	$continueConfirmationModal.modal('show');
}

function goToVariant(id) {
	var callback = function () {
		var linkUrl;
		var contentObjectId = getParam('contentObjectId');

		if (id == -9) {
			linkUrl = context + '/content/content_object_edit_content.form?tk=' + gup('tk') + '&contentObjectId=' + contentObjectId;
		} else {
			linkUrl = context + '/content/content_object_edit_content_dynamic_variant.form?tk=' + gup('tk') + '&contentObjectId=' + contentObjectId + '&paramInstId=' + id;
		}

		var statusViewId = gup('statusViewId');

		if (!_.isEmpty(statusViewId)) {
			linkUrl += '&statusViewId=' + statusViewId;
		}

		javascriptHref(linkUrl);
	};

	if(window.serializedObservableForm !== undefined) {
		var saveSuccess = new URL(window.location.href).searchParams.get('saveSuccess');
		var isViewMode = window.location.href.includes("_view");
		serializedObservableForm.checkIfNeedSave();

		if (serializedObservableForm.needsSave && saveSuccess === null && !isViewMode) {
			showContinueConfirmationModal(callback);
		} else {
			callback();
		}

	} else {
		callback();
	}
}

// END - HREF FUNCTIONS

// **********************************************

// START - HELPER FUNCTIONS

function eventTarget(e) {
	if (window != null && window.event != null)
		return event.srcElement;
	else
		return e.target;
}

function exists(id) {
	if ($('#'+id).length > 0)
		return true;
	else
		return false;
}

function parseId(ele) {
	try {
		if ( $(ele).attr('id') ) {
			if ( $(ele).attr('id').indexOf('_') != -1 )
				return $(ele).attr('id').substring( $(ele).attr('id').indexOf('_')+1 );
			else
				return null;
		} else if ( ele.indexOf('_') != -1 ) {
			return ele.substring( ele.indexOf('_')+1 );
		} else {
			return $(ele).attr('id');
		}
	} catch (e) {
		console.log(e)
	}
}

function mpParseJSON(obj) {
	if ( jQuery.type( obj ) === "string" )
		return jQuery.parseJSON( obj );
	return obj
}

function fmtClientMessage(message, params) {
	//TO DO: Detect array; use regex for replacement
	return message.replace("{0}",params);
}

//case insensitive contains selector (Mantis #6556) 
jQuery.expr[':'].Contains = function(a,i,m){
     return jQuery(a).text().toUpperCase().indexOf(m[3].toUpperCase())>=0;
};

function endsWith(str, suffix) {
    return str.indexOf(suffix, str.length - suffix.length) !== -1;
}

function escapeHtmlEntities (text) {
    return text.replace(/[\u00A0-\u2666<>\&]/g, function(c) {
        return '&' +
        (escapeHtmlEntities.entityTable[c.charCodeAt(0)] || '#'+c.charCodeAt(0)) + ';';
    });
};

function isValidRedirectUrl(redirectUrl) {
	const baseOrigin = new URL(document.baseURI).origin;
	const redirectOrigin = new URL(redirectUrl, document.baseURI).origin;

	return baseOrigin.toLowerCase() === redirectOrigin.toLowerCase();
}

function getCookie(name) {
	const value = `; ${document.cookie}`;
	const parts = value.split(`; ${name}=`);
	if (parts.length === 2) return parts.pop().split(';').shift();
}

/*
 * closestDescendant
 * https://github.com/tlindig/jquery-closest-descendant
 *
 * Copyright (c) 2014 Tobias Lindig
 * Licensed under the MIT license.
 */

(function($) {

    /**
     * Get the first element(s) that matches the selector by traversing down
     * through descendants in the DOM tree level by level. It use a breadth
     * first search (BFS), that mean it will stop search and not going deeper in
     * the current subtree if the first matching descendant was found.
     *
     * @param  {selectors} selector -required- a jQuery selector
     * @param  {boolean} findAll -optional- default is false, if true, every
     *                           subtree will be visited until first match
     * @return {jQuery} matched element(s)
     */
    $.fn.closestDescendant = function(selector, findAll) {

        if (!selector || selector === '') {
            return $();
        }

        findAll = findAll ? true : false;

        var resultSet = $();

        this.each(function() {

            var $this = $(this);

            // breadth first search for every matched node,
            // go deeper, until a child was found in the current subtree or the leave was reached.
            var queue = [];
            queue.push($this);
            while (queue.length > 0) {
                var node = queue.shift();
                var children = node.children();
                for (var i = 0; i < children.length; ++i) {
                    var $child = $(children[i]);
                    if ($child.is(selector)) {
                        resultSet.push($child[0]); //well, we found one
                        if (!findAll) {
                            return false; //stop processing
                        }
                    } else {
                        queue.push($child); //go deeper
                    }
                }
            }
        });

        return resultSet;
    };

	$.fn.preciseWidth = function() {
		if (!this.is(":visible")) {
			console.warn("getPreciseWidth called on element that is not visible.", this);
			return 0;
		}
		var rect = this[0].getBoundingClientRect();
		return rect.width;
	};

	$.fn.preciseOuterWidth = function() {
		if (!this.is(":visible")) {
			console.warn("getPreciseOuterWidth called on element that is not visible.", this);
			return 0;
		}
		var rect = this[0].getBoundingClientRect();
		var width = rect.width;

		var style = window.getComputedStyle(this[0]);
		var marginLeft = parseFloat(style.marginLeft);
		var marginRight = parseFloat(style.marginRight);
		width += marginLeft + marginRight;

		return width;
	};

	$.fn.preciseHeight = function() {
		if (!this.is(":visible")) {
			console.warn("getPreciseHeight called on element that is not visible.");
			return 0;
		}
		var rect = this[0].getBoundingClientRect();
		return rect.height;
	};

	$.fn.preciseOuterHeight = function() {
		if (!this.is(":visible")) {
			console.warn("getPreciseOuterHeight called on element that is not visible.");
			return 0;
		}
		var rect = this[0].getBoundingClientRect();
		var height = rect.height;

		var style = window.getComputedStyle(this[0]);
		var marginTop = parseFloat(style.marginTop);
		var marginBottom = parseFloat(style.marginBottom);
		height += marginTop + marginBottom;

		return height;
	};

})(jQuery);

// END - HELPER FUNCTIONS

// **********************************************

// START - NAV PANEL MANAGEMENT

function initNavContainer() {
	$('.contentTableHeaderTD').each( function() {
		if ( $(this).parent().closest('.contentTableHeaderTD').length != 0 )
			return;

		var label = $.trim( $(this).text() ).replace(/[\s']/g,'');
		var displayLabel = $.trim( $(this).text() );
		if ( displayLabel.length > 30 )
			displayLabel = "<span class=\"txtFmtTip\" title=\"|<div class=\'txtFmtTipText\'>" + displayLabel + "</div>\">" + displayLabel.substring(0,29) + "...</span>";

		var editLinkEle = $(this).find('.navEditLink');
		$(this).closest('table').addClass('navTarget_' + label);
		$('#navContainer')
		.append("<div class=\"navItem\" id=\"navItem_" + label + "\">" +
					( $(editLinkEle).length != 0 ?
						"<i class=\"navItemLinkIcon fa-mp-btn far fa-edit\"></i>" :
					  "" ) +
					  displayLabel +
				"</div>");

		$('#navContainer .txtFmtTip').each( function() {
			initTip(this);
		})

		$("#navItem_" + label).find('.navItemLinkIcon').click( function(e) {
			javascriptHref( $(editLinkEle).attr('path') );
			e.preventDefault ? e.stopPropagation() : e.returnValue = false;
		});
	});
	toggleNavItems();

	$('#navContainer').find('.navItem').each( function() {
		$(this).click( function() {
            $(window.frameElement).closest('.iFramePopupWrapper').stop().animate({scrollTop: $('.navTarget_' + parseId(this)).offset().top}, 600);
		});
	});
}

function toggleNavItems() {
	$('.contentTableHeaderTD').each( function() {
		if ( $(this).parent().closest('.contentTableHeaderTD').length != 0 )
			return;

		var label = $.trim( $(this).text() ).replace(/[\s']/g,'');
		if ( $(this).is(':visible') )
			$('#navItem_' + label).show();
		else
			$('#navItem_' + label).hide();
	});
}

function loadSidebarNavTab() {
	if ($("#side-section-content").length) {
		var navTab = localStorage.getItem("msgpt_sidebar_tab");
		if (navTab && $('#'+navTab).length) {
			$('#'+navTab).click();
			setTimeout(function() {
				$("#side-section-content").removeClass("opacity-0");
			}, 100);
		} else {
			$("#side-section-content").removeClass("opacity-0");
		}
	}
}
// END- NAV PANEL MANAGEMENT

// all HTML4 entities as defined here: http://www.w3.org/TR/html4/sgml/entities.html
// added: amp, lt, gt, quot and apos
escapeHtmlEntities.entityTable = {
    34 : 'quot',
    38 : 'amp',
    39 : 'apos',
    60 : 'lt',
    62 : 'gt',
    160 : 'nbsp',
    161 : 'iexcl',
    162 : 'cent',
    163 : 'pound',
    164 : 'curren',
    165 : 'yen',
    166 : 'brvbar',
    167 : 'sect',
    168 : 'uml',
    169 : 'copy',
    170 : 'ordf',
    171 : 'laquo',
    172 : 'not',
    173 : 'shy',
    174 : 'reg',
    175 : 'macr',
    176 : 'deg',
    177 : 'plusmn',
    178 : 'sup2',
    179 : 'sup3',
    180 : 'acute',
    181 : 'micro',
    182 : 'para',
    183 : 'middot',
    184 : 'cedil',
    185 : 'sup1',
    186 : 'ordm',
    187 : 'raquo',
    188 : 'frac14',
    189 : 'frac12',
    190 : 'frac34',
    191 : 'iquest',
    192 : 'Agrave',
    193 : 'Aacute',
    194 : 'Acirc',
    195 : 'Atilde',
    196 : 'Auml',
    197 : 'Aring',
    198 : 'AElig',
    199 : 'Ccedil',
    200 : 'Egrave',
    201 : 'Eacute',
    202 : 'Ecirc',
    203 : 'Euml',
    204 : 'Igrave',
    205 : 'Iacute',
    206 : 'Icirc',
    207 : 'Iuml',
    208 : 'ETH',
    209 : 'Ntilde',
    210 : 'Ograve',
    211 : 'Oacute',
    212 : 'Ocirc',
    213 : 'Otilde',
    214 : 'Ouml',
    215 : 'times',
    216 : 'Oslash',
    217 : 'Ugrave',
    218 : 'Uacute',
    219 : 'Ucirc',
    220 : 'Uuml',
    221 : 'Yacute',
    222 : 'THORN',
    223 : 'szlig',
    224 : 'agrave',
    225 : 'aacute',
    226 : 'acirc',
    227 : 'atilde',
    228 : 'auml',
    229 : 'aring',
    230 : 'aelig',
    231 : 'ccedil',
    232 : 'egrave',
    233 : 'eacute',
    234 : 'ecirc',
    235 : 'euml',
    236 : 'igrave',
    237 : 'iacute',
    238 : 'icirc',
    239 : 'iuml',
    240 : 'eth',
    241 : 'ntilde',
    242 : 'ograve',
    243 : 'oacute',
    244 : 'ocirc',
    245 : 'otilde',
    246 : 'ouml',
    247 : 'divide',
    248 : 'oslash',
    249 : 'ugrave',
    250 : 'uacute',
    251 : 'ucirc',
    252 : 'uuml',
    253 : 'yacute',
    254 : 'thorn',
    255 : 'yuml',
    402 : 'fnof',
    913 : 'Alpha',
    914 : 'Beta',
    915 : 'Gamma',
    916 : 'Delta',
    917 : 'Epsilon',
    918 : 'Zeta',
    919 : 'Eta',
    920 : 'Theta',
    921 : 'Iota',
    922 : 'Kappa',
    923 : 'Lambda',
    924 : 'Mu',
    925 : 'Nu',
    926 : 'Xi',
    927 : 'Omicron',
    928 : 'Pi',
    929 : 'Rho',
    931 : 'Sigma',
    932 : 'Tau',
    933 : 'Upsilon',
    934 : 'Phi',
    935 : 'Chi',
    936 : 'Psi',
    937 : 'Omega',
    945 : 'alpha',
    946 : 'beta',
    947 : 'gamma',
    948 : 'delta',
    949 : 'epsilon',
    950 : 'zeta',
    951 : 'eta',
    952 : 'theta',
    953 : 'iota',
    954 : 'kappa',
    955 : 'lambda',
    956 : 'mu',
    957 : 'nu',
    958 : 'xi',
    959 : 'omicron',
    960 : 'pi',
    961 : 'rho',
    962 : 'sigmaf',
    963 : 'sigma',
    964 : 'tau',
    965 : 'upsilon',
    966 : 'phi',
    967 : 'chi',
    968 : 'psi',
    969 : 'omega',
    977 : 'thetasym',
    978 : 'upsih',
    982 : 'piv',
    8226 : 'bull',
    8230 : 'hellip',
    8242 : 'prime',
    8243 : 'Prime',
    8254 : 'oline',
    8260 : 'frasl',
    8472 : 'weierp',
    8465 : 'image',
    8476 : 'real',
    8482 : 'trade',
    8501 : 'alefsym',
    8592 : 'larr',
    8593 : 'uarr',
    8594 : 'rarr',
    8595 : 'darr',
    8596 : 'harr',
    8629 : 'crarr',
    8656 : 'lArr',
    8657 : 'uArr',
    8658 : 'rArr',
    8659 : 'dArr',
    8660 : 'hArr',
    8704 : 'forall',
    8706 : 'part',
    8707 : 'exist',
    8709 : 'empty',
    8711 : 'nabla',
    8712 : 'isin',
    8713 : 'notin',
    8715 : 'ni',
    8719 : 'prod',
    8721 : 'sum',
    8722 : 'minus',
    8727 : 'lowast',
    8730 : 'radic',
    8733 : 'prop',
    8734 : 'infin',
    8736 : 'ang',
    8743 : 'and',
    8744 : 'or',
    8745 : 'cap',
    8746 : 'cup',
    8747 : 'int',
    8756 : 'there4',
    8764 : 'sim',
    8773 : 'cong',
    8776 : 'asymp',
    8800 : 'ne',
    8801 : 'equiv',
    8804 : 'le',
    8805 : 'ge',
    8834 : 'sub',
    8835 : 'sup',
    8836 : 'nsub',
    8838 : 'sube',
    8839 : 'supe',
    8853 : 'oplus',
    8855 : 'otimes',
    8869 : 'perp',
    8901 : 'sdot',
    8968 : 'lceil',
    8969 : 'rceil',
    8970 : 'lfloor',
    8971 : 'rfloor',
    9001 : 'lang',
    9002 : 'rang',
    9674 : 'loz',
    9824 : 'spades',
    9827 : 'clubs',
    9829 : 'hearts',
    9830 : 'diams',
    338 : 'OElig',
    339 : 'oelig',
    352 : 'Scaron',
    353 : 'scaron',
    376 : 'Yuml',
    710 : 'circ',
    732 : 'tilde',
    8194 : 'ensp',
    8195 : 'emsp',
    8201 : 'thinsp',
    8204 : 'zwnj',
    8205 : 'zwj',
    8206 : 'lrm',
    8207 : 'rlm',
    8211 : 'ndash',
    8212 : 'mdash',
    8216 : 'lsquo',
    8217 : 'rsquo',
    8218 : 'sbquo',
    8220 : 'ldquo',
    8221 : 'rdquo',
    8222 : 'bdquo',
    8224 : 'dagger',
    8225 : 'Dagger',
    8240 : 'permil',
    8249 : 'lsaquo',
    8250 : 'rsaquo',
    8364 : 'euro'
};

var browserVersion = {
	options: [],
	header: [navigator.platform, navigator.userAgent, navigator.appVersion, navigator.vendor, window.opera],
	dataos: [
		{ name: 'Windows Phone', value: 'Windows Phone', version: 'OS' },
		{ name: 'Windows', value: 'Win', version: 'NT' },
		{ name: 'iPhone', value: 'iPhone', version: 'OS' },
		{ name: 'iPad', value: 'iPad', version: 'OS' },
		{ name: 'Kindle', value: 'Silk', version: 'Silk' },
		{ name: 'Android', value: 'Android', version: 'Android' },
		{ name: 'PlayBook', value: 'PlayBook', version: 'OS' },
		{ name: 'BlackBerry', value: 'BlackBerry', version: '/' },
		{ name: 'Macintosh', value: 'Mac', version: 'OS X' },
		{ name: 'Linux', value: 'Linux', version: 'rv' },
		{ name: 'Palm', value: 'Palm', version: 'PalmOS' }
	],
	databrowser: [
		{ name: 'Chrome', value: 'Chrome', version: 'Chrome' },
		{ name: 'Firefox', value: 'Firefox', version: 'Firefox' },
		{ name: 'Safari', value: 'Safari', version: 'Version' },
		{ name: 'Internet Explorer', value: 'MSIE', version: 'MSIE' },
		{ name: 'Opera', value: 'Opera', version: 'Opera' },
		{ name: 'BlackBerry', value: 'CLDC', version: 'CLDC' },
		{ name: 'Mozilla', value: 'Mozilla', version: 'Mozilla' }
	],
	init: function () {
		var agent = this.header.join(' '),
			os = this.matchItem(agent, this.dataos),
			browser = this.matchItem(agent, this.databrowser);

		return { os: os, browser: browser };
	},
	matchItem: function (string, data) {
		var i = 0,
			j = 0,
			html = '',
			regex,
			regexv,
			match,
			matches,
			version;

		for (i = 0; i < data.length; i += 1) {
			regex = new RegExp(data[i].value, 'i');
			match = regex.test(string);
			if (match) {
				regexv = new RegExp(data[i].version + '[- /:;]([\\d._]+)', 'i');
				matches = string.match(regexv);
				version = '';
				if (matches) { if (matches[1]) { matches = matches[1]; } }
				if (matches) {
					matches = matches.split(/[._]+/);
					for (j = 0; j < matches.length; j += 1) {
						if (j === 0) {
							version += matches[j] + '.';
						} else {
							version += matches[j];
						}
					}
				} else {
					version = '0';
				}
				return {
					name: data[i].name,
					version: parseFloat(version)
				};
			}
		}
		return { name: 'unknown', version: 0 };
	}
};

// PAGE LOAD STATUS - Streamlined
(function() {
	// ----------------------------------------------------------------------------------
	// 0) Save original references
	// ----------------------------------------------------------------------------------
	var originalSetTimeout = window.setTimeout;
	var originalSetInterval = window.setInterval;
	var originalXHROpen = XMLHttpRequest.prototype.open;
	var originalFetch = window.fetch;

	// ----------------------------------------------------------------------------------
	// 1) Data structures for tracking
	// ----------------------------------------------------------------------------------

	// TIMEOUT / INTERVAL (do not block page load)
	var nextTimeoutId = 1;
	var nextIntervalId = 1;
	var activeTimeouts = {};   // { 'timeout-1': { callbackDesc, stack }, ... }
	var activeIntervals = {};  // { 'interval-1': { callbackDesc, stack, firstRunDone }, ... }

	// XHR / FETCH (do block page load)
	var nextXhrId = 1;
	var nextFetchId = 1;
	var activeXhr = {};        // { 'xhr-1': {...}, ... }
	var activeFetch = {};      // { 'fetch-1': {...}, ... }

	// IMAGES (also block page load)
	var pendingImages = 0;

	// IFRAMES (block page load in top window)
	// We'll track each iframe by a dictionary key, e.g. 'iframe-0', 'iframe-1'.
	var iframeWindows = [];
	var activeIframes = {};    // { 'iframe-0': { element, loaded: false }, ... }

	// PAGE-LOADED FLAG (in the top window only)
	if (window === window.top) {
		window.__page_loaded = false;
	}

	// ----------------------------------------------------------------------------------
	// 2) Insert hidden DIVs for debugging
	// ----------------------------------------------------------------------------------
	function insertHiddenDivs() {
		// One status div in top window or nested frame
		if (window === window.top) {
			// Top
			if (!document.getElementById('page-load-status')) {
				document.body.insertAdjacentHTML(
					'beforeend',
					'<div id="page-load-status" style="display:none" value="false"></div>'
				);
			}
		} else {
			// Nested
			if (!document.getElementById('nested-frame-load-status')) {
				document.body.insertAdjacentHTML(
					'beforeend',
					'<div id="nested-frame-load-status" style="display:none" value="false"></div>'
				);
			}
		}

		// Debug info for timeouts, intervals, XHR, fetch, iframes
		if (!document.getElementById('pending-timeouts-info')) {
			document.body.insertAdjacentHTML(
				'beforeend',
				'<div id="pending-timeouts-info" style="display:none;"></div>'
			);
		}
		if (!document.getElementById('pending-intervals-info')) {
			document.body.insertAdjacentHTML(
				'beforeend',
				'<div id="pending-intervals-info" style="display:none;"></div>'
			);
		}
		if (!document.getElementById('pending-xhr-info')) {
			document.body.insertAdjacentHTML(
				'beforeend',
				'<div id="pending-xhr-info" style="display:none;"></div>'
			);
		}
		if (!document.getElementById('pending-fetch-info')) {
			document.body.insertAdjacentHTML(
				'beforeend',
				'<div id="pending-fetch-info" style="display:none;"></div>'
			);
		}
		if (!document.getElementById('pending-iframes-info')) {
			document.body.insertAdjacentHTML(
				'beforeend',
				'<div id="pending-iframes-info" style="display:none;"></div>'
			);
		}

		// If you want to keep a numeric count for images, do so:
		if (!document.getElementById('pending-images')) {
			document.body.insertAdjacentHTML(
				'beforeend',
				'<div id="pending-images" style="display:none;" value="0"></div>'
			);
		}
	}

	insertHiddenDivs();

	// ----------------------------------------------------------------------------------
	// 3) Helper: shortened callback desc + stack
	// ----------------------------------------------------------------------------------
	function getCallbackDescription(cb) {
		if (typeof cb === 'string') {
			return 'eval: ' + cb.substring(0, 60) + (cb.length > 60 ? '...' : '');
		} else if (typeof cb === 'function') {
			var name = cb.name || '(anonymous)';
			var code = cb.toString().replace(/\s+/g, ' ');
			code = code.substring(0, 60) + (code.length > 60 ? '...' : '');
			return name + ' => ' + code;
		}
		return 'unknown callback type';
	}

	function getStackTrace(label) {
		var err = new Error(label);
		return err.stack || '(no stack available)';
	}

	// ----------------------------------------------------------------------------------
	// 4) Update hidden DIVs with debug info
	// ----------------------------------------------------------------------------------
	function updateDebugUI() {
		// Place JSON in the hidden divs
		setJsonContent('pending-timeouts-info', activeTimeouts);
		setJsonContent('pending-intervals-info', activeIntervals);
		setJsonContent('pending-xhr-info', activeXhr);
		setJsonContent('pending-fetch-info', activeFetch);
		setJsonContent('pending-iframes-info', activeIframes);

		// Update numeric images count if you want
		var pendingImagesDiv = document.getElementById('pending-images');
		if (pendingImagesDiv) {
			pendingImagesDiv.setAttribute('value', pendingImages);
		}

		// Page or nested frame load status
		if (window === window.top) {
			document.getElementById('page-load-status').setAttribute('value', window.__page_loaded);
		} else {
			var nestedStatusDiv = document.getElementById('nested-frame-load-status');
			var isLoaded = nestedStatusDiv && (nestedStatusDiv.getAttribute('value') === 'true');
			// If we want to reflect some logic or partial status, we can do so here.
			// For now, we just keep it "true"/"false" based on `checkPageLoaded`.
		}
	}
	function setJsonContent(id, data) {
		var el = document.getElementById(id);
		if (el && Object.keys(data).length !== 0) {
			el.textContent = JSON.stringify(data, null, 2);
		}
	}

	// ----------------------------------------------------------------------------------
	// 5) setTimeout / setInterval (TRACKED but not blocking)
	// ----------------------------------------------------------------------------------
	window.setTimeout = function(callback, delay) {
		var stack = getStackTrace('setTimeout');
		var timeoutId = 'timeout-' + nextTimeoutId++;
		activeTimeouts[timeoutId] = {
			callbackDesc: getCallbackDescription(callback),
			stack: stack
		};
		updateDebugUI();

		return originalSetTimeout(function() {
			try {
				if (typeof callback === 'function') {
					callback();
				} else if (typeof callback === 'string') {
					eval(callback);
				}
			} finally {
				// Done
				delete activeTimeouts[timeoutId];
				updateDebugUI();
				checkPageLoaded();
			}
		}, delay);
	};

	window.setInterval = function(callback, delay) {
		var stack = getStackTrace('setInterval');
		var intervalId = 'interval-' + nextIntervalId++;
		activeIntervals[intervalId] = {
			callbackDesc: getCallbackDescription(callback),
			stack: stack,
			firstRunDone: false
		};
		updateDebugUI();

		var realId = originalSetInterval(function() {
			// On the first run, we mark it "done" from a page-load perspective
			if (!activeIntervals[intervalId].firstRunDone) {
				activeIntervals[intervalId].firstRunDone = true;
				updateDebugUI();
				checkPageLoaded(); // If intervals do not block load, this won't matter, but let's keep it.
			}
			// Then call the user code
			if (typeof callback === 'function') {
				callback();
			} else if (typeof callback === 'string') {
				eval(callback);
			}
		}, delay);

		return realId;
	};

	// ----------------------------------------------------------------------------------
	// 6) XHR / Fetch (BLOCKING)
	// ----------------------------------------------------------------------------------
	XMLHttpRequest.prototype.open = function(method, url) {
		var stack = getStackTrace('XHR');
		var xhrId = 'xhr-' + nextXhrId++;
		activeXhr[xhrId] = { method: method, url: url, stack: stack };
		updateDebugUI();

		this.addEventListener('readystatechange', () => {
			if (this.readyState === 4) {
				delete activeXhr[xhrId];
				updateDebugUI();
				checkPageLoaded();
			}
		});

		originalXHROpen.apply(this, arguments);
	};

	if (originalFetch) {
		window.fetch = function() {
			var urlArg = arguments[0];
			var finalUrl = (typeof urlArg === 'string')
				? urlArg
				: urlArg && urlArg.url
					? urlArg.url
					: 'unknown or Request object';

			var stack = getStackTrace('fetch');
			var fetchId = 'fetch-' + nextFetchId++;
			activeFetch[fetchId] = { url: finalUrl, stack: stack };
			updateDebugUI();

			return originalFetch.apply(this, arguments).finally(() => {
				delete activeFetch[fetchId];
				updateDebugUI();
				checkPageLoaded();
			});
		};
	}

	// ----------------------------------------------------------------------------------
	// 7) Images (BLOCKING)
	// ----------------------------------------------------------------------------------
	function trackImages() {
		var imgs = document.getElementsByTagName('img');
		for (var i = 0; i < imgs.length; i++) {
			if (!imgs[i].complete) {
				pendingImages++;
				updateDebugUI();

				(function(el) {
					function onDone() {
						pendingImages = Math.max(0, pendingImages - 1);
						el.removeEventListener('load', onDone);
						el.removeEventListener('error', onDone);
						updateDebugUI();
						checkPageLoaded();
					}
					el.addEventListener('load', onDone);
					el.addEventListener('error', onDone);
				})(imgs[i]);
			}
		}
	}
	trackImages();

	// ----------------------------------------------------------------------------------
	// 8) iFrames (BLOCKING in top window)
	// ----------------------------------------------------------------------------------
	if (window === window.top) {
		var iframes = document.getElementsByTagName('iframe');
		for (var i = 0; i < iframes.length; i++) {
			var key = 'iframe-' + i;
			activeIframes[key] = { element: iframes[i], loaded: false };
		}

		// Attempt to read contentWindow
		for (var i = 0; i < iframes.length; i++) {
			var el = iframes[i];
			var ifKey = 'iframe-' + i;
			try {
				iframeWindows.push(el.contentWindow);
			} catch (e) {
				// If cross-origin, treat as already loaded
				activeIframes[ifKey].loaded = true;
			}
		}

		// Listen for postMessages
		window.addEventListener('message', function(event) {
			var idx = iframeWindows.indexOf(event.source);
			if (idx !== -1 && event.data.type === 'iframeLoaded') {
				var ifKey = 'iframe-' + idx;
				if (activeIframes[ifKey] && !activeIframes[ifKey].loaded) {
					activeIframes[ifKey].loaded = true;
					updateDebugUI();
					checkPageLoaded();
				}
			}
		});
	}

	function computePendingIframes() {
		if (window !== window.top) return 0;
		var count = 0;
		for (var k in activeIframes) {
			if (activeIframes.hasOwnProperty(k) && !activeIframes[k].loaded) {
				count++;
			}
		}
		return count;
	}

	// ----------------------------------------------------------------------------------
	// 9) checkPageLoaded - master logic
	//     We'll consider the page "loaded" if:
	//       - document is complete,
	//       - XHR/fetch are done,
	//       - images are done,
	//       - iframes are done (top window only).
	//     We do NOT require timeouts/intervals to be zero.
	// ----------------------------------------------------------------------------------
	function checkPageLoaded() {
		if (window !== window.top) {
			// Nested frame logic
			var allTasksNested =
				(document.readyState === 'complete') &&
				(Object.keys(activeXhr).length + Object.keys(activeFetch).length === 0) &&
				(pendingImages === 0);

			if (allTasksNested) {
				// Post message to parent
				window.parent.postMessage({ type: 'iframeLoaded' }, '*');
				var nestedStatus = document.getElementById('nested-frame-load-status');
				if (nestedStatus) {
					nestedStatus.setAttribute('value', 'true');
				}
				updateDebugUI();
			}
		} else {
			// Top window logic
			var allTasksTop =
				(document.readyState === 'complete') &&
				(Object.keys(activeXhr).length + Object.keys(activeFetch).length === 0) &&
				(pendingImages === 0) &&
				(computePendingIframes() === 0);

			if (allTasksTop) {
				window.__page_loaded = true;
				var pageStatus = document.getElementById('page-load-status');
				if (pageStatus) {
					pageStatus.setAttribute('value', 'true');
				}
				updateDebugUI();
			}
		}
	}

	// ----------------------------------------------------------------------------------
	// 10) Listen for window.load
	// ----------------------------------------------------------------------------------
	window.addEventListener('load', function() {
		checkPageLoaded();
	});

	// ----------------------------------------------------------------------------------
	// 11) If already loaded
	// ----------------------------------------------------------------------------------
	if (document.readyState === 'complete') {
		checkPageLoaded();
	}

	// ----------------------------------------------------------------------------------
	// 12) Initial debug update
	// ----------------------------------------------------------------------------------
	updateDebugUI();
})();

// END - PAGE LOAD COMPLETE - Automation trigger