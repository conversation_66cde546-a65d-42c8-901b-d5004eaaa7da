/*!
* jQuery Mobile 1.4.5
* Git HEAD hash: 68e55e78b292634d3991c795f06f5e37a512decc <> Date: Fri Oct 31 2014 17:33:30 UTC
* http://jquerymobile.com
*
* Copyright 2010, 2014 jQuery Foundation, Inc. and othercontributors
* Released under the MIT license.
* http://jquery.org/license
*
*/


/* SVG icons */
.ui-icon-action:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M9%2C5v3l5-4L9%2C0v3c0%2C0-5%2C0-5%2C7C6%2C5%2C9%2C5%2C9%2C5z%20M11%2C12H2V5h1l2-2H0v11h13V7l-2%2C2V12z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-alert:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M7%2C0L0%2C12h14L7%2C0z%20M7%2C11c-0.553%2C0-1-0.447-1-1s0.447-1%2C1-1c0.553%2C0%2C1%2C0.447%2C1%2C1S7.553%2C11%2C7%2C11z%20M7%2C8%20C6.447%2C8%2C6%2C7.553%2C6%2C7V5c0-0.553%2C0.447-1%2C1-1c0.553%2C0%2C1%2C0.447%2C1%2C1v2C8%2C7.553%2C7.553%2C8%2C7%2C8z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-arrow-d-l:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20fill%3D%22%23FFF%22%20points%3D%2214%2C3%2011%2C0%203.5%2C7.5%200%2C4%200%2C14%2010%2C14%206.5%2C10.5%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-arrow-d-r:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20fill%3D%22%23FFF%22%20points%3D%2210.5%2C7.5%203%2C0%200%2C3%207.5%2C10.5%204%2C14%2014%2C14%2014%2C4%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-arrow-d:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20fill%3D%22%23FFF%22%20points%3D%229%2C7%209%2C0%205%2C0%205%2C7%200%2C7%207%2C14%2014%2C7%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-arrow-l:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20fill%3D%22%23FFF%22%20points%3D%227%2C5%207%2C0%200%2C7%207%2C14%207%2C9%2014%2C9%2014%2C5%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-arrow-r:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20fill%3D%22%23FFF%22%20points%3D%2214%2C7%207%2C0%207%2C5%200%2C5%200%2C9%207%2C9%207%2C14%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-arrow-u-l:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20fill%3D%22%23FFF%22%20points%3D%2214%2C11%206.5%2C3.5%2010%2C0%200%2C0%200%2C10%203.5%2C6.5%2011%2C14%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-arrow-u-r:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20fill%3D%22%23FFF%22%20points%3D%2214%2C0%204%2C0%207.5%2C3.5%200%2C11%203%2C14%2010.5%2C6.5%2014%2C10%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-arrow-u:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20fill%3D%22%23FFF%22%20points%3D%227%2C0%200%2C7%205%2C7%205%2C14%209%2C14%209%2C7%2014%2C7%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-audio:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214.018px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014.018%2014%22%20style%3D%22enable-background%3Anew%200%200%2014.018%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M1%2C4C0.447%2C4%2C0%2C4.447%2C0%2C5v4c0%2C0.553%2C0.447%2C1%2C1%2C1h1l4%2C4V0L2%2C4H1z%20M10.346%2C7c0-1.699-1.042-3.154-2.546-3.867L6.982%2C4.68%20C7.885%2C5.107%2C8.51%2C5.98%2C8.51%2C7S7.885%2C8.893%2C6.982%2C9.32L7.8%2C10.867C9.304%2C10.154%2C10.346%2C8.699%2C10.346%2C7z%20M9.447%2C0.017L8.618%2C1.586%20C10.723%2C2.584%2C12.182%2C4.621%2C12.182%2C7s-1.459%2C4.416-3.563%2C5.414l0.829%2C1.569c2.707-1.283%2C4.57-3.925%2C4.57-6.983%20S12.154%2C1.3%2C9.447%2C0.017z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-back:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M5%2C3V0L1%2C4l4%2C4V5c0%2C0%2C6%2C0%2C6%2C3s-5%2C4-5%2C4v2c0%2C0%2C7-1%2C7-6C13%2C4%2C8%2C3%2C5%2C3z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-bars:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M1%2C4h12c0.553%2C0%2C1-0.447%2C1-1s-0.447-1-1-1H1C0.447%2C2%2C0%2C2.447%2C0%2C3S0.447%2C4%2C1%2C4z%20M13%2C6H1%20C0.447%2C6%2C0%2C6.447%2C0%2C7c0%2C0.553%2C0.447%2C1%2C1%2C1h12c0.553%2C0%2C1-0.447%2C1-1C14%2C6.447%2C13.553%2C6%2C13%2C6z%20M13%2C10H1c-0.553%2C0-1%2C0.447-1%2C1%20s0.447%2C1%2C1%2C1h12c0.553%2C0%2C1-0.447%2C1-1S13.553%2C10%2C13%2C10z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-bullets:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M5%2C4h8c0.553%2C0%2C1-0.447%2C1-1s-0.447-1-1-1H5C4.447%2C2%2C4%2C2.447%2C4%2C3S4.447%2C4%2C5%2C4z%20M13%2C6H5%20C4.447%2C6%2C4%2C6.447%2C4%2C7c0%2C0.553%2C0.447%2C1%2C1%2C1h8c0.553%2C0%2C1-0.447%2C1-1C14%2C6.447%2C13.553%2C6%2C13%2C6z%20M13%2C10H5c-0.553%2C0-1%2C0.447-1%2C1%20s0.447%2C1%2C1%2C1h8c0.553%2C0%2C1-0.447%2C1-1S13.553%2C10%2C13%2C10z%20M1%2C2C0.447%2C2%2C0%2C2.447%2C0%2C3s0.447%2C1%2C1%2C1s1-0.447%2C1-1S1.553%2C2%2C1%2C2z%20M1%2C6%20C0.447%2C6%2C0%2C6.447%2C0%2C7c0%2C0.553%2C0.447%2C1%2C1%2C1s1-0.447%2C1-1C2%2C6.447%2C1.553%2C6%2C1%2C6z%20M1%2C10c-0.553%2C0-1%2C0.447-1%2C1s0.447%2C1%2C1%2C1s1-0.447%2C1-1%20S1.553%2C10%2C1%2C10z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-calendar:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M0%2C8h2V6H0V8z%20M3%2C8h2V6H3V8z%20M6%2C8h2V6H6V8z%20M9%2C8h2V6H9V8z%20M12%2C8h2V6h-2V8z%20M0%2C11h2V9H0V11z%20M3%2C11h2V9H3V11z%20M6%2C11h2V9H6V11z%20%20M9%2C11h2V9H9V11z%20M12%2C11h2V9h-2V11z%20M0%2C14h2v-2H0V14z%20M3%2C14h2v-2H3V14z%20M6%2C14h2v-2H6V14z%20M9%2C14h2v-2H9V14z%20M12%2C1%20c0-0.553-0.447-1-1-1s-1%2C0.447-1%2C1H4c0-0.553-0.447-1-1-1S2%2C0.447%2C2%2C1H0v4h14V1H12z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-camera:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M12%2C2.5H9.908c-0.206-0.581-0.756-1-1.408-1h-3c-0.652%2C0-1.202%2C0.419-1.408%2C1H2c-1.104%2C0-2%2C0.896-2%2C2%20v6c0%2C1.104%2C0.896%2C2%2C2%2C2h10c1.104%2C0%2C2-0.896%2C2-2v-6C14%2C3.396%2C13.104%2C2.5%2C12%2C2.5z%20M7%2C10.5c-1.657%2C0-3-1.344-3-3c0-1.657%2C1.343-3%2C3-3%20s3%2C1.343%2C3%2C3C10%2C9.156%2C8.657%2C10.5%2C7%2C10.5z%20M7%2C5.5c-1.104%2C0-2%2C0.896-2%2C2c0%2C1.104%2C0.896%2C2%2C2%2C2c1.104%2C0%2C2-0.896%2C2-2%20C9%2C6.396%2C8.104%2C5.5%2C7%2C5.5z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-carat-d:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20style%3D%22fill%3A%23FFFFFF%3B%22%20points%3D%2211.949%2C3.404%207%2C8.354%202.05%2C3.404%20-0.071%2C5.525%207%2C12.596%2014.07%2C5.525%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-carat-l:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20style%3D%22fill%3A%23FFFFFF%3B%22%20points%3D%2210.596%2C11.949%205.646%2C7%2010.596%2C2.05%208.475%2C-0.071%201.404%2C7%208.475%2C14.07%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-carat-r:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20style%3D%22fill%3A%23FFFFFF%3B%22%20points%3D%223.404%2C2.051%208.354%2C7%203.404%2C11.95%205.525%2C14.07%2012.596%2C7%205.525%2C-0.071%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-carat-u:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20style%3D%22fill%3A%23FFFFFF%3B%22%20points%3D%222.051%2C10.596%207%2C5.646%2011.95%2C10.596%2014.07%2C8.475%207%2C1.404%20-0.071%2C8.475%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-check:after,
/* Used ui-checkbox-on twice to increase specificity. If active state has background-image for gradient this rule overrides. */
html .ui-btn.ui-checkbox-on.ui-checkbox-on:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20style%3D%22fill%3A%23FFFFFF%3B%22%20points%3D%2214%2C4%2011%2C1%205.003%2C6.997%203%2C5%200%2C8%204.966%2C13%204.983%2C12.982%205%2C13%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-clock:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M7%2C0C3.134%2C0%2C0%2C3.134%2C0%2C7s3.134%2C7%2C7%2C7s7-3.134%2C7-7S10.866%2C0%2C7%2C0z%20M7%2C12c-2.762%2C0-5-2.238-5-5s2.238-5%2C5-5s5%2C2.238%2C5%2C5%20S9.762%2C12%2C7%2C12z%20M9%2C6H8V4c0-0.553-0.447-1-1-1S6%2C3.447%2C6%2C4v3c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1S9.553%2C6%2C9%2C6z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-cloud:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M14%2C9.5c0-0.793-0.465-1.473-1.134-1.795C12.949%2C7.484%2C13%2C7.249%2C13%2C7c0-1.104-0.896-2-2-2%20c-0.158%2C0-0.311%2C0.023-0.457%2C0.058C9.816%2C3.549%2C8.286%2C2.5%2C6.5%2C2.5c-2.33%2C0-4.224%2C1.777-4.454%2C4.046C0.883%2C6.76%2C0%2C7.773%2C0%2C9%20c0%2C1.381%2C1.119%2C2.5%2C2.5%2C2.5h10v-0.07C13.361%2C11.206%2C14%2C10.432%2C14%2C9.5z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-comment:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M12%2C0H2C0.896%2C0%2C0%2C0.896%2C0%2C2v7c0%2C1.104%2C0.896%2C2%2C2%2C2h1v3l3-3h6c1.104%2C0%2C2-0.896%2C2-2V2C14%2C0.896%2C13.104%2C0%2C12%2C0z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-delete:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20fill%3D%22%23FFF%22%20points%3D%2214%2C3%2011%2C0%207%2C4%203%2C0%200%2C3%204%2C7%200%2C11%203%2C14%207%2C10%2011%2C14%2014%2C11%2010%2C7%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-edit:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M1%2C10l-1%2C4l4-1l7-7L8%2C3L1%2C10z%20M11%2C0L9%2C2l3%2C3l2-2L11%2C0z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-eye:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M7%2C2C3%2C2%2C0%2C7%2C0%2C7s3%2C5%2C7%2C5s7-5%2C7-5S11%2C2%2C7%2C2z%20M7%2C10c-1.657%2C0-3-1.344-3-3c0-1.657%2C1.343-3%2C3-3%20s3%2C1.343%2C3%2C3C10%2C8.656%2C8.657%2C10%2C7%2C10z%20M7%2C6C6.448%2C6%2C6%2C6.447%2C6%2C7c0%2C0.553%2C0.448%2C1%2C1%2C1s1-0.447%2C1-1C8%2C6.447%2C7.552%2C6%2C7%2C6z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-forbidden:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M12.601%2C11.187C13.476%2C10.018%2C14%2C8.572%2C14%2C7c0-3.866-3.134-7-7-7C5.428%2C0%2C3.982%2C0.524%2C2.813%2C1.399L2.757%2C1.343L2.053%2C2.048%20L2.048%2C2.053L1.343%2C2.758l0.056%2C0.056C0.524%2C3.982%2C0%2C5.428%2C0%2C7c0%2C3.866%2C3.134%2C7%2C7%2C7c1.572%2C0%2C3.018-0.524%2C4.187-1.399l0.056%2C0.057%20l0.705-0.705l0.005-0.005l0.705-0.705L12.601%2C11.187z%20M7%2C2c2.761%2C0%2C5%2C2.238%2C5%2C5c0%2C1.019-0.308%2C1.964-0.832%2C2.754L4.246%2C2.832%20C5.036%2C2.308%2C5.981%2C2%2C7%2C2z%20M7%2C12c-2.761%2C0-5-2.238-5-5c0-1.019%2C0.308-1.964%2C0.832-2.754l6.922%2C6.922C8.964%2C11.692%2C8.019%2C12%2C7%2C12z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-forward:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M13%2C4L9%2C0v3C6%2C3%2C1%2C4%2C1%2C8c0%2C5%2C7%2C6%2C7%2C6v-2c0%2C0-5-1-5-4s6-3%2C6-3v3L13%2C4z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-gear:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M13.621%2C5.904l-1.036-0.259c-0.168-0.042-0.303-0.168-0.355-0.332c-0.092-0.284-0.205-0.559-0.339-0.82%20c-0.079-0.153-0.073-0.337%2C0.017-0.486l0.549-0.915c0.118-0.196%2C0.088-0.448-0.075-0.61l-0.862-0.863%20c-0.162-0.163-0.414-0.193-0.611-0.075l-0.916%2C0.55C9.844%2C2.182%2C9.659%2C2.188%2C9.506%2C2.109C9.244%2C1.975%2C8.97%2C1.861%2C8.686%2C1.77%20c-0.165-0.052-0.29-0.187-0.332-0.354L8.095%2C0.379C8.039%2C0.156%2C7.839%2C0%2C7.609%2C0H6.391c-0.229%2C0-0.43%2C0.156-0.485%2C0.379L5.646%2C1.415%20C5.604%2C1.582%2C5.479%2C1.718%2C5.313%2C1.77c-0.284%2C0.092-0.559%2C0.206-0.82%2C0.34C4.339%2C2.188%2C4.155%2C2.182%2C4.007%2C2.093L3.092%2C1.544%20c-0.196-0.118-0.448-0.087-0.61%2C0.075L1.619%2C2.481C1.457%2C2.644%2C1.426%2C2.896%2C1.544%2C3.093l0.549%2C0.914%20c0.089%2C0.148%2C0.095%2C0.332%2C0.017%2C0.486C1.975%2C4.755%2C1.861%2C5.029%2C1.77%2C5.314c-0.053%2C0.164-0.188%2C0.29-0.354%2C0.332L0.379%2C5.905%20C0.156%2C5.961%2C0%2C6.161%2C0%2C6.391v1.219c0%2C0.229%2C0.156%2C0.43%2C0.379%2C0.485l1.036%2C0.26C1.582%2C8.396%2C1.717%2C8.521%2C1.77%2C8.687%20c0.092%2C0.284%2C0.205%2C0.559%2C0.34%2C0.82C2.188%2C9.66%2C2.182%2C9.844%2C2.093%2C9.993l-0.549%2C0.915c-0.118%2C0.195-0.087%2C0.448%2C0.075%2C0.61%20l0.862%2C0.862c0.162%2C0.163%2C0.414%2C0.193%2C0.61%2C0.075l0.915-0.549c0.148-0.089%2C0.332-0.095%2C0.486-0.017%20c0.262%2C0.135%2C0.536%2C0.248%2C0.82%2C0.34c0.165%2C0.053%2C0.291%2C0.187%2C0.332%2C0.354l0.259%2C1.036C5.96%2C13.844%2C6.16%2C14%2C6.39%2C14h1.22%20c0.229%2C0%2C0.43-0.156%2C0.485-0.379l0.259-1.036c0.042-0.167%2C0.168-0.302%2C0.333-0.354c0.284-0.092%2C0.559-0.205%2C0.82-0.34%20c0.154-0.078%2C0.338-0.072%2C0.486%2C0.017l0.914%2C0.549c0.197%2C0.118%2C0.449%2C0.088%2C0.611-0.074l0.862-0.863%20c0.163-0.162%2C0.193-0.415%2C0.075-0.611l-0.549-0.915c-0.089-0.148-0.096-0.332-0.017-0.485c0.134-0.263%2C0.248-0.536%2C0.339-0.82%20c0.053-0.165%2C0.188-0.291%2C0.355-0.333l1.036-0.259C13.844%2C8.039%2C14%2C7.839%2C14%2C7.609V6.39C14%2C6.16%2C13.844%2C5.96%2C13.621%2C5.904z%20M7%2C10%20c-1.657%2C0-3-1.343-3-3s1.343-3%2C3-3s3%2C1.343%2C3%2C3S8.657%2C10%2C7%2C10z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-grid:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M3%2C0H1C0.447%2C0%2C0%2C0.447%2C0%2C1v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1V1C4%2C0.447%2C3.553%2C0%2C3%2C0z%20M8%2C0H6%20C5.447%2C0%2C5%2C0.447%2C5%2C1v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1V1C9%2C0.447%2C8.553%2C0%2C8%2C0z%20M13%2C0h-2c-0.553%2C0-1%2C0.447-1%2C1v2%20c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1V1C14%2C0.447%2C13.553%2C0%2C13%2C0z%20M3%2C5H1C0.447%2C5%2C0%2C5.447%2C0%2C6v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2%20c0.553%2C0%2C1-0.447%2C1-1V6C4%2C5.447%2C3.553%2C5%2C3%2C5z%20M8%2C5H6C5.447%2C5%2C5%2C5.447%2C5%2C6v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1V6%20C9%2C5.447%2C8.553%2C5%2C8%2C5z%20M13%2C5h-2c-0.553%2C0-1%2C0.447-1%2C1v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1V6C14%2C5.447%2C13.553%2C5%2C13%2C5z%20M3%2C10%20H1c-0.553%2C0-1%2C0.447-1%2C1v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1v-2C4%2C10.447%2C3.553%2C10%2C3%2C10z%20M8%2C10H6c-0.553%2C0-1%2C0.447-1%2C1v2%20c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1v-2C9%2C10.447%2C8.553%2C10%2C8%2C10z%20M13%2C10h-2c-0.553%2C0-1%2C0.447-1%2C1v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2%20c0.553%2C0%2C1-0.447%2C1-1v-2C14%2C10.447%2C13.553%2C10%2C13%2C10z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-heart:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M7%2C1.872c-2-3-7-2-7%2C2c0%2C3%2C4%2C7%2C4%2C7s2.417%2C2.479%2C3%2C3c0.583-0.521%2C3-3%2C3-3s4-4%2C4-7%20C14-0.128%2C9-1.128%2C7%2C1.872z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-home:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20fill%3D%22%23FFF%22%20points%3D%227%2C0%200%2C7%202%2C7%202%2C14%205%2C14%205%2C9%209%2C9%209%2C14%2012%2C14%2012%2C7%2014%2C7%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-info:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M7%2C0C3.134%2C0%2C0%2C3.134%2C0%2C7s3.134%2C7%2C7%2C7s7-3.134%2C7-7S10.866%2C0%2C7%2C0z%20M7%2C2c0.552%2C0%2C1%2C0.447%2C1%2C1S7.552%2C4%2C7%2C4S6%2C3.553%2C6%2C3%20S6.448%2C2%2C7%2C2z%20M9%2C11H5v-1h1V6H5V5h3v5h1V11z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-location:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M7%2C0C4.791%2C0%2C3%2C1.791%2C3%2C4c0%2C2%2C4%2C10%2C4%2C10s4-8%2C4-10C11%2C1.791%2C9.209%2C0%2C7%2C0z%20M7%2C6C5.896%2C6%2C5%2C5.104%2C5%2C4%20s0.896-2%2C2-2c1.104%2C0%2C2%2C0.896%2C2%2C2S8.104%2C6%2C7%2C6z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-lock:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M12%2C6V5c0-2.762-2.238-5-5-5C4.239%2C0%2C2%2C2.238%2C2%2C5v1H1v8h12V6H12z%20M7.5%2C9.848V12h-1V9.848%20C6.207%2C9.673%2C6%2C9.366%2C6%2C9c0-0.553%2C0.448-1%2C1-1s1%2C0.447%2C1%2C1C8%2C9.366%2C7.793%2C9.673%2C7.5%2C9.848z%20M10%2C6H4V5c0-1.657%2C1.343-3%2C3-3%20s3%2C1.343%2C3%2C3V6z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-mail:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M0%2C3.75V12h14V3.75L7%2C9L0%2C3.75z%20M14%2C2H0l7%2C5L14%2C2z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-minus:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Crect%20y%3D%225%22%20style%3D%22fill%3A%23FFFFFF%3B%22%20width%3D%2214%22%20height%3D%224%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-navigation:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20style%3D%22fill%3A%23FFFFFF%3B%22%20points%3D%2213%2C1%200%2C6%207%2C7%208%2C14%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-phone:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%22-0.01%200.008%2014%2014%22%20style%3D%22enable-background%3Anew%20-0.01%200.008%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M6.939%2C9.189C6.165%2C8.557%2C5.271%2C7.705%2C4.497%2C6.744C3.953%2C6.071%2C3.473%2C5.363%2C3.969%2C4.866l-3.482-3.48%20C-0.021%2C2.02-1.146%2C5.04%2C3.675%2C9.984c5.08%2C5.211%2C8.356%2C4.097%2C8.92%2C3.511l-3.396-3.4C8.725%2C10.568%2C8.113%2C10.146%2C6.939%2C9.189z%20%20M13.82%2C11.519v-0.004c0%2C0-2.648-2.646-2.649-2.647c-0.21-0.211-0.546-0.205-0.754%2C0.002L9.455%2C9.831l3.403%2C3.407%20c0%2C0%2C0.962-0.96%2C0.961-0.961l0.002-0.001C14.043%2C12.056%2C14.021%2C11.721%2C13.82%2C11.519z%20M5.192%2C3.644V3.642%20c0.222-0.222%2C0.2-0.557%2C0-0.758V2.881c0%2C0-2.726-2.725-2.727-2.726C2.255-0.055%2C1.92-0.05%2C1.712%2C0.156L0.751%2C1.121l3.479%2C3.482%20C4.231%2C4.604%2C5.192%2C3.645%2C5.192%2C3.644z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-plus:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20fill%3D%22%23FFF%22%20points%3D%2214%2C5%209%2C5%209%2C0%205%2C0%205%2C5%200%2C5%200%2C9%205%2C9%205%2C14%209%2C14%209%2C9%2014%2C9%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-power:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M11.243%2C2.408c-0.392-0.401-1.024-0.401-1.415%2C0c-0.391%2C0.401-0.391%2C1.054%2C0%2C1.455%20C10.584%2C4.642%2C11%2C5.675%2C11%2C6.773s-0.416%2C2.133-1.172%2C2.91c-1.512%2C1.558-4.145%2C1.558-5.656%2C0C3.416%2C8.904%2C3%2C7.872%2C3%2C6.773%20C3%2C5.673%2C3.416%2C4.64%2C4.172%2C3.863c0.39-0.401%2C0.39-1.054%2C0-1.455c-0.391-0.401-1.024-0.401-1.415%2C0C1.624%2C3.574%2C1%2C5.125%2C1%2C6.773%20c0%2C1.647%2C0.624%2C3.199%2C1.757%2C4.365c1.134%2C1.166%2C2.64%2C1.809%2C4.243%2C1.809c1.604%2C0%2C3.109-0.645%2C4.243-1.811%20C12.376%2C9.975%2C13%2C8.423%2C13%2C6.773C13%2C5.125%2C12.376%2C3.574%2C11.243%2C2.408z%20M7%2C8.053c0.553%2C0%2C1-0.445%2C1-1v-6c0-0.553-0.447-1-1-1%20c-0.553%2C0-1%2C0.447-1%2C1v6C6%2C7.604%2C6.447%2C8.053%2C7%2C8.053z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-recycle:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M3%2C7h1L2%2C4L0%2C7h1c0%2C3.313%2C2.687%2C6%2C6%2C6c0.702%2C0%2C1.374-0.127%2C2-0.35v-2.205C8.41%2C10.789%2C7.732%2C11%2C7%2C11%20C4.791%2C11%2C3%2C9.209%2C3%2C7z%20M13%2C7c0-3.313-2.688-6-6-6C6.298%2C1%2C5.626%2C1.127%2C5%2C1.349v2.206C5.59%2C3.211%2C6.268%2C3%2C7%2C3c2.209%2C0%2C4%2C1.791%2C4%2C4%20h-1l2%2C3l2-3H13z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-refresh:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214.001px%22%20height%3D%2214.002px%22%20viewBox%3D%220%200%2014.001%2014.002%22%20style%3D%22enable-background%3Anew%200%200%2014.001%2014.002%3B%22%20%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M14.001%2C6.001v-6l-2.06%2C2.06c-0.423-0.424-0.897-0.809-1.44-1.122C7.153-0.994%2C2.872%2C0.153%2C0.939%2C3.501%20c-1.933%2C3.348-0.786%2C7.629%2C2.562%2C9.562c3.348%2C1.933%2C7.629%2C0.785%2C9.562-2.562l-1.732-1c-1.381%2C2.392-4.438%2C3.211-6.83%2C1.83%20s-3.211-4.438-1.83-6.83s4.438-3.211%2C6.83-1.83c0.389%2C0.225%2C0.718%2C0.506%2C1.02%2C0.81l-2.52%2C2.52H14.001z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-search:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M10.171%2C8.766c0.617-0.888%2C0.979-1.964%2C0.979-3.126c0-3.037-2.463-5.5-5.5-5.5s-5.5%2C2.463-5.5%2C5.5%20s2.463%2C5.5%2C5.5%2C5.5c1.152%2C0%2C2.223-0.355%2C3.104-0.962l3.684%2C3.683l1.414-1.414L10.171%2C8.766z%20M5.649%2C9.14c-1.933%2C0-3.5-1.567-3.5-3.5%20c0-1.933%2C1.567-3.5%2C3.5-3.5c1.933%2C0%2C3.5%2C1.567%2C3.5%2C3.5C9.149%2C7.572%2C7.582%2C9.14%2C5.649%2C9.14z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-shop:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M10%2C4V3c0-1.657-1.343-3-3-3S4%2C1.343%2C4%2C3v1H1v10h12V4H10z%20M4.5%2C6C4.224%2C6%2C4%2C5.776%2C4%2C5.5%20S4.224%2C5%2C4.5%2C5S5%2C5.224%2C5%2C5.5S4.776%2C6%2C4.5%2C6z%20M5%2C3c0-1.104%2C0.896-2%2C2-2c1.104%2C0%2C2%2C0.896%2C2%2C2v1H5V3z%20M9.5%2C6C9.225%2C6%2C9%2C5.776%2C9%2C5.5%20S9.225%2C5%2C9.5%2C5S10%2C5.224%2C10%2C5.5S9.775%2C6%2C9.5%2C6z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-star:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20style%3D%22fill%3A%23FFFFFF%3B%22%20points%3D%2214%2C5%209%2C5%207%2C0%205%2C5%200%2C5%204%2C8%202.625%2C13%207%2C10%2011.375%2C13%2010%2C8%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-icon-tag:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M5%2C0H0v5l9%2C9l5-5L5%2C0z%20M3%2C4C2.447%2C4%2C2%2C3.553%2C2%2C3s0.447-1%2C1-1s1%2C0.447%2C1%2C1S3.553%2C4%2C3%2C4z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-user:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M8.851%2C10.101c-0.18-0.399-0.2-0.763-0.153-1.104C9.383%2C8.49%2C9.738%2C7.621%2C9.891%2C6.465C10.493%2C6.355%2C10.5%2C5.967%2C10.5%2C5.5%20c0-0.437-0.008-0.804-0.502-0.94C9.999%2C4.539%2C10%2C4.521%2C10%2C4.5c0-2.103-1-4-2-4C8%2C0.5%2C7.5%2C0%2C6.5%2C0C5%2C0%2C4%2C1.877%2C4%2C4.5%20c0%2C0.021%2C0.001%2C0.039%2C0.002%2C0.06C3.508%2C4.696%2C3.5%2C5.063%2C3.5%2C5.5c0%2C0.467%2C0.007%2C0.855%2C0.609%2C0.965%20C4.262%2C7.621%2C4.617%2C8.49%2C5.303%2C8.997c0.047%2C0.341%2C0.026%2C0.704-0.153%2C1.104C1.503%2C10.503%2C0%2C12%2C0%2C12v2h14v-2%20C14%2C12%2C12.497%2C10.503%2C8.851%2C10.101z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-icon-video:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%20-2%2014%2014%22%20style%3D%22enable-background%3Anew%200%20-2%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20style%3D%22fill%3A%23FFFFFF%3B%22%20d%3D%22M8%2C0H2C0.896%2C0%2C0%2C0.896%2C0%2C2v6c0%2C1.104%2C0.896%2C2%2C2%2C2h6c1.104%2C0%2C2-0.896%2C2-2V5V2C10%2C0.896%2C9.104%2C0%2C8%2C0z%20%20M10%2C5l4%2C4V1L10%2C5z%22%2F%3E%3C%2Fsvg%3E");
}
/* Alt icons */
.ui-alt-icon.ui-icon-action:after,
.ui-alt-icon .ui-icon-action:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M9%2C5v3l5-4L9%2C0v3c0%2C0-5%2C0-5%2C7C6%2C5%2C9%2C5%2C9%2C5z%20M11%2C12H2V5h1l2-2H0v11h13V7l-2%2C2V12z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-alert:after,
.ui-alt-icon .ui-icon-alert:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M7%2C0L0%2C12h14L7%2C0z%20M7%2C11c-0.553%2C0-1-0.447-1-1s0.447-1%2C1-1c0.553%2C0%2C1%2C0.447%2C1%2C1S7.553%2C11%2C7%2C11z%20M7%2C8C6.447%2C8%2C6%2C7.553%2C6%2C7V5%20c0-0.553%2C0.447-1%2C1-1c0.553%2C0%2C1%2C0.447%2C1%2C1v2C8%2C7.553%2C7.553%2C8%2C7%2C8z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-arrow-d:after,
.ui-alt-icon .ui-icon-arrow-d:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%229%2C7%209%2C0%205%2C0%205%2C7%200%2C7%207%2C14%2014%2C7%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-arrow-d-l:after,
.ui-alt-icon .ui-icon-arrow-d-l:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%2214%2C3%2011%2C0%203.5%2C7.5%200%2C4%200%2C14%2010%2C14%206.5%2C10.5%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-arrow-d-r:after,
.ui-alt-icon .ui-icon-arrow-d-r:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%2210.5%2C7.5%203%2C0%200%2C3%207.5%2C10.5%204%2C14%2014%2C14%2014%2C4%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-arrow-l:after,
.ui-alt-icon .ui-icon-arrow-l:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%227%2C5%207%2C0%200%2C7%207%2C14%207%2C9%2014%2C9%2014%2C5%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-arrow-r:after,
.ui-alt-icon .ui-icon-arrow-r:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%2214%2C7%207%2C0%207%2C5%200%2C5%200%2C9%207%2C9%207%2C14%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-arrow-u:after,
.ui-alt-icon .ui-icon-arrow-u:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%227%2C0%200%2C7%205%2C7%205%2C14%209%2C14%209%2C7%2014%2C7%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-arrow-u-l:after,
.ui-alt-icon .ui-icon-arrow-u-l:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%2214%2C11%206.5%2C3.5%2010%2C0%200%2C0%200%2C10%203.5%2C6.5%2011%2C14%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-arrow-u-r:after,
.ui-alt-icon .ui-icon-arrow-u-r:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%2214%2C0%204%2C0%207.5%2C3.5%200%2C11%203%2C14%2010.5%2C6.5%2014%2C10%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-audio:after,
.ui-alt-icon .ui-icon-audio:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214.018px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014.018%2014%22%20style%3D%22enable-background%3Anew%200%200%2014.018%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M1%2C4C0.447%2C4%2C0%2C4.447%2C0%2C5v4c0%2C0.553%2C0.447%2C1%2C1%2C1h1l4%2C4V0L2%2C4H1z%20M10.346%2C7c0-1.699-1.042-3.154-2.546-3.867L6.982%2C4.68%20C7.885%2C5.107%2C8.51%2C5.98%2C8.51%2C7S7.885%2C8.893%2C6.982%2C9.32L7.8%2C10.867C9.304%2C10.154%2C10.346%2C8.699%2C10.346%2C7z%20M9.447%2C0.017L8.618%2C1.586%20C10.723%2C2.584%2C12.182%2C4.621%2C12.182%2C7s-1.459%2C4.416-3.563%2C5.414l0.829%2C1.569c2.707-1.283%2C4.57-3.925%2C4.57-6.983%20S12.154%2C1.3%2C9.447%2C0.017z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-back:after,
.ui-alt-icon .ui-icon-back:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M5%2C3V0L1%2C4l4%2C4V5c0%2C0%2C6%2C0%2C6%2C3s-5%2C4-5%2C4v2c0%2C0%2C7-1%2C7-6C13%2C4%2C8%2C3%2C5%2C3z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-bars:after,
.ui-alt-icon .ui-icon-bars:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M1%2C4h12c0.553%2C0%2C1-0.447%2C1-1s-0.447-1-1-1H1C0.447%2C2%2C0%2C2.447%2C0%2C3S0.447%2C4%2C1%2C4z%20M13%2C6H1C0.447%2C6%2C0%2C6.447%2C0%2C7%20c0%2C0.553%2C0.447%2C1%2C1%2C1h12c0.553%2C0%2C1-0.447%2C1-1C14%2C6.447%2C13.553%2C6%2C13%2C6z%20M13%2C10H1c-0.553%2C0-1%2C0.447-1%2C1s0.447%2C1%2C1%2C1h12%20c0.553%2C0%2C1-0.447%2C1-1S13.553%2C10%2C13%2C10z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-bullets:after,
.ui-alt-icon .ui-icon-bullets:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M5%2C4h8c0.553%2C0%2C1-0.447%2C1-1s-0.447-1-1-1H5C4.447%2C2%2C4%2C2.447%2C4%2C3S4.447%2C4%2C5%2C4z%20M13%2C6H5C4.447%2C6%2C4%2C6.447%2C4%2C7%20c0%2C0.553%2C0.447%2C1%2C1%2C1h8c0.553%2C0%2C1-0.447%2C1-1C14%2C6.447%2C13.553%2C6%2C13%2C6z%20M13%2C10H5c-0.553%2C0-1%2C0.447-1%2C1s0.447%2C1%2C1%2C1h8%20c0.553%2C0%2C1-0.447%2C1-1S13.553%2C10%2C13%2C10z%20M1%2C2C0.447%2C2%2C0%2C2.447%2C0%2C3s0.447%2C1%2C1%2C1s1-0.447%2C1-1S1.553%2C2%2C1%2C2z%20M1%2C6C0.447%2C6%2C0%2C6.447%2C0%2C7%20c0%2C0.553%2C0.447%2C1%2C1%2C1s1-0.447%2C1-1C2%2C6.447%2C1.553%2C6%2C1%2C6z%20M1%2C10c-0.553%2C0-1%2C0.447-1%2C1s0.447%2C1%2C1%2C1s1-0.447%2C1-1S1.553%2C10%2C1%2C10z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-calendar:after,
.ui-alt-icon .ui-icon-calendar:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M0%2C8h2V6H0V8z%20M3%2C8h2V6H3V8z%20M6%2C8h2V6H6V8z%20M9%2C8h2V6H9V8z%20M12%2C8h2V6h-2V8z%20M0%2C11h2V9H0V11z%20M3%2C11h2V9H3V11z%20M6%2C11h2V9H6V11z%20%20M9%2C11h2V9H9V11z%20M12%2C11h2V9h-2V11z%20M0%2C14h2v-2H0V14z%20M3%2C14h2v-2H3V14z%20M6%2C14h2v-2H6V14z%20M9%2C14h2v-2H9V14z%20M12%2C1%20c0-0.553-0.447-1-1-1s-1%2C0.447-1%2C1H4c0-0.553-0.447-1-1-1S2%2C0.447%2C2%2C1H0v4h14V1H12z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-camera:after,
.ui-alt-icon .ui-icon-camera:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M12%2C2.5H9.908c-0.206-0.581-0.756-1-1.408-1h-3c-0.652%2C0-1.202%2C0.419-1.408%2C1H2c-1.104%2C0-2%2C0.896-2%2C2v6c0%2C1.104%2C0.896%2C2%2C2%2C2%20h10c1.104%2C0%2C2-0.896%2C2-2v-6C14%2C3.396%2C13.104%2C2.5%2C12%2C2.5z%20M7%2C10.5c-1.657%2C0-3-1.344-3-3c0-1.657%2C1.343-3%2C3-3s3%2C1.343%2C3%2C3%20C10%2C9.156%2C8.657%2C10.5%2C7%2C10.5z%20M7%2C5.5c-1.104%2C0-2%2C0.896-2%2C2c0%2C1.104%2C0.896%2C2%2C2%2C2c1.104%2C0%2C2-0.896%2C2-2C9%2C6.396%2C8.104%2C5.5%2C7%2C5.5z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-carat-d:after,
.ui-alt-icon .ui-icon-carat-d:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%2211.949%2C3.404%207%2C8.354%202.05%2C3.404%20-0.071%2C5.525%207%2C12.596%2014.07%2C5.525%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-carat-l:after,
.ui-alt-icon .ui-icon-carat-l:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%2210.596%2C11.949%205.646%2C7%2010.596%2C2.05%208.475%2C-0.071%201.404%2C7%208.475%2C14.07%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-carat-r:after,
.ui-alt-icon .ui-icon-carat-r:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%223.404%2C2.051%208.354%2C7%203.404%2C11.95%205.525%2C14.07%2012.596%2C7%205.525%2C-0.071%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-carat-u:after,
.ui-alt-icon .ui-icon-carat-u:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%222.051%2C10.596%207%2C5.646%2011.95%2C10.596%2014.07%2C8.475%207%2C1.404%20-0.071%2C8.475%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-check:after,
.ui-alt-icon .ui-icon-check:after,
html .ui-alt-icon.ui-btn.ui-checkbox-on:after,
html .ui-alt-icon .ui-btn.ui-checkbox-on:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%2214%2C4%2011%2C1%205.003%2C6.997%203%2C5%200%2C8%204.966%2C13%204.983%2C12.982%205%2C13%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-clock:after,
.ui-alt-icon .ui-icon-clock:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M7%2C0C3.134%2C0%2C0%2C3.134%2C0%2C7s3.134%2C7%2C7%2C7s7-3.134%2C7-7S10.866%2C0%2C7%2C0z%20M7%2C12c-2.762%2C0-5-2.238-5-5s2.238-5%2C5-5s5%2C2.238%2C5%2C5%20S9.762%2C12%2C7%2C12z%20M9%2C6H8V4c0-0.553-0.447-1-1-1S6%2C3.447%2C6%2C4v3c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1S9.553%2C6%2C9%2C6z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-cloud:after,
.ui-alt-icon .ui-icon-cloud:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M14%2C9.5c0-0.793-0.465-1.473-1.134-1.795C12.949%2C7.484%2C13%2C7.249%2C13%2C7c0-1.104-0.896-2-2-2c-0.158%2C0-0.311%2C0.023-0.457%2C0.058%20C9.816%2C3.549%2C8.286%2C2.5%2C6.5%2C2.5c-2.33%2C0-4.224%2C1.777-4.454%2C4.046C0.883%2C6.76%2C0%2C7.773%2C0%2C9c0%2C1.381%2C1.119%2C2.5%2C2.5%2C2.5h10v-0.07%20C13.361%2C11.206%2C14%2C10.432%2C14%2C9.5z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-comment:after,
.ui-alt-icon .ui-icon-comment:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M12%2C0H2C0.896%2C0%2C0%2C0.896%2C0%2C2v7c0%2C1.104%2C0.896%2C2%2C2%2C2h1v3l3-3h6c1.104%2C0%2C2-0.896%2C2-2V2C14%2C0.896%2C13.104%2C0%2C12%2C0z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-delete:after,
.ui-alt-icon .ui-icon-delete:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%2214%2C3%2011%2C0%207%2C4%203%2C0%200%2C3%204%2C7%200%2C11%203%2C14%207%2C10%2011%2C14%2014%2C11%2010%2C7%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-edit:after,
.ui-alt-icon .ui-icon-edit:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M1%2C10l-1%2C4l4-1l7-7L8%2C3L1%2C10z%20M11%2C0L9%2C2l3%2C3l2-2L11%2C0z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-eye:after,
.ui-alt-icon .ui-icon-eye:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M7%2C2C3%2C2%2C0%2C7%2C0%2C7s3%2C5%2C7%2C5s7-5%2C7-5S11%2C2%2C7%2C2z%20M7%2C10c-1.657%2C0-3-1.344-3-3c0-1.657%2C1.343-3%2C3-3s3%2C1.343%2C3%2C3%20C10%2C8.656%2C8.657%2C10%2C7%2C10z%20M7%2C6C6.448%2C6%2C6%2C6.447%2C6%2C7c0%2C0.553%2C0.448%2C1%2C1%2C1s1-0.447%2C1-1C8%2C6.447%2C7.552%2C6%2C7%2C6z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-forbidden:after,
.ui-alt-icon .ui-icon-forbidden:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M12.601%2C11.187C13.476%2C10.018%2C14%2C8.572%2C14%2C7c0-3.866-3.134-7-7-7C5.428%2C0%2C3.982%2C0.524%2C2.813%2C1.399L2.757%2C1.343L2.053%2C2.048%20L2.048%2C2.053L1.343%2C2.758l0.056%2C0.056C0.524%2C3.982%2C0%2C5.428%2C0%2C7c0%2C3.866%2C3.134%2C7%2C7%2C7c1.572%2C0%2C3.018-0.524%2C4.187-1.399l0.056%2C0.057%20l0.705-0.705l0.005-0.005l0.705-0.705L12.601%2C11.187z%20M7%2C2c2.761%2C0%2C5%2C2.238%2C5%2C5c0%2C1.019-0.308%2C1.964-0.832%2C2.754L4.246%2C2.832%20C5.036%2C2.308%2C5.981%2C2%2C7%2C2z%20M7%2C12c-2.761%2C0-5-2.238-5-5c0-1.019%2C0.308-1.964%2C0.832-2.754l6.922%2C6.922C8.964%2C11.692%2C8.019%2C12%2C7%2C12z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-forward:after,
.ui-alt-icon .ui-icon-forward:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M13%2C4L9%2C0v3C6%2C3%2C1%2C4%2C1%2C8c0%2C5%2C7%2C6%2C7%2C6v-2c0%2C0-5-1-5-4s6-3%2C6-3v3L13%2C4z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-gear:after,
.ui-alt-icon .ui-icon-gear:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M13.621%2C5.904l-1.036-0.259c-0.168-0.042-0.303-0.168-0.355-0.332c-0.092-0.284-0.205-0.559-0.339-0.82%20c-0.079-0.153-0.073-0.337%2C0.017-0.486l0.549-0.915c0.118-0.196%2C0.088-0.448-0.075-0.61l-0.862-0.863%20c-0.162-0.163-0.414-0.193-0.611-0.075l-0.916%2C0.55C9.844%2C2.182%2C9.659%2C2.188%2C9.506%2C2.109C9.244%2C1.975%2C8.97%2C1.861%2C8.686%2C1.77%20c-0.165-0.052-0.29-0.187-0.332-0.354L8.095%2C0.379C8.039%2C0.156%2C7.839%2C0%2C7.609%2C0H6.391c-0.229%2C0-0.43%2C0.156-0.485%2C0.379L5.646%2C1.415%20C5.604%2C1.582%2C5.479%2C1.718%2C5.313%2C1.77c-0.284%2C0.092-0.559%2C0.206-0.82%2C0.34C4.339%2C2.188%2C4.155%2C2.182%2C4.007%2C2.093L3.092%2C1.544%20c-0.196-0.118-0.448-0.087-0.61%2C0.075L1.619%2C2.481C1.457%2C2.644%2C1.426%2C2.896%2C1.544%2C3.093l0.549%2C0.914%20c0.089%2C0.148%2C0.095%2C0.332%2C0.017%2C0.486C1.975%2C4.755%2C1.861%2C5.029%2C1.77%2C5.314c-0.053%2C0.164-0.188%2C0.29-0.354%2C0.332L0.379%2C5.905%20C0.156%2C5.961%2C0%2C6.161%2C0%2C6.391v1.219c0%2C0.229%2C0.156%2C0.43%2C0.379%2C0.485l1.036%2C0.26C1.582%2C8.396%2C1.717%2C8.521%2C1.77%2C8.687%20c0.092%2C0.284%2C0.205%2C0.559%2C0.34%2C0.82C2.188%2C9.66%2C2.182%2C9.844%2C2.093%2C9.993l-0.549%2C0.915c-0.118%2C0.195-0.087%2C0.448%2C0.075%2C0.61%20l0.862%2C0.862c0.162%2C0.163%2C0.414%2C0.193%2C0.61%2C0.075l0.915-0.549c0.148-0.089%2C0.332-0.095%2C0.486-0.017%20c0.262%2C0.135%2C0.536%2C0.248%2C0.82%2C0.34c0.165%2C0.053%2C0.291%2C0.187%2C0.332%2C0.354l0.259%2C1.036C5.96%2C13.844%2C6.16%2C14%2C6.39%2C14h1.22%20c0.229%2C0%2C0.43-0.156%2C0.485-0.379l0.259-1.036c0.042-0.167%2C0.168-0.302%2C0.333-0.354c0.284-0.092%2C0.559-0.205%2C0.82-0.34%20c0.154-0.078%2C0.338-0.072%2C0.486%2C0.017l0.914%2C0.549c0.197%2C0.118%2C0.449%2C0.088%2C0.611-0.074l0.862-0.863%20c0.163-0.162%2C0.193-0.415%2C0.075-0.611l-0.549-0.915c-0.089-0.148-0.096-0.332-0.017-0.485c0.134-0.263%2C0.248-0.536%2C0.339-0.82%20c0.053-0.165%2C0.188-0.291%2C0.355-0.333l1.036-0.259C13.844%2C8.039%2C14%2C7.839%2C14%2C7.609V6.39C14%2C6.16%2C13.844%2C5.96%2C13.621%2C5.904z%20M7%2C10%20c-1.657%2C0-3-1.343-3-3s1.343-3%2C3-3s3%2C1.343%2C3%2C3S8.657%2C10%2C7%2C10z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-grid:after,
.ui-alt-icon .ui-icon-grid:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M3%2C0H1C0.447%2C0%2C0%2C0.447%2C0%2C1v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1V1C4%2C0.447%2C3.553%2C0%2C3%2C0z%20M8%2C0H6%20C5.447%2C0%2C5%2C0.447%2C5%2C1v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1V1C9%2C0.447%2C8.553%2C0%2C8%2C0z%20M13%2C0h-2c-0.553%2C0-1%2C0.447-1%2C1v2%20c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1V1C14%2C0.447%2C13.553%2C0%2C13%2C0z%20M3%2C5H1C0.447%2C5%2C0%2C5.447%2C0%2C6v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2%20c0.553%2C0%2C1-0.447%2C1-1V6C4%2C5.447%2C3.553%2C5%2C3%2C5z%20M8%2C5H6C5.447%2C5%2C5%2C5.447%2C5%2C6v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1V6%20C9%2C5.447%2C8.553%2C5%2C8%2C5z%20M13%2C5h-2c-0.553%2C0-1%2C0.447-1%2C1v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1V6C14%2C5.447%2C13.553%2C5%2C13%2C5z%20M3%2C10%20H1c-0.553%2C0-1%2C0.447-1%2C1v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1v-2C4%2C10.447%2C3.553%2C10%2C3%2C10z%20M8%2C10H6c-0.553%2C0-1%2C0.447-1%2C1v2%20c0%2C0.553%2C0.447%2C1%2C1%2C1h2c0.553%2C0%2C1-0.447%2C1-1v-2C9%2C10.447%2C8.553%2C10%2C8%2C10z%20M13%2C10h-2c-0.553%2C0-1%2C0.447-1%2C1v2c0%2C0.553%2C0.447%2C1%2C1%2C1h2%20c0.553%2C0%2C1-0.447%2C1-1v-2C14%2C10.447%2C13.553%2C10%2C13%2C10z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-heart:after,
.ui-alt-icon .ui-icon-heart:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M7%2C1.958c-2-3-7-2.128-7%2C1.872c0%2C3%2C4%2C7%2C4%2C7s2.417%2C2.48%2C3%2C3c0.583-0.52%2C3-3%2C3-3s4-4%2C4-7C14-0.169%2C9-1.042%2C7%2C1.958z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-home:after,
.ui-alt-icon .ui-icon-home:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%227%2C0%200%2C7%202%2C7%202%2C14%205%2C14%205%2C9%209%2C9%209%2C14%2012%2C14%2012%2C7%2014%2C7%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-info:after,
.ui-alt-icon .ui-icon-info:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M7%2C0C3.134%2C0%2C0%2C3.134%2C0%2C7s3.134%2C7%2C7%2C7s7-3.134%2C7-7S10.866%2C0%2C7%2C0z%20M7%2C2c0.552%2C0%2C1%2C0.447%2C1%2C1S7.552%2C4%2C7%2C4S6%2C3.553%2C6%2C3%20S6.448%2C2%2C7%2C2z%20M9%2C11H5v-1h1V6H5V5h3v5h1V11z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-location:after,
.ui-alt-icon .ui-icon-location:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M7%2C0C4.791%2C0%2C3%2C1.791%2C3%2C4c0%2C2%2C4%2C10%2C4%2C10s4-8%2C4-10C11%2C1.791%2C9.209%2C0%2C7%2C0z%20M7%2C6C5.896%2C6%2C5%2C5.104%2C5%2C4s0.896-2%2C2-2%20c1.104%2C0%2C2%2C0.896%2C2%2C2S8.104%2C6%2C7%2C6z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-lock:after,
.ui-alt-icon .ui-icon-lock:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M12%2C6V5c0-2.762-2.238-5-5-5C4.239%2C0%2C2%2C2.238%2C2%2C5v1H1v8h12V6H12z%20M7.5%2C9.848V12h-1V9.848C6.207%2C9.673%2C6%2C9.366%2C6%2C9%20c0-0.553%2C0.448-1%2C1-1s1%2C0.447%2C1%2C1C8%2C9.366%2C7.793%2C9.673%2C7.5%2C9.848z%20M10%2C6H4V5c0-1.657%2C1.343-3%2C3-3s3%2C1.343%2C3%2C3V6z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-mail:after,
.ui-alt-icon .ui-icon-mail:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M0%2C3.75V12h14V3.75L7%2C9L0%2C3.75z%20M14%2C2H0l7%2C5L14%2C2z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-minus:after,
.ui-alt-icon .ui-icon-minus:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Crect%20y%3D%225%22%20width%3D%2214%22%20height%3D%224%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-navigation:after,
.ui-alt-icon .ui-icon-navigation:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%2213%2C1%200%2C6%207%2C7%208%2C14%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-phone:after,
.ui-alt-icon .ui-icon-phone:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M6.949%2C9.182C6.175%2C8.549%2C5.281%2C7.697%2C4.507%2C6.736C3.963%2C6.063%2C3.483%2C5.355%2C3.979%2C4.858l-3.482-3.48%20c-0.508%2C0.634-1.633%2C3.654%2C3.188%2C8.598c5.08%2C5.211%2C8.356%2C4.097%2C8.92%2C3.511l-3.396-3.399C8.734%2C10.561%2C8.123%2C10.139%2C6.949%2C9.182z%20%20M13.83%2C11.512v-0.004c0%2C0-2.648-2.646-2.649-2.647c-0.21-0.212-0.546-0.205-0.754%2C0.002L9.465%2C9.823l3.402%2C3.407%20c0%2C0%2C0.963-0.961%2C0.961-0.961l0.002-0.002C14.053%2C12.049%2C14.031%2C11.713%2C13.83%2C11.512z%20M5.202%2C3.636V3.634%20c0.222-0.222%2C0.2-0.557%2C0-0.758V2.873c0%2C0-2.726-2.725-2.727-2.726c-0.21-0.21-0.545-0.205-0.753%2C0.001L0.761%2C1.113L4.24%2C4.595%20C4.241%2C4.596%2C5.202%2C3.637%2C5.202%2C3.636z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-plus:after,
.ui-alt-icon .ui-icon-plus:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%2214%2C5%209%2C5%209%2C0%205%2C0%205%2C5%200%2C5%200%2C9%205%2C9%205%2C14%209%2C14%209%2C9%2014%2C9%20%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-power:after,
.ui-alt-icon .ui-icon-power:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M11.243%2C2.408c-0.392-0.401-1.024-0.401-1.415%2C0c-0.391%2C0.401-0.391%2C1.054%2C0%2C1.455C10.584%2C4.642%2C11%2C5.675%2C11%2C6.773%20s-0.416%2C2.133-1.172%2C2.91c-1.512%2C1.558-4.145%2C1.558-5.656%2C0C3.416%2C8.904%2C3%2C7.872%2C3%2C6.773C3%2C5.673%2C3.416%2C4.64%2C4.172%2C3.863%20c0.39-0.401%2C0.39-1.054%2C0-1.455c-0.391-0.401-1.024-0.401-1.415%2C0C1.624%2C3.574%2C1%2C5.125%2C1%2C6.773c0%2C1.647%2C0.624%2C3.199%2C1.757%2C4.365%20c1.134%2C1.166%2C2.64%2C1.809%2C4.243%2C1.809c1.604%2C0%2C3.109-0.645%2C4.243-1.811C12.376%2C9.975%2C13%2C8.423%2C13%2C6.773%20C13%2C5.125%2C12.376%2C3.574%2C11.243%2C2.408z%20M7%2C8.053c0.553%2C0%2C1-0.445%2C1-1v-6c0-0.553-0.447-1-1-1c-0.553%2C0-1%2C0.447-1%2C1v6%20C6%2C7.604%2C6.447%2C8.053%2C7%2C8.053z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-recycle:after,
.ui-alt-icon .ui-icon-recycle:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M3%2C7h1L2%2C4L0%2C7h1c0%2C3.313%2C2.687%2C6%2C6%2C6c0.702%2C0%2C1.374-0.127%2C2-0.35v-2.205C8.41%2C10.789%2C7.732%2C11%2C7%2C11C4.791%2C11%2C3%2C9.209%2C3%2C7z%20%20M13%2C7c0-3.313-2.688-6-6-6C6.298%2C1%2C5.626%2C1.127%2C5%2C1.349v2.206C5.59%2C3.211%2C6.268%2C3%2C7%2C3c2.209%2C0%2C4%2C1.791%2C4%2C4h-1l2%2C3l2-3H13z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-refresh:after,
.ui-alt-icon .ui-icon-refresh:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214.001px%22%20height%3D%2214.002px%22%20viewBox%3D%220%200%2014.001%2014.002%22%20style%3D%22enable-background%3Anew%200%200%2014.001%2014.002%3B%22%20%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M14.001%2C6.001v-6l-2.06%2C2.06c-0.423-0.424-0.897-0.809-1.44-1.122C7.153-0.994%2C2.872%2C0.153%2C0.939%2C3.501%20c-1.933%2C3.348-0.786%2C7.629%2C2.562%2C9.562c3.348%2C1.933%2C7.629%2C0.785%2C9.562-2.562l-1.732-1c-1.381%2C2.392-4.438%2C3.211-6.83%2C1.83%20s-3.211-4.438-1.83-6.83s4.438-3.211%2C6.83-1.83c0.389%2C0.225%2C0.718%2C0.506%2C1.02%2C0.81l-2.52%2C2.52H14.001z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-search:after,
.ui-alt-icon .ui-icon-search:after,
.ui-input-search:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M10.171%2C8.766c0.617-0.888%2C0.979-1.964%2C0.979-3.126c0-3.037-2.463-5.5-5.5-5.5s-5.5%2C2.463-5.5%2C5.5s2.463%2C5.5%2C5.5%2C5.5%20c1.152%2C0%2C2.223-0.355%2C3.104-0.962l3.684%2C3.683l1.414-1.414L10.171%2C8.766z%20M5.649%2C9.14c-1.933%2C0-3.5-1.567-3.5-3.5%20c0-1.933%2C1.567-3.5%2C3.5-3.5c1.933%2C0%2C3.5%2C1.567%2C3.5%2C3.5C9.149%2C7.572%2C7.582%2C9.14%2C5.649%2C9.14z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-shop:after,
.ui-alt-icon .ui-icon-shop:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M10%2C4V3c0-1.657-1.343-3-3-3S4%2C1.343%2C4%2C3v1H1v10h12V4H10z%20M4.5%2C6C4.224%2C6%2C4%2C5.776%2C4%2C5.5S4.224%2C5%2C4.5%2C5S5%2C5.224%2C5%2C5.5%20S4.776%2C6%2C4.5%2C6z%20M5%2C3c0-1.104%2C0.896-2%2C2-2c1.104%2C0%2C2%2C0.896%2C2%2C2v1H5V3z%20M9.5%2C6C9.225%2C6%2C9%2C5.776%2C9%2C5.5S9.225%2C5%2C9.5%2C5S10%2C5.224%2C10%2C5.5%20S9.775%2C6%2C9.5%2C6z%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-star:after,
.ui-alt-icon .ui-icon-star:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20points%3D%2214%2C5%209%2C5%207%2C0%205%2C5%200%2C5%204%2C8%202.625%2C13%207%2C10%2011.375%2C13%2010%2C8%20%22%2F%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-tag:after,
.ui-alt-icon .ui-icon-tag:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M5%2C0H0v5l9%2C9l5-5L5%2C0z%20M3%2C4C2.447%2C4%2C2%2C3.553%2C2%2C3s0.447-1%2C1-1s1%2C0.447%2C1%2C1S3.553%2C4%2C3%2C4z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-user:after,
.ui-alt-icon .ui-icon-user:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%200%2014%2014%22%20style%3D%22enable-background%3Anew%200%200%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M8.851%2C10.101c-0.18-0.399-0.2-0.763-0.153-1.104C9.383%2C8.49%2C9.738%2C7.621%2C9.891%2C6.465C10.493%2C6.355%2C10.5%2C5.967%2C10.5%2C5.5%20c0-0.437-0.008-0.804-0.502-0.94C9.999%2C4.539%2C10%2C4.521%2C10%2C4.5c0-2.103-1-4-2-4C8%2C0.5%2C7.5%2C0%2C6.5%2C0C5%2C0%2C4%2C1.877%2C4%2C4.5%20c0%2C0.021%2C0.001%2C0.039%2C0.002%2C0.06C3.508%2C4.696%2C3.5%2C5.063%2C3.5%2C5.5c0%2C0.467%2C0.007%2C0.855%2C0.609%2C0.965%20C4.262%2C7.621%2C4.617%2C8.49%2C5.303%2C8.997c0.047%2C0.341%2C0.026%2C0.704-0.153%2C1.104C1.503%2C10.503%2C0%2C12%2C0%2C12v2h14v-2%20C14%2C12%2C12.497%2C10.503%2C8.851%2C10.101z%22%2F%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3Cg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.ui-alt-icon.ui-icon-video:after,
.ui-alt-icon .ui-icon-video:after {
	background-image: url("data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22iso-8859-1%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%2214px%22%20height%3D%2214px%22%20viewBox%3D%220%20-2%2014%2014%22%20style%3D%22enable-background%3Anew%200%20-2%2014%2014%3B%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20d%3D%22M8%2C0H2C0.896%2C0%2C0%2C0.896%2C0%2C2v6c0%2C1.104%2C0.896%2C2%2C2%2C2h6c1.104%2C0%2C2-0.896%2C2-2V5V2C10%2C0.896%2C9.104%2C0%2C8%2C0z%20M10%2C5l4%2C4V1L10%2C5z%22%2F%3E%3C%2Fsvg%3E");
}
/* PNG icons */
.ui-nosvg .ui-icon-action:after {
	background-image: url(images/icons-png/action-white.png);
}
.ui-nosvg .ui-icon-alert:after {
	background-image: url(images/icons-png/alert-white.png);
}
.ui-nosvg .ui-icon-arrow-d-l:after {
	background-image: url(images/icons-png/arrow-d-l-white.png);
}
.ui-nosvg .ui-icon-arrow-d-r:after {
	background-image: url(images/icons-png/arrow-d-r-white.png);
}
.ui-nosvg .ui-icon-arrow-d:after {
	background-image: url(images/icons-png/arrow-d-white.png);
}
.ui-nosvg .ui-icon-arrow-l:after {
	background-image: url(images/icons-png/arrow-l-white.png);
}
.ui-nosvg .ui-icon-arrow-r:after {
	background-image: url(images/icons-png/arrow-r-white.png);
}
.ui-nosvg .ui-icon-arrow-u-l:after {
	background-image: url(images/icons-png/arrow-u-l-white.png);
}
.ui-nosvg .ui-icon-arrow-u-r:after {
	background-image: url(images/icons-png/arrow-u-r-white.png);
}
.ui-nosvg .ui-icon-arrow-u:after {
	background-image: url(images/icons-png/arrow-u-white.png);
}
.ui-nosvg .ui-icon-audio:after {
	background-image: url(images/icons-png/audio-white.png);
}
.ui-nosvg .ui-icon-back:after {
	background-image: url(images/icons-png/back-white.png);
}
.ui-nosvg .ui-icon-bars:after {
	background-image: url(images/icons-png/bars-white.png);
}
.ui-nosvg .ui-icon-bullets:after {
	background-image: url(images/icons-png/bullets-white.png);
}
.ui-nosvg .ui-icon-calendar:after {
	background-image: url(images/icons-png/calendar-white.png);
}
.ui-nosvg .ui-icon-camera:after {
	background-image: url(images/icons-png/camera-white.png);
}
.ui-nosvg .ui-icon-carat-d:after {
	background-image: url(images/icons-png/carat-d-white.png);
}
.ui-nosvg .ui-icon-carat-l:after {
	background-image: url(images/icons-png/carat-l-white.png);
}
.ui-nosvg .ui-icon-carat-r:after {
	background-image: url(images/icons-png/carat-r-white.png);
}
.ui-nosvg .ui-icon-carat-u:after {
	background-image: url(images/icons-png/carat-u-white.png);
}
.ui-nosvg .ui-icon-check:after,
html.ui-nosvg .ui-btn.ui-checkbox-on:after {
	background-image: url(images/icons-png/check-white.png);
}
.ui-nosvg .ui-icon-clock:after {
	background-image: url(images/icons-png/clock-white.png);
}
.ui-nosvg .ui-icon-cloud:after {
	background-image: url(images/icons-png/cloud-white.png);
}
.ui-nosvg .ui-icon-comment:after {
	background-image: url(images/icons-png/comment-white.png);
}
.ui-nosvg .ui-icon-delete:after {
	background-image: url(images/icons-png/delete-white.png);
}
.ui-nosvg .ui-icon-edit:after {
	background-image: url(images/icons-png/edit-white.png);
}
.ui-nosvg .ui-icon-eye:after {
	background-image: url(images/icons-png/eye-white.png);
}
.ui-nosvg .ui-icon-forbidden:after {
	background-image: url(images/icons-png/forbidden-white.png);
}
.ui-nosvg .ui-icon-forward:after {
	background-image: url(images/icons-png/forward-white.png);
}
.ui-nosvg .ui-icon-gear:after {
	background-image: url(images/icons-png/gear-white.png);
}
.ui-nosvg .ui-icon-grid:after {
	background-image: url(images/icons-png/grid-white.png);
}
.ui-nosvg .ui-icon-heart:after {
	background-image: url(images/icons-png/heart-white.png);
}
.ui-nosvg .ui-icon-home:after {
	background-image: url(images/icons-png/home-white.png);
}
.ui-nosvg .ui-icon-info:after {
	background-image: url(images/icons-png/info-white.png);
}
.ui-nosvg .ui-icon-location:after {
	background-image: url(images/icons-png/location-white.png);
}
.ui-nosvg .ui-icon-lock:after {
	background-image: url(images/icons-png/lock-white.png);
}
.ui-nosvg .ui-icon-mail:after {
	background-image: url(images/icons-png/mail-white.png);
}
.ui-nosvg .ui-icon-minus:after {
	background-image: url(images/icons-png/minus-white.png);
}
.ui-nosvg .ui-icon-navigation:after {
	background-image: url(images/icons-png/navigation-white.png);
}
.ui-nosvg .ui-icon-phone:after {
	background-image: url(images/icons-png/phone-white.png);
}
.ui-nosvg .ui-icon-plus:after {
	background-image: url(images/icons-png/plus-white.png);
}
.ui-nosvg .ui-icon-power:after {
	background-image: url(images/icons-png/power-white.png);
}
.ui-nosvg .ui-icon-recycle:after {
	background-image: url(images/icons-png/recycle-white.png);
}
.ui-nosvg .ui-icon-refresh:after {
	background-image: url(images/icons-png/refresh-white.png);
}
.ui-nosvg .ui-icon-search:after {
	background-image: url(images/icons-png/search-white.png);
}
.ui-nosvg .ui-icon-shop:after {
	background-image: url(images/icons-png/shop-white.png);
}
.ui-nosvg .ui-icon-star:after {
	background-image: url(images/icons-png/star-white.png);
}
.ui-nosvg .ui-icon-tag:after {
	background-image: url(images/icons-png/tag-white.png);
}
.ui-nosvg .ui-icon-user:after {
	background-image: url(images/icons-png/user-white.png);
}
.ui-nosvg .ui-icon-video:after {
	background-image: url(images/icons-png/video-white.png);
}
/* Alt icons */
.ui-nosvg .ui-alt-icon.ui-icon-action:after,
.ui-nosvg .ui-alt-icon .ui-icon-action:after {
	background-image: url(images/icons-png/action-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-alert:after,
.ui-nosvg .ui-alt-icon .ui-icon-alert:after {
	background-image: url(images/icons-png/alert-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-arrow-d:after,
.ui-nosvg .ui-alt-icon .ui-icon-arrow-d:after {
	background-image: url(images/icons-png/arrow-d-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-arrow-d-l:after,
.ui-nosvg .ui-alt-icon .ui-icon-arrow-d-l:after {
	background-image: url(images/icons-png/arrow-d-l-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-arrow-d-r:after,
.ui-nosvg .ui-alt-icon .ui-icon-arrow-d-r:after {
	background-image: url(images/icons-png/arrow-d-r-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-arrow-l:after,
.ui-nosvg .ui-alt-icon .ui-icon-arrow-l:after {
	background-image: url(images/icons-png/arrow-l-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-arrow-r:after,
.ui-nosvg .ui-alt-icon .ui-icon-arrow-r:after {
	background-image: url(images/icons-png/arrow-r-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-arrow-u:after,
.ui-nosvg .ui-alt-icon .ui-icon-arrow-u:after {
	background-image: url(images/icons-png/arrow-u-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-arrow-u-l:after,
.ui-nosvg .ui-alt-icon .ui-icon-arrow-u-l:after {
	background-image: url(images/icons-png/arrow-u-l-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-arrow-u-r:after,
.ui-nosvg .ui-alt-icon .ui-icon-arrow-u-r:after {
	background-image: url(images/icons-png/arrow-u-r-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-audio:after,
.ui-nosvg .ui-alt-icon .ui-icon-audio:after {
	background-image: url(images/icons-png/audio-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-back:after,
.ui-nosvg .ui-alt-icon .ui-icon-back:after {
	background-image: url(images/icons-png/back-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-bars:after,
.ui-nosvg .ui-alt-icon .ui-icon-bars:after {
	background-image: url(images/icons-png/bars-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-bullets:after,
.ui-nosvg .ui-alt-icon .ui-icon-bullets:after {
	background-image: url(images/icons-png/bullets-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-calendar:after,
.ui-nosvg .ui-alt-icon .ui-icon-calendar:after {
	background-image: url(images/icons-png/calendar-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-camera:after,
.ui-nosvg .ui-alt-icon .ui-icon-camera:after {
	background-image: url(images/icons-png/camera-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-carat-d:after,
.ui-nosvg .ui-alt-icon .ui-icon-carat-d:after {
	background-image: url(images/icons-png/carat-d-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-carat-l:after,
.ui-nosvg .ui-alt-icon .ui-icon-carat-l:after {
	background-image: url(images/icons-png/carat-l-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-carat-r:after,
.ui-nosvg .ui-alt-icon .ui-icon-carat-r:after {
	background-image: url(images/icons-png/carat-r-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-carat-u:after,
.ui-nosvg .ui-alt-icon .ui-icon-carat-u:after {
	background-image: url(images/icons-png/carat-u-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-check:after,
.ui-nosvg .ui-alt-icon .ui-icon-check:after,
.ui-nosvg .ui-alt-icon.ui-btn.ui-checkbox-on:after,
.ui-nosvg .ui-alt-icon .ui-btn.ui-checkbox-on:after {
	background-image: url(images/icons-png/check-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-clock:after,
.ui-nosvg .ui-alt-icon .ui-icon-clock:after {
	background-image: url(images/icons-png/clock-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-cloud:after,
.ui-nosvg .ui-alt-icon .ui-icon-cloud:after {
	background-image: url(images/icons-png/cloud-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-comment:after,
.ui-nosvg .ui-alt-icon .ui-icon-comment:after {
	background-image: url(images/icons-png/comment-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-delete:after,
.ui-nosvg .ui-alt-icon .ui-icon-delete:after {
	background-image: url(images/icons-png/delete-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-edit:after,
.ui-nosvg .ui-alt-icon .ui-icon-edit:after {
	background-image: url(images/icons-png/edit-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-eye:after,
.ui-nosvg .ui-alt-icon .ui-icon-eye:after {
	background-image: url(images/icons-png/eye-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-forbidden:after,
.ui-nosvg .ui-alt-icon .ui-icon-forbidden:after {
	background-image: url(images/icons-png/forbidden-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-forward:after,
.ui-nosvg .ui-alt-icon .ui-icon-forward:after {
	background-image: url(images/icons-png/forward-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-gear:after,
.ui-nosvg .ui-alt-icon .ui-icon-gear:after {
	background-image: url(images/icons-png/gear-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-grid:after,
.ui-nosvg .ui-alt-icon .ui-icon-grid:after {
	background-image: url(images/icons-png/grid-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-heart:after,
.ui-nosvg .ui-alt-icon .ui-icon-heart:after {
	background-image: url(images/icons-png/heart-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-home:after,
.ui-nosvg .ui-alt-icon .ui-icon-home:after {
	background-image: url(images/icons-png/home-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-info:after,
.ui-nosvg .ui-alt-icon .ui-icon-info:after {
	background-image: url(images/icons-png/info-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-location:after,
.ui-nosvg .ui-alt-icon .ui-icon-location:after {
	background-image: url(images/icons-png/location-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-lock:after,
.ui-nosvg .ui-alt-icon .ui-icon-lock:after {
	background-image: url(images/icons-png/lock-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-mail:after,
.ui-nosvg .ui-alt-icon .ui-icon-mail:after {
	background-image: url(images/icons-png/mail-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-minus:after,
.ui-nosvg .ui-alt-icon .ui-icon-minus:after {
	background-image: url(images/icons-png/minus-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-navigation:after,
.ui-nosvg .ui-alt-icon .ui-icon-navigation:after {
	background-image: url(images/icons-png/navigation-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-phone:after,
.ui-nosvg .ui-alt-icon .ui-icon-phone:after {
	background-image: url(images/icons-png/phone-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-plus:after,
.ui-nosvg .ui-alt-icon .ui-icon-plus:after {
	background-image: url(images/icons-png/plus-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-power:after,
.ui-nosvg .ui-alt-icon .ui-icon-power:after {
	background-image: url(images/icons-png/power-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-recycle:after,
.ui-nosvg .ui-alt-icon .ui-icon-recycle:after {
	background-image: url(images/icons-png/recycle-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-refresh:after,
.ui-nosvg .ui-alt-icon .ui-icon-refresh:after {
	background-image: url(images/icons-png/refresh-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-search:after,
.ui-nosvg .ui-alt-icon .ui-icon-search:after,
.ui-nosvg .ui-input-search:after {
	background-image: url(images/icons-png/search-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-shop:after,
.ui-nosvg .ui-alt-icon .ui-icon-shop:after {
	background-image: url(images/icons-png/shop-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-star:after,
.ui-nosvg .ui-alt-icon .ui-icon-star:after {
	background-image: url(images/icons-png/star-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-tag:after,
.ui-nosvg .ui-alt-icon .ui-icon-tag:after {
	background-image: url(images/icons-png/tag-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-user:after,
.ui-nosvg .ui-alt-icon .ui-icon-user:after {
	background-image: url(images/icons-png/user-black.png);
}
.ui-nosvg .ui-alt-icon.ui-icon-video:after,
.ui-nosvg .ui-alt-icon .ui-icon-video:after {
	background-image: url(images/icons-png/video-black.png);
}
/* Globals */
/* Font
-----------------------------------------------------------------------------------------------------------*/
html {
	font-size: 100%;
}
body,
input,
select,
textarea,
button,
.ui-btn {
	font-size: 1em;
	line-height: 1.3;
	font-family: sans-serif /*{global-font-family}*/;
}
legend,
.ui-input-text input,
.ui-input-search input {
	color: inherit;
	text-shadow: inherit;
}
/* Form labels (overrides font-weight bold in bars, and mini font-size) */
.ui-mobile label,
div.ui-controlgroup-label {
	font-weight: normal;
	font-size: 16px;
}
/* Separators
-----------------------------------------------------------------------------------------------------------*/
/* Field contain separator (< 28em) */
.ui-field-contain {
	border-bottom-color: #828282;
	border-bottom-color: rgba(0,0,0,.15);
	border-bottom-width: 1px;
	border-bottom-style: solid;
}
/* Table opt-in classes: strokes between each row, and alternating row stripes */
/* Classes table-stroke and table-stripe are deprecated in 1.4. */
.table-stroke thead th,
.table-stripe thead th,
.table-stripe tbody tr:last-child {
	border-bottom: 1px solid #d6d6d6; /* non-RGBA fallback */
	border-bottom: 1px solid rgba(0,0,0,.1);
}
.table-stroke tbody th,
.table-stroke tbody td {
	border-bottom: 1px solid #e6e6e6; /* non-RGBA fallback  */
	border-bottom: 1px solid rgba(0,0,0,.05);
}
.table-stripe.table-stroke tbody tr:last-child th,
.table-stripe.table-stroke tbody tr:last-child td {
	border-bottom: 0;
}
.table-stripe tbody tr:nth-child(odd) td,
.table-stripe tbody tr:nth-child(odd) th {
	background-color: #eeeeee; /* non-RGBA fallback  */
	background-color: rgba(0,0,0,.04);
}
/* Buttons
-----------------------------------------------------------------------------------------------------------*/
.ui-btn,
label.ui-btn {
	font-weight: bold;
	border-width: 1px;
	border-style: solid;
}
.ui-btn {
	text-decoration: none !important;
}
.ui-btn-active {
	cursor: pointer;
}
/* Corner rounding
-----------------------------------------------------------------------------------------------------------*/
/* Class ui-btn-corner-all deprecated in 1.4 */
.ui-corner-all {
	-webkit-border-radius: 				.3125em /*{global-radii-blocks}*/;
	border-radius: 						.3125em /*{global-radii-blocks}*/;
}
/* Buttons */
.ui-btn-corner-all,
.ui-btn.ui-corner-all,
/* Slider track */
.ui-slider-track.ui-corner-all,
/* Flipswitch */
.ui-flipswitch.ui-corner-all,
/* Count bubble */
.ui-li-count {
	-webkit-border-radius: 				.3125em /*{global-radii-buttons}*/;
	border-radius: 						.3125em /*{global-radii-buttons}*/;
}
/* Icon-only buttons */
.ui-btn-icon-notext.ui-btn-corner-all,
.ui-btn-icon-notext.ui-corner-all {
	-webkit-border-radius: 1em;
	border-radius: 1em;
}
/* Radius clip workaround for cleaning up corner trapping */
.ui-btn-corner-all,
.ui-corner-all {
	-webkit-background-clip: padding;
	background-clip: padding-box;
}
/* Popup arrow */
.ui-popup.ui-corner-all > .ui-popup-arrow-guide {
	left: .6em /*{global-radii-blocks}*/;
	right: .6em /*{global-radii-blocks}*/;
	top: .6em /*{global-radii-blocks}*/;
	bottom: .6em /*{global-radii-blocks}*/;
}
/* Shadow
-----------------------------------------------------------------------------------------------------------*/
.ui-shadow {
	-webkit-box-shadow: 0 1px 3px /*{global-box-shadow-size}*/ 		rgba(0,0,0,.15) /*{global-box-shadow-color}*/;
	-moz-box-shadow: 0 1px 3px /*{global-box-shadow-size}*/ 		rgba(0,0,0,.15) /*{global-box-shadow-color}*/;
	box-shadow: 0 1px 3px /*{global-box-shadow-size}*/ 				rgba(0,0,0,.15) /*{global-box-shadow-color}*/;
}
.ui-shadow-inset {
	-webkit-box-shadow: inset 0 1px 3px /*{global-box-shadow-size}*/ 	rgba(0,0,0,.2) /*{global-box-shadow-color}*/;
	-moz-box-shadow: inset 0 1px 3px /*{global-box-shadow-size}*/ 		rgba(0,0,0,.2) /*{global-box-shadow-color}*/;
	box-shadow: inset 0 1px 3px /*{global-box-shadow-size}*/ 	rgba(0,0,0,.2) /*{global-box-shadow-color}*/;
}
.ui-overlay-shadow {
	-webkit-box-shadow: 0 0 12px 		rgba(0,0,0,.6);
	-moz-box-shadow: 0 0 12px 			rgba(0,0,0,.6);
	box-shadow: 0 0 12px 				rgba(0,0,0,.6);
}
/* Icons
-----------------------------------------------------------------------------------------------------------*/
.ui-btn-icon-left:after,
.ui-btn-icon-right:after,
.ui-btn-icon-top:after,
.ui-btn-icon-bottom:after,
.ui-btn-icon-notext:after {
	background-color: 					#666 /*{global-icon-color}*/;
	background-color: 					rgba(0,0,0,.3) /*{global-icon-disc}*/;
	background-position: center center;
	background-repeat: no-repeat;
	-webkit-border-radius: 1em;
	border-radius: 1em;
}
/* Alt icons */
.ui-alt-icon.ui-btn:after,
.ui-alt-icon .ui-btn:after,
html .ui-alt-icon.ui-checkbox-off:after,
html .ui-alt-icon.ui-radio-off:after,
html .ui-alt-icon .ui-checkbox-off:after,
html .ui-alt-icon .ui-radio-off:after {
	background-color: 					#666 /*{global-icon-color}*/;
	background-color: 					rgba(0,0,0,.15) /*{global-icon-disc}*/;
}
/* No disc */
.ui-nodisc-icon.ui-btn:after,
.ui-nodisc-icon .ui-btn:after {
	background-color: transparent;
}
/* Icon shadow */
.ui-shadow-icon.ui-btn:after,
.ui-shadow-icon .ui-btn:after {
	-webkit-box-shadow: 0 1px 0 			rgba(255,255,255,.3) /*{global-icon-shadow}*/;
	-moz-box-shadow: 0 1px 0 				rgba(255,255,255,.3) /*{global-icon-shadow}*/;
	box-shadow: 0 1px 0 					rgba(255,255,255,.3) /*{global-icon-shadow}*/;
}
/* Checkbox and radio */
.ui-btn.ui-checkbox-off:after,
.ui-btn.ui-checkbox-on:after,
.ui-btn.ui-radio-off:after,
.ui-btn.ui-radio-on:after {
	display: block;
	width: 18px;
	height: 18px;
	margin: -9px 2px 0 2px;
}
.ui-checkbox-off:after,
.ui-btn.ui-radio-off:after {
	filter: Alpha(Opacity=30);
	opacity: .3;
}
.ui-btn.ui-checkbox-off:after,
.ui-btn.ui-checkbox-on:after {
	-webkit-border-radius: .1875em;
	border-radius: .1875em;
}
.ui-btn.ui-checkbox-off:after {
	background-color: #666;
	background-color: rgba(0,0,0,.3);
}
.ui-radio .ui-btn.ui-radio-on:after {
	background-image: none;
	background-color: #fff;
	width: 8px;
	height: 8px;
	border-width: 5px;
	border-style: solid; 
}
.ui-alt-icon.ui-btn.ui-radio-on:after,
.ui-alt-icon .ui-btn.ui-radio-on:after {
	background-color: #000;
}
/* Loader */
.ui-icon-loading {
	background: url(images/ajax-loader.gif);
	background-size: 2.875em 2.875em;
}
/* Swatches */
/* A
-----------------------------------------------------------------------------------------------------------*/
/* Bar: Toolbars, dividers, slider track */
.ui-bar-a,
.ui-page-theme-a .ui-bar-inherit,
html .ui-bar-a .ui-bar-inherit,
html .ui-body-a .ui-bar-inherit,
html body .ui-group-theme-a .ui-bar-inherit {
	background-color: 			#e9e9e9 /*{a-bar-background-color}*/;
	border-color:	 		#ddd /*{a-bar-border}*/;
	color: 					#333 /*{a-bar-color}*/;
	text-shadow: 0 /*{a-bar-shadow-x}*/ 1px /*{a-bar-shadow-y}*/ 0 /*{a-bar-shadow-radius}*/ 	#eee /*{a-bar-shadow-color}*/;
	font-weight: bold;
}
.ui-bar-a {
	border-width: 1px;
	border-style: solid;
}
/* Page and overlay */
.ui-overlay-a,
.ui-page-theme-a,
.ui-page-theme-a .ui-panel-wrapper {
	background-color: 			#f9f9f9 /*{a-page-background-color}*/;
	border-color:	 		#bbb /*{a-page-border}*/;
	color: 					#333 /*{a-page-color}*/;
	text-shadow: 0 /*{a-page-shadow-x}*/ 1px /*{a-page-shadow-y}*/ 0 /*{a-page-shadow-radius}*/ 	#f3f3f3 /*{a-page-shadow-color}*/;
}
/* Body: Read-only lists, text inputs, collapsible content */
.ui-body-a,
.ui-page-theme-a .ui-body-inherit,
html .ui-bar-a .ui-body-inherit,
html .ui-body-a .ui-body-inherit,
html body .ui-group-theme-a .ui-body-inherit,
html .ui-panel-page-container-a {
	background-color: 			#fff /*{a-body-background-color}*/;
	border-color:	 		#ddd /*{a-body-border}*/;
	color: 					#333 /*{a-body-color}*/;
	text-shadow: 0 /*{a-body-shadow-x}*/ 1px /*{a-body-shadow-y}*/ 0 /*{a-body-shadow-radius}*/ 	#f3f3f3 /*{a-body-shadow-color}*/;
}
.ui-body-a {
	border-width: 1px;
	border-style: solid;
}
/* Links */
.ui-page-theme-a a,
html .ui-bar-a a,
html .ui-body-a a,
html body .ui-group-theme-a a {
	color: #3388cc /*{a-link-color}*/;
	font-weight: bold;
}
.ui-page-theme-a a:visited,
html .ui-bar-a a:visited,
html .ui-body-a a:visited,
html body .ui-group-theme-a a:visited {
    color: #3388cc /*{a-link-visited}*/;
}
.ui-page-theme-a a:hover,
html .ui-bar-a a:hover,
html .ui-body-a a:hover,
html body .ui-group-theme-a a:hover {
	color: #005599 /*{a-link-hover}*/;
}
.ui-page-theme-a a:active,
html .ui-bar-a a:active,
html .ui-body-a a:active,
html body .ui-group-theme-a a:active {
	color: #005599 /*{a-link-active}*/;
}
/* Button up */
.ui-page-theme-a .ui-btn,
html .ui-bar-a .ui-btn,
html .ui-body-a .ui-btn,
html body .ui-group-theme-a .ui-btn,
html head + body .ui-btn.ui-btn-a,
/* Button visited */
.ui-page-theme-a .ui-btn:visited,
html .ui-bar-a .ui-btn:visited,
html .ui-body-a .ui-btn:visited,
html body .ui-group-theme-a .ui-btn:visited,
html head + body .ui-btn.ui-btn-a:visited {
	background-color: 			#f6f6f6 /*{a-bup-background-color}*/;
	border-color:	 		#ddd /*{a-bup-border}*/;
	color: 					#333 /*{a-bup-color}*/;
	text-shadow: 0 /*{a-bup-shadow-x}*/ 1px /*{a-bup-shadow-y}*/ 0 /*{a-bup-shadow-radius}*/ #f3f3f3 /*{a-bup-shadow-color}*/;
}
/* Button hover */
.ui-page-theme-a .ui-btn:hover,
html .ui-bar-a .ui-btn:hover,
html .ui-body-a .ui-btn:hover,
html body .ui-group-theme-a .ui-btn:hover,
html head + body .ui-btn.ui-btn-a:hover {
	background-color: 			#ededed /*{a-bhover-background-color}*/;
	border-color:	 		#ddd /*{a-bhover-border}*/;
	color: 					#333 /*{a-bhover-color}*/;
	text-shadow: 0 /*{a-bhover-shadow-x}*/ 1px /*{a-bhover-shadow-y}*/ 0 /*{a-bhover-shadow-radius}*/ #f3f3f3 /*{a-bhover-shadow-color}*/;
}
/* Button down */
.ui-page-theme-a .ui-btn:active,
html .ui-bar-a .ui-btn:active,
html .ui-body-a .ui-btn:active,
html body .ui-group-theme-a .ui-btn:active,
html head + body .ui-btn.ui-btn-a:active {
	background-color: 			#e8e8e8 /*{a-bdown-background-color}*/;
	border-color:	 		#ddd /*{a-bdown-border}*/;
	color: 					#333 /*{a-bdown-color}*/;
	text-shadow: 0 /*{a-bdown-shadow-x}*/ 1px /*{a-bdown-shadow-y}*/ 0 /*{a-bdown-shadow-radius}*/ #f3f3f3 /*{a-bdown-shadow-color}*/;
}
/* Active button */
.ui-page-theme-a .ui-btn.ui-btn-active,
html .ui-bar-a .ui-btn.ui-btn-active,
html .ui-body-a .ui-btn.ui-btn-active,
html body .ui-group-theme-a .ui-btn.ui-btn-active,
html head + body .ui-btn.ui-btn-a.ui-btn-active,
/* Active checkbox icon */
.ui-page-theme-a .ui-checkbox-on:after,
html .ui-bar-a .ui-checkbox-on:after,
html .ui-body-a .ui-checkbox-on:after,
html body .ui-group-theme-a .ui-checkbox-on:after,
.ui-btn.ui-checkbox-on.ui-btn-a:after,
/* Active flipswitch background */
.ui-page-theme-a .ui-flipswitch-active,
html .ui-bar-a .ui-flipswitch-active,
html .ui-body-a .ui-flipswitch-active,
html body .ui-group-theme-a .ui-flipswitch-active,
html body .ui-flipswitch.ui-bar-a.ui-flipswitch-active,
/* Active slider track */
.ui-page-theme-a .ui-slider-track .ui-btn-active,
html .ui-bar-a .ui-slider-track .ui-btn-active,
html .ui-body-a .ui-slider-track .ui-btn-active,
html body .ui-group-theme-a .ui-slider-track .ui-btn-active,
html body div.ui-slider-track.ui-body-a .ui-btn-active {
	background-color: 		#3388cc /*{a-active-background-color}*/;
	border-color:	 		#3388cc /*{a-active-border}*/;
	color: 					#fff /*{a-active-color}*/;
	text-shadow: 0 /*{a-active-shadow-x}*/ 1px /*{a-active-shadow-y}*/ 0 /*{a-active-shadow-radius}*/ #005599 /*{a-active-shadow-color}*/;
}
/* Active radio button icon */
.ui-page-theme-a .ui-radio-on:after,
html .ui-bar-a .ui-radio-on:after,
html .ui-body-a .ui-radio-on:after,
html body .ui-group-theme-a .ui-radio-on:after,
.ui-btn.ui-radio-on.ui-btn-a:after {
	border-color:			#3388cc /*{a-active-background-color}*/;
}
/* Focus */
.ui-page-theme-a .ui-btn:focus,
html .ui-bar-a .ui-btn:focus,
html .ui-body-a .ui-btn:focus,
html body .ui-group-theme-a .ui-btn:focus,
html head + body .ui-btn.ui-btn-a:focus,
/* Focus buttons and text inputs with div wrap */
.ui-page-theme-a .ui-focus,
html .ui-bar-a .ui-focus,
html .ui-body-a .ui-focus,
html body .ui-group-theme-a .ui-focus,
html head + body .ui-btn-a.ui-focus,
html head + body .ui-body-a.ui-focus {
	-webkit-box-shadow: 0 0 12px 	#3388cc /*{a-active-background-color}*/;
	-moz-box-shadow: 0 0 12px 		#3388cc /*{a-active-background-color}*/;
	box-shadow: 0 0 12px 			#3388cc /*{a-active-background-color}*/;
}
/* B
-----------------------------------------------------------------------------------------------------------*/
/* Bar: Toolbars, dividers, slider track */
.ui-bar-b,
.ui-page-theme-b .ui-bar-inherit,
html .ui-bar-b .ui-bar-inherit,
html .ui-body-b .ui-bar-inherit,
html body .ui-group-theme-b .ui-bar-inherit {
	background-color: 			#1d1d1d /*{b-bar-background-color}*/;
	border-color:	 		#1b1b1b /*{b-bar-border}*/;
	color: 					#fff /*{b-bar-color}*/;
	text-shadow: 0 /*{b-bar-shadow-x}*/ 1px /*{b-bar-shadow-y}*/ 0 /*{b-bar-shadow-radius}*/ 	#111 /*{b-bar-shadow-color}*/;
	font-weight: bold;
}
.ui-bar-b {
	border-width: 1px;
	border-style: solid;
}
/* Page and overlay */
.ui-overlay-b,
.ui-page-theme-b,
.ui-page-theme-b .ui-panel-wrapper {
	background-color: 			#252525 /*{b-page-background-color}*/;
	border-color:	 		#454545 /*{b-page-border}*/;
	color: 					#fff /*{b-page-color}*/;
	text-shadow: 0 /*{b-page-shadow-x}*/ 1px /*{b-page-shadow-y}*/ 0 /*{b-page-shadow-radius}*/ 	#111 /*{b-page-shadow-color}*/;
}
/* Body: Read-only lists, text inputs, collapsible content */
.ui-body-b,
.ui-page-theme-b .ui-body-inherit,
html .ui-bar-b .ui-body-inherit,
html .ui-body-b .ui-body-inherit,
html body .ui-group-theme-b .ui-body-inherit,
html .ui-panel-page-container-b {
	background-color: 			#2a2a2a /*{b-body-background-color}*/;
	border-color:	 		#1d1d1d /*{b-body-border}*/;
	color: 					#fff /*{b-body-color}*/;
	text-shadow: 0 /*{b-body-shadow-x}*/ 1px /*{b-body-shadow-y}*/ 0 /*{b-body-shadow-radius}*/ 	#111 /*{b-body-shadow-color}*/;
}
.ui-body-b {
	border-width: 1px;
	border-style: solid;
}
/* Links */
.ui-page-theme-b a,
html .ui-bar-b a,
html .ui-body-b a,
html body .ui-group-theme-b a {
	color: #22aadd /*{b-link-color}*/;
	font-weight: bold;
}
.ui-page-theme-b a:visited,
html .ui-bar-b a:visited,
html .ui-body-b a:visited,
html body .ui-group-theme-b a:visited {
    color: #22aadd /*{b-link-visited}*/;
}
.ui-page-theme-b a:hover,
html .ui-bar-b a:hover,
html .ui-body-b a:hover,
html body .ui-group-theme-b a:hover {
	color: #0088bb /*{b-link-hover}*/;
}
.ui-page-theme-b a:active,
html .ui-bar-b a:active,
html .ui-body-b a:active,
html body .ui-group-theme-b a:active {
	color: #0088bb /*{b-link-active}*/;
}
/* Button up */
.ui-page-theme-b .ui-btn,
html .ui-bar-b .ui-btn,
html .ui-body-b .ui-btn,
html body .ui-group-theme-b .ui-btn,
html head + body .ui-btn.ui-btn-b,
/* Button visited */
.ui-page-theme-b .ui-btn:visited,
html .ui-bar-b .ui-btn:visited,
html .ui-body-b .ui-btn:visited,
html body .ui-group-theme-b .ui-btn:visited,
html head + body .ui-btn.ui-btn-b:visited {
	background-color: 			#333 /*{b-bup-background-color}*/;
	border-color:	 		#1f1f1f /*{b-bup-border}*/;
	color: 					#fff /*{b-bup-color}*/;
	text-shadow: 0 /*{b-bup-shadow-x}*/ 1px /*{b-bup-shadow-y}*/ 0 /*{b-bup-shadow-radius}*/ #111 /*{b-bup-shadow-color}*/;
}
/* Button hover */
.ui-page-theme-b .ui-btn:hover,
html .ui-bar-b .ui-btn:hover,
html .ui-body-b .ui-btn:hover,
html body .ui-group-theme-b .ui-btn:hover,
html head + body .ui-btn.ui-btn-b:hover {
	background-color: 			#373737 /*{b-bhover-background-color}*/;
	border-color:	 		#1f1f1f /*{b-bhover-border}*/;
	color: 					#fff /*{b-bhover-color}*/;
	text-shadow: 0 /*{b-bhover-shadow-x}*/ 1px /*{b-bhover-shadow-y}*/ 0 /*{b-bhover-shadow-radius}*/ #111 /*{b-bhover-shadow-color}*/;
}
/* Button down */
.ui-page-theme-b .ui-btn:active,
html .ui-bar-b .ui-btn:active,
html .ui-body-b .ui-btn:active,
html body .ui-group-theme-b .ui-btn:active,
html head + body .ui-btn.ui-btn-b:active {
	background-color: 			#404040 /*{b-bdown-background-color}*/;
	border-color:	 		#1f1f1f /*{b-bdown-border}*/;
	color: 					#fff /*{b-bdown-color}*/;
	text-shadow: 0 /*{b-bdown-shadow-x}*/ 1px /*{b-bdown-shadow-y}*/ 0 /*{b-bdown-shadow-radius}*/ #111 /*{b-bdown-shadow-color}*/;
}
/* Active button */
.ui-page-theme-b .ui-btn.ui-btn-active,
html .ui-bar-b .ui-btn.ui-btn-active,
html .ui-body-b .ui-btn.ui-btn-active,
html body .ui-group-theme-b .ui-btn.ui-btn-active,
html head + body .ui-btn.ui-btn-b.ui-btn-active,
/* Active checkbox icon */
.ui-page-theme-b .ui-checkbox-on:after,
html .ui-bar-b .ui-checkbox-on:after,
html .ui-body-b .ui-checkbox-on:after,
html body .ui-group-theme-b .ui-checkbox-on:after,
.ui-btn.ui-checkbox-on.ui-btn-b:after,
/* Active flipswitch background */
.ui-page-theme-b .ui-flipswitch-active,
html .ui-bar-b .ui-flipswitch-active,
html .ui-body-b .ui-flipswitch-active,
html body .ui-group-theme-b .ui-flipswitch-active,
html body .ui-flipswitch.ui-bar-b.ui-flipswitch-active,
/* Active slider track */
.ui-page-theme-b .ui-slider-track .ui-btn-active,
html .ui-bar-b .ui-slider-track .ui-btn-active,
html .ui-body-b .ui-slider-track .ui-btn-active,
html body .ui-group-theme-b .ui-slider-track .ui-btn-active,
html body div.ui-slider-track.ui-body-b .ui-btn-active {
	background-color: 		#22aadd /*{b-active-background-color}*/;
	border-color:	 		#22aadd /*{b-active-border}*/;
	color: 					#fff /*{b-active-color}*/;
	text-shadow: 0 /*{b-active-shadow-x}*/ 1px /*{b-active-shadow-y}*/ 0 /*{b-active-shadow-radius}*/ #0088bb /*{b-active-shadow-color}*/;
}
/* Active radio button icon */
.ui-page-theme-b .ui-radio-on:after,
html .ui-bar-b .ui-radio-on:after,
html .ui-body-b .ui-radio-on:after,
html body .ui-group-theme-b .ui-radio-on:after,
.ui-btn.ui-radio-on.ui-btn-b:after {
	border-color:			#22aadd /*{b-active-background-color}*/;
}
/* Focus */
.ui-page-theme-b .ui-btn:focus,
html .ui-bar-b .ui-btn:focus,
html .ui-body-b .ui-btn:focus,
html body .ui-group-theme-b .ui-btn:focus,
html head + body .ui-btn.ui-btn-b:focus,
/* Focus buttons and text inputs with div wrap */
.ui-page-theme-b .ui-focus,
html .ui-bar-b .ui-focus,
html .ui-body-b .ui-focus,
html body .ui-group-theme-b .ui-focus,
html head + body .ui-btn-b.ui-focus,
html head + body .ui-body-b.ui-focus {
	-webkit-box-shadow: 0 0 12px 	#22aadd /*{b-active-background-color}*/;
	-moz-box-shadow: 0 0 12px 		#22aadd /*{b-active-background-color}*/;
	box-shadow: 0 0 12px 			#22aadd /*{b-active-background-color}*/;
}
/* Structure */
/* Disabled
-----------------------------------------------------------------------------------------------------------*/
/* Class ui-disabled deprecated in 1.4. :disabled not supported by IE8 so we use [disabled] */
.ui-disabled,
.ui-state-disabled,
button[disabled],
.ui-select .ui-btn.ui-state-disabled {
	filter: Alpha(Opacity=30);
	opacity: .3;
	cursor: default !important;
	pointer-events: none;
}
/* Focus state outline
-----------------------------------------------------------------------------------------------------------*/
.ui-btn:focus,
.ui-btn.ui-focus {
	outline: 0;
}
/* Unset box-shadow in browsers that don't do it right */
.ui-noboxshadow .ui-shadow,
.ui-noboxshadow .ui-shadow-inset,
.ui-noboxshadow .ui-overlay-shadow,
.ui-noboxshadow .ui-shadow-icon.ui-btn:after,
.ui-noboxshadow .ui-shadow-icon .ui-btn:after,
.ui-noboxshadow .ui-focus,
.ui-noboxshadow .ui-btn:focus,
.ui-noboxshadow  input:focus,
.ui-noboxshadow .ui-panel {
	-webkit-box-shadow: none !important;
	-moz-box-shadow: none !important;
	box-shadow: none !important;
}
.ui-noboxshadow .ui-btn:focus,
.ui-noboxshadow .ui-focus {
	outline-width: 1px;
	outline-style: auto;
}
/* Some unsets */
.ui-mobile,
.ui-mobile body {
	height: 99.9%;
}
.ui-mobile fieldset,
.ui-page {
	padding: 0;
	margin: 0;
}
.ui-mobile a img,
.ui-mobile fieldset {
	border-width: 0;
}
/* Fixes for fieldset issues on IE10 and FF (see #6077) */
.ui-mobile fieldset {
	min-width: 0;
}
@-moz-document url-prefix() {
	.ui-mobile fieldset {
		display: table-column;
		vertical-align: middle;
	}
}
/* Viewport */
.ui-mobile-viewport {
	margin: 0;
	overflow-x: visible;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust:none;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
/* Issue #2066 */
body.ui-mobile-viewport,
div.ui-mobile-viewport {
	overflow-x: hidden;
}
/* "page" containers - full-screen views, one should always be in view post-pageload */
.ui-mobile [data-role=page],
.ui-mobile [data-role=dialog],
.ui-page {
	top: 0;
	left: 0;
	width: 100%;
	min-height: 100%;
	position: absolute;
	display: none;
	border: 0;
}
/* On ios4, setting focus on the page element causes flashing during transitions when there is an outline, so we turn off outlines */
.ui-page {
	outline: none;
}
.ui-mobile .ui-page-active {
	display: block;
	overflow: visible;
	overflow-x: hidden;
}
@media screen and (orientation: portrait) {
	.ui-mobile .ui-page {
		min-height: 420px;
	}
}
@media screen and (orientation: landscape) {
	.ui-mobile .ui-page {
		min-height: 300px;
	}
}
/* Fouc */
.ui-mobile-rendering > * {
	visibility: hidden;
}
/* Non-js content hiding */
.ui-nojs {
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	clip: rect(1px,1px,1px,1px);
}
/* Loading screen */
.ui-loading .ui-loader {
	display: block;
}
.ui-loader {
	display: none;
	z-index: 9999999;
	position: fixed;
	top: 50%;
	left: 50%;
	border:0;
}
.ui-loader-default {
	background: none;
	filter: Alpha(Opacity=18);
	opacity: .18;
	width: 2.875em;
	height: 2.875em;
	margin-left: -1.4375em;
	margin-top: -1.4375em;
}
.ui-loader-verbose {
	width: 12.5em;
	filter: Alpha(Opacity=88);
	opacity: .88;
	box-shadow: 0 1px 1px -1px #fff;
	height: auto;
	margin-left: -6.875em;
	margin-top: -2.6875em;
	padding: .625em;
}
.ui-loader-default h1 {
	font-size: 0;
	width: 0;
	height: 0;
	overflow: hidden;
}
.ui-loader-verbose h1 {
	font-size: 1em;
	margin: 0;
	text-align: center;
}
.ui-loader .ui-icon-loading {
	background-color: #000;
	display: block;
	margin: 0;
	width: 2.75em;
	height: 2.75em;
	padding: .0625em;
	-webkit-border-radius: 2.25em;
	border-radius: 2.25em;
}
.ui-loader-verbose .ui-icon-loading {
	margin: 0 auto .625em;
	filter: Alpha(Opacity=75);
	opacity: .75;
}
.ui-loader-textonly {
	padding: .9375em;
	margin-left: -7.1875em;
}
.ui-loader-textonly .ui-icon-loading {
	display: none;
}
.ui-loader-fakefix {
	position: absolute;
}
/* Headers, content panels */
.ui-bar,
.ui-body {
	position: relative;
	padding: .4em 1em;
	overflow: hidden;
	display: block;
	clear: both;
}
.ui-bar h1,
.ui-bar h2,
.ui-bar h3,
.ui-bar h4,
.ui-bar h5,
.ui-bar h6 {
	margin: 0;
	padding: 0;
	font-size: 1em;
	display: inline-block;
}
.ui-header,
.ui-footer {
	border-width: 1px 0;
	border-style: solid;
	position: relative;
}
.ui-header:empty,
.ui-footer:empty {
	min-height: 2.6875em;
}
.ui-header .ui-title,
.ui-footer .ui-title {
	font-size: 1em;
	min-height: 1.1em;
	text-align: center;
	display: block;
	margin: 0 30%;
	padding: .7em 0;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	outline: 0 !important;
}
.ui-footer .ui-title {
	margin: 0 1em;
}
.ui-content {
	border-width: 0;
	overflow: visible;
	overflow-x: hidden;
	padding: 1em;
}
/* Corner styling for dialogs and popups */
.ui-corner-all > .ui-header:first-child,
.ui-corner-all > .ui-content:first-child,
.ui-corner-all > .ui-footer:first-child {
	-webkit-border-top-left-radius: inherit;
	border-top-left-radius: inherit;
	-webkit-border-top-right-radius: inherit;
	border-top-right-radius: inherit;
}
.ui-corner-all > .ui-header:last-child,
.ui-corner-all > .ui-content:last-child,
.ui-corner-all > .ui-footer:last-child {
	-webkit-border-bottom-left-radius: inherit;
	border-bottom-left-radius: inherit;
	-webkit-border-bottom-right-radius: inherit;
	border-bottom-right-radius: inherit;
}
/* Buttons and icons */
.ui-btn {
	font-size: 16px;
	margin: .5em 0;
	padding: .7em 1em;
	display: block;
	position: relative;
	text-align: center;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
.ui-btn-icon-notext,
.ui-header button.ui-btn.ui-btn-icon-notext,
.ui-footer button.ui-btn.ui-btn-icon-notext {
	padding: 0;
	width: 1.75em;
	height: 1.75em;
	text-indent: -9999px;
	white-space: nowrap !important;
}
.ui-mini {
	font-size: 12.5px;
}
.ui-mini .ui-btn {
	font-size: inherit;
}
/* Make buttons in toolbars default to mini and inline. */
.ui-header .ui-btn,
.ui-footer .ui-btn {
	font-size: 12.5px;
	display: inline-block;
	vertical-align: middle;
}
.ui-header .ui-controlgroup .ui-btn-icon-notext,
.ui-footer .ui-controlgroup .ui-btn-icon-notext {
    font-size: 12.5px;
}
/* To ensure same top and left/right position when ui-btn-left/right are added to something other than buttons. */
.ui-header .ui-btn-left,
.ui-header .ui-btn-right {
	font-size: 12.5px;
}
.ui-mini.ui-btn-icon-notext,
.ui-mini .ui-btn-icon-notext,
.ui-header .ui-btn-icon-notext,
.ui-footer .ui-btn-icon-notext {
	font-size: 16px;	
	padding: 0;
}
.ui-btn-inline {
	display: inline-block;
	vertical-align: middle;
	margin-right: .625em;
}
.ui-btn-icon-left {
	padding-left: 2.5em;
}
.ui-btn-icon-right {
	padding-right: 2.5em;
}
.ui-btn-icon-top {
	padding-top: 2.5em;
}
.ui-btn-icon-bottom {
	padding-bottom: 2.5em;
}
.ui-header .ui-btn-icon-top,
.ui-footer .ui-btn-icon-top,
.ui-header .ui-btn-icon-bottom,
.ui-footer .ui-btn-icon-bottom {
	padding-left: .3125em;
	padding-right: .3125em;
}
.ui-btn-icon-left:after,
.ui-btn-icon-right:after,
.ui-btn-icon-top:after,
.ui-btn-icon-bottom:after,
.ui-btn-icon-notext:after {
	content: "";
	position: absolute;
	display: block;
	width: 22px;
	height: 22px;
}
.ui-btn-icon-notext:after,
.ui-btn-icon-left:after,
.ui-btn-icon-right:after {
	top: 50%;
	margin-top: -11px;
}
.ui-btn-icon-left:after {
	left: .5625em;
}
.ui-btn-icon-right:after {
	right: .5625em;
}
.ui-mini.ui-btn-icon-left:after,
.ui-mini .ui-btn-icon-left:after,
.ui-header .ui-btn-icon-left:after,
.ui-footer .ui-btn-icon-left:after {
	left: .37em;
}
.ui-mini.ui-btn-icon-right:after,
.ui-mini .ui-btn-icon-right:after,
.ui-header .ui-btn-icon-right:after,
.ui-footer .ui-btn-icon-right:after {
	right: .37em;
}
.ui-btn-icon-notext:after,
.ui-btn-icon-top:after,
.ui-btn-icon-bottom:after {
	left: 50%;
	margin-left: -11px;
}
.ui-btn-icon-top:after {
	top: .5625em;
}
.ui-btn-icon-bottom:after {
	top: auto;
	bottom: .5625em;
}
/* Buttons in header position classes */
.ui-header .ui-btn-left,
.ui-header .ui-btn-right,
.ui-btn-left > [class*="ui-"],
.ui-btn-right > [class*="ui-"] {
	margin: 0;
}
.ui-btn-left,
.ui-btn-right {
	position: absolute;
	top: .24em;
}
.ui-btn-left {
	left: .4em;
}
.ui-btn-right {
	right: .4em;
}
.ui-btn-icon-notext.ui-btn-left {
	top: .3125em;
	left: .3125em;
}
.ui-btn-icon-notext.ui-btn-right {
	top: .3125em;
	right: .3125em;
}
/* Button elements */
button.ui-btn,
.ui-controlgroup-controls button.ui-btn-icon-notext {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-appearance: none;
	-moz-appearance: none;
	width: 100%;
}
button.ui-btn-inline,
.ui-header button.ui-btn,
.ui-footer button.ui-btn {
	width: auto;
}
/* Firefox adds a 1px border in a button element. We negate this to make sure they have the same height as other buttons in controlgroups. */
button.ui-btn::-moz-focus-inner {
	border: 0;
}
button.ui-btn-icon-notext,
.ui-controlgroup-horizontal .ui-controlgroup-controls button.ui-btn {
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	width: 1.75em;
}
/* Form labels */
.ui-mobile label,
.ui-controlgroup-label {
	display: block;
	margin: 0 0 .4em;
}
/* Accessible content hiding */
/* ui-hide-label deprecated in 1.4. TODO: Remove in 1.5 */
.ui-hide-label > label,
.ui-hide-label .ui-controlgroup-label,
.ui-hide-label .ui-rangeslider label,
.ui-hidden-accessible {
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	clip: rect(1px,1px,1px,1px);
}
/* Used for hiding elements by the filterable widget. You can also use this class to hide list items or buttons in controlgroups; this ensures correct corner styling. */
.ui-screen-hidden {
	display: none !important;
}
/* Transitions originally inspired by those from jQtouch, nice work, folks */
.ui-mobile-viewport-transitioning,
.ui-mobile-viewport-transitioning .ui-page {
	width: 100%;
	height: 100%;
	overflow: hidden;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.ui-page-pre-in {
	opacity: 0;
}
.in {
	-webkit-animation-timing-function: ease-out;
	-webkit-animation-duration: 350ms;
	-moz-animation-timing-function: ease-out;
	-moz-animation-duration: 350ms;
	animation-timing-function: ease-out;
	animation-duration: 350ms;
}
.out {
	-webkit-animation-timing-function: ease-in;
	-webkit-animation-duration: 225ms;
	-moz-animation-timing-function: ease-in;
	-moz-animation-duration: 225ms;
	animation-timing-function: ease-in;
	animation-duration: 225ms;
}
@-webkit-keyframes fadein {
    from { opacity: 0; }
    to { opacity: 1; }
}
@-moz-keyframes fadein {
    from { opacity: 0; }
    to { opacity: 1; }
}
@keyframes fadein {
    from { opacity: 0; }
    to { opacity: 1; }
}
@-webkit-keyframes fadeout {
    from { opacity: 1; }
    to { opacity: 0; }
}
@-moz-keyframes fadeout {
    from { opacity: 1; }
    to { opacity: 0; }
}
@keyframes fadeout {
    from { opacity: 1; }
    to { opacity: 0; }
}
.fade.out {
	opacity: 0;
	-webkit-animation-duration: 125ms;
	-webkit-animation-name: fadeout;
	-moz-animation-duration: 125ms;
	-moz-animation-name: fadeout;
	animation-duration: 125ms;
	animation-name: fadeout;
}
.fade.in {
	opacity: 1;
	-webkit-animation-duration: 225ms;
	-webkit-animation-name: fadein;
	-moz-animation-duration: 225ms;
	-moz-animation-name: fadein;
	animation-duration: 225ms;
	animation-name: fadein;
}
.pop {
	-webkit-transform-origin: 50% 50%;
	-moz-transform-origin: 50% 50%;
	transform-origin: 50% 50%;
}
.pop.in {
	-webkit-transform: scale(1);
	-webkit-animation-name: popin;
	-webkit-animation-duration: 350ms;
	-moz-transform: scale(1);
	-moz-animation-name: popin;
	-moz-animation-duration: 350ms;
	transform: scale(1);
	animation-name: popin;
	animation-duration: 350ms;
    opacity: 1;
}
.pop.out {
	-webkit-animation-name: fadeout;
	-webkit-animation-duration: 100ms;
	-moz-animation-name: fadeout;
	-moz-animation-duration: 100ms;
	animation-name: fadeout;
	animation-duration: 100ms;
	opacity: 0;
}
.pop.in.reverse {
	-webkit-animation-name: fadein;
	-moz-animation-name: fadein;
	animation-name: fadein;
}
.pop.out.reverse {
	-webkit-transform: scale(.8);
	-webkit-animation-name: popout;
	-moz-transform: scale(.8);
	-moz-animation-name: popout;
	transform: scale(.8);
	animation-name: popout;
}
@-webkit-keyframes popin {
    from {
        -webkit-transform: scale(.8);
        opacity: 0;
    }
    to {
        -webkit-transform: scale(1);
        opacity: 1;
    }
}
@-moz-keyframes popin {
    from {
        -moz-transform: scale(.8);
        opacity: 0;
    }
    to {
        -moz-transform: scale(1);
        opacity: 1;
    }
}
@keyframes popin {
    from {
        transform: scale(.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}
@-webkit-keyframes popout {
    from {
        -webkit-transform: scale(1);
        opacity: 1;
    }
    to {
        -webkit-transform: scale(.8);
        opacity: 0;
    }
}
@-moz-keyframes popout {
    from {
        -moz-transform: scale(1);
        opacity: 1;
    }
    to {
        -moz-transform: scale(.8);
        opacity: 0;
    }
}
@keyframes popout {
    from {
        transform: scale(1);
        opacity: 1;
    }
    to {
        transform: scale(.8);
        opacity: 0;
    }
}
/* keyframes for slidein from sides */
@-webkit-keyframes slideinfromright {
    from { -webkit-transform: translate3d(100%,0,0); }
    to { -webkit-transform: translate3d(0,0,0); }
}
@-moz-keyframes slideinfromright {
    from { -moz-transform: translateX(100%); }
    to { -moz-transform: translateX(0); }
}
@keyframes slideinfromright {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}
@-webkit-keyframes slideinfromleft {
    from { -webkit-transform: translate3d(-100%,0,0); }
    to { -webkit-transform: translate3d(0,0,0); }
}
@-moz-keyframes slideinfromleft {
    from { -moz-transform: translateX(-100%); }
    to { -moz-transform: translateX(0); }
}
@keyframes slideinfromleft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}
/* keyframes for slideout to sides */
@-webkit-keyframes slideouttoleft {
    from { -webkit-transform: translate3d(0,0,0); }
    to { -webkit-transform: translate3d(-100%,0,0); }
}
@-moz-keyframes slideouttoleft {
    from { -moz-transform: translateX(0); }
    to { -moz-transform: translateX(-100%); }
}
@keyframes slideouttoleft {
    from { transform: translateX(0); }
    to { transform: translateX(-100%); }
}
@-webkit-keyframes slideouttoright {
    from { -webkit-transform: translate3d(0,0,0); }
    to { -webkit-transform: translate3d(100%,0,0); }
}
@-moz-keyframes slideouttoright {
    from { -moz-transform: translateX(0); }
    to { -moz-transform: translateX(100%); }
}
@keyframes slideouttoright {
    from { transform: translateX(0); }
    to { transform: translateX(100%); }
}
.slide.out, .slide.in {
	-webkit-animation-timing-function: ease-out;
	-webkit-animation-duration: 350ms;
	-moz-animation-timing-function: ease-out;
	-moz-animation-duration: 350ms;
	animation-timing-function: ease-out;
	animation-duration: 350ms;
}
.slide.out {
	-webkit-transform: translate3d(-100%,0,0);
	-webkit-animation-name: slideouttoleft;
	-moz-transform: translateX(-100%);
	-moz-animation-name: slideouttoleft;
	transform: translateX(-100%);
	animation-name: slideouttoleft;
}
.slide.in {
	-webkit-transform: translate3d(0,0,0);
	-webkit-animation-name: slideinfromright;
	-moz-transform: translateX(0);
	-moz-animation-name: slideinfromright;
	transform: translateX(0);
	animation-name: slideinfromright;
}
.slide.out.reverse {
	-webkit-transform: translate3d(100%,0,0);
	-webkit-animation-name: slideouttoright;
	-moz-transform: translateX(100%);
	-moz-animation-name: slideouttoright;
	transform: translateX(100%);
	animation-name: slideouttoright;
}
.slide.in.reverse {
	-webkit-transform: translate3d(0,0,0);
	-webkit-animation-name: slideinfromleft;
	-moz-transform: translateX(0);
	-moz-animation-name: slideinfromleft;
	transform: translateX(0);
	animation-name: slideinfromleft;
}
.slidefade.out {
	-webkit-transform: translateX(-100%);
	-webkit-animation-name: slideouttoleft;
	-webkit-animation-duration: 225ms;
	-moz-transform: translateX(-100%);
	-moz-animation-name: slideouttoleft;
	-moz-animation-duration: 225ms;
	transform: translateX(-100%);
	animation-name: slideouttoleft;
	animation-duration: 225ms;
}
.slidefade.in {
	-webkit-transform: translateX(0);
	-webkit-animation-name: fadein;
	-webkit-animation-duration: 200ms;
	-moz-transform: translateX(0);
	-moz-animation-name: fadein;
	-moz-animation-duration: 200ms;
	transform: translateX(0);
	animation-name: fadein;
	animation-duration: 200ms;
}
.slidefade.out.reverse {
	-webkit-transform: translateX(100%);
	-webkit-animation-name: slideouttoright;
	-webkit-animation-duration: 200ms;
	-moz-transform: translateX(100%);
	-moz-animation-name: slideouttoright;
	-moz-animation-duration: 200ms;
	transform: translateX(100%);
	animation-name: slideouttoright;
	animation-duration: 200ms;
}
.slidefade.in.reverse {
	-webkit-transform: translateX(0);
	-webkit-animation-name: fadein;
	-webkit-animation-duration: 200ms;
	-moz-transform: translateX(0);
	-moz-animation-name: fadein;
	-moz-animation-duration: 200ms;
	transform: translateX(0);
	animation-name: fadein;
	animation-duration: 200ms;
}
/* slide down */
.slidedown.out {
	-webkit-animation-name: fadeout;
	-webkit-animation-duration: 100ms;
	-moz-animation-name: fadeout;
	-moz-animation-duration: 100ms;
	animation-name: fadeout;
	animation-duration: 100ms;
}
.slidedown.in {
	-webkit-transform: translateY(0);
	-webkit-animation-name: slideinfromtop;
	-webkit-animation-duration: 250ms;
	-moz-transform: translateY(0);
	-moz-animation-name: slideinfromtop;
	-moz-animation-duration: 250ms;
	transform: translateY(0);
	animation-name: slideinfromtop;
	animation-duration: 250ms;
}
.slidedown.in.reverse {
	-webkit-animation-name: fadein;
	-webkit-animation-duration: 150ms;
	-moz-animation-name: fadein;
	-moz-animation-duration: 150ms;
	animation-name: fadein;
	animation-duration: 150ms;
}
.slidedown.out.reverse {
	-webkit-transform: translateY(-100%);
	-webkit-animation-name: slideouttotop;
	-webkit-animation-duration: 200ms;
	-moz-transform: translateY(-100%);
	-moz-animation-name: slideouttotop;
	-moz-animation-duration: 200ms;
	transform: translateY(-100%);
	animation-name: slideouttotop;
	animation-duration: 200ms;
}
@-webkit-keyframes slideinfromtop {
    from { -webkit-transform: translateY(-100%); }
    to { -webkit-transform: translateY(0); }
}
@-moz-keyframes slideinfromtop {
    from { -moz-transform: translateY(-100%); }
    to { -moz-transform: translateY(0); }
}
@keyframes slideinfromtop {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}
@-webkit-keyframes slideouttotop {
    from { -webkit-transform: translateY(0); }
    to { -webkit-transform: translateY(-100%); }
}
@-moz-keyframes slideouttotop {
    from { -moz-transform: translateY(0); }
    to { -moz-transform: translateY(-100%); }
}
@keyframes slideouttotop {
    from { transform: translateY(0); }
    to { transform: translateY(-100%); }
}
/* slide up */
.slideup.out {
	-webkit-animation-name: fadeout;
	-webkit-animation-duration: 100ms;
	-moz-animation-name: fadeout;
	-moz-animation-duration: 100ms;
	animation-name: fadeout;
	animation-duration: 100ms;
}
.slideup.in {
	-webkit-transform: translateY(0);
	-webkit-animation-name: slideinfrombottom;
	-webkit-animation-duration: 250ms;
	-moz-transform: translateY(0);
	-moz-animation-name: slideinfrombottom;
	-moz-animation-duration: 250ms;
	transform: translateY(0);
	animation-name: slideinfrombottom;
	animation-duration: 250ms;
}
.slideup.in.reverse {
	-webkit-animation-name: fadein;
	-webkit-animation-duration: 150ms;
	-moz-animation-name: fadein;
	-moz-animation-duration: 150ms;
	animation-name: fadein;
	animation-duration: 150ms;
}
.slideup.out.reverse {
	-webkit-transform: translateY(100%);
	-webkit-animation-name: slideouttobottom;
	-webkit-animation-duration: 200ms;
	-moz-transform: translateY(100%);
	-moz-animation-name: slideouttobottom;
	-moz-animation-duration: 200ms;
	transform: translateY(100%);
	animation-name: slideouttobottom;
	animation-duration: 200ms;
}
@-webkit-keyframes slideinfrombottom {
    from { -webkit-transform: translateY(100%); }
    to { -webkit-transform: translateY(0); }
}
@-moz-keyframes slideinfrombottom {
    from { -moz-transform: translateY(100%); }
    to { -moz-transform: translateY(0); }
}
@keyframes slideinfrombottom {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}
@-webkit-keyframes slideouttobottom {
    from { -webkit-transform: translateY(0); }
    to { -webkit-transform: translateY(100%); }
}
@-moz-keyframes slideouttobottom {
    from { -moz-transform: translateY(0); }
    to { -moz-transform: translateY(100%); }
}
@keyframes slideouttobottom {
    from { transform: translateY(0); }
    to { transform: translateY(100%); }
}
/* The properties in this rule are only necessary for the 'flip' transition.
 * We need specify the perspective to create a projection matrix. This will add
 * some depth as the element flips. The depth number represents the distance of
 * the viewer from the z-plane. According to the CSS3 spec, 1000 is a moderate
 * value.
 */
.viewport-flip {
	-webkit-perspective: 1000;
	-moz-perspective: 1000;
	perspective: 1000;
	position: absolute;
}
.flip {
	-webkit-backface-visibility: hidden;
	-webkit-transform: translateX(0); /* Needed to work around an iOS 3.1 bug that causes listview thumbs to disappear when -webkit-visibility:hidden is used. */
	-moz-backface-visibility: hidden;
	-moz-transform: translateX(0);
	backface-visibility: hidden;
	transform: translateX(0);
}
.flip.out {
	-webkit-transform: rotateY(-90deg) scale(.9);
	-webkit-animation-name: flipouttoleft;
	-webkit-animation-duration: 175ms;
	-moz-transform: rotateY(-90deg) scale(.9);
	-moz-animation-name: flipouttoleft;
	-moz-animation-duration: 175ms;
	transform: rotateY(-90deg) scale(.9);
	animation-name: flipouttoleft;
	animation-duration: 175ms;
}
.flip.in {
	-webkit-animation-name: flipintoright;
	-webkit-animation-duration: 225ms;
	-moz-animation-name: flipintoright;
	-moz-animation-duration: 225ms;
	animation-name: flipintoright;
	animation-duration: 225ms;
}
.flip.out.reverse {
	-webkit-transform: rotateY(90deg) scale(.9);
	-webkit-animation-name: flipouttoright;
	-moz-transform: rotateY(90deg) scale(.9);
	-moz-animation-name: flipouttoright;
	transform: rotateY(90deg) scale(.9);
	animation-name: flipouttoright;
}
.flip.in.reverse {
	-webkit-animation-name: flipintoleft;
	-moz-animation-name: flipintoleft;
	animation-name: flipintoleft;
}
@-webkit-keyframes flipouttoleft {
    from { -webkit-transform: rotateY(0); }
    to { -webkit-transform: rotateY(-90deg) scale(.9); }
}
@-moz-keyframes flipouttoleft {
    from { -moz-transform: rotateY(0); }
    to { -moz-transform: rotateY(-90deg) scale(.9); }
}
@keyframes flipouttoleft {
    from { transform: rotateY(0); }
    to { transform: rotateY(-90deg) scale(.9); }
}
@-webkit-keyframes flipouttoright {
    from { -webkit-transform: rotateY(0) ; }
    to { -webkit-transform: rotateY(90deg) scale(.9); }
}
@-moz-keyframes flipouttoright {
    from { -moz-transform: rotateY(0); }
    to { -moz-transform: rotateY(90deg) scale(.9); }
}
@keyframes flipouttoright {
    from { transform: rotateY(0); }
    to { transform: rotateY(90deg) scale(.9); }
}
@-webkit-keyframes flipintoleft {
    from { -webkit-transform: rotateY(-90deg) scale(.9); }
    to { -webkit-transform: rotateY(0); }
}
@-moz-keyframes flipintoleft {
    from { -moz-transform: rotateY(-90deg) scale(.9); }
    to { -moz-transform: rotateY(0); }
}
@keyframes flipintoleft {
    from { transform: rotateY(-90deg) scale(.9); }
    to { transform: rotateY(0); }
}
@-webkit-keyframes flipintoright {
    from { -webkit-transform: rotateY(90deg) scale(.9); }
    to { -webkit-transform: rotateY(0); }
}
@-moz-keyframes flipintoright {
    from { -moz-transform: rotateY(90deg) scale(.9); }
    to { -moz-transform: rotateY(0); }
}
@keyframes flipintoright {
    from { transform: rotateY(90deg) scale(.9); }
    to { transform: rotateY(0); }
}
/* The properties in this rule are only necessary for the 'flip' transition.
 * We need specify the perspective to create a projection matrix. This will add
 * some depth as the element flips. The depth number represents the distance of
 * the viewer from the z-plane. According to the CSS3 spec, 1000 is a moderate
 * value.
 */
.viewport-turn {
	-webkit-perspective: 200px;
	-moz-perspective: 200px;
	-ms-perspective: 200px;
	perspective: 200px;
	position: absolute;
}
.turn {
	-webkit-backface-visibility: hidden;
	-webkit-transform: translateX(0); /* Needed to work around an iOS 3.1 bug that causes listview thumbs to disappear when -webkit-visibility:hidden is used. */
	-webkit-transform-origin: 0;
	
	-moz-backface-visibility: hidden;
	-moz-transform: translateX(0);
	-moz-transform-origin: 0;
	
	backface-visibility :hidden;
	transform: translateX(0);
	transform-origin: 0;
}
.turn.out {
	-webkit-transform: rotateY(-90deg) scale(.9);
	-webkit-animation-name: flipouttoleft;
	-webkit-animation-duration: 125ms;
	-moz-transform: rotateY(-90deg) scale(.9);
	-moz-animation-name: flipouttoleft;
	-moz-animation-duration: 125ms;
	transform: rotateY(-90deg) scale(.9);
	animation-name: flipouttoleft;
	animation-duration: 125ms;
}
.turn.in {
	-webkit-animation-name: flipintoright;
	-webkit-animation-duration: 250ms;
	-moz-animation-name: flipintoright;
	-moz-animation-duration: 250ms;
	animation-name: flipintoright;
	animation-duration: 250ms;
	
}
.turn.out.reverse {
	-webkit-transform: rotateY(90deg) scale(.9);
	-webkit-animation-name: flipouttoright;
	-moz-transform: rotateY(90deg) scale(.9);
	-moz-animation-name: flipouttoright;
	transform: rotateY(90deg) scale(.9);
	animation-name: flipouttoright;
}
.turn.in.reverse {
	-webkit-animation-name: flipintoleft;
	-moz-animation-name: flipintoleft;
	animation-name: flipintoleft;
}
@-webkit-keyframes flipouttoleft {
    from { -webkit-transform: rotateY(0); }
    to { -webkit-transform: rotateY(-90deg) scale(.9); }
}
@-moz-keyframes flipouttoleft {
    from { -moz-transform: rotateY(0); }
    to { -moz-transform: rotateY(-90deg) scale(.9); }
}
@keyframes flipouttoleft {
    from { transform: rotateY(0); }
    to { transform: rotateY(-90deg) scale(.9); }
}
@-webkit-keyframes flipouttoright {
    from { -webkit-transform: rotateY(0) ; }
    to { -webkit-transform: rotateY(90deg) scale(.9); }
}
@-moz-keyframes flipouttoright {
    from { -moz-transform: rotateY(0); }
    to { -moz-transform: rotateY(90deg) scale(.9); }
}
@keyframes flipouttoright {
    from { transform: rotateY(0); }
    to { transform: rotateY(90deg) scale(.9); }
}
@-webkit-keyframes flipintoleft {
    from { -webkit-transform: rotateY(-90deg) scale(.9); }
    to { -webkit-transform: rotateY(0); }
}
@-moz-keyframes flipintoleft {
    from { -moz-transform: rotateY(-90deg) scale(.9); }
    to { -moz-transform: rotateY(0); }
}
@keyframes flipintoleft {
    from { transform: rotateY(-90deg) scale(.9); }
    to { transform: rotateY(0); }
}
@-webkit-keyframes flipintoright {
    from { -webkit-transform: rotateY(90deg) scale(.9); }
    to { -webkit-transform: rotateY(0); }
}
@-moz-keyframes flipintoright {
    from { -moz-transform: rotateY(90deg) scale(.9); }
    to { -moz-transform: rotateY(0); }
}
@keyframes flipintoright {
    from { transform: rotateY(90deg) scale(.9); }
    to { transform: rotateY(0); }
}
/* flow transition */
.flow {
	-webkit-transform-origin: 50% 30%;
	-webkit-box-shadow: 0 0 20px rgba(0,0,0,.4);
	-moz-transform-origin: 50% 30%;	
	-moz-box-shadow: 0 0 20px rgba(0,0,0,.4);
	transform-origin: 50% 30%;	
	box-shadow: 0 0 20px rgba(0,0,0,.4);
}
.ui-dialog.flow {
	-webkit-transform-origin: none;
	-webkit-box-shadow: none;
	-moz-transform-origin: none;	
	-moz-box-shadow: none;
	transform-origin: none;	
	box-shadow: none;
}
.flow.out {
	-webkit-transform: translateX(-100%) scale(.7);
	-webkit-animation-name: flowouttoleft;
	-webkit-animation-timing-function: ease;
	-webkit-animation-duration: 350ms;
	-moz-transform: translateX(-100%) scale(.7);
	-moz-animation-name: flowouttoleft;
	-moz-animation-timing-function: ease;
	-moz-animation-duration: 350ms;
	transform: translateX(-100%) scale(.7);
	animation-name: flowouttoleft;
	animation-timing-function: ease;
	animation-duration: 350ms;
}
.flow.in {
	-webkit-transform: translateX(0) scale(1);
	-webkit-animation-name: flowinfromright;
	-webkit-animation-timing-function: ease;
	-webkit-animation-duration: 350ms;
	-moz-transform: translateX(0) scale(1);
	-moz-animation-name: flowinfromright;
	-moz-animation-timing-function: ease;
	-moz-animation-duration: 350ms;
	transform: translateX(0) scale(1);
	animation-name: flowinfromright;
	animation-timing-function: ease;
	animation-duration: 350ms;
}
.flow.out.reverse {
	-webkit-transform: translateX(100%);
	-webkit-animation-name: flowouttoright;
	-moz-transform: translateX(100%);
	-moz-animation-name: flowouttoright;
	transform: translateX(100%);
	animation-name: flowouttoright;
}
.flow.in.reverse {
	-webkit-animation-name: flowinfromleft;
	-moz-animation-name: flowinfromleft;
	animation-name: flowinfromleft;
}
@-webkit-keyframes flowouttoleft {
    0% { -webkit-transform: translateX(0) scale(1); }
	60%, 70% { -webkit-transform: translateX(0) scale(.7); }
    100% { -webkit-transform: translateX(-100%) scale(.7); }
}
@-moz-keyframes flowouttoleft {
    0% { -moz-transform: translateX(0) scale(1); }
	60%, 70% { -moz-transform: translateX(0) scale(.7); }
    100% { -moz-transform:  translateX(-100%) scale(.7); }
}
@keyframes flowouttoleft {
    0% { transform: translateX(0) scale(1); }
	60%, 70% { transform: translateX(0) scale(.7); }
    100% { transform:  translateX(-100%) scale(.7); }
}
@-webkit-keyframes flowouttoright {
    0% { -webkit-transform: translateX(0) scale(1); }
	60%, 70% { -webkit-transform: translateX(0) scale(.7); }
    100% { -webkit-transform:  translateX(100%) scale(.7); }
}
@-moz-keyframes flowouttoright {
    0% { -moz-transform: translateX(0) scale(1); }
	60%, 70% { -moz-transform: translateX(0) scale(.7); }
    100% { -moz-transform:  translateX(100%) scale(.7); }
}
@keyframes flowouttoright {
    0% { transform: translateX(0) scale(1); }
	60%, 70% { transform: translateX(0) scale(.7); }
    100% { transform:  translateX(100%) scale(.7); }
}
@-webkit-keyframes flowinfromleft {
    0% { -webkit-transform: translateX(-100%) scale(.7); }
	30%, 40% { -webkit-transform: translateX(0) scale(.7); }
    100% { -webkit-transform: translateX(0) scale(1); }
}
@-moz-keyframes flowinfromleft {
    0% { -moz-transform: translateX(-100%) scale(.7); }
	30%, 40% { -moz-transform: translateX(0) scale(.7); }
    100% { -moz-transform: translateX(0) scale(1); }
}
@keyframes flowinfromleft {
    0% { transform: translateX(-100%) scale(.7); }
	30%, 40% { transform: translateX(0) scale(.7); }
    100% { transform: translateX(0) scale(1); }
}
@-webkit-keyframes flowinfromright {
    0% { -webkit-transform: translateX(100%) scale(.7); }
	30%, 40% { -webkit-transform: translateX(0) scale(.7); }
    100% { -webkit-transform: translateX(0) scale(1); }
}
@-moz-keyframes flowinfromright {
    0% { -moz-transform: translateX(100%) scale(.7); }
	30%, 40% { -moz-transform: translateX(0) scale(.7); }
    100% { -moz-transform: translateX(0) scale(1); }
}
@keyframes flowinfromright {
    0% { transform: translateX(100%) scale(.7); }
	30%, 40% { transform: translateX(0) scale(.7); }
    100% { transform: translateX(0) scale(1); }
}
.ui-field-contain,
.ui-mobile fieldset.ui-field-contain {
	display: block;
	position: relative;
	overflow: visible;
	clear: both;
	padding: .8em 0;
}
.ui-field-contain > label ~ [class*="ui-"],
.ui-field-contain .ui-controlgroup-controls {
	margin: 0;
}
.ui-field-contain:last-child {
	border-bottom-width: 0;
}
@media (min-width: 28em) {
	.ui-field-contain,
	.ui-mobile fieldset.ui-field-contain {
		padding: 0;
		margin: 1em 0;
		border-bottom-width: 0;
	}
	.ui-field-contain:before,
	.ui-field-contain:after {
		content: "";
		display: table;
	}
	.ui-field-contain:after {
		clear: both;
	}
	.ui-field-contain > label,
	.ui-field-contain .ui-controlgroup-label,
	.ui-field-contain > .ui-rangeslider > label {
		float: left;
		width: 20%;
		margin: .5em 2% 0 0;
	}
	.ui-popup .ui-field-contain > label,
	.ui-popup .ui-field-contain .ui-controlgroup-label,
	.ui-popup .ui-field-contain > .ui-rangeslider > label {
		float: none;
		width: auto;
		margin: 0 0 .4em;
	}
	.ui-field-contain > label ~ [class*="ui-"],
	.ui-field-contain .ui-controlgroup-controls {
		float: left;
		width: 78%;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
	}
	/* ui-hide-label deprecated in 1.4. TODO: Remove in 1.5 */
	.ui-hide-label > label ~ [class*="ui-"],
	.ui-hide-label .ui-controlgroup-controls,
	.ui-popup .ui-field-contain > label ~ [class*="ui-"],
	.ui-popup .ui-field-contain .ui-controlgroup-controls {
		float: none;
		width: 100%;
	}
	.ui-field-contain > label ~ .ui-btn-inline {
		width: auto;
		margin-right: .625em;
	}
	.ui-field-contain > label ~ .ui-btn-inline.ui-btn-icon-notext {
		width: 1.75em;
	}
}
/* content configurations. */
.ui-grid-a,
.ui-grid-b,
.ui-grid-c,
.ui-grid-d,
.ui-grid-solo {
	overflow: hidden;
}
.ui-block-a,
.ui-block-b,
.ui-block-c,
.ui-block-d,
.ui-block-e {
	margin: 0;
	padding: 0;
	border: 0;
	float: left;
	min-height: 1px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
/* force new row */
.ui-block-a {
	clear: left;
}
ul.ui-grid-a,
ul.ui-grid-b,
ul.ui-grid-c,
ul.ui-grid-d,
ul.ui-grid-solo,
li.ui-block-a,
li.ui-block-b,
li.ui-block-c,
li.ui-block-d,
li.ui-block-e {
	margin-left: 0;
	margin-right: 0;
	padding: 0;
	list-style: none;
}
/* No margin in grids for 100% width button elements until we can use max-width: fill-available; */
[class*="ui-block-"] > button.ui-btn {
	margin-right: 0;
	margin-left: 0;
}
[class*="ui-block-"] > .ui-btn,
[class*="ui-block-"] > .ui-select,
[class*="ui-block-"] > .ui-checkbox,
[class*="ui-block-"] > .ui-radio,
[class*="ui-block-"] > button.ui-btn-inline,
[class*="ui-block-"] > button.ui-btn-icon-notext,
.ui-header [class*="ui-block-"] > button.ui-btn,
.ui-footer [class*="ui-block-"] > button.ui-btn {
	margin-right: .3125em;
	margin-left: .3125em;
}
.ui-grid-a > .ui-block-a,
.ui-grid-a > .ui-block-b {
	/* width: 49.95%; IE7 */
	/* margin-right: -.5px; BB5 */
	width: 50%;
}
.ui-grid-b > .ui-block-a,
.ui-grid-b > .ui-block-b,
.ui-grid-b > .ui-block-c {
	/* width: 33.25%; IE7 */
	/* margin-right: -.5px; BB5 */
	width: 33.333%;
}
.ui-grid-c > .ui-block-a,
.ui-grid-c > .ui-block-b,
.ui-grid-c > .ui-block-c,
.ui-grid-c > .ui-block-d {
	/* width: 24.925%; IE7 */
	/* margin-right: -.5px; BB5 */
	width: 25%;
}
.ui-grid-d > .ui-block-a,
.ui-grid-d > .ui-block-b,
.ui-grid-d > .ui-block-c,
.ui-grid-d > .ui-block-d,
.ui-grid-d > .ui-block-e {
	/* width: 19.925%; IE7 */
	width: 20%;
}
.ui-grid-solo > .ui-block-a {
	width: 100%;
	float: none; 
}
/* preset breakpoint to switch to stacked grid styles below 35em (560px) */
@media (max-width: 35em) {
	.ui-responsive > .ui-block-a,
	.ui-responsive > .ui-block-b,
	.ui-responsive > .ui-block-c,
	.ui-responsive > .ui-block-d,
	.ui-responsive > .ui-block-e {
		width: 100%; 
		float: none; 
	}
}
/* fixed page header & footer configuration */
.ui-header-fixed,
.ui-footer-fixed {
	left: 0;
	right: 0;
	width: 100%;
	position: fixed;
	z-index: 1000;
}
.ui-header-fixed {
	top: -1px;
	padding-top: 1px;
}
.ui-header-fixed.ui-fixed-hidden {
	top: 0;
	padding-top: 0;
}
.ui-header-fixed .ui-btn-left,
.ui-header-fixed .ui-btn-right {
	margin-top: 1px;
}
.ui-header-fixed.ui-fixed-hidden .ui-btn-left,
.ui-header-fixed.ui-fixed-hidden .ui-btn-right {
	margin-top: 0;
}
.ui-footer-fixed {
	bottom: -1px;
	padding-bottom: 1px;
}
.ui-footer-fixed.ui-fixed-hidden {
	bottom: 0;
	padding-bottom: 0;
}
.ui-header-fullscreen,
.ui-footer-fullscreen {
	filter: Alpha(Opacity=90);
	opacity: .9;
}
/* updatePagePadding() will update the padding to actual height of header and footer. */
.ui-page-header-fixed {
	padding-top: 2.8125em;
}
.ui-page-footer-fixed {
	padding-bottom: 2.8125em;
}
.ui-page-header-fullscreen > .ui-content,
.ui-page-footer-fullscreen > .ui-content {
	padding: 0;
}
.ui-fixed-hidden {
	position: absolute;
}
/* Tap toggle: hide external fixed footer. See issue #6604 */
.ui-footer-fixed.ui-fixed-hidden {
  display: none;
}
.ui-page .ui-footer-fixed.ui-fixed-hidden {
  display: block
}
.ui-page-header-fullscreen .ui-fixed-hidden,
.ui-page-footer-fullscreen .ui-fixed-hidden {
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	clip: rect(1px,1px,1px,1px);
}
.ui-header-fixed .ui-btn,
.ui-footer-fixed .ui-btn { 
	z-index: 10;
}
/* workarounds for other widgets */
.ui-android-2x-fixed .ui-li-has-thumb {
	-webkit-transform: translate3d(0,0,0);
}
.ui-navbar {
	max-width: 100%;
}
.ui-navbar ul:before,
.ui-navbar ul:after {
	content: "";
	display: table;
}
.ui-navbar ul:after {
	clear: both;
}
.ui-navbar ul {
	list-style: none;
	margin: 0;
	padding: 0;
	position: relative;
	display: block;
	border: 0;
	max-width: 100%;
	overflow: visible;
}
.ui-navbar li .ui-btn {
	font-size: 12.5px;
	display: block;
	margin: 0;
	border-right-width: 0;
}
.ui-header .ui-navbar li button.ui-btn,
.ui-footer .ui-navbar li button.ui-btn {
	margin: 0;
	width: 100%;
}
.ui-navbar .ui-btn:focus {
	z-index: 1;
}
/* fixes gaps caused by subpixel problem */
.ui-navbar li:last-child .ui-btn {
	margin-right: -4px;
}
.ui-navbar li:last-child .ui-btn:after {
	margin-right: 4px;
}
.ui-content .ui-navbar li:last-child .ui-btn,
.ui-content .ui-navbar .ui-grid-duo .ui-block-b .ui-btn {
	border-right-width: 1px;
	margin-right: 0;
}
.ui-content .ui-navbar li:last-child .ui-btn:after,
.ui-content .ui-navbar .ui-grid-duo .ui-block-b .ui-btn:after {
	margin-right: 0;
}
.ui-navbar .ui-grid-duo .ui-block-a:last-child .ui-btn {
	border-right-width: 1px;
	margin-right: -1px;
}
.ui-navbar .ui-grid-duo .ui-block-a:last-child .ui-btn:after {
	margin-right: 1px;
}
.ui-navbar .ui-grid-duo .ui-btn {
	border-top-width: 0;
}
.ui-navbar .ui-grid-duo .ui-block-a:first-child .ui-btn,
.ui-navbar .ui-grid-duo .ui-block-a:first-child + .ui-block-b .ui-btn {
	border-top-width: 1px;
}
.ui-header .ui-navbar .ui-btn,
.ui-footer .ui-navbar .ui-btn {
	border-top-width: 0;
	border-bottom-width: 0;
}
.ui-header .ui-navbar .ui-grid-duo .ui-block-a:first-child .ui-btn,
.ui-footer .ui-navbar .ui-grid-duo .ui-block-a:first-child .ui-btn,
.ui-header .ui-navbar .ui-grid-duo .ui-block-a:first-child + .ui-block-b .ui-btn,
.ui-footer .ui-navbar .ui-grid-duo .ui-block-a:first-child + .ui-block-b .ui-btn {
	border-top-width: 0;
}
.ui-header .ui-title ~ .ui-navbar .ui-btn,
.ui-footer .ui-title ~ .ui-navbar .ui-btn,
.ui-header .ui-navbar .ui-grid-duo .ui-btn,
.ui-footer .ui-navbar .ui-grid-duo .ui-btn,
.ui-header .ui-title ~ .ui-navbar .ui-grid-duo .ui-block-a:first-child .ui-btn,
.ui-footer .ui-title ~ .ui-navbar .ui-grid-duo .ui-block-a:first-child .ui-btn,
.ui-header .ui-title ~ .ui-navbar .ui-grid-duo .ui-block-a:first-child + .ui-block-b .ui-btn,
.ui-footer .ui-title ~ .ui-navbar .ui-grid-duo .ui-block-a:first-child + .ui-block-b .ui-btn {
	border-top-width: 1px;
}
/* Hide the native input element */
.ui-input-btn input {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	padding: 0;
	border: 0;
	outline: 0;
	-webkit-border-radius: inherit;
	border-radius: inherit;	
	-webkit-appearance: none;
	-moz-appearance: none;
	cursor: pointer;
	background: #fff;
	background: rgba(255,255,255,0);
	filter: Alpha(Opacity=0);
	opacity: .1;
	font-size: 1px;
	text-indent: -9999px;
	z-index: 2;
}
/* Fixes IE/WP filter alpha opacity bugs */
.ui-input-btn.ui-state-disabled input {
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	clip: rect(1px,1px,1px,1px);
}
.ui-collapsible {
	margin: 0 -1em;
}
.ui-collapsible-inset,
.ui-collapsible-set {
	margin: .5em 0;
}
.ui-collapsible-heading {
	display: block;
	margin: 0;
	padding: 0;
	position: relative;
}
.ui-collapsible-heading .ui-btn {
	text-align: left;
	margin: 0;
	border-left-width: 0;
	border-right-width: 0;
}
.ui-collapsible-heading .ui-btn-icon-top,
.ui-collapsible-heading .ui-btn-icon-bottom {
	text-align: center;
}
.ui-collapsible-inset .ui-collapsible-heading .ui-btn {
	border-right-width: 1px;
	border-left-width: 1px;
}
.ui-collapsible-collapsed + .ui-collapsible:not(.ui-collapsible-inset) > .ui-collapsible-heading .ui-btn {
	border-top-width: 0;
}
.ui-collapsible-set .ui-collapsible:not(.ui-collapsible-inset) .ui-collapsible-heading .ui-btn {
	border-top-width: 1px;
}
.ui-collapsible-heading-status {
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	clip: rect(1px,1px,1px,1px);
}
.ui-collapsible-content {
	display: block;
	margin: 0;	
	padding: .5em 1em;
}
.ui-collapsible-themed-content .ui-collapsible-content {
	border-left-width: 0;
	border-right-width: 0;
	border-top-width: 0;
	border-bottom-width: 1px;
	border-style: solid;
}
.ui-collapsible-inset.ui-collapsible-themed-content .ui-collapsible-content {
	border-left-width: 1px;
	border-right-width: 1px;
}
.ui-collapsible-inset .ui-collapsible-content {
	margin: 0;
}
.ui-collapsible-content-collapsed {
	display: none;
}
.ui-collapsible-set > .ui-collapsible.ui-corner-all {
	-webkit-border-radius: 0;
	border-radius: 0;
}
.ui-collapsible-heading,
.ui-collapsible-heading > .ui-btn {
	-webkit-border-radius: inherit;	
	border-radius: inherit;	
}
.ui-collapsible-set .ui-collapsible.ui-first-child {
	-webkit-border-top-right-radius: inherit;	
	border-top-right-radius: inherit;
	-webkit-border-top-left-radius: inherit;	
	border-top-left-radius: inherit;		
}
.ui-collapsible-content,
.ui-collapsible-set .ui-collapsible.ui-last-child {
	-webkit-border-bottom-right-radius: inherit;	
	border-bottom-right-radius: inherit;
	-webkit-border-bottom-left-radius: inherit;	
	border-bottom-left-radius: inherit;		
}
.ui-collapsible-themed-content:not(.ui-collapsible-collapsed) > .ui-collapsible-heading {
	-webkit-border-bottom-right-radius: 0;	
	border-bottom-right-radius: 0;
	-webkit-border-bottom-left-radius: 0;	
	border-bottom-left-radius: 0;		
}
.ui-collapsible-set .ui-collapsible {
	margin: -1px -1em 0;
}
.ui-collapsible-set .ui-collapsible-inset {
	margin: -1px 0 0;
}
.ui-collapsible-set .ui-collapsible.ui-first-child {
	margin-top: 0;
}
.ui-controlgroup,
fieldset.ui-controlgroup {
	padding: 0;
	margin: .5em 0;
}
.ui-field-contain .ui-controlgroup,
.ui-field-contain fieldset.ui-controlgroup {
	margin: 0;
}
.ui-mini .ui-controlgroup-label {
	font-size: 16px;
}
.ui-controlgroup.ui-mini .ui-btn-icon-notext,
.ui-controlgroup .ui-mini.ui-btn-icon-notext {
	font-size: inherit;
}
.ui-controlgroup-controls .ui-btn,
.ui-controlgroup-controls .ui-checkbox,
.ui-controlgroup-controls .ui-radio,
.ui-controlgroup-controls .ui-select {
	margin: 0;
}
.ui-controlgroup-controls .ui-btn:focus,
.ui-controlgroup-controls .ui-btn.ui-focus {
	z-index: 1;
}
.ui-controlgroup-controls li {
	list-style: none;
}
.ui-controlgroup-horizontal .ui-controlgroup-controls {
	display: inline-block;
	vertical-align: middle;
}
.ui-controlgroup-horizontal .ui-controlgroup-controls:before,
.ui-controlgroup-horizontal .ui-controlgroup-controls:after {
	content: "";
	display: table;
}
.ui-controlgroup-horizontal .ui-controlgroup-controls:after {
	clear: both;
}
.ui-controlgroup-horizontal .ui-controlgroup-controls > .ui-btn,
.ui-controlgroup-horizontal .ui-controlgroup-controls li > .ui-btn,
.ui-controlgroup-horizontal .ui-controlgroup-controls .ui-checkbox,
.ui-controlgroup-horizontal .ui-controlgroup-controls .ui-radio,
.ui-controlgroup-horizontal .ui-controlgroup-controls .ui-select {
	float: left;
	clear: none;
}
.ui-controlgroup-horizontal .ui-controlgroup-controls button.ui-btn,
.ui-controlgroup-controls .ui-btn-icon-notext {
	width: auto;
}
.ui-controlgroup-horizontal .ui-controlgroup-controls .ui-btn-icon-notext,
.ui-controlgroup-horizontal .ui-controlgroup-controls button.ui-btn-icon-notext {
	width: 1.5em;
}
 .ui-controlgroup-controls .ui-btn-icon-notext {
	height: auto;
	padding: .7em 1em;
}
.ui-controlgroup-vertical .ui-controlgroup-controls .ui-btn {
	border-bottom-width: 0;
}
.ui-controlgroup-vertical .ui-controlgroup-controls .ui-btn.ui-last-child {
	border-bottom-width: 1px;
}
.ui-controlgroup-horizontal .ui-controlgroup-controls .ui-btn {
	border-right-width: 0;
}
.ui-controlgroup-horizontal .ui-controlgroup-controls .ui-btn.ui-last-child {
	border-right-width: 1px;
}
.ui-controlgroup-controls .ui-btn-corner-all,
.ui-controlgroup-controls .ui-btn.ui-corner-all {
	-webkit-border-radius: 0;
	border-radius: 0;
}
.ui-controlgroup-controls,
.ui-controlgroup-controls .ui-radio,
.ui-controlgroup-controls .ui-checkbox,
.ui-controlgroup-controls .ui-select,
.ui-controlgroup-controls li {
	-webkit-border-radius: inherit;
	border-radius: inherit;
}
.ui-controlgroup-vertical .ui-btn.ui-first-child {
	-webkit-border-top-left-radius: inherit;
	border-top-left-radius: inherit;
	-webkit-border-top-right-radius: inherit;
	border-top-right-radius: inherit;
}
.ui-controlgroup-vertical .ui-btn.ui-last-child {
	-webkit-border-bottom-left-radius: inherit;
	border-bottom-left-radius: inherit;
	-webkit-border-bottom-right-radius: inherit;
	border-bottom-right-radius: inherit;
}
.ui-controlgroup-horizontal .ui-btn.ui-first-child {
	-webkit-border-top-left-radius: inherit;
	border-top-left-radius: inherit;
	-webkit-border-bottom-left-radius: inherit;
	border-bottom-left-radius: inherit;
}
.ui-controlgroup-horizontal .ui-btn.ui-last-child {
	-webkit-border-top-right-radius: inherit;
	border-top-right-radius: inherit;
	-webkit-border-bottom-right-radius: inherit;
	border-bottom-right-radius: inherit;
}
.ui-controlgroup-controls a.ui-shadow:not(:focus),
.ui-controlgroup-controls button.ui-shadow:not(:focus),
.ui-controlgroup-controls div.ui-shadow:not(.ui-focus) {
	-moz-box-shadow: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}
/* Fixes legend not wrapping on IE10 */
.ui-controlgroup-label legend {
	max-width: 100%;
}
.ui-controlgroup-controls > label {
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	clip: rect(1px,1px,1px,1px);
}
.ui-dialog {
	 background: none !important; /* this is to ensure that dialog theming does not apply (by default at least) on the page div */
}
.ui-dialog-contain {
	width: 92.5%;
	max-width: 500px;
	margin: 10% auto 1em auto;
	padding: 0;
	position: relative;
	top: -1em;
}
.ui-dialog-contain > .ui-header, 
.ui-dialog-contain > .ui-content, 
.ui-dialog-contain > .ui-footer { 
	display: block;
	position: relative; 
	width: auto;
	margin: 0;
}
.ui-dialog-contain > .ui-header {
	overflow: hidden;
	z-index: 10; 
	padding: 0;
	border-top-width: 0;
}
.ui-dialog-contain > .ui-footer {
	z-index: 10; 
	padding: 0 1em; 
	border-bottom-width: 0;
}
.ui-popup-open .ui-header-fixed,
.ui-popup-open .ui-footer-fixed {
	position: absolute !important; /* See issues #4816, #4844 and #4874 and popup.js */
}
.ui-popup-screen {
	background-image: url("data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=="); /* Necessary to set some form of background to ensure element is clickable in IE6/7. While legacy IE won't understand the data-URI'd image, it ensures no additional requests occur in all other browsers with little overhead. */
	top: 0;
	left: 0;
	right: 0;
	bottom: 1px;
	position: absolute;
	filter: Alpha(Opacity=0);
	opacity: 0;
	z-index: 1099;
}
.ui-popup-screen.in {
	opacity: 0.5;
	filter: Alpha(Opacity=50);
}
.ui-popup-screen.out {
	opacity: 0;
	filter: Alpha(Opacity=0);
}
.ui-popup-container {
	z-index: 1100;
	display: inline-block;
	position: absolute;
	padding: 0;
	outline: 0;
}
.ui-popup {
	position: relative;
}
.ui-popup.ui-body-inherit {
	border-width: 1px;
	border-style: solid;
}
.ui-popup-hidden {
	left: 0;
	top: 0;
	position: absolute !important;
	visibility: hidden;
}
.ui-popup-truncate {
	height: 1px;
	width: 1px;
	margin: -1px;
	overflow: hidden;
	clip: rect(1px,1px,1px,1px);
}
.ui-popup.ui-content,
.ui-popup .ui-content {
	overflow: visible;
}
.ui-popup > .ui-header {
	border-top-width: 0;
}
.ui-popup > .ui-footer {
	border-bottom-width: 0;
}
.ui-popup > p,
.ui-popup > h1,
.ui-popup > h2,
.ui-popup > h3,
.ui-popup > h4,
.ui-popup > h5,
.ui-popup > h6 {
	margin: .5em .4375em;
}
.ui-popup > span {
	display: block;
	margin: .5em .4375em;
}
.ui-popup-container .ui-content > p,
.ui-popup-container .ui-content > h1,
.ui-popup-container .ui-content > h2,
.ui-popup-container .ui-content > h3,
.ui-popup-container .ui-content > h4,
.ui-popup-container .ui-content > h5,
.ui-popup-container .ui-content > h6 {
	margin: .5em 0;
}
.ui-popup-container .ui-content > span {
	margin: 0;
}
.ui-popup-container .ui-content > p:first-child,
.ui-popup-container .ui-content > h1:first-child,
.ui-popup-container .ui-content > h2:first-child,
.ui-popup-container .ui-content > h3:first-child,
.ui-popup-container .ui-content > h4:first-child,
.ui-popup-container .ui-content > h5:first-child,
.ui-popup-container .ui-content > h6:first-child {
	margin-top: 0;
}
.ui-popup-container .ui-content > p:last-child,
.ui-popup-container .ui-content > h1:last-child,
.ui-popup-container .ui-content > h2:last-child,
.ui-popup-container .ui-content > h3:last-child,
.ui-popup-container .ui-content > h4:last-child,
.ui-popup-container .ui-content > h5:last-child,
.ui-popup-container .ui-content > h6:last-child {
	margin-bottom: 0;
}
.ui-popup > img {
	max-width: 100%;
	max-height: 100%;
	vertical-align: middle;
}
.ui-popup:not(.ui-content) > img:only-child,
.ui-popup:not(.ui-content) > .ui-btn-left:first-child + img:last-child,
.ui-popup:not(.ui-content) > .ui-btn-right:first-child + img:last-child {
	-webkit-border-radius: inherit;
	border-radius: inherit;
}
.ui-popup iframe {
	vertical-align: middle;
}
.ui-popup > .ui-btn-left,
.ui-popup > .ui-btn-right {
	position: absolute; 
	top: -11px;
	margin: 0;
	z-index: 1101;
}
.ui-popup > .ui-btn-left {
	left: -11px;
}
.ui-popup > .ui-btn-right {
	right: -11px;
}
/* Dimensions related to the popup arrow
-----------------------------------------------------------------------------------------------------------*/
/* desired triangle height: 10px */
/**
 * guide for the arrow - its width, height, and offset are theme-dependent and
 * should be expessed as left, right, top, bottom, so that the element bearing
 * such a class becomes stretched inside its parent position: relative element.
 * The left/top/right/bottom specified below should reflect the corresponding
 * border radii and so it leaves room for the shadow:
 *     ..--------------------..
 *   ."        ^ top           ".
 *  /          v                 \
 * |     +------------------+     |
 * |     |                  |     |
 * | left|                  |right|
 * |<--->|                  |<--->|
 * |     +------------------+     |
 *  \          ^                 /
 *   `.        v bottom        .'
 *     ""--------------------""
 * The idea is that the top/left of the arrow container box does not move to a
 * coordinate smaller than the top/left of the guide and the right/bottom of
 * the arrow container box does not move to a coordinate larger than the
 * bottom/right of the guide. This will help us avoid the following situation:
 *        ..--------------------..
 *      ."        ^ top           ".
 *   /|/          v                 \
 *  / |     +------------------+     |
 *  \ |     |                  |     |
 *   \| left|                  |right|
 *    |<--->|                  |<--->|
 *    |     +------------------+     |
 *     \          ^                 /
 *      `.        v bottom        .'
 *        ""--------------------""
 * The arrow should not receive a top/left coordinate such that it is too close
 * to one of the corners, because then at first the shadow of the arrow and,
 * given a coordinate even closer to the corner, even the body of the arrow will
 * "stick out" of the corner of the popup. The guide provides a hint to the
 * arrow positioning code as to which range of values is acceptable for the
 * arrow container's top/left coordinate.
 **/
.ui-popup-arrow-container {
	width: 20px;
	height: 20px;
}
/* aside from the "infinities" (-1000,2000), triangle height is used */
.ui-popup-arrow-container.ui-popup-arrow-l {
	left: -10px;
	clip: rect(-1000px,10px,2000px,-1000px);
}
.ui-popup-arrow-container.ui-popup-arrow-t {
	top: -10px;
	clip: rect(-1000px,2000px,10px,-1000px);
}
.ui-popup-arrow-container.ui-popup-arrow-r {
	right: -10px;
	clip: rect(-1000px,2000px,2000px,10px);
}
.ui-popup-arrow-container.ui-popup-arrow-b {
	bottom: -10px;
	clip: rect(10px,2000px,1000px,-1000px);
}
/**
 * For each side, the arrow is twice the desired size and its corner is aligned
 * with the edge of the container:
 *                                           
 *           /\         /\                +----+       /\
 *          /  \       /  \               | /\ |top   /  \
 *      +----+  \     /  +----+       +-->|/  \|     /    \
 *  left| /  |   \   /   |  \ |right  |   |    |    /      \
 *      |/   |    \ /    |   \|       |  /|    |\  /        \
 *      |\   |    / \    |   /|       | / +----+ \ \ +----+ /
 *      | \  |   /   \   |  / |       | \        /  \|    |/
 *      +----+  /     \  +----+       |  \      /    |    |
 *       ^  \  /       \  /  ^        |   \    /  +->|\  /|
 *       |   \/         \/   |        |    \  /   |  | \/ |bottom
 *       |                   |        |     \/    |  +----+
 *       +-------------------+--------+-----------+
 *                           |
 *                    arrow container
 *                     (clips arrow)
 **/
.ui-popup-arrow-container .ui-popup-arrow {
	/* (4*desired triangle height)/sqrt(2) - does not account for border - centred within the outer rectangle */
	width: 28.284271247px;
	height: 28.284271247px;
	border-width: 1px;
	border-style: solid;
}
.ui-popup-arrow-container.ui-popup-arrow-t .ui-popup-arrow {
	left: -4.142135623px;
	top: 5.857864376px;
}
.ui-popup-arrow-container.ui-popup-arrow-b .ui-popup-arrow {
	left: -4.142135623px;
	top: -14.142135623px;
}
.ui-popup-arrow-container.ui-popup-arrow-l .ui-popup-arrow {
	left: 5.857864376px;
	top: -4.142135623px;
}
.ui-popup-arrow-container.ui-popup-arrow-r .ui-popup-arrow {
	left: -14.142135623px;
	top: -4.142135623px;
}
/* Fix rotation center for oldIE - see http://www.useragentman.com/IETransformsTranslator/ */
.ui-popup-arrow-container.ui-popup-arrow-t.ie .ui-popup-arrow {
    margin-left: -5.857864376269049px;
    margin-top: -7.0710678118654755px;
}
.ui-popup-arrow-container.ui-popup-arrow-b.ie .ui-popup-arrow {
    margin-left: -5.857864376269049px;
    margin-top: -4.142135623730951px;
}
 
.ui-popup-arrow-container.ui-popup-arrow-l.ie .ui-popup-arrow {
    margin-left: -7.0710678118654755px;
    margin-top: -5.857864376269049px;
}
.ui-popup-arrow-container.ui-popup-arrow-r.ie .ui-popup-arrow {
    margin-left: -4.142135623730951px;
    margin-top: -5.857864376269049px;
}
/* structure */
.ui-popup > .ui-popup-arrow-guide {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	visibility: hidden;
}
.ui-popup-arrow-container {
	position: absolute;
}
.ui-popup-arrow {
	-webkit-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	transform: rotate(45deg);
	position: absolute;
	overflow: hidden;
	box-sizing: border-box;
}
.ui-popup-arrow-container.ie .ui-popup-arrow {
	-ms-filter: "progid:DXImageTransform.Microsoft.Matrix(M11=0.7071067811865474, M12=-0.7071067811865477, M21=0.7071067811865477, M22=0.7071067811865474, SizingMethod='auto expand')";
	filter: progid:DXImageTransform.Microsoft.Matrix(
	      	M11=0.7071067811865474,
        	M12=-0.7071067811865477,
        	M21=0.7071067811865477,
        	M22=0.7071067811865474,
        	SizingMethod='auto expand');
}
.ui-checkbox,
.ui-radio {
	margin: .5em 0;
	position: relative;
}
.ui-checkbox .ui-btn,
.ui-radio .ui-btn {
	margin: 0;
	text-align: left;
	white-space: normal; /* Nowrap + ellipsis doesn't work on label. Issue #1419. */
	z-index: 2;
}
.ui-controlgroup .ui-checkbox .ui-btn.ui-focus,
.ui-controlgroup .ui-radio .ui-btn.ui-focus {
	z-index: 3;
}
.ui-checkbox .ui-btn-icon-top,
.ui-radio .ui-btn-icon-top,
.ui-checkbox .ui-btn-icon-bottom,
.ui-radio .ui-btn-icon-bottom {
	text-align: center;
}
.ui-controlgroup-horizontal .ui-checkbox .ui-btn:after,
.ui-controlgroup-horizontal .ui-radio .ui-btn:after {
	content: none;
	display: none;
}
/* Native input positioning */
.ui-checkbox input,
.ui-radio input {
	position: absolute;
	left: .466em;
	top: 50%;
	width: 22px;
	height: 22px;
	margin: -11px 0 0 0;
	outline: 0 !important;
	z-index: 1;
}
.ui-controlgroup-horizontal .ui-checkbox input,
.ui-controlgroup-horizontal .ui-radio input {
	left: 50%;
	margin-left: -9px;
}
.ui-checkbox input:disabled,
.ui-radio input:disabled {
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	clip: rect(1px,1px,1px,1px);
}
.ui-select {
	margin-top: .5em;
	margin-bottom: .5em; /* no shorthand for margin because it would override margin-right for inline selects */
	position: relative;
}
.ui-select > select {
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	clip: rect(1px,1px,1px,1px);
}
.ui-select .ui-btn {
	margin: 0;
	opacity: 1; /* Fixes #2588: When Windows Phone 7.5 (Mango) tries to calculate a numeric opacity for a select (including "inherit") without explicitly specifying an opacity on the parent to give it context, a bug appears where clicking elsewhere on the page after opening the select will open the select again. */
}
.ui-select .ui-btn select {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	min-height: 1.5em;
	min-height: 100%;
	height: 3em;
	max-height: 100%;
	outline: 0;
	-webkit-border-radius: inherit;
	border-radius: inherit;	
	-webkit-appearance: none;
	-moz-appearance: none;
	cursor: pointer;
	filter: Alpha(Opacity=0);
	opacity: 0;
	z-index: 2;
}
@-moz-document url-prefix() {
	.ui-select .ui-btn select {
		opacity: 0.0001;
	}
}
/* Display none because of issues with IE/WP's filter alpha opacity */
.ui-select .ui-state-disabled select {
	display: none;
}
/* Because we add all classes of the select and option elements to the span... */ 
.ui-select span.ui-state-disabled {
	filter: Alpha(Opacity=100);
	opacity: 1;
}
.ui-select .ui-btn.ui-select-nativeonly {
	border-radius: 0;
	border: 0;
}
.ui-select .ui-btn.ui-select-nativeonly select {
	opacity: 1;
	text-indent: 0;
	display: block;
}
/* ui-li-count is styled in the listview CSS. We set padding and offset here because select supports icon position while listview doesn't. */
.ui-select .ui-li-has-count.ui-btn {
	padding-right: 2.8125em;
}
.ui-select .ui-li-has-count.ui-btn-icon-right {
	padding-right: 4.6875em;
}
.ui-select .ui-btn-icon-right .ui-li-count {
	right: 3.2em;
}
/* We set the rules for the span as well to fix an issue on Chrome with text-overflow ellipsis for the button in combination with text-align center. */
.ui-select .ui-btn > span:not(.ui-li-count) {
	display: block;
	text-overflow: ellipsis;
	overflow: hidden !important;
	white-space: nowrap;
}
.ui-selectmenu.ui-popup {
	min-width: 11em;
}
.ui-selectmenu .ui-dialog-contain {
	overflow: hidden;
}
.ui-selectmenu .ui-header {
	margin: 0;
	padding: 0;
	border-width: 0;
}
.ui-selectmenu.ui-dialog .ui-header {
	z-index: 1;
	position: relative;
}
.ui-selectmenu.ui-popup .ui-header {
	-webkit-border-bottom-right-radius: 0;
	border-bottom-right-radius: 0;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
}
/* when no placeholder is defined in a multiple select, the header height doesn't even extend past the close button.  this shim's content in there */
.ui-selectmenu.ui-popup .ui-header h1:after {
	content: '.';
	visibility: hidden;
}
.ui-selectmenu .ui-header .ui-title {
	margin: 0 2.875em;
}
.ui-selectmenu.ui-dialog .ui-content {
	overflow: visible;
	z-index: 1;
}
.ui-selectmenu .ui-selectmenu-list {
	margin: 0;
	-webkit-border-radius: inherit;
	border-radius: inherit;	
}
.ui-header:not(.ui-screen-hidden) + .ui-selectmenu-list {
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
	-webkit-border-top-left-radius: 0;
	border-top-left-radius: 0;
}
.ui-header.ui-screen-hidden + .ui-selectmenu-list li.ui-first-child .ui-btn {
	border-top-width: 0;
}
.ui-selectmenu .ui-selectmenu-list li.ui-last-child .ui-btn {
	border-bottom-width: 0;
}
.ui-selectmenu .ui-btn.ui-li-divider {
	cursor: default;
}
.ui-selectmenu .ui-selectmenu-placeholder {
	display: none;
}
.ui-listview,
.ui-listview > li {
	margin: 0;
	padding: 0;
	list-style: none;
}
.ui-content .ui-listview,
.ui-panel-inner > .ui-listview {
	margin: -1em;
}
.ui-content .ui-listview-inset,
.ui-panel-inner > .ui-listview-inset {
	margin: 1em 0;
}
.ui-collapsible-content > .ui-listview {
	margin: -.5em -1em;
}
.ui-collapsible-content > .ui-listview-inset {
	margin: .5em 0;
}
.ui-listview > li {
	display: block;
	position: relative;
	overflow: visible;
}
.ui-listview > .ui-li-static,
.ui-listview > .ui-li-divider,
.ui-listview > li > a.ui-btn {
	margin: 0;
	display: block;
	position: relative;
	text-align: left;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}
.ui-listview > li > .ui-btn:focus {
	z-index: 1;
}
.ui-listview > .ui-li-static,
.ui-listview > .ui-li-divider,
.ui-listview > li > a.ui-btn {
	border-width: 1px 0 0 0;
	border-style: solid;
}
.ui-listview-inset > .ui-li-static,
.ui-listview-inset > .ui-li-divider,
.ui-listview-inset > li > a.ui-btn {
	border-right-width: 1px;
	border-left-width: 1px;
}
.ui-listview > .ui-li-static.ui-last-child,
.ui-listview > .ui-li-divider.ui-last-child,
.ui-listview > li.ui-last-child > a.ui-btn {
	border-bottom-width: 1px;
}
.ui-collapsible-content > .ui-listview:not(.ui-listview-inset) > li.ui-first-child,
.ui-collapsible-content > .ui-listview:not(.ui-listview-inset) > li.ui-first-child > a.ui-btn {
	border-top-width: 0;
}
.ui-collapsible-themed-content .ui-listview:not(.ui-listview-inset) > li.ui-last-child,
.ui-collapsible-themed-content .ui-listview:not(.ui-listview-inset) > li.ui-last-child > a.ui-btn {
	border-bottom-width: 0;
}
.ui-listview > li.ui-first-child,
.ui-listview > li.ui-first-child > a.ui-btn {
	-webkit-border-top-right-radius: inherit;	
	border-top-right-radius: inherit;
	-webkit-border-top-left-radius: inherit;
	border-top-left-radius: inherit;
}
.ui-listview > li.ui-last-child,
.ui-listview > li.ui-last-child > a.ui-btn {
	-webkit-border-bottom-right-radius: inherit;
	border-bottom-right-radius: inherit;
	-webkit-border-bottom-left-radius: inherit;
	border-bottom-left-radius: inherit;
}
.ui-listview > li.ui-li-has-alt > a.ui-btn {
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
	-webkit-border-bottom-right-radius: 0;
	border-bottom-right-radius: 0;
}
.ui-listview > li.ui-first-child > a.ui-btn + a.ui-btn {
	-webkit-border-top-left-radius: 0;	
	border-top-left-radius: 0;
	-webkit-border-top-right-radius: inherit;
	border-top-right-radius: inherit;
}
.ui-listview > li.ui-last-child > a.ui-btn + a.ui-btn {
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
	-webkit-border-bottom-right-radius: inherit;
	border-bottom-right-radius: inherit;
}
.ui-listview > li.ui-first-child img:first-child:not(.ui-li-icon) {
	-webkit-border-top-left-radius: inherit;
	border-top-left-radius: inherit;	
}
.ui-listview > li.ui-last-child img:first-child:not(.ui-li-icon) {
	-webkit-border-bottom-left-radius: inherit;
	border-bottom-left-radius: inherit;	
}
.ui-collapsible-content > .ui-listview:not(.ui-listview-inset) {
	-webkit-border-radius: inherit;
	border-radius: inherit;	
}
.ui-listview > .ui-li-static {
	padding: .7em 1em;
}
.ui-listview > .ui-li-divider {
	padding: .5em 1.143em;
	font-size: 14px;
	font-weight: bold;
	cursor: default;
	outline: 0; /* Dividers in custom selectmenus have tabindex */
}
.ui-listview > .ui-li-has-count > .ui-btn,
.ui-listview > .ui-li-static.ui-li-has-count,
.ui-listview > .ui-li-divider.ui-li-has-count {
	padding-right: 2.8125em;
}
.ui-listview > .ui-li-has-count > .ui-btn-icon-right {
	padding-right: 4.6875em;
}
.ui-listview > .ui-li-has-thumb > .ui-btn,
.ui-listview > .ui-li-static.ui-li-has-thumb {
	min-height: 3.625em;
	padding-left: 6.25em;
}
/* ui-li-has-icon deprecated in 1.4. TODO: remove in 1.5 */
.ui-listview > .ui-li-has-icon > .ui-btn,
.ui-listview > .ui-li-static.ui-li-has-icon {
	min-height: 1.25em;
	padding-left: 2.5em;
}
/* Used by both listview and custom multiple select button */
.ui-li-count {
	position: absolute;
	font-size: 12.5px;
	font-weight: bold;
	text-align: center;
	border-width: 1px;
	border-style: solid;
	padding: 0 .48em;
	line-height: 1.6em;
	min-height: 1.6em;
	min-width: .64em;
	right: .8em;
	top: 50%;
	margin-top: -.88em;
}
.ui-listview .ui-btn-icon-right .ui-li-count {
	right: 3.2em;
}
.ui-listview .ui-li-has-thumb > img:first-child,
.ui-listview .ui-li-has-thumb > .ui-btn > img:first-child,
.ui-listview .ui-li-has-thumb .ui-li-thumb {
	position: absolute;
	left: 0;
	top: 0;
	max-height: 5em;
	max-width: 5em;
}
/* ui-li-has-icon deprecated in 1.4. TODO: remove in 1.5 */
.ui-listview > .ui-li-has-icon > img:first-child,
.ui-listview > .ui-li-has-icon > .ui-btn > img:first-child {
	position: absolute;
	left: .625em;
	top: .9em;
	max-height: 1em;
	max-width: 1em;
}
.ui-listview > li h1,
.ui-listview > li h2,
.ui-listview > li h3,
.ui-listview > li h4,
.ui-listview > li h5,
.ui-listview > li h6 {
	font-size: 1em;
	font-weight: bold;
	display: block;
	margin: .45em 0;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}
.ui-listview > li p {
	font-size: .75em;
	font-weight: normal;
	display: block;
	margin: .6em 0;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}
.ui-listview .ui-li-aside {
	position: absolute;
	top: 1em;
	right: 3.333em;
	margin: 0;
	text-align: right;
}
.ui-listview > li.ui-li-has-alt > .ui-btn {
	margin-right: 2.5em;
	border-right-width: 0;
}
.ui-listview > li.ui-li-has-alt > .ui-btn + .ui-btn {
	position: absolute;
	width: 2.5em;
	height: 100%;
	min-height: auto;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	border-left-width: 1px;
	top: 0;
	right: 0;
	margin: 0;
	padding: 0;
	z-index: 2;
}
.ui-listview-inset > li.ui-li-has-alt > .ui-btn + .ui-btn {
	border-right-width: 1px;
}
.ui-listview > li.ui-li-has-alt > .ui-btn + .ui-btn:focus {
	z-index: 3;
}
ol.ui-listview,
ol.ui-listview > .ui-li-divider {
	counter-reset: listnumbering;
}
ol.ui-listview > li > .ui-btn,
ol.ui-listview > li.ui-li-static {
	vertical-align: middle;
}
ol.ui-listview > li > .ui-btn:first-child:before,
ol.ui-listview > li.ui-li-static:before,
ol.ui-listview > li.ui-field-contain > label:before,
ol.ui-listview > li.ui-field-contain > .ui-controlgroup-label:before {
	display: inline-block;
	font-size: .9em;
	font-weight: normal;
	padding-right: .3em;
	min-width: 1.4em;
	line-height: 1.5;
	vertical-align: middle;
	counter-increment: listnumbering;
	content: counter(listnumbering) ".";
}
ol.ui-listview > li.ui-field-contain:before {
	content: none;
	display: none;
}
ol.ui-listview > li h1:first-child,
ol.ui-listview > li h2:first-child,
ol.ui-listview > li h3:first-child,
ol.ui-listview > li h4:first-child,
ol.ui-listview > li h5:first-child,
ol.ui-listview > li h6:first-child,
ol.ui-listview > li p:first-child,
ol.ui-listview > li img:first-child + * {
	display: inline-block;
	vertical-align: middle;
}
ol.ui-listview > li h1:first-child ~ *,
ol.ui-listview > li h2:first-child ~ *,
ol.ui-listview > li h3:first-child ~ *,
ol.ui-listview > li h4:first-child ~ *,
ol.ui-listview > li h5:first-child ~ *,
ol.ui-listview > li h6:first-child ~ *,
ol.ui-listview > li p:first-child ~ *,
ol.ui-listview > li img:first-child + * ~ * {
	margin-top: 0;
	text-indent: 2.04em; /* (1.4em + .3em) * .9em / .75em */
}
html .ui-filterable + .ui-listview,
html .ui-filterable.ui-listview {
	margin-top: .5em;
}
.ui-collapsible-content > form.ui-filterable {
	margin-top: -.5em;
}
.ui-collapsible-content > .ui-input-search.ui-filterable {
	margin-top: 0;
}
.ui-collapsible-content > .ui-filterable + .ui-listview:not(.ui-listview-inset) > li.ui-first-child,
.ui-collapsible-content > .ui-filterable + .ui-listview:not(.ui-listview-inset) > li.ui-first-child > a.ui-btn,
.ui-collapsible-content > .ui-filterable.ui-listview:not(.ui-listview-inset) > li.ui-first-child,
.ui-collapsible-content > .ui-filterable.ui-listview:not(.ui-listview-inset) > li.ui-first-child > a.ui-btn {
	border-top-width: 1px;
}
div.ui-slider {
	height: 30px;
	margin: .5em 0;
	padding: 0;
	-ms-touch-action: pan-y pinch-zoom double-tap-zoom;
}
div.ui-slider:before,
div.ui-slider:after {
	content: "";
	display: table;
}
div.ui-slider:after {
	clear: both;
}
input.ui-slider-input {
	display: block;
	float: left;
	font-size: 14px;
	font-weight: bold;
	margin: 0;
	padding: 4px;
	width: 40px;
	height: 20px;
	line-height: 20px;
	border-width: 1px;
	border-style: solid;
	outline: 0;
	text-align: center;
	vertical-align: text-bottom;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
}
.ui-slider-input::-webkit-outer-spin-button,
.ui-slider-input::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}
.ui-slider-track {
	position: relative;
	overflow: visible;
	border-width: 1px;
	border-style: solid;
	height: 15px;
	margin: 0 15px 0 68px;
	top: 6px;
}
.ui-slider-track.ui-mini {
	height: 12px;
	top: 8px;
}
.ui-slider-track .ui-slider-bg {
	height: 100%;
}
/* High level of specificity to override button margins in grids */
.ui-slider-track .ui-btn.ui-slider-handle {
	position: absolute;
	z-index: 1;
	top: 50%;
	width: 28px;
	height: 28px;
	margin: -15px 0 0 -15px;
	outline: 0;
	padding: 0;
}
.ui-slider-track.ui-mini .ui-slider-handle {
	height: 14px;
	width: 14px;
	margin: -8px 0 0 -8px;
}
select.ui-slider-switch {
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	clip: rect(1px,1px,1px,1px);
}
div.ui-slider-switch {
	display: inline-block;
	height: 32px;
	width: 5.8em;
	top: 0;
}
/* reset the clearfix */
div.ui-slider-switch:before,
div.ui-slider-switch:after {
	display: none;
	clear: none;
}
div.ui-slider-switch.ui-mini {
	height: 29px;
	top: 0;
}
.ui-slider-inneroffset {
	margin: 0 16px;
	position: relative;
	z-index: 1;
}
.ui-slider-switch.ui-mini .ui-slider-inneroffset {
	margin: 0 15px 0 14px;
}
.ui-slider-switch .ui-btn.ui-slider-handle {
	margin: 1px 0 0 -15px;
}
.ui-slider-switch.ui-mini .ui-slider-handle {
	width: 25px;
	height: 25px;
	margin: 1px 0 0 -13px;
	padding: 0;
}
.ui-slider-handle-snapping {
	-webkit-transition: left 70ms linear;
	-moz-transition: left 70ms linear;
	transition: left 70ms linear;
}
.ui-slider-switch .ui-slider-label {
	position: absolute;
	text-align: center;
	width: 100%;
	overflow: hidden;
	font-size: 16px;
	top: 0;
	line-height: 2;
	min-height: 100%;
	white-space: nowrap;
	cursor: pointer;
}
.ui-slider-switch.ui-mini .ui-slider-label {
	font-size: 14px;
}
.ui-slider-switch .ui-slider-label-a {
	z-index: 1;
	left: 0;
	text-indent: -1.5em;
}
.ui-slider-switch .ui-slider-label-b {
	z-index: 0;
	right: 0;
	text-indent: 1.5em;
}
/* The corner radii for ui-slider-switch/track can be specified in theme CSS. The bg and handle inherits. */
.ui-slider-track .ui-slider-bg,
.ui-slider-switch .ui-slider-label,
.ui-slider-switch .ui-slider-inneroffset,
.ui-slider-handle {
	-webkit-border-radius: inherit;
	border-radius: inherit;
}
.ui-field-contain div.ui-slider-switch {
	margin: 0;
}
/* ui-hide-label deprecated in 1.4. TODO: Remove in 1.5 */
.ui-field-contain div.ui-slider-switch,
.ui-field-contain.ui-hide-label div.ui-slider-switch,
html .ui-popup .ui-field-contain div.ui-slider-switch {
	display: inline-block;
	width: 5.8em;
}
/* slider tooltip
-----------------------------------------------------------------------------------------------------------*/
.ui-slider-popup {
	width: 64px;
	height: 64px;
	font-size: 36px;
	padding-top: 14px;
	opacity: 0.8;
}
.ui-slider-popup {
	position: absolute !important;
	text-align: center;
	z-index: 100;
}
.ui-slider-track .ui-btn.ui-slider-handle {
	font-size: .9em;
	line-height: 30px;
}
.ui-rangeslider {
	margin: .5em 0;
}
.ui-rangeslider:before,
.ui-rangeslider:after {
	content: "";
	display: table;
}
.ui-rangeslider:after {
	clear: both;
}
.ui-rangeslider .ui-slider-input.ui-rangeslider-last {
	float: right;
}
.ui-rangeslider .ui-rangeslider-sliders {
	position: relative;
	overflow: visible;
	height: 30px;
	margin: 0 68px;
}
.ui-rangeslider .ui-rangeslider-sliders .ui-slider-track {
	position: absolute;
	top: 6px;
	right: 0;
	left: 0;
	margin: 0;
}
.ui-rangeslider.ui-mini .ui-rangeslider-sliders .ui-slider-track {
	top: 8px;
}
.ui-rangeslider .ui-slider-track:first-child .ui-slider-bg {
	display: none;
}
.ui-rangeslider .ui-rangeslider-sliders .ui-slider-track:first-child {
	background-color: transparent;
	background: none;
	border-width: 0;
	height: 0;
}
/* this makes ie6 and ie7 set height to 0 to fix z-index problem */
html >/**/body .ui-rangeslider .ui-rangeslider-sliders .ui-slider-track:first-child {
	height: 15px;
	border-width: 1px;
}
html >/**/body .ui-rangeslider.ui-mini .ui-rangeslider-sliders .ui-slider-track:first-child {
	height: 12px;
}
/* Hide the second label (the first is moved outside the div) */
div.ui-rangeslider label {
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	clip: rect(1px,1px,1px,1px);
}
.ui-field-contain .ui-rangeslider input.ui-slider-input,
.ui-field-contain .ui-rangeslider.ui-mini input.ui-slider-input,
.ui-field-contain .ui-rangeslider .ui-rangeslider-sliders,
.ui-field-contain .ui-rangeslider.ui-mini .ui-rangeslider-sliders {
	margin-top: 0;
	margin-bottom: 0;
}
.ui-input-text,
.ui-input-search {
	margin: .5em 0;
	border-width: 1px;
	border-style: solid;
}
.ui-mini {
	margin: .446em;
}
.ui-input-text input,
.ui-input-search input,
textarea.ui-input-text {
	padding: .4em;
	line-height: 1.4em;
	display: block;
	width: 100%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	outline: 0;
}
.ui-input-text input,
.ui-input-search input {
	margin: 0;
	min-height: 2.2em;
	text-align: left; /* Opera aligns type="date" right by default */
	border: 0;
	background: transparent none;
	-webkit-appearance: none;
	-webkit-border-radius: inherit;
	border-radius: inherit;
}
textarea.ui-input-text {
	overflow: auto;
	resize: vertical;
}
.ui-mini .ui-input-text input,
.ui-mini .ui-input-search input,
.ui-input-text.ui-mini input,
.ui-input-search.ui-mini input,
.ui-mini textarea.ui-input-text,
textarea.ui-mini {
	font-size: 14px;
}
/* Same margin for mini textareas as other mini sized widgets (12.5/14 * 0.5em) */
.ui-mini textarea.ui-input-text,
textarea.ui-mini {
	margin: .446em 0;
}
.ui-input-has-clear,
.ui-input-search {
	position: relative;
}
/* Padding on the div instead of input because of browser spinners etc. */
.ui-input-has-clear {
	padding-right: 2.375em;
}
.ui-mini.ui-input-has-clear {
	padding-right: 2.923em;
}
.ui-input-has-clear input {
	padding-right: 0;
	/* Autofill on Chrome has bg color so we unset corners right as well. */
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
	-webkit-border-bottom-right-radius: 0;
	border-bottom-right-radius: 0;
}
/* Search icon */
.ui-input-search input {
	padding-left: 1.75em;
}
.ui-input-search:after {
	position: absolute;
	left: .3125em;
	top: 50%;
	margin-top: -7px;
	content: "";
	background-position: center center;
	background-repeat: no-repeat;
	width: 14px;
	height: 14px;
	filter: Alpha(Opacity=50);
	opacity: .5;
}
.ui-input-search.ui-input-has-clear .ui-btn.ui-input-clear,
.ui-input-text.ui-input-has-clear .ui-btn.ui-input-clear {
	position: absolute;
	right: 0;
	top: 50%;
	margin: -14px .3125em 0;
	border: 0;
	background-color: transparent;
}
.ui-input-search .ui-input-clear-hidden,
.ui-input-text .ui-input-clear-hidden {
	display: none;
}
/* Resolves issue #5166: Added to support issue introduced in Firefox 15. We can likely remove this in the future. */
.ui-input-text input::-moz-placeholder,
.ui-input-search input::-moz-placeholder,
textarea.ui-input-text::-moz-placeholder {
	color: #aaa;
}
/* Same for IE10 */
.ui-input-text input:-ms-input-placeholder,
.ui-input-search input:-ms-input-placeholder,
textarea.ui-input-text:-ms-input-placeholder {
	color: #aaa;
}
/* Resolves issue #5131: Width of textinput depends on its type,
for Android 4.1 */
.ui-input-text input[type=number]::-webkit-outer-spin-button {
	margin: 0;
}
/* Resolves issue #5756: Textinput in IE10 has a default clear button */
.ui-input-text input::-ms-clear,
.ui-input-search input::-ms-clear {
	display: none;
}
.ui-input-text input:focus,
.ui-input-search input:focus {
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
}
textarea.ui-input-text.ui-textinput-autogrow {
	overflow: hidden;
}
.ui-textinput-autogrow-resize {
	-webkit-transition: height 0.25s;
	-o-transition: height 0.25s;
	-moz-transition: height 0.25s;
	transition: height 0.25s;
}
.ui-flipswitch {
	display: inline-block;
	vertical-align: middle;
	width: 5.875em; /* Override this and padding-left in next rule if you use labels other than "on/off" and need more space */
	height: 1.875em;
	border-width: 1px;
	border-style: solid;
	margin: .5em 0;
	overflow: hidden;
	-webkit-transition-property: padding, width, background-color, color, border-color;
	-moz-transition-property: padding, width, background-color, color, border-color;
	-o-transition-property: padding, width, background-color, color, border-color;
	transition-property: padding, width, background-color, color, border-color;
	-webkit-transition-duration: 100ms;
	-moz-transition-duration: 100ms;
	-o-transition-duration: 100ms;
	transition-duration: 100ms;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	cursor: pointer;
}
.ui-flipswitch.ui-flipswitch-active {
	padding-left: 4em;  /* Override this and width in previous rule if you use labels other than "on/off" and need more space */
	width: 1.875em;
}
.ui-flipswitch-input {
	position: absolute;
	height: 1px;
	width: 1px;
	margin: -1px;
	overflow: hidden;
	clip: rect(1px,1px,1px,1px);
	border: 0;
	outline: 0;
	filter: Alpha(Opacity=0);
	opacity: 0;
}
.ui-flipswitch .ui-btn.ui-flipswitch-on,
.ui-flipswitch .ui-flipswitch-off {
	float: left;
	height: 1.75em;
	margin: .0625em;
	line-height: 1.65em;
}
.ui-flipswitch .ui-btn.ui-flipswitch-on {
	width: 1.75em;
	padding: 0;
	text-indent: -2.6em; /* Override this to center text if you use a label other than "on" */
	text-align: left;
	border-width: 1px;
	border-style: solid;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	border-radius: inherit;
	overflow: visible;
	color: inherit;
	text-shadow: inherit;
}
.ui-flipswitch .ui-flipswitch-off {
	padding: 1px;
	text-indent: 1em; /* Override this to center text if you use a label other than "off" */
}
/* Override field container CSS to prevent the flipswitch from becomming full width */
html .ui-field-contain > label + .ui-flipswitch,
html .ui-popup .ui-field-contain > label + .ui-flipswitch {
	display: inline-block;
	width: 5.875em; /* If you override the width for .ui-flipswitch you should repeat the same value here */
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
}
.ui-field-contain .ui-flipswitch.ui-flipswitch-active,
.ui-popup .ui-field-contain .ui-flipswitch.ui-flipswitch-active {
	width: 1.875em;
}
.ui-table {
	border: 0;
	border-collapse: collapse;
	padding: 0;
	width: 100%;
}
.ui-table th,
.ui-table td {
	line-height: 1.5em;
	text-align: left;
	padding: .4em .5em;
	vertical-align:top;
}
.ui-table th .ui-btn,
.ui-table td .ui-btn {
	line-height: normal;
}
.ui-table th {
	font-weight: bold;
}
.ui-table caption {
	text-align: left;
	margin-bottom: 1.4em;
	opacity: .5;
}
/*
 Styles for the table columntoggle mode
*/
.ui-table-columntoggle-btn {
	float: right;
	margin-bottom: .8em;
}
/* Remove top/bottom margins around the fieldcontain on check list */
.ui-table-columntoggle-popup fieldset {
	margin:0;
}
.ui-table-columntoggle {
	clear: both;
}
/* Hide all prioritized columns by default */
@media only all {
	th.ui-table-priority-6,
	td.ui-table-priority-6,
	th.ui-table-priority-5,
	td.ui-table-priority-5,
	th.ui-table-priority-4,
	td.ui-table-priority-4,
	th.ui-table-priority-3,
	td.ui-table-priority-3,
	th.ui-table-priority-2,
	td.ui-table-priority-2,
	th.ui-table-priority-1,
	td.ui-table-priority-1 {
		display: none;
	}
}
/* Preset breakpoints if ".ui-responsive" class added to table */
/* Show priority 1 at 320px (20em x 16px) */
@media screen and (min-width: 20em) {
	.ui-table-columntoggle.ui-responsive th.ui-table-priority-1,
	.ui-table-columntoggle.ui-responsive td.ui-table-priority-1 {
		display: table-cell;
	}
}
/* Show priority 2 at 480px (30em x 16px) */
@media screen and (min-width: 30em) {
	.ui-table-columntoggle.ui-responsive th.ui-table-priority-2,
	.ui-table-columntoggle.ui-responsive td.ui-table-priority-2 {
		display: table-cell;
	}
}
/* Show priority 3 at 640px (40em x 16px) */
@media screen and (min-width: 40em) {
	.ui-table-columntoggle.ui-responsive th.ui-table-priority-3,
	.ui-table-columntoggle.ui-responsive td.ui-table-priority-3 {
		display: table-cell;
	}
}
/* Show priority 4 at 800px (50em x 16px) */
@media screen and (min-width: 50em) {
	.ui-table-columntoggle.ui-responsive th.ui-table-priority-4,
	.ui-table-columntoggle.ui-responsive td.ui-table-priority-4 {
		display: table-cell;
	}
}
/* Show priority 5 at 960px (60em x 16px) */
@media screen and (min-width: 60em) {
	.ui-table-columntoggle.ui-responsive th.ui-table-priority-5,
	.ui-table-columntoggle.ui-responsive td.ui-table-priority-5 {
		display: table-cell;
	}
}
/* Show priority 6 at 1,120px (70em x 16px) */
@media screen and (min-width: 70em) {
	.ui-table-columntoggle.ui-responsive th.ui-table-priority-6,
	.ui-table-columntoggle.ui-responsive td.ui-table-priority-6 {
		display: table-cell;
	}
}
/* Unchecked manually: Always hide */
.ui-table-columntoggle th.ui-table-cell-hidden,
.ui-table-columntoggle td.ui-table-cell-hidden,
.ui-table-columntoggle.ui-responsive th.ui-table-cell-hidden,
.ui-table-columntoggle.ui-responsive td.ui-table-cell-hidden {
	display: none;
}
/* Checked manually: Always show */
.ui-table-columntoggle th.ui-table-cell-visible,
.ui-table-columntoggle td.ui-table-cell-visible,
.ui-table-columntoggle.ui-responsive th.ui-table-cell-visible,
.ui-table-columntoggle.ui-responsive td.ui-table-cell-visible {
	display: table-cell;
}
/*
 Styles for the table columntoggle mode
*/
.ui-table-reflow td .ui-table-cell-label,
.ui-table-reflow th .ui-table-cell-label { 
	display: none;
}
/* Mobile first styles: Begin with the stacked presentation at narrow widths */ 
@media only all {
	/* Hide the table headers */ 
	.ui-table-reflow thead td, 
	.ui-table-reflow thead th {
		display: none;
	}
	/* Show the table cells as a block level element */ 
	.ui-table-reflow td,
	.ui-table-reflow th { 
		text-align: left;
		display: block;
	}
	/* Add a fair amount of top margin to visually separate each row when stacked */  
	.ui-table-reflow tbody th {
		margin-top: 3em;
	}
	/* Make the label elements a percentage width */ 
	.ui-table-reflow td .ui-table-cell-label,
	.ui-table-reflow th .ui-table-cell-label { 
		padding: .4em; 
		min-width: 30%; 
		display: inline-block;
		margin: -.4em 1em -.4em -.4em;
	}
	/* For grouped headers, have a different style to visually separate the levels by classing the first label in each col group */ 
	.ui-table-reflow th .ui-table-cell-label-top,
	.ui-table-reflow td .ui-table-cell-label-top {
		display: block;
		padding: .4em 0;
		margin: .4em 0;
		text-transform: uppercase;
		font-size: .9em;
		font-weight: normal;
	}
}
/* Breakpoint to show as a standard table at 560px (35em x 16px) or wider */ 
@media ( min-width: 35em ) {
	/* Show the table header rows */ 
	.ui-table-reflow.ui-responsive td,
	.ui-table-reflow.ui-responsive th,
	.ui-table-reflow.ui-responsive tbody th,
	.ui-table-reflow.ui-responsive tbody td,
	.ui-table-reflow.ui-responsive thead td,
	.ui-table-reflow.ui-responsive thead th {
		display: table-cell;
		margin: 0;
	}
	/* Hide the labels in each cell */ 
	.ui-table-reflow.ui-responsive td .ui-table-cell-label,
	.ui-table-reflow.ui-responsive th .ui-table-cell-label { 
		display: none;
	}
}
/* Hack to make IE9 and WP7.5 treat cells like block level elements, scoped to ui-responsive class */ 
/* Applied in a max-width media query up to the table layout breakpoint so we don't need to negate this*/ 
@media ( max-width: 35em ) {
	.ui-table-reflow.ui-responsive td,
	.ui-table-reflow.ui-responsive th {
		width: 100%;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		float: left;
		clear: left;
	}
}
/* Panel */
.ui-panel {
	width: 17em;
	min-height: 100%;
	max-height: none;
	border-width: 0;
	position: absolute;
	top: 0;
	display: block;
}
.ui-panel-closed {
	width: 0;
	max-height: 100%;
	overflow: hidden;
	visibility: hidden;
	left: 0;
	clip: rect(1px,1px,1px,1px);
}
.ui-panel-fixed {
	position: fixed;
	bottom: -1px; /* Fixes gap on Chrome for Android */
	padding-bottom: 1px;
}
.ui-panel-display-reveal {
	z-index: 1;
}
.ui-panel-display-push {
	z-index: 999;
}
.ui-panel-display-overlay {
	z-index: 1001; /* Fixed toolbars have z-index 1000 */
}
.ui-panel-inner {
	padding: 1em;
}
/* Container, page and wrapper */
.ui-panel-page-container {
	overflow-x: visible;
}
.ui-panel-page-container-themed .ui-page-active {
	background: none;
}
.ui-panel-wrapper {
	position: relative;
	min-height: inherit;
	border: 0;
	overflow-x: hidden;
	z-index: 999;
}
/* Fixed toolbars */
.ui-panel-fixed-toolbar {
	overflow-x: hidden;
}
/* Dismiss */
.ui-panel-dismiss {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 100%;
	z-index: 1002;
	display: none;
}
.ui-panel-dismiss-open {
	display: block;
}
/* Animate class is added to panel, wrapper and fixed toolbars */
.ui-panel-animate {
	-webkit-transition: -webkit-transform 300ms ease;
	-webkit-transition-duration: 300ms;
	-moz-transition: -moz-transform 300ms ease;
	transition: transform 300ms ease;
}
/* Fix for Windows Phone issue #6349: unset the transition for transforms in case of fixed toolbars. */
@media screen and ( max-device-width: 768px ) {
	.ui-page-header-fixed .ui-panel-animate.ui-panel-wrapper,
	.ui-page-footer-fixed .ui-panel-animate.ui-panel-wrapper,
	.ui-panel-animate.ui-panel-fixed-toolbar {
		-ms-transition: none;
	}
	/* We need a transitionend event ... */
	.ui-panel-animate.ui-panel-fixed-toolbar {
		-ms-transition: -ms-transform 1ms;
		-ms-transform: rotate(0deg);
	}
}
/* Hardware acceleration for smoother transitions on WebKit browsers */
.ui-panel-animate.ui-panel:not(.ui-panel-display-reveal) {
	-webkit-backface-visibility: hidden;
	-webkit-transform: translate3d(0,0,0);
}
/* Panel positioning (for overlay and push) */
/* Panel left closed */
.ui-panel-position-left {
	left: -17em;
}
/* Panel left closed animated */
.ui-panel-animate.ui-panel-position-left.ui-panel-display-overlay,
.ui-panel-animate.ui-panel-position-left.ui-panel-display-push {
	left: 0;
	-webkit-transform: translate3d(-17em,0,0);
	-moz-transform: translate3d(-17em,0,0);
	transform: translate3d(-17em,0,0);
}
/* Panel left open */
.ui-panel-position-left.ui-panel-display-reveal, /* Unset "panel left closed" for reveal */
.ui-panel-open.ui-panel-position-left {
	left: 0;
}
/* Panel left open animated */
.ui-panel-animate.ui-panel-open.ui-panel-position-left.ui-panel-display-overlay,
.ui-panel-animate.ui-panel-open.ui-panel-position-left.ui-panel-display-push {
	-webkit-transform: translate3d(0,0,0);
	transform: translate3d(0,0,0);
	-moz-transform: none;
}
/* Panel right closed */
.ui-panel-position-right {
	right: -17em;
}
/* Panel right closed animated */
.ui-panel-animate.ui-panel-position-right.ui-panel-display-overlay,
.ui-panel-animate.ui-panel-position-right.ui-panel-display-push {
	right: 0;
	-webkit-transform: translate3d(17em,0,0);
	-moz-transform: translate3d(17em,0,0);
	transform: translate3d(17em,0,0);
}
/* Panel right open */
.ui-panel-position-right.ui-panel-display-reveal, /* Unset "panel right closed" for reveal */
.ui-panel-position-right.ui-panel-open {
	right: 0;
}
/* Panel right open animated */
.ui-panel-animate.ui-panel-open.ui-panel-position-right.ui-panel-display-overlay,
.ui-panel-animate.ui-panel-open.ui-panel-position-right.ui-panel-display-push {
	-webkit-transform: translate3d(0,0,0);
	transform: translate3d(0,0,0);
	-moz-transform: none;
}
/* Wrapper and fixed toolbars positioning (for reveal and push) */
/* Panel left open */
.ui-panel-page-content-position-left {
	left: 17em;
	right: -17em;
}
/* Panel left open animated */
.ui-panel-animate.ui-panel-page-content-position-left {
	left: 0;
	right: 0;
	-webkit-transform: translate3d(17em,0,0);
	-moz-transform: translate3d(17em,0,0);
	transform: translate3d(17em,0,0);
}
/* Panel right open */
.ui-panel-page-content-position-right {
	left: -17em;
	right: 17em;
}
/* Panel right open animated */
.ui-panel-animate.ui-panel-page-content-position-right {
	left: 0;
	right: 0;
	-webkit-transform: translate3d(-17em,0,0);
	-moz-transform: translate3d(-17em,0,0);
	transform: translate3d(-17em,0,0);
}
/* Dismiss model open */
.ui-panel-dismiss-open.ui-panel-dismiss-position-left {
	left: 17em;
}
.ui-panel-dismiss-open.ui-panel-dismiss-position-right {
	right: 17em;
}
/* Shadows and borders */
.ui-panel-display-reveal {
	-webkit-box-shadow: inset -5px 0 5px rgba(0,0,0,.15);
	-moz-box-shadow: inset -5px 0 5px rgba(0,0,0,.15);
	box-shadow: inset -5px 0 5px rgba(0,0,0,.15);
}
.ui-panel-position-right.ui-panel-display-reveal {
	-webkit-box-shadow: inset 5px 0 5px rgba(0,0,0,.15);
	-moz-box-shadow: inset 5px 0 5px rgba(0,0,0,.15);
	box-shadow: inset 5px 0 5px rgba(0,0,0,.15);
}
.ui-panel-display-overlay {
	-webkit-box-shadow: 5px 0 5px rgba(0,0,0,.15);
	-moz-box-shadow: 5px 0 5px rgba(0,0,0,.15);
	box-shadow: 5px 0 5px rgba(0,0,0,.15);
}
.ui-panel-position-right.ui-panel-display-overlay {
	-webkit-box-shadow: -5px 0 5px rgba(0,0,0,.15);
	-moz-box-shadow: -5px 0 5px rgba(0,0,0,.15);
	box-shadow: -5px 0 5px rgba(0,0,0,.15);
}
.ui-panel-open.ui-panel-position-left.ui-panel-display-push {
	border-right-width: 1px;
	margin-right: -1px;
}
.ui-panel-page-content-position-left.ui-panel-page-content-display-push {
	margin-left: 1px;
	width: auto;
}
.ui-panel-open.ui-panel-position-right.ui-panel-display-push {
	border-left-width: 1px;
	margin-left: -1px;
}
.ui-panel-page-content-position-right.ui-panel-page-content-display-push {
	margin-right: 1px;
	width: auto;
}
/* Responsive: wrap on wide viewports once open */
@media (min-width:55em) {
	.ui-responsive-panel .ui-panel-page-content-open.ui-panel-page-content-position-left {
		margin-right: 17em;
	}
	.ui-responsive-panel .ui-panel-page-content-open.ui-panel-page-content-position-right {
		margin-left: 17em;
	}
	.ui-responsive-panel .ui-panel-page-content-open {
		width: auto;	
	}
	.ui-responsive-panel .ui-panel-dismiss-display-push,
	.ui-responsive-panel.ui-page-active ~ .ui-panel-dismiss-display-push {
		display: none;
	}
}
.ui-tabs {
	position: relative;/* position: relative prevents IE scroll bug (element with position: relative inside container with overflow: auto appear as "fixed") */
	padding: .2em;
}
