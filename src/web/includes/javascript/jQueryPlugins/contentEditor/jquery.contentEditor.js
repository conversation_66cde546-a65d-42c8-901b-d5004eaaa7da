/** contentEditor r1 // 2011.06.29 // jQuery 1.4 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * 
 */

(function($) {
	$.fn.changeContentGroup = function(groupId) {
		var _this = $.contentEditor.get(this);

		var targetContentGroup 				= $(_this.content.bindingContainer).find(".contentGroup[groupId='"+groupId+"']");
		_this.data.currentContentType		= $(targetContentGroup).attr("content");
		_this.data.currentContentGroupId 	= groupId;

		if ( _this.data.currentContentType == "text" ) {
			_this.setTextContent( _this.getCurrentEditorBinding().val() );
		} else if ( _this.data.currentContentType == "graphic" ) {
			$(_this.display.graphicUpdateContainer).find("[class*='contentEditor_GraphicInputsContainer']").hide();
			_this.getCurrentGraphicInputsContainer().show();
			_this.initImageSelectMenu( _this.getCurrentGraphicInputsContainer().find("[id^='conLibSelect_']") );
			_this.initLocalImageSelectMenu( _this.getCurrentGraphicInputsContainer().find("[id^='localConLibSelect_']") );
			_this.setGraphicContent( _this.getCurrentGraphicFileName(), 
									_this.getCurrentGraphicFilePath(), 
									_this.getCurrentGraphicUploadDate(), 
									_this.getCurrentGraphicAppliedImageName() );
			_this.setCmsAssetInfo(_this.getCurrentGraphicCmsLastUpdateDate(), _this.getCurrentGraphicCmsLastSyncDate());
			// IMAGE LINK
			$(_this.display.graphicImageLinkContainer).find("[class*='contentEditor_GraphicImageLinkContainer']").hide();
			_this.getCurrentGraphicImageLinkContainer().show();
			_this.setGraphicImageLink(_this.getCurrentGraphicImageLink());
			// IMAGE ALT TEXT
			$(_this.display.graphicImageAltTextContainer).find("[class*='contentEditor_GraphicImageAltTextContainer']").hide();
			if($('.graphicImageLink').length > 0){
				$('.contentEditor_GraphicImageAltTextContainer').hide();
			}
			_this.getCurrentGraphicImageAltTextContainer().show();
			_this.setGraphicImageAltText(_this.getCurrentGraphicImageAltText());
			// IMAGE EXTERNAL LINK
			$(_this.display.graphicImageExtLinkContainer).find("[class*='contentEditor_GraphicImageExtLinkContainer']").hide();
			if($('.graphicImageAltText').length > 0){
				$('.contentEditor_GraphicImageExtLinkContainer').hide();
			}
			_this.getCurrentGraphicImageExtLinkContainer().show();
			_this.setGraphicImageExtLink(_this.getCurrentGraphicImageExtLink());
			// IMAGE EXTERNAL PATH
			$(_this.display.graphicImageExtPathContainer).find("[class*='contentEditor_GraphicImageExtPathContainer']").hide();
			if($('.graphicImageAltText').length > 0){
				$('.contentEditor_GraphicImageExtPathContainer').hide();
			}
			_this.getCurrentGraphicImageExtPathContainer().show();
			_this.setGraphicImageExtPath(_this.getCurrentGraphicImageExtPath());
		}
		_this.setState( $(_this.content.bindingContainer).attr('globalState'), 
						$(targetContentGroup).attr('groupState'),
						_this.getCurrentContentEntry().attr('contentState') );
		
		_this.clearEditorUndoManager();

		_this.updateDefaultLanguagePopOutVisibility();
		_this.updateContentInfo();

		return this;
	};
	
	$.fn.clearContent = function() {
		var _this = $.contentEditor.get(this);
		
		$(_this.content.bindingContainer).find('.contentEntry').each(function(){
			$(this).find('textarea').val('');
		});
		$(_this.display.graphicUpdateContainer).find("[class*='contentEditor_GraphicInputsContainer']").each( function(){
			$(this).find('.graphicFileInput').attr('imgPath','');
			$(this).find('.graphicFileInput').attr('imgName','');
			$(this).find('.graphicFileInput').attr('uploadDate','');
			$(this).find('.graphicAppliedImageName input').val('');
			$(this).find('.cmsAssetInfo').attr('cmsLastUpdateDate','');
			$(this).find('.cmsAssetInfo').attr('cmsLastSyncDate','');
		});
		
		// IMAGE LINK
		$(_this.display.graphicImageLinkContainer).find("[class*='contentEditor_GraphicImageLinkContainer']").each( function(){
			$(this).find('.graphicImageLink .imageLinkInput').val('');
		});
		
		// IMAGE ALT TEXT
		$(_this.display.graphicImageAltTextContainer).find("[class*='contentEditor_GraphicImageAltTextContainer']").each( function(){
			$(this).find('.graphicImageAltText .imageAltTextInput').val('');
		});

		// IMAGE EXTERNAL LINK
		$(_this.display.graphicImageExtLinkContainer).find("[class*='contentEditor_GraphicImageExtLinkContainer']").each( function(){
			$(this).find('.graphicImageExtLink .imageExtLinkInput').val('');
		});

		// IMAGE EXTERNAL PATH
		$(_this.display.graphicImageExtPathContainer).find("[class*='contentEditor_GraphicImageExtPathContainer']").each( function(){
			$(this).find('.graphicImageExtPath .imageExtPathInput').val('');
		});

		if ( _this.data.currentContentType == "text" ){
			_this.setTextContent("");
		}else if ( _this.data.currentContentType == "graphic" ){
			_this.setGraphicContent("","","","");
			_this.setCmsAssetInfo("","");
			_this.setGraphicImageLink("");
			_this.setGraphicImageAltText("");
			_this.setGraphicImageExtLink("");
			_this.setGraphicImageExtPath("");
		}
		return this;
	};
	
	$.fn.clearTextContent = function() {
		var _this = $.contentEditor.get(this);
		
		$(_this.content.bindingContainer).find('.contentEntry').each(function(){
			$(this).find('textarea').val('');
		});
		
		if ( _this.data.currentContentType == "text" )
			_this.setTextContent("");
		
		return this;
	};

	$.fn.contentEditorCopyFrom = function() {
		var _this = $.contentEditor.get(this);
		
		var copyFromSelect = $('#contentEditor_CopyFromSelect_'+_this.data.instId);
		var selectionId	= $(copyFromSelect).find(':selected').val();
		_this.requestSelectionContent(selectionId, 'copyFrom');
		
		return this;
	};
	
	$.fn.resize = function() {
		var _this = $.contentEditor.get(this);
		_this.resizeContentArea();
		return this;
	};

	$.fn.restoreDefaultContent = function(selectionId) {
		var _this = $.contentEditor.get(this);
		
		_this.requestSelectionContent(selectionId, 'default');
		
		return this;
	};
	
	$.fn.setCurrentGroupStateToEdit = function() {
		var _this = $.contentEditor.get(this);
		
		var currentContentGroup = $(_this.content.bindingContainer).find(".contentGroup[groupId='"+_this.data.currentContentGroupId+"']");
		$(currentContentGroup).attr('groupState','edit');
		
		_this.setState('edit','edit',_this.getCurrentContentEntry().attr('contentState'));
		
		return this;
	};
	
	$.fn.clearContentForCurrentGroup = function() {
		var _this = $.contentEditor.get(this);
		if ( _this.data.currentContentType == "text" ) {
			_this.getCurrentGroup().find('.contentEntry').each(function(){
				$(this).find('textarea').val('');
			});
			_this.setTextContent("");
		} else if ( _this.data.currentContentType == "graphic" ) {
			$(_this.display.graphicUpdateContainer).find("[class*='contentEditor_GraphicInputsContainer_"+_this.data.currentContentGroupId+"_']").each( function(){
				$(this).find('.graphicFileInput').attr('imgPath','');
				$(this).find('.graphicFileInput').attr('imgName','');
				$(this).find('.graphicFileInput').attr('uploadDate','');
				$(this).find('.graphicAppliedImageName input').val('');
				$(this).find('.cmsAssetInfo').attr('cmsLastUpdateDate','');
				$(this).find('.cmsAssetInfo').attr('cmsLastSyncDate','');
			});
			$(_this.display.graphicUpdateContainer).find("[class*='contentEditor_GraphicInputsContainer_"+_this.data.currentContentGroupId+"_'] select[id^='conLibSelect_']").selectOption(0);
			_this.setGraphicContent("","","","");
			_this.setCmsAssetInfo("","");
			// IMAGE LINK
			$(_this.display.graphicImageLinkContainer).find("[class*='contentEditor_GraphicImageLinkContainer_"+_this.data.currentContentGroupId+"_']").each( function(){
				$(this).find('.graphicImageLink .imageLinkInput').val('');
			});
			_this.setGraphicImageLink("");
			// IMAGE ALT TEXT
			$(_this.display.graphicImageAltTextContainer).find("[class*='contentEditor_GraphicImageAltTextContainer_"+_this.data.currentContentGroupId+"_']").each( function(){
				$(this).find('.graphicImageAltText .imageAltTextInput').val('');
			});
			_this.setGraphicImageAltText("");
			// IMAGE EXTERNAL LINK
			$(_this.display.graphicImageExtLinkContainer).find("[class*='contentEditor_GraphicImageExtLinkContainer_"+_this.data.currentContentGroupId+"_']").each( function(){
				$(this).find('.graphicImageExtLink .imageExtLinkInput').val('');
			});
			_this.setGraphicImageExtLink("");
			// IMAGE EXTERNAL PATH
			$(_this.display.graphicImageExtPathContainer).find("[class*='contentEditor_GraphicImageExtPathContainer_"+_this.data.currentContentGroupId+"_']").each( function(){
				$(this).find('.graphicImageExtPath .imageExtPathInput').val('');
			});
			_this.setGraphicImageExtPath("");
		}
		
		return this;
	};
	
	$.fn.setCurrentGroupStateToLeaveEmpty = function() {
		var _this = $.contentEditor.get(this);
		
		var currentContentGroup = $(_this.content.bindingContainer).find(".contentGroup[groupId='"+_this.data.currentContentGroupId+"']");
		$(currentContentGroup).attr('groupState','leaveEmpty');
		
		_this.setState('edit', 'leaveEmpty');
		
		return this;
	};
	
	$.fn.setStateToSameAs = function(selectionId, selectionName) {
		var _this = $.contentEditor.get(this);
		
		$(_this.content.bindingContainer).attr('globalState','sameAs');
		$(_this.content.bindingContainer).attr('globalRefObjLabel',selectionName);
		$(_this.content.bindingContainer).find('.contentGroup').each(function(){
			$(this).attr('groupState','inherit');
		});
		
		_this.requestSelectionContent(selectionId, 'sameAs');
		
		return this;
	};
	
	$.fn.setStateToSuppress = function() {
		var _this = $.contentEditor.get(this);

		$(_this.content.bindingContainer).attr('globalState','suppress');
		$(_this.content.bindingContainer).attr('globalRefObjLabel','');
		$(_this.content.bindingContainer).find('.contentGroup').each(function(){
			$(this).attr('groupState','inherit');
		});
		
		_this.setState('suppress');
		
		return this;
	};
	
	$.fn.setStateToEdit = function() {
		var _this = $.contentEditor.get(this);

		$(_this.content.bindingContainer).attr('globalState','edit');
		$(_this.content.bindingContainer).attr('globalRefObjLabel','');
		$(_this.content.bindingContainer).find('.contentGroup').each(function(){
			$(this).attr('groupState','edit');
		});

		_this.setState('edit',
						_this.getCurrentGroup().attr('groupState'),
						_this.getCurrentContentEntry().attr('contentState'));

		_this.setCurrentSameAsDefaultInput();
		_this.setCurrentSameAsParentInput();

		return this;
	};
		
	$.contentEditor = {
		get	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return contentEditor_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			charCountEnabled	: false,
			defaultLocaleId 	: '1',
			defaultLanguage		: client_messages.language.english_us,
			focusLocaleId		: '1',
			globalContentObject	: false,
			applies_freeform	: false,
			library_only		: false,
			editorActions		: {	
										'clear' 	: 	false, 
										'copyFrom' 	: 	false
									},
			languages 			: { 
									'1' : client_messages.language.english
								},
			onload			:	function() { },
			size			:	'default',
			statusViewId	:	2,
			text			:	{ 
									appliedImageName	:	client_messages.content_editor.applied_image_name,
									charCount			:	client_messages.content_editor.character_count,
									clear 				: 	client_messages.content_editor.clear,
									click				:	client_messages.content_editor.click,
									clickToDownload		:	client_messages.content_editor.click_to_download,
									copyFrom			:	client_messages.content_editor.copy_from,
									copy				:	client_messages.content_editor.copy,
									currentImage		:	client_messages.content_editor.content_file,
									edit				:	client_messages.content_editor.edit,
									imageLibrary		:	client_messages.content_editor.image_library,
									imageLink			: 	client_messages.content_editor.image_link_for_this_language,
									imageAltText		: 	client_messages.content_editor.image_alt_text_for_this_language,
									imageExtLink		: 	client_messages.content_editor.image_ext_link_for_this_language,
									imageExtPath		: 	client_messages.content_editor.image_ext_path_for_this_language,
									leaveEmpty			:	client_messages.content_editor.leave_empty,
									localImageLibrary	:	client_messages.content_editor.local_image_library,
									none				:	client_messages.content_editor.none,
									noImage				:	client_messages.content_editor.no_image,
									noState				:	client_messages.content_editor.select_content_state,
									refImageLibrary		:	client_messages.content_editor.referencing_image_library,
									refLocalImageLibrary:	client_messages.content_editor.referencing_local_image_library,
									sameAsDefault		:	client_messages.content_editor.use_same_as_default,
									sameAsSystemDefault	:	client_messages.content_editor.use_same_as_system_default,
									sameAsParent		:	client_messages.content_editor.inherit_from_parent,
									sameAsSelection		:	client_messages.content_editor.same_as_variant,
									shared				:	client_messages.content_editor.shared_content_reference,
									toEditContentFile 	: 	client_messages.content_editor.to_edit_content_file,
									noContent			:	client_messages.content_editor.no_content,
									noImageUploaded		:	client_messages.content_editor.no_image_selected,
									suppressed			:	client_messages.content_editor.content_suppressed,
									unknown				:	client_messages.content_editor.unknown,
									uploadImageDate		:	client_messages.content_editor.upload_date,
									uploadImage			:	client_messages.content_editor.new_image,
									uploadedImage		:	client_messages.content_editor.uploaded,
									useConLib			:   client_messages.content_editor.use_content_library,
									cmsLastUpdateDate 	:	client_messages.content_editor.repo_last_update_date,
									cmsLastSyncDate 	:	client_messages.content_editor.repo_last_sync_date
								},
			indicator 			: {
									primary_joiner 	: "--",
									sub_joiner 		: "-",
									color_joiner 	: ";"
								},
			topPadding		:	0,
			usage			:	'view',
			appliesTemplates:	false,
			enabledFileEdit	:	false,
			onInitComplete	:	null,
			showImageAltText: 	true,
			showImageExtLink:   true,
			showImageExtPath:	true,
			isTestContext	:   false,
			nTk				:   null,
			uTk				:   null
		}
	};
	
	$.fn.contentEditor = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new contentEditor_component().init(this, conf);
		});
	};
	
	function contentEditor_component () {
		return {
			
		data : $.extend({},$.contentEditor.defaults),	

		init : function(elem, conf) {
			var _this = this;

			contentEditor_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(false, {}, this.data, conf);
			
			_this.content 						= {};
			_this.content.bindingContainer 		= $(elem);
			
			_this.data.instId 					= $(_this.content.bindingContainer).attr("id");
			_this.data.currentLocaleId 			= _this.data.focusLocaleId != null ? _this.data.focusLocaleId : "UNUSED";
			_this.data.currentContentGroupId 	= $(_this.content.bindingContainer).find('.contentGroup:first').attr('groupId');
			_this.data.currentContentType		= $(_this.content.bindingContainer).find('.contentGroup:first').attr("content");
			_this.data.fullscreen 				= {
													is_fullscreen : false
													};
			_this.data.source_editor 			= {
													is_displayed : false
													};
			_this.data.encoded_content_editor 	= {
													is_displayed : false
													};
            _this.data.isDefaultLanguangePersisted = false;
			_this.data.wasDefaultLanguangePersisted = false;
			_this.data.languageSnapOutPanel 	= null;
			_this.display 						= {};
			
			_this.styles.data = _this;
			_this.styles.initStyleData();
			
			_this.data.graphicInitComplete = false;
			_this.data.contentLanguageKey = 'content-language';

			// CONTAINER
			// Init Display: Container
			var displayContainer = '<div class="contentEditor_DisplayContainer">';

			if (!_.isUndefined(_this.display.container) && _this.display.container !== null) {
				return;
			}

            _this.display.container = $(displayContainer);
            $(_this.content.bindingContainer).before($(_this.display.container));
			
			// INIT LANGUAGE TABS
			_this.initLanguageSelect();

			_this.updateContentInfo();
			
			// CONTENT CONTAINER
			// Init Display: Container	
			
			var innerContainerHeight = (_this.data.usage == "edit" ? '310px' : '200px');
			if ( _this.data.size == "small" )
				innerContainerHeight = '150px';
			else if ( _this.data.size == "medium" )
				innerContainerHeight = '275px';
			else if ( _this.data.size == "large" )
				innerContainerHeight = '375px';
			else
				innerContainerHeight = _this.data.size;
			if ( _this.data.editorInitData != undefined )
				innerContainerHeight = (parseInt(_this.data.editorInitData.height)-25) + "px";
			
			var contentContainer = 	'<div class="contentEditor_OuterContentContainer">' +
										'<div class="contentEditor_LoadingIndicatorContainer" align="center" style="display:none; height: '+innerContainerHeight+'">' +
											'<img src="'+_this.data.contextPath+'/includes/themes/commonimages/tree/loadingAnimation.gif" style="margin: 40px;" />' +
										'</div>' +
										'<div class="contentEditor_InnerContentContainer ' + (_this.data.usage == "edit" ? '' : 'contentEditor_ContainerBorder') + '" ' + 'style="height: 100%; min-height: ' + innerContainerHeight + '">' +
										'</div>' +
									'</div>';
			$(_this.display.container).append(contentContainer);

			// SAME AS DEFAULT CONTAINER
			// Condition: Is same as default content required?
			var sameAsDefaultInputExists = false;
			if ( $(_this.content.bindingContainer).find('.sameAsDefaultInput').length > 0 )
				sameAsDefaultInputExists = true;
			
			if ( sameAsDefaultInputExists ) {
				// Init Same as Default Container
				var sameAsDefaultContainer;

				if (_this.data.usage == "view") {
					sameAsDefaultContainer =  '<div class="contentEditor_SameAsParentContainer" style="display: none;"></div>';
					_this.display.sameAsDefaultContainer = $(sameAsDefaultContainer);
					$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.sameAsDefaultContainer) );
				} else {
					sameAsDefaultContainer = $('#same-as-default-language');
					_this.display.sameAsDefaultContainer = $(sameAsDefaultContainer);
					_this.display.sameAsDefaultContainer.hide();

					var sameAsDefaultBarContainer =  '<div class="contentEditor_SameAsDefaultBarContainer" style="display: none;"></div>';
					_this.display.sameAsDefaultBarContainer = $(sameAsDefaultBarContainer);
					$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.sameAsDefaultBarContainer) );
				}

				$(_this.content.bindingContainer).find('.contentGroup').each( function() {
					$(this).find('.contentEntry').each( function() {
						if ( $(this).find('.sameAsDefaultInput').length > 0 ) {
							var currentGroupId 	= $(this).parent().attr('groupId');
							var currentLocaleId = $(this).attr('localeId');
							var contentLocaleId = _this.getContentLanguage();

							var $input = $(this).find('.sameAsDefaultInput');
							var currentSameAsDefaultInputsContainer = _this.getSameAsDefaultInputs(currentGroupId, currentLocaleId, contentLocaleId, $input.attr("id"));
							var currentSameAsDefaultBarContainer = _this.getSameAsDefaultBar(currentGroupId, currentLocaleId, contentLocaleId, $input.attr("id"));

							$(currentSameAsDefaultInputsContainer).find('.sameAsDefaultInputSpan').prepend($input);
	
							if ( _this.data.usage == "view" ){
								$(currentSameAsDefaultInputsContainer).find('.sameAsDefaultInputSpan').parent().hide();
							} else {
								_this.display.sameAsDefaultBarContainer.append( $(currentSameAsDefaultBarContainer) );
							}

							$(currentSameAsDefaultInputsContainer).find('.sameAsDefaultInputSpan input')
								.click( function(){
									_this.toggleSameAsDefault(this);
								});
							
							$(_this.display.sameAsDefaultContainer).append( $(currentSameAsDefaultInputsContainer) );
						}
					});
				});
			}
			
			// SAME AS PARENT CONTAINER
			// Condition: Is same as parent required?
			var sameAsParentInputExists = false;
			if ( $(_this.content.bindingContainer).find('.sameAsParentInput').length > 0 )
				sameAsParentInputExists = true;
			
			if ( sameAsParentInputExists ) {
				// Init Same As Parent Container
				var sameAsParentContainer;

				if (_this.data.usage == "view") {
					sameAsParentContainer =  '<div class="contentEditor_SameAsParentContainer" style="display: none;"></div>';
					_this.display.sameAsParentContainer = $(sameAsParentContainer);
					$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.sameAsParentContainer) );
				} else {
					sameAsParentContainer = $('#same-as-parent-container');
					_this.display.sameAsParentContainer = $(sameAsParentContainer);
					_this.display.sameAsParentContainer.hide();
				}

				$(_this.content.bindingContainer).find('.contentGroup').each( function() {
					$(this).find('.contentEntry').each( function() {
						if ( $(this).find('.sameAsParentInput').length > 0 ) {
							var currentGroupId 	= $(this).parent().attr('groupId');
							var currentLocaleId = $(this).attr('localeId');
							
							var $input = $(this).find('.sameAsParentInput');
							var currentSameAsParentInputsContainer = _this.getSameAsParentInputs(currentGroupId, currentLocaleId, $input.attr("id"));

							$(currentSameAsParentInputsContainer).find('.sameAsParentInputSpan').prepend($input);

							if ( _this.data.usage == "view" ){
								$(currentSameAsParentInputsContainer).find('.sameAsParentInputSpan').parent().hide();
							}

							$(currentSameAsParentInputsContainer).find('.sameAsParentInputSpan input')
								.click( function(){
									if ( _this.data.inheritOrCustomAll) {
										_this.toggleAllSameAsParent(this);
									} else {
										_this.toggleSameAsParent(this);
									}
								});
							
							$(_this.display.sameAsParentContainer).append( $(currentSameAsParentInputsContainer) );
						}
					});
				});
				
				if ( (_this.getCurrentGroup().attr('groupState') == 'edit' || $(_this.content.bindingContainer).attr('globalState') == 'edit') )
					$(_this.display.sameAsParentContainer).find('.contentEditor_SameAsParentInputContainer_'+_this.data.currentContentGroupId+'_'+_this.data.currentLocaleId).show();

			}

			// SAME AS CONTAINER
			// Init Display: Container
			var sameAsContainer =	'<div class="contentEditor_SameAsContainer" style="display: none;">' +
										_this.data.text.sameAsSelection + ':' +
										'&nbsp;&nbsp;<span class="contentEditor_SameAsLabel"></span>' +
										'<span class="contentEditor_SameAsNoContent" style="display: none">' +
											'&nbsp;&nbsp;[' + _this.data.text.noContent + ']' +
										'</span>' +
									'</div>';
			_this.display.sameAsContainer = $(sameAsContainer);
			$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.sameAsContainer) );

			// EDITOR
			// Condition: Is text editing required?
			var textContentExists = false;
			$(_this.content.bindingContainer).find('.contentGroup').each( function() {
				if ($(this).attr('content') == 'text')
					textContentExists = true;
			});
			
			if ( textContentExists && _this.data.usage == "edit" ) {
				
				// Init Editor Container
				var editorContainer = 	'<div class="contentEditor_EditorContainer" style="display: none;">' +
											'<textarea id="contentEditor_EditorTextarea_'+_this.data.instId+'"></textarea>' +
										'</div>';
				_this.display.editorContainer 	= $(editorContainer);
				$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.editorContainer) );

				// Init Editor Actions

				// Copy from
				if ( _this.data.copyFromData != undefined ) {
					var $copyFromContainer = $('#copy-from-content');
					var editorActionBar = '<div>' +
						'<div id="copyFromHeading" class="mb-3 pb-2 border-bottom font-weight-bold">' + _this.data.text.copyFrom + '</div>' +
						'<div class="d-flex align-items-center">' +
						'<div>' +
						'<select id="contentEditor_CopyFromSelect_' + _this.data.instId + '" aria-labelledby="copyFromHeading" class="style_select inputL" style="display: none">' +
						_this.selectDataJSONtoHTML(_this.data.copyFromData) +
						'</select>' +
						'</div>' +
						'<div class="pl-2">' +
						'<input title="' + _this.data.text.copy + '" type="button" ' +
						'id="contentEditor_CopyFromBtn_' + _this.data.instId + '" ' +
						'onclick="$(\'#' + _this.data.instId + '\').contentEditorCopyFrom()" style="display: none;" />' +
						'</div>' +
						'</div>' +
						'</div>';

					if (_this.data.editorActions.copyFrom && _this.data.copyFromData.groups) {
						$copyFromContainer.prepend(editorActionBar);
						$copyFromContainer.find('input:button').styleActionElement();
						$copyFromContainer.find('select').styleActionElement({maxItemDisplay: 10, maxCurrentOptionLength: 25});

						$('select#contentEditor_CopyFromSelect_' + _this.data.instId + ' option').each(function () {
							var instId = $(this).val();
							$('#menuOption_' + $(this).attr('id')).contentPopup({
								trigger: "hover",
								contentType: 'contentObject',
								contentItemId: _this.data.contentObjectId,
								popupLocation: 'right',
								fnBeforeContentRequest: function (o) {
									o.data.contentLocaleId = $.contentEditor.get('contentData').data.currentLocaleId;
								},
								fnExtServerParams: function (o) {
									var obj = [
										{"name": "touchpointSelectionId", "value": instId},
										{"name": "selectionStatusId", "value": "1"}
									];
									return obj;
								}
							});
						});
					} else {
						$copyFromContainer.hide();
					}

				} // end: Copy from

				
				// Init Editor
				_this.display.editorContent = $(_this.display.editorContainer).find('#contentEditor_EditorTextarea_'+_this.data.instId);
				if ( _this.data.currentContentType == "text" )
					$(_this.display.editorContent).val( _this.getCurrentEditorBinding().val() ).trigger('change');  // Set content
				
				// Spellcheck language init
				var spellCheckLangs = '';
				for (index in _this.data.languages)
					for (localeId in _this.data.languages[index])
						spellCheckLangs += _this.data.languages[index][localeId] + "=" + localeId + ',';

				if (spellCheckLangs.length > 0) {
					spellCheckLangs = spellCheckLangs.substring(0, spellCheckLangs.length - 1);
				}
				if (_this.data.editorInitData === undefined) {
					_this.data.editorInitData = {};
				}
				_this.data.editorInitData.spellchecker_languages = spellCheckLangs;
				_this.data.editorInitData.spellchecker_language = _this.data.currentLocaleId;

				if ( _this.data.library_only ) {
					_this.data.editorInitData.toolbar1 			= "reset";
					_this.data.editorInitData.toolbar2 			= _this.data.editorInitData.toolbar3 = "";
					_this.data.editorInitData.singleLibraryItem = true;
					_this.data.editorInitData.statusbar			= false;
					_this.data.editorInitData.plugins			= _this.data.editorInitData.plugins + 
																	(_this.data.editorInitData.plugins.length > 0 ? "," : "") +  
																	"mp_readonly";
					_this.data.editorInitData.menubar 			= 'content',
					_this.data.editorInitData.menu 				= { content: {title: 'Content', items: 'insertembeddedcontent' } }
				}

				$(_this.display.editorContent).tinymce(_this.data.editorInitData);

				window.setInterval( function(){ _this.pollEditorForContentChange(); }, 250 );
				window.setInterval( function(){ _this.pollEditorForContextData(); }, 1000 );
				window.setTimeout( function(){ _this.pollEditorForDisplayInit(); }, 250);
				
			} // End If: textContentExists

			// GRAPHIC CONTAINER
			// Condition: Is graphic content required?
			var graphicContentExists = false;
			$(_this.content.bindingContainer).find('.contentGroup').each( function() {
				if ($(this).attr('content') == "graphic")
					graphicContentExists = true;
			});
			
			if ( graphicContentExists ) {

				// Init Graphic Container
				var graphicUpdateContainer = 	'<div class="contentEditor_GraphicUpdateContainer" style="display: none;"></div>';	
				_this.display.graphicUpdateContainer = $(graphicUpdateContainer);
				$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.graphicUpdateContainer) );
				
				// Image Library: Is applied?
				_this.imageLibraryApplied = false;
				if ( $(_this.content.bindingContainer).find('.useContentLibraryToggle').length > 0 )
					_this.imageLibraryApplied = true;
				
				// Local Image Library: Is applied?
				_this.localImageLibraryApplied = false;
				if ( $(_this.content.bindingContainer).find('.useLocalContentLibraryToggle').length > 0 )
					_this.localImageLibraryApplied = true;
				
				if ( _this.data.usage == "view" ) {
					// Init Referencing Image Library Container
					var refImageLibraryContainer = 	'<div class="contentEditor_ReferencingImageLibraryContainer"></div>';	
					_this.display.referencingImageLibraryContainer = $(refImageLibraryContainer);
					$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.referencingImageLibraryContainer) );
				}
				
				$(_this.content.bindingContainer).find('.contentGroup').each( function() {
					if ($(this).attr('content') == "graphic") {
						$(this).find('.contentEntry').each( function() {
							var currentGroupId 	= $(this).parent().attr('groupId');
							var currentLocaleId = $(this).attr('localeId');
							
							var currentGraphicInputsContainer =	$(	'<div class="contentEditor_GraphicInputsContainer contentEditor_GraphicInputsContainer_'+currentGroupId+'_'+currentLocaleId+'" style="display: none;">' +
																		'<table class="contentEditor_GraphicInputTable" width="100%" cellspacing="0" cellpadding="0" border="0">' +
																			'<tr>' +
																				'<td width="1%" class="imageLibraryToggleTD" style="padding: 8px 8px 8px 0; vertical-align: middle;">' +
																					'<select id="imageLibaryTypeSelect" class="imageLibaryTypeSelect style_select input125">' +
																					'</select>' +
																				'</td>' +
																				'<td class="imageLibrarySelectTD imageLibraryTD" align="left" style="vertical-align: middle;"></td>' +
																				'<td width="1%" class="customGraphicTD" style="vertical-align: middle;">' +
																					'<form id="fileUpload_'+currentGroupId+'_'+currentLocaleId+'" method="POST" enctype="multipart/form-data" class="position-relative overflow-hidden">' +
																						'<input id="fileInput_'+currentGroupId+'_'+currentLocaleId+'" type="file" name="files[]" class="contentEditor_FileUploadInput" size="1">' +
																						'<input title="' + _this.data.text.uploadImage + '" type="button" id="imageUploadBtn_'+currentGroupId+'_'+currentLocaleId+'" style="display: none;" />' +
																					'</form>' +
																				'<td class="graphicFileInputTD customGraphicTD" style="vertical-align: middle;"></td>' +
																				'<td width="1%" class="customGraphicTD" style="vertical-align: middle;"></td>' +
																				'<td class="graphicAppliedImageNameInputTD customGraphicTD" style="padding: 6px 12px; border-left: 1px solid #ccc; vertical-align: middle;">' +
																					'<span class="fullLineLabel" style="font-weight: normal; padding-left: 3px;">' + _this.data.text.appliedImageName + '</span>' +
																				'</td>' +
																				'<td class="cmsAssetInfoInputTD customGraphicTD"></td>' +
																			'</tr>' +
																		'<table>' +
																	'</div>');
							
							if ( _this.data.library_only ) {
								$(this).find('.useContentLibraryToggle input').attr('checked','checked');
								$(currentGraphicInputsContainer).find('.customGraphicTD,.graphicFileInputTD,.customGraphicTD,.graphicAppliedImageNameInputTD').remove();
							}
								
							$(currentGraphicInputsContainer).find('.graphicFileInputTD').append( $(this).find('.graphicFileInput') );
							$(currentGraphicInputsContainer).find('.graphicAppliedImageNameInputTD').append( $(this).find('.graphicAppliedImageName') );
							$(currentGraphicInputsContainer).find('.cmsAssetInfoInputTD').append( $(this).find('.cmsAssetInfo') );
							
							if (_this.imageLibraryApplied) {
								$(currentGraphicInputsContainer).find('.imageLibraryToggleTD').append( $(this).find('.useContentLibraryToggle') );
								$(currentGraphicInputsContainer).find('.imageLibrarySelectTD').append( $(this).find('.useContentLibrarySelect') );
								$(currentGraphicInputsContainer).find('.imageLibraryToggleTD .useContentLibraryToggle').hide();
								
								if ( _this.data.usage == "view" ) {
									if ( $(currentGraphicInputsContainer).find('.useContentLibraryToggle input').is(':checked') ) {
										var imageLibraryName = $(currentGraphicInputsContainer).find('.useContentLibraryToggle input').attr('itemName');
										var currentReferencingImageLibraryContainer =	$(	'<div class="contentEditor_InfoDivContainer contentEditor_ReferencingImageLibraryText contentEditor_ReferencingImageLibraryContainer contentEditor_ReferencingImageLibraryContainer_'+currentGroupId+'_'+currentLocaleId+'" style="'+(currentLocaleId == _this.data.currentLocaleId ? '' : 'display: none;')+'">' +
																								'<table class="contentEditor_SameAsTable" width="100%" cellspacing="0" cellpadding="0" border="0">' +
																									'<tr>' +
																										'<td><span class="fullLineLabel">' + _this.data.text.refImageLibrary + ': ' + imageLibraryName + '</span></td>' +
																									'</tr>' +
																								'<table>' +
																							'</div>');
										$(_this.display.referencingImageLibraryContainer).append( $(currentReferencingImageLibraryContainer) );
									}
								}
							} 
							if (_this.localImageLibraryApplied) {
								$(currentGraphicInputsContainer).find('.imageLibraryToggleTD').append( $(this).find('.useLocalContentLibraryToggle') );
								$(currentGraphicInputsContainer).find('.imageLibrarySelectTD').append( $(this).find('.useLocalContentLibrarySelect') );
								$(currentGraphicInputsContainer).find('.imageLibraryToggleTD .useLocalContentLibraryToggle').hide();
								
								if ( _this.data.usage == "view" ) {
									if ( $(currentGraphicInputsContainer).find('.useLocalContentLibraryToggle input').is(':checked') ) {
										var localImageLibraryName = $(currentGraphicInputsContainer).find('.useLocalContentLibraryToggle input').attr('itemName');
										var currentReferencingLocalImageLibraryContainer =	$(	'<div class="contentEditor_InfoDivContainer contentEditor_ReferencingImageLibraryText contentEditor_ReferencingImageLibraryContainer contentEditor_ReferencingLocalImageLibraryContainer_'+currentGroupId+'_'+currentLocaleId+'" style="'+(currentLocaleId == _this.data.currentLocaleId ? '' : 'display: none;')+'">' +
																									'<table class="contentEditor_SameAsTable" width="100%" cellspacing="0" cellpadding="0" border="0">' +
																										'<tr>' +
																											'<td><span class="fullLineLabel">' + _this.data.text.refLocalImageLibrary + ': ' + localImageLibraryName + '</span></td>' +
																										'</tr>' +
																									'<table>' +
																								'</div>');
										$(_this.display.referencingImageLibraryContainer).append( $(currentReferencingLocalImageLibraryContainer) );
									}
								}
							}

							$(_this.display.graphicUpdateContainer).append( $(currentGraphicInputsContainer) );
							
							// EDIT: Init async file upload
							if ( _this.data.usage == "edit" ) {

								// Select: Image libarary: Load entries
								if (_this.imageLibraryApplied) {
									$(currentGraphicInputsContainer).find("select[id^='conLibSelect_']").each( function() {
										
										var splId = $(this).attr('id').split('_');
										var zoneId = splId[1];
										var partId = splId[2];
										var localeId = splId[3];
										
										var selectedConLibId 	= $("#defaultContentLibraryId_" + zoneId + '_' + partId + ( localeId != null ? '_' + localeId : '')).val();
										if ( selectedConLibId != undefined )
											$(this).html('<option id="' + selectedConLibId + '" value="' + selectedConLibId + '" selected="selected">BINDING PLACEHOLDER</option>');
										
										if ( !$(this).is('.style_select') )
											$(this).addClass('style_select');
										
										$(this).change(function(){
											if ( $(this).find('option:selected').val() != "0") {
												_this.getCurrentGraphicInputsContainer().find('.useContentLibraryBinding').attr('checked', 'checked');
												// Global, local, upload
												var types = [2, 3, 1];
												for (var i = 0; i < types.length; i++) {
													if (_this.getCurrentGraphicInputsContainer().find('.imageLibaryTypeSelect option#imageLibaryTypeSelect_' + types[i]).length != 0 ) {
														if ( _this.getCurrentGraphicInputsContainer().find('.imageLibaryTypeSelect option:selected').attr('id') != 'imageLibaryTypeSelect_' + types[i] )
															_this.getCurrentGraphicInputsContainer().find('.imageLibaryTypeSelect').selectOption('imageLibaryTypeSelect_' + types[i]);
														break;
													}
												}

											} else {
												_this.getCurrentGraphicInputsContainer().find('.useContentLibraryBinding').attr('checked', false);
											}
											if ( _this.data.currentImageLibraryId == undefined || _this.data.currentImageLibraryId != $(this).find('option:selected').val() ) {
												_this.requestImageLibraryContent($(this).find('option:selected').val(), false);
												_this.data.currentImageLibraryId = $(this).find('option:selected').val();
											}	
										});

									});
									
								}
								
								// Select: Image libarary: Load entries
								if (_this.localImageLibraryApplied) {
									$(currentGraphicInputsContainer).find("select[id^='localConLibSelect_']").each( function() {
										
										var splId = $(this).attr('id').split('_');
										var zoneId = splId[1];
										var partId = splId[2];
										var localeId = splId[3];
										
										var selectedLocalConLibId 	= $("#defaultLocalContentLibraryId_" + zoneId + '_' + partId + ( localeId != null ? '_' + localeId : '')).val();
										if ( selectedLocalConLibId != undefined )
											$(this).html('<option id="' + selectedLocalConLibId + '" value="' + selectedLocalConLibId + '" selected="selected">BINDING PLACEHOLDER</option>');
										
										if ( !$(this).is('.style_select') )
											$(this).addClass('style_select');
										
										$(this).change(function(){
											if ( $(this).find('option:selected').val() != "0") {
												_this.getCurrentGraphicInputsContainer().find('.useLocalContentLibraryBinding').attr('checked','checked');
												// Local, global, upload
												var types = [3, 2, 1];
												for (var i = 0; i < types.length; i++) {
													if (_this.getCurrentGraphicInputsContainer().find('.imageLibaryTypeSelect option#imageLibaryTypeSelect_' + types[i]).length != 0 ) {
														if ( _this.getCurrentGraphicInputsContainer().find('.imageLibaryTypeSelect option:selected').attr('id') != 'imageLibaryTypeSelect_' + types[i] )
															_this.getCurrentGraphicInputsContainer().find('.imageLibaryTypeSelect').selectOption('imageLibaryTypeSelect_' + types[i]);
														break;
													}
												}
											} else {
												_this.getCurrentGraphicInputsContainer().find('.useContentLibraryBinding').attr('checked', false);
											}
											if ( _this.data.currentLocalImageLibraryId == undefined || _this.data.currentLocalImageLibraryId != $(this).find('option:selected').val() ) {
												_this.requestImageLibraryContent($(this).find('option:selected').val(), true);
												_this.data.currentLocalImageLibraryId = $(this).find('option:selected').val();
											}
										});

									});
									
								}
								
								// Button: Style upload button, attach file input trigger
								$("#imageUploadBtn_"+currentGroupId+"_"+currentLocaleId).styleActionElement();
								$("#imageUploadBtn_"+currentGroupId+"_"+currentLocaleId+"_button").hover( function() {
									_this.fileUploadBtnInit();	
								}); 
								
								// Plug-in: Init async upload
								if ( !_this.data.library_only )
									$("#fileUpload_"+currentGroupId+"_"+currentLocaleId).fileupload({
								        url				: context+'/uploadFileHandler.form?type=graphicContent&action=upload&tk='+getParam('tk'),
								        dataType		: 'json',
							            add: function (e, data) {
								        	var varInputFileType = $(currentGraphicInputsContainer).find('.graphicFileInputTD .graphicFileInput').attr('fileType');
											var currentInputFileType = !_.isEmpty(varInputFileType) ? varInputFileType : "";
											var permittedFileTypes = /.+$/i;
											if (currentInputFileType == "ANY_IMAGE")
												permittedFileTypes = "/(\.|\/)(gif|jpe?g|pdf|rtf|tif?f|img|png|dxf|dlf|docx|eps|pseg)$/i";
											else if (currentInputFileType == "WEB")
												permittedFileTypes = "/(\.|\/)(gif|jpe?g|png)$/i";
											else
												permittedFileTypes = "/(\.|\/)(" + currentInputFileType + ")$/i";
							                
							                var parts = permittedFileTypes.split('/'), modifiers = parts.pop();
							                parts.shift();
							                var acceptFileTypesRegEx = new RegExp(parts.join('/'), modifiers);
							                
							                // Validation: File type
							                for (var i=0; i<data.files.length; i++)
							                	if ( !acceptFileTypesRegEx.test(data.files[i].name) ) {
							                		$("#fileUpload_"+currentGroupId+"_"+currentLocaleId).popupFactory({
														title				: client_messages.content_editor.oops,
														trigger				: "instant",
														fnSetContent		: function(o) {
																				var fileTypes = currentInputFileType.split('?')[0].toUpperCase();
																				if ( currentInputFileType == "ANY_IMAGE" )
																					fileTypes = "GIF, JPG, PDF, RTF, TIF, PNG, IMG, EPS, DXF, DLF, PSEG or DOCX";
																				else if ( fileTypes == "ANY")
																					fileTypes = "GIF, JPG or PNG";
																				else if ( fileTypes == "JPE" )
																					fileTypes = "JPG";
															
																				return 	"<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" + 
																							fmtClientMessage(client_messages.content_editor.bad_image_file_type, fileTypes) + 
																						"</div>";
																			  }
							    						});
							                		
							                		return;
							                	}
							                
							                // Validation: Prevent multiple file upload
							                if ( data.files.length > 1 ) {
						                		$("#fileUpload_"+currentGroupId+"_"+currentLocaleId).popupFactory({
													title				: client_messages.content_editor.oops,
													trigger				: "instant",
													fnSetContent		: function(o) {
																			return "<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" + 
																						client_messages.text.too_many_files_selected + 
																					"</div>";
																		  }
						    						});
						                		
						                		return;
							                }
							                	
							                data.submit();
							            },
								        done			: function (e, data) {
								        					// Post Upload: Update current display
								            				_this.getCurrentGraphicInputsContainer().find('.graphicFileInputTD input').val(data.result.files[0].sandboxId);
								            				_this.setGraphicContent(data.result.files[0].name, 
								            										data.result.files[0].url, 
								            										client_messages.content_editor.pending_save, 
								            										_this.getCurrentGraphicAppliedImageName());		            		
								            				_this.getCurrentGraphicInputsContainer().find('.graphicFileInput').attr('imgPath',data.result.files[0].url);
								            				_this.getCurrentGraphicInputsContainer().find('.graphicFileInput').attr('imgName',data.result.files[0].name);
								            				_this.getCurrentGraphicInputsContainer().find('.graphicFileInput').attr('uploadDate',client_messages.content_editor.pending_save);
								            				
								            				// Same as default: Update same as references
								            				if (_this.data.currentLocaleId == _this.data.defaultLocaleId) {
								            					
								            					for (index in _this.data.languages) {				
								            						for (localeId in _this.data.languages[index]) {
								            							if (localeId != _this.data.defaultLocaleId) {
	
								            								if ( $(_this.display.container).find('.contentEditor_SameAsDefaultInputContainer_'+_this.data.currentContentGroupId+'_'+localeId+' .sameAsDefaultInput input[type=checkbox]:checked').length > 0 ) {
								            									$(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicInputsContainer_'+_this.data.currentContentGroupId+'_'+localeId).each( function() {
												        							$(this).find('.graphicFileInput').attr('imgName', data.result.files[0].name);
														        					$(this).find('.graphicFileInput').attr('imgPath', data.result.files[0].url);
														        					$(this).find('.graphicFileInput ').attr('uploadDate', client_messages.content_editor.pending_save);
														        					$(this).find('.graphicFileInput input').val(data.result.files[0].sandboxId);
												        						});									
								            								}
								            							}
								            						}
								            					} 						
								        						
								            				}
	
								        				  }
								    })
								    .bind('fileuploadstart', function (e) {
										$(_this.display.container).find('.contentEditor_InnerContentContainer').hide();
										$(_this.display.container).find('.contentEditor_LoadingIndicatorContainer').show();
								    })
								    .bind('fileuploadalways', function (e, data) {
										$(_this.display.container).find('.contentEditor_LoadingIndicatorContainer').hide();
										$(_this.display.container).find('.contentEditor_InnerContentContainer').show();
								    })
								    .bind('fileuploaddrop', function (e, data) {
								    	// Block drag and drop handler for all but current language
								    	if ( $(this).attr('id') != "fileUpload_"+_this.data.currentContentGroupId+'_'+_this.data.currentLocaleId)
								    		e.preventDefault();
								    });

							}
							
						});
					}
				});

				if ( _this.data.currentContentType == "graphic" && _this.data.usage == "edit" ) {
					var sameAsParent = _this.getCurrentSameAsParentInputContainer().find('.sameAsParentInput').is(":checked");
					if (!sameAsParent) {
						_this.getCurrentGraphicInputsContainer().show();
					}
					_this.initImageSelectMenu( _this.getCurrentGraphicInputsContainer().find("[id^='conLibSelect_']") );
					_this.initLocalImageSelectMenu( _this.getCurrentGraphicInputsContainer().find("[id^='localConLibSelect_']") );
				}
				
			}

			// SHARED CONTAINER
			// Init Display: Container
			var sharedContainer =	'<div class="contentEditor_SharedContainer" style="display: none;">' +
										_this.data.text.shared + ':' +
										'&nbsp;&nbsp;<span class="contentEditor_SharedLabel"></span>' +
										'<span class="contentEditor_SharedNoContent" style="display: none">' +
											'&nbsp;&nbsp;[' + _this.data.text.noContent + ']' +
										'</span>' +
									'</div>';
			_this.display.sharedContainer = $(sharedContainer);
			$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.sharedContainer) );
			
			// STATIC CONTENT
			if ( textContentExists ) {
				

				// Init Display: Static Text Container
				var staticTextContainer = 	'<div style="display: none; position: relative;">' +
												'<div class="contentEditor_btnContainer" style="position: absolute; bottom: 16px; right: ' + (common.isFeatureEnabled('ViewRawContent') ? '80px' : '45px') + '; z-index: 1; white-space: nowrap;">' +
													'<div class="contentEditor_contentRenderToggle actionBtnHighlighted actionBtn_roundAll actionBtn" data-toggle="tooltip" title="'+client_messages.content_editor.render_content+'">' +
														'<i class="fas fa-info" aria-hidden="true" style="color: skyblue; top: 3px; position: relative;"></i>' +
													'</div>' +
													'<div class="contentEditor_sampleValueToggle actionBtnHighlighted actionBtn_roundAll actionBtn" style="margin-left: 4px;" data-toggle="tooltip" title="'+client_messages.content_editor.show_variable_sample_values+'">' +
														'<i class="fas fa-info" aria-hidden="true" style="color: yellow; top: 3px; position: relative;"></i>' +
													'</div>' +
													(common.isFeatureEnabled('ViewRawContent') ?
														'<div class="contentEditor_encodedContentToggle actionBtnHighlighted actionBtn_roundAll actionBtn" style="margin-left: 4px;" data-toggle="tooltip" title="'+client_messages.text.encoded_content+'">' +
															'<i class="fa fa-laptop-code" aria-hidden="true"></i>' +
														'</div>' : '') +
													'<div class="contentEditor_tagTextToggle actionBtnHighlighted actionBtn_roundAll actionBtn" style="margin-left: 4px;" data-toggle="tooltip" title="'+client_messages.text.source+'">' +
														'<i class="fa fa-code" aria-hidden="true"></i>' +
													'</div>' +
													'<div class="contentEditor_viewTagPanel actionBtnHighlighted actionBtn_roundAll actionBtn" style="margin-left: 4px; width: 30px;" data-toggle="tooltip" title="View Tag Panel">' +
														'<i class="fa fa-list-tree" aria-hidden="true"></i>' +
													'</div>' +
													'<div class="contentEditor_fullScreenToggle actionBtnHighlighted actionBtn_roundAll actionBtn" style="margin-left: 4px;" data-toggle="tooltip" title="'+client_messages.content_editor.view_fullscreen+'">' +
														'<i class="fullscreenIcon fa fa-expand" aria-hidden="true"></i>' +
													'</div>' +
								                '</div>' +
												'<div class="contentEditor_StaticTextContentContainer" style="max-width: ' + (_this.display.container.innerWidth() - 2) + 'px;">';
				
				
				if ( _this.data.applies_freeform )
					staticTextContainer += 			"<div class=\"contentEditor_OuterFreeformContainer\">" +
														"<div class=\"contentEditor_FreeformContainer contentContainer\"></div>" +
													"</div>";
				else
					staticTextContainer += 			"<div class=\"contentEditor_OuterPageContainer contentContainer\"></div>";

				staticTextContainer +=			'</div>' +	
											'</div>';
				_this.display.staticTextContentContainer = $(staticTextContainer);

				// CANVAS: Background image
				if ( _this.data.contentObjectId != undefined ) {
					var stampDate = new Date();
					$.ajax({
						type: "GET",
						url: context + "/getObjectInfo.form" +
											"?type=messageZoneSummary" + 
											"&objectId=" + _this.data.contentObjectId +
											"&cacheStamp=" + (stampDate.getTime()),
						dataType: "json",
						success: function(data) {
							if ( data.section && data.section.apply_background_image && data.is_transparent && !data.is_multipart && data.section.background_image ) {
								var topOffset 		= -1 * (data.section.margin_top + data.section.header_height + ((data.top/100) * data.section.body_height) );
								var leftOffset 		= -1 * (data.section.margin_left + data.section.region_left_width + ((data.left/100) * data.section.body_width) );
								
								// Position: -4 left and top:  Accounts for zone borders in layout manager
								$('.contentContainer').css({
									'background'		: 'url("' + data.section.background_image + '&cacheStamp=' + stampDate.getTime() + '&tk=' + getParam('tk') + '") no-repeat '+(leftOffset + 0 - 4)+'px '+(topOffset + 0 - 4)+'px',
									'background-size'	: data.section.fit_to_canvas ? data.section.width + 'px' + ' ' + data.section.height + 'px' : 'auto auto'
								});
							}
						}
					});
					
				}
				
				if ( _this.data.currentContentType == "text" ){
					if ( _this.getCurrentContentEntry().find('.embeddedVideo').length > 0 && _this.data.usage == "view" ) {
						var videoHtml = $.trim(_this.getCurrentContentEntry().find('.embeddedVideo').text());
						_this.display.staticTextContentContainer.find('.contentContainer').html(videoHtml);
					} else {
						var content = _this.getCurrentEditorBinding().val();
						_this.display.staticTextContentContainer.find('.contentContainer').html( content );
					}
				}
				$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.staticTextContentContainer) );
				_this.styles.updateStyleReferenceCSS();
				
				_this.display.staticTextContentContainer
					.find('.contentEditor_tagTextToggle,.contentEditor_encodedContentToggle,.contentEditor_fullScreenToggle,.contentEditor_contentRenderToggle,.contentEditor_sampleValueToggle, .contentEditor_viewTagPanel')
					.click( function() {
					if(!$(this).is('.contentEditor_viewTagPanel')) {
						if ($(this).is('.actionBtn_selected')) {
							$(this).removeClass('actionBtn_selected');
							$(this).addClass('actionBtn');
						} else {
							$(this).removeClass('actionBtn');
							$(this).addClass('actionBtn_selected');
						}
					}
					
					if ( $(this).is('.contentEditor_tagTextToggle') ) {
						_this.toggleStaticTextTagDisplay();
					} else if ( $(this).is('.contentEditor_encodedContentToggle') ) {
						_this.toggleEncodedContentDisplay();
					} else if ( $(this).is('.contentEditor_fullScreenToggle') ) {
						_this.toggleFullScreenDisplay();
					} else if ( $(this).is('.contentEditor_contentRenderToggle,.contentEditor_sampleValueToggle') ) {
						_this.toggleStaticTextRendering();
					} else if( $(this).is('.contentEditor_viewTagPanel')){
						_this.toggleViewTagPanel();
					}
					
				});
				// Init toggle states
				if ( localStorage.getItem("msgpt_contentrendering_enable") == null || localStorage.getItem("msgpt_contentrendering_enable") == "true" )
					_this.display.staticTextContentContainer.find('.contentEditor_contentRenderToggle').addClass('actionBtn_selected');
				if ( localStorage.getItem("msgpt_samplevaluerendering_enable") == "true" )
					_this.display.staticTextContentContainer.find('.contentEditor_sampleValueToggle').addClass('actionBtn_selected');

				_this.display.staticTextContentContainer.find('[data-toggle="tooltip"]').tooltip();
				
				_this.initTextContentActions();
				_this.updateBackgroundColor();	
				_this.updateCanvasSize();
				_this.toggleStaticTextRendering();
				
			}
			if ( graphicContentExists ) {

				var graphicContainerHeight = (_this.data.usage == "edit" ? '210px' : '180px');
				
				// Init Display: Static Graphic Container
				var staticGraphicContainer = 	'<div class="contentEditor_StaticGraphicContentContainer" style="display: none;">' +
													'<div class="contentEditor_StaticGraphicInfoContainer" style="white-space: normal;">' +
														'<div align="left" style="padding: 0px; padding-right: 6px;">' +
															'<span class="fullLineLabel">' + _this.data.text.currentImage + ':</span>' + 
															'<span class="contentEditor_GraphicName"><a href="#"></a></span>' +
														'</div>' +
														'<div align="left" style="padding: 0px; padding-right: 6px;">' +
															'<span class="fullLineLabel">' + _this.data.text.uploadImageDate + ':</span>' +
															'<span class="contentEditor_GraphicUploadDate"></span>' +
														'</div>';

				// CMS Image
				if($('.cmsAssetInfo').length > 0){
					staticGraphicContainer += 			'<div align="left" style="padding: 0px; padding-right: 6px;">' +
															'<span class="fullLineLabel">' + _this.data.text.cmsLastUpdateDate + ':</span>' +
															'<span class="contentEditor_GraphicCmsLastUpdateDate"></span>' +
														'</div>' +
														'<div align="left" style="padding: 0px; padding-right: 6px;">' +
															'<span class="fullLineLabel">' + _this.data.text.cmsLastSyncDate + ':</span>' +
															'<span class="contentEditor_GraphicCmsLastSyncDate"></span>' +
														'</div>';
				}
																			
					staticGraphicContainer += 		'<div align="left" class="contentEditor_GraphicAppliedImageNameContainer" style="display: none; padding: 0px;">' +
														'<span class="fullLineLabel">' + _this.data.text.appliedImageName + ':</span>' +
														'<span class="contentEditor_GraphicAppliedImageName"></span>' +
													'</div>';
					
					staticGraphicContainer += 		'</div>' +
													'<div class="contentEditor_ImageContainer" style="height: auto; min-height: '+graphicContainerHeight+'; max-width: ' + _this.display.container.innerWidth() + 'px;">' +
														'<div id="noImageMsgContainer" class="InfoSysContainer_info">' +
															'<i class="fa icon fa-info-circle" aria-hidden="true"></i>' +
															'<p>' + _this.data.text.noImageUploaded + '</p>' +
														'</div>' +
														'<div id="clickToDownloadMsgContainer" class="InfoSysContainer_info">' +
                        									'<i class="fa icon fa-info-circle" aria-hidden="true"></i>' +
															'<p>' + _this.data.text.clickToDownload + '</p>' +
														'</div>';
				if ( false && _this.data.usage == "edit" && _this.data.enabledFileEdit ) {
					staticGraphicContainer +=			'<div id="editFileBtnContainer" class="contentEditor_InfoDivContainer">' +
															'<div style="display: inline-block; vertical-align: middle;">' + 
																_this.data.text.click + 
															'</div>' +
															'<div style="display: inline-block; vertical-align: middle;">' + 
																'<input title="' + _this.data.text.edit + '" type="button" id="editFileBtn" style="display: none;" />';
																
					// Firefox: Required to object webdav docs
					if ( $.browser.mozilla && $(document).find('#winFirefoxPlugin').length == 0 )
						staticGraphicContainer +=				'<object id="winFirefoxPlugin" type="application/x-sharepoint" width="0" height="0" style="visibility:hidden;"></object>';

					staticGraphicContainer +=				'</div>' +
															'<div style="display: inline-block; vertical-align: middle;">' + 
																_this.data.text.toEditContentFile + 
															'</div>' +
														'</div>';
				}											
				staticGraphicContainer +=				'<img class="contentEditor_GraphicImg" src=""/>' +
														'<div id="pdfViewerContainer"></div>' +
													'</div>';
													
				// Static Graphic Image link
				if($('.graphicImageLink').length > 0){
					staticGraphicContainer +=		'<div class="contentEditor_StaticGraphicImageLinkContainer">' +
														'<div align="left" style="padding: 0px; padding-right: 6px; padding-left: 6px;">' +
															'<div class="fullLineLabel" style="display: inline-block; min-width: 75px; vertical-align: middle;">' + _this.data.text.imageLink + ': </div>' +
															'<div class="contentEditor_StaticGraphicImageLink" style="display: inline-block; vertical-align: middle; font-size: 11px;"></div>' +
														'</div>' +	
													'</div>';
				}
				
				// Static Graphic Image Alt Text
				if(_this.data.showImageAltText && $('.graphicImageAltText').length > 0){
				  staticGraphicContainer +=			'<div class="contentEditor_StaticGraphicImageAltTextContainer">' +
				                    					'<div align="left" style="padding: 0px; padding-right: 6px; padding-left: 6px;">' +
				                    						'<div class="fullLineLabel" style="display: inline-block; min-width: 75px; vertical-align: middle;">' + _this.data.text.imageAltText + ': </div>' +
				                    						'<div class="contentEditor_StaticGraphicImageAltText" style="display: inline-block; vertical-align: middle; font-size: 11px;"></div>' +
				                    					'</div>' +	
				                    				'</div>';
				}

				// Static Graphic Image External Link
				if(_this.data.showImageExtLink && $('.graphicImageExtLink').length > 0){
					staticGraphicContainer +=		'<div class="contentEditor_StaticGraphicImageExtLinkContainer">' +
														'<div align="left" style="padding: 0px; padding-right: 6px; padding-left: 6px;">' +
															'<div class="fullLineLabel" style="display: inline-block; min-width: 75px; vertical-align: middle;">' + _this.data.text.imageExtLink + ': </div>' +
															'<div class="contentEditor_StaticGraphicImageExtLink" style="display: inline-block; vertical-align: middle; font-size: 11px;"></div>' +
														'</div>' +
													'</div>';
				}

				// Static Graphic Image External Path
				if(_this.data.showImageExtPath && $('.graphicImageExtPath').length > 0){
					staticGraphicContainer +=		'<div class="contentEditor_StaticGraphicImageExtPathContainer">' +
														'<div align="left" style="padding: 0px; padding-right: 6px; padding-left: 6px;">' +
															'<div class="fullLineLabel" style="display: inline-block; min-width: 75px; vertical-align: middle;">' + _this.data.text.imageExtPath + ': </div>' +
															'<div class="contentEditor_StaticGraphicImageExtPath" style="display: inline-block; vertical-align: middle; font-size: 11px;"></div>' +
														'</div>' +
													'</div>';
				}

				staticGraphicContainer +=		'</div>';

				_this.display.staticGraphicContentContainer = $(staticGraphicContainer);

				if ( (_this.getCurrentGraphicInputsContainer().find('.useContentLibraryToggle input').is(':checked') &&
					  parseInt(_this.getCurrentGraphicInputsContainer().find("[id^='conLibSelect_']").val()) <= 0 ) ||
					  (!_this.getCurrentGraphicInputsContainer().find('.useContentLibraryToggle input').is(':checked') &&
					  (!_this.getCurrentGraphicFileName() || _this.getCurrentGraphicFileName() == "") ) ||
					  (_this.getCurrentGraphicInputsContainer().find('.useLocalContentLibraryToggle input').is(':checked') &&
					  parseInt(_this.getCurrentGraphicInputsContainer().find("[id^='localConLibSelect_']").val()) <= 0 ) ||
					  (!_this.getCurrentGraphicInputsContainer().find('.useLocalContentLibraryToggle input').is(':checked') &&
					  (!_this.getCurrentGraphicFileName() || _this.getCurrentGraphicFileName() == "") ) ) {
					_this.data.graphicInitComplete = true;
				}

				if ( _this.data.currentContentType == "graphic" ) {
					_this.setGraphicContent( _this.getCurrentGraphicFileName(), 
											_this.getCurrentGraphicFilePath(), 
											_this.getCurrentGraphicUploadDate(), 
											_this.getCurrentGraphicAppliedImageName() );
					_this.setCmsAssetInfo(_this.getCurrentGraphicCmsLastUpdateDate(), _this.getCurrentGraphicCmsLastSyncDate());
				}
				$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.staticGraphicContentContainer) );
				
				// INIT IMAGE LINK CONTAINER
				if ( $(_this.content.bindingContainer).find('.graphicImageLink').length > 0 ) {
					var imageLinkContainer = '<div class="contentEditor_GraphicImageLinkContainer" style="display: none;"></div>';	
					_this.display.graphicImageLinkContainer = $(imageLinkContainer);

					$('#mini-editor').append( $(_this.display.graphicImageLinkContainer) );
					
					$(_this.content.bindingContainer).find('.contentGroup').each( function() {
						$(this).find('.contentEntry').each( function() {
							if ( $(this).find('.graphicImageLink').length > 0 ) {
								var currentGroupId 	= $(this).parent().attr('groupId');
								var currentLocaleId = $(this).attr('localeId');
								var currentImageLinkInputsContainer =	$('<div class="contentEditor_GraphicImageLinkContainer_'+currentGroupId+'_'+currentLocaleId+'" style="display: none; white-space: nowrap;">' +
																				'<table class="contentEditor_ImageLinkTable">' +
																					'<tr>' +
																						'<td>' +
																							'<div class="mb-3 pb-2 border-bottom font-weight-bold">' + _this.data.text.imageLink + '</div>' +
																							'<div class="imageLinkInputSpan"></div>' +
																						'</td>' +
																					'</tr>' +
																				'<table>' +
																			'</div>');

								$(currentImageLinkInputsContainer).find('.imageLinkInputSpan').append( $(this).find('.graphicImageLink') );
		
								if ( _this.data.usage == "view" )
									$(currentImageLinkInputsContainer).find('.imageLinkInputSpan').parent().hide();

								$(_this.display.graphicImageLinkContainer).append( $(currentImageLinkInputsContainer) );
							}
						});
					});
				}
				
				// INIT IMAGE ALT TEXT CONTAINER
				if ( _this.data.showImageAltText && $(_this.content.bindingContainer).find('.graphicImageAltText').length > 0 ) {
					var imageAltTextContainer = '<div class="contentEditor_GraphicImageAltTextContainer" style="display: none;"></div>';	
					_this.display.graphicImageAltTextContainer = $(imageAltTextContainer);

					$('#mini-editor').append( $(_this.display.graphicImageAltTextContainer) );
					
					$(_this.content.bindingContainer).find('.contentGroup').each( function() {
						$(this).find('.contentEntry').each( function() {
							if ( $(this).find('.graphicImageAltText').length > 0 ) {
								var currentGroupId 	= $(this).parent().attr('groupId');
								var currentLocaleId = $(this).attr('localeId');
								var currentImageAltTextInputsContainer =	$('<div class="contentEditor_GraphicImageAltTextContainer_'+currentGroupId+'_'+currentLocaleId+'" style="display: none; white-space: nowrap;">' +
																				'<table class="contentEditor_ImageAltTextTable">' +
																					'<tr>' +
																						'<td>' +
																							'<div class="mb-3 pb-2 border-bottom font-weight-bold">' + _this.data.text.imageAltText + '</div>' +
																							'<div class="imageAltTextInputSpan"></div>' +
																						'</td>' +
																					'</tr>' +
																				'<table>' +
																			'</div>');
								$(currentImageAltTextInputsContainer).find('.imageAltTextInputSpan').append( $(this).find('.graphicImageAltText') );
		
								if ( _this.data.usage == "view" )
									$(currentImageAltTextInputsContainer).find('.imageAltTextInputSpan').parent().hide();
								
								$(_this.display.graphicImageAltTextContainer).append( $(currentImageAltTextInputsContainer) );
							}
						});
					});
				}

				// INIT IMAGE EXTERNAL LINK CONTAINER
				if ( _this.data.showImageExtLink && $(_this.content.bindingContainer).find('.graphicImageExtLink').length > 0 ) {
					var imageExtLinkContainer = '<div class="contentEditor_GraphicImageExtLinkContainer" style="display: none;"></div>';
					_this.display.graphicImageExtLinkContainer = $(imageExtLinkContainer);

					$('#mini-editor').append( $(_this.display.graphicImageExtLinkContainer) );

					$(_this.content.bindingContainer).find('.contentGroup').each( function() {
						$(this).find('.contentEntry').each( function() {
							if ( $(this).find('.graphicImageExtLink').length > 0 ) {
								var currentGroupId 	= $(this).parent().attr('groupId');
								var currentLocaleId = $(this).attr('localeId');
								var currentImageExtLinkInputsContainer =	$('<div class="contentEditor_GraphicImageExtLinkContainer_'+currentGroupId+'_'+currentLocaleId+'" style="display: none; white-space: nowrap;">' +
																				'<table class="contentEditor_ImageExtLinkTable">' +
																					'<tr>' +
																						'<td>' +
																							'<div class="mb-3 pb-2 border-bottom font-weight-bold">' + _this.data.text.imageExtLink + '</div>' +
																							'<div class="imageExtLinkInputSpan"></div>' +
																						'</td>' +
																					'</tr>' +
																				'<table>' +
																			'</div>');

								$(currentImageExtLinkInputsContainer).find('.imageExtLinkInputSpan').append( $(this).find('.graphicImageExtLink') );

								if ( _this.data.usage == "view" )
									$(currentImageExtLinkInputsContainer).find('.imageExtLinkInputSpan').parent().hide();

								$(_this.display.graphicImageExtLinkContainer).append( $(currentImageExtLinkInputsContainer) );
							}
						});
					});
				}

				// INIT IMAGE EXTERNAL PATH CONTAINER
				if ( _this.data.showImageExtPath && $(_this.content.bindingContainer).find('.graphicImageExtPath').length > 0 ) {
					var imageExtPathContainer = '<div class="contentEditor_GraphicImageExtPathContainer" style="display: none;"></div>';
					_this.display.graphicImageExtPathContainer = $(imageExtPathContainer);

					$('#mini-editor').append( $(_this.display.graphicImageExtPathContainer) );

					$(_this.content.bindingContainer).find('.contentGroup').each( function() {
						$(this).find('.contentEntry').each( function() {
							if ( $(this).find('.graphicImageExtPath').length > 0 ) {
								var currentGroupId 	= $(this).parent().attr('groupId');
								var currentLocaleId = $(this).attr('localeId');
								var currentImageExtPathInputsContainer =	$('<div class="contentEditor_GraphicImageExtPathContainer_'+currentGroupId+'_'+currentLocaleId+'" style="display: none; white-space: nowrap;">' +
																				'<table class="contentEditor_ImageExtPathTable">' +
																					'<tr>' +
																						'<td>' +
																							'<div class="mb-3 pb-2 border-bottom font-weight-bold">' + _this.data.text.imageExtPath + '</div>' +
																							'<div class="imageExtPathInputSpan"></div>' +
																						'</td>' +
																					'</tr>' +
																				'<table>' +
																			'</div>');

								$(currentImageExtPathInputsContainer).find('.imageExtPathInputSpan').append( $(this).find('.graphicImageExtPath') );

								if ( _this.data.usage == "view" )
									$(currentImageExtPathInputsContainer).find('.imageExtPathInputSpan').parent().hide();

								$(_this.display.graphicImageExtPathContainer).append( $(currentImageExtPathInputsContainer) );
							}
						});
					});
				}
				
				if ( _this.data.currentContentType == "graphic" ) {
					// IMAGE LINK
					_this.setGraphicImageLink(_this.getCurrentGraphicImageLink());
					if (  _this.data.usage == "edit" )
						_this.getCurrentGraphicImageLinkContainer().show();
					// IMAGE ALT TEXT
					_this.setGraphicImageAltText(_this.getCurrentGraphicImageAltText());
					if (  _this.data.usage == "edit" ){
						if($('.graphicImageLink').length > 0){
							$('.contentEditor_GraphicImageAltTextContainer').hide();
						}						
						_this.getCurrentGraphicImageAltTextContainer().show();
					}
					// IMAGE EXTERNAL LINK
					_this.setGraphicImageExtLink(_this.getCurrentGraphicImageExtLink());
					if (  _this.data.usage == "edit" ){
						if($('.graphicImageLink').length > 0 || $('.graphicImageAltText').length > 0){
							$('.contentEditor_GraphicImageExtLinkContainer').hide();
						}
						_this.getCurrentGraphicImageExtLinkContainer().show();
					}
					// IMAGE EXTERNAL PATH
					_this.setGraphicImageExtPath(_this.getCurrentGraphicImageExtPath());
					if (  _this.data.usage == "edit" ){
						if($('.graphicImageLink').length > 0 || $('.graphicImageAltText').length > 0){
							$('.contentEditor_GraphicImageExtPathContainer').hide();
						}
						_this.getCurrentGraphicImageExtPathContainer().show();
					}
				}
				
				$(_this.display.container).find('.contentEditor_InnerContentContainer #editFileBtn').styleActionElement();
				$("#editFileBtn_button").click( function() {
					_this.editContentFileAction();	
				}); 
				
			}
			
			// SUPPRESS CONTAINER
			// Init Display: Suppress
			var suppressContainer =	'<div class="contentEditor_SuppressContainer" style="display: none;">' +
										'<div class="InfoSysContainer_info">' +
                							'<i class="fa icon fa-info-circle" aria-hidden="true"></i>' +
											'<p>' + _this.data.text.suppressed + '</p>' +
										'</div>' + 
									'</div>';
			_this.display.suppressContainer = $(suppressContainer);
			$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.suppressContainer) );
			
			// NO STATE CONTAINER
			// Init Display: No State
			var noStateContainer =	'<div class="contentEditor_NoStateContainer" style="display: none;">' +
										'<div class="InfoSysContainer_info">' +
											'<i class="fa icon fa-info-circle" aria-hidden="true"></i>' +
											'<p>' + _this.data.text.noState + '</p>' +
										'</div>' + 
									'</div>';
			_this.display.noStateContainer = $(noStateContainer);
			$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.noStateContainer) );

			// LEAVE EMPTY CONTAINER
			// Init Display: Leave Empty
			var leaveEmptyContainer =	'<div class="contentEditor_LeaveEmptyContainer" style="display: none;">' +
											'<div class="InfoSysContainer_info">' +
												'<i class="fa icon fa-info-circle" aria-hidden="true"></i>' +
												'<p>' + _this.data.text.leaveEmpty + '</p>' +
											'</div>' + 
										'</div>';
			_this.display.leaveEmptyContainer = $(leaveEmptyContainer);
			$(_this.display.container).find('.contentEditor_InnerContentContainer').append( $(_this.display.leaveEmptyContainer) );
			
			// CHARACTER COUNT CONTAINER
			// Init Display: Character Count
			if (_this.data.charCountEnabled) {
				var charCountContainer =	'<div class="contentEditor_CharacterCountContainer">' +
												'<span class="fullLineLabel">' +
													_this.data.text.charCount + ':' +
													'<span class="contentEditor_CharacterCount"></span>' +
												'</span>' + 
											'</div>';
				_this.display.characterCountContainer = $(charCountContainer);
				$(_this.display.container).find('.contentEditor_OuterContentContainer').append( $(_this.display.characterCountContainer) );
				_this.updateCharacterCount();
			}
			
			// CONTENT ACTIONS CONTAINER
			// Init Display: Content Actions
			if ( $(_this.content.bindingContainer).find('.contentActions').length > 0 ) {
				var contentActionsContainer =	'<div align="right" class="contentEditor_OuterContentActionsContainer">' +
													'<table class="contentEditor_ContentActionsTable" width="100%" cellspacing="0" cellpadding="0" border="0"><tr>' +
														'<td align="right" style="padding: 0px; position: relative;">' +
														'</td>' +
													'</tr></table>' +
												'</div>';
				_this.display.contentActionsContainer = $(contentActionsContainer);
				$(_this.display.container).find('.contentEditor_OuterContentContainer').prepend( $(_this.display.contentActionsContainer) );
					
				$(_this.content.bindingContainer).find('.contentActions').each( function() {
					var currentLocaleId = $(this).attr('localeId');
						
					var currentContentActionContainer = $('<div class="contentEditor_ContentActionContainer_'+currentLocaleId+'" style="display: none;"></div>');
					$(currentContentActionContainer).append( $(this).find('select') );
					$(currentContentActionContainer).append( $(this).find('input') );
	
					$(_this.display.contentActionsContainer).find('td').append( $(currentContentActionContainer) );
				});
				$(_this.display.container).find('.contentEditor_OuterContentContainer').css('paddingTop','0px');
				$(_this.display.contentActionsContainer).find('select').styleActionElement();
				$(_this.display.contentActionsContainer).find('input:button').styleActionElement();
				_this.getCurrentContentActionContainer().show();
			}
				
			// STATE
			// Set display based on state
			_this.setState( $(_this.content.bindingContainer).attr('globalState'), 
								_this.getCurrentGroup().attr('groupState'), 
								_this.getCurrentContentEntry().attr('contentState')  );
			
			//console.log(_this);
			
			if ( _this.data.currentContentType == "graphic" && _this.data.usage == "edit" )
				_this.setCurrentImageLibraryToggle();	

			_this.data.onload.apply();
			
			_this.data.graphicInitComplete = true;

			// TODO: Load the content language during initialization instead of after initialization
			_this.loadContentLanguage();
		},

		changeContentActionsContainer : function() {
			var _this = this;
			$(_this.display.contentActionsContainer).find("[class*='contentEditor_ContentActionContainer']").hide();
			_this.getCurrentContentActionContainer().show();
		},

		getSameAsParentInputs: function (currentGroupId, currentLocaleId, inputId) {
			var _this = this;
			if (_this.data.usage === "view") {
				return $('<div class="contentEditor_InfoDivContainer contentEditor_SameAsParentInputContainer contentEditor_SameAsParentInputContainer_'+currentGroupId+'_'+currentLocaleId+'" style="display: none; white-space: nowrap;">' +
							'<table class="contentEditor_SameAsTable" width="100%" cellspacing="0" cellpadding="0" border="0">' +
								'<tr>' +
									'<td width="1%"><span class="sameAsParentInputSpan"></span></td>' +
									'<td><span class="fullLineLabel">' + _this.data.text.sameAsParent + '</span></td>' +
								'</tr>' +
							'<table>' +
						'</div>');
			}
			return $('<div class="contentEditor_SameAsParentInputContainer_'+currentGroupId+'_'+currentLocaleId+'" style="display: none;">' +
						'<div class="mb-3 pb-2 border-bottom font-weight-bold">' +
							(_this.data.inheritOrCustomAll ? client_messages.text.content_for_all_languages : client_messages.text.content_for_this_language) +
						'</div>' +
						'<div class="mb-2 custom-control custom-switch sameAsParentInputSpan">' +
							'<label for="' + inputId + '" class="custom-control-label">' + _this.data.text.sameAsParent + '</label>' +
						'</div>' +
					'</div>');
		},

		getSameAsDefaultInputs: function (currentGroupId, currentLocaleId, contentLocaleId, inputId) {
				var _this = this;
				var label = (_this.data.globalContentObject ? _this.data.text.sameAsSystemDefault : _this.data.text.sameAsDefault) + ' (' + _this.data.defaultLanguage + ')';
				var display = (currentLocaleId == contentLocaleId ? '' : 'display: none;');

				if (_this.data.usage === "view") {
					return $('<div class="contentEditor_InfoDivContainer contentEditor_SameAsDefaultInputContainer contentEditor_SameAsDefaultInputContainer_'+currentGroupId+'_'+currentLocaleId+'" style="'+ display +'">' +
								'<table class="contentEditor_SameAsTable" width="100%" cellspacing="0" cellpadding="0" border="0">' +
									'<tr>' +
										'<td width="1%"><span class="sameAsDefaultInputSpan"></span></td>' +
											'<td><span class="fullLineLabel">' + label + '</span></td>' +
									'</tr>' +
								'<table>' +
							'</div>');
				}
				return $('<div class="contentEditor_SameAsDefaultInputContainer_'+currentGroupId+'_'+currentLocaleId+'" style="' + display + '">' +
							'<div class="mb-3 pb-2 border-bottom font-weight-bold">' + client_messages.text.content_for_this_language + '</div>' +
							'<div class="mb-2 custom-control custom-switch sameAsDefaultInputSpan">' +
								'<label for="' + inputId + '" class="custom-control-label">' + label + '</label>' +
							'</div>' +
						'</div>');
			},

		getSameAsDefaultBar: function (currentGroupId, currentLocaleId, contentLocaleId, inputId) {
			var _this = this;
			var label = (_this.data.globalContentObject ? _this.data.text.sameAsSystemDefault : _this.data.text.sameAsDefault) + ' (' + _this.data.defaultLanguage + ')';
			var display = (currentLocaleId == contentLocaleId ? '' : 'display: none;');

			return $('<div class="contentEditor_InfoDivContainer contentEditor_SameAsDefaultBarContainer contentEditor_SameAsDefaultBarContainer_'+currentGroupId+'_'+currentLocaleId+'" style="'+ display +'">' +
				'<table class="contentEditor_SameAsTable" width="100%" cellspacing="0" cellpadding="0" border="0">' +
				'<tr>' +
				'<td width="1%"></td>' +
				'<td><span class="fullLineLabel">' + label + '</span></td>' +
				'</tr>' +
				'<table>' +
				'</div>');
		},

		changeLanguageTab : function(localeId) {
			var _this = this;

			// Save editor content to current editor binding
			if (_this.data.usage == "edit" && _this.display.editorContent && _this.display.editorContent.length > 0) {
				var editorValue = $(_this.display.editorContent).val() ? $(_this.display.editorContent).val() : null;
				if (editorValue !== null) {
					var isDefaultLanguageEditPage = common.isFeatureEnabled('ViewDefaultLanguageEditPage') && _this.data.currentLocaleId === _this.data.defaultLocaleId && _this.data.isTranslating;
					if (!isDefaultLanguageEditPage) {
						_this.getCurrentEditorBinding().val(editorValue).trigger('change');
					}
				}
			}

			// Resolve target language
			_this.data.currentLocaleId = localeId;

			// Display: Same As Default
			_this.setCurrentSameAsDefaultInput();
			// Display: Same As Parent
			_this.setCurrentSameAsParentInput();
			
			if ( _this.data.currentContentType == "text" ) {
				// Load content from target editor binding
				if ( _this.getCurrentContentEntry().find('.embeddedVideo').length > 0 && _this.data.usage == "view" ) {
					var videoHtml = $.trim(_this.getCurrentContentEntry().find('.embeddedVideo').text());
					_this.setTextContent( videoHtml );
				} else {
					_this.setTextContent( _this.getCurrentEditorBinding().val() );
				}

				// Change toggle spellcheck language to current
				if ( _this.data.usage == "edit" ) {

					_this.setSpellcheckLang();

					var ed = tinymce.get('contentEditor_EditorTextarea_'+_this.data.instId);

					var spellCheckControl = ed.buttons.spellchecker;
					
					if(spellCheckControl != undefined){
						var currentLangLabel = null;
						for (index in _this.data.languages)
							for (localeId in _this.data.languages[index])
								if (localeId == _this.data.currentLocaleId)
									currentLangLabel = _this.data.languages[index][localeId];

						if ( spellCheckControl.menu && spellCheckControl.menu.items ) {
							var targetLanguageItemId = null;
							for ( var i=0; i < spellCheckControl.menu.items.length; i++ ) {
								var menuObject = spellCheckControl.menu.items[i];
								if ( menuObject._text == currentLangLabel )
									targetLanguageItemId = menuObject._id;
							}

							if ( targetLanguageItemId != null )
								$('#'+targetLanguageItemId).click();
						}
						
						ed.settings.spellchecker_language = _this.data.currentLocaleId;

						setTimeout(
							function() { 
								if ( $(ed.editorContainer).is(':visible') ){
									if (ed.settings.utils) {
										ed.settings.utils.focus();
									}
								}
							}
						, 250); // Fix 1363: Immediate focus causes cursor error						
					}

					_this.clearEditorUndoManager();
				}
			} else if ( _this.data.currentContentType == "graphic" ) {
				// GRAPHIC LOAD
				_this.getCurrentGraphicInputsContainer().find("[id^='conLibSelect_']").trigger('change');
				_this.getCurrentGraphicInputsContainer().find("[id^='localConLibSelect_']").trigger('change');
				$(_this.display.graphicUpdateContainer).find("[class*='contentEditor_GraphicInputsContainer']").hide();

				$(_this.display.referencingImageLibraryContainer).find("[class*='contentEditor_ReferencingImageLibraryContainer_'],[class*='contentEditor_ReferencingLocalImageLibraryContainer_']").hide();
				if ( _this.getCurrentGraphicInputsContainer().find('.useContentLibraryToggle input').is(':checked') )
					$(_this.display.referencingImageLibraryContainer).find(".contentEditor_ReferencingImageLibraryContainer_"+_this.data.currentContentGroupId+"_"+_this.data.currentLocaleId).show();
				else if ( _this.getCurrentGraphicInputsContainer().find('.useLocalContentLibraryToggle input').is(':checked') )
					$(_this.display.referencingImageLibraryContainer).find(".contentEditor_ReferencingLocalImageLibraryContainer_"+_this.data.currentContentGroupId+"_"+_this.data.currentLocaleId).show();

				var sameAsParent = _this.getCurrentSameAsParentInputContainer().find('.sameAsParentInput').is(":checked");
				if (!sameAsParent) {
					_this.getCurrentGraphicInputsContainer().show();
				}

				_this.initImageSelectMenu( _this.getCurrentGraphicInputsContainer().find("[id^='conLibSelect_']") );
				_this.initLocalImageSelectMenu( _this.getCurrentGraphicInputsContainer().find("[id^='localConLibSelect_']") );
				
				_this.setGraphicContent( _this.getCurrentGraphicFileName(), 
										_this.getCurrentGraphicFilePath(), 
										_this.getCurrentGraphicUploadDate(), 
										_this.getCurrentGraphicAppliedImageName() );
				_this.setCmsAssetInfo(_this.getCurrentGraphicCmsLastUpdateDate(), _this.getCurrentGraphicCmsLastSyncDate());
				_this.setCurrentImageLibraryToggle();
				// IMAGE LINK
				$(_this.display.graphicImageLinkContainer).find("[class*='contentEditor_GraphicImageLinkContainer']").hide();
				_this.getCurrentGraphicImageLinkContainer().show();
				_this.setGraphicImageLink(_this.getCurrentGraphicImageLink());
				// IMAGE ALT TEXT
				$(_this.display.graphicImageAltTextContainer).find("[class*='contentEditor_GraphicImageAltTextContainer']").hide();
				if($('.graphicImageLink').length > 0){
					$('.contentEditor_GraphicImageAltTextContainer').hide();
				}
				_this.getCurrentGraphicImageAltTextContainer().show();
				_this.setGraphicImageAltText(_this.getCurrentGraphicImageAltText());
				// IMAGE EXTERNAL LINK
				$(_this.display.graphicImageExtLinkContainer).find("[class*='contentEditor_GraphicImageExtLinkContainer']").hide();
				if($('.graphicImageLink').length > 0 || $('.graphicImageAltText').length > 0){
					$('.contentEditor_GraphicImageExtLinkContainer').hide();
				}
				_this.getCurrentGraphicImageExtLinkContainer().show();
				_this.setGraphicImageExtLink(_this.getCurrentGraphicImageExtLink());
				// IMAGE EXTERNAL PATH
				$(_this.display.graphicImageExtPathContainer).find("[class*='contentEditor_GraphicImageExtPathContainer']").hide();
				if($('.graphicImageLink').length > 0 || $('.graphicImageAltText').length > 0){
					$('.contentEditor_GraphicImageExtPathContainer').hide();
				}
				_this.getCurrentGraphicImageExtPathContainer().show();
				_this.setGraphicImageExtPath(_this.getCurrentGraphicImageExtPath());
			}
			_this.changeContentActionsContainer();
			
			// Refresh state
			_this.setState( $(_this.content.bindingContainer).attr('globalState'), 
					_this.getCurrentGroup().attr('groupState'), 
					_this.getCurrentContentEntry().attr('contentState')  );

			_this.updateDefaultLanguagePopOutVisibility()

		},

		loadContentLanguage: function() {
			var _this = this;
			var contentLanguage = _this.getContentLanguage();
			var contentLanguageIsInDropdown = false;
			if (_this.data.languages)
				contentLanguageIsInDropdown = _this.data.languages.filter(function(e){ return e[contentLanguage] }).length > 0;
			if (contentLanguage && contentLanguageIsInDropdown) {
				var tinymceExists = setInterval(function() {
					var container = $(_this.display.container);
					var isEditMode = _this.data.usage === "edit";
					var isViewMode = _this.data.usage === "view";

					var hasTinyMCE = container.find('.mce-tinymce').length > 0;
					var hasStaticTextContent = container.find('.contentEditor_StaticTextContentContainer').length > 0;
					var hasStaticGraphicContent = container.find('.contentEditor_StaticGraphicContentContainer').length > 0;

					var hasGraphicImageLink = _this.getCurrentGraphicImageLinkContainer().find('.mce-tinymce').length > 0;
					var hasGraphicImageAltText = _this.getCurrentGraphicImageAltTextContainer().find('.mce-tinymce').length > 0;
					var hasGraphicImageExtPath = _this.getCurrentGraphicImageExtPathContainer().find('.mce-tinymce').length > 0;
					var hasGraphicImageExtLink = _this.getCurrentGraphicImageExtLinkContainer().find('.mce-tinymce').length > 0;

					var hasRequiredGraphicContent = hasGraphicImageLink || hasGraphicImageAltText || hasGraphicImageExtPath || hasGraphicImageExtLink;

					if (
						(isEditMode && hasTinyMCE) ||
						(isViewMode && hasStaticTextContent) ||
						(hasStaticGraphicContent && hasRequiredGraphicContent)
					) {
						clearInterval(tinymceExists);
						_this.changeLanguageTab(contentLanguage);
					}
				}, 500);
			}
			if(localStorage.getItem("back_forward")) {
				localStorage.removeItem("back_forward");
			}
		},

		clearEditorUndoManager : function() {
			var _this = this;
			
			if ( _this.data.usage == "edit" && $(_this.display.container).find('.mce-tinymce').length > 0 ) {
				var editor = tinyMCE.get('contentEditor_EditorTextarea_' + _this.data.instId);
				editor.undoManager.clear();
			}
		},

		editContentFileAction : function() {
			var _this = this;

			var fileToken = "";
			if (  _this.getCurrentGraphicInputsContainer().find('.graphicFileInputTD input').val() != "" )
				fileToken = "sandbox_file_fid" + _this.getCurrentGraphicInputsContainer().find('.graphicFileInputTD input').val() + ".docx";
			else
				fileToken = "content_file_fid" + _this.getCurrentGraphicInputsContainer().find('.graphicFileInput').attr('contentId') + ".docx";
			
			var stampDate = new Date();
			var path = context + "/file_serve/" + _this.data.nTk + "/" + _this.data.uTk + "/" + fileToken + "?cacheStamp=" + (stampDate.getTime());

			_this.webdavFileOpen( path );			
		},

		storeContentLanguage : function(language) {
			var _this = this;
			var url = new URLSearchParams(window.location.search);
			var contentObjectId = url.get('contentObjectId');
			var contentLanguages = JSON.parse(localStorage.getItem(_this.data.contentLanguageKey)) || [];
			var index = contentLanguages.findIndex(e => e.contentObjectId == contentObjectId);
			if (index !== -1) {
				contentLanguages[index].language = language;
			} else {
				contentLanguages.push({contentObjectId,language});
			}
			_this.setContentLanguage(JSON.stringify(contentLanguages));

		},

		setContentLanguage: function(value){
			var _this = this;
			localStorage.setItem(_this.data.contentLanguageKey, value);
		},

		getContentLanguage : function() {
			var _this = this;
			var isUserGoingBack = false;
			var performance = window.parent ? window.parent.performance : window.performance;
			if (performance){
				var navigationType = performance.getEntriesByType("navigation")[0].type
				if(navigationType == "back_forward" || localStorage.getItem("back_forward")) {
					isUserGoingBack = true;
				}
			}

			var searchParams = new URLSearchParams(window.location.search);
			var contentObjectId = searchParams.get('contentObjectId');
			var languages = JSON.parse(localStorage.getItem(_this.data.contentLanguageKey)) || [];

			var findContentLanguage = function (objectId) {
				return languages.slice().reverse().find(function (e) {
					return e.contentObjectId === objectId;
				});
			};

			var contentLanguage = findContentLanguage(contentObjectId);

			if (!isUserGoingBack) {
				var referrerPage = window.parent ? window.parent.document.referrer : window.document.referrer;
				if (referrerPage) {
					var referrerPageContentObjectId = new URL(referrerPage).searchParams.get('contentObjectId');
					var referrerContentLanguage = findContentLanguage(referrerPageContentObjectId);

					if (referrerContentLanguage) {
						contentLanguage = referrerContentLanguage;
						_this.storeContentLanguage(referrerContentLanguage.language);
					}
				}
			}
			var validContentLanguage = contentLanguage && _this.data.languages && _this.data.languages.some(function (e) {
				return e[contentLanguage.language];
			});

			if (validContentLanguage) {
				return contentLanguage.language;
			}

			var localeId = _this.data.currentLocaleId || _this.data.defaultLocaleId || "";
			if (localeId) {
				_this.storeContentLanguage(localeId);
			}
			return localeId;

		},

		initLanguageSelect : function () {

			var _this = this;

			if (_this.data.languages != null) {
				var $languageSelect;

				if (_this.data.usage == "view") {
                    var languageSelectionTemplate = '<div class="input-group w-50 mb-4">' +
                        '<div class="input-group-prepend">' +
                        '<label class="input-group-text" for="contentLanguageSelect' + _this.data.instId + '">' +
                        client_messages.content_editor.content_language +
                        '</label>' +
                        '</div>' +
                        '<select class="custom-select" id="contentLanguageSelect' + _this.data.instId + '"></select>' +
                        '</div>';
					$languageSelect = $(languageSelectionTemplate);

				} else {
					$languageSelect = $("#languageSelectionSection");
				}

				var $select = $languageSelect.find('.custom-select');
				$select.empty();

				$select.on('change', function () {
					_this.storeContentLanguage($(this).val());
					_this.changeLanguageTab($(this).val());
					_this.updateContentInfo();

					var $container = $(_this.display.container);
					var editorId = 'contentEditor_EditorTextarea_' + _this.data.instId;
					var $tinymce = $container.find('.mce-tinymce');

					if ($tinymce.length > 0 && $tinymce.is(':visible')) {
						var ed = tinymce.get(editorId);
						if ($.isFunction(ed.settings.styles.fn.refresh_applied_text_style_classes)) {
							ed.settings.styles.fn.refresh_applied_text_style_classes();
						}
						if ($.isFunction(ed.settings.styles.fn.refresh_applied_paragraph_style_classes)) {
							ed.settings.styles.fn.refresh_applied_paragraph_style_classes();
						}
					}

				});

				if(!_this.data.defaultLanguage || _this.data.defaultLanguage == "") {

					_this.data.defaultLanguage = "";

					for (index in _this.data.languages) {

						for (localeId in _this.data.languages[index]) {

							if (localeId == _this.data.defaultLocaleId)
								_this.data.defaultLanguage = _this.data.languages[index][_this.data.defaultLocaleId];

						}

					}

				}

				var selectedContentLanguage = _this.data.currentLocaleId;
				var contentLanguage = _this.getContentLanguage();
				if (contentLanguage) {
					selectedContentLanguage = contentLanguage;
					_this.data.currentLocaleId = selectedContentLanguage;
 				}

				// Default Language Ordered as first language
				for (index in _this.data.languages) {

					for (localeId in _this.data.languages[index]) {

						var isSelected = localeId == selectedContentLanguage;
						var text =_this.data.languages[index][localeId];
						var defaultText = localeId === _this.data.defaultLocaleId ? ' - Default' : '';
						$option = $('<option value="' + localeId + '">' + text + defaultText + '</option>');

						$option.prop('selected', isSelected);
						$select.append($option);

					}

				}

				_this.data.hasDefaultLanguage = _this.data.languages && _this.data.languages.filter(function (item) { return item[_this.data.defaultLocaleId] }).length > 0;

				var defaultLanguagePopOutTemplate =
						'<div class="input-group-append d-none z-index-1" title="' + client_messages.text.snap_out_default_language + '">' +
						'<button type="button" class="btn btn-outline-light text-body">' +
						'<span class="sr-only">' + client_messages.text.snap_out_default_language + '</span>' +
						'<i class="far fa-external-link-square-alt fa-lg" aria-hidden="true"></i>' +
						'</button>' +
						'</div>',
					$defaultLanguagePopOut = $(defaultLanguagePopOutTemplate);

				_this.defaultLanguagePopOutBtn = $defaultLanguagePopOut;

				$defaultLanguagePopOut.children('.btn').on('click', function () {
					var $this = $(this);
					$this.prop("disabled", true);
					_this.snapOutDefaultLanguage($this.position());
				});

				if ( _this.data.usage == "view" ) {
					$languageSelect.append($defaultLanguagePopOut);
					$(_this.display.container).prepend($languageSelect);
				} else {
					$languageSelect.find(".input-group").append($defaultLanguagePopOut);
					$languageSelect.removeClass("d-none");
				}

				_this.updateDefaultLanguagePopOutVisibility();
			}

		},

		updateContentInfo : function () {
			var _this = this;
			var $container = $(_this.display.container);
			var $contentInfo = _this.getCurrentContentEntry().find('.contentInfo');

			if ( _this.data.usage == "view" ) {
				$container.find('.contentInfo').remove();

				if ($contentInfo.length) {
					$container.prepend($contentInfo.prop('outerHTML'));
				}
			}
		},
		
		getCurrentContentActionContainer : function() {
			var _this = this;
			return $(_this.display.contentActionsContainer).find('.contentEditor_ContentActionContainer_'+_this.data.currentLocaleId);
		},
		
		getCurrentContentEntry : function () {
			var _this = this;
			return $( _this.getCurrentGroup() ).find("[localeId='"+_this.data.currentLocaleId+"']");
		},

		getAllContentEntry : function () {
			var _this = this;
			return $( _this.getCurrentGroup() ).find("[localeId]");
		},
		
		getCurrentEditorBinding : function () {
			var _this = this;
			return $( _this.getCurrentContentEntry() ).find("textarea");
		},
		
		getCurrentGraphicInputsContainer : function () {
			var _this = this;
			return $(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicInputsContainer_'+_this.data.currentContentGroupId+'_'+_this.data.currentLocaleId);
		},
		
		getCurrentGraphicImageLinkContainer : function () {
			var _this = this;
			return $(_this.display.graphicImageLinkContainer).find('.contentEditor_GraphicImageLinkContainer_'+_this.data.currentContentGroupId+'_'+_this.data.currentLocaleId);
		},
		
		getCurrentGraphicImageAltTextContainer : function () {
			var _this = this;
			return $(_this.display.graphicImageAltTextContainer).find('.contentEditor_GraphicImageAltTextContainer_'+_this.data.currentContentGroupId+'_'+_this.data.currentLocaleId);
		},

		getCurrentGraphicImageExtLinkContainer : function () {
			var _this = this;
			return $(_this.display.graphicImageExtLinkContainer).find('.contentEditor_GraphicImageExtLinkContainer_'+_this.data.currentContentGroupId+'_'+_this.data.currentLocaleId);
		},

		getCurrentGraphicImageExtPathContainer : function () {
			var _this = this;
			return $(_this.display.graphicImageExtPathContainer).find('.contentEditor_GraphicImageExtPathContainer_'+_this.data.currentContentGroupId+'_'+_this.data.currentLocaleId);
		},

		getCurrentSameAsParentInputContainer : function () {
			var _this = this;
			return $(_this.display.sameAsParentContainer).find('.contentEditor_SameAsParentInputContainer_'+_this.data.currentContentGroupId+'_'+_this.data.currentLocaleId);
		},

		getCurrentSameAsDefaultInputContainer : function () {
			var _this = this;
			return $(_this.display.sameAsDefaultContainer).find('.contentEditor_SameAsDefaultInputContainer_'+_this.data.currentContentGroupId+'_'+_this.data.currentLocaleId);
		},
		
		getCurrentGraphicAppliedImageName : function () {
			var _this = this;
			return _this.getCurrentGraphicInputsContainer().find('.graphicAppliedImageName input').val();
		},
		
		getCurrentGraphicImageLink : function () {
			var _this = this;
			return _this.getCurrentGraphicImageLinkContainer().find('.graphicImageLink .imageLinkInput').val();
		},
		
		getCurrentGraphicImageAltText : function () {
			var _this = this;
			return _this.getCurrentGraphicImageAltTextContainer().find('.graphicImageAltText .imageAltTextInput').val();
		},

		getCurrentGraphicImageExtLink : function () {
			var _this = this;
			return _this.getCurrentGraphicImageExtLinkContainer().find('.graphicImageExtLink .imageExtLinkInput').val();
		},

		getCurrentGraphicImageExtPath : function () {
			var _this = this;
			return _this.getCurrentGraphicImageExtPathContainer().find('.graphicImageExtPath .imageExtPathInput').val();
		},
		
		getCurrentGraphicFilePath : function () {
			var _this = this;
			return _this.getCurrentGraphicInputsContainer().find('.graphicFileInput').attr('imgPath');
		},
		
		getCurrentGraphicFileName : function () {
			var _this = this;
			return _this.getCurrentGraphicInputsContainer().find('.graphicFileInput').attr('imgName');
		},
		
		getCurrentGraphicUploadDate : function () {
			var _this = this;
			return _this.getCurrentGraphicInputsContainer().find('.graphicFileInput').attr('uploadDate');
		},
		
		getCurrentGraphicCmsLastUpdateDate : function () {
			var _this = this;
			return _this.getCurrentGraphicInputsContainer().find('.cmsAssetInfo').attr('cmsLastUpdateDate');
		},
		
		getCurrentGraphicCmsLastSyncDate : function () {
			var _this = this;
			return _this.getCurrentGraphicInputsContainer().find('.cmsAssetInfo').attr('cmsLastSyncDate');
		},
		
		getCurrentGroup : function () {
			var _this = this;
			return $(_this.content.bindingContainer).find("[groupId='"+_this.data.currentContentGroupId+"']");
		},
		
		initImageSelectMenu : function(menu) {
			var _this = this;
			_this.generateImageSelectMenu(menu, "imageLibrary");
		},
		
		initLocalImageSelectMenu : function(menu) {
			var _this = this;
			_this.generateImageSelectMenu(menu, "localImageLibrary");
		},
		
		initTextContentActions : function(containerOverride) {
			var _this = this;

			var contentContainer = containerOverride != undefined && $(containerOverride).length != 0 ? containerOverride : _this.display.staticTextContentContainer;

			var targetDoc = $(contentContainer)[0].ownerDocument;
			var targetWindow = targetDoc.defaultView || targetDoc.parentWindow;

			// VAR Tags: Init popups
			$(contentContainer).find('.contentContainer').find('.staticContentItem').each( function() {
				_this.initStaticContentItem(this);
			});
			
			// SMART CANVAS: Adjust canvas size (may change after initial creation
			$(contentContainer).find('.contentContainer .mceDraggable > .mceStaticContainer').each( function() {
				var maxX = 0, maxY = 0;
				$(this).find('.mceLockedDraggable').each( function() {
					// Don't evaluate empty containers
					if ( jQuery.trim($(this).text()).length == 0 && $(this).children().length < 2 && $(this).find('table,.mceLineElement,img').length == 0 )
						return;
					
					var left = parseFloat($(this).css('left').replace('px',''));
					var top = parseFloat($(this).css('top').replace('px',''));
					var width = parseFloat($(this).css('width').replace('px',''));
					var height = parseFloat($(this).css('height').replace('px',''));
					
					if ( left + width > maxX )
						maxX = left + width;
					if ( top + height > maxY )
						maxY = top + height;
				});
				
				$(this).closest('.mceDraggable').css({width: maxX + 'px', height: maxY + 'px'});
			});
			
			$(contentContainer)
				.find('.contentContainer .contentTargetingIcon,.contentContainer .repeatingRowIcon,.contentContainer .repeatingHeaderIcon,.contentContainer .fixedHeaderIcon,.contentContainer .repeatingFooterIcon,.contentContainer .fixedFooterIcon,.contentContainer .listPropertiesIcon')
				.each( function(){ $(this).parent().remove(); } );
			
			// FORMS: Input icon indicators
			$(contentContainer).find('.mceFormEleValue,.mceFormEleAltText,.mceFormEleValue [type=15],.mceFormEleAltText [type=15],.mceFormEleID [type=15]').each( function() {
				
				var formEle = $(this).closest('.mceTextFieldElement,.mceCheckboxElement,.mceRadioElement,.mceMenuElement,.mceSubmitButtonElement');
				$(formEle).find('.mceFormElementIconContainer').remove();
				
				if ( $(formEle).is('.mceTextFieldElement,.mceMenuElement,.mceSubmitButtonElement') ) {
					var iconArray = new Array();
					if ( $(formEle).find('.mceFormEleValue').length != 0 ) {
						iconArray[iconArray.length] = 
							"<div class=\"mceStaticElement prefillIndicatorIcon\" contenteditable=\"false\">" +
								"<i class=\"far fa-font\" style=\"position: relative; top: 0px; left: -3px;\"></i>" +
							"</div>";
					}
					if ( $(formEle).find('.mceFormEleAltText').length != 0 ) {
						iconArray[iconArray.length] = 
							"<div class=\"mceStaticElement altTextIndicatorIcon\" contenteditable=\"false\">" +
								"<i class=\"far fa-comment\" style=\"position: relative; top: 0px; left: 0px;\"></i>" +
							"</div>";
					}
					if ( $(formEle).find('.mceFormEleValue [type=15],.mceFormEleAltText [type=15],.mceFormEleID [type=15]').length != 0 ) {
						iconArray[iconArray.length] = 
							"<div class=\"mceStaticElement hybridAttributesIndicatorIcon\" contenteditable=\"false\">" +
								"<i class=\"far fa-chevron-circle-down\" style=\"position: relative; top: 0px; left: 0px;\"></i>" +
							"</div>";
					}
					
					if ( iconArray.length > 0 ) {
						var containerWidth = iconArray.length * 20;
						$(formEle).find('.mceFormInput')
							.prepend("<div class=\"mceStaticElement mceFormElementIconContainer\" contenteditable=\"false\" style=\"margin-right: 4px; white-space: nowrap; right: 0px; top: 0px; width: " + containerWidth + "px; height: 0px; position: absolute;\">" +
										iconArray.join("") +
									 "</div>");
					}
				}
				
			});
			
			// FORMS: Text field value
			$(contentContainer).find('.contentContainer .mceFormEleValue').each( function() {

				if ( $(this).closest('.mceTextFieldElement').find('.prefillIndicatorIcon').length == 0 ) {

					var valueHTML = $(this).html();
					$(this).closest('.mceTextFieldElement').find('.prefillIndicatorIcon').popupFactory({
						title				: client_messages.content_editor.text_field_value,
						width				: 225,
						offsetWindow		: targetWindow,
						fnSetContent		: function(o) {
												var popupHTML = "<div align=\"left\" style=\"font-size: 11px; margin: 8px 16px;\">" +
																	valueHTML +
																"</div>";
												return 	$(popupHTML);
											  }
					});

				}

			});
			// FORMS: Text field alt text
			$(contentContainer).find('.contentContainer .mceFormEleAltText').each( function() {

				if ( $(this).closest('.mceTextFieldElement').find('.altTextIndicatorIcon').length == 0 ) {

					var valueHTML = $(this).html();
					$(this).closest('.mceTextFieldElement').find('.altTextIndicatorIcon').popupFactory({
						title				: client_messages.content_editor.alt_text,
						width				: 225,
						offsetWindow		: targetWindow,
						fnSetContent		: function(o) {
												var popupHTML = "<div align=\"left\" style=\"font-size: 11px; margin: 8px 16px;\">" +
																	valueHTML +
																"</div>";
												return 	$(popupHTML);
											  }
					});

				}

			});
			// TABLE ROW TARGETING
			$(contentContainer).find('.contentContainer [content_targeting_id]').each( function() {

				if (
					($(this).attr('mce_mp_target_event_init') === undefined || $(this).attr('mce_mp_target_event_init') === "true") ||
					($(this).find('.contentTargetingIcon').length === 0 &&
					$(this).find('.mceInlineTargeting').filter(function() {
						return $(this).closest('.mceInlineTargeting').get(0) === this;
					}).length === 0)
				) {

					// Clean stale icon (re-init popupFactory)
					$(this).find('.contentTargetingIcon').parent().remove();
					$(this).children('.mceInlineTargeting').remove();

					var id = $(this).attr('content_targeting_id');

					var isTableRowTargeting = $(this).is('tr');

					var appendCell 		= isTableRowTargeting ? $(this).find('td:first') : $(this);
					var tablePadding 	= isTableRowTargeting && $(this).closest('table').attr('cellpadding') ? parseInt( $(this).closest('table').attr('cellpadding')) : 0;
					var cellPaddingLeft = isTableRowTargeting ? parseInt($(appendCell).css('padding-left').replace('px','')) : 0;
					var cellPaddingTop 	= isTableRowTargeting ?  parseInt($(appendCell).css('padding-top').replace('px','')) : 0;

					var leftPadding = topPadding = 0;
					if ( tablePadding > 0 || cellPaddingLeft > 0 )
						leftPadding = -1 * (tablePadding + cellPaddingLeft - 1);
					if ( tablePadding > 0 || cellPaddingTop > 0 )
						topPadding = -1 * (tablePadding + cellPaddingTop - 1);

					if ( isTableRowTargeting ) {
						$(appendCell)
							.prepend("<div class=\"mceStaticElement\" contenteditable=\"false\" style=\"margin-left: " + leftPadding + "px; margin-top: " + topPadding + "px; width: 0px; height: 0px; position: absolute;\">" +
								"<div class=\"mceStaticElement contentTargetingIcon\" contenteditable=\"false\"></div>" +
								"</div>");
					} else {
						$(appendCell)
							.prepend("<i class=\"far fa-bullseye mp-ico-targeting fa-mp-ed-ico mceInlineTargeting\" contenteditable=\"false\" style=\"cursor: default;\">&nbsp;</i>");
					}
					
					var stampDate 	= new Date();
					$(this).find(isTableRowTargeting ? 'td:first .contentTargetingIcon' : '.mceInlineTargeting:first').popupFactory({
						title					: client_messages.title.targeting,
						popupLocation			: "right",
						offsetWindow			: targetWindow,
						width					: 250,
						asyncDataType			: "xml",
						asyncSetContentURL		: context + "/getTargeting.form?type=summary&contentTargetingId=" + id + "&cacheStamp=" + (stampDate.getTime()),
						asyncSetContentHandler	: function(o, data) {
													if ($(data).find("content").length > 0)
														return "<div align=\"left\" style=\"padding: 6px 12px; font-size: 10px;\">" +
																	$(data).find("content").text() +
																"</div>";
													else 
														return 	"<div>Error: " + client_messages.text.bad_request_for_targeting_summary + "</div>";
												  }
					});
					
				}

			});

			// TABLE COLUMN TARGETING
			$(contentContainer).find('.contentContainer [column_targeting_ids]').each( function() {

				if (
					($(this).attr('mce_mp_target_event_init') === undefined || $(this).attr('mce_mp_target_event_init') === "true") ||
					($(this).find('.columnTargetingIcon').length === 0 )
				) {

					$(this).find('.columnTargetingIcon').parent().remove();

					var columnTargetingIds = $(this).attr('column_targeting_ids');
					columnTargetingIds = columnTargetingIds ? columnTargetingIds.split(':') : [];

					for (var i = 0; i < columnTargetingIds.length; i++) {
						var id = columnTargetingIds[i];
						var appendCell = $(this).find('td').eq(i);

						if (id !== "0" && $(appendCell).find('.columnTargetingIcon').length == 0) {
							$(appendCell).prepend("<div class=\"mceStaticElement\" contenteditable=\"false\" style=\"position: absolute; left: 50%; top: -11px; margin-left: 10px;\">" +
								"<div class=\"mceStaticElement columnTargetingIcon\" contenteditable=\"false\"></div>" +
								"</div>");

							var stampDate = new Date();
							$(appendCell).find('.columnTargetingIcon').popupFactory({
								title: client_messages.title.targeting,
								popupLocation: "bottom",
								offsetWindow: targetWindow,
								width: 250,
								asyncDataType: "xml",
								asyncSetContentURL: context + "/getTargeting.form?type=summary&contentTargetingId=" + id + "&cacheStamp=" + (stampDate.getTime()),
								asyncSetContentHandler: function (o, data) {
									if ($(data).find("content").length > 0)
										return "<div align=\"left\" style=\"padding: 6px 12px; font-size: 10px;\">" +
											$(data).find("content").text() +
											"</div>";
									else
										return "<div>Error: " + client_messages.text.bad_request_for_targeting_summary + "</div>";
								}
							});
						}
					}

				}

			});

			// TABLE REPEATING HEADERS
			$(contentContainer).find(".contentContainer tr[row_type]").each( function() {
				
				var isRepeatingRow = $(this).attr('row_type') == 'repeating_row' && $(this).find('.repeatingRowIcon').length == 0;
				var isRepeatingHeader = ($(this).attr('row_type') == 'repeating_header' || $(this).attr('row_type') == 'header') && $(this).find('.repeatingHeaderIcon').length == 0;
				var isFixedHeader = $(this).attr('row_type') == 'fixed_header' && $(this).find('.fixedHeaderIcon').length == 0;
				var isRepeatingFooter = ($(this).attr('row_type') == 'repeating_footer' || $(this).attr('row_type') == 'footer') && $(this).find('.repeatingFooterIcon').length == 0;
				var isFixedFooter = $(this).attr('row_type') == 'fixed_footer' && $(this).find('.fixedFooterIcon').length == 0;
				
				if ( isRepeatingRow || isRepeatingHeader || isFixedHeader || isRepeatingFooter || isFixedFooter ) {
				
					var appendCell 		= $(this).find('td:first');
					var tablePadding 	= $(this).closest('table').attr('cellpadding') ? parseInt( $(this).closest('table').attr('cellpadding')) : 0;
					var cellPaddingLeft = parseInt($(appendCell).css('padding-left').replace('px',''));
					var cellPaddingTop 	= parseInt($(appendCell).css('padding-top').replace('px',''));
					
					var leftPadding = topPadding = 0;
					if ( tablePadding > 0 || cellPaddingLeft > 0 )
						leftPadding = -1 * (tablePadding + cellPaddingLeft - 1);
					if ( tablePadding > 0 || cellPaddingTop > 0 )
						topPadding = -1 * (tablePadding + cellPaddingTop - 1);
					
					if ( isRepeatingRow ) {

						$(appendCell)
							.prepend("<div class=\"mceStaticElement\" contenteditable=\"false\" style=\"margin-left: " + leftPadding + "px; margin-top: " + topPadding + "px; width: 0px; height: 0px; position: absolute;\">" +
										"<div class=\"mceStaticElement repeatingRowIcon\" contenteditable=\"false\"></div>" +
									"</div>");
						
						var row = $(this);
						$(row).find('td:first .repeatingRowIcon').popupFactory({
							title					: client_messages.title.repeating_row,
							popupLocation			: "right",
							offsetWindow			: targetWindow,
							width					: 215,
							fnSetContent			: function(o) {
								
								var keepTogetherCount = !$(row).attr('keep_together_threshold') ? 0 : parseInt( $(row).attr('keep_together_threshold') );
				        		var keepTogetherHTML  = ""; 
				        		if ( keepTogetherCount > 0 ) {
				        			keepTogetherHTML  +="<div style=\"margin-bottom: 6px;\">" +
															client_messages.text.group_keep_together_threshold + ": " + keepTogetherCount +
														"</div>";
				        		}
								
				            	var cellIndex = 1;
				            	var repeatingCellsHTML = "<table cellspacing=\"0\" cellpadding=\"0\" border=\"0\">";
				            	
				            	$(row).find('td,th').each( function() {
				            		var repeatingCell 	= $(this).attr('repeat_cell') == "true";
				            		var repeatingOnFlow = $(this).attr('on_flow') == "repeat";
				            		repeatingCellsHTML += 	"<tr><td style=\"padding: 3px; padding-left: 0px;\">" +
				            									client_messages.text.table_cell + " " + cellIndex++ + ":" +
				            								"</td>" + 
				            								"<td style=\"padding: 3px 3px 3px 5px;\">" +
				            									(repeatingCell ? client_messages.text.repeating : client_messages.text.fixed) +
				            									(repeatingOnFlow ? " (" + client_messages.text.repeat_on_flow + ")" : "") +
				            								"</td></tr>";
				            	});
				            	repeatingCellsHTML += "</table>";
				            	
				    			var popupEle = 	$(	"<div id=\"repeatingCellsContainer\">" +
					    								"<div style=\"margin: 8px 16px;\" align=\"left\">" +
					    									keepTogetherHTML + 
					    									repeatingCellsHTML +
														"</div>" +
													"</div>");
	
								return popupEle;
							}
						});
					
					} else if ( isRepeatingHeader ) {
						
						var omitInitial = $(this).closest('table').attr('omit_initial_header') == "true" && $(this).prevAll('tr').length == 0;
						var repeatType = $(this).attr('repeat') != undefined ? $(this).attr('repeat') : null;
						var tooltipTxt = (omitInitial ? client_messages.title.row_repeated_on_flow_omit_initial : client_messages.title.header_row_repeated_on_flow);
						if ( repeatType != null ) {
							if ( repeatType == 'first')
								tooltipTxt = client_messages.content_editor.header_first_only;
							else if ( repeatType == 'notfirst')
								tooltipTxt = client_messages.content_editor.header_not_first;
							else if ( repeatType == 'all')
								tooltipTxt = client_messages.content_editor.header_all;
						}

						$(appendCell)
						.prepend("<div class=\"mceStaticElement\" data-toggle=\"tooltip\" contenteditable=\"false\" style=\"margin-left: " + leftPadding + "px; margin-top: " + topPadding + "px; width: 0px; height: 0px; position: absolute;\" " +
									"title=\"" + tooltipTxt +  "\">" +
									"<div class=\"mceStaticElement repeatingHeaderIcon\" contenteditable=\"false\"></div>" +
								"</div>");
						$(appendCell).find('[data-toggle=tooltip]').tooltip({'placement':'right'});

					} else if ( isFixedHeader || isFixedFooter ) {
						
						var txt = isFixedHeader ? client_messages.title.header_row_fixed : client_messages.title.footer_row_fixed;
						var icon = isFixedHeader ? 'fixedHeaderIcon' : 'fixedFooterIcon';
						$(appendCell)
						.prepend("<div class=\"mceStaticElement\" data-toggle=\"tooltip\" contenteditable=\"false\" style=\"margin-left: " + leftPadding + "px; margin-top: " + topPadding + "px; width: 0px; height: 0px; position: absolute;\" " +
									"title=\"" + txt +  "\">" +
									"<div class=\"mceStaticElement " + icon + "\" contenteditable=\"false\"></div>" +
								"</div>");
						$(appendCell).find('[data-toggle=tooltip]').tooltip({'placement':'right'});

					} else if ( isRepeatingFooter ) {

						var omitLast = $(this).closest('table').attr('omit_last_footer') == "true" && $(this).nextAll('tr').length == 0;
						var repeatType = $(this).attr('repeat') != undefined ? $(this).attr('repeat') : null;
						var tooltipTxt = (omitLast ? client_messages.title.row_repeated_on_flow_omit_last : client_messages.title.footer_row_repeated_on_flow);
						if ( repeatType != null ) {
							if ( repeatType == 'last')
								tooltipTxt = client_messages.content_editor.footer_last_only;
							else if ( repeatType == 'notlast')
								tooltipTxt = client_messages.content_editor.footer_not_last;
							else if ( repeatType == 'all')
								tooltipTxt = client_messages.content_editor.footer_all;
						}

						$(appendCell)
							.prepend("<div class=\"mceStaticElement\" data-toggle=\"tooltip\" contenteditable=\"false\" style=\"margin-left: " + leftPadding + "px; margin-top: " + topPadding + "px; width: 0px; height: 0px; position: absolute;\" " +
								"title=\"" + tooltipTxt +  "\">" +
								"<div class=\"mceStaticElement repeatingFooterIcon\" contenteditable=\"false\"></div>" +
								"</div>");
						$(appendCell).find('[data-toggle=tooltip]').tooltip({'placement':'right'});

					}
				
				}
				
			});
			
			// LIST PROPERTIES
			$(contentContainer).find(".contentContainer ol[start],.contentContainer ol[continue]").each( function() {

				var isStartNumbering = $(this).attr('start') != undefined && $(this).attr('continue') == undefined && $(this).find('.listPropertiesIcon').length == 0;
				var isContinueNumbering = $(this).attr('continue') != undefined && $(this).find('.listPropertiesIcon').length == 0;
				
				if ( isStartNumbering || isContinueNumbering ) {
				
					var appendEle 		= $(this).find('li:first');
					var elePaddingLeft = parseFloat($(appendEle).css('padding-left').replace('px',''));
					var elePaddingTop 	= parseFloat($(appendEle).css('padding-top').replace('px',''));
					
					var leftPadding = topPadding = 0;
					if ( elePaddingLeft > 0 )
						leftPadding = -1 * (elePaddingLeft - 1);
					if ( elePaddingTop > 0 )
						topPadding = -1 * (elePaddingTop - 1);
					var leftOffset = -62;

					$(appendEle)
						.prepend("<div class=\"mceStaticElement mceListPropertiesContainer\" contenteditable=\"false\" style=\"margin-left: " + leftPadding + "px; margin-top: " + topPadding + "px; width: 0px; height: 0px; position: absolute;\">" +
								"<div class=\"mceStaticElement listPropertiesIcon\" contenteditable=\"false\" style=\"left: " + leftOffset + "px\" align=\"center\"><i class=\"fa fa-list-ol\" style=\"position: relative; top: 1px; left: 1px;\">&nbsp;</i></div>" +
								"</div>");
					
					var listEle = $(this);
					$(listEle).children().find('.listPropertiesIcon').popupFactory({
						title				: client_messages.title.list_properties,
						width				: 225,
						offsetWindow		: targetWindow,
						fnSetContent		: function(o) {
												var tagHTML = 	"<div align=\"left\" style=\"font-size: 11px; margin: 8px 16px;\">";

												if ( isStartNumbering )
													tagHTML += 		client_messages.text.starting_number + ": " + $(listEle).attr('start');
												else if ( isContinueNumbering )
													tagHTML += 		client_messages.text.continue_numbering;

												tagHTML +=		"</div>";
												
												var returnTagsEle = $(tagHTML);

												return 	returnTagsEle;
											  }
					});

				}
				
			});
			
		},
		
		initStaticContentItem: function(staticContentItem) {
			var _this = this;

			var targetDoc = $(staticContentItem)[0].ownerDocument;
			var targetWindow = targetDoc.defaultView || targetDoc.parentWindow;

			// Smart Text:  Init content popup
			if ( $(staticContentItem).attr('type') == "4" || 
				 $(staticContentItem).attr('type') == "10" ) {

                $(staticContentItem).smartTextContentViewer('init', targetWindow);
                
				$(staticContentItem).contentPopup({
					contentType				: $(staticContentItem).attr('type') == "4" ?
											  'embeddedText' : 'localContentRendering',
					offsetWindow			: targetWindow,
					contentItemId			: $(staticContentItem).attr('id'),
					objectDna				: $(staticContentItem).attr('dna'),
					fnExtServerParams		: function(o) {
												var obj = new Array();
												obj[obj.length] = {"name" : "context", "value" : _this.data.isTestContext ? "test" : "default"};
												return obj;
											},
					fnBeforeContentRequest	: function(o) {
												o.data.contentLocaleId = _this.data.currentLocaleId;
											}
				});
			} else if ( $(staticContentItem).attr('type') == "5" ) {



                $(staticContentItem).smartTextContentViewer('init', targetWindow);

				// Compound Smart Text: Init popup
				$(staticContentItem).smartTextTagAttrManager({
							objectDna				: $(staticContentItem).attr('dna'),
							mode					: "view",
							offsetWindow			: targetWindow,
				});
			} else if ( $(staticContentItem).attr('type') == "1" ) {
				// Variable: Init popup
				var localeId = _this.getCurrentContentEntry() && _this.getCurrentContentEntry().attr('contentState') == 'sameAsDefault' ? _this.data.defaultLocaleId : _this.data.currentLocaleId;
				$(staticContentItem).varTagAttrManager({
							objectDna				: $(staticContentItem).attr('dna'),
							localeId				: localeId,
							mode					: "view",
							offsetWindow			: targetWindow,
				});
			} else if ( $(staticContentItem).attr('type') == "2" || $(staticContentItem).attr('type') == "3" ) {
				// Constants: Init popup
				$(staticContentItem).popupFactory({
							title					: client_messages.title.whats_this,
							fnSetContent			: function(o) {
														return "<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\"><b><i>" + client_messages.text.constant + "</i></b></div>";
													  },
							offsetWindow			: targetWindow,
				});
			} else if ( $(staticContentItem).attr('type') == "17" ) {
				
				// Placeholder: Init popup
				$(staticContentItem).placeholderTagAttrManager({
							objectDna				: $(staticContentItem).attr('dna'),
							mode					: "view",
							offsetWindow			: targetWindow,
				});
			}
			
			if ( ($(staticContentItem).attr('type') == "10" || $(staticContentItem).attr('type') == "4" ||  $(staticContentItem).attr('type') == "5")) {
				// Shared Content: Init fast edit

				var location = parent.document.location.href;

				if(location.indexOf('fastView=true') < 0 && location.indexOf('fastEdit=true') < 0) {
					$(staticContentItem).sharedTextFastEdit();

					// To avoid overlaps with targeting icons
					$(staticContentItem).hover(function(){
						var containerWidth = $(this).closest('.contentEditor_StaticTextContentContainer').width();
						var positionLeft = $(this).position().left;
						var elementWidth = $(this).width();
						if (positionLeft <= 60 && elementWidth <= containerWidth) {
							$(this).addClass('leftPositioned');
						}
					});
				}

			}
			
			$(staticContentItem).find(".mceVarTargeting").each( function() {
				var targetedVar = $(this).closest('.staticContentItem,.innerStaticContentItem');
				
				var params = "";
				if ( $(targetedVar).is("[type='10'],[type='4'],[type='5']") )
					params += "&objectDna=" + $(targetedVar).attr('dna');
				
				var stampDate = new Date();
				params += "&cacheStamp=" + (stampDate.getTime());

				$(this).popupFactory({
					title					: client_messages.title.targeting,
					popupLocation			: "bottom",
					width					: 250,
					offsetWindow			: targetWindow,
		            beforePopupOpen			: function() {
									            	popupFactoryRemove('smartTextContentViewer_beforePopupOpen');
									            },
					asyncDataType			: "xml",
					asyncSetContentURL		: context + "/getTargeting.form?type=summary" + params ,
					asyncSetContentHandler	: function(o, data) {
												if ($(data).find("content").length > 0)
													return "<div align=\"left\" style=\"padding: 6px 12px; font-size: 10px;\">" +
																$(data).find("content").text() +
															"</div>";
												else 
													return 	"<div>Error: " + client_messages.text.bad_request_for_targeting_summary + "</div>";
											  }
				});
			});

		},
		
		generateImageSelectMenu : function(menu, type) {
			
			var _this = this;	

			if ( type == "imageLibrary" && (!_this.imageLibraryApplied || _this.data.usage != "edit" || $(menu).is('.initComplete')) )
				return;
			if ( type == "localImageLibrary" && (!_this.localImageLibraryApplied || _this.data.usage != "edit" || $(menu).is('.initComplete')) )
				return;
			
			var splId = $(menu).attr('id').split('_');
			var zoneId = splId[1];
			var partId = splId[2];
			var localeId = splId[3];

			$(menu).styleActionElement({
				tagCloudFilter	: true,
				tagCloudType	: 3,
				rightOffsetAdj	: 5,
				maxItemsInList	: 20,
                maxItemDisplay	: 5,
				isAsync			: true,				
				getItemsAsync 	: {
					loadOnDemand		: false,
					getItemsURL			: context+"/getContentObject.form",
					fnExtServerParams	: function () {
												var stampDate = new Date();

												var extType 			= $("#extType_" + zoneId + '_' + partId + (_this.data.defaultLocaleId ? '_' + _this.data.defaultLocaleId : '')).val();
												var documentId 			= $("#documentId_" + zoneId + '_' + partId + (_this.data.defaultLocaleId ? '_' + _this.data.defaultLocaleId : '')).val();
												
												var obj = null;
												if ( type == "imageLibrary" ) {
													
													var contentObjectId 	= $("#contentObjectId_" + (_this.data.defaultLocaleId ? _this.data.defaultLocaleId : '')).val();
													var selectedConLibId 	= $("#defaultContentLibraryId_" + zoneId + '_' + partId + ( localeId != null ? '_' + localeId : '')).val();
													var communicationId		= $("#communicationId_" + zoneId + '_' + partId + ( localeId != null ? '_' + localeId : '')).val();
													
													obj = 	[
																{"name" : "type", "value" : (_this.data.appliesTemplates ? "communicationConLibList" : "conLibList") }
															];

													if ( contentObjectId != undefined && contentObjectId != "" )
														obj[obj.length] = {"name" : "contentObjectId", "value" : contentObjectId};
													if ( selectedConLibId != undefined && selectedConLibId != "" )
														obj[obj.length] = {"name" : "selectedContentLibraryId", "value" : selectedConLibId};
													if ( communicationId != undefined && communicationId != "" )
														obj[obj.length] = {"name" : "communicationId", "value" : communicationId};
													
												} else if ( type == "localImageLibrary" ) {
													
													var contentObjectId 	= $("#contentObjectId_" + (_this.data.defaultLocaleId ? _this.data.defaultLocaleId : '')).val();
													var selectedLocalConLibId 	= $("#defaultLocalContentLibraryId_" + zoneId + '_' + partId + ( localeId != null ? '_' + localeId : '')).val();
													
													obj = 	[
																{"name" : "type", "value" : "localConLibList" }
															];

													if ( contentObjectId != undefined && contentObjectId != "" )
														obj[obj.length] = {"name" : "contentObjectId", "value" : contentObjectId};
													if ( selectedLocalConLibId != undefined && selectedLocalConLibId != "" )
														obj[obj.length] = {"name" : "selectedLocalContentLibraryId", "value" : selectedLocalConLibId};
													
												}

												if ( zoneId != undefined && zoneId != "" )
													obj[obj.length] = {"name" : "zoneId", "value" : zoneId};
												if ( documentId != undefined && documentId != "" )
													obj[obj.length] = {"name" : "documentId", "value" : documentId};
												if ( extType != undefined && extType != "" )
													obj[obj.length] = {"name" : "extType", "value" : extType};
												obj[obj.length] = {"name" : "context", "value" : _this.data.isTestContext ? "test" : "default"};

												return obj;
										  }
				},
				afterFilter		: function(ele, o) {
					
					if ( type == "imageLibrary" ) {
						
						if ( _this.data.appliesTemplates )
							$('#conLibSelect_' + zoneId + '_' + partId + ( localeId != null ? '_' + localeId : '')).change();
						
						$('#conLibSelect_' + zoneId + '_' + partId + ( localeId != null ? '_' + localeId : '')+'_menuTable').find('.actionOption').each( function() {
							// Image Library:  Init content popup
							var instId = parseId(this);
							
							// Default menu item: No popup
							if ( instId.indexOf("empty") != -1 )
								return;
							
							$(this).contentPopup({
										trigger					: "dual",
										contentType				: 'contentLibrary',
										contentItemId			: instId,
										popupLocation			: 'left',
										fnExtServerParams		: function(o) {
											var obj = new Array();
											if ( _this.data.referenceData && _this.data.referenceData.content_library && 
													_this.data.referenceData.content_library[o.data.contentItemId] ) {
													obj = 	[
																{"name" : "variantId", "value" : _this.data.referenceData.content_library[o.data.contentItemId] },
															];
											}
											obj[obj.length] = {"name" : "context", "value" : _this.data.isTestContext ? "test" : "default"};
											return obj;
										},
										fnBeforeContentRequest	: function(o) {
																	o.data.contentLocaleId = _this.data.appliesTemplates ? _this.data.templateLocaleId : _this.data.currentLocaleId;
																  }
									});
						});
						
						
					} else if ( type == "localImageLibrary" ) {
						
						$('#localConLibSelect_' + zoneId + '_' + partId + ( localeId != null ? '_' + localeId : '')+'_menuTable').find('.actionOption').each( function() {
							// Image Library:  Init content popup
							var instId = parseId(this);
							
							// Default menu item: No popup
							if ( instId.indexOf("empty") != -1 )
								return;
							
							$(this).contentPopup({
										trigger					: "dual",
										contentType				: 'localContentLibrary',
										contentItemId			: instId,
										popupLocation			: 'left',
										statusViewId			: _this.data.statusViewId,
										fnExtServerParams		: function(o) {
																	var obj = new Array();
																	obj[obj.length] = {"name" : "context", "value" : _this.data.isTestContext ? "test" : "default"};
																	return obj;
										},
										fnBeforeContentRequest	: function(o) {
																	o.data.contentLocaleId = _this.data.currentLocaleId;
																  }
									});
						});
						
					}

				}
			});
			
			$(menu).addClass('initComplete');

		},

		optionDataJSONtoHTML : function (JSONdata) {
			var _this = this;
			return JSONdata.map(function(e) {
				var styleAttr = _this.getOptionTreeStyleAttr(e);
				var option = '<option style="' + styleAttr + '" value="'+e.id+'">'+e.name+'</option>';
				return option;
			}).join('');
		},

		getOptionTreeStyleAttr : function (option) {
			var paddingLeft = (15 + 16 * option.level);
			var styleAttr = "padding-left: " + paddingLeft + "px;";
			switch (option.contentStatus) {
				case "references":
					styleAttr += "color: #2c40bb";
					break;
				case "dirty":
					styleAttr += "text-decoration: underline";
					break;
				case "suppresses":
				case "outOfSync":
					styleAttr += "color: #bb0002";
					break;

			}
			return styleAttr;
		},

		pollEditorForContextData : function () {
			var _this = this;
			try {
				// Pass currentContentGroupId for plugin use
				if ( $(_this.display.container).find('.mce-tinymce').length > 0
					&& $(_this.display.container).find('.mce-tinymce').is(':visible') ) {
					var ed = tinymce.get('contentEditor_EditorTextarea_'+_this.data.instId);
					ed.settings.currentContentGroupId = _this.data.currentContentGroupId;
				}
			} catch(e) {
				// CATCH tinymce serializer error
			}
		},

		pollEditorForContentChange : function () {
			var _this = this;
			try {
				if ( $(_this.display.editorContent).val() != _this.getCurrentEditorBinding().val() ) {
					if (_this.data.currentContentType == "text") {
						var isDefaultLanguageEditPage = common.isFeatureEnabled('ViewDefaultLanguageEditPage') &&
							_this.data.currentLocaleId === _this.data.defaultLocaleId &&
							_this.data.isTranslating;

						if (!isDefaultLanguageEditPage) {
							_this.getCurrentEditorBinding().val( $(_this.display.editorContent).val() ).trigger('change');
						}
					}
				}
			} catch(e) {
				// CATCH tinymce serializer error
			}
		},
		
		pollEditorForDisplayInit : function () {
			var _this = this;
			if ( $(_this.display.container).find('.mce-tinymce').length > 0
					&& $(_this.display.container).find('.mce-tinymce').is(':visible') ) {
				_this.resizeContentArea();
				_this.updateBackgroundColor();
				_this.updateCanvasSize();

				$('.mce_fullscreen').click( function() {
					var ed = tinymce.get('contentEditor_EditorTextarea_'+_this.data.instId);

					ed.selection.select($(ed.getDoc()).find('p,ul,ol,table,div')[0]);
					ed.selection.collapse(false);
				});
				
				if ( $.isFunction(_this.data.onInitComplete) ) {
					_this.data.onInitComplete(_this);
					_this.data.onInitComplete = null;
				}

				if(window.serializedObservableForm !== undefined) {
					serializedObservableForm.checkIfNeedSave();
				}

			} else {

				window.setTimeout( function(){

					_this.pollEditorForDisplayInit();

					if(window.serializedObservableForm !== undefined) {
						serializedObservableForm.checkIfNeedSave();
					}

				}, 100);
			}
		},
		
		initPDFDisplay : function(url) {
			
			function postPDFLoadCallback(o) {
				
				var _this = o;

				if ( _this.data.graphicInitComplete && $.isFunction(_this.data.onInitComplete) ) {
					_this.data.onInitComplete(_this);
					_this.data.onInitComplete = null;	
				} else {
					window.setTimeout( function(){ postPDFLoadCallback(o); }, 250);
				}
				
			}
			
			var _this = this;
	
			_this.resizeContentArea();
			var pdfContainerHeight = $(_this.display.staticGraphicContentContainer).find('.contentEditor_ImageContainer').height();
			
			var pdfContainer = "<iframe src=\"" + url + "\" style=\"width: 100%; height: " + pdfContainerHeight +"px;\" frameborder=\"0\" scrolling=\"no\">" +
									"<p style=\"padding: 30px; font-size: 12px; color: #333;\">It appears you don't have Adobe Reader or PDF support in this web browser. <a href=\"" + url + "\">Click here to download the PDF</a>. Or <a href=\"http://get.adobe.com/reader/\" target=\"_blank\">click here to install Adobe Reader</a>.</p>" +
								"</iframe>";	
			
			_this.display.staticGraphicContentContainer.find('#pdfContainer').append( pdfContainer );
			
			postPDFLoadCallback(_this);

		},

		processCopyFromContent : function (data) {
			var _this = this;
			
			$(data).find('content').each(function(){
				if ($(this).text() == "")
					_this.setTextContent( "<p>!! " + client_messages.content_editor.no_variant_content_to_copy + " !!</p>" );
				else
					_this.setTextContent( $(this).text() );
			});
		},
		
		processImageLibraryContent : function (data) {
			var _this = this;

			$(data).find('content').each( function(){
				var currentLocaleId 	= $(this).attr('localeId');
				var currentContentEntry	= $(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicInputsContainer_'+_this.data.currentContentGroupId+'_'+currentLocaleId);
				$(currentContentEntry).find('.graphicFileInput').attr('imgName', $(this).attr('name') ? $(this).attr('name') : '' );
				$(currentContentEntry).find('.graphicFileInput').attr('imgPath', $(this).text() );
				$(currentContentEntry).find('.graphicFileInput').attr('uploadDate', $(this).attr('uploadDate') );
				$(currentContentEntry).find('.graphicAppliedImageName input').val( $(this).attr('appliedImageName') );
				$(currentContentEntry).find('.cmsAssetInfo').attr('cmsLastUpdateDate', $(this).attr('cmsLastUpdateDate') );
				$(currentContentEntry).find('.cmsAssetInfo').attr('cmsLastSyncDate', $(this).attr('cmsLastSyncDate') );

				// Update current display
				_this.setGraphicContent( $(this).attr('name'), $(this).text(), $(this).attr('uploadDate'), $(this).attr('appliedImageName') );
				_this.setCmsAssetInfo($(this).attr('cmsLastUpdateDate'), $(this).attr('cmsLastSyncDate'));
				
				// IMAGE LINK
				var currentImageLink	= $(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicImageLinkContainer_'+_this.data.currentContentGroupId+'_'+currentLocaleId);
				$(currentImageLink).find('.graphicImageLink .imageLinkInput').val( $(this).attr('imageLink') );				
				_this.setGraphicImageLink($(this).attr('imageLink'));
				
				// IMAGE ALT TEXT
				var currentImageAltText	= $(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicImageAltTextContainer_'+_this.data.currentContentGroupId+'_'+currentLocaleId);
				$(currentImageAltText).find('.graphicImageAltText .imageAltTextInput').val( $(this).attr('imageAltText') );				
				_this.setGraphicImageAltText($(this).attr('imageAltText'));

				// IMAGE EXTERNAL LINK
				var currentImageExtLink	= $(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicImageExtLinkContainer_'+_this.data.currentContentGroupId+'_'+currentLocaleId);
				$(currentImageExtLink).find('.graphicImageExtLink .imageExtLinkInput').val( $(this).attr('imageExtLink') );
				_this.setGraphicImageExtLink($(this).attr('imageExtLink'));

				// IMAGE EXTERNAL PATH
				var currentImageExtPath	= $(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicImageExtPathContainer_'+_this.data.currentContentGroupId+'_'+currentLocaleId);
				$(currentImageExtPath).find('.graphicImageExtPath .imageExtPathInput').val( $(this).attr('imageExtPath') );
				_this.setGraphicImageExtPath($(this).attr('imageExtPath'));
				
				// Same as default: Update same as references
				if (currentLocaleId == _this.data.defaultLocaleId) {
					
					for (index in _this.data.languages) {				
						for (localeId in _this.data.languages[index]) {
							if (localeId != _this.data.defaultLocaleId) {

								if ( $(_this.display.container).find('.contentEditor_SameAsDefaultInputContainer_'+_this.data.currentContentGroupId+'_'+localeId+' .sameAsDefaultInput input[type=checkbox]:checked').length > 0 ) {
									$(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicInputsContainer_'+_this.data.currentContentGroupId+'_'+localeId).each( function() {
	        							$(this).find('.graphicFileInput').attr('imgName', $(currentContentEntry).find('.graphicFileInput').attr('imgName') ? $(currentContentEntry).find('.graphicFileInput').attr('imgName') : '');
			        					$(this).find('.graphicFileInput').attr('imgPath', $(currentContentEntry).find('.graphicFileInput').attr('imgPath'));
			        					$(this).find('.graphicFileInput').attr('uploadDate', $(currentContentEntry).find('.graphicFileInput').attr('uploadDate'));
			        					$(this).find('.graphicAppliedImageName input').val( $(currentContentEntry).find('.graphicAppliedImageName input').val() );
			        					$(this).find('.cmsAssetInfo').attr('cmsLastUpdateDate', $(currentContentEntry).find('.cmsAssetInfo').attr('cmsLastUpdateDate'));
			        					$(this).find('.cmsAssetInfo').attr('cmsLastSyncDate', $(currentContentEntry).find('.cmsAssetInfo').attr('cmsLastSyncDate'));
			        					//$(this).find('.useContentLibraryToggle input').attr('checked','checked');
	        						});
									// IMAGE LINK
									$(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicImageLinkContainer_'+_this.data.currentContentGroupId+'_'+localeId).each( function() {
										$(this).find('.graphicImageLink .imageLinkInput').val( $(currentImageLink).find('.graphicImageLink .imageLinkInput').val() );
									});
									// IMAGE ALT TEXT
									$(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicImageAltTextContainer_'+_this.data.currentContentGroupId+'_'+localeId).each( function() {
										$(this).find('.graphicImageAltText .imageAltTextInput').val( $(currentImageAltText).find('.graphicImageAltText .imageAltTextInput').val() );
									});
									// IMAGE EXTERNAL LINK
									$(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicImageExtLinkContainer_'+_this.data.currentContentGroupId+'_'+localeId).each( function() {
										$(this).find('.graphicImageExtLink .imageExtLinkInput').val( $(currentImageExtLink).find('.graphicImageExtLink .imageExtLinkInput').val() );
									});
									// IMAGE EXTERNAL PATH
									$(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicImageExtPathContainer_'+_this.data.currentContentGroupId+'_'+localeId).each( function() {
										$(this).find('.graphicImageExtPath .imageExtPathInput').val( $(currentImageExtPath).find('.graphicImageExtPath .imageExtPathInput').val() );
									});
								}
							}
						}
					} 						
					
				}
				
				
			});
			
			_this.setState( "edit", "edit" );
		},
		
		processSharedContent : function (data) {
			var _this = this;

			var currentGroup = $(_this.content.bindingContainer).find("[groupId='"+_this.data.currentContentGroupId+"']");

			$(data).find('content').each( function(){
				var sharedContentName = $(this).find('contentName').text();
				$(currentGroup).attr('groupRefObjLabel',sharedContentName);
				
				if ( _this.data.currentContentType == "text" ) {
					$(this).find('textItem').each( function(){
						var localeId = $(this).attr('id');
						var content = $(this).text();
						
						var currentContentEntry	= $(currentGroup).find("[localeId='"+localeId+"']");
						$(currentContentEntry).find('textarea').val( content );
						
						if (_this.data.currentLocaleId == localeId)
							_this.setTextContent( content );
					});
				} else if ( _this.data.currentContentType == "graphic" ) {
					$(this).find('graphicItem').each( function(){
						var localeId 			= $(this).attr('id');
						var imageName 			= $(this).find('name').text();
						var imagePath 			= $(this).find('location').text();
						var appliedImageName 	= $(this).find('appliedName').text();
						var imageLink			= $(this).find('imageLink').text();
						var imageAltText		= $(this).find('imageAltText').text();
						var imageExtLink		= $(this).find('imageExtLink').text();
						var imageExtPath		= $(this).find('imageExtPath').text();
						var uploadDate 			= $(this).find('uploadDate').text();
						var lastUpdateDate 		= $(this).find('uploadDate').text();
						var lastSyncDate 		= $(this).find('uploadDate').text();

						var currentContentEntry	= $(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicInputsContainer_'+_this.data.currentContentGroupId+'_'+localeId);
						$(currentContentEntry).find('.graphicFileInput').attr('imgName', imageName);
						$(currentContentEntry).find('.graphicFileInput').attr('imgPath', imagePath);
						$(currentContentEntry).find('.graphicFileInput ').attr('uploadDate', uploadDate);
						$(currentContentEntry).find('.graphicAppliedImageName input').val(appliedImageName);
						$(currentContentEntry).find('.cmsAssetInfo').attr('cmsLastUpdateDate', lastUpdateDate);
						$(currentContentEntry).find('.cmsAssetInfo').attr('cmsLastSyncDate', lastSyncDate);
						
						// IMAGE LINK
						var currentImageLink	= $(_this.display.graphicImageLinkContainer).find('.contentEditor_GraphicImageLinkContainer_'+_this.data.currentContentGroupId+'_'+localeId);
						$(currentImageLink).find('.graphicImageLink .imageLinkInput').val(imageLink);
						
						// IMAGE ALT TEXT
						var currentImageAltText	= $(_this.display.graphicImageAltTextContainer).find('.contentEditor_GraphicImageAltTextContainer_'+_this.data.currentContentGroupId+'_'+localeId);
						$(currentImageAltText).find('.graphicImageAltText .imageAltTextInput').val(imageAltText);

						// IMAGE EXTERNAL LINK
						var currentImageExtLink	= $(_this.display.graphicImageExtLinkContainer).find('.contentEditor_GraphicImageExtLinkContainer_'+_this.data.currentContentGroupId+'_'+localeId);
						$(currentImageExtLink).find('.graphicImageExtLink .imageExtLinkInput').val(imageExtLink);

						// IMAGE EXTERNAL PATH
						var currentImageExtPath	= $(_this.display.graphicImageExtPathContainer).find('.contentEditor_GraphicImageExtPathContainer_'+_this.data.currentContentGroupId+'_'+localeId);
						$(currentImageExtPath).find('.graphicImageExtPath .imageExtPathInput').val(imageExtPath);

						if (_this.data.currentLocaleId == localeId){
							_this.setGraphicContent( imageName, imagePath, uploadDate, appliedImageName );
							_this.setCmsAssetInfo(lastUpdateDate, lastSyncDate);
							_this.setGraphicImageLink(imageLink);
							_this.setGraphicImageAltText(imageAltText);
							_this.setGraphicImageExtLink(imageExtLink);
							_this.setGraphicImageExtPath(imageExtPath);
						}
					});				
				}
			});
			
			_this.setState( "edit", "shared" );
		},
		
		processSelectionContent : function (data, state) {
			var _this = this;

			$(data).find('content').each( function(){
				var currentGroupId 		= $(this).attr('groupId');
				var currentLocaleId 	= $(this).attr('localeId');
				var currentGroup		= $(_this.content.bindingContainer).find("[groupId='"+currentGroupId+"']");
				var currentContentType	= $(currentGroup).attr('content');

				if ( $(currentGroup).attr('groupState') != 'shared' && $(currentGroup).attr('groupState') != 'leaveEmpty') {
					// Update content for all content entries
					if (currentContentType == "text") {
						var currentContentEntry	= $(currentGroup).find("[localeId='"+currentLocaleId+"']");
						$(currentContentEntry).find('textarea').val( $(this).text() );
					} else if (currentContentType == "graphic") {
						var currentContentEntry	= $(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicInputsContainer_'+currentGroupId+'_'+currentLocaleId);
						$(currentContentEntry).find('.graphicFileInput').attr('imgName', $(this).attr('name') ? $(this).attr('name') : '' );
						$(currentContentEntry).find('.graphicFileInput').attr('imgPath', $(this).text() );
						$(currentContentEntry).find('.graphicFileInput').attr('uploadDate', $(this).attr('uploadDate') );
						$(currentContentEntry).find('.graphicAppliedImageName input').val( $(this).attr('appliedImageName') );
						$(currentContentEntry).find('.cmsAssetInfo').attr('cmsLastUpdateDate', $(this).attr('cmsLastUpdateDate') );
						$(currentContentEntry).find('.cmsAssetInfo').attr('cmsLastSyncDate', $(this).attr('cmsLastSyncDate') );
						// IMAGE LINK
						var currentImageLink	= $(_this.display.graphicImageLinkContainer).find('.contentEditor_GraphicImageLinkContainer_'+currentGroupId+'_'+currentLocaleId);
						$(currentImageLink).find('.graphicImageLink .imageLinkInput').val( $(this).attr('imageLink') );
						// IMAGE ALT TEXT
						var currentImageAltText	= $(_this.display.graphicImageAltTextContainer).find('.contentEditor_GraphicImageAltTextContainer_'+currentGroupId+'_'+currentLocaleId);
						$(currentImageAltText).find('.graphicImageAltText .imageAltTextInput').val( $(this).attr('imageAltText') );
						// IMAGE EXTERNAL LINK
						var currentImageExtLink	= $(_this.display.graphicImageExtLinkContainer).find('.contentEditor_GraphicImageExtLinkContainer_'+currentGroupId+'_'+currentLocaleId);
						$(currentImageExtLink).find('.graphicImageExtLink .imageExtLinkInput').val( $(this).attr('imageExtLink') );
						// IMAGE EXTERNAL PATH
						var currentImageExtPath	= $(_this.display.graphicImageExtPathContainer).find('.contentEditor_GraphicImageExtPathContainer_'+currentGroupId+'_'+currentLocaleId);
						$(currentImageExtPath).find('.graphicImageExtPath .imageExtPathInput').val( $(this).attr('imageExtPath') );
					}
	
					// Update current display
					if (_this.data.currentContentGroupId == currentGroupId && _this.data.currentLocaleId == currentLocaleId) {
						if (currentContentType == "text"){
							_this.setTextContent( $(this).text() );
						}else if (currentContentType == "graphic"){
							_this.setGraphicContent( $(this).attr('name'), $(this).text(), $(this).attr('uploadDate'), $(this).attr('appliedImageName') );
							_this.setCmsAssetInfo($(this).attr('cmsLastUpdateDate'), $(this).attr('cmsLastSyncDate'));
							_this.setGraphicImageLink($(this).attr('imageLink'));
							_this.setGraphicImageAltText($(this).attr('imageAltText'));
							_this.setGraphicImageExtLink($(this).attr('imageExtLink'));
							_this.setGraphicImageExtPath($(this).attr('imageExtPath'));
						}
					}
				}
				
			});

			_this.setState(state);
		},

		processParentDataContent: function() {
			var _this = this;

			var parentGraphicFileInput = _this.getCurrentContentEntry().find('.parentGraphicFileInput');

			if (parentGraphicFileInput.length) {
				var parentImageName = parentGraphicFileInput.attr('imgName');
				var parentImagePath = parentGraphicFileInput.attr('imgPath');
				var parentAppliedImageName = parentGraphicFileInput.attr('appliedLangImageName');
				var parentUploadDate = parentGraphicFileInput.attr('uploadDate');

				var parentCmsLastUpdateDate = '';
				var parentCmsLastSyncDate = '';
				var parentImageLink = parentGraphicFileInput.attr('imageLink');
				var parentImageAltText = parentGraphicFileInput.attr('imageAltText');
				var parentImageExtLink = parentGraphicFileInput.attr('imageExtLink');
				var parentImageExtPath = parentGraphicFileInput.attr('imageExtPath');

				var currentContentEntry	= _this.getCurrentGraphicInputsContainer();
				$(currentContentEntry).find('.graphicFileInput').attr('imgName', parentImageName);
				$(currentContentEntry).find('.graphicFileInput').attr('imgPath', parentImagePath);
				$(currentContentEntry).find('.graphicFileInput ').attr('uploadDate', parentUploadDate);
				$(currentContentEntry).find('.graphicAppliedImageName input').val(parentAppliedImageName);
				$(currentContentEntry).find('.cmsAssetInfo').attr('cmsLastUpdateDate', parentCmsLastUpdateDate);
				$(currentContentEntry).find('.cmsAssetInfo').attr('cmsLastSyncDate', parentCmsLastSyncDate);

				_this.setGraphicContent(_this.getCurrentGraphicFileName(),
					_this.getCurrentGraphicFilePath(),
					_this.getCurrentGraphicUploadDate(),
					_this.getCurrentGraphicAppliedImageName());
				_this.setCmsAssetInfo(_this.getCurrentGraphicCmsLastUpdateDate(), _this.getCurrentGraphicCmsLastSyncDate());

				// IMAGE LINK
				var currentImageLinkEntry = _this.getCurrentGraphicImageLinkContainer();
				$(currentImageLinkEntry).find('.graphicImageLink .imageLinkInput').val(parentImageLink);
				_this.setGraphicImageLink(_this.getCurrentGraphicImageLink());

				// IMAGE ALT TEXT
				var currentImageAltTextEntry = _this.getCurrentGraphicImageAltTextContainer();
				$(currentImageAltTextEntry).find('.graphicImageAltText .imageAltTextInput').val(parentImageAltText);
				_this.setGraphicImageAltText(_this.getCurrentGraphicImageAltText());

				// IMAGE EXTERNAL LINK
				var currentImageExtLinkEntry = _this.getCurrentGraphicImageExtLinkContainer();
				$(currentImageExtLinkEntry).find('.graphicImageExtLink .imageExtLinkInput').val(parentImageExtLink);
				_this.setGraphicImageExtLink(_this.getCurrentGraphicImageExtLink());

				// IMAGE EXTERNAL PATH
				var currentImageExtPathEntry = _this.getCurrentGraphicImageExtPathContainer();
				$(currentImageExtPathEntry).find('.graphicImageExtPath .imageExtPathInput').val(parentImageExtPath);
				_this.setGraphicImageExtPath(_this.getCurrentGraphicImageExtPath());
			}
		},
		
		requestImageLibraryContent : function(contentObjectId, requestLocalContent) {
			var _this = this;
			var stampDate = new Date();

			var globalState = $(_this.content.bindingContainer).attr('globalState');
			var sameAsDefault = _this.getCurrentSameAsDefaultInputContainer().find('.sameAsDefaultInput input[type="checkbox"]').is(":checked");
			var sameAsParent = _this.getCurrentSameAsParentInputContainer().find('.sameAsParentInput input[type="checkbox"]').is(":checked");
			if ( globalState === 'sameAs' || sameAsDefault || sameAsParent || !(parseInt(contentObjectId) > 0 )) {
				return;
			}

			var touchpointSelectionId = getParam("touchpointSelectionId") || "-1";

			var paramString = 	"contentObjectId=" + contentObjectId +
								"&localContent=" + requestLocalContent +
								"&statusViewId=" + (requestLocalContent ? _this.data.statusViewId : 2) +
								"&localeId=" + (_this.data.appliesTemplates ? _this.data.templateLocaleId : _this.data.currentLocaleId) +
								"&context=" + (_this.data.isTestContext ? "test" : "default") +
								"&touchpointSelectionId=" + touchpointSelectionId +
								"&cacheStamp=" + stampDate.getTime();

			// Connected: Resolve image library
			if ( !requestLocalContent &&
				 _this.data.referenceData && _this.data.referenceData.content_library && 
				 _this.data.referenceData.content_library[contentObjectId] )
				paramString += "&variantId=" + _this.data.referenceData.content_library[contentObjectId];

			$(_this.display.container).find('.contentEditor_InnerContentContainer').hide();
			$(_this.display.container).find('.contentEditor_LoadingIndicatorContainer').show();
			
			$.ajax({
				type: "GET",
				url: context+"/getContent.form?" + paramString,
				dataType: "xml",
				success: function(data) {
					_this.processImageLibraryContent(data);

					$(_this.display.container).find('.contentEditor_LoadingIndicatorContainer').hide();
					$(_this.display.container).find('.contentEditor_InnerContentContainer').show();
					
					_this.resizeContentArea();
				}
			});
		},
		
		requestSelectionContent : function(selectionId, type) {
			var _this = this;
			var stampDate = new Date();
			
			if (type == "copyFrom")
				lang = _this.data.currentLocaleId;
			else if (type == "sameAs" || type == "default")
				lang = 0;
			
			var paramString = 	"touchpointSelectionId=" + selectionId + 
								"&contentObjectId=" + _this.data.contentObjectId +
								"&localeId=" + lang +
								"&cacheStamp=" + stampDate.getTime();
			
			var currentGroupId = _this.data.currentContentGroupId;
			if ( currentGroupId.indexOf('part') != -1 && type == "copyFrom" )
				paramString += "&partId="+currentGroupId.replace('part','');
			
			$(_this.display.container).find('.contentEditor_InnerContentContainer').hide();
			$(_this.display.container).find('.contentEditor_LoadingIndicatorContainer').show();
			
			
			$.ajax({
				type: "GET",
				url: context+"/getContent.form?"+paramString,
				dataType: "xml",
				success: function(data) {
					if (type == "default")
						_this.processSelectionContent(data, "edit");
					else if (type == "copyFrom")
						_this.processCopyFromContent(data);
					else if (type == "sameAs")
						_this.processSelectionContent(data, "sameAs");
					
					$(_this.display.container).find('.contentEditor_LoadingIndicatorContainer').hide();
					$(_this.display.container).find('.contentEditor_InnerContentContainer').show();

					_this.toggleStaticTextRendering();
					_this.resizeContentArea();
				}
			});
		},
		
		resizeContentArea : function () {
			var _this = this;

			var innerContentFrame = $(_this.display.container).find('.contentEditor_InnerContentContainer');
			var targetContentArea = null;
			if (_this.data.currentContentType == "text") {
				if ( $(_this.display.staticTextContentContainer).is(':visible') )
					targetContentArea = $(_this.display.staticTextContentContainer).find('.contentEditor_StaticTextContentContainer');
				else if ( $(_this.display.container).find('.mce-tinymce').is(':visible') )
					targetContentArea = $(_this.display.container).find('.mce-tinymce');
				else
					return;
			} else if (_this.data.currentContentType == "graphic") {
				targetContentArea = $(_this.display.staticGraphicContentContainer).find('.contentEditor_ImageContainer');
			}

			if ( !$(targetContentArea).is(':visible') || !$(innerContentFrame).is(':visible') )
				return;

			var contentAreaTopDelta = targetContentArea != null ? $(targetContentArea).offset().top - $(innerContentFrame).offset().top : 0;
			var contentAreaBottomDelta = 0;
			var graphicImageAttributes = ['GraphicImageLinkContainer', 'GraphicImageAltTextContainer', 'GraphicImageExtLinkContainer', 'GraphicImageExtPathContainer'];

			graphicImageAttributes.forEach(function(attribute) {
				var element = $(_this.display.staticGraphicContentContainer).find('.contentEditor_' + attribute);
				if (element.is(':visible')) {
					contentAreaBottomDelta += element.height();
				}
			});

			var contentAreaHeight = $(innerContentFrame).height() - contentAreaTopDelta - contentAreaBottomDelta;
			
			if ( _this.data.currentContentType == "graphic" &&
					$(_this.display.container).find('.contentEditor_GraphicImageLinkContainer:visible').length != 0 && $(_this.display.container).find('.contentEditor_GraphicImageLinkContainer:visible').height() != 0 &&
					$(_this.display.container).find('.contentEditor_GraphicImageAltTextContainer:visible').length != 0 && $(_this.display.container).find('.contentEditor_GraphicImageAltTextContainer:visible').height() != 0 &&
					$(_this.display.container).find('.contentEditor_GraphicImageExtLinkContainer:visible').length != 0 && $(_this.display.container).find('.contentEditor_GraphicImageExtLinkContainer:visible').height() != 0 &&
					$(_this.display.container).find('.contentEditor_GraphicImageExtPathContainer:visible').length != 0 && $(_this.display.container).find('.contentEditor_GraphicImageExtPathContainer:visible').height() != 0) {
				contentAreaHeight -= 177;
			}

			var imgMaxWidth = $(_this.display.staticGraphicContentContainer).parent().width();
			if ( _this.data.canvas_dimensions && _this.data.canvas_dimensions.length != 0 ) {
				var canvasWidth = parseFloat(_this.data.canvas_dimensions.split(':')[0]) * 100;
				if ( imgMaxWidth > canvasWidth )
					imgMaxWidth = canvasWidth;
			}
			
			if (_this.data.currentContentType == "graphic") {
				$(_this.display.staticGraphicContentContainer).find('.contentEditor_ImageContainer')
					.css({'max-width': $(_this.display.staticGraphicContentContainer).parent().width() + 'px'});
				$(_this.display.staticGraphicContentContainer).find('.contentEditor_ImageContainer .contentEditor_GraphicImg')
				.css({'max-width': imgMaxWidth + 'px'});
			}
				
			if (!_this.data.fullscreen.is_fullscreen) {
				$(targetContentArea).height(contentAreaHeight);
				$(targetContentArea).find('#pdfContainer iframe').height(contentAreaHeight);
			}
			
			if ( _this.data.usage == "view" ) {
				$(targetContentArea).css({'max-width': (_this.display.container.innerWidth() - (_this.data.currentContentType == "text" ? 2 : 0)) + 'px'});
				if (_this.data.currentContentType == "text" && !_this.data.fullscreen.is_fullscreen) {
					$(targetContentArea).css({'max-height': '485px'});
					$(targetContentArea).find('#pdfContainer iframe').css({'max-height': '485px'});
				} else {
					$(targetContentArea).css({'max-height': 'none'});
					$(targetContentArea).find('#pdfContainer iframe').css({'max-height': 'none'});
				}
			}
			
			if ( $(_this.display.container).find('.mce-tinymce').is(':visible') ) {
					if (_this.data.editorInitData != undefined){
						_this.data.editorInitData.height = contentAreaHeight;
			        	// Restrict size of editor
						var ed = tinymce.get('contentEditor_EditorTextarea_'+_this.data.instId);
						if ( ed != undefined ) {
							var mce_bars_height = 0;
			                $(ed.editorContainer).find('.mce-toolbar, .mce-statusbar, .mce-menubar').each( function() {
			                	mce_bars_height += Math.min( $(this).height(), 41 );
			                });
				            ed.theme.resizeTo("100%", contentAreaHeight - mce_bars_height - 4);
							_this.updateCanvasSize();
						}
					}
			}

			if (window !== getTopFrame()) {
				common.refreshParentIframeHeight();
			}

		},
		
		selectDataJSONtoHTML : function (JSONdata) {
			var _this = this;
			var selectHtml = '';
			if (JSONdata.groups) {
				for (currentGroup in JSONdata.groups) {
					selectHtml += 	'<optgroup label="'+JSONdata.groups[currentGroup].name+'">' +
										_this.optionDataJSONtoHTML(JSONdata.groups[currentGroup].options) +
									'</optgroup>';
				}
			} else if (JSONdata.options) {
				selectHtml += _this.optionDataJSONtoHTML(JSONdata.options);
			}
			return selectHtml;
		},
		
		fileUploadBtnInit : function() {
			// Hidden file input: Position in line with upload button
			var _this = this;
			
			var imgUploadBtn = _this.getCurrentGraphicInputsContainer().find('.customGraphicTD').find('.actionBtnTable');
			if ( _this.data.usage == "edit" && $(imgUploadBtn).length > 0 )
				_this.getCurrentGraphicInputsContainer().find('.customGraphicTD').find('.contentEditor_FileUploadInput')
					.css({'top': $(imgUploadBtn).position().top+'px', 'left': $(imgUploadBtn).position().left+'px', 'width': $(imgUploadBtn).width() + 'px'});
		},
		
		setCurrentImageLibraryToggle : function() {
			var _this = this;
			var contentLocaleId = _this.getContentLanguage();
			var isSameAsDefault = $(_this.display.sameAsDefaultContainer).find('.contentEditor_SameAsDefaultInputContainer_'+_this.data.currentContentGroupId+'_'+contentLocaleId+' .sameAsDefaultInput input[type=checkbox]:checked').length === 1;

			if ( _this.data.currentContentType != "graphic" || _this.data.usage != "edit" )
				return;

			var imageLibraryToggle 		= _this.getCurrentGraphicInputsContainer().find('.useContentLibraryToggle input');
			var localImageLibraryToggle = _this.getCurrentGraphicInputsContainer().find('.useLocalContentLibraryToggle input');
			
			_this.getCurrentGraphicInputsContainer().find('.imageLibraryTD').hide();
			_this.getCurrentGraphicInputsContainer().find('.customGraphicTD').hide();
			_this.getCurrentGraphicImageLinkContainer().hide();
			_this.getCurrentGraphicImageAltTextContainer().hide();
            _this.getCurrentGraphicImageExtLinkContainer().hide();
			_this.getCurrentGraphicImageExtPathContainer().hide();

			if ( !_this.imageLibraryApplied && !_this.localImageLibraryApplied ) {
				_this.getCurrentGraphicInputsContainer().find('.imageLibraryToggleTD').hide();
				_this.getCurrentGraphicInputsContainer().find('.customGraphicTD').show();

				_this.getCurrentGraphicImageLinkContainer().show();
				_this.getCurrentGraphicImageAltTextContainer().show();
                _this.getCurrentGraphicImageExtLinkContainer().show();
				_this.getCurrentGraphicImageExtPathContainer().show();
				return;
			}

			if ( isSameAsDefault) {
				return;
			}
			
			if ( _this.data.library_only && (!_this.imageLibraryApplied || !_this.localImageLibraryApplied) )
				_this.getCurrentGraphicInputsContainer().find('.imageLibraryToggleTD').hide();
			
			if ( $(imageLibraryToggle).is(':checked') || $(localImageLibraryToggle).is(':checked') ) {
				_this.getCurrentGraphicInputsContainer().find('.imageLibraryTD').show();
				_this.getCurrentGraphicInputsContainer().find('.useContentLibrarySelect,.useLocalContentLibrarySelect').hide();
				if ( $(imageLibraryToggle).is(':checked') )
					_this.getCurrentGraphicInputsContainer().find('.useContentLibrarySelect').show();
				else if ( $(localImageLibraryToggle).is(':checked') )
					_this.getCurrentGraphicInputsContainer().find('.useLocalContentLibrarySelect').show();
			} else {
				_this.getCurrentGraphicInputsContainer().find('.customGraphicTD').show();
				_this.getCurrentGraphicImageLinkContainer().show();
                _this.getCurrentGraphicImageAltTextContainer().show();
                _this.getCurrentGraphicImageExtLinkContainer().show();
				_this.getCurrentGraphicImageExtPathContainer().show();
			}
				
			// IMAGE LINK
			if(_this.getCurrentGraphicImageLinkContainer().is(':visible')){
				$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageLinkContainer').hide();
			}else{
				$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageLinkContainer').show();
			}
			
			// IMAGE ALT TEXT
			if(!_this.getCurrentGraphicImageAltTextContainer().is(':visible') && !_this.getCurrentGraphicImageLinkContainer().is(':visible')){
				$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageAltTextContainer').show();
			}else{
				$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageAltTextContainer').hide();
			}

			// IMAGE EXTERNAL LINK
			if(!_this.getCurrentGraphicImageExtLinkContainer().is(':visible') && !_this.getCurrentGraphicImageLinkContainer().is(':visible')
				&& !_this.getCurrentGraphicImageAltTextContainer().is(':visible')){
				$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageExtLinkContainer').show();
			}else{
				$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageExtLinkContainer').hide();
			}

			// IMAGE EXTERNAL PATH
			if(!_this.getCurrentGraphicImageExtPathContainer().is(':visible') && !_this.getCurrentGraphicImageExtPathContainer().is(':visible')
				&& !_this.getCurrentGraphicImageAltTextContainer().is(':visible')){
				$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageExtPathContainer').show();
			}else{
				$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageExtPathContainer').hide();
			}
			
			// INIT IMAGE USAGE TYPE TOGGLE
			var typeSelect = _this.getCurrentGraphicInputsContainer().find('.imageLibaryTypeSelect');
			if ( !$(typeSelect).is('.initComplete') ) {
				if ( !_this.data.library_only )
					$(typeSelect).append('<option value="1">' + _this.data.text.uploadedImage + '</option>');
				if ( _this.imageLibraryApplied )
					$(typeSelect).append('<option value="2" ' + ($(imageLibraryToggle).is(':checked') ? 'selected="selected"' : '') + '>' + _this.data.text.imageLibrary + '</option>');
				if ( _this.localImageLibraryApplied )
					$(typeSelect).append('<option value="3" ' + ($(localImageLibraryToggle).is(':checked') ? 'selected="selected"' : '') + '>' + _this.data.text.localImageLibrary + '</option>');
				
				$(typeSelect).styleActionElement();
				$(typeSelect).change( function() {
					
					var imageLibraryToggle 		= _this.getCurrentGraphicInputsContainer().find('.useContentLibraryToggle input');
					var localImageLibraryToggle = _this.getCurrentGraphicInputsContainer().find('.useLocalContentLibraryToggle input');
					var useImageLibraryBinding = _this.getCurrentContentEntry().find('.useImageLibraryBinding');
					var imageLibraryObjectTypeBinding = _this.getCurrentContentEntry().find('.imageLibraryObjectTypeBinding');

					$(imageLibraryToggle).removeAttr('checked');
					$(localImageLibraryToggle).removeAttr('checked');
					$(useImageLibraryBinding).val(false);
					$(imageLibraryObjectTypeBinding).val(0);
					if ( $(this).val() == "2" ) {
						$(imageLibraryToggle).attr('checked','checked');
						$(useImageLibraryBinding).val(true);
						$(imageLibraryObjectTypeBinding).val(8);
					} else if ( $(this).val() == "3" ) {
						$(localImageLibraryToggle).attr('checked','checked');
						$(useImageLibraryBinding).val(true);
						$(imageLibraryObjectTypeBinding).val(2);
					}
					
					_this.setGraphicContent("","","","");
					_this.setCmsAssetInfo("","");
					_this.setGraphicImageLink("");
					_this.setGraphicImageAltText("");
					_this.setGraphicImageExtLink("");
					_this.setGraphicImageExtPath("");
					_this.getCurrentGraphicInputsContainer().find('.imageLibrarySelectTD select').each( function() {
						$(this).selectOption('0');
					})
					_this.setCurrentImageLibraryToggle();
					_this.resizeContentArea();
					
				});
				
				$(typeSelect).addClass('initComplete');
			}
			
			_this.fileUploadBtnInit();
		},
		
		setCurrentSameAsDefaultInput : function() {
			var _this = this;

			$("[class*='contentEditor_SameAsDefaultInputContainer']").hide();
			var currentSameAsDefaultContainer = $(_this.display.sameAsDefaultContainer).find('.contentEditor_SameAsDefaultInputContainer_'+_this.data.currentContentGroupId+'_'+_this.data.currentLocaleId);
			if ( currentSameAsDefaultContainer.length > 0 && 
				(_this.getCurrentGroup().attr('groupState') == 'edit' || $(_this.content.bindingContainer).attr('globalState') == 'edit') ) {
				$(currentSameAsDefaultContainer).show();
			}
		},
		
		setCurrentSameAsParentInput : function() {
			var _this = this;

			$("[class*='contentEditor_SameAsParentInputContainer']").hide();
			var currentSameAsParentContainer = $(_this.display.sameAsParentContainer).find('.contentEditor_SameAsParentInputContainer_'+_this.data.currentContentGroupId+'_'+_this.data.currentLocaleId);
			if ( currentSameAsParentContainer.length > 0 && 
				(_this.getCurrentGroup().attr('groupState') == 'edit' || $(_this.content.bindingContainer).attr('globalState') == 'edit') ) {
				$(currentSameAsParentContainer).show();
			}
		},
		
		setGraphicContent : function(imageName, imagePath, imageUploadDate, appliedImageName) {
			var _this = this;
			var stampDate = new Date();

			_this.setCurrentSameAsDefaultInput();
			_this.setCurrentSameAsParentInput();
			
			if (imageName && imageName != "") {
				if (imagePath && imagePath != "") {
					var filePath = imagePath.indexOf('sandbox_file') == -1 ? encodeURIComponent(imagePath.replace(/\\/g,"/").replace("&","%26")) : imagePath;
					var imageHrefHtml = '<a href="javascript:javascriptHref(\''+_this.data.contextPath+'/download/image.form?file=' + filePath + "&action=saveFile&cacheStamp="+stampDate.getTime()+'\');">' +
											imageName + 
										'</a>';
					_this.display.staticGraphicContentContainer.find('.contentEditor_GraphicName').html(imageHrefHtml);
				} else
					_this.display.staticGraphicContentContainer.find('.contentEditor_GraphicName').text(imageName);
			} else
				_this.display.staticGraphicContentContainer.find('.contentEditor_GraphicName').text(_this.data.text.noImage);
			
			_this.display.staticGraphicContentContainer.find('#noImageMsgContainer').hide();
			_this.display.staticGraphicContentContainer.find('#clickToDownloadMsgContainer').hide();
			_this.display.staticGraphicContentContainer.find('#editFileBtnContainer').hide();
			_this.display.staticGraphicContentContainer.find('img').attr('src','').hide();
			_this.display.staticGraphicContentContainer.find('#pdfViewerContainer #pdfContainer').remove();
			_this.display.staticGraphicContentContainer.find('#pdfViewerContainer').hide();
			
			var isPDF = imagePath && (endsWith(imageName.toUpperCase(),'.PDF') || endsWith(imagePath.toUpperCase(),'.PDF'));
			if ( $.isFunction(_this.data.onInitComplete) && !isPDF ) {
				_this.data.onInitComplete(_this);
				_this.data.onInitComplete = null;
			}

			if ( isPDF ) {
				var pdfContainer = "<div id=\"pdfContainer\"></div>";
				_this.display.staticGraphicContentContainer.find('#pdfViewerContainer').show().append(pdfContainer);
				
				var filePath = imagePath.indexOf('sandbox_file') == -1 ? encodeURIComponent(imagePath.replace(/\\/g,"/")) : imagePath;
				var url = _this.data.contextPath + 
							"/download/pdf.form" +
							"?file=" + filePath + 
							"&embedded_pdf=true" +
							"&tk=" + getParam('tk') +
							"&cacheStamp="+stampDate.getTime();
				
				_this.initPDFDisplay(url);
			} else if ( imagePath && (endsWith(imageName.toUpperCase(),'.RTF') || endsWith(imageName.toUpperCase(),'.IMG') || endsWith(imageName.toUpperCase(),'.EPS') ||
					endsWith(imageName.toUpperCase(),'.DXF') || endsWith(imageName.toUpperCase(),'.DLF') || endsWith(imageName.toUpperCase(),'.DOCX') || endsWith(imageName.toUpperCase(),'.PSEG')) ) {
				_this.display.staticGraphicContentContainer.find('#clickToDownloadMsgContainer').show();
				
				if ( endsWith(imageName.toUpperCase(),'.DOCX') )
					_this.display.staticGraphicContentContainer.find('#editFileBtnContainer').show();
				
			} else if ( imagePath && imagePath != "" ) {
				_this.display.staticGraphicContentContainer.find('img').attr('src',_this.data.contextPath+'/download/image.form?file='+imagePath+"&cacheStamp="+stampDate.getTime()).show();
				_this.display.staticGraphicContentContainer.find('img').load( function() {
					if ( $.isFunction(_this.data.onInitComplete) && !isPDF ) {
						_this.data.onInitComplete(_this);
						_this.data.onInitComplete = null;	
					}
				});
			} else {
				_this.display.staticGraphicContentContainer.find('#noImageMsgContainer').show();
			}
			
			if (imageUploadDate && imageUploadDate != "")
				_this.display.staticGraphicContentContainer.find('.contentEditor_GraphicUploadDate').text(imageUploadDate);
			else if (imageName && imageName != "")
				_this.display.staticGraphicContentContainer.find('.contentEditor_GraphicUploadDate').text(_this.data.text.unknown);
			else
				_this.display.staticGraphicContentContainer.find('.contentEditor_GraphicUploadDate').text(_this.data.text.none);
			
			if (appliedImageName && appliedImageName != "")
				_this.display.staticGraphicContentContainer.find('.contentEditor_GraphicAppliedImageName').text(appliedImageName);
			else
				_this.display.staticGraphicContentContainer.find('.contentEditor_GraphicAppliedImageName').html(_this.data.text.none);
		},
		
		setGraphicImageLink : function(imageLink){
			var _this = this;
			if (imageLink && imageLink != ""){
				// Remove the <p> tag
				imageLink = imageLink.replace('<p>','').replace('</p>','');
				_this.display.staticGraphicContentContainer.find('.contentEditor_StaticGraphicImageLink').html(imageLink);
				_this.display.staticGraphicContentContainer.find('.contentEditor_StaticGraphicImageLink .staticContentItem').each(function(){
					_this.initStaticContentItem(this);
				});
			}else{
				_this.display.staticGraphicContentContainer.find('.contentEditor_StaticGraphicImageLink').html(_this.data.text.none);
			}
		},
		
		setGraphicImageAltText : function(imageAltText){
			var _this = this;
			if (imageAltText && imageAltText != ""){
				// Remove the <p> tag
				imageAltText = imageAltText.replace('<p>','').replace('</p>','');
				_this.display.staticGraphicContentContainer.find('.contentEditor_StaticGraphicImageAltText').html(imageAltText);
				_this.display.staticGraphicContentContainer.find('.contentEditor_StaticGraphicImageAltText .staticContentItem').each(function(){
					_this.initStaticContentItem(this);
				});
			}else{
				_this.display.staticGraphicContentContainer.find('.contentEditor_StaticGraphicImageAltText').html(_this.data.text.none);
			}
		},

		setGraphicImageExtLink : function(imageExtLink){
			var _this = this;
			if (imageExtLink && imageExtLink != ""){
				// Remove the <p> tag
				imageExtLink = imageExtLink.replace('<p>','').replace('</p>','');
				_this.display.staticGraphicContentContainer.find('.contentEditor_StaticGraphicImageExtLink').html(imageExtLink);
				_this.display.staticGraphicContentContainer.find('.contentEditor_StaticGraphicImageExtLink .staticContentItem').each(function(){
					_this.initStaticContentItem(this);
				});
			}else{
				_this.display.staticGraphicContentContainer.find('.contentEditor_StaticGraphicImageExtLink').html(_this.data.text.none);
			}
		},

		setGraphicImageExtPath : function(imageExtPath){
			var _this = this;
			if (imageExtPath && imageExtPath != ""){
				// Remove the <p> tag
				imageExtPath = imageExtPath.replace('<p>','').replace('</p>','');
				_this.display.staticGraphicContentContainer.find('.contentEditor_StaticGraphicImageExtPath').html(imageExtPath);
				_this.display.staticGraphicContentContainer.find('.contentEditor_StaticGraphicImageExtPath .staticContentItem').each(function(){
					_this.initStaticContentItem(this);
				});
			}else{
				_this.display.staticGraphicContentContainer.find('.contentEditor_StaticGraphicImageExtPath').html(_this.data.text.none);
			}
		},
		
		setCmsAssetInfo : function(cmsLastUpdateDate, cmsLastSyncDate) {
			var _this = this;
			if (cmsLastUpdateDate && cmsLastUpdateDate != "")
				_this.display.staticGraphicContentContainer.find('.contentEditor_GraphicCmsLastUpdateDate').text(cmsLastUpdateDate);
			else
				_this.display.staticGraphicContentContainer.find('.contentEditor_GraphicCmsLastUpdateDate').text(_this.data.text.none);

			if (cmsLastSyncDate && cmsLastSyncDate != "")
				_this.display.staticGraphicContentContainer.find('.contentEditor_GraphicCmsLastSyncDate').text(cmsLastSyncDate);
			else
				_this.display.staticGraphicContentContainer.find('.contentEditor_GraphicCmsLastSyncDate').text(_this.data.text.none);
		},

		setTextContent : function(content, target) {
			var _this = this;

			_this.setCurrentSameAsDefaultInput();
			_this.setCurrentSameAsParentInput();
			
			// VAR Tags: Remove actionVariable and fastNavigation class: Forces re-init
			var cleanContent = "";
			if ($.trim(content) != "") {
				$(content).each(function(){
					$(this).find('.actionVariable').removeClass('actionVariable');
					$(this).find("[mce_obj_id]").removeAttr('mce_obj_id');
					$(this).find('.fastNavigation').removeClass('fastNavigation');
					cleanContent += $('<div>').append($(this).clone()).html();
				});
			}

			if ( (!target || target == "editor") && _this.data.usage == "edit" && $(_this.display.container).find('.mce-tinymce').length > 0 ) {
				var editor = tinyMCE.get('contentEditor_EditorTextarea_' + _this.data.instId);
				editor.setContent( cleanContent );	// Editor
				editor.undoManager.clear();
			}
			if (!target || target == "static") {			
				$(_this.display.staticTextContentContainer).find('.contentContainer').html(cleanContent);// Static display
				_this.updateCharacterCount();	
				_this.initTextContentActions();
				_this.toggleStaticTextRendering();
			}
			
			_this.styles.updateStyleReferenceCSS();
			_this.updateBackgroundColor();
			_this.updateCanvasSize();
		},

		toggleViewTagPanel: function(){
			var targetBody = $(getTopFrame().document).find('body');
			targetBody.viewTagPanel($(this.display.staticTextContentContainer).find('.contentContainer'));

		},
		
		toggleFullScreenDisplay : function() {
			var _this = this;

			var targetWidth = 1000;
			var targetBody = null;
			var targetWindow = window;

			while ( targetWindow && targetWindow != null ) {
				var targetContainer = $(targetWindow.document).find('.fullscreenBoundary');
				if ( $(targetContainer).length != 0) {
					targetWidth = $(targetContainer).length != 0 ? $(targetContainer).outerWidth() : targetWidth;
					targetBody = $(targetWindow.document).find('body');
					break;
				}
				
				if ( targetWindow == getTopFrame() )
					break;
				targetWindow = targetWindow.parent;
			}

			if ( targetBody != null ) {
				if (_this.data.fullscreen.is_fullscreen) {

					$(_this.display.staticTextContentContainer).find('.contentEditor_fullScreenToggle').removeClass('actionBtn_selected').addClass('actionBtn');
					$(targetBody).find('.fullscreenContainer,.fullscreenContentCSS,.modal-backdrop').remove();
					$(targetBody).removeClass('modal-open');

				} else {

					$(targetBody)
						.append(
							"<div id=\"contentFullscreen\" class=\"fullscreenContainer modal fade\" tabindex=\"-1\" role=\"dialog\" aria-modal=\"true\" style=\"display: block;\">" +
							"	<div class=\"modal-dialog\"  role=\"document\" style=\"width: " + targetWidth + "px; max-width: " + targetWidth + "px;\">" +
							"		<div class=\"modal-content\">" +
							"			<div class=\"modal-ribbon bg-ribbon position-absolute rounded-top py-1 w-100\" role=\"presentation\"></div>" +
							"			<div class=\"modal-header\">" +
							"				<h4 class=\"modal-title\">" + client_messages.title.expanded_content_view + "</h4>" +
							"				<button type=\"button\" class=\"close position-relative\" data-dismiss=\"modal\">" +
							"					<i class=\"far fa-times m-2\" aria-hidden=\"true\"></i>" +
							"				</button>" +
							"			</div>" +
							"			<div class=\"modal-body p-0\" style=\"min-height: 8rem;\">" +
							"      	 	<div class=\"px-3\">" +
							"   				<div class=\"px-3 pb-4 fullscreenContentContainer\">" +
							"     				</div>" +
							"  				</div>" +
							" 			</div>" +
							"		</div>" +
							"	</div>" +
							"</div>");

					$(targetBody).find('.fullscreenContentContainer')
						.append( $(_this.display.staticTextContentContainer).find('.contentEditor_StaticTextContentContainer').clone() );
					targetWindow.triggerModal( $(targetBody).find('#contentFullscreen') );
					$(targetBody).find('.fullscreenContainer .contentEditor_StaticTextContentContainer').css({'max-width':'auto', 'max-height':'auto', 'border': '1px solid #eee'});
					$(targetBody).find('.fullscreenContainer button.close').click( function() {
						_this.toggleFullScreenDisplay();
					});

					// Copy content CSS to append container
					$('head link').each( function() {
						if ( $(this).attr('href').indexOf('textStyles.css') != -1 || $(this).attr('href').indexOf('paragraphStyles.css') != -1 ||
							$(this).attr('href').indexOf('listStyles.css') != -1 || $(this).attr('href').indexOf('defaultViewStyles.css') != -1 ) {
							var cssLink = $(this).clone();
							$(cssLink).addClass('fullscreenContentCSS');
							$(targetBody).closest('html').find('head').append( $(cssLink) );
						}
					});
					var localCssLink = $(_this.styles.css_container).clone();
					$(localCssLink).addClass('fullscreenContentCSS');
					$(targetBody).closest('html').find('head').append( $(localCssLink) );
					$(targetBody).closest('html').find('head').append( "<link rel=\"stylesheet\" class=\"fullscreenContentCSS\" href=\"" + context + "/includes/javascript/jQueryPlugins/contentEditor/contentEditor.css\" >" );

					// Init content interactions
					_this.initTextContentActions( $(targetBody).find('.fullscreenContentContainer') )

				}

				_this.data.fullscreen.is_fullscreen = !_this.data.fullscreen.is_fullscreen;

			}

		},

        snapOutDefaultLanguage: function (position) {

			var _this = this,
				popupTemplate = '	<div id="editorDefaultContentViewPanel" class="box-shadow-4 rounded m-4 position-absolute overflow-hidden z-sticky">' +
					'                            <div class="bg-secondary position-absolute rounded-top pt-1 w-100"' +
					'                                 role="presentation"></div>' +
					'                            <div class="d-flex flex-column flex-grow-1 box-shadow-3 bg-white rounded w-100 h-100 pt-1">' +
					'                                <h5 class="d-flex align-items-stretch mb-0 py-3 px-4 cursor-draggable">' +
					'                                   <span class="text-uppercase align-self-center fs-sm mr-auto">' +
					'                                        <span class="mr-2 opacity-65">' +
					'                                             <i class="far fa-ellipsis-v" aria-hidden="true"></i>' +
					'                                             <i class="far fa-ellipsis-v" aria-hidden="true"></i>' +
					'                                        </span>' +
					'                                        <span class="snapin-title">Default language</span>' +
					'                                    </span>' +
					'                                    <button class="snapoff-btn btn btn-blank btn-icon py-0 px-2 border-0 mr-n2"' +
					'                                            aria-label="Close" type="button">' +
					'                                        <i class="far fa-times fs-md text-dark"' +
					'                                           aria-hidden="true"></i>' +
					'                                    </button>' +
					'                                </h5>' +
					'                                <div class="flex-grow-1 px-4 py-3 border-top overflow-auto">' +
					'										<div class="contentEditor_StaticTextContentContainer">' +
					'                                     		<div id="defaultlanguageContentView" class="contentEditor_OuterPageContainer contentContainer h-100"></div>' +
					'										</div>' +
					'                                </div>' +
					'                                <div class="flex-grow-1 px-4 py-2 border-top">' +
					'                                	<button id="copyFromDefaultLanguage" type="button" class="btn btn-outline-dark">' + client_messages.text.copy_from_default_language +
					'									</button>' +
					'                                </div>' +
					'                            </div>' +
					'                        </div>',
				$defaultLanguagePopup = $(popupTemplate),
				$defaultLanguagePanel = $defaultLanguagePopup.find('#defaultlanguageContentView'),
				$closeBtn = $defaultLanguagePopup.find(".snapoff-btn"),
				$copyFromDefaultLanguageBtn = $defaultLanguagePopup.find("#copyFromDefaultLanguage");

			var $staticContainer = $(_this.display.staticTextContentContainer).find('.contentEditor_StaticTextContentContainer').children(".contentContainer"),
				styles = $staticContainer.attr("style");
			$defaultLanguagePanel.attr("style", styles);
			$defaultLanguagePopup.css({top: position.top, left: position.left});

			function setAsyncContent (contentContainer) {
				var requestParam = 	"contentType=contentObject" +
					"&selectionStatusId=1" +
					"&getStyles=false" +
					(_this.data.contentObjectId ? ( "&contentItemId=" + _this.data.contentObjectId ) : "") +
					(_this.data.contentObjectDna ? ( "&objectDna=" + _this.data.contentObjectDna ) : "") +
					"&statusViewId=" + _this.data.statusViewId +
					"&localeId=" + _this.data.defaultLocaleId;
				var stampDate = new Date();
				$.ajax({
					type: "GET",
					url: context + "/getContentPreview.form?" + requestParam + "&cacheStamp=" + (stampDate.getTime()),
					dataType: "json",
					success: function(data) {
						contentContainer.html('');
						for (var contentIndex = 0; contentIndex < data.contents.length; contentIndex++) {
							contentContainer.append($('<div/>').html(data.contents[contentIndex].text_content).text());
						}
					}
				});
			}

			if (_this.data.hasDefaultLanguage) {
				if (_this.data.usage == "edit")
					$defaultLanguagePanel.append($(window.parent.tinymce.activeEditor.getBody()).contents().clone());
				else $defaultLanguagePanel.append($staticContainer.contents().clone());
			} else setAsyncContent($defaultLanguagePanel);

			if (!_this.data.wasDefaultLanguangePersisted && _this.data.usage == "edit") {
				window.parent.tinymce.activeEditor.on("change", function () {
					if (_this.data.languageSnapOutPanel && _this.data.defaultLocaleId === _this.data.currentLocaleId)
						_this.data.languageSnapOutPanel.html($(this.getBody()).contents().clone());
				});
			}

			$closeBtn.on("click", function () {
				$defaultLanguagePopup.remove();
				_this.defaultLanguagePopOutBtn.children(".btn").prop("disabled", false);
				_this.data.isDefaultLanguangePersisted = false;
				_this.data.languageSnapOutPanel = null;
			});

			$copyFromDefaultLanguageBtn.on("click", function () {
				var currentGroup = $(_this.getCurrentGroup());
				var defaultContent = currentGroup.find("[localeId='" + _this.data.defaultLocaleId + "'] textarea").val()
					|| $('#defaultlanguageContentView').html();

				if (defaultContent) {
					var currentEditorBinding = _this.getCurrentEditorBinding();
					currentEditorBinding.val(defaultContent);
					_this.setTextContent(defaultContent);
				}
			});

			if (_this.data.usage == "edit")
				_this.display.container.append($defaultLanguagePopup);
			else $(getTopFrame().document).find('body').append($defaultLanguagePopup);

			_this.data.isDefaultLanguangePersisted = true;
			_this.data.wasDefaultLanguangePersisted = true;
			_this.data.languageSnapOutPanel = $defaultLanguagePanel;

			$defaultLanguagePopup.width(560).draggable({
				scroll: false,
				handle: ".cursor-draggable"
			}).resizable({
				minHeight: 145,
				maxHeight: 720,
				minWidth: 400,
				maxWidth: (_this.display.container.innerWidth() - 2)
			});
		},

		updateDefaultLanguagePopOutVisibility: function () {
			var _this = this,
				location = parent.document.location.href,
				isFastViewOrEdit = (location.indexOf('fastView=true') >= 0 || location.indexOf('fastEdit=true') >= 0);

			if (_this.data.currentContentType === "text" && !isFastViewOrEdit) {
				if (!_this.data.hasDefaultLanguage || (_this.data.hasDefaultLanguage && (_this.data.defaultLocaleId === _this.data.currentLocaleId))) {
					_this.defaultLanguagePopOutBtn.removeClass("d-none");
				} else {
					_this.defaultLanguagePopOutBtn.addClass("d-none");
				}
			} else {
				_this.defaultLanguagePopOutBtn.addClass("d-none");
			}
		},
		
		toggleStaticTextRendering : function() {
			var _this = this;

			var isRenderContentDisplay = _this.display.staticTextContentContainer.find('.contentEditor_contentRenderToggle').is('.actionBtn_selected');
			var isSampleValueDisplay = _this.display.staticTextContentContainer.find('.contentEditor_sampleValueToggle').is('.actionBtn_selected');
			
			// SMART TEXT: Label/content rendering
			$(_this.display.staticTextContentContainer).find('.renderedContentContainer').each( function() {
				var varEle = $(this).closest('.staticContentItem');
				if ( isRenderContentDisplay ) {
					$(varEle).children('.renderedLabelContainer').hide();
					$(varEle).children('.renderedContentContainer').show();
				} else {
					$(varEle).children('.renderedContentContainer').hide();
					$(varEle).children('.renderedLabelContainer').show();
				}
			});
			// VARIABLES: Label/sample value rendering
			$(_this.display.staticTextContentContainer).find('.renderedSampleValueContainer').each( function() {
				var varEle = $(this).closest("[type='1']");
				if ( isSampleValueDisplay ) {
					$(varEle).children('.renderedLabelContainer').hide();
					$(varEle).children('.renderedSampleValueContainer').show();
				} else {
					$(varEle).children('.renderedSampleValueContainer').hide();
					$(varEle).children('.renderedLabelContainer').show();
				}
			});		
		},

		toggleStaticTextTagDisplay : function() {

			var _this = this;

			var targetBody = null;
			var targetWindow = window;

			while ( targetWindow && targetWindow != null ) {
				var targetContainer = $(targetWindow.document).find('.fullscreenBoundary');
				if ( $(targetContainer).length != 0) {
					targetBody = $(targetWindow.document).find('body');
					break;
				}

				if ( targetWindow == getTopFrame() )
					break;
				targetWindow = targetWindow.parent;
			}

			if ( targetBody != null ) {

				if (_this.data.source_editor.is_displayed) {

					$(_this.display.staticTextContentContainer).find('.contentEditor_tagTextToggle').removeClass('actionBtn_selected').addClass('actionBtn');
					$(targetBody).find('.sourceDisplayModal,.modal-backdrop').remove();
					$(targetBody).removeClass('modal-open');

				} else {

					$(targetBody)
						.append(
							"<div class=\"modal fade sourceDisplayModal\" id=\"sourceDisplayModal\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"sourceDisplayModalLabel\" aria-hidden=\"true\">\n" +
							"	<div class=\"modal-dialog modal-lg\" style=\"max-width: 870px; min-width: 870px;\" role=\"document\">\n" +
							"  	  <div class=\"modal-content\">\n" +
							"   	     <div class=\"bg-ribbon position-absolute rounded-top py-1 w-100\" role=\"presentation\"></div>\n" +
							"   	     <div class=\"modal-header border-0\">\n" +
							"   	         <h4 class=\"modal-title\" id=\"sourceDisplayModalLabel\"> Source </h4>\n" +
							"    	        <button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\">\n" +
							"   	             <i class=\"far fa-times m-2\" aria-hidden=\"true\"></i>\n" +
							"    	        </button>\n" +
							"    	    </div>\n" +
							"    	    <div class=\"modal-body pt-0\">\n" +
							"       	     <div class=\"row no-gutters\">\n" +
							"        	        <div id=\"sourceEditor\" class=\"sourceEditor\"></div>\n" +
							"       	     </div>\n" +
							"       	 </div>\n" +
							"   	 </div>\n" +
							"	</div>\n" +
							"</div>"
						);

					if ( $(targetBody).find('#sourceEditorStyle').length == 0 ) {
						$(targetBody).append(
							"<style id=\"sourceEditorStyle\" type=\"text/css\" media=\"screen\">\n" +
							"	.sourceEditor {\n" +
							"		width: 1000px;\n" +
							"		height: 500px;\n" +
							"		text-align: left;\n" +
							"	}\n" +
							"</style>"
						);
					}

					var clone = $(_this.display.staticTextContentContainer).find('.contentContainer').clone();
					var source = $(clone).html();

					targetWindow.toggleCodeEditor(source,"sourceEditor");
					targetWindow.triggerModal( $(targetBody).find('#sourceDisplayModal') );
					$(targetBody).find('.sourceDisplayModal button.close').click( function() {
						_this.toggleStaticTextTagDisplay();
					});

				}

				_this.data.source_editor.is_displayed = !_this.data.source_editor.is_displayed;

			}

		},

		toggleEncodedContentDisplay : function() {

			var _this = this;

			var targetBody = null;
			var targetWindow = window;

			while ( targetWindow && targetWindow != null ) {
				var targetContainer = $(targetWindow.document).find('.fullscreenBoundary');
				if ( $(targetContainer).length != 0) {
					targetBody = $(targetWindow.document).find('body');
					break;
				}

				if ( targetWindow == getTopFrame() )
					break;
				targetWindow = targetWindow.parent;
			}

			if ( targetBody != null ) {

				if (_this.data.encoded_content_editor.is_displayed) {

					$(_this.display.staticTextContentContainer).find('.contentEditor_encodedContentToggle').removeClass('actionBtn_selected').addClass('actionBtn');
					$(targetBody).find('.sourceDisplayModal,.modal-backdrop').remove();
					$(targetBody).removeClass('modal-open');

				} else {

					$(targetBody)
						.append(
							"<div class=\"modal fade encodedContentDisplayModal\" id=\"encodedContentDisplayModal\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"encodedContentDisplayModalLabel\" aria-hidden=\"true\">\n" +
							"	<div class=\"modal-dialog modal-lg\" style=\"max-width: 870px; min-width: 870px;\" role=\"document\">\n" +
							"  	  <div class=\"modal-content\">\n" +
							"   	     <div class=\"bg-ribbon position-absolute rounded-top py-1 w-100\" role=\"presentation\"></div>\n" +
							"   	     <div class=\"modal-header border-0\">\n" +
							"   	         <h4 class=\"modal-title\" id=\"encodedContentDisplayModalLabel\"> Encoded Content </h4>\n" +
							"    	        <button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\">\n" +
							"   	             <i class=\"far fa-times m-2\" aria-hidden=\"true\"></i>\n" +
							"    	        </button>\n" +
							"    	    </div>\n" +
							"    	    <div class=\"modal-body pt-0\">\n" +
							"       	     <div class=\"row no-gutters\">\n" +
							"        	        <div id=\"encodedContentEditor\" class=\"encodedContentEditor\"></div>\n" +
							"       	     </div>\n" +
							"       	 </div>\n" +
							"   	 </div>\n" +
							"	</div>\n" +
							"</div>"
						);

					if ( $(targetBody).find('#encodedContentEditorStyle').length == 0 ) {
						$(targetBody).append(
							"<style id=\"encodedContentEditorStyle\" type=\"text/css\" media=\"screen\">\n" +
							"	.encodedContentEditor {\n" +
							"		width: 1000px;\n" +
							"		height: 500px;\n" +
							"		text-align: left;\n" +
							"	}\n" +
							"</style>"
						);
					}

					var encodedContentSource = _this.getCurrentContentEntry().find('.encodedContentInput textarea').val();

					targetWindow.toggleCodeEditor(encodedContentSource,"encodedContentEditor");
					targetWindow.triggerModal( $(targetBody).find('#encodedContentDisplayModal') );
					$(targetBody).find('.encodedContentDisplayModal button.close').click( function() {
						_this.toggleEncodedContentDisplay();
					});

				}

				_this.data.encoded_content_editor.is_displayed = !_this.data.encoded_content_editor.is_displayed;

			}

		},

		updateCanvasSize : function() {
			var _this = this;
			
			var dimensions = _this.getCurrentGroup().attr('canvasDimensions');
			var rotation = _this.getCurrentGroup().attr('canvasRotation');
			if ( dimensions == undefined || dimensions == "null" || dimensions == "" || dimensions == "0.0:0.0" || dimensions == "0:0" )
				dimensions = "8.5:11";

			var dimensionParts = dimensions.split(":");
			var width = parseFloat(dimensionParts[0]);
			var height = parseFloat(dimensionParts[1]);

			var extraWidth = 0;
			var $editorContainer = $(_this.display.container).find('.mce-tinymce');

			if ( $editorContainer.length > 0 && $editorContainer.is(':visible') ) {
				var editor = tinyMCE.get('contentEditor_EditorTextarea_'+_this.data.instId);
				if (editor.settings.is_new_editor) {
					extraWidth = 0.64; // Extra space for floating elements
				}
			}

			var canvasWidth  = width + extraWidth;
			var canvasHeight = height;
			
			// Static display
			if ( _this.data.applies_canvas_dimension != false ) {

				var viewCanvasWidth = canvasWidth;
				var viewCanvasHeight = canvasHeight;
				if ( rotation == 90 || rotation == 270 ) {
					viewCanvasWidth  = height + extraWidth;
					viewCanvasHeight = width;
				}

				$(_this.display.staticTextContentContainer).find('.contentContainer').css({ width: (viewCanvasWidth * 100) + 'px'}); 	
				if ( _this.data.applies_freeform )
					$(_this.display.staticTextContentContainer).find('.contentContainer').css({ height: (viewCanvasHeight * 100) + 'px'});
				else
					$(_this.display.staticTextContentContainer).find('.contentContainer').css({ 'min-height': (viewCanvasHeight * 100) + 'px'}); 
			}
			
			if ( $editorContainer.length > 0 && $editorContainer.is(':visible') ) { // Editor
				var editor = tinyMCE.get('contentEditor_EditorTextarea_'+_this.data.instId);
				if ( _this.data.applies_freeform && editor.settings.canvas != undefined && editor.settings.canvas.fn != undefined && $.isFunction(editor.settings.canvas.fn.set_canvas_size) )
					editor.settings.canvas.fn.set_canvas_size( canvasWidth, canvasHeight, rotation );
				else if ( editor.settings.mp_fn != undefined && $.isFunction(editor.settings.mp_fn.set_editable_area_size) ) 
					editor.settings.mp_fn.set_editable_area_size( canvasWidth, canvasHeight, rotation );
			}
			
		},
		
		updateBackgroundColor : function() {
			var _this = this;
			
			var backgroundColor = _this.getCurrentGroup().attr('backgroundColor');
			if ( backgroundColor == undefined || backgroundColor == 'transparent' )
				backgroundColor = "FFFFFF";

			if ( $(_this.display.container).find('.mce-tinymce').length > 0
					&& $(_this.display.container).find('.mce-tinymce').is(':visible') ) {					// Editor
				var editor = tinyMCE.get('contentEditor_EditorTextarea_'+_this.data.instId);
				$(editor.getDoc()).find('body').css({ backgroundColor: "#"+backgroundColor });
			}
			$(_this.display.staticTextContentContainer).find('.contentContainer').css({ backgroundColor: "#"+backgroundColor }); 	// Static display
		},
	
		setSameAsDisplay: function() {
			var _this = this;
			var sameAsLabel = $(_this.content.bindingContainer).attr('globalRefObjLabel');

			// Info Display: Set sameAs label
			$(_this.display.sameAsContainer).find('.contentEditor_SameAsLabel').html(sameAsLabel);

			// Info Display: Show 'No content' when content is empty
			$(_this.display.sameAsContainer).find('.contentEditor_SameAsNoContent').hide();
			
			var content = "";
			if (_this.data.currentContentType == "text")
				content = _this.getCurrentEditorBinding().val();
			else if (_this.data.currentContentType == "graphic")
				content = _this.getCurrentGraphicFilePath();
			
			if (content.length == 0)
				$(_this.display.sameAsContainer).find('.contentEditor_SameAsNoContent').show();
		},
		
		setSharedDisplay: function() {
			var _this = this;
			var sharedLabel = _this.getCurrentGroup().attr('groupRefObjLabel');

			var content = "";
			if (_this.data.currentContentType == "text")
				content = _this.getCurrentEditorBinding().val();
			else if (_this.data.currentContentType == "graphic")
				content = _this.getCurrentGraphicFilePath();

			// Info Display: Set sameAs label
			$(_this.display.sharedContainer).find('.contentEditor_SharedLabel').html(sharedLabel);

			// Info Display: Show 'No content' when content is empty
			$(_this.display.sharedContainer).find('.contentEditor_SharedNoContent').hide();
			if (content.length == 0)
				$(_this.display.sharedContainer).find('.contentEditor_SharedNoContent').show();
		},
		
		setState : function(globalState, groupState, contentState) {
			var _this = this;
			
			//console.log(globalState+":"+groupState+":"+contentState);
			
			$(_this.display.editorContainer).hide();
			$(_this.display.graphicUpdateContainer).hide();
			$(_this.display.sameAsContainer).hide();
			$(_this.display.staticTextContentContainer).hide();
			$(_this.display.staticGraphicContentContainer).hide();
			$(_this.display.suppressContainer).hide();
			$(_this.display.noStateContainer).hide();
			$(_this.display.leaveEmptyContainer).hide();
			$(_this.display.characterCountContainer).hide();
			$(_this.display.sharedContainer).hide();
			$(_this.display.staticGraphicContentContainer).find('.contentEditor_GraphicAppliedImageNameContainer').hide();
			$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageLinkContainer').hide();
			$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageAltTextContainer').hide();
			$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageExtLinkContainer').hide();
			$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageExtPathContainer').hide();
			$(_this.display.graphicImageLinkContainer).hide();
			$(_this.display.graphicImageAltTextContainer).hide();
			$(_this.display.graphicImageExtLinkContainer).hide();
			$(_this.display.graphicImageExtPathContainer).hide();
			$(_this.display.sameAsDefaultContainer).hide();
			$(_this.display.sameAsDefaultBarContainer).hide();
			$(_this.display.sameAsParentContainer).hide();

			if (groupState == "leaveEmpty") {
				$(_this.display.leaveEmptyContainer).show();
			} else if (groupState == "shared") {
				_this.setSharedDisplay();
				$(_this.display.sharedContainer).show();
				if (_this.data.currentContentType == "text")
					$(_this.display.staticTextContentContainer).show();
				if (_this.data.currentContentType == "graphic")
					$(_this.display.staticGraphicContentContainer).show();
			} else if (globalState == "sameAs") {
				_this.setSameAsDisplay();
				$(_this.display.sameAsContainer).show();
				if (_this.data.currentContentType == "text")
					$(_this.display.staticTextContentContainer).show();
				if (_this.data.currentContentType == "graphic"){
					$(_this.display.staticGraphicContentContainer).show();
					$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageLinkContainer').show();
					$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageAltTextContainer').show();
					$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageExtLinkContainer').show();
					$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageExtPathContainer').show();
				}
			} else if (globalState == "suppress") {
				$(_this.display.suppressContainer).show();
			} else if (globalState == "none") {
				$(_this.display.noStateContainer).show();
			} else if (contentState == "sameAsDefault") {
				$(_this.display.sameAsDefaultContainer).show();
				$(_this.display.sameAsDefaultBarContainer).show();
				if (_this.data.currentContentType == "text")
					$(_this.display.staticTextContentContainer).show();
				if (_this.data.currentContentType == "graphic")
					$(_this.display.staticGraphicContentContainer).show();
			} else if (contentState == "sameAsParent") {
				$(_this.display.sameAsParentContainer).show();
				if (_this.data.currentContentType == "text")
					$(_this.display.staticTextContentContainer).show();
				if (_this.data.currentContentType == "graphic")
					$(_this.display.staticGraphicContentContainer).show();
			} else if (globalState == "edit" && groupState != "leaveEmpty" &&  groupState != "shared") {
				$(_this.display.sameAsDefaultContainer).show();
				$(_this.display.sameAsParentContainer).show();
				if (_this.data.currentContentType == "text") {
					if (_this.data.usage == "edit") {
						if (_this.getCurrentSameAsDefaultInputContainer().find('input[type=checkbox].sameAsDefaultInput:checked').length < 1) {
							$(_this.display.editorContainer).show();
						} else  $(_this.display.staticTextContentContainer).show();
					} else if (_this.data.usage == "view")
						$(_this.display.staticTextContentContainer).show();
				}
				if (_this.data.currentContentType == "graphic") {
					$(_this.display.staticGraphicContentContainer).show();
					if (_this.data.usage == "edit"){
						if (_this.getCurrentSameAsDefaultInputContainer().find('input[type=checkbox].sameAsDefaultInput:checked').length < 1)
							$(_this.display.graphicUpdateContainer).show();
						else $(_this.display.graphicUpdateContainer).hide();
						$(_this.display.graphicImageLinkContainer).show();
						$(_this.display.graphicImageAltTextContainer).show();
						$(_this.display.graphicImageExtLinkContainer).show();
						$(_this.display.graphicImageExtPathContainer).show();
						_this.setCurrentImageLibraryToggle();	
					}
				}
			}
			
			if ( _this.data.currentContentType == "graphic" ) {
				if( !$(_this.display.graphicUpdateContainer).is(':visible')){
					$(_this.display.staticGraphicContentContainer).find('.contentEditor_GraphicAppliedImageNameContainer').show();
				}
				if(!$(_this.display.graphicImageLinkContainer).is(':visible')){
					$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageLinkContainer').show();
				}
				if(!$(_this.display.graphicImageAltTextContainer).is(':visible') && !$(_this.display.graphicImageLinkContainer).is(':visible')){
					$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageAltTextContainer').show();
				}
				if(!$(_this.display.graphicImageExtLinkContainer).is(':visible') && !$(_this.display.graphicImageLinkContainer).is(':visible')
					&& !$(_this.display.graphicImageAltTextContainer).is(':visible')){
					$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageExtLinkContainer').show();
				}
				if(!$(_this.display.graphicImageExtPathContainer).is(':visible') && !$(_this.display.graphicImageLinkContainer).is(':visible')
					&& !$(_this.display.graphicImageAltTextContainer).is(':visible')){
					$(_this.display.staticGraphicContentContainer).find('.contentEditor_StaticGraphicImageExtPathContainer').show();
				}
				$(_this.display.referencingImageLibraryContainer).show();
				$(_this.display.referencingImageLibraryContainer).find("[class*='contentEditor_ReferencingImageLibraryContainer_'],[class*='contentEditor_ReferencingLocalImageLibraryContainer_']").hide();
				if ( _this.getCurrentGraphicInputsContainer().find('.useContentLibraryToggle input').is(':checked') )
					$(_this.display.referencingImageLibraryContainer).find(".contentEditor_ReferencingImageLibraryContainer_"+_this.data.currentContentGroupId+"_"+_this.data.currentLocaleId).show();
				else if ( _this.getCurrentGraphicInputsContainer().find('.useLocalContentLibraryToggle input').is(':checked') )
					$(_this.display.referencingImageLibraryContainer).find(".contentEditor_ReferencingLocalImageLibraryContainer_"+_this.data.currentContentGroupId+"_"+_this.data.currentLocaleId).show();
				
			} else {
				$(_this.display.referencingImageLibraryContainer).hide();
			}
			
			if ( $(_this.display.staticTextContentContainer).is(':visible') && _this.data.charCountEnabled)
				$(_this.display.characterCountContainer).show();

			_this.setSpellcheckLang();

			_this.changeContentActionsContainer();
			
			_this.resizeContentArea();
		},
		setSpellcheckLang : function() {
			var _this = this;
			// WEBSPELLCHECKER Language Toggle
			if ( window.WEBSPELLCHECKER != undefined ) {
				var wscInstances = window.WEBSPELLCHECKER.getInstances();
				if (wscInstances.length === 0 && window.WEBSPELLCHECKER_CONTAINER) {
					window.WEBSPELLCHECKER.init({
						container: window.WEBSPELLCHECKER_CONTAINER
					});
				}
				var spellcheckLang  = this.getSpellcheckerLang(_this);

				if (wscInstances != undefined && wscInstances != null && wscInstances.length > 0) {
					wscInstances[0].setLang(spellcheckLang);
				}
					window.WEBSPELLCHECKER_CONFIG.lang = spellcheckLang;
			} else {
				window.spellcheckLang = this.getSpellcheckerLang(_this);
			}
			// END WEBSPELLCHECKER Language Toggle
		},
		getSpellcheckerLang : function(_this){
			var spellcheckLang = "en_US";
			if (global_data.spellcheck_languages != undefined && global_data.spellcheck_languages != null) {
				for (var i = 0; i < global_data.spellcheck_languages.length; i++)
					if (global_data.spellcheck_languages[i].locale_id == _this.data.currentLocaleId)
						spellcheckLang = global_data.spellcheck_languages[i].code + "_" + global_data.spellcheck_languages[i].locale;
			}
			// Convert unsupported lang/locale combinations
			if (global_data.lang_support_conversions != undefined)
				for (var i = 0; i < global_data.lang_support_conversions.length; i++) {
					if (global_data.lang_support_conversions[i].target == spellcheckLang)
						spellcheckLang = global_data.lang_support_conversions[i].replacement;
				}
			return spellcheckLang;
		},
		toggleSameAsDefault : function (checkboxEle) {
			var _this = this;
			$(getTopFrame().document).find('#mce_summarize_panel,#mce_sentiment_panel,#mce_readability_panel, #iFramePopup_brandCheck, #mce_plain_panel, #mce_translate_panel, #iFramePopup_contentCompare').remove();
			var currentGlobalState = $(_this.content.bindingContainer).attr('globalState');
			var currentGroupState = $(_this.getCurrentGroup()).attr('groupState');

			if ( $(checkboxEle).is(':checked') ) {
				_this.getCurrentContentEntry().attr('contentState','sameAsDefault');
				
				if (_this.data.currentContentType == "text") {
					var defaultContent = $( _this.getCurrentGroup() ).find("[localeId='"+_this.data.defaultLocaleId+"'] textarea").val();
					_this.getCurrentEditorBinding().val( defaultContent );
					_this.setTextContent( _this.getCurrentEditorBinding().val() );
				} else if (_this.data.currentContentType == "graphic") {
					var defaultLangGraphicContainer 	= 	$(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicInputsContainer_'+_this.data.currentContentGroupId+'_'+_this.data.defaultLocaleId);
					var defaultLangImageName 			=	$(defaultLangGraphicContainer).find('.graphicFileInput').attr('imgName');
					var defaultLangImagePath 			=	$(defaultLangGraphicContainer).find('.graphicFileInput').attr('imgPath');
					var defaultLangAppliedImageName 	=	$(defaultLangGraphicContainer).find('.graphicAppliedImageName input').val();
					var defaultLangUploadDate 			= 	$(defaultLangGraphicContainer).find('.graphicFileInput').attr('uploadDate');
					var defaultLangCmsLastUpdateDate 	= 	$(defaultLangGraphicContainer).find('.cmsAssetInfo').attr('cmsLastUpdateDate');
					var defaultLangCmsLastSyncDate 		= 	$(defaultLangGraphicContainer).find('.cmsAssetInfo').attr('cmsLastSyncDate');

					var currentContentEntry	= _this.getCurrentGraphicInputsContainer();
					$(currentContentEntry).find('.graphicFileInput').attr('imgName', defaultLangImageName);
					$(currentContentEntry).find('.graphicFileInput').attr('imgPath', defaultLangImagePath);
					$(currentContentEntry).find('.graphicFileInput ').attr('uploadDate', defaultLangUploadDate);
					$(currentContentEntry).find('.graphicAppliedImageName input').val(defaultLangAppliedImageName);
					$(currentContentEntry).find('.cmsAssetInfo').attr('cmsLastUpdateDate', defaultLangCmsLastUpdateDate);
					$(currentContentEntry).find('.cmsAssetInfo').attr('cmsLastSyncDate', defaultLangCmsLastSyncDate);
					
					_this.setGraphicContent( _this.getCurrentGraphicFileName(), 
							_this.getCurrentGraphicFilePath(), 
							_this.getCurrentGraphicUploadDate(), 
							_this.getCurrentGraphicAppliedImageName() );
					_this.setCmsAssetInfo(_this.getCurrentGraphicCmsLastUpdateDate(), _this.getCurrentGraphicCmsLastSyncDate());
					
					// IMAGE LINK
					var defaultLangImageLinkContainer 	= 	$(_this.display.graphicImageLinkContainer).find('.contentEditor_GraphicImageLinkContainer_'+_this.data.currentContentGroupId+'_'+_this.data.defaultLocaleId);
					var defaultLangImageLink			= 	$(defaultLangImageLinkContainer).find('.graphicImageLink .imageLinkInput').val();
					var currentImageLinkEntry			= 	_this.getCurrentGraphicImageLinkContainer();
					$(currentImageLinkEntry).find('.graphicImageLink .imageLinkInput').val(defaultLangImageLink);
					_this.setGraphicImageLink(_this.getCurrentGraphicImageLink());
					
					// IMAGE ALT TEXT
					var defaultLangImageAltTextContainer 	= 	$(_this.display.graphicImageAltTextContainer).find('.contentEditor_GraphicImageAltTextContainer_'+_this.data.currentContentGroupId+'_'+_this.data.defaultLocaleId);
					var defaultLangImageAltText				= 	$(defaultLangImageAltTextContainer).find('.graphicImageAltText .imageAltTextInput').val();
					var currentImageAltTextEntry			= 	_this.getCurrentGraphicImageAltTextContainer();
					$(currentImageAltTextEntry).find('.graphicImageAltText .imageAltTextInput').val(defaultLangImageAltText);
					_this.setGraphicImageAltText(_this.getCurrentGraphicImageAltText());

					// IMAGE EXTERNAL LINK
					var defaultLangImageExtLinkContainer 	= 	$(_this.display.graphicImageExtLinkContainer).find('.contentEditor_GraphicImageExtLinkContainer_'+_this.data.currentContentGroupId+'_'+_this.data.defaultLocaleId);
					var defaultLangImageExtLink				= 	$(defaultLangImageExtLinkContainer).find('.graphicImageExtLink .imageExtLinkInput').val();
					var currentImageExtLinkEntry			= 	_this.getCurrentGraphicImageExtLinkContainer();
					$(currentImageExtLinkEntry).find('.graphicImageExtLink .imageExtLinkInput').val(defaultLangImageExtLink);
					_this.setGraphicImageExtLink(_this.getCurrentGraphicImageExtLink());

					// IMAGE EXTERNAL PATH
					var defaultLangImageExtPathContainer 	= 	$(_this.display.graphicImageExtPathContainer).find('.contentEditor_GraphicImageExtPathContainer_'+_this.data.currentContentGroupId+'_'+_this.data.defaultLocaleId);
					var defaultLangImageExtPath				= 	$(defaultLangImageExtPathContainer).find('.graphicImageExtPath .imageExtPathInput').val();
					var currentImageExtPathEntry			= 	_this.getCurrentGraphicImageExtPathContainer();
					$(currentImageExtPathEntry).find('.graphicImageExtPath .imageExtPathInput').val(defaultLangImageExtPath);
					_this.setGraphicImageExtPath(_this.getCurrentGraphicImageExtPath());
				}
				_this.setState(currentGlobalState, currentGroupState, "sameAsDefault");
			} else {
				if ( _this.data.currentContentType == "graphic" ) {
					// Default is ref image library: Clear current img on 'same as' toggle
					_this.resetImageLibraryTypeSelect();
					var defaultLangGraphicContainer = $(_this.display.graphicUpdateContainer).find('.contentEditor_GraphicInputsContainer_' + _this.data.currentContentGroupId + '_' + _this.data.defaultLocaleId);
					if ($(defaultLangGraphicContainer).find('.useContentLibraryToggle input').is(':checked') || $(defaultLangGraphicContainer).find('.useLocalContentLibraryToggle input').is(':checked')) {
						_this.setGraphicContent("", "", "", "");
						_this.setCmsAssetInfo("", "");
						_this.setGraphicImageLink("");
						_this.setGraphicImageAltText("");
						_this.setGraphicImageExtLink("");
						_this.setGraphicImageExtPath("");
					}
				}
				_this.getCurrentContentEntry().attr('contentState','inherit');
				_this.setState(currentGlobalState, currentGroupState);
				_this.initTextContentActions();
			}
		},
		
		toggleSameAsParent : function (checkboxEle) {
			var _this = this;
			
			var currentGlobalState = $(_this.content.bindingContainer).attr('globalState');
			var currentGroupState = $(_this.getCurrentGroup()).attr('groupState');

			if ( $(checkboxEle).is(':checked') ) {
				_this.getCurrentContentEntry().attr('contentState','sameAsParent');

				if ( _this.data.currentContentType == "graphic" ) {
					_this.processParentDataContent();
				}

				_this.setState(currentGlobalState, currentGroupState, "sameAsParent");
				$(_this.display.staticGraphicContentContainer).show();

			} else {
				if ( _this.data.currentContentType == "graphic" ) {
					_this.resetImageLibraryTypeSelect();
					_this.getCurrentGraphicInputsContainer().each( function(){
						$(this).find('.graphicFileInput').attr('imgPath','');
						$(this).find('.graphicFileInput').attr('imgName','');
						$(this).find('.graphicFileInput').attr('uploadDate','');
						$(this).find('.graphicAppliedImageName input').val('');
						$(this).find('.cmsAssetInfo').attr('cmsLastUpdateDate','');
						$(this).find('.cmsAssetInfo').attr('cmsLastSyncDate','');
					});
					_this.getCurrentGraphicInputsContainer().show();
					_this.setGraphicContent("","","","");
					_this.setCmsAssetInfo("", "");
					// IMAGE LINK
					_this.getCurrentGraphicImageLinkContainer().each( function(){
						$(this).find('.graphicImageLink .imageLinkInput').val('');
					});
					// IMAGE ALT TEXT
					_this.getCurrentGraphicImageAltTextContainer().each( function(){
						$(this).find('.graphicImageAltText .imageAltTextInput').val('');
					});					
					_this.setGraphicImageAltText("");
					// IMAGE EXTERNAL LINK
					_this.getCurrentGraphicImageExtLinkContainer().each( function(){
						$(this).find('.graphicImageExtLink .imageExtLinkInput').val('');
					});
					_this.setGraphicImageExtLink("");
					// IMAGE EXTERNAL PATH
					_this.getCurrentGraphicImageExtPathContainer().each( function(){
						$(this).find('.graphicImageExtPath .imageExtPathInput').val('');
					});
					_this.setGraphicImageExtPath("");
				}
				_this.getCurrentContentEntry().attr('contentState','inherit');
				_this.setState(currentGlobalState, currentGroupState, "inherit");
			}
		},

		toggleAllSameAsParent : function (checkboxEle) {
			var _this = this;

			var currentGlobalState = $(_this.content.bindingContainer).attr('globalState');
			var currentGroupState = $(_this.getCurrentGroup()).attr('groupState');
			if ( $(checkboxEle).is(':checked') ) {
				$(_this.display.sameAsParentContainer).find('.sameAsParentInput').prop('checked', true);
				_this.getAllContentEntry().attr('contentState','sameAsParent');

				if ( _this.data.currentContentType == "graphic" ) {
					_this.processParentDataContent();
				}

				_this.setState(currentGlobalState, currentGroupState, "sameAsParent");
				$(_this.display.staticGraphicContentContainer).show();

			} else {
				$(_this.display.sameAsParentContainer).find('.sameAsParentInput').prop('checked', false);
				if ( _this.data.currentContentType == "graphic" ) {
					_this.resetImageLibraryTypeSelect();
					_this.getCurrentGraphicInputsContainer().each( function(){
						$(this).find('.graphicFileInput').attr('imgPath','');
						$(this).find('.graphicFileInput').attr('imgName','');
						$(this).find('.graphicFileInput').attr('uploadDate','');
						$(this).find('.graphicAppliedImageName input').val('');
						$(this).find('.cmsAssetInfo').attr('cmsLastUpdateDate','');
						$(this).find('.cmsAssetInfo').attr('cmsLastSyncDate','');
					});
					_this.getCurrentGraphicInputsContainer().show();
					_this.setGraphicContent("","","","");
					_this.setCmsAssetInfo("", "");
					// IMAGE LINK
					_this.getCurrentGraphicImageLinkContainer().each( function(){
						$(this).find('.graphicImageLink .imageLinkInput').val('');
					});
					// IMAGE ALT TEXT
					_this.getCurrentGraphicImageAltTextContainer().each( function(){
						$(this).find('.graphicImageAltText .imageAltTextInput').val('');
					});
					_this.setGraphicImageAltText("");
					// IMAGE EXTERNAL LINK
					_this.getCurrentGraphicImageExtLinkContainer().each( function(){
						$(this).find('.graphicImageExtLink .imageExtLinkInput').val('');
					});
					_this.setGraphicImageExtLink("");
					// IMAGE EXTERNAL PATH
					_this.getCurrentGraphicImageExtPathContainer().each( function(){
						$(this).find('.graphicImageExtPath .imageExtPathInput').val('');
					});
					_this.setGraphicImageExtPath("");
				}
				_this.getAllContentEntry().attr('contentState','inherit');
				_this.setState(currentGlobalState, currentGroupState, "inherit");
			}
		},

		resetImageLibraryTypeSelect : function () {
			var _this = this;
			var firstImageLibraryTypeOption = _this.getCurrentGraphicInputsContainer().find('.imageLibaryTypeSelect option:first');
			_this.getCurrentGraphicInputsContainer().find('.imageLibaryTypeSelect option:first').prop("selected", true);
			_this.getCurrentGraphicInputsContainer().find('.actionSelectMenuText').text(firstImageLibraryTypeOption.text());
			_this.data.currentImageLibraryId = 0;
			var imageLibraryToggle 		= _this.getCurrentGraphicInputsContainer().find('.useContentLibraryToggle input');
			var localImageLibraryToggle = _this.getCurrentGraphicInputsContainer().find('.useLocalContentLibraryToggle input');
			var useImageLibraryBinding = _this.getCurrentContentEntry().find('.useImageLibraryBinding');
			var imageLibraryObjectTypeBinding = _this.getCurrentContentEntry().find('.imageLibraryObjectTypeBinding');

			$(imageLibraryToggle).removeAttr('checked');
			$(localImageLibraryToggle).removeAttr('checked');
			$(useImageLibraryBinding).val(false);
			$(imageLibraryObjectTypeBinding).val(0);

			_this.setGraphicContent("", "", "", "");
			_this.setCmsAssetInfo("", "");
			_this.setGraphicImageLink("");
			_this.setGraphicImageAltText("");
			_this.setGraphicImageExtLink("");
			_this.setGraphicImageExtPath("");
		},

		updateCharacterCount : function () {
			var _this = this;
			
			if (!_this.display.characterCountContainer)
				return;

			var charCount = 0;
			var content = "<content>"+jQuery.trim(_this.getCurrentEditorBinding().val())+"</content>";
			var variableLength = false;

			if ( content == "" )
				return;
			
			if ( $(content).find('.staticContentItem') && $(content).find('.staticContentItem').length > 0) {
				$(content).remove('.staticContentItem');
				variableLength = true;
			}
			charCount = $(content).text().length;

			$(_this.display.characterCountContainer).find('.contentEditor_CharacterCount').text( charCount+(variableLength ? '+' : '') );
		},

		webdavFileOpen: function(file) {
			var obj = null;
			if ($.browser.mozilla)
				obj = document.getElementById("winFirefoxPlugin");
			else
				obj = new ActiveXObject('SharePoint.OpenDocuments.3');
			
			var pathArray = window.location.href.split( '/' );
			var protocol = pathArray[0];
			var host = pathArray[2];
			var baseUrl = protocol + '//' + host;
			
			obj.EditDocument(baseUrl + file);
		},
		
		styles : {
			
			data 						: null,
			css_container				: null,
			parsed_text_styles			: [],
			parsed_paragraph_styles		: [],
			indicator 					: {
				primary_joiner 				: "--",
				sub_joiner 					: "-",
				color_joiner 				: ";",
				paragraph_class_prefix		: 'tinymce_paragraphstyle_'
			},
			dspScale					: 1.042,
			text_styles_post_loaded		: [],
			paragraph_styles_post_loaded: [],
			control_classes			: ["staticContentItem","innerStaticContentItem","mceNonEditable","active","varTagRenderedBlock","varTagRenderedInline",
			               			   "mceInfoTxt","embedded_content_tag","actionVariable","renderedLabelContainer","renderedContentContainer","fa-mp-container",
			               			   "touchpointContentEditContainer"],

			initStyleData : function() {

				var _this = this.data;

				_this.styles.dspScale = _this.data.channel != undefined && (parseInt(_this.data.channel) == 3 || parseInt(_this.data.channel) == 4)? 1.000 : 1.042
			
				// TEXT STYLES: Format JSON
				if ( _this.data.text_data != undefined && _this.data.text_data != null) {
					for (var i=0; i < _this.data.text_data.length; i++) {
						if (_.isString(_this.data.text_data[i])) {
						_this.data.text_data[i] = mpParseJSON( _this.data.text_data[i] );
						}
						
						if ( _this.data.text_data[i].has_variations ) {
							_this.data.apply_text_style_variations = true;
							if ( _this.data.text_data[i].variation_data.toggle_color )
								_this.data.text_data[i].variation_data.colors = mpParseJSON(_this.data.text_data[i].variation_data.colors);
							if ( _this.data.text_data[i].variation_data.toggle_point_size )
								_this.data.text_data[i].variation_data.point_sizes = mpParseJSON(_this.data.text_data[i].variation_data.point_sizes);
						}
					}
				
					// DATA: Init style css data
					var styleClasses = new Object();
					for (var i=0; i < _this.data.text_data.length; i++) {
						styleClasses[_this.data.text_data[i].title] = {
							css_attributes 	: _this.data.text_data[i].css_attributes,
							variation_data	: _this.data.text_data[i].has_variations ? 
												_this.data.text_data[i].variation_data : 
												null
						};
					}
					this.text_styles = styleClasses;

				}
				
				// PARAGRAPH STYLES: Format JSON
				if ( _this.data.paragraph_data != undefined && _this.data.paragraph_data != null) {
					for (var i=0; i < _this.data.paragraph_data.length; i++) {
						if (_.isString(_this.data.paragraph_data[i])) {
							_this.data.paragraph_data[i] = mpParseJSON( _this.data.paragraph_data[i] );
						}

						if ( _this.data.paragraph_data[i].has_variations ) {
							_this.data.apply_paragraph_style_variations = true;
							if ( _this.data.paragraph_data[i].variation_data.toggle_line_spacings ) {
								if (_.isString(_this.data.paragraph_data[i].variation_data.line_spacings)) {
									_this.data.paragraph_data[i].variation_data.line_spacings = mpParseJSON(_this.data.paragraph_data[i].variation_data.line_spacings);
								}
							}

						}
					}

					// DATA: Init style css data
					var styleClasses = new Object();
					for (var i=0; i < _this.data.paragraph_data.length; i++) {
						styleClasses[_this.data.paragraph_data[i].name] = {
							css_attributes 		: _this.data.paragraph_data[i].css_attributes,
							is_fixed_spacing	: _this.data.paragraph_data[i].is_fixed_spacing,
							variation_data		: _this.data.paragraph_data[i].has_variations ?
													_this.data.paragraph_data[i].variation_data :
														null
						};
					}
					this.paragraph_styles = styleClasses;

				}
				
				var cssContainer = $("<style class=\"compoundContentStyles\"></style>");
				$(document).find('head').append(cssContainer);
				this.css_container = cssContainer;

			},
			
			CMYKtoHEX : function(cmykVal) {
				
				var _this = this.data;

				if ( !cmykVal || cmykVal == null )
					return "#000000";
				
			    function padZero(str) {
			        return "000000".substr(str.length) + str;
			    }
				
				var hexVal = cmykVal.replace("cmyk(","").replace(")","").replaceAll("d",".");
				
				var cmykValues = hexVal.split(_this.styles.indicator.color_joiner);
				var C = parseFloat(cmykValues[0])/100;
				var M = parseFloat(cmykValues[1])/100;
				var Y = parseFloat(cmykValues[2])/100;
				var K = parseFloat(cmykValues[3])/100;

			    var cyan = (C * 255 * (1-K)) << 16;
			    var magenta = (M * 255 * (1-K)) << 8;
			    var yellow = (Y * 255 * (1-K)) >> 0;

			    var black = 255 * (1-K);
			    var white = black | black << 8 | black << 16;

			    var color = white - (cyan | magenta | yellow );

			    return ("#"+padZero(color.toString(16)));

			},

			getStyleAttr : function(css, attr) {
				if (css != undefined && css != '') {
					var attrArray = css.split(";");
					for (var i = 0; i < attrArray.length; i++) {
						var currentAttr = attrArray[i].split(":");
						if ($.trim(currentAttr[0]) == attr)
							return $.trim(currentAttr[1]);
					}
				}
			},

			replaceAll : function(target, str, sub) {
				while (target.indexOf(str) != -1)
					target = target.replace(str,sub);
				return target;
			},
			
			updateStyleReferenceCSS : function() {

				function getTextStylesData(allTextStyles) {
					var requestParam = "type=textStyles";
					if ( _this.data.contentObjectId != undefined && _this.data.contentObjectId != null && _this.data.contentObjectId.length != 0 )
						requestParam += "&contentObjectId=" + _this.data.contentObjectId;

					var stampDate = new Date();
					$.ajax({
						type: "GET",
						url: context + "/getObjectInfo.form?" + requestParam + "&cacheStamp=" + (stampDate.getTime()),
						data: {
							connectors: allTextStyles
						},
						success: function(data) {
							for (style in data[0]) {
								data = mpParseJSON( data[0][style][0] );

								if ( !data )
									return;

								if ( data.has_variations ) {
									if ( data.variation_data.toggle_color )
										data.variation_data.colors = mpParseJSON(data.variation_data.colors);

									if ( data.variation_data.toggle_point_size )
										data.variation_data.point_sizes = mpParseJSON(data.variation_data.point_sizes);
								}

								_this.styles.text_styles[style] = data;
							}
						}
					});
				}

				function getParagraphStyleData(styleBase) {
					var requestParam = "connector=" + styleBase + "&type=paragraphStyle";
					if ( _this.data.contentObjectId != undefined && _this.data.contentObjectId != null && _this.data.contentObjectId.length != 0 )
						requestParam += "&contentObjectId=" + _this.data.contentObjectId;
					
					var stampDate = new Date();
					$.ajax({
						type: "GET",
						url: context + "/getObjectInfo.form?" + requestParam + "&cacheStamp=" + (stampDate.getTime()),
						dataType: "json",
						success: function(data) {
							data = mpParseJSON( data[0] );
							if ( data != undefined && data.has_variations ) {
								if ( data.variation_data.toggle_line_spacing )
									data.variation_data.line_spacings = mpParseJSON(data.variation_data.line_spacings);
							}

							_this.styles.paragraph_styles[styleBase] = data;
							_this.styles.updateStyleReferenceCSS();
						}
					});	
				}

				function parseListAttributes(list) {
					function asciiToHex(str) {
						return Array.from(str)
							.map(char => char.charCodeAt(0).toString(16))
							.join('');
					}

					function rgbToCmyk(args) {
						const r = args[0] / 255;
						const g = args[1] / 255;
						const b = args[2] / 255;

						const k = Math.min(1 - r, 1 - g, 1 - b);
						const c = (1 - r - k) / (1 - k) || 0;
						const m = (1 - g - k) / (1 - k) || 0;
						const y = (1 - b - k) / (1 - k) || 0;

						return [c * 100, m * 100, y * 100, k * 100];
					}

					function getAttr(attr, list) {
						if (!list || list.length === 0) return null;

						var ulComputedStyle = window.getComputedStyle(list[0]);
						var liComputedStyle = window.getComputedStyle($(list).find('li')[0]);
						var liBeforeComputedStyle = window.getComputedStyle($(list).find('li')[0], ':before');
						var contentComputedStyle = window.getComputedStyle($(list).find('li .mceListItemContent')[0]);

						if (list.attr(attr)) {
							switch (attr) {
								case 'bullet_spacing':
								case 'list_spacing':
									var spacingValues = list.attr(attr).split(':').map(function (val) {
										return parseFloat(val.replace('in', ''));
									});

									return {
										top: spacingValues[0],
										right: spacingValues[1],
										bottom: spacingValues[2],
										left: spacingValues[3]
									};
								case 'bullet_symbol':
									// Here we have the symbol in hex format
									var symbol = list.attr(attr);
									symbol = symbol.includes('counter') ? null : list.attr(attr);
									return symbol;
								case 'bullet_color':
								case 'bullet_font':
								case 'bullet_size':
								case 'text_align':
									return list.attr(attr);
								case 'line_spacing':
									var lineSpacing = list.attr(attr);
									var value = parseFloat(lineSpacing);
									var type = lineSpacing.slice(-1) === 'm' ? 1 : 2;
									return {
										value: value,
										type: type
									};
							}
						} else {
							switch (attr) {
								case 'bullet_spacing':
									var top = contentComputedStyle.getPropertyValue('padding-top');
									var bottom = contentComputedStyle.getPropertyValue('padding-bottom');
									var right = parseFloat(contentComputedStyle.getPropertyValue('left')) - parseFloat(liBeforeComputedStyle.getPropertyValue('left'));
									var left = liBeforeComputedStyle.getPropertyValue('left');

									return {
										top: parseFloat(top) / 100,
										right: parseFloat(right) / 100,
										bottom: parseFloat(bottom) / 100,
										left: parseFloat(left) / 100
									};
								case 'list_spacing':
									var top = ulComputedStyle.getPropertyValue('padding-top');
									var bottom = ulComputedStyle.getPropertyValue('padding-bottom');
									var right = parseFloat(liComputedStyle.getPropertyValue('width')) - parseFloat(contentComputedStyle.getPropertyValue('width')) - parseFloat(contentComputedStyle.getPropertyValue('left'));
									var left = 0;
									return {
										top: parseFloat(top) / 100,
										right: parseFloat(right) / 100,
										bottom: parseFloat(bottom) / 100,
										left: parseFloat(left) / 100
									};
								case 'bullet_symbol':
									// Here we have the symbol in ascii format
									var symbol = liBeforeComputedStyle.getPropertyValue('content');
									symbol = symbol.includes('counter') || symbol == "none" ? "none" : asciiToHex(symbol.replace(/"/g, ''));
									return symbol;
								case 'bullet_color':
									// Conver color from rgb to hex
									var color = liBeforeComputedStyle.getPropertyValue('color');

									if (color.includes('rgb')) {
										var rgb = color.match(/\d+/g);
										color = rgbToCmyk(rgb).map((num) => Number(num.toFixed(0)));
										;
										color = 'cmyk(' + color.join(';') + ')';
									}

									return color;
								case 'bullet_font':
									return liBeforeComputedStyle.getPropertyValue('font-family');
								case 'bullet_size':
									var fontSize = liBeforeComputedStyle.getPropertyValue('font-size');
									// convert font size to pt
									var fontSizePt = (parseFloat(fontSize) * 0.75).toFixed(2);
									return fontSizePt;
								case 'text_align':
									var textAlign = ulComputedStyle.getPropertyValue('text-align');
									return textAlign;
								case 'line_spacing':
									var lineHeight = liComputedStyle.getPropertyValue('line-height');
									var fontSize = parseFloat(liComputedStyle.getPropertyValue('font-size'));

									var value = parseFloat(lineHeight);
									var type = lineHeight.includes('px') ? 1 : 2;

									if (type === 1) {
										// Convert px to unitless by dividing by font size
										value = value / fontSize;
									}

									return {
										value: value,
										type: type
									};
							}
						}
						return null;
					}

					var listAttributes = {};

					// List Text Alignment
					listAttributes.text_align = getAttr('text_align', list);

					// Bullet Spacing
					listAttributes.bullet_spacing = getAttr('bullet_spacing', list);

					// List Spacing
					listAttributes.list_spacing = getAttr('list_spacing', list);

					// Bullet Symbol Overrides
					var bulletSymbol = getAttr('bullet_symbol', list);
					var bulletColor = getAttr('bullet_color', list);
					var bulletFont = getAttr('bullet_font', list);
					var bulletSize = getAttr('bullet_size', list);

					if (bulletSymbol || bulletColor || bulletFont || bulletSize) {
						listAttributes.bullet = {
							symbol: bulletSymbol,
							color: bulletColor,
							font: bulletFont,
							size: bulletSize
						};
					}

					// Line Spacing
					listAttributes.line_spacing = getAttr('line_spacing', list);

					return listAttributes;
				}

				function updateCustomListStyles(list) {
					function componentToHex(c) {
						var hex = Math.round(c).toString(16);
						return hex.length === 1 ? "0" + hex : hex;
					}

					function cmykToHex(cmykString) {
						// Extract CMYK values from the input string
						var cmykValues = cmykString.slice(5, -1).split(';').map(Number);

						// Destructure the array into individual variables
						var c = cmykValues[0];
						var m = cmykValues[1];
						var y = cmykValues[2];
						var k = cmykValues[3];

						// Ensure values are within valid range (0 to 100)
						c = Math.max(0, Math.min(100, c));
						m = Math.max(0, Math.min(100, m));
						y = Math.max(0, Math.min(100, y));
						k = Math.max(0, Math.min(100, k));

						// Convert CMYK to RGB
						var r = 255 * (1 - c / 100) * (1 - k / 100);
						var g = 255 * (1 - m / 100) * (1 - k / 100);
						var b = 255 * (1 - y / 100) * (1 - k / 100);

						// Convert RGB to HEX
						var hex = "#" + componentToHex(r) + componentToHex(g) + componentToHex(b);
						return hex;
					}

					function parseValue(value) {
						return parseFloat(value) * 100 || 0;
					}

					var $list = $(list);

					var custListAttr = ['text_align', 'bullet_spacing','list_spacing','bullet_symbol','bullet_color','bullet_font','bullet_size','line_spacing'];
					// Determine if current list has at least one custom attribute; otherwise return;
					if ( custListAttr.every(function(attr) { return !$list.attr(attr); }) )
						return;

					var listId = $list.attr('custom_list_id') || Math.random().toString(36).substr(2, 9);
					$list.attr('custom_list_id', listId);

					// Remove existing custom list styles if any
					$('#customListStyles_' + listId).remove();

					var listAttr = parseListAttributes($list);
					var selector = $list.prop('tagName').toLowerCase() + '[custom_list_id="' + listId + '"]';

					var textAlign = listAttr.text_align;

					var bulletRightSpacing = parseValue(listAttr.bullet_spacing.right);
					var bulletLeftSpacing = parseValue(listAttr.bullet_spacing.left);
					var bulletTopSpacing = parseValue(listAttr.bullet_spacing.top);
					var bulletBottomSpacing = parseValue(listAttr.bullet_spacing.bottom);

					var listSpacingTop = parseValue(listAttr.list_spacing.top);
					var listSpacingBottom = parseValue(listAttr.list_spacing.bottom);
					var listSpacingRight = parseValue(listAttr.list_spacing.right);

					var lineSpacingValue = listAttr.line_spacing.value;
					var lineSpacingType = listAttr.line_spacing.type;

					var bullet = listAttr.bullet;
					var bulletSymbol = bullet.symbol;
					var bulletColor = bullet.color;
					var bulletFont = bullet.font;
					var bulletSize = bullet.size;

					var listStyle = '';
					if (listSpacingTop > 0)
						listStyle += 'padding-top:' + listSpacingTop + 'px;';
					if (listSpacingBottom > 0)
						listStyle += 'padding-bottom:' + listSpacingBottom + 'px;';
					if (textAlign)
						listStyle += 'text-align:' + textAlign + ';';

					var bulletContentStyle = "";
					bulletContentStyle += 'padding-top:' + bulletTopSpacing + 'px;';
					bulletContentStyle += 'padding-bottom:' + bulletBottomSpacing + 'px;';
					bulletContentStyle += 'left: ' + (bulletLeftSpacing + bulletRightSpacing) + 'px;';
					bulletContentStyle += 'width: calc(100% - ' + (bulletLeftSpacing + bulletRightSpacing + listSpacingRight) + 'px);';
					bulletContentStyle += 'line-height:' + (lineSpacingValue > 0 ? lineSpacingValue : 1) + (lineSpacingType == 2 ? 'pt': '') + ';';

					var bulletBefore = 'left: ' + bulletLeftSpacing + 'px; padding-top:' + bulletTopSpacing + 'px;';

					if (bullet) {
						if (bulletSymbol && bulletSymbol !== 'none') {
							bulletBefore += 'content: "\\'  + bulletSymbol + '";';
						}
						bulletBefore += "color: " + (bulletColor.includes('cmyk') ? cmykToHex(bulletColor) : bulletColor) + ";" +
							"font-size: " + bulletSize + "pt;" +
							"font-family: " + bulletFont + ";"
					}

					var listStyleCSS =
						selector + ' > li:before{' +
						bulletBefore +
						'} \n' +
						selector + ' {' +
						listStyle +
						'} \n' +
						selector + ' > li > div.mceListItemContent {' +
						bulletContentStyle +
						'} \n';

					var modifiedStyleCSS = listStyleCSS.replace(/;/g, ' !important;');

					// Add new custom list styles
					$('head').append("<style id=\"customListStyles_"+listId+"\" type=\"text/css\">" + modifiedStyleCSS + "</style>")

				}

				function parseParagraphAttributes(paragraph) {
					var $paragraph = $(paragraph);
					var paragraphId = $paragraph.attr('custom_paragraph_id');
					if (paragraphId) {
						// Remove existing custom paragraph styles if any
						$('#customParagraphStyles_' + paragraphId).remove();
					}

					function pxToInches(px) {
						var ppi = 100;
						return parseFloat(px) / ppi;
					}

					function getAttr(attr, paragraph) {
						if (!paragraph || paragraph.length === 0) {
							return null;
						}

						var pComputedStyle = window.getComputedStyle(paragraph[0]);
						var paragraphClass = paragraph.attr('paragraphclass') || "";
						var primaryJoiner = "--";
						var paragraphClassPrefix = "tinymce_paragraphstyle_";
						var currentStyle = paragraphClass.replace(paragraphClassPrefix, "");
						var styleBase = currentStyle.split(primaryJoiner)[0];


						if (paragraph.attr(attr)) {
							switch (attr) {
								case 'text_align':
								case 'line_spacing':
									return paragraph.attr(attr);
								case 'spacing':
									// In inches
									var spacingValues = paragraph.attr(attr).split(':').map(function(val) {
										return parseFloat(val.replace('in', ''));
									});
									return {
										top: spacingValues[0],
										right: spacingValues[1],
										bottom: spacingValues[2],
										left: spacingValues[3]
									};
								case 'indent_type':
								case 'special_indent':
									return {
										type: paragraph.attr('indent_type'),
										value: parseFloat(paragraph.attr('special_indent'))
									};
							}
						} else {
							switch (attr) {
								case 'text_align':
									return pComputedStyle.getPropertyValue('text-align');
								case 'spacing':
									var spacingInPx = [
										pComputedStyle.getPropertyValue('padding-top'),
										pComputedStyle.getPropertyValue('margin-right'),
										pComputedStyle.getPropertyValue('padding-bottom'),
										pComputedStyle.getPropertyValue('margin-left')
									];

									// Convert px to inches
									var spacingValues = spacingInPx.map(function(val) {
										var inchValue = pxToInches(val.replace('in', ''));
										return Math.round(inchValue * 100) / 100; // Round to 2 decimal places
									});

									return {
										top: spacingValues[0],
										right: spacingValues[1],
										bottom: spacingValues[2],
										left: spacingValues[3]
									};
								case 'line_spacing':
									var fontSizePx = parseFloat(pComputedStyle.getPropertyValue('font-size'));
									var lineHeightPx = parseFloat(pComputedStyle.getPropertyValue('line-height'));
									var unitlessLineHeight = lineHeightPx / fontSizePx; // Ratio of line-height to font-size

									if (!_this.data.paragraph_data) {
										return unitlessLineHeight.toFixed(2);
									}

									var currentStyleData = _this.data.paragraph_data.find(function(style){
										return style.name == styleBase
									});

									if (currentStyleData && currentStyleData.is_fixed_spacing) { // Check if spacing is in pt
										var pxToPtFactor = 0.74999943307122;
										var displayScale = 100 / 96;
										var lineHeightInPt = parseFloat(lineHeightPx) * (pxToPtFactor / displayScale);
										return lineHeightInPt.toFixed(2) + 'pt';
									}

									return unitlessLineHeight.toFixed(2);
								case 'indent_type':
								case 'special_indent':
									var textIndent = parseFloat(pComputedStyle.getPropertyValue('text-indent')) / 100;
									return textIndent === 0 ? { type: "", value: 0 } : {
										type: textIndent > 0 ? 'first' : 'hanging',
										value: textIndent
									};
							}
						}
						return null;
					}

					return {
						text_align: getAttr('text_align', paragraph),
						spacing: getAttr('spacing', paragraph),
						line_spacing: getAttr('line_spacing', paragraph),
						indent_type: getAttr('indent_type', paragraph),
						special: getAttr('special_indent', paragraph)
					};
				}

				function updateCustomParagraphStyles(paragraph) {
					var $paragraph = $(paragraph);

					function inchesToPx(inches) {
						var ppi = 100;
						return parseFloat(inches) * ppi;
					}

					var custParagraphAttr = ['text_align', 'spacing', 'line_spacing', 'indent_type', 'special_indent'];

					// Determine if current paragraph has at least one custom attribute; otherwise return;
					if ( custParagraphAttr.every(function(attr) { return !$paragraph.attr(attr); }) )
						return;

					var paragraphId = $paragraph.attr('custom_paragraph_id') || Math.random().toString(36).substr(2, 9);
					$paragraph.attr('custom_paragraph_id', paragraphId);

					// Remove existing custom paragraph styles if any
					$('#customParagraphStyles_' + paragraphId).remove();

					var paragraphAttr = parseParagraphAttributes($paragraph);
					var selector = $paragraph.prop('tagName').toLowerCase() + '[custom_paragraph_id="' + paragraphId + '"]';

					var textAlign = paragraphAttr.text_align;

					var spacingLeft = inchesToPx(paragraphAttr.spacing.left);
					var spacingRight = inchesToPx(paragraphAttr.spacing.right);

					var spacingBottom = inchesToPx(paragraphAttr.spacing.bottom);
					var spacingTop = inchesToPx(paragraphAttr.spacing.top);

					var lineSpacingType = paragraphAttr.line_spacing.toString().slice(-2) === 'pt' ? 2 : 1;
					var lineSpacing = paragraphAttr.line_spacing;

					var specialType = paragraphAttr.special.type;
					var specialIndent = inchesToPx(paragraphAttr.special.value);

					var paragraphStyle = '';
					if (textAlign)
						paragraphStyle += 'text-align:' + textAlign + ';';

					var marginLeft = spacingLeft - (specialIndent < 0 ? specialIndent : 0);
					paragraphStyle += 'margin-left: ' + marginLeft + 'px;';
					paragraphStyle += 'margin-right: ' + spacingRight + 'px;';
					paragraphStyle += 'padding-bottom: ' + spacingBottom + 'px;';
					paragraphStyle += 'padding-top: ' + spacingTop + 'px;';

					paragraphStyle += 'line-height: ' + lineSpacing + ';';

					paragraphStyle += 'text-indent: ' + specialIndent + 'px;';

					var paragraphStyleCSS =
						selector + ' {' +
						paragraphStyle +
						'} \n';

					var modifiedStyleCSS = paragraphStyleCSS.replace(/;/g, ' !important;');

					$('head').append("<style id=\"customParagraphStyles_"+paragraphId+"\" type=\"text/css\">" + modifiedStyleCSS + "</style>")

				}

				var _this = this.data;

				if ( _this.styles.text_styles ) {
					var allTextStyles = [];
					$(_this.display.staticTextContentContainer).find('.contentContainer span').each( function() {
						if ( $(this).attr("class") ) {
							var currentClasses = $(this).attr("class").split(" ");
							for (var i = 0; i < currentClasses.length; i++) {
								var styleBase = currentClasses[i].split(_this.styles.indicator.primary_joiner)[0];
								if ( allTextStyles.indexOf(styleBase) != -1 || _this.styles.control_classes.indexOf(styleBase) != -1 || $.trim(styleBase).length == 0 )
									continue;
								if (!_this.styles.text_styles[styleBase])
									allTextStyles.push(styleBase);
							}
						}
					});
					if (allTextStyles.length) {
						getTextStylesData(allTextStyles);
					}


					$(_this.display.staticTextContentContainer).find('.contentContainer span').each( function() {
	
						if ( $(this).attr("class") ) {
							var currentClasses = $(this).attr("class").split(" ");
							
							for ( var i=0; i < currentClasses.length; i++ ) {
								var styleBase = currentClasses[i].split(_this.styles.indicator.primary_joiner)[0];
								

								if ( _this.styles.text_styles[styleBase] && _this.styles.parsed_text_styles.indexOf(currentClasses[i]) == -1 ) {
									
									var cssAttrs = new Array();
									cssAttrs.push(_this.styles.text_styles[styleBase].css_attributes);
									
									if ( currentClasses[i].indexOf(_this.styles.indicator.primary_joiner) != -1 &&
										 _this.styles.text_styles[styleBase].variation_data != null ) {
									
										styleAttr = currentClasses[i]
														.split(_this.styles.indicator.primary_joiner)[1]
														.split(_this.styles.indicator.sub_joiner);

										if ( _this.styles.text_styles[styleBase].variation_data.toggle_bold ) {
											if ( styleAttr.indexOf("B") != -1 )
												cssAttrs.push("font-weight: bold;\n");
											else
												cssAttrs.push("font-weight: normal;\n");
										} else {
											var fixedValue = _this.styles.getStyleAttr(_this.styles.text_styles[styleBase].css_attributes,"font-weight");
											cssAttrs.push("font-weight: " + fixedValue + ";\n");
										}
										
										if ( _this.styles.text_styles[styleBase].variation_data.toggle_italic ) {
											if ( styleAttr.indexOf("I") != -1 )
												cssAttrs.push("font-style: italic;\n");
											else
												cssAttrs.push("font-style: normal;\n");
										} else {
											var fixedValue = _this.styles.getStyleAttr(_this.styles.text_styles[styleBase].css_attributes,"font-style");
											cssAttrs.push("font-style: " + fixedValue + ";\n");
										}
										
										if ( _this.styles.text_styles[styleBase].variation_data.toggle_underline ) {
											if ( styleAttr.indexOf("U") != -1 )
												cssAttrs.push("text-decoration: underline;\n");
											else
												cssAttrs.push("text-decoration: none;\n");
										} else {
											var fixedValue = _this.styles.getStyleAttr(_this.styles.text_styles[styleBase].css_attributes,"text-decoration");
											cssAttrs.push("text-decoration: " + fixedValue + ";\n");
										}

										if ( _this.styles.text_styles[styleBase].variation_data.applied_font_family ) {
											var appliedFontFamily = _this.styles.text_styles[styleBase].variation_data.applied_font_family;
											if (_this.styles.text_styles[styleBase].variation_data.applies_font_bold_italic &&
												_this.styles.text_styles[styleBase].variation_data.toggle_italic && styleAttr.indexOf("I") != -1 &&
												_this.styles.text_styles[styleBase].variation_data.toggle_bold && styleAttr.indexOf("B") != -1) {
												cssAttrs.push("font-family: '" + appliedFontFamily + "-BoldItalic" + "';");
											} else if (_this.styles.text_styles[styleBase].variation_data.applies_font_bold &&
												_this.styles.text_styles[styleBase].variation_data.toggle_bold && styleAttr.indexOf("B") != -1) {
												cssAttrs.push("font-family: '" + appliedFontFamily + "-Bold" + "';");
											} else if (_this.styles.text_styles[styleBase].variation_data.applies_font_italic &&
												_this.styles.text_styles[styleBase].variation_data.toggle_italic && styleAttr.indexOf("I") != -1) {
												cssAttrs.push("font-family: '" + appliedFontFamily + "-Italic" + "';");
											}
										}

										for ( var j=0; j < styleAttr.length; j++ ) {
											if ( _this.styles.text_styles[styleBase].variation_data.toggle_point_size ) {
												if ( styleAttr[j].indexOf('S') != -1 ) {
													cssAttrs.push("font-size: " + (parseFloat(styleAttr[j].replace("S","").replace("_",".")) * _this.styles.dspScale) + "pt;\n");
													// PARAGRAPH MIN HEIGHT
													if ( _this.data.usage == "view" )
														cssAttrs.push("min-height: " + (parseFloat(styleAttr[j].replace("S","").replace("_",".")) * _this.styles.dspScale) + "pt;\n");
												}
											}
											if ( _this.styles.text_styles[styleBase].variation_data.toggle_color ) {
												if ( styleAttr[j].indexOf('cmyk') != -1 ) {
													cssAttrs.push("color: " + 
														_this.styles.CMYKtoHEX( _this.styles.replaceAll(styleAttr[j].replace('cmyk','cmyk('),"_",_this.styles.indicator.color_joiner) + ")" ) + 
													";\n");
												}
											}
										};
		
									};
		
									_this.styles.parsed_text_styles[_this.styles.parsed_text_styles.length] = currentClasses[i];
									$(_this.styles.css_container).append('.' + currentClasses[i] + ' {\n' + cssAttrs.join(' ') + '}\n');
										
								};
							};
						};
		
					});
				
				} // END IF _this.styles.text_styles
				
				if ( _this.styles.paragraph_styles ) {
					
					$(_this.display.staticTextContentContainer).find('[paragraphclass]').each( function() {
		
						var currentStyle = $(this).attr('paragraphclass').replace(_this.styles.indicator.paragraph_class_prefix,'');
						var styleBase = currentStyle.split(_this.styles.indicator.primary_joiner)[0];
						
						if ( !_this.styles.paragraph_styles[styleBase] && _this.styles.paragraph_styles_post_loaded.indexOf(styleBase) == -1 ) {
							_this.styles.paragraph_styles_post_loaded[_this.styles.paragraph_styles_post_loaded.length] = styleBase;
							getParagraphStyleData(styleBase);
						}

						if ( _this.styles.paragraph_styles[styleBase] && _this.styles.parsed_paragraph_styles.indexOf(currentStyle) == -1 ) {

							var cssAttrs = new Array();
							cssAttrs.push(_this.styles.paragraph_styles[styleBase].css_attributes);
							
							if ( currentStyle.indexOf(_this.styles.indicator.primary_joiner) != -1 &&
								 _this.styles.paragraph_styles[styleBase].variation_data != null ) {
							
								styleAttr = currentStyle
												.split(_this.styles.indicator.primary_joiner)[1]
												.split(_this.styles.indicator.sub_joiner);
		
								for ( var j=0; j < styleAttr.length; j++ ) {
									
									if ( _this.styles.paragraph_styles[styleBase].variation_data.toggle_alignment ) {
										if ( styleAttr[j].indexOf("AL") != -1 )
											cssAttrs.push("text-align: left;");
										else if ( styleAttr[j].indexOf("AC") != -1 )
											cssAttrs.push("text-align: center;");
										else if ( styleAttr[j].indexOf("AR") != -1 )
											cssAttrs.push("text-align: right;");
										else if ( styleAttr[j].indexOf("AJ") != -1 )
											cssAttrs.push("text-align: justify;");
									}
									
									if ( _this.styles.paragraph_styles[styleBase].variation_data.toggle_line_spacing ) {
										if ( styleAttr[j].indexOf("LS") != -1 ) {
											cssAttrs.push("line-height: " + (parseFloat(styleAttr[j].replace("LS","").replace("_",".")) * _this.styles.dspScale) + 
																			(_this.styles.paragraph_styles[styleBase].is_fixed_spacing ? "pt" : "") + ";");
										}
									}
									
									if ( _this.styles.paragraph_styles[styleBase].variation_data.toggle_left_margin ) {
										if ( styleAttr[j].indexOf("M") != -1 ) {
											var appliedMargin = parseFloat(styleAttr[j].replace("M","").replace("_","."));
											appliedMargin = appliedMargin != null ? parseFloat(appliedMargin) : 0;
											cssAttrs.push("margin-left: " + (appliedMargin * _this.styles.dspScale) + "px");
										}
									}
		
								};
		
							};

							_this.styles.parsed_paragraph_styles[_this.styles.parsed_paragraph_styles.length] = currentStyle;
							// Paragraph
							$(_this.styles.css_container).append(".contentContainer [paragraphclass='" + _this.styles.indicator.paragraph_class_prefix + currentStyle + "']:not(li), [paragraphclass='" + _this.styles.indicator.paragraph_class_prefix + currentStyle + "']:not(li) {" + cssAttrs.join(' ') + "}");
							$(_this.styles.css_container).append(".contentContainer li[paragraphclass='" + _this.styles.indicator.paragraph_class_prefix + currentStyle + "'] div.mceListItemContent, li[paragraphclass='" + _this.styles.indicator.paragraph_class_prefix + currentStyle + "'] div.mceListItemContent {" + cssAttrs.join(' ') + "}");
						};
		
					});
				
				} // END IF data.paragraph_styles

				// Custom list styles
				$(_this.display.staticTextContentContainer).find('ul,ol').each( function() {
					updateCustomListStyles(this);
				});

				// Custom paragraph styles
				$(_this.display.staticTextContentContainer).find('p').each( function() {
					updateCustomParagraphStyles(this);
				});
					
			}

		} // END .styles
		
		}; // end component
	};

    function computeFrameOffset(win, dims ) {
        dims = (typeof dims === 'undefined')?{ top: 0, left: 0}:dims;
        if (win !== top) {
            var rect = win.frameElement.getBoundingClientRect();
            dims.left += rect.left;
            dims.top += rect.top;
            computeFrameOffset(win.parent, dims );
        }
        return dims;
    };

	// instance manager
	contentEditor_component.inst = {};
	
})(jQuery);	