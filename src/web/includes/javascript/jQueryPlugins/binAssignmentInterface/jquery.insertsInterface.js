/** insertsInterface r1 // 2010.03.02 // jQuery 1.3 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * 
 */

(function($) {

	$.insertsInterface = {
		ref	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return insertsInterface_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			scrollTop: 0
		}
	};
	
	$.fn.insertsInterface = function (opts) {
		return this.each(function() {
			var conf = $.extend({
					parentContainer: $(this).parent(),
					insertIcons: '.insertOptionalIcon,.insertMandatoryIcon,.insertNonSelectableIcon'
				},opts);
			if(conf !== false) new insertsInterface_component().init(this, conf);
		});
	};

	function insertsInterface_component () {
		return {
			
		settings : $.extend({},$.insertsInterface.defaults),	

		init : function(elem, conf) {
			var _this = this;
			insertsInterface_component.inst[$(elem).attr("id")] = this;
			this.settings = $.extend(true, {}, this.settings, conf);
			
			// Define interface container
			$(elem).after("<div class=\"insertsInterfaceContainer\"></div>");

			// If no inserts: Display empty listing message
			var inserts = $(elem).find('.insertDefinition');
			if (inserts.length == 0) {
				$(this.settings.parentContainer).find('.insertsInterfaceContainer').append("<div class=\"interfaceMsgContainer\"></div>");
				$(this.settings.parentContainer).find('.insertsInterfaceContainer').find('.interfaceMsgContainer').append($(elem).find('#insertsMsg_noInserts').html());
				return;
			}

			// Generate insert elements
			// Use init container to improve performance: append() becomes expensive for large number of items
			var insertsInitContainer = $('<div/>');
			inserts.each( function() {
				// Specify HTML structure of insert element
				var insertName = $(this).text();
				var insertId = $(this).attr('insertId');
				var deliveryStatus = $(this).attr('deliveryStatus');
				var insertIconClass = null;
				if (deliveryStatus == '1')
					insertIconClass = 'insertOptionalIcon';
				else if (deliveryStatus == '2')
					insertIconClass = 'insertMandatoryIcon';
				else if (deliveryStatus == '3')	
					insertIconClass = 'insertNonSelectableIcon';
				var insertHTML = 	"<div id=\"insert_"+insertId+"\" insertId=\""+insertId+"\" class=\"insertDiv\">" +
										"<table cellspacing=\"0\" cellpadding=\"0\" border=\"0\"><tr>" +
											"<td style=\"padding: 1px; vertical-align: top;\">" +
												"<div class=\""+insertIconClass+"\" insertId=\""+insertId+"\"/>" +
											"</td>" +
											"<td style=\"padding: 1px; vertical-align: top;\">" +
												"<div style=\"padding-left: 11px;\">" +
													"<span class=\"insertLabel\" style=\"font-size: 15px;\">" +
														insertName +
													"</span>" +
												"</div>" +
											"</td>" +
										"</tr></table>" +
									"</div>";
				$(insertsInitContainer).append(insertHTML);
			});
			$(_this.settings.parentContainer).find('.insertsInterfaceContainer').append( $(insertsInitContainer).children() );

			// Init mouseovers
			$(_this.settings.parentContainer).find("[id^='insert_']").each( function(){
				$(this).mouseover(function(){
					_this.insertMouseover(this);
					// Init draggable on first mouse over: Occurs on mouse over to improve performance
					if (!_this.isDraggable(this))
						_this.initDraggable(this);
				})
				.mouseout(function(){_this.insertMouseout(this);});
			}); 

			$(_this.settings.parentContainer).find('.insertOptionalIcon,.insertMandatoryIcon,.insertNonSelectableIcon').each(function(){
				$(this).closest('.insertDiv').insertImagePopup({marginLeft:'80px', type: 'verbose'});
			});

			if ($.binAssignmentInterface.ref(this.settings.binLayoutBinding))
				this.disableInserts($.binAssignmentInterface.ref(this.settings.binLayoutBinding).getAssignedInserts());
		},
		
		initDraggable : function (insert) {
			var _this = this;
			
			$(insert).draggable({
				cursor: 'default',
				cursorAt: { top: 10, left: 10},
				opacity: 0.7,
				scroll: false,
				helper: 'clone',
				appendTo: 'body'
			});
			if ($(insert).hasClass('insertDisabled') && _this.isDraggable($(insert)) )
				$(insert).draggable('disable');
		},

		insertMouseover : function (insert) {	
			var insertId = $(insert).attr('insertId');
			$.binAssignmentInterface.ref(this.settings.binLayoutBinding).binHighlightAction('on',insertId);
		},
		
		insertMouseout : function (insert) {
			var insertId = $(insert).attr('insertId');
			$.binAssignmentInterface.ref(this.settings.binLayoutBinding).binHighlightAction('off',insertId);
		},
		
		insertHighlightAction : function (action, insertId) {
			if (action == 'on') {
				$(this.settings.parentContainer).find('.insertsInterfaceContainer #insert_'+insertId).css('backgroundColor','#ddd');
				$(this.settings.parentContainer).find('.insertsInterfaceContainer').scrollTo($('#insert_'+insertId),0);
			} else if (action == 'off') {
				$(this.settings.parentContainer).find('.insertsInterfaceContainer #insert_'+insertId).css('backgroundColor','#fff');
			}
		},
		
		isDraggable : function (insert) {
			return $(insert).hasClass("ui-draggable");
		},
		
		getInsertName : function (insertId) {
			return $(this.settings.parentContainer).find('.insertsInterfaceContainer #insert_'+insertId+' .insertLabel').text();
		},
		
		disableInserts : function (insertList) {
			var _this = this;
			$(this.settings.parentContainer).find("div[id^='insert']").each(function(){
				if ( _this.isDraggable($(this)) )
					$(this).draggable('enable');
				$(this).removeClass('insertDisabled');
				$(this).find(_this.settings.insertIcons).css('opacity',1);
				$(this).find('.insertLabel').removeClass('insertTextDisabled');
			});
			for (var i=0; i<insertList.length; i++) {
				var currentInsertId = $(insertList[i]).attr('insertId');
				$(this.settings.parentContainer).find('#insert_'+currentInsertId).each(function(){
					if ( _this.isDraggable($(this)) )
						$(this).draggable('disable');
					$(this).addClass('insertDisabled');
					$(this).find(_this.settings.insertIcons).css('opacity',0.7);
					$(this).find('.insertLabel').addClass('insertTextDisabled');
				});
			}
		}
		
		};
	};
	
	// instance manager
	insertsInterface_component.inst = {};
})(jQuery);	