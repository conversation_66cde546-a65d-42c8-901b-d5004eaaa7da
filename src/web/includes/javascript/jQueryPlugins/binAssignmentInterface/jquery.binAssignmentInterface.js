/** binAssignmentInterface r1 // 2010.02.25 // jQuery 1.3 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * 
 */

(function($) {

	$.binAssignmentInterface = {
		ref	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return binAssignmentInterface_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			activeInsertId					: -1,
			removeActiveInsert				: false,
			scrollTop						: 0,
			anyInsertIcon					: '.insertOptionalIcon,.insertMandatoryIcon,.insertNonSelectableIcon',
			anyInsertIconClasses			: 'insertOptionalIcon,insertMandatoryIcon,insertNonSelectableIcon',
			optionalInsertIcon				: '.insertOptionalIcon',
			optionalInsertIconClass			: 'insertOptionalIcon',
			linkToInserts					: true,
			mandatoryInsertIcon				: '.insertMandatoryIcon',
			mandatoryInsertIconClass		: 'insertMandatoryIcon',
			nonSelectableInsertIcon			: '.insertNonSelectableIcon',
			nonSelectableInsertIconClass	: 'insertNonSelectableIcon',
			binsPerRow						: 8
		}
	};
	
	$.fn.binAssignmentInterface = function (opts) {
		return this.each(function() {
			var conf = $.extend({
				binAssignmentBinding: $(this),
				parentContainer: $(this).parent(),
				numberOfBins: $(this).val().split(',').length},opts);
			if(conf !== false) new binAssignmentInterface_component().init(this, conf);
		});
	};

	function binAssignmentInterface_component () {
		return {
			
		settings : $.extend({},$.binAssignmentInterface.defaults),	

		init : function(elem, conf) {
			var _this = this;
			binAssignmentInterface_component.inst[$(elem).attr("id")] = this;
			_this.settings = $.extend(true, {}, this.settings, conf);
			
			var binAssignments = $(elem).val().split(',');
			var deliveryStatus = new Array();
			if ( $(_this.settings.deliveryStatusBinding).length > 0 )
				deliveryStatus = $(_this.settings.deliveryStatusBinding).val().split(',');

			// Clear any pre existing bin interface (ex. for bin number change)
			_this.settings.scrollTop = $(_this.settings.parentContainer).find('.binInterfaceContainer').scrollTop();
			$(_this.settings.parentContainer).find('.binInterfaceInfoContainer').replaceWith('');
			
			// Define and set bin interface container
			_this.settings.parentContainer.append(	"<div class=\"binInterfaceInfoContainer\">" +
														"<div class=\"binInterfaceContainer\" align=\"left\"></div>" +
												 	"</div>");
			
			// Legend info popup init
			var binInterfaceInfoPopupHTML = "<div class=\"binInterfaceInfoPopup\" style=\"display: none;\">" +
												"<table class=\"innerContentTable\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\">" +
													"<tr><td/><td style=\"vertical-align: middle;\"><b>" + client_messages.text.legend + "</b></td></tr>" +
													"<tr><td style=\"vertical-align: middle;\"><div class=\"insertOptionalIcon\"></div></td><td style=\"vertical-align: middle;\">" + client_messages.text.optional_insert + "</td></tr>" +
													"<tr><td style=\"vertical-align: middle;\"><div class=\"insertMandatoryIcon\"></div></td><td style=\"vertical-align: middle;\">" + client_messages.text.mandatory_insert + "</td></tr>" +
													"<tr><td style=\"vertical-align: middle;\"><div class=\"insertNonSelectableIcon\"></div></td><td style=\"vertical-align: middle;\">" + client_messages.text.non_selectable_insert + "</td></tr>" +
													"<tr><td style=\"vertical-align: middle;\"><div class=\"unassignableIcon\"></div></td><td style=\"vertical-align: middle;\">" + client_messages.text.unassignable_bin + "</td></tr>";
			binInterfaceInfoPopupHTML += 		"</table>" +
											"<div>";
			this.settings.parentContainer.find('.binInterfaceInfoContainer').append("<div class=\"binInterfaceInfoIcon\"></div>");
			this.settings.parentContainer.find('.binInterfaceInfoIcon')
			.append(binInterfaceInfoPopupHTML)
			.mouseover(function(){
				$(this).attr('class','binInterfaceInfoIconHov');
			})
			.mouseout(function(){
				$(this).attr('class','binInterfaceInfoIcon');
			})
			.hoverIntent({
				sensitivity: 1, // number = sensitivity threshold (must be 1 or higher)
				interval: 200,   // number = milliseconds of polling interval
				over: function(){$(_this.settings.parentContainer).find('.binInterfaceInfoPopup').showEle('normal');},
				out: function(){$(_this.settings.parentContainer).find('.binInterfaceInfoPopup').hide();}    
			 });
			
			// No Bins defined - Show message
			if ( $(elem).val() == '' ) {
				_this.settings.parentContainer.find('.binInterfaceContainer').append("<div class=\"interfaceMsgContainer\"></div>");
				_this.settings.parentContainer.find('.binInterfaceContainer .interfaceMsgContainer').append($(_this.settings.parentContainer).find('#binAssignmentMsg_noBins').html());
				return;
			}
			$(_this.settings.parentContainer).find('.binInterfaceContainer').append("<table class=\"innerContentTable binAssignmentTable\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"></table>");
			
			// Bin Row HTML: Define a row template
			var binRowHTML 	=	"<tr>";
			binRowHTML		+=		"<td align=\"center\" style=\"padding: 0px; vertical-align: bottom;\"><div class=\"binRowNumbers\">BIN_RANGE</div></td>";
			for (var i=0; i<_this.settings.binsPerRow; i++)
				binRowHTML	+=		"<td style=\"padding: 0px; vertical-align: bottom;\" class=\"binContainer\"></td>";
			binRowHTML 		+=	"</tr>";
			
			// Init starting bin index (Currently 0 or 1)
			var binIndex = 1;
			if (_this.settings.binStartingIndexOverride != undefined)
				binIndex = _this.settings.binStartingIndexOverride;
			
			// Add number of required bin rows
			for (var j=0; j<_this.settings.numberOfBins/_this.settings.binsPerRow; j++) {
				var startRange = j*_this.settings.binsPerRow + binIndex;
				var endRange = j+1 < _this.settings.numberOfBins/_this.settings.binsPerRow ? startRange+_this.settings.binsPerRow-1 : _this.settings.numberOfBins + (binIndex-1);
				var currentBinRowHTML = binRowHTML.replace('BIN_RANGE','<b>'+startRange+' - '+endRange+'</b>');
				$(_this.settings.parentContainer).find('.binAssignmentTable').append(currentBinRowHTML);
			}
			
			// Populate bins
			for (var k=0; k<_this.settings.numberOfBins; k++) {
				var binHTML = 	"<div class=\"insertBin\" align=\"center\">";
				// Watermark populated (hidden by default)
				binHTML += 			"<div class=\"binNumberWatermark"+(binIndex<9 ? '' : '_doubleDigit')+"\" style=\"display:none;\">"+
										binIndex +
									"</div>";
				// Unassignable icon populated (hidden by default)
				binHTML +=			"<div class=\"unassignableIcon binPositioning\" style=\"display:none;\"></div>";
				binHTML +=			"<div class=\"tenantAssignableIcon binPositioning\" style=\"display:none;\"></div>";
				// If Insert exists - Populate insert
				if (deliveryStatus[k] == '1')
					binHTML += 		"<div class=\"insertOptionalIcon binPositioning\" id=\"insertIcon_"+binAssignments[k]+"\" insertId=\""+binAssignments[k]+"\"></div>";
				else if (deliveryStatus[k] == '2')
					binHTML += 		"<div class=\"insertMandatoryIcon binPositioning\" id=\"insertIcon_"+binAssignments[k]+"\" insertId=\""+binAssignments[k]+"\"></div>";
				else if (deliveryStatus[k] == '3')
					binHTML += 		"<div class=\"insertNonSelectableIcon binPositioning\" id=\"insertIcon_"+binAssignments[k]+"\" insertId=\""+binAssignments[k]+"\"></div>";
				binHTML += 		"</div>";
				
				binIndex++;
				
				var currentBin = $(_this.settings.parentContainer).find('.binContainer:eq('+k+')');
				$(currentBin).append(binHTML);
				// Reveal appropriate bin items
				if (binAssignments[k] == '0')
					$(currentBin).find("[class^='binNumberWatermark']").show();
				else if (binAssignments[k] == '-1')
					$(currentBin).find(".unassignableIcon").show();
				else if (binAssignments[k] == '-2')
					$(currentBin).find(".tenantAssignableIcon").show();
					
			}
			
			// Fill empty TDs to preserve spacing on bin counts < 8
			for (var m=_this.settings.numberOfBins; m<$(_this.settings.parentContainer).find('.binContainer').length; m++) {
				fillerHTML = "<div class=\"nullBinFiller\"></div>";
				$(_this.settings.parentContainer).find('.binContainer:eq('+m+')').append(fillerHTML);
			}
			
			if ( _this.settings.type == 'view' ) {
				$(_this.settings.parentContainer).find("div[id^='insertIcon']").each(function() {
					var insertId = $(this).attr('insertId');
					$(this).insertImagePopup({marginLeft:'-300px', type: 'verbose'});
					if ( _this.settings.linkToInserts )
						$(this).dblclick( function(){ popupLock = true; javascriptHref('insert_view.form?insertId='+insertId+'&insertSchedId='+_this.settings.insertSchedId); } );
				});	
			} else if ( _this.settings.type == 'assignable' ) {
				$(_this.settings.parentContainer).find('.binContainer')
				.mouseover(function(e){_this.binHoverAction(this,e);})
				.mouseout(function(e){_this.binHoverAction(this,e);})
				.click(function(){_this.toggleBinAssignability(this);});
			} else if ( _this.settings.type == 'insertAssignment' || _this.settings.type == 'reservedAssignment' ) {
				// Make bins droppable: Can receive insert elements
				$(_this.settings.parentContainer).find('.binContainer').droppable({
					accept: '.insertDiv,'+_this.settings.anyInsertIcon,
					tolerance: 'pointer',
					drop: function(event, ui) {
						// Don't delete active insert icon if target bin is unavailable or
						// is the originating bin of the active insert
						var insertIdForTargetBin = $(this).find(_this.settings.anyInsertIcon).attr('insertId');
						if (!_this.isBinAvailable($(this)) || insertIdForTargetBin == _this.settings.activeInsertId)
							_this.settings.removeActiveInsert = false;
						
						if (_this.isBinAvailable($(this)))
							_this.placeInsertInBin(ui.helper,$(this));
					},
					over: function(event, ui) { // Hide bin number watermark when visible
						var binWatermark = $(this).find("[class^='binNumberWatermark']");
						if (binWatermark.is(':visible'))
							binWatermark.hide();
					},
					out: function(event, ui) { // Show bin number watermark if bin is available
						var binWatermark = $(this).find("[class^='binNumberWatermark']");
						if (_this.isBinAvailable($(this)))
							binWatermark.showEle('normal');
					}
				});
				// Make insert draggable and set mouseover behavior (i.e. Insert highlighting)
				$(_this.settings.parentContainer).find(_this.settings.anyInsertIcon).each(function(){
					if ( $(this).closest('.binInterfaceInfoPopup').length > 0 )
						return;
					_this.makeInsertDraggable($(this));
					_this.initInsertMouseover($(this));
					_this.initInsertMouseout($(this));
					$(this).insertImagePopup({marginLeft:'80px', type: 'verbose'});
				});
			}

			$(_this.settings.parentContainer).find('.binInterfaceContainer').scrollTop(_this.settings.scrollTop);
			if ($.insertsInterface && $.insertsInterface.ref(_this.settings.insertsInterface))
				$.insertsInterface.ref(_this.settings.insertsInterface).disableInserts(this.getAssignedInserts());
		},
		
		// Set number of bins
		setNumberOfBins : function(numOfBins) {
			var currentBinAssignments = $(this.settings.binAssignmentBinding).val().split(',');
			var resizedBinAssignmentsArray = new Array();
			for (var i=0; i<numOfBins; i++) {
				if (currentBinAssignments[i])
					resizedBinAssignmentsArray[i] = currentBinAssignments[i];
				else
					resizedBinAssignmentsArray[i] = "0";
			}
			$(this.settings.binAssignmentBinding).val(resizedBinAssignmentsArray.join(','));
			this.settings.numberOfBins = numOfBins;
			this.refreshInsertDeliveryStatusBinding();
			this.init(this.settings.binAssignmentBinding,this.settings);
		},
		
		// Bin hover action: Used for type:assignable bin mouseovers
		binHoverAction : function(elem,e) {
			var _this = this;
			var insertIcon = $(elem).find(_this.settings.anyInsertIcon);
			var targetIcon = null;
			if ( _this.settings.type == 'assignable' )
				targetIcon = $(elem).find(".unassignableIcon");
			
			if (	e.type == "mouseover" && 
					$(elem).find(".tenantAssignableIcon").is(":hidden") &&
					$(elem).find(".unassignableIcon").is(":hidden") ) {
				$(elem).find("[class^='binNumberWatermark']").hide();
				$(insertIcon).hide();
				targetIcon.css('opacity',0.5).attr('markAssignable','true').show();
			} else if (e.type == "mouseout" && targetIcon.attr('markAssignable')) {
				targetIcon.hide().css('opacity',1).removeAttr('markAssignable');
				if ((insertIcon).length > 0)
					$(insertIcon).show();
				else
					$(elem).find("[class^='binNumberWatermark']").show();
			}
		},
		
		binHighlightAction : function(action, insertId) {
			if (action == "on") {
				$(this.settings.parentContainer).find('#insertIcon_'+insertId).closest('.binContainer').addClass('insertHovFrame');
			} else if (action == "off") {
				$(this.settings.parentContainer).find('#insertIcon_'+insertId).closest('.binContainer').removeClass('insertHovFrame');
			}
			var firstBinContainer = $(this.settings.parentContainer).find('#insertIcon_'+insertId+':first').closest('.binContainer');
			if ($(firstBinContainer).get(0))
				$(this.settings.parentContainer).find('.binInterfaceContainer').scrollTo($(firstBinContainer),0);
		},
		
		// Refresh Bin Assignments binding
		refreshBinAssignmentsBinding : function() {
			var newBinAssignmentArray = new Array();
			for (var i=0; i<this.settings.numberOfBins; i++) {
				var currentBin = $(this.settings.parentContainer).find('.binContainer:eq('+i+')');
				if ($(currentBin).find('.nullBinFiller').get(0))
					break;
				else if ( $(currentBin).find(this.settings.mandatoryInsertIcon).length > 0 )
					newBinAssignmentArray[i] = $(currentBin).find(this.settings.mandatoryInsertIcon).attr('insertId');
				else if ( $(currentBin).find(this.settings.optionalInsertIcon).length > 0 )
					newBinAssignmentArray[i] = $(currentBin).find(this.settings.optionalInsertIcon).attr('insertId');
				else if ( $(currentBin).find(this.settings.nonSelectableInsertIcon).length > 0 )
					newBinAssignmentArray[i] = $(currentBin).find(this.settings.nonSelectableInsertIcon).attr('insertId');
				else if ( !$(currentBin).find('.unassignableIcon').is(':hidden') )
					newBinAssignmentArray[i] = "-1";
				else if ( !$(currentBin).find('.tenantAssignableIcon').is(':hidden') )
					newBinAssignmentArray[i] = "-2";
				else
					newBinAssignmentArray[i] = "0";
			}
			this.settings.binAssignmentBinding.val(newBinAssignmentArray.join(','));
		},
		
		// Refresh Insert Delivery Status binding
		refreshInsertDeliveryStatusBinding : function() {
			var newDeliveryStatusArray = new Array();
			for (var i=0; i<this.settings.numberOfBins; i++) {
				var currentBin = $(this.settings.parentContainer).find('.binContainer:eq('+i+')');
				if ($(currentBin).find(this.settings.nonSelectableInsertIcon).length > 0)
					newDeliveryStatusArray[i] = "3";
				else if ($(currentBin).find(this.settings.mandatoryInsertIcon).length > 0)
					newDeliveryStatusArray[i] = "2";
				else if ($(currentBin).find(this.settings.optionalInsertIcon).length > 0)
					newDeliveryStatusArray[i] = "1";
				else
					newDeliveryStatusArray[i] = "0";
				
			}
			$(this.settings.deliveryStatusBinding).val(newDeliveryStatusArray.join(','));
		},
		
		// Onclick function to toggle bin assignability
		toggleBinAssignability : function(bin) {
			var _this = this;
			var targetIcon = null; 
			if ( _this.settings.type == 'assignable' )
				targetIcon = $(bin).find(".unassignableIcon");

			if (targetIcon.attr('markAssignable')) {
				$(bin).find(_this.settings.anyInsertIcon).remove();
				targetIcon.css('opacity',1).removeAttr('markAssignable');
			} else {
				targetIcon.hide();
				if ( 	$(bin).find(".tenantAssignableIcon").is(':hidden') && 
						$(bin).find(".unassignableIcon").is(':hidden') )
					$(bin).find("[class^='binNumberWatermark']").show();
			}
			this.refreshBinAssignmentsBinding();
			this.refreshInsertDeliveryStatusBinding();
		},
		
		// Determine if bin currently has an insert or is unassignable
		isBinAvailable : function(binEle) {
			var isUnassignable = 	$(binEle).find('.unassignableIcon').is(':visible') ||
									$(binEle).find('.tenantAssignableIcon').is(':visible');
			var hasInsert = $(binEle).find(this.settings.anyInsertIcon).length > 0;
			return (!isUnassignable && !hasInsert);
		},
		
		// Make Insert draggable: Permits dragging from bin to bin
		//	- If insert is dropped outside of a bin it is removed
		//	- appendTo: 'body' fixes an IE6 display issue
		//	- this.settings.removeActiveInsert is communication flag between dragging and dropping behavior
		makeInsertDraggable : function(insertEle) {
			var _this = this;
			if (_this.settings.type != 'reservedAssignment' && $(insertEle).hasClass(_this.settings.nonSelectableInsertIconClass))
				return;

			$(insertEle).draggable({
				cursor: 'default',
				cursorAt: { top: 10, left: 10},
				opacity: 0.7,
				helper: 'clone',
				scroll: false,
				appendTo: 'body',
				stop: function(event, ui) {
					// On drag stop: Remove insert (droppable interface manages bin inserting)
					if (_this.settings.removeActiveInsert)
						$(this).remove();

					_this.refreshBinAssignmentsBinding();
					_this.refreshInsertDeliveryStatusBinding();
					
					var insertId = $(this).attr('insertId');
					var insertName = $.insertsInterface.ref(_this.settings.insertsInterface).getInsertName(insertId);
					var isBinHopAction = _this.containsInsert(insertId);

					if (_this.settings.removeActiveInsert && $.insertsPriorityInterface.ref(_this.settings.insertPriorityInterface)) {
						$.insertsPriorityInterface.ref(_this.settings.insertPriorityInterface).removePriorityInsert(insertId);
						if (isBinHopAction && $(this).hasClass(_this.settings.optionalInsertIconClass)) 
							$.insertsPriorityInterface.ref(_this.settings.insertPriorityInterface).addPriorityInsert(insertId,insertName);
					}
					
					_this.init( _this.settings.binAssignmentBinding,_this.settings);
				},
				start: function(event, ui) {
					// Set flags for insert delete check
					_this.settings.removeActiveInsert = true;
					_this.settings.activeInsertId = $(this).attr('insertId');
				}
			});
		},
		
		// Insert Mouseover: Add highlighting of insert to insert interface
		initInsertMouseover : function(insertEle) {
			var _this = this;
			$(insertEle).mouseover( function() {
				var insertId = $(this).attr('insertId');
				$.insertsInterface.ref(_this.settings.insertsInterface).insertHighlightAction('on',insertId);
			});
		},
		
		// Insert Mouseout: Remove insert highlighting from insert interface
		initInsertMouseout : function(insertEle) {
			var _this = this;
			$(insertEle).mouseout( function() {
				var insertId = $(this).attr('insertId');
				$.insertsInterface.ref(_this.settings.insertsInterface).insertHighlightAction('off',insertId);
			});
		},
		
		// Place Insert in Bin
		placeInsertInBin : function(insertDropEle,binContainer) {
			// Get insert element from container
			var insertDiv = $(insertDropEle).find(this.settings.anyInsertIcon);
			// If no container: Insert element is same as insertDropEle
			if ($(insertDropEle).is(this.settings.anyInsertIcon))
				insertDiv = $(insertDropEle);
			
			// Init dropped insert element: Style,draggable,mouseover
			insertDiv.addClass('binPositioning');
			insertDiv.attr('id','insertIcon_'+insertDiv.attr('insertId'));
			$(binContainer).find('.insertBin').append(insertDiv);
			this.makeInsertDraggable(insertDiv);
			this.initInsertMouseover(insertDiv);
			this.initInsertMouseout(insertDiv);
			// Insert popup init
			insertDiv.removeClass('insertPopupInit');
			$(insertDiv).insertImagePopup({marginLeft:'80px', type: 'verbose'});
			
			// Refresh bindings to account for newly dropped insert
			this.refreshBinAssignmentsBinding();
			this.refreshInsertDeliveryStatusBinding();
			
			if ($(insertDiv).hasClass(this.settings.optionalInsertIconClass)) {
				var insertId = $(insertDiv).attr('insertId');
				var insertName = $.insertsInterface.ref(this.settings.insertsInterface).getInsertName(insertId);
				if ($.insertsPriorityInterface.ref(this.settings.insertPriorityInterface))
					$.insertsPriorityInterface.ref(this.settings.insertPriorityInterface).addPriorityInsert(insertId,insertName);
			}
			
			$.insertsInterface.ref(this.settings.insertsInterface).disableInserts(this.getAssignedInserts());
		},
		
		getAssignedInserts : function() {
			return $(this.settings.parentContainer).find('.binInterfaceContainer').find(this.settings.anyInsertIcon);
		},
		
		getAssignedOptionalInserts : function() {
			return $(this.settings.parentContainer).find('.binInterfaceContainer').find(this.settings.optionalInsertIcon);
		},
		
		containsInsert : function(insertId) {
			var assignedInserts = $(this.settings.binAssignmentBinding).val().split(',');
			var containsInsert = false;
			for (var i=0; i< assignedInserts.length; i++)
				if (assignedInserts[i] == insertId)
					containsInsert = true;
			return containsInsert;
		}
		
		};
	};
	
	// instance manager
	binAssignmentInterface_component.inst = {};

})(jQuery);	