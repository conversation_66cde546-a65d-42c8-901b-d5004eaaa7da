/** connectedStyleManager - copied from jquery.styleManager.js
 * 
 * Dependencies:
 * jQuery 1.8 or later
 * 
 */

var connectedStyleManager_style_data = {};
var connectedStyleManager_text_style_data = {};
var connectedStyleManager_paragraph_style_data = {};
var connectedStyleManager_list_style_data = {};
var connectedStyleManager_parsed_text_styles = [];
var connectedStyleManager_parsed_paragraph_styles = [];
var connectedStyleManager_css_container = null;
var connectedStyleManager_global_init = false;

function applyConnectedStyleManagerForTag(mainContainerSelection, textStyle, phStyle){
	$(mainContainerSelection).connectedStyleManager({
		text_data: textStyle,
		paragraph_data: phStyle,
		channel: 2
	});
	console.log("Style Rendering Manager: init style manager.");
}

function updateConnectedStyleManagerReferenceCSS(mainContainerSelection, textStyle, phStyle){
	$(mainContainerSelection).connectedStyleManager({
		text_data: textStyle,
		paragraph_data: phStyle,
		channel: 2
	});
	$.connectedStyleManager.get($(mainContainerSelection)).updateConnectedStyleReferenceCSS();
	console.log("Style Rendering Manager: update style reference css.");
}

(function($) {
	
	$.connectedStyleManager = {
		get	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return connectedStyleManager_component.inst[o.attr('id')] || null;
		},
		defaults : {
			document					: document,
			indicator 					: {
				primary_joiner 			: "--",
				sub_joiner 				: "-",
				color_joiner 			: ";",
				dspScale				: 1.000,
				paragraph_class_prefix	: 'tinymce_paragraphstyle_'
			}
		}
	};
	
	$.fn.connectedStyleManager = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if (conf !== false) {
				new connectedStyleManager_component().init(this, conf);
			}
		});
	};
	
	function connectedStyleManager_component () {
		return {
			
		data : $.extend({},$.connectedStyleManager.defaults),

		init : function(elem, conf) {
			var _this = this;

			connectedStyleManager_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(false, {}, this.data, conf);
			_this.targetEle = $(elem);


			let defaultDspScale = 1.000;
			if (
				TinyMcePluginsConnected
				&& TinyMcePluginsConnected.constants
				&& TinyMcePluginsConnected.constants.dspScale
			) {
				defaultDspScale = TinyMcePluginsConnected.constants.dspScale;
			}

			if ( _this.data.channel != undefined && (parseInt(_this.data.channel) == 3 || parseInt(_this.data.channel) == 4) ) {
				_this.data.indicator.dspScale = 1.000;
			} else {
				_this.data.indicator.dspScale = defaultDspScale;
			}

			// TEXT STYLES: Format JSON
			if (_this.data.text_data !== undefined && _this.data.text_data != null) {
				for (var i = 0; i < _this.data.text_data.length; i++) {
					if (_.isString(_this.data.text_data[i])) {
						_this.data.text_data[i] = mpParseJSON(_this.data.text_data[i]);
					}

					if (_this.data.text_data[i].has_variations) {
						_this.data.apply_text_style_variations = true;
						if (_this.data.text_data[i].variation_data.toggle_color) {
							if (_.isString(_this.data.text_data[i].variation_data.colors)) {
								_this.data.text_data[i].variation_data.colors = mpParseJSON(_this.data.text_data[i].variation_data.colors);
							}
						}

						if (_this.data.text_data[i].variation_data.toggle_point_size) {
							if (_.isString(_this.data.text_data[i].variation_data.point_sizes)) {
								_this.data.text_data[i].variation_data.point_sizes = mpParseJSON(_this.data.text_data[i].variation_data.point_sizes);
							}
						}
					}
				}

				if (_this.data.apply_text_style_variations) {
					// DATA: Init style css data
					var styleClasses = new Object();
					for (var i = 0; i < _this.data.text_data.length; i++) {
						styleClasses[_this.data.text_data[i].title] = {
							css_attributes: _this.data.text_data[i].css_attributes,
							variation_data: _this.data.text_data[i].has_variations ?
								_this.data.text_data[i].variation_data :
								null
						};
					}
					_this.text_styles = styleClasses;
				}

			}

			// PARAGRAPH STYLES: Format JSON
			if (_this.data.paragraph_data != undefined && _this.data.paragraph_data != null) {
				for (var i = 0; i < _this.data.paragraph_data.length; i++) {
					if (_.isString(_this.data.paragraph_data[i])) {
						_this.data.paragraph_data[i] = mpParseJSON(_this.data.paragraph_data[i]);
					}

					if (_this.data.paragraph_data[i].has_variations) {
						_this.data.apply_paragraph_style_variations = true;
						if (_this.data.paragraph_data[i].variation_data.toggle_line_spacings) {
							if (_.isString(_this.data.paragraph_data[i].variation_data.line_spacings)) {
								_this.data.paragraph_data[i].variation_data.line_spacings = mpParseJSON(_this.data.paragraph_data[i].variation_data.line_spacings);
							}
						}

					}
				}

				if (_this.data.apply_paragraph_style_variations) {
					// DATA: Init style css data
					var styleClasses = new Object();
					for (var i = 0; i < _this.data.paragraph_data.length; i++) {
						styleClasses[_this.data.paragraph_data[i].name] = {
							css_attributes: _this.data.paragraph_data[i].css_attributes,
							is_fixed_spacing: _this.data.paragraph_data[i].is_fixed_spacing,
							variation_data: _this.data.paragraph_data[i].has_variations ?
								_this.data.paragraph_data[i].variation_data :
								null
						};
					}
					_this.paragraph_styles = styleClasses;
				}

			}

			if ( $(_this.data.document).find('head').find('.compoundContentStyles').length == 0 ) {
				var cssContainer = $("<style class=\"compoundContentStyles\"></style>");
				$(_this.data.document).find('head').append(cssContainer);
				connectedStyleManager_css_container = cssContainer;
			}

			// _this.updateConnectedStyleReferenceCSS();


		},

		CMYKtoHEX : function(cmykVal) {
			
			var _this = this;

			if ( !cmykVal || cmykVal == null )
				return "#000000";
			
		    function padZero(str) {
		        return "000000".substr(str.length) + str;
		    }
			
			var hexVal = cmykVal.replace("cmyk(","").replace(")","");
			
			var cmykValues = hexVal.split(_this.data.indicator.color_joiner);
			var C = parseFloat(cmykValues[0])/100;
			var M = parseFloat(cmykValues[1])/100;
			var Y = parseFloat(cmykValues[2])/100;
			var K = parseFloat(cmykValues[3])/100;

		    var cyan = (C * 255 * (1-K)) << 16;
		    var magenta = (M * 255 * (1-K)) << 8;
		    var yellow = (Y * 255 * (1-K)) >> 0;

		    var black = 255 * (1-K);
		    var white = black | black << 8 | black << 16;

		    var color = white - (cyan | magenta | yellow );

		    return ("#"+padZero(color.toString(16)));

		},

		getStyleAttr : function(css, attr) {
			if (css != undefined && css != '') {
				var attrArray = css.split(";");
				for (var i = 0; i < attrArray.length; i++) {
					var currentAttr = attrArray[i].split(":");
					if ($.trim(currentAttr[0]) == attr)
						return $.trim(currentAttr[1]);
				}
			}
		},
		
		replaceAll : function(target, str, sub) {
			while (target.indexOf(str) != -1)
				target = target.replace(str,sub);
			return target;
		},


			parseListAttributes : function(list) {
				var _this = this;
				function asciiToHex(str) {
					return Array.from(str)
						.map(char => char.charCodeAt(0).toString(16))
						.join('');
				}

				function rgbToCmyk(args) {
					const r = args[0] / 255;
					const g = args[1] / 255;
					const b = args[2] / 255;

					const k = Math.min(1 - r, 1 - g, 1 - b);
					const c = (1 - r - k) / (1 - k) || 0;
					const m = (1 - g - k) / (1 - k) || 0;
					const y = (1 - b - k) / (1 - k) || 0;

					return [c * 100, m * 100, y * 100, k * 100];
				}

				function getAttr(attr, list) {
					if (!list || list.length === 0) return null;

					var ulComputedStyle = window.getComputedStyle(list[0]);
					var liComputedStyle = window.getComputedStyle($(list).find('li')[0]);
					var liBeforeComputedStyle = window.getComputedStyle($(list).find('li')[0], ':before');
					var contentComputedStyle = window.getComputedStyle($(list).find('li .mceListItemContent')[0]);

					if (list.attr(attr)) {
						switch (attr) {
							case 'bullet_spacing':
							case 'list_spacing':
								var spacingValues = list.attr(attr).split(':').map(function (val) {
									return parseFloat(val.replace('in', ''));
								});

								return {
									top: spacingValues[0],
									right: spacingValues[1],
									bottom: spacingValues[2],
									left: spacingValues[3]
								};
							case 'bullet_symbol':
								// Here we have the symbol in hex format
								var symbol = list.attr(attr);
								symbol = symbol.includes('counter') ? null : list.attr(attr);
								return symbol;
							case 'bullet_color':
							case 'bullet_font':
							case 'bullet_size':
							case 'text_align':
								return list.attr(attr);
							case 'line_spacing':
								var lineSpacing = list.attr(attr);
								var value = parseFloat(lineSpacing);
								var type = lineSpacing.slice(-1) === 'm' ? 1 : 2;
								return {
									value: value,
									type: type
								};
						}
					} else {
						switch (attr) {
							case 'bullet_spacing':
								var top = contentComputedStyle.getPropertyValue('padding-top');
								var bottom = contentComputedStyle.getPropertyValue('padding-bottom');
								var right = parseFloat(contentComputedStyle.getPropertyValue('left')) - parseFloat(liBeforeComputedStyle.getPropertyValue('left'));
								var left = liBeforeComputedStyle.getPropertyValue('left');

								return {
									top: parseFloat(top) / 100,
									right: parseFloat(right) / 100,
									bottom: parseFloat(bottom) / 100,
									left: parseFloat(left) / 100
								};
							case 'list_spacing':
								var top = ulComputedStyle.getPropertyValue('padding-top');
								var bottom = ulComputedStyle.getPropertyValue('padding-bottom');
								var right = parseFloat(liComputedStyle.getPropertyValue('width')) - parseFloat(contentComputedStyle.getPropertyValue('width')) - parseFloat(contentComputedStyle.getPropertyValue('left'));
								var left = 0;
								return {
									top: parseFloat(top) / 100,
									right: parseFloat(right) / 100,
									bottom: parseFloat(bottom) / 100,
									left: parseFloat(left) / 100
								};
							case 'bullet_symbol':
								// Here we have the symbol in ascii format
								var symbol = liBeforeComputedStyle.getPropertyValue('content');
								symbol = symbol.includes('counter') || symbol == "none" ? "none" : asciiToHex(symbol.replace(/"/g, ''));
								return symbol;
							case 'bullet_color':
								// Conver color from rgb to hex
								var color = liBeforeComputedStyle.getPropertyValue('color');

								if (color.includes('rgb')) {
									var rgb = color.match(/\d+/g);
									color = rgbToCmyk(rgb).map((num) => Number(num.toFixed(0)));
									;
									color = 'cmyk(' + color.join(';') + ')';
								}

								return color;
							case 'bullet_font':
								let tmpFontFamily = liBeforeComputedStyle.getPropertyValue('font-family');
								if (TinyMcePluginsConnected.computeNormalizedBulletFontFamily) {
									tmpFontFamily = TinyMcePluginsConnected.computeNormalizedBulletFontFamily('' + tmpFontFamily);
								}
								return tmpFontFamily;
							case 'bullet_size':
								var fontSize = liBeforeComputedStyle.getPropertyValue('font-size');
								// convert font size to pt
								var fontSizePt = (parseFloat(fontSize) * 0.75).toFixed(2);
								return fontSizePt;
							case 'text_align':
								var textAlign = ulComputedStyle.getPropertyValue('text-align');
								return textAlign;
							case 'line_spacing':
								var lineHeight = liComputedStyle.getPropertyValue('line-height');
								var fontSize = parseFloat(liComputedStyle.getPropertyValue('font-size'));

								var value = parseFloat(lineHeight);
								var type = lineHeight.includes('px') ? 1 : 2;

								if (type === 1) {
									// Convert px to unitless by dividing by font size
									value = value / fontSize;
								}

								return {
									value: value,
									type: type
								};
						}
					}
					return null;
				}

				var listAttributes = {};

				// List Text Alignment
				listAttributes.text_align = getAttr('text_align', list);

				// Bullet Spacing
				listAttributes.bullet_spacing = getAttr('bullet_spacing', list);

				// List Spacing
				listAttributes.list_spacing = getAttr('list_spacing', list);

				// Bullet Symbol Overrides
				var bulletSymbol = getAttr('bullet_symbol', list);
				var bulletColor = getAttr('bullet_color', list);
				var bulletFont = getAttr('bullet_font', list);
				var bulletSize = getAttr('bullet_size', list);

				if (bulletSymbol || bulletColor || bulletFont || bulletSize) {
					listAttributes.bullet = {
						symbol: bulletSymbol,
						color: bulletColor,
						font: bulletFont,
						size: bulletSize
					};
				}

				// Line Spacing
				listAttributes.line_spacing = getAttr('line_spacing', list);

				return listAttributes;
			},

			updateCustomListStyles : function(list) {
			var _this = this;

			function componentToHex(c) {
				var hex = Math.round(c).toString(16);
				return hex.length === 1 ? "0" + hex : hex;
			}

			function cmykToHex(cmykString) {
				// Extract CMYK values from the input string
				var cmykValues = cmykString.slice(5, -1).split(';').map(Number);

				// Destructure the array into individual variables
				var c = cmykValues[0];
				var m = cmykValues[1];
				var y = cmykValues[2];
				var k = cmykValues[3];

				// Ensure values are within valid range (0 to 100)
				c = Math.max(0, Math.min(100, c));
				m = Math.max(0, Math.min(100, m));
				y = Math.max(0, Math.min(100, y));
				k = Math.max(0, Math.min(100, k));

				// Convert CMYK to RGB
				var r = 255 * (1 - c / 100) * (1 - k / 100);
				var g = 255 * (1 - m / 100) * (1 - k / 100);
				var b = 255 * (1 - y / 100) * (1 - k / 100);

				// Convert RGB to HEX
				var hex = "#" + componentToHex(r) + componentToHex(g) + componentToHex(b);
				return hex;
			}

			function parseValue(value) {
				return parseFloat(value) * 100 || 0;
			}

			var $list = $(list);

			var custListAttr = ['text_align', 'bullet_spacing','list_spacing','bullet_symbol','bullet_color','bullet_font','bullet_size','line_spacing'];
			// Determine if current list has at least one custom attribute; otherwise return;
			if ( custListAttr.every(function(attr) { return !$list.attr(attr); }) )
				return;

			var listId = $list.attr('custom_list_id') || Math.random().toString(36).substr(2, 9);
			$list.attr('custom_list_id', listId);

			// Remove existing custom list styles if any
			$('#customListStyles_' + listId).remove();

			var listAttr = _this.parseListAttributes($list);
			var selector = $list.prop('tagName').toLowerCase() + '[custom_list_id="' + listId + '"]';

			var textAlign = listAttr.text_align;

			var bulletRightSpacing = parseValue(listAttr.bullet_spacing.right);
			var bulletLeftSpacing = parseValue(listAttr.bullet_spacing.left);
			var bulletTopSpacing = parseValue(listAttr.bullet_spacing.top);
			var bulletBottomSpacing = parseValue(listAttr.bullet_spacing.bottom);

			var listSpacingTop = parseValue(listAttr.list_spacing.top);
			var listSpacingBottom = parseValue(listAttr.list_spacing.bottom);
			var listSpacingRight = parseValue(listAttr.list_spacing.right);

			var lineSpacingValue = listAttr.line_spacing.value;
			var lineSpacingType = listAttr.line_spacing.type;

			var bullet = listAttr.bullet;
			var bulletSymbol = bullet.symbol;
			var bulletColor = bullet.color;
			var bulletFont = bullet.font;
			var bulletSize = bullet.size;

			var listStyle = '';
			if (listSpacingTop > 0)
				listStyle += 'padding-top:' + listSpacingTop + 'px;';
			if (listSpacingBottom > 0)
				listStyle += 'padding-bottom:' + listSpacingBottom + 'px;';
			if (textAlign)
				listStyle += 'text-align:' + textAlign + ';';

			var bulletContentStyle = "";
			bulletContentStyle += 'padding-top:' + bulletTopSpacing + 'px;';
			bulletContentStyle += 'padding-bottom:' + bulletBottomSpacing + 'px;';
			bulletContentStyle += 'left: ' + (bulletLeftSpacing + bulletRightSpacing) + 'px;';
			bulletContentStyle += 'width: calc(100% - ' + (bulletLeftSpacing + bulletRightSpacing + listSpacingRight) + 'px);';
			bulletContentStyle += 'line-height:' + (lineSpacingValue > 0 ? lineSpacingValue : 1) + (lineSpacingType == 2 ? 'pt': '') + ';';

			var bulletBefore = 'left: ' + bulletLeftSpacing + 'px; padding-top:' + bulletTopSpacing + 'px;';

			if (bullet) {
				if (bulletSymbol && bulletSymbol !== 'none') {
					bulletBefore += 'content: "\\'  + bulletSymbol + '";';
				}
				bulletBefore += "color: " + (bulletColor.includes('cmyk') ? cmykToHex(bulletColor) : bulletColor) + ";" +
					"font-size: " + bulletSize + "pt;" +
					"font-family: " + bulletFont + ";"
			}

			var listStyleCSS =
				selector + ' > li:before{' +
				bulletBefore +
				'} \n' +
				selector + ' {' +
				listStyle +
				'} \n' +
				selector + ' > li > div.mceListItemContent {' +
				bulletContentStyle +
				'} \n';

			var modifiedStyleCSS = listStyleCSS.replace(/;/g, ' !important;');

			// Add new custom list styles
			$('head').append("<style id=\"customListStyles_"+listId+"\" type=\"text/css\">" + modifiedStyleCSS + "</style>")

		},

		updateCustomParagraphStyles: function(paragraph, paragraph_styles) {
			var customAttributes = ['text_align', 'spacing', 'tab_incr', 'line_spacing', 'indent_type', 'special_indent'];

			var hasCustomAttributes = customAttributes.some(function(attr) {
				return paragraph.getAttribute(attr) !== null;
			});

			if (!hasCustomAttributes)
				return;

			function getIntValue(inputValue) {
				var value = $.trim($(inputValue).val());

				// Check if the value is not empty and greater than 0
				if (value !== '' && parseFloat(value) > 0) {
					return Math.round(parseFloat(value) * 72);
				}
				return 0;
			}

			function pxToInches(px) {
				var ppi = 100;
				return parseFloat(px) / ppi;
			}

			function inchesToPx(inches) {
				var ppi = 100;
				return parseFloat(inches) * ppi;
			}

			function inchesToPt(inches) {
				var pointsPerInch = 72; // Standard points per inch
				return inches * pointsPerInch;
			}

			function pxToPt(px) {
				var pxToPtFactor = 0.74999943307122;
				return parseFloat(px) * pxToPtFactor;
			}

			function ptToPx(pt) {
				var pxToPtFactor = 0.74999943307122;
				return parseFloat(pt) / pxToPtFactor;
			}

			function parseParagraphAttributes(paragraph, cleanStyle, paragraph_styles) {

				var $paragraph = $(paragraph);
				var paragraphId = $paragraph.attr('custom_paragraph_id');
				if (paragraphId && cleanStyle) {
					// Remove existing custom paragraph styles if any
					$('#customParagraphStyles_' + paragraphId).remove();
				}

				function getAttr(attr, paragraph, paragraph_styles) {
					if (!paragraph || paragraph.length === 0) return null;

					var pComputedStyle = window.getComputedStyle(paragraph[0]);
					var paragraphClass = paragraph.attr('paragraphclass') || "";
					var primaryJoiner = "--";
					var paragraphClassPrefix = "tinymce_paragraphstyle_";
					var currentStyle = paragraphClass.replace(paragraphClassPrefix, "");
					var styleBase = currentStyle.split(primaryJoiner)[0];


					if (paragraph.attr(attr)) {
						switch (attr) {
							case 'text_align':
							case 'tab_incr':
							case 'line_spacing':
								return paragraph.attr(attr);
							case 'spacing':
								// In inches
								var spacingValues = paragraph.attr(attr).split(':').map(function(val) {
									return parseFloat(val.replace('in', ''));
								});

								return {
									top: spacingValues[0],
									right: spacingValues[1],
									bottom: spacingValues[2],
									left: spacingValues[3]
								};
							case 'indent_type':
							case 'special_indent':
								return {
									type: paragraph.attr('indent_type'),
									value: parseFloat(paragraph.attr('special_indent'))
								};
						}
					} else {
						switch (attr) {
							case 'text_align':
								return pComputedStyle.getPropertyValue('text-align');
							case 'spacing':
								var topSpacing = pComputedStyle.getPropertyValue('padding-top');
								var rightSpacing = pComputedStyle.getPropertyValue('padding-right');
								var bottomSpacing = pComputedStyle.getPropertyValue('padding-bottom');
								var leftSpacing = pComputedStyle.getPropertyValue('padding-left');

								var spacingInPx = [topSpacing, rightSpacing, bottomSpacing, leftSpacing];

								// Convert px to inches
								var spacingValues = spacingInPx.map(function(val) {
									var inchValue = pxToInches(val.replace('in', ''));
									return Math.round(inchValue * 100) / 100; // Round to 2 decimal places
								});

								return {
									top: spacingValues[0],
									right: spacingValues[1],
									bottom: spacingValues[2],
									left: spacingValues[3]
								};
							case 'tab_incr':
								var paragraphClass = paragraph.attr('paragraphclass');
								if (!paragraphClass) {
									return 0.25;
								}


								if (paragraph_styles &&
									paragraph_styles[styleBase] &&
									paragraph_styles[styleBase].variation_data) {

									return paragraph_styles[styleBase].variation_data.margin_incr / 100;
								}

								return 0.25;
							case 'line_spacing':
								var paragraphClass = paragraph.attr('paragraphclass');
								var fontSizePx = pComputedStyle.getPropertyValue('font-size');
								var lineHeightPx = pComputedStyle.getPropertyValue('line-height');

								var unitlessLineHeight = parseFloat(lineHeightPx) / parseFloat(fontSizePx); // Ratio of line-height to font-size

								if (!paragraphClass) {
									return unitlessLineHeight.toFixed(2);
								}

								if (paragraph_styles &&
									paragraph_styles[styleBase] &&
									paragraph_styles[styleBase].is_fixed_spacing) { // Check if spacing is in pt
									var pxToPtFactor = 0.74999943307122;
									var displayScale = 100 / 96;
									var lineHeightInPt = parseFloat(lineHeightPx) * (pxToPtFactor / displayScale);
									return lineHeightInPt.toFixed(2) + 'pt';
								} else {
									//unitless
									return unitlessLineHeight.toFixed(2);
								}
							case 'indent_type':
							case 'special_indent':
								var textIndent = parseFloat(pComputedStyle.getPropertyValue('text-indent')) / 100;

								if (textIndent === 0) {
									return { type: "", value: 0};
								}
								return {
									type: textIndent > 0 ? 'first' : 'hanging',
									value: textIndent
								};
						}
					}
					return null;
				}

				var paragraphAttributes = {};

				paragraphAttributes.text_align = getAttr('text_align', paragraph, paragraph_styles);
				paragraphAttributes.spacing = getAttr('spacing', paragraph, paragraph_styles);
				paragraphAttributes.tab_incr = getAttr('tab_incr', paragraph, paragraph_styles);
				paragraphAttributes.line_spacing = getAttr('line_spacing', paragraph, paragraph_styles);
				paragraphAttributes.indent_type = getAttr('indent_type', paragraph, paragraph_styles);
				paragraphAttributes.special = getAttr('special_indent', paragraph, paragraph_styles);

				return paragraphAttributes;
			}

			var $paragraph = $(paragraph);

			var custParagraphAttr = ['text_align', 'spacing', 'tab_incr', 'line_spacing', 'indent_type', 'special_indent'];
			// Determine if current paragraph has at least one custom attribute; otherwise return;
			if ( custParagraphAttr.every(function(attr) { return !$paragraph.attr(attr); }) ) {

				// Remove existing custom paragraph styles if any
				if($paragraph.attr('custom_paragraph_id')){
					$('#customParagraphStyles_' + $paragraph.attr('custom_paragraph_id')).remove();
					$paragraph.removeAttr('custom_paragraph_id');
				}

				return;
			}

			var paragraphId = $paragraph.attr('custom_paragraph_id') || Math.random().toString(36).substr(2, 9);
			$paragraph.attr('custom_paragraph_id', paragraphId);

			// Remove existing custom paragraph styles if any
			$('#customParagraphStyles_' + paragraphId).remove();

			var paragraphAttr = parseParagraphAttributes($paragraph, true, paragraph_styles);
			var selector = $paragraph.prop('tagName').toLowerCase() + '[custom_paragraph_id="' + paragraphId + '"]';

			var textAlign = paragraphAttr.text_align;

			var spacingLeft = inchesToPx(paragraphAttr.spacing.left);
			var spacingRight = inchesToPx(paragraphAttr.spacing.right);

			var spacingBottom = inchesToPx(paragraphAttr.spacing.bottom);
			var spacingTop = inchesToPx(paragraphAttr.spacing.top);

			// var increments = parseValue(paragraphAttr.increments);

			var lineSpacingType = paragraphAttr.line_spacing.toString().slice(-2) === 'pt' ? 2 : 1;
			var lineSpacing = paragraphAttr.line_spacing;

			var specialType = paragraphAttr.special.type;
			var specialIndent = inchesToPx(paragraphAttr.special.value);

			var paragraphStyle = '';
			if (textAlign)
				paragraphStyle += 'text-align:' + textAlign + ';';

			var marginLeft = spacingLeft - (specialIndent < 0 ? specialIndent : 0);
			paragraphStyle += 'padding-left: ' + marginLeft + 'px;';
			paragraphStyle += 'padding-right: ' + spacingRight + 'px;';
			paragraphStyle += 'padding-bottom: ' + spacingBottom + 'px;';
			paragraphStyle += 'padding-top: ' + spacingTop + 'px;';

			paragraphStyle += 'line-height: ' + lineSpacing + ';';

			paragraphStyle += 'text-indent: ' + specialIndent + 'px;';

			var paragraphStyleCSS =
				selector + ' {' +
				paragraphStyle +
				'} \n';

			paragraphStyleCSS = paragraphStyleCSS.replace(/;/g, ' !important;');

			// Add new custom paragraph styles
			$('head').append("<style id=\"customParagraphStyles_"+paragraphId+"\" type=\"text/css\">" + paragraphStyleCSS + "</style>")

		},

		updateConnectedStyleReferenceCSS : function() {
			var _this = this;
			var text_styles = _this.text_styles;

			let normalizeCssAttrs = (cssAttrsParam) => {
				let cssAttrsString = cssAttrsParam.join(' ');
				let cssAttrNames = {};
				let inputCssAttrsArray = cssAttrsString.split(";").map(item => item.trim());
				let outputCssAttrsArray = [];
				for (let j = inputCssAttrsArray.length - 1; j >= 0; j--) {
					let crtCssAttrNameAndValue = inputCssAttrsArray[j];
					let index = crtCssAttrNameAndValue.indexOf(":");
					if (index === -1) {
						outputCssAttrsArray.push(crtCssAttrNameAndValue);
						continue;
					}
					let crtCssAttrName = crtCssAttrNameAndValue.substring(0, index).trim().toLowerCase();
					if (cssAttrNames[crtCssAttrName]) {
						continue;
					}
					cssAttrNames[crtCssAttrName] = crtCssAttrName;
					outputCssAttrsArray.push(crtCssAttrNameAndValue);

				}

				outputCssAttrsArray.reverse();
				cssAttrsString = outputCssAttrsArray.join(';\n ');

				return cssAttrsString;
			};

			if ( text_styles ) {

				$(_this.targetEle).find('p,span,a').each( function() {

					if ( $(this).attr("class") ) {
						var currentClasses = $(this).attr("class").split(" ");
						for ( var i=0; i < currentClasses.length; i++ ) {
							let crtCssClass = currentClasses[i];
							var styleBase = crtCssClass.split(_this.data.indicator.primary_joiner)[0];

							if ( text_styles[styleBase] && connectedStyleManager_parsed_text_styles.indexOf(crtCssClass) == -1 ) {

								var cssAttrs = new Array();
								cssAttrs.push(text_styles[styleBase].css_attributes);
								
								if ( crtCssClass.indexOf(_this.data.indicator.primary_joiner) != -1 &&
									 text_styles[styleBase].variation_data != null ) {
								
									styleAttr = crtCssClass
													.split(_this.data.indicator.primary_joiner)[1]
													.split(_this.data.indicator.sub_joiner);

									if ( text_styles[styleBase].variation_data.toggle_bold ) {
										if ( styleAttr.indexOf("B") != -1 ) {
											cssAttrs.push("font-weight: bold;\n");
										} else {
											cssAttrs.push("font-weight: normal;\n");
										}
									} else {
										var fixedValue = _this.getStyleAttr(text_styles[styleBase].css_attributes,"font-weight");
										cssAttrs.push("font-weight: " + fixedValue + ";\n");
									}
									
									if ( text_styles[styleBase].variation_data.toggle_italic ) {
										if ( styleAttr.indexOf("I") != -1 ) {
											cssAttrs.push("font-style: italic;\n");
										} else {
											cssAttrs.push("font-style: normal;\n");
										}
									} else {
										let fixedValue = _this.getStyleAttr(text_styles[styleBase].css_attributes,"font-style");
										cssAttrs.push("font-style: " + fixedValue + ";\n");
									}
									
									if ( text_styles[styleBase].variation_data.toggle_underline ) {
										if ( styleAttr.indexOf("U") != -1 ) {
											cssAttrs.push("text-decoration: underline;\n");
										} else {
											cssAttrs.push("text-decoration: none;\n");
										}
									} else {
										let fixedValue = _this.getStyleAttr(text_styles[styleBase].css_attributes,"text-decoration");
										cssAttrs.push("text-decoration: " + fixedValue + ";\n");
									}

									if ( text_styles[styleBase].variation_data.applied_font_family ) {
										var appliedFontFamily = text_styles[styleBase].variation_data.applied_font_family;
										if (text_styles[styleBase].variation_data.applies_font_bold_italic &&
											text_styles[styleBase].variation_data.toggle_italic && styleAttr.indexOf("I") != -1 &&
											text_styles[styleBase].variation_data.toggle_bold && styleAttr.indexOf("B") != -1) {
											cssAttrs.push("font-family: '" + appliedFontFamily + "-BoldItalic" + "';");
										} else if (text_styles[styleBase].variation_data.applies_font_bold &&
											text_styles[styleBase].variation_data.toggle_bold && styleAttr.indexOf("B") != -1) {
											cssAttrs.push("font-family: '" + appliedFontFamily + "-Bold" + "';");
										} else if (text_styles[styleBase].variation_data.applies_font_italic &&
											text_styles[styleBase].variation_data.toggle_italic && styleAttr.indexOf("I") != -1) {
											cssAttrs.push("font-family: '" + appliedFontFamily + "-Italic" + "';");
										}
									}
	
									for ( var j=0; j < styleAttr.length; j++ ) {
										if ( text_styles[styleBase].variation_data.toggle_point_size ) {
											if ( styleAttr[j].indexOf('S') != -1 ) {
												cssAttrs.push("font-size: " + (parseFloat(styleAttr[j].replace("S","").replace("_",".")) * _this.data.indicator.dspScale) + "pt;\n");
												// PARAGRAPH MIN HEIGHT
												cssAttrs.push("min-height: " + (parseFloat(styleAttr[j].replace("S","").replace("_",".")) * _this.data.indicator.dspScale) + "pt;\n");
											}
										}
										if ( text_styles[styleBase].variation_data.toggle_color ) {
											if ( styleAttr[j].indexOf('cmyk') != -1 ) {
												cssAttrs.push("color: " + 
													_this.CMYKtoHEX( _this.replaceAll(styleAttr[j].replace('cmyk','cmyk('),"_",_this.data.indicator.color_joiner) + ")" ) + 
												";\n");
											}
										}
									}
								}

								connectedStyleManager_parsed_text_styles[connectedStyleManager_parsed_text_styles.length] = crtCssClass;

								let cssAttrsString = normalizeCssAttrs(cssAttrs);

								let styleBlockDefinition = ' .' + crtCssClass + ' {\n' + cssAttrsString + '}\n';

								$(connectedStyleManager_css_container).append(`.contentContainer${styleBlockDefinition}`);
								$(connectedStyleManager_css_container).append(styleBlockDefinition);
							};
						};
					};
	
				});
			
			} // END IF text_styles

			var paragraph_styles = _this.paragraph_styles;

			if ( paragraph_styles ) {
			
				$(_this.targetEle).find('[paragraphclass]').each( function() {
	
					var currentStyle = $(this).attr('paragraphclass').replace(_this.data.indicator.paragraph_class_prefix,'');
					var styleBase = currentStyle.split(_this.data.indicator.primary_joiner)[0];
					if ( paragraph_styles[styleBase] && connectedStyleManager_parsed_paragraph_styles.indexOf(currentStyle) == -1 ) {

						var cssAttrs = new Array();
						cssAttrs.push(paragraph_styles[styleBase].css_attributes);
						
						if ( currentStyle.indexOf(_this.data.indicator.primary_joiner) != -1 &&
							 paragraph_styles[styleBase].variation_data != null ) {
						
							styleAttr = currentStyle
											.split(_this.data.indicator.primary_joiner)[1]
											.split(_this.data.indicator.sub_joiner);
	
							for ( var j=0; j < styleAttr.length; j++ ) {
								
								if ( paragraph_styles[styleBase].variation_data.toggle_alignment ) {
									if ( styleAttr[j].indexOf("AL") != -1 )
										cssAttrs.push("text-align: left;");
									else if ( styleAttr[j].indexOf("AC") != -1 )
										cssAttrs.push("text-align: center;");
									else if ( styleAttr[j].indexOf("AR") != -1 )
										cssAttrs.push("text-align: right;");
									else if ( styleAttr[j].indexOf("AJ") != -1 )
										cssAttrs.push("text-align: justify;");
								}
								
								if ( paragraph_styles[styleBase].variation_data.toggle_line_spacing ) {
									if ( styleAttr[j].indexOf("LS") != -1 ) {
										cssAttrs.push("line-height: " + (parseFloat(styleAttr[j].replace("LS","").replace("_",".")) * _this.data.indicator.dspScale) + 
																		(paragraph_styles[styleBase].is_fixed_spacing ? "pt" : "") + ";");
									}
								}
								
								if ( paragraph_styles[styleBase].variation_data.toggle_left_margin ) {
									if ( styleAttr[j].indexOf("M") != -1 ) {
										var appliedMargin = parseFloat(styleAttr[j].replace("M","").replace("_","."));
										appliedMargin = appliedMargin != null ? parseFloat(appliedMargin) : 0;
										cssAttrs.push("margin-left: " + (appliedMargin * _this.data.indicator.dspScale) + "px;");
									}
								}
	
							};
	
						};

						let cssAttrsString = normalizeCssAttrs(cssAttrs);

						connectedStyleManager_parsed_paragraph_styles[connectedStyleManager_parsed_paragraph_styles.length] = currentStyle;
						// Paragraph
						let paragraphClassName = _this.data.indicator.paragraph_class_prefix + currentStyle;
						$(connectedStyleManager_css_container).append("[paragraphclass='" + paragraphClassName + "']:not(li) {" + cssAttrsString + "}");
						$(connectedStyleManager_css_container).append("[paragraphclass='" + paragraphClassName + "']:empty {" + cssAttrsString + "}");
						$(connectedStyleManager_css_container).append("li[paragraphclass='" + paragraphClassName + "'] div.mceListItemContent {" + cssAttrsString + "}");
						
					};
	
				});
			
			} // END IF paragraph_styles

			// Custom list styles
			$(_this.targetEle).find('ul,ol').each( function() {
				_this.updateCustomListStyles(this);
			});

			// Custom paragraph styles
			$(_this.targetEle).find('p').each( function() {
				_this.updateCustomParagraphStyles(this, _this.paragraph_styles);
			});

		}
		
		}; // end component
	};
	
	// instance manager
	connectedStyleManager_component.inst = {};
	
})(jQuery);
