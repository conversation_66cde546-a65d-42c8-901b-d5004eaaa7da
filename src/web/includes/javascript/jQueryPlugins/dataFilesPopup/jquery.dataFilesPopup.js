/** dataFilesPopup r1 // 2012.11.08 // jQuery 1.7.2 // Prinova */



(function($) {
	$.dataFilesPopup = {
		get	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return dataFilesPopup_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			type					: 'default',
			popupLocation			: 'left',
			dataResourceId			: null,
			nTk						: null,
			uTk						: null,
			permitEditing			: true,
			text					: {
										primary_source			: client_messages.text.targeting.primary_source,
										reference_sources		: client_messages.text.targeting.reference_source,
										remote_resource			: client_messages.text.targeting.remote_source
									  }
		}
	};
	
	$.fn.dataFilesPopup = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new dataFilesPopup_component().init(this, conf);
		});
	};

	function dataFilesPopup_component () {
		return {
			
		data : $.extend({},$.dataFilesPopup.defaults),

		init : function(elem, conf) {
			var _this = this;

			dataFilesPopup_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(true, {}, this.data, conf);
			_this.targetEle = $(elem);
			
			if (_this.data.nTk == null || _this.data.uTk == null) {
				alert("Bad token definition");
				return;
			}
			
			// Firefox: Required to object webdav docs
			if ( $.browser.mozilla && $(document).find('#winFirefoxPlugin').length == 0 && _this.data.permitEditing )
				$(document).find('body')
					.append("<object id=\"winFirefoxPlugin\" type=\"application/x-sharepoint\" width=\"0\" height=\"0\" style=\"visibility:hidden;\"></object>");
			
			$('#'+_this.targetEle.attr('id')+'_menuTable .actionMenu').mouseover( function() {
				popupFactoryRemove('dataFilesPopup_menuhover');
			});

			var targetEle = _this.targetEle;

			if ( _this.data.type === 'style_select' ) {
				targetEle = $('#' + targetEle.attr('id') + '_menuTable');
			} else if ( _this.data.type === 'complex-dropdown' ) {
				targetEle = $('#' + targetEle.attr('id') + '-toggle');
			}

			$(targetEle).hoverIntent({
				sensitivity		: 50, // number = sensitivity threshold (must be 1 or higher)
				interval		: 1000,   // number = milliseconds of polling interval
				over			: function(e) { 
					
					if ( $(e.target).closest('.actionMenu').length != 0 )
						return;

					var currentResourceId = _this.data.dataResourceId;
					if ( _this.data.dataResourceId == null)
						currentResourceId = _this.targetEle.val();
					
					var stampDate = new Date();

					if ( currentResourceId > 0 ) {
		        		$(this).popupFactory({
							title					: _this.data.dataResourceId == null ? _this.targetEle.find('option:selected').html() : client_messages.title.data_resource,
							popupLocation			: _this.data.popupLocation,
							trigger					: "instant",
							asyncSetContentURL		: context + "/getObjectInfo.form?type=dataResource&objectId=" + currentResourceId + "&cacheStamp=" + (stampDate.getTime()),
							asyncSetContentHandler	: function(o, data) {
														return _this.processDataResourceDefinition(data);
													  }
							});
					}	
				},  // function = onMouseOver callback (required)
				out				: function() { }
			});
			
		},
		
		processDataResourceDefinition: function(data) {
			var _this = this;

			var popupHTML =	"<div align=\"left\" style=\"padding: 5px 15px 10px 15px; font-size: 11px;\">" +
								"<div style=\"font-weight: bold; padding: 3px 0px;\">" + _this.data.text.primary_source + "</div>" +
								"<div style=\"padding-left: 12px;\">" +
									(_this.data.permitEditing && data.primary_data_file.type == "local" ?
											"<i class=\"sourceEditor fa fa-pencil\" dataFileId=\"" + data.primary_data_file.id + "\" style=\"display: inline-block; margin-right: 8px;\">" + 
												"<input type=\"hidden\" value=\"" + data.primary_data_file.resource + "\">" +
											"</i>" : "") +
									(data.primary_data_file.type == "local" ?
											"<div style=\"display: inline-block; word-wrap: break-word;\"><a href=\"javascript:javascriptHref('" + context + "/download/xml.form?resource=" + data.primary_data_file.resource + "&action=saveFile&cacheStamp=" + stampDate.getTime() + "');\">" + data.primary_data_file.name + "</a></div>" :
											_this.data.text.remote_resource ) +
								"</div>";
							
			if ( data.reference_data_files.length != 0 ) {
				
				popupHTML += 	"<div style=\"font-weight: bold; padding: 8px 0px 3px 0px;\">" + _this.data.text.reference_sources + "</div>";
				
				for ( var i=0; i < data.reference_data_files.length; i++ ) {
					
					popupHTML +="<div style=\"padding-left: 12px;\">" +
									(_this.data.permitEditing && data.reference_data_files[i].type == "local" ?
											"<i class=\"sourceEditor fa fa-pencil\" dataFileId=\"" + data.reference_data_files[i].id + "\" style=\"display: inline-block; margin-right: 8px;\">" + 
												"<input type=\"hidden\" value=\"" + data.reference_data_files[i].resource + "\">" +
											"</i>" : "") +
									(data.reference_data_files[i].type == "local" ?
										"<div style=\"display: inline-block; word-wrap: break-word;\"><a href=\"javascript:javascriptHref('" + context + "/download/xml.form?resource=" + data.reference_data_files[i].resource + "&action=saveFile&cacheStamp=" + stampDate.getTime() + "');\">" + data.reference_data_files[i].name + "</a></div>" :
										_this.data.text.remote_resource ) +
								"</div>";
					
				}
			}					

			popupHTML +=	"</div>";
			
			var popupEle = $(popupHTML);
			$(popupEle).find('.sourceEditor').click( function() {
				popupSourceEditorIframe(_this.targetEle, 1, $(this).find('input').val(), false, null);
			});
			
			return popupEle;
		},
		
		}; // end component
	};
	
	// instance manager
	dataFilesPopup_component.inst = {};
	
})(jQuery);	