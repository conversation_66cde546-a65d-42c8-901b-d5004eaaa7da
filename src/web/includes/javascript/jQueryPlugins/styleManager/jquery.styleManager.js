/** styleManager r1 // 2013.05.30 // jQuery 1.8 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.8 or later
 * 
 */

var styleManager_style_data = {};
var styleManager_text_style_data = {};
var styleManager_paragraph_style_data = {};
var styleManager_list_style_data = {};
var styleManager_parsed_text_styles = [];
var styleManager_parsed_paragraph_styles = [];
var styleManager_css_container = null;
var styleManager_global_init = false;

(function($) {
	
	$.styleManager = {
		get	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return styleManager_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			document					: document,
			indicator 					: {
				primary_joiner 			: "--",
				sub_joiner 				: "-",
				color_joiner 			: ";",
				dspScale				: 1.042,
				paragraph_class_prefix	: 'tinymce_paragraphstyle_'
			},
			apply_global_styles			: false
		}
	};
	
	$.fn.styleManager = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new styleManager_component().init(this, conf);
		});
	};
	
	function styleManager_component () {
		return {
			
		data : $.extend({},$.styleManager.defaults),	

		init : function(elem, conf) {
			var _this = this;

			styleManager_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(false, {}, this.data, conf);
			_this.targetEle = $(elem);

			if ( _this.data.channel != undefined && (parseInt(_this.data.channel) == 3 || parseInt(_this.data.channel) == 4) )
				_this.data.indicator.dspScale = 1.000;

			if ( _this.data.apply_global_styles ) {

				_this.data.document_id = _this.data.document_id != undefined && _this.data.document_id > 0 ? _this.data.document_id : 0;
				if (styleManager_global_init == false) {

					styleManager_global_init = true;

					$.ajax({
						type: "GET",
						url: context + "/asyncStyleManager.form?action=2&documentId=" + _this.data.document_id + "&cacheStamp=" + stampDate.getTime(),
						dataType: "json",
						success: function (data) {
							styleManager_style_data[_this.data.document_id] = data;

							var styleClasses = new Object();
							for ( var i = 0; i < data.text_styles.length; i++ ) {
								styleClasses[data.text_styles[i].title] = {
									css_attributes: data.text_styles[i].css_attributes,
									variation_data: data.text_styles[i].has_variations ?
										data.text_styles[i].variation_data :
										null
								};
							}
							styleManager_text_style_data[_this.data.document_id] = styleClasses;

							var styleClasses = new Object();
							for ( var i = 0; i < data.paragraph_styles.length; i++ ) {
								styleClasses[data.paragraph_styles[i].name] = {
									css_attributes: data.paragraph_styles[i].css_attributes,
									is_fixed_spacing: data.paragraph_styles[i].is_fixed_spacing,
									variation_data: data.paragraph_styles[i].has_variations ?
										data.paragraph_styles[i].variation_data :
										null
								};
							}
							styleManager_paragraph_style_data[_this.data.document_id] = styleClasses;

							var styleClasses = new Object();
							for ( var i = 0; i < data.list_styles.length; i++ ) {
								styleClasses[data.list_styles[i].classes] = {
									applies_symbol_overrides: data.list_styles[i].applies_symbol_overrides,
									style: data.list_styles[i].style
								}
							}
							styleManager_list_style_data[_this.data.document_id] = styleClasses;

							if ( $(_this.data.document).find('head').find('#contentStyles').length == 0 ) {
								var cssContainer = $("<style id=\"contentStyles\"></style>");
								$(_this.data.document).find('head').append(cssContainer);
								styleManager_css_container = cssContainer;
								_this.initStylesheet();
							}
							_this.updateStylesAfterDataLoad();

						}
					});

				} else {

					_this.updateStylesAfterDataLoad();

				}

			} else {

				// TEXT STYLES: Format JSON
				if (_this.data.text_data != undefined && _this.data.text_data != null) {
					for (var i = 0; i < _this.data.text_data.length; i++) {
						if (_.isString(_this.data.text_data[i])) {
							_this.data.text_data[i] = mpParseJSON(_this.data.text_data[i]);
						}

						if (_this.data.text_data[i].has_variations) {
							_this.data.apply_text_style_variations = true;
							if (_this.data.text_data[i].variation_data.toggle_color) {
								if (_.isString(_this.data.text_data[i].variation_data.colors)) {
									_this.data.text_data[i].variation_data.colors = mpParseJSON(_this.data.text_data[i].variation_data.colors);
								}
							}

							if (_this.data.text_data[i].variation_data.toggle_point_size) {
								if (_.isString(_this.data.text_data[i].variation_data.point_sizes)) {
									_this.data.text_data[i].variation_data.point_sizes = mpParseJSON(_this.data.text_data[i].variation_data.point_sizes);
								}
							}
						}
					}

					if (_this.data.apply_text_style_variations) {
						// DATA: Init style css data
						var styleClasses = new Object();
						for (var i = 0; i < _this.data.text_data.length; i++) {
							styleClasses[_this.data.text_data[i].title] = {
								css_attributes: _this.data.text_data[i].css_attributes,
								variation_data: _this.data.text_data[i].has_variations ?
									_this.data.text_data[i].variation_data :
									null
							};
						}
						_this.text_styles = styleClasses;
					}

				}

				// PARAGRAPH STYLES: Format JSON
				if (_this.data.paragraph_data != undefined && _this.data.paragraph_data != null) {
					for (var i = 0; i < _this.data.paragraph_data.length; i++) {
						if (_.isString(_this.data.paragraph_data[i])) {
							_this.data.paragraph_data[i] = mpParseJSON(_this.data.paragraph_data[i]);
						}

						if (_this.data.paragraph_data[i].has_variations) {
							_this.data.apply_paragraph_style_variations = true;
							if (_this.data.paragraph_data[i].variation_data.toggle_line_spacings) {
								if (_.isString(_this.data.paragraph_data[i].variation_data.line_spacings)) {
									_this.data.paragraph_data[i].variation_data.line_spacings = mpParseJSON(_this.data.paragraph_data[i].variation_data.line_spacings);
								}
							}

						}
					}

					if (_this.data.apply_paragraph_style_variations) {
						// DATA: Init style css data
						var styleClasses = new Object();
						for (var i = 0; i < _this.data.paragraph_data.length; i++) {
							styleClasses[_this.data.paragraph_data[i].name] = {
								css_attributes: _this.data.paragraph_data[i].css_attributes,
								is_fixed_spacing: _this.data.paragraph_data[i].is_fixed_spacing,
								variation_data: _this.data.paragraph_data[i].has_variations ?
									_this.data.paragraph_data[i].variation_data :
									null
							};
						}
						_this.paragraph_styles = styleClasses;
					}

				}

				if ( $(_this.data.document).find('head').find('.compoundContentStyles').length == 0 ) {
					var cssContainer = $("<style class=\"compoundContentStyles\"></style>");
					$(_this.data.document).find('head').append(cssContainer);
					styleManager_css_container = cssContainer;
				}

				_this.updateStyleReferenceCSS();

			}

		},

		initStylesheet : function() {

			var _this = this;

			var stylesheet = "";

			var textstyles = styleManager_text_style_data[_this.data.document_id];
			for ( var styleName in textstyles ) {
				stylesheet +=   ".contentContainer span." + styleName + ", .contentContainer div." + styleName + ", .contentContainer p." + styleName + ", " +
								".contentContainer a." + styleName + " { \n" +
									textstyles[styleName].css_attributes + "\n" +
								"} \n";
			}

			var paragraphstyles = styleManager_paragraph_style_data[_this.data.document_id];
			for ( var styleName in paragraphstyles ) {
				stylesheet +=   ".contentContainer span[paragraphclass='tinymce_paragraphstyle_"+styleName+"'], .contentContainer div[paragraphclass='tinymce_paragraphstyle_"+styleName+"'], " +
								".contentContainer p[paragraphclass='tinymce_paragraphstyle_"+styleName+"'], .contentContainer li[paragraphclass='tinymce_paragraphstyle_"+styleName+"'] div.mceListItemContent {\n" +
									paragraphstyles[styleName].css_attributes + "\n" +
								" }\n";
			}

			var liststyles = styleManager_list_style_data[_this.data.document_id];
			for ( var styleName in liststyles ) {
				stylesheet += liststyles[styleName].style;
			}

			$(styleManager_css_container).text( stylesheet ).addClass('init');

		},

		CMYKtoHEX : function(cmykVal) {
			
			var _this = this;

			if ( !cmykVal || cmykVal == null )
				return "#000000";
			
		    function padZero(str) {
		        return "000000".substr(str.length) + str;
		    }
			
			var hexVal = cmykVal.replace("cmyk(","").replace(")","").replaceAll("d",".");
			
			var cmykValues = hexVal.split(_this.data.indicator.color_joiner);
			var C = parseFloat(cmykValues[0])/100;
			var M = parseFloat(cmykValues[1])/100;
			var Y = parseFloat(cmykValues[2])/100;
			var K = parseFloat(cmykValues[3])/100;

		    var cyan = (C * 255 * (1-K)) << 16;
		    var magenta = (M * 255 * (1-K)) << 8;
		    var yellow = (Y * 255 * (1-K)) >> 0;

		    var black = 255 * (1-K);
		    var white = black | black << 8 | black << 16;

		    var color = white - (cyan | magenta | yellow );

		    return ("#"+padZero(color.toString(16)));

		},

		getStyleAttr : function(css, attr) {
			if (css != undefined && css != '') {
				var attrArray = css.split(";");
				for (var i = 0; i < attrArray.length; i++) {
					var currentAttr = attrArray[i].split(":");
					if ($.trim(currentAttr[0]) == attr)
						return $.trim(currentAttr[1]);
				}
			}
		},
		
		replaceAll : function(target, str, sub) {
			while (target.indexOf(str) != -1)
				target = target.replace(str,sub);
			return target;
		},

		updateStylesAfterDataLoad : function() {
			var _this = this;

			if ( $(_this.data.document).find('head').find('#contentStyles').length == 0 || !$(_this.data.document).find('head').find('#contentStyles').is('.init') ) {
				setTimeout(function () {
					_this.updateStylesAfterDataLoad();
				}, 250);
			} else {
				_this.updateStyleReferenceCSS();
			}
		},

		parseListAttributes : function(list) {
			var _this = this;
			function asciiToHex(str) {
				return Array.from(str)
					.map(char => char.charCodeAt(0).toString(16))
					.join('');
			}

			function rgbToCmyk(args) {
				const r = args[0] / 255;
				const g = args[1] / 255;
				const b = args[2] / 255;

				const k = Math.min(1 - r, 1 - g, 1 - b);
				const c = (1 - r - k) / (1 - k) || 0;
				const m = (1 - g - k) / (1 - k) || 0;
				const y = (1 - b - k) / (1 - k) || 0;

				return [c * 100, m * 100, y * 100, k * 100];
			}

			function getAttr(attr, list) {
				if (!list || list.length === 0) return null;

				var ulComputedStyle = window.getComputedStyle(list[0]);
				var liComputedStyle = window.getComputedStyle($(list).find('li')[0]);
				var liBeforeComputedStyle = window.getComputedStyle($(list).find('li')[0], ':before');
				var contentComputedStyle = window.getComputedStyle($(list).find('li .mceListItemContent')[0]);

				if (list.attr(attr)) {
					switch (attr) {
						case 'bullet_spacing':
						case 'list_spacing':
							var spacingValues = list.attr(attr).split(':').map(function (val) {
								return parseFloat(val.replace('in', ''));
							});

							return {
								top: spacingValues[0],
								right: spacingValues[1],
								bottom: spacingValues[2],
								left: spacingValues[3]
							};
						case 'bullet_symbol':
							// Here we have the symbol in hex format
							var symbol = list.attr(attr);
							symbol = symbol.includes('counter') ? null : list.attr(attr);
							return symbol;
						case 'bullet_color':
						case 'bullet_font':
						case 'bullet_size':
						case 'text_align':
							return list.attr(attr);
						case 'line_spacing':
							var lineSpacing = list.attr(attr);
							var value = parseFloat(lineSpacing);
							var type = lineSpacing.slice(-1) === 'm' ? 1 : 2;
							return {
								value: value,
								type: type
							};
					}
				} else {
					switch (attr) {
						case 'bullet_spacing':
							var top = contentComputedStyle.getPropertyValue('padding-top');
							var bottom = contentComputedStyle.getPropertyValue('padding-bottom');
							var right = parseFloat(contentComputedStyle.getPropertyValue('left')) - parseFloat(liBeforeComputedStyle.getPropertyValue('left'));
							var left = liBeforeComputedStyle.getPropertyValue('left');

							return {
								top: parseFloat(top) / 100,
								right: parseFloat(right) / 100,
								bottom: parseFloat(bottom) / 100,
								left: parseFloat(left) / 100
							};
						case 'list_spacing':
							var top = ulComputedStyle.getPropertyValue('padding-top');
							var bottom = ulComputedStyle.getPropertyValue('padding-bottom');
							var right = parseFloat(liComputedStyle.getPropertyValue('width')) - parseFloat(contentComputedStyle.getPropertyValue('width')) - parseFloat(contentComputedStyle.getPropertyValue('left'));
							var left = 0;
							return {
								top: parseFloat(top) / 100,
								right: parseFloat(right) / 100,
								bottom: parseFloat(bottom) / 100,
								left: parseFloat(left) / 100
							};
						case 'bullet_symbol':
							// Here we have the symbol in ascii format
							var symbol = liBeforeComputedStyle.getPropertyValue('content');
							symbol = symbol.includes('counter') || symbol == "none" ? "none" : asciiToHex(symbol.replace(/"/g, ''));
							return symbol;
						case 'bullet_color':
							// Conver color from rgb to hex
							var color = liBeforeComputedStyle.getPropertyValue('color');

							if (color.includes('rgb')) {
								var rgb = color.match(/\d+/g);
								color = rgbToCmyk(rgb).map((num) => Number(num.toFixed(0)));
								;
								color = 'cmyk(' + color.join(';') + ')';
							}

							return color;
						case 'bullet_font':
							return liBeforeComputedStyle.getPropertyValue('font-family');
						case 'bullet_size':
							var fontSize = liBeforeComputedStyle.getPropertyValue('font-size');
							// convert font size to pt
							var fontSizePt = (parseFloat(fontSize) * 0.75).toFixed(2);
							return fontSizePt;
						case 'text_align':
							var textAlign = ulComputedStyle.getPropertyValue('text-align');
							return textAlign;
						case 'line_spacing':
							var lineHeight = liComputedStyle.getPropertyValue('line-height');
							var fontSize = parseFloat(liComputedStyle.getPropertyValue('font-size'));

							var value = parseFloat(lineHeight);
							var type = lineHeight.includes('px') ? 1 : 2;

							if (type === 1) {
								// Convert px to unitless by dividing by font size
								value = value / fontSize;
							}

							return {
								value: value,
								type: type
							};
					}
				}
				return null;
			}

			var listAttributes = {};

			// List Text Alignment
			listAttributes.text_align = getAttr('text_align', list);

			// Bullet Spacing
			listAttributes.bullet_spacing = getAttr('bullet_spacing', list);

			// List Spacing
			listAttributes.list_spacing = getAttr('list_spacing', list);

			// Bullet Symbol Overrides
			var bulletSymbol = getAttr('bullet_symbol', list);
			var bulletColor = getAttr('bullet_color', list);
			var bulletFont = getAttr('bullet_font', list);
			var bulletSize = getAttr('bullet_size', list);

			if (bulletSymbol || bulletColor || bulletFont || bulletSize) {
				listAttributes.bullet = {
					symbol: bulletSymbol,
					color: bulletColor,
					font: bulletFont,
					size: bulletSize
				};
			}

			// Line Spacing
			listAttributes.line_spacing = getAttr('line_spacing', list);

			return listAttributes;
		},

		updateCustomListStyles : function(list) {
			var _this = this;

			function componentToHex(c) {
				var hex = Math.round(c).toString(16);
				return hex.length === 1 ? "0" + hex : hex;
			}

			function cmykToHex(cmykString) {
				// Extract CMYK values from the input string
				var cmykValues = cmykString.slice(5, -1).split(';').map(Number);

				// Destructure the array into individual variables
				var c = cmykValues[0];
				var m = cmykValues[1];
				var y = cmykValues[2];
				var k = cmykValues[3];

				// Ensure values are within valid range (0 to 100)
				c = Math.max(0, Math.min(100, c));
				m = Math.max(0, Math.min(100, m));
				y = Math.max(0, Math.min(100, y));
				k = Math.max(0, Math.min(100, k));

				// Convert CMYK to RGB
				var r = 255 * (1 - c / 100) * (1 - k / 100);
				var g = 255 * (1 - m / 100) * (1 - k / 100);
				var b = 255 * (1 - y / 100) * (1 - k / 100);

				// Convert RGB to HEX
				var hex = "#" + componentToHex(r) + componentToHex(g) + componentToHex(b);
				return hex;
			}

			function parseValue(value) {
				return parseFloat(value) * 100 || 0;
			}

			var $list = $(list);

			var custListAttr = ['text_align', 'bullet_spacing','list_spacing','bullet_symbol','bullet_color','bullet_font','bullet_size','line_spacing'];
			// Determine if current list has at least one custom attribute; otherwise return;
			if ( custListAttr.every(function(attr) { return !$list.attr(attr); }) )
				return;

			var listId = $list.attr('custom_list_id') || Math.random().toString(36).substr(2, 9);
			$list.attr('custom_list_id', listId);

			// Remove existing custom list styles if any
			$('#customListStyles_' + listId).remove();

			var listAttr = _this.parseListAttributes($list);
			var parentClass = $list.closest('.contentContainer').length > 0 ? ".contentContainer " : "";
			var selector = parentClass + $list.prop('tagName').toLowerCase() + '[custom_list_id="' + listId + '"]';

			var textAlign = listAttr.text_align;

			var bulletRightSpacing = parseValue(listAttr.bullet_spacing.right);
			var bulletLeftSpacing = parseValue(listAttr.bullet_spacing.left);
			var bulletTopSpacing = parseValue(listAttr.bullet_spacing.top);
			var bulletBottomSpacing = parseValue(listAttr.bullet_spacing.bottom);

			var listSpacingTop = parseValue(listAttr.list_spacing.top);
			var listSpacingBottom = parseValue(listAttr.list_spacing.bottom);
			var listSpacingRight = parseValue(listAttr.list_spacing.right);

			var lineSpacingValue = listAttr.line_spacing.value;
			var lineSpacingType = listAttr.line_spacing.type;

			var bullet = listAttr.bullet;
			var bulletSymbol = bullet.symbol;
			var bulletColor = bullet.color;
			var bulletFont = bullet.font;
			var bulletSize = bullet.size;

			var listStyle = '';
			if (listSpacingTop > 0)
				listStyle += 'padding-top:' + listSpacingTop + 'px;';
			if (listSpacingBottom > 0)
				listStyle += 'padding-bottom:' + listSpacingBottom + 'px;';
			if (textAlign)
				listStyle += 'text-align:' + textAlign + ';';

			var bulletContentStyle = "";
			bulletContentStyle += 'padding-top:' + bulletTopSpacing + 'px;';
			bulletContentStyle += 'padding-bottom:' + bulletBottomSpacing + 'px;';
			bulletContentStyle += 'left: ' + (bulletLeftSpacing + bulletRightSpacing) + 'px;';
			bulletContentStyle += 'width: calc(100% - ' + (bulletLeftSpacing + bulletRightSpacing + listSpacingRight) + 'px);';
			bulletContentStyle += 'line-height:' + (lineSpacingValue > 0 ? lineSpacingValue : 1) + (lineSpacingType == 2 ? 'pt': '') + ';';

			var bulletBefore = 'left: ' + bulletLeftSpacing + 'px; padding-top:' + bulletTopSpacing + 'px;';

			if (bullet) {
				if (bulletSymbol && bulletSymbol !== 'none') {
					bulletBefore += 'content: "\\'  + bulletSymbol + '";';
				}
				bulletBefore += "color: " + (bulletColor.includes('cmyk') ? cmykToHex(bulletColor) : bulletColor) + ";" +
					"font-size: " + bulletSize + "pt;" +
					"font-family: " + bulletFont + ";"
			}

			var listStyleCSS =
				selector + ' > li:before{' +
				bulletBefore +
				'} \n' +
				selector + ' {' +
				listStyle +
				'} \n' +
				selector + ' > li > div.mceListItemContent {' +
				bulletContentStyle +
				'} \n';

			// Add new custom list styles
			$(_this.data.document).find('head').append("<style id=\"customListStyles_"+listId+"\" type=\"text/css\">" + listStyleCSS + "</style>")

		},

		parseParagraphAttributes : function(paragraph) {
			var _this = this;
			var $paragraph = $(paragraph);
			var paragraphId = $paragraph.attr('custom_paragraph_id');
			var tempStyle = null;

			// Remove existing custom paragraph styles if any
			if (paragraphId) {
				$('#customParagraphStyles_' + paragraphId).remove();
			}

			if ($(paragraph).attr('style')) {
				tempStyle = $(paragraph).attr('style');
				$(paragraph).removeAttr('style');
			}

			function pxToInches(px) {
				var ppi = 100;
				return parseFloat(px) / ppi;
			}

			function getAttr(attr, paragraph) {
				if (!paragraph || paragraph.length === 0) return null;

				var pComputedStyle = window.getComputedStyle(paragraph[0]);
				var paragraphClass = paragraph.attr('paragraphclass') || "";
				var primaryJoiner = "--";
				var paragraphClassPrefix = "tinymce_paragraphstyle_";
				var currentStyle = paragraphClass.replace(paragraphClassPrefix, "");
				var styleBase = currentStyle.split(primaryJoiner)[0];


				if (paragraph.attr(attr)) {
					switch (attr) {
						case 'text_align':
						case 'line_spacing':
							return paragraph.attr(attr);
						case 'spacing':
							// In inches
							var spacingValues = paragraph.attr(attr).split(':').map(function(val) {
								return parseFloat(val.replace('in', ''));
							});
							return {
								top: spacingValues[0],
								right: spacingValues[1],
								bottom: spacingValues[2],
								left: spacingValues[3]
							};
						case 'indent_type':
						case 'special_indent':
							return {
								type: paragraph.attr('indent_type'),
								value: parseFloat(paragraph.attr('special_indent'))
							};
					}
				} else {
					switch (attr) {
						case 'text_align':
							return pComputedStyle.getPropertyValue('text-align');
						case 'spacing':
							var spacingInPx = [
								pComputedStyle.getPropertyValue('padding-top'),
								pComputedStyle.getPropertyValue('margin-right'),
								pComputedStyle.getPropertyValue('padding-bottom'),
								pComputedStyle.getPropertyValue('margin-left')
							];
							// Convert px to inches
							var spacingValues = spacingInPx.map(function(val) {
								var inchValue = pxToInches(val.replace('in', ''));
								return Math.round(inchValue * 100) / 100; // Round to 2 decimal places
							});

							return {
								top: spacingValues[0],
								right: spacingValues[1],
								bottom: spacingValues[2],
								left: spacingValues[3]
							};

						case 'line_spacing':
							var fontSizePx = parseFloat(pComputedStyle.getPropertyValue('font-size'));
							var lineHeightPx = parseFloat(pComputedStyle.getPropertyValue('line-height'));

							var unitlessLineHeight = lineHeightPx / fontSizePx; // Ratio of line-height to font-size

							if (!_this.data.paragraph_data) {
								return unitlessLineHeight.toFixed(2);
							}

							var currentStyleData = _this.data.paragraph_data.find(function(style){
								return style.name == styleBase
							});

							if (currentStyleData && currentStyleData.is_fixed_spacing) { // Check if spacing is in pt
								var pxToPtFactor = 0.74999943307122;
								var displayScale = 100 / 96;
								var lineHeightInPt = parseFloat(lineHeightPx) * (pxToPtFactor / displayScale);
								return lineHeightInPt.toFixed(2) + 'pt';
							}

							return unitlessLineHeight.toFixed(2);
						case 'indent_type':
						case 'special_indent':
							var textIndent = parseFloat(pComputedStyle.getPropertyValue('text-indent')) / 100;
							return textIndent === 0 ? { type: "", value: 0 } : {
								type: textIndent > 0 ? 'first' : 'hanging',
								value: textIndent
							};
					}
				}
				return null;
			}

			var paragraphAttr = {
				text_align: getAttr('text_align', paragraph),
				spacing: getAttr('spacing', paragraph),
				line_spacing: getAttr('line_spacing', paragraph),
				indent_type: getAttr('indent_type', paragraph),
				special: getAttr('special_indent', paragraph)
			};

			if (tempStyle) {
				$(paragraph).attr('style', tempStyle);
			}

			return paragraphAttr;
		},

		updateCustomParagraphStyles: function(paragraph) {
			var _this = this;
			var $paragraph = $(paragraph);
			function inchesToPx(inches) {
				var ppi = 100;
				return parseFloat(inches) * ppi;
			}

			var custParagraphAttr = ['text_align', 'spacing', 'line_spacing', 'indent_type', 'special_indent'];

			// Determine if current paragraph has at least one custom attribute; otherwise return;
			if ( custParagraphAttr.every(function(attr) { return !$paragraph.attr(attr); }) )
				return;

			var paragraphId = $paragraph.attr('custom_paragraph_id') || Math.random().toString(36).substr(2, 9);
			$paragraph.attr('custom_paragraph_id', paragraphId);

			// Remove existing custom paragraph styles if any
			$('#customParagraphStyles_' + paragraphId).remove();

			var paragraphAttr = this.parseParagraphAttributes($paragraph);
			var parentClass = $paragraph.closest('.contentContainer').length > 0 ? ".contentContainer " : "";
			var selector = parentClass + $paragraph.prop('tagName').toLowerCase() + '[custom_paragraph_id="' + paragraphId + '"]';

			var textAlign = paragraphAttr.text_align;

			var spacingLeft = inchesToPx(paragraphAttr.spacing.left);
			var spacingRight = inchesToPx(paragraphAttr.spacing.right);

			var spacingBottom = inchesToPx(paragraphAttr.spacing.bottom);
			var spacingTop = inchesToPx(paragraphAttr.spacing.top);

			var lineSpacing = paragraphAttr.line_spacing;

			var specialIndent = inchesToPx(paragraphAttr.special.value);

			var paragraphStyle = '';
			if (textAlign)
				paragraphStyle += 'text-align:' + textAlign + ';';

			var marginLeft = spacingLeft - (specialIndent < 0 ? specialIndent : 0);
			paragraphStyle += 'margin-left: ' + marginLeft + 'px;';
			paragraphStyle += 'margin-right: ' + spacingRight + 'px;';
			paragraphStyle += 'padding-bottom: ' + spacingBottom + 'px;';
			paragraphStyle += 'padding-top: ' + spacingTop + 'px;';

			paragraphStyle += 'line-height: ' + lineSpacing + ';';

			paragraphStyle += 'text-indent: ' + specialIndent + 'px;';

			var paragraphStyleCSS =
				selector + ' {' +
				paragraphStyle +
				'} \n';

			// Add new custom paragraph styles
			$(_this.data.document).find('head').append("<style id=\"customParagraphStyles_"+paragraphId+"\" type=\"text/css\">" + paragraphStyleCSS + "</style>");
		},

		updateStyleReferenceCSS : function() {

			var _this = this;

			var text_styles = _this.data.apply_global_styles ? styleManager_text_style_data[_this.data.document_id] : _this.text_styles;

			if ( text_styles ) {

				$(_this.targetEle).find('span,a').each( function() {

					if ( $(this).attr("class") ) {
						var currentClasses = $(this).attr("class").split(" ");
						for ( var i=0; i < currentClasses.length; i++ ) {
							var styleBase = currentClasses[i].split(_this.data.indicator.primary_joiner)[0];

							if ( text_styles[styleBase] && styleManager_parsed_text_styles.indexOf(currentClasses[i]) == -1 ) {

								var cssAttrs = new Array();
								cssAttrs.push(text_styles[styleBase].css_attributes);
								
								if ( currentClasses[i].indexOf(_this.data.indicator.primary_joiner) != -1 && 
									 text_styles[styleBase].variation_data != null ) {
								
									styleAttr = currentClasses[i]
													.split(_this.data.indicator.primary_joiner)[1]
													.split(_this.data.indicator.sub_joiner);

									if ( text_styles[styleBase].variation_data.toggle_bold ) {
										if ( styleAttr.indexOf("B") != -1 )
											cssAttrs.push("font-weight: bold;\n");
										else
											cssAttrs.push("font-weight: normal;\n");
									} else {
										var fixedValue = _this.getStyleAttr(text_styles[styleBase].css_attributes,"font-weight");
										cssAttrs.push("font-weight: " + fixedValue + ";\n");
									}
									
									if ( text_styles[styleBase].variation_data.toggle_italic ) {
										if ( styleAttr.indexOf("I") != -1 )
											cssAttrs.push("font-style: italic;\n");
										else
											cssAttrs.push("font-style: normal;\n");
									} else {
										var fixedValue = _this.getStyleAttr(text_styles[styleBase].css_attributes,"font-style");
										cssAttrs.push("font-style: " + fixedValue + ";\n");
									}
									
									if ( text_styles[styleBase].variation_data.toggle_underline ) {
										if ( styleAttr.indexOf("U") != -1 )
											cssAttrs.push("text-decoration: underline;\n");
										else
											cssAttrs.push("text-decoration: none;\n");
									} else {
										var fixedValue = _this.getStyleAttr(text_styles[styleBase].css_attributes,"text-decoration");
										cssAttrs.push("text-decoration: " + fixedValue + ";\n");
									}

									if ( text_styles[styleBase].variation_data.applied_font_family ) {
										var appliedFontFamily = text_styles[styleBase].variation_data.applied_font_family;
										if (text_styles[styleBase].variation_data.applies_font_bold_italic &&
											text_styles[styleBase].variation_data.toggle_italic && styleAttr.indexOf("I") != -1 &&
											text_styles[styleBase].variation_data.toggle_bold && styleAttr.indexOf("B") != -1) {
											cssAttrs.push("font-family: '" + appliedFontFamily + "-BoldItalic" + "';");
										} else if (text_styles[styleBase].variation_data.applies_font_bold &&
											text_styles[styleBase].variation_data.toggle_bold && styleAttr.indexOf("B") != -1) {
											cssAttrs.push("font-family: '" + appliedFontFamily + "-Bold" + "';");
										} else if (text_styles[styleBase].variation_data.applies_font_italic &&
											text_styles[styleBase].variation_data.toggle_italic && styleAttr.indexOf("I") != -1) {
											cssAttrs.push("font-family: '" + appliedFontFamily + "-Italic" + "';");
										}
									}
	
									for ( var j=0; j < styleAttr.length; j++ ) {
										if ( text_styles[styleBase].variation_data.toggle_point_size ) {
											if ( styleAttr[j].indexOf('S') != -1 ) {
												cssAttrs.push("font-size: " + (parseFloat(styleAttr[j].replace("S","").replace("_",".")) * _this.data.indicator.dspScale) + "pt;\n");
												// PARAGRAPH MIN HEIGHT
												cssAttrs.push("min-height: " + (parseFloat(styleAttr[j].replace("S","").replace("_",".")) * _this.data.indicator.dspScale) + "pt;\n");
											}
										}
										if ( text_styles[styleBase].variation_data.toggle_color ) {
											if ( styleAttr[j].indexOf('cmyk') != -1 ) {
												cssAttrs.push("color: " + 
													_this.CMYKtoHEX( _this.replaceAll(styleAttr[j].replace('cmyk','cmyk('),"_",_this.data.indicator.color_joiner) + ")" ) + 
												";\n");
											}
										}
									};
	
								};

								styleManager_parsed_text_styles[styleManager_parsed_text_styles.length] = currentClasses[i];

								$(styleManager_css_container).append('.contentContainer .' + currentClasses[i] + ' {\n' + cssAttrs.join(' ') + '}\n');
								$(styleManager_css_container).append(' .' + currentClasses[i] + ' {\n' + cssAttrs.join(' ') + '}\n');
							};
						};
					};
	
				});
			
			} // END IF text_styles

			var paragraph_styles = _this.data.apply_global_styles ? styleManager_paragraph_style_data[_this.data.document_id] : _this.paragraph_styles;

			if ( paragraph_styles ) {
			
				$(_this.targetEle).find('[paragraphclass]').each( function() {
	
					var currentStyle = $(this).attr('paragraphclass').replace(_this.data.indicator.paragraph_class_prefix,'');
					var styleBase = currentStyle.split(_this.data.indicator.primary_joiner)[0];
					if ( paragraph_styles[styleBase] && styleManager_parsed_paragraph_styles.indexOf(currentStyle) == -1 ) {

						var cssAttrs = new Array();
						cssAttrs.push(paragraph_styles[styleBase].css_attributes);
						
						if ( currentStyle.indexOf(_this.data.indicator.primary_joiner) != -1 &&
							 paragraph_styles[styleBase].variation_data != null ) {
						
							styleAttr = currentStyle
											.split(_this.data.indicator.primary_joiner)[1]
											.split(_this.data.indicator.sub_joiner);
	
							for ( var j=0; j < styleAttr.length; j++ ) {
								
								if ( paragraph_styles[styleBase].variation_data.toggle_alignment ) {
									if ( styleAttr[j].indexOf("AL") != -1 )
										cssAttrs.push("text-align: left;");
									else if ( styleAttr[j].indexOf("AC") != -1 )
										cssAttrs.push("text-align: center;");
									else if ( styleAttr[j].indexOf("AR") != -1 )
										cssAttrs.push("text-align: right;");
									else if ( styleAttr[j].indexOf("AJ") != -1 )
										cssAttrs.push("text-align: justify;");
								}
								
								if ( paragraph_styles[styleBase].variation_data.toggle_line_spacing ) {
									if ( styleAttr[j].indexOf("LS") != -1 ) {
										cssAttrs.push("line-height: " + (parseFloat(styleAttr[j].replace("LS","").replace("_",".")) * _this.data.indicator.dspScale) + 
																		(paragraph_styles[styleBase].is_fixed_spacing ? "pt" : "") + ";");
									}
								}
								
								if ( paragraph_styles[styleBase].variation_data.toggle_left_margin ) {
									if ( styleAttr[j].indexOf("M") != -1 ) {
										var appliedMargin = parseFloat(styleAttr[j].replace("M","").replace("_","."));
										appliedMargin = appliedMargin != null ? parseFloat(appliedMargin) : 0;
										cssAttrs.push("margin-left: " + (appliedMargin * _this.data.indicator.dspScale) + "px");
									}
								}
	
							};
	
						};

						styleManager_parsed_paragraph_styles[styleManager_parsed_paragraph_styles.length] = currentStyle;
						// Paragraph
						$(styleManager_css_container).append(".contentContainer [paragraphclass='" + _this.data.indicator.paragraph_class_prefix + currentStyle + "']:not(li) {" + cssAttrs.join(' ') + "}");
						$(styleManager_css_container).append(".contentContainer [paragraphclass='" + _this.data.indicator.paragraph_class_prefix + currentStyle + "']:empty {" + cssAttrs.join(' ') + "}");
						$(styleManager_css_container).append(".contentContainer li[paragraphclass='" + _this.data.indicator.paragraph_class_prefix + currentStyle + "'] div.mceListItemContent {" + cssAttrs.join(' ') + "}");
						
					};
	
				});
			
			} // END IF paragraph_styles

			// Custom list styles
			$(_this.targetEle).find('ul,ol').each( function() {
				_this.updateCustomListStyles(this);
			});

			// Custom paragraph styles
			$(_this.targetEle).find('p').each( function() {
				_this.updateCustomParagraphStyles(this);
			});

		}
		
		}; // end component
	};
	
	// instance manager
	styleManager_component.inst = {};
	
})(jQuery);
