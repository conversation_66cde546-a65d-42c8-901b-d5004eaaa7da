(function ($, _) {
    var JsonDataSourceHandler = /** @class */ (function () {
        function JsonDataSourceHandler() {
            this.dataElementVisible = {};
            this.template = Handlebars.compile($('#nodeDisplayDataJsonTemplate').html());
            this.definitionTemplate = Handlebars.compile($('#nodeDisplayDefinitionView').html());
            this.keyTemplate = Handlebars.compile($('#nodeDisplayKeyView').html());
            this.elementTemplate = Handlebars.compile($('#nodeDisplayElementView').html());
            this.variableTemplate = Handlebars.compile($('#nodeDisplayVariableView').html());
        }
        JsonDataSourceHandler.prototype.applyFilter = function () {
        };
        JsonDataSourceHandler.prototype.setAnonymized = function (showAnonymized) {
            this.settings.state.filterOptions.anonymized = showAnonymized;
        };
        JsonDataSourceHandler.prototype.setSortBy = function (sortBy) {
            this.settings.state.filterOptions.sortBy = sortBy;
        };
        JsonDataSourceHandler.prototype.renderNodeHtml = function (node) {
            var data = $.extend({}, this.settings.strings, node);
            return this.template(data);
        };
        JsonDataSourceHandler.prototype.attachEvents = function (node, element, openSelected) {
            var _this = this;
            var nodeDisplay = $('#nodeDisplayDataSummary_' + node.id);
            var nodeType = 'key';
            if(nodeDisplay.find('i').hasClass('is-object')){
                nodeType = 'object';
            }else if(nodeDisplay.find('i').hasClass('is-array')){
                nodeType = 'array';
            }
            nodeDisplay.on('mouseover', function () {
                _this.settings.state.activeTreeElementId = 'nodeDisplayDataSummary_' + node.id;
                _this.activeJsonDefId = node.id;
                if($(this).find('i').hasClass('is-object')){
                    _this.settings.state.nodeType = 'object';
                }else if(nodeDisplay.find('i').hasClass('is-array')){
                    _this.settings.state.nodeType = 'array';
                }else{
                    _this.settings.state.nodeType = 'key';
                }

                // Highlight the all children of the node
                $('#'+node.id+'_anchor').parent().find('.jstree-wholerow').addClass('jstree-wholerow-hovered');
            });
            nodeDisplay.on('mouseout', function () {
                // Clear all the highlighted nodes
                $('.jstree-wholerow-hovered').removeClass('jstree-wholerow-hovered');
            });
            nodeDisplay.on('click', function () {
                var editArgs = _this.getContextMenuItem(_this.settings.strings.page.label._edit);
                _this.common.triggerPopup(editArgs);
            });
            this.attachHoverPopup(node, nodeType);
            this.initElementDetails(node);
            if (this.dataElementVisible[node.id] || (openSelected && node.extras.selected)) {
                this.renderDataElement(node.id, openSelected);
            }
        };
        JsonDataSourceHandler.prototype.getContentMenuOptions = function () {
            var strings = this.settings.strings;
            var forObject = this.settings.state.nodeType === 'object';
            var forArray = this.settings.state.nodeType === 'array';
            var forKey = this.settings.state.nodeType === 'key';
            var forElement = this.settings.state.activeTreeElementId.startsWith('nodeDisplayElement');

            if (forObject) {
                return [strings.page.label._edit,
                    strings.page.label.delete,
                    strings.page.label.add.definition,
                    strings.page.label.add.key];
            } else if (forArray) {
                return [strings.page.label._edit,
                    strings.page.label.delete,
                    strings.page.label.add.definition,
                    strings.page.label.add.key,
                    strings.page.label.add.element];
            } else if(forKey){
                return [strings.page.label._edit,
                    strings.page.label.delete,
                    strings.page.label.add.definition,
                    strings.page.label.add.element];
            } else {
                return [strings.page.label._edit,
                    strings.page.label.delete];
            }
        };
        JsonDataSourceHandler.prototype.getContextMenuItem = function (action) {
            var forObject = this.settings.state.nodeType === 'object';
            var forArray = this.settings.state.nodeType === 'array';
            var forKey = this.settings.state.nodeType === 'key';
            var forElement = this.settings.state.activeTreeElementId.startsWith('nodeDisplayElement');
            var menuArgs;
            if (forElement) {
                menuArgs = this.getElementContextMenuItem(action);
            } else {
                menuArgs = this.getJsonDefContextMenuItem(action, forKey);
            }
            return menuArgs;
        };
        JsonDataSourceHandler.prototype.setActiveNode = function (value) {
            this.activeJsonDefId = value.id;
        };
        JsonDataSourceHandler.prototype.setCommon = function (value) {
            if (value != null) {
                this.common = value;
            }
        };
        JsonDataSourceHandler.prototype.setSettings = function (value) {
            if (value != null) {
                this.settings = value;
            }
        };
        JsonDataSourceHandler.prototype.getElementContextMenuItem = function (action) {
            var _this = this;
            var onSaveCallback = _.wrap(customCallback, function (callback, iframe) {
                callback(iframe);
                _this.common.refresh();
            });
            if (action === this.settings.strings.page.label._edit) {
                return {
                    appliedParams: {
                        deid: this.activeAttribute,
                        dsid: this.settings.dataSourceId,
                        jddid: this.activeJsonDefId,
                        cacheStamp: new Date().getTime()
                    },
                    onSave: onSaveCallback,
                    src: 'json_data_element_edit.form',
                    title: client_messages.title.edit_data_element,
                    width: 800
                };
            }
            else if (action === this.settings.strings.page.label.delete) {
                return {
                    appliedParams: {
                        cancelView: 'datasources.form',
                        deid: this.activeAttribute,
                        dsid: this.settings.dataSourceId,
                        parameter: 'deid',
                        successParameter: 'jddid',
                        jddid: this.activeJsonDefId,
                        cacheStamp: new Date().getTime()
                    },
                    onSave: onSaveCallback,
                    src: 'json_data_element_delete.form',
                    title: client_messages.title.delete_data_element,
                    width: 800,
                };
            }
            else {
                throw 'invalid context menu action: ' + action;
            }
        };
        JsonDataSourceHandler.prototype.getJsonDefContextMenuItem = function (action, forKey) {
            var _this = this;
            var onSaveCallback = _.wrap(customCallback, function (callback, iframe) {
                callback(iframe);
                _this.common.refresh();
            });
            switch (action) {
                case this.settings.strings.page.label._edit:
                    return {
                        appliedParams: {
                            action: forKey ? 'updatejsondatakey' : 'updatejsondatadef',
                            dsid: this.settings.dataSourceId,
                            jddid: this.activeJsonDefId,
                        },
                        onSave: onSaveCallback,
                        src: forKey ? 'json_data_key_edit.form' : 'json_data_definition_edit.form',
                        title: forKey ? client_messages.title.edit_data_key : client_messages.title.edit_data_definition,
                        width: 800,
                    };
                case this.settings.strings.page.label.delete:
                    return {
                        appliedParams: {
                            cancelView: 'datasources.form',
                            dsid: this.settings.dataSourceId,
                            parameter: 'jddid',
                            successParameter: 'dsid',
                            jddid: this.activeJsonDefId,
                        },
                        onSave: onSaveCallback,
                        src: 'json_data_definition_delete.form',
                        title: forKey ? client_messages.title.delete_data_key : client_messages.title.delete_data_definition,
                        width: 800,
                    };
                case this.settings.strings.page.label.add.definition:
                    return {
                        appliedParams: {
                            action: 'insertjsondatadef',
                            dsid: this.settings.dataSourceId,
                            'new': true,
                            parentid: this.activeJsonDefId,
                        },
                        onSave: onSaveCallback,
                        src: 'json_data_definition_edit.form',
                        title: client_messages.title.insert_data_definition,
                        width: 800,
                    };
                case this.settings.strings.page.label.add.key:
                    return {
                        appliedParams: {
                            action: 'insertjsondatakey',
                            dsid: this.settings.dataSourceId,
                            isAttributeDataElement: true,
                            'new': true,
                            parentid: this.activeJsonDefId,
                            cacheStamp: new Date().getTime()
                        },
                        onSave: onSaveCallback,
                        src: 'json_data_key_edit.form',
                        title: client_messages.title.insert_data_key,
                        width: 800,
                    };
                case this.settings.strings.page.label.add.element:
                    return {
                        appliedParams: {
                            action: 'updatejsondataelement',
                            dsid: this.settings.dataSourceId,
                            isAttributeDataElement: false,
                            'new': true,
                            jddid: this.activeJsonDefId,
                            cacheStamp: new Date().getTime()
                        },
                        onSave: onSaveCallback,
                        src: 'json_data_element_edit.form',
                        title: client_messages.title.insert_data_element,
                        width: 800,
                    };
            }
            throw 'invalid context menu action: ' + action;
        };
        JsonDataSourceHandler.prototype.initElementDetails = function (node) {
            var _this = this;
            $('#elementExpandIcon_' + node.id).on('click', function () {
                _this.common.closePopups();
                var detailView = $('#nodeElementDetailView_' + node.id);
                _this.common.saveScrollPosition();
                if (detailView.length > 0) {
                    $('#elementExpandIcon_' + node.id).removeClass('fa-minus-square fa-spinner fa-spin').addClass('fa-plus-square');
                    _this.dataElementVisible[node.id] = false;
                    detailView.remove();
                }
                else {
                    _this.renderDataElement(node.id);
                }
            });
        };
        JsonDataSourceHandler.prototype.renderDataElement = function (nodeId, openSelected) {
            var _this = this;
            $('#elementExpandIcon_' + nodeId).removeClass('fa-plus-square').addClass('fa-minus-square');
            this.dataElementVisible[nodeId] = true;
            var newDetailView = document.createElement('div');
            newDetailView.id = 'nodeElementDetailView_' + nodeId;
            newDetailView.style.marginLeft = '1.5rem';
            newDetailView.style.height = '50px';
            $('#elementExpandIconContainer_' + nodeId).append(newDetailView);
            return this.asyncGetDataElementValue(nodeId).done(function (data) {
                _.each(data, function (value) {
                    if (_.isArray(data) && data.length === 0) {
                        $('#nodeElementDetailView_' + nodeId).remove();
                        delete _this.dataElementVisible[nodeId];
                        return;
                    }
                    var attrData = {
                        isSelected: openSelected && value.id === _this.settings.selectedNodeId
                    };
                    var rendered = $(_this.elementTemplate($.extend(attrData, _this.settings.strings, value)));
                    rendered.on('mouseover', function () {
                        _this.settings.state.activeTreeElementId = 'nodeDisplayElement_' + value.id;
                        _this.activeAttribute = value.id;
                        _this.activeJsonDefId = nodeId;
                    });
                    rendered.on('click', function () {
                        var item = _this.getContextMenuItem(_this.settings.strings.page.label._edit);

                        _this.common.triggerPopup(item);
                    });
                    $(newDetailView).append(rendered);
                    _this.common.restoreScrollPosition();
                });
                $('#elementExpandIcon_' + nodeId).removeClass('fa-spinner fa-spin').addClass('fa-minus-square');
            });
        };
        JsonDataSourceHandler.prototype.attachHoverPopup = function (node, nodeType) {
            var contentUrl = this.settings.context + 'dataSourceTree.form?&method=jsondefsummary&jddid=' + node.id;
            if(nodeType === 'object' || nodeType === 'array'){
                this.common.addNodeHoverPopup(node, contentUrl, this.definitionTemplate, this.variableTemplate);
            }else{
                this.common.addNodeHoverPopup(node, contentUrl, this.keyTemplate, this.variableTemplate);
            }
        };
        JsonDataSourceHandler.prototype.asyncGetDataElementValue = function (nodeId) {
            return $.post(this.settings.context + 'dataSourceTree.form', {
                dsid: this.settings.dataSourceId,
                method: 'jsondataelements',
                jddid: nodeId
            });
        };
        return JsonDataSourceHandler;
    }());
    return new JsonDataSourceHandler();
})(jQuery, _);
