<!doctype html>

<title>CodeMirror: TTCN-CFG mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="ttcn-cfg.js"></script>
<style type="text/css">
    .CodeMirror {
        border-top: 1px solid black;
        border-bottom: 1px solid black;
    }
</style>
<div id=nav>
    <a href="http://codemirror.net"><h1>CodeMirror</h1>
        <img id=logo src="../../doc/logo.png">
    </a>

    <ul>
        <li><a href="../../index.html">Home</a>
        <li><a href="../../doc/manual.html">Manual</a>
        <li><a href="https://github.com/codemirror/codemirror">Code</a>
    </ul>
    <ul>
        <li><a href="../index.html">Language modes</a>
        <li><a class=active href="http://en.wikipedia.org/wiki/TTCN">TTCN-CFG</a>
    </ul>
</div>
<article>
    <h2>TTCN-CFG example</h2>
    <div>
        <textarea id="ttcn-cfg-code">
[MODULE_PARAMETERS]
# This section shall contain the values of all parameters that are defined in your TTCN-3 modules.

[LOGGING]
# In this section you can specify the name of the log file and the classes of events
# you want to log into the file or display on console (standard error).

LogFile := "logs/%e.%h-%r.%s"
FileMask := LOG_ALL | DEBUG | MATCHING
ConsoleMask := ERROR | WARNING | TESTCASE | STATISTICS | PORTEVENT

LogSourceInfo := Yes
AppendFile := No
TimeStampFormat := DateTime
LogEventTypes := Yes
SourceInfoFormat := Single
LogEntityName := Yes

[TESTPORT_PARAMETERS]
# In this section you can specify parameters that are passed to Test Ports.

[DEFINE]
# In this section you can create macro definitions,
# that can be used in other configuration file sections except [INCLUDE].

[INCLUDE]
# To use configuration settings given in other configuration files,
# the configuration files just need to be listed in this section, with their full or relative pathnames.

[EXTERNAL_COMMANDS]
# This section can define external commands (shell scripts) to be executed by the ETS
# whenever a control part or test case is started or terminated.

BeginTestCase := ""
EndTestCase := ""
BeginControlPart := ""
EndControlPart := ""

[EXECUTE]
# In this section you can specify what parts of your test suite you want to execute.

[GROUPS]
# In this section you can specify groups of hosts. These groups can be used inside the
# [COMPONENTS] section to restrict the creation of certain PTCs to a given set of hosts.

[COMPONENTS]
# This section consists of rules restricting the location of created PTCs.

[MAIN_CONTROLLER]
# The options herein control the behavior of MC.

TCPPort := 0
KillTimer := 10.0
NumHCs := 0
LocalAddress :=
        </textarea>
    </div>

    <script> 
      var ttcnEditor = CodeMirror.fromTextArea(document.getElementById("ttcn-cfg-code"), {
        lineNumbers: true,
        matchBrackets: true,
        mode: "text/x-ttcn-cfg"
      });
      ttcnEditor.setSize(600, 860);
      var mac = CodeMirror.keyMap.default == CodeMirror.keyMap.macDefault;
      CodeMirror.keyMap.default[(mac ? "Cmd" : "Ctrl") + "-Space"] = "autocomplete";
    </script>
    <br/>
    <p><strong>Language:</strong> Testing and Test Control Notation -
        Configuration files
        (<a href="http://en.wikipedia.org/wiki/TTCN">TTCN-CFG</a>)
    </p>
    <p><strong>MIME types defined:</strong> <code>text/x-ttcn-cfg</code>.</p>

    <br/>
    <p>The development of this mode has been sponsored by <a href="http://www.ericsson.com/">Ericsson
    </a>.</p>
    <p>Coded by Asmelash Tsegay Gebretsadkan </p>
</article>

