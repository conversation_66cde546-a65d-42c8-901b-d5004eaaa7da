<!doctype html>

<title>CodeMirror: User Manual</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="docs.css">
<script src="activebookmark.js"></script>

<script src="../lib/codemirror.js"></script>
<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../addon/runmode/runmode.js"></script>
<script src="../addon/runmode/colorize.js"></script>
<script src="../mode/javascript/javascript.js"></script>
<script src="../mode/xml/xml.js"></script>
<script src="../mode/css/css.js"></script>
<script src="../mode/htmlmixed/htmlmixed.js"></script>
<style>
  dt { text-indent: -2em; padding-left: 2em; margin-top: 1em; }
  dd { margin-left: 1.5em; margin-bottom: 1em; }
  dt {margin-top: 1em;}
  dd dl, dd dt, dd dd, dd ul { margin-top: 0; margin-bottom: 0; }
  dt + dt { margin-top: 0; }
  dt.command { position: relative; }
  span.keybinding { position: absolute; right: 0; font-size: 80%; color: #555; text-indent: 0; }
</style>

<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="logo.png"></a>
  <ul>
    <li><a href="../index.html">Home</a></li>
    <li><a href="#overview" class=active data-default="true">Manual</a></li>
    <li><a href="https://github.com/codemirror/codemirror">Code</a></li>
  </ul>
  <ul>
    <li><a href="#usage">Basic Usage</a></li>
    <li><a href="#config">Configuration</a></li>
    <li><a href="#events">Events</a></li>
    <li><a href="#keymaps">Key maps</a></li>
    <li><a href="#commands">Commands</a></li>
    <li><a href="#styling">Customized Styling</a></li>
    <li><a href="#api">Programming API</a>
      <ul>
        <li><a href="#api_constructor">Constructor</a></li>
        <li><a href="#api_content">Content manipulation</a></li>
        <li><a href="#api_selection">Selection</a></li>
        <li><a href="#api_configuration">Configuration</a></li>
        <li><a href="#api_doc">Document management</a></li>
        <li><a href="#api_history">History</a></li>
        <li><a href="#api_marker">Text-marking</a></li>
        <li><a href="#api_decoration">Widget, gutter, and decoration</a></li>
        <li><a href="#api_sizing">Sizing, scrolling, and positioning</a></li>
        <li><a href="#api_mode">Mode, state, and tokens</a></li>
        <li><a href="#api_misc">Miscellaneous methods</a></li>
        <li><a href="#api_static">Static properties</a></li>
      </ul>
    </li>
    <li><a href="#addons">Addons</a></li>
    <li><a href="#modeapi">Writing CodeMirror Modes</a></li>
    <li><a href="#vimapi">Vim Mode API</a>
      <ul>
        <li><a href="#vimapi_configuration">Configuration</a></li>
        <li><a href="#vimapi_extending">Extending VIM</a></li>
      </ul>
    </li>
  </ul>
</div>

<article>

<section class=first id=overview>
    <h2 style="position: relative">
      User manual and reference guide
      <span style="color: #888; font-size: 1rem; position: absolute; right: 0; bottom: 0">version 5.10.1</span>
    </h2>

    <p>CodeMirror is a code-editor component that can be embedded in
    Web pages. The core library provides <em>only</em> the editor
    component, no accompanying buttons, auto-completion, or other IDE
    functionality. It does provide a rich API on top of which such
    functionality can be straightforwardly implemented. See
    the <a href="#addons">addons</a> included in the distribution,
    and the <a href="https://github.com/codemirror/CodeMirror/wiki/CodeMirror-addons">list
    of externally hosted addons</a>, for reusable
    implementations of extra features.</p>

    <p>CodeMirror works with language-specific modes. Modes are
    JavaScript programs that help color (and optionally indent) text
    written in a given language. The distribution comes with a number
    of modes (see the <a href="../mode/"><code>mode/</code></a>
    directory), and it isn't hard to <a href="#modeapi">write new
    ones</a> for other languages.</p>
</section>

<section id=usage>
    <h2>Basic Usage</h2>

    <p>The easiest way to use CodeMirror is to simply load the script
    and style sheet found under <code>lib/</code> in the distribution,
    plus a mode script from one of the <code>mode/</code> directories.
    (See <a href="compress.html">the compression helper</a> for an
    easy way to combine scripts.) For example:</p>

    <pre data-lang="text/html">&lt;script src="lib/codemirror.js">&lt;/script>
&lt;link rel="stylesheet" href="lib/codemirror.css">
&lt;script src="mode/javascript/javascript.js">&lt;/script></pre>

    <p>(Alternatively, use a module loader. <a href="#modloader">More
    about that later.</a>)</p>

    <p>Having done this, an editor instance can be created like
    this:</p>

    <pre data-lang="javascript">var myCodeMirror = CodeMirror(document.body);</pre>

    <p>The editor will be appended to the document body, will start
    empty, and will use the mode that we loaded. To have more control
    over the new editor, a configuration object can be passed
    to <a href="#CodeMirror"><code>CodeMirror</code></a> as a second
    argument:</p>

    <pre data-lang="javascript">var myCodeMirror = CodeMirror(document.body, {
  value: "function myScript(){return 100;}\n",
  mode:  "javascript"
});</pre>

    <p>This will initialize the editor with a piece of code already in
    it, and explicitly tell it to use the JavaScript mode (which is
    useful when multiple modes are loaded).
    See <a href="#config">below</a> for a full discussion of the
    configuration options that CodeMirror accepts.</p>

    <p>In cases where you don't want to append the editor to an
    element, and need more control over the way it is inserted, the
    first argument to the <code>CodeMirror</code> function can also
    be a function that, when given a DOM element, inserts it into the
    document somewhere. This could be used to, for example, replace a
    textarea with a real editor:</p>

    <pre data-lang="javascript">var myCodeMirror = CodeMirror(function(elt) {
  myTextArea.parentNode.replaceChild(elt, myTextArea);
}, {value: myTextArea.value});</pre>

    <p>However, for this use case, which is a common way to use
    CodeMirror, the library provides a much more powerful
    shortcut:</p>

    <pre data-lang="javascript">var myCodeMirror = CodeMirror.fromTextArea(myTextArea);</pre>

    <p>This will, among other things, ensure that the textarea's value
    is updated with the editor's contents when the form (if it is part
    of a form) is submitted. See the <a href="#fromTextArea">API
    reference</a> for a full description of this method.</p>

    <h3 id=modloader>Module loaders</h3>

    <p>The files in the CodeMirror distribution contain shims for
    loading them (and their dependencies) in AMD or CommonJS
    environments. If the variables <code>exports</code>
    and <code>module</code> exist and have type object, CommonJS-style
    require will be used. If not, but there is a
    function <code>define</code> with an <code>amd</code> property
    present, AMD-style (RequireJS) will be used.</p>

    <p>It is possible to
    use <a href="http://browserify.org/">Browserify</a> or similar
    tools to statically build modules using CodeMirror. Alternatively,
    use <a href="http://requirejs.org/">RequireJS</a> to dynamically
    load dependencies at runtime. Both of these approaches have the
    advantage that they don't use the global namespace and can, thus,
    do things like load multiple versions of CodeMirror alongside each
    other.</p>

    <p>Here's a simple example of using RequireJS to load CodeMirror:</p>

    <pre data-lang="javascript">require([
  "cm/lib/codemirror", "cm/mode/htmlmixed/htmlmixed"
], function(CodeMirror) {
  CodeMirror.fromTextArea(document.getElementById("code"), {
    lineNumbers: true,
    mode: "htmlmixed"
  });
});</pre>

    <p>It will automatically load the modes that the mixed HTML mode
    depends on (XML, JavaScript, and CSS). Do <em>not</em> use
    RequireJS' <code>paths</code> option to configure the path to
    CodeMirror, since it will break loading submodules through
    relative paths. Use
    the <a href="http://requirejs.org/docs/api.html#packages"><code>packages</code></a>
    configuration option instead, as in:</p>

    <pre data-lang=javascript>require.config({
  packages: [{
    name: "codemirror",
    location: "../path/to/codemirror",
    main: "lib/codemirror"
  }]
});</pre>

</section>

<section id=config>
    <h2>Configuration</h2>

    <p>Both the <a href="#CodeMirror"><code>CodeMirror</code></a>
    function and its <code>fromTextArea</code> method take as second
    (optional) argument an object containing configuration options.
    Any option not supplied like this will be taken
    from <a href="#defaults"><code>CodeMirror.defaults</code></a>, an
    object containing the default options. You can update this object
    to change the defaults on your page.</p>

    <p>Options are not checked in any way, so setting bogus option
    values is bound to lead to odd errors.</p>

    <p>These are the supported options:</p>

    <dl>
      <dt id="option_value"><code><strong>value</strong>: string|CodeMirror.Doc</code></dt>
      <dd>The starting value of the editor. Can be a string, or
      a <a href="#api_doc">document object</a>.</dd>

      <dt id="option_mode"><code><strong>mode</strong>: string|object</code></dt>
      <dd>The mode to use. When not given, this will default to the
      first mode that was loaded. It may be a string, which either
      simply names the mode or is
      a <a href="http://en.wikipedia.org/wiki/MIME">MIME</a> type
      associated with the mode. Alternatively, it may be an object
      containing configuration options for the mode, with
      a <code>name</code> property that names the mode (for
      example <code>{name: "javascript", json: true}</code>). The demo
      pages for each mode contain information about what configuration
      parameters the mode supports. You can ask CodeMirror which modes
      and MIME types have been defined by inspecting
      the <code>CodeMirror.modes</code>
      and <code>CodeMirror.mimeModes</code> objects. The first maps
      mode names to their constructors, and the second maps MIME types
      to mode specs.</dd>

      <dt id="option_lineSeparator"><code><strong>lineSeparator</strong>: string|null</code></dt>
      <dd>Explicitly set the line separator for the editor. By default
      (value <code>null</code>), the document will be split on CRLFs
      as well as lone CRs and LFs, and a single LF will be used as
      line separator in all output (such
      as <a href="#getValue"><code>getValue</code></a>). When a
      specific string is given, lines will only be split on that
      string, and output will, by default, use that same
      separator.</dd>

      <dt id="option_theme"><code><strong>theme</strong>: string</code></dt>
      <dd>The theme to style the editor with. You must make sure the
      CSS file defining the corresponding <code>.cm-s-[name]</code>
      styles is loaded (see
      the <a href="../theme/"><code>theme</code></a> directory in the
      distribution). The default is <code>"default"</code>, for which
      colors are included in <code>codemirror.css</code>. It is
      possible to use multiple theming classes at once—for
      example <code>"foo bar"</code> will assign both
      the <code>cm-s-foo</code> and the <code>cm-s-bar</code> classes
      to the editor.</dd>

      <dt id="option_indentUnit"><code><strong>indentUnit</strong>: integer</code></dt>
      <dd>How many spaces a block (whatever that means in the edited
      language) should be indented. The default is 2.</dd>

      <dt id="option_smartIndent"><code><strong>smartIndent</strong>: boolean</code></dt>
      <dd>Whether to use the context-sensitive indentation that the
      mode provides (or just indent the same as the line before).
      Defaults to true.</dd>

      <dt id="option_tabSize"><code><strong>tabSize</strong>: integer</code></dt>
      <dd>The width of a tab character. Defaults to 4.</dd>

      <dt id="option_indentWithTabs"><code><strong>indentWithTabs</strong>: boolean</code></dt>
      <dd>Whether, when indenting, the first N*<code>tabSize</code>
      spaces should be replaced by N tabs. Default is false.</dd>

      <dt id="option_electricChars"><code><strong>electricChars</strong>: boolean</code></dt>
      <dd>Configures whether the editor should re-indent the current
      line when a character is typed that might change its proper
      indentation (only works if the mode supports indentation).
      Default is true.</dd>

      <dt id="option_specialChars"><code><strong>specialChars</strong>: RegExp</code></dt>
      <dd>A regular expression used to determine which characters
      should be replaced by a
      special <a href="#option_specialCharPlaceholder">placeholder</a>.
      Mostly useful for non-printing special characters. The default
      is <code>/[\u0000-\u0019\u00ad\u200b-\u200f\u2028\u2029\ufeff]/</code>.</dd>
      <dt id="option_specialCharPlaceholder"><code><strong>specialCharPlaceholder</strong>: function(char) → Element</code></dt>
      <dd>A function that, given a special character identified by
      the <a href="#option_specialChars"><code>specialChars</code></a>
      option, produces a DOM node that is used to represent the
      character. By default, a red dot (<span style="color: red">•</span>)
      is shown, with a title tooltip to indicate the character code.</dd>

      <dt id="option_rtlMoveVisually"><code><strong>rtlMoveVisually</strong>: boolean</code></dt>
      <dd>Determines whether horizontal cursor movement through
      right-to-left (Arabic, Hebrew) text is visual (pressing the left
      arrow moves the cursor left) or logical (pressing the left arrow
      moves to the next lower index in the string, which is visually
      right in right-to-left text). The default is <code>false</code>
      on Windows, and <code>true</code> on other platforms.</dd>

      <dt id="option_keyMap"><code><strong>keyMap</strong>: string</code></dt>
      <dd>Configures the key map to use. The default
      is <code>"default"</code>, which is the only key map defined
      in <code>codemirror.js</code> itself. Extra key maps are found in
      the <a href="../keymap/"><code>key map</code></a> directory. See
      the <a href="#keymaps">section on key maps</a> for more
      information.</dd>

      <dt id="option_extraKeys"><code><strong>extraKeys</strong>: object</code></dt>
      <dd>Can be used to specify extra key bindings for the editor,
      alongside the ones defined
      by <a href="#option_keyMap"><code>keyMap</code></a>. Should be
      either null, or a valid <a href="#keymaps">key map</a> value.</dd>

      <dt id="option_lineWrapping"><code><strong>lineWrapping</strong>: boolean</code></dt>
      <dd>Whether CodeMirror should scroll or wrap for long lines.
      Defaults to <code>false</code> (scroll).</dd>

      <dt id="option_lineNumbers"><code><strong>lineNumbers</strong>: boolean</code></dt>
      <dd>Whether to show line numbers to the left of the editor.</dd>

      <dt id="option_firstLineNumber"><code><strong>firstLineNumber</strong>: integer</code></dt>
      <dd>At which number to start counting lines. Default is 1.</dd>

      <dt id="option_lineNumberFormatter"><code><strong>lineNumberFormatter</strong>: function(line: integer) → string</code></dt>
      <dd>A function used to format line numbers. The function is
      passed the line number, and should return a string that will be
      shown in the gutter.</dd>

      <dt id="option_gutters"><code><strong>gutters</strong>: array&lt;string&gt;</code></dt>
      <dd>Can be used to add extra gutters (beyond or instead of the
      line number gutter). Should be an array of CSS class names, each
      of which defines a <code>width</code> (and optionally a
      background), and which will be used to draw the background of
      the gutters. <em>May</em> include
      the <code>CodeMirror-linenumbers</code> class, in order to
      explicitly set the position of the line number gutter (it will
      default to be to the right of all other gutters). These class
      names are the keys passed
      to <a href="#setGutterMarker"><code>setGutterMarker</code></a>.</dd>

      <dt id="option_fixedGutter"><code><strong>fixedGutter</strong>: boolean</code></dt>
      <dd>Determines whether the gutter scrolls along with the content
      horizontally (false) or whether it stays fixed during horizontal
      scrolling (true, the default).</dd>

      <dt id="option_scrollbarStyle"><code><strong>scrollbarStyle</strong>: string</code></dt>
      <dd>Chooses a scrollbar implementation. The default
      is <code>"native"</code>, showing native scrollbars. The core
      library also provides the <code>"null"</code> style, which
      completely hides the
      scrollbars. <a href="#addon_simplescrollbars">Addons</a> can
      implement additional scrollbar models.</dd>

      <dt id="option_coverGutterNextToScrollbar"><code><strong>coverGutterNextToScrollbar</strong>: boolean</code></dt>
      <dd>When <a href="#option_fixedGutter"><code>fixedGutter</code></a>
      is on, and there is a horizontal scrollbar, by default the
      gutter will be visible to the left of this scrollbar. If this
      option is set to true, it will be covered by an element with
      class <code>CodeMirror-gutter-filler</code>.</dd>

      <dt id="option_inputStyle"><code><strong>inputStyle</strong>: string</code></dt>
      <dd>Selects the way CodeMirror handles input and focus. The core
      library defines the <code>"textarea"</code>
      and <code>"contenteditable"</code> input models. On mobile
      browsers, the default is <code>"contenteditable"</code>. On
      desktop browsers, the default is <code>"textarea"</code>.
      Support for IME and screen readers is better in
      the <code>"contenteditable"</code> model. The intention is to
      make it the default on modern desktop browsers in the
      future.</dd>

      <dt id="option_readOnly"><code><strong>readOnly</strong>: boolean|string</code></dt>
      <dd>This disables editing of the editor content by the user. If
      the special value <code>"nocursor"</code> is given (instead of
      simply <code>true</code>), focusing of the editor is also
      disallowed.</dd>

      <dt id="option_showCursorWhenSelecting"><code><strong>showCursorWhenSelecting</strong>: boolean</code></dt>
      <dd>Whether the cursor should be drawn when a selection is
      active. Defaults to false.</dd>

      <dt id="option_lineWiseCopyCut"><code><strong>lineWiseCopyCut</strong>: boolean</code></dt>
      <dd>When enabled, which is the default, doing copy or cut when
      there is no selection will copy or cut the whole lines that have
      cursors on them.</dd>

      <dt id="option_undoDepth"><code><strong>undoDepth</strong>: integer</code></dt>
      <dd>The maximum number of undo levels that the editor stores.
      Note that this includes selection change events. Defaults to
      200.</dd>

      <dt id="option_historyEventDelay"><code><strong>historyEventDelay</strong>: integer</code></dt>
      <dd>The period of inactivity (in milliseconds) that will cause a
      new history event to be started when typing or deleting.
      Defaults to 1250.</dd>

      <dt id="option_tabindex"><code><strong>tabindex</strong>: integer</code></dt>
      <dd>The <a href="http://www.w3.org/TR/html401/interact/forms.html#adef-tabindex">tab
      index</a> to assign to the editor. If not given, no tab index
      will be assigned.</dd>

      <dt id="option_autofocus"><code><strong>autofocus</strong>: boolean</code></dt>
      <dd>Can be used to make CodeMirror focus itself on
      initialization. Defaults to off.
      When <a href="#fromTextArea"><code>fromTextArea</code></a> is
      used, and no explicit value is given for this option, it will be
      set to true when either the source textarea is focused, or it
      has an <code>autofocus</code> attribute and no other element is
      focused.</dd>
    </dl>

    <p>Below this a few more specialized, low-level options are
    listed. These are only useful in very specific situations, you
    might want to skip them the first time you read this manual.</p>

    <dl>
      <dt id="option_dragDrop"><code><strong>dragDrop</strong>: boolean</code></dt>
      <dd>Controls whether drag-and-drop is enabled. On by default.</dd>

      <dt id="option_allowDropFileTypes"><code><strong>allowDropFileTypes</strong>: array&lt;string&gt;</code></dt>
      <dd>When set (default is <code>null</code>) only files whose
      type is in the array can be dropped into the editor. The strings
      should be MIME types, and will be checked against
      the <a href="https://w3c.github.io/FileAPI/#dfn-type"><code>type</code></a>
      of the <code>File</code> object as reported by the browser.</dd>

      <dt id="option_cursorBlinkRate"><code><strong>cursorBlinkRate</strong>: number</code></dt>
      <dd>Half-period in milliseconds used for cursor blinking. The default blink
      rate is 530ms. By setting this to zero, blinking can be disabled. A
      negative value hides the cursor entirely.</dd>

      <dt id="option_cursorScrollMargin"><code><strong>cursorScrollMargin</strong>: number</code></dt>
      <dd>How much extra space to always keep above and below the
      cursor when approaching the top or bottom of the visible view in
      a scrollable document. Default is 0.</dd>

      <dt id="option_cursorHeight"><code><strong>cursorHeight</strong>: number</code></dt>
      <dd>Determines the height of the cursor. Default is 1, meaning
      it spans the whole height of the line. For some fonts (and by
      some tastes) a smaller height (for example <code>0.85</code>),
      which causes the cursor to not reach all the way to the bottom
      of the line, looks better</dd>

      <dt id="option_resetSelectionOnContextMenu"><code><strong>resetSelectionOnContextMenu</strong>: boolean</code></dt>
      <dd>Controls whether, when the context menu is opened with a
      click outside of the current selection, the cursor is moved to
      the point of the click. Defaults to <code>true</code>.</dd>

      <dt id="option_workTime"><code id="option_wordkDelay"><strong>workTime</strong>, <strong>workDelay</strong>: number</code></dt>
      <dd>Highlighting is done by a pseudo background-thread that will
      work for <code>workTime</code> milliseconds, and then use
      timeout to sleep for <code>workDelay</code> milliseconds. The
      defaults are 200 and 300, you can change these options to make
      the highlighting more or less aggressive.</dd>

      <dt id="option_pollInterval"><code><strong>pollInterval</strong>: number</code></dt>
      <dd>Indicates how quickly CodeMirror should poll its input
      textarea for changes (when focused). Most input is captured by
      events, but some things, like IME input on some browsers, don't
      generate events that allow CodeMirror to properly detect it.
      Thus, it polls. Default is 100 milliseconds.</dd>

      <dt id="option_flattenSpans"><code><strong>flattenSpans</strong>: boolean</code></dt>
      <dd>By default, CodeMirror will combine adjacent tokens into a
      single span if they have the same class. This will result in a
      simpler DOM tree, and thus perform better. With some kinds of
      styling (such as rounded corners), this will change the way the
      document looks. You can set this option to false to disable this
      behavior.</dd>

      <dt id="option_addModeClass"><code><strong>addModeClass</strong>: boolean</code></dt>
      <dd>When enabled (off by default), an extra CSS class will be
      added to each token, indicating the
      (<a href="#innerMode">inner</a>) mode that produced it, prefixed
      with <code>"cm-m-"</code>. For example, tokens from the XML mode
      will get the <code>cm-m-xml</code> class.</dd>

      <dt id="option_maxHighlightLength"><code><strong>maxHighlightLength</strong>: number</code></dt>
      <dd>When highlighting long lines, in order to stay responsive,
      the editor will give up and simply style the rest of the line as
      plain text when it reaches a certain position. The default is
      10 000. You can set this to <code>Infinity</code> to turn off
      this behavior.</dd>

      <dt id="option_viewportMargin"><code><strong>viewportMargin</strong>: integer</code></dt>
      <dd>Specifies the amount of lines that are rendered above and
      below the part of the document that's currently scrolled into
      view. This affects the amount of updates needed when scrolling,
      and the amount of work that such an update does. You should
      usually leave it at its default, 10. Can be set
      to <code>Infinity</code> to make sure the whole document is
      always rendered, and thus the browser's text search works on it.
      This <em>will</em> have bad effects on performance of big
      documents.</dd>
    </dl>
</section>

<section id=events>
    <h2>Events</h2>

    <p>Various CodeMirror-related objects emit events, which allow
    client code to react to various situations. Handlers for such
    events can be registered with the <a href="#on"><code>on</code></a>
    and <a href="#off"><code>off</code></a> methods on the objects
    that the event fires on. To fire your own events,
    use <code>CodeMirror.signal(target, name, args...)</code>,
    where <code>target</code> is a non-DOM-node object.</p>

    <p>An editor instance fires the following events.
    The <code>instance</code> argument always refers to the editor
    itself.</p>

    <dl>
      <dt id="event_change"><code><strong>"change"</strong> (instance: CodeMirror, changeObj: object)</code></dt>
      <dd>Fires every time the content of the editor is changed.
      The <code>changeObj</code> is a <code>{from, to, text, removed,
      origin}</code> object containing information about the changes
      that occurred as second argument. <code>from</code>
      and <code>to</code> are the positions (in the pre-change
      coordinate system) where the change started and ended (for
      example, it might be <code>{ch:0, line:18}</code> if the
      position is at the beginning of line #19). <code>text</code> is
      an array of strings representing the text that replaced the
      changed range (split by line). <code>removed</code> is the text
      that used to be between <code>from</code> and <code>to</code>,
      which is overwritten by this change. This event is
      fired <em>before</em> the end of
      an <a href="#operation">operation</a>, before the DOM updates
      happen.</dd>

      <dt id="event_changes"><code><strong>"changes"</strong> (instance: CodeMirror, changes: array&lt;object&gt;)</code></dt>
      <dd>Like the <a href="#event_change"><code>"change"</code></a>
      event, but batched per <a href="#operation">operation</a>,
      passing an array containing all the changes that happened in the
      operation. This event is fired after the operation finished, and
      display changes it makes will trigger a new operation.</dd>

      <dt id="event_beforeChange"><code><strong>"beforeChange"</strong> (instance: CodeMirror, changeObj: object)</code></dt>
      <dd>This event is fired before a change is applied, and its
      handler may choose to modify or cancel the change.
      The <code>changeObj</code> object
      has <code>from</code>, <code>to</code>, and <code>text</code>
      properties, as with
      the <a href="#event_change"><code>"change"</code></a> event. It
      also has a <code>cancel()</code> method, which can be called to
      cancel the change, and, <strong>if</strong> the change isn't
      coming from an undo or redo event, an <code>update(from, to,
      text)</code> method, which may be used to modify the change.
      Undo or redo changes can't be modified, because they hold some
      metainformation for restoring old marked ranges that is only
      valid for that specific change. All three arguments
      to <code>update</code> are optional, and can be left off to
      leave the existing value for that field
      intact. <strong>Note:</strong> you may not do anything from
      a <code>"beforeChange"</code> handler that would cause changes
      to the document or its visualization. Doing so will, since this
      handler is called directly from the bowels of the CodeMirror
      implementation, probably cause the editor to become
      corrupted.</dd>

      <dt id="event_cursorActivity"><code><strong>"cursorActivity"</strong> (instance: CodeMirror)</code></dt>
      <dd>Will be fired when the cursor or selection moves, or any
      change is made to the editor content.</dd>

      <dt id="event_keyHandled"><code><strong>"keyHandled"</strong> (instance: CodeMirror, name: string, event: Event)</code></dt>
      <dd>Fired after a key is handled through a
      key map. <code>name</code> is the name of the handled key (for
      example <code>"Ctrl-X"</code> or <code>"'q'"</code>),
      and <code>event</code> is the DOM <code>keydown</code>
      or <code>keypress</code> event.</dd>

      <dt id="event_inputRead"><code><strong>"inputRead"</strong> (instance: CodeMirror, changeObj: object)</code></dt>
      <dd>Fired whenever new input is read from the hidden textarea
      (typed or pasted by the user).</dd>

      <dt id="event_electricInput"><code><strong>"electricInput"</strong> (instance: CodeMirror, line: integer)</code></dt>
      <dd>Fired if text input matched the
      mode's <a href="#option_electricChars">electric</a> patterns,
      and this caused the line's indentation to change.</dd>

      <dt id="event_beforeSelectionChange"><code><strong>"beforeSelectionChange"</strong> (instance: CodeMirror, obj: {ranges, origin, update})</code></dt>
      <dd>This event is fired before the selection is moved. Its
      handler may inspect the set of selection ranges, present as an
      array of <code>{anchor, head}</code> objects in
      the <code>ranges</code> property of the <code>obj</code>
      argument, and optionally change them by calling
      the <code>update</code> method on this object, passing an array
      of ranges in the same format. The object also contains
      an <code>origin</code> property holding the origin string passed
      to the selection-changing method, if any. Handlers for this
      event have the same restriction
      as <a href="#event_beforeChange"><code>"beforeChange"</code></a>
      handlers — they should not do anything to directly update the
      state of the editor.</dd>

      <dt id="event_viewportChange"><code><strong>"viewportChange"</strong> (instance: CodeMirror, from: number, to: number)</code></dt>
      <dd>Fires whenever the <a href="#getViewport">view port</a> of
      the editor changes (due to scrolling, editing, or any other
      factor). The <code>from</code> and <code>to</code> arguments
      give the new start and end of the viewport.</dd>

      <dt id="event_swapDoc"><code><strong>"swapDoc"</strong> (instance: CodeMirror, oldDoc: Doc)</code></dt>
      <dd>This is signalled when the editor's document is replaced
      using the <a href="#swapDoc"><code>swapDoc</code></a>
      method.</dd>

      <dt id="event_gutterClick"><code><strong>"gutterClick"</strong> (instance: CodeMirror, line: integer, gutter: string, clickEvent: Event)</code></dt>
      <dd>Fires when the editor gutter (the line-number area) is
      clicked. Will pass the editor instance as first argument, the
      (zero-based) number of the line that was clicked as second
      argument, the CSS class of the gutter that was clicked as third
      argument, and the raw <code>mousedown</code> event object as
      fourth argument.</dd>

      <dt id="event_gutterContextMenu"><code><strong>"gutterContextMenu"</strong> (instance: CodeMirror, line: integer, gutter: string, contextMenu: Event: Event)</code></dt>
      <dd>Fires when the editor gutter (the line-number area)
      receives a <code>contextmenu</code> event. Will pass the editor
      instance as first argument, the (zero-based) number of the line
      that was clicked as second argument, the CSS class of the
      gutter that was clicked as third argument, and the raw
      <code>contextmenu</code> mouse event object as fourth argument.
      You can <code>preventDefault</code> the event, to signal that
      CodeMirror should do no further handling.</dd>

      <dt id="event_focus"><code><strong>"focus"</strong> (instance: CodeMirror)</code></dt>
      <dd>Fires whenever the editor is focused.</dd>

      <dt id="event_blur"><code><strong>"blur"</strong> (instance: CodeMirror)</code></dt>
      <dd>Fires whenever the editor is unfocused.</dd>

      <dt id="event_scroll"><code><strong>"scroll"</strong> (instance: CodeMirror)</code></dt>
      <dd>Fires when the editor is scrolled.</dd>

      <dt id="event_scrollCursorIntoView"><code><strong>"scrollCursorIntoView"</strong> (instance: CodeMirror, event: Event)</code></dt>
      <dd>Fires when the editor tries to scroll its cursor into view.
      Can be hooked into to take care of additional scrollable
      containers around the editor. When the event object has
      its <code>preventDefault</code> method called, CodeMirror will
      not itself try to scroll the window.</dd>

      <dt id="event_update"><code><strong>"update"</strong> (instance: CodeMirror)</code></dt>
      <dd>Will be fired whenever CodeMirror updates its DOM display.</dd>

      <dt id="event_renderLine"><code><strong>"renderLine"</strong> (instance: CodeMirror, line: LineHandle, element: Element)</code></dt>
      <dd>Fired whenever a line is (re-)rendered to the DOM. Fired
      right after the DOM element is built, <em>before</em> it is
      added to the document. The handler may mess with the style of
      the resulting element, or add event handlers, but
      should <em>not</em> try to change the state of the editor.</dd>

      <dt id="event_dom"><code><strong>"mousedown"</strong>,
      <strong>"dblclick"</strong>, <strong>"touchstart"</strong>, <strong>"contextmenu"</strong>,
      <strong>"keydown"</strong>, <strong>"keypress"</strong>,
      <strong>"keyup"</strong>, <strong>"cut"</strong>, <strong>"copy"</strong>, <strong>"paste"</strong>,
      <strong>"dragstart"</strong>, <strong>"dragenter"</strong>,
      <strong>"dragover"</strong>, <strong>"drop"</strong>
      (instance: CodeMirror, event: Event)</code></dt>
      <dd>Fired when CodeMirror is handling a DOM event of this type.
      You can <code>preventDefault</code> the event, or give it a
      truthy <code>codemirrorIgnore</code> property, to signal that
      CodeMirror should do no further handling.</dd>
    </dl>

    <p>Document objects (instances
    of <a href="#Doc"><code>CodeMirror.Doc</code></a>) emit the
    following events:</p>

    <dl>
      <dt id="event_doc_change"><code><strong>"change"</strong> (doc: CodeMirror.Doc, changeObj: object)</code></dt>
      <dd>Fired whenever a change occurs to the
      document. <code>changeObj</code> has a similar type as the
      object passed to the
      editor's <a href="#event_change"><code>"change"</code></a>
      event.</dd>

      <dt id="event_doc_beforeChange"><code><strong>"beforeChange"</strong> (doc: CodeMirror.Doc, change: object)</code></dt>
      <dd>See the <a href="#event_beforeChange">description of the
      same event</a> on editor instances.</dd>

      <dt id="event_doc_cursorActivity"><code><strong>"cursorActivity"</strong> (doc: CodeMirror.Doc)</code></dt>
      <dd>Fired whenever the cursor or selection in this document
      changes.</dd>

      <dt id="event_doc_beforeSelectionChange"><code><strong>"beforeSelectionChange"</strong> (doc: CodeMirror.Doc, selection: {head, anchor})</code></dt>
      <dd>Equivalent to
      the <a href="#event_beforeSelectionChange">event by the same
      name</a> as fired on editor instances.</dd>
    </dl>

    <p>Line handles (as returned by, for
    example, <a href="#getLineHandle"><code>getLineHandle</code></a>)
    support these events:</p>

    <dl>
      <dt id="event_delete"><code><strong>"delete"</strong> ()</code></dt>
      <dd>Will be fired when the line object is deleted. A line object
      is associated with the <em>start</em> of the line. Mostly useful
      when you need to find out when your <a href="#setGutterMarker">gutter
      markers</a> on a given line are removed.</dd>
      <dt id="event_line_change"><code><strong>"change"</strong> (line: LineHandle, changeObj: object)</code></dt>
      <dd>Fires when the line's text content is changed in any way
      (but the line is not deleted outright). The <code>change</code>
      object is similar to the one passed
      to <a href="#event_change">change event</a> on the editor
      object.</dd>
    </dl>

    <p>Marked range handles (<code>CodeMirror.TextMarker</code>), as returned
    by <a href="#markText"><code>markText</code></a>
    and <a href="#setBookmark"><code>setBookmark</code></a>, emit the
    following events:</p>

    <dl>
      <dt id="event_beforeCursorEnter"><code><strong>"beforeCursorEnter"</strong> ()</code></dt>
      <dd>Fired when the cursor enters the marked range. From this
      event handler, the editor state may be inspected
      but <em>not</em> modified, with the exception that the range on
      which the event fires may be cleared.</dd>
      <dt id="event_clear"><code><strong>"clear"</strong> (from: {line, ch}, to: {line, ch})</code></dt>
      <dd>Fired when the range is cleared, either through cursor
      movement in combination
      with <a href="#mark_clearOnEnter"><code>clearOnEnter</code></a>
      or through a call to its <code>clear()</code> method. Will only
      be fired once per handle. Note that deleting the range through
      text editing does not fire this event, because an undo action
      might bring the range back into existence. <code>from</code>
      and <code>to</code> give the part of the document that the range
      spanned when it was cleared.</dd>
      <dt id="event_hide"><code><strong>"hide"</strong> ()</code></dt>
      <dd>Fired when the last part of the marker is removed from the
      document by editing operations.</dd>
      <dt id="event_unhide"><code><strong>"unhide"</strong> ()</code></dt>
      <dd>Fired when, after the marker was removed by editing, a undo
      operation brought the marker back.</dd>
    </dl>

    <p>Line widgets (<code>CodeMirror.LineWidget</code>), returned
    by <a href="#addLineWidget"><code>addLineWidget</code></a>, fire
    these events:</p>

    <dl>
      <dt id="event_redraw"><code><strong>"redraw"</strong> ()</code></dt>
      <dd>Fired whenever the editor re-adds the widget to the DOM.
      This will happen once right after the widget is added (if it is
      scrolled into view), and then again whenever it is scrolled out
      of view and back in again, or when changes to the editor options
      or the line the widget is on require the widget to be
      redrawn.</dd>
    </dl>
</section>

<section id=keymaps>
    <h2>Key Maps</h2>

    <p>Key maps are ways to associate keys with functionality. A key map
    is an object mapping strings that identify the keys to functions
    that implement their functionality.</p>

    <p>The CodeMirror distributions comes
    with <a href="../demo/emacs.html">Emacs</a>, <a href="../demo/vim.html">Vim</a>,
    and <a href="../demo/sublime.html">Sublime Text</a>-style keymaps.</p>

    <p>Keys are identified either by name or by character.
    The <code>CodeMirror.keyNames</code> object defines names for
    common keys and associates them with their key codes. Examples of
    names defined here are <code>Enter</code>, <code>F5</code>,
    and <code>Q</code>. These can be prefixed
    with <code>Shift-</code>, <code>Cmd-</code>, <code>Ctrl-</code>,
    and <code>Alt-</code> to specify a modifier. So for
    example, <code>Shift-Ctrl-Space</code> would be a valid key
    identifier.</p>

    <p>Common example: map the Tab key to insert spaces instead of a tab
    character.</p>

    <pre data-lang="javascript">
editor.setOption("extraKeys", {
  Tab: function(cm) {
    var spaces = Array(cm.getOption("indentUnit") + 1).join(" ");
    cm.replaceSelection(spaces);
  }
});</pre>

    <p>Alternatively, a character can be specified directly by
    surrounding it in single quotes, for example <code>'$'</code>
    or <code>'q'</code>. Due to limitations in the way browsers fire
    key events, these may not be prefixed with modifiers.</p>

    <p id="normalizeKeyMap">Multi-stroke key bindings can be specified
    by separating the key names by spaces in the property name, for
    example <code>Ctrl-X Ctrl-V</code>. When a map contains
    multi-stoke bindings or keys with modifiers that are not specified
    in the default order (<code>Shift-Cmd-Ctrl-Alt</code>), you must
    call <code>CodeMirror.normalizeKeyMap</code> on it before it can
    be used. This function takes a keymap and modifies it to normalize
    modifier order and properly recognize multi-stroke bindings. It
    will return the keymap itself.</p>

    <p>The <code>CodeMirror.keyMap</code> object associates key maps
    with names. User code and key map definitions can assign extra
    properties to this object. Anywhere where a key map is expected, a
    string can be given, which will be looked up in this object. It
    also contains the <code>"default"</code> key map holding the
    default bindings.</p>

    <p>The values of properties in key maps can be either functions of
    a single argument (the CodeMirror instance), strings, or
    <code>false</code>. Strings refer
    to <a href="#commands">commands</a>, which are described below. If
    the property is set to <code>false</code>, CodeMirror leaves
    handling of the key up to the browser. A key handler function may
    return <code>CodeMirror.Pass</code> to indicate that it has
    decided not to handle the key, and other handlers (or the default
    behavior) should be given a turn.</p>

    <p>Keys mapped to command names that start with the
    characters <code>"go"</code> or to functions that have a
    truthy <code>motion</code> property (which should be used for
    cursor-movement actions) will be fired even when an
    extra <code>Shift</code> modifier is present (i.e. <code>"Up":
    "goLineUp"</code> matches both up and shift-up). This is used to
    easily implement shift-selection.</p>

    <p>Key maps can defer to each other by defining
    a <code>fallthrough</code> property. This indicates that when a
    key is not found in the map itself, one or more other maps should
    be searched. It can hold either a single key map or an array of
    key maps.</p>

    <p>When a key map needs to set something up when it becomes
    active, or tear something down when deactivated, it can
    contain <code>attach</code> and/or <code>detach</code> properties,
    which should hold functions that take the editor instance and the
    next or previous keymap. Note that this only works for the
    <a href="#option_keyMap">top-level keymap</a>, not for fallthrough
    maps or maps added
    with <a href="#option_extraKeys"><code>extraKeys</code></a>
    or <a href="#addKeyMap"><code>addKeyMap</code></a>.</p>
</section>

<section id=commands>
    <h2>Commands</h2>

    <p>Commands are parameter-less actions that can be performed on an
    editor. Their main use is for key bindings. Commands are defined by
    adding properties to the <code>CodeMirror.commands</code> object.
    A number of common commands are defined by the library itself,
    most of them used by the default key bindings. The value of a
    command property must be a function of one argument (an editor
    instance).</p>

    <p>Some of the commands below are referenced in the default
    key map, but not defined by the core library. These are intended to
    be defined by user code or addons.</p>

    <p>Commands can also be run with
    the <a href="#execCommand"><code>execCommand</code></a>
    method.</p>

    <dl>
      <dt class=command id=command_selectAll><code><strong>selectAll</strong></code><span class=keybinding>Ctrl-A (PC), Cmd-A (Mac)</span></dt>
      <dd>Select the whole content of the editor.</dd>

      <dt class=command id=command_singleSelection><code><strong>singleSelection</strong></code><span class=keybinding>Esc</span></dt>
      <dd>When multiple selections are present, this deselects all but
      the primary selection.</dd>

      <dt class=command id=command_killLine><code><strong>killLine</strong></code><span class=keybinding>Ctrl-K (Mac)</span></dt>
      <dd>Emacs-style line killing. Deletes the part of the line after
      the cursor. If that consists only of whitespace, the newline at
      the end of the line is also deleted.</dd>

      <dt class=command id=command_deleteLine><code><strong>deleteLine</strong></code><span class=keybinding>Ctrl-D (PC), Cmd-D (Mac)</span></dt>
      <dd>Deletes the whole line under the cursor, including newline at the end.</dd>

      <dt class=command id=command_delLineLeft><code><strong>delLineLeft</strong></code></dt>
      <dd>Delete the part of the line before the cursor.</dd>

      <dt class=command id=command_delWrappedLineLeft><code><strong>delWrappedLineLeft</strong></code><span class=keybinding>Cmd-Backspace (Mac)</span></dt>
      <dd>Delete the part of the line from the left side of the visual line the cursor is on to the cursor.</dd>

      <dt class=command id=command_delWrappedLineRight><code><strong>delWrappedLineRight</strong></code><span class=keybinding>Cmd-Delete (Mac)</span></dt>
      <dd>Delete the part of the line from the cursor to the right side of the visual line the cursor is on.</dd>

      <dt class=command id=command_undo><code><strong>undo</strong></code><span class=keybinding>Ctrl-Z (PC), Cmd-Z (Mac)</span></dt>
      <dd>Undo the last change.</dd>

      <dt class=command id=command_redo><code><strong>redo</strong></code><span class=keybinding>Ctrl-Y (PC), Shift-Cmd-Z (Mac), Cmd-Y (Mac)</span></dt>
      <dd>Redo the last undone change.</dd>

      <dt class=command id=command_undoSelection><code><strong>undoSelection</strong></code><span class=keybinding>Ctrl-U (PC), Cmd-U (Mac)</span></dt>
      <dd>Undo the last change to the selection, or if there are no
      selection-only changes at the top of the history, undo the last
      change.</dd>

      <dt class=command id=command_redoSelection><code><strong>redoSelection</strong></code><span class=keybinding>Alt-U (PC), Shift-Cmd-U (Mac)</span></dt>
      <dd>Redo the last change to the selection, or the last text change if
      no selection changes remain.</dd>

      <dt class=command id=command_goDocStart><code><strong>goDocStart</strong></code><span class=keybinding>Ctrl-Home (PC), Cmd-Up (Mac), Cmd-Home (Mac)</span></dt>
      <dd>Move the cursor to the start of the document.</dd>

      <dt class=command id=command_goDocEnd><code><strong>goDocEnd</strong></code><span class=keybinding>Ctrl-End (PC), Cmd-End (Mac), Cmd-Down (Mac)</span></dt>
      <dd>Move the cursor to the end of the document.</dd>

      <dt class=command id=command_goLineStart><code><strong>goLineStart</strong></code><span class=keybinding>Alt-Left (PC), Ctrl-A (Mac)</span></dt>
      <dd>Move the cursor to the start of the line.</dd>

      <dt class=command id=command_goLineStartSmart><code><strong>goLineStartSmart</strong></code><span class=keybinding>Home</span></dt>
      <dd>Move to the start of the text on the line, or if we are
      already there, to the actual start of the line (including
      whitespace).</dd>

      <dt class=command id=command_goLineEnd><code><strong>goLineEnd</strong></code><span class=keybinding>Alt-Right (PC), Ctrl-E (Mac)</span></dt>
      <dd>Move the cursor to the end of the line.</dd>

      <dt class=command id=command_goLineRight><code><strong>goLineRight</strong></code><span class=keybinding>Cmd-Right (Mac)</span></dt>
      <dd>Move the cursor to the right side of the visual line it is on.</dd>

      <dt class=command id=command_goLineLeft><code><strong>goLineLeft</strong></code><span class=keybinding>Cmd-Left (Mac)</span></dt>
      <dd>Move the cursor to the left side of the visual line it is on. If
      this line is wrapped, that may not be the start of the line.</dd>

      <dt class=command id=command_goLineLeftSmart><code><strong>goLineLeftSmart</strong></code></dt>
      <dd>Move the cursor to the left side of the visual line it is
      on. If that takes it to the start of the line, behave
      like <a href="#command_goLineStartSmart"><code>goLineStartSmart</code></a>.</dd>

      <dt class=command id=command_goLineUp><code><strong>goLineUp</strong></code><span class=keybinding>Up, Ctrl-P (Mac)</span></dt>
      <dd>Move the cursor up one line.</dd>

      <dt class=command id=command_goLineDown><code><strong>goLineDown</strong></code><span class=keybinding>Down, Ctrl-N (Mac)</span></dt>
      <dd>Move down one line.</dd>

      <dt class=command id=command_goPageUp><code><strong>goPageUp</strong></code><span class=keybinding>PageUp, Shift-Ctrl-V (Mac)</span></dt>
      <dd>Move the cursor up one screen, and scroll up by the same distance.</dd>

      <dt class=command id=command_goPageDown><code><strong>goPageDown</strong></code><span class=keybinding>PageDown, Ctrl-V (Mac)</span></dt>
      <dd>Move the cursor down one screen, and scroll down by the same distance.</dd>

      <dt class=command id=command_goCharLeft><code><strong>goCharLeft</strong></code><span class=keybinding>Left, Ctrl-B (Mac)</span></dt>
      <dd>Move the cursor one character left, going to the previous line
      when hitting the start of line.</dd>

      <dt class=command id=command_goCharRight><code><strong>goCharRight</strong></code><span class=keybinding>Right, Ctrl-F (Mac)</span></dt>
      <dd>Move the cursor one character right, going to the next line
      when hitting the end of line.</dd>

      <dt class=command id=command_goColumnLeft><code><strong>goColumnLeft</strong></code></dt>
      <dd>Move the cursor one character left, but don't cross line boundaries.</dd>

      <dt class=command id=command_goColumnRight><code><strong>goColumnRight</strong></code></dt>
      <dd>Move the cursor one character right, don't cross line boundaries.</dd>

      <dt class=command id=command_goWordLeft><code><strong>goWordLeft</strong></code><span class=keybinding>Alt-B (Mac)</span></dt>
      <dd>Move the cursor to the start of the previous word.</dd>

      <dt class=command id=command_goWordRight><code><strong>goWordRight</strong></code><span class=keybinding>Alt-F (Mac)</span></dt>
      <dd>Move the cursor to the end of the next word.</dd>

      <dt class=command id=command_goGroupLeft><code><strong>goGroupLeft</strong></code><span class=keybinding>Ctrl-Left (PC), Alt-Left (Mac)</span></dt>
      <dd>Move to the left of the group before the cursor. A group is
      a stretch of word characters, a stretch of punctuation
      characters, a newline, or a stretch of <em>more than one</em>
      whitespace character.</dd>

      <dt class=command id=command_goGroupRight><code><strong>goGroupRight</strong></code><span class=keybinding>Ctrl-Right (PC), Alt-Right (Mac)</span></dt>
      <dd>Move to the right of the group after the cursor
      (see <a href="#command_goGroupLeft">above</a>).</dd>

      <dt class=command id=command_delCharBefore><code><strong>delCharBefore</strong></code><span class=keybinding>Shift-Backspace, Ctrl-H (Mac)</span></dt>
      <dd>Delete the character before the cursor.</dd>

      <dt class=command id=command_delCharAfter><code><strong>delCharAfter</strong></code><span class=keybinding>Delete, Ctrl-D (Mac)</span></dt>
      <dd>Delete the character after the cursor.</dd>

      <dt class=command id=command_delWordBefore><code><strong>delWordBefore</strong></code><span class=keybinding>Alt-Backspace (Mac)</span></dt>
      <dd>Delete up to the start of the word before the cursor.</dd>

      <dt class=command id=command_delWordAfter><code><strong>delWordAfter</strong></code><span class=keybinding>Alt-D (Mac)</span></dt>
      <dd>Delete up to the end of the word after the cursor.</dd>

      <dt class=command id=command_delGroupBefore><code><strong>delGroupBefore</strong></code><span class=keybinding>Ctrl-Backspace (PC), Alt-Backspace (Mac)</span></dt>
      <dd>Delete to the left of the <a href="#command_goGroupLeft">group</a> before the cursor.</dd>

      <dt class=command id=command_delGroupAfter><code><strong>delGroupAfter</strong></code><span class=keybinding>Ctrl-Delete (PC), Ctrl-Alt-Backspace (Mac), Alt-Delete (Mac)</span></dt>
      <dd>Delete to the start of the <a href="#command_goGroupLeft">group</a> after the cursor.</dd>

      <dt class=command id=command_indentAuto><code><strong>indentAuto</strong></code><span class=keybinding>Shift-Tab</span></dt>
      <dd>Auto-indent the current line or selection.</dd>

      <dt class=command id=command_indentMore><code><strong>indentMore</strong></code><span class=keybinding>Ctrl-] (PC), Cmd-] (Mac)</span></dt>
      <dd>Indent the current line or selection by one <a href="#option_indentUnit">indent unit</a>.</dd>

      <dt class=command id=command_indentLess><code><strong>indentLess</strong></code><span class=keybinding>Ctrl-[ (PC), Cmd-[ (Mac)</span></dt>
      <dd>Dedent the current line or selection by one <a href="#option_indentUnit">indent unit</a>.</dd>

      <dt class=command id=command_insertTab><code><strong>insertTab</strong></code></dt>
      <dd>Insert a tab character at the cursor.</dd>

      <dt class=command id=command_insertSoftTab><code><strong>insertSoftTab</strong></code></dt>
      <dd>Insert the amount of spaces that match the width a tab at
      the cursor position would have.</dd>

      <dt class=command id=command_defaultTab><code><strong>defaultTab</strong></code><span class=keybinding>Tab</span></dt>
      <dd>If something is selected, indent it by
      one <a href="#option_indentUnit">indent unit</a>. If nothing is
      selected, insert a tab character.</dd>

      <dt class=command id=command_transposeChars><code><strong>transposeChars</strong></code><span class=keybinding>Ctrl-T (Mac)</span></dt>
      <dd>Swap the characters before and after the cursor.</dd>

      <dt class=command id=command_newlineAndIndent><code><strong>newlineAndIndent</strong></code><span class=keybinding>Enter</span></dt>
      <dd>Insert a newline and auto-indent the new line.</dd>

      <dt class=command id=command_toggleOverwrite><code><strong>toggleOverwrite</strong></code><span class=keybinding>Insert</span></dt>
      <dd>Flip the <a href="#toggleOverwrite">overwrite</a> flag.</dd>

      <dt class=command id=command_save><code><strong>save</strong></code><span class=keybinding>Ctrl-S (PC), Cmd-S (Mac)</span></dt>
      <dd>Not defined by the core library, only referred to in
      key maps. Intended to provide an easy way for user code to define
      a save command.</dd>

      <dt class=command id=command_find><code><strong>find</strong></code><span class=keybinding>Ctrl-F (PC), Cmd-F (Mac)</span></dt>
      <dt class=command id=command_findNext><code><strong>findNext</strong></code><span class=keybinding>Ctrl-G (PC), Cmd-G (Mac)</span></dt>
      <dt class=command id=command_findPrev><code><strong>findPrev</strong></code><span class=keybinding>Shift-Ctrl-G (PC), Shift-Cmd-G (Mac)</span></dt>
      <dt class=command id=command_replace><code><strong>replace</strong></code><span class=keybinding>Shift-Ctrl-F (PC), Cmd-Alt-F (Mac)</span></dt>
      <dt class=command id=command_replaceAll><code><strong>replaceAll</strong></code><span class=keybinding>Shift-Ctrl-R (PC), Shift-Cmd-Alt-F (Mac)</span></dt>
      <dd>Not defined by the core library, but defined in
      the <a href="#addon_search">search addon</a> (or custom client
      addons).</dd>

    </dl>

</section>

<section id=styling>
    <h2>Customized Styling</h2>

    <p>Up to a certain extent, CodeMirror's look can be changed by
    modifying style sheet files. The style sheets supplied by modes
    simply provide the colors for that mode, and can be adapted in a
    very straightforward way. To style the editor itself, it is
    possible to alter or override the styles defined
    in <a href="../lib/codemirror.css"><code>codemirror.css</code></a>.</p>

    <p>Some care must be taken there, since a lot of the rules in this
    file are necessary to have CodeMirror function properly. Adjusting
    colors should be safe, of course, and with some care a lot of
    other things can be changed as well. The CSS classes defined in
    this file serve the following roles:</p>

    <dl>
      <dt id="class_CodeMirror"><code><strong>CodeMirror</strong></code></dt>
      <dd>The outer element of the editor. This should be used for the
      editor width, height, borders and positioning. Can also be used
      to set styles that should hold for everything inside the editor
      (such as font and font size), or to set a background. Setting
      this class' <code>height</code> style to <code>auto</code> will
      make the editor <a href="../demo/resize.html">resize to fit its
      content</a> (it is recommended to also set
      the <a href="#option_viewportMargin"><code>viewportMargin</code>
      option</a> to <code>Infinity</code> when doing this.</dd>

      <dt id="class_CodeMirror_focused"><code><strong>CodeMirror-focused</strong></code></dt>
      <dd>Whenever the editor is focused, the top element gets this
      class. This is used to hide the cursor and give the selection a
      different color when the editor is not focused.</dd>

      <dt id="class_CodeMirror_gutters"><code><strong>CodeMirror-gutters</strong></code></dt>
      <dd>This is the backdrop for all gutters. Use it to set the
      default gutter background color, and optionally add a border on
      the right of the gutters.</dd>

      <dt id="class_CodeMirror_linenumbers"><code><strong>CodeMirror-linenumbers</strong></code></dt>
      <dd>Use this for giving a background or width to the line number
      gutter.</dd>

      <dt id="class_CodeMirror_linenumber"><code><strong>CodeMirror-linenumber</strong></code></dt>
      <dd>Used to style the actual individual line numbers. These
      won't be children of the <code>CodeMirror-linenumbers</code>
      (plural) element, but rather will be absolutely positioned to
      overlay it. Use this to set alignment and text properties for
      the line numbers.</dd>

      <dt id="class_CodeMirror_lines"><code><strong>CodeMirror-lines</strong></code></dt>
      <dd>The visible lines. This is where you specify vertical
      padding for the editor content.</dd>

      <dt id="class_CodeMirror_cursor"><code><strong>CodeMirror-cursor</strong></code></dt>
      <dd>The cursor is a block element that is absolutely positioned.
      You can make it look whichever way you want.</dd>

      <dt id="class_CodeMirror_selected"><code><strong>CodeMirror-selected</strong></code></dt>
      <dd>The selection is represented by <code>span</code> elements
      with this class.</dd>

      <dt id="class_CodeMirror_matchingbracket"><code><strong>CodeMirror-matchingbracket</strong></code>,
        <code><strong>CodeMirror-nonmatchingbracket</strong></code></dt>
      <dd>These are used to style matched (or unmatched) brackets.</dd>
    </dl>

    <p>If your page's style sheets do funky things to
    all <code>div</code> or <code>pre</code> elements (you probably
    shouldn't do that), you'll have to define rules to cancel these
    effects out again for elements under the <code>CodeMirror</code>
    class.</p>

    <p>Themes are also simply CSS files, which define colors for
    various syntactic elements. See the files in
    the <a href="../theme/"><code>theme</code></a> directory.</p>
</section>

<section id=api>
    <h2>Programming API</h2>

    <p>A lot of CodeMirror features are only available through its
    API. Thus, you need to write code (or
    use <a href="#addons">addons</a>) if you want to expose them to
    your users.</p>

    <p>Whenever points in the document are represented, the API uses
    objects with <code>line</code> and <code>ch</code> properties.
    Both are zero-based. CodeMirror makes sure to 'clip' any positions
    passed by client code so that they fit inside the document, so you
    shouldn't worry too much about sanitizing your coordinates. If you
    give <code>ch</code> a value of <code>null</code>, or don't
    specify it, it will be replaced with the length of the specified
    line.</p>

    <p>Methods prefixed with <code>doc.</code> can, unless otherwise
    specified, be called both on <code>CodeMirror</code> (editor)
    instances and <code>CodeMirror.Doc</code> instances. Methods
    prefixed with <code>cm.</code> are <em>only</em> available
    on <code>CodeMirror</code> instances.</p>

    <h3 id="api_constructor">Constructor</h3>

    <p id="CodeMirror">Constructing an editor instance is done with
    the <code><strong>CodeMirror</strong>(place: Element|fn(Element),
    ?option: object)</code> constructor. If the <code>place</code>
    argument is a DOM element, the editor will be appended to it. If
    it is a function, it will be called, and is expected to place the
    editor into the document. <code>options</code> may be an element
    mapping <a href="#config">option names</a> to values. The options
    that it doesn't explicitly specify (or all options, if it is not
    passed) will be taken
    from <a href="#defaults"><code>CodeMirror.defaults</code></a>.</p>

    <p>Note that the options object passed to the constructor will be
    mutated when the instance's options
    are <a href="#setOption">changed</a>, so you shouldn't share such
    objects between instances.</p>

    <p>See <a href="#fromTextArea"><code>CodeMirror.fromTextArea</code></a>
    for another way to construct an editor instance.</p>

    <h3 id="api_content">Content manipulation methods</h3>

    <dl>
      <dt id="getValue"><code><strong>doc.getValue</strong>(?separator: string) → string</code></dt>
      <dd>Get the current editor content. You can pass it an optional
      argument to specify the string to be used to separate lines
      (defaults to <code>"\n"</code>).</dd>
      <dt id="setValue"><code><strong>doc.setValue</strong>(content: string)</code></dt>
      <dd>Set the editor content.</dd>

      <dt id="getRange"><code><strong>doc.getRange</strong>(from: {line, ch}, to: {line, ch}, ?separator: string) → string</code></dt>
      <dd>Get the text between the given points in the editor, which
      should be <code>{line, ch}</code> objects. An optional third
      argument can be given to indicate the line separator string to
      use (defaults to <code>"\n"</code>).</dd>
      <dt id="replaceRange"><code><strong>doc.replaceRange</strong>(replacement: string, from: {line, ch}, to: {line, ch}, ?origin: string)</code></dt>
      <dd>Replace the part of the document between <code>from</code>
      and <code>to</code> with the given string. <code>from</code>
      and <code>to</code> must be <code>{line, ch}</code>
      objects. <code>to</code> can be left off to simply insert the
      string at position <code>from</code>. When <code>origin</code>
      is given, it will be passed on
      to <a href="#event_change"><code>"change"</code> events</a>, and
      its first letter will be used to determine whether this change
      can be merged with previous history events, in the way described
      for <a href="#selection_origin">selection origins</a>.</dd>

      <dt id="getLine"><code><strong>doc.getLine</strong>(n: integer) → string</code></dt>
      <dd>Get the content of line <code>n</code>.</dd>

      <dt id="lineCount"><code><strong>doc.lineCount</strong>() → integer</code></dt>
      <dd>Get the number of lines in the editor.</dd>
      <dt id="firstLine"><code><strong>doc.firstLine</strong>() → integer</code></dt>
      <dd>Get the first line of the editor. This will
      usually be zero but for <a href="#linkedDoc_from">linked sub-views</a>,
      or <a href="#api_doc">documents</a> instantiated with a non-zero
      first line, it might return other values.</dd>
      <dt id="lastLine"><code><strong>doc.lastLine</strong>() → integer</code></dt>
      <dd>Get the last line of the editor. This will
      usually be <code>doc.lineCount() - 1</code>,
      but for <a href="#linkedDoc_from">linked sub-views</a>,
      it might return other values.</dd>

      <dt id="getLineHandle"><code><strong>doc.getLineHandle</strong>(num: integer) → LineHandle</code></dt>
      <dd>Fetches the line handle for the given line number.</dd>
      <dt id="getLineNumber"><code><strong>doc.getLineNumber</strong>(handle: LineHandle) → integer</code></dt>
      <dd>Given a line handle, returns the current position of that
      line (or <code>null</code> when it is no longer in the
      document).</dd>
      <dt id="eachLine"><code><strong>doc.eachLine</strong>(f: (line: LineHandle))</code></dt>
      <dt><code><strong>doc.eachLine</strong>(start: integer, end: integer, f: (line: LineHandle))</code></dt>
      <dd>Iterate over the whole document, or if <code>start</code>
      and <code>end</code> line numbers are given, the range
      from <code>start</code> up to (not including) <code>end</code>,
      and call <code>f</code> for each line, passing the line handle.
      This is a faster way to visit a range of line handlers than
      calling <a href="#getLineHandle"><code>getLineHandle</code></a>
      for each of them. Note that line handles have
      a <code>text</code> property containing the line's content (as a
      string).</dd>

      <dt id="markClean"><code><strong>doc.markClean</strong>()</code></dt>
      <dd>Set the editor content as 'clean', a flag that it will
      retain until it is edited, and which will be set again when such
      an edit is undone again. Useful to track whether the content
      needs to be saved. This function is deprecated in favor
      of <a href="#changeGeneration"><code>changeGeneration</code></a>,
      which allows multiple subsystems to track different notions of
      cleanness without interfering.</dd>
      <dt id="changeGeneration"><code><strong>doc.changeGeneration</strong>(?closeEvent: boolean) → integer</code></dt>
      <dd>Returns a number that can later be passed
      to <a href="#isClean"><code>isClean</code></a> to test whether
      any edits were made (and not undone) in the meantime.
      If <code>closeEvent</code> is true, the current history event
      will be ‘closed’, meaning it can't be combined with further
      changes (rapid typing or deleting events are typically
      combined).</dd>
      <dt id="isClean"><code><strong>doc.isClean</strong>(?generation: integer) → boolean</code></dt>
      <dd>Returns whether the document is currently clean — not
      modified since initialization or the last call
      to <a href="#markClean"><code>markClean</code></a> if no
      argument is passed, or since the matching call
      to <a href="#changeGeneration"><code>changeGeneration</code></a>
      if a generation value is given.</dd>
    </dl>

    <h3 id="api_selection">Cursor and selection methods</h3>

    <dl>
      <dt id="getSelection"><code><strong>doc.getSelection</strong>(?lineSep: string) → string</code></dt>
      <dd>Get the currently selected code. Optionally pass a line
      separator to put between the lines in the output. When multiple
      selections are present, they are concatenated with instances
      of <code>lineSep</code> in between.</dd>
      <dt id="getSelections"><code><strong>doc.getSelections</strong>(?lineSep: string) → string</code></dt>
      <dd>Returns an array containing a string for each selection,
      representing the content of the selections.</dd>

      <dt id="replaceSelection"><code><strong>doc.replaceSelection</strong>(replacement: string, ?select: string)</code></dt>
      <dd>Replace the selection(s) with the given string. By default,
      the new selection ends up after the inserted text. The
      optional <code>select</code> argument can be used to change
      this—passing <code>"around"</code> will cause the new text to be
      selected, passing <code>"start"</code> will collapse the
      selection to the start of the inserted text.</dd>
      <dt id="replaceSelections"><code><strong>doc.replaceSelections</strong>(replacements: array&lt;string&gt;, ?select: string)</code></dt>
      <dd>The length of the given array should be the same as the
      number of active selections. Replaces the content of the
      selections with the strings in the array.
      The <code>select</code> argument works the same as
      in <a href="#replaceSelection"><code>replaceSelection</code></a>.</dd>

      <dt id="getCursor"><code><strong>doc.getCursor</strong>(?start: string) → {line, ch}</code></dt>
      <dd>Retrieve one end of the <em>primary</em>
      selection. <code>start</code> is an optional string indicating
      which end of the selection to return. It may
      be <code>"from"</code>, <code>"to"</code>, <code>"head"</code>
      (the side of the selection that moves when you press
      shift+arrow), or <code>"anchor"</code> (the fixed side of the
      selection). Omitting the argument is the same as
      passing <code>"head"</code>. A <code>{line, ch}</code> object
      will be returned.</dd>
      <dt id="listSelections"><code><strong>doc.listSelections</strong>() → array&lt;{anchor, head}&gt;</code></dt>
      <dd>Retrieves a list of all current selections. These will
      always be sorted, and never overlap (overlapping selections are
      merged). Each object in the array contains <code>anchor</code>
      and <code>head</code> properties referring to <code>{line,
      ch}</code> objects.</dd>

      <dt id="somethingSelected"><code><strong>doc.somethingSelected</strong>() → boolean</code></dt>
      <dd>Return true if any text is selected.</dd>
      <dt id="setCursor"><code><strong>doc.setCursor</strong>(pos: {line, ch}|number, ?ch: number, ?options: object)</code></dt>
      <dd>Set the cursor position. You can either pass a
      single <code>{line, ch}</code> object, or the line and the
      character as two separate parameters. Will replace all
      selections with a single, empty selection at the given position.
      The supported options are the same as for <a href="#setSelection"><code>setSelection</code></a>.</dd>

      <dt id="setSelection"><code><strong>doc.setSelection</strong>(anchor: {line, ch}, ?head: {line, ch}, ?options: object)</code></dt>
      <dd>Set a single selection range. <code>anchor</code>
      and <code>head</code> should be <code>{line, ch}</code>
      objects. <code>head</code> defaults to <code>anchor</code> when
      not given. These options are supported:
      <dl>
        <dt id="selection_scroll"><code><strong>scroll</strong>: boolean</code></dt>
        <dd>Determines whether the selection head should be scrolled
        into view. Defaults to true.</dd>
        <dt id="selection_origin"><code><strong>origin</strong>: string</code></dt>
        <dd>Determines whether the selection history event may be
        merged with the previous one. When an origin starts with the
        character <code>+</code>, and the last recorded selection had
        the same origin and was similar (close
        in <a href="#option_historyEventDelay">time</a>, both
        collapsed or both non-collapsed), the new one will replace the
        old one. When it starts with <code>*</code>, it will always
        replace the previous event (if that had the same origin).
        Built-in motion uses the <code>"+move"</code> origin. User input uses the <code>"+input"</code> origin.</dd>
        <dt id="selection_bias"><code><strong>bias</strong>: number</code></dt>
        <dd>Determine the direction into which the selection endpoints
        should be adjusted when they fall inside
        an <a href="#mark_atomic">atomic</a> range. Can be either -1
        (backward) or 1 (forward). When not given, the bias will be
        based on the relative position of the old selection—the editor
        will try to move further away from that, to prevent getting
        stuck.</dd>
      </dl></dd>

      <dt id="setSelections"><code><strong>doc.setSelections</strong>(ranges: array&lt;{anchor, head}&gt;, ?primary: integer, ?options: object)</code></dt>
      <dd>Sets a new set of selections. There must be at least one
      selection in the given array. When <code>primary</code> is a
      number, it determines which selection is the primary one. When
      it is not given, the primary index is taken from the previous
      selection, or set to the last range if the previous selection
      had less ranges than the new one. Supports the same options
      as <a href="#setSelection"><code>setSelection</code></a>.</dd>
      <dt id="addSelection"><code><strong>doc.addSelection</strong>(anchor: {line, ch}, ?head: {line, ch})</code></dt>
      <dd>Adds a new selection to the existing set of selections, and
      makes it the primary selection.</dd>

      <dt id="extendSelection"><code><strong>doc.extendSelection</strong>(from: {line, ch}, ?to: {line, ch}, ?options: object)</code></dt>
      <dd>Similar
      to <a href="#setSelection"><code>setSelection</code></a>, but
      will, if shift is held or
      the <a href="#setExtending">extending</a> flag is set, move the
      head of the selection while leaving the anchor at its current
      place. <code>to</code> is optional, and can be passed to ensure
      a region (for example a word or paragraph) will end up selected
      (in addition to whatever lies between that region and the
      current anchor). When multiple selections are present, all but
      the primary selection will be dropped by this method.
      Supports the same options as <a href="#setSelection"><code>setSelection</code></a>.</dd>
      <dt id="extendSelections"><code><strong>doc.extendSelections</strong>(heads: array&lt;{line, ch}&gt;, ?options: object)</code></dt>
      <dd>An equivalent
      of <a href="#extendSelection"><code>extendSelection</code></a>
      that acts on all selections at once.</dd>
      <dt id="extendSelectionsBy"><code><strong>doc.extendSelectionsBy</strong>(f: function(range: {anchor, head}) → {line, ch}), ?options: object)</code></dt>
      <dd>Applies the given function to all existing selections, and
      calls <a href="#extendSelections"><code>extendSelections</code></a>
      on the result.</dd>
      <dt id="setExtending"><code><strong>doc.setExtending</strong>(value: boolean)</code></dt>
      <dd>Sets or clears the 'extending' flag, which acts similar to
      the shift key, in that it will cause cursor movement and calls
      to <a href="#extendSelection"><code>extendSelection</code></a>
      to leave the selection anchor in place.</dd>
      <dt id="getExtending"><code><strong>doc.getExtending</strong>() → boolean</code></dt>
      <dd>Get the value of the 'extending' flag.</dd>

      <dt id="hasFocus"><code><strong>cm.hasFocus</strong>() → boolean</code></dt>
      <dd>Tells you whether the editor currently has focus.</dd>

      <dt id="findPosH"><code><strong>cm.findPosH</strong>(start: {line, ch}, amount: integer, unit: string, visually: boolean) → {line, ch, ?hitSide: boolean}</code></dt>
      <dd>Used to find the target position for horizontal cursor
      motion. <code>start</code> is a <code>{line, ch}</code>
      object, <code>amount</code> an integer (may be negative),
      and <code>unit</code> one of the
      string <code>"char"</code>, <code>"column"</code>,
      or <code>"word"</code>. Will return a position that is produced
      by moving <code>amount</code> times the distance specified
      by <code>unit</code>. When <code>visually</code> is true, motion
      in right-to-left text will be visual rather than logical. When
      the motion was clipped by hitting the end or start of the
      document, the returned value will have a <code>hitSide</code>
      property set to true.</dd>
      <dt id="findPosV"><code><strong>cm.findPosV</strong>(start: {line, ch}, amount: integer, unit: string) → {line, ch, ?hitSide: boolean}</code></dt>
      <dd>Similar to <a href="#findPosH"><code>findPosH</code></a>,
      but used for vertical motion. <code>unit</code> may
      be <code>"line"</code> or <code>"page"</code>. The other
      arguments and the returned value have the same interpretation as
      they have in <code>findPosH</code>.</dd>

      <dt id="findWordAt"><code><strong>cm.findWordAt</strong>(pos: {line, ch}) → {anchor: {line, ch}, head: {line, ch}}</code></dt>
      <dd>Returns the start and end of the 'word' (the stretch of
      letters, whitespace, or punctuation) at the given position.</dd>
    </dl>

    <h3 id="api_configuration">Configuration methods</h3>

    <dl>
      <dt id="setOption"><code><strong>cm.setOption</strong>(option: string, value: any)</code></dt>
      <dd>Change the configuration of the editor. <code>option</code>
      should the name of an <a href="#config">option</a>,
      and <code>value</code> should be a valid value for that
      option.</dd>
      <dt id="getOption"><code><strong>cm.getOption</strong>(option: string) → any</code></dt>
      <dd>Retrieves the current value of the given option for this
      editor instance.</dd>

      <dt id="addKeyMap"><code><strong>cm.addKeyMap</strong>(map: object, bottom: boolean)</code></dt>
      <dd>Attach an additional <a href="#keymaps">key map</a> to the
      editor. This is mostly useful for addons that need to register
      some key handlers without trampling on
      the <a href="#option_extraKeys"><code>extraKeys</code></a>
      option. Maps added in this way have a higher precedence than
      the <code>extraKeys</code>
      and <a href="#option_keyMap"><code>keyMap</code></a> options,
      and between them, the maps added earlier have a lower precedence
      than those added later, unless the <code>bottom</code> argument
      was passed, in which case they end up below other key maps added
      with this method.</dd>
      <dt id="removeKeyMap"><code><strong>cm.removeKeyMap</strong>(map: object)</code></dt>
      <dd>Disable a keymap added
      with <a href="#addKeyMap"><code>addKeyMap</code></a>. Either
      pass in the key map object itself, or a string, which will be
      compared against the <code>name</code> property of the active
      key maps.</dd>

      <dt id="addOverlay"><code><strong>cm.addOverlay</strong>(mode: string|object, ?options: object)</code></dt>
      <dd>Enable a highlighting overlay. This is a stateless mini-mode
      that can be used to add extra highlighting. For example,
      the <a href="../demo/search.html">search addon</a> uses it to
      highlight the term that's currently being
      searched. <code>mode</code> can be a <a href="#option_mode">mode
      spec</a> or a mode object (an object with
      a <a href="#token"><code>token</code></a> method).
      The <code>options</code> parameter is optional. If given, it
      should be an object. Currently, only the <code>opaque</code>
      option is recognized. This defaults to off, but can be given to
      allow the overlay styling, when not <code>null</code>, to
      override the styling of the base mode entirely, instead of the
      two being applied together.</dd>
      <dt id="removeOverlay"><code><strong>cm.removeOverlay</strong>(mode: string|object)</code></dt>
      <dd>Pass this the exact value passed for the <code>mode</code>
      parameter to <a href="#addOverlay"><code>addOverlay</code></a>,
      or a string that corresponds to the <code>name</code> property of
      that value, to remove an overlay again.</dd>

      <dt id="on"><code><strong>cm.on</strong>(type: string, func: (...args))</code></dt>
      <dd>Register an event handler for the given event type (a
      string) on the editor instance. There is also
      a <code>CodeMirror.on(object, type, func)</code> version
      that allows registering of events on any object.</dd>
      <dt id="off"><code><strong>cm.off</strong>(type: string, func: (...args))</code></dt>
      <dd>Remove an event handler on the editor instance. An
      equivalent <code>CodeMirror.off(object, type,
      func)</code> also exists.</dd>
    </dl>

    <h3 id="api_doc">Document management methods</h3>

    <p id="Doc">Each editor is associated with an instance
    of <code>CodeMirror.Doc</code>, its document. A document
    represents the editor content, plus a selection, an undo history,
    and a <a href="#option_mode">mode</a>. A document can only be
    associated with a single editor at a time. You can create new
    documents by calling the <code>CodeMirror.Doc(text, mode,
    firstLineNumber)</code> constructor. The last two arguments are
    optional and can be used to set a mode for the document and make
    it start at a line number other than 0, respectively.</p>

    <dl>
      <dt id="getDoc"><code><strong>cm.getDoc</strong>() → Doc</code></dt>
      <dd>Retrieve the currently active document from an editor.</dd>
      <dt id="getEditor"><code><strong>doc.getEditor</strong>() → CodeMirror</code></dt>
      <dd>Retrieve the editor associated with a document. May
      return <code>null</code>.</dd>

      <dt id="swapDoc"><code><strong>cm.swapDoc</strong>(doc: CodeMirror.Doc) → Doc</code></dt>
      <dd>Attach a new document to the editor. Returns the old
      document, which is now no longer associated with an editor.</dd>

      <dt id="copy"><code><strong>doc.copy</strong>(copyHistory: boolean) → Doc</code></dt>
      <dd>Create an identical copy of the given doc.
      When <code>copyHistory</code> is true, the history will also be
      copied. Can not be called directly on an editor.</dd>

      <dt id="linkedDoc"><code><strong>doc.linkedDoc</strong>(options: object) → Doc</code></dt>
      <dd>Create a new document that's linked to the target document.
      Linked documents will stay in sync (changes to one are also
      applied to the other) until <a href="#unlinkDoc">unlinked</a>.
      These are the options that are supported:
        <dl>
          <dt id="linkedDoc_sharedHist"><code><strong>sharedHist</strong>: boolean</code></dt>
          <dd>When turned on, the linked copy will share an undo
          history with the original. Thus, something done in one of
          the two can be undone in the other, and vice versa.</dd>
          <dt id="linkedDoc_from"><code><strong>from</strong>: integer</code></dt>
          <dt id="linkedDoc_to"><code><strong>to</strong>: integer</code></dt>
          <dd>Can be given to make the new document a subview of the
          original. Subviews only show a given range of lines. Note
          that line coordinates inside the subview will be consistent
          with those of the parent, so that for example a subview
          starting at line 10 will refer to its first line as line 10,
          not 0.</dd>
          <dt id="linkedDoc_mode"><code><strong>mode</strong>: string|object</code></dt>
          <dd>By default, the new document inherits the mode of the
          parent. This option can be set to
          a <a href="#option_mode">mode spec</a> to give it a
          different mode.</dd>
        </dl></dd>
      <dt id="unlinkDoc"><code><strong>doc.unlinkDoc</strong>(doc: CodeMirror.Doc)</code></dt>
      <dd>Break the link between two documents. After calling this,
      changes will no longer propagate between the documents, and, if
      they had a shared history, the history will become
      separate.</dd>
      <dt id="iterLinkedDocs"><code><strong>doc.iterLinkedDocs</strong>(function: (doc: CodeMirror.Doc, sharedHist: boolean))</code></dt>
      <dd>Will call the given function for all documents linked to the
      target document. It will be passed two arguments, the linked document
      and a boolean indicating whether that document shares history
      with the target.</dd>
    </dl>

    <h3 id="api_history">History-related methods</h3>

    <dl>
      <dt id="undo"><code><strong>doc.undo</strong>()</code></dt>
      <dd>Undo one edit (if any undo events are stored).</dd>
      <dt id="redo"><code><strong>doc.redo</strong>()</code></dt>
      <dd>Redo one undone edit.</dd>

      <dt id="undoSelection"><code><strong>doc.undoSelection</strong>()</code></dt>
      <dd>Undo one edit or selection change.</dd>
      <dt id="redoSelection"><code><strong>doc.redoSelection</strong>()</code></dt>
      <dd>Redo one undone edit or selection change.</dd>

      <dt id="historySize"><code><strong>doc.historySize</strong>() → {undo: integer, redo: integer}</code></dt>
      <dd>Returns an object with <code>{undo, redo}</code> properties,
      both of which hold integers, indicating the amount of stored
      undo and redo operations.</dd>
      <dt id="clearHistory"><code><strong>doc.clearHistory</strong>()</code></dt>
      <dd>Clears the editor's undo history.</dd>
      <dt id="getHistory"><code><strong>doc.getHistory</strong>() → object</code></dt>
      <dd>Get a (JSON-serializeable) representation of the undo history.</dd>
      <dt id="setHistory"><code><strong>doc.setHistory</strong>(history: object)</code></dt>
      <dd>Replace the editor's undo history with the one provided,
      which must be a value as returned
      by <a href="#getHistory"><code>getHistory</code></a>. Note that
      this will have entirely undefined results if the editor content
      isn't also the same as it was when <code>getHistory</code> was
      called.</dd>
    </dl>

    <h3 id="api_marker">Text-marking methods</h3>

    <dl>
      <dt id="markText"><code><strong>doc.markText</strong>(from: {line, ch}, to: {line, ch}, ?options: object) → TextMarker</code></dt>
      <dd>Can be used to mark a range of text with a specific CSS
      class name. <code>from</code> and <code>to</code> should
      be <code>{line, ch}</code> objects. The <code>options</code>
      parameter is optional. When given, it should be an object that
      may contain the following configuration options:
      <dl>
        <dt id="mark_className"><code><strong>className</strong>: string</code></dt>
        <dd>Assigns a CSS class to the marked stretch of text.</dd>
        <dt id="mark_inclusiveLeft"><code><strong>inclusiveLeft</strong>: boolean</code></dt>
        <dd>Determines whether
        text inserted on the left of the marker will end up inside
        or outside of it.</dd>
        <dt id="mark_inclusiveRight"><code><strong>inclusiveRight</strong>: boolean</code></dt>
        <dd>Like <code>inclusiveLeft</code>,
        but for the right side.</dd>
        <dt id="mark_atomic"><code><strong>atomic</strong>: boolean</code></dt>
        <dd>Atomic ranges act as a single unit when cursor movement is
        concerned—i.e. it is impossible to place the cursor inside of
        them. In atomic ranges, <code>inclusiveLeft</code>
        and <code>inclusiveRight</code> have a different meaning—they
        will prevent the cursor from being placed respectively
        directly before and directly after the range.</dd>
        <dt id="mark_collapsed"><code><strong>collapsed</strong>: boolean</code></dt>
        <dd>Collapsed ranges do not show up in the display. Setting a
        range to be collapsed will automatically make it atomic.</dd>
        <dt id="mark_clearOnEnter"><code><strong>clearOnEnter</strong>: boolean</code></dt>
        <dd>When enabled, will cause the mark to clear itself whenever
        the cursor enters its range. This is mostly useful for
        text-replacement widgets that need to 'snap open' when the
        user tries to edit them. The
        <a href="#event_clear"><code>"clear"</code></a> event
        fired on the range handle can be used to be notified when this
        happens.</dd>
        <dt id="mark_clearWhenEmpty"><code><strong>clearWhenEmpty</strong>: boolean</code></dt>
        <dd>Determines whether the mark is automatically cleared when
        it becomes empty. Default is true.</dd>
        <dt id="mark_replacedWith"><code><strong>replacedWith</strong>: Element</code></dt>
        <dd>Use a given node to display this range. Implies both
        collapsed and atomic. The given DOM node <em>must</em> be an
        inline element (as opposed to a block element).</dd>
        <dt><code><strong>handleMouseEvents</strong>: boolean</code></dt>
        <dd>When <code>replacedWith</code> is given, this determines
        whether the editor will capture mouse and drag events
        occurring in this widget. Default is false—the events will be
        left alone for the default browser handler, or specific
        handlers on the widget, to capture.</dd>
        <dt id="mark_readOnly"><code><strong>readOnly</strong>: boolean</code></dt>
        <dd>A read-only span can, as long as it is not cleared, not be
        modified except by
        calling <a href="#setValue"><code>setValue</code></a> to reset
        the whole document. <em>Note:</em> adding a read-only span
        currently clears the undo history of the editor, because
        existing undo events being partially nullified by read-only
        spans would corrupt the history (in the current
        implementation).</dd>
        <dt id="mark_addToHistory"><code><strong>addToHistory</strong>: boolean</code></dt>
        <dd>When set to true (default is false), adding this marker
        will create an event in the undo history that can be
        individually undone (clearing the marker).</dd>
        <dt id="mark_startStyle"><code><strong>startStyle</strong>: string</code></dt><dd>Can be used to specify
        an extra CSS class to be applied to the leftmost span that
        is part of the marker.</dd>
        <dt id="mark_endStyle"><code><strong>endStyle</strong>: string</code></dt><dd>Equivalent
        to <code>startStyle</code>, but for the rightmost span.</dd>
        <dt id="mark_css"><code><strong>css</strong>: string</code></dt>
        <dd>A string of CSS to be applied to the covered text. For example <code>"color: #fe3"</code>.</dd>
        <dt id="mark_title"><code><strong>title</strong>:
        string</code></dt><dd>When given, will give the nodes created
        for this span a HTML <code>title</code> attribute with the
        given value.</dd>
        <dt id="mark_shared"><code><strong>shared</strong>: boolean</code></dt><dd>When the
        target document is <a href="#linkedDoc">linked</a> to other
        documents, you can set <code>shared</code> to true to make the
        marker appear in all documents. By default, a marker appears
        only in its target document.</dd>
      </dl>
      The method will return an object that represents the marker
      (with constructor <code>CodeMirror.TextMarker</code>), which
      exposes three methods:
      <code><strong>clear</strong>()</code>, to remove the mark,
      <code><strong>find</strong>()</code>, which returns
      a <code>{from, to}</code> object (both holding document
      positions), indicating the current position of the marked range,
      or <code>undefined</code> if the marker is no longer in the
      document, and finally <code><strong>changed</strong>()</code>,
      which you can call if you've done something that might change
      the size of the marker (for example changing the content of
      a <a href="#mark_replacedWith"><code>replacedWith</code></a>
      node), and want to cheaply update the display.</dd>

      <dt id="setBookmark"><code><strong>doc.setBookmark</strong>(pos: {line, ch}, ?options: object) → TextMarker</code></dt>
      <dd>Inserts a bookmark, a handle that follows the text around it
      as it is being edited, at the given position. A bookmark has two
      methods <code>find()</code> and <code>clear()</code>. The first
      returns the current position of the bookmark, if it is still in
      the document, and the second explicitly removes the bookmark.
      The options argument is optional. If given, the following
      properties are recognized:
      <dl>
        <dt><code><strong>widget</strong>: Element</code></dt><dd>Can be used to display a DOM
        node at the current location of the bookmark (analogous to
        the <a href="#mark_replacedWith"><code>replacedWith</code></a>
        option to <a href="#markText"><code>markText</code></a>).</dd>
        <dt><code><strong>insertLeft</strong>: boolean</code></dt><dd>By default, text typed
        when the cursor is on top of the bookmark will end up to the
        right of the bookmark. Set this option to true to make it go
        to the left instead.</dd>
        <dt><code><strong>shared</strong>: boolean</code></dt><dd>See
        the corresponding <a href="#mark_shared">option</a>
        to <code>markText</code>.</dd>
        <dt><code><strong>handleMouseEvents</strong>: boolean</code></dt>
        <dd>As with <a href="#markText"><code>markText</code></a>,
        this determines whether mouse events on the widget inserted
        for this bookmark are handled by CodeMirror. The default is
        false.</dd>
      </dl></dd>

      <dt id="findMarks"><code><strong>doc.findMarks</strong>(from: {line, ch}, to: {line, ch}) → array&lt;TextMarker&gt;</code></dt>
      <dd>Returns an array of all the bookmarks and marked ranges
      found between the given positions.</dd>
      <dt id="findMarksAt"><code><strong>doc.findMarksAt</strong>(pos: {line, ch}) → array&lt;TextMarker&gt;</code></dt>
      <dd>Returns an array of all the bookmarks and marked ranges
      present at the given position.</dd>
      <dt id="getAllMarks"><code><strong>doc.getAllMarks</strong>() → array&lt;TextMarker&gt;</code></dt>
      <dd>Returns an array containing all marked ranges in the document.</dd>
    </dl>

    <h3 id="api_decoration">Widget, gutter, and decoration methods</h3>

    <dl>
      <dt id="setGutterMarker"><code><strong>cm.setGutterMarker</strong>(line: integer|LineHandle, gutterID: string, value: Element) → LineHandle</code></dt>
      <dd>Sets the gutter marker for the given gutter (identified by
      its CSS class, see
      the <a href="#option_gutters"><code>gutters</code></a> option)
      to the given value. Value can be either <code>null</code>, to
      clear the marker, or a DOM element, to set it. The DOM element
      will be shown in the specified gutter next to the specified
      line.</dd>

      <dt id="clearGutter"><code><strong>cm.clearGutter</strong>(gutterID: string)</code></dt>
      <dd>Remove all gutter markers in
      the <a href="#option_gutters">gutter</a> with the given ID.</dd>

      <dt id="addLineClass"><code><strong>doc.addLineClass</strong>(line: integer|LineHandle, where: string, class: string) → LineHandle</code></dt>
      <dd>Set a CSS class name for the given line. <code>line</code>
      can be a number or a line handle. <code>where</code> determines
      to which element this class should be applied, can can be one
      of <code>"text"</code> (the text element, which lies in front of
      the selection), <code>"background"</code> (a background element
      that will be behind the selection), <code>"gutter"</code> (the
      line's gutter space), or <code>"wrap"</code> (the wrapper node
      that wraps all of the line's elements, including gutter
      elements). <code>class</code> should be the name of the class to
      apply.</dd>

      <dt id="removeLineClass"><code><strong>doc.removeLineClass</strong>(line: integer|LineHandle, where: string, class: string) → LineHandle</code></dt>
      <dd>Remove a CSS class from a line. <code>line</code> can be a
      line handle or number. <code>where</code> should be one
      of <code>"text"</code>, <code>"background"</code>,
      or <code>"wrap"</code>
      (see <a href="#addLineClass"><code>addLineClass</code></a>). <code>class</code>
      can be left off to remove all classes for the specified node, or
      be a string to remove only a specific class.</dd>

      <dt id="lineInfo"><code><strong>cm.lineInfo</strong>(line: integer|LineHandle) → object</code></dt>
      <dd>Returns the line number, text content, and marker status of
      the given line, which can be either a number or a line handle.
      The returned object has the structure <code>{line, handle, text,
      gutterMarkers, textClass, bgClass, wrapClass, widgets}</code>,
      where <code>gutterMarkers</code> is an object mapping gutter IDs
      to marker elements, and <code>widgets</code> is an array
      of <a href="#addLineWidget">line widgets</a> attached to this
      line, and the various class properties refer to classes added
      with <a href="#addLineClass"><code>addLineClass</code></a>.</dd>

      <dt id="addWidget"><code><strong>cm.addWidget</strong>(pos: {line, ch}, node: Element, scrollIntoView: boolean)</code></dt>
      <dd>Puts <code>node</code>, which should be an absolutely
      positioned DOM node, into the editor, positioned right below the
      given <code>{line, ch}</code> position.
      When <code>scrollIntoView</code> is true, the editor will ensure
      that the entire node is visible (if possible). To remove the
      widget again, simply use DOM methods (move it somewhere else, or
      call <code>removeChild</code> on its parent).</dd>

      <dt id="addLineWidget"><code><strong>doc.addLineWidget</strong>(line: integer|LineHandle, node: Element, ?options: object) → LineWidget</code></dt>
      <dd>Adds a line widget, an element shown below a line, spanning
      the whole of the editor's width, and moving the lines below it
      downwards. <code>line</code> should be either an integer or a
      line handle, and <code>node</code> should be a DOM node, which
      will be displayed below the given line. <code>options</code>,
      when given, should be an object that configures the behavior of
      the widget. The following options are supported (all default to
      false):
        <dl>
          <dt><code><strong>coverGutter</strong>: boolean</code></dt>
          <dd>Whether the widget should cover the gutter.</dd>
          <dt><code><strong>noHScroll</strong>: boolean</code></dt>
          <dd>Whether the widget should stay fixed in the face of
          horizontal scrolling.</dd>
          <dt><code><strong>above</strong>: boolean</code></dt>
          <dd>Causes the widget to be placed above instead of below
          the text of the line.</dd>
          <dt><code><strong>handleMouseEvents</strong>: boolean</code></dt>
          <dd>Determines whether the editor will capture mouse and
          drag events occurring in this widget. Default is false—the
          events will be left alone for the default browser handler,
          or specific handlers on the widget, to capture.</dd>
          <dt><code><strong>insertAt</strong>: integer</code></dt>
          <dd>By default, the widget is added below other widgets for
          the line. This option can be used to place it at a different
          position (zero for the top, N to put it after the Nth other
          widget). Note that this only has effect once, when the
          widget is created.
        </dl>
      Note that the widget node will become a descendant of nodes with
      CodeMirror-specific CSS classes, and those classes might in some
      cases affect it. This method returns an object that represents
      the widget placement. It'll have a <code>line</code> property
      pointing at the line handle that it is associated with, and the following methods:
        <dl>
          <dt id="widget_clear"><code><strong>clear</strong>()</code></dt><dd>Removes the widget.</dd>
          <dt id="widget_changed"><code><strong>changed</strong>()</code></dt><dd>Call
          this if you made some change to the widget's DOM node that
          might affect its height. It'll force CodeMirror to update
          the height of the line that contains the widget.</dd>
        </dl>
      </dd>
    </dl>

    <h3 id="api_sizing">Sizing, scrolling and positioning methods</h3>

    <dl>
      <dt id="setSize"><code><strong>cm.setSize</strong>(width: number|string, height: number|string)</code></dt>
      <dd>Programmatically set the size of the editor (overriding the
      applicable <a href="#css-resize">CSS
      rules</a>). <code>width</code> and <code>height</code>
      can be either numbers (interpreted as pixels) or CSS units
      (<code>"100%"</code>, for example). You can
      pass <code>null</code> for either of them to indicate that that
      dimension should not be changed.</dd>

      <dt id="scrollTo"><code><strong>cm.scrollTo</strong>(x: number, y: number)</code></dt>
      <dd>Scroll the editor to a given (pixel) position. Both
      arguments may be left as <code>null</code>
      or <code>undefined</code> to have no effect.</dd>
      <dt id="getScrollInfo"><code><strong>cm.getScrollInfo</strong>() → {left, top, width, height, clientWidth, clientHeight}</code></dt>
      <dd>Get an <code>{left, top, width, height, clientWidth,
      clientHeight}</code> object that represents the current scroll
      position, the size of the scrollable area, and the size of the
      visible area (minus scrollbars).</dd>
      <dt id="scrollIntoView"><code><strong>cm.scrollIntoView</strong>(what: {line, ch}|{left, top, right, bottom}|{from, to}|null, ?margin: number)</code></dt>
      <dd>Scrolls the given position into view. <code>what</code> may
      be <code>null</code> to scroll the cursor into view,
      a <code>{line, ch}</code> position to scroll a character into
      view, a <code>{left, top, right, bottom}</code> pixel range (in
      editor-local coordinates), or a range <code>{from, to}</code>
      containing either two character positions or two pixel squares.
      The <code>margin</code> parameter is optional. When given, it
      indicates the amount of vertical pixels around the given area
      that should be made visible as well.</dd>

      <dt id="cursorCoords"><code><strong>cm.cursorCoords</strong>(where: boolean|{line, ch}, mode: string) → {left, top, bottom}</code></dt>
      <dd>Returns an <code>{left, top, bottom}</code> object
      containing the coordinates of the cursor position.
      If <code>mode</code> is <code>"local"</code>, they will be
      relative to the top-left corner of the editable document. If it
      is <code>"page"</code> or not given, they are relative to the
      top-left corner of the page. If <code>mode</code>
      is <code>"window"</code>, the coordinates are relative to the
      top-left corner of the currently visible (scrolled)
      window. <code>where</code> can be a boolean indicating whether
      you want the start (<code>true</code>) or the end
      (<code>false</code>) of the selection, or, if a <code>{line,
      ch}</code> object is given, it specifies the precise position at
      which you want to measure.</dd>
      <dt id="charCoords"><code><strong>cm.charCoords</strong>(pos: {line, ch}, ?mode: string) → {left, right, top, bottom}</code></dt>
      <dd>Returns the position and dimensions of an arbitrary
      character. <code>pos</code> should be a <code>{line, ch}</code>
      object. This differs from <code>cursorCoords</code> in that
      it'll give the size of the whole character, rather than just the
      position that the cursor would have when it would sit at that
      position.</dd>
      <dt id="coordsChar"><code><strong>cm.coordsChar</strong>(object: {left, top}, ?mode: string) → {line, ch}</code></dt>
      <dd>Given an <code>{left, top}</code> object, returns
      the <code>{line, ch}</code> position that corresponds to it. The
      optional <code>mode</code> parameter determines relative to what
      the coordinates are interpreted. It may
      be <code>"window"</code>, <code>"page"</code> (the default),
      or <code>"local"</code>.</dd>
      <dt id="lineAtHeight"><code><strong>cm.lineAtHeight</strong>(height: number, ?mode: string) → number</code></dt>
      <dd>Computes the line at the given pixel
      height. <code>mode</code> can be one of the same strings
      that <a href="#coordsChar"><code>coordsChar</code></a>
      accepts.</dd>
      <dt id="heightAtLine"><code><strong>cm.heightAtLine</strong>(line: integer|LineHandle, ?mode: string) → number</code></dt>
      <dd>Computes the height of the top of a line, in the coordinate
      system specified by <code>mode</code>
      (see <a href="#coordsChar"><code>coordsChar</code></a>), which
      defaults to <code>"page"</code>. When a line below the bottom of
      the document is specified, the returned value is the bottom of
      the last line in the document.</dd>
      <dt id="defaultTextHeight"><code><strong>cm.defaultTextHeight</strong>() → number</code></dt>
      <dd>Returns the line height of the default font for the editor.</dd>
      <dt id="defaultCharWidth"><code><strong>cm.defaultCharWidth</strong>() → number</code></dt>
      <dd>Returns the pixel width of an 'x' in the default font for
      the editor. (Note that for non-monospace fonts, this is mostly
      useless, and even for monospace fonts, non-ascii characters
      might have a different width).</dd>

      <dt id="getViewport"><code><strong>cm.getViewport</strong>() → {from: number, to: number}</code></dt>
      <dd>Returns a <code>{from, to}</code> object indicating the
      start (inclusive) and end (exclusive) of the currently rendered
      part of the document. In big documents, when most content is
      scrolled out of view, CodeMirror will only render the visible
      part, and a margin around it. See also
      the <a href="#event_viewportChange"><code>viewportChange</code></a>
      event.</dd>

      <dt id="refresh"><code><strong>cm.refresh</strong>()</code></dt>
      <dd>If your code does something to change the size of the editor
      element (window resizes are already listened for), or unhides
      it, you should probably follow up by calling this method to
      ensure CodeMirror is still looking as intended. See also
      the <a href="#addon_autorefresh">autorefresh addon</a>.</dd>
    </dl>

    <h3 id="api_mode">Mode, state, and token-related methods</h3>

    <p>When writing language-aware functionality, it can often be
    useful to hook into the knowledge that the CodeMirror language
    mode has. See <a href="#modeapi">the section on modes</a> for a
    more detailed description of how these work.</p>

    <dl>
      <dt id="getMode"><code><strong>doc.getMode</strong>() → object</code></dt>
      <dd>Gets the (outer) mode object for the editor. Note that this
      is distinct from <code>getOption("mode")</code>, which gives you
      the mode specification, rather than the resolved, instantiated
      <a href="#defineMode">mode object</a>.</dd>

      <dt id="getModeAt"><code><strong>cm.getModeAt</strong>(pos: {line, ch}) → object</code></dt>
      <dd>Gets the inner mode at a given position. This will return
      the same as <a href="#getMode"><code>getMode</code></a> for
      simple modes, but will return an inner mode for nesting modes
      (such as <code>htmlmixed</code>).</dd>

      <dt id="getTokenAt"><code><strong>cm.getTokenAt</strong>(pos: {line, ch}, ?precise: boolean) → object</code></dt>
      <dd>Retrieves information about the token the current mode found
      before the given position (a <code>{line, ch}</code> object). The
      returned object has the following properties:
      <dl>
        <dt><code><strong>start</strong></code></dt><dd>The character (on the given line) at which the token starts.</dd>
        <dt><code><strong>end</strong></code></dt><dd>The character at which the token ends.</dd>
        <dt><code><strong>string</strong></code></dt><dd>The token's string.</dd>
        <dt><code><strong>type</strong></code></dt><dd>The token type the mode assigned
        to the token, such as <code>"keyword"</code>
        or <code>"comment"</code> (may also be null).</dd>
        <dt><code><strong>state</strong></code></dt><dd>The mode's state at the end of this token.</dd>
      </dl>
      If <code>precise</code> is true, the token will be guaranteed to be accurate based on recent edits. If false or
      not specified, the token will use cached state information, which will be faster but might not be accurate if
      edits were recently made and highlighting has not yet completed.
      </dd>

      <dt id="getLineTokens"><code><strong>cm.getLineTokens</strong>(line: integer, ?precise: boolean) → array&lt;{start, end, string, type, state}&gt;</code></dt>
      <dd>This is similar
      to <a href="#getTokenAt"><code>getTokenAt</code></a>, but
      collects all tokens for a given line into an array. It is much
      cheaper than repeatedly calling <code>getTokenAt</code>, which
      re-parses the part of the line before the token for every call.</dd>

      <dt id="getTokenTypeAt"><code><strong>cm.getTokenTypeAt</strong>(pos: {line, ch}) → string</code></dt>
      <dd>This is a (much) cheaper version
      of <a href="#getTokenAt"><code>getTokenAt</code></a> useful for
      when you just need the type of the token at a given position,
      and no other information. Will return <code>null</code> for
      unstyled tokens, and a string, potentially containing multiple
      space-separated style names, otherwise.</dd>

      <dt id="getHelpers"><code><strong>cm.getHelpers</strong>(pos: {line, ch}, type: string) → array&lt;helper&gt;</code></dt>
      <dd>Fetch the set of applicable helper values for the given
      position. Helpers provide a way to look up functionality
      appropriate for a mode. The <code>type</code> argument provides
      the helper namespace (see
      <a href="#registerHelper"><code>registerHelper</code></a>), in
      which the values will be looked up. When the mode itself has a
      property that corresponds to the <code>type</code>, that
      directly determines the keys that are used to look up the helper
      values (it may be either a single string, or an array of
      strings). Failing that, the mode's <code>helperType</code>
      property and finally the mode's name are used.</dd>
      <dd>For example, the JavaScript mode has a
      property <code>fold</code> containing <code>"brace"</code>. When
      the <code>brace-fold</code> addon is loaded, that defines a
      helper named <code>brace</code> in the <code>fold</code>
      namespace. This is then used by
      the <a href="#addon_foldcode"><code>foldcode</code></a> addon to
      figure out that it can use that folding function to fold
      JavaScript code.</dd>
      <dd>When any <a href="#registerGlobalHelper">'global'</a>
      helpers are defined for the given namespace, their predicates
      are called on the current mode and editor, and all those that
      declare they are applicable will also be added to the array that
      is returned.</dd>

      <dt id="getHelper"><code><strong>cm.getHelper</strong>(pos: {line, ch}, type: string) → helper</code></dt>
      <dd>Returns the first applicable helper value.
      See <a href="#getHelpers"><code>getHelpers</code></a>.</dd>

      <dt id="getStateAfter"><code><strong>cm.getStateAfter</strong>(?line: integer, ?precise: boolean) → object</code></dt>
      <dd>Returns the mode's parser state, if any, at the end of the
      given line number. If no line number is given, the state at the
      end of the document is returned. This can be useful for storing
      parsing errors in the state, or getting other kinds of
      contextual information for a line. <code>precise</code> is defined
      as in <code>getTokenAt()</code>.</dd>
    </dl>

    <h3 id="api_misc">Miscellaneous methods</h3>

    <dl>
      <dt id="operation"><code><strong>cm.operation</strong>(func: () → any) → any</code></dt>
      <dd>CodeMirror internally buffers changes and only updates its
      DOM structure after it has finished performing some operation.
      If you need to perform a lot of operations on a CodeMirror
      instance, you can call this method with a function argument. It
      will call the function, buffering up all changes, and only doing
      the expensive update after the function returns. This can be a
      lot faster. The return value from this method will be the return
      value of your function.</dd>

      <dt id="indentLine"><code><strong>cm.indentLine</strong>(line: integer, ?dir: string|integer)</code></dt>
      <dd>Adjust the indentation of the given line. The second
      argument (which defaults to <code>"smart"</code>) may be one of:
        <dl>
          <dt><code><strong>"prev"</strong></code></dt>
          <dd>Base indentation on the indentation of the previous line.</dd>
          <dt><code><strong>"smart"</strong></code></dt>
          <dd>Use the mode's smart indentation if available, behave
          like <code>"prev"</code> otherwise.</dd>
          <dt><code><strong>"add"</strong></code></dt>
          <dd>Increase the indentation of the line by
          one <a href="#option_indentUnit">indent unit</a>.</dd>
          <dt><code><strong>"subtract"</strong></code></dt>
          <dd>Reduce the indentation of the line.</dd>
          <dt><code><strong>&lt;integer></strong></code></dt>
          <dd>Add (positive number) or reduce (negative number) the
          indentation by the given amount of spaces.</dd>
        </dl></dd>

      <dt id="toggleOverwrite"><code><strong>cm.toggleOverwrite</strong>(?value: boolean)</code></dt>
      <dd>Switches between overwrite and normal insert mode (when not
      given an argument), or sets the overwrite mode to a specific
      state (when given an argument).</dd>

      <dt id="isReadOnly"><code><strong>cm.isReadOnly</strong>() → boolean</code></dt>
      <dd>Tells you whether the editor's content can be edited by the
      user.</dd>

      <dt id="lineSeparator"><code><strong>doc.lineSeparator</strong>()</code></dt>
      <dd>Returns the preferred line separator string for this
      document, as per the <a href="#option_lineSeparator">option</a>
      by the same name. When that option is <code>null</code>, the
      string <code>"\n"</code> is returned.</dd>

      <dt id="execCommand"><code><strong>cm.execCommand</strong>(name: string)</code></dt>
      <dd>Runs the <a href="#commands">command</a> with the given name on the editor.</dd>

      <dt id="posFromIndex"><code><strong>doc.posFromIndex</strong>(index: integer) → {line, ch}</code></dt>
      <dd>Calculates and returns a <code>{line, ch}</code> object for a
      zero-based <code>index</code> who's value is relative to the start of the
      editor's text. If the <code>index</code> is out of range of the text then
      the returned object is clipped to start or end of the text
      respectively.</dd>
      <dt id="indexFromPos"><code><strong>doc.indexFromPos</strong>(object: {line, ch}) → integer</code></dt>
      <dd>The reverse of <a href="#posFromIndex"><code>posFromIndex</code></a>.</dd>

      <dt id="focus"><code><strong>cm.focus</strong>()</code></dt>
      <dd>Give the editor focus.</dd>

      <dt id="getInputField"><code><strong>cm.getInputField</strong>() → Element</code></dt>
      <dd>Returns the input field for the editor. Will be a textarea
      or an editable div, depending on the value of
      the <a href="#option_inputStyle"><code>inputStyle</code></a>
      option.</dd>
      <dt id="getWrapperElement"><code><strong>cm.getWrapperElement</strong>() → Element</code></dt>
      <dd>Returns the DOM node that represents the editor, and
      controls its size. Remove this from your tree to delete an
      editor instance.</dd>
      <dt id="getScrollerElement"><code><strong>cm.getScrollerElement</strong>() → Element</code></dt>
      <dd>Returns the DOM node that is responsible for the scrolling
      of the editor.</dd>
      <dt id="getGutterElement"><code><strong>cm.getGutterElement</strong>() → Element</code></dt>
      <dd>Fetches the DOM node that contains the editor gutters.</dd>
    </dl>

    <h3 id="api_static">Static properties</h3>
    <p>The <code>CodeMirror</code> object itself provides
    several useful properties.</p>

    <dl>
      <dt id="version"><code><strong>CodeMirror.version</strong>: string</code></dt>
      <dd>It contains a string that indicates the version of the
      library. This is a triple of
      integers <code>"major.minor.patch"</code>,
      where <code>patch</code> is zero for releases, and something
      else (usually one) for dev snapshots.</dd>

      <dt id="fromTextArea"><code><strong>CodeMirror.fromTextArea</strong>(textArea: TextAreaElement, ?config: object)</code></dt>
      <dd>
        The method provides another way to initialize an editor. It
        takes a textarea DOM node as first argument and an optional
        configuration object as second. It will replace the textarea
        with a CodeMirror instance, and wire up the form of that
        textarea (if any) to make sure the editor contents are put
        into the textarea when the form is submitted. The text in the
        textarea will provide the content for the editor. A CodeMirror
        instance created this way has three additional methods:
        <dl>
          <dt id="save"><code><strong>cm.save</strong>()</code></dt>
          <dd>Copy the content of the editor into the textarea.</dd>

          <dt id="toTextArea"><code><strong>cm.toTextArea</strong>()</code></dt>
          <dd>Remove the editor, and restore the original textarea (with
          the editor's current content).</dd>

          <dt id="getTextArea"><code><strong>cm.getTextArea</strong>() → TextAreaElement</code></dt>
          <dd>Returns the textarea that the instance was based on.</dd>
        </dl>
      </dd>

      <dt id="defaults"><code><strong>CodeMirror.defaults</strong>: object</code></dt>
      <dd>An object containing default values for
      all <a href="#config">options</a>. You can assign to its
      properties to modify defaults (though this won't affect editors
      that have already been created).</dd>

      <dt id="defineExtension"><code><strong>CodeMirror.defineExtension</strong>(name: string, value: any)</code></dt>
      <dd>If you want to define extra methods in terms of the
      CodeMirror API, it is possible to
      use <code>defineExtension</code>. This will cause the given
      value (usually a method) to be added to all CodeMirror instances
      created from then on.</dd>

      <dt id="defineDocExtension"><code><strong>CodeMirror.defineDocExtension</strong>(name: string, value: any)</code></dt>
      <dd>Like <a href="#defineExtenstion"><code>defineExtension</code></a>,
      but the method will be added to the interface
      for <a href="#Doc"><code>Doc</code></a> objects instead.</dd>

      <dt id="defineOption"><code><strong>CodeMirror.defineOption</strong>(name: string,
      default: any, updateFunc: function)</code></dt>
      <dd>Similarly, <code>defineOption</code> can be used to define new options for
      CodeMirror. The <code>updateFunc</code> will be called with the
      editor instance and the new value when an editor is initialized,
      and whenever the option is modified
      through <a href="#setOption"><code>setOption</code></a>.</dd>

      <dt id="defineInitHook"><code><strong>CodeMirror.defineInitHook</strong>(func: function)</code></dt>
      <dd>If your extension just needs to run some
      code whenever a CodeMirror instance is initialized,
      use <code>CodeMirror.defineInitHook</code>. Give it a function as
      its only argument, and from then on, that function will be called
      (with the instance as argument) whenever a new CodeMirror instance
      is initialized.</dd>

      <dt id="registerHelper"><code><strong>CodeMirror.registerHelper</strong>(type: string, name: string, value: helper)</code></dt>
      <dd>Registers a helper value with the given <code>name</code> in
      the given namespace (<code>type</code>). This is used to define
      functionality that may be looked up by mode. Will create (if it
      doesn't already exist) a property on the <code>CodeMirror</code>
      object for the given <code>type</code>, pointing to an object
      that maps names to values. I.e. after
      doing <code>CodeMirror.registerHelper("hint", "foo",
      myFoo)</code>, the value <code>CodeMirror.hint.foo</code> will
      point to <code>myFoo</code>.</dd>

      <dt id="registerGlobalHelper"><code><strong>CodeMirror.registerGlobalHelper</strong>(type: string, name: string, predicate: fn(mode, CodeMirror), value: helper)</code></dt>
      <dd>Acts
      like <a href="#registerHelper"><code>registerHelper</code></a>,
      but also registers this helper as 'global', meaning that it will
      be included by <a href="#getHelpers"><code>getHelpers</code></a>
      whenever the given <code>predicate</code> returns true when
      called with the local mode and editor.</dd>

      <dt id="Pos"><code><strong>CodeMirror.Pos</strong>(line: integer, ?ch: integer)</code></dt>
      <dd>A constructor for the <code>{line, ch}</code> objects that
      are used to represent positions in editor documents.</dd>

      <dt id="changeEnd"><code><strong>CodeMirror.changeEnd</strong>(change: object) → {line, ch}</code></dt>
      <dd>Utility function that computes an end position from a change
      (an object with <code>from</code>, <code>to</code>,
      and <code>text</code> properties, as passed to
      various <a href="#event_change">event handlers</a>). The
      returned position will be the end of the changed
      range, <em>after</em> the change is applied.</dd>
    </dl>
</section>

<section id=addons>
    <h2 id="addons">Addons</h2>

    <p>The <code>addon</code> directory in the distribution contains a
    number of reusable components that implement extra editor
    functionality (on top of extension functions
    like <a href="#defineOption"><code>defineOption</code></a>, <a href="#defineExtension"><code>defineExtension</code></a>,
    and <a href="#registerHelper"><code>registerHelper</code></a>). In
    brief, they are:</p>

    <dl>
      <dt id="addon_dialog"><a href="../addon/dialog/dialog.js"><code>dialog/dialog.js</code></a></dt>
      <dd>Provides a very simple way to query users for text input.
      Adds the <strong><code>openDialog(template, callback, options) →
      closeFunction</code></strong> method to CodeMirror instances,
      which can be called with an HTML fragment or a detached DOM
      node that provides the prompt (should include an <code>input</code>
      or <code>button</code> tag), and a callback function that is called
      when the user presses enter. It returns a function <code>closeFunction</code>
      which, if called, will close the dialog immediately.
      <strong><code>openDialog</code></strong> takes the following options:
        <dl>
          <dt><code><strong>closeOnEnter</strong></code>:</dt>
          <dd>If true, the dialog will be closed when the user presses
          enter in the input. Defaults to <code>true</code>.</dd>
          <dt><code><strong>onKeyDown</strong></code>:</dt>
          <dd>An event handler of the signature <code>(event, value, closeFunction)</code>
          that will be called whenever <code>keydown</code> fires in the
          dialog's input. If your callback returns <code>true</code>,
          the dialog will not do any further processing of the event.</dd>
          <dt><code><strong>onKeyUp</strong></code>:</dt>
          <dd>Same as <code>onKeyDown</code> but for the
          <code>keyup</code> event.</dd>
          <dt><code><strong>onInput</strong></code>:</dt>
          <dd>Same as <code>onKeyDown</code> but for the
          <code>input</code> event.</dd>
          <dt><code><strong>onClose</strong></code>:</dt>
          <dd>A callback of the signature <code>(dialogInstance)</code>
          that will be called after the dialog has been closed and
          removed from the DOM. No return value.</dd>
        </dl>

      <p>Also adds an <strong><code>openNotification(template, options) →
      closeFunction</code></strong> function that simply shows an HTML
      fragment as a notification at the top of the editor. It takes a
      single option: <code>duration</code>, the amount of time after
      which the notification will be automatically closed. If <code>
      duration</code> is zero, the dialog will not be closed automatically.</p>

      <p>Depends on <code>addon/dialog/dialog.css</code>.</p></dd>

      <dt id="addon_searchcursor"><a href="../addon/search/searchcursor.js"><code>search/searchcursor.js</code></a></dt>
      <dd>Adds the <code>getSearchCursor(query, start, caseFold) →
      cursor</code> method to CodeMirror instances, which can be used
      to implement search/replace functionality. <code>query</code>
      can be a regular expression or a string (only strings will match
      across lines—if they contain newlines). <code>start</code>
      provides the starting position of the search. It can be
      a <code>{line, ch}</code> object, or can be left off to default
      to the start of the document. <code>caseFold</code> is only
      relevant when matching a string. It will cause the search to be
      case-insensitive. A search cursor has the following methods:
        <dl>
          <dt><code><strong>findNext</strong>() → boolean</code></dt>
          <dt><code><strong>findPrevious</strong>() → boolean</code></dt>
          <dd>Search forward or backward from the current position.
          The return value indicates whether a match was found. If
          matching a regular expression, the return value will be the
          array returned by the <code>match</code> method, in case you
          want to extract matched groups.</dd>
          <dt><code><strong>from</strong>() → {line, ch}</code></dt>
          <dt><code><strong>to</strong>() → {line, ch}</code></dt>
          <dd>These are only valid when the last call
          to <code>findNext</code> or <code>findPrevious</code> did
          not return false. They will return <code>{line, ch}</code>
          objects pointing at the start and end of the match.</dd>
          <dt><code><strong>replace</strong>(text: string, ?origin: string)</code></dt>
          <dd>Replaces the currently found match with the given text
          and adjusts the cursor position to reflect the
          replacement.</dd>
        </dl></dd>

      <dt id="addon_search"><a href="../addon/search/search.js"><code>search/search.js</code></a></dt>
      <dd>Implements the search commands. CodeMirror has keys bound to
      these by default, but will not do anything with them unless an
      implementation is provided. Depends
      on <code>searchcursor.js</code>, and will make use
      of <a href="#addon_dialog"><code>openDialog</code></a> when
      available to make prompting for search queries less ugly.</dd>

      <dt id="addon_jump-to-line"><a href="../addon/search/jump-to-line.js"><code>search/jump-to-line.js</code></a></dt>
      <dd>Implements a <code>jumpToLine</code> command and binding <code>Alt-G</code> to it.
      Accepts <code>linenumber</code>, <code>+/-linenumber</code>, <code>line:char</code>,
      <code>scroll%</code> and <code>:linenumber</code> formats.
      This will make use of <a href="#addon_dialog"><code>openDialog</code></a>
      when available to make prompting for line number neater.</dd>

      <dt id="addon_matchesonscrollbar"><a href="../addon/search/matchesonscrollbar.js"><code>search/matchesonscrollbar.js</code></a></dt>
      <dd>Adds a <code>showMatchesOnScrollbar</code> method to editor
      instances, which should be given a query (string or regular
      expression), optionally a case-fold flag (only applicable for
      strings), and optionally a class name (defaults
      to <code>CodeMirror-search-match</code>) as arguments. When
      called, matches of the given query will be displayed on the
      editor's vertical scrollbar. The method returns an object with
      a <code>clear</code> method that can be called to remove the
      matches. Depends on
      the <a href="#addon_annotatescrollbar"><code>annotatescrollbar</code></a>
      addon, and
      the <a href="../addon/search/matchesonscrollbar.css"><code>matchesonscrollbar.css</code></a>
      file provides a default (transparent yellowish) definition of
      the CSS class applied to the matches. Note that the matches are
      only perfectly aligned if your scrollbar does not have buttons
      at the top and bottom. You can use
      the <a href="#addon_simplescrollbars"><code>simplescrollbar</code></a>
      addon to make sure of this. If this addon is loaded,
      the <a href="#addon_search"><code>search</code></a> addon will
      automatically use it.</dd>

      <dt id="addon_matchbrackets"><a href="../addon/edit/matchbrackets.js"><code>edit/matchbrackets.js</code></a></dt>
      <dd>Defines an option <code>matchBrackets</code> which, when set
      to true, causes matching brackets to be highlighted whenever the
      cursor is next to them. It also adds a
      method <code>matchBrackets</code> that forces this to happen
      once, and a method <code>findMatchingBracket</code> that can be
      used to run the bracket-finding algorithm that this uses
      internally.</dd>

      <dt id="addon_closebrackets"><a href="../addon/edit/closebrackets.js"><code>edit/closebrackets.js</code></a></dt>
      <dd>Defines an option <code>autoCloseBrackets</code> that will
      auto-close brackets and quotes when typed. By default, it'll
      auto-close <code>()[]{}''""</code>, but you can pass it a string
      similar to that (containing pairs of matching characters), or an
      object with <code>pairs</code> and
      optionally <code>explode</code> properties to customize
      it. <code>explode</code> should be a similar string that gives
      the pairs of characters that, when enter is pressed between
      them, should have the second character also moved to its own
      line. <a href="../demo/closebrackets.html">Demo here</a>.</dd>

      <dt id="addon_matchtags"><a href="../addon/edit/matchtags.js"><code>edit/matchtags.js</code></a></dt>
      <dd>Defines an option <code>matchTags</code> that, when enabled,
      will cause the tags around the cursor to be highlighted (using
      the <code>CodeMirror-matchingtag</code> class). Also
      defines
      a <a href="#commands">command</a> <code>toMatchingTag</code>,
      which you can bind a key to in order to jump to the tag matching
      the one under the cursor. Depends on
      the <code>addon/fold/xml-fold.js</code>
      addon. <a href="../demo/matchtags.html">Demo here.</a></dd>

      <dt id="addon_trailingspace"><a href="../addon/edit/trailingspace.js"><code>edit/trailingspace.js</code></a></dt>
      <dd>Adds an option <code>showTrailingSpace</code> which, when
      enabled, adds the CSS class <code>cm-trailingspace</code> to
      stretches of whitespace at the end of lines.
      The <a href="../demo/trailingspace.html">demo</a> has a nice
      squiggly underline style for this class.</dd>

      <dt id="addon_closetag"><a href="../addon/edit/closetag.js"><code>edit/closetag.js</code></a></dt>
      <dd>Defines an <code>autoCloseTags</code> option that will
      auto-close XML tags when '<code>&gt;</code>' or '<code>/</code>'
      is typed, and
      a <code>closeTag</code> <a href="#commands">command</a> that
      closes the nearest open tag. Depends on
      the <code>fold/xml-fold.js</code> addon. See
      the <a href="../demo/closetag.html">demo</a>.</dd>

      <dt id="addon_continuelist"><a href="../addon/edit/continuelist.js"><code>edit/continuelist.js</code></a></dt>
      <dd>Markdown specific. Defines
      a <code>"newlineAndIndentContinueMarkdownList"</code> <a href="#commands">command</a>
      command that can be bound to <code>enter</code> to automatically
      insert the leading characters for continuing a list. See
      the <a href="../mode/markdown/index.html">Markdown mode
      demo</a>.</dd>

      <dt id="addon_comment"><a href="../addon/comment/comment.js"><code>comment/comment.js</code></a></dt>
      <dd>Addon for commenting and uncommenting code. Adds four
      methods to CodeMirror instances:
      <dl>
        <dt id="toggleComment"><code><strong>toggleComment</strong>(from: {line, ch}, to: {line, ch}, ?options: object)</code></dt>
        <dd>Tries to uncomment the current selection, and if that
        fails, line-comments it.</dd>
        <dt id="lineComment"><code><strong>lineComment</strong>(from: {line, ch}, to: {line, ch}, ?options: object)</code></dt>
        <dd>Set the lines in the given range to be line comments. Will
        fall back to <code>blockComment</code> when no line comment
        style is defined for the mode.</dd>
        <dt id="blockComment"><code><strong>blockComment</strong>(from: {line, ch}, to: {line, ch}, ?options: object)</code></dt>
        <dd>Wrap the code in the given range in a block comment. Will
        fall back to <code>lineComment</code> when no block comment
        style is defined for the mode.</dd>
        <dt id="uncomment"><code><strong>uncomment</strong>(from: {line, ch}, to: {line, ch}, ?options: object) → boolean</code></dt>
        <dd>Try to uncomment the given range.
          Returns <code>true</code> if a comment range was found and
          removed, <code>false</code> otherwise.</dd>
      </dl>
      The <code>options</code> object accepted by these methods may
      have the following properties:
      <dl>
        <dt><code>blockCommentStart, blockCommentEnd, blockCommentLead, lineComment: string</code></dt>
        <dd>Override the <a href="#mode_comment">comment string
        properties</a> of the mode with custom comment strings.</dd>
        <dt><code><strong>padding</strong>: string</code></dt>
        <dd>A string that will be inserted after opening and leading
        markers, and before closing comment markers. Defaults to a
        single space.</dd>
        <dt><code><strong>commentBlankLines</strong>: boolean</code></dt>
        <dd>Whether, when adding line comments, to also comment lines
        that contain only whitespace.</dd>
        <dt><code><strong>indent</strong>: boolean</code></dt>
        <dd>When adding line comments and this is turned on, it will
        align the comment block to the current indentation of the
        first line of the block.</dd>
        <dt><code><strong>fullLines</strong>: boolean</code></dt>
        <dd>When block commenting, this controls whether the whole
        lines are indented, or only the precise range that is given.
        Defaults to <code>true</code>.</dd>
      </dl>
      The addon also defines
      a <code>toggleComment</code> <a href="#commands">command</a>,
      which is a shorthand command for calling
      <code>toggleComment</code> with no options.</dd>

      <dt id="addon_foldcode"><a href="../addon/fold/foldcode.js"><code>fold/foldcode.js</code></a></dt>
      <dd>Helps with code folding. Adds a <code>foldCode</code> method
      to editor instances, which will try to do a code fold starting
      at the given line, or unfold the fold that is already present.
      The method takes as first argument the position that should be
      folded (may be a line number or
      a <a href="#Pos"><code>Pos</code></a>), and as second optional
      argument either a range-finder function, or an options object,
      supporting the following properties:
      <dl>
        <dt><code><strong>rangeFinder</strong>: fn(CodeMirror, Pos)</code></dt>
        <dd id="helper_fold_auto">The function that is used to find
        foldable ranges. If this is not directly passed, it will
        default to <code>CodeMirror.fold.auto</code>, which
        uses <a href="#getHelpers"><code>getHelpers</code></a> with
        a <code>"fold"</code> type to find folding functions
        appropriate for the local mode. There are files in
        the <a href="../addon/fold/"><code>addon/fold/</code></a>
        directory providing <code>CodeMirror.fold.brace</code>, which
        finds blocks in brace languages (JavaScript, C, Java,
        etc), <code>CodeMirror.fold.indent</code>, for languages where
        indentation determines block structure (Python, Haskell),
        and <code>CodeMirror.fold.xml</code>, for XML-style languages,
        and <code>CodeMirror.fold.comment</code>, for folding comment
        blocks.</dd>
        <dt><code><strong>widget</strong>: string|Element</code></dt>
        <dd>The widget to show for folded ranges. Can be either a
        string, in which case it'll become a span with
        class <code>CodeMirror-foldmarker</code>, or a DOM node.</dd>
        <dt><code><strong>scanUp</strong>: boolean</code></dt>
        <dd>When true (default is false), the addon will try to find
        foldable ranges on the lines above the current one if there
        isn't an eligible one on the given line.</dd>
        <dt><code><strong>minFoldSize</strong>: integer</code></dt>
        <dd>The minimum amount of lines that a fold should span to be
        accepted. Defaults to 0, which also allows single-line
        folds.</dd>
      </dl>
      See <a href="../demo/folding.html">the demo</a> for an
      example.</dd>

      <dt id="addon_foldgutter"><a href="../addon/fold/foldgutter.js"><code>fold/foldgutter.js</code></a></dt>
      <dd>Provides an option <code>foldGutter</code>, which can be
      used to create a gutter with markers indicating the blocks that
      can be folded. Create a gutter using
      the <a href="#option_gutters"><code>gutters</code></a> option,
      giving it the class <code>CodeMirror-foldgutter</code> or
      something else if you configure the addon to use a different
      class, and this addon will show markers next to folded and
      foldable blocks, and handle clicks in this gutter. Note that
      CSS styles should be applied to make the gutter, and the fold
      markers within it, visible. A default set of CSS styles are
      available in:
      <a href="../addon/fold/foldgutter.css">
        <code>addon/fold/foldgutter.css</code>
      </a>.
      The option
      can be either set to <code>true</code>, or an object containing
      the following optional option fields:
      <dl>
        <dt><code><strong>gutter</strong>: string</code></dt>
        <dd>The CSS class of the gutter. Defaults
        to <code>"CodeMirror-foldgutter"</code>. You will have to
        style this yourself to give it a width (and possibly a
        background). See the default gutter style rules above.</dd>
        <dt><code><strong>indicatorOpen</strong>: string | Element</code></dt>
        <dd>A CSS class or DOM element to be used as the marker for
        open, foldable blocks. Defaults
        to <code>"CodeMirror-foldgutter-open"</code>.</dd>
        <dt><code><strong>indicatorFolded</strong>: string | Element</code></dt>
        <dd>A CSS class or DOM element to be used as the marker for
        folded blocks. Defaults to <code>"CodeMirror-foldgutter-folded"</code>.</dd>
        <dt><code><strong>rangeFinder</strong>: fn(CodeMirror, Pos)</code></dt>
        <dd>The range-finder function to use when determining whether
        something can be folded. When not
        given, <a href="#helper_fold_auto"><code>CodeMirror.fold.auto</code></a>
        will be used as default.</dd>
      </dl>
      The <code>foldOptions</code> editor option can be set to an
      object to provide an editor-wide default configuration.
      Demo <a href="../demo/folding.html">here</a>.</dd>

      <dt id="addon_runmode"><a href="../addon/runmode/runmode.js"><code>runmode/runmode.js</code></a></dt>
      <dd>Can be used to run a CodeMirror mode over text without
      actually opening an editor instance.
      See <a href="../demo/runmode.html">the demo</a> for an example.
      There are alternate versions of the file available for
      running <a href="../addon/runmode/runmode-standalone.js">stand-alone</a>
      (without including all of CodeMirror) and
      for <a href="../addon/runmode/runmode.node.js">running under
      node.js</a> (see <code>bin/source-highlight</code> for an example of using the latter).</dd>

      <dt id="addon_colorize"><a href="../addon/runmode/colorize.js"><code>runmode/colorize.js</code></a></dt>
      <dd>Provides a convenient way to syntax-highlight code snippets
      in a webpage. Depends on
      the <a href="#addon_runmode"><code>runmode</code></a> addon (or
      its standalone variant). Provides
      a <code>CodeMirror.colorize</code> function that can be called
      with an array (or other array-ish collection) of DOM nodes that
      represent the code snippets. By default, it'll get
      all <code>pre</code> tags. Will read the <code>data-lang</code>
      attribute of these nodes to figure out their language, and
      syntax-color their content using the relevant CodeMirror mode
      (you'll have to load the scripts for the relevant modes
      yourself). A second argument may be provided to give a default
      mode, used when no language attribute is found for a node. Used
      in this manual to highlight example code.</dd>

      <dt id="addon_overlay"><a href="../addon/mode/overlay.js"><code>mode/overlay.js</code></a></dt>
      <dd>Mode combinator that can be used to extend a mode with an
      'overlay' — a secondary mode is run over the stream, along with
      the base mode, and can color specific pieces of text without
      interfering with the base mode.
      Defines <code>CodeMirror.overlayMode</code>, which is used to
      create such a mode. See <a href="../demo/mustache.html">this
      demo</a> for a detailed example.</dd>

      <dt id="addon_multiplex"><a href="../addon/mode/multiplex.js"><code>mode/multiplex.js</code></a></dt>
      <dd>Mode combinator that can be used to easily 'multiplex'
      between several modes.
      Defines <code>CodeMirror.multiplexingMode</code> which, when
      given as first argument a mode object, and as other arguments
      any number of <code>{open, close, mode [, delimStyle, innerStyle, parseDelimiters]}</code>
      objects, will return a mode object that starts parsing using the
      mode passed as first argument, but will switch to another mode
      as soon as it encounters a string that occurs in one of
      the <code>open</code> fields of the passed objects. When in a
      sub-mode, it will go back to the top mode again when
      the <code>close</code> string is encountered.
      Pass <code>"\n"</code> for <code>open</code> or <code>close</code>
      if you want to switch on a blank line.
      <ul><li>When <code>delimStyle</code> is specified, it will be the token
      style returned for the delimiter tokens (as well as
      <code>[delimStyle]-open</code> on the opening token and
      <code>[delimStyle]-close</code> on the closing token).</li>
      <li>When <code>innerStyle</code> is specified, it will be the token
      style added for each inner mode token.</li>
      <li>When <code>parseDelimiters</code> is true, the content of
      the delimiters will also be passed to the inner mode.
      (And <code>delimStyle</code> is ignored.)</li></ul> The outer
      mode will not see the content between the delimiters.
      See <a href="../demo/multiplex.html">this demo</a> for an
      example.</dd>

      <dt id="addon_show-hint"><a href="../addon/hint/show-hint.js"><code>hint/show-hint.js</code></a></dt>
      <dd>Provides a framework for showing autocompletion hints.
      Defines <code>editor.showHint</code>, which takes an optional
      options object, and pops up a widget that allows the user to
      select a completion. Finding hints is done with a hinting
      functions (the <code>hint</code> option), which is a function
      that take an editor instance and options object, and return
      a <code>{list, from, to}</code> object, where <code>list</code>
      is an array of strings or objects (the completions),
      and <code>from</code> and <code>to</code> give the start and end
      of the token that is being completed as <code>{line, ch}</code>
      objects. An optional <code>selectedHint</code> property (an
      integer) can be added to the completion object to control the
      initially selected hint.</dd>
      <dd>If no hinting function is given, the addon will
      use <code>CodeMirror.hint.auto</code>, which
      calls <a href="#getHelpers"><code>getHelpers</code></a> with
      the <code>"hint"</code> type to find applicable hinting
      functions, and tries them one by one. If that fails, it looks
      for a <code>"hintWords"</code> helper to fetch a list of
      completable words for the mode, and
      uses <code>CodeMirror.hint.fromList</code> to complete from
      those.</dd>
      <dd>When completions aren't simple strings, they should be
      objects with the following properties:
      <dl>
        <dt><code><strong>text</strong>: string</code></dt>
        <dd>The completion text. This is the only required
        property.</dd>
        <dt><code><strong>displayText</strong>: string</code></dt>
        <dd>The text that should be displayed in the menu.</dd>
        <dt><code><strong>className</strong>: string</code></dt>
        <dd>A CSS class name to apply to the completion's line in the
        menu.</dd>
        <dt><code><strong>render</strong>: fn(Element, self, data)</code></dt>
        <dd>A method used to create the DOM structure for showing the
        completion by appending it to its first argument.</dd>
        <dt><code><strong>hint</strong>: fn(CodeMirror, self, data)</code></dt>
        <dd>A method used to actually apply the completion, instead of
        the default behavior.</dd>
        <dt><code><strong>from</strong>: {line, ch}</code></dt>
        <dd>Optional <code>from</code> position that will be used by <code>pick()</code> instead
        of the global one passed with the full list of completions.</dd>
        <dt><code><strong>to</strong>: {line, ch}</code></dt>
        <dd>Optional <code>to</code> position that will be used by <code>pick()</code> instead
        of the global one passed with the full list of completions.</dd>
      </dl>
      The plugin understands the following options (the options object
      will also be passed along to the hinting function, which may
      understand additional options):
      <dl>
        <dt><code><strong>hint</strong>: function</code></dt>
        <dd>A hinting function, as specified above. It is possible to
        set the <code>async</code> property on a hinting function to
        true, in which case it will be called with
        arguments <code>(cm, callback, ?options)</code>, and the
        completion interface will only be popped up when the hinting
        function calls the callback, passing it the object holding the
        completions. By default, hinting only works when there is no
        selection. You can give a hinting function
        a <code>supportsSelection</code> property with a truthy value
        to indicate that it supports selections.</dd>
        <dt><code><strong>completeSingle</strong>: boolean</code></dt>
        <dd>Determines whether, when only a single completion is
        available, it is completed without showing the dialog.
        Defaults to true.</dd>
        <dt><code><strong>alignWithWord</strong>: boolean</code></dt>
        <dd>Whether the pop-up should be horizontally aligned with the
        start of the word (true, default), or with the cursor (false).</dd>
        <dt><code><strong>closeOnUnfocus</strong>: boolean</code></dt>
        <dd>When enabled (which is the default), the pop-up will close
        when the editor is unfocused.</dd>
        <dt><code><strong>customKeys</strong>: keymap</code></dt>
        <dd>Allows you to provide a custom key map of keys to be active
        when the pop-up is active. The handlers will be called with an
        extra argument, a handle to the completion menu, which
        has <code>moveFocus(n)</code>, <code>setFocus(n)</code>, <code>pick()</code>,
        and <code>close()</code> methods (see the source for details),
        that can be used to change the focused element, pick the
        current element or close the menu. Additionally <code>menuSize()</code>
        can give you access to the size of the current dropdown menu,
        <code>length</code> give you the number of available completions, and
        <code>data</code> give you full access to the completion returned by the
        hinting function.</dd>
        <dt><code><strong>extraKeys</strong>: keymap</code></dt>
        <dd>Like <code>customKeys</code> above, but the bindings will
        be added to the set of default bindings, instead of replacing
        them.</dd>
      </dl>
      The following events will be fired on the completions object
      during completion:
      <dl>
        <dt><code><strong>"shown"</strong> ()</code></dt>
        <dd>Fired when the pop-up is shown.</dd>
        <dt><code><strong>"select"</strong> (completion, Element)</code></dt>
        <dd>Fired when a completion is selected. Passed the completion
        value (string or object) and the DOM node that represents it
        in the menu.</dd>
        <dt><code><strong>"pick"</strong> (completion)</code></dt>
        <dd>Fired when a completion is picked. Passed the completion value
        (string or object).</dd>
        <dt><code><strong>"close"</strong> ()</code></dt>
        <dd>Fired when the completion is finished.</dd>
      </dl>
      This addon depends on styles
      from <code>addon/hint/show-hint.css</code>. Check
      out <a href="../demo/complete.html">the demo</a> for an
      example.</dd>

      <dt id="addon_javascript-hint"><a href="../addon/hint/javascript-hint.js"><code>hint/javascript-hint.js</code></a></dt>
      <dd>Defines a simple hinting function for JavaScript
      (<code>CodeMirror.hint.javascript</code>) and CoffeeScript
      (<code>CodeMirror.hint.coffeescript</code>) code. This will
      simply use the JavaScript environment that the editor runs in as
      a source of information about objects and their properties.</dd>

      <dt id="addon_xml-hint"><a href="../addon/hint/xml-hint.js"><code>hint/xml-hint.js</code></a></dt>
      <dd>Defines <code>CodeMirror.hint.xml</code>, which produces
      hints for XML tagnames, attribute names, and attribute values,
      guided by a <code>schemaInfo</code> option (a property of the
      second argument passed to the hinting function, or the third
      argument passed to <code>CodeMirror.showHint</code>).<br>The
      schema info should be an object mapping tag names to information
      about these tags, with optionally a <code>"!top"</code> property
      containing a list of the names of valid top-level tags. The
      values of the properties should be objects with optional
      properties <code>children</code> (an array of valid child
      element names, omit to simply allow all tags to appear)
      and <code>attrs</code> (an object mapping attribute names
      to <code>null</code> for free-form attributes, and an array of
      valid values for restricted
      attributes). <a href="../demo/xmlcomplete.html">Demo
      here.</a></dd>

      <dt id="addon_html-hint"><a href="../addon/hint/html-hint.js"><code>hint/html-hint.js</code></a></dt>
      <dd>Provides schema info to
      the <a href="#addon_xml-hint">xml-hint</a> addon for HTML
      documents. Defines a schema
      object <code>CodeMirror.htmlSchema</code> that you can pass to
      as a <code>schemaInfo</code> option, and
      a <code>CodeMirror.hint.html</code> hinting function that
      automatically calls <code>CodeMirror.hint.xml</code> with this
      schema data. See
      the <a href="../demo/html5complete.html">demo</a>.</dd>

      <dt id="addon_css-hint"><a href="../addon/hint/css-hint.js"><code>hint/css-hint.js</code></a></dt>
      <dd>A hinting function for CSS, SCSS, or LESS code.
      Defines <code>CodeMirror.hint.css</code>.</dd>

      <dt id="addon_anyword-hint"><a href="../addon/hint/anyword-hint.js"><code>hint/anyword-hint.js</code></a></dt>
      <dd>A very simple hinting function
      (<code>CodeMirror.hint.anyword</code>) that simply looks for
      words in the nearby code and completes to those. Takes two
      optional options, <code>word</code>, a regular expression that
      matches words (sequences of one or more character),
      and <code>range</code>, which defines how many lines the addon
      should scan when completing (defaults to 500).</dd>

      <dt id="addon_sql-hint"><a href="../addon/hint/sql-hint.js"><code>hint/sql-hint.js</code></a></dt>
      <dd>A simple SQL hinter. Defines <code>CodeMirror.hint.sql</code>.
      Takes two optional options, <code>tables</code>, a object with
      table names as keys and array of respective column names as values,
      and <code>defaultTable</code>, a string corresponding to a
      table name in <code>tables</code> for autocompletion.</dd>

      <dt id="addon_match-highlighter"><a href="../addon/search/match-highlighter.js"><code>search/match-highlighter.js</code></a></dt>
      <dd>Adds a <code>highlightSelectionMatches</code> option that
      can be enabled to highlight all instances of a currently
      selected word. Can be set either to true or to an object
      containing the following options: <code>minChars</code>, for the
      minimum amount of selected characters that triggers a highlight
      (default 2), <code>style</code>, for the style to be used to
      highlight the matches (default <code>"matchhighlight"</code>,
      which will correspond to CSS
      class <code>cm-matchhighlight</code>),
      and <code>showToken</code> which can be set to <code>true</code>
      or to a regexp matching the characters that make up a word. When
      enabled, it causes the current word to be highlighted when
      nothing is selected (defaults to off).
      Demo <a href="../demo/matchhighlighter.html">here</a>.</dd>

      <dt id="addon_lint"><a href="../addon/lint/lint.js"><code>lint/lint.js</code></a></dt>
      <dd>Defines an interface component for showing linting warnings,
      with pluggable warning sources
      (see <a href="../addon/lint/html-lint.js"><code>html-lint.js</code></a>,
      <a href="../addon/lint/json-lint.js"><code>json-lint.js</code></a>,
      <a href="../addon/lint/javascript-lint.js"><code>javascript-lint.js</code></a>,
      <a href="../addon/lint/coffeescript-lint.js"><code>coffeescript-lint.js</code></a>,
      and <a href="../addon/lint/css-lint.js"><code>css-lint.js</code></a>
      in the same directory). Defines a <code>lint</code> option that
      can be set to an annotation source (for
      example <code>CodeMirror.lint.javascript</code>), to an options
      object (in which case the <code>getAnnotations</code> field is
      used as annotation source), or simply to <code>true</code>. When
      no annotation source is
      specified, <a href="#getHelper"><code>getHelper</code></a> with
      type <code>"lint"</code> is used to find an annotation function.
      An annotation source function should, when given a document
      string, an options object, and an editor instance, return an
      array of <code>{message, severity, from, to}</code> objects
      representing problems. When the function has
      an <code>async</code> property with a truthy value, it will be
      called with an additional second argument, which is a callback
      to pass the array to. By default, the linter will run
      (debounced) whenever the document is changed. You can pass
      a <code>lintOnChange: false</code> option to disable that.
      Depends on <code>addon/lint/lint.css</code>. A demo can be
      found <a href="../demo/lint.html">here</a>.</dd>

      <dt id="addon_mark-selection"><a href="../addon/selection/mark-selection.js"><code>selection/mark-selection.js</code></a></dt>
      <dd>Causes the selected text to be marked with the CSS class
      <code>CodeMirror-selectedtext</code> when the <code>styleSelectedText</code> option
      is enabled. Useful to change the colour of the selection (in addition to the background),
      like in <a href="../demo/markselection.html">this demo</a>.</dd>

      <dt id="addon_active-line"><a href="../addon/selection/active-line.js"><code>selection/active-line.js</code></a></dt>
      <dd>Defines a <code>styleActiveLine</code> option that, when enabled,
      gives the wrapper of the active line the class <code>CodeMirror-activeline</code>,
      and adds a background with the class <code>CodeMirror-activeline-background</code>.
      is enabled. See the <a href="../demo/activeline.html">demo</a>.</dd>

      <dt id="addon_selection-pointer"><a href="../addon/selection/selection-pointer.js"><code>selection/selection-pointer.js</code></a></dt>
      <dd>Defines a <code>selectionPointer</code> option which you can
      use to control the mouse cursor appearance when hovering over
      the selection. It can be set to a string,
      like <code>"pointer"</code>, or to true, in which case
      the <code>"default"</code> (arrow) cursor will be used. You can
      see a demo <a href="../mode/htmlmixed/index.html">here</a>.</dd>

      <dt id="addon_loadmode"><a href="../addon/mode/loadmode.js"><code>mode/loadmode.js</code></a></dt>
      <dd>Defines a <code>CodeMirror.requireMode(modename,
      callback)</code> function that will try to load a given mode and
      call the callback when it succeeded. You'll have to
      set <code>CodeMirror.modeURL</code> to a string that mode paths
      can be constructed from, for
      example <code>"mode/%N/%N.js"</code>—the <code>%N</code>'s will
      be replaced with the mode name. Also
      defines <code>CodeMirror.autoLoadMode(instance, mode)</code>,
      which will ensure the given mode is loaded and cause the given
      editor instance to refresh its mode when the loading
      succeeded. See the <a href="../demo/loadmode.html">demo</a>.</dd>

      <dt id="addon_meta"><a href="../mode/meta.js"><code>mode/meta.js</code></a></dt>
      <dd>Provides meta-information about all the modes in the
      distribution in a single file.
      Defines <code>CodeMirror.modeInfo</code>, an array of objects
      with <code>{name, mime, mode}</code> properties,
      where <code>name</code> is the human-readable
      name, <code>mime</code> the MIME type, and <code>mode</code> the
      name of the mode file that defines this MIME. There are optional
      properties <code>mimes</code>, which holds an array of MIME
      types for modes with multiple MIMEs associated,
      and <code>ext</code>, which holds an array of file extensions
      associated with this mode. Four convenience
      functions, <code>CodeMirror.findModeByMIME</code>,
      <code>CodeMirror.findModeByExtension</code>,
      <code>CodeMirror.findModeByFileName</code>
      and <code>CodeMirror.findModeByName</code> are provided, which
      return such an object given a MIME, extension, file name or mode name
      string. Note that, for historical reasons, this file resides in the
      top-level <code>mode</code> directory, not
      under <code>addon</code>. <a href="../demo/loadmode.html">Demo</a>.</dd>

      <dt id="addon_continuecomment"><a href="../addon/comment/continuecomment.js"><code>comment/continuecomment.js</code></a></dt>
      <dd>Adds a <code>continueComments</code> option, which sets whether the
      editor will make the next line continue a comment when you press Enter
      inside a comment block. Can be set to a boolean to enable/disable this
      functionality. Set to a string, it will continue comments using a custom
      shortcut. Set to an object, it will use the <code>key</code> property for
      a custom shortcut and the boolean <code>continueLineComment</code>
      property to determine whether single-line comments should be continued
      (defaulting to <code>true</code>).</dd>

      <dt id="addon_placeholder"><a href="../addon/display/placeholder.js"><code>display/placeholder.js</code></a></dt>
      <dd>Adds a <code>placeholder</code> option that can be used to
      make content appear in the editor when it is empty and not
      focused. It can hold either a string or a DOM node. Also gives
      the editor a <code>CodeMirror-empty</code> CSS class whenever it
      doesn't contain any text.
      See <a href="../demo/placeholder.html">the demo</a>.</dd>

      <dt id="addon_fullscreen"><a href="../addon/display/fullscreen.js"><code>display/fullscreen.js</code></a></dt>
      <dd>Defines an option <code>fullScreen</code> that, when set
      to <code>true</code>, will make the editor full-screen (as in,
      taking up the whole browser window). Depends
      on <a href="../addon/display/fullscreen.css"><code>fullscreen.css</code></a>. <a href="../demo/fullscreen.html">Demo
      here</a>.</dd>

      <dt id="addon_autorefresh"><a href="../addon/display/autorefresh.js"><code>display/autorefresh.js</code></a></dt>
      <dd>This addon can be useful when initializing an editor in a
      hidden DOM node, in cases where it is difficult to
      call <a href="#refresh"><code>refresh</code></a> when the editor
      becomes visible. It defines an option <code>autoRefresh</code>
      which you can set to true to ensure that, if the editor wasn't
      visible on initialization, it will be refreshed the first time
      it becomes visible. This is done by polling every 250
      milliseconds (you can pass a value like <code>{delay:
      500}</code> as the option value to configure this). Note that
      this addon will only refresh the editor <em>once</em> when it
      first becomes visible, and won't take care of further restyling
      and resizing.</dd>

      <dt id="addon_simplescrollbars"><a href="../addon/scroll/simplescrollbars.js"><code>scroll/simplescrollbars.js</code></a></dt>
      <dd>Defines two additional scrollbar
      models, <code>"simple"</code> and <code>"overlay"</code>
      (see <a href="../demo/simplescrollbars.html">demo</a>) that can
      be selected with
      the <a href="#option_scrollbarStyle"><code>scrollbarStyle</code></a>
      option. Depends
      on <a href="../addon/scroll/simplescrollbars.css"><code>simplescrollbars.css</code></a>,
      which can be further overridden to style your own
      scrollbars.</dd>

      <dt id="addon_annotatescrollbar"><a href="../addon/scroll/annotatescrollbar.js"><code>scroll/annotatescrollbar.js</code></a></dt>
      <dd>Provides functionality for showing markers on the scrollbar
      to call out certain parts of the document. Adds a
      method <code>annotateScrollbar</code> to editor instances that
      can be called, with a CSS class name as argument, to create a
      set of annotations. The method returns an object
      whose <code>update</code> method can be called with an array
      of <code>{from: Pos, to: Pos}</code> objects marking the ranges
      to be highlighted. To detach the annotations, call the
      object's <code>clear</code> method.</dd>

      <dt id="addon_rulers"><a href="../addon/display/rulers.js"><code>display/rulers.js</code></a></dt>
      <dd>Adds a <code>rulers</code> option, which can be used to show
      one or more vertical rulers in the editor. The option, if
      defined, should be given an array of <code>{column [, className,
      color, lineStyle, width]}</code> objects or numbers (which
      indicate a column). The ruler will be displayed at the column
      indicated by the number or the <code>column</code> property.
      The <code>className</code> property can be used to assign a
      custom style to a ruler. <a href="../demo/rulers.html">Demo
      here</a>.</dd>

      <dt id="addon_panel"><a href="../addon/display/panel.js"><code>display/panel.js</code></a></dt>
      <dd>Defines an <code>addPanel</code> method for CodeMirror
      instances, which places a DOM node above or below an editor, and
      shrinks the editor to make room for the node. The method takes
      as first argument as DOM node, and as second an optional options
      object. The <code>Panel</code> object returned by this method
      has a <code>clear</code> method that is used to remove the
      panel, and a <code>changed</code> method that can be used to
      notify the addon when the size of the panel's DOM node has
      changed.<br/>
      The method accepts the following options:
      <dl>
        <dt><code><strong>position</strong> : string</code></dt>
        <dd>Controls the position of the newly added panel. The
        following values are recognized:
          <dl>
            <dt><code><strong>top</strong> (default)</code></dt>
            <dd>Adds the panel at the very top.</dd>
            <dt><code><strong>after-top</strong></code></dt>
            <dd>Adds the panel at the bottom of the top panels.</dd>
            <dt><code><strong>bottom</strong></code></dt>
            <dd>Adds the panel at the very bottom.</dd>
            <dt><code><strong>before-bottom</strong></code></dt>
            <dd>Adds the panel at the top of the bottom panels.</dd>
          </dl>
        </dd>
        <dt><code><strong>before</strong> : Panel</code></dt>
        <dd>The new panel will be added before the given panel.</dd>
        <dt><code><strong>after</strong> : Panel</code></dt>
        <dd>The new panel will be added after the given panel.</dd>
        <dt><code><strong>replace</strong> : Panel</code></dt>
        <dd>The new panel will replace the given panel.</dd>
      </dl>
      When using the <code>after</code>, <code>before</code> or <code>replace</code> options,
      if the panel doesn't exists or has been removed,
      the value of the <code>position</code> option will be used as a fallback.
      <br>
      A demo of the addon is available <a href="../demo/panel.html">here</a>.
      </dd>

      <dt id="addon_hardwrap"><a href="../addon/wrap/hardwrap.js"><code>wrap/hardwrap.js</code></a></dt>
      <dd>Addon to perform hard line wrapping/breaking for paragraphs
      of text. Adds these methods to editor instances:
        <dl>
          <dt><code><strong>wrapParagraph</strong>(?pos: {line, ch}, ?options: object)</code></dt>
          <dd>Wraps the paragraph at the given position.
          If <code>pos</code> is not given, it defaults to the cursor
          position.</dd>
          <dt><code><strong>wrapRange</strong>(from: {line, ch}, to: {line, ch}, ?options: object)</code></dt>
          <dd>Wraps the given range as one big paragraph.</dd>
          <dt><code><strong>wrapParagraphsInRange</strong>(from: {line, ch}, to: {line, ch}, ?options: object)</code></dt>
          <dd>Wraps the paragraphs in (and overlapping with) the
          given range individually.</dd>
        </dl>
        The following options are recognized:
        <dl>
          <dt><code><strong>paragraphStart</strong>, <strong>paragraphEnd</strong>: RegExp</code></dt>
          <dd>Blank lines are always considered paragraph boundaries.
          These options can be used to specify a pattern that causes
          lines to be considered the start or end of a paragraph.</dd>
          <dt><code><strong>column</strong>: number</code></dt>
          <dd>The column to wrap at. Defaults to 80.</dd>
          <dt><code><strong>wrapOn</strong>: RegExp</code></dt>
          <dd>A regular expression that matches only those
          two-character strings that allow wrapping. By default, the
          addon wraps on whitespace and after dash characters.</dd>
          <dt><code><strong>killTrailingSpace</strong>: boolean</code></dt>
          <dd>Whether trailing space caused by wrapping should be
          preserved, or deleted. Defaults to true.</dd>
        </dl>
        A demo of the addon is available <a href="../demo/hardwrap.html">here</a>.
      </dd>

      <dt id="addon_merge"><a href="../addon/merge/merge.js"><code>merge/merge.js</code></a></dt>
      <dd>Implements an interface for merging changes, using either a
      2-way or a 3-way view. The <code>CodeMirror.MergeView</code>
      constructor takes arguments similar to
      the <a href="#CodeMirror"><code>CodeMirror</code></a>
      constructor, first a node to append the interface to, and then
      an options object. Options are passed through to the editors
      inside the view. These extra options are recognized:
        <dl>
          <dt><code><strong>origLeft</strong></code> and <code><strong>origRight</strong>: string</code></dt>
          <dd>If given these provide original versions of the
          document, which will be shown to the left and right of the
          editor in non-editable CodeMirror instances. The merge
          interface will highlight changes between the editable
          document and the original(s). To create a 2-way (as opposed
          to 3-way) merge view, provide only one of them.</dd>
          <dt><code><strong>revertButtons</strong>: boolean</code></dt>
          <dd>Determines whether buttons that allow the user to revert
          changes are shown. Defaults to true.</dd>
          <dt><code><strong>connect</strong>: string</code></dt>
          <dd>Sets the style used to connect changed chunks of code.
          By default, connectors are drawn. When this is set
          to <code>"align"</code>, the smaller chunk is padded to
          align with the bigger chunk instead.</dd>
          <dt><code><strong>collapseIdentical</strong>: boolean|number</code></dt>
          <dd>When true (default is false), stretches of unchanged
          text will be collapsed. When a number is given, this
          indicates the amount of lines to leave visible around such
          stretches (which defaults to 2).</dd>
          <dt><code><strong>allowEditingOriginals</strong>: boolean</code></dt>
          <dd>Determines whether the original editor allows editing.
          Defaults to false.</dd>
          <dt><code><strong>showDifferences</strong>: boolean</code></dt>
          <dd>When true (the default), changed pieces of text are
          highlighted.</dd>
        </dl>
        The addon also defines commands <code>"goNextDiff"</code>
        and <code>"goPrevDiff"</code> to quickly jump to the next
        changed chunk. <a href="../demo/merge.html">Demo
        here</a>.</dd>

      <dt id="addon_tern"><a href="../addon/tern/tern.js"><code>tern/tern.js</code></a></dt>
      <dd>Provides integration with
      the <a href="http://ternjs.net">Tern</a> JavaScript analysis
      engine, for completion, definition finding, and minor
      refactoring help. See the <a href="../demo/tern.html">demo</a>
      for a very simple integration. For more involved scenarios, see
      the comments at the top of
      the <a href="../addon/tern/tern.js">addon</a> and the
      implementation of the
      (multi-file) <a href="http://ternjs.net/doc/demo.html">demonstration
      on the Tern website</a>.</dd>
    </dl>
</section>

<section id=modeapi>
    <h2>Writing CodeMirror Modes</h2>

    <p>Modes typically consist of a single JavaScript file. This file
    defines, in the simplest case, a lexer (tokenizer) for your
    language—a function that takes a character stream as input,
    advances it past a token, and returns a style for that token. More
    advanced modes can also handle indentation for the language.</p>

    <p>This section describes the low-level mode interface. Many modes
    are written directly against this, since it offers a lot of
    control, but for a quick mode definition, you might want to use
    the <a href="../demo/simplemode.html">simple mode addon</a>.</p>

    <p id="defineMode">The mode script should
    call <code><strong>CodeMirror.defineMode</strong></code> to
    register itself with CodeMirror. This function takes two
    arguments. The first should be the name of the mode, for which you
    should use a lowercase string, preferably one that is also the
    name of the files that define the mode (i.e. <code>"xml"</code> is
    defined in <code>xml.js</code>). The second argument should be a
    function that, given a CodeMirror configuration object (the thing
    passed to the <code>CodeMirror</code> function) and an optional
    mode configuration object (as in
    the <a href="#option_mode"><code>mode</code></a> option), returns
    a mode object.</p>

    <p>Typically, you should use this second argument
    to <code>defineMode</code> as your module scope function (modes
    should not leak anything into the global scope!), i.e. write your
    whole mode inside this function.</p>

    <p>The main responsibility of a mode script is <em>parsing</em>
    the content of the editor. Depending on the language and the
    amount of functionality desired, this can be done in really easy
    or extremely complicated ways. Some parsers can be stateless,
    meaning that they look at one element (<em>token</em>) of the code
    at a time, with no memory of what came before. Most, however, will
    need to remember something. This is done by using a <em>state
    object</em>, which is an object that is always passed when
    reading a token, and which can be mutated by the tokenizer.</p>

    <p id="startState">Modes that use a state must define
    a <code><strong>startState</strong></code> method on their mode
    object. This is a function of no arguments that produces a state
    object to be used at the start of a document.</p>

    <p id="token">The most important part of a mode object is
    its <code><strong>token</strong>(stream, state)</code> method. All
    modes must define this method. It should read one token from the
    stream it is given as an argument, optionally update its state,
    and return a style string, or <code>null</code> for tokens that do
    not have to be styled. For your styles, you are encouraged to use
    the 'standard' names defined in the themes (without
    the <code>cm-</code> prefix). If that fails, it is also possible
    to come up with your own and write your own CSS theme file.<p>

    <p id="token_style_line">A typical token string would
    be <code>"variable"</code> or <code>"comment"</code>. Multiple
    styles can be returned (separated by spaces), for
    example <code>"string error"</code> for a thing that looks like a
    string but is invalid somehow (say, missing its closing quote).
    When a style is prefixed by <code>"line-"</code>
    or <code>"line-background-"</code>, the style will be applied to
    the whole line, analogous to what
    the <a href="#addLineClass"><code>addLineClass</code></a> method
    does—styling the <code>"text"</code> in the simple case, and
    the <code>"background"</code> element
    when <code>"line-background-"</code> is prefixed.</p>

    <p id="StringStream">The stream object that's passed
    to <code>token</code> encapsulates a line of code (tokens may
    never span lines) and our current position in that line. It has
    the following API:</p>

    <dl>
      <dt><code><strong>eol</strong>() → boolean</code></dt>
      <dd>Returns true only if the stream is at the end of the
      line.</dd>
      <dt><code><strong>sol</strong>() → boolean</code></dt>
      <dd>Returns true only if the stream is at the start of the
      line.</dd>

      <dt><code><strong>peek</strong>() → string</code></dt>
      <dd>Returns the next character in the stream without advancing
      it. Will return an <code>null</code> at the end of the
      line.</dd>
      <dt><code><strong>next</strong>() → string</code></dt>
      <dd>Returns the next character in the stream and advances it.
      Also returns <code>null</code> when no more characters are
      available.</dd>

      <dt><code><strong>eat</strong>(match: string|regexp|function(char: string) → boolean) → string</code></dt>
      <dd><code>match</code> can be a character, a regular expression,
      or a function that takes a character and returns a boolean. If
      the next character in the stream 'matches' the given argument,
      it is consumed and returned. Otherwise, <code>undefined</code>
      is returned.</dd>
      <dt><code><strong>eatWhile</strong>(match: string|regexp|function(char: string) → boolean) → boolean</code></dt>
      <dd>Repeatedly calls <code>eat</code> with the given argument,
      until it fails. Returns true if any characters were eaten.</dd>
      <dt><code><strong>eatSpace</strong>() → boolean</code></dt>
      <dd>Shortcut for <code>eatWhile</code> when matching
      white-space.</dd>
      <dt><code><strong>skipToEnd</strong>()</code></dt>
      <dd>Moves the position to the end of the line.</dd>
      <dt><code><strong>skipTo</strong>(ch: string) → boolean</code></dt>
      <dd>Skips to the next occurrence of the given character, if
      found on the current line (doesn't advance the stream if the
      character does not occur on the line). Returns true if the
      character was found.</dd>
      <dt><code><strong>match</strong>(pattern: string, ?consume: boolean, ?caseFold: boolean) → boolean</code></dt>
      <dt><code><strong>match</strong>(pattern: regexp, ?consume: boolean) → array&lt;string&gt;</code></dt>
      <dd>Act like a
      multi-character <code>eat</code>—if <code>consume</code> is true
      or not given—or a look-ahead that doesn't update the stream
      position—if it is false. <code>pattern</code> can be either a
      string or a regular expression starting with <code>^</code>.
      When it is a string, <code>caseFold</code> can be set to true to
      make the match case-insensitive. When successfully matching a
      regular expression, the returned value will be the array
      returned by <code>match</code>, in case you need to extract
      matched groups.</dd>

      <dt><code><strong>backUp</strong>(n: integer)</code></dt>
      <dd>Backs up the stream <code>n</code> characters. Backing it up
      further than the start of the current token will cause things to
      break, so be careful.</dd>
      <dt><code><strong>column</strong>() → integer</code></dt>
      <dd>Returns the column (taking into account tabs) at which the
      current token starts.</dd>
      <dt><code><strong>indentation</strong>() → integer</code></dt>
      <dd>Tells you how far the current line has been indented, in
      spaces. Corrects for tab characters.</dd>

      <dt><code><strong>current</strong>() → string</code></dt>
      <dd>Get the string between the start of the current token and
      the current stream position.</dd>
    </dl>

    <p id="blankLine">By default, blank lines are simply skipped when
    tokenizing a document. For languages that have significant blank
    lines, you can define
    a <code><strong>blankLine</strong>(state)</code> method on your
    mode that will get called whenever a blank line is passed over, so
    that it can update the parser state.</p>

    <p id="copyState">Because state object are mutated, and CodeMirror
    needs to keep valid versions of a state around so that it can
    restart a parse at any line, copies must be made of state objects.
    The default algorithm used is that a new state object is created,
    which gets all the properties of the old object. Any properties
    which hold arrays get a copy of these arrays (since arrays tend to
    be used as mutable stacks). When this is not correct, for example
    because a mode mutates non-array properties of its state object, a
    mode object should define
    a <code><strong>copyState</strong></code> method, which is given a
    state and should return a safe copy of that state.</p>

    <p id="indent">If you want your mode to provide smart indentation
    (through the <a href="#indentLine"><code>indentLine</code></a>
    method and the <code>indentAuto</code>
    and <code>newlineAndIndent</code> commands, to which keys can be
    <a href="#option_extraKeys">bound</a>), you must define
    an <code><strong>indent</strong>(state, textAfter)</code> method
    on your mode object.</p>

    <p>The indentation method should inspect the given state object,
    and optionally the <code>textAfter</code> string, which contains
    the text on the line that is being indented, and return an
    integer, the amount of spaces to indent. It should usually take
    the <a href="#option_indentUnit"><code>indentUnit</code></a>
    option into account. An indentation method may
    return <code>CodeMirror.Pass</code> to indicate that it
    could not come up with a precise indentation.</p>

    <p id="mode_comment">To work well with
    the <a href="#addon_comment">commenting addon</a>, a mode may
    define <code><strong>lineComment</strong></code> (string that
    starts a line
    comment), <code><strong>blockCommentStart</strong></code>, <code><strong>blockCommentEnd</strong></code>
    (strings that start and end block comments),
    and <code>blockCommentLead</code> (a string to put at the start of
    continued lines in a block comment). All of these are
    optional.</p>

    <p id="electricChars">Finally, a mode may define either
    an <code>electricChars</code> or an <code>electricInput</code>
    property, which are used to automatically reindent the line when
    certain patterns are typed and
    the <a href="#option_electricChars"><code>electricChars</code></a>
    option is enabled. <code>electricChars</code> may be a string, and
    will trigger a reindent whenever one of the characters in that
    string are typed. Often, it is more appropriate to
    use <code>electricInput</code>, which should hold a regular
    expression, and will trigger indentation when the part of the
    line <em>before</em> the cursor matches the expression. It should
    usually end with a <code>$</code> character, so that it only
    matches when the indentation-changing pattern was just typed, not when something was
    typed after the pattern.</p>

    <p>So, to summarize, a mode <em>must</em> provide
    a <code>token</code> method, and it <em>may</em>
    provide <code>startState</code>, <code>copyState</code>,
    and <code>indent</code> methods. For an example of a trivial mode,
    see the <a href="../mode/diff/diff.js">diff mode</a>, for a more
    involved example, see the <a href="../mode/clike/clike.js">C-like
    mode</a>.</p>

    <p>Sometimes, it is useful for modes to <em>nest</em>—to have one
    mode delegate work to another mode. An example of this kind of
    mode is the <a href="../mode/htmlmixed/htmlmixed.js">mixed-mode HTML
    mode</a>. To implement such nesting, it is usually necessary to
    create mode objects and copy states yourself. To create a mode
    object, there are <code>CodeMirror.getMode(options,
    parserConfig)</code>, where the first argument is a configuration
    object as passed to the mode constructor function, and the second
    argument is a mode specification as in
    the <a href="#option_mode"><code>mode</code></a> option. To copy a
    state object, call <code>CodeMirror.copyState(mode, state)</code>,
    where <code>mode</code> is the mode that created the given
    state.</p>

    <p id="innerMode">In a nested mode, it is recommended to add an
    extra method, <code><strong>innerMode</strong></code> which, given
    a state object, returns a <code>{state, mode}</code> object with
    the inner mode and its state for the current position. These are
    used by utility scripts such as the <a href="#addon_closetag">tag
    closer</a> to get context information. Use
    the <code>CodeMirror.innerMode</code> helper function to, starting
    from a mode and a state, recursively walk down to the innermost
    mode and state.</p>

    <p>To make indentation work properly in a nested parser, it is
    advisable to give the <code>startState</code> method of modes that
    are intended to be nested an optional argument that provides the
    base indentation for the block of code. The JavaScript and CSS
    parser do this, for example, to allow JavaScript and CSS code
    inside the mixed-mode HTML mode to be properly indented.</p>

    <p id="defineMIME">It is possible, and encouraged, to associate
    your mode, or a certain configuration of your mode, with
    a <a href="http://en.wikipedia.org/wiki/MIME">MIME</a> type. For
    example, the JavaScript mode associates itself
    with <code>text/javascript</code>, and its JSON variant
    with <code>application/json</code>. To do this,
    call <code><strong>CodeMirror.defineMIME</strong>(mime,
    modeSpec)</code>, where <code>modeSpec</code> can be a string or
    object specifying a mode, as in
    the <a href="#option_mode"><code>mode</code></a> option.</p>

    <p>If a mode specification wants to add some properties to the
    resulting mode object, typically for use
    with <a href="#getHelpers"><code>getHelpers</code></a>, it may
    contain a <code>modeProps</code> property, which holds an object.
    This object's properties will be copied to the actual mode
    object.</p>

    <p id="extendMode">Sometimes, it is useful to add or override mode
    object properties from external code.
    The <code><strong>CodeMirror.extendMode</strong></code> function
    can be used to add properties to mode objects produced for a
    specific mode. Its first argument is the name of the mode, its
    second an object that specifies the properties that should be
    added. This is mostly useful to add utilities that can later be
    looked up through <a href="#getMode"><code>getMode</code></a>.</p>
</section>

<section id="vimapi">
    <h2>VIM Mode API</h2>

    <p>CodeMirror has a robust VIM mode that attempts to faithfully
    emulate VIM's most useful features. It can be enabled by
    including <a href="../keymap/vim.js"><code>keymap/vim.js</code>
    </a> and setting the <code>keyMap</code> option to
    <code>"vim"</code>.</p>

    <h3 id="vimapi_configuration">Configuration</h3>

    <p>VIM mode accepts configuration options for customizing
    behavior at run time. These methods can be called at any time
    and will affect all existing CodeMirror instances unless
    specified otherwise. The methods are exposed on the
    <code><strong>CodeMirror.Vim</strong></code> object.</p>

    <dl>
      <dt id="vimapi_setOption"><code><strong>setOption(name: string, value: any, ?cm: CodeMirror, ?cfg: object)</strong></code></dt>
      <dd>Sets the value of a VIM option. <code>name</code> should
      be the name of an option. If <code>cfg.scope</code> is not set
      and <code>cm</code> is provided, then sets the global and
      instance values of the option. Otherwise, sets either the
      global or instance value of the option depending on whether
      <code>cfg.scope</code> is <code>global</code> or
      <code>local</code>.</dd>
      <dt id="vimapi_getOption"><code><strong>getOption(name: string, ?cm: CodeMirror: ?cfg: object)</strong></code></dt>
      <dd>Gets the current value of a VIM option. If
      <code>cfg.scope</code> is not set and <code>cm</code> is
      provided, then gets the instance value of the option, falling
      back to the global value if not set. If <code>cfg.scope</code> is provided, then gets the <code>global</code> or
      <code>local</code> value without checking the other.</dd>

      <dt id="vimapi_map"><code><strong>map(lhs: string, rhs: string, ?context: string)</strong></code></dt>
      <dd>Maps a key sequence to another key sequence. Implements
      VIM's <code>:map</code> command. To map ; to : in VIM would be
      <code><strong>:map ; :</strong></code>. That would translate to
      <code><strong>CodeMirror.Vim.map(';', ':');</strong></code>.
      The <code>context</code> can be <code>normal</code>,
      <code>visual</code>, or <code>insert</code>, which correspond
      to <code>:nmap</code>, <code>:vmap</code>, and
      <code>:imap</code>
      respectively.</dd>

      <dt id="vimapi_mapCommand"><code><strong>mapCommand(keys: string, type: string, name: string, ?args: object, ?extra: object)</strong></code></dt>
      <dd>Maps a key sequence to a <code>motion</code>,
      <code>operator</code>, or <code>action</code> type command.
      The args object is passed through to the command when it is
      invoked by the provided key sequence.
      <code>extras.context</code> can be <code>normal</code>,
      <code>visual</code>, or <code>insert</code>, to map the key
      sequence only in the corresponding mode.
      <code>extras.isEdit</code> is applicable only to actions,
      determining whether it is recorded for replay for the
      <code>.</code> single-repeat command.
    </dl>

    <h3 id="vimapi_extending">Extending VIM</h3>

    <p>CodeMirror's VIM mode implements a large subset of VIM's core
    editing functionality. But since there's always more to be
    desired, there is a set of APIs for extending VIM's
    functionality. As with the configuration API, the methods are
    exposed on <code><strong>CodeMirror.Vim</strong></code> and may
    be called at any time.</p>

    <dl>
      <dt id="vimapi_defineOption"><code><strong>defineOption(name: string, default: any, type: string, ?aliases: array&lt;string&gt;, ?callback: function (?value: any, ?cm: CodeMirror) → ?any)</strong></code></dt>
      <dd>Defines a VIM style option and makes it available to the
      <code>:set</code> command. Type can be <code>boolean</code> or
      <code>string</code>, used for validation and by
      <code>:set</code> to determine which syntax to accept. If a
      <code>callback</code> is passed in, VIM does not store the value of the
      option itself, but instead uses the callback as a setter/getter. If the
      first argument to the callback is <code>undefined</code>, then the
      callback should return the value of the option. Otherwise, it should set
      instead. Since VIM options have global and instance values, whether a
      <code>CodeMirror</code> instance is passed in denotes whether the global
      or local value should be used. Consequently, it's possible for the
      callback to be called twice for a single <code>setOption</code> or
      <code>getOption</code> call. Note that right now, VIM does not support
      defining buffer-local options that do not have global values. If an
      option should not have a global value, either always ignore the
      <code>cm</code> parameter in the callback, or always pass in a
      <code>cfg.scope</code> to <code>setOption</code> and
      <code>getOption</code>.</dd>

      <dt id="vimapi_defineMotion"><code><strong>defineMotion(name: string, fn: function(cm: CodeMirror, head: {line, ch}, ?motionArgs: object}) → {line, ch})</strong></code></dt>
      <dd>Defines a motion command for VIM. The motion should return
      the desired result position of the cursor. <code>head</code>
      is the current position of the cursor. It can differ from
      <code>cm.getCursor('head')</code> if VIM is in visual mode.
      <code>motionArgs</code> is the object passed into
      <strong><code>mapCommand()</code></strong>.</dd>

      <dt id="vimapi_defineOperator"><strong><code>defineOperator(name: string, fn: function(cm: CodeMirror, ?operatorArgs: object, ranges: array&lt;{anchor, head}&gt;) → ?{line, ch})</code></strong></dt>
      <dd>Defines an operator command, similar to <strong><code>
      defineMotion</code></strong>. <code>ranges</code> is the range
      of text the operator should operate on. If the cursor should
      be set to a certain position after the operation finishes, it
      can return a cursor object.</dd>

      <dt id="vimapi_defineActon"><strong><code>defineAction(name: string, fn: function(cm: CodeMirror, ?actionArgs: object))</code></strong></dt>
      <dd>Defines an action command, similar to
      <strong><code>defineMotion</code></strong>. Action commands
      can have arbitrary behavior, making them more flexible than
      motions and operators, at the loss of orthogonality.</dd>

      <dt id="vimapi_defineEx"><strong><code>defineEx(name: string, ?prefix: string, fn: function(cm: CodeMirror, ?params: object))</code></strong></dt>
      <dd>Defines an Ex command, and maps it to <code>:name</code>.
      If a prefix is provided, it, and any prefixed substring of the
      <code>name</code> beginning with the <code>prefix</code> can
      be used to invoke the command. If the <code>prefix</code> is
      falsy, then <code>name</code> is used as the prefix. <code>
      params.argString</code> contains the part of the prompted
      string after the command name. <code>params.args</code> is
      <code>params.argString</code> split by whitespace. If the
      command was prefixed with a
      <code><strong><a href="http://vimdoc.sourceforge.net/htmldoc/cmdline.html#cmdline-ranges">line range</a></strong></code>,
      <code>params.line</code> and <code>params.lineEnd</code> will
      be set.
    </dl>

</section>

</article>

<script>setTimeout(function(){CodeMirror.colorize();}, 20);</script>
