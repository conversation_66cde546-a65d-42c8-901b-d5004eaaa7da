<%@page import="com.prinova.messagepoint.reports.model.LogEntry"%>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.testing" >


    <msgpt:Script>
        <script>
            $( function() {
                $("input:button").styleActionElement();
            });
        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:GetHibernateObject hibernateObjectName="${param.class}" id="${param.id}" outputName="command" />
<msgpt:GetHibernateObject hibernateObjectName="com.prinova.messagepoint.model.deliveryevent.DeliveryEvent" id="${param.deliveryid}" outputName="deliveryEvent" />
<msgpt:QueryHibernateObjects hqlQuery="from com.prinova.messagepoint.reports.model.JobMetadataDetail where jobId = ${deliveryEvent.job.id} order by part.sequence" outputName="list" />

<msgpt:BodyNew type="minimal">

	<!-- Report Control Bar -->
	<div style="border: 1px solid #bbb; border-left: none; border-right: none; background-color: #eee; padding: 5px;">
		<msgpt:FlowLayout align="right">
			<msgpt:FlowLayoutItem>
				<input title="${msgpt:getMessage('page.label.PRINT')}" type="button" onclick="window.print()" style="display: none;" />
			</msgpt:FlowLayoutItem>
			<msgpt:FlowLayoutItem>
				<input title="${msgpt:getMessage('page.label.CLOSE')}" type="button" onclick="window.close();" style="display: none;" />
			</msgpt:FlowLayoutItem>
		</msgpt:FlowLayout>
	</div>

	<msgpt:iFrameContainer>
		<msgpt:ContentPanel>
			<msgpt:ContentData title="page.label.delivery.logs">

				<c:if test="${command.error}">
					<msgpt:DataTable>
						<msgpt:TableHeader label="page.label.error" />
						<msgpt:TableItem>
							<c:out value='${msgpt:getMessage("page.text.delivery.error.part1")}' escapeXml="false" />
							<c:out value="${deliveryEvent.job.status.name}" />
							<c:out value='${msgpt:getMessage("page.text.delivery.error.part2")}' escapeXml="false" />
						</msgpt:TableItem>
					</msgpt:DataTable>
				</c:if>

				<c:set var="CRITICAL" value="<%= Integer.valueOf (LogEntry.LOG_TYPE_CRITICAL) %>" />
				<c:set var="ERROR" value="<%= Integer.valueOf (LogEntry.LOG_TYPE_ERROR) %>" />
				<c:set var="WARN" value="<%= Integer.valueOf (LogEntry.LOG_TYPE_WARN) %>" />
				<c:set var="INFO" value="<%= Integer.valueOf (LogEntry.LOG_TYPE_INFO) %>" />
				<c:set var="DEBUG" value="<%= Integer.valueOf (LogEntry.LOG_TYPE_DEBUG) %>" />

				<c:forEach var="detail" items="${list}">
					<c:choose>
						<c:when test="${empty detail.logEntries}">
							<msgpt:DataTable>
								<msgpt:TableHeader label="${detail.part.name }:&nbsp;${detail.returnCodeName}"/>
								<!-- No Log Messages -->
								<msgpt:TableItem><c:out value='${msgpt:getMessage("page.text.no.log.messages")}' /></msgpt:TableItem>
							</msgpt:DataTable>
						</c:when>
						<c:otherwise>
							<msgpt:DataTable listHeader="${detail.part.name }:&nbsp;${detail.returnCodeName}">
								<c:forEach var="logEntry" items="${detail.logEntries}" >
									<msgpt:TableListGroup>
										<!-- Label Type -->
										<msgpt:TableElement label="page.label.type">
											<c:choose>
												<c:when test="${logEntry.type eq CRITICAL || logEntry.type eq ERROR }">
													<img src="../includes/themes/commonimages/error.png" />
												</c:when>
												<c:when test="${logEntry.type eq WARN}">
													<img src="../includes/themes/commonimages/warn.png" />
												</c:when>
											</c:choose>
										</msgpt:TableElement>
										<!-- Log Message -->
										<msgpt:TableElement label="page.label.log.message" align="left">
											<c:out value="${logEntry.text}"/>
										</msgpt:TableElement>
										<!-- Time -->
										<msgpt:TableElement label="page.label.time">
											<fmtJSTL:formatDate value="${logEntry.logTime}" pattern="${dateTimeFormat}" />
										</msgpt:TableElement>
									</msgpt:TableListGroup>
								</c:forEach>
							</msgpt:DataTable>
						</c:otherwise>
					</c:choose>
				</c:forEach>
				
				<table width="30%" summary="Page Form" cellspacing="0" cellpadding="0" border="0" style="font-size: .8em;">
					<tbody>
						<tr class="contentTableHeaderTRsecond">
							<td style="padding: 2px;"><c:out value='${msgpt:getMessage("page.label.type")}' /></td>
							<td style="padding: 2px;"><c:out value='${msgpt:getMessage("page.label.name")}' /></td>
						</tr>
						<tr class="contentTableContentTREven">	
							<td><img src="../includes/themes/commonimages/warn.png" /></td>
							<td><c:out value='${msgpt:getMessage("page.label.warning")}' /></td>
						</tr>
						<tr class="contentTableContentTR">	
							<td><img src="../includes/themes/commonimages/error.png" /></td>
							<td><c:out value='${msgpt:getMessage("page.label.error")}' /></td>
						</tr>
					</tbody>
				</table>
				
			</msgpt:ContentData>
		</msgpt:ContentPanel>
	</msgpt:iFrameContainer>
</msgpt:BodyNew>
</msgpt:Html5>