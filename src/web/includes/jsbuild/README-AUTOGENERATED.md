***This directory contains compiled JavaScript! Do not edit directly!***

<pre>
    .-------.
  .'         `.
.'             `.
|.-. .-. .-. .-.|
|`-.  |  | | |-'|
|`-'  '  `-' '  |
'               '
 `.           .'.''.  .''.
   `._______.'   __   __
       | | .----/  \ /  \---.
       | | |   |    |    |  |____
       | | |   |`--''`--'| /  |  \_
     ,----.|   \  O | O  _ |  |  | \
     | ---'|    '._/ \_.| `|  |  | |
     \.---'|            |  | `- ,| |
      `---'|            | :        |
       | | |            |  '._.--  ;
       | | |    .      .:      `  /
       '-' |     '....'  `.______/
           |                |
           |                |
           `----------------'
               ||      ||
               ||      ||
        _.---'' '-, ,-' ''---._
       /      __..' '..__      \
       '---''`           `''---'

------------------------------------------------
</pre>

Any changes made to javascript files in this directory will be lost when the project is rebuilt!

To edit these files install the TypeScript compiler via npm

`npm install -g typescript`

Recommended editors are IntelliJ or Visual Studio code, edit files in this directory:

`../../../typescript/modules`

.tsconfig files specify compilation options, see editor documentation to enable compile on save of TypeScript files