table[t_align="right"] {
	float: right;
}
table[t_align="center"] {
	margin: 0px auto;
}

.staticContentItem[type="1"] { 
	background-color: #ffff55;
	vertical-align: middle;
}
.staticContentItem:not([type="1"]) { 
	background-color: #62f4eb;
	vertical-align: middle;
}
.staticContentItem[type="8"] { 
	background-color: #59c5ea; 
}
.staticContentItem[type="9"] { 
	background-color: #ff9fdd; 
}
.staticContentItem .staticContentItem[type="15"] { 
	background-color: #e09fe0;
}
nbsp {
	background-color: #ff4141;
}
p, ul, ol {
    font-size: 10.42pt;
    margin-top: 0px;
    margin-bottom: 0px;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
.contentContainer p, .contentContainer ul, .contentContainer ol {
	min-height: 12px;
}

.mce-widget button {
    height: 28px;
}

.mceDraggable {
	box-sizing: border-box;
	position: absolute;
	padding: 3px;
	border: 1px dashed #eee;
}
.mceDraggable:hover:not(.mceLockedContainer) {
	border: 1px solid #ccc;
	border-radius: 3px 3px 6px 6px;
	box-shadow: 1.5px 1.5px 1.5px #f5f5f5;
}

.mceDraggableSelected {
	border: 1px solid #ccc;
	border-radius: 3px 3px 6px 6px;
	box-shadow: 1.5px 1.5px 1.5px #f5f5f5;
}

.ui-drag-n {
	height: 6px;
	width: 100%;
	top: -9px;
	left: -1px;
}
.mceDraggable .ui-drag-n {
	cursor: move !important;
}
.mceInline .ui-drag-n {
	cursor: pointer !important;
}
.ui-drag-handle {
	z-index: 99;
	position: absolute;
	text-align: center;
	color: #000;
	font-size: 3px;
	display: block;
	border: 1px solid #ccc;
	background-color: #f5f5f5;
	border-radius: 3px 3px 0px 0px;
	background-image: linear-gradient(to bottom, #ffffff, #ccc);
	text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
	box-sizing: content-box;
}
.mceContentSelected .ui-drag-handle {
	background-color: #ccc;
	background-image: linear-gradient(to bottom, #ffffff, #bbb);
	color: #fff;
	text-shadow: 1px 1px 0 rgba(255, 255, 255, 1);
}
.mceContentSelected {
	border: 1px solid #d6d6d6;
}

.mceHiddenContent {
	display: none;
}

.staticContentItem .fa-mp-ed-ico {
	color: #414141;
}
.staticContentItem .fa-mp-ed-ico:not(.fa-stack) {
	position: relative;
	top: 1px;	
}
.staticContentItem .fa-mp-ed-ico.fa-stack {
	width: 1em;
	height: 1em;
	line-height: 1em;
}
.staticContentItem .fa-stack span.mce-ico-txt {
	font-size: 0.6em;
	font-weight: bold;
	top: -1px;
	left: -1px;
	font-family: "Times New Roman", Times, serif;
	font-style: normal;
	text-decoration: none;
	min-height: 0.6em;
}
.staticContentItem .mce-ico-txt.mce-ico-rev {
	color: #f9f9f9;
}

[rotate='90'] {
	-ms-transform: rotate(90deg); /* IE 9 */
	transform: rotate(90deg);
	transform-origin: top left;
	position: absolute;
	left: 100%;
}
[rotate='180'] {
	-ms-transform: rotate(180deg); /* IE 9 */
	transform: rotate(180deg);
	position: absolute;
}
[rotate='270'] {
	-ms-transform: rotate(270deg); /* IE 9 */
	transform: rotate(270deg);
	transform-origin: top left;
	position: absolute;
	top: 100%;
}