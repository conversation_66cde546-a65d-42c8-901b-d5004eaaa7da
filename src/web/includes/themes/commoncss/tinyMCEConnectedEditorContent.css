#main-container table[t_align="right"] {
	margin-left: auto;
	margin-right: 0px;
}
#main-container table[t_align="center"] {
	margin: 0px auto;
}

#main-container .staticContentItem[type="1"] {
	background-color: #ffff55;
	vertical-align: middle;
}
#main-container .staticContentItem:not([type="1"]) {
	background-color: #62f4eb;
	vertical-align: middle;
}
#main-container .staticContentItem[type="8"] {
	background-color: #59c5ea; 
}
#main-container .staticContentItem[type="9"] {
	background-color: #ff9fdd; 
}
#main-container .staticContentItem .staticContentItem[type="15"] {
	background-color: #e09fe0;
}
#main-container p, #main-container ul, #main-container ol {
    margin-top: 0px;
    margin-bottom: 0px;
}
#main-container table {
	border-collapse: collapse;
	border-spacing: 0;
}
#main-container p, #main-container ul, #main-container ol {
	min-height: 12px;
}

#main-container .mce-widget button {
    height: 28px;
}

#main-container .mceDraggable {
	box-sizing: border-box;
	position: absolute;
	padding: 3px;
	border: 1px dashed #eee;
}

#main-container .mceDraggable:hover:not(.mceLockedContainer) {
	border: 1px solid #ccc;
	border-radius: 3px 3px 6px 6px;
	box-shadow: 1.5px 1.5px 1.5px #f5f5f5;
}

#main-container .mceDraggableSelected {
	border: 1px solid #ccc;
	border-radius: 3px 3px 6px 6px;
	box-shadow: 1.5px 1.5px 1.5px #f5f5f5;
}

#main-container .ui-drag-n {
	height: 6px;
	width: 100%;
	top: -9px;
	left: -1px;
}
#main-container .mceDraggable .ui-drag-n {
	cursor: move !important;
}
#main-container .mceInline .ui-drag-n {
	cursor: pointer !important;
}
#main-container .ui-drag-handle {
	z-index: 99;
	position: absolute;
	text-align: center;
	color: #000;
	font-size: 3px;
	display: block;
	border: 1px solid #ccc;
	background-color: #f5f5f5;
	border-radius: 3px 3px 0px 0px;
	background-image: linear-gradient(to bottom, #ffffff, #ccc);
	text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
	box-sizing: content-box;
}
#main-container .mceContentSelected .ui-drag-handle {
	background-color: #ccc;
	background-image: linear-gradient(to bottom, #ffffff, #bbb);
	color: #fff;
	text-shadow: 1px 1px 0 rgba(255, 255, 255, 1);
}
#main-container .mceContentSelected {
	border: 1px solid #d6d6d6;
}

#main-container .mceHiddenContent {
	display: none;
}

#main-container .staticContentItem .fa-mp-ed-ico {
	color: #414141;
}
#main-container .staticContentItem .fa-mp-ed-ico:not(.fa-stack) {
	position: relative;
	top: 1px;	
}
#main-container .staticContentItem .fa-mp-ed-ico.fa-stack {
	width: 1em;
	height: 1em;
	line-height: 1em;
}
#main-container .staticContentItem .fa-stack span.mce-ico-txt {
	font-size: 0.6em;
	font-weight: bold;
	top: -1px;
	left: -1px;
	font-family: "Times New Roman", Times, serif;
	font-style: normal;
	text-decoration: none;
	min-height: 0.6em;
}
#main-container .staticContentItem .mce-ico-txt.mce-ico-rev {
	color: #f9f9f9;
}

#main-container [rotate='90'] {
	-ms-transform: rotate(90deg); /* IE 9 */
	transform: rotate(90deg);
	transform-origin: top left;
	position: absolute;
	left: 100%;
}
#main-container [rotate='180'] {
	-ms-transform: rotate(180deg); /* IE 9 */
	transform: rotate(180deg);
	position: absolute;
}
#main-container [rotate='270'] {
	-ms-transform: rotate(270deg); /* IE 9 */
	transform: rotate(270deg);
	transform-origin: top left;
	position: absolute;
	top: 100%;
}


/*default.css*/

#main-container p {
	line-height: 1.3;
}
#main-container li {
	line-height: 1;
}

#main-container td, #main-container th {
	vertical-align: top;
}

#main-container * {
	white-space: normal;
}

#main-container .mceHorizontalGuide, #main-container .mceVerticalGuide {
	position: absolute;
	z-index: 0;
}

#main-container .mceHorizontalGuide {
	left: 0px;
}

#main-container .mceVerticalGuide {
	top: 0px;
}

#main-container .mceGuideContainer {
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: -1;
}

#main-container table {
	border-collapse: separate;
	border-spacing: 0;
}

#main-container p:not([paragraphclass]) {
	min-height: 1em;
	margin-top: 0px;
	margin-bottom: 0px;
	line-height: 1em;
	font-weight: normal;
	font-family: Helvetica, sans-serif;
}
