<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>

<%@ include file="../includes/includes.jsp" %>
<c:set var="noVisibleTouchpoints" value="<%= !Document.existsDocumentsOrProjectsVisible() %>" />

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.touchpoints" extendedScripts="true">
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/taskManager/taskManager.css"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iFrameMenu/iFrameMenu.css"/>
        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/taskManager/jquery.taskManager.js"/>
        <msgpt:Script src="touchpoints/scripts/touchpoint_content_object_list.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/styleManager/jquery.styleManager.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/sharedTextFastEdit/jquery.sharedTextFastEdit.js"/>
        <msgpt:Script src="includes/javascript/mp.cardViewList.js"/>
        <msgpt:Script src="_ux/js/mp.postTrigger.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js"/>
        <msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.editorActions.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/scrollTo/jquery.scrollTo-2.1.2.min.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.varTagAttrManager.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.smartTextTagAttrManager.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.placeholderTagAttrManager.js"/>
        
        <msgpt:CalendarIncludes/>


        <msgpt:Script insertBeforeExternals="true">
            <script>
                var isVariantTouchpoint = ${isVariantTouchpoint ? 'true' : 'false'};
                var isMasterSelection = ${isMasterSelection ? 'true' : 'false'};
                var isStatusViewActive = ${isStatusViewActive ? 'true' : 'false'};
                var hasMetadataFormDef = ${hasMetadataFormDef ? 'true' : 'false'};
                var hasMessageEditPermission = ${hasMessageEditPermission ? 'true' : 'false'};
                var displaySegmentationAnalysis = ${displaySegmentationAnalysis ? 'true' : 'false'};
                var canUpdateVariant = ${canUpdateVariant ? 'true' : 'false'};
                var showContentJSONExport = ${showContentJSONExport ? 'true' : 'false'};
                var param = {};
                param.tk = '${param.tk}';
                param.documentId = '${param.documentId}';
            </script>
        </msgpt:Script>
        <style id="defaultContentViewStyles">
        	${default_css}
        </style>
    </msgpt:HeaderNew>
    <msgpt:BodyNew>
        <!-- FLAGS AND PERMISSIONS -->
        <c:set var="viewMessagePermission" value="false"/>
        <msgpt:IfAuthGranted authority="ROLE_MESSAGE_VIEW_MY">
            <c:set var="viewMessagePermission" value="true"/>
        </msgpt:IfAuthGranted>
        <msgpt:IfAuthGranted authority="ROLE_MESSAGE_VIEW_ALL">
            <c:set var="viewMessagePermission" value="true"/>
        </msgpt:IfAuthGranted>
        <c:if test="${viewMessagePermission}">
            <input type="hidden" id="viewMessagePermission"/>
        </c:if>
        <c:set var="viewMetatagsPermission" value="false"/>
        <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
            <c:set var="viewMetatagsPermission" value="true"/>
        </msgpt:IfAuthGranted>
        <!-- END FLAGS AND PERMISSIONS -->
        <c:if test="${isVariantTouchpoint}">
            <input type="hidden" id="isVariantTouchpoint"/>
        </c:if>
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
        <msgpt:ContextBarNew touchpointSelectionContext="true" channelContextApplied="true"
                             touchpointAttributesContextApplied="${(touchpointContext.emailTouchpoint || touchpointContext.webTouchpoint) && !isVariantTouchpoint}"/>
        <msgpt:LowerContainer fullPanel="true" extendedWidth="true" cssClass="fluid-content">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="true">
                <c:if test="${not trashTpContext }">
                    <form:form method="post" modelAttribute="command" cssClass="h-100">
                        <form:hidden path="exportId" id="exportId"/>
                        <form:hidden path="exportName" id="exportName"/>
                        <form:errors path="*">
                            <msgpt:Information errorMsgs="${messages}" type="error"/>
                        </form:errors>
                        <c:if test="${not (noTouchpoints and not trashTpContext) and not hasDefaultTouchpointLanguage}">
                            <msgpt:Information type="error">
                                <fmtSpring:message code="page.label.touchpoint.default.language.not_found"/>
                            </msgpt:Information>
                        </c:if>
                        <c:if test="${not empty param.saveSuccess && param.saveSuccess == 'true'}">
                            <msgpt:Information type="success">
                                <fmtSpring:message code="page.label.save.complete"/>
                            </msgpt:Information>
                        </c:if>
                        <c:if test="${not noVisibleTouchpoints}">
                            <c:if test="${isVariantTouchpoint}">
                                <div class="mt--3 mb-4 pb-2">
                                    <div class="d-flex justify-content-start align-items-center fs-md">
                                        <msgpt:Breadcrumb/>
                                        <c:if test="${hasTaskViewPermission}">
                                            <div id="taskIndicator" class="ml-3 position-relative text-primary"></div>
                                        </c:if>
                                        <c:if test="${!isMasterSelection && isEnabledForVariantWorkflow}">
                                            <div class="d-inline-block ml-3 text-dark fs-sm">
                                                <c:choose>
                                                    <c:when test="${isStatusViewActive}">
                                                        <c:choose>
                                                            <c:when test="${currentSelection.hasActiveCopy}">
                                                                <fmtSpring:message code="page.label.active"/>
                                                            </c:when>
                                                            <c:otherwise>
                                                        <span class="text-secondary"><fmtSpring:message
                                                                code="page.label.no.active.copy"/></span>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <c:choose>
                                                            <c:when test="${currentSelection.hasWorkingCopy}">
                                                                <c:choose>
                                                                    <c:when test="${currentSelection.releasedForApproval}">
                                                                        <fmtSpring:message code="page.label.pending"/>
                                                                        <msgpt:TxtFmt maxLength="30"><c:out
                                                                                value="${currentSelection.workflowAction.configurableWorkflowStep.state}"/></msgpt:TxtFmt>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <fmtSpring:message
                                                                                code="page.label.working.copy"/>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </c:when>
                                                            <c:otherwise>
                                                        <span class="text-secondary"><fmtSpring:message
                                                                code="page.label.no.working.copy"/></span>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </c:if>
                                    </div>
                                </div>
                            </c:if>
                            <div class="<c:if test="${not isVariantTouchpoint}">mt-4 pt-2 </c:if>h-100">
                                <div class="row flex-nowrap">
                                    <div id="widgetContainer" class="col-auto pb-2 mb-4">
                                        <div class="box-shadow-4 rounded" style="width: 20rem;">
                                            <div class="d-flex btn-group border-separate" role="group"
                                                 aria-label="${msgpt:getMessage("page.label.widget.controls")}">
                                                <div class="btn-group col-6 px-0" role="group"
                                                     aria-label="${msgpt:getMessage("page.label.touchpoint.section.toggle")}"
                                                     data-toggle="tooltip"
                                                     data-delay='{"show": 500, "hide": 0}'
                                                     title="${msgpt:getMessage('page.label.all.sections')}">
                                                    <select id="sectionListSelect"
                                                            class="complex-dropdown-select persistedValue"
                                                            aria-label="${msgpt:getMessage("page.label.section")}"
                                                            data-toggle="complex-dropdown"
                                                            data-enablefilter="true"
                                                            data-dropdown-class="btn-lg btn-blank w-100 dropdown-flexblock">
                                                        <option class="fixedOption font-weight-bold" value="0"><fmtSpring:message
                                                                code="page.label.all.sections"/>
                                                        </option>
                                                        <c:forEach items="${layoutSections}" var="currentSection">
                                                            <option id="section_${currentSection.id}"
                                                                    value="${currentSection.id}">${currentSection.name}
                                                            </option>
                                                        </c:forEach>
                                                    </select>
                                                </div>
                                                <div class="btn-group col-6 px-0" role="group"
                                                     aria-label="${msgpt:getMessage('page.label.touchpoint.zone.toggle')}"
                                                     data-toggle="tooltip"
                                                     data-delay='{"show": 500, "hide": 0}'
                                                     title="${msgpt:getMessage('page.text.all.zones')}">
                                                    <select id="zoneSelect"
                                                            class="complex-dropdown-select persistedValue"
                                                            aria-label="${msgpt:getMessage('page.label.zone')}"
                                                            data-toggle="complex-dropdown"
                                                            data-enablefilter="true"
                                                            data-dropdown-class="btn-lg btn-blank w-100 dropdown-flexblock">
                                                        <option class="fixedOption font-weight-bold"
                                                                value="0"
                                                                data-sectionId="unset"><fmtSpring:message
                                                                code="page.text.all.zones"/>
                                                        </option>
                                                        <c:forEach items="${layoutZones}" var="currentZone">
                                                            <option id="zone_${currentZone.id}"
                                                                    value="${currentZone.id}"
                                                                    data-sectionId="${currentZone.section.id}">
                                                                <c:out value="${currentZone.friendlyName}"/>
                                                            </option>
                                                        </c:forEach>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="touchpointWidgetContainer" class="mt-2 pt-4">
                                            <!-- DOCUMENT WIDGET -->
                                            <div style="width: 20rem;">
                                                <iframe
                                                        id="touchpointNavigationWidget"
                                                        title="${msgpt:getMessage('page.label.touchpoint.widget')}"
                                                        name="touchpointNavigationWidget"
                                                        src="touchpoint_widget.form?documentId=${currentLayout.id}&touchpointSelectionId=${not empty param.touchpointSelectionId ? param.touchpointSelectionId : -1}${canUpdateVariant ? '&contentActions=true' : ''}&tk=${param.tk}"
                                                        allowtransparency="true"
                                                        frameborder="0"
                                                        style="width: 22rem; height: 24rem; margin: 0 0 0 -1rem; padding: 0;"
                                                        scrolling="no">
                                                </iframe>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div id="infoMsg_invalidFilterSetting"
                                             class="alert alert-info alert-dismissible fade show d-none" role="alert">
                                            <strong class="mr-2">
                                                <i class="fas fa-info-circle fa-lg mr-2"
                                                   aria-hidden="true"></i><fmtSpring:message
                                                    code="page.label.info"/>:
                                            </strong>
                                            <fmtSpring:message code="page.text.my.messages.filter.does.not.apply"/>
                                            <button type="button" class="close mt-1" data-dismiss="alert"
                                                    aria-label="Close">
                                                <i class="far fa-times" aria-hidden="true"></i>
                                            </button>
                                        </div>
                                        <c:if test="${not isContextActiveStatusValid}">
                                            <div class="alert alert-info" role="alert">
                                                <strong class="mr-2">
                                                    <i class="fas fa-info-circle fa-lg mr-2"
                                                       aria-hidden="true"></i><fmtSpring:message
                                                        code="page.label.info"/>:
                                                </strong>
                                                <fmtSpring:message code="page.text.variant.is.not.active"/>
                                            </div>
                                        </c:if>
                                        <c:if test="${not isContextWorkingCopyStatusValid}">
                                            <div class="alert alert-info" role="alert">
                                                <strong class="mr-2">
                                                    <i class="fas fa-info-circle fa-lg mr-2"
                                                       aria-hidden="true"></i><fmtSpring:message
                                                        code="page.label.info"/>:
                                                </strong>
                                                <fmtSpring:message code="page.text.variant.has.no.working.copy"/>
                                            </div>
                                        </c:if>
                                        <div class="box-shadow-4 rounded bg-white p-4">
                                            <div class="px-2 pb-1">
                                                <h1 class="h4 d-flex align-items-center"><fmtSpring:message
                                                        code="page.label.messages"/>
                                                    <button id="widgetToggleBtn"
                                                            class="btn btn-link btn-link-inline ml-auto fs-xs font-weight-bold text-uppercase text-dark persistedClass"
                                                            data-toggle="button" aria-pressed="false">
                                                        <i id="widgetToggleBtnIcon"
                                                           class="btn-icon fas fa-expand-wide fa-lg mr-2 persistedClass"></i>
                                                        <span class="btn-text"><fmtSpring:message
                                                                code="client_messages.text.expand"/></span>
                                                    </button>
                                                </h1>
                                                <div class="my-4">
                                                    <div class="d-flex align-items-center mb-3">
                                                        <form:hidden path="actionValue" id="actionElement"/>
                                                        <msgpt:IfAuthGranted authority="ROLE_MESSAGE_EDIT">
                                                            <div class="mr-3" data-toggle="tooltip" data-html="true"
                                                                 tabindex="0"
                                                                 title="<i class='fas fa-info-circle mr-2' aria-hidden='true'></i>${msgpt:getMessage('page.text.select.zone.to.add.message')}">
                                                                <button id="addMessageBtn" class="btn btn-primary"
                                                                        type="button" disabled>
                                                                    <i class="far fa-plus-circle mr-2"
                                                                       aria-hidden="true"></i>
                                                                    <fmtSpring:message code="page.label.add"/>
                                                                </button>
                                                            </div>
                                                        </msgpt:IfAuthGranted>
                                                        <div class="btn-group border-separate mr-auto" role="group"
                                                             aria-label="${msgpt:getMessage("page.label.actions")}">
                                                            <button id="updateBtn" type="button" class="btn btn-dark post-trigger"
                                                                    onclick="iFrameAction(1);" disabled>
                                                                <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                                                <fmtSpring:message code="action.button.label.update"/>
                                                            </button>
                                                            <button id="cloneBtn" type="button" class="btn btn-dark"
                                                                    onclick="actionSelected(23);" disabled>
                                                                <i class="far fa-clone mr-2" aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.clone"/>
                                                            </button>
                                                            <c:if test="${viewMessagePermission}">
                                                                <div class="btn-group">
                                                                    <select title="${msgpt:getMessage('page.label.audit')}"
                                                                            id="exportMenu"
                                                                            class="complex-dropdown-select"
                                                                            onchange="infoItemAction(this,-1)"
                                                                            aria-label="${msgpt:getMessage('page.label.audit')}"
                                                                            data-toggle="complex-dropdown"
                                                                            data-action="menu"
                                                                            data-dropdown-class="btn-dark">
                                                                        <option id="actionOption_11"><fmtSpring:message
                                                                                code="page.label.generate.message.report"/></option>
                                                                        <c:if test="${not empty auditReport}">
                                                                            <option class="divider" disabled/>
                                                                            <c:choose>
                                                                                <c:when test="${not auditReport.complete && not auditReport.error}"> <!-- Audit Report in process -->
                                                                                    <option id="itemInProcess_${auditReport.id}"
                                                                                            type="inProcess" disabled>
                                                                                        <fmtSpring:message
                                                                                                code="page.label.processing"/>
                                                                                        -
                                                                                        <fmtJSTL:formatDate
                                                                                                value="${auditReport.requestDate}"
                                                                                                pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${not auditReport.complete && auditReport.error}"> <!-- Audit Report error -->
                                                                                    <option id="itemError_${auditReport.id}"
                                                                                            deliveryId="-1"
                                                                                            itemClass="null"
                                                                                            class="text-danger">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.error"/>
                                                                                        -
                                                                                        <fmtJSTL:formatDate
                                                                                                value="${auditReport.requestDate}"
                                                                                                pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:otherwise> <!-- View report -->
                                                                                    <option id="itemReport_${auditReport.id}"
                                                                                            value="${auditReport.reportPath}">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.view.audit.report"/>
                                                                                        -
                                                                                        <fmtJSTL:formatDate
                                                                                                value="${auditReport.requestDate}"
                                                                                                pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                        </c:if>
                                                                    </select>
                                                                </div>
                                                            </c:if>
                                                            <div class="btn-group">
                                                                <select title="${msgpt:getMessage('page.label.more')}"
                                                                        id="actionMenu"
                                                                        class="complex-dropdown-select"
                                                                        aria-label="${msgpt:getMessage('page.label.more')}"
                                                                        data-toggle="complex-dropdown"
                                                                        data-action="menu"
                                                                        data-dropdown-class="btn-dark"
                                                                        onchange="actionMenuSelected(this)">
                                                                    <option id="actionOption_1" disabled>
                                                                        <fmtSpring:message
                                                                                code="page.label.create.working.copy"/></option>
                                                                    <option id="actionOption_19"
                                                                            class="d-none"
                                                                            disabled><fmtSpring:message
                                                                            code="page.label.activate"/></option>
                                                                    <option id="actionOption_6" disabled>
                                                                        <fmtSpring:message
                                                                                code="page.label.release.for.approval"/></option>
                                                                    <option id="actionOption_17" disabled>
                                                                        <fmtSpring:message
                                                                                code="page.label.approve"/></option>
                                                                    <option id="actionOption_30"
                                                                            class="d-none"
                                                                            disabled><fmtSpring:message
                                                                            code="page.label.release.from.translation"/></option>
                                                                    <option id="actionOption_18" disabled>
                                                                        <fmtSpring:message
                                                                                code="page.label.reject"/></option>
                                                                    <option id="actionOption_20"
                                                                            class="d-none"
                                                                            disabled><fmtSpring:message
                                                                            code="page.label.approve.and.override"/></option>
                                                                    <option id="actionOption_21"
                                                                            class="d-none"
                                                                            disabled><fmtSpring:message
                                                                            code="page.label.reject.and.override"/></option>
                                                                    <option class="divider" disabled/>
                                                                    <option id="actionOption_5" disabled>
                                                                        <fmtSpring:message
                                                                                code="page.label.reassign"/></option>
                                                                    <option id="actionOption_41" disabled> <!-- Translation Reassign -->
                                                                        <fmtSpring:message
                                                                                code="page.label.reassign"/></option>
                                                                    <option id="actionOption_43" disabled>
                                                                        <fmtSpring:message
                                                                                code="page.label.content.export"/></option>
                                                                    <option id="actionOption_40" disabled>
                                                                        <fmtSpring:message
                                                                                code="page.label.abort"/></option>
                                                                    <option id="actionOption_45" disabled>
                                                                        <fmtSpring:message
                                                                                code="page.label.retry.translation"/></option>
                                                                    <option id="actionOption_2"
                                                                            class="d-none" disabled><fmtSpring:message
                                                                            code="page.label.discard.working.copy"/></option>
                                                                    <c:if test="${not touchpointContext.smsTouchpoint}">
                                                                        <option id="actioniFrame_22" disabled>
                                                                            <fmtSpring:message
                                                                                    code="page.label.preview"/></option>
                                                                    </c:if>
                                                                    <option id="actioniFrame_28" disabled>
                                                                        <fmtSpring:message
                                                                                code="page.label.move"/></option>
                                                                    <c:if test="${isVariantTouchpoint}">
                                                                        <option id="actionOption_24" disabled>
                                                                            <fmtSpring:message
                                                                                    code="page.label.hold"/></option>
                                                                        <option id="actionOption_25" disabled>
                                                                            <fmtSpring:message
                                                                                    code="page.label.unhold"/></option>
                                                                    </c:if>
                                                                    <msgpt:IfAuthGranted
                                                                            authority="ROLE_MESSAGE_ACTIVATION">
                                                                        <option id="actionOption_26" disabled>
                                                                            <fmtSpring:message
                                                                                    code="page.label.suppress"/></option>
                                                                        <option id="actionOption_27" disabled>
                                                                            <fmtSpring:message
                                                                                    code="page.label.restore"/></option>
                                                                    </msgpt:IfAuthGranted>
                                                                    <msgpt:IfAuthGranted
                                                                            authority="ROLE_MESSAGE_ACTIVATION">
                                                                        <option id="actionOption_3" disabled>
                                                                            <fmtSpring:message
                                                                                    code="page.label.archive"/></option>
                                                                        <option id="actionOption_4"
                                                                                class="d-none"
                                                                                disabled><fmtSpring:message
                                                                                code="page.label.delete.archive"/></option>
                                                                    </msgpt:IfAuthGranted>
                                                                    <option class="divider" disabled/>
                                                                    <c:if test="${not hasTaskMetadataFormDef}">
                                                                        <option id="actioniFrame_38" disabled>
                                                                            <fmtSpring:message
                                                                                    code="page.label.add.task"/></option>
                                                                    </c:if>
                                                                    <c:if test="${hasTaskMetadataFormDef}">
                                                                        <option id="actionOption_39" disabled>
                                                                            <fmtSpring:message
                                                                                    code="page.label.add.task"/></option>
                                                                    </c:if>
                                                                </select>
                                                            </div>
                                                        </div>
				                                        <div class="mr-3 ml-5" data-toggle="tooltip"
				                                             title="${msgpt:getMessage('client_messages.label.toggle_list_display')}">
                                                            <div class="btn-group">
                                                                <button id="listTypeToggleMessageList" type="button"
                                                                        data-toggle="button"
                                                                        aria-pressed="false"
                                                                        class="btn btn-link btn-link-inline mark-pressed text-dark persistedClass cardViewToggle">
                                                                    <i class="far fa-th fa-lg" aria-hidden="true"></i>
                                                                    <span class="sr-only"><fmtSpring:message
                                                                            code="client_messages.label.toggle_list_display"/></span>
                                                                </button>
                                                                <button type="button" class="btn dropdown-toggle dropdown-toggle-split cardViewVisToggle" style="display: none; padding-right: 0.5rem; padding-left: 0.5rem" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                    <span class="sr-only">Toggle Dropdown</span>
                                                                </button>
                                                                <div class="dropdown-menu dropdown-custom">
                                                                    <div class="dropdown-item">
                                                                        <div class="custom-control custom-checkbox">
                                                                            <input type="checkbox" class="custom-control-input persistedCheckbox" checked="checked" id="cardDisplayToggle_name">
                                                                            <label class="custom-control-label" for="cardDisplayToggle_name"><fmtSpring:message code="page.label.name"/></label>
                                                                        </div>
                                                                    </div>
                                                                    <div class="dropdown-item">
                                                                        <div class="custom-control custom-checkbox">
                                                                            <input type="checkbox" class="custom-control-input persistedCheckbox" checked="checked" id="cardDisplayToggle_content">
                                                                            <label class="custom-control-label" for="cardDisplayToggle_content"><fmtSpring:message code="page.label.content"/></label>
                                                                        </div>
                                                                    </div>
                                                                    <div class="dropdown-item">
                                                                        <div class="custom-control custom-checkbox">
                                                                            <input type="checkbox" class="custom-control-input persistedCheckbox" checked="checked" id="cardDisplayToggle_details">
                                                                            <label class="custom-control-label" for="cardDisplayToggle_details"><fmtSpring:message code="page.label.details"/></label>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
				                                        </div>
                                                        <div class="mr-3 colVisToggleContainer" data-toggle="tooltip"
                                                             title="${msgpt:getMessage('client_messages.label.toggle_columns')}">
                                                        </div>
                                                        <div class="form-group position-relative d-inline-block m-0">
                                                            <label for="listSearchInput"
                                                                   class="sr-only"><fmtSpring:message
                                                                    code="page.label.search"/></label>
                                                            <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                                               style="z-index: 1;" aria-hidden="true"></i>
                                                            <msgpt:InputFilter type="description">
                                                                <input id="listSearchInput" type="text"
                                                                       class="form-control bg-lightest has-control-x border-0"
                                                                    ${viewMetatagsPermission ? 'data-toggle="tagcloud"' :''}
                                                                       data-cloud-type="1"
                                                                       data-document-id="${document.id}"
                                                                       placeholder="${msgpt:getMessage('page.label.search')}"
                                                                       size="20"/>
                                                            </msgpt:InputFilter>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex align-items-center pt-1">
                                                     <span class="text-dark mr-1" id="filter">
                                                        <fmtSpring:message code="page.label.filter"/>:
                                                    </span>
                                                        <div class="mx-2">
                                                            <select id="messagesListAssignmentFilter"
                                                                    aria-labelledby="filter"
                                                                    data-toggle="complex-dropdown"
                                                                    data-menu-class="dropdown-custom mt-2"
                                                                    class="complex-dropdown-select persistedValue"
                                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                                    onchange="toggleFilter(this)">
                                                                <c:forEach items="${primaryFilterTypes}"
                                                                           var="currentFilter">
                                                                    <option id="messagesListAssignmentFilter_${currentFilter.id}"
                                                                            value="${currentFilter.id}">
                                                                        <fmtSpring:message
                                                                                code="${currentFilter.displayMessageCode}"/></option>
                                                                </c:forEach>
                                                            </select>
                                                        </div>
                                                        <span class="text-dark mx-1" id="message-status">
                                                        <fmtSpring:message code="page.text.messages.which.are"/>
                                                    </span>
                                                        <div class="mx-2">
                                                            <form:select id="contentObjectListStatusFilter"
                                                                    path="statusFilterId"
                                                                    class="complex-dropdown-select persistedValue"
                                                                    aria-labelledby="message-status"
                                                                    data-toggle="complex-dropdown"
                                                                    data-menu-class="dropdown-custom mt-2"
                                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                                    onchange="toggleFilter(this)">
                                                                <c:forEach items="${messageStatusFilterTypes}"
                                                                           var="currentFilter">
                                                                    <form:option id="contentObjectListStatusFilter_${currentFilter.id}"
                                                                            value="${currentFilter.id}">
                                                                        <fmtSpring:message
                                                                                code="${currentFilter.displayMessageCode}"/></form:option>
                                                                </c:forEach>
                                                            </form:select>
                                                        </div>
                                                        <c:choose>
                                                            <c:when test="${document.enabledForVariation}">
                                                            <span class="text-dark mx-1" id="content-type">
                                                                <fmtSpring:message code="page.text.with"/>
                                                            </span>
                                                                <div class="mx-2">
                                                                    <select id="contentListTypeFilter"
                                                                            class="complex-dropdown-select persistedValue"
                                                                            aria-labelledby="content-type"
                                                                            data-toggle="complex-dropdown"
                                                                            data-menu-class="dropdown-custom mt-2"
                                                                            data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                                            onchange="toggleFilter(this)">
                                                                        <c:forEach items="${contentTypeFilterTypes}"
                                                                                   var="currentFilter">
                                                                            <option id="contentListTypeFilter_${currentFilter.id}"
                                                                                    value="${currentFilter.id}">
                                                                                <fmtSpring:message
                                                                                        code="${currentFilter.displayMessageCode}"/></option>
                                                                        </c:forEach>
                                                                    </select>
                                                                </div>
                                                                <span class="text-dark ml-1">
                                                                <fmtSpring:message code="page.text.content"/>
                                                            </span>
                                                            </c:when>
                                                        </c:choose>
                                                        <span class="text-dark mx-1 cardSortInterface" id="sorted-by" style="display: none;">
                                                        	<fmtSpring:message code="page.text.sorted.by"/>
                                                       	</span>
                                                     	<div class="mx-2 cardSortInterface" style="display: none;">
                                                     		<select id="messageListSortBySelect"
                                                                    class="complex-dropdown-select persistedValue"
                                                                    aria-labelledby="sorted-by"
                                                                    data-toggle="complex-dropdown"
                                                                    data-menu-class="dropdown-custom mt-2"
                                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                                    onchange="toggleSort(this)">
                                                     		</select>
                                               			</div>
                                                    </div>
                                                </div>
                                                <div class="bg-white">
                                                    <msgpt:DataTable id="messageList" listHeader="page.label.messages"
                                                                     async="true"
                                                                     columnReorder="true" numUnreorderableCols="2"
                                                                     columnVisibility="true" drillDown="true"
                                                                     multiSelect="true"
                                                                     searchFilter="true">
                                                    </msgpt:DataTable>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- POPUP DATA -->
                            <div id="actionSpecs" style="display: none;">
                                <!-- ACTIONS POPUP DATA -->
                                <div id="actionSpec_1" type="simpleConfirm" submitId="4"> <!-- Create working copy -->
                                    <div id="actionTitle_1"><fmtSpring:message
                                            code="page.label.confirm.create.working.copy"/></div>
                                    <div id="actionInfo_1"><p><b><fmtSpring:message
                                            code="page.text.create.working.copies.of.selected.messages"/></b></p></div>
                                    <div id="actionContinueToTaskSelectButtons_1"></div>
                                </div>
                                <div id="actionSpec_2" type="simpleConfirm" submitId="5"> <!-- Discard working copy -->
                                    <div id="actionTitle_2"><fmtSpring:message
                                            code="page.label.confirm.discard.working.copy"/></div>
                                    <div id="actionInfo_2"><p><b><fmtSpring:message
                                            code="page.text.discard.message.working.copy"/></b></p></div>
                                </div>
                                <div id="actionSpec_3" submitId="6"> <!-- Archive -->
                                    <div id="actionTitle_3"><fmtSpring:message code="page.label.confirm.archive"/></div>
                                    <div id="actionInfo_3"><p><b><fmtSpring:message
                                            code="page.text.archive.selected.active.messages"/></b></p></div>
                                    <div id="actionNote_3" required="true"><fmtSpring:message
                                            code="page.text.note.required.brackets"/></div>
                                </div>
                                <div id="actionSpec_4" type="simpleConfirm" submitId="7"> <!-- Delete archive -->
                                    <div id="actionTitle_4"><fmtSpring:message
                                            code="page.label.confirm.delete.archive"/></div>
                                    <div id="actionInfo_4"><p><b><fmtSpring:message
                                            code="page.text.delete.selected.archived.messages"/></b></p></div>
                                </div>
                                <div id="actionSpec_5" submitId="3"> <!-- Reassign to user -->
                                    <div id="actionTitle_5"><fmtSpring:message
                                            code="page.label.confirm.reassign.to.user"/></div>
                                    <div id="actionInfo_5"><p><b><fmtSpring:message
                                            code="page.text.assign.message.to.user"/></b></p></div>
                                    <div id="actionNote_5"></div>
                                    <div id="actionUserSelect_5" required="true" type="currentStageUsers"></div>
                                </div>
                                <div id="actionSpec_41" submitId="41"> <!-- Translation Reassign to user -->
                                    <div id="actionTitle_41"><fmtSpring:message
                                            code="page.label.confirm.reassign.to.user"/></div>
                                    <div id="actionInfo_41"><p><b><fmtSpring:message
                                            code="page.text.assign.message.to.user"/></b></p></div>
                                    <div id="actionNote_41"></div>
                                    <div id="actionWorkflowSelect_41" required="true" type="messages_releaseFromTransWorkflows"></div>
                                    <div id="actionUserSelect_41" required="true" type="currentTranslators"></div>
                                </div>
                                <div id="actionSpec_6" submitId="9"> <!-- Release for approval -->
                                    <div id="actionTitle_6"><fmtSpring:message
                                            code="page.label.confirm.release.for.approval"/></div>
                                    <div id="actionInfo_6"><p><b><fmtSpring:message
                                            code="page.text.provide.description.of.action.required"/></b></p></div>
                                    <div id="actionNote_6"></div>
                                </div>
                                <!-- APPROVE MESSAGE(S) -->
                                <div id="actionSpec_17" type="simpleConfirm" submitId="17">
                                    <!-- Approve -->
                                    <div id="actionTitle_17"><fmtSpring:message code="page.label.approve"/></div>
                                    <div id="actionInfo_17"><p><b><fmtSpring:message
                                            code="page.text.would.you.like.to.approve"/></b></p></div>
                                    <div id="actionNote_17"></div>
                                    <div id="actionWorkflowSelect_17" required="true" type="messages_approveWorkflows"></div>
                                    <div id="actionApproval_17" approveSubmitId="17"></div>
                                </div>
                                <!-- Translation Step approve -->
                                <div id="actionSpec_30" type="simpleConfirm" submitId="17">
                                    <!-- Approve -->
                                    <div id="actionTitle_30"><fmtSpring:message
                                            code="page.label.approve.workflow.translation"/></div>
                                    <div id="actionInfo_30"><p><b><fmtSpring:message
                                            code="page.text.workflow.step.release.from.translation"/></b></p></div>
                                    <div id="actionWorkflowSelect_30" required="true" type="messages_releaseFromTransWorkflows"></div>
                                    <div id="actionReleaseFromTranslation_30" approveSubmitId="17"></div>
                                </div>
                                <!-- REJECT MESSAGE(S) -->
                                <div id="actionSpec_18" type="simpleConfirm" submitId="18">
                                    <!-- Reject -->
                                    <div id="actionTitle_18"><fmtSpring:message code="page.label.reject"/></div>
                                    <div id="actionInfo_18"><p><b><fmtSpring:message
                                            code="page.text.would.you.like.reject"/></b></p></div>
                                    <div id="actionNote_18" required="true"><fmtSpring:message
                                            code="page.text.note.required.to.reject.brackets"/></div>
                                    <div id="actionWorkflowSelect_18" required="true" type="messages_rejectWorkflows"></div>
                                    <div id="actionUserSelect_18" required="true" type="message_rejectToUsers"></div>
                                    <div id="actionReject_18" rejectSubmitId="18"></div>
                                </div>
                                <!-- Activate -->
                                <div id="actionSpec_19" submitId="10">
                                    <div id="actionTitle_19"><fmtSpring:message
                                            code="page.label.confirm.activate"/></div>
                                    <div id="actionInfo_19"><p><b><fmtSpring:message
                                            code="page.text.activate.messages"/></b></p></div>
                                    <div id="actionNote_19"></div>
                                </div>
                                <!-- Override approve -->
                                <div id="actionSpec_20" type="simpleConfirm" submitId="17">
                                    <!-- Approve -->
                                    <div id="actionTitle_20"><fmtSpring:message
                                            code="page.label.approve.workflow.owner"/></div>
                                    <div id="actionInfo_20"><p><b><fmtSpring:message
                                            code="page.text.workflow.owners.may.approve.step"/></b></p></div>
                                    <div id="actionWorkflowSelect_20" required="true" type="messages_approveWorkflows"></div>
                                    <div id="actionApproval_20" approveSubmitId="17"></div>
                                </div>
                                <!-- Override reject -->
                                <div id="actionSpec_21" type="simpleConfirm" submitId="18">
                                    <!-- Reject -->
                                    <div id="actionTitle_21"><fmtSpring:message
                                            code="page.label.reject.workflow.owner"/></div>
                                    <div id="actionInfo_21"><p><b><fmtSpring:message
                                            code="page.text.workflow.owners.may.reject.step"/></b></p></div>
                                    <div id="actionNote_21" required="true"><fmtSpring:message
                                            code="page.text.note.required.to.reject.brackets"/></div>
                                    <div id="actionWorkflowSelect_21" required="true" type="messages_rejectWorkflows"></div>
                                    <div id="actionUserSelect_21" required="true" type="message_rejectToUsers"></div>
                                    <div id="actionReject_21" rejectSubmitId="18"></div>
                                </div>
                                <!-- AUDIT POPUP DATA -->
                                <div id="actionSpec_11" submitId="16">
                                    <!-- Generate message report -->
                                    <div id="actionTitle_11"><fmtSpring:message
                                            code="page.label.generate.message.report"/></div>
                                    <div id="actionInfo_11"><p><b><fmtSpring:message
                                            code="page.text.set.options.for.audit.report"/></b></p></div>
                                    <div id="actionReportOptions_11"></div>
                                </div>
                                <!-- Clone -->
                                <div id="actionSpec_23" submitId="2" contentWidth="500px"> <!-- Clone -->
                                    <div id="actionTitle_23"><fmtSpring:message code="page.label.confirm.clone"/></div>
                                    <div id="actionInfo_23"><p><b><fmtSpring:message
                                            code="page.text.clone.selected.message"/></b></p></div>
                                    <div id="actionClone_23" required="true"></div>
                                </div>
                                <!-- Content Export -->
                                <div id="actionSpec_43" submitId="43"> <!-- Content Export -->
                                    <div id="actionTitle_43"><fmtSpring:message code="page.label.confirm.content.export"/></div>
                                    <div id="actionInfo_43"><p><b><fmtSpring:message
                                            code="page.text.content.export.selected.messages"/></b></p></div>
                                    <div id="actionPopupContentJSONExport_43"></div>
                                </div>
                                <c:if test="${isVariantTouchpoint}">
                                    <!-- Hold -->
                                    <div id="actionSpec_24" submitId="24"> <!-- Hold -->
                                        <div id="actionTitle_24"><fmtSpring:message
                                                code="page.label.confirm.hold"/></div>
                                        <div id="actionInfo_24"><p><b><fmtSpring:message
                                                code="page.text.on.hold.messages.not.used.in.production"/></b></p></div>
                                        <div id="actionSuppress_24"></div>
                                    </div>
                                    <!-- Unhold -->
                                    <div id="actionSpec_25" submitId="25"> <!-- Unhold -->
                                        <div id="actionTitle_25"><fmtSpring:message
                                                code="page.label.confirm.unhold"/></div>
                                        <div id="actionInfo_25"><p><b><fmtSpring:message
                                                code="page.text.selected.messages.now.used.in.production"/></b></p>
                                        </div>
                                        <c:if test="${isEnabledForVariantWorkflow}">
                                            <div id="actionUpdateVariantVersioning_25"></div>
                                        </c:if>
                                    </div>
                                </c:if>
                                <msgpt:IfAuthGranted authority="ROLE_MESSAGE_ACTIVATION">
                                    <!-- Suppress -->
                                    <div id="actionSpec_26" submitId="26"> <!-- Suppress -->
                                        <div id="actionTitle_26"><fmtSpring:message
                                                code="page.label.confirm.suppress"/></div>
                                        <div id="actionInfo_26"><p><b><fmtSpring:message
                                                code="page.text.suppress.selected.messages"/></b></p></div>
                                    </div>
                                    <!-- Restore -->
                                    <div id="actionSpec_27" submitId="27"> <!-- Restore -->
                                        <div id="actionTitle_27"><fmtSpring:message
                                                code="page.label.confirm.restore"/></div>
                                        <div id="actionInfo_27"><p><b><fmtSpring:message
                                                code="page.text.restore.selected.messages"/></b></p></div>
                                    </div>
                                </msgpt:IfAuthGranted>
                                <div id="actionSpec_29"> <!-- Add with metadata -->
                                    <div id="actionTitle_29"><fmtSpring:message code="page.label.add.message"/></div>
                                    <div id="actionMetadataSelect_29"></div>
                                    <div id="actionMetadataButtons_29"></div>
                                </div>
                                <div id="actionSpec_39"> <!-- Add task with metadata -->
                                    <div id="actionTitle_39"><fmtSpring:message code="page.label.add.task"/></div>
                                    <div id="actionTaskMetadataSelect_39"></div>
                                    <div id="actionTaskMetadataButtons_39"></div>
                                </div>
                                <div id="actionSpec_40" submitId="40"> <!-- Abort workflow -->
                                    <div id="actionTitle_40"><fmtSpring:message code="page.label.abort"/></div>
                                    <div id="actionInfo_40"><p><b><fmtSpring:message
                                            code="page.text.would.you.like.to.abort"/></b></p></div>
                                    <div id="actionNote_40"></div>
                                    <div id="actionAbort_40" abortSubmitId="40"></div>
                                </div>
                                <div id="actionSpec_45" submitId="45"> <!-- Retry workflow -->
                                    <div id="actionTitle_45"><fmtSpring:message code="page.label.retry.translation"/></div>
                                    <div id="actionInfo_45"><p><b><fmtSpring:message
                                            code="page.text.would.you.like.to.retry.translation"/></b></p></div>
                                    <div id="actionNote_45"></div>
                                    <div id="actionWorkflowSelect_45" required="true" type="messages_retryWorkflows"></div>
                                </div>
                                <div id="actionSpec_46" submitId="4"> <!-- Link task -->
                                    <div id="actionTitle_46"><fmtSpring:message code="page.label.link.task"/></div>
                                    <div id="actionInfo_46"><p><b><fmtSpring:message
                                            code="page.text.would.you.like.to.link.with.an.existing.task"/></b></p></div>
                                    <div id="actionTaskSelect_46" required="true"></div>
                                    <c:if test="${requiresDescriptionOnTasks}">
                                        <div id="actionNote_46" required="true"><fmtSpring:message
                                                code="page.text.description.required.to.create.new.task.brackets"/></div>
                                    </c:if>
                                    <c:if test="${!requiresDescriptionOnTasks}">
                                        <div id="actionNote_46"></div>
                                    </c:if>
                                    <div id="actionContinueTaskSelectButtons_46"></div>
                                </div>
                            </div>

                            <!-- POPUP INTERFACE -->
                            <msgpt:Popup id="actionPopup" theme="minimal">
                                <div id="actionPopupInfoFrame">
                                    <div id="actionPopupInfo">&nbsp;</div>
                                </div>
                                <div id="actionPopupWorkflowSelect">
                                    <br>
                                    <div class="formControl">
                                        <div class="controlWrapper">
                                            <form:select id="workflowSelect" path="actionToWorkflow" cssClass="inputL"
                                                         onchange="selectWorkflow()" onkeyup="selectWorkflow()" >
                                                <option id="0" value="0"><fmtSpring:message
                                                        code="page.text.loading"/></option>
                                            </form:select>
                                        </div>
                                    </div>
                                </div>
                                <div id="actionPopupUserSelect">
                                    <br>
                                    <div class="formControl">
                                        <div class="controlWrapper">
                                            <form:select id="userSelect" path="assignedToUser" cssClass="inputL"
                                                         onchange="validatePopupReq()" onkeyup="validatePopupReq()">
                                                <option id="0" value="0"><fmtSpring:message
                                                        code="page.text.loading"/></option>
                                            </form:select>
                                        </div>
                                    </div>
                                </div>
                                <div id="actionPopupClone">
                                    <div class="formControl">
                                        <label><span class="labelText"><fmtSpring:message
                                                code="page.label.name"/></span></label>
                                        <div class="controlWrapper">
                                            <msgpt:InputFilter type="simpleName">
                                                <form:input path="cloneName"
                                                            onkeyup="validatePopupReq();"
                                                            onchange="validatePopupReq();"
                                                            id="clone_newInstanceName" onfocus="this.select()"/>
                                            </msgpt:InputFilter>
                                        </div>
                                    </div>
                                </div>
                                <div id="actionPopupReportOptions">
                                    <c:if test="${viewMessagePermission}">
                                        <p>
                                            <form:checkbox id="includeTargetingCheckbox" path="includeTargeting"
                                                           cssClass="checkbox"/>
                                            <fmtSpring:message code="page.label.include.targeting"/>
                                        </p>
                                    </c:if>
                                    <p>
                                        <form:checkbox id="includeContentCheckbox" path="includeContent"
                                                       cssClass="checkbox"/>
                                        <fmtSpring:message code="page.label.include.content"/>
                                    </p>
                                    <p>
                                        <form:checkbox id="includeAllMessagesCheckboxAudit"
                                                       path="includeAllMessagesAudit"
                                                       cssClass="checkbox"/>
                                        <fmtSpring:message code="page.label.include.all.messages"/>
                                    </p>
                                    <br>
                                    <p>
                                        <form:checkbox id="includeFilterCheckbox" path="dateFilterEnabled"
                                                       cssClass="checkbox"/>
                                        <b><fmtSpring:message code="page.label.filter"/></b>
                                    </p>
                                    <div class="formControl">
                                        <label>
                                            <span class="labelText"><fmtSpring:message
                                                    code="page.text.include.messages.activated.after"/>:</span>
                                        </label>
                                        <div class="controlWrapper hasGroupControl">
                                            <msgpt:Calendar id="includeFilterDate" path="startDate"
                                                            viewableDateFormat="${viewableDateFormat}"/>
                                        </div>
                                    </div>
                                </div>
                                <c:if test="${isVariantTouchpoint}">
                                    <div id="actionPopupSuppress">
                                        <div class="InfoSysContainer_question">
                                            <p>
                                                <b><fmtSpring:message code="page.text.suppress.production"/></b>
                                            </p>
                                            <br>
                                            <form:checkbox id="isSuppressedOnHold" path="suppressedOnHold"
                                                           cssClass="flipToggleBinding defaultFalse"
                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                        </div>
                                    </div>
                                    <div id="actionPopupUpdateVariantVersioning">
                                        <div class="InfoSysContainer_question">
                                            <p>
                                                <b><fmtSpring:message
                                                        code="page.text.create.variant.working.copies"/></b>
                                            </p>
                                            <br>
                                            <form:checkbox id="updateVariantVersioning"
                                                           path="updateVariantVersioning"
                                                           cssClass="flipToggleBinding defaultTrue"
                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                        </div>
                                    </div>
                                </c:if>
                                <div id="actionPopupMetadataSelect">
                                    <div class="formControl">
                                        <label><span class="labelText"><fmtSpring:message
                                                code="page.label.metadata.optional"/></span></label>
                                        <div class="controlWrapper">
                                            <select id="metadataSelect" class="style_select">
                                                <option value="0"><fmtSpring:message code="page.label.none"/></option>
                                                <c:forEach var="metadataFormDefinition"
                                                           items="${metadataFormDefinitions}">
                                                    <option id="metadataFormDefinitionOption_${metadataFormDefinition.id}"
                                                            value="${metadataFormDefinition.id}">
                                                        <c:out value="${metadataFormDefinition.name}"/>
                                                    </option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div id="actionPopupTaskMetadataSelect">
                                    <div class="formControl">
                                        <label><span class="labelText"><fmtSpring:message
                                                code="page.label.metadata.optional"/></span></label>
                                        <div class="controlWrapper">
                                            <select id="taskMetadataSelect" class="style_select">
                                                <option value="0"><fmtSpring:message code="page.label.none"/></option>
                                                <c:forEach var="taskMetadataFormDefinition"
                                                           items="${taskMetadataFormDefinitions}">
                                                    <option id="taskMetadataFormDefinitionOption_${taskMetadataFormDefinition.id}"
                                                            value="${taskMetadataFormDefinition.id}">
                                                        <c:out value="${taskMetadataFormDefinition.name}"/>
                                                    </option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div id="actionPopupTaskSelect">
                                    <div class="formControl">
                                        <input type="hidden" id="currentTaskLinkedObjectId">
                                        <label><span class="labelText"><fmtSpring:message code="page.label.name"/></span></label>
                                        <span id="linkTaskToObjectName"></span>
                                    </div>
                                    <div class="formControl" id="taskSelectDiv">
                                        <label><span class="labelText"><fmtSpring:message code="page.label.task"/></span></label>
                                        <div class="controlWrapper">
                                            <form:select id="taskSelect" path="currentLinkedTaskId" cssClass="inputXXL"
                                                         onchange="selectTask()" onkeyup="selectTask()">
                                                <option id="0" value="0"><fmtSpring:message code="page.label.create.new.task"/></option>
                                            </form:select>
                                        </div>
                                    </div>
                                    <div class="formControl" id="newTaskTypeDiv">
                                        <label><span class="labelText"><fmtSpring:message code="page.label.type"/></span></label>
                                        <div class="controlWrapper">
                                            <form:select id="taskTypeSelect" path="createTaskType" cssClass="style_select"
                                                         onchange="selectTaskType()" items="${taskTypes}" itemLabel="name" itemValue="id">
                                            </form:select>
                                        </div>
                                    </div>
                                    <div class="formControl" id="newTaskLanguageDiv" style="display: none">
                                        <label><span class="labelText"><fmtSpring:message code="page.label.language"/></span></label>
                                        <div class="controlWrapper">
                                            <form:select id="newTasklanguageSelect" path="messagepointLocaleId" cssClass="inputXXL style_select"
                                                         items="${messagepointLocales}" itemLabel="name" itemValue="id">
                                            </form:select>
                                        </div>
                                    </div>
                                    <div class="formControl" id="taskVariantDiv">
                                        <label><span class="labelText"><fmtSpring:message code="page.label.variant"/></span></label>
                                        <div class="controlWrapper">
                                            <form:select id="taskVariantSelect" path="taskVariantId" cssClass="inputXXL">
                                                <option id="0" value="0"><fmtSpring:message code="page.label.no.selected.items"/></option>
                                            </form:select>
                                        </div>
                                    </div>
                                    <div id="taskDescriptionDiv">
                                    </div>
                                </div>
                                <div id="actionPopupNote" state="default">
                                    <br/>
                                    <div class="formControl">
                                        <label><span id="descriptionLabel" class="labelText" style="display: none;"><fmtSpring:message code="page.label.description"/></span></label>
                                        <div class="controlWrapper">
                                            <msgpt:InputFilter type="comment">
                                                <form:textarea path="userNote" onkeyup="validatePopupReq();"
                                                               rows="3"
                                                               onclick="initTextarea(this)"/>
                                            </msgpt:InputFilter>
                                        </div>
                                    </div>
                                </div>
                                <div id="actionPopupApprovalButtons" class="actionPopupButtonsContainer">
                                    <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                    <span id="approveBtn">
                                        <msgpt:Button URL="#" primary="true" label="page.flow.approve"/>
                                    </span>
                                    <span id="approveBtnDisabled" style="display: none;">
                                        <msgpt:Button URL="#" label="page.flow.approve" disabled="true"/>
                                    </span>
                                </div>
                                <div id="actionPopupReleaseFromTranslationButtons" class="actionPopupButtonsContainer">
                                    <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                    <span id="releaseBtn">
                                        <msgpt:Button URL="#" primary="true" label="page.label.release"/>
                                    </span>
                                    <span id="releaseBtnDisabled" style="display: none;">
                                        <msgpt:Button URL="#" label="page.label.release" disabled="true"/>
                                    </span>
                                </div>
                                <div id="actionPopupRejectButtons" class="actionPopupButtonsContainer">
                                    <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                    <span id="rejectBtn">
                                        <msgpt:Button URL="#" primary="true" label="page.flow.reject"/>
                                    </span>
                                    <span id="rejectBtnDisabled" style="display: none;">
                                        <msgpt:Button URL="#" label="page.flow.reject" disabled="true"/>
                                    </span>
                                </div>
                                <div id="actionPopupAbortButtons" class="actionPopupButtonsContainer">
                                    <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                    <span id="abortBtn">
                                        <msgpt:Button URL="#" primary="true" label="page.label.abort"/>
                                    </span>
                                    <span id="abortBtnDisabled" style="display: none;">
                                        <msgpt:Button URL="#" label="page.label.abort" disabled="true"/>
                                    </span>
                                </div>
                                <!-- Add Message w/ Metadata -->
                                <div id="actionPopupMetadataButtons" class="actionPopupButtonsContainer">
                                    <span id="customCancelBtnEnabled">
                                        <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                    </span>
                                    <span id="customContinueBtnEnabled">
                                        <msgpt:Button URL="javascript:iFrameAction(2);"
                                                      label="page.label.continue" primary="true"/>
                                    </span>
                                </div>
                                <!-- Add Task w/ Metadata -->
                                <div id="actionPopupTaskMetadataButtons" class="actionPopupButtonsContainer">
                                    <span id="taskCustomCancelBtnEnabled">
                                        <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                    </span>
                                    <span id="taskCustomContinueBtnEnabled">
                                        <msgpt:Button URL="javascript:iFrameAction(38);"
                                                      label="page.label.continue" primary="true"/>
                                    </span>
                                </div>
                                <div id="actionPopupExtTypeSelect" style="padding: 2px 8px 6px 8px;" align="center">
                                    <form:checkbox id="includeMessagesCheckbox" path="includeAllMessages"
                                                   cssClass="checkbox"/>
                                    <fmtSpring:message code="page.label.include.all.messages"/>
                                    <form:select id="extType" path="extType" cssClass="inputL">
                                        <option id="0" value="0"><fmtSpring:message code="page.label.xml"/></option>
                                        <option id="1" value="1"><fmtSpring:message code="page.label.excel"/></option>
                                    </form:select>
                                </div>
                                <div id="actionPopupContentJSONExport" style="padding: 2px 8px 6px 8px;">
                                    <label><span class="labelText"><fmtSpring:message
                                            code="page.label.target.locale"/></span></label>
                                    <form:select items="${exportTargetLocales}"
                                                 itemLabel="name"
                                                 itemValue="id"
                                                 cssClass="custom-select custom-select-lg"
                                                 id="exportTargetLocaleSelect"
                                                 path="exportTargetLocale"/>
                                </div>
                                <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                                    <span id="cancelBtnEnabled">
                                        <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                    </span>
                                    <span id="cancelBtnDisabled" style="display: none;">
                                        <msgpt:Button URL="#" label="page.label.cancel" disabled="true"/>
                                    </span>
                                    <span id="continueBtnEnabled">
                                        <msgpt:Button URL="#" primary="true" label="page.label.continue"/>
                                    </span>
                                    <span id="continueBtnDisabled" style="display: none;">
                                        <msgpt:Button URL="#" label="page.label.continue" disabled="true"/>
                                    </span>
                                </div>
                                <div id="actionPopupContinueToTaskSelectButtons" class="actionPopupButtonsContainer">
                                    <span id="toLinkTaskCustomCancelBtnEnabled">
                                        <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                    </span>
                                    <span id="toLinkTaskCustomContinueBtnEnabled">
                                        <msgpt:Button URL="javascript:continueToLinkTask(false);" label="page.label.continue" primary="true"/>
                                    </span>
                                </div>
                                <div id="actionPopupContinueTaskSelectButtons" class="actionPopupButtonsContainer">
                                    <span id="linkTaskCustomCancelBtnEnabled">
                                        <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                    </span>
                                    <span id="linkTaskCustomContinueBtnEnabled">
                                        <msgpt:Button URL="javascript:continueToLinkTask(true);" label="page.label.continue" primary="true"/>
                                    </span>
                                    <span id="linkTaskCustomContinueBtnDisabled" style="display: none;">
                                        <msgpt:Button URL="#" label="page.label.continue" disabled="true"/>
                                    </span>
                                </div>
                            </msgpt:Popup>

                        </c:if>
                        <c:if test="${noVisibleTouchpoints}">
                            <div class="alert alert-info" role="alert">
                                <strong class="mr-2">
                                    <i class="fas fa-info-circle fa-lg mr-2"
                                       aria-hidden="true"></i><fmtSpring:message
                                        code="page.label.info"/>:
                                </strong>
                                <fmtSpring:message code="page.text.no.touchpoints.available.to.you"/>
                            </div>
                        </c:if>
                    </form:form>
                </c:if>
                <c:if test="${trashTpContext}">
                    <div class="alert alert-info" role="alert">
                        <strong class="mr-2">
                            <i class="fas fa-info-circle fa-lg mr-2"
                               aria-hidden="true"></i><fmtSpring:message
                                code="page.label.info"/>:
                        </strong>
                        <fmtSpring:message code="page.text.trash.touchpoint.does.not.apply"/>
                    </div>
                </c:if>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
        <msgpt:ContextMenu name="messageList">
            <msgpt:ContextMenuEntry name="actionOption_1" link="#actionSelected:1"><fmtSpring:message
                    code="page.label.create.working.copy"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actioniFrame_1" link="#iFrameAction:1"><fmtSpring:message
                    code="page.label.edit"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_19" link="#actionSelected:19"><fmtSpring:message
                    code="page.label.activate"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_6" link="#actionSelected:6"><fmtSpring:message
                    code="page.label.release.for.approval"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_17" link="#actionSelected:17"><fmtSpring:message
                    code="page.label.approve"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_30" link="#actionSelected:30"><fmtSpring:message
                    code="page.label.release.from.translation"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_18" link="#actionSelected:18"><fmtSpring:message
                    code="page.label.reject"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_20" link="#actionSelected:20"><fmtSpring:message
                    code="page.label.approve.and.override"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_21" link="#actionSelected:21"><fmtSpring:message
                    code="page.label.reject.and.override"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_40" link="#actionSelected:40"><fmtSpring:message
                    code="page.label.abort"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_45" link="#actionSelected:45"><fmtSpring:message
                    code="page.label.retry.translation"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_2" link="#actionSelected:2"><fmtSpring:message
                    code="page.label.discard.working.copy"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="separator"><li class="separator"></li></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_5" link="#actionSelected:5"><fmtSpring:message
                    code="page.label.reassign"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_41" link="#actionSelected:41"><fmtSpring:message
                    code="page.label.reassign"/></msgpt:ContextMenuEntry>
            <c:if test="${not touchpointContext.emailTouchpoint && not touchpointContext.smsTouchpoint && not touchpointContext.webTouchpoint}">
                <msgpt:ContextMenuEntry name="actioniFrame_22" link="#iFrameAction:22"><fmtSpring:message
                        code="page.label.preview"/></msgpt:ContextMenuEntry>
            </c:if>
            <msgpt:ContextMenuEntry name="actionOption_23" link="#actionSelected:23"><fmtSpring:message
                    code="page.label.clone"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_43" link="#actionSelected:43"><fmtSpring:message
                    code="page.label.content.export"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actioniFrame_28" link="#iFrameAction:28"><fmtSpring:message
                    code="page.label.move"/></msgpt:ContextMenuEntry>
            <c:if test="${isVariantTouchpoint}">
                <msgpt:ContextMenuEntry name="actionOption_24" link="#actionSelected:24"><fmtSpring:message
                        code="page.label.hold"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="actionOption_25" link="#actionSelected:25"><fmtSpring:message
                        code="page.label.unhold"/></msgpt:ContextMenuEntry>
            </c:if>
            <msgpt:IfAuthGranted authority="ROLE_MESSAGE_ACTIVATION">
                <msgpt:ContextMenuEntry name="actionOption_26" link="#actionSelected:26"><fmtSpring:message
                        code="page.label.suppress"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="actionOption_27" link="#actionSelected:27"><fmtSpring:message
                        code="page.label.restore"/></msgpt:ContextMenuEntry>
            </msgpt:IfAuthGranted>
            <msgpt:IfAuthGranted authority="ROLE_MESSAGE_ACTIVATION">
                <msgpt:ContextMenuEntry name="actionOption_3" link="#actionSelected:3"><fmtSpring:message
                        code="page.label.archive"/></msgpt:ContextMenuEntry>
                <msgpt:ContextMenuEntry name="actionOption_4" link="#actionSelected:4"><fmtSpring:message
                        code="page.label.delete.archive"/></msgpt:ContextMenuEntry>
            </msgpt:IfAuthGranted>
            <msgpt:ContextMenuEntry name="separator"><li class="separator"></li></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actioniFrame_36" link="#iFrameAction:36"><fmtSpring:message
                    code="page.label.calculate.hash"/></msgpt:ContextMenuEntry>
            <c:if test="${not hasTaskMetadataFormDef}">
                <msgpt:ContextMenuEntry name="actioniFrame_38" link="#iFrameAction:38"><fmtSpring:message
                        code="page.label.add.task"/></msgpt:ContextMenuEntry>
            </c:if>
            <c:if test="${hasTaskMetadataFormDef}">
                <msgpt:ContextMenuEntry name="actionOption_39" link="#actionSelected:39"><fmtSpring:message
                        code="page.label.add.task"/></msgpt:ContextMenuEntry>
            </c:if>
        </msgpt:ContextMenu>
    </msgpt:BodyNew>
</msgpt:Html5>