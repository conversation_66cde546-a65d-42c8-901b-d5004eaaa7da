<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>

<%@ include file="../includes/includes.jsp" %>

<c:set var="isEmbedded" value="${not empty param.embedded && param.embedded == 'true'}" />
<c:set var="isReturnToList" value="${not empty param.return_to_list && param.return_to_list == 'true'}" />

<!-- FLAGS AND PERMISSIONS -->
<c:set var="editPerm" value="false" scope="request"/>
<msgpt:IfAuthGranted authority="ROLE_LICENCED_COMMUNICATIONS_EDIT">
    <c:set var="editPerm" value="true" scope="request"/>
</msgpt:IfAuthGranted>
<c:set var="reassignEditPerm" value="false" scope="request"/>
<msgpt:IfAuthGranted authority="ROLE_LICENCED_COMMUNICATIONS_EDIT">
    <msgpt:IfAuthGranted authority="ROLE_LICENCED_COMMUNICATIONS_REASSIGN">
        <c:set var="reassignEditPerm" value="true" scope="request"/>
    </msgpt:IfAuthGranted>
</msgpt:IfAuthGranted>
<c:set var="reassignPerm" value="false" scope="request"/>
<msgpt:IfAuthGranted authority="ROLE_LICENCED_COMMUNICATIONS_REASSIGN">
    <c:set var="reassignPerm" value="true" scope="request"/>
</msgpt:IfAuthGranted>
<c:set var="approvePerm" value="false" scope="request"/>
<msgpt:IfAuthGranted authority="ROLE_LICENCED_COMMUNICATIONS_APPROVE">
    <c:set var="approvePerm" value="true" scope="request"/>
</msgpt:IfAuthGranted>
<c:set var="viewMetatagsPermission" value="false"/>
<msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
    <c:set var="viewMetatagsPermission" value="true"/>
</msgpt:IfAuthGranted>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.touchpoints" extendedScripts="true">
        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>
        <msgpt:CalendarIncludes/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

        <c:if test="${isEmbedded}">
            <style>
                body {
                    background: url("../includes/themes/commonimages/layout/content_panel_background.gif") repeat scroll left top;
                }
            </style>
        </c:if>
        <style>
            .read-more {
                font-size: 14px;
                color: darkblue;
            }

        </style>

        <msgpt:Script>
            <script>

                    var message_reassignToSelf = "${msgpt:getMessage('page.text.reassign.to.self')}";
                    var message_reassignToSelfActivate = "${msgpt:getMessage('page.text.reassign.to.self.activate')}";
                    var message_reassignToSelfRelease = "${msgpt:getMessage('page.text.reassign.to.self.release')}";

                var $updateBtn, $proofBtn, $actionMenu, $widgetToggle;

                // *********  INIT: START  *********
                $(function () {

                    $updateBtn = $('#updateBtn');
                    $proofBtn = $('#proofBtn');
                    $actionMenu = $('#actionMenu');
                    $widgetToggle = $('#widgetToggleBtn');
                    

                    // Hide widget based on persisted state
                    if ($widgetToggle.is('.active')) {

                        $widgetToggle.children('.btn-text').text('Compress');
                        $widgetToggle.attr('aria-pressed', true);

                        updateWidgetDisplay(false);

                    }

                    $("[id*='itemInProcess']").actionStatusPolling({
    					itemLabel 	: client_messages.text.audit_report,
    					type		: 'auditreport',
    					postItemInit: function(item) {
    										var menuEleId = $(item).closest('select').attr('id') + '_menuTable';
    										if ( $('#'+menuEleId).find("[id*='itemInProcess']").length == 0 )
    											$('#'+menuEleId).find('.menuItemInProcessIcon,.menuItemInProcessSpacer').remove();
    									},
    					onInit		: function(o) {
    										if ( $(o.option).closest('.actionBtnTable').find('.actionBtnText').find('.menuItemInProcessIcon').length == 0 )
    											$(o.option).closest('.actionBtnTable').find('.actionBtnText').prepend("<div class=\"menuItemInProcessIcon\" style=\"display: inline-block; position: absolute;\"></div><div class=\"menuItemInProcessSpacer\" style=\"display: inline-block; width: 22px; height: 1px;\"></div>");
    									}	
    				});

                    var $searchTagCloud = $('#listSearchInput[data-toggle="tagcloud"]');

                    $searchTagCloud.tagCloud({
                        tagCloudType: $searchTagCloud.data('cloud-type'),
                        inputType: 'search',
                        rightOffsetAdj: 16,
                        topOffsetAdj: 12,
                        popupLocation: 'bottom-left',
                        afterInputValueChange: function (o, val) {
                            $searchTagCloud.keyup();
                        }
                    });

                    // Proof polling init
                    $("[id^='preProofPollingContainer_']").each(function () {
                        if ($(this).attr('pollingInit') == undefined || $(this).attr('pollingInit') != "true") {
                            $(this).attr('pollingInit', 'true');
                            pollForPreProof(parseId(this));
                        }
                    });

                    // Production status polling init
                    $("[id^='productionStatusContainer_']").each(function () {
                        if ($(this).attr('poll_status') == "true" && ($(this).attr('pollingInit') == undefined || $(this).attr('pollingInit') != "true")) {
                            $(this).attr('pollingInit', 'true');
                            pollForProductionStatus(parseId(this));
                        }
                    });

                    // Init widget toggle
                    $widgetToggle.on('click', function () {

                        _.defer(function () {

                            var $btnText = $widgetToggle.children('.btn-text'),
                                $btnIcon = $widgetToggle.children('.btn-icon'),
                                initializeWidget = false;

                            if ($widgetToggle.is('.active')) {

                                $btnText.text(client_messages.text.compress);
                                $btnIcon.removeClass('fa-expand-wide').addClass('fa-compress-wide');

                                updatePersistedClass($btnIcon);
                                updatePersistedClass($widgetToggle);

                                updateWidgetDisplay(initializeWidget);

                            } else {

                                $btnText.text(client_messages.text.expand);
                                $btnIcon.addClass('fa-expand-wide').removeClass('fa-compress-wide');

                                updatePersistedClass($btnIcon);
                                updatePersistedClass($widgetToggle);

                                if (!$("#touchpointNavigationWidget").contents().find(".touchpointWidgetContainer").length)
                                    initializeWidget = true;

                                updateWidgetDisplay(initializeWidget);

                            }

                        });

                    });

                    //Init Add selection button
                    $("[id^='addCommunicationBtn']").click(function () {
                        // Button disabled: Do nothing
                        if ($(this).is('.highlightedBtnDisabled'))
                            return;

                        var params = 'tk=' + getParam('tk');
                        if ($('#isTestCenterContext').val() == "true")
                            params += '&context=test';
                        if ( getParam('embedded') == 'true' ) {
                            params += '&embedded=true';
                            if ( getParam('return_to_list') == 'true' )
                                params += '&return_to_list=true'
                            if ( getParam('return_url') != '' )
                                params += '&return_url=' + getParam('return_url');
                        }
                        if (getParam('documentId') != '' )
                            params += '&documentId=' + getParam('documentId');

                        getTopFrame().location.href = context + "/communication/communication_order_entry_edit.form?" + params;
                    });

                    if($actionMenu.length) {
                        toggleFilter($('#communicationsListAssignmentFilter'), true);	// Adjust messaging based on filter settings
                        validateActionReq();
                    }
                });
                // *********  INIT: END  *********

                // *********  WIDGET FUNCTIONS: START  *********
                function updateWidgetDisplay(reloadWidget) {

                    var $widgetContainer = $('#widgetContainer');

                    if ($widgetToggle.is('.active')) {

                        $widgetContainer.addClass('d-none');

                    } else {

                        $widgetContainer.removeClass('d-none');

                        if (reloadWidget && window.frames['touchpointNavigationWidget'])
                            window.frames['touchpointNavigationWidget'].location = context + "/touchpoints/" + $('#touchpointNavigationWidget').attr('src');

                    }

                }

                // *********  WIDGET FUNCTIONS: END  *********

                // *********  LIST TABLE FUNCTIONS: START  *********
                function pollForPreProof(proofItemId) {
                    var proofResultContainer = $('#preProofPollingContainer_' + proofItemId);
                    if ($(proofResultContainer).length == 0)
                        return;

                    var stampDate = new Date();
                    $.ajax({
                        type: "GET",
                        url: context + "/getCommunicationProofStatus.form?proofId=" + proofItemId + "&source=order_list&tk=" + getParam('tk') + "&cacheStamp=" + stampDate.getTime(),
                        dataType: "json",
                        success: function (data) {
                            processPreProofPollResult(data);
                        }
                    });

                    window.setTimeout(function () {
                        pollForPreProof(proofItemId);
                    }, 2000);
                }

                function pollForProductionStatus(communicationId) {
                    if ($('#productionStatusContainer_' + communicationId).length == 0 || $('#productionStatusContainer_' + communicationId).attr('poll_status') == "false")
                        return;

                    var stampDate = new Date();
                    $.ajax({
                        type: "GET",
                        url: context + "/getMetadata.form?communicationId=" + communicationId + "&dataType=prod_status&tk=" + getParam('tk') + "&cacheStamp=" + stampDate.getTime(),
                        dataType: "json",
                        success: function (data) {
                            processProductionStatusPollResult(data);
                        }
                    });

                    window.setTimeout(function () {
                        pollForProductionStatus(communicationId);
                    }, 2000);
                }

                function processPreProofPollResult(data) {

                    var proofResultContainer = $('#preProofPollingContainer_' + data.item_id).closest('td');
                    if (data.status != "in_process")
                        $('#preProofPollingContainer_' + data.item_id).remove();

                    if (data.status == "no_matching_recipient_error") {
                        $(proofResultContainer).append("<div class=\"errorMsg detailTip\" title=\"|" + client_messages.text.unable_to_generate_proof + "\">" + client_messages.text.error + "</div>");
                    } else if (data.status == "no_production_content_error") {
                        $(proofResultContainer).append("<div class=\"errorMsg detailTip\" title=\"|" + client_messages.text.unable_to_generate_preproof + "\">" + client_messages.text.error + "</div>");
                    } else if (data.status == "log") {
                        $(proofResultContainer).append("<div style=\"cursor: pointer;\" class=\"detailTip\" title=\"" + client_messages.text.unable_to_generate_preproof_view_log + "\">" +
                            "<a href=\"javascript:popItUp(context+'/includes/delivery_event_logs.jsp?id=" + data.item_id + "&deliveryid=" + data.delivery_event_id + "&class=" + data.item_class + "')\">" + client_messages.text.error + "</a>" +
                            "</div>");
                    } else if (data.status == "complete") {
                        if (data.is_print_touchpoint) {
                            if (data.is_remote_result)
                                $(proofResultContainer).append("<div style=\"cursor: pointer;\" class=\"detailTip\" title=\"" + client_messages.text.click_to_view + ".\">" +
                                    "<a href=\"javascript:javascriptHref(context+'/download/pdf.form?comm_proof_id=" + data.item_id + "&comm_proof_type=proof&type=remote_comm_pdf')\">" + client_messages.text.complete + "</a>" +
                                    "</div>");
                            else
                                $(proofResultContainer).append("<div style=\"cursor: pointer;\" class=\"detailTip\" title=\"" + client_messages.text.click_to_view + ".\">" +
                                    "<a href=\"javascript:javascriptHref(context+'/download/pdf.form?resource=" + data.resource + "')\">" + client_messages.text.complete + "</a>" +
                                    "</div>");
                        } else {
                            $(proofResultContainer).append("<div>" +
                                client_messages.text.complete +
                                "</div>");
                        }

                        // Enable Socialize Proof
                        var orderId = parseId($(proofResultContainer).closest('tr').find("[id^='listItemCheck_']"));
                        $(proofResultContainer).closest('tr').find("[id^='listItemCheck_']").closest('td').append("<input type='hidden' id='canSocialize_" + orderId + "' />");

                    } else if (data.status == "error") {
                        $(proofResultContainer).append("<div style=\"cursor: pointer;\" class=\"detailTip\" title=\"" + client_messages.text.unable_to_generate_proof_view_log + "\">" +
                            "<a href=\"javascript:popItUp(context+'/includes/delivery_event_logs.jsp?id=" + data.item_id + "&deliveryid=" + data.delivery_event_id + "&class=" + data.item_class + "')\">" + client_messages.text.error + "</a>" +
                            "</div>");
                    } else if (data.status == "proof_error") {
                        $(proofResultContainer).append("<div style=\"cursor: pointer;\" class=\"detailTip\" title=\"" + client_messages.text.unable_to_generate_proof_view_log + "\">" +
                            "<a href=\"javascript:popItUp(context+'/includes/proof_error_message.jsp?id=" + data.communication_id + "')\">" + client_messages.text.error + "</a>" +
                            "</div>");
                    }

                }

                function processProductionStatusPollResult(data) {
                    $('#productionStatusContainer_' + data.communication_id)
                        .attr('poll_status', data.tracking_complete == false)
                        .html(data.status);
                    if (data.tracking_complete) {
                        $('#communicationsList').DataTable().ajax.reload();
                    }
                }

                function toggleFilter(select, maintainCurrentList) {
                    $('#infoMsg_invalidFilterSetting').addClass('d-none');
                    if ($('#communicationsListAssignmentFilter option:selected').val() == '2' &&
                        $('#communicationsListStatusFilter option:selected').val() == '2')
                        $('#infoMsg_invalidFilterSetting').removeClass('d-none');

                    if (!maintainCurrentList)
                        rebuildListTable(false);
                }

                function readMore(type){
                    var content = "";
                    if(type == 1){
                        content = "${invalidDataItemsName}";
                    } else {
                        content = "${variableWithoutDataElement}";
                    }
                    var element = document.getElementById("readMoreContent");
                    if(document.getElementById("readMoreBtn").textContent.includes(">>>")){
                        element.textContent = content.substring(400, content.length);
                        document.getElementById("readMoreBtn").textContent = "Read less <<<";
                    } else {
                        element.textContent = "";
                        document.getElementById("readMoreBtn").textContent = "Read more >>>";
                    }
                }

                function rebuildListTable(maintainCurrentPage) {
                    $('#communicationsList').DataTable().ajax.reload(null, !maintainCurrentPage);
                }

                function getAsyncExtListTableConfig() {

                    var recipientColumnName = client_messages.text.recipient;
                    if ($('#recipientColumnName').length != 0)
                        recipientColumnName = $('#recipientColumnName').val();

                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.columns = [
                        {
                            columnMap: 'recipient',
                            columnName: recipientColumnName,
                            sort: true,
                            width: '30%',
                            colVisToggle: false
                        },
                        {
                            columnMap: 'created',
                            columnName: client_messages.text.created,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'status',
                            columnName: client_messages.text.status,
                            sort: true,
                            width: '10%',
                            colVisToggle: true,
                            applied: ${not isTestCenterContext}
                        },
                        {
                            columnMap: 'assignedTo',
                            columnName: client_messages.text.assigned,
                            sort: true,
                            width: '15%',
                            colVisToggle: true,
                            applied: ${approvePerm}
                        },
                        {
                            columnMap: 'proof',
                            columnName: client_messages.text.Proof,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'productionStatus',
                            columnName: client_messages.text.result,
                            sort: true,
                            width: '15%',
                            colVisToggle: true,
                            applied: ${pollProductionStatus && not isTestCenterContext}
                        },
                        {
                            columnMap: 'externalValidation',
                            columnName: client_messages.response,
                            sort: false,
                            width: '5%',
                            colVisToggle: true,
                            applied: ${externalValidationEnabled && not isDigitalProof && not isTestCenterContext}
                        }
                    ];
                    return obj;
                }

                function getAsyncExtParams() {
                    var obj = [
                        {"name": "listTableType", "value": "10"},
                        {"name": "documentId", "value": getParam('documentId')},
                        {
                            "name": "communicationsAssignmentFilterId",
                            "value": exists('communicationsListAssignmentFilter') && parseInt($('#communicationsListAssignmentFilter').val()) > 0 ? $('#communicationsListAssignmentFilter').val() : 2
                        },
                        {
                            "name": "communicationsStatusFilterId",
                            "value": exists('communicationsListStatusFilter') && parseInt($('#communicationsListStatusFilter').val()) > 0 ? $('#communicationsListStatusFilter').val() : -1
                        },
                        {
                            "name": "displayMode",
                            "value": $('#widgetToggleBtn').hasClass('toggleLeftIcon') ? "limited" : "full"
                        },
                        {"name": "context", "value": "${not isTestCenterContext ? 'default' : 'test'}"},
                        {"name": "cacheStamp", "value": stampDate.getTime()}
                    ];
                    return obj;
                }

                function postListDrawCallback(nTable) {
                    // Proof polling init
                    $(nTable).find("[id^='preProofPollingContainer_']").each(function () {
                        if ($(this).attr('pollingInit') == undefined || $(this).attr('pollingInit') != "true") {
                            $(this).attr('pollingInit', 'true');
                            pollForPreProof(parseId(this));
                        }
                    });

                    // Production status polling init
                    $(nTable).find("[id^='productionStatusContainer_']").each(function () {
                        if ($(this).attr('poll_status') == "true" &&
                            ($(this).attr('pollingInit') == undefined || $(this).attr('pollingInit') != "true")) {
                            $(this).attr('pollingInit', 'true');
                            pollForProductionStatus(parseId(this));
                        }
                    });

                    $(nTable).find("[ext_validation_id]").each(function () {
                        // External Validation:  Init  popup
                        var validationId = $(this).attr('ext_validation_id');
                        var stampDate = new Date();

                        $(this).popupFactory({
                            title: client_messages.response,
                            popupLocation: "left",
                            width: 250,
                            asyncDataType: "json",
                            asyncSetContentURL: context + "/getMetadata.form?validationId=" + validationId + "&dataType=ext_validation&tk=" + getParam('tk') + "&cacheStamp=" + stampDate.getTime(),
                            asyncSetContentHandler: function (o, data) {

                                var statusHTML = "<div align=\"left\" style=\"font-size: 12px; color: #333; margin-bottom: 3px;\">" +
                                    client_messages.text.status +
                                    "</div>";
                                if (data.status_id == 0)
                                    statusHTML += "<div align=\"left\" style=\"font-size: 10px; color: #555; margin-bottom: 6px;\">" +
                                        client_messages.text.awaiting_approval +
                                        "</div>";
                                else if (data.status_id == 1)
                                    statusHTML += "<div align=\"left\" style=\"font-size: 10px; color: #555; margin-bottom: 6px;\">" +
                                        client_messages.text.approved +
                                        "</div>";
                                else if (data.status_id == 2)
                                    statusHTML += "<div align=\"left\" style=\"font-size: 10x; color: #555; margin-bottom: 6px;\">" +
                                        client_messages.text.rejected +
                                        "</div>";

                                var feedbackHTML = "";
                                if (data.status_id == 1 || data.status_id == 2) {
                                    feedbackHTML = "<div align=\"left\" style=\"font-size: 12px; color: #333; margin-bottom: 3px;\">" +
                                        client_messages.text.response_date +
                                        "</div>" +
                                        "<div align=\"left\" style=\"font-size: 10x; color: #555; margin-bottom: 6px;\">" +
                                        data.response_date +
                                        "</div>" +
                                        "<div align=\"left\" style=\"font-size: 12px; color: #333; margin-bottom: 3px;\">" +
                                        client_messages.text.feedback +
                                        "</div>";
                                    if ($.trim(data.feedback).length > 0) {
                                        feedbackHTML += "<div align=\"left\" style=\"font-size: 10x; color: #555; margin-bottom: 6px;\">" +
                                            data.feedback +
                                            "</div>";
                                    } else {
                                        feedbackHTML += "<div align=\"left\" style=\"font-size: 10x; color: #555; margin-bottom: 6px;\">" +
                                            "<i>None</i>" +
                                            "</div>";
                                    }
                                }

                                return "<div align=\"left\" style=\"padding: 6px 12px; font-size: 10px;\">" +
                                    statusHTML +
                                    "<div align=\"left\" style=\"font-size: 12px; color: #333; margin-bottom: 3px;\">" +
                                    client_messages.text.recipient +
                                    "</div>" +
                                    "<div align=\"left\" style=\"font-size: 10x; color: #555; margin-bottom: 6px;\">" +
                                    data.email +
                                    "</div>" +
                                    feedbackHTML +
                                    "</div>";

                            }
                        });
                    });

                }

                function postListRenderFlagInjection(oObj) {
                    var iFrameId = "communication-id";
                    var iFrameSrc = context + "/touchpoints/touchpoint_communications_list_detail.form?communicationId=" + oObj.aData.dt_RowId +
                        "&isTestCenterContext=" + $('#isTestCenterContext').val();
                    var binding = oObj.aData.binding;

                    var text = oObj.aData.recipient;
                    text += "<input id='iFrameId' value=" + iFrameId + " type='hidden' class='iframe-data' />";
                    text += "<input id='iFrameSrc' value=" + iFrameSrc + " type='hidden' class='iframe-data' />";

                    //Communication check select
                    text += binding;

                    //Selection permission flags
                    text += "<input type='hidden' id='assignedTo_" + oObj.aData.dt_RowId + "' value='" + oObj.aData.assignedTo + "' />";
                    if (oObj.aData.flags.canUpdate)
                        text += "<input type='hidden' id='canUpdate_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReassignUpdate)
                        text += "<input type='hidden' id='canReassignUpdate_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReassign)
                        text += "<input type='hidden' id='canReassign_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canClone)
                        text += "<input type='hidden' id='canClone_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReleaseForApproval)
                        text += "<input type='hidden' id='canReleaseForApproval_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canApprove)
                        text += "<input type='hidden' id='canApprove_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReassignRelease)
                        text += "<input type='hidden' id='canReassignRelease_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReject)
                        text += "<input type='hidden' id='canReject_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canDelete)
                        text += "<input type='hidden' id='canDelete_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.hasNoStepForWorkflow)
                        text += "<input type='hidden' id='hasNoStepForWorkflow_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.workflowOwner)
                        text += "<input type='hidden' id='workflowOwner_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canSocialize)
                        text += "<input type='hidden' id='canSocialize_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.emailOrder)
                        text += "<input type='hidden' id='emailOrder_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canUnsubmit)
                        text += "<input type='hidden' id='canUnsubmit_" + oObj.aData.dt_RowId + "' />";

                    return text;
                }

                function actionMenuSelected(el) {

                    var elSelectedId = $(el).children(':selected').attr('id');

                    if (elSelectedId.indexOf('actionOption_') !== -1)
                        actionSelected(el);

                    else
                        iFrameAction(elSelectedId.replace('actioniFrame_', ''));

                }

                // List action
                function iFrameAction(actionId) {
                    var communicationId = null;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        communicationId = this.id.replace('listItemCheck_', '');
                    });

                }

                // Retrieve selected ids seperated by ','
                function getSelectedIds() {
                    var selectedIds = '';
                    var selectedCount = $("input[id^='listItemCheck']:checked").length;
                    var count = 0;
                    $("input[id^='listItemCheck']:checked").each(
                        function () {
                            selectedIds += this.id.replace('listItemCheck_', '');
                            count++;
                            if (count != selectedCount)
                                selectedIds += '_';
                        }
                    );
                    return selectedIds;
                }

                function redirectToEdit(editUrl) {
                    if ( getParam('embedded') == 'true' ) {
                        editUrl += '&embedded=true';
                        if ( getParam('return_to_list') == 'true' )
                            editUrl += '&return_to_list=true'
                        if ( getParam('return_url') != '' )
                            editUrl += '&return_url=' + getParam('return_url');
                    }
                    javascriptHref(editUrl);
                }

                // ******* Selection Checkbox Functions *******
                function validateActionReq(communicationId) {
                    var singleSelect = true;
                    var canUpdate = true;
                    var canReassignUpdate = true;
                    var canReassign = true;
                    var canApprove = true;
                    var canReject = true;
                    var canDelete = true;
                    var canReleaseForApproval = true;
                    var canReassignRelease = true;
                    var sameApprovalStepForAll = true;
                    var canSocialize = true;
                    var isEmailItem = false;
                    var canUnsubmit = true;
                    var fastOrderIndicatorEdit = ${document.communicationFastOrderIndicatorEdit};

                    var allHasWorkflowStep = true;
                    var allAreWorkflowOwner = true;

                    // Resolve selection flags
                    if ($("input[id^='listItemCheck']:checked").length != 1)
                        singleSelect = false;
                    var approvalStepId = -1;
                    var communicationId = -1;
                    var assignedTo = '';
                    $("input[id^='listItemCheck']:checked").each(
                        function () {
                            communicationId = this.id.replace('listItemCheck_', '');
                            if (exists('assignedTo_' + communicationId)) {
                                assignedTo = $('#assignedTo_' + communicationId).val();
                            }
                            if (!exists('canUpdate_' + communicationId))
                                canUpdate = false;
                            if (!exists('canReassignUpdate_' + communicationId))
                                canReassignUpdate = false;
                            if (!exists('canReassign_' + communicationId))
                                canReassign = false;
                            if (!exists('canApprove_' + communicationId))
                                canApprove = false;
                            if (!exists('canReject_' + communicationId))
                                canReject = false;
                            if (!exists('canDelete_' + communicationId))
                                canDelete = false;
                            if (!exists('canReleaseForApproval_' + communicationId))
                                canReleaseForApproval = false;
                            if (!exists('canReassignRelease_' + communicationId))
                                canReassignRelease = false;
                            if (exists('approvalType_' + communicationId)) {
                                if (approvalStepId == -1 || approvalStepId == $('#approvalType_' + communicationId).val()) {
                                    approvalStepId = $('#approvalType_' + communicationId).val();
                                } else {
                                    sameApprovalStepForAll = false;
                                }
                            }
                            if (exists('hasNoStepForWorkflow_' + communicationId)) {
                                allHasWorkflowStep = false;
                            } else {
                                allHasNoWorkflowStep = false;
                            }
                            if (exists('workflowOwner_' + communicationId)) {
                                allAreNotWorkflowOwner = false;
                            } else {
                                allAreWorkflowOwner = false;
                            }
                            if (!exists('canSocialize_' + communicationId))
                                canSocialize = false;
                            if (exists('emailOrder_' + communicationId)) {
                                isEmailItem = true;
                            }
                            if (!exists('canUnsubmit_' + communicationId))
                                canUnsubmit = false;
                        }
                    );

                    $('#actionInfo_21').text(message_reassignToSelf.replace('[currentUser]', assignedTo));
                    $('#actionInfo_22').text(message_reassignToSelfActivate.replace('[currentUser]', assignedTo));
                    $('#actionInfo_23').text(message_reassignToSelfRelease.replace('[currentUser]', assignedTo));

                    //Disable all context menu entries
                    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
                    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');
                    common.disableElement($updateBtn);
                    common.disableElement($proofBtn);
                    $actionMenu.data('complexDropdown').disableAllTheOptions();
                    $actionMenu.data('complexDropdown').enableOptionById('actionOption_9') // keep Batch enabled

                    //Toggle channel specific action components
                    isEmailItem ? $('.proofAction_email').show() : $('.proofAction_email').hide();
                    !isEmailItem ?  $('.proofAction_print').show() : $('.proofAction_print').hide();

                    if ($("input[id^='listItemCheck']:checked").length > 0) {

                        if (singleSelect && canUpdate) {
                            $('a#actionOption_10').removeClass('disabled'); // Edit
                            common.enableElement($updateBtn);
                            if (fastOrderIndicatorEdit) {
                                $('a#actionOption_15').removeClass('disabled'); // Edit Indicator
                                $actionMenu.data('complexDropdown').enableOptionById('actionOption_15');
                            }
                            $('a#actionOption_21').hide(); // Reassign and edit.
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_21');
                        } else {
                            if (canReassignUpdate) {
                                $('a#actionOption_21').show(); // Reassign and edit.
                                $actionMenu.data('complexDropdown').showOptionById('actionOption_21');
                            }
                        }
                        if (singleSelect && canReassignUpdate) {
                            $('a#actionOption_21').removeClass('disabled'); // Reassign and edit.
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_21');
                        }

                        if (canDelete) {
                            $('a#actionOption_6').removeClass('disabled'); // Delete
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_6'); // Delete
                        }

                        if (canReassign) {
                            $('a#actionOption_19').removeClass('disabled'); // Reassign
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_19'); // Reassign
                        }
                     	// Request report: 
                     	//$('a#actionOption_13').removeClass('disabled'); // Audit Report
        				//$actionMenu.data('complexDropdown').enableOptionById('actionOption_13'); // Audit Report

                        if (allHasWorkflowStep) {
                            if (allAreWorkflowOwner) {
                                $('a#actionOption_17').show();	// Approve and override
                                $('a#actionOption_18').show();	// Reject and override
                                $('a#actionOption_2').hide();	// Approve
                                $('a#actionOption_8').hide();	// Reject
                                $actionMenu.data('complexDropdown').showOptionById('actionOption_17'); // Approve and override
                                $actionMenu.data('complexDropdown').showOptionById('actionOption_18'); // Reject and override
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_2'); // Approve
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_8'); // Reject
                            } else {
                                $('a#actionOption_2').show(); // Approve
                                $('a#actionOption_8').show(); // Reject
                                $('a#actionOption_17').hide();	// Approve and override
                                $('a#actionOption_18').hide();	// Reject and override
                                $actionMenu.data('complexDropdown').showOptionById('actionOption_2'); // Approve
                                $actionMenu.data('complexDropdown').showOptionById('actionOption_8'); // Reject
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_17'); // Approve and override
                                $actionMenu.data('complexDropdown').hideOptionById('actionOption_18'); // Reject and override
                            }
                            $('a#actionOption_1').show();  // Release for approval
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_1');	// Release for approval
                            $('a#actionOption_23').show();  // Reassign and Release for approval
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_23');  // Reassign and Release for approval
                            $('a#actionOption_16').hide();	// Activate
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_16');	// Activate
                            $('a#actionOption_22').hide();	// Reassign and Activate
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_22');	// Reassign and Activate
                        } else {
                            $('a#actionOption_16').show(); // Activate
                            $('a#actionOption_17').hide();	// Approve and override
                            $('a#actionOption_18').hide();	// Reject and override
                            $('a#actionOption_1').hide();	// Release for approval
                            $('a#actionOption_2').hide();	// Approve
                            $('a#actionOption_8').hide();	// Reject
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_16'); // Activate
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_17'); // Approve and override
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_18'); // Reject and override
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_1'); // Release for approval
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_2'); // Approve
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_8'); // Reject

                            $('a#actionOption_22').show();                                          // Reassign and Activate
                            $actionMenu.data('complexDropdown').showOptionById('actionOption_22');
                            $('a#actionOption_23').hide();	                                        // Reassign and Release for approval
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_23');

                        }

                        if (canReleaseForApproval) {
                            $('a#actionOption_1').removeClass('disabled');	// Release for approval
                            $('a#actionOption_16').removeClass('disabled');	// Activate
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_1');	// Release for approval
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_16');	// Activate
                            $('a#actionOption_22').hide();	// Reassign and Activate
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_22');	// Reassign and Activate
                            $('a#actionOption_23').hide();	                                        // Reassign and Release for approval
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_23');
                        } else if (canReassignRelease) {
                            $('a#actionOption_22').removeClass('disabled');
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_22');
                            $('a#actionOption_23').removeClass('disabled');
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_23');
                        } else {
                            $('a#actionOption_22').hide();	// Reassign and Activate
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_22');	// Reassign and Activate
                            $('a#actionOption_23').hide();	                                        // Reassign and Release for approval
                            $actionMenu.data('complexDropdown').hideOptionById('actionOption_23');
                        }
                        if (canApprove && sameApprovalStepForAll) {
                            if (canApprove) {
                                $('a#actionOption_2').removeClass('disabled'); // Approve
                                $('a#actionOption_17').removeClass('disabled'); // Approve and override
                                $actionMenu.data('complexDropdown').enableOptionById('actionOption_2'); // Approve
                                $actionMenu.data('complexDropdown').enableOptionById('actionOption_17'); // Approve and override
                            }
                            if (canReject) {
                                $('a#actionOption_8').removeClass('disabled'); // Reject
                                $('a#actionOption_18').removeClass('disabled'); // Reject and override
                                $actionMenu.data('complexDropdown').enableOptionById('actionOption_8'); // Reject
                                $actionMenu.data('complexDropdown').enableOptionById('actionOption_18'); // Reject and override
                            }
                        }

                        if (canUnsubmit) {
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_20'); // Unsubmit
                            $('a#actionOption_20').removeClass('disabled'); // Unsubmit
                        }

                        if (singleSelect) {
                            var canClone = exists('canClone_' + communicationId);

                            $('a#actionOption_3').removeClass('disabled'); // Add
                            $actionMenu.data('complexDropdown').enableOptionById('actionOption_3'); // Add

                            if(canClone) {
                                $('a#actionOption_4').removeClass('disabled'); // Clone
                                $actionMenu.data('complexDropdown').enableOptionById('actionOption_4'); // Clone
                            }

                            $('a#actionOption_5').removeClass('disabled'); // Proof
                            common.enableElement($proofBtn); // Proof
                            if (canSocialize) {
                                $('a#actionOption_12').removeClass('disabled'); // Socialize Proof
                                $actionMenu.data('complexDropdown').enableOptionById('actionOption_12'); // Socialize Proof
                            }
                        }

                    }
                }

                // *********  LIST TABLE FUNCTIONS: END  *********
            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>
    <msgpt:BodyNew type="${isEmbedded ? 'iframe' : 'default'}">
        <!-- FLAGS AND PERMISSIONS -->
        <c:set var="editPerm" value="false" scope="request"/>
        <msgpt:IfAuthGranted authority="ROLE_LICENCED_COMMUNICATIONS_EDIT">
            <c:set var="editPerm" value="true" scope="request"/>
            <input type="hidden" id="editCommunicationPerm">
        </msgpt:IfAuthGranted>
        <c:set var="reassignEditPerm" value="false" scope="request"/>
        <msgpt:IfAuthGranted authority="ROLE_LICENCED_COMMUNICATIONS_EDIT">
            <c:set var="reassignEditPerm" value="true" scope="request"/>
            <input type="hidden" id="reassignEditCommunicationPerm">
        </msgpt:IfAuthGranted>
        <c:set var="approvePerm" value="false" scope="request"/>
        <msgpt:IfAuthGranted authority="ROLE_LICENCED_COMMUNICATIONS_APPROVE">
            <c:set var="approvePerm" value="true" scope="request"/>
            <input type="hidden" id="approvePerm"/>
        </msgpt:IfAuthGranted>
        <c:set var="viewMetatagsPermission" value="false"/>
        <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
            <c:set var="viewMetatagsPermission" value="true"/>
        </msgpt:IfAuthGranted>
        <c:set var="unsubmitPerm" value="false" scope="request"/>
        <msgpt:IfAuthGranted authority="ROLE_LICENCED_COMMUNICATIONS_SETUP">
            <c:set var="unsubmitPerm" value="true" scope="request"/>
        </msgpt:IfAuthGranted>

        <c:choose>
            <c:when test="${isEmbedded}">
                <div class="actionsBarContainer">
                    <div class="actionBarHeaderLabel featureLabelText">
                        <fmtSpring:message code="page.lable.messagepoint.connected" />
                        <span id="headerTextForSalesforce" style="display: none;">
						<fmtSpring:message code="page.lable.for.salesforce" />
					</span>
                    </div>
                    <div style="text-align: right; padding: 12px 20px;">
                        <img src="${contextPath}/includes/themes/logos/messagepoint_text_logo.png" class="d-inline-block"
                             alt="${msgpt:getMessage('page.label.header.logo')}"/>
                    </div>
                </div>
            </c:when>
            <c:otherwise>
                <msgpt:BannerNew  edit="true" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>" applyTabs="false" />
                <msgpt:NewNavigationTabs edit="true" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>" />
                <msgpt:ContextBarNew languageContextApplied="false" touchpointSetupContext="true"/>
            </c:otherwise>
        </c:choose>

        <msgpt:LowerContainer fullPanel="true" extendedWidth="true" cssClass="fluid-content">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel extendedWidth="true">
                <c:if test="${not trashTpContext }">
                    <form:form method="post" modelAttribute="command">
                        <form:hidden path="exportName" id="exportName"/>
                        <div class="pt-2">
                            <form:errors path="*">
                                <msgpt:Information errorMsgs="${messages}" type="error"/>
                            </form:errors>
                            <input type="hidden" id="isTestCenterContext" value="${isTestCenterContext}"/>
                            <c:choose>
                                <c:when test="${not connectedEnabled}">
                                    <div id="infoMsg_connectedNotEnabled"
                                         class="alert alert-info" role="alert">
                                        <strong class="mr-2">
                                            <i class="fas fa-info-circle fa-lg mr-2"
                                               aria-hidden="true"></i><fmtSpring:message
                                                code="page.label.info"/>:
                                        </strong>
                                        <fmtSpring:message
                                                code="page.text.connected.not.enabled"/>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                
                                	<c:if test="${empty document.connectedWorkflow}">
			                            <msgpt:Information type="warning">
			                                <fmtSpring:message code="page.text.warning.no.workflow.assigned"/>
			                            </msgpt:Information>
                                	</c:if>
                                
                                    <div id="infoMsg_invalidFilterSetting"
                                         class="alert alert-info alert-dismissible fade show d-none" role="alert">
                                        <strong class="mr-2">
                                            <i class="fas fa-info-circle fa-lg mr-2"
                                               aria-hidden="true"></i><fmtSpring:message
                                                code="page.label.info"/>:
                                        </strong>
                                        <fmtSpring:message
                                                code="page.text.my.filter.does.not.apply.to.active.communications"/>
                                        <button type="button" class="close mt-1" data-dismiss="alert"
                                                aria-label="Close">
                                            <i class="far fa-times" aria-hidden="true"></i>
                                        </button>
                                    </div>
                                    <c:if test="${editPerm}">
                                        <c:choose>
                                            <c:when test="${not hasRequestorConfigured}">
                                                <div id="infoMsg_noRequestorConfigured"
                                                     class="alert alert-info alert-dismissible fade show" role="alert">
                                                    <strong class="mr-2">
                                                        <i class="fas fa-info-circle fa-lg mr-2"
                                                           aria-hidden="true"></i><fmtSpring:message
                                                            code="page.label.info"/>:
                                                    </strong>
                                                    <fmtSpring:message
                                                            code="page.text.requester.configuration.required"/>
                                                    <button type="button" class="close mt-1" data-dismiss="alert"
                                                            aria-label="Close">
                                                        <i class="far fa-times" aria-hidden="true"></i>
                                                    </button>
                                                </div>
                                            </c:when>
                                            <c:when test="${not hasVariablesForAllItems}">
                                                <div id="infoMsg_unsetDataVariables"
                                                     class="alert alert-danger alert-dismissible fade show" role="alert">
                                                    <strong class="mr-2">
                                                        <i class="fas fa-minus-circle mr-2"
                                                           aria-hidden="true"></i><fmtSpring:message
                                                            code="page.label.warning"/>:
                                                    </strong>
                                                    <fmtSpring:message
                                                            code="client_messages.text.has_no_variable" />
                                                    <c:if test="${invalidDataItemsName.length() > 400}">
                                                        <div>${invalidDataItemsName.substring(0, 400)}</div>
                                                        <c:if test="${invalidDataItemsName.length() > 400}">
                                                            <span id="readMoreContent"> </span>
                                                            <div class="read-more" onclick="readMore(1)" id = "readMoreBtn" style="cursor: pointer"> Read more &gt;&gt;&gt; </div>
                                                        </c:if>
                                                        <button type="button" class="close mt-1" data-dismiss="alert"
                                                                aria-label="Close">
                                                            <i class="far fa-times" aria-hidden="true"></i>
                                                        </button>
                                                    </c:if>
                                                    <c:if test="${invalidDataItemsName.length() <= 400}">
                                                        ${invalidDataItemsName}
                                                    </c:if>
                                                    <button type="button" class="close mt-1" data-dismiss="alert"
                                                            aria-label="Close">
                                                        <i class="far fa-times" aria-hidden="true"></i>
                                                    </button>
                                                </div>
                                            </c:when>
                                            <c:when test="${missingDataReferenceConnection}">
                                                <div id="infoMsg_missingDataReferenceConnection"
                                                     class="alert alert-danger alert-dismissible fade show" role="alert">
                                                    <strong class="mr-2">
                                                        <i class="fas fa-minus-circle mr-2"
                                                           aria-hidden="true"></i><fmtSpring:message
                                                            code="page.label.warning"/>:
                                                    </strong>
                                                    <fmtSpring:message
                                                            code="client_messages.text.touchpoint_does_not_have_reference_connection" />
                                                </div>
                                            </c:when>
                                            <c:when test="${shareCollectionWithAnOldConnectedDoc}">
                                                <div id="infoMsg_shareCollectionWithAnOldConnectedDoc"
                                                     class="alert alert-danger alert-dismissible fade show" role="alert">
                                                    <strong class="mr-2">
                                                        <i class="fas fa-minus-circle mr-2"
                                                           aria-hidden="true"></i><fmtSpring:message
                                                            code="page.label.warning"/>:
                                                    </strong>
                                                    <fmtSpring:message
                                                            code="client_messages.text.touchpoint_save_interview" />
                                                </div>
                                            </c:when>
                                            <c:when test="${not hasLinkedDataElementForAllItems}">
                                                <div id="infoMsg_unsetDataElements"
                                                     class="alert alert-danger alert-dismissible fade show" role="alert">
                                                    <strong class="mr-2">
                                                        <i class="fas fa-minus-circle mr-2"
                                                           aria-hidden="true"></i><fmtSpring:message
                                                            code="page.label.warning"/>:
                                                    </strong>
                                                    <fmtSpring:message
                                                            code="client_messages.text.has_no_bridged_variables"/>
                                                    <c:if test="${variableWithoutDataElement.length() > 400}">
                                                        <div>${variableWithoutDataElement.substring(0, 400)}</div>
                                                        <c:if test="${variableWithoutDataElement.length() > 400}">
                                                            <span id="readMoreContent"> </span>
                                                            <div class="read-more" onclick="readMore(2)" id = "readMoreBtn" style="cursor: pointer"> Read more &gt;&gt;&gt; </div>
                                                        </c:if>
                                                        <button type="button" class="close mt-1" data-dismiss="alert"
                                                                aria-label="Close">
                                                            <i class="far fa-times" aria-hidden="true"></i>
                                                        </button>
                                                    </c:if>
                                                    <c:if test="${variableWithoutDataElement.length() <= 400}">
                                                        ${variableWithoutDataElement}
                                                    </c:if>
                                                </div>
                                            </c:when>
                                        </c:choose>
                                    </c:if>
                                    <div class="row flex-nowrap mt-4">
                                        <c:if test="${document.communicationDisplayTouchpointThumbnail}">
                                            <div id="widgetContainer" class="col-auto pb-2 mb-4">
                                                <div id="touchpointWidgetContainer">
                                                    <div style="width: 20rem;">
                                                        <iframe
                                                                id="touchpointNavigationWidget"
                                                                title="${msgpt:getMessage('page.label.touchpoint.widget')}"
                                                                name="touchpointNavigationWidget"
                                                                src="${contextPath}/touchpoints/touchpoint_widget.form?documentId=${param.documentId}&zoneFilter=communications&tk=${param.tk}"
                                                                allowtransparency="true"
                                                                frameborder="0"
                                                                style="width: 22rem; height: 24rem; margin: 0 0 0 -1rem; padding: 0;"
                                                                scrolling="no">
                                                        </iframe>
                                                    </div>
                                                </div>
                                            </div>
                                        </c:if>
                                        <div class="col">
                                            <div class="box-shadow-4 rounded bg-white p-4">
                                                <div class="px-2 pb-1">
                                                    <h1 class="h4 d-flex align-items-center">
                                                        <c:choose>
                                                            <c:when test="${isTestCenterContext}">
                                                                <fmtSpring:message
                                                                        code="page.label.connected.test.center"/>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <fmtSpring:message code="page.label.communications"/>
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <c:if test="${document.communicationDisplayTouchpointThumbnail}">
                                                            <button id="widgetToggleBtn"
                                                                    class="btn btn-link btn-link-inline ml-auto fs-xs font-weight-bold text-uppercase text-dark persistedClass"
                                                                    data-toggle="button" aria-pressed="false">
                                                                <i id="widgetToggleBtnIcon"
                                                                   class="btn-icon fas fa-expand-wide fa-lg mr-2 persistedClass"></i>
                                                                <span class="btn-text"><fmtSpring:message
                                                                        code="client_messages.text.expand"/></span>
                                                            </button>
                                                        </c:if>
                                                    </h1>
                                                    <div class="my-4">
                                                        <div class="d-flex align-items-center mb-3">
                                                            <form:hidden path="actionValue" id="actionElement"/>
                                                            <c:if test="${editPerm}">
                                                                <c:choose>
                                                                    <c:when test="${hasRequestorConfigured}">
                                                                        <div class="mr-3">
                                                                            <button id="addCommunicationBtn"
                                                                                    class="btn btn-primary"
                                                                                    type="button">
                                                                                <i class="far fa-plus-circle mr-2"
                                                                                   aria-hidden="true"></i>
                                                                                <fmtSpring:message
                                                                                        code="page.label.add"/>
                                                                            </button>
                                                                        </div>
                                                                    </c:when>
                                                                </c:choose>
                                                            </c:if>
                                                            <div class="btn-group border-separate mr-auto" role="group"
                                                                 aria-label="${msgpt:getMessage("page.label.actions")}">
                                                                <button id="updateBtn" type="button"
                                                                        class="btn btn-dark"
                                                                        onclick="actionSelected(10);" disabled>
                                                                    <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                                                    <fmtSpring:message
                                                                            code="action.button.label.update"/>
                                                                </button>
                                                                <button id="proofBtn" type="button" class="btn btn-dark"
                                                                        onclick="actionSelected(5);" disabled>
                                                                    <i class="far fa-file-export mr-2"
                                                                       aria-hidden="true"></i><fmtSpring:message
                                                                        code="page.label.Proof"/>
                                                                </button>
                                                                <div class="btn-group">
                                                                    <select title="${msgpt:getMessage('page.label.audit')}"
                                                                            id="exportMenu"
                                                                            class="complex-dropdown-select"
                                                                            onchange="infoItemAction(this,-1)"
                                                                            aria-label="${msgpt:getMessage('page.label.audit')}"
                                                                            data-toggle="complex-dropdown"
                                                                            data-action="menu"
                                                                            data-dropdown-class="btn-dark">
                                                                        <option id="actionOption_13"d><fmtSpring:message
                                                                                code="page.label.generate.audit.report"/></option>
                                                                        <c:if test="${not empty auditReport}">
                                                                            <option class="divider" disabled/>
                                                                            <c:choose>
                                                                                <c:when test="${not auditReport.complete && not auditReport.error}"> <!-- Audit Report in process -->
                                                                                    <option id="itemInProcess_${auditReport.id}"
                                                                                            type="inProcess" disabled>
                                                                                        <fmtSpring:message
                                                                                                code="page.label.processing"/>
                                                                                        -
                                                                                        <fmtJSTL:formatDate
                                                                                                value="${auditReport.requestDate}"
                                                                                                pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${not auditReport.complete && auditReport.error}"> <!-- Audit Report error -->
                                                                                    <option id="itemError_${auditReport.id}"
                                                                                            deliveryId="-1"
                                                                                            itemClass="null"
                                                                                            class="text-danger">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.error"/>
                                                                                        -
                                                                                        <fmtJSTL:formatDate
                                                                                                value="${auditReport.requestDate}"
                                                                                                pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:otherwise> <!-- View report -->
                                                                                    <option id="itemReport_${auditReport.id}"
                                                                                            value="${auditReport.reportPath}">
                                                                                        <fmtSpring:message
                                                                                                code="page.label.view.audit.report"/>
                                                                                        -
                                                                                        <fmtJSTL:formatDate
                                                                                                value="${auditReport.requestDate}"
                                                                                                pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                        </c:if>
                                                                    </select>
                                                                </div>
                                                                <div class="btn-group">
                                                                    <select title="${msgpt:getMessage('page.label.more')}"
                                                                            id="actionMenu"
                                                                            class="complex-dropdown-select"
                                                                            aria-label="${msgpt:getMessage('page.label.more')}"
                                                                            data-toggle="complex-dropdown"
                                                                            data-action="menu"
                                                                            data-dropdown-class="btn-dark"
                                                                            onchange="actionMenuSelected(this)">
                                                                        <c:if test="${reassignEditPerm}">
                                                                            <option id="actionOption_21" disabled>
                                                                                <fmtSpring:message
                                                                                        code="page.label.reassign.edit"/></option>
                                                                        </c:if>
                                                                        <c:if test="${editPerm and document.communicationFastOrderIndicatorEdit}">
                                                                            <option id="actionOption_15" disabled>
                                                                                <fmtSpring:message code="page.label.edit.indicator"/></option>
                                                                        </c:if>
<%--                                                                        <c:if test="${not isNewConnected}">--%>
                                                                            <option id="actionOption_4" disabled>
                                                                                <fmtSpring:message code="page.label.clone"/></option>
<%--                                                                        </c:if>--%>
                                                                        <c:if test="${not isTestCenterContext}">
                                                                            <option id="actionOption_1" disabled>
                                                                                <fmtSpring:message code="page.label.release.for.approval"/></option>
                                                                            <c:if test="${reassignPerm}">
                                                                                <option id="actionOption_23" disabled>
                                                                                    <fmtSpring:message code="page.label.reassign.release.for.approval"/></option>
                                                                            </c:if>
                                                                            <option id="actionOption_16" class="d-none" disabled>
                                                                                <fmtSpring:message code="page.label.activate"/></option>
                                                                            <c:if test="${reassignPerm}">
                                                                                <option id="actionOption_22" class="d-none" disabled>
                                                                                    <fmtSpring:message code="page.label.reassign.activate"/></option>
                                                                            </c:if>
                                                                            <c:if test="${approvePerm}">
                                                                                <option id="actionOption_2" disabled>
                                                                                    <fmtSpring:message code="page.label.approve"/></option>
                                                                                <option id="actionOption_8" disabled>
                                                                                    <fmtSpring:message code="page.label.reject"/></option>
                                                                                <option id="actionOption_17" class="d-none" disabled>
                                                                                    <fmtSpring:message code="page.label.approve.and.override"/></option>
                                                                                <option id="actionOption_18" class="d-none" disabled>
                                                                                    <fmtSpring:message code="page.label.reject.and.override"/></option>
                                                                            </c:if>
                                                                            <c:if test="${unsubmitPerm}">
                                                                                <option id="actionOption_20" disabled>
                                                                                    <fmtSpring:message code="page.label.unsubmit"/></option>
                                                                            </c:if>
                                                                        </c:if>
                                                                        <c:if test="${reassignPerm}">
                                                                            <option id="actionOption_19" disabled>
                                                                                <fmtSpring:message code="page.label.reassign"/></option>
                                                                        </c:if>
                                                                        <c:if test="${editPerm}">
                                                                            <option id="actionOption_6" disabled>
                                                                                <fmtSpring:message code="page.label.delete"/></option>
                                                                        </c:if>
                                                                        <c:if test="${editPerm && not isTestCenterContext}">
                                                                            <option id="actionOption_9">
                                                                                <fmtSpring:message code="page.label.batch"/></option>
                                                                        </c:if>
                                                                        <c:if test="${externalValidationEnabled && not isDigitalProof && not isTestCenterContext}">
                                                                            <option id="actionOption_12">
                                                                                <fmtSpring:message code="page.label.socialize.proof"/></option>
                                                                        </c:if>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="mr-3 colVisToggleContainer"
                                                                 data-toggle="tooltip"
                                                                 title="${msgpt:getMessage('client_messages.label.toggle_columns')}">
                                                            </div>
                                                            <div class="form-group position-relative d-inline-block m-0">
                                                                <label for="listSearchInput"
                                                                       class="sr-only"><fmtSpring:message
                                                                        code="page.label.search"/></label>
                                                                <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                                                   style="z-index: 1;" aria-hidden="true"></i>
                                                                <msgpt:InputFilter type="description">
                                                                    <input id="listSearchInput" type="text" size="20"
                                                                           class="form-control bg-lightest has-control-x border-0"
                                                                        ${viewMetatagsPermission && (document.communicationAppliesTagCloud || isTestCenterContext) ? 'data-toggle="tagcloud"' :''}
                                                                           data-cloud-type="${isTestCenterContext ? 15 : 7}"
                                                                           placeholder="${msgpt:getMessage('page.label.search')}"/>
                                                                </msgpt:InputFilter>
                                                            </div>
                                                        </div>
                                                        <div class="d-flex align-items-center pt-1">
                                                            <c:choose>
                                                                <c:when test="${approvePerm}">
                                                                    <span class="text-dark mr-1" id="filter">
                                                                        <fmtSpring:message code="page.label.filter"/>:
                                                                    </span>
                                                                    <div class="mx-2">
                                                                        <select id="communicationsListAssignmentFilter"
                                                                                aria-labelledby="filter"
                                                                                data-toggle="complex-dropdown"
                                                                                data-menu-class="dropdown-custom mt-2"
                                                                                class="complex-dropdown-select persistedValue"
                                                                                data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                                                onchange="toggleFilter(this)">
                                                                            <c:forEach items="${primaryFilterTypes}"
                                                                                       var="currentFilter">
                                                                                <option id="communicationsListAssignmentFilterOption_${currentFilter.id}"
                                                                                        value="${currentFilter.id}">
                                                                                    <fmtSpring:message
                                                                                            code="${currentFilter.displayMessageCode}"/>
                                                                                </option>
                                                                            </c:forEach>
                                                                        </select>
                                                                    </div>
                                                                </c:when>
                                                            </c:choose>
                                                            <c:if test="${not isTestCenterContext}">
                                                            <span class="text-dark mr-1" id="communication-status">
                                                                <fmtSpring:message
                                                                        code="page.text.communications.which.are"/>
                                                            </span>
                                                                <div class="ml-2">
                                                                    <select id="communicationsListStatusFilter"
                                                                            aria-labelledby="communication-status"
                                                                            data-toggle="complex-dropdown"
                                                                            data-menu-class="dropdown-custom mt-2"
                                                                            class="complex-dropdown-select persistedValue"
                                                                            data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                                            onchange="toggleFilter(this)">
                                                                        <option id="communicationsListStatusFilter_0"
                                                                                value="0">
                                                                            <fmtSpring:message
                                                                                    code="page.label.any.status"/>
                                                                        </option>
                                                                        <c:forEach
                                                                                items="${communicationStatusFilterTypes}"
                                                                                var="currentFilter">
                                                                            <option id="communicationsListStatusFilter_${currentFilter.id}"
                                                                                    value="${currentFilter.id}">
                                                                                <fmtSpring:message
                                                                                        code="${currentFilter.displayMessageCode}"/>
                                                                            </option>
                                                                        </c:forEach>
                                                                    </select>
                                                                </div>
                                                            </c:if>
                                                        </div>
                                                    </div>
                                                    <div class="bg-white">
                                                        <c:if test="${not empty primaryDriverLabel}">
                                                            <input id="recipientColumnName" value="${primaryDriverLabel}" style="display: none;" />
                                                        </c:if>
                                                        <msgpt:DataTable id="communicationsList"
                                                                         listHeader="page.label.communications"
                                                                         async="true"
                                                                         columnReorder="true" numUnreorderableCols="2"
                                                                         columnVisibility="true" drillDown="true"
                                                                         multiSelect="true" searchFilter="true">
                                                        </msgpt:DataTable>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- POPUP DATA -->
                                    <div id="actionSpecs" style="display: none;">
                                        <!-- ACTIONS POPUP DATA -->
                                        <!-- actionSpec_10 RESERVED: EDIT -->
                                        <div id="actionSpec_1" submitId="5"> <!-- Release for approval -->
                                            <div id="actionTitle_1"><fmtSpring:message code="page.label.confirm.release.for.approval"/></div>
                                            <div id="actionInfo_1"><fmtSpring:message code="page.text.provide.description.of.action.required"/></div>
                                            <div id="actionNote_1"></div>
                                        </div>
                                        <c:if test="${not cookie['mp-dontaskagain'].getValue().equals('true')}">
                                            <div id="actionSpec_23" submitId="23"> <!-- Reassign and Release for approval -->
                                                <div id="actionTitle_23"><fmtSpring:message code="page.label.confirm.reassign.release.for.approval"/></div>
                                                <div id="actionInfo_23"><fmtSpring:message code="page.text.reassign.to.self.release"/></div>
                                                <div id="actionNote_23"></div>
                                                <div id="actionDontAskAgain_23"></div>
                                            </div>
                                        </c:if>
                                        <div id="actionSpec_4" submitId="8"> <!-- Clone -->
                                            <div id="actionTitle_4"><fmtSpring:message code="page.label.confirm.clone"/></div>
                                            <div id="actionInfo_4"><fmtSpring:message code="page.text.clone.communication"/></div>
                                            <div id="actionCloneOptions_4"></div>
                                        </div>
                                        <div id="actionSpec_5" submitId="7"> <!-- Proof -->
                                            <div id="actionTitle_5"><fmtSpring:message code="page.label.confirm.proof"/></div>
                                            <div id="actionInfo_5">
                                                <div class="proofAction_email" style="display: none;">
                                                    <fmtSpring:message code="page.text.email.proof.selected.communication"/></div>
                                                 <div class="proofAction_print" style="display: none;">
                                                     <fmtSpring:message code="page.text.proof.selected.communication"/></div>
                                            </div>
                                            <div id="actionProofEmail_5"></div>
                                        </div>
                                        <c:if test="${not cookie['mp-dontaskagain'].getValue().equals('true')}">
                                            <div id="actionSpec_21" type="simpleConfirm" submitId="21">
                                                <div id="actionTitle_21"><fmtSpring:message code="page.label.confirm.reassign.edit"/><br/></div>
                                                <div id="actionInfo_21"><fmtSpring:message code="page.text.reassign.to.self"/></div>
                                                <div id="actionApproval_21" approveSubmitId="21"></div>
                                                <div id="actionDontAskAgain_21"></div>
                                            </div>
                                        </c:if>
                                        <div id="actionSpec_2" type="simpleConfirm" submitId="3"> <!-- Approve -->
                                            <div id="actionTitle_2"><fmtSpring:message code="page.label.approve"/></div>
                                            <div id="actionInfo_2"><fmtSpring:message code="page.text.approve.this.asset"/></div>
                                            <div id="actionApproval_2" approveSubmitId="3"></div>
                                        </div>
                                        <div id="actionSpec_8" type="simpleConfirm" submitId="4"> <!-- Reject -->
                                            <div id="actionTitle_8"><fmtSpring:message code="page.label.reject"/></div>
                                            <div id="actionInfo_8"><fmtSpring:message code="page.text.would.you.like.reject"/></div>
                                            <div id="actionNote_8" required="true"><fmtSpring:message code="page.text.note.required.to.reject.brackets"/></div>
                                            <div id="actionUserSelect_8" type="communications_rejectToUsers"
                                                 required="true"
                                                 firstDefault="true"></div>
                                            <div id="actionReject_8" rejectSubmitId="4"></div>
                                        </div>
                                        <!-- Edit Indicator -->
                                        <div id="actionSpec_15" submitId="15"> <!-- Rename selection -->
                                            <div id="actionTitle_15"><fmtSpring:message code="page.label.confirm.edit.indicator"/></div>
                                            <div id="actionInfo_15"><fmtSpring:message code="page.text.edit.selected.indicator"/></div>
                                            <div id="actionRename_15" required="true"></div>
                                        </div>
                                        <!-- Activate -->
                                        <div id="actionSpec_16" submitId="6">
                                            <div id="actionTitle_16"><fmtSpring:message code="page.label.confirm.activate"/></div>
                                            <div id="actionInfo_16"><fmtSpring:message code="page.text.would.you.like.to.activate.communications"/></div>
                                            <div id="actionNote_16"></div>
                                        </div>
                                        <!-- Activate -->
                                        <c:if test="${not cookie['mp-dontaskagain'].getValue().equals('true')}">
                                            <div id="actionSpec_22" submitId="22">
                                                <div id="actionTitle_22"><fmtSpring:message code="page.label.confirm.reassign.activate"/></div>
                                                <div id="actionInfo_22"><fmtSpring:message code="page.text.reassign.to.self.activate"/></div>
                                                <div id="actionNote_22"></div>
                                                <div id="actionDontAskAgain_22"></div>
                                            </div>
                                        </c:if>
                                        <!-- Override approve -->
                                        <div id="actionSpec_17" type="simpleConfirm" submitId="3" contentWidth="330px">
                                            <!-- Override Approve -->
                                            <div id="actionTitle_17"><fmtSpring:message code="page.label.approve.workflow.owner"/></div>
                                            <div id="actionInfo_17"><fmtSpring:message code="page.text.workflow.owners.may.approve.step"/></div>
                                            <div id="actionInfo_17"><fmtSpring:message code="page.text.would.you.like.to.approve.step"/></div>
                                            <div id="actionApproval_17" approveSubmitId="3"></div>
                                        </div>
                                        <!-- Override reject -->
                                        <div id="actionSpec_18" type="simpleConfirm" submitId="4" contentWidth="330px">
                                            <!-- Override Reject -->
                                            <div id="actionTitle_18"><fmtSpring:message code="page.label.reject.workflow.owner"/></div>
                                            <div id="actionInfo_18"><fmtSpring:message code="page.text.workflow.owners.may.reject.step"/></div>
                                            <div id="actionNote_18" required="true"><fmtSpring:message code="page.text.note.required.to.reject.brackets"/></div>
                                            <div id="actionUserSelect_18" required="true" type="communications_rejectToUsers"></div>
                                            <div id="actionReject_18" rejectSubmitId="4"></div>
                                        </div>
                                        <div id="actionSpec_19" submitId="11"> <!-- Reassign to user -->
                                            <div id="actionTitle_19"><fmtSpring:message code="page.label.confirm.reassign.to.user"/></div>
                                            <div id="actionInfo_19"><fmtSpring:message code="page.text.assign.message.to.user"/></div>
                                            <div id="actionNote_19"></div>
                                            <div id="actionUserSelect_19" required="true" type="communications_currentStageUsers"></div>
                                        </div>
                                        <div id="actionSpec_6" type="simpleConfirm" submitId="2">
                                            <!-- Delete communication -->
                                            <div id="actionTitle_6"><fmtSpring:message code="page.label.confirm.delete"/></div>
                                            <div id="actionInfo_6"><fmtSpring:message code="page.text.delete.selected.communications"/></div>
                                        </div>
                                        <div id="actionSpec_9" type="simpleConfirm" submitId="9"> <!-- Batch -->
                                            <div id="actionTitle_9"><fmtSpring:message code="page.label.confirm.production.batch"/></div>
                                            <div id="actionInfo_9"><fmtSpring:message code="page.text.select.data.range.for.batch.window"/></div>
                                            <div id="actionDateRange_9"></div>
                                        </div>
                                        <c:if test="${externalValidationEnabled && not isDigitalProof}">
                                            <div id="actionSpec_12" type="simpleConfirm" submitId="12">
                                                <!-- Socialize Proof -->
                                                <div id="actionTitle_12"><fmtSpring:message code="page.label.socialize.proof"/></div>
                                                <div id="actionInfo_12"><fmtSpring:message code="page.text.enter.recipient.emial.for.proof.socialization"/></div>
                                                <div id="actionSocializeEmail_12"></div>
                                            </div>
                                        </c:if>
                                        <c:if test="${unsubmitPerm}">
                                            <div id="actionSpec_20" submitId="14"> <!-- Unsubmit -->
                                                <div id="actionTitle_20"><fmtSpring:message code="page.label.confirm.unsubmit"/></div>
                                                <div id="actionInfo_20"><fmtSpring:message code="page.text.unsubmit.orders"/></div>
                                            </div>
                                        </c:if>
                                    </div>
                                    <!-- END: POPUP DATA -->

                                    <!-- POPUP INTERFACE -->
                                    <msgpt:Popup id="actionPopup" theme="minimal">
                                        <div id="actionPopupInfoFrame">
                                            <div id="actionPopupInfo">&nbsp;</div>
                                        </div>
                                        <div id="actionPopupNote" state="default" style="padding: 3px 15px 6px 15px; white-space:nowrap;" align="left">
                                            <msgpt:InputFilter type="comment">
                                                <form:textarea path="userNote" onkeyup="validatePopupReq();"
                                                               cssStyle="width: 320px; padding: 4px;" rows="3"
                                                               onclick="initTextarea(this)"/>
                                            </msgpt:InputFilter>
                                        </div>
                                        <div id="actionPopupDontAskAgain" style="padding: 3px 15px 6px 15px;" align="left">
                                            <table class="innerContentTable" cellspacing="0" cellpadding="0" border="0">
                                                <tr>
                                                    <td style="padding: 2px 0px; vertical-align: middle; color: #444;"align="left">
                                                        <form:checkbox path="dontAskAgain" cssClass="checkbox" />
                                                    </td>
                                                    <td style="padding: 2px 10px; vertical-align: middle; color: #444;">
                                                        <fmtSpring:message code="page.label.dont.ask.again"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div id="actionPopupRename">
                                            <div class="formControl">
                                                <label><span class="labelText"><fmtSpring:message code="page.label.name"/></span></label>
                                                <div class="controlWrapper">
                                                    <msgpt:InputFilter type="simpleName">

                                                        <form:textarea path="recipientIdentifier"
                                                                       onkeyup="validatePopupReq();" onchange="validatePopupReq();"
                                                                       cssStyle="width: 600px; padding: 4px;" rows="3"
                                                                       id="rename_newNodeName" onfocus="this.select()"/>

                                                    </msgpt:InputFilter>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="actionPopupUserSelect" style="padding: 2px 8px 6px 8px;"
                                             align="center">
                                            <form:select id="userSelect" path="assignedToUser" cssClass="inputL"
                                                         onchange="validatePopupReq()" onkeyup="validatePopupReq()">
                                                <option id="0" value="0"><fmtSpring:message code="page.text.loading"/></option>
                                            </form:select>
                                        </div>
                                        <div id="actionPopupCloneOptions" style="padding: 2px 8px 6px 8px;" align="center">
                                            <table class="innerContentTable" cellspacing="0" cellpadding="0" border="0">
                                                <tr>
                                                    <td style="padding: 2px 0px; vertical-align: middle; color: #444;"align="left">
                                                        <fmtSpring:message code="page.label.clone.interview"/>:
                                                    </td>
                                                    <td style="padding: 2px 10px; vertical-align: middle; color: #444;">
                                                        <form:checkbox path="cloneInterview" cssClass="checkbox" disabled="${isNewConnected}" />
                                                        <c:if test="${isNewConnected}">
                                                            <input type="hidden" name="cloneInterview" value="true"/>
                                                        </c:if>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="padding: 2px 0px; vertical-align: middle; color: #444;" align="left">
                                                        <fmtSpring:message code="page.label.clone.interactive"/>:
                                                    </td>
                                                    <td style="padding: 2px 10px; vertical-align: middle; color: #444;">
                                                        <form:checkbox path="cloneInteractive" cssClass="checkbox" />
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div id="actionPopupDateRange" style="padding: 2px 8px 6px 8px;" align="center">
                                            <table class="innerContentTable" cellspacing="0" cellpadding="0" border="0">
                                                <tr>
                                                    <td style="padding: 2px 0px; vertical-align: middle; color: #444;" align="left">
                                                        <fmtSpring:message code="page.label.start.date"/>:
                                                    </td>
                                                    <td style="padding: 2px 10px; vertical-align: middle; color: #444;">
                                                        <msgpt:Calendar id="batchWindowStartDate"
                                                                        path="batchWindowStartDate"
                                                                        viewableDateFormat="${viewableDateFormat}"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="padding: 2px 0px; vertical-align: middle; color: #444;" align="left">
                                                        <fmtSpring:message code="page.label.end.date"/>:
                                                    </td>
                                                    <td style="padding: 2px 10px; vertical-align: middle; color: #444;">
                                                        <msgpt:Calendar id="batchWindowEndDate"
                                                                        path="batchWindowEndDate"
                                                                        viewableDateFormat="${viewableDateFormat}"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div id="actionPopupProofEmail"
                                             style="padding: 3px 15px 6px 15px; white-space:nowrap;"
                                             align="left">
                                            <div class="proofAction_email" style="display: none;">
                                                <msgpt:InputFilter type="email">
                                                    <form:input path="proofEmail" cssClass="inputXL"/>
                                                </msgpt:InputFilter>
                                            </div>
                                        </div>
                                        <div id="actionPopupSocializeEmail"
                                             style="padding: 3px 15px 6px 15px; white-space:nowrap;"
                                             align="left">
                                            <msgpt:InputFilter type="email">
                                                <form:input path="socializeEmail" cssClass="inputXL"/>
                                            </msgpt:InputFilter>
                                        </div>
                                        <div id="actionPopupApprovalButtons" class="actionPopupButtonsContainer">
                                            <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                            <span id="approveBtn">
                                                <msgpt:Button URL="#" label="page.flow.approve" primary="true"/>
                                            </span>
                                            <span id="approveBtnDisabled" style="display: none;">
                                                <msgpt:Button URL="#" label="page.flow.approve" disabled="true"/>
                                            </span>
                                        </div>
                                        <div id="actionPopupRejectButtons" class="actionPopupButtonsContainer">
                                            <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/>
                                            <span id="rejectBtn"><msgpt:Button URL="#" label="page.flow.reject" primary="true"/></span>
                                            <span id="rejectBtnDisabled" style="display: none;">
                                                <msgpt:Button URL="#" label="page.flow.reject" disabled="true"/></span>
                                        </div>
                                        <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                                            <span id="cancelBtnDisabled" style="display: none;">
                                                <msgpt:Button URL="#" label="page.label.cancel" disabled="true"/></span>
                                            <span id="cancelBtnEnabled">
                                                <msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel"/></span>
                                            <span id="continueBtnDisabled" style="display: none;">
                                                <msgpt:Button URL="#" label="page.label.continue" disabled="true"/></span>
                                            <span id="continueBtnEnabled">
                                                <msgpt:Button URL="#" label="page.label.continue" primary="true"/></span>
                                        </div>
                                        <div id="actionPopupCloseButton" class="actionPopupButtonsContainer">
                                            <msgpt:Button URL="javascript:actionCancel();" label="page.label.close"/>
                                        </div>
                                    </msgpt:Popup>
                                    <!-- END: POPUP INTERFACE -->

                                    <!-- Has communication zones -->
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </form:form>
                </c:if>
                <c:if test="${trashTpContext}">
                    <div class="alert alert-info" role="alert">
                        <strong class="mr-2">
                            <i class="fas fa-info-circle fa-lg mr-2" aria-hidden="true"></i>
                            <fmtSpring:message code="page.label.info"/>:
                        </strong>
                        <fmtSpring:message code="page.text.trash.touchpoint.does.not.apply"/>
                    </div>
                </c:if>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
        <msgpt:ContextMenu name="communicationsList">
            <c:if test="${editPerm}">
                <msgpt:ContextMenuEntry name="actionOption_10" link="#actionSelected:10">
                    <fmtSpring:message code="page.label.edit"/></msgpt:ContextMenuEntry>
            </c:if>
            <c:if test="${reassignEditPerm}">
                <msgpt:ContextMenuEntry name="actionOption_21" link="#actionSelected:21">
                    <fmtSpring:message code="page.label.reassign.edit"/></msgpt:ContextMenuEntry>
            </c:if>
            <c:if test="${editPerm}">
                <c:if test="${document.communicationFastOrderIndicatorEdit}">
                    <msgpt:ContextMenuEntry name="actionOption_15" link="#actionSelected:15">
                        <fmtSpring:message code="page.label.edit.indicator"/></msgpt:ContextMenuEntry>
                </c:if>
<%--                <c:if test="${not isNewConnected}">--%>
                    <msgpt:ContextMenuEntry name="actionOption_4" link="#actionSelected:4">
                        <fmtSpring:message code="page.label.clone"/></msgpt:ContextMenuEntry>
<%--                </c:if>--%>
            </c:if>
            <c:if test="${reassignPerm}">
                <msgpt:ContextMenuEntry name="actionOption_19" link="#actionSelected:19">
                    <fmtSpring:message code="page.label.reassign"/></msgpt:ContextMenuEntry>
            </c:if>
            <msgpt:ContextMenuEntry name="actionOption_5" link="#actionSelected:5">
                <fmtSpring:message code="page.text.proof"/></msgpt:ContextMenuEntry>
            <c:if test="${not isTestCenterContext}">
                <msgpt:ContextMenuEntry name="actionOption_1" link="#actionSelected:1">
                    <fmtSpring:message code="page.label.release.for.approval"/></msgpt:ContextMenuEntry>
                <c:if test="${reassignPerm}">
                    <msgpt:ContextMenuEntry name="actionOption_23" link="#actionSelected:23">
                        <fmtSpring:message code="page.label.reassign.release.for.approval"/></msgpt:ContextMenuEntry>
                </c:if>
                <msgpt:ContextMenuEntry name="actionOption_16" link="#actionSelected:16">
                    <fmtSpring:message code="page.label.activate"/></msgpt:ContextMenuEntry>
                <c:if test="${reassignPerm}">
                    <msgpt:ContextMenuEntry name="actionOption_22" link="#actionSelected:22">
                        <fmtSpring:message code="page.label.reassign.activate"/></msgpt:ContextMenuEntry>
                </c:if>
                <c:if test="${approvePerm}">
                    <msgpt:ContextMenuEntry name="actionOption_2" link="#actionSelected:2">
                        <fmtSpring:message code="page.label.approve"/></msgpt:ContextMenuEntry>
                    <msgpt:ContextMenuEntry name="actionOption_8" link="#actionSelected:8">
                        <fmtSpring:message code="page.label.reject"/></msgpt:ContextMenuEntry>
                    <msgpt:ContextMenuEntry name="actionOption_17" link="#actionSelected:17">
                        <fmtSpring:message code="page.label.approve.and.override"/></msgpt:ContextMenuEntry>
                    <msgpt:ContextMenuEntry name="actionOption_18" link="#actionSelected:18">
                        <fmtSpring:message code="page.label.reject.and.override"/></msgpt:ContextMenuEntry>
                </c:if>
                <c:if test="${unsubmitPerm}">
                    <msgpt:ContextMenuEntry name="actionOption_20" link="#actionSelected:20">
                        <fmtSpring:message code="page.label.unsubmit"/></msgpt:ContextMenuEntry>
                </c:if>
            </c:if>
            <c:if test="${editPerm}">
                <msgpt:ContextMenuEntry name="actionOption_6" link="#actionSelected:6">
                    <fmtSpring:message code="page.label.delete"/></msgpt:ContextMenuEntry>
            </c:if>
            <c:if test="${externalValidationEnabled && not isDigitalProof && not isTestCenterContext}">
                <msgpt:ContextMenuEntry name="actionOption_12" link="#actionSelected:12">
                    <fmtSpring:message code="page.label.socialize.proof"/></msgpt:ContextMenuEntry>
            </c:if>
        </msgpt:ContextMenu>
    </msgpt:BodyNew>
</msgpt:Html5>
