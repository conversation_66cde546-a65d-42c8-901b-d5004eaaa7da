// *********  INIT: START  *********

var $zoneSelect, $sectionListSelect, $updateBtn, $cloneBtn, $exportMenu, $actionMenu, $widgetToggle;
var $listTypeToggle = $('.cardViewToggle');


$(function () {

    $zoneSelect = $('#zoneSelect');
    $sectionListSelect = $('#sectionListSelect');
    $updateBtn = $('#updateBtn');
    $cloneBtn = $('#cloneBtn');
    $exportMenu = $('#exportMenu');
    $actionMenu = $('#actionMenu');
    $widgetToggle = $('#widgetToggleBtn');

    // Hide widget based on persisted state
    if ($widgetToggle.is('.active')) {

        $widgetToggle.children('.btn-text').text('Compress');
        $widgetToggle.attr('aria-pressed', true);

        updateWidgetDisplay(false);

    }

    $widgetToggle.removeClass('focus');
    updatePersistedClass($widgetToggle);

    toggleAddMessageBtn();

    var $searchTagCloud = $('#listSearchInput[data-toggle="tagcloud"]');

    $searchTagCloud.tagCloud({
        tagCloudType: $searchTagCloud.data('cloud-type'),
        documentId: $searchTagCloud.data('document-id'),
        inputType: 'search',
        rightOffsetAdj: 16,
        topOffsetAdj: 12,
        popupLocation: 'bottom-left',
        afterInputValueChange: function (o, val) {
            $searchTagCloud.keyup();
        }
    });

    $("#metadataSelect,#taskMetadataSelect").styleActionElement({labelAlign: true});

    $('#taskIndicator').taskManager({
        item_type: 3,
        item_id: getParam('touchpointSelectionId'),
        button_type: "icon_only",
        popup_location: "right"
    });
    
    $listTypeToggle
	    .removeClass('focus')
	    .click(function () {
	        _.defer(function () {
	            updatePersistedClass($listTypeToggle);
	            getTopFrame().location.reload();
	        });
	    });
	
	if ($listTypeToggle.is('.active')) {
		$listTypeToggle.attr('aria-pressed', true);
		$('.colVisToggleContainer').hide();
		$('.cardSortInterface').show();
	}

    // Init widget toggle
    $widgetToggle.on('click', function () {

        _.defer(function () {

            var $btnText = $widgetToggle.children('.btn-text'),
                $btnIcon = $widgetToggle.children('.btn-icon'),
                initializeWidget = false;

            if ($widgetToggle.is('.active')) {

                $btnText.text(client_messages.text.compress);
                $btnIcon.removeClass('fa-expand-wide').addClass('fa-compress-wide');

                updatePersistedClass($btnIcon);
                updatePersistedClass($widgetToggle);

                updateWidgetDisplay(initializeWidget);

            } else {

                $btnText.text(client_messages.text.expand);
                $btnIcon.addClass('fa-expand-wide').removeClass('fa-compress-wide');

                updatePersistedClass($btnIcon);
                updatePersistedClass($widgetToggle);

                if (!$("#touchpointNavigationWidget").contents().find(".touchpointWidgetContainer").length)
                    initializeWidget = true;

                updateWidgetDisplay(initializeWidget);

            }

        });

    });

    $("[id*='taskInProcess']").actionStatusPolling({
        itemLabel: client_messages.text.background_task,
        type: 'backgroundtask',
        postItemInit: function (item) {
            var menuEleId = $(item).closest('select').attr('id') + '_menuTable';
            if ($('#' + menuEleId).find("[id*='taskInProcess']").length == 0)
                $('#' + menuEleId).find('.menuItemInProcessIcon,.menuItemInProcessSpacer').remove();
        },
        onInit: function (o) {
            if ($(o.option).closest('.actionBtnTable').find('.actionBtnText').find('.menuItemInProcessIcon').length == 0)
                $(o.option).closest('.actionBtnTable').find('.actionBtnText').prepend("<div class=\"menuItemInProcessIcon\" style=\"display: inline-block; position: absolute;\"></div><div class=\"menuItemInProcessSpacer\" style=\"display: inline-block; width: 22px; height: 1px;\"></div>");
        }
    });

    $("[id*='itemInProcess']").actionStatusPolling({
        itemLabel: client_messages.text.audit_report,
        type: 'auditreport',
        postItemInit: function ($item) {
            var $option = $('#' + $item.attr('id')),
                $select = $option.parent(),
                itemIndex = $select.children().index($option),
                $dropdownItem = $select.siblings('.dropdown').find('.dropdown-content').children().eq(itemIndex);

            if ($item.attr('id').indexOf('itemInProcess') < 0)
                $dropdownItem.find('.progress-loader').remove();
            else
                $dropdownItem.prepend('<div class="progress-loader mr-2">' +
                    '<i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>' +
                    '</div>');

        },
        onInit: function (o) {
            var $option = $('#' + o.option.attr('id')),
                $select = $option.parent(),
                itemIndex = $select.children().index($option),
                $dropdown = $select.siblings('.dropdown'),
                $dropdownItem = $dropdown.find('.dropdown-content').children().eq(itemIndex);

            $dropdownItem.prepend('<div class="progress-loader mr-2">' +
                '<i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>' +
                '</div>');


            // TODO: REVIEW RENDER/PAINT ISSUE WITH MULTIPLE SPINNING (ANIMATED) ELEMENTS
            /*$dropdown.children('.dropdown-toggle').prepend('<div class="progress-loader mr-2 bg-transparent p-0">' +
                '<i class="progress-loader-icon far fa-spinner-third text-white" aria-hidden="true"></i>' +
                '</div>');*/
        }
    });

    // Segmentation analysis result polling init
    $("[id^='segStatusPollingContainer_']").each(function () {
        if ($(this).attr('pollingInit') == undefined || $(this).attr('pollingInit') != "true") {
            $(this).attr('pollingInit', 'true');
            pollForSegResult(parseId(this));
        }
    });

    var $addMessageBtn = $('#addMessageBtn');

    if (hasMetadataFormDef) {

        $addMessageBtn.click(function () {

            // Clear the previous selection
            $('#metadataSelect_menu').find('.selectedOption').text($('#metadataSelect').find('option:first').text());

            actionSelected(29);

        });

    } else {

        $addMessageBtn.on('click', function () {

            iFrameAction(2);

        }).postTrigger();

    }

    $sectionListSelect.on('change', toggleSection);
    $zoneSelect.on('change', toggleZone);

    checkWidgetControlsInitialValue();
    updateZoneListForSelectedSection();					// Adjust zone list for section persisted value
    updateWidgetControlsTooltip();

    if($actionMenu.length) {
        toggleFilter($('#messagesListAssignmentFilter'), true);	// Adjust messaging based on filter settings
        validateActionReq();
    }

    handleOnLoadAssets(); // Onload asset popup: E-mail links/Content search

});

$(document).ready(function () {
    $('#taskVariantSelect').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        console.log(selectedOption.attr('data-clean'))
        $('#taskVariantSelect_menuTable .actionSelectMenuText.selectedOption').text(selectedOption.attr('data-clean'));
    });
});

function updateWidgetControlsTooltip() {

    $zoneSelect.add($sectionListSelect).each(function () {

        var $el = $(this),
            $tooltipTarget = $el.closest('.btn-group'),
            tooltipText = $el.siblings('.dropdown').find('.dropdown-label').text();

        if ($tooltipTarget.attr('data-original-title'))
            $tooltipTarget.attr('data-original-title', tooltipText);

        else
            tooltipText.attr('title', tooltipText);

    });

}

function handleOnLoadAssets () {

    var contentObjectId = getParam('contentObjectId'),
        documentId = getParam('documentId'),
        contentSelectionId = getParam('contentSelectionId'),
        touchpointSelectionId = getParam('touchpointSelectionId'),
        statusViewId = getParam('statusViewId'),
        paramInstId = getParam('paramInstId'),
        url;

    if(contentObjectId && contentObjectId !== ""){
        if (paramInstId && paramInstId !== "" && statusViewId && statusViewId !== "") {
            url = context + '/content/content_object_view_content_dynamic_variant.form?documentId=' + documentId + '&contentObjectId=' + contentObjectId + '&paramInstId=' + paramInstId + '&statusViewId=' + statusViewId;

        }else if (((contentSelectionId && contentSelectionId !== "") || (touchpointSelectionId && touchpointSelectionId !== "")) && statusViewId && statusViewId !== "") {
            var contentSelectionAttr = (contentSelectionId && contentSelectionId !== "")?('&contentSelectionId=' + contentSelectionId):'';
            var touchpointSelectionAttr = (touchpointSelectionId && touchpointSelectionId !== "")?('&touchpointSelectionId=' + touchpointSelectionId):'';
            url = context + '/tpadmin/touchpoint_content_selection_view.form?contentObjectId=' + contentObjectId + contentSelectionAttr + touchpointSelectionAttr + '&statusViewId=' + statusViewId;

        }else {
            url = context + '/content/content_object_view.form?documentId=' + documentId + '&contentObjectId=' + contentObjectId + '&statusViewId=1';
        }
    }

    if (url) {

        var $body = $('body'),
            $fullPageLoader = $('<div id="fullPageLoader" class="modal fade d-block">' +
            '<div class="modal-dialog">' +
            '<div class="m-5 p-1 text-center">' +
            '<div class="progress-loader progress-loader-lg mb-3">' +
            '<i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>' +
            '</div>' +
            '<p class="text-white">' +
            client_messages.text.loading +
            '</p>' +
            '</div>' +
            '</div>' +
            '</div>');

        $body
            .addClass('modal-open')
            .append($fullPageLoader)
            .append('<div class="modal-backdrop fade"></div>');

        _.defer(function () {

            $('.modal-backdrop').addClass('show');

            _.delay(function () {

                $('#fullPageLoader').addClass('show');

            }, 150);

        });

        javascriptHref(url);

    }

}

// *********  INIT END  *********

// *********  WIDGET FUNCTIONS: START  *********

function toggleAddMessageBtn() {

    var $addMessageBtn = $('#addMessageBtn');

    if ($zoneSelect.val() == null || $zoneSelect.val() == 0 || !canUpdateVariant) {

        common.makeItFocusable($addMessageBtn.parent());
        $addMessageBtn.css('pointer-events', 'none').parent().tooltip('enable');

        common.disableElement($addMessageBtn);

    } else {

        common.makeItNonFocusable($addMessageBtn.parent());
        $addMessageBtn.css('pointer-events', 'auto').parent().tooltip('disable');

        common.enableElement($addMessageBtn);

    }

}

function updateWidgetDisplay(reloadWidget) {

    var $widgetContainer = $('#widgetContainer');

    if ($widgetToggle.is('.active')) {

        $widgetContainer.addClass('d-none');

    } else {

        $widgetContainer.removeClass('d-none');

        if (reloadWidget && window.frames['touchpointNavigationWidget'])
            window.frames['touchpointNavigationWidget'].location = context + "/touchpoints/" + $('#touchpointNavigationWidget').attr('src');

    }

}

function checkWidgetControlsInitialValue() {

    if(!$sectionListSelect.val()) {
        resetPersistedValue($sectionListSelect.attr('id'));
        resetPersistedValue($zoneSelect.attr('id'));

        $sectionListSelect.children().first().attr('selected', true);
        $sectionListSelect.change();
    }

}

function toggleSection() {

    $zoneSelect.children().removeAttr('selected');
    $zoneSelect.children().first().attr('selected', true);
    $zoneSelect.trigger('change.mp.complexDropdown');

    updatePersistedValue($zoneSelect);

    // Update widget display
    if (window.frames['touchpointNavigationWidget'] && $.isFunction(window.frames['touchpointNavigationWidget'].requestWidgetDisplay))
        window.frames['touchpointNavigationWidget'].requestWidgetDisplay($sectionListSelect.val());

    toggleAddMessageBtn();
    rebuildListTable(false);
    updateZoneListForSelectedSection();
    _.defer(updateWidgetControlsTooltip);

}

function toggleSort() {
	rebuildListTable(false);
}

function toggleZone() {
    var currentSectionId = $sectionListSelect.val();
    var targetSectionId = $zoneSelect.find('option:selected').data('sectionid').toString();

    if (currentSectionId !== targetSectionId) {

        if (targetSectionId !== "unset") {

            $sectionListSelect.children().removeAttr('selected');
            $sectionListSelect.children('[value="' + targetSectionId + '"]').attr('selected', true);
            $sectionListSelect.trigger('change.mp.complexDropdown');

        }

        if (window.frames['touchpointNavigationWidget'] && $.isFunction(window.frames['touchpointNavigationWidget'].requestWidgetDisplay))
            window.frames['touchpointNavigationWidget'].requestWidgetDisplay($sectionListSelect.val());

        _.defer(updateZoneListForSelectedSection);

    }

    updatePersistedValue($sectionListSelect);

    // Update widget display
    if (window.frames['touchpointNavigationWidget'] && $.isFunction(window.frames['touchpointNavigationWidget'].toggleZone))
        window.frames['touchpointNavigationWidget'].toggleZone($zoneSelect.val());

    toggleAddMessageBtn();
    rebuildListTable(false);
    _.defer(updateWidgetControlsTooltip);
}

function zoneClicked(zoneId) {

    if ($zoneSelect.val() !== zoneId) {

        $zoneSelect.children().removeAttr('selected');
        $zoneSelect.children('[value="' + zoneId + '"]').attr('selected', true);
        $zoneSelect.trigger('change.mp.complexDropdown');
        updatePersistedValue($zoneSelect);

        if ($zoneSelect.children(':selected').data('sectionid').toString() !== $sectionListSelect.val()) {

            $sectionListSelect.removeAttr('selected');
            $sectionListSelect.children('[value="' + $zoneSelect.children(':selected').data('sectionid') + '"]').attr('selected', true);
            $sectionListSelect.trigger('change.mp.complexDropdown');
            updatePersistedValue($sectionListSelect);
            updateZoneListForSelectedSection();

        }

        rebuildListTable(false);
        updateWidgetControlsTooltip();

    }

    toggleAddMessageBtn();

}

function sectionClicked(sectionId) {

    var refreshTable = false;

    if ($sectionListSelect.val() !== sectionId) {

        $sectionListSelect.children().removeAttr('selected');
        $sectionListSelect.children('[value="' + sectionId + '"]').attr('selected', true);
        $sectionListSelect.trigger('change.mp.complexDropdown');
        updatePersistedValue($sectionListSelect);
        refreshTable = true;

    }

    if ($zoneSelect.val() !== $zoneSelect.children().filter('.fixedOption').val()) {

        $zoneSelect.children().removeAttr('selected');
        $zoneSelect.children().first().attr('selected', true);
        $zoneSelect.trigger('change.mp.complexDropdown');
        updatePersistedValue($zoneSelect);
        refreshTable = true;

    }

    if (refreshTable) {

        rebuildListTable(false);
        updateWidgetControlsTooltip();
        updateZoneListForSelectedSection();

    }

    toggleAddMessageBtn();

}

function updateZoneListForSelectedSection() {

    var selectedSectionId = $sectionListSelect.val(),
        $zoneSelectOptions = $zoneSelect.children(),
        $zoneSelectDropdown = $zoneSelect.data('complexDropdown');

    if($zoneSelectDropdown)
        $zoneSelectDropdown.showAllTheOptions();

    if (selectedSectionId !== $zoneSelectOptions.filter('.fixedOption').val()) {

        $zoneSelectOptions.not('.fixedOption').each(function () {

            var $el = $(this);

            if ($el.data('sectionid').toString() !== selectedSectionId) {
                if($zoneSelectDropdown)
                    $zoneSelectDropdown.hideOptionById($el.attr('id'));

            }

        });

    }

}

// *********  WIDGET FUNCTIONS: END  *********

function pollForSegResult(contentObjectId) {

    if ($('#segStatusPollingContainer_' + contentObjectId).length == 0)
        return;

    var stampDate = new Date();
    $.ajax({
        type: "GET",
        url: context + "/getSegResult.form?modelType=1&modelId=" + contentObjectId + "&tk=" + param.tk + "&documentId=" + param.documentId + "&cacheStamp=" + stampDate.getTime(),
        dataType: "json",
        success: function (data) {
            processSegPollResult(data);
        }
    });

    window.setTimeout(function () {
        pollForSegResult(contentObjectId);
    }, 2000);
}

function processSegPollResult(data) {
    var segStatusContainer = $('#segStatusPollingContainer_' + data.item_id).closest('td');
    if (data.status != "in_process") {
        $('#segStatusPollingContainer_' + data.item_id).remove();
        $('#segResultContainer_' + data.item_id).text(data.reach);
    }
}

function showOrHideWorkflowSelect(popupId, actionSpecId){
    var isInSubworkflowStep = false;
    $("input[id^='listItemCheck_']:checked").each(function(){
        var objectId =$(this).attr('id').split('_')[1];
        if (exists('isInSubworkflowStep_' + objectId)){
            isInSubworkflowStep = true;
        }
    });
    if (isInSubworkflowStep){
        $('#actionPopupWorkflowSelect').show();
        if (exists('approveBtnDisabled')) {	$('#approveBtnDisabled').show(); $('#approveBtn').hide(); }
        if (exists('releaseBtnDisabled')) {	$('#releaseBtnDisabled').show(); $('#releaseBtn').hide(); }
        if(actionSpecId == 18 || actionSpecId == 21){ // Reject or Reject and Override
            $('#actionPopupUserSelect').hide();
            $('#rejectBtnDisabled').show(); $('#rejectBtn').hide();
        }
    }else{
        $('#actionPopupWorkflowSelect').hide();
        if(actionSpecId == 17 || actionSpecId == 20) { // Approve or Approve and Override
            $('#approveBtn').show(); $('#approveBtnDisabled').hide();
        }
        if(actionSpecId == 18 || actionSpecId == 21) { // Reject or Reject and Override
            $('#actionPopupUserSelect').show();
            $('#rejectBtnDisabled').show(); $('#rejectBtn').hide();
        }
    }
}

function selectWorkflow(){
    var actionId = $('#popupActionId').val();
    if(actionId == 41){ // Reassign for translation
        // Reload the user select based on the workflow selection
        requestDataForSelectMenu($('#actionUserSelect_'+actionId).attr('type'));
    }
    validatePopupReq();
}

function selectTask(){
    $('#actionPopupNote').find('textarea').val('');
    if ($('#taskSelect').val() === '0'){
        $('#actionPopupNote').find('textarea').removeAttr("disabled");
        $('#actionPopupNote').attr('state','clear');
        $('#newTaskTypeDiv').show();
        $('#taskVariantDiv').show();
        selectTaskType();
    }else{
        $('#actionPopupNote').find('textarea').attr("disabled", true);
        $('#newTaskTypeDiv').hide();
        $('#newTaskLanguageDiv').hide();
        $('#taskVariantDiv').hide();
        let taskNameWithDescription = $('#taskSelect').find(':selected').text();
        let match = taskNameWithDescription.match(/\(([^)]+)\)/);
        if(match){
            $('#actionPopupNote').find('textarea').val(match[1]);
        }
    }
    validatePopupReq();
}

function selectTaskType(){
    if ($('#taskTypeSelect').val() === '2'){
        $('#newTaskLanguageDiv').show();
    }
    else{
        $('#newTaskLanguageDiv').hide();
    }
}

function iFrameView(path, eleId, e) {
    if (e != undefined) {
        if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
            $("#" + eleId).iFramePopup($.extend({
                src: path,
                displayOnInit: true,
                setFullModalPopup: true,
                matchContentHeight: true,
                id: "messageFrame",
                appliedParams: {tk: param.tk},
                beforePopupClose: function () {
                    $('#iFramePopup_workflowHistoryFrame').remove();
                    $('#iFramePopup_messagePreviewFrame').remove();
                    $('#iFramePopup_messageMoveFrame').remove();
                    $('#iFramePopup_contentHistoryFrame').remove();
                    updateWidgetDisplay(true);
                    rebuildListTable(true);
                    $('#backgroundTasksPlaceholder').refresh();
                }
            }, iFramePopup_fullFrameAttr));
        }
        $('.contentPopup_popupContainer').remove();
        e.preventDefault ? e.stopPropagation() : e.returnValue = false;
    }
}

// Inject icon in list headers
function headerLabelOverride(nHead, aData, iStart, iEnd, aiDisplay) {
    $(nHead).find('th').each(function () {
        var iconColumn = false;
        if ($(this).text().indexOf(client_messages.text.targeted) != -1) {
            $(this).append("<i class=\"far fa-bullseye table-icon\" aria-hidden=\"true\"></i>");
            iconColumn = true;
        } else if ($(this).text().indexOf(client_messages.text.timed) != -1) {
            $(this).append("<i class=\"far fa-clock table-icon\" aria-hidden=\"true\"></i>");
            iconColumn = true;
        } else if ($(this).text().indexOf(client_messages.text.variable) != -1) {
            $(this).append("<i class=\"far fa-clone table-icon\" aria-hidden=\"true\"></i>");
            iconColumn = true;
        } else if ($(this).text().indexOf(client_messages.text.inherited) != -1) {
            $(this).append("<i class=\"far fa-sitemap table-icon\" aria-hidden=\"true\"></i>");
            iconColumn = true;
        }

        if (iconColumn)
            $(this)
                .contents()
                .filter(function () {
                    return this.nodeType == 3; //Node.TEXT_NODE
                }).remove();
    });
}

function listItemClickHandler(ele) {
    var contentObjectId = $(ele).closest('tr').find("[id^='listItemCheck']").val();
    if (window.frames['touchpointNavigationWidget'] && $.isFunction(window.frames['touchpointNavigationWidget'].updateContent))
        window.frames['touchpointNavigationWidget'].updateContent(contentObjectId);
}

function toggleFilter(select, maintainCurrentList) {
    $('#infoMsg_invalidFilterSetting').addClass('d-none');
    if ($('#messagesListAssignmentFilter option:selected').val() == '2' &&
        ($('#contentObjectListStatusFilter option:selected').val() == '2' || $('#contentObjectListStatusFilter option:selected').val() == '3'))
        $('#infoMsg_invalidFilterSetting').removeClass('d-none');

    if (!maintainCurrentList)
        rebuildListTable(false);
}

function rebuildListTable(maintainCurrentPage) {
    $('#messageList').DataTable().ajax.reload(null, !maintainCurrentPage);
}

function getAsyncExtListTableConfig() {
    var obj = {};
    obj.ajaxSource = "getListTable.form";
    
    var cardDisplay =  $listTypeToggle.is('.active');

    //Document column is added just for the sort and does not currently carry any data
    obj.columns = [
        {
            columnMap: 'name',
            columnName: cardDisplay ? client_messages.text.content : client_messages.text.name,
            sort: !cardDisplay,
            width: '100%',
            colVisToggle: false,
            applied: true
        },
        {
            columnMap: 'versioning',
            columnName: client_messages.text.state,
            sort: false,
            width: '100%',
            colVisToggle: false,
            applied: !isStatusViewActive
        },
        {
            columnMap: 'assignedTo',
            columnName: client_messages.text.assigned,
            sort: true,
            width: '100%',
            colVisToggle: true,
            applied: !isStatusViewActive
        },
        {
            columnMap: 'status',
            columnName: client_messages.text.content_status,
            sort: true,
            width: '100%',
            colVisToggle: true,
            applied: !isStatusViewActive
        },
        {
            columnMap: 'zoneName',
            columnName: client_messages.text.zone,
            sort: true,
            width: '100%',
            colVisToggle: true,
            applied: true
        },
        {
            columnMap: 'document',
            columnName: client_messages.text.document,
            sort: true,
            width: '100%',
            colVisToggle: false,
            applied: cardDisplay
        },
        {
            columnMap: 'type',
            columnName: client_messages.text.type,
            sort: true,
            width: '100%',
            colVisToggle: true,
            applied: true
        },
        {
            columnMap: 'sharedFrom',
            columnName: client_messages.text.inherited,
            sort: true,
            width: '100%',
            colVisToggle: true,
            applied: isVariantTouchpoint && !isMasterSelection
        },
        {
            columnMap: 'targeting',
            columnName: client_messages.text.targeted,
            sort: true,
            width: '100%',
            colVisToggle: true,
            applied: true
        }
    ];

    var cardSortCols = ['name','assignedTo','zoneName','document'];
    if ( cardDisplay ) {
    	$('#messageListSortBySelect').html('');
    	for (var i = 0; i < obj.columns.length; i++ ) {
    		if ( obj.columns[i].applied && cardSortCols.indexOf(obj.columns[i].columnMap) != -1 ) {
    			var label = obj.columns[i].columnMap == 'name' ? client_messages.text.name : obj.columns[i].columnName;
                var labelAsc = obj.columns[i].columnMap == 'document' ? client_messages.text.top_to_bottom : client_messages.text.abbr_ascending;
                var labelDesc = obj.columns[i].columnMap == 'document' ? client_messages.text.bottom_to_top : client_messages.text.abbr_descending;

    			$('#messageListSortBySelect').append("<option id=\"messageListSortBySelect_asc_" + i + "\" value=\"" + obj.columns[i].columnMap + "_asc\">" + label + " ("+labelAsc+")</option>");
    			$('#messageListSortBySelect').append("<option id=\"messageListSortBySelect_desc_" + i + "\" value=\"" + obj.columns[i].columnMap + "_desc\">" + label + " ("+labelDesc+")</option>");
    		}
    		if ( i != 0 )
    			obj.columns[i].applied = false;
    	}
    	var persistedValue = getPersistedValue($('#messageListSortBySelect'));
    	if ( persistedValue )
    		$('#messageListSortBySelect').val(persistedValue);
    	$('#messageListSortBySelect').data('complexDropdown').refreshAllTheOptions();
    }

    if (displaySegmentationAnalysis && !cardDisplay) {
        obj.columns.push({
            columnMap: 'segmentation',
            columnName: client_messages.text.segmentation,
            sort: true, width: '1%',
            colVisToggle: true
        });
    }

    return obj;
}

function getAsyncExtParams() {

    function getSelectionStatusId() {
        if ($('#selectionStatusActive').is(":checked")) {
            return 2;
        } else if ($('#selectionStatusWorking').is(':checked')) {
            return 1
        } else {
            return -1;
        }
    }

    var stampDate = new Date();
    var obj = [
        {"name": "listTableType", "value": "101"},
        {"name": "documentId", "value": getParam('documentId') != "" ? getParam('documentId') : -1},
        {
            "name": "touchpointSelectionId",
            "value": getParam('touchpointSelectionId') != "" ? getParam('touchpointSelectionId') : -1
        },
        {
            "name": "docSectionId",
            "value": ($('#sectionListSelect').val() != "" && $('#sectionListSelect').val() != undefined) ? $('#sectionListSelect').val() : -1
        },
        {
            "name": "docZoneId",
            "value": ($('#zoneSelect').val() != "" && $('#zoneSelect').val() != undefined) ? $('#zoneSelect').val() : -1
        },
        {
            "name": "messagesAssignmentFilterId",
            "value": ($('#messagesListAssignmentFilter').val() != "" && $('#messagesListAssignmentFilter').val() != undefined) ? $('#messagesListAssignmentFilter').val() : -1
        },
        {"name": "messagesStatusFilterId", "value": $('#contentObjectListStatusFilter').val()},
        {
            "name": "contentTypeFilterId",
            "value": $('#contentListTypeFilter').val() != undefined ? $('#contentListTypeFilter').val() : 1
        },
        {"name": "currentSelectionStatusFilterId", "value": getSelectionStatusId()},
        {
            "name": "listDisplayFormat",
            "value": $listTypeToggle.is('.active') ? "card" : "table"
        },
        {"name": "displayMode", "value": $('#widgetToggleBtn').is(':not(.active)') ? "limited" : "full"},
        {"name": "cacheStamp", "value": stampDate.getTime()}
    ];
    
    if ( $listTypeToggle.is('.active') ) {
    	obj[obj.length] = { "name" : "cardSort", "value" : $('#messageListSortBySelect').val() }
    }

    return obj;
}

function requestContentForCard(contentContainer) {
    var requestParam = 	"contentType=contentObject" +
        "&viewType=list" +
        "&contentItemId=" + $(contentContainer).attr('contentItemId') +
        "&statusViewId=" + $(contentContainer).attr('statusViewId') +
        "&statusFilterId=" + ($('#contentObjectListStatusFilter').val() != undefined && $('#contentObjectListStatusFilter').val() != null ? $('#contentObjectListStatusFilter').val() : 1) +
        "&touchpointSelectionId=" + (getParam('touchpointSelectionId') != "" ? getParam('touchpointSelectionId') : -1) +
        "&zoneId=" + ($('#zoneSelect').val() != undefined && $('#zoneSelect').val() != null ? $('#zoneSelect').val() : -1) +
        "&documentId=" + (getParam('documentId') != "" ? getParam('documentId') : -1) +
        "&selectionStatusId=" + getSelectionStatusId() +
        "&localeId=0" +
        "&getStyles=false";

    // Async Request
    var stampDate = new Date();
    $.ajax({
        type: "GET",
        url: context + "/getContentPreview.form?" + requestParam + "&cacheStamp=" + (stampDate.getTime()),
        dataType: "json",
        success: function(data) {
            // mp.cardViewList.js
            processCardContent(data, contentContainer, $(contentContainer).attr('contentItemId'));
        }
    });
}

function postListDrawCallback(nTable) {

    function getSelectionStatusId() {
        if ($('#selectionStatusActive').is(":checked")) {
            return 2;
        } else if ($('#selectionStatusWorking').is(':checked')) {
            return 1
        } else {
            return -1;
        }
    }

    $(nTable).find('.dataTableErrorLink,.dataTableLink,.dataTableSecondaryLink').each(function () {
        // Content:  Init content popup
        var instId = parseId($(this));

        if ( $listTypeToggle.is('.active') ) {

            toggleCardDisplayVis();

        	var contentContainer = $(this).closest('tr').find('.contentContainer');
            requestContentForCard(contentContainer)
        	
        } else {
        	
            $(this).closest('td').contentPopup({
                trigger: "dual",
                contentType: 'contentObject',
                contentItemId: instId,
                popupLocation: 'right',
                fnBeforeContentRequest: function (o) {
                    o.data.contentLocaleId = 0;
                },
                fnExtServerParams: function (o) {
                    
                    function getSelectionStatusId() {
                        if ($('#selectionStatusActive').is(":checked")) {
                            return 2;
                        } else if ($('#selectionStatusWorking').is(':checked')) {
                            return 1
                        } else {
                            return -1;
                        }
                    }

                    var obj = [
                        {
                            "name": "statusFilterId",
                            "value": $('#contentObjectListStatusFilter').val() != undefined && $('#contentObjectListStatusFilter').val() != null ? $('#contentObjectListStatusFilter').val() : 1
                        },
                        {
                            "name": "zoneId",
                            "value": $('#zoneSelect').val() != undefined && $('#zoneSelect').val() != null ? $('#zoneSelect').val() : -1
                        },
                        {
                            "name": "documentId",
                            "value": getParam('documentId') != "" ? getParam('documentId') : -1
                        },
                        {
                            "name": "touchpointSelectionId",
                            "value": getParam('touchpointSelectionId') != "" ? getParam('touchpointSelectionId') : -1
                        },
                        {"name": "selectionStatusId", "value": getSelectionStatusId()}
                    ];
                    return obj;
                },
                maxWidth: 600,
                maxHeight: 300
            });
        	
        }
        
    });

    $(nTable).find('.popupTargetingSummary').each(function () {
        // Targeting:  Init targeting popup
        var instId = parseId($(this));
        var stampDate = new Date();
        var statusFilterId = $('#contentObjectListStatusFilter').val();
        $(this).closest('td').popupFactory({
            title: client_messages.title.targeting,
            popupLocation: "left",
            width: 250,
            asyncDataType: "xml",
            asyncSetContentURL: context + "/getTargeting.form?type=summary&contentObjectId=" + instId + "&cacheStamp=" + (stampDate.getTime()) + "&statusFilterId=" + statusFilterId,
            asyncSetContentHandler: function (o, data) {
                if ($(data).find("content").length > 0)
                    return "<div align=\"left\" style=\"padding: 6px 12px; font-size: 10px;\">" +
                        $(data).find("content").text() +
                        "</div>";
                else
                    return "<div>Error: " + client_messages.text.bad_request_for_targeting_summary + "</div>";
            }
        });
    });

    // Segmentation analysis result polling
    $(nTable).find("[id^='segStatusPollingContainer_']").each(function () {
        if ($(this).attr('pollingInit') == undefined || $(this).attr('pollingInit') != "true") {
            $(this).attr('pollingInit', 'true');
            pollForSegResult(parseId(this));
        }
    });

    $(nTable).find('.detailTip').each(function () {
        initTip(this, {zIndex: 10000});
    });
    $(nTable).find('[data-toggle=tooltip]').tooltip();
}

function postListRenderFlagInjection(oObj) {
    var iFrameId;
    var iFrameSrc;

    if (oObj.aData.flags.type == "message") {
        iFrameSrc = context + "/touchpoints/touchpoint_content_object_list_detail.form?contentObjectId=" + oObj.aData.dt_RowId;
        iFrameId = "message-instant-id";
    } else {
        iFrameSrc = context + "/touchpoints/touchpoint_selections_content_list_detail.form?contentObjectId=" + oObj.aData.dt_RowId + "&touchpointSelectionId=" + getParam('touchpointSelectionId');
        iFrameId = "selection-content-id";
    }

    if ($('#listSearchInput').val() != "")
        iFrameSrc += "&sSearch=" + $('#listSearchInput').val();

    var binding = oObj.aData.binding;

    var text = oObj.aData.name;
    text += "<input id='iFrameId' value=" + iFrameId + " type='hidden' class='iframe-data' />";
    text += "<input id='iFrameSrc' value=" + iFrameSrc + " type='hidden' class='iframe-data' />";

    //Message Instance check select
    text += binding;

    //Message Instance permission flags
    if (oObj.aData.flags.canUpdate)
        text += "<input type='hidden' id='canUpdate_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canReassign)
        text += "<input type='hidden' id='canReassign_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canTranslationReassign)
        text += "<input type='hidden' id='canTranslationReassign_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canReleaseFromTranslation)
        text += "<input type='hidden' id='canReleaseFromTranslation_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canClone)
        text += "<input type='hidden' id='canClone_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canClone)
        text += "<input type='hidden' id='canContentExport_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canReleaseForApproval)
        text += "<input type='hidden' id='canReleaseForApproval_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canApprove)
        text += "<input type='hidden' id='canApprove_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canReject)
        text += "<input type='hidden' id='canReject_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canDiscard)
        text += "<input type='hidden' id='canDiscard_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canArchive)
        text += "<input type='hidden' id='canArchive_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.archived)
        text += "<input type='hidden' id='archived_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canCreateWorkingCopy)
        text += "<input type='hidden' id='canCreateWorkingCopy_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canPreview)
        text += "<input type='hidden' id='canPreview_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.inMyFavorites)
        text += "<input type='hidden' id='inMyFavorites_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.hasNoStepForWorkflow)
        text += "<input type='hidden' id='hasNoStepForWorkflow_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.workflowOwner)
        text += "<input type='hidden' id='workflowOwner_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canHold)
        text += "<input type='hidden' id='canHold_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canUnhold)
        text += "<input type='hidden' id='canUnhold_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canSuppress)
        text += "<input type='hidden' id='canSuppress_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canRestore)
        text += "<input type='hidden' id='canRestore_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canMove)
        text += "<input type='hidden' id='canMove_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.contentSuppressed)
        text += "<input type='hidden' id='isContentSuppressed_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.accessRestricted)
        text += "<input type='hidden' id='isAccessRestricted_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.inTranslationStep)
        text += "<input type='hidden' id='isInTranslationStep_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.inApprovalStep)
        text += "<input type='hidden' id='isInApprovalStep_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.inSubworkflowStep)
        text += "<input type='hidden' id='isInSubworkflowStep_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.currentTranslator)
        text += "<input type='hidden' id='isCurrentTranslator_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canAddTask)
        text += "<input type='hidden' id='canAddTask_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canAbort)
        text += "<input type='hidden' id='canAbort_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.canRetry)
        text += "<input type='hidden' id='canRetry_" + oObj.aData.dt_RowId + "' />";
    if (oObj.aData.flags.hasTaskToLink)
        text += "<input type='hidden' id='hasTaskToLink_" + oObj.aData.dt_RowId + "' />";
    return text;
}

function toggleSelection(selId) {
    var targetURL = "";
    var URLparameters = "?documentId=" + getParam('documentId') +
        "&touchpointSelectionId=" + selId +
        "&tk=" + getParam('tk');
    var parentURL = getTopFrame().location.href;

    // If viewing selection list - stay in context and change selection
    // Else if viewing message list - stay on message list if master or TPMS touchpoint;
    // 								  otherwise, toggle to content list (TPCS only)
    if (parentURL.indexOf('touchpoint_variant_list') != -1)
        targetURL = "touchpoint_variant_list.form" + URLparameters;
    else {
        targetURL = "touchpoint_content_object_list.form" + URLparameters;
        if (selId == -1) {
            if (!exists('viewMessagePermission'))
                return;
        }
    }

    if (window.opener != undefined && window.opener != null)
        window.opener.location.href = context + "/touchpoints/" + targetURL;
    else
        getTopFrame().location.href = context + "/touchpoints/" + targetURL;
}

function adjustIFrameHeight(ele) {
    $('.iFrameLoadingContainer').fadeOut('fast', function () {
        var $ele = $(ele);
        $ele.show();
        $ele.height(Math.min($ele.contents().find('body').height() + 10, 70));
    });
}

function actionMenuSelected(el) {

    var elSelectedId = $(el).children(':selected').attr('id');

    if (elSelectedId.indexOf('actionOption_') !== -1)
        actionSelected(el);

    else
        iFrameAction(elSelectedId.replace('actioniFrame_', ''));

}

function continueToLinkTask(saveCurrentSelection) {
    if(saveCurrentSelection){
        // Save the current selection
        var currentTaskLinkedObjectId = $('#currentTaskLinkedObjectId').val();
        var currentLinkedTaskBinding = '<input id=\'taskLinkedMap_'+currentTaskLinkedObjectId+'\' type=\'hidden\' name=\'taskLinkedMap['+currentTaskLinkedObjectId+']\' value=\''+$('#taskSelect').val()+'\'>';
        var currentTaskDescriptionBinding = '<input id=\'taskDescriptionMap_'+currentTaskLinkedObjectId+'\' type=\'hidden\' name=\'taskDescriptionMap['+currentTaskLinkedObjectId+']\' value=\''+$('#actionPopupNote').find('textarea').val()+'\'>';
        var currentTaskTypeBinding = '<input id=\'taskTypeMap_'+currentTaskLinkedObjectId+'\' type=\'hidden\' name=\'taskTypeMap['+currentTaskLinkedObjectId+']\' value=\''+$('#taskTypeSelect').val()+'\'>';

        if($('#taskTypeSelect').val() === '2'){
            var currentTaskLocaleBinding = '<input id=\'taskLocaleMap_'+currentTaskLinkedObjectId+'\' type=\'hidden\' name=\'taskLocaleMap['+currentTaskLinkedObjectId+']\' value=\''+$('#newTasklanguageSelect').val()+'\'>';
            $('#newTaskLanguageDiv').append(currentTaskLocaleBinding);
        }

        if ($('#taskVariantSelect').val() && $('#taskVariantSelect').val() !== "0") {
            var tokens = $('#taskVariantSelect').val().split('_');
            if (tokens.length > 1) {
                var currentTaskVariantBinding = '<input id=\'taskVariantMap_'+currentTaskLinkedObjectId+'\' type=\'hidden\' name=\'taskVariantMap['+currentTaskLinkedObjectId+']\' value=\''+tokens[1]+'\'>';
                $('#taskVariantDiv').append(currentTaskVariantBinding);
            }
        }

        $('#taskSelectDiv').append(currentLinkedTaskBinding);
        $('#taskDescriptionDiv').append(currentTaskDescriptionBinding);
        $('#newTaskTypeDiv').append(currentTaskTypeBinding);
    }else{
        // Clear the selections
        $("input[id^='taskLinkedMap_']").remove();
        $("input[id^='taskDescriptionMap_']").remove();
        $("input[id^='taskTypeMap_']").remove();
        $("input[id^='taskLocaleMap_']").remove();
        $("input[id^='taskVariantMap_']").remove();
    }

    var selectedIds = getSelectedIds().split('_');
    var nextUnlinkedObjectId = 0;
    for(var i = 0; i < selectedIds.length; i++) {
        // Check if the selected object has a task to link
        if(exists('hasTaskToLink_'+selectedIds[i])) {
            var linkedToTaskId = $('#taskLinkedMap_' + selectedIds[i]).val();
            if (!exists('taskLinkedMap_'+selectedIds[i])) {  // If the task is not linked before
                nextUnlinkedObjectId = selectedIds[i];
                break;
            }
        }
    }

    if(nextUnlinkedObjectId > 0) {
        // Set the popup to the next unlinked task
        $('#currentTaskLinkedObjectId').val(nextUnlinkedObjectId);
        $('#linkTaskToObjectName').text($('#actionLabel_'+nextUnlinkedObjectId).attr('itemname'));
        $.ajax({
            type: "GET",
            url: context+"/getDataForSelectMenu.form?type=linkedToTasks&contentObjectId="+nextUnlinkedObjectId+"&cacheStamp="+(stampDate.getTime()),
            dataType: "xml",
            success: function(data) {
                var taskOptions = $(data).find("taskOptions");
                if (taskOptions.length > 0) {
                    var taskOptions = $(data).find('taskOptions').text();
                    $('#taskSelect').html(taskOptions);
                    $('#taskSelect').addClass('style_select');
                    $('#taskSelect_menuTable').remove();
                    $("#taskSelect").styleActionElement();
                    $("#taskTypeSelect").styleActionElement();
                    $("#newTasklanguageSelect").styleActionElement();
                }
            }
        });

        $.ajax({
            type: "GET",
            url: context+"/getDataForSelectMenu.form?type=variantsForContentObject&contentObjectId="+nextUnlinkedObjectId+"&documentId="+(param.documentId)+"&cacheStamp="+(stampDate.getTime()),
            dataType: "xml",
            success: function(data) {
                if ($(data).find("varOptions").length > 0) {
                    var varOptions = $(data).find('varOptions').text();
                    $('#taskVariantSelect').html(varOptions);
                    $('#taskVariantSelect').addClass('style_select');
                    $('#taskVariantSelect_menuTable').remove();
                    $("#taskVariantSelect").styleActionElement();
                }
            }
        });

        actionSelected(46);
    }else{
        submitAction('4');
    }
}

// *********  LIST TABLE FUNCTIONS: END  *********

function iFrameAction(actionId) {

    var contentObjectId = "",
        selectionContentId = null,
        statusViewId = gup('statusViewId') != '' ? gup('statusViewId') : '1',
        tk = param.tk,
        applyMetadata = hasMetadataFormDef,
        isInTranslationStep = false,
        isInApprovalStep = false;

    $("input[id^='listItemCheck_']:checked").each(function () {
        contentObjectId = this.id.replace('listItemCheck_', '');
        selectionContentId = $(this).attr('selectionContentId');
        isInTranslationStep = exists('isInTranslationStep_'+contentObjectId);
        isInApprovalStep = exists('isInApprovalStep_'+contentObjectId);
    });

    if (actionId == '1') {

        if (selectionContentId != null) {

            javascriptHref(context + '/tpadmin/touchpoint_content_selection_edit.form?contentObjectId=' +  contentObjectId +
                '&statusViewId=' + statusViewId + '&contentSelectionId=' + selectionContentId + '&touchpointSelectionId=' + getParam('touchpointSelectionId'));

        } else {

            javascriptHref(context + '/content/content_object_edit_content.form?contentObjectId=' +  contentObjectId +
                '&statusViewId=' + statusViewId +
                (isInTranslationStep ? '&translationCompare=' + isInTranslationStep : '') +
                (exists('isVariantTouchpoint') ? '&touchpointSelectionId=' + getParam('touchpointSelectionId') : '') );

        }

    } else if (actionId == '2') {

        javascriptHref(context + '/content/content_object_edit.form?documentId=' + param.documentId + '&statusViewId=' + statusViewId +
            (applyMetadata ? '&metadataDefinitionId=' + $('#metadataSelect').val() : '') + '&docZoneId=' + $zoneSelect.val() +
            (exists('isVariantTouchpoint') ? '&touchpointSelectionId=' + getParam('touchpointSelectionId') : '') );

    }

    else if (actionId == '22') {
        $('#actioniFrame_' + actionId).iFramePopup({
            width: 620,
            displayOnInit: true,
            id: "previewFrame",
            title: client_messages.title.preview,
            src: "touchpoint_content_object_preview.form",
            appliedParams: {documentId: param.documentId, contentObjectId: contentObjectId, tk: tk},
            closeBtnId: "cancelBtn_button",
            onSave: function () {
                return true;
            }
        });
    }

    else if (actionId == '28') {
        $('#actioniFrame_' + actionId).iFramePopup({
            width: 620,
            displayOnInit: true,
            id: "moveFrame",
            title: client_messages.title.move,
            src: "touchpoint_content_object_move_to_zone.form",
            appliedParams: {documentId: param.documentId, contentObjectId: contentObjectId, tk: tk},
            closeBtnId: "cancelBtn_button",
            onSave: function () {
                return true;
            }
        });
    }
    else if (actionId == '36') {

        $('.contextMenu a#actioniFrame_36').addClass('disabled');
        $actionMenu.data('complexDropdown').disableOptionById('actioniFrame_36');
        $.ajax({
            type: "GET",
            url: context + "/messageSyncOperations.form?action=runRehashTask&objectType=1&objectId=" + contentObjectId,
            dataType: "json",
            success: function (data) {
                getTopFrame().location.reload();
            },
            error: function (data) {
                getTopFrame().location.reload();
            }
        });
    }

    else if (actionId == '38') {
        // assetIds=sas-123_234+ili-245_231
        var assetIds = 'msg-'+getSelectedIds();
        openAddTaskModal(assetIds, $('#taskMetadataSelect'), true);
    }
}

// Retrieve selected ids seperated by ','
function getSelectedIds() {
    var selectedIds = '';
    var selectedCount = $("input[id^='listItemCheck']:checked").length;
    var count = 0;
    $("input[id^='listItemCheck']:checked").each(
        function () {
            selectedIds += this.id.replace('listItemCheck_', '');
            count++;
            if (count != selectedCount)
                selectedIds += '_';
        }
    );
    return selectedIds;
}

function validateActionReq(messageInstanceId) {
    var singleSelect = true;

    var canUpdate = true;
    var canReassign = true;
    var canTranslationReassign = true;
    var canReleaseFromTranslation = true;
    var canReleaseForApproval = true;
    var canApprove = true;
    var canReject = true;
    var canClone = true;
    var canContentExport = showContentJSONExport;
    var canDiscard = true;
    var archived = true;
    var canArchive = true;
    var canCreateWorkingCopy = true;
    var canPreview = true;
    var allHasWorkflowStep = true;
    var allHasNoWorkflowStep = true;
    var allAreWorkflowOwner = true;
    var allAreNotWorkflowOwner = true;
    var canHold = true;
    var canUnhold = true;
    var canSuppress = true;
    var canRestore = true;
    var isContentSuppressed = true;
    var isAccessRestricted = false;
    var allIsInTranslationStep = true;
    var allNotIsInTranslationStep = true;
    var allIsInApprovalStep = true;
    var isCurrentTranslator = true;
    var canMove = true;
    var canAddTask = true;
    var canAbort = true;
    var canRetry = true;

    // Resolve selection flags
    if ($("input[id^='listItemCheck_']:checked").length != 1)
        singleSelect = false;
    $("input[id^='listItemCheck_']:checked").each(function () {
        var contentObjectId = this.id.replace('listItemCheck_', '');
        if (!exists('canUpdate_' + contentObjectId))
            canUpdate = false;
        if (!exists('canReassign_' + contentObjectId))
            canReassign = false;
        if (!exists('canTranslationReassign_' + contentObjectId))
            canTranslationReassign = false;
        if (!exists('canReleaseFromTranslation_' + contentObjectId))
            canReleaseFromTranslation = false;
        if (!exists('canClone_' + contentObjectId))
            canClone = false;
        if (!exists('canReleaseForApproval_' + contentObjectId))
            canReleaseForApproval = false;
        if (!exists('canApprove_' + contentObjectId))
            canApprove = false;
        if (!exists('canReject_' + contentObjectId))
            canReject = false;
        if (!exists('canAbort_' + contentObjectId))
            canAbort = false;
        if (!exists('canRetry_' + contentObjectId))
            canRetry = false;
        if (!exists('canDiscard_' + contentObjectId))
            canDiscard = false;
        if (!exists('canArchive_' + contentObjectId))
            canArchive = false;
        if (!exists('canCreateWorkingCopy_' + contentObjectId))
            canCreateWorkingCopy = false;
        if (!exists('archived_' + contentObjectId))
            archived = false;
        if (!exists('canPreview_' + contentObjectId))
            canPreview = false;
        if (!exists('canHold_' + contentObjectId))
            canHold = false;
        if (!exists('canUnhold_' + contentObjectId))
            canUnhold = false;
        if (!exists('canSuppress_' + contentObjectId))
            canSuppress = false;
        if (!exists('canRestore_' + contentObjectId))
            canRestore = false;
        if (!exists('isContentSuppressed_' + contentObjectId))
            isContentSuppressed = false;
        if (!exists('canMove_' + contentObjectId))
            canMove = false;
        if (!exists('canAddTask_' + contentObjectId))
            canAddTask = false;
        if (exists('hasNoStepForWorkflow_' + contentObjectId)) {
            allHasWorkflowStep = false;
        } else {
            allHasNoWorkflowStep = false;
        }
        if (exists('workflowOwner_' + contentObjectId)) {
            allAreNotWorkflowOwner = false;
        } else {
            allAreWorkflowOwner = false;
        }
        if (exists('isAccessRestricted_' + contentObjectId))
            isAccessRestricted = true;

        if (exists('isInTranslationStep_' + contentObjectId)){
            allNotIsInTranslationStep = false;
        }else{
            allIsInTranslationStep = false;
        }

        if (!exists('isInApprovalStep_' + contentObjectId)){
            allIsInApprovalStep = false;
        }

        if (!exists('isCurrentTranslator_' + contentObjectId))
            isCurrentTranslator = false;
    });

    //Disable all context menu entries
    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');
    $actionMenu.data('complexDropdown').disableAllTheOptions();
    common.disableElement($updateBtn);
    common.disableElement($cloneBtn);

    // Request report: Disable
    //$exportMenu.data('complexDropdown').disableOptionById('actionOption_11');
    // Restricted:  One or more messages owned by restricted variant: Prevent all actions
    if (isAccessRestricted) {
        $(".contextMenu a[id^='actionOption_'], .contextMenu a[id^='actioniFrame_']").hide();
        return;
    }

    //Enable context menu entries based on flag status
    if (singleSelect) {
        if (canUpdate) {
            $('.contextMenu a#actioniFrame_1').removeClass('disabled');
            common.enableElement($updateBtn);
        }
        if (canClone) {
            $('.contextMenu a#actionOption_23').removeClass('disabled');
            common.enableElement($cloneBtn);
        }
        if (canPreview) {
            $('.contextMenu a#actioniFrame_22').removeClass('disabled');
            $actionMenu.data('complexDropdown').enableOptionById('actioniFrame_22');
        }
        if (canMove) {
            $('.contextMenu a#actioniFrame_28').removeClass('disabled');
            $actionMenu.data('complexDropdown').enableOptionById('actioniFrame_28');
        }

        $('.contextMenu a#actioniFrame_36').removeClass('disabled');
        $actionMenu.data('complexDropdown').enableOptionById('actioniFrame_36');
    }

    if ($("input[id^='listItemCheck_']:checked").length > 0) {
        $('ul.contextMenu').find("a[id^='actionOption']").show();
        $('ul.contextMenu').find("a[id^='actioniFrame']").show();
        $actionMenu.data('complexDropdown').showAllTheOptions();
        // Request report
        $exportMenu.data('complexDropdown').enableOptionById('actionOption_11');
        $('#includeAllMessagesCheckbox').removeAttr('disabled');
        if (allHasWorkflowStep) {
            if (allAreWorkflowOwner) {
                if(allNotIsInTranslationStep || allIsInApprovalStep){
                    $('.contextMenu a#actionOption_20').show();	// Approve and override
                    $actionMenu.data('complexDropdown').showOptionById('actionOption_20'); // Approve and override
                }
                if(allIsInTranslationStep){
                    $('.contextMenu a#actionOption_30').show();	// Release from translation (Approve)
                    $actionMenu.data('complexDropdown').showOptionById('actionOption_30'); // Release from translation (Approve)
                }

                $('.contextMenu a#actionOption_21').show();	// Reject and override
                $('.contextMenu a#actionOption_17').hide();	// Approve
                $('.contextMenu a#actionOption_18').hide();	// Reject

                $actionMenu.data('complexDropdown').showOptionById('actionOption_21'); // Reject and override
                $actionMenu.data('complexDropdown').hideOptionById('actionOption_17'); // Approve
                $actionMenu.data('complexDropdown').hideOptionById('actionOption_18'); // Reject
            } else {
                if(allNotIsInTranslationStep || allIsInApprovalStep){
                    $('.contextMenu a#actionOption_17').show(); // Approve
                    $actionMenu.data('complexDropdown').showOptionById('actionOption_17'); // Approve
                }
                if(allIsInTranslationStep && isCurrentTranslator){
                    $('.contextMenu a#actionOption_30').show(); // Release from translation (Approve)
                    $actionMenu.data('complexDropdown').showOptionById('actionOption_30'); // Release from translation (Approve)
                }
                $('.contextMenu a#actionOption_18').show(); // Reject
                $('.contextMenu a#actionOption_20').hide();	// Approve and override
                $('.contextMenu a#actionOption_21').hide();	// Reject and override

                $actionMenu.data('complexDropdown').showOptionById('actionOption_18'); // Reject
                $actionMenu.data('complexDropdown').hideOptionById('actionOption_20'); // Approve and override
                $actionMenu.data('complexDropdown').hideOptionById('actionOption_21'); // Reject and override
            }

            $('.contextMenu a#actionOption_6').show();  // Release for approval

            $('.contextMenu a#actionOption_19').hide();	// Activate
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_19'); // Activate
        } else {
            $('.contextMenu a#actionOption_19').show(); // Activate
            $('.contextMenu a#actionOption_20').hide();	// Approve and override
            $('.contextMenu a#actionOption_21').hide();	// Reject and override
            $('.contextMenu a#actionOption_6').hide();	// Release for approval
            $('.contextMenu a#actionOption_17').hide();	// Approve
            $('.contextMenu a#actionOption_30').hide();	// Release from translation (Approve)
            $('.contextMenu a#actionOption_18').hide();	// Reject

            $actionMenu.data('complexDropdown').showOptionById('actionOption_19'); // Activate
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_20'); // Approve and override
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_21'); // Reject and override
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_6'); // Release for approval
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_17'); // Approve
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_30'); // Release from translation (Approve)
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_18'); // Reject
        }

        if (canReleaseForApproval) {
            $('.contextMenu a#actionOption_6').removeClass('disabled');	// Release for approval
            $('.contextMenu a#actionOption_19').removeClass('disabled');	// Activate
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_6'); // Release for approval
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_19'); // Activate
        }
        if (canApprove) {
            if(allIsInApprovalStep){
                $('.contextMenu a#actionOption_17').removeClass('disabled'); // Approve
                $actionMenu.data('complexDropdown').enableOptionById('actionOption_17'); // Approve
            }

            if(allIsInApprovalStep && allAreWorkflowOwner) {
                $('.contextMenu a#actionOption_20').removeClass('disabled'); // Approve and override
                $actionMenu.data('complexDropdown').enableOptionById('actionOption_20'); // Approve and override
            }
        }
        if (canReleaseFromTranslation) {
            if(allIsInTranslationStep && (isCurrentTranslator || allAreWorkflowOwner)){
                $('.contextMenu a#actionOption_30').removeClass('disabled'); // Release from translation (Approve)
                $actionMenu.data('complexDropdown').enableOptionById('actionOption_30'); // Release from translation (Approve)
            }
        }
        if (canReject) {
            $('a#actionOption_18').removeClass('disabled'); // Reject
            $('a#actionOption_21').removeClass('disabled'); // Reject and override
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_18'); // Reject
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_21'); // Reject and override
        }
        if (canAbort){
            $('a#actionOption_40').removeClass('disabled'); // Abort
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_40'); // Abort
        }
        if (canRetry){
            $('a#actionOption_45').removeClass('disabled'); // Retry
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_45'); // Abort
        }

        if (canCreateWorkingCopy) {
            $('.contextMenu a#actionOption_2').hide();
            $('.contextMenu a#actionOption_1').show();
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_2'); // Discard WC
            $actionMenu.data('complexDropdown').showOptionById('actionOption_1'); // Create WC
            $('.contextMenu a#actionOption_1').removeClass('disabled'); // Create WC
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_1'); // Create WC
        } else {
            $('a#actionOption_2').hide();
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_2');
        }
        if (canDiscard) {
            $('.contextMenu a#actionOption_1').hide();
            $('.contextMenu a#actionOption_2').show();
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_1'); // Create WC
            $actionMenu.data('complexDropdown').showOptionById('actionOption_2'); // Discard WC
            $('.contextMenu a#actionOption_2').removeClass('disabled'); // Discard WC
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_2'); // Discard WC
        }
        if (canArchive) {
            $('.contextMenu a#actionOption_4').hide();	// Delete Archive
            $('.contextMenu a#actionOption_3').show();	// Archive
            $('.contextMenu a#actionOption_3').removeClass('disabled'); // Archive
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_4'); // Delete Archive
            $actionMenu.data('complexDropdown').showOptionById('actionOption_3'); // Archive
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_3'); // Archive
        } else {
            $('.contextMenu a#actionOption_4').hide();	// Delete Archive
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_4'); // Delete Archive
        }
        if (canContentExport) {
            $('.contextMenu a#actionOption_43').removeClass('disabled');
        }

        $('.contextMenu a#actionOption_25').hide();	// Unhold
        $('.contextMenu a#actionOption_24').show();	// Hold
        $actionMenu.data('complexDropdown').hideOptionById('actionOption_25'); // Unhold
        $actionMenu.data('complexDropdown').showOptionById('actionOption_24'); // Hold
        $('#actionPopupUpdateVariantVersioning').removeClass('versionTogglePermitted');
        $('#actionPopupSuppress').removeClass('canSuppressOnHold');
        if (canHold) {
            if (canSuppress)
                $('#actionPopupSuppress').addClass('canSuppressOnHold');
            $('.contextMenu a#actionOption_24').removeClass('disabled');		// Hold
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_24'); // Hold
        } else if (canUnhold) {
            $('.contextMenu a#actionOption_24').hide();	// Hold
            $('.contextMenu a#actionOption_25').removeClass('disabled').show();	// Unhold
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_24'); // Hold
            $actionMenu.data('complexDropdown').showOptionById('actionOption_25'); // Unhold
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_25'); // Unhold
            if (isContentSuppressed)
                $('#actionPopupUpdateVariantVersioning').addClass('versionTogglePermitted');
        }
        $('.contextMenu a#actionOption_27').hide();	// Restore
        $('.contextMenu a#actionOption_26').show();	// Suppress
        $actionMenu.data('complexDropdown').hideOptionById('actionOption_27'); // Restore
        $actionMenu.data('complexDropdown').showOptionById('actionOption_26'); // Suppress
        if (canSuppress) {
            $('.contextMenu a#actionOption_26').removeClass('disabled');		// Suppress
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_26'); // Suppress
        } else if (canRestore) {
            $('a#actionOption_26').hide();	// Suppress
            $('a#actionOption_27').removeClass('disabled').show();	// Restore
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_26'); // Suppress
            $actionMenu.data('complexDropdown').showOptionById('actionOption_27'); // Restore
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_27'); // Restore
        }

        if (archived) {
            common.disableElement($updateBtn);
            common.enableElement($cloneBtn);
            $('ul.contextMenu').find("a[id^='actionOption']").hide();
            $('ul.contextMenu').find("a[id^='actioniFrame']").hide();
            $('.contextMenu a#actionOption_1').show();	// Create WC
            $('.contextMenu a#actionOption_1').removeClass('disabled'); // Create WC
            $('.contextMenu a#actionOption_4').show();	// Delete Archive
            $('.contextMenu a#actionOption_4').removeClass('disabled'); // Delete Archive
            $actionMenu.data('complexDropdown').hideAllTheOptions();
            $actionMenu.data('complexDropdown').showOptionById('actionOption_1'); // Create WC
            $actionMenu.data('complexDropdown').showOptionById('actionOption_4'); // Delete Archive
            $actionMenu.data('complexDropdown').showOptionById('actionOption_23'); // Clone
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_1'); // Create WC
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_4'); // Delete Archive
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_23'); // Clone
        }

        if (canReassign) {
            $('.contextMenu a#actionOption_5').removeClass('disabled');
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_5'); // Reassign
        }

        // Translation Reassign
        if (canTranslationReassign) {
            $('a#actionOption_41').show().removeClass('disabled');
            $('a#actionOption_5').hide();   // Reassign
            $actionMenu.data('complexDropdown').showOptionById('actionOption_41');
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_41');
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_5');  // Reassign
        }else{
            $('a#actionOption_41').hide();
            $('a#actionOption_5').show();   // Reassign
            $actionMenu.data('complexDropdown').hideOptionById('actionOption_41');
            $actionMenu.data('complexDropdown').showOptionById('actionOption_5');   // Reassign
        }

        if(canAddTask){
            $('.contextMenu a#actioniFrame_38').removeClass('disabled'); // Add task
            $actionMenu.data('complexDropdown').enableOptionById('actioniFrame_38'); // Add task
            $('.contextMenu a#actionOption_39').removeClass('disabled'); // Add task
            $actionMenu.data('complexDropdown').enableOptionById('actionOption_39'); // Add task
        }
    }

    if ($("input[id^='listItemCheck_']:checked").length == 0) {
        $exportMenu.data('complexDropdown').enableOptionById('actionOption_11');
        $('#includeAllMessagesCheckbox').attr('checked', true);

        $('#includeAllMessagesCheckboxAudit').attr('checked', true);
        $('#includeAllMessagesCheckboxAudit').attr('disabled', 'disabled')
    } else {
        $('#includeAllMessagesCheckbox').attr('checked', false);

        $('#includeAllMessagesCheckboxAudit').attr('checked', false);
        $('#includeAllMessagesCheckboxAudit').removeAttr('disabled');
    }

    hideDisabledContextMenus();
    $actionMenu.data('complexDropdown').hideAllDisabledOptions();
}
// ******* CONTEXT ACTIONS: END *******