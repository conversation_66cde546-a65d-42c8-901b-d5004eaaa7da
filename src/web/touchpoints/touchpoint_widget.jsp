<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew extendedScripts="true" disableMaintenancePopup="true">


        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iFrameMenu/iFrameMenu.css"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/dataTables/jquery.contextMenu.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iFrameMenu/jquery.iFrameMenu.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/smartTextContentViewer/jquery.smartTextContentViewer.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/styleManager/jquery.styleManager.js"/>

        <style type="text/css">

            /* ----- ----- ----- ----- ----- */
            /* ----- ----- ----- ----- ----- */
            /* Section Toggle */
            .sectionToggleContainer {
                display: none;
                position: absolute;
                width: 100%;
                z-index: 2;
            }

            .fullscreenToggleBtn {
                opacity: 0;
                position: absolute;
                top: 40px;
                right: 12px;
                z-index: 2;
                -webkit-transition: top .1s linear, opacity .1s linear;
                -webkit-border-radius: 50%;
                -moz-border-radius: 50%;
                border-radius: 50%;
                -webkit-box-shadow: 0 0 8px 1px rgba(0, 0, 0, 0.3);
                -moz-box-shadow: 0 0 8px 1px rgba(0, 0, 0, 0.3);
                box-shadow: 0 0 8px 1px rgba(0, 0, 0, 0.3);
            }

            .fullscreenToggleBtn:active {
                background: #9069a5;
                border-color: #9069a5;
            }

            #widgetInterfaceArea:hover .fullscreenToggleBtn.showable {
                opacity: 1;
                top: 32px;
            }

            /* ----- ----- ----- ----- ----- */
            /* ----- ----- ----- ----- ----- */
            /* Layout */
            .touchpointWidgetProcessing {
                display: none;
                position: fixed;
                left: 0;
                top: 0;
                bottom: 0;
                right: 0;
                left: 0;
                margin-bottom: 4.5rem;
                z-index: 1;
            }

            .touchpointWidgetContainer {
                z-index: 1;
                white-space: normal;
            }

            .roundAll {
                -webkit-border-radius: 6px;
                -moz-border-radius: 6px;
                border-radius: 6px;
            }

            .smsTextBubble {
                padding-top: 8px;
            }

            /* ----- ----- ----- ----- ----- */
            /* ----- ----- ----- ----- ----- */
            /* Forms */
            .mceInlineFormElements {
                display: inline-block;
                vertical-align: top;
            }

            .mceTextFieldElement, .mceCheckboxElement, .mceRadioElement, .mceMenuElement, .mceSubmitButtonElement {
                white-space: nowrap;
            }

            .mceFormInput {
                position: relative;
                box-sizing: border-box;
            }

            .mceFormInputLabel {
                white-space: normal;
                vertical-align: middle;
            }

            .mceRadioInput {
                font-size: 9px;
                text-align: center;
                vertical-align: middle;
            }

            .mceInline {
                box-sizing: border-box;
                display: inline-block;
                position: relative;
                vertical-align: top;
                padding: 1px;
            }

            .contentContainer var.staticContentItem, .contentContainer var.innerStaticContentItem {
                padding: 0px;
            }

            .contentContainer table, .contentContainer tr, .contentContainer td {
                box-sizing: border-box;
                line-height: 0;
                border: none;
            }

            [rotate='90'] {
                -ms-transform: rotate(90deg); /* IE 9 */
                transform: rotate(90deg);
                transform-origin: top left;
                position: absolute;
                left: 100%;
            }

            [rotate='180'] {
                -ms-transform: rotate(180deg); /* IE 9 */
                transform: rotate(180deg);
                position: absolute;
                left: 0px;
                top: 0px;
            }

            [rotate='270'] {
                -ms-transform: rotate(270deg); /* IE 9 */
                transform: rotate(270deg);
                transform-origin: top left;
                position: absolute;
                top: 100%;
            }

            .mceHiddenContent {
                display: none;
            }

            .tp-widget-card-bg {
                display: none;
                position: absolute;
                top: 0px;
                bottom: -2px;
                left: 27px;
                right: 27px;
                z-index: -1;
            }
        </style>

        <msgpt:Script>
            <script>
                function beforeContentMenuOpen(target) {
                    zoneSelect(target, false);
                }

                function processTouchpointRequestResponce(data, slideDirection, callback) {
                    $('#widgetInterfaceArea #touchpointContainer')
                        .prepend("<div style=\"display: inline-block\"><div id=\"touchpointWidgetContainer_" + data.section_id + "\" class=\"touchpointWidgetContainer activeContainer contentContainer\" style=\"display: none;\"></div></div>");

                    var currentWidgetContainer = $('#touchpointWidgetContainer_' + data.section_id);
                    $(currentWidgetContainer).html($('<div/>').html(data.section_html).text());

                    // Zone presentation: Adjust positioning to accomodate border width
                    $(currentWidgetContainer).find('.zoneNotSelectedClass,.zoneSelectedClass').each(function () {
                        try {
                            var borderWidth = parseFloat($(this).css('border-top-width').replace('px', ''));
                            var top = parseFloat($(this).css('top').replace('px', ''));
                            var left = parseFloat($(this).css('left').replace('px', ''));
                            $(this).css({
                                'top': top - borderWidth,
                                'left': left - borderWidth,
                                'cursor': 'pointer'
                            });
                        } catch (err) {
                            // UNKNOWN error resulting from .css() being UNDEFINED
                        }
                    });

                    // Zone presentation: Set selected zone
                    var currentZoneId = $(window.parent.document).find('#zoneSelect').val();
                    if (currentZoneId > 0)
                        $(".zoneNotSelectedClass[zoneid='" + currentZoneId + "']").removeClass('zoneNotSelectedClass').addClass('zoneSelectedClass');

                    setZoneBackgrounds();

                    $('.touchpointWidgetProcessing').removeClass('d-flex');

                    $("[id^='touchpointWidgetContainer']").removeClass('activeContainer').hide();

                    postSectionLoad($(currentWidgetContainer), data, slideDirection, callback);

                    function postSectionLoad(currentWidgetContainer, data, slideDirection, callback) {
                        if (slideDirection == null)
                            $(currentWidgetContainer).showEle('normal');
                        else
                            $(currentWidgetContainer).show("slide", {direction: slideDirection}, 400);

                        $('.sectionToggleContainer').css({top: $(currentWidgetContainer).outerHeight() + 30 + "px"}).fadeIn(400);

                        $('#fullscreenToggle').show().addClass('showable');

                        $(currentWidgetContainer).click(function (e) {
                            var element = eventTarget(e);
                            var closestZone = $(element).closest('.zoneNotSelectedClass,.zoneSelectedClass');
                            if (closestZone.length != 0) {
                                zoneSelect(closestZone, false);
                            } else if ($(element).closest('.sectionClass').length != 0) {
                                $(".zoneSelectedClass").removeClass('zoneSelectedClass').addClass('zoneNotSelectedClass');
                                setZoneBackgrounds();
                                if ($.isFunction(window.parent.sectionClicked)) {
                                    window.parent.sectionClicked($(element).closest('.sectionClass').attr('sectionId'));
                                }
                            }
                        });

                        updateSectionContents(data);

                        if ($.isFunction(callback))
                            callback();
                    }

                }

                function zoneSelect(zone, highlineOnly) {

                    if ( $(zone).is('.flowZone') )
                        return;

                    var targetZoneId = $(zone).attr('zoneid');
                    $(".zoneSelectedClass").removeClass('zoneSelectedClass').addClass('zoneNotSelectedClass');
                    $(".zoneNotSelectedClass[zoneid='" + targetZoneId + "']").removeClass('zoneNotSelectedClass').addClass('zoneSelectedClass');

                    setZoneBackgrounds();
                    if (!highlineOnly && $.isFunction(window.parent.zoneClicked)) {
                        window.parent.zoneClicked(targetZoneId);
                    }
                }

                function setZoneBackgrounds() {
                    $(".zoneSelectedClass:not(.flowZone),.zoneNotSelectedClass:not(.flowZone)").each(function () {
                        var backgroundColor = $(this).is('.zoneSelectedClass') ? "#c5cae3" : "transparent";
                        if (getParam('renderContent') != "false") {
                            backgroundColor = $(this).attr('zoneBackground');
                            if ($(this).is('.zoneSelectedClass'))
                                backgroundColor = '';
                            else
                                backgroundColor = backgroundColor != 'transparent' ? '#' + backgroundColor : backgroundColor;
                            // IE Hack: Transparent background causes zone to not be clickable: Apply transparent image
                            if (backgroundColor == 'transparent' && $(this).find('.blankZoneFill').length == 0) {
                                $(this).append("<img class=\"blankZoneFill\" src=\"../includes/themes/commonimages/icons/dot.gif\" width=\"" + $(this).width() + "px\" height=\"" + $(this).height() + "px\" />");
                                // Standard Zones: Reposition transparent cover overtop of content
                                if ($(this).find('.multipartZonePart').length == 0 && $(this).children().length > 1)
                                    $(this).find('.blankZoneFill').css({
                                        position: 'relative',
                                        top: '-' + $(this).height() + 'px'
                                    });
                            }
                        }
                        $(this).css({'background-color': backgroundColor});
                    });
                }

                beforeIndex = 0;

                function inchesToPx(inches) {
                    var ppi = 100;
                    return parseFloat(inches) * ppi;
                }

                function setContent(ele, content, fontScale) {
                    // Clear any existing content
                    if (content.content_type == 4) {
                        if ($(ele).is('.multipartZonePart'))
                            $(ele).html('');
                        else
                            $(ele).find('.multipartZonePart').html('');
                    } else {
                        $(ele).html('');
                    }

                    // Zone border color management: No messages; Red, No content, Yellow
                    if ($(ele).is('.multipartZonePart'))
                        $(ele).parent().css('border-color', '');
                    else
                        $(ele).css('border-color', '');

                    if (content.content_state == "NO_MESSAGES") {
                        $(ele).css('border-color', '#ff0000');
                        setZoneBackgrounds();
                        if (content.zone_type != 10)
                            return;
                    } else if (content.content_state == "NO_CONTENT") {
                        $(ele).css('border-color', '#ffff00');
                        setZoneBackgrounds();
                        return;
                    } else {
                        $(ele).css('border-color', '#299000');
                        $(ele).closest('.zoneNotSelectedClass,.zoneSelectedClass').css('border-color', '#299000');
                    }


                    // Mark zone with current message instance ID
                    if (content.message_id != undefined) {
                        if ($(ele).is('.multipartZonePart'))
                            $(ele).parent().attr('contentObjectId', content.message_id);
                        else
                            $(ele).attr('contentObjectId', content.message_id);
                    }

                    var stampDate = new Date();
                    var width = $(ele).width();
                    var height = $(ele).height();
                    if (content.img_name) {
                        var src = null;
                        if (content.img_name.match(/\.pdf$|\.pdf&|\.pdf\?/i))
                            src = context + '/download/image.form?resource=' + content.img_resource + "&type=rendered_pdf&tk=" + getParam('tk') + "&cacheStamp=" + stampDate.getTime();
                        else if (content.img_name.match(/\.rtf$|\.rtf&|\.rtf\?/i))
                            src = "../includes/themes/commonimages/icons/rtf_large_icon.png";
                        else if (content.img_name.match(/\.img$|\.img&|\.img\?/i))
                            src = "../includes/themes/commonimages/icons/img_large_icon.png";
                        else if (content.img_name.match(/\.eps$|\.eps&|\.eps\?/i))
                            src = "../includes/themes/commonimages/icons/eps_large_icon.png";
                        else if (content.img_name.match(/\.dxf$|\.dxf&|\.dxf\?/i))
                            src = "../includes/themes/commonimages/icons/dxf_large_icon.png";
                        else if (content.img_name.match(/\.dlf$|\.dlf&|\.dlf\?/i))
                            src = "../includes/themes/commonimages/icons/dlf_large_icon.png";
                        else if (content.img_name.match(/\.docx$|\.docx&|\.docx\?/i))
                            src = "../includes/themes/commonimages/icons/docx_large_icon.png";
                        else
                            src = context + '/download/image.form?resource=' + content.img_resource + "&tk=" + getParam('tk') + "&cacheStamp=" + stampDate.getTime();
                        $(ele)
                            .html("<img src=\"" + src + "\" style=\"width: 100%; max-width: " + width + "px; max-height: " + height + "px; display: none;\" />")
                            .css({'opacity': 1, 'text-align': 'center', 'z-index': '1'});
                        if (parseFloat(content.zone_rotation) > 0)
                            $(ele).find('img')
                                .attr('rotate', content.zone_rotation)
                                .css({'max-width': (content.zone_rotation == 180 ? width: height) + 'px', 'max-height': (content.zone_rotation == 180 ? height : width) + 'px'});
                        $(ele).find('img').load(function () {
                            $(this).showEle("normal");
                        });
                    } else if (content.content == undefined) {
                        $(ele).css('border-color', '#ffcc00');
                        if (content.content_type == 4)
                            $(ele).find('.multipartZonePart').html('');
                        else
                            $(ele).html('');
                    } else {
                        if (content.zone_rotation == 90 || content.zone_rotation == 270)
                            $(ele).html("<div class=\"textContentContainer\" style=\"width: " + height + "px; height: " + width + "px;\"></div>");
                        else
                            $(ele).html("<div class=\"textContentContainer\" style=\"width: " + width + "px; height: " + height + "px;\"></div>");

                        if (content.zone_rotation)
                            $(ele).find('.textContentContainer').attr('rotate', content.zone_rotation);

                        // SMS Presentation: Text appears in bubble graphic
                        if ($('.smsWidgetContainer').length > 0) {
                            $(ele).append("<div class=\"smsTextBubble\">" +
                                "<img src=\"../includes/themes/commonimages/misc/smsTextBubble.png\" width=\"100%\" style=\"position: absolute; z-index: 1;\" />" +
                                "</div>");
                            $(ele).find('.smsTextBubble').append($('.textContentContainer'));
                            $('.textContentContainer')
                                .css({
                                    'z-index': 2,
                                    'position': 'relative',
                                    'margin': '7px 0px 0px 19px',
                                    'word-wrap': 'break-word'
                                })
                                .width($('.textContentContainer').width() - 31);
                        }

                        $(ele).find('.textContentContainer').html($('<div/>').html(content.content).text());
                        $(ele).css({'opacity': 1, 'overflow': 'hidden', 'z-index': '2'});

                        if ( $.styleManager.get($('#touchpointContainer')) )
                            $.styleManager.get($('#touchpointContainer')).updateStyleReferenceCSS();

                        // Smart Text:  Init content popup
                        $(ele).find('.textContentContainer var').each(function () {
                            if ($(this).attr('type') == "4" || $(this).attr('type') == "5") {
                                $(this).smartTextContentViewer({
                                    trigger: "dblclick",
                                    contentType: 'embeddedText',
                                    contentItemId: $(this).attr('id'),
                                    fnBeforeContentRequest: function (o) {
                                        o.data.contentLocaleId = 0;
                                    }
                                });
                            }
                        });

                        // VARIABLES: Label/sample value rendering
                        $(ele).find('.textContentContainer .renderedSampleValueContainer').each( function() {
                            var varEle = $(this).closest("[type='1']");
                            if ( localStorage.getItem("msgpt_samplevaluerendering_enable") == "true" ) {
                                $(varEle).children('.renderedLabelContainer').hide();
                                $(varEle).children('.renderedSampleValueContainer').show();
                            } else {
                                $(varEle).children('.renderedSampleValueContainer').hide();
                                $(varEle).children('.renderedLabelContainer').show();
                            }
                        });

                        var tagTargetArray = new Array();
                        var tagIdIndex = 0;
                        $(ele).find('.textContentContainer').find('span,p,li,div,td,th,table,iframe,embed,img[rotate],a').each(function () {
                            var targetStyleJSON = {};

                            if ($(this).is('iframe,embed,img[rotate],a')) {
                                var width = parseFloat($(this).css('width').replace('px', ''));
                                if (width != $(ele).width())
                                    $.extend(true, targetStyleJSON, {'width': (width * fontScale) + 'px'});
                                var height = parseFloat($(this).css('height').replace('px', ''));
                                if (height != $(ele).height())
                                    $.extend(true, targetStyleJSON, {'height': (height * fontScale) + 'px'});
                            }
                            if ($(this).is('table')) {
                                $(this).css({'border-collapse': 'separate'});
                                if ($(this).attr('t_dim') != undefined || $(this).attr('t_width') != undefined) {
                                    if ($(this).attr('t_width') != undefined) {
                                        var width = parseFloat($(this).attr('t_width').replace('px', ''));
                                        $.extend(true, targetStyleJSON, {'width': (width * fontScale) + 'px'});
                                    } else {
                                        var width = parseFloat($(this).attr('t_dim').replace('px', ''));
                                        $.extend(true, targetStyleJSON, {'width': width + '%'});
                                    }
                                    $.extend(true, targetStyleJSON, {'height': 'initial'});

                                    var colWidths = $(this).attr('c_dim').split(':');
                                    var rowHTML = "<tr>";
                                    for (var i = 0; i < colWidths.length; i++)
                                        rowHTML += "<td style=\"height: 0px; width: " + colWidths[i] + "%; padding: 0px;\"></td>";
                                    rowHTML += "</tr>";
                                    $(this).find('tr:last').after(rowHTML);
                                }
                            }
                            if ($(this).is('th,td')) {
                                var maxWidth = parseFloat($(this).css('max-width').replace('px', ''));
                                $.extend(true, targetStyleJSON, {'width': 'initial'});
                                $.extend(true, targetStyleJSON, {'max-width': (maxWidth * fontScale) + 'px'});
                                var minWidth = parseFloat($(this).css('min-width').replace('px', ''));
                                $.extend(true, targetStyleJSON, {'min-width': (minWidth * fontScale) + 'px'});
                                if ($(this).closest('tr').attr('row_height')) {
                                    var height = parseFloat($(this).css('height').replace('px', ''));
                                    $.extend(true, targetStyleJSON, {'height': (height * fontScale) + 'px'});
                                } else {
                                    $.extend(true, targetStyleJSON, {'height': 'initial'});
                                }
                            }
                            if ($(this).is('p,li,span,a')) {
                                $.extend(true, targetStyleJSON, {'min-height': 'initial'});

                                if ($(this).css('line-height').indexOf('px') != -1) {
                                    var lineHeight = parseFloat($(this).css('line-height').replace('px', ''));
                                    $.extend(true, targetStyleJSON, {'line-height': Math.max((lineHeight * fontScale), 1) + 'px'});
                                }
                            }
                            if ($(this).is('li:not([beforeIndex])')) {
                                if ( $('head').find('style#listStyles').length == 0 )
                                    $('head').append("<style id=\"listStyles\"></style>");

                                var beforeCSS = 'li.beforeIndexBEFORE_INDEX:before { CSS_VALUES }';
                                var beforeCSSValues = '';
                                var beforeLeft = window.getComputedStyle($(this).get(0), ':before').getPropertyValue('left');
                                if (beforeLeft.indexOf('px') != -1) {
                                    var beforeLeft = parseFloat(beforeLeft.replace('px', ''));
                                    beforeCSSValues += 'left: ' + (beforeLeft * fontScale) + 'px !important;';
                                }
                                var beforeFontSize = window.getComputedStyle($(this).get(0), ':before').getPropertyValue('font-size');
                                if (beforeFontSize.indexOf('px') != -1) {
                                    var beforeFontSize = parseFloat(beforeFontSize.replace('px', ''));
                                    beforeCSSValues += 'font-size: ' + (beforeFontSize * fontScale) + 'px !important;';
                                }
                                var beforePaddingTop = window.getComputedStyle($(this).get(0), ':before').getPropertyValue('padding-top');
                                if (beforePaddingTop.indexOf('px') != -1) {
                                    var beforePaddingTop = parseFloat(beforePaddingTop.replace('px', ''));
                                    beforeCSSValues += 'padding-top: ' + (beforePaddingTop * fontScale) + 'px !important;';
                                }
                                beforeCSS = beforeCSS.replace('CSS_VALUES', beforeCSSValues);
                                beforeCSS = beforeCSS.replace('BEFORE_INDEX', beforeIndex);
                                $('head').find('style#listStyles').append(beforeCSS);
                                $(this).addClass('beforeIndex' + beforeIndex);
                                beforeIndex++;
                            }
                            if ($(this).is('p')) {
                                if ($.trim($(this).text()).length == 0)
                                    $(this).html("&nbsp;");
                            }
                            var fontSize = parseFloat($(this).css('font-size').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'font-size': (fontSize * fontScale) + 'px'});
                            var marginTop = parseFloat($(this).css('margin-top').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'margin-top': (marginTop * fontScale) + 'px'});
                            var marginBottom = parseFloat($(this).css('margin-bottom').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'margin-bottom': (marginBottom * fontScale) + 'px'});
                            var paddingTop = parseFloat($(this).css('padding-top').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'padding-top': (paddingTop * fontScale) + 'px'});
                            var paddingRight = parseFloat($(this).css('padding-right').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'padding-right': (paddingRight * fontScale) + 'px'});
                            var paddingBottom = parseFloat($(this).css('padding-bottom').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'padding-bottom': (paddingBottom * fontScale) + 'px'});
                            var paddingLeft = parseFloat($(this).css('padding-left').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'padding-left': (paddingLeft * fontScale) + 'px'});
                            var marginLeft = parseFloat($(this).css('margin-left').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'margin-left': (marginLeft * fontScale) + 'px'});
                            var marginRight = parseFloat($(this).css('margin-right').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'margin-right': (marginRight * fontScale) + 'px'});
                            var textIndent = parseFloat($(this).css('text-indent').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'text-indent': (textIndent * fontScale) + 'px'});

                            var borderTopWidth = parseFloat($(this).css('border-top-width').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'border-top-width': Math.ceil(borderTopWidth * fontScale) + 'px'});
                            var borderBottomWidth = parseFloat($(this).css('border-bottom-width').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'border-bottom-width': Math.ceil(borderBottomWidth * fontScale) + 'px'});
                            var borderLeftWidth = parseFloat($(this).css('border-left-width').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'border-left-width': Math.ceil(borderLeftWidth * fontScale) + 'px'});
                            var borderRightWidth = parseFloat($(this).css('border-right-width').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'border-right-width': Math.ceil(borderRightWidth * fontScale) + 'px'});


                            if ($(this).is('p')) {
                                var customAttributes = ['text_align', 'spacing', 'line_spacing', 'special_indent'];

                                var hasCustomAttributes = customAttributes.some(function(attr) {
                                    return $(this).attr(attr) !== null;
                                }, this);

                                if (hasCustomAttributes) {

                                    var textAlign = $(this).attr('text_align');
                                    if (textAlign) {
                                        $.extend(true, targetStyleJSON, {'text-align': textAlign});
                                    }

                                    var spacing = $(this).attr('spacing');
                                    var marginLeft = 0;

                                    if (spacing) {
                                        var spacingValues = spacing.split(':').map(function(val) {
                                            return parseFloat(val.replace('in', ''));
                                        });
                                        $.extend(true, targetStyleJSON, {'padding-top': inchesToPx(spacingValues[0] * fontScale) + 'px'});
                                        $.extend(true, targetStyleJSON, {'margin-right': inchesToPx(spacingValues[1] * fontScale) + 'px'});
                                        $.extend(true, targetStyleJSON, {'padding-bottom': inchesToPx(spacingValues[2] * fontScale) + 'px'});
                                        $.extend(true, targetStyleJSON, {'margin-left': inchesToPx(spacingValues[3] * fontScale) + 'px'});
                                    }

                                    var specialIndent = parseFloat($(this).attr('special_indent'));
                                    if (specialIndent) {
                                        $.extend(true, targetStyleJSON, {'text-indent': inchesToPx(specialIndent * fontScale) + 'px'});

                                        marginLeft -= specialIndent < 0 ? specialIndent : 0;
                                        $.extend(true, targetStyleJSON, {'margin-left': inchesToPx(marginLeft * fontScale) + 'px'});
                                    }

                                    var lineHeight = parseFloat($(this).attr('line_spacing'));
                                    if (lineHeight) {
                                        $.extend(true, targetStyleJSON, {'line-height': lineHeight});
                                    }
                                }

                            }

                            if ($(this).is('div.mceListItemContent')) {
                                var list = $(this).closest('ul,ol');
                                var customAttributes = ['bullet_spacing', 'list_spacing', 'line_spacing' ];

                                var hasCustomAttributes = customAttributes.some(function(attr) {
                                    return $(list).attr(attr) !== null;
                                }, this);

                                var bulletSpacingValues = [0, 0, 0, 0];
                                var listSpacingValues = [0, 0, 0, 0];

                                if (hasCustomAttributes) {
                                    var bulletSpacing = $(list).attr('bullet_spacing');
                                    if (bulletSpacing) {
                                        bulletSpacingValues = bulletSpacing.split(':').map(function(val) {
                                            return parseFloat(val.replace('in', ''));
                                        });
                                    }

                                    var listSpacing = $(list).attr('list_spacing');
                                    if (listSpacing) {
                                        listSpacingValues = listSpacing.split(':').map(function(val) {
                                            return parseFloat(val.replace('in', ''));
                                        });
                                    }
                                    var bulletTopSpacing = bulletSpacingValues[0] || 0;
                                    var bulletLeftSpacing = bulletSpacingValues[1] || 0;
                                    var bulletBottomSpacing = bulletSpacingValues[2] || 0;
                                    var bulletRightSpacing = bulletSpacingValues[3] || 0;
                                    var listSpacingRight = listSpacingValues[1] || 0;

                                    var left = bulletLeftSpacing + bulletRightSpacing;
                                    var width = bulletLeftSpacing + bulletRightSpacing + listSpacingRight;

                                    var lineSpacing = $(list).attr('line_spacing') || '';
                                    var lineSpacingValue = parseFloat(lineSpacing) || 1;
                                    var lineSpacingType = lineSpacing.slice(-1) === 'm' ? 1 : 2;
                                    var lineHeight = (lineSpacingValue > 0 ? lineSpacingValue : 1) + (lineSpacingType == 2 ? 'pt': '')

                                    $.extend(true, targetStyleJSON, {
                                        'padding-top': inchesToPx(bulletTopSpacing * fontScale) + 'px',
                                        'padding-bottom': inchesToPx(bulletBottomSpacing * fontScale) + 'px',
                                        'left': inchesToPx(left * fontScale) + 'px',
                                        'width': 'calc(100% - ' + inchesToPx(width * fontScale) + 'px)'
                                    });

                                    // Handle line height based on type
                                    if (typeof lineHeight === 'string' && lineHeight.indexOf('pt') !== -1) {
                                        $.extend(true, targetStyleJSON, {
                                            'line-height': Math.max(lineSpacingValue * fontScale, 1) + 'px'
                                        });
                                    } else {
                                        $.extend(true, targetStyleJSON, {
                                            'line-height': Math.max(lineSpacingValue, 1)
                                        });
                                    }
                                }
                            }

                            $(this).addClass('tagStyle_' + tagIdIndex);
                            tagTargetArray[tagIdIndex++] = targetStyleJSON;
                        });
                        $(ele).find('.textContentContainer').find('ul,ol').each(function () {
                            var targetStyleJSON = {};

                            var marginTop = parseFloat($(this).css('margin-top').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'margin-top': (marginTop * fontScale * 0.5) + 'px'});
                            var marginBottom = parseFloat($(this).css('margin-bottom').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'margin-bottom': (marginBottom * fontScale * 0.5) + 'px'});
                            var paddingTop = parseFloat($(this).css('padding-top').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'padding-top': (paddingTop * fontScale * 0.5) + 'px'});
                            var paddingBottom = parseFloat($(this).css('padding-bottom').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'padding-bottom': (paddingBottom * fontScale * 0.5) + 'px'});
                            var paddingLeft = parseFloat($(this).css('padding-left').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'padding-left': (paddingLeft * fontScale * 0.5) + 'px'});
                            var left = parseFloat($(this).css('left').replace('px', ''));
                            $.extend(true, targetStyleJSON, {'left': (left * fontScale * 0.5) + 'px'});

                            $(this).addClass('tagStyle_' + tagIdIndex);
                            tagTargetArray[tagIdIndex++] = targetStyleJSON;
                        });
                        $(ele).find('.textContentContainer').find('.staticContentItem,.innerStaticContentItem').each(function () {
                            $(this).css('border', 'none');
                        });
                        $(ele).find('.textContentContainer').find('.mceLineElement div').each(function () {
                            var height = $(this).height();
                            $(this).css({'height': Math.max(($(this).height() * fontScale), 1) + 'px'});
                        });
                        $(ele).find('.textContentContainer').find('.mceInline:not([r_dim])').each(function () {
                            $(this).css({'height': Math.max(($(this).height() * fontScale), 1) + 'px'});
                            $(this).css({'width': Math.max(($(this).width() * fontScale), 1) + 'px'});
                        });
                        $($(ele).find('.textContentContainer').find('.mceDraggable,.mceLockedDraggable,.mceInline').get().reverse()).each(function () {

                            if ($(this).attr('r_dim') == undefined)
                                return;

                            var rDimArray = $(this).attr('r_dim').split(':');
                            var x = rDimArray[0].split('=')[1];
                            var y = rDimArray[1].split('=')[1];
                            var w = rDimArray[2].split('=')[1];
                            var h = rDimArray[3].split('=')[1];
                            var cw = $(this).closest('.textContentContainer').width(),
                                ch = $(this).closest('.textContentContainer').height();
                            if (rDimArray.length > 4) {
                                cw = $(this).attr('r_dim').split(':')[4].split('=')[1] * fontScale;
                                ch = $(this).attr('r_dim').split(':')[5].split('=')[1] * fontScale;
                            }

                            if ($(this).is('.mceDraggable'))
                                $(this).css({'position': 'absolute'});

                            // SIZE SUBCONTAINERS: Forms
                            var rootContainer = $(this);
                            $(this).find('.mceFormInputLabel,.mceFormInput,.mceFormInput div').each(function () {

                                if ($(this).is('.scaled'))
                                    return
                                $(this).addClass('scaled');

                                var nestedTable = $(rootContainer).find('table');
                                var isNestedTableEle = nestedTable.length != 0 && $.contains($(nestedTable).get(0), this);
                                var isFormInputSubNode = $(this).parent().is('.mceFormInput');
                                var appliedContainer = isNestedTableEle ? $(this) : rootContainer;

                                var relW = $(this).width() / $(appliedContainer).width();
                                var relH = $(this).height() / $(appliedContainer).height();
                                var relL = $(this).position().left / $(this).width();

                                var appliedW = isNestedTableEle ? $(this).closest("[r_dim]").attr('r_dim').split(':')[2].split('=')[1] : w;
                                var appliedH = isNestedTableEle ? $(this).closest("[r_dim]").attr('r_dim').split(':')[3].split('=')[1] : h;

                                var left = relL * relW * cw * appliedW / 100;
                                if (isNestedTableEle && $(this).attr('r_dim'))
                                    left = 0;
                                else if (isNestedTableEle || isFormInputSubNode)
                                    left = $(this).position().left * fontScale;

                                $(this).css({
                                    'left': left,
                                    'width': relW * cw * appliedW / 100,
                                    'height': relH * ch * appliedH / 100
                                });

                                if ($(this).css('line-height').indexOf('px') != -1) {
                                    var lineHeight = parseFloat($(this).css('line-height').replace('px', ''));
                                    $(this).css({'line-height': Math.max((lineHeight * fontScale), 1) + 'px'});
                                }
                            });

                            var containerContainer = $(rootContainer).is('.mceLockedDraggable') ? $(this).closest('.mceDraggable') : $(this).closest('.textContentContainer');
                            var ccw = $(containerContainer).width();
                            var cch = $(containerContainer).height();
                            if (rDimArray.length > 4) {
                                ccw = cw;
                                cch = ch;
                            }
                            if ($(rootContainer).is('.mceDraggable,.mceLockedDraggable')) {
                                $(this).css({
                                    'left': ccw * x / 100,
                                    'top': cch * y / 100,
                                });
                            }// + 2 container border width (1 px all sides)
                            $(this).css({
                                'width': ccw * w / 100 + 2,
                                'height': cch * h / 100 + 2
                            });

                        });

                        for (var styleIndex = 0; styleIndex < tagTargetArray.length; styleIndex++)
                            $('.tagStyle_' + styleIndex).removeClass('tagStyle_' + styleIndex).css(tagTargetArray[styleIndex]);

                        setZoneBackgrounds();
                    }
                }

                function updateContent(contentObjectId) {

                    if (!$('#widgetInterfaceArea').is(':visible') || !$(window.parent.document).find('#widgetContainer').is(':visible'))
                        return;

                    if (!contentObjectId)
                        return;

                    var stampDate = new Date();
                    $.ajax({
                        type: "GET",
                        url: context + "/getSectionImageThumbnail.form" +
                        "?type=updateContent" +
                        "&documentId=" + getParam('documentId') +
                        "&touchpointSelectionId=" + (getParam('touchpointSelectionId') != "" ? getParam('touchpointSelectionId') : -1) +
                        "&channelContextId=" + (getParam('channelContextId') != "" ? getParam('channelContextId') : -1) +
                        "&renderContent=" + (getParam('renderContent') != "" ? getParam('renderContent') : "true") +
                        "&contentObjectId=" + contentObjectId +
                        "&zoneFilter=" + (getParam('zoneFilter') != "" ? getParam('zoneFilter') : "default") +
                        "&maxWidth=320" +
                        "&tk=" + getParam('tk') +
                        "&cacheStamp=" + (stampDate.getTime()),
                        dataType: "json",
                        success: function (data) {
                            processUpdateContentRequestResponce(data);
                            updateIframeHeight();
                        }
                    });
                }

                function updateZoneDisplay(zoneId, sectionId) {
                    var targetZone = $("div[zoneid='" + zoneId + "']");
                    requestWidgetDisplay(sectionId, null, zoneSelect(targetZone, true), null);
                }

                function updateSectionContents(data) {
                    if ($('head #dynamicContentCSS').length == 0)
                        $('head').append("<style id=\"dynamicContentCSS\" type=\"text/css\">" + data.css + "</style>");
                    else
                        $('head #dynamicContentCSS').append(data.css);

                    if (data.contents) {
                        for (var contentIndex = 0; contentIndex < data.contents.length; contentIndex++) {
                            var currentContent = data.contents[contentIndex];

                            if (currentContent.content_type == 4 && currentContent.content_state != "NO_MESSAGES") {
                                // Multi-part Zone
                                setContent($('#zone' + currentContent.zone_id + '_part' + currentContent.part_id), currentContent, data.fontScale);
                            } else {
                                // Standard Zone
                                setContent($("div[zoneid='" + currentContent.zone_id + "']"), currentContent, data.fontScale);
                            }
                        }
                    }

                }

                function processUpdateContentRequestResponce(data) {
                    var currentSectionId = $("[id^='touchpointWidgetContainer'].activeContainer .sectionBody").attr('sectionId');
                    if (data.section_id != currentSectionId)
                        requestWidgetDisplay(data.section_id, data, null, null);
                    else
                        updateSectionContents(data);
                }

                function clicked(ele) {
                    // Do nothing: default click handler from widget
                }

                function initZoneActions() {
                    // Register right click context menu for each zone
                    // Init tool tips
                    var stampDate = new Date();
                    $(".zoneSelectedClass,.zoneNotSelectedClass").each(function () {
                        $(this).popupFactory({
                            title: $(this).is('.flowZone') ? client_messages.title.flow_zone : client_messages.title.zone,
                            popupLocation: "right",
                            asyncSetContentURL: context + "/getObjectInfo.form?type=zoneSummary&objectId=" + $(this).attr('zoneid') + "&cacheStamp=" + (stampDate.getTime()),
                            asyncSetContentHandler: function (o, data) {
                                return processZoneSummaryRequest(data);
                            }
                        });

                        // Append icons indicating flow to Zone if is .flowZone or .flowIntoZone
                        if ($(this).is('.flowZone') && $(this).find('.flowIcon').length == 0) {
                            var flowIcon = $('<div class="flowIcon"><i class="far fa-circle-arrow-right"></i></div>');
                            $(this).append(flowIcon);
                        } else if ($(this).is('.flowIntoZone') && $(this).find('.flowIntoIcon').length == 0) {
                            var flowIntoIcon = $('<div class="flowIntoIcon"><i class="far fa-circle-arrow-right"></i></div>');
                            $(this).append(flowIntoIcon);
                        }

                        // Only display the context menu for the message list page
                        var parentURL = getTopFrame().location.href;
                        if (!$(this).is('.flowZone') && parentURL.indexOf('touchpoint_content_object_list') != -1) {
                            $(this).contextMenu({
                                    menu: "contextMenuContainer_touchpointWidget"
                                },

                                function (entry, action, el, pos) {
                                    var contextMenuEntry = entry;
                                    //make sure the entry is not disabled
                                    if (!$(contextMenuEntry).hasClass('disabled')) {
                                        var actionType = action.split(':')[0];
                                        var actionId = action.split(':')[1];
                                        if (actionType == "iFrameAction")
                                            if ($.isFunction(iFrameAction))
                                                iFrameAction(actionId, contextMenuEntry);
                                    }
                                });
                        }
                    });
                }

                function processZoneSummaryRequest(data) {
                    var popupHTML = "<div align=\"left\" style=\"padding: 5px 15px 10px 15px; font-size: 11px;\">";
                    if (data.is_flowzone) {
                        popupHTML +=
                            "<div style=\" padding: 2px 0px 6px 0px;\">" +
                                client_messages.text.click_on_flowzone +
                            "</div>";
                    }
                    popupHTML += "<div style=\"font-weight: bold; padding: 0px;\">" + client_messages.text.name + "</div>" +
                        "<div style=\" padding: 2px 0px 6px 0px;\">" +
                        data.name +
                        "</div>" +
                        "<div style=\"font-weight: bold; padding: 0px;\">" + client_messages.text.connector + "</div>" +
                        "<div style=\" padding: 2px 0px 6px 0px;\">" +
                        (data.connector.length != 0 ? data.connector : client_messages.text.none) +
                        "</div>";
                    if (data.parts) {
                        popupHTML += "<div style=\"font-weight: bold; padding: 0px;\">" + client_messages.text.parts + "</div>" +
                            "<div style=\" padding: 2px 0px 6px 0px;\">";
                        for (var i = 0; i < data.parts.length; i++)
                            popupHTML += data.parts[i].name +
                                " (" +
                                data.parts[i].content_type +
                                (data.parts[i].sub_content_type ? ": " + data.parts[i].sub_content_type : "") +
                                ")<br/>";
                        popupHTML += "</div>";
                    }
                    popupHTML += "</div>";

                    var popupEle = $(popupHTML);

                    return popupEle;
                }

                function updateIframeHeight() {
                    var parentIFrame = $(window.parent.document).find('#touchpointNavigationWidget');
                    var height = $(parentIFrame).contents().find('body').get(0).scrollHeight;
                    $(parentIFrame).height(height);
                }

                function requestWidgetDisplay(sectionId, contentPostHandlerData, onWidgetCallback, slideDirection) {

                    if (!$('#widgetInterfaceArea').is(':visible') || !$(window.parent.document).find('#widgetContainer').is(':visible'))
                        return;

                    // Clear currently selected zone
                    $("[id^='touchpointWidgetContainer'].activeContainer .zoneSelectedClass").removeClass('zoneSelectedClass').addClass('zoneNotSelectedClass');

                    $("[id^='touchpointWidgetContainer']").removeClass('activeContainer');
                    $("[id^='touchpointWidgetContainer']").hide();
                    $(".tp-widget-card-bg").hide();
                    $('#fullscreenToggle').hide().removeClass('showable');

                    if (exists('touchpointWidgetContainer_' + sectionId)) {
                        var $container = $('#touchpointWidgetContainer_' + sectionId);
                        $container.addClass('activeContainer');

                        if (slideDirection == null)
                            $container.showEle('normal');
                        else
                            $container.show("slide", {direction: slideDirection}, 400);

                        $('#fullscreenToggle').show().addClass('showable');

                        if (contentPostHandlerData)
                            updateSectionContents(contentPostHandlerData);

                        $('.sectionToggleContainer').css({top: $container.outerHeight() + 30 + "px"}).fadeIn(400);

                        updateSectionToggle();
                        updateIframeHeight();
                        $(".tp-widget-card-bg").show();
                    } else {

                        $('.touchpointWidgetProcessing').addClass('d-flex');

                        var parentURL = getTopFrame().location.href;
                        var visibility = (parentURL.indexOf('zone_list') != -1) ? 'full' : 'null';
                        var stampDate = new Date();
                        $.ajax({
                            type: "GET",
                            url: context + "/getSectionImageThumbnail.form" +
                            "?type=touchpointNavigation" +
                            "&documentId=" + getParam('documentId') +
                            "&sectionId=" + (sectionId ? sectionId : -1) +
                            "&touchpointSelectionId=" + (getParam('touchpointSelectionId') != "" ? getParam('touchpointSelectionId') : -1) +
                            "&channelContextId=" + (getParam('channelContextId') != "" ? getParam('channelContextId') : -1) +
                            "&renderContent=" + (getParam('renderContent') != "" ? getParam('renderContent') : "true") +
                            "&zoneFilter=" + (getParam('zoneFilter') != "" ? getParam('zoneFilter') : "default") +
                            "&maxWidth=320" +
                            "&tk=" + getParam('tk') +
                            "&visibility=" + visibility +
                            "&cacheStamp=" + (stampDate.getTime()),
                            dataType: "json",
                            success: function (data) {
                                processTouchpointRequestResponce(data, slideDirection, function () {
                                    updateSectionToggle();
                                    initZoneActions();
                                    updateIframeHeight();
                                    $(".tp-widget-card-bg").show();
                                    if (contentPostHandlerData)
                                        updateSectionContents(contentPostHandlerData);
                                });
                            }
                        });
                    }

                    $('.sectionToggleContainer').find('[data-toggle="tooltip"]').tooltip('hide');

                    if ($.isFunction(onWidgetCallback))
                        onWidgetCallback();

                }

                function toggleZone(zoneId) {
                    $(".zoneSelectedClass").removeClass('zoneSelectedClass').addClass('zoneNotSelectedClass');
                    $(".zoneNotSelectedClass[zoneid='" + zoneId + "']").removeClass('zoneNotSelectedClass').addClass('zoneSelectedClass');
                    setZoneBackgrounds();
                }

                function updateSectionToggle() {
                    if ($('.sectionIndicator').length == 0)
                        return;
                    $("[id^='sectionIndicator']").hide();
                    $("[id^='itemsIndicator']").hide();

                    // Set indicator for currently viewed section
                    $('.sequenceItem').removeClass('sequenceItemIcon_selected').addClass('sequenceItemIcon').parent().removeClass('active');
                    var currentSectionId = $("[id^='touchpointWidgetContainer']:visible .sectionBody").attr('sectionId');
                    if ($('#sectionIndicator_' + currentSectionId).length == 0)
                        return;
                    $('#sectionIndicator_' + currentSectionId).showEle('normal');
                    $('#sectionIndicator_' + currentSectionId).find('.sequenceItem').addClass('sequenceItemIcon_selected').parent().addClass('active');

                    // Cap the maximum number of section indicators that can be displayed
                    var indCount = 1;
                    // additional index to ensure that the loop does not run infinitely
                    var maxIndCount = 0;
                    while (indCount < 3 && indCount < $("[id^='sectionIndicator']").length && maxIndCount < 10) {
                        if ($('#sectionIndicator_' + currentSectionId).prevAll('.sectionIndicator:hidden:first').length != 0) {
                            $('#sectionIndicator_' + currentSectionId).prevAll('.sectionIndicator:hidden:first').showEle('normal');
                            indCount++;
                        }
                        if ($('#sectionIndicator_' + currentSectionId).nextAll('.sectionIndicator:hidden:first').length != 0) {
                            $('#sectionIndicator_' + currentSectionId).nextAll('.sectionIndicator:hidden:first').showEle('normal');
                            indCount++;
                        }
                        maxIndCount++;
                    }
                    if ($('#sectionIndicator_' + currentSectionId).prevAll('.sectionIndicator:hidden').length != 0)
                        $('#itemsIndicator_prev').show();
                    if ($('#sectionIndicator_' + currentSectionId).nextAll('.sectionIndicator:hidden').length != 0)
                        $('#itemsIndicator_next').show();
                }

                function iFrameAction(actionId) {
                    var currentZoneId = $(window.parent.document).find('#zoneSelect').val();
                    if (actionId == '1') {
                    		// Invoke a parent action
                    		$(window.parent.document).find('#addMessageBtn').click();
                    } else if (actionId == '4') {
                        $('#actioniFrame_' + actionId).iFramePopup({
                            width: 900,
                            vertOffset: -50,
                            displayOnInit: true,
                            title: client_messages.title.manage_zone_message_priority,
                            src: context + "/content/content_object_zone_priority_edit.form",
                            appliedParams: {
                                zoneId: currentZoneId,
                                tk: "${param.tk}",
                                touchpointSelectionId: getParam('touchpointSelectionId')
                            },
                            closeBtnId: "cancelBtn_button"
                        });
                    }
                }

                $(function () {
                    // Widget is currently hidden: No need to init
                    if (!$('#widgetInterfaceArea').is(':visible') || !$(window.parent.document).find('#widgetContainer').is(':visible'))
                        return;
                
                    // Resolve and display current section (if section not explicitly defined attempt defer to selected zone)
                    var currentSectionId = $(window.parent.document).find('#sectionListSelect').val();
                    if (!(currentSectionId > 0))
                        if ($(window.parent.document).find('#zoneSelect').val() > 0)
                            currentSectionId = $(window.parent.document).find('#zoneSelect option:selected').attr('sectionId');
                    requestWidgetDisplay(currentSectionId, null, null, null);

                    $('.previousItemIcon').parent().click(function () {
                        var $prevItem = $('.sequenceItemIcon_selected').parent().prev('.sectionIndicator');
                        if ($prevItem.length == 0)
                            $prevItem = $("[id^='sectionIndicator']:last");
                        requestWidgetDisplay(parseId($prevItem), null, null, "left");
                        if ($.isFunction(window.parent.sectionClicked)) {
                            window.parent.sectionClicked(parseId($prevItem));
                        }
                    });
                    $('.nextItemIcon').parent().click(function () {
                        var $nextItem = $('.sequenceItemIcon_selected').parent().next('.sectionIndicator');
                        if ($nextItem.length == 0)
                            $nextItem = $("[id^='sectionIndicator']:first");
                        requestWidgetDisplay(parseId($nextItem), null, null, "right");
                        if ($.isFunction(window.parent.sectionClicked)) {
                            window.parent.sectionClicked(parseId($nextItem));
                        }
                    });

                    $('.firstItemIcon').parent().click(function () {
                        var $firstItem = $("[id^='sectionIndicator']:first");
                        requestWidgetDisplay(parseId($firstItem), null, null, "left");
                        if ($.isFunction(window.parent.sectionClicked)) {
                            window.parent.sectionClicked(parseId($firstItem));
                        }
                    });
                    $('.lastItemIcon').parent().click(function () {
                        var $lastItem = $("[id^='sectionIndicator']:last");
                        requestWidgetDisplay(parseId($lastItem), null, null, "right");
                        if ($.isFunction(window.parent.sectionClicked)) {
                            window.parent.sectionClicked(parseId($lastItem));
                        }
                    });

                    $('#fullscreenToggle').each(function () {
                        $(this).iFrameMenu({
                            width: 0,
                            height: $("body", getTopFrame().document).height(),
                            fullWidthIframe: true,
                            vertOffset: -1 * $('.containerLowerFull', getTopFrame().document).offset().top,
                            src: context + "/touchpoints/touchpoint_widget_full.form",
                            appliedParams: {
                                touchpointSelectionId: (getParam('touchpointSelectionId') != "" ? getParam('touchpointSelectionId') : -1),
                                tk: getParam('tk'),
                                documentId: getParam('documentId'),
                                zoneFilter: (getParam('zoneFilter') != "" ? getParam('zoneFilter') : "default"),
                                sectionId: function () {
                                    return $("[id^='touchpointWidgetContainer']:visible .sectionBody").attr('sectionId');
                                }
                            },
                            onMenuOpen: function (oMenu) {
                                $('body', getTopFrame().document).append('<div class="contextMenuPageScreen"></div>').addClass('fullModalOpen');
                                $('.contextMenuPageScreen', getTopFrame().document)
                                    .css({height: ($(getTopFrame().document).height()), opacity: 0})
                                    .fadeTo(1000, 0.6);
                            },
                            onTargetHover: function (oMenu) {
                                if (!$(oMenu.targetEle).hasClass('actionBtn_selected')) {
                                    $(oMenu.targetEle).removeClass('actionBtn');
                                    $(oMenu.targetEle).addClass('actionBtn_hov');
                                }
                            },
                            onTargetHoverOut: function (oMenu) {
                                if (!$(oMenu.targetEle).hasClass('actionBtn_selected')) {
                                    $(oMenu.targetEle).removeClass('actionBtn_hov');
                                    $(oMenu.targetEle).addClass('actionBtn');
                                }
                            }
                        });
                    });

                    $('.sectionIndicator').click(function () {
                        requestWidgetDisplay(parseId($(this)), null, null, null);
                        if ($.isFunction(window.parent.sectionClicked)) {
                            window.parent.sectionClicked(parseId($(this)));
                        }
                    });

                    $('#customModifiersBtn').iFramePopup({
                        width: 820,
                        title: client_messages.title.set_modifiers_and_attributes,
                        src: "touchpoint_variant_template_modifiers_edit.form",
                        appliedParams: {
                            documentId: getParam('documentId'),
                            touchpointSelectionId: (getParam('touchpointSelectionId') != "" ? getParam('touchpointSelectionId') : -1),
                            selectedIds: (getParam('touchpointSelectionId') != "" ? getParam('touchpointSelectionId') : -1),
                            tk: getParam('tk')
                        },
                        closeBtnId: "cancelBtn_button",
                        draggable: false,
                        beforePopupClose: function () {
                            window.parent.updateWidgetDisplay(true);
                        }
                    });

                    $('#alternateTemplateBtn').iFramePopup({
                        width: 550,
                        title: client_messages.title.alternate_templates,
                        src: "touchpoint_variant_template_edit.form",
                        appliedParams: {
                            documentId: getParam('documentId'),
                            touchpointSelectionId: (getParam('touchpointSelectionId') != "" ? getParam('touchpointSelectionId') : -1),
                            selectedIds: (getParam('touchpointSelectionId') != "" ? getParam('touchpointSelectionId') : -1),
                            tk: getParam('tk')
                        },
                        closeBtnId: "cancelBtn_button",
                        draggable: false,
                        beforePopupClose: function () {
                            window.parent.updateWidgetDisplay(true);
                        }
                    });

                    $('#touchpointContainer').styleManager({
                        channel: ${channelId},
                        documentId: getParam('documentId'),
                        apply_global_styles: true
                    });

                    //Disable all context menu entries
                    //$('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');

                });

                // IE11 Hack for FB17890
                $.fn.showEle = function(action) {
                    if ( true ) {
                        $(this).fadeIn(action);
                        $(this).show();
                    } else
                        $(this).show();
                    return $(this);
                };
                $.fn.hideEle = function(action) {
                    if ( true ) {
                        $(this).fadeOut(action);
                        $(this).hide();
                    } else
                        $(this).hide();
                    return $(this);
                };
            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <msgpt:BodyNew type="minimal" cssClass="bg-transparent" cssStyle="background-color: transparent;">

        <c:set var="editPerm" value="false" scope="request"/>
        <msgpt:IfAuthGranted authority="ROLE_MESSAGE_EDIT">
            <c:set var="editPerm" value="true" scope="request"/>
        </msgpt:IfAuthGranted>

        <c:if test="${isVariantEnabled}">
            <input type="hidden" id="isVariantEnabled"/>
        </c:if>

        <c:set var="communicationsView" value="${not empty param.zoneFilter && param.zoneFilter == 'communications'}"/>

        <c:if test="${editPerm && not communicationsView && not empty param.contentActions && param.contentActions == 'true'}">
            <msgpt:ContextMenu name="touchpointWidget">
                <msgpt:ContextMenuEntry name="actioniFrame_1" link="#iFrameAction:1"><fmtSpring:message
                        code="page.label.Add.message"/></msgpt:ContextMenuEntry>
                <msgpt:IfAuthGranted authority="ROLE_PRIORITY_ADMIN">
                    <msgpt:ContextMenuEntry name="actioniFrame_4" link="#iFrameAction:4"><fmtSpring:message
                            code="page.label.message.priority"/></msgpt:ContextMenuEntry>
                </msgpt:IfAuthGranted>
            </msgpt:ContextMenu>
        </c:if>

        <div id="widgetInterfaceArea" style="width: 22rem; position: relative;">

            <div id="touchpointContainer" class="text-center"
                 style="width: 22rem; margin: 0 0 4.5rem; position: relative; z-index: 1; white-space: nowrap;">
                <c:if test="${touchpoint.isPrintTouchpoint && fn:length(sections) > 0}">
                    <div class="bg-white rounded box-shadow tp-widget-card-bg" role="presentation"></div>
                </c:if>
            </div>

            <c:if test="${(touchpoint.isWebTouchpoint || touchpoint.isEmailTouchpoint) && not empty param.contentActions && param.contentActions == 'true'}">
                <div style="position: absolute; right: 23px; top: 3px; float: right; z-index: 2;">
                    <table cellspacing="0" cellpadding="0" border="0">
                        <tr>
                            <td width="98%"/>
                            <c:if test="${hasTemplateModifiers}">
                                <td width="1%">
                                    <div id="customModifiersBtn" class="actionBtn_roundAll detailTip actionBtn"
                                         align="left" style="width: 28px; height: 22px;"
                                         title="|<div class='detailTipText'>${msgpt:getMessage('page.text.applies.template.modifiers')}</div>">
                                        <table border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td>
                                                    <div class="contextBar_codeIcon" style="margin: 4px 7px 0px 6px;"/>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </td>
                            </c:if>
                            <c:if test="${appliesTemplateVariant}">
                                <td width="1%">
                                    <div id="alternateTemplateBtn" class="actionBtn_roundAll detailTip actionBtn"
                                         align="left" style="width: 28px;"
                                         title="|<div class='detailTipText'>${msgpt:getMessage('page.text.applies.alternate.template')}</div>">
                                        <table border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td>
                                                    <div class="hierarchyIconDiv" style="margin: 3px 7px 0px 6px;"/>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </td>
                            </c:if>
                        </tr>
                    </table>
                </div>
            </c:if>
            <div class="touchpointWidgetProcessing justify-content-center align-items-middle">
                <div class="progress-loader progress-loader-md">
                    <i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>
                </div>
            </div>
            <c:if test="${touchpoint.isPrintTouchpoint && fn:length(sections) > 0}">
                <div class="sectionToggleContainer">
                    <ul class="pagination justify-content-center m-0 sectionToggleFrame">
                        <li class="page-item sectionToggleLeftNav">
                            <button class="page-link" aria-label="First" type="button" data-toggle="tooltip"
                                    data-delay='{"show": 500, "hide": 0}' title="First Section">
                                <i class="firstItemIcon far fa-angle-double-left"></i>
                                <span class="sr-only">First</span>
                            </button>
                        </li>
                        <li class="page-item sectionToggleLeftNav">
                            <button class="page-link" aria-label="Previous" type="button" data-toggle="tooltip"
                                    data-delay='{"show": 500, "hide": 0}' title="Previous Section">
                                <i class="previousItemIcon far fa-angle-left"></i>
                                <span class="sr-only">Previous</span>
                            </button>
                        </li>
                        <li id="itemsIndicator_prev" class="page-item disabled">
                                <span class="page-link itemsIndicator">
                                    ...
                                </span>
                        </li>
                        <c:forEach var="currentSection" items="${sections}">
                            <li id="sectionIndicator_${currentSection.id}" class="page-item sectionIndicator">
                                <button class="page-link sequenceItem sequenceItemIcon" type="button"
                                        title="${currentSection.name}" data-toggle="tooltip"
                                        data-delay='{"show": 500, "hide": 0}'>
                                        <span class="sequenceItemText"><c:out
                                                value="${currentSection.sectionOrder}"/></span>
                                </button>
                            </li>
                        </c:forEach>
                        <li id="itemsIndicator_next" class="page-item disabled">
                                <span class="page-link itemsIndicator">
                                    ...
                                </span>
                        </li>
                        <li class="page-item sectionToggleRightNav">
                            <button class="page-link" type="button" aria-label="Next" data-toggle="tooltip"
                                    data-delay='{"show": 500, "hide": 0}' title="Next Section">
                                <i class="nextItemIcon far fa-angle-right"></i>
                                <span class="sr-only">Next</span>
                            </button>
                        </li>
                        <li class="page-item sectionToggleRightNav">
                            <button class="page-link" type="button" aria-label="Last" data-toggle="tooltip"
                                    data-delay='{"show": 500, "hide": 0}' title="Last Section">
                                <i class="lastItemIcon far fa-angle-double-right"></i>
                                <span class="sr-only">Last</span>
                            </button>
                        </li>
                    </ul>
                </div>
            </c:if>

            <c:if test="${not communicationsView}">
                <div id="fullscreenToggle"
                     class="actionSelectMenuField fullscreenToggleBtn actionBtn actionBtn_roundAll actionBtnHighlighted actionBtnSmall detailTip"
                     title="|<div class='detailTipText'>${msgpt:getMessage('page.text.view.fullscreen')}</div>">
                    <i class="fullscreenIcon fa fa-expand-alt"></i>
                </div>
            </c:if>

            <div>
                <input type="button" id="devModeBtn" value="DEV MODE: RELOAD FRAME" style="width: 150px; display: none;"
                       title="Toggle 'devMode' in iFrameMenu init" onclick="javascript:window.location.reload()"/>
            </div>

        </div>

    </msgpt:BodyNew>
</msgpt:Html5>