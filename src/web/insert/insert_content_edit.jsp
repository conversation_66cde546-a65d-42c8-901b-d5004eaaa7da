<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.model.workflow.Workflow"%>
<%@page import="com.prinova.messagepoint.model.SystemPropertyKeys.WebAppSecurity"%>
<%@page import="com.prinova.messagepoint.util.ApplicationUtil"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<c:set var="csrfTokenKey" value="<%=ApplicationUtil.getCSRFTokenParam(request.getParameter(WebAppSecurity.CSRF_TOKEN_KEY))%>" />

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.inserts" viewType="<%= MessagepointHeader.ViewType.EDIT %>">

	<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />

	<msgpt:Script src="includes/javascript/ajaxUpload/ajaxupload.3.6.js" />

	<style type="text/css">
		.interfaceFrame, .enabledInterfaceFrame {
			padding: 15px;
			width: 300px;
			position: relative;
			z-index: 2;
		}
		.enabledInterfaceFrame {
			background-color: #eee;
			border: 1px solid #ccc;
			border-bottom: none;
		}
		.interfaceFrame {
			border: none;
			background-color: transparent;
		}
		#insertImageSelectionDiv {
			background-color: #eee; 
			width: 700px; 
			border: 1px #ccc solid; 
			position: relative; 
			top: -1px; 
			z-index: 1;
			font-size: 13px;
		}
		
		.image {
			border: 1px solid #ccc;
		}
		.imageHov {
			border: 1px solid #d81a1a;
		}
		.imageSelected {
			border: 1px solid #12c31b;
		}
		

		.contentDataContainer {
			padding: 0px;
		}
	</style>

    <msgpt:Script>
        <script>
            function openImageInterface(target) {
                closeImageInterface();

                if ($('.image').length > 0 || $('.imageSelected').length > 0)
                    setImageSelectionDisplay('viewImages');
                else
                    setImageSelectionDisplay('noImages');

                $('#targetSection').val(target);
                $('#imageInfoType').html(target);
                $('#insertDisplayDiv_'+target).attr('class','enabledInterfaceFrame');
                $('#setBackgroundBtn_'+target).hide();
                $('#insertImageSelectionDiv').show();

                common.refreshParentIframeHeight();
            }

            function closeImageInterface() {
                $("[id^='insertDisplayDiv']").attr('class','interfaceFrame');
                $("[id^='setBackgroundBtn']").show();
                $('#insertImageSelectionDiv').hide();
                common.refreshParentIframeHeight();
            }

            function toogleUploadDisplay(divEle) {
                if( $('#uploadInterfaceDiv').is(':visible') ) {
                    $(divEle).find('#importExpansionArrow').attr('class','expansionArrow_closed');
                    $('#uploadInterfaceDiv').hide();
                } else {
                    $('#uploadInterfaceDiv').showEle('normal');
                    $(divEle).find('#importExpansionArrow').attr('class','expansionArrow_open');
                }
            }

            // Image Setting
            function setImage() {
                var selectedImage = $('.imageSelected').attr('src');
                selectedImage = selectedImage.substring(selectedImage.indexOf('=')+1);
                var sectionId = $('#targetSection').val();
                closeImageInterface();
                $.ajax({
                    type: "GET",
                    url: context+"/uploadedImageEvent.form?action=select&type=insert&section="+sectionId+"&resource="+selectedImage,
                    dataType: "xml",
                    success: function(data) {
                        setImageBinding(data);
                    }
                });
            }

            function setImageBinding(data) {
                var filename = data.getElementsByTagName('token');
                var sectionId = $(filename[0]).attr('sectionId');
                var filePath = filename[0].firstChild.nodeValue;
                var timeStamp = $(filename[0]).attr('timeStamp'); // Use timestamp to prevent image request caching
                var newImgSrc = context+'/download/image.form?resource='+escapeURL(filePath)+'&timeStamp='+timeStamp;
                $('#newImgBinding_'+sectionId).val(filePath);
                $('#currentImg_'+sectionId).attr('src',newImgSrc);
            }

            // Image Retrieval/Management
            function requestExistingContent() {
                $.ajax({
                    type: "GET",
                    url: context+"/uploadedImageEvent.form?action=retrieve&type=insert&cacheStamp="+cacheStamp,
                    dataType: "xml",
                    success: function(data) {
                        updateImageDisplay(data);
                    }
                });
            }

            function updateImageDisplay(imageData) {
                var filenames = imageData.getElementsByTagName('token');

                if (filenames.length < 1) {
                    setImportInterface('noImages');
                    return;
                }

                var imagesTable = '<table class="innerContentTable" cellspacing="0" cellpadding="0" border="0"><tr>';
                for (var i=0; i<filenames.length; i++) {
                    currentFile = filenames[i].firstChild.nodeValue;
                    imagesTable += 	'<td style="padding: 10px 5px;">' +
                        '<img class="image" src="'+context+'/download/image.form?resource='+escapeURL(currentFile)+'&timeStamp='+$(filenames[i]).attr('timeStamp')+'" ' +
                        'onclick="imageSelect(this)" onmouseover="imageAction(\'hov\',this)" onmouseout="imageAction(\'out\',this)" />' +
                        '</td>';
                }
                imagesTable += '</tr></table>';
                $('#imageDisplayDiv').html(imagesTable);

                setImageSelectionDisplay('viewImages')
            }

            function escapeURL(path) {
                var bckSlashIndex = path.lastIndexOf('\\');
                var fwdSlashIndex = path.lastIndexOf('/');
                var splitIndex = Math.max(bckSlashIndex, fwdSlashIndex);
                var basePath = path.substring(0, splitIndex+1);
                var encodedFilename = encodeURIComponent( path.substring(splitIndex+1, path.length) );
                return basePath+encodedFilename;
            }

            function setImageSelectionDisplay(state) {
                if (state == 'noImages') {
                    $('#imageLoadAnim').hide();
                    $('#noImagesDiv').show();
                    setImportInterface('show');
                } else if (state == 'loading') {
                    $('#noImagesDiv').hide();
                    $('#imageDisplayDiv').hide();
                    $('#imageLoadAnim').show();
                } else if (state == 'viewImages') {
                    setImportInterface('hide');
                    $('#noImagesDiv').hide();
                    $('#imageLoadAnim').hide();
                    $('#imageDisplayDiv').show();
                }
            }

            function setImportInterface(action) {
                if (action == 'show') {
                    $('#importExpansionArrow').attr('class','expansionArrow_open');
                    $('#uploadInterfaceDiv').showEle('normal');
                } else if (action == 'hide') {
                    $('#importExpansionArrow').attr('class','expansionArrow_closed');
                    $('#uploadInterfaceDiv').hide();
                }
            }

            function imageAction(action, imageEle) {
                if (!$(imageEle).hasClass('imageSelected')) {
                    if (action == 'hov') {
                        $(imageEle).attr('class','imageHov');
                    } else if (action == 'out') {
                        $(imageEle).attr('class','image');
                    }
                }
            }

            function imageSelect(imageEle) {
                $('.imageSelected').attr('class','image');
                $(imageEle).attr('class','imageSelected');
                $('#continueBtnDisabled').hide();$('#continueBtnEnabled').show();
            }

            // Async Image Upload
            var imageFileUpload;
            $( function() {

                setPrimaryBtn('save');

                imageFileUpload = new AjaxUpload('#imageFileSelect', {
                    action: context+'/uploadedImageEvent.form?action=load&type=insert${csrfTokenKey}',
                    name: 'uploadedFile',
                    autoSubmit: false,
                    onComplete : function(file, response){
                        $('#uploadingFileDiv').hide();
                        $('#uploadedImageDiv').show();
                        updateImageDisplay(response);
                    },
                    onSubmit : function (file, ext) {
                        $('#uploadedImageDiv').hide();
                        $('#imageUploadError').hide();
                        if (ext && /^(pdf|jpg|jpeg|png|gif)$/.test(ext)){
                            setImageSelectionDisplay('loading');
                            // Set extension type
                            this.setData({
                                'fileExt': ext
                            });
                            $('#uploadingFileDiv').show();
                        } else {
                            $('#imageUploadError').html( fmtClientMessage(client_messages.text.valid_file_upload_types, 'PDF, JPG, PNG or GIF') ).show(); // extension is not allowed
                            return false; // cancel upload
                        }
                    },
                    onChange : function(file, ext) {
                        $('#imageUploadError').hide();
                        $('#selectedImage').html('');
                        if (ext && /^(pdf|jpg|jpeg|png|gif)$/.test(ext)){
                            $('#selectedImage').html(file);
                        } else {
                            $('#imageUploadError').html( fmtClientMessage(client_messages.text.valid_file_upload_types, 'PDF, JPG, PNG or GIF') ).show(); // extension is not allowed
                        }
                    }
                });

                requestExistingContent();

                common.refreshParentIframeHeight();
            });

            function uploadImage() {
                imageFileUpload.submit();
            }
        </script>
    </msgpt:Script>

</msgpt:HeaderNew>

<msgpt:BodyNew theme="minimal" type="iframe">
	<%--@elvariable id="command" type=""--%>
	<form:form modelAttribute="command" method="post" enctype="multipart/form-data">
	
		<div id="popupHeaderTitle" style="display: none;">
			<span class="titleText">
				<c:out value='${msgpt:getMessage("page.label.edit")} ${msgpt:getMessage("page.label.insert")} ${command.name != null ? ":":""} ${command.name} ${!isNewInsert ? status : ""}' />
			</span>
		</div>
		
		<msgpt:WorkflowTab parameter="insertId" type="<%= Workflow.INSERT_WORKFLOW_ID %>" edit="true" btnAlign="right">
			<div class="contentTableIframe">
				<form:errors path="*">
					<msgpt:Information errorMsgs="${messages}" type="error" />
				</form:errors>
	
				<!-- Front/Back Image Interface Frame -->
				<div align="center" style="padding: 15px 0px 0px 0px;">
					<table width="100%" cellspacing="0" cellpadding="0" border="0">
						<tr>
							<td style="padding: 0px; vertical-align: middle;" align="right">
								<form:hidden path="frontNewImageLocation" id="newImgBinding_front" />
								<div id="insertDisplayDiv_front" align="center" class="interfaceFrame">
									<div class="fullLineLabel" style="font-size: 14px;"><fmtSpring:message code="page.label.FRONT"/></div>
									<div style="height: 275px; vertical-align: middle;">
										<msgpt:ShadowFrame>
											<c:choose>
												<c:when test="${not empty command.frontOriginalImageLocation}">
													<img id="currentImg_front" src="${contextPath}/download/image.form?resource=${msgpt:getResourceToken().add('file', command.frontOriginalImageLocation).value}" style=" border: 1px #ccc solid; max-width: 300px; max-height: 250px;" />
												</c:when>
												<c:otherwise>
													<img id="currentImg_front" src="../includes/themes/commonimages/inserts/insert_watermark_front.gif" style=" border: 1px #ccc solid; max-width: 300px; max-height: 250px;" />
												</c:otherwise>
											</c:choose>
										</msgpt:ShadowFrame>
									</div>
									<div style="height: 20px;">
										<msgpt:SlimButton id="setBackgroundBtn_front" URL="javascript:openImageInterface('front');" label="page.label.set.background" />
									</div>
								</div>
							</td>
							<td style="padding: 0px; vertical-align: middle;" align="left">
								<form:hidden path="backNewImageLocation" id="newImgBinding_back" />
								<div id="insertDisplayDiv_back" align="center" class="interfaceFrame">
									<div class="fullLineLabel" style="font-size: 14px;"><fmtSpring:message code="page.label.BACK"/></div>
									<div style="height: 275px; vertical-align: middle;">
										<msgpt:ShadowFrame>
											<c:choose>
												<c:when test="${not empty command.backOriginalImageLocation}">
													<img id="currentImg_back" src="${contextPath}/download/image.form?resource=${msgpt:getResourceToken().add('file', command.backOriginalImageLocation).value}" style=" border: 1px #bbb solid; max-width: 300px; max-height: 250px;" />
												</c:when>
												<c:otherwise>
													<img id="currentImg_back" src="../includes/themes/commonimages/inserts/insert_watermark_back.gif" style=" border: 1px #bbb solid; max-width: 300px; max-height: 250px;" />
												</c:otherwise>
											</c:choose>
										</msgpt:ShadowFrame>
									</div>
									<div style="height: 20px;">
										<msgpt:SlimButton id="setBackgroundBtn_back" URL="javascript:openImageInterface('back');" label="page.label.set.background" />
									</div>
								</div>
							</td>
						</tr>
						<tr><td colspan="2" align="center">
							<input type="hidden" id="targetSection" value="" />
							<div id="insertImageSelectionDiv" style="display: none;">
								<!-- Image Selection -->
								<div id="insertImageInfoFrame" style="padding: 8px 15px 4px 15px;">
									<div align="left">
										<div class="fullLineLabel" style="float: left;"><fmtSpring:message code="page.label.IMAGE.SELECTION"/>:</div>
										&nbsp;&nbsp;&nbsp;<fmtSpring:message code="page.label.insert"/>&nbsp;<span id="imageInfoType"><fmtSpring:message code="page.label.TYPE"/></span>
									</div>
								</div>
								<div id="insertImageImageFileSelect" style="padding: 2px 8px 6px 8px;" align="center">
									<div style="background-color: #fff; padding: 4px 5px; border: 1px solid #ccc;">
										<div id="imageLoadAnim" align="center" style="padding: 25px;"><img src="../includes/themes/commonimages/tree/loadingAnimation.gif" /></div>
										<div id="imageDisplayDiv" align="center" style="width: 640px; display: none; overflow-x: scroll;"><fmtSpring:message code="page.label.IMAGES.DISPLAYED.HERE"/></div>
										<div id="noImagesDiv" align="left" style="display: none; margin: 5px; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
											<fmtSpring:message code="page.text.no.images.select.file.to.upload"/>
										</div>	
										<div align="center" style="width: 300px;">
											<div style="padding: 4px 5px 8px 5px;">
												<div style="margin: 2px; cursor: default;" align="center" onclick="toogleUploadDisplay(this)">
													<table width="100%" class="innerContentTable" cellspacing="0" cellpadding="0" border="0"><tr>
														<td style="padding: 0px; white-space: nowrap;" width="1%"><div class="fullLineLabel"><fmtSpring:message code="page.label.IMPORT.IMAGES"/></div></td>
														<td style="padding: 0px; vertical-align: top;" align="left"><div id="importExpansionArrow" class="expansionArrow_open" style="width: 15px; height: 10px; font-size: 1px; margin-left: 8px;">&nbsp;</div></td>
													</tr></table>
												</div>
											</div>
											<div id="uploadInterfaceDiv">
												<div id="imageUploadError" class="errorMsg" style="display: none; padding-left: 5px;" align="left">&nbsp;</div>
												<div align="left" id="uploadingFileDiv" style="display: none; padding: 5px">
													<table width="100%" class="innerContentTable" cellspacing="0" cellpadding="0" border="0"><tr>
														<td style="padding: 0px; white-space: nowrap; color: #555;" width="1%"><fmtSpring:message code="page.label.importing.image"/></td>
														<td style="padding: 0px; padding-left: 8px;" align="left"><div class="loadingIconDiv">&nbsp;</div></td>
													</tr></table>			
												</div>
												<div align="left" id="uploadedImageDiv" style="display: none; padding: 5px"><fmtSpring:message code="page.label.import.complete"/></div>
												<table width="100%" class="innerContentTable" cellspacing="0" cellpadding="0" border="0">
													<tr id="imageFileSelect">
														<td style="padding: 0px; vertical-align: middle;"><div style="width: 180px; height: 16px; overflow:hidden; border: 1px solid #bbb;"><div id="selectedImage" style="overflow:hidden; font-size: 10px; padding: 3px;">&nbsp;</div></div></td>
														<td style="padding: 0px; vertical-align: middle;" width="33%"><div style="width: 80px;"><msgpt:SlimButton URL="#" label="page.label.browse" /></div></td>
													</tr>
													<tr><td colspan="2" align="center"><div style="padding: 8px;"><msgpt:SlimButton URL="javascript:uploadImage();" label="page.label.import" /></div></td></tr>
												</table>
											</div>
										</div>
									</div>
								</div>
								<div align="center" style="padding-bottom: 14px; padding-top: 6px;">
									<span id="continueBtnDisabled"><msgpt:SlimButton URL="#" label="page.label.select" disabled="true" /></span>
									<span id="continueBtnEnabled" style="display: none;"><msgpt:SlimButton URL="javascript:setImage();" label="page.label.select" /></span>
									<msgpt:SlimButton URL="javascript:closeImageInterface();" label="page.label.close" />								
								</div>
							</div>
	
						</td></tr>
					</table>
				</div>
			</div>
		</msgpt:WorkflowTab>
	</form:form>
</msgpt:BodyNew>

</msgpt:Html5>