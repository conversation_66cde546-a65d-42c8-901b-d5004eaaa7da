
<%@page import="com.prinova.messagepoint.model.workflow.Workflow"%>
<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<c:set var="insertSchedule" value="${command.insertSchedule}" />
<c:set var="isOverview" value="true" />

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.inserts" viewType="<%= MessagepointHeader.ViewType.EDIT %>" extendedScripts="true">

	<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/jsTree_1.0rc2/themes/default/style.css" />

	<msgpt:Script src="includes/javascript/jQueryPlugins/jsTree_1.0rc2/jquery.jstree.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/scrollTo/jquery.scrollTo-2.1.2.min.js" />

    <msgpt:Script>
        <script>

            // *************** DATA VALUE INIT FUNCTIONS START ***************
            function nodeDataValuesInit(type) {
                if (type == 'view') {
                    if ( !$('#viewDataValueTreeDiv').hasClass('treeInit') ) {
                        var dataValueCollectionId = $('#'+type+'DataValueTreeDiv').attr('dataValueCollection');
                        $('#viewDataValueTree').hide();
                        $('#removeDataValueTree').hide();
                        initViewDataValuesTree();
                    }
                    $(specId).find('#actionPopupStandardButtons').hide();
                    $(specId).find('#actionPopupCloseButton').show();
                    $(specId).find('#actionPopupViewDataValues').show();
                }
            }

            function initViewDataValuesTree() {

                var dataValueCollectionId = $('#viewDataValueTreeDiv').attr('dataValueCollection');
                $("#viewDataValueTreeDiv")
                    .jstree({
                        "plugins" 	: [ "themes", "json_data", "ui" ],
                        "themes" 	: { "theme" : "default", "dots" : false, "icons" : false },
                        "ui"		: { "select_limit" : 0 },
                        "json_data"	: {
                            "ajax" : {
                                "url" 	: context+"/getCollectionDataValues.form",
                                "data"	:	function(n) {
                                    if (!n.attr)
                                        return {
                                            "collectionId" 	: dataValueCollectionId,
                                            "verboseData" 	: true,
                                            "cacheStamp" 	: cacheStamp
                                        }
                                    else
                                        return {
                                            "collectionId" 	: dataValueCollectionId,
                                            "verboseData" 	: true,
                                            "cacheStamp" 	: cacheStamp,
                                            "fullValue"		: $(n).attr('id'),
                                            "nodeLevel"		: $(n).attr('level')
                                        }
                                }
                            }
                        }
                    });

                $('#viewDataValueTreeDiv').addClass('treeInit');
                $('#viewDataValueTree').fadeIn('normal');

            }
            // *************** DATA VALUE INIT FUNCTIONS END ***************

            // Init Javascript
            $( function() {
                // Style action bar elements
                $("input:button").styleActionElement();
                $("#actionMenu").styleActionElement();
                $("#dateFilterSelect").styleActionElement();

                $('#timingListDiv').find('.insertOptionalIcon,.insertMandatoryIcon,.insertNonSelectableIcon').each(function(){
                    var insertId = $(this).attr('insertId');
                    $(this).closest('tr')
                        .mouseover(function(){
                            $.binAssignmentInterface.ref('#insertBinLayout').binHighlightAction('on',insertId);
                        })
                        .mouseout(function(){
                            $.binAssignmentInterface.ref('#insertBinLayout').binHighlightAction('off',insertId);
                        })

                });
                $('#timingListDiv').find('.insertOptionalIcon,.insertMandatoryIcon,.insertNonSelectableIcon').insertImagePopup({marginLeft: '80px', type: 'verbose'});

                initViewDataValuesTree();

                initNavContainer();
            });

        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:BodyNew theme="minimal" type="iframe">

	<div id="popupHeaderTitle" style="display: none;">
		<span class="titleText">
			<c:out value='${msgpt:getMessage("page.label.view")} ${msgpt:getMessage("page.label.insert.schedule")} ${insertSchedule.name != null ? ":":""} ${insertSchedule.name} [${insertSchedule.statusDisplay}]' />
		</span>
	</div>
				
	<form:form method="post" modelAttribute="command" cssStyle="padding: 0px; margin: 0px;">
		<form:errors path="*">
			<msgpt:Information errorMsgs="${messages}" type="error" />
		</form:errors>
				
		<jsp:include page="insert_schedule_view_action_bar.jsp" />

		<div class="contentTableIframe">
			<table width="100%" cellspacing="0" cellpadding="0" border="0">
			<tr>
				<td class="summaryPanel" align="left" style="padding: 0px; background-color: #efefef; vertical-align: top;">

					<div id="summaryPanel">
						
						<div id="navContainer"></div>

					</div>
				</td>
				<td style="vertical-align: top;">
				
					<div class="propertiesContainer backgroundTile_10p" style="height: 630px; overflow:auto; padding: 18px 32px; border-left: 1px solid #eee;">
						<c:if test="${insertSchedule.defaultSchedule}">
							<msgpt:DataTable style="width: 550px; margin-bottom: 0px;" labelWidths="1%">
								<!-- Default Schedule -->
								<msgpt:TableItem label="">
									<div class="fullLineLabel">* <fmtSpring:message code="page.text.DEFAULT.SCHEDULE.LAYOUT"/></div><br>
								</msgpt:TableItem>
							</msgpt:DataTable>
						</c:if>
						
						<!-- DETAILS -->
						<msgpt:DataTable style="width: 550px;" labelPosition="top" multiColumn="true">
							<msgpt:TableHeader label="page.label.details" 
								canEdit="${canUpdate}" 
								editPath="insert_schedule_overview_edit.form?insertSchedId=${insertSchedule.id}${isSetup ? '&isScheduleSetup=true' : ''}&tk=${param.tk}" />
							<!-- Description -->
							<msgpt:TableItem label="page.label.description">
						        <c:choose>
						            <c:when test="${not empty insertSchedule.description}">
										<c:out value="${insertSchedule.description}" />&nbsp;
						            </c:when>
						            <c:otherwise>
						                <fmtSpring:message code="page.label.none" />
						            </c:otherwise>
						        </c:choose>
							</msgpt:TableItem>	
							<!-- Schedule ID -->
							<msgpt:TableItem label="page.label.schedule.ID">
						        <c:choose>
						            <c:when test="${not empty insertSchedule.scheduleId}">
										<c:out value="${insertSchedule.scheduleId}" />&nbsp;
						            </c:when>
						            <c:otherwise>
						                <fmtSpring:message code="page.label.none" />
						            </c:otherwise>
						        </c:choose>
							</msgpt:TableItem>
							<!--  Date Range -->
							<msgpt:TableItem label="page.label.date.range">
								<fmtJSTL:formatDate value="${insertSchedule.startDate}" pattern="${dateFormat}" />&nbsp;~&nbsp;
								<c:choose>
									<c:when test="${not empty insertSchedule.endDate}">
										<fmtJSTL:formatDate value="${insertSchedule.endDate}" pattern="${dateFormat}" />
									</c:when>
									<c:otherwise>
										<fmtSpring:message code="page.label.indefinite"/>
									</c:otherwise>
								</c:choose>
							</msgpt:TableItem>
							<!-- Touchpoint -->
							<msgpt:TableItem label="page.label.touchpoint">
								<div class="blockItem" style="padding-top: 0px; padding-bottom: 0px; background-color: #f5f5f5;">
                                    <div style="display: inline-block; padding: 4px;">
                                        <msgpt:TxtFmt maxLength="50">
                                            <c:out value="${insertSchedule.scheduleCollection.document.name}" />
                                        </msgpt:TxtFmt>
                                    </div>
                                </div>
							</msgpt:TableItem>
							<!-- Keywords -->
							<msgpt:TableItem label="page.label.keywords">
						        <c:choose>
						            <c:when test="${not empty insertSchedule.keywords}">
										<c:out value="${insertSchedule.keywords}" />&nbsp;
						            </c:when>
						            <c:otherwise>
						                <fmtSpring:message code="page.label.none" />
						            </c:otherwise>
						        </c:choose>
							</msgpt:TableItem>
							<c:if test="${not empty command.assignedToUser.name}">
								<!-- Assigned To -->
								<msgpt:TableItem label="page.label.assigned.to">
									<c:out value="${command.assignedToUser.name}" />&nbsp;
								</msgpt:TableItem>
							</c:if>
						</msgpt:DataTable>
											
						<c:if test="${!isSetup}">
							<!-- Bin Assignments -->
							<msgpt:DataTable style="width: 550px;" labelPosition="top">
								<msgpt:TableHeader label="${msgpt:getMessage('page.label.bin.assignments')}" 
									canEdit="${canUpdate}" 
									editPath="insert_schedule_bin_assignment_edit.form?insertSchedId=${insertSchedule.id}&tk=${param.tk}" />
								<msgpt:TableItem>
									<table width="100%" cellspacing="0" cellpadding="0" border="0" style="width: 460px; margin-top: 8px;">
										<tr>
											<td width="69%" style="padding: 0px; vertical-align: middle;"><span style="padding-left: 15px;">
												(${insertSchedule.numberOfBins} <fmtSpring:message code="page.text.bins"/>)
											</span></td>
										<tr>
										<c:if test="${not empty insertSchedule.optionalBinAssignmentsSorted}">
											<tr><td align="left" colspan="3" style="font-size: 12px;">
												<div id="optionalInsertsDiv" style="padding: 3px 0px 0px 10px; width: 120px;">
													<c:out value="${fn:length(insertSchedule.optionalBinAssignmentsSorted)}"/> 
													<a href="#"><fmtSpring:message code="page.label.optional.inserts"/></a>
												</div>
												<div style="position: relative; z-index: 1;">
													<div id="optionalInsertsPopup" align="left" style="display: none; width: 225px; padding: 8px 10px; border: 1px solid #848484; background-color: #fff; overflow-x: hidden; position: absolute; top: -15px; left: 120px; z-index: 1;">
														<div style="padding: 2px;"><b><fmtSpring:message code="page.label.optional.inserts"/></b> <fmtSpring:message code="page.text.in.priority.order"/></div>
														<c:forEach var="currentBinAssignment" items="${insertSchedule.optionalBinAssignmentsSorted}">
															<div style="padding: 2px 15px;"><c:out value="${currentBinAssignment.insert.name}" /></div>
														</c:forEach>
													</div>
												</div>
											</td></tr>
										</c:if>
										<tr><td align="center" colspan="3">
											<div style="margin-top: 5px;">
												<input type="hidden" id="insertBinLayout" value="${command.binAvailabilities}" />
												<input type="hidden" id="insertDeliveryStatus" value="${command.binOptionalities}" />
												<div id="binAssignmentMsg_noBins" style="display:none;">
													<fmtSpring:message code="page.text.no.bins.specified"/>
												</div>
											</div>
											<div align="center" style="width: 400px; font-size: 10px;">		
												<fmtSpring:message code="page.text.double.click.insert.to.jump.to.page.view"/>
											</div>
										</td></tr>
									</table>
								</msgpt:TableItem>
							</msgpt:DataTable>
						</c:if>
						
						<c:if test="${setupPerm}">
							<!-- Reservations -->
							<msgpt:DataTable style="width: 550px;" labelPosition="top">
								<msgpt:TableHeader label="${msgpt:getMessage('page.label.bin.reservations')}" 
									canEdit="${canUpdate}" 
									editPath="insert_schedule_bin_assignment_edit.form?insertSchedId=${insertSchedule.id}${isSetup ? '&isScheduleSetup=true' : ''}&isReservation=true&tk=${param.tk}" />
								<msgpt:TableItem>
									<table width="100%" cellspacing="0" cellpadding="0" border="0" style="width: 460px;">
										<tr>
											<td width="69%" style="padding: 0px; vertical-align: middle;"><span style="padding-left: 15px;">
												<c:if test="${command.insertSchedule.numberOfBinReservations > 0}">
													(<c:out value="${command.insertSchedule.numberOfBinReservations}" /> 
													<fmtSpring:message code="${command.insertSchedule.numberOfBinReservations > 1 ? 'page.text.reservations' : 'page.text.reservation'}"/>)
												</c:if>
											</span></td>
										<tr>
										<c:if test="${isSetup}">
											<tr><td align="center" colspan="3">
												<div style="margin-top: 5px;">
													<input type="hidden" id="insertBinLayout" value="${command.binAvailabilities}" />
													<input type="hidden" id="insertDeliveryStatus" value="${command.binOptionalities}" />
													<div id="binAssignmentMsg_noBins" style="display:none;">
														<fmtSpring:message code="page.text.no.bins.specified"/>
													</div>
												</div>
												<div align="center" style="width: 400px; font-size: 10px;">		
													<fmtSpring:message code="page.text.double.click.insert.to.jump.to.page.view"/>
												</div>
											</td></tr>
										</c:if>
									</table>
								</msgpt:TableItem>
							</msgpt:DataTable>
						</c:if>
							
						<!-- Insert Timing -->
						<msgpt:DataTable style="width: 550px;" labelPosition="top">
							<msgpt:TableHeader label="${msgpt:getMessage('page.label.insert.timing')}" 
								canEdit="${canUpdate}" 
								editPath="insert_schedule_insert_timing_edit.form?insertSchedId=${insertSchedule.id}${isSetup ? '&isScheduleSetup=true' : ''}&tk=${param.tk}" />
							<msgpt:TableItem>
								<table width="100%" cellspacing="0" cellpadding="0" border="0" style="width: 460px;">
									<tr><td align="center" colspan="3" style="font-size: 12px;">
										<c:choose>
											<c:when test="${command.insertSchedule.hasTiming}">
												<div id="timingListDiv" align="left" style="background-color: #fff; border: #ddd solid 1px; padding: 5px 0px; height: 63px; width: 435px; overflow: auto;">
													<!-- Insert Timing -->
													<table class="innerContentTable" cellspacing="0" cellpadding="0" border="0">
														<!-- Inserts -->
														<c:forEach var="currentBinAssignment" items="${command.insertSchedule.binAssignments}" varStatus="insertStat">
															<c:if test="${not empty currentBinAssignment.startDate}">
																<tr>
																	<td style="vertical-align: middle; padding: 1px; padding-left: 10px;" width="12%">
																		<c:if test="${currentBinAssignment.insert.deliveryTypeId=='1'}">
																			<c:set var="iconClass" value="insertOptionalIcon" />
																		</c:if>
																		<c:if test="${currentBinAssignment.insert.deliveryTypeId=='2'}">
																			<c:set var="iconClass" value="insertMandatoryIcon" />
																		</c:if>
																		<c:if test="${currentBinAssignment.insert.deliveryTypeId=='3'}">
																			<c:set var="iconClass" value="insertNonSelectableIcon" />
																		</c:if>
																		<div class="${iconClass}" insertId="${currentBinAssignment.insert.id}">&nbsp;</div>
																	</td>
																	<td style="vertical-align: middle; padding: 1px; padding-left: 7px;" width="30%">
																		<c:out value="${currentBinAssignment.insert.name}" />
																	</td>
																	<td style="vertical-align: middle; padding: 1px;">
																		<fmtJSTL:formatDate value="${currentBinAssignment.startDate}" pattern="${dateFormat}" />&nbsp;~&nbsp;
																		<c:choose>
																			<c:when test="${not empty currentBinAssignment.endDate}">
																				<fmtJSTL:formatDate value="${currentBinAssignment.endDate}" pattern="${dateFormat}" />
																			</c:when>
																			<c:otherwise>
																				<fmtSpring:message code="page.label.indefinite"/>
																			</c:otherwise>
																		</c:choose>
																	</td>
																</tr>
															</c:if>
														</c:forEach>
													</table>
												</div>
											</c:when>
											<c:otherwise>
												<div align="left" style="margin: 5px; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
													<fmtSpring:message code="page.text.no.timing.for.inserts.in.schedule"/>
												</div>
											</c:otherwise>
										</c:choose>
									</td></tr>
								</table>
							</msgpt:TableItem>
						</msgpt:DataTable>
							
						<!-- Rate Sheets -->
						<msgpt:DataTable style="width: 550px;" labelPosition="top">
							<msgpt:TableHeader label="${msgpt:getMessage('page.label.rate.sheets')}" 
								canEdit="${canUpdate}" 
								editPath="insert_schedule_rate_schedule_edit.form?insertSchedId=${insertSchedule.id}${isSetup ? '&isScheduleSetup=true' : ''}&tk=${param.tk}" />
							<msgpt:TableItem>
								<table width="100%" cellspacing="0" cellpadding="0" border="0" style="width: 460px;">
									<tr><td align="left" colspan="3" style="font-size: 12px;">
										<div style="margin: 5px;">
											<c:choose>
												<c:when test="${not empty command.rateSchedules}">
													<table class="innerContentTable" cellspacing="0" cellpadding="0" border="0">
														<c:forEach var="currentRateSchedule" items="${command.rateSchedules}" varStatus="rateSchedStat">
															<tr>
																<td style="padding: 1px;"><fmtSpring:message code="page.label.use"/> <i><c:out value="${currentRateSchedule.name}" /></i></td>
																<td style="padding: 1px 8px;">
																	<c:choose>
																		<c:when test="${fn:length(command.rateSchedules) != rateSchedStat.count}">
																			<fmtSpring:message code="page.text.when.greater.than"/> <b><c:out value="${command.numberOfSheets[rateSchedStat.index]}"/></b> <fmtSpring:message code="page.label.sheets"/>
																		</c:when>
																		<c:otherwise>
																			<c:if test="${fn:length(command.rateSchedules) != 1}">
																				<fmtSpring:message code="page.text.otherwise"/>
																			</c:if>
																		</c:otherwise>
																	</c:choose>
																</td>
															</tr>
														</c:forEach>
													</table>
												</c:when>
												<c:otherwise>
													<div style="margin: 5px; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
														<fmtSpring:message code="page.text.no.rate.sheets.for.insert.schedule"/>
													</div>
												</c:otherwise>
											</c:choose>
										</div>
									</td></tr>
								</table>
							</msgpt:TableItem>
						</msgpt:DataTable>
						
						<!-- Selectors -->
						<msgpt:DataTable style="width: 550px;" labelPosition="top">
							<msgpt:TableHeader label="page.label.selectors" 
								canEdit="${canUpdate}" 
								editPath="insert_schedule_selector_edit.form?insertSchedId=${insertSchedule.id}${isSetup ? '&isScheduleSetup=true' : ''}&tk=${param.tk}" />
							<msgpt:TableItem>
								<table width="100%" cellspacing="0" cellpadding="0" border="0" style="width: 460px;">
									<tr>
										<td width="69%" style="padding: 0px; vertical-align: middle;"><span style="padding-left: 15px;">
									        <c:choose>
									            <c:when test="${not empty insertSchedule.parameterGroupInstanceCollection}">
													(<c:out value="${insertSchedule.parameterGroupInstanceCollection.parameterGroup.name}" />)
									            </c:when>
									            <c:otherwise>
									                (<fmtSpring:message code="page.label.none" />)
									            </c:otherwise>
									        </c:choose>
										</span></td>
										<td width="12%" align="right" style="padding: 10px 0px; vertical-align: middle;">
											<c:if test="${not empty insertSchedule.parameterGroupInstanceCollection}">
												<div id="viewButton_dataValues">
													<input title="${msgpt:getMessage('page.label.VIEW')}" type="button" id="dataValuesViewBtn" onclick="actionSelected('8')" style="display: none;" />
												</div>
											</c:if>
										</td>
									<tr>
								</table>
							</msgpt:TableItem>
						</msgpt:DataTable>
					</div>
				</td>
			</tr></table>
		</div>

	</form:form>
</msgpt:BodyNew>
</msgpt:Html5>