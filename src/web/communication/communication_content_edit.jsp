<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.model.admin.ContentType"%>
<%@page import="com.prinova.messagepoint.model.Document"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<c:set var="communication" value="${command.communication}" />

<c:set var="isEmbedded" value="${not empty param.embedded && param.embedded == 'true'}" />
<c:set var="isReturnToList" value="${not empty param.return_to_list && param.return_to_list == 'true'}" />

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">


	<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />
	
	<!-- Content editor: Async file upload JS -->
	<msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.image-gallery.min.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.iframe-transport.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/fileUpload/js/jquery.fileupload.js" />
	
	<msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js" />
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css" />
	
	<msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js" />

	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css" />
	
	<msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js" />
	
	<msgpt:Script src="includes/javascript/jQueryPlugins/contentEditor/jquery.contentEditor.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/fastNavigation/jquery.fastNavigation.js"/>
	<msgpt:Script src="includes/javascript/jQueryPlugins/sharedTextFastEdit/jquery.sharedTextFastEdit.js"/>
	<msgpt:Script src="includes/javascript/jQueryPlugins/smartTextContentViewer/jquery.smartTextContentViewer.js" />
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/contentEditor/contentEditor.css" />
	<msgpt:Script src="includes/javascript/pdfObject/pdfobject.min.js" />
		
	<msgpt:Script src="includes/javascript/jQueryPlugins/hoverIntent/jquery.hoverIntent.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.editorActions.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.varTagAttrManager.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.smartTextTagAttrManager.js" />

	<msgpt:Script src="includes/javascript/pdfObject/pdfobject.min.js" />
	
	<msgpt:Script src="includes/javascript/jQueryPlugins/scrollTo/jquery.scrollTo-2.1.2.min.js" />
	
	<msgpt:Script src="includes/javascript/handlebars/handlebars-v4.7.8.js" />
	
	<style>	
		.contentEditor_OuterContentContainer {
			background-color: transparent;
			box-shadow: none;
			padding: 0px;
			border: none;
		}
		.contentEditor_InnerContentContainer {
			padding-top: 0px;
			padding-left: 0px;
			background-color: transparent;
		}
		.contentEditor_DisplayContainer {
			padding: 0px;
		}
		.stageContainer .tableOutline TD.tableContentCol {
			padding: 0px;
		}
		
		.zoneIndicator {
			text-align: left; 
			vertical-align: middle; 
			position: absolute;
			padding: 6px;
			background-color: #f9f9f9;
			border-left: 3px solid #ccc;
			border-top: 3px solid #ccc;
			border-right: 3px solid #bbb;
			border-bottom: 3px solid #bbb;
			-moz-box-shadow:0px 3px 3px 0px rgba(119, 119, 119, 0.25);
			-webkit-box-shadow:0px 3px 3px 0px rgba(119, 119, 119, 0.25);
			box-shadow:0px 3px 3px 0px rgba(119, 119, 119, 0.25);
			cursor: default;
			min-width: 150px;
			max-height: 48px;
			overflow: hidden;
			z-index: 1;
		}
		.zoneIndicator:hover, .zoneNavigationItem:not(.itemSelected):hover {
			background-color: #fff;
		}
		.zoneIndicator:active {
			-moz-box-shadow: none;
			-webkit-box-shadow: none;
			box-shadow: none;
		}
		
		.zoneIndicatorLabel {
			white-space: nowrap;
		}
		
		.zoneIndicatorHeader {
			white-space: nowrap;
		}
		
		.miniIndicator {
			position: absolute;
			cursor: pointer;
			width: 20px;
			height: 20px;
			z-index: 0;
			font-size: 20px;
		}
		.miniIndicator i {
			text-shadow: 3px 3px 3px #ccc;
			position: relative;
			top: -10px; 
		}
		
		.marginIndicator {
			cursor: pointer;
			position: absolute;
			color: #555;
		}
		
		.touchpointPage {
			position: relative;
			border: 1px solid #eee;
			-moz-box-shadow:0px 6px 12px 0px rgba(119, 119, 119, 0.3);
			-webkit-box-shadow:0px 6px 12px 0px rgba(119, 119, 119, 0.3);
			box-shadow:0px 6px 12px 0px rgba(119, 119, 119, 0.3);
			margin-bottom: 20px;
		}
		
		.zoneNavigationContainer {
			max-height: 425px;
			overflow: auto;
		}

		.zoneNavigationItem {
			text-align: left; 
			vertical-align: middle; 
			padding: 8px;
			cursor: default;
			background-color: #f9f9f9;
			border-bottom: 1px solid #bbb;
		}
		.zoneNavigationItem i {
			font-size: 18px; 
			padding-left: 8px;
			position: relative;
			top: -1px;
		}

		.zoneNavigationItem.zoneNavItemError .zoneNavNameContainer, .zoneNavigationItem.zoneNavItemError i {
			color: red;
		}
		
		.itemSelected {
			background-color: #ddd;
		}
		
		.proofContainer {
			border-left: 1px solid #ccc;
			vertical-align:top;
		}
		
		.interactiveEditorContainer {
			padding: 12px 6px 6px 6px;
		}
		.interactiveTouchpointContainer {
			padding: 25px; 
			width: 965px;
			height: 700px;
			overflow: visible;
			box-sizing: border-box;
			border: 1px solid #ddd;
			padding-bottom: 400px;
		}
		.interactiveTouchpointContainer.init {
		   -moz-box-shadow:    inset 0 0 10px #ededed;
		   -webkit-box-shadow: inset 0 0 10px #ededed;
		   box-shadow:         inset 0 0 10px #ededed;
   		}
		
		.viewProofIcon {
			color: #555;
			text-shadow: 0 1px 0 #fff;
			cursor: pointer;
		}
		.viewProofIcon.disabled {
			color: #a0a0a0 !important;
		}
		.viewProofIcon:not(.disabled):hover {
			color: #777 !important;
		}
		.viewProofIcon:not(.disabled):active {
			color: #999 !important;
		}
		
		.pageIndicator {
			font-size: 16px;
			color:#414141;
			text-shadow: 0px 1px 0px #fff;  
			padding-left: 8px;
		}

		<c:if test="${isEmbedded}">
		body {
			background: url("../includes/themes/commonimages/layout/content_panel_background.gif") repeat scroll left top;
		}
		</c:if>

		.sideBarHeaderLabel {
			margin: 5px 15px;
			font-size: 14px;
			font-weight: bold;
		}
		.suppressedContentIndicator {
			display: none !important;
		}
	</style>

    <msgpt:Script>
        <script>

            // ***************** Editor Functions ************************
            var preProofData 				= null;
            var generalDebugData			= {};
            var contentRefData 				= null;
            var zonePropertiesData 			= null;
            var variableValuesData 			= null;
            var interactivePanelWidth 		= 0;
            var loadCounter					= 0;

            var debugStartLoad				= new Date().getTime();
            var debugPreProofLoadTime 		= null;
            var debugLastEditorLoadTime 	= null;
            var debugInteractiveDataTime	= null;

            var suppressNonEditableZones	= '${suppressNonEditableZones}';

            var timestamp_base_page						= (new Date()).getTime();
            var timestamp_javascript_base				= -1;
			var timestamp_start_proof_poll 				= -1;
			var timestamp_last_async_template_request 	= -1;
			var timestamp_process_a_start 				= -1;
			var timestamp_process_a_return 				= -1;
			var timestamp_process_b_start 				= -1;
			var timestamp_process_b_return 				= -1;
			var timestamp_audit_load_complete_start 	= -1;
			var timestamp_audit_load_complete_return	= -1;

			timestamp_javascript_base = (new Date()).getTime();
			global_data.spellcheck_languages = ${communication.spellcheckLanguages};

            function doCustomSubmit(actionId,btn) {
            	if ( $('#proofPollingContainer').length != 0 ) {

            		$(btn)
            		.popupFactory({
            			title				: client_messages.title.override_proof,
            			popupLocation		: "bottom",
            			trigger				: "instant",
            			width				: 272,
            			fnSetContent		: function(o) {
            				var popupEle = $("<p>" +
            									client_messages.text.override_proof_inprocess +
            								"</p>" +
            								"<div class=\"popupFactoryButtonsContainer\">" +
            									"<input title=\"" + client_messages.button.ok + "\" type=\"button\" id=\"confirmOverrideProofBtn\" onclick=\"javascript:doSubmit(" + actionId + ")\" style=\"display: none;\" />" +
            									"<input title=\"" + client_messages.button.cancel + "\" type=\"button\" id=\"cancelOverrideProofBtn\" onclick=\"javascript:popupFactoryRemove('connectedInteractive_canceloverrideproof');\" style=\"display: none;\" />" +
            	                			"</div>");

            				$(popupEle).find('#cancelOverrideProofBtn').styleActionElement();
            	            $(popupEle).find('#confirmOverrideProofBtn').styleActionElement({highlighted: true});

            	            return popupEle;
            			}
            		});
            		
                } else {
					doSubmit(actionId);
                }
            }

            function setDefaultText(zoneId) {

            	function cleanContent(content, binding) {

            		function pollForCleaningEditorInit(c,b) {

						if ( $('#cleaningEditorContainer').find('.mce-tinymce').length > 0
								&& $('#cleaningEditorContainer').find('.mce-tinymce').is(':visible') ) {
							var ed = tinymce.get('cleaningEditor');
							ed.execCommand( "mceSetContent", false, c );
							$(b).val( $('#cleaningEditor').val() );

							loadCounter--;
							var zoneId = parseId( $(b).closest("[id^='contentData_']") );
							toggleIndicatorOnEditableContentStatus(zoneId);
							toggleButtonState();
						} else {
							setTimeout( function() { pollForCleaningEditorInit(c,b) }, 100);
						}

					}

            		if ( $('#cleaningEditor').length == 0 ) {
            			$('body').prepend("<div id=\"cleaningEditorContainer\" style=\"opacity: 0; width: 0px; height: 0px;\"><textarea id=\"cleaningEditor\"></textarea></div>");
						$('#cleaningEditor').tinymce({
							script_url					: context + '/includes/javascript/' + tinymceDir + '/tinymce.min.js',
							valid_elements				: "*[*]",
							entity_encoding				: "named+numeric",
							entities					: tinymce_entities,
							plugins						: [	"mp_zone,mp_cleaner" ]
						});
					}

            		pollForCleaningEditorInit( content, binding );

				}

                var smartTextId = $('#contentData_' + zoneId + ' .contentGroup').attr('resetTemplateId');

            	// Don't request if no template has been set
				if ( parseInt(smartTextId) <= 0 )
					return;

                var requestParam = "contentType=communicationEmbeddedTextTemplate" +
                    "&contentItemId=" + smartTextId +
                    "&localeId=" + $('#communicationLocaleId').val();

                if (contentRefData && contentRefData.embedded_contents &&
                    contentRefData.embedded_contents[smartTextId]) {
                    requestParam += "&variantId=" + contentRefData.embedded_contents[smartTextId];
                    requestParam += "&selectionStatusId=" + ( $('#isTestCenterContext').val() == "true" ? 1 : 2);
                }
                if ( $('#isDebugContext').val() == "true" )
                	requestParam += "&communicationId=" + communicationId;
                requestParam += "&preProofId=" + preProofId;
                if ($('#isTestCenterContext').val() == "true")
                    requestParam += "&context=test";

				loadCounter++;
				toggleButtonState();

                var stampDate = new Date();
                $.ajax({
                    type: "GET",
                    url: context + "/getContentPreview.form?" + requestParam + "&cacheStamp=" + (stampDate.getTime()),
                    dataType: "json",
                    success: function (data) {
                        var renderedContent = $('<div/>').html(data.contents[0].text_content).text();
                        var contentEditor = $.contentEditor.get($('#contentData_' + zoneId));
                        if (contentEditor != null) {
                            contentEditor.setTextContent(renderedContent);

							if ( data.error != undefined )
								loadCounter = -1;
							else
								loadCounter--;
							toggleButtonState();
							toggleIndicatorOnEditableContentStatus(zoneId);
                        } else {
                        	// loadCounter update occurs post cleaning
                        	cleanContent(renderedContent, $('#contentData_' + zoneId).find('.textContentBinding'));
                        }

						timestamp_last_async_template_request = (new Date()).getTime() - timestamp_base_page;
                    },
					error: function(data) {
                    	loadCounter == -1;
						toggleButtonState();
					}
                });
            }

            // ***************** Display Functions ************************
            function toggleIndicatorOnEditableContentStatus(zoneId) {

                if ( suppressNonEditableZones == 'false' )
					return;
                    
            	$('#contentData_' + zoneId).find('.textContentBinding').each( function() {
					var content = $($(this).val());
					var appliesContentMenus = $(content).find('[type=15]').length != 0
					var hasFixedContent = $(content).find('[template_fixed_content=true]').length != 0

					$(content).find('[template_fixed_content=true],.mceNonEditable').remove();

					if ( $.trim($(content).text()).length == 0 && hasFixedContent && !appliesContentMenus ) {
						$('#miniIndicator_'+zoneId+",#interactiveZoneIndicator_"+zoneId+",#zoneNavigationItem_"+zoneId+",#marginIndicator_"+zoneId)
						 .addClass('suppressedContentIndicator').hide();
					}
                });
            }

            function toggleButtonState() {
            	if ( loadCounter != 0 ) {
            		var btnClass = loadCounter < 0 ? 'btn-danger' : 'disabled';
					$('.toolbarRightSection .btn').each( function() {
						if ( $(this).find('#link_Cancel').length == 0 )
							$(this).addClass(btnClass);
					});
				} else {
					$('.toolbarRightSection .btn').removeClass('disabled btn-danger');
				}
			}
            
            function toggleZone(select) {
                var zoneId = $(select).find('option:selected').val();
                $('#contentData').changeContentGroup("zone_" + zoneId);
            }

            // ***************** Init Functions ***************************

            function pollForProof() {

                if ($('#proofPollingContainer').length == 0)
                    return;

                var stampDate = new Date();
                var communicationId = '${communication.id}';
                var params = "?proofId=" + $("#pdfProofContainer").attr("itemId") +
                    "&communicationId=" + '${communication.id}' +
                    "&source=order_content_proof" +
                    "&tk=" + getParam('tk') +
                    "&cacheStamp=" + stampDate.getTime();

                $.ajax({
                    type: "GET",
                    url: context + "/getCommunicationProofStatus.form" + params,
                    dataType: "json",
                    success: function (data) {
                        processPollResult(data, "proof");
                    },
                    error: function (data) {

                    }
                });

            }

            var preProofId = '${preProofId}';
            var communicationId = '${communication.id}';
            var firstPoll = true;

            function pollForPreProof() {

                if ($('#pagesLoadingContainer').length == 0 || $('#pagesLoadingContainer').is('.init'))
                    return;

                var stampDate = new Date();

                var customerId = '${communication.customerIdentifier}';
                var params = (preProofId != '-1' ? ("?proofId=" + preProofId) : ("?recip_id=" + encodeURIComponent(customerId)) ) +
                    "&rerun_on_error=" + firstPoll +
                    (parseInt(communicationId) > 0 ?
                            "&communicationId=" + communicationId :
                            "&recip_id=" + encodeURIComponent(customerId)
                    ) +
                    "&source=order_content_preproof" +
                    "&tk=" + getParam('tk') +
                    "&cacheStamp=" + stampDate.getTime();

                firstPoll = false;

                $.ajax({
                    type: "GET",
                    url: context + "/getCommunicationProofStatus.form" + params,
                    dataType: "json",
                    success: function (data) {
                        processPollResult(data, "preproof");
                    },
                    error: function (data) {

                    }
                });

            }

            var proofParams = null;

            function processPollResult(data, type) {

                var stampDate = new Date();
                var targetContainer = null;
                if (type == "proof") {
                    targetContainer = $('#proofContainer');
                    if (data.status == "no_matching_recipient_error" || data.status == "no_production_content_error" || data.status == "log" || data.status == "error" || data.status == "complete")
                        $('#proofPollingContainer').remove();
                    else
                        window.setTimeout(function () {
                            pollForProof();
                        }, 2000);
                } else if (type == "preproof") {
                    targetContainer = $('#interactiveTouchpointContainer');
                    if (data.status != "in_process") {
                        $('#interactiveTouchpointContainer,#pagesLoadingContainer').addClass('init');
                        $('#pagesLoadingContainer #loadingTextContainer').html( client_messages.text.rendering + "...");
                    } else {
                        window.setTimeout(function () {
                            pollForPreProof();
                        }, 2000);
                    }
                    preProofId = data.pre_proof_id;
                }

                if (data.status == "no_matching_recipient_error") {

                    var errorContainer = $('#errorContainerTemplate').clone();
                    $(errorContainer).find('.errorInfoContainer').append(client_messages.text.unable_to_generate_proof);
                    $(errorContainer).show();
                    $(targetContainer).append(errorContainer);

                } else if (data.status == "no_production_content_error") {

                    var errorContainer = $('#errorContainerTemplate').clone();
                    $(errorContainer).find('.errorInfoContainer').append(client_messages.text.unable_to_generate_preproof);
                    $(errorContainer).show();
                    $(targetContainer).append(errorContainer);

                } else if (data.status == "log") {

                    var infoContainer = $("<div id=\"logInfoContainer_" + type + "\" style=\"cursor: pointer; position: relative; z-index: 1001;\" class=\"InfoSysContainer_info\">" + client_messages.text.unable_to_generate_preproof_view_log + "</div>");
                    $(targetContainer).append(infoContainer);
                    $('#logInfoContainer_' + type).click(function () {
                        var reqUrl = context + '/download/ data.form?type=bundle_file&bundle_type=diagnostic&job_id=' + data.job_id + '&cacheStamp=' + stampDate.getTime();
                        javascriptHref(reqUrl);
                    });

                } else if (data.status == "complete") {

                	if ( debugPreProofLoadTime != null ) {
						$('#preProofLoadTimeContainer').html((((new Date().getTime()) - debugPreProofLoadTime) / 1000) + "s");
						generalDebugData.waiting_for_preproof = (((new Date().getTime()) - debugPreProofLoadTime) / 1000) + "s";
					}

                    if (type == "proof") {

                        $('#pdfProofContainer').show();
                        $('.viewProofIcon')
                            .removeClass('disabled')
                            .click(function () {
                                if (data.is_print_touchpoint) {
                                    if (data.is_remote_result)
                                        javascriptHref(context + "/download/pdf.form?comm_proof_id=" + data.item_id + "&comm_proof_type=proof&type=remote_comm_pdf");
                                    else
                                        javascriptHref(context + "/download/pdf.form?resource=" + data.resource);
                                } else {
                                    popItUpWithDimensions(context + "/html_display.form?file=" + data.output_path + "&type=preview", data.template_width, data.template_height);
                                }
                            });

                        if (data.is_print_touchpoint) {

                            var params = "";
                            if (data.is_remote_result) {
                                params += "?comm_proof_id=" + data.item_id +
                                    "&comm_proof_type=" + (data.is_pre_proof ? "pre_proof" : "proof") +
                                    "&type=remote_comm_pdf";
                            } else {
                                params += "?resource=" + data.resource +
                                    "&embedded_pdf=true";
                            }
                            params += "#toolbar=0&navpanes=0&statusbar=0&pagemode=thumbs";

                            proofParams = params;
                            setProofDisplay();
                        } else {
                            $('#pdfProofContainer').append("<iframe src=\"" + context + "/html_display.form?file=" + data.output_path + "&type=preview\" height=\"300px\" width=\"100%\"></iframe>");
                        }
                        ;

                        if ( data.job_id && !$('#proofJobIdContainer').is('.init') ) {
                            $('#proofJobIdContainer').addClass('init').html(data.job_id);
                            $('#proofJobIdContainer').click(function () {
                                var reqUrl = context + '/download/data.form?type=bundle_file&bundle_type=diagnostic&job_id=' + data.job_id + '&cacheStamp=' + stampDate.getTime();
                                javascriptHref(reqUrl);
                            });
                        }

                    } else if (type == "preproof") {

                        initInteractiveTouchpoint(data);

                    }
                    ;

                } else if (data.status == "error") {

                    var errorContainer = $('#errorContainerTemplate').clone();
                    $(errorContainer).find('.InfoSysContainer_error').find('p').html(type == "proof" ?
                        client_messages.text.unable_to_generate_proof_view_log :
                        client_messages.text.unable_to_generate_preproof_view_log);
                    $(errorContainer).show();
                    $(targetContainer).append(errorContainer);

                    $(errorContainer).find('.InfoSysContainer_error').click(function () {
                        var reqUrl = context + '/download/data.form?type=bundle_file&bundle_type=diagnostic&job_id=' + data.job_id + '&cacheStamp=' + stampDate.getTime();
                        javascriptHref(reqUrl);
                    });

                }
                ;

				if (!!data.recipient_contents) {
					$('#jsonRecipientContents').html( data.recipient_contents );
				} else {
					$('#jsonRecipientContents').empty();
				}
				if (!!data.variable_values) {
					$('#jsonVariableValues').html( data.variable_values );
				} else {
					$('#jsonVariableValues').empty();
				}
				if (!!data.json_selections) {
					$('#jsonSelections').html( data.json_selections );
				} else {
					$('#jsonSelections').empty();
				}
				if (!!data.json_order_log) {
					$('#jsonOrderLog').html( data.json_order_log );
				} else {
					$('#jsonOrderLog').empty();
				}

            }


            function initEditorForIndicator(ind, data) {
                var zoneId = parseId(ind);

                $(ind).find('.editorLoadingIcon').showEle('normal');


                var canvasDimensions = zonePropertiesData["zone_" + zoneId].canvas_dimensions.split(':');
                var canvasWidth = Math.min(30 + (parseFloat(canvasDimensions[0]) * 100), $(ind).closest("[id^='touchpointPage_']").width());
                var canvasHeight = Math.max((parseFloat(canvasDimensions[1]) * 100), 320);


                var templateId = zonePropertiesData["zone_" + zoneId].template_id;

                var stampDate = new Date();
				var dataUrl = context + "/getInteractiveData.form?" +
				                "&type=editor_data" +
				                "&zoneId=" + zoneId +				                
				                "&cacheStamp=" + (stampDate.getTime()) +
				                "&preProofId=" + data.pre_proof_id;
				if ( $('#isDebugContext').val() == "true" )
                	dataUrl += "&communicationId=" + communicationId;

                $.ajax({
                    type: "GET",
                    url: dataUrl,
                    dataType: "json",
                    success: function (editorData) {

                    	function initEditor(d) {

    						d.enforce_max_height = false;
                            d.pre_proof_id = preProofId;
                            d.communication_id = communicationId;
                            d.unrestricted_frame_width = true;
                            d.is_test_context = $('#isTestCenterContext').val() == "true";
                            d.content_menu = {
                                applied: true,
                                mode: 'content'
                            };
                            d.async_variables = { type : 'connected', pinnable: false };
                            d.async_embedded_content = { type : 'connected', pinnable: false };
                            d.dynamic_content_data = data.general_editor_data.content_ref_data;
                            d.apply_rulers = false;
                            d.variable_data = variableValuesData;
                            d.marcie_flags = ${marcieFlags};

    						// Editor: Reset data
    						if ( d.apply_reset) {
    							var templateId = $('#contentData_' + zoneId + ' .contentGroup').attr('resetTemplateId');
    							d.reset_data = {};
    							d.reset_data.template_id = templateId;
    	                        if ( contentRefData && contentRefData.embedded_contents && contentRefData.embedded_contents[templateId] ) {
									d.reset_data.variant_id = contentRefData.embedded_contents[templateId];
									d.reset_data.selection_statis_id = ( $('#isTestCenterContext').val() == "true" ? 1 : 2);
								}
    	                        d.reset_data.pre_proof_id = preProofId;
    	                        d.reset_data.communication_id = communicationId;
    	                        d.reset_data.is_test_context = ($('#isTestCenterContext').val() == "true");
    						}

                            <c:choose>
                            <c:when test="${communication.isEmailContentDelivery || communication.isWebDelivery}">
                            tinyMCEemailInit(canvasWidth, canvasHeight, d);
                            </c:when>
                            <c:otherwise>
                            tinyMCEinit(canvasWidth, canvasHeight, d);
                            </c:otherwise>
                            </c:choose>

                            $("#interactiveEditorContainer_" + zoneId).show();

                            $("#contentData_" + zoneId).contentEditor({
                                text_data: d.text_data,
                                paragraph_data: d.paragraph_style_data,
                                contextPath: '${contextPath}',
								defaultLocaleId: '${command.localeId}',
								focusLocaleId: '${command.localeId}',
                                editorInitData: tinymceEditorDef_standard,
                                languages: null,
                                messageId: null,
                                onload: function () {

                                },
                                applies_freeform: zonePropertiesData["zone_" + zoneId].is_freeform,
                                library_only: zonePropertiesData["zone_" + zoneId].library_only,
                                canvas_dimensions: zonePropertiesData["zone_" + zoneId].canvas_dimensions,
                                canvas_rotation: zonePropertiesData["zone_" + zoneId].canvas_rotation,
                                zone_rotation: zonePropertiesData["zone_" + zoneId].rotation,
                                topPadding: 0,
                                usage: 'edit',
                                appliesTemplates: true,
                                templateId: templateId,
                                templateLocaleId: '${command.localeId}',
                                referenceData: contentRefData,
                                nTk: '${nodeGUID}',
                                uTk: '${webDAVToken}',
                                enabledFileEdit: true,
                                statusViewId: 1,
                                isTestContext: $('#isTestCenterContext').val() == "true",
                                onInitComplete: function (o) {
                                    var id = parseId(o.data.instId);
                                    var zoneInd = $("#interactiveEditorContainer_" + id).closest('.zoneIndicator');
                                    var canvasDimensions = o.data.canvas_dimensions.split(':');
                                    var canvasWidth = Math.min(30 + (parseFloat(canvasDimensions[0]) * 100), $(zoneInd).closest(".touchpointPage").width());
                                    if (o.data.canvas_rotation == 90 || o.data.canvas_rotation == 270)
                                        canvasWidth = Math.min(30 + (parseFloat(canvasDimensions[1]) * 100), $(zoneInd).closest(".touchpointPage").width());
                                    $(zoneInd).attr('canvas_width', canvasWidth);
                                    $(zoneInd).find('.contentEditor_ImageContainer').css({'max-width': canvasWidth + 'px'});
                                    $(zoneInd).width($(zoneInd).attr('min-width')).css({
                                        'max-height': 'none',
                                        'overflow': 'visible'
                                    });

                                    zoneIndicatorToggle(ind, true);
                                    $(ind).find('.editorLoadingIcon').hide();

                                   	$('#lastEditorLoadTimeContainer').html( (((new Date()).getTime() - debugLastEditorLoadTime)/1000) + "s" );
                                }
                            });

                        }

                    	function pollForVariableDataInit(o) {
	                        if ( variableValuesData == null )
	                            setTimeout( function() { pollForVariableDataInit(o) }, 200);
	                        else
	                        	initEditor(o);
                    	}

                    	pollForVariableDataInit(editorData);

                    }
                });

            }

            var _zIndicatorTemplate = null;
            var _zMarginIndicatorTemplate = null;
            var _zMiniIndicatorTemplate = null;

            function initInteractiveTouchpoint(data) {

            	debugInteractiveDataTime = new Date().getTime();

                var zIndicatorTemplate = _zIndicatorTemplate || (_zIndicatorTemplate = Handlebars.compile($('#zoneIndicatorTemplate').html()));
                var zMarginIndicatorTemplate = _zMarginIndicatorTemplate || (_zMarginIndicatorTemplate = Handlebars.compile($('#marginIndicatorTemplate').html()));
                var zMiniIndicatorTemplate = _zMiniIndicatorTemplate || (_zMiniIndicatorTemplate = Handlebars.compile($('#miniIndicatorTemplate').html()));

                function initTemplateContent(data, indicator) {

                    var zoneId = parseId(indicator);

                    $(indicator).showEle('normal');
                    var isRestrictedEditor = zonePropertiesData["zone_" + zoneId].library_only;
                    var minHeaderWidth = Math.max($(indicator).find('.zoneIndicatorLabel').outerWidth() + $(indicator).find('.zoneIndicatorHeader i').outerWidth(), (isRestrictedEditor ? 150 : 350));
					if ( zonePropertiesData["zone_" + zoneId].content_type == 'graphic' )
						minHeaderWidth = Math.max(minHeaderWidth, 550);

                    $(indicator).attr('min-width', minHeaderWidth);
                    $(indicator).width(minHeaderWidth);
                    var canvasDimensions = zonePropertiesData["zone_" + zoneId].canvas_dimensions.split(':');
                    var canvasWidth = Math.min(30 + (parseFloat(canvasDimensions[0]) * 100), $(indicator).closest("[id^='touchpointPage_']").width());
                    if ($(indicator).attr('min-width') < canvasWidth)
                        $(indicator).width(canvasWidth);

                    $("#contentData_" + zoneId + " .contentGroup").each(function () {
                        $(this).attr('backgroundColor', zonePropertiesData["zone_" + zoneId].background_color);
                        $(this).attr('canvasDimensions', zonePropertiesData["zone_" + zoneId].canvas_dimensions);
                        $(this).attr('canvasRotation', zonePropertiesData["zone_" + zoneId].canvas_rotation);

						// DEFAULT TEMPALTES
                        var templateId = zonePropertiesData["zone_" + zoneId].template_id;
                        $(this).attr('resetTemplateId', templateId);
                        if (data.general_editor_data.applies_default_templates) {
                            $(this).attr('templateId', templateId);
                            $(this).find("[id^='defaultContentLibraryId_" + zoneId + "_']").val(templateId);

                            $(this).find("select[id^='conLibSelect_']")
                                .html('<option id="' + templateId + '" value="' + templateId + '" selected="selected">BINDING PLACEHOLDER</option>');
                            $(this).find('.useContentLibraryBinding').attr('checked', 'checked');
                            $(this).find('.imageLibaryTypeSelect').selectOption('imageLibaryTypeSelect_2');
                        } else {
                            $(this).attr('templateId', 0);
                        }
                    });
                    if (data.general_editor_data.applies_default_templates && zonePropertiesData["zone_" + zoneId].content_type == 'text') {
                        setDefaultText(zoneId);
                    } else {
                    	toggleIndicatorOnEditableContentStatus(zoneId);
                    }
                }

                function initIndicator(zoneData, i, isDigital) {

                    if (!zoneData.enabled) {
                        $('#zoneNavigationItem_' + zoneData.id + ", #interactiveEditorContainer_" + zoneData.id).remove();
                        return;
                    }

                    var topX = zoneData.x1 * 100;
                    var topY = zoneData.y1 * 100;

                    var zoneLocatorEle = null;
                    // Digital: Find location in HTML
                    $("#interactiveTouchpointContainer .connectedZoneLocator").each(function () {
                    	 if ($(this).attr('id') == "##Z" + zoneData.id + "##") {
                    		zoneLocatorEle = $(this);
     						topX = $(this).offset().left - $(this).closest('.touchpointPage').offset().left;
    						topY = $(this).offset().top - $(this).closest('.touchpointPage').offset().top;
                    	 }
                    });

                    $('#zoneNavigationItem_' + zoneData.id + " .zoneNavNameContainer").html(zonePropertiesData["zone_" + zoneData.id].name);
                    $('#zoneNavigationItem_' + zoneData.id).showEle('normal');

                    // Unmatched zone/Zone marker not found in template
                    if ( (isDigital && zoneLocatorEle == null) || i < 0 ) {
                    	$('#zoneNavigationItem_' + zoneData.id).addClass('zoneNavItemError');
                        return;
                    }

                    var indData = {
                        eleIdx: zoneData.id,
                        icon: zoneData.content_type == "text" ? "fa-font" : "fa-image",
                        zone_name: zoneData.name
                    };

                    var newZoneIndicator = $(zIndicatorTemplate(indData))[0];
                    $("#touchpointPage_" + (i + 1)).append(newZoneIndicator);

                    var indData = {
                        eleIdx: zoneData.id
                    };
                    var newMiniIndicator = $(zMiniIndicatorTemplate(indData))[0];
                    $("#touchpointPage_" + (i + 1)).append(newMiniIndicator);
                    $("#miniIndicator_" + zoneData.id)
                        .click(function (e) {
                            zoneIndicatorToggle($('#interactiveZoneIndicator_' + parseId(this)), false);
                        })
                        .show();

                    var indData = {
                        eleIdx: zoneData.id
                    };
                    var newMarginIndicator = $(zMarginIndicatorTemplate(indData))[0];
                    $("#touchpointPage_" + (i + 1)).append(newMarginIndicator);
                    $(newMarginIndicator)
                        .click(function (e) {
                            zoneIndicatorToggle($('#interactiveZoneIndicator_' + parseId(this)), false);
                        })
                        .show();

                    $(newZoneIndicator).find('.zoneIndicatorHeader').click(function (e) {
                        zoneIndicatorToggle($(this).closest('.zoneIndicator'), false);
                    });

                    positionIndicators(zoneData.id, topX, topY);

                    // EDITOR INIT
                    $("#editorContainer_" + zoneData.id).append($("#interactiveEditorContainer_" + zoneData.id));

                }

                function initVariableValuesData() {

                	var stampDate = new Date();
                    var dataUrl = context + "/getInteractiveData.form?" +
			                    "type=variable_data" +
			                    "&preProofId=" + data.pre_proof_id +
			                    "&cacheStamp=" + (stampDate.getTime());
                    if ( $('#isDebugContext').val() == "true" )
                    	dataUrl += "&communicationId=" + communicationId;

                    $.ajax({
                        type: "GET",
                        url: dataUrl,
                        dataType: "json",
                        success: function (data) {

							if ( data.variable_values )
								variableValuesData = data;
                        	
                        	if ( data.debug_timing ) {
								$('#variableValuesLoadTimeContainer').html("<br/>" + JSON.stringify(data.debug_timing))
								if ( data.debug_timing.ws_get_variable_values )
									generalDebugData.ws_get_variable_values = data.debug_timing.ws_get_variable_values;
							}

                        }
                    });
                	
                }

                function positionIndicators(id, x, y) {

                    $('#miniIndicator_'+id).css({'top' : (y-4) + 'px', 'left' : (x-4) + 'px'});
                    $('#interactiveZoneIndicator_'+id).css({'top' : y + 'px', 'left' : x + 'px'});
                    $('#marginIndicator_'+id).css({'top' : y + 'px', 'left' : '-20px'});
 
                }

                function postSectionLoad() {
                    // ON LOAD: Convert scroll to main window from interactive panel
                    $('.interactiveTouchpointContainer').css({
                        'width': Math.max(965, (interactivePanelWidth + 120)) + 'px',
                        'height': 'auto'
                    });
                    $('#floatPanelContainer').css({
                        'width': Math.max(965, (interactivePanelWidth + 120)) + $('#navigationPanel').outerWidth() + 'px'
                    });
                    /*$('#containerLower').attr('align', 'center').css({
                        'min-width': ((interactivePanelWidth + 120) + $('#navigationPanel').width()) + 'px',
                        'width': 'auto'
                    });
                    //$('.contentPanelInnerContainer').css({'width': 'auto', 'margin': '0px 30px'});
                    //$('body').css({'min-width': ($('#containerLower').outerWidth() + 120) + 'px'});
					*/
                    updateHasContentIndicators();
                    toggleIndicators();

                    // Permit proofing once interactive data has loaded (delay to accomodate async loading of template content)
                    setTimeout( function() {
                        $('.btnsDisabled').hide();
                        $('.btnsEnabled').show();
                    }, 500);

                }

                function processInitializationData() {
					zonePropertiesData = preProofData.general_editor_data.zone_data;
					contentRefData = preProofData.general_editor_data.content_ref_data;

					// DEBUG INFO
					if ( preProofData.preproof_job_id && !$('#preProofJobIdContainer').is('init') ) {
						$('#preProofJobIdContainer').addClass('init').html(preProofData.preproof_job_id);
						$('#preProofJobIdContainer').click(function () {
							var reqUrl = context + '/download/data.form?type=bundle_file&bundle_type=diagnostic&job_id=' + preProofData.preproof_job_id + '&cacheStamp=' + stampDate.getTime();
							javascriptHref(reqUrl);
						});
					}
					if ( preProofData.proof_job_id && !$('#proofJobIdContainer').is('init') ) {
						$('#proofJobIdContainer').addClass('init').html(preProofData.proof_job_id);
						$('#proofJobIdContainer').click(function () {
							var reqUrl = context + '/download/data.form?type=bundle_file&bundle_type=diagnostic&job_id=' + preProofData.proof_job_id + '&cacheStamp=' + stampDate.getTime();
							javascriptHref(reqUrl);
						});
					}
					$('#variantNameContainer').html(preProofData.variant_name + " (" + preProofData.alternate_name + ")");

					// CONTENT VIEW CSS
					if ($('#defaultContentViewCSS').length == 0 && preProofData.general_editor_data.default_content_view_css)
						$('head').append("<link id=\"defaultContentViewCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + preProofData.general_editor_data.default_content_view_css + "\" />");
					if ($('#defaultTextStylesViewCSS').length == 0 && preProofData.general_editor_data.extended_text_styles_view_css)
						$('head').append("<link id=\"defaultTextStylesViewCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + preProofData.general_editor_data.extended_text_styles_view_css + "\" />");
					if ($('#defaultParagraphStylesViewCSS').length == 0 && preProofData.general_editor_data.extended_paragraph_styles_view_css)
						$('head').append("<link id=\"defaultParagraphStylesViewCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + preProofData.general_editor_data.extended_paragraph_styles_view_css + "\" />");
					if ($('#defaultListStylesViewCSS').length == 0 && preProofData.general_editor_data.extended_list_styles_view_css)
						$('head').append("<link id=\"defaultListStylesViewCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + preProofData.general_editor_data.extended_list_styles_view_css + "\" />");

					$('#pagesLoadingContainer').addClass('init').hide();

					for (var i = 0; i < preProofData.page_count; i++) {

						if (preProofData.is_print) {

							var backgroundImgSrc = null;
							if ( $('#isDebugContext').val() == "true" )
								backgroundImgSrc = preProofData.pages[i].background_image;
							else
								backgroundImgSrc = "data:image/jpeg;base64," + preProofData.pages[i].background_image;

							$('#interactiveTouchpointContainer')
									.append("<div id=\"pageIndicator_" + (i + 1) + "\" class=\"pageIndicator\" style=\"margin-bottom: 10px; display: none;\">" +
											"<i class=\"fa fa-file\" style=\"padding-right: 12px; color: #777;\" />" +
											client_messages.text.page + " " + (i + 1) +
											"</div>" +
											"<div id=\"touchpointPage_" + (i + 1) + "\" class=\"touchpointPage\" style=\"width: " + (preProofData.pages[i].width * 100) + "px; height: " + (preProofData.pages[i].height * 100) + "px; display: none;\">" +
											"<img style=\"width: " + (preProofData.pages[i].width * 100) + "px; height: " + (preProofData.pages[i].height * 100) + "px;\" " +
											"src=\"" + backgroundImgSrc + "\" />" +
											"</div>");

							if (interactivePanelWidth < preProofData.pages[i].width * 100)
								interactivePanelWidth = preProofData.pages[i].width * 100;

							function initSection(section) {
								$(section).showEle('normal');
								$('#pageIndicator_' + parseId($(section))).showEle('normal');
								$(section).find('.zoneIndicator').each(function () {
									initTemplateContent(preProofData, this);
								});
								postSectionLoad();
							}

							for (var j = 0; j < preProofData.pages[i].zones.length; j++) {
								var zoneData = preProofData.pages[i].zones[j];
								initIndicator(zoneData, i, false);
							};

							// PAGE LOAD COMPLETE
							if ( $('#isDebugContext').val() == "true" ) {
								$("#touchpointPage_" + (i + 1)).find('img').each(function () {
									initSection($(this).closest('.touchpointPage'));
								});
							} else {
								$("#touchpointPage_" + (i + 1)).find('img').load(function () {
									initSection($(this).closest('.touchpointPage'));
								});
							}

						} else if (preProofData.is_digital) {

							var template = $("<div id=\"touchpointPage_" + (i + 1) + "\" class=\"touchpointPage\">" +
									preProofData.display_html +
									"</div>");

							$('#interactiveTouchpointContainer').append(template);

							$(template).find('img').load( function() {
								$('#interactiveTouchpointContainer .connectedZoneLocator').each( function() {
									var zoneLocator = $(this);
									var zoneId = $(zoneLocator).attr('id').replace(/#/g,"").replace(/Z/g,"");
									var left = $(zoneLocator).offset().left - $(zoneLocator).closest('.touchpointPage').offset().left;
									var top = $(zoneLocator).offset().top - $(zoneLocator).closest('.touchpointPage').offset().top;
									positionIndicators(zoneId, left, top);
								});
							});

							if (interactivePanelWidth < preProofData.pages[i].width)
								interactivePanelWidth = preProofData.pages[i].width;

							for (var j = 0; j < preProofData.pages[i].zones.length; j++) {
								var zoneData = preProofData.pages[i].zones[j];
								initIndicator(zoneData, i, true);
							};

							$('#interactiveTouchpointContainer').find('.zoneIndicator').each(function () {
								initTemplateContent(preProofData, this);
							});
							postSectionLoad();

						}

					}
					;

					if (preProofData.unmatched_zones) {
						for (var k = 0; k < preProofData.unmatched_zones.length; k++) {
							var zoneData = preProofData.unmatched_zones[k];
							initIndicator(zoneData, -1, preProofData.is_digital);
						}
						;
					}

					if ( debugInteractiveDataTime != null ) {
						$('#interactiveLoadTimeContainer').append("Time from start of data request to UI complete:<br\>" + (((new Date()).getTime() - debugInteractiveDataTime) / 1000) + "s");
						generalDebugData.total_async_data_load_time = (((new Date()).getTime() - debugInteractiveDataTime) / 1000) + "s";
					}
					if ( preProofData.debug_timing )
						$('#interactiveLoadTimeContainer').append("<br/>Collected debug data points:<br\>" + JSON.stringify(preProofData.debug_timing));
					if ( preProofData.general_editor_data.ref_debug_timing )
						$('#interactiveLoadTimeContainer').append("<br/>" + JSON.stringify(preProofData.general_editor_data.ref_debug_timing));

					sendLoadCompleteForAudit();
				}

                if (data.status == "complete") {

					initVariableValuesData();
                	
                	var stampDate = new Date();
                    var dataUrl = context + "/getInteractiveData.form?" +
			                    "type=TYPE_PLACEHOLDER" +
			                    "&preProofId=" + data.pre_proof_id +
			                    "&cacheStamp=" + (stampDate.getTime());
                    if ( $('#isDebugContext').val() == "true" )
                    	dataUrl += "&communicationId=" + communicationId;

					timestamp_process_a_start = (new Date()).getTime() - timestamp_base_page;

                    // Async: General data
					loadCounter++;
                    $.ajax({
                        type: "GET",
                        url: dataUrl.replace("TYPE_PLACEHOLDER","general_data"),
                        dataType: "json",
                        success: function (data) {

							timestamp_process_a_return = (new Date()).getTime() - timestamp_base_page;

                        	$.extend(true, generalDebugData, data.debug_timing);
							if ( preProofData == null ) {
								preProofData = data;
							} else {
								$.extend(true, preProofData, data);
								processInitializationData();
							}

							if ( data.error != undefined )
								loadCounter = -1;
							else
								loadCounter--;
							toggleButtonState();

                        },
						error: function(data) {
							loadCounter == -1;
							toggleButtonState();
						}
                    });

					timestamp_process_b_start = (new Date()).getTime() - timestamp_base_page;

                    // Async: Conversion data
					loadCounter++;
					$.ajax({
						type: "GET",
						url: dataUrl.replace("TYPE_PLACEHOLDER","conversion"),
						dataType: "json",
						success: function (data) {

							timestamp_process_b_return = (new Date()).getTime() - timestamp_base_page;

							if ( preProofData == null ) {
								preProofData = data;
							} else {
								$.extend(true, preProofData, data);
								processInitializationData();
							}

							if ( data.error != undefined )
								loadCounter = -1;
							else
								loadCounter--;
							toggleButtonState();
						},
						error: function(data) {
							loadCounter == -1;
							toggleButtonState();
						}
					});

                }
                ; // END: if ( data.status == "complete" )
            }

            function zoneIndicatorToggle(ind, navClick) {

                if (preProofData == null)
                    return;

                var zoneId = parseId(ind);

				// Content Intel: Toggle panel
				$('.mce-custompanel').remove();

                if ( $('#zoneNavigationItem_'+zoneId).is('.zoneNavItemError') )
                    return;

                if ($("#interactiveEditorContainer_" + zoneId).is('.editorInit')) {

                    $('.zoneNavigationItem').removeClass('itemSelected');
                    $('.zoneIndicator').css({"z-index": 1});

                    updateHasContentIndicators();
                    if ($("#interactiveEditorContainer_" + zoneId).is(':visible') && !navClick) {
                        $(ind).width($(ind).attr('min-width') + 'px');
                        $("#interactiveEditorContainer_" + zoneId).hide();
                        if ($('#toggleIndIcon').is('.fa-toggle-off'))
                            $('#interactiveZoneIndicator_' + zoneId).hide();
                    } else {

                        $(ind).css({"z-index": 2});

                        // CLOSE OTHER EDITORS
                        $(".interactiveEditorContainer").each(function () {
                            if (parseId(this) != zoneId) {
                                $(this).closest('.zoneIndicator').width($(this).closest('.zoneIndicator').attr('min-width') + 'px');
                                $("#interactiveEditorContainer_" + parseId(this)).hide();
                                if ($('#toggleIndIcon').is('.fa-toggle-off'))
                                    $('#interactiveZoneIndicator_' + parseId(this)).hide();
                            }
                        });

                        // OPEN TARGET EDITOR
                        var canvasDimensions = zonePropertiesData["zone_" + zoneId].canvas_dimensions.split(':');
                        var canvasRotation = zonePropertiesData["zone_" + zoneId].canvas_rotation;
                        var canvasWidth = Math.min(30 + (parseFloat(canvasDimensions[0]) * 100), $(ind).closest('.touchpointPage').width());
                        if (canvasRotation == 90 || canvasRotation == 270)
                            canvasWidth = Math.min(30 + (parseFloat(canvasDimensions[1]) * 100), $(ind).closest('.touchpointPage').width());
                        if ($(ind).attr('min-width') < canvasWidth)
                            $(ind).width(canvasWidth + 20);

                        if ($('#toggleIndIcon').is('.fa-toggle-off'))
                            $('#interactiveZoneIndicator_' + zoneId).show();

                        $("#interactiveEditorContainer_" + zoneId).showEle('normal');
                        // FLIP TOGGLE: Refresh toggle init
                        $.contentEditor.get($('#contentData_' + zoneId)).setCurrentImageLibraryToggle();

                        $("#zoneNavigationItem_" + zoneId).addClass('itemSelected');
                        $(ind).find('i').css({'color': '#414141'});
                        $("#zoneNavigationItem_" + zoneId).find('i').css({'color': '#414141'});

                        var displayContainerWidth = $(ind).find('.contentEditor_DisplayContainer').outerWidth();
                        $("#contentData_" + zoneId).resize();
                        if ($(ind).outerWidth() < displayContainerWidth) {
                            $(ind).width(displayContainerWidth + 12);
                            $(ind).find('.contentEditor_ImageContainer .contentEditor_GraphicImg').css({'max-width': Math.min(parseFloat($(ind).attr('canvas_width')), displayContainerWidth) + 'px'});
                        }

                        // EDITOR HEIGHT: Permit dynamic sizing
                        if ($(ind).find('.mce-tinymce.mce-container.mce-panel').length != 0)
                            $(ind).find('.contentEditor_InnerContentContainer,.mce-tinymce.mce-container.mce-panel').css({'height': 'auto'});

                        if (navClick) {
                            // SCROLL TO INDICATOR
                            $(document).scrollTo($('#interactiveZoneIndicator_' + zoneId), 500, {
                                offset: {
                                    left: -500,
                                    top: -50
                                }
                            });
                        }
                        ;
                    }
                    ;
                } else {

                	debugLastEditorLoadTime = (new Date()).getTime();

                    if ($('#toggleIndIcon').is('.fa-toggle-off'))
                        $('#interactiveZoneIndicator_' + zoneId).show();

                    // SCROLL TO INDICATOR
                    $(document).scrollTo($('#interactiveZoneIndicator_' + zoneId), 500, {
                        offset: {
                            left: -500,
                            top: -50
                        }
                    });

                	$("#interactiveEditorContainer_" + zoneId).addClass('editorInit');

                    initEditorForIndicator(ind, preProofData);

                }


            }

            function updateHasContentIndicators() {

                function zoneHasContent(id) {
                    var bindings = $("#interactiveEditorContainer_" + id);
                    var useImageLibrary = $(bindings).find('.useContentLibraryBinding').is(':checked');
                    var contentLibrarySelectValue = $(bindings).find('.useContentLibrarySelect select').val();
                    var appliesDefaultLibrarySelectValue = $.trim($(bindings).find("[id^='defaultContentLibraryId_']").val()).length > 0;
                    var appliesGraphicFile = $.trim($(bindings).find('.graphicFileBinding').val()).length > 0 ||
                        $.trim($(bindings).find('.graphicFileInput').attr('imgPath')).length > 0;
                    var appliesTextContent = $.trim($(bindings).find('.textContentBinding').val()).length > 0;
                    var hasImageSelectLoaded = $(bindings).find('.useContentLibrarySelect select .loadingOption').length == 0;
                    var hasEditorLoaded = $(bindings).find('.mce-tinymce.mce-container').length != 0;
                    var applesDefaultText = preProofData != null && preProofData.general_editor_data.applies_default_templates &&
                        zonePropertiesData["zone_" + id].template_id > 0;
                    var isTextContent = $(bindings).find('.textContentBinding').length > 0;

                    if (
                        ( !isTextContent && (
                            ( useImageLibrary && ((hasImageSelectLoaded && contentLibrarySelectValue != "0") || (!hasImageSelectLoaded && appliesDefaultLibrarySelectValue))  ) ||
                            ( !useImageLibrary && appliesGraphicFile )
                        )) ||
                        ( isTextContent && ( hasEditorLoaded && appliesTextContent ) || ( !hasEditorLoaded && (applesDefaultText || appliesTextContent) ) )
                    ) return true;
                    return false;
                }


                $('.zoneNavigationItem').each(function () {
                    var zoneId = parseId(this);
                    if (zoneHasContent(zoneId)) {
                        $(this).find('i').css({'color': 'green'});
                        $('#interactiveZoneIndicator_' + zoneId).find('.zoneIndicatorHeader i:not(.editorLoadingIcon)').css({'color': 'green'});
                    } else {
                        $(this).find('i').css({'color': 'red'});
                        $('#interactiveZoneIndicator_' + zoneId).find('.zoneIndicatorHeader i:not(.editorLoadingIcon)').css({'color': 'red'});
                    }
                    ;
                });
            }

            function toggleIndicators() {
                if ($('#toggleIndIcon').is('.fa-toggle-on')) {
                    $('.zoneIndicator').each(function () {
                        if (!$(this).is(':visible'))
                            $(this).show('normal');
                    });
                } else {
                    $('.zoneIndicator').hide('normal');
                }
            }

            function setProofDisplay() {
                if ($('#proofToggle').is('.fa-caret-right'))
                    $('#proofContainer').hide();
                else
                    $('#proofContainer').show();

                if ($('#proofContainer').is(':visible') && !$('#proofContainer').is('.init') && proofParams != null) {
                    $('#proofContainer').addClass('init');

                    var pdfContainer = "<iframe src=\"" + context + '/download/pdf.form' + proofParams + "\" style=\"width: 100%; height: 390px;\" frameborder=\"0\" scrolling=\"no\">" +
                        "<p style=\"padding: 30px; font-size: 12px; color: #333;\">It appears you don't have Adobe Reader or PDF support in this web browser. <a href=\"" + context + '/download/pdf.form' + proofParams + "\">Click here to download the PDF</a>. Or <a href=\"http://get.adobe.com/reader/\" target=\"_blank\">click here to install Adobe Reader</a>.</p>" +
                        "</iframe>";
                    $('#pdfProofContainer').append(pdfContainer);
                }
            }

            function toggleProof() {
                if ($('#proofToggle').is('.fa-caret-right'))
                    $('#proofToggle').removeClass('fa-caret-right').addClass('fa-caret-down');
                else
                    $('#proofToggle').removeClass('fa-caret-down').addClass('fa-caret-right');
                updatePersistedClass($('#proofToggle'));
                setProofDisplay();
            }

            function initBtn(ele, type) {

                $(ele)
                    .mouseover(function () {
                        if (!$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight .actionBtn_toggleSelect]')) {
                            $(this).removeClass('actionBtn');
                            $(this).addClass('actionBtn_hov');
                        }
                    })
                    .mouseout(function () {
                        if (!$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight .actionBtn_toggleSelect')) {
                            $(this).removeClass('actionBtn_hov');
                            $(this).addClass('actionBtn');
                        }
                    });

                if (type == "toggle") {
                    $(ele)
                        .click(function () {
                            if (!$(this).hasClass('actionBtn_disabled')) {
                                if ($(this).hasClass('actionBtn_toggleSelectHighlight'))
                                    $(this).removeClass('actionBtn_toggleSelectHighlight').addClass('actionBtn');
                                else
                                    $(this).removeClass('actionBtn').addClass('actionBtn_toggleSelectHighlight');
                            }

                        });
                } else if (type == "button") {
                    $(ele)
                        .mousedown(function () {
                            if (!$(this).hasClass('actionBtn_disabled'))
                                $(this).removeClass('actionBtn actionBtn_hov').addClass('actionBtn_selected');
                        })
                        .mouseup(function () {
                            if (!$(this).hasClass('actionBtn_disabled'))
                                $(this).removeClass('actionBtn_selected').addClass('actionBtn');
                        });
                }
                ;

            }

            function sendLoadCompleteForAudit() {

            	var debugDataArray = [];
            	for (var key in generalDebugData )
					debugDataArray[debugDataArray.length] = key + ',' + generalDebugData[key];

				timestamp_audit_load_complete_start = (new Date()).getTime() - timestamp_base_page;

				debugDataArray[debugDataArray.length] =  'timestamp_base_page,' + timestamp_base_page;
				debugDataArray[debugDataArray.length] =  'timestamp_javascript_base,' + timestamp_javascript_base;
				debugDataArray[debugDataArray.length] =  'timestamp_start_proof_poll,' + timestamp_start_proof_poll;
				debugDataArray[debugDataArray.length] =  'timestamp_last_async_template_request,' + timestamp_last_async_template_request;
				debugDataArray[debugDataArray.length] =  'timestamp_process_a_start,' + timestamp_process_a_start;
				debugDataArray[debugDataArray.length] =  'timestamp_process_a_return,' + timestamp_process_a_return;
				debugDataArray[debugDataArray.length] =  'timestamp_process_b_start,' + timestamp_process_b_start;
				debugDataArray[debugDataArray.length] =  'timestamp_process_b_return,' + timestamp_process_b_return;
				debugDataArray[debugDataArray.length] =  'timestamp_audit_load_complete_start,' + timestamp_audit_load_complete_start;

				var bv = browserVersion.init();
				debugDataArray[debugDataArray.length] =  'os_name,' + bv.os.name;
				debugDataArray[debugDataArray.length] =  'os_version,' + bv.os.version;
				debugDataArray[debugDataArray.length] =  'browser_name,' + bv.browser.name;
				debugDataArray[debugDataArray.length] =  'browser_version,' + bv.browser.version;


				var stampDate = new Date();
				var dataUrl = context + "/getInteractiveData.form?" +
						"type=audit_load_complete" +
						"&preProofId=" + preProofId +
						"&debug_data=" + debugDataArray.join(':') +
						"&cacheStamp=" + (stampDate.getTime());
				if ( $('#isDebugContext').val() == "true" )
					dataUrl += "&communicationId=" + communicationId;

				$.ajax({
					type: "GET",
					url: dataUrl,
					dataType: "json",
					success: function (data) {

						timestamp_audit_load_complete_return = (new Date()).getTime() - timestamp_base_page;
						
						$('#totalRoundTripTime').html( data.end_to_end_time );
					}
				});
			}

			function redirectToList(listUrl) {
				if ( getParam('embedded') == 'true' ) {
					listUrl += '&embedded=true';
					if ( getParam('return_to_list') == 'true' )
						listUrl += '&return_to_list=true'
					if ( getParam('return_url') != '' )
						listUrl += '&return_url=' + getParam('return_url');
				}
				javascriptHref(listUrl);
			}

            // #######################################################
            // ############### START INIT ############################
            $(function () {

				timestamp_javascript_base = (new Date()).getTime() - timestamp_base_page;

            	if ( document.referrer.toLowerCase().indexOf("salesforce") != -1 )
                	$('#headerTextForSalesforce').show();

                $('#currentZoneSelect').styleActionElement();
                $("[id^='resetBtn_']").styleActionElement();
                initBtn($("#toggleIndicatorsBtn"), 'button');
                $("#toggleIndIcon").click(function () {
                    if ($(this).is('.fa-toggle-on'))
                        $(this).removeClass('fa-toggle-on').addClass('fa-toggle-off');
                    else
                        $(this).removeClass('fa-toggle-off').addClass('fa-toggle-on');
                    updatePersistedClass($(this));

                    toggleIndicators();
                });

                setProofDisplay();

                $('#communicationMetatags').tagCloud({
                    tagCloudType: 7,
                    popupLocation: "bottom-left"
                });

                $('.zoneNavigationItem').click(function (e) {
                    // TOGGLE EDITOR
                    zoneIndicatorToggle($('#interactiveZoneIndicator_' + parseId(this)), true);
                });

                // PDF Widget scroll management
                var navPanelTop = $('#navigationPanel').position().top;
                $(window).scroll(function () {
                    var doc = document.documentElement, body = document.body;
                    var pageTop = (doc && doc.scrollTop || body && body.scrollTop || 0);
                    var scrollLimit = $('#interactiveTouchpointContainer').position().top + $('#interactiveTouchpointContainer').height();

                    if (( ($('#navigationPanel').position().top < pageTop) ||
                            ($('#navigationPanel').position().top > pageTop && (pageTop - navPanelTop) > 0) ) &&
                        $('#navigationPanel').position().top + $('#navigationPanel').height() <= scrollLimit) {
                        $('#navigationPanel').css('top', Math.min((pageTop - navPanelTop), (scrollLimit - $('#navigationPanel').height() - navPanelTop)) + 'px');
                    } else if ((pageTop - navPanelTop) < 0) {
                        $('#navigationPanel').css('top', '0px');
                    }

                });

                timestamp_start_proof_poll = new Date().getTime() - timestamp_base_page;
                debugPreProofLoadTime = new Date().getTime();
                pollForPreProof();

                if ($('#pdfProofContainer').length != 0)
                    pollForProof();

                $('#scriptLoadTimeContainer').html( (((new Date().getTime()) - debugStartLoad)/1000) + "s" );

    			$('#debugToggleContainer').dblclick( function() {
    				$('#debugInterfaceContainer').toggle();
    			});

            });
            // ################ END INIT #############################
            // #######################################################

			// IE11 Hack for FB17890
			$.fn.showEle = function(action) {
				if ( true ) {
					$(this).fadeIn(action);
					$(this).show();
				} else
					$(this).show();
				return $(this);
			};
			$.fn.hideEle = function(action) {
				if ( true ) {
					$(this).fadeOut(action);
					$(this).hide();
				} else
					$(this).hide();
				return $(this);
			};

        </script>
    </msgpt:Script>
    <script id="miniIndicatorTemplate" type="text/x-handlebars-template">
		<div id="miniIndicator_{{eleIdx}}" class="miniIndicator fa-mp-container" style="display: none;">
			<i class="fa fa-pencil fa-mp-btn"></i>
		</div>
	</script>
    <script id="zoneIndicatorTemplate" type="text/x-handlebars-template">
		<div id="interactiveZoneIndicator_{{eleIdx}}" class="zoneIndicator" style="display: none;">
			<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>
				<td class="zoneIndicatorHeader" style="padding: 0px;" width="99%">
					<i class="fa {{icon}} fa-med fa-mp-btn" style="font-size: 18px; position: relative; padding-left: 6px; top: -1px;"></i>
					<span class="zoneIndicatorLabel" style="padding: 0px 12px; font-size: 16px;">{{zone_name}}</span>
					<i class="editorLoadingIcon fa fa-cog fa-spin fa-fw" style="display: none; font-size: 18px; position: relative; top: -1px; color: #777;"></i>
				</td>
				<td style="padding: 0px;" width="1%">

				</td>
			</tr></table>
			<div id="editorContainer_{{eleIdx}}" style="position: relative;">

			</div>
		</div>
	</script>
    <script id="marginIndicatorTemplate" type="text/x-handlebars-template">
		<div id="marginIndicator_{{eleIdx}}" class="marginIndicator fa-mp-container" style="display: none;">
			<i class="fa fa-caret-right fa-mp-btn" style="font-size: 30px; position: relative; top: -15px; cursor: pointer;"></i>
		</div>
	</script>

</msgpt:HeaderNew>

<!-- Content Type IDs -->
<c:set var="textContentTypeID" value="<%= ContentType.TEXT %>" />
<c:set var="graphicContentTypeID" value="<%= ContentType.GRAPHIC %>" />

<msgpt:BodyNew theme="minimal" type="${isEmbedded ? 'iframe' : 'default'}">
	<c:choose>
		<c:when test="${isEmbedded}">
			<div class="actionsBarContainer">
				<div class="actionBarHeaderLabel featureLabelText">
					<fmtSpring:message code="page.lable.messagepoint.connected" />
					<span id="headerTextForSalesforce" style="display: none;">
						<fmtSpring:message code="page.lable.for.salesforce" />
					</span>
				</div>
				<div style="text-align: right; padding: 12px 20px;">
				     <img src="${contextPath}/includes/themes/logos/messagepoint_text_logo.png" class="d-inline-block"
                     alt="${msgpt:getMessage('page.label.header.logo')}"/>
				</div>
			</div>
		</c:when>
		<c:otherwise>
			<msgpt:BannerNew  edit="true" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>" applyTabs="false" />
			<msgpt:NewNavigationTabs edit="true" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>" />
		</c:otherwise>
	</c:choose>
	
	<msgpt:LowerContainer fullPanel="true" extendedWidth="true" >
		<msgpt:ContentPanel extendedWidth="true">

		<%--@elvariable id="command" type=""--%>
		<form:form modelAttribute="command" enctype="multipart/form-data">

			<form:errors path="*">
				<msgpt:Information errorMsgs="${messages}" type="error" />
			</form:errors>
			
			<form:hidden path="actionValue" id="actionElement"/>
			<input type="hidden" id="isTestCenterContext" value="${isTestCenterContext}" />
			<input type="hidden" id="isDebugContext" value="${communication.debugOrder}" />

			<!-- Begin new layout -->

			<h4 class="mb-4">
				<span id="debugToggleContainer">
					<c:choose>
						<c:when test="${empty param.communicationId}"><fmtSpring:message code="page.label.create"/><c:out value=" " /></c:when>
						<c:otherwise><fmtSpring:message code="page.label.edit"/><c:out value=" " /></c:otherwise>
					</c:choose>
					<c:choose>
						<c:when test="${isTestCenterContext}"><fmtSpring:message code="page.label.test.order"/> </c:when>
						<c:otherwise><fmtSpring:message code="page.label.communication"/> </c:otherwise>
					</c:choose>
				</span>
			</h4>

			<!-- HEADER/BUTTONS -->
			<div class="actionBtnToolbar">
				<div class="toolbarRightSection">
					<div class="btnGroup">
						<c:if test="${orderEntryEnabled || appliesVariantSelect}">
							<span style="margin-left: 8px">
								<span class="btnsEnabled" style="display: none;">
									<msgpt:Button label="page.label.interview" URL="javascript:doSubmit(4)" flowControl="true" icon="fa-save" />
								</span>
								<span class="btnsDisabled">
									<msgpt:Button label="page.label.interview" URL="#" disabled="true" flowControl="true" icon="fa-save" />
								</span>
							</span>
						</c:if>
						<span style="margin-left: 8px">
							<span class="btnsEnabled" style="display: none;">
								<msgpt:Button label="page.label.save.and.preview" URL="javascript:doCustomSubmit(3,this)" flowControl="true" icon="fa-save" />
							</span>
							<span class="btnsDisabled">
								<msgpt:Button label="page.label.save.and.preview" URL="#" disabled="true" flowControl="true" icon="fa-save" />
							</span>
						</span>
						<c:choose>
							<c:when test="${isEmbedded}">
								<span style="margin-left: 8px">
									<span class="btnsEnabled" style="display: none;">
										<msgpt:Button label="page.label.save.and.submit" URL="javascript:doCustomSubmit(5,this)" primary="true" flowControl="true" icon="fa-save" />
									</span>
									<span class="btnsDisabled">
										<msgpt:Button label="page.label.save.and.submit" URL="#" disabled="true" primary="true" flowControl="true" icon="fa-save" />
									</span>
								</span>
							</c:when>
							<c:otherwise>
								<span style="margin-left: 8px">
									<msgpt:Button label="page.label.save" primary="true" URL="javascript:doSubmit(1)" flowControl="true" icon="fa-save" />
								</span>
							</c:otherwise>
						</c:choose>
						<span style="margin-left: 8px">
							<c:choose>
							<c:when test="${isEmbedded && !isReturnToList}">
								<msgpt:Button label="page.label.exit" URL="${contextPath}/signout?order_guid=${communication.guid}&return_url=${not empty param.return_url ? param.return_url : 'null'}&tk=${param.tk}" flowControl="true"/>
							</c:when>
							<c:otherwise>
								<c:choose>
									<c:when test="${isTestCenterContext}">
										<msgpt:Button label="page.label.cancel" URL="javascript:redirectToList('${contextPath}/testing/test_center_communications_list.form?documentId=${document.id}')" flowControl="true"/>
									</c:when>
									<c:otherwise>
										<msgpt:Button label="page.label.cancel" URL="javascript:redirectToList('${contextPath}/touchpoints/touchpoint_communications_list.form?documentId=${document.id}')" flowControl="true"/>
									</c:otherwise>
								</c:choose>
							</c:otherwise>
						</c:choose>
						</span>
					</div>
				</div>
			</div>

			<!-- NAVIGATION PANEL -->
			<div id="floatPanelContainer">
				<div id="navigationPanel" style="float:left; width: 275px" class="stageCardsSectionWrapper">
					<c:if test="${not command.communication.isEmailContentDelivery && not command.communication.isWebDelivery}">
						<div class="stageContainer" style="height: 56px; line-height: 56px; margin-bottom: 0px;">
							<i id="proofToggle" class="fa fa-caret-right fa-mp-btn persistedClass" onclick="toggleProof()" style="font-size: 20px; cursor: pointer; position: relative; top: 1px; color: #555; margin-left: 16px"></i>
							<span class="sideBarHeaderLabel">
								<fmtSpring:message code="page.label.preview"/>
							</span>
							<i class="fa fa-search disabled viewProofIcon" style="line-height: 56px; position: absolute; right: 16px; font-size:16px"></i>
						</div>
						<!-- PROOF -->
						<div id="proofContainer" class="stageContainer proofContainer"> <!-- Content Area: PDF DISPLAY -->
							<c:choose>
								<c:when test="${not empty preProofId}">
									<div id="proofPollingContainer" align="center" style="width: 100%; background-color: #f5f5f5; min-height: 320px;">
										<img src="${contextPath}/includes/themes/commonimages/layout/loading_large_light.gif" style="position: relative; top: 100px;" />
									</div>
									<div id="pdfProofContainer" itemId="${preProofId}" style="position: relative; display: none;"></div>
								</c:when>
								<c:otherwise>
									<div style="background-color: #f5f5f5; min-height: 150px; padding: 24px; text-align: center; vertical-align: middle;">
										<div style="margin-bottom: 12px;">
											<i class="far fa-file-pdf" style="font-size: 100px; color: #ddd;"></i>
										</div>
										<span style="font-size: 16px; color: #ccc;">
												<fmtSpring:message code="page.text.save.and.preview.to.view"/>
											</span>
									</div>
								</c:otherwise>
							</c:choose>
	
						</div>
					</c:if>
	
					<c:if test="${command.communication.isEmailContentDelivery || command.communication.isWebDelivery}">
						<!-- SUMMARY PANEL: PROOF RECIPIENT -->
						<div class="stageContainer" style="padding: 8px; margin-bottom: 0px; margin-top: 16px;">
							<msgpt:FormControl label="page.label.proof.recipient" verticalAlign="true">
								<msgpt:InputFilter type="email">
									<form:input path="email" cssClass="inputXL" />
								</msgpt:InputFilter>
							</msgpt:FormControl>
						</div>
					</c:if>
	
					<div class="stageContainer" style="height: 56px; line-height: 56px; margin: 16px 0 16px 0">
						<span class="sideBarHeaderLabel">
							<fmtSpring:message code="page.label.interactive.content"/>
						</span>
						<i id="toggleIndIcon" class="fa fa-toggle-on fa-mp-btn persistedClass" style="line-height: 56px; position: absolute; right: 16px; font-size:16px"></i>
					</div>
	
					<c:if test="${fn:length(zones) gt 0}">
						<div class="zoneNavigationContainer stageContainer" style="line-height: 40px; margin: 16px 0 16px 0">
							<c:forEach items="${zones}" var="currentZone">
								<div id="zoneNavigationItem_${currentZone.id}" class="zoneNavigationItem" style="display: none;">
									<c:if test="${currentZone.contentType.id == textContentTypeID}">
										<i class="fa fa-font fa-med fa-mp-btn" style="padding-right: 4px;"></i>
									</c:if>
									<c:if test="${currentZone.contentType.id == graphicContentTypeID}">
										<i class="fa fa-image fa-med fa-mp-btn"></i>
									</c:if>
									<span class="zoneNavNameContainer" style="font-size: 12px; padding: 0px 12px;">ASYNC</span>
								</div>
							</c:forEach>
						</div>
					</c:if>
	
					<!-- SUMMARY PANEL: ORDER ENTRY -->
					<c:if test="${(orderEntryEnabled || webServiceDriverEnabled) && not empty command.communication.orderEntryItemsInOrder}">
	
						<div class="zoneNavigationContainer stageContainer" style="padding: 16px; margin: 16px 0 16px 0">
							<div id="orderSummaryPanel">
	
								<c:choose>
									<c:when test="${orderEntryEnabled || webServiceDriverEnabled}">
	
										<!-- ORDER ENTRY SUMMARY -->
										<msgpt:FormLayout vertical="true">
											<msgpt:FormField label="${communication.customerIdentifierDriverLabel}">
												<c:out value="${communication.customerIdentifierDisplayValue}" />
											</msgpt:FormField>
											<c:if test="${document.communicationAppliesTagCloud}">
												<msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW" >
													<msgpt:FormField label="page.label.metatags">
														<c:choose>
															<c:when test="${not empty command.communication.metatags}">
																<c:out value="${command.communication.metatags}" />
															</c:when>
															<c:otherwise>
																<i><fmtSpring:message code="page.label.none"/></i>
															</c:otherwise>
														</c:choose>
													</msgpt:FormField>
												</msgpt:IfAuthGranted>
											</c:if>
										</msgpt:FormLayout>
	
									</c:when>
									<c:otherwise>
	
										<!-- RECIPIENT SUMMARY -->
										<msgpt:FormLayout vertical="true">
											<msgpt:FormField label="page.label.recipient">
												<c:out value="${communication.customerIdentifier}" />
											</msgpt:FormField>
											<c:if test="${document.communicationAppliesTagCloud}">
												<msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW" >
													<msgpt:FormField label="page.label.metatags">
														<msgpt:InputFilter type="simpleName">
															<form:input id="communicationMetatags" path="communication.metatags" htmlEscape="true" cssClass="inputXXL"/>
														</msgpt:InputFilter>
													</msgpt:FormField>
												</msgpt:IfAuthGranted>
											</c:if>
										</msgpt:FormLayout>
	
									</c:otherwise>
								</c:choose>
	
	
								<msgpt:FormLayout vertical="true">
									<c:forEach var="currentItem" items="${command.communication.orderEntryItemsInOrder}">
										<c:if test="${not currentItem.isIndicatorValue && currentItem.appliedItem && currentItem.isUnrestricted}">
											<msgpt:FormField label="${currentItem.name}">
												<c:choose>
													<c:when test="${not empty currentItem.displayValue}">
														<c:out value="${currentItem.displayValue}" />
													</c:when>
													<c:otherwise>
														<i><fmtSpring:message code="page.label.none"/></i>
													</c:otherwise>
												</c:choose>
											</msgpt:FormField>
										</c:if>
									</c:forEach>
									<msgpt:FormField label="">
									</msgpt:FormField>
								</msgpt:FormLayout>

							</div>
	
						</div>
					</c:if>
	
				</div>
				<!-- PAGES -->
				<div style="float: right;" class="backgroundTile_10p">
					<div id="interactiveTouchpointContainer" class="interactiveTouchpointContainer contentContainer">
						<div id="pagesLoadingContainer" align="center" style="width: 100%; min-height: 325px;">
							<div style="position: relative; top: 100px;">
								<div>
									<div class="progress-loader progress-loader-lg">
										<i class="progress-loader-icon fad fa-spinner-third" aria-hidden="true"></i>
									</div>
								</div>
								<div id="loadingTextContainer" style="margin-top: 8px;">
									<fmtSpring:message code="page.text.gathering.result" />...
								</div>
							</div>
						</div>
					</div>
					
					<!-- DEBUG -->
					<div id="debugInterfaceContainer" style="${command.communication.debugOrder ? '' : 'display:none;'} background-color: #fff; padding: 20px; border: 1px solid red; margin-top: 20px; max-width: 850px;">
						<msgpt:DataTable labelPosition="top" multiColumn="true">
							<msgpt:TableItem label="Debug mode">
								<form:checkbox path="communication.debugOrder" cssClass="checkbox" />
							</msgpt:TableItem>
							<msgpt:TableItem label="Debug reference data">
								<form:textarea path="communication.debugReferenceData" cssStyle="width: 375px;" rows="3" />
							</msgpt:TableItem>
							<msgpt:TableItem label="Pre-proof Job ID">
								<div id="preProofJobIdContainer" style="cursor: pointer"></div>
							</msgpt:TableItem>
							<msgpt:TableItem label="Proof Job ID">
								<div id="proofJobIdContainer" style="cursor: pointer"></div>
							</msgpt:TableItem>
							<msgpt:TableItem label="Variant">
								<div id="variantNameContainer"></div>
							</msgpt:TableItem>
							<msgpt:TableItem label="Javascript load time">
								<span id="scriptLoadTimeContainer"></span>
							</msgpt:TableItem>
							<msgpt:TableItem label="Interactive data load time">
								<div style="max-width: 400px; word-break: break-all;">
									<span id="interactiveLoadTimeContainer"></span>
								</div>
							</msgpt:TableItem>
							<msgpt:TableItem label="Preproof load time">
								<span id="preProofLoadTimeContainer"></span>
							</msgpt:TableItem>
							<msgpt:TableItem label="Last editor load time">
								<span id="lastEditorLoadTimeContainer"></span>
							</msgpt:TableItem>
							<msgpt:TableItem label="Reference data load time">
								<c:out value="${debugTime_referenceData}" />s
							</msgpt:TableItem>
							<msgpt:TableItem label="Wrapper load time">
								<c:out value="${command.wrapperInitDebugTime}" />s
							</msgpt:TableItem>
							<msgpt:TableItem label="Variable values load time">
								<span id="variableValuesLoadTimeContainer"></span>
							</msgpt:TableItem>
							<msgpt:TableItem label="Latest round trip time">
								<span id="totalRoundTripTime"></span>
							</msgpt:TableItem>
							<c:if test="${not empty preProof}">
								<msgpt:TableItem label="Latest pre-proof reference data">
									<div style="max-width: 400px; word-break: break-all;"><c:out value="${preProof.preProofReferenceData}" /></div>
								</msgpt:TableItem>
							</c:if>
							<msgpt:TableItem label="Conversion quality (0-100)">
								<form:input path="communicationPDFConversionQuality" cssClass="input2digit" />
							</msgpt:TableItem>

							<msgpt:TableItem label="Recipient Contents (JSON)">
								<span id="jsonRecipientContents" style="word-break: break-word;"></span>
							</msgpt:TableItem>
							<msgpt:TableItem label="VariableValues (JSON)">
								<span id="jsonVariableValues" style="word-break: break-word;"></span>
							</msgpt:TableItem>
							<msgpt:TableItem label="Selections (JSON)">
								<span id="jsonSelections" style="word-break: break-word;"></span>
							</msgpt:TableItem>
							<msgpt:TableItem label="OrderLog (JSON)">
								<span id="jsonOrderLog" style="word-break: break-word;"></span>
							</msgpt:TableItem>

						</msgpt:DataTable>

					</div> <!-- END DEBUG -->
					
				</div> <!-- END PAGES -->

			</div> <!-- End floatPanelContainer -->
			
			<!-- End new layout -->



			<!-- EDITOR DEFINITIONS -->
			<div id="communicationContentContainer">

				<input type="hidden" id="communicationLocaleId" value="${command.localeId}" />

				<c:forEach items="${zones}" var="currentZone">
					<div id="interactiveEditorContainer_${currentZone.id}" class="interactiveEditorContainer" style="display: none;">

						<div style="padding: ${currentZone.contentType.id == graphicContentType ? '0px 0px 0px 10px' : '0px'}; text-align: left;">
							<div id="contentData_${currentZone.id}" globalState			= "edit"
								 globalRefObjLabel	= "UNUSED"
								 style				= "display: none;">
								<div 	class				= "contentGroup"
										groupId				= "zone_${currentZone.id}"
										groupState			= "edit"
										groupRefObjLabel	= "UNUSED"
										backgroundColor		= "ASYNC"
										canvasDimensions	= "ASYNC"
										canvasRotation		= "ASYNC"
										templateId			= "ASYNC"
										content				= "${currentZone.contentType.id == textContentTypeID ? 'text' : 'graphic'}" >
									<div 	class		="contentEntry"
											contentState= "inherit"
											localeId	="${command.localeId}" >
										<c:if test="${currentZone.contentType.id == textContentTypeID}">
											<div class="textContentInput">
												<form:textarea cssClass="textContentBinding" path="zoneContentMap[${currentZone.id}].content" />
											</div>
										</c:if>
										<c:if test="${currentZone.contentType.id == graphicContentTypeID}">
											<c:if test="${not currentZone.isGraphicUploadOnly}">
												<div class="useImageLibrary">
													<div class="useContentLibraryToggle">
														<form:checkbox cssClass="useContentLibraryBinding" path="zoneContentMap[${currentZone.id}].useImageLibrary" />
													</div>
													<div class="useContentLibrarySelect">
														<form:select id="conLibSelect_${currentZone.id}_0" cssClass="inputXL" onchange="" path="zoneContentMap[${currentZone.id}].imageLibraryId">
															<c:choose>
																<c:when test="${not empty command.zoneContentMap[currentZone.id].imageLibraryId}">
																	<option id="${command.zoneContentMap[currentZone.id].imageLibraryId}" value="${command.zoneContentMap[currentZone.id].imageLibraryId}">DEFAULT</option>
																</c:when>
																<c:otherwise>
																	<option id="0" value="0" class="loadingOption"><fmtSpring:message code="page.text.loading"/></option>									 </c:otherwise>
															</c:choose>
														</form:select>
													</div>
													<input type="hidden" id="defaultContentLibraryId_${currentZone.id}_0" value="${command.zoneContentMap[currentZone.id].imageLibraryId}" />
													<input type="hidden" id="extType_${currentZone.id}_0" value="${currentZone.subContentType.name}" />
													<input type="hidden" id="documentId_${currentZone.id}_0" value="${currentZone.document.id}" />
													<input type="hidden" id="communicationId_${currentZone.id}_0" value="${communication.id}" />
												</div>
											</c:if>
											<div 	class="graphicFileInput"
													imgPath="${command.zoneContentMap[currentZone.id].imageLocation}"
													imgName="${command.zoneContentMap[currentZone.id].imageName}"
													contentId="${command.zoneContentMap[currentZone.id].contentId}"
													uploadDate="<fmtJSTL:formatDate value="${command.zoneContentMap[currentZone.id].imageUploadedDate}" pattern="${dateTimeFormat}"/>"
													fileType="${currentZone.graphicTypeRegEx}">
												<form:input cssClass="graphicFileBinding" path="zoneContentMap[${currentZone.id}].sandboxFileId" style="display: none;" />
											</div>
											<div class="graphicAppliedImageName">
												<msgpt:InputFilter type="imageName">
													<form:input path="zoneContentMap[${currentZone.id}].appliedImageFilename" maxlength="40" cssClass="inputL" />
												</msgpt:InputFilter>
											</div>
										</c:if>
									</div>
								</div>

							</div>
						</div>
						
					</div>

				</c:forEach>

			</div> <!-- END: EDITOR DEFINITIONS -->

			<div id="errorContainerTemplate" style="display: none; z-index: 1001; position: relative;">
				<msgpt:Information type="error" />
			</div>
			
		</form:form>

		</msgpt:ContentPanel>
	</msgpt:LowerContainer>
</msgpt:BodyNew>
</msgpt:Html5>