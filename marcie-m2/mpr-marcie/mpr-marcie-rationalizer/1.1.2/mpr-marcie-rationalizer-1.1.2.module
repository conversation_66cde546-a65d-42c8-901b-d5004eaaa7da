{"formatVersion": "1.1", "component": {"group": "mpr-marcie", "module": "mpr-marcie-rationalizer", "version": "1.1.2", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.2"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "mpr-marcie", "module": "mpr-marcie-text", "version": {"requires": "1.1.2"}}, {"group": "org.apache.commons", "module": "commons-compress", "version": {"requires": "1.27.1"}}, {"group": "org.apache.httpcomponents", "module": "httpclient", "version": {"requires": "4.5.14"}}, {"group": "com.google.code.gson", "module": "gson", "version": {"requires": "2.13.1"}}, {"group": "org.jsoup", "module": "jsoup", "version": {"requires": "1.20.1"}}], "files": [{"name": "mpr-marcie-rationalizer-1.1.2.jar", "url": "mpr-marcie-rationalizer-1.1.2.jar", "size": 344952, "sha512": "57a91f37239eafd5062ad45fc926f70616fbafee91e6525a2d492be0abac3762c76115b9ec909a2b30add781e3279da88a9524c516c8ffda5bda6d5ed8e40908", "sha256": "755cfe0eace8d811fe6a4c235b228d5ce501a8845b6c4475f91789d61c411114", "sha1": "6c80aaeee40f384f27a621a234a2956605072023", "md5": "95ef176aea6287bacda332b1749575c9"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "mpr-marcie", "module": "mpr-marcie-text", "version": {"requires": "1.1.2"}}, {"group": "org.apache.commons", "module": "commons-compress", "version": {"requires": "1.27.1"}}, {"group": "org.apache.httpcomponents", "module": "httpclient", "version": {"requires": "4.5.14"}}, {"group": "com.google.code.gson", "module": "gson", "version": {"requires": "2.13.1"}}, {"group": "org.jsoup", "module": "jsoup", "version": {"requires": "1.20.1"}}], "files": [{"name": "mpr-marcie-rationalizer-1.1.2.jar", "url": "mpr-marcie-rationalizer-1.1.2.jar", "size": 344952, "sha512": "57a91f37239eafd5062ad45fc926f70616fbafee91e6525a2d492be0abac3762c76115b9ec909a2b30add781e3279da88a9524c516c8ffda5bda6d5ed8e40908", "sha256": "755cfe0eace8d811fe6a4c235b228d5ce501a8845b6c4475f91789d61c411114", "sha1": "6c80aaeee40f384f27a621a234a2956605072023", "md5": "95ef176aea6287bacda332b1749575c9"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "mpr-marcie-rationalizer-1.1.2-sources.jar", "url": "mpr-marcie-rationalizer-1.1.2-sources.jar", "size": 114382, "sha512": "99c83c9bdfebf1774b3ca83d17fb8f699032b12e6397efeede3f3c0a56d39a92b2538a6c6c12a62b72fcf1b90bb402a724cf007c9f627853bdf12d955ac23ce1", "sha256": "5421f64cc5304696f46b7ffbf7744155b5d3e569c45754666925fdcbf4e473c7", "sha1": "6f8384a155295f82f028dc93b7b28dd9aa0a043d", "md5": "ccf8fc5a193deefb256fc6a211764f4e"}]}]}