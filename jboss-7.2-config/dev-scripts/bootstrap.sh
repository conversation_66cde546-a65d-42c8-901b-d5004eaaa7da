#!/usr/bin/env bash

#wget http://ui-automation.inprinova.com

DATA_DIR=$1
JBOSS_ZIP=$2
JBOSS_ZIP_DOWNLOAD="${DATA_DIR}/${JBOSS_ZIP}"

mkdir -p ${DATA_DIR}

if [[ ! -f ./${JBOSS_ZIP_DOWNLOAD} ]]; then
    wget http://ui-automation.inprinova.com/jboss-eap/${JBOSS_ZIP} -P ${DATA_DIR}
fi

JBOSS_SHA256=`shasum -a 256 ${JBOSS_ZIP_DOWNLOAD} | awk '{ print $1 }'`

if [[ ${JBOSS_SHA256} == "105b3f6723eaa2a026b0af5d444a389c54b507c3f0b6912f4915c83b9c5713f3" ]]; then


    for i in $(seq 1 3);
    do
        if [[ ! -d ./${DATA_DIR}/node-${i} ]]; then
            unzip ./${DATA_DIR}/${JBOSS_ZIP} -d./${DATA_DIR}/node-${i}

            JBOSS_BASE_NAME=`echo ${JBOSS_ZIP} | sed 's/.zip//g'`
            STANDALONE_XML="./${DATA_DIR}/node-${i}/${JBOSS_BASE_NAME}/standalone/configuration/standaloneMessagepoint.xml"
            EXPLODED_WAR="`pwd | sed 's/jboss-7.2-config\/dev-scripts//g'`/build/libs/exploded/messagepoint.war"

            ./configure-node.sh ${DATA_DIR} ${JBOSS_ZIP} ${i}

        fi
    done

    exit 0
fi

echo ${JBOSS_SHA256}

exit 1